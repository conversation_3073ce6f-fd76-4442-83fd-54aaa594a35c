package br.com.ksisolucoes.bo.frota.roteiroviagempassageiro;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.atividadegrupo.interfaces.dto.ContaEItemContaPacienteTfdDTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.frota.interfaces.dto.ManutencaoViagemDTO;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfdPassageiro;
import br.com.ksisolucoes.vo.frota.OcorrenciaPassageiroViagem;
import br.com.ksisolucoes.vo.frota.RoteiroViagem;
import br.com.ksisolucoes.vo.frota.RoteiroViagemPassageiro;
import br.com.ksisolucoes.vo.frota.viagem.solicitacao.SolicitacaoViagem;
import br.com.ksisolucoes.vo.frota.viagem.solicitacao.SolicitacaoViagemOcorrencia;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.OcorrenciaContaPaciente;
import org.hibernate.criterion.Restrictions;

import java.util.List;
import java.util.stream.Collectors;

import static br.com.celk.faturamento.FechamentoContaHelper.permiteGerarBpaSemTfd;

/**
 *
 * <AUTHOR>
 */
public class ManutencaoRoteiroViagemPassageiro extends AbstractCommandTransaction<ManutencaoRoteiroViagemPassageiro> {

    private final List<RoteiroViagemPassageiro> lstPassageiros;
    private final RoteiroViagem roteiroViagem;

    public ManutencaoRoteiroViagemPassageiro(ManutencaoViagemDTO manutencaoViagemDTO) {
        this.lstPassageiros = manutencaoViagemDTO.getLstPassageiros();
        this.roteiroViagem = manutencaoViagemDTO.getRoteiroViagem();
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (lstPassageiros == null) {
            return;
        }

        for (RoteiroViagemPassageiro rvp : lstPassageiros) {
            if (rvp.getUsuarioCadsus() == null && (rvp.getObservacao() == null || rvp.getObservacao().isEmpty())) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_observacao"));
            }
            rvp.setRoteiro(roteiroViagem);
        }

        List<RoteiroViagemPassageiro> roteiroViagemPassageiroList = LoadManager.getInstance(RoteiroViagemPassageiro.class)
                .addProperties(new HQLProperties(RoteiroViagemPassageiro.class).getProperties())
                .addProperties(new HQLProperties(SolicitacaoViagem.class, RoteiroViagemPassageiro.PROP_SOLICITACAO_VIAGEM).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RoteiroViagemPassageiro.PROP_ROTEIRO, RoteiroViagem.PROP_CODIGO), roteiroViagem.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RoteiroViagemPassageiro.PROP_STATUS), RoteiroViagem.Status.AGENDADO.value()))
                .start().getList();

        roteiroViagemPassageiroList.removeAll(lstPassageiros);

        gerarOcorrenciaInclusaoPassageiro();

        if (!roteiroViagemPassageiroList.isEmpty()) {
            gerarOcorrenciaExclusaoPassageiro(roteiroViagemPassageiroList);
        }

        ContaPaciente contaPaciente;
        List<ItemContaPaciente> itemContaPacienteList;
        OcorrenciaContaPaciente ocorrenciaContaPaciente;
        for (RoteiroViagemPassageiro object : roteiroViagemPassageiroList) {
            SolicitacaoViagem solicitacaoViagem = object.getSolicitacaoViagem();
            
            if(solicitacaoViagem != null){
                solicitacaoViagem.setStatus(SolicitacaoViagem.Status.PENDENTE.value());
                BOFactory.save(solicitacaoViagem);
            
                gerarOcorrenciaSolicitacaoViagem(solicitacaoViagem);
            }
            
            contaPaciente = BOFactory.getBO(HospitalFacade.class).encontrarContaPaciente(object);
            
            if(contaPaciente != null) {
                itemContaPacienteList = getSession().createCriteria(ItemContaPaciente.class)
                        .add(Restrictions.eq(ItemContaPaciente.PROP_CONTA_PACIENTE, contaPaciente))
                        .list();

                if(CollectionUtils.isNotNullEmpty(itemContaPacienteList)){
                    for(ItemContaPaciente icp : itemContaPacienteList){
                        BOFactory.delete(icp);
                    }
                }
                
                ocorrenciaContaPaciente = (OcorrenciaContaPaciente) getSession().createCriteria(OcorrenciaContaPaciente.class)
                            .add(Restrictions.eq(OcorrenciaContaPaciente.PROP_CONTA_PACIENTE, contaPaciente))
                            .uniqueResult();
                
                if(ocorrenciaContaPaciente != null){
                    BOFactory.delete(ocorrenciaContaPaciente);
                }

                BOFactory.delete(contaPaciente);
            }

            object.setStatus(RoteiroViagemPassageiro.Status.CANCELADO.value());
            object.setUsuarioCancelamento(getSessao().getUsuario());
            object.setDataCancelamento(DataUtil.getDataAtual());

            BOFactory.save(object);
        }

        PedidoTfdPassageiro pedidoTfdPassageiro;
        ContaEItemContaPacienteTfdDTO contaDTO;
        for (RoteiroViagemPassageiro object : lstPassageiros) {
            BOFactory.save(object);

            pedidoTfdPassageiro = null;
            
            if(object.getSolicitacaoViagem() != null) {
                pedidoTfdPassageiro = (PedidoTfdPassageiro) this.getSession().createCriteria(PedidoTfdPassageiro.class)
                        .add(Restrictions.eq(PedidoTfdPassageiro.PROP_SOLICITACAO_VIAGEM, object.getSolicitacaoViagem()))
                        .uniqueResult();
            }

            if (pedidoTfdPassageiro != null || permiteGerarBpaSemTfd(object)) {
                contaDTO = new ContaEItemContaPacienteTfdDTO();
                contaDTO.setRoteiroViagemPassageiro(object);
                contaDTO.setPedidoTfdPassageiro(pedidoTfdPassageiro);
                BOFactory.getBO(AtendimentoFacade.class).salvarContaEItemContaPacienteTfd(contaDTO);
            }
        }
    }

    private void gerarOcorrenciaSolicitacaoViagem(SolicitacaoViagem solicitacaoViagem) throws DAOException, ValidacaoException {
        SolicitacaoViagemOcorrencia ocorrencia = new SolicitacaoViagemOcorrencia();
        ocorrencia.setSolicitacaoViagem(solicitacaoViagem);
        ocorrencia.setDescricao(Bundle.getStringApplication("msg_excluida_viagem_dia_X_veiculo_X", Data.formatar(solicitacaoViagem.getDataViagem()), roteiroViagem.getVeiculo().getDescricao()));

        BOFactory.save(ocorrencia);
    }

    private void gerarOcorrenciaInclusaoPassageiro() throws DAOException, ValidacaoException {
        OcorrenciaPassageiroViagem ocorrencia = new OcorrenciaPassageiroViagem();

        ocorrencia.setTipoOcorrencia(OcorrenciaPassageiroViagem.TipoOcorrencia.INCLUSAO_PACIENTE.value());
        ocorrencia.setDataCadastro(DataUtil.getDataAtual());
        ocorrencia.setUsuario(roteiroViagem.getUsuario());
        ocorrencia.setRoteiroViagem(roteiroViagem);
        ocorrencia.setDescricaoOcorrencia(lstPassageiros
                    .stream()
                    .map(RoteiroViagemPassageiro::getNomePaciente)
                    .collect(Collectors.joining(" - ")
                    ));

        BOFactory.save(ocorrencia);
    }

    private void gerarOcorrenciaExclusaoPassageiro(List<RoteiroViagemPassageiro> roteiroViagemPassageiroExcluidoList) throws DAOException, ValidacaoException {
        OcorrenciaPassageiroViagem ocorrencia = new OcorrenciaPassageiroViagem();

        ocorrencia.setTipoOcorrencia(OcorrenciaPassageiroViagem.TipoOcorrencia.EXCLUSAO_PACIENTE.value());
        ocorrencia.setDataCadastro(DataUtil.getDataAtual());
        ocorrencia.setUsuario(roteiroViagem.getUsuario());
        ocorrencia.setRoteiroViagem(roteiroViagem);
        ocorrencia.setDescricaoOcorrencia(roteiroViagemPassageiroExcluidoList
                .stream()
                .map(RoteiroViagemPassageiro::getNomePaciente)
                .collect(Collectors.joining(" - ")
                ));

        BOFactory.save(ocorrencia);
    }

    public List<RoteiroViagemPassageiro> getListaPassageiros() {
        return lstPassageiros;
    }
}
