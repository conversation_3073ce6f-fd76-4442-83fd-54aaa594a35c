package br.com.ksisolucoes.bo.vigilancia.estabelecimento;

import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoTreinamento;
import org.hibernate.criterion.Restrictions;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class AtualizarCredenciamentoEstabelecimento extends AbstractCommandTransaction {

    private RequerimentoVigilancia requerimentoVigilancia;

    public AtualizarCredenciamentoEstabelecimento(RequerimentoVigilancia requerimentoVigilancia) {
        this.requerimentoVigilancia = requerimentoVigilancia;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        Estabelecimento estabelecimento = (Estabelecimento) getSession().createCriteria(Estabelecimento.class)
                .add(Restrictions.eq(Estabelecimento.PROP_CODIGO, requerimentoVigilancia.getEstabelecimento().getCodigo()))
                .uniqueResult();

        if (estabelecimento != null) {
            RequerimentoTreinamento cred = (RequerimentoTreinamento) getSession().createCriteria(RequerimentoTreinamento.class)
                    .add(Restrictions.eq(RequerimentoTreinamento.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                    .uniqueResult();
            requerimentoVigilancia.setTipoDocumento(TipoSolicitacao.TipoDocumento.TREINAMENTOS_ALIMENTO.value());
            requerimentoVigilancia.getTipoSolicitacao().setTipoDocumento(TipoSolicitacao.TipoDocumento.TREINAMENTOS_ALIMENTO.value());

            Date validadeCredenciamento = BOFactory.getBO(VigilanciaFacade.class).calcularDataValidadeRequerimentos(requerimentoVigilancia, null);
            if (cred != null) {
                cred.setDataValidade(validadeCredenciamento);
                BOFactory.save(cred);
            }
            if (estabelecimento.getDataValidadePrimeiroCredenciamento() == null) {
                estabelecimento.setDataValidadePrimeiroCredenciamento(validadeCredenciamento);
            }
            BOFactory.save(estabelecimento);
        }
    }
}