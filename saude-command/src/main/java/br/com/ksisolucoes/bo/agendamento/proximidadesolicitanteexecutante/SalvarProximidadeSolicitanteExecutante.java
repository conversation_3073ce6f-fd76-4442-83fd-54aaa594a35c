package br.com.ksisolucoes.bo.agendamento.proximidadesolicitanteexecutante;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.agendamento.interfaces.dto.CadastroProximidadeSolicitanteExecutanteDTO;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.ProximidadeSolicitanteExecutante;
import br.com.ksisolucoes.vo.agendamento.ProximidadeSolicitanteExecutanteItem;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaProcedimentoItem;
import org.hibernate.criterion.Restrictions;

import java.util.List;

/**
 * Created by sulivan on 31/07/19.
 */
public class SalvarProximidadeSolicitanteExecutante extends AbstractCommandTransaction {

    private CadastroProximidadeSolicitanteExecutanteDTO dto;

    public SalvarProximidadeSolicitanteExecutante(CadastroProximidadeSolicitanteExecutanteDTO dto){
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        ProximidadeSolicitanteExecutante proximidadeSolicitanteExecutante = BOFactory.save(dto.getProximidadeSolicitanteExecutante());

        removerItens(proximidadeSolicitanteExecutante);

        if (CollectionUtils.isNotNullEmpty(dto.getProximidadeSolicitanteExecutanteItemList())) {
            Long i = 1L;
            for (ProximidadeSolicitanteExecutanteItem item : dto.getProximidadeSolicitanteExecutanteItemList()) {
                item.setCodigo(null);
                item.setVersion(null);
                item.setOrdem(i);
                item.setProximidadeSolicitanteExecutante(proximidadeSolicitanteExecutante);

                i++;

                BOFactory.save(item);
            }
        }
    }

    private void removerItens(ProximidadeSolicitanteExecutante proximidadeSolicitanteExecutante) throws ValidacaoException, DAOException {
        List<ProximidadeSolicitanteExecutanteItem> itemDeletarList = getSession().createCriteria(ProximidadeSolicitanteExecutanteItem.class)
                .add(Restrictions.eq(ProximidadeSolicitanteExecutanteItem.PROP_PROXIMIDADE_SOLICITANTE_EXECUTANTE, proximidadeSolicitanteExecutante))
                .list();

        if (CollectionUtils.isNotNullEmpty(itemDeletarList)) {
            for(ProximidadeSolicitanteExecutanteItem item : itemDeletarList){
                BOFactory.delete(item);
            }
        }
    }
}