package br.com.ksisolucoes.bo.cadsus.cds.esusfichausuariocadsusdomicilio;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaUsuarioCadsusDomicilio;

/**
 * Created by sulivan on 14/08/17.
 */
public class SaveEsusFichaUsuarioCadsusDomicilio extends SaveVO<EsusFichaUsuarioCadsusDomicilio> {

    public SaveEsusFichaUsuarioCadsusDomicilio(EsusFichaUsuarioCadsusDomicilio vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getDataCadastro() == null) {
            this.vo.setDataCadastro(DataUtil.getDataAtual());
        }
    }
}