/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.prontuario.basico.receituario;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.ReceituarioItemComponentesDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ReceituarioFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;
import org.hamcrest.Matchers;
import org.hibernate.LockMode;
import org.hibernate.criterion.Restrictions;

import java.util.ArrayList;
import java.util.List;
import java.util.ListIterator;

import static ch.lambdaj.Lambda.on;
import static ch.lambdaj.Lambda.select;

/**
 * <AUTHOR>
 */
public class ConfirmaEmissaoReceituario extends AbstractCommandTransaction<ConfirmaEmissaoReceituario> {

    private Long codigoAtendimento;
    private List<Long> codigoMedicamentoPaciente;
    private List<Receituario> receituarios;
    private Long codigoProfissional;
    private boolean gerarReceitasReceituario;

    public ConfirmaEmissaoReceituario(Long codigoAtendimento, List<Long> codigoMedicamentoPaciente, Long codigoProfissional, boolean gerarReceitasReceituario) {
        this.codigoAtendimento = codigoAtendimento;
        this.codigoMedicamentoPaciente = codigoMedicamentoPaciente;
        this.codigoProfissional = codigoProfissional;
        this.gerarReceitasReceituario = gerarReceitasReceituario;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        Atendimento atendimento = (Atendimento) this.getSession().get(Atendimento.class, this.codigoAtendimento);
        Profissional profissional = (Profissional) this.getSession().get(Profissional.class, this.codigoProfissional);

        List<MedicamentoPaciente> medicamentoPacientes = this.getSession().createCriteria(MedicamentoPaciente.class)
                .add(Restrictions.in(MedicamentoPaciente.PROP_CODIGO, this.codigoMedicamentoPaciente))
                .setLockMode(LockMode.UPGRADE).list();

        if (medicamentoPacientes.isEmpty()) {
            throw new ValidacaoException(Bundle.getStringApplication("naoExisteMedicamentoPaciente"));// ver uma msg melhor
        }

        List<List<MedicamentoPaciente>> list = agruparMedicamentosReceituario(medicamentoPacientes);

        Usuario usuario = SessaoAplicacaoImp.getInstance().getUsuario();

        for (List<MedicamentoPaciente> listTr : list) {
            Long qtdadeTipoReceita = Long.MAX_VALUE;
            if (!listTr.isEmpty() && Coalesce.asLong(listTr.get(0).getTipoReceita().getQuantidadeMaximaRequisicao()) > 0) {
                qtdadeTipoReceita = listTr.get(0).getTipoReceita().getQuantidadeMaximaRequisicao();
            }

            List<ReceituarioItem> receituarioItems = new ArrayList();
            int quantidadeAdicionada = 0;

            for (MedicamentoPaciente medicamentoPaciente : listTr) {

                ReceituarioItem receituarioItem = new ReceituarioItem();
                receituarioItem.setNomeProduto(medicamentoPaciente.getNomeProduto());
                receituarioItem.setProduto(medicamentoPaciente.getProduto());
                receituarioItem.setPosologia(medicamentoPaciente.getPosologia());
                receituarioItem.setQuantidadePrescrita(medicamentoPaciente.getQuantidadePrescrita());
                receituarioItem.setTipoViaMedicamento(medicamentoPaciente.getTipoViaMedicamento());
                receituarioItem.setMedicamentoPaciente(medicamentoPaciente);
                receituarioItem.setUnidade(medicamentoPaciente.getUnidade());
                receituarioItem.setQuantidade(medicamentoPaciente.getQuantidade());
                receituarioItem.setFrequencia(medicamentoPaciente.getFrequencia());
                receituarioItem.setIntervalo(medicamentoPaciente.getIntervalo());
                receituarioItem.setDiasTratamento(medicamentoPaciente.getDiasTratamento());
                receituarioItem.setTipoReceitaProdutoNaoCadastrado(medicamentoPaciente.getTipoReceita());

                validarUsoContinuo(medicamentoPaciente, receituarioItem);

                receituarioItems.add(receituarioItem);

                if (++quantidadeAdicionada >= qtdadeTipoReceita || listTr.indexOf(medicamentoPaciente) == listTr.size() - 1) {
                    Receituario receituario = new Receituario();
                    receituario.setEmpresa(SessaoAplicacaoImp.getInstance().getEmpresa());
                    receituario.setProfissional(profissional);
                    receituario.setUsuario(usuario);
                    receituario.setDataCadastro(Data.getDataAtual());
                    receituario.setTipoReceita(medicamentoPaciente.getTipoReceita());
                    receituario.setAtendimento(atendimento);
                    receituario.setUsuarioCadsus(atendimento.getUsuarioCadsus());
                    receituario.setCid(medicamentoPaciente.getCid());
                    receituario.setReceituarioItemList(receituarioItems);
                    receituario.setReceitaContinua(medicamentoPaciente.getUsoContinuo());
                    receituario.setDataReceituario(medicamentoPaciente.getDataCadastro());

                    List<ReceituarioItemComponentesDTO> receituarioItemComponentesDTOList = new ArrayList<ReceituarioItemComponentesDTO>();

                    for (ReceituarioItem item : receituario.getReceituarioItemList()) {
                        ReceituarioItemComponentesDTO receituarioItemComponentesDTO = new ReceituarioItemComponentesDTO();
                        receituarioItemComponentesDTO.setReceituarioItem(item);

                        receituarioItemComponentesDTOList.add(receituarioItemComponentesDTO);
                    }

                    BOFactory.getBO(ReceituarioFacade.class).salvarReceituarioPadrao(receituario, receituarioItemComponentesDTOList);

                    quantidadeAdicionada = 0;
                    receituarioItems = new ArrayList();
                }

            }
        }

        receituarios = LoadManager.getInstance(Receituario.class)
                .addProperties(new HQLProperties(Receituario.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(Receituario.PROP_ATENDIMENTO, atendimento))
                .addParameter(new QueryCustom.QueryCustomParameter(Receituario.PROP_SITUACAO, BuilderQueryCustom.QueryParameter.DIFERENTE, Receituario.Situacao.CANCELADO.value()))
                .start().getList();
    }

    /**
     * Agrupamento dos medicamentos da receita para gerar receituários.
     *
     * @param medicamentoPacientes
     * @return
     */
    private List<List<MedicamentoPaciente>> agruparMedicamentosReceituario(List<MedicamentoPaciente> medicamentoPacientes) {

        List<List<MedicamentoPaciente>> medicamentosAgrupados = new ArrayList<>();

        medicamentosAgrupados.add(this.filtraMedicamentosByTipoReceita(medicamentoPacientes, TipoReceita.RECEITA_ANTIMICROBIANA));
        medicamentosAgrupados.add(this.filtraMedicamentosByTipoReceita(medicamentoPacientes, TipoReceita.RECEITA_AZUL));
        medicamentosAgrupados.add(this.filtraMedicamentosByTipoReceita(medicamentoPacientes, TipoReceita.RECEITA_AMARELA));

        List<MedicamentoPaciente> medicamentosReceitaBranca = this.filtraMedicamentosByTipoReceita(medicamentoPacientes, TipoReceita.RECEITA_BRANCA);
        List<MedicamentoPaciente> medicamentosReceitaBasica = this.filtraMedicamentosByTipoReceita(medicamentoPacientes, TipoReceita.RECEITA_BASICA);
        return separaReceituario(medicamentosAgrupados, medicamentosReceitaBranca, medicamentosReceitaBasica);
    }

    private List<List<MedicamentoPaciente>> separaReceituario(List<List<MedicamentoPaciente>> medicamentosAgrupados,
                                                              List<MedicamentoPaciente> medicamentosReceitaBranca,
                                                              List<MedicamentoPaciente> medicamentosReceitaBasica) {

        boolean isSeparaReceituario = RepositoryComponentDefault.SIM.equals(this.separaReceituarioAutomaticamente());
        if (isSeparaReceituario) {
            medicamentosAgrupados.add(medicamentosReceitaBranca);
            medicamentosAgrupados.add(medicamentosReceitaBasica);
        } else {
            medicamentosReceitaBasica.addAll(medicamentosReceitaBranca);            
            ListIterator<Group<MedicamentoPaciente>> medicamentoslistIterator = Lambda.group(medicamentosReceitaBasica, Lambda.by(on(MedicamentoPaciente.class).getReceita())).subgroups().listIterator();
            while (medicamentoslistIterator.hasNext()) {
                Group<MedicamentoPaciente> medicamentosGroup = medicamentoslistIterator.next();
                medicamentosAgrupados.add(medicamentosGroup.findAll());
            }
        }
        return medicamentosAgrupados;
    }

    protected void validarUsoContinuo(MedicamentoPaciente medicamentoPaciente, ReceituarioItem receituarioItem) {
        //Quando produto for de uso continuo e a receita for de uso continuo seta o uso continuo sim
        if(medicamentoPaciente.getProduto() != null){
            if (RepositoryComponentDefault.SIM.equals(medicamentoPaciente.getProduto().getUsoContinuo())) {
                receituarioItem.setFlagContinuo(RepositoryComponentDefault.SIM);
                receituarioItem.setFlagTratamentoContinuo(RepositoryComponentDefault.SIM_LONG);
            }else{
                receituarioItem.setFlagContinuo(RepositoryComponentDefault.NAO);
                receituarioItem.setFlagTratamentoContinuo(RepositoryComponentDefault.NAO_LONG);
            }
        }else{
            if (RepositoryComponentDefault.SIM.equals(medicamentoPaciente.getUsoContinuo())) {
                receituarioItem.setFlagContinuo(RepositoryComponentDefault.SIM);
                receituarioItem.setFlagTratamentoContinuo(RepositoryComponentDefault.SIM_LONG);
            }else{
                receituarioItem.setFlagContinuo(RepositoryComponentDefault.NAO);
                receituarioItem.setFlagTratamentoContinuo(RepositoryComponentDefault.NAO_LONG);
            }
        }
    }

    public List<Receituario> getReceituarios() {
        return receituarios;
    }

    private List<MedicamentoPaciente> filtraMedicamentosByTipoReceita(List<MedicamentoPaciente> medicamentoPacientes, String tipoReceita) {
        return select(medicamentoPacientes, Lambda.having(on(MedicamentoPaciente.class).getTipoReceita().getTipoReceita(), Matchers.equalTo(tipoReceita)));
    }

    private String separaReceituarioAutomaticamente() {
        try {
            return BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("separaReceituarioAutomaticamente");
        } catch (DAOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
        return null;
    }
}
