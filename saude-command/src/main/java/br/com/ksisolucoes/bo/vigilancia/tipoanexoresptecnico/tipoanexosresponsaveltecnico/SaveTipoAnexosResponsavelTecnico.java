/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.vigilancia.tipoanexoresptecnico.tipoanexosresponsaveltecnico;
import  br.com.ksisolucoes.vo.vigilancia.tipoanexoresptecnico.TipoAnexosResponsavelTecnico;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;


/**
 *
 * <AUTHOR>
 */
public class SaveTipoAnexosResponsavelTecnico extends SaveVO<TipoAnexosResponsavelTecnico> {

    public SaveTipoAnexosResponsavelTecnico(TipoAnexosResponsavelTecnico vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {

        Usuario usuario;
        if (getSessao() == null) {
            usuario = new Usuario(Usuario.USUARIO_ADMINISTRADOR);
        } else {
            usuario = getSessao().<Usuario>getUsuario();
        }

        if (this.vo.getDataCadastro() == null) {
            this.vo.setDataCadastro(DataUtil.getDataAtual());
        }

        if (this.vo.getUsuarioCadastro() == null) {
            this.vo.setUsuarioCadastro(usuario);
        }

    }

}
