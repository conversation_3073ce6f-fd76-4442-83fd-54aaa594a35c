package br.com.ksisolucoes.bo.vigilancia.requerimentos.requerimentolivro;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoLivroDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaSolicitacaoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoLivro;
import org.hibernate.Criteria;
import org.hibernate.criterion.Restrictions;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SalvarRequerimentoAberturaLivro extends AbstractCommandTransaction<SalvarRequerimentoAberturaLivro> {
    
    private final RequerimentoLivroDTO dto;
    private RequerimentoVigilancia requerimentoVigilancia;

    public SalvarRequerimentoAberturaLivro(RequerimentoLivroDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        Criteria c = this.getSession().createCriteria(RequerimentoLivro.class)
                .add(Restrictions.eq(RequerimentoLivro.PROP_ESTABELECIMENTO, dto.getRequerimentoLivro().getEstabelecimento()));
        
        if(dto.getRequerimentoLivro().getCodigo() != null){
            c.add(Restrictions.ne(RequerimentoLivro.PROP_CODIGO, dto.getRequerimentoLivro().getCodigo()));
        }

        c.add(Restrictions.eq(RequerimentoLivro.PROP_TIPO, dto.getRequerimentoLivro().getTipo()));

        c.createCriteria(RequerimentoLivro.PROP_REQUERIMENTO_VIGILANCIA)
                .add(Restrictions.in(RequerimentoVigilancia.PROP_SITUACAO, RequerimentoVigilancia.Situacao.getSituacoesPendentes()));


        List<RequerimentoLivro> requerimentoLivroPendenteList = c.list();

        if (dto.getRequerimentoLivro().getTipo() == null){
            throw new ValidacaoException(Bundle.getStringApplication("msg_insira_um_tipo"));
        }

        if(CollectionUtils.isNotNullEmpty(requerimentoLivroPendenteList)){
            Estabelecimento e = dto.getRequerimentoLivro().getEstabelecimento();
            String msg = e.getFantasia() == null ? e.getRazaoSocial() : e.getRazaoSocial() + " / " + e.getFantasia();
            throw new ValidacaoException(Bundle.getStringApplication("msg_ja_existe_livro_aberto_para_estabelecimento_X", msg));
        }

        boolean isNew = false;
        if(dto.getRequerimentoLivro().getRequerimentoVigilancia().getCodigo() == null){
            isNew = true;
            dto.getRequerimentoLivro().getRequerimentoVigilancia().setEstabelecimento(dto.getRequerimentoLivro().getEstabelecimento());
            dto.getRequerimentoLivro().getRequerimentoVigilancia().setSituacao(RequerimentoVigilancia.Situacao.PENDENTE.value());
            dto.getRequerimentoLivro().getRequerimentoVigilancia().setTipoDocumento(TipoSolicitacao.TipoDocumento.ABERTURA_LIVRO_CONTROLE.value());
        }
        
        requerimentoVigilancia = BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilancia(new RequerimentoVigilanciaSolicitacaoDTO(dto.getRequerimentoLivro().getRequerimentoVigilancia()));
        
        BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(Bundle.getStringApplication("msg_requerimento_cadastrado"), requerimentoVigilancia, null);
            
        dto.getRequerimentoLivro().setRequerimentoVigilancia(requerimentoVigilancia);
        dto.setRequerimentoLivro(BOFactory.save(dto.getRequerimentoLivro()));
        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaAnexo(requerimentoVigilancia, dto.getRequerimentoVigilanciaAnexoDTOList(), dto.getRequerimentoVigilanciaAnexoExcluidoDTOList(), false);
        BOFactory.getBO(VigilanciaFacade.class).salvarEloRequerimentoVigilanciaSetorVigilancia(requerimentoVigilancia, dto.getEloRequerimentoVigilanciaSetorVigilanciaList(), dto.getEloRequerimentoVigilanciaSetorVigilanciaExcluirList());

        if(isNew) {
            BOFactory.getBO(VigilanciaFacade.class).enviarEmailNovoRequerimentoVigilancia(this.requerimentoVigilancia);
        }
    }
    
    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }
}