package br.com.ksisolucoes.bo.unidadesaude.historicopaciente;

import br.com.celk.util.DataUtil;
import br.com.celk.view.unidadesaude.atendimento.consulta.consutaHistoricoPaciente.dto.HistoricoPacienteDTO;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.celk.view.unidadesaude.atendimento.consulta.consutaHistoricoPaciente.dto.HistoricoPacienteDTOParam;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.basico.empresa.QueryConsultaDominioEstabelecimento;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.AtendimentoHelper;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import static br.com.ksisolucoes.vo.prontuario.basico.Atendimento.STATUS_OBSERVACAO;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.hibernate.Query;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaAtendimentosPaciente extends CommandQuery<QueryConsultaAtendimentosPaciente> {

    private HistoricoPacienteDTOParam param;
    private List<HistoricoPacienteDTO> resultado;

    private boolean restricaoEmpresa;

    public QueryConsultaAtendimentosPaciente(HistoricoPacienteDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {

        hql.addToSelect("atendimento.codigo", "atendimento.codigo");
        hql.addToSelect("atendimento.dataAtendimento", "atendimento.dataAtendimento");
        hql.addToSelect("atendimento.dataObservacao", "atendimento.dataObservacao");
        hql.addToSelect("atendimento.dataChegada", "atendimento.dataChegada");
        hql.addToSelect("atendimento.dataCadastro", "atendimento.dataCadastro");

        hql.addToSelect("naturezaProcuraTipoAtendimento.codigo", "atendimento.naturezaProcuraTipoAtendimento.codigo");
        hql.addToSelect("tipoAtendimento.codigo", "atendimento.naturezaProcuraTipoAtendimento.tipoAtendimento.codigo");
        hql.addToSelect("tipoAtendimento.descricao", "atendimento.naturezaProcuraTipoAtendimento.tipoAtendimento.descricao");
        hql.addToSelect("tipoAtendimentoPrincipal.codigo", "atendimento.naturezaProcuraTipoAtendimento.tipoAtendimento.tipoAtendimentoPrincipal.codigo");
        hql.addToSelect("tipoAtendimentoPrincipal.descricao", "atendimento.naturezaProcuraTipoAtendimento.tipoAtendimento.tipoAtendimentoPrincipal.descricao");
        hql.addToSelect("tipoProcedimentoAtendimento.codigo", "atendimento.tipoProcedimentoAtendimento.codigo");
        hql.addToSelect("tipoProcedimentoAtendimento.descricao", "atendimento.tipoProcedimentoAtendimento.descricao");
        hql.addToSelect("empresa.codigo", "atendimento.empresa.codigo");
        hql.addToSelect("empresa.descricao", "atendimento.empresa.descricao");
        hql.addToSelect("profissional.codigo", "atendimento.profissional.codigo");
        hql.addToSelect("profissional.nome", "atendimento.profissional.nome");

        hql.setTypeSelect(HistoricoPacienteDTO.class.getName());

        hql.setFrom("Atendimento atendimento"
                + " left join atendimento.usuarioCadsus usuarioCadsus"
                + " left join atendimento.naturezaProcuraTipoAtendimento naturezaProcuraTipoAtendimento"
                + " left join naturezaProcuraTipoAtendimento.tipoAtendimento tipoAtendimento"
                + " left join tipoAtendimento.tipoAtendimentoPrincipal tipoAtendimentoPrincipal"
                + " left join atendimento.empresa empresa"
                + " left join atendimento.profissional profissional"
                + " left join atendimento.tipoProcedimentoAtendimento tipoProcedimentoAtendimento"
        );

        hql.addToWhereWhithAnd("usuarioCadsus.codigo = ", this.param.getUsuarioCadsus().getCodigo());
        hql.addToWhereWhithAnd("atendimento.status in", Arrays.asList(Atendimento.STATUS_FINALIZADO, Atendimento.STATUS_FECHADO_SEM_ATENDIMENTO));
        hql.addToWhereWhithAnd("atendimento.codigo = atendimento.atendimentoPrincipal");

        try {
            Usuario usuario = SessaoAplicacaoImp.getInstance().<Usuario>getUsuario();
            if (!usuario.isNivelAdminOrMaster()) {
                if (usuario.getEmpresasUsuario() == null) {
                    usuario.setEmpresasUsuario(BOFactory.getBO(UsuarioFacade.class).getEmpresasUsuario(usuario));
                }
                if (usuario.getEmpresasUsuario() != null) {
                    restricaoEmpresa = true;
                    hql.addToWhereWhithAnd("(empresa.acessoRestrito = " + RepositoryComponentDefault.NAO_LONG + " or empresa.codigo in (:lista))");
                } else {
                    hql.addToWhereWhithAnd("empresa.codigo = ", SessaoAplicacaoImp.getInstance().<Empresa>getEmpresa().getCodigo());
                }
            }
        } catch (SGKException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        if (param.getPropSort() != null) {
            hql.addToOrder(param.getPropSort() + (param.isAscending() ? " asc" : " desc"));
        } else {
            hql.addToOrder("atendimento.codigo desc");
        }
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) {
        Usuario usuario = SessaoAplicacaoImp.getInstance().<Usuario>getUsuario();
        if (restricaoEmpresa) {
            try {
                usuario.setEmpresasUsuario(BOFactory.getBO(UsuarioFacade.class).getEmpresasUsuario(usuario));
                query.setParameterList("lista", usuario.getEmpresasUsuario());
            } catch (SGKException ex) {
                Logger.getLogger(QueryConsultaDominioEstabelecimento.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        String parametroTempo = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("CalculoTempo");
        int i = 0;
        for (HistoricoPacienteDTO resultado1 : resultado) {
            resultado.get(i).setUsuarioCadsus(param.getUsuarioCadsus());
            i++;
            resultado1.setTempoAtendimento(getTempoAtendimento(resultado1.getAtendimento().getDataAtendimento(), resultado1.getAtendimento().getDataChegada()));
        }
    }

    public static String getTempoAtendimento(Date dataAtendimento, Date dataChegada) {

        return Data.imprimirIntervaloHoras(dataChegada, dataAtendimento);

    }

    @Override
    protected void customQuery(Query query) {
        if (param.getPagina() != null) {
            query.setMaxResults(param.getPagina());
        }

    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        resultado = hql.getBeanList((List) result);
    }

    public List<HistoricoPacienteDTO> getResultado() {
        return resultado;
    }

}
