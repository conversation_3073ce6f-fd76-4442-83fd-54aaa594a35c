/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.cadsus.usuariocadsus;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHistoricoAprovacao;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHistoricoAprovacaoPK;
import br.com.ksisolucoes.vo.controle.Usuario;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class AprovarCadastroUsuarioCadsus extends AbstractCommandTransaction{

    private Long codigoUsuarioCadsus;
    private Long situacao;

    public AprovarCadastroUsuarioCadsus(Long codigoUsuarioCadsus) {
        this.codigoUsuarioCadsus = codigoUsuarioCadsus;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        Date dataAprovacao = Data.getDataAtual();
        UsuarioCadsusHistoricoAprovacao ucha = new UsuarioCadsusHistoricoAprovacao();

        UsuarioCadsus usuarioCadsus = (UsuarioCadsus) getSession().get(UsuarioCadsus.class, codigoUsuarioCadsus);
        usuarioCadsus.setDataAprovacao(dataAprovacao);

        if(LoadManager.getInstance(UsuarioCadsusCns.class)
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS), usuarioCadsus))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_EXCLUIDO), RepositoryComponentDefault.NAO_EXCLUIDO))
                .exists()){
            usuarioCadsus.setSituacao(UsuarioCadsus.SITUACAO_ATIVO);
            usuarioCadsus.setSituacaoAprovacao(UsuarioCadsus.SITUACAO_APROVACAO_CNS_EXISTENTE);
        }else{
            usuarioCadsus.setSituacaoAprovacao(UsuarioCadsus.SITUACAO_APROVACAO_ENCAMINHADO);
            usuarioCadsus.setSituacao(UsuarioCadsus.SITUACAO_PROVISORIO);
        }

        this.situacao = usuarioCadsus.getSituacao();

        ucha.setId(new UsuarioCadsusHistoricoAprovacaoPK());
        ucha.getId().setUsuarioCadsus(usuarioCadsus);
        ucha.getId().setDataAprovacao(dataAprovacao);
        ucha.setUsuario((Usuario) getSessao().getUsuario());
        ucha.setSituacao(usuarioCadsus.getSituacaoAprovacao());
        ucha.setObservacao(Bundle.getStringApplication("rotulo_aprovado"));

        BOFactory.getBO(CadastroFacade.class).save(ucha);
    }

    public Long getSituacao() {
        return situacao;
    }

}
