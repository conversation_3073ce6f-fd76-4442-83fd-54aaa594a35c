/*
 * QueryProcessoControleInventario.java
 *
 * Created on 8 de Setembro de 2006, 09:30
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.entradas.estoque.controleinventario;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.materiais.inventario.InventarioHelper;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.ControleInventario;
import br.com.ksisolucoes.vo.entradas.estoque.Inventario;
import br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura;
import org.hibernate.Query;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryProcessoControleInventario extends CommandQuery<QueryProcessoControleInventario> {

    private List<LocalizacaoEstrutura> listLocalizacao;
    private Bean bean;
    private List<ControleInventario> dtoList;
    
    /** Creates a new instance of QueryRetornaControleInventario */
    public QueryProcessoControleInventario(Bean bean) throws ValidacaoException {
        this.bean = bean;
        this.listLocalizacao = InventarioHelper.listaLocalizacaoInventario(this.bean.getInventario());
    }

    protected void createQuery(HQLHelper hql) throws ValidacaoException {
        
        /*..........Select............*/
        hql.addToSelect(new HQLProperties(ControleInventario.class,"ci").getProperties());
        hql.setConvertToLeftJoin(true);
        
        // from
        hql.addToFrom( ControleInventario.class.getName() + " ci " );
        
        hql.setTypeSelect(ControleInventario.class.getName());

        if ( !this.bean.getEmpresas().isEmpty() ){
            hql.addToWhereWhithAnd( "ci.id.empresa in (:empresas)" );
        }
        if (this.bean.getInventario() != null) {
            if (CollectionUtils.isNotNullEmpty(listLocalizacao)) {
                hql.addToWhereWhithAnd("ci.localizacaoEstrutura in (:localizacao)");
            }
            hql.addToWhereWhithAnd("ci.inventario = (:inventario)");
        }

        if ( !this.bean.getStatus().isEmpty() ){
            hql.addToWhereWhithAnd( "ci.status in (:status)" );
        }
        if ( this.bean.getDate() != null ){
            hql.addToWhereWhithAnd( "ci.dataProcessamento >= :dataProcessamento" );
            hql.addToWhereWhithAnd( "ci.dataProcessamento = " +
                    " (select max(dataProcessamento) from " 
                    + ControleInventario.class.getName() + 
                    " where empresa = ci.id.empresa and produto = ci.produto and ci.usuario = usuario) " );
            hql.addToWhereWhithAnd( "ci.produto not in (select produto from " 
                    + ControleInventario.class.getName() + 
                    " where status = 0 and empresa = ci.id.empresa and produto = ci.produto)" );
        }
        
        hql.addToOrder("ci.id.codigo desc");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.dtoList = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
    
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException {
        if ( !this.bean.getEmpresas().isEmpty() ){
            hql.setParameterValue( query, "empresas", this.bean.getEmpresas() );
        }
        if ( !this.bean.getStatus().isEmpty() ){
            hql.setParameterValue( query, "status", this.bean.getStatus() );
        }
        if ( this.bean.getDate() != null ){
            hql.setParameterValue( query, "dataProcessamento", this.bean.getDate() );
        }
        if (this.bean.getInventario() != null && CollectionUtils.isNotNullEmpty(listLocalizacao)) {
            hql.setParameterValue( query, "localizacao", listLocalizacao);
        }
        if (this.bean.getInventario() != null) {
            hql.setParameterValue( query, "inventario", this.bean.getInventario());
        }
    }
    
    public List<ControleInventario> getDtoList() {
        return dtoList;
    }
    
    public static class Bean implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private List< Empresa > empresas;
        private List< Long > status;
        private Date date;
        private Inventario inventario;
        
        public static long getSerialVersionUID() {
            return serialVersionUID;
        }
        
        public List<Empresa> getEmpresas() {
            return empresas;
        }
        
        public void setEmpresas(List<Empresa> empresas) {
            this.empresas = empresas;
        }
        
        public List< Long > getStatus() {
            if ( status == null ){
                status = new ArrayList< Long >();
            }
            return status;
        }
        
        public void setStatus(List< Long > status) {
            this.status = status;
        }
        
        public Date getDate() {
            return date;
        }
        
        public void setDate(Date date) {
            this.date = date;
        }

        public Inventario getInventario() {
            return inventario;
        }

        public void setInventario(Inventario inventario) {
            this.inventario = inventario;
        }
    }
}
