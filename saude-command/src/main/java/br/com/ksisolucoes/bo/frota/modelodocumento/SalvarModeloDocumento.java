package br.com.ksisolucoes.bo.frota.modelodocumento;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.EloGrupoAtendimentoCboTipoDocAtend;
import br.com.ksisolucoes.vo.prontuario.basico.TipoDocumentoAtendimento;
import br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCbo;

import java.util.ArrayList;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR> Santos
 */
public class SalvarModeloDocumento extends AbstractCommandTransaction<SalvarModeloDocumento> {

    TipoDocumentoAtendimento tipoDocumentoAtendimento;
    List<GrupoAtendimentoCbo> grupoAtendimentoCboList;

    public SalvarModeloDocumento(TipoDocumentoAtendimento tipoDocumentoAtendimento, List<GrupoAtendimentoCbo> grupoAtendimentoCboList) {
        this.tipoDocumentoAtendimento = tipoDocumentoAtendimento;
        this.grupoAtendimentoCboList = grupoAtendimentoCboList;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        validate();

        tipoDocumentoAtendimento = BOFactory.save(tipoDocumentoAtendimento);
        salvarGruposAtendimentoCbo(tipoDocumentoAtendimento);
    }

    private void validate() throws ValidacaoException {
        if (RepositoryComponentDefault.NAO_LONG.equals(tipoDocumentoAtendimento.getCompartilhado())
                && tipoDocumentoAtendimento.getProfissional() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_profissional"));
        }
        if (grupoAtendimentoCboList != null && grupoAtendimentoCboList.size() > 4) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_max_4_grupos"));
        }

        TipoDocumentoAtendimento proxy = on(TipoDocumentoAtendimento.class);

        TipoDocumentoAtendimento tipoDoc = LoadManager.getInstance(TipoDocumentoAtendimento.class)
                .addProperty(path(proxy.getCodigo()))
                .addProperty(path(proxy.getModelo()))
                .addProperty(path(proxy.getTipoModelo()))
                .addProperty(path(proxy.getDescricao()))
                .addParameter(new QueryCustom.QueryCustomParameter(TipoDocumentoAtendimento.PROP_TIPO_MODELO, BuilderQueryCustom.QueryParameter.IGUAL, TipoDocumentoAtendimento.TipoDocumento.TIPO_EMAIL.value()))
                .start().getVO();

        if(tipoDoc != null && tipoDocumentoAtendimento.getTipoModelo().equals(TipoDocumentoAtendimento.TipoDocumento.TIPO_EMAIL.value()) && !tipoDoc.getCodigo().equals(tipoDocumentoAtendimento.getCodigo())){
            throw new ValidacaoException(String.format("Já existe o modelo %s cadastrado", tipoDoc.getDescricao()));
        }
    }

    private void salvarGruposAtendimentoCbo(TipoDocumentoAtendimento object) throws DAOException, ValidacaoException {
        List<EloGrupoAtendimentoCboTipoDocAtend> eloGrupoAtendimentoCboTipoDocAtendList = new ArrayList<>();

        if (grupoAtendimentoCboList != null) {
            for (GrupoAtendimentoCbo grupoAtendimentoCbo : grupoAtendimentoCboList) {
                EloGrupoAtendimentoCboTipoDocAtend eloGrupoAtendimentoCboTipoDocAtend = new EloGrupoAtendimentoCboTipoDocAtend();

                eloGrupoAtendimentoCboTipoDocAtend.setGrupoAtendimentoCbo(grupoAtendimentoCbo);
                eloGrupoAtendimentoCboTipoDocAtend.setTipoDocumentoAtendimento(object);

                eloGrupoAtendimentoCboTipoDocAtendList.add(eloGrupoAtendimentoCboTipoDocAtend);
            }
        }

        VOUtils.persistirListaVosModificados(
                EloGrupoAtendimentoCboTipoDocAtend.class,
                eloGrupoAtendimentoCboTipoDocAtendList,
                new QueryCustom.QueryCustomParameter(EloGrupoAtendimentoCboTipoDocAtend.PROP_TIPO_DOCUMENTO_ATENDIMENTO, object)
        );
    }

    public TipoDocumentoAtendimento getTipoDocumentoAtendimento() {
        return tipoDocumentoAtendimento;
    }
}
