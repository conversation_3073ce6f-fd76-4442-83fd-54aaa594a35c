/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.basico.parametromaterial;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.bo.dto.EventoSistemaDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Reflection;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.EventoSistema;
import br.com.ksisolucoes.vo.basico.ParametroMaterial;
import org.apache.commons.lang.ObjectUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 *
 * <AUTHOR>
 */
public class SaveParametroMaterial extends SaveVO<ParametroMaterial> {

    public SaveParametroMaterial(ParametroMaterial vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        gerarEventoSistema();
    }

    private void gerarEventoSistema() {
        if (this.vo.getCodigo() == null) {
            return;
        }

        try {
            ParametroMaterial origin = LoadManager.getInstance(ParametroMaterial.class)
                    .addProperties(new HQLProperties(ParametroMaterial.class).getProperties())
                    .setId(CargaBasicoPadrao.CODIGO_PARAMETROS_PADRAO).start().getVO();

            if (origin != null) {
                String[] singleProperties = new HQLProperties(ParametroMaterial.class).getSingleProperties();
                for (String singleProperty : singleProperties) {
                    Method method = Reflection.getMethodByName(ParametroMaterial.class, Reflection.getGetMethodName(singleProperty));
                    Object valueModify = method.invoke(vo);
                    Object valueOrigin = method.invoke(origin);
                    if (!ObjectUtils.equals(valueModify, valueOrigin)) {
                        String descricao = Bundle.getStringApplication("msg_parametro_X_alterado_X_para_X", singleProperty.toString(), valueOrigin, valueModify);

                        EventoSistemaDTO dto = new EventoSistemaDTO();
                        dto.setDescricao(descricao);
                        dto.setTipoEvento(EventoSistema.TipoEvento.PROGRAMA.value());
                        dto.setNivelCriticidade(EventoSistema.NivelCriticidade.INFORMACAO.value());
                        dto.setFonteEvento("Parâmetros");
                        dto.setKeyword(Bundle.getStringApplication("key_configuracao"));
                        dto.setIdentificacaoEvento(EventoSistema.IdentificacaoEvento.CONFIGURACAO.value());

                        try {
                            BOFactory.getBO(CommomFacade.class).gerarEventoSistema(dto);
                        } catch (DAOException e) {
                            Loggable.log.error(e.getMessage());
                        } catch (ValidacaoException e) {
                            Loggable.log.error(e.getMessage());
                        }
                    }
                }
            }
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e ) {
            Loggable.log.error(e.getMessage());
        }
    }

}
