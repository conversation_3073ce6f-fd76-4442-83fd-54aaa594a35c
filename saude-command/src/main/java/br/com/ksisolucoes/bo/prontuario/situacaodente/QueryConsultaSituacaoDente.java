package br.com.ksisolucoes.bo.prontuario.situacaodente;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.situacaodente.interfaces.dto.QueryConsultaSituacaoDenteDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.prontuario.basico.SituacaoDente;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaSituacaoDente extends CommandQueryPager<QueryConsultaSituacaoDente> {

    private QueryConsultaSituacaoDenteDTOParam param;

    public QueryConsultaSituacaoDente(QueryConsultaSituacaoDenteDTOParam param) {
        this.param = param;
    }
    
    @Override
    protected void createQuery(HQLHelper hql) {
        
        hql.addToSelect("sd.codigo", true);
        hql.addToSelect("sd.referencia", true);
        hql.addToSelect("sd.descricao", true);
        hql.addToSelect("sd.tipoSituacao", true);
        
        hql.setTypeSelect(SituacaoDente.class.getName());
        hql.addToFrom("SituacaoDente sd");
        
        hql.addToWhereWhithAnd(hql.getConsultaLiked("sd.descricao", param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("sd.referencia || ' ' || sd.descricao",param.getKeyword()));

        if(param.getTipoSituacao() != null){
            hql.addToWhereWhithAnd("sd.tipoSituacao = ", param.getTipoSituacao());
        }
        
        if(param.getPropSort() != null){
            hql.addToOrder("sd."+param.getPropSort()+" "+ (param.isAscending()?"asc":"desc"));
        }else{
            hql.addToOrder("sd.descricao");
        }
    }
    
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list =  hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
