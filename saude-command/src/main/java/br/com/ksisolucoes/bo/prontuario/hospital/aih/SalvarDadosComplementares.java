package br.com.ksisolucoes.bo.prontuario.hospital.aih;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.hospital.datasus.sisaih.DadosComplementares;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SalvarDadosComplementares extends AbstractCommandTransaction {

    List<DadosComplementares> dadosComplementaresList;

    public SalvarDadosComplementares(List<DadosComplementares> dadosComplementaresList) {
        this.dadosComplementaresList = dadosComplementaresList;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        VOUtils.persistirListaVosModificados(DadosComplementares.class, dadosComplementaresList, null);
    }

}
