/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.prontuario.basico.classificacaocid;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.ClassificacaoCids;
import br.com.ksisolucoes.vo.prontuario.basico.Cid;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SaveClassificacaoCid extends AbstractCommandTransaction {

    private ClassificacaoCids clsCid;
    private List<Cid> cidList;

    public SaveClassificacaoCid(ClassificacaoCids cid, List<Cid> cidList) {
        this.clsCid = cid;
        this.cidList = cidList;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (Coalesce.asString(clsCid).isEmpty()) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_descricao"));
        }

        BOFactory.save(clsCid);

        List<Cid> tbCidList = LoadManager.getInstance(Cid.class)
                .addParameter(new QueryCustom.QueryCustomParameter(Cid.PROP_CID_CLASSIFICACAO, clsCid))
                .start().getList();

        tbCidList.removeAll(cidList);
        for (Cid c : tbCidList) {
            c.setCidClassificacao(null);
            BOFactory.save(c);
        }
        for (Cid cid : cidList) {
            cid.setCidClassificacao(clsCid);
            BOFactory.save(cid);
        }

    }

}
