package br.com.ksisolucoes.bo.controle.base;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.controle.interfaces.dto.UsuarioEmpresaBpaDTO;
import br.com.ksisolucoes.bo.controle.interfaces.dto.UsuarioEmpresaBpaDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryUsuarioEmpresaBpa extends CommandQuery<QueryUsuarioEmpresaBpa> {

    private UsuarioEmpresaBpaDTOParam param;
    private List<UsuarioEmpresaBpaDTO> result;

    public QueryUsuarioEmpresaBpa(UsuarioEmpresaBpaDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(UsuarioEmpresaBpaDTO.class.getName());

        hql.addToSelect("empresaBpa.codigo", "empresaBpa.codigo");
        hql.addToSelect("empresaBpa.descricao", "empresaBpa.descricao");

        hql.addToFrom("Usuario u"
                + " left join u.profissional p ");
        
        hql.addToFrom("UsuarioEmpresa usuarioEmpresa"
                + " left join usuarioEmpresa.usuario usuario"
                + " left join usuarioEmpresa.empresa empresa"
                + " left join usuarioEmpresa.empresaBpa empresaBpa");

        hql.addToWhereWhithAnd("u.codigo = usuario.codigo");
        hql.addToWhereWhithAnd("u.flagUsuarioTemporario = ", RepositoryComponentDefault.NAO_LONG);
        
        hql.addToWhereWhithAnd("empresa.codigo = ", param.getEmpresa().getCodigo());
        hql.addToWhereWhithAnd("p.codigo = ", param.getProfissional().getCodigo());
        hql.addToWhereWhithAnd("empresaBpa.codigo is not null");
    }
    
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String,Object>>)result);
    }

    @Override
    public List<UsuarioEmpresaBpaDTO> getResult() {
        return this.result;
    }
    
}
