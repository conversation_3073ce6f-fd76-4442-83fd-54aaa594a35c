package br.com.ksisolucoes.bo.atendimento.bpaprocesso;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atendimento.BpaProcesso;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class SaveBpaProcesso extends SaveVO<BpaProcesso> {

    public SaveBpaProcesso(BpaProcesso vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (BpaProcesso.STATUS_CANCELADO.equals(this.vo.getStatus())) {
            this.vo.setUsuarioCancelamento(getSessao().getUsuario());
            this.vo.setDataCancelamento(new Date(System.currentTimeMillis()));
        }
    }
}
