package br.com.ksisolucoes.bo.tfd;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.encaminhamento.interfaces.dto.LaudoTfdDTOParam;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryPagerConsultaLaudoTfd extends CommandQueryPager<QueryPagerConsultaLaudoTfd> {

    private LaudoTfdDTOParam param;

    public QueryPagerConsultaLaudoTfd(LaudoTfdDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        ConsultaLaudoTfdHqlProperties.initHql(hql, param);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

}
