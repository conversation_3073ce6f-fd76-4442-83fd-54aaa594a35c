package br.com.ksisolucoes.bo.vigilancia.forcatarefa;

import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FichaForcaTarefaCovid19DTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Email;
import br.com.ksisolucoes.util.MailAttachment;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.forcatarefa.FichaForcaTarefaCovid19;

import javax.ws.rs.core.MediaType;
import java.util.UUID;

public class EnviarEmailForcaTarefaCovid19 extends AbstractCommandTransaction {

    private static String BREAK_LINE = "<br/>";
    private FichaForcaTarefaCovid19DTO fichaForcaTarefaCovid19Dto;

    public EnviarEmailForcaTarefaCovid19(FichaForcaTarefaCovid19DTO fichaForcaTarefaCovid19Dto) {
        this.fichaForcaTarefaCovid19Dto = fichaForcaTarefaCovid19Dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        FichaForcaTarefaCovid19 fichaForcaTarefaCovid19 = fichaForcaTarefaCovid19Dto.getFichaForcaTarefaCovid19();
        try {
            String cid = UUID.randomUUID().toString();
            Email.create()
                .assunto("Força tarefa COVID-19 - Florianópolis")
                .para(fichaForcaTarefaCovid19.getEmail())
                .mensagem(this.getMensagem(fichaForcaTarefaCovid19Dto, cid))
                .content(MediaType.TEXT_HTML)
                .addAttachment(new MailAttachment(
                    cid,
                    fichaForcaTarefaCovid19Dto.getQrCode(),
                    "qrcode",
                    "PNG"
                ))
                .send();
        } catch (Throwable ex) {
            Loggable.log.warn("Email: " + fichaForcaTarefaCovid19.getEmail() + " - " + ex.getMessage());
            throw new ValidacaoException("Não foi possível enviar o e-mail. Verifique se o e-mail definido está correto e tente novamente.");
        }
    }

    private String getMensagem(FichaForcaTarefaCovid19DTO dto, String cid) {
        StringBuilder sb = new StringBuilder();
        sb.append("Olá, ").append(bold(dto.getFichaForcaTarefaCovid19().getNome())).append("!").append(BREAK_LINE);
        sb.append("Abaixo segue o seu QRCode para apresentação no desembarque em Florianópolis:").append(BREAK_LINE).append(BREAK_LINE);
        sb.append("<img src=\"cid:").append(cid).append("\" />").append(BREAK_LINE).append(BREAK_LINE);
        sb.append(bold("Guarde este e-mail ou faça um print do QRCode para agilizar o procedimento.")).append(BREAK_LINE).append(BREAK_LINE);
        if (dto.getAceiteTermo()) {
            sb.append("Se não apresentou nenhum dos sintomas, <b>siga</b> o termo abaixo:").append(BREAK_LINE).append(BREAK_LINE);
            sb.append("<i>" + dto.getTermo() + "</i>").append(BREAK_LINE).append(BREAK_LINE);
        }
        sb.append("Esta é uma mensagem automática, favor não responder.");
        return sb.toString();
    }

    private static String bold(String content) {
        return "<b>" + content + "</b>";
    }

}
