package br.com.ksisolucoes.bo.prontuario.hospital.quartointernacao;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto;
import br.com.ksisolucoes.vo.prontuario.hospital.QuartoInternacao;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ReativarQuartoIsoladoInternacao extends AbstractCommandTransaction {

    private QuartoInternacao quartoInternacao;

    public ReativarQuartoIsoladoInternacao(QuartoInternacao quartoInternacao) {
        this.quartoInternacao = quartoInternacao;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        List<LeitoQuarto> lstLeitoQuarto = LoadManager.getInstance(LeitoQuarto.class)
                .addParameter(new QueryCustom.QueryCustomParameter(LeitoQuarto.PROP_QUARTO_INTERNACAO, this.quartoInternacao))
                .addParameter(new QueryCustom.QueryCustomParameter(LeitoQuarto.PROP_SITUACAO, LeitoQuarto.Situacao.ISOLADO.value()))
                .start().getList();
        
        for (LeitoQuarto leitoQuarto : lstLeitoQuarto) {
            BOFactory.getBO(HospitalFacade.class).reativarLeitoQuarto(leitoQuarto);
        }
        quartoInternacao.setSituacao(QuartoInternacao.Situacao.ATIVO.value());
        BOFactory.save(quartoInternacao);
    }
}
