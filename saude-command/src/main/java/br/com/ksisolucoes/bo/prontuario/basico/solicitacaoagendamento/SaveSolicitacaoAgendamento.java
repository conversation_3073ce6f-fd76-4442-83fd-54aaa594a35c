package br.com.ksisolucoes.bo.prontuario.basico.solicitacaoagendamento;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import br.com.ksisolucoes.vo.prontuario.exame.SolicitacaoAgendamentoExame;
import org.hibernate.Criteria;
import org.hibernate.Query;
import org.hibernate.criterion.Conjunction;
import org.hibernate.criterion.Restrictions;

import java.text.ParseException;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SaveSolicitacaoAgendamento extends SaveVO<SolicitacaoAgendamento> {


    private String tipoControleRegulacao;
    private String habilitaClassificacaoRiscoExame;
    private Long habilitaClassificacaoRiscoEncaminhamentoEspecialista = 0L;

    public SaveSolicitacaoAgendamento(SolicitacaoAgendamento vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (vo.getSituacaoContato() == null) {
            vo.setSituacaoContato(SolicitacaoAgendamento.SituacaoContato.NAO_CONTACTADO.value());
        }
        if (vo.getNomeProfissionalOrigem() == null && vo.getProfissional() != null) {
            vo.setNomeProfissionalOrigem(vo.getProfissional().getNome());
        }
        if (vo.getNomeProfissionalDesejado() == null && vo.getProfissionalDesejado() != null) {
            vo.setNomeProfissionalDesejado(vo.getProfissionalDesejado().getNome());
        }
        if(vo.getDataCadastro() == null){
            vo.setDataCadastro(Data.getDataAtual());
        }
        if(vo.getUsuario() == null){
            vo.setUsuario(getSessao().getUsuario());
        }

        if  (vo.getFlagAvaliacaoAprovado() == null){
            vo.setFlagAvaliacaoAprovado(RepositoryComponentDefault.NAO_LONG);
        }

        if  (vo.getFlagBloqueado() == null){
            vo.setFlagBloqueado(RepositoryComponentDefault.NAO_LONG);
        }

        if  (vo.getFlagDevolvido() == null){
            vo.setFlagDevolvido(RepositoryComponentDefault.NAO_LONG);
        }

        TipoProcedimento tipoProcedimento = (TipoProcedimento) getSession().get(TipoProcedimento.class, this.vo.getTipoProcedimento().getCodigo());
        vo.setTipoProcedimento(tipoProcedimento);

        if(vo.getStatus() == null){
            String controlaEnvioSolicitacoes = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("ControlaEnvioSolicitacoes");

            if (RepositoryComponentDefault.SIM.equals(controlaEnvioSolicitacoes) && RepositoryComponentDefault.SIM_LONG.equals(tipoProcedimento.getFlagControlaLote())) {
                vo.setStatus(SolicitacaoAgendamento.STATUS_AGUARDANDO_ANALISE);
            } else if (RepositoryComponentDefault.SIM.equals(vo.getTipoProcedimento().getRegulado()) && RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_TIPO_PROCEDIMENTO.equals(getTipoControleRegulacao())) {
                vo.setStatus(SolicitacaoAgendamento.STATUS_REGULACAO_PENDENTE);
            } else {
                vo.setStatus(SolicitacaoAgendamento.STATUS_FILA_ESPERA);
            }
        }

        if (vo.getTipoFila() == null) {
            vo.setTipoFila(SolicitacaoAgendamento.TIPO_FILA_NORMAL);
        }

        String separarFilaSolicitacaoParam = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("SepararFilaSolicitacao");
        if (RepositoryComponentDefault.SIM.equals(separarFilaSolicitacaoParam) && SolicitacaoAgendamento.STATUS_REGULACAO.contains(vo.getStatus())) {
            vo.setTipoFila(SolicitacaoAgendamento.TIPO_FILA_REGULACAO);
        }

        if (vo.getProcedimento() == null) {
            if (!vo.getTipoProcedimento().getTipoProcedimentoClassificacao().pertenceClassificacaoExame()) {
                vo.setProcedimento(vo.getTipoProcedimento().getProcedimento());
                if (vo.getProcedimento() == null) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_procedimento_nao_definido_para_o_tipo_de_procedimento_selecionado", vo.getTipoProcedimento().getDescricao()));
                }
            }else{
                throw new ValidacaoException(Bundle.getStringApplication("msg_tipo_procedimento_nao_define_procedimento", vo.getTipoProcedimento().getDescricao()));
            }
        }

        if (vo.getCodigo() == null
                && RepositoryComponentDefault.SIM_LONG.equals(getSolicitacaoAgendamento().getTipoProcedimento().getFlagValidaPendente())
                && !SolicitacaoAgendamento.STATUS_CANCELADO.equals(vo.getStatus())) {
            validarSolicitacaoEmAberto();
        }

        if(vo.getEmpresaLocalAtual() == null){
            vo.setEmpresaLocalAtual(vo.getEmpresa());
        }
        
        if(SolicitacaoAgendamento.PRIORIDADE_URGENTE.equals(vo.getPrioridade())){
            if(vo.getUsuarioUrgente() == null){
                vo.setUsuarioUrgente(getSessao().getUsuario());
            }
        } else {
            vo.setUsuarioUrgente(null);            
        }

        if (vo.getPrioridade() == null){
            if (getHabilitaClassificacaoRiscoExame()) {
                vo.setPrioridade(SolicitacaoAgendamento.PRIORIDADE_POUCO_URGENTE);
            } else {
                vo.setPrioridade(SolicitacaoAgendamento.PRIORIDADE_ELETIVO);
            }
        }

        if (vo.getSolicitarPrioridade() == null){
            vo.setSolicitarPrioridade(RepositoryComponentDefault.NAO_LONG);
        }
        vo.setDataIntegracaoInovamfri(null);

        //Validação feita para subtrair o status conforme regra aplicada na classe ContatoAgendamentoAcompanhamentoPage no metodo salvarOcorrencia;
        if (vo.getStatus() >= 100){
            vo.setStatus(vo.getStatus() - 100);
        } else {
            validaTipoProcedimento(vo);
        }

        if (this.vo.getFlagEnviarRegulacao() == null) {
            this.vo.setFlagEnviarRegulacao(RepositoryComponentDefault.NAO_LONG);
        }

        vo.setDataAlteracao(Data.getDataAtual());
    }

    @Override
    protected void depoisSave() throws ValidacaoException, DAOException {
        if (!SolicitacaoAgendamento.PRIORIDADE_URGENTE.equals(vo.getPrioridade())) {
            if (vo.getSolicitacaoAgendamentoPosicaoFila() != null && vo.getSolicitacaoAgendamentoPosicaoFila().getCodigo() != null) {
                atualizarPosicaoFilaManual(vo.getSolicitacaoAgendamentoPosicaoFila().getCodigo());
            }
        }
        BOFactory.getBO(AgendamentoFacade.class).gerarSolicitacaoAgendamentoPosicaoFila(vo);
    }

    private void atualizarPosicaoFilaManual(Long codigoSolicitacaoPosicaoFila) {
        String update = "UPDATE solicitacao_agendamento_posicao_fila SET posicao_fila_espera_manual = null  WHERE cd_solicitacao = ? ";
        Query query = getSession().createSQLQuery(update);
        query.setLong(0, codigoSolicitacaoPosicaoFila);

        query.executeUpdate();
    }

    public SolicitacaoAgendamento getSolicitacaoAgendamento() {
        return vo;
    }

    private void validarSolicitacaoEmAberto() throws ValidacaoException {
        if (SolicitacaoAgendamento.TIPO_CONSULTA_RETORNO.equals(vo.getTipoConsulta()) && RepositoryComponentDefault.SIM.equals(vo.getTipoProcedimento().getFlagTfd())) {
            return;
        }
        
        List<SolicitacaoAgendamento> solicitacoesAbertas = null;
        Criteria c = getSession().createCriteria(SolicitacaoAgendamento.class);
        c.add(Restrictions.eq(SolicitacaoAgendamento.PROP_TIPO_PROCEDIMENTO, vo.getTipoProcedimento()));
        c.add(Restrictions.eq(SolicitacaoAgendamento.PROP_USUARIO_CADSUS, vo.getUsuarioCadsus()));
        if(vo.getCodigo() != null){
            c.add(Restrictions.ne(SolicitacaoAgendamento.PROP_CODIGO, vo.getCodigo()));
        }

        Conjunction dataAgendamento = Restrictions.conjunction();
        dataAgendamento.add(Restrictions.gt(SolicitacaoAgendamento.PROP_DATA_AGENDAMENTO, Data.addDias(Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial(), 1)));
        dataAgendamento.add(Restrictions.in(SolicitacaoAgendamento.PROP_STATUS, Arrays.asList(SolicitacaoAgendamento.STATUS_AGENDADO, SolicitacaoAgendamento.STATUS_REGULACAO_AGENDADO, SolicitacaoAgendamento.STATUS_AGENDADO_FORA_REDE)));
        dataAgendamento.add(Restrictions.isNull(SolicitacaoAgendamento.PROP_DATA_FECHAMENTO));

        if (vo.getTipoProcedimento().habilitaAgendamentoGrupo() && vo.getDataAgendamento() != null) {
            dataAgendamento.add(Restrictions.ge(SolicitacaoAgendamento.PROP_DATA_AGENDAMENTO, Data.adjustRangeHour(vo.getDataAgendamento()).getDataInicial()));
            dataAgendamento.add(Restrictions.le(SolicitacaoAgendamento.PROP_DATA_AGENDAMENTO, Data.adjustRangeHour(vo.getDataAgendamento()).getDataFinal()));
        }

        c.add(Restrictions.disjunction()
                .add(dataAgendamento)
                .add(Restrictions.in(SolicitacaoAgendamento.PROP_STATUS, Arrays.asList(SolicitacaoAgendamento.STATUS_FILA_ESPERA, SolicitacaoAgendamento.STATUS_REGULACAO_DEVOLVIDO,SolicitacaoAgendamento.STATUS_REGULACAO_PENDENTE))));
        solicitacoesAbertas = c.list();

        if(getSolicitacaoAgendamento().isValidarExisteOutraSolicitacao()) {
            if(CollectionUtils.isNotNullEmpty(solicitacoesAbertas)){
                for (SolicitacaoAgendamento sa : solicitacoesAbertas) {
                    if(sa.getTipoProcedimento().getTipoProcedimentoClassificacao().pertenceClassificacaoExame()){
                        if(sa.getProcedimento().equals(vo.getProcedimento())){
                            if(vo.getCodigo() != null) {
                                List<SolicitacaoAgendamentoExame> lstSolicitacaoAgendamentoExames = LoadManager.getInstance(SolicitacaoAgendamentoExame.class)
                                    .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoExame.PROP_SOLICITACAO_AGENDAMENTO, vo))
                                    .start().getList();
                                if(br.com.celk.util.CollectionUtils.isNotNullEmpty(lstSolicitacaoAgendamentoExames)) {
                                    throw new ValidacaoException(Bundle.getStringApplication("msg_existe_solicitacao_mesmo_procedimento", sa.getCodigo()));
                                }
                            }
                        }
                        continue;
                    }
                    throw new ValidacaoException(Bundle.getStringApplication("msg_existe_solicitacao_aberto", sa.getCodigo()));
                }
            }
        }
    }

    private void validaTipoProcedimento(SolicitacaoAgendamento solicitacaoAgendamento) throws ValidacaoException {
        try {
            if (solicitacaoAgendamento.getTipoProcedimento().getSexo() != null && !solicitacaoAgendamento.getTipoProcedimento().getSexo().equals(solicitacaoAgendamento.getUsuarioCadsus().getSexo())) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_sexo_paciente_X_nao_compativel_tipo_procedimento_X", solicitacaoAgendamento.getUsuarioCadsus().getNome(), solicitacaoAgendamento.getTipoProcedimento().getDescricao()));
            }
            if (solicitacaoAgendamento.getTipoProcedimento().getFaixaEtariaInicial() != null && solicitacaoAgendamento.getTipoProcedimento().getFaixaEtariaInicial() > Data.getMesDiferenca(loadDataNascimentoPaciente(solicitacaoAgendamento.getUsuarioCadsus()).getDataNascimento() , DataUtil.getDataAtual()) && !getSolicitacaoAgendamento().isStatusRegulacaoDevolvido()) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_faixa_etaria_inicial_paciente_X_nao_compativel_procedimento_X", solicitacaoAgendamento.getUsuarioCadsus().getNome(), solicitacaoAgendamento.getTipoProcedimento().getDescricao()));
            }
            if (solicitacaoAgendamento.getTipoProcedimento().getFaixaEtariaFinal() != null && solicitacaoAgendamento.getTipoProcedimento().getFaixaEtariaFinal() < Data.getMesDiferenca(loadDataNascimentoPaciente(solicitacaoAgendamento.getUsuarioCadsus()).getDataNascimento(), DataUtil.getDataAtual()) && !getSolicitacaoAgendamento().isStatusRegulacaoDevolvido()) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_faixa_etaria_final_paciente_X_nao_compativel_procedimento_X", solicitacaoAgendamento.getUsuarioCadsus().getNome(), solicitacaoAgendamento.getTipoProcedimento().getDescricao()));
            }
        } catch (ParseException e) {
            throw new ValidacaoException("Erro ao validar procedimento");
        }
    }
    private UsuarioCadsus loadDataNascimentoPaciente(UsuarioCadsus usuarioCadsus) {
        if (usuarioCadsus.getDataNascimento() != null){
            return usuarioCadsus;
        }
        return LoadManager.getInstance(UsuarioCadsus.class)
                .addProperty(UsuarioCadsus.PROP_DATA_NASCIMENTO)
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_CODIGO, usuarioCadsus.getCodigo()))
                .start().getVO();
    }

    public String getTipoControleRegulacao() {
        if(tipoControleRegulacao == null){
            try {
                tipoControleRegulacao = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("tipoControleRegulação");
            } catch (DAOException e) {
                 br.com.ksisolucoes.util.log.Loggable.log.error(e);
            }
        }
        return tipoControleRegulacao;
    }

    private boolean getHabilitaClassificacaoRiscoExame() {
        try {
            habilitaClassificacaoRiscoExame = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("habilitaClassificacaoRiscoExame");
            habilitaClassificacaoRiscoEncaminhamentoEspecialista = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("habilitaClassificaçãoRiscoEncaminhamentoEspecialista");
        } catch (DAOException e) {
            Loggable.log.error(e);
        }
        return RepositoryComponentDefault.SIM_LONG.equals(habilitaClassificacaoRiscoEncaminhamentoEspecialista) ||
                RepositoryComponentDefault.SIM.equals(habilitaClassificacaoRiscoExame);
    }
}
