package br.com.ksisolucoes.bo.agendamento.exame;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTO;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorCompetencia;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;

import java.util.ArrayList;
import java.util.List;

import static ch.lambdaj.Lambda.by;
import static ch.lambdaj.Lambda.on;

public class BuscarExamesPrestadoresCompetencia {

    public List<ExamePrestadorCompetencia> examePrestadorCompetencias(List<AgendaGradeAtendimentoDTO> atendimentos, TipoExame tipoExame) throws ValidacaoException, DAOException {
        List<ExamePrestadorCompetencia> competencias = new ArrayList<>();

        Group<AgendaGradeAtendimentoDTO> agendaGradeAtendimentoAgrupado = Lambda.group(atendimentos, by(on(AgendaGradeAtendimentoDTO.class).getAgenda().getEmpresa().getCodigo()));
        for (Group<AgendaGradeAtendimentoDTO> agendaGradeAtendimentoSubgrupo : agendaGradeAtendimentoAgrupado.subgroups()) {
            Empresa unidadeExecutante = agendaGradeAtendimentoSubgrupo.first().getAgenda().getEmpresa();
            ExamePrestadorCompetencia examePrestadorCompetencia = BOFactory.getBO(AgendamentoFacade.class).buscarExamePrestadorCompetencia(unidadeExecutante, tipoExame, DataUtil.getDataAtual());
            if (examePrestadorCompetencia != null) {
                competencias.add(examePrestadorCompetencia);
            }
        }
        return competencias;
    }
}
