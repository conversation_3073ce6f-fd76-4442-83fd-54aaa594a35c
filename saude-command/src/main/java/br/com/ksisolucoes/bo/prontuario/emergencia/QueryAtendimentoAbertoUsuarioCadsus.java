/*
 * QueryMediaTempoEspera.java
 *
 * Created on 23 de Agosto de 2006, 11:51
 *
 * To change this template, choose <PERSON><PERSON> | Template Manager
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.prontuario.emergencia;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
//import br.com.ksisolucoes.vo.prontuario.emergencia.ProntuarioEmergencia;

import java.util.List;

import org.hibernate.Query;

/**
 * Query a qual avalia de h algum atendimento aberto para o referido paciente.
 *
 * <AUTHOR>
 */
public class QueryAtendimentoAbertoUsuarioCadsus extends CommandQuery<QueryAtendimentoAbertoUsuarioCadsus> {
    
    private Long codigoUsuarioCadsus;
    private boolean atendimentoAberto;
    
    /**
     * Creates a new instance of QueryMediaTempoEspera
     */
    public QueryAtendimentoAbertoUsuarioCadsus( Long codigoUsuarioCadsus ) {
        this.codigoUsuarioCadsus = codigoUsuarioCadsus;
    }
    
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect( "count( pe.id.codigo )" );
        
//        hql.addToFrom( ProntuarioEmergencia.class.getName() + " pe " );
        
        hql.addToWhereWhithAnd( "pe.usuarioCadsus.codigo = :usuarioCadsus" );
        hql.addToWhereWhithAnd( "pe.status < :statusLimite" );
    }
    
    protected void setParameters(HQLHelper hql, Query query) {
        query.setParameter("usuarioCadsus", this.codigoUsuarioCadsus);
//        query.setParameter("statusLimite", ProntuarioEmergencia.STATUS_ATENDIMENTO_CONCLUIDO);
    }
    
    protected void result(Object result) {
        this.atendimentoAberto = ((List< Long >)result).get(0) > 0;
    }
    
    public boolean isAtendimentoAberto() {
        return atendimentoAberto;
    }
}