/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.agendamento.agendappivaga;

import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Projections;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaPpiVaga;

/**
 *
 * <AUTHOR>
 */
public class AdicionarAgendaPpiVaga extends AbstractCommandTransaction{

    private AgendaPpiVaga agendaPpiVaga;

    public AdicionarAgendaPpiVaga(AgendaPpiVaga agendaPpiVaga) {
        this.agendaPpiVaga = agendaPpiVaga;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        Long count = (Long) getSession().createCriteria(AgendaPpiVaga.class)
                .setProjection(Projections.rowCount())
//                .add(Restrictions.eq(AgendaPpiVaga.PROP_AGENDA_PROFISSIONAL_GRADE_ATENDIMENTO, agendaPpiVaga.getAgendaProfissionalGradeAtendimento()))
                .add(Restrictions.eq(AgendaPpiVaga.PROP_EMPRESA, agendaPpiVaga.getEmpresa()))
                .uniqueResult();
        if(count > 0){
            throw new ValidacaoException(Bundle.getStringApplication("msg_registro_existente"));
        }

        Long sum = (Long) getSession().createCriteria(AgendaPpiVaga.class)
                .setProjection(Projections.sum(AgendaPpiVaga.PROP_QUANTIDADE_VAGA_TOTAL))
//                .add(Restrictions.eq(AgendaPpiVaga.PROP_AGENDA_PROFISSIONAL_GRADE_ATENDIMENTO, agendaPpiVaga.getAgendaProfissionalGradeAtendimento()))
                .uniqueResult();
//        if(Coalesce.asLong(sum) + agendaPpiVaga.getQuantidadeVagaTotal() > agendaPpiVaga.getAgendaProfissionalGradeAtendimento().getQuantidadeAtendimento() ){
//            throw new ValidacaoException(Bundle.getStringApplication("msg_total_vagas_maior_quantidade_atendimento"));
//        }

        BOFactory.getBO(CadastroFacade.class).save(agendaPpiVaga);
    }
}
