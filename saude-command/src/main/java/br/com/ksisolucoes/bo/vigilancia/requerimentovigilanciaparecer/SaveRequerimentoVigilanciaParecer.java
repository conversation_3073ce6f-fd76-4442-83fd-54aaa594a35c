package br.com.ksisolucoes.bo.vigilancia.requerimentovigilanciaparecer;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaParecer;

/**
 * <AUTHOR>
 */
public class SaveRequerimentoVigilanciaParecer extends SaveVO<RequerimentoVigilanciaParecer> {

    public SaveRequerimentoVigilanciaParecer(RequerimentoVigilanciaParecer vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getUsuario() == null) {
            this.vo.setUsuario(getSessao().getUsuario());
        }
    }
}