/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.programasaude.testepezinho;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QuerySorter;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomSorter;
import br.com.ksisolucoes.bo.programasaude.programasaudeusuario.SaveProgramaSaudeUsuario;
import br.com.ksisolucoes.bo.prontuario.emergencia.prontuarioeventos.GerarProntuarioEvento;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.programasaude.ProgramaSaudeUsuario;
import br.com.ksisolucoes.vo.programasaude.ProgramaSaudeUsuario.TipoProgramaSaude;
import br.com.ksisolucoes.vo.programasaude.TestePezinho;
import br.com.ksisolucoes.vo.prontuario.ListaEsperaProgramaSaude;
import br.com.ksisolucoes.vo.prontuario.ListaEsperaProgramaSaudePK;
import br.com.ksisolucoes.vo.prontuario.emergencia.ProntuarioEventos;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ConcluirTestePezinho extends AbstractCommandTransaction{

    private TestePezinho testePezinho;

    public ConcluirTestePezinho(TestePezinho testePezinho) {
        this.testePezinho = testePezinho;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {

        new SaveTestePezinho(testePezinho).start();


        GerarProntuarioEvento gerarProntuarioEvento = new GerarProntuarioEvento(
                ProntuarioEventos.TIPO_ATENDIMENTO_PROGRAMA_SAUDE,
                testePezinho.getUsuarioCadsus(),
                Bundle.getStringApplication("rotulo_coleta_teste_pezinho"),
                null);
        gerarProntuarioEvento.start();

        List<ListaEsperaProgramaSaude> lepsList = LoadManager.getInstance(ListaEsperaProgramaSaude.class)
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(ListaEsperaProgramaSaude.PROP_TIPO_PROGRAMA_SAUDE), ListaEsperaProgramaSaude.TIPO_PROGRAMA_SAUDE_TESTE_PEZINHO))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(ListaEsperaProgramaSaude.PROP_NUMERO_ATENDIMENTO_PROGRAMA_SAUDE), testePezinho.getId().getCodigo()))
                .addSorter(new QueryCustomSorter(VOUtils.montarPath(ListaEsperaProgramaSaude.PROP_ID,ListaEsperaProgramaSaudePK.PROP_CODIGO),QuerySorter.DECRESCENTE))
                .start().getList();

        if(CollectionUtils.isNotNullEmpty(lepsList)){
            ProgramaSaudeUsuario programaSaudeUsuario = LoadManager.getInstance(ProgramaSaudeUsuario.class)
//                    .addParameter(new QueryCustomParameter(new HQLConvertKeyToProperties(ProgramaSaudeUsuario.PROP_LISTA_ESPERA_PROGRAMA_SAUDE, lepsList.get(0))))
                    .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProgramaSaudeUsuario.PROP_TIPO_PROGRAMA),TipoProgramaSaude.TESTE_PEZINHO.getValue()))
                    .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProgramaSaudeUsuario.PROP_USUARIO_CADSUS), testePezinho.getUsuarioCadsus()))
                    .start().getVO();

            if(programaSaudeUsuario == null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_programa_saude_usuario_nao_encontrado",testePezinho.getUsuarioCadsus().getDescricaoFormatado(),lepsList.get(0).getId().getCodigo()));
            }

            programaSaudeUsuario.setStatus(ProgramaSaudeUsuario.STATUS_CONCLUIDO);
            programaSaudeUsuario.setDataConclusao(Data.getDataAtual());

            new SaveProgramaSaudeUsuario(programaSaudeUsuario).start();
        }else{
            throw new ValidacaoException(Bundle.getStringApplication("msg_lista_espera_programa_saude_nao_encontrada",testePezinho.getId().getCodigo()));
        }
    }
}
