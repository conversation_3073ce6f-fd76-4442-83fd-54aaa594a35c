package br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.node;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.unidadesaude.CiapHelper;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.covid19.FichaCovidUtils;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.AtendimentoHelper;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.AbstractCommandValidacaoV2;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.annotations.ValidacaoProntuarioNode;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.DadosEnfermagemDTO;
import br.com.ksisolucoes.bo.prontuario.enfermagem.SalvarDadosEnfermagem;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper;
import br.com.ksisolucoes.vo.esus.CboFichaEsusItem;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import ch.lambdaj.Lambda;
import org.hamcrest.Matchers;

import java.util.Date;
import java.util.List;

import static ch.lambdaj.Lambda.having;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@ValidacaoProntuarioNode(value = NodesAtendimentoRef.DADOS_ENFERMAGEM, refClass = DadosEnfermagemDTO.class)
public class ValidarDadosEnfermagemNode extends AbstractCommandValidacaoV2<DadosEnfermagemDTO> {

    public ValidarDadosEnfermagemNode(Atendimento atendimento) {
        super(atendimento);
    }

    @Override
    public DadosEnfermagemDTO executarValidacao(DadosEnfermagemDTO object) throws DAOException, ValidacaoException {
        if(object != null) {

            if (FichaCovidUtils.isCondutaCovidInvalida(getAtendimento(), object.getCondutaCovid())) {
                validacao(Bundle.getStringApplication("msg_informe_conduta_covid"));
            }

//        if (object != null) {
//            if (object.getClassificacaoAtendimento() == null) {
//                validacao(Bundle.getStringApplication("msg_informe_tipo_atendimento"));
//            }
//        } else {
//            validacao(Bundle.getStringApplication("msg_informe_dados_consulta"));
//        }
            validarESUS(object);
//        if (CiapHelper.isCiapRequired(getAtendimento()) && getAtendimento().getCiap() == null && object != null && object.getCiap() == null) {
//            throw new ValidacaoException(Bundle.getStringApplication("informe_ciap"));
//        }
            try {
                if (RepositoryComponentDefault.SIM_LONG.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("obrigatorioInformarCIAP")) && object.getCiap() == null) {
                    throw new ValidacaoException("Por favor, informe o CIAP.");
                }
            } catch (DAOException e) {
                Loggable.log.error(e.getMessage());
            }
            validarCiapSexoPaciente(object.getCiap());

            if (object.getProfissionalAuxiliar() != null) {
                if (object.getProfissionalAuxiliar().equals(atendimento.getProfissional())) {
                    validacao(Bundle.getStringApplication("msg_profissional_aux_nao_pode_ser_mesmo_profissional_atendimento"));
                } else if (object.getCboProfissionalAuxiliar() == null) {
                    validacao(Bundle.getStringApplication("msg_informe_cbo_profissional_auxiliar"));
                }
            }
            PreNatal preNatal = UsuarioCadsusHelper.getPreNatal(getAtendimento().getUsuarioCadsus());
            if (preNatal != null) {
                if (preNatal.getDataUltimaMenstruacao() != null) {
                    object.setDumGestante(preNatal.getDataUltimaMenstruacao());
                }
            }
            AtendimentoHelper.validarCamposGestacao(atendimento, object.getDumGestante(), object.getIdadeGestacional(), this);
        }
        return object;
    }

    @Override
    public void processar(DadosEnfermagemDTO object) throws DAOException, ValidacaoException {
        if (object != null) {
            object.setCodigoAtendimento(getAtendimento().getCodigo());
            new SalvarDadosEnfermagem(object).start();
        }
    }

    private void validarESUS(DadosEnfermagemDTO object) throws ValidacaoException, DAOException {
        Long tipoClassificacao = getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getTipoClassificacao();
        if (TipoAtendimento.TipoClassificacao.ENFERMAGEM_MEDICA.value().equals(tipoClassificacao)) {
            if (TabelaCbo.NivelEnsino.SUPERIOR.value().equals(getAtendimento().getTabelaCbo().getNivelEnsino()) && !RepositoryComponentDefault.NAO_LONG.equals(object.getFlagConsulta())) {
                getSessao().putClientProperty("VALIDACAO_ESUS_CLASS_ATEND" + getAtendimento().getCodigo(), true);
                if (object == null || object.getConduta() == null) {
                    validacao(Bundle.getStringApplication("msg_faturamento_esus_necessario_preencher_conduta_evolucao"));
                }
                if (object == null || object.getClassificacaoAtendimento() == null) {
                    validacao(Bundle.getStringApplication("msg_faturamento_esus_necessario_preencher_classificacao_atendimento"));
                }
                else if (object == null || object.getTipoAtendimentoEsus() == null) {
                    validacao(Bundle.getStringApplication("msg_faturamento_esus_necessario_preencher_tipo_atendimento"));
                }
            }

            List<UsuarioCadsus> lstUsuarioCadsusValidacao = LoadManager.getInstance(UsuarioCadsus.class)
                    .addProperty(UsuarioCadsus.PROP_CODIGO)
                    .addProperty(UsuarioCadsus.PROP_DATA_NASCIMENTO)
                    .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_CODIGO, getAtendimento().getUsuarioCadsus()))
                    .setMaxResults(1).start().getList();


            Date dataNascimento = null;
            if (CollectionUtils.isNotNullEmpty(lstUsuarioCadsusValidacao)) {
                dataNascimento = lstUsuarioCadsusValidacao.get(0).getDataNascimento();
            }

            if (object != null && object.getDumGestante() != null && dataNascimento != null) {
                if (object.getDumGestante().before(dataNascimento)) {
                    validacao(Bundle.getStringApplication("msg_data_dum_menor_menstruacao"));
                }
            }

            if(object != null && object.getCiap() != null){
                List<Ciap> ciapList =  Lambda.extract(object.getCiapAtendimentoList(), Lambda.on(CiapAtendimento.class).getCiap());
                ciapList.add(object.getCiap());

                Ciap ciap = Lambda.selectFirst(ciapList,
                        having(on(Ciap.class), Matchers.notNullValue())
                                .and(having(on(Ciap.class).getReferencia(), Matchers.not(Matchers.isIn(CiapHelper.getOcorrenciasList().keySet().toArray())))));

                if (ciap == null) {
                    ciap = Lambda.selectFirst(ciapList, Lambda.having(on(Ciap.class), Matchers.notNullValue()));
                }
                atendimento.setCiap(ciap);
            }

            if (!CboFichaEsusItem.verificarCboPodeInformarCid10(getAtendimento().getProfissional().getCboProfissional(getAtendimento().getEmpresa()).getCbo())
                    && ClassificacaoAtendimento.ClassificacaoEsus.OUTROS.value().equals(object.getClassificacaoAtendimento().getClassificacaoEsus()) && atendimento.getCiap() == null){

                    Ciap ciap = LoadManager.getInstance(Ciap.class).addParameter(new QueryCustom.QueryCustomParameter(Ciap.PROP_REFERENCIA,"A29")).start().getVO();
                    atendimento.setCiap(ciap);

            }
        }
    }

    public void validarCiapSexoPaciente(Ciap ciap) {
        boolean ciapInvalido = CiapHelper.isInvalidoCiapSexoPaciente(ciap,getAtendimento());
        if(ciapInvalido){
            validacao(Bundle.getStringApplication("ciap_nao_valido_sexo_paciente"));
        }
    }
}
