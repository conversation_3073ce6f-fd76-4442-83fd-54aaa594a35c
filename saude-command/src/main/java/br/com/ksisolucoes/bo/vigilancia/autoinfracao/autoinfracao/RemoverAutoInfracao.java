package br.com.ksisolucoes.bo.vigilancia.autoinfracao.autoinfracao;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidade;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracaoProvidencia;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.FiscalAutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMulta;
import br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilancia;
import br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilanciaItem;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoOcorrencia;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RemoverAutoInfracao extends AbstractCommandTransaction {
    
    private AutoInfracao autoInfracao;
    
    public RemoverAutoInfracao(AutoInfracao autoInfracao) {
        this.autoInfracao = autoInfracao;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        List<AutoIntimacao> lstIntimacao = LoadManager.getInstance(AutoIntimacao.class)
                .addParameter(new QueryCustom.QueryCustomParameter(AutoIntimacao.PROP_AUTO_INFRACAO, autoInfracao))
                .start().getList();
        if (CollectionUtils.isNotNullEmpty(lstIntimacao)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_possivel_excluir_intimacao_existente", lstIntimacao.get(0).getNumeroFormatado()));
        }

        List<AutoMulta> lstMulta = LoadManager.getInstance(AutoMulta.class)
                .addParameter(new QueryCustom.QueryCustomParameter(AutoMulta.PROP_AUTO_INFRACAO, autoInfracao))
                .start().getList();
        if (CollectionUtils.isNotNullEmpty(lstMulta)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_possivel_excluir_multa_existente", lstMulta.get(0).getNumeroFormatado()));
        }

        List<AutoPenalidade> lstPenalidade = LoadManager.getInstance(AutoPenalidade.class)
                .addParameter(new QueryCustom.QueryCustomParameter(AutoPenalidade.PROP_AUTO_INFRACAO, autoInfracao))
                .start().getList();
        if (CollectionUtils.isNotNullEmpty(lstPenalidade)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_possivel_excluir_penalidade_existente", lstPenalidade.get(0).getNumeroFormatado()));
        }

        List<ProcessoAdministrativo> lstProcessoAdministrativo = LoadManager.getInstance(ProcessoAdministrativo.class)
                .addParameter(new QueryCustom.QueryCustomParameter(ProcessoAdministrativo.PROP_AUTO_INFRACAO, autoInfracao))
                .start().getList();
        if (CollectionUtils.isNotNullEmpty(lstProcessoAdministrativo)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_possivel_excluir_processo_administrativo_existente", lstProcessoAdministrativo.get(0).getNumeroProcessoFormatado()));
        }

        List<FiscalAutoInfracao> lstFiscal = LoadManager.getInstance(FiscalAutoInfracao.class)
                .addParameter(new QueryCustom.QueryCustomParameter(FiscalAutoInfracao.PROP_AUTO_INFRACAO, autoInfracao))
                .start().getList();
        List<AutoInfracaoProvidencia> lstProvidencias = LoadManager.getInstance(AutoInfracaoProvidencia.class)
                .addParameter(new QueryCustom.QueryCustomParameter(AutoInfracaoProvidencia.PROP_AUTO_INFRACAO, autoInfracao))
                .start().getList();

        for (FiscalAutoInfracao fiscal : lstFiscal) {
            BOFactory.delete(fiscal);
        }
        for(AutoInfracaoProvidencia providencia : lstProvidencias){
            BOFactory.delete(providencia);
        }

        VOUtils.removerListaVos(RequerimentoVigilanciaAnexo.class, new QueryCustom.QueryCustomParameter(RequerimentoVigilanciaAnexo.PROP_AUTO_INFRACAO, autoInfracao));

        VOUtils.removerListaVos(ProcessoAdministrativoOcorrencia.class, new QueryCustom.QueryCustomParameter(ProcessoAdministrativoOcorrencia.PROP_DOCUMENTO, autoInfracao.getCodigo()));

        if (autoInfracao.getNumero() != null) {
            VOUtils.removerListaVos(LancamentoAtividadesVigilanciaItem.class, new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LancamentoAtividadesVigilanciaItem.PROP_LANCAMENTO_ATIVIDADES_VIGILANCIA, LancamentoAtividadesVigilancia.PROP_NUMERO_AUTO_INFRACAO), autoInfracao.getNumero()));
            VOUtils.removerListaVos(LancamentoAtividadesVigilancia.class, new QueryCustom.QueryCustomParameter(LancamentoAtividadesVigilancia.PROP_NUMERO_AUTO_INFRACAO, autoInfracao.getNumero()));
        }

        getSession().flush();
        BOFactory.delete(autoInfracao);

    }
}
