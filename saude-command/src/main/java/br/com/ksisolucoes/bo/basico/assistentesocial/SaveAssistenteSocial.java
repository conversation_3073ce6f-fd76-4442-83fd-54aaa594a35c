package br.com.ksisolucoes.bo.basico.assistentesocial;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.AssistenteSocial;
import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class SaveAssistenteSocial extends SaveVO {

    private AssistenteSocial assistenteSocial;

    public SaveAssistenteSocial(Object vo) {
        super(vo);
        this.assistenteSocial = (AssistenteSocial) vo;
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (StringUtils.trimToNull(this.assistenteSocial.getNome())==null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_digite_nome"));
        }
    }

}
