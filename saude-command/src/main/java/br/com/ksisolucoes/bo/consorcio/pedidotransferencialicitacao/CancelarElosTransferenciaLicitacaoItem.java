package br.com.ksisolucoes.bo.consorcio.pedidotransferencialicitacao;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.EloPedidoLicitacaoItemPedidoTransferenciaLicitacaoItem;
import br.com.ksisolucoes.vo.consorcio.PedidoLicitacaoItem;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacaoItem;
import java.math.MathContext;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CancelarElosTransferenciaLicitacaoItem extends AbstractCommandTransaction {

    private PedidoTransferenciaLicitacaoItem pedidoTransferenciaLicitacaoItem;

    public CancelarElosTransferenciaLicitacaoItem(PedidoTransferenciaLicitacaoItem pedidoTransferenciaLicitacaoItem) {
        this.pedidoTransferenciaLicitacaoItem = pedidoTransferenciaLicitacaoItem;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        List<EloPedidoLicitacaoItemPedidoTransferenciaLicitacaoItem> elosExistentes = LoadManager.getInstance(EloPedidoLicitacaoItemPedidoTransferenciaLicitacaoItem.class)
                .addProperties(new HQLProperties(EloPedidoLicitacaoItemPedidoTransferenciaLicitacaoItem.class).getProperties())
                .addProperties(new HQLProperties(PedidoTransferenciaLicitacaoItem.class, EloPedidoLicitacaoItemPedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO_ITEM).getProperties())
                .addProperties(new HQLProperties(PedidoLicitacaoItem.class, EloPedidoLicitacaoItemPedidoTransferenciaLicitacaoItem.PROP_PEDIDO_LICITACAO_ITEM).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EloPedidoLicitacaoItemPedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO_ITEM), pedidoTransferenciaLicitacaoItem))
                .start().getList();

        for (EloPedidoLicitacaoItemPedidoTransferenciaLicitacaoItem elo : elosExistentes) {
            PedidoLicitacaoItem pedidoLicitacaoItem = elo.getPedidoLicitacaoItem();

            Long quantidadeAtualElo = 0L;
            if (Coalesce.asDouble(pedidoLicitacaoItem.getPrecoReal()) > 0D) {
                quantidadeAtualElo = new Dinheiro(elo.getValor(), MathContext.DECIMAL128).dividir(pedidoLicitacaoItem.getPrecoReal(), 4).round().longValue();
            }

            pedidoLicitacaoItem.setQuantidadeRecebida(Coalesce.asLong(pedidoLicitacaoItem.getQuantidadeRecebida()) - quantidadeAtualElo);
            pedidoLicitacaoItem.setValorRecebido(new Dinheiro(Coalesce.asDouble(pedidoLicitacaoItem.getValorRecebido())).subtrair(elo.getValor()).doubleValue());
            BOFactory.save(pedidoLicitacaoItem);
            BOFactory.delete(elo);
        }
    }
}
