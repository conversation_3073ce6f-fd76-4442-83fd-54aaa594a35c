package br.com.ksisolucoes.bo.integracao.bpa;

import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.EmpresaBpa;
import br.com.ksisolucoes.vo.basico.ParametroAtendimento;
import java.util.ArrayList;
import br.com.celk.util.Coalesce;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.prontuario.basico.EmpresaConvenioBpa;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class SalvarConfiguracaoBpa extends AbstractCommandTransaction {

    private ParametroAtendimento pa;
    private Map<EmpresaBpa, List<EmpresaConvenioBpa>> map = new HashMap();
    private ArrayList<EmpresaConvenioBpa> lstConvenio = new ArrayList<EmpresaConvenioBpa>();

    public SalvarConfiguracaoBpa(ParametroAtendimento pa, Map<EmpresaBpa, List<EmpresaConvenioBpa>> map) {
        this.pa = pa;
        this.map = map;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        pa.setBpaCnpjCpfOrigem(StringUtil.getDigits(Coalesce.asString(pa.getBpaCnpjCpfOrigem())));

        BOFactory.getBO(CadastroFacade.class).save(pa);

        List<EmpresaBpa> cadastrados = LoadManager.getInstance(EmpresaBpa.class)
                .start().getList();

        cadastrados.removeAll(map.keySet());
        for (EmpresaBpa empresaBpa : cadastrados) {
            BOFactory.delete(empresaBpa);
        }
        for (Map.Entry<EmpresaBpa, List<EmpresaConvenioBpa>> entry : map.entrySet()) {
            EmpresaBpa empresaBpa = entry.getKey();
            EmpresaBpa eb = (EmpresaBpa) getSession().get(EmpresaBpa.class, empresaBpa.getEmpresa().getCodigo());
            if (eb != null) {
                eb.setOrigemDados(empresaBpa.getOrigemDados());
                empresaBpa = eb;
            }
            empresaBpa.setGeraBpa("S");

            BOFactory.getBO(CadastroFacade.class).save(empresaBpa);

            for (EmpresaConvenioBpa empresaConvenioItem : entry.getValue()) {
                empresaConvenioItem.setEmpresa(empresaBpa.getEmpresa());
            }

            VOUtils.persistirListaVosModificados(EmpresaConvenioBpa.class, entry.getValue(), new QueryCustom.QueryCustomParameter(EmpresaConvenioBpa.PROP_EMPRESA, empresaBpa.getEmpresa()));
        }
    }

}
