package br.com.ksisolucoes.bo.entradas.estoque.controleinventario;

import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.ControleInventarioFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.ConfirmacaoInventarioProcesso;
import br.com.ksisolucoes.vo.entradas.estoque.Inventario;
import br.com.ksisolucoes.vo.service.AsyncProcess;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class EnviarConfirmacaoInventarioProcessoFila extends AbstractCommandTransaction {

    private final List<Empresa> empresas;
    private final boolean zerarProdutos;
    private final Date dataInventario;
    private final boolean zerarLotes;
    private final boolean considerarProdutosZerados;
    private final Inventario inventario;

    public EnviarConfirmacaoInventarioProcessoFila(List<Empresa> empresas, boolean zerarProdutos, Date dataInventario, boolean zerarLotes, Inventario inventario, boolean considerarProdutosZerados) {
        this.empresas = empresas;
        this.zerarProdutos = zerarProdutos;
        this.dataInventario = dataInventario;
        this.zerarLotes = zerarLotes;
        this.inventario = inventario;
        this.considerarProdutosZerados = considerarProdutosZerados;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        ConfirmacaoInventarioProcesso confirmacaoInventarioProcesso = new ConfirmacaoInventarioProcesso();

        confirmacaoInventarioProcesso.setDataGeracao(dataInventario);

        confirmacaoInventarioProcesso.setInventario(inventario);

        AsyncProcess processo = BOFactory.getBO(ControleInventarioFacade.class).processar(empresas, zerarProdutos, dataInventario, zerarLotes, inventario, considerarProdutosZerados);

        confirmacaoInventarioProcesso.setAsyncProcess(processo);

        BOFactory.save(confirmacaoInventarioProcesso);
    }

}
