/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.formulario;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.formulario.dto.ConsultaFormularioValorDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.formulario.FormularioItem;
import java.util.List;
import java.util.Map;
import org.hibernate.Query;

/**
 *
 * <AUTHOR>
 */
public class QueryPagerConsultaFormularioValor extends CommandQueryPager<QueryPagerConsultaFormularioValor> {

    private ConsultaFormularioValorDTOParam param;
    private PropertiesQueryConsultaFormularioValor propertiesQuery;

    public QueryPagerConsultaFormularioValor(ConsultaFormularioValorDTOParam param) {
        this.param = param;
        this.propertiesQuery = new PropertiesQueryConsultaFormularioValor(param);
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        propertiesQuery.createHqlQuery(hql);
    }

    @Override
    protected int getCountValue(Query query) {
        int sizeEstrutura = 0;
        for (FormularioItem formularioItem : this.param.getDTOFormularioEstrutura().getFormularioItems()) {
            if (!FormularioItem.VALUE_TIPO_CAMPO_GRUPO.equals(formularioItem.getTipoCampo())) {
                sizeEstrutura++;
            }
        }
        int count_ = super.getCountValue(query);
        return count_ / sizeEstrutura;
    }

    @Override
    protected HQLHelper customHQLHelperBeforeExecute(HQLHelper hql) {
        this.setMaxResults(getDataPaging().getMaxResult() * this.param.getDTOFormularioEstrutura().getFormularioItems().size());
        return super.customHQLHelperBeforeExecute(hql);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result);
        try{
            list = propertiesQuery.transformResult(list);
        }catch(Exception ex){
            throw new RuntimeException(ex);
        }
    }

}
