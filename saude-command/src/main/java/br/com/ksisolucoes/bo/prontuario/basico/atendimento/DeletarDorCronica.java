package br.com.ksisolucoes.bo.prontuario.basico.atendimento;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoProntuario;
import br.com.ksisolucoes.vo.prontuario.basico.DorCronica;
import br.com.ksisolucoes.vo.prontuario.basico.FearAvoidanceBeliefs;
import br.com.ksisolucoes.vo.prontuario.basico.IndiceOswestry;
import br.com.ksisolucoes.vo.prontuario.basico.StartBackScreeningTool;
import org.hibernate.criterion.Restrictions;

/**
 * <AUTHOR>
 */
public class DeletarDorCronica extends AbstractCommandTransaction {

    private DorCronica dorCronica;

    public DeletarDorCronica(DorCronica dorCronica) {
        this.dorCronica = dorCronica;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        DorCronica dorCronica = (DorCronica) getSession().get(DorCronica.class, this.dorCronica.getCodigo());
        BOFactory.delete(dorCronica);

        if (this.dorCronica.getFearAvoidanceBeliefs() != null) {
            FearAvoidanceBeliefs fearAvoidanceBeliefs = (FearAvoidanceBeliefs) getSession().createCriteria(FearAvoidanceBeliefs.class).add(Restrictions.eq(FearAvoidanceBeliefs.PROP_CODIGO, this.dorCronica.getFearAvoidanceBeliefs().getCodigo())).uniqueResult();
            BOFactory.delete(fearAvoidanceBeliefs);
        }
        if (this.dorCronica.getIndiceOswestry() != null) {
            IndiceOswestry oswestry = (IndiceOswestry) getSession().get(IndiceOswestry.class, this.dorCronica.getIndiceOswestry().getCodigo());
            BOFactory.delete(oswestry);
        }
        if (this.dorCronica.getStartBackScreeningTool() != null) {
            StartBackScreeningTool startBackScreeningTool = (StartBackScreeningTool) getSession().get(StartBackScreeningTool.class, this.dorCronica.getStartBackScreeningTool().getCodigo());
            BOFactory.delete(startBackScreeningTool);
        }
        AtendimentoProntuario atendimentoProntuario = LoadManager.getInstance(AtendimentoProntuario.class)
                .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoProntuario.PROP_DOR_CRONICA, dorCronica))
                .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoProntuario.PROP_ATENDIMENTO, dorCronica.getAtendimento().getCodigo())).start().getVO();

        if (atendimentoProntuario != null){
            BOFactory.getBO(AtendimentoFacade.class).removerAtendimentoProntuario(AtendimentoProntuario.TipoRegistro.DOR_CRONICA.value(), dorCronica.getAtendimento(), dorCronica.getCodigo());
        }
    }
}
