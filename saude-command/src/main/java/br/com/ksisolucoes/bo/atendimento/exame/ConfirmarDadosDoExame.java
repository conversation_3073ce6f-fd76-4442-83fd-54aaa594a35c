package br.com.ksisolucoes.bo.atendimento.exame;

import br.com.celk.util.DataUtil;
import br.com.celk.view.unidadesaude.exames.resultadoexame.InformarCidDTO;
import br.com.celk.view.unidadesaude.exames.resultadoexame.ResultadoCidExameDTO;
import br.com.ksisolucoes.bo.dto.AnexosPacienteDTO;
import br.com.ksisolucoes.bo.dto.MensagemAnexoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.*;
import br.com.ksisolucoes.vo.prontuario.basico.AnexoPaciente;
import br.com.ksisolucoes.vo.prontuario.exame.ResultadoCidExame;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import org.hibernate.criterion.Restrictions;
import org.jrimum.utilix.Objects;

import static br.com.celk.faturamento.FechamentoContaHelper.ajustarItemContaPaciente;

public class ConfirmarDadosDoExame extends AbstractCommandTransaction {

    private final InformarCidDTO informarCidDTO;

    public ConfirmarDadosDoExame(InformarCidDTO informarCidDTO) {
        this.informarCidDTO = informarCidDTO;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        salvarAnexoLaudo();

        ContaPaciente contaPaciente = (ContaPaciente) getSession().createCriteria(ContaPaciente.class)
                .add(Restrictions.eq(VOUtils.montarPath(ContaPaciente.PROP_CODIGO), informarCidDTO.getContaPaciente().getCodigo()))
                .uniqueResult();

        if (Objects.isNull(contaPaciente))
            return;

        contaPaciente.setStatus(ContaPaciente.Status.FECHADA.value());
        BOFactory.save(contaPaciente);

        for (ItemContaPaciente itemContaPaciente : informarCidDTO.getListaItemContaPaciente()) {
            itemContaPaciente.setContaPaciente(contaPaciente);

            itemContaPaciente.setCid(informarCidDTO.getCid());
            Profissional profissionalLaudo = informarCidDTO.getProfissionalResponsavel();
            itemContaPaciente.setProfissional(profissionalLaudo);
            itemContaPaciente.setCbo(null);

            ajustarItemContaPaciente(itemContaPaciente);

            BOFactory.save(itemContaPaciente);
        }

        salvarResultadoCidExame();
    }

    private void salvarResultadoCidExame() throws ValidacaoException, DAOException {
        ResultadoCidExameDTO resultadoCidExameDTO = new ResultadoCidExameDTO();

        resultadoCidExameDTO.setDataLaudo(informarCidDTO.getResultadoCidExame().getDataLaudo());
        resultadoCidExameDTO.setStatus(ResultadoCidExame.Status.CONCLUIDO.value());
        resultadoCidExameDTO.setProfissionalResponsavel(informarCidDTO.getProfissionalResponsavel());
        resultadoCidExameDTO.setCid(informarCidDTO.getCid());
        resultadoCidExameDTO.setAnexoPaciente(informarCidDTO.getAnexoPaciente());
        resultadoCidExameDTO.setExame(informarCidDTO.getExame());
        resultadoCidExameDTO.setAtendimentoExame(informarCidDTO.getResultadoCidExame().getAtendimentoExame());
        resultadoCidExameDTO.setPaciente(informarCidDTO.getPaciente());
        resultadoCidExameDTO.setContaPaciente(informarCidDTO.getContaPaciente());

        BOFactory.getBO(AtendimentoFacade.class).salvarResultadoCidExame(resultadoCidExameDTO);
    }

    private void salvarAnexoLaudo() throws ValidacaoException, DAOException {
        MensagemAnexoDTO dto = informarCidDTO.getMensagemAnexoDTO();
        if (dto != null && dto.getNomeArquivoUpload() != null && !dto.getNomeArquivoUpload().isEmpty()) {
            AnexoPaciente anexoPaciente = new AnexoPaciente();
            anexoPaciente.setDataDocumento(DataUtil.getDataAtual());
            anexoPaciente.setTipoAnexo(informarCidDTO.getTipoAnexo());
            anexoPaciente.setUsuarioCadsus(informarCidDTO.getPaciente());

            AnexosPacienteDTO objPaciente = new AnexosPacienteDTO();
            objPaciente.setAnexoPaciente(anexoPaciente);
            objPaciente.setTipoAnexo(informarCidDTO.getTipoAnexo());
            objPaciente.addAnexo(informarCidDTO.getMensagemAnexoDTO());

            anexoPaciente = BOFactory.getBO(AtendimentoFacade.class).salvarAnexosPaciente(objPaciente);
            informarCidDTO.setAnexoPaciente(anexoPaciente);
        }
    }
}
