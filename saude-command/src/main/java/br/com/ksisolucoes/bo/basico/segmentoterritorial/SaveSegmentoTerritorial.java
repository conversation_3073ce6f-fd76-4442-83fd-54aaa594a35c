/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.basico.segmentoterritorial;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.SegmentoTerritorial;

/**
 *
 * <AUTHOR>
 */
public class SaveSegmentoTerritorial extends SaveVO {

    private SegmentoTerritorial segmentoTerritorial;

    public SaveSegmentoTerritorial(Object vo) {
        super(vo);
        this.segmentoTerritorial = (SegmentoTerritorial) vo;
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        this.segmentoTerritorial.setDataAtualizacao(Data.getDataAtual());
        
//        if (Coalesce.asLong(this.segmentoTerritorial.getId().getCodigo()) > 0
//                && Coalesce.asLong(this.segmentoTerritorial.getId().getCidade().getCodigo()) > 0) {
//             
//            Criteria criteria = this.getSession().createCriteria(SegmentoTerritorial.class)
//                    .add(Restrictions.eq(VOUtils.montarPath(SegmentoTerritorial.PROP_ID, SegmentoTerritorialPK.PROP_CODIGO), this.segmentoTerritorial.getId().getCodigo()))
//                    .add(Restrictions.eq(VOUtils.montarPath(SegmentoTerritorial.PROP_ID, SegmentoTerritorialPK.PROP_CIDADE), this.segmentoTerritorial.getId().getCidade()));
//
//            Long count = (Long) criteria.setProjection(Projections.rowCount()).uniqueResult();
//            if (count > 0) {
//                throw new ValidacaoException(Bundle.getStringApplication("msg_ja_existe_uma_segmentacao_territorial_cadastrada_para_esta_cidade_com_o_mesmo_codigo"));
//            }
//        }
    }

}
