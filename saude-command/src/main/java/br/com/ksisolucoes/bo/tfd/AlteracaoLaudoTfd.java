package br.com.ksisolucoes.bo.tfd;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.tfd.interfaces.facade.TfdFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.factory.exception.FactoryException;
import br.com.ksisolucoes.tfd.dto.RegistroTfdManualDTO;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfdCopia;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.ProcedimentoSolicitadoTfd;
import br.com.ksisolucoes.vo.cadsus.TipoOcorrencia;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import org.hibernate.Criteria;
import org.hibernate.HibernateException;
import org.hibernate.criterion.Restrictions;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AlteracaoLaudoTfd extends AbstractCommandTransaction {

    private RegistroTfdManualDTO registroTfdManualDTO;

    public AlteracaoLaudoTfd(RegistroTfdManualDTO registroTfdManualDTO) {
        this.registroTfdManualDTO = registroTfdManualDTO;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        boolean obrigatorioTodosCamposLaudo = RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("obrigatorioTodosCamposLaudo"));
        if (obrigatorioTodosCamposLaudo) {
            if (CollectionUtils.isAllEmpty(registroTfdManualDTO.getProcedimentoSolicitadoTfdList())) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_obrigatorio_informar_pelo_menos_um_procedimento_tratamento_solicitado"));
            }
        }
        createLaudoTfdCopia();

        getSession().flush();
        getSession().clear();

        LaudoTfd laudoTfd = BOFactory.getBO(CadastroFacade.class).save(registroTfdManualDTO.getLaudoTfd());

        if(!LaudoTfd.StatusLaudoTfd.INCONCLUSIVO.value().equals(registroTfdManualDTO.getLaudoTfd().getStatus())){
            alterarSolicitacaoAgendamento(laudoTfd);
        }

        salvarProcedimentoSolicitadoTfdList();

        BOFactory.getBO(UsuarioCadsusFacade.class).gerarOcorrenciaUsuarioCadsus(registroTfdManualDTO.getLaudoTfd().getUsuarioCadsus(), TipoOcorrencia.TIPO_TFD, Bundle.getStringApplication("msg_realizado_alteracao_laudo"), registroTfdManualDTO.getLaudoTfd());
    }

    private void createLaudoTfdCopia() throws HibernateException, FactoryException, DAOException, ValidacaoException {
        LaudoTfd laudoTfd = (LaudoTfd) getSession().get(LaudoTfd.class, registroTfdManualDTO.getLaudoTfd().getCodigo());

        LaudoTfdCopia laudoTfdCopia = new LaudoTfdCopia();
        laudoTfdCopia.setLaudoTfd(laudoTfd);
        laudoTfdCopia.setCaraterAtendimento(laudoTfd.getCaraterAtendimento());
        laudoTfdCopia.setCid(laudoTfd.getCid());
        laudoTfdCopia.setDataLaudo(laudoTfd.getDataLaudo());
        laudoTfdCopia.setDiagnosticoProvavel(laudoTfd.getDiagnosticoProvavel());
        laudoTfdCopia.setExameComplementarRealizado(laudoTfd.getExameComplementarRealizado());
        laudoTfdCopia.setExameFisico(laudoTfd.getExameFisico());
        laudoTfdCopia.setFlagUrgente(laudoTfd.getFlagUrgente());
        laudoTfdCopia.setHistoricoDoenca(laudoTfd.getHistoricoDoenca());
        laudoTfdCopia.setJustificativaAcompanhante(laudoTfd.getJustificativaAcompanhante());
        laudoTfdCopia.setJustificativaTfd(laudoTfd.getJustificativaTfd());
        laudoTfdCopia.setJustificativaTransporte(laudoTfd.getJustificativaTransporte());
        laudoTfdCopia.setObservacaoUrgente(laudoTfd.getObservacaoUrgente());
        laudoTfdCopia.setProcedimento(laudoTfd.getProcedimento());
        laudoTfdCopia.setNomeProfissional(laudoTfd.getNomeProfissional());

        Criteria crit = getSession().createCriteria(ProcedimentoSolicitadoTfd.class);
        crit.add(Restrictions.eq(ProcedimentoSolicitadoTfd.PROP_LAUDO_TFD, laudoTfd));
        List<ProcedimentoSolicitadoTfd> lista = crit.list();
        String descricaoProcedimentoTratamentoSolicitado = "";
        for (ProcedimentoSolicitadoTfd procedimentoSolicitadoTfd : lista) {
            descricaoProcedimentoTratamentoSolicitado += procedimentoSolicitadoTfd.getProcedimento().getCodigoFormatado() + " " + procedimentoSolicitadoTfd.getProcedimento().getDescricao();
            if (procedimentoSolicitadoTfd.getObservacao() != null) {
                descricaoProcedimentoTratamentoSolicitado += " - " + procedimentoSolicitadoTfd.getObservacao();
            }
            descricaoProcedimentoTratamentoSolicitado += "\n";
        }
        laudoTfdCopia.setProcedimentoTratamentoSolicitado(descricaoProcedimentoTratamentoSolicitado);

        laudoTfdCopia.setProfissional(laudoTfd.getProfissional());
        laudoTfdCopia.setTipoProcedimento(laudoTfd.getTipoProcedimento());
        laudoTfdCopia.setTransporteRecomendavel(laudoTfd.getTransporteRecomendavel());
        laudoTfdCopia.setTratamentoRealizado(laudoTfd.getTratamentoRealizado());

        laudoTfdCopia.setDataCopia(Data.getDataAtual());
        laudoTfdCopia.setUsuario((Usuario) getSessao().getUsuario());

        BOFactory.getBO(CadastroFacade.class).save(laudoTfdCopia);
    }

    //Rotina replicada no RegistroTfdManual, se necessário alterar, avaliar a possibilidade de centralizar esta rotina.
    private void salvarProcedimentoSolicitadoTfdList() throws DAOException, ValidacaoException {
        BOFactory.getBO(TfdFacade.class).salvarProcedimentoSolicitadoTfdList(registroTfdManualDTO.getProcedimentoSolicitadoTfdList(), registroTfdManualDTO.getLaudoTfd());
    }

    public LaudoTfd getLaudoTfd() {
        return registroTfdManualDTO.getLaudoTfd();
    }

    private void alterarSolicitacaoAgendamento(LaudoTfd laudoTfd) throws DAOException, ValidacaoException {
        PedidoTfd pedidoTfd = (PedidoTfd) getSession().createCriteria(PedidoTfd.class)
                .add(Restrictions.eq(VOUtils.montarPath(PedidoTfd.PROP_CODIGO), laudoTfd.getPedidoTfd().getCodigo()))
                .uniqueResult();
        SolicitacaoAgendamento solicitacaoAgendamento = (SolicitacaoAgendamento) getSession().createCriteria(SolicitacaoAgendamento.class)
                .add(Restrictions.eq(SolicitacaoAgendamento.PROP_CODIGO, pedidoTfd.getSolicitacaoAgendamento().getCodigo()))
                .uniqueResult();
        solicitacaoAgendamento.setTipoProcedimento(laudoTfd.getTipoProcedimento());
        solicitacaoAgendamento.setProcedimento(laudoTfd.getProcedimento());
        BOFactory.save(solicitacaoAgendamento);
    }
}
