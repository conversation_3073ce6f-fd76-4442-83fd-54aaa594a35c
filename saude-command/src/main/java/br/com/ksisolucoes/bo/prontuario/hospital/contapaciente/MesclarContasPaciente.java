package br.com.ksisolucoes.bo.prontuario.hospital.contapaciente;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.MesclarContaPacienteDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoAlta;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import static ch.lambdaj.Lambda.selectMax;
import static ch.lambdaj.Lambda.on;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class MesclarContasPaciente extends AbstractCommandTransaction {

    private MesclarContaPacienteDTO dto;

    public MesclarContasPaciente(MesclarContaPacienteDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (dto.getLstContaPaciente().isEmpty()) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_selecione_conta_para_mesclar"));
        }

        ContaPaciente contaPacientePrincipal = (ContaPaciente) getSession().get(ContaPaciente.class, dto.getContaPacientePrincipal().getCodigo());

        Convenio convenioSUS = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("convenioSUS");
        if (convenioSUS.equals(contaPacientePrincipal.getConvenio())) {
            List<Aih> lstAihConta = LoadManager.getInstance(Aih.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(Aih.PROP_CONTA_PACIENTE, QueryCustom.QueryCustomParameter.IN, dto.getLstContaPaciente()))
                    .addParameter(new QueryCustom.QueryCustomParameter(Aih.PROP_STATUS, QueryCustom.QueryCustomParameter.DIFERENTE, Aih.Status.CANCELADA.value()))
                    .start().getList();

            if (!lstAihConta.isEmpty()) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_conta_X_nao_pode_ser_incorporada", lstAihConta.get(0).getContaPaciente().getCodigo()));
            }
        }

        alterContaPrincipal(dto.getLstContaPaciente(), contaPacientePrincipal);

        AtendimentoAlta atendimentoAlta = ((ContaPaciente) selectMax(dto.getLstContaPaciente(), on(ContaPaciente.class).getAtendimentoAlta().getDataAlta())).getAtendimentoAlta();
        if (contaPacientePrincipal.getAtendimentoAlta() == null ||
                (atendimentoAlta != null
                    && !atendimentoAlta.equals(contaPacientePrincipal.getAtendimentoAlta())
                    && atendimentoAlta.getDataAlta().after(contaPacientePrincipal.getAtendimentoAlta().getDataAlta()))) {
            contaPacientePrincipal.setAtendimentoAlta(atendimentoAlta);
            BOFactory.save(contaPacientePrincipal);
        }
    }

    private void alterContaPrincipal(List<ContaPaciente> lstContas, ContaPaciente contaPacientePrincipal) throws DAOException, ValidacaoException {
        for (ContaPaciente contaPaciente : lstContas) {
            if (contaPaciente.getAtendimentoInformacao().getDataChegada()
                    .before(contaPacientePrincipal.getAtendimentoInformacao().getDataChegada())) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_data_chegada_conta_X_menor_data_chegada_conta_principal", contaPaciente.getCodigo()));
            }

            contaPaciente.setContaPacientePrincipal(contaPacientePrincipal);
//            alterContaPrincipalContasIncorporadas(contaPaciente, contaPacientePrincipal);
            BOFactory.save(contaPaciente);
        }
    }

//    private void alterContaPrincipalContasIncorporadas(ContaPaciente contaPacientePrincipalAtual, ContaPaciente contaPacientePrincipalNova) throws DAOException, ValidacaoException {
//        List<ContaPaciente> lstContaIncorporadas = LoadManager.getInstance(ContaPaciente.class)
//                .addParameter(new QueryCustom.QueryCustomParameter(ContaPaciente.PROP_CONTA_PACIENTE_PRINCIPAL, contaPacientePrincipalAtual))
//                .addParameter(new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryRelationKeys(ContaPaciente.PROP_CODIGO, VOUtils.montarPath(ContaPaciente.PROP_CONTA_PACIENTE_PRINCIPAL, ContaPaciente.PROP_CODIGO), QueryCustom.QueryCustomParameter.DIFERENTE)))
//                .start().getList();
//
//        if (!lstContaIncorporadas.isEmpty()) {
//            alterContaPrincipal(lstContaIncorporadas, contaPacientePrincipalNova);
//        }
//    }
}
