package br.com.ksisolucoes.bo.vigilancia.estabelecimento;

import br.com.celk.bo.service.rest.estabelecimento.EstabelecimentoRestDTO;
import br.com.celk.util.*;
import br.com.ksisolucoes.bo.basico.dto.CepWSDTO;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoUtil;
import br.com.ksisolucoes.util.validacao.CpfCnpJValidator;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.commons.lang.SerializationUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

public class CreateUpdateEstabelecimentoRest extends AbstractCommandTransaction<CreateUpdateEstabelecimentoRest> implements Serializable {

    private EstabelecimentoRestDTO dto;
    private String ipClient;
    private Estabelecimento estabelecimento;
    private Estabelecimento estabelecimentoBefore;

    public CreateUpdateEstabelecimentoRest(EstabelecimentoRestDTO dto, String ipClient) {
        this.dto = dto;
        this.ipClient = ipClient;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        validar();
        SessaoUtil.createApplicationSession(ipClient);

        String cpfCnpjDigits = StringUtils.trimToNull(StringUtil.getDigits(dto.getCpfCnpj()));
        estabelecimento = VigilanciaHelper.loadEstabelecimentoByCpfCnpj(cpfCnpjDigits);
        if (estabelecimento == null) {
            estabelecimento = new Estabelecimento();
        } else {
            estabelecimentoBefore = (Estabelecimento) SerializationUtils.clone(estabelecimento);
        }

        estabelecimento.setTipoPorte(dto.getTipoPorte());
        estabelecimento.setDataCadastro(Coalesce.asDate(dto.getDataCadastro(), DataUtil.getDataAtual()));
        estabelecimento.setRazaoSocial(dto.getRazaoSocial());
        estabelecimento.setFantasia(dto.getNomeFantasia());
        estabelecimento.setFlagMicroEmpresa(dto.getFlagMei());
        estabelecimento.setSituacao(dto.getSituacao());
        estabelecimento.setTipoPessoa(dto.getTipoPessoa());
        estabelecimento.setCnpjCpf(dto.getCpfCnpj());
        estabelecimento.setDataUsuario(Coalesce.asDate(dto.getDataUsuario(), DataUtil.getDataAtual()));

        estabelecimento.setInscricaoMunicipal(dto.getIsncricaoMunicipal());
        estabelecimento.setAreaAlvara(dto.getAreaAlvara());

        estabelecimento.setVigilanciaEndereco(getVigilanciaEndereco());
        estabelecimento.setComplemento(dto.getComplemento());
        estabelecimento.setNumeroLogradouro(dto.getNumeroLogradouro());

        estabelecimento.setMatriz(RepositoryComponentDefault.NAO_LONG);
        estabelecimento.setCertificacaoAnvisa(RepositoryComponentDefault.NAO_LONG);
        estabelecimento.setOutraCertificacao(RepositoryComponentDefault.NAO_LONG);
        estabelecimento.setImportador(RepositoryComponentDefault.NAO_LONG);

        estabelecimento.setRepresentanteNome(dto.getNomeRepresentanteLegal());

        estabelecimento = BOFactory.save(estabelecimento);

        StringBuilder sb = new StringBuilder("Alteração ocorrida no Estabelecimento via Integração: " + System.lineSeparator());

        String ocorrenciaAlteracao = OcorrenciasUtil.getDiffCamposEstabelecimento(estabelecimentoBefore, estabelecimento, sb);
        BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaEstabelecimento(ocorrenciaAlteracao, estabelecimento);
    }

    private void validar() throws ValidacaoException {
        if (!EstabelecimentoRestUtil.habilitaIntegracaoEstabelecimentoRest()) {
            throw new ValidacaoException("Integração não está habilitada, verifique o parâmetro GEM (habilitaIntegracaoEstabelecimentoRest), com a Diretoria de Vigilância Sanitária!");
        }

        if (StringUtils.isEmpty(dto.getCep())) {
            throw new ValidacaoException("CEP não foi informado!");
        }

        if (dto.getTipoPorte() == null) {
            throw new ValidacaoException("Tipo de Porte está vazio!");
        }

        if (StringUtils.isEmpty(dto.getRazaoSocial())) {
            throw new ValidacaoException("Razão Social está vazio!");
        }

        if (StringUtils.isEmpty(dto.getNomeFantasia())) {
            throw new ValidacaoException("Nome Fantasia está vazio!");
        }

        if (dto.getSituacao() == null) {
            throw new ValidacaoException("Situação está vazia!");
        }

        if (dto.getTipoPessoa() == null) {
            throw new ValidacaoException("Tipo Pessoa está vazio!");
        }

        if (StringUtils.isEmpty(dto.getCpfCnpj())) {
            throw new ValidacaoException("CPF/CNPJ está vazio!");
        }

        if (StringUtils.isEmpty(dto.getNumeroLogradouro())) {
            throw new ValidacaoException("Número do Logradouro está vazio!");
        } else {
            if (dto.getNumeroLogradouro().length() > 6) {
                throw new ValidacaoException("Número do Logradouro não pode ser maior que 6 caracteres!");
            }
        }

        String cpfCnpjDigits = StringUtils.trimToNull(StringUtil.getDigits(dto.getCpfCnpj()));

        if (Util.isCpf(cpfCnpjDigits) && !CpfCnpJValidator.CPFIsValid(cpfCnpjDigits)) {
            throw new ValidacaoException("CPF Inválido! " + cpfCnpjDigits);
        } else if (Util.isCnpj(cpfCnpjDigits) && !CpfCnpJValidator.CNPJIsValid(cpfCnpjDigits)) {
            throw new ValidacaoException("CNPJ Inválido! " + cpfCnpjDigits);
        }

        if (Estabelecimento.TipoPessoa.JURIDICA.value().equals(dto.getTipoPessoa()) && Util.isCpf(cpfCnpjDigits)) {
            throw new ValidacaoException("Você tentou cadastrar uma pessoa JURÍDICA com o CPF: " + cpfCnpjDigits);
        } else if (Estabelecimento.TipoPessoa.FISICA.value().equals(dto.getTipoPessoa()) && Util.isCnpj(cpfCnpjDigits)) {
            throw new ValidacaoException("Você tentou cadastrar uma pessoa FÍSICA com o CNPJ: " + cpfCnpjDigits);
        }

        if (VigilanciaHelper.existsEstabelecimentoByCpfCnpj(cpfCnpjDigits)) {
            throw new ValidacaoException("Já existe um estabelecimento cadastrado com o CPF/CNPJ: " + cpfCnpjDigits);
        }
    }


    public VigilanciaEndereco getVigilanciaEndereco() throws DAOException, ValidacaoException {
        VigilanciaEndereco ve = null;
        if (StringUtils.isNotEmpty(dto.getCep())) {
            ve = VigilanciaHelper.carregarVigilanciaEnderecoByCEP(dto.getCep());

            if (ve == null) {
                ve = new VigilanciaEndereco();
                CepWSDTO cepWSDTO = BOFactory.getBO(BasicoFacade.class).consultaCepWS(dto.getCep());

                if (cepWSDTO != null) {
                    ve.setBairro(Coalesce.asString(cepWSDTO.getBairro(), dto.getNomeBairro()));
                    ve.setCep(Coalesce.asString(cepWSDTO.getCep(), dto.getCep()));
                    ve.setLogradouro(Coalesce.asString(cepWSDTO.getLogradouro(), dto.getNomeLogradouro()));
                    ve.setNumeroLogradouro(dto.getNumeroLogradouro());
                    ve.setCidade(cepWSDTO.getCidade());
                    ve.setTipoLogradouro(cepWSDTO.getTipoLogradouro());
                    ve.setDataCadastro(DataUtil.getDataAtual());

                    ve = BOFactory.save(ve);
                    getSession().flush();
                    getSession().clear();
                }
            }
        }

        if (ve == null && StringUtils.isNotEmpty(dto.getNomeLogradouro())) {
            ve = new VigilanciaEndereco();
            ve.setBairro(dto.getNomeBairro());
            ve.setLogradouro(dto.getNomeLogradouro());
            ve.setNumeroLogradouro(dto.getNumeroLogradouro());
            ve.setDataCadastro(DataUtil.getDataAtual());

            ve = BOFactory.save(ve);
            getSession().flush();
            getSession().clear();
        }

        if (ve == null) {
            throw new ValidacaoException("Não foi possível encontrar um endereço válido com os dados informados, por favor verifique!");
        }

        return ve;
    }

    public Estabelecimento getEstabelecimento() {
        return estabelecimento;
    }
}
