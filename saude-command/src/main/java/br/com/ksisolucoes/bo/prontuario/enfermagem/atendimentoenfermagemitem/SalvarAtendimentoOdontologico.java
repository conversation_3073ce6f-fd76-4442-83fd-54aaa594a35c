/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.prontuario.enfermagem.atendimentoenfermagemitem;

import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.DenteAtendimentoItemDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoItem;
import br.com.ksisolucoes.vo.prontuario.basico.Dente;
import br.com.ksisolucoes.vo.prontuario.basico.EloDenteAtendimentoItem;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class SalvarAtendimentoOdontologico extends AbstractCommandTransaction{

    private Map<String,List<DenteAtendimentoItemDTO>> atendimentosMap;

    public SalvarAtendimentoOdontologico(Map<String, List<DenteAtendimentoItemDTO>> atendimentosMap) {
        this.atendimentosMap = atendimentosMap;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        for (Map.Entry<String, List<DenteAtendimentoItemDTO>> entry : atendimentosMap.entrySet()) {
            if(entry.getKey().equals("0")){
                for (DenteAtendimentoItemDTO denteAtendimentoItemDTO : entry.getValue()) {
                    AtendimentoItem ai = null;
                    if(denteAtendimentoItemDTO.getAtendimentoItem().getCodigo() == null){
                        ai = denteAtendimentoItemDTO.getAtendimentoItem();
                    }else{
                        ai = (AtendimentoItem) getSession().merge(denteAtendimentoItemDTO.getAtendimentoItem());
                    }
                    ai.setOrigem(AtendimentoItem.ORIGEM_ODONTOLOGIA);

                    BOFactory.getBO(CadastroFacade.class).save(ai);
                }
            }else{
                for (DenteAtendimentoItemDTO dto : entry.getValue()) {
                    AtendimentoItem ai = null;
                    if(dto.getAtendimentoItem().getCodigo() == null){
                        ai = dto.getAtendimentoItem();
                    }else{
                        ai = (AtendimentoItem) getSession().merge(dto.getAtendimentoItem());
                    }
                    ai.setOrigem(AtendimentoItem.ORIGEM_ODONTOLOGIA);
                    BOFactory.getBO(CadastroFacade.class).save(ai);

                    
                    EloDenteAtendimentoItem elo;
                    if(dto.getEloDenteAtendimentoItem() == null){
                        elo = new EloDenteAtendimentoItem();
                        Dente dente = new Dente(Coalesce.asLong(entry.getKey()));
                        elo.setAtendimentoItem(ai);
                        elo.setDente(dente);
                    }else{
                        elo = dto.getEloDenteAtendimentoItem();
                    }
                    elo.setAtendimentoItem(ai);

                    BOFactory.getBO(CadastroFacade.class).save(elo);
                }
            }
        }
    }
}
