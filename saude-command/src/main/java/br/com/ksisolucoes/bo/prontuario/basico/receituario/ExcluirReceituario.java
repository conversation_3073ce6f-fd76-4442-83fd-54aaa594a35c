package br.com.ksisolucoes.bo.prontuario.basico.receituario;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import org.hibernate.criterion.Restrictions;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ExcluirReceituario extends AbstractCommandTransaction<ExcluirReceituario> {

    private Receituario receituario;
    private boolean cancelarComponentes;

    public ExcluirReceituario(Receituario receituario, boolean cancelarComponentes) {
        this.receituario = receituario;
        this.cancelarComponentes = cancelarComponentes;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (receituario != null) {
            receituario.setSituacao(Receituario.Situacao.CANCELADO.value());
            BOFactory.getBO(AtendimentoFacade.class).removerAtendimentoProntuario(AtendimentoProntuario.TipoRegistro.RECEITUARIO.value(), receituario.getAtendimento(), receituario.getCodigo());
            BOFactory.save(receituario);

            List<ReceituarioItem> receituarioItems = getSession().createCriteria(ReceituarioItem.class)
                    .add(Restrictions.eq(ReceituarioItem.PROP_RECEITUARIO, receituario))
                    .add(Restrictions.ne(ReceituarioItem.PROP_STATUS, ReceituarioItem.Status.CANCELADO.value()))
                    .list();

            for (ReceituarioItem receituarioItem : receituarioItems) {
                receituarioItem.setStatus(ReceituarioItem.Status.CANCELADO.value());
                receituarioItem.setDataCancelamento(DataUtil.getDataAtual());
                receituarioItem.setUsuarioCancelamento(SessaoAplicacaoImp.getInstance().getUsuario());
                BOFactory.save(receituarioItem);

                if (receituarioItem.getLaudoMedicamentoEspecial() != null) {
                    LaudoMedicamentosEspeciais lme = LoadManager.getInstance(LaudoMedicamentosEspeciais.class)
                            .setId(receituarioItem.getLaudoMedicamentoEspecial().getCodigo())
                            .start().getVO();
                    if (lme != null && !LaudoMedicamentosEspeciais.Status.CANCELADO.value().equals(lme.getStatus())) {
                        lme.setStatus(LaudoMedicamentosEspeciais.Status.CANCELADO.value());
                        BOFactory.save(lme);
                    }
                }

                if(cancelarComponentes){
                    List<ReceituarioItemComponente> componentesList = getSession().createCriteria(ReceituarioItemComponente.class)
                        .add(Restrictions.eq(ReceituarioItemComponente.PROP_RECEITUARIO_ITEM, receituarioItem))
                        .add(Restrictions.ne(ReceituarioItemComponente.PROP_STATUS, ReceituarioItemComponente.Status.CANCELADO.value()))
                        .list();
                    
                    if(CollectionUtils.isNotNullEmpty(componentesList)){
                        for (ReceituarioItemComponente componente : componentesList) {
                            componente.setStatus(ReceituarioItemComponente.Status.CANCELADO.value());
                            componente.setDataCancelamento(DataUtil.getDataAtual());
                            componente.setUsuarioCancelamento(SessaoAplicacaoImp.getInstance().getUsuario());
                            BOFactory.save(componente);
                        }
                    }
                }
            }
        }
    }
}
