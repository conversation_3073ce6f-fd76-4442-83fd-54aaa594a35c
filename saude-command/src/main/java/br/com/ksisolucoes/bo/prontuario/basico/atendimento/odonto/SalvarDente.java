package br.com.ksisolucoes.bo.prontuario.basico.atendimento.odonto;

import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Dente;
import br.com.ksisolucoes.vo.prontuario.basico.EloDenteSituacao;

import java.util.List;

import static ch.lambdaj.Lambda.forEach;

/**
 *
 * <AUTHOR>
 */
public class SalvarDente extends AbstractCommandTransaction {

    private Dente dente;
    private List<EloDenteSituacao> eloList;

    public SalvarDente(Dente dente, List<EloDenteSituacao> eloList) {
        this.dente = dente;
        this.eloList = eloList;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        BOFactory.save(dente);

        if (CollectionUtils.isNotNullEmpty(eloList)) {
            forEach(eloList).setDente(dente);
            VOUtils.persistirListaVosModificados(EloDenteSituacao.class, eloList, new QueryCustom.QueryCustomParameter(EloDenteSituacao.PROP_DENTE, dente));
        }
    }

    public Dente getDente() {
        return dente;
    }
}
