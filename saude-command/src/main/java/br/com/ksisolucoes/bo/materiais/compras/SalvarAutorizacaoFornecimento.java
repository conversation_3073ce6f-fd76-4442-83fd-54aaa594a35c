package br.com.ksisolucoes.bo.materiais.compras;

import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.materiais.compras.interfaces.dto.AutorizacaoFornecimentoDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import org.hibernate.criterion.Restrictions;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class SalvarAutorizacaoFornecimento extends AbstractCommandTransaction{

    private AutorizacaoFornecimentoDTO dto;
    private OrdemCompra ordemCompraSave;
    private boolean novaAutorizacao;

    public SalvarAutorizacaoFornecimento(AutorizacaoFornecimentoDTO dto, boolean novaAutorizacao) {
        this.dto = dto;
        this.novaAutorizacao = novaAutorizacao;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if(CollectionUtils.isEmpty(dto.getAutorizacaoFornecimentoItemList())){
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_pelo_menos_um_produto"));
        }

        OrdemCompra ordemCompra = HibernateUtil.lockTable(OrdemCompra.class, dto.getOrdemCompra().getCodigo());

        AutorizacaoFornecimento autorizacaoFornecimento = new AutorizacaoFornecimento();
        if(novaAutorizacao) {
            autorizacaoFornecimento.setOrdemCompra(ordemCompra);
            autorizacaoFornecimento.setDataAutorizacao(DataUtil.getDataAtual());
            autorizacaoFornecimento.setUsuario(SessaoAplicacaoImp.getInstance().getUsuario());
        } else {
            autorizacaoFornecimento = dto.getAutorizacaoFornecimento();
        }
        autorizacaoFornecimento.setNumeroAF(dto.getNumeroAF());
        if(autorizacaoFornecimento.getNumeroAF() != null) {
            LoadManager load = LoadManager.getInstance(AutorizacaoFornecimento.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(AutorizacaoFornecimento.PROP_NUMERO_A_F, BuilderQueryCustom.QueryParameter.IGUAL, autorizacaoFornecimento.getNumeroAF()));
            AutorizacaoFornecimento afAux = load.setMaxResults(1).start().getVO();

            if(afAux != null && afAux.getCodigo().compareTo(Coalesce.asLong(autorizacaoFornecimento.getCodigo())) != 0){
                throw new ValidacaoException(Bundle.getStringApplication("msg_numero_autorizacao_ja_existe"));
            }
        }

        autorizacaoFornecimento = BOFactory.getBO(CadastroFacade.class).save(autorizacaoFornecimento);

        Fabricante fabricante;
        for(int i = 0; i < dto.getAutorizacaoFornecimentoItemList().size(); i++) {
            if(novaAutorizacao || dto.getAutorizacaoFornecimentoItemList().get(i).getAutorizacaoFornecimento() == null) {

                fabricante = null;
                if (dto.getAutorizacaoFornecimentoItemList().get(i).getDescricaoFabricante() != null) {
                    fabricante = (Fabricante) getSession().createCriteria(Fabricante.class)
                            .add(Restrictions.eq(Fabricante.PROP_DESCRICAO, dto.getAutorizacaoFornecimentoItemList().get(i).getDescricaoFabricante().trim()).ignoreCase())
                            .uniqueResult();
                    if (fabricante == null) {
                        fabricante = new Fabricante();
                        fabricante.setFlagInternacional(1L);
                        fabricante.setDescricao(dto.getAutorizacaoFornecimentoItemList().get(i).getDescricaoFabricante());
                        BOFactory.save(fabricante);
                    }
                }

                OrdemCompraItem ordemCompraItem = HibernateUtil.lockTable(OrdemCompraItem.class, dto.getAutorizacaoFornecimentoItemList().get(i).getOrdemCompraItem().getCodigo());
                validarSaldoPregao(ordemCompraItem, dto.getAutorizacaoFornecimentoItemList().get(i).getQtdItens());
                ordemCompraItem.setQuantidadeCompra(Coalesce.asBigDecimal(ordemCompraItem.getQuantidadeCompra()).add(dto.getAutorizacaoFornecimentoItemList().get(i).getQtdItens()));
                BOFactory.getBO(CadastroFacade.class).save(ordemCompraItem);

                AutorizacaoFornecimentoItem item = dto.getAutorizacaoFornecimentoItemList().get(i);
                item.setUsuario(autorizacaoFornecimento.getUsuario());
                item.setAutorizacaoFornecimento(autorizacaoFornecimento);
                item.setDataAutorizacaoItem(DataUtil.getDataAtual());
                item.setFabricante(fabricante);
                item.setSituacao(AutorizacaoFornecimentoItem.Situacao.AUTORIZADO.value());

                BOFactory.getBO(CadastroFacade.class).save(item);
            }
        }
        if(autorizacaoFornecimento != null) {
            ordemCompra.setAutorizacaoFornecimento(autorizacaoFornecimento);
            this.ordemCompraSave = BOFactory.getBO(CadastroFacade.class).save(ordemCompra);
        }
    }

    private void validarSaldoPregao(OrdemCompraItem ordemCompraItem, BigDecimal qtdSolicitada) throws ValidacaoException {
        if(ordemCompraItem != null) {
            BigDecimal saldo = ordemCompraItem.getSaldoPregao();
            if(qtdSolicitada.compareTo(saldo) > 0L) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_saldo_indisponivel_pregao_x_saldo_x_produto", saldo, ordemCompraItem.getProduto().getDescricao()));
            }
        } else {
            throw new ValidacaoException(Bundle.getStringApplication("msg_produto_indisponivel"));
        }
    }


    public OrdemCompra getOrdemCompraSave(){
        return this.ordemCompraSave;
    }

}
