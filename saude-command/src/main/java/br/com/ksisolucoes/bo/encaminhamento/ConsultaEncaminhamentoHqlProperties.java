package br.com.ksisolucoes.bo.encaminhamento;

import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.encaminhamento.interfaces.dto.EncaminhamentoDTO;
import br.com.ksisolucoes.encaminhamento.interfaces.dto.EncaminhamentoDTOParam;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.cadsus.TipoOcorrencia;
import br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ConsultaEncaminhamentoHqlProperties implements Serializable {



    public static void initHql(HQLHelper hql, EncaminhamentoDTOParam param){
        hql.addToSelect("usuarioCadsus.codigo", "encaminhamento.usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "encaminhamento.usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.telefone", "encaminhamento.usuarioCadsus.telefone");
        hql.addToSelect("usuarioCadsus.telefone2", "encaminhamento.usuarioCadsus.telefone2");
        hql.addToSelect("usuarioCadsus.telefone3", "encaminhamento.usuarioCadsus.telefone3");
        hql.addToSelect("usuarioCadsus.telefone4", "encaminhamento.usuarioCadsus.telefone4");
        hql.addToSelect("usuarioCadsus.celular", "encaminhamento.usuarioCadsus.celular");
        hql.addToSelect("usuarioCadsus.email", "encaminhamento.usuarioCadsus.email");
        hql.addToSelect("usuarioCadsus.dataNascimento", "encaminhamento.usuarioCadsus.dataNascimento");
        hql.addToSelect("usuarioCadsus.sexo", "encaminhamento.usuarioCadsus.sexo");

        hql.addToSelect("unidadeEncaminhamento.codigo", "encaminhamento.unidadeEncaminhamento.codigo");
        hql.addToSelect("unidadeEncaminhamento.descricao", "encaminhamento.unidadeEncaminhamento.descricao");

        hql.addToSelect("tipoEncaminhamento.codigo", "encaminhamento.tipoEncaminhamento.codigo");
        hql.addToSelect("tipoEncaminhamento.descricao", "encaminhamento.tipoEncaminhamento.descricao");

        hql.addToSelect("profissional.codigo", "encaminhamento.profissional.codigo");
        hql.addToSelect("profissional.referencia", "encaminhamento.profissional.referencia");
        hql.addToSelect("profissional.nome", "encaminhamento.profissional.nome");

        hql.addToSelect("profissionalAgendamento.codigo", "encaminhamento.profissionalAgendamento.codigo");
        hql.addToSelect("profissionalAgendamento.referencia", "encaminhamento.profissionalAgendamento.referencia");
        hql.addToSelect("profissionalAgendamento.nome", "encaminhamento.profissionalAgendamento.nome");

        hql.addToSelect("encaminhamento.codigo", "encaminhamento.codigo");
        hql.addToSelect("encaminhamento.observacaoAutorizacao", "encaminhamento.observacaoAutorizacao");
        hql.addToSelect("encaminhamento.dataCadastro", "encaminhamento.dataCadastro");
        hql.addToSelect("encaminhamento.flagPrioridade", "encaminhamento.flagPrioridade");
        hql.addToSelect("encaminhamento.flagUrgencia", "encaminhamento.flagUrgencia");
        hql.addToSelect("encaminhamento.status", "encaminhamento.status");
        hql.addToSelect("encaminhamento.tipo", "encaminhamento.tipo");
        hql.addToSelect("encaminhamento.compareceuAtendimento", "encaminhamento.compareceuAtendimento");
        hql.addToSelect("encaminhamento.dataContatoPaciente", "encaminhamento.dataContatoPaciente");
        
        hql.addToSelect("encaminhamentoAgendamento.codigo", "encaminhamento.encaminhamentoAgendamento.codigo");
        hql.addToSelect("encaminhamentoAgendamento.dataAgendamento", "encaminhamento.encaminhamentoAgendamento.dataAgendamento");
        
        hql.addToSelect("(select uco.descricao from UsuarioCadsusOcorrencia uco"
                + " where uco.usuarioCadsus = usuarioCadsus"
                + " and uco.tipoOcorrencia.codigo = "+TipoOcorrencia.TIPO_AGENDAMENTO+""
                + " and uco.encaminhamento = encaminhamento"
                + " and uco.dataOcorrencia = (select max(usuarioCadsusOcorrencia.dataOcorrencia) from UsuarioCadsusOcorrencia usuarioCadsusOcorrencia"
                + "                           where usuarioCadsusOcorrencia.usuarioCadsus = usuarioCadsus"
                + "                           and usuarioCadsusOcorrencia.encaminhamento = encaminhamento"
                + "                           and usuarioCadsusOcorrencia.tipoOcorrencia.codigo = "+TipoOcorrencia.TIPO_AGENDAMENTO+"))", "ocorrencia");
        hql.addToSelect("(select uco.dataOcorrencia from UsuarioCadsusOcorrencia uco"
                + " where uco.usuarioCadsus = usuarioCadsus"
                + " and uco.tipoOcorrencia.codigo = "+TipoOcorrencia.TIPO_AGENDAMENTO+""
                + " and uco.encaminhamento = encaminhamento"
                + " and uco.dataOcorrencia = (select max(usuarioCadsusOcorrencia.dataOcorrencia) from UsuarioCadsusOcorrencia usuarioCadsusOcorrencia"
                + "                           where usuarioCadsusOcorrencia.usuarioCadsus = usuarioCadsus"
                + "                           and usuarioCadsusOcorrencia.encaminhamento = encaminhamento"
                + "                           and usuarioCadsusOcorrencia.tipoOcorrencia.codigo = "+TipoOcorrencia.TIPO_AGENDAMENTO+"))", "dataOcorrencia");

        hql.addToSelect("localAgendamento.codigo", "encaminhamentoAgendamento.localAgendamento.codigo");
        hql.addToSelect("localAgendamento.referencia", "encaminhamentoAgendamento.localAgendamento.referencia");
        hql.addToSelect("localAgendamento.descricao", "encaminhamentoAgendamento.localAgendamento.descricao");

        hql.addToSelect("naturezaProcura.codigo", "encaminhamento.tipoEncaminhamento.naturezaProcuraTipoAtendimento.naturezaProcura.codigo");
        hql.addToSelect("tipoAtendimento.codigo", "encaminhamento.tipoEncaminhamento.naturezaProcuraTipoAtendimento.tipoAtendimento.codigo");

        hql.addToSelect("usuarioContato.codigo", "encaminhamento.usuarioContato.codigo");
        hql.addToSelect("usuarioContato.nome", "encaminhamento.usuarioContato.nome");

        hql.addToSelect("(select count(*) from EncaminhamentoConsulta ec where ec.encaminhamento = encaminhamento)", "consultas");

        hql.setTypeSelect(EncaminhamentoDTO.class.getName());
        hql.addToFrom("Encaminhamento encaminhamento"
                + " left join encaminhamento.unidadeEncaminhamento unidadeEncaminhamento"
                + " left join encaminhamento.usuarioCadsus usuarioCadsus"
                + " left join encaminhamento.tipoEncaminhamento tipoEncaminhamento"
                + " left join encaminhamento.encaminhamentoAgendamento encaminhamentoAgendamento"
                + " left join encaminhamentoAgendamento.localAgendamento localAgendamento"
                + " left join tipoEncaminhamento.naturezaProcuraTipoAtendimento naturezaProcuraTipoAtendimento"
                + " left join naturezaProcuraTipoAtendimento.naturezaProcura naturezaProcura"
                + " left join naturezaProcuraTipoAtendimento.tipoAtendimento tipoAtendimento"
                + " left join encaminhamento.atendimento atendimento"
                + " left join atendimento.empresa empresaAtendimento"
                + " left join encaminhamento.profissional profissional"
                + " left join encaminhamento.profissionalAgendamento profissionalAgendamento"
                + " left join encaminhamento.usuarioContato usuarioContato");

        hql.addToWhereWhithAnd("unidadeEncaminhamento in", param.getEmpresas());
        hql.addToWhereWhithAnd("usuarioCadsus in", param.getPacientes());
        hql.addToWhereWhithAnd("tipoEncaminhamento in", param.getTiposEncaminhamento());

        if(CollectionUtils.isNotNullEmpty(param.getTipos())){
            List<Long> tipos = new ArrayList<Long>();
            for (Encaminhamento.Tipo tipo : param.getTipos()) {
                tipos.add(tipo.value());
            }
            hql.addToWhereWhithAnd("encaminhamento.tipo in", tipos);
        }

        if (param.getPeriodo()!=null) {
            hql.addToWhereWhithAnd("encaminhamento."+param.getTipoData(), Data.adjustRangeHour(param.getPeriodo()));
        }

        if (!RepositoryComponentDefault.SIM.equals(param.getTodas())) {
            hql.addToWhereWhithAnd("encaminhamento.status in ", param.getInStatus());
            hql.addToWhereWhithAnd("encaminhamento.dataContatoPaciente is null");
        }

        hql.addToOrder("encaminhamento.status");
        hql.addToOrder("encaminhamento.flagUrgencia");
        hql.addToOrder("encaminhamento.flagPrioridade");
        hql.addToOrder("encaminhamento.codigo");
    }

}
