/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.entradas.estoque.estoqueempresa;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryEstoqueDisponivelUnidadeEstoqueValidoDTO;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryEstoqueDisponivelUnidadeEstoqueValidoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.vo.basico.Empresa;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryEstoqueDisponivelUnidadeEstoqueValido extends CommandQuery<QueryEstoqueDisponivelUnidadeEstoqueValido> {
    
    private QueryEstoqueDisponivelUnidadeEstoqueValidoDTOParam param;
    private List<QueryEstoqueDisponivelUnidadeEstoqueValidoDTO> result;

    public QueryEstoqueDisponivelUnidadeEstoqueValido(QueryEstoqueDisponivelUnidadeEstoqueValidoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        String pathQuantidade = "((estoqueDepositoView.estoqueFisico - estoqueDepositoView.estoqueVencido) + estoqueDepositoView.estoqueEncomendado - (estoqueDepositoView.estoqueReservado - estoqueDepositoView.estoqueReservadoVencido) )";
        if (param.isConsiderarLotesVencidos()) {
            pathQuantidade = "(estoqueDepositoView.estoqueFisico + estoqueDepositoView.estoqueEncomendado - estoqueDepositoView.estoqueReservado) ";
        }
        
        hql.addToSelectAndGroup("empresa.codigo","estabelecimento.codigo");
        hql.addToSelectAndGroup("empresa.referencia","estabelecimento.referencia");
        hql.addToSelectAndGroup("empresa.descricao","estabelecimento.descricao");
        
        hql.addToSelectAndGroup("produto.codigo","produto.codigo");
        hql.addToSelectAndGroup("produto.referencia","produto.referencia");
        hql.addToSelectAndGroup("produto.descricao","produto.descricao");
        
        hql.addToSelect("sum("+pathQuantidade+")","estoqueDisponivel");
        
        hql.setTypeSelect(QueryEstoqueDisponivelUnidadeEstoqueValidoDTO.class.getName());
        hql.addToFrom("EstoqueDepositoView estoqueDepositoView");
        
        hql.addToWhereWhithAnd("produto.codigo = ",param.getCodigoProduto());
        
        hql.addToWhereWhithAnd("(empresa.tipoUnidade = "+Empresa.TIPO_ESTABELECIMENTO_FARMACIA+" or empresa.codigo = "+SessaoAplicacaoImp.getInstance().<Empresa>getEmpresa().getCodigo()+") ");
        
        hql.addToWhereWhithAnd("("+pathQuantidade+") > ",param.getQuantidade());
        
        hql.addToOrder("produto.descricao");
        hql.addToOrder("empresa.descricao");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String,Object>>)result);
    }

    public List<QueryEstoqueDisponivelUnidadeEstoqueValidoDTO> getResult() {
        return result;
    }

}
