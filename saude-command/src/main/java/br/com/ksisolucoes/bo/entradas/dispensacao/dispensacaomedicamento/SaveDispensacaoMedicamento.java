package br.com.ksisolucoes.bo.entradas.dispensacao.dispensacaomedicamento;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.atividadegrupo.interfaces.dto.ContaEItemContaPacienteDispensacaoMedicamentoDTO;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.bo.entradas.devolucao.interfaces.dto.DispensacaoMedicamentoContaPacienteDTO;
import br.com.ksisolucoes.bo.entradas.dispensacao.interfaces.facade.DispensacaoMedicamentoFacade;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ReceituarioFacade;
import br.com.ksisolucoes.bo.recepcao.interfaces.dto.GerarAtendimentoDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.ConcurrentDAOException;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.bo.BusinessObjectConstants;
import br.com.ksisolucoes.util.validacao.AbstractValidacao;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Doenca;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.*;
import br.com.ksisolucoes.vo.cadsus.base.BaseUsuarioCadsusDoenca;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoHelper;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.dispensacao.LiberacaoReceita;
import br.com.ksisolucoes.vo.entradas.dispensacao.base.BaseDispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.ProdutoHelper;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import org.hibernate.Criteria;
import org.hibernate.criterion.Restrictions;

import java.util.*;
import java.util.stream.Collectors;

import static br.com.ksisolucoes.system.factory.BOFactory.getBO;

/**
 * <AUTHOR> Giordani
 */
public class SaveDispensacaoMedicamento extends SaveVO {

    private static final long serialVersionUID = 1L;
    private DispensacaoMedicamento dispensacaoMedicamento;

    private static final String HIPERTENSAO_ARTERIAL = "Hipertensão Arterial";
    private static final String DIABETE = "Diabetes";

    public SaveDispensacaoMedicamento(Object vo) {
        super(vo);
        this.dispensacaoMedicamento = (DispensacaoMedicamento) vo;
    }

    protected void antesSave() throws ValidacaoException, DAOException {


        if (this.dispensacaoMedicamento.getEmpresa() == null) {
            this.dispensacaoMedicamento.setEmpresa(this.getSessao().<Empresa>getEmpresa());
        }

        if (this.dispensacaoMedicamento.getEmpresaOrigem() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_empresa_origem_obrigatoria"));
        }

        if (this.dispensacaoMedicamento.getDataReceita() != null) {
            if (!DispensacaoMedicamentoHelper.isDataReceitaValida(this.dispensacaoMedicamento)) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_data_prescricao_invalida"));
            }
        } else {
            throw new ValidacaoException(Bundle.getStringApplication("msg_data_prescricao_obrigatoria"));
        }

        if (existeMedicamentoLista()) {
            if (this.dispensacaoMedicamento.getTipoReceita() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_tipo_receita_obrigatorio"));
            }
        }

        if (this.dispensacaoMedicamento.getUsuario() == null) {
            this.dispensacaoMedicamento.setUsuario((Usuario) this.sessao.getUsuario());
        }

        if (this.dispensacaoMedicamento.getDataUsuario() == null) {
            this.dispensacaoMedicamento.setDataUsuario(Data.getDataAtual());
        }
        if (this.dispensacaoMedicamento.getDataUltimaDispensacao() == null) {
            this.dispensacaoMedicamento.setDataUltimaDispensacao(Data.getDataAtual());
        }

        if (this.dispensacaoMedicamento.getDataDispensacao() == null) {
            this.dispensacaoMedicamento.setDataDispensacao(Data.getDataAtual());
        }

        if (this.dispensacaoMedicamento.getUsuarioCadsusDestino() != null) {
            if (this.dispensacaoMedicamento.getNomeUsuarioDestino() == null || this.dispensacaoMedicamento.getNomeUsuarioDestino().isEmpty()) {
                this.dispensacaoMedicamento.setNomeUsuarioDestino(this.dispensacaoMedicamento.getUsuarioCadsusDestino().getNomeSocial());
            }
        }
        if (this.dispensacaoMedicamento.getProfissionalSemVinculo() == null) {
            if (this.dispensacaoMedicamento.getProfissional() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_profissional_obrigatorio"));
            }
            this.dispensacaoMedicamento.setNomeProfissional(dispensacaoMedicamento.getProfissional().getNome());
        }

    }

    /* (non-Javadoc)
     * @see br.com.ksisolucoes.bo.command.SaveVO#depoisSave()
     */
    protected void depoisSave() throws ValidacaoException, DAOException {
        this.salvarItens();

        if (!this.dispensacaoMedicamento.getRetornoValidacao().isValido()) {
            throw new ValidacaoException(this.dispensacaoMedicamento.getRetornoValidacao());
        }

        if (isPacienteForaMunicipio()) {
            /*
             * CONTROLE SOBRE LIBERAÇÃO DE RECEITAS
             * ------------------------------------
             * Se houver uma liberação vinculada a dispensação atual do tipo Paciente Externo,
             * deve ser então realizado sua devida atualização.
             *---------------------------------------------------------------------*/
            Criteria criteria = this.getSession().createCriteria(LiberacaoReceita.class);

            criteria.add(Restrictions.eq(LiberacaoReceita.PROP_TIPO_LIBERACAO, LiberacaoReceita.LIBERACAO_PACIENTE_EXTERNO));
            criteria.add(Restrictions.eq(LiberacaoReceita.PROP_USUARIO_CADSUS, this.dispensacaoMedicamento.getUsuarioCadsusDestino()));
            criteria.add(Restrictions.eq(LiberacaoReceita.PROP_STATUS, LiberacaoReceita.STATUS_ABERTO));
            criteria.add(Restrictions.eq(LiberacaoReceita.PROP_EMPRESA, this.dispensacaoMedicamento.getEmpresa()));

            LiberacaoReceita liberacaoReceita = (LiberacaoReceita) criteria.uniqueResult();

            if (liberacaoReceita != null) {
                if (liberacaoReceita.getDispensacaoMedicamentoItem() != null) {
                    throw new ConcurrentDAOException();
                }
                liberacaoReceita.setDispensacaoMedicamento(this.dispensacaoMedicamento);
                liberacaoReceita.setStatus(LiberacaoReceita.STATUS_LIBERADO);

                BOFactory.getBO(CadastroFacade.class).save(liberacaoReceita);
            }
            /*---------------------------------------------------------------------*/
        }

        gerarContaPaciente();
        
        if(dispensacaoMedicamento.getEvolucaoAtendimento() != null){
            gerarAtendimento(dispensacaoMedicamento.getUsuarioCadsusDestino(), this.sessao.getUsuario().getProfissional());
        }
    }
    
    private NaturezaProcuraTipoAtendimento getNaturezaProcuraTipoAtendimento(TipoAtendimento tipoAtendimento, NaturezaProcura naturezaProcura) throws ValidacaoException {
        List<NaturezaProcuraTipoAtendimento> nptaList = getSession().createCriteria(NaturezaProcuraTipoAtendimento.class)
                .add(Restrictions.eq(NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, tipoAtendimento))
                .add(Restrictions.eq(NaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA, naturezaProcura))
                .createCriteria(NaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA)
                .add(Restrictions.eq(NaturezaProcura.PROP_PERMITE_AGENDAMENTO, RepositoryComponentDefault.NAO))
                .list();
        if (CollectionUtils.isEmpty(nptaList)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_existe_relacionameto_tipo_proced_nat_proc"));
        } else if (nptaList.size() > 1) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_existe_mais_um_relacionamento_tp_proc_nat_proc"));
        }
        return nptaList.get(0);
    }

    private void gerarAtendimento(UsuarioCadsus usuarioCadsus, Profissional profissional) throws ValidacaoException, DAOException {
        NaturezaProcura naturezaProcura = getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("NatProcuraMarcacaoUnidade");
        TipoAtendimento tipoAtendimentoAtividade = getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("TipoAtendimentoDispensacao");
        Convenio convenio = getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("convenioSUS");
        NaturezaProcuraTipoAtendimento naturezaProcuraTipoAtendimento = getNaturezaProcuraTipoAtendimento(tipoAtendimentoAtividade, naturezaProcura);
        TabelaCbo cbo = profissional.getCboProfissional();

        GerarAtendimentoDTO dtoAtendimento = new GerarAtendimentoDTO();
        dtoAtendimento.setTipoAtendimento(naturezaProcuraTipoAtendimento.getTipoAtendimento());
        dtoAtendimento.setUsuarioCadsus(usuarioCadsus);
        dtoAtendimento.setTipoDemanda(Atendimento.TIPO_ATENDIMENTO_IMEDIATA);
        dtoAtendimento.setNaturezaProcura(naturezaProcura);
        dtoAtendimento.setConvenio(convenio);
        dtoAtendimento.setProfissional(profissional);
        dtoAtendimento.setEmpresa(this.sessao.getEmpresa());
        dtoAtendimento.setValidarCboProfissionalEmpresa(false);

        Atendimento atendimento = getBO(AtendimentoFacade.class).inserirAtendimentoUnidade(dtoAtendimento);
        atendimento.setTabelaCbo(cbo);

        atendimento.setStatus(Atendimento.STATUS_FECHADO_SEM_ATENDIMENTO);
        atendimento.setDataFechamento(DataUtil.getDataAtual());
        atendimento.setDataAtendimento(atendimento.getDataFechamento());

        atendimento = BOFactory.save(atendimento);
        
        salvarEvolucaoProntuario(profissional, cbo, atendimento);
        salvarAtendimentoProntuario(profissional, cbo, atendimento);
    }    
    
    private void salvarEvolucaoProntuario(Profissional profissional, TabelaCbo cbo, Atendimento atendimento) throws DAOException, ValidacaoException {
        EvolucaoProntuario evolucaoProntuario = new EvolucaoProntuario();

        evolucaoProntuario.setDescricao(dispensacaoMedicamento.getEvolucaoAtendimento());
        evolucaoProntuario.setProfissional(profissional);
        evolucaoProntuario.setAtendimento(atendimento);
        evolucaoProntuario.setDataHistorico(DataUtil.getDataAtual());
        evolucaoProntuario.setDataRegistro(DataUtil.getDataAtual());
        evolucaoProntuario.setUsuario((Usuario) sessao.getUsuario());
        evolucaoProntuario.setAcessoCompartilhado(null);
        evolucaoProntuario.setConduta(null);
        evolucaoProntuario.setTabelaCbo(cbo);

        BOFactory.getBO(ReceituarioFacade.class).salvarEvolucaoProntuario(evolucaoProntuario);
    }

    private void salvarAtendimentoProntuario(Profissional profissional, TabelaCbo cbo, Atendimento atendimento) throws DAOException, ValidacaoException {
        AtendimentoProntuario atendimentoProntuario = new AtendimentoProntuario();
        atendimentoProntuario.setData(DataUtil.getDataAtual());
        atendimentoProntuario.setDescricao(dispensacaoMedicamento.getEvolucaoAtendimento());
        atendimentoProntuario.setTipoRegistro(AtendimentoProntuario.TipoRegistro.EVOLUCAO.value());
        atendimentoProntuario.setUsuarioCadsus(atendimento.getUsuarioCadsus());
        atendimentoProntuario.setEmpresa(atendimento.getEmpresa());
        atendimentoProntuario.setProfissional(profissional);
        atendimentoProntuario.setAtendimento(atendimento);
        atendimentoProntuario.setAcessoCompartilhado(null);
        atendimentoProntuario.setTabelaCbo(cbo);
        atendimentoProntuario.setFlagOrigem(AtendimentoProntuario.Origem.INTERNO.value());

        BOFactory.save(atendimentoProntuario);
    }
    
    
    protected void validacaoNotNull(Object obj) throws ValidacaoException {
        AbstractValidacao validacao = new RetornoValidacao();

        super.validacaoNotNull(obj);

        String entidade = Bundle.getStringApplication("rotulo_dispensacao_medicamento", this.sessao.getLocale());
        String tab = "    - ";
        RetornoValidacao retornoValidacao = new RetornoValidacao();
        if (validacao.isValido()) {
            retornoValidacao.add(entidade, this.dispensacaoMedicamento.getClass().toString());
        }

        if (this.dispensacaoMedicamento.getUsuarioCadsusDestino() == null && Coalesce.asString(this.dispensacaoMedicamento.getNomeUsuarioDestino()).equals("")) {
            validacao.add(tab + Bundle.getStringBO(BusinessObjectConstants.MENSAGEM_ARGUMENTO_NULO, Bundle.getStringApplication("rotulo_paciente", this.sessao.getLocale()), this.sessao.getLocale()), "usuarioCadsusDestino");
        }

        if (!validacao.isValido()) {
            retornoValidacao.merge(validacao);
            throw new ValidacaoException(retornoValidacao);
        }
    }

    /**
     * Salva os itens
     *
     * @throws ValidacaoException
     */
    private void salvarItens() throws ValidacaoException, DAOException {
        if (!this.dispensacaoMedicamento.isItensDispensacaoMedicamentoNullOrBlank()) {
            Set itens = this.dispensacaoMedicamento.getItensDispensacaoMedicamentoSet();
            if (itens != null) {
                {
                    List<Long> codigosForNotDelete = new ArrayList<Long>();
                    for (DispensacaoMedicamentoItem dispensacaoMedicamentoItem : this.dispensacaoMedicamento.getItensDispensacaoMedicamentoSet()) {
                        if (dispensacaoMedicamentoItem.getCodigo() != null) {
                            codigosForNotDelete.add(dispensacaoMedicamentoItem.getCodigo());
                        }
                    }
                    Criteria criteria = this.getSession().createCriteria(DispensacaoMedicamentoItem.class)
                            .add(Restrictions.eq(DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO, this.dispensacaoMedicamento));
                    if (!codigosForNotDelete.isEmpty()) {
                        criteria.add(Restrictions.not(Restrictions.in(DispensacaoMedicamentoItem.PROP_CODIGO, codigosForNotDelete)));
                    }
                    List<DispensacaoMedicamentoItem> dispensacaoMedicamentoItems = criteria.list();
                    for (DispensacaoMedicamentoItem dispensacaoMedicamentoItem : dispensacaoMedicamentoItems) {
                        BOFactory.getBO(CadastroFacade.class).delete(dispensacaoMedicamentoItem);
                    }
                }

                Iterator itensIterator = itens.iterator();
                while (itensIterator.hasNext()) {
                    DispensacaoMedicamentoItem dispensacaoMedicamentoItem = (DispensacaoMedicamentoItem) itensIterator.next();

                    if (dispensacaoMedicamentoItem.getProduto() != null) {
                        dispensacaoMedicamentoItem.setDispensacaoMedicamento(this.dispensacaoMedicamento);

                        BOFactory.getBO(CadastroFacade.class).save(dispensacaoMedicamentoItem);
                    } else {
                        itens.remove(dispensacaoMedicamentoItem);
                    }
                }

                setarCondicaoSaudePacienteConformeMedicamentosDispensados();
            }
        }
    }

    private boolean isPacienteForaMunicipio() {
        Long count = LoadManager.getInstance(UsuarioCadsusEndereco.class)
                .addGroup(new QueryCustom.QueryCustomGroup(VOUtils.montarPath(UsuarioCadsusEndereco.PROP_ID, UsuarioCadsusEnderecoPK.PROP_ENDERECO), BuilderQueryCustom.QueryGroup.COUNT))
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusEndereco.PROP_STATUS, UsuarioCadsusEndereco.STATUS_ABERTO))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusEndereco.PROP_ID, UsuarioCadsusEnderecoPK.PROP_USUARIO_CADSUS), this.dispensacaoMedicamento.getUsuarioCadsusDestino()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusEndereco.PROP_ID, UsuarioCadsusEnderecoPK.PROP_ENDERECO, EnderecoUsuarioCadsus.PROP_CIDADE), BuilderQueryCustom.QueryParameter.DIFERENTE, this.dispensacaoMedicamento.getEmpresa().getCidade()))
                .start().getVO();

        return br.com.celk.util.Coalesce.asLong(count) > 0L;
    }

    private boolean existeMedicamentoLista() {
        Set itens = this.dispensacaoMedicamento.getItensDispensacaoMedicamentoSet();
        if (itens != null) {
            Iterator itensIterator = itens.iterator();
            while (itensIterator.hasNext()) {
                DispensacaoMedicamentoItem dispensacaoMedicamentoItem = (DispensacaoMedicamentoItem) itensIterator.next();

                if (ProdutoHelper.isMedicamento(dispensacaoMedicamentoItem.getProduto())) {
                    return true;
                }
            }
        }
        return false;
    }

    public DispensacaoMedicamento getDispensacaoMedicamento() {
        return this.dispensacaoMedicamento;
    }

    private void gerarContaPaciente() throws DAOException, ValidacaoException {
        List<DispensacaoMedicamentoContaPacienteDTO> dtoList = new ArrayList<>();

        // Dispensaçao Administraçao Medicamento Procedimento
        List<DispensacaoMedicamentoContaPacienteDTO> dispensacaoAdministracaoMedicamentoProcedimentoList = BOFactory.getBO(DispensacaoMedicamentoFacade.class)
                .consultarDispensacaoAdministracaoMedicamentoProcedimento(this.dispensacaoMedicamento.getCodigo());

        if (CollectionUtils.isNotNullEmpty(dispensacaoAdministracaoMedicamentoProcedimentoList)) {
            dtoList.addAll(dispensacaoAdministracaoMedicamentoProcedimentoList);
        }

        // Dispensaçao Unidade Cbo Procedimento
        List<DispensacaoMedicamentoContaPacienteDTO> dispensacaoUnidadeCboProcedimentoList = BOFactory.getBO(DispensacaoMedicamentoFacade.class).consultarDispensacaoUnidadeCboProcedimento(this.dispensacaoMedicamento.getCodigo());

        if (CollectionUtils.isNotNullEmpty(dispensacaoUnidadeCboProcedimentoList)) {
            dtoList.addAll(dispensacaoUnidadeCboProcedimentoList);
        }

        // Dispensações Faturáveis
        List<DispensacaoMedicamentoContaPacienteDTO> dispensacoesFaturaveisList = BOFactory.getBO(DispensacaoMedicamentoFacade.class).consultarDispensacoesFaturaveis(this.dispensacaoMedicamento.getCodigo());

        if (CollectionUtils.isNotNullEmpty(dispensacoesFaturaveisList)) {
            dtoList.addAll(dispensacoesFaturaveisList);
        }

        if (CollectionUtils.isNotNullEmpty(dtoList)) {
            ContaEItemContaPacienteDispensacaoMedicamentoDTO dto = new ContaEItemContaPacienteDispensacaoMedicamentoDTO();
            dto.setDispensacaoMedicamento(this.getDispensacaoMedicamento());
            dto.setItemDispensacaoList(dtoList);
            BOFactory.getBO(AtendimentoFacade.class).salvarContaEItemContaPacienteDispensacaoMedicamento(dto);
        }
    }

    private void setarCondicaoSaudePacienteConformeMedicamentosDispensados() throws DAOException, ValidacaoException {
        final List<DispensacaoMedicamentoItem> medicamentosDispensados = buscarMedicamentosDispensadosPaciente();
        final Set<Produto> produtos = mapearProdutos(medicamentosDispensados);

        int medicamentosHipertensao = 0;
        int medicamentosDiabete = 0;

        for (Produto produto : produtos) {
            medicamentosHipertensao = incrementarMedicamentoHipertensao(medicamentosHipertensao,
                    produto.getFlagHipertensaoArterial());

            medicamentosDiabete = incrementarMedicamentoDiabete(medicamentosDiabete,
                    produto.getFlagDiabete());
        }

        final Doenca hipertensao = buscarDoenca(Doenca.CondicaoEsus.HIPERTENSO);
        final Doenca diabete = buscarDoenca(Doenca.CondicaoEsus.DIABETE);

        final List<UsuarioCadsusDoenca> doencasAtuais = buscarDoencasPaciente();
        final Set<Doenca> codigosDoencasAtuais = mapearDoencas(doencasAtuais);

        List<UsuarioCadsusDoenca> novasDoencas = new ArrayList<>();

        if (Objects.nonNull(hipertensao) && !codigosDoencasAtuais.contains(hipertensao) && medicamentosHipertensao >= 2) {
            UsuarioCadsusDoenca doenca = new UsuarioCadsusDoenca();
            doenca.setDoenca(hipertensao);

            UsuarioCadsus paciente = this.dispensacaoMedicamento.getUsuarioCadsusDestino();
            doenca.setUsuarioCadsus(paciente);

            novasDoencas.add(doenca);
        }

        if (Objects.nonNull(diabete) && !codigosDoencasAtuais.contains(diabete) && medicamentosDiabete >= 2) {
            UsuarioCadsusDoenca doenca = new UsuarioCadsusDoenca();
            doenca.setDoenca(diabete);

            UsuarioCadsus paciente = this.dispensacaoMedicamento.getUsuarioCadsusDestino();
            doenca.setUsuarioCadsus(paciente);

            novasDoencas.add(doenca);
        }

        salvarDoencas(novasDoencas);
    }

    private Doenca buscarDoenca(Doenca.CondicaoEsus condicaoEsus) {
        return LoadManager.getInstance(Doenca.class)
                .addProperties(new HQLProperties(Doenca.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(Doenca.PROP_CONDICAO_ESUS, QueryCustom.QueryCustomParameter.IS_NOT_NULL))
                .addParameter(new QueryCustom.QueryCustomParameter(Doenca.PROP_CONDICAO_ESUS,condicaoEsus.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(Doenca.PROP_DOENCA_PRINCIPAL, QueryCustom.QueryCustomParameter.IS_NULL))
                .addParameter(new QueryCustom.QueryCustomParameter(Doenca.PROP_PADRAO, RepositoryComponentDefault.SIM))
                .setMaxResults(1)
                .start()
                .getVO();
    }

    private List<DispensacaoMedicamentoItem> buscarMedicamentosDispensadosPaciente() {
        return LoadManager.getInstance(DispensacaoMedicamentoItem.class)
                .addProperty(DispensacaoMedicamentoItem.PROP_CODIGO)
                .addProperty(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_PRODUTO, Produto.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_PRODUTO, Produto.PROP_FLAG_HIPERTENSAO_ARTERIAL))
                .addProperty(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_PRODUTO, Produto.PROP_FLAG_DIABETE))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_USUARIO_CADSUS_DESTINO,
                        UsuarioCadsus.PROP_CODIGO),
                        this.dispensacaoMedicamento.getUsuarioCadsusDestino()))
                .start()
                .getList();
    }

    private Set<Produto> mapearProdutos(List<DispensacaoMedicamentoItem> medicamentosDispensados) {
        return medicamentosDispensados.stream()
                .map(BaseDispensacaoMedicamentoItem::getProduto)
                .collect(Collectors.toSet());
    }

    private int incrementarMedicamentoHipertensao(int medicamentosHipertensao, String flagHipertensaoArterial) {
        if ("S".equals(flagHipertensaoArterial)) {
            medicamentosHipertensao++;
        }
        return medicamentosHipertensao;
    }

    private int incrementarMedicamentoDiabete(int medicamentosDiabete, String flagDiabete) {
        if ("S".equals(flagDiabete)) {
            medicamentosDiabete++;
        }
        return medicamentosDiabete;
    }

    private List<UsuarioCadsusDoenca> buscarDoencasPaciente() {
        return LoadManager.getInstance(UsuarioCadsusDoenca.class)
                .addProperties(new HQLProperties(Doenca.class, VOUtils.montarPath(UsuarioCadsusDoenca.PROP_DOENCA)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusDoenca.PROP_USUARIO_CADSUS,
                        this.dispensacaoMedicamento.getUsuarioCadsusDestino()))
                .start()
                .getList();
    }

    private Set<Doenca> mapearDoencas(List<UsuarioCadsusDoenca> doencas) {
        return doencas.stream()
                .map(BaseUsuarioCadsusDoenca::getDoenca)
                .collect(Collectors.toSet());
    }

    private void salvarDoencas(List<UsuarioCadsusDoenca> doencas) throws DAOException, ValidacaoException {
        if (!doencas.isEmpty()) {
            for (UsuarioCadsusDoenca novaDoenca : doencas) {
                BOFactory.save(novaDoenca);
            }
        }
    }

}
