package br.com.ksisolucoes.bo.cadsus.cds.esusfichaatendindividual;

import br.com.celk.bo.esus.interfaces.facade.EsusFacade;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.esus.cds.interfaces.dto.CadastroFichaAtendimentoIndividualDTO;
import br.com.ksisolucoes.bo.esus.cds.interfaces.dto.CadastroFichaAtendimentoIndividualItemDTO;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.GeracaoFichaEsusAtendimentoContaPacienteDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.Valor;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Equipe;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendIndividual;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendIndividualItem;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.grupos.EloGrupoAtendimentoCbo;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaEsus;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

import static br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper.carregarNumeroCartao;

/**
 * Created by sulivan on 28/08/17.
 */
public class GerarFichaEsusAtendimentoContaPaciente extends AbstractCommandTransaction {

    private GeracaoFichaEsusAtendimentoContaPacienteDTO dto;

    public GerarFichaEsusAtendimentoContaPaciente(GeracaoFichaEsusAtendimentoContaPacienteDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        EloGrupoAtendimentoCbo eloGrupoAtendimentoCbo = LoadManager.getInstance(EloGrupoAtendimentoCbo.class)
                .addProperty(EloGrupoAtendimentoCbo.PROP_CODIGO)
                .addProperty(VOUtils.montarPath(EloGrupoAtendimentoCbo.PROP_TABELA_CBO, TabelaCbo.PROP_CBO))
                .addProperty(VOUtils.montarPath(EloGrupoAtendimentoCbo.PROP_TABELA_CBO, TabelaCbo.PROP_NIVEL_ENSINO))
                .addParameter(new QueryCustom.QueryCustomParameter(EloGrupoAtendimentoCbo.PROP_TABELA_CBO, dto.getItemContaEsus().getTabelaCbo()))
                .start().getVO();

        if(eloGrupoAtendimentoCbo == null){
            throw new ValidacaoException(Bundle.getStringApplication("msg_cbo_X_nao_foi_encontrado_nenhum_grupo_cbo", dto.getItemContaEsus().getTabelaCbo().getDescricaoFormatado()));
        } else if(eloGrupoAtendimentoCbo.getTabelaCbo().getNivelEnsino() == null){
            throw new ValidacaoException(Bundle.getStringApplication("msg_nivel_ensino_cbo_X_nao_foi_configurado_grupo_cbo", dto.getItemContaEsus().getTabelaCbo().getDescricaoFormatado()));
        } else if(TabelaCbo.NivelEnsino.MEDIO.value().equals(eloGrupoAtendimentoCbo.getTabelaCbo().getNivelEnsino())){
            return;
        }

        if(dto.getItemContaEsus().getTipoAtendimento() == null || dto.getItemContaEsus().getClassificacaoAtendimento() == null || dto.getItemContaEsus().getConduta() == null){
            throw new ValidacaoException(Bundle.getStringApplication("informe_campo_atendimento_profissional_X", dto.getItemContaEsus().getProfissional().getNome()));
        }

        // Item
        EsusFichaAtendIndividualItem item = new EsusFichaAtendIndividualItem();
        item.setItemContaEsus(dto.getItemContaEsus());
        item.setTurno(dto.getItemContaEsus().getTurno());
        if(dto.getItemContaEsus().getContaPaciente().getUsuarioCadsus() != null) {
            item.setUsuarioCadsus(dto.getItemContaEsus().getContaPaciente().getUsuarioCadsus());
            item.setDataNascimento(dto.getItemContaEsus().getContaPaciente().getUsuarioCadsus().getDataNascimento());
            Long numeroCartao = carregarNumeroCartao(dto.getItemContaEsus().getContaPaciente().getUsuarioCadsus());
            if (numeroCartao != null) {
                item.setNumeroCartao(numeroCartao);
            }
            if (RepositoryComponentDefault.SEXO_MASCULINO.equals(dto.getItemContaEsus().getContaPaciente().getUsuarioCadsus().getSexo())) {
                item.setSexo(0L);
            } else {
                item.setSexo(1L);
            }

            UsuarioCadsusDado usuarioCadsusDado = LoadManager.getInstance(UsuarioCadsusDado.class)
                    .addProperty(UsuarioCadsusDado.PROP_IDADE_GESTACIONAL)
                    .addProperty(UsuarioCadsusDado.PROP_GESTANTE)
                    .setId(dto.getItemContaEsus().getContaPaciente().getUsuarioCadsus().getCodigo())
                    .start().getVO();

            if(usuarioCadsusDado != null){
                if(Coalesce.asLong(usuarioCadsusDado.getIdadeGestacional()) > 0L){
                    item.setIdadeGestacional(usuarioCadsusDado.getIdadeGestacional());
                }
                if(RepositoryComponentDefault.SIM_LONG.equals(usuarioCadsusDado.getGestante())){
                    PreNatal preNatal = LoadManager.getInstance(PreNatal.class)
                            .addProperty(PreNatal.PROP_CODIGO)
                            .addProperty(PreNatal.PROP_DATA_ULTIMA_MENSTRUACAO)
                            .addProperty(PreNatal.PROP_GRAVIDEZ_PLANEJADA)
                            .addProperty(PreNatal.PROP_PARTOS)
                            .addProperty(PreNatal.PROP_GESTACOES)
                            .addParameter(new QueryCustom.QueryCustomParameter(PreNatal.PROP_STATUS, PreNatal.Status.ABERTO.value()))
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PreNatal.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), dto.getItemContaEsus().getContaPaciente().getUsuarioCadsus().getCodigo()))
                            .start().getVO();

                    if(preNatal != null){
                        if (preNatal.getDataUltimaMenstruacao() != null) {
                            item.setDumGestante(preNatal.getDataUltimaMenstruacao());
                        }
                        if (preNatal.getGravidezPlanejada() != null) {
                            item.setGravidezPlanejada(preNatal.getGravidezPlanejada());
                        }
                        if (preNatal.getPartos() != null) {
                            item.setNumeroPartos(Coalesce.asLong(preNatal.getPartos()));
                        }
                        if (preNatal.getPartos() != null) {
                            item.setNumeroGestasPrevias(Coalesce.asLong(preNatal.getGestacoes()));
                        }
                    }
                }
            }
        }
        item.setLocalAtendimento(dto.getItemContaEsus().getLocalAtendimento() != null ? dto.getItemContaEsus().getLocalAtendimento() : Atendimento.LocalAtendimentoEsus.UBS.value());
        item.setTipoAtendimento(dto.getItemContaEsus().getTipoAtendimento());

        /****
         * REAVALIAR
         */
        // Atendimento Primário
//        AtendimentoPrimario atendimentoPrimario = LoadManager.getInstance(AtendimentoPrimario.class)
//                .addProperty(AtendimentoPrimario.PROP_PESO)
//                .addProperty(AtendimentoPrimario.PROP_ALTURA)
//                .addProperty(VOUtils.montarPath(AtendimentoPrimario.PROP_SISVAN_ALIMENTACAO, SisvanAlimentacao.PROP_CODIGO_ESUS))
//                .addProperty(AtendimentoPrimario.PROP_GESTANTE)
//                .addProperty(AtendimentoPrimario.PROP_IDADE_GESTACIONAL)
//                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoPrimario.PROP_ATENDIMENTO, Atendimento.PROP_ATENDIMENTO_PRINCIPAL), dto.getAtendimentoPrincipal()))
//                .addSorter(new QueryCustom.QueryCustomSorter(AtendimentoPrimario.PROP_CODIGO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
//                .setMaxResults(1)
//                .start().getVO();
//
//        if (atendimentoPrimario != null) {
//            Double peso = null;
//            Long altura = null;
//            SisvanAlimentacao sisvanAlimentacao = null;
//
//            if (Coalesce.asDouble(atendimentoPrimario.getPeso()) != 0) {
//                peso = atendimentoPrimario.getPeso();
//            }
//            if (Coalesce.asLong(atendimentoPrimario.getAltura()) != 0) {
//                altura = atendimentoPrimario.getAltura();
//            }
//            if (atendimentoPrimario.getSisvanAlimentacao() != null) {
//                sisvanAlimentacao = atendimentoPrimario.getSisvanAlimentacao();
//            }
//
//            if (Coalesce.asDouble(peso) > 0D) {
//                if (peso > 0.5 && peso < 500) {
//                    item.setPeso(peso);
//                }
//            }
//
//            if (Coalesce.asLong(altura) > 0L && Coalesce.asLong(altura) < 1000L) {
//                item.setAltura(altura.doubleValue());
//            }
//
//            if (sisvanAlimentacao != null && sisvanAlimentacao.getCodigoEsus() != null) {
//                item.setAleitamentoMaterno(sisvanAlimentacao.getCodigoEsus());
//            }
//        }

        if(dto.getItemContaEsus().getCiap() != null){
            item.setCiap(dto.getItemContaEsus().getCiap());
        }

        boolean cidTela = false;
        if (dto.getItemContaEsus().getCid() != null && dto.getItemContaEsus().getCid().getCodigo() != null) {
            item.setCid(dto.getItemContaEsus().getCid());
            cidTela = true;
        } else {
            Cid cid = (Cid) getSession().get(Cid.class, "Z00");
            if(cid != null){
                item.setCid(cid);
            }
        }

        List<String> codigoEsusCiapList = new ArrayList<>();
        if (dto.getItemContaEsus().getClassificacaoAtendimento() != null) {
            ClassificacaoAtendimento ca = (ClassificacaoAtendimento) getSession().get(ClassificacaoAtendimento.class, dto.getItemContaEsus().getClassificacaoAtendimento().getCodigo());
            if (ca != null && ca.getCodigoEsus() != null
                    && EsusFichaAtendIndividual.listaCiapCondicaoAvaliada.contains(ca.getCodigoEsus())) {
                codigoEsusCiapList.add(ca.getCodigoEsus());
            }
        } else if(item.getCid() == null){
            Cid cid = (Cid) getSession().get(Cid.class, "Z00");
            if(cid != null){
                item.setCid(cid);
            }
        }

        if(CollectionUtils.isNotNullEmpty(codigoEsusCiapList)){
            item.setProblemaCondicaoAvaliada(getProblemaCondicaoAvaliada(codigoEsusCiapList));
        } else {
            item.setProblemaCondicaoAvaliada(0L);
        }

        if(!cidTela && (item.getCiap() != null || item.getCiap2() != null || Coalesce.asLong(item.getProblemaCondicaoAvaliada()) > 0L)){
            item.setCid(null);
        }

        /****
         * REAVALIAR
         */
//        if (RepositoryComponentDefault.SIM_LONG.equals(dto.getVacinaEmDia())) {
//            item.setVacinaEmDia(RepositoryComponentDefault.SIM_LONG);
//        } else {
//            item.setVacinaEmDia(RepositoryComponentDefault.NAO_LONG);
//        }
        item.setNasfs(0L);

        // Consultar Atendimentos Encaminhamentos
//        boolean existsAtendimentosEncaminhamentos = LoadManager.getInstance(AtendimentoEncaminhamento.class)
//                .addProperty(AtendimentoEncaminhamento.PROP_CODIGO)
//                .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoEncaminhamento.PROP_ATENDIMENTO, dto))
//                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoEncaminhamento.PROP_ENCAMINHAMENTO_TIPO, EncaminhamentoTipo.PROP_TIPO), EncaminhamentoTipo.Tipo.OBSERVACAO.value()))
//                .exists();
//
//        if (existsAtendimentosEncaminhamentos) {
//            item.setFicouEmObservacao(RepositoryComponentDefault.SIM_LONG);
//        }

        // Conduta
        List<Long> condutaAtendimentoAdicionarList = new ArrayList<>();
        if (dto.getItemContaEsus().getConduta() != null && dto.getItemContaEsus().getConduta().getCodigo() != null) {
            Conduta conduta = (Conduta) getSession().get(Conduta.class, dto.getItemContaEsus().getConduta().getCodigo());
            if(conduta != null && conduta.getCodigoEsus() != null){
                if (!Conduta.TipoConduta.CONSULTA_ODONTOLOGICA.value().equals(conduta.getTipoConduta())) {
                    condutaAtendimentoAdicionarList.add(conduta.getCodigoEsus());
                }
            }
        }

        if (CollectionUtils.isNotNullEmpty(condutaAtendimentoAdicionarList)) {
            Long somatorio = 0L;
            EsusFichaAtendIndividualItem.CondutaEncaminhamento condutaEncaminhamento;
            for(Long i : condutaAtendimentoAdicionarList){
                condutaEncaminhamento = EsusFichaAtendIndividualItem.CondutaEncaminhamento.value(i);

                if(condutaEncaminhamento != null){
                    somatorio = new Dinheiro(somatorio).somar(condutaEncaminhamento.sum().doubleValue()).longValue();
                }
            }
            item.setCondutas(somatorio);
        }

        if(Coalesce.asLong(item.getCondutas()) <= 0L){
            throw new ValidacaoException(Bundle.getStringApplication("msg_faturamento_esus_necessario_preencher_conduta"));
        }

        CadastroFichaAtendimentoIndividualItemDTO itemFichaAtendimentoIndividualDTO = new CadastroFichaAtendimentoIndividualItemDTO();
        itemFichaAtendimentoIndividualDTO.setEsusFichaAtendIndividualItem(item);

        // Ficha
        EsusFichaAtendIndividual esusFichaAtendIndividual = new EsusFichaAtendIndividual();
        esusFichaAtendIndividual.setEmpresa(dto.getItemContaEsus().getEmpresaFaturamento());
        esusFichaAtendIndividual.setProfissionalPrincipal(dto.getItemContaEsus().getProfissional());
        esusFichaAtendIndividual.setCboPrincipal(dto.getItemContaEsus().getTabelaCbo());
        esusFichaAtendIndividual.setDataAtendimento(dto.getItemContaEsus().getContaPaciente().getDataGeracao());
        esusFichaAtendIndividual.setDataAtendimentoFinal(dto.getItemContaEsus().getContaPaciente().getDataFinal());

        Equipe equipe = getEquipeNoAtendimento(dto.getItemContaEsus());
        if (equipe != null && equipe.getEquipeCnes() != null) {
            esusFichaAtendIndividual.setCodigoIne(StringUtils.leftPad(Coalesce.asString(equipe.getEquipeCnes()), 10, "0"));
        }else{
            equipe = getEquipeProfissional(dto.getItemContaEsus().getProfissional());
            if (equipe != null && equipe.getEquipeCnes() != null) {
                esusFichaAtendIndividual.setCodigoIne(StringUtils.leftPad(Coalesce.asString(equipe.getEquipeCnes()), 10, "0"));
            }
        }

        CadastroFichaAtendimentoIndividualDTO cadastroFichaAtendimentoIndividualDTO = new CadastroFichaAtendimentoIndividualDTO();
        cadastroFichaAtendimentoIndividualDTO.setEsusFichaAtendIndividual(esusFichaAtendIndividual);
        cadastroFichaAtendimentoIndividualDTO.getEsusFichaAtendimentoIndividualItemList().add(itemFichaAtendimentoIndividualDTO);

        // Obs: O registro EsusIntegracaoCds é gerado ao salvar a ficha
        BOFactory.getBO(EsusFacade.class).salvarFichaAtendIndividual(cadastroFichaAtendimentoIndividualDTO);
    }

    private Equipe getEquipeNoAtendimento(ItemContaEsus itemContaEsus) {
        if (itemContaEsus != null
                && itemContaEsus.getContaPaciente() != null
                && itemContaEsus.getContaPaciente().getAtendimentoInformacao() != null) {
            return itemContaEsus.getContaPaciente().getAtendimentoInformacao().getAtendimentoPrincipal().getEquipe();
        }

        return null;
    }
    private Equipe getEquipeProfissional(Profissional profissional) {

        if (profissional != null&& profissional.getCodigo() != null){
            List<EquipeProfissional> equipeProfissionalList = LoadManager.getInstance(EquipeProfissional.class)
                    .addProperty(EquipeProfissional.PROP_CODIGO)
                    .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_REFERENCIA))
                    .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_CNES))
                    .addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_STATUS, EquipeProfissional.STATUS_ATIVO))
                    .addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_PROFISSIONAL, profissional))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_ATIVO), RepositoryComponentDefault.SIM))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA), dto.getItemContaEsus().getEmpresaFaturamento()))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(equipeProfissionalList)) {
                return equipeProfissionalList.get(0).getEquipe();
            }
        }
        return null;
    }


    public Long getNasf(Long valor) {
        List<Long> lstResolvida = Valor.resolveSomatorio(Coalesce.asLong(valor));
        Long totalSomatorio = 0L;
        for (Long dado : lstResolvida) {
            totalSomatorio = new Dinheiro(Coalesce.asLong(totalSomatorio)).somar(((Long) EsusFichaAtendIndividualItem.Nasf.valeuOf(dado).sum()).doubleValue()).longValue();
        }
        return totalSomatorio;
    }

    public Long getProblemaCondicaoAvaliada(List<String> codigoEsusCiapList) {
        Long somatorio = 0L;
        EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada problemaCondicaoAvaliada;
        for(String s : codigoEsusCiapList){
            problemaCondicaoAvaliada = EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.valeuOf(s);

            if(problemaCondicaoAvaliada != null){
                somatorio = new Dinheiro(somatorio).somar(problemaCondicaoAvaliada.sum().doubleValue()).longValue();
            }
        }
        return somatorio;
    }
}