package br.com.ksisolucoes.bo.agendamento.exame;

import br.com.ksisolucoes.bo.exame.interfaces.dto.SolicitacaoExameDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import java.util.Arrays;

/**
 *
 * <AUTHOR>
 */
public class ConsultaExamesAdicionadosAtendimento {

    public static void initHqlLacen(HQLHelper hql, Atendimento atendimento) {

        hql.addToSelect("exame", new HQLProperties(Exame.class, "exame").getSingleProperties());
        hql.addToSelect("exame.codigo", "exame.codigo");
        hql.addToSelect("classificacaoDeRisco.codigo", "exame.classificacaoDeRisco.codigo");
        hql.addToSelect("classificacaoDeRisco.nivelGravidade", "exame.classificacaoDeRisco.nivelGravidade");

        hql.addToSelect("profissional.codigo", "exame.profissional.codigo");
        hql.addToSelect("profissional.nome", "exame.profissional.nome");

        hql.addToSelect("cid.codigo", "exame.cid.codigo");
        hql.addToSelect("cid.descricao", "exame.cid.descricao");

        hql.addToSelect("documentoAssinado.codigo", "exame.documentoAssinado.codigo");
        hql.addToSelect("documentoAssinado.linkRetorno", "exame.documentoAssinado.linkRetorno");
        hql.addToSelect("documentoAssinado.dataLinkRetorno", "exame.documentoAssinado.dataLinkRetorno");
        hql.addToSelect("documentoAssinado.flagAssinado", "exame.documentoAssinado.flagAssinado");
        hql.addToSelect("documentoAssinado.gerenciadorArquivoAssinado.nomeArquivo", "exame.documentoAssinado.gerenciadorArquivoAssinado.nomeArquivo");
        hql.addToSelect("documentoAssinado.gerenciadorArquivoAssinado.caminho", "exame.documentoAssinado.gerenciadorArquivoAssinado.caminho");

        hql.addToSelect("tipoExame.codigo", "exame.tipoExame.codigo");
        hql.addToSelect("tipoExame.descricao", "exame.tipoExame.descricao");
        hql.addToSelect("tipoExame.quantidadeExameRequisicao", "exame.tipoExame.quantidadeExameRequisicao");
        hql.addToSelect("tipoExame.convenio", "exame.tipoExame.convenio");
        hql.addToSelect("tipoExame.agendado", "exame.tipoExame.agendado");
        hql.addToSelect("tipoExame.classificacao", "exame.tipoExame.classificacao");
        hql.addToSelect("tipoExame.tipo", "exame.tipoExame.tipo");
        hql.addToSelect("tipoExame.flagImprimirSemAutorizacao", "exame.tipoExame.flagImprimirSemAutorizacao");

        hql.addToSelect("tipoProcedimento.codigo", "exame.tipoExame.tipoProcedimento.codigo");
        hql.addToSelect("tipoProcedimento.regulado", "exame.tipoExame.tipoProcedimento.regulado");

        hql.addToSelect("exameProcedimentoPadrao.codigo", "exame.tipoExame.exameProcedimentoPadrao.codigo");
        hql.addToSelect("exameProcedimentoPadrao.descricaoProcedimento", "exame.tipoExame.exameProcedimentoPadrao.descricaoProcedimento");

        hql.addToSelect("atendimento.codigo", "exame.atendimento.codigo");
        hql.addToSelect("atendimento.empresa.codigo", "exame.atendimento.empresa.codigo");
        hql.addToSelect("atendimento.empresa.referencia", "exame.atendimento.empresa.referencia");
        hql.addToSelect("atendimento.empresa.descricao", "exame.atendimento.empresa.descricao");
        hql.addToSelect("atendimento.empresa.telefone", "exame.atendimento.empresa.telefone");

        hql.addToSelect("atendimento.profissional.codigo", "exame.atendimento.profissional.codigo");
        hql.addToSelect("atendimento.profissional.nome", "exame.atendimento.profissional.nome");

        hql.addToSelect("atendimento.usuarioCadsus.codigo", "exame.atendimento.usuarioCadsus.codigo");
        hql.addToSelect("atendimento.usuarioCadsus.nome", "exame.atendimento.usuarioCadsus.nome");

        hql.addToSelect("case when exists(select 1 from TipoExameUnidade tipoExameUnidade where tipoExameUnidade.empresa = atendimento.empresa and tipoExameUnidade.tipoExame = tipoExame) then 'S' else 'N' end", "existeAprovacaoTipoExame");

        hql.addToSelect("(select sa.dataAgendamento from IMTableSolicitacaoAgendamentoToExamePadrao im left join im.exame e left join im.solicitacaoAgendamento sa where e.codigo = exame.codigo)", "dataAgendamento");
        hql.addToSelect("(select sa.dataAgendamento from IMTableSolicitacaoAgendamentoToExameBpai im left join im.exameBpai eb left join eb.exame e left join im.solicitacaoAgendamento sa where e.codigo = exame.codigo)", "dataAgendamento");

        hql.setTypeSelect(SolicitacaoExameDTO.class.getName());
        hql.addToFrom("Exame exame"
                + " left join exame.tipoExame tipoExame"
                + " left join exame.profissional profissional"
                + " left join tipoExame.exameProcedimentoPadrao exameProcedimentoPadrao"
                + " left join tipoExame.tipoProcedimento tipoProcedimento"
                + " left join exame.classificacaoDeRisco classificacaoDeRisco"
                + " left join exame.documentoAssinado documentoAssinado"
                + " left join exame.cid cid"
                + " left join documentoAssinado.gerenciadorArquivoAssinado gerenciadorArquivoAssinado"
                + " left join exame.atendimento atendimento ");

        hql.addToWhereWhithAnd("exame.status in ", Arrays.asList(Exame.STATUS_SOLICITADO, Exame.STATUS_AUTORIZADO));
        hql.addToWhereWhithAnd("tipoExame.tipo <> ", TipoExame.Tipo.LACEN.value());
        hql.addToWhereWhithAnd("atendimento = ", atendimento);

        hql.addToWhereWhithAnd(" not exists ( select 1 from ExameApac exameApac where exameApac.exame = exame)");
        hql.addToWhereWhithAnd("exame.origem = ", Exame.Origem.SOLICITACAO_EXAME.value());

        hql.addToOrder("exame.codigo desc");
    }

}
