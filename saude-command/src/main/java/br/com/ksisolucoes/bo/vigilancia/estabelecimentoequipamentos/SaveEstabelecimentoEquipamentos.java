package br.com.ksisolucoes.bo.vigilancia.estabelecimentoequipamentos;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.EstabelecimentoEquipamentos;

/**
 * Created by maicon on 25/05/16.
 */
public class SaveEstabelecimentoEquipamentos extends SaveVO<EstabelecimentoEquipamentos> {

    public SaveEstabelecimentoEquipamentos(EstabelecimentoEquipamentos vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getDataCadastro() == null) {
            this.vo.setDataCadastro(DataUtil.getDataAtual());
        }
        if (this.vo.getUsuario() == null) {
            this.vo.setUsuario(getSessao().<Usuario>getUsuario());
        }
    }
}
