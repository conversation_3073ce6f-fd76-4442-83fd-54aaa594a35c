package br.com.ksisolucoes.bo.tfd;

import br.com.ksisolucoes.agendamento.exame.dto.AgendarSolicitacaoForaRedeDTO;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.encaminhamento.dto.RegistrarRetornoRegionalDTO;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.bo.tfd.interfaces.facade.TfdFacade;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.TipoOcorrencia;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd;

/**
 *
 * <AUTHOR>
 */
public class RegistrarRetornoRegional extends AbstractCommandTransaction {

    private RegistrarRetornoRegionalDTO dto;

    public RegistrarRetornoRegional(RegistrarRetornoRegionalDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (dto.getParecer() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("rotulo_informe_parecer"));
        }
        if (dto.getCodigoPedido() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_pedido"));
        }
        if (dto.getDataParecer() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_data_parecer"));
        }

        PedidoTfd pedidoTfd = HibernateUtil.rechargeVO(PedidoTfd.class, this.dto.getCodigoPedido(), this.dto.getVersionPedido());

        pedidoTfd.setParecer(dto.getParecer().value());
        pedidoTfd.setDataParecerRegional(Data.getDataAtual());
        pedidoTfd.setJustificativaParecer(dto.getJustificativa());
        pedidoTfd.setDataRetornoRegional(Data.getDataAtual());

        if (PedidoTfd.Parecer.AUTORIZADO.equals(dto.getParecer())) {
            if (dto.getUnidadeAgendamento() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_unidade_agendamento"));
            }
            if (dto.getDataAgendamento() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_data_agendamento"));
            }
            String parametroAgendarRetroativo = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("agendarRetroativo");
            if (RepositoryComponentDefault.NAO.equals(parametroAgendarRetroativo)) {
                if (dto.getDataAgendamento().before(Data.getDataAtual())) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_data_agendamento_maior_atual"));
                }
            }
            registrarAutorizado(pedidoTfd);
        } else if (PedidoTfd.Parecer.NEGADO.equals(dto.getParecer())) {
            if (Coalesce.asString(dto.getJustificativa()).isEmpty()) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_justificativa"));
            }
            registrarNegado(pedidoTfd);
        } else if (PedidoTfd.Parecer.CANCELAMENTO_PACIENTE.equals(dto.getParecer())) {
            if (Coalesce.asString(dto.getJustificativa()).isEmpty()) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_justificativa"));
            }
            cancelamentoPaciente(pedidoTfd);
        } else if (PedidoTfd.Parecer.PENDENTE_REGULACAO.equals(dto.getParecer())) {
            if (Coalesce.asString(dto.getJustificativa()).isEmpty()) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_justificativa"));
            }
            registrarPendenteRegulacao(pedidoTfd);
        } else {
            if (Coalesce.asString(dto.getJustificativa()).isEmpty()) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_justificativa"));
            }
            registrarInconclusivo(pedidoTfd);
        }

        BOFactory.getBO(UsuarioCadsusFacade.class).gerarOcorrenciaUsuarioCadsus(pedidoTfd.getLaudoTfd().getUsuarioCadsus(), TipoOcorrencia.TIPO_TFD, "Retorno Regional - Parecer: " + dto.getParecer().toString() + " " + Coalesce.asString(dto.getJustificativa()), pedidoTfd.getLaudoTfd());
    }

    private void registrarInconclusivo(PedidoTfd pedidoTfd) throws DAOException, ValidacaoException {
        pedidoTfd.getLaudoTfd().setStatus(LaudoTfd.StatusLaudoTfd.INCONCLUSIVO.value());
//        pedidoTfd.setDataImpressaoPedido(null);

        BOFactory.getBO(AgendamentoFacade.class).cancelarSolicitacaoAgendamento(pedidoTfd.getSolicitacaoAgendamento().getCodigo(), "Parecer do TFD Inconclusivo: " + dto.getJustificativa(), true);

//        BOFactory.getBO(CadastroFacade.class).save(pedidoTfd);
        BOFactory.getBO(CadastroFacade.class).save(pedidoTfd.getLaudoTfd());
    }

    private void registrarNegado(PedidoTfd pedidoTfd) throws DAOException, ValidacaoException {
        pedidoTfd.getLaudoTfd().setStatus(LaudoTfd.StatusLaudoTfd.NEGADO.value());

        BOFactory.getBO(AgendamentoFacade.class).negarSolicitacaoAgendamento(pedidoTfd.getSolicitacaoAgendamento().getCodigo(), dto.getJustificativa());

        BOFactory.getBO(CadastroFacade.class).save(pedidoTfd);
        BOFactory.getBO(CadastroFacade.class).save(pedidoTfd.getLaudoTfd());
    }

    private void registrarAutorizado(PedidoTfd pedidoTfd) throws DAOException, ValidacaoException {
        pedidoTfd.getLaudoTfd().setStatus(LaudoTfd.StatusLaudoTfd.AUTORIZADO.value());

        {
            AgendarSolicitacaoForaRedeDTO agendarSolicitacaoForaRedeDTO = new AgendarSolicitacaoForaRedeDTO();
            agendarSolicitacaoForaRedeDTO.setCodigoSolicitacao(pedidoTfd.getSolicitacaoAgendamento().getCodigo());
            agendarSolicitacaoForaRedeDTO.setUnidadeExecutante(dto.getUnidadeAgendamento());
            agendarSolicitacaoForaRedeDTO.setProfissionalExecutante(dto.getProfissionalAgendamento());
            agendarSolicitacaoForaRedeDTO.setNomeProfissionalExecutante(dto.getNomeProfissionalAgendamento());
            agendarSolicitacaoForaRedeDTO.setDataHora(dto.getDataAgendamento());
            agendarSolicitacaoForaRedeDTO.setDtoDatasRetorno(dto.getDtoDatasRetorno());
            agendarSolicitacaoForaRedeDTO.setContato(dto.getContato());

            BOFactory.getBO(AgendamentoFacade.class).agendarSolicitacaoForaRede(agendarSolicitacaoForaRedeDTO);
        }

        BOFactory.getBO(CadastroFacade.class).save(pedidoTfd);
        BOFactory.getBO(CadastroFacade.class).save(pedidoTfd.getLaudoTfd());

    }

    private void cancelamentoPaciente(PedidoTfd pedidoTfd) throws DAOException, ValidacaoException {
        BOFactory.getBO(CadastroFacade.class).save(pedidoTfd);

        BOFactory.getBO(TfdFacade.class).cancelarTfd(pedidoTfd.getLaudoTfd(), dto.getJustificativa());
    }

    private void registrarPendenteRegulacao(PedidoTfd pedidoTfd) throws DAOException, ValidacaoException {
        BOFactory.getBO(CadastroFacade.class).save(pedidoTfd);
    }
}
