package br.com.ksisolucoes.bo.vigilancia.financeiro;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.vigilancia.interfaces.AutosHelper;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidade;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMulta;
import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoOcorrencia;

/**
 *
 * <AUTHOR>
 */
public class CancelarFinanceiroProcessoAdministrativo extends AbstractCommandTransaction {

    private VigilanciaFinanceiro vigilanciaFinanceiro;
    private ProcessoAdministrativo processoAdministrativo;
    private AutoMulta autoMulta;
    private AutoPenalidade autoPenalidade;
    private boolean viaAPI;

    public CancelarFinanceiroProcessoAdministrativo(VigilanciaFinanceiro vigilanciaFinanceiro, boolean viaAPI) {
        this.vigilanciaFinanceiro = vigilanciaFinanceiro;
        this.autoMulta = vigilanciaFinanceiro.getAutoMulta();
        this.autoPenalidade = vigilanciaFinanceiro.getAutoPenalidade();
        this.viaAPI = viaAPI;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        ProcessoAdministrativo pa = null;
        if(this.autoPenalidade != null) {
            this.autoPenalidade = (AutoPenalidade) getSession().get(AutoPenalidade.class, this.autoPenalidade.getCodigo());
            pa = this.autoPenalidade.getProcessoAdministrativo();
        } else if(this.autoMulta != null) {
            pa = AutosHelper.getProcessoAdministrativo(this.autoMulta);
        }
        if(pa != null) {
            processoAdministrativo = (ProcessoAdministrativo) getSession().get(ProcessoAdministrativo.class, pa.getCodigo());
            processoAdministrativo.setSituacaoFinanceira(ProcessoAdministrativo.SituacaoFinanceira.SEM_FINANCEIRO.value());
            processoAdministrativo = BOFactory.save(processoAdministrativo);

            cadastrarOcorrenciaProcessoAdministrativo(processoAdministrativo);
        }
    }

    private void cadastrarOcorrenciaProcessoAdministrativo(ProcessoAdministrativo processoAdministrativo) throws DAOException, ValidacaoException {
        StringBuilder builderOcorrencia = new StringBuilder();
        builderOcorrencia.append("Cancelamento do registro do boleto");
        builderOcorrencia.append(" (Boleto Nº ");
        builderOcorrencia.append(vigilanciaFinanceiro.getCodigo());
        builderOcorrencia.append(") ");
        if(viaAPI){
            builderOcorrencia.append(" - Reconhecimento automático");
        } else {
            builderOcorrencia.append(" - Via Gestão Financeira");
        }
        builderOcorrencia.append(" Motivo: ");
        builderOcorrencia.append(vigilanciaFinanceiro.getDescricaoMotivoCancelamento());

        ProcessoAdministrativoOcorrencia processoAdministrativoOcorrencia = new ProcessoAdministrativoOcorrencia();
        processoAdministrativoOcorrencia.setProcessoAdministrativo(processoAdministrativo);
        processoAdministrativoOcorrencia.setDataOcorrencia(DataUtil.getDataAtual());
        processoAdministrativoOcorrencia.setDescricao(builderOcorrencia.toString());
        processoAdministrativoOcorrencia.setUsuario(getSessao().getUsuario());
        processoAdministrativoOcorrencia.setTipo(ProcessoAdministrativoOcorrencia.Tipo.CANCELAMENTO_FINANCEIRO.value());
        processoAdministrativoOcorrencia.setDocumento(vigilanciaFinanceiro.getCodigo());
        BOFactory.save(processoAdministrativoOcorrencia);

    }

}