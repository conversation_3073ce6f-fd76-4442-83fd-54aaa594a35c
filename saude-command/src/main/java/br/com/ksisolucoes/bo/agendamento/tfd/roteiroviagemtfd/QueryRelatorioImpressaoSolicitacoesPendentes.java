package br.com.ksisolucoes.bo.agendamento.tfd.roteiroviagemtfd;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.tfd.dto.QueryRelatorioImpressaoSolicitacoesPendentesDTO;
import br.com.ksisolucoes.tfd.dto.RelatorioImpressaoSolicitacoesPendentesDTOParam;
import br.com.ksisolucoes.vo.frota.viagem.solicitacao.SolicitacaoViagem;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioImpressaoSolicitacoesPendentes extends CommandQuery<QueryRelatorioImpressaoSolicitacoesPendentes> implements ITransferDataReport<RelatorioImpressaoSolicitacoesPendentesDTOParam, QueryRelatorioImpressaoSolicitacoesPendentesDTO> {

    private RelatorioImpressaoSolicitacoesPendentesDTOParam param;
    private Collection<QueryRelatorioImpressaoSolicitacoesPendentesDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("solicitacaoViagem.codigo", "solicitacaoViagem.codigo");
        hql.addToSelect("solicitacaoViagem.status", "solicitacaoViagem.status");
        hql.addToSelect("solicitacaoViagem.dataViagem", "solicitacaoViagem.dataViagem");
        hql.addToSelect("solicitacaoViagem.local", "solicitacaoViagem.local");
        hql.addToSelect("solicitacaoViagem.horario", "solicitacaoViagem.horario");
        hql.addToSelect("solicitacaoViagem.observacao", "solicitacaoViagem.observacao");
        
        hql.addToSelect("usuarioCadsus.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "usuarioCadsus.nome");
        
        hql.addToSelect("acompanhante.codigo", "acompanhante.codigo");
        hql.addToSelect("acompanhante.nome", "acompanhante.nome");
        
        hql.addToSelect("cidade.codigo", "cidade.codigo");
        hql.addToSelect("cidade.descricao", "cidade.descricao");
        
        hql.addToSelect("tipoTransporteViagem.codigo", "tipoTransporteViagem.codigo");
        hql.addToSelect("tipoTransporteViagem.descricao", "tipoTransporteViagem.descricao");

        hql.setTypeSelect(QueryRelatorioImpressaoSolicitacoesPendentesDTO.class.getName());
        hql.addToFrom("SolicitacaoViagem solicitacaoViagem"
                + " left join solicitacaoViagem.usuarioCadsus usuarioCadsus "
                + " left join solicitacaoViagem.acompanhante acompanhante "
                + " left join solicitacaoViagem.cidade cidade "
                + " left join solicitacaoViagem.tipoTransporteViagem tipoTransporteViagem "
                + "  ");

        hql.addToWhereWhithAnd("solicitacaoViagem.status = ", SolicitacaoViagem.Status.PENDENTE.value());
        hql.addToWhereWhithAnd("solicitacaoViagem.dataViagem >= ", DataUtil.getDataAtual());

        
        hql.addToOrder("solicitacaoViagem.dataViagem");
        hql.addToOrder("cidade.descricao");
        hql.addToOrder("usuarioCadsus.nome");
        hql.addToOrder("acompanhante.nome");

    }

    @Override
    public void setDTOParam(RelatorioImpressaoSolicitacoesPendentesDTOParam param) {
        this.param = param;
    }

    @Override
    public Collection<QueryRelatorioImpressaoSolicitacoesPendentesDTO> getResult() {
        return this.result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }
}
