package br.com.ksisolucoes.bo.prontuario.basico.atendimento;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.MedicamentoPaciente;

/**
 *
 * <AUTHOR>
 */
public class ReabrirMedicamentoPaciente extends AbstractCommandTransaction{
    
    private MedicamentoPaciente medicamentoPaciente;

    public ReabrirMedicamentoPaciente(MedicamentoPaciente medicamentoPaciente) {
        this.medicamentoPaciente = medicamentoPaciente;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        medicamentoPaciente = HibernateUtil.rechargeVO(MedicamentoPaciente.class, medicamentoPaciente.getCodigo(), medicamentoPaciente.getVersion());
        
        medicamentoPaciente.setStatus(MedicamentoPaciente.Status.ATIVO.value());
        medicamentoPaciente.setUsuarioCancelamento(null);
        medicamentoPaciente.setDataCancelamento(null);
        
        BOFactory.save(medicamentoPaciente);
    }
}