package br.com.ksisolucoes.bo.prontuario.basico.receituario;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.QueryConsultaReceituariosAbertosDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.Receituario;
import java.util.Date;
import java.util.List;
import java.util.Map;

import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;
import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaReceituariosAbertos extends CommandQueryPager<QueryConsultaReceituariosAbertos> {

    private QueryConsultaReceituariosAbertosDTOParam param;
    private String validaSaldo;
    private Date dataValidadePrescricao;

    public QueryConsultaReceituariosAbertos(QueryConsultaReceituariosAbertosDTOParam param) throws DAOException {
        this.param = param;
        validaSaldo = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("ValidaSaldoDispensacaoPrescricao");

        Long validadePrescricaoInterna = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("ValidadePrescricaoInterna");
        if (validadePrescricaoInterna != null) {
            dataValidadePrescricao = Data.adjustRangeHour(Data.removeMinutos(DataUtil.getDataAtual(), (validadePrescricaoInterna.intValue() * 60))).getDataInicial();
        }
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect(new HQLProperties(Receituario.class, "receituario").getProperties());

        hql.addToSelect("receituario.atendimento.convenio.codigo", true);
        hql.addToSelect("receituario.atendimento.convenio.descricao", true);

        hql.addToSelect("receituario.atendimento.naturezaProcuraTipoAtendimento.tipoAtendimento.codigo", true);
        hql.addToSelect("receituario.atendimento.naturezaProcuraTipoAtendimento.tipoAtendimento.dispensacaoPorTurno", true);

        hql.addToSelect("usuarioCadsus.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.dataNascimento", "usuarioCadsus.dataNascimento");

        hql.setTypeSelect(Receituario.class.getName());
        hql.addToFrom("Receituario receituario "
                + "left join receituario.usuarioCadsus usuarioCadsus");

        hql.setConvertToLeftJoin(true);

        hql.addToWhereWhithAnd(hql.getConsultaLiked("receituario.usuarioCadsus.nome", param.getNomePaciente()));
        hql.addToWhereWhithAnd("receituario.codigo =", param.getCodigoBarras());
        hql.addToWhereWhithAnd("usuarioCadsus =", param.getPaciente());
        hql.addToWhereWhithAnd("receituario.empresa =", param.getSetor());
        hql.addToWhereWhithAnd("receituario.atendimento.convenio =", param.getConvenio());
        hql.addToWhereWhithAnd("receituario.tipoReceita.tipoReceita in", param.getTipoReceita());
        if(param.getEmpresaDispensacao() != null) {
            hql.addToWhereWhithAnd("receituario.empresaDispensacao.codigo = ", param.getEmpresaDispensacao().getCodigo());
        } else if(param.isPermissaoVisualizarApenasPrescricaoEstabelecimento()) {
            try {
                Usuario usuario = SessaoAplicacaoImp.getInstance().<br.com.ksisolucoes.vo.controle.Usuario>getUsuario();
                if (!usuario.isNivelAdminOrMaster()) {
                    usuario.setEmpresasUsuario(BOFactory.getBO(UsuarioFacade.class).getEmpresasUsuario(usuario));
                    hql.addToWhereWhithAnd("receituario.empresaDispensacao.codigo in ", usuario.getEmpresasUsuario());
                }
            } catch (SGKException ex) {
                Loggable.log.error(ex);
            }
        }
//        hql.addToWhereWhithAnd("receituario.atendimento.status =", Atendimento.STATUS_FINALIZADO);

        if (RepositoryComponentDefault.SIM.equals(validaSaldo)) {
            hql.addToWhereWhithAnd("receituario.dataReceituario >=", dataValidadePrescricao);
        } else {
            hql.addToWhereWhithAnd("((not exists(select 1 from DispensacaoMedicamento dispensacaoMedicamento where dispensacaoMedicamento.receituario = receituario)) "
                    + "or (receituario.atendimento.naturezaProcuraTipoAtendimento.tipoAtendimento.dispensacaoPorTurno = 1 and receituario.somatorioTurno < 7))");
        }

        hql.addToWhereWhithAnd("receituario.situacao = ", Receituario.Situacao.PRESCRITO.value());
        hql.addToWhereWhithAnd("exists(SELECT 1 FROM ReceituarioItem receituarioItem"
                        + " WHERE receituarioItem.receituario = receituario AND receituarioItem.status <> " + ReceituarioItem.Status.CANCELADO.value()
                        + ")");

        if (StringUtils.trimToNull(param.getPropOrder()) != null) {
            hql.addToOrder("receituario." + param.getPropOrder() + " " + (param.isAscendingOrder() ? "asc" : "desc"));
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result);
    }

}
