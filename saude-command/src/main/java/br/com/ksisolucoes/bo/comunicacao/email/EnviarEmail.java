package br.com.ksisolucoes.bo.comunicacao.email;

import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.EnviarEmailDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Email;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

import javax.ws.rs.core.MediaType;

/**
 * <AUTHOR>
 */
public class EnviarEmail extends AbstractCommandTransaction {

    private EnviarEmailDTO dto;

    public EnviarEmail(EnviarEmailDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (dto != null) {
            try {
                Email.create()
                        .assunto(dto.getAssunto())
                        .para(dto.getEmailDestino())
                        .mensagem(dto.getMensagem())
                        .content(dto.getContent() != null ? dto.getContent() : MediaType.TEXT_HTML)
                        .send();
            } catch (Throwable ex) {
                Loggable.log.warn("Email: " + dto.getEmailDestino() + " - " + ex.getMessage());
                throw new ValidacaoException("Não foi possível enviar o e-mail. Verifique se o e-mail definido está correto e tente novamente.");
            }
        }
    }
}
