package br.com.ksisolucoes.bo.cadsus.cds.esusfichaprocedimentoitem;

import br.com.ksisolucoes.bo.command.DeleteVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaProcedimentoItem;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaProcedimentoItemSigtap;
import java.util.List;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class DeleteEsusFichaProcedimentoItem extends DeleteVO<EsusFichaProcedimentoItem> {

    public DeleteEsusFichaProcedimentoItem(EsusFichaProcedimentoItem vo) {
        super(vo);
    }

    @Override
    protected void antesDelete() throws DAOException, ValidacaoException {
        List<EsusFichaProcedimentoItemSigtap> lstItens = getSession().createCriteria(EsusFichaProcedimentoItemSigtap.class)
                .add(Restrictions.eq(EsusFichaProcedimentoItemSigtap.PROP_ESUS_FICHA_PROCEDIMENTO_ITEM, this.vo))
                .list();
        for (EsusFichaProcedimentoItemSigtap item : lstItens) {
            BOFactory.delete(item);
        }
    }
}
