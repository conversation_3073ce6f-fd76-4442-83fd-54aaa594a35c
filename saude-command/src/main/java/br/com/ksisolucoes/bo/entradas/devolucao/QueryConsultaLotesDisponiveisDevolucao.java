package br.com.ksisolucoes.bo.entradas.devolucao;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.entradas.devolucao.interfaces.dto.QueryConsultaLotesDisponiveisDevolucaoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoItemLote;
import org.hibernate.Query;

import java.util.List;
import java.util.Map;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaLotesDisponiveisDevolucao extends CommandQuery<QueryConsultaLotesDisponiveisDevolucao> {

    private QueryConsultaLotesDisponiveisDevolucaoDTOParam param;
    private QueryType type;
    private List<DispensacaoItemLote> result;
    private Double sum;
    
    public enum QueryType{
        LIST,
        GROUP,
        SUM,
        ;
    }
    
    public QueryConsultaLotesDisponiveisDevolucao(QueryType type, QueryConsultaLotesDisponiveisDevolucaoDTOParam param) {
        this.param = param;
        this.type = type;
    }
    
    @Override
    protected void createQuery(HQLHelper hql) {
        if (isSumType()) {
            hql.addToSelect("sum(coalesce(dispensacaoItemLote.quantidade,0) - coalesce(dispensacaoItemLote.quantidadeDevolvida,0))");
        } else if(type.equals(QueryType.GROUP)) {
            hql.addToSelectAndGroup("grupoEstoque.id.grupo", "grupoEstoque.id.grupo");
            hql.addToSelect("sum(dispensacaoItemLote.quantidade)", "quantidade");
            hql.addToSelect("sum(dispensacaoItemLote.quantidadeDevolvida)", "quantidadeDevolvida");
            
            hql.setTypeSelect(DispensacaoItemLote.class.getName());

        } else {
            DispensacaoItemLote proxy = on(DispensacaoItemLote.class);
            hql.addToSelect("dispensacaoItemLote.codigo", path(proxy.getCodigo()));
            hql.addToSelect("dispensacaoItemLote.quantidade", path(proxy.getQuantidade()));
            hql.addToSelect("dispensacaoItemLote.quantidadeDevolvida", path(proxy.getQuantidadeDevolvida()));
            hql.addToSelect("produto.codigo", path(proxy.getGrupoEstoque().getId().getEstoqueEmpresa().getId().getProduto().getCodigo()));
            hql.addToSelect("produto.descricao", path(proxy.getGrupoEstoque().getId().getEstoqueEmpresa().getId().getProduto().getDescricao()));
            hql.addToSelect("empresa.codigo", path(proxy.getGrupoEstoque().getId().getEstoqueEmpresa().getId().getEmpresa().getCodigo()));
            hql.addToSelect("empresa.descricao", path(proxy.getGrupoEstoque().getId().getEstoqueEmpresa().getId().getEmpresa().getDescricao()));
            hql.addToSelect("grupoEstoque.id.grupo", path(proxy.getGrupoEstoque().getId().getGrupo()));
            hql.addToSelect("grupoEstoque.id.codigoDeposito", path(proxy.getGrupoEstoque().getId().getCodigoDeposito()));
            hql.addToSelect("dispensacaoMedicamentoItem.codigo", path(proxy.getDispensacaoMedicamentoItem().getCodigo()));
            hql.addToSelect("localizacaoEstrutura.codigo", path(proxy.getGrupoEstoque().getId().getLocalizacaoEstrutura().getCodigo()));

            hql.setTypeSelect(DispensacaoItemLote.class.getName());

            hql.addToOrder("dispensacaoMedicamento.dataDispensacao desc");
            hql.addToOrder("dispensacaoMedicamentoItem.dataProximaDispensacao desc");
        }
        
        hql.addToFrom("DispensacaoItemLote dispensacaoItemLote"
                + " left join dispensacaoItemLote.dispensacaoMedicamentoItem dispensacaoMedicamentoItem"
                + " left join dispensacaoMedicamentoItem.dispensacaoMedicamento dispensacaoMedicamento"
                + " left join dispensacaoMedicamentoItem.produto produto"
                + " left join dispensacaoMedicamento.usuarioCadsusDestino usuarioCadsus"
                + " left join dispensacaoItemLote.grupoEstoque grupoEstoque"
                + " left join grupoEstoque.id.localizacaoEstrutura localizacaoEstrutura"
                + " left join grupoEstoque.id.estoqueEmpresa estoqueEmpresa"
                + " left join estoqueEmpresa.id.produto produto"
                + " left join estoqueEmpresa.id.empresa empresa");
        
        hql.addToWhereWhithAnd("coalesce(dispensacaoItemLote.quantidadeDevolvida,0) < coalesce(dispensacaoItemLote.quantidade,0)");
     
        hql.addToWhereWhithAnd("produto = ", param.getProduto());
        hql.addToWhereWhithAnd("usuarioCadsus = ", param.getUsuarioCadsus());
        hql.addToWhereWhithAnd("grupoEstoque.id.grupo = ", param.getLote());
        if (param.getCodigoDispencacaoItemLote() != null){
            hql.addToWhereWhithAnd("dispensacaoItemLote.codigo = ", param.getCodigoDispencacaoItemLote());
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        if (isSumType()) {
            this.sum = (Double) result;
        } else {
            this.result = hql.getBeanList((List<Map<String, Object>>)result, false);
        }
    }

    @Override
    protected Object executeQuery(Query query) {
        if (isSumType()) {
            Number count_ = ((Number) query.uniqueResult());

            if (count_ == null) {
                count_ = 0l;
            }
            return count_.doubleValue();
        }
        return super.executeQuery(query);
    }

    @Override
    public List<DispensacaoItemLote> getResult() {
        return result;
    }

    public Double getSum() {
        return sum;
    }
    
    private boolean isSumType(){
        return type.equals(QueryType.SUM);
    }

}
