package br.com.ksisolucoes.bo.unidadesaude.historicopaciente;

import br.com.celk.view.unidadesaude.atendimento.consulta.consutaHistoricoPaciente.dto.HistoricoPacienteDTO;
import br.com.celk.view.unidadesaude.atendimento.consulta.consutaHistoricoPaciente.dto.HistoricoPacienteDTOParam;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryAgendamentoHistorico extends CommandQuery<QueryAgendamentoHistorico> {

    private HistoricoPacienteDTOParam param;
    private List<HistoricoPacienteDTO> resultado;

    public QueryAgendamentoHistorico(HistoricoPacienteDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.addToSelect("agendaGradeAtendimentoHorario.codigo", "agendaGradeAtendimentoHorario.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.status", "agendaGradeAtendimentoHorario.status");
        hql.addToSelect("agendaGradeAtendimentoHorario.dataAgendamento", "agendaGradeAtendimentoHorario.dataAgendamento");
        hql.addToSelect("localAgendamento.codigo", "agendaGradeAtendimentoHorario.localAgendamento.codigo");
        hql.addToSelect("localAgendamento.descricao", "agendaGradeAtendimentoHorario.localAgendamento.descricao");

        hql.addToSelect("tipoProcedimento.descricao", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.tipoProcedimento.descricao");
        hql.addToSelect("solicitacaoAgendamento.codigo", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.codigo");
        hql.addToSelect("solicitacaoAgendamento.dataSolicitacao", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.dataSolicitacao");
        hql.addToSelect("solicitacaoAgendamento.nomeProfissionalOrigem", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.nomeProfissionalOrigem");
        hql.addToSelect("profissional.nome", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.profissional.nome");
        hql.addToSelect("solicitacaoAgendamento.prioridade", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.prioridade");
        hql.addToSelect("solicitacaoAgendamento.status", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.status");

        hql.setTypeSelect(HistoricoPacienteDTO.class.getName());
        hql.addToFrom("AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario"
                + " left join agendaGradeAtendimentoHorario.solicitacaoAgendamento solicitacaoAgendamento"
                + " left join agendaGradeAtendimentoHorario.tipoProcedimento tipoProcedimento"
                + " left join agendaGradeAtendimentoHorario.profissional profissional"
                + " left join agendaGradeAtendimentoHorario.localAgendamento localAgendamento");

        hql.addToWhereWhithAnd("agendaGradeAtendimentoHorario.usuarioCadsus.codigo = ", this.param.getUsuarioCadsus().getCodigo());
        hql.addToWhereWhithAnd("agendaGradeAtendimentoHorario.tipoProcedimento.flagTfd = ", "N");
        hql.addToWhereWhithAnd("agendaGradeAtendimentoHorario.status <> ", AgendaGradeAtendimentoHorario.STATUS_REMANEJADO);

        if (param.getPropSort().equals("agendaGradeAtendimentoHorario.solicitacaoAgendamento.tipoProcedimento.descricao")) {
            hql.addToOrder("tipoProcedimento.descricao" + (param.isAscending() ? " asc" : " desc"));
        } else if (param.getPropSort().equals("agendaGradeAtendimentoHorario.solicitacaoAgendamento.dataSolicitacao")) {
            hql.addToOrder("solicitacaoAgendamento.dataSolicitacao" + (param.isAscending() ? " asc" : " desc"));
        } else if (param.getPropSort().equals("agendaGradeAtendimentoHorario.solicitacaoAgendamento.profissional.nome")) {
            hql.addToOrder("profissional.nome" + (param.isAscending() ? " asc" : " desc"));
        } else if (param.getPropSort().equals("agendaGradeAtendimentoHorario.solicitacaoAgendamento.prioridade")) {
            hql.addToOrder("solicitacaoAgendamento.prioridade" + (param.isAscending() ? " asc" : " desc"));
        } else if (param.getPropSort().equals("agendaGradeAtendimentoHorario.dataAgendamento")) {
            hql.addToOrder("agendaGradeAtendimentoHorario.dataAgendamento" + (param.isAscending() ? " asc" : " desc"));
        } else {
            hql.addToOrder("agendaGradeAtendimentoHorario.dataAgendamento desc");
        }

        if (param.getStatusAgendamento() != null) {
            hql.addToWhereWhithAnd("agendaGradeAtendimentoHorario.status = ", param.getStatusAgendamento());
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        resultado = hql.getBeanList((List) result);
    }

    public List<HistoricoPacienteDTO> getResultado() {
        if (param.getPropSort().equals("agendaGradeAtendimentoHorario.solicitacaoAgendamento.status")) {
            Collections.sort(resultado, new Comparator<HistoricoPacienteDTO>() {
                @Override
                public int compare(HistoricoPacienteDTO o1, HistoricoPacienteDTO o2) {
                    if (o1.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento() != null && o2.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento() != null) {

                        int aux = o1.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento().getDescricaoSituacao().compareTo(o2.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento().getDescricaoSituacao());
                        if (param.isAscending()) {
                            aux = aux * -1;
                        }
                        if (aux == 0 && o1.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento().getDataSolicitacao() != null && o2.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento().getDataSolicitacao() != null) {
                            return o1.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento().getDataSolicitacao().compareTo(o2.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento().getDataSolicitacao());
                        } else {
                            return aux;
                        }
                    }
                    return 0;
                }
            });
        }
        return resultado;
    }

}
