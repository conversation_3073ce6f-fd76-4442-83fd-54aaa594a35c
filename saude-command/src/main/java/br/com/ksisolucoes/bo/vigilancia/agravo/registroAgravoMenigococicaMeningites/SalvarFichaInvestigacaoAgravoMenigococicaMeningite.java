package br.com.ksisolucoes.bo.vigilancia.agravo.registroAgravoMenigococicaMeningites;

import br.com.celk.vigilancia.dto.FichaInvestigacaoAgravoMenigococicaMeningiteDTO;
import br.com.celk.vigilancia.dto.FichaInvestigacaoAgravoMeningiteDTO;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.agravo.helper.FichaInvestigacaoHelper;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoMenigococicaMeningites;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoMeningite;

public class SalvarFichaInvestigacaoAgravoMenigococicaMeningite extends AbstractCommandTransaction {

    private FichaInvestigacaoAgravoMenigococicaMeningiteDTO fichaDto;

    public SalvarFichaInvestigacaoAgravoMenigococicaMeningite(
            FichaInvestigacaoAgravoMenigococicaMeningiteDTO dto
    ) {
        this.fichaDto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        InvestigacaoAgravoMenigococicaMeningites investigacaoAgravo = fichaDto.getInvestigacaoAgravoMenigococicaMeningites();
        RegistroAgravo registroAgravo = FichaInvestigacaoHelper.getInstance().getRegistroAgravo(fichaDto.getRegistroAgravo().getCodigo());
        investigacaoAgravo.setRegistroAgravo(registroAgravo);
        Profissional profissional = FichaInvestigacaoHelper.getInstance().getProfissional(getSessao(), registroAgravo);
        Empresa empresa = FichaInvestigacaoHelper.getInstance().getEmpresa(getSessao());

        registroAgravo.setProfissionalInvestigacao(profissional);
        registroAgravo.setUnidadeProfissionalInvestigacao(empresa);
        registroAgravo.setDataPrimeirosSintomas(fichaDto.getRegistroAgravo().getDataPrimeirosSintomas());
        registroAgravo.setStatus(RegistroAgravo.Status.EM_INVESTIGACAO.value());
        registroAgravo = FichaInvestigacaoHelper.getInstance().getStatusEncerramentoFicha(fichaDto.isEncerrarFicha(),registroAgravo);

        if (!isTemFichas(investigacaoAgravo, registroAgravo)) {
            BOFactory.save(investigacaoAgravo);
            BOFactory.save(registroAgravo);
        } else {
            throw new ValidacaoException(Bundle.getStringApplication("msg_ja_existe_um_registro_cadastrado_com_o_mesmo_registro_agravo"));
        }
    }

    private boolean isTemFichas(InvestigacaoAgravoMenigococicaMeningites investigacaoAgravo, RegistroAgravo registroAgravo) {
        LoadManager loadManager = LoadManager.getInstance(InvestigacaoAgravoMenigococicaMeningites.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_REGISTRO_AGRAVO, RegistroAgravo.PROP_CODIGO), registroAgravo.getCodigo()));
        if (investigacaoAgravo.getCodigo() != null) {
            loadManager.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_CODIGO), BuilderQueryCustom.QueryParameter.DIFERENTE, investigacaoAgravo.getCodigo()));
        }
        return loadManager.start().exists();
    }
}
