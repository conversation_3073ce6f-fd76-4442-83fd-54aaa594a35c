package br.com.ksisolucoes.bo.vigilancia.autointimacao.autointimacao;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacaoProrogacao;

/**
 *
 * <AUTHOR>
 */
public class SalvarAutoIntimacaoProrogacao extends AbstractCommandTransaction {

    private AutoIntimacao autoIntimacao;

    public SalvarAutoIntimacaoProrogacao(AutoIntimacao autoIntimacao) {
        this.autoIntimacao = autoIntimacao;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        autoIntimacao = BOFactory.save(autoIntimacao);
        
        AutoIntimacaoProrogacao autoIntimacaoProrogacao = new AutoIntimacaoProrogacao();
        
        autoIntimacaoProrogacao.setAutoIntimacao(autoIntimacao);
        autoIntimacaoProrogacao.setDataUsuario(DataUtil.getDataAtual());
        autoIntimacaoProrogacao.setUsuario(sessao.getUsuario());
//        autoIntimacaoProrogacao.setDataProrogada(autoIntimacao.getDataCumprimentoPrazo());
//        autoIntimacaoProrogacao.setPrazo(autoIntimacao.getPrazo());
//        autoIntimacaoProrogacao.setObservacaoPrazo(autoIntimacao.getObservacaoPrazo());
        
        BOFactory.save(autoIntimacaoProrogacao);
        
    }

    public AutoIntimacao getAutoIntimacao() {
        return autoIntimacao;
    }
}
