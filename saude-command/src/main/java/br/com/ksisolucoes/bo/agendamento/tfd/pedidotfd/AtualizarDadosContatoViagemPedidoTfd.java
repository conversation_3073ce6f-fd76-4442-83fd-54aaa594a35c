/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.agendamento.tfd.pedidotfd;

import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd;

/**
 *
 * <AUTHOR>
 */
public class AtualizarDadosContatoViagemPedidoTfd extends AbstractCommandTransaction<AtualizarDadosContatoViagemPedidoTfd> {

    private Long codigoPedidoTfd;

    public AtualizarDadosContatoViagemPedidoTfd(Long codigoPedidoTfd) {
        this.codigoPedidoTfd = codigoPedidoTfd;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        PedidoTfd pedidoTfd = (PedidoTfd) this.getSession().get(PedidoTfd.class, this.codigoPedidoTfd);

        pedidoTfd.setDataContatoViagem(Data.getDataAtual());

        BOFactory.getBO(CadastroFacade.class).save(pedidoTfd);

    }

}
