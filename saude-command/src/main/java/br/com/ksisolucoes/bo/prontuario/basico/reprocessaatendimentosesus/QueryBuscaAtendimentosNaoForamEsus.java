package br.com.ksisolucoes.bo.prontuario.basico.reprocessaatendimentosesus;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.hibernate.type.LongType;

import java.util.List;

public class QueryBuscaAtendimentosNaoForamEsus extends AbstractCommandTransaction {

    private List<Atendimento> atendimentos;

    public QueryBuscaAtendimentosNaoForamEsus() {
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        HQLHelper hqlBuscaAtendimentosNaoForamEsus = this.getHQLHorariosAgenda();
        SQLQuery query = getSessionLeitura().createSQLQuery(hqlBuscaAtendimentosNaoForamEsus.getQuery());
        this.addScalar(query);
        atendimentos = query.list();
    }

    private HQLHelper getHQLHorariosAgenda() {
        HQLHelper hql = new HQLHelper();
        hql.setUseSQL(true);

        hql.addToSelect("atendimento.nr_atendimento", "codigo");
        ReprocessaAtendimentosEsusQueryHelper.addFrom(hql);
        ReprocessaAtendimentosEsusQueryHelper.addWhere(hql);
        return hql;
    }

    private void addScalar(SQLQuery sql) {
        sql.addScalar("codigo", LongType.INSTANCE)
                .setResultTransformer(Transformers.aliasToBean(Atendimento.class));
    }

    public List<Atendimento> getResult() {
        return atendimentos;
    }
}
