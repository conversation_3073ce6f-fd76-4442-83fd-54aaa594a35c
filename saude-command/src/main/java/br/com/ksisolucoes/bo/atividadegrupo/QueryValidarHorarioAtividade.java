/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.atividadegrupo;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import java.util.Arrays;
import java.util.List;
import org.hibernate.Query;

/**
 *
 * <AUTHOR>
 */
public class QueryValidarHorarioAtividade extends CommandQuery<QueryValidarHorarioAtividade> {

    private AtividadeGrupo atividadeGrupo;
    private List<AtividadeGrupo> atividadeGrupoExistente;
    private List<Profissional> lstProfissional;
    private boolean horarioExistente = false;

    public QueryValidarHorarioAtividade(AtividadeGrupo atividadeGrupo, List<Profissional> lstProfissional) {
        this.atividadeGrupo = atividadeGrupo;
        this.lstProfissional = lstProfissional;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("ag.codigo","codigo");
        hql.addToSelect("local.codigo","localAtividadeGrupo.codigo");
        hql.addToSelect("local.descricao","localAtividadeGrupo.descricao");
        hql.addToSelect("prof.codigo","profissional.codigo");
        hql.addToSelect("prof.referencia","profissional.referencia");
        hql.addToSelect("prof.nome","profissional.nome");

        hql.setTypeSelect(AtividadeGrupo.class.getName());

        hql.addToFrom("AtividadeGrupo ag "
                + " left join ag.localAtividadeGrupo local"
                + " left join ag.profissional prof");

//        hql.addToWhereWhithAnd("ag.codigo <> :codigo");
//        hql.addToWhereWhithAnd("ag.situacao in (:situacao)");
//        hql.addToWhereWhithAnd("(ag.localAtividadeGrupo = :local or ag.profissional = :profissional )");

        String where = ""
                + " ag.codigo <> :codigo"
                + " and "
                + " ag.situacao in (:situacao)"
                + " and "
                + " (local = :local or prof in (:profissional) )"
                + " and ("
                + " (ag.dataInicio < :inicio and ag.dataFim > :fim)"
                + " or "
                + " (ag.dataInicio < :inicio and ag.dataFim > :inicio)"
                + " or "
                + " (ag.dataInicio < :fim and ag.dataFim > :fim)"
                + " or "
                + " (ag.dataInicio > :inicio and ag.dataFim < :fim) )";

        hql.addToWhereWhithAnd(where);
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setParameter("codigo", atividadeGrupo.getCodigo());
        //query.setParameterList("situacao", Arrays.asList(AtividadeGrupo.SITUACAO_PENDENTE,AtividadeGrupo.SITUACAO_APROVADA));
        query.setParameter("local", atividadeGrupo.getLocalAtividadeGrupo());
        query.setParameter("profissional", lstProfissional);
        query.setParameter("inicio", atividadeGrupo.getDataInicio());
        query.setParameter("fim", atividadeGrupo.getDataFim());
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        atividadeGrupoExistente = hql.getBeanList((List) result);
        if(CollectionUtils.isNotNullEmpty(atividadeGrupoExistente)){
            horarioExistente  = true;
        }
    }

    public AtividadeGrupo getAgendaExistente() {
        return atividadeGrupoExistente.get(0);
    }

    public boolean isHorarioExistente() {
        return horarioExistente;
    }
}
