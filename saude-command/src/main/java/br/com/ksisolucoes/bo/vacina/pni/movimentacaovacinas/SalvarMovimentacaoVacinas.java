package br.com.ksisolucoes.bo.vacina.pni.movimentacaovacinas;

import br.com.celk.vacinas.bo.dto.MovimentacaoVacinasDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vacina.pni.MovimentacaoVacinas;
import br.com.ksisolucoes.vo.vacina.pni.MovimentacaoVacinasItem;
import ch.lambdaj.Lambda;
import org.hamcrest.Matchers;

import java.util.List;

/**
 * Created by laudecir on 10/07/17.
 */
public class SalvarMovimentacaoVacinas extends AbstractCommandTransaction {

    private MovimentacaoVacinas movimentacaoVacinas;
    private List<MovimentacaoVacinasItem> itens;

    public SalvarMovimentacaoVacinas(MovimentacaoVacinasDTO dto) {
        this.movimentacaoVacinas = dto.getMovimentacaoVacinas();
        this.itens = dto.getItens();
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        boolean existsItemPendente = Lambda.exists(itens, Lambda.having(Lambda.on(MovimentacaoVacinasItem.class).getStatus(), Matchers.equalTo(MovimentacaoVacinasItem.Status.PENDENTE.value())));
        if (!existsItemPendente) {
            movimentacaoVacinas.setStatus(MovimentacaoVacinas.Status.CONCLUIDO.value());
        }

        BOFactory.save(movimentacaoVacinas);
        salvarItens();
    }

    private void salvarItens() throws ValidacaoException, DAOException {
        for (MovimentacaoVacinasItem item : itens) {
            item.setMovimentacaoVacinas(movimentacaoVacinas);
            BOFactory.save(item);
        }
    }

    public MovimentacaoVacinas getMovimentacaoVacinas() {
        return movimentacaoVacinas;
    }
}
