/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.cadsus.usuariocadsusdocumento;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDocumento;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ExcluirUsuarioCadsusDocumento extends AbstractCommandTransaction {

    private UsuarioCadsus usuarioCadsus;

    public ExcluirUsuarioCadsusDocumento(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        List<UsuarioCadsusDocumento> lista = LoadManager.getInstance(UsuarioCadsusDocumento.class)
                .setLazyMode(true)
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusDocumento.PROP_USUARIO_CADSUS), this.usuarioCadsus))
                .start().getList();

        for (UsuarioCadsusDocumento usuarioCadsusDocumento : lista) {
            BOFactory.delete(usuarioCadsusDocumento);
        }
    }

}
