/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.cadsus.profissional;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Equipe;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;

import java.util.List;
import java.util.Map;

public class QueryConsultaEquipeFaturamentoProfissional extends CommandQuery {

    private List<EquipeProfissional> equipeProfissionalList;
    private final Profissional profissional;
    private final Empresa empresaLogado;
    private final Empresa empresaFaturamento;
    private final Atendimento atendimento;

    public QueryConsultaEquipeFaturamentoProfissional(Profissional profissional, Empresa empresaLogado, Empresa empresaFaturamento, Atendimento atendimento) {
        this.profissional = profissional;
        this.empresaLogado = empresaLogado;
        this.empresaFaturamento = empresaFaturamento;
        this.atendimento = atendimento;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(EquipeProfissional.class.getName());

        hql.addToSelect("ep.codigo", "codigo");
        hql.addToSelect("eq.codigo", "equipe.codigo");
        hql.addToSelect("eq.referencia", "equipe.referencia");
        hql.addToSelect("eq.equipeCnes", "equipe.equipeCnes");
        hql.addToSelect("eq.empresa", "equipe.empresa");

        hql.addToFrom("EquipeProfissional ep " +
                "inner join ep.profissional p " +
                "inner join ep.equipe eq");

        hql.addToFrom("UsuarioEmpresa ue");
        hql.addToFrom("Usuario u");

        hql.addToWhereWhithAnd("(ue.equipeFaturamento.codigo is null or ue.equipeFaturamento.codigo = eq.codigo)");
        hql.addToWhereWhithAnd("ue.usuario.codigo = u.codigo");
        hql.addToWhereWhithAnd("u.profissional.codigo = p.codigo");

        hql.addToWhereWhithAnd("ue.empresa.codigo = ", empresaLogado.getCodigo());
        hql.addToWhereWhithAnd("p.codigo = ", profissional.getCodigo());
        hql.addToWhereWhithAnd("ep.status = ", EquipeProfissional.STATUS_ATIVO);
        hql.addToWhereWhithAnd("eq.ativo = ", RepositoryComponentDefault.SIM);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.equipeProfissionalList = hql.getBeanList((List<Map<String, Object>>) result);
    }

    public Equipe getEquipe() {
        if (br.com.celk.util.CollectionUtils.isNotNullEmpty(this.equipeProfissionalList)) {
            for (EquipeProfissional equipeList : equipeProfissionalList) {
                if (empresaFaturamento != null) {
                    if (empresaFaturamento.equals(equipeList.getEquipe().getEmpresa())) {
                        return equipeList.getEquipe();
                    }

                } else {
                    if (atendimento.getEquipe() != null && atendimento.getEquipe().getEquipeCnes() != null
                    && equipeList.getEquipe() != null && equipeList.getEquipe().getEquipeCnes() != null &&
                            atendimento.getEquipe().getEquipeCnes().equals(equipeList.getEquipe().getEquipeCnes())) {
                        return equipeList.getEquipe();
                    }
                }
            }
            return null;
        }
        return null;
    }
}
