package br.com.ksisolucoes.bo.cadsus.usuariocadsusexclusao;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Receituario;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;

/**
 *
 * <AUTHOR>
 */
public class ReabrirReceituarioExclusaoPaciente extends AbstractCommandTransaction<ReabrirReceituarioExclusaoPaciente> {

    private final ReceituarioItem receituarioItem;

    public ReabrirReceituarioExclusaoPaciente(ReceituarioItem receituarioItem) {
        this.receituarioItem = receituarioItem;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        Receituario receituario = (Receituario) getSession().get(Receituario.class, receituarioItem.getReceituario().getCodigo());
        
        if(receituario != null && !Receituario.Situacao.PRESCRITO.value().equals(receituario.getSituacao())){
            receituario.setSituacao(Receituario.Situacao.PRESCRITO.value());
            BOFactory.save(receituario);
        }

        receituarioItem.setStatus(ReceituarioItem.Status.NORMAL.value());
        receituarioItem.setDataCancelamento(null);
        receituarioItem.setUsuarioCancelamento(null);
        BOFactory.save(receituarioItem);
    }
}
