package br.com.ksisolucoes.bo.prontuario.basico.atendimento;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;

/**
 *
 * <AUTHOR>
 */
public class TrocarProfissionalResponsavel extends AbstractCommandTransaction {

    private Long codigoAih;
    private Profissional profissional;

    public TrocarProfissionalResponsavel(Long codigoAih, Profissional profissional) {
        this.codigoAih = codigoAih;
        this.profissional = profissional;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (profissional == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_medico_obrigatorio"));
        }
        if (profissional.getCodigoCns() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_profissional_responsavel_sem_cns"));
        }

        Aih aih = (Aih) getSession().get(Aih.class, codigoAih);
        aih.setProfissionalResponsavel(profissional);
        BOFactory.save(aih);
    }
}
