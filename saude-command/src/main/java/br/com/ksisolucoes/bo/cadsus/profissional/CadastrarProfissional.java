package br.com.ksisolucoes.bo.cadsus.profissional;

import br.com.celk.pharos.PharosHelper;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.vigilancia.fiscalnarua.ProfissionalDTO;
import br.com.celk.vigilancia.fiscalnarua.SqsSincronizadorFiscalRuaDto;
import br.com.celk.vigilancia.fiscalnarua.TipoMensagemSqsFiscalRua;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.dto.UsuarioDTO;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.ProfissionalCargaHoraria;
import br.com.ksisolucoes.vo.cadsus.ProfissionalHistorico;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.controle.web.UsuarioGrupo;
import br.com.ksisolucoes.vo.hospital.tiss.EloTissProfissionalConvenio;
import br.com.ksisolucoes.vo.vigilancia.pharos.PharosIntegracaoRegistro;
import org.apache.commons.lang.StringUtils;
import org.hibernate.criterion.Restrictions;

import java.util.List;

import static ch.lambdaj.Lambda.forEach;

/**
 * <AUTHOR>
 */
public class CadastrarProfissional extends AbstractCommandTransaction {

    private Profissional profissionalBeforeSave;
    private Profissional profissionalSaved;
    private Profissional profissional;
    private List<ProfissionalCargaHoraria> vinculosAtivos;
    private List<EloTissProfissionalConvenio> elosTiss;

    public CadastrarProfissional(Profissional profissional, List<ProfissionalCargaHoraria> vinculosAtivos, List<EloTissProfissionalConvenio> elosTiss) {
        this.profissional = profissional;
        this.vinculosAtivos = vinculosAtivos;
        this.elosTiss = elosTiss;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (profissional.getCodigo() != null) {
            profissionalBeforeSave =
                    LoadManager.getInstance(Profissional.class)
                            .addProperties(new HQLProperties(Profissional.class).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(Profissional.PROP_CODIGO, profissional.getCodigo()))
                            .setMaxResults(1).start().getVO();
        }

        profissionalSaved = BOFactory.save(profissional);

        vincularProfissionalCargaHoraria();
        vincularUsuarioProfissional();
        vincularProfissionalTISS();

        PharosHelper.integrarReintegrarPharos(profissionalSaved.getCodigo(), PharosIntegracaoRegistro.TipoIntegracao.PESSOAL.value());

        if (integrarFiscalNaRua()) {
            BOFactory.getBO(VigilanciaFacade.class).enviarMensagemSqsSincronizadorFiscalRua(new SqsSincronizadorFiscalRuaDto(
                    new ProfissionalDTO(profissionalSaved)), TipoMensagemSqsFiscalRua.PROFISSIONAL);
        }
    }

    private boolean integrarFiscalNaRua() {
        // se for um cadastro
        if (profissionalBeforeSave == null) {
            return true;
        }

        return
                ((StringUtils.isNotEmpty(profissionalBeforeSave.getNome()) && StringUtils.isNotEmpty(profissionalSaved.getNome())) && !profissionalBeforeSave.getNome().equalsIgnoreCase(profissionalSaved.getNome())) ||
                        ((StringUtils.isNotEmpty(profissionalBeforeSave.getReferencia()) && StringUtils.isNotEmpty(profissionalSaved.getReferencia())) && !profissionalBeforeSave.getReferencia().equalsIgnoreCase(profissionalSaved.getReferencia())) ||
                        ((StringUtils.isNotEmpty(profissionalBeforeSave.getNumeroRegistro()) && StringUtils.isNotEmpty(profissionalSaved.getNumeroRegistro())) && !profissionalBeforeSave.getNumeroRegistro().equalsIgnoreCase(profissionalSaved.getNumeroRegistro()))
                ;
    }

    private void gerarProfissionalHistorico(ProfissionalCargaHoraria pch, boolean delecao) throws DAOException, ValidacaoException {
        ProfissionalHistorico ph = new ProfissionalHistorico();
        ph.setEmpresa(pch.getEmpresa());
        ph.setProfissional(pch.getProfissional());
        ph.setCompetenciaFim(pch.getCompetenciaFim());
        ph.setCompetenciaInicio(pch.getCompetenciaInicio());
        ph.setDataDesligamento(pch.getDataDesativacao());
        ph.setDataEntrada(pch.getDataAtivacao());
        ph.setMotivoDesligamento(pch.getMotivoDesligamento());
        ph.setTabelaCbo(pch.getTabelaCbo());
        ph.setTipoSusNaoSus(pch.getTipoSusNaoSus());
        ph.setVinculacaoSubTipo(pch.getVinculacaoSubTipo());
        if (delecao) {
            ph.setDataExclusao(DataUtil.getDataAtual());
            ph.setUsuarioCancelamento(getSessao().<Usuario>getUsuario());
        }

        BOFactory.save(ph);
    }

    private List<ProfissionalCargaHoraria> loadCargaHorariaProfissional(Profissional profissional) {
        return LoadManager.getInstance(ProfissionalCargaHoraria.class)
                .addParameter(new QueryCustom.QueryCustomParameter(ProfissionalCargaHoraria.PROP_PROFISSIONAL, profissional))
                .start().getList();
    }

    private Usuario loadUsuario(Profissional profissional) {
        return (Usuario) getSession().createCriteria(Usuario.class)
                .add(Restrictions.eq(Usuario.PROP_PROFISSIONAL, profissional))
                .add(Restrictions.eq(Usuario.PROP_FLAG_USUARIO_TEMPORARIO, RepositoryComponentDefault.NAO_LONG))
                .setMaxResults(1)
                .uniqueResult();
    }

    private List<UsuarioGrupo> loadUsuarioGrupo(Usuario usuario) {
        return getSession().createCriteria(UsuarioGrupo.class)
                .add(Restrictions.eq(UsuarioGrupo.PROP_USUARIO, usuario))
                .list();
    }

    private void vincularUsuarioProfissional() throws DAOException, ValidacaoException {
        Usuario usuario = loadUsuario(profissional);
        if (usuario != null) {
            usuario.setNome(profissional.getNome());

            UsuarioDTO dto = new UsuarioDTO();
            dto.setUsuario(usuario);
            List<UsuarioGrupo> usuarioGrupoList = loadUsuarioGrupo(usuario);
            dto.setGrupos(usuarioGrupoList);

            BOFactory.getBO(UsuarioFacade.class).cadastrarUsuarioProfissional(dto);
        }
    }

    private void vincularProfissionalTISS() throws DAOException, ValidacaoException {
        if (CollectionUtils.isNotNullEmpty(elosTiss)) {
            forEach(elosTiss).setProfissional(profissional);
        }

        VOUtils.persistirListaVosModificados(EloTissProfissionalConvenio.class, elosTiss, new QueryCustom.QueryCustomParameter(EloTissProfissionalConvenio.PROP_PROFISSIONAL, profissional));
    }

    private void vincularProfissionalCargaHoraria() throws DAOException, ValidacaoException {
        List<ProfissionalCargaHoraria> cadastrados = loadCargaHorariaProfissional(profissional);
        cadastrados.removeAll(vinculosAtivos);

        for (ProfissionalCargaHoraria profissionalCargaHoraria : cadastrados) {
            gerarProfissionalHistorico(profissionalCargaHoraria, true);
            BOFactory.delete(profissionalCargaHoraria);
        }

        if (CollectionUtils.isNotNullEmpty(vinculosAtivos)) {
            for (ProfissionalCargaHoraria pch : vinculosAtivos) {
                pch.setProfissional(profissional);
                pch.setNumeroRegistro(profissional.getNumeroRegistro());
                pch.setOrgaoEmissor(profissional.getConselhoClasse());

                BOFactory.save(pch);
                gerarProfissionalHistorico(pch, false);
            }
        }
    }
}
