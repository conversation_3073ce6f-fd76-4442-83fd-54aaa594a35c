package br.com.ksisolucoes.bo.frota.diariobordoveiculo.datapaging;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.frota.interfaces.dto.DiarioBordoVeiculoDTO;
import br.com.ksisolucoes.bo.frota.interfaces.dto.DiarioBordoVeiculoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class DataPagingConsultaDiarioBordoVeiculo extends CommandQueryPager<DataPagingConsultaDiarioBordoVeiculo> {

    private DiarioBordoVeiculoDTOParam param;

    public DataPagingConsultaDiarioBordoVeiculo(DiarioBordoVeiculoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(DiarioBordoVeiculoDTO.class.getName());

        hql.addToSelect("diarioBordoVeiculo.codigo", "diarioBordoVeiculo.codigo");
        hql.addToSelect("diarioBordoVeiculo.dataCadastro", "diarioBordoVeiculo.dataCadastro");
        hql.addToSelect("diarioBordoVeiculo.dataSaida", "diarioBordoVeiculo.dataSaida");
        hql.addToSelect("diarioBordoVeiculo.dataChegada", "diarioBordoVeiculo.dataChegada");
        hql.addToSelect("diarioBordoVeiculo.kmInicial", "diarioBordoVeiculo.kmInicial");
        hql.addToSelect("diarioBordoVeiculo.kmFinal", "diarioBordoVeiculo.kmFinal");
        hql.addToSelect("diarioBordoVeiculo.destino", "diarioBordoVeiculo.destino");
        hql.addToSelect("diarioBordoVeiculo.observacao", "diarioBordoVeiculo.observacao");
        hql.addToSelect("diarioBordoVeiculo.valorDespesaViagem", "diarioBordoVeiculo.valorDespesaViagem");
        hql.addToSelect("diarioBordoVeiculo.valorDespesaVeiculo", "diarioBordoVeiculo.valorDespesaVeiculo");
        hql.addToSelect("diarioBordoVeiculo.dataUsuario", "diarioBordoVeiculo.dataUsuario");
        hql.addToSelect("diarioBordoVeiculo.version", "diarioBordoVeiculo.version");
        
        hql.addToSelect("veiculo.codigo", "diarioBordoVeiculo.veiculo.codigo");
        hql.addToSelect("veiculo.descricao", "diarioBordoVeiculo.veiculo.descricao");

        hql.addToSelect("motorista.codigo", "diarioBordoVeiculo.motorista.codigo");
        hql.addToSelect("motorista.nome", "diarioBordoVeiculo.motorista.nome");
        
        hql.addToSelect("roteiro.codigo", "diarioBordoVeiculo.roteiro.codigo");
        
        hql.addToSelect("usuario.codigo", "diarioBordoVeiculo.usuario.codigo");

        hql.addToFrom("DiarioBordoVeiculo diarioBordoVeiculo"
                + " left join diarioBordoVeiculo.veiculo veiculo"
                + " left join diarioBordoVeiculo.motorista motorista"
                + " left join diarioBordoVeiculo.roteiro roteiro"
                + " left join diarioBordoVeiculo.usuario usuario");

        if(param.getVeiculo() != null){
            hql.addToWhereWhithAnd("veiculo.codigo = ", param.getVeiculo().getCodigo());            
        }
        if(param.getMotorista() != null){
            hql.addToWhereWhithAnd("motorista.codigo = ", param.getMotorista().getCodigo());            
        }
        hql.addToWhereWhithAnd("diarioBordoVeiculo.dataSaida", param.getPeriodo());

        String orderType = Coalesce.asString(param.getTipoOrdenacao(), "asc");
        String orderField = param.getCampoOrdenacao();

        if (StringUtils.trimToNull(param.getCampoOrdenacao()) != null) {
            hql.addToOrder(orderField + " " + orderType);
        } else {
            hql.addToOrder("cast(diarioBordoVeiculo.dataSaida as date)");
            hql.addToOrder("veiculo.descricao");
            hql.addToOrder("extract(hour from diarioBordoVeiculo.dataSaida)");
            hql.addToOrder("extract(minute from diarioBordoVeiculo.dataSaida)");
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
