package br.com.ksisolucoes.bo.agendamento.exame;

import br.com.ksisolucoes.agendamento.exame.dto.angedamentosolictacaolote.ValoresProcedimentosCotaFinanceiraDTO;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorCompetencia;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;

import java.util.Collections;
import java.util.List;

public class ValidarCotaFinanceira {

    private TipoProcedimento tipoProcedimento;
    private Empresa unidadeExecutante;
    private List<ExameProcedimento> procedimentosSolicitacao;
    private ExamePrestadorCompetencia examePrestadorCompetencia;

    public ValidarCotaFinanceira setTipoProcedimento(TipoProcedimento tipoProcedimento) {
        this.tipoProcedimento = tipoProcedimento;
        return this;
    }

    public ValidarCotaFinanceira setUnidadeExecutante(Empresa unidadeExecutante) {
        this.unidadeExecutante = unidadeExecutante;
        return this;
    }

    public ValidarCotaFinanceira setProcedimentosSolicitacao(List<ExameProcedimento> procedimentosSolicitacao) {
        this.procedimentosSolicitacao = procedimentosSolicitacao;
        return this;
    }

    public ValidarCotaFinanceira setExamePrestadorCompetencia(ExamePrestadorCompetencia examePrestadorCompetencia) {
        this.examePrestadorCompetencia = examePrestadorCompetencia;
        return this;
    }

    public boolean possuiCotaFinanceiraTodosProcedimentos() throws ValidacaoException {
        ValoresProcedimentosCotaFinanceiraDTO valores = CalcularValorProcedimentoExame.calcularValorProcedimentos(procedimentosSolicitacao, unidadeExecutante, tipoProcedimento);
        return possuiCotaFinanceiraTodosProcedimentos(valores);
    }

    public boolean possuiCotaFinanceiraUmProcedimento() throws ValidacaoException {
        for (ExameProcedimento procedimento : procedimentosSolicitacao) {
            if (possuiCotaParaExame(procedimento)) {
                return true;
            }
        }
        return false;
    }

    private boolean possuiCotaParaExame(ExameProcedimento procedimento) throws ValidacaoException {
        return possuiCotaParaExame(Collections.singletonList(procedimento));
    }

    public boolean possuiCotaParaExame(List<ExameProcedimento> procedimentos) throws ValidacaoException {
        ValoresProcedimentosCotaFinanceiraDTO valores = CalcularValorProcedimentoExame.calcularValorProcedimentos(procedimentos, unidadeExecutante, tipoProcedimento);
        return possuiCotaFinanceiraTodosProcedimentos(valores);
    }

    private boolean possuiCotaFinanceiraTodosProcedimentos(ValoresProcedimentosCotaFinanceiraDTO valoresProcedimentos) {
        valoresProcedimentos.somarValorUtilizado(examePrestadorCompetencia.getTetoFinanceiroRealizado());

        return examePrestadorCompetencia.getTetoFinanceiro() != null && valoresProcedimentos.getValorTotalUtilizado().doubleValue() <= examePrestadorCompetencia.getTetoFinanceiro();
    }
}
