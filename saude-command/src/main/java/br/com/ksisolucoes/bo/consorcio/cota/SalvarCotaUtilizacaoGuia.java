package br.com.ksisolucoes.bo.consorcio.cota;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.*;
import br.com.ksisolucoes.bo.consorcio.cota.dominio.CotaUtilizacaoGuiasHelper;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SalvarCotaUtilizacaoGuia extends AbstractCommandTransaction {

    private CotaUtilizacaoGuia cotaUtilizacaoGuia;

    public SalvarCotaUtilizacaoGuia(CotaUtilizacaoGuia cotaUtilizacaoGuia) {
        this.cotaUtilizacaoGuia = cotaUtilizacaoGuia;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {

        if (cotaUtilizacaoGuia.getConsorciado() != null) {
            LoadManager loadManager = LoadManager.getInstance(CotaUtilizacaoGuia.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(CotaUtilizacaoGuia.PROP_CONSORCIADO, cotaUtilizacaoGuia.getConsorciado()))
                    .addParameter(new QueryCustom.QueryCustomParameter(CotaUtilizacaoGuia.PROP_MES_ANO, cotaUtilizacaoGuia.getMesAno()));

            if (cotaUtilizacaoGuia.getCodigo() != null) {
                loadManager.addParameter(new QueryCustom.QueryCustomParameter(CotaUtilizacaoGuia.PROP_CODIGO, BuilderQueryCustom.QueryParameter.DIFERENTE, cotaUtilizacaoGuia.getCodigo()));
            }

            List<CotaUtilizacaoGuia> cotaUtilizacaoGuiaList = loadManager.start().getList();
            if (CollectionUtils.isNotNullEmpty(cotaUtilizacaoGuiaList)) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_ja_existe_cota_ativa_periodo"));
            }

        }

        cotaUtilizacaoGuia.setValorUtilizado(CotaUtilizacaoGuiasHelper.getValorUtilizacaoGuiasAtualizado(cotaUtilizacaoGuia));

        cotaUtilizacaoGuia.setUsuario(getSessao().getUsuario());

        if (cotaUtilizacaoGuia.getDataHoraCadastro() == null) {
            cotaUtilizacaoGuia.setDataHoraCadastro(DataUtil.getDataAtual());
        }

        if (cotaUtilizacaoGuia.getMesAno().before(Data.adjustRangeMonth(DataUtil.getDataAtual()).getDataInicial())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_mes_ano_maior_data_atual"));
        }

        BOFactory.save(cotaUtilizacaoGuia);

    }

    public CotaUtilizacaoGuia getCotaUtilizacaoGuia() {
        return cotaUtilizacaoGuia;
    }

    public void salvaValorAtualizado(CotaUtilizacaoGuia cotaUtilizacaoGuia) throws ValidacaoException, DAOException {
        BOFactory.save(cotaUtilizacaoGuia);
    }


}
