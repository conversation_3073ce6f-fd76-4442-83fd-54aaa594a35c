package br.com.ksisolucoes.bo.vigilancia.financeiro;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.vigilancia.interfaces.AutosHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.integracao.boleto.Boleto;
import br.com.ksisolucoes.vo.integracao.boleto.BoletoOcorrencia;
import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidade;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMulta;
import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoOcorrencia;

/**
 *
 * <AUTHOR>
 */
public class EfetuarPagamentoFinanceiroProcessoAdministrativo extends AbstractCommandTransaction {

    private VigilanciaFinanceiro vigilanciaFinanceiro;
    private ProcessoAdministrativo processoAdministrativo;
    private AutoMulta autoMulta;
    private AutoPenalidade autoPenalidade;
    private boolean viaAPI;

    public EfetuarPagamentoFinanceiroProcessoAdministrativo(VigilanciaFinanceiro vigilanciaFinanceiro, boolean viaAPI) {
        this.vigilanciaFinanceiro = vigilanciaFinanceiro;
        this.autoMulta = vigilanciaFinanceiro.getAutoMulta();
        this.autoPenalidade = vigilanciaFinanceiro.getAutoPenalidade();
        this.viaAPI = viaAPI;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        ProcessoAdministrativo pa = null;
        if(this.autoPenalidade != null) {
            this.autoPenalidade = (AutoPenalidade) getSession().get(AutoPenalidade.class, this.autoPenalidade.getCodigo());
            pa = this.autoPenalidade.getProcessoAdministrativo();
        } else if(this.autoMulta != null) {
            pa = AutosHelper.getProcessoAdministrativo(this.autoMulta);
        }
        if(pa != null) {
            processoAdministrativo = (ProcessoAdministrativo) getSession().get(ProcessoAdministrativo.class, pa.getCodigo());
            processoAdministrativo.setSituacaoFinanceira(ProcessoAdministrativo.SituacaoFinanceira.PGTO_EFETUADO.value());
            processoAdministrativo = BOFactory.save(processoAdministrativo);

            cadastrarOcorrenciaPagamentoProcessoAdministrativo(processoAdministrativo);
            BOFactory.getBO(VigilanciaFacade.class).finalizarProcessoAdministrativo(processoAdministrativo, true, null);
            if (vigilanciaFinanceiro.getBoleto() != null && vigilanciaFinanceiro.getBoleto().getCodigo() != null) {
                cadastrarOcorrenciaBoleto(vigilanciaFinanceiro.getBoleto());
            }
        }
    }

    private void cadastrarOcorrenciaPagamentoProcessoAdministrativo(ProcessoAdministrativo processoAdministrativo) throws DAOException, ValidacaoException {
        StringBuilder builderOcorrencia = new StringBuilder();
        if(ProcessoAdministrativo.Tipo.AUTO_MULTA.value().equals(processoAdministrativo.getTipo())) {
            builderOcorrencia.append("Pagamento da Multa efetuado");
        } else {
            builderOcorrencia.append("Pagamento da Penalidade efetuado");
        }
        builderOcorrencia.append(" (Boleto Nº ");
        builderOcorrencia.append(vigilanciaFinanceiro.getCodigo());
        builderOcorrencia.append(") ");
        if(viaAPI){
            builderOcorrencia.append(" - Reconhecimento automático");
        } else {
            builderOcorrencia.append(" - Comprovante anexado");
        }

        ProcessoAdministrativoOcorrencia processoAdministrativoOcorrencia = new ProcessoAdministrativoOcorrencia();
        processoAdministrativoOcorrencia.setProcessoAdministrativo(processoAdministrativo);
        processoAdministrativoOcorrencia.setDataOcorrencia(DataUtil.getDataAtual());
        processoAdministrativoOcorrencia.setDescricao(builderOcorrencia.toString());
        processoAdministrativoOcorrencia.setUsuario(getSessao().getUsuario());
        processoAdministrativoOcorrencia.setTipo(ProcessoAdministrativoOcorrencia.Tipo.PAGAMENTO_FINANCEIRO.value());
        processoAdministrativoOcorrencia.setDocumento(vigilanciaFinanceiro.getCodigo());
        BOFactory.save(processoAdministrativoOcorrencia);

    }

    private void cadastrarOcorrenciaBoleto(Boleto boleto) throws DAOException, ValidacaoException {
        BoletoOcorrencia boletoOcorrencia = new BoletoOcorrencia();

        boletoOcorrencia.setBoleto(boleto);
        boletoOcorrencia.setData(DataUtil.getDataAtual());
        String descricaoOcorrencia = "Pagamento efetuado";
        if(viaAPI) {
            descricaoOcorrencia  = descricaoOcorrencia.concat(" (reconhecimento automático)");
        } else {
            descricaoOcorrencia  = descricaoOcorrencia.concat(" (comprovante de pagamento enviado)");
        }
        boletoOcorrencia.setDescricao(descricaoOcorrencia);
        boletoOcorrencia.setUsuario(getSessao().getUsuario());

        BOFactory.save(boletoOcorrencia);
    }

}