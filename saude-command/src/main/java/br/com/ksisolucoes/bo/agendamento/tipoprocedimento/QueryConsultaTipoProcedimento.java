package br.com.ksisolucoes.bo.agendamento.tipoprocedimento;

import br.com.ksisolucoes.agendamento.dto.QueryConsultaTipoProcedimentoDTOParam;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaTipoProcedimento extends CommandQueryPager<QueryConsultaTipoProcedimento> {

    private QueryConsultaTipoProcedimentoDTOParam param;

    public QueryConsultaTipoProcedimento(QueryConsultaTipoProcedimentoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("tipoProcedimento.codigo", true);
        hql.addToSelect("tipoProcedimento.descricao", true);
        hql.addToSelect("tipoProcedimento.situacao", true);
        hql.addToSelect("tipoProcedimento.flagRequerCid", true);
        hql.addToSelect("tipoProcedimento.flagValidaConsultaReguladaAnterior", true);
        hql.addToSelect("tipoProcedimento.flagCadastrarNaRecepcao", true);
        hql.addToSelect("tipoProcedimentoClassificacao.codigo", "tipoProcedimentoClassificacao.codigo");
        hql.addToSelect("tipoProcedimentoClassificacao.descricao", "tipoProcedimentoClassificacao.descricao");
        hql.addToSelect("procedimento.codigo", "procedimento.codigo");
        hql.addToSelect("procedimento.descricao", "procedimento.descricao");
        hql.addToSelect("tipoExameFiltro.codigo", "tipoExameFiltro.codigo");
        hql.addToSelect("tipoExameFiltro.descricao", "tipoExameFiltro.descricao");

        hql.setTypeSelect(TipoProcedimento.class.getName());
        hql.addToFrom("TipoProcedimento tipoProcedimento"
                + " left join tipoProcedimento.tipoProcedimentoClassificacao tipoProcedimentoClassificacao"
                + " left join tipoProcedimento.tipoExameFiltro tipoExameFiltro"
                + " left join tipoProcedimento.procedimento procedimento");

        hql.addToWhereWhithAnd("tipoProcedimento.codigo = ", param.getCodigo());

        if (!param.isIncluirInativos()){
            hql.addToWhereWhithAnd("tipoProcedimento.situacao = ", TipoProcedimento.Situacao.ATIVO.value());
        }

        if (param.isSomenteVisiveis()) {
            hql.addToWhereWhithAnd("tipoProcedimento.flagVisivel = ", RepositoryComponentDefault.SIM_LONG);
        }

        hql.addToWhereWhithAnd(hql.getConsultaLiked("tipoProcedimento.descricao", param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("tipoProcedimento.codigo || ' ' || tipoProcedimento.descricao", param.getKeyword()));

        if (param.isTfd()) {
            hql.addToWhereWhithAnd("tipoProcedimento.flagTfd = ", RepositoryComponentDefault.SIM);
        } else {
            hql.addToWhereWhithAnd("tipoProcedimento.flagTfd = ", RepositoryComponentDefault.NAO);
        }

        if (param.getClassificacao() != null) {
            hql.addToWhereWhithAnd("tipoProcedimento.tipoProcedimentoClassificacao.codigo = ", param.getClassificacao());
        }

        if (param.getTiposAgendamento() != null && !param.getTiposAgendamento().isEmpty()) {
            hql.addToWhereWhithAnd("tipoProcedimento.tipoAgendamento in ", param.getTiposAgendamento());
        }

        if (param.getPropSort() != null) {
            hql.addToOrder("tipoProcedimento." + param.getPropSort() + " " + (param.isAscending() ? "asc" : "desc"));
        } else {
            hql.addToOrder("tipoProcedimento.descricao");
        }

        if (param.isSomenteAgendamentoAutomatico()) {
            hql.addToWhereWhithAnd("tipoProcedimento.agendaAutomatico = ", RepositoryComponentDefault.SIM);
        }

        if (param.isRegulado()) {
            hql.addToWhereWhithAnd("tipoProcedimento.regulado = ", RepositoryComponentDefault.SIM);
        }

        if (param.getTipoProcedimentoPrincipal() != null) {
            HQLHelper subQuery = hql.getNewInstanceSubQuery();
            subQuery.addToSelect("elo.codigo");
            subQuery.addToFrom("EloTipoProcedimentoRelacionado elo");
            subQuery.addToWhereWhithAnd("elo.tipoProcedimentoPrincipal = ", param.getTipoProcedimentoPrincipal());

            StringBuilder where = new StringBuilder();
            where.append("(not exists(").append(subQuery.getQuery()).append(")");

            subQuery.addToWhereWhithAnd("elo.tipoProcedimentoRelacionado = tipoProcedimento");

            where.append(" OR exists(").append(subQuery.getQuery()).append("))");

            hql.addToWhereWhithAnd(where.toString());
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
