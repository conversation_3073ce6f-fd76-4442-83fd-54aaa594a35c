package br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.node;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.AbstractCommandValidacaoV2;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.annotations.ValidacaoProntuarioNode;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.PrescricaoEnfermagemDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.EvolucaoProntuario;
import br.com.ksisolucoes.vo.prontuario.prescricaoenfermagem.PrescricaoEnfermagemAtendimento;
import java.util.List;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
@ValidacaoProntuarioNode(value = NodesAtendimentoRef.PRESCRICAO_ENFERMAGEM, refClass = PrescricaoEnfermagemDTO.class)
public class ValidarPrescricaoEnfermagemNode extends AbstractCommandValidacaoV2<PrescricaoEnfermagemDTO> {
    
    public ValidarPrescricaoEnfermagemNode(Atendimento atendimento) {
        super(atendimento);
    }

    @Override
    public PrescricaoEnfermagemDTO executarValidacao(PrescricaoEnfermagemDTO object) throws DAOException, ValidacaoException {
        return object;
    }

    @Override
    public void processar(PrescricaoEnfermagemDTO obj) throws DAOException, ValidacaoException {
        List<PrescricaoEnfermagemAtendimento> list = getSession().createCriteria(PrescricaoEnfermagemAtendimento.class)
                .add(Restrictions.eq(VOUtils.montarPath(PrescricaoEnfermagemAtendimento.PROP_ATENDIMENTO), getAtendimento()))
                .add(Restrictions.eq(VOUtils.montarPath(PrescricaoEnfermagemAtendimento.PROP_STATUS), PrescricaoEnfermagemAtendimento.Status.ABERTA.value()))
                .list();

        if (CollectionUtils.isNotNullEmpty(list)) {
            for (PrescricaoEnfermagemAtendimento prescricaoEnfermagemAtendimento : list) {
                salvarEvolucao(prescricaoEnfermagemAtendimento);
                prescricaoEnfermagemAtendimento.setStatus(PrescricaoEnfermagemAtendimento.Status.FECHADA.value());
                BOFactory.save(prescricaoEnfermagemAtendimento);
            }
        }
    }

    private void salvarEvolucao(PrescricaoEnfermagemAtendimento prescricaoEnfermagemAtendimento) throws DAOException, ValidacaoException {
        EvolucaoProntuario evolucao = new EvolucaoProntuario();
        evolucao.setDescricao(prescricaoEnfermagemAtendimento.getEvolucaoPrescricao());
        evolucao.setProfissional(getAtendimento().getProfissional());
        evolucao.setAtendimento(getAtendimento());
        evolucao.setDataHistorico(prescricaoEnfermagemAtendimento.getDataPrescricao());
        evolucao.setDataRegistro(DataUtil.getDataAtual());
        BOFactory.save(evolucao);
    }
}
