/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.entradas.estoque.pedidotransferencia;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QuerySeparacaoPedidoTransferenciaItemDTOParam;
import br.com.ksisolucoes.bo.materiais.pedidotransferencia.DTOPedidoTransferenciaItem;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresaPK;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferencia;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferenciaItem;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.util.List;
import java.util.Map;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class QuerySeparacaoPedidoTransferenciaItem extends CommandQuery<QuerySeparacaoPedidoTransferenciaItem> {

    private QuerySeparacaoPedidoTransferenciaItemDTOParam param;
    private List<DTOPedidoTransferenciaItem> result;

    public QuerySeparacaoPedidoTransferenciaItem(QuerySeparacaoPedidoTransferenciaItemDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("pedidoTransferenciaItem",new HQLProperties(PedidoTransferenciaItem.class,"pedidoTransferenciaItem").getProperties());
        hql.addToSelect("pedidoTransferenciaItem",new HQLProperties(PedidoTransferencia.class,"pedidoTransferenciaItem.pedidoTransferencia").getProperties());
        hql.addToSelect("pedidoTransferenciaItem",new HQLProperties(Produto.class,"pedidoTransferenciaItem.produto").getProperties());
        hql.addToSelect("pedidoTransferenciaItem.produto.subGrupo.id.codigo","pedidoTransferenciaItem.produto.subGrupo.id.codigo");
        hql.addToSelect("pedidoTransferenciaItem.produto.subGrupo.id.codigoGrupoProduto","pedidoTransferenciaItem.produto.subGrupo.id.codigoGrupoProduto");
        hql.addToSelect("pedidoTransferenciaItem.produto.subGrupo.flagControlaGrupoEstoque","pedidoTransferenciaItem.produto.subGrupo.flagControlaGrupoEstoque");
        hql.addToSelect("pedidoTransferenciaItem.produto.unidade.codigo","pedidoTransferenciaItem.produto.unidade.codigo");
        hql.addToSelect("pedidoTransferenciaItem.produto.unidade.unidade","pedidoTransferenciaItem.produto.unidade.unidade");
        hql.addToSelect("((estoqueDepositoView.estoqueFisico - estoqueDepositoView.estoqueVencido) + estoqueDepositoView.estoqueEncomendado - (estoqueDepositoView.estoqueReservado - estoqueDepositoView.estoqueReservadoVencido) )","estoqueDisponivel");

        hql.addToFrom("PedidoTransferenciaItem pedidoTransferenciaItem");
        hql.setTypeSelect(DTOPedidoTransferenciaItem.class.getName());
        hql.setConvertToLeftJoin(true);

        hql.addToFrom("EstoqueDepositoView estoqueDepositoView");

        hql.addToWhereWhithAnd("pedidoTransferenciaItem.pedidoTransferencia.empresaOrigem = estoqueDepositoView.empresa");
        hql.addToWhereWhithAnd("pedidoTransferenciaItem.produto = estoqueDepositoView.produto");
        hql.addToWhereWhithAnd("pedidoTransferenciaItem.pedidoTransferencia.deposito = estoqueDepositoView.deposito");

        hql.addToWhereWhithAnd("pedidoTransferenciaItem.pedidoTransferencia.codigo = ",this.param.getCodigoPeditoTransferencia());
        hql.addToWhereWhithAnd("pedidoTransferenciaItem.status <> ",this.param.getStatus());

    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<DTOPedidoTransferenciaItem> getResult() {
        return result;
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        for (DTOPedidoTransferenciaItem dto : result) {
            dto.setEstoqueEmpresaOrigem(carregarEstoqueEmpresa(dto.getPedidoTransferenciaItem().getProduto(), dto.getPedidoTransferenciaItem().getPedidoTransferencia().getEmpresaOrigem()));
            dto.setEstoqueEmpresaDestino(carregarEstoqueEmpresa(dto.getPedidoTransferenciaItem().getProduto(), dto.getPedidoTransferenciaItem().getPedidoTransferencia().getEmpresaDestino()));
        }
    }
    
    private EstoqueEmpresa carregarEstoqueEmpresa(Produto produto, Empresa empresa) throws DAOException, ValidacaoException {
        return LoadManager.getInstance(EstoqueEmpresa.class)
                .addProperty(VOUtils.montarPath(EstoqueEmpresa.PROP_ESTOQUE_FISICO))
                .addProperty(VOUtils.montarPath(EstoqueEmpresa.PROP_ESTOQUE_RESERVADO))
                .addProperty(VOUtils.montarPath(EstoqueEmpresa.PROP_ESTOQUE_ENCOMENDADO))
                .addProperty(VOUtils.montarPath(EstoqueEmpresa.PROP_ESTOQUE_MINIMO))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO), produto))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA), empresa))
                .start().getVO();
    }

}
