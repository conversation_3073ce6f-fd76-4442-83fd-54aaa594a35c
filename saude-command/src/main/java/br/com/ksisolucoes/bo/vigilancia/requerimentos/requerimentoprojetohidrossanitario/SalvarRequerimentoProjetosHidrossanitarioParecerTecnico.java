package br.com.ksisolucoes.bo.vigilancia.requerimentos.requerimentoprojetohidrossanitario;

import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaFiscal;
import br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.*;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.enums.RequerimentosProjetosEnums;
import ch.lambdaj.Lambda;

import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class SalvarRequerimentoProjetosHidrossanitarioParecerTecnico extends AbstractCommandTransaction<SalvarRequerimentoProjetosHidrossanitarioParecerTecnico> {
    private PnlRequerimentoVigilanciaAnexoDTO pnlRequerimentoVigilanciaAnexoDTO;
    private RequerimentoProjetosHidrossanitarioParecerTecnicoDTO dto;
    private RequerimentoVigilancia requerimentoVigilancia;
    private ConfiguracaoVigilancia configuracaoVigilancia;

    public SalvarRequerimentoProjetosHidrossanitarioParecerTecnico(RequerimentoProjetosHidrossanitarioParecerTecnicoDTO dto) {
        this.dto = dto;
        this.pnlRequerimentoVigilanciaAnexoDTO = dto.getAnexoDTO();
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        RequerimentoVigilanciaCancelamentoFinalizacaoDTO dtoFinalizar = new RequerimentoVigilanciaCancelamentoFinalizacaoDTO();
        RequerimentoProjetoHidrossanitarioParecer parecer = this.dto.getRequerimentoProjetoHidrossanitarioParecer();

        boolean isNew = parecer.getCodigo() == null;

        if (RequerimentoVigilancia.Situacao.DEFERIDO.value().equals(parecer.getStatus()) || RequerimentoVigilancia.Situacao.INDEFERIDO.value().equals(parecer.getStatus())) {
            parecer.getRequerimentoProjetoHidrossanitario().getRequerimentoVigilancia().setSituacao(parecer.getStatus());
            parecer.getRequerimentoProjetoHidrossanitario().getRequerimentoVigilancia().setSituacaoAnaliseProjetos(null);
        } else if (RequerimentoVigilancia.Situacao.ANALISE.value().equals(parecer.getStatus())) {
            parecer.getRequerimentoProjetoHidrossanitario().getRequerimentoVigilancia().setSituacaoAnaliseProjetos(RequerimentoVigilancia.SituacaoAnaliseProjetos.ANALISE.value());
        } else if (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(parecer.getStatus())) {
            parecer.getRequerimentoProjetoHidrossanitario().getRequerimentoVigilancia().setSituacaoAnaliseProjetos(RequerimentoVigilancia.SituacaoAnaliseProjetos.COM_PENDENCIA.value());
        }

        dtoFinalizar.setRequerimentoVigilancia(parecer.getRequerimentoProjetoHidrossanitario().getRequerimentoVigilancia());
        dtoFinalizar.setDataFinalizacao(parecer.getRequerimentoProjetoHidrossanitario().getRequerimentoVigilancia().getDataFinalizacao());
        dtoFinalizar.setSituacao(RequerimentoVigilancia.Situacao.valeuOf(parecer.getRequerimentoProjetoHidrossanitario().getRequerimentoVigilancia().getSituacao()));

        requerimentoVigilancia = BOFactory.getBO(VigilanciaFacade.class).cancelarFinalizarRequerimentoVigilancia(dtoFinalizar);

        carregarConfiguracaoVigilancia();
        if (configuracaoVigilancia == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_existe_configuracao"));
        }
        configurarNumeracaoParecer();
        if (RequerimentoVigilancia.Situacao.DEFERIDO.value().equals(parecer.getStatus())) {
            configurarNumeracaoAprovacao();
            saveAnexosPranchas();
        }
        if (RequerimentoVigilancia.Situacao.INDEFERIDO.value().equals(parecer.getStatus()) && CollectionUtils.isNotNullEmpty(dto.getAnexosPranchaList())) {
            for (AnexoPranchaDTO anexoPranchaDTO : dto.getAnexosPranchaList()) {
                anexoPranchaDTO.getRequerimentoProjetoHidrossanitarioAnexo().setSituacao(RequerimentosProjetosEnums.Status.REPROVADO.value());
            }
            saveAnexosPranchas();
        }
        BOFactory.save(configuracaoVigilancia);

        RequerimentoProjetoHidrossanitario save = BOFactory.save(parecer.getRequerimentoProjetoHidrossanitario());

        parecer.setRequerimentoProjetoHidrossanitario(save);

        parecer.setDataSaida(DataUtil.getDataAtual());
        parecer = BOFactory.save(parecer);

        if (CollectionUtils.isEmpty(dto.getRequerimentoVigilanciaFiscalList())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_pelo_menos_um_fiscal"));
        }

        if (CollectionUtils.isNotNullEmpty(dto.getRequerimentoVigilanciaFiscalList())) {
            for (RequerimentoVigilanciaFiscal requerimentoVigilanciaFiscal : dto.getRequerimentoVigilanciaFiscalList()) {
                RequerimentoVigilanciaFiscal fiscal = LoadManager.getInstance(RequerimentoVigilanciaFiscal.class)
                        .addProperties(new HQLProperties(RequerimentoVigilanciaFiscal.class).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVigilanciaFiscal.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                        .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVigilanciaFiscal.PROP_PROFISSIONAL, requerimentoVigilanciaFiscal.getProfissional()))
                        .setMaxResults(1).start().getVO();

                if (fiscal == null) {
                    fiscal = new RequerimentoVigilanciaFiscal();
                    fiscal.setProfissional(requerimentoVigilanciaFiscal.getProfissional());
                    fiscal.setRequerimentoVigilancia(requerimentoVigilancia);
                    fiscal = BOFactory.save(fiscal);
                }
                RequerimentoProjetoHidrossanitarioParecerFiscal requerimentoProjetoHidrossanitarioParecerFiscal = LoadManager.getInstance(RequerimentoProjetoHidrossanitarioParecerFiscal.class)
                        .addProperties(new HQLProperties(RequerimentoProjetoHidrossanitarioParecerFiscal.class).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoProjetoHidrossanitarioParecerFiscal.PROP_REQUERIMENTO_VIGILANCIA_FISCAL, fiscal))
                        .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoProjetoHidrossanitarioParecerFiscal.PROP_REQUERIMENTO_PROJETO_HIDROSSANITARIO_PARECER, parecer))
                        .setMaxResults(1).start().getVO();
                if (requerimentoProjetoHidrossanitarioParecerFiscal == null) {
                    requerimentoProjetoHidrossanitarioParecerFiscal = new RequerimentoProjetoHidrossanitarioParecerFiscal();
                    requerimentoProjetoHidrossanitarioParecerFiscal.setRequerimentoVigilanciaFiscal(fiscal);
                    requerimentoProjetoHidrossanitarioParecerFiscal.setRequerimentoProjetoHidrossanitarioParecer(parecer);
                    BOFactory.save(requerimentoProjetoHidrossanitarioParecerFiscal);
                }
            }
        }

        if (CollectionUtils.isNotNullEmpty(this.dto.getAtividadesVigilanciaList())) {
            for (AtividadesVigilancia atividadesVigilancia : this.dto.getAtividadesVigilanciaList()) {
                RequerimentoProjetoHidrossanitarioParecerAtividade requerimentoProjetoHidrossanitarioParecerAtividade = new RequerimentoProjetoHidrossanitarioParecerAtividade();
                requerimentoProjetoHidrossanitarioParecerAtividade.setAtividadesVigilancia(atividadesVigilancia);
                requerimentoProjetoHidrossanitarioParecerAtividade.setRequerimentoProjetoHidrossanitarioParecer(parecer);
                BOFactory.save(requerimentoProjetoHidrossanitarioParecerAtividade);
            }

            FaturamentoRequerimentoDTOParam param = new FaturamentoRequerimentoDTOParam();
            param.setEstabelecimento(dto.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getRequerimentoVigilancia().getEstabelecimento());
            param.setVigilanciaPessoa(dto.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getRequerimentoVigilancia().getVigilanciaPessoa());
            if (CollectionUtils.isNotNullEmpty(dto.getRequerimentoVigilanciaFiscalList())) {
                param.setProfissionalList(Lambda.extract(dto.getRequerimentoVigilanciaFiscalList(), on(RequerimentoVigilanciaFiscal.class).getProfissional()));
            }
            param.setAtividadesVigilanciaList(dto.getAtividadesVigilanciaList());
            param.setDataFaturamento(dto.getRequerimentoProjetoHidrossanitarioParecer().getDataParecer());
            param.setObservacao(Bundle.getStringApplication("msg_gerado_tecnico_projeto_hidrossanitario_protocolo_x", requerimentoVigilancia.getProtocoloFormatado()));
            param.setRequerimentoVigilancia(requerimentoVigilancia);
            param.setTipoAtividade(Bundle.getStringApplication("rotulo_projeto_hidrossanitario_parecer_tecnico"));

            BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoRequerimento(param);
        }

        if (pnlRequerimentoVigilanciaAnexoDTO != null) {
            BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaAnexo(requerimentoVigilancia, parecer, pnlRequerimentoVigilanciaAnexoDTO.getRequerimentoVigilanciaAnexoDTOList(), pnlRequerimentoVigilanciaAnexoDTO.getRequerimentoVigilanciaAnexoExcluidoDTOList());
        }

        if (isNew && RequerimentoVigilancia.Situacao.PENDENTE.value().equals(parecer.getStatus())) {
            BOFactory.getBO(VigilanciaFacade.class).enviarEmailCadastroParecerRequerimento(requerimentoVigilancia, parecer.getDescricaoParecer());
        }
    }

    private void saveAnexosPranchas() throws DAOException, ValidacaoException {
        if (CollectionUtils.isNotNullEmpty(dto.getAnexosPranchaList())) {
            for (AnexoPranchaDTO anexoDTO : dto.getAnexosPranchaList()) {
                BOFactory.save(anexoDTO.getRequerimentoProjetoHidrossanitarioAnexo());
            }
        }
    }

    private void configurarNumeracaoParecer() throws ValidacaoException, DAOException {
        if (configuracaoVigilancia.getAnoBaseGeral() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_ano_base_configuracao_vigilancia"));
        }

        long sequencial = 0L;
        if (configuracaoVigilancia != null && Coalesce.asLong(configuracaoVigilancia.getNumeracaoParecerTecnico()) > 0L) {
            sequencial = configuracaoVigilancia.getNumeracaoParecerTecnico();
        }
        sequencial++;
        String montaId = String.valueOf(sequencial).concat(String.valueOf(configuracaoVigilancia.getAnoBaseGeral()));
        Long nextId = Long.valueOf(StringUtil.getDigits(montaId));
        dto.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().setNumeroParecerTecnico(nextId);

    }

    private void configurarNumeracaoAprovacao() throws ValidacaoException, DAOException {
        if (configuracaoVigilancia.getAnoBaseGeral() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_ano_base_configuracao_vigilancia"));
        }

        long sequencial = 0L;
        if (configuracaoVigilancia != null && Coalesce.asLong(configuracaoVigilancia.getNumeroInicialAprovacaoLaudoHidrossanitario()) > 0L) {
            sequencial = configuracaoVigilancia.getNumeroInicialAprovacaoLaudoHidrossanitario();
        }
        sequencial++;
        dto.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().setNumeroAprovacao(sequencial);
        configuracaoVigilancia.setNumeroInicialAprovacaoLaudoHidrossanitario(sequencial);
    }

    private void carregarConfiguracaoVigilancia() throws ValidacaoException, DAOException {
        configuracaoVigilancia = BOFactory.getBO(VigilanciaFacade.class).carregarConfiguracaoVigilancia();
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }

}
