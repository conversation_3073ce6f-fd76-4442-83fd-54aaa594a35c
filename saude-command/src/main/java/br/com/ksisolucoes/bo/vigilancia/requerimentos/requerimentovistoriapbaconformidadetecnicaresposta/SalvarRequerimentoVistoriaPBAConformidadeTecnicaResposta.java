package br.com.ksisolucoes.bo.vigilancia.requerimentos.requerimentovistoriapbaconformidadetecnicaresposta;

import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.PnlRequerimentoVigilanciaAnexoDTO;
import br.com.ksisolucoes.bo.vigilancia.requerimentovigilanciaanexo.SalvarRequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaPBAConformidadeTecnicaResposta;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaProjetoBasicoArquitetura;

/**
 *
 * <AUTHOR>
 */
public class SalvarRequerimentoVistoriaPBAConformidadeTecnicaResposta extends AbstractCommandTransaction<RequerimentoVistoriaPBAConformidadeTecnicaResposta> {
    private boolean enviar;
    private PnlRequerimentoVigilanciaAnexoDTO pnlRequerimentoVigilanciaAnexoDTO;
    private RequerimentoVistoriaPBAConformidadeTecnicaResposta requerimentoVistoriaPBAConformidadeTecnicaResposta;
    private RequerimentoVigilancia requerimentoVigilancia;

    public SalvarRequerimentoVistoriaPBAConformidadeTecnicaResposta(RequerimentoVistoriaPBAConformidadeTecnicaResposta requerimentoVistoriaPBAConformidadeTecnicaResposta, PnlRequerimentoVigilanciaAnexoDTO pnlRequerimentoVigilanciaAnexoDTO) {
        this.requerimentoVistoriaPBAConformidadeTecnicaResposta = requerimentoVistoriaPBAConformidadeTecnicaResposta;
        this.requerimentoVigilancia = requerimentoVistoriaPBAConformidadeTecnicaResposta.getRequerimentoVistoriaPBAConformidadeTecnica().getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia();
        this.pnlRequerimentoVigilanciaAnexoDTO = pnlRequerimentoVigilanciaAnexoDTO;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if(RequerimentoVistoriaPBAConformidadeTecnicaResposta.Situacao.ENVIADO.value().equals(requerimentoVistoriaPBAConformidadeTecnicaResposta.getSituacao())) {
            RequerimentoVistoriaProjetoBasicoArquitetura requerimentoAnaliseProjeto = (RequerimentoVistoriaProjetoBasicoArquitetura) getSession().createCriteria(RequerimentoVistoriaProjetoBasicoArquitetura.class).add(Restrictions.eq(RequerimentoVistoriaProjetoBasicoArquitetura.PROP_CODIGO, requerimentoVistoriaPBAConformidadeTecnicaResposta.getRequerimentoVistoriaPBAConformidadeTecnica().getRequerimentoVistoriaProjetoBasicoArquitetura().getCodigo())).uniqueResult();
            requerimentoVigilancia = requerimentoAnaliseProjeto.getRequerimentoVigilancia();
            requerimentoVigilancia.setSituacaoAnaliseProjetos(RequerimentoVigilancia.SituacaoAnaliseProjetos.RETORNO.value());
            requerimentoVigilancia = BOFactory.save(requerimentoVigilancia);
        }

        BOFactory.save(requerimentoVistoriaPBAConformidadeTecnicaResposta.getRequerimentoVistoriaPBAConformidadeTecnica());

        requerimentoVistoriaPBAConformidadeTecnicaResposta = BOFactory.save(requerimentoVistoriaPBAConformidadeTecnicaResposta);
        SalvarRequerimentoVigilanciaAnexo commandAnexos = new SalvarRequerimentoVigilanciaAnexo(requerimentoVistoriaPBAConformidadeTecnicaResposta, pnlRequerimentoVigilanciaAnexoDTO.getRequerimentoVigilanciaAnexoDTOList(), pnlRequerimentoVigilanciaAnexoDTO.getRequerimentoVigilanciaAnexoExcluidoDTOList());
        commandAnexos.start();
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }
}
