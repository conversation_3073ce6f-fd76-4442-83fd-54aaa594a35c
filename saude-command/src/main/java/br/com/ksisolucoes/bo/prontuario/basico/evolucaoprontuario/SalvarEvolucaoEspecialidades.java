package br.com.ksisolucoes.bo.prontuario.basico.evolucaoprontuario;

import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.prontuario.basico.atendimentocid.SaveAtendimentoCidList;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.EvolucaoEspecialidadesDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ReceituarioFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atendimento.AtendimentoNotificacao;
import br.com.ksisolucoes.vo.basico.AtendimentoCidNotificavel;
import br.com.ksisolucoes.vo.basico.ClassificacaoCids;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusPatologia;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import org.hibernate.Criteria;
import org.hibernate.criterion.Projections;
import org.hibernate.sql.JoinType;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SalvarEvolucaoEspecialidades extends AbstractCommandTransaction {

    private EvolucaoEspecialidadesDTO dto;
    private Atendimento atendimento;
    private boolean lastTemp = false;
    private Profissional profissional;
    private TabelaCbo cboProfissional;
    private UsuarioCadsusDado usuarioCadsusDado;

    public SalvarEvolucaoEspecialidades(Atendimento atendimento, EvolucaoEspecialidadesDTO dto, boolean lastTemp, Profissional profissional, TabelaCbo cboProfissional) {
        this.dto = dto;
        this.atendimento = atendimento;
        this.lastTemp = lastTemp;
        this.profissional = profissional;
        this.cboProfissional = cboProfissional;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (lastTemp) {
            atendimento.setCidPrincipal(dto.getCid());
            atendimento.setDiasRetorno(dto.getDiasRetorno());
            atendimento.setTipoAtendimentoEsus(dto.getTipoAtendimentoEsus());

            atendimento.setLocalAtendimento(dto.getLocalAtendimento());
            atendimento.setCidSecundario(dto.getCidSecundario());

            if (atendimento.getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getClassificacaoAtendimento() != null) {
                atendimento.setClassificacaoAtendimento(atendimento.getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getClassificacaoAtendimento());
            } else {
                List<ClassificacaoAtendimento> classificacaoAtendimentoList = getSession().createCriteria(ClassificacaoAtendimento.class)
                        .add(Restrictions.eq(ClassificacaoAtendimento.PROP_CLASSIFICACAO_ESUS, ClassificacaoAtendimento.ClassificacaoEsus.OUTROS.value()))
                        .list();

                if (CollectionUtils.isNotNullEmpty(classificacaoAtendimentoList)) {
                    atendimento.setClassificacaoAtendimento(classificacaoAtendimentoList.get(0));
                } else {
                    throw new ValidacaoException(Bundle.getStringApplication("rotulo_tipo_atendimento_nao_possui_classificacao_atendimento", atendimento.getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDescricaoFormatado()));
                }
            }

            BOFactory.getBO(CadastroFacade.class).save(atendimento);

            List<AtendimentoCid> atendimentoCidList = new ArrayList<AtendimentoCid>();
            if (br.com.ksisolucoes.util.CollectionUtils.isNotNullEmpty(dto.getCidSecundarioList())) {
                for (Cid cid : dto.getCidSecundarioList()) {
                    AtendimentoCid ac = new AtendimentoCid();
                    ac.setAtendimento(atendimento);
                    ac.setCid(cid);
                    atendimentoCidList.add(ac);
                }
            }
            new SaveAtendimentoCidList(atendimentoCidList, atendimento).start();

            List<EncaminhamentoAtendimento> list = getSession().createCriteria(EncaminhamentoAtendimento.class)
                    .add(Restrictions.eq(EncaminhamentoAtendimento.PROP_ATENDIMENTO, atendimento))
                    .createCriteria(EncaminhamentoAtendimento.PROP_ENCAMINHAMENTO, JoinType.LEFT_OUTER_JOIN)
                    .list();
            if (CollectionUtils.isNotNullEmpty(list)) {
                for (EncaminhamentoAtendimento ea : list) {
                    if (Coalesce.asLong(atendimento.getDiasRetorno()) > 0) {
                        ea.getEncaminhamento().setStatus(Encaminhamento.STATUS_CONCLUIDO_COM_RETORNO);
                    } else {
                        ea.getEncaminhamento().setStatus(Encaminhamento.STATUS_CONCLUIDO);
                    }
                    BOFactory.getBO(CadastroFacade.class).save(ea.getEncaminhamento());
                }
            }

            UsuarioCadsusPatologia usuarioCadsusPatologia = (UsuarioCadsusPatologia) getSession().createCriteria(UsuarioCadsusPatologia.class)
                    .add(Restrictions.eq("usuarioCadsus", atendimento.getUsuarioCadsus()))
                    .add(Restrictions.eq("cid", dto.getCid()))
                    .uniqueResult();

            if (dto.getRegistrarCidPatologiasPaciente() != null && dto.getRegistrarCidPatologiasPaciente() && atendimento.getCidPrincipal() != null && usuarioCadsusPatologia == null) {

                usuarioCadsusPatologia = new UsuarioCadsusPatologia();

                usuarioCadsusPatologia.setCid(dto.getCid());
                usuarioCadsusPatologia.setAtendimento(atendimento);
                usuarioCadsusPatologia.setDataAtendimento(atendimento.getDataAtendimento());
                usuarioCadsusPatologia.setDataUsuario(DataUtil.getDataAtual());
                usuarioCadsusPatologia.setProfissional(atendimento.getProfissional());
                usuarioCadsusPatologia.setUsuario((Usuario) getSessao().getUsuario());
                usuarioCadsusPatologia.setUsuarioCadsus(atendimento.getUsuarioCadsus());

                BOFactory.save(usuarioCadsusPatologia);
            }

            ClassificacaoCids classificacaoCid = br.com.ksisolucoes.bo.prontuario.basico.interfaces.AtendimentoHelper.getClassificacaoCid(dto.getCid());
            if (classificacaoCid != null) {
                gerarRegistroAgravo(classificacaoCid);
                gerarAtendimentoCidNotificavel();
            }
        }

        salvarEvolucaoProntuario();
        salvarSolicitacaoAgendamento();
        salvarAtendimentoMDDA();
        salvarAtendimentoProntuario();
        salvarUsuarioCadsusDado();
    }

    private void salvarSolicitacaoAgendamento() throws DAOException, ValidacaoException {
            TipoProcedimento tipoProcedimento = (TipoProcedimento) getSession().createCriteria(NaturezaProcuraTipoAtendimento.class)
                    .add(Restrictions.eq(NaturezaProcuraTipoAtendimento.PROP_CODIGO, atendimento.getNaturezaProcuraTipoAtendimento().getCodigo()))
                    .setProjection(Projections.property(NaturezaProcuraTipoAtendimento.PROP_TIPO_PROCEDIMENTO))
                    .createCriteria(NaturezaProcuraTipoAtendimento.PROP_TIPO_PROCEDIMENTO)
                    .uniqueResult();

            if (tipoProcedimento == null) {
                tipoProcedimento = (TipoProcedimento) getSession().createCriteria(NaturezaProcuraTipoAtendimento.class)
                        .add(Restrictions.eq(NaturezaProcuraTipoAtendimento.PROP_CODIGO, atendimento.getAtendimentoPrincipal().getNaturezaProcuraTipoAtendimento().getCodigo()))
                        .setProjection(Projections.property(NaturezaProcuraTipoAtendimento.PROP_TIPO_PROCEDIMENTO))
                        .createCriteria(NaturezaProcuraTipoAtendimento.PROP_TIPO_PROCEDIMENTO)
                        .uniqueResult();
            }

            if (tipoProcedimento == null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_tipo_procedimento_nat_tipo_nao_definido"));

            }

            if  (Coalesce.asLong(atendimento.getDiasRetorno()) > 0) {
                if (RepositoryComponentDefault.SIM_LONG.equals(tipoProcedimento.getFlagSolicitarRetornoAutomatico())) {
                    SolicitacaoAgendamento solicitacaoAgendamento = new SolicitacaoAgendamento();

                    solicitacaoAgendamento.setEmpresa(atendimento.getEmpresaSolicitante());
                    solicitacaoAgendamento.setTipoProcedimento(tipoProcedimento);
                    solicitacaoAgendamento.setProfissional(atendimento.getProfissional());
                    solicitacaoAgendamento.setProcedimento(tipoProcedimento.getProcedimento());
                    solicitacaoAgendamento.setUsuarioCadsus(atendimento.getUsuarioCadsus());
                    solicitacaoAgendamento.setDataSolicitacao(DataUtil.getDataAtual());
                    solicitacaoAgendamento.setStatus(SolicitacaoAgendamento.STATUS_FILA_ESPERA);
                    solicitacaoAgendamento.setPrioridade(SolicitacaoAgendamento.PRIORIDADE_ELETIVO);
                    solicitacaoAgendamento.setTipoConsulta(SolicitacaoAgendamento.TIPO_CONSULTA_RETORNO);
                    solicitacaoAgendamento.setAtendimentoOrigem(atendimento);
                    solicitacaoAgendamento.setProfissionalDesejado(atendimento.getProfissional());

                    Date dataDesejada = Data.addDias(atendimento.getDataAtendimento(), dto.getDiasRetorno().intValue());
                    solicitacaoAgendamento.setDataDesejada(dataDesejada);

                    BOFactory.getBO(AgendamentoFacade.class).solicitarAgendamento(solicitacaoAgendamento);
                }
            }
}

    private void salvarEvolucaoProntuario() throws DAOException, ValidacaoException {
        EvolucaoProntuario evolucaoProntuario = new EvolucaoProntuario();

        evolucaoProntuario.setDescricao(dto.getDescricaoEvolucao());
        evolucaoProntuario.setProfissional(profissional);
        evolucaoProntuario.setAtendimento(atendimento);
        evolucaoProntuario.setDataHistorico(DataUtil.getDataAtual());
        evolucaoProntuario.setDataRegistro(DataUtil.getDataAtual());
        evolucaoProntuario.setUsuario((Usuario) sessao.getUsuario());
        evolucaoProntuario.setConduta(dto.getConduta());
        evolucaoProntuario.setTabelaCbo(cboProfissional);

        BOFactory.getBO(ReceituarioFacade.class).salvarEvolucaoProntuario(evolucaoProntuario);
    }

    private void salvarAtendimentoMDDA() throws DAOException, ValidacaoException {
        AtendimentoMDDA mdda = dto.getAtendimentoMDDA();
        AtendimentoMDDA mddaClone = null;
        
        if(mdda != null){
            if (mdda.getCodigo() != null) {
                BOFactory.delete(mdda);
                mddaClone = VOUtils.cloneObject(mdda);
                mddaClone.setCodigo(null);
            }else{
                mddaClone = mdda;
            }
        }
    }

    private void salvarAtendimentoProntuario() throws DAOException, ValidacaoException {
        if (dto.getDescricaoEvolucao() == null) {
            return;
        }

        AtendimentoProntuario atendimentoProntuario = new AtendimentoProntuario();
        atendimentoProntuario.setData(DataUtil.getDataAtual());
        atendimentoProntuario.setDescricao(getDescricaoEvolucao(dto.getDescricaoEvolucao()));
        atendimentoProntuario.setTipoRegistro(AtendimentoProntuario.TipoRegistro.EVOLUCAO.value());
        atendimentoProntuario.setUsuarioCadsus(atendimento.getUsuarioCadsus());
        atendimentoProntuario.setEmpresa(atendimento.getEmpresa());
        atendimentoProntuario.setProfissional(profissional);
        atendimentoProntuario.setAtendimento(atendimento);
        atendimentoProntuario.setTabelaCbo(cboProfissional);

        BOFactory.save(atendimentoProntuario);
    }

    private void salvarUsuarioCadsusDado() throws DAOException, ValidacaoException{
        if (dto.getDescricaoAnotacao() == null){
            return;
        }

        if (getUsuarioCadsusDado() == null) {
            usuarioCadsusDado = new UsuarioCadsusDado();
            usuarioCadsusDado.setCodigo(atendimento.getUsuarioCadsus().getCodigo());
        }
        usuarioCadsusDado.setDescricaoAnotacao(dto.getDescricaoAnotacao());
        usuarioCadsusDado.setDataDados(DataUtil.getDataAtual());

        BOFactory.save(usuarioCadsusDado);
    }

    private String getDescricaoEvolucao(String descEvolucao) {
        StringBuilder descricao = new StringBuilder();

        if (RepositoryComponentDefault.SIM_LONG.equals(getSessao().getUsuario().getFlagUsuarioTemporario())) {
            descricao.append("<b>Registrado por: </b>");
            descricao.append(getSessao().getUsuario().getNome());
            descricao.append("\n<br/>");
        }

        descricao.append(descEvolucao);

        return descricao.toString();
    }

    private void gerarAtendimentoCidNotificavel() throws DAOException, ValidacaoException {
        AtendimentoCidNotificavel atendimentoCidNotificavel = new AtendimentoCidNotificavel();

        atendimentoCidNotificavel.setAtendimento(atendimento);
        atendimentoCidNotificavel.setCid(dto.getCid());
        atendimentoCidNotificavel.setFlagPreencheuNotificacao(dto.getFlagPreencheuNotificacao());

        BOFactory.getBO(ReceituarioFacade.class).salvarAtendimentoCidNotificavel(atendimentoCidNotificavel);
    }

    private void gerarRegistroAgravo(ClassificacaoCids classificacaoCid) throws DAOException, ValidacaoException {
        List<Cid> cids = new ArrayList<>();
        cids.add(dto.getCid());

        Cid cidPrincipal = dto.getCid();
        if (classificacaoCid.getCidAgrupador() != null) {
            cids.add(classificacaoCid.getCidAgrupador());
            cidPrincipal = classificacaoCid.getCidAgrupador();
        }

        salvarAtendimentoNotificacao(cidPrincipal);
        Criteria criteria = getSession().createCriteria(RegistroAgravo.class)
                .setProjection(Projections.property(RegistroAgravo.PROP_CODIGO))
                .add(Restrictions.eq(RegistroAgravo.PROP_USUARIO_CADSUS, atendimento.getUsuarioCadsus()))
                .add(Restrictions.in(RegistroAgravo.PROP_CID, cids))
                .add(Restrictions.ne(RegistroAgravo.PROP_STATUS,RegistroAgravo.Status.CANCELADO.value()));
        if (classificacaoCid.getFichaInvestigacaoAgravo() != null) {
            criteria.add(Restrictions.not(Restrictions.in(RegistroAgravo.PROP_STATUS,Arrays.asList(RegistroAgravo.Status.INVESTIGACAO_CONCLUIDA.value()))));
        } else {
            criteria.add(Restrictions.not(Restrictions.in(RegistroAgravo.PROP_STATUS,Arrays.asList(RegistroAgravo.Status.CONCLUIDO.value()))));
        }

        //Uma nova rotina foi criada na classe DefaultProntuarioPage para salvar o Registro Agravo
        /*List<RegistroAgravo> registroAgravoList = (List<RegistroAgravo>) criteria.setMaxResults(1).list();

        if (registroAgravoList.isEmpty() ||
                RepositoryComponentDefault.SIM_LONG.equals(classificacaoCid.getPermiteNotificacaoConcomitante()) && RepositoryComponentDefault.SIM_LONG.equals(dto.getPermiteOutraNotificacaoCID())) {
            salvarRegistroAgravo(cidPrincipal);
        }*/
    }

    private void salvarRegistroAgravo(Cid cid) throws ValidacaoException, DAOException {

        RegistroAgravo ra = new RegistroAgravo();
        ra.setUsuarioCadsus(atendimento.getUsuarioCadsus());

        ra.setCid(cid);
        ra.setDataRegistro(DataUtil.getDataAtual());
        ra.setProfissional(profissional);
        ra.setEmpresa(atendimento.getEmpresa());
        ra.setAtendimento(atendimento);
        ra.setDataPrimeirosSintomas(dto.getDataPrimeirosSintomas());

        if (ra.getCodigoNotificacao() == null) {
            Long codigoNotificacao = BOFactory.getBO(VigilanciaFacade.class).validaCodigoNotificacao(null);
            ra.setCodigoNotificacao(codigoNotificacao);
        }

        if (getUsuarioCadsusDado() != null) {
            ra.setGestante(getUsuarioCadsusDado().getGestante());
        } else {
            ra.setGestante(RepositoryComponentDefault.NAO_LONG);
        }

        BOFactory.save(ra);
    }

    private void salvarAtendimentoNotificacao(Cid cid) throws ValidacaoException, DAOException {
        AtendimentoNotificacao atendimentoNotificacao = new AtendimentoNotificacao();
        atendimentoNotificacao.setCid(cid);
        atendimentoNotificacao.setAtendimento(atendimento);
        atendimentoNotificacao.setUsuario(getSessao().<Usuario>getUsuario());
        atendimentoNotificacao.setEmpresa(atendimento.getEmpresa());
        atendimentoNotificacao.setFlagRegistroAgravoCriado(0L);
        atendimentoNotificacao.setDataRegistro(DataUtil.getDataAtual());

        BOFactory.save(atendimentoNotificacao);
    }

    public UsuarioCadsusDado getUsuarioCadsusDado() {
        if(usuarioCadsusDado == null){
            usuarioCadsusDado = (UsuarioCadsusDado) getSession().createCriteria(UsuarioCadsusDado.class)
                .add(Restrictions.eq(VOUtils.montarPath(UsuarioCadsusDado.PROP_CODIGO), atendimento.getUsuarioCadsus().getCodigo()))
                .uniqueResult();
        }
        return usuarioCadsusDado;
    }
}
