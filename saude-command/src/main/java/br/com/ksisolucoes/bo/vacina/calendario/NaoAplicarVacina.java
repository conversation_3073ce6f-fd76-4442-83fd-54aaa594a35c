package br.com.ksisolucoes.bo.vacina.calendario;

import br.com.ksisolucoes.bo.vacina.interfaces.dto.VacinaCalendarioDTO;
import br.com.ksisolucoes.bo.vacina.interfaces.facade.VacinaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vacina.VacinaAplicacao;
import br.com.ksisolucoes.vo.vacina.VacinaCalendario;
import org.hibernate.Criteria;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class NaoAplicarVacina extends AbstractCommandTransaction {

    private VacinaCalendarioDTO vacinaCalendarioDTO;
    private UsuarioCadsus usuarioCadsus;

    public NaoAplicarVacina(VacinaCalendarioDTO vacinaCalendarioDTO, UsuarioCadsus usuarioCadsus) {
        this.vacinaCalendarioDTO = vacinaCalendarioDTO;
        this.usuarioCadsus = usuarioCadsus;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (vacinaCalendarioDTO.getVacinaCalendario()!=null){
            Criteria cCalendario = getSession().createCriteria(VacinaAplicacao.class)
                    .add(Restrictions.eq(VOUtils.montarPath(VacinaAplicacao.PROP_VACINA_CALENDARIO), vacinaCalendarioDTO.getVacinaCalendario()))
                    .add(Restrictions.eq(VOUtils.montarPath(VacinaAplicacao.PROP_USUARIO_CADSUS), usuarioCadsus))
                    .add(Restrictions.ne(VOUtils.montarPath(VacinaAplicacao.PROP_STATUS), VacinaAplicacao.StatusVacinaAplicacao.CANCELADA.value()));

            VacinaAplicacao loadVacinaAplicacao = (VacinaAplicacao) cCalendario.uniqueResult();

            if (loadVacinaAplicacao!=null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_nao_permitido_nao_aplicar_vacinas_nao_estejam_aberto"));
            }
        }
        
        VacinaCalendario vacinaCalendario = (VacinaCalendario) getSession().get(VacinaCalendario.class, vacinaCalendarioDTO.getVacinaCalendario().getCodigo());
        VacinaAplicacao vacinaAplicacao = new VacinaAplicacao();
        
        vacinaAplicacao.setVacinaCalendario(vacinaCalendario);
        vacinaAplicacao.setStatus(VacinaAplicacao.StatusVacinaAplicacao.NAO_APLICADA.value());
        vacinaAplicacao.setDataCadastro(Data.getDataAtual());
        vacinaAplicacao.setTipoVacina(vacinaCalendario.getTipoVacina());
        vacinaAplicacao.setUsuario((Usuario)getSessao().getUsuario());
        vacinaAplicacao.setObservacao(vacinaCalendarioDTO.getObservacao());
        vacinaAplicacao.setDescricaoVacina(vacinaCalendario.getTipoVacina().getDescricao());
        vacinaAplicacao.setUsuarioCadsus(usuarioCadsus);
        vacinaAplicacao.setFlagHistorico(RepositoryComponentDefault.SIM_LONG);

        BOFactory.getBO(VacinaFacade.class).salvarVacinaAplicacao(vacinaAplicacao);
    }

}
