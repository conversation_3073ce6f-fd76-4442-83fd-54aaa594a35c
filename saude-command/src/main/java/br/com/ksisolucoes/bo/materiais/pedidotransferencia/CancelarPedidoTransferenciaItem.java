package br.com.ksisolucoes.bo.materiais.pedidotransferencia;

import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.ReservaFacade;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import org.hibernate.Criteria;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CancelarPedidoTransferenciaItem extends AbstractCommandTransaction {

    private Long codigoPedidoTransferenciaItem;
    private OrigemProcessoPedido origemProcessoPedido;
    private String motivo;

    public CancelarPedidoTransferenciaItem(Long codigoPedidoTransferenciaItem, OrigemProcessoPedido origemProcessoPedido, String motivo) {
        this.codigoPedidoTransferenciaItem = codigoPedidoTransferenciaItem;
        this.origemProcessoPedido = origemProcessoPedido;
        this.motivo = motivo;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        PedidoTransferenciaItem pedidoTransferenciaItem = (PedidoTransferenciaItem) getSession().get(PedidoTransferenciaItem.class, codigoPedidoTransferenciaItem);
        
        pedidoTransferenciaItem.setDataCancelamento(Data.getDataAtual());
        pedidoTransferenciaItem.setUsuarioCancelamento(getSessao().<Usuario>getUsuario());

        if (OrigemProcessoPedido.LANCAMENTO.equals(this.origemProcessoPedido) || OrigemProcessoPedido.LANCAMENTO_WEB.equals(this.origemProcessoPedido)) {
            if (PedidoTransferenciaItem.StatusPedidoTransferenciaItem.ABERTO.value().equals(pedidoTransferenciaItem.getStatus())) {
                pedidoTransferenciaItem.setDescricaoCancelamento(Bundle.getStringApplication("msg_item_cancelado"));
            } else {
                pedidoTransferenciaItem.setDescricaoCancelamento(Bundle.getStringApplication("item_cancelado_lancamento_pedido"));
            }

            pedidoTransferenciaItem.setStatus(PedidoTransferenciaItem.StatusPedidoTransferenciaItem.CANCELADO.value());
        } else {
            pedidoTransferenciaItem.setStatus(PedidoTransferenciaItem.StatusPedidoTransferenciaItem.CANCELADO.value());
            if (pedidoTransferenciaItem.getPedidoTransferencia().getStatus().equals(PedidoTransferencia.STATUS_SEPARANDO)
                    || pedidoTransferenciaItem.getPedidoTransferencia().getStatus().equals(PedidoTransferencia.STATUS_NAO_APROVADO)) {

                EstoqueDepositoView estoqueDepositoView = (EstoqueDepositoView) getSession().createCriteria(EstoqueDepositoView.class)
                        .add(Restrictions.eq(EstoqueDepositoView.PROP_EMPRESA, pedidoTransferenciaItem.getPedidoTransferencia().getEmpresaOrigem()))
                        .add(Restrictions.eq(EstoqueDepositoView.PROP_PRODUTO, pedidoTransferenciaItem.getProduto()))
                        .add(Restrictions.eq(EstoqueDepositoView.PROP_DEPOSITO, pedidoTransferenciaItem.getPedidoTransferencia().getDeposito()))
                        .uniqueResult();

                if (estoqueDepositoView == null || estoqueDepositoView.getEstoqueDisponivel() < pedidoTransferenciaItem.getQuantidade()) {
                    pedidoTransferenciaItem.setStatus(PedidoTransferenciaItem.StatusPedidoTransferenciaItem.SEM_ESTOQUE.value());
                    pedidoTransferenciaItem.setDescricaoCancelamento(Bundle.getStringApplication("msg_nao_possui_estoque"));
                } else {
                    pedidoTransferenciaItem.setStatus(PedidoTransferenciaItem.StatusPedidoTransferenciaItem.NAO_APROVADO.value());
                    pedidoTransferenciaItem.setDescricaoCancelamento(Bundle.getStringApplication("msg_nao_aprovado"));
                }

            }
        }
        if (motivo != null) {
            pedidoTransferenciaItem.setDescricaoJustificativaSeparacao(motivo);
            pedidoTransferenciaItem.setStatus(PedidoTransferenciaItem.StatusPedidoTransferenciaItem.CANCELADO.value());
        }

        BOFactory.getBO(CadastroFacade.class).save(pedidoTransferenciaItem);

         List<PedidoTransferenciaItemLote> lotes = getSession().createCriteria(PedidoTransferenciaItemLote.class)
                    .add(Restrictions.eq(VOUtils.montarPath(PedidoTransferenciaItemLote.PROP_PEDIDO_TRANSFERENCIA_ITEM), pedidoTransferenciaItem))
                    .list();

        Criteria cReserva = getSession().createCriteria(Reserva.class)
            .createAlias(VOUtils.montarPath(Reserva.PROP_GRUPO_ESTOQUE, GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_ESTOQUE_EMPRESA, EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA), VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_ESTOQUE_EMPRESA, EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA))
            .createAlias(VOUtils.montarPath(Reserva.PROP_GRUPO_ESTOQUE, GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_ESTOQUE_EMPRESA, EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO), VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_ESTOQUE_EMPRESA, EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO))
            .add(Restrictions.eq(VOUtils.montarPath(Reserva.PROP_TIPO_RESERVA), Reserva.TIPO_RESERVA_PEDIDO))
            .add(Restrictions.eq(VOUtils.montarPath(Reserva.PROP_DOCUMENTO), pedidoTransferenciaItem.getPedidoTransferencia().getCodigo()))
            .add(Restrictions.eq(VOUtils.montarPath(Reserva.PROP_ITEM), pedidoTransferenciaItem.getItem()))
            .add(Restrictions.eq(VOUtils.montarPath(Reserva.PROP_GRUPO_ESTOQUE, GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_CODIGO_DEPOSITO), pedidoTransferenciaItem.getPedidoTransferencia().getDeposito().getCodigo()))
            .add(Restrictions.eq(VOUtils.montarPath(Reserva.PROP_GRUPO_ESTOQUE, GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_ESTOQUE_EMPRESA, EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA), pedidoTransferenciaItem.getPedidoTransferencia().getEmpresaOrigem()))
            .add(Restrictions.eq(VOUtils.montarPath(Reserva.PROP_GRUPO_ESTOQUE, GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_ESTOQUE_EMPRESA, EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO), pedidoTransferenciaItem.getProduto()))
            .add(Restrictions.ne(VOUtils.montarPath(Reserva.PROP_STATUS), Reserva.STATUS_CANCELADO));
         
        List<Reserva> reservas = cReserva.list();
        if(CollectionUtils.isNotNullEmpty(reservas)){
            for (Reserva reserva : reservas) {
                BOFactory.getBO(ReservaFacade.class).cancelarReserva(reserva);
            }
        }

        for (PedidoTransferenciaItemLote pedidoTransferenciaItemLote : lotes) {
            BOFactory.getBO(CadastroFacade.class).delete(pedidoTransferenciaItemLote);
        }
    }

}
