package br.com.ksisolucoes.bo.agendamento.solicitacaoagendamento;

import br.com.ksisolucoes.agendamento.recebimentosolicitacoes.dto.ConfirmacaoEnvioSolicitacoesDTO;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.LoteSolicitacaoAgendamento;
import br.com.ksisolucoes.vo.agendamento.LoteSolicitacaoAgendamentoItem;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamentoOcorrencia;

/**
 *
 * <AUTHOR>
 */
public class ConfirmarEnvioSolicitacoes extends AbstractCommandTransaction {
    
    private ConfirmacaoEnvioSolicitacoesDTO dto;
    private Long statusItem;

    public ConfirmarEnvioSolicitacoes(ConfirmacaoEnvioSolicitacoesDTO dto, Long statusItem) {
        this.dto = dto;
        this.statusItem = statusItem;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        String descricao = null;

        for(LoteSolicitacaoAgendamentoItem item : dto.getLoteSolicitacaoAgendamentoItemList()){
            item.setStatus(statusItem);
         
            if(LoteSolicitacaoAgendamentoItem.Status.REGULACAO.value().equals(statusItem)){
                item.getSolicitacaoAgendamento().setStatus(SolicitacaoAgendamento.STATUS_REGULACAO_PENDENTE);
                descricao = Bundle.getStringApplication("msg_solicitacao_enviada_para_regulacao_analise_lote");
            } else if(LoteSolicitacaoAgendamentoItem.Status.FILA_ESPERA.value().equals(statusItem)){
                item.getSolicitacaoAgendamento().setStatus(SolicitacaoAgendamento.STATUS_FILA_ESPERA);
                descricao = Bundle.getStringApplication("msg_solicitacao_encaminhada_para_fila_espera_analise_lote");
            }

            BOFactory.save(item);
            BOFactory.save(item.getSolicitacaoAgendamento());

            if(descricao != null){
                BOFactory.getBO(AgendamentoFacade.class).gerarOcorrenciaSolicitacaoAgendamento(SolicitacaoAgendamentoOcorrencia.TipoOcorrencia.SOLICITACAO, descricao, item.getSolicitacaoAgendamento());
                descricao = null;
            }
        }

        LoteSolicitacaoAgendamento loteSolicitacaoAgendamento = dto.getLoteSolicitacaoAgendamento();
        if (!LoteSolicitacaoAgendamento.StatusLoteSolicitacaoAgendamento.RECEBIDO.value().equals(loteSolicitacaoAgendamento.getStatus())
                && !BOFactory.getBO(AgendamentoFacade.class).existeLoteSolicitacaoAgendamentoItemPendente(loteSolicitacaoAgendamento)) {
            loteSolicitacaoAgendamento.setStatus(LoteSolicitacaoAgendamento.StatusLoteSolicitacaoAgendamento.RECEBIDO.value());
            BOFactory.save(loteSolicitacaoAgendamento);
        }
    }
    
}
