/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.basico.faixaetaria;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.FaixaEtaria;
import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class SaveFaixaEtaria extends SaveVO {

    private FaixaEtaria faixaEtaria;

    public SaveFaixaEtaria(Object vo) {
        super(vo);
        this.faixaEtaria = (FaixaEtaria) vo;
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (StringUtils.trimToNull(this.faixaEtaria.getDescricao())==null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_descricao"));
        }
    }

    public FaixaEtaria getFaixaEtaria() {
        return faixaEtaria;
    }

}
