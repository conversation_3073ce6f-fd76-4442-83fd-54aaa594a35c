package br.com.ksisolucoes.bo.vigilancia.externo;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.externo.UsuarioVigilancia;
import org.hibernate.criterion.Restrictions;

/**
 * <AUTHOR>
 */
public class RegistrarNovaSenhaVigilancia extends AbstractCommandTransaction{

    private Long codigoUsuarioVigilancia;
    private String senha;
    private String codigoSenha;

    public RegistrarNovaSenhaVigilancia(Long codigoUsuarioVigilancia, String senha, String codigoSenha) {
        this.codigoUsuarioVigilancia = codigoUsuarioVigilancia;
        this.senha = senha;
        this.codigoSenha = codigoSenha;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        if(codigoUsuarioVigilancia == null){
            throw new ValidacaoException(Bundle.getStringApplication("contaNaoLocalizada"));
        }
        String senhaCriptografada = Util.criptografarSenha(senha);

        UsuarioVigilancia up = (UsuarioVigilancia) getSession().createCriteria(UsuarioVigilancia.class)
                .add(Restrictions.idEq(codigoUsuarioVigilancia))
                .add(Restrictions.eq(UsuarioVigilancia.PROP_CHAVE_NOVA_SENHA, codigoSenha))
                .createCriteria(UsuarioVigilancia.PROP_USUARIO)
                .uniqueResult();
                
        if(up == null){
            throw new ValidacaoException(Bundle.getStringApplication("contaNaoLocalizada"));
        }

        up.setChaveNovaSenha(null);
        up.setSenha(senhaCriptografada);
        BOFactory.save(up);
        
        up.getUsuario().setSenha(senhaCriptografada);
        up.getUsuario().setStatus(Usuario.STATUS_ATIVO);
        BOFactory.save(up.getUsuario());
    }
}
