package br.com.ksisolucoes.bo.vigilancia.termoajustamentoconduta.termoajustamentoconduta;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.termoajustamentoconduta.TermoAjustamentoConduta;

public class SaveTermoAjustamentoConduta extends SaveVO<TermoAjustamentoConduta> {
    /**
     * Construtor para o Objeto de Negcio. Este prove obrigatoriedade
     * na passagem do VO como parmetro.
     *
     * @param vo
     */
    public SaveTermoAjustamentoConduta(TermoAjustamentoConduta vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getSituacao() == null) {
            this.vo.setSituacao(TermoAjustamentoConduta.Status.EM_ANDAMENTO.value());
        }

        if (this.vo.getDataCadastro() == null) {
            this.vo.setDataCadastro(DataUtil.getDataAtual());
        }

        if (this.vo.getDataEmissao() != null &&
                this.vo.getDataEmissao().after(DataUtil.getDataAtual())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_data_emissao_maior_atual"));
        }

        this.vo.setUsuario(sessao.getUsuario());
        this.vo.setDataAlteracao(DataUtil.getDataAtual());
    }
}
