package br.com.ksisolucoes.bo.entradas.estoque.centrocusto;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.CentroCusto;
import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class SaveCentroCusto extends SaveVO {

    private CentroCusto centroCusto;

    public SaveCentroCusto(Object vo) {
        super(vo);
        this.centroCusto = (CentroCusto) vo;
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.centroCusto.getDescricao() == null || StringUtils.trimToNull(this.centroCusto.getDescricao()) == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_descricao"));
        }
    }

}
