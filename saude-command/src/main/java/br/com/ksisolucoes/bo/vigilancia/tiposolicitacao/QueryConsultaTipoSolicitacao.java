package br.com.ksisolucoes.bo.vigilancia.tiposolicitacao;

import br.com.ksisolucoes.bo.basico.dto.QueryConsultaTipoSolicitacaoDTOParam;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryConsultaTipoSolicitacao extends CommandQueryPager<QueryConsultaTipoSolicitacao> {

    private QueryConsultaTipoSolicitacaoDTOParam param;

    public QueryConsultaTipoSolicitacao(QueryConsultaTipoSolicitacaoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("ts.codigo", true);
        hql.addToSelect("ts.descricao", true);

        hql.setTypeSelect(TipoSolicitacao.class.getName());
        hql.addToFrom("TipoSolicitacao ts");

        hql.addToWhereWhithAnd("ts.codigo = ", param.getCodigo());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("ts.descricao", param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("ts.codigo || ' ' || ts.descricao", param.getKeyword()));

        if (!param.isMostrarInativos()) {
            hql.addToWhereWhithAnd("ts.ativo = ", RepositoryComponentDefault.SIM_LONG);
        }

        if (param.getPropSort() != null) {
            hql.addToOrder("ts." + param.getPropSort() + " " + (param.isAscending() ? "asc" : "desc"));
        } else {
            hql.addToOrder("ts.descricao");
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
