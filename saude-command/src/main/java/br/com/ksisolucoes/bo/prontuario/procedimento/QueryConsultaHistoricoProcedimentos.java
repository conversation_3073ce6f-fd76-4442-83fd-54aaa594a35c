package br.com.ksisolucoes.bo.prontuario.procedimento;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.prontuario.web.procedimento.dto.HistoricoProcedimentosDTO;
import br.com.ksisolucoes.bo.prontuario.web.procedimento.dto.HistoricoProcedimentosDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoItem;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class QueryConsultaHistoricoProcedimentos extends CommandQuery<QueryConsultaHistoricoProcedimentos> {

    private HistoricoProcedimentosDTOParam param;
    private List<HistoricoProcedimentosDTO> result;

    public QueryConsultaHistoricoProcedimentos(HistoricoProcedimentosDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {

        hql.addToSelect("atendimentoItem.codigo", "codigoAtendimentoItem");
        hql.addToSelect("atendimentoItem.dataHora", "dataAtendimento");
        hql.addToSelect("tipoAtendimento.descricao", "descricaoTipoAtendimento");
        hql.addToSelect("procedimento.descricao", "descricaoProcedimento");
        hql.addToSelect("empresa.descricao", "descricaoUnidade");
        hql.addToSelect("profissional.nome", "nomeProfissional");
        hql.addToSelect("cidPrincipal.codigo", "codigoCid");
        hql.addToSelect("cid.descricao", "descricaoCidItem");
        hql.addToSelect("cid.codigo", "codigoCidItem");
        hql.addToSelect("atendimentoItem.anotacaoAtendimento", "anotacao");
        hql.addToSelect("atendimentoItem.justificativa", "justificativa");

        hql.setTypeSelect(HistoricoProcedimentosDTO.class.getName());
        hql.addToFrom("AtendimentoItem atendimentoItem"
                + " left join atendimentoItem.atendimento atendimento"
                + " left join atendimentoItem.procedimentoCompetencia procedimentoCompetencia"
                + " left join procedimentoCompetencia.id.procedimento procedimento "
                + " left join atendimento.empresa empresa"
                + " left join atendimento.profissional profissional"
                + " left join atendimento.cidPrincipal cidPrincipal"
                + " left join atendimentoItem.cid cid"
                + " left join atendimento.naturezaProcuraTipoAtendimento naturezaProcuraTipoAtendimento"
                + " left join naturezaProcuraTipoAtendimento.tipoAtendimento tipoAtendimento"
                + " left join atendimento.usuarioCadsus usuarioCadsus"
                + " left join atendimento.profissional profissional"
                + " left join atendimento.tabelaCbo cboAtendimento");

        hql.addToWhereWhithAnd("usuarioCadsus.codigo = ", param.getCodigoPaciente());
        hql.addToWhereWhithAnd("atendimento.status in ", Arrays.asList(Atendimento.STATUS_ATENDENDO, Atendimento.STATUS_FINALIZADO));
        hql.addToWhereWhithAnd("tipoAtendimento = ", param.getTipoAtendimento());
        hql.addToWhereWhithAnd("atendimentoItem.tipoOrigem is null ");
        hql.addToWhereWhithAnd("atendimentoItem.status <> ", AtendimentoItem.STATUS_CANCELADO);

        if (param.getMeusAtendimentos()) {
            hql.addToWhereWhithAnd("profissional.codigo = ", param.getCodigoProfissional());
        }
        if (param.getMeses() != null) {
            Date dataAtual = DataUtil.getDataAtual();
            hql.addToWhereWhithAnd("atendimentoItem.dataHora ", Data.adjustRangeHour(new DatePeriod(Data.removeMeses(dataAtual, param.getMeses()), dataAtual)));
        }
        if (param.getGrupoAtendimentoCbo() != null) {
            HQLHelper hqlSub = hql.getNewInstanceSubQuery();

            hqlSub.addToSelect("1");
            hqlSub.addToFrom("EloGrupoAtendimentoCbo eloGrupoAtendimentoCbo"
                    + " left join eloGrupoAtendimentoCbo.grupoAtendimentoCbo grupoAtendimentoCbo"
                    + " left join eloGrupoAtendimentoCbo.tabelaCbo cboGrupo");
            hqlSub.addToWhereWhithAnd("grupoAtendimentoCbo =", param.getGrupoAtendimentoCbo());
            hqlSub.addToWhereWhithAnd("cboGrupo = cboAtendimento");

            hql.addToWhereWhithAnd("exists(" + hqlSub.getQuery() + ")");
        }

        HistoricoProcedimentosDTO on = on(HistoricoProcedimentosDTO.class);

        if (path(on.getDescricaoTipoAtendimento()).equals(param.getPropOrdenacao())) {
            hql.addToOrder("tipoAtendimento.descricao " + (param.getOrdenacao() ? "asc" : "desc"));
        } else if (path(on.getNomeProfissional()).equals(param.getPropOrdenacao())) {
            hql.addToOrder("profissional.nome " + (param.getOrdenacao() ? "asc" : "desc"));
        } else if (path(on.getDescricaoProcedimento()).equals(param.getPropOrdenacao())) {
            hql.addToOrder("procedimento.descricao " + (param.getOrdenacao() ? "asc" : "desc"));
        } else {
            hql.addToOrder("atendimentoItem.dataHora " + (param.getOrdenacao() ? "asc" : "desc"));
        }

    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<HistoricoProcedimentosDTO> getResult() {
        return result;
    }

}
