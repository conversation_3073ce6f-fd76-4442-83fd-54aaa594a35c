package br.com.ksisolucoes.bo.prontuario.basico.configuracaoestratificacao;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.ConfiguracaoEstratificacao;
import br.com.ksisolucoes.vo.prontuario.basico.ConfiguracaoEstratificacaoCbos;

import java.util.List;

import static ch.lambdaj.Lambda.forEach;

/**
 *
 * <AUTHOR>
 */
public class SalvarConfiguracaoEstratificacao extends AbstractCommandTransaction {
    private ConfiguracaoEstratificacao configuracaoEstratificacao;
    private List<ConfiguracaoEstratificacaoCbos> cbosList;

    public SalvarConfiguracaoEstratificacao(ConfiguracaoEstratificacao configuracaoEstratificacao) {
        this.configuracaoEstratificacao = configuracaoEstratificacao;
    }

    public SalvarConfiguracaoEstratificacao(ConfiguracaoEstratificacao configuracaoEstratificacao, List<ConfiguracaoEstratificacaoCbos> cbosList) {
        this.configuracaoEstratificacao = configuracaoEstratificacao;
        this.cbosList = cbosList;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        LoadManager load = LoadManager.getInstance(ConfiguracaoEstratificacao.class)
                .addParameter(new QueryCustom.QueryCustomParameter(ConfiguracaoEstratificacao.PROP_FORMULARIO, configuracaoEstratificacao.getFormulario()));
        if (configuracaoEstratificacao.getCodigo() != null) {
            load.addParameter(new QueryCustom.QueryCustomParameter(ConfiguracaoEstratificacao.PROP_CODIGO, BuilderQueryCustom.QueryParameter.DIFERENTE, configuracaoEstratificacao.getCodigo()));
        }
        ConfiguracaoEstratificacao configuracaoEstratificacaoBanco = load.start().getVO();

        if (configuracaoEstratificacaoBanco != null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_configuracao_ja_cadastrada_para_formulario_x", configuracaoEstratificacaoBanco.getDescricaoFormulario()));
        }
        BOFactory.save(configuracaoEstratificacao);

        if (CollectionUtils.isNotNullEmpty(cbosList)) {
            forEach(cbosList).setConfiguracaoEstratificacao(configuracaoEstratificacao);
            VOUtils.persistirListaVosModificados(ConfiguracaoEstratificacaoCbos.class, cbosList, new QueryCustom.QueryCustomParameter(ConfiguracaoEstratificacaoCbos.PROP_CONFIGURACAO_ESTRATIFICACAO, configuracaoEstratificacao));
        }
    }

    public ConfiguracaoEstratificacao getConfiguracaoEstratificacao() {
        return configuracaoEstratificacao;
    }
}
