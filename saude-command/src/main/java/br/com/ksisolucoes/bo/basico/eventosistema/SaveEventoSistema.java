/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.basico.eventosistema;

import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoUtil;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.EventoSistema;

/**
 * <AUTHOR>
 */
public class SaveEventoSistema extends SaveVO<EventoSistema> {

    public SaveEventoSistema(EventoSistema vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getDataRegistro() == null) {
            this.vo.setDataRegistro(DataUtil.getDataAtual());
        }

        if (getSessao() != null) {
            if (this.vo.getUsuario() == null && getSessao().getUsuario() != null) {
                this.vo.setUsuario(getSessao().getUsuario());
            }

            if (this.vo.getEmpresa() == null && getSessao().getEmpresa() != null) {
                this.vo.setEmpresa(getSessao().getEmpresa());
            }

            // caso não tenha sido setado o profissional, pega-se da sessão.
            if (this.vo.getProfissional() == null && getSessao().getUsuario() != null && getSessao().getUsuario().getProfissional() != null) {
                this.vo.setProfissional(getSessao().getUsuario().getProfissional());
            }
        }

        if (this.vo.getIdEvento() == null) {
            this.vo.setIdEvento(EventoSistema.IdentificacaoEvento.CONFIGURACAO.value());
        }
        this.vo.setMaquina(Coalesce.asString(Util.getRemoteClient(), SessaoUtil.getIpClientSessao()));

        // Garante que a precisão não ultrapasse a máxima
        this.vo.setDescricaoEmpresa(StringUtil.getStringMaxPrecision(this.vo.getDescricaoEmpresa(), 150));
        this.vo.setDescricaoProfissional(StringUtil.getStringMaxPrecision(this.vo.getDescricaoProfissional(), 150));
        this.vo.setDescricaoUsuarioCadsus(StringUtil.getStringMaxPrecision(this.vo.getDescricaoUsuarioCadsus(), 150));
        this.vo.setDescricaoUsuario(StringUtil.getStringMaxPrecision(this.vo.getDescricaoUsuario(), 200));
    }

}
