package br.com.ksisolucoes.bo.agendamento.agendagradehorario;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeHorario;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class DeletarAgendaGradeHorario extends AbstractCommandTransaction<DeletarAgendaGradeHorario> {

    private Agenda agenda;

    public DeletarAgendaGradeHorario(Agenda agenda) {
        this.agenda = agenda;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        QueryAgendaGradeHorarioSemAgendamento query = new QueryAgendaGradeHorarioSemAgendamento(agenda);

        query.start();
        List<AgendaGradeHorario> list = query.getResult();

        for (AgendaGradeHorario agendaGradeHorario : list) {
            BOFactory.delete(agendaGradeHorario);
        }
    }

    private class QueryAgendaGradeHorarioSemAgendamento extends CommandQuery {

        private Agenda agenda;
        private List<AgendaGradeHorario> result;

        public QueryAgendaGradeHorarioSemAgendamento(Agenda agenda) {
            this.agenda = agenda;
        }

        @Override
        protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
            hql.setTypeSelect(AgendaGradeHorario.class.getName());

            hql.addToSelect("agendaGradeHorario.codigo", true);
            hql.addToSelect("agendaGradeHorario.version", true);

            hql.addToFrom("AgendaGradeHorario agendaGradeHorario"
                    + " left join agendaGradeHorario.agendaGradeAtendimento agendaGradeAtendimento"
                    + " left join agendaGradeAtendimento.agendaGrade agendaGrade"
                    + " left join agendaGrade.agenda agenda");

            hql.addToWhereWhithAnd("agenda = ", this.agenda);
            hql.addToWhereWhithAnd("agendaGradeHorario.hora >= ", Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial());
            hql.addToWhereWhithAnd("coalesce(agendaGradeAtendimento.quantidadeCotaUnidade, 0) = 0");
            hql.addToWhereWhithAnd("not exists(select 1 from AgendaGradeAtendimentoHorario agah where agah.agendaGradeHorario = agendaGradeHorario)");
        }

        @Override
        public List<AgendaGradeHorario> getResult() {
            return this.result;
        }

        @Override
        protected void result(HQLHelper hql, Object result) {
            this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
        }
    }
}
