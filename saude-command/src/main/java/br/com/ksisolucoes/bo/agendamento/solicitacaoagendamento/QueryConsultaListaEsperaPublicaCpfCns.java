package br.com.ksisolucoes.bo.agendamento.solicitacaoagendamento;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.agendamento.exame.dto.AgendamentoListaEsperaDTO;
import br.com.ksisolucoes.agendamento.exame.dto.AgendamentoListaEsperaDTOParam;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;
import org.apache.commons.lang.time.DateUtils;
import org.hibernate.Query;
import org.hibernate.Session;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryConsultaListaEsperaPublicaCpfCns extends CommandQuery<QueryConsultaListaEsperaPublicaCpfCns> {

    private final AgendamentoListaEsperaDTOParam param;
    private List<AgendamentoListaEsperaDTO> lstAgendamentoListaEsperaDTO;

    public QueryConsultaListaEsperaPublicaCpfCns(AgendamentoListaEsperaDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(AgendamentoListaEsperaDTO.class.getName());
        hql.addToSelectAndGroup("usuarioCadsus.codigo", "solicitacaoAgendamento.usuarioCadsus.codigo");
        hql.addToSelectAndGroup("usuarioCadsus.nome", "solicitacaoAgendamento.usuarioCadsus.nome");

        hql.addToSelectAndGroup("solicitacaoAgendamento.codigo", "solicitacaoAgendamento.codigo");
        hql.addToSelectAndGroup("solicitacaoAgendamento.dataAgendamento", "solicitacaoAgendamento.dataAgendamento");
        hql.addToSelectAndGroup("solicitacaoAgendamento.dataSolicitacao", "solicitacaoAgendamento.dataSolicitacao");
        hql.addToSelectAndGroup("solicitacaoAgendamento.flagDevolvido", "solicitacaoAgendamento.flagDevolvido");
        hql.addToSelectAndGroup("solicitacaoAgendamento.flagAvaliacaoAprovado", "solicitacaoAgendamento.flagAvaliacaoAprovado");
        hql.addToSelectAndGroup("solicitacaoAgendamento.solicitarPrioridade", "solicitacaoAgendamento.solicitarPrioridade");
        hql.addToSelectAndGroup("solicitacaoAgendamento.tipoFila", "solicitacaoAgendamento.tipoFila");
        hql.addToSelectAndGroup("solicitacaoAgendamento.prioridade", "solicitacaoAgendamento.prioridade");
        hql.addToSelectAndGroup("solicitacaoAgendamento.tipoFila", "tipoFila");

        hql.addToSelectAndGroup("justificativaPriorizacao.codigo", "solicitacaoAgendamento.justificativaPriorizacao.codigo");
        hql.addToSelectAndGroup("justificativaPriorizacao.justificativa", "solicitacaoAgendamento.justificativaPriorizacao.justificativa");

        hql.addToSelectAndGroup("solicitacaoAgendamentoPosicaoFila.posicaoFilaEspera", "solicitacaoAgendamento.solicitacaoAgendamentoPosicaoFila.posicaoFilaEspera");

        hql.addToSelectAndGroup("classificacaoRisco.codigo", "solicitacaoAgendamento.classificacaoRisco.codigo");
        hql.addToSelectAndGroup("classificacaoRisco.nivelGravidade", "solicitacaoAgendamento.classificacaoRisco.nivelGravidade");
        hql.addToSelectAndGroup("classificacaoRisco.descricao", "solicitacaoAgendamento.classificacaoRisco.descricao");

        hql.addToSelectAndGroup("classificacaoRisco.codigo", "classificacaoRisco.codigo");
        hql.addToSelectAndGroup("classificacaoRisco.nivelGravidade", "classificacaoRisco.nivelGravidade");
        hql.addToSelectAndGroup("classificacaoRisco.descricao", "classificacaoRisco.descricao");

        hql.addToSelectAndGroup("tipoProcedimento.codigo", "tipoProcedimento.codigo");
        hql.addToSelectAndGroup("tipoProcedimento.descricao", "tipoProcedimento.descricao");

        hql.addToSelectAndGroup("unidadeOrigem.codigo", "solicitacaoAgendamento.unidadeOrigem.codigo");
        hql.addToSelectAndGroup("unidadeOrigem.descricao", "solicitacaoAgendamento.unidadeOrigem.descricao");

        hql.addToSelectAndGroup("usuarioCadsus.nomeMae", "solicitacaoAgendamento.usuarioCadsus.nomeMae");
        hql.addToSelectAndGroup("usuarioCadsus.sexo", "solicitacaoAgendamento.usuarioCadsus.sexo");
        hql.addToSelectAndGroup("usuarioCadsus.dataNascimento", "solicitacaoAgendamento.usuarioCadsus.dataNascimento");

        hql.addToSelectAndGroup("tipoProcedimento.codigo", "solicitacaoAgendamento.tipoProcedimento.codigo");
        hql.addToSelectAndGroup("tipoProcedimento.descricao", "solicitacaoAgendamento.tipoProcedimento.descricao");

        hql.addToSelectAndGroup("tipoExameFiltro.codigo", "tipoExameFiltro.codigo");
        hql.addToSelectAndGroup("tipoExameFiltro.descricao", "tipoExameFiltro.descricao");


        hql.addToFrom(
                "SolicitacaoAgendamento solicitacaoAgendamento"
                + " left join solicitacaoAgendamento.justificativaPriorizacao justificativaPriorizacao"
                + " left join solicitacaoAgendamento.usuarioCadsus usuarioCadsus"
                + " left join usuarioCadsus.roDominioUsuarioCadsus domUsuarioCadsus"
                + " left join solicitacaoAgendamento.tipoProcedimento tipoProcedimento"
                + " left join solicitacaoAgendamento.unidadeOrigem unidadeOrigem "
                + " left join tipoProcedimento.tipoExameFiltro tipoExameFiltro"
                + " left join solicitacaoAgendamento.cid"
                + " left join solicitacaoAgendamento.classificacaoRisco classificacaoRisco"
                + " left join solicitacaoAgendamento.solicitacaoAgendamentoPosicaoFila solicitacaoAgendamentoPosicaoFila"
        );

        if (param.getSituacaoList() != null && br.com.celk.util.CollectionUtils.isNotNullEmpty(param.getSituacaoList())) {
            hql.addToWhereWhithAnd("solicitacaoAgendamento.status in ", param.getSituacaoList());
        }

        hql.addToWhereWhithAnd("tipoProcedimento.flagTfd = ", RepositoryComponentDefault.NAO);
        hql.addToWhereWhithAnd("tipoProcedimento.flagExibeFilaEsperaPublica = ", RepositoryComponentDefault.SIM_LONG);

        if (this.param.getNumeroCartao() != null) {
            hql.addToWhereWhithAnd("domUsuarioCadsus.cns = '" + this.param.getNumeroCartao().replaceAll("[^0-9]", "") + "')");
        }

        if (this.param.getCpf() != null) {
            hql.addToWhereWhithAnd("usuarioCadsus.cpf = ",this.param.getCpf().replaceAll("[^0-9]", ""));
        }

        hql.addToOrder("solicitacaoAgendamentoPosicaoFila.posicaoFilaEspera");
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        boolean listaAgendamentos = param.getSituacaoList().contains(SolicitacaoAgendamento.STATUS_AGENDADO) && param.getSituacaoList().contains(SolicitacaoAgendamento.STATUS_AGENDADO_FORA_REDE);
        if (!listaAgendamentos) {
            for (AgendamentoListaEsperaDTO agendamentoListaEsperaDTO : lstAgendamentoListaEsperaDTO) {
                DataPrevistaAgendamento.dataPrevistaAgendamento(agendamentoListaEsperaDTO, new QueryConsultaMediaVagasAgendadasTipoProcedimento(param, agendamentoListaEsperaDTO.getTipoProcedimento()));
            }
            if (CollectionUtils.isNotNullEmpty(lstAgendamentoListaEsperaDTO)) {
                Lambda.forEach(lstAgendamentoListaEsperaDTO).setTipoFila(param.getTipoFila());
            }
        }
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        super.setParameters(hql, query);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.lstAgendamentoListaEsperaDTO = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    public List<AgendamentoListaEsperaDTO> getLstAgendamentoListaEsperaDTO() {
        return lstAgendamentoListaEsperaDTO;
    }

}
