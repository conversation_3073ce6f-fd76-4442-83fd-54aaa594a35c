package br.com.ksisolucoes.bo.cadsus.usuariocadsus;

import br.com.ksisolucoes.bo.cadsus.interfaces.dto.UsuarioCadsusInformacoesDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado;
import br.com.ksisolucoes.vo.programasaude.ProgramaSaudeUsuario;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ConsultarInformacoesNoDadosPaciente extends AbstractCommandTransaction{

    private UsuarioCadsusInformacoesDTO dto;
    private Long codigoUsuarioCadsus;

    public ConsultarInformacoesNoDadosPaciente(Long codigoUsuarioCadsus) {
        this.codigoUsuarioCadsus = codigoUsuarioCadsus;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        dto = BOFactory.getBO(UsuarioCadsusFacade.class).consultarInformacoesUsuarioCadsus(codigoUsuarioCadsus);

        dto.setUsuarioCadsus(getUsuarioCadsus());
        dto.setProgramasSaudeList(getProgramasSaude());
        dto.setUsuarioCadsusDado(getUsuarioCadsusDado());
        
    }

    public UsuarioCadsusInformacoesDTO getDto() {
        return dto;
    }

    private List<ProgramaSaudeUsuario> getProgramasSaude() throws DAOException, ValidacaoException {
        List<ProgramaSaudeUsuario> programasList = LoadManager.getInstance(ProgramaSaudeUsuario.class)
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProgramaSaudeUsuario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), codigoUsuarioCadsus))
                .addParameter(new QueryCustomParameter(ProgramaSaudeUsuario.PROP_STATUS, ProgramaSaudeUsuario.STATUS_ABERTO))
                .start().getList();
        return programasList;
    }

    private UsuarioCadsus getUsuarioCadsus() throws DAOException, ValidacaoException {
        return LoadManager.getInstance(UsuarioCadsus.class)
                .addParameter(new QueryCustomParameter(UsuarioCadsus.PROP_CODIGO, codigoUsuarioCadsus))
                .start().getVO();
    }

    private UsuarioCadsusDado getUsuarioCadsusDado() throws DAOException, ValidacaoException {
        UsuarioCadsusDado usuarioCadsusDado = LoadManager.getInstance(UsuarioCadsusDado.class)
            .addParameter(new QueryCustomParameter(UsuarioCadsusDado.PROP_CODIGO, codigoUsuarioCadsus))
            .start().getVO();

        return usuarioCadsusDado;
    }
}
