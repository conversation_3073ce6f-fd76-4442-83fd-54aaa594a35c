package br.com.ksisolucoes.bo.geral.estruturaequipamentorevisao;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.util.bo.EstruturaEquipamentoRevisaoHelper;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.geral.interfaces.dto.EstruturaEquipamentoDTOParam;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;


import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.geral.EstruturaEquipamentoRevisao;
import br.com.ksisolucoes.vo.geral.EstruturaEquipamentoRevisaoPK;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;

public class MontarEstruturaEquipamentoRevisao extends AbstractCommandTransaction {

    /*
     * Relacao de niveis
     * -----------------
     *---------------------------------------------------------------------*/
    private int niveis;
    private final int NIVEL_MAXIMO = 50;
    /*--------------------------------------------------------------------*/

    private Produto produto;

    private final List<EstruturaEquipamentoRevisao> estruturaEquipamentoList = new LinkedList<EstruturaEquipamentoRevisao>();

    /**
     * Recebe a Procedencia para adicionar a restrio ao filtro.
     * Procedencia.FABRICADO
     * Procedencia...
     */
    private String procedencia;
    private Long revisao;
    private Long status;
    private boolean cancelados = false;
    private Double quantidadeNecessaria = 1D;
    //Varivel para controlar se deve utilizar a revisao para todos os niveis
    private boolean notRevisaoSub = false;
    
    //properties que vai carregar na estrutura
    private String[] properties = EstruturaEquipamentoRevisaoHelper.getUsedPropertiesEstruturaEquipamentoRevisaoToCadastroEstrutura();
    
    public MontarEstruturaEquipamentoRevisao(EstruturaEquipamentoDTOParam bean) {
        this.niveis = bean.getNiveis();
        this.produto = bean.getProduto();
        this.status = bean.getStatus();
        this.revisao = bean.getRevisao();
        this.cancelados = bean.isCancelados();
        this.notRevisaoSub = bean.isRevisaoSubNiveis();
        this.quantidadeNecessaria = bean.getQuantidadeInicial();
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        this.montar(produto);
    }

    private void montar(Produto produto) throws DAOException, ValidacaoException {

        Set<EstruturaEquipamentoRevisao> estruturaEquipamentoSet = this.getEstruturaEquipamentoRevisaoFilhoSet(produto);
//        this.produto.setEstruturaEquipamentoRevisaoSet(estruturaEquipamentoSet);
        if ( this.notRevisaoSub ){
            revisao = null;
        }
        int i = 1;
        for (EstruturaEquipamentoRevisao equipamento : estruturaEquipamentoSet) {
            this.montarSubNivel(equipamento, this.quantidadeNecessaria, i, 1);
            i++;
        }
    }

    private void montarSubNivel(EstruturaEquipamentoRevisao estruturaEquipamento, Double quantidadeNecessaria, int nivelPrimeiroNivelRefRoot, int nivelRefRoot) throws DAOException, ValidacaoException {
//        estruturaEquipamento.setNivelPrimeiroNivelRefRoot(new Long(nivelPrimeiroNivelRefRoot));
//        estruturaEquipamento.setNivelRefRoot(new Long(nivelRefRoot));
        this.estruturaEquipamentoList.add(estruturaEquipamento);

        /*
         * ATRIBUICAO
         * ----------
         * Seta a quantidade necessria calculada anteriormente.
         *---------------------------------------------------------------------*/
        quantidadeNecessaria = quantidadeNecessaria * estruturaEquipamento.getQuantidade();

//        estruturaEquipamento.setQuantidadeNecessaria(quantidadeNecessaria);
        /*--------------------------------------------------------------------*/

        /*
         * CALCULO PESO
         * ------------
         * Calcula o peso parcial da estrutura.
         * Obs.:  utilizada uma constante referente ao KG.
         *---------------------------------------------------------------------*/
//        double peso = 0;
//        if( estruturaEquipamento.getId().getComponente().getUnidade().getUnidade().trim().toUpperCase().equals(Unidade.UNIDADE_KG.toUpperCase()) ) {
//            peso = quantidadeNecessaria;
//        } else {
//            peso = quantidadeNecessaria * estruturaEquipamento.getId().getComponente().getPesoEspecifico();
//        }
//        estruturaEquipamento.setPesoEstrutura( peso );
//        /*--------------------------------------------------------------------*/

        /*
         * CONTROLE
         * --------
         * Para a recursividade caso o nivel definido tenha sido atingido.
         *---------------------------------------------------------------------*/
        if( nivelRefRoot > niveis - 1 && niveis != 0) {
            return;
        } else if( nivelRefRoot >= NIVEL_MAXIMO ) {
              //Nivel mximo({0}) de nveis hierarquicos atingido. Os produtos podem estar distribuidos de forma recursiva.
              throw new ValidacaoException(Bundle.getStringBO( "2143", new Long(NIVEL_MAXIMO) ));
        }
        /*--------------------------------------------------------------------*/

        Set<EstruturaEquipamentoRevisao> estruturaEquipamentoSet = this.getEstruturaEquipamentoRevisaoFilhoSet( estruturaEquipamento.getId().getComponente() );
//        estruturaEquipamento.getId().getComponente().setEstruturaEquipamentoRevisaoSet(estruturaEquipamentoSet);

        for (EstruturaEquipamentoRevisao equipamento : estruturaEquipamentoSet) {
            this.getSession().flush();
            this.getSession().evict( equipamento );
            this.montarSubNivel(equipamento, quantidadeNecessaria, nivelPrimeiroNivelRefRoot, (nivelRefRoot + 1) );
        }
    }

    public List<EstruturaEquipamentoRevisao> getEstruturaEquipamentoRevisaoList() {
        return this.estruturaEquipamentoList;
    }

    public Produto getProduto() {
        return this.produto;
    }

    private Set<EstruturaEquipamentoRevisao> getEstruturaEquipamentoRevisaoFilhoSet(Produto produto) throws DAOException, ValidacaoException {
        Set<EstruturaEquipamentoRevisao> estruturaEquipamentoFilhoSet = null;
        if( estruturaEquipamentoFilhoSet == null ) {

            List<QueryCustomParameter> parameters = new ArrayList<QueryCustomParameter>();

            parameters.add(new QueryCustomParameter(VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID,EstruturaEquipamentoRevisaoPK.PROP_PRODUTO, Produto.PROP_CODIGO), QueryCustomParameter.IGUAL, produto.getCodigo(), HQLHelper.RESOLVE_CHAR_TYPE));
            
            if ( !this.cancelados ){
                parameters.add(new QueryCustomParameter(VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_FLAG_MOVIMENTACAO)  , QueryCustomParameter.DIFERENTE, EstruturaEquipamentoRevisao.APAGADO, HQLHelper.NOT_RESOLVE_TYPE, EstruturaEquipamentoRevisao.MODIFICADO));
            }

            if ( this.status != null ){
                parameters.add(new QueryCustomParameter(VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_STATUS), this.status));
            }
            
            if ( this.revisao != null ){
                parameters.add(new QueryCustomParameter(VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID,EstruturaEquipamentoRevisaoPK.PROP_REVISAO), this.revisao));
            }

            
            List<EstruturaEquipamentoRevisao> list = LoadManager.getInstance(EstruturaEquipamentoRevisao.class).setLazyMode(true).setParameters(parameters).start().getList();
            
            Collections.sort(list, new Comparator<EstruturaEquipamentoRevisao>(){
                public int compare(EstruturaEquipamentoRevisao ee1, EstruturaEquipamentoRevisao ee2){
                    if( ee1.getId().getItem().compareTo(ee2.getId().getItem()) == 0){
                        return ee1.getId().getComponente().getDescricao().compareTo(ee2.getId().getComponente().getDescricao());
                    }else{
                        return ee1.getId().getItem().compareTo(ee2.getId().getItem());
                    }
                }
            });
            
            estruturaEquipamentoFilhoSet = new LinkedHashSet<EstruturaEquipamentoRevisao>( list );                

        }
        return estruturaEquipamentoFilhoSet;
    }
}
