package br.com.ksisolucoes.bo.consorcio.consorcioprestador;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.CadastroPrestadorDTO;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.CpfCnpJValidator;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaMaterial;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.consorcio.ConsorcioPrestador;
import ch.lambdaj.Lambda;

import static br.com.celk.util.CollectionUtils.isNotNullEmpty;

/**
 * <AUTHOR>
 */
public class CadastrarPrestador extends AbstractCommandTransaction {

    private final CadastroPrestadorDTO dto;
    private ConsorcioPrestador consorcioPrestador;

    public CadastrarPrestador(CadastroPrestadorDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (dto.getConsorcioPrestador().getBancoPrestador() != null) {
            if (dto.getConsorcioPrestador().getTipoPessoaConta().equals(Pessoa.PESSOA_FISICA)) {
                if (!CpfCnpJValidator.CPFIsValid(StringUtil.getDigits(dto.getConsorcioPrestador().getCpfCnpjConta()))) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_cpf_cnpj_invalidos"));
                }
            } else if (dto.getConsorcioPrestador().getTipoPessoaConta().equals(Pessoa.PESSOA_JURIDICA)) {
                if (!CpfCnpJValidator.CNPJIsValid(StringUtil.getDigits(dto.getConsorcioPrestador().getCpfCnpjConta()))) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_cpf_cnpj_invalidos"));
                }
            }
        }

        if (dto.getConsorcioPrestador().getEmpresaPrestador().getEmpresaMaterial() == null) {
            dto.getConsorcioPrestador().getEmpresaPrestador().setEmpresaMaterial(new EmpresaMaterial(dto.getConsorcioPrestador().getCodigo()));
        }

        dto.getConsorcioPrestador().getEmpresaPrestador().setTipoUnidade(Empresa.TIPO_ESTABELECIMENTO_PRESTADOR_SERVICO);

        BOFactory.save(dto.getConsorcioPrestador().getEmpresaPrestador());

        Empresa consorcio = BOFactory.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("consorcioPadrao");
        if (consorcio == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_parametro_gem_X_nao_definido", "consorcioPadrao"));
        }

        dto.getConsorcioPrestador().setEmpresaConsorcio(consorcio);

        if (dto.getConsorcioPrestador().getStatus() == null) {
            dto.getConsorcioPrestador().setStatus(ConsorcioPrestador.StatusConsorcioPrestador.ATIVO.value());
        }

        if (dto.getConsorcioPrestador().getCpfCnpjConta() != null) {
            dto.getConsorcioPrestador().setCpfCnpjConta(StringUtil.getDigits(dto.getConsorcioPrestador().getCpfCnpjConta()));
        }

        consorcioPrestador = BOFactory.save(dto.getConsorcioPrestador());

        if (CollectionUtils.isNotNullEmpty(dto.getConsorcioPrestadorEditalList())) {
            Lambda.forEach(dto.getConsorcioPrestadorEditalList()).getConsorcioPrestadorEdital().setConsorcioPrestador(consorcioPrestador);
            new CadastrarEditaisPrestador(dto.getConsorcioPrestadorEditalList()).start();
        }

        if (CollectionUtils.isNotNullEmpty(dto.getConsorcioPrestadorEditalExcludeList())) {
            new ExcluirEditaisPrestador(dto.getConsorcioPrestadorEditalExcludeList()).start();
        }

        if (CollectionUtils.isNotNullEmpty(dto.getConsorcioPrestadorAgendaList())) {
            Lambda.forEach(dto.getConsorcioPrestadorAgendaList()).getConsorcioPrestadorAgenda().setConsorcioPrestador(consorcioPrestador);
            new CadastrarAgendasPrestador(dto.getConsorcioPrestadorAgendaList()).start();
        }

        if (CollectionUtils.isNotNullEmpty(dto.getConsorcioPrestadorAgendaExcludeList())) {
            new ExcluirAgendaPrestador(dto.getConsorcioPrestadorAgendaExcludeList()).start();
        }

        if (isNotNullEmpty(dto.getConsorcioEmpresaTerceiroDto().getEmpresaTerceiroResponsavelList())) {
            Lambda.forEach(dto.getConsorcioEmpresaTerceiroDto()).setEmpresa(consorcioPrestador.getEmpresaPrestador());
            Lambda.forEach(dto.getConsorcioEmpresaTerceiroDto().getEmpresaTerceiroResponsavelList()).setEmpresa(consorcioPrestador.getEmpresaPrestador());
            BOFactory.getBO(ConsorcioFacade.class).salvarEmpresaTerceiroResponsavel(dto.getConsorcioEmpresaTerceiroDto());
        } else {
            if (dto.getConsorcioEmpresaTerceiroDto().getEmpresaTerceiroResponsavelList() != null) {
                dto.getConsorcioEmpresaTerceiroDto().setEmpresa(consorcioPrestador.getEmpresaPrestador());
                BOFactory.getBO(ConsorcioFacade.class).salvarEmpresaTerceiroResponsavel(dto.getConsorcioEmpresaTerceiroDto());
            }
        }


    }

    public ConsorcioPrestador getConsorcioPrestador() {
        return consorcioPrestador;
    }

}
