package br.com.ksisolucoes.bo.agendamento;

import br.com.celk.bo.service.sms.interfaces.facade.SmsFacade;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.MensagemDTO;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.MensagemSmsDTO;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.bo.service.sms.QuerySmsRespondidos;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.service.sms.SmsControleIntegracao;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ProcessoRespostaSMS extends AbstractCommandTransaction<ProcessoRespostaSMS> {

    @Override
    public void execute() throws DAOException, ValidacaoException {
        QuerySmsRespondidos query = new QuerySmsRespondidos();
        query.start();

        List<SmsControleIntegracao> listMensagens = new ArrayList<SmsControleIntegracao>(query.getResult());
        Loggable.log.info("Leitura da Caixa de Entrada dos Agendamentos");
        try {
            MensagemSmsDTO mensagens = new MensagemSmsDTO();
            for (SmsControleIntegracao smsControleIntegracao : listMensagens) {
                BOFactory.getBO(AgendamentoFacade.class).processarRespostaSMS(mensagens, smsControleIntegracao);
            }

            if (!mensagens.getMsgAvisos().isEmpty()) {
                Usuario usuarioResponsavelAnaliseResposta = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("UsuarioResponsavelAnaliseRespostasUsuarios");
                if (usuarioResponsavelAnaliseResposta != null) {
                    for (Map.Entry<String, List<String>> entrySet : mensagens.getMsgAvisos().entrySet()) {
                        String assunto = entrySet.getKey();
                        List<String> msgs = entrySet.getValue();
                        Collections.sort(msgs);

                        StringBuilder mensagem = new StringBuilder();
                        mensagem.append(assunto).append("\n");
                        for (String msg : msgs) {
                            mensagem.append("\n").append(msg);
                        }

                        MensagemDTO mensagemDTO = new MensagemDTO();
                        mensagemDTO.setAssunto(Bundle.getStringApplication("msg_resposta_SMS"));
                        mensagemDTO.setUsuarios(Arrays.asList(usuarioResponsavelAnaliseResposta));
                        mensagemDTO.setMensagem(mensagem.toString());

                        BOFactory.getBO(ComunicacaoFacade.class).enviarMensagem(mensagemDTO);
                    }
                }
            }

            if (!mensagens.getMsgErros().isEmpty()) {
                Usuario usuarioResponsavelAgendamento = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("UsuarioResponsavelAgendamentoSolicitacao");
                if (usuarioResponsavelAgendamento != null) {
                    for (Map.Entry<String, List<String>> entrySet : mensagens.getMsgErros().entrySet()) {
                        String assunto = entrySet.getKey();
                        List<String> msgs = entrySet.getValue();
                        Collections.sort(msgs);

                        StringBuilder mensagem = new StringBuilder();
                        mensagem.append(assunto).append("\n");
                        for (String msg : msgs) {
                            mensagem.append("\n").append(msg);
                        }

                        MensagemDTO mensagemDTO = new MensagemDTO();
                        mensagemDTO.setAssunto(Bundle.getStringApplication("msg_problemas_resposta_SMS"));
                        mensagemDTO.setUsuarios(Arrays.asList(usuarioResponsavelAgendamento));
                        mensagemDTO.setMensagem(mensagem.toString());

                        BOFactory.getBO(ComunicacaoFacade.class).enviarMensagem(mensagemDTO);
                    }
                }
            }

        } catch (Throwable ex) {
            BOFactory.getBO(SmsFacade.class).enviarEmailErroSms(ex);
        }
    }

}
