/*
 * Created on 26/08/2004
 *
 */
package br.com.ksisolucoes.bo.entradas.estoque.tipodocumento;

import br.com.ksisolucoes.bo.command.SaveVO;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Parametro;

/**
 * <AUTHOR>
 *
 */
public class SaveTipoDocumento extends SaveVO{
    
    /**
     *
     */
    public SaveTipoDocumento(Object vo) {
        super( vo );
    }
    
    /* (non-Javadoc)
     * @see br.com.ksisolucoes.command.AbstractCommand#getInterfaceChave()
     */
    
    protected void depoisSave() throws ValidacaoException, DAOException {
        /*
         * WORKAROUND PARA ATUALIZAO DE PARAMETRO
         * ----------------------------------------
         * Sempre que um tipo de documento  alterado, os parametros devem ser recarregados
         * para que seja garantido que todos os tipos de documentos estejam apropriadamente
         * sincronizados com a base de dados.
         *---------------------------------------------------------------------*/
        Parametro param = (Parametro) getSession().get(Parametro.class, Parametro.PARAMETRO_PADRAO);
        param.setDataAtualizacao(Data.getDataAtual());
        
        /*---------------------------------------------------------------------*/
    }
    
}