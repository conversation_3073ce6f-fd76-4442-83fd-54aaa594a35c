package br.com.ksisolucoes.bo.integracao.fastmedic.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.basico.Empresa;
import ch.lambdaj.Lambda;

import java.util.List;
import java.util.Map;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;

public class QueryListaEmpresas extends CommandQuery<QueryListaEmpresas> {

    private List<Empresa> empresaList;

    public QueryListaEmpresas() {
    }

    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(Empresa.class.getName());
        Empresa proxy = Lambda.on(Empresa.class);

        hql.addToSelect("empresa.codigo", path(proxy.getCodigo()));
        hql.addToSelect("empresa.cnes", path(proxy.getCnes()));
        hql.addToSelect("empresa.descricao", path(proxy.getDescricao()));

        hql.addToFrom(Empresa.class.getName() + " empresa");

        hql.addToWhereWhithAnd(" empresa.atividade = " + Empresa.ATIVIDADE_PRONTO_ATENDIMENTO);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.empresaList = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    public List<Empresa> getResult() {
        return this.empresaList;
    }
}