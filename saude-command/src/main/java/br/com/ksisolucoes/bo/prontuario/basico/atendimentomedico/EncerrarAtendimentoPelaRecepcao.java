package br.com.ksisolucoes.bo.prontuario.basico.atendimentomedico;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;

/**
 *
 * <AUTHOR>
 */
public class EncerrarAtendimentoPelaRecepcao extends AbstractCommandTransaction {

    private Long codigoAtendimento;
    private Profissional profissional;

    public EncerrarAtendimentoPelaRecepcao(Long codigoAtendimento, Profissional profissional) {
        this.codigoAtendimento = codigoAtendimento;
        this.profissional = profissional;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        new FinalizarAtendimentoPelaRecepcao(codigoAtendimento, profissional).start();
        
//        new GerarProximoAtendimentoPadrao(codigoAtendimento).start();
        
        new LiberarLeitoQuarto(codigoAtendimento).start();
        
        new GerarAtendimentoInformacao(codigoAtendimento).start();
        
    }
}
