package br.com.ksisolucoes.bo.agendamento.exame;

import br.com.ksisolucoes.agendamento.exame.dto.CancelamentoAgendaGradeAtendimentoHorarioDTO;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaExame;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import java.util.List;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class CancelarAgendamentoExame extends AbstractCommandTransaction {

    private Exame exame;
    private String motivo;

    public CancelarAgendamentoExame(Exame exame, String motivo) {
        this.exame = exame;
        this.motivo = motivo;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        AgendaExame agendaExame = (AgendaExame) getSession().createCriteria(AgendaExame.class)
                .add(Restrictions.eq(AgendaExame.PROP_EXAME, exame))
                .uniqueResult();
        
        if (agendaExame != null) {
            List<AgendaGradeAtendimentoHorario> agahList = getSession().createCriteria(AgendaGradeAtendimentoHorario.class)
                    .add(Restrictions.eq(AgendaGradeAtendimentoHorario.PROP_AGENDA_EXAME, agendaExame))
                    .add(Restrictions.eq(AgendaGradeAtendimentoHorario.PROP_STATUS, AgendaGradeAtendimentoHorario.STATUS_AGENDADO))
                    .list();

            if(CollectionUtils.isNotNullEmpty(agahList)){
                CancelamentoAgendaGradeAtendimentoHorarioDTO cancelamentoDTO = new CancelamentoAgendaGradeAtendimentoHorarioDTO();
                cancelamentoDTO.setAgendaGradeAtendimentoHorarioList(agahList);
                cancelamentoDTO.setReabrirAgenda(RepositoryComponentDefault.SIM_LONG);
                cancelamentoDTO.setMotivoCancelamento(motivo);

                BOFactory.getBO(AgendamentoFacade.class).cancelarAgendamentos(cancelamentoDTO);
            }
            
            BOFactory.delete(agendaExame);
        }
    }
}
