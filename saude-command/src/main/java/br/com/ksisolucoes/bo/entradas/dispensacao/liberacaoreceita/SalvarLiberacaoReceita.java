package br.com.ksisolucoes.bo.entradas.dispensacao.liberacaoreceita;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.dispensacao.LiberacaoReceita;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.util.List;

public class SalvarLiberacaoReceita extends AbstractCommandTransaction {

    private LiberacaoReceita liberacaoReceita;
    private List<Produto> produtos;

    public SalvarLiberacaoReceita(LiberacaoReceita liberacaoReceita, List<Produto> produtos) {
        this.liberacaoReceita = liberacaoReceita;
        this.produtos = produtos;
    }

    private boolean exiteLiberacao(Long codigo, Produto produto) {
        return LoadManager.getInstance(LiberacaoReceita.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LiberacaoReceita.PROP_CODIGO), codigo))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LiberacaoReceita.PROP_PRODUTO), produto))
                .start().getVO() != null;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (CollectionUtils.isAllEmpty(produtos)) {
            BOFactory.getBO(CadastroFacade.class).save(liberacaoReceita);
        } else {
            for (Produto produto : produtos) {
                if (liberacaoReceita.getCodigo() != null && exiteLiberacao(liberacaoReceita.getCodigo(), produto)) {
                    BOFactory.save(liberacaoReceita);
                } else {
                    LiberacaoReceita lr = new LiberacaoReceita();
                    lr.setDispensacaoMedicamento(liberacaoReceita.getDispensacaoMedicamento());
                    lr.setDispensacaoMedicamentoItem(liberacaoReceita.getDispensacaoMedicamentoItem());
                    lr.setEmpresa(liberacaoReceita.getEmpresa());
                    lr.setObservacao(liberacaoReceita.getObservacao());
                    lr.setQuantidade(liberacaoReceita.getQuantidade());
                    lr.setStatus(liberacaoReceita.getStatus());
                    lr.setTipoLiberacao(liberacaoReceita.getTipoLiberacao());
                    lr.setUsuarioCadsus(liberacaoReceita.getUsuarioCadsus());

                    lr.setDataUsuario(DataUtil.getDataAtual());
                    lr.setUsuario(getSessao().<Usuario>getUsuario());

                    lr.setProduto(produto);

                    BOFactory.save(lr);
                }
            }
        }
    }
}
