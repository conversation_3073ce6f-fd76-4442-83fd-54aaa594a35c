package br.com.ksisolucoes.bo.basico.produtosolicitadomovimento;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.ProdutoSolicitadoMovimento;
import br.com.ksisolucoes.vo.controle.Usuario;

/**
 *
 * <AUTHOR>
 */
public class SaveProdutoSolicitadoMovimento extends SaveVO {

    private ProdutoSolicitadoMovimento produtoSolicitadoMovimento;

    public SaveProdutoSolicitadoMovimento(Object vo) {
        super(vo);
        this.produtoSolicitadoMovimento = (ProdutoSolicitadoMovimento) vo;
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (produtoSolicitadoMovimento.getDataCadastro() == null) {
            produtoSolicitadoMovimento.setDataCadastro(Data.getDataAtual());
        }
        if (produtoSolicitadoMovimento.getDataMovimento() == null) {
            produtoSolicitadoMovimento.setDataMovimento(Data.getDataAtual());
        }
        if (produtoSolicitadoMovimento.getEmpresa() == null) {
            produtoSolicitadoMovimento.setEmpresa(getSessao().<Empresa>getEmpresa());
        }
        if (produtoSolicitadoMovimento.getUsuario() == null) {
            produtoSolicitadoMovimento.setUsuario(getSessao().<Usuario>getUsuario());
        }
    }

    public ProdutoSolicitadoMovimento getProdutoSolicitadoMovimento() {
        return produtoSolicitadoMovimento;
    }

}
