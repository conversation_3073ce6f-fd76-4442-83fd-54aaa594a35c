package br.com.ksisolucoes.bo.prontuario.basico.receituario;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.ReceituarioItemComponentesDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ReceituarioFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.Receituario;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItemComponente;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SalvarPrescricaoSN extends AbstractCommandTransaction {

    private Long codigoReceituario;
    private Profissional profissional;
    private Atendimento atendimento;
    private List<ReceituarioItemComponentesDTO> lstReceituarioItemComponentesDTO;

    public SalvarPrescricaoSN(List<ReceituarioItemComponentesDTO> lstReceituarioItemComponentesDTO, Atendimento atendimento) {
        this.lstReceituarioItemComponentesDTO = lstReceituarioItemComponentesDTO;
        this.atendimento = atendimento;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (CollectionUtils.isEmpty(lstReceituarioItemComponentesDTO)) {
            return;
        }

        Receituario receituario = new Receituario();
        receituario.setAtendimento(atendimento);

        receituario.setProfissionalAprazamento(SessaoAplicacaoImp.getInstance().getUsuario().getProfissional());

        for (ReceituarioItemComponentesDTO item : lstReceituarioItemComponentesDTO) {
            cloneAndUpdateReceituarioItem(item);
        }

        if (profissional != null) {
            receituario.setProfissional(profissional);
        }
        codigoReceituario = BOFactory.getBO(ReceituarioFacade.class).salvarReceituarioPadrao(receituario, lstReceituarioItemComponentesDTO);
    }

    private void cloneAndUpdateReceituarioItem(ReceituarioItemComponentesDTO item) {
        ReceituarioItem receituarioItemOrigem = LoadManager.getInstance(ReceituarioItem.class)
                .addProperties(new HQLProperties(ReceituarioItem.class).getProperties())
                .addProperties(new HQLProperties(Receituario.class, ReceituarioItem.PROP_RECEITUARIO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(ReceituarioItem.PROP_CODIGO, item.getReceituarioItem().getCodigo()))
                .start().getVO();
        if (item.getReceituarioItem().getJustificativaAdep() != null) {
            receituarioItemOrigem.setJustificativaAdep(item.getReceituarioItem().getJustificativaAdep());
        }

        if (receituarioItemOrigem.getReceituario() != null) {
            profissional = receituarioItemOrigem.getReceituario().getProfissional();
        }

        ReceituarioItem receituarioItemNovo = VOUtils.cloneObject(receituarioItemOrigem);
        receituarioItemNovo.setReceituario(null);
        receituarioItemNovo.setFlagSeNecessario(null);
        receituarioItemNovo.setFlagAcm(null);
        receituarioItemNovo.setQuantidadeDispensada(0L);
        receituarioItemNovo.setQuantidadePrescrita(null);
        receituarioItemNovo.setFrequencia(ReceituarioItem.FREQUENCIA_DIA);
        receituarioItemNovo.setTipoPrescricao(ReceituarioItem.TipoPrescricao.ADEP.value());
        receituarioItemNovo.setAtendimentoMedicamento(null);
        receituarioItemNovo.setReceituarioItemOrigem(receituarioItemOrigem);
        item.setReceituarioItemComponenteList(getReceituarioItemComponenteList(receituarioItemNovo, receituarioItemOrigem.getCodigo()));

        item.setReceituarioItem(receituarioItemNovo);
    }

    private List<ReceituarioItemComponente> getReceituarioItemComponenteList(ReceituarioItem receituarioItem, Long codigo) {
        List<ReceituarioItemComponente> lstReceituarioItemComponente = LoadManager.getInstance(ReceituarioItemComponente.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItemComponente.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_CODIGO), codigo))
                .start().getList();

        for (ReceituarioItemComponente item : lstReceituarioItemComponente) {
            item.setCodigo(null);
            item.setVersion(null);
            item.setQuantidadeDispensada(0L);
            item.setReceituarioItem(receituarioItem);
        }

        return lstReceituarioItemComponente;
    }

    public Long getCodigoReceituario() {
        return codigoReceituario;
    }
}
