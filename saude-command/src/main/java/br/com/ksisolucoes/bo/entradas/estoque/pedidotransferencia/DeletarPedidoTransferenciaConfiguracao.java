package br.com.ksisolucoes.bo.entradas.estoque.pedidotransferencia;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.materiais.pedidotransferencia.ConfiguracaoPedidoTransferenciaDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferenciaConfiguracao;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferenciaConfiguracaoItem;
import br.com.ksisolucoes.vo.vacina.EloTipoVacinaDose;
import ch.lambdaj.Lambda;

import java.util.List;

/**
 * <AUTHOR> Ramos
 */
public class DeletarPedidoTransferenciaConfiguracao extends AbstractCommandTransaction {

    private final PedidoTransferenciaConfiguracao configuracaoPedido;

    public DeletarPedidoTransferenciaConfiguracao(PedidoTransferenciaConfiguracao configuracaoPedido) {
        this.configuracaoPedido = configuracaoPedido;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        List<PedidoTransferenciaConfiguracaoItem> itemConfiguracaoList = LoadManager.getInstance(PedidoTransferenciaConfiguracaoItem.class)
                .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferenciaConfiguracaoItem.PROP_PEDIDO_TRANSFERENCIA_CONFIGURACAO, configuracaoPedido))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(itemConfiguracaoList)) {
            for (PedidoTransferenciaConfiguracaoItem pedidoTransferenciaConfiguracaoItem : itemConfiguracaoList) {
                BOFactory.delete(pedidoTransferenciaConfiguracaoItem);
            }
        }

        BOFactory.delete(configuracaoPedido);

    }


}