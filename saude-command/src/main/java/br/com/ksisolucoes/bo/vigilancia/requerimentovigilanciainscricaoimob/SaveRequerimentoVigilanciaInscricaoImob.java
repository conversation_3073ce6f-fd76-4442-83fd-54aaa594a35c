package br.com.ksisolucoes.bo.vigilancia.requerimentovigilanciainscricaoimob;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaInscricaoImob;
import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class SaveRequerimentoVigilanciaInscricaoImob extends SaveVO<RequerimentoVigilanciaInscricaoImob> {

    public SaveRequerimentoVigilanciaInscricaoImob(RequerimentoVigilanciaInscricaoImob vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (StringUtils.trimToNull(vo.getNumeroInscricaoImobiliaria())  == null) {
            throw new ValidacaoException("Informe a Inscrição Imobiliária");
//            vo.setNumeroInscricaoImobiliaria(StringUtil.getDigits(vo.getNumeroInscricaoImobiliaria()));
        }
    }
}