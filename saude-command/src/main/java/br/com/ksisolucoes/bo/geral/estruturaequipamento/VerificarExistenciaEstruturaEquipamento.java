package br.com.ksisolucoes.bo.geral.estruturaequipamento;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresaPK;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.geral.EstruturaEquipamento;

/**
 * <AUTHOR>
 *
 * Verifica se o produto possui uma <code>EstruturaEquipamento</code> montada;
 */
public class VerificarExistenciaEstruturaEquipamento extends AbstractCommandTransaction {

    private Produto produto;
    private boolean retorno;    
    
    public VerificarExistenciaEstruturaEquipamento(Produto produto) {
        this.produto = produto;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        if(LoadManager.getInstance(EstruturaEquipamento.class)
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(EstruturaEquipamento.PROP_ID,EstoqueEmpresaPK.PROP_PRODUTO), produto))
                .start().getList().size() > 0){
            retorno = true;                
        } else {
            retorno = false;
        }
    }
    
    public boolean isExistenciaEstruturaEquipamento() {
        return retorno;
    }

}
