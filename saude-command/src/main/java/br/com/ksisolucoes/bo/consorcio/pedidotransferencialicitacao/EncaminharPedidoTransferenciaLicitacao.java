package br.com.ksisolucoes.bo.consorcio.pedidotransferencialicitacao;

import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.ValorReservadoDTO;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.MovimentoEstoqueFacade;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.ReservaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.parametrogem.IParameterModuleContainer;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.*;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import ch.lambdaj.Lambda;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;

import java.math.MathContext;
import java.util.Date;
import java.util.List;

import static br.com.ksisolucoes.system.factory.BOFactory.save;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;

/**
 *
 * <AUTHOR>
 */
public class EncaminharPedidoTransferenciaLicitacao extends AbstractCommandTransaction {

    private PedidoTransferenciaLicitacaoEntrega pedidoTransferenciaLicitacaoEntrega;

    public EncaminharPedidoTransferenciaLicitacao(PedidoTransferenciaLicitacaoEntrega pedidoTransferenciaLicitacaoEntrega) {
        this.pedidoTransferenciaLicitacaoEntrega = pedidoTransferenciaLicitacaoEntrega;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        Date dataAtual = DataUtil.getDataAtual();
        pedidoTransferenciaLicitacaoEntrega.setStatus(PedidoTransferenciaLicitacaoEntrega.StatusPedidoTransferenciaLicitacaoEntrega.ENVIADA.value());
        pedidoTransferenciaLicitacaoEntrega.setDataEntrega(dataAtual);
        pedidoTransferenciaLicitacaoEntrega.setUsuarioEntrega(getSessao().<Usuario>getUsuario());
        BOFactory.save(pedidoTransferenciaLicitacaoEntrega);

        SubConta subConta = null;
        Conta conta = null;
        Long ano = null;
        TipoMovimentacao tipoMovimentacao = null;
        if (pedidoTransferenciaLicitacaoEntrega.getPedidoTransferenciaLicitacao() != null) {
            pedidoTransferenciaLicitacaoEntrega.getPedidoTransferenciaLicitacao().setStatus(PedidoTransferenciaLicitacao.StatusPedidoTransferenciaLicitacao.ENCAMINHADO.value());
            PedidoTransferenciaLicitacao pedidoTransferenciaLicitacao = BOFactory.save(pedidoTransferenciaLicitacaoEntrega.getPedidoTransferenciaLicitacao());

            if(PedidoTransferenciaLicitacao.SituacaoControleFinanceiro.APROVADO.value().equals(pedidoTransferenciaLicitacao.getSituacaoControleFinanceiro())
                    && pedidoTransferenciaLicitacao.getSubContaMedicamento() != null && pedidoTransferenciaLicitacao.getSubContaMedicamento().getCodigo() != null) {
                subConta = HibernateUtil.lockTable(SubConta.class, pedidoTransferenciaLicitacao.getSubContaMedicamento().getCodigo());
                conta = HibernateUtil.lockTable(Conta.class, subConta.getConta().getCodigo());
                ano = pedidoTransferenciaLicitacaoEntrega.getPedidoTransferenciaLicitacao().getAnoCadastro();
                tipoMovimentacao = getParametroContainer(Modulos.CONSORCIO).getParametro("tipoMovimentoPagamentoMedicamento");

                List<PedidoTransferenciaLicitacaoItem> itemList = LoadManager.getInstance(PedidoTransferenciaLicitacaoItem.class)
                        .addProperty(PedidoTransferenciaLicitacaoItem.PROP_QUANTIDADE)
                        .addProperty(PedidoTransferenciaLicitacaoItem.PROP_QUANTIDADE_ENVIADA)
                        .addProperty(PedidoTransferenciaLicitacaoItem.PROP_PRECO_UNITARIO)
                        .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacaoItem.PROP_STATUS, PedidoTransferenciaLicitacaoItem.StatusPedidoTransferenciaLicitacaoItem.ABERTO.value()))
                        .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, pedidoTransferenciaLicitacao))
                        .start().getList();

                if(CollectionUtils.isNotNullEmpty(itemList)) {
                    BOFactory.getBO(ConsorcioFacade.class).removerValorReservado(new ValorReservadoDTO(subConta, ano, new Dinheiro(Coalesce.asDouble(Lambda.sum(itemList, Lambda.on(PedidoTransferenciaLicitacaoItem.class).getTotalItem()))).doubleValue()));
                }
            }
        }

        List<Reserva> reservas = getSession().createCriteria(Reserva.class)
            .add(Restrictions.eq(VOUtils.montarPath(Reserva.PROP_DOCUMENTO), pedidoTransferenciaLicitacaoEntrega.getCodigo()))
            .add(Restrictions.eq(VOUtils.montarPath(Reserva.PROP_TIPO_RESERVA), Reserva.TIPO_RESERVA_TRANSFERENCIA_LICITACAO))
            .createCriteria(Reserva.PROP_GRUPO_ESTOQUE)
                .createCriteria(VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_ESTOQUE_EMPRESA))
            .list();
        
        IParameterModuleContainer modulo = getParametroContainer(Modulos.CONSORCIO);
        
        TipoDocumento tipoDocumentoSaidaAlmoxarifado = modulo.getParametro("tipoDocumentoSaidaAlmoxarifado");
        
        for (Reserva reserva : reservas) {
            BOFactory.getBO(ReservaFacade.class).baixarReserva(reserva);
            
            MovimentoEstoque movimentoEstoque = new MovimentoEstoque();
            MovimentoEstoquePK id = new MovimentoEstoquePK(reserva.getGrupoEstoque().getId().getEstoqueEmpresa().getId().getEmpresa(), null);
            movimentoEstoque.setId(id);

            movimentoEstoque.setEmpresaDestino(pedidoTransferenciaLicitacaoEntrega.getPedidoTransferenciaLicitacao().getEmpresaConsorciado());
            movimentoEstoque.setDeposito(reserva.getGrupoEstoque().getRoDeposito());
            movimentoEstoque.setTipoDocumento(tipoDocumentoSaidaAlmoxarifado);
            movimentoEstoque.setProduto(reserva.getGrupoEstoque().getId().getEstoqueEmpresa().getId().getProduto());
            movimentoEstoque.setNumeroDocumento(reserva.getDocumento().toString());
            movimentoEstoque.setItemDocumento(reserva.getItem());
            movimentoEstoque.setGrupoEstoque(reserva.getGrupoEstoque().getId().getGrupo());
            movimentoEstoque.setQuantidade(reserva.getQuantidade());
            
            BOFactory.getBO(MovimentoEstoqueFacade.class).gerarMovimentoEstoque(movimentoEstoque);
        }

        List<PedidoTransferenciaLicitacaoEntregaItem> itens = getSession().createCriteria(PedidoTransferenciaLicitacaoEntregaItem.class)
            .add(Restrictions.eq(PedidoTransferenciaLicitacaoEntregaItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO_ENTREGA, pedidoTransferenciaLicitacaoEntrega))
            .createCriteria(PedidoTransferenciaLicitacaoEntregaItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO_ITEM)
            .list();

        Double totalItensSubConta = 0D;
        for (PedidoTransferenciaLicitacaoEntregaItem ptlei : itens) {
            Long novaQuantidadeEnviada = new Dinheiro(Coalesce.asLong(ptlei.getPedidoTransferenciaLicitacaoItem().getQuantidadeEnviada())).somar(new Dinheiro(ptlei.getQuantidade())).longValue();
            
            ptlei.getPedidoTransferenciaLicitacaoItem().setQuantidadeEnviada(novaQuantidadeEnviada);
            ptlei.getPedidoTransferenciaLicitacaoItem().setQuantidadeSeparada(0L);
            
            if (ptlei.getPedidoTransferenciaLicitacaoItem().getQuantidade().equals(ptlei.getPedidoTransferenciaLicitacaoItem().getQuantidadeEnviada())) {
                ptlei.getPedidoTransferenciaLicitacaoItem().setStatus(PedidoTransferenciaLicitacaoItem.StatusPedidoTransferenciaLicitacaoItem.FECHADO.value());
            }

            ptlei.getPedidoTransferenciaLicitacaoItem().setFlagSeparado(RepositoryComponentDefault.NAO_LONG);

            totalItensSubConta = new Dinheiro(totalItensSubConta).somar(ptlei.getValor()).doubleValue();
            save(ptlei.getPedidoTransferenciaLicitacaoItem());


            EloPedidoLicitacaoItemPedidoTransferenciaLicitacaoItem proxy = Lambda.on(EloPedidoLicitacaoItemPedidoTransferenciaLicitacaoItem.class);
            EloPedidoLicitacaoItemPedidoTransferenciaLicitacaoItem elo = (EloPedidoLicitacaoItemPedidoTransferenciaLicitacaoItem) getSession().createCriteria(EloPedidoLicitacaoItemPedidoTransferenciaLicitacaoItem.class)
                    .add(Restrictions.eq(path(proxy.getPedidoTransferenciaLicitacaoItem()), ptlei.getPedidoTransferenciaLicitacaoItem()))
                    .uniqueResult();
            elo.getPedidoLicitacaoItem().setQuantidadeRecebida(new Dinheiro(Coalesce.asLong(elo.getPedidoLicitacaoItem().getQuantidadeRecebida())).somar(ptlei.getQuantidade().doubleValue()).longValue());
            elo.getPedidoLicitacaoItem().setValorRecebido(new Dinheiro(Coalesce.asDouble(elo.getPedidoLicitacaoItem().getValorRecebido())).somar(ptlei.getValor()).doubleValue());
            save(elo.getPedidoLicitacaoItem());
        }

        if(tipoMovimentacao != null){
            /*
                De acordo com a alteração #19835, não será mais necessário realizar essa validação. A conta ficará negativa e será administrada pelo consórcio.
             */
//            Double saldoDisponivelSubConta = new Dinheiro(subConta.getSaldoAtual()).subtrair(subConta.getValorReservado()).doubleValue();
//            if(Coalesce.asDouble(saldoDisponivelSubConta).compareTo(Coalesce.asDouble(Coalesce.asDouble(totalItensSubConta))) < 0) {
//                throw new ValidacaoException(Bundle.getStringApplication("msg_saldo_consorciado_insuficiente_valor_guia_saldo_disponivel", totalItensSubConta, saldoDisponivelSubConta));
//            }

            BOFactory.getBO(ConsorcioFacade.class).gerarMovimentacao(subConta, ano, tipoMovimentacao, totalItensSubConta, "Nº da Guia " + pedidoTransferenciaLicitacaoEntrega.getCodigo());

            List<PedidoTransferenciaLicitacaoItem> itemList = LoadManager.getInstance(PedidoTransferenciaLicitacaoItem.class)
                    .addProperty(PedidoTransferenciaLicitacaoItem.PROP_QUANTIDADE)
                    .addProperty(PedidoTransferenciaLicitacaoItem.PROP_QUANTIDADE_ENVIADA)
                    .addProperty(PedidoTransferenciaLicitacaoItem.PROP_PRECO_UNITARIO)
                    .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacaoItem.PROP_STATUS, PedidoTransferenciaLicitacaoItem.StatusPedidoTransferenciaLicitacaoItem.ABERTO.value()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, PedidoTransferenciaLicitacao.PROP_CODIGO), pedidoTransferenciaLicitacaoEntrega.getPedidoTransferenciaLicitacao().getCodigo()))
                    .start().getList();

            if(CollectionUtils.isNotNullEmpty(itemList)) {
                Double totalPrecoItens = Lambda.sum(itemList, Lambda.on(PedidoTransferenciaLicitacaoItem.class).getTotalItem());
                BOFactory.getBO(ConsorcioFacade.class).gerarValorReservado(new ValorReservadoDTO(subConta, pedidoTransferenciaLicitacaoEntrega.getPedidoTransferenciaLicitacao().getAnoCadastro(), Coalesce.asDouble(totalPrecoItens)));
            }

//          BOFactory.getBO(ConsorcioFacade.class).atualizarSituacaoControleFinanceiroPedidoTransferenciaLicitacao(pedidoTransferenciaLicitacaoEntrega.getPedidoTransferenciaLicitacao(), subConta);
        }

        Long count = (Long) getSession().createCriteria(PedidoTransferenciaLicitacaoItem.class)
                .setProjection(Projections.count(PedidoTransferenciaLicitacaoItem.PROP_CODIGO))
                .add(Restrictions.eq(VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, PedidoTransferenciaLicitacao.PROP_CODIGO), pedidoTransferenciaLicitacaoEntrega.getPedidoTransferenciaLicitacao().getCodigo()))
                .add(Restrictions.eq(VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_STATUS), PedidoTransferenciaLicitacaoItem.StatusPedidoTransferenciaLicitacaoItem.ABERTO.value()))
                .uniqueResult();
        
        if (count==0) {
            pedidoTransferenciaLicitacaoEntrega.getPedidoTransferenciaLicitacao().setStatus(PedidoTransferenciaLicitacao.StatusPedidoTransferenciaLicitacao.FECHADO.value());
            pedidoTransferenciaLicitacaoEntrega.getPedidoTransferenciaLicitacao().setUsuarioFechamento(getSessao().<Usuario>getUsuario());
            pedidoTransferenciaLicitacaoEntrega.getPedidoTransferenciaLicitacao().setDataFechamento(dataAtual);
            save(pedidoTransferenciaLicitacaoEntrega.getPedidoTransferenciaLicitacao());
        }
    }

    public PedidoTransferenciaLicitacaoEntrega getPedidoTransferenciaLicitacaoEntrega() {
        return pedidoTransferenciaLicitacaoEntrega;
    }

}
