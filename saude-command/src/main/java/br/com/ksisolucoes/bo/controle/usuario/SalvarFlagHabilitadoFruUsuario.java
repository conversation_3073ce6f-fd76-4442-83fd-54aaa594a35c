package br.com.ksisolucoes.bo.controle.usuario;


import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;

/**
 * Created by <PERSON> on 19/04/2023.
 */
public class SalvarFlagHabilitadoFruUsuario extends AbstractCommandTransaction {

    private Usuario usuario;

    public SalvarFlagHabilitadoFruUsuario(Usuario usuario) {
        this.usuario = usuario;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        usuario.setFlagHabilitaAppFru(RepositoryComponentDefault.SIM);
        getSession().saveOrUpdate(usuario);
    }
}
