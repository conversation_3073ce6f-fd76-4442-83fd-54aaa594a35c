/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.agendamento.exame;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CalcularMediaEsperaAgendamentoQuery extends CommandQuery{

    private TipoProcedimento tipoProcedimento;
    private DatePeriod periodo;
    private Long media;

    public CalcularMediaEsperaAgendamentoQuery(TipoProcedimento tipoProcedimento, DatePeriod periodo) {
        this.tipoProcedimento = tipoProcedimento;
        this.periodo = periodo;
    }

    @Override
    protected void createQuery(HQLHelper hql) {

//        hql.addToSelect("count(*)");
        hql.addToSelect("cast(sum(cast(solicitacaoAgendamento.dataAgendamento as date) - cast(solicitacaoAgendamento.dataSolicitacao as date)) / count(*) as long)");

        hql.addToFrom("SolicitacaoAgendamento solicitacaoAgendamento");

        hql.addToWhereWhithAnd("solicitacaoAgendamento.tipoProcedimento = ", tipoProcedimento);
        hql.addToWhereWhithAnd("solicitacaoAgendamento.dataAgendamento is not null");
        hql.addToWhereWhithAnd("solicitacaoAgendamento.dataAgendamento", periodo);
        hql.addToWhereWhithAnd("solicitacaoAgendamento.status in ", Arrays.asList(SolicitacaoAgendamento.STATUS_AGENDADO_FORA_REDE,SolicitacaoAgendamento.STATUS_AGENDADO, SolicitacaoAgendamento.STATUS_REGULACAO_AGENDADO));
        
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        List l = (List) result;
        media = (Long) l.get(0);
    }

    public Long getMedia(){
        return media;
    }

}
