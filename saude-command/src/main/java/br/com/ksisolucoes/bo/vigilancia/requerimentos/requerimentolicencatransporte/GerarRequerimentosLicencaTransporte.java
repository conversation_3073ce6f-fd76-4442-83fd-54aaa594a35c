package br.com.ksisolucoes.bo.vigilancia.requerimentos.requerimentolicencatransporte;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoLicencaTransporteDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.VeiculoEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoLicencaTransporte;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Deprecated
public class GerarRequerimentosLicencaTransporte extends AbstractCommandTransaction<GerarRequerimentosLicencaTransporte> {

    private final RequerimentoLicencaTransporteDTO dto;
    private RequerimentoVigilancia requerimentoVigilancia;
    private List<VeiculoEstabelecimento> veiculoEstabelecimentoList;

    public GerarRequerimentosLicencaTransporte(RequerimentoLicencaTransporteDTO dto) {
        this.dto = dto;
        this.veiculoEstabelecimentoList = new ArrayList(dto.getLstVeiculoEstabelecimentoDTO());
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
//        if (dto.getRequerimentoLicencaTransporte().getCodigo() == null && !CollectionUtils.isNotNullEmpty(veiculoEstabelecimentoList)) {
//            throw new ValidacaoException(Bundle.getStringApplication("adicione_um_veiculo"));
//        }
//        ArrayList<Long> protocolosList = new ArrayList();
//        if (dto.getRequerimentoLicencaTransporte().getCodigo() == null) {
//            RequerimentoLicencaTransporte rlt;
//            RequerimentoVigilancia rv;
//
//            for (VeiculoEstabelecimento veiculoEstabelecimento : veiculoEstabelecimentoList) {
//                if (existsRequerimentoLicencaTransporte(veiculoEstabelecimento)) {
//                    throw new ValidacaoException(Bundle.getStringApplication("msgJaExisteRequerimentoLicencaTransportePendenteEstabelecimentoXVeiculoX", this.dto.getRequerimentoLicencaTransporte().getEstabelecimento().getRazaoSocial(), veiculoEstabelecimento.getPlaca()));
//                }
//                RequerimentoLicencaTransporteDTO requerimentoLicencaTransporteDTO = dto;
//                requerimentoLicencaTransporteDTO.getLstVeiculoEstabelecimentoDTO().clear();
//                requerimentoLicencaTransporteDTO.getLstVeiculoEstabelecimentoDTO().add(veiculoEstabelecimento);
//                rv = VOUtils.cloneObject(this.dto.getRequerimentoLicencaTransporte().getRequerimentoVigilancia());
//                rlt = VOUtils.cloneObject(this.dto.getRequerimentoLicencaTransporte());
//                rlt.setRequerimentoVigilancia(rv);
//                requerimentoLicencaTransporteDTO.setRequerimentoLicencaTransporte(rlt);
//                requerimentoVigilancia = BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoLicencaTransporte(requerimentoLicencaTransporteDTO);
//                protocolosList.add(requerimentoVigilancia.getProtocolo());
//            }
//        } else {
//            requerimentoVigilancia = BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoLicencaTransporte(dto);
//        }
//        requerimentoVigilancia.setProtocoloList(protocolosList);
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }

    private boolean existsRequerimentoLicencaTransporte(VeiculoEstabelecimento veiculoEstabelecimento) {
        LoadManager loadManager = LoadManager.getInstance(RequerimentoLicencaTransporte.class);
        if (dto.getRequerimentoLicencaTransporte().getCodigo() != null) {
            loadManager.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RequerimentoLicencaTransporte.PROP_CODIGO), BuilderQueryCustom.QueryParameter.DIFERENTE, dto.getRequerimentoLicencaTransporte().getCodigo()));
        }
        loadManager.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RequerimentoLicencaTransporte.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_SITUACAO),
                RequerimentoVigilancia.Situacao.PENDENTE.value()));
        return loadManager.exists();

    }

}