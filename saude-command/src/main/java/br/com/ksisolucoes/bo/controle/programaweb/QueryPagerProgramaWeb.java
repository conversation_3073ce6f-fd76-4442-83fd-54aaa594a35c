package br.com.ksisolucoes.bo.controle.programaweb;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.controle.interfaces.dto.ProgramaWebDTO;
import br.com.ksisolucoes.bo.controle.interfaces.dto.ProgramaWebDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.controle.web.ProgramaWeb;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryPagerProgramaWeb extends CommandQueryPager<QueryPagerProgramaWeb> {

    private ProgramaWebDTOParam param;
    

    public QueryPagerProgramaWeb(ProgramaWebDTOParam param) {
        this.param = param;
    }
    
    @Override
    protected void createQuery(HQLHelper hql) {
        
        hql.addToSelect("programaWeb.codigo", "programaWeb.codigo");
        hql.addToSelect("programaWeb.descricao", "programaWeb.descricao");
        hql.addToSelect("programaWeb.ativo", "programaWeb.ativo");
        hql.addToSelect("programaPaginaPrincipal.codigo", "programaWeb.programaPaginaPrincipal.codigo");
        hql.addToSelect("programaPaginaPrincipal.caminhoPagina", "programaWeb.programaPaginaPrincipal.caminhoPagina");
        hql.addToSelect("menuModulo.codigo", "menuModulo.codigo");
        hql.addToSelect("menuModulo.descricao", "menuModulo.descricao");
        
        hql.setTypeSelect(ProgramaWebDTO.class.getName());
        hql.addToFrom("MenuWeb menuWeb"
                + " right join menuWeb.programaWeb programaWeb"
                + " left join programaWeb.programaPaginaPrincipal programaPaginaPrincipal"
                + " left join menuWeb.menuModulo menuModulo");
        
        hql.addToWhereWhithAnd(hql.getConsultaLiked("programaWeb.descricao", param.getDescricao()));

        hql.addToWhereWhithAnd("programaWeb.ativo =", param.getAcesso());
        
        hql.addToWhereWhithAnd("menuModulo = ", param.getModulo());
        
        hql.addToWhereWhithAnd("programaWeb.codigo is not null");

        if (VOUtils.montarPath(ProgramaWebDTO.PROP_PROGRAMA_WEB, ProgramaWeb.PROP_ATIVO_FORMATADO).equals(param.getPropOrdenacao())) {
            hql.addToOrder("programaWeb.ativo "+getOrdenacao());
        } else {
            hql.addToOrder(param.getPropOrdenacao()+" "+getOrdenacao());
        }
        
    }
    
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result , false);
    }
    
    private String getOrdenacao(){
        return param.getOrdenacao()? "asc" : "desc";
    }

}
