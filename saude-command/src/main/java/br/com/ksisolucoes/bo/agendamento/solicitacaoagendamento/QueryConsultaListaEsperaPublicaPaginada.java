package br.com.ksisolucoes.bo.agendamento.solicitacaoagendamento;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.agendamento.exame.dto.AgendamentoListaEsperaDTO;
import br.com.ksisolucoes.agendamento.exame.dto.AgendamentoListaEsperaDTOParam;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.TipoAtendimentoAgenda;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import ch.lambdaj.Lambda;
import org.hibernate.Query;
import org.hibernate.Session;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryConsultaListaEsperaPublicaPaginada extends CommandQueryPager<QueryConsultaListaEsperaPublicaPaginada> {

    private final AgendamentoListaEsperaDTOParam param;

    public QueryConsultaListaEsperaPublicaPaginada(AgendamentoListaEsperaDTOParam param) {
        this.param = param;
        this.setRealizarCountRegistros(RepositoryComponentDefault.SIM_LONG);
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(AgendamentoListaEsperaDTO.class.getName());


        hql.addToSelect("domUsuarioCadsus.cns", "numeroCns");
        hql.addToSelect("solicitacaoAgendamento.codigo", "solicitacaoAgendamento.codigo");
        hql.addToSelect("solicitacaoAgendamento.dataAgendamento", "solicitacaoAgendamento.dataAgendamento");
        hql.addToSelect("solicitacaoAgendamento.dataSolicitacao", "solicitacaoAgendamento.dataSolicitacao");
        hql.addToSelect("justificativaPriorizacao.codigo", "solicitacaoAgendamento.justificativaPriorizacao.codigo");
        hql.addToSelect("justificativaPriorizacao.justificativa", "solicitacaoAgendamento.justificativaPriorizacao.justificativa");

        hql.addToSelect("solicitacaoAgendamento.solicitarPrioridade", "solicitacaoAgendamento.solicitarPrioridade");
        hql.addToSelect("solicitacaoAgendamento.flagAvaliacaoAprovado", "solicitacaoAgendamento.flagAvaliacaoAprovado");
        hql.addToSelect("solicitacaoAgendamento.flagBloqueado", "solicitacaoAgendamento.flagBloqueado");
        hql.addToSelect("solicitacaoAgendamento.flagDevolvido", "solicitacaoAgendamento.flagDevolvido");
        hql.addToSelect("solicitacaoAgendamento.diaAteBloqueio", "solicitacaoAgendamento.diaAteBloqueio");
        hql.addToSelect("solicitacaoAgendamento.prioridade", "solicitacaoAgendamento.prioridade");
        hql.addToSelect("solicitacaoAgendamento.cid", "solicitacaoAgendamento.cid");
        hql.addToSelect("solicitacaoAgendamento.acamado", "solicitacaoAgendamento.acamado");

        hql.addToSelect("solicitacaoAgendamentoPosicaoFila.posicaoFilaEspera", "solicitacaoAgendamento.solicitacaoAgendamentoPosicaoFila.posicaoFilaEspera");

        hql.addToSelect("classificacaoRisco.codigo", "solicitacaoAgendamento.classificacaoRisco.codigo");
        hql.addToSelect("classificacaoRisco.descricao", "solicitacaoAgendamento.classificacaoRisco.descricao");
        hql.addToSelect("classificacaoRisco.nivelGravidade", "solicitacaoAgendamento.classificacaoRisco.nivelGravidade");

        hql.addToSelect("usuarioCadsus.codigo", "solicitacaoAgendamento.usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "solicitacaoAgendamento.usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.nomeMae", "solicitacaoAgendamento.usuarioCadsus.nomeMae");
        hql.addToSelect("usuarioCadsus.sexo", "solicitacaoAgendamento.usuarioCadsus.sexo");
        hql.addToSelect("usuarioCadsus.dataNascimento", "solicitacaoAgendamento.usuarioCadsus.dataNascimento");

        hql.addToSelect("tipoProcedimento.codigo", "solicitacaoAgendamento.tipoProcedimento.codigo");
        hql.addToSelect("tipoProcedimento.descricao", "solicitacaoAgendamento.tipoProcedimento.descricao");

        hql.addToSelect("tipoExameFiltro.codigo", "tipoExameFiltro.codigo");
        hql.addToSelect("tipoExameFiltro.descricao", "tipoExameFiltro.descricao");


        hql.addToFrom("SolicitacaoAgendamento solicitacaoAgendamento"
                + " left join solicitacaoAgendamento.justificativaPriorizacao justificativaPriorizacao"
                + " left join solicitacaoAgendamento.usuarioCadsus usuarioCadsus"
                + " left join usuarioCadsus.roDominioUsuarioCadsus domUsuarioCadsus"
                + " left join solicitacaoAgendamento.tipoProcedimento tipoProcedimento"
                + " left join tipoProcedimento.tipoExameFiltro tipoExameFiltro"
                + " left join solicitacaoAgendamento.cid"
                + " left join solicitacaoAgendamento.classificacaoRisco classificacaoRisco"
                + " left join solicitacaoAgendamento.solicitacaoAgendamentoPosicaoFila solicitacaoAgendamentoPosicaoFila"
        );


        if(param.isTiposExames()){
            param.setTipoConsulta(null);
            param.setSituacaoList(null);
            hql.addToWhereWhithAnd("solicitacaoAgendamento.tipoFila in ", Arrays.asList(SolicitacaoAgendamento.TIPO_FILA_NORMAL, SolicitacaoAgendamento.TIPO_FILA_REGULACAO));
        }else{
            if (param.getTipoFila() != null && TipoAtendimentoAgenda.TIPO_CONSULTA.equals(param.getTipoFila())) {
                hql.addToWhereWhithAnd("solicitacaoAgendamento.tipoFila = ", SolicitacaoAgendamento.TIPO_FILA_NORMAL);
            } else if (param.getTipoFila() != null && TipoAtendimentoAgenda.TIPO_REGULACAO.equals(param.getTipoFila())) {
                hql.addToWhereWhithAnd("solicitacaoAgendamento.tipoFila = ", SolicitacaoAgendamento.TIPO_FILA_REGULACAO);
            }
        }

        if (param.getTipoProcedimento() != null) {
            hql.addToWhereWhithAnd("tipoProcedimento = ", param.getTipoProcedimento());
        }

        if (param.getSituacaoList() != null && CollectionUtils.isNotNullEmpty(param.getSituacaoList())) {
            hql.addToWhereWhithAnd("solicitacaoAgendamento.status in ", param.getSituacaoList());
        }

        hql.addToWhereWhithAnd("tipoProcedimento.flagTfd = ", RepositoryComponentDefault.NAO);

        if (param.getExameProcedimento() != null /* && param.isNaoProcessaDataPrevista()*/) {
            hql.addToWhereWhithAnd(" exists ( select 1 from SolicitacaoAgendamentoExame sae " +
                    " where sae.exameProcedimento.codigo =  "+ param.getExameProcedimento().getCodigo()+" " +
                    " and sae.solicitacaoAgendamento.codigo = solicitacaoAgendamento.codigo ) ");
        }

        if (param.getTipoConsulta() != null) {
            hql.addToWhereWhithAnd("solicitacaoAgendamento.tipoConsulta in ", param.getTipoConsulta());
        }

        if (this.param.getNumeroCartao() != null) {
            hql.addToWhereWhithAnd("domUsuarioCadsus.cns = '" + this.param.getNumeroCartao().replaceAll("[^0-9]", "") + "')");
        }

        if (this.param.getCpf() != null) {
            hql.addToWhereWhithAnd("usuarioCadsus.cpf = ", this.param.getCpf().replaceAll("[^0-9]", ""));
        }

        if (AgendamentoListaEsperaDTOParam.OrderBy.DATA_AGENDAMENTO.equals(param.getOrderBy())) {
            hql.addToWhereWhithAnd("solicitacaoAgendamento.dataAgendamento >= ", Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial());
            hql.addToOrder("solicitacaoAgendamento.dataAgendamento " + QueryCustom.QueryCustomSorter.DECRESCENTE_NULLS_LAST);
        }

        if (TipoAtendimentoAgenda.TIPO_REGULACAO.equals(param.getTipoFila().longValue())) {
            hql.addToOrder("classificacaoRisco.nivelGravidade asc");
            hql.addToOrder("solicitacaoAgendamento.valorSubclassificacaoFilaEspera asc");
            hql.addToOrder("solicitacaoAgendamentoPosicaoFila.posicaoFilaEspera asc");
            hql.addToOrder("solicitacaoAgendamento.dataSolicitacao asc");
            hql.addToOrder("solicitacaoAgendamento.codigo asc");
        } else {
            hql.addToOrder("solicitacaoAgendamentoPosicaoFila.posicaoFilaEspera");
            hql.addToOrder("solicitacaoAgendamento.codigo");
        }
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        boolean listaAgendamentos = param.getSituacaoList().contains(SolicitacaoAgendamento.STATUS_AGENDADO) && param.getSituacaoList().contains(SolicitacaoAgendamento.STATUS_AGENDADO_FORA_REDE);
        if (!listaAgendamentos) {
            DataPrevistaAgendamento.dataPrevistaAgendamento(list, new QueryConsultaMediaVagasAgendadasTipoProcedimento(param));
            if (CollectionUtils.isNotNullEmpty(list)) {
                Lambda.forEach((List<AgendamentoListaEsperaDTO>)list).setTipoFila(param.getTipoFila());
            }
        }
    }

    @Override
    protected void setParameters(Query query) {
        super.setParameters(query);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
