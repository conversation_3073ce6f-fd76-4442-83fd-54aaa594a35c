package br.com.ksisolucoes.bo.hospital.financeiro.contafinanceiraitem;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.hospital.financeiro.ContaFinanceiraItem;

/**
 *
 * <AUTHOR>
 */
public class SaveContaFinanceiraItem extends SaveVO<ContaFinanceiraItem> {

    public SaveContaFinanceiraItem(ContaFinanceiraItem vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getUsuario() == null) {
            this.vo.setUsuario(getSessao().<Usuario>getUsuario());
        }
        if (this.vo.getDataCriacao() == null) {
            this.vo.setDataCriacao(DataUtil.getDataAtual());
        }
        if(this.vo.getStatus() == null){
            this.vo.setStatus(ContaFinanceiraItem.Status.ABERTO.value());
        }
        if(this.vo.getFlagOrigem() == null){
           this.vo.setFlagOrigem(ContaFinanceiraItem.FLAG_ORIGEM_ENTRADA); 
        }
    }
}