package br.com.ksisolucoes.bo.agendamento.agenda;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.agendamento.AgendaGrade;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class DeletarAgenda extends AbstractCommandTransaction<DeletarAgenda> {

    private Agenda agenda;

    public DeletarAgenda(Agenda agenda) {
        this.agenda = agenda;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        validacoes();

        BOFactory.getBO(AgendamentoFacade.class).deletarAgendaGradeHorario(agenda);
        BOFactory.getBO(AgendamentoFacade.class).deletarAgendaGradeAtendimento(agenda);
        BOFactory.getBO(AgendamentoFacade.class).deletarAgendaGrade(agenda);

        Long count = (Long) getSession().createCriteria(AgendaGrade.class)
                .add(Restrictions.eq(AgendaGrade.PROP_AGENDA, agenda))
                .setProjection(Projections.count(AgendaGrade.PROP_CODIGO))
                .uniqueResult();

        if (Coalesce.asLong(count) == 0L) {
            BOFactory.delete(agenda);
        }
    }

    private void validacoes() throws ValidacaoException, DAOException {
        String aprovarAgenda = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("aprovaAgendaCadastro");
        if (new Long(Agenda.STATUS_CONFIRMADO).equals(Coalesce.asLong(this.agenda.getStatus())) && RepositoryComponentDefault.SIM.equals(aprovarAgenda)) {
//            if (!this.agenda.getUsuarioCadastro().equals(SessaoAplicacaoImp.getInstance().<Usuario>getUsuario()) || !this.agenda.getUsuarioCadastro().equals(this.agenda.getUsuarioAprovacao())) {
//                throw new ValidacaoException(Bundle.getStringApplication("msg_operacao_nao_permitida_para_a_agenda_selecionada_usuario_cadastro_aprovacao_diferentes"));
//            }
        } else if (!new Long(Agenda.STATUS_ABERTO).equals(Coalesce.asLong(this.agenda.getStatus()))) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_somente_agendas_aberto_podem_excluidas"));
        }
    }
}
