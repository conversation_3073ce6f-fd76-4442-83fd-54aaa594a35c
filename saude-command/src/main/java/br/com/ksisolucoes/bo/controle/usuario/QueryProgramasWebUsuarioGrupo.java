package br.com.ksisolucoes.bo.controle.usuario;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.controle.web.ProgramaWeb;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryProgramasWebUsuarioGrupo extends CommandQuery<QueryProgramasWebUsuarioGrupo> {

    private Usuario usuario;

    private List<ProgramaWeb> programas;

    public QueryProgramasWebUsuarioGrupo(Usuario usuario) {
        this.usuario = usuario;
    }

    public QueryProgramasWebUsuarioGrupo(Usuario usuario, List<ProgramaWeb> programas) {
        this.usuario = usuario;
        this.programas = programas;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("pw.codigo", "codigo");
        hql.addToSelect("pw.descricao", "descricao");
        hql.addToSelect("ppp.codigo", "programaPaginaPrincipal.codigo");
        hql.addToSelect("ppp.caminhoPagina", "programaPaginaPrincipal.caminhoPagina");
        hql.addToSelect("ppp.flagPublico", "programaPaginaPrincipal.flagPublico");

        hql.setTypeSelect(ProgramaWeb.class.getName());
        hql.addToFrom("ControleProgramaWebGrupo cpwg "
                + " left join cpwg.programaWeb pw "
                + " left join pw.programaPaginaPrincipal ppp "
                + " left join cpwg.grupo g ");
        hql.addToFrom("UsuarioGrupo ug "
                + " left join ug.usuario u"
                + " left join ug.grupo gru");

        hql.addToWhereWhithAnd("gru = g");

        hql.addToWhereWhithAnd("u = ", usuario);
        hql.addToWhereWhithAnd("pw in ", programas);
        hql.addToWhereWhithAnd("pw.ativo = ", RepositoryComponentDefault.SIM);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.programas = hql.getBeanList((List) result);
    }

    public List<ProgramaWeb> getProgramas() {
        return programas;
    }
}
