package br.com.ksisolucoes.bo.prontuario.basico.tiporeceita;

import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceitaCbo;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceitaCboPK;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceitaEmpresa;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class DeletarTipoReceita extends AbstractCommandTransaction {

    private TipoReceita tipoReceita;

    public DeletarTipoReceita(TipoReceita tipoReceita) {
        this.tipoReceita = tipoReceita;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        tipoReceita = HibernateUtil.rechargeVO(TipoReceita.class, tipoReceita.getCodigo(), tipoReceita.getVersion());

        List<TipoReceitaCbo> tipoReceitaCbos = getSession().createCriteria(TipoReceitaCbo.class)
                .add(Restrictions.eq(VOUtils.montarPath(TipoReceitaCbo.PROP_ID, TipoReceitaCboPK.PROP_TIPO_RECEITA), this.tipoReceita))
                .list();

        for (TipoReceitaCbo tipoReceitaCbo : tipoReceitaCbos) {
            BOFactory.getBO(CadastroFacade.class).delete(tipoReceitaCbo);
        }

        List<TipoReceitaEmpresa> tipoReceitaEmpresas = getSession().createCriteria(TipoReceitaEmpresa.class)
                .add(Restrictions.eq(VOUtils.montarPath(TipoReceitaEmpresa.PROP_TIPO_RECEITA), this.tipoReceita))
                .list();

        for (TipoReceitaEmpresa tipoReceitaCbo : tipoReceitaEmpresas) {
            BOFactory.getBO(CadastroFacade.class).delete(tipoReceitaCbo);
        }

        BOFactory.getBO(CadastroFacade.class).delete(this.tipoReceita);
    }
}
