package br.com.ksisolucoes.bo.portal.servicos.vacina;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.vacina.dto.RelatorioImpressaoHistoricoVacinacaoDTO;
import br.com.ksisolucoes.report.vacina.dto.RelatorioImpressaoHistoricoVacinacaoDTOParam;
import br.com.ksisolucoes.vo.vacina.VacinaAplicacao;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryImpressaoHistoricoVacinacaoPortal extends CommandQuery<QueryImpressaoHistoricoVacinacaoPortal> implements ITransferDataReport<RelatorioImpressaoHistoricoVacinacaoDTOParam, RelatorioImpressaoHistoricoVacinacaoDTO> {

    private RelatorioImpressaoHistoricoVacinacaoDTOParam param;
    private List<RelatorioImpressaoHistoricoVacinacaoDTO> result;
    
    public QueryImpressaoHistoricoVacinacaoPortal(){
        
    }
    
    public QueryImpressaoHistoricoVacinacaoPortal(RelatorioImpressaoHistoricoVacinacaoDTOParam param){
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        
        hql.addToSelect("vacinaAplicacao.dataAplicacao", "vacinaAplicacao.dataAplicacao");
        hql.addToSelect("vacinaAplicacao.descricaoVacina", "vacinaAplicacao.descricaoVacina");
        hql.addToSelect("vacinaCalendario.dose", "vacinaAplicacao.vacinaCalendario.dose");
        hql.addToSelect("vacinaAplicacao.observacao", "vacinaAplicacao.observacao");
        hql.addToSelect("vacinaAplicacao.dataValidade", "vacinaAplicacao.dataValidade");
        hql.addToSelect("vacinaAplicacao.lote", "vacinaAplicacao.lote");
        hql.addToSelect("vacinaAplicacao.usuario", "vacinaAplicacao.usuario");
        hql.addToSelect("usuarioCadsus.nome", "usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.nomeMae", "usuarioCadsus.nomeMae");
        hql.addToSelect("usuarioCadsus.dataNascimento", "usuarioCadsus.dataNascimento");
        hql.addToSelect("(select min(ucc.numeroCartao) from UsuarioCadsusCns ucc where ucc.usuarioCadsus = usuarioCadsus and ucc.excluido = 0)", "numeroCartao");
        
        hql.setTypeSelect(RelatorioImpressaoHistoricoVacinacaoDTO.class.getName());
        hql.addToFrom("VacinaAplicacao vacinaAplicacao"
                + " left join vacinaAplicacao.vacinaCalendario vacinaCalendario"
                + " left join vacinaAplicacao.usuarioCadsus usuarioCadsus");
        
        hql.addToWhereWhithAnd("vacinaAplicacao.status = ", VacinaAplicacao.StatusVacinaAplicacao.APLICADA.value());
        if(param.getUsuarioCadsus() !=null){
            hql.addToWhereWhithAnd("usuarioCadsus.codigo = ", param.getUsuarioCadsus().getCodigo());
        }
        
        hql.addToOrder("vacinaAplicacao.dataAplicacao asc");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>)result);
    }
    
    @Override
    public void setDTOParam(RelatorioImpressaoHistoricoVacinacaoDTOParam param) {
        this.param = param;
    }

    @Override
    public List<RelatorioImpressaoHistoricoVacinacaoDTO> getResult() {
        return result;
    }

}
