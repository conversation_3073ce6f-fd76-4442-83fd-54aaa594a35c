package br.com.ksisolucoes.bo.vigilancia.autointimacao.autointimacao;

import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ConsultaAutoIntimacaoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QueryConsultaAutoIntimacaoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.RelatorioInspecao;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.MotivoRetorno;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMulta;
import br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoAutenticacao;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecao;
import org.hibernate.Query;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryConsultaAutoIntimacao extends CommandQueryPager<QueryConsultaAutoIntimacao> {

    private QueryConsultaAutoIntimacaoDTOParam param;

    public QueryConsultaAutoIntimacao(QueryConsultaAutoIntimacaoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.setTypeSelect(ConsultaAutoIntimacaoDTO.class.getName());
        hql.setConvertToLeftJoin(true);

        hql.addToSelect("autoIntimacao", new HQLProperties(AutoIntimacao.class, "autoIntimacao").getProperties());

        hql.addToSelect("autoIntimacao.estabelecimento", new HQLProperties(Estabelecimento.class, "estabelecimento").getProperties());

        hql.addToSelect("autoIntimacao.vigilanciaPessoa", new HQLProperties(VigilanciaPessoa.class, "vigilanciaPessoa").getProperties());

        hql.addToSelect("autoIntimacao.vigilanciaEndereco", new HQLProperties(VigilanciaEndereco.class, "vigilanciaEndereco").getProperties());

        hql.addToSelect("autoIntimacao.requerimentoVigilancia", new HQLProperties(RequerimentoVigilancia.class, "requerimentoVigilancia").getProperties());

        hql.addToSelect("autoIntimacao.relatorioInspecao", new HQLProperties(RelatorioInspecao.class, "relatorioInspecao").getProperties());

        hql.addToSelect("autoIntimacao.autoInfracao", new HQLProperties(AutoInfracao.class, "autoInfracao").getProperties());

        hql.addToSelect("autoIntimacao.autoMulta", new HQLProperties(AutoMulta.class, "autoMulta").getProperties());

        hql.addToSelect("autoIntimacao.registroInspecao", new HQLProperties(RegistroInspecao.class, "registroInspecao").getProperties());

        hql.addToSelect("autoIntimacao.processoAdministrativoAutenticacao", new HQLProperties(ProcessoAdministrativoAutenticacao.class, "processoAdministrativoAutenticacao").getProperties());

        hql.addToSelect("autoIntimacao.motivoRetorno", new HQLProperties(MotivoRetorno.class, "motivoRetorno").getProperties());

        hql.addToSelect("autoIntimacao.denuncia", new HQLProperties(Denuncia.class, "denuncia").getProperties());

        hql.addToSelect("requerimentoVigilanciaRoteiro.codigo", "autoIntimacao.registroInspecao.requerimentoVigilancia.codigo");
        hql.addToSelect("requerimentoVigilanciaRoteiro.version", "autoIntimacao.registroInspecao.requerimentoVigilancia.version");

        hql.addToSelect(" (SELECT MAX(autoIntimacaoExigencia.dataCumprimentoPrazo) FROM AutoIntimacaoExigencia autoIntimacaoExigencia WHERE autoIntimacaoExigencia.autoIntimacao.codigo = autoIntimacao.codigo)", "prazoFinalExigencias");
        hql.addToSelect(" (SELECT COUNT(autoIntimacaoExigencia.codigo) FROM AutoIntimacaoExigencia autoIntimacaoExigencia WHERE autoIntimacaoExigencia.autoIntimacao.codigo = autoIntimacao.codigo)", "quantidadeExigencias");

        StringBuilder sbFrom = new StringBuilder("AutoIntimacao autoIntimacao ");
        sbFrom.append(" LEFT JOIN autoIntimacao.estabelecimento estabelecimento");
        sbFrom.append(" LEFT JOIN autoIntimacao.vigilanciaPessoa vigilanciaPessoa");
        sbFrom.append(" LEFT JOIN autoIntimacao.vigilanciaEndereco vigilanciaEndereco");
        sbFrom.append(" LEFT JOIN autoIntimacao.registroInspecao registroInspecao");
        sbFrom.append(" LEFT JOIN autoIntimacao.relatorioInspecao relatorioInspecao");
        sbFrom.append(" LEFT JOIN autoIntimacao.autoInfracao autoInfracao");
        sbFrom.append(" LEFT JOIN autoIntimacao.autoMulta autoMulta");
        sbFrom.append(" LEFT JOIN autoIntimacao.requerimentoVigilancia requerimentoVigilancia");
        sbFrom.append(" LEFT JOIN autoIntimacao.motivoRetorno motivoRetorno");
        sbFrom.append(" LEFT JOIN autoIntimacao.denuncia denuncia");
        sbFrom.append(" LEFT JOIN autoIntimacao.processoAdministrativoAutenticacao processoAdministrativoAutenticacao");
        sbFrom.append(" LEFT JOIN registroInspecao.requerimentoVigilancia requerimentoVigilanciaRoteiro");
        hql.addToFrom(sbFrom.toString());

        hql.addToWhereWhithAnd("autoIntimacao.numero = ", param.getNumeroAutoIntimacao());
        hql.addToWhereWhithAnd("autoIntimacao.dataCadastro", param.getPeriodo());
        hql.addToWhereWhithAnd("autoIntimacao.situacao = ", param.getSituacao());
        hql.addToWhereWhithAnd("autoIntimacao.tipo = ", param.getTipo());

        if(param.getVigilanciaEndereco() != null) {
            hql.addToWhereWhithAnd("vigilanciaEndereco.keyword = ", param.getVigilanciaEndereco().getKeyword());
        }

        if (Coalesce.asString(param.getAutuado()).trim().length() > 0) {
            hql.addToWhereWhithAnd(hql.getConsultaLiked("autoIntimacao.autuado", param.getAutuado()));
        }

        Profissional profissional = SessaoAplicacaoImp.getInstance().getUsuario().getProfissional();
        if (profissional != null && RepositoryComponentDefault.NAO_LONG.equals(param.getExibirOutrosFiscais())) {
            StringBuilder existsProfissional = new StringBuilder("exists(select 1");
            existsProfissional.append("                      from AutoIntimacaoFiscal fiscal");
            existsProfissional.append("                     where fiscal.autoIntimacao.codigo = autoIntimacao.codigo");
            existsProfissional.append("                       and fiscal.profissional.codigo = :codigoProfissional)");
            hql.addToWhereWhithAnd(existsProfissional.toString());
        }

        if (param.isAutosVencendo()) {
            StringBuilder sbExistsAutosVencendo =  new StringBuilder("exists(select 1");
            sbExistsAutosVencendo.append("                      from AutoIntimacaoExigencia exigencia");
            sbExistsAutosVencendo.append("                     where exigencia.autoIntimacao.codigo = autoIntimacao.codigo");
            sbExistsAutosVencendo.append("                       and exigencia.dataCumprimentoPrazo between :dataInicial and :dataFinal)");
            hql.addToWhereWhithAnd(sbExistsAutosVencendo.toString());
        }

//        if (param.getPropSort() != null) {
//            StringBuilder sbSort =  new StringBuilder("autoIntimacao.");
//            sbSort.append(param.getPropSort());
//            sbSort.append(" ");
//            sbSort.append((param.isAscending() ? "asc" : "desc"));
//            hql.addToOrder(sbSort.toString());
//        }
        if (param.getPropSort() != null) {
            hql.addToOrder(param.getPropSort() + " " + (param.isAscending() ? "asc" : "desc"));
        }
    }

    @Override
    protected void setParameters(Query query) {
        if (param.isAutosVencendo()) {
            Date hoje = DataUtil.getDataAtual();
            Date amanha = Data.addDias(hoje, 1);
            DatePeriod period = Data.adjustRangeHour(hoje, amanha);
            query.setParameter("dataInicial", period.getDataInicial());
            query.setParameter("dataFinal", period.getDataFinal());
        }

//        if(param.isChamadoPorPendenciasFiscais() && AutoIntimacao.Status.PENDENTE.value().equals(param.getSituacao())) {
//            query.setParameter("nao", RepositoryComponentDefault.NAO_LONG);
//            query.setParameter("pendente", AutoIntimacao.Status.PENDENTE.value());
//            query.setParameter("dataInicio", DataUtil.getDataAtualSemHora());
//        }

        Profissional profissional = SessaoAplicacaoImp.getInstance().getUsuario().getProfissional();
        if (profissional != null && RepositoryComponentDefault.NAO_LONG.equals(param.getExibirOutrosFiscais())) {
            query.setParameter("codigoProfissional", profissional.getCodigo());
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

}
