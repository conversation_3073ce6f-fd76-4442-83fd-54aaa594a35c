package br.com.ksisolucoes.bo.prontuario.avaliacao.auxilioBrasil;

import br.com.celk.util.DataUtil;
import br.com.celk.util.Util;
import br.com.celk.util.validacao.ValidacaoProcesso;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.avaliacao.interfaces.dto.AuxilioBrasilDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.avaliacao.AuxilioBrasil;
import br.com.ksisolucoes.vo.prontuario.basico.PreNatal;

import java.util.Date;

import static br.com.ksisolucoes.util.VOUtils.montarPath;


public class SalvarAuxilioBrasil extends AbstractCommandTransaction<SalvarAuxilioBrasil> {

    private final AuxilioBrasilDTO dto;
    private final ValidacaoProcesso validacaoProcesso = new ValidacaoProcesso();

    public SalvarAuxilioBrasil(AuxilioBrasilDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (auxilioBrasilExist(dto.getUsuarioCadsus()) && atendimentoPrimarioIsNotNull())  {
            validarDadosRegistro();
            AuxilioBrasil auxilioBrasil = new MontarAuxilioBrasil(dto).build();
            BOFactory.save(auxilioBrasil);
            getSession().flush();
            getSession().clear();
        }
    }

    private boolean auxilioBrasilExist(UsuarioCadsus usuarioCadsus) {
        EncontrarAuxilioBrasil encontrarAuxilioBrasil = new EncontrarAuxilioBrasil(usuarioCadsus);
        return encontrarAuxilioBrasil.auxilioBrasilExist();
    }

    private boolean atendimentoPrimarioIsNotNull() {
        return Util.isNotNull(dto.getAtendimentoPrimario());
    }

    private void validarDadosRegistro() throws ValidacaoException {

        validarPeso(dto.getAtendimentoPrimario().getPeso());
        validarAltura(dto.getAtendimentoPrimario().getAltura());

        if (!validacaoProcesso.getMensagemList().isEmpty()) {
            throw new ValidacaoException(validacaoProcesso);
        }
    }

    private void validarPeso(Double peso) {
        try {
            isPesoVazio(peso);
        } catch (Exception e) {
            validacaoProcesso.add(Bundle.getStringApplication("campoXValorInvalido", Bundle.getStringApplication("rotulo_peso")));
        }
    }

    private void isPesoVazio(Double peso) throws Exception {
        if (peso <= 0D)
            throw new Exception();
    }

    private void validarAltura(Double altura) {
        try {
            isAlturaVazia(altura);
        } catch (Exception e) {
            validacaoProcesso.add(Bundle.getStringApplication("campoXValorInvalido", Bundle.getStringApplication("rotulo_altura")));
        }
    }

    private void isAlturaVazia(Double altura) throws Exception {
        if (altura <= 0D)
            throw new Exception();
    }
}

class EncontrarAuxilioBrasil {

    private final UsuarioCadsus usuarioCadsus;

    public EncontrarAuxilioBrasil(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public boolean auxilioBrasilExist(){
        return  Util.isNotNull(getAuxilioBrasilByUsuario());
    }

    public AuxilioBrasil getAuxilioBrasilByUsuario(){
        return queryConsultaAuxilioBrasil();
    }

    private AuxilioBrasil queryConsultaAuxilioBrasil() {
        return LoadManager.getInstance(AuxilioBrasil.class)
                .addProperties(new HQLProperties(AuxilioBrasil.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(montarPath(AuxilioBrasil.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), usuarioCadsus.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(montarPath(AuxilioBrasil.PROP_ANO), String.valueOf(DataUtil.getAno())))
                .addParameter(new QueryCustom.QueryCustomParameter(montarPath(AuxilioBrasil.PROP_SEMESTRE), String.valueOf(DataUtil.getSemester(DataUtil.getDataAtual()))))
                .start().getVO();
    }
}

class MontarAuxilioBrasil {

    private final AuxilioBrasilDTO dto;
    private PreNatal preNatal;

    MontarAuxilioBrasil(AuxilioBrasilDTO dto) {
        this.dto = dto;
    }

    public AuxilioBrasil build() throws DAOException, ValidacaoException {
        return popularAuxilioBrasil();
    }

    private AuxilioBrasil popularAuxilioBrasil(){

        AuxilioBrasil auxilioBrasil = getAuxilioBrasilByUsuario(dto.getUsuarioCadsus());

        auxilioBrasil.setAltura(this.dto.getAtendimentoPrimario().getAltura());
        auxilioBrasil.setPeso(this.dto.getAtendimentoPrimario().getPeso());
        auxilioBrasil.setDataAtendimento(this.dto.getAtendimento().getDataAtendimento());
        auxilioBrasil.setVacinaEmDia(isVacinaEmDia() ? RepositoryComponentDefault.SIM_LONG : RepositoryComponentDefault.NAO_LONG);

        if (dto.getUsuarioCadsus().isFeminino()) {
            auxilioBrasil.setGestante(AuxilioBrasil.GestanteAuxilioBraisl.valueOfAuxilioBrasil(this.dto.getAtendimentoPrimario().getGestante()));
            if (verificaNecessidadePreNatal()) {
                auxilioBrasil.setDum(preNatal.getDataUltimaMenstruacao());
                auxilioBrasil.setDataPreNatal(preNatal.getDataUltimaConsulta());
            }
        }

        auxilioBrasil.setSituacao(getSituacao(auxilioBrasil));

        return auxilioBrasil;
    }

    private AuxilioBrasil getAuxilioBrasilByUsuario(UsuarioCadsus usuarioCadsus){
        EncontrarAuxilioBrasil encontrarAuxilioBrasil = new EncontrarAuxilioBrasil(usuarioCadsus);
        return encontrarAuxilioBrasil.getAuxilioBrasilByUsuario();
    }

    private boolean verificaNecessidadePreNatal() {
        return isGestante() && Util.isNotNull(getPreNatalEmAberto());
    }

    private PreNatal getPreNatalEmAberto() {

        preNatal = LoadManager.getInstance(PreNatal.class)
                .addProperties(new HQLProperties(PreNatal.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(montarPath(PreNatal.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), dto.getUsuarioCadsus().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(PreNatal.PROP_STATUS, PreNatal.Status.ABERTO.value()))
                .start().getVO();

        return preNatal;
    }

    private Long getSituacao(AuxilioBrasil auxilioBrasil) {
        if (isCriancaAndVacinaNaoEmDia(auxilioBrasil) || isGestanteAndDumVazio(auxilioBrasil))
            return AuxilioBrasil.Situacao.NAO_ACOMPANHADO.value();
        return AuxilioBrasil.Situacao.ACOMPANHADO.value();
    }

    private boolean isCriancaAndVacinaNaoEmDia(AuxilioBrasil auxilioBrasil) {
        return isCrianca(auxilioBrasil.getTipoIntegrante()) && !isVacinaEmDia();
    }

    private boolean isGestanteAndDumVazio(AuxilioBrasil auxilioBrasil) {
        return isGestante() && dumVazio(auxilioBrasil.getDum());
    }

    private boolean isGestante() {
        return RepositoryComponentDefault.SIM_LONG.equals(this.dto.getAtendimentoPrimario().getGestante());
    }

    private boolean dumVazio(Date dum) {
        return Util.isNull(dum);
    }

    private boolean isVacinaEmDia() {
        return RepositoryComponentDefault.SIM_LONG.equals(dto.getAtendimento().getVacinaEmDia());
    }

    private boolean isCrianca(Long tipoIntegrante) {
        return tipoIntegrante.equals(3L);
    }
}
