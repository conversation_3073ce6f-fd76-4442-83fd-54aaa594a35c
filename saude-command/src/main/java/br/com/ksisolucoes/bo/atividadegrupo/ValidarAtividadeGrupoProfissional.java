package br.com.ksisolucoes.bo.atividadegrupo;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoProfissional;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import org.hibernate.Criteria;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Restrictions;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ValidarAtividadeGrupoProfissional extends AbstractCommandTransaction {

    private AtividadeGrupo atividadeGrupo;
    private List<AtividadeGrupoProfissional> lstAtividadeGrupoProfissional;


    public ValidarAtividadeGrupoProfissional(AtividadeGrupo atividadeGrupo, List<AtividadeGrupoProfissional> lstAtividadeGrupoProfissional) {
        this.atividadeGrupo = atividadeGrupo;
        this.lstAtividadeGrupoProfissional = lstAtividadeGrupoProfissional;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        List<Profissional> lstProfissionais = new ArrayList<Profissional>();

        for (AtividadeGrupoProfissional item : lstAtividadeGrupoProfissional) {
            lstProfissionais.add(item.getProfissional());
        }

        Criterion or1 = Restrictions.and(
                Restrictions.lt(AtividadeGrupo.PROP_DATA_INICIO, atividadeGrupo.getDataInicio()),
                Restrictions.gt(AtividadeGrupo.PROP_DATA_FIM, atividadeGrupo.getDataFim())
        );

        Criterion or2 = Restrictions.and(
                Restrictions.lt(AtividadeGrupo.PROP_DATA_INICIO, atividadeGrupo.getDataInicio()),
                Restrictions.gt(AtividadeGrupo.PROP_DATA_FIM, atividadeGrupo.getDataInicio())
        );

        Criterion or3 = Restrictions.and(
                Restrictions.lt(AtividadeGrupo.PROP_DATA_INICIO, atividadeGrupo.getDataFim()),
                Restrictions.gt(AtividadeGrupo.PROP_DATA_FIM, atividadeGrupo.getDataFim())
        );

        Criterion or4 = Restrictions.and(
                Restrictions.eq(AtividadeGrupo.PROP_DATA_INICIO, atividadeGrupo.getDataInicio()),
                Restrictions.eq(AtividadeGrupo.PROP_DATA_FIM, atividadeGrupo.getDataFim())
        );

        Criterion or5 = Restrictions.and(
                Restrictions.eq(AtividadeGrupo.PROP_DATA_INICIO, atividadeGrupo.getDataInicio()),
                Restrictions.lt(AtividadeGrupo.PROP_DATA_INICIO, atividadeGrupo.getDataFim())
        );

        Criterion or6 = Restrictions.and(
                Restrictions.eq(AtividadeGrupo.PROP_DATA_FIM, atividadeGrupo.getDataFim()),
                Restrictions.gt(AtividadeGrupo.PROP_DATA_FIM, atividadeGrupo.getDataInicio())
        );

        Criteria critProfissionalGrupo = getSession().createCriteria(AtividadeGrupoProfissional.class)
                .add(Restrictions.in(AtividadeGrupoProfissional.PROP_PROFISSIONAL, lstProfissionais))
                .createCriteria(AtividadeGrupoProfissional.PROP_ATIVIDADE_GRUPO)
                .add(Restrictions.eq(AtividadeGrupo.PROP_SITUACAO, AtividadeGrupo.SITUACAO_PENDENTE))
                .add(Restrictions.disjunction().add(or1).add(or2).add(or3).add(or4).add(or5).add(or6));

        if (atividadeGrupo.getCodigo() != null) {
            critProfissionalGrupo.add(Restrictions.ne(AtividadeGrupo.PROP_CODIGO, atividadeGrupo.getCodigo()));
        }

        List<AtividadeGrupoProfissional> atvGrupoProfissional = critProfissionalGrupo.list();

        if (CollectionUtils.isNotNullEmpty(atvGrupoProfissional)) {
            String nomeProfissionais = "";
            for (AtividadeGrupoProfissional item : atvGrupoProfissional) {
                nomeProfissionais += "Atividade (" + item.getAtividadeGrupo().getCodigo() + ") - " + item.getProfissional().getNome() + "\n ";
            }

            nomeProfissionais = nomeProfissionais.substring(0, nomeProfissionais.length() - 2);

            throw new ValidacaoException(Bundle.getStringApplication("msg_atividade_existente_profissional", nomeProfissionais));
        }
    }
}
