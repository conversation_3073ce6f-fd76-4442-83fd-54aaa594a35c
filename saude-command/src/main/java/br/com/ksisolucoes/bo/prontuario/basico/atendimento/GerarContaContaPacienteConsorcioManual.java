package br.com.ksisolucoes.bo.prontuario.basico.atendimento;

import br.com.ksisolucoes.bo.atividadegrupo.interfaces.dto.ContaEItemContaPacienteConsorcioDTO;
import br.com.ksisolucoes.bo.atividadegrupo.interfaces.dto.ContaEItemContaPacienteTfdDTO;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.ContaContaPacienteConsorcioManualOcorrenciasDTO;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.GerarContaContaPacienteConsorcioManualDTO;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.consorcio.*;
import br.com.ksisolucoes.vo.frota.RoteiroViagem;
import br.com.ksisolucoes.vo.frota.RoteiroViagemPassageiro;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;

import java.util.Arrays;
import java.util.List;

import static br.com.celk.faturamento.FechamentoContaHelper.permiteGerarBpaSemTfd;

/**
 * Created by sulivan on 02/01/18.
 */
public class GerarContaContaPacienteConsorcioManual extends AbstractCommandTransaction {

    private RetornoValidacao retornoValidacao = new RetornoValidacao();

    private GerarContaContaPacienteConsorcioManualDTO dto;
    private ContaPaciente contaPaciente;

    private ContaContaPacienteConsorcioManualOcorrenciasDTO retorno = new ContaContaPacienteConsorcioManualOcorrenciasDTO();

    public GerarContaContaPacienteConsorcioManual(GerarContaContaPacienteConsorcioManualDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() {
        if (GerarContaContaPacienteConsorcioManualDTO.TipoConta.TFD.value().equals(dto.getTipoConta())) {
            gerarContaPacienteTfd();
        } else if (GerarContaContaPacienteConsorcioManualDTO.TipoConta.BPA.value().equals(dto.getTipoConta())) {
            gerarContaPacienteBpa();
        }
    }

    public void gerarContaPacienteTfd() {
        List<RoteiroViagemPassageiro> lst = LoadManager.getInstance(RoteiroViagemPassageiro.class)
                .addProperties(new HQLProperties(RoteiroViagemPassageiro.class).getProperties())
                .addProperties(new HQLProperties(RoteiroViagem.class, RoteiroViagemPassageiro.PROP_ROTEIRO).getProperties())
                .addProperties(new HQLProperties(UsuarioCadsus.class, RoteiroViagemPassageiro.PROP_USUARIO_CADSUS).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RoteiroViagemPassageiro.PROP_ROTEIRO, RoteiroViagem.PROP_DATA_SAIDA), dto.getPeriodo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RoteiroViagemPassageiro.PROP_ROTEIRO, RoteiroViagem.PROP_CIDADE),BuilderQueryCustom.QueryParameter.DIFERENTE, getSessao().getEmpresa().getCidade()))
                .addInterceptor(new LoadInterceptor() {
                    @Override
                    public void customHQL(HQLHelper hql, String alias) {
                        hql.addToWhereWhithAnd("NOT EXISTS(SELECT 1 FROM ContaPaciente item WHERE item.roteiroViagemPassageiro.codigo = "
                                + alias + ".codigo)"
                        );
                    }
                })
                .start().getList();
        StringBuilder msg = new StringBuilder();
        int pull = 0;
        if (lst != null) {
            for (RoteiroViagemPassageiro rvp : lst) {
                ContaEItemContaPacienteTfdDTO dto = new ContaEItemContaPacienteTfdDTO();
                dto.setRoteiroViagemPassageiro(rvp);
                dto.setGerarContaManual(true);

                try {
                    if (permiteGerarBpaSemTfd(rvp)){
                        BOFactory.getBO(AtendimentoFacade.class).salvarContaEItemContaPacienteTfdManual(dto);
                        pull += 1;
                    }
                } catch (ValidacaoException | DAOException e) {
                    msg.append(e.getMessage()).append("\n");
                } catch (Exception e) {
                    msg.append(e.getMessage()).append("\n");
                } finally {
                    getSession().flush();
                    getSession().clear();
                }
            }
        }
        retorno.setQtdContasGeradas(pull);
        retorno.setMsg(msg.toString());
    }

    private void gerarContaPacienteBpa() {
        List<ConsorcioGuiaProcedimento> consorcioGuiasProcedimento = LoadManager.getInstance(ConsorcioGuiaProcedimento.class)
                .addProperties(new HQLProperties(ConsorcioGuiaProcedimento.class).getProperties())
                .addProperties(new HQLProperties(SubConta.class, VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_SUB_CONTA)).getProperties())
                .addProperties(new HQLProperties(Conta.class, VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_SUB_CONTA, SubConta.PROP_CONTA)).getProperties())
                .addProperties(new HQLProperties(ConsorcioPrestador.class, VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(ConsorcioGuiaProcedimento.PROP_STATUS, ConsorcioGuiaProcedimento.StatusGuiaProcedimento.PAGA.value()))
                .addParameter((new QueryCustom.QueryCustomParameter(ConsorcioGuiaProcedimento.PROP_DATA_APLICACAO, dto.getPeriodo())))
                .addParameter(new QueryCustom.QueryCustomParameter(ConsorcioGuiaProcedimento.PROP_USUARIO_CADSUS, BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                .addInterceptor(new LoadInterceptor() {
                    @Override
                    public void customHQL(HQLHelper hql, String alias) {
                        hql.addToWhereWhithAnd("NOT EXISTS(SELECT 1 FROM ContaPaciente item WHERE item.consorcioGuiaProcedimento.codigo = "
                                + alias + ".codigo)"
                        );
                    }
                })
                .start().getList();

        StringBuilder msg = new StringBuilder();
        int pull = 0;
        for (ConsorcioGuiaProcedimento consorcioGuiaProcedimento : consorcioGuiasProcedimento) {

            List<ConsorcioGuiaProcedimentoItem> consorcioGuiaProcedimentoItemList = getSession().createCriteria(ConsorcioGuiaProcedimentoItem.class)
                    .add(Restrictions.eq(ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, consorcioGuiaProcedimento))
                    .add(Restrictions.ne(ConsorcioGuiaProcedimentoItem.PROP_STATUS, ConsorcioGuiaProcedimentoItem.StatusGuiaProcedimentoItem.CANCELADA.value()))
                    .createCriteria(ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_PROCEDIMENTO)
                    .createCriteria(ConsorcioProcedimento.PROP_PROCEDIMENTO)
                    .list();

            if (CollectionUtils.isNotNullEmpty(consorcioGuiaProcedimentoItemList)) {
                // Gerar Conta Paciente
                ContaEItemContaPacienteConsorcioDTO contaDTO = new ContaEItemContaPacienteConsorcioDTO();
                contaDTO.setConsorcioGuiaProcedimento(consorcioGuiaProcedimento);
                contaDTO.setLstConsorcioGuiaProcedimentoItem(consorcioGuiaProcedimentoItemList);

                try {
                    BOFactory.getBO(ConsorcioFacade.class).salvarContaEItemContaPacienteConsorcioManual(contaDTO);
                    pull += 1;
                } catch (ValidacaoException | DAOException e) {
                    msg.append(e.getMessage()).append("\n");
                } catch (Exception e) {
                    msg.append(e.getMessage()).append("\n");
                } finally {
                    getSession().flush();
                    getSession().clear();
                }
            }
        }
        retorno.setQtdContasGeradas(pull);
        retorno.setMsg(msg.toString());
    }

    public ContaContaPacienteConsorcioManualOcorrenciasDTO getContaPaciente() {
        return retorno;
    }

}