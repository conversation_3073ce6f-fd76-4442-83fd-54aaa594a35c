package br.com.ksisolucoes.bo.prontuario.basico.examebpai;

import br.com.ksisolucoes.agendamento.examebpai.dto.ExameBpaiDTOParam;
import br.com.ksisolucoes.agendamento.examebpai.dto.ExameRequisicaoBpaiDTO;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.*;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import ch.lambdaj.Lambda;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class DetalhesExameRequisicaoBpai extends CommandQuery<DetalhesExameRequisicaoBpai> {

    private ExameBpaiDTOParam param;
    private List<ExameRequisicaoBpaiDTO> result;

    public DetalhesExameRequisicaoBpai(ExameBpaiDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(ExameRequisicaoBpaiDTO.class.getName());
        ExameRequisicaoBpaiDTO proxy = Lambda.on(ExameRequisicaoBpaiDTO.class);

        hql.addToSelect("exameBpai.codigo", path(proxy.getExameBpai().getCodigo()));
        hql.addToSelect("tipoExame.codigo", path(proxy.getExameBpai().getExame().getTipoExame().getCodigo()));
        hql.addToSelect("tipoExame.descricao", path(proxy.getExameBpai().getExame().getTipoExame().getDescricao()));
        hql.addToSelect("exame1.codigo", path(proxy.getExameBpai().getExame().getCodigo()));
        hql.addToSelect("exame1.dataCadastro", path(proxy.getExameBpai().getExame().getDataCadastro()));
        hql.addToSelect("exame1.flagUrgente", path(proxy.getExameBpai().getExame().getFlagUrgente()));
        
        hql.addToSelect("exameRequisicao.codigo", path(proxy.getExameRequisicao().getCodigo()));
        hql.addToSelect("exameRequisicao.quantidade", path(proxy.getExameRequisicao().getQuantidade()));
        hql.addToSelect("exameProcedimento.codigo",path(proxy.getExameRequisicao().getExameProcedimento().getCodigo()));
        hql.addToSelect("exameProcedimento.descricaoProcedimento",path(proxy.getExameRequisicao().getExameProcedimento().getDescricaoProcedimento()));

        hql.addToFrom("ExameBpai exameBpai");
        hql.addToFrom("ExameRequisicao exameRequisicao"
                + " left join exameRequisicao.exameProcedimento exameProcedimento"
                + " left join exameBpai.exame exame1"
                + " left join exame1.tipoExame tipoExame");
        
        hql.addToWhereWhithAnd("exameRequisicao.exame = exame1");
        hql.addToWhereWhithAnd("exame1.codigo =", param.getCodigoExame());
        if (param.isContemAtendimento()) {
            hql.addToWhereWhithAnd("exame1.atendimento =", param.getAtendimento());
        } else {
            hql.addToWhereWhithAnd("exame1.atendimento <>", param.getAtendimento());
        }
        hql.addToWhereWhithAnd("exame1.dataCadastro", param.getPeriodo());
        
        hql.addToWhereWhithAnd("exame1.status in ",Arrays.asList(Exame.STATUS_SOLICITADO, Exame.STATUS_AUTORIZADO));
        hql.addToWhereWhithAnd("not exists (select laudoTfd.codigo from LaudoTfd laudoTfd where laudoTfd.exameBpai = exameBpai)");
        
        hql.addToOrder("exame1.dataCadastro desc");
    }

    @Override
    public List<ExameRequisicaoBpaiDTO> getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result =  hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
