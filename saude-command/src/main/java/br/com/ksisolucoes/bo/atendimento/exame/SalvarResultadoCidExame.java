package br.com.ksisolucoes.bo.atendimento.exame;

import br.com.celk.view.unidadesaude.exames.resultadoexame.ResultadoCidExameDTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.exame.ResultadoCidExame;
import org.jrimum.utilix.Objects;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class SalvarResultadoCidExame extends AbstractCommandTransaction {

    private ResultadoCidExame resultadoCidExame;
    private final ResultadoCidExameDTO resultadoCidExameDTO;

    public SalvarResultadoCidExame(ResultadoCidExameDTO resultadoCidExameDTO) {
        this.resultadoCidExameDTO = resultadoCidExameDTO;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        carregaResultadoCidExame();

        if (Objects.isNull(resultadoCidExame))
            resultadoCidExame = new ResultadoCidExame();

        resultadoCidExame.setDataLaudo(resultadoCidExameDTO.getDataLaudo());
        resultadoCidExame.setStatus(resultadoCidExameDTO.getStatus());
        resultadoCidExame.setProfissionalResponsavel(resultadoCidExameDTO.getProfissionalResponsavel());
        resultadoCidExame.setCid(resultadoCidExameDTO.getCid());
        resultadoCidExame.setAnexoPaciente(resultadoCidExameDTO.getAnexoPaciente());
        resultadoCidExame.setExame(resultadoCidExameDTO.getExame());
        resultadoCidExame.setAtendimentoExame(resultadoCidExameDTO.getAtendimentoExame());
        resultadoCidExame.setUsuarioCadsus(resultadoCidExameDTO.getUsuarioCadsus());
        resultadoCidExame.setContaPaciente(resultadoCidExameDTO.getContaPaciente());

        BOFactory.save(resultadoCidExame);
    }

    private void carregaResultadoCidExame() {
        if (Objects.isNotNull(resultadoCidExameDTO.getContaPaciente())) {
            ResultadoCidExameDTO proxy = on(ResultadoCidExameDTO.class);
            resultadoCidExame = LoadManager.getInstance(ResultadoCidExame.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getContaPaciente()), resultadoCidExameDTO.getContaPaciente()))
                    .start().getVO();
        }
    }

    public ResultadoCidExame getResultadoCidExame() {
        return resultadoCidExame;
    }
}
