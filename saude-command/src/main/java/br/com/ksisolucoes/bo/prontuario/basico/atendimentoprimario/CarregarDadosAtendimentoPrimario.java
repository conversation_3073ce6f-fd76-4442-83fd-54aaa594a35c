/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.prontuario.basico.atendimentoprimario;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AtendimentoPrimarioDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Comorbidade;
import br.com.ksisolucoes.vo.basico.ComorbidadePaciente;
import br.com.ksisolucoes.vo.basico.Doenca;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDoenca;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoOxigenoterapia;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoPrimario;
import br.com.ksisolucoes.vo.prontuario.basico.ClassificacaoRisco;
import br.com.ksisolucoes.vo.prontuario.basico.PreNatal;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CarregarDadosAtendimentoPrimario extends AbstractCommandTransaction {

    private Long codigoUsuarioCadsus;
    private Atendimento atendimento;
    private AtendimentoPrimarioDTO dto;

    public CarregarDadosAtendimentoPrimario(Long codigoUsuarioCadsus, Atendimento atendimento) {
        this.codigoUsuarioCadsus = codigoUsuarioCadsus;
        this.atendimento = atendimento;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
       dto = new AtendimentoPrimarioDTO();
       AtendimentoPrimario atendimentoPrimario = getAtendimentoPrimario();
       AtendimentoInformacao atendimentoInformacao = getAtendimentoInformacao();

        if(atendimentoPrimario == null){
            atendimentoPrimario = new AtendimentoPrimario();
            atendimentoPrimario.setAtendimento(atendimento);
        }
        if(atendimentoInformacao != null){
            atendimentoPrimario.setAtendimentoRN(atendimentoInformacao.getAtendimentoRN());
            atendimentoPrimario.setIndicadorAcidente(atendimentoInformacao.getIndicadorAcidente());
        }
       
       dto.setAtendimentoPrimario(atendimentoPrimario);
       dto.setAtendimentoInformacao(atendimentoInformacao );
       dto.setAtendimentoPrimarioList(getAtendimentoPrimarioList());
       dto.setAtendimentoPrimarioGestanteList(getAtendimentoPrimarioGestanteList());
       dto.setUsuarioCadsusDado(getUsuarioCadsusDados());
       dto.setComorbidadePacienteList(getComorbidadeList());
       dto.setPreNatal(getPreNatal());
       dto.setUltimoAtendimentoPrimario(getUltimoAtendimentoPrimario());
       dto.setUsuarioCadsusDoencaList(getUsuarioCadsusDoencaList());
       dto.setAtendimentoOxigenoterapiaList(getAtendimentoOxigenoterapiaList());
    }
    
    private UsuarioCadsusDado getUsuarioCadsusDados() throws DAOException, ValidacaoException {
        return LoadManager.getInstance(UsuarioCadsusDado.class)
            .addProperties(new HQLProperties(UsuarioCadsusDado.class).getProperties())
            .addParameter(new QueryCustomParameter(UsuarioCadsusDado.PROP_CODIGO, atendimento.getUsuarioCadsus().getCodigo()))
            .start().getVO();        
    }
    
    private AtendimentoPrimario getAtendimentoPrimario() throws DAOException, ValidacaoException {
        return LoadManager.getInstance(AtendimentoPrimario.class)
                .addProperties(new HQLProperties(AtendimentoPrimario.class).getProperties())
                .addProperties(new HQLProperties(Atendimento.class, VOUtils.montarPath(AtendimentoPrimario.PROP_ATENDIMENTO)).getProperties())
                .addProperties(new HQLProperties(ClassificacaoRisco.class, VOUtils.montarPath(AtendimentoPrimario.PROP_ATENDIMENTO, Atendimento.PROP_CLASSIFICACAO_RISCO)).getProperties())
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(AtendimentoPrimario.PROP_ATENDIMENTO), atendimento))
                .start().getVO();
    }
    
    private AtendimentoInformacao getAtendimentoInformacao(){
        return LoadManager.getInstance(AtendimentoInformacao.class)
                .addProperties(new HQLProperties(AtendimentoInformacao.class).getProperties())
                .addProperties(new HQLProperties(Atendimento.class, VOUtils.montarPath(AtendimentoInformacao.PROP_ATENDIMENTO_PRINCIPAL)).getProperties())
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(AtendimentoInformacao.PROP_SEQUENCIA_CICLO), atendimento.getSequencialCiclo()))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(AtendimentoInformacao.PROP_ATENDIMENTO_PRINCIPAL), atendimento.getAtendimentoPrincipal()))
                .start().getVO();
    }
    
    private List<AtendimentoPrimario> getAtendimentoPrimarioList() throws DAOException, ValidacaoException {
        return LoadManager.getInstance(AtendimentoPrimario.class)
                .addProperties(new HQLProperties(AtendimentoPrimario.class).getProperties())
                .addProperty(VOUtils.montarPath(AtendimentoPrimario.PROP_ATENDIMENTO, Atendimento.PROP_DATA_ATENDIMENTO))
                .addProperty(VOUtils.montarPath(AtendimentoPrimario.PROP_ATENDIMENTO, Atendimento.PROP_ATENDIMENTO_PRINCIPAL, Atendimento.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoPrimario.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), codigoUsuarioCadsus))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(AtendimentoPrimario.PROP_ATENDIMENTO,Atendimento.PROP_DATA_ATENDIMENTO), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .start().getList();
    }
    
    private List<AtendimentoPrimario> getAtendimentoPrimarioGestanteList() throws DAOException, ValidacaoException {
        AtendimentoPrimario proxy = on(AtendimentoPrimario.class);

        return LoadManager.getInstance(AtendimentoPrimario.class)
                .addProperties(new HQLProperties(AtendimentoPrimario.class).getProperties())
                .addProperty(path(proxy.getAtendimento().getDataAtendimento()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getAtendimento().getUsuarioCadsus().getCodigo()), codigoUsuarioCadsus))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getTipoAtendimentoPrincipal()), atendimento.getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getTipoAtendimentoPrincipal()))
                .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getAtendimento().getDataAtendimento()), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .start().getList();
    }
    
    private List<ComorbidadePaciente> getComorbidadeList() throws DAOException, ValidacaoException {
        return LoadManager.getInstance(ComorbidadePaciente.class)
                .addProperties(new HQLProperties(ComorbidadePaciente.class).getProperties())
                .addProperties(new HQLProperties(Comorbidade.class, VOUtils.montarPath(ComorbidadePaciente.PROP_COMORBIDADE)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ComorbidadePaciente.PROP_USUARIO_CADSUS, UsuarioCadsusDado.PROP_CODIGO), codigoUsuarioCadsus))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(ComorbidadePaciente.PROP_COMORBIDADE, Comorbidade.PROP_DESCRICAO), BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();
    }
    
    private PreNatal getPreNatal() throws DAOException, ValidacaoException {
        PreNatal preNatal = LoadManager.getInstance(PreNatal.class)
                .addParameter(new QueryCustomParameter(PreNatal.PROP_STATUS, PreNatal.Status.ABERTO.value()))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(PreNatal.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), codigoUsuarioCadsus))
                .start().getVO();
        
        if(preNatal == null){
            preNatal = new PreNatal();
        }
        
        return preNatal;
    }

    private AtendimentoPrimario getUltimoAtendimentoPrimario() {
        if (CollectionUtils.isNotNullEmpty(dto.getAtendimentoPrimarioList())) {
            AtendimentoPrimario ap = dto.getAtendimentoPrimarioList().get(0);
            if (ap.getAtendimento().getAtendimentoPrincipal().getCodigo().equals(atendimento.getAtendimentoPrincipal().getCodigo())) {
                return ap;
            }
        }
        return null;
    }
    
    private List<UsuarioCadsusDoenca> getUsuarioCadsusDoencaList() throws DAOException, ValidacaoException {
        return LoadManager.getInstance(UsuarioCadsusDoenca.class)
                .addProperties(new HQLProperties(UsuarioCadsusDoenca.class).getProperties())
                .addProperties(new HQLProperties(Doenca.class, VOUtils.montarPath(UsuarioCadsusDoenca.PROP_DOENCA)).getProperties())
                .addProperties(new HQLProperties(Doenca.class, VOUtils.montarPath(UsuarioCadsusDoenca.PROP_DOENCA, Doenca.PROP_DOENCA_PRINCIPAL)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusDoenca.PROP_USUARIO_CADSUS, codigoUsuarioCadsus))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(UsuarioCadsusDoenca.PROP_DOENCA, Doenca.PROP_DESCRICAO), BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();
    }

    private List<AtendimentoOxigenoterapia> getAtendimentoOxigenoterapiaList() throws DAOException, ValidacaoException {
        if (dto.getAtendimentoPrimario() != null && dto.getAtendimentoPrimario().getCodigo() != null) {
            return LoadManager.getInstance(AtendimentoOxigenoterapia.class)
                    .addProperties(new HQLProperties(AtendimentoOxigenoterapia.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoOxigenoterapia.PROP_ATENDIMENTO_PRIMARIO, AtendimentoPrimario.PROP_CODIGO), dto.getAtendimentoPrimario().getCodigo()))
                    .start().getList();
        }

        return new ArrayList();
    }
    
    public AtendimentoPrimarioDTO getDto() {
        return dto;
    }
    
}
