package br.com.ksisolucoes.bo.integracao.bpa;

import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.AtendimentoHelper;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atendimento.LancamentoBpaConsolidado;
import br.com.ksisolucoes.vo.atendimento.LancamentoBpaConsolidadoItem;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SalvarLancamentoBpaConsolidado extends AbstractCommandTransaction {

    private LancamentoBpaConsolidado lancamentoBpaConsolidado;
    private List<LancamentoBpaConsolidadoItem> lancamentoBpaConsolidadoItemList = new ArrayList<LancamentoBpaConsolidadoItem>();

    public SalvarLancamentoBpaConsolidado(LancamentoBpaConsolidado lancamentoBpaConsolidado, List<LancamentoBpaConsolidadoItem> lancamentoBpaConsolidadoItemList) {
        this.lancamentoBpaConsolidado = lancamentoBpaConsolidado;
        this.lancamentoBpaConsolidadoItemList = lancamentoBpaConsolidadoItemList;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if(CollectionUtils.isEmpty(lancamentoBpaConsolidadoItemList)){
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_pelo_menos_um_procedimento"));
        }
        
        lancamentoBpaConsolidado = BOFactory.save(lancamentoBpaConsolidado);
        
        for(LancamentoBpaConsolidadoItem item : lancamentoBpaConsolidadoItemList){
            if(AtendimentoHelper.isProcedimentoContainsCBO(item.getProcedimento())) {
                AtendimentoHelper.validarCboProfissionalProcedimento(lancamentoBpaConsolidado.getTabelaCbo(), lancamentoBpaConsolidado.getEmpresa(),
                        lancamentoBpaConsolidado.getProfissional(), item.getProcedimento());
            }
            
            item.setLancamentoBpaConsolidado(lancamentoBpaConsolidado);
        }
        
        VOUtils.persistirListaVosModificados(LancamentoBpaConsolidadoItem.class, lancamentoBpaConsolidadoItemList, new QueryCustom.QueryCustomParameter(LancamentoBpaConsolidadoItem.PROP_LANCAMENTO_BPA_CONSOLIDADO, lancamentoBpaConsolidado));
    }

    public LancamentoBpaConsolidado getLancamentoBpaConsolidado() {
        return lancamentoBpaConsolidado;
    }
}