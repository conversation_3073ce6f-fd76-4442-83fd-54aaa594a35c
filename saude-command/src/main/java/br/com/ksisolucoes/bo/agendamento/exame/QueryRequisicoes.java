package br.com.ksisolucoes.bo.agendamento.exame;

import static br.com.celk.unidadesaude.exames.SubstituirExamesAutorizadosDTO.RequisicaoDTO;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRequisicoes extends CommandQuery {

    private final Long codigoExame;
    private List<RequisicaoDTO> result;

    public QueryRequisicoes(Long codigoExame) {
        this.codigoExame = codigoExame;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("exameRequisicao", new HQLProperties(ExameRequisicao.class, "exameRequisicao").getProperties());
        hql.addToSelect("exameProcedimento", new HQLProperties(ExameProcedimento.class, "exameProcedimento").getProperties());
        hql.addToSelect("exameRequisicao.valorProcedimento", "valorProcedimento");

        hql.setTypeSelect(RequisicaoDTO.class.getName());
        hql.setConvertToLeftJoin(true);

        hql.addToFrom("ExameRequisicao exameRequisicao "
                + "join exameRequisicao.exameProcedimento exameProcedimento");

        hql.addToWhereWhithAnd("exameRequisicao.exame.codigo = ", codigoExame);
        hql.addToWhereWhithAnd("exameRequisicao.status = ", ExameRequisicao.Status.ABERTO.value());
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    public List<RequisicaoDTO> getResult() {
        return result;
    }
}
