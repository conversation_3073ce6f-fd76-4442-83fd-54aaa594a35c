package br.com.ksisolucoes.bo.vigilancia.estabelecimento;

import br.com.celk.system.report.TipoRelatorio;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.MensagemDTO;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.bo.dto.MensagemAnexoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioRelacaoEstabelecimentosSemRTDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JRExporter;
import net.sf.jasperreports.engine.JRExporterParameter;
import net.sf.jasperreports.engine.JasperPrint;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ProcessoRelacaoEstabelecimentosSemRT extends AbstractCommandTransaction {
    
    private List<Usuario> usuariosList;
    private boolean validaUsuarioNaoInformado;

    public ProcessoRelacaoEstabelecimentosSemRT(List<Usuario> usuariosList, boolean validaUsuarioNaoInformado) throws ValidacaoException, DAOException {
        this.usuariosList = usuariosList;
        this.validaUsuarioNaoInformado = validaUsuarioNaoInformado;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (!CollectionUtils.isNotNullEmpty(this.usuariosList)) {
            if (validaUsuarioNaoInformado) {
                throw new ValidacaoException(Bundle.getStringApplication("incluaUsuarioAgendadorProcesso"));
            }
            return;
        }
        
        try {
            RelatorioRelacaoEstabelecimentosSemRTDTOParam param = new RelatorioRelacaoEstabelecimentosSemRTDTOParam();
            param.setVenceuPrazo(RepositoryComponentDefault.SIM);
            
            DataReport dataReport = BOFactory.getBO(VigilanciaReportFacade.class).processoRelatorioRelacaoEstabelecimentosSemRT(param);
            
            if(dataReport != null && dataReport.getJasperPrint() != null && CollectionUtils.isNotNullEmpty(dataReport.getJasperPrint().getPages())){
                MensagemDTO mensagemDTO = new MensagemDTO();
                mensagemDTO.setAssunto(Bundle.getStringApplication("rotulo_relacao_estabelecimentos_sem_rt"));
                mensagemDTO.setUsuarios(this.usuariosList);
                mensagemDTO.setMensagem("Segue em anexo a lista dos estabelecimentos que venceram o prazo sem responsável técnico.");
                
                MensagemAnexoDTO mensagemAnexoDTO = criaFileReport(dataReport.getJasperPrint(), TipoRelatorio.PDF);
                
                List<MensagemAnexoDTO> mensagemAnexoList = new ArrayList<>();      
                mensagemAnexoList.add(mensagemAnexoDTO);
                mensagemDTO.setUploadList(mensagemAnexoList);
                BOFactory.getBO(ComunicacaoFacade.class).enviarMensagem(mensagemDTO);
            }
        } catch (ReportException | IOException ex) {
            throw new ValidacaoException(ex.getMessage());
        }
    }
    
    private MensagemAnexoDTO criaFileReport(JasperPrint jasperPrint, TipoRelatorio tipoRelatorio) throws IOException {
        MensagemAnexoDTO msgAnexoDTO = new MensagemAnexoDTO();

        try {
            JRExporter exporter;
            File newFile = File.createTempFile(Bundle.getStringApplication("rotulo_relacao_estabelecimentos_sem_rt"), tipoRelatorio.descricao());

            exporter = new net.sf.jasperreports.engine.export.JRPdfExporter();

            exporter.setParameter(JRExporterParameter.OUTPUT_FILE, newFile);
            exporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
            exporter.exportReport();

            msgAnexoDTO.setNomeArquivoOriginal(Bundle.getStringApplication("rotulo_relacao_estabelecimentos_sem_rt"));
            msgAnexoDTO.setNomeArquivoUpload(newFile.getAbsolutePath());
        } catch (JRException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        return msgAnexoDTO;
    }
}