package br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.node;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.AbstractCommandValidacaoV3;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.annotations.ValidacaoProntuarioNode;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.EvolucaoProcedimentoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.EvolucaoProntuarioProcedimentoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ReceituarioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.StringUtilKsi;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoProntuario;
import br.com.ksisolucoes.vo.prontuario.basico.EvolucaoProntuario;
import br.com.ksisolucoes.vo.prontuario.grupos.EloGrupoAtendimentoCbo;
import br.com.ksisolucoes.vo.prontuario.grupos.EloTipoAtendimentoGrupoAtendimentoCbo;
import br.com.ksisolucoes.vo.service.TempStore;
import org.hibernate.criterion.*;

import java.util.Date;
import java.util.List;
/**
 *
 * <AUTHOR> felipe
 */
@ValidacaoProntuarioNode(value=NodesAtendimentoRef.EVOLUCAO_PROCEDIMENTO,refClass= EvolucaoProcedimentoDTO.class)
public class ValidarEvolucaoProcedimentoNode extends AbstractCommandValidacaoV3<EvolucaoProcedimentoDTO> {

    public ValidarEvolucaoProcedimentoNode(Atendimento atendimento) {
        super(atendimento, true, atendimento.getProfissional());
    }

    @Override
    public EvolucaoProcedimentoDTO executarValidacao(EvolucaoProcedimentoDTO obj) throws DAOException, ValidacaoException {
        if (obj!=null) {
            if(obj.getDescricaoEvolucao()!= null){
                if (obj.getDataHistorico() == null) {
                    obj.setDataHistorico(DataUtil.getDataAtual());
                }

                Date dataMaxima = Data.addMinutos(DataUtil.getDataAtual(), 720);
                Date dataMinima = Data.removeMinutos(DataUtil.getDataAtual(), 720);
                if ((obj.getDataHistorico().after(dataMaxima)) || (obj.getDataHistorico().before(dataMinima))) {
                    validacao(Bundle.getStringApplication("rotulo_data_evolucao_nao_pode_ter_diferenca_maior_que_doze_horas_e_maior_que_data_atual"));
                }
            } else {
                validarEvolucaoObrigatoria();
            }
        } else {
            validarEvolucaoObrigatoria();
        }
        return obj;
    }
    
    private void validarEvolucaoObrigatoria(){
        DetachedCriteria dc = DetachedCriteria.forClass(EloGrupoAtendimentoCbo.class, "eloGrupo")
            .add(Restrictions.eq(EloGrupoAtendimentoCbo.PROP_TABELA_CBO, atendimento.getTabelaCbo()))
            .setProjection(Projections.property(EloGrupoAtendimentoCbo.PROP_CODIGO))
            .add(Property.forName("eloGrupo.grupoAtendimentoCbo").eqProperty("eloTipo.grupoAtendimentoCbo"));

        EloTipoAtendimentoGrupoAtendimentoCbo elo = (EloTipoAtendimentoGrupoAtendimentoCbo) getSession().createCriteria(EloTipoAtendimentoGrupoAtendimentoCbo.class, "eloTipo")
            .add(Restrictions.eq(EloTipoAtendimentoGrupoAtendimentoCbo.PROP_TIPO_ATENDIMENTO, atendimento.getNaturezaProcuraTipoAtendimento().getTipoAtendimento()))
            .add(Subqueries.exists(dc))
            .uniqueResult();    
        if(elo != null && RepositoryComponentDefault.SIM_LONG.equals(elo.getExigeEvolucao())){
            validacao(Bundle.getStringApplication("msg_nao_foi_feito_o_registro_evolucao_favor_registrar_para_concluir_atendimento"));
        }
    }

    @Override
    public void processar(EvolucaoProcedimentoDTO object, boolean lastTemp, TempStore tempStore) throws DAOException, ValidacaoException {
        if(object != null){
            if (object.getDescricaoEvolucao()!=null) {

                EvolucaoProntuario evolucao = new EvolucaoProntuario();
                evolucao.setDescricao(StringUtilKsi.htmlIsEmpty(object.getDescricaoEvolucao()) ? null : object.getDescricaoEvolucao());
                evolucao.setProfissional(tempStore.getProfissional());
                evolucao.setAtendimento(getAtendimento());
                evolucao.setDataHistorico(object.getDataHistorico());
                evolucao.setDataRegistro(DataUtil.getDataAtual());
                evolucao.setAcessoCompartilhado(object.getAcessoCompartilhado());
                evolucao.setTabelaCbo(tempStore.getTabelaCbo());
                BOFactory.getBO(ReceituarioFacade.class).salvarEvolucaoProntuario(evolucao, false);

                AtendimentoProntuario atendimentoProntuario = new AtendimentoProntuario();
                atendimentoProntuario.setData(object.getDataHistorico());
                atendimentoProntuario.setDescricao(StringUtilKsi.htmlIsEmpty(object.getDescricaoEvolucao()) ? null : getDescricaoEvolucao(object.getDescricaoEvolucao()));
                atendimentoProntuario.setTipoRegistro(AtendimentoProntuario.TipoRegistro.EVOLUCAO.value());
                atendimentoProntuario.setUsuarioCadsus(getAtendimento().getUsuarioCadsus());
                atendimentoProntuario.setEmpresa(getAtendimento().getEmpresa());
                atendimentoProntuario.setProfissional(tempStore.getProfissional());
                atendimentoProntuario.setAtendimento(getAtendimento());
                atendimentoProntuario.setTabelaCbo(tempStore.getTabelaCbo());
                atendimentoProntuario.setAcessoCompartilhado(object.getAcessoCompartilhado());

                BOFactory.save(atendimentoProntuario);
            } 
            
            if(!object.getProcedimentos().isEmpty()) {

                AtendimentoProntuario atendimentoProntuario = new AtendimentoProntuario();
                atendimentoProntuario.setData(object.getDataHistorico());
                atendimentoProntuario.setDescricao(getDescricaoEvolucaoProcedimento(object.getProcedimentos()));
                atendimentoProntuario.setTipoRegistro(AtendimentoProntuario.TipoRegistro.PROCEDIMENTOS.value());
                atendimentoProntuario.setUsuarioCadsus(getAtendimento().getUsuarioCadsus());
                atendimentoProntuario.setEmpresa(getAtendimento().getEmpresa());
                atendimentoProntuario.setProfissional(tempStore.getProfissional());
                atendimentoProntuario.setAtendimento(getAtendimento());
                atendimentoProntuario.setTabelaCbo(tempStore.getTabelaCbo());
                atendimentoProntuario.setAcessoCompartilhado(object.getAcessoCompartilhado());

                BOFactory.save(atendimentoProntuario);
            }

            if (object.getDescricaoAnotacao() != null){
                UsuarioCadsusDado usuarioCadsusDado = (UsuarioCadsusDado) getSession().createCriteria(UsuarioCadsusDado.class)
                        .add(Restrictions.eq(VOUtils.montarPath(UsuarioCadsusDado.PROP_CODIGO), atendimento.getUsuarioCadsus().getCodigo()))
                        .uniqueResult();

                if (usuarioCadsusDado == null) {
                    usuarioCadsusDado = new UsuarioCadsusDado();
                    usuarioCadsusDado.setCodigo(atendimento.getUsuarioCadsus().getCodigo());
                }
                usuarioCadsusDado.setDescricaoAnotacao(object.getDescricaoAnotacao());
                usuarioCadsusDado.setDataDados(DataUtil.getDataAtual());

                BOFactory.save(usuarioCadsusDado);
            }
        }
    }
    
    @Override
    public void processar(EvolucaoProcedimentoDTO temp) throws DAOException, ValidacaoException {}

    private String getDescricaoEvolucao(String descEvolucao) {
        StringBuilder descricao = new StringBuilder();

        if (RepositoryComponentDefault.SIM_LONG.equals(getSessao().getUsuario().getFlagUsuarioTemporario())) {
            descricao.append("<b>Registrado por: </b>");
            descricao.append(getSessao().getUsuario().getNome());
            descricao.append("\n<br/>");
        }

        descricao.append(descEvolucao);

        return descricao.toString();
    }

    private String getDescricaoEvolucaoProcedimento(List<EvolucaoProntuarioProcedimentoDTO> list) {
        StringBuilder descricao = null;

        for (EvolucaoProntuarioProcedimentoDTO dto : list) {
            if (descricao == null) {
                descricao = new StringBuilder();

                if (RepositoryComponentDefault.SIM_LONG.equals(getSessao().getUsuario().getFlagUsuarioTemporario())) {
                    descricao.append("<b>Registrado por: </b>");
                    descricao.append(getSessao().getUsuario().getNome());
                    descricao.append("\n<br/>");
                }

                descricao.append("<b><u>");
                descricao.append(Bundle.getStringApplication("rotulo_procedimentos_realizados"));
                descricao.append(": </b></u>");
            }

            descricao.append("<br/>\n<b>");
            descricao.append(dto.getProcedimentoCompetencia().getDescricaoVO());
            descricao.append("</b>");

            if (dto.getDescricao() != null) {
                descricao.append("<br/>\n");
                descricao.append(dto.getDescricao().replaceAll("\n", "<br>\n"));
            }
        }

        return descricao != null ? descricao.toString() : null;
    }
}
