package br.com.ksisolucoes.bo.cadsus.usuariocadsusesus;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEsus;

/**
 *
 * <AUTHOR>
 */
public class SaveUsuarioCadsusEsus extends SaveVO{

    private UsuarioCadsusEsus usuarioCadsusEsus;

    public SaveUsuarioCadsusEsus(Object vo) {
        super(vo);
        this.usuarioCadsusEsus = (UsuarioCadsusEsus) vo;
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.usuarioCadsusEsus.getDataCadastro() == null) {
            this.usuarioCadsusEsus.setDataCadastro(DataUtil.getDataAtual());
        }
        if (this.usuarioCadsusEsus.getDtEntradaBrasil() != null && this.usuarioCadsusEsus.getUsuarioCadsus().getDataNascimento() != null
                && this.usuarioCadsusEsus.getDtEntradaBrasil().before(this.usuarioCadsusEsus.getUsuarioCadsus().getDataNascimento())) {
            throw new ValidacaoException("Por favor, a data de entrada no Brasil não pode ser anterior a data de nascimento do paciente.");
        }


        /*
          Manter Compatilidade com Tablet - WORKAROUND
        */

//        Long LESBICA = 7L;
//        Long TRAVESTI = 9L;
//        Long TRANSSEXUAL = 10L;
//
//        if (LESBICA.equals(usuarioCadsusEsus.getOrientacaoSexual())) {
//            this.usuarioCadsusEsus.setIdentidadeGenero(UsuarioCadsusEsus.IdentidadeGenero.MULHER_TRANSGENERO.value());
//            this.usuarioCadsusEsus.setOrientacaoSexual(UsuarioCadsusEsus.OrientacaoSexual.OUTRO.value());
//        } else if (TRAVESTI.equals(usuarioCadsusEsus.getOrientacaoSexual())) {
//            this.usuarioCadsusEsus.setIdentidadeGenero(UsuarioCadsusEsus.IdentidadeGenero.TRAVESTI.value());
//            this.usuarioCadsusEsus.setOrientacaoSexual(UsuarioCadsusEsus.OrientacaoSexual.OUTRO.value());
//        } else if (TRANSSEXUAL.equals(usuarioCadsusEsus.getOrientacaoSexual())) {
//            this.usuarioCadsusEsus.setIdentidadeGenero(UsuarioCadsusEsus.IdentidadeGenero.HOMEM_TRANSGENERO.value());
//            this.usuarioCadsusEsus.setOrientacaoSexual(UsuarioCadsusEsus.OrientacaoSexual.OUTRO.value());
//        }

        if (this.usuarioCadsusEsus.getUsuarioCadsus().getNomePai() == null) {
            this.usuarioCadsusEsus.setPaiDesconhecido(RepositoryComponentDefault.SIM_LONG);
        }

        if (this.usuarioCadsusEsus.getIdentidadeGenero() != null && this.usuarioCadsusEsus.getInformaIdentidadeGenero() == null){
            this.usuarioCadsusEsus.setInformaIdentidadeGenero(RepositoryComponentDefault.SIM_LONG);
        }

    }

    @Override
    protected void depoisSave() throws ValidacaoException, DAOException {
//        BOFactory.getBO(UsuarioCadsusFacade.class).gerarAtualizarEsusFichaUsuarioCadsusEsus(this.usuarioCadsusEsus);
    }
}