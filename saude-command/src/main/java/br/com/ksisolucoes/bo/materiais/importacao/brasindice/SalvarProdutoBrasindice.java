package br.com.ksisolucoes.bo.materiais.importacao.brasindice;

import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.bo.materiais.importacao.brasindice.dto.BrasindiceDTO;
import br.com.ksisolucoes.bo.materiais.importacao.brasindice.dto.ProdutoBrasindiceDTO;
import br.com.ksisolucoes.bo.materiais.importacao.brasindice.layout.BrasindiceVoBind;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.ControleImportacaoProdutoBrasindice;
import br.com.ksisolucoes.vo.entradas.estoque.ProdutoBrasindice;
import br.com.ksisolucoes.vo.entradas.estoque.ProdutoBrasindiceItem;
import ch.lambdaj.Lambda;
import static ch.lambdaj.Lambda.having;
import static ch.lambdaj.Lambda.on;
import java.util.Date;
import java.util.List;
import static org.hamcrest.Matchers.equalTo;
import org.hibernate.Criteria;
import org.hibernate.LockMode;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class SalvarProdutoBrasindice extends AbstractCommandTransaction {

    private List<BrasindiceVoBind> list;
    private BrasindiceDTO dto;

    public SalvarProdutoBrasindice(List brasindiceBind, BrasindiceDTO dto) {
        this.list = brasindiceBind;
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        Integer idx = 0;
        ControleImportacaoProdutoBrasindice controleImportacaoProdutoBrasindice = HibernateUtil.lockTable(ControleImportacaoProdutoBrasindice.class, 1L);
        controleImportacaoProdutoBrasindice.setDataImportacao(new Date());
        BOFactory.save(controleImportacaoProdutoBrasindice);
        for (BrasindiceVoBind brasindiceBind : list) {
            try {
                salvarRegistro(brasindiceBind);
            } catch (Exception ex) {
                Loggable.log.error(ex.getMessage(), ex);
                BOFactory.getBO(MaterialBasicoFacade.class).registrarAndamentoBrasindice(dto.getCodigoBrasindiceProcess(), null, "Linha " + dto.getIndice() + idx + ": " + ex.getMessage());
            } finally {
                idx++;
            }
        }
        BOFactory.getBO(MaterialBasicoFacade.class).registrarAndamentoBrasindice(dto.getCodigoBrasindiceProcess(), idx, null);
        getSession().flush();
        getSession().clear();
    }

    private void salvarRegistro(BrasindiceVoBind bind) throws InstantiationException, IllegalAccessException, DAOException, ValidacaoException {

        Criteria criteria = getSession().createCriteria(ProdutoBrasindice.class);
        bind.setRestrictions(criteria);

        criteria.setLockMode(LockMode.PESSIMISTIC_WRITE);
        ProdutoBrasindice produtoBrasindice = (ProdutoBrasindice) criteria.uniqueResult();

        ProdutoBrasindiceDTO produtoBrasindiceDTO = new ProdutoBrasindiceDTO();
        if (produtoBrasindice != null) {
            List<ProdutoBrasindiceItem> listProdutoBrasindiceItem = getSession().createCriteria(ProdutoBrasindiceItem.class)
                    .add(Restrictions.eq(ProdutoBrasindiceItem.PROP_PRODUTO_BRASINDICE, produtoBrasindice))
                    .list();
            if (!listProdutoBrasindiceItem.isEmpty()) {
                ProdutoBrasindiceItem item = Lambda.selectFirst(listProdutoBrasindiceItem, having(on(ProdutoBrasindiceItem.class).getTipoPreco(), equalTo(bind.getFlagTipoPreco())));
                if (item != null) {
                    produtoBrasindiceDTO = new ProdutoBrasindiceDTO(item.getProdutoBrasindice(), item);
                } else {
                    produtoBrasindiceDTO = new ProdutoBrasindiceDTO(produtoBrasindice, new ProdutoBrasindiceItem());
                }
            }
        } else {
            produtoBrasindiceDTO = new ProdutoBrasindiceDTO(new ProdutoBrasindice(), new ProdutoBrasindiceItem());
        }

        ProdutoBrasindiceDTO voToSave = (ProdutoBrasindiceDTO) bind.vosToSave(produtoBrasindiceDTO, getSession());

        if (voToSave != null) {
            getSession().save(voToSave.getProdutoBrasindice());
            voToSave.getProdutoBrasindiceItem().setDataInicioVigencia(this.dto.getDataVigencia());
            getSession().save(voToSave.getProdutoBrasindiceItem());
        }
    }
}
