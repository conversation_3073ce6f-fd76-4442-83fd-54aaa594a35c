package br.com.ksisolucoes.bo.atendimento.tipoatendimento;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.facade.AtendimentoGeralFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ExcluirTipoAtendimento extends AbstractCommandTransaction {

    private TipoAtendimento tp;

    public ExcluirTipoAtendimento(TipoAtendimento tp) {
        this.tp = tp;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        List<TipoAtendimento> lstTipoAtendimento = LoadManager.getInstance(TipoAtendimento.class)
                .addParameter(new QueryCustom.QueryCustomParameter(TipoAtendimento.PROP_TIPO_ATENDIMENTO_AGRUPADOR, tp))
                .start().getList();

        for (TipoAtendimento tipoAtendimento : lstTipoAtendimento) {
            tipoAtendimento = (TipoAtendimento) getSession().get(TipoAtendimento.class, tipoAtendimento.getCodigo());
            tipoAtendimento.setTipoAtendimentoAgrupador(null);
            BOFactory.save(tipoAtendimento);
        }

        List<NaturezaProcuraTipoAtendimento> lstNaturezaProcura = LoadManager.getInstance(NaturezaProcuraTipoAtendimento.class)
                .addParameter(new QueryCustom.QueryCustomParameter(NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, tp))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(lstNaturezaProcura)) {
            for (NaturezaProcuraTipoAtendimento npta : lstNaturezaProcura) {
                BOFactory.getBO(AtendimentoGeralFacade.class).removerNaturezaProcuraTipoAtendimento(npta);
            }
        }

        BOFactory.getBO(AtendimentoFacade.class).removerTipoAtendimento(tp);
    }
}
