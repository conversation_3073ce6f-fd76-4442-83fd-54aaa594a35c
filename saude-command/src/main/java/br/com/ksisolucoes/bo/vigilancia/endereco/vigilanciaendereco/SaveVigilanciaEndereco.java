package br.com.ksisolucoes.bo.vigilancia.endereco.vigilanciaendereco;

import br.com.celk.vigilancia.fiscalnarua.SqsSincronizadorFiscalRuaDto;
import br.com.celk.vigilancia.fiscalnarua.TipoMensagemSqsFiscalRua;
import br.com.celk.vigilancia.fiscalnarua.VigilanciaEnderecoDTO;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.celk.util.Coalesce;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;

public class SaveVigilanciaEndereco extends SaveVO {

    private VigilanciaEndereco vigilanciaEndereco;

    public SaveVigilanciaEndereco(Object vo) {
        super(vo);
        this.vigilanciaEndereco = (VigilanciaEndereco) vo;
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        
        String cep = StringUtil.getDigits(vigilanciaEndereco.getCep());
        
        VigilanciaEndereco ve = LoadManager.getInstance(VigilanciaEndereco.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VigilanciaEndereco.PROP_CEP, cep))
                .addParameter(new QueryCustom.QueryCustomParameter(VigilanciaEndereco.PROP_CIDADE, this.vigilanciaEndereco.getCidade()))
                .addParameter(new QueryCustom.QueryCustomParameter(VigilanciaEndereco.PROP_BAIRRO, this.vigilanciaEndereco.getBairro().trim().toUpperCase()))
                .addParameter(new QueryCustom.QueryCustomParameter(VigilanciaEndereco.PROP_LOGRADOURO, this.vigilanciaEndereco.getLogradouro().trim().toUpperCase()))
                .setMaxResults(1)
                .start().getVO();

        if (Coalesce.asString(vigilanciaEndereco.getLogradouro().trim()).equals("")) {
            addRetornoValidacao(Bundle.getStringApplication("rotulo_logradouro"));
        }

        if (ve != null) {
            vigilanciaEndereco.getRetornoValidacao().add(Bundle.getStringApplication("msg_endereco_ja_cadastrado"));
        }

        if (!vigilanciaEndereco.getRetornoValidacao().isValido()) {
            throw new ValidacaoException(vigilanciaEndereco.getRetornoValidacao());
        }

        if (this.vigilanciaEndereco.getDataAlteracao() != null) {
            this.vigilanciaEndereco.setDataAlteracao(Data.getDataAtual());
        }

        vigilanciaEndereco.setCep(cep);
    }
    @Override
    public void depoisSave() throws DAOException, ValidacaoException {
        BOFactory.getBO(VigilanciaFacade.class).enviarMensagemSqsSincronizadorFiscalRua(new SqsSincronizadorFiscalRuaDto(new VigilanciaEnderecoDTO(this.vigilanciaEndereco)), TipoMensagemSqsFiscalRua.VIGILANCIA_ENDERECO);
    }

    private void addRetornoValidacao(String stringApplication) {
        if (vigilanciaEndereco.getRetornoValidacao().isValido()) {
            vigilanciaEndereco.getRetornoValidacao().add(Bundle.getStringApplication("msg_campos_obrigatorios_faltantes"));
        }
        vigilanciaEndereco.getRetornoValidacao().add(stringApplication + ", ");
    }

    public VigilanciaEndereco getVigilanciaEndereco() {
        return vigilanciaEndereco;
    }
}
