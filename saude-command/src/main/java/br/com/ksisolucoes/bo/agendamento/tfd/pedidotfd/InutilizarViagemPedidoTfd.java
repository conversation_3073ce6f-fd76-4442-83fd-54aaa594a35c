/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.agendamento.tfd.pedidotfd;

import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd;
import br.com.ksisolucoes.vo.cadsus.TipoOcorrencia;

/**
 *
 * <AUTHOR>
 */
public class InutilizarViagemPedidoTfd extends AbstractCommandTransaction<InutilizarViagemPedidoTfd> {

    private Long codigoPedidoTfd;
    private String motivo;

    public InutilizarViagemPedidoTfd(Long codigoPedidoTfd, String motivo) {
        this.codigoPedidoTfd = codigoPedidoTfd;
        this.motivo = motivo;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        PedidoTfd pedidoTfd = (PedidoTfd) this.getSession().get(PedidoTfd.class, this.codigoPedidoTfd);

        pedidoTfd.setDataContatoViagem(Data.getDataAtual());
        pedidoTfd.setStatusTransporte(PedidoTfd.StatusTransporte.NAO_UTILIZA.getValue());

        BOFactory.getBO(CadastroFacade.class).save(pedidoTfd);

        gerarOcorrenciaUsuarioCadsus(pedidoTfd);

    }

    private void gerarOcorrenciaUsuarioCadsus(PedidoTfd pedidoTfd) throws DAOException, ValidacaoException {

        String descricao = Bundle.getStringApplication("msg_pedido_tfd_informado_que_nao_vai_utilizar_transporte_motivo_X",this.motivo);

        LaudoTfd laudoTfd = pedidoTfd.getLaudoTfd();

        BOFactory.getBO(UsuarioCadsusFacade.class).gerarOcorrenciaUsuarioCadsus(laudoTfd.getUsuarioCadsus(), TipoOcorrencia.TIPO_TFD, descricao, laudoTfd);
    }

}
