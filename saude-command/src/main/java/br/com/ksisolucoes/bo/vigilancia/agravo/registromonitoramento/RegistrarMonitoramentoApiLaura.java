package br.com.ksisolucoes.bo.vigilancia.agravo.registromonitoramento;

import br.com.celk.bo.service.rest.monitoramentolaura.MonitoramentoRestDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.base.BaseProfissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravoOcorrencia;
import br.com.ksisolucoes.vo.vigilancia.agravo.TipoOcorrenciaAgravo;
import br.com.ksisolucoes.vo.vigilancia.agravo.base.BaseRegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.agravo.base.BaseTipoOcorrenciaAgravo;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 */
public class RegistrarMonitoramentoApiLaura extends AbstractCommandTransaction<RegistrarMonitoramentoApiLaura> {

    private final MonitoramentoRestDTO dto;

    public RegistrarMonitoramentoApiLaura(MonitoramentoRestDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        validar();
        RegistroAgravo ra = buscarRegistroAgravo(this.dto.getCodigoNotificacao());
        if (ra == null) throw new ValidacaoException(Bundle.getStringApplication("conteudo_invalido"));

        processar(ra);
    }

    public RegistroAgravoOcorrencia processar(RegistroAgravo ra) throws ValidacaoException, DAOException {
        if (ra == null) return null;

        atualizarStatusRegitroAgravo(ra);

        return criarOcorrencia(ra);
    }

    public RegistroAgravo atualizarStatusRegitroAgravo(RegistroAgravo ra) throws DAOException, ValidacaoException {
        if (RegistroAgravo.Status.PENDENTE.value().equals(ra.getStatus())) {
            ra.setStatus(RegistroAgravo.Status.MONITORAMENTO.value());
            return salvarRegistroAgravo(ra);
        }
        return null;
    }

    public RegistroAgravoOcorrencia criarOcorrencia(RegistroAgravo ra) throws ValidacaoException, DAOException {
        if (ra == null) return null;

        RegistroAgravoOcorrencia registroAgravoOcorrencia = new RegistroAgravoOcorrencia();
        registroAgravoOcorrencia.setRegistroAgravo(ra);
        registroAgravoOcorrencia.setDataOcorrencia(new LocalDate(dto.getDataResposta()).toDate());
        registroAgravoOcorrencia.setDescricaoOcorrencia(dto.getRespostas());
        registroAgravoOcorrencia.setProfissional(buscarProfissionalPadrao(330141533L));
        registroAgravoOcorrencia.setTipoOcorrenciaAgravo(buscarTipoOcorrenciaAgravo(dto.getMonitoramento()));
        registroAgravoOcorrencia.setUsuarioCadastro((Usuario) getSession().get(Usuario.class, Usuario.USUARIO_ADMINISTRADOR));

        return salvarRegistroAgravoOcorrencia(registroAgravoOcorrencia);
    }

    public RegistroAgravoOcorrencia salvarRegistroAgravoOcorrencia(RegistroAgravoOcorrencia registroAgravoOcorrencia) throws DAOException, ValidacaoException {
        return BOFactory.save(registroAgravoOcorrencia);
    }

    public RegistroAgravo salvarRegistroAgravo(RegistroAgravo ra) throws DAOException, ValidacaoException {
        return BOFactory.save(ra);
    }

    private void validar() throws ValidacaoException {
        String applicationConteudoInvalido = "conteudo_invalido";
        if (dto == null) throw new ValidacaoException(Bundle.getStringApplication(applicationConteudoInvalido));

        if (MonitoramentoRestDTO.Monitoramento.getInstance(dto.getMonitoramento()) == null)
            throw new ValidacaoException(Bundle.getStringApplication(applicationConteudoInvalido));

        if (dto.getCodigoNotificacao() == null)
            throw new ValidacaoException(Bundle.getStringApplication(applicationConteudoInvalido));

        if (!isDataRespostaValida(dto.getDataResposta()))
            throw new ValidacaoException(Bundle.getStringApplication(applicationConteudoInvalido));
    }

    public boolean isDataRespostaValida(String dataResposta) {
        if (StringUtils.isEmpty(dataResposta)) return false;

        String pattern = "yyyy-MM-dd";
        try {
            DateTimeFormatter fmt = DateTimeFormat.forPattern(pattern);
            fmt.parseDateTime(dataResposta);
        } catch (Exception e) {
            return false;
        }
        return true;
    }


    public RegistroAgravo buscarRegistroAgravo(Long codigoNotificacao) {
        RegistroAgravo registroAgravo = null;
        if (codigoNotificacao != null) {
            registroAgravo = (RegistroAgravo) getSession().createCriteria(RegistroAgravo.class)
                    .add(Restrictions.eq(BaseRegistroAgravo.PROP_CODIGO_NOTIFICACAO, codigoNotificacao))
                    .setMaxResults(1).uniqueResult();
        }
        return registroAgravo;
    }

    public TipoOcorrenciaAgravo buscarTipoOcorrenciaAgravo(String monitoramento) {
        if (!StringUtils.isEmpty(monitoramento)) {
            return (TipoOcorrenciaAgravo) getSession().createCriteria(TipoOcorrenciaAgravo.class)
                    .add(Restrictions.eq(BaseTipoOcorrenciaAgravo.PROP_CODIGO, MonitoramentoRestDTO.Monitoramento.getInstance(dto.getMonitoramento()).value()))
                    .setMaxResults(1).uniqueResult();
        }
        return null;
    }

    public Profissional buscarProfissionalPadrao(Long codigoProfissional) {
        if (codigoProfissional == null) return null;
        return (Profissional) getSession().createCriteria(Profissional.class)
                .add(Restrictions.eq(BaseProfissional.PROP_CODIGO, codigoProfissional))
                .setMaxResults(1).uniqueResult();
    }
}
