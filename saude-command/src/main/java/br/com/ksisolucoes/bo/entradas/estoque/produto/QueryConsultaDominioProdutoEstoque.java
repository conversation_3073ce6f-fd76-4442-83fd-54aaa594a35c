/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.entradas.estoque.produto;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.DominioProdutoEstoqueDTO;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryConsultaDominioProdutoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import org.hibernate.Query;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaDominioProdutoEstoque extends CommandQueryPager<QueryConsultaDominioProdutoEstoque> {

    private QueryConsultaDominioProdutoDTOParam param;

    public QueryConsultaDominioProdutoEstoque(QueryConsultaDominioProdutoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException {
        DominioProdutoEstoqueDTO on = on(DominioProdutoEstoqueDTO.class);

        hql.addToSelectAndGroup("produto.codigo", path(on.getDominioProduto().getProduto().getCodigo()));
        hql.addToSelectAndGroup("produto.referencia", path(on.getDominioProduto().getProduto().getReferencia()));
        hql.addToSelectAndGroup("unidade.descricao", path(on.getDominioProduto().getProduto().getUnidade().getDescricao()));
        hql.addToSelectAndGroup("unidade.unidade", path(on.getDominioProduto().getProduto().getUnidade().getUnidade()));

        hql.addToSelectAndGroup("dominioProduto.nome", path(on.getDominioProduto().getNome()));
        hql.addToSelectAndGroup("subGrupo.id.codigo", path(on.getDominioProduto().getSubGrupo().getId().getCodigo()));
        hql.addToSelectAndGroup("subGrupo.id.codigoGrupoProduto", path(on.getDominioProduto().getSubGrupo().getId().getCodigoGrupoProduto()));
        hql.addToSelectAndGroup("subGrupo.descricao", path(on.getDominioProduto().getSubGrupo().getDescricao()));
        hql.addToSelectAndGroup("subGrupo.flagControlaGrupoEstoque",path(on.getDominioProduto().getSubGrupo().getFlagControlaGrupoEstoque()));
        hql.addToSelectAndGroup("subGrupo.flagNaoPadronizado",path(on.getDominioProduto().getSubGrupo().getFlagNaoPadronizado()));
        hql.addToSelectAndGroup("grupoProduto.codigo", path(on.getDominioProduto().getSubGrupo().getRoGrupoProduto().getCodigo()));
        hql.addToSelectAndGroup("grupoProduto.descricao", path(on.getDominioProduto().getSubGrupo().getRoGrupoProduto().getDescricao()));

        if(getDataPaging().getType().equals(DataPaging.Type.ALVO_LIST)){
            HQLHelper hqlSub = hql.getNewInstanceSubQuery();

            hqlSub.addToSelect("sum(grupoEstoqueSub.estoqueFisico + grupoEstoqueSub.estoqueEncomendado - grupoEstoqueSub.estoqueReservado)");

            hqlSub.addToFrom("GrupoEstoque grupoEstoqueSub"
                    + " left join grupoEstoqueSub.id.estoqueEmpresa estoqueEmpresaSub"
                    + " left join estoqueEmpresaSub.id.empresa empresaSub"
                    + " left join estoqueEmpresaSub.id.produto produtoSub");

            hqlSub.addToWhereWhithAnd("coalesce(grupoEstoqueSub.dataValidade, :dataAtual ) >=", Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial());
            hqlSub.addToWhereWhithAnd("produto = produtoSub");
            hqlSub.addToWhereWhithAnd("empresaSub in", this.param.getEmpresas());
            if (RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("mostrarEstoqueAlmoxarifado"))) {
                hql.addToWhereWhithAnd("empresa.tipoUnidade in ", Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_UNIDADE, Empresa.TIPO_ESTABELECIMENTO_FARMACIA, Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_MEDICAMENTO));
            } else{
                hqlSub.addToWhereWhithAnd("empresaSub.tipoUnidade in ", Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_UNIDADE, Empresa.TIPO_ESTABELECIMENTO_FARMACIA));
            }
            hql.addToSelect("("+hqlSub.getQuery()+")", path(on.getEstoqueDisponivel()));
        }

        if(getDataPaging().getType().equals(DataPaging.Type.ALVO_LIST)){
            HQLHelper hqlSub = hql.getNewInstanceSubQuery();

            hqlSub.addToSelect("sum(grupoEstoqueSub.estoqueFisico + grupoEstoqueSub.estoqueEncomendado - grupoEstoqueSub.estoqueReservado)");

            hqlSub.addToFrom("GrupoEstoque grupoEstoqueSub"
                    + " left join grupoEstoqueSub.id.estoqueEmpresa estoqueEmpresaSub"
                    + " left join estoqueEmpresaSub.id.empresa empresaSub"
                    + " left join estoqueEmpresaSub.id.produto produtoSub");

            hqlSub.addToWhereWhithAnd("coalesce(grupoEstoqueSub.dataValidade, :dataAtual) >=", Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial());
            hqlSub.addToWhereWhithAnd("produto = produtoSub");

            if (BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("mostrarSaldoDaFarmacianaPrescrIcaointerna") == null) {
                hqlSub.addToWhereWhithAnd("empresaSub = ", getSessao().getEmpresa());
            } else {
                hqlSub.addToWhereWhithAnd("empresaSub = ", BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("mostrarSaldoDaFarmacianaPrescrIcaointerna"));
            }

            if (RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("mostrarEstoqueAlmoxarifado"))) {
                hql.addToWhereWhithAnd("empresa.tipoUnidade in ", Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_UNIDADE, Empresa.TIPO_ESTABELECIMENTO_FARMACIA, Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_MEDICAMENTO));
            } else {
                hqlSub.addToWhereWhithAnd("empresaSub.tipoUnidade in ", Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_UNIDADE, Empresa.TIPO_ESTABELECIMENTO_FARMACIA));
            }

            hql.addToSelect("("+hqlSub.getQuery()+")", path(on.getEstoqueUnidade()));
        }

        hql.setTypeSelect(DominioProdutoEstoqueDTO.class.getName());
        hql.addToFrom("DominioProduto dominioProduto"
                + " left join dominioProduto.produto produto"
                + " left join dominioProduto.empresa empresa"
                + " left join dominioProduto.subGrupo subGrupo"
                + " left join subGrupo.roGrupoProduto grupoProduto"
                + " left join produto.unidade unidade"
                + " left join produto.tipoReceita tipoReceita"
                );
        if(param.getCboAtendimento() != null){
            hql.addToFrom("EloGrupoAtendimentoCbo eloGrupoAtendimentoCbo"
                + " left join eloGrupoAtendimentoCbo.grupoAtendimentoCbo grupoAtendimentoCbo"
                + " left join eloGrupoAtendimentoCbo.tabelaCbo cboGrupo");

         hql.addToWhereWhithAnd("cboGrupo.cbo = :cboAtendimento");

          hql.addToWhereWhithAnd("(grupoAtendimentoCbo.filtrarMedicamentos = :NAO "
                + " or grupoAtendimentoCbo.codigo in ("
                    + " select gac2.codigo from ProdutoGrupoAtendimento pga"
                    + " left join pga.grupoAtendimentoCbo gac2"
                    + " left join pga.produto produto2 "
                    + " where produto2 = produto )"
                + " )");
        }
        hql.addToWhereWhithAnd("produto.referencia = ", this.param.getReferencia());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("dominioProduto.nome", this.param.getDescricao(), false, true));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("dominioProduto.keyword", this.param.getKeyword(), false, true));
        hql.addToWhereWhithAnd("subGrupo = ", this.param.getSubGrupo() );
        hql.addToWhereWhithAnd("grupoProduto = ", this.param.getGrupoProduto() );
        hql.addToWhereWhithAnd("empresa in ", this.param.getEmpresas() );
        hql.addToWhereWhithAnd("tipoReceita.tipoReceita in ", this.param.getTiposReceita() );
        hql.addToWhereWhithAnd("produto.usoContinuo = ", this.param.getUsoContinuo() );
        hql.addToWhereWhithAnd("produto.usoContinuo = ", this.param.getUsoContinuo() );
        hql.addToWhereWhithAnd("subGrupo.flagMedicamento = ", this.param.getMedicamento() );
        hql.addToWhereWhithAnd("subGrupo.flagJudicial = ", this.param.getMostrarJudicial());

        if (QueryConsultaDominioProdutoDTOParam.Exibir.SOMENTE_ATIVO.equals(this.param.getExibir())) {
            hql.addToWhereWhithAnd("dominioProduto.situacao = ", RepositoryComponentDefault.SIM);
        } else if (QueryConsultaDominioProdutoDTOParam.Exibir.SOMENTE_INATIVO.equals(this.param.getExibir())) {
            hql.addToWhereWhithAnd("dominioProduto.situacao = ", RepositoryComponentDefault.NAO);
        }

        //Não incluir inativos
        if (!param.getIncluirInativos()){
            hql.addToWhereWhithAnd("produto.flagAtivo = ", RepositoryComponentDefault.SIM_LONG);
        }

        if (param.isPedidoLicitatorio()) {
            hql.addToWhereWhithAnd("produto.flagDisponivelPedidoLicitacao = ", RepositoryComponentDefault.SIM_LONG);
        }

        if(param.getPropSort() != null){
            hql.addToOrder(param.getPropSort()+" "+ (param.isAscending()?"asc":"desc"));
            hql.addToGroup(param.getPropSort());
        }else{
            hql.addToOrder("dominioProduto.nome");
        }

    }

    @Override
    protected void setParameters(Query query) {
        if(getDataPaging().getType().equals(DataPaging.Type.ALVO_LIST)){
            query.setDate("dataAtual", DataUtil.getDataAtual());
        }
        if(param.getCboAtendimento() != null){
            query.setParameter("NAO", RepositoryComponentDefault.NAO_LONG);
            query.setParameter("cboAtendimento", param.getCboAtendimento().getCbo());
        }
    }

    @Override
    protected HQLHelper customHQLCount(HQLHelper hql) {
        hql.setSelect("count(distinct produto.codigo)");
        hql.setGroup("");
        return hql;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

}
