package br.com.ksisolucoes.bo.vigilancia.requerimentovigilancia;

import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaCancelamentoFinalizacaoDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAutorizacaoSanitaria;
import org.hibernate.criterion.Restrictions;

/**
 * <AUTHOR>
 */
public class AtualizarRequerimentoAutorizacaoSanitaria extends AbstractCommandTransaction<AtualizarRequerimentoAutorizacaoSanitaria> {

    private RequerimentoVigilancia requerimentoVigilancia;
    private RequerimentoVigilanciaCancelamentoFinalizacaoDTO dto;

    public AtualizarRequerimentoAutorizacaoSanitaria(RequerimentoVigilancia requerimentoVigilancia, RequerimentoVigilanciaCancelamentoFinalizacaoDTO dto) {
        this.requerimentoVigilancia = requerimentoVigilancia;
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        RequerimentoAutorizacaoSanitaria requerimentoAutorizacaoSanitaria = (RequerimentoAutorizacaoSanitaria) getSession().createCriteria(RequerimentoAutorizacaoSanitaria.class)
                .add(Restrictions.eq(VOUtils.montarPath(RequerimentoAutorizacaoSanitaria.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_CODIGO), requerimentoVigilancia.getCodigo()))
                .setMaxResults(1).uniqueResult();

        requerimentoAutorizacaoSanitaria.setDescricaoObservacaoDestaque(dto.getObservacaoAlvara());
        BOFactory.save(requerimentoAutorizacaoSanitaria);
    }


    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }
}