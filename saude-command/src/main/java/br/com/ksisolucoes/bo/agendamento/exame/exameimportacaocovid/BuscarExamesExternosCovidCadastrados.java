package br.com.ksisolucoes.bo.agendamento.exame.exameimportacaocovid;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.vo.prontuario.basico.ExameExternoCovid;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

import java.util.Date;
import java.util.List;

public class BuscarExamesExternosCovidCadastrados {

    private BuscarExamesExternosCovidCadastrados() {}

    public static List<ExameExternoCovid> getExames(List<Date> datasNascimento) {
        ExameExternoCovid exameExternoCovid = on(ExameExternoCovid.class);

        return LoadManager.getInstance(ExameExternoCovid.class)
                .addProperty(path(exameExternoCovid.getNomePaciente()))
                .addProperty(path(exameExternoCovid.getDataNascimento()))
                .addProperty(path(exameExternoCovid.getDataExame()))
                .addProperty(path(exameExternoCovid.getDescricaoExame()))
                .addProperty(path(exameExternoCovid.getEmpresa()))
                .addProperty(path(exameExternoCovid.getEmpresa().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(exameExternoCovid.getDataNascimento()), QueryCustom.QueryCustomParameter.IN, datasNascimento))
                .start().getList();
    }
}
