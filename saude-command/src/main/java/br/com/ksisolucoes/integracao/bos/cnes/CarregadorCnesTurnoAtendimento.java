/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.integracao.bos.cnes;

import br.com.ksisolucoes.integracao.conversao.MCGenericType;
import br.com.ksisolucoes.integracao.dao.DAOException;
import br.com.ksisolucoes.integracao.dao.interfaces.SearchDAO;
import br.com.ksisolucoes.integracao.dao.util.vo.AtributoSync;
import br.com.ksisolucoes.integracao.dao.util.vo.EntidadeSync;
import br.com.ksisolucoes.integracao.vos.AtributoImpl;
import br.com.ksisolucoes.integracao.vos.interfaces.Entidade;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CarregadorCnesTurnoAtendimento extends CarregadorCnesTemplate {

    private SearchDAO daoDestino;
    private SearchDAO daoOrigem;

    public CarregadorCnesTurnoAtendimento(SearchDAO daoOrigem, SearchDAO daoDestino) {
        this.daoOrigem = daoOrigem;
        this.daoDestino = daoDestino;
    }

    @Override
    protected List<Entidade> load() throws DAOException {
        EntidadeSync entidadeSync = new EntidadeSync("NFCES011", "turno_atendimento");
        entidadeSync.addAtributo(new AtributoSync("COD_TURNAT", new AtributoImpl("cd_turno_atendimento", true), new MCGenericType(Long.class)));
        entidadeSync.addAtributo(new AtributoSync("DESCRICAO", new AtributoImpl("ds_turno_atendimento")));

        return this.daoOrigem.loadSynchronizer(entidadeSync);
    }
}
