/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.integracao.exportacao.geradores;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.integracao.exportacao.consultas.ConsultaEnderecosUsuarios;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEndereco;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import noNamespace.ROOTDocument.ROOT.DADOS.CADASTROS.ENDERECO;
import noNamespace.ROOTDocument.ROOT.DADOS.CADASTROS.ENDERECO.USUARIOS.USUARIO;
import noNamespace.ROOTDocument.ROOT.DADOS.CADASTROS.ENDERECO.USUARIOS.USUARIO.GRUPORLENDERECO;
import noNamespace.ROOTDocument.ROOT.DADOS.CADASTROS.ENDERECO.USUARIOS.USUARIO.GRUPORLENDERECO.RLENDERECO;

/**
 *
 * <AUTHOR>
 */
public class GeradorGRUPORLENDERECO {

    private GRUPORLENDERECO[] grupoRlEnderecoArray;
    private USUARIO usuario;
    private ENDERECO endereco;
    private UsuarioCadsus usuarioCadsus;
    private Date dataControle;

    public GeradorGRUPORLENDERECO(USUARIO usuario, ENDERECO endereco, EnderecoUsuarioCadsus enderecoUsuarioCadsus, UsuarioCadsus usuarioCadsus,Date dataControle) {
        this.usuario = usuario;
        this.endereco = endereco;
        this.usuarioCadsus = usuarioCadsus;
        this.dataControle = dataControle;
    }

    public GRUPORLENDERECO[] getGrupoRlEnderecoArray() throws DAOException, ValidacaoException {
        if(grupoRlEnderecoArray == null){
            carregar();
        }
        return grupoRlEnderecoArray;
    }

    private void carregar() throws DAOException, ValidacaoException {

        GRUPORLENDERECO grupo = GRUPORLENDERECO.Factory.newInstance();
        grupoRlEnderecoArray = new GRUPORLENDERECO[]{grupo};

         ConsultaEnderecosUsuarios consultaEnderecoUsuarios = new ConsultaEnderecosUsuarios();
         consultaEnderecoUsuarios.setUsuarioCadsus(usuarioCadsus);
         consultaEnderecoUsuarios.setDataControle(dataControle);
         consultaEnderecoUsuarios.start();

         List<UsuarioCadsusEndereco> usuarioCadsusEnderecoList = consultaEnderecoUsuarios.getUsuarioCadsusEnderecoList();
         if(CollectionUtils.isNotNullEmpty(usuarioCadsusEnderecoList)){
             for (UsuarioCadsusEndereco usuarioCadsusEndereco : usuarioCadsusEnderecoList) {

                 RLENDERECO rlendereco = grupo.addNewRLENDERECO();

                 rlendereco.setCOENDERECO(usuarioCadsusEndereco.getId().getEndereco().getEnderecoInterno());
                 rlendereco.setCOUSUARIO(usuario.getCOUSUARIO());
                 rlendereco.setCOBANCO("");
                 rlendereco.setCOTIPOENDERECO(usuarioCadsusEndereco.getTipoEndereco()==null?"":Coalesce.asString(usuarioCadsusEndereco.getTipoEndereco().getCodigo()));
                 if(UsuarioCadsusEndereco.STATUS_CANCELADO == Coalesce.asLong(usuarioCadsusEndereco.getStatus())){
                    rlendereco.setSTCONTROLE("X");
                    rlendereco.setSTEXCLUIDO(RepositoryComponentDefault.EXCLUIDO.toString());
                 } else if(dataControle.equals(usuarioCadsusEndereco.getDataAlteracao())){
                    rlendereco.setSTCONTROLE("I");
                    rlendereco.setSTEXCLUIDO(RepositoryComponentDefault.NAO_EXCLUIDO.toString());
                 }else{
                    rlendereco.setSTCONTROLE("A");
                    rlendereco.setSTEXCLUIDO(RepositoryComponentDefault.NAO_EXCLUIDO.toString());
                 }
                 rlendereco.setDTOPERACAO(new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(dataControle));
                 rlendereco.setNUVERSAO("0");
                 rlendereco.setSTVINCULO(Coalesce.asString(usuarioCadsusEndereco.getVinculo()));
             }
         }
    }

}
