/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.integracao.bos.datasus.cadsus;

import br.com.ksisolucoes.integracao.bos.interfaces.InterfaceCommand;
import br.com.ksisolucoes.integracao.conversao.datasus.cadsus.MCCadSusDefault;
import br.com.ksisolucoes.integracao.vos.interfaces.Entidade;
import br.com.ksisolucoes.integracao.vos.interfaces.Entidade.Atributo;
import br.com.ksisolucoes.integracao.dao.util.ValorAtributoExistente;
import br.com.ksisolucoes.integracao.dao.util.ValorAtributoSequencial;
import br.com.ksisolucoes.integracao.dao.interfaces.DAO;
import br.com.ksisolucoes.integracao.dao.util.ValorAtributoCodigoUsuarioCadsus;
import br.com.ksisolucoes.integracao.dao.util.ValorAtributoSequence;
import br.com.ksisolucoes.integracao.dao.util.ValorAtributoTipoDocumentoUsuario;
import br.com.ksisolucoes.integracao.util.DOMLibrary;
import br.com.ksisolucoes.integracao.vos.AtributoXml;
import br.com.ksisolucoes.integracao.vos.EntidadeXml;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.controle.Usuario;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

/**
 *
 * <AUTHOR>
 */
public class CarregadorCadsus implements InterfaceCommand {

    private List<Entidade> entidades = new ArrayList();
    private List<String> mapeamentos;
    private DAO dao;
    private Date dataImportacao;

    public CarregadorCadsus(DAO dao) {
        this.dao = dao;
    }

    public void setMapeamentos(List<String> mapeamentos) {
        this.mapeamentos = mapeamentos;
    }

    public void setDataImportacao(Date dataImportacao) {
        this.dataImportacao = dataImportacao;
    }

    public List<EntidadeXml> getMapeamento(String xml) {
        DOMLibrary dOMLibrary = new DOMLibrary();
        Document document = dOMLibrary.getDOM(xml, false);
        if (document == null) {
            Loggable.log.error("ERRO: " + dOMLibrary.getLastErr());
            throw new RuntimeException(Bundle.getStringApplication("msg_caminho_para_o_arquivo_invalido"));
        }
        List<EntidadeXml> entidadesXML = new ArrayList();

        NodeList enderecos = document.getElementsByTagName("ENDERECO");

        for (int i = 0; i < enderecos.getLength(); i++) {
            Element endereco = (Element) enderecos.item(i);

            /*
             * ENDERECO
             */
            EntidadeXml tbEndereco = new EntidadeXml();
            tbEndereco.setNomeTabelaBanco("ENDERECO_USUARIO_CADSUS");
            tbEndereco.setAtributos(getAtributosEndereco(endereco));
            entidadesXML.add(tbEndereco);

            /*
             * BAIRRO
             */
            EntidadeXml tbBairro = new EntidadeXml();
            tbBairro.setNomeTabelaBanco("CIDADE_BAIRRO");
            tbBairro.setAtributos(getAtributosBairro(endereco));
            entidadesXML.add(tbBairro);

//            /**
//             * DOMICILIO
//             */
//            NodeList domicilios = endereco.getElementsByTagName("DOMICILIO");
//
//            for (int j = 0; j < domicilios.getLength(); j++) {
//                Element domicilio = (Element) domicilios.item(j);
//
//                EntidadeXml tbDomicilio = new EntidadeXml();
//                tbDomicilio.setNomeTabelaBanco("ENDERECO_DOMICILIO");
//                tbDomicilio.setAtributos(getAtributosDomicilio(domicilio));
//
//                entidadesXML.add(tbDomicilio);
//            }
//            /*****DOMICILIO******/
            /**
             * USUARIO
             */
            NodeList usuarios = endereco.getElementsByTagName("USUARIO");

            for (int k = 0; k < usuarios.getLength(); k++) {
                Element usuario = (Element) usuarios.item(k);

                /**
                 * TABELA USUARIO_CADSUS
                 */
                EntidadeXml tbUsuario = new EntidadeXml();
                tbUsuario.setNomeTabelaBanco("USUARIO_CADSUS");
                tbUsuario.setAtributos(getAtributosBaseUsuario(usuario));

                entidadesXML.add(tbUsuario);

//                /**
//                 * TABELA USUARIO_CADSUS_DOMICILIO
//                 */
//                EntidadeXml tbUsuarioDomicilio = new EntidadeXml();
//                tbUsuarioDomicilio.setNomeTabelaBanco("USUARIO_CADSUS_DOMICILIO");
//                tbUsuarioDomicilio.setAtributos(getAtributosUsuarioDomicilio(usuario));
//
//                entidadesXML.add(tbUsuarioDomicilio);
//---------------------------------------------------
                /*
                 * DOCUMENTO
                 */
                NodeList documentos = usuario.getElementsByTagName("DOCUMENTO");

                for (int z = 0; z < documentos.getLength(); z++) {
                    Element documento = (Element) documentos.item(z);

                    EntidadeXml tbDocumento = new EntidadeXml();
                    tbDocumento.setNomeTabelaBanco("USUARIO_CADSUS_DOCUMENTO");
                    tbDocumento.setAtributos(getAtributosBaseDocumento(documento));

                    entidadesXML.add(tbDocumento);
                }
                /****DOCUMENTO****/

                /*
                 * CNS
                 */
                NodeList cnsList = usuario.getElementsByTagName("CNS");

                for (int z = 0; z < cnsList.getLength(); z++) {
                    Element cns = (Element) cnsList.item(z);

                    EntidadeXml tbCns = new EntidadeXml();
                    tbCns.setNomeTabelaBanco("USUARIO_CADSUS_CNS");
                    tbCns.setAtributos(getAtributosBaseCns(cns));

                    entidadesXML.add(tbCns);
                }
                /****CNS****/

                /*
                 * RLENDERECO
                 */
                NodeList rlEnderecoList = usuario.getElementsByTagName("RLENDERECO");

                for (int z = 0; z < rlEnderecoList.getLength(); z++) {
                    Element rlEndereco = (Element) rlEnderecoList.item(z);

                    EntidadeXml tbRlEndereco = new EntidadeXml();
                    tbRlEndereco.setNomeTabelaBanco("USUARIO_CADSUS_ENDERECO");
                    tbRlEndereco.setAtributos(getAtributosBaseRlEndereco(rlEndereco));

                    entidadesXML.add(tbRlEndereco);
                }
                /****CNS****/
            }
            /*****USUARIO******/
        }

        return entidadesXML;
    }

    // <editor-fold defaultstate="collapsed" desc="Atributos ELENDERECO">
    private List<Atributo> getAtributosBaseRlEndereco(Element rlEndereco) {

        List<Atributo> atributoList = new ArrayList();

        AtributoXml atributoWhereUsuario = new AtributoXml(new MCCadSusDefault());
        atributoWhereUsuario.setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoWhereUsuario.setColunaBanco("CD_USUARIO_INTERNO");
        atributoWhereUsuario.setValor(rlEndereco.getAttribute("CO_USUARIO"));

        AtributoXml atributoCodigoUsuarioCadsus = new AtributoXml(new ValorAtributoExistente(dao, "USUARIO_CADSUS", "CD_USU_CADSUS", atributoWhereUsuario));
        atributoCodigoUsuarioCadsus.setColunaBanco("CD_USU_CADSUS");
        atributoCodigoUsuarioCadsus.setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        atributoCodigoUsuarioCadsus.setIdentificador(true);
        atributoList.add(atributoCodigoUsuarioCadsus);

        AtributoXml atributoWhereEndereco = new AtributoXml(new MCCadSusDefault());
        atributoWhereEndereco.setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoWhereEndereco.setColunaBanco("CD_ENDERECO_INTERNO");
        atributoWhereEndereco.setValor(rlEndereco.getAttribute("CO_ENDERECO"));

        AtributoXml atributoCodigoEndereco = new AtributoXml(new ValorAtributoExistente(dao, "ENDERECO_USUARIO_CADSUS", "CD_ENDERECO", atributoWhereEndereco));
        atributoCodigoEndereco.setColunaBanco("CD_ENDERECO");
        atributoCodigoEndereco.setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoCodigoEndereco.setIdentificador(true);
        atributoList.add(atributoCodigoEndereco);

        newAtributo();
        getAtributo().setColunaBanco("COD_TIP_END");
        getAtributo().setValor(rlEndereco.getAttribute("CO_TIPO_ENDERECO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("ST_VINCULO");
        getAtributo().setValor(rlEndereco.getAttribute("ST_VINCULO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("DT_ALTERACAO");
        getAtributo().setValor(rlEndereco.getAttribute("DT_OPERACAO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_TIMESTAMP);
        atributoList.add(getAtributo());

        return atributoList;
    }
    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="Atributos CNS">
    private List<Atributo> getAtributosBaseCns(Element cns) {

        List<Atributo> atributoList = new ArrayList();

        AtributoXml atributoWhere = new AtributoXml(new MCCadSusDefault());
        atributoWhere.setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoWhere.setColunaBanco("CD_USUARIO_INTERNO");
        atributoWhere.setValor(cns.getAttribute("CO_USUARIO"));

        AtributoXml atributoCodigoUsuarioCadsus = new AtributoXml(new ValorAtributoExistente(dao, "USUARIO_CADSUS", "CD_USU_CADSUS", atributoWhere));
        atributoCodigoUsuarioCadsus.setColunaBanco("CD_USU_CADSUS");
        atributoCodigoUsuarioCadsus.setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        atributoCodigoUsuarioCadsus.setIdentificador(true);
        atributoList.add(atributoCodigoUsuarioCadsus);

        newAtributo();
        getAtributo().setColunaBanco("CD_NUMERO_CARTAO");
        getAtributo().setValor(cns.getAttribute("CO_NUMERO_CARTAO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        getAtributo().setIdentificador(true);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("TP_CARTAO");
        getAtributo().setValor(cns.getAttribute("TP_CARTAO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("DT_ATRIBUICAO");
        getAtributo().setValor(cns.getAttribute("DT_ATRIBUICAO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_TIMESTAMP);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("ST_EXCLUIDO");
        getAtributo().setValor(cns.getAttribute("ST_EXCLUIDO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("DT_ALTERACAO");
        getAtributo().setValor(cns.getAttribute("DT_OPERACAO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_TIMESTAMP);
        atributoList.add(getAtributo());

        return atributoList;
    }
    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="Atributos Documento">
    private List<Atributo> getAtributosBaseDocumento(Element documento) {

        List<Atributo> atributoList = new ArrayList();

        AtributoXml whereUsuarioCadsus = new AtributoXml(new MCCadSusDefault());
        whereUsuarioCadsus.setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        whereUsuarioCadsus.setColunaBanco("CD_USUARIO_INTERNO");
        whereUsuarioCadsus.setValor(documento.getAttribute("CO_USUARIO"));
        
        AtributoXml whereUsuarioCadsusDocumento = new AtributoXml(new ValorAtributoExistente(dao, "USUARIO_CADSUS", "CD_USU_CADSUS", whereUsuarioCadsus));
        whereUsuarioCadsusDocumento.setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        whereUsuarioCadsusDocumento.setColunaBanco("CD_USU_CADSUS");

        AtributoXml whereTipoDocumento = new AtributoXml(new ValorAtributoTipoDocumentoUsuario(documento.getAttribute("CO_TIPO_DOCUMENTO")));
        whereTipoDocumento.setColunaBanco("CD_TIPO_DOCUMENTO");
        whereTipoDocumento.setTipoDado(MCCadSusDefault.TIPO_INT);
/***********************************************************************************************/
        AtributoXml atributoSequencia = new AtributoXml(new ValorAtributoSequence(dao, "seq_usuario_cadsus_documento", "SEQUENCIA", "USUARIO_CADSUS_DOCUMENTO", whereUsuarioCadsusDocumento, whereTipoDocumento));
        atributoSequencia.setColunaBanco("SEQUENCIA");
        atributoSequencia.setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoSequencia.setUpdate(false);
        atributoList.add(atributoSequencia);

        AtributoXml atributoCodigoUsuarioCadsus = new AtributoXml(new ValorAtributoExistente(dao, "USUARIO_CADSUS", "CD_USU_CADSUS", whereUsuarioCadsusDocumento));
        atributoCodigoUsuarioCadsus.setColunaBanco("CD_USU_CADSUS");
        atributoCodigoUsuarioCadsus.setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        atributoCodigoUsuarioCadsus.setIdentificador(true);
        atributoList.add(atributoCodigoUsuarioCadsus);

        AtributoXml atributoTipoDocumento = new AtributoXml(new ValorAtributoTipoDocumentoUsuario(documento.getAttribute("CO_TIPO_DOCUMENTO")));
        atributoTipoDocumento.setColunaBanco("CD_TIPO_DOCUMENTO");
        atributoTipoDocumento.setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        atributoTipoDocumento.setIdentificador(true);
        atributoTipoDocumento.setUpdate(false);
        atributoList.add(atributoTipoDocumento);

        newAtributo();
        getAtributo().setColunaBanco("CD_ORGAO_EMISSOR");
        getAtributo().setValor(documento.getAttribute("CO_ORGAO_EMISSOR"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_CARTORIO");
        getAtributo().setValor(documento.getAttribute("NO_CARTORIO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_LIVRO");
        getAtributo().setValor(documento.getAttribute("NU_LIVRO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_FOLHA");
        getAtributo().setValor(documento.getAttribute("NU_FOLHA").replaceAll("^0*", ""));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_TERMO");
        getAtributo().setValor(documento.getAttribute("NU_TERMO").replaceAll("^0*", ""));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("DT_EMISSAO");
        getAtributo().setValor(documento.getAttribute("DT_EMISSAO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_DATE);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("DT_CHEGADA_BRASIL");
        getAtributo().setValor(documento.getAttribute("DT_CHEGADA_BRASIL"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_DATE);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_PORTARIA");
        getAtributo().setValor(documento.getAttribute("NU_PORTARIA"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("DT_NATURALIZACAO");
        getAtributo().setValor(documento.getAttribute("DT_NATURALIZACAO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_DATE);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_DOCUMENTO");
        if ("02".equals(documento.getAttribute("CO_TIPO_DOCUMENTO"))) {
            getAtributo().setValor(new DecimalFormat("00000000000").format(new Double(documento.getAttribute("NU_DOCUMENTO"))));
        } else {
            getAtributo().setValor(documento.getAttribute("NU_DOCUMENTO").replaceAll("^0*", ""));
        }
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_DOCUMENTO_COMPL");
        getAtributo().setValor(documento.getAttribute("NU_DOCUMENTO_COMPL"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_SERIE");
        getAtributo().setValor(documento.getAttribute("NU_SERIE"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_ZONA_ELEITORAL");
        getAtributo().setValor(documento.getAttribute("NU_ZONA_ELEITORAL"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_SECAO_ELEITORAL");
        getAtributo().setValor(documento.getAttribute("NU_SECAO_ELEITORAL"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("SG_UF");
        getAtributo().setValor(documento.getAttribute("SG_UF"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("ST_EXCLUIDO");
        getAtributo().setValor(documento.getAttribute("ST_EXCLUIDO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("DT_ALTERACAO");
        getAtributo().setValor(documento.getAttribute("DT_OPERACAO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_TIMESTAMP);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        return atributoList;
    }
    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="Atributos Domicilio">
    private List<Atributo> getAtributosDomicilio(Element domicilio) {

        List<Atributo> atributoList = new ArrayList();

        AtributoXml atributoCodigoDomicilio = new AtributoXml(new ValorAtributoSequencial(dao, "ENDERECO_DOMICILIO", "CD_DOMICILIO"));
        atributoCodigoDomicilio.setColunaBanco("CD_DOMICILIO");
        atributoCodigoDomicilio.setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoCodigoDomicilio.setUpdate(false);
        atributoCodigoDomicilio.setValor(null);
        atributoList.add(atributoCodigoDomicilio);

        AtributoXml atributoWhere = new AtributoXml(new MCCadSusDefault());
        atributoWhere.setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoWhere.setColunaBanco("CD_ENDERECO_INTERNO");
        atributoWhere.setValor(domicilio.getAttribute("CO_ENDERECO"));

        AtributoXml atributoCodigoEndereco = new AtributoXml(new ValorAtributoExistente(dao, "ENDERECO_USUARIO_CADSUS", "CD_ENDERECO", atributoWhere));
        atributoCodigoEndereco.setColunaBanco("CD_ENDERECO");
        atributoCodigoEndereco.setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoList.add(atributoCodigoEndereco);

        newAtributo();
        getAtributo().setColunaBanco("CD_DOMICILIO_INTERNO");
//        getAtributo().setValor(domicilio.getAttribute("CO_DOMICILIO"));
        getAtributo().setValor(null);
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setIdentificador(true);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("CD_CADASTRADOR");
        getAtributo().setValor(domicilio.getAttribute("CO_CADASTRADOR"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_FICHA");
        getAtributo().setValor(domicilio.getAttribute("NU_FICHA"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_DOMICILIO");
        getAtributo().setValor(domicilio.getAttribute("NU_DOMICILIO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("DS_USO_MUNICIPAL");
        getAtributo().setValor(domicilio.getAttribute("DS_USO_MUNICIPAL"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("QT_PESSOAS");
        getAtributo().setValor(domicilio.getAttribute("QT_PESSOAS"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("CD_PROGRAMAS_COBERTURA");
        getAtributo().setValor(domicilio.getAttribute("CO_PROGRAMAS_COBERTURA"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("QT_COMODOS");
        getAtributo().setValor(domicilio.getAttribute("QT_COMODOS"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("CD_ESGOTO_SANITARIO");
        getAtributo().setValor(domicilio.getAttribute("CO_ESGOTO_SANITARIO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("CD_TIPO_DOMICILIO");
        getAtributo().setValor(domicilio.getAttribute("CO_TIPO_DOMICILIO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("CD_DESTINO_LIXO");
        getAtributo().setValor(domicilio.getAttribute("CO_DESTINO_LIXO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("CD_ABASTECIMENTO_AGUA");
        getAtributo().setValor(domicilio.getAttribute("CO_ABASTECIMENTO_AGUA"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("CD_TRATAMENTO_AGUA");
        getAtributo().setValor(domicilio.getAttribute("CO_TRATAMENTO_AGUA"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("ST_ENERGIA_ELETRICA");
        getAtributo().setValor(domicilio.getAttribute("ST_ENERGIA_ELETRICA"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("COD_CID");
        getAtributo().setValor(domicilio.getAttribute("CO_MUNICIPIO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("DT_PREENCHIMENTO");
        getAtributo().setValor(domicilio.getAttribute("DT_PREENCHIMENTO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_DATE);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_SEGMENTO");
        getAtributo().setValor(domicilio.getAttribute("NU_SEGMENTO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_AREA");
        getAtributo().setValor(domicilio.getAttribute("NU_AREA"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_MICRO_AREA");
        getAtributo().setValor(domicilio.getAttribute("NU_MICRO_AREA"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_FAMILIA");
        getAtributo().setValor(domicilio.getAttribute("NU_FAMILIA"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_LATITUDE");
        getAtributo().setValor(domicilio.getAttribute("NU_LATITUDE"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_LONGITUDE");
        getAtributo().setValor(domicilio.getAttribute("NU_LONGITUDE"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("ST_EXCLUIDO");
        getAtributo().setValor(domicilio.getAttribute("ST_EXCLUIDO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("DT_ALTERACAO");
        getAtributo().setValor(domicilio.getAttribute("DT_OPERACAO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_TIMESTAMP);
        atributoList.add(getAtributo());

        return atributoList;
    }
    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="Atributos Endereço">
    private List<Atributo> getAtributosEndereco(Element endereco) {

        List<Atributo> atributoList = new ArrayList();

        AtributoXml atributoWhereEndereco = new AtributoXml(new MCCadSusDefault());
        atributoWhereEndereco.setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoWhereEndereco.setColunaBanco("CD_ENDERECO_INTERNO");
        atributoWhereEndereco.setValor(endereco.getAttribute("CO_ENDERECO"));

//        AtributoXml atributoCodigoEndereco = new AtributoXml(new ValorAtributoSequence(dao, "endereco_usuario_cadsus_seq"));
        AtributoXml atributoCodigoEndereco = new AtributoXml(new ValorAtributoSequence(dao, "endereco_usuario_cadsus_seq", "CD_ENDERECO", "ENDERECO_USUARIO_CADSUS", atributoWhereEndereco));
        atributoCodigoEndereco.setColunaBanco("CD_ENDERECO");
        atributoCodigoEndereco.setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoCodigoEndereco.setUpdate(false);
        atributoList.add(atributoCodigoEndereco);

        newAtributo();
        getAtributo().setColunaBanco("CD_ENDERECO_INTERNO");
        getAtributo().setValor(endereco.getAttribute("CO_ENDERECO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setIdentificador(true);
//        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("CD_TIPO_LOGRADOURO");
        getAtributo().setValor(endereco.getAttribute("CO_TIPO_LOGRADOURO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NM_LOGRADOURO");
        getAtributo().setValor(endereco.getAttribute("NO_LOGRADOURO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_LOGRADOURO");
        getAtributo().setValor(endereco.getAttribute("NU_LOGRADOURO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NM_COMP_LOGRADOURO");
        getAtributo().setValor(endereco.getAttribute("NO_COMPL_LOGRADOURO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NM_BAIRRO");
        getAtributo().setValor(endereco.getAttribute("NO_BAIRRO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("CEP");
        getAtributo().setValor(endereco.getAttribute("CO_CEP"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_DDD");
        getAtributo().setValor(endereco.getAttribute("NU_DDD"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_TELEFONE");
        getAtributo().setValor(endereco.getAttribute("NU_TELEFONE"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_DDD_FAX");
        getAtributo().setValor(endereco.getAttribute("NU_DDD_FAX"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_FAX");
        getAtributo().setValor(endereco.getAttribute("NU_FAX"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("COD_CID");
        getAtributo().setValor(endereco.getAttribute("CO_MUNICIPIO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("DS_USO_MUNICIPAL");
        getAtributo().setValor(endereco.getAttribute("DS_USO_MUNICIPAL"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("ST_EXCLUIDO");
        getAtributo().setValor(endereco.getAttribute("ST_EXCLUIDO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("ST_ATIVO");
        getAtributo().setValor(endereco.getAttribute("ST_ATIVO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("DT_ALTERACAO");
        getAtributo().setValor(endereco.getAttribute("DT_OPERACAO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_TIMESTAMP);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        return atributoList;
    }
    // </editor-fold>
    
    // <editor-fold defaultstate="collapsed" desc="Atributos Bairro">
    private List<Atributo> getAtributosBairro(Element endereco) {

        List<Atributo> atributoList = new ArrayList();

        newAtributo();
        getAtributo().setColunaBanco("DS_BAIRRO");
        getAtributo().setValor(endereco.getAttribute("NO_BAIRRO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setIdentificador(true);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("COD_CID");
        getAtributo().setValor(endereco.getAttribute("CO_MUNICIPIO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        getAtributo().setIdentificador(true);
        atributoList.add(getAtributo());

        return atributoList;
    }
    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="Atributos UsuarioDomicilio">
    private List<Atributo> getAtributosUsuarioDomicilio(Element usuario) {

        List<Atributo> atributoList = new ArrayList();

        AtributoXml atributoWhere = new AtributoXml(new MCCadSusDefault());
        atributoWhere.setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoWhere.setColunaBanco("CD_DOMICILIO_INTERNO");
        atributoWhere.setValor(usuario.getAttribute("CO_DOMICILIO"));

        AtributoXml atributoCodigoDomicilio = new AtributoXml(new ValorAtributoExistente(dao, "ENDERECO_DOMICILIO", "CD_DOMICILIO", atributoWhere));
        atributoCodigoDomicilio.setColunaBanco("CD_DOMICILIO");
        atributoCodigoDomicilio.setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoCodigoDomicilio.setIdentificador(true);
        atributoList.add(atributoCodigoDomicilio);

        atributoWhere = new AtributoXml(new MCCadSusDefault());
        atributoWhere.setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoWhere.setColunaBanco("CD_USUARIO_INTERNO");
        atributoWhere.setValor(usuario.getAttribute("CO_USUARIO"));

        AtributoXml atributoCodigoUsuario = new AtributoXml(new ValorAtributoExistente(dao, "USUARIO_CADSUS", "CD_USU_CADSUS", atributoWhere));
        atributoCodigoUsuario.setColunaBanco("CD_USU_CADSUS");
        atributoCodigoUsuario.setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoCodigoUsuario.setIdentificador(true);
        atributoCodigoUsuario.setUpdate(false);
        atributoList.add(atributoCodigoUsuario);

        newAtributo();
        getAtributo().setColunaBanco("CD_USUARIO");
        getAtributo().setValor(SessaoAplicacaoImp.getInstance().getCodigoUsuario().toString());
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("DT_USUARIO");
        getAtributo().setValor(new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(new Date()));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_TIMESTAMP);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("STATUS");
        getAtributo().setValor("0");
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("DT_CADASTRO");
        getAtributo().setValor(usuario.getAttribute("DT_OPERACAO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_TIMESTAMP);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());


        return atributoList;


    }
    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="Atributos Usuario">
    private List<Atributo> getAtributosBaseUsuario(Element usuario) {

        List<Atributo> atributoList = new ArrayList();

        /**
         * CAMPOS CPF E RG DO USUARIO
         */
        NodeList documentos = usuario.getElementsByTagName("DOCUMENTO");

        String cpf = null;
        
        String rg = null;
        String uf = null;
        String emissaoRg = null;
        String expedicao = null;

        String tipo = null;
        String folha = null;
        String termo = null;
        String livro = null;
        String emissaoCertidao = null;

        for (int z = 0; z < documentos.getLength(); z++) {
            Element documento = (Element) documentos.item(z);

            if ("02".equals(documento.getAttribute("CO_TIPO_DOCUMENTO"))) {
                newAtributo();
                getAtributo().setColunaBanco("CPF");
                getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);

                cpf = documento.getAttribute("NU_DOCUMENTO");
                
                getAtributo().setValor(new DecimalFormat("00000000000").format(new Double(cpf)));
                getAtributo().setUpdate(false);
                atributoList.add(getAtributo());
            } else if ("10".equals(documento.getAttribute("CO_TIPO_DOCUMENTO"))) {
                rg = documento.getAttribute("NU_DOCUMENTO").replaceAll("^0*", "");
                uf = documento.getAttribute("SG_UF");
                emissaoRg = documento.getAttribute("DT_EMISSAO");
                expedicao = documento.getAttribute("CO_TIPO_DOCUMENTO");

                newAtributo();
                getAtributo().setColunaBanco("RG");
                getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
                getAtributo().setValor(rg);
                getAtributo().setUpdate(false);
                atributoList.add(getAtributo());

            } else if ("91".equals(documento.getAttribute("CO_TIPO_DOCUMENTO")) 
//                    || "92".equals(documento.getAttribute("CO_TIPO_DOCUMENTO"))
                    ) {
                tipo = documento.getAttribute("CO_TIPO_DOCUMENTO");
                folha = documento.getAttribute("NU_FOLHA").replaceAll("^0*", "");
                termo = documento.getAttribute("NU_TERMO").replaceAll("^0*", "");
                livro = documento.getAttribute("NU_LIVRO");
                emissaoCertidao = documento.getAttribute("DT_EMISSAO");
            }
        }
        /*****CAMPOS CPF E RG DO USUARIO******/

        AtributoXml atributoCodigoCadSus = new AtributoXml(new ValorAtributoCodigoUsuarioCadsus(dao, usuario.getAttribute("CO_USUARIO"), usuario.getElementsByTagName("CNS"), cpf, rg, uf, emissaoRg, expedicao, tipo, folha, termo, livro, emissaoCertidao));
//        AtributoXml atributoCodigoCadSus = new AtributoXml(new ValorAtributoSequencial(dao, "USUARIO_CADSUS", "CD_USU_CADSUS"));
        atributoCodigoCadSus.setColunaBanco("CD_USU_CADSUS");
        atributoCodigoCadSus.setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        atributoCodigoCadSus.setUpdate(false);
        atributoCodigoCadSus.setIdentificador(true);
        atributoList.add(atributoCodigoCadSus);

//        AtributoXml atributoWhere = new AtributoXml(new MCCadSusDefault());
//        atributoWhere.setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
//        atributoWhere.setColunaBanco("CD_DOMICILIO_INTERNO");
//        atributoWhere.setUpdate(false);
//        atributoWhere.setValor(usuario.getAttribute("CO_DOMICILIO"));
//
//        AtributoXml atributoCodigoDomicilio = new AtributoXml(new ValorAtributoExistente(dao, "ENDERECO_DOMICILIO", "CD_DOMICILIO", atributoWhere));
//        atributoCodigoDomicilio.setColunaBanco("CD_DOMICILIO");
//        atributoCodigoDomicilio.setTipoDado(MCCadSusDefault.TIPO_INT);
//        atributoCodigoDomicilio.setUpdate(false);
//        atributoList.add(atributoCodigoDomicilio);

        newAtributo();
        getAtributo().setColunaBanco("CD_USUARIO_INTERNO");
        getAtributo().setValor(usuario.getAttribute("CO_USUARIO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
//        getAtributo().setIdentificador(true);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NM_USUARIO");
        getAtributo().setValor(usuario.getAttribute("NO_USUARIO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("DT_NASCIMENTO");
        getAtributo().setValor(usuario.getAttribute("DT_NASCIMENTO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_DATE);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("COD_CID_NASCIMENTO");
        getAtributo().setValor(usuario.getAttribute("CO_MUNICIPIO_NASC"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NM_MAE");
        getAtributo().setValor(usuario.getAttribute("NO_MAE"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NM_PAI");
        getAtributo().setValor(usuario.getAttribute("NO_PAI"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("ST_PROFISSIONAL");
        getAtributo().setValor(usuario.getAttribute("ST_PROFISSIONAL"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("ST_FREQUENTA_ESCOLA");
        getAtributo().setValor(usuario.getAttribute("ST_FREQUENTA_ESCOLA"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("EMAIL");
        getAtributo().setValor(usuario.getAttribute("DS_EMAIL"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("CD_RACA");
        getAtributo().setValor(usuario.getAttribute("CO_RACA"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("CD_ESTADO_CIVIL");
        getAtributo().setValor(usuario.getAttribute("CO_ESTADO_CIVIL"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("CD_SITUACAO_FAMILIAR");
        getAtributo().setValor(usuario.getAttribute("CO_SITUACAO_FAMILIAR"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("CD_PAIS_NASCIMENTO");
        getAtributo().setValor(usuario.getAttribute("CO_PAIS"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("CD_CBO");
        String valor = usuario.getAttribute("CO_SGRP_CBO");
        if(valor != null && !valor.trim().equals("XXX")){
            getAtributo().setValor(valor);
        }
        getAtributo().setUpdate(false);
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("SG_SEXO");
        getAtributo().setValor(usuario.getAttribute("CO_SEXO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

//        newAtributo();
//        getAtributo().setColunaBanco("NR_DDD");
//        getAtributo().setValor(usuario.getAttribute("NU_DDD"));
//        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
//        getAtributo().setUpdate(false);
//        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_TELEFONE");
        getAtributo().setValor(usuario.getAttribute("NU_DDD")+usuario.getAttribute("NU_TELEFONE"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

//        newAtributo();
//        getAtributo().setColunaBanco("NR_DDD_2");
//        getAtributo().setValor(usuario.getAttribute("NU_DDD_2"));
//        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
//        getAtributo().setUpdate(false);
//        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_TELEFONE_2");
        getAtributo().setValor(usuario.getAttribute("NU_DDD_2")+usuario.getAttribute("NU_TELEFONE_2"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("DT_PREENCHIMENTO_FORM");
        getAtributo().setValor(usuario.getAttribute("DT_PREENCHIMENTO_FORM"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_DATE);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("CD_MUNICIPIO_RESIDENCIA");
        getAtributo().setValor(usuario.getAttribute("CO_MUNICIPIO_RESIDENCIA"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("ST_SEM_DOCUMENTO");
        getAtributo().setValor(usuario.getAttribute("ST_SEM_DOCUMENTO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NR_USUARIO_NO_DOMICILIO");
        getAtributo().setValor(usuario.getAttribute("NU_USUARIO_NO_DOMICILIO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("ST_EXCLUIDO");
        getAtributo().setValor(usuario.getAttribute("ST_EXCLUIDO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("CD_DOMICILIO_INTERNO");
//        getAtributo().setValor(usuario.getAttribute("CO_DOMICILIO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("CD_ESCOLARIDADE");
        getAtributo().setValor(usuario.getAttribute("CO_ESCOLARIDADE"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("SITUACAO");
        getAtributo().setValor(UsuarioCadsus.SITUACAO_INATIVO.toString());
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("ST_VIVO");
        getAtributo().setValor(usuario.getAttribute("ST_VIVO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("DT_ALTERACAO");
        getAtributo().setValor(usuario.getAttribute("DT_OPERACAO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_TIMESTAMP);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("DT_INCLUSAO");
        getAtributo().setValor(usuario.getAttribute("DT_INCLUSAO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_TIMESTAMP);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("DT_CADASTRO");
        getAtributo().setValor(new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(dataImportacao));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_TIMESTAMP);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());
        
        newAtributo();
        getAtributo().setColunaBanco("dt_usuario");
        getAtributo().setValor(new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(dataImportacao));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_TIMESTAMP);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());
        
        newAtributo();
        getAtributo().setColunaBanco("cd_usuario");
        getAtributo().setValor(SessaoAplicacaoImp.getInstance().<Usuario>getUsuario().getCodigo().toString());
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_INT);
        getAtributo().setUpdate(false);
        atributoList.add(getAtributo());

        return atributoList;
    }
    // </editor-fold>
    private AtributoXml atributo;

    private AtributoXml getAtributo() {
        return atributo;
    }

    private void newAtributo() {
        atributo = new AtributoXml(new MCCadSusDefault());
    }

    public void start() {
        for (String path : mapeamentos) {
            entidades.addAll(getMapeamento(path));
        }
    }

    public List<Entidade> getRegistros() {
        return entidades;
    }
}
