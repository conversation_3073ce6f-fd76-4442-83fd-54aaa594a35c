/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.integracao.exportacao;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.XmlCadsus;
import org.hibernate.criterion.Projections;

/**
 *
 * <AUTHOR>
 */
public class SaveXmlCadsus extends AbstractCommandTransaction{

    private XmlCadsus xmlCadsus;

    public SaveXmlCadsus(XmlCadsus xmlCadsus) {
        this.xmlCadsus = xmlCadsus;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        xmlCadsus.setCodigo(getNewCodigo());
        xmlCadsus.setDataCricao(Data.getDataAtual());

        getSession().saveOrUpdate(xmlCadsus);
    }

    private Long getNewCodigo() {
        Object codigo = getSession().createCriteria(XmlCadsus.class)
                .setProjection(Projections.projectionList().add(Projections.max("codigo")))
                .uniqueResult();

        return Coalesce.asLong(codigo,0L) + 1;
    }

}
