/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.integracao.dao.util;

import br.com.ksisolucoes.integracao.dao.DAOException;
import br.com.ksisolucoes.integracao.vos.interfaces.Entidade.Atributo.ValorAtributo;

/**
 * Implementacao Padrao da interface ValorAtributo.
 * <AUTHOR>
 */
public class ValorAtributoImpl implements ValorAtributo{

    private Object object;

    public ValorAtributoImpl(Object object) {
        this.object = object;
    }

    public Object getValor() throws DAOException {
        return object;
    }

    @Override
    public ValorAtributo newInstance() {
        return new ValorAtributoImpl(object);
    }

}
