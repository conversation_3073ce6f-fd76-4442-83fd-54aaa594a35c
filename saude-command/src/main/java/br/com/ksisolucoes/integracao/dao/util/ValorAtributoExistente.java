/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.integracao.dao.util;

import br.com.ksisolucoes.integracao.vos.interfaces.Entidade.Atributo;
import br.com.ksisolucoes.integracao.dao.DAOException;
import br.com.ksisolucoes.integracao.dao.interfaces.SearchDAO;
import br.com.ksisolucoes.integracao.vos.interfaces.Entidade;
import br.com.ksisolucoes.integracao.vos.interfaces.Entidade.Atributo.ValorAtributo;

/**
 * Implementacao da interface ValorAtributo que abstrai a recuperacao do valor apartir de um dao predefinido.
 * <AUTHOR>
 */
public class ValorAtributoExistente implements Entidade.Atributo.ValorAtributo{

    private SearchDAO dao;
    private String nomeEntidade;
    private String nomeAtributo;
    private Entidade.Atributo[] atributosWhere;

    public ValorAtributoExistente(SearchDAO dao, String nomeEntidade, String nomeAtributo, Atributo... atributosWhere) {
        this.dao = dao;
        this.nomeEntidade = nomeEntidade;
        this.nomeAtributo = nomeAtributo;
        this.atributosWhere = atributosWhere;
    }

    public Object getValor() throws DAOException {
        return dao.find(nomeAtributo, nomeEntidade, atributosWhere);
    }

    @Override
    public ValorAtributo newInstance() {
        return new ValorAtributoExistente(dao, nomeEntidade, nomeAtributo, atributosWhere);
    }
    
}
