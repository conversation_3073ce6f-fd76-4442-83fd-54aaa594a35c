/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.integracao.conversao.datasus.tabelascorporativas;

import br.com.ksisolucoes.integracao.dao.util.vo.ModoConversao;

/**
 * Implementacao da Interface ModoConvercao para recuperar o valor do CodigoSubGrupoCbo
 * <AUTHOR>
 */
public class MCAgrupamentoCodigoSubGrupoCbo implements ModoConversao<String>{

    public Object getValorConvertido(String tipo, String value) throws IllegalArgumentException {
        if (value != null && !value.equals("")) {
            String newValue = "";
            if (value.length() > 2) {
                newValue = value.substring(2);
            } else {
                newValue = value.substring(1);
            }

            return new MCTabelasCorporativasDefault().getValorConvertido(tipo, newValue);
        }

        return null;

    }

}
