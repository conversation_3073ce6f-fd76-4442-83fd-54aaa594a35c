package br.com.ksisolucoes.integracao.bos.datasus.fpo;

import br.com.celk.fpo.build.ImportacaoFpoDTOBuild;
import br.com.celk.fpo.dto.ImportacaoFpoDTO;
import br.com.celk.util.Util;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.io.FileUtils;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.controle.ArquivoFpo;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.logging.Logger;

public class ImportarFpo extends AbstractCommandTransaction<ImportarFpo> {


    protected static final long QTD_CARACTERES_POR_LINHA = 97L;
    private static final Logger LOG = Logger.getLogger(ImportarFpo.class.getName());

    private final GerenciadorArquivo ga;
    private final ArrayList<ImportacaoFpoDTO> importacaoFpoDTOArrayList;
    private ArquivoFpo arquivoFpo;

    public ImportarFpo(GerenciadorArquivo ga, ArquivoFpo arquivoFpo) {
        this.arquivoFpo = arquivoFpo;
        this.importacaoFpoDTOArrayList = new ArrayList<>();
        this.ga = ga;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        try {
            File f = File.createTempFile("anexo", "fpo");
            FileUtils.buscarArquivoFtp(ga.getCaminho(), f.getAbsolutePath());
            InputStream inputStream = Files.newInputStream(f.toPath());
            BufferedReader br = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.ISO_8859_1));

            String line;
            int numLine = 0;

            while ((line = br.readLine()) != null) {
                numLine++;
                validarLayoutArquivo(line, numLine);
                String cnes = line.substring(6, 13);
                String sigtap = line.substring(13, 23);
                String[] valores = new String[]{cnes, sigtap};
                this.addImportacaoFpo(new ImportacaoFpoDTOBuild().build(valores));
            }

            arquivoFpo = (ArquivoFpo) this.getSession().get(ArquivoFpo.class, arquivoFpo.getCodigo());
            BOFactory.getBO(BasicoFacade.class).cadastrarImportacaoFpo(importacaoFpoDTOArrayList, arquivoFpo);

        } catch (IOException ex) {
            throw new RuntimeException(ex);
        } catch (Exception e) {
            arquivoFpo = (ArquivoFpo) this.getSession().get(ArquivoFpo.class, arquivoFpo.getCodigo());
            arquivoFpo.setSituacao(ArquivoFpo.Situacao.ERRO.value());
            arquivoFpo.addLog(e.getMessage());
            BOFactory.save(arquivoFpo);
        }
    }

    private void validarLayoutArquivo(String primeiraLinha, int numLine) throws ValidacaoException, DAOException {
        if ((long) primeiraLinha.length() != QTD_CARACTERES_POR_LINHA) {
            throw new ValidacaoException("FPO: O arquivo possui quantidade de caracteres por linha incorreta! Linha " + numLine);
        }
    }


    private void addImportacaoFpo(ImportacaoFpoDTO importacaoFpoDTO) {
        if (Util.isNotNull(importacaoFpoDTO))
            importacaoFpoDTOArrayList.add(importacaoFpoDTO);
    }

}
