package br.com.ksisolucoes.integracao.bos.datasus.bolsafamilia;

import br.com.celk.bolsafamilia.dto.ConsultaImportacaoArquivoBolsaFamiliaDTO;
import br.com.celk.bolsafamilia.dto.ConsultaImportacaoArquivoBolsaFamiliaDTOParam;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

import java.util.List;
import java.util.Map;

public class QueryConsultaIntegracaoBolsaFamilia extends CommandQueryPager<QueryConsultaIntegracaoBolsaFamilia> {


    private final ConsultaImportacaoArquivoBolsaFamiliaDTOParam param;

    public QueryConsultaIntegracaoBolsaFamilia(ConsultaImportacaoArquivoBolsaFamiliaDTOParam param) {
        this.param = param;
    }


    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(ConsultaImportacaoArquivoBolsaFamiliaDTO.class.getName());

        hql.addToSelect("bf.codigo", "codigoProcesso");
        hql.addToSelect("bf.nomeArquivo", "nomeArquivo");
        hql.addToSelect("bf.ano", "ano");
        hql.addToSelect("bf.semestre", "semestre");
        hql.addToSelect("bf.dataEnvio", "dataEnvio");
        hql.addToSelect("bf.situacao", "situacao");
        hql.addToSelect("bf.quantidadeRegistros", "quantidadeRegistros");
        hql.addToSelect("bf.quantidadeRegistrosAtualizados", "quantidadeRegistrosAtualizados");
        hql.addToSelect("asyncProcess.codigo", "asyncProcess.codigo");
        hql.addToSelect("asyncProcess.status", "asyncProcess.status");
        hql.addToSelect("asyncProcess.dataRegistro", "asyncProcess.dataRegistro");

        hql.addToFrom("ArquivoBolsaFamilia bf "
                + " left join bf.asyncProcess asyncProcess ");

        if (param.getSituacao() != null) {
            hql.addToWhereWhithAnd("asyncProcess.status = ", param.getSituacao());
        }
        if (param.getAno() != null) {
            hql.addToWhereWhithAnd("bf.ano = ", param.getAno());
        }
        if (param.getSemestre() != null) {
            hql.addToWhereWhithAnd("bf.semestre = ", param.getSemestre());
        }
        if (param.getPropSort() != null) {
            hql.addToOrder(param.getPropSort() + " " + (param.isAscending() ? "asc" : "desc"));
        } else {
            hql.addToOrder("bf.dataEnvio desc, asyncProcess.dataRegistro desc");
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
