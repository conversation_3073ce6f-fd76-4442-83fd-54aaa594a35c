package br.com.ksisolucoes.integracao.bos.datasus.bolsafamilia;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.controle.ArquivoBolsaFamilia;
import br.com.ksisolucoes.vo.service.AsyncProcess;

import java.io.File;

public class EnviarImportacaoBolsaFamiliaFila extends AbstractCommandTransaction<EnviarImportacaoBolsaFamiliaFila> {

    private final String nomeArquivo;
    private final File file;
    private final Long ano;
    private final String semestre;


    public EnviarImportacaoBolsaFamiliaFila(String nomeArquivo, File file, Long ano, String semestre) {
        this.nomeArquivo = nomeArquivo;
        this.file = file;
        this.ano = ano;
        this.semestre = semestre;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        ArquivoBolsaFamilia processo = new ArquivoBolsaFamilia();

        processo.setNomeArquivo(nomeArquivo);
        processo.setDataEnvio(DataUtil.getDataAtual());
        processo.setUsuario(getSessao().getUsuario());
        processo.setAno(String.valueOf(ano));
        processo.setSemestre(semestre);

        processo = BOFactory.newTransactionSave(processo);
        processo = HibernateUtil.lockTable(ArquivoBolsaFamilia.class, processo.getCodigo());
        getSession().save(processo);

        GerenciadorArquivo ga = BOFactory.getBO(ComunicacaoFacade.class).enviarArquivoFtp(file, GerenciadorArquivo.OrigemArquivo.QWARE.value(), nomeArquivo);

        AsyncProcess asyncProcess;
        asyncProcess = BOFactory.getBO(BasicoFacade.class).importarArquivoBolsaFamilia(ga, processo);
        asyncProcess.setNomeProcesso("Importação arquivo Bolsa Família");
        processo.setAsyncProcess(asyncProcess);
        getSession().saveOrUpdate(processo);
    }
}
