/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.integracao.conversao.datasus.cadsus;

import br.com.ksisolucoes.integracao.dao.util.vo.ModoConversao;
import br.com.ksisolucoes.integracao.conversao.datasus.tabelascorporativas.MCTabelasCorporativasDefault;
import br.com.ksisolucoes.util.log.Loggable;

/**
 * Implementacao da Interface ModoConvercao para recuperar um valor de uma determinada posicao de uma String e converter utilizando o conversor MCTabelasCorporativasDefault.
 * <AUTHOR>
 */
public class MCCadSusAgrupamento implements ModoConversao {

    private int indexInicial;
    private int indexFinal;

    public MCCadSusAgrupamento(int indexInicial, int indexFinal) {
        this.indexInicial = indexInicial;
        this.indexFinal = indexFinal;
    }

    public Object getValorConvertido(String tipo, Object value) throws IllegalArgumentException {
        if (value != null && !value.equals("")) {
            String newValue = value.toString().substring(this.indexInicial, this.indexFinal);
            return new MCTabelasCorporativasDefault().getValorConvertido(tipo, newValue);
        }

        return null;

    }

    public static void main(String[] args) {
        Loggable.log.debug("klaus".substring(0, 5));
    }
}
