/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.integracao.bos.datasus.cnes;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.integracao.cadsus.ImportacaoXmlCadsus;
import java.util.ArrayList;
import java.util.List;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaImportacaoCadsus extends CommandQuery {

    private List<ImportacaoXmlCadsus> list;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("xml.codigo",true);
        hql.addToSelect("xml.dataImportacao",true);
        hql.addToSelect("xml.totalUsuarios",true);
        hql.addToSelect("xml.usuariosImportados",true);
        hql.addToSelect("xml.usuario.codigo",true);
        hql.addToSelect("xml.usuario.nome",true);

        hql.setTypeSelect(ImportacaoXmlCadsus.class.getName());

        hql.addToFrom(ImportacaoXmlCadsus.class.getName()+" xml");

        hql.addToOrder("xml.dataImportacao desc");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
         list = hql.getBeanList((List) result);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        ArrayList<ImportacaoXmlCadsus> retorno = new ArrayList<ImportacaoXmlCadsus>();
        if (CollectionUtils.isNotNullEmpty(list)) {
            if (list.size() > 10) {
                for (int i = 0; i < 10; i++) {
                    ImportacaoXmlCadsus importacaoXmlCadsus = list.get(i);

                    retorno.add(importacaoXmlCadsus);
                }
            } else {
                retorno.addAll(list);
            }
        }

        list = retorno;
    }

    public List<ImportacaoXmlCadsus> getList() {
        return list;
    }
    
}
