package br.com.ksisolucoes.integracao.bos.datasus.fpo;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.controle.ArquivoFpo;
import br.com.ksisolucoes.vo.service.AsyncProcess;

import java.io.File;
import java.util.Date;

public class EnviarImportacaoFpoFila extends AbstractCommandTransaction<EnviarImportacaoFpoFila> {

    private final String nomeArquivo;
    private final File file;
    private final Date competencia;


    public EnviarImportacaoFpoFila(String nomeArquivo, File file, Date competencia) {
        this.nomeArquivo = nomeArquivo;
        this.file = file;
        this.competencia = competencia;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        ArquivoFpo processo = new ArquivoFpo();
        try {
            processo.setNomeArquivo(nomeArquivo);
            processo.setDataGeracao(DataUtil.getDataAtual());
            processo.setUsuario(getSessao().getUsuario());
            processo.setCompetencia(competencia);
            processo.setSituacao(ArquivoFpo.Situacao.GERANDO.value());

            processo = BOFactory.newTransactionSave(processo);
            processo = HibernateUtil.lockTable(ArquivoFpo.class, processo.getCodigo());

            GerenciadorArquivo ga = BOFactory.getBO(ComunicacaoFacade.class).enviarArquivoFtp(file, GerenciadorArquivo.OrigemArquivo.FPO.value(), nomeArquivo);

            AsyncProcess asyncProcess = BOFactory.getBO(BasicoFacade.class).importarArquivoFpo(ga, processo);
            asyncProcess.setNomeProcesso("Importação arquivo FPO");

            processo.setGerenciadorArquivo(ga);
            processo.setAsyncProcess(asyncProcess);
            BOFactory.save(processo);

        } catch (Exception e) {
            processo.setSituacao(ArquivoFpo.Situacao.ERRO.value());
            processo.addLog("Erro ao importar arquivo FPO. " + e.getMessage());
            BOFactory.save(processo);
        }

    }
}
