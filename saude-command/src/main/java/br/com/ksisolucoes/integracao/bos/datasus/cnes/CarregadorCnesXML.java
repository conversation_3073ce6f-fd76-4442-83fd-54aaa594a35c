/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.integracao.bos.datasus.cnes;

import br.com.ksisolucoes.integracao.bos.interfaces.InterfaceCommand;
import br.com.ksisolucoes.integracao.conversao.datasus.cadsus.MCCadSusDefault;
import br.com.ksisolucoes.integracao.vos.interfaces.Entidade;
import br.com.ksisolucoes.integracao.vos.interfaces.Entidade.Atributo;
import br.com.ksisolucoes.integracao.dao.util.ValorAtributoExistente;
import br.com.ksisolucoes.integracao.dao.util.ValorAtributoSequencial;
import br.com.ksisolucoes.integracao.dao.interfaces.DAO;
import br.com.ksisolucoes.integracao.util.DOMLibrary;
import br.com.ksisolucoes.integracao.vos.AtributoXml;
import br.com.ksisolucoes.integracao.vos.EntidadeXml;
import br.com.ksisolucoes.util.log.Loggable;
import java.util.ArrayList;
import java.util.List;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

/**
 *
 * <AUTHOR>
 */
public class CarregadorCnesXML implements InterfaceCommand {

    private List<Entidade> entidades = new ArrayList();
    private List<String> mapeamentos;
    private DAO dao;

    public CarregadorCnesXML(DAO dao) {
        this.dao = dao;
    }

    public void setMapeamentos(List<String> mapeamentos) {
        this.mapeamentos = mapeamentos;
    }

    public List<EntidadeXml> getMapeamento(String xml) {
        DOMLibrary dOMLibrary = new DOMLibrary();
        Document document = dOMLibrary.getDOM(xml, false);
        if (document == null) {
            Loggable.log.error("ERRO: " + dOMLibrary.getLastErr());
            return null;
        }
        List<EntidadeXml> entidadesXML = new ArrayList();

        NodeList estabelecimentos = document.getElementsByTagName("DADOS_GERAIS_ESTABELECIMENTOS");

        for (int i = 0; i < estabelecimentos.getLength(); i++) {
            /*
             * EMPRESA
             */
            Element unidade = (Element) estabelecimentos.item(i);

            EntidadeXml tbUnidade = new EntidadeXml();
            tbUnidade.setNomeTabelaBanco("EMPRESA");
            tbUnidade.setAtributos(getAtributosUnidade(unidade));

            entidadesXML.add(tbUnidade);
            /*
             * EMPRESA_MATERIAL
             */
            EntidadeXml tbEmpresaMaterial = new EntidadeXml();
            tbEmpresaMaterial.setNomeTabelaBanco("EMPRESA_MATERIAL");

            tbEmpresaMaterial.setAtributos(getAtributosEmpresaMaterial(unidade));

            entidadesXML.add(tbEmpresaMaterial);

            /**
             * PROFISSIONAL
             */
            NodeList profissionais = unidade.getElementsByTagName("DADOS_PROFISSIONAIS");

            for (int j = 0; j < profissionais.getLength(); j++) {
                Element profissional = (Element) profissionais.item(j);

                EntidadeXml tbProfissional = new EntidadeXml();
                tbProfissional.setNomeTabelaBanco("PROFISSIONAL");
                tbProfissional.setAtributos(getAtributosProfissional(profissional));

                entidadesXML.add(tbProfissional);
                /**
                 * PROFISSIONAL CARGA HORARIA
                 */
                NodeList cargaHorariaList = unidade.getElementsByTagName("DADOS_VINC_PROF");

                for (int k = 0; k < cargaHorariaList.getLength(); k++) {
                    Element cargaHoraria = (Element) cargaHorariaList.item(j);

                    EntidadeXml tbCargaHoraria = new EntidadeXml();
                    tbCargaHoraria.setNomeTabelaBanco("profissional_carga_horaria");
                    tbCargaHoraria.setAtributos(getAtributosCargaHoraria(cargaHoraria));

                    entidadesXML.add(tbCargaHoraria);

                }
            }
            //-------------------FIM PROFISSIONAL
        }
        //-----------------FIM EMPRESA
        return entidadesXML;
    }

    // <editor-fold defaultstate="collapsed" desc="Profissional Carga Horaria">
    private List<Atributo> getAtributosCargaHoraria(Element cargaHoraria) {
        return null;
    }
    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="Profissional">
    private List<Atributo> getAtributosProfissional(Element profissional) {

        List<Atributo> atributoList = new ArrayList();

        AtributoXml atributoCodigoProfissional = new AtributoXml(new ValorAtributoSequencial(dao, "PROFISSIONAL", "CD_PROFISSIONAL"));
        atributoCodigoProfissional.setColunaBanco("CD_PROFISSIONAL");
        atributoCodigoProfissional.setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        atributoCodigoProfissional.setUpdate(false);
        atributoList.add(atributoCodigoProfissional);

        AtributoXml atributoWhere = new AtributoXml(new MCCadSusDefault());
        atributoWhere.setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoWhere.setColunaBanco("PROFISSIONAL_ID_CNES");
        atributoWhere.setValor(profissional.getAttribute("PROF_ID"));

        AtributoXml atributoCodigoProf = new AtributoXml(new ValorAtributoExistente(dao, "PROFISSIONAL", "CD_PROFISSIONAL", atributoWhere));
        atributoCodigoProf.setColunaBanco("CD_PROFISSIONAL");
        atributoCodigoProf.setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoCodigoProf.setInsert(false);
        atributoCodigoProf.setUpdate(false);
        atributoList.add(atributoCodigoProf);

        newAtributo();
        getAtributo().setColunaBanco("PROFISSIONAL_ID_CNES");
        getAtributo().setValor(profissional.getAttribute("PROF_ID"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setIdentificador(true);
        atributoList.add(getAtributo());


        newAtributo();
        getAtributo().setColunaBanco("CPF");
        getAtributo().setValor(profissional.getAttribute("CPF_PROF"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("nm_profissional");
        getAtributo().setValor(profissional.getAttribute("NOME_PROF"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("nm_mae");
        getAtributo().setValor(profissional.getAttribute("NOME_MAE"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("dt_nascimento");
        getAtributo().setValor(profissional.getAttribute("DATA_NASC"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_DATE);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("cod_cid_nasc");
        getAtributo().setValor(profissional.getAttribute("COD_MUN"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("SEXO");
        getAtributo().setValor(profissional.getAttribute("SEXO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("cd_orgao_emissor");
        getAtributo().setValor(profissional.getAttribute("CODORGEMIS"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("nr_rg");
        getAtributo().setValor(profissional.getAttribute("NUM_IDENT"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("uf_rg");
        getAtributo().setValor(profissional.getAttribute("SIGLA_EST"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("dt_emissao_rg");
        getAtributo().setValor(profissional.getAttribute("DTEMIIDENT"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_DATE);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("RUA");
        getAtributo().setValor(profissional.getAttribute("LOGRADOURO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("NUMERO_RUA");
        getAtributo().setValor(profissional.getAttribute("NUMERO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("COMPLEMENTO");
        getAtributo().setValor(profissional.getAttribute("COMPLEMENT"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("CEP");
        getAtributo().setValor(profissional.getAttribute("COD_CEP"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("cd_cns");
        getAtributo().setValor(profissional.getAttribute("COD_CNS"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("TELEFONE");
        getAtributo().setValor(profissional.getAttribute("TELEFONE"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("nm_pai");
        getAtributo().setValor(profissional.getAttribute("NOME_PAI"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("TP_SUS_NAO_SUS");
        getAtributo().setValor(profissional.getAttribute("TP_SUS_NAO_SUS"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        return atributoList;
    }
    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="Empresa Material">
    private List<Atributo> getAtributosEmpresaMaterial(Element unidade) {
        List<Atributo> atributoList = new ArrayList();

        AtributoXml atributoWhereUnidade = new AtributoXml(new MCCadSusDefault());
        atributoWhereUnidade.setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoWhereUnidade.setColunaBanco("UNIDADE_ID_CNES");
        atributoWhereUnidade.setValor(unidade.getAttribute("UNIDADE_ID"));

        AtributoXml atributoCodigoUnidade = new AtributoXml(new ValorAtributoExistente(dao, "EMPRESA", "EMPRESA", atributoWhereUnidade));
        atributoCodigoUnidade.setColunaBanco("EMPRESA");
        atributoCodigoUnidade.setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoCodigoUnidade.setIdentificador(true);
        atributoList.add(atributoCodigoUnidade);

        return atributoList;
    }
    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="Empresa (Unidade)">
    private List<Atributo> getAtributosUnidade(Element unidade) {

        List<Atributo> atributoList = new ArrayList();

        AtributoXml atributoCodigoCadUnidade = new AtributoXml(new ValorAtributoSequencial(dao, "EMPRESA", "EMPRESA"));
        atributoCodigoCadUnidade.setColunaBanco("EMPRESA");
        atributoCodigoCadUnidade.setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        atributoCodigoCadUnidade.setUpdate(false);
        atributoList.add(atributoCodigoCadUnidade);

        AtributoXml atributoWhere = new AtributoXml(new MCCadSusDefault());
        atributoWhere.setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoWhere.setColunaBanco("UNIDADE_ID_CNES");
        atributoWhere.setValor(unidade.getAttribute("UNIDADE_ID"));

        AtributoXml atributoUnidade = new AtributoXml(new ValorAtributoExistente(dao, "EMPRESA", "EMPRESA", atributoWhere));
        atributoUnidade.setColunaBanco("EMPRESA");
        atributoUnidade.setTipoDado(MCCadSusDefault.TIPO_INT);
        atributoUnidade.setInsert(false);
        atributoUnidade.setUpdate(false);
        atributoList.add(atributoUnidade);

        newAtributo();
        getAtributo().setColunaBanco("UNIDADE_ID_CNES");
        getAtributo().setValor(unidade.getAttribute("UNIDADE_ID"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        getAtributo().setIdentificador(true);
        atributoList.add(getAtributo());

        
        newAtributo();
        getAtributo().setColunaBanco("CNES");
        getAtributo().setValor(unidade.getAttribute("CNES"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("CNPJ_MANTENEDORA");
        getAtributo().setValor(unidade.getAttribute("CNPJ_MANT"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("FIS_JUR");
        getAtributo().setValor(unidade.getAttribute("PFPJ_IND"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("CD_SIASUS");
        getAtributo().setValor(unidade.getAttribute("COD_SIASUS"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("FANTASIA");
        getAtributo().setValor(unidade.getAttribute("R_SOCIAL"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("DESCRICAO");
        getAtributo().setValor(unidade.getAttribute("NOME_FANTA"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("RUA");
        getAtributo().setValor(unidade.getAttribute("LOGRADOURO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("BAIRRO");
        getAtributo().setValor(unidade.getAttribute("BAIRRO"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("CEP");
        getAtributo().setValor(unidade.getAttribute("COD_CEP"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("REG_SAUDE");
        getAtributo().setValor(unidade.getAttribute("REG_SAUDE"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("MICRO_REGIAO");
        getAtributo().setValor(unidade.getAttribute("MICRO_REG"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("DIST_SANITARIO");
        getAtributo().setValor(unidade.getAttribute("DIST_SANIT"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("DIST_ADMIN");
        getAtributo().setValor(unidade.getAttribute("DIST_ADMIN"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("TELEFONE");
        getAtributo().setValor(unidade.getAttribute("TELEFONE"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("FAX");
        getAtributo().setValor(unidade.getAttribute("FAX"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("EMAIL");
        getAtributo().setValor(unidade.getAttribute("E_MAIL"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("CNPJ");
        getAtributo().setValor(unidade.getAttribute("CNPJ"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_VARCHAR2);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("cd_esfera_admnistrativa");
        getAtributo().setValor(unidade.getAttribute("COD_ESFADM"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("cd_fluxo_clientela");
        getAtributo().setValor(unidade.getAttribute("COD_CLIENT"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("cod_atv");
        getAtributo().setValor(unidade.getAttribute("TP_UNID_ID"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("cd_turno_atendimento");
        getAtributo().setValor(unidade.getAttribute("COD_TURNAT"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("cd_hierarquia");
        getAtributo().setValor(unidade.getAttribute("CODNIVHIER"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        atributoList.add(getAtributo());

        newAtributo();
        getAtributo().setColunaBanco("cod_cid");
        getAtributo().setValor(unidade.getAttribute("CODMUNGEST"));
        getAtributo().setTipoDado(MCCadSusDefault.TIPO_NUMERIC);
        atributoList.add(getAtributo());

        return atributoList;
    }
    // </editor-fold>
    
    private AtributoXml atributo;

    private AtributoXml getAtributo() {
        return atributo;
    }

    private void newAtributo() {
        atributo = new AtributoXml(new MCCadSusDefault());
    }

    public void start() {
        for (String path : mapeamentos) {
            entidades.addAll(getMapeamento(path));
        }
    }

    public List<Entidade> getRegistros() {
        return entidades;
    }
}
