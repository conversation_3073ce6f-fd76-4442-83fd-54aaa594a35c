package br.com.ksisolucoes.integracao.util;

import br.com.celk.util.Util;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEndereco;
import br.com.ksisolucoes.vo.enderecoestruturado.EnderecoEstruturado;
import br.com.ksisolucoes.vo.enderecoestruturado.EnderecoEstruturadoLogradouro;
import br.com.ksisolucoes.vo.esus.helper.EsusIntegracaoHelper;

import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class AgendamentoUtils {
    private AgendamentoUtils() {
    }

    public static String getEquipeReferenciaEnderecoEstruturado(UsuarioCadsus usuarioCadsus, boolean utilizaEnderecoEstruturado) throws DAOException {
        // Mesma regra implementada no CadastroPacientePanel para busca do endereço estruturado
        Boolean exigeEquipeAcompanhamento = RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("ExigeEquipeAcompanhamento"));

        List<EquipeProfissional> equipeProfisionalLogado = null;
        try {
            equipeProfisionalLogado = getEquipeProfisionalLogado();
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage());
        }

        if (!equipeProfisionalLogado.isEmpty() && exigeEquipeAcompanhamento && utilizaEnderecoEstruturado && usuarioCadsus == null) {
            Equipe equipeProfisional = equipeProfisionalLogado.get(0).getEquipe();
            return Equipe.TIPOS_SAUDE_DA_FAMILIA.contains(equipeProfisional.getTipoEquipe().getCodigo()) ? equipeProfisional.getReferencia() : null;
        } else if (Util.isNotNull(usuarioCadsus) && Util.isNotNull(usuarioCadsus.getEquipe())) {
            return usuarioCadsus.getEquipe().getReferencia();
        } else {
            EnderecoEstruturado enderecoEstruturado = getEnderecoEstruturado(usuarioCadsus);
            if (enderecoEstruturado != null &&
                    Util.isNotNull(enderecoEstruturado.getEquipeMicroArea()) &&
                    Util.isNotNull(enderecoEstruturado.getEquipeMicroArea().getEquipeProfissional()) &&
                    Util.isNotNull(enderecoEstruturado.getEquipeMicroArea().getEquipeProfissional().getEquipe()))
                return enderecoEstruturado.getEquipeMicroArea().getEquipeProfissional().getEquipe().getReferencia();
        }
        return null;
    }

    private static EnderecoEstruturado getEnderecoEstruturado(UsuarioCadsus usuarioCadsus) {
        UsuarioCadsusEndereco usuarioCadsusEndereco = getUsuarioCadsusEndereco(usuarioCadsus);
        if (usuarioCadsusEndereco != null && Util.isNotNull(usuarioCadsusEndereco.getId()) && Util.isNotNull(usuarioCadsusEndereco.getId().getEndereco().getEnderecoEstruturado())) {
            EnderecoEstruturado enderecoEstruturadoProxy = on(EnderecoEstruturado.class);

            return LoadManager.getInstance(EnderecoEstruturado.class)
                    .addProperties(new HQLProperties(EnderecoEstruturado.class).getProperties())
                    .addProperties(new HQLProperties(Bairro.class, path(enderecoEstruturadoProxy.getBairro())).getProperties())
                    .addProperties(new HQLProperties(EquipeMicroArea.class, path(enderecoEstruturadoProxy.getEquipeMicroArea())).getProperties())
                    .addProperties(new HQLProperties(EnderecoEstruturadoLogradouro.class, path(enderecoEstruturadoProxy.getEnderecoEstruturadoLogradouro())).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(path(enderecoEstruturadoProxy.getCodigo()), usuarioCadsusEndereco.getId().getEndereco().getEnderecoEstruturado().getCodigo()))
                    .start().getVO();
        }

        return null;
    }

    private static List<EquipeProfissional> getEquipeProfisionalLogado() throws DAOException {
        return EsusIntegracaoHelper.findEquipesProfissional(getProfissional(), getEmpresa());
    }

    private static UsuarioCadsusEndereco getUsuarioCadsusEndereco(UsuarioCadsus usuarioCadsus) {
        if (Util.isNotNull(usuarioCadsus)) {
            UsuarioCadsusEndereco usuarioCadsusEnderecoProxy = on(UsuarioCadsusEndereco.class);

            return LoadManager.getInstance(UsuarioCadsusEndereco.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(path(usuarioCadsusEnderecoProxy.getRoUsuarioCadsus()), usuarioCadsus))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(usuarioCadsusEnderecoProxy.getStatus()), BuilderQueryCustom.QueryParameter.DIFERENTE, UsuarioCadsusEndereco.STATUS_CANCELADO, HQLHelper.NOT_RESOLVE_TYPE, UsuarioCadsusEndereco.STATUS_ABERTO))
                    .addProperties(new HQLProperties(EnderecoUsuarioCadsus.class, path(usuarioCadsusEnderecoProxy.getId().getEndereco())).getProperties())
                    .setMaxResults(1)
                    .start().getVO();
        }

        return null;
    }

    public static Profissional getProfissional() {
        return SessaoAplicacaoImp.getInstance().getUsuario().getProfissional();
    }

    private static Empresa getEmpresa() {
        return SessaoAplicacaoImp.getInstance().getEmpresa();
    }
}
