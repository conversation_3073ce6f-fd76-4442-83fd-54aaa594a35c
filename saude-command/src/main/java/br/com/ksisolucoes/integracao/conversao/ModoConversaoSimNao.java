/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.integracao.conversao;

import br.com.ksisolucoes.integracao.dao.util.vo.ModoConversao;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;

/**
 *
 * <AUTHOR>
 */
public class ModoConversaoSimNao implements ModoConversao<String> {

    @Override
    public Object getValorConvertido(String tipo, String value) throws IllegalArgumentException {
        if (Coalesce.asString(value).equals("1")) {
            return RepositoryComponentDefault.SIM;
        } else if (Coalesce.asString(value).equals("2")) {
            return RepositoryComponentDefault.NAO;
        }
        return null;
    }
}

