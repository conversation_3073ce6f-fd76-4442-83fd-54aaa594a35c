<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relacao_pacientes_comorbidade" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="904ef39c-8037-443b-8222-af49929dd456">
	<property name="ireport.zoom" value="2.2000000000000064"/>
	<property name="ireport.x" value="7"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="br.com.celk.util.Coalesce"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<import value="org.apache.commons.lang.StringUtils"/>
	<parameter name="FORMA_APRESENTACAO" class="br.com.celk.unidadesaude.doenca.interfaces.dto.QueryRelatorioPacienteCondicaoSituacaoSaudeDTOParam.FormaApresentacao"/>
	<parameter name="IMPRIMIR_ATENDIMENTO" class="java.lang.Long"/>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="equipeMicroArea" class="br.com.ksisolucoes.vo.basico.EquipeMicroArea"/>
	<field name="enderecoUsuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus"/>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="doenca" class="br.com.ksisolucoes.vo.basico.Doenca"/>
	<field name="faixaEtariaItem" class="br.com.ksisolucoes.vo.basico.FaixaEtariaItem"/>
	<field name="descricaoDoenca" class="java.lang.String"/>
	<field name="descricaoFaixaEtaria" class="java.lang.String"/>
	<field name="descricaoUnidade" class="java.lang.String"/>
	<field name="descricaoArea" class="java.lang.String"/>
	<field name="descricaoMicroArea" class="java.lang.String"/>
	<field name="dataUltimoAtendimento" class="java.util.Date"/>
	<field name="descricaoTipoAtendimento" class="java.lang.String"/>
	<variable name="usuarioCadsus_1" class="java.lang.Integer" resetType="Group" resetGroup="FA" calculation="Count">
		<variableExpression><![CDATA[$F{usuarioCadsus}]]></variableExpression>
	</variable>
	<variable name="usuarioCadsus_3" class="java.lang.Integer" resetType="Group" resetGroup="FADefault" calculation="Count">
		<variableExpression><![CDATA[$F{usuarioCadsus}]]></variableExpression>
	</variable>
	<variable name="FA" class="br.com.celk.unidadesaude.doenca.interfaces.dto.QueryRelatorioPacienteCondicaoSituacaoSaudeDTOParam.FormaApresentacao"/>
	<group name="FADefault">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band/>
		</groupHeader>
		<groupFooter>
			<band height="14">
				<textField>
					<reportElement uuid="ae4251ef-856e-42b3-809b-396a92c7e146" x="764" y="2" width="36" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{usuarioCadsus_3}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="8345a293-b452-4406-b95b-5ac985206b88" x="718" y="2" width="46" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_geral") + ": "]]></textFieldExpression>
				</textField>
				<line>
					<reportElement uuid="74011bb7-90cb-42e4-9848-1230bc7c15e7" x="718" y="1" width="84" height="1"/>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="FA" isReprintHeaderOnEachPage="true" keepTogether="true">
		<groupExpression><![CDATA[$V{FA}.CONDICAO_SITUACAO_SAUDE.equals($P{FORMA_APRESENTACAO}) ?
    $F{descricaoDoenca}
:
    $V{FA}.FAIXA_ETARIA.equals($P{FORMA_APRESENTACAO}) ?
        $F{descricaoFaixaEtaria}
    :
        $V{FA}.UNIDADE.equals($P{FORMA_APRESENTACAO}) ?
            $F{descricaoUnidade}
        :
            $V{FA}.AREA.equals($P{FORMA_APRESENTACAO}) ?
                $F{descricaoArea}
            :
                $V{FA}.MICRO_AREA.equals($P{FORMA_APRESENTACAO}) ?
                    $F{descricaoMicroArea}
                :
                    null]]></groupExpression>
		<groupHeader>
			<band height="26">
				<textField>
					<reportElement uuid="985826df-b42d-4bcf-9c57-0f7e63ed7560" x="0" y="14" width="256" height="11"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_paciente")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="bbc19e09-5458-4cd3-9e87-a164c4143f53" x="258" y="14" width="23" height="10"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_idade")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="90c7a641-b12a-4e67-bf8e-6407c4f7d5a2" x="293" y="14" width="37" height="11"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_sexo")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="a4c0356d-a7ca-4e0c-bc2b-b4f8ff1f54a9" x="336" y="14" width="172" height="11"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_mae")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement uuid="fb44fa67-4ef7-47e8-b53f-ca21c6313e0c" x="0" y="24" width="802" height="1"/>
				</line>
				<textField>
					<reportElement uuid="e199520c-c239-4028-a5d2-b36929bfb86b" x="0" y="0" width="802" height="13"/>
					<textElement>
						<font fontName="Arial" size="10" isBold="true" isUnderline="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{FA}.CONDICAO_SITUACAO_SAUDE.equals($P{FORMA_APRESENTACAO}) ?
    $F{descricaoDoenca}
:
    $V{FA}.FAIXA_ETARIA.equals($P{FORMA_APRESENTACAO}) ?
        $F{descricaoFaixaEtaria}
    :
        $V{FA}.UNIDADE.equals($P{FORMA_APRESENTACAO}) ?
            $F{descricaoUnidade}
        :
            $V{FA}.AREA.equals($P{FORMA_APRESENTACAO}) ?
                $F{descricaoArea}
            :
                $V{FA}.MICRO_AREA.equals($P{FORMA_APRESENTACAO}) ?
                    $F{descricaoMicroArea}
                :
                    null]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="f564cc3b-5f8a-42e3-8aae-0a3f43e953b4" x="510" y="14" width="290" height="10"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{IMPRIMIR_ATENDIMENTO} == 0L
?
Bundle.getStringApplication("rotulo_data_nascimento_abv3")
:
Bundle.getStringApplication("rotulo_ultimo_atendimento_abv")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="13">
				<textField>
					<reportElement uuid="61c7b548-9c05-4407-af90-365421551d7d" x="764" y="2" width="36" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{usuarioCadsus_1}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="2bae9521-d953-4c9f-ab88-b051edb9372b" x="687" y="2" width="77" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")
+ " " +
($V{FA}.CONDICAO_SITUACAO_SAUDE.equals($P{FORMA_APRESENTACAO}) ?
    Bundle.getStringApplication("rotulo_condicao_saude") + ": "
:
    $V{FA}.FAIXA_ETARIA.equals($P{FORMA_APRESENTACAO}) ?
        Bundle.getStringApplication("rotulo_faixa_etaria") + ": "
    :
        $V{FA}.UNIDADE.equals($P{FORMA_APRESENTACAO}) ?
            Bundle.getStringApplication("rotulo_unidade") + ": "
        :
            $V{FA}.AREA.equals($P{FORMA_APRESENTACAO}) ?
                Bundle.getStringApplication("rotulo_area") + ": "
            :
                $V{FA}.MICRO_AREA.equals($P{FORMA_APRESENTACAO}) ?
                    Bundle.getStringApplication("rotulo_micro_area") + ": "
                :
                    null)]]></textFieldExpression>
				</textField>
				<line>
					<reportElement uuid="8bfe4a50-cb96-4e1d-a685-d65ba678c114" x="687" y="1" width="115" height="1"/>
				</line>
			</band>
		</groupFooter>
	</group>
	<detail>
		<band height="16" splitType="Immediate">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="c2f57e7f-b180-4855-9012-7fa5d4f14afa" positionType="Float" stretchType="RelativeToTallestObject" x="0" y="4" width="256" height="11"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNome()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="728b2e70-e30f-410c-8e6b-c16f46d657b0" positionType="Float" stretchType="RelativeToTallestObject" x="293" y="4" width="37" height="11"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getSexoFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="52fae1d9-fe11-4dc5-9d16-5adee2dde633" positionType="Float" stretchType="RelativeToTallestObject" x="336" y="4" width="172" height="11"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNomeMae()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="0304db16-c378-43e7-8139-ac3751cbacb7" positionType="Float" stretchType="RelativeToTallestObject" x="258" y="4" width="23" height="11"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getIdade()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement uuid="2c27c4e4-d5fb-47b5-8a16-8a7c736691b1" positionType="Float" stretchType="RelativeToTallestObject" x="510" y="4" width="290" height="11"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{IMPRIMIR_ATENDIMENTO} == 0L
?
Data.formatar($F{usuarioCadsus}.getDataNascimento())
:
Data.formatar($F{dataUltimoAtendimento}) + " " + ($F{descricaoTipoAtendimento} == null ? "" : $F{descricaoTipoAtendimento})]]></textFieldExpression>
			</textField>
		</band>
		<band height="14">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="ce4520e6-555f-4bd9-9b0d-b9268ddff410" positionType="Float" stretchType="RelativeToTallestObject" x="64" y="0" width="625" height="11"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{enderecoUsuarioCadsus}.getEnderecoComplementoFormatadoComCidade()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement uuid="d0cd4000-2713-4fa4-9017-04aaf7984f01" positionType="Float" stretchType="RelativeToTallestObject" x="22" y="0" width="42" height="11"/>
				<box>
					<leftPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_endereco") + ": "]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
