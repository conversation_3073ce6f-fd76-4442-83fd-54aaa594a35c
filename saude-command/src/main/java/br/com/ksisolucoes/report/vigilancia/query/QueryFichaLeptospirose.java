package br.com.ksisolucoes.report.vigilancia.query;

import br.com.celk.report.vigilancia.query.QueryFichaInvestigacaoBase;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.vigilancia.investigacao.*;
import org.hibernate.Session;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class QueryFichaLeptospirose extends QueryFichaInvestigacaoBase {

    @Override
    protected Class getClasseFichaInvestigacao() {
        return InvestigacaoAgravoLeptospirose.class;
    }

    @Override
    protected Map<String, String> getCamposInvestigacaoAgravo() {
        Map<String, String> campos = new HashMap<>();

        //ANTECEDENTES EPIDEMIOLÓGICOS
        campos.put("investigacaoAgravo.codigo", "investigacaoAgravo_codigo");
        campos.put(formatarData("investigacaoAgravo.dataInvestigacao"), "_31_data_investigacao");
        campos.put("ocupacaoCbo.descricao", "_32_ocupacao");
        campos.put("investigacaoAgravo.situacaoRiscoAguaLamaEnchente", "_33_situacao_risco_agua_lama_enchente");
        campos.put("investigacaoAgravo.situacaoRiscoCriacaoAnimais", "_33_situacao_risco_criacao_animais");
        campos.put("investigacaoAgravo.situacaoRiscoCaixaAgua", "_33_situacao_risco_caixa_agua");
        campos.put("investigacaoAgravo.situacaoRiscoFossaCaixaGorduraEsgoto", "_33_situacao_risco_fossa_caixa_gordura_esgoto");
        campos.put("investigacaoAgravo.situacaoRiscoLocalRoedores", "_33_situacao_risco_risco_local_roedores");
        campos.put("investigacaoAgravo.situacaoRiscoPlantioColheita", "_33_situacao_risco_plantio_colheita");
        campos.put("investigacaoAgravo.situacaoRiscoRioCorregoLagoaRepresa", "_33_situacao_risco_rio_corrego_lagoa_represa");
        campos.put("investigacaoAgravo.situacaoRiscoRoedoresDiretamente", "_33_situacao_risco_roedores_diretamente");
        campos.put("investigacaoAgravo.situacaoRiscoArmazenamentoGraosAlimentos", "_33_situacao_risco_armazenamento_graos");
        campos.put("investigacaoAgravo.situacaoRiscoTerrenoBaldio", "_33_situacao_risco_terreno_baldio");
        campos.put("investigacaoAgravo.situacaoRiscoLixoEntulho", "_33_situacao_risco_lixo_entulho");
        campos.put("investigacaoAgravo.situacaoRiscoOutros", "_33_situacao_risco_outros");
        campos.put("investigacaoAgravo.casosAnterioresLocalHumanos", "_34_casos_anteriores_local_humanos");
        campos.put("investigacaoAgravo.casosAnterioresLocalAnimais", "_34_casos_anteriores_local_animais");

        //DADOS CLÍNICOS
        campos.put(formatarData("investigacaoAgravo.dataAtendimento"), "_35_data_atendimento");
        campos.put("investigacaoAgravo.sinaisSintomasFebre", "_36_sinais_sintomas_febre");
        campos.put("investigacaoAgravo.sinaisSintomasCongestaoConjuntival", "_36_sinais_sintomas_congestao");
        campos.put("investigacaoAgravo.sinaisSintomasIctericia", "_36_sinais_sintomas_ictericia");
        campos.put("investigacaoAgravo.sinaisSintomasHemorragiaPulmonar", "_36_sinais_sintomas_hemorragia_pulmonar");
        campos.put("investigacaoAgravo.sinaisSintomasMialgia", "_36_sinais_sintomas_mialgia");
        campos.put("investigacaoAgravo.sinaisSintomasDorPanturrilha", "_36_sinais_sintomas_dor_panturrilha");
        campos.put("investigacaoAgravo.sinaisSintomasInsuficienciaRenal", "_36_sinais_sintomas_insuficiencia_renal");
        campos.put("investigacaoAgravo.sinaisSintomasOutrasHemorragias", "_36_sinais_sintomas_outras_hemorragias");
        campos.put("investigacaoAgravo.sinaisSintomasCefaleia", "_36_sinais_sintomas_cefaleia");
        campos.put("investigacaoAgravo.sinaisSintomasVomito", "_36_sinais_sintomas_vomito");
        campos.put("investigacaoAgravo.sinaisSintomasAlteracoesRespiratorias", "_36_sinais_sintomas_alteracoes_respiratorias");
        campos.put("investigacaoAgravo.sinaisSintomasMeningismo", "_36_sinais_sintomas_meningismo");
        campos.put("investigacaoAgravo.sinaisSintomasProstracao", "_36_sinais_sintomas_prostracao");
        campos.put("investigacaoAgravo.sinaisSintomasDiarreia", "_36_sinais_sintomas_diarreia");
        campos.put("investigacaoAgravo.sinaisSintomasAlteracoesCardiacas", "_36_sinais_sintomas_alteracoes_cardiacas");
        campos.put("investigacaoAgravo.sinaisSintomasOutros", "_36_sinais_sintomas_outros");

        //ATENDIMENTO
        campos.put("investigacaoAgravo.hospitalizacao", "_37_ocorreu_hospitalizacao");
        campos.put(formatarData("investigacaoAgravo.dataInternacao"), "_38_data_internacao");
        campos.put(formatarData("investigacaoAgravo.dataAlta"), "_39_data_alta");
        campos.put("estadohospitalAtendimento.sigla", "_40_estado_hospital_atendimento");
        campos.put("cidadehospitalAtendimento.descricao", "_41_municipio_hospital_atendimento");
        campos.put("cidadehospitalAtendimento.codigo", "_41_ibge_hospital_atendimento");
        campos.put("hospitalAtendimento.descricao", "_42_nome_hospital_atendimento");
        campos.put("hospitalAtendimento.codigo", "_42_codigo_hospital_atendimento");

        //DADOS DO LABORATÓRIO
        //Sorologia IgM - Elisa
        campos.put(formatarData("investigacaoAgravo.sorologiaIgmDataColeta1"), "_43_sorologia_igm_data_coleta1");
        campos.put("investigacaoAgravo.sorologiaIgmResultado1", "_44_sorologia_igm_resultado1");
        campos.put(formatarData("investigacaoAgravo.sorologiaIgmDataColeta2"), "_45_sorologia_igm_data_coleta2");
        campos.put("investigacaoAgravo.sorologiaIgmResultado2", "_46_sorologia_igm_resultado2");

        //Microaglutinação
        campos.put(formatarData("investigacaoAgravo.microaglutinacaoDataColeta1"), "_47_microaglutinacao_data_coleta1");
        campos.put("investigacaoAgravo.microaglutinacaoAmostra1Sorovar1", "_48_microaglutinacao_amostra1_sorovar1");
        campos.put("investigacaoAgravo.microaglutinacaoAmostra1Sorovar2", "_49_microaglutinacao_amostra1_sorovar2");
        campos.put("investigacaoAgravo.microaglutinacaoResultadoAmostra1", "_50_microaglutinacao_resultado_amostra1");
        campos.put(formatarData("investigacaoAgravo.microaglutinacaoDataColeta2"), "_51_microaglutinacao_data_coleta2");
        campos.put("investigacaoAgravo.microaglutinacaoAmostra2Sorovar1", "_52_microaglutinacao_amostra2_sorovar1");
        campos.put("investigacaoAgravo.microaglutinacaoAmostra2Sorovar2", "_53_microaglutinacao_amostra2_sorovar2");
        campos.put("investigacaoAgravo.microaglutinacaoResultadoAmostra2", "_54_microaglutinacao_resultado_amostra2");

        //isolamento
        campos.put(formatarData("investigacaoAgravo.isolamentoDataColeta"), "_55_isolamento_data_coleta");
        campos.put("investigacaoAgravo.isolamentoResultado", "_56_isolamento_resultado");

        //Imunohistoquímica
        campos.put(formatarData("investigacaoAgravo.imunohistoquimicaDataColeta"), "_57_imunohistoquimica_data_coleta");
        campos.put("investigacaoAgravo.imunohistoquimicaResultado", "_58_imunohistoquimica_resultado");

        //RT-PCR
        campos.put(formatarData("investigacaoAgravo.rtpcrDataColeta"), "_59_rtpcr_data_coleta");
        campos.put("investigacaoAgravo.rtpcrResultado", "_60_rtpcr_resultado");

        //CONSLUSÃO
        campos.put("investigacaoAgravo.classificacaoFinal", "_61_classificacao_final");
        campos.put("investigacaoAgravo.criterioConfirmacao", "_62_criterio_confirmacao");
        campos.put("investigacaoAgravo.casoAutoctone", "_63_caso_autoctone");
        campos.put("estadoLocalInfeccao.sigla", "_64_uf_local_infeccao");
        campos.put("paisLocalInfeccao.descricao", "_65_pais_local_infeccao");
        campos.put("cidadeLocalInfeccao.descricao", "_66_cidade_local_infeccao");
        campos.put("cidadeLocalInfeccao.codigo", "_66_ibge_local_infeccao");
        campos.put("investigacaoAgravo.distritoLocalInfeccao", "_67_distrito_local_infeccao");
        campos.put("investigacaoAgravo.bairroLocalInfeccao", "_68_bairro_local_infeccao");
        campos.put("investigacaoAgravo.areaProvavelInfeccao", "_69_area_local_infeccao");
        campos.put("investigacaoAgravo.ambienteInfeccao", "_70_ambiente_local_infeccao");
        campos.put("investigacaoAgravo.doencaRelacionadaTrabalho", "_71_doenca_relacionada_trabalho");
        campos.put("investigacaoAgravo.evolucaoCaso", "_72_evolucao_caso");
        campos.put(formatarData("investigacaoAgravo.dataObito"), "_73_data_obito");
        campos.put(formatarData("investigacaoAgravo.dataEncerramento"), "_74_data_encerramento");

        //INFORMAÇÕES COMPLEMENTARES E OBSERVAÇÕES
        campos.put("investigacaoAgravo.observacao", "observacoes");

        return campos;
    }

    @Override
    protected String getJuncoesFicha() {
        return "left join investigacaoAgravo.ocupacaoCbo ocupacaoCbo "
                + "left join investigacaoAgravo.hospital hospitalAtendimento "
                + "left join hospitalAtendimento.cidade cidadehospitalAtendimento "
                + "left join cidadehospitalAtendimento.estado estadohospitalAtendimento "
                + "left join investigacaoAgravo.cidadeLocalInfeccao cidadeLocalInfeccao "
                + "left join cidadeLocalInfeccao.estado estadoLocalInfeccao "
                + "left join investigacaoAgravo.paisLocalInfeccao paisLocalInfeccao ";
    }
    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        super.customProcess(session);
        for (Map<String, Object> map : getResult()) {
            addListaInformacoesComplementares(map);
        }
    }

    private void addListaInformacoesComplementares(Map<String, Object> map) {
        int i;
        Long codigoInvestigacaoAgravoLeptospirose = (Long) map.get("investigacaoAgravo_codigo");

        if (codigoInvestigacaoAgravoLeptospirose != null) {
            List<InvestigacaoAgravoLeptospiroseLocalSituacaoRisco> deslocamentoList =
                    LoadManager.getInstance(InvestigacaoAgravoLeptospiroseLocalSituacaoRisco.class)
                            .addProperties(new HQLProperties(InvestigacaoAgravoLeptospiroseLocalSituacaoRisco.class).getProperties())
                            .addProperty(VOUtils.montarPath(InvestigacaoAgravoLeptospiroseLocalSituacaoRisco.PROP_CIDADE_LOCAL_SITUACAO_RISCO, Cidade.PROP_ESTADO, Estado.PROP_SIGLA))
                            .addParameter(new QueryCustom.QueryCustomParameter(
                                    VOUtils.montarPath(InvestigacaoAgravoLeptospiroseLocalSituacaoRisco.PROP_INVESTIGACAO_AGRAVO_LEPTOSPIROSE, InvestigacaoAgravoLeptospirose.PROP_CODIGO),
                                    codigoInvestigacaoAgravoLeptospirose))
                            .start().getList();

            for (i = 0; i < deslocamentoList.size(); i++) {
                InvestigacaoAgravoLeptospiroseLocalSituacaoRisco investigacaoAgravoLocalSituacaoRisco = deslocamentoList.get(i);

                map.put("_data_local_situacao_risco" + i, Data.formatar(investigacaoAgravoLocalSituacaoRisco.getDataLocalSituacaoRisco()));
                map.put("_uf" + i, investigacaoAgravoLocalSituacaoRisco.getCidadeLocalSituacaoRisco().getEstado().getSigla());
                map.put("_municipio" + i, investigacaoAgravoLocalSituacaoRisco.getCidadeLocalSituacaoRisco().getDescricao());
                map.put("_endereco" + i, investigacaoAgravoLocalSituacaoRisco.getEndereco());
                map.put("_localidade" + i, investigacaoAgravoLocalSituacaoRisco.getLocalidade());
            }
        }
    }
}
