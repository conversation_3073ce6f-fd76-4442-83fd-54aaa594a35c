package br.com.ksisolucoes.report.cadsus;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.cadsus.interfaces.dto.FichaPacienteDTOParam;
import br.com.ksisolucoes.report.cadsus.query.QueryRelatorioFichaPaciente;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioFichaPaciente extends AbstractReport<FichaPacienteDTOParam>{

    public RelatorioFichaPaciente(FichaPacienteDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        addParametro("USUARIO_LOGADO", getSessao().getUsuario().getNome());
        return "/br/com/ksisolucoes/report/cadsus/jrxml/relatorio_ficha_paciente.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_ficha_paciente");
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioFichaPaciente();
    }
}
