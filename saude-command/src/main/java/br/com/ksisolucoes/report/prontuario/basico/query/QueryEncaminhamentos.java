package br.com.ksisolucoes.report.prontuario.basico.query;

import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioPerfilAtendimentoDTO;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioPerfilAtendimentoDTOParam;
import java.util.List;
import java.util.Map;

public class QueryEncaminhamentos extends QueryPerfilAtendimento {

    private List<RelatorioPerfilAtendimentoDTO> result;

    public QueryEncaminhamentos(RelatorioPerfilAtendimentoDTOParam param) {
        super(param);
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("a.conduta.descricao", "descricao");
        hql.addToSelect("sum(1)", "quantidade");
        {
            //subSelect para o total
            HQLHelper hqlTotal = hql.getNewInstanceSubQuery();
            hqlTotal.addToSelect("count(a1.conduta.codigo)");
            hqlTotal.addToFrom("Atendimento a1");
            addWhereAtendimento(hqlTotal, "a1", false);
            hql.addToSelect("(" + hqlTotal.getQuery() + ")", "total");
        }
        hql.addToFrom("Atendimento a");
        hql.setTypeSelect(RelatorioPerfilAtendimentoDTO.class.getName());
        addWhereAtendimento(hql, "a", false);
        hql.addToGroup("a.conduta.descricao");
        hql.addToOrder("2 desc");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioPerfilAtendimentoDTO> getResult() {
        return result;
    }
}
