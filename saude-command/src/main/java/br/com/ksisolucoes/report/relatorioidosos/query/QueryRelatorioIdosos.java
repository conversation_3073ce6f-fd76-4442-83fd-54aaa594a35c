package br.com.ksisolucoes.report.relatorioidosos.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.relacaoidosos.dto.RelatorioIdososDTO;
import br.com.ksisolucoes.report.relacaoidosos.dto.RelatorioIdososDTOParam;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDoenca;
import br.com.ksisolucoes.vo.cadsus.VisitaDomiciliar;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioIdosos extends CommandQuery<QueryRelatorioIdosos> implements ITransferDataReport<RelatorioIdososDTOParam, RelatorioIdososDTO>{

    private RelatorioIdososDTOParam param;
    private List<RelatorioIdososDTO> result;

    @Override
    public void setDTOParam(RelatorioIdososDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(RelatorioIdososDTO.class.getName());

        hql.addToSelect("ucd.codigo", "usuarioCadsusDoenca.codigo");
        hql.addToSelect("doenca.codigo", "usuarioCadsusDoenca.doenca.codigo");
        hql.addToSelect("doenca.descricao", "usuarioCadsusDoenca.doenca.descricao");

        hql.addToSelect("ucs.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("ucs.nome", "usuarioCadsus.nome");
        hql.addToSelect("ucs.dataNascimento", "usuarioCadsus.dataNascimento");
        hql.addToSelect("ema.microArea","usuarioCadsus.enderecoDomicilio.equipeMicroArea.microArea");
        hql.addToSelect("ea.descricao","usuarioCadsus.enderecoDomicilio.equipeMicroArea.equipeArea.descricao");
        hql.addToSelect("empresa.descricao","usuarioCadsus.empresaResponsavel.descricao");

        hql.addToSelect("(select max(at.dataAtendimento) from Atendimento at where at.usuarioCadsus = ucs"
                + " and at.status in ("  + Atendimento.STATUS_FINALIZADO + "))", "dataUltimoAtendimento");
        hql.addToSelect("(select max(vd.dataVisita) from VisitaDomiciliar vd where vd.usuarioCadsus = ucs"
                + " and vd.situacao in ("  + VisitaDomiciliar.Situacao.CADASTRADO.value() + "))", "dataUltimaVisita");

        hql.addToSelect("edu.codigo", "usuarioCadsus.enderecoUsuarioCadsus.codigo");
        hql.addToSelect("edu.numeroLogradouro", "usuarioCadsus.enderecoUsuarioCadsus.numeroLogradouro");
        hql.addToSelect("edu.logradouro", "usuarioCadsus.enderecoUsuarioCadsus.logradouro");
        hql.addToSelect("edu.bairro", "usuarioCadsus.enderecoUsuarioCadsus.bairro");
        hql.addToSelect("edu.cep", "usuarioCadsus.enderecoUsuarioCadsus.cep");
        hql.addToSelect("edu.complementoLogradouro", "usuarioCadsus.enderecoUsuarioCadsus.complementoLogradouro");

        hql.addToSelect("cidade.codigo", "usuarioCadsus.enderecoUsuarioCadsus.cidade.codigo");
        hql.addToSelect("cidade.descricao", "usuarioCadsus.enderecoUsuarioCadsus.cidade.descricao");
        hql.addToSelect("estado.codigo", "usuarioCadsus.enderecoUsuarioCadsus.cidade.estado.codigo");
        hql.addToSelect("estado.sigla", "usuarioCadsus.enderecoUsuarioCadsus.cidade.estado.sigla");

        hql.addToFrom("UsuarioCadsusDoenca ucd "
        +   "LEFT JOIN ucd.doenca doenca "
        +   "RIGHT JOIN ucd.usuarioCadsus ucs "
        +   "LEFT JOIN ucs.enderecoDomicilio ed "
        +   "LEFT JOIN ed.equipeMicroArea ema "
        +   "LEFT JOIN ema.equipeArea ea "
        +   "LEFT JOIN ucs.enderecoUsuarioCadsus edu "
        +   "LEFT JOIN edu.cidade cidade "
        +   "LEFT JOIN cidade.estado estado "
        +   "LEFT JOIN ucs.empresaResponsavel empresa ");


        hql.addToWhereWhithAnd("cast(extract(years from age(current_date, ucs.dataNascimento)) as long) >= 60 ");
        hql.addToWhereWhithAnd("empresa in ", param.getEstabelecimento());
        hql.addToWhereWhithAnd("ea = ", param.getEquipeArea());
        hql.addToWhereWhithAnd("ema = ", param.getEquipeMicroArea());
        hql.addToWhereWhithAnd("ucs.sexo = ", param.getSexo());
        hql.addToWhereWhithAnd("ucs = ", param.getPaciente());
        hql.addToWhereWhithAnd("doenca in ", param.getDoencaList());

        hql.addToOrder("ucs.nome");
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(result)) {
            Group<RelatorioIdososDTO> byUsuarioCadsus = Lambda.group(result, Lambda.by(Lambda.on(RelatorioIdososDTO.class).getUsuarioCadsus()));
            List<RelatorioIdososDTO> groupedList = new ArrayList<>();
            for (Group<RelatorioIdososDTO> group : byUsuarioCadsus.subgroups()) {
                RelatorioIdososDTO dto = group.first();
                List<RelatorioIdososDTO> all = group.findAll();
                Lambda.on(RelatorioIdososDTO.class).getUsuarioCadsusDoenca();
                List<UsuarioCadsusDoenca> usuarioCadsusDoencas = Lambda.extract(all, Lambda.on(RelatorioIdososDTO.class).getUsuarioCadsusDoenca());
                if (CollectionUtils.isNotNullEmpty(usuarioCadsusDoencas)) {
                    List<String> extract = Lambda.extract(usuarioCadsusDoencas, Lambda.on(UsuarioCadsusDoenca.class).getDoenca().getDescricao());
                    String doencasFormatada = Lambda.join(extract, ", ");
                    dto.setDoencas(doencasFormatada);
                }
                groupedList.add(dto);
            }
            result.clear();
            result.addAll(groupedList);
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public Collection getResult() {
        return result;
    }
}
