<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="impressao_habitese" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="a1b57cfd-9055-4cac-88e7-9178d405e0e1">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.5421731577181272"/>
	<property name="ireport.x" value="20"/>
	<property name="ireport.y" value="48"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.util.Util"/>
	<import value="br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper"/>
	<import value="br.com.celk.util.DataUtil"/>
	<import value="br.com.celk.util.Coalesce"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<subDataset name="fiscais" uuid="4795f36f-f40a-4bf1-9798-6251b27cc871">
		<field name="nome" class="java.lang.String"/>
		<field name="referenciaRegistroFormatado" class="java.lang.String"/>
	</subDataset>
	<parameter name="cidade" class="java.lang.String"/>
	<parameter name="uf" class="java.lang.String"/>
	<parameter name="urlQRcode" class="java.lang.String"/>
	<parameter name="para" class="java.lang.String"/>
	<parameter name="marcaDagua" class="java.lang.Boolean"/>
	<parameter name="pathMarcaDagua" class="java.lang.String"/>
	<parameter name="observacaoDestaque" class="java.lang.String"/>
    <parameter name="gerarDocumentoComAssinaturaFiscal" class="java.lang.Boolean"/>
	<field name="parecer" class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaHidrossanitarioDeclaratorioParecer">
		<fieldDescription><![CDATA[parecer]]></fieldDescription>
	</field>
	<field name="descricaoAtividadePrincipal" class="java.lang.String">
		<fieldDescription><![CDATA[descricaoAtividadePrincipal]]></fieldDescription>
	</field>
	<field name="inscricoesImobiliarias" class="java.lang.String">
		<fieldDescription><![CDATA[inscricoesImobiliarias]]></fieldDescription>
	</field>
	<field name="requerimento" class="br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia">
		<fieldDescription><![CDATA[requerimento]]></fieldDescription>
	</field>
	<field name="numeroProjetoRPH" class="java.lang.String"/>
	<field name="numeroProjeto" class="java.lang.Long"/>
	<field name="nomePessoa" class="java.lang.String"/>
	<field name="nomeFantasia" class="java.lang.String"/>
	<field name="endereco" class="java.lang.String"/>
	<field name="bairro" class="java.lang.String"/>
	<field name="cep" class="java.lang.String"/>
	<field name="complemento" class="java.lang.String"/>
	<field name="fone" class="java.lang.String"/>
	<field name="atividadePrincipal" class="java.lang.String"/>
	<field name="cpfCnpj" class="java.lang.String"/>
	<field name="fiscais" class="java.util.List"/>
	<group name="parecer" isStartNewPage="true">
		<groupExpression><![CDATA[$F{parecer}]]></groupExpression>
	</group>
	<background>
		<band height="802" splitType="Stretch">
			<image hAlign="Center" vAlign="Middle">
				<reportElement x="0" y="0" width="555" height="802" uuid="0e07e797-d27f-4884-bd27-962c2b11c35e">
					<printWhenExpression><![CDATA[$P{marcaDagua}]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[$P{pathMarcaDagua}]]></imageExpression>
			</image>
		</band>
	</background>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<detail>
		<band height="10"/>
		<band height="161">
			<rectangle>
				<reportElement positionType="Float" mode="Transparent" x="2" y="33" width="550" height="32" uuid="5e7ef14b-efe8-4569-ba4d-ef6bbf8c64b9"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement positionType="Float" mode="Transparent" x="2" y="65" width="550" height="32" uuid="c6e72a7b-9706-4b7e-bbbb-8f8da38847ea"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement positionType="Float" mode="Transparent" x="2" y="97" width="550" height="32" uuid="c7d39a0d-d959-4304-bf8a-cd431da0cf9f"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement positionType="Float" mode="Transparent" x="2" y="129" width="550" height="32" uuid="a45a0562-3bf8-49d5-854c-5e345a790c57"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="3" width="239" height="14" isRemoveLineWhenBlank="true" uuid="467d840d-73e0-467a-a289-16fad794eef7"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_para").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="35" width="427" height="14" uuid="152ffd82-00bc-4ec2-88cf-ab72245beb47"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nome_pessoa_fisica_juridica").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="67" width="546" height="14" uuid="3d1d50f2-b716-4a63-a05d-f0fcc1dbf74f"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("nome_fantasia").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="434" y="50" width="116" height="14" uuid="38d1d284-3416-4d50-863f-69d45c4fe28f"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cpfCnpj}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="99" width="427" height="14" uuid="7103b3fc-476b-4b3c-a44f-6bd4ac883466"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_endereco").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="434" y="99" width="116" height="14" uuid="74f792a1-8d71-4ccf-9d4f-5d033cf1fcad"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cep")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="131" width="193" height="14" uuid="ce5b0aff-40b5-4671-aabc-0b1830f6f9dd"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_bairro").toUpperCase()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" x="431" y="33" width="1" height="32" uuid="98a7466b-79f3-414e-b88b-8871138294b5"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="434" y="35" width="116" height="14" uuid="46c67ec8-b71d-4720-9863-8f7bfc3cb64e"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cnpj_cpf").toUpperCase()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" x="431" y="129" width="1" height="32" uuid="9e75ceda-1e13-4fdd-97e4-b76d0e3e7182"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<line>
				<reportElement positionType="Float" x="431" y="97" width="1" height="32" uuid="b5dde8a5-27fb-4c7a-bd4b-8a518537e18d"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="434" y="131" width="116" height="14" uuid="a87a4ebe-89fd-46d1-86e5-3a684a6999cd"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_fone").toUpperCase()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" x="199" y="129" width="1" height="32" uuid="1bc6fddf-6018-432a-9dbe-7d0036f9d7bb"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="200" y="131" width="231" height="14" uuid="0162f767-cd04-4a44-8a9d-bfe3e27d5a35"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_complemento").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="17" width="239" height="14" isRemoveLineWhenBlank="true" uuid="e5425bfc-1a8f-46ad-add2-19db07920ca8"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{para}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="49" width="427" height="14" uuid="dcd94a61-6134-47f2-bfef-3d2435837653"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nomePessoa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="81" width="546" height="14" uuid="64cbf08f-2440-4b64-9ad0-086f7bb4e8fc"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nomeFantasia}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="113" width="427" height="14" uuid="aa24b75b-7dff-4c84-adeb-34cc988b07dd"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{endereco}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="434" y="145" width="116" height="14" uuid="51348877-7b1b-484f-88f0-844851d42686"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{fone}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="200" y="145" width="231" height="14" uuid="99351bb5-4598-4bfa-8f94-8fbff6f5473a"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{complemento}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="145" width="193" height="14" uuid="18bf6b39-89d1-4c27-960d-fdab267c834a"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{bairro}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="434" y="113" width="116" height="14" uuid="1e71209b-6aaf-410c-8562-8c206f062a23"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cep}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="434" y="17" width="116" height="14" uuid="1bde5c23-d813-4614-9bf2-130520b7ef35"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{parecer}.getNumeroHabiteseFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="434" y="4" width="116" height="13" uuid="1318bcf1-903b-43c7-afc0-92a97c71f03d"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_numero_habitese").toUpperCase()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" x="244" y="1" width="1" height="32" uuid="da4847b3-d1a2-47ac-b2c1-002ca3f49070"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<line>
				<reportElement positionType="Float" x="431" y="1" width="1" height="32" uuid="28993a9a-091a-4ec1-9f29-3cf1ecb80b2b"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<rectangle>
				<reportElement positionType="Float" mode="Transparent" x="2" y="1" width="550" height="32" isRemoveLineWhenBlank="true" uuid="89a366c6-cdf5-4bd2-af2c-be17a2c61381"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="245" y="3" width="185" height="14" isRemoveLineWhenBlank="true" uuid="b8888dc1-e243-4277-a70a-bbbb99115bbc"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_numero_projeto_hidrossanitario_aprovado").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="246" y="17" width="184" height="14" isRemoveLineWhenBlank="true" uuid="2719f492-dc86-4fa4-9fb1-e41c413a10d6"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[( $F{numeroProjeto} == null ? $F{numeroProjetoRPH} : VigilanciaHelper.getProtocoloSemAno($F{numeroProjeto}) )]]></textFieldExpression>
			</textField>
		</band>
		<band height="64">
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="4" y="2" width="546" height="14" uuid="fc347faa-d159-4cf2-9fb0-ee4099112093"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_tipos_projeto_areas").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="34" width="546" height="14" uuid="28f4f1cc-fbcf-423a-80c4-9206c10c172c"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_inscricoes_imobiliarias_abv").toUpperCase()]]></textFieldExpression>
			</textField>
			<elementGroup>
				<rectangle>
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="2" y="32" width="550" height="32" isPrintWhenDetailOverflows="true" uuid="0eb21c9a-7e88-4e84-8141-481d379719ab"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField-9" positionType="Float" x="4" y="49" width="546" height="15" uuid="41abf647-e15d-4039-b893-ff5dce449aa6"/>
					<box topPadding="1" leftPadding="2" bottomPadding="2" rightPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{inscricoesImobiliarias}]]></textFieldExpression>
				</textField>
			</elementGroup>
			<elementGroup>
				<rectangle>
					<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="2" y="0" width="550" height="32" isPrintWhenDetailOverflows="true" uuid="fd8bb9f9-8c3e-477b-bc48-96904a5d87fc"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField-9" isPrintRepeatedValues="false" x="4" y="17" width="546" height="15" uuid="1d414da6-a28c-478f-aac8-61d39bd5698e"/>
					<box topPadding="1" leftPadding="2" bottomPadding="2" rightPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{descricaoAtividadePrincipal}]]></textFieldExpression>
				</textField>
			</elementGroup>
		</band>
		<band height="32">
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="2" y="0" width="550" height="32" isPrintWhenDetailOverflows="true" uuid="db3e0c20-3aac-4dd1-badd-35551453bedf"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-9" x="4" y="17" width="546" height="15" uuid="2bf80666-7c01-483f-bc61-0df7d91760f1"/>
				<box topPadding="1" leftPadding="2" bottomPadding="2" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Top" markup="html">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{parecer}.getTipoConcessao() != null ?
"Tipo de Aprovação: " + $F{parecer}.getTipoConcessaoFormatado() + "\n" + $F{parecer}.getDescricaoParecer()
: $F{parecer}.getDescricaoParecer()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="4" y="2" width="546" height="14" uuid="6c63f116-0c9b-4dd2-a2b9-fd9ed8c8add9"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_parecer_observacao").toUpperCase()]]></textFieldExpression>
			</textField>
		</band>
		<band height="34">
			<printWhenExpression><![CDATA[$P{observacaoDestaque} != null]]></printWhenExpression>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-9" x="4" y="0" width="546" height="34" uuid="c2fddbe9-1d56-465e-b384-34ea4767670c"/>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{observacaoDestaque}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="2" y="0" width="550" height="34" isPrintWhenDetailOverflows="true" uuid="0b2646af-3a3c-483a-b7c1-5d484ce16458"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
		</band>
		<band height="110" splitType="Immediate">
			<printWhenExpression><![CDATA[VigilanciaHelper.exibirLinhaAssinatura()]]></printWhenExpression>
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="2" y="31" width="550" height="79" isPrintWhenDetailOverflows="true" uuid="77fad0ed-9546-4bcb-8d2a-45acb7877719"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="4" y="34" width="284" height="14"
                               isPrintWhenDetailOverflows="true" uuid="eadeaef1-d8d4-48ef-a5e7-16f60ad71143">
                    <printWhenExpression><![CDATA[$P{gerarDocumentoComAssinaturaFiscal}]]></printWhenExpression>
                </reportElement>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["AUTORIDADES SANITÁRIAS RESPONSÁVEIS PELA ANÁLISE"]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement positionType="Float" mode="Transparent" x="2" y="0" width="550" height="31" isPrintWhenDetailOverflows="true" uuid="0c94812c-d694-42b3-9287-cf8f074dcb4b"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="2" width="546" height="14" uuid="c4713c45-621b-4f16-a809-3c95af4585b7"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_local_data").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="17" width="546" height="14" uuid="d8714513-cb93-4b88-a55b-e96cd1b1e376"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{cidade} + ", " + Data.formatar(DataUtil.getDataAtual())]]></textFieldExpression>
			</textField>
			<componentElement>
                <reportElement x="7" y="49" width="540" height="61" isRemoveLineWhenBlank="true"
                               uuid="a9ccd24d-8820-4bc3-99d9-5d7a8046258c">
                    <printWhenExpression><![CDATA[$P{gerarDocumentoComAssinaturaFiscal}]]></printWhenExpression>
                </reportElement>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Horizontal">
					<datasetRun subDataset="fiscais" uuid="a3b31889-2ee9-40f8-9049-689b211fb9e4">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource( $F{fiscais} )]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="61" width="270">
						<line>
							<reportElement x="6" y="33" width="262" height="1" uuid="1778bdc2-dfbe-49ef-b4f5-49d21c56a17b"/>
							<graphicElement>
								<pen lineWidth="0.5"/>
							</graphicElement>
						</line>
						<textField isBlankWhenNull="true">
							<reportElement mode="Transparent" x="6" y="33" width="262" height="15" isPrintWhenDetailOverflows="true" uuid="e9499af3-3cfb-439e-b187-81cc6e0165d3"/>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{nome}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement stretchType="RelativeToTallestObject" x="6" y="49" width="262" height="12" uuid="166ed7ea-c6b0-4530-950e-ad7ff17e3104"/>
							<box topPadding="1" leftPadding="0" bottomPadding="0" rightPadding="0">
								<topPen lineWidth="0.0"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Top">
								<font fontName="Arial" size="8" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{referenciaRegistroFormatado}]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
		</band>
		<band height="83" splitType="Immediate">
			<printWhenExpression><![CDATA[!VigilanciaHelper.exibirLinhaAssinatura()]]></printWhenExpression>
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="2" y="31" width="550" height="52" isPrintWhenDetailOverflows="true" uuid="77fad0ed-9546-4bcb-8d2a-45acb7877719"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
                <reportElement key="textField-9" positionType="Float" x="4" y="34" width="284" height="14"
                               isPrintWhenDetailOverflows="true" uuid="eadeaef1-d8d4-48ef-a5e7-16f60ad71143">
                    <printWhenExpression><![CDATA[$P{gerarDocumentoComAssinaturaFiscal}]]></printWhenExpression>
                </reportElement>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["AUTORIDADES SANITÁRIAS RESPONSÁVEIS PELA ANÁLISE"]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement positionType="Float" mode="Transparent" x="2" y="0" width="550" height="31" isPrintWhenDetailOverflows="true" uuid="0c94812c-d694-42b3-9287-cf8f074dcb4b"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="2" width="546" height="14" uuid="c4713c45-621b-4f16-a809-3c95af4585b7"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_local_data").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="17" width="546" height="14" uuid="d8714513-cb93-4b88-a55b-e96cd1b1e376"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{cidade} + ", " + Data.formatar(DataUtil.getDataAtual())]]></textFieldExpression>
			</textField>
			<componentElement>
                <reportElement x="7" y="50" width="540" height="33" isRemoveLineWhenBlank="true"
                               uuid="a9ccd24d-8820-4bc3-99d9-5d7a8046258c">
                    <printWhenExpression><![CDATA[$P{gerarDocumentoComAssinaturaFiscal}]]></printWhenExpression>
                </reportElement>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Horizontal">
					<datasetRun subDataset="fiscais" uuid="a3b31889-2ee9-40f8-9049-689b211fb9e4">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource( $F{fiscais} )]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="33" width="270">
						<textField isBlankWhenNull="true">
							<reportElement mode="Transparent" x="6" y="2" width="262" height="15" isPrintWhenDetailOverflows="true" uuid="e9499af3-3cfb-439e-b187-81cc6e0165d3"/>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{nome}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement stretchType="RelativeToTallestObject" x="6" y="17" width="262" height="12" uuid="166ed7ea-c6b0-4530-950e-ad7ff17e3104"/>
							<box topPadding="1" leftPadding="0" bottomPadding="0" rightPadding="0">
								<topPen lineWidth="0.0"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Top">
								<font fontName="Arial" size="8" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{referenciaRegistroFormatado}]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
		</band>
		<band height="126" splitType="Prevent">
			<line>
				<reportElement x="300" y="90" width="10" height="1" uuid="ce05fe7b-def4-4d03-96c4-6575511399f8"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="310" y="81" width="1" height="10" uuid="e5af0489-7006-4053-836e-4b9832f01f1c"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="243" y="81" width="1" height="10" uuid="f6b0172d-cd03-49e2-8416-bbc3e4527aeb"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</line>
			<image scaleImage="RealSize">
				<reportElement x="247" y="27" width="60" height="60" uuid="80243f8c-cf15-45d4-ad5f-d8b1c9c4ca3d"/>
				<imageExpression><![CDATA[com.google.zxing.client.j2se.MatrixToImageWriter.toBufferedImage(
    new com.google.zxing.qrcode.QRCodeWriter().encode(
            $P{urlQRcode},
            com.google.zxing.BarcodeFormat.QR_CODE, 600, 600))]]></imageExpression>
			</image>
			<line>
				<reportElement x="243" y="23" width="10" height="1" uuid="d950566c-8d56-4bb0-b7a0-e731cef5c544"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</line>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="178" y="94" width="199" height="32" uuid="d19511cb-791d-43e2-8770-dec754a65971"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Acompanhe a situação do requerimento direto do seu dispositivo móvel através do QRcode acima"]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="310" y="23" width="1" height="10" uuid="2ba2fa87-2d1d-4ebf-9bb9-6f63491e5401"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="300" y="23" width="10" height="1" uuid="9141e782-bf60-42fa-bcb8-9ee01b41ee8b"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="243" y="23" width="1" height="10" uuid="20cdb333-3231-4aae-8f57-0e7c8b76a5a4"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="244" y="90" width="10" height="1" uuid="2c9c5b77-de88-4deb-a9a7-9dce6152535e"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="3" width="547" height="14" uuid="42de90cd-7c4c-4515-a0fc-a3e062eed461"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_protocolo").toUpperCase() + ": " +
$F{requerimento}.getProtocoloFormatado()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
