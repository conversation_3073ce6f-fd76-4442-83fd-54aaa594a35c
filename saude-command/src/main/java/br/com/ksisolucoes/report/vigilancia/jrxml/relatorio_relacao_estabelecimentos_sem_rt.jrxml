<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_estabelecimentos_sem_rt" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="9155b179-bb03-426d-a648-bc310bac77b6">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.1961500000000145"/>
	<property name="ireport.x" value="914"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioRequerimentoDTOParam.FormaApresentacao"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="LABEL_ATIVIDADE" class="java.lang.String"/>
	<field name="estabelecimento" class="br.com.ksisolucoes.vo.vigilancia.Estabelecimento"/>
	<field name="descricaoAtividadePrincipal" class="java.lang.String"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="21" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-35" mode="Transparent" x="0" y="6" width="257" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="d6cac8f7-7f34-49d5-914a-98848eeb1a3c"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Bottom" rotation="None">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_estabelecimento")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="19" width="802" height="1" uuid="aea060cb-9f73-42da-a184-43e78ff6256f"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-35" mode="Transparent" x="260" y="6" width="410" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="d6cac8f7-7f34-49d5-914a-98848eeb1a3c"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Bottom" rotation="None">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_estabelecimento")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-35" mode="Transparent" x="675" y="6" width="127" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="d6cac8f7-7f34-49d5-914a-98848eeb1a3c"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Bottom" rotation="None">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{LABEL_ATIVIDADE}]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="1" width="257" height="10" uuid="4e5f86a4-3c18-4686-9e74-e135c6665185"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{estabelecimento}.getRazaoSocial()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="260" y="1" width="410" height="10" uuid="4e5f86a4-3c18-4686-9e74-e135c6665185"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{estabelecimento}.getVigilanciaEndereco().getEnderecoFormatadoComCidade()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="675" y="1" width="127" height="10" uuid="4e5f86a4-3c18-4686-9e74-e135c6665185"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoAtividadePrincipal}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
