package br.com.ksisolucoes.report.vigilancia;

import br.com.celk.report.vigilancia.query.QueryFichaHepatiteViral;
import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FichaInvestigacaoAgravoDTOParam;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;

public class ImpressaoFichaInvestigacaoAgravoHepatiteViral extends AbstractReport<FichaInvestigacaoAgravoDTOParam> {

    private QueryFichaHepatiteViral query;

    public ImpressaoFichaInvestigacaoAgravoHepatiteViral(FichaInvestigacaoAgravoDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        if(query == null){
            query = new QueryFichaHepatiteViral();
        }
        return query;
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/vigilancia/pdf/ficha_investigacao_hepatite_viral.pdf";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_hepatite_viral");
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return param.getTipoArquivo();
    }
}
