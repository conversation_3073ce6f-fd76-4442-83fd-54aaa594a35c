<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_visita_armadilha" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="9155b179-bb03-426d-a648-bc310bac77b6">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.815000000000027"/>
	<property name="ireport.x" value="405"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.report.vigilancia.dengue.armadilhavisita.dto.RelatorioVisitaArmadilhaDTOParam.FormaApresentacao"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="formaApresentacao" class="br.com.ksisolucoes.report.vigilancia.dengue.armadilhavisita.dto.RelatorioVisitaArmadilhaDTOParam.FormaApresentacao"/>
	<field name="dav" class="br.com.ksisolucoes.vo.vigilancia.dengue.DengueArmadilhaVisita"/>
	<field name="area" class="br.com.ksisolucoes.vo.vigilancia.dengue.DengueAreaVigilancia"/>
	<field name="microArea" class="br.com.ksisolucoes.vo.vigilancia.dengue.DengueMicroAreaVigilancia"/>
	<field name="localidade" class="br.com.ksisolucoes.vo.vigilancia.dengue.DengueLocalidade"/>
	<field name="tipo" class="java.lang.String"/>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<field name="coletaDescricao" class="java.lang.String"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="qtdeTotalFA" class="java.lang.Long" resetType="Group" resetGroup="FA" calculation="Count">
		<variableExpression><![CDATA[$F{dav}]]></variableExpression>
	</variable>
	<variable name="qtdeTotalGeral" class="java.lang.Long" calculation="Count">
		<variableExpression><![CDATA[$F{dav}]]></variableExpression>
	</variable>
	<group name="GERAL">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupFooter>
			<band height="13">
				<line>
					<reportElement x="726" y="1" width="76" height="1" uuid="e3c43a7a-bafd-45ca-939b-12bed2e2584d"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="719" y="2" width="50" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="d5fbc985-f23a-47e5-a5bd-b7a0c0b1b1f8"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Total Geral*/
$V{BUNDLE}.getStringApplication("rotulo_total_geral") + ": "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="771" y="1" width="28" height="12" uuid="8496eb47-b113-4bcc-9ca5-5860cbc0dbd7"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{qtdeTotalGeral}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="FA" isReprintHeaderOnEachPage="true" keepTogether="true">
		<groupExpression><![CDATA[FormaApresentacao.DATA_VISITA.equals($P{formaApresentacao})
    ?
        Bundle.getStringApplication("rotulo_data_visita") + ": " + Data.formatar($F{dav}.getDataVisita())
    :
        FormaApresentacao.PROFISSIONAL.equals($P{formaApresentacao})
        ?
            Bundle.getStringApplication("rotulo_profissional") + ": " + $F{profissional}.getNome()
        :
            FormaApresentacao.LOCALIDADE.equals($P{formaApresentacao})
                ?
                    Bundle.getStringApplication("rotulo_localidade") + ": " + $F{localidade}.getLocalidade()
                    :
            FormaApresentacao.ARMADILHA.equals($P{formaApresentacao})
                ?
                    Bundle.getStringApplication("rotulo_armadilha") + ": " + $F{dav}.getDengueArmadilha().getDescricao()
:
            null]]></groupExpression>
		<groupHeader>
			<band height="13" splitType="Stretch">
				<printWhenExpression><![CDATA[!FormaApresentacao.GERAL.equals($P{formaApresentacao})]]></printWhenExpression>
				<textField evaluationTime="Group" evaluationGroup="FA" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-1" x="1" y="0" width="802" height="13" uuid="b9688033-a55f-4fb7-894e-a2be7b80d264"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[FormaApresentacao.DATA_VISITA.equals($P{formaApresentacao})
    ?
        Bundle.getStringApplication("rotulo_data_visita") + ": " + Data.formatar($F{dav}.getDataVisita())
    :
        FormaApresentacao.PROFISSIONAL.equals($P{formaApresentacao})
        ?
            Bundle.getStringApplication("rotulo_profissional") + ": " + $F{profissional}.getNome()
        :
            FormaApresentacao.LOCALIDADE.equals($P{formaApresentacao})
                ?
                    Bundle.getStringApplication("rotulo_localidade") + ": " + $F{localidade}.getLocalidade()
                    :
            FormaApresentacao.ARMADILHA.equals($P{formaApresentacao})
                ?
                    Bundle.getStringApplication("rotulo_armadilha") + ": " + $F{dav}.getDengueArmadilha().getDescricao()
:
            null]]></textFieldExpression>
				</textField>
			</band>
			<band height="16">
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="2" y="3" width="65" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="d6cac8f7-7f34-49d5-914a-98848eeb1a3c"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Dt. Visita*/
$V{BUNDLE}.getStringApplication("rotulo_visita_abv")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="14" width="802" height="1" uuid="aea060cb-9f73-42da-a184-43e78ff6256f"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="334" y="3" width="93" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="5e791dba-cf0a-40fa-9994-6239cb54fe4f"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Tipo de Imóvel*/
$V{BUNDLE}.getStringApplication("rotulo_tipo_imovel")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="68" y="3" width="166" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="44e08b52-484b-4f37-b95e-e1ede7c7c1a2"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Profissional*/
$V{BUNDLE}.getStringApplication("rotulo_profissional")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="427" y="3" width="103" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="04ccb654-c941-4ec3-9e4a-984230f74baf"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Localidade*/
$V{BUNDLE}.getStringApplication("rotulo_localidade")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="237" y="3" width="93" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="c684d54e-3beb-433d-946e-2ccbc5c61781"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Armadilha*/
$V{BUNDLE}.getStringApplication("rotulo_armadilha")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="744" y="3" width="56" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="1b3b3fe3-14d4-4d96-b702-5a7a86fa529f"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Bottom" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Coleta*/
$V{BUNDLE}.getStringApplication("rotulo_coleta")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="530" y="3" width="103" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="db4bcc86-b15e-4542-acb0-0d17cef32dbb"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Área*/
$V{BUNDLE}.getStringApplication("rotulo_area")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="633" y="3" width="103" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="4f806059-06ad-4900-8af2-f7659f435da2"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*MicroArea*/
$V{BUNDLE}.getStringApplication("rotulo_micro_area")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="19">
				<printWhenExpression><![CDATA[(!FormaApresentacao.GERAL.equals($P{formaApresentacao}))]]></printWhenExpression>
				<line>
					<reportElement x="744" y="1" width="58" height="1" uuid="d5bdde4e-1457-4e0a-a52d-3bab9153f29f"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="744" y="3" width="25" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="bc6d87e7-c68b-4a1f-a2e1-e450daf94550"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Total*/
$V{BUNDLE}.getStringApplication("rotulo_total") + ": "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="770" y="2" width="28" height="12" uuid="53669052-a6cb-49b1-a484-5c092155227b"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{qtdeTotalFA}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="12" splitType="Stretch">
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="2" y="0" width="65" height="12" uuid="94c77f3b-ede0-42bb-8e09-d361ff47a170"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dav}.getDataVisita()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="68" y="0" width="166" height="12" uuid="07a3ba1b-c9e1-457c-8737-f33e476028fb"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{profissional}.getNome()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="334" y="0" width="93" height="12" uuid="b68fafbf-46b0-49a1-9157-252255cc8f96"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tipo}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="427" y="0" width="103" height="12" uuid="e31e40ba-a24b-4efd-831d-7bd274de440f"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{localidade}.getLocalidade()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="237" y="0" width="93" height="12" uuid="ec0753ce-db96-4612-8cff-df0de0b2140d"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dav}.getDengueArmadilha().getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="744" y="0" width="56" height="12" uuid="778cc767-bf38-46d6-8004-8fe4276a3292"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{coletaDescricao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="530" y="0" width="103" height="12" uuid="6a478f28-94b1-4e3c-9a73-d668137786ae"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{area}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="633" y="0" width="103" height="12" uuid="a862c951-d77d-417e-b4a1-6286617b70d4"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{microArea}.getDescricao()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
