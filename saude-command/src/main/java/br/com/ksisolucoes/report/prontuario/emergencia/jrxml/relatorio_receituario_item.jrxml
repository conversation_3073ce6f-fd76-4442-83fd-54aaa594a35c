<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_receituario_item" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="177911e4-d904-4868-ab85-22e162f3998f">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.357947691000002"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.TipoReceita"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="caminhoImagem" class="java.lang.String" isForPrompting="false"/>
	<parameter name="EXIBIR_CABECALHO" class="java.lang.Boolean"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["/home/<USER>/projetos/saude-2/saude-command/src/main/java/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/"]]></defaultValueExpression>
	</parameter>
	<parameter name="DESC_LINHA_1" class="java.lang.String"/>
	<parameter name="CABECALHO_ADICIONAL_1" class="java.lang.String"/>
	<parameter name="CABECALHO_ADICIONAL_2" class="java.lang.String"/>
	<parameter name="CABECALHO_DIRETOR_TECNICO" class="java.lang.String"/>
	<parameter name="UNIDADE_ATENDIMENTO" class="java.lang.String"/>
	<parameter name="TITULO_REPORT" class="java.lang.String"/>
	<parameter name="CAMINHO_IMAGEM_PADRAO" class="java.lang.String"/>
	<parameter name="BAIRRO_UNIDADE" class="java.lang.String"/>
	<parameter name="RUA_UNIDADE" class="java.lang.String"/>
	<parameter name="CIDADE_UNIDADE" class="java.lang.String"/>
	<parameter name="UF_UNIDADE" class="java.lang.String"/>
	<parameter name="CEP_UNIDADE" class="java.lang.String"/>
	<parameter name="FONE_UNIDADE" class="java.lang.String"/>
	<parameter name="EXIBIR_RODAPE" class="java.lang.Boolean"/>
	<parameter name="NUMERO_UNIDADE" class="java.lang.String"/>
	<parameter name="USUARIO_LOGADO" class="java.lang.String"/>
	<parameter name="EXIBIR_HORARIO" class="java.lang.Boolean"/>
	<parameter name="DESC_CABECALHO_PADRAO" class="java.lang.String"/>
	<parameter name="TIPO_RECEITA" class="java.lang.String"/>
	<parameter name="TITULO_FORMULARIO_MEDICAMENTO_NAO_PADRONIZADO" class="java.lang.String"/>
	<parameter name="identificarMedicamentoNaoConstaSecretaria" class="java.lang.Boolean"/>
	<parameter name="validadeReceitaBasica" class="java.lang.Long"/>
	<parameter name="VERSAO_SISTEMA" class="java.lang.String"/>
	<parameter name="SISTEMA" class="java.lang.String"/>
	<parameter name="EXIBIR_DIRETOR_TECNICO" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[false]]></defaultValueExpression>
	</parameter>
	<parameter name="exibeValidadeReceita" class="java.lang.String"/>
	<parameter name="urlQRCode" class="java.lang.String"/>
	<parameter name="ASSINADO_DIGITALMENTE" class="java.lang.Boolean"/>
	<parameter name="TOKEN" class="java.lang.String"/>
	<parameter name="DATA_ASSINATURA" class="java.lang.String"/>
	<field name="receituarioList" class="java.util.List"/>
	<field name="relatorioReceituarioItemNaoPadronizadoDTOList" class="java.util.List"/>
	<group name="receituario" isStartNewPage="true">
		<groupHeader>
			<band height="10" splitType="Stretch">
				<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{receituarioList}) && !TipoReceita.RECEITA_BRANCA.equals($P{TIPO_RECEITA}) && !TipoReceita.RECEITA_PRESCRICAO_OCULOS.equals($P{TIPO_RECEITA})]]></printWhenExpression>
				<subreport runToBottom="true">
					<reportElement x="0" y="0" width="595" height="10" uuid="55795548-9a0e-4cbd-b7bf-8ab07f5b1f1b">
						<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{receituarioList}) && !TipoReceita.RECEITA_BRANCA.equals($P{TIPO_RECEITA}) && !TipoReceita.RECEITA_PRESCRICAO_OCULOS.equals($P{TIPO_RECEITA})
&& !$P{ASSINADO_DIGITALMENTE}]]></printWhenExpression>
					</reportElement>
					<subreportParameter name="CAMINHO_IMAGEM_PADRAO">
						<subreportParameterExpression><![CDATA[$P{CAMINHO_IMAGEM_PADRAO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="EXIBIR_HORARIO">
						<subreportParameterExpression><![CDATA[$P{EXIBIR_HORARIO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="CEP_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{CEP_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="DESC_LINHA_1">
						<subreportParameterExpression><![CDATA[$P{DESC_LINHA_1}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="VERSAO_SISTEMA">
						<subreportParameterExpression><![CDATA[$P{VERSAO_SISTEMA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="EXIBIR_RODAPE">
						<subreportParameterExpression><![CDATA[$P{EXIBIR_RODAPE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="CABECALHO_ADICIONAL_1">
						<subreportParameterExpression><![CDATA[$P{CABECALHO_ADICIONAL_1}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="CABECALHO_ADICIONAL_2">
						<subreportParameterExpression><![CDATA[$P{CABECALHO_ADICIONAL_2}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="SISTEMA">
						<subreportParameterExpression><![CDATA[$P{SISTEMA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="CIDADE_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{CIDADE_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="UF_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{UF_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="NUMERO_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{NUMERO_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="identificarMedicamentoNaoConstaSecretaria">
						<subreportParameterExpression><![CDATA[$P{identificarMedicamentoNaoConstaSecretaria}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="BAIRRO_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{BAIRRO_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="exibeValidadeReceita">
						<subreportParameterExpression><![CDATA[$P{exibeValidadeReceita}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="caminhoImagem">
						<subreportParameterExpression><![CDATA[$P{caminhoImagem}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="DESC_CABECALHO_PADRAO">
						<subreportParameterExpression><![CDATA[$P{DESC_CABECALHO_PADRAO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="validadeReceitaBasica">
						<subreportParameterExpression><![CDATA[$P{validadeReceitaBasica}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="TITULO_REPORT">
						<subreportParameterExpression><![CDATA[$P{TITULO_REPORT}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="urlQRCode">
						<subreportParameterExpression><![CDATA[$P{urlQRCode}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="CABECALHO_DIRETOR_TECNICO">
						<subreportParameterExpression><![CDATA[$P{CABECALHO_DIRETOR_TECNICO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="RUA_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{RUA_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="FONE_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{FONE_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="EXIBIR_CABECALHO">
						<subreportParameterExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="EXIBIR_DIRETOR_TECNICO">
						<subreportParameterExpression><![CDATA[$P{EXIBIR_DIRETOR_TECNICO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="UNIDADE_ATENDIMENTO">
						<subreportParameterExpression><![CDATA[$P{UNIDADE_ATENDIMENTO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="USUARIO_LOGADO">
						<subreportParameterExpression><![CDATA[$P{USUARIO_LOGADO}]]></subreportParameterExpression>
					</subreportParameter>
					<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{receituarioList})]]></dataSourceExpression>
					<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/relatorio_receituario.jasper"]]></subreportExpression>
				</subreport>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="10">
				<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{receituarioList}) && !TipoReceita.RECEITA_BRANCA.equals($P{TIPO_RECEITA}) && !TipoReceita.RECEITA_PRESCRICAO_OCULOS.equals($P{TIPO_RECEITA})
 && $P{ASSINADO_DIGITALMENTE}]]></printWhenExpression>
				<subreport runToBottom="true">
					<reportElement x="0" y="0" width="595" height="10" uuid="aeaf56e0-c479-409a-89ae-19a20f2bbf2c">
						<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{receituarioList}) && !TipoReceita.RECEITA_BRANCA.equals($P{TIPO_RECEITA}) && !TipoReceita.RECEITA_PRESCRICAO_OCULOS.equals($P{TIPO_RECEITA})
 && $P{ASSINADO_DIGITALMENTE}]]></printWhenExpression>
					</reportElement>
					<subreportParameter name="CAMINHO_IMAGEM_PADRAO">
						<subreportParameterExpression><![CDATA[$P{CAMINHO_IMAGEM_PADRAO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="EXIBIR_HORARIO">
						<subreportParameterExpression><![CDATA[$P{EXIBIR_HORARIO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="DESC_LINHA_1">
						<subreportParameterExpression><![CDATA[$P{DESC_LINHA_1}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="CEP_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{CEP_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="VERSAO_SISTEMA">
						<subreportParameterExpression><![CDATA[$P{VERSAO_SISTEMA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="EXIBIR_RODAPE">
						<subreportParameterExpression><![CDATA[$P{EXIBIR_RODAPE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="CABECALHO_ADICIONAL_1">
						<subreportParameterExpression><![CDATA[$P{CABECALHO_ADICIONAL_1}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="CABECALHO_ADICIONAL_2">
						<subreportParameterExpression><![CDATA[$P{CABECALHO_ADICIONAL_2}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="CIDADE_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{CIDADE_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="UF_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{UF_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="NUMERO_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{NUMERO_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="TITULO_REPORT">
						<subreportParameterExpression><![CDATA[$P{TITULO_FORMULARIO_MEDICAMENTO_NAO_PADRONIZADO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="CABECALHO_DIRETOR_TECNICO">
						<subreportParameterExpression><![CDATA[$P{CABECALHO_DIRETOR_TECNICO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="FONE_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{FONE_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="EXIBIR_CABECALHO">
						<subreportParameterExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="EXIBIR_DIRETOR_TECNICO">
						<subreportParameterExpression><![CDATA[$P{EXIBIR_DIRETOR_TECNICO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="USUARIO_LOGADO">
						<subreportParameterExpression><![CDATA[$P{USUARIO_LOGADO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="SISTEMA">
						<subreportParameterExpression><![CDATA[$P{SISTEMA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="BAIRRO_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{BAIRRO_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="DESC_CABECALHO_PADRAO">
						<subreportParameterExpression><![CDATA[$P{DESC_CABECALHO_PADRAO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="RUA_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{RUA_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="UNIDADE_ATENDIMENTO">
						<subreportParameterExpression><![CDATA[$P{UNIDADE_ATENDIMENTO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="TOKEN">
						<subreportParameterExpression><![CDATA[$P{TOKEN}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="urlQRCode">
						<subreportParameterExpression><![CDATA[$P{urlQRCode}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="DATA_ASSINATURA">
						<subreportParameterExpression><![CDATA[$P{DATA_ASSINATURA}]]></subreportParameterExpression>
					</subreportParameter>
					<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{receituarioList})]]></dataSourceExpression>
					<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/relatorio_receituario_branca_assinatura_digital.jasper"]]></subreportExpression>
				</subreport>
			</band>
		</groupFooter>
	</group>
	<group name="receituario_branca" isStartNewPage="true">
		<groupFooter>
			<band height="10">
				<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{receituarioList}) && (TipoReceita.RECEITA_BRANCA.equals($P{TIPO_RECEITA}) || TipoReceita.RECEITA_ANTIMICROBIANA.equals($P{TIPO_RECEITA}))
&& $P{ASSINADO_DIGITALMENTE}]]></printWhenExpression>
				<subreport runToBottom="true">
					<reportElement x="0" y="0" width="595" height="10" uuid="cca1cca9-4aaa-43da-9afc-dea437afc188">
						<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{receituarioList}) && (TipoReceita.RECEITA_BRANCA.equals($P{TIPO_RECEITA}) || TipoReceita.RECEITA_ANTIMICROBIANA.equals($P{TIPO_RECEITA}))
&& $P{ASSINADO_DIGITALMENTE}]]></printWhenExpression>
					</reportElement>
					<subreportParameter name="CAMINHO_IMAGEM_PADRAO">
						<subreportParameterExpression><![CDATA[$P{CAMINHO_IMAGEM_PADRAO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="EXIBIR_HORARIO">
						<subreportParameterExpression><![CDATA[$P{EXIBIR_HORARIO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="DESC_LINHA_1">
						<subreportParameterExpression><![CDATA[$P{DESC_LINHA_1}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="CEP_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{CEP_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="VERSAO_SISTEMA">
						<subreportParameterExpression><![CDATA[$P{VERSAO_SISTEMA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="EXIBIR_RODAPE">
						<subreportParameterExpression><![CDATA[$P{EXIBIR_RODAPE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="CABECALHO_ADICIONAL_1">
						<subreportParameterExpression><![CDATA[$P{CABECALHO_ADICIONAL_1}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="CABECALHO_ADICIONAL_2">
						<subreportParameterExpression><![CDATA[$P{CABECALHO_ADICIONAL_2}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="CIDADE_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{CIDADE_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="UF_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{UF_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="NUMERO_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{NUMERO_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="TITULO_REPORT">
						<subreportParameterExpression><![CDATA[$P{TITULO_FORMULARIO_MEDICAMENTO_NAO_PADRONIZADO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="CABECALHO_DIRETOR_TECNICO">
						<subreportParameterExpression><![CDATA[$P{CABECALHO_DIRETOR_TECNICO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="FONE_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{FONE_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="EXIBIR_CABECALHO">
						<subreportParameterExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="EXIBIR_DIRETOR_TECNICO">
						<subreportParameterExpression><![CDATA[$P{EXIBIR_DIRETOR_TECNICO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="USUARIO_LOGADO">
						<subreportParameterExpression><![CDATA[$P{USUARIO_LOGADO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="SISTEMA">
						<subreportParameterExpression><![CDATA[$P{SISTEMA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="BAIRRO_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{BAIRRO_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="DESC_CABECALHO_PADRAO">
						<subreportParameterExpression><![CDATA[$P{DESC_CABECALHO_PADRAO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="RUA_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{RUA_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="UNIDADE_ATENDIMENTO">
						<subreportParameterExpression><![CDATA[$P{UNIDADE_ATENDIMENTO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="TOKEN">
						<subreportParameterExpression><![CDATA[$P{TOKEN}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="urlQRCode">
						<subreportParameterExpression><![CDATA[$P{urlQRCode}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="DATA_ASSINATURA">
						<subreportParameterExpression><![CDATA[$P{DATA_ASSINATURA}]]></subreportParameterExpression>
					</subreportParameter>
					<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{receituarioList})]]></dataSourceExpression>
					<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/relatorio_receituario_basica_assinatura_digital_v2.jasper"]]></subreportExpression>
				</subreport>
			</band>
		</groupFooter>
	</group>
	<group name="receituario_prescricao_oculos"/>
	<group name="receituario_nao_padronizado" isStartNewPage="true">
		<groupHeader>
			<band height="10">
				<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{relatorioReceituarioItemNaoPadronizadoDTOList})]]></printWhenExpression>
				<subreport runToBottom="true">
					<reportElement x="0" y="0" width="595" height="10" uuid="55795548-9a0e-4cbd-b7bf-8ab07f5b1f1b">
						<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{relatorioReceituarioItemNaoPadronizadoDTOList})
&& !$P{ASSINADO_DIGITALMENTE}]]></printWhenExpression>
					</reportElement>
					<subreportParameter name="CAMINHO_IMAGEM_PADRAO">
						<subreportParameterExpression><![CDATA[$P{CAMINHO_IMAGEM_PADRAO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="EXIBIR_HORARIO">
						<subreportParameterExpression><![CDATA[$P{EXIBIR_HORARIO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="DESC_LINHA_1">
						<subreportParameterExpression><![CDATA[$P{DESC_LINHA_1}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="CEP_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{CEP_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="VERSAO_SISTEMA">
						<subreportParameterExpression><![CDATA[$P{VERSAO_SISTEMA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="EXIBIR_RODAPE">
						<subreportParameterExpression><![CDATA[$P{EXIBIR_RODAPE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="CABECALHO_ADICIONAL_1">
						<subreportParameterExpression><![CDATA[$P{CABECALHO_ADICIONAL_1}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="CABECALHO_ADICIONAL_2">
						<subreportParameterExpression><![CDATA[$P{CABECALHO_ADICIONAL_2}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="CIDADE_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{CIDADE_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="UF_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{UF_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="NUMERO_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{NUMERO_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="TITULO_REPORT">
						<subreportParameterExpression><![CDATA[$P{TITULO_FORMULARIO_MEDICAMENTO_NAO_PADRONIZADO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="CABECALHO_DIRETOR_TECNICO">
						<subreportParameterExpression><![CDATA[$P{CABECALHO_DIRETOR_TECNICO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="FONE_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{FONE_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="EXIBIR_CABECALHO">
						<subreportParameterExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="EXIBIR_DIRETOR_TECNICO">
						<subreportParameterExpression><![CDATA[$P{EXIBIR_DIRETOR_TECNICO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="USUARIO_LOGADO">
						<subreportParameterExpression><![CDATA[$P{USUARIO_LOGADO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="SISTEMA">
						<subreportParameterExpression><![CDATA[$P{SISTEMA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="BAIRRO_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{BAIRRO_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="DESC_CABECALHO_PADRAO">
						<subreportParameterExpression><![CDATA[$P{DESC_CABECALHO_PADRAO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="RUA_UNIDADE">
						<subreportParameterExpression><![CDATA[$P{RUA_UNIDADE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="UNIDADE_ATENDIMENTO">
						<subreportParameterExpression><![CDATA[$P{UNIDADE_ATENDIMENTO}]]></subreportParameterExpression>
					</subreportParameter>
					<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{relatorioReceituarioItemNaoPadronizadoDTOList})]]></dataSourceExpression>
					<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/sub_relatorio_formulario_aquisicao_medicamentos_nao_padronizados.jasper"]]></subreportExpression>
				</subreport>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
</jasperReport>
