package br.com.ksisolucoes.report.vacina;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.vacina.dto.RelatorioPedidoInsumoDTOParam;
import br.com.ksisolucoes.report.vacina.query.QueryRelatorioPedidoInsumo;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioPedidoInsumo extends AbstractReport<RelatorioPedidoInsumoDTOParam> {

    public RelatorioPedidoInsumo(RelatorioPedidoInsumoDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/vacina/jrxml/relatorio_pedido_insumo.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relatorio_pedidos_insumo");
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioPedidoInsumo();
    }

}
