/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.agendamento;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.agendamento.dto.RelatorioDistribuicaoCotasPpiDTOParam;
import br.com.ksisolucoes.report.agendamento.query.QueryRelatorioDistribuicaoCotasPpi;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

/**
 *
 * <AUTHOR>
 */
public class RelatorioDistribuicaoCotasPpi extends AbstractReport<RelatorioDistribuicaoCotasPpiDTOParam>{

    public RelatorioDistribuicaoCotasPpi(RelatorioDistribuicaoCotasPpiDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        addParametro("formaApresentacao", getParam().getFormaApresentacao());

        return "/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_distribuicao_cotas_ppi.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_distribuicao_cotas_ppi");
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioDistribuicaoCotasPpi();
    }

    @Override
    protected void customDTOParam(RelatorioDistribuicaoCotasPpiDTOParam param) throws ValidacaoException {
        if(param.getPeriodo().getDataInicial() == null || param.getPeriodo().getDataFinal() == null){
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_periodo"));
        }
    }

}
