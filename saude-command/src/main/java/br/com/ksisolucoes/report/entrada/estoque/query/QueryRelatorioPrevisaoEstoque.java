package br.com.ksisolucoes.report.entrada.estoque.query;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioPrevisaoEstoqueDTO;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioPrevisaoEstoqueDTOParam;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento;
import ch.lambdaj.Lambda;

import ch.lambdaj.function.compare.ArgumentComparator;
import ch.lambdaj.group.Group;

import java.util.*;

import org.hibernate.Query;
import org.hibernate.Session;

import static ch.lambdaj.Lambda.*;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioPrevisaoEstoque extends CommandQuery<QueryRelatorioPrevisaoEstoque> implements ITransferDataReport<RelatorioPrevisaoEstoqueDTOParam, RelatorioPrevisaoEstoqueDTO> {

    private RelatorioPrevisaoEstoqueDTOParam param;

    private List<RelatorioPrevisaoEstoqueDTO> listRelatorioResumoConsumoProdutos;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("p.subGrupo.roGrupoProduto.codigo", "codigoGrupo");
        hql.addToSelect("p.subGrupo.roGrupoProduto.descricao", "descricaoGrupo");
        hql.addToSelect("p.subGrupo.id.codigo", "codigoSubGrupo");
        hql.addToSelect("p.subGrupo.descricao", "descricaoSubGrupo");
        hql.addToSelect("p.codigo", "codigoProduto"); // produto
        hql.addToSelect("p.descricao", "descricaoProduto"); // descricao produto
        hql.addToSelect("p.unidade.unidade", "unidadeProduto"); // unidade produto

        String sumQuantidade = "(select (sum(case when me.tipoDocumento.flagTipoMovimento = :isEntrada then "
                + "               (me.quantidade * -1)"
                + "          else"
                + "               me.quantidade"
                + "          end)) from MovimentoEstoque me"
                + " where me.produto = p";

        if (RepositoryComponentDefault.NAO_LONG.equals(param.getAgruparEstabelecimento())) {
            sumQuantidade += " and me.roEmpresa = e";
        }

        sumQuantidade += " and me.tipoDocumento.flagConsumo = :isConsumo"
                + " and me.dataLancamento between :novaData and :novaDataFinal)";

        hql.addToSelect(sumQuantidade + "/" + this.param.getQuantidadeDiasMeses(), "quantidade");

        hql.addToSelect("sum(ee.estoqueFisico)", "estoqueFisico");
        hql.addToSelect("sum(ee.estoqueMinimo)", "estoqueMinimo");

        //soma quantidade média
        hql.setTypeSelect(RelatorioPrevisaoEstoqueDTO.class.getName());
        hql.addToFrom("Produto p, Empresa e, EstoqueEmpresa ee ");
        hql.addToWhereWhithAnd("p.codigo = ee.roProduto.codigo");
        hql.addToWhereWhithAnd("e.codigo = ee.roEmpresa.codigo");

        hql.addToWhereWhithAnd("p.subGrupo.roGrupoProduto = ", this.param.getGrupoProduto());
        if (this.param.getSubGrupo() != null) {
            hql.addToWhereWhithAnd("p.subGrupo.id.codigo = ", this.param.getSubGrupo().getId().getCodigo());
        }
        hql.addToWhereWhithAnd("p = ", this.param.getProduto());
        hql.addToWhereWhithAnd("e = ", this.param.getEstabelecimento());

        if (RepositoryComponentDefault.NAO_LONG.equals(param.getAgruparEstabelecimento())) {
            hql.addToSelect("e.referencia", "codigoEmpresa");
            hql.addToSelect("e.descricao", "descricaoEmpresa");
            hql.addToGroup("e.codigo");
            hql.addToGroup("e.referencia");
            hql.addToOrder("e.descricao");
            hql.addToOrder("e.codigo");
        }

        hql.addToOrder("p.subGrupo.roGrupoProduto.descricao");
        hql.addToOrder("p.subGrupo.roGrupoProduto.codigo");
        hql.addToOrder("p.subGrupo.descricao");
        hql.addToOrder("p.subGrupo.id.codigo");

        if (RelatorioPrevisaoEstoqueDTOParam.Ordenacao.DESCRICAO.value().equals(this.param.getOrdenacao())) {
            hql.addToOrder("p.descricao " + this.param.getTipoOrdenacao().getCommand());
        }

        hql.addToGroup("p.subGrupo.roGrupoProduto.descricao");
        hql.addToGroup("p.subGrupo.roGrupoProduto.codigo");
        hql.addToGroup("p.subGrupo.descricao");
        hql.addToGroup("p.subGrupo.id.codigo");
        hql.addToGroup("p.descricao"); // descricao produto
        hql.addToGroup("p.codigo"); // descricao produto

        hql.addToGroup("p.unidade.unidade");

    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {

        hql.setParameterValue(query, "isConsumo", TipoDocumento.IS_CONSUMO);
        hql.setParameterValue(query, "isEntrada", TipoDocumento.IS_ENTRADA);
        if (RelatorioPrevisaoEstoqueDTOParam.TipoPrevisao.MESES.value().equals(this.param.getTipoPrevisao())) {
            hql.setParameterValue(query, "novaData", Data.removeMeses(DataUtil.getDataAtual(), (this.param.getQuantidadeDiasMeses().intValue())));
            hql.setParameterValue(query, "novaDataFinal", DataUtil.getDataAtual());
        } else if (RelatorioPrevisaoEstoqueDTOParam.TipoPrevisao.DIAS.value().equals(this.param.getTipoPrevisao())) {
            hql.setParameterValue(query, "novaData", Data.removeDias(DataUtil.getDataAtual(), (this.param.getQuantidadeDiasMeses().intValue())));
            hql.setParameterValue(query, "novaDataFinal", DataUtil.getDataAtual());
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result
    ) {
        this.listRelatorioResumoConsumoProdutos = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    public Collection getResult() {
        return listRelatorioResumoConsumoProdutos;
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {

        List<RelatorioPrevisaoEstoqueDTO> itensModificados = new ArrayList<>();

        for (RelatorioPrevisaoEstoqueDTO item : listRelatorioResumoConsumoProdutos) {
            if (item.getQuantidade() == null || item.getQuantidade() <= 0) {
                item.setPrevisao(0.0);
            } else {
                item.setPrevisao(RelatorioPrevisaoEstoqueDTOParam.TipoPrevisao.MESES.value().equals(this.param.getTipoPrevisao())
                        ? item.getEstoqueFisico() / item.getQuantidade()
                        : (item.getEstoqueFisico() / item.getQuantidade()) * 30);
            }

            itensModificados.add(item);
        }

        listRelatorioResumoConsumoProdutos.clear();
        listRelatorioResumoConsumoProdutos.addAll(itensModificados);

        if (RelatorioPrevisaoEstoqueDTOParam.Ordenacao.PREVISAO_ESTOQUE.value().equals(this.param.getOrdenacao())) {
            Comparator<RelatorioPrevisaoEstoqueDTO> comparator = new ArgumentComparator(on(RelatorioPrevisaoEstoqueDTO.class).getPrevisao());

            if (RelatorioPrevisaoEstoqueDTOParam.TipoOrdenacao.DECRESCENTE.equals(this.param.getTipoOrdenacao())) {
                listRelatorioResumoConsumoProdutos.sort(comparator.reversed());
            } else {
                listRelatorioResumoConsumoProdutos.sort(comparator);
            }
        }
    }

    @Override
    public void setDTOParam(RelatorioPrevisaoEstoqueDTOParam arg0
    ) {
        param = arg0;
    }
}
