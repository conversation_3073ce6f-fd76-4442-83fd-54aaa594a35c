package br.com.ksisolucoes.report.prontuario.basico;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.basico.query.QueryRelatorioTransferenciaPaciente;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoTransferenciaSetor;

/**
 *
 * <AUTHOR>
 */
public class RelatorioTransferenciaPaciente extends AbstractReport<AtendimentoTransferenciaSetor>{

    public RelatorioTransferenciaPaciente(AtendimentoTransferenciaSetor param) {
        super(param);
    }
    
    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioTransferenciaPaciente();
    }

    @Override
    public String getXML() {
        if  (param.getFlagOrigem().equals(AtendimentoTransferenciaSetor.ENCAMINHAMENTO)){
            return "/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_encaminhamento_paciente.jrxml";
        }
        else {
            return "/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_transferencia_paciente.jrxml";
        }
    }

    @Override
    public String getTitulo() {
        if  (param.getFlagOrigem().equals(AtendimentoTransferenciaSetor.ENCAMINHAMENTO)){
            return Bundle.getStringApplication("rotulo_comprovante_encaminhamento");
        }
        else {
            return Bundle.getStringApplication("rotulo_comprovante_transferencia");
        }
    }
}