/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.cadsus.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.cadsus.interfaces.dto.RelatorioProfissionalDTO;
import br.com.ksisolucoes.report.cadsus.interfaces.dto.RelatorioProfissionalDTOParam;
import br.com.ksisolucoes.util.Bundle;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioProfissional extends CommandQuery<QueryRelatorioProfissional> implements ITransferDataReport<RelatorioProfissionalDTOParam, RelatorioProfissionalDTO> {

    private RelatorioProfissionalDTOParam param;
    private List<RelatorioProfissionalDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("p.codigo", "profissional.codigo");
        hql.addToSelect("p.referencia", "profissional.referencia");
        hql.addToSelect("p.nome", "profissional.nome");
        hql.addToSelect("p.sexo", "profissional.sexo");
        hql.addToSelect("p.cpf", "profissional.cpf");
        hql.addToSelect("p.codigoCns", "profissional.codigoCns");
        hql.addToSelect("pch.tabelaCbo.cbo", "profissionalCargaHoraria.tabelaCbo.cbo");
        hql.addToSelect("pch.tabelaCbo.descricao", "profissionalCargaHoraria.tabelaCbo.descricao");
        hql.addToSelect("pch.empresa.codigo", "profissionalCargaHoraria.empresa.codigo");
        hql.addToSelect("pch.empresa.referencia", "profissionalCargaHoraria.empresa.referencia");
        hql.addToSelect("pch.empresa.descricao", "profissionalCargaHoraria.empresa.descricao");
        hql.addToSelect("pch.cargaHorariaAmbulatorial", "profissionalCargaHoraria.cargaHorariaAmbulatorial");
        hql.addToSelect("pch.cargaHorariaHospitalar", "profissionalCargaHoraria.cargaHorariaHospitalar");
        hql.addToSelect("pch.cargaHorariaOutros", "profissionalCargaHoraria.cargaHorariaOutros");
        hql.addToSelect("pch.competenciaFim", "profissionalCargaHoraria.competenciaFim");
        hql.addToSelect("pch.competenciaInicio", "profissionalCargaHoraria.competenciaInicio");

        hql.setTypeSelect(RelatorioProfissionalDTO.class.getName());
        hql.addToFrom("ProfissionalCargaHoraria pch " +
                " right join pch.profissional p");

        hql.addToWhereWhithAnd("pch.empresa ", this.param.getEmpresas());
        hql.addToWhereWhithAnd("pch.tabelaCbo ", this.param.getTabelasCbo());
        hql.addToWhereWhithAnd("p ", this.param.getProfissionais());

        if(this.param.getTabelaCboSubGrupo() != null){
            hql.addToWhereWhithAnd("pch.tabelaCbo.tabelaCboSubGrupo.id.tabelaCboGrupo.codigo = ",this.param.getTabelaCboSubGrupo().getId().getTabelaCboGrupo().getCodigo());
            if(this.param.getTabelaCboSubGrupo().getId().getCodigo() != null){
                hql.addToWhereWhithAnd("pch.tabelaCbo.tabelaCboSubGrupo.id.codigo = ",this.param.getTabelaCboSubGrupo().getId().getCodigo());
            }
        }
        if(param.getCompetenciaInicial()!=null){
            hql.addToWhereWhithAnd("pch.competenciaInicio =",param.getCompetenciaInicial());
        }
        if(param.getCompetenciaFinal()!=null){
            hql.addToWhereWhithAnd("pch.competenciaFim =",param.getCompetenciaFinal());
        }

        hql.addToWhereWhithAnd("coalesce(pch.dataDesativacao,current_date) >= current_date");

        if (Bundle.getStringApplication("rotulo_unidade").equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("pch.empresa.codigo");
        } else if (Bundle.getStringApplication("rotulo_cbo").equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("pch.tabelaCbo.cbo");
        }

        hql.addToOrder("p.codigo");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public void setDTOParam(RelatorioProfissionalDTOParam param) {
        this.param = param;
    }

    @Override
    public List<RelatorioProfissionalDTO> getResult() {
        return result;
    }

}
