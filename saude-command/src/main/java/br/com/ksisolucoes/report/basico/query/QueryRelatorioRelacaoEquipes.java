package br.com.ksisolucoes.report.basico.query;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.basico.interfaces.dto.RelatorioRelacaoEquipesDTO;
import br.com.ksisolucoes.report.basico.interfaces.dto.RelatorioRelacaoEquipesDTOParam;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Equipe;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.basico.TipoEquipe;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.ProfissionalCargaHoraria;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import org.hibernate.Query;
import org.hibernate.Session;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioRelacaoEquipes extends CommandQuery<QueryRelatorioRelacaoEquipes> implements ITransferDataReport<RelatorioRelacaoEquipesDTOParam, RelatorioRelacaoEquipesDTO>{

    private RelatorioRelacaoEquipesDTOParam param;
    private List<RelatorioRelacaoEquipesDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("te.codigo", "equipe.tipoEquipe.codigo");
        hql.addToSelect("te.descricao", "equipe.tipoEquipe.descricao");

        if (param.getFormaApresentacao().equals(Equipe.REF)) {
            hql.addToSelect("emp.codigo", "equipe.empresa.codigo");
            hql.addToSelect("emp.referencia", "equipe.empresa.referencia");
            hql.addToSelect("emp.cnes", "equipe.empresa.cnes");
            hql.addToSelect("emp.descricao", "equipe.empresa.descricao");
            hql.addToSelect("cid.codigo", "equipe.equipeArea.cidade.codigo");
            hql.addToSelect("cid.descricao", "equipe.equipeArea.cidade.descricao");
            hql.addToSelect("e.id.codigo", "equipe.codigo");
            hql.addToSelect("e.referencia", "equipe.referencia");
            hql.addToSelect("e.ativo", "equipe.ativo");
            hql.addToSelect("e.tipoQuilombo", "equipe.tipoQuilombo");
            hql.addToSelect("e.tipoAssentado", "equipe.tipoAssentado");
            hql.addToSelect("e.tipoGeral", "equipe.tipoGeral");
            hql.addToSelect("e.tipoEscola", "equipe.tipoEscola");
            hql.addToSelect("e.tipoPronasci", "equipe.tipoPronasci");
            hql.addToSelect("e.tipoIndigena", "equipe.tipoIndigena");
            hql.addToSelect("e.equipeCnes", "equipe.equipeCnes");
        } else if (param.getFormaApresentacao().equals(TipoEquipe.REF)) {
            hql.addToSelect(" count(e.codigo) ", "totalEquipes");
            hql.addToSelect(" sum( case when e.tipoQuilombo = '1' then 1 else 0 end ) ", "totalQuilombolas");
            hql.addToSelect(" sum( case when e.tipoAssentado = '1' then 1 else 0 end ) ", "totalAssentados");
            hql.addToSelect(" sum( case when e.tipoGeral = '1' then 1 else 0 end ) ", "totalGeral");
            hql.addToSelect(" sum( case when e.tipoEscola = '1' then 1 else 0 end ) ", "totalEscola");
            hql.addToSelect(" sum( case when e.tipoPronasci = '1' then 1 else 0 end ) ", "totalPronasci");
            hql.addToSelect(" sum( case when e.tipoIndigena = '1' then 1 else 0 end ) ", "totalIndigena");
            hql.addToSelect("e.equipeCnes","equipe.equipeCnes");
        }

        hql.addToSelect("cbo.cbo", "tabelaCbo.cbo");
        hql.addToSelect("cbo.descricao", "tabelaCbo.descricao");

        if (ReportProperties.DETALHADO == param.getTipoRelatorio()) {
            hql.addToSelect("p.codigo", "profissional.codigo");
            hql.addToSelect("p.referencia", "profissional.referencia");
            hql.addToSelect("p.nome", "profissional.nome");
            hql.addToSelect("ema.microArea", "microArea");
            hql.addToSelect("ep.dataEntrada", "dataEntrada");
        } else if (ReportProperties.RESUMIDO == param.getTipoRelatorio()) {
            hql.addToSelect(" count(p.codigo) ", "quantidadeProfissionais");
        }

        hql.setTypeSelect(RelatorioRelacaoEquipesDTO.class.getName());
        hql.addToFrom("EquipeProfissional ep"
                + " left join ep.equipeMicroArea ema"
                + " left join ep.equipe e"
                + " left join e.equipeArea ea"
                + " left join ea.id.cidade cid"
                + " left join e.tipoEquipe te"
                + " left join e.empresa emp"
                + " left join ep.profissional p ");

        hql.addToFrom("ProfissionalCargaHoraria pch "
                + " left join pch.tabelaCbo cbo");

        hql.addToWhereWhithAnd("pch.profissional = p");
        hql.addToWhereWhithAnd("pch.empresa = emp");

        hql.addToWhereWhithAnd("pch.dataDesativacao IS NULL");

        hql.addToWhereWhithAnd("emp ", param.getEmpresas());
        hql.addToWhereWhithAnd("ea = ", param.getEquipeArea());
        hql.addToWhereWhithAnd("p ", param.getProfissionais());
        if(param.getTabelaCbo() != null && CollectionUtils.isNotNullEmpty(param.getTabelaCbo().getValue())){
            hql.addToWhereWhithAnd("cbo.cbo = :cbo");
        }
        hql.addToWhereWhithAnd("te ", param.getTipoEquipes());

        hql.addToWhereWhithAnd("e.ativo =", param.getSituacao());
        hql.addToWhereWhithAnd("ep.status =", EquipeProfissional.STATUS_ATIVO);

        if (param.getFormaApresentacao().equals(Equipe.REF)) {
            hql.addToOrder("e.codigo");
        } else if (param.getFormaApresentacao().equals(TipoEquipe.REF)) {
            hql.addToOrder("te.codigo");
        }

//        hql.addToOrder("cbo.descricao");

        if (param.getFormaApresentacao().equals(Equipe.REF)) {
            if (ReportProperties.RESUMIDO == param.getTipoRelatorio()) {
                hql.addToGroup("e.codigo");
                hql.addToGroup("e.referencia");
                hql.addToGroup("e.ativo");
                hql.addToGroup("te.codigo");
                hql.addToGroup("te.descricao");
                hql.addToGroup("emp.codigo");
                hql.addToGroup("emp.referencia");
                hql.addToGroup("emp.cnes");
                hql.addToGroup("emp.descricao");
                hql.addToGroup("cid.codigo");
                hql.addToGroup("cid.descricao");
                hql.addToGroup("e.tipoQuilombo");
                hql.addToGroup("e.tipoAssentado");
                hql.addToGroup("e.tipoGeral");
                hql.addToGroup("e.tipoEscola");
                hql.addToGroup("e.tipoPronasci");
                hql.addToGroup("e.tipoIndigena");
                hql.addToGroup("cbo.cbo");
            }
        } else if (param.getFormaApresentacao().equals(TipoEquipe.REF)) {
            hql.addToGroup("te.codigo");
            hql.addToGroup("te.descricao");
            hql.addToGroup("e.equipeCnes");
            hql.addToGroup("cbo.cbo");
            if (ReportProperties.DETALHADO == param.getTipoRelatorio()) {
                hql.addToGroup("ema.microArea");
                hql.addToGroup("p.codigo");
                hql.addToGroup("p.referencia");
                hql.addToGroup("p.nome");
                hql.addToGroup("ep.dataEntrada");
            }
        }
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (this.result != null) {
            for (RelatorioRelacaoEquipesDTO relatorioRelacaoEquipesDTO : this.result) {
                ProfissionalCargaHoraria profissionalCargaHoraria = LoadManager.getInstance(ProfissionalCargaHoraria.class)
                        .addProperties(new HQLProperties(ProfissionalCargaHoraria.class).getProperties())
                        .addProperties(new HQLProperties(TabelaCbo.class, VOUtils.montarPath(ProfissionalCargaHoraria.PROP_TABELA_CBO)).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProfissionalCargaHoraria.PROP_EMPRESA), relatorioRelacaoEquipesDTO.getEquipe().getEmpresa()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProfissionalCargaHoraria.PROP_PROFISSIONAL, Profissional.PROP_CODIGO), relatorioRelacaoEquipesDTO.getProfissional() != null ? relatorioRelacaoEquipesDTO.getProfissional().getCodigo(): null))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProfissionalCargaHoraria.PROP_TABELA_CBO, TabelaCbo.PROP_CBO), relatorioRelacaoEquipesDTO.getTabelaCbo().getCbo()))
                        .addParameter(new QueryCustom.QueryCustomParameter(ProfissionalCargaHoraria.PROP_DATA_DESATIVACAO, QueryCustom.QueryCustomParameter.IS_NULL))
                        .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(ProfissionalCargaHoraria.PROP_TABELA_CBO, TabelaCbo.PROP_DESCRICAO)))
                        .setMaxResults(1).start().getVO();
                if (profissionalCargaHoraria != null) {
                    relatorioRelacaoEquipesDTO.setChAmb(profissionalCargaHoraria.getCargaHorariaAmbulatorial());
                    relatorioRelacaoEquipesDTO.setChHosp(profissionalCargaHoraria.getCargaHorariaHospitalar());
                    relatorioRelacaoEquipesDTO.setChOut(profissionalCargaHoraria.getCargaHorariaOutros());
                }
            }
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioRelacaoEquipesDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(RelatorioRelacaoEquipesDTOParam param) {
        this.param = param;
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        if (param.getTabelaCbo() != null && CollectionUtils.isNotNullEmpty(param.getTabelaCbo().getValue())) {
            query.setString("cbo", param.getTabelaCbo().getValue().get(0).getCbo());
        }
    }
}
