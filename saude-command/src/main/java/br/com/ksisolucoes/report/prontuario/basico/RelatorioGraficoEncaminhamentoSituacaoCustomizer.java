package br.com.ksisolucoes.report.prontuario.basico;

import net.sf.jasperreports.engine.JRChart;
import net.sf.jasperreports.engine.JRChartCustomizer;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.plot.CategoryPlot;

import java.awt.*;

/**
 *
 * <AUTHOR>
 */
public class RelatorioGraficoEncaminhamentoSituacaoCustomizer implements JRChartCustomizer {

    @Override
    public void customize(<PERSON><PERSON><PERSON><PERSON><PERSON> jfc, JRChart jrc) {
        if (jfc == null) {
            return;
        }
        if (jfc.getPlot() instanceof CategoryPlot) {
            ((CategoryPlot)jfc.getPlot()).getDomainAxis().setMaximumCategoryLabelLines(3);
            ((CategoryPlot)jfc.getPlot()).getRenderer().setBaseItemLabelFont(new Font("Arial", Font.PLAIN,5));
        }
    }

}
