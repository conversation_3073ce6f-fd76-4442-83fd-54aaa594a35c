/*
 * QueryResumoConsumoProduto.java
 *
 * Created on 01 de Novembro de 2005,10:23
 *
 * To change this template, choose Tools | Options and locate the template under
 * the Source Creation and Management node. Right-click the template and choose
 * Open. You can then make changes to the template in the Source Editor.
 */
package br.com.ksisolucoes.report.entrada.estoque.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioResumoConsumoProdutoDTO;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioResumoConsumoProdutoDTOParam;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.hibernate.Query;

/**
 *
 * <AUTHOR> Possamai
 */
public class QueryResumoConsumoProduto extends CommandQuery<QueryResumoConsumoProduto> implements ITransferDataReport<RelatorioResumoConsumoProdutoDTOParam, RelatorioResumoConsumoProdutoDTO> {

    private RelatorioResumoConsumoProdutoDTOParam bean;

    private List<RelatorioResumoConsumoProdutoDTO> listRelatorioResumoConsumoProdutos;

    @Override
    protected void createQuery(HQLHelper hql) {
        if (RepositoryComponentDefault.NAO.equals(this.bean.getAgruparEmpresa())) {
            hql.addToSelect("e.referencia", "codigoEmpresa");
            hql.addToSelect("e.descricao", "descricaoEmpresa");
        }

        hql.addToSelect("p.subGrupo.roGrupoProduto.codigo", "codigoGrupo");
        hql.addToSelect("p.subGrupo.roGrupoProduto.descricao", "descricaoGrupo");
        hql.addToSelect("p.subGrupo.id.codigo", "codigoSubGrupo");
        hql.addToSelect("p.subGrupo.descricao", "descricaoSubGrupo");
        hql.addToSelect("pe.codigo", "codigoFornecedor");
        hql.addToSelect("pe.descricao", "descricaoFornecedor");
        hql.addToSelect("p.referencia", "codigoProduto"); // produto
        hql.addToSelect("p.descricao", "descricaoProduto"); // descricao produto
        hql.addToSelect("p.unidade.unidade", "unidadeProduto"); // unidade produto
        hql.addToSelect("sum(case when me.dataLancamento between :mesAno and :dataFinal then "
                + "         (case when me.tipoDocumento.flagTipoMovimento = :isEntrada then "
                + "               (me.quantidade * -1)"
                + "          else"
                + "               me.quantidade"
                + "          end)"
                + "    else"
                + "         0"
                + "    end)", "quantidade");
        //soma valor
        hql.addToSelect("sum(case when me.dataLancamento between :mesAno and :dataFinal then "
                + "         (case when me.tipoDocumento.flagTipoMovimento = :isEntrada then "
                + "               ((me.quantidade * -1 )* coalesce(me.precoMedio, 0))"
                + "          else"
                + "               (me.quantidade * coalesce(me.precoMedio, 0))"
                + "          end)"
                + "    else"
                + "         0"
                + "    end)", "valor");

        //soma quantidade m�dia
        hql.addToSelect("sum(case when me.dataLancamento between :novaData and :novaDataFinal then "
                + "         (case when me.tipoDocumento.flagTipoMovimento = :isEntrada then "
                + "               (me.quantidade * -1) "
                + "          else"
                + "               me.quantidade "
                + "          end)"
                + "    else "
                + "         0"
                + "    end)", "quantidadeMedia");
        hql.addToSelect("cast ('" + this.bean.getQuantidadeMesesMedia() + "' as integer)", "totalMesesMovimentados");

        //soma valor m�dia
        hql.addToSelect("sum(case when me.dataLancamento between :novaData and :novaDataFinal then "
                + "         (case when me.tipoDocumento.flagTipoMovimento = :isEntrada then "
                + "               ((me.quantidade * -1 )* coalesce(me.precoMedio, 0))"
                + "          else"
                + "               (me.quantidade * coalesce(me.precoMedio, 0))"
                + "          end)"
                + "    else"
                + "         0"
                + "    end)", "valorMedia");

        hql.setTypeSelect(RelatorioResumoConsumoProdutoDTO.class.getName());

        hql.addToFrom("Produto p, Empresa e, MovimentoEstoque me"
                + " left join me.pessoa pe ");

        hql.addToWhereWhithAnd("p.codigo = me.produto.codigo");
        hql.addToWhereWhithAnd("e.codigo = me.id.empresa.codigo");
        hql.addToWhereWhithAnd("me.empresaDestino = ", bean.getEmpresaDestino());
        hql.addToWhereWhithAnd("me.pessoa = ", bean.getFornecedor());
        if (CollectionUtils.isNotNullEmpty(this.bean.getEmpresas().getValue())) {
            hql.addToWhereWhithAnd("me.id.empresa " + this.bean.getEmpresas().getOperador().value() + " (:empresa)");
        }
        if (CollectionUtils.isNotNullEmpty(this.bean.getGrupoProduto().getValue())) {
            hql.addToWhereWhithAnd("p.subGrupo.roGrupoProduto " + this.bean.getGrupoProduto().getOperador().value() + " (:grupoProduto)");

        }

        if (this.bean.getGrupoProdutoSubGrupo() != null) {
            hql.addToWhereWhithAnd("p.subGrupo.roGrupoProduto = :subGrupoGrupoProduto");// grupo
            if (this.bean.getSubGrupo() != null) {
                hql.addToWhereWhithAnd("p.subGrupo.id.codigo = :subGrupo");// subgrupo
            }
        }
        if (CollectionUtils.isNotNullEmpty(this.bean.getLocalizacao().getValue())) {
            hql.addToWhereWhithAnd("exists(select 1 from EstoqueEmpresa ee"
                    + "                    where ee.id.empresa.codigo = e.codigo"
                    + "                    and ee.id.produto.codigo = p.codigo"
                    + "                    and ee.localizacao " + this.bean.getLocalizacao().getOperador().value() + " (:localizacao))");
        }
        if (!this.bean.getCurva().getChave().equals(RepositoryComponentDefault.TODOS)) {
            hql.addToWhereWhithAnd("p.curva = :Flagcurva");
        }

        hql.addToWhereWhithAnd("me.dataLancamento between :novaData and :dataFinal");
        hql.addToWhereWhithAnd("me.tipoDocumento.flagConsumo = :isConsumo");

        if (RepositoryComponentDefault.NAO.equals(this.bean.getAgruparEmpresa())) {
            hql.addToOrder("e.descricao");
            hql.addToOrder("e.codigo");
        }

        hql.addToOrder("p.subGrupo.roGrupoProduto.descricao");
        hql.addToOrder("p.subGrupo.roGrupoProduto.codigo");
        hql.addToOrder("p.subGrupo.descricao");
        hql.addToOrder("p.subGrupo.id.codigo");

        if (this.bean.getOrdenacao().equals(Produto.PROP_CODIGO)) {
            hql.addToOrder("p.codigo");
        } else {
            hql.addToOrder("p.descricao");
        }

        hql.addToOrder("p.unidade.unidade");
//        hql.addToOrder("year( me.dataLancamento ), month( me.dataLancamento )" );

        if (RepositoryComponentDefault.NAO.equals(this.bean.getAgruparEmpresa())) {
            hql.addToGroup("e.codigo");
            hql.addToGroup("e.referencia");
            hql.addToGroup("e.descricao");
        }

        hql.addToGroup("p.subGrupo.roGrupoProduto.descricao");
        hql.addToGroup("p.subGrupo.roGrupoProduto.codigo");
        hql.addToGroup("p.subGrupo.descricao");
        hql.addToGroup("p.subGrupo.id.codigo");
        hql.addToGroup("pe.codigo");

        if (this.bean.getOrdenacao().equals(Produto.PROP_CODIGO)) {
            hql.addToGroup("p.codigo");
            hql.addToGroup("p.referencia");
            hql.addToGroup("p.descricao");
        } else {
            hql.addToGroup("p.descricao");
            hql.addToGroup("p.referencia");
            hql.addToGroup("p.codigo");
        }

        hql.addToGroup("p.unidade.unidade");
//        hql.addToGroup("year( me.dataLancamento ), month( me.dataLancamento )" );

    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {

        if (CollectionUtils.isNotNullEmpty(this.bean.getEmpresas().getValue())) {
            hql.setParameterValue(query, "empresa", bean.getEmpresas().getValue());
        }
        if (CollectionUtils.isNotNullEmpty(this.bean.getGrupoProduto().getValue())) {
            hql.setParameterValue(query, "grupoProduto", bean.getGrupoProduto().getValue());
        }

        if (this.bean.getGrupoProdutoSubGrupo() != null) {
            hql.setParameterValue(query, "subGrupoGrupoProduto", bean.getGrupoProdutoSubGrupo());
            if (this.bean.getSubGrupo() != null) {
                hql.setParameterValue(query, "subGrupo", bean.getSubGrupo().getId().getCodigo());
            }
        }

        if (CollectionUtils.isNotNullEmpty(this.bean.getLocalizacao().getValue())) {
            hql.setParameterValue(query, "localizacao", bean.getLocalizacao().getValue());
        }
        hql.setParameterValue(query, "mesAno", this.bean.getMesAno());
        hql.setParameterValue(query, "dataFinal", this.bean.getDataFinal());
        hql.setParameterValue(query, "isConsumo", TipoDocumento.IS_CONSUMO);
        if (!this.bean.getCurva().getChave().equals(RepositoryComponentDefault.TODOS)) {

            hql.setParameterValue(query, "Flagcurva", this.bean.getCurva().getChave());

        }
        hql.setParameterValue(query, "novaData", Data.removeMeses(this.bean.getMesAno(), (this.bean.getQuantidadeMesesMedia().intValue() - 1)));
        hql.setParameterValue(query, "novaDataFinal", this.bean.getDataFinal());
        hql.setParameterValue(query, "isEntrada", TipoDocumento.IS_ENTRADA);

    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.listRelatorioResumoConsumoProdutos = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public Collection getResult() {
        return listRelatorioResumoConsumoProdutos;
    }

    @Override
    public void setDTOParam(RelatorioResumoConsumoProdutoDTOParam arg0) {
        bean = arg0;
    }

}
