package br.com.ksisolucoes.report.vigilancia.query;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ImpressaoParecerProjetoHidrossanitarioDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ImpressaoParecerProjetoHidrossanitarioDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecer;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecerFiscal;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecerResposta;
import ch.lambdaj.Lambda;
import com.ibm.icu.lang.UCharacter;
import com.ibm.icu.text.BreakIterator;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;

/**
 * <AUTHOR>
 */
public class QueryRelatorioImpressaoParecerProjetoHidrossanitario extends CommandQuery<QueryRelatorioImpressaoParecerProjetoHidrossanitario> implements ITransferDataReport<ImpressaoParecerProjetoHidrossanitarioDTOParam, RequerimentoProjetoHidrossanitarioParecer> {

    private ImpressaoParecerProjetoHidrossanitarioDTOParam param;
    private List<ImpressaoParecerProjetoHidrossanitarioDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(ImpressaoParecerProjetoHidrossanitarioDTO.class.getName());
        hql.addToSelect("requerimentoProjetoHidrossanitarioParecer.codigo", "codigo");
        hql.addToSelect("requerimentoProjetoHidrossanitarioParecer.observacao", "observacao");
        hql.addToSelect("requerimentoProjetoHidrossanitarioParecer.descricaoParecer", "descricaoParecer");
        hql.addToSelect("requerimentoProjetoHidrossanitarioParecer.dataParecer", "dataParecer");
        hql.addToSelect("requerimentoProjetoHidrossanitarioParecer.dataCadastro", "dataCadastro");
        hql.addToSelect("requerimentoProjetoHidrossanitarioParecer.dataRetorno", "dataRetorno");

        hql.addToSelect("rv.codigo", "requerimentoProjetoHidrossanitario.requerimentoVigilancia.codigo");
        hql.addToSelect("rv.codigo", "requerimentoVigilancia.codigo");
        hql.addToSelect("rv.protocolo", "requerimentoVigilancia.protocolo");
        hql.addToSelect("requerimentoProjetoHidrossanitarioParecer.status", "status");

        StringBuilder from = new StringBuilder("RequerimentoProjetoHidrossanitarioParecer requerimentoProjetoHidrossanitarioParecer ");
        from.append(" LEFT JOIN requerimentoProjetoHidrossanitarioParecer.requerimentoProjetoHidrossanitario requerimentoProjetoHidrossanitario ");
        from.append(" LEFT JOIN requerimentoProjetoHidrossanitario.requerimentoVigilancia rv ");
        hql.addToFrom(from.toString());

        hql.addToWhereWhithAnd("rv.codigo = ", this.param.getRequerimentoVigilancia().getCodigo());
        hql.addToWhereWhithAnd("requerimentoProjetoHidrossanitarioParecer.status in ", Arrays.asList(RequerimentoVigilancia.Situacao.PENDENTE.value(), RequerimentoVigilancia.Situacao.INDEFERIDO.value(),  RequerimentoVigilancia.Situacao.ANALISE.value()));
    }

    @Override
    public Collection getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List) result);
    }

    @Override
    public void setDTOParam(ImpressaoParecerProjetoHidrossanitarioDTOParam param) {
        this.param = param;
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(result)) {
            for (ImpressaoParecerProjetoHidrossanitarioDTO dto : result) {
                dto.setUrlQrCode(param.getUrlQRcode());
                setFiscais(dto);
                dto.setDescricaoRodape(null);
                ImpressaoParecerProjetoHidrossanitarioDTO impressaoParecerProjetoHidrossanitarioDTO = result.get(result.size() - 1);
                dto.setDescricaoRodape(impressaoParecerProjetoHidrossanitarioDTO.getRodape());

                RequerimentoProjetoHidrossanitarioParecerResposta proxy = Lambda.on(RequerimentoProjetoHidrossanitarioParecerResposta.class);
                RequerimentoProjetoHidrossanitarioParecerResposta respostaParecer = LoadManager.getInstance(RequerimentoProjetoHidrossanitarioParecerResposta.class)
                        .addProperty(path(proxy.getCodigo()))
                        .addProperty(path(proxy.getDescricaoResposta()))
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getCodigo()), dto.getCodigo()))
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getSituacao()), RequerimentoProjetoHidrossanitarioParecerResposta.Situacao.ENVIADO.value()))
                        .setMaxResults(1).start().getVO();
                if (respostaParecer != null) {
                    dto.setResposta(respostaParecer.getDescricaoResposta());
                    RequerimentoVigilanciaAnexo proxyAnexo = Lambda.on(RequerimentoVigilanciaAnexo.class);
                    List<RequerimentoVigilanciaAnexo> requerimentoVigilanciaAnexoList = LoadManager.getInstance(RequerimentoVigilanciaAnexo.class)
                            .addProperty(path(proxyAnexo.getCodigo()))
                            .addProperty(path(proxyAnexo.getDescricao()))
                            .addProperty(path(proxyAnexo.getGerenciadorArquivo().getCodigo()))
                            .addProperty(path(proxyAnexo.getGerenciadorArquivo().getNomeArquivo()))
                            .addParameter(new QueryCustom.QueryCustomParameter(path(proxyAnexo.getRequerimentoProjetoHidrossanitarioParecerResposta().getCodigo()), respostaParecer.getCodigo()))
                            .addParameter(new QueryCustom.QueryCustomParameter(path(proxyAnexo.getStatus()), RequerimentoVigilanciaAnexo.Status.CADASTRADO.value()))
                            .start().getList();
                    if (CollectionUtils.isNotNullEmpty(requerimentoVigilanciaAnexoList)) {
                        StringBuilder anexos = new StringBuilder();
                        for (RequerimentoVigilanciaAnexo requerimentoVigilanciaAnexo : requerimentoVigilanciaAnexoList) {
                            anexos.append(requerimentoVigilanciaAnexo.getDescricao());
                            anexos.append(": ");
                            anexos.append(requerimentoVigilanciaAnexo.getGerenciadorArquivo().getNomeArquivo());
                            anexos.append("<br/>");
                        }
                        dto.setAnexos(anexos.toString());
                    }
                }
            }
        }
    }

    private void setFiscais(ImpressaoParecerProjetoHidrossanitarioDTO dto) {
        RequerimentoProjetoHidrossanitarioParecerFiscal proxy = Lambda.on(RequerimentoProjetoHidrossanitarioParecerFiscal.class);

        List<RequerimentoProjetoHidrossanitarioParecerFiscal> list = LoadManager.getInstance(RequerimentoProjetoHidrossanitarioParecerFiscal.class)
                .addProperty(path(proxy.getRequerimentoVigilanciaFiscal().getProfissional().getCodigo()))
                .addProperty(path(proxy.getRequerimentoVigilanciaFiscal().getProfissional().getReferencia()))
                .addProperty(path(proxy.getRequerimentoVigilanciaFiscal().getProfissional().getNome()))
                .addProperty(path(proxy.getRequerimentoVigilanciaFiscal().getProfissional().getNumeroRegistro()))
                .addProperty(path(proxy.getRequerimentoVigilanciaFiscal().getProfissional().getUnidadeFederacaoConselhoRegistro()))
                .addProperty(path(proxy.getRequerimentoVigilanciaFiscal().getProfissional().getConselhoClasse().getCodigo()))
                .addProperty(path(proxy.getRequerimentoVigilanciaFiscal().getProfissional().getConselhoClasse().getSigla()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getCodigo()), dto.getCodigo()))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(list)) {
            List<Profissional> extract = Lambda.extract(list, proxy.getRequerimentoVigilanciaFiscal().getProfissional());
            List<Profissional> fiscais = new ArrayList<>();
            for (Profissional fiscal : extract) {
                fiscal.setNome(UCharacter.toTitleCase(fiscal.getNome().toLowerCase(), BreakIterator.getTitleInstance()));
                fiscais.add(fiscal);
            }
            dto.setFiscais(fiscais);
        }
    }

}