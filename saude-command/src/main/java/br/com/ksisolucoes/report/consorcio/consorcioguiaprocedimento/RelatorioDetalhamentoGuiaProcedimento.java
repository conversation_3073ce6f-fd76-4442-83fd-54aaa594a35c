package br.com.ksisolucoes.report.consorcio.consorcioguiaprocedimento;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.consorcio.consorcioguiaprocedimento.query.QueryRelatorioDetalhamentoGuiaProcedimento;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoGuiaProcedimentoDTOParam;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioDetalhamentoGuiaProcedimento extends AbstractReport<RelatorioDetalhamentoGuiaProcedimentoDTOParam> {

    public RelatorioDetalhamentoGuiaProcedimento(RelatorioDetalhamentoGuiaProcedimentoDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        if (RelatorioDetalhamentoGuiaProcedimentoDTOParam.ModeloImpressao.PAISAGEM.equals(param.getModeloImpressao())) {
            return "/br/com/ksisolucoes/report/consorcio/consorcioguiaprocedimento/jrxml/relatorio_detalhamento_guia_procedimento.jrxml";
        }else{
            return "/br/com/ksisolucoes/report/consorcio/consorcioguiaprocedimento/jrxml/relatorio_detalhamento_guia_procedimento_retrato.jrxml";
        }
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_detalhamento_guia_procedimento");
    }

    @Override
    public ITransferDataReport getQuery() {
        addParametro("formaApresentacao", getParam().getFormaApresentacao());
        addParametro("tipoResumo", getParam().getTipoResumo());
        addParametro("tipoData", getParam().getTipoData().descricao());
        
        return new QueryRelatorioDetalhamentoGuiaProcedimento();
    }
    
}
