<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_pedido_transferencia" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="bdb8ee69-8284-422c-bedd-679ae28dc056">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.zoom" value="2.1435888100000016"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferencia"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa"/>
	<import value="br.com.ksisolucoes.report.ReportProperties"/>
	<import value="br.com.ksisolucoes.vo.entradas.estoque.Produto"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<parameter name="ParamFormaApresentacao" class="java.lang.String" isForPrompting="false"/>
	<parameter name="utilizaLocalizacaoEstoque" class="java.lang.Boolean"/>
	<field name="class" class="java.lang.Object"/>
	<field name="codigoEmpresa" class="java.lang.String"/>
	<field name="codigoLocalizacao" class="java.lang.Long"/>
	<field name="codigoProduto" class="java.lang.Long"/>
	<field name="descricaoEmpresaFormatado" class="java.lang.String"/>
	<field name="descricaoLocalizacao" class="java.lang.String"/>
	<field name="descricaoProduto" class="java.lang.String"/>
	<field name="descricaoProdutoFormatado" class="java.lang.String"/>
	<field name="estoqueMinimo" class="java.lang.Double"/>
	<field name="nomeEmpresa" class="java.lang.String"/>
	<field name="numeroPedidoTransferencia" class="java.lang.Long"/>
	<field name="quantidade" class="java.lang.Double"/>
	<field name="unidade" class="java.lang.String"/>
	<field name="descricaoLocalizacaoFormatado" class="java.lang.String"/>
	<field name="codigoEmpresaDestino" class="java.lang.Long"/>
	<field name="nomeEmpresaDestino" class="java.lang.String"/>
	<field name="descricaoEmpresaDestinoFormatado" class="java.lang.String"/>
	<field name="lote" class="java.lang.String"/>
	<field name="quantidadeLote" class="java.lang.Double"/>
	<field name="codigoDeposito" class="java.lang.Long"/>
	<field name="descricaoDeposito" class="java.lang.String"/>
	<field name="descricaoCentroCustoFormatado" class="java.lang.String"/>
	<field name="numeroPedidoTransferenciaFormatado" class="java.lang.String"/>
	<field name="mascaraLocalizacaoEstrutura" class="java.lang.String"/>
	<group name="GrupoEmpresa" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{codigoEmpresa}]]></groupExpression>
		<groupHeader>
			<band height="14" splitType="Stretch">
				<rectangle radius="10">
					<reportElement uuid="7bb18082-fe7e-485e-9948-e9772ac4f656" x="0" y="0" width="535" height="14"/>
				</rectangle>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="" isBlankWhenNull="false">
					<reportElement uuid="6f20e9d0-**************-4fc8a96a2d17" key="textField-43" mode="Transparent" x="0" y="0" width="535" height="14" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*empresa*/Bundle.getStringApplication("rotulo_empresa") + ":" + " " + $F{descricaoEmpresaDestinoFormatado}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<group name="GrupoPedidoTransferenciaGeral">
		<groupExpression><![CDATA[$F{numeroPedidoTransferencia}]]></groupExpression>
		<groupHeader>
			<band height="44" splitType="Stretch">
				<rectangle radius="10">
					<reportElement uuid="f252ff1c-c0ca-43e2-adba-f3e9c31b6f2b" x="0" y="0" width="535" height="30">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( ReportProperties.FORMA_APRESENTACAO_GERAL )]]></printWhenExpression>
					</reportElement>
				</rectangle>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="e11a95ea-950d-49f1-bdfa-08f6d7cb0f5d" key="textField-67" mode="Transparent" x="5" y="32" width="239" height="9" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( ReportProperties.FORMA_APRESENTACAO_GERAL )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*produto*/Bundle.getStringApplication("rotulo_produto")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="074c88c5-316a-403c-bb8f-44f53d2e8c06" key="textField-68" mode="Transparent" x="245" y="32" width="26" height="9" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( ReportProperties.FORMA_APRESENTACAO_GERAL )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*unidade*/Bundle.getStringApplication("rotulo_unidade_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="e1d2ee1d-0225-4a00-a0b6-abe6e4a4a487" key="textField-69" mode="Opaque" x="329" y="32" width="55" height="9" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( ReportProperties.FORMA_APRESENTACAO_GERAL )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*QtdPadrao*/Bundle.getStringApplication("rotulo_quantidade_padrao")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="26a7ab45-2455-4f9e-9c9c-c5c84cb295d3" key="textField-70" mode="Opaque" x="384" y="32" width="51" height="9" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( ReportProperties.FORMA_APRESENTACAO_GERAL )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Quantidade*/Bundle.getStringApplication("rotulo_quantidade")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="193e409e-a25e-422f-a741-eefb36b7a60b" key="textField-73" mode="Opaque" x="445" y="32" width="81" height="9" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( ReportProperties.FORMA_APRESENTACAO_GERAL )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*localizacao*/Bundle.getStringApplication("rotulo_localizacao")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoPedidoTransferenciaGeral" pattern="" isBlankWhenNull="false">
					<reportElement uuid="79cd1dd1-c3aa-4187-b516-e5aeceacdf84" key="textField-74" mode="Transparent" x="10" y="0" width="516" height="14" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( ReportProperties.FORMA_APRESENTACAO_GERAL )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*pedidoTransferencia*/Bundle.getStringApplication("rotulo_pedido_transferencia") + ":" + " " + $F{numeroPedidoTransferenciaFormatado}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement uuid="3403cda1-95e1-4d48-ae5e-9136359407a5" x="0" y="42" width="535" height="1">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( ReportProperties.FORMA_APRESENTACAO_GERAL )]]></printWhenExpression>
					</reportElement>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="05810a0d-ab49-465e-afb7-ff0a951d92ea" key="textField-68" mode="Transparent" x="274" y="32" width="45" height="9" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( ReportProperties.FORMA_APRESENTACAO_GERAL )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*lote*/Bundle.getStringApplication("rotulo_lote")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoPedidoTransferencia" pattern="" isBlankWhenNull="false">
					<reportElement uuid="f421763b-450c-4f4c-9fca-fa0fce7a1cf6" key="textField-82" mode="Transparent" x="269" y="15" width="256" height="14" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( ReportProperties.FORMA_APRESENTACAO_GERAL )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_centro_custo")+": "+$F{descricaoCentroCustoFormatado}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoPedidoTransferencia" pattern="" isBlankWhenNull="false">
					<reportElement uuid="eb9a70b0-abe7-400e-8927-f7bfd787500f" key="textField-82" mode="Transparent" x="9" y="15" width="261" height="14" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( ReportProperties.FORMA_APRESENTACAO_GERAL )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_deposito")+": ("+$F{codigoDeposito}+") "+$F{descricaoDeposito}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<group name="GrupoPedidoTransferencia" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{numeroPedidoTransferencia}]]></groupExpression>
		<groupHeader>
			<band height="30" splitType="Stretch">
				<rectangle radius="10">
					<reportElement uuid="6c5d1fdd-5f65-45a2-8dc9-cf554c5b74d1" x="0" y="0" width="535" height="30">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( EstoqueEmpresa.PROP_LOCALIZACAO )]]></printWhenExpression>
					</reportElement>
				</rectangle>
				<textField evaluationTime="Group" evaluationGroup="GrupoPedidoTransferencia" pattern="" isBlankWhenNull="false">
					<reportElement uuid="ab2f26de-b147-47d6-8c75-41d122f959b7" key="textField-82" mode="Transparent" x="10" y="0" width="516" height="14" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( EstoqueEmpresa.PROP_LOCALIZACAO )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*pedidoTransferencia*/Bundle.getStringApplication("rotulo_pedido_transferencia") + ":" + " " + $F{numeroPedidoTransferenciaFormatado}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoPedidoTransferencia" pattern="" isBlankWhenNull="false">
					<reportElement uuid="58bd72a8-859a-42a0-a6cf-09d41ffd0da2" key="textField-82" mode="Transparent" x="10" y="16" width="261" height="14" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( EstoqueEmpresa.PROP_LOCALIZACAO )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_deposito")+": ("+$F{codigoDeposito}+") "+$F{descricaoDeposito}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoPedidoTransferencia" pattern="" isBlankWhenNull="false">
					<reportElement uuid="e78f8895-e4c4-49dd-abaa-46e0926c3f40" key="textField-82" mode="Transparent" x="270" y="16" width="256" height="14" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( EstoqueEmpresa.PROP_LOCALIZACAO )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_centro_custo")+": "+$F{descricaoCentroCustoFormatado}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<group name="GrupoLocalizacao" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{codigoLocalizacao}]]></groupExpression>
		<groupHeader>
			<band height="28" splitType="Stretch">
				<rectangle radius="10">
					<reportElement uuid="a3f83a99-ed51-4c12-8ded-2e016d30dfe3" x="0" y="0" width="535" height="14">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( EstoqueEmpresa.PROP_LOCALIZACAO )]]></printWhenExpression>
					</reportElement>
				</rectangle>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="1abd9b37-3abc-45eb-9b67-e0df629f4080" key="textField-77" mode="Transparent" x="5" y="17" width="239" height="9" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( EstoqueEmpresa.PROP_LOCALIZACAO )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*produto*/Bundle.getStringApplication("rotulo_produto")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="5d670418-4ee5-4fc3-9778-5815edf6141f" key="textField-78" mode="Transparent" x="245" y="17" width="26" height="9" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( EstoqueEmpresa.PROP_LOCALIZACAO )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*unidade*/Bundle.getStringApplication("rotulo_unidade_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="d81c4b0b-481a-4eef-b8eb-1faff7d62cf3" key="textField-79" mode="Opaque" x="329" y="17" width="55" height="9" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( EstoqueEmpresa.PROP_LOCALIZACAO )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*QtdPadrao*/Bundle.getStringApplication("rotulo_quantidade_padrao")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="4ea0a176-0980-4fff-a60d-96f145e657b4" key="textField-80" mode="Opaque" x="384" y="17" width="51" height="9" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( EstoqueEmpresa.PROP_LOCALIZACAO )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Quantidade*/Bundle.getStringApplication("rotulo_quantidade")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="83466a9c-6764-430d-9314-66430704a952" key="textField-81" mode="Opaque" x="445" y="17" width="81" height="9" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( EstoqueEmpresa.PROP_LOCALIZACAO )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*localizacao*/Bundle.getStringApplication("rotulo_localizacao")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoLocalizacao" pattern="" isBlankWhenNull="false">
					<reportElement uuid="e0486708-5d71-4b69-b4ee-21d37f507e16" key="textField-76" mode="Transparent" x="10" y="0" width="525" height="14" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( EstoqueEmpresa.PROP_LOCALIZACAO )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*localizacao*/Bundle.getStringApplication("rotulo_localizacao") + ":" + " " + $F{descricaoLocalizacaoFormatado}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement uuid="8b242370-fed1-4584-a987-56718c239e3c" x="0" y="27" width="535" height="1">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( EstoqueEmpresa.PROP_LOCALIZACAO )]]></printWhenExpression>
					</reportElement>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="0727486d-9ec6-49e4-b6b5-0ce57f2a6f4a" key="textField-78" mode="Transparent" x="274" y="17" width="45" height="9" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( EstoqueEmpresa.PROP_LOCALIZACAO )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*lote*/Bundle.getStringApplication("rotulo_lote")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="10" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="e90cc204-4e44-4d7f-b81c-2c2eb1a7df77" key="textField-5" mode="Transparent" x="5" y="0" width="239" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoProdutoFormatado}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement uuid="bb16e108-7e4d-495f-9927-b268bb707631" key="textField-53" mode="Transparent" x="245" y="0" width="26" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unidade}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="245e21ca-d695-48f9-b491-fff2b8818f3a" key="textField-54" mode="Opaque" x="329" y="0" width="55" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{estoqueMinimo}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="8aa80550-3a44-4c7d-ad6c-c1b64a742adf" key="textField-55" mode="Opaque" x="384" y="0" width="51" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidadeLote}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="31abccca-8cc7-44df-9269-b87b99832cbb" key="textField-58" mode="Opaque" x="445" y="0" width="81" height="10" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[$P{utilizaLocalizacaoEstoque}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mascaraLocalizacaoEstrutura}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement uuid="2fc208e8-c0bc-4d03-868f-52000bc30710" key="textField-53" mode="Transparent" x="274" y="0" width="45" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{lote}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
