package br.com.ksisolucoes.report.vacina.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.insumo.ItemPedidoInsumo;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryImpressaoPedidoInsumo extends CommandQuery<QueryImpressaoPedidoInsumo> {

    private Long codigoPedido;
    private List<ItemPedidoInsumo> result;

    public QueryImpressaoPedidoInsumo(Long codigoPedido) {
        this.codigoPedido = codigoPedido;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        
        hql.addToSelect("empresa.codigo", "pedidoVacinaInsumo.empresa.codigo");
        hql.addToSelect("empresa.referencia", "pedidoVacinaInsumo.empresa.referencia");
        hql.addToSelect("empresa.descricao", "pedidoVacinaInsumo.empresa.descricao");
        hql.addToSelect("pedidoVacinaInsumo.codigo", "pedidoVacinaInsumo.codigo");
        hql.addToSelect("pedidoVacinaInsumo.numeroPedido", "pedidoVacinaInsumo.numeroPedido");
        hql.addToSelect("pedidoVacinaInsumo.dataCadastro", "pedidoVacinaInsumo.dataCadastro");
        hql.addToSelect("pedidoVacinaInsumo.status", "pedidoVacinaInsumo.status");
        hql.addToSelect("pedidoVacinaInsumo.observacao", "pedidoVacinaInsumo.observacao");
        hql.addToSelect("usuario.codigo", "pedidoVacinaInsumo.usuario.codigo");
        hql.addToSelect("usuario.nome", "pedidoVacinaInsumo.usuario.nome");
        hql.addToSelect("produto.codigo", "produto.codigo");
        hql.addToSelect("produto.referencia", "produto.referencia");
        hql.addToSelect("produto.descricao", "produto.descricao");
        hql.addToSelect("unidade.codigo", "produto.unidade.codigo");
        hql.addToSelect("unidade.unidade", "produto.unidade.unidade");
        hql.addToSelect("itemPedidoInsumo.quantidadePedida", "quantidadePedida");
        
        hql.setTypeSelect(ItemPedidoInsumo.class.getName());
        hql.addToFrom("ItemPedidoInsumo itemPedidoInsumo"
                + " left join itemPedidoInsumo.pedidoVacinaInsumo pedidoVacinaInsumo"
                + " left join pedidoVacinaInsumo.empresa empresa"
                + " left join pedidoVacinaInsumo.usuario usuario"
                + " left join itemPedidoInsumo.produto produto"
                + " left join produto.unidade unidade");
        
        hql.addToWhereWhithAnd("pedidoVacinaInsumo.codigo =", codigoPedido);

        hql.addToWhereWhithAnd("itemPedidoInsumo.status =", ItemPedidoInsumo.StatusItemPedidoInsumo.ABERTO.value());
        
        hql.addToOrder("produto.descricao");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>)result);
    }

    @Override
    public List<ItemPedidoInsumo> getResult() {
        return result;
    }
    
}
