/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.prontuario.exame;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.exame.query.QueryImpressaoLaudoExame;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.ImpressaoLaudoExameDTOParam;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Parametro;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoLaudoExame extends AbstractReport<ImpressaoLaudoExameDTOParam> {

    public ImpressaoLaudoExame(ImpressaoLaudoExameDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_impressao_laudo_exame.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_laudo_exame");
    }

    @Override
    protected void customDTOParam(ImpressaoLaudoExameDTOParam param) throws ValidacaoException {
            this.addParametro("nomeEmpresaRelatorio", getNomeEmpresaRelatorio());
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryImpressaoLaudoExame();
    }

    private String getNomeEmpresaRelatorio(){
        Empresa empresa = SessaoAplicacaoImp.getInstance().getEmpresa();

        if (StringUtils.trimToNull(empresa.getNomeRelatorio())!=null) {
            return empresa.getNomeRelatorio();
        }

        return empresa.getDescricao();
    }

}
