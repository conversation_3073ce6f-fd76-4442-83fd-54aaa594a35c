/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.prontuario.examebpai.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.prontuario.exame.interfaces.dto.RelatorioImpressaoExameBpaiDTO;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioImpressaoExameBpai extends CommandQuery<QueryRelatorioImpressaoExameBpai> implements ITransferDataReport<Long, RelatorioImpressaoExameBpaiDTO> {

    private Long param;
    private List<RelatorioImpressaoExameBpaiDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        
        hql.addToSelect("exameBpai.codigo","exameBpai.codigo");
        
        hql.addToSelect("exameBpai.descricaoDiagnostico","exameBpai.descricaoDiagnostico");
        hql.addToSelect("exameBpai.resumoAnamnese","exameBpai.resumoAnamnese");
        hql.addToSelect("exameBpai.justificativaProcedimento","exameBpai.justificativaProcedimento");
        
        hql.addToSelect("cidPrincipal.codigo","exameBpai.cidPrincipal.codigo");
        hql.addToSelect("cidPrincipal.descricao","exameBpai.cidPrincipal.descricao");
        
        hql.addToSelect("cidSecundario.codigo","exameBpai.cidSecundario.codigo");
        hql.addToSelect("cidSecundario.descricao","exameBpai.cidSecundario.descricao");
        
        hql.addToSelect("cidCausa.codigo","exameBpai.cidCausa.codigo");
        hql.addToSelect("cidCausa.descricao","exameBpai.cidCausa.descricao");
        
        hql.addToSelect("exameRequisicao.codigo","exameRequisicao.codigo");
        hql.addToSelect("exameRequisicao.quantidade","exameRequisicao.quantidade");
        hql.addToSelect("exameRequisicao.complemento","exameRequisicao.complemento");
        
        hql.addToSelect("exameProcedimento.codigo","exameRequisicao.exameProcedimento.codigo");
        hql.addToSelect("exameProcedimento.descricaoProcedimento","exameRequisicao.exameProcedimento.descricaoProcedimento");
        
        hql.addToSelect("procedimento.codigo","exameRequisicao.exameProcedimento.procedimento.codigo");
        hql.addToSelect("procedimento.referencia","exameRequisicao.exameProcedimento.procedimento.referencia");
        hql.addToSelect("procedimento.descricao","exameRequisicao.exameProcedimento.procedimento.descricao");
        
        hql.addToSelect("exame.codigo","exame.codigo");
        hql.addToSelect("exame.dataAutorizacao","exame.dataAutorizacao");
        hql.addToSelect("exame.nomeProfissional","exame.nomeProfissional");
        hql.addToSelect("exame.nomePaciente","exame.nomePaciente");
        hql.addToSelect("exame.dataSolicitacao","exame.dataSolicitacao");
        hql.addToSelect("exame.numeroProtocoloAutorizacao","exame.numeroProtocoloAutorizacao");
        hql.addToSelect("exame.flagUrgente","exame.flagUrgente");
        hql.addToSelect("exame.descricaoDadoClinico","exame.descricaoDadoClinico");
        
        hql.addToSelect("usuarioAutorizacao.codigo","exame.usuarioAutorizacao.codigo");
        hql.addToSelect("profissional.codigo",                          "exame.profissional.codigo");
        hql.addToSelect("profissional.nome",                            "exame.profissional.nome");
        hql.addToSelect("profissional.codigoCns",                       "exame.profissional.codigoCns");
        hql.addToSelect("profissional.unidadeFederacaoConselhoRegistro","exame.profissional.unidadeFederacaoConselhoRegistro");
        hql.addToSelect("profissional.numeroRegistro",                  "exame.profissional.numeroRegistro");
        
        hql.addToSelect("conselhoClasse.sigla",                         "exame.profissional.conselhoClasse.sigla"); 
        
        hql.addToSelect("empresaSolicitante.codigo","exame.empresaSolicitante.codigo");
        hql.addToSelect("empresaSolicitante.descricao","exame.empresaSolicitante.descricao");
        hql.addToSelect("empresaSolicitante.cnes","exame.empresaSolicitante.cnes");
        
        hql.addToSelect("localExame.codigo","exame.localExame.codigo");
        hql.addToSelect("localExame.descricao","exame.localExame.descricao");
        hql.addToSelect("localExame.cnes","exame.localExame.cnes");
        
        hql.addToSelect("usuarioCadsus.codigo","exame.usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome","exame.usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.apelido","exame.usuarioCadsus.apelido");
        hql.addToSelect("usuarioCadsus.utilizaNomeSocial","exame.usuarioCadsus.utilizaNomeSocial");
        hql.addToSelect("usuarioCadsus.nomeMae","exame.usuarioCadsus.nomeMae");
        hql.addToSelect("usuarioCadsus.nomePai","exame.usuarioCadsus.nomePai");
        hql.addToSelect("usuarioCadsus.responsavel","exame.usuarioCadsus.responsavel");
        hql.addToSelect("usuarioCadsus.telefone","exame.usuarioCadsus.telefone");
        hql.addToSelect("usuarioCadsus.telefone2","exame.usuarioCadsus.telefone2");
        hql.addToSelect("usuarioCadsus.celular","exame.usuarioCadsus.celular");
        hql.addToSelect("usuarioCadsus.cpf","exame.usuarioCadsus.cpf");
        hql.addToSelect("usuarioCadsus.sexo","exame.usuarioCadsus.sexo");
        hql.addToSelect("usuarioCadsus.dataNascimento","exame.usuarioCadsus.dataNascimento");
        
        hql.addToSelect("raca.codigo","exame.usuarioCadsus.raca.codigo");
        hql.addToSelect("raca.descricao","exame.usuarioCadsus.raca.descricao");
        
        hql.addToSelect("enderecoUsuarioCadsus.codigo","enderecoUsuarioCadsus.codigo");
        hql.addToSelect("enderecoUsuarioCadsus.logradouro","enderecoUsuarioCadsus.logradouro");
        hql.addToSelect("enderecoUsuarioCadsus.bairro","enderecoUsuarioCadsus.bairro");
        hql.addToSelect("enderecoUsuarioCadsus.cep","enderecoUsuarioCadsus.cep");
        hql.addToSelect("enderecoUsuarioCadsus.numeroLogradouro","enderecoUsuarioCadsus.numeroLogradouro");
        hql.addToSelect("enderecoUsuarioCadsus.complementoLogradouro","enderecoUsuarioCadsus.complementoLogradouro");
        hql.addToSelect("tipoLogradouro.codigo","enderecoUsuarioCadsus.tipoLogradouro.codigo");
        hql.addToSelect("tipoLogradouro.descricao","enderecoUsuarioCadsus.tipoLogradouro.descricao");
        hql.addToSelect("tipoLogradouro.sigla","enderecoUsuarioCadsus.tipoLogradouro.sigla");
        
        hql.addToSelect("cidade.codigo", "enderecoUsuarioCadsus.cidade.codigo");
        hql.addToSelect("cidade.descricao", "enderecoUsuarioCadsus.cidade.descricao");
        
        hql.addToSelect("estado.codigo", "enderecoUsuarioCadsus.cidade.estado.sigla");
        hql.addToSelect("estado.sigla", "enderecoUsuarioCadsus.cidade.estado.sigla");
        
        hql.addToSelect("(select up.numeroProntuario from UsuarioCadsusProntuario up where up.id.usuarioCadsus = usuarioCadsus and up.id.empresa = empresaSolicitante)","numeroProntuario");
        hql.addToSelect("(select min(ucc.numeroCartao) from UsuarioCadsusCns ucc where ucc.usuarioCadsus = usuarioCadsus and ucc.excluido = 0 )","numeroCartao");
        hql.addToSelect("(select sa.codigo from IMTableSolicitacaoAgendamentoToExameBpai im left join im.solicitacaoAgendamento sa left join im.exameBpai eb where eb.codigo = exameBpai.codigo )","codigoSolicitacaoAgendamento");
        
        hql.setTypeSelect(RelatorioImpressaoExameBpaiDTO.class.getName());
        
        hql.addToFrom("ExameRequisicao exameRequisicao "
                + " join exameRequisicao.exame exame_"
                + " join exameRequisicao.exameProcedimento exameProcedimento "
                + " join exameProcedimento.procedimento procedimento ");
        
        hql.addToFrom("ExameBpai exameBpai "
                + " left join exameBpai.cidPrincipal cidPrincipal "
                + " left join exameBpai.cidSecundario cidSecundario "
                + " left join exameBpai.cidCausa cidCausa "
                + " join exameBpai.exame exame "
                + " left join exame.atendimento atendimento "
                + " left join atendimento.enderecoUsuarioCadsus enderecoUsuarioCadsus "
                + " left join enderecoUsuarioCadsus.tipoLogradouro tipoLogradouro "
                + " left join enderecoUsuarioCadsus.cidade cidade "
                + " left join cidade.estado estado "
                + " left join exame.profissional profissional"
                + " left join profissional.conselhoClasse conselhoClasse"
                + " left join exame.usuarioCadsus usuarioCadsus"
                + " left join exame.empresaSolicitante empresaSolicitante"
                + " left join exame.localExame localExame"
                + " left join exame.usuarioAutorizacao usuarioAutorizacao"
                + " left join usuarioCadsus.raca raca"
                );
        
       /* hql.addToFrom("UsuarioCadsusEndereco usuarioCadsusEndereco"
                + " left join usuarioCadsusEndereco.id.endereco enderecoUsuarioCadsus"
                + " left join enderecoUsuarioCadsus.tipoLogradouro tipoLogradouro"
                + " left join enderecoUsuarioCadsus.cidade cidade"
                + " left join cidade.estado estado");*/
        
        hql.addToWhereWhithAnd("exame = exame_");
        
        //hql.addToWhereWhithAnd("usuarioCadsusEndereco.id.usuarioCadsus = usuarioCadsus");
        //hql.addToWhereWhithAnd("coalesce(usuarioCadsusEndereco.status, " + UsuarioCadsusEndereco.STATUS_ABERTO + ") = ", UsuarioCadsusEndereco.STATUS_ABERTO);
        
        hql.addToWhereWhithAnd("exame.codigo = ", param);
        
    }

    @Override
    public void setDTOParam(Long param) {
        this.param = param;
    }

    @Override
    public List<RelatorioImpressaoExameBpaiDTO> getResult() {
        return this.result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String,Object>>)result);
    }
    
}
