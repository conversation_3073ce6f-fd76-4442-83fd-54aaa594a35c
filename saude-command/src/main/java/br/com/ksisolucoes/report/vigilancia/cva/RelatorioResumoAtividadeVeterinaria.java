package br.com.ksisolucoes.report.vigilancia.cva;

import br.com.ksisolucoes.report.vigilancia.*;
import br.com.celk.vigilancia.dto.RelatorioResumoAtividadeVeterinariaDTOParam;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.vigilancia.query.QueryRelatorioResumoAtividadeVeterinaria;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioResumoAtividadeVeterinaria extends AbstractReport<RelatorioResumoAtividadeVeterinariaDTOParam> {

    RelatorioResumoAtividadeVeterinariaDTOParam param;

    public RelatorioResumoAtividadeVeterinaria(RelatorioResumoAtividadeVeterinariaDTOParam param) {
        super(param);
        this.param = param;
    }

    @Override
    public ITransferDataReport getQuery() {
        this.getParam().isShowFormaApresentacao();
        addParametro("formaApresentacao", this.getParam().getFormaApresentacao());
        addParametro("TIPO_RESUMO", this.getParam().getTipoResumo());
        return new QueryRelatorioResumoAtividadeVeterinaria();
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_resumo_atividade_veterinaria.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_resumo_registro_atividade");
    }
}
