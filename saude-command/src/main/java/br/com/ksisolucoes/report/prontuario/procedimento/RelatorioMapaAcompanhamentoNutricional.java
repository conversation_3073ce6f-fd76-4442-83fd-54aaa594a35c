package br.com.ksisolucoes.report.prontuario.procedimento;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioMapaAcompanhamentoNutricionalDTO;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioMapaAcompanhamentoNutricionalDTOParam;
import br.com.ksisolucoes.report.prontuario.procedimento.query.QueryMapaAcompanhamentoNutricional;
import br.com.ksisolucoes.util.Bundle;
import ch.lambdaj.Lambda;
import java.util.LinkedHashMap;

/**
 *
 * <AUTHOR>
 */
public class RelatorioMapaAcompanhamentoNutricional extends AbstractReport<RelatorioMapaAcompanhamentoNutricionalDTOParam> {

    public RelatorioMapaAcompanhamentoNutricional(RelatorioMapaAcompanhamentoNutricionalDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryMapaAcompanhamentoNutricional() {
            @Override
            public void addParameter(String key, Object object) {
                addParametro(key, object);
            }
        };
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> columns = new LinkedHashMap<>();
        RelatorioMapaAcompanhamentoNutricionalDTO proxy = Lambda.on(RelatorioMapaAcompanhamentoNutricionalDTO.class);
        
        columns.put((Bundle.getStringApplication("rotulo_sisvan")), proxy.getSisvan().getCodigo());
        columns.put((Bundle.getStringApplication("rotulo_data_acompanhamento")), proxy.getSisvan().getDataAcompanhamento());
        columns.put((Bundle.getStringApplication("rotulo_peso")), proxy.getSisvan().getPeso());
        columns.put((Bundle.getStringApplication("rotulo_altura")), proxy.getSisvan().getAltura());
        columns.put((Bundle.getStringApplication("rotulo_peso_nascer")), proxy.getSisvan().getPesoNascer());
        columns.put((Bundle.getStringApplication("rotulo_peso_pre_gestacional")), proxy.getSisvan().getPesoPreGestacional());
        columns.put((Bundle.getStringApplication("rotulo_dum")), proxy.getSisvan().getDum());
        columns.put((Bundle.getStringApplication("rotulo_tipo_alimentacao")), proxy.getSisvanAlimentacao().getDescricaoFormatada());
        columns.put((Bundle.getStringApplication("rotulo_doenca")), proxy.getSisvanDoenca().getDescricaoFormatada());
        columns.put((Bundle.getStringApplication("rotulo_intercorrencia")), proxy.getSisvanIntercorrencia().getDescricaoFormatada());
        columns.put((Bundle.getStringApplication("rotulo_cnes")), proxy.getEmpresa().getCnes());
        columns.put((Bundle.getStringApplication("rotulo_unidade")), proxy.getEmpresa().getDescricao());
        columns.put((Bundle.getStringApplication("rotulo_profissional")), proxy.getProfissional().getNome());
        columns.put((Bundle.getStringApplication("rotulo_microarea")), proxy.getMicroArea());
        columns.put((Bundle.getStringApplication("rotulo_paciente")), proxy.getUsuarioCadsus().getNome());
        columns.put((Bundle.getStringApplication("rotulo_sexo")), proxy.getUsuarioCadsus().getSexoFormatado());
        columns.put((Bundle.getStringApplication("rotulo_data_nascimento")), proxy.getUsuarioCadsus().getDataNascimento());
        
        return columns;
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_mapa_acompanhamento_nutricional.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_mapa_acompanhamento_nutricional");
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return this.param.getTipoArquivo();
    }
}
