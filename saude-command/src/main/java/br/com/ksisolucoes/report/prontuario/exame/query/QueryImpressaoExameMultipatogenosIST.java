package br.com.ksisolucoes.report.prontuario.exame.query;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.prontuario.exame.interfaces.dto.ImpressaoExameMultipatogenoISTDTO;
import br.com.ksisolucoes.report.prontuario.exame.interfaces.dto.ImpressaoRequisicaoMultipatogenosISTDTOParam;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import org.hibernate.Session;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.text.SimpleDateFormat;

/**
 *
 * <AUTHOR>
 */
public class QueryImpressaoExameMultipatogenosIST extends CommandQuery implements ITransferDataReport<ImpressaoRequisicaoMultipatogenosISTDTOParam, ImpressaoExameMultipatogenoISTDTO> {

    private ImpressaoRequisicaoMultipatogenosISTDTOParam param;
    private List<ImpressaoExameMultipatogenoISTDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(ImpressaoExameMultipatogenoISTDTO.class.getName());

        hql.addToSelect("requisicaoMultipatogenosIST.motivoSolicitacao"     , "requisicaoMultipatogenosIST.motivoSolicitacao"  );
        hql.addToSelect("requisicaoMultipatogenosIST.motivosOutros"     , "requisicaoMultipatogenosIST.motivosOutros"  );
        hql.addToSelect("requisicaoMultipatogenosIST.dataCadastro"     , "requisicaoMultipatogenosIST.dataCadastro"  );

        hql.addToSelect("usuarioCadsus.dataNascimento"    , "usuarioCadsus.dataNascimento"   );
        hql.addToSelect("usuarioCadsus.prontuario"        , "usuarioCadsus.prontuario"       );
        hql.addToSelect("usuarioCadsus.telefone"          , "usuarioCadsus.telefone"         );
        hql.addToSelect("usuarioCadsus.nomeMae"           , "usuarioCadsus.nomeMae"          );
        hql.addToSelect("usuarioCadsus.apelido"           , "usuarioCadsus.apelido"          );
        hql.addToSelect("usuarioCadsus.utilizaNomeSocial" , "usuarioCadsus.utilizaNomeSocial");
        hql.addToSelect("usuarioCadsus.codigo"            , "usuarioCadsus.codigo"           );
        hql.addToSelect("usuarioCadsus.sexo"              , "usuarioCadsus.sexo"             );
        hql.addToSelect("usuarioCadsus.nome"              , "usuarioCadsus.nome"             );
        hql.addToSelect("usuarioCadsus.cpf"               , "usuarioCadsus.cpf"              );
        hql.addToSelect("usuarioCadsus.rg"                , "usuarioCadsus.rg"               );

        hql.addToSelect("raca.codigo"      , "usuarioCadsus.raca.codigo"     );
        hql.addToSelect("raca.descricao"   , "usuarioCadsus.raca.descricao"  );

        hql.addToSelect("etniaIndigena.codigo",         "usuarioCadsus.etniaIndigena.codigo");
        hql.addToSelect("etniaIndigena.descricao",      "usuarioCadsus.etniaIndigena.descricao");

        hql.addToSelect("cidadeEmpresaSolic.descricao"      , "empresaSolicitante.cidade.descricao"  );
        hql.addToSelect("cidadeEmpresaSolic.codigo"         , "empresaSolicitante.cidade.codigo"     );
        hql.addToSelect("empresaSolicitante.descricao"      , "empresaSolicitante.descricao"         );
        hql.addToSelect("empresaSolicitante.telefone"       , "empresaSolicitante.telefone"          );
        hql.addToSelect("empresaSolicitante.codigo"         , "empresaSolicitante.codigo"            );
        hql.addToSelect("empresaSolicitante.cnes"           , "empresaSolicitante.cnes"              );
        hql.addToSelect("empresaSolicitante.cnpj"           , "empresaSolicitante.cnpj"              );

        hql.addToSelect("tipoLogradouro.descricao"                   , "enderecoUsuarioCadsus.tipoLogradouro.descricao"   );
        hql.addToSelect("tipoLogradouro.codigo"                      , "enderecoUsuarioCadsus.tipoLogradouro.codigo"      );
        hql.addToSelect("tipoLogradouro.sigla"                       , "enderecoUsuarioCadsus.tipoLogradouro.sigla"       );
        hql.addToSelect("enderecoUsuarioCadsus.numeroLogradouro"     , "enderecoUsuarioCadsus.numeroLogradouro"           );
        hql.addToSelect("enderecoUsuarioCadsus.complementoLogradouro", "enderecoUsuarioCadsus.complementoLogradouro"      );
        hql.addToSelect("enderecoUsuarioCadsus.logradouro"           , "enderecoUsuarioCadsus.logradouro"                 );
        hql.addToSelect("enderecoUsuarioCadsus.bairro"               , "enderecoUsuarioCadsus.bairro"                     );
        hql.addToSelect("enderecoUsuarioCadsus.cep"                  , "enderecoUsuarioCadsus.cep"                        );
        hql.addToSelect("enderecoUsuarioCadsus.codigo"                  , "enderecoUsuarioCadsus.codigo"                  );

        hql.addToSelect("est.codigo"                        , "usuarioCadsus.municipioResidencia.estado.codigo"    );
        hql.addToSelect("est.sigla"                         , "usuarioCadsus.municipioResidencia.estado.sigla"     );
        hql.addToSelect("municipioResidencia.descricao"     , "usuarioCadsus.municipioResidencia.descricao"        );
        hql.addToSelect("municipioResidencia.codigo"        , "usuarioCadsus.municipioResidencia.codigo"           );

        hql.addToSelect("estadoNascimento.codigo"           , "usuarioCadsus.cidadeNascimento.estado.codigo"       );
        hql.addToSelect("estadoNascimento.sigla"            , "usuarioCadsus.cidadeNascimento.estado.sigla"        );
        hql.addToSelect("cidadeNascimento.descricao"        , "usuarioCadsus.cidadeNascimento.descricao"           );
        hql.addToSelect("paisNascimento.descricao"          , "usuarioCadsus.paisNascimento.descricao"             );
        hql.addToSelect("cidadeNascimento.codigo"           , "usuarioCadsus.cidadeNascimento.codigo"              );
        hql.addToSelect("paisNascimento.codigo"             , "usuarioCadsus.paisNascimento.codigo"                );

        hql.addToSelect("cidade.descricao"     , "enderecoUsuarioCadsus.cidade.descricao"          );
        hql.addToSelect("cidade.codigo"        , "enderecoUsuarioCadsus.cidade.codigo"             );
        hql.addToSelect("estado.descricao"     , "enderecoUsuarioCadsus.cidade.estado.descricao"   );
        hql.addToSelect("estado.codigo"        , "enderecoUsuarioCadsus.cidade.estado.codigo"      );
        hql.addToSelect("estado.sigla"         , "enderecoUsuarioCadsus.cidade.estado.sigla"       );

        hql.addToSelect("profissional.numeroRegistro"  ,    "profissional.numeroRegistro"       );
        hql.addToSelect("profissional.referencia"      ,    "profissional.referencia"           );
        hql.addToSelect("profissional.codigoCns"       ,    "profissional.codigoCns"            );
        hql.addToSelect("profissional.codigo"          ,    "profissional.codigo"               );
        hql.addToSelect("profissional.nome"            ,    "profissional.nome"                 );
        hql.addToSelect("profissional.cpf"             ,    "profissional.cpf"                  );
        hql.addToSelect("conselhoClasse.sigla"         ,    "profissional.conselhoClasse.sigla" );

        hql.addToSelect("(select ucd.dum               from UsuarioCadsusDado ucd where ucd.codigo             = usuarioCadsus.codigo  )", "usuarioCadsusDado.dum"  );
        hql.addToSelect("(select ucd.gestante          from UsuarioCadsusDado ucd where ucd.codigo             = usuarioCadsus.codigo  )", "gestante"          );
        hql.addToSelect("(select uce.nivelEscolaridade from UsuarioCadsusEsus uce where uce.usuarioCadsus      = usuarioCadsus         )", "escolaridade"      );
        hql.addToSelect("(select uce.identidadeGenero from UsuarioCadsusEsus uce where uce.usuarioCadsus      = usuarioCadsus         )", "identidadeGenero"      );
        hql.addToSelect("(select uce.orientacaoSexual from UsuarioCadsusEsus uce where uce.usuarioCadsus      = usuarioCadsus         )", "orientacaoSexual"      );

        hql.addToFrom("RequisicaoMultipatogenosIST requisicaoMultipatogenosIST"
                + " left join requisicaoMultipatogenosIST.exameRequisicao exameRequisicao"
                + " left join exameRequisicao.exame exame"
                + " left join exame.usuarioCadsus usuarioCadsus"
                + " left join usuarioCadsus.raca raca"
                + " left join usuarioCadsus.etniaIndigena etniaIndigena "
                + " left join usuarioCadsus.municipioResidencia municipioResidencia "
                + " left join municipioResidencia.estado est "
                + " left join usuarioCadsus.enderecoUsuarioCadsus enderecoUsuarioCadsus"
                + " left join enderecoUsuarioCadsus.tipoLogradouro tipoLogradouro"
                + " left join enderecoUsuarioCadsus.cidade cidade"
                + " left join usuarioCadsus.paisNascimento paisNascimento "
                + " left join usuarioCadsus.cidadeNascimento cidadeNascimento "
                + " left join cidadeNascimento.estado estadoNascimento "
                + " left join cidade.estado estado"
                + " left join exame.profissional profissional"
                + " left join profissional.conselhoClasse conselhoClasse"
                + " left join exame.atendimento atendimento"
                + " left join exame.empresaSolicitante empresaSolicitante"
                + " left join empresaSolicitante.cidade cidadeEmpresaSolic"
        );

        hql.addToWhereWhithAnd("exame.status     <> ", Exame.STATUS_CANCELADO);
        hql.addToWhereWhithAnd("exame.codigo     =  ", this.param.getCodigoExame());
        hql.addToWhereWhithAnd("atendimento      =  ", this.param.getAtendimento());
    }

    @Override
    public void setDTOParam(ImpressaoRequisicaoMultipatogenosISTDTOParam param) {
        this.param = param;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
        if (CollectionUtils.isNotNullEmpty(this.result)) {
            for (ImpressaoExameMultipatogenoISTDTO dto : this.result) {
                if (dto.getRequisicaoMultipatogenosIST() != null && 
                    dto.getRequisicaoMultipatogenosIST().getDataCadastro() != null) {
                    SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
                    String dataFormatada = sdf.format(dto.getRequisicaoMultipatogenosIST().getDataCadastro());
                    dto.setDataSolicitacaoFormatada(dataFormatada);
                }
            }
        }
    }

    @Override
    public Collection<ImpressaoExameMultipatogenoISTDTO> getResult() {
        return result;
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(result)) {
            for(ImpressaoExameMultipatogenoISTDTO dto : result) {
                if (dto.getUsuarioCadsusDado() != null) {
                    dto.setIdadeGestacional(Data.calcularTrimestreGestacional(dto.getUsuarioCadsusDado().getDum(), DataUtil.getDataAtual()));
                }
            }
        }
    }
}
