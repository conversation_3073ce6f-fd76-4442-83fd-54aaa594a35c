/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.entrada.estoque;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioProdutoParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperExportManager;

/**
 *
 * <AUTHOR>
 */
public class RelatorioProdutoWeb extends AbstractCommandTransaction {

    private byte[] pdf;
    private RelatorioProdutoParam param;

    public RelatorioProdutoWeb(RelatorioProdutoParam param) {
        this.param = param;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        try {
            this.param.setProduto((Produto) getSession().get(Produto.class, this.param.getProduto().getCodigo()));

            DataReport dataReport = BOFactory.getBO(EstoqueReportFacade.class).relatorioProduto(this.param);
            pdf = JasperExportManager.exportReportToPdf(dataReport.getJasperPrint());
        } catch (ReportException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (JRException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    public byte[] getPdf() {
        return pdf;
    }

}
