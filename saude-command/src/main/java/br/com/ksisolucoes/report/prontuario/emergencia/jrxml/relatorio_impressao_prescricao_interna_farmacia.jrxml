<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_impressao_prescricao_interna" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="177911e4-d904-4868-ab85-22e162f3998f">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.1961500000000282"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="240"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.TipoReceita"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.celk.util.Coalesce"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<subDataset name="dataset1" uuid="3ac42309-1cbb-4141-a4cb-************">
		<parameter name="COLUMN_COUNT_PARAM" class="java.lang.Integer"/>
		<field name="produto" class="br.com.ksisolucoes.vo.entradas.estoque.Produto"/>
		<field name="unidade" class="br.com.ksisolucoes.vo.entradas.estoque.Unidade"/>
		<field name="quantidade" class="java.lang.Double"/>
		<field name="quantidadePrescrita" class="java.lang.Long"/>
	</subDataset>
	<subDataset name="dataset2" uuid="de04ea07-27ed-4742-8c76-2576513b5758">
		<field name="produto" class="br.com.ksisolucoes.vo.entradas.estoque.Produto"/>
		<field name="quantidadePrescrita" class="java.lang.Long"/>
		<field name="lstReceituarioItemComponente" class="java.util.List"/>
	</subDataset>
	<parameter name="caminhoImagem" class="java.lang.String" isForPrompting="false"/>
	<parameter name="exibirCabecalho" class="java.lang.Boolean"/>
	<parameter name="exibirPosologiaColuna" class="java.lang.Boolean"/>
	<field name="codigo" class="java.lang.Long"/>
	<field name="receituario" class="br.com.ksisolucoes.vo.prontuario.basico.Receituario"/>
	<field name="tipoViaMedicamento" class="br.com.ksisolucoes.vo.entradas.estoque.TipoViaMedicamento"/>
	<field name="posologia" class="java.lang.String"/>
	<field name="quantidadePrescrita" class="java.lang.Long"/>
	<field name="descricaoReceituario" class="java.lang.String"/>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus">
		<fieldDescription><![CDATA[receituario.atendimento.usuarioCadsus]]></fieldDescription>
	</field>
	<field name="convenio" class="br.com.ksisolucoes.vo.prontuario.basico.Convenio">
		<fieldDescription><![CDATA[receituario.atendimento.convenio]]></fieldDescription>
	</field>
	<field name="dataChegada" class="java.util.Date">
		<fieldDescription><![CDATA[receituario.atendimento.atendimentoPrincipal.dataChegada]]></fieldDescription>
	</field>
	<field name="setor" class="br.com.ksisolucoes.vo.basico.Empresa">
		<fieldDescription><![CDATA[receituario.atendimento.empresa]]></fieldDescription>
	</field>
	<field name="leito" class="br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto">
		<fieldDescription><![CDATA[receituario.atendimento.leitoQuarto]]></fieldDescription>
	</field>
	<field name="quarto" class="br.com.ksisolucoes.vo.prontuario.hospital.QuartoInternacao">
		<fieldDescription><![CDATA[receituario.atendimento.leitoQuarto.quartoInternacao]]></fieldDescription>
	</field>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional">
		<fieldDescription><![CDATA[receituario.profissional]]></fieldDescription>
	</field>
	<field name="produto" class="br.com.ksisolucoes.vo.entradas.estoque.Produto"/>
	<field name="lstCuidados" class="java.util.List"/>
	<field name="evolucaoProntuario" class="br.com.ksisolucoes.vo.prontuario.basico.EvolucaoProntuario"/>
	<field name="eloTipoDietaReceituario" class="br.com.ksisolucoes.vo.prontuario.basico.EloTipoDietaReceituario"/>
	<field name="lstProdutoPadrao" class="java.util.List"/>
	<field name="nomeProduto" class="java.lang.String"/>
	<field name="usuarioCadsusDado" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado"/>
	<field name="lstReceituarioItemComponente" class="java.util.List"/>
	<field name="quantidade" class="java.lang.Double"/>
	<field name="descricaoIntervalo" class="java.lang.String"/>
	<field name="exibirMedicamentosSolucoes" class="java.lang.Boolean"/>
	<field name="kitList" class="java.util.List"/>
	<field name="tipoItem" class="java.lang.Long"/>
	<field name="horariosAprazamento" class="java.lang.String"/>
	<field name="justificativa" class="java.lang.String"/>
	<field name="flagPadronizado" class="java.lang.Long"/>
	<field name="riscoProduto" class="java.lang.String"/>
	<group name="geral" isStartNewPage="true">
		<groupExpression><![CDATA[$F{receituario}.getCodigo()]]></groupExpression>
		<groupHeader>
			<band height="98">
				<rectangle radius="10">
					<reportElement x="0" y="29" width="535" height="68" uuid="4d1592a0-e7de-457b-a721-5302fe8b2cb4"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-4" positionType="Float" x="6" y="70" width="74" height="12" uuid="91fcdbd7-1936-471d-b3a0-ee0de15f5c65"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Data prescricao: */Bundle.getStringApplication("rotulo_data_prescricao")+":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="6" y="34" width="44" height="12" uuid="bbb1b902-3b3c-46cf-a6cf-3aec0e4be2e2"/>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_paciente")+":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="226" y="82" width="48" height="12" uuid="bbb1b902-3b3c-46cf-a6cf-3aec0e4be2e2"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Convenio:*/ Bundle.getStringApplication("rotulo_convenio") + ": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-4" positionType="Float" x="243" y="70" width="31" height="12" uuid="91fcdbd7-1936-471d-b3a0-ee0de15f5c65"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Leito:*/ Bundle.getStringApplication("rotulo_leito") + ": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-4" positionType="Float" x="6" y="58" width="60" height="12" uuid="91fcdbd7-1936-471d-b3a0-ee0de15f5c65"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Profissional*/Bundle.getStringApplication("rotulo_profissional")+":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-4" positionType="Float" x="273" y="70" width="110" height="12" uuid="91fcdbd7-1936-471d-b3a0-ee0de15f5c65"/>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{leito}.getDescricaoQuarto()]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy" isBlankWhenNull="false">
					<reportElement key="textField-4" positionType="Float" x="79" y="70" width="76" height="12" uuid="91fcdbd7-1936-471d-b3a0-ee0de15f5c65"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{receituario}.getDataReceituario()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="6" y="46" width="30" height="12" uuid="bbb1b902-3b3c-46cf-a6cf-3aec0e4be2e2"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Idade*/Bundle.getStringApplication("rotulo_idade")+":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="163" y="46" width="31" height="12" uuid="bbb1b902-3b3c-46cf-a6cf-3aec0e4be2e2"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Sexo:*/Bundle.getStringApplication("rotulo_sexo") + ": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-4" positionType="Float" x="6" y="82" width="30" height="12" uuid="91fcdbd7-1936-471d-b3a0-ee0de15f5c65"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Setor*/Bundle.getStringApplication("rotulo_setor")+":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="273" y="82" width="110" height="12" uuid="bbb1b902-3b3c-46cf-a6cf-3aec0e4be2e2"/>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{convenio}.getDescricao()]]></textFieldExpression>
				</textField>
				<componentElement>
					<reportElement x="396" y="44" width="129" height="50" uuid="9a5dc30a-5a8d-4d4f-9c85-343cddbb64ca"/>
					<jr:Code128 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" moduleWidth="1.3">
						<jr:codeExpression><![CDATA[new java.text.DecimalFormat("0000000000").format($F{receituario}.getCodigo())]]></jr:codeExpression>
					</jr:Code128>
				</componentElement>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="49" y="34" width="194" height="12" uuid="760fbda1-91a8-4bbd-b452-f28428b7bfa7"/>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{receituario}.getAtendimento().getUsuarioCadsus().getDescricaoFormatado()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="35" y="46" width="125" height="12" uuid="c3d9546e-223e-427c-9587-d05baf8896b5"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getDescricaoIdade()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="193" y="46" width="50" height="12" uuid="a754e978-bc69-4cca-a9e8-a7c40c2fdcca"/>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getSexoFormatado()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-4" positionType="Float" x="35" y="82" width="191" height="12" uuid="ab281384-ba8e-4bf0-981c-13ad22e308c0"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{setor}.getDescricao()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-4" positionType="Float" x="65" y="58" width="318" height="12" uuid="3874108b-e4f8-4a88-8dbc-a24d46dfa42c"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{profissional}.getNome()
+ ($F{profissional}.getRegistroFormatado() != null
   ? " - " + $F{profissional}.getRegistroFormatado()
   : ""
    )]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-4" positionType="Float" x="159" y="70" width="35" height="12" uuid="301a87b3-982b-497e-9d1f-4d8fb650ed5c"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Atend:*/ Bundle.getStringApplication("rotulo_atendimento_abv")+":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-4" positionType="Float" x="193" y="70" width="50" height="12" uuid="ef593b42-dbe9-420f-a6e5-5a1017b9b48d"/>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{receituario}.getAtendimento().getAtendimentoPrincipal().getCodigo()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-4" positionType="Float" x="243" y="46" width="31" height="12" uuid="0e4a8a3d-b142-424f-ae2e-f1101f73033f"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Peso:*/ Bundle.getStringApplication("rotulo_peso") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00" isBlankWhenNull="true">
					<reportElement key="textField-4" positionType="Float" x="273" y="46" width="55" height="12" uuid="6e5822c0-2093-4df3-b1e8-c8e81dc395b6"/>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsusDado}.getPeso() != null
? $F{usuarioCadsusDado}.getPeso() + " Kg"
: null]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="0" width="535" height="20" uuid="085a972b-6cc8-4438-b702-8d556dc0bd17"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="16"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{receituario}.getTipoReceita().getTipoReceita().equals(TipoReceita.RECEITA_SOLICITACAO_MATERIAIS)
?
Bundle.getStringApplication("rotulo_solicitacao_materiais")
:
Bundle.getStringApplication("rotulo_prescricao_paciente_farmacia")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-4" positionType="Float" x="249" y="34" width="43" height="12" uuid="280b0112-57ac-4c63-9fa6-c5aba7e930c7"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Dt.Nasc.:*/ Bundle.getStringApplication("rotulo_data_nascimento_abv2") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
					<reportElement key="textField-4" positionType="Float" x="292" y="34" width="91" height="12" uuid="8d090cd4-3441-4e87-bab4-842587096d46"/>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{receituario}.getAtendimento().getUsuarioCadsus().getDataNascimento()]]></textFieldExpression>
				</textField>
			</band>
			<band height="28">
				<printWhenExpression><![CDATA[$F{exibirMedicamentosSolucoes} && ($P{exibirPosologiaColuna} || $F{receituario}.getTipoReceita().getTipoReceita().equals(TipoReceita.RECEITA_SOLICITACAO_MATERIAIS))]]></printWhenExpression>
				<line>
					<reportElement positionType="Float" x="0" y="26" width="534" height="1" uuid="2856212d-9092-43f0-9cbe-8bb92df51673"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="513" y="15" width="21" height="11" uuid="bbb1b902-3b3c-46cf-a6cf-3aec0e4be2e2"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*UN*/
Bundle.getStringApplication("rotulo_un")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="408" y="15" width="78" height="11" uuid="7e7d5d27-c1e6-4fb3-93a7-6845883f5e1e">
						<printWhenExpression><![CDATA[$F{exibirMedicamentosSolucoes}]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_horarios")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="275" y="15" width="60" height="11" uuid="bbb1b902-3b3c-46cf-a6cf-3aec0e4be2e2"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{receituario}.getTipoReceita().getTipoReceita().equals(TipoReceita.RECEITA_SOLICITACAO_MATERIAIS)
?
""
:
Bundle.getStringApplication("rotulo_posologia")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="2" y="15" width="271" height="11" uuid="bbb1b902-3b3c-46cf-a6cf-3aec0e4be2e2"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_descricao")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="339" y="15" width="26" height="11" uuid="06e60ca1-e1dc-454b-bd0e-390d3d61e6c2"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{receituario}.getTipoReceita().getTipoReceita().equals(TipoReceita.RECEITA_SOLICITACAO_MATERIAIS)
?
""
:
Bundle.getStringApplication("rotulo_via")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="370" y="15" width="35" height="11" uuid="f0329123-68e8-4e77-bf84-8249109ddfce"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{receituario}.getTipoReceita().getTipoReceita().equals(TipoReceita.RECEITA_SOLICITACAO_MATERIAIS)
?
""
:
Bundle.getStringApplication("rotulo_intervalo")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="489" y="15" width="21" height="11" uuid="4a6fa2be-**************-1876e02565e4"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*quantidade*/
Bundle.getStringApplication("rotulo_quantidade_abv")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="2" y="2" width="383" height="13" uuid="bbb1b902-3b3c-46cf-a6cf-3aec0e4be2e2"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_medicamento_solucoes")]]></textFieldExpression>
				</textField>
			</band>
			<band height="28">
				<printWhenExpression><![CDATA[$F{exibirMedicamentosSolucoes} && !$P{exibirPosologiaColuna} && !$F{receituario}.getTipoReceita().getTipoReceita().equals(TipoReceita.RECEITA_SOLICITACAO_MATERIAIS)]]></printWhenExpression>
				<line>
					<reportElement positionType="Float" x="0" y="24" width="534" height="1" uuid="57a6da59-59f0-47f7-b86d-be13c38af63f"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="513" y="13" width="21" height="11" uuid="a0387aea-dd00-4823-8f8d-fc76d82048bf"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*UN*/
Bundle.getStringApplication("rotulo_un")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="408" y="13" width="78" height="11" uuid="435ca97f-4650-4daa-8827-d921165b33a5">
						<printWhenExpression><![CDATA[$F{exibirMedicamentosSolucoes}]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_horarios")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="2" y="13" width="333" height="11" uuid="2a3a799a-261c-4702-9900-704cda772762"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_descricao")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="339" y="13" width="26" height="11" uuid="497e3dcc-3c70-4031-8c5f-b244175a7d7c"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{receituario}.getTipoReceita().getTipoReceita().equals(TipoReceita.RECEITA_SOLICITACAO_MATERIAIS)
?
""
:
Bundle.getStringApplication("rotulo_via")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="370" y="13" width="35" height="11" uuid="32fc3def-9e1f-4c67-ab23-7390b419763e"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{receituario}.getTipoReceita().getTipoReceita().equals(TipoReceita.RECEITA_SOLICITACAO_MATERIAIS)
?
""
:
Bundle.getStringApplication("rotulo_intervalo")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="489" y="13" width="21" height="11" uuid="5cd90cdc-5aa2-45b3-a167-e0e135e53e1d"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*quantidade*/
Bundle.getStringApplication("rotulo_quantidade_abv")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="2" y="0" width="383" height="13" uuid="c70034b9-3631-4cea-8e55-536c251a439a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_medicamento_solucoes")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="evolucao">
		<groupExpression><![CDATA[""]]></groupExpression>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<detail>
		<band height="13" splitType="Stretch">
			<printWhenExpression><![CDATA[$F{exibirMedicamentosSolucoes} && ($P{exibirPosologiaColuna} || $F{receituario}.getTipoReceita().getTipoReceita().equals(TipoReceita.RECEITA_SOLICITACAO_MATERIAIS))]]></printWhenExpression>
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="534" height="13" backcolor="#E6E6E6" uuid="a19a2fc9-eb11-4cc5-8aa6-11fa05201de8">
					<printWhenExpression><![CDATA[$V{COLUMN_COUNT} % 2 == 0]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="19" y="1" width="254" height="11" isRemoveLineWhenBlank="true" uuid="a8c8bb57-e29e-4986-914a-5240afcc0dc7"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" pdfFontName="Courier"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{produto}.getDescricao() != null
? $F{produto}.getDescricao()
: $F{nomeProduto}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="275" y="1" width="60" height="11" isRemoveLineWhenBlank="true" uuid="a8c8bb57-e29e-4986-914a-5240afcc0dc7"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" pdfFontName="Courier"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{receituario}.getTipoReceita().getTipoReceita().equals(TipoReceita.RECEITA_SOLICITACAO_MATERIAIS)
?
""
:
$F{posologia}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="0" y="1" width="19" height="11" isRemoveLineWhenBlank="true" uuid="e47097ff-6c71-4bd0-a58c-b52dbf2f531d"/>
				<box rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Courier"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{COLUMN_COUNT} + "."]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="513" y="1" width="21" height="11" isRemoveLineWhenBlank="true" uuid="f55d1337-3e4a-4f2e-94a0-0dd2496180a1"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="8" pdfFontName="Courier"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{produto}.getUnidade().getUnidade()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="370" y="1" width="35" height="11" isRemoveLineWhenBlank="true" uuid="8cbd7cb0-9fbc-49ec-94cb-cf071e319356"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" pdfFontName="Courier"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoIntervalo}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="339" y="1" width="26" height="11" isRemoveLineWhenBlank="true" uuid="df9b6bbd-cf8b-4c02-82d6-0bb5296ba881"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" pdfFontName="Courier"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{receituario}.getTipoReceita().getTipoReceita().equals(TipoReceita.RECEITA_SOLICITACAO_MATERIAIS)
?
""
:
$F{tipoViaMedicamento}.getSigla()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="408" y="1" width="78" height="11" isRemoveLineWhenBlank="true" uuid="72b1495d-94b3-4597-ac4e-83353af8367f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" pdfFontName="Courier"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{horariosAprazamento}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="489" y="1" width="21" height="11" isRemoveLineWhenBlank="true" uuid="9affc2e1-11d8-4159-af76-558e5fc15544">
					<printWhenExpression><![CDATA[ReceituarioItem.TipoItem.MEDICAMENTO.value().equals($F{tipoItem})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="8" pdfFontName="Courier"/>
				</textElement>
				<textFieldExpression><![CDATA[Coalesce.asLong($F{quantidadePrescrita})]]></textFieldExpression>
			</textField>
		</band>
		<band height="37" splitType="Stretch">
			<printWhenExpression><![CDATA[$F{exibirMedicamentosSolucoes} && !$P{exibirPosologiaColuna} && !$F{receituario}.getTipoReceita().getTipoReceita().equals(TipoReceita.RECEITA_SOLICITACAO_MATERIAIS)]]></printWhenExpression>
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="534" height="37" backcolor="#E6E6E6" uuid="a19a2fc9-eb11-4cc5-8aa6-11fa05201de8">
					<printWhenExpression><![CDATA[$V{COLUMN_COUNT} % 2 == 0]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="19" y="1" width="316" height="11" isRemoveLineWhenBlank="true" uuid="a8c8bb57-e29e-4986-914a-5240afcc0dc7"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" pdfFontName="Courier"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{produto}.getDescricao() != null
? $F{produto}.getDescricao()
: $F{nomeProduto}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="62" y="12" width="472" height="11" isRemoveLineWhenBlank="true" uuid="a8c8bb57-e29e-4986-914a-5240afcc0dc7"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" pdfFontName="Courier"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{posologia}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="0" y="1" width="19" height="11" isRemoveLineWhenBlank="true" uuid="e47097ff-6c71-4bd0-a58c-b52dbf2f531d"/>
				<box rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Courier"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{COLUMN_COUNT} + "."]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="513" y="1" width="21" height="11" isRemoveLineWhenBlank="true" uuid="f55d1337-3e4a-4f2e-94a0-0dd2496180a1"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="8" pdfFontName="Courier"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{produto}.getUnidade().getUnidade()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="370" y="1" width="35" height="11" isRemoveLineWhenBlank="true" uuid="8cbd7cb0-9fbc-49ec-94cb-cf071e319356"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" pdfFontName="Courier"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoIntervalo}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="339" y="1" width="26" height="11" isRemoveLineWhenBlank="true" uuid="df9b6bbd-cf8b-4c02-82d6-0bb5296ba881"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" pdfFontName="Courier"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{receituario}.getTipoReceita().getTipoReceita().equals(TipoReceita.RECEITA_SOLICITACAO_MATERIAIS)
?
""
:
$F{tipoViaMedicamento}.getSigla()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="408" y="1" width="78" height="11" isRemoveLineWhenBlank="true" uuid="72b1495d-94b3-4597-ac4e-83353af8367f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" pdfFontName="Courier"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{horariosAprazamento}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="489" y="1" width="21" height="11" isRemoveLineWhenBlank="true" uuid="9affc2e1-11d8-4159-af76-558e5fc15544">
					<printWhenExpression><![CDATA[ReceituarioItem.TipoItem.MEDICAMENTO.value().equals($F{tipoItem})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="8" pdfFontName="Courier"/>
				</textElement>
				<textFieldExpression><![CDATA[Coalesce.asLong($F{quantidadePrescrita})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" positionType="Float" mode="Transparent" x="19" y="12" width="43" height="11" isRemoveLineWhenBlank="true" uuid="636006a4-82ee-4400-b9bb-************">
					<printWhenExpression><![CDATA[$F{exibirMedicamentosSolucoes}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_posologia") + ": "]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="19" y="23" width="515" height="11" isRemoveLineWhenBlank="true" forecolor="#FF0000" uuid="a7593771-761b-466f-9d50-9437e5a830e8">
					<printWhenExpression><![CDATA[$F{riscoProduto} != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Courier"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{riscoProduto}]]></textFieldExpression>
			</textField>
		</band>
		<band height="12">
			<printWhenExpression><![CDATA[$F{exibirMedicamentosSolucoes} &&
(RepositoryComponentDefault.NAO.equals($F{produto}.getFlagPadronizado())
|| RepositoryComponentDefault.NAO_LONG.equals($F{flagPadronizado}))]]></printWhenExpression>
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="534" height="12" backcolor="#E6E6E6" uuid="aa3b5c77-f789-4722-82ea-7f76d6867651">
					<printWhenExpression><![CDATA[$V{COLUMN_COUNT} % 2 == 0]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement key="textField" positionType="Float" mode="Transparent" x="62" y="1" width="472" height="11" uuid="5dd0e53f-e863-44e3-989d-8a138eb6fef0"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Medicamento nao padronizado*/Bundle.getStringApplication("rotulo_medicamento_nao_padronizado") + ".    " + Bundle.getStringApplication("rotulo_justificativa") + ": " + $F{justificativa}]]></textFieldExpression>
			</textField>
		</band>
		<band height="12">
			<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{lstReceituarioItemComponente})
&& $F{exibirMedicamentosSolucoes}]]></printWhenExpression>
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="534" height="12" backcolor="#E6E6E6" uuid="66739e05-7136-4133-ad43-58cd18aeaf7e">
					<printWhenExpression><![CDATA[$V{COLUMN_COUNT} % 2 == 0]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<componentElement>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="534" height="12" uuid="9773a527-5a69-49c5-8067-52e57d532624"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="dataset1" uuid="bda3b09a-76bb-4f6a-926c-6ded0cbf9d2a">
						<datasetParameter name="COLUMN_COUNT_PARAM">
							<datasetParameterExpression><![CDATA[$V{COLUMN_COUNT}]]></datasetParameterExpression>
						</datasetParameter>
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource( $F{lstReceituarioItemComponente} )]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="12" width="534">
						<textField pattern="" isBlankWhenNull="true">
							<reportElement key="textField" positionType="Float" mode="Transparent" x="275" y="2" width="60" height="10" isRemoveLineWhenBlank="true" backcolor="#FFFFFF" uuid="9ab658f7-b078-4b6f-ac74-bce3fb2d1e55"/>
							<box leftPadding="0">
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textElement verticalAlignment="Top">
								<font fontName="Arial" size="7" pdfFontName="Courier"/>
							</textElement>
							<textFieldExpression><![CDATA[(Coalesce.asDouble($F{quantidade}) % 1 == 0
?
    String.valueOf(Coalesce.asDouble($F{quantidade}).longValue())
:
    String.valueOf(Coalesce.asDouble($F{quantidade}))
)
+ " " + $F{unidade}.getUnidade()]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement key="textField" positionType="Float" mode="Transparent" x="40" y="2" width="233" height="10" isRemoveLineWhenBlank="true" backcolor="#FFFFFF" uuid="df480b22-14c1-4101-b8f0-70ec1fc0d3ab"/>
							<box leftPadding="0" rightPadding="0">
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textElement textAlignment="Left" verticalAlignment="Top">
								<font fontName="Arial" size="7" pdfFontName="Courier"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{produto}.getDescricao()]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement key="textField" positionType="Float" x="0" y="2" width="39" height="10" isRemoveLineWhenBlank="true" uuid="dc2b62fc-3d33-4a38-9184-a8903f8153fd"/>
							<box rightPadding="3">
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textElement textAlignment="Right" verticalAlignment="Top">
								<font fontName="Arial" size="7" isBold="true" pdfFontName="Courier"/>
							</textElement>
							<textFieldExpression><![CDATA[$P{COLUMN_COUNT_PARAM} + "." + $V{COLUMN_COUNT} + "."]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement key="textField" positionType="Float" mode="Transparent" x="489" y="1" width="21" height="11" isRemoveLineWhenBlank="true" backcolor="#FFFFFF" uuid="a4fbea32-ccbc-483c-aaae-ae8e086fbd95"/>
							<box leftPadding="0" rightPadding="0">
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textElement textAlignment="Right" verticalAlignment="Top">
								<font fontName="Arial" size="7" pdfFontName="Courier"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{quantidadePrescrita}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement key="textField" positionType="Float" mode="Transparent" x="513" y="1" width="21" height="11" isRemoveLineWhenBlank="true" backcolor="#FFFFFF" uuid="a83d7c8f-6470-4564-bfbd-d596d8580cb7"/>
							<box leftPadding="0" rightPadding="0">
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textElement textAlignment="Right" verticalAlignment="Top">
								<font fontName="Arial" size="7" pdfFontName="Courier"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{produto}.getUnidade().getUnidade()]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
		</band>
		<band height="59">
			<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{kitList})]]></printWhenExpression>
			<componentElement>
				<reportElement x="10" y="28" width="524" height="28" uuid="cb1614f0-e7c0-4cb7-9243-f11f79cf50b6"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="dataset2" uuid="f30e0d34-f1aa-4cee-bb67-9d0b9ca763ab">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource( $F{kitList} )]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="28" width="524">
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement key="textField" positionType="Float" x="19" y="2" width="412" height="11" isRemoveLineWhenBlank="true" uuid="155e9a77-4c07-4f2a-8e8b-976892b23bbb"/>
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textElement verticalAlignment="Top">
								<font fontName="Arial" size="8" pdfFontName="Courier"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{produto}.getDescricao()]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
							<reportElement key="textField" positionType="Float" x="456" y="2" width="68" height="11" isRemoveLineWhenBlank="true" uuid="bea904ca-7618-4478-9e80-e2931480bd30"/>
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textElement textAlignment="Right" verticalAlignment="Top">
								<font fontName="Arial" size="8" pdfFontName="Courier"/>
							</textElement>
							<textFieldExpression><![CDATA[Coalesce.asLong($F{quantidadePrescrita})]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement key="textField" positionType="Float" x="0" y="2" width="19" height="11" isRemoveLineWhenBlank="true" uuid="732f0647-cf94-4ee2-811d-b6758e873a3d"/>
							<box rightPadding="3">
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textElement textAlignment="Right" verticalAlignment="Top">
								<font fontName="Arial" size="8" isBold="true" pdfFontName="Courier"/>
							</textElement>
							<textFieldExpression><![CDATA[$V{COLUMN_COUNT} + "."]]></textFieldExpression>
						</textField>
						<subreport>
							<reportElement x="0" y="15" width="524" height="12" uuid="6e7fa1ca-e1db-469c-8c7f-c0876fcf9d5c"/>
							<subreportParameter name="COLUMN_COUNT_PARAM">
								<subreportParameterExpression><![CDATA[$V{COLUMN_COUNT}]]></subreportParameterExpression>
							</subreportParameter>
							<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{lstReceituarioItemComponente})]]></dataSourceExpression>
							<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/sub_relatorio_impressao_prescricao_interna_farmacia_kit.jasper"]]></subreportExpression>
						</subreport>
					</jr:listContents>
				</jr:list>
			</componentElement>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" positionType="Float" mode="Transparent" x="0" y="0" width="193" height="13" uuid="bf2ec564-e952-4fb0-87d4-c7da86eb121a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_kit")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" positionType="Float" mode="Transparent" x="10" y="15" width="238" height="11" uuid="c8199faf-3b84-4067-947f-e2d6bf98e66a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_descricao")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" positionType="Float" mode="Transparent" x="466" y="15" width="68" height="11" uuid="6ebe8914-e4e3-4f55-9c63-7786a44dbb6e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*quantidade*/
Bundle.getStringApplication("rotulo_quantidade")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" x="10" y="26" width="525" height="1" uuid="acee2730-9e03-4ee9-ba8a-e191081f3b81"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" positionType="Float" mode="Transparent" x="441" y="15" width="21" height="11" uuid="175190d5-ea69-4098-a4ab-72154deefd4c"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*UN*/
Bundle.getStringApplication("rotulo_un")]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
