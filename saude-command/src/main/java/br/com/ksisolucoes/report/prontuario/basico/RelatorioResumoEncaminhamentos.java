package br.com.ksisolucoes.report.prontuario.basico;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioResumoEncaminhamentosDTOParam;
import br.com.ksisolucoes.report.prontuario.basico.query.QueryRelatorioresumoEncaminhamentos;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioResumoEncaminhamentos extends AbstractReport<RelatorioResumoEncaminhamentosDTOParam> {

    public RelatorioResumoEncaminhamentos(RelatorioResumoEncaminhamentosDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_resumo_encaminhamentos.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_resumo_encaminhamentos");
    }

    @Override
    public ITransferDataReport getQuery() {
        addParametro("formaApresentacao", getParam().getFormaApresentacao());
        addParametro("tipoResumo", getParam().getTipoResumo());

        return new QueryRelatorioresumoEncaminhamentos();
    }
}
