package br.com.ksisolucoes.report.prontuario.basico.query;

import br.com.ksisolucoes.bo.command.*;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioImprimirComprovanteAgendamentoDTO;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioImprimirComprovanteAgendamentoDTOParam;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.agendamento.AgendamentoProcedimento;
import br.com.ksisolucoes.vo.agendamento.PreparacaoExame;
import br.com.ksisolucoes.vo.agendamento.PreparacaoExameItem;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.TipoDocumentoUsuario;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;
import org.hamcrest.Matchers;
import org.hibernate.Query;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static ch.lambdaj.Lambda.by;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class QueryRelatorioImprimirComprovanteAgendamentoSemSolicitacao extends CommandQuery<QueryRelatorioImprimirComprovanteAgendamentoSemSolicitacao> implements ITransferDataReport<RelatorioImprimirComprovanteAgendamentoDTOParam, RelatorioImprimirComprovanteAgendamentoDTO> {

    private RelatorioImprimirComprovanteAgendamentoDTOParam param;
    private List<RelatorioImprimirComprovanteAgendamentoDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("agendaGradeAtendimentoHorario", new HQLProperties(AgendaGradeAtendimentoHorario.class, "agendaGradeAtendimentoHorario").getProperties());
        hql.addToSelect("agendaGradeAtendimentoHorario", new HQLProperties(Empresa.class, "agendaGradeAtendimentoHorario.localAgendamento").getProperties());
        hql.addToSelect("agendaGradeAtendimentoHorario.localAgendamento.cidade.estado.sigla", "agendaGradeAtendimentoHorario.localAgendamento.cidade.estado.sigla");
        hql.addToSelect("agendaGradeAtendimentoHorario.empresaOrigem.cnes", "agendaGradeAtendimentoHorario.empresaOrigem.cnes");
        hql.addToSelect("agendaGradeAtendimentoHorario.agendaGradeAtendimento.agendaGrade.agenda.recomendacoes", "agendaGradeAtendimentoHorario.agendaGradeAtendimento.agendaGrade.agenda.recomendacoes");
        hql.addToSelect("agendaGradeAtendimentoHorario.agendaGradeAtendimento.tipoAtendimentoAgenda.codigo", "agendaGradeAtendimentoHorario.agendaGradeAtendimento.tipoAtendimentoAgenda.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.agendaGradeAtendimento.tipoAtendimentoAgenda.descricao", "agendaGradeAtendimentoHorario.agendaGradeAtendimento.tipoAtendimentoAgenda.descricao");
        hql.addToSelect("agendaGradeAtendimentoHorario.observacao", "agendaGradeAtendimentoHorario.observacao");
        hql.addToSelect("agendaGradeAtendimentoHorario.profissional.codigo", "agendaGradeAtendimentoHorario.profissional.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.profissional.referencia", "agendaGradeAtendimentoHorario.profissional.referencia");
        hql.addToSelect("agendaGradeAtendimentoHorario.profissional.nome", "agendaGradeAtendimentoHorario.profissional.nome");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.nomeProfissionalOrigem", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.nomeProfissionalOrigem");
        hql.addToSelect("agendaGradeAtendimentoHorario.profissional.numeroRegistro", "agendaGradeAtendimentoHorario.profissional.numeroRegistro");
        hql.addToSelect("agendaGradeAtendimentoHorario.profissional.unidadeFederacaoConselhoRegistro", "agendaGradeAtendimentoHorario.profissional.unidadeFederacaoConselhoRegistro");
        hql.addToSelect("agendaGradeAtendimentoHorario.profissional.conselhoClasse.sigla", "agendaGradeAtendimentoHorario.profissional.conselhoClasse.sigla");
        hql.addToSelect("agendaGradeAtendimentoHorario.nomeProfissional", "agendaGradeAtendimentoHorario.nomeProfissional");
        hql.addToSelect("agendaGradeAtendimentoHorario.codigoExterno", "agendaGradeAtendimentoHorario.codigoExterno");

        hql.addToSelect("agendaGradeAtendimentoHorario.nomePaciente", "agendaGradeAtendimentoHorario.nomePaciente");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.codigo", "agendaGradeAtendimentoHorario.usuarioCadsus.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.apelido", "agendaGradeAtendimentoHorario.usuarioCadsus.apelido");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.utilizaNomeSocial", "agendaGradeAtendimentoHorario.usuarioCadsus.utilizaNomeSocial");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.referencia", "agendaGradeAtendimentoHorario.usuarioCadsus.referencia");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.nome", "agendaGradeAtendimentoHorario.usuarioCadsus.nome");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.dataNascimento", "agendaGradeAtendimentoHorario.usuarioCadsus.dataNascimento");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.nomeMae", "agendaGradeAtendimentoHorario.usuarioCadsus.nomeMae");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.telefone", "agendaGradeAtendimentoHorario.usuarioCadsus.telefone");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.telefone2", "agendaGradeAtendimentoHorario.usuarioCadsus.telefone2");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.telefone3", "agendaGradeAtendimentoHorario.usuarioCadsus.telefone3");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.telefone4", "agendaGradeAtendimentoHorario.usuarioCadsus.telefone4");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.sexo", "agendaGradeAtendimentoHorario.usuarioCadsus.sexo");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.celular", "agendaGradeAtendimentoHorario.usuarioCadsus.celular");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.cpf", "agendaGradeAtendimentoHorario.usuarioCadsus.cpf");

        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.codigo", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.numeroLogradouro", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.numeroLogradouro");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.logradouro", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.logradouro");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.bairro", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.bairro");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.cep", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.cep");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.complementoLogradouro", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.complementoLogradouro");

        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.cidade.codigo", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.cidade.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.cidade.descricao", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.cidade.descricao");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.cidade.estado.codigo", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.cidade.estado.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.cidade.estado.sigla", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.cidade.estado.sigla");

        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.tipoLogradouro.codigo", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.tipoLogradouro.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.tipoLogradouro.descricao", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoUsuarioCadsus.tipoLogradouro.descricao");

        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.codigo", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.numeroFamilia", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.numeroFamilia");

        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.codigo", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.numeroLogradouro", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.numeroLogradouro");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.logradouro", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.logradouro");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.bairro", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.bairro");

        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.tipoLogradouro.codigo", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.tipoLogradouro.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.tipoLogradouro.descricao", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.tipoLogradouro.descricao");

        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.equipeMicroArea.equipeArea.descricao", "agendaGradeAtendimentoHorario.usuarioCadsus.enderecoDomicilio.equipeMicroArea.equipeArea.descricao");
        hql.addToSelect("agendaGradeAtendimentoHorario.usuarioCadsus.equipe.referencia", "agendaGradeAtendimentoHorario.usuarioCadsus.equipe.referencia");

        hql.addToSelect("agendaGradeAtendimentoHorario.tipoProcedimento.codigo", "agendaGradeAtendimentoHorario.tipoProcedimento.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.tipoProcedimento.descricao", "agendaGradeAtendimentoHorario.tipoProcedimento.descricao");
        hql.addToSelect("agendaGradeAtendimentoHorario.tipoProcedimento.tipoAgendamento", "agendaGradeAtendimentoHorario.tipoProcedimento.tipoAgendamento");
        hql.addToSelect("agendaGradeAtendimentoHorario.tipoProcedimento.tipoProcedimentoClassificacao.codigo", "agendaGradeAtendimentoHorario.tipoProcedimento.tipoProcedimentoClassificacao.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.tipoProcedimento.tipoProcedimentoClassificacao.descricao", "agendaGradeAtendimentoHorario.tipoProcedimento.tipoProcedimentoClassificacao.descricao");
        hql.addToSelect("agendaGradeAtendimentoHorario.procedimento.codigo", "agendaGradeAtendimentoHorario.procedimento.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.procedimento.descricao", "agendaGradeAtendimentoHorario.procedimento.descricao");
        hql.addToSelect("(select ucd.numeroDocumento from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_RG + " and ucd.situacaoExcluido = 0 )", "usuarioCadsusDocumentoIdent.numeroDocumento");
        hql.addToSelect("(select ucd.numeroDocumentoComplementar from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_RG + " and ucd.situacaoExcluido = 0 )", "usuarioCadsusDocumentoIdent.numeroDocumentoComplementar");
        hql.addToSelect("(select ucd.siglaUf from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_RG + " and ucd.situacaoExcluido = 0 )", "usuarioCadsusDocumentoIdent.siglaUf");
        hql.addToSelect("(select ucd.dataEmissao from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_RG + " and ucd.situacaoExcluido = 0 )", "usuarioCadsusDocumentoIdent.dataEmissao");
        hql.addToSelect("(select ucd.orgaoEmissor.descricao from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_RG + " and ucd.situacaoExcluido = 0 )", "usuarioCadsusDocumentoIdent.orgaoEmissor.descricao");
        hql.addToSelect("(select ucd.numeroMatricula from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and (ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_CASAMENTO + " or ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO + ") and ucd.situacaoExcluido = 0 )", "usuarioCadsusDocumentoCert.numeroMatricula");
        hql.addToSelect("(select ucd.tipoDocumento.descricao from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and (ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_CASAMENTO + " or ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO + ") and ucd.situacaoExcluido = 0 )", "usuarioCadsusDocumentoCert.tipoDocumento.descricao");
        hql.addToSelect("(select ucd.numeroLivro from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and (ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_CASAMENTO + " or ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO + ") and ucd.situacaoExcluido = 0 )", "usuarioCadsusDocumentoCert.numeroLivro");
        hql.addToSelect("(select ucd.numeroFolha from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and (ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_CASAMENTO + " or ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO + ") and ucd.situacaoExcluido = 0 )", "usuarioCadsusDocumentoCert.numeroFolha");
        hql.addToSelect("(select ucd.numeroTermo from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and (ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_CASAMENTO + " or ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO + ") and ucd.situacaoExcluido = 0 )", "usuarioCadsusDocumentoCert.numeroTermo");
        hql.addToSelect("(select ucd.numeroCartorio from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and (ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_CASAMENTO + " or ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO + ") and ucd.situacaoExcluido = 0 )", "usuarioCadsusDocumentoCert.numeroCartorio");
        hql.addToSelect("(select ucd.dataEmissao from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and (ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_CASAMENTO + " or ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO + ") and ucd.situacaoExcluido = 0 )", "usuarioCadsusDocumentoCert.dataEmissao");
        hql.addToSelect("(select min(ucc.numeroCartao) from UsuarioCadsusCns ucc where ucc.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and ucc.excluido = 0 )", "usuarioCadsusCns.numeroCartao");
        hql.addToSelect("(select min(ucp.numeroProntuario) from UsuarioCadsusProntuario ucp where id.usuarioCadsus = agendaGradeAtendimentoHorario.usuarioCadsus and id.empresa = agendaGradeAtendimentoHorario.localAgendamento )", "numeroProntuario");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.observacao", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.observacao");
        hql.addToSelect("(select min(t1.exame.codigo) from ExameRequisicao t1 where t1.solicitacaoAgendamento.codigo is not null and t1.solicitacaoAgendamento.codigo = agendaGradeAtendimentoHorario.solicitacaoAgendamento.codigo)", "exameRequisicao");

        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.profissional.nome","agendaGradeAtendimentoHorario.solicitacaoAgendamento.profissional.nome");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.profissional.conselhoClasse.sigla", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.profissional.conselhoClasse.sigla");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.profissional.numeroRegistro","agendaGradeAtendimentoHorario.solicitacaoAgendamento.profissional.numeroRegistro");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.profissional.unidadeFederacaoConselhoRegistro","agendaGradeAtendimentoHorario.solicitacaoAgendamento.profissional.unidadeFederacaoConselhoRegistro");

        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.empresa.descricao","agendaGradeAtendimentoHorario.solicitacaoAgendamento.empresa.descricao");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.empresa.sigla","agendaGradeAtendimentoHorario.solicitacaoAgendamento.empresa.sigla");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.empresa.cnes","agendaGradeAtendimentoHorario.solicitacaoAgendamento.empresa.cnes");

        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.procedimento.codigo","agendaGradeAtendimentoHorario.solicitacaoAgendamento.procedimento.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.procedimento.descricao","agendaGradeAtendimentoHorario.solicitacaoAgendamento.procedimento.descricao");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.procedimento.referencia","agendaGradeAtendimentoHorario.solicitacaoAgendamento.procedimento.referencia");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.tipoProcedimento.tipoProcedimentoClassificacao.codigo","agendaGradeAtendimentoHorario.solicitacaoAgendamento.tipoProcedimento.tipoProcedimentoClassificacao.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.tipoProcedimento.tipoProcedimentoClassificacao.descricao","agendaGradeAtendimentoHorario.solicitacaoAgendamento.tipoProcedimento.tipoProcedimentoClassificacao.descricao");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.tipoProcedimento.codigo","agendaGradeAtendimentoHorario.solicitacaoAgendamento.tipoProcedimento.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.solicitacaoAgendamento.tipoProcedimento.descricao","agendaGradeAtendimentoHorario.solicitacaoAgendamento.tipoProcedimento.descricao");


        hql.setConvertToLeftJoin(true);

        hql.setTypeSelect(RelatorioImprimirComprovanteAgendamentoDTO.class.getName());
        hql.addToFrom("AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario");

        if (param.getAgendaGradeAtendimentoHorario() != null) {
            hql.addToWhereWhithAnd("agendaGradeAtendimentoHorario = ", param.getAgendaGradeAtendimentoHorario());
        }
        if (CollectionUtils.isNotNullEmpty(param.getAgendaGradeAtendimentoHorarioList())) {
            hql.addToWhereWhithAnd("agendaGradeAtendimentoHorario in ", param.getAgendaGradeAtendimentoHorarioList());
        }
    }

    @Override
    protected void customQuery(Query query) {
        query.setMaxResults(1);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        for (RelatorioImprimirComprovanteAgendamentoDTO dto : result) {
            dto.setAgendaGradeAtendimentoHorarioList(new QueryRelatorioImprimirComprovanteAgendamentoSemSolicitacao.QueryRelatorioComprovanteDataAgendamento().start().getResult());
            if (TipoProcedimento.TIPO_PROCEDIMENTO_FORA_DA_REDE.equals(dto.getAgendaGradeAtendimentoHorario().getTipoProcedimento().getTipoAgendamento())) {
                dto.getAgendaGradeAtendimentoHorario().getAgendaGradeAtendimento().getAgendaGrade().getAgenda().setRecomendacoes(dto.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento().getObservacao());
            }
            if (CollectionUtils.isNotNullEmpty(dto.getAgendaGradeAtendimentoHorarioList()) && dto.getAgendaGradeAtendimentoHorarioList().size() >= 20) {
                dto.setAgendaGradeAtendimentoHorarioList(dto.getAgendaGradeAtendimentoHorarioList().subList(0, 20));
            }
            List<AgendamentoProcedimento> exameList = LoadManager.getInstance(AgendamentoProcedimento.class)
                    .addProperties(new HQLProperties(AgendamentoProcedimento.class).getProperties())
                    .addProperties(new HQLProperties(ExameProcedimento.class, AgendamentoProcedimento.PROP_EXAME_PROCEDIMENTO).getProperties())
                    .addProperties(new HQLProperties(Procedimento.class, VOUtils.montarPath(AgendamentoProcedimento.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_PROCEDIMENTO)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(AgendamentoProcedimento.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, dto.getAgendaGradeAtendimentoHorario()))
                    .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(AgendamentoProcedimento.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_PROCEDIMENTO, Procedimento.PROP_DESCRICAO), BuilderQueryCustom.QuerySorter.CRESCENTE))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(exameList)) {
                dto.setExameList(exameList);
                List<ExameProcedimento> exameProcedimentoList = Lambda.extract(exameList, on(AgendamentoProcedimento.class).getExameProcedimento());
                List<PreparacaoExameItem> listPreparacaoExameItem = LoadManager.getInstance(PreparacaoExameItem.class)
                        .addProperty(VOUtils.montarPath(PreparacaoExameItem.PROP_EXAME_PROCEDIMENTO))
                        .addProperty(VOUtils.montarPath(PreparacaoExameItem.PROP_PREPARACAO_EXAME, PreparacaoExame.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(PreparacaoExameItem.PROP_PREPARACAO_EXAME, PreparacaoExame.PROP_DESCRICAO))
                        .addProperty(VOUtils.montarPath(PreparacaoExameItem.PROP_PREPARACAO_EXAME, PreparacaoExame.PROP_EMPRESA))
                        .addProperty(VOUtils.montarPath(PreparacaoExameItem.PROP_PREPARACAO_EXAME, PreparacaoExame.PROP_OBSERVACAO))
                        .addParameter(new QueryCustom.QueryCustomParameter(PreparacaoExameItem.PROP_EXAME_PROCEDIMENTO, BuilderQueryCustom.QueryParameter.IN, exameProcedimentoList))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PreparacaoExameItem.PROP_PREPARACAO_EXAME, PreparacaoExame.PROP_EMPRESA), dto.getAgendaGradeAtendimentoHorario().getLocalAgendamento()))
                        .start().getList();
                if (CollectionUtils.isNotNullEmpty(listPreparacaoExameItem)) {
                    List<String> preparacoesList = new ArrayList<>();
                    Group<PreparacaoExameItem> groupByPreparacao = Lambda.group(listPreparacaoExameItem, by(on(PreparacaoExameItem.class).getPreparacaoExame()));
                    List<PreparacaoExameItem> preparacaoExameItemList;
                    List<String> descricaoExameList;
                    String descricaoExame;
                    for (Group<PreparacaoExameItem> group : groupByPreparacao.subgroups()) {
                        PreparacaoExameItem preparacaoExameItem = group.first();
                        if (preparacaoExameItem.getPreparacaoExame() != null && preparacaoExameItem.getPreparacaoExame().getObservacao() != null) {
                            preparacaoExameItemList = Lambda.select(listPreparacaoExameItem, Lambda.having(Lambda.on(PreparacaoExameItem.class).getPreparacaoExame().getCodigo(),
                                    Matchers.equalTo(preparacaoExameItem.getPreparacaoExame().getCodigo())));

                            descricaoExameList = Lambda.extract(preparacaoExameItemList, Lambda.on(PreparacaoExameItem.class).getExameProcedimento().getDescricaoProcedimento());
                            if (CollectionUtils.isNotNullEmpty(descricaoExameList)) {
                                descricaoExame = Lambda.join(descricaoExameList, ", ");
                                preparacoesList.add(descricaoExame + ": " + preparacaoExameItem.getPreparacaoExame().getObservacao());
                            } else {
                                preparacoesList.add(preparacaoExameItem.getPreparacaoExame().getObservacao());
                            }
                        }
                    }
                    if (CollectionUtils.isNotNullEmpty(preparacoesList)) {
                        String join = Lambda.join(preparacoesList, "\n");
                        dto.setPreparacaoExames(join);
                        dto.adicionarPreparacaoExames(preparacoesList);
                    }
                }
            }
        }
    }

    private class QueryRelatorioComprovanteDataAgendamento extends CommandQuery<QueryRelatorioComprovanteDataAgendamento> {

        private List<AgendaGradeAtendimentoHorario> result;

        @Override
        protected void createQuery(HQLHelper hql) {
            hql.setTypeSelect(AgendaGradeAtendimentoHorario.class.getName());

            hql.addToSelect("agendaGradeAtendimentoHorario.dataAgendamento", "dataAgendamento");

            hql.addToFrom("AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario");

            if (param.getAgendaGradeAtendimentoHorario() != null) {
                hql.addToWhereWhithAnd("agendaGradeAtendimentoHorario = ", param.getAgendaGradeAtendimentoHorario());
            }
            if (CollectionUtils.isNotNullEmpty(param.getAgendaGradeAtendimentoHorarioList())) {
                hql.addToWhereWhithAnd("agendaGradeAtendimentoHorario in ", param.getAgendaGradeAtendimentoHorarioList());
            }
            hql.addToWhereWhithAnd("agendaGradeAtendimentoHorario.agendaGradeAtendimentoPrincipal is null");

            hql.addToOrder("agendaGradeAtendimentoHorario.dataAgendamento");
        }

        @Override
        protected void result(HQLHelper hql, Object result) {
            this.result = hql.getBeanList((List<Map<String, Object>>) result);
        }

        @Override
        public List<AgendaGradeAtendimentoHorario> getResult() {
            return result;
        }

    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioImprimirComprovanteAgendamentoDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(RelatorioImprimirComprovanteAgendamentoDTOParam param) {
        this.param = param;
    }

}
