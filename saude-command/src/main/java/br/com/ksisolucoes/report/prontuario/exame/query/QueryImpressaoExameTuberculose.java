package br.com.ksisolucoes.report.prontuario.exame.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.prontuario.exame.interfaces.dto.ImpressaoExameTuberculoseDTO;
import br.com.ksisolucoes.report.prontuario.exame.interfaces.dto.ImpressaoExameTuberculoseDTOParam;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryImpressaoExameTuberculose extends CommandQuery implements ITransferDataReport<ImpressaoExameTuberculoseDTOParam, ImpressaoExameTuberculoseDTO> {

    private ImpressaoExameTuberculoseDTOParam param;
    private List<ImpressaoExameTuberculoseDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(ImpressaoExameTuberculoseDTO.class.getName());

        hql.addToSelect("empresaSolicitante.codigo", "exameRequisicao.exame.empresaSolicitante.codigo");
        hql.addToSelect("empresaSolicitante.descricao", "exameRequisicao.exame.empresaSolicitante.descricao");
        hql.addToSelect("empresaSolicitante.cnes", "exameRequisicao.exame.empresaSolicitante.cnes");
        hql.addToSelect("empresaSolicitante.telefone", "exameRequisicao.exame.empresaSolicitante.telefone");
        hql.addToSelect("cidadeEmpresaSolic.codigo", "exameRequisicao.exame.empresaSolicitante.cidade.codigo");
        hql.addToSelect("cidadeEmpresaSolic.descricao", "exameRequisicao.exame.empresaSolicitante.cidade.descricao");

        hql.addToSelect("usuarioCadsus.codigo", "exameRequisicao.exame.usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "exameRequisicao.exame.usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.apelido", "exameRequisicao.exame.usuarioCadsus.apelido");
        hql.addToSelect("usuarioCadsus.utilizaNomeSocial", "exameRequisicao.exame.usuarioCadsus.utilizaNomeSocial");
        hql.addToSelect("usuarioCadsus.telefone", "exameRequisicao.exame.usuarioCadsus.telefone");
        hql.addToSelect("usuarioCadsus.dataNascimento", "exameRequisicao.exame.usuarioCadsus.dataNascimento");
        hql.addToSelect("usuarioCadsus.sexo", "exameRequisicao.exame.usuarioCadsus.sexo");
        hql.addToSelect("usuarioCadsus.nomeMae", "exameRequisicao.exame.usuarioCadsus.nomeMae");
        hql.addToSelect("raca.codigo", "exameRequisicao.exame.usuarioCadsus.raca.codigo");
        hql.addToSelect("raca.descricao", "exameRequisicao.exame.usuarioCadsus.raca.descricao");
        hql.addToSelect("tabelaCbo.cbo", "exameRequisicao.exame.usuarioCadsus.tabelaCbo.cbo");
        hql.addToSelect("tabelaCbo.descricao", "exameRequisicao.exame.usuarioCadsus.tabelaCbo.descricao");

        hql.addToSelect("enderecoUsuarioCadsus.logradouro", "enderecoUsuarioCadsus.logradouro");
        hql.addToSelect("enderecoUsuarioCadsus.numeroLogradouro", "enderecoUsuarioCadsus.numeroLogradouro");
        hql.addToSelect("enderecoUsuarioCadsus.cep", "enderecoUsuarioCadsus.cep");
        hql.addToSelect("enderecoUsuarioCadsus.bairro", "enderecoUsuarioCadsus.bairro");
        hql.addToSelect("tipoLogradouro.codigo", "enderecoUsuarioCadsus.tipoLogradouro.codigo");
        hql.addToSelect("tipoLogradouro.descricao", "enderecoUsuarioCadsus.tipoLogradouro.descricao");
        hql.addToSelect("tipoLogradouro.sigla", "enderecoUsuarioCadsus.tipoLogradouro.sigla");

        hql.addToSelect("cidade.codigo", "enderecoUsuarioCadsus.cidade.codigo");
        hql.addToSelect("cidade.descricao", "enderecoUsuarioCadsus.cidade.descricao");
        hql.addToSelect("estado.codigo", "enderecoUsuarioCadsus.cidade.estado.codigo");
        hql.addToSelect("estado.descricao", "enderecoUsuarioCadsus.cidade.estado.descricao");
        hql.addToSelect("estado.sigla", "enderecoUsuarioCadsus.cidade.estado.sigla");

        hql.addToSelect("requisicaoTuberculose.numeroNotificacao", "requisicaoTuberculose.numeroNotificacao");
        hql.addToSelect("requisicaoTuberculose.testeSensibilidade", "requisicaoTuberculose.testeSensibilidade");
        hql.addToSelect("requisicaoTuberculose.codigo", "requisicaoTuberculose.codigo");
        hql.addToSelect("requisicaoTuberculose.amostraBiologicaOutra", "requisicaoTuberculose.amostraBiologicaOutra");
        hql.addToSelect("requisicaoTuberculose.diagnostico", "requisicaoTuberculose.diagnostico");
        hql.addToSelect("requisicaoTuberculose.cultura", "requisicaoTuberculose.cultura");
        hql.addToSelect("requisicaoTuberculose.baciloscopia", "requisicaoTuberculose.baciloscopia");
        hql.addToSelect("requisicaoTuberculose.controleTratamento", "requisicaoTuberculose.controleTratamento");
        hql.addToSelect("requisicaoTuberculose.segundaColeta", "requisicaoTuberculose.segundaColeta");
        hql.addToSelect("requisicaoTuberculose.amostraBiologica", "requisicaoTuberculose.amostraBiologica");
        hql.addToSelect("requisicaoTuberculose.exameRequisicao", "requisicaoTuberculose.exameRequisicao");
        hql.addToSelect("requisicaoTuberculose.primeiraColeta", "requisicaoTuberculose.primeiraColeta");
        hql.addToSelect("requisicaoTuberculose.outroMes", "requisicaoTuberculose.outroMes");

        hql.addToSelect("profissional.codigo", "exameRequisicao.exame.profissional.codigo");
        hql.addToSelect("profissional.referencia", "exameRequisicao.exame.profissional.referencia");
        hql.addToSelect("profissional.nome",                             "exameRequisicao.exame.profissional.nome");
        hql.addToSelect("profissional.unidadeFederacaoConselhoRegistro", "exameRequisicao.exame.profissional.unidadeFederacaoConselhoRegistro");
        hql.addToSelect("profissional.numeroRegistro",                   "exameRequisicao.exame.profissional.numeroRegistro");
        
        hql.addToSelect("conselhoClasse.sigla",                          "exameRequisicao.exame.profissional.conselhoClasse.sigla"); 

        hql.addToSelect("(select uce.nivelEscolaridade from UsuarioCadsusEsus uce where uce.usuarioCadsus = usuarioCadsus)", "nivelEscolaridade");

        hql.addToFrom("RequisicaoTuberculose requisicaoTuberculose"
                + " left join requisicaoTuberculose.exameRequisicao exameRequisicao"
                + " left join exameRequisicao.exame exame"
                + " left join exame.usuarioCadsus usuarioCadsus"
                + " left join usuarioCadsus.raca raca"
                + " left join usuarioCadsus.enderecoUsuarioCadsus enderecoUsuarioCadsus"
                + " left join usuarioCadsus.tabelaCbo tabelaCbo"
                + " left join enderecoUsuarioCadsus.tipoLogradouro tipoLogradouro"
                + " left join enderecoUsuarioCadsus.cidade cidade"
                + " left join cidade.estado estado"
                + " left join exame.profissional profissional"
                + " left join profissional.conselhoClasse conselhoClasse"
                + " left join exame.atendimento atendimento"
                + " left join exame.empresaSolicitante empresaSolicitante"
                + " left join empresaSolicitante.cidade cidadeEmpresaSolic");

        hql.addToWhereWhithAnd("atendimento = ", this.param.getAtendimento());
        hql.addToWhereWhithAnd("exame.codigo = ", this.param.getCodigoExame());
        hql.addToWhereWhithAnd("exame.status <> ", Exame.STATUS_CANCELADO);
    }

    @Override
    public void setDTOParam(ImpressaoExameTuberculoseDTOParam param) {
        this.param = param;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public Collection<ImpressaoExameTuberculoseDTO> getResult() {
        return result;
    }
}
