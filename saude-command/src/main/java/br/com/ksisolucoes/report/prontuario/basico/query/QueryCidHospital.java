package br.com.ksisolucoes.report.prontuario.basico.query;

import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioPerfilAtendimentoHospitalDTO;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioPerfilAtendimentoHospitalDTOParam;
import br.com.ksisolucoes.util.Coalesce;
import java.util.List;
import java.util.Map;
import org.hibernate.Query;

public class QueryCidHospital extends QueryPerfilAtendimentoHospital {

    private List<RelatorioPerfilAtendimentoHospitalDTO> result;

    public QueryCidHospital(RelatorioPerfilAtendimentoHospitalDTOParam param) {
        super(param);
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("aa.cid.codigo", "codigoString");
        hql.addToSelect("aa.cid.descricao", "descricao");
        hql.addToSelect("sum(1)", "quantidade");
        {
            //subSelect para o total
            HQLHelper hqlTotal = hql.getNewInstanceSubQuery();
            hqlTotal.addToSelect("count(aa1.codigo)");
            hqlTotal.addToFrom("AtendimentoAlta aa1 "
                    + " left join aa1.atendimento a1");
//            hqlTotal.addToGroup("a1.cidPrincipal.codigo");
            addWhereAtendimento(hqlTotal, "a1");
            hql.addToSelect("(" + hqlTotal.getQuery() + ")", "total");
        }
        hql.addToFrom("AtendimentoAlta aa"
                + " left join aa.atendimento a ");
        hql.setTypeSelect(RelatorioPerfilAtendimentoHospitalDTO.class.getName());
        addWhereAtendimento(hql, "a");
        hql.addToGroup("aa.cid.codigo");
        hql.addToGroup("aa.cid.descricao");
        hql.addToOrder("3 desc");
    }
    
    @Override
    protected void customQuery(Query query) {
        query.setMaxResults(Coalesce.asLong(this.getParam().getNumeroCids(), 10L).intValue());
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }
    
    @Override
    public List<RelatorioPerfilAtendimentoHospitalDTO> getResult() {
        return result;
    }
}
