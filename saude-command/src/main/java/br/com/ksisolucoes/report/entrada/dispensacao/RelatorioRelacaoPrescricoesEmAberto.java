package br.com.ksisolucoes.report.entrada.dispensacao;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioRelacaoPrescricoesEmAbertoDTOParam;
import br.com.ksisolucoes.report.entrada.dispensacao.query.QueryRelatorioRelacaoPrescricoesEmAberto;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoPrescricoesEm<PERSON>berto extends AbstractReport<RelatorioRelacaoPrescricoesEmAbertoDTOParam> {
    
    public RelatorioRelacaoPrescricoesEmAberto(RelatorioRelacaoPrescricoesEmAbertoDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        if (RelatorioRelacaoPrescricoesEmAbertoDTOParam.TipoRelatorio.DETALHADO.equals(getParam().getTipoRelatorio())) {
            return "/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_relacao_prescricoes_nao_dispensadas.jrxml";
        } else {
            return "/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_relacao_prescricoes_nao_dispensadas_resumido.jrxml";
        }
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relatorio_relacao_prescricoes_em_aberto");
    }
    
    @Override
    public ITransferDataReport getQuery() {
        addParametro("FORMA_APRESENTACAO", getParam().getFormaApresentacao());
        
        return new QueryRelatorioRelacaoPrescricoesEmAberto();
    }
    
}
