package br.com.ksisolucoes.report.basico.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.cadsus.interfaces.dto.ImpressaoCartaoIdentificacaoPacienteDTO;
import br.com.ksisolucoes.report.cadsus.interfaces.dto.ImpressaoCartaoIdentificacaoPacienteDTOParam;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.TipoDocumentoUsuario;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDocumento;
import org.hibernate.Query;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class QueryImpressaoCartaoIdentificacaoPaciente extends CommandQuery implements ITransferDataReport<ImpressaoCartaoIdentificacaoPacienteDTOParam, ImpressaoCartaoIdentificacaoPacienteDTO> {

    private ImpressaoCartaoIdentificacaoPacienteDTOParam param;
    private List<ImpressaoCartaoIdentificacaoPacienteDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(ImpressaoCartaoIdentificacaoPacienteDTO.class.getName());
        ImpressaoCartaoIdentificacaoPacienteDTO proxy = on(ImpressaoCartaoIdentificacaoPacienteDTO.class);

        hql.addToSelectAndGroup("usuarioCadsus.codigo", path(proxy.getUsuarioCadsus().getCodigo()));
        hql.addToSelectAndGroup("usuarioCadsus.nome", path(proxy.getUsuarioCadsus().getNome()));
        hql.addToSelectAndGroup("usuarioCadsus.dataNascimento", path(proxy.getUsuarioCadsus().getDataNascimento()));
        hql.addToSelectAndGroup("usuarioCadsus.nomeMae", path(proxy.getUsuarioCadsus().getNomeMae()));
        hql.addToSelectAndGroup("usuarioCadsus.telefone", path(proxy.getUsuarioCadsus().getTelefone()));
        hql.addToSelectAndGroup("usuarioCadsus.cpf", path(proxy.getUsuarioCadsus().getCpf()));
        hql.addToSelectAndGroup("usuarioCadsus.referencia", path(proxy.getUsuarioCadsus().getReferencia()));
        hql.addToSelectAndGroup("enderecoDomicilio",path(proxy.getUsuarioCadsus().getEnderecoDomicilio()));
        hql.addToSelectAndGroup("enderecoUsuarioCadsus.logradouro", path(proxy.getEnderecoUsuarioCadsus().getLogradouro()));
        hql.addToSelectAndGroup("enderecoUsuarioCadsus.numeroLogradouro", path(proxy.getEnderecoUsuarioCadsus().getNumeroLogradouro()));
        hql.addToSelectAndGroup("enderecoUsuarioCadsus.cep", path(proxy.getEnderecoUsuarioCadsus().getCep()));
        hql.addToSelectAndGroup("enderecoUsuarioCadsus.bairro", path(proxy.getEnderecoUsuarioCadsus().getBairro()));
        hql.addToSelectAndGroup("tipoLogradouro.codigo", path(proxy.getEnderecoUsuarioCadsus().getTipoLogradouro().getCodigo()));
        hql.addToSelectAndGroup("tipoLogradouro.descricao", path(proxy.getEnderecoUsuarioCadsus().getTipoLogradouro().getDescricao()));
        hql.addToSelectAndGroup("tipoLogradouro.sigla", path(proxy.getEnderecoUsuarioCadsus().getTipoLogradouro().getSigla()));

        hql.addToSelectAndGroup("cidade.codigo", path(proxy.getEnderecoUsuarioCadsus().getCidade().getCodigo()));
        hql.addToSelectAndGroup("cidade.descricao", path(proxy.getEnderecoUsuarioCadsus().getCidade().getDescricao()));

        hql.addToSelectAndGroup("equipeArea.codigoArea", "equipeMicroArea.equipeArea.codigoArea");
        hql.addToSelectAndGroupAndOrder("equipeArea.descricao", "equipeMicroArea.equipeArea.descricao");
        hql.addToSelectAndGroup("equipeMicroArea.codigo", "equipeMicroArea.codigo");
        hql.addToSelectAndGroupAndOrder("equipeMicroArea.microArea", "equipeMicroArea.microArea");

        hql.addToSelect("(select min(ucc.numeroCartao) from UsuarioCadsusCns ucc where ucc.usuarioCadsus.codigo = usuarioCadsus and ucc.excluido = :naoExcluido)", path(proxy.getUsuarioCadsusCns().getNumeroCartao()));

        hql.addToSelect("(select u.numeroDocumento "
                + " from UsuarioCadsusDocumento u "
                + " left join u.orgaoEmissor orgaoEmissor"
                + " left join u.tipoDocumento tipoDocumento"
                + " where u.usuarioCadsus = usuarioCadsus and tipoDocumento = :rg and u.situacaoExcluido = :documentoAtivo) ", path(proxy.getRg()));

        hql.addToFrom("UsuarioCadsus usuarioCadsus"
                + " left join usuarioCadsus.empresaResponsavel empresaResponsavel"
                + " left join usuarioCadsus.enderecoUsuarioCadsus enderecoUsuarioCadsus"
                + " left join usuarioCadsus.enderecoDomicilio enderecoDomicilio"
                + " left join enderecoDomicilio.equipeMicroArea equipeMicroArea"
                + " left join equipeMicroArea.equipeArea equipeArea"
                + " left join enderecoUsuarioCadsus.tipoLogradouro tipoLogradouro"
                + " left join enderecoUsuarioCadsus.cidade cidade"
                + " left join usuarioCadsus.usuarioCadastro usuarioCadastro"
                + " left join cidade.estado estado");

        hql.addToWhereWhithAnd("usuarioCadsus.codigo = ", param.getUsuarioCadsus().getCodigo());

    }

    @Override
    public void setDTOParam(ImpressaoCartaoIdentificacaoPacienteDTOParam param) {
        this.param = param;
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setLong("naoExcluido", RepositoryComponentDefault.NAO_EXCLUIDO);
        query.setLong("documentoAtivo", UsuarioCadsusDocumento.STATUS_ATIVO);
        query.setLong("rg", TipoDocumentoUsuario.TIPO_DOCUMENTO_RG);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public Collection<ImpressaoCartaoIdentificacaoPacienteDTO> getResult() {
        return result;
    }

}
