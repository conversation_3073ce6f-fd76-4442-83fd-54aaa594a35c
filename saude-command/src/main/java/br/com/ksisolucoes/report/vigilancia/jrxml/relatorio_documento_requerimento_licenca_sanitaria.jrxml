<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_documento_requerimento_licenca_sanitaria" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="bf0059f8-7793-4ef2-b2f4-c9046402701c">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="0.9330147604194747"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<subDataset name="profissionais-ds" uuid="18ad5a1f-e8ab-4132-9930-b5abc71fe32b">
		<field name="codigo" class="java.lang.Long"/>
		<field name="nome" class="java.lang.String"/>
	</subDataset>
	<parameter name="lstProfissionais" class="java.util.List"/>
	<parameter name="unidade" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="numero" class="java.lang.String"/>
	<field name="protocolo" class="java.lang.String"/>
	<field name="especificacao" class="java.lang.String"/>
	<field name="nomeEstabelecimento" class="java.lang.String"/>
	<field name="fantasia" class="java.lang.String"/>
	<field name="cnpjCpf" class="java.lang.String"/>
	<field name="endereco" class="java.lang.String"/>
	<field name="bairro" class="java.lang.String"/>
	<field name="responsavel" class="java.lang.String"/>
	<field name="tipoVeiculo" class="java.lang.String"/>
	<field name="placa" class="java.lang.String"/>
	<field name="dataFimValidade" class="java.util.Date"/>
	<field name="dataImpressao" class="java.util.Date"/>
	<field name="concedidoPor" class="java.lang.String"/>
	<field name="restricoes" class="java.lang.String"/>
	<field name="tipoDocumento" class="java.lang.Long"/>
	<field name="dataInicioValidade" class="java.util.Date"/>
	<field name="formatacaoAlvara" class="java.lang.String">
		<fieldDescription><![CDATA[grupoEstabelecimento.formatacaoAlvara]]></fieldDescription>
	</field>
	<field name="veiculo" class="br.com.ksisolucoes.vo.vigilancia.VeiculoEstabelecimento">
		<fieldDescription><![CDATA[requerimentoVigilancia.veiculo]]></fieldDescription>
	</field>
	<field name="responsavelTecnico" class="br.com.ksisolucoes.vo.vigilancia.ResponsavelTecnico">
		<fieldDescription><![CDATA[estabelecimento.responsavelTecnico]]></fieldDescription>
	</field>
	<field name="tipoSolicitacao" class="br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao">
		<fieldDescription><![CDATA[requerimentoVigilancia.tipoSolicitacao]]></fieldDescription>
	</field>
	<field name="telefone" class="java.lang.String"/>
	<field name="cep" class="java.lang.String"/>
	<field name="cidade" class="br.com.ksisolucoes.vo.basico.Cidade"/>
	<variable name="numeroLicenca" class="java.lang.String">
		<variableExpression><![CDATA[$F{formatacaoAlvara} != null
?
    $F{formatacaoAlvara}.replaceAll("@sequencia", $F{numero}.split("/")[0]).replaceAll("@ano", $F{numero}.split("/")[1])
:
    $F{numero}]]></variableExpression>
	</variable>
	<background>
		<band height="802" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="555" height="802" forecolor="#DFDFDF" uuid="fca1b47c-cfc6-47b0-9c7e-85aa7007d122"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="72" isBold="true"/>
				</textElement>
				<text><![CDATA[LICENÇA
SANITÁRIA]]></text>
			</staticText>
		</band>
	</background>
	<detail>
		<band height="266">
			<rectangle>
				<reportElement mode="Transparent" x="2" y="97" width="551" height="32" uuid="a9fc9b08-1cf8-42c0-a287-d4b274ff11ad"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="2" y="193" width="551" height="32" uuid="2141602a-8079-4119-bc37-c6aca1522bc3"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="2" y="161" width="551" height="32" uuid="26d5e520-6e4f-47ae-a477-e2d3249ed196"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="2" y="129" width="551" height="32" uuid="170fd792-730b-4b66-9555-8b83f8e1b0a2"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="2" y="33" width="551" height="32" uuid="89a366c6-cdf5-4bd2-af2c-be17a2c61381"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="2" y="65" width="551" height="32" uuid="5e7ef14b-efe8-4569-ba4d-ef6bbf8c64b9"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="360" y="4" width="85" height="29" uuid="fb1c7ea3-b46d-493e-9304-d73e50b4daa9"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="445" y="4" width="108" height="29" uuid="25c7e198-0b11-4313-8325-bf2be03163c8"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="34" width="173" height="14" uuid="467d840d-73e0-467a-a289-16fad794eef7"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_especificacao").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="360" y="6" width="85" height="12" uuid="008f7d59-0160-4d39-901b-d9c27ff03936"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_numero").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="360" y="19" width="85" height="12" uuid="e5224ed4-02fd-4862-8a8d-3db0a32c6d30"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{numeroLicenca}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="445" y="6" width="108" height="12" uuid="612d8c41-473d-4c05-a11a-4e05e4a3844e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_protocolo").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="445" y="19" width="108" height="12" uuid="5b97a71e-901b-488e-8fb1-49edba49f6e5"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{protocolo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="50" width="548" height="14" uuid="3000afb3-60b8-44a3-9607-6139b5e71e2e"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{especificacao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="67" width="441" height="14" uuid="152ffd82-00bc-4ec2-88cf-ab72245beb47"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nome_pessoa_fisica_juridica").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="82" width="441" height="14" uuid="301544c4-9e35-484c-a2f9-f85fa3c0970f"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nomeEstabelecimento}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="210" width="547" height="14" uuid="09cc30fa-ab1f-422c-9bd9-8b76da42d9d3"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{responsavel}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="163" width="80" height="14" uuid="91de84d1-4b7d-4bfe-830a-86e1b20f4449"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_placa").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="195" width="547" height="14" uuid="3d992217-1da6-4d45-b69c-55da2fbf36e5"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_proprietario_e_ou_representante_legal").toUpperCase() + "  (" + Bundle.getStringApplication("rotulo_transportador_conforme_renavam") + ")"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="178" width="80" height="14" uuid="3d40963e-17ef-42a9-b5a1-9e51c02eb09c"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Util.getPlacaFormatado($F{veiculo}.getPlaca())]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="448" y="82" width="103" height="14" uuid="b8da64ff-595a-4bd5-9f0c-97cf362db02b"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cnpjCpf} != null
?
    $F{cnpjCpf}.trim().length() > 12
     ?
        Util.getCnpjFormatado($F{cnpjCpf})
     :
        Util.getCpfFormatado($F{cnpjCpf})
:
""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="448" y="67" width="103" height="14" uuid="46f7af98-d588-4c3e-8fd3-76de6cb9994d"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cnpj_cpf").toUpperCase()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="445" y="65" width="1" height="32" uuid="0ded1293-355c-47c1-97bc-b34a3768c93a"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="224" y="129" width="1" height="32" uuid="4f804a13-24d4-49ed-96b2-060d10d46433"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="131" width="218" height="14" uuid="f6cc4eac-078b-446d-8398-208809548ab8"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_bairro").toUpperCase()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="493" y="97" width="1" height="32" uuid="007c652c-15ef-4185-a82a-947917e98410"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="448" y="146" width="103" height="14" uuid="0f125aa3-4998-4d2e-a18e-df5b0037a953"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{telefone} != null
? Util.getTelefoneFormatado($F{telefone})
: null]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" mode="Transparent" x="4" y="114" width="489" height="14" uuid="b59aa8c8-a553-4436-840b-e6d26c4921d4"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{endereco}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="146" width="218" height="14" uuid="25e3fd73-2bdb-42e7-9fd1-8f151b73be11"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{bairro}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="448" y="131" width="103" height="14" uuid="f67d490e-a314-40ed-b3f5-29408afb5588"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_fone").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="227" y="146" width="218" height="14" uuid="2ef0053c-7ac2-448a-aca5-56a76248023c"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cidade}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="99" width="489" height="14" uuid="33522230-bafb-4dac-81ae-628b058505e5"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_endereco_logradouro_rua_av_praca").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="495" y="99" width="56" height="14" uuid="38d13c62-6bcf-4ae3-95df-b70afbec5f27"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cep")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="495" y="114" width="56" height="14" uuid="ffd5cd0b-320d-49d0-9f14-ff0164d0a7f7"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Util.getCepFormatado($F{cep})]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="446" y="129" width="1" height="64" uuid="512aac23-2ef4-4cb3-9e7a-d8d27adf2fc5"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="227" y="131" width="219" height="14" uuid="c75f7e8b-e380-4f7d-8c38-8405602b3a6a"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_municipio").toUpperCase()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="85" y="161" width="1" height="32" uuid="ad447f73-a2fe-4b1c-87da-e321cb254842"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="88" y="163" width="357" height="14" uuid="fa079158-861b-4582-87c1-a0d9208cbf42"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_veiculo").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="88" y="178" width="357" height="14" uuid="ce82ef06-720c-4ad9-ae46-a776512a4ced"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{veiculo}.getTipoVeiculo()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="448" y="163" width="103" height="14" uuid="fbfe66d6-2a92-4df0-8c77-823f517d8e4d"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_renavam").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="448" y="178" width="103" height="14" uuid="71c46318-2961-4868-a5cb-30f101afaad0"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{veiculo}.getRenavam()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" stretchType="RelativeToTallestObject" x="4" y="226" width="547" height="40" uuid="567c2d5b-9e27-47a2-8155-a5741a7d966f"/>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tipoSolicitacao}.getLei()]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="2" y="225" width="551" height="41" uuid="6322cdb7-4ff9-4fa4-aa8f-94ce76f7fdd2"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
		</band>
		<band height="64">
			<rectangle>
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="2" y="32" width="551" height="32" uuid="01ce7310-10a4-438d-a6ca-db1aad713915"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="2" y="0" width="551" height="32" uuid="04dca9eb-c5d0-4f04-8ff8-938e3c415ed2"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="359" y="17" width="192" height="14" uuid="bf30cccc-dcce-46b9-bba0-e368214ca280"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.getDescricaoData($F{dataFimValidade}).toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="359" y="2" width="192" height="14" uuid="dcaea625-01a4-4d05-881e-71dad8eb862c"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_prazo_validade").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="280" y="34" width="271" height="14" uuid="a924eb4c-4d63-482c-9876-ce7228cfc965"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_fiscal").toUpperCase()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="356" y="0" width="1" height="32" uuid="2ef54505-3750-45af-96e2-c0ee38e35431"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="4" y="34" width="273" height="14" uuid="87a6f56f-29db-4406-abcf-71fbb2553afe"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_autoridade_saude").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="4" y="2" width="352" height="14" uuid="06159866-f892-4d54-bdfc-b170b0343421"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_concedido_por").toUpperCase()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="277" y="32" width="1" height="32" uuid="93168e4a-1e2e-42cb-b958-1e48b7f2c77f"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="17" width="352" height="14" uuid="0fdfe82f-f931-47c5-80ef-2122c5f72e23"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{concedidoPor}]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement x="280" y="49" width="271" height="15" uuid="23da94b0-4363-49ed-bc68-159502cff846"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Horizontal">
					<datasetRun subDataset="profissionais-ds" uuid="a7612143-fb27-4774-8f7c-44cf68e27e71">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($P{lstProfissionais})]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="15" width="271">
						<textField isBlankWhenNull="true">
							<reportElement key="textField-9" positionType="Float" x="0" y="0" width="269" height="14" uuid="84a9d306-6422-49d3-94e4-f1d63e5d8b86"/>
							<box leftPadding="2">
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Arial" size="11" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{nome}]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
		</band>
		<band height="33">
			<rectangle>
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="2" y="0" width="551" height="33" uuid="f184a60d-1796-4854-94b9-beef9efb90ac"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="17" width="547" height="16" uuid="41e09b05-057b-4a7f-a165-b0bcb5716b2b"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{restricoes}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="4" y="2" width="273" height="14" uuid="56bbfbc6-a9ab-49b9-9323-8e828df91e06"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_observacoes").toUpperCase()]]></textFieldExpression>
			</textField>
		</band>
		<band height="84">
			<rectangle>
				<reportElement mode="Transparent" x="2" y="32" width="551" height="51" uuid="89fbce54-0a5d-4ab1-8ce4-0f6af64806d6"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="2" y="0" width="551" height="32" uuid="cd6b2c83-5e16-47ca-bf92-690d6f671abe"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="34" width="274" height="14" uuid="5feead68-6cd2-436a-9914-b98aff5aee2c"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nome_carimbo_assinatura").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="448" y="32" width="104" height="51" uuid="90ab47e2-b811-4111-a1c6-62b311b00087"/>
				<box topPadding="5">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.formatar($F{dataImpressao})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="280" y="2" width="90" height="14" uuid="5a61adca-8d5e-4c8d-8ad6-3269639e4f10"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_telefone").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="4" y="2" width="273" height="14" uuid="e59f16dc-4efd-4e51-a566-1bba0bc11f24"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_municipio_autoridade_saude").toUpperCase()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="277" y="0" width="1" height="32" uuid="a37a75ea-087d-454d-8e63-************"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="375" y="2" width="176" height="14" uuid="a6a37659-496b-4d63-9f0b-705478ce3b26"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_email").toUpperCase()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="372" y="0" width="1" height="32" uuid="6e4a7dd4-8041-4a9e-9299-1781f80e47fc"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<line>
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="447" y="32" width="1" height="51" uuid="76770ac4-bc3e-4b25-845e-78feb5f0d862"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="4" y="17" width="273" height="14" uuid="41e5876a-8ffe-4608-b06c-dc5a53877603"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{unidade}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="280" y="17" width="90" height="14" uuid="86c361aa-fa26-4b77-b445-da06c071e58b"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{unidade}.getTelefoneFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="375" y="17" width="176" height="14" uuid="3e79cba4-eeeb-455d-978f-70d411d9bbf9"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{unidade}.getEmail()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="448" y="34" width="104" height="14" uuid="a4df38b5-5cc1-407c-8bf1-0b8075a632ed"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data").toUpperCase()]]></textFieldExpression>
			</textField>
		</band>
		<band height="25">
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="-1" y="5" width="556" height="19" uuid="b663a1d3-2366-41c3-be5e-c94a3ddf2280"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_obrigatorio_manter_veiculo_perfeito_estado_conservacao").toUpperCase()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
