<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_prestadores.jrxml" pageWidth="840" pageHeight="595" orientation="Landscape" columnWidth="800" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="904ef39c-8037-443b-8222-af49929dd456">
	<property name="ireport.zoom" value="1.652892561983477"/>
	<property name="ireport.x" value="234"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="br.com.celk.util.Coalesce"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<import value="br.com.ksisolucoes.report.consorcio.dto.RelatorioRelacaoPrestadoresDTOParam.FormaApresentacao"/>
	<import value="org.apache.commons.lang.StringUtils"/>
	<parameter name="formaApresentacao" class="br.com.ksisolucoes.report.consorcio.dto.RelatorioRelacaoPrestadoresDTOParam.FormaApresentacao"/>
	<field name="consorcioPrestadorEdital" class="br.com.ksisolucoes.vo.consorcio.ConsorcioPrestadorEdital"/>
	<field name="tabelaPrecoEdital" class="br.com.ksisolucoes.vo.consorcio.TabelaPrecoEdital"/>
	<field name="consorcioPrestador" class="br.com.ksisolucoes.vo.consorcio.ConsorcioPrestador"/>
	<field name="contratoEdital" class="br.com.ksisolucoes.vo.consorcio.ContratoEdital"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="totalPrestadoresFA" class="java.lang.Integer" resetType="Group" resetGroup="FA" calculation="DistinctCount">
		<variableExpression><![CDATA[$F{consorcioPrestador}.getEmpresaPrestador().getCodigo()]]></variableExpression>
	</variable>
	<variable name="totalPrestadoresGeral" class="java.lang.Integer" resetType="Group" resetGroup="GERAL" calculation="DistinctCount">
		<variableExpression><![CDATA[$F{consorcioPrestador}.getEmpresaPrestador().getCodigo()]]></variableExpression>
	</variable>
	<group name="GERAL">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupFooter>
			<band height="13">
				<textField>
					<reportElement x="667" y="2" width="94" height="11" uuid="62859f88-3f25-4e05-ac24-3b1aab89aeec"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_geral_prestadores") + ": "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="762" y="2" width="36" height="11" uuid="356d3beb-8a4a-4162-b0e8-72687da67d5c"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalPrestadoresGeral}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="667" y="1" width="133" height="1" uuid="eccc6d33-3b85-4e9b-9539-550956c1fe76"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="FA" isReprintHeaderOnEachPage="true" keepTogether="true">
		<groupExpression><![CDATA[FormaApresentacao.EDITAL.equals($P{formaApresentacao})
? $F{tabelaPrecoEdital}.getCodigo()
:
    FormaApresentacao.CIDADE.equals($P{formaApresentacao})
    ? $F{consorcioPrestador}.getEmpresaPrestador().getCidade().getCodigo()
    : null]]></groupExpression>
		<groupHeader>
			<band height="20">
				<printWhenExpression><![CDATA[!FormaApresentacao.GERAL.equals($P{formaApresentacao})]]></printWhenExpression>
				<rectangle radius="8">
					<reportElement x="0" y="1" width="800" height="18" uuid="9acf27c7-80b0-40ee-98f8-a67674c92b9c"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField>
					<reportElement x="0" y="1" width="800" height="18" uuid="e199520c-c239-4028-a5d2-b36929bfb86b"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true" isUnderline="true"/>
					</textElement>
					<textFieldExpression><![CDATA[FormaApresentacao.EDITAL.equals($P{formaApresentacao})
? $F{tabelaPrecoEdital}.getEdital()
:
    FormaApresentacao.CIDADE.equals($P{formaApresentacao})
    ? $F{consorcioPrestador}.getEmpresaPrestador().getCidade().getDescricao()
    : null]]></textFieldExpression>
				</textField>
			</band>
			<band height="23">
				<textField isStretchWithOverflow="true">
					<reportElement x="0" y="2" width="60" height="19" uuid="985826df-b42d-4bcf-9c57-0f7e63ed7560"/>
					<box bottomPadding="1">
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_referencia")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="21" width="800" height="1" uuid="fb44fa67-4ef7-47e8-b53f-ca21c6313e0c"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField isStretchWithOverflow="true">
					<reportElement x="330" y="2" width="64" height="19" uuid="ce72fcdc-9cef-4efb-9a7d-9fdab062b230"/>
					<box leftPadding="1" bottomPadding="1">
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_telefone")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="60" y="2" width="155" height="19" uuid="36c27d1a-5ef6-46d4-ba6b-6be52713415b"/>
					<box bottomPadding="1">
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nome")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="215" y="2" width="40" height="19" uuid="9a3d8714-8fe2-49fd-a342-9b26b63bc506"/>
					<box bottomPadding="1">
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cnes")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="255" y="2" width="75" height="19" uuid="98220092-e278-4e44-86ce-0067bbff3318"/>
					<box bottomPadding="1">
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cnpj_cpf")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="394" y="2" width="110" height="19" uuid="013334cc-a53b-466c-a973-38a64213ac1e"/>
					<box bottomPadding="1">
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cidade")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="667" y="2" width="42" height="19" uuid="e6fafd8f-fb64-40e0-8bfa-d07f882810b0"/>
					<box bottomPadding="1">
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_numero_contrato")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="709" y="2" width="45" height="19" uuid="49f4c3fe-5601-4b8d-8944-f5ecd41abb55"/>
					<box bottomPadding="1">
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_contrato")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="754" y="2" width="45" height="19" uuid="1b067760-ec62-4cf1-a633-c8fb435f7844"/>
					<box leftPadding="6" bottomPadding="1">
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_vencimento_contrato")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="504" y="2" width="118" height="19" uuid="db8e69b6-63f3-47c5-b83c-a847b0d68174"/>
					<box leftPadding="2" bottomPadding="1"/>
					<textElement verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_edital")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="622" y="2" width="45" height="19" uuid="d1d1d05c-99be-48d2-97a0-a912205150e0"/>
					<box bottomPadding="1">
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_vencimento_edital")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="12">
				<printWhenExpression><![CDATA[!FormaApresentacao.GERAL.equals($P{formaApresentacao})]]></printWhenExpression>
				<textField>
					<reportElement x="688" y="1" width="73" height="11" uuid="8345a293-b452-4406-b95b-5ac985206b88"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_prestadores") + ": "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="762" y="1" width="36" height="11" uuid="ae4251ef-856e-42b3-809b-396a92c7e146"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalPrestadoresFA}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="688" y="0" width="112" height="1" uuid="74011bb7-90cb-42e4-9848-1230bc7c15e7"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
			</band>
		</groupFooter>
	</group>
	<detail>
		<band height="12" splitType="Prevent">
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="0" y="0" width="60" height="12" uuid="06bb4500-91b9-4ba9-9e8d-379ab33555d6"/>
				<box topPadding="1" bottomPadding="1"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{consorcioPrestador}.getEmpresaPrestador().getReferencia()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="60" y="0" width="155" height="12" uuid="d99a197a-a0d6-44d2-ac10-161c31df6587"/>
				<box topPadding="1" bottomPadding="1"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{consorcioPrestador}.getEmpresaPrestador().getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="215" y="0" width="40" height="12" uuid="bb801f34-f62c-4570-bfed-f7f101f9e56c"/>
				<box topPadding="1" bottomPadding="1"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{consorcioPrestador}.getEmpresaPrestador().getCnes()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="255" y="0" width="75" height="12" uuid="2ea5dd97-9ad4-4892-92aa-167fe4e6c379"/>
				<box topPadding="1" bottomPadding="1"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{consorcioPrestador}.getEmpresaPrestador().getCnpjFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="330" y="0" width="64" height="12" uuid="42fba737-95a0-44b9-ae0c-eb203b171959"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{consorcioPrestador}.getEmpresaPrestador().getTelefoneFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="394" y="0" width="110" height="12" uuid="915ac5b9-**************-f950d7dfa773"/>
				<box topPadding="1" bottomPadding="1"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{consorcioPrestador}.getEmpresaPrestador().getCidade().getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="667" y="0" width="42" height="12" uuid="050e5750-11d2-4c88-9f51-97f30466ba8b">
					<printWhenExpression><![CDATA[$F{contratoEdital}!=null]]></printWhenExpression>
				</reportElement>
				<box topPadding="1" bottomPadding="1"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{contratoEdital}.getNumeroContrato()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="709" y="0" width="45" height="12" uuid="190cd05c-f061-4d61-8bb1-f94b91e0d950">
					<printWhenExpression><![CDATA[$F{contratoEdital}!=null]]></printWhenExpression>
				</reportElement>
				<box topPadding="1" bottomPadding="1"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{contratoEdital}.getDataContrato()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="754" y="0" width="45" height="12" uuid="d24d19e3-1733-4bd5-9ed8-d175ab765b00">
					<printWhenExpression><![CDATA[$F{contratoEdital}!=null]]></printWhenExpression>
				</reportElement>
				<box topPadding="1" bottomPadding="1"/>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{contratoEdital}.getDataVencimentoRenovacao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="504" y="0" width="118" height="12" uuid="74291777-8ddd-4314-8326-40d20a2902b6"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tabelaPrecoEdital}.getEdital()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="622" y="0" width="45" height="12" uuid="791a52c2-714a-4896-baff-80d7c7da6d6c"/>
				<box topPadding="1" bottomPadding="1"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tabelaPrecoEdital}.getDataFim()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
