package br.com.ksisolucoes.report.entrada.dispensacao.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioRelacaoMensalReceitasControladasDTO;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioRelacaoMensalReceitasControladasDTOParam;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryRelatorioRelacaoMensalReceitasControladas extends CommandQuery<QueryRelatorioRelacaoMensalReceitasControladas> implements ITransferDataReport<RelatorioRelacaoMensalReceitasControladasDTOParam, RelatorioRelacaoMensalReceitasControladasDTO> {

    private RelatorioRelacaoMensalReceitasControladasDTOParam param;
    private List<RelatorioRelacaoMensalReceitasControladasDTO> list;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("dm.receita", "numeroReceita");
        hql.addToSelect("coalesce(dm.profissional.codigo, profissionalSemVinculo.codigo)", "codigoProfissional");
        hql.addToSelect("coalesce(dm.nomeProfissional, profissionalSemVinculo.nomeProfissional)", "nomeProfissional");
        hql.addToSelect("dm.numeroRegistro", "numeroRegistroProfissional");
        hql.addToSelect("dm.dataReceita", "dataReceita");
        hql.addToSelect("dm.empresa.empresaMaterial.numLicencaFuncionamento", "numLicencaFuncionamento");
        hql.addToSelect("dm.empresa.empresaMaterial.numeroAutorizacaoFuncionamento", "numeroAutorizacaoFuncionamento");
        hql.addToSelect("dmi.dataUltimaDispensacao", "dataUltimaDispensacao");
        hql.addToSelect("dmi.quantidadePrescrita", "quantidadePrescrita");
        hql.addToSelect("dmi.quantidadeDispensada", "quantidadeDispensada");
        hql.addToSelect("pr.codigoDcb", "codigoDcb");
        hql.addToSelect("pr.nomeDcb", "nomeDcb");
        hql.addToSelect("pr.descricao", "nomeProduto");
        hql.addToSelect("pr.concentracao", "concentracao");
        hql.addToSelect("pr.numeroLista", "numeroLista");
        hql.addToSelect("usu.nome", "nomePaciente");
        hql.addToSelect("tr.descricao", "descricaoReceita");
        hql.addToSelect("sub.descricao", "descricaoSubGrupo");
        hql.addToSelect("grupo.descricao", "descricaoGrupo");

        hql.addToFrom(DispensacaoMedicamentoItem.class.getName() + " dmi "
                + " right join dmi.dispensacaoMedicamento dm "
                + " left join dm.usuarioCadsusDestino usu "
                + " left join dm.tipoReceita tr"
                + " left join dm.profissionalSemVinculo profissionalSemVinculo "
        );
        hql.addToFrom(Produto.class.getName() + " pr"
                + " left join pr.subGrupo sub"
                + " left join sub.roGrupoProduto grupo");

        hql.setTypeSelect(RelatorioRelacaoMensalReceitasControladasDTO.class.getName());

        hql.addToWhereWhithAnd(" dmi.produto = pr ");
        hql.addToWhereWhithAnd(" sub.flagControlado = ", RepositoryComponentDefault.SIM);

        if (this.param.getEmpresa() != null) {
            hql.addToWhereWhithAnd("dm.empresa = ", this.param.getEmpresa());
        }
        if (this.param.getDataInicial() != null) {
            hql.addToWhereWhithAnd("dm.dataDispensacao >= ", this.param.getDataInicial());
        }
        if (this.param.getDataFinal() != null) {
            hql.addToWhereWhithAnd("dm.dataDispensacao <= ", this.param.getDataFinal());
        }
        hql.addToWhereWhithAnd("sub = ", this.param.getSubGrupo());
        hql.addToWhereWhithAnd("grupo = ", this.param.getGrupoProduto());
        hql.addToWhereWhithAnd("tr = ", this.param.getTipoReceita());
        hql.addToWhereWhithAnd("pr.numeroLista = ", this.param.getNumeroLista());

        if (RelatorioRelacaoMensalReceitasControladasDTOParam.FormaApresentacao.GRUPO_PRODUTO.toString().equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("grupo.descricao");
            hql.addToOrder("sub.descricao");
        }

        hql.addToOrder("dm.dataReceita");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioRelacaoMensalReceitasControladasDTO> getResult() {
        return this.list;
    }

    @Override
    public void setDTOParam(RelatorioRelacaoMensalReceitasControladasDTOParam param) {
        this.param = param;
    }
}
