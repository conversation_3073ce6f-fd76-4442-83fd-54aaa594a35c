package br.com.ksisolucoes.report.vigilancia;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FichaInvestigacaoAgravoDTOParam;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.vigilancia.query.QueryFichaAcidenteTrabalhoMaterialBiologico;
import br.com.ksisolucoes.util.Bundle;

import java.util.LinkedHashMap;

public class ImpressaoFichaInvestigacaoAgravoAcidenteTrabalhoMaterialBiologico extends AbstractReport<FichaInvestigacaoAgravoDTOParam> {

    private QueryFichaAcidenteTrabalhoMaterialBiologico query;

    public ImpressaoFichaInvestigacaoAgravoAcidenteTrabalhoMaterialBiologico(FichaInvestigacaoAgravoDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        if(query == null){
            query = new QueryFichaAcidenteTrabalhoMaterialBiologico();
        }
        return query;
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> columnsMap = new LinkedHashMap<>(((QueryFichaAcidenteTrabalhoMaterialBiologico) getQuery()).getMapeamentoPlanilhaBase());

        columnsMap.put("31 Ocupacao", "_31_ocupacao");
        columnsMap.put("32 Situacao Mercado Trabalho", "_32_situacao_mercado_trabalho");
        columnsMap.put("33 Tempo Trabalho Ocupacao", "_33_tempo_trabalho_ocupacao");
        columnsMap.put("33 Tempo Trabalho Ocupacao Unidade Medida", "_33_tempo_unidade_medida");

        columnsMap.put("34 Empresa Cnpj", "_34_cnpj_empresa");
        columnsMap.put("35 Empresa Descricao", "_35_descricao_formatado");
        columnsMap.put("36 Atividae Descricao", "_36_atividade_economica");
        columnsMap.put("37 Sigla", "_37_UF_empresa_contratante");
        columnsMap.put("38 Descricao", "_38_cidade_empresa_contratante");
        columnsMap.put("39 Empresa Distrito", "_39_distrito");
        columnsMap.put("40 Empresa Bairro", "_40_bairro");
        columnsMap.put("41 Empresa Rua", "_41_endereco");
        columnsMap.put("42 Empresa Numero", "_42_numero");
        columnsMap.put("43 Empresa Referencia", "_43_referencia");
        columnsMap.put("43 Empresa Telefone", "_44_telefone");

        columnsMap.put("45 Empregador Empresa Terceirizada", "_45_empregador_empresa_terceirizada");

        columnsMap.put("46 Tipo Exposicao Percutanea", "_46_tipo_exposicao_percutanea");
        columnsMap.put("46 Tipo Exposicao Mucosa", "_46_tipo_exposicao_mucosa");
        columnsMap.put("46 Tipo Exposicao Pele Integra", "_46_tipo_exposicao_pele_integra");
        columnsMap.put("46 Tipo Exposicao Pele Nao Integra", "_46_tipo_exposicao_pele_nao_integra");
        columnsMap.put("46 Tipo Exposicao Outros", "_46_tipo_exposicao_outros");

        columnsMap.put("47 Material Organico", "_47_material_organico");
        columnsMap.put("47 Material Organico Outros", "_47_material_organico_outros");

        columnsMap.put("48 Circunstancia Acidente", "_48_circunstancia_acidente");
        columnsMap.put("49 Aente", "_49_agente");

        columnsMap.put("50 Uso Epi Luva", "_50_uso_epi_luva");
        columnsMap.put("50 Uso Epi Avental", "_50_uso_epi_avental");
        columnsMap.put("50 Uso Epi Oculos", "_50_uso_epi_oculos");
        columnsMap.put("50 Uso Epi Mascara", "_50_uso_epi_mascara");
        columnsMap.put("50 Uso Epi ProtecaoFacial", "_50_uso_epi_protecao_facial");
        columnsMap.put("50 Uso Epi Bota", "_50_uso_epi_bota");

        columnsMap.put("52 Vacina Hepatite B", "_51_conduta_vacina_hepatite_b");
        columnsMap.put("52 Resultado Exames AntiHIV", "_52_resultado_exames_anti_hiv");
        columnsMap.put("52 Resultado Exames HBSAg", "_52_resultado_exames_hbsag");
        columnsMap.put("52 Resultado Exames AntiHBS", "_52_resultado_exames_anti_hbs");
        columnsMap.put("52 Resultado Exames AntiHCV", "_52_resultado_exames_anti_hcv");

        columnsMap.put("53 Paciente Fonte Conhecida", "_53_paciente_fonte_conhecida");
        columnsMap.put("54 Resultado Sorologico HBSAg", "_54_resultado_sorologico_hbsag");
        columnsMap.put("54 Resultado Sorologico AntiHIV", "_54_resultado_sorologico_anti_hiv");
        columnsMap.put("54 Resultado Sorologico AntiHBC", "_54_resultado_sorologico_anti_hbc");
        columnsMap.put("54 Resultado Sorologico AntiHCV", "_54_resultado_sorologico_anti_hcv");

        columnsMap.put("55 Conduta Sem Quimioprofilaxia", "_55_conduta_sem_quimioprofilaxia");
        columnsMap.put("55 Conduta Recusou Quimioprofilaxia", "_55_conduta_recusou_quimioprofilaxia");
        columnsMap.put("55 Conduta AZT3TC", "_55_conduta_AZT3TC");
        columnsMap.put("55 Conduta AZT3TCIndinavir", "_55_conduta_AZT3TC_indinavir");
        columnsMap.put("55 Conduta AZT3TCNelfinavir", "_55_conduta_AZT3TC_nelfinavir");
        columnsMap.put("55 Conduta Vacina HepatiteB", "_55_conduta_vacina_hepatite_b");
        columnsMap.put("55 Conduta Outros", "_55_conduta_outros");

        columnsMap.put("56 Evolucao Caso", "_56_evolucao_caso");
        columnsMap.put("56 Evolucao Caso Virus", "_56_evolucao_caso_virus");
        columnsMap.put("57 Data Obito", "_57_data_obito");
        columnsMap.put("58 Emitida CAT", "_58_emitida_comunicacao_acidente_trabalho");
        columnsMap.put("Observacao", "observacoes");

        return columnsMap;
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/vigilancia/pdf/ficha_investigacao_acidente_trabalho_material_biologico.pdf";
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return param.getTipoArquivo();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_acidente_animal_peconhento");
    }

}
