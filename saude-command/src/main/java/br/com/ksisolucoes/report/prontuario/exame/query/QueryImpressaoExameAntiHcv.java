package br.com.ksisolucoes.report.prontuario.exame.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.prontuario.exame.interfaces.dto.ImpressaoExameAntiHcvDTO;
import br.com.ksisolucoes.report.prontuario.exame.interfaces.dto.ImpressaoExameAntiHcvDTOParam;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryImpressaoExameAntiHcv extends CommandQuery implements ITransferDataReport<ImpressaoExameAntiHcvDTOParam, ImpressaoExameAntiHcvDTO> {

    private ImpressaoExameAntiHcvDTOParam param;
    private List<ImpressaoExameAntiHcvDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(ImpressaoExameAntiHcvDTO.class.getName());

        hql.addToSelect("empresaSolicitante.codigo", "exameRequisicao.exame.empresaSolicitante.codigo");
        hql.addToSelect("empresaSolicitante.descricao", "exameRequisicao.exame.empresaSolicitante.descricao");
        hql.addToSelect("empresaSolicitante.cnes", "exameRequisicao.exame.empresaSolicitante.cnes");
        hql.addToSelect("empresaSolicitante.telefone", "exameRequisicao.exame.empresaSolicitante.telefone");
        hql.addToSelect("cidadeEmpresaSolic.codigo", "exameRequisicao.exame.empresaSolicitante.cidade.codigo");
        hql.addToSelect("cidadeEmpresaSolic.descricao", "exameRequisicao.exame.empresaSolicitante.cidade.descricao");

        hql.addToSelect("usuarioCadsus.codigo", "exameRequisicao.exame.usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "exameRequisicao.exame.usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.apelido", "exameRequisicao.exame.usuarioCadsus.apelido");
        hql.addToSelect("usuarioCadsus.utilizaNomeSocial", "exameRequisicao.exame.usuarioCadsus.utilizaNomeSocial");
        hql.addToSelect("usuarioCadsus.telefone", "exameRequisicao.exame.usuarioCadsus.telefone");
        hql.addToSelect("usuarioCadsus.dataNascimento", "exameRequisicao.exame.usuarioCadsus.dataNascimento");
        hql.addToSelect("usuarioCadsus.sexo", "exameRequisicao.exame.usuarioCadsus.sexo");
        hql.addToSelect("usuarioCadsus.nomeMae", "exameRequisicao.exame.usuarioCadsus.nomeMae");
        hql.addToSelect("raca.codigo", "exameRequisicao.exame.usuarioCadsus.raca.codigo");
        hql.addToSelect("raca.descricao", "exameRequisicao.exame.usuarioCadsus.raca.descricao");

        hql.addToSelect("enderecoUsuarioCadsus.logradouro", "enderecoUsuarioCadsus.logradouro");
        hql.addToSelect("enderecoUsuarioCadsus.numeroLogradouro", "enderecoUsuarioCadsus.numeroLogradouro");
        hql.addToSelect("enderecoUsuarioCadsus.cep", "enderecoUsuarioCadsus.cep");
        hql.addToSelect("enderecoUsuarioCadsus.bairro", "enderecoUsuarioCadsus.bairro");
        hql.addToSelect("tipoLogradouro.codigo", "enderecoUsuarioCadsus.tipoLogradouro.codigo");
        hql.addToSelect("tipoLogradouro.descricao", "enderecoUsuarioCadsus.tipoLogradouro.descricao");
        hql.addToSelect("tipoLogradouro.sigla", "enderecoUsuarioCadsus.tipoLogradouro.sigla");

        hql.addToSelect("cidade.codigo", "enderecoUsuarioCadsus.cidade.codigo");
        hql.addToSelect("cidade.descricao", "enderecoUsuarioCadsus.cidade.descricao");
        hql.addToSelect("estado.codigo", "enderecoUsuarioCadsus.cidade.estado.codigo");
        hql.addToSelect("estado.descricao", "enderecoUsuarioCadsus.cidade.estado.descricao");
        hql.addToSelect("estado.sigla", "enderecoUsuarioCadsus.cidade.estado.sigla");

        hql.addToSelect("requisicaoAntiHcv.numeroNotificacao", "requisicaoAntiHcv.numeroNotificacao");
        hql.addToSelect("requisicaoAntiHcv.hipoteseDiagnostica", "requisicaoAntiHcv.hipoteseDiagnostica");
        hql.addToSelect("requisicaoAntiHcv.somaMotivo", "requisicaoAntiHcv.somaMotivo");

        hql.addToSelect("cid.codigo", "requisicaoAntiHcv.cid.codigo");
        hql.addToSelect("cid.descricao", "requisicaoAntiHcv.cid.descricao");

        hql.addToSelect("profissional.codigo",                           "exameRequisicao.exame.profissional.codigo");
        hql.addToSelect("profissional.referencia",                       "exameRequisicao.exame.profissional.referencia");
        hql.addToSelect("profissional.nome",                             "exameRequisicao.exame.profissional.nome");
        hql.addToSelect("profissional.unidadeFederacaoConselhoRegistro", "exameRequisicao.exame.profissional.unidadeFederacaoConselhoRegistro");
        hql.addToSelect("profissional.numeroRegistro",                   "exameRequisicao.exame.profissional.numeroRegistro");

        hql.addToSelect("conselhoClasse.sigla",                          "exameRequisicao.exame.profissional.conselhoClasse.sigla"); 

        hql.addToFrom("RequisicaoAntiHcv requisicaoAntiHcv"
                + " left join requisicaoAntiHcv.cid cid"
                + " left join requisicaoAntiHcv.exameRequisicao exameRequisicao"
                + " left join exameRequisicao.exame exame"
                + " left join exame.usuarioCadsus usuarioCadsus"
                + " left join usuarioCadsus.raca raca"
                + " left join usuarioCadsus.enderecoUsuarioCadsus enderecoUsuarioCadsus"
                + " left join enderecoUsuarioCadsus.tipoLogradouro tipoLogradouro"
                + " left join enderecoUsuarioCadsus.cidade cidade"
                + " left join cidade.estado estado"
                + " left join exame.profissional profissional"
                + " left join profissional.conselhoClasse conselhoClasse"
                + " left join exame.atendimento atendimento"
                + " left join exame.empresaSolicitante empresaSolicitante"
                + " left join empresaSolicitante.cidade cidadeEmpresaSolic");

        hql.addToWhereWhithAnd("atendimento = ", this.param.getAtendimento());
        hql.addToWhereWhithAnd("exame.codigo = ", this.param.getCodigoExame());
        hql.addToWhereWhithAnd("exame.status <> ", Exame.STATUS_CANCELADO);
    }

    @Override
    public void setDTOParam(ImpressaoExameAntiHcvDTOParam param) {
        this.param = param;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public Collection<ImpressaoExameAntiHcvDTO> getResult() {
        return result;
    }
}
