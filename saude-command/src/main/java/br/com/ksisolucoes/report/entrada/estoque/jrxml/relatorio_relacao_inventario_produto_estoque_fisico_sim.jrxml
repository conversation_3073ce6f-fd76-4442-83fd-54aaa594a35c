<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_inventario_produto_estoque_fisico_sim" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="bbea0966-4779-466f-ba84-837a38e25d74">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.zoom" value="1.948717100000002"/>
	<property name="ireport.x" value="218"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="ParamEstoqueFisico" class="java.lang.String" isForPrompting="false"/>
	<field name="codigoEmpresa" class="java.lang.String"/>
	<field name="descricaoEmpresa" class="java.lang.String"/>
	<field name="codigoGrupoProduto" class="java.lang.Long"/>
	<field name="descricaoGrupoProduto" class="java.lang.String"/>
	<field name="codigoSubGrupo" class="java.lang.Long"/>
	<field name="descricaoSubGrupo" class="java.lang.String"/>
	<field name="codigoProduto" class="java.lang.String"/>
	<field name="descricaoProduto" class="java.lang.String"/>
	<field name="unidadeProduto" class="java.lang.String"/>
	<field name="codigoLocalizacao" class="java.lang.Long"/>
	<field name="descricaoLocalizacao" class="java.lang.String"/>
	<field name="estoqueFisico" class="java.lang.Double"/>
	<field name="descricaoFormatadoGrupoProduto" class="java.lang.String"/>
	<field name="descricaoFormatadoSubGrupo" class="java.lang.String"/>
	<field name="descricaoFormatadoProduto" class="java.lang.String"/>
	<field name="flagAtivoEstoqueEmpresaDescricao" class="java.lang.String"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<group name="GrupoEmpresa" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{codigoEmpresa}]]></groupExpression>
		<groupHeader>
			<band height="29" splitType="Stretch">
				<rectangle radius="10">
					<reportElement x="0" y="0" width="802" height="15" uuid="9224305a-029d-4b3e-98d7-ed978f0766b6"/>
				</rectangle>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-5" mode="Opaque" x="6" y="17" width="185" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="2c63841f-d99d-48bc-959b-790e907041c5"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*produto*/$V{BUNDLE}.getStringApplication("rotulo_produto")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-6" mode="Opaque" x="192" y="17" width="23" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="77f00782-9acc-4718-989d-073e1d8dbfcd"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*unidade*/$V{BUNDLE}.getStringApplication("rotulo_unidade_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-7" mode="Opaque" x="256" y="17" width="64" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="7c47b943-f70c-421f-83c4-27a4560ab8cb"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*estoque fisico*/$V{BUNDLE}.getStringApplication("rotulo_estoque_fisico")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-50" mode="Opaque" x="562" y="17" width="130" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="ec5c7820-59c4-4b6d-8542-4a5ec2a6ecb0"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*grupo*/$V{BUNDLE}.getStringApplication("rotulo_grupo")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-51" mode="Opaque" x="694" y="17" width="106" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="d8ba8938-cac7-4f6d-aab4-931a9204de13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*subgrupo*/$V{BUNDLE}.getStringApplication("rotulo_subgrupo")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-52" mode="Opaque" x="322" y="17" width="89" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="45fedd36-2828-4536-94cb-49c69fa0737f"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Estoque/Lote/Validade*/$V{BUNDLE}.getStringApplication("rotulo_estoque_lote_validade")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-54" mode="Opaque" x="412" y="17" width="149" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="beb51263-d3fa-4739-826f-0dc82dff7641"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Localizacao*/$V{BUNDLE}.getStringApplication("rotulo_localizacao")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-43" mode="Transparent" x="1" y="1" width="801" height="13" forecolor="#000000" uuid="44801b23-924c-420f-b91d-4a65ca9e4c4f"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*empresa*/$V{BUNDLE}.getStringApplication("rotulo_empresa") + ": (" + $F{codigoEmpresa}.trim() + ") "+  $F{descricaoEmpresa}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="28" width="802" height="1" uuid="55d2fc4e-631a-4da3-b744-0702761ac3d4"/>
				</line>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-6" mode="Opaque" x="216" y="17" width="40" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="b9657e20-9e51-4d6d-bb74-b5209c54f363"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_situacao")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="11" splitType="Stretch">
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Opaque" x="6" y="0" width="185" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="767094c6-5d01-45be-a289-826e592d340b"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoFormatadoProduto}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-46" mode="Opaque" x="192" y="0" width="23" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="4a5ac534-fdf4-42a5-8d5c-86a2eb525ac6"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unidadeProduto}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-48" mode="Opaque" x="562" y="0" width="130" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="0e46b135-c1af-44b5-9533-e9ee8162451c"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[/*grupo*/$F{descricaoFormatadoGrupoProduto}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-49" mode="Opaque" x="694" y="0" width="106" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="b0cb4e49-eac6-4e2a-af12-7b155219c596"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[/*subgrupo*/$F{descricaoFormatadoSubGrupo}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="line-2" mode="Opaque" x="322" y="9" width="87" height="1" forecolor="#000000" backcolor="#FFFFFF" uuid="374722ec-b903-4cbf-8300-9f7b67a66603"/>
				<graphicElement fill="Solid">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-53" mode="Opaque" x="256" y="0" width="64" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="970d8964-c05f-4b87-b8e7-a2166c83d483"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{estoqueFisico}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-55" mode="Opaque" x="412" y="0" width="149" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="995be287-11ad-4522-843e-90e5b28f7329"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[/*localizacao*/$F{descricaoLocalizacao}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-46" mode="Opaque" x="216" y="0" width="40" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="ff22776e-c738-4789-91d4-ec5b26047241"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{flagAtivoEstoqueEmpresaDescricao}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
