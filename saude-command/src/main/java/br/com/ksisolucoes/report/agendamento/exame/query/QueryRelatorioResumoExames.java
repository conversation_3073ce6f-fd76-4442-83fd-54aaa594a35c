package br.com.ksisolucoes.report.agendamento.exame.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioRequerimentoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioResumoExamesDTO;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioResumoExamesDTOParam;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR> Colombo
 */
public class QueryRelatorioResumoExames extends CommandQuery<QueryRelatorioResumoExames> implements ITransferDataReport<RelatorioResumoExamesDTOParam, RelatorioResumoExamesDTO>{

    private RelatorioResumoExamesDTOParam param;
    private List<RelatorioResumoExamesDTO> dtoList;

    @Override
    public void setDTOParam(RelatorioResumoExamesDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(RelatorioResumoExamesDTO.class.getName());
        
        String pathCalc = "";
        if (RelatorioResumoExamesDTOParam.TipoDado.QUANTIDADE.equals(this.param.getTipoDado())) {
            pathCalc = "(cast( er.quantidade as double))";
        } else if (RelatorioResumoExamesDTOParam.TipoDado.VALOR.equals(this.param.getTipoDado())) {
            pathCalc = "(er.quantidade * er.valorProcedimento)";
        }

        hql.addToSelect("sum("
                + "case when e.status not in (" + Exame.STATUS_CANCELADO + "," + Exame.STATUS_DESVINCULADO + "," + Exame.STATUS_CONCLUIDO_NAO_SUS + ")"
                + "then "+pathCalc+" else 0 end"
                + ")","totalSus");

        hql.addToSelect("sum("
                + "case when e.status <> " + Exame.STATUS_CANCELADO + " "
                + "and e.status in (" + Exame.STATUS_DESVINCULADO + "," + Exame.STATUS_CONCLUIDO_NAO_SUS + ")"
                + "then "+pathCalc+" else 0 end"
                + ")","totalNaoSus");

        hql.addToSelect("sum("
                + "case when e.status not in (" + Exame.STATUS_CANCELADO + "," + Exame.STATUS_DESVINCULADO + "," + Exame.STATUS_CONCLUIDO_NAO_SUS + ")"
                + "then "+pathCalc+" else 0 end"
                + ") + sum("
                + "case when e.status <> " + Exame.STATUS_CANCELADO + " "
                + "and e.status in (" + Exame.STATUS_DESVINCULADO + "," + Exame.STATUS_CONCLUIDO_NAO_SUS + ")"
                + "then "+pathCalc+" else 0 end"
                + ")","totalExames");

        hql.addToSelect("sum(extract (day from (e.dataAgendamento - e.dataSolicitacao))) / "
                + "(case when sum("
                + "case when e.status in (" + Exame.STATUS_AUTORIZADO + "," + Exame.STATUS_CONCLUIDO_SUS + ") "
                + "then 1 else 0 end) = 0 then 1 else sum("
                + "case when e.status in (" + Exame.STATUS_AUTORIZADO + "," + Exame.STATUS_CONCLUIDO_SUS + ") "
                + "then 1 else 0 end) end)", "diasMediaEspera");

        if (!param.getTipoResumo().toString().equals(param.getFormaApresentacao().toString())) {
            if (RelatorioResumoExamesDTOParam.FormaApresentacao.UNIDADE_SOLICITANTE.equals(this.param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("empresaSolicitante.codigo", "empresa.codigo");
                hql.addToSelectAndGroupAndOrder("empresaSolicitante.referencia", "empresa.referencia");
                hql.addToSelectAndGroupAndOrder("empresaSolicitante.descricao", "empresa.descricao");
            } else if (RelatorioResumoExamesDTOParam.FormaApresentacao.PROFISSIONAL_SOLICITANTE.equals(this.param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("profissional.codigo", "profissional.codigo");
                hql.addToSelectAndGroupAndOrder("profissional.referencia", "profissional.referencia");
                hql.addToSelectAndGroupAndOrder("profissional.nome", "profissional.nome");
            } else if (RelatorioResumoExamesDTOParam.FormaApresentacao.TIPO_EXAME.equals(this.param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("tipoExame.codigo", "tipoExame.codigo");
                hql.addToSelectAndGroupAndOrder("tipoExame.descricao", "tipoExame.descricao");
            } else if (RelatorioResumoExamesDTOParam.FormaApresentacao.ESTABELECIMENTO_EXECUTANTE.equals(this.param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("localExame.codigo","localExame.codigo");
                hql.addToSelectAndGroupAndOrder("localExame.referencia","localExame.referencia");
                hql.addToSelectAndGroupAndOrder("localExame.descricao","localExame.descricao");
            }
        }

        if (RelatorioResumoExamesDTOParam.TipoResumo.UNIDADE_SOLICITANTE.equals(this.param.getTipoResumo())) {
            hql.addToSelectAndGroup("empresaSolicitante.codigo","empresa.codigo");
            hql.addToSelectAndGroup("empresaSolicitante.referencia","empresa.referencia");
            hql.addToSelectAndGroup("empresaSolicitante.descricao","empresa.descricao");
        } else if (RelatorioResumoExamesDTOParam.TipoResumo.PROFISSIONAL_SOLICITANTE.equals(this.param.getTipoResumo())) {
            hql.addToSelectAndGroup("profissional.codigo","profissional.codigo");
            hql.addToSelectAndGroup("profissional.nome","profissional.nome");
        } else if (RelatorioResumoExamesDTOParam.TipoResumo.TIPO_EXAME.equals(this.param.getTipoResumo())) {
            hql.addToSelectAndGroup("tipoExame.codigo","tipoExame.codigo");
            hql.addToSelectAndGroup("tipoExame.descricao","tipoExame.descricao");
        } else if (RelatorioResumoExamesDTOParam.TipoResumo.ESTABELECIMENTO_EXECUTANTE.equals(this.param.getTipoResumo())) {
            hql.addToSelectAndGroup("localExame.codigo","localExame.codigo");
            hql.addToSelectAndGroup("localExame.referencia","localExame.referencia");
            hql.addToSelectAndGroup("localExame.descricao","localExame.descricao");
        }

        hql.addToFrom("ExameRequisicao er"
                + " left join er.exame e "
                + " left join e.empresaSolicitante empresaSolicitante"
                + " left join e.profissional profissional "
                + " left join e.tipoExame tipoExame "
                + " left join e.localExame localExame "
                + " left join e.usuarioCadsus usuarioCadsus "
                );

        hql.addToWhereWhithAnd("empresaSolicitante", this.param.getUnidadeSolicitante());
        hql.addToWhereWhithAnd("profissional", this.param.getProfissionalSolicitante());
        hql.addToWhereWhithAnd("usuarioCadsus", this.param.getPaciente());
        hql.addToWhereWhithAnd("tipoExame", this.param.getTipoExame());
        hql.addToWhereWhithAnd("localExame = ", this.param.getEstabelecimentoExecutante());

        if (RelatorioResumoExamesDTOParam.TipoPeriodo.DATA_SOLICITACAO.equals(param.getTipoPeriodo())) {
            hql.addToWhereWhithAnd("e.dataSolicitacao", this.param.getPeriodo());
        } else if (RelatorioResumoExamesDTOParam.TipoPeriodo.DATA_CONFIRMACAO_PRESTADOR.equals(param.getTipoPeriodo())) {
            hql.addToWhereWhithAnd("er.dataConfirmacaoPrestador", this.param.getPeriodo());
        }

        hql.addToWhereWhithAnd("e.status <>", Exame.STATUS_CANCELADO);
        hql.addToWhereWhithAnd("er.status <> ", ExameRequisicao.Status.CANCELADO.value());
        
        if (RelatorioResumoExamesDTOParam.TipoResumo.PROFISSIONAL_SOLICITANTE.equals(this.param.getTipoResumo())
                || RelatorioResumoExamesDTOParam.FormaApresentacao.PROFISSIONAL_SOLICITANTE.equals(this.param.getFormaApresentacao())) {
            hql.addToWhereWhithAnd("profissional.codigo is not null");
        }
        
        if (RelatorioResumoExamesDTOParam.TipoResumo.ESTABELECIMENTO_EXECUTANTE.equals(this.param.getTipoResumo())
                || RelatorioResumoExamesDTOParam.FormaApresentacao.ESTABELECIMENTO_EXECUTANTE.equals(this.param.getFormaApresentacao())) {
            hql.addToWhereWhithAnd("localExame.codigo is not null");
        }

        if (this.param.getOrdenacao().equals(RelatorioResumoExamesDTOParam.Ordenacao.TOTAL_EXAMES)) {
            hql.addToOrder("3");
        } else if (this.param.getOrdenacao().equals(RelatorioResumoExamesDTOParam.Ordenacao.DIAS_MEDIA_ESPERA)) {
            hql.addToOrder("4");
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        dtoList = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public Collection getResult() {
        return dtoList;
    }
}
