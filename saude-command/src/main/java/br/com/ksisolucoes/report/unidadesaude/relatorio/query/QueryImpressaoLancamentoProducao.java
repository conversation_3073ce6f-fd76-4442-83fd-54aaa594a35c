package br.com.ksisolucoes.report.unidadesaude.relatorio.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.hospital.dto.RelatorioImpressaoFechamentoContaPacienteDTO;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryImpressaoLancamentoProducao extends CommandQuery<QueryImpressaoLancamentoProducao> implements ITransferDataReport<ContaPaciente, RelatorioImpressaoFechamentoContaPacienteDTO> {

    private ContaPaciente param;
    private List<RelatorioImpressaoFechamentoContaPacienteDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("emp.codigo", "empresa.codigo");
        hql.addToSelect("emp.referencia", "empresa.referencia");
        hql.addToSelect("emp.descricao", "empresa.descricao");
        hql.addToSelect("emp.cnes", "empresa.cnes");
        hql.addToSelect("ap.codigo", "numeroAtendimento");
        hql.addToSelect("ap.dataAtendimento", "dataAtendimento");
        hql.addToSelect("ap.dataAlta", "dataAlta");
        hql.addToSelect("ta.codigo", "tipoAtendimento.codigo");
        hql.addToSelect("ta.descricao", "tipoAtendimento.descricao");

        hql.addToSelect("u.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("u.nome", "usuarioCadsus.nome");
        hql.addToSelect("u.sexo", "usuarioCadsus.sexo");
        hql.addToSelect("u.dataNascimento", "usuarioCadsus.dataNascimento");

        hql.addToSelect("ai.dataChegada", "dataChegada");
        hql.addToSelect("ai.atendimentoPrincipal", "atendimentoInformacao.atendimentoPrincipal");

        hql.addToSelect("leitoQuarto.codigo", "leitoQuarto.codigo");
        hql.addToSelect("leitoQuarto.descricao", "leitoQuarto.descricao");

        hql.addToSelect("quartoInternacao.codigo", "quartoInternacao.codigo");
        hql.addToSelect("quartoInternacao.descricao", "quartoInternacao.descricao");

        hql.addToSelect("convenio.codigo", "convenio.codigo");
        hql.addToSelect("convenio.descricao", "convenio.descricao");

        hql.addToSelect("cpp.codigo", "contaPaciente.codigo");
        hql.addToSelect("cpp.status", "contaPaciente.status");
        hql.addToSelect("cpp.dataGeracao", "contaPaciente.dataGeracao");

        hql.addToSelect("proc.codigo", "procedimento.codigo");
        hql.addToSelect("proc.referencia", "procedimento.referencia");
        hql.addToSelect("proc.descricao", "procedimento.descricao");

        hql.addToSelect("prod.codigo", "produto.codigo");
        hql.addToSelect("prod.descricao", "produto.descricao");

        hql.addToSelect("pb.codigo", "produtoBrasindice.codigo");
        hql.addToSelect("pb.codigoTuss", "produtoBrasindice.codigoTuss");

        hql.addToSelect("exproc.codigo", "exameProcedimento.codigo");
        hql.addToSelect("exproc.descricaoProcedimento", "exameProcedimento.descricaoProcedimento");

        hql.addToSelect("icp.codigo", "itemContaPaciente.codigo");
        hql.addToSelect("icp.precoUnitario", "itemContaPaciente.precoUnitario");
        hql.addToSelect("icp.quantidade", "itemContaPaciente.quantidade");
        hql.addToSelect("icp.quantidadeDias", "itemContaPaciente.quantidadeDias");
        hql.addToSelect("icp.quantidadePorDia", "itemContaPaciente.quantidadePorDia");
        hql.addToSelect("icp.status", "itemContaPaciente.status");
        hql.addToSelect("icp.tipo", "itemContaPaciente.tipo");
        hql.addToSelect("icp.produtoTiss", "itemContaPaciente.produtoTiss");

        hql.addToSelect("(select min(aih.nroAutorizacao) from Aih aih"
                + " left join aih.contaPaciente contaPaciente "
                + " where contaPaciente = cp)", "numeroAutorizacao");

        hql.setTypeSelect(RelatorioImpressaoFechamentoContaPacienteDTO.class.getName());
        hql.addToFrom(" ItemContaPaciente icp"
                + " left join icp.contaPaciente cp"
                + " left join cp.contaPacientePrincipal cpp"
                + " left join cpp.atendimentoInformacao ai"
                + " left join icp.procedimento proc"
                + " left join icp.exameProcedimento exproc"
                + " left join icp.produto prod"
                + " left join ai.atendimentoPrincipal ap"
                + " left join ap.naturezaProcuraTipoAtendimento npta"
                + " left join npta.tipoAtendimento ta"
                + " left join cp.empresa emp"
                + " left join cp.convenio convenio"
                + " left join ai.leitoQuarto leitoQuarto"
                + " left join leitoQuarto.quartoInternacao quartoInternacao"
                + " left join cpp.usuarioCadsus u"
                + " left join prod.eloProdutoBrasindice epb"
                + " left join epb.produtoBrasindice pb");

        hql.addToWhereWhithAnd("cp.codigo = ", this.param.getCodigo());
        hql.addToWhereWhithAnd("icp.status = ", ItemContaPaciente.Status.CONFIRMADO.value());
        hql.addToWhereWhithAnd("icp.tipo in ", Arrays.asList(ItemContaPaciente.Tipo.PROCEDIMENTO.value()));

        hql.addToOrder("icp.tipo");
        hql.addToOrder("prod.descricao");
        hql.addToOrder("proc.descricao");
        hql.addToOrder("exproc.descricaoProcedimento");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioImpressaoFechamentoContaPacienteDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(ContaPaciente param) {
        this.param = param;
    }

}