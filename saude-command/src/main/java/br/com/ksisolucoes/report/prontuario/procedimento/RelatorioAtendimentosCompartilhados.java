/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.prontuario.procedimento;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioAtendimentosCompartilhadosDTOParam;
import br.com.ksisolucoes.report.prontuario.procedimento.query.QueryRelatorioAtendimentosCompartilhados;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioAtendimentosCompartilhados extends AbstractReport<RelatorioAtendimentosCompartilhadosDTOParam> {

    public RelatorioAtendimentosCompartilhados(RelatorioAtendimentosCompartilhadosDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        addParametro("FORMA_APRESENTACAO", getParam().getFormaApresentacao());
        return "/br/com/celk/report/unidadesaude/jrxml/relatorio_atendimentos_compartilhados.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_atendimentos_compartilhados");
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioAtendimentosCompartilhados();
    }

}
