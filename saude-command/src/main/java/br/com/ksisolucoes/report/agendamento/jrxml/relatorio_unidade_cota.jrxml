<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_agenda_unidade_vaga" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="FORMA_APRESENTACAO" class="br.com.ksisolucoes.report.agendamento.dto.RelatorioUnidadeCotaDTOParam.FormaApresentacao"/>
	<field name="unidade" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="unidadeExecutante" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="tipoProcedimento" class="br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento"/>
	<field name="vagasPeriodoLong" class="java.lang.Long"/>
	<field name="vagasUtilizadas" class="java.lang.Long"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="vagasPeriodoLong_1" class="java.lang.Long" resetType="Group" resetGroup="FORMA_APRESETANCAO" calculation="Sum">
		<variableExpression><![CDATA[$F{vagasPeriodoLong}]]></variableExpression>
	</variable>
	<variable name="vagasPeriodoLong_2" class="java.lang.Long" resetType="Group" resetGroup="default" calculation="Sum">
		<variableExpression><![CDATA[$F{vagasPeriodoLong}]]></variableExpression>
	</variable>
	<variable name="vagasUtilizadas_1" class="java.lang.Long" resetType="Group" resetGroup="FORMA_APRESETANCAO" calculation="Sum">
		<variableExpression><![CDATA[$F{vagasUtilizadas}]]></variableExpression>
	</variable>
	<variable name="vagasUtilizadas_2" class="java.lang.Long" resetType="Group" resetGroup="default" calculation="Sum">
		<variableExpression><![CDATA[$F{vagasUtilizadas}]]></variableExpression>
	</variable>
	<group name="default">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupFooter>
			<band height="12">
				<textField>
					<reportElement x="334" y="1" width="70" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{vagasPeriodoLong_2}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="404" y="1" width="81" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{vagasUtilizadas_2}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="229" y="0" width="105" height="11"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_total_geral")]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="FORMA_APRESETANCAO">
		<groupExpression><![CDATA[br.com.ksisolucoes.report.agendamento.dto.RelatorioUnidadeCotaDTOParam.FormaApresentacao.UNIDADE.equals($P{FORMA_APRESENTACAO})
?
    $F{unidade}
:
    br.com.ksisolucoes.report.agendamento.dto.RelatorioUnidadeCotaDTOParam.FormaApresentacao.UNIDADE_EXECUTANTE.equals($P{FORMA_APRESENTACAO})
    ?
        $F{unidadeExecutante}
    :
        br.com.ksisolucoes.report.agendamento.dto.RelatorioUnidadeCotaDTOParam.FormaApresentacao.TIPO_PROCEDIMENTO.equals($P{FORMA_APRESENTACAO})
        ?
            $F{tipoProcedimento}
        :
            null]]></groupExpression>
		<groupHeader>
			<band height="23">
				<textField>
					<reportElement x="1" y="11" width="165" height="11"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[br.com.ksisolucoes.report.agendamento.dto.RelatorioUnidadeCotaDTOParam.FormaApresentacao.UNIDADE.equals($P{FORMA_APRESENTACAO})
?
    Bundle.getStringApplication("rotulo_unidade_executante")
:
    br.com.ksisolucoes.report.agendamento.dto.RelatorioUnidadeCotaDTOParam.FormaApresentacao.UNIDADE_EXECUTANTE.equals($P{FORMA_APRESENTACAO})
    ?
        Bundle.getStringApplication("rotulo_unidade")
    :
        br.com.ksisolucoes.report.agendamento.dto.RelatorioUnidadeCotaDTOParam.FormaApresentacao.TIPO_PROCEDIMENTO.equals($P{FORMA_APRESENTACAO})
        ?
            Bundle.getStringApplication("rotulo_unidade")
        :
            null]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="21" width="535" height="1"/>
				</line>
				<textField>
					<reportElement x="485" y="11" width="50" height="11"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_saldo")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="404" y="11" width="80" height="11"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_vagas_utilizadas")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="0" width="535" height="10"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*FA*/
br.com.ksisolucoes.report.agendamento.dto.RelatorioUnidadeCotaDTOParam.FormaApresentacao.UNIDADE.equals($P{FORMA_APRESENTACAO})
?
    Bundle.getStringApplication("rotulo_unidade")+":"+$F{unidade}.getDescricaoFormatado()
:
    br.com.ksisolucoes.report.agendamento.dto.RelatorioUnidadeCotaDTOParam.FormaApresentacao.UNIDADE_EXECUTANTE.equals($P{FORMA_APRESENTACAO})
    ?
        Bundle.getStringApplication("rotulo_unidade_executante")+":"+$F{unidadeExecutante}.getDescricaoFormatado()
    :
        br.com.ksisolucoes.report.agendamento.dto.RelatorioUnidadeCotaDTOParam.FormaApresentacao.TIPO_PROCEDIMENTO.equals($P{FORMA_APRESENTACAO})
        ?
            Bundle.getStringApplication("rotulo_tipo_procedimento")+":"+$F{tipoProcedimento}.getDescricaoFormatado()
        :
            null]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="169" y="11" width="130" height="11"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[br.com.ksisolucoes.report.agendamento.dto.RelatorioUnidadeCotaDTOParam.FormaApresentacao.UNIDADE.equals($P{FORMA_APRESENTACAO})
?
    Bundle.getStringApplication("rotulo_tipo_procedimento")
:
    br.com.ksisolucoes.report.agendamento.dto.RelatorioUnidadeCotaDTOParam.FormaApresentacao.UNIDADE_EXECUTANTE.equals($P{FORMA_APRESENTACAO})
    ?
        Bundle.getStringApplication("rotulo_tipo_procedimento")
    :
        br.com.ksisolucoes.report.agendamento.dto.RelatorioUnidadeCotaDTOParam.FormaApresentacao.TIPO_PROCEDIMENTO.equals($P{FORMA_APRESENTACAO})
        ?
            Bundle.getStringApplication("rotulo_unidade_executante")
        :
            null]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="299" y="11" width="105" height="11"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_vagas_periodo")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="16">
				<textField>
					<reportElement x="334" y="2" width="70" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{vagasPeriodoLong_1}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="405" y="2" width="80" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression class="java.lang.Long"><![CDATA[$V{vagasUtilizadas_1}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="229" y="2" width="105" height="11"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_total")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="299" y="1" width="186" height="1"/>
				</line>
			</band>
		</groupFooter>
	</group>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<detail>
		<band height="12" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="1" y="1" width="165" height="10"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[br.com.ksisolucoes.report.agendamento.dto.RelatorioUnidadeCotaDTOParam.FormaApresentacao.UNIDADE.equals($P{FORMA_APRESENTACAO})
?
    $F{unidadeExecutante}.getDescricaoFormatado()
:
    br.com.ksisolucoes.report.agendamento.dto.RelatorioUnidadeCotaDTOParam.FormaApresentacao.UNIDADE_EXECUTANTE.equals($P{FORMA_APRESENTACAO})
    ?
        $F{unidade}.getDescricaoFormatado()
    :
        br.com.ksisolucoes.report.agendamento.dto.RelatorioUnidadeCotaDTOParam.FormaApresentacao.TIPO_PROCEDIMENTO.equals($P{FORMA_APRESENTACAO})
        ?
            $F{unidade}.getDescricaoFormatado()
        :
            null]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="334" y="1" width="70" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression class="java.lang.Long"><![CDATA[$F{vagasPeriodoLong}]]></textFieldExpression>
			</textField>
			<textField pattern="###0" isBlankWhenNull="true">
				<reportElement x="404" y="1" width="81" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression class="java.lang.Long"><![CDATA[$F{vagasUtilizadas}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="485" y="1" width="50" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression class="java.lang.Long"><![CDATA[$F{vagasPeriodoLong}-$F{vagasUtilizadas} > 0L ? $F{vagasPeriodoLong}-$F{vagasUtilizadas} : 0L]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="169" y="1" width="165" height="10"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[br.com.ksisolucoes.report.agendamento.dto.RelatorioUnidadeCotaDTOParam.FormaApresentacao.UNIDADE.equals($P{FORMA_APRESENTACAO})
?
    $F{tipoProcedimento}.getDescricaoFormatado()
:
    br.com.ksisolucoes.report.agendamento.dto.RelatorioUnidadeCotaDTOParam.FormaApresentacao.UNIDADE_EXECUTANTE.equals($P{FORMA_APRESENTACAO})
    ?
        $F{tipoProcedimento}.getDescricaoFormatado()
    :
        br.com.ksisolucoes.report.agendamento.dto.RelatorioUnidadeCotaDTOParam.FormaApresentacao.TIPO_PROCEDIMENTO.equals($P{FORMA_APRESENTACAO})
        ?
            $F{unidadeExecutante}.getDescricaoFormatado()
        :
            null]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
