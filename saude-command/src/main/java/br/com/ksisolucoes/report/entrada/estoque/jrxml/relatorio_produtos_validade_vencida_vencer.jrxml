<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_produtos_validade_vencida_vencer" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="90938fb6-70e1-43f5-b53c-51ffeafbb897">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="3.1384283767210097"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.report.ReportProperties"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<field name="subGrupoDescricao" class="java.lang.String">
		<fieldDescription><![CDATA[subGrupoDescricao]]></fieldDescription>
	</field>
	<field name="produtoDescricaoFormatado" class="java.lang.String">
		<fieldDescription><![CDATA[produtoDescricaoFormatado]]></fieldDescription>
	</field>
	<field name="estoqueFisico" class="java.lang.Double">
		<fieldDescription><![CDATA[estoqueFisico]]></fieldDescription>
	</field>
	<field name="unidadeUnidade" class="java.lang.String">
		<fieldDescription><![CDATA[unidadeUnidade]]></fieldDescription>
	</field>
	<field name="grupoCodigo" class="java.lang.Long">
		<fieldDescription><![CDATA[grupoCodigo]]></fieldDescription>
	</field>
	<field name="produtoDescricao" class="java.lang.String">
		<fieldDescription><![CDATA[produtoDescricao]]></fieldDescription>
	</field>
	<field name="grupoDescricao" class="java.lang.String">
		<fieldDescription><![CDATA[grupoDescricao]]></fieldDescription>
	</field>
	<field name="subGrupoCodigo" class="java.lang.Long">
		<fieldDescription><![CDATA[subGrupoCodigo]]></fieldDescription>
	</field>
	<field name="empresaDescricaoFormatado" class="java.lang.String">
		<fieldDescription><![CDATA[empresaDescricaoFormatado]]></fieldDescription>
	</field>
	<field name="subGrupoDescricaoFormatado" class="java.lang.String">
		<fieldDescription><![CDATA[subGrupoDescricaoFormatado]]></fieldDescription>
	</field>
	<field name="produtoCodigo" class="java.lang.String">
		<fieldDescription><![CDATA[produtoCodigo]]></fieldDescription>
	</field>
	<field name="empresaDescricao" class="java.lang.String">
		<fieldDescription><![CDATA[empresaDescricao]]></fieldDescription>
	</field>
	<field name="empresaCodigo" class="java.lang.String">
		<fieldDescription><![CDATA[empresaCodigo]]></fieldDescription>
	</field>
	<field name="grupoDescricaoFormatado" class="java.lang.String">
		<fieldDescription><![CDATA[grupoDescricaoFormatado]]></fieldDescription>
	</field>
	<field name="codigoDeposito" class="java.lang.Long"/>
	<field name="descricaoDeposito" class="java.lang.String"/>
	<field name="descricaoDepositoFormatado" class="java.lang.String"/>
	<field name="grupoEstoque" class="java.lang.String"/>
	<field name="dataValidade" class="java.util.Date"/>
	<field name="mascaraLocalizacaoEstrutura" class="java.lang.String"/>
	<field name="consumo" class="java.lang.Double"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<group name="GrupoEmpresa" isReprintHeaderOnEachPage="true" keepTogether="true">
		<groupExpression><![CDATA[$F{empresaDescricao}]]></groupExpression>
		<groupHeader>
			<band height="16" splitType="Stretch">
				<rectangle radius="10">
					<reportElement uuid="4079f19b-958a-4fb3-b908-c0c5985b841a" x="0" y="0" width="555" height="16"/>
				</rectangle>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement uuid="3c3e895d-6b12-4240-b027-b87f50f88d8d" key="textField-43" mode="Transparent" x="6" y="1" width="542" height="14" forecolor="#000000" backcolor="#CCCCCC"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*empresa*/
$F{empresaCodigo} != null
?
    $V{BUNDLE}.getStringApplication("rotulo_empresa") + ": (" + $F{empresaCodigo}.trim() + ") " + $F{empresaDescricao}
:
    $V{BUNDLE}.getStringApplication("rotulo_empresa") + ": " + $V{BUNDLE}.getStringApplication("rotulo_sem_cadastro")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="10"/>
		</groupFooter>
	</group>
	<group name="Deposito" isReprintHeaderOnEachPage="true" keepTogether="true">
		<groupExpression><![CDATA[$F{codigoDeposito}]]></groupExpression>
		<groupHeader>
			<band height="16">
				<textField pattern="" isBlankWhenNull="false">
					<reportElement uuid="139324a9-8050-40bd-b227-320db3a3f7a9" key="textField-43" mode="Transparent" x="0" y="2" width="555" height="14" forecolor="#000000" backcolor="#CCCCCC"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_deposito") + ": " + $F{descricaoDepositoFormatado}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="10"/>
		</groupFooter>
	</group>
	<group name="GrupoGrupo" isReprintHeaderOnEachPage="true" keepTogether="true">
		<groupExpression><![CDATA[$F{grupoCodigo} + " " + $F{subGrupoCodigo}]]></groupExpression>
		<groupHeader>
			<band height="17" splitType="Stretch">
				<textField pattern="" isBlankWhenNull="false">
					<reportElement uuid="dfcc3ac4-f38b-4899-b80f-6a414ad7edbe" key="textField-43" mode="Transparent" x="13" y="1" width="542" height="14" forecolor="#000000" backcolor="#CCCCCC"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*grupo*/
$F{grupoCodigo} == null
?
    $V{BUNDLE}.getStringApplication("rotulo_grupo")  + ": " + $V{BUNDLE}.getStringApplication("rotulo_sem_cadastro") + " / " + $V{BUNDLE}.getStringApplication("rotulo_subgrupo") + ": " + $V{BUNDLE}.getStringApplication("rotulo_sem_cadastro")
:
    $F{subGrupoCodigo} == null
    ?
        $V{BUNDLE}.getStringApplication("rotulo_grupo") + ": " + $F{grupoDescricaoFormatado} + " / " + $V{BUNDLE}.getStringApplication("rotulo_subgrupo") + ": " + $V{BUNDLE}.getStringApplication("rotulo_sem_cadastro")
    :
        $V{BUNDLE}.getStringApplication("rotulo_grupo") + ": " + $F{grupoDescricaoFormatado} + " / " + $V{BUNDLE}.getStringApplication("rotulo_subgrupo") + ": " + $F{subGrupoDescricaoFormatado}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="10"/>
		</groupFooter>
	</group>
	<group name="GrupoSubGrupo" isReprintHeaderOnEachPage="true" keepTogether="true">
		<groupExpression><![CDATA[$F{subGrupoCodigo}]]></groupExpression>
		<groupHeader>
			<band height="12" splitType="Stretch">
				<textField pattern="" isBlankWhenNull="false">
					<reportElement uuid="c612a2cf-a50e-479f-b2e5-6740e8d05cf1" key="textField-45" mode="Transparent" x="3" y="0" width="275" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*produto*/
$V{BUNDLE}.getStringApplication("rotulo_produto")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement uuid="09c0a853-b1c2-47f0-9709-1742673dd493" key="textField-46" mode="Transparent" x="280" y="0" width="14" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*unidade*/
$V{BUNDLE}.getStringApplication("rotulo_unidade_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement uuid="13fcf82d-9db0-45f4-b515-7033fb16fdc5" key="textField-47" mode="Transparent" x="445" y="0" width="50" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*est.Fisico*/
$V{BUNDLE}.getStringApplication("rotulo_estoque_fisico")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement uuid="26c9b300-79f1-4e5c-bc70-26a704409643" x="0" y="11" width="555" height="1"/>
				</line>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement uuid="82a409a7-0b6d-41d1-af4d-eefda31d3058" key="textField-7" mode="Transparent" x="395" y="0" width="48" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*dataValidade*/
$V{BUNDLE}.getStringApplication("rotulo_data_validade_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement uuid="cf461cbb-d4a9-4a59-8901-1c14f3880a1c" key="textField-7" mode="Transparent" x="296" y="0" width="49" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Lote*/
$V{BUNDLE}.getStringApplication("rotulo_lote")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement uuid="c8adc764-d8e0-4b6b-8a65-4070250e5225" key="textField-7" mode="Transparent" x="347" y="0" width="47" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*localizacao*/
$V{BUNDLE}.getStringApplication("rotulo_localizacao")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement uuid="cc7514a9-103b-4a8b-8444-1279745779d6" key="textField-47" mode="Transparent" x="497" y="0" width="57" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*consumo*/
$V{BUNDLE}.getStringApplication("rotulo_consumo_medio")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="10"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="10" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="be7626b1-dc3c-4871-ab13-795ab2f279f2" key="textField-5" mode="Transparent" x="4" y="0" width="274" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{produtoDescricaoFormatado}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="4232bbc5-86c8-4c3c-9906-286601803e83" key="textField-6" mode="Transparent" x="280" y="0" width="14" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unidadeUnidade}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="60901c3b-70a1-459b-b21c-4703797520c0" key="textField-46" mode="Transparent" x="296" y="0" width="49" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{grupoEstoque}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="3ea81659-0eab-4d5c-9e87-0b29ccc54463" key="textField-46" mode="Transparent" x="395" y="0" width="48" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.formatar($F{dataValidade})]]></textFieldExpression>
			</textField>
			<textField pattern="###0">
				<reportElement uuid="e543f139-552e-45bd-a2fa-b57e02a23c30" mode="Transparent" x="445" y="0" width="50" height="10"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{estoqueFisico}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="1e55a748-044d-4bda-95c7-52198fda7818" key="textField-46" mode="Transparent" x="347" y="0" width="47" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mascaraLocalizacaoEstrutura}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00">
				<reportElement uuid="17704c67-a201-412a-b999-1083b8a7525f" mode="Transparent" x="497" y="0" width="57" height="10"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{consumo} == null
?
0.00
:
$F{consumo}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
