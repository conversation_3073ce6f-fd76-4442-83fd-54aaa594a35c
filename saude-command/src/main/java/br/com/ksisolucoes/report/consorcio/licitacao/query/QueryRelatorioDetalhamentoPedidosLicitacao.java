package br.com.ksisolucoes.report.consorcio.licitacao.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoPedidosLicitacaoDTO;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoPedidosLicitacaoDTOParam;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoPedidosLicitacaoDTOParam.FormaApresentacao;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoPedidosLicitacaoDTOParam.Ordenacao;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoPedidosLicitacaoDTOParam.TipoResumo;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public class QueryRelatorioDetalhamentoPedidosLicitacao extends CommandQuery implements ITransferDataReport<RelatorioDetalhamentoPedidosLicitacaoDTOParam, RelatorioDetalhamentoPedidosLicitacaoDTO> {

    private RelatorioDetalhamentoPedidosLicitacaoDTOParam param;
    private List<RelatorioDetalhamentoPedidosLicitacaoDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("empresa.codigo", "empresa.codigo");
        hql.addToSelect("empresa.descricao", "empresa.descricao");
        hql.addToSelect("pedidoLicitacao.codigo", "codigoPedido");
        hql.addToSelect("pedidoLicitacao.dataCadastro", "dataCadastro");
        hql.addToSelect("produto.codigo", "produto.codigo");
        hql.addToSelect("produto.descricao", "produto.descricao");
        hql.addToSelect("pedidoLicitacaoItem.quantidade", "quantidade");
        hql.addToSelect("pedidoLicitacaoItem.precoReal", "precoReal");
        hql.addToSelect("pedidoLicitacaoItem.valorTotalLicitado", "valorTotalLicitado");
        hql.addToSelect("pedidoLicitacaoItem.quantidadeRecebida", "quantidadeRecebida");
        hql.addToSelect("pedidoLicitacaoItem.status", "status");
        hql.addToSelect("pessoa.codigo", "pessoa.codigo");
        hql.addToSelect("pessoa.descricao", "pessoa.descricao");

        hql.setTypeSelect(RelatorioDetalhamentoPedidosLicitacaoDTO.class.getName());
        hql.addToFrom("EloLicitacaoPedido eloLicitacaoPedido"
                + " right join eloLicitacaoPedido.pedidoLicitacaoItem pedidoLicitacaoItem"
                + " left join pedidoLicitacaoItem.pedidoLicitacao pedidoLicitacao"
                + " left join pedidoLicitacao.empresa empresa"
                + " left join pedidoLicitacaoItem.produto produto"
                + " left join eloLicitacaoPedido.licitacaoItem licitacaoItem"
                + " left join licitacaoItem.pessoa pessoa"
                + " left join licitacaoItem.licitacao licitacao");

        hql.addToWhereWhithAnd("empresa =", param.getConsorciado());
        hql.addToWhereWhithAnd("produto =", param.getProduto());
        hql.addToWhereWhithAnd("pessoa =", param.getPessoa());
        hql.addToWhereWhithAnd("pedidoLicitacao.dataCadastro ", param.getPeriodo());
        hql.addToWhereWhithAnd("pedidoLicitacaoItem.status =", param.getStatus());
        hql.addToWhereWhithAnd("licitacao.codigo =", param.getCodigoLicitacao());
        hql.addToWhereWhithAnd("licitacao.numeroPregao =", param.getNumeroPregao());


        if (FormaApresentacao.CONSORCIADO.equals(param.getFormaApresentacao())) {
            hql.addToOrder("empresa.descricao");
        } else if (FormaApresentacao.PEDIDO.equals(param.getFormaApresentacao())) {
            hql.addToOrder("pedidoLicitacao.codigo");
        } else if (FormaApresentacao.PRODUTO.equals(param.getFormaApresentacao())) {
            hql.addToOrder("produto.descricao");
        } else if (FormaApresentacao.FORNECEDOR.equals(param.getFormaApresentacao())) {
            hql.addToOrder("pessoa.descricao");
        }

        if (TipoResumo.CONSORCIADO.equals(param.getTipoResumo())) {
            hql.addToOrder("empresa.descricao");
        } else if (TipoResumo.PEDIDO.equals(param.getTipoResumo())) {
            hql.addToOrder("pedidoLicitacao.codigo");
        } else if (TipoResumo.PRODUTO.equals(param.getTipoResumo())) {
            hql.addToOrder("produto.descricao");
        } else if (TipoResumo.FORNECEDOR.equals(param.getTipoResumo())) {
            hql.addToOrder("pessoa.descricao");
        }

        if (Ordenacao.CONSORCIADO.equals(param.getOrdenacao())) {
            hql.addToOrder("empresa.descricao");
        } else if (Ordenacao.SITUACAO.equals(param.getOrdenacao())) {
            hql.addToOrder("pedidoLicitacaoItem.status");
        } else if (Ordenacao.PRODUTO.equals(param.getOrdenacao())) {
            hql.addToOrder("produto.descricao");
        }

    }

    @Override
    public Collection getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List) result);
    }

    @Override
    public void setDTOParam(RelatorioDetalhamentoPedidosLicitacaoDTOParam param) {
        this.param = param;
    }
}
