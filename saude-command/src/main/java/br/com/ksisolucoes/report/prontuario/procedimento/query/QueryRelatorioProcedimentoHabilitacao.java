/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.prontuario.procedimento.query;

import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioProcedimentoHabilitacaoParam;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoHabilitacao;
import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioProcedimentoHabilitacao extends CommandQuery implements ITransferDataReport< RelatorioProcedimentoHabilitacaoParam, ProcedimentoHabilitacao> {

    private RelatorioProcedimentoHabilitacaoParam dTOParam;
    private List<ProcedimentoHabilitacao> procedimentoHabilitacao;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.setTypeSelect(ProcedimentoHabilitacao.class.getName());

        hql.addToSelect("ph.id.procedimentoHabilitacaoCadastro.codigo", true);
        hql.addToSelect("ph.id.procedimentoHabilitacaoCadastro.descricao", true);
        hql.addToSelect("ph.id.procedimentoGrupoHabilitacao.codigo", true);
        hql.addToSelect("ph.id.procedimentoGrupoHabilitacao.descricao", true);
        hql.addToSelect("ph.id.procedimentoCompetencia.id.procedimento.codigo", true);
        hql.addToSelect("ph.id.procedimentoCompetencia.id.procedimento.descricao", true);
        hql.addToSelect("ph.id.procedimentoCompetencia.id.dataCompetencia", true);

        hql.addToFrom(ProcedimentoHabilitacao.class.getName(), "ph");

        hql.addToWhereWhithAnd("ph.id.procedimentoGrupoHabilitacao.codigo in", this.dTOParam.getProcedimentoGrupoHabilitacao());
        hql.addToWhereWhithAnd("ph.id.procedimentoHabilitacaoCadastro.codigo in", this.dTOParam.getProcedimentoHabilitacao());
        hql.addToWhereWhithAnd("ph.id.procedimentoCompetencia.id.procedimento.codigo in", this.dTOParam.getProcedimentos());

        hql.addToOrder("ph.id.procedimentoCompetencia.id.procedimento.codigo");
        hql.addToOrder("ph.id.procedimentoGrupoHabilitacao.codigo");
        hql.addToOrder("ph.id.procedimentoHabilitacaoCadastro.codigo");

    }

    public void setDTOParam(RelatorioProcedimentoHabilitacaoParam param) {
        this.dTOParam = param;
    }

    public Collection<ProcedimentoHabilitacao> getResult() {
        return this.procedimentoHabilitacao;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.procedimentoHabilitacao = hql.getBeanList((List) result);
    }

}
