<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_impressao_controle_infeccao_hospitalar" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="984d6952-fb05-43b9-a758-90ffa106d0ab">
	<property name="ireport.zoom" value="1.6500000000000028"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="73"/>
	<import value="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.AtendimentoAnamnese"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<field name="leitoQuarto" class="br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto"/>
	<field name="convenio" class="br.com.ksisolucoes.vo.prontuario.basico.Convenio"/>
	<field name="quartoInternacao" class="br.com.ksisolucoes.vo.prontuario.hospital.QuartoInternacao"/>
	<field name="numeroAtendimento" class="java.lang.Long"/>
	<field name="tipoAtendimento" class="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"/>
	<field name="dataChegada" class="java.util.Date"/>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="tabelaCbo" class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"/>
	<field name="controleInfeccaoHospitalar" class="br.com.ksisolucoes.vo.prontuario.hospital.ControleInfeccaoHospitalar"/>
	<field name="cirurgias" class="java.lang.Boolean"/>
	<field name="procedimentosDiagnosticos" class="java.lang.Boolean"/>
	<field name="procedimentosTerapeuticos" class="java.lang.Boolean"/>
	<field name="cateterismoUrinario" class="java.lang.Boolean"/>
	<field name="local" class="java.lang.Boolean"/>
	<field name="germeIsolado" class="java.lang.Boolean"/>
	<field name="terapeuticos" class="java.lang.Boolean"/>
	<field name="profilaticos" class="java.lang.Boolean"/>
	<field name="viasAereasSuperiores" class="java.lang.Boolean"/>
	<field name="viasAereasInferiores" class="java.lang.Boolean"/>
	<field name="gastroIntestinal" class="java.lang.Boolean"/>
	<field name="epidermeDerme" class="java.lang.Boolean"/>
	<field name="ossea" class="java.lang.Boolean"/>
	<field name="sistemaNervoso" class="java.lang.Boolean"/>
	<field name="seplicemia" class="java.lang.Boolean"/>
	<field name="urinaria" class="java.lang.Boolean"/>
	<title>
		<band splitType="Stretch"/>
	</title>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="437" splitType="Stretch">
			<rectangle radius="5">
				<reportElement uuid="eaed94ec-622a-4cca-8819-44a4efdf69a4" x="0" y="9" width="555" height="73"/>
			</rectangle>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="90" width="555" height="13"/>
				<textElement>
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_fatores_risco_para_infeccoes")+
($F{controleInfeccaoHospitalar}.getRisco() > 0L ? " ( X ) SIM (   ) NAO" : "(   ) SIM ( X ) NAO")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="f644d319-0be3-4703-affa-c7ab941fa848" key="textField-77" mode="Transparent" x="440" y="29" width="114" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getSexoFormatado()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="62ccd2df-ed6d-4a6c-a5d5-9ca98adb6500" key="textField-77" mode="Transparent" x="4" y="29" width="92" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_de_nascimento")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement uuid="f644d319-0be3-4703-affa-c7ab941fa848" key="textField-77" mode="Transparent" x="96" y="29" width="88" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getDataNascimento()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement uuid="792cedce-243e-449e-b3a9-2e042db7f8a3" key="textField-76" mode="Transparent" x="221" y="42" width="35" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quarto")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement uuid="792cedce-243e-449e-b3a9-2e042db7f8a3" key="textField-76" mode="Opaque" x="22" y="2" width="115" height="13" isRemoveLineWhenBlank="true" forecolor="#000000"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dados_atendimento")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement uuid="792cedce-243e-449e-b3a9-2e042db7f8a3" key="textField-76" mode="Transparent" x="414" y="42" width="26" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_leito")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="f644d319-0be3-4703-affa-c7ab941fa848" key="textField-77" mode="Transparent" x="65" y="68" width="55" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{numeroAtendimento}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement uuid="792cedce-243e-449e-b3a9-2e042db7f8a3" key="textField-76" mode="Transparent" x="414" y="16" width="52" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_prontuario")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement uuid="792cedce-243e-449e-b3a9-2e042db7f8a3" key="textField-76" mode="Transparent" x="4" y="42" width="47" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_convenio")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="f644d319-0be3-4703-affa-c7ab941fa848" key="textField-77" mode="Transparent" x="51" y="42" width="170" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{convenio}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="f644d319-0be3-4703-affa-c7ab941fa848" key="textField-77" mode="Transparent" x="466" y="16" width="88" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getCodigo()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement uuid="792cedce-243e-449e-b3a9-2e042db7f8a3" key="textField-76" mode="Transparent" x="414" y="29" width="26" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_sexo")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="f644d319-0be3-4703-affa-c7ab941fa848" key="textField-77" mode="Transparent" x="256" y="42" width="150" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quartoInternacao}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement uuid="792cedce-243e-449e-b3a9-2e042db7f8a3" key="textField-76" mode="Transparent" x="4" y="68" width="61" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_atendimento")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH:mm" isBlankWhenNull="true">
				<reportElement uuid="f644d319-0be3-4703-affa-c7ab941fa848" key="textField-77" mode="Transparent" x="83" y="55" width="86" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataChegada}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement uuid="792cedce-243e-449e-b3a9-2e042db7f8a3" key="textField-76" mode="Transparent" x="46" y="16" width="368" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNome()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="f644d319-0be3-4703-affa-c7ab941fa848" key="textField-77" mode="Transparent" x="249" y="55" width="251" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresa}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="f644d319-0be3-4703-affa-c7ab941fa848" key="textField-77" mode="Transparent" x="246" y="68" width="254" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tipoAtendimento}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement uuid="792cedce-243e-449e-b3a9-2e042db7f8a3" key="textField-76" mode="Transparent" x="4" y="16" width="42" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_paciente")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement uuid="792cedce-243e-449e-b3a9-2e042db7f8a3" key="textField-76" mode="Transparent" x="221" y="55" width="28" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_setor")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement uuid="792cedce-243e-449e-b3a9-2e042db7f8a3" key="textField-76" mode="Transparent" x="221" y="29" width="29" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_idade")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="f644d319-0be3-4703-affa-c7ab941fa848" key="textField-77" mode="Transparent" x="440" y="42" width="114" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{leitoQuarto}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement uuid="792cedce-243e-449e-b3a9-2e042db7f8a3" key="textField-76" mode="Transparent" x="221" y="68" width="25" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_tipo")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="f644d319-0be3-4703-affa-c7ab941fa848" key="textField-77" mode="Transparent" x="250" y="29" width="128" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getDescricaoIdade()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="62ccd2df-ed6d-4a6c-a5d5-9ca98adb6500" key="textField-77" mode="Transparent" x="4" y="55" width="79" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_da_chegada")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="109" width="137" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cirurgias")+" . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . ."]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="121" width="137" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_procedimentos_diagnosticos")+" . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . ."]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="133" width="137" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_procedimentos_terapeuticos")+" . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . ."]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="145" width="137" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cateterismo_urinario")+" . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . ."]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="174" width="555" height="13"/>
				<textElement>
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cultura")+
($F{controleInfeccaoHospitalar}.getCultura() > 0L ? " ( X ) SIM (   ) NAO" : "(   ) SIM ( X ) NAO")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="193" width="137" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_local")+" . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . ."]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="205" width="137" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_germe_isolado")+" . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . ."]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="240" width="137" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_terapeuticos")+" . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . ."]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="252" width="137" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_profilaticos")+" . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . ."]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="157" width="31" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_outros")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="31" y="157" width="523" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{controleInfeccaoHospitalar}.getRiscoOutros()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="264" width="31" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_outros")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="31" y="264" width="524" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{controleInfeccaoHospitalar}.getQuimioOutros()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="281" width="554" height="13"/>
				<textElement>
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_local_infeccao")+
($F{controleInfeccaoHospitalar}.getLocalInfeccao() > 0L ? " ( X ) SIM (   ) NAO" : "(   ) SIM ( X ) NAO")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="335" width="137" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_epiderme_derme")+" . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . ."]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="323" width="137" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_gastro_intestinal")+" . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . ."]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="299" width="137" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_vias_aereas_superiores")+" . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . ."]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="311" width="137" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_vias_aereas_inferiores")+" . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . ."]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="347" width="137" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_ossea")+" . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . ."]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="359" width="137" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_sistema_nervoso")+" . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . ."]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="371" width="137" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_seplicemia")+" . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . ."]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="383" width="137" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_urinaria")+" . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . .. . . . . . . . . . . . . . ."]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="395" width="31" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_outros")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="31" y="395" width="524" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{controleInfeccaoHospitalar}.getLocalInfeccaoOutros()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="413" width="88" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_tipo_tratamento")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="277" y="413" width="39" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cirurgia")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="316" y="413" width="239" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{controleInfeccaoHospitalar}.getSituacaoTratamentoDescricao()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="425" width="40" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_infeccao")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="41" y="425" width="236" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{controleInfeccaoHospitalar}.getInfeccaoDescricao()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="360" y="425" width="195" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{controleInfeccaoHospitalar}.getCondicoesAltaDescricao()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="277" y="425" width="83" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_condicoes_alta")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="88" y="413" width="189" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{controleInfeccaoHospitalar}.getTipoTratamentoDescricao()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="0" y="222" width="555" height="13"/>
				<textElement>
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_antibioticos_quimioterapicos")+
($F{controleInfeccaoHospitalar}.getQuimioTerapicos() > 0L ? " ( X ) SIM (   ) NAO" : "(   ) SIM ( X ) NAO")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="137" y="109" width="73" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cirurgias} ? "( X )"
: 
"(    )"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="137" y="121" width="73" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{procedimentosDiagnosticos} ? "( X )"
: 
"(    )"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="137" y="133" width="73" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{procedimentosTerapeuticos} ? "( X )"
: 
"(    )"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="137" y="145" width="73" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cateterismoUrinario}? "( X )"
: 
"(    )"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="137" y="193" width="73" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{local}? "( X )"
: 
"(    )"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="137" y="205" width="73" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{germeIsolado} ? "( X )"
: 
"(    )"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="137" y="240" width="73" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{terapeuticos} ? "( X )"
: 
"(    )"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="137" y="252" width="73" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{profilaticos} ? "( X )"
: 
"(    )"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="137" y="299" width="73" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{viasAereasSuperiores} ? "( X )"
: 
"(    )"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="137" y="311" width="73" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{viasAereasInferiores} ? "( X )"
: 
"(    )"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="137" y="323" width="73" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{gastroIntestinal} ? "( X )"
: 
"(    )"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="137" y="335" width="73" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{epidermeDerme} ? "( X )"
: 
"(    )"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="137" y="347" width="73" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ossea}? "( X )"
: 
"(    )"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="137" y="359" width="73" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{sistemaNervoso} ? "( X )"
: 
"(    )"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="137" y="371" width="73" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{seplicemia} ? "( X )"
: 
"(    )"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6fa6a7ce-e6ed-4d85-b168-8eb12d7c162e" x="137" y="383" width="73" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{urinaria}? "( X )"
: 
"(    )"]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="98">
			<textField>
				<reportElement uuid="b9b86659-fe94-4b07-816c-f72f441c3bc7" x="120" y="61" width="264" height="13"/>
				<textElement textAlignment="Center">
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_assinatura_medico_responsavel")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement uuid="65c41752-13b0-46b3-9c61-e684b73c5e9e" x="120" y="59" width="264" height="1"/>
			</line>
			<textField>
				<reportElement uuid="b9b86659-fe94-4b07-816c-f72f441c3bc7" x="120" y="74" width="264" height="13"/>
				<textElement textAlignment="Center">
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{profissional}.getNome()]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
