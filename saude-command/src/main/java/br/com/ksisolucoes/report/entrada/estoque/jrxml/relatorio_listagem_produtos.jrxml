<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="report name" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="11cdab03-6a2c-4464-9bbe-41c197b00890">
	<property name="ireport.zoom" value="2.143588810000002"/>
	<property name="ireport.x" value="59"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<parameter name="FORMA_APRESENTACAO" class="br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioListagemProdutosDTOParam.FormaApresentacao"/>
	<field name="descricaoFormatado" class="java.lang.String"/>
	<field name="unidade" class="br.com.ksisolucoes.vo.entradas.estoque.Unidade"/>
	<field name="subGrupo" class="br.com.ksisolucoes.vo.entradas.estoque.SubGrupo"/>
	<group name="subGrupo" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioListagemProdutosDTOParam.FormaApresentacao.GRUPO.equals($P{FORMA_APRESENTACAO})
?
    $F{subGrupo}
:
    null]]></groupExpression>
		<groupHeader>
			<band height="22">
				<printWhenExpression><![CDATA[br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioListagemProdutosDTOParam.FormaApresentacao.GRUPO.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
				<textField>
					<reportElement x="0" y="10" width="238" height="11" uuid="3da2a4e5-b3fd-466c-ac36-9837eecfdd59"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_produto")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="321" y="10" width="117" height="11" uuid="2a30410c-ddc6-445e-b872-2eb850189b56"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_grupo_produto")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="438" y="10" width="117" height="11" uuid="649d3548-dd59-490b-a33a-15198206e3ae"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_sub_grupo")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="21" width="555" height="1" uuid="22c9e476-8aa8-490a-bb35-19d9e26b538f"/>
				</line>
				<textField>
					<reportElement x="238" y="10" width="20" height="11" uuid="dd4ae1d5-bafa-4f02-8908-3570a84bba3a"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_unidade_abv")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="0" width="555" height="11" uuid="7bfbeb0d-f2e4-456d-adb1-d0df8a015aa1"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true" isUnderline="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_sub_grupo")+": "+$F{subGrupo}.getDescricaoReportComGrupo()]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="258" y="10" width="63" height="11" uuid="4a0131f4-b9ae-4f9e-acc9-e0dc7ff21051"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_tipo_produto_abv")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="5"/>
		</groupFooter>
	</group>
	<columnHeader>
		<band height="12" splitType="Stretch">
			<printWhenExpression><![CDATA[!br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioListagemProdutosDTOParam.FormaApresentacao.GRUPO.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
			<textField>
				<reportElement x="238" y="0" width="20" height="11" uuid="e0f41f97-3fd0-4c2b-af4d-51370bf4a253"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_unidade_abv")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="258" y="0" width="180" height="11" uuid="852f77a2-31dc-4bf2-a0af-df552ecba873"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_grupo_produto")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="438" y="0" width="117" height="11" uuid="128f4417-5622-4b6e-8c4c-318c200611d9"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_sub_grupo")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="0" width="238" height="11" uuid="a097e0df-abcf-49e5-92a8-8196da2a3dd7"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_produto")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="11" width="555" height="1" uuid="f5f5b554-dc40-484f-9a5d-26f70c7433a2"/>
			</line>
		</band>
	</columnHeader>
	<detail>
		<band height="11" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement x="0" y="0" width="238" height="11" uuid="f737c803-78e3-4134-81df-c37cb45da489"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoFormatado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="238" y="0" width="20" height="11" uuid="5073b168-9fc2-4134-bbe3-60492ccd7c6b"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unidade}.getUnidade()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="258" y="0" width="180" height="11" uuid="fef490aa-ab82-48f7-9342-4be624396059"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{subGrupo}.getRoGrupoProduto().getDescricaoFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="438" y="0" width="117" height="11" uuid="b8765a5d-6053-4741-a25e-0918fcc0e279"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{subGrupo}.getDescricaoFormatado()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
