package br.com.ksisolucoes.report.vigilancia.query;

import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ImpressaoDeferimentoPHSDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ImpressaoDeferimentoPHSDTO.InstalacaoHidrossanitariaEdificacaoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ImpressaoParecerProjetoHidrossanitarioDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.util.Valor;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.EloRequerimentoVigilanciaResponsavelTecnico;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaInscricaoImob;
import br.com.ksisolucoes.vo.vigilancia.TipoProjetoRequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecer;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecerFiscal;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.enums.RequerimentosProjetosEnums;
import ch.lambdaj.Lambda;
import com.ibm.icu.lang.UCharacter;
import com.ibm.icu.text.BreakIterator;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Query;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class QueryRelatorioImpressaoDeferimentoProjetoHidrossanitario extends CommandQuery<QueryRelatorioImpressaoDeferimentoProjetoHidrossanitario> implements ITransferDataReport<ImpressaoParecerProjetoHidrossanitarioDTOParam, ImpressaoDeferimentoPHSDTO> {

    private ImpressaoParecerProjetoHidrossanitarioDTOParam param;
    private List<ImpressaoDeferimentoPHSDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(ImpressaoDeferimentoPHSDTO.class.getName());
        ImpressaoDeferimentoPHSDTO proxy = on(ImpressaoDeferimentoPHSDTO.class);

        hql.addToSelect("rv.codigo", path(proxy.getRequerimentoVigilancia().getCodigo()));
        hql.addToSelect("rv.codigo", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getRequerimentoVigilancia().getCodigo()));

        hql.addToSelect("rv.protocolo", path(proxy.getRequerimentoVigilancia().getProtocolo()));

        hql.addToSelect("e.codigo", path(proxy.getRequerimentoVigilancia().getEstabelecimento().getCodigo()));
        hql.addToSelect("e.razaoSocial", path(proxy.getRequerimentoVigilancia().getEstabelecimento().getRazaoSocial()));
        hql.addToSelect("e.fantasia", path(proxy.getRequerimentoVigilancia().getEstabelecimento().getFantasia()));
        hql.addToSelect("e.dataCadastro", path(proxy.getRequerimentoVigilancia().getEstabelecimento().getDataCadastro()));
        hql.addToSelect("e.cnpjCpf", path(proxy.getRequerimentoVigilancia().getEstabelecimento().getCnpjCpf()));
        hql.addToSelect("e.numeroLogradouro", path(proxy.getRequerimentoVigilancia().getEstabelecimento().getNumeroLogradouro()));
        hql.addToSelect("e.complemento", path(proxy.getRequerimentoVigilancia().getEstabelecimento().getComplemento()));
        hql.addToSelect("e.pontoReferencia", path(proxy.getRequerimentoVigilancia().getEstabelecimento().getPontoReferencia()));
        hql.addToSelect("e.telefone", path(proxy.getRequerimentoVigilancia().getEstabelecimento().getTelefone()));
        hql.addToSelect("e.celular", path(proxy.getRequerimentoVigilancia().getEstabelecimento().getCelular()));
        hql.addToSelect("e.email", path(proxy.getRequerimentoVigilancia().getEstabelecimento().getEmail()));

        hql.addToSelect("vp.codigo", path(proxy.getRequerimentoVigilancia().getVigilanciaPessoa().getCodigo()));
        hql.addToSelect("vp.nome", path(proxy.getRequerimentoVigilancia().getVigilanciaPessoa().getNome()));
        hql.addToSelect("vp.nomeFantasia", path(proxy.getRequerimentoVigilancia().getVigilanciaPessoa().getNomeFantasia()));
        hql.addToSelect("vp.cpf", path(proxy.getRequerimentoVigilancia().getVigilanciaPessoa().getCpf()));
        hql.addToSelect("vp.celular", path(proxy.getRequerimentoVigilancia().getVigilanciaPessoa().getCelular()));
        hql.addToSelect("vp.numeroLogradouro", path(proxy.getRequerimentoVigilancia().getVigilanciaPessoa().getNumeroLogradouro()));
        hql.addToSelect("vp.representanteLegal",path(proxy.getRequerimentoVigilancia().getVigilanciaPessoa().getRepresentanteLegal()));
        hql.addToSelect("vp.atividade",path(proxy.getRequerimentoVigilancia().getVigilanciaPessoa().getAtividade()));
        hql.addToSelect("vp.celular",path(proxy.getRequerimentoVigilancia().getVigilanciaPessoa().getCelular()));

        hql.addToSelect("ve.cep", path(proxy.getRequerimentoVigilancia().getVigilanciaEndereco().getCep()));
        hql.addToSelect("ve.bairro", path(proxy.getRequerimentoVigilancia().getVigilanciaEndereco().getBairro()));
        hql.addToSelect("ve.logradouro", path(proxy.getRequerimentoVigilancia().getVigilanciaEndereco().getLogradouro()));
        hql.addToSelect("c.descricao", path(proxy.getRequerimentoVigilancia().getVigilanciaEndereco().getCidade().getDescricao()));
        hql.addToSelect("es.descricao", path(proxy.getRequerimentoVigilancia().getVigilanciaEndereco().getCidade().getEstado().getDescricao()));
        hql.addToSelect("es.sigla", path(proxy.getRequerimentoVigilancia().getVigilanciaEndereco().getCidade().getEstado().getSigla()));
        hql.addToSelect("tl.codigo", path(proxy.getRequerimentoVigilancia().getVigilanciaEndereco().getTipoLogradouro().getCodigo()));
        hql.addToSelect("tl.descricao", path(proxy.getRequerimentoVigilancia().getVigilanciaEndereco().getTipoLogradouro().getDescricao()));

        // DADOS DO PROJETO
        hql.addToSelect("projeto.codigo", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getCodigo()));
        hql.addToSelect("projeto.usoEdificacao", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getUsoEdificacao()));
        hql.addToSelect("projeto.areaTotalConstrucao", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getAreaTotalConstrucao()));
        hql.addToSelect("projeto.parcelamentoSoloNumeroLotes", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getParcelamentoSoloNumeroLotes()));
        hql.addToSelect("projeto.numeroProjetoUrbanistico", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getNumeroProjetoUrbanistico()));
        hql.addToSelect("projeto.numeroLicitacaoAmbiental", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getNumeroLicitacaoAmbiental()));
        hql.addToSelect("projeto.numeroProjetoEsgoto", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getNumeroProjetoEsgoto()));
        hql.addToSelect("projeto.numeroProjetoAgua", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getNumeroProjetoAgua()));
        hql.addToSelect("projeto.numeroAprovacao", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getNumeroAprovacao()));

        hql.addToSelect("enderecoProjeto.cep", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getVigilanciaEndereco().getCep()));
        hql.addToSelect("enderecoProjeto.bairro", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getVigilanciaEndereco().getBairro()));
        hql.addToSelect("enderecoProjeto.logradouro", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getVigilanciaEndereco().getLogradouro()));

        hql.addToSelect("projeto.obraNumeroEndereco", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getObraNumeroEndereco()));
        hql.addToSelect("projeto.obraQuadra", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getObraQuadra()));
        hql.addToSelect("projeto.obraNumeroLado", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getObraNumeroLado()));
        hql.addToSelect("projeto.obraLote", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getObraLote()));
        hql.addToSelect("projeto.obraComplemento", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getObraComplemento()));
        hql.addToSelect("projeto.obraNumeroLoteamento", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getObraNumeroLoteamento()));

        hql.addToSelect("tipoEnquadramentoProjeto.codigo", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getTipoEnquadramentoProjeto().getCodigo()));
        hql.addToSelect("tipoEnquadramentoProjeto.descricao", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getTipoEnquadramentoProjeto().getDescricao()));

        hql.addToSelect("parecer.codigo",path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getCodigo()));
        hql.addToSelect("parecer.dataParecer",path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getDataParecer()));
        hql.addToSelect("parecer.status",path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getStatus()));
        hql.addToSelect("parecer.observacao", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getObservacao()));
        hql.addToSelect("parecer.tipoAprovacao", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getTipoAprovacao()));

        // ABASTECIMENTO DE ÁGUA
        hql.addToSelect("parecer.abastecimentoAguaSomatorio", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getAbastecimentoAguaSomatorio()));
        hql.addToSelect("parecer.abastecimentoAguaSomatorioItens", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getAbastecimentoAguaSomatorioItens()));
        hql.addToSelect("parecer.abastecimentoAguaOutros", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getAbastecimentoAguaOutros()));
        hql.addToSelect("parecer.usoAbastecimentoAgua", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getUsoAbastecimentoAgua()));
        hql.addToSelect("parecer.descricaoAbastecimentoAgua", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getDescricaoAbastecimentoAgua()));
        hql.addToSelect("parecer.areaCaptacao", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getAreaCaptacao()));

        // TRATAMENTO DE EFLUENTES
        hql.addToSelect("parecer.tratamentoEfluentesSomatorio", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getTratamentoEfluentesSomatorio()));
        hql.addToSelect("parecer.tratamentoEfluentesSomatorioItens", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getTratamentoEfluentesSomatorioItens()));
        hql.addToSelect("parecer.descricaoEfluentes", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getDescricaoEfluentes()));
        hql.addToSelect("parecer.usoEfluentes", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getUsoEfluentes()));
        hql.addToSelect("parecer.tratamentoEfluentesOutros", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getTratamentoEfluentesOutros()));

        //LICENCIAMENTO AMBIENTAL
        hql.addToSelect("parecer.licenciamentoAmbientalSomatorio", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getLicenciamentoAmbientalSomatorio()));
        hql.addToSelect("parecer.numeroLaiLicenciamentoAmbiental", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getNumeroLaiLicenciamentoAmbiental()));
        hql.addToSelect("parecer.numeroLapLicenciamentoAmbiental", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getNumeroLapLicenciamentoAmbiental()));
        hql.addToSelect("parecer.numeroLaoLicenciamentoAmbiental", path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getNumeroLaoLicenciamentoAmbiental()));

        StringBuilder from = new StringBuilder("RequerimentoProjetoHidrossanitarioParecer parecer ");
        from.append(" LEFT JOIN parecer.requerimentoProjetoHidrossanitario projeto ");
        from.append(" LEFT JOIN projeto.vigilanciaEndereco enderecoProjeto ");
        from.append(" LEFT JOIN projeto.tipoEnquadramentoProjeto tipoEnquadramentoProjeto ");
        from.append(" LEFT JOIN projeto.requerimentoVigilancia rv ");
        from.append(" LEFT JOIN rv.estabelecimento e ");
        from.append(" LEFT JOIN rv.vigilanciaPessoa vp ");
        from.append(" LEFT JOIN e.estabelecimentoPrincipal ep ");
        from.append(" LEFT JOIN rv.vigilanciaEndereco ve ");
        from.append(" LEFT JOIN ve.cidade c ");
        from.append(" LEFT JOIN ve.tipoLogradouro tl ");
        from.append(" LEFT JOIN c.estado es ");
        hql.addToFrom(from.toString());

        hql.addToWhereWhithAnd("rv.codigo = ", this.param.getRequerimentoVigilancia().getCodigo());
        hql.addToWhereWhithAnd("parecer.status = ", RequerimentoVigilancia.Situacao.DEFERIDO.value());

        hql.addToOrder("parecer.codigo desc");
    }

    @Override
    public Collection getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List) result);
    }

    @Override
    protected Object executeQuery(Query query) {
        return super.executeQuery(query.setMaxResults(1));
    }

    @Override
    public void setDTOParam(ImpressaoParecerProjetoHidrossanitarioDTOParam param) {
        this.param = param;
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        super.customProcess(session);
        if(CollectionUtils.isNotNullEmpty(result)){
            for (ImpressaoDeferimentoPHSDTO dto : result) {

                if(dto.getRequerimentoVigilancia().getEstabelecimento() != null && dto.getRequerimentoVigilancia().getEstabelecimento().getCodigo() != null) {

                    dto.setNomePessoa(dto.getRequerimentoVigilancia().getEstabelecimento().getRazaoSocial());
                    dto.setNomeFantasia(dto.getRequerimentoVigilancia().getEstabelecimento().getFantasia());
                    dto.setCpfCnpj(dto.getRequerimentoVigilancia().getEstabelecimento().getCnpjCpfFormatado());

                    String complemento = "Nº " + dto.getRequerimentoVigilancia().getEstabelecimento().getNumeroLogradouro();
                    if(dto.getRequerimentoVigilancia().getEstabelecimento().getComplemento() != null ) {
                        complemento.concat(" / " + dto.getRequerimentoVigilancia().getEstabelecimento().getComplemento());
                    }
                    dto.setComplemento(complemento);
                    dto.setFone(dto.getRequerimentoVigilancia().getEstabelecimento().getTelefoneCelularFormatado());

                } else if(dto.getRequerimentoVigilancia().getVigilanciaPessoa() != null && dto.getRequerimentoVigilancia().getVigilanciaPessoa().getCodigo() != null) {
                    dto.setFone(dto.getRequerimentoVigilancia().getVigilanciaPessoa().getCelularFormatado());

                    dto.setNomePessoa(dto.getRequerimentoVigilancia().getVigilanciaPessoa().getNome());
                    dto.setNomeFantasia(dto.getRequerimentoVigilancia().getVigilanciaPessoa().getNomeFantasia());
                    dto.setCpfCnpj(dto.getRequerimentoVigilancia().getVigilanciaPessoa().getCpfFormatado());

                    if(dto.getRequerimentoVigilancia().getVigilanciaPessoa().getNumeroLogradouro() != null ) {
                        dto.setComplemento("Nº "+ dto.getRequerimentoVigilancia().getVigilanciaPessoa().getNumeroLogradouro());
                    }
                }

                dto.setEndereco(dto.getRequerimentoVigilancia().getVigilanciaEndereco().getRuaFormatada());
                if(dto.getRequerimentoVigilancia().getVigilanciaEndereco().getBairro() != null ) {
                    dto.setBairro(dto.getRequerimentoVigilancia().getVigilanciaEndereco().getBairro());
                }
                if(dto.getRequerimentoVigilancia().getVigilanciaEndereco().getCep() != null ) {
                    dto.setCep(dto.getRequerimentoVigilancia().getVigilanciaEndereco().getCepFormatado());
                }

                List<RequerimentoVigilanciaInscricaoImob> listInscricoesImobiliarias = this.getSession().createCriteria(RequerimentoVigilanciaInscricaoImob.class)
                        .add(Restrictions.eq(RequerimentoVigilanciaInscricaoImob.PROP_REQUERIMENTO_VIGILANCIA, dto.getRequerimentoVigilancia()))
                        .list();
                if(CollectionUtils.isNotNullEmpty(listInscricoesImobiliarias)) {
                    dto.setInscricoesImobiliarias(Lambda.join(Lambda.extract(listInscricoesImobiliarias, on(RequerimentoVigilanciaInscricaoImob.class).getNumeroInscricaoImobiliaria()), ", "));
                }

                List<EloRequerimentoVigilanciaResponsavelTecnico> listResponsavelTecnico = this.getSession().createCriteria(EloRequerimentoVigilanciaResponsavelTecnico.class)
                        .add(Restrictions.eq(EloRequerimentoVigilanciaResponsavelTecnico.PROP_REQUERIMENTO_VIGILANCIA, dto.getRequerimentoVigilancia()))
                        .list();
                if(CollectionUtils.isNotNullEmpty(listResponsavelTecnico)) {
                    dto.setResponsaveisTecnicos(Lambda.join(Lambda.extract(listResponsavelTecnico, on(EloRequerimentoVigilanciaResponsavelTecnico.class).getResponsavelTecnico().getDescricaoFormatada()), "\n"));
                }

                List<TipoProjetoRequerimentoVigilancia> listTipoProjeto = getSession().createCriteria(TipoProjetoRequerimentoVigilancia.class)
                        .add(Restrictions.eq(TipoProjetoRequerimentoVigilancia.PROP_REQUERIMENTO_VIGILANCIA, dto.getRequerimentoVigilancia()))
                        .list();

                if (CollectionUtils.isNotNullEmpty(listTipoProjeto)) {
                    List<String> tipoProjetoDescricaoList = new ArrayList<>();
                    StringBuilder builder;
                    for (TipoProjetoRequerimentoVigilancia tipoProjetoRequerimentoVigilancia : listTipoProjeto) {
                        builder = new StringBuilder();
                        builder.append(tipoProjetoRequerimentoVigilancia.getTipoProjetoVigilancia().getDescricao());
                        builder.append(" / ");
                        builder.append(tipoProjetoRequerimentoVigilancia.getDescricaoAreaFormatado());
                        tipoProjetoDescricaoList.add(builder.toString());
                    }
                    dto.setDescricaoTipoProjeto(Lambda.join(tipoProjetoDescricaoList, "<br/>"));
                }

                InstalacaoHidrossanitariaEdificacaoDTO descricaoAbastecimentoAgua = getDescricaoAbastecimentoAgua(dto.getRequerimentoProjetoHidrossanitarioParecer());
                if (descricaoAbastecimentoAgua != null) {
                    dto.getInstalacaoHidrossanitariaEdificacaoDTOList().add(descricaoAbastecimentoAgua);
                }

                InstalacaoHidrossanitariaEdificacaoDTO descricaoTratamentoEfluentes = getDescricaoTratamentoEfluentes(dto.getRequerimentoProjetoHidrossanitarioParecer());
                if (descricaoTratamentoEfluentes != null) {
                    dto.getInstalacaoHidrossanitariaEdificacaoDTOList().add(descricaoTratamentoEfluentes);
                }

                InstalacaoHidrossanitariaEdificacaoDTO descricaoLicenciamentoAmbiental = getDescricaoLicenciamentoAmbiental(dto.getRequerimentoProjetoHidrossanitarioParecer());
                if (descricaoLicenciamentoAmbiental != null) {
                    dto.getInstalacaoHidrossanitariaEdificacaoDTOList().add(descricaoLicenciamentoAmbiental);
                }

                dto.setEnderecoObra(dto.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getVigilanciaEndereco().getEnderecoFormatado());

                if(StringUtils.trimToNull(dto.getRequerimentoProjetoHidrossanitarioParecer().getObservacao()) != null) {
                    InstalacaoHidrossanitariaEdificacaoDTO instalacaoHidrossanitariaEdificacaoDTO = new InstalacaoHidrossanitariaEdificacaoDTO();
                    instalacaoHidrossanitariaEdificacaoDTO.setTipo("OBSERVAÇÃO");
                    instalacaoHidrossanitariaEdificacaoDTO.setDescricao(dto.getRequerimentoProjetoHidrossanitarioParecer().getObservacao());
                    dto.getInstalacaoHidrossanitariaEdificacaoDTOList().add(instalacaoHidrossanitariaEdificacaoDTO);
                }

                if(dto.getRequerimentoProjetoHidrossanitarioParecer().getRequerimentoProjetoHidrossanitario().getUsoEdificacao() != null) {
                    dto.setDescricaoObjetoAnalise(getDescricaoObjetoAnalise(dto.getRequerimentoProjetoHidrossanitarioParecer()));
                }
                dto.setDescricaoRodape(null);
                setFiscais(dto);
            }
            ImpressaoDeferimentoPHSDTO dtoAux = result.get(result.size() - 1);
            dtoAux.setDescricaoRodape(dtoAux.getRequerimentoProjetoHidrossanitarioParecer().getRodape());
        }
    }


    private String getDescricaoObjetoAnalise(RequerimentoProjetoHidrossanitarioParecer parecer) {
        StringBuilder builder = new StringBuilder();
        if (parecer.getRequerimentoProjetoHidrossanitario().getUsoEdificacao() != null) {
            builder.append("<b>");
            builder.append(parecer.getRequerimentoProjetoHidrossanitario().getDescricaoUsoEdificacao());
            if(parecer.getRequerimentoProjetoHidrossanitario().getAreaTotalConstrucao() != null) {
                builder.append(" - ");
                builder.append(parecer.getRequerimentoProjetoHidrossanitario().getAreaTotalConstrucao());
                builder.append(" m²");
            }
            builder.append("</b>");
        }
        if (RequerimentosProjetosEnums.UsoEdificacao.isLoteamentoOrCondominio(parecer.getRequerimentoProjetoHidrossanitario().getUsoEdificacao())) {
            builder.append("<br/>\n");
            builder.append("Nº de Lotes: ");
            builder.append(parecer.getRequerimentoProjetoHidrossanitario().getParcelamentoSoloNumeroLotes());
            if (parecer.getRequerimentoProjetoHidrossanitario().getNumeroProjetoUrbanistico() != null) {
                builder.append("<br/>\n");
                builder.append("Nº Projeto Urbanístico - SMDU: ");
                builder.append(parecer.getRequerimentoProjetoHidrossanitario().getNumeroProjetoUrbanistico());
            }

            if (parecer.getRequerimentoProjetoHidrossanitario().getNumeroLicitacaoAmbiental() != null) {
                builder.append("<br/>\n");
                builder.append("Nº Licença Ambiental (LAI): ");
                builder.append(parecer.getRequerimentoProjetoHidrossanitario().getNumeroLicitacaoAmbiental());
            }

            if (parecer.getRequerimentoProjetoHidrossanitario().getNumeroProjetoEsgoto() != null) {
                builder.append("<br/>\n");
                builder.append("Nº Projeto Esgoto - Concessionária: ");
                builder.append(parecer.getRequerimentoProjetoHidrossanitario().getNumeroProjetoEsgoto());
            }

            if (parecer.getRequerimentoProjetoHidrossanitario().getNumeroProjetoAgua() != null) {
                builder.append("<br/>\n");
                builder.append("Nº Projeto Água - Concessionária: ");
                builder.append(parecer.getRequerimentoProjetoHidrossanitario().getNumeroProjetoAgua());
            }
        }
        if (RequerimentosProjetosEnums.UsoEdificacao.isMista(parecer.getRequerimentoProjetoHidrossanitario().getUsoEdificacao())) {
            if (parecer.getRequerimentoProjetoHidrossanitario().getAreaComercial() != null) {
                builder.append("<br/>\n");
                builder.append("Área Comercial: ");
                builder.append(parecer.getRequerimentoProjetoHidrossanitario().getAreaComercial());
                builder.append(" m²");
            }

            if (parecer.getRequerimentoProjetoHidrossanitario().getAreaResidencial() != null) {
                builder.append("<br/>\n");
                builder.append("Área Residencial: ");
                builder.append(parecer.getRequerimentoProjetoHidrossanitario().getAreaResidencial());
                builder.append(" m²");
            }
        }

        if (RequerimentosProjetosEnums.UsoEdificacao.OUTROS.value().equals(parecer.getRequerimentoProjetoHidrossanitario().getUsoEdificacao())) {
            if (parecer.getRequerimentoProjetoHidrossanitario().getObservacaoUsoEdificacao() != null) {
                builder.append("<br/>\n");
                builder.append(parecer.getRequerimentoProjetoHidrossanitario().getObservacaoUsoEdificacao());
            }
        }
        return builder.toString();
    }

    private InstalacaoHidrossanitariaEdificacaoDTO getDescricaoLicenciamentoAmbiental(RequerimentoProjetoHidrossanitarioParecer parecer){
        if (parecer.getLicenciamentoAmbientalSomatorio() != null && parecer.getLicenciamentoAmbientalSomatorio() > 0) {
            List<Long> lstLong = Valor.resolveSomatorio(parecer.getLicenciamentoAmbientalSomatorio());
            StringBuilder builder = new StringBuilder();

            for (Long opcao : lstLong) {
                builder.append("<b> ■ ");
                builder.append(RequerimentosProjetosEnums.LicenciamentoAmbiental.valueOf(opcao).descricao());
                builder.append("</b>");
                if (RequerimentosProjetosEnums.LicenciamentoAmbiental.NUMERO_LAI.sum().equals(opcao)) {
                    builder.append(": ");
                    builder.append(Coalesce.asString(parecer.getNumeroLaiLicenciamentoAmbiental()));
                    builder.append("<br/>\n");
                }
                if (RequerimentosProjetosEnums.LicenciamentoAmbiental.NUMERO_LAO.sum().equals(opcao)) {
                    builder.append(": ");
                    builder.append(Coalesce.asString(parecer.getNumeroLaoLicenciamentoAmbiental()));
                    builder.append("<br/>\n");
                }
                if (RequerimentosProjetosEnums.LicenciamentoAmbiental.NUMERO_LAP.sum().equals(opcao)) {
                    builder.append(": ");
                    builder.append(Coalesce.asString(parecer.getNumeroLapLicenciamentoAmbiental()));
                    builder.append("<br/>\n");
                }
                builder.append("<br/>\n");
            }

            InstalacaoHidrossanitariaEdificacaoDTO instalacaoHidrossanitariaEdificacaoDTO = new InstalacaoHidrossanitariaEdificacaoDTO();
            instalacaoHidrossanitariaEdificacaoDTO.setTipo("LICENCIAMENTO AMBIENTAL");
            instalacaoHidrossanitariaEdificacaoDTO.setDescricao(builder.toString());
            return instalacaoHidrossanitariaEdificacaoDTO;
        }
        return null;
    }

    private InstalacaoHidrossanitariaEdificacaoDTO getDescricaoTratamentoEfluentes(RequerimentoProjetoHidrossanitarioParecer parecer){
        if (parecer.getTratamentoEfluentesSomatorio() != null && parecer.getTratamentoEfluentesSomatorio() > 0) {
            List<Long> lstLong = Valor.resolveSomatorio(parecer.getTratamentoEfluentesSomatorio());
            StringBuilder builder = new StringBuilder();

            for (Long opcao : lstLong) {
                builder.append("<b> ■ ");
                builder.append(RequerimentosProjetosEnums.TratamentoEfluentesPadrao.valueOf(opcao).descricao());
                builder.append("</b>");
                if (RequerimentosProjetosEnums.TratamentoEfluentesPadrao.SISTEMA_LOCAL_TRATAMENTO.sum().equals(opcao)) {
                    if (parecer.getTratamentoEfluentesSomatorioItens() != null && parecer.getTratamentoEfluentesSomatorioItens() > 0) {
                        List<Long> lstLongItens = Valor.resolveSomatorio(parecer.getTratamentoEfluentesSomatorioItens());
                        builder.append("<br/>\n");
                        for (Long item : lstLongItens) {
                            builder.append(" • ");
                            builder.append(RequerimentosProjetosEnums.TratamentoAguaItens.valueOf(item).descricao());
                            if (RequerimentosProjetosEnums.TratamentoAguaItens.OUTROS.sum().equals(item)) {
                                builder.append(": ");
                                builder.append(Coalesce.asString(parecer.getTratamentoEfluentesOutros()));
                            }
                            builder.append("<br/>\n");
                        }
                        builder.append("<br/>\n");
                        if(StringUtils.trimToNull(parecer.getDescricaoEfluentes()) != null) {
                            builder.append("Descrição: ");
                            builder.append(parecer.getDescricaoEfluentes());
                            builder.append("<br/>\n");
                        }
                    }
                }
                if (RequerimentosProjetosEnums.TratamentoEfluentesPadrao.REUSO_EFLUENTE_TRATADO.sum().equals(opcao) && StringUtils.trimToNull(parecer.getUsoEfluentes()) != null) {
                    builder.append("<br/>\n");
                    builder.append("Uso: ");
                    builder.append(parecer.getUsoEfluentes());
                }
                builder.append("<br/>\n");
            }

            InstalacaoHidrossanitariaEdificacaoDTO instalacaoHidrossanitariaEdificacaoDTO = new InstalacaoHidrossanitariaEdificacaoDTO();
            instalacaoHidrossanitariaEdificacaoDTO.setTipo("TRATAMENTO DE EFLUENTES");
            instalacaoHidrossanitariaEdificacaoDTO.setDescricao(builder.toString());
            return instalacaoHidrossanitariaEdificacaoDTO;
        }
        return null;
    }


    private InstalacaoHidrossanitariaEdificacaoDTO getDescricaoAbastecimentoAgua(RequerimentoProjetoHidrossanitarioParecer parecer){
        if (parecer.getAbastecimentoAguaSomatorio() != null && parecer.getAbastecimentoAguaSomatorio() > 0) {
            List<Long> lstLong = Valor.resolveSomatorio(parecer.getAbastecimentoAguaSomatorio());
            StringBuilder builder = new StringBuilder();

            for (Long opcao : lstLong) {
                builder.append("<b> ■ ");
                builder.append(RequerimentoProjetoHidrossanitarioParecer.AbastecimentoAgua.valueOf(opcao).descricao());
                builder.append("</b>");
                if (RequerimentosProjetosEnums.AbastecimentoAgua.APROVEITAMENTO_AGUA_PLUVIAL.sum().equals(opcao)) {
                    if (parecer.getAbastecimentoAguaSomatorioItens() != null && parecer.getAbastecimentoAguaSomatorioItens() > 0) {
                        List<Long> lstLongItens = Valor.resolveSomatorio(parecer.getAbastecimentoAguaSomatorioItens());
                        builder.append("<br/>\n");
                        for (Long item : lstLongItens) {
                            builder.append(" • ");
                            builder.append(RequerimentosProjetosEnums.AbastecimentoAguaItens.valueOf(item).descricao());
                            if (RequerimentosProjetosEnums.AbastecimentoAguaItens.OUTROS.sum().equals(item)) {
                                builder.append(": ");
                                builder.append(Coalesce.asString(parecer.getAbastecimentoAguaOutros()));
                            }
                            builder.append("<br/>\n");
                        }
                        builder.append("<br/>\n");
                        if(StringUtils.trimToNull(parecer.getUsoAbastecimentoAgua()) != null) {
                            builder.append("Uso: ");
                            builder.append(parecer.getUsoAbastecimentoAgua());
                            builder.append("<br/>\n");
                        }
                        if(StringUtils.trimToNull(parecer.getAreaCaptacao()) != null) {
                            builder.append("<br/>\n");
                            builder.append("Área de Captação: ");
                            builder.append(parecer.getAreaCaptacao());
                        }
                    }
                }
                if(RequerimentoProjetoHidrossanitarioParecer.AbastecimentoAgua.SISTEMA_LOCAL_TRATAMENTO.sum().equals(opcao) && StringUtils.trimToNull(parecer.getDescricaoAbastecimentoAgua()) != null){
                    builder.append("<br/>\n");
                    builder.append("Descrição: ");
                    builder.append(parecer.getDescricaoAbastecimentoAgua());
                }
                builder.append("<br/>\n");
            }

            InstalacaoHidrossanitariaEdificacaoDTO instalacaoHidrossanitariaEdificacaoDTO = new InstalacaoHidrossanitariaEdificacaoDTO();
            instalacaoHidrossanitariaEdificacaoDTO.setTipo("ABASTECIMENTO DE ÁGUA");
            instalacaoHidrossanitariaEdificacaoDTO.setDescricao(builder.toString());
            return instalacaoHidrossanitariaEdificacaoDTO;
        }

        return null;
    }

    private void setFiscais(ImpressaoDeferimentoPHSDTO dto) {
        RequerimentoProjetoHidrossanitarioParecerFiscal proxy = Lambda.on(RequerimentoProjetoHidrossanitarioParecerFiscal.class);

        List<RequerimentoProjetoHidrossanitarioParecerFiscal> list = LoadManager.getInstance(RequerimentoProjetoHidrossanitarioParecerFiscal.class)
                .addProperty(path(proxy.getRequerimentoVigilanciaFiscal().getProfissional().getCodigo()))
                .addProperty(path(proxy.getRequerimentoVigilanciaFiscal().getProfissional().getReferencia()))
                .addProperty(path(proxy.getRequerimentoVigilanciaFiscal().getProfissional().getNome()))
                .addProperty(path(proxy.getRequerimentoVigilanciaFiscal().getProfissional().getNumeroRegistro()))
                .addProperty(path(proxy.getRequerimentoVigilanciaFiscal().getProfissional().getUnidadeFederacaoConselhoRegistro()))
                .addProperty(path(proxy.getRequerimentoVigilanciaFiscal().getProfissional().getConselhoClasse().getCodigo()))
                .addProperty(path(proxy.getRequerimentoVigilanciaFiscal().getProfissional().getConselhoClasse().getSigla()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getRequerimentoProjetoHidrossanitarioParecer().getCodigo()), dto.getRequerimentoProjetoHidrossanitarioParecer().getCodigo()))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(list)) {
            List<Profissional> extract = Lambda.extract(list, proxy.getRequerimentoVigilanciaFiscal().getProfissional());
            List<Profissional> fiscais = new ArrayList<>();
            for (Profissional fiscal : extract) {
                fiscal.setNome(UCharacter.toTitleCase(fiscal.getNome().toLowerCase(), BreakIterator.getTitleInstance()));
                fiscais.add(fiscal);
            }
            dto.setFiscais(fiscais);
        }

    }

}