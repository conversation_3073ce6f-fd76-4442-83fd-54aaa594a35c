package br.com.ksisolucoes.report.vigilancia.query;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioRequerimentoVigilanciaComprovanteDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoDeclaracaoVisaProdutosDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.server.HibernateSessionFactory;
import br.com.ksisolucoes.system.consulta.Projections;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.hibernate.Session;
import org.hibernate.criterion.Restrictions;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryRelatorioRequerimentoDeclaracaoVisaProdutos extends CommandQuery<QueryRelatorioRequerimentoDeclaracaoVisaProdutos> implements ITransferDataReport<RelatorioRequerimentoVigilanciaComprovanteDTOParam, RequerimentoDeclaracaoVisaProdutosDTO> {

    private RelatorioRequerimentoVigilanciaComprovanteDTOParam param;
    private List<RequerimentoDeclaracaoVisaProdutosDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.setTypeSelect(RequerimentoDeclaracaoVisaProdutosDTO.class.getName());
        hql.addToSelect("est.codigo", "requerimentoDeclaracaoVisaProdutos.requerimentoVigilancia.estabelecimento.codigo");
        hql.addToSelect("est.razaoSocial", "requerimentoDeclaracaoVisaProdutos.requerimentoVigilancia.estabelecimento.razaoSocial");
        hql.addToSelect("est.cnpjCpf", "requerimentoDeclaracaoVisaProdutos.requerimentoVigilancia.estabelecimento.cnpjCpf");
        hql.addToSelect("est.fantasia", "requerimentoDeclaracaoVisaProdutos.requerimentoVigilancia.estabelecimento.fantasia");
        hql.addToSelect("ts.codigo", "requerimentoDeclaracaoVisaProdutos.requerimentoVigilancia.tipoSolicitacao.codigo");
        hql.addToSelect("ts.descricao", "requerimentoDeclaracaoVisaProdutos.requerimentoVigilancia.tipoSolicitacao.descricao");
        hql.addToSelect("rv.codigo", "requerimentoDeclaracaoVisaProdutos.requerimentoVigilancia.codigo");
        hql.addToSelect("rv.protocolo", "requerimentoDeclaracaoVisaProdutos.requerimentoVigilancia.protocolo");
        hql.addToSelect("rv.situacao", "requerimentoDeclaracaoVisaProdutos.requerimentoVigilancia.situacao");
        hql.addToSelect("rv.nomeSolicitante", "requerimentoDeclaracaoVisaProdutos.requerimentoVigilancia.nomeSolicitante");
        hql.addToSelect("rv.rgCpfSolicitante", "requerimentoDeclaracaoVisaProdutos.requerimentoVigilancia.rgCpfSolicitante");
        hql.addToSelect("rv.cargoSolicitante", "requerimentoDeclaracaoVisaProdutos.requerimentoVigilancia.cargoSolicitante");
        hql.addToSelect("rv.telefoneSolicitante", "requerimentoDeclaracaoVisaProdutos.requerimentoVigilancia.telefoneSolicitante");
        hql.addToSelect("rv.dataRequerimento", "requerimentoDeclaracaoVisaProdutos.requerimentoVigilancia.dataRequerimento");
        hql.addToSelect("rv.tipoDocumento", "requerimentoDeclaracaoVisaProdutos.requerimentoVigilancia.tipoDocumento");

        hql.addToSelect("vp.nomeProduto", "requerimentoDeclaracaoVisaProdutos.nomeProduto");
        hql.addToSelect("vp.quantidadeProduto", "requerimentoDeclaracaoVisaProdutos.quantidadeProduto");
        hql.addToSelect("vp.loteProduto", "requerimentoDeclaracaoVisaProdutos.loteProduto");
        hql.addToSelect("vp.dataFabricacaoProduto", "requerimentoDeclaracaoVisaProdutos.dataFabricacaoProduto");
        hql.addToSelect("vp.dataValidadeProduto", "requerimentoDeclaracaoVisaProdutos.dataValidadeProduto");
        hql.addToSelect("vp.numeroNotaFiscalProduto", "requerimentoDeclaracaoVisaProdutos.numeroNotaFiscalProduto");
        hql.addToSelect("vp.dataCompraProduto", "requerimentoDeclaracaoVisaProdutos.dataCompraProduto");
        hql.addToSelect("vp.localCompraProduto", "requerimentoDeclaracaoVisaProdutos.localCompraProduto");
        hql.addToSelect("vp.motivo", "requerimentoDeclaracaoVisaProdutos.motivo");
        hql.addToSelect("vp.observacao", "requerimentoDeclaracaoVisaProdutos.observacao");

        StringBuilder from = new StringBuilder(" RequerimentoDeclaracaoVisaProdutos vp ");
        from.append("left join vp.requerimentoVigilancia rv ");
        from.append("left join rv.tipoSolicitacao ts ");
        from.append("left join rv.estabelecimento est ");
        hql.addToFrom(from.toString());

        hql.addToWhereWhithAnd("rv in ", param.getRequerimentoVigilancia());
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if(CollectionUtils.isNotNullEmpty(this.result)) {
            Long codigoProfissional = null;
            Usuario usuarioImpressao = getSessao().getUsuario();
            if(usuarioImpressao != null && usuarioImpressao.getCodigo() != null) {
                codigoProfissional = (Long) HibernateSessionFactory.getSession().createCriteria(Usuario.class)
                        .add(Restrictions.idEq(usuarioImpressao.getCodigo()))
                        .setProjection(Projections.property(VOUtils.montarPath(Usuario.PROP_PROFISSIONAL, Profissional.PROP_CODIGO)))
                        .uniqueResult();
            }
            if (codigoProfissional != null) {
                this.result.get(0).setProfissionalImpressao(VigilanciaHelper.reloadProfissionalImpressao(codigoProfissional));
            }
        }
    }

    @Override
    public List<RequerimentoDeclaracaoVisaProdutosDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(RelatorioRequerimentoVigilanciaComprovanteDTOParam param) {
        this.param = param;
    }

}
