package br.com.ksisolucoes.report.vigilancia.query;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.CadastroEventosVigilanciaDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.server.HibernateSessionFactory;
import br.com.ksisolucoes.system.consulta.Projections;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.OrgaoEmissor;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.grupoestabelecimento.GrupoEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import ch.lambdaj.Lambda;
import com.ibm.icu.lang.UCharacter;
import com.ibm.icu.text.BreakIterator;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.criterion.Restrictions;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public class QueryRelatorioRequerimentoAlvaraCadastroEvento extends CommandQuery<QueryRelatorioRequerimentoAlvaraCadastroEvento> implements ITransferDataReport<Long, CadastroEventosVigilanciaDTO> {

    private Long codigoRequerimento;
    private List<CadastroEventosVigilanciaDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(CadastroEventosVigilanciaDTO.class.getName());
        hql.addToSelect("ev.codigo", "eventosVigilancia.codigo");
        hql.addToSelect("ev.dataCadastro", "eventosVigilancia.dataCadastro");
        hql.addToSelect("ev.numeroAlvara", "eventosVigilancia.numeroAlvara");
        hql.addToSelect("ev.descricaoObservacaoDestaque", "eventosVigilancia.descricaoObservacaoDestaque");


        hql.addToSelect("ev.dataInicial", "eventosVigilancia.dataInicial");
        hql.addToSelect("ev.dataFinal", "eventosVigilancia.dataFinal");
        hql.addToSelect("ev.descricaoTipo", "eventosVigilancia.descricaoTipo");
        hql.addToSelect("ev.nome", "eventosVigilancia.nome");

        hql.addToSelect("rv.codigo", "eventosVigilancia.requerimentoVigilancia.codigo");
        hql.addToSelect("rv.protocolo", "eventosVigilancia.requerimentoVigilancia.protocolo");
        hql.addToSelect("rv.nomeSolicitante", "eventosVigilancia.requerimentoVigilancia.nomeSolicitante");
        hql.addToSelect("rv.cargoSolicitante", "eventosVigilancia.requerimentoVigilancia.cargoSolicitante");

        hql.addToSelect("e.codigo", "eventosVigilancia.estabelecimento.codigo");
        hql.addToSelect("e.razaoSocial", "eventosVigilancia.estabelecimento.razaoSocial");
        hql.addToSelect("e.fantasia", "eventosVigilancia.estabelecimento.fantasia");
        hql.addToSelect("e.dataCadastro", "eventosVigilancia.estabelecimento.dataCadastro");
        hql.addToSelect("e.tipoPessoa", "eventosVigilancia.estabelecimento.tipoPessoa");
        hql.addToSelect("e.situacao", "eventosVigilancia.estabelecimento.situacao");
        hql.addToSelect("e.cnpjCpf", "eventosVigilancia.estabelecimento.cnpjCpf");
        hql.addToSelect("e.matriz", "eventosVigilancia.estabelecimento.matriz");
        hql.addToSelect("e.numeroLogradouro", "eventosVigilancia.estabelecimento.numeroLogradouro");
        hql.addToSelect("e.complemento", "eventosVigilancia.estabelecimento.complemento");
        hql.addToSelect("e.pontoReferencia", "eventosVigilancia.estabelecimento.pontoReferencia");
        hql.addToSelect("e.telefone", "eventosVigilancia.estabelecimento.telefone");
        hql.addToSelect("e.celular", "eventosVigilancia.estabelecimento.celular");
        hql.addToSelect("e.fax", "eventosVigilancia.estabelecimento.fax");
        hql.addToSelect("e.email", "eventosVigilancia.estabelecimento.email");
        hql.addToSelect("e.site", "eventosVigilancia.estabelecimento.site");
        hql.addToSelect("e.inscricaoEstadual", "eventosVigilancia.estabelecimento.inscricaoEstadual");
        hql.addToSelect("e.inscricaoMunicipal", "eventosVigilancia.estabelecimento.inscricaoMunicipal");
        hql.addToSelect("e.dataInicioFuncionamento", "eventosVigilancia.estabelecimento.dataInicioFuncionamento");
        hql.addToSelect("e.certificacaoAnvisa", "eventosVigilancia.estabelecimento.certificacaoAnvisa");
        hql.addToSelect("e.autorizacaoAnvisa", "eventosVigilancia.estabelecimento.autorizacaoAnvisa");
        hql.addToSelect("e.outraCertificacao", "eventosVigilancia.estabelecimento.outraCertificacao");
        hql.addToSelect("e.nomeCertificacao", "eventosVigilancia.estabelecimento.nomeCertificacao");
        hql.addToSelect("e.numeroCertificacao", "eventosVigilancia.estabelecimento.numeroCertificacao");
        hql.addToSelect("e.importador", "eventosVigilancia.estabelecimento.importador");
        hql.addToSelect("e.observacao", "eventosVigilancia.estabelecimento.observacao");
        hql.addToSelect("e.representanteNome", "eventosVigilancia.estabelecimento.representanteNome");
        hql.addToSelect("e.representanteCpf", "eventosVigilancia.estabelecimento.representanteCpf");
        hql.addToSelect("e.representanteRg", "eventosVigilancia.estabelecimento.representanteRg");
        hql.addToSelect("e.representanteRgOrgao", "eventosVigilancia.estabelecimento.representanteRgOrgao");
        hql.addToSelect("e.representanteRgData", "eventosVigilancia.estabelecimento.representanteRgData");
        hql.addToSelect("e.representanteTelefone", "eventosVigilancia.estabelecimento.representanteTelefone");
        hql.addToSelect("e.representanteCelular", "eventosVigilancia.estabelecimento.representanteCelular");
        hql.addToSelect("e.dataUsuario", "eventosVigilancia.estabelecimento.dataUsuario");
        hql.addToSelect("e.protocolo", "eventosVigilancia.estabelecimento.protocolo");
        hql.addToSelect("e.alvara", "eventosVigilancia.estabelecimento.alvara");
        hql.addToSelect("e.validadeAlvara", "eventosVigilancia.estabelecimento.validadeAlvara");
        hql.addToSelect("e.dataBaixaFuncionamento", "eventosVigilancia.estabelecimento.dataBaixaFuncionamento");
        hql.addToSelect("e.numeroFuncionarios", "eventosVigilancia.estabelecimento.numeroFuncionarios");
        hql.addToSelect("e.horaInicioPrimeiroTurno", "eventosVigilancia.estabelecimento.horaInicioPrimeiroTurno");
        hql.addToSelect("e.horaFimPrimeiroTurno", "eventosVigilancia.estabelecimento.horaFimPrimeiroTurno");
        hql.addToSelect("e.horaInicioSegundoTurno", "eventosVigilancia.estabelecimento.horaInicioSegundoTurno");
        hql.addToSelect("e.horaFimSegundoTurno", "eventosVigilancia.estabelecimento.horaFimSegundoTurno");
        hql.addToSelect("e.flagHoraFuncionamento", "eventosVigilancia.estabelecimento.flagHoraFuncionamento");
        hql.addToSelect("ep.codigo", "eventosVigilancia.estabelecimento.estabelecimentoPrincipal.codigo");
        hql.addToSelect("ep.razaoSocial", "eventosVigilancia.estabelecimento.estabelecimentoPrincipal.razaoSocial");
        hql.addToSelect("ep.cnpjCpf", "eventosVigilancia.estabelecimento.estabelecimentoPrincipal.cnpjCpf");
        hql.addToSelect("ep.fantasia", "eventosVigilancia.estabelecimento.estabelecimentoPrincipal.fantasia");
        
        hql.addToSelect("ve.cep", "eventosVigilancia.estabelecimento.vigilanciaEndereco.cep");
        hql.addToSelect("ve.bairro", "eventosVigilancia.estabelecimento.vigilanciaEndereco.bairro");
        hql.addToSelect("ve.logradouro", "eventosVigilancia.estabelecimento.vigilanciaEndereco.logradouro");
        hql.addToSelect("c.descricao", "eventosVigilancia.estabelecimento.vigilanciaEndereco.cidade.descricao");
        hql.addToSelect("es.descricao", "eventosVigilancia.estabelecimento.vigilanciaEndereco.cidade.estado.descricao");
        hql.addToSelect("es.sigla", "eventosVigilancia.estabelecimento.vigilanciaEndereco.cidade.estado.sigla");
        
        hql.addToSelect("(select ae.descricao "
                + "from EstabelecimentoAtividade ea "
                + "left join ea.atividadeEstabelecimento ae "
                + "where ea.estabelecimento = e AND ea.flagPrincipal = :sim)", "descricaoAtividadePrincipal");
        
        hql.addToSelect("vigilanciaEndereco.codigo", "eventosVigilancia.vigilanciaEndereco.codigo");
        hql.addToSelect("vigilanciaEndereco.cep", "eventosVigilancia.vigilanciaEndereco.cep");
        hql.addToSelect("vigilanciaEndereco.bairro", "eventosVigilancia.vigilanciaEndereco.bairro");
        hql.addToSelect("vigilanciaEndereco.logradouro", "eventosVigilancia.vigilanciaEndereco.logradouro");
        
        hql.addToSelect("cidade.codigo", "eventosVigilancia.vigilanciaEndereco.cidade.codigo");
        hql.addToSelect("cidade.descricao", "eventosVigilancia.vigilanciaEndereco.cidade.descricao");
        
        hql.addToSelect("estado.codigo", "eventosVigilancia.vigilanciaEndereco.cidade.estado.codigo");
        hql.addToSelect("estado.descricao", "eventosVigilancia.vigilanciaEndereco.cidade.estado.descricao");
        hql.addToSelect("estado.sigla", "eventosVigilancia.vigilanciaEndereco.cidade.estado.sigla");
        
        hql.addToSelect("tipoLogradouro.codigo", "eventosVigilancia.vigilanciaEndereco.tipoLogradouro.codigo");
        hql.addToSelect("tipoLogradouro.descricao", "eventosVigilancia.vigilanciaEndereco.tipoLogradouro.descricao");
        hql.addToSelect("tipoLogradouro.sigla", "eventosVigilancia.vigilanciaEndereco.tipoLogradouro.sigla");

        hql.addToSelect("uf.codigo", "eventosVigilancia.requerimentoVigilancia.usuarioFinalizacao.codigo");
        hql.addToSelect("uf.nome", "eventosVigilancia.requerimentoVigilancia.usuarioFinalizacao.nome");

        StringBuilder from = new StringBuilder(" EventosVigilancia ev ");
        from.append(" LEFT JOIN ev.requerimentoVigilancia rv ");
        from.append(" LEFT JOIN rv.usuarioFinalizacao uf ");
        from.append(" LEFT JOIN rv.estabelecimento e ");
        from.append(" left join e.estabelecimentoPrincipal ep ");
        from.append(" LEFT JOIN e.vigilanciaEndereco ve ");
        from.append(" LEFT JOIN ve.cidade c ");
        from.append(" LEFT JOIN c.estado es ");
        from.append(" LEFT JOIN ev.vigilanciaEndereco vigilanciaEndereco ");
        from.append(" LEFT JOIN vigilanciaEndereco.tipoLogradouro tipoLogradouro ");
        from.append(" LEFT JOIN vigilanciaEndereco.cidade cidade ");
        from.append(" LEFT JOIN cidade.estado estado ");
        hql.addToFrom(from.toString());
        hql.addToWhereWhithAnd("rv.codigo = ", this.codigoRequerimento);
    }

    @Override
    public Collection getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List) result);
    }

    @Override
    public void setDTOParam(Long codigo) {
        this.codigoRequerimento = codigo;
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        super.customProcess(session);
        if(CollectionUtils.isNotNullEmpty(result) && result.get(0).getEventosVigilancia() != null && result.get(0).getEventosVigilancia().getCodigo() != null){
            Long codigoEstab = result.get(0).getEventosVigilancia().getEstabelecimento().getCodigo();

            List<ResponsavelTecnico> responsavelTecnicoList = Lambda.extract(getEstabelecimentoResponsavelTecnico(codigoEstab), Lambda.on(EstabelecimentoResponsavelTecnico.class).getResponsavelTecnico());
            result.get(0).setResponsavelTecnicoList(responsavelTecnicoList);

            List<TabelaCnae> tabelaCnaeList = Lambda.extract(getEstabelecimentoCnae(codigoEstab), Lambda.on(EstabelecimentoCnae.class).getCnae());
            result.get(0).setTabelaCnaeList(tabelaCnaeList);

            String observacaoDestaqueAlvaraSanitario = VigilanciaHelper.getConfiguracaoVigilancia().getObservacaoDestaqueCadastroEvento();
            if (observacaoDestaqueAlvaraSanitario != null) {
                result.get(0).setObservacao(observacaoDestaqueAlvaraSanitario);
            }

            EstabelecimentoAtividade estabelecimentoAtividade = LoadManager.getInstance(EstabelecimentoAtividade.class)
                    .addProperties(new HQLProperties(GrupoEstabelecimento.class, VOUtils.montarPath(EstabelecimentoAtividade.PROP_ATIVIDADE_ESTABELECIMENTO, AtividadeEstabelecimento.PROP_GRUPO_ESTABELECIMENTO)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstabelecimentoAtividade.PROP_ESTABELECIMENTO, Estabelecimento.PROP_CODIGO), codigoEstab))
                    .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_FLAG_PRINCIPAL, QueryCustom.QueryCustomParameter.IGUAL, RepositoryComponentDefault.SIM_LONG))
                    .start().getVO();

            if (estabelecimentoAtividade != null && estabelecimentoAtividade.getAtividadeEstabelecimento() != null) {
                result.get(0).setGrupoEstabelecimento(estabelecimentoAtividade.getAtividadeEstabelecimento().getGrupoEstabelecimento());
            }

            if(result.get(0).getEventosVigilancia().getRequerimentoVigilancia().getUsuarioFinalizacao() != null && result.get(0).getEventosVigilancia().getRequerimentoVigilancia().getUsuarioFinalizacao().getCodigo() != null) {
                Long codigoProfissional = null;
                try {
                    codigoProfissional = (Long) HibernateSessionFactory.getSession().createCriteria(Usuario.class)
                            .add(Restrictions.idEq(result.get(0).getEventosVigilancia().getRequerimentoVigilancia().getUsuarioFinalizacao().getCodigo()))
                            .setProjection(Projections.property(VOUtils.montarPath(Usuario.PROP_PROFISSIONAL, Profissional.PROP_CODIGO)))
                            .uniqueResult();
                } catch (DAOException e) {
                     br.com.ksisolucoes.util.log.Loggable.log.error(e);
                }
                if (codigoProfissional != null) {
                    Profissional profissional = VigilanciaHelper.reloadProfissionalImpressao(codigoProfissional);
                    profissional.setNome((UCharacter.toTitleCase(profissional.getNome().toLowerCase(), BreakIterator.getTitleInstance())));
                    this.result.get(0).setProfissionalFinalizacao(profissional);
                }
            }
        }
    }

    private List<EstabelecimentoResponsavelTecnico> getEstabelecimentoResponsavelTecnico(Long codigoEstab) {
        return LoadManager.getInstance(EstabelecimentoResponsavelTecnico.class)
                .addProperties(new HQLProperties(EstabelecimentoResponsavelTecnico.class).getProperties())
                .addProperties(new HQLProperties(Estabelecimento.class, EstabelecimentoResponsavelTecnico.PROP_ESTABELECIMENTO).getProperties())
                .addProperties(new HQLProperties(ResponsavelTecnico.class, EstabelecimentoResponsavelTecnico.PROP_RESPONSAVEL_TECNICO).getProperties())
                .addProperties(new HQLProperties(OrgaoEmissor.class, VOUtils.montarPath(EstabelecimentoResponsavelTecnico.PROP_RESPONSAVEL_TECNICO, ResponsavelTecnico.PROP_ORGAO_EMISSOR)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstabelecimentoResponsavelTecnico.PROP_ESTABELECIMENTO, Estabelecimento.PROP_CODIGO), codigoEstab))
                .setMaxResults(5).start().getList();
    }

    private List<EstabelecimentoCnae> getEstabelecimentoCnae(Long codigoEstab) {
        return LoadManager.getInstance(EstabelecimentoCnae.class)
                .addProperties(new HQLProperties(EstabelecimentoCnae.class).getProperties())
                .addProperties(new HQLProperties(Estabelecimento.class, EstabelecimentoCnae.PROP_ESTABELECIMENTO).getProperties())
                .addProperties(new HQLProperties(TabelaCnae.class, EstabelecimentoCnae.PROP_CNAE).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstabelecimentoCnae.PROP_ESTABELECIMENTO, Estabelecimento.PROP_CODIGO), codigoEstab))
                .start().getList();
    }
    
    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setLong("sim", RepositoryComponentDefault.SIM_LONG);
    }
}