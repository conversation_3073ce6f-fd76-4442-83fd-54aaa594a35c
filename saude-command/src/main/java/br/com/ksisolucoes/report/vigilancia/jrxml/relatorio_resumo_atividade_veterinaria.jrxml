<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_resumo_atividade_veterinaria" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="9155b179-bb03-426d-a648-bc310bac77b6">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.4157650000000217"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.celk.vigilancia.dto.RelatorioResumoAtividadeVeterinariaDTOParam.FormaApresentacao"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="formaApresentacao" class="br.com.celk.vigilancia.dto.RelatorioResumoAtividadeVeterinariaDTOParam.FormaApresentacao"/>
	<parameter name="TIPO_RESUMO" class="java.util.List"/>
	<field name="registroAtividadeVeterinaria" class="br.com.ksisolucoes.vo.vigilancia.RegistroAtividadeVeterinaria"/>
	<field name="registroAtividadeAnimal" class="br.com.ksisolucoes.vo.vigilancia.RegistroAtividadeAnimal"/>
	<field name="quantidade" class="java.lang.Long"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="qtdeTotalFA" class="java.lang.Long" resetType="Group" resetGroup="FA" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}]]></variableExpression>
	</variable>
	<variable name="qtdeTotalGeral" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}]]></variableExpression>
	</variable>
	<variable name="TP" class="br.com.celk.vigilancia.dto.RelatorioResumoAtividadeVeterinariaDTOParam.TipoResumo"/>
	<group name="GERAL">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupFooter>
			<band height="14">
				<line>
					<reportElement x="450" y="1" width="105" height="1" uuid="e3c43a7a-bafd-45ca-939b-12bed2e2584d"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="439" y="3" width="50" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="d5fbc985-f23a-47e5-a5bd-b7a0c0b1b1f8"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Total Geral*/
$V{BUNDLE}.getStringApplication("rotulo_total_geral") + ": "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="491" y="3" width="64" height="11" uuid="506be6e2-2861-4582-9cb8-fd4aa1eebca7"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{qtdeTotalGeral}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="FA" isReprintHeaderOnEachPage="true" keepTogether="true">
		<groupExpression><![CDATA[!FormaApresentacao.GERAL.equals($P{formaApresentacao})
?
    FormaApresentacao.TIPO_ATIVIDADE.equals($P{formaApresentacao})
    ?
        Bundle.getStringApplication("rotulo_tipo_atividade") + ": " + $F{registroAtividadeVeterinaria}.getAtividadeVeterinaria().getDescricao()
    :
     FormaApresentacao.SEXO.equals($P{formaApresentacao})
    ?
        $F{registroAtividadeAnimal}.getDescricaoSexo() == null
        ?
                Bundle.getStringApplication("rotulo_sexo") + ": " + "Sem Descrição"
        :
                Bundle.getStringApplication("rotulo_sexo") + ": " + $F{registroAtividadeAnimal}.getDescricaoSexo()
    :
        FormaApresentacao.PROFISSIONAL.equals($P{formaApresentacao})
        ?
            Bundle.getStringApplication("rotulo_profissional") + ": " + $F{registroAtividadeVeterinaria}.getProfissional().getNome()
        :
            FormaApresentacao.ESPECIE_ANIMAL.equals($P{formaApresentacao})
            ?
                $F{registroAtividadeAnimal}.getEspecieAnimal().getDescricao() == null
                ?
                    Bundle.getStringApplication("rotulo_especie_animal") + ": " +"Sem Descrição"
                :
                    Bundle.getStringApplication("rotulo_especie_animal") + ": " + $F{registroAtividadeAnimal}.getEspecieAnimal().getDescricao()
            :
                null
:
    null]]></groupExpression>
		<groupHeader>
			<band height="22" splitType="Stretch">
				<printWhenExpression><![CDATA[!FormaApresentacao.GERAL.equals($P{formaApresentacao})]]></printWhenExpression>
				<rectangle radius="8">
					<reportElement x="1" y="8" width="553" height="13" uuid="e5e68bc5-4efc-4b22-ae73-b77a820b63f6"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField evaluationTime="Group" evaluationGroup="FA" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-1" x="3" y="8" width="551" height="13" uuid="b9688033-a55f-4fb7-894e-a2be7b80d264"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[!FormaApresentacao.GERAL.equals($P{formaApresentacao})
?
    FormaApresentacao.TIPO_ATIVIDADE.equals($P{formaApresentacao})
    ?
        Bundle.getStringApplication("rotulo_tipo_atividade") + ": " + $F{registroAtividadeVeterinaria}.getAtividadeVeterinaria().getDescricao()
    :
     FormaApresentacao.SEXO.equals($P{formaApresentacao})
    ?
        $F{registroAtividadeAnimal}.getDescricaoSexo() == ""
        ?
                Bundle.getStringApplication("rotulo_sexo") + ": " + "Sem Descrição"
        :
                Bundle.getStringApplication("rotulo_sexo") + ": " + $F{registroAtividadeAnimal}.getDescricaoSexo()
    :
        FormaApresentacao.PROFISSIONAL.equals($P{formaApresentacao})
        ?
            Bundle.getStringApplication("rotulo_profissional") + ": " + $F{registroAtividadeVeterinaria}.getProfissional().getNome()
        :
            FormaApresentacao.ESPECIE_ANIMAL.equals($P{formaApresentacao})
            ?
                $F{registroAtividadeAnimal}.getEspecieAnimal().getDescricao() == null
                ?
                    Bundle.getStringApplication("rotulo_especie_animal") + ": " +"Sem Descrição"
                :
                    Bundle.getStringApplication("rotulo_especie_animal") + ": " + $F{registroAtividadeAnimal}.getEspecieAnimal().getDescricao()
            :
                null
:
    null]]></textFieldExpression>
				</textField>
			</band>
			<band height="16">
				<line>
					<reportElement x="0" y="14" width="555" height="1" uuid="aea060cb-9f73-42da-a184-43e78ff6256f"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="0" y="3" width="177" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="d6cac8f7-7f34-49d5-914a-98848eeb1a3c">
						<printWhenExpression><![CDATA[$P{TIPO_RESUMO}.contains($V{TP}.TIPO_ATIVIDADE.value())]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Tipo Atividade*/
$V{BUNDLE}.getStringApplication("rotulo_atividade_veterinaria")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="180" y="3" width="184" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="5354e98a-4c12-4eda-bd16-757f13afb866">
						<printWhenExpression><![CDATA[$P{TIPO_RESUMO}.contains($V{TP}.PROFISSIONAL.value())]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Profissional*/
$V{BUNDLE}.getStringApplication("rotulo_profissional")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="365" y="3" width="79" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="4dfa8b36-a59c-4e0e-8791-ee0c49a76bbe">
						<printWhenExpression><![CDATA[$P{TIPO_RESUMO}.contains($V{TP}.ESPECIE_ANIMAL.value())]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Espécie*/
$V{BUNDLE}.getStringApplication("rotulo_especie")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="445" y="4" width="31" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="5a7d74ec-245a-4f28-8322-46c4a6e9d4d7">
						<printWhenExpression><![CDATA[$P{TIPO_RESUMO}.contains($V{TP}.SEXO.value())]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Sexo*/
$V{BUNDLE}.getStringApplication("rotulo_sexo")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="503" y="3" width="52" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="d1a8215d-65ff-4f82-bb60-26cae463cafe"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Bottom" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Quantidade*/
$V{BUNDLE}.getStringApplication("rotulo_quantidade")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="20">
				<printWhenExpression><![CDATA[!FormaApresentacao.GERAL.equals($P{formaApresentacao})]]></printWhenExpression>
				<line>
					<reportElement x="470" y="1" width="85" height="1" uuid="d5bdde4e-1457-4e0a-a52d-3bab9153f29f"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="464" y="3" width="25" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="bc6d87e7-c68b-4a1f-a2e1-e450daf94550"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Total*/
$V{BUNDLE}.getStringApplication("rotulo_total") + ": "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="491" y="3" width="64" height="11" uuid="e239a846-8eb2-494c-9414-e08877a2c77d"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{qtdeTotalFA}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="12" splitType="Stretch">
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="503" y="0" width="52" height="12" uuid="b9c0a4de-a925-4feb-a12d-a3bf6e9d2693"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidade}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy">
				<reportElement x="0" y="0" width="177" height="12" uuid="51af1638-e4ce-4912-9184-eb6b77371cb9">
					<printWhenExpression><![CDATA[$P{TIPO_RESUMO}.contains($V{TP}.TIPO_ATIVIDADE.value())]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="8" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{registroAtividadeVeterinaria}.getAtividadeVeterinaria().getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy">
				<reportElement x="180" y="0" width="184" height="12" uuid="412ad7a6-1e94-4837-8c46-91c7b63260d4">
					<printWhenExpression><![CDATA[$P{TIPO_RESUMO}.contains($V{TP}.PROFISSIONAL.value())]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="8" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{registroAtividadeVeterinaria}.getProfissional().getNome()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="366" y="0" width="78" height="12" uuid="a8c8e438-63fc-46e6-b53c-16de4c6729d6">
					<printWhenExpression><![CDATA[$P{TIPO_RESUMO}.contains($V{TP}.ESPECIE_ANIMAL.value())]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="8" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{registroAtividadeAnimal}.getEspecieAnimal().getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="445" y="0" width="31" height="12" uuid="5f73ddfe-4df5-4397-846c-f67a03e9c472">
					<printWhenExpression><![CDATA[$P{TIPO_RESUMO}.contains($V{TP}.SEXO.value())]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="8" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{registroAtividadeAnimal}.getDescricaoSexo()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
