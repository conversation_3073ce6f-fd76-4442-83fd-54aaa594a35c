package br.com.ksisolucoes.report.vigilancia;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FichaInvestigacaoAgravoDTOParam;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.vigilancia.query.QueryFichaMalaria;
import br.com.ksisolucoes.util.Bundle;

import java.util.LinkedHashMap;

public class ImpressaoFichaInvestigacaoAgravoMalaria extends AbstractReport<FichaInvestigacaoAgravoDTOParam> {

    private QueryFichaMalaria query;

    public ImpressaoFichaInvestigacaoAgravoMalaria(FichaInvestigacaoAgravoDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        if(query == null){
            query = new QueryFichaMalaria();
        }
        return query;
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> columnsMap = new LinkedHashMap<>(((QueryFichaMalaria) getQuery()).getMapeamentoPlanilhaBase());

        //DADOS COMPLEMENTARES DO CASO
        columnsMap.put("dataInvestigacao", "31_data_investigacao");
        columnsMap.put("descricao", "_32_ocupacao");
        columnsMap.put("principalAtividadeUltimosDias", "_33_principal_atividade_ultimos_dias");
        columnsMap.put("tipoLamina", "_34_tipo_lamina");
        columnsMap.put("sintomas", "_35_sintomas");
        columnsMap.put("exameParasitosMm3", "_38_exame_parasitos_mm3");
        columnsMap.put("exameParasitemiaCruzes", "_39_parasitemia_cruzes");
        columnsMap.put("tratamentoEsquema", "_40_tratamento_esquema");
        columnsMap.put("tratamentoEsquemaOutro", "_40_tratamento_esquema_outro");
        columnsMap.put("tratamentoEsquemaDataInicio", "_41_tratamento_esquema_data_inicio");

        columnsMap.put("classificacaoFinal", "_42_classificacao_final");
        columnsMap.put("casoAutoctone", "_43_caso_autoctone");
        columnsMap.put("cidadeLocalInfeccao.descricaoCidadeUf", "_44_uf_provavel_infeccao");

        columnsMap.put("paisLocalInfeccao.descricao", "_45_pais_provavel_infeccao");
        columnsMap.put("cidadeLocalInfeccao.descricao", "_46_cidade_provavel_infeccao");
        columnsMap.put("cidadeLocalInfeccao.codigo", "_46_ibge_cidade");
        columnsMap.put("distritoLocalInfeccao", "_47_distrito_local_infeccao");
        columnsMap.put("bairroLocalInfeccao", "_48_bairro_local_infeccao");
        columnsMap.put("localicadeProvavelInfeccao", "_49_localidade_provavel_infeccao");
        columnsMap.put("dataEncerramento", "_50_data_encerramento");
        columnsMap.put("observacao", "observacao");

        columnsMap.put("dataExame", "data_exame");
        columnsMap.put("exameResultado", "resultado_exame");

        return columnsMap;
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/vigilancia/pdf/ficha_investigacao_malaria.pdf";
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return param.getTipoArquivo();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("relatorioFichasMalaria");
    }

}
