<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_receituario" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="812a5b70-40d5-4117-bc4f-81ca6e8acef4">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.8181818181818363"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="764"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.FormasUsoHelper"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="caminhoImagem" class="java.lang.String" isForPrompting="false"/>
	<parameter name="exibirCabecalho" class="java.lang.Boolean"/>
	<parameter name="observacaoGeral" class="java.lang.String"/>
	<parameter name="observacaoAutorizado" class="java.lang.String"/>
	<parameter name="EXIBIR_MARCADAGUA_SUS" class="java.lang.Boolean"/>
	<parameter name="localExameParametrizado" class="java.lang.String"/>
	<parameter name="permiteInformarPrioridadeSolicitacaoExames" class="java.lang.String"/>
	<parameter name="shouldPrintQrCode" class="java.lang.Boolean"/>
	<parameter name="urlQRCode" class="java.lang.String"/>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus">
		<fieldDescription><![CDATA[exameRequisicao.exame.usuarioCadsus]]></fieldDescription>
	</field>
	<field name="usuarioCadsusCns" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns"/>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional">
		<fieldDescription><![CDATA[exameRequisicao.exame.profissional]]></fieldDescription>
	</field>
	<field name="exameRequisicao" class="br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao"/>
	<field name="nomeEmpresaRelatorio" class="java.lang.String"/>
	<field name="numeroExame" class="java.lang.String"/>
	<field name="enderecoUsuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus"/>
	<field name="enderecoEstruturado" class="br.com.ksisolucoes.vo.enderecoestruturado.EnderecoEstruturado">
		<fieldDescription><![CDATA[enderecoUsuarioCadsus.enderecoEstruturado]]></fieldDescription>
	</field>
	<field name="codigoSolicitacaoAgendamento" class="java.lang.Long"/>
	<group name="pageGroup" footerPosition="ForceAtBottom">
		<groupFooter>
			<band height="79">
				<printWhenExpression><![CDATA[$F{exameRequisicao}.getExame().getNumeroProtocoloAutorizacao()==null]]></printWhenExpression>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-13" positionType="FixRelativeToBottom" mode="Opaque" x="186" y="59" width="186" height="12" uuid="400f196f-4f54-4289-a0cf-47bf7a181e06"/>
					<box leftPadding="4">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_requisicao")+": "+Data.formatar($F{exameRequisicao}.getExame().getDataSolicitacao())]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="FixRelativeToBottom" x="6" y="49" width="551" height="10" isRemoveLineWhenBlank="true" uuid="e070d6bd-a3f4-4c44-aca2-3b16785ba56c"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getProfissional().getRegistroFormatado()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" stretchType="RelativeToTallestObject" x="155" y="37" width="220" height="12" uuid="73d9946a-6cc2-46e1-a79f-3850ca4da95e"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getProfissional().getNome()]]></textFieldExpression>
				</textField>
			</band>
			<band height="264">
				<printWhenExpression><![CDATA[$F{exameRequisicao}.getExame().getNumeroProtocoloAutorizacao()!=null]]></printWhenExpression>
				<rectangle radius="0">
					<reportElement key="rectangle-2" stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="82" width="555" height="182" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="3cd0758e-29cc-4266-983e-48c8dcc50f5b"/>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</rectangle>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField-10" x="67" y="84" width="359" height="12" isRemoveLineWhenBlank="true" uuid="817f3701-0323-4b19-888a-367220772fc7"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Justified" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{localExameParametrizado} != null && !$P{localExameParametrizado}.isEmpty()
    ?
        $P{localExameParametrizado}
    :
        $F{exameRequisicao}.getExame().getLocalExame().getDescricao()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-9" x="4" y="84" width="61" height="12" isRemoveLineWhenBlank="true" uuid="e904b536-f62e-4cf8-82c4-9adcc47ffd1b"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_local_exame")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-13" mode="Opaque" x="426" y="84" width="126" height="12" uuid="ebcf3306-06cf-457f-baf7-6f6b841a3081">
						<printWhenExpression><![CDATA["S".equals($P{permiteInformarPrioridadeSolicitacaoExames})]]></printWhenExpression>
					</reportElement>
					<box leftPadding="4">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_prioridade")+": "+$F{exameRequisicao}.getExame().getDescricaoPrioridadeLaboratorio()]]></textFieldExpression>
				</textField>
				<elementGroup>
					<line>
						<reportElement positionType="FixRelativeToBottom" x="0" y="100" width="555" height="1" isRemoveLineWhenBlank="true" uuid="db612f16-093a-4933-800e-d90832aec914"/>
						<graphicElement>
							<pen lineWidth="0.5"/>
						</graphicElement>
					</line>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" positionType="FixRelativeToBottom" mode="Opaque" x="1" y="101" width="117" height="12" isRemoveLineWhenBlank="true" uuid="37ea02b8-8556-48d6-9faa-dbfd48c1de9c"/>
						<box leftPadding="4">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_autorizacao")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" positionType="FixRelativeToBottom" mode="Opaque" x="119" y="101" width="181" height="12" isRemoveLineWhenBlank="true" uuid="913e38ca-1b5e-4c19-92c6-5bba22095bbd"/>
						<box leftPadding="4">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dados_clinicos")]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" positionType="FixRelativeToBottom" mode="Opaque" x="2" y="178" width="102" height="12" isRemoveLineWhenBlank="true" uuid="eafd75d2-a20c-46a9-9758-bd5a43fa6576"/>
						<box leftPadding="4">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data")+": "+Data.formatar($F{exameRequisicao}.getExame().getDataAutorizacao())]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" positionType="FixRelativeToBottom" mode="Opaque" x="122" y="228" width="218" height="13" isRemoveLineWhenBlank="true" uuid="400f196f-4f54-4289-a0cf-47bf7a181e06"/>
						<box leftPadding="2" rightPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_requisicao")+": "+Data.formatar($F{exameRequisicao}.getExame().getDataSolicitacao())]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField" positionType="FixRelativeToBottom" x="340" y="240" width="214" height="13" isRemoveLineWhenBlank="true" uuid="73d9946a-6cc2-46e1-a79f-3850ca4da95e"/>
						<box>
							<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="10"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getProfissional().getNome()]]></textFieldExpression>
					</textField>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement key="textField-10" positionType="FixRelativeToBottom" x="122" y="194" width="430" height="34" isRemoveLineWhenBlank="true" uuid="14d98898-c8dd-4e30-90ec-35743dc86b2c"/>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Top">
							<font fontName="Arial" size="9"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getNumeroProtocoloAutorizacao()!=null
?
    Coalesce.asString($P{observacaoGeral})+" \n \n" +Coalesce.asString($P{observacaoAutorizado})
:
    Coalesce.asString($P{observacaoGeral})]]></textFieldExpression>
					</textField>
					<componentElement>
						<reportElement positionType="FixRelativeToBottom" x="1" y="115" width="117" height="63" isRemoveLineWhenBlank="true" uuid="bef0af97-bc3e-4d30-aa90-9d00c061474b"/>
						<jr:Code128 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" textPosition="bottom" quietZone="8.0" verticalQuietZone="0.0">
							<jr:codeExpression><![CDATA[$F{exameRequisicao}.getExame().getNumeroProtocoloAutorizacao()]]></jr:codeExpression>
						</jr:Code128>
					</componentElement>
					<line>
						<reportElement positionType="FixRelativeToBottom" stretchType="RelativeToTallestObject" x="118" y="99" width="1" height="165" isRemoveLineWhenBlank="true" uuid="29c7f2de-bcf3-400a-a205-0647a32f0e52"/>
						<graphicElement>
							<pen lineWidth="0.5"/>
						</graphicElement>
					</line>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-9" positionType="FixRelativeToBottom" x="122" y="115" width="430" height="79" isRemoveLineWhenBlank="true" uuid="34ab25bc-3c12-46c5-9028-6d9d24e9416b"/>
						<box bottomPadding="2">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Top">
							<font fontName="Arial" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getDescricaoDadoClinico()]]></textFieldExpression>
					</textField>
					<textField pattern="MM/yyyy" isBlankWhenNull="true">
						<reportElement positionType="FixRelativeToBottom" mode="Opaque" x="6" y="189" width="103" height="12" uuid="42b6feaa-b602-46dd-a179-e2a0776d9c5e">
							<printWhenExpression><![CDATA[$F{exameRequisicao}.getExame().getDataAutorizacao() != null]]></printWhenExpression>
						</reportElement>
						<textElement>
							<font fontName="Arial" size="9" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getDataAutorizacao()]]></textFieldExpression>
					</textField>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement positionType="FixRelativeToBottom" x="340" y="252" width="214" height="12" isRemoveLineWhenBlank="true" uuid="ebdc02d6-1424-4db1-9247-7faf2f80ac08"/>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Arial" size="10"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getProfissional().getRegistroFormatado()]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" positionType="FixRelativeToBottom" mode="Opaque" x="122" y="238" width="218" height="12" isRemoveLineWhenBlank="true" uuid="822fae49-5427-4455-8d8d-023c30474064">
							<printWhenExpression><![CDATA[$F{exameRequisicao}.getExame().getDataValidadeAutorizacao() != null]]></printWhenExpression>
						</reportElement>
						<box leftPadding="2" rightPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_validade_autorizacao")+": "+Data.formatar($F{exameRequisicao}.getExame().getDataValidadeAutorizacao())]]></textFieldExpression>
					</textField>
					<textField isBlankWhenNull="true">
						<reportElement key="textField-13" positionType="FixRelativeToBottom" mode="Opaque" x="122" y="250" width="218" height="13" isRemoveLineWhenBlank="true" uuid="c921a920-5627-4045-9c9d-3f2bf306b0c0"/>
						<box leftPadding="2" rightPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
						</textElement>
						<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_desejada")+": "+Data.formatar($F{exameRequisicao}.getExame().getDataDesejada())]]></textFieldExpression>
					</textField>
				</elementGroup>
				<image scaleImage="RealHeight">
					<reportElement x="472" y="0" width="80" height="80" uuid="ba5d8673-2445-4a3d-8f8e-8805d13dd0cf">
						<printWhenExpression><![CDATA[$P{shouldPrintQrCode}]]></printWhenExpression>
					</reportElement>
					<imageExpression><![CDATA[com.google.zxing.client.j2se.MatrixToImageWriter.toBufferedImage(
    new com.google.zxing.qrcode.QRCodeWriter().encode(
            $P{urlQRCode},
            com.google.zxing.BarcodeFormat.QR_CODE, 900, 900))]]></imageExpression>
				</image>
			</band>
		</groupFooter>
	</group>
	<group name="Exame" isStartNewPage="true" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{exameRequisicao}.getExame().getCodigo()]]></groupExpression>
		<groupHeader>
			<band height="107">
				<textField isBlankWhenNull="true">
					<reportElement key="textField-9" positionType="Float" x="0" y="41" width="28" height="13" uuid="2ab87f75-6c1b-4f8f-8448-76e1b22ae861">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Sexo*/
Bundle.getStringApplication("rotulo_sexo")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-9" positionType="Float" x="290" y="55" width="82" height="13" uuid="4fdbe425-bb43-433a-b216-c262b82602ee">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box rightPadding="0"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Telefone*/
Bundle.getStringApplication("rotulo_telefone")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-9" x="0" y="13" width="60" height="13" uuid="91f122e0-d979-4d05-9ef9-4b958d326746"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Nome:*/
$F{exameRequisicao}.getExame().getUsuarioCadsus().getBundleNomeSocialNome()+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-10" positionType="Float" x="372" y="55" width="103" height="13" uuid="f7a794e7-66a7-4910-b2ab-0216c23b7d6b">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="3"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getCelularOuTelefonesFormatado()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-8" positionType="Float" x="160" y="41" width="130" height="13" uuid="78186ffb-8f76-4b97-89a8-300d1b379d81">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="3"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[Data.getDescricaoIdade($F{usuarioCadsus}.getDataNascimento())]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-8" positionType="Float" x="372" y="41" width="103" height="13" uuid="7776d7ab-8928-469a-85cc-b857dd61fb6c">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="3"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[Data.formatar($F{usuarioCadsus}.getDataNascimento())]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-10" positionType="Float" x="23" y="55" width="108" height="13" uuid="95b827bd-96bd-410f-8bd1-5aab94b928a0">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="3"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsusCns}.getNumeroCartaoFormatado()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" positionType="Float" x="290" y="41" width="82" height="13" uuid="709dee49-f1fe-4b3d-a0c9-9e6296d240d2">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box rightPadding="0"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Data Nascimento*/
Bundle.getStringApplication("rotulo_data_nascimento")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-9" positionType="Float" x="0" y="55" width="23" height="13" uuid="3abcdacc-2abd-42d9-8f11-f0590ac63ec4">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*CNS*/
Bundle.getStringApplication("rotulo_cns")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-9" positionType="Float" x="0" y="69" width="23" height="13" uuid="893b8d5e-a078-4b15-9efb-e1723c7018e6">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Mae*/
Bundle.getStringApplication("rotulo_mae")+": "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField-10" positionType="Float" x="23" y="69" width="452" height="13" uuid="439733db-7d6d-4b12-9176-201f16f53e55">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="3"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNomeMae()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" positionType="Float" x="131" y="41" width="29" height="13" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Idade*/
Bundle.getStringApplication("rotulo_idade")+": "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField-10" x="60" y="13" width="415" height="13" uuid="c5c20e7f-b3fc-4ca6-9326-d6114c2f9e55"/>
					<box leftPadding="3"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getUsuarioCadsus().getNomeSocialNome()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-10" positionType="Float" mode="Opaque" x="28" y="41" width="103" height="13" uuid="b0f68a33-29f8-458f-8e72-d561ff3fc2c5">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="3"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getSexoFormatado()]]></textFieldExpression>
				</textField>
				<componentElement>
					<reportElement x="479" y="13" width="73" height="55" uuid="22e6df20-78a3-4ac7-9388-17af2b110776"/>
					<jr:Code128 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" textPosition="bottom">
						<jr:codeExpression><![CDATA[$F{numeroExame}]]></jr:codeExpression>
					</jr:Code128>
				</componentElement>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-9" x="0" y="0" width="101" height="13" uuid="541d9f2e-4a88-4ae1-a4ea-96489b8b6c04"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true" isItalic="true" isUnderline="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dados_paciente")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement positionType="Float" x="0" y="96" width="555" height="1" uuid="0e67c792-d4aa-4642-838e-1b3e7be29a6b"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-9" positionType="Float" x="131" y="55" width="29" height="13" uuid="194bffd5-1d53-4eff-a557-e78cf248cdc1">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null && $F{usuarioCadsus}.getCpf() != null && !$F{usuarioCadsus}.getCpf().trim().isEmpty()]]></printWhenExpression>
					</reportElement>
					<box rightPadding="0"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*CPF*/
Bundle.getStringApplication("rotulo_cpf")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-10" positionType="Float" x="160" y="55" width="130" height="13" uuid="c68d092d-83b1-494c-be90-1304a94efecd">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null && $F{usuarioCadsus}.getCpf() != null && !$F{usuarioCadsus}.getCpf().trim().isEmpty()]]></printWhenExpression>
					</reportElement>
					<box leftPadding="3"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getCpfFormatado()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField-10" positionType="Float" stretchType="RelativeToBandHeight" x="45" y="82" width="430" height="13" uuid="abd0cbf3-54c7-4ab1-ade9-18f92ac09662"/>
					<box leftPadding="3"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{enderecoEstruturado}.getCodigo() != null
? $F{enderecoEstruturado}.getEnderecoComplementoComCepFormatado()
: $F{enderecoUsuarioCadsus}.getEnderecoComplementoComCepFormatado()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-9" positionType="Float" x="0" y="82" width="45" height="13" uuid="1f458d18-915d-45e3-a71e-7fc5f039d596"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Endereco*/
Bundle.getStringApplication("rotulo_endereco")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-9" positionType="Float" x="0" y="27" width="60" height="13" uuid="3dede2ad-6e55-4297-96c6-85fabab7d04b"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Nome Social:*/
$F{exameRequisicao}.getExame().getUsuarioCadsus().getBundleNomeSocialNome(true)+": "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField-10" positionType="Float" x="60" y="27" width="415" height="13" uuid="0409f2ad-70cd-4582-8541-ba1d981e71ae"/>
					<box leftPadding="3"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getUsuarioCadsus().getNomeSocialNome(true)]]></textFieldExpression>
				</textField>
			</band>
			<band height="94">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField-10" positionType="Float" isPrintRepeatedValues="false" x="272" y="0" width="62" height="13" uuid="c4ac1b40-a6d7-4826-8054-9ad2a2c48f44"/>
					<box leftPadding="3"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getUsuarioCadsusDado().getPeso() != null ? $F{exameRequisicao}.getExame().getUsuarioCadsusDado().getPeso() + " kg" : ""]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField-13" positionType="Float" isPrintRepeatedValues="false" mode="Transparent" x="11" y="26" width="514" height="15" isRemoveLineWhenBlank="true" uuid="45ec9dbc-57b3-4eff-bec0-cf4c3d348710"/>
					<box leftPadding="4">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getClassificacaoDeRisco().getDescricao() == null ?
Bundle.getStringApplication("rotulo_prioridade")+": "+$F{exameRequisicao}.getExame().getDescricaoPrioridadeLaboratorio() :
Bundle.getStringApplication("rotulo_classificacao_risco")+" "+$F{exameRequisicao}.getExame().getClassificacaoDeRisco().getDescricao()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-13" isPrintRepeatedValues="false" mode="Transparent" x="11" y="1" width="144" height="12" isRemoveLineWhenBlank="true" uuid="913e38ca-1b5e-4c19-92c6-5bba22095bbd"/>
					<box leftPadding="4">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dados_clinicos")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-9" positionType="Float" isPrintRepeatedValues="false" x="372" y="0" width="37" height="13" isRemoveLineWhenBlank="true" uuid="10f458b2-231e-4488-9d42-2a8e0ed6ad4d"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Altura*/
Bundle.getStringApplication("rotulo_altura")+": "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField-13" positionType="Float" isPrintRepeatedValues="false" mode="Transparent" x="11" y="66" width="531" height="12" isRemoveLineWhenBlank="true" uuid="e50cb716-3647-426c-bfce-8b78728406e9"/>
					<box leftPadding="4">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getCid().getCodigo()!=null ?
Bundle.getStringApplication("rotulo_cid_enum")+": "+ $F{exameRequisicao}.getExame().getCid().getCodigo() + " - " + $F{exameRequisicao}.getExame().getCid().getDescricao() : "Cid:"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField-10" positionType="Float" isPrintRepeatedValues="false" x="409" y="0" width="133" height="13" isRemoveLineWhenBlank="true" uuid="df9d65c1-9b04-4322-b985-e04edb8367f9"/>
					<box leftPadding="3"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getUsuarioCadsusDado().getAltura() != null ? $F{exameRequisicao}.getExame().getUsuarioCadsusDado().getAltura() + " cm" : ""]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-13" positionType="FixRelativeToBottom" isPrintRepeatedValues="false" mode="Opaque" x="11" y="79" width="175" height="12" isRemoveLineWhenBlank="true" uuid="8dd393d3-56d7-4bb1-97a2-9b16986e06e4">
						<printWhenExpression><![CDATA[$F{codigoSolicitacaoAgendamento} != null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="4">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cod_solicitacao")+": "+$F{codigoSolicitacaoAgendamento}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-9" positionType="Float" isPrintRepeatedValues="false" x="235" y="0" width="37" height="13" isRemoveLineWhenBlank="true" uuid="9a55d812-08af-43bc-a9a7-518282c4d6bd"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Peso*/
Bundle.getStringApplication("rotulo_peso")+": "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField-9" positionType="Float" isPrintRepeatedValues="false" x="15" y="13" width="510" height="13" isRemoveLineWhenBlank="true" uuid="8de65b2f-1ca3-4791-a9a5-3724458bdc15"/>
					<box bottomPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Justified" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getDescricaoDadoClinico()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-13" positionType="Float" isPrintRepeatedValues="false" mode="Transparent" x="11" y="41" width="144" height="12" isRemoveLineWhenBlank="true" uuid="3f2ded36-ded6-468f-9684-6ad034f99bf0"/>
					<box leftPadding="4">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_justificativa")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField-9" positionType="Float" isPrintRepeatedValues="false" x="11" y="53" width="510" height="13" isRemoveLineWhenBlank="true" uuid="57a96ff3-c66f-442d-bc4f-3edd0e467a09"/>
					<box bottomPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Justified" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getJustificativaClassificacaoRisco()]]></textFieldExpression>
				</textField>
			</band>
			<band height="50">
				<textField isBlankWhenNull="true">
					<reportElement key="textField-9" positionType="Float" x="449" y="34" width="83" height="12" isRemoveLineWhenBlank="true" uuid="7ffb2d23-b78d-474b-aa9c-e289cdbcc745"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quantidade")]]></textFieldExpression>
				</textField>
				<image scaleImage="RealHeight">
					<reportElement x="503" y="0" width="39" height="24" uuid="f2bd809e-92b5-45b7-9990-9277ca435236">
						<printWhenExpression><![CDATA[$P{shouldPrintQrCode}]]></printWhenExpression>
					</reportElement>
					<imageExpression><![CDATA[com.google.zxing.client.j2se.MatrixToImageWriter.toBufferedImage(
    new com.google.zxing.qrcode.QRCodeWriter().encode(
            $P{urlQRCode},
            com.google.zxing.BarcodeFormat.QR_CODE, 900, 900))]]></imageExpression>
				</image>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-9" positionType="Float" x="15" y="34" width="102" height="12" uuid="30ab60a7-e7e9-4fa6-be4a-fac5ffa62f3a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_exames_solicitados")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField-10" positionType="Float" x="45" y="12" width="441" height="12" uuid="2d88cf1c-0caf-4284-9161-44b71173190e"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{exameRequisicao}.getExame().getTipoExame().getDescricao()]]></textFieldExpression>
				</textField>
				<line>
					<reportElement positionType="Float" x="0" y="45" width="555" height="1" uuid="40ff24c8-7938-43e7-b9a8-7002358dcec4"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
			</band>
		</groupHeader>
	</group>
	<background>
		<band height="802" splitType="Stretch">
			<printWhenExpression><![CDATA[$P{EXIBIR_MARCADAGUA_SUS}]]></printWhenExpression>
			<image hAlign="Center" vAlign="Middle">
				<reportElement mode="Opaque" x="1" y="160" width="554" height="587" uuid="b14226d9-d78b-48fe-8c71-7a2d6e8aad6b"/>
				<imageExpression><![CDATA["br/com/ksisolucoes/gui/imagens/back_ground_sus.png"]]></imageExpression>
			</image>
		</band>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="24" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="0" y="0" width="475" height="12" uuid="962d3e65-0f74-46c1-ad98-938a47998a9c"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{exameRequisicao}.getExameProcedimento().getProcedimento().getCodigo()!=null
?
    $F{exameRequisicao}.getExameProcedimento().getProcedimento().getCodigoFormatado()+
    " - "+
    $F{exameRequisicao}.getExameProcedimento().getDescricaoProcedimento() + (1L == Coalesce.asLong($F{exameRequisicao}.getFlagHiv(), 3L) ? ", B24+" : "") + (1L == Coalesce.asLong($F{exameRequisicao}.getFlagRetratamento(), 3L) ? ", Retratamento" : "")
:
    $F{exameRequisicao}.getExameProcedimento().getDescricaoProcedimento() + (1L == Coalesce.asLong($F{exameRequisicao}.getFlagHiv(), 3L) ? ", B24+" : "") + (1L == Coalesce.asLong($F{exameRequisicao}.getFlagRetratamento(), 3L) ? ", Retratamento" : "")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="479" y="0" width="46" height="12" uuid="18baf89d-ac34-44c4-bd5d-14b11e8d0a36"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{exameRequisicao}.getQuantidade()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" positionType="Float" x="69" y="12" width="456" height="12" isRemoveLineWhenBlank="true" uuid="a3089e6c-b5cf-4d34-aa12-ff9e258f08f7">
					<printWhenExpression><![CDATA[new Boolean($F{exameRequisicao}.getComplemento()!=null)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{exameRequisicao}.getComplemento()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="0" y="12" width="55" height="12" isRemoveLineWhenBlank="true" uuid="258d9db4-11f6-45ab-b1b2-e198c8012b49">
					<printWhenExpression><![CDATA[new Boolean($F{exameRequisicao}.getComplemento()!=null)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_complemento")+": "]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
