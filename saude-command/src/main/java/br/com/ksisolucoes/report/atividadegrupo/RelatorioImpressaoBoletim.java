package br.com.ksisolucoes.report.atividadegrupo;

import br.com.ksisolucoes.bo.atividadegrupo.interfaces.dto.AtividadeGrupoDTO;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.atividadegrupo.interfaces.dto.AtividadeDTOParam;
import br.com.ksisolucoes.report.atividadegrupo.query.QueryRelatorioImpressaoBoletim;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoPaciente;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoProcedimento;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoProfissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;

import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoBoletim extends AbstractReport<AtividadeDTOParam> {
    
    private List<AtividadeGrupoProfissional> lstAtividadeGrupoProfissional;
    private List<AtividadeGrupoProcedimento> lstAtividadeGrupoProcedimento;
    private List<AtividadeGrupoPaciente> atividadeGrupoPacienteList;
    private String titulo;
    
    public RelatorioImpressaoBoletim(AtividadeDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/atividadegrupo/jrxml/relatorio_impressao_boletim.jrxml";
    }

    @Override
    public String getTitulo() {
        return titulo;
    }

    @Override
    protected Collection customizeCollection(Collection collection) {
        AtividadeGrupoDTO dto = (AtividadeGrupoDTO) collection.iterator().next();
        titulo = dto.getAtividadeGrupo().getTipoAtividadeGrupo().getDescricaoBoletim();
        
        return collection;
    }

    @Override
    public ITransferDataReport getQuery() {
        
        addParametro("profissionaisList", procurarAtividadeGrupoProfissional());
        addParametro("procedimentosList", procurarAtividadeGrupoProcedimento());
        addParametro("atividadeGrupoPacienteList", procurarAtividadeGrupoPaciente());

        return new QueryRelatorioImpressaoBoletim(lstAtividadeGrupoProfissional.size(), lstAtividadeGrupoProcedimento.size(), atividadeGrupoPacienteList);
    }

    private List<AtividadeGrupoProfissional> procurarAtividadeGrupoProfissional(){
        lstAtividadeGrupoProfissional = LoadManager.getInstance(AtividadeGrupoProfissional.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtividadeGrupoProfissional.PROP_ATIVIDADE_GRUPO, AtividadeGrupo.PROP_CODIGO), getParam().getCodigoAtividade()))
                .start().getList();
        
        return lstAtividadeGrupoProfissional;
    }
    
    private List<AtividadeGrupoProcedimento> procurarAtividadeGrupoProcedimento(){
        lstAtividadeGrupoProcedimento = LoadManager.getInstance(AtividadeGrupoProcedimento.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtividadeGrupoProcedimento.PROP_ATIVIDADE_GRUPO, AtividadeGrupo.PROP_CODIGO), getParam().getCodigoAtividade()))
                .start().getList();
        
        return lstAtividadeGrupoProcedimento;
    }

    private List<AtividadeGrupoPaciente> procurarAtividadeGrupoPaciente(){
        List<AtividadeGrupoPaciente> atividadeGrupoPacienteList = LoadManager.getInstance(AtividadeGrupoPaciente.class)
                .addProperties(new HQLProperties(AtividadeGrupoPaciente.class).getProperties())
                .addProperties(new HQLProperties(UsuarioCadsus.class, AtividadeGrupoPaciente.PROP_USUARIO_CADSUS).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtividadeGrupoPaciente.PROP_ATIVIDADE_GRUPO, AtividadeGrupo.PROP_CODIGO), getParam().getCodigoAtividade()))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(AtividadeGrupoPaciente.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME), BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();

        return this.atividadeGrupoPacienteList = atividadeGrupoPacienteList;
    }
}
