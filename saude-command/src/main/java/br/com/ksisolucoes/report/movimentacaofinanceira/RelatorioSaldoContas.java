/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.movimentacaofinanceira;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioSaldoContasDTOParam;
import br.com.ksisolucoes.report.movimentacaofinanceira.query.QueryRelatorioSaldoContas;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

/**
 *
 * <AUTHOR>
 */
public class RelatorioSaldoContas extends AbstractReport<RelatorioSaldoContasDTOParam> {

    public RelatorioSaldoContas(RelatorioSaldoContasDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/movimentacaofinanceira/jrxml/relatorio_saldo_contas.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_saldo_contas");
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioSaldoContas();
    }

    @Override
    protected void customReport() throws DAOException, ValidacaoException {
        getParam().setControlaSaldoPorAno(RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("controlaSaldoPorAno")));
        addParametro("controlaSaldoPorAno", getParam().getControlaSaldoPorAno());
        addParametro("totalGeral", getParam().getTotalGeral());
    }
}
