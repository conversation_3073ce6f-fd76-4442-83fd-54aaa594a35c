package br.com.ksisolucoes.report.consorcio.pedidotransferencialicitacao.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioPedidosTransferenciaFornecedorDTO;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioPedidosTransferenciaFornecedorDTOParam;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioPedidosTransferenciaFornecedorDTOParam.FormaApresentacao;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioPedidosTransferenciaFornecedorDTOParam.TipoResumo;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioPedidosTransferenciaFornecedorDTOParam.TipoEstoque;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacaoItem.StatusPedidoTransferenciaLicitacaoItem;
import java.util.List;
import java.util.Map;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioPedidosTransferenciaFornecedor extends CommandQuery<QueryRelatorioPedidosTransferenciaFornecedor> implements ITransferDataReport<RelatorioPedidosTransferenciaFornecedorDTOParam, RelatorioPedidosTransferenciaFornecedorDTO> {

    private RelatorioPedidosTransferenciaFornecedorDTOParam param;
    private List<RelatorioPedidosTransferenciaFornecedorDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(RelatorioPedidosTransferenciaFornecedorDTO.class.getName());

        if (FormaApresentacao.CONSORCIADO.equals(param.getFormaApresentacao())) {
            hql.addToSelectAndGroup("consorciado.codigo", "consorciado.codigo");
            hql.addToSelectAndGroupAndOrder("consorciado.descricao", "consorciado.descricao");
        } else if (FormaApresentacao.FORNECEDOR.equals(param.getFormaApresentacao())) {
            hql.addToSelectAndGroup("p.codigo", "fornecedor.codigo");
            hql.addToSelectAndGroupAndOrder("p.descricao", "fornecedor.descricao");
        }

        if (TipoResumo.CONSORCIADO.equals(param.getTipoResumo())) {
            hql.addToSelectAndGroup("consorciado.codigo", "consorciado.codigo");
            hql.addToSelectAndGroupAndOrder("consorciado.descricao", "consorciado.descricao");
        } else if (TipoResumo.FORNECEDOR.equals(param.getTipoResumo())) {
            hql.addToSelectAndGroup("p.codigo", "fornecedor.codigo");
            hql.addToSelectAndGroupAndOrder("p.descricao", "fornecedor.descricao");
        }

        hql.addToSelect("sum(ptli.quantidade)", "quantidade");
        hql.addToSelect("sum(ptli.quantidade * li.precoUnitario )", "valor");
        hql.addToSelect("sum(round( ((ptli.quantidade-coalesce(ptli.quantidadeEnviada,0)) * li.precoUnitario ), 2))", "valorSaldo");
        if (this.param.getFlagListaProdutos().equals(RepositoryComponentDefault.SIM)) {
            hql.addToSelectAndGroup("prod.codigo", "produto.codigo");
            hql.addToSelectAndGroupAndOrder("prod.descricao", "produto.descricao");
            hql.addToSelectAndGroup("uni.unidade", "produto.unidade.unidade");
            hql.addToSelect("sum(coalesce(ptli.quantidadeEnviada,0))", "quantidadeEnviada");
            hql.addToSelectAndGroup("li.precoUnitario", "precoUnitario");

            if (TipoEstoque.DISPONIVEL.equals(this.param.getTipoEstoque())) {
                hql.addToSelect("((estoqueDepositoView.estoqueFisico - estoqueDepositoView.estoqueVencido) + estoqueDepositoView.estoqueEncomendado - (estoqueDepositoView.estoqueReservado - estoqueDepositoView.estoqueReservadoVencido) )", "estoque");
                hql.addToGroup("estoqueDepositoView.estoqueFisico");
                hql.addToGroup("estoqueDepositoView.estoqueVencido");
                hql.addToGroup("estoqueDepositoView.estoqueEncomendado");
                hql.addToGroup("estoqueDepositoView.estoqueReservado");
                hql.addToGroup("estoqueDepositoView.estoqueReservadoVencido");
            } else if (TipoEstoque.FISICO.equals(this.param.getTipoEstoque())) {
                hql.addToSelect("(estoqueDepositoView.estoqueFisico - estoqueDepositoView.estoqueVencido)", "estoque");
                hql.addToGroup("estoqueDepositoView.estoqueFisico");
                hql.addToGroup("estoqueDepositoView.estoqueVencido");
            }
        }
        hql.addToSelect("sum(ptli.quantidade-coalesce(ptli.quantidadeEnviada,0))", "saldo");

        hql.addToFrom("EloPedidoLicitacaoItemPedidoTransferenciaLicitacaoItem elo1 "
                + " left join elo1.pedidoLicitacaoItem pli"
                + " left join elo1.pedidoTransferenciaLicitacaoItem ptli"
                + " left join ptli.pedidoTransferenciaLicitacao ptl"
                + " left join ptl.empresaConsorciado consorciado"
                + " left join ptli.produto prod"
                + " left join prod.unidade uni");
        hql.addToFrom("EloLicitacaoPedido elo2 "
                + " left join elo2.pedidoLicitacaoItem pli2"
                + " left join elo2.licitacaoItem li"
                + " left join li.pessoa p");

        if (this.param.getFlagListaProdutos().equals(RepositoryComponentDefault.SIM)) {
            hql.addToFrom("EstoqueDepositoView estoqueDepositoView");
            hql.addToFrom("EmpresaMaterial em");
        }

        hql.addToWhereWhithAnd("ptli.status <> ", StatusPedidoTransferenciaLicitacaoItem.CANCELADO.value());
        hql.addToWhereWhithAnd("pli.codigo = pli2.codigo");

        hql.addToWhereWhithAnd("consorciado = ", param.getConsorciado());
        hql.addToWhereWhithAnd("p = ", param.getFornecedor());
        hql.addToWhereWhithAnd("p.descricao is not null");
        hql.addToWhereWhithAnd("prod = ", param.getProduto());
        hql.addToWhereWhithAnd("ptl.dataCadastro ", param.getPeriodo());
        hql.addToWhereWhithAnd("ptl.status in ", param.getInSituacao());

        if (this.param.getFlagListaProdutos().equals(RepositoryComponentDefault.SIM)) {
            hql.addToWhereWhithAnd("prod = estoqueDepositoView.produto");
            hql.addToWhereWhithAnd("em = ptl.empresaAlmoxarifado");
            hql.addToWhereWhithAnd("estoqueDepositoView.deposito = em.deposito");
            hql.addToWhereWhithAnd("estoqueDepositoView.empresa = ptl.empresaAlmoxarifado");
        }
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        for (RelatorioPedidosTransferenciaFornecedorDTO result1 : result) {
            if (result1.getPrecoUnitario() == null) {
                result1.setPrecoUnitario(0.0);
            }
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioPedidosTransferenciaFornecedorDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(RelatorioPedidosTransferenciaFornecedorDTOParam param) {
        this.param = param;
    }
}
