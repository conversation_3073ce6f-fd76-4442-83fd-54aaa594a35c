<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_impressao_atendimento_internacao" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="9211d19c-9cb7-4699-a35b-b3ac889c3b2f">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.zoom" value="1.6105100000000008"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferencia"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.util.Coalesce"/>
	<import value="br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa"/>
	<import value="br.com.ksisolucoes.report.ReportProperties"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="br.com.ksisolucoes.vo.entradas.estoque.Produto"/>
	<import value="br.com.ksisolucoes.vo.basico.Empresa"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<parameter name="atendimentoAlta" class="br.com.ksisolucoes.vo.prontuario.basico.AtendimentoAlta"/>
	<field name="listEvolucao" class="java.util.List"/>
	<field name="listDispensacao" class="java.util.List"/>
	<group name="Footer" footerPosition="ForceAtBottom">
		<groupFooter>
			<band height="82">
				<line>
					<reportElement x="10" y="48" width="231" height="1" uuid="65c41752-13b0-46b3-9c61-e684b73c5e9e"/>
				</line>
				<textField>
					<reportElement x="10" y="50" width="231" height="13" uuid="b9b86659-fe94-4b07-816c-f72f441c3bc7"/>
					<textElement textAlignment="Center"/>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_assinatura_medico_odontologo")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="292" y="48" width="231" height="1" uuid="65c41752-13b0-46b3-9c61-e684b73c5e9e"/>
				</line>
				<textField>
					<reportElement x="292" y="50" width="231" height="13" uuid="b9b86659-fe94-4b07-816c-f72f441c3bc7"/>
					<textElement textAlignment="Center"/>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_assinatura_paciente_responsavel")]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<detail>
		<band height="20">
			<subreport>
				<reportElement positionType="Float" x="0" y="0" width="555" height="15" uuid="063f6b13-f697-4b87-9aca-60480048f3ec"/>
				<subreportParameter name="atendimentoAlta">
					<subreportParameterExpression><![CDATA[$P{atendimentoAlta}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource( $F{listEvolucao})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/jrxml/sub_relatorio_impressao_atendimento_emergencia_evolucao.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="15">
			<subreport>
				<reportElement positionType="Float" x="0" y="0" width="555" height="15" uuid="063f6b13-f697-4b87-9aca-60480048f3ec"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource( $F{listDispensacao})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/jrxml/sub_relatorio_impressao_atendimento_emergencia_dispensacao.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
