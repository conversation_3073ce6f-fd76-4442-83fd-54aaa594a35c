package br.com.ksisolucoes.report.prontuario;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.query.QueryRelatorioImpressaoAnamnese;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoAnamnese extends AbstractReport<Atendimento> {

    public RelatorioImpressaoAnamnese(Atendimento param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/prontuario/jrxml/relatorio_impressao_anamneses.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_anamneses_paciente");
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioImpressaoAnamnese();
    }

}
