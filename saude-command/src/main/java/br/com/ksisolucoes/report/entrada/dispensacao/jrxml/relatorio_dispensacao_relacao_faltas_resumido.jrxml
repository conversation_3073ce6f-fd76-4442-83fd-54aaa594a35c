<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_dispensacao_relacao_faltas_detalhado" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="22" bottomMargin="22" uuid="d289bc8f-2fe3-4738-aba9-f0e9662d683a">
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.zoom" value="2.3579476910000103"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioDispensacaoRelacaoFaltasDTOParam"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioDispensacaoRelacaoFaltasDTOParam.FormaApresentacao"/>
	<import value="br.com.celk.util.Coalesce"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="FORMA_APRESENTACAO" class="java.lang.String" isForPrompting="false"/>
	<field name="unidade" class="java.lang.String"/>
	<field name="quantidadePrescrita" class="java.lang.Double"/>
	<field name="data" class="java.util.Date"/>
	<field name="paciente" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="produto" class="br.com.ksisolucoes.vo.entradas.estoque.Produto"/>
	<field name="codigoDispensacao" class="java.lang.Long"/>
	<variable name="totalQuantidadePrescrita" class="java.lang.Double" resetType="Group" resetGroup="FORMA_APRESENTACAO" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidadePrescrita}]]></variableExpression>
	</variable>
	<group name="FORMA_APRESENTACAO" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$P{FORMA_APRESENTACAO}.equals(FormaApresentacao.ESTABELECIMENTO.toString())
                    ?
                        $F{empresa} == null
                        ?
                            $P{FORMA_APRESENTACAO} + ": " + Bundle.getStringApplication("rotulo_sem_descricao")
                        :
                            $P{FORMA_APRESENTACAO} + ": " + $F{empresa}.getDescricao()
                      :
                        $P{FORMA_APRESENTACAO}.equals(FormaApresentacao.PRODUTO.toString())
                        ?
                            $F{produto} == null
                            ?
                                $P{FORMA_APRESENTACAO} + ": " +  Bundle.getStringApplication("rotulo_sem_descricao")
                            :
                                $P{FORMA_APRESENTACAO} + ": " + $F{produto}.getDescricao()
                        :
                            $P{FORMA_APRESENTACAO}.equals(FormaApresentacao.PACIENTE.toString())
                            ?
                                $F{paciente} == null
                                ?
                                    $P{FORMA_APRESENTACAO} + ": " +  Bundle.getStringApplication("rotulo_sem_descricao")
                                :
                                    $P{FORMA_APRESENTACAO} + ": " + $F{paciente}.getNome()
                             :
                            null]]></groupExpression>
		<groupHeader>
			<band height="17" splitType="Stretch">
				<printWhenExpression><![CDATA[$P{FORMA_APRESENTACAO} != ""]]></printWhenExpression>
				<rectangle radius="10">
					<reportElement mode="Transparent" x="0" y="0" width="555" height="15" uuid="9f2f3214-4574-4b71-a49b-5c47e0e97d34"/>
				</rectangle>
				<textField evaluationTime="Group" evaluationGroup="FORMA_APRESENTACAO" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-19" mode="Transparent" x="0" y="0" width="555" height="15" forecolor="#000000" backcolor="#CCCCCC" uuid="c9813072-5948-4cf3-8ae3-3c5ef9709c63"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{FORMA_APRESENTACAO}.equals(FormaApresentacao.ESTABELECIMENTO.toString())
                    ?
                        $F{empresa} == null
                        ?
                            $P{FORMA_APRESENTACAO} + ": " + Bundle.getStringApplication("rotulo_sem_descricao")
                        :
                            $P{FORMA_APRESENTACAO} + ": " + $F{empresa}.getDescricao()
                      :
                        $P{FORMA_APRESENTACAO}.equals(FormaApresentacao.PRODUTO.toString())
                        ?
                            $F{produto} == null
                            ?
                                $P{FORMA_APRESENTACAO} + ": " +  Bundle.getStringApplication("rotulo_sem_descricao")
                            :
                                $P{FORMA_APRESENTACAO} + ": " + $F{produto}.getDescricao()
                        :
                            $P{FORMA_APRESENTACAO}.equals(FormaApresentacao.PACIENTE.toString())
                            ?
                                $F{paciente} == null
                                ?
                                    $P{FORMA_APRESENTACAO} + ": " +  Bundle.getStringApplication("rotulo_sem_descricao")
                                :
                                    $P{FORMA_APRESENTACAO} + ": " + $F{paciente}.getNome()
                             :
                            null]]></textFieldExpression>
				</textField>
			</band>
			<band height="13">
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-4" mode="Transparent" x="363" y="0" width="187" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="ba35828b-938c-4c04-a835-11ecf2bce7a7"/>
					<box>
						<pen lineWidth="0.0"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication( "rotulo_qtd_prescrita_abv" )]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-3" mode="Transparent" x="226" y="0" width="124" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="c5dbf7ad-4cb5-497d-8cd6-4bbc42ce4ceb"/>
					<box>
						<pen lineWidth="0.0"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication( "rotulo_unidade_abv" )]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-2" mode="Transparent" x="0" y="0" width="215" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="c9d20af5-1e70-4800-8c98-ae786ee1ceca"/>
					<box>
						<pen lineWidth="0.0"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication( "rotulo_produto" )]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="12" width="555" height="1" uuid="9d2d9f84-b30e-488a-a05b-e8bc37a982be"/>
				</line>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="12">
				<printWhenExpression><![CDATA[$P{FORMA_APRESENTACAO}.equals(FormaApresentacao.PRODUTO.toString()) || $P{FORMA_APRESENTACAO} == ""]]></printWhenExpression>
				<textField isStretchWithOverflow="true" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField" mode="Transparent" x="491" y="1" width="62" height="9" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="705fbcb2-eda0-459d-a574-07c650f64377"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalQuantidadePrescrita} == null ? 0.00 : $V{totalQuantidadePrescrita}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-8" mode="Transparent" x="436" y="1" width="54" height="9" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f4eacc95-6175-49cc-b5c2-6d1ac0466bbd"/>
					<box>
						<pen lineWidth="0.0"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total" )]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="438" y="0" width="117" height="1" uuid="08b33873-0c1c-4f38-b616-5e751589c89c"/>
				</line>
			</band>
		</groupFooter>
	</group>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="10" splitType="Stretch">
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" mode="Transparent" x="3" y="1" width="215" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="73a7380c-ab68-4720-b6f3-************"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{produto}.getDescricaoFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" mode="Transparent" x="226" y="1" width="124" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="836aab42-9e4c-4f94-a0f9-cd9e55afa339"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unidade}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField" mode="Transparent" x="366" y="1" width="187" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="4e52f20c-9d7b-4b21-b165-6eb1c857678b"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidadePrescrita}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
</jasperReport>
