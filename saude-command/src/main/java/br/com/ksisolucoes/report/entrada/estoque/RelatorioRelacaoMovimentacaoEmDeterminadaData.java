/*
 * RelatorioRelacaoMovimentacaoEmDeterminadaData.java
 *
 * Created on 17 de Outubro, 2005
 */
package br.com.ksisolucoes.report.entrada.estoque;

//Importa as classes necessarias

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioRelacaoMovimentacaoEmDeterminadaDataDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.query.QueryRelacaoMovimentacaoEmDeterminadaData;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.entradas.estoque.RelacaoMovimentacaoEmDeterminadaData;

import java.util.LinkedHashMap;

import static ch.lambdaj.Lambda.on;

/**n
 * <AUTHOR> Possamai
 */
public class RelatorioRelacaoMovimentacaoEmDeterminadaData extends AbstractReport<RelatorioRelacaoMovimentacaoEmDeterminadaDataDTOParam> implements ReportProperties {

    private RelatorioRelacaoMovimentacaoEmDeterminadaDataDTOParam param;

    public RelatorioRelacaoMovimentacaoEmDeterminadaData( RelatorioRelacaoMovimentacaoEmDeterminadaDataDTOParam bean ) {
        super(bean);
        this.param = bean;
    }
    
    @Override
    public String getTitulo() {
        return Bundle.getStringApplication( "rotulo_relatorio_relacao_movimentacao_em_determinada_data" );
    }
    
    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_movimentacao_em_determinada_data.jrxml";
    }

    @Override
    public ITransferDataReport getQuery() {
        addParametro("tipoPreco", getParam().getTipoPreco());
        return new QueryRelacaoMovimentacaoEmDeterminadaData();
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();

        RelacaoMovimentacaoEmDeterminadaData proxy = on(RelacaoMovimentacaoEmDeterminadaData.class);

        map.put(Bundle.getStringApplication("rotulo_unidade"), proxy.getDescricaoEmpresa());
        map.put(Bundle.getStringApplication("rotulo_grupo"), proxy.getDescricaoGrupoProduto());
        map.put(Bundle.getStringApplication("rotulo_sub_grupo"), proxy.getDescricaoSubGrupoFormatado());
        map.put(Bundle.getStringApplication("rotulo_cod_produto"), proxy.getCodigoProduto());
        map.put(Bundle.getStringApplication("rotulo_produto"), proxy.getDescricaoProdutoFormatado());
        map.put(Bundle.getStringApplication("rotulo_unidade_abv"), proxy.getUnidade());
        map.put(Bundle.getStringApplication("rotulo_estoque_fisico"), proxy.getEstoqueFisico());
        if (RepositoryComponentDefault.TipoPreco.PRECO_MEDIO.equals(param.getTipoPreco())) {
            map.put(Bundle.getStringApplication("rotulo_preco_medio"), proxy.getPreco());
        } else {
            map.put(Bundle.getStringApplication("rotulo_preco_custo"), proxy.getPreco());
        }
        map.put(Bundle.getStringApplication("rotulo_total"), proxy.getTotal());
        map.put(Bundle.getStringApplication("rotulo_data_ultima_movimentacao"), proxy.getDataFormatada());

        return map;
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return getParam().getTipoArquivo();
    }

}