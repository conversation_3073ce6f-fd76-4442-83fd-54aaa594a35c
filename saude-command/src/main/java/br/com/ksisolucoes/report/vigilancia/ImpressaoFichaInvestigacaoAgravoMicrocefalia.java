/*tipoNotificacao
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.vigilancia;

import br.com.celk.report.vigilancia.query.QueryFichaMicrocefalia;
import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FichaInvestigacaoAgravoDTOParam;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;

import java.util.LinkedHashMap;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoFichaInvestigacaoAgravoMicrocefalia extends AbstractReport<FichaInvestigacaoAgravoDTOParam> {

    private QueryFichaMicrocefalia query;

    public ImpressaoFichaInvestigacaoAgravoMicrocefalia(FichaInvestigacaoAgravoDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        if(query == null){
            query = new QueryFichaMicrocefalia();
        }
        return query;
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> campos = new LinkedHashMap<>(((QueryFichaMicrocefalia)getQuery()).getMapeamentoPlanilhaBase());
        

        campos.put("", "");

        return campos;
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/vigilancia/pdf/ficha_investigacao_agravo_microcelafia.pdf";
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return param.getTipoArquivo();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_notificacao_negativa");
    }

}
