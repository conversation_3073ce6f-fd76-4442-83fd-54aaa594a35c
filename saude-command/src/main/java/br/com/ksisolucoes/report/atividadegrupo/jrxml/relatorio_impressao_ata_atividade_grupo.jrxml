<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_impressao_ata_atividade_grupo" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="9cc01e46-52b6-4d7b-a2b4-4f86b8864455">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.6105100000000068"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.bo.atividadegrupo.interfaces.dto.AtividadeGrupoPacienteWebDTO"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.FormasUsoHelper"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.CollectionUtils"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<subDataset name="pacientes" uuid="a6de6c60-5461-48e1-8a4c-c8a5dab7a8ae">
		<field name="nome" class="java.lang.String"/>
	</subDataset>
	<parameter name="caminhoImagem" class="java.lang.String" isForPrompting="false"/>
	<parameter name="exibirCabecalho" class="java.lang.Boolean"/>
	<parameter name="profissionaisList" class="java.util.List"/>
	<parameter name="procedimentosList" class="java.util.List"/>
	<parameter name="usuarioCadsusList" class="java.util.List"/>
	<field name="tipoAtividadeGrupo" class="br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupo">
		<fieldDescription><![CDATA[atividadeGrupo.tipoAtividadeGrupo]]></fieldDescription>
	</field>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa">
		<fieldDescription><![CDATA[atividadeGrupo.empresa]]></fieldDescription>
	</field>
	<field name="atividadeGrupo" class="br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo">
		<fieldDescription><![CDATA[atividadeGrupo]]></fieldDescription>
	</field>
	<field name="lstAtividadeGrupoPaciente" class="java.util.List">
		<fieldDescription><![CDATA[lstAtividadeGrupoPaciente]]></fieldDescription>
	</field>
	<variable name="count" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[1]]></variableExpression>
	</variable>
	<group name="Dados">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band height="62">
				<textField isBlankWhenNull="true">
					<reportElement key="textField-4" x="0" y="0" width="45" height="13" uuid="7775e62c-97d8-4cc6-ad93-48d1b2b69d82"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_empresa")+":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-4" x="46" y="0" width="343" height="13" uuid="9ac58d92-2d0d-41a6-86de-258917fa2d83"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA["("+$F{empresa}.getCnes()+") "+$F{empresa}.getDescricao()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-4" x="0" y="14" width="30" height="13" uuid="5cedda44-990c-4785-8c1f-04ebae2c59ad"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data")+":"]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
					<reportElement key="textField-4" x="32" y="14" width="56" height="13" uuid="814afafc-55a9-43ba-9a2f-fbd3274f7380"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{atividadeGrupo}.getDataInicio()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-4" x="111" y="14" width="60" height="13" uuid="ebb03c4c-4fc0-4492-9900-7aa0a1155af6"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_hora_inicio")+":"]]></textFieldExpression>
				</textField>
				<textField pattern="HH:mm" isBlankWhenNull="true">
					<reportElement key="textField-4" x="173" y="14" width="50" height="13" uuid="52a55bfc-3ef3-4d01-b200-4a8c1ea6afc0"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{atividadeGrupo}.getDataInicio()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-4" x="236" y="14" width="70" height="13" uuid="f554466a-b18d-48eb-be1c-9036277e1d60"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_hora_termino")+":"]]></textFieldExpression>
				</textField>
				<textField pattern="HH:mm" isBlankWhenNull="true">
					<reportElement key="textField-4" x="307" y="14" width="50" height="13" uuid="c7fa30e0-6948-4c01-83b5-311331e6b05d"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{atividadeGrupo}.getDataFim()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-4" x="0" y="28" width="45" height="13" uuid="19a6bb48-65ec-49e2-b88a-bc3fe43de1f2"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_assunto")+":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-4" x="46" y="28" width="489" height="13" uuid="0bcd48ac-7fbc-4f91-982a-08afd6c1ffb5"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{atividadeGrupo}.getAssunto()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-4" x="0" y="42" width="32" height="13" uuid="19a6bb48-65ec-49e2-b88a-bc3fe43de1f2"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_local")+":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-4" x="33" y="42" width="502" height="13" uuid="0bcd48ac-7fbc-4f91-982a-08afd6c1ffb5"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{atividadeGrupo}.getLocalAtividadeGrupo().getDescricao()]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="1" y="61" width="554" height="1" uuid="2ed033a2-516d-4127-9264-7bdb568fcbc5"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="39">
			<textField isStretchWithOverflow="true">
				<reportElement x="57" y="3" width="498" height="36" uuid="7488b6fe-6491-4fd6-8eb5-fc71e9eea9ef"/>
				<box rightPadding="2"/>
				<textElement textAlignment="Justified"/>
				<textFieldExpression><![CDATA[$F{atividadeGrupo}.getDescricaoAta()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-4" x="1" y="3" width="56" height="13" uuid="bbc5bf67-11ba-4db5-9511-87c906c47c7f"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_descricao")+":"]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
