/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.vigilancia;

import br.com.celk.report.vigilancia.query.QueryFichaHivGestante;
import br.com.celk.system.report.TipoRelatorio;
import br.com.celk.vigilancia.dto.FichaInvestigacaoAgravoAdultoHivDTO;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FichaInvestigacaoAgravoDTOParam;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;
import ch.lambdaj.Lambda;
import java.util.LinkedHashMap;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoFichaInvestigacaoAgravoHivGestante extends AbstractReport<FichaInvestigacaoAgravoDTOParam> {

    public ImpressaoFichaInvestigacaoAgravoHivGestante(FichaInvestigacaoAgravoDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryFichaHivGestante();
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        FichaInvestigacaoAgravoAdultoHivDTO dto = Lambda.on(FichaInvestigacaoAgravoAdultoHivDTO.class);
        LinkedHashMap<String, Object> columnsMap = new LinkedHashMap<>();
        
        columnsMap.put("Codigo Registro Agravo", "registroAgravo_codigo");
        columnsMap.put("Codigo Notificacao", "registroAgravo_codigoNotificacao");
        columnsMap.put("03 Data Notificacao", "registroAgravo_dataRegistro");
        columnsMap.put("04 Sigla Estado", "estadoRegistroAgravo_sigla");
        columnsMap.put("05 IBGE Cidade Notificacao", "cidadeRegistroAgravo_codigo");
        columnsMap.put("05 Cidade Notificacao", "cidadeRegistroAgravo_descricao");
        columnsMap.put("06 Unidade Notificacao", "unidadeRegistroAgravo_descricao");
        columnsMap.put("06 CNES Unidade Notificacao", "unidadeRegistroAgravo_cnes");
        columnsMap.put("07 Data Acidente", "registroAgravo_dataCadastro");
        columnsMap.put("08 Paciente", "paciente_nome");
        columnsMap.put("09 Data Nascimento", "paciente_dataNascimento");
        columnsMap.put("11 Sexo", "paciente_sexo");
        columnsMap.put("12 Idade Gestacional", "registroAgravo_idadeGestacional");
        columnsMap.put("13 Raca", "racaPaciente_codigo");// o codigo do banco esta diferente do codigo da ficha
        columnsMap.put("14 Escolaridade", "registroAgravo_escolaridade");
        columnsMap.put("14 Descricao Escolaridade", "registroAgravo_descricaoEscolaridade");
        columnsMap.put("15 Cartao SUS", "usuarioCadsus_cns");
        columnsMap.put("16 Nome Mae", "paciente_nomeMae");
        columnsMap.put("17 Estado Residencia", "estadoPaciente_sigla");
        columnsMap.put("18 IBGE Municipio Residencia", "cidadePaciente_codigo");
        columnsMap.put("18 Municipio Residencia", "cidadePaciente_descricao");
        columnsMap.put("19 Distrito", "investigacaoAgravoAdultoHiv_distrito");
        columnsMap.put("20 Bairro Residencia", "enderecoPaciente_bairro");
        columnsMap.put("21 Logradouro Residencia", "enderecoPaciente_logradouro");
        columnsMap.put("22 Numero Residencia", "enderecoPaciente_numeroLogradouro");
        columnsMap.put("23 Complemento", "enderecoPaciente_complementoLogradouro");
        columnsMap.put("24 GEO Campo 1", "investigacaoAgravoAdultoHiv_geoCampo1");
        columnsMap.put("25 GEO Campo 2", "investigacaoAgravoAdultoHiv_geoCampo2");
        columnsMap.put("26 Ponto Referencia", "enderecoPaciente_pontoReferencia");
        columnsMap.put("27 CEP", "enderecoPaciente_cep");
        columnsMap.put("28 Telefone Paciente", "paciente_telefone");
        columnsMap.put("29 Zona", "investigacaoAgravoAdultoHiv_zona");
        columnsMap.put("30 Pais", "paisPaciente_descricao");
        
        columnsMap.put("31 Ocupacao", "ocupacao_descricao");
        columnsMap.put("32 Evidencia Lab HIV", "investigacaoAgravoHivGestante_evidenciaLaboratorialHiv");
        columnsMap.put("33 Fez Pre-natal", "investigacaoAgravoHivGestante_fezFazPreNatal");
        columnsMap.put("34 Estado Pre-natal", "estadoUnidadePrenatal_sigla");
        columnsMap.put("35 IBGE Cidade Pre-natal", "cidadeUnidadePrenatal_codigo");
        columnsMap.put("35 Cidade Pre-natal", "cidadeUnidadePrenatal_descricao");
        columnsMap.put("36 Unidade Pre-natal", "unidadeRealizacaoPreNatal_descricao");
        columnsMap.put("37 Numero SISPRENATAL", "investigacaoAgravoHivGestante_numeroSisPreNatal");
        columnsMap.put("38 Uso anti-retrovirais", "investigacaoAgravoHivGestante_usoAntiRetroviraisPreNatal");
        columnsMap.put("39 Data Inicio anti-retrovirais", "investigacaoAgravoHivGestante_dataInicioUsoAntiRetroviral");
        
        columnsMap.put("40 Estado Parto", "estadoParto_sigla");
        columnsMap.put("41 IBGE Cidade Parto", "municipioParto_codigo");
        columnsMap.put("41 Cidade Parto", "municipioParto_descricao");
        columnsMap.put("42 Local Parto", "localRealizacaoParto_descricao");
        columnsMap.put("43 Data Parto", "investigacaoAgravoHivGestante_dataParto");
        columnsMap.put("44 Tipo Parto", "investigacaoAgravoHivGestante_tipoParto");
        columnsMap.put("45 Uso anti-retroviral durante parto", "investigacaoAgravoHivGestante_fezUsoProfilaxiaAntiRetroviralDuranteParto");
        columnsMap.put("46 Evolucao gravidez", "investigacaoAgravoHivGestante_evolucaoGravidez");
        columnsMap.put("47 Profilaxia na crianca", "investigacaoAgravoHivGestante_inicioProfilaxiaAntiRetroviralCrianca");
        
        columnsMap.put("Investigador", "profissionalInvestigacao_nome");
        columnsMap.put("Unidade Investigacao", "unidadeProfissionalInvestigacao_descricao");
        
        return columnsMap;
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/vigilancia/pdf/ficha_investigacao_gestante_hiv.pdf";
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return param.getTipoArquivo();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_agravo_hiv_gestante");
    }

}
