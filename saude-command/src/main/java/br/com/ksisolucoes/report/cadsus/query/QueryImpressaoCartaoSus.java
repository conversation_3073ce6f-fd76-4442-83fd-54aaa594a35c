package br.com.ksisolucoes.report.cadsus.query;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.dto.EventoSistemaDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.cadsus.interfaces.dto.ImpressaoCartaoSusDTO;
import br.com.ksisolucoes.report.cadsus.interfaces.dto.ImpressaoCartaoSusDTOParam;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.EventoSistema;
import br.com.ksisolucoes.vo.basico.EventoSistema.IdentificacaoEvento;
import br.com.ksisolucoes.vo.basico.EventoSistema.TipoEvento;
import br.com.ksisolucoes.vo.basico.TipoEndereco;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEndereco;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.hibernate.Session;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryImpressaoCartaoSus extends CommandQuery<QueryImpressaoCartaoSus> implements ITransferDataReport<ImpressaoCartaoSusDTOParam, ImpressaoCartaoSusDTO> {

    private List<ImpressaoCartaoSusDTO> result;
    private ImpressaoCartaoSusDTOParam param;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelectAndGroup("uc.codigo", "usuarioCadsus.codigo");
        hql.addToSelectAndGroup("uc.nome", "usuarioCadsus.nome");
        hql.addToSelectAndGroup("uc.dataNascimento", "usuarioCadsus.dataNascimento");
        hql.addToSelectAndGroup("uc.sexo", "usuarioCadsus.sexo");
        hql.addToSelect("min(cns.numeroCartao)", "numeroCartao");
        hql.addToSelect("min(cidade.descricao)", "cidade");
        hql.addToSelect("min(cidade.codigo)", "codigoCidade");
        hql.addToSelect("min(estado.sigla)", "uf");//Caso exista mais de um UsuarioCadsusEndereco para o paciente pode ficar errado a cidade e o estado!
        hql.setTypeSelect(ImpressaoCartaoSusDTO.class.getName());
        
        hql.addToFrom("UsuarioCadsusCns cns "
                + " left join cns.usuarioCadsus uc, "
                + " UsuarioCadsusEndereco uce "
                + " left join uce.id.usuarioCadsus uc2 "
                + " left join uce.id.endereco euc "
//                + " left join uce.tipoEndereco tipoEndereco "
                + " left join euc.cidade cidade"
                + " left join cidade.estado estado");

        hql.addToWhereWhithAnd(" uc in ", param.getUsuarioCadsus());
        hql.addToWhereWhithAnd(" uc = uc2 ");
        
        hql.addToWhereWhithAnd(" euc.codigo = (select min(uceSub.id.endereco.codigo) from UsuarioCadsusEndereco uceSub where "
                + "uceSub.id.usuarioCadsus = uc and "
                + "coalesce(uceSub.status,"+UsuarioCadsusEndereco.STATUS_ABERTO+") =  "+UsuarioCadsusEndereco.STATUS_ABERTO+" and "
                + "uceSub.tipoEndereco.codigo = "+TipoEndereco.TIPO_ENDERECO_PRINCIPAL+ ")");
        
//        hql.addToWhereWhithAnd(" coalesce(uce.status,"+UsuarioCadsusEndereco.STATUS_ABERTO+") =", UsuarioCadsusEndereco.STATUS_ABERTO);
//        hql.addToWhereWhithAnd(" tipoEndereco.codigo =", TipoEndereco.TIPO_ENDERECO_PRINCIPAL);
        hql.addToWhereWhithAnd(" cns.excluido =", RepositoryComponentDefault.NAO_EXCLUIDO);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        for (UsuarioCadsus usuarioCadsus : param.getUsuarioCadsus()) {
            EventoSistemaDTO dto = new EventoSistemaDTO();
            dto.setDescricao(Bundle.getStringApplication("msg_realizado_impressao_cns", Data.formatarDataHora(Data.getDataAtual()),getSessao().<Usuario>getUsuario().getDescricaoFormatado()));
            dto.setNivelCriticidade(EventoSistema.NivelCriticidade.INFORMACAO.value());
            dto.setFonteEvento("QueryImpressaoCartaoSus");
            dto.setKeyword(Bundle.getStringApplication("key_impressao_cartao"));
            dto.setTipoEvento(TipoEvento.PROGRAMA.value());
            dto.setIdentificacaoEvento(IdentificacaoEvento.CADSUS.value());
            dto.setUsuarioCadsus(usuarioCadsus);
            dto.setCodigoDocumento(null);

            BOFactory.getBO(CommomFacade.class).gerarEventoSistema(dto);
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<ImpressaoCartaoSusDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(ImpressaoCartaoSusDTOParam param) {
        this.param = param;
    }

}
