package br.com.ksisolucoes.report.entrada.estoque.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioRelacaoProdutosUnidadeDTO;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioRelacaoProdutosUnidadeDTOParam;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.TipoProduto;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryListagemProdutosUnidade extends CommandQuery<QueryListagemProdutosUnidade> {

    private List<RelatorioRelacaoProdutosUnidadeDTO> dtoList;
    private RelatorioRelacaoProdutosUnidadeDTOParam bean;

    public QueryListagemProdutosUnidade(RelatorioRelacaoProdutosUnidadeDTOParam bean) {
        this.bean = bean;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect( "ee.id.empresa.referencia", "empresaCodigo" );
        hql.addToSelect( "ee.id.empresa.descricao", "empresaDescricao" );
        hql.addToSelect( "ee.id.produto.subGrupo.roGrupoProduto.codigo", "grupoCodigo" );
        hql.addToSelect( "ee.id.produto.subGrupo.roGrupoProduto.descricao", "grupoDescricao" );
        hql.addToSelect( "ee.id.produto.subGrupo.id.codigo", "subGrupoCodigo" );
        hql.addToSelect( "ee.id.produto.subGrupo.descricao", "subGrupoDescricao" );
        hql.addToSelect( "ee.id.produto.referencia", "produtoCodigo" );
        hql.addToSelect( "ee.id.produto.descricao", "produtoDescricao" );
        hql.addToSelect( "ee.id.produto.unidade.unidade", "unidadeUnidade" );
        hql.addToSelect( "ee.estoqueMinimo", "estoqueMinimo" );
        hql.addToSelect( "ee.quantidadePadraoDispensacao", "quantidadePadraoDispensacao" );

        hql.addToFrom( EstoqueEmpresa.class.getName() + " ee " );

        hql.setTypeSelect( RelatorioRelacaoProdutosUnidadeDTO.class.getName() );

        hql.addToWhereWhithAnd( "ee.id.empresa in ", this.bean.getEmpresaList() );
        hql.addToWhereWhithAnd("ee.id.produto.subGrupo.roGrupoProduto = ", this.bean.getGrupoProdutoSubGrupo());
        hql.addToWhereWhithAnd("ee.id.produto.subGrupo = ", this.bean.getSubGrupo());

        hql.addToWhereWhithAnd("ee.flagAtivo = ", RepositoryComponentDefault.SIM);

        if (TipoProduto.TIPO_PRODUTO_MEDICAMENTO.equals(this.bean.getTipoProduto())) {
            hql.addToWhereWhithAnd("ee.id.produto.subGrupo.flagMedicamento = ", SubGrupo.MEDICAMENTO_SIM);
        } else if (TipoProduto.TIPO_PRODUTO_VACINA.equals(this.bean.getTipoProduto())) {
            hql.addToWhereWhithAnd("ee.id.produto.subGrupo.roGrupoProduto.flagVacina = ", RepositoryComponentDefault.SIM_LONG);
        } else if (TipoProduto.TIPO_PRODUTO_MATERIAL.equals(this.bean.getTipoProduto())) {
            hql.addToWhereWhithAnd("ee.id.produto.subGrupo.flagMedicamento != ", SubGrupo.MEDICAMENTO_SIM);
            hql.addToWhereWhithAnd("ee.id.produto.subGrupo.roGrupoProduto.flagVacina != ", RepositoryComponentDefault.SIM_LONG);
        }
//        hql.addToWhereWhithAnd("ee.id.produto.tipoProduto.codigo = ", this.bean.getTipoProduto());

        hql.addToOrder( "ee.id.empresa.referencia" );
        if(this.bean.getFormaApresentacao().intValue() == ReportProperties.AGRUPAR_GRUPO) {
            if(this.bean.getOrdenacao().equals( Produto.PROP_CODIGO )) {
                hql.addToOrder( "ee.id.produto.subGrupo.roGrupoProduto.codigo" );
                hql.addToOrder( "ee.id.produto.subGrupo.id.codigo" );
            }else{
                hql.addToOrder( "ee.id.produto.subGrupo.roGrupoProduto.descricao" );
                hql.addToOrder( "ee.id.produto.subGrupo.descricao" );
            }
        }
        if(this.bean.getOrdenacao().equals( Produto.PROP_CODIGO )) {
            hql.addToOrder( "ee.id.produto.referencia" );
        }else{
            hql.addToOrder( "ee.id.produto.descricao" );
        }

    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.dtoList = hql.getBeanList((List<Map<String, Object>>) result);
    }

    public List<RelatorioRelacaoProdutosUnidadeDTO> getDtoList() {
        return dtoList;
    }

}
