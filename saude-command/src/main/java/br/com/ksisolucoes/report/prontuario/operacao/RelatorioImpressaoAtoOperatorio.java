package br.com.ksisolucoes.report.prontuario.operacao;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.operacao.interfaces.dto.QueryImpressaoAtoOperatorioDTOParam;
import br.com.ksisolucoes.report.prontuario.operacao.query.QueryImpressaoAtoOperatorio;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoAtoOperatorio extends AbstractReport<QueryImpressaoAtoOperatorioDTOParam> {

    public RelatorioImpressaoAtoOperatorio(QueryImpressaoAtoOperatorioDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/prontuario/operacao/jrxml/relatorio_impressao_ato_operatorio.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_ato_operatorio");
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryImpressaoAtoOperatorio();
    }

}
