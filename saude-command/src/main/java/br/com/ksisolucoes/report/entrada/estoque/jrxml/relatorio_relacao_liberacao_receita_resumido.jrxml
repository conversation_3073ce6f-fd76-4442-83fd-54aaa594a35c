<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_liberacao_receita_resumido" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<import value="br.com.ksisolucoes.vo.entradas.dispensacao.*"/>
	<import value="br.com.ksisolucoes.report.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.entradas.dispensacao.LiberacaoReceita"/>
	<import value="br.com.ksisolucoes.vo.basico.*"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="FORMA_APRESENTACAO" class="java.lang.String" isForPrompting="false"/>
	<parameter name="TIPO_RESUMO" class="java.lang.String" isForPrompting="false"/>
	<field name="codigoEmpresa" class="java.lang.String"/>
	<field name="unidade" class="java.lang.String"/>
	<field name="quantidadeDispensada" class="java.lang.Double"/>
	<field name="numeroDispensacao" class="java.lang.Long"/>
	<field name="dataDispensacao" class="java.util.Date"/>
	<field name="nomeUsuarioDestino" class="java.lang.String"/>
	<field name="codigoProduto" class="java.lang.String"/>
	<field name="codigoOperador" class="java.lang.Long"/>
	<field name="observacao" class="java.lang.String"/>
	<field name="dataUsuario" class="java.util.Date"/>
	<field name="codigoGrupoProduto" class="java.lang.Long"/>
	<field name="codigoSubGrupoProduto" class="java.lang.Long"/>
	<field name="codigoProfissional" class="java.lang.Long"/>
	<field name="descricaoProfissional" class="java.lang.String"/>
	<field name="valorTotal" class="java.lang.Double"/>
	<field name="descricaoProfissionalFormatado" class="java.lang.String"/>
	<field name="descricaoOperadorFormatado" class="java.lang.String"/>
	<field name="descricaoGrupoProduto" class="java.lang.String"/>
	<field name="descricaoProdutoFormatado" class="java.lang.String"/>
	<field name="nomeOperador" class="java.lang.String"/>
	<field name="descricaoSubGrupoProduto" class="java.lang.String"/>
	<field name="preco" class="java.lang.Double"/>
	<field name="codigoEmpresaOrigem" class="java.lang.Long"/>
	<field name="descricaoEmpresaOrigemLiberacaoFormatado" class="java.lang.String"/>
	<field name="descricaoEmpresaOrigemDispensacaoFormatado" class="java.lang.String"/>
	<field name="codigoUsuarioCadsus" class="java.lang.Long"/>
	<field name="descricaoUsuarioCadsusFormatado" class="java.lang.String"/>
	<variable name="SomaTotalFA" class="java.lang.Double" resetType="Group" resetGroup="FORMA_APRESENTACAO" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotal}]]></variableExpression>
	</variable>
	<variable name="SomaTotalEmpresa" class="java.lang.Double" resetType="Group" resetGroup="empresa" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotal}]]></variableExpression>
	</variable>
	<variable name="somaQuantidade" class="java.lang.Double" resetType="Group" resetGroup="TipoResumo" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidadeDispensada}]]></variableExpression>
	</variable>
	<variable name="somaTotal" class="java.lang.Double" resetType="Group" resetGroup="TipoResumo" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotal}]]></variableExpression>
	</variable>
	<variable name="totalGeral" class="java.lang.Double" resetType="Group" resetGroup="GrupoGeral" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotal}]]></variableExpression>
	</variable>
	<group name="GrupoGeral">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band splitType="Stretch"/>
		</groupHeader>
		<groupFooter>
			<band height="28" splitType="Stretch">
				<rectangle radius="5">
					<reportElement key="rectangle-3" mode="Opaque" x="349" y="7" width="140" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</rectangle>
				<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-132" mode="Opaque" x="413" y="10" width="65" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.Double"><![CDATA[$V{totalGeral} == null ? 0.00 : $V{totalGeral}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-133" mode="Opaque" x="356" y="10" width="55" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*total*/Bundle.getStringApplication("rotulo_total_geral") + " :"]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="empresa" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{codigoEmpresa}]]></groupExpression>
		<groupHeader>
			<band height="28" splitType="Stretch">
				<textField evaluationTime="Group" evaluationGroup="empresa" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-38" mode="Opaque" x="0" y="0" width="535" height="15" forecolor="#000000" backcolor="#CCCCCC"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_unidade") + ": " + $F{descricaoEmpresaOrigemLiberacaoFormatado}]]></textFieldExpression>
				</textField>
				<rectangle radius="5">
					<reportElement key="rectangle-2" mode="Opaque" x="0" y="15" width="535" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{TIPO_RESUMO}.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</rectangle>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-127" mode="Opaque" x="288" y="17" width="57" height="9" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{TIPO_RESUMO}.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*Quantidade*/Bundle.getStringApplication("rotulo_quantidade_dispensada_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-128" mode="Opaque" x="415" y="17" width="65" height="9" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{TIPO_RESUMO}.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*Valor*/Bundle.getStringApplication("rotulo_total_dispensado_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-129" mode="Opaque" x="271" y="17" width="15" height="9" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{TIPO_RESUMO}.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*UN*/Bundle.getStringApplication("rotulo_unidade_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-130" mode="Opaque" x="346" y="17" width="67" height="9" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{TIPO_RESUMO}.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*preco*/Bundle.getStringApplication("rotulo_preco")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="TipoResumo" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-131" mode="Opaque" x="4" y="17" width="172" height="9" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{TIPO_RESUMO}.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*tipo Resumo*/
$P{TIPO_RESUMO}.equals(LiberacaoReceita.PROP_USUARIO)?
Bundle.getStringApplication("rotulo_usuario") :

$P{TIPO_RESUMO}.equals(LiberacaoReceita.PROP_DATA_USUARIO)?
Bundle.getStringApplication("rotulo_data_liberacao") :

$P{TIPO_RESUMO}.equals(VOUtils.montarPath(LiberacaoReceita.PROP_DISPENSACAO_MEDICAMENTO_ITEM, DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO, DispensacaoMedicamento.PROP_EMPRESA))?
Bundle.getStringApplication("rotulo_empresa_origem") :

$P{TIPO_RESUMO}.equals(LiberacaoReceita.PROP_PRODUTO)?
Bundle.getStringApplication("rotulo_produto") :

$P{TIPO_RESUMO}.equals("subGrupo.id.codigoGrupoProduto")?
Bundle.getStringApplication("rotulo_grupo_produto") :

$P{TIPO_RESUMO}.equals(DispensacaoMedicamento.PROP_PROFISSIONAL)?
Bundle.getStringApplication("rotulo_profissional"):
" "]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="12" splitType="Stretch">
				<textField evaluationTime="Group" evaluationGroup="empresa" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-117" mode="Opaque" x="415" y="3" width="65" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.Double"><![CDATA[$V{SomaTotalEmpresa} == null ? 0.00 : $V{SomaTotalEmpresa}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-3" mode="Opaque" x="415" y="2" width="66" height="1" forecolor="#000000" backcolor="#FFFFFF"/>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-118" mode="Opaque" x="356" y="3" width="55" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*total*/

Bundle.getStringApplication("rotulo_total_unidade") + ": "]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="FORMA_APRESENTACAO" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$P{FORMA_APRESENTACAO}.equals(LiberacaoReceita.PROP_USUARIO)?
$F{codigoOperador}.toString():
$P{FORMA_APRESENTACAO}.equals(LiberacaoReceita.PROP_DATA_USUARIO)?
 Data.formatar($F{dataUsuario}):
$P{FORMA_APRESENTACAO}.equals(VOUtils.montarPath(LiberacaoReceita.PROP_DISPENSACAO_MEDICAMENTO_ITEM, DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO, DispensacaoMedicamento.PROP_EMPRESA))?
$F{codigoEmpresaOrigem}.toString():
$P{FORMA_APRESENTACAO}.equals(LiberacaoReceita.PROP_PRODUTO)?
$F{codigoProduto} :
$P{FORMA_APRESENTACAO}.equals("subGrupo.id.roGrupoProduto")?
$F{codigoGrupoProduto}.toString() + $F{codigoSubGrupoProduto}.toString():
$P{FORMA_APRESENTACAO}.equals(DispensacaoMedicamento.PROP_PROFISSIONAL)?
$F{codigoProfissional}.toString():
$P{FORMA_APRESENTACAO}.equals(LiberacaoReceita.PROP_USUARIO_CADSUS)?
$F{codigoUsuarioCadsus}.toString():
null]]></groupExpression>
		<groupHeader>
			<band height="30" splitType="Stretch">
				<printWhenExpression><![CDATA[!$P{TIPO_RESUMO}.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
				<rectangle radius="5">
					<reportElement key="rectangle-1" mode="Opaque" x="0" y="17" width="535" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</rectangle>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-103" mode="Opaque" x="288" y="19" width="57" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*Quantidade*/Bundle.getStringApplication("rotulo_quantidade_dispensada_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-104" mode="Opaque" x="415" y="19" width="65" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*Valor*/Bundle.getStringApplication("rotulo_total_dispensado_abv")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="FORMA_APRESENTACAO" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-105" mode="Opaque" x="0" y="1" width="535" height="15" forecolor="#000000" backcolor="#CCCCCC"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{FORMA_APRESENTACAO}.equals(LiberacaoReceita.PROP_USUARIO)?
$F{codigoOperador} == null ? Bundle.getStringApplication("rotulo_usuario") + ": " +  Bundle.getStringApplication("rotulo_sem_descricao") :
Bundle.getStringApplication("rotulo_usuario") + ": " + $F{descricaoOperadorFormatado}:

$P{FORMA_APRESENTACAO}.equals(LiberacaoReceita.PROP_DATA_USUARIO)?
Bundle.getStringApplication("rotulo_data_liberacao") + ": " + Data.formatar( $F{dataUsuario}):

$P{FORMA_APRESENTACAO}.equals(VOUtils.montarPath(LiberacaoReceita.PROP_DISPENSACAO_MEDICAMENTO_ITEM, DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO, DispensacaoMedicamento.PROP_EMPRESA))?
$F{codigoEmpresaOrigem}== null ?Bundle.getStringApplication("rotulo_empresa_origem") + ": " +  Bundle.getStringApplication("rotulo_sem_descricao"):
Bundle.getStringApplication("rotulo_empresa_origem") + ": " + $F{descricaoEmpresaOrigemDispensacaoFormatado}:

$P{FORMA_APRESENTACAO}.equals(LiberacaoReceita.PROP_PRODUTO)?
Bundle.getStringApplication("rotulo_produto") + ": " +  $F{descricaoProdutoFormatado}:

$P{FORMA_APRESENTACAO}.equals("subGrupo.id.codigoGrupoProduto")?
Bundle.getStringApplication("rotulo_grupo_produto") + ": " +  br.com.ksisolucoes.vo.entradas.estoque.SubGrupo.getDescricaoReportComGrupo($F{codigoSubGrupoProduto}, $F{descricaoSubGrupoProduto}, $F{codigoGrupoProduto}, $F{descricaoGrupoProduto}):

$P{FORMA_APRESENTACAO}.equals(DispensacaoMedicamento.PROP_PROFISSIONAL)?
$F{codigoProfissional} == null ? Bundle.getStringApplication("rotulo_profissional") + ": " +  Bundle.getStringApplication("rotulo_sem_descricao") :
Bundle.getStringApplication("rotulo_profissional") + ": " +  $F{descricaoProfissionalFormatado}:

$P{FORMA_APRESENTACAO}.equals(LiberacaoReceita.PROP_USUARIO_CADSUS)?
$F{codigoUsuarioCadsus} == null ? Bundle.getStringApplication("rotulo_usuario_cadsus") + ": " +  Bundle.getStringApplication("rotulo_sem_descricao") :
Bundle.getStringApplication("rotulo_usuario_cadsus") + ": " +  $F{descricaoUsuarioCadsusFormatado}:
" "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-109" mode="Opaque" x="271" y="19" width="15" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*UN*/Bundle.getStringApplication("rotulo_unidade_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-111" mode="Opaque" x="346" y="19" width="67" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*preco*/Bundle.getStringApplication("rotulo_preco")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="TipoResumo" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-125" mode="Opaque" x="4" y="19" width="172" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*tipo Resumo*/
$P{TIPO_RESUMO}.equals(LiberacaoReceita.PROP_USUARIO)?
Bundle.getStringApplication("rotulo_usuario") :

$P{TIPO_RESUMO}.equals(LiberacaoReceita.PROP_DATA_USUARIO)?
Bundle.getStringApplication("rotulo_data_liberacao") :

$P{TIPO_RESUMO}.equals(VOUtils.montarPath(LiberacaoReceita.PROP_DISPENSACAO_MEDICAMENTO_ITEM, DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO, DispensacaoMedicamento.PROP_EMPRESA))?
Bundle.getStringApplication("rotulo_empresa_origem") :

$P{TIPO_RESUMO}.equals(LiberacaoReceita.PROP_PRODUTO)?
Bundle.getStringApplication("rotulo_produto") :

$P{TIPO_RESUMO}.equals("subGrupo.id.codigoGrupoProduto")?
Bundle.getStringApplication("rotulo_grupo_produto") :

$P{TIPO_RESUMO}.equals(DispensacaoMedicamento.PROP_PROFISSIONAL)?
Bundle.getStringApplication("rotulo_profissional"):
" "]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="11" splitType="Stretch">
				<printWhenExpression><![CDATA[!$P{TIPO_RESUMO}.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
				<textField evaluationTime="Group" evaluationGroup="FORMA_APRESENTACAO" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-113" mode="Opaque" x="415" y="2" width="65" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.Double"><![CDATA[$V{SomaTotalFA} == null ? 0.00 : $V{SomaTotalFA}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-114" mode="Opaque" x="356" y="2" width="55" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*total*/Bundle.getStringApplication("rotulo_total") + ": "]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-4" mode="Opaque" x="415" y="1" width="66" height="1" forecolor="#000000" backcolor="#FFFFFF"/>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="TipoResumo">
		<groupExpression><![CDATA[$P{TIPO_RESUMO}.equals(LiberacaoReceita.PROP_USUARIO)?
$F{codigoOperador} + $F{unidade}:

$P{TIPO_RESUMO}.equals(LiberacaoReceita.PROP_DATA_USUARIO)?
Data.formatar( $F{dataUsuario}) + $F{unidade}  :

$P{TIPO_RESUMO}.equals(VOUtils.montarPath(LiberacaoReceita.PROP_DISPENSACAO_MEDICAMENTO_ITEM, DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO, DispensacaoMedicamento.PROP_EMPRESA))?
$F{codigoEmpresaOrigem} + $F{unidade}:

$P{TIPO_RESUMO}.equals(LiberacaoReceita.PROP_PRODUTO)?
$F{codigoProduto} :

$P{TIPO_RESUMO}.equals("subGrupo.id.roGrupoProduto")?
$F{codigoSubGrupoProduto} + $F{codigoGrupoProduto} + $F{unidade}:

$P{TIPO_RESUMO}.equals(DispensacaoMedicamento.PROP_PROFISSIONAL)?
$F{codigoProfissional} + $F{unidade} :
null]]></groupExpression>
		<groupHeader>
			<band height="9" splitType="Stretch">
				<textField evaluationTime="Group" evaluationGroup="TipoResumo" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-100" mode="Opaque" x="288" y="0" width="57" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.Double"><![CDATA[$V{somaQuantidade} == null ? 0.00 : $V{somaQuantidade}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="TipoResumo" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-101" mode="Opaque" x="415" y="0" width="65" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.Double"><![CDATA[$V{somaTotal} == null ? 0.00 : $V{somaTotal}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="TipoResumo" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-110" mode="Opaque" x="271" y="0" width="15" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{codigoProduto} == null ?Bundle.getStringApplication("rotulo_sem_descricao") : $F{unidade}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="TipoResumo" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-112" mode="Opaque" x="346" y="0" width="67" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.Double"><![CDATA[($V{somaQuantidade} == null)  ?
 ( ($V{somaTotal} == null ? 0  : $V{somaTotal}) /new Double(1)  ) :
 ($V{somaTotal} == null) ?
 (new Double(0)/$V{somaQuantidade}) :
 ($V{somaTotal}/$V{somaQuantidade})]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-126" mode="Opaque" x="4" y="0" width="265" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{TIPO_RESUMO}.equals(LiberacaoReceita.PROP_USUARIO)?
$F{codigoOperador} == null ? Bundle.getStringApplication("rotulo_sem_descricao") :
$F{descricaoOperadorFormatado}:

$P{TIPO_RESUMO}.equals(LiberacaoReceita.PROP_DATA_USUARIO)?
Data.formatar( $F{dataUsuario}) :

$P{TIPO_RESUMO}.equals(VOUtils.montarPath(LiberacaoReceita.PROP_DISPENSACAO_MEDICAMENTO_ITEM, DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO, DispensacaoMedicamento.PROP_EMPRESA))?
$F{codigoEmpresaOrigem} == null ? Bundle.getStringApplication("rotulo_sem_descricao"):
$F{descricaoEmpresaOrigemDispensacaoFormatado}:

$P{TIPO_RESUMO}.equals(LiberacaoReceita.PROP_PRODUTO)?
$F{descricaoProdutoFormatado}:

$P{TIPO_RESUMO}.equals("subGrupo.id.codigoGrupoProduto")?
br.com.ksisolucoes.vo.entradas.estoque.SubGrupo.getDescricaoReportComGrupo($F{codigoSubGrupoProduto}, $F{descricaoSubGrupoProduto}, $F{codigoGrupoProduto}, $F{descricaoGrupoProduto}):

$P{TIPO_RESUMO}.equals(DispensacaoMedicamento.PROP_PROFISSIONAL)?
$F{codigoProfissional} == null ?  Bundle.getStringApplication("rotulo_sem_descricao") :
$F{descricaoProfissionalFormatado}:
" "]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<group name="GrupoLiberacao">
		<groupExpression><![CDATA[$F{nomeUsuarioDestino}+$F{codigoProduto}]]></groupExpression>
		<groupHeader>
			<band splitType="Stretch"/>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band splitType="Stretch"/>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
