<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="sub_rel_descricao_conduta_ajustada" columnDirection="RTL" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="56eab6e4-5301-4f11-a723-bfb165341f5f">
	<property name="ireport.zoom" value="2.657341500000003"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<field name="ciap" class="br.com.ksisolucoes.vo.prontuario.basico.Ciap"/>
	<field name="cid" class="br.com.ksisolucoes.vo.prontuario.basico.Cid"/>
	<field name="cipe" class="br.com.ksisolucoes.vo.sae.diagnosticoenfermagemsae.DiagnosticoEnfermagemSae"/>
	<field name="dataInicial" class="java.util.Date"/>
	<field name="dataFinal" class="java.util.Date"/>
	<field name="descricao" class="java.lang.String"/>
	<field name="situacaoFormatada" class="java.lang.String"/>
	<detail>
		<band height="29">
			<rectangle radius="5">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="1" y="0" width="554" height="24" uuid="437156ed-10fb-4494-b46a-969dc0918214"/>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement x="344" y="0" width="71" height="12" isPrintWhenDetailOverflows="true" uuid="e504d39d-a4a5-4341-973b-5d453ccd8c52"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_final_abv")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="15" y="0" width="92" height="12" isPrintWhenDetailOverflows="true" uuid="48ee2dd6-f899-4a73-a497-7c1f0d1e698d"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_ciap")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="112" y="0" width="73" height="12" isPrintWhenDetailOverflows="true" uuid="159736af-7698-4c28-a14d-6ee2179f3c0c"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cid")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="185" y="0" width="80" height="12" isPrintWhenDetailOverflows="true" uuid="6872461f-196c-49d5-9407-410080c19bdb"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cipe")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="265" y="0" width="78" height="12" isPrintWhenDetailOverflows="true" uuid="e9994d20-a8b3-4251-a53a-488191b7fc59"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_inicial_abv")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="415" y="0" width="71" height="12" isPrintWhenDetailOverflows="true" uuid="ef3b972c-f365-447f-b554-f37d4489c4b4"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_descricao")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="488" y="0" width="69" height="12" isPrintWhenDetailOverflows="true" uuid="036cd907-500f-4689-af6f-034b7424cb90"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_situacao")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="15" y="12" width="95" height="12" uuid="c60805ac-bb54-48d7-8f81-2f7229a602c6"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ciap}.getTituloOriginal()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="112" y="12" width="72" height="12" uuid="2a99582d-583a-47b5-ba06-1f056b81833a"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cid}.getDescricaoFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="185" y="12" width="80" height="12" uuid="1d14e197-4eb6-4819-9f00-97f22913c9a0"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cipe}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="265" y="12" width="77" height="12" uuid="9a21cdaa-5ad1-456b-90e4-365b13cc303a"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.formatar($F{dataInicial})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="344" y="12" width="70" height="12" uuid="41964d5e-5d78-44a2-b5af-7291ae92680a"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.formatar($F{dataFinal})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="415" y="12" width="71" height="12" uuid="41a671b1-f6a8-4b11-b9b3-0a51900fb949"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="488" y="12" width="66" height="12" uuid="54029ef7-6e70-47a3-885b-a5ed9e8af728"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{situacaoFormatada}]]></textFieldExpression>
			</textField>
		</band>
		<band/>
	</detail>
</jasperReport>
