package br.com.ksisolucoes.report.programasaude.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.report.prontuario.programasaude.interfaces.RelatorioCadastroPreventivoDTO;
import br.com.ksisolucoes.report.prontuario.programasaude.interfaces.RelatorioCadastroPreventivoDTOParam;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.basico.OrgaoEmissor;
import br.com.ksisolucoes.vo.cadsus.*;
import br.com.ksisolucoes.vo.programasaude.Preventivo;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;

import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioCadastroPreventivo extends CommandQuery<QueryRelatorioCadastroPreventivo> implements ITransferDataReport<RelatorioCadastroPreventivoDTOParam, RelatorioCadastroPreventivoDTO> {

    private RelatorioCadastroPreventivoDTOParam param;
    private List<RelatorioCadastroPreventivoDTO> list;

    @Override
    public void setDTOParam(RelatorioCadastroPreventivoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(RelatorioCadastroPreventivoDTO.class.getName());

        hql.addToSelect(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, new HQLProperties(Preventivo.class, "prev").getSingleProperties());
        hql.addToSelect("prev.codigo", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_CODIGO));

        hql.addToSelect("usuarioCadsus.codigo", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO));
        hql.addToSelect("usuarioCadsus.nome", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME));
        hql.addToSelect("usuarioCadsus.apelido", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO));
        hql.addToSelect("usuarioCadsus.utilizaNomeSocial", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL));
        hql.addToSelect("usuarioCadsus.dataNascimento", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_DATA_NASCIMENTO));
        hql.addToSelect("usuarioCadsus.nomeMae", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME_MAE));
        hql.addToSelect("usuarioCadsus.cpf", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CPF));
        hql.addToSelect("usuarioCadsus.rg", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_RG));
        hql.addToSelect("usuarioCadsus.nomePai", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME_PAI));
        hql.addToSelect("usuarioCadsus.telefone", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_TELEFONE));
        hql.addToSelect("usuarioCadsus.celular", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CELULAR));
        hql.addToSelect("usuarioCadsusEsus.nivelEscolaridade", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_USUARIO_CADSUS_ESUS, UsuarioCadsusEsus.PROP_NIVEL_ESCOLARIDADE));

        hql.addToSelect("raca.codigo", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_RACA, Raca.PROP_CODIGO));
        hql.addToSelect("raca.descricao", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_RACA, Raca.PROP_DESCRICAO));

        hql.addToSelect("atendimento.dataAtendimento", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_ATENDIMENTO, Atendimento.PROP_DATA_ATENDIMENTO));
        hql.addToSelect("atendimento.codigo", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_ATENDIMENTO, Atendimento.PROP_CODIGO));

        hql.addToSelect("endereco.logradouro", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_ATENDIMENTO, Atendimento.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_LOGRADOURO));
        hql.addToSelect("endereco.numeroLogradouro", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_ATENDIMENTO, Atendimento.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_NUMERO_LOGRADOURO));
        hql.addToSelect("endereco.complementoLogradouro", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_ATENDIMENTO, Atendimento.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_COMPLEMENTO_LOGRADOURO));
        hql.addToSelect("endereco.bairro", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_ATENDIMENTO, Atendimento.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_BAIRRO));
        hql.addToSelect("endereco.cep", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_ATENDIMENTO, Atendimento.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_CEP));
        hql.addToSelect("endereco.telefone", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_ATENDIMENTO, Atendimento.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_TELEFONE));
        hql.addToSelect("cidade.codigo", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_ATENDIMENTO, Atendimento.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_CIDADE, Cidade.PROP_CODIGO));
        hql.addToSelect("cidade.descricao", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_ATENDIMENTO, Atendimento.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_CIDADE, Cidade.PROP_DESCRICAO));
        hql.addToSelect("estado.codigo", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_ATENDIMENTO, Atendimento.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_CIDADE, Cidade.PROP_ESTADO, Estado.PROP_CODIGO));
        hql.addToSelect("estado.sigla", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_ATENDIMENTO, Atendimento.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_CIDADE, Cidade.PROP_ESTADO, Estado.PROP_SIGLA));

        hql.addToSelect("empresa.codigo", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_ATENDIMENTO, Atendimento.PROP_EMPRESA, Empresa.PROP_CODIGO));
        hql.addToSelect("empresa.referencia", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_ATENDIMENTO, Atendimento.PROP_EMPRESA, Empresa.PROP_REFERENCIA));
        hql.addToSelect("empresa.descricao", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_ATENDIMENTO, Atendimento.PROP_EMPRESA, Empresa.PROP_DESCRICAO));
        hql.addToSelect("empresa.cnes", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_ATENDIMENTO, Atendimento.PROP_EMPRESA, Empresa.PROP_CNES));
        hql.addToSelect("cidadeEmp.codigo", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_ATENDIMENTO, Atendimento.PROP_EMPRESA, Empresa.PROP_CIDADE, Cidade.PROP_CODIGO));
        hql.addToSelect("cidadeEmp.descricao", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_ATENDIMENTO, Atendimento.PROP_EMPRESA, Empresa.PROP_CIDADE, Cidade.PROP_DESCRICAO));
        hql.addToSelect("estadoEmp.codigo", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_ATENDIMENTO, Atendimento.PROP_EMPRESA, Empresa.PROP_CIDADE, Cidade.PROP_ESTADO, Estado.PROP_CODIGO));
        hql.addToSelect("estadoEmp.sigla", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_ATENDIMENTO, Atendimento.PROP_EMPRESA, Empresa.PROP_CIDADE, Cidade.PROP_ESTADO, Estado.PROP_SIGLA));

        hql.addToSelect("profissional.codigo",                          VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_PROFISSIONAL, Profissional.PROP_CODIGO));
        hql.addToSelect("profissional.referencia",                      VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_PROFISSIONAL, Profissional.PROP_REFERENCIA));
        hql.addToSelect("profissional.nome",                            VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_PROFISSIONAL, Profissional.PROP_NOME));
        
        hql.addToSelect("profissional.unidadeFederacaoConselhoRegistro",VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_PROFISSIONAL, Profissional.PROP_UNIDADE_FEDERACAO_CONSELHO_REGISTRO));
        hql.addToSelect("profissional.numeroRegistro",                  VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_PROFISSIONAL, Profissional.PROP_NUMERO_REGISTRO));
        hql.addToSelect("conselhoClasse.sigla",                         VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_PREVENTIVO, Preventivo.PROP_PROFISSIONAL, Profissional.PROP_CONSELHO_CLASSE, OrgaoEmissor.PROP_SIGLA));
        

        hql.addToSelect("(select ucp.numeroProntuario from UsuarioCadsusProntuario ucp where ucp.id.usuarioCadsus.codigo = usuarioCadsus.codigo and ucp.id.empresa.codigo = empresa.codigo)", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_NUMERO_PRONTUARIO));

        hql.addToSelect("(select ucd.numeroDocumento from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = usuarioCadsus and ucd.situacaoExcluido = " + UsuarioCadsusDocumento.STATUS_ATIVO + " and ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_RG + ")", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_NUMERO_DOCUMENTO));
        hql.addToSelect("(select ucd.orgaoEmissor.descricao from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = usuarioCadsus and ucd.situacaoExcluido = " + UsuarioCadsusDocumento.STATUS_ATIVO + " and ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_RG + ")", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_ORGAO_EMISSOR));
        hql.addToSelect("(select ucd.siglaUf from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = usuarioCadsus and ucd.situacaoExcluido = " + UsuarioCadsusDocumento.STATUS_ATIVO + " and ucd.tipoDocumento.codigo = " + TipoDocumentoUsuario.TIPO_DOCUMENTO_RG + ")", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_SIGLA_UF));

        hql.addToSelect("(select min(ucc.numeroCartao) from UsuarioCadsusCns ucc where ucc.usuarioCadsus = usuarioCadsus and ucc.excluido = 0 )", VOUtils.montarPath(RelatorioCadastroPreventivoDTO.PROP_NUMERO_CNS));

        hql.addToFrom("Preventivo prev"
                + " left join prev.profissional profissional"
                + " left join profissional.conselhoClasse conselhoClasse"
                + " left join prev.usuarioCadsus usuarioCadsus"
                + " left join usuarioCadsus.usuarioCadsusEsus usuarioCadsusEsus"
                + " left join usuarioCadsus.raca raca"
                + " left join prev.atendimento atendimento"
                + " left join atendimento.empresa empresa"
                + " left join empresa.cidade cidadeEmp"
                + " left join cidadeEmp.estado estadoEmp"
                + " left join atendimento.enderecoUsuarioCadsus endereco"
                + " left join endereco.cidade cidade"
                + " left join cidade.estado estado"
                + " left join prev.exameRequisicao exameRequisicao"
                + " left join exameRequisicao.exame exame");

        hql.addToWhereWhithAnd("exame.codigo = ", param.getCodigoExame());
        hql.addToWhereWhithAnd("atendimento = ", param.getAtendimento());
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List) result);
    }

    @Override
    public Collection getResult() {
        return list;
    }

}
