package br.com.ksisolucoes.report.agendamento.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.agendamento.dto.RelatorioResumoSolicitacoesDTO;
import br.com.ksisolucoes.report.agendamento.dto.RelatorioResumoSolicitacoesDTOParam;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioResumoSolicitacoes extends CommandQuery<QueryRelatorioResumoSolicitacoes> implements ITransferDataReport<RelatorioResumoSolicitacoesDTOParam, RelatorioResumoSolicitacoesDTO>{

    private RelatorioResumoSolicitacoesDTOParam param;
    private List<RelatorioResumoSolicitacoesDTO> dtoList;

    @Override
    public void setDTOParam(RelatorioResumoSolicitacoesDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(RelatorioResumoSolicitacoesDTO.class.getName());
        hql.setConvertToLeftJoin(true);

        hql.addToSelect("count(sa.codigo)", "totalSolicitacoes");
        hql.addToSelect("sum(extract (day from (sa.dataAgendamento - sa.dataSolicitacao))) / "
                + "(case when sum("
                + "case when sa.status in (" + SolicitacaoAgendamento.STATUS_AGENDADO + "," + SolicitacaoAgendamento.STATUS_REGULACAO_AGENDADO + ") "
                + "then 1 else 0 end) = 0 then 1 else sum("
                + "case when sa.status in (" + SolicitacaoAgendamento.STATUS_AGENDADO + "," + SolicitacaoAgendamento.STATUS_REGULACAO_AGENDADO + ") "
                + "then 1 else 0 end) end)", "diasMediaEspera");
        
        if (!param.getTipoResumo().toString().equals(param.getFormaApresentacao().toString())) {
            if (RelatorioResumoSolicitacoesDTOParam.FormaApresentacao.UNIDADE_SOLICITANTE.equals(this.param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("sa.empresa.codigo", "empresa.codigo");
                hql.addToSelectAndGroupAndOrder("sa.empresa.referencia", "empresa.referencia");
                hql.addToSelectAndGroupAndOrder("sa.empresa.descricao", "empresa.descricao");
            } else if (RelatorioResumoSolicitacoesDTOParam.FormaApresentacao.PROFISSIONAL_SOLICITANTE.equals(this.param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("sa.profissional.codigo", "profissional.codigo");
                hql.addToSelectAndGroupAndOrder("sa.profissional.referencia", "profissional.referencia");
                hql.addToSelectAndGroupAndOrder("sa.profissional.nome", "profissional.nome");
            } else if (RelatorioResumoSolicitacoesDTOParam.FormaApresentacao.TIPO_PROCEDIMENTO.equals(this.param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("sa.tipoProcedimento.codigo", "tipoProcedimento.codigo");
                hql.addToSelectAndGroupAndOrder("sa.tipoProcedimento.descricao", "tipoProcedimento.descricao");
            } else if (RelatorioResumoSolicitacoesDTOParam.FormaApresentacao.CID.equals(this.param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("sa.cid.codigo", "cid.codigo");
                hql.addToSelectAndGroupAndOrder("sa.cid.descricao", "cid.descricao");
            }
        }

        if (RelatorioResumoSolicitacoesDTOParam.TipoResumo.UNIDADE_SOLICITANTE.equals(this.param.getTipoResumo())) {
            hql.addToSelectAndGroup("sa.empresa.codigo","empresa.codigo");
            hql.addToSelectAndGroup("sa.empresa.referencia","empresa.referencia");
            hql.addToSelectAndGroup("sa.empresa.descricao","empresa.descricao");
        } else if (RelatorioResumoSolicitacoesDTOParam.TipoResumo.PROFISSIONAL_SOLICITANTE.equals(this.param.getTipoResumo())) {
            hql.addToSelectAndGroup("sa.profissional.codigo","profissional.codigo");
            hql.addToSelectAndGroup("sa.profissional.referencia","profissional.referencia");
            hql.addToSelectAndGroup("sa.profissional.nome","profissional.nome");
        } else if (RelatorioResumoSolicitacoesDTOParam.TipoResumo.TIPO_PROCEDIMENTO.equals(this.param.getTipoResumo())) {
            hql.addToSelectAndGroup("sa.tipoProcedimento.codigo","tipoProcedimento.codigo");
            hql.addToSelectAndGroup("sa.tipoProcedimento.descricao","tipoProcedimento.descricao");
        } else if (RelatorioResumoSolicitacoesDTOParam.TipoResumo.CID.equals(this.param.getTipoResumo())) {
            hql.addToSelectAndGroup("sa.cid.codigo","cid.codigo");
            hql.addToSelectAndGroup("sa.cid.descricao","cid.descricao");
        }

        hql.addToFrom("SolicitacaoAgendamento sa");

        hql.addToWhereWhithAnd("sa.dataSolicitacao", this.param.getPeriodo());

        hql.addToWhereWhithAnd("sa.tipoProcedimento", this.param.getTipoProcedimento());
        hql.addToWhereWhithAnd("sa.tipoProcedimento.tipoProcedimentoClassificacao", this.param.getTipoProcedimentoClassificacao());

        hql.addToWhereWhithAnd("sa.empresa", this.param.getUnidadeSolicitante());
        hql.addToWhereWhithAnd("sa.profissional", this.param.getProfissionalSolicitante());
        hql.addToWhereWhithAnd("sa.usuarioCadsus", this.param.getPaciente());
        hql.addToWhereWhithAnd("sa.cid", this.param.getCid());

        hql.addToWhereWhithAnd("sa.status not in ",Arrays.asList(SolicitacaoAgendamento.STATUS_REGULACAO_NEGADO, SolicitacaoAgendamento.STATUS_CANCELADO));

        if (this.param.getOrdenacao().equals(RelatorioResumoSolicitacoesDTOParam.Ordenacao.TOTAL_SOLICITACOES)) {
            hql.addToOrder("1 "+param.getTipoOrdenacao().getCommand());
        } else if (this.param.getOrdenacao().equals(RelatorioResumoSolicitacoesDTOParam.Ordenacao.DIAS_MEDIA_ESPERA)) {
            hql.addToOrder("2 "+param.getTipoOrdenacao().getCommand());
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        dtoList = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public Collection getResult() {
        return dtoList;
    }
}
