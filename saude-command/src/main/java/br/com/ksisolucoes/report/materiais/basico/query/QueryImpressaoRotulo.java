package br.com.ksisolucoes.report.materiais.basico.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.materiais.estoque.ImpressaoRotuloDTO;
import br.com.ksisolucoes.report.materiais.estoque.ImpressaoRotuloDTOParam;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.entradas.estoque.CodigoBarrasProduto;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public class QueryImpressaoRotulo extends CommandQuery implements ITransferDataReport<ImpressaoRotuloDTOParam, ImpressaoRotuloDTO> {

    private ImpressaoRotuloDTOParam param;
    private List<ImpressaoRotuloDTO> listRotulo;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.setTypeSelect(ImpressaoRotuloDTO.class.getName());

        hql.addToSelect("codigoBarrasProduto.codigo", VOUtils.montarPath(ImpressaoRotuloDTOParam.PROP_CODIGO_BARRAS, CodigoBarrasProduto.PROP_CODIGO));

        hql.addToFrom("CodigoBarrasProduto codigoBarrasProduto ");

        hql.addToWhereWhithAnd("codigoBarrasProduto.codigo in ", this.param.getLstCodigoBarrasProduto());

        hql.addToOrder("codigoBarrasProduto.codigo");
    }

    public Collection<ImpressaoRotuloDTO> getResult() {
        return this.listRotulo;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        listRotulo = hql.getBeanList((List) result);
    }

    public void setDTOParam(ImpressaoRotuloDTOParam t) {
        this.param = t;
    }

}
