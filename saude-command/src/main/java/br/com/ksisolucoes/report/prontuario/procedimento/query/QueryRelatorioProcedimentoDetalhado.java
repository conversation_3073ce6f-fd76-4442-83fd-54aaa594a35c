package br.com.ksisolucoes.report.prontuario.procedimento.query;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.JoSQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioProcedimentoDTO;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioProcedimentoDetalhadoDTOParam;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.basico.Parametro;
import br.com.ksisolucoes.vo.basico.ParametroAtendimento;
import br.com.ksisolucoes.vo.cadsus.VisitaDomiciliar;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoItem;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import ch.lambdaj.Lambda;
import ch.lambdaj.function.compare.ArgumentComparator;
import org.apache.commons.collections.ComparatorUtils;
import org.hibernate.Session;
import org.josql.Query;
import org.josql.QueryExecutionException;
import org.josql.QueryParseException;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class QueryRelatorioProcedimentoDetalhado extends CommandQuery<QueryRelatorioProcedimentoDetalhado> implements ITransferDataReport<RelatorioProcedimentoDetalhadoDTOParam, RelatorioProcedimentoDTO> {

    private RelatorioProcedimentoDetalhadoDTOParam param;
    private List<RelatorioProcedimentoDTO> result;

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        List<RelatorioProcedimentoDTO> procedimentos = new ArrayList<RelatorioProcedimentoDTO>();

        Date competenciaInicioGeracaoBpaConta = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("competenciaInicioGeracaoBpaConta");

        if (competenciaInicioGeracaoBpaConta == null
                || (param.getPeriodo().getDataInicial() != null && Data.adjustRangeHour(param.getPeriodo().getDataInicial()).getDataInicial().compareTo(Data.adjustRangeHour(competenciaInicioGeracaoBpaConta).getDataInicial()) < 0)
                || (param.getPeriodo().getDataFinal() != null && Data.adjustRangeHour(param.getPeriodo().getDataFinal()).getDataInicial().compareTo(Data.adjustRangeHour(competenciaInicioGeracaoBpaConta).getDataInicial()) < 0)) {
            if (!RelatorioProcedimentoDetalhadoDTOParam.SituacaoProducao.ABERTA.equals(this.param.getSituacaoProducao())) {
                {
                    QueryAtendimento query = new QueryAtendimento(param);
                    query.start();
                    procedimentos.addAll(query.getResult());
                }

                {
                    QueryAtendimentoItem query = new QueryAtendimentoItem(param);
                    query.start();
                    procedimentos.addAll(query.getResult());
                }

                {
                    if (this.param.getUsuariosCadsus() == null && (this.param.getProfissionals() == null || CollectionUtils.isEmpty(this.param.getProfissionals().getValue()))) {
                        QueryAtividadeGrupo query = new QueryAtividadeGrupo(param);
                        query.start();
                        procedimentos.addAll(query.getResult());
                    }
                }

                {
                    QueryVisitaDomiciliar query = new QueryVisitaDomiciliar(param);
                    query.start();
                    procedimentos.addAll(query.getResult());
                }

                {
                    QueryLancamentoBpaConsolidado query = new QueryLancamentoBpaConsolidado(param);
                    query.start();
                    procedimentos.addAll(query.getResult());
                }
            }
            {
                QueryContaPaciente query = new QueryContaPaciente(param);
                query.start();
                procedimentos.addAll(query.getResult());
            }
        } else {
            QueryContaPacienteNew query = new QueryContaPacienteNew(param);
            query.start();
            procedimentos.addAll(query.getResult());
        }
        groupList(procedimentos);

    }

    private void groupList(List<RelatorioProcedimentoDTO> procedimentos) throws DAOException {
        JoSQLHelper helper = new JoSQLHelper();

        if (RepositoryComponentDefault.NAO.equals(this.param.getAgruparUnidade())) {
            helper.addToSelectGetAndSet("empresa.codigo");
            helper.addToSelectGetAndSet("empresa.referencia");
            helper.addToSelectGetAndSet("empresa.descricao");
            helper.addToSelectGetAndSet("empresa.cnes");

            helper.addToGroup("empresa.codigo");
            helper.addToGroup("empresa.referencia");
            helper.addToGroup("empresa.descricao");
            helper.addToGroup("empresa.cnes");
        }

        helper.addToSelectGetAndSet("usuarioCadsus.codigo");
        helper.addToSelectGetAndSet("usuarioCadsus.nome");
        helper.addToSelectGetAndSet("usuarioCadsus.sexo");
        helper.addToSelectGetAndSet("usuarioCadsus.dataNascimento");
        helper.addToSelectGetAndSet("procedimento.codigo");
        helper.addToSelectGetAndSet("procedimento.referencia");
        helper.addToSelectGetAndSet("procedimento.descricao");
        helper.addToSelectGetAndSet("dataAtendimento");
        helper.addToSelectGetAndSet("profissional.codigo");
        helper.addToSelectGetAndSet("profissional.nome");
        helper.addToSelectGetAndSet("empresaSolicitante.codigo");
        helper.addToSelectGetAndSet("empresaSolicitante.descricao");
        helper.addToSelectGetAndSet("sum(quantidade)", "quantidade");
        helper.addToSelectGetAndSet("equipeProfissional.equipe.equipeCnes");
        helper.addToSelectGetAndSet("equipeProfissional.equipe.referencia");

        helper.addToGroup("equipeProfissional.equipe.equipeCnes");
        helper.addToGroup("equipeProfissional.equipe.referencia");
        helper.addToGroup("usuarioCadsus.codigo");
        helper.addToGroup("usuarioCadsus.nome");
        helper.addToGroup("usuarioCadsus.sexo");
        helper.addToGroup("usuarioCadsus.dataNascimento");
        helper.addToGroup("procedimento.codigo");
        helper.addToGroup("procedimento.referencia");
        helper.addToGroup("procedimento.descricao");
        helper.addToGroup("dataAtendimento");
        helper.addToGroup("profissional.codigo");
        helper.addToGroup("profissional.nome");

        Long tipoOrdenacao = RelatorioProcedimentoDetalhadoDTOParam.TipoOrdenacao.CRESCENTE.equals(param.getTipoOrdenacao()) ? JoSQLHelper.CRESCENTE : JoSQLHelper.DECRESCENTE;
        helper.setOrderType(tipoOrdenacao);

        if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.PROFISSIONAL.equals((this.param.getFormaApresentacao()))) {
            helper.addToSelectGetAndSet("profissional.referencia");
            helper.addToSelectGetAndSet("profissional.codigoCns");

            helper.addToSelectGetAndSet("equipeProfissional.equipe.equipeCnes");
            helper.addToSelectGetAndSet("equipeProfissional.equipe.referencia");

            helper.addToGroup("equipeProfissional.equipe.equipeCnes");
            helper.addToGroup("equipeProfissional.equipe.referencia");

            helper.addToGroup("profissional.referencia");
            helper.addToGroup("profissional.codigoCns");

            helper.addToSelectGetAndSet("empresa.codigo");
            helper.addToSelectGetAndSet("empresa.referencia");
            helper.addToSelectGetAndSet("empresa.descricao");
            helper.addToSelectGetAndSet("empresa.cnes");

            helper.addToGroup("empresa.codigo");
            helper.addToGroup("empresa.referencia");
            helper.addToGroup("empresa.descricao");
            helper.addToGroup("empresa.cnes");
        } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.CBO.equals((this.param.getFormaApresentacao()))) {
            helper.addToSelectGetAndSet("tabelaCbo.cbo");
            helper.addToSelectGetAndSet("tabelaCbo.descricao");
            helper.addToGroup("tabelaCbo.descricao");
            helper.addToGroup("tabelaCbo.cbo");
        } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.CONVENIO.equals((this.param.getFormaApresentacao()))) {
            helper.addToSelectGetAndSet("convenio.codigo");
            helper.addToSelectGetAndSet("convenio.descricao");
            helper.addToGroup("convenio.descricao");
            helper.addToGroup("convenio.codigo");
        } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.MUNICIPIO.equals((this.param.getFormaApresentacao()))) {
            helper.addToSelectGetAndSet("cidade.codigo");
            helper.addToSelectGetAndSet("cidade.descricao");

            helper.addToGroup("cidade.codigo");
            helper.addToGroup("cidade.descricao");
        } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.UNIDADE_ORIGEM.equals((this.param.getFormaApresentacao()))) {
            helper.addToSelectGetAndSet("empresaSolicitante.codigo");
            helper.addToSelectGetAndSet("empresaSolicitante.descricao");

            helper.addToGroup("empresaSolicitante.codigo");
            helper.addToGroup("empresaSolicitante.descricao");
        }


        helper.addToFrom(RelatorioProcedimentoDTO.class.getName());
        helper.setTypeSelectGettersAndSetters(RelatorioProcedimentoDTO.class.getName());

        try {
            Query query = new Query();

            query.parse(helper.getQuery());
            result = helper.getGroupByResults(query.execute(procedimentos));

            RelatorioProcedimentoDTO proxy = on(RelatorioProcedimentoDTO.class);
            Collection<Comparator> comparatorList = new ArrayList<Comparator>();

            if (RepositoryComponentDefault.NAO.equals(this.param.getAgruparUnidade())) {
                comparatorList.add(getComparatorByProxy(proxy.getEmpresa().getDescricao()));
            }

            if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.PROFISSIONAL.equals((this.param.getFormaApresentacao()))) {
                comparatorList.add(getComparatorByProxy(proxy.getProfissional().getNome()));
            } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.CBO.equals((this.param.getFormaApresentacao()))) {
                comparatorList.add(getComparatorByProxy(proxy.getTabelaCbo().getDescricao()));
            } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.CONVENIO.equals((this.param.getFormaApresentacao()))) {
                comparatorList.add(getComparatorByProxy(proxy.getConvenio().getDescricao()));
            } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.PACIENTE.equals((this.param.getFormaApresentacao()))) {
                comparatorList.add(getComparatorByProxy(proxy.getUsuarioCadsus().getNome()));
            } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.PROCEDIMENTO.equals((this.param.getFormaApresentacao()))) {
                comparatorList.add(getComparatorByProxy(proxy.getProcedimento().getDescricao()));
            } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.MUNICIPIO.equals((this.param.getFormaApresentacao()))) {
                comparatorList.add(getComparatorByProxy(proxy.getCidade().getDescricao()));
            }else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.UNIDADE_ORIGEM.equals((this.param.getFormaApresentacao()))) {
                comparatorList.add(getComparatorByProxy(proxy.getEmpresaSolicitante().getDescricao()));
            }
            if (RelatorioProcedimentoDetalhadoDTOParam.Ordenacao.PROFISSIONAL.equals(param.getOrdenacao())
                    && !RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.PROFISSIONAL.equals(param.getFormaApresentacao())) {
                comparatorList.add(getComparatorByProxy(proxy.getProfissional().getNome(), false));
            } else if (RelatorioProcedimentoDetalhadoDTOParam.Ordenacao.PACIENTE.equals(param.getOrdenacao())
                    && !RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.PACIENTE.equals(param.getFormaApresentacao())) {
                comparatorList.add(getComparatorByProxy(proxy.getUsuarioCadsus().getNome(), false));
            } else if (RelatorioProcedimentoDetalhadoDTOParam.Ordenacao.PROCEDIMENTO.equals(param.getOrdenacao())
                    && !RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.PROCEDIMENTO.equals((param.getFormaApresentacao()))) {
                comparatorList.add(getComparatorByProxy(proxy.getProcedimento().getDescricao(), false));
            } else if (RelatorioProcedimentoDetalhadoDTOParam.Ordenacao.DATA.equals(param.getOrdenacao())) {
                comparatorList.add(getComparatorByProxy(proxy.getDataAtendimento(), false));
            }

            if (CollectionUtils.isNotNullEmpty(comparatorList)) {
                result = Lambda.sort(result, proxy, ComparatorUtils.chainedComparator(comparatorList));
            }
        } catch (QueryParseException ex) {
            throw new DAOException(ex);
        } catch (QueryExecutionException ex) {
            throw new DAOException(ex);
        }
    }

    private Comparator getComparatorByProxy(Object argumentProxy) {
        return getComparatorByProxy(argumentProxy, true);
    }

    private Comparator getComparatorByProxy(Object argumentProxy, boolean ignoreOrderType) {
        Comparator comparator = ComparatorUtils.nullLowComparator(new ArgumentComparator(argumentProxy));

        if (!ignoreOrderType && RelatorioProcedimentoDetalhadoDTOParam.TipoOrdenacao.DECRESCENTE.equals(param.getTipoOrdenacao())) {
            comparator = ComparatorUtils.reversedComparator(comparator);
        }

        return comparator;
    }

    private static class QueryContaPacienteNew extends CommandQuery {

        private RelatorioProcedimentoDetalhadoDTOParam param;
        private List<RelatorioProcedimentoDTO> dtos;

        public QueryContaPacienteNew(RelatorioProcedimentoDetalhadoDTOParam param) {
            this.param = param;
        }

        @Override
        protected void createQuery(HQLHelper hql) {
            hql.setTypeSelect(RelatorioProcedimentoDTO.class.getName());

            if (RepositoryComponentDefault.NAO.equals(this.param.getAgruparUnidade())) {
                hql.addToSelect("emp.codigo", "empresa.codigo");
                hql.addToSelect("emp.referencia", "empresa.referencia");
                hql.addToSelect("emp.descricao", "empresa.descricao");
                hql.addToSelect("emp.cnes", "empresa.cnes");
            }

            hql.addToSelect("icp.quantidade", "quantidade");
            hql.addToSelect("cp.dataGeracao", "dataAtendimento");
            hql.addToSelect("usuarioCadsus.codigo", "usuarioCadsus.codigo");
            hql.addToSelect("usuarioCadsus.nome", "usuarioCadsus.nome");
            hql.addToSelect("usuarioCadsus.sexo", "usuarioCadsus.sexo");
            hql.addToSelect("usuarioCadsus.dataNascimento", "usuarioCadsus.dataNascimento");
            hql.addToSelect("profissional.codigo", "profissional.codigo");
            hql.addToSelect("profissional.nome", "profissional.nome");
            hql.addToSelect("procedimento.codigo", "procedimento.codigo");
            hql.addToSelect("procedimento.referencia", "procedimento.referencia");
            hql.addToSelect("procedimento.descricao", "procedimento.descricao");

            hql.addToSelect("empresaSolicitante.codigo", "empresaSolicitante.codigo");
            hql.addToSelect("empresaSolicitante.descricao", "empresaSolicitante.descricao");

            hql.addToSelect("emp.codigo", "empresa.codigo");
            hql.addToSelect("emp.referencia", "empresa.referencia");
            hql.addToSelect("emp.descricao", "empresa.descricao");
            hql.addToSelect("emp.cnes", "empresa.cnes");

            if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.PROFISSIONAL.equals((this.param.getFormaApresentacao()))) {
                hql.addToSelect("(select min (e.equipeCnes) FROM EquipeProfissional ep JOIN ep.equipe e WHERE ep.profissional = profissional and ep.status = :statusAtivo)", "equipeProfissional.equipe.equipeCnes");
                hql.addToSelect("profissional.referencia", "profissional.referencia");
                hql.addToSelect("profissional.codigoCns", "profissional.codigoCns");
                hql.addToSelect("profissional.codigo", "profissional.codigo");
                hql.addToSelect("profissional.nome", "profissional.nome");
                hql.addToSelect("emp.codigo", "empresa.codigo");
            } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.CBO.equals((this.param.getFormaApresentacao()))) {
                hql.addToSelect("cbo.cbo", "tabelaCbo.cbo");
                hql.addToSelect("cbo.descricao", "tabelaCbo.descricao");
            } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.CONVENIO.equals((this.param.getFormaApresentacao()))) {
                hql.addToSelect("cp.convenio.codigo", "convenio.codigo");
                hql.addToSelect("cp.convenio.descricao", "convenio.descricao");
            } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.MUNICIPIO.equals((this.param.getFormaApresentacao()))) {
                hql.addToSelect("cidade.codigo", "cidade.codigo");
                hql.addToSelect("cidade.descricao", "cidade.descricao");
            } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.UNIDADE_ORIGEM.equals((this.param.getFormaApresentacao()))) {
                hql.addToSelect("empresaSolicitante.codigo", "empresaSolicitante.codigo");
                hql.addToSelect("empresaSolicitante.descricao", "empresaSolicitante.descricao");
            }

            hql.addToFrom(ItemContaPaciente.class.getName(), "icp"
                    + " left join icp.procedimento procedimento"
                    + " left join icp.profissional profissional"
                    + " left join icp.cbo cbo"
                    + " left join icp.contaPaciente cp"
                    + " left join cp.usuarioCadsus usuarioCadsus"
                    + " left join cp.empresa emp"
                    + " left join cp.enderecoUsuarioCadsus enderecoUsuarioCadsus "
                    + " left join enderecoUsuarioCadsus.cidade cidade"
                    + " left join cp.atendimentoInformacao ai"
                    + " left join ai.atendimentoPrincipal ap"
                    + " left join ap.empresaSolicitante empresaSolicitante"
            );

            hql.addToWhereWhithAnd("cp.dataGeracao ", param.getPeriodo());
            hql.addToWhereWhithAnd("profissional ", this.param.getProfissionals());
            hql.addToWhereWhithAnd("emp ", this.param.getEmpresas());
            hql.addToWhereWhithAnd("empresaSolicitante = ", this.param.getEmpresaSolicitante());
            hql.addToWhereWhithAnd("usuarioCadsus = ", this.param.getUsuariosCadsus());
            hql.addToWhereWhithAnd("procedimento ", this.param.getProcedimento());
            hql.addToWhereWhithAnd("cp.dataGeracao is not null");
            hql.addToWhereWhithAnd("cp.convenio ", this.param.getConvenio());

            if (RelatorioProcedimentoDetalhadoDTOParam.SituacaoProducao.FECHADA.equals(this.param.getSituacaoProducao())) {
                hql.addToWhereWhithAnd("cp.status = " + ContaPaciente.Status.FECHADA.value());
                hql.addToWhereWhithAnd("icp.status = " + ItemContaPaciente.Status.CONFIRMADO.value());

            } else if (RelatorioProcedimentoDetalhadoDTOParam.SituacaoProducao.ABERTA.equals(this.param.getSituacaoProducao())) {
                hql.addToWhereWhithAnd("cp.status = " + ContaPaciente.Status.ABERTA.value());
                hql.addToWhereWhithAnd("icp.status in (" + Arrays.asList(ItemContaPaciente.Status.CONFIRMADO.value(), ItemContaPaciente.Status.ABERTO.value()).toString().replaceAll("\\[", "").replaceAll("\\]", "") + ")");

            } else {
                hql.addToWhereWhithAnd("cp.status in (" + Arrays.asList(ContaPaciente.Status.ABERTA.value(), ContaPaciente.Status.FECHADA.value()).toString().replaceAll("\\[", "").replaceAll("\\]", "") + ")");
                hql.addToWhereWhithAnd("icp.status in (" + Arrays.asList(ItemContaPaciente.Status.CONFIRMADO.value(), ItemContaPaciente.Status.ABERTO.value()).toString().replaceAll("\\[", "").replaceAll("\\]", "") + ")");

            }

//            hql.addToWhereWhithAnd("icp.origemLancamento = ", ItemContaPaciente.OrigemLancamento.FECHAMENTO_CONTA.value());
            hql.addToWhereWhithAnd("icp.tipo = ", ItemContaPaciente.Tipo.PROCEDIMENTO.value());
            hql.addToWhereWhithAnd("cidade = ", this.param.getCidade());

            hql.addToWhereWhithAnd("cbo ", this.param.getTabelasCbo());

            if (this.param.getTabelaCboSubGrupo() != null) {
                hql.addToWhereWhithAnd("cbo.tabelaCboSubGrupo.id.tabelaCboGrupo.codigo = ", this.param.getTabelaCboSubGrupo().getId().getTabelaCboGrupo().getCodigo());
                if (this.param.getTabelaCboSubGrupo().getId().getCodigo() != null) {
                    hql.addToWhereWhithAnd("cbo.tabelaCboSubGrupo.id.codigo = ", this.param.getTabelaCboSubGrupo().getId().getCodigo());
                }
            }

            if (this.param.getTipoProcedimento() != null) {
                hql.addToWhereWhithAnd("coalesce(procedimento.flagFaturavel, 'S') = ", this.param.getTipoProcedimento());
            }
        }

        @Override
        protected void result(HQLHelper hql, Object result) {
            this.dtos = hql.getBeanList((List<Map<String, Object>>) result);
        }

        @Override
        public List<RelatorioProcedimentoDTO> getResult() {
            return this.dtos;
        }

        @Override
        protected void setParameters(HQLHelper hql, org.hibernate.Query query) throws ValidacaoException, DAOException {
            if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.PROFISSIONAL.equals((this.param.getFormaApresentacao()))) {
                query.setParameter("statusAtivo", EquipeProfissional.STATUS_ATIVO);
            }
        }
    }

    private static class QueryAtendimento extends CommandQuery {

        private RelatorioProcedimentoDetalhadoDTOParam param;
        private List<RelatorioProcedimentoDTO> dtos;

        public QueryAtendimento(RelatorioProcedimentoDetalhadoDTOParam param) {
            this.param = param;
        }

        @Override
        protected void createQuery(HQLHelper hql) {
            hql.setTypeSelect(RelatorioProcedimentoDTO.class.getName());

            if (RepositoryComponentDefault.NAO.equals(this.param.getAgruparUnidade())) {
                hql.addToSelectAndGroup("a.empresa.codigo", "empresa.codigo");
                hql.addToSelectAndGroup("a.empresa.referencia", "empresa.referencia");
                hql.addToSelectAndGroup("a.empresa.descricao", "empresa.descricao");
                hql.addToSelectAndGroup("a.empresa.cnes", "empresa.cnes");
            }

            hql.addToSelect("count(a.profissional.codigo)", "quantidadeCount");
            hql.addToSelectAndGroup("a.dataAtendimento", "dataAtendimento");
            hql.addToSelectAndGroup("a.usuarioCadsus.codigo", "usuarioCadsus.codigo");
            hql.addToSelectAndGroup("a.usuarioCadsus.nome", "usuarioCadsus.nome");
            hql.addToSelectAndGroup("a.usuarioCadsus.sexo", "usuarioCadsus.sexo");
            hql.addToSelectAndGroup("a.usuarioCadsus.dataNascimento", "usuarioCadsus.dataNascimento");
            hql.addToSelectAndGroup("profissional.codigo", "profissional.codigo");
            hql.addToSelectAndGroup("profissional.nome", "profissional.nome");
            hql.addToSelectAndGroup("procedimento.codigo", "procedimento.codigo");
            hql.addToSelectAndGroup("procedimento.referencia", "procedimento.referencia");
            hql.addToSelectAndGroup("procedimento.descricao", "procedimento.descricao");
            hql.addToSelectAndGroup("empresaSolicitante.codigo", "empresaSolicitante.codigo");
            hql.addToSelectAndGroup("empresaSolicitante.descricao", "empresaSolicitante.descricao");

            if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.PROFISSIONAL.equals((this.param.getFormaApresentacao()))) {
                hql.addToSelect("(select e.equipeCnes FROM EquipeProfissional ep JOIN ep.equipe e WHERE ep.profissional = profissional and ep.status = :statusAtivo)", "equipeProfissional.equipe.equipeCnes");
                hql.addToSelect("profissional.referencia", "profissional.referencia");
                hql.addToSelect("profissional.codigoCns", "profissional.codigoCns");
                hql.addToSelect("profissional.codigo", "profissional.codigo");
                hql.addToSelect("profissional.nome", "profissional.nome");
                hql.addToSelect("emp.codigo", "empresa.codigo");
            } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.CBO.equals((this.param.getFormaApresentacao()))) {
                hql.addToSelectAndGroup("cbo.cbo", "tabelaCbo.cbo");
                hql.addToSelectAndGroup("cbo.descricao", "tabelaCbo.descricao");
            } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.CONVENIO.equals((this.param.getFormaApresentacao()))) {
                hql.addToSelectAndGroup("a.convenio.codigo", "convenio.codigo");
                hql.addToSelectAndGroup("a.convenio.descricao", "convenio.descricao");
            } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.MUNICIPIO.equals((this.param.getFormaApresentacao()))) {
                hql.addToSelectAndGroup("cidade.codigo", "cidade.codigo");
                hql.addToSelectAndGroup("cidade.descricao", "cidade.descricao");
            } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.UNIDADE_ORIGEM.equals(this.param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("empresaSolicitante.codigo", "empresaSolicitante.codigo");
                hql.addToSelectAndGroupAndOrder("empresaSolicitante.descricao", "empresaSolicitante.descricao");
            }

            hql.addToFrom(Atendimento.class.getName(), "a "
                    + " left join a.profissional profissional"
                    + " left join a.procedimentoCompetencia procedimentoCompetencia "
                    + " left join procedimentoCompetencia.id.procedimento procedimento"
                    + " left join a.tabelaCbo cbo"
                    + " left join a.enderecoUsuarioCadsus enderecoUsuarioCadsus"
                    + " left join enderecoUsuarioCadsus.cidade cidade"
                    + " left join a.empresaSolicitante empresaSolicitante"
            );

            //Nao usa o STATUS_FECHADO_SEM_ATENDIMENTO pois os procedimentos devem vir da conta do paciente
            hql.addToWhereWhithAnd("a.status in", Arrays.asList(Atendimento.STATUS_FINALIZADO));

            hql.addToWhereWhithAnd("a.dataAtendimento ", Data.adjustRangeHour(param.getPeriodo()));
            hql.addToWhereWhithAnd("profissional ", this.param.getProfissionals());
            hql.addToWhereWhithAnd("a.empresa ", this.param.getEmpresas());
            hql.addToWhereWhithAnd("empresaSolicitante = ", this.param.getEmpresaSolicitante());
            hql.addToWhereWhithAnd("a.usuarioCadsus = ", this.param.getUsuariosCadsus());
            hql.addToWhereWhithAnd("procedimento ", this.param.getProcedimento());
            hql.addToWhereWhithAnd("a.dataFechamento is not null");
            hql.addToWhereWhithAnd("a.convenio ", this.param.getConvenio());
            hql.addToWhereWhithAnd("cidade = ", this.param.getCidade());

            hql.addToWhereWhithAnd("cbo ", this.param.getTabelasCbo());

            if (this.param.getTabelaCboSubGrupo() != null) {
                hql.addToWhereWhithAnd("cbo.tabelaCboSubGrupo.id.tabelaCboGrupo.codigo = ", this.param.getTabelaCboSubGrupo().getId().getTabelaCboGrupo().getCodigo());
                if (this.param.getTabelaCboSubGrupo().getId().getCodigo() != null) {
                    hql.addToWhereWhithAnd("cbo.tabelaCboSubGrupo.id.codigo = ", this.param.getTabelaCboSubGrupo().getId().getCodigo());
                }
            }

            if (this.param.getTipoProcedimento() != null) {
                hql.addToWhereWhithAnd("coalesce(procedimento.flagFaturavel, 'S') = ", this.param.getTipoProcedimento());
            }

        }

        @Override
        protected void result(HQLHelper hql, Object result) {
            this.dtos = hql.getBeanList((List<Map<String, Object>>) result);
        }

        @Override
        public List<RelatorioProcedimentoDTO> getResult() {
            return this.dtos;
        }

        @Override
        protected void setParameters(HQLHelper hql, org.hibernate.Query query) throws ValidacaoException, DAOException {
            if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.PROFISSIONAL.equals((this.param.getFormaApresentacao()))) {
                query.setParameter("statusAtivo", EquipeProfissional.STATUS_ATIVO);
            }

        }

    }

    private static class QueryAtendimentoItem extends CommandQuery {

        private RelatorioProcedimentoDetalhadoDTOParam param;
        private List<RelatorioProcedimentoDTO> dtos;

        public QueryAtendimentoItem(RelatorioProcedimentoDetalhadoDTOParam param) {
            this.param = param;
        }

        @Override
        protected void createQuery(HQLHelper hql) {
            hql.setTypeSelect(RelatorioProcedimentoDTO.class.getName());

            if (RepositoryComponentDefault.NAO.equals(this.param.getAgruparUnidade())) {
                hql.addToSelect("a.empresa.codigo", "empresa.codigo");
                hql.addToSelect("a.empresa.referencia", "empresa.referencia");
                hql.addToSelect("a.empresa.descricao", "empresa.descricao");
                hql.addToSelect("a.empresa.cnes", "empresa.cnes");
            }

            hql.addToSelectAndGroup("pei.quantidade", "quantidade");
            hql.addToSelectAndGroup("a.dataAtendimento", "dataAtendimento");
            hql.addToSelectAndGroup("a.usuarioCadsus.codigo", "usuarioCadsus.codigo");
            hql.addToSelectAndGroup("a.usuarioCadsus.nome", "usuarioCadsus.nome");
            hql.addToSelectAndGroup("a.usuarioCadsus.sexo", "usuarioCadsus.sexo");
            hql.addToSelectAndGroup("a.usuarioCadsus.dataNascimento", "usuarioCadsus.dataNascimento");
            hql.addToSelectAndGroup("profissional.codigo", "profissional.codigo");
            hql.addToSelectAndGroup("profissional.nome", "profissional.nome");
            hql.addToSelectAndGroup("procedimento.codigo", "procedimento.codigo");
            hql.addToSelectAndGroup("procedimento.referencia", "procedimento.referencia");
            hql.addToSelectAndGroup("procedimento.descricao", "procedimento.descricao");
            hql.addToSelectAndGroup("empresaSolicitante.codigo", "empresaSolicitante.codigo");
            hql.addToSelectAndGroup("empresaSolicitante.descricao", "empresaSolicitante.descricao");

            if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.PROFISSIONAL.equals((this.param.getFormaApresentacao()))) {
                hql.addToSelect("(select e.equipeCnes FROM EquipeProfissional ep JOIN ep.equipe e WHERE ep.profissional = profissional and ep.status = :statusAtivo)", "equipeProfissional.equipe.equipeCnes");
                hql.addToSelect("profissional.referencia", "profissional.referencia");
                hql.addToSelect("profissional.codigoCns", "profissional.codigoCns");
                hql.addToSelect("profissional.codigo", "profissional.codigo");
                hql.addToSelect("profissional.nome", "profissional.nome");
                hql.addToSelect("emp.codigo", "empresa.codigo");
            } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.CBO.equals((this.param.getFormaApresentacao()))) {
                hql.addToSelect("cbo.cbo", "tabelaCbo.cbo");
                hql.addToSelect("cbo.descricao", "tabelaCbo.descricao");
            } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.CONVENIO.equals((this.param.getFormaApresentacao()))) {
                hql.addToSelect("a.convenio.codigo", "convenio.codigo");
                hql.addToSelect("a.convenio.descricao", "convenio.descricao");
            } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.MUNICIPIO.equals((this.param.getFormaApresentacao()))) {
                hql.addToSelect("cidade.codigo", "cidade.codigo");
                hql.addToSelect("cidade.descricao", "cidade.descricao");
            } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.UNIDADE_ORIGEM.equals(this.param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("empresaSolicitante.codigo", "empresaSolicitante.codigo");
                hql.addToSelectAndGroupAndOrder("empresaSolicitante.descricao", "empresaSolicitante.descricao");
            }

            hql.addToFrom(AtendimentoItem.class.getName(), "pei "
                    + " left join pei.profissional profissional"
                    + " left join pei.procedimentoCompetencia procedimentoCompetencia "
                    + " left join procedimentoCompetencia.id.procedimento procedimento"
                    + " left join pei.atendimento a"
                    + " left join a.tabelaCbo cbo"
                    + " left join a.enderecoUsuarioCadsus enderecoUsuarioCadsus"
                    + " left join enderecoUsuarioCadsus.cidade cidade"
                    + " left join a.empresaSolicitante empresaSolicitante"
            );

            hql.addToWhereWhithAnd("pei.status <> ", AtendimentoItem.STATUS_CANCELADO);
            hql.addToWhereWhithAnd("a.dataAtendimento ", Data.adjustRangeHour(param.getPeriodo()));
            hql.addToWhereWhithAnd("profissional ", this.param.getProfissionals());
            hql.addToWhereWhithAnd("a.empresa ", this.param.getEmpresas());
            hql.addToWhereWhithAnd("empresaSolicitante = ", this.param.getEmpresaSolicitante());
            hql.addToWhereWhithAnd("a.usuarioCadsus = ", this.param.getUsuariosCadsus());
            hql.addToWhereWhithAnd("procedimento ", this.param.getProcedimento());
            hql.addToWhereWhithAnd("a.dataFechamento is not null");
            hql.addToWhereWhithAnd("a.convenio ", this.param.getConvenio());
            hql.addToWhereWhithAnd("cidade = ", this.param.getCidade());

            hql.addToWhereWhithAnd("cbo ", this.param.getTabelasCbo());

            //Nao usa o STATUS_FECHADO_SEM_ATENDIMENTO pois os procedimentos devem vir da conta do paciente
            hql.addToWhereWhithAnd("a.status in", Arrays.asList(Atendimento.STATUS_FINALIZADO));

            if (this.param.getTabelaCboSubGrupo() != null) {
                hql.addToWhereWhithAnd("cbo.tabelaCboSubGrupo.id.tabelaCboGrupo.codigo = ", this.param.getTabelaCboSubGrupo().getId().getTabelaCboGrupo().getCodigo());
                if (this.param.getTabelaCboSubGrupo().getId().getCodigo() != null) {
                    hql.addToWhereWhithAnd("cbo.tabelaCboSubGrupo.id.codigo = ", this.param.getTabelaCboSubGrupo().getId().getCodigo());
                }
            }

            if (this.param.getTipoProcedimento() != null) {
                hql.addToWhereWhithAnd("coalesce(procedimento.flagFaturavel, 'S') = ", this.param.getTipoProcedimento());
            }

        }

        @Override
        protected void result(HQLHelper hql, Object result) {
            this.dtos = hql.getBeanList((List<Map<String, Object>>) result);
        }

        @Override
        public List<RelatorioProcedimentoDTO> getResult() {
            return this.dtos;
        }

        @Override
        protected void setParameters(HQLHelper hql, org.hibernate.Query query) throws ValidacaoException, DAOException {
            if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.PROFISSIONAL.equals((this.param.getFormaApresentacao()))) {
                query.setParameter("statusAtivo", EquipeProfissional.STATUS_ATIVO);
            }
        }
    }

    private static class QueryContaPaciente extends CommandQuery {

        private RelatorioProcedimentoDetalhadoDTOParam param;
        private List<RelatorioProcedimentoDTO> dtos;

        public QueryContaPaciente(RelatorioProcedimentoDetalhadoDTOParam param) {
            this.param = param;
        }

        @Override
        protected void createQuery(HQLHelper hql) {
            hql.setTypeSelect(RelatorioProcedimentoDTO.class.getName());

            if (RepositoryComponentDefault.NAO.equals(this.param.getAgruparUnidade())) {
                hql.addToSelect("emp.codigo", "empresa.codigo");
                hql.addToSelect("emp.referencia", "empresa.referencia");
                hql.addToSelect("emp.descricao", "empresa.descricao");
                hql.addToSelect("emp.cnes", "empresa.cnes");
            }

            hql.addToSelect("icp.quantidade", "quantidade");
            hql.addToSelect("cp.dataGeracao", "dataAtendimento");
            hql.addToSelect("usuarioCadsus.codigo", "usuarioCadsus.codigo");
            hql.addToSelect("usuarioCadsus.nome", "usuarioCadsus.nome");
            hql.addToSelect("usuarioCadsus.sexo", "usuarioCadsus.sexo");
            hql.addToSelect("usuarioCadsus.dataNascimento", "usuarioCadsus.dataNascimento");
            hql.addToSelect("profissional.codigo", "profissional.codigo");
            hql.addToSelect("profissional.nome", "profissional.nome");
            hql.addToSelect("procedimento.codigo", "procedimento.codigo");
            hql.addToSelect("procedimento.referencia", "procedimento.referencia");
            hql.addToSelect("procedimento.descricao", "procedimento.descricao");
            hql.addToSelect("empresaSolicitante.codigo", "empresaSolicitante.codigo");
            hql.addToSelect("empresaSolicitante.descricao", "empresaSolicitante.descricao");

            if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.PROFISSIONAL.equals((this.param.getFormaApresentacao()))) {
                hql.addToSelect("(select e.equipeCnes FROM EquipeProfissional ep JOIN ep.equipe e WHERE ep.profissional = profissional and ep.status = :statusAtivo)", "equipeProfissional.equipe.equipeCnes");
                hql.addToSelect("profissional.referencia", "profissional.referencia");
                hql.addToSelect("profissional.codigoCns", "profissional.codigoCns");
                hql.addToSelect("profissional.codigo", "profissional.codigo");
                hql.addToSelect("profissional.nome", "profissional.nome");
                hql.addToSelect("emp.codigo", "empresa.codigo");
            } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.CBO.equals((this.param.getFormaApresentacao()))) {
                hql.addToSelect("cbo.cbo", "tabelaCbo.cbo");
                hql.addToSelect("cbo.descricao", "tabelaCbo.descricao");
            } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.CONVENIO.equals((this.param.getFormaApresentacao()))) {
                hql.addToSelect("cp.convenio.codigo", "convenio.codigo");
                hql.addToSelect("cp.convenio.descricao", "convenio.descricao");
            } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.MUNICIPIO.equals((this.param.getFormaApresentacao()))) {
                hql.addToSelect("cidade.codigo", "cidade.codigo");
                hql.addToSelect("cidade.descricao", "cidade.descricao");
            } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.UNIDADE_ORIGEM.equals((this.param.getFormaApresentacao()))) {
                hql.addToSelect("empresaSolicitante.codigo", "empresaSolicitante.codigo");
                hql.addToSelect("empresaSolicitante.descricao", "empresaSolicitante.descricao");
            }

            hql.addToFrom(ItemContaPaciente.class.getName(), "icp"
                    + " left join icp.procedimento procedimento"
                    + " left join icp.profissional profissional"
                    + " left join icp.cbo cbo"
                    + " left join icp.contaPaciente cp"
                    + " left join cp.usuarioCadsus usuarioCadsus"
                    + " left join cp.empresa emp"
                    + " left join cp.enderecoUsuarioCadsus enderecoUsuarioCadsus "
                    + " left join enderecoUsuarioCadsus.cidade cidade"
                    + " left join cp.atendimentoInformacao ai"
                    + " left join ai.atendimentoPrincipal ap"
                    + " left join ap.empresaSolicitante empresaSolicitante"
            );

            hql.addToWhereWhithAnd("cp.dataGeracao ", param.getPeriodo());
            hql.addToWhereWhithAnd("profissional ", this.param.getProfissionals());
            hql.addToWhereWhithAnd("emp ", this.param.getEmpresas());
            hql.addToWhereWhithAnd("empresaSolicitante = ", this.param.getEmpresaSolicitante());
            hql.addToWhereWhithAnd("usuarioCadsus = ", this.param.getUsuariosCadsus());
            hql.addToWhereWhithAnd("procedimento ", this.param.getProcedimento());
            hql.addToWhereWhithAnd("cp.dataGeracao is not null");
            hql.addToWhereWhithAnd("cp.convenio ", this.param.getConvenio());

            if (RelatorioProcedimentoDetalhadoDTOParam.SituacaoProducao.FECHADA.equals(this.param.getSituacaoProducao())) {
                hql.addToWhereWhithAnd("cp.status = " + ContaPaciente.Status.FECHADA.value());
                hql.addToWhereWhithAnd("icp.status = " + ItemContaPaciente.Status.CONFIRMADO.value());

            } else if (RelatorioProcedimentoDetalhadoDTOParam.SituacaoProducao.ABERTA.equals(this.param.getSituacaoProducao())) {
                hql.addToWhereWhithAnd("cp.status = " + ContaPaciente.Status.ABERTA.value());
                hql.addToWhereWhithAnd("icp.status in (" + Arrays.asList(ItemContaPaciente.Status.CONFIRMADO.value(), ItemContaPaciente.Status.ABERTO.value()).toString().replaceAll("\\[", "").replaceAll("\\]", "") + ")");

            } else {
                hql.addToWhereWhithAnd("cp.status in (" + Arrays.asList(ContaPaciente.Status.ABERTA.value(), ContaPaciente.Status.FECHADA.value()).toString().replaceAll("\\[", "").replaceAll("\\]", "") + ")");
                hql.addToWhereWhithAnd("icp.status in (" + Arrays.asList(ItemContaPaciente.Status.CONFIRMADO.value(), ItemContaPaciente.Status.ABERTO.value()).toString().replaceAll("\\[", "").replaceAll("\\]", "") + ")");

            }

//            hql.addToWhereWhithAnd("icp.origemLancamento = ", ItemContaPaciente.OrigemLancamento.FECHAMENTO_CONTA.value());
            hql.addToWhereWhithAnd("icp.tipo = ", ItemContaPaciente.Tipo.PROCEDIMENTO.value());
            hql.addToWhereWhithAnd("cidade = ", this.param.getCidade());

            hql.addToWhereWhithAnd("cbo ", this.param.getTabelasCbo());

            if (this.param.getTabelaCboSubGrupo() != null) {
                hql.addToWhereWhithAnd("cbo.tabelaCboSubGrupo.id.tabelaCboGrupo.codigo = ", this.param.getTabelaCboSubGrupo().getId().getTabelaCboGrupo().getCodigo());
                if (this.param.getTabelaCboSubGrupo().getId().getCodigo() != null) {
                    hql.addToWhereWhithAnd("cbo.tabelaCboSubGrupo.id.codigo = ", this.param.getTabelaCboSubGrupo().getId().getCodigo());
                }
            }

            if (this.param.getTipoProcedimento() != null) {
                hql.addToWhereWhithAnd("coalesce(procedimento.flagFaturavel, 'S') = ", this.param.getTipoProcedimento());
            }

            hql.addToWhereWhithAnd("cp.flagFechadaAutomatico = ", RepositoryComponentDefault.NAO_LONG);
        }

        @Override
        protected void result(HQLHelper hql, Object result) {
            this.dtos = hql.getBeanList((List<Map<String, Object>>) result);
        }

        @Override
        public List<RelatorioProcedimentoDTO> getResult() {
            return this.dtos;
        }

        @Override
        protected void setParameters(HQLHelper hql, org.hibernate.Query query) throws ValidacaoException, DAOException {
            if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.PROFISSIONAL.equals((this.param.getFormaApresentacao()))) {
                query.setParameter("statusAtivo", EquipeProfissional.STATUS_ATIVO);
            }
        }
    }

    private static class QueryVisitaDomiciliar extends CommandQuery {

        private RelatorioProcedimentoDetalhadoDTOParam param;
        private List<RelatorioProcedimentoDTO> dtos;

        public QueryVisitaDomiciliar(RelatorioProcedimentoDetalhadoDTOParam param) {
            this.param = param;
        }

        @Override
        protected void createQuery(HQLHelper hql) {
            hql.setTypeSelect(RelatorioProcedimentoDTO.class.getName());

            if (RepositoryComponentDefault.NAO.equals(this.param.getAgruparUnidade())) {
                hql.addToSelect("emp.codigo", "empresa.codigo");
                hql.addToSelect("emp.referencia", "empresa.referencia");
                hql.addToSelect("emp.descricao", "empresa.descricao");
                hql.addToSelect("emp.cnes", "empresa.cnes");
            }

            hql.addToSelect("cast(1 as double)", "quantidade");
            hql.addToSelect("vd.dataVisita", "dataAtendimento");
            hql.addToSelect("usu.codigo", "usuarioCadsus.codigo");
            hql.addToSelect("usu.nome", "usuarioCadsus.nome");
            hql.addToSelect("usu.sexo", "usuarioCadsus.sexo");
            hql.addToSelect("usu.dataNascimento", "usuarioCadsus.dataNascimento");
            hql.addToSelect("prof.codigo", "profissional.codigo");
            hql.addToSelect("prof.nome", "profissional.nome");
            hql.addToSelect("proc.codigo", "procedimento.codigo");
            hql.addToSelect("proc.referencia", "procedimento.referencia");
            hql.addToSelect("proc.descricao", "procedimento.descricao");

            if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.PROFISSIONAL.equals((this.param.getFormaApresentacao()))) {
                hql.addToSelect("prof.referencia", "profissional.referencia");
                hql.addToSelect("prof.codigoCns", "profissional.codigoCns");
            } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.CBO.equals((this.param.getFormaApresentacao()))) {
                hql.addToSelect("cbo.cbo", "tabelaCbo.cbo");
                hql.addToSelect("cbo.descricao", "tabelaCbo.descricao");
            }

            hql.addToFrom("VisitaDomiciliar vd "
                    + " left join vd.profissional prof"
                    + " left join vd.usuarioCadsus usu"
                    + " left join vd.cbo cboVd"
                    + " left join vd.empresa emp");
            hql.addToFrom("TabelaCbo cbo ");
            hql.addToFrom("ProcedimentoCompetencia pc "
                    + " join pc.financiamento pf"
                    + " join pc.id.procedimento proc");
            hql.addToFrom("Usuario usuario ");
            hql.addToFrom("VisitaDomiciliarProcedimento vdp ");

            hql.addToWhereWhithAnd("cbo = vdp.cbo");
            hql.addToWhereWhithAnd("cboVd = vdp.cbo");
            hql.addToWhereWhithAnd("pc.id.dataCompetencia = :dataCompetencia");
            hql.addToWhereWhithAnd("proc = vdp.procedimento");
            hql.addToWhereWhithAnd("vd.usuario = usuario");
            hql.addToWhereWhithAnd("vd.situacao <> ", VisitaDomiciliar.Situacao.CANCELADO.value());

            hql.addToWhereWhithAnd("vd.dataVisita ", param.getPeriodo());
            hql.addToWhereWhithAnd("prof ", this.param.getProfissionals());
            hql.addToWhereWhithAnd("emp ", this.param.getEmpresas());
            hql.addToWhereWhithAnd("usu = ", this.param.getUsuariosCadsus());
            hql.addToWhereWhithAnd("proc ", this.param.getProcedimento());
            hql.addToWhereWhithAnd("cbo ", this.param.getTabelasCbo());
            hql.addToWhereWhithAnd("vd.dataVisita is not null");

            if (this.param.getTabelaCboSubGrupo() != null) {
                hql.addToWhereWhithAnd("cbo.tabelaCboSubGrupo.id.tabelaCboGrupo.codigo = ", this.param.getTabelaCboSubGrupo().getId().getTabelaCboGrupo().getCodigo());
                if (this.param.getTabelaCboSubGrupo().getId().getCodigo() != null) {
                    hql.addToWhereWhithAnd("cbo.tabelaCboSubGrupo.id.codigo = ", this.param.getTabelaCboSubGrupo().getId().getCodigo());
                }
            }

            if (this.param.getTipoProcedimento() != null) {
                hql.addToWhereWhithAnd("coalesce(proc.flagFaturavel, 'S') = ", this.param.getTipoProcedimento());
            }

        }

        @Override
        protected void setParameters(HQLHelper hql, org.hibernate.Query query) throws ValidacaoException, DAOException {
            query.setParameter("dataCompetencia", (Date) CargaBasicoPadrao.getInstance().getParametroPadrao().getPropertyValue(Parametro.PROP_DATA_COMPETENCIA_PROCEDIMENTO));
        }


        @Override
        protected void result(HQLHelper hql, Object result) {
            this.dtos = hql.getBeanList((List<Map<String, Object>>) result);
        }

        @Override
        public List<RelatorioProcedimentoDTO> getResult() {
            return this.dtos;
        }
    }

    private static class QueryLancamentoBpaConsolidado extends CommandQuery {

        private RelatorioProcedimentoDetalhadoDTOParam param;
        private List<RelatorioProcedimentoDTO> dtos;

        public QueryLancamentoBpaConsolidado(RelatorioProcedimentoDetalhadoDTOParam param) {
            this.param = param;
        }

        @Override
        protected void createQuery(HQLHelper hql) {
            try {
                hql.setTypeSelect(RelatorioProcedimentoDTO.class.getName());

                if (RepositoryComponentDefault.NAO.equals(this.param.getAgruparUnidade())) {
                    hql.addToSelect("emp.codigo", "empresa.codigo");
                    hql.addToSelect("emp.referencia", "empresa.referencia");
                    hql.addToSelect("emp.descricao", "empresa.descricao");
                    hql.addToSelect("emp.cnes", "empresa.cnes");
                }

                hql.addToSelect("cast(coalesce(lancamentoBpaConsolidadoItem.quantidade, 0) as double)", "quantidade");
                hql.addToSelect("lancamentoBpaConsolidadoItem.dataCadastro", "dataAtendimento");
                hql.addToSelect("prof.codigo", "profissional.codigo");
                hql.addToSelect("prof.nome", "profissional.nome");
                hql.addToSelect("proc.codigo", "procedimento.codigo");
                hql.addToSelect("proc.referencia", "procedimento.referencia");
                hql.addToSelect("proc.descricao", "procedimento.descricao");

                if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.PROFISSIONAL.equals((this.param.getFormaApresentacao()))) {
                    hql.addToSelect("prof.referencia", "profissional.referencia");
                    hql.addToSelect("prof.codigoCns", "profissional.codigoCns");
                } else if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.CBO.equals((this.param.getFormaApresentacao()))) {
                    hql.addToSelect("cbo.cbo", "tabelaCbo.cbo");
                    hql.addToSelect("cbo.descricao", "tabelaCbo.descricao");
                }

                hql.addToFrom("LancamentoBpaConsolidadoItem lancamentoBpaConsolidadoItem "
                        + " left join lancamentoBpaConsolidadoItem.lancamentoBpaConsolidado lancamentoBpaConsolidado"
                        + " left join lancamentoBpaConsolidado.profissional prof"
                        + " left join lancamentoBpaConsolidado.empresa emp");
                hql.addToFrom("TabelaCbo cbo ");
                hql.addToFrom("ProcedimentoCompetencia pc "
                        + " join pc.financiamento pf"
                        + " join pc.id.procedimento proc");

                if (param.getPeriodo() != null) {
                    int diaInicioCompetencia = ((Long) CargaBasicoPadrao.getInstance().getParametroAtendimento().getPropertyValue(ParametroAtendimento.PROP_DIA_INICIO_COMPETENCIA)).intValue();
                    Date inicio = Data.competenciaData(diaInicioCompetencia, param.getPeriodo().getDataInicial());
                    Date fim;
                    if (param.getPeriodo().getDataFinal() != null) {
                        fim = Data.competenciaData(diaInicioCompetencia, param.getPeriodo().getDataFinal());
                    } else {
                        fim = Data.competenciaData(diaInicioCompetencia, DataUtil.getDataAtual());
                    }

                    hql.addToWhereWhithAnd("lancamentoBpaConsolidado.competencia", new DatePeriod(inicio, fim));
                }
                hql.addToWhereWhithAnd("pc.id.dataCompetencia = :dataCompetencia");
                hql.addToWhereWhithAnd("proc = lancamentoBpaConsolidadoItem.procedimento");
                hql.addToWhereWhithAnd("cbo = lancamentoBpaConsolidado.tabelaCbo");

//                hql.addToWhereWhithAnd("lancamentoBpaConsolidadoItem.dataCadastro ", param.getPeriodo());
                hql.addToWhereWhithAnd("prof ", this.param.getProfissionals());
                hql.addToWhereWhithAnd("emp ", this.param.getEmpresas());
                hql.addToWhereWhithAnd("proc ", this.param.getProcedimento());
                hql.addToWhereWhithAnd("cbo ", this.param.getTabelasCbo());
                hql.addToWhereWhithAnd("lancamentoBpaConsolidadoItem.dataCadastro is not null");

                if (this.param.getTabelaCboSubGrupo() != null) {
                    hql.addToWhereWhithAnd("cbo.tabelaCboSubGrupo.id.tabelaCboGrupo.codigo = ", this.param.getTabelaCboSubGrupo().getId().getTabelaCboGrupo().getCodigo());
                    if (this.param.getTabelaCboSubGrupo().getId().getCodigo() != null) {
                        hql.addToWhereWhithAnd("cbo.tabelaCboSubGrupo.id.codigo = ", this.param.getTabelaCboSubGrupo().getId().getCodigo());
                    }
                }

                if (this.param.getTipoProcedimento() != null) {
                    hql.addToWhereWhithAnd("coalesce(proc.flagFaturavel, 'S') = ", this.param.getTipoProcedimento());
                }
            } catch (ValidacaoException ex) {
                Logger.getLogger(QueryRelatorioProcedimentoDetalhado.class.getName()).log(Level.SEVERE, null, ex);
            }

        }

        @Override
        protected void setParameters(HQLHelper hql, org.hibernate.Query query) throws ValidacaoException, DAOException {
            query.setParameter("dataCompetencia", (Date) CargaBasicoPadrao.getInstance().getParametroPadrao().getPropertyValue(Parametro.PROP_DATA_COMPETENCIA_PROCEDIMENTO));
        }

        @Override
        protected void result(HQLHelper hql, Object result) {
            this.dtos = hql.getBeanList((List<Map<String, Object>>) result);
        }

        @Override
        public List<RelatorioProcedimentoDTO> getResult() {
            return this.dtos;
        }
    }

    private static class QueryAtividadeGrupo extends CommandQuery {

        private RelatorioProcedimentoDetalhadoDTOParam param;
        private List<RelatorioProcedimentoDTO> dtos;

        public QueryAtividadeGrupo(RelatorioProcedimentoDetalhadoDTOParam param) {
            this.param = param;
        }

        @Override
        protected void createQuery(HQLHelper hql) {
            hql.setTypeSelect(RelatorioProcedimentoDTO.class.getName());

            if (RepositoryComponentDefault.NAO.equals(this.param.getAgruparUnidade())) {
                hql.addToSelect("emp.codigo", "empresa.codigo");
                hql.addToSelect("emp.referencia", "empresa.referencia");
                hql.addToSelect("emp.descricao", "empresa.descricao");
                hql.addToSelect("emp.cnes", "empresa.cnes");
            }

            hql.addToSelect("cast(coalesce(agp.quantidade, 0) as double)", "quantidade");
            hql.addToSelect("ag.dataInicio", "dataAtendimento");
            hql.addToSelect("proc.codigo", "procedimento.codigo");
            hql.addToSelect("proc.referencia", "procedimento.referencia");
            hql.addToSelect("proc.descricao", "procedimento.descricao");

            if (RelatorioProcedimentoDetalhadoDTOParam.FormaApresentacao.CBO.equals((this.param.getFormaApresentacao()))) {
                hql.addToSelect("cbo.cbo", "tabelaCbo.cbo");
                hql.addToSelect("cbo.descricao", "tabelaCbo.descricao");
            }

            hql.addToFrom("AtividadeGrupoProcedimento agp"
                    + " left join agp.procedimento proc"
                    + " left join agp.tabelaCbo cbo"
                    + " left join agp.atividadeGrupo ag"
                    + " left join agp.empresaBpa emp");

            hql.addToFrom("ProcedimentoCompetencia pc"
                    + " left join pc.financiamento pf"
                    + " left join pc.id.procedimento proc1");

            hql.addToWhereWhithAnd("proc = proc1");
            hql.addToWhereWhithAnd("pc.id.dataCompetencia = :dataCompetencia");
            hql.addToWhereWhithAnd("ag.situacao = :situacaoAtividade");

            hql.addToWhereWhithAnd("ag.dataInicio ", param.getPeriodo());
            hql.addToWhereWhithAnd("emp ", this.param.getEmpresas());
            hql.addToWhereWhithAnd("proc ", this.param.getProcedimento());
            hql.addToWhereWhithAnd("cbo ", this.param.getTabelasCbo());
            hql.addToWhereWhithAnd("ag.dataInicio is not null");

            if (this.param.getTabelaCboSubGrupo() != null) {
                hql.addToWhereWhithAnd("cbo.tabelaCboSubGrupo.id.tabelaCboGrupo.codigo = ", this.param.getTabelaCboSubGrupo().getId().getTabelaCboGrupo().getCodigo());
                if (this.param.getTabelaCboSubGrupo().getId().getCodigo() != null) {
                    hql.addToWhereWhithAnd("cbo.tabelaCboSubGrupo.id.codigo = ", this.param.getTabelaCboSubGrupo().getId().getCodigo());
                }
            }

            if (this.param.getTipoProcedimento() != null) {
                hql.addToWhereWhithAnd("coalesce(proc.flagFaturavel, 'S') = ", this.param.getTipoProcedimento());
            }

        }

        @Override
        protected void setParameters(HQLHelper hql, org.hibernate.Query query) throws ValidacaoException, DAOException {
            query.setParameter("dataCompetencia", (Date) CargaBasicoPadrao.getInstance().getParametroPadrao().getPropertyValue(Parametro.PROP_DATA_COMPETENCIA_PROCEDIMENTO));
            query.setParameter("situacaoAtividade", AtividadeGrupo.SITUACAO_CONCLUIDA);
        }

        @Override
        protected void result(HQLHelper hql, Object result) {
            this.dtos = hql.getBeanList((List<Map<String, Object>>) result);
        }

        @Override
        public List<RelatorioProcedimentoDTO> getResult() {
            return this.dtos;
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public Collection getResult() {
        return this.result;
    }

    @Override
    public void setDTOParam(RelatorioProcedimentoDetalhadoDTOParam param) {
        this.param = param;
    }
}
