package br.com.ksisolucoes.report.unidadesaude.relatorio.query;

import br.com.celk.examesExternosTestesRapidos.dto.ExameExternoTesteRapidoDTO;
import br.com.celk.unidadesaude.esus.relatorios.dto.RelatorioExamesExternosTestesRapidosDTOParam;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.unidadesaude.relatorio.utils.RelatorioExamesExternosTestesRapidosUtil;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.TesteRapidoRealizado;
import br.com.ksisolucoes.vo.prontuario.basico.TipoTesteRapido;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.hibernate.type.DateType;
import org.hibernate.type.LongType;
import org.hibernate.type.StringType;

import java.util.Collection;
import java.util.List;

public class QueryTestesRapidos extends AbstractCommandTransaction<ExameExternoTesteRapidoDTO> {

    private RelatorioExamesExternosTestesRapidosDTOParam param;
    private List<ExameExternoTesteRapidoDTO> testesRapidos;

    public QueryTestesRapidos(RelatorioExamesExternosTestesRapidosDTOParam param) {
        this.param = param;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        HQLHelper hql = this.createQuery();
        SQLQuery query = getSession().createSQLQuery(hql.getQuery());
        RelatorioExamesExternosTestesRapidosUtil.setQueryParameters(param, query);
        query.setParameter("tipoTeste", TipoTesteRapido.TipoTeste.COVID_19.value());
        this.addScalar(query);
        testesRapidos = query.list();
        for (ExameExternoTesteRapidoDTO testeRapidos : testesRapidos) {
            testeRapidos.setCategoriaExame(RelatorioExamesExternosTestesRapidosDTOParam.CategoriaExame.TESTE_RAPIDO.descricao());
            TesteRapidoRealizado.Resultado resultado = TesteRapidoRealizado.Resultado.valeuOf(testeRapidos.getResultadoTesteRapido());
            testeRapidos.setResultado(resultado != null ? resultado.descricao() : "");
        }
    }

    protected HQLHelper createQuery() {
        HQLHelper hql = new HQLHelper();
        hql.setTypeSelect(ExameExternoTesteRapidoDTO.class.getName());
        hql.setUseSQL(true);

        // CAMPOS
        hql.addToSelect("usuario_cadsus.nm_usuario", "nomePaciente");
        hql.addToSelect("teste_rapido_tipo.ds_tp_teste", "tipoExame");
        hql.addToSelect("teste_rapido_realizado.resultado", "resultadoTesteRapido");
        hql.addToSelect("atendimento.dt_atendimento", "dataExame");
        hql.addToSelect("empresa.descricao", "unidadeSaude");
        hql.addToSelect("equipe_area_1.ds_area", "equipeReferencia1");
        hql.addToSelect("equipe_area_2.ds_area", "equipeReferencia2");
        hql.addToSelect("equipe_area_3.ds_area", "equipeReferencia3");

        hql.addToFrom(" teste_rapido_realizado"
                + " left join teste_rapido_tipo on teste_rapido_tipo.cd_tp_teste = teste_rapido_realizado.cd_tp_teste"
                + " left join teste_rapido on teste_rapido.cd_teste_rapido = teste_rapido_realizado.cd_teste_rapido"
                + " left join atendimento on atendimento.nr_atendimento = teste_rapido.nr_atendimento"
                + " left join usuario_cadsus on usuario_cadsus.cd_usu_cadsus = atendimento.cd_usu_cadsus"
                + " left join empresa on atendimento.empresa = empresa.empresa"
                + " left join endereco_usuario_cadsus on endereco_usuario_cadsus.cd_endereco = usuario_cadsus.cd_endereco "
                + " left join endereco_estruturado on endereco_estruturado.cd_endereco_estruturado = endereco_usuario_cadsus.cd_endereco_estruturado "
                + " left join equipe_micro_area equipe_micro_area_1 on equipe_micro_area_1.cd_eqp_micro_area = endereco_estruturado.cd_eqp_micro_area "
                + " left join endereco_domicilio on endereco_domicilio.cd_endereco = endereco_usuario_cadsus.cd_endereco "
                + " left join equipe_micro_area equipe_micro_area_2 on equipe_micro_area_2.cd_eqp_micro_area = endereco_domicilio.cd_eqp_micro_area "
                + " left join equipe_area equipe_area_1 on usuario_cadsus.cd_equipe = equipe_area_1.cd_equipe_area "
                + " left join equipe_area equipe_area_2 on equipe_micro_area_1.cd_equipe_area = equipe_area_2.cd_equipe_area "
                + " left join equipe_area equipe_area_3 on equipe_micro_area_2.cd_equipe_area = equipe_area_3.cd_equipe_area "
        );

        // PARÂMETROS
        hql.addToWhereWhithAnd("teste_rapido_tipo.tp_teste = :tipoTeste");
        if (param.getEmpresa() != null) {
            hql.addToWhereWhithAnd("empresa.empresa = :codigoEmpresa");
        }
        if (param.getPeriodo() != null) {
            hql.addToWhereWhithAnd("(atendimento.dt_atendimento >= :dataInicial and atendimento.dt_atendimento <= :dataFinal)");
        }
        return hql;
    }

    private void addScalar(SQLQuery sql) {
        sql.addScalar("nomePaciente", StringType.INSTANCE)
                .addScalar("tipoExame", StringType.INSTANCE)
                .addScalar("dataExame", DateType.INSTANCE)
                .addScalar("resultadoTesteRapido", LongType.INSTANCE)
                .addScalar("unidadeSaude", StringType.INSTANCE)
                .addScalar("equipeReferencia1", StringType.INSTANCE)
                .addScalar("equipeReferencia2", StringType.INSTANCE)
                .addScalar("equipeReferencia3", StringType.INSTANCE)
                .setResultTransformer(Transformers.aliasToBean(ExameExternoTesteRapidoDTO.class));
    }

    public Collection<ExameExternoTesteRapidoDTO> getResult() {
        return testesRapidos;
    }

}
