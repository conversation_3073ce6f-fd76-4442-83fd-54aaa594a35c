<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_detalhamento_gastos" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="786" leftMargin="28" rightMargin="28" topMargin="20" bottomMargin="20" uuid="ab2d4061-50d8-4a89-9d0a-037ae58fa7ce">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="3.1384283767210035"/>
	<property name="ireport.x" value="2196"/>
	<property name="ireport.y" value="125"/>
	<import value="br.com.ksisolucoes.vo.entradas.recebimento.RegistroNotaFiscal"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.entradas.estoque.Produto"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<import value="br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal"/>
	<import value="br.com.ksisolucoes.report.vacina.dto.RelatorioEstoqueVacinasDTOParam.FormaApresentacao"/>
	<parameter name="formaApresentacao" class="br.com.ksisolucoes.report.frota.interfaces.dto.RelatorioDetalhamentoGastosDTOParam.FormaApresentacao"/>
	<parameter name="tipoResumo" class="br.com.ksisolucoes.report.frota.interfaces.dto.RelatorioDetalhamentoGastosDTOParam.TipoResumo"/>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa">
		<fieldDescription><![CDATA[registroManutencao.empresa]]></fieldDescription>
	</field>
	<field name="programaSaude" class="br.com.ksisolucoes.vo.programasaude.ProgramaSaude">
		<fieldDescription><![CDATA[veiculo.programaSaude]]></fieldDescription>
	</field>
	<field name="veiculo" class="br.com.ksisolucoes.vo.frota.Veiculo"/>
	<field name="tipoOperacao" class="br.com.ksisolucoes.vo.frota.TipoOperacao"/>
	<field name="registroManutencao" class="br.com.ksisolucoes.vo.frota.RegistroManutencao"/>
	<field name="quantidade" class="java.lang.Double"/>
	<field name="valor" class="java.lang.Double"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="totalTipoResumo" class="java.lang.Double" resetType="Group" resetGroup="GrupoTipoResumo" calculation="Sum">
		<variableExpression><![CDATA[$F{valor}]]></variableExpression>
	</variable>
	<variable name="totalFormaApresentacao" class="java.lang.Double" resetType="Group" resetGroup="GrupoFormaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{valor}]]></variableExpression>
	</variable>
	<variable name="totalGeral" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{valor}]]></variableExpression>
	</variable>
	<group name="GroupGeral">
		<groupFooter>
			<band height="13">
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="679" y="3" width="53" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="ab69386e-42a5-4b6f-b605-4c6925fd8726"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*TotalGeral*/$V{BUNDLE}.getStringApplication("rotulo_total_geral")]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-57" mode="Transparent" x="736" y="3" width="50" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="c0d35f84-ba85-4285-b6b8-8b33a940545f"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("0.0000").format($V{totalGeral})]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="629" y="2" width="157" height="1" uuid="a862f086-5a86-460f-9677-d54fc9aeb523"/>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="GrupoFormaApresentacao" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$P{formaApresentacao}.equals(br.com.ksisolucoes.report.frota.interfaces.dto.RelatorioDetalhamentoGastosDTOParam.FormaApresentacao.UNIDADE)?
    $F{empresa}.getCodigo()
:
    $P{formaApresentacao}.equals(br.com.ksisolucoes.report.frota.interfaces.dto.RelatorioDetalhamentoGastosDTOParam.FormaApresentacao.PROGRAMA_SAUDE)?
        $F{programaSaude}.getCodigo()
    :
        $P{formaApresentacao}.equals(br.com.ksisolucoes.report.frota.interfaces.dto.RelatorioDetalhamentoGastosDTOParam.FormaApresentacao.VEICULO)?
            $F{veiculo}.getCodigo()
        :
            $P{formaApresentacao}.equals(br.com.ksisolucoes.report.frota.interfaces.dto.RelatorioDetalhamentoGastosDTOParam.FormaApresentacao.TIPO_OPERACAO)?
                $F{tipoOperacao}.getCodigo()
            :
                null]]></groupExpression>
		<groupHeader>
			<band height="15" splitType="Stretch">
				<rectangle radius="5">
					<reportElement key="rectangle-1" x="0" y="1" width="786" height="13" uuid="08f37741-8d6e-4de0-8fb9-8977d49536b0"/>
				</rectangle>
				<textField evaluationTime="Group" evaluationGroup="GrupoFormaApresentacao" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-1" x="0" y="1" width="786" height="13" uuid="*************-4d48-9daf-3fd043377cba"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{formaApresentacao}.equals(br.com.ksisolucoes.report.frota.interfaces.dto.RelatorioDetalhamentoGastosDTOParam.FormaApresentacao.UNIDADE)?
    $V{BUNDLE}.getStringApplication("rotulo_empresa")+": "+$F{empresa}.getDescricaoFormatado()
:
    $P{formaApresentacao}.equals(br.com.ksisolucoes.report.frota.interfaces.dto.RelatorioDetalhamentoGastosDTOParam.FormaApresentacao.PROGRAMA_SAUDE)?
        $V{BUNDLE}.getStringApplication("rotulo_programa_saude")+": "+$F{programaSaude}.getDescricao()
    :
        $P{formaApresentacao}.equals(br.com.ksisolucoes.report.frota.interfaces.dto.RelatorioDetalhamentoGastosDTOParam.FormaApresentacao.VEICULO)?
            $V{BUNDLE}.getStringApplication("rotulo_veiculo")+": "+"("+$F{veiculo}.getReferencia().trim()+") "+$F{veiculo}.getDescricao()+" - "+Coalesce.asString($F{veiculo}.getPlaca())+" - "+Coalesce.asString($F{veiculo}.getNumeroFrota())
        :
            $P{formaApresentacao}.equals(br.com.ksisolucoes.report.frota.interfaces.dto.RelatorioDetalhamentoGastosDTOParam.FormaApresentacao.TIPO_OPERACAO)?
                $V{BUNDLE}.getStringApplication("rotulo_tipo_operacao")+": "+$F{tipoOperacao}.getDescricao()
            :
                ""]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="12" splitType="Stretch">
				<line>
					<reportElement x="629" y="2" width="157" height="1" uuid="62b02c46-0039-4ace-9071-8177fc9c5242"/>
				</line>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-57" mode="Transparent" x="736" y="3" width="50" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="2918f5ce-a1ed-4ea5-876d-06b8b2e2eefe"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("0.0000").format($V{totalFormaApresentacao})]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="679" y="3" width="53" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="02eaba87-e799-49b2-ada9-e9b6e7bcdfa4"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Total*/$V{BUNDLE}.getStringApplication("rotulo_total")]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="GrupoTipoResumo" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$P{tipoResumo}.equals(br.com.ksisolucoes.report.frota.interfaces.dto.RelatorioDetalhamentoGastosDTOParam.TipoResumo.UNIDADE)?
    $F{empresa}.getCodigo()
:
    $P{tipoResumo}.equals(br.com.ksisolucoes.report.frota.interfaces.dto.RelatorioDetalhamentoGastosDTOParam.TipoResumo.PROGRAMA_SAUDE)?
        $F{programaSaude}.getCodigo()
    :
        $P{tipoResumo}.equals(br.com.ksisolucoes.report.frota.interfaces.dto.RelatorioDetalhamentoGastosDTOParam.TipoResumo.VEICULO)?
            $F{veiculo}.getCodigo()
        :
            $P{tipoResumo}.equals(br.com.ksisolucoes.report.frota.interfaces.dto.RelatorioDetalhamentoGastosDTOParam.TipoResumo.TIPO_OPERACAO)?
                $F{tipoOperacao}.getCodigo()
            :
                ""]]></groupExpression>
		<groupHeader>
			<band height="20" splitType="Stretch">
				<printWhenExpression><![CDATA[!$P{formaApresentacao}.descricao().equals($P{tipoResumo}.descricao())]]></printWhenExpression>
				<textField evaluationTime="Group" evaluationGroup="GrupoTipoResumo" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-60" x="0" y="3" width="786" height="14" uuid="163140e8-3a4a-4fd0-b2ec-ff0a512bfe63"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="true" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{tipoResumo}.equals(br.com.ksisolucoes.report.frota.interfaces.dto.RelatorioDetalhamentoGastosDTOParam.TipoResumo.UNIDADE)?
    $V{BUNDLE}.getStringApplication("rotulo_empresa")+": "+$F{empresa}.getDescricaoFormatado()
:
    $P{tipoResumo}.equals(br.com.ksisolucoes.report.frota.interfaces.dto.RelatorioDetalhamentoGastosDTOParam.TipoResumo.PROGRAMA_SAUDE)?
        $V{BUNDLE}.getStringApplication("rotulo_programa_saude")+": "+$F{programaSaude}.getDescricao()
    :
        $P{tipoResumo}.equals(br.com.ksisolucoes.report.frota.interfaces.dto.RelatorioDetalhamentoGastosDTOParam.TipoResumo.VEICULO)?
            $V{BUNDLE}.getStringApplication("rotulo_veiculo")+": "+"("+$F{veiculo}.getReferencia().trim()+") "+$F{veiculo}.getDescricao()+" - "+Coalesce.asString($F{veiculo}.getPlaca())+" - "+Coalesce.asString($F{veiculo}.getNumeroFrota())
        :
            $P{tipoResumo}.equals(br.com.ksisolucoes.report.frota.interfaces.dto.RelatorioDetalhamentoGastosDTOParam.TipoResumo.TIPO_OPERACAO)?
                $V{BUNDLE}.getStringApplication("rotulo_tipo_operacao")+": "+$F{tipoOperacao}.getDescricao()
            :
                ""]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="12" splitType="Stretch">
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-57" mode="Transparent" x="736" y="3" width="50" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="f0a442d2-eec7-431a-ab1e-7fdf035d958e"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("0.0000").format($V{totalTipoResumo})]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="629" y="2" width="157" height="1" uuid="8de51291-5303-4a31-b53a-2dd55a960135"/>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="679" y="3" width="53" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="31b9cd07-b965-456b-9edc-9d8bb5adac4c"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*SubTotal*/$V{BUNDLE}.getStringApplication("rotulo_subtotal")]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="GroupHeader">
		<groupHeader>
			<band height="12">
				<line>
					<reportElement key="line-4" x="0" y="11" width="786" height="1" uuid="1c454801-1ade-4dc3-9481-************"/>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="223" y="1" width="60" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="4f8e1876-5371-404c-add4-201a6dd3d440"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Manuteção*/$V{BUNDLE}.getStringApplication("rotulo_manutencao")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="285" y="1" width="110" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="f77404e8-33d7-4889-a79d-9e550b157d20"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*TipoOperacao*/$V{BUNDLE}.getStringApplication("rotulo_tipo_operacao")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="398" y="1" width="110" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="fa67be40-c23a-441e-b6a1-729495c9e293"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*ProgramaSaude*/$V{BUNDLE}.getStringApplication("rotulo_programa_saude")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="512" y="1" width="162" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="bcb99c45-db05-4679-bdac-7dbf46aaf34b"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Veiculo*/$V{BUNDLE}.getStringApplication("rotulo_veiculo")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="679" y="1" width="53" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="64e6a470-d713-43b4-b68e-4cb8536129e2"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Quantidade*/$V{BUNDLE}.getStringApplication("rotulo_quantidade")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="736" y="1" width="50" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="fd3ac153-93ae-45f3-870c-15f3c3bb939e"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Valor*/$V{BUNDLE}.getStringApplication("rotulo_valor")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="57" y="1" width="163" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="92e9ae5c-f505-4079-a1b2-1c002411a09c"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Empresa*/$V{BUNDLE}.getStringApplication("rotulo_empresa")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="0" y="2" width="55" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="33210b2b-6257-4257-ada3-9261529b1467"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Documento*/$V{BUNDLE}.getStringApplication("rotulo_documento")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="9" splitType="Stretch">
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" mode="Transparent" x="57" y="0" width="163" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="ffb6a40b-0e38-490a-9294-7c51f642b630"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresa}.getDescricaoFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement key="textField-57" mode="Transparent" x="223" y="0" width="60" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="de55e635-121b-4dc1-b0a7-a0b3418a7be8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{registroManutencao}.getDataManutencao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" mode="Transparent" x="285" y="0" width="110" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="5a8cb48d-432d-4112-b5f1-cc5bb8336900"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tipoOperacao}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" mode="Transparent" x="398" y="0" width="110" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="24fa5c28-828c-44a9-bfc6-a2ed6a4160b0"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{programaSaude}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" mode="Transparent" x="512" y="0" width="162" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="0d307644-de30-47b0-8779-94abf531fb63"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["("+$F{veiculo}.getReferencia().trim()+") "+$F{veiculo}.getDescricao()+" - "+Coalesce.asString($F{veiculo}.getPlaca())+" - "+Coalesce.asString($F{veiculo}.getNumeroFrota())]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement key="textField-57" mode="Transparent" x="679" y="0" width="53" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="9cf360d0-14ca-402f-bc75-de4cdb0aa2d5"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidade}]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement key="textField-57" mode="Transparent" x="736" y="0" width="50" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="acaf8d21-46ba-4c54-ac65-94ba24541f82"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("0.0000").format($F{valor})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" mode="Transparent" x="0" y="0" width="55" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="58ed3421-44f7-4492-89c8-07115ec05a7c"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{registroManutencao}.getDocumento()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
