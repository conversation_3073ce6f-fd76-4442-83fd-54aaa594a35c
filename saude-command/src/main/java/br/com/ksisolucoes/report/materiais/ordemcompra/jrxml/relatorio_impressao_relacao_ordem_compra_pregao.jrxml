<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_pedidos_transferencia_fornecedor_pregao" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="3338a750-9d07-4276-9464-b9351dbcc2b7">
	<property name="ireport.zoom" value="3.2210200000000038"/>
	<property name="ireport.x" value="1716"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.report.materiais.ordemcompra.interfaces.dto.RelatorioRelacaoOrdemCompraDTOParam.FormaApresentacao"/>
	<import value="br.com.ksisolucoes.report.materiais.ordemcompra.interfaces.dto.RelatorioRelacaoOrdemCompraDTOParam.BaseCalculo"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="FORMA_APRESENTACAO" class="br.com.ksisolucoes.report.materiais.ordemcompra.interfaces.dto.RelatorioRelacaoOrdemCompraDTOParam.FormaApresentacao"/>
	<parameter name="BASE_CALCULO" class="br.com.ksisolucoes.report.materiais.ordemcompra.interfaces.dto.RelatorioRelacaoOrdemCompraDTOParam.BaseCalculo"/>
	<field name="produto" class="br.com.ksisolucoes.vo.entradas.estoque.Produto"/>
	<field name="fornecedor" class="br.com.ksisolucoes.vo.basico.Pessoa"/>
	<field name="unidade" class="br.com.ksisolucoes.vo.entradas.estoque.Unidade"/>
	<field name="ordemCompra" class="br.com.ksisolucoes.vo.entradas.estoque.OrdemCompra"/>
	<field name="ordemCompraItem" class="br.com.ksisolucoes.vo.entradas.estoque.OrdemCompraItem"/>
	<field name="saldo" class="java.lang.Double"/>
	<variable name="totalValorFA" class="java.lang.Double" resetType="Group" resetGroup="formaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[]]></variableExpression>
	</variable>
	<variable name="totalGeral" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$V{total}]]></variableExpression>
	</variable>
	<variable name="totalResumo" class="java.lang.Double" resetType="Group" resetGroup="tipoResumo" calculation="Sum">
		<variableExpression><![CDATA[]]></variableExpression>
	</variable>
	<variable name="saldo" class="java.math.BigDecimal">
		<variableExpression><![CDATA[$F{ordemCompraItem}.getQuantidadeCompra().subtract($F{ordemCompraItem}.getQuantidadeRecebida())]]></variableExpression>
	</variable>
	<variable name="total" class="java.math.BigDecimal">
		<variableExpression><![CDATA[BaseCalculo.QUANTIDADE.value().equals($P{BASE_CALCULO}.value())
?
   $F{ordemCompraItem}.getQuantidadeCompra().multiply($F{ordemCompraItem}.getPrecoUnitario())
:
   $V{saldo}.multiply($F{ordemCompraItem}.getPrecoUnitario())]]></variableExpression>
	</variable>
	<variable name="totalFA" class="java.lang.Double" resetType="Group" resetGroup="formaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$V{total}]]></variableExpression>
	</variable>
	<variable name="totalQuantidade" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{ordemCompraItem}.getQuantidadeCompra()]]></variableExpression>
	</variable>
	<variable name="totalQuantidadeRecebida" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{ordemCompraItem}.getQuantidadeRecebida()]]></variableExpression>
	</variable>
	<variable name="totalQuantidadeFA" class="java.lang.Double" resetType="Group" resetGroup="formaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{ordemCompraItem}.getQuantidadeCompra()]]></variableExpression>
	</variable>
	<variable name="totalQuantidadeRecebidaFA" class="java.lang.Double" resetType="Group" resetGroup="formaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{ordemCompraItem}.getQuantidadeRecebida()]]></variableExpression>
	</variable>
	<variable name="totalQuantidadePregao" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{ordemCompraItem}.getQuantidadePregao()]]></variableExpression>
	</variable>
	<variable name="totalQuantidadeFAPregao" class="java.lang.Double" resetType="Group" resetGroup="formaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{ordemCompraItem}.getQuantidadePregao()]]></variableExpression>
	</variable>
	<group name="padrao">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupFooter>
			<band height="12">
				<textField>
					<reportElement x="316" y="2" width="124" height="10" uuid="a0e2b943-fb7c-4550-af98-7d8d21fb6085"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_geral")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="742" y="0" width="59" height="1" uuid="3e9ae5e8-19e9-4d87-8265-80608df877dd"/>
				</line>
				<textField pattern="###0.00;-###0.00">
					<reportElement x="742" y="2" width="60" height="10" uuid="5796cd30-40b4-4a03-be53-678eb54dc4db"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalGeral}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00">
					<reportElement x="496" y="2" width="45" height="10" uuid="dd1ac478-e9db-4151-857e-2b15380421d5"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalQuantidade}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00">
					<reportElement x="602" y="2" width="60" height="10" uuid="dd1ac478-e9db-4151-857e-2b15380421d5"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalQuantidadeRecebida}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00">
					<reportElement x="441" y="2" width="54" height="10" uuid="dd1ac478-e9db-4151-857e-2b15380421d5"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalQuantidadePregao}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="formaApresentacao">
		<groupExpression><![CDATA[FormaApresentacao.FORNECEDOR.equals($P{FORMA_APRESENTACAO})
?
   $F{fornecedor}.getDescricao()
:
    FormaApresentacao.PRODUTO.equals($P{FORMA_APRESENTACAO})
?
   $F{produto}.getDescricao()
:
null]]></groupExpression>
		<groupHeader>
			<band height="15">
				<printWhenExpression><![CDATA[!FormaApresentacao.GERAL.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
				<rectangle radius="5">
					<reportElement positionType="Float" x="0" y="0" width="802" height="14" uuid="03bcfaf6-bd86-48c5-8e4c-0eee33edcd2f"/>
				</rectangle>
				<textField>
					<reportElement positionType="Float" x="0" y="1" width="802" height="14" uuid="e30f2dec-7870-4507-8b01-8ef4812c77ce"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[FormaApresentacao.FORNECEDOR.equals($P{FORMA_APRESENTACAO})
?
   $F{fornecedor}.getDescricao()
:
   $F{produto}.getDescricao()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="12">
				<textField pattern="###0.00;-###0.00">
					<reportElement x="742" y="2" width="60" height="10" uuid="dd1ac478-e9db-4151-857e-2b15380421d5"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalFA}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="742" y="0" width="59" height="1" uuid="3ba14641-c10c-4cb4-90f7-c71691f88761"/>
				</line>
				<textField>
					<reportElement x="316" y="2" width="124" height="10" uuid="347ee29f-ed4a-41c6-9801-623a09b604fe"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00">
					<reportElement x="496" y="2" width="45" height="10" uuid="dd1ac478-e9db-4151-857e-2b15380421d5"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalQuantidadeFA}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00">
					<reportElement x="602" y="2" width="61" height="10" uuid="dd1ac478-e9db-4151-857e-2b15380421d5"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalQuantidadeRecebidaFA}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="441" y="0" width="100" height="1" uuid="3ba14641-c10c-4cb4-90f7-c71691f88761"/>
				</line>
				<textField pattern="###0.00;-###0.00">
					<reportElement x="441" y="2" width="54" height="10" uuid="dd1ac478-e9db-4151-857e-2b15380421d5"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalQuantidadeFAPregao}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="602" y="0" width="60" height="1" uuid="61118016-b46c-4c15-bfbe-d1ae8d317323"/>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="tipoResumo" isReprintHeaderOnEachPage="true">
		<groupHeader>
			<band height="14">
				<textField>
					<reportElement positionType="Float" x="496" y="2" width="45" height="11" uuid="58ba40b6-620c-4873-b37c-fd9d0288be7a"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_qtd_af")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="738" y="2" width="64" height="12" uuid="23da1026-e0dc-4ad3-a2ad-399a4e51e5bc"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement positionType="Float" x="0" y="13" width="802" height="1" uuid="4a8d6dff-0b9d-490f-aa5b-9b31f385e91b"/>
				</line>
				<textField>
					<reportElement positionType="Float" x="0" y="2" width="37" height="11" uuid="58ba40b6-620c-4873-b37c-fd9d0288be7a"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_ordem_compra_abv")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="426" y="2" width="15" height="12" uuid="58ba40b6-620c-4873-b37c-fd9d0288be7a"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_unidade_abv")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="601" y="2" width="61" height="12" uuid="58ba40b6-620c-4873-b37c-fd9d0288be7a"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quantidade_recebia_abv")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="707" y="2" width="30" height="12" uuid="5c1d767c-2d7a-4e9c-b44d-0a3be66d697d"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_preco")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="665" y="2" width="40" height="12" uuid="d5839070-b15a-4fa9-87ea-b150682d645e"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_saldo")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="37" y="2" width="40" height="12" uuid="58ba40b6-620c-4873-b37c-fd9d0288be7a"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="78" y="2" width="33" height="12" uuid="58ba40b6-620c-4873-b37c-fd9d0288be7a"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_pregao")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="112" y="2" width="131" height="12" uuid="58ba40b6-620c-4873-b37c-fd9d0288be7a"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_fornecedor")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="244" y="2" width="48" height="12" uuid="58ba40b6-620c-4873-b37c-fd9d0288be7a"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_item_pregao_abv")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="292" y="2" width="133" height="12" uuid="58ba40b6-620c-4873-b37c-fd9d0288be7a"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_produto")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="441" y="2" width="54" height="11" uuid="58ba40b6-620c-4873-b37c-fd9d0288be7a"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_qtd_pregao")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="543" y="2" width="55" height="12" uuid="d5839070-b15a-4fa9-87ea-b150682d645e"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_saldo_pregao")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="11" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="37" height="11" uuid="f6082c12-20a4-4fe5-9ee8-c3cadc7699b2"/>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="7"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ordemCompra}.getCodigo()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement x="738" y="0" width="64" height="11" uuid="10bea8b4-fdc3-47ce-b435-23bc8d050cdc"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="7"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{total}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="426" y="0" width="15" height="11" uuid="f6082c12-20a4-4fe5-9ee8-c3cadc7699b2"/>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="7"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unidade}.getUnidade()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="37" y="0" width="40" height="11" uuid="f6082c12-20a4-4fe5-9ee8-c3cadc7699b2"/>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="7"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.formatar($F{ordemCompra}.getDataCadastro())]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="78" y="0" width="33" height="11" uuid="f6082c12-20a4-4fe5-9ee8-c3cadc7699b2"/>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="7"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ordemCompra}.getNumeroPregao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="112" y="0" width="131" height="11" uuid="f6082c12-20a4-4fe5-9ee8-c3cadc7699b2"/>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="7"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{fornecedor}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="244" y="0" width="48" height="11" uuid="f6082c12-20a4-4fe5-9ee8-c3cadc7699b2"/>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="7"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ordemCompraItem}.getNumeroItemPregao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="292" y="0" width="133" height="11" uuid="f6082c12-20a4-4fe5-9ee8-c3cadc7699b2"/>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="7"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{produto}.getDescricaoFormatado()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement x="496" y="0" width="45" height="11" uuid="d782ea80-e2bf-4e45-b9a4-4c7892fa37d5"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="7"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ordemCompraItem}.getQuantidadeCompra()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement x="601" y="0" width="61" height="11" uuid="d782ea80-e2bf-4e45-b9a4-4c7892fa37d5"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="7"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ordemCompraItem}.getQuantidadeRecebida()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement x="665" y="0" width="40" height="11" uuid="d782ea80-e2bf-4e45-b9a4-4c7892fa37d5"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="7"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{saldo}]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement x="707" y="0" width="30" height="11" uuid="d782ea80-e2bf-4e45-b9a4-4c7892fa37d5"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="7"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ordemCompraItem}.getPrecoUnitario()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement x="441" y="0" width="54" height="11" uuid="d782ea80-e2bf-4e45-b9a4-4c7892fa37d5"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="7"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ordemCompraItem}.getQuantidadePregao()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement x="543" y="0" width="55" height="11" uuid="d782ea80-e2bf-4e45-b9a4-4c7892fa37d5"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="7"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ordemCompraItem}.getSaldoPregao()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band/>
	</pageFooter>
</jasperReport>
