<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_consumo_produto_detalhado_quebra" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<import value="br.com.ksisolucoes.report.*"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.basico.*"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="FORMA_APRESENTACAO" class="java.lang.Integer" isForPrompting="false"/>
	<parameter name="QuebraPagina" class="java.lang.Boolean" isForPrompting="false"/>
	<parameter name="agruparUnidade" class="java.lang.String"/>
	<field name="class" class="java.lang.Object"/>
	<field name="codigoEmpresa" class="java.lang.String"/>
	<field name="codigoProduto" class="java.lang.String"/>
	<field name="descricaoEmpresa" class="java.lang.String"/>
	<field name="descricaoProduto" class="java.lang.String"/>
	<field name="descricaoFormatado" class="java.lang.String"/>
	<field name="codigoGrupoProduto" class="java.lang.Long"/>
	<field name="descricaoGrupoProduto" class="java.lang.String"/>
	<field name="descricaoSubGrupoProduto" class="java.lang.String"/>
	<field name="codigoSubGrupoProduto" class="java.lang.Long"/>
	<field name="codigoEmpresaDestino" class="java.lang.String"/>
	<field name="descricaoEmpresaDestino" class="java.lang.String"/>
	<field name="descricaoGrupoProdutoFormatado" class="java.lang.String"/>
	<field name="descricaoSubGrupoProdutoFormatado" class="java.lang.String"/>
	<field name="descricaoEmpresaDestinoFormatado" class="java.lang.String"/>
	<field name="descricaoEmpresaOrigemFormatado" class="java.lang.String"/>
	<field name="unidade" class="java.lang.String"/>
	<field name="quantidade" class="java.lang.Double"/>
	<field name="valorUnitario" class="java.lang.Double"/>
	<field name="valorTotal" class="java.lang.Double"/>
	<field name="codigoCentroCusto" class="java.lang.Long"/>
	<field name="descricaoCentroCusto" class="java.lang.String"/>
	<field name="descricaoCentroCustoFormatado" class="java.lang.String"/>
	<variable name="ValorUnitario" class="java.lang.Double">
		<variableExpression><![CDATA[$F{valorTotal}/$F{quantidade}]]></variableExpression>
	</variable>
	<variable name="Total" class="java.lang.Double" resetType="None" incrementType="Report">
		<variableExpression><![CDATA[(( ($F{quantidade} == 0D) || ($V{ValorUnitario} ==0D) )? 0: ($F{quantidade}*$V{ValorUnitario}) )]]></variableExpression>
	</variable>
	<variable name="ValorTotal" class="java.lang.Double" resetType="Group" resetGroup="FORMA_APRESENTACAO" calculation="Sum">
		<variableExpression><![CDATA[$V{Total}]]></variableExpression>
	</variable>
	<variable name="ValorTotalEmpresaOrigem" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresaOrigem" calculation="Sum">
		<variableExpression><![CDATA[$V{Total}]]></variableExpression>
	</variable>
	<variable name="ValorTotalEmpresaDestino" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresaDestino" calculation="Sum">
		<variableExpression><![CDATA[$V{Total}]]></variableExpression>
	</variable>
	<variable name="ValorTotalGeral" class="java.lang.Double" resetType="Group" resetGroup="Geral" calculation="Sum">
		<variableExpression><![CDATA[$V{Total}]]></variableExpression>
	</variable>
	<group name="Geral">
		<groupHeader>
			<band/>
		</groupHeader>
		<groupFooter>
			<band height="11">
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-118" mode="Opaque" x="315" y="2" width="115" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*total*/Bundle.getStringApplication("rotulo_total_geral") + ": "]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-3" mode="Opaque" x="431" y="1" width="96" height="1" forecolor="#000000" backcolor="#FFFFFF"/>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresaOrigem" pattern="#,##0.0000;-#,##0.0000" isBlankWhenNull="true">
					<reportElement key="textField-117" mode="Opaque" x="431" y="2" width="95" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.Double"><![CDATA[$V{ValorTotalGeral} ==  null ? 0.00 : $V{ValorTotalGeral}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="GrupoEmpresaOrigem" isStartNewPage="true" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{agruparUnidade})
?
    $F{codigoEmpresa}
:
    null]]></groupExpression>
		<groupHeader>
			<band height="15" splitType="Stretch">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{agruparUnidade})]]></printWhenExpression>
				<rectangle radius="10">
					<reportElement x="0" y="0" width="535" height="15"/>
				</rectangle>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresaOrigem" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-38" mode="Transparent" x="0" y="0" width="535" height="15" forecolor="#000000"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*Empresa origem*/
Bundle.getStringApplication("rotulo_empresa_dispensadora") + ": " + $F{descricaoEmpresaOrigemFormatado}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="12" splitType="Stretch">
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresaOrigem" pattern="#,##0.0000;-#,##0.0000" isBlankWhenNull="true">
					<reportElement key="textField-117" mode="Opaque" x="431" y="3" width="95" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.Double"><![CDATA[$V{ValorTotalEmpresaOrigem} ==  null ? 0.00 : $V{ValorTotalEmpresaOrigem}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-3" mode="Opaque" x="431" y="2" width="96" height="1" forecolor="#000000" backcolor="#FFFFFF"/>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-118" mode="Opaque" x="314" y="3" width="115" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*total*/Bundle.getStringApplication("rotulo_total_unidade") + ": "]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="GrupoEmpresaDestino" isStartNewPage="true" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{codigoEmpresaDestino}]]></groupExpression>
		<groupHeader>
			<band height="16" splitType="Stretch">
				<printWhenExpression><![CDATA[$P{FORMA_APRESENTACAO}.intValue() == ReportProperties.AGRUPAR_OPERACAO  ? new Boolean(true) : new Boolean(false)]]></printWhenExpression>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresaDestino" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-106" mode="Transparent" x="0" y="1" width="535" height="15" isRemoveLineWhenBlank="true" forecolor="#000000">
						<printWhenExpression><![CDATA[$P{FORMA_APRESENTACAO}.intValue() == ReportProperties.AGRUPAR_OPERACAO  ? new Boolean(true) : new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*Empresa Destino*/
$P{FORMA_APRESENTACAO}.intValue() == ReportProperties.AGRUPAR_OPERACAO ?
$F{codigoEmpresaDestino} == null? Bundle.getStringApplication("rotulo_unidade_origem") + ": " +  Bundle.getStringApplication("rotulo_sem_descricao") :
Bundle.getStringApplication("rotulo_unidade_origem") + ": " +  $F{descricaoEmpresaDestinoFormatado} : ""]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="15" width="535" height="1"/>
				</line>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="12" splitType="Stretch">
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresaDestino" pattern="#,##0.0000;-#,##0.0000" isBlankWhenNull="true">
					<reportElement key="textField-115" mode="Opaque" x="431" y="3" width="95" height="9" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[($P{FORMA_APRESENTACAO}.intValue() == ReportProperties.AGRUPAR_OPERACAO) ? new Boolean(true) : new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.Double"><![CDATA[$V{ValorTotalEmpresaDestino} == null ? 0.00 : $V{ValorTotalEmpresaDestino}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-2" mode="Opaque" x="431" y="2" width="96" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[($P{FORMA_APRESENTACAO}.intValue() == ReportProperties.AGRUPAR_OPERACAO) ? new Boolean(true) : new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-116" mode="Opaque" x="314" y="3" width="115" height="9" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[($P{FORMA_APRESENTACAO}.intValue() == ReportProperties.AGRUPAR_OPERACAO) ? new Boolean(true) : new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*total*/Bundle.getStringApplication("rotulo_total_empresa_destino") + ": "]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="FORMA_APRESENTACAO" isStartNewPage="true" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[/*Forma de Apresentacao*/
(($P{FORMA_APRESENTACAO}.intValue() == ReportProperties.AGRUPAR_GRUPO) || ($P{FORMA_APRESENTACAO}.intValue() == ReportProperties.AGRUPAR_OPERACAO))
?
    $F{codigoGrupoProduto} +$F{codigoSubGrupoProduto}
:
    (($P{FORMA_APRESENTACAO}.intValue() == ReportProperties.AGRUPAR_CENTRO_CUSTO))
    ?
        $F{codigoCentroCusto}
    :
        null]]></groupExpression>
		<groupHeader>
			<band height="30" splitType="Stretch">
				<rectangle radius="5">
					<reportElement key="rectangle-1" mode="Opaque" x="0" y="17" width="535" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</rectangle>
				<textField evaluationTime="Group" evaluationGroup="FORMA_APRESENTACAO" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-103" mode="Opaque" x="327" y="19" width="50" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*Quantidade*/Bundle.getStringApplication("rotulo_quantidade")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="FORMA_APRESENTACAO" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-104" mode="Opaque" x="451" y="19" width="75" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*Valor*/Bundle.getStringApplication("rotulo_total")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="FORMA_APRESENTACAO" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-105" mode="Transparent" x="21" y="1" width="514" height="15" isRemoveLineWhenBlank="true" forecolor="#000000">
						<printWhenExpression><![CDATA[(($P{FORMA_APRESENTACAO}.intValue() == ReportProperties.AGRUPAR_GRUPO)
    || ($P{FORMA_APRESENTACAO}.intValue() == ReportProperties.AGRUPAR_OPERACAO)
    || ($P{FORMA_APRESENTACAO}.intValue() == ReportProperties.AGRUPAR_CENTRO_CUSTO))
?
    new Boolean(true)
:
    new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="10" isBold="true" isItalic="true" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica-Bold" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*Forma de Apresentacao*/
(($P{FORMA_APRESENTACAO}.intValue() == ReportProperties.AGRUPAR_GRUPO) || ($P{FORMA_APRESENTACAO}.intValue() == ReportProperties.AGRUPAR_OPERACAO))
?
    $F{codigoGrupoProduto} == null
    ?
        Bundle.getStringApplication("rotulo_grupo") + ": " +   Bundle.getStringApplication("rotulo_sem_descricao")
    :
        Bundle.getStringApplication("rotulo_grupo") + ": " + $F{descricaoGrupoProdutoFormatado} + " - " + Bundle.getStringApplication("rotulo_subgrupo") + ": " + $F{descricaoSubGrupoProdutoFormatado}
:
    (($P{FORMA_APRESENTACAO}.intValue() == ReportProperties.AGRUPAR_CENTRO_CUSTO))
    ?
        $F{codigoCentroCusto} == null
        ?
            Bundle.getStringApplication("rotulo_centro_custo") + ": " +   Bundle.getStringApplication("rotulo_nao_definido")
        :
            Bundle.getStringApplication("rotulo_centro_custo") + ": " + $F{descricaoCentroCustoFormatado}
    :
        ""]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="FORMA_APRESENTACAO" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-107" mode="Opaque" x="4" y="19" width="112" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_produto")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="FORMA_APRESENTACAO" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-109" mode="Opaque" x="309" y="19" width="17" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*UN*/Bundle.getStringApplication("rotulo_unidade_abv")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="FORMA_APRESENTACAO" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-111" mode="Opaque" x="379" y="19" width="70" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*vlUnitario*/Bundle.getStringApplication("rotulo_valor_unitario_sigla")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="11" splitType="Stretch">
				<textField evaluationTime="Group" evaluationGroup="FORMA_APRESENTACAO" pattern="#,##0.0000;-#,##0.0000" isBlankWhenNull="true">
					<reportElement key="textField-113" mode="Opaque" x="431" y="2" width="95" height="9" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[(($P{FORMA_APRESENTACAO}.intValue() == ReportProperties.AGRUPAR_GRUPO) || ($P{FORMA_APRESENTACAO}.intValue() == ReportProperties.AGRUPAR_OPERACAO)) ? new Boolean(true) : new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.Double"><![CDATA[$V{ValorTotal} == null ? 0.00 : $V{ValorTotal}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-114" mode="Opaque" x="314" y="2" width="115" height="9" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[(($P{FORMA_APRESENTACAO}.intValue() == ReportProperties.AGRUPAR_GRUPO) || ($P{FORMA_APRESENTACAO}.intValue() == ReportProperties.AGRUPAR_OPERACAO)) ? new Boolean(true) : new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*total*/Bundle.getStringApplication("rotulo_total_sub_grupo") + ": "]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-4" mode="Opaque" x="451" y="1" width="76" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[(($P{FORMA_APRESENTACAO}.intValue() == ReportProperties.AGRUPAR_GRUPO) || ($P{FORMA_APRESENTACAO}.intValue() == ReportProperties.AGRUPAR_OPERACAO)) ? new Boolean(true) : new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="11" splitType="Stretch">
			<textField pattern="###0.00;-###0.00" isBlankWhenNull="false">
				<reportElement key="textField-100" mode="Transparent" x="327" y="1" width="50" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{quantidade}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.0000;-#,##0.0000" isBlankWhenNull="true">
				<reportElement key="textField-101" mode="Transparent" x="451" y="1" width="75" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$V{Total}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-108" mode="Transparent" x="4" y="1" width="302" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" lineSpacing="Single">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricaoFormatado}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-110" mode="Transparent" x="309" y="1" width="17" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" lineSpacing="Single">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{unidade}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.0000;-#,##0.0000" isBlankWhenNull="false">
				<reportElement key="textField-112" mode="Transparent" x="379" y="1" width="70" height="9" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$V{ValorUnitario}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
