package br.com.ksisolucoes.report.vigilancia;

import br.com.celk.report.vigilancia.query.QueryFichaDoencaCreutzfeldtJacob;
import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FichaInvestigacaoAgravoDTOParam;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;

import java.util.LinkedHashMap;

public class ImpressaoFichaInvestigacaoDoencaCreutzfeldtJacob extends AbstractReport<FichaInvestigacaoAgravoDTOParam> {

    private QueryFichaDoencaCreutzfeldtJacob query;

    public ImpressaoFichaInvestigacaoDoencaCreutzfeldtJacob(FichaInvestigacaoAgravoDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        if(query == null){
            query = new QueryFichaDoencaCreutzfeldtJacob();
        }
        return query;
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/vigilancia/pdf/ficha_investigacao_doenca_creutzfeldt_jacob.pdf";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_doenca_creutzfeldt_jacob");
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return param.getTipoArquivo();
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> campos = new LinkedHashMap<>(((QueryFichaDoencaCreutzfeldtJacob)getQuery()).getMapeamentoPlanilhaBase());

        campos.put("criteriosSuspeitaClinica", "_31_criterio_suspeita_clinica");
        campos.put("ocupacaoCbo", "_33_ocupacao");

        campos.put("sinalSintomaDemenciaProgressiva", "_34_demencia");
        campos.put("sinalSintomaMioclonias", "_34_mioclonias");
        campos.put("sinalSintomaDisturbiosVisuais", "_34_disturbios_visuais");
        campos.put("sinalSintomaDisturbiosCerebelares", "_34_disturbios_cerebelares");
        campos.put("sinalSintomaDisestesiasDolorosasPersistentes", "_34_disestesias_dolorosas");
        campos.put("sinalSintomaAtaxia", "_34_ataxia");
        campos.put("sinalSintomaSinaisPiramidais", "_34_sinais_piramidais");
        campos.put("sinalSintomaSinaisExtrapiramidais", "_34_sinais_extrapiramidais");
        campos.put("sinalSintomaMutismoAcinetico", "_34_mutismo_acinetico");
        campos.put("sinalSintomaTranstornosPsiquiatricos", "_34_transtornos_psiquiatricos");
        campos.put("sinalSintomaAlteracoesSono", "_34_alteracoes_sono");

        campos.put("viagemExterior", "_35_viagem_exterior");
        campos.put("dataViagemExterior", "_36_dt_ultima_viagem");
        campos.put("viagemExteriorPais", "_37_pais_viagem_exterior");
        campos.put("familiarApresentouQuadroSemelhante", "_38_familiar_caso_semelhante");
        campos.put("pacienteComeCarneBovina", "_39_paciente_carne_1984");
        campos.put("pacienteVegetariano", "_40_paciente_vegetariano");

        campos.put("exposicaoIantrogenicaDuraMater", "_41_dura_mater");
        campos.put("exposicaoIantrogenicaHormonioCrescimento", "_41_hormonio_crescimento");
        campos.put("exposicaoIantrogenicaTransplanteCorneas", "_41_transplante_corneas");
        campos.put("exposicaoIantrogenicaNeurocirurgias", "_41_neurocirurgias");
        campos.put("exposicaoIantrogenicaTransfusaoSangue", "_41_transfusao_sangue");

        campos.put("resultadoEeg", "_42_resultado_Eeg");
        campos.put("resultadoRessonanciaMagnetica", "_43_resultado_ressonancia");
        campos.put("resultadoProteinaLcr", "_44_resultado_proteina_lcr");
        campos.put("resultadoProteinaTauLcr", "_45_resultado_proteina_tau");
        campos.put("resultadoBiopsiaCerebral", "_46_resultado_biopsia_cerebral");
        campos.put("resultadoNecropsiaCerebral", "_47_resultado_necropsia");
        campos.put("resultadoImunohistoquimicaProteinaPrionica", "_48_resultado_imunohistoquimica");
        campos.put("resultadoAnaliseGenetica", "_49_resultado_genetica");

        campos.put("conclusaoDiagnosticoFinalCid.descricao", "_50_diagnostico_final");
        campos.put("conclusaoFormaClinica", "_51_forma_clinica");
        campos.put("conclusaoEvolucaoCaso", "_52_evolucao_caso");
        campos.put("conclusaoDataObito", "_53_dt_obito");
        campos.put("dataEncerramento", "_54_dt_encerramento");

        campos.put("observacao", "_observacao");

        return campos;
    }
}
