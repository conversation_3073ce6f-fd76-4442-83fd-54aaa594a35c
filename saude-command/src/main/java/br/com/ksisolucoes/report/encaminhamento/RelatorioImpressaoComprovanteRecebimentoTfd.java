/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.encaminhamento;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.encaminhamento.dto.RelatorioImpressaoComprovanteRecebimentoTfdDTOParam;
import br.com.ksisolucoes.report.encaminhamento.query.QueryRelatorioImprimirComprovanteRecebimentoTfd;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Parametro;
import java.util.Collection;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoComprovanteRecebimentoTfd extends AbstractReport<RelatorioImpressaoComprovanteRecebimentoTfdDTOParam> {

    public RelatorioImpressaoComprovanteRecebimentoTfd(RelatorioImpressaoComprovanteRecebimentoTfdDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/encaminhamento/jrxml/relatorio_impressao_comprovante_recebimento_tfd.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_comprovante_entrega_laudo_tfd");
    }

    @Override
    public Collection getCollection() throws DAOException, ValidacaoException {
        Parametro parametro = CargaBasicoPadrao.getInstance().getParametroPadrao();

        this.addParametro("nomePrefeitura", parametro.getPropertyValue(Parametro.PROP_NOME_PREFEITURA));
        this.addParametro("descricaoEmpresa", SessaoAplicacaoImp.getInstance().<Empresa>getEmpresa().getDescricao());

        QueryRelatorioImprimirComprovanteRecebimentoTfd qriec = new QueryRelatorioImprimirComprovanteRecebimentoTfd();
        qriec.setDTOParam(getParam());
        qriec.start();

        return qriec.getResult();
    }
    
}
