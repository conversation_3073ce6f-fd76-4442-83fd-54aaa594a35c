<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_resumo_procedimentos" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="e25ea69a-dae4-447a-b32f-1790a19ce1e5">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.3579476910000023"/>
	<property name="ireport.x" value="826"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioRelacaoLancamentoInventarioDTOParam.FormaApresentacao"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.util.Dinheiro"/>
	<import value="br.com.ksisolucoes.report.ReportProperties"/>
	<import value="br.com.celk.util.Coalesce"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="formaApresentacao" class="br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioRelacaoLancamentoInventarioDTOParam.FormaApresentacao"/>
	<field name="controleInventario" class="br.com.ksisolucoes.vo.entradas.estoque.ControleInventario"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<group name="Header">
		<groupFooter>
			<band height="28"/>
		</groupFooter>
	</group>
	<group name="formaApresentacao">
		<groupExpression><![CDATA[FormaApresentacao.EMPRESA.equals($P{formaApresentacao})?
$F{controleInventario}.getRoEmpresa().getCodigo() :
FormaApresentacao.GRUPO.equals($P{formaApresentacao})?
$F{controleInventario}.getProduto().getSubGrupo().getRoGrupoProduto().getCodigo() :
FormaApresentacao.USUARIO.equals($P{formaApresentacao})?
$F{controleInventario}.getUsuario().getCodigo() :
""]]></groupExpression>
		<groupHeader>
			<band height="32">
				<printWhenExpression><![CDATA[$P{formaApresentacao} != null]]></printWhenExpression>
				<rectangle radius="10">
					<reportElement x="0" y="0" width="802" height="13" uuid="7b2396b6-0646-43a2-8c1d-ab5cf2ef5384"/>
				</rectangle>
				<textField isBlankWhenNull="true">
					<reportElement x="0" y="0" width="802" height="13" uuid="dd6080bd-e6d6-4cc8-834b-0290a5cabebf"/>
					<textElement textAlignment="Center">
						<font fontName="SansSerif" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false"/>
					</textElement>
					<textFieldExpression><![CDATA[FormaApresentacao.EMPRESA.equals($P{formaApresentacao})?
$F{controleInventario}.getRoEmpresa().getDescricao():
FormaApresentacao.GRUPO.equals($P{formaApresentacao})?
$F{controleInventario}.getProduto().getSubGrupo().getRoGrupoProduto().getDescricao() :
FormaApresentacao.USUARIO.equals($P{formaApresentacao})?
$F{controleInventario}.getUsuario().getNome() :
""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="21" width="74" height="11" uuid="ecb09790-46af-4ea0-a37c-84bf9e816587"/>
					<textElement verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_produto")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="31" width="802" height="1" uuid="bb8b85cf-2cd6-456f-a557-d6aaf374456e"/>
				</line>
				<textField>
					<reportElement x="200" y="20" width="74" height="11" uuid="88949849-9e48-48a4-a914-c9cdd0416fd0"/>
					<textElement verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_deposito")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="274" y="20" width="47" height="11" uuid="fc4fee69-d667-492e-8846-eb1aa802a578"/>
					<textElement verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_lote")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="321" y="20" width="55" height="11" uuid="0fd2f449-933b-4b78-bf0c-6666021ab7bd"/>
					<textElement verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quantidade")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="376" y="20" width="47" height="11" uuid="2bc16db9-4943-4532-875b-1adf457626bb"/>
					<textElement verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_situacao")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="423" y="20" width="47" height="11" uuid="efa028f7-d540-43ae-86f4-2189a662faa5"/>
					<textElement verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_lancamento_abv")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="470" y="20" width="52" height="11" uuid="80f7279f-cf2d-4a02-adc0-fc8e82a93ef5"/>
					<textElement verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_usuario")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="565" y="20" width="56" height="11" uuid="5ed7e9fe-96a1-48e2-b8ab-b126299ee737"/>
					<textElement verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_grupo")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="644" y="20" width="63" height="11" uuid="76716a26-1834-4e7f-bb0e-be0c568e4bcc"/>
					<textElement verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_subgrupo")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="746" y="20" width="56" height="11" uuid="e763ac1b-b32f-4d39-8ccf-42dafde8c8e4"/>
					<textElement verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_localizacao")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="30"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch">
			<printWhenExpression><![CDATA[$P{formaApresentacao} == null]]></printWhenExpression>
		</band>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			<textField>
				<reportElement x="0" y="2" width="200" height="11" uuid="4b8ad52b-91da-4e4c-b659-1f6d90e4bac8"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{controleInventario}.getProduto().getDescricaoFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="200" y="2" width="74" height="11" uuid="27afdd6b-4503-41fa-a714-c03a52753c4f"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{controleInventario}.getDeposito().getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="274" y="2" width="47" height="11" uuid="49e4c382-2343-43d7-9f54-a74bf54e41c8"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{controleInventario}.getGrupoEstoque()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="321" y="2" width="47" height="11" uuid="ba121179-9660-400d-b590-251d3f4d462d"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{controleInventario}.getQuantidade().intValue()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="376" y="2" width="47" height="11" uuid="cb6916c2-8bf6-4d5b-b027-56c75e3606eb"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{controleInventario}.getStatus() == 0L ?
Bundle.getStringApplication("rotulo_aberto") :
$F{controleInventario}.getStatus() == 2L ?
Bundle.getStringApplication("rotulo_processado") :
$F{controleInventario}.getStatus() == 5L ?
Bundle.getStringApplication("rotulo_zerado") :
""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="423" y="2" width="47" height="11" uuid="1465c4f7-e0fe-42c7-b8c5-581bd0744b06"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{controleInventario}.getDataLancamento()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="470" y="2" width="95" height="11" uuid="f451047c-814d-4470-b384-3b208fde051d"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{controleInventario}.getUsuario().getNome()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="565" y="2" width="79" height="11" uuid="c3260d1d-887d-4505-9e76-cc77b289da62"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{controleInventario}.getProduto().getSubGrupo().getRoGrupoProduto().getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="644" y="2" width="102" height="11" uuid="68eb310e-dc83-44c7-9744-18c19bbe73fa"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{controleInventario}.getProduto().getSubGrupo().getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="746" y="2" width="56" height="11" uuid="23d78fd2-53e3-49c7-9be0-7f98960b3600"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{controleInventario}.getLocalizacao().getDescricao()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
