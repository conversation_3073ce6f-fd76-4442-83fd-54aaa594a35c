<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_procedimento_atividade_resumido" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="30" bottomMargin="30" uuid="70b103f2-1238-47df-930d-20f73bd78afa">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.3579476910000023"/>
	<property name="ireport.x" value="420"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.FormasUsoHelper"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.report.atividadegrupo.interfaces.dto.RelatorioRelacaoProcedimentoAtividadeDTOParam.FormaApresentacao"/>
	<import value="br.com.ksisolucoes.report.atividadegrupo.interfaces.dto.RelatorioRelacaoProcedimentoAtividadeDTO"/>
	<import value="br.com.ksisolucoes.report.atividadegrupo.interfaces.dto.RelatorioRelacaoProcedimentoAtividadeDTOParam.QuantidadePor"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="formaApresentacao" class="br.com.ksisolucoes.report.atividadegrupo.interfaces.dto.RelatorioRelacaoProcedimentoAtividadeDTOParam.FormaApresentacao"/>
	<parameter name="quantidadePor" class="br.com.ksisolucoes.report.atividadegrupo.interfaces.dto.RelatorioRelacaoProcedimentoAtividadeDTOParam.QuantidadePor"/>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="atividadeGrupo" class="br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo"/>
	<field name="tabelaCbo" class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"/>
	<field name="tipoAtividadeGrupo" class="br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupo"/>
	<field name="procedimento" class="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"/>
	<field name="atividadeGrupoProcedimento" class="br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoProcedimento"/>
	<field name="quantidadePaciente" class="java.lang.Long"/>
	<variable name="totalQuantidadeFA" class="java.lang.Long" resetType="Group" resetGroup="formaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$P{quantidadePor}.equals(QuantidadePor.PARTICIPANTES)
    ?
        $F{quantidadePaciente}
    :
        $F{atividadeGrupoProcedimento}.getQuantidade()]]></variableExpression>
	</variable>
	<variable name="totalGeralQuantidade" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$P{quantidadePor}.equals(QuantidadePor.PARTICIPANTES)
    ?
        $F{quantidadePaciente}
    :
        $F{atividadeGrupoProcedimento}.getQuantidade()]]></variableExpression>
	</variable>
	<group name="geral">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupFooter>
			<band height="18">
				<line>
					<reportElement x="432" y="1" width="103" height="1" uuid="ea615b6c-0324-426d-84a8-41a9fcfecfe6"/>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="369" y="3" width="109" height="12" uuid="07dc6e7d-c8cf-455d-90d8-8b7e568c1fad"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_geral") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="478" y="3" width="57" height="12" uuid="6d20825d-bffa-493e-bf4d-7258b24963ee"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalGeralQuantidade}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="formaApresentacao">
		<groupExpression><![CDATA[FormaApresentacao.UNIDADE.equals($P{formaApresentacao}) ?
    $F{empresa}
:
    FormaApresentacao.TIPO_ATIVIDADE.equals($P{formaApresentacao}) ?
        $F{tipoAtividadeGrupo}
    :
        FormaApresentacao.CBO.equals($P{formaApresentacao}) ?
            $F{tabelaCbo}
        :
            null]]></groupExpression>
		<groupHeader>
			<band height="38">
				<rectangle radius="10">
					<reportElement x="0" y="1" width="535" height="18" uuid="73faf3b9-98d3-4f90-8444-6397ec746eb8"/>
				</rectangle>
				<line>
					<reportElement x="0" y="37" width="535" height="1" uuid="a479e575-158d-42f3-b072-532f91ad1a8c"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="0" y="26" width="466" height="12" uuid="b0047bd8-3042-44e7-a53f-da08f67a75fa"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_procedimento")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="466" y="26" width="69" height="12" uuid="eb93e566-6676-4954-ab40-861f609e6a88"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{quantidadePor}.equals(QuantidadePor.PARTICIPANTES)
    ?
        Bundle.getStringApplication("rotulo_quantidade_paciente_abrv")
    :
        Bundle.getStringApplication("rotulo_quantidade")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" mode="Transparent" x="0" y="4" width="535" height="13" uuid="e70df8d4-aa94-445b-aa29-1d366a6bd66f"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[FormaApresentacao.UNIDADE.equals($P{formaApresentacao}) ?
    Bundle.getStringApplication("rotulo_empresa")+": "+$F{empresa}.getDescricao()
:
    FormaApresentacao.TIPO_ATIVIDADE.equals($P{formaApresentacao}) ?
        Bundle.getStringApplication("rotulo_tipo_atividade")+": "+$F{tipoAtividadeGrupo}.getDescricao()
    :
        FormaApresentacao.CBO.equals($P{formaApresentacao}) ?
            Bundle.getStringApplication("rotulo_cbo")+": "+$F{tabelaCbo}.getDescricao()
        :
            ""]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="21">
				<line>
					<reportElement x="455" y="3" width="80" height="1" uuid="be452fc1-001d-4f2d-abaf-15a74dfa7df1"/>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="393" y="5" width="85" height="12" uuid="448a6f58-35c7-4136-ad23-b95264fe5d63"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="478" y="5" width="57" height="12" uuid="5d3b4b49-f9c4-4578-9b35-9195e1b80701"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalQuantidadeFA}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="14" splitType="Stretch">
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="0" y="0" width="466" height="12" uuid="ad0b9976-66c1-4e01-8e6a-c4f03ecfbbb3"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{procedimento}.getDescricaoFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="466" y="0" width="69" height="12" uuid="65783131-0c31-4038-b68e-b274e03e23a4"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{quantidadePor}.equals(QuantidadePor.PARTICIPANTES)
    ?
        $F{quantidadePaciente}
    :
        $F{atividadeGrupoProcedimento}.getQuantidade()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
