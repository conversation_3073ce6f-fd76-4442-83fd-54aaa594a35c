<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_vacinas_aplicadas" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="6593bd24-0947-4e1e-bf45-a8c14dfdbcd2">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.8150000000000015"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.report.vacina.dto.RelatorioRelacaoVacinasAplicadasDTOParam.FormaApresentacao"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="formaApresentacao" class="br.com.ksisolucoes.report.vacina.dto.RelatorioRelacaoVacinasAplicadasDTOParam.FormaApresentacao"/>
	<parameter name="fabricanteVacina" class="java.lang.Long"/>
	<field name="tipoVacina" class="br.com.ksisolucoes.vo.vacina.TipoVacina"/>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="usuario" class="br.com.ksisolucoes.vo.controle.Usuario"/>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="vacinaAplicacao" class="br.com.ksisolucoes.vo.vacina.VacinaAplicacao"/>
	<field name="faixaEtariaItem" class="br.com.ksisolucoes.vo.basico.FaixaEtariaItem"/>
	<field name="equipeMicroArea" class="br.com.ksisolucoes.vo.basico.EquipeMicroArea"/>
	<field name="vacinaCalendario" class="br.com.ksisolucoes.vo.vacina.VacinaCalendario"/>
	<field name="enderecoUsuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus"/>
	<field name="fabricante" class="br.com.ksisolucoes.vo.entradas.estoque.Fabricante"/>
	<field name="nomeProfissional" class="java.lang.String"/>
	<field name="cboProfissional" class="java.lang.String"/>
	<variable name="totalFA" class="java.lang.Integer" resetType="Group" resetGroup="FormaApresentacao" calculation="Count">
		<variableExpression><![CDATA[$F{vacinaAplicacao}.getCodigo()]]></variableExpression>
	</variable>
	<variable name="totalGeral" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[$F{vacinaAplicacao}.getCodigo()]]></variableExpression>
	</variable>
	<group name="GERAL">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupFooter>
			<band height="13">
				<textField>
					<reportElement x="758" y="2" width="43" height="10" uuid="449581f7-48a8-443f-898b-d532f5464b5b"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalGeral} == null ? "0": $V{totalGeral}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-4" x="666" y="1" width="136" height="1" uuid="9c8a0b72-ead6-46c3-b629-6eb2b3c9799d"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="666" y="2" width="90" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="8d8e578a-4b53-44b9-b904-1a3cffcd9b9a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Total Geral de Vacinas*/
Bundle.getStringApplication("rotulo_total_geral_vacinas") + ": "]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="FormaApresentacao" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[FormaApresentacao.UNIDADE.equals($P{formaApresentacao})
?
    $F{empresa}.getDescricao()
:
    FormaApresentacao.TIPO_VACINA.equals($P{formaApresentacao})
    ?
        $F{tipoVacina}.getDescricao()
    :
        FormaApresentacao.PACIENTE.equals($P{formaApresentacao})
        ?
            $F{usuarioCadsus}.getNome()
        :
            FormaApresentacao.FAIXA_ETARIA.equals($P{formaApresentacao})
            ?
                $F{faixaEtariaItem}.getDescricao()
            :
                FormaApresentacao.AREA.equals($P{formaApresentacao})
                ?
                    $F{equipeMicroArea}.getEquipeArea().getDescricao()
                :
                    FormaApresentacao.MICROAREA.equals($P{formaApresentacao})
                    ?
                        $F{equipeMicroArea}.getDescricaoMicroAreaComArea()
                    :
						FormaApresentacao.PROFISSIONAL.equals($P{formaApresentacao})
						?
							$F{nomeProfissional}
						:
							FormaApresentacao.CBO.equals($P{formaApresentacao})
							?
								$F{cboProfissional}
							:
								null]]></groupExpression>
		<groupHeader>
			<band height="18" splitType="Stretch">
				<textField evaluationTime="Group" evaluationGroup="FormaApresentacao" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-60" x="0" y="0" width="802" height="14" uuid="5c6578ea-1862-442f-913a-6d475e42a00f"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="true" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{formaApresentacao} + ": " +
(
    FormaApresentacao.UNIDADE.equals($P{formaApresentacao})
    ?
        $F{empresa}.getDescricao()
    :
        FormaApresentacao.TIPO_VACINA.equals($P{formaApresentacao})
        ?
            $F{tipoVacina}.getDescricao() != null
            ? $F{tipoVacina}.getDescricao()
            : Bundle.getStringApplication("rotulo_vacina_sem_tipo")
        :
            FormaApresentacao.PACIENTE.equals($P{formaApresentacao})
            ?
                $F{usuarioCadsus}.getNome()
            :
                FormaApresentacao.FAIXA_ETARIA.equals($P{formaApresentacao})
                ?
                    $F{faixaEtariaItem}.getDescricao()
                :
                    FormaApresentacao.AREA.equals($P{formaApresentacao})
                    ?
                        $F{equipeMicroArea}.getEquipeArea().getDescricao() != null
                            ? $F{equipeMicroArea}.getEquipeArea().getDescricao()
                            : "Sem área"
                    :
                        FormaApresentacao.MICROAREA.equals($P{formaApresentacao})
                        ?
                            $F{equipeMicroArea}.getDescricaoMicroAreaComArea()
                        :
							FormaApresentacao.PROFISSIONAL.equals($P{formaApresentacao})
							?
								$F{nomeProfissional}
							:
								FormaApresentacao.CBO.equals($P{formaApresentacao})
								?
									$F{cboProfissional}
								:
                            	null
)]]></textFieldExpression>
				</textField>
			</band>
			<band height="11">
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="364" y="0" width="73" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="8d8e578a-4b53-44b9-b904-1a3cffcd9b9a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Tipo vacina*/
Bundle.getStringApplication("rotulo_tipo_vacina")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-4" x="0" y="10" width="802" height="1" uuid="9c8a0b72-ead6-46c3-b629-6eb2b3c9799d"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="1" y="0" width="40" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="8d8e578a-4b53-44b9-b904-1a3cffcd9b9a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Data*/
Bundle.getStringApplication("rotulo_data")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="41" y="0" width="90" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="8d8e578a-4b53-44b9-b904-1a3cffcd9b9a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Paciente*/
Bundle.getStringApplication("rotulo_paciente")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="207" y="0" width="45" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="8d8e578a-4b53-44b9-b904-1a3cffcd9b9a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Idade*/
Bundle.getStringApplication("rotulo_idade")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="654" y="0" width="39" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="8d8e578a-4b53-44b9-b904-1a3cffcd9b9a">
						<printWhenExpression><![CDATA[$P{formaApresentacao} != $P{formaApresentacao}.UNIDADE]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Unidade*/
Bundle.getStringApplication("rotulo_unidade")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="437" y="0" width="43" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="46adc2f2-e718-4c33-a7bd-268bb02a4acc"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Dose*/
Bundle.getStringApplication("rotulo_dose")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="252" y="0" width="112" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="64e6eacb-c2ed-4e8d-8987-051ea9aaff0e"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Endereço*/
Bundle.getStringApplication("rotulo_endereco")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="557" y="0" width="97" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="120085a7-6992-495d-bda9-1fef831ba6d3"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Grupo Atendimento*/
Bundle.getStringApplication("rotulo_grupo_atendimento")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="120" y="0" width="87" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="3630bab9-47fc-4632-a3cf-41ce7910c26d"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*dt nascimento*/
Bundle.getStringApplication("rotulo_data_de_nascimento")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="480" y="0" width="40" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="ac590ed2-a97e-4b7c-ac75-b67fdd5354f0"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Lote*/
Bundle.getStringApplication("rotulo_lote")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="520" y="0" width="37" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="1a77d3d1-d8f6-4ea3-8153-ec6f1d96bd16"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Validade*/
Bundle.getStringApplication("rotulo_validade")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="695" y="0" width="106" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="597e636e-307c-4171-a06e-7aad653bb156">
						<printWhenExpression><![CDATA[$P{fabricanteVacina} == 1L]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Fabricante Vacina*/
Bundle.getStringApplication("rotulo_fabricante_vacina")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="12" splitType="Stretch">
				<textField>
					<reportElement x="758" y="2" width="43" height="10" uuid="aef48eab-1fe4-4d37-a121-86a60eb1f244"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalFA} == null ? "0":$V{totalFA}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-4" x="688" y="1" width="114" height="1" uuid="9c8a0b72-ead6-46c3-b629-6eb2b3c9799d"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="688" y="2" width="68" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="8d8e578a-4b53-44b9-b904-1a3cffcd9b9a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Total de Vacinas*/
Bundle.getStringApplication("rotulo_total_vacinas") + ": "]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="15" splitType="Stretch">
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" mode="Transparent" x="364" y="0" width="63" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="427722c5-fb41-4aa1-a3bd-3d9ea66ec616"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tipoVacina}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" mode="Transparent" x="1" y="0" width="40" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="427722c5-fb41-4aa1-a3bd-3d9ea66ec616"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{vacinaAplicacao}.getDataAplicacao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" mode="Transparent" x="41" y="0" width="90" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="427722c5-fb41-4aa1-a3bd-3d9ea66ec616"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNome()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" mode="Transparent" x="480" y="0" width="30" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="fd56f2c4-2e49-4429-b34e-c3493ba427c5"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{vacinaAplicacao}.getLote()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" mode="Transparent" x="207" y="0" width="45" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="427722c5-fb41-4aa1-a3bd-3d9ea66ec616"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.getDescricaoIdade($F{usuarioCadsus}.getDataNascimento(), $F{vacinaAplicacao}.getDataAplicacao())]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" mode="Transparent" x="575" y="0" width="79" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="a3d7d6ff-75a6-4a4c-be74-3b0285543bfa"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{vacinaAplicacao}.getDescricaoGrupoAtendimento()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" mode="Transparent" x="120" y="0" width="87" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="e1d0c447-a234-4f87-b3e0-940e0175fa82"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getDataNascimentoFormatado(true)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" mode="Transparent" x="520" y="0" width="37" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="40a48e4f-62d8-421e-ba93-1dd7362b6ffd"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{vacinaAplicacao}.getDataValidadeFormatada()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" mode="Transparent" x="252" y="0" width="112" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="9b31f0c6-135b-481d-b82c-6ff730a41dbf"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{enderecoUsuarioCadsus}.getEnderecoFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" mode="Transparent" x="437" y="0" width="43" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="7616a01e-7012-413e-9ba4-a133d776f199"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{vacinaAplicacao}.getDescricaoDoses()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" mode="Transparent" x="654" y="0" width="39" height="11" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="427722c5-fb41-4aa1-a3bd-3d9ea66ec616">
					<printWhenExpression><![CDATA[$P{formaApresentacao} != $P{formaApresentacao}.UNIDADE]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresa}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" mode="Transparent" x="695" y="0" width="95" height="11" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="a83344fd-e715-4163-bd30-538d10278076">
					<printWhenExpression><![CDATA[$P{fabricanteVacina} == 1L]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{fabricante}.getDescricao()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
