<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_atendimentos_convenio" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="86842486-c6b4-46d4-8b0d-74cb0a16b770">
	<property name="ireport.zoom" value="2.8531167061100047"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="br.com.ksisolucoes.vo.basico.Cidade"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.Convenio"/>
	<import value="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<import value="br.com.celk.util.Coalesce"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"/>
	<import value="br.com.ksisolucoes.vo.basico.Empresa"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="agruparUnidade" class="java.lang.String"/>
	<parameter name="FORMA_APRESENTACAO" class="br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioRelacaoAtendimentosConvenioDTOParam.FormaApresentacao"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa">
		<fieldDescription><![CDATA[empresa]]></fieldDescription>
	</field>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional">
		<fieldDescription><![CDATA[profissional]]></fieldDescription>
	</field>
	<field name="tipoAtendimento" class="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento">
		<fieldDescription><![CDATA[tipoAtendimento]]></fieldDescription>
	</field>
	<field name="convenio" class="br.com.ksisolucoes.vo.prontuario.basico.Convenio"/>
	<field name="quantidadeAtendimento" class="java.lang.Long"/>
	<variable name="FA" class="br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioRelacaoAtendimentosConvenioDTOParam.FormaApresentacao"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="totalFA" class="java.lang.Integer" resetType="Group" resetGroup="Forma Apresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidadeAtendimento}]]></variableExpression>
	</variable>
	<variable name="totalGeral" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidadeAtendimento}]]></variableExpression>
	</variable>
	<group name="Geral">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupFooter>
			<band height="14">
				<staticText>
					<reportElement uuid="331d1da9-61e1-412a-b32a-30b556540ebd" mode="Transparent" x="408" y="2" width="73" height="12"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Total geral:]]></text>
				</staticText>
				<textField>
					<reportElement uuid="4ff348a2-afd5-4b14-8ba9-a06c3a3b847c" positionType="Float" x="482" y="2" width="73" height="12" isRemoveLineWhenBlank="true"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalGeral}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement uuid="2239b8f4-449e-4393-acbb-a47615737e6f" x="426" y="1" width="129" height="1"/>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="unidade">
		<groupExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{agruparUnidade})
?
    $F{empresa}
:
    null]]></groupExpression>
		<groupHeader>
			<band height="17">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{agruparUnidade})]]></printWhenExpression>
				<rectangle radius="5">
					<reportElement uuid="ee8f1ccc-2c30-4937-9df9-24b3118bca68" x="0" y="3" width="555" height="12"/>
				</rectangle>
				<textField>
					<reportElement uuid="d7379551-3396-4cd6-9e54-dd1f67bfa468" mode="Transparent" x="0" y="3" width="555" height="12"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" isItalic="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*unidade*/
Bundle.getStringApplication("rotulo_unidade")+
": "+$F{empresa}.getDescricaoFormatado()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="Forma Apresentacao">
		<groupExpression><![CDATA[$V{FA}.TIPO_ATENDIMENTO.equals($P{FORMA_APRESENTACAO}) ?
    $F{tipoAtendimento}.getCodigo()
:
    $V{FA}.PROFISSIONAL.equals($P{FORMA_APRESENTACAO}) ?
       $F{profissional}.getCodigo()
    :
        null]]></groupExpression>
		<groupHeader>
			<band height="18">
				<printWhenExpression><![CDATA[!$V{FA}.GERAL.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
				<textField>
					<reportElement uuid="868c72b2-2042-41cb-9119-a185d4be637e" positionType="Float" mode="Transparent" x="0" y="5" width="555" height="12"/>
					<textElement>
						<font fontName="Arial" isBold="true" isItalic="true" isUnderline="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{FA}.PROFISSIONAL.equals($P{FORMA_APRESENTACAO}) ?
    $V{BUNDLE}.getStringApplication("rotulo_profissional") + ": " + Coalesce.asString($F{profissional}.getNome(), $V{BUNDLE}.getStringApplication("rotulo_nao_informado"))
:
    $V{FA}.TIPO_ATENDIMENTO.equals($P{FORMA_APRESENTACAO}) ?
        $V{BUNDLE}.getStringApplication("rotulo_tipo_atendimento") + ": " + $F{tipoAtendimento}.getDescricao()
    :
        null]]></textFieldExpression>
				</textField>
			</band>
			<band height="14">
				<textField>
					<reportElement uuid="83496868-0d24-4faf-9d8e-3ee78e53e72c" positionType="Float" x="0" y="0" width="275" height="12"/>
					<textElement>
						<font fontName="Arial"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_convenio")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="2f79b95b-4b16-49c1-a2ec-5500bc210b3e" positionType="Float" x="285" y="0" width="270" height="12"/>
					<textElement textAlignment="Right">
						<font fontName="Arial"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_atendimentos")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement uuid="d956a48b-e15d-460c-971b-ddbb8f23ea03" positionType="Float" x="0" y="12" width="555" height="1"/>
				</line>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="15">
				<staticText>
					<reportElement uuid="2a75900b-1616-4296-976e-010ff87fdebb" mode="Transparent" x="408" y="2" width="73" height="12"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Total:]]></text>
				</staticText>
				<textField>
					<reportElement uuid="9e706315-070b-4e4c-b6c8-5101db771ff6" positionType="Float" x="482" y="2" width="73" height="12" isRemoveLineWhenBlank="true"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalFA}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement uuid="dab649ba-583a-4c40-afa3-cff2be7bc425" x="453" y="1" width="102" height="1"/>
				</line>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement uuid="d710430b-adda-4f45-9e79-58fbd97e6a4f" positionType="Float" x="285" y="0" width="270" height="12"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidadeAtendimento}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement uuid="2424409b-d42b-49b6-b8df-83cfa73a0e39" positionType="Float" x="0" y="0" width="275" height="12"/>
				<textElement>
					<font fontName="Arial" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[Coalesce.asString($F{convenio}.getDescricao(), $V{BUNDLE}.getStringApplication("rotulo_nao_informado"))]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
