<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_impressao_atendimento_painel" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="3b1582db-a51d-45c8-bdae-3a1b497e06e8">
	<property name="ireport.zoom" value="1.610510000000007"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="ocorrencias" class="java.util.List"/>
	<parameter name="imprimirOcorrencias" class="java.lang.Boolean"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="atendimento" class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento">
		<fieldDescription><![CDATA[atendimento]]></fieldDescription>
	</field>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa">
		<fieldDescription><![CDATA[empresa]]></fieldDescription>
	</field>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional">
		<fieldDescription><![CDATA[profissional]]></fieldDescription>
	</field>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus">
		<fieldDescription><![CDATA[usuarioCadsus]]></fieldDescription>
	</field>
	<field name="tipoAtendimento" class="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento">
		<fieldDescription><![CDATA[tipoAtendimento]]></fieldDescription>
	</field>
	<field name="atendimentoPainel" class="br.com.ksisolucoes.vo.prontuario.basico.AtendimentoPainel">
		<fieldDescription><![CDATA[atendimentoPainel]]></fieldDescription>
	</field>
	<variable name="totalChamadas" class="java.lang.Long" resetType="Group" resetGroup="usuarioCadsus" calculation="Count">
		<variableExpression><![CDATA[$F{atendimento}.getCodigo()]]></variableExpression>
	</variable>
	<group name="usuarioCadsus">
		<groupExpression><![CDATA[$F{usuarioCadsus}]]></groupExpression>
		<groupHeader>
			<band height="104">
				<rectangle radius="10">
					<reportElement mode="Transparent" x="0" y="7" width="555" height="62" uuid="55bcae86-4c8a-4b88-ab29-0ea677754279"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isBlankWhenNull="true">
					<reportElement x="8" y="30" width="62" height="12" uuid="607bdb8a-7e20-4730-9202-f497a043eaef"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Sexo*/
Bundle.getStringApplication("rotulo_sexo")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="205" y="30" width="82" height="12" uuid="70327581-36fd-4f2b-8235-b73a353c6596"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Data Nascimento*/
Bundle.getStringApplication("rotulo_data_nascimento")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="392" y="30" width="50" height="12" uuid="248abd8f-7347-471c-9c75-25e3fc70ded0"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Idade*/
Bundle.getStringApplication("rotulo_idade")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="72" y="18" width="480" height="12" uuid="7824d823-542b-4edf-ab0d-e714bdc813ec"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNome()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="72" y="30" width="120" height="12" uuid="b63259ae-6c63-44c0-8185-adea3f32a199"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getSexoFormatado()]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
					<reportElement x="289" y="30" width="101" height="12" uuid="18ebf7d8-9aa6-4f7b-832b-4e2123971146"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getDataNascimento()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="444" y="30" width="108" height="12" uuid="e6122ae1-e3ab-43a1-9a95-f393d6a968d9"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getDescricaoIdade()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="8" y="18" width="62" height="12" uuid="a7c4677e-67ff-4bc0-aea5-f92c39d2d928"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Paciente*/
Bundle.getStringApplication("rotulo_paciente")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement mode="Opaque" x="22" y="1" width="95" height="12" uuid="5552ae68-4e80-40b4-8224-4566d485398a"/>
					<box leftPadding="0"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dados_paciente")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="72" y="42" width="480" height="12" uuid="7824d823-542b-4edf-ab0d-e714bdc813ec"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNomeMae()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="8" y="42" width="62" height="12" uuid="a7c4677e-67ff-4bc0-aea5-f92c39d2d928"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Nome mãe*/
Bundle.getStringApplication("rotulo_nome_mae")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="8" y="54" width="62" height="12" uuid="5d4c4793-5645-4c41-9cc4-0323b59e0771"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*CNS*/
Bundle.getStringApplication("rotulo_cns")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="72" y="54" width="133" height="12" uuid="39a72bce-e5fd-40c0-a4b7-8b5b73d31e54"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getCns()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="205" y="54" width="82" height="12" uuid="36287631-da1d-4432-ae0a-3e27e236b5c5"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Telefone*/
Bundle.getStringApplication("rotulo_telefone")+": "]]></textFieldExpression>
				</textField>
				<textField pattern="(##) ####-#####" isBlankWhenNull="true">
					<reportElement x="289" y="54" width="101" height="12" uuid="311c4003-4cbb-457d-8eeb-693719d85fb9"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getTelefone()]]></textFieldExpression>
				</textField>
				<textField pattern="(##) ####-#####" isBlankWhenNull="true">
					<reportElement x="444" y="54" width="108" height="12" uuid="829bb796-8584-4332-bb14-dbccc23f1240"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getCelular()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="392" y="54" width="50" height="12" uuid="a23732a4-fe78-4a47-87da-1582f933827f"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Celular*/
Bundle.getStringApplication("rotulo_celular")+": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement mode="Opaque" x="0" y="74" width="555" height="15" isPrintWhenDetailOverflows="true" backcolor="#DFDFDF" uuid="313abe6b-ba77-4180-b13f-49bef4c6d330"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Atendimentos*/
Bundle.getStringApplication("rotulo_atendimentos")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="FixRelativeToBottom" x="1" y="89" width="87" height="12" isPrintWhenDetailOverflows="true" uuid="e9249b12-5345-4446-84ed-9fb2375c64a7"/>
					<box bottomPadding="1"/>
					<textElement verticalAlignment="Bottom">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*data/hora Chamada*/
Bundle.getStringApplication("rotulo_data_hora_chamada")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement positionType="FixRelativeToBottom" x="106" y="89" width="256" height="12" isPrintWhenDetailOverflows="true" uuid="fac76082-0171-41d1-aa06-e87831b3ccd7"/>
					<box bottomPadding="1"/>
					<textElement verticalAlignment="Bottom">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Tipo Atendimento*/
Bundle.getStringApplication("rotulo_tipo_atendimento")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement positionType="FixRelativeToBottom" x="365" y="89" width="188" height="12" isPrintWhenDetailOverflows="true" uuid="f61924e7-83f8-480c-a23a-ccbbc131b369"/>
					<box bottomPadding="1"/>
					<textElement verticalAlignment="Bottom">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Profissional*/
Bundle.getStringApplication("rotulo_profissional")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement positionType="FixRelativeToBottom" x="0" y="102" width="555" height="1" uuid="1c16fcdc-86d0-4440-9383-da2b989e0c53"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="14">
				<line>
					<reportElement positionType="FixRelativeToBottom" x="480" y="1" width="75" height="1" uuid="2fe1fcf3-3d5f-4c23-9a17-bae468a27c7c"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField>
					<reportElement x="516" y="2" width="38" height="12" uuid="16a9814e-89d3-4c2c-a5cb-b5b317afc7bb"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalChamadas}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement positionType="FixRelativeToBottom" x="468" y="2" width="45" height="12" isPrintWhenDetailOverflows="true" uuid="*************-498a-becc-859de3ad974a"/>
					<box bottomPadding="1"/>
					<textElement textAlignment="Right" verticalAlignment="Bottom">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Total:*/
Bundle.getStringApplication("rotulo_total") + ":"]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="14">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="365" y="0" width="188" height="13" isPrintWhenDetailOverflows="true" uuid="382270da-c7af-4a76-ba08-1bef19719c3f"/>
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{profissional}.getNome()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="106" y="0" width="256" height="13" isPrintWhenDetailOverflows="true" uuid="f23de395-b518-4ed5-8898-0c0ea3846b0d"/>
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tipoAtendimento}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1" y="0" width="103" height="14" isPrintWhenDetailOverflows="true" uuid="16a5589d-2d7e-4b9e-8abd-9033c00c0da7"/>
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{atendimentoPainel}.getDataChamada()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
