package br.com.ksisolucoes.report.prontuario.exame.testesrapidos.query;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.prontuario.exame.interfaces.dto.ImpressaoTesteRapidoDTO;
import br.com.ksisolucoes.report.prontuario.exame.interfaces.dto.ImpressaoTesteRapidoDTOParam;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.TipoTesteRapido;
import ch.lambdaj.group.Group;
import org.hibernate.Session;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static ch.lambdaj.Lambda.*;

/**
 *
 * <AUTHOR>
 */
public class QueryImpressaoTestesRapidos extends CommandQuery implements ITransferDataReport<ImpressaoTesteRapidoDTOParam, ImpressaoTesteRapidoDTO> {

    private ImpressaoTesteRapidoDTOParam param;
    private List<ImpressaoTesteRapidoDTO> result;

    public QueryImpressaoTestesRapidos(ImpressaoTesteRapidoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(ImpressaoTesteRapidoDTO.class.getName());

        hql.addToSelect("usuarioCadsus.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.dataNascimento", "usuarioCadsus.dataNascimento");
        hql.addToSelect("usuarioCadsus.sexo", "usuarioCadsus.sexo");
        hql.addToSelect("usuarioCadsus.flagEstrangeiro", "usuarioCadsus.flagEstrangeiro");
        hql.addToSelect("usuarioCadsus.nomeMae", "usuarioCadsus.nomeMae");
        hql.addToSelect("usuarioCadsus.apelido", "usuarioCadsus.apelido");
        hql.addToSelect("usuarioCadsus.telefone", "usuarioCadsus.telefone");
        hql.addToSelect("usuarioCadsus.utilizaNomeSocial", "usuarioCadsus.utilizaNomeSocial");

        hql.addToSelect("paisNascimento.codigo", "usuarioCadsus.paisNascimento.codigo");
        hql.addToSelect("paisNascimento.descricao", "usuarioCadsus.paisNascimento.descricao");
        hql.addToSelect("estadoCivil.codigo", "usuarioCadsus.estadoCivil.codigo");
        hql.addToSelect("estadoCivil.descricao", "usuarioCadsus.estadoCivil.descricao");
        hql.addToSelect("escolaridade.codigo", "usuarioCadsus.escolaridade.codigo");
        hql.addToSelect("escolaridade.descricao", "usuarioCadsus.escolaridade.descricao");

        hql.addToSelect("empresa.codigo", "empresa.codigo");
        hql.addToSelect("empresa.descricao", "empresa.descricao");
        hql.addToSelect("empresa.cnes", "empresa.cnes");
        hql.addToSelect("empresa.rua", "empresa.rua");
        hql.addToSelect("empresa.numero", "empresa.numero");
        hql.addToSelect("empresa.complemento", "empresa.complemento");
        hql.addToSelect("empresa.bairro", "empresa.bairro");
        hql.addToSelect("empresa.telefone", "empresa.telefone");

        hql.addToSelect("atendimento.codigo", "atendimento.codigo");
        hql.addToSelect("atendimento.dataAtendimento", "atendimento.dataAtendimento");

        hql.addToSelect("enderecoUsuarioCadsus.logradouro", "enderecoUsuarioCadsus.logradouro");
        hql.addToSelect("enderecoUsuarioCadsus.numeroLogradouro", "enderecoUsuarioCadsus.numeroLogradouro");
        hql.addToSelect("enderecoUsuarioCadsus.cep", "enderecoUsuarioCadsus.cep");
        hql.addToSelect("enderecoUsuarioCadsus.bairro", "enderecoUsuarioCadsus.bairro");
        hql.addToSelect("tipoLogradouro.codigo", "enderecoUsuarioCadsus.tipoLogradouro.codigo");
        hql.addToSelect("tipoLogradouro.descricao", "enderecoUsuarioCadsus.tipoLogradouro.descricao");
        hql.addToSelect("tipoLogradouro.sigla", "enderecoUsuarioCadsus.tipoLogradouro.sigla");

        hql.addToSelect("cidade.codigo", "enderecoUsuarioCadsus.cidade.codigo");
        hql.addToSelect("cidade.descricao", "enderecoUsuarioCadsus.cidade.descricao");
        hql.addToSelect("estado.codigo", "enderecoUsuarioCadsus.cidade.estado.codigo");
        hql.addToSelect("estado.descricao", "enderecoUsuarioCadsus.cidade.estado.descricao");
        hql.addToSelect("estado.sigla", "enderecoUsuarioCadsus.cidade.estado.sigla");

        hql.addToSelect("testeRapido.codigo", "testeRapido.codigo");
        hql.addToSelect("testeRapido.orientacaoSexual", "testeRapido.orientacaoSexual");
        hql.addToSelect("testeRapido.permiteContato", "testeRapido.permiteContato");
        hql.addToSelect("testeRapido.tipoContato", "testeRapido.tipoContato");
        hql.addToSelect("testeRapido.tipoContatoOutro", "testeRapido.tipoContatoOutro");
        hql.addToSelect("testeRapido.apenasComProprio", "testeRapido.apenasComProprio");
        hql.addToSelect("testeRapido.falarCom", "testeRapido.falarCom");
        hql.addToSelect("testeRapido.primeiroTesteHiv", "testeRapido.primeiroTesteHiv");
        hql.addToSelect("testeRapido.comoFicouSabendoServico", "testeRapido.comoFicouSabendoServico");
        hql.addToSelect("testeRapido.comoFicouSabendoOutro", "testeRapido.comoFicouSabendoOutro");
        hql.addToSelect("testeRapido.tipoParceiro", "testeRapido.tipoParceiro");
        hql.addToSelect("testeRapido.numeroParceiro", "testeRapido.numeroParceiro");
        hql.addToSelect("testeRapido.tipoExposicaoRelacaoSexual", "testeRapido.tipoExposicaoRelacaoSexual");
        hql.addToSelect("testeRapido.tipoExposicaoUsuarioDrogasInjetaveis", "testeRapido.tipoExposicaoUsuarioDrogasInjetaveis");
        hql.addToSelect("testeRapido.tipoExposicaoUsuarioDrogas", "testeRapido.tipoExposicaoUsuarioDrogas");
        hql.addToSelect("testeRapido.tipoExposicaoTransmitido", "testeRapido.tipoExposicaoTransmitido");
        hql.addToSelect("testeRapido.tipoExposicaoHemofilico", "testeRapido.tipoExposicaoHemofilico");
        hql.addToSelect("testeRapido.tipoExposicaoSemRisco", "testeRapido.tipoExposicaoSemRisco");
        hql.addToSelect("testeRapido.tipoExposicaoOutro", "testeRapido.tipoExposicaoOutro");
        hql.addToSelect("testeRapido.recorteUsuarioDrogas", "testeRapido.recorteUsuarioDrogas");
        hql.addToSelect("testeRapido.recorteUsuarioDrogasInjetaveis", "testeRapido.recorteUsuarioDrogasInjetaveis");
        hql.addToSelect("testeRapido.recorteProfissionalSexo", "testeRapido.recorteProfissionalSexo");
        hql.addToSelect("testeRapido.recorteSexoHomem", "testeRapido.recorteSexoHomem");
        hql.addToSelect("testeRapido.recorteOutro", "testeRapido.recorteOutro");
        hql.addToSelect("testeRapido.teveDst", "testeRapido.teveDst");
        hql.addToSelect("testeRapido.usoDrogaNunca", "testeRapido.usoDrogaNunca");
        hql.addToSelect("testeRapido.usoDrogaBebeFrequencia", "testeRapido.usoDrogaBebeFrequencia");
        hql.addToSelect("testeRapido.usoDrogaInjetavel", "testeRapido.usoDrogaInjetavel");
        hql.addToSelect("testeRapido.usoDrogaOutras", "testeRapido.usoDrogaOutras");
        hql.addToSelect("testeRapido.usoDrogaParceiro", "testeRapido.usoDrogaParceiro");
        hql.addToSelect("testeRapido.usoCamisinhaParceiroFixo", "testeRapido.usoCamisinhaParceiroFixo");
        hql.addToSelect("testeRapido.usoCamisinhaParceiroEventual", "testeRapido.usoCamisinhaParceiroEventual");
        hql.addToSelect("testeRapido.gestante", "testeRapido.gestante");
        hql.addToSelect("testeRapido.contatoPortadorDoenca", "testeRapido.contatoPortadorDoenca");
        hql.addToSelect("testeRapido.possuiDor", "testeRapido.possuiDor");
        hql.addToSelect("testeRapido.apresentaLesaoMancha", "testeRapido.apresentaLesaoMancha");
        hql.addToSelect("testeRapido.perdaDor", "testeRapido.perdaDor");
        hql.addToSelect("testeRapido.localManchaLesao", "testeRapido.localManchaLesao");



        hql.addToSelect("testeRapidoRealizado.codigo", "testeRapidoRealizado.codigo");
        hql.addToSelect("testeRapidoRealizado.dataPrimeirosSintomas", "testeRapidoRealizado.dataPrimeirosSintomas");
        hql.addToSelect("testeRapidoRealizado.lote", "testeRapidoRealizado.lote");
        hql.addToSelect("testeRapidoRealizado.resultado", "testeRapidoRealizado.resultado");
        hql.addToSelect("testeRapidoRealizado.nrTesteRapido", "testeRapidoRealizado.nrTesteRapido");
        hql.addToSelect("coalesce(testeRapidoRealizado.descricaoResultado, '')","testeRapidoRealizado.descricaoResultado");
        hql.addToSelect("testeRapidoRealizado.tipoAmostra", "testeRapidoRealizado.tipoAmostra");
        hql.addToSelect("testeRapidoRealizado.dataValidade", "testeRapidoRealizado.dataValidade");
        hql.addToSelect("tipoTesteRapido.codigo", "testeRapidoRealizado.tipoTesteRapido.codigo");
        hql.addToSelect("tipoTesteRapido.tipoTeste", "testeRapidoRealizado.tipoTesteRapido.tipoTeste");
        hql.addToSelect("tipoTesteRapido.observacao", "testeRapidoRealizado.tipoTesteRapido.observacao");
        hql.addToSelect("tipoTesteRapido.flagUtilizaFormIst", "testeRapidoRealizado.tipoTesteRapido.flagUtilizaFormIst");
        hql.addToSelect("tipoTesteRapido.descricao", "testeRapidoRealizado.tipoTesteRapido.descricao");

        hql.addToSelect("testeRapidoRealizado.resultadoHematocrito", "testeRapidoRealizado.resultadoHematocrito");
        hql.addToSelect("testeRapidoRealizado.resultadoHemoglobinaGl", "testeRapidoRealizado.resultadoHemoglobinaGl");

        hql.addToSelect("testeRapidoRealizado.loteInfluenza", "testeRapidoRealizado.loteInfluenza");
        hql.addToSelect("testeRapidoRealizado.dataValidadeInfluenza", "testeRapidoRealizado.dataValidadeInfluenza");
        hql.addToSelect("testeRapidoRealizado.resultadoInfluenzaA", "testeRapidoRealizado.resultadoInfluenzaA");
        hql.addToSelect("testeRapidoRealizado.resultadoInfluenzaB", "testeRapidoRealizado.resultadoInfluenzaB");

        hql.addToSelect("resultadoAidsAvancado.codigo", "resultadoAidsAvancado.codigo");
        hql.addToSelect("resultadoAidsAvancado.metodoCd4", "resultadoAidsAvancado.metodoCd4");
        hql.addToSelect("resultadoAidsAvancado.loteMetodoCd4", "resultadoAidsAvancado.loteMetodoCd4");
        hql.addToSelect("resultadoAidsAvancado.resultadoAntigenoCriptococico", "resultadoAidsAvancado.resultadoAntigenoCriptococico");
        hql.addToSelect("resultadoAidsAvancado.loteResultadoAntigenoCriptococico", "resultadoAidsAvancado.loteResultadoAntigenoCriptococico");
        hql.addToSelect("resultadoAidsAvancado.motivoAntigenoCriptococico", "resultadoAidsAvancado.motivoAntigenoCriptococico");
        hql.addToSelect("resultadoAidsAvancado.resultadoAntigenoMicobacterium", "resultadoAidsAvancado.resultadoAntigenoMicobacterium");
        hql.addToSelect("resultadoAidsAvancado.loteResultadoAntigenoMicobacterium", "resultadoAidsAvancado.loteResultadoAntigenoMicobacterium");
        hql.addToSelect("resultadoAidsAvancado.motivoAntigenoMicobacterium", "resultadoAidsAvancado.motivoAntigenoMicobacterium");
        hql.addToSelect("resultadoAidsAvancado.resultadoAntigenoHistoplasmose", "resultadoAidsAvancado.resultadoAntigenoHistoplasmose");
        hql.addToSelect("resultadoAidsAvancado.loteResultadoAntigenoHistoplasmose", "resultadoAidsAvancado.loteResultadoAntigenoHistoplasmose");
        hql.addToSelect("resultadoAidsAvancado.motivoAntigenoHistoplasmose", "resultadoAidsAvancado.motivoAntigenoHistoplasmose");

        hql.addToSelect("testeRapidoConjunto.codigo", "testeRapidoConjunto.codigo");
        hql.addToSelect("testeRapidoConjunto.nomeConjunto", "testeRapidoConjunto.nomeConjunto");
        hql.addToSelect("testeRapidoConjunto.fabricante", "testeRapidoConjunto.fabricante");
        hql.addToSelect("testeRapidoConjunto.metodo", "testeRapidoConjunto.metodo");

        hql.addToSelect("raca.codigo", "usuarioCadsus.raca.codigo");
        hql.addToSelect("raca.descricao", "usuarioCadsus.raca.descricao");

        hql.addToFrom("TesteRapidoRealizado testeRapidoRealizado"
                + " left join testeRapidoRealizado.testeRapido testeRapido"
                + " left join testeRapido.atendimento atendimento"
                + " left join atendimento.usuarioCadsus usuarioCadsus"
                + " left join usuarioCadsus.enderecoDomicilio enderecoDomicilio"
                + " left join usuarioCadsus.raca raca"
                + " left join usuarioCadsus.paisNascimento paisNascimento"
                + " left join usuarioCadsus.estadoCivil estadoCivil"
                + " left join usuarioCadsus.escolaridade escolaridade"
                + " left join atendimento.enderecoUsuarioCadsus enderecoUsuarioCadsus"
                + " left join enderecoUsuarioCadsus.tipoLogradouro tipoLogradouro"
                + " left join enderecoUsuarioCadsus.cidade cidade"
                + " left join cidade.estado estado"
                + " left join atendimento.empresa empresa"
                + " left join testeRapidoRealizado.testeRapidoConjunto testeRapidoConjunto"
                + " left join testeRapidoRealizado.tipoTesteRapido tipoTesteRapido"
                + " left join testeRapidoRealizado.resultadoAidsAvancado resultadoAidsAvancado");

        hql.addToWhereWhithAnd("testeRapidoRealizado.codigo in ", this.param.getCodigoTesteRapidoRealizados());
        hql.addToOrder("testeRapidoRealizado.codigo");
    }

    @Override
    public void setDTOParam(ImpressaoTesteRapidoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<ImpressaoTesteRapidoDTO> getResult() {
        return result;
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(result)) {
            ImpressaoTesteRapidoDTO dto = new ImpressaoTesteRapidoDTO();
            dto.setFichaTesteRapido(Arrays.asList(result.get(0)));

            // TESTE RÁPIDO HEPATITE HIV
            if ( TipoTesteRapido.TipoTeste.HIV.value().equals(result.get(0).getTesteRapidoRealizado().getTipoTesteRapido().getTipoTeste())){
                dto.setTesteRapidoHIV(result);
                dto.setFichaTesteRapido(result);
            }else{
                dto.setFichaTesteRapido(Arrays.asList(result.get(0)));
            }


            Group<ImpressaoTesteRapidoDTO> group = group(result, by(on(ImpressaoTesteRapidoDTO.class).getTesteRapidoRealizado().getTipoTesteRapido().getTipoTeste()));
            { // TESTE RÁPIDO HEPATITE B
                List<ImpressaoTesteRapidoDTO> lstHepatiteB = group.find(TipoTesteRapido.TipoTeste.HEPATITE_B.value());
                dto.setTesteRapidoHepatiteB(lstHepatiteB);
            }

            { // TESTE RÁPIDO HEPATITE C
                List<ImpressaoTesteRapidoDTO> lstHepatiteC = group.find(TipoTesteRapido.TipoTeste.HEPATITE_C.value());
                dto.setTesteRapidoHepatiteC(lstHepatiteC);
            }

            { // TESTE RÁPIDO SÍFILIS
                List<ImpressaoTesteRapidoDTO> lstSifilis = group.find(TipoTesteRapido.TipoTeste.SIFILIS.value());
                dto.setTesteRapidoSifilis(lstSifilis);
            }
            List<ImpressaoTesteRapidoDTO> lstTuberculose;
            { // TESTE RÁPIDO TUBERCULOSE
                lstTuberculose = group.find(TipoTesteRapido.TipoTeste.TB_LAM.value());
                dto.setTesteRapidoTuberculose(lstTuberculose);
            }

            List<ImpressaoTesteRapidoDTO> lstCovid19;
            { // TESTE RÁPIDO COVID-19
                lstCovid19 = group.find(TipoTesteRapido.TipoTeste.COVID_19.value());
                dto.setTesteRapidoCovid19(lstCovid19);
            }
            List<ImpressaoTesteRapidoDTO> lsCovidInfluenza;
            { // TESTE RÁPIDO Covid 19 Influenza A/B
                lsCovidInfluenza = group.find(TipoTesteRapido.TipoTeste.COVID_MAIS_INFLUENZA_AB.value());
                dto.setTesteRapidoCovidInfluenza(lsCovidInfluenza);
            }

            List<ImpressaoTesteRapidoDTO> lstDengue;
            { // TESTE RÁPIDO DENGUE
                lstDengue = group.find(TipoTesteRapido.TipoTeste.DENGUE.value());
                dto.setTesteRapidoDengue(lstDengue);
            }

            List<ImpressaoTesteRapidoDTO> lstInfluenza;
            { // TESTE RÁPIDO INFLUENZA
                lstInfluenza = group.find(TipoTesteRapido.TipoTeste.INFLUENZA.value());
                dto.setTesteRapidoInfluenza(lstInfluenza);
            }

            List<ImpressaoTesteRapidoDTO> lstHanseniase;
            { // TESTE RÁPIDO HANSENIASE
                lstHanseniase = group.find(TipoTesteRapido.TipoTeste.HANSENIASE.value());
                dto.setTesteRapidoHanseniase(lstHanseniase);
            }

            List<ImpressaoTesteRapidoDTO> lstHivSifilis;
            { // TESTE RÁPIDO HIV/SÍFILIS
                lstHivSifilis = group.find(TipoTesteRapido.TipoTeste.HIV_SIFILIS.value());
                dto.setTesteRapidoHivSifilis(lstHivSifilis);
            }

            List<ImpressaoTesteRapidoDTO> lstAidsAvancado;
            { // TESTE RÁPIDO AIDS AVANÇADO
                lstAidsAvancado = group.find(TipoTesteRapido.TipoTeste.AIDS_AVANCADO.value());
                dto.setTesteRapidoAidsAvancado(lstAidsAvancado);
            }

            if (group.find(TipoTesteRapido.TipoTeste.GRAVIDEZ.value()).isEmpty()) {
                if (param.getImprimeSomenteFicha()) {
                    dto.setImprimeFicha(false);
                    dto.setImprimeRelatorioFicha(true);
                } else if (param.getImprimeSomenteResultado()) {
                    dto.setImprimeFicha(true);
                    dto.setImprimeRelatorioFicha(false);
                }

                if (!lstCovid19.isEmpty() && !param.getImprimeSomenteResultado()) {
                    dto.setImprimeFicha(true);
                    dto.setImprimeRelatorioFicha(false);
                }
                if (!lsCovidInfluenza.isEmpty() && !param.getImprimeSomenteResultado()) {
                    dto.setImprimeFicha(true);
                    dto.setImprimeRelatorioFicha(false);
                }

                if (!lstDengue.isEmpty() && !param.getImprimeSomenteResultado()) {
                    dto.setImprimeFicha(true);
                    dto.setImprimeRelatorioFicha(false);
                }

                if (!lstInfluenza.isEmpty() && !param.getImprimeSomenteResultado()) {
                    dto.setImprimeFicha(true);
                    dto.setImprimeRelatorioFicha(false);
                }

                if (!lstHanseniase.isEmpty() && !param.getImprimeSomenteResultado()) {
                    dto.setImprimeFicha(true);
                    dto.setImprimeRelatorioFicha(false);
                }

                if (!lstHivSifilis.isEmpty() && !param.getImprimeSomenteResultado()) {
                    dto.setImprimeFicha(true);
                    dto.setImprimeRelatorioFicha(false);
                }

                if (!lstTuberculose.isEmpty() && !param.getImprimeSomenteResultado()) {
                    dto.setImprimeFicha(true);
                    dto.setImprimeRelatorioFicha(false);
                }

                if (!lstAidsAvancado.isEmpty() && !param.getImprimeSomenteResultado()) {
                    dto.setImprimeFicha(true);
                    dto.setImprimeRelatorioFicha(false);
                }

                if (result.get(0).getTesteRapidoRealizado() != null
                        && result.get(0).getTesteRapidoRealizado().getTipoTesteRapido() != null) {
                    if (RepositoryComponentDefault.SIM_LONG.equals(result.get(0).getTesteRapidoRealizado().getTipoTesteRapido().getFlagUtilizaFormIst())) {
                        dto.setImprimeFicha(true);
                        dto.setImprimeRelatorioFicha(false);
                    }
                }
            }

            result = Arrays.asList(dto);
        }
    }
}
