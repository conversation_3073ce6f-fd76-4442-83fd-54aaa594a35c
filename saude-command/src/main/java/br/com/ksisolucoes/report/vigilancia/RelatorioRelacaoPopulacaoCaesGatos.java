package br.com.ksisolucoes.report.vigilancia;

import br.com.celk.vigilancia.dto.RelatorioRelacaoPopulacaoCaesGatosDTOParam;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.vigilancia.query.QueryRelatorioRelacaoPopulacaoCaesGatos;
import br.com.ksisolucoes.util.Bundle;

public class RelatorioRelacaoPopulacaoCaesGatos extends AbstractReport<RelatorioRelacaoPopulacaoCaesGatosDTOParam> {


    public  RelatorioRelacaoPopulacaoCaesGatos (RelatorioRelacaoPopulacaoCaesGatosDTOParam param){
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioRelacaoPopulacaoCaesGatos();
    }

    @Override
    public String getXML() {
        addParametro("FORMA_APRESENTACAO", param.getFormaApresentacao());
        return "/br/com/celk/report/vigilancia/cva/jrxml/relatorio_relacao_populacao_caes_gatos.jrxml";
    }


    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_caes_gatos");
    }

}
