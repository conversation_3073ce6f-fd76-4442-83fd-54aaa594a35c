package br.com.ksisolucoes.report.vigilancia.query;

import br.com.celk.vigilancia.dto.UsuarioExternoVigilanciaUsuariosDTOParam;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.vo.controle.UsuarioEmpresa;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryConsultaUsuariosVigilancia extends CommandQueryPager<QueryConsultaUsuariosVigilancia> {

    private UsuarioExternoVigilanciaUsuariosDTOParam param;

    public QueryConsultaUsuariosVigilancia(UsuarioExternoVigilanciaUsuariosDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException {
        hql.setTypeSelect(UsuarioEmpresa.class.getName());


        hql.addToSelect("usuarioEmpresa.codigo", "codigo");

        hql.addToSelect("empresa.codigo", "empresa.codigo");

        hql.addToSelect("usuario.codigo", "usuario.codigo");
        hql.addToSelect("usuario.nome", "usuario.nome");
        hql.addToSelect("usuario.login", "usuario.login");
        hql.addToSelect("usuario.status", "usuario.status");

        hql.addToFrom("UsuarioEmpresa usuarioEmpresa"
                + " left join usuarioEmpresa.usuario usuario"
                + " left join usuarioEmpresa.empresa empresa");

        hql.addToWhereWhithAnd(hql.getConsultaLiked("usuario.nome", param.getNome()));
        hql.addToWhereWhithAnd("usuario.login = ", param.getLogin());
        hql.addToWhereWhithAnd("empresa = ", param.getEmpresa());
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
