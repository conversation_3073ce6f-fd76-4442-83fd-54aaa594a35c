<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relacao_usuarios_provisorio" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<import value="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<parameter name="formaApresentacao" class="java.lang.String"/>
	<parameter name="agruparDomicilio" class="java.lang.String"/>
	<parameter name="visualizarEndereco" class="java.lang.String"/>
	<parameter name="FORMA_APRESENTACAO" class="java.lang.String"/>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="descricaoSituacao" class="java.lang.String"/>
	<field name="situacao" class="java.lang.String"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="UTIL" class="br.com.ksisolucoes.util.Util"/>
	<group name="geral">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupFooter>
			<band height="20">
				<rectangle radius="10">
					<reportElement mode="Transparent" x="0" y="4" width="500" height="15"/>
				</rectangle>
				<textField>
					<reportElement x="356" y="6" width="140" height="11"/>
					<textElement>
						<font size="8"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[br.com.ksisolucoes.report.ReportProperties.COM_CNS_NAO_APROVADO+": "+
br.com.ksisolucoes.report.cadsus.interfaces.dto.RelacaoUsuariosProvisoriosDTOParam.getDescricaoSituacao(br.com.ksisolucoes.report.ReportProperties.COM_CNS_NAO_APROVADO)]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="124" y="6" width="104" height="11"/>
					<textElement>
						<font size="8"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[br.com.ksisolucoes.report.ReportProperties.SEM_DOCUMENTOS+": "+
br.com.ksisolucoes.report.cadsus.interfaces.dto.RelacaoUsuariosProvisoriosDTOParam.getDescricaoSituacao(br.com.ksisolucoes.report.ReportProperties.SEM_DOCUMENTOS)]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="8" y="6" width="115" height="11"/>
					<textElement>
						<font size="8"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[br.com.ksisolucoes.report.ReportProperties.SEM_CNS_APROVADO+": "+
br.com.ksisolucoes.report.cadsus.interfaces.dto.RelacaoUsuariosProvisoriosDTOParam.getDescricaoSituacao(br.com.ksisolucoes.report.ReportProperties.SEM_CNS_APROVADO)]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="230" y="6" width="125" height="11"/>
					<textElement>
						<font size="8"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[br.com.ksisolucoes.report.ReportProperties.SEM_CNS_NAO_APROVADO+": "+
br.com.ksisolucoes.report.cadsus.interfaces.dto.RelacaoUsuariosProvisoriosDTOParam.getDescricaoSituacao(br.com.ksisolucoes.report.ReportProperties.SEM_CNS_NAO_APROVADO)]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="EMPRESA_RESPONSAVEL">
		<groupExpression><![CDATA[$F{usuarioCadsus}.getEmpresaResponsavel().getCodigo()]]></groupExpression>
		<groupHeader>
			<band height="33">
				<printWhenExpression><![CDATA[br.com.ksisolucoes.vo.basico.Empresa.REF.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
				<rectangle radius="10">
					<reportElement x="0" y="0" width="802" height="15"/>
				</rectangle>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="0" y="0" width="802" height="15"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_empresa")+": "+
($F{usuarioCadsus}.getEmpresaResponsavel().getCodigo()!=null?
$F{usuarioCadsus}.getEmpresaResponsavel().getDescricaoFormatado():
$V{BUNDLE}.getStringApplication("rotulo_nao_definida"))]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="527" y="17" width="30" height="13"/>
					<textElement verticalAlignment="Top">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_sexo")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="29" width="802" height="1"/>
				</line>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="466" y="17" width="60" height="13"/>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_data_nascimento_abv2")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="558" y="17" width="75" height="13"/>
					<textElement verticalAlignment="Top">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_cpf")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="634" y="17" width="58" height="13"/>
					<textElement verticalAlignment="Top">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_rg")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="693" y="17" width="66" height="13"/>
					<textElement verticalAlignment="Top">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_telefone")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="0" y="17" width="211" height="13"/>
					<textElement verticalAlignment="Top">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_paciente")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="214" y="17" width="190" height="13"/>
					<textElement verticalAlignment="Top">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_mae")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="760" y="17" width="42" height="13"/>
					<textElement verticalAlignment="Top">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_situacao")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="405" y="17" width="60" height="13"/>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_data_cadastro_abv")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="15">
			<printWhenExpression><![CDATA[!br.com.ksisolucoes.vo.basico.Empresa.REF.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="0" width="211" height="13"/>
				<textElement verticalAlignment="Top">
					<font size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_paciente")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="214" y="0" width="190" height="13"/>
				<textElement verticalAlignment="Top">
					<font size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_mae")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="466" y="0" width="60" height="13"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_data_nascimento_abv2")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="527" y="0" width="30" height="13"/>
				<textElement verticalAlignment="Top">
					<font size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_sexo")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="558" y="0" width="75" height="13"/>
				<textElement verticalAlignment="Top">
					<font size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_cpf")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="634" y="0" width="58" height="13"/>
				<textElement verticalAlignment="Top">
					<font size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_rg")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="693" y="0" width="66" height="13"/>
				<textElement verticalAlignment="Top">
					<font size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_telefone")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="760" y="0" width="42" height="13"/>
				<textElement verticalAlignment="Top">
					<font size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_situacao")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="12" width="802" height="1"/>
			</line>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="405" y="0" width="60" height="13"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_data_cadastro_abv")]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="15" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true" hyperlinkType="Reference">
				<reportElement x="0" y="2" width="211" height="11" forecolor="#0000FF"/>
				<textElement>
					<font size="8" isUnderline="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{usuarioCadsus}.getDescricaoFormatado()]]></textFieldExpression>
				<hyperlinkReferenceExpression><![CDATA[UsuarioCadsus.REF]]></hyperlinkReferenceExpression>
				<hyperlinkTooltipExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_ficha_paciente")]]></hyperlinkTooltipExpression>
				<hyperlinkParameter name="UsuarioCadsus">
					<hyperlinkParameterExpression class="java.lang.Long"><![CDATA[$F{usuarioCadsus}.getCodigo()]]></hyperlinkParameterExpression>
				</hyperlinkParameter>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="214" y="2" width="190" height="11"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{usuarioCadsus}.getNomeMae()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="466" y="2" width="60" height="11"/>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{usuarioCadsus}.getDataNascimento()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="527" y="2" width="30" height="11"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{usuarioCadsus}.getSexoAbreviadoFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="558" y="2" width="75" height="11"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{usuarioCadsus}.getCpfFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="634" y="2" width="58" height="11"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{usuarioCadsus}.getRg()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="693" y="2" width="66" height="11"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{usuarioCadsus}.getTelefoneFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="760" y="2" width="42" height="11"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{situacao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="405" y="2" width="60" height="11"/>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{usuarioCadsus}.getDataInclusao()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band/>
	</columnFooter>
	<pageFooter>
		<band/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
