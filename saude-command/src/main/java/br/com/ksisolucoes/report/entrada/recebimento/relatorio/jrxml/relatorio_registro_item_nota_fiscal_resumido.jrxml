<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_registro_item_nota_fiscal_resumido" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="28fa247d-d5ee-4aa7-b2ee-7411d38cb10a">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.6105100000000008"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.vo.entradas.recebimento.RegistroNotaFiscal"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.entradas.estoque.Produto"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="ParamFormaApresentacao" class="java.lang.String" isForPrompting="false"/>
	<field name="produto" class="br.com.ksisolucoes.vo.entradas.estoque.Produto"/>
	<field name="quantidade" class="java.lang.Double"/>
	<field name="quantidadeEstoque" class="java.lang.Double"/>
	<field name="precoUnitario" class="java.lang.Double"/>
	<field name="percentualIpi" class="java.lang.Double"/>
	<field name="valorTotalItem" class="java.lang.Double"/>
	<field name="valorMercadoria" class="java.lang.Double"/>
	<field name="descricaoProduto" class="java.lang.String"/>
	<field name="desconto" class="java.lang.Double"/>
	<field name="valorIpi" class="java.lang.Double"/>
	<field name="percentualIcms" class="java.lang.Double"/>
	<field name="valorIcms" class="java.lang.String"/>
	<field name="registroNotaFiscal" class="br.com.ksisolucoes.vo.entradas.recebimento.RegistroNotaFiscal"/>
	<field name="item" class="java.lang.Long"/>
	<field name="codigo" class="java.lang.Long"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="DATA" class="br.com.ksisolucoes.util.Data"/>
	<variable name="PRODUTO" class="br.com.ksisolucoes.vo.entradas.estoque.Produto">
		<variableExpression><![CDATA[$F{produto}]]></variableExpression>
	</variable>
	<variable name="REGISTRO_ITEM_NOTA_FISCAL" class="br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal"/>
	<variable name="SomaValorTotalNF" class="java.lang.Double" resetType="Group" resetGroup="GrupoNumeroNotaFiscal" calculation="Sum">
		<variableExpression><![CDATA[( $F{valorTotalItem} + Coalesce.asDouble($F{valorIpi}) - Coalesce.asDouble($F{desconto}) )]]></variableExpression>
	</variable>
	<variable name="EMPRESA" class="br.com.ksisolucoes.vo.basico.Empresa">
		<variableExpression><![CDATA[$F{registroNotaFiscal}.getEmpresa()]]></variableExpression>
	</variable>
	<variable name="SomaTotalIpi" class="java.lang.Double" resetType="Group" resetGroup="GrupoNumeroNotaFiscal" calculation="Sum">
		<variableExpression><![CDATA[($F{quantidade} * $F{precoUnitario}) * ($F{percentualIpi} /100)]]></variableExpression>
	</variable>
	<variable name="SomaValor" class="java.lang.Double" resetType="Group" resetGroup="GrupoNumeroNotaFiscal" calculation="Sum">
		<variableExpression><![CDATA[$F{valorMercadoria}]]></variableExpression>
	</variable>
	<variable name="TotalValorTotalNF" class="java.lang.Double" resetType="Group" resetGroup="GrupoFormaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[( $F{quantidade} * $F{precoUnitario} * ( $F{percentualIpi} / 100 + 1 )  - $F{desconto})]]></variableExpression>
	</variable>
	<variable name="TotalGeralValorTotalNF" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[( $F{valorTotalItem} + Coalesce.asDouble($F{valorIpi}) - Coalesce.asDouble($F{desconto}) )]]></variableExpression>
	</variable>
	<variable name="TotalTotalIpi" class="java.lang.Double" resetType="Group" resetGroup="GrupoFormaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[($F{quantidade} * $F{precoUnitario}) * ($F{percentualIpi} /100)]]></variableExpression>
	</variable>
	<variable name="TotalGeralTotalIpi" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[($F{quantidade} * $F{precoUnitario}) * ($F{percentualIpi} /100)]]></variableExpression>
	</variable>
	<variable name="TotalValor" class="java.lang.Double" resetType="Group" resetGroup="GrupoFormaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{valorMercadoria}]]></variableExpression>
	</variable>
	<variable name="TotalGeralValor" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[$F{valorMercadoria}]]></variableExpression>
	</variable>
	<variable name="SomaValorGeral" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{valorMercadoria}]]></variableExpression>
	</variable>
	<variable name="SomaTotalIpiGeral" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[($F{quantidade} * $F{precoUnitario}) * ($F{percentualIpi} /100)]]></variableExpression>
	</variable>
	<variable name="SomaValorTotalGeral" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[( $F{valorTotalItem} + Coalesce.asDouble($F{valorIpi}) - Coalesce.asDouble($F{desconto}) )]]></variableExpression>
	</variable>
	<variable name="SOMA_FA_TOTAL_NF" class="java.lang.Double" resetType="Group" resetGroup="GrupoFormaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[( $F{valorTotalItem} + Coalesce.asDouble($F{valorIpi}) - Coalesce.asDouble($F{desconto}) )]]></variableExpression>
	</variable>
	<variable name="SomaTotalIcms" class="java.lang.Double" resetType="Group" resetGroup="GrupoNumeroNotaFiscal" calculation="Sum">
		<variableExpression><![CDATA[($F{quantidade} * $F{precoUnitario}) * ($F{percentualIcms} /100)]]></variableExpression>
	</variable>
	<variable name="TotalTotalIcms" class="java.lang.Double" resetType="Group" resetGroup="GrupoFormaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[($F{quantidade} * $F{precoUnitario}) * ($F{percentualIcms} /100)]]></variableExpression>
	</variable>
	<variable name="TotalGeralTotalIcms" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[($F{quantidade} * $F{precoUnitario}) * ($F{percentualIcms} /100)]]></variableExpression>
	</variable>
	<variable name="SomaTotalIcmsGeral" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[($F{quantidade} * $F{precoUnitario}) * ($F{percentualIcms} /100)]]></variableExpression>
	</variable>
	<group name="GrupoGeral">
		<groupExpression><![CDATA[]]></groupExpression>
		<groupHeader>
			<band splitType="Stretch"/>
		</groupHeader>
		<groupFooter>
			<band height="12" splitType="Stretch">
				<line>
					<reportElement key="line-3" mode="Opaque" x="228" y="2" width="281" height="1" forecolor="#000000" backcolor="#FFFFFF" uuid="ffcf95d2-9ee4-46ff-b998-77a2849e5751"/>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-101" mode="Opaque" x="416" y="3" width="53" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="1558c90e-13fa-413a-b053-f4198ee8c944"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{SomaValorTotalGeral}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-102" mode="Transparent" x="159" y="3" width="87" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="edcb1116-29ee-406f-a501-aa5c773dc564"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Total Geral*/$V{BUNDLE}.getStringApplication("rotulo_total_geral") + ":"]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-103" mode="Opaque" x="303" y="3" width="54" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="f270e699-16be-4c44-877f-7f3b04a7fef9"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{SomaValorGeral}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-104" mode="Opaque" x="360" y="3" width="53" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="a174fbfa-103f-470c-9f42-afa382f3b2f9"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{SomaTotalIpiGeral}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="GrupoEmpresa" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$V{EMPRESA}.getCodigo()]]></groupExpression>
		<groupHeader>
			<band height="16" splitType="Stretch">
				<rectangle radius="5">
					<reportElement key="rectangle-2" x="0" y="0" width="535" height="16" uuid="3404b96f-d3c8-4dc2-97bd-4968b4ad1c70"/>
				</rectangle>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-1" x="10" y="1" width="515" height="14" uuid="8634b963-1cc6-4cae-a81b-73a325a8df17"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Empresa*/ $V{EMPRESA}.getDescricaoFormatado()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="12" splitType="Stretch">
				<line>
					<reportElement key="line-2" mode="Opaque" x="228" y="2" width="281" height="1" forecolor="#000000" backcolor="#FFFFFF" uuid="7a682985-2d90-46d4-8711-a2f4bf70a83f"/>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-93" mode="Opaque" x="416" y="3" width="53" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="c3d61405-537b-4fe4-b4c7-97161199725c"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TotalGeralValorTotalNF}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-94" mode="Transparent" x="159" y="3" width="87" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="1e155e77-4115-4fd9-8668-875b5ae994c5"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*total unidade*/$V{BUNDLE}.getStringApplication("rotulo_total_unidade") + ":"]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-95" mode="Opaque" x="303" y="3" width="54" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="a5e40def-a563-4c31-bb4a-e5c2f3b89dd7"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TotalGeralValor}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-96" mode="Opaque" x="360" y="3" width="53" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="039875db-5169-444b-a8f1-86dc8ffb95d0"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TotalGeralTotalIpi}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="GrupoFormaApresentacao" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$P{ParamFormaApresentacao}.equals( $V{REGISTRO_ITEM_NOTA_FISCAL}.PROP_PRODUTO )
?
    $V{PRODUTO}.getCodigo()
:
    $P{ParamFormaApresentacao}.equals( RegistroNotaFiscal.PROP_DATA_PORTARIA )
    ?
        $F{registroNotaFiscal}.getDataPortaria()
    :
        $P{ParamFormaApresentacao}.equals(RegistroNotaFiscal.PROP_FORNECEDOR)
        ?
            $F{registroNotaFiscal}.getFornecedor().getCodigo()
        :
            $P{ParamFormaApresentacao}.equals( RegistroNotaFiscal.PROP_DATA_EMISSAO )
            ?
                $F{registroNotaFiscal}.getDataEmissao()
            :
                $P{ParamFormaApresentacao}.equals(Produto.PROP_SUB_GRUPO)
                ?
                    $F{produto}.getSubGrupo().getId().getCodigo()
                :
                    $P{ParamFormaApresentacao}.equals(RegistroNotaFiscal.PROP_TIPO_DOCUMENTO)
                    ?
                        $F{registroNotaFiscal}.getTipoDocumento().getCodigo()
                    :
                        null]]></groupExpression>
		<groupHeader>
			<band height="28" splitType="Stretch">
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="2" y="16" width="40" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="a4c110d6-45a3-4c0d-a5b4-156892e83b96"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Num. NF*/$V{BUNDLE}.getStringApplication("rotulo_num_nota_fiscal")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoFormaApresentacao" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-60" x="0" y="0" width="535" height="14" uuid="32598f7a-4678-4c8e-bfe0-9a7f81da2b92"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*FormaApresentacao*/
$P{ParamFormaApresentacao}.equals($V{REGISTRO_ITEM_NOTA_FISCAL}.PROP_PRODUTO )
?
    ($V{PRODUTO}.getCodigo() == null
    ?
        $V{BUNDLE}.getStringApplication("rotulo_produto") + ":" + $V{BUNDLE}.getStringApplication("rotulo_sem_cadastro")
    :
        ( $V{BUNDLE}.getStringApplication("rotulo_produto") + ": " + $F{produto}.getDescricaoFormatado() ))
:
    $P{ParamFormaApresentacao}.equals( RegistroNotaFiscal.PROP_DATA_PORTARIA )
    ?
        ($V{BUNDLE}.getStringApplication("rotulo_data_entrada") + ": " + $V{DATA}.formatar($F{registroNotaFiscal}.getDataPortaria()) )
    :
        $P{ParamFormaApresentacao}.equals( RegistroNotaFiscal.PROP_FORNECEDOR )
        ?
            ($F{registroNotaFiscal}.getFornecedor().getCodigo() == null
            ?
                $V{BUNDLE}.getStringApplication("rotulo_fornecedor") + ": "+ $V{BUNDLE}.getStringApplication("rotulo_sem_cadastro")
            :
                ( $V{BUNDLE}.getStringApplication("rotulo_fornecedor") + ": " + $F{registroNotaFiscal}.getFornecedor().getDescricaoFormatado() ) )
        :
            $P{ParamFormaApresentacao}.equals( RegistroNotaFiscal.PROP_DATA_EMISSAO )
            ?
                ($V{BUNDLE}.getStringApplication("rotulo_data_emissao") + ": " + $V{DATA}.formatar($F{registroNotaFiscal}.getDataEmissao()) )
            :
                $P{ParamFormaApresentacao}.equals(Produto.PROP_SUB_GRUPO)
                ?
                    ($F{produto}.getSubGrupo().getRoGrupoProduto().getCodigo() == null
                    ?
                        Bundle.getStringApplication("rotulo_grupo") + ": " +   Bundle.getStringApplication("rotulo_sem_descricao")
                    :
                        Bundle.getStringApplication("rotulo_grupo") + ": " + $F{produto}.getSubGrupo().getRoGrupoProduto().getDescricaoFormatado() + " - " + Bundle.getStringApplication("rotulo_subgrupo") + ": " + $F{produto}.getSubGrupo().getDescricaoFormatado())
                :
                    $P{ParamFormaApresentacao}.equals(RegistroNotaFiscal.PROP_TIPO_DOCUMENTO)
                    ?
                        $F{registroNotaFiscal}.getTipoDocumento().getDescricaoFormatado()
                    :
                        ""]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-69" mode="Transparent" x="50" y="16" width="158" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="bcb24d51-**************-08151787005d"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Fornecedor*/$V{BUNDLE}.getStringApplication("rotulo_fornecedor")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-70" mode="Transparent" x="211" y="16" width="40" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="282f5dfe-663f-43f3-baef-24f1945e5119"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Dt Emissao*/$V{BUNDLE}.getStringApplication("rotulo_data_emissao_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-75" mode="Transparent" x="360" y="16" width="53" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="9fc72217-b243-4058-9866-1d4e2fc5ffd4"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*TotalIPI*/$V{BUNDLE}.getStringApplication("rotulo_total_ipi")]]></textFieldExpression>
				</textField>
				<textField pattern=" #,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-85" mode="Transparent" x="416" y="16" width="53" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="ea96bc81-cd6d-42ec-8e37-e9ec2d47b14e"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*totalNF*/$V{BUNDLE}.getStringApplication("rotulo_total_nota_fiscal_abv")]]></textFieldExpression>
				</textField>
				<textField pattern=" #,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-89" mode="Transparent" x="303" y="16" width="54" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="6f3d03a9-bfbc-4596-a951-377ceca3a382"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*ValorMercadoria*/$V{BUNDLE}.getStringApplication("rotulo_valor_mercadoria_abv")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-4" x="0" y="27" width="535" height="1" uuid="82a94177-dafb-4bd9-bcb1-8b101ad61b07"/>
				</line>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="12" splitType="Stretch">
				<line>
					<reportElement key="line-1" mode="Opaque" x="228" y="2" width="281" height="1" forecolor="#000000" backcolor="#FFFFFF" uuid="1bbadd20-0c66-4b70-9ec5-bf102e9d135c"/>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<textField evaluationTime="Group" evaluationGroup="GrupoNumeroNotaFiscal" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-87" mode="Opaque" x="416" y="3" width="53" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="56b08fae-ccb2-46a5-a8bc-af1b260cb0ce"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{SOMA_FA_TOTAL_NF}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-88" mode="Transparent" x="159" y="3" width="87" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="198b1432-1ee2-4d03-be51-324daeca29b5"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*total*/$V{BUNDLE}.getStringApplication("rotulo_total") + ":"]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoFormaApresentacao" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-91" mode="Opaque" x="303" y="3" width="54" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="d2de0948-4cdb-4dc8-8398-1cfe86a5f794"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TotalValor}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoFormaApresentacao" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-92" mode="Opaque" x="360" y="3" width="53" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="e8f6a73a-f5a8-459e-b516-b6b44b8d5d79"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TotalTotalIpi}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="GrupoNumeroNotaFiscal">
		<groupExpression><![CDATA[$F{registroNotaFiscal}.getNumeroNotaFiscal()]]></groupExpression>
		<groupHeader>
			<band height="9" splitType="Stretch">
				<textField evaluationTime="Group" evaluationGroup="GrupoNumeroNotaFiscal" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-57" mode="Opaque" x="2" y="0" width="40" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="ada8be65-59ce-4628-99c8-7ca7ac6d3930"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{registroNotaFiscal}.getNumeroNotaFiscal()]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoNumeroNotaFiscal" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-76" mode="Opaque" x="50" y="0" width="161" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="d015fea5-3f42-4231-bd19-1adecaefb35d"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{registroNotaFiscal}.getFornecedor().getCodigo() == null ? " " :
$F{registroNotaFiscal}.getFornecedor().getDescricaoFormatado()]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoNumeroNotaFiscal" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-77" mode="Opaque" x="211" y="0" width="40" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="4343190b-36c7-444e-a050-b3325d7bdb2a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{DATA}.formatar( $F{registroNotaFiscal}.getDataEmissao() )]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoNumeroNotaFiscal" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-84" mode="Opaque" x="360" y="0" width="53" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="31023908-a42f-4f03-8e14-3def2ef8a3c3"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{SomaTotalIpi}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoNumeroNotaFiscal" pattern=" #,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-86" mode="Opaque" x="416" y="0" width="53" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="d6150bf3-bcce-4512-867d-5c7ccf0c278c"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{SomaValorTotalNF}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoNumeroNotaFiscal" pattern=" #,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-90" mode="Opaque" x="303" y="0" width="54" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="de11096d-17bc-4387-9752-21dedce422bc"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{SomaValor}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band splitType="Stretch"/>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
