package br.com.ksisolucoes.report.indicadores;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.indicadores.dto.RelatorioRelacaoMetasDTOParam;
import br.com.ksisolucoes.report.indicadores.query.QueryRelatorioRelacaoMetas;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoMetas extends AbstractReport<RelatorioRelacaoMetasDTOParam> {

    private RelatorioRelacaoMetasDTOParam param;

    public RelatorioRelacaoMetas(RelatorioRelacaoMetasDTOParam param) {
        super(param);
        this.param = param;
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/indicadores/jrxml/relatorio_relacao_metas.jrxml";
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioRelacaoMetas();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_metas");
    }
}
