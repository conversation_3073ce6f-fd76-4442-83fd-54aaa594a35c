/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.vigilancia;

import br.com.celk.report.vigilancia.query.QueryFichaHantavirose;
import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FichaInvestigacaoAgravoDTOParam;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;

import java.util.LinkedHashMap;

public class ImpressaoFichaInvestigacaoAgravoHantavirose extends AbstractReport<FichaInvestigacaoAgravoDTOParam> {

    private QueryFichaHantavirose query;

    public ImpressaoFichaInvestigacaoAgravoHantavirose(FichaInvestigacaoAgravoDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        if(query == null){
            query = new QueryFichaHantavirose();
        }
        return query;
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> columnsMap = new LinkedHashMap<>(((QueryFichaHantavirose)getQuery()).getMapeamentoPlanilhaBase());

        columnsMap.put("ocupacaoCbo", "_32_ocupacao");

        columnsMap.put("atividadeTreinamentoMilitar", "_33_treinamento_militar");
        columnsMap.put("atividadeDesmatamento", "_33_desmatamento");
        columnsMap.put("atividadeLimpeza", "_33_limpeza");
        columnsMap.put("atividadeMoagem", "_33_moagem");
        columnsMap.put("atividadeDormiu", "_33_dormiu");
        columnsMap.put("atividadeTransporte", "_33_transporte");
        columnsMap.put("atividadePescaCaca", "_33_pesca_caca");
        columnsMap.put("atividadeContatoRato", "_33_contato_rato");
        columnsMap.put("atividadeOutras", "_33_outras");

        columnsMap.put("localPrimeiroAtendimento", "_35_primeiro_atendimento");
        columnsMap.put("sinalSintomaFebre", "_36_febre");
        columnsMap.put("sinalSintomaTosseSeca", "_36_tosse_seca");
        columnsMap.put("sinalSintomaDispneia", "_36_dispineia");
        columnsMap.put("sinalSintomaInsuficienciaRespiratoria", "_36_insuficiencia_resp");
        columnsMap.put("sinalSintomaCefaleia", "_36_cefaleia");
        columnsMap.put("sinalSintomaMialgiaGeneralizada", "_36_mialgia_generalizada");
        columnsMap.put("sinalSintomaDorLombar", "_36_dor_lombar");
        columnsMap.put("sinalSintomaDorAbdominal", "_36_dor_abdominal");
        columnsMap.put("sinalSintomaHipotensao", "_36_hipotensao");
        columnsMap.put("sinalSintomaChoque", "_36_choque");
        columnsMap.put("sinalSintomaNauseasVomito", "_36_nausear_vomito");
        columnsMap.put("sinalSintomaDiarreia", "_36_diarreia");
        columnsMap.put("sinalSintomaDorToracica", "_36_dor_toracica");
        columnsMap.put("sinalSintomaTontura", "_36_tontura");
        columnsMap.put("sinalSintomaInsuficienciaCardiaca", "_36_insuficiencia_cardiaca");
        columnsMap.put("sinalSintomaInsuficienciaRenal", "_36_insuficiencia_renal");
        columnsMap.put("sinalSintomaNeurologico", "_36_neurologico");
        columnsMap.put("sinalSintomaAstenia", "_36_astenia");
        columnsMap.put("sinalSintomaPetequias", "_36_petequias");
        columnsMap.put("sinalSintomaOutrosHemorragico", "_36_hemorragico");
        columnsMap.put("sinalSintomaOutos", "_36_outros");

        columnsMap.put("colheuAmostraSangue", "_37_colheu_amostra_sangue");
        columnsMap.put("resultadoAHematocrito", "_38_resultado_hematocrito");
        columnsMap.put("resultadoATrombocitopenia", "_38_resultado_trombocitopenia");
        columnsMap.put("resultadoALinfocitoAtipico", "_38_resultado_linfocitos_atipicos");
        columnsMap.put("resultadoAUreiaCreatina", "_38_resultado_ureia_creatinina");
        columnsMap.put("resultadoATGO", "_38_resultado_tgo");
        columnsMap.put("resultadoATGP", "_38_resultado_tgp");
        columnsMap.put("resultadoBLeucocito", "_39_resultado_leucocitos");
        columnsMap.put("radiografiaTorax", "_40_radiografia_torax");
        columnsMap.put("alteracoesToraxInfiltradoDifuso", "_41_pulmonar_difuso");
        columnsMap.put("alteracoesToraxInfiltradoLocalizado", "_41_pulmonar_localizado");
        columnsMap.put("alteracoesToraxDerramePleural", "_41_derrame_pleural");
        columnsMap.put("exameSorologicoDataColeta", "_42_data_sorologico");
        columnsMap.put("exameSorologicoResultado", "_43_resultado_sorologico");
        columnsMap.put("imunohistoquimica", "_44_imunohistoquimica");
        columnsMap.put("rtPcrDataColeta", "_45_data_rtpcr");
        columnsMap.put("rtPcrResultado", "_46_resultado_rtpcr");

        columnsMap.put("hospitalizacao", "_47_hospitalizacao");
        columnsMap.put("dataInternacao", "_48_data_internacao");
        columnsMap.put("estado", "_49_estado");
        columnsMap.put("cidade", "_50_cidade");
        columnsMap.put("ibge", "_50_ibge");
        columnsMap.put("hospital", "_51_hospital");
        columnsMap.put("cnes", "_51_cnes");
        columnsMap.put("suporteTerapeuticoRespiradorMecanico", "_52_respirador_mecanico");
        columnsMap.put("suporteTerapeuticoMedicamentoAntiviral", "_52_medicamento_antiviral");
        columnsMap.put("suporteTerapeuticoCorticoide", "_52_corticoide");
        columnsMap.put("suporteTerapeuticoCpapBipap", "_52_cpa_bipap");
        columnsMap.put("suporteTerapeuticoDrogasVasoativas", "_52_drogas_vasoativas");
        columnsMap.put("suporteTerapeuticoAntibiotico", "_52_antibiotico");
        columnsMap.put("suporteTerapeuticoOutros", "_52_outros");

        columnsMap.put("classificacaoFinal", "_53_classificacao_final");
        columnsMap.put("formaClinica", "_54_forma_clinica");
        columnsMap.put("criterioDiagnostico", "_55_criterio_diagnostico");

        columnsMap.put("casoAutoctone", "_56_caso_autoctone");
        columnsMap.put("estado", "_57_estado");
        columnsMap.put("paisLocalInfeccao", "_58_pais");
        columnsMap.put("cidadeLocalInfeccao", "_59_cidade");
        columnsMap.put("ibge", "_59_ibge");
        columnsMap.put("distritoLocalInfeccao", "_60_distrito");
        columnsMap.put("bairroLocalInfeccao", "_61_bairro");
        columnsMap.put("localInfeccaoCaracteristicaZona", "_62_zona");
        columnsMap.put("localInfeccaoTipoAmbiente", "_63_tipo_ambiente");
        columnsMap.put("localInfeccaoTipoAmbienteOutro", "_63_outro");
        columnsMap.put("localInfeccaoLocalizacaoLpiKm", "_64_localizacao_km");
        columnsMap.put("localInfeccaoLocalizacaoLpiDirecao", "_64_localizacao_direcao");

        columnsMap.put("evolucaoCaso", "_65_evolucao_caso");
        columnsMap.put("dataObitoAlta", "_66_data_obito_alta");
        columnsMap.put("obitoAutopsia", "_67_obito_autopsia");
        columnsMap.put("doencaRelacionadaTrabalho", "_68_doenca_trabalho");
        columnsMap.put("observacao", "_observacao");

        return columnsMap;
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/vigilancia/pdf/ficha_investigacao_hantavirose.pdf";
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return param.getTipoArquivo();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_hantavirose");
    }

}
