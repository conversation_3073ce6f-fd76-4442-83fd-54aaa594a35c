package br.com.ksisolucoes.report.encaminhamento.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.encaminhamento.dto.RelacaoAgendamentoTfdDTOParam;
import br.com.ksisolucoes.report.encaminhamento.dto.RelacaoTfdDTO;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;

import java.util.List;
import java.util.Map;

import static br.com.ksisolucoes.report.encaminhamento.dto.RelacaoAgendamentoTfdDTOParam.FormaApresentacao;
import static br.com.ksisolucoes.report.encaminhamento.dto.RelacaoAgendamentoTfdDTOParam.Ordenacao;

/**
 *
 * <AUTHOR>
 */
public class QueryRelacaoAgendamentosTfd extends CommandQuery<QueryRelacaoAgendamentosTfd> implements ITransferDataReport<RelacaoAgendamentoTfdDTOParam, RelacaoTfdDTO> {
  
    private RelacaoAgendamentoTfdDTOParam param;
    private List<RelacaoTfdDTO> result;

    
    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("empresa.codigo", "laudoTfd.empresa.codigo");
        hql.addToSelect("empresa.referencia", "laudoTfd.empresa.referencia");
        hql.addToSelect("empresa.cnes", "laudoTfd.empresa.cnes");
        hql.addToSelect("empresa.descricao", "laudoTfd.empresa.descricao");

        hql.addToSelect("unidadeExecutante.codigo", "laudoTfd.pedidoTfd.solicitacaoAgendamento.unidadeExecutante.codigo");
        hql.addToSelect("unidadeExecutante.referencia", "laudoTfd.pedidoTfd.solicitacaoAgendamento.unidadeExecutante.referencia");
        hql.addToSelect("unidadeExecutante.descricao", "laudoTfd.pedidoTfd.solicitacaoAgendamento.unidadeExecutante.descricao");
        hql.addToSelect("unidadeExecutante.cnes", "laudoTfd.pedidoTfd.solicitacaoAgendamento.unidadeExecutante.cnes");

        hql.addToSelect("cidadeExecutante.codigo", "laudoTfd.pedidoTfd.solicitacaoAgendamento.unidadeExecutante.cidade.codigo");
        hql.addToSelect("cidadeExecutante.descricao", "laudoTfd.pedidoTfd.solicitacaoAgendamento.unidadeExecutante.cidade.descricao");

        hql.addToSelect("profissional.codigo", "laudoTfd.profissional.codigo");
        hql.addToSelect("profissional.referencia", "laudoTfd.profissional.referencia");
        hql.addToSelect("profissional.nome", "laudoTfd.profissional.nome");

        hql.addToSelect("tipoProcedimento.codigo", "laudoTfd.tipoProcedimento.codigo");
        hql.addToSelect("tipoProcedimento.descricao", "laudoTfd.tipoProcedimento.descricao");

        hql.addToSelect("pedidoTfd.numeroPedido", "laudoTfd.pedidoTfd.numeroPedido");
        hql.addToSelect("pedidoTfd.dataEntregaPaciente", "laudoTfd.pedidoTfd.dataEntregaPaciente");
        hql.addToSelect("tfd.status", "laudoTfd.status");

        hql.addToSelect("usuarioCadsus.codigo", "laudoTfd.usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "laudoTfd.usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.dataNascimento", "laudoTfd.usuarioCadsus.dataNascimento");

        hql.addToSelect("solicitacao.dataAgendamento", "laudoTfd.pedidoTfd.solicitacaoAgendamento.dataAgendamento");
        hql.addToSelect("solicitacao.dataConfirmacaoUsuario", "laudoTfd.pedidoTfd.solicitacaoAgendamento.dataConfirmacaoUsuario");
        hql.addToSelect("tfd.dataCadastro", "laudoTfd.dataCadastro");
        hql.addToSelect("tfd.status", "laudoTfd.status");

        hql.setTypeSelect(RelacaoTfdDTO.class.getName());
        hql.addToFrom("LaudoTfd tfd"
                + " left join tfd.empresa empresa"
                + " left join tfd.pedidoTfd pedidoTfd"
                + " left join pedidoTfd.regionalSaude regionalSaude"
                + " left join pedidoTfd.solicitacaoAgendamento solicitacao"
                + " left join solicitacao.unidadeExecutante unidadeExecutante"
                + " left join unidadeExecutante.cidade cidadeExecutante"
                + " left join tfd.profissional profissional"
                + " left join tfd.tipoProcedimento tipoProcedimento"
                + " left join tfd.usuarioCadsus usuarioCadsus");

        if (param.getRegionalSaude() != null) {
            hql.addToWhereWhithAnd("regionalSaude.codigo = ", param.getRegionalSaude().getCodigo());
        }
        hql.addToWhereWhithAnd("empresa in ", param.getEmpresa());
        hql.addToWhereWhithAnd("unidadeExecutante in ", param.getUnidadeExecutante());
        hql.addToWhereWhithAnd("usuarioCadsus in ", param.getUsuarioCadsus());
        hql.addToWhereWhithAnd("profissional ", param.getProfissional());
        hql.addToWhereWhithAnd("tipoProcedimento in ", param.getTipoProcedimento());

        if(param.getTipoPeriodo().equals(RelacaoAgendamentoTfdDTOParam.TipoPeriodo.DATA_AGENDAMENTO)){
            hql.addToWhereWhithAnd("solicitacao.dataAgendamento", Data.adjustRangeHour(param.getPeriodo()));
        }else{
            hql.addToWhereWhithAnd("tfd.dataCadastro", Data.adjustRangeHour(param.getPeriodo()));
        }
        if (RelacaoAgendamentoTfdDTOParam.Contato.NAO_CONTACTADOS.equals(param.getContato())) {
            hql.addToWhereWhithAnd(" solicitacao.dataConfirmacaoUsuario is null ");
        } else if (RelacaoAgendamentoTfdDTOParam.Contato.NAO_CONFIRMADOS.equals(param.getContato())) {
            hql.addToWhereWhithAnd(" pedidoTfd.dataEntregaPaciente is null ");
        }

        hql.addToWhereWhithAnd("tfd.status = ", LaudoTfd.StatusLaudoTfd.AUTORIZADO.value());

        if (FormaApresentacao.UNIDADE.equals(param.getFormaApresentacao())) {
            hql.addToOrder("empresa.descricao");
        } else if (FormaApresentacao.UNIDADE_EXECUTANTE.equals(param.getFormaApresentacao())) {
            hql.addToOrder("unidadeExecutante.descricao");
        } else if (FormaApresentacao.TIPO_PROCEDIMENTO.equals(param.getFormaApresentacao())) {
            hql.addToOrder("tipoProcedimento.descricao");
        } else if (FormaApresentacao.PROFISSIONAL.equals(param.getFormaApresentacao())) {
            hql.addToOrder("profissional.nome");
        } else if (FormaApresentacao.DATA_AGENDAMENTO.equals(param.getFormaApresentacao())) {
            hql.addToOrder("solicitacao.dataAgendamento");
        }

        if (Ordenacao.UNIDADE_EXECUTANTE.equals(param.getOrdenacao())) {
            hql.addToOrder("unidadeExecutante.descricao");
        } else if (Ordenacao.PACIENTE.equals(param.getOrdenacao())) {
            hql.addToOrder("usuarioCadsus.nome");
        } else if (Ordenacao.DATA_AGENDAMENTO.equals(param.getOrdenacao())) {
            hql.addToOrder("solicitacao.dataAgendamento desc");
        }

        hql.addToOrder("tfd.codigo");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelacaoTfdDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(RelacaoAgendamentoTfdDTOParam param) {
        this.param = param;
    }

}
