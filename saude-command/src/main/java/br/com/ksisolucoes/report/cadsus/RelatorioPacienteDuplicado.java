package br.com.ksisolucoes.report.cadsus;

import br.com.ksisolucoes.bo.cadsus.usuariocadsus.QueryCarregarPacienteDuplicado;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.cadsus.interfaces.dto.QueryRelatorioPacienteDuplicadoDTOParam;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioPacienteDuplicado extends AbstractReport<QueryRelatorioPacienteDuplicadoDTOParam>{
    
    public RelatorioPacienteDuplicado(QueryRelatorioPacienteDuplicadoDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/cadsus/jrxml/relatorio_paciente_duplicado.jrxml";
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryCarregarPacienteDuplicado();
    }

    @Override
    public String getTitulo() {
        addParametro("tipoDocumento", getParam().getTipoDocumento());
        return Bundle.getStringApplication("rotulo_relatorio_pacientes_duplicados");
    }
    
}