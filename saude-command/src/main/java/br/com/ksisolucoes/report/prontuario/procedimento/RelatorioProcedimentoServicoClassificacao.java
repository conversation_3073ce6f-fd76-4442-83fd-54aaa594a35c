/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.prontuario.procedimento;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.prontuario.procedimento.query.QueryRelatorioProcedimentoServicoClassificacao;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioProcedimentoServicoClassificacaoParam;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioProcedimentoServicoClassificacao extends AbstractReport<RelatorioProcedimentoServicoClassificacaoParam>{

    public RelatorioProcedimentoServicoClassificacao(RelatorioProcedimentoServicoClassificacaoParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_servico_classificacao.jrxml";
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioProcedimentoServicoClassificacao();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_servico_classificacao");
    }
}
