/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.vigilancia;

import br.com.celk.report.vigilancia.query.QueryFichaCisticercose;
import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FichaInvestigacaoAgravoDTOParam;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;

import java.util.LinkedHashMap;

public class ImpressaoFichaInvestigacaoCisticercose extends AbstractReport<FichaInvestigacaoAgravoDTOParam> {

    private QueryFichaCisticercose query;

    public ImpressaoFichaInvestigacaoCisticercose(FichaInvestigacaoAgravoDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        if(query == null){
            query = new QueryFichaCisticercose();
        }
        return query;
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> columnsMap = new LinkedHashMap<>(((QueryFichaCisticercose)getQuery()).getMapeamentoPlanilhaBase());


        columnsMap.put("dataInvestigacao", "_data_investigacao");
        columnsMap.put("ocupacaoCbo", "_24_ocupacao");

        columnsMap.put("deposicaoFezesDentroChiqueiro", "_25_deposicao_fezes_chiqueiro");
        columnsMap.put("deposicaoFezesDentroRiacho", "_25_deposicao_fezes_riacho");
        columnsMap.put("deposicaoFezesDentroCisterna", "_25_deposicao_fezes_cisterna");
        columnsMap.put("deposicaoFezesOutros", "_25_deposicao_fezes_outros");

        columnsMap.put("saneamentoBasicoAgua", "_26_saneamento_basico_agua");
        columnsMap.put("saneamentoBasicoEncanada", "_26_saneamento_basico_encanada");
        columnsMap.put("saneamentoBasicoEncanadaTratada", "_26_saneamento_basico_encanada_tratada");
        columnsMap.put("saneamentoBasicoCisterna", "_26_saneamento_basico_cisterna");
        columnsMap.put("saneamentoBasicoPoco", "_26_saneamento_basico_poco");
        columnsMap.put("saneamentoBasicoRiacho", "_26_saneamento_basico_riacho");
        columnsMap.put("saneamentoBasicoNascente", "_26_saneamento_basico_nascente");
        columnsMap.put("saneamentoBasicoAcude", "_26_saneamento_basico_acude");
        columnsMap.put("saneamentoBasicoEspecificar", "_26_saneamento_basico_especificar");

        columnsMap.put("localDepositoSanitario", "_27_local_deposito_sanitario");
        columnsMap.put("localDepositoFossa", "_27_local_deposito_fossa");
        columnsMap.put("localDepositoCeuAberto", "_27_local_deposito_ceu_aberto");
        columnsMap.put("localDepositoRiacho", "_27_local_deposito_riacho");

        columnsMap.put("habitosAlimentaresCarneSuina", "_28_habitos_alimentares_carne_suina");
        columnsMap.put("habitosAlimentaresLavaFrutas", "_28_habitos_alimentares_lava_frutas");
        columnsMap.put("habitosAlimentaresVerdurasCruas", "_28_habitos_alimentares_verduras_cruas");

        columnsMap.put("hospitalizacao", "_30_hospitalizacao");

        columnsMap.put("formaInicioEnfermidade", "_31_forma_inicio_enfermidade");

        columnsMap.put("sintomasDisturbioPsiquico", "_32_sintomas_disturbio_psiquico");
        columnsMap.put("sintomasSindromeHipertensao", "_32_sintomas_sindrome_hipertensao");
        columnsMap.put("sintomasCriseConvulsiva", "_32_sintomas_crise_convulsiva");
        columnsMap.put("sintomasCefaleia", "_32_sintomas_cefaleia");
        columnsMap.put("sintomasDisturbiosVisuais", "_32_sintomas_disturbios_visuais");
        columnsMap.put("sintomasVomitos", "_32_sintomas_vomitos");
        columnsMap.put("sintomasAlteracaoMotora", "_32_sintomas_alteracao_motora");
        columnsMap.put("sintomasVertigens", "_32_sintomas_vertigens");
        columnsMap.put("sintomasOutros", "_32_sintomas_outros");

        columnsMap.put("exameLiquorImunofluorescencia", "_32_exame_liquor_imunofluorescencia");
        columnsMap.put("exameLiquorWeimberg", "_32_exame_liquor_weimberg");
        columnsMap.put("exameLiquorElisa", "_32_exame_liquor_elisa");
        columnsMap.put("exameLiquorOutro", "_32_exame_liquor_outro");

        columnsMap.put("exameSoroImunofluorescencia", "_32_exame_soro_imunofluorescencia");
        columnsMap.put("exameSoroWeimberg", "_32_exame_soro_weimberg");
        columnsMap.put("exameSoroElisa", "_32_exame_soro_elisa");
        columnsMap.put("exameSoroOutro", "_32_exame_soro_outro");

        columnsMap.put("exameDiagnosticoImagemRxCranio", "_32_exame_diagnostico_imagem_rx_cranio");
        columnsMap.put("exameDiagnosticoImagemTomografia", "_32_exame_diagnostico_imagem_tomografia");
        columnsMap.put("exameDiagnosticoImagemRessonancia", "_32_exame_diagnostico_imagem_ressonancia");
        columnsMap.put("exameDiagnosticoImagemOutro", "_32_exame_diagnostico_imagem_outro");

        columnsMap.put("exameDiagnosticoDefinitivoCisticercose", "_32_exame_diagnostico_definitivo_cisticercose");
        columnsMap.put("exameDiagnosticoDefinitivoNeurocisticercose", "_32_exame_diagnostico_definitivo_neurocisticercose");
        columnsMap.put("exameDiagnosticoDefinitivoOutro", "_32_exame_diagnostico_definitivo_outro");

        columnsMap.put("dataDiagnosticoDefinitivo", "_32_data_diagnostico_definitivo");

        columnsMap.put("localizacaoAnatomicaPerenquimatosa", "_33_localizacao_anatomica_perenquimatosa");
        columnsMap.put("localizacaoAnatomicaMeningea", "_33_localizacao_anatomica_meningea");
        columnsMap.put("localizacaoAnatomicaIntraventricular", "_33_localizacao_anatomica_intraventricular");
        columnsMap.put("localizacaoAnatomicaMedular", "_33_localizacao_anatomica_medular");
        columnsMap.put("localizacaoAnatomicaSubcutanea", "_33_localizacao_anatomica_subcutanea");
        columnsMap.put("localizacaoAnatomicaOftalmica", "_33_localizacao_anatomica_oftalmica");
        columnsMap.put("localizacaoAnatomicaMuscular", "_33_localizacao_anatomica_muscular");
        columnsMap.put("localizacaoAnatomicaOutros", "_33_localizacao_anatomica_outros");

        columnsMap.put("tratamentoMedicamento", "_34_tratamento_medicamento");
        columnsMap.put("tratamentoEspecifico", "_34_tratamento_especifico");
        columnsMap.put("tratamentoCirurgico", "_34_tratamento_cirurgico");
        columnsMap.put("tratamentoSintomatico", "_34_tratamento_sintomatico");
        columnsMap.put("dataTratamento", "_34_data_tratamento");

        columnsMap.put("evolucaoCaso", "_35_evolucao_caso");
        columnsMap.put("dataObito", "_35_data_obito");

        columnsMap.put("sequelasCrisesEptileptiformes", "_36_sequelas_crises_eptileptiformes");
        columnsMap.put("sequelasDisturbioEquilibrio", "_36_sequelas_disturbio_equilibrio");
        columnsMap.put("sequelasDisturbioEquilibrioDesc", "_36_sequelas_disturbio_equilibrio_desc");
        columnsMap.put("sequelasDisturbiosPsiquiatricos", "_36_sequelas_crises_psiquiatricos");
        columnsMap.put("sequelasDisturbiosPsiquiatricosDesc", "_36_sequelas_crises_psiquiatricos_desc");
        columnsMap.put("sequelasDisturbioVisuais", "_36_sequelas_visuais");
        columnsMap.put("sequelasDisturbiosVisuaisDesc", "_36_sequelas_visuais_desc");
        columnsMap.put("sequelasHidrocefalia", "_36_sequelas_hidrocefalia");
        columnsMap.put("sequelasMotora", "_36_sequelas_motora");
        columnsMap.put("sequelasMotoraDesc", "_36_sequelas_motora_desc");
        columnsMap.put("sequelasSensitiva", "_36_sequelas_sensitiva");
        columnsMap.put("sequelasSensitivaDesc", "_36_sequelas_sensitiva_desc");
        columnsMap.put("sequelasOutros", "_36_sequelas_outros");

        columnsMap.put("observacao", "_observacao");


        return columnsMap;
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/vigilancia/pdf/ficha_investigacao_cisticercose.pdf";
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return param.getTipoArquivo();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_cisticercose");
    }

}
