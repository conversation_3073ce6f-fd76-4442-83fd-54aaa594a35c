<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_atestado_medico" pageWidth="595" pageHeight="842" columnWidth="361" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="0ec2e0d7-e039-472f-bce6-ba91b633f306">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.5026296018031569"/>
	<property name="ireport.x" value="253"/>
	<property name="ireport.y" value="477"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="exibirMedicamentosDispensados" class="java.lang.Boolean"/>
	<parameter name="visualizarBairros" class="java.lang.Boolean"/>
	<parameter name="visualizarMotivoAlta" class="java.lang.Boolean"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="usuariosIdade" class="java.util.List"/>
	<field name="tiposAtendimento" class="java.util.List"/>
	<field name="procedimentos" class="java.util.List"/>
	<field name="cids" class="java.util.List"/>
	<field name="exames" class="java.util.List"/>
	<field name="encaminhamentos" class="java.util.List"/>
	<field name="usuariosSexo" class="java.util.List"/>
	<field name="usuariosTipoDemanda" class="java.util.List"/>
	<field name="usuariosCidade" class="java.util.List"/>
	<field name="medicamentos" class="java.util.List"/>
	<field name="usuariosPrioridade" class="java.util.List"/>
	<field name="usuariosUnidade" class="java.util.List"/>
	<field name="usuariosMicroArea" class="java.util.List"/>
	<field name="usuariosUnidadesAtendimento" class="java.util.List"/>
	<field name="dispensacoesIdade" class="java.util.List"/>
	<field name="doencas" class="java.util.List"/>
	<field name="classificaoAtendimento" class="java.util.List"/>
	<field name="classificacaoRisco" class="java.util.List"/>
	<field name="encaminhamentoEspecialista" class="java.util.List"/>
	<field name="atividadeGrupo" class="java.util.List"/>
	<field name="vacinasAplicadas" class="java.util.List"/>
	<field name="cidades" class="java.util.List"/>
	<field name="cidadesBairros" class="java.util.List"/>
	<field name="racasAtendimento" class="java.util.List"/>
	<field name="atendimentoAlta" class="java.util.List"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="24" splitType="Stretch">
			<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{usuariosSexo})
|| CollectionUtils.isNotNullEmpty($F{usuariosUnidade})
|| CollectionUtils.isNotNullEmpty($F{usuariosCidade})
|| CollectionUtils.isNotNullEmpty($F{usuariosPrioridade})
|| CollectionUtils.isNotNullEmpty($F{usuariosTipoDemanda})]]></printWhenExpression>
			<subreport>
				<reportElement positionType="Float" x="0" y="14" width="107" height="10" uuid="5012ebfa-c929-4491-897e-3971cdc1f822"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource( $F{usuariosSexo} )]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_usuarios_atendidos_1_coluna.jasper"]]></subreportExpression>
			</subreport>
			<subreport>
				<reportElement positionType="Float" x="107" y="14" width="107" height="10" uuid="b81892a0-0d37-498f-869d-b1c9792021bd"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource( $F{usuariosUnidade} )]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_usuarios_atendidos_1_coluna.jasper"]]></subreportExpression>
			</subreport>
			<textField>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="535" height="14" backcolor="#F6F4F2" uuid="6f08d274-2933-452f-8260-6bbf95866e71"/>
				<box>
					<topPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_perfil_usuarios_atendidos")]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement positionType="Float" x="214" y="14" width="107" height="10" uuid="c505af39-bac3-400e-b21d-51008e97b595"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource( $F{usuariosCidade} )]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_usuarios_atendidos_1_coluna.jasper"]]></subreportExpression>
			</subreport>
			<subreport>
				<reportElement positionType="Float" x="321" y="14" width="107" height="10" uuid="c505af39-bac3-400e-b21d-51008e97b595"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource( $F{usuariosPrioridade} )]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_usuarios_atendidos_1_coluna.jasper"]]></subreportExpression>
			</subreport>
			<subreport>
				<reportElement positionType="Float" x="428" y="14" width="107" height="10" uuid="5012ebfa-c929-4491-897e-3971cdc1f822"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource( $F{usuariosTipoDemanda} )]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_usuarios_atendidos_1_coluna.jasper"]]></subreportExpression>
			</subreport>
			<line>
				<reportElement stretchType="RelativeToBandHeight" x="106" y="14" width="1" height="10" uuid="bbd08649-4ee0-4deb-a304-8d917e0babbb"/>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</line>
			<line>
				<reportElement stretchType="RelativeToBandHeight" x="213" y="14" width="1" height="10" uuid="bbd08649-4ee0-4deb-a304-8d917e0babbb"/>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</line>
			<line>
				<reportElement stretchType="RelativeToBandHeight" x="320" y="14" width="1" height="10" uuid="bbd08649-4ee0-4deb-a304-8d917e0babbb"/>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</line>
			<line>
				<reportElement stretchType="RelativeToBandHeight" x="427" y="14" width="1" height="10" uuid="bbd08649-4ee0-4deb-a304-8d917e0babbb"/>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</line>
		</band>
		<band height="11">
			<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{usuariosIdade})]]></printWhenExpression>
			<subreport>
				<reportElement positionType="Float" x="0" y="1" width="535" height="10" uuid="2f96a9c7-f8a8-4ac2-a0e9-2a67c315141b"/>
				<subreportParameter name="titulo">
					<subreportParameterExpression><![CDATA[Bundle.getStringApplication("rotulo_idade")]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{usuariosIdade})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_usuarios_atendidos.jasper"]]></subreportExpression>
			</subreport>
			<line>
				<reportElement positionType="Float" x="0" y="0" width="535" height="1" uuid="8e3fb8bf-0224-4566-947f-18a478e28355"/>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</line>
		</band>
		<band height="11">
			<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{racasAtendimento})]]></printWhenExpression>
			<subreport>
				<reportElement positionType="Float" x="0" y="1" width="535" height="10" uuid="2f96a9c7-f8a8-4ac2-a0e9-2a67c315141b"/>
				<subreportParameter name="titulo">
					<subreportParameterExpression><![CDATA[Bundle.getStringApplication("rotulo_raca")]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{racasAtendimento})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_raca.jasper"]]></subreportExpression>
			</subreport>
			<line>
				<reportElement positionType="Float" x="0" y="0" width="535" height="1" uuid="8e3fb8bf-0224-4566-947f-18a478e28355"/>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</line>
		</band>
		<band height="11">
			<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{usuariosMicroArea})]]></printWhenExpression>
			<subreport>
				<reportElement positionType="Float" x="0" y="1" width="535" height="10" isRemoveLineWhenBlank="true" uuid="2f96a9c7-f8a8-4ac2-a0e9-2a67c315141b"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{usuariosMicroArea})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_usuarios_atendidos_micro_area.jasper"]]></subreportExpression>
			</subreport>
			<line>
				<reportElement positionType="Float" x="0" y="0" width="535" height="1" uuid="8e3fb8bf-0224-4566-947f-18a478e28355"/>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</line>
		</band>
		<band height="11">
			<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{usuariosUnidadesAtendimento})]]></printWhenExpression>
			<subreport>
				<reportElement positionType="Float" x="0" y="1" width="535" height="10" isRemoveLineWhenBlank="true" uuid="2f96a9c7-f8a8-4ac2-a0e9-2a67c315141b"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{usuariosUnidadesAtendimento})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_usuarios_atendidos_unidades_atendimento.jasper"]]></subreportExpression>
			</subreport>
			<line>
				<reportElement positionType="Float" x="0" y="0" width="535" height="1" uuid="8e3fb8bf-0224-4566-947f-18a478e28355"/>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</line>
		</band>
		<band height="25">
			<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{classificacaoRisco})]]></printWhenExpression>
			<textField>
				<reportElement positionType="Float" mode="Opaque" x="0" y="1" width="535" height="14" backcolor="#F6F4F2" uuid="10634a49-192f-4901-904a-a2b76e853e9d"/>
				<box>
					<topPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_classificacao_risco")]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement positionType="Float" x="0" y="15" width="535" height="10" uuid="ea319cb0-fa71-46cc-8c47-9cc040e52704"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{classificacaoRisco})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_classificacao_risco.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="24">
			<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{tiposAtendimento})]]></printWhenExpression>
			<subreport>
				<reportElement positionType="Float" x="0" y="14" width="535" height="10" uuid="b4918e19-7f63-4430-ae61-cb4f1b111c25"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{tiposAtendimento})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento.jasper"]]></subreportExpression>
			</subreport>
			<textField>
				<reportElement positionType="Float" mode="Opaque" x="0" y="1" width="535" height="14" backcolor="#F6F4F2" uuid="09039101-cb09-43b8-9459-1b1ed55c7dca"/>
				<box>
					<topPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_tipos_atendimentos")]]></textFieldExpression>
			</textField>
		</band>
		<band height="24">
			<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{procedimentos})]]></printWhenExpression>
			<subreport>
				<reportElement positionType="Float" x="0" y="14" width="535" height="10" uuid="b9ec0ac9-798d-490d-942f-03229da0428f"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{procedimentos})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_codigo.jasper"]]></subreportExpression>
			</subreport>
			<textField>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="535" height="14" backcolor="#F6F4F2" uuid="c95ae774-4640-45ff-8da2-5cf562684ce0"/>
				<box>
					<topPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_procedimentos_executados")]]></textFieldExpression>
			</textField>
		</band>
		<band height="24">
			<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{cids})]]></printWhenExpression>
			<textField>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="535" height="14" backcolor="#F6F4F2" uuid="7abac4d3-05ca-439a-a43e-5a0aef075d70"/>
				<box>
					<topPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cid")]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement positionType="Float" x="0" y="14" width="535" height="10" uuid="ce43ab1b-33a4-416f-ab18-1bb80f7450d1"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{cids})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_codigo_cid.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="24">
			<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{exames})]]></printWhenExpression>
			<subreport>
				<reportElement positionType="Float" x="0" y="14" width="535" height="10" uuid="484d6d91-f330-4b56-9ed0-e05fcada3edc"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{exames})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento.jasper"]]></subreportExpression>
			</subreport>
			<textField>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="535" height="14" backcolor="#F6F4F2" uuid="d4a3d3fe-f8af-43c1-9200-d40bfe3a47d9"/>
				<box>
					<topPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_exames_ofertados")]]></textFieldExpression>
			</textField>
		</band>
		<band height="24">
			<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{doencas})]]></printWhenExpression>
			<textField>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="535" height="14" backcolor="#F6F4F2" uuid="d4a3d3fe-f8af-43c1-9200-d40bfe3a47d9"/>
				<box>
					<topPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_doencas_condicoes_referidas")]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement positionType="Float" x="0" y="14" width="535" height="10" uuid="484d6d91-f330-4b56-9ed0-e05fcada3edc"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{doencas})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_doencas.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="24">
			<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{encaminhamentos})]]></printWhenExpression>
			<subreport>
				<reportElement positionType="Float" x="0" y="14" width="535" height="10" uuid="ea319cb0-fa71-46cc-8c47-9cc040e52704"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{encaminhamentos})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento.jasper"]]></subreportExpression>
			</subreport>
			<textField>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="535" height="14" backcolor="#F6F4F2" uuid="10634a49-192f-4901-904a-a2b76e853e9d"/>
				<box>
					<topPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_encaminhamento")]]></textFieldExpression>
			</textField>
		</band>
		<band height="24">
			<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{encaminhamentoEspecialista})]]></printWhenExpression>
			<subreport>
				<reportElement positionType="Float" x="0" y="14" width="535" height="10" uuid="ea319cb0-fa71-46cc-8c47-9cc040e52704"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{encaminhamentoEspecialista})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_encaminhamento_especialista.jasper"]]></subreportExpression>
			</subreport>
			<textField>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="535" height="14" backcolor="#F6F4F2" uuid="10634a49-192f-4901-904a-a2b76e853e9d"/>
				<box>
					<topPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_encaminhamento_especialista")]]></textFieldExpression>
			</textField>
		</band>
		<band height="24">
			<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{classificaoAtendimento})]]></printWhenExpression>
			<subreport>
				<reportElement positionType="Float" x="0" y="14" width="535" height="10" uuid="ea319cb0-fa71-46cc-8c47-9cc040e52704"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{classificaoAtendimento})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento.jasper"]]></subreportExpression>
			</subreport>
			<textField>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="535" height="14" backcolor="#F6F4F2" uuid="10634a49-192f-4901-904a-a2b76e853e9d"/>
				<box>
					<topPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_classificacao_atendimentos")]]></textFieldExpression>
			</textField>
		</band>
		<band height="24">
			<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{atividadeGrupo})]]></printWhenExpression>
			<textField>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="535" height="14" backcolor="#F6F4F2" uuid="6397ad1c-e698-4ea3-a9b0-9de29cfc8e34"/>
				<box>
					<topPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_atividade_grupo")]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement positionType="Float" x="0" y="14" width="535" height="10" uuid="ef610bdb-b020-4a7e-a724-f2cdedf0778c"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{atividadeGrupo})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_atividade_grupo.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="24">
			<printWhenExpression><![CDATA[$P{exibirMedicamentosDispensados} && CollectionUtils.isNotNullEmpty($F{medicamentos})]]></printWhenExpression>
			<subreport>
				<reportElement positionType="Float" x="0" y="14" width="535" height="10" uuid="5730dcb6-ca74-4d0b-9728-eec50f27badc"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{medicamentos})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_quantidade.jasper"]]></subreportExpression>
			</subreport>
			<textField>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="535" height="14" backcolor="#F6F4F2" uuid="84c2d0a4-4d08-480f-95f5-decc8be35d1a"/>
				<box>
					<topPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_medicamentos_dispensados")]]></textFieldExpression>
			</textField>
		</band>
		<band height="24">
			<printWhenExpression><![CDATA[$P{exibirMedicamentosDispensados} && CollectionUtils.isNotNullEmpty($F{dispensacoesIdade})]]></printWhenExpression>
			<textField>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="535" height="14" backcolor="#F6F4F2" uuid="84c2d0a4-4d08-480f-95f5-decc8be35d1a"/>
				<box>
					<topPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dispensacoes_idade")]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement positionType="Float" x="0" y="14" width="535" height="10" uuid="5730dcb6-ca74-4d0b-9728-eec50f27badc"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{dispensacoesIdade})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_dispensacao_idade.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="24">
			<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{vacinasAplicadas})]]></printWhenExpression>
			<subreport>
				<reportElement positionType="Float" x="0" y="14" width="535" height="10" uuid="5730dcb6-ca74-4d0b-9728-eec50f27badc"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{vacinasAplicadas})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_vacinas_aplicadas.jasper"]]></subreportExpression>
			</subreport>
			<textField>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="535" height="14" backcolor="#F6F4F2" uuid="84c2d0a4-4d08-480f-95f5-decc8be35d1a"/>
				<box>
					<topPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_vacinas_aplicadas")]]></textFieldExpression>
			</textField>
		</band>
		<band height="24">
			<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{cidades})]]></printWhenExpression>
			<subreport>
				<reportElement positionType="Float" x="0" y="14" width="535" height="10" uuid="76e481db-cd99-4e67-a3c5-4dcbcff772e9"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{cidades})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_cidades.jasper"]]></subreportExpression>
			</subreport>
			<textField>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="535" height="14" backcolor="#F6F4F2" uuid="4c6e0a6f-6b11-4656-88b6-19d4fa8c208a"/>
				<box>
					<topPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cidade")]]></textFieldExpression>
			</textField>
		</band>
		<band height="25">
			<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{cidadesBairros}) && $P{visualizarBairros}]]></printWhenExpression>
			<textField>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="535" height="14" backcolor="#F6F4F2" uuid="46ab3ba5-f74e-43da-8d08-ca7e4b230e2f"/>
				<box>
					<topPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cidade_bairro")]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement positionType="Float" x="0" y="14" width="535" height="10" uuid="7e51ed91-2456-4bed-8c18-67449675e593"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{cidadesBairros})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_cidades_bairros.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="50">
			<printWhenExpression><![CDATA[CollectionUtils.isNotNullEmpty($F{atendimentoAlta}) && $P{visualizarMotivoAlta}]]></printWhenExpression>
			<textField>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="535" height="14" backcolor="#F6F4F2" uuid="780e88c3-2596-455a-bed1-d097f63f943c"/>
				<box>
					<topPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_atendimento_alta")]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement positionType="Float" x="0" y="14" width="535" height="10" uuid="a432388f-4a5d-4f37-bb2b-00e9dfe83bd4"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{atendimentoAlta})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_alta.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
