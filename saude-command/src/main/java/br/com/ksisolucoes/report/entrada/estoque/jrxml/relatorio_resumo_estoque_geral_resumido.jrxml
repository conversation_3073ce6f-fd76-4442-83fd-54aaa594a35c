<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_resumo_estoque_geral_resumido" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="d8f33d5b-984a-4fda-bfc8-95ebb4f784a5">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.zoom" value="1.9487171000000014"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.util.Valor"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="FormaApresentacao" class="java.lang.Integer" isForPrompting="false"/>
	<parameter name="AgruparEmpresa" class="java.lang.String"/>
	<parameter name="tipoMovimentacao" class="java.lang.String"/>
	<field name="codigoEmpresa" class="java.lang.String"/>
	<field name="descricaoEmpresa" class="java.lang.String"/>
	<field name="codigoProduto" class="java.lang.String"/>
	<field name="descricaoProduto" class="java.lang.String"/>
	<field name="unidadeProduto" class="java.lang.String"/>
	<field name="descricaoTipoDocumento" class="java.lang.String"/>
	<field name="flagSiglaTipoDocumento" class="java.lang.String"/>
	<field name="flagTipoMovimentoTipoDocumento" class="java.lang.String"/>
	<field name="quantidade" class="java.lang.Double"/>
	<field name="precoMedio" class="java.lang.Double"/>
	<field name="precoCusto" class="java.lang.Double"/>
	<field name="precoUnitario" class="java.lang.Double"/>
	<field name="codigoGrupoProduto" class="java.lang.Double"/>
	<field name="descricaoGrupoProduto" class="java.lang.String"/>
	<field name="codigoSubGrupo" class="java.lang.Double"/>
	<field name="descricaoSubGrupo" class="java.lang.String"/>
	<field name="descricaoFormatadoSubGrupo" class="java.lang.String"/>
	<field name="descricaoFormatadoGrupoProduto" class="java.lang.String"/>
	<field name="descricaoFormatadoProduto" class="java.lang.String"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="DATA" class="br.com.ksisolucoes.util.Data"/>
	<variable name="TIPO_DOCUMENTO" class="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"/>
	<variable name="QUANTIDADE_ENTRADA" class="java.lang.Double" resetType="Group" resetGroup="GrupoProduto" calculation="Sum">
		<variableExpression><![CDATA[$F{flagTipoMovimentoTipoDocumento}.equals($V{TIPO_DOCUMENTO}.IS_ENTRADA) ? $F{quantidade}:0]]></variableExpression>
	</variable>
	<variable name="TOTAL_CUSTO" class="java.lang.Double" resetType="Group" resetGroup="GrupoProduto" calculation="Sum">
		<variableExpression><![CDATA[$F{flagTipoMovimentoTipoDocumento}.equals($V{TIPO_DOCUMENTO}.IS_ENTRADA) ? ($F{quantidade} * $F{precoMedio}): 0]]></variableExpression>
	</variable>
	<variable name="PRECO_MEDIO" class="java.lang.Double" resetType="Group" resetGroup="GrupoProduto" incrementType="Group" incrementGroup="GrupoProduto" calculation="Sum">
		<variableExpression><![CDATA[(($V{TOTAL_CUSTO} != 0 && $V{QUANTIDADE_ENTRADA} != 0 )? ($V{TOTAL_CUSTO}/$V{QUANTIDADE_ENTRADA}): 0)]]></variableExpression>
	</variable>
	<variable name="QUANTIDADE_SAIDA" class="java.lang.Double" resetType="Group" resetGroup="GrupoProduto" calculation="Sum">
		<variableExpression><![CDATA[$F{flagTipoMovimentoTipoDocumento}.equals($V{TIPO_DOCUMENTO}.IS_SAIDA) ? $F{quantidade}:0]]></variableExpression>
	</variable>
	<variable name="TOTAL_VENDA" class="java.lang.Double" resetType="Group" resetGroup="GrupoProduto" calculation="Sum">
		<variableExpression><![CDATA[$F{flagTipoMovimentoTipoDocumento}.equals($V{TIPO_DOCUMENTO}.IS_SAIDA) ? ($F{quantidade} * $F{precoUnitario}):0]]></variableExpression>
	</variable>
	<variable name="PRECO_MEDIO_VENDA" class="java.lang.Double" resetType="Group" resetGroup="GrupoProduto" incrementType="Group" incrementGroup="GrupoProduto" calculation="Sum">
		<variableExpression><![CDATA[(($V{QUANTIDADE_SAIDA} != 0 && $V{TOTAL_VENDA} != 0) ? ($V{TOTAL_VENDA} / $V{QUANTIDADE_SAIDA}): 0)]]></variableExpression>
	</variable>
	<variable name="MARGEM" class="java.lang.Double" resetType="Group" resetGroup="GrupoProduto" calculation="Sum">
		<variableExpression><![CDATA[(($V{TOTAL_CUSTO} != 0 &&  $V{TOTAL_VENDA} != 0 ) ? ((($V{TOTAL_VENDA}/$V{TOTAL_CUSTO})-1)*100) : 0)]]></variableExpression>
	</variable>
	<variable name="totalTotalCusto" class="java.lang.Double" incrementType="Group" incrementGroup="GrupoProduto" calculation="Sum">
		<variableExpression><![CDATA[$V{TOTAL_CUSTO}]]></variableExpression>
	</variable>
	<variable name="totalTotalVenda" class="java.lang.Double" incrementType="Group" incrementGroup="GrupoProduto" calculation="Sum">
		<variableExpression><![CDATA[$V{TOTAL_VENDA}]]></variableExpression>
	</variable>
	<group name="Geral">
		<groupFooter>
			<band height="12">
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GrupoProduto" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-56" mode="Opaque" x="304" y="1" width="58" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="af1aac9c-84f3-4f2a-92b7-01d7d86f8f47">
						<printWhenExpression><![CDATA[!TipoDocumento.IS_SAIDA.equals($P{tipoMovimentacao})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalTotalCusto}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-55" mode="Opaque" x="228" y="1" width="76" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="8590a6f1-6ef6-4ef4-8b5b-601645462fe3">
						<printWhenExpression><![CDATA[!TipoDocumento.IS_SAIDA.equals($P{tipoMovimentacao})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_total_entradas")+":"]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-55" mode="Opaque" x="429" y="1" width="54" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="e2fe9054-6420-44e2-b1e8-d4ec205cc8f5">
						<printWhenExpression><![CDATA[!TipoDocumento.IS_ENTRADA.equals($P{tipoMovimentacao})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*totalSaidas*/$V{BUNDLE}.getStringApplication("rotulo_total_saidas")+":"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GrupoProduto" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-56" mode="Opaque" x="491" y="1" width="42" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="ff495afc-705a-424a-a14f-c2c739911284">
						<printWhenExpression><![CDATA[!TipoDocumento.IS_ENTRADA.equals($P{tipoMovimentacao})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalTotalVenda}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="228" y="0" width="307" height="1" uuid="c2c52da3-be9e-4533-9a84-d85be9ab02da"/>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="GrupoEmpresa" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{AgruparEmpresa})
?
    $F{codigoEmpresa}
:
    null]]></groupExpression>
		<groupHeader>
			<band height="16" splitType="Stretch">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{AgruparEmpresa})]]></printWhenExpression>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-43" mode="Opaque" x="1" y="0" width="534" height="14" forecolor="#333333" backcolor="#FFFFFF" uuid="90829715-0bca-4e52-8c7c-e6e530a091d0"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*empresa*/$V{BUNDLE}.getStringApplication("rotulo_unidade_movimentacao") + ":" + " " + $F{descricaoEmpresa} + " (" + $F{codigoEmpresa}.trim() + ") "]]></textFieldExpression>
				</textField>
				<rectangle radius="10">
					<reportElement mode="Transparent" x="0" y="0" width="535" height="16" uuid="1a7659a8-bf28-4b7a-b248-3fdfa97a707f"/>
				</rectangle>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<group name="Header" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{AgruparEmpresa})
?
    $F{codigoEmpresa}
:
    null]]></groupExpression>
		<groupHeader>
			<band height="15">
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-47" mode="Opaque" x="172" y="2" width="20" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="3fcd9a0a-6fbf-4cda-a264-d6d3f4f573e8"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*unidade*/$V{BUNDLE}.getStringApplication("rotulo_unidade_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-51" mode="Opaque" x="194" y="2" width="60" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="3136b539-741e-40ac-81ea-5486b4c5fedb">
						<printWhenExpression><![CDATA[!TipoDocumento.IS_SAIDA.equals($P{tipoMovimentacao})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*qtdade Entrada*/$V{BUNDLE}.getStringApplication("rotulo_quantidade_entrada")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-54" mode="Opaque" x="258" y="2" width="59" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="d52b7b6b-c80b-4fcd-a311-a70564e40e14">
						<printWhenExpression><![CDATA[!TipoDocumento.IS_SAIDA.equals($P{tipoMovimentacao})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*preco medio*/ $V{BUNDLE}.getStringApplication("rotulo_preco_medio")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-55" mode="Opaque" x="327" y="2" width="35" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="cd512d70-b534-4249-a71d-2187d1a0343a">
						<printWhenExpression><![CDATA[!TipoDocumento.IS_SAIDA.equals($P{tipoMovimentacao})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*total*/$V{BUNDLE}.getStringApplication("rotulo_total")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-45" mode="Opaque" x="7" y="2" width="163" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="0aff3b79-a471-4dd6-a64e-1764d92a27ae"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*produto*/$V{BUNDLE}.getStringApplication("rotulo_produto")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-57" mode="Opaque" x="369" y="2" width="60" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="656a211b-1b3c-44b3-a59b-e95bb71badb6">
						<printWhenExpression><![CDATA[!TipoDocumento.IS_ENTRADA.equals($P{tipoMovimentacao})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*qtdade Saida*/$V{BUNDLE}.getStringApplication("rotulo_quantidade_saida")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="14" width="535" height="1" uuid="6ac85a68-958b-4e05-a7c3-591867901d15"/>
				</line>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-54" mode="Opaque" x="438" y="2" width="53" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="a68d767f-6cb5-422b-acaf-dc3188f4a42a">
						<printWhenExpression><![CDATA[!TipoDocumento.IS_ENTRADA.equals($P{tipoMovimentacao})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*preco medio venda*/ $V{BUNDLE}.getStringApplication("rotulo_preco_medio")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-55" mode="Opaque" x="506" y="2" width="27" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="2f41a043-d224-460d-a10f-50f55ff12715">
						<printWhenExpression><![CDATA[!TipoDocumento.IS_ENTRADA.equals($P{tipoMovimentacao})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*total*/$V{BUNDLE}.getStringApplication("rotulo_total")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="GrupoProduto" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{descricaoProduto} + $F{unidadeProduto}]]></groupExpression>
		<groupHeader>
			<band height="12" splitType="Stretch">
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-5" mode="Opaque" x="7" y="0" width="163" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="e1ab1909-7106-4322-a68e-eee9077d0a6d"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{descricaoFormatadoProduto}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoProduto" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-6" mode="Opaque" x="172" y="0" width="20" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="4b83cc8a-6ada-47f0-98c7-12ea807acbd9"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{unidadeProduto}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GrupoProduto" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-52" mode="Opaque" x="194" y="0" width="60" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="1082f41f-88e9-48b4-839c-93d592fdce24">
						<printWhenExpression><![CDATA[!TipoDocumento.IS_SAIDA.equals($P{tipoMovimentacao})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{QUANTIDADE_ENTRADA}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GrupoProduto" pattern="###0.0000" isBlankWhenNull="true">
					<reportElement key="textField-53" mode="Opaque" x="258" y="0" width="59" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="9607e136-e960-4ee7-ae9a-9cf302cfcfd4">
						<printWhenExpression><![CDATA[!TipoDocumento.IS_SAIDA.equals($P{tipoMovimentacao})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{PRECO_MEDIO}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GrupoProduto" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-56" mode="Opaque" x="327" y="0" width="35" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="816134f4-d398-45a1-aa74-d7140adfbb7c">
						<printWhenExpression><![CDATA[!TipoDocumento.IS_SAIDA.equals($P{tipoMovimentacao})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TOTAL_CUSTO}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GrupoProduto" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-58" mode="Opaque" x="369" y="0" width="60" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="d19442ff-c7aa-4c0f-9d8a-13e9dc4db9d2">
						<printWhenExpression><![CDATA[!TipoDocumento.IS_ENTRADA.equals($P{tipoMovimentacao})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{QUANTIDADE_SAIDA}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GrupoProduto" pattern="###0.0000" isBlankWhenNull="true">
					<reportElement key="textField-53" mode="Opaque" x="438" y="1" width="53" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="79e66333-4acc-49e3-8e08-d467da9c5c98">
						<printWhenExpression><![CDATA[!TipoDocumento.IS_ENTRADA.equals($P{tipoMovimentacao})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{PRECO_MEDIO_VENDA}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GrupoProduto" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-56" mode="Opaque" x="506" y="1" width="27" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="f56a58c0-f00e-4653-b7bc-6336ac504da4">
						<printWhenExpression><![CDATA[!TipoDocumento.IS_ENTRADA.equals($P{tipoMovimentacao})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TOTAL_VENDA}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band splitType="Stretch"/>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
