/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.agendamento.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.agendamento.dto.RelacaoAgendaDTO;
import br.com.ksisolucoes.report.agendamento.dto.RelacaoAgendaDTOParam;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryRelacaoAgenda extends CommandQuery<QueryRelacaoAgenda> implements ITransferDataReport<RelacaoAgendaDTOParam, RelacaoAgendaDTO> {

    private RelacaoAgendaDTOParam param;
    private List<RelacaoAgendaDTO> dtoList;
    private static final String AGENDA_GRADE_EXAME = "age2";
    private static final String AGENDA_GRADE_ATENDIMENTO = "aega";

    @Override
    public void setDTOParam(RelacaoAgendaDTOParam param) {
        this.param = param;
    }

    private boolean checarFiltroExame() {
        return param.getExameProcedimentoList() != null && !param.getExameProcedimentoList().isEmpty();
    }

    protected void createQuery(final HQLHelper hql) {
        hql.setTypeSelect(RelacaoAgendaDTO.class.getName());
        hql.setConvertToLeftJoin(true);
        String tabela;
        boolean filtroExame = checarFiltroExame();
        tabela = filtroExame ? AGENDA_GRADE_EXAME : AGENDA_GRADE_ATENDIMENTO;

        hql.addToSelect(tabela + ".agendaGrade.agenda.tipoProcedimento.codigo", "tipoProcedimento.codigo");
        hql.addToSelect(tabela + ".agendaGrade.agenda.tipoProcedimento.descricao", "tipoProcedimento.descricao");
        hql.addToSelect(tabela + ".agendaGrade.agenda.tipoProcedimento.tipoProcedimentoClassificacao.codigo", "tipoProcedimento.tipoProcedimentoClassificacao.codigo");
        hql.addToSelect(tabela + ".agendaGrade.agenda.tipoProcedimento.tipoProcedimentoClassificacao.descricao", "tipoProcedimento.tipoProcedimentoClassificacao.descricao");
        hql.addToSelect(tabela + ".agendaGrade.agenda.empresa.codigo", "empresa.codigo");
        hql.addToSelect(tabela + ".agendaGrade.agenda.empresa.referencia", "empresa.referencia");
        hql.addToSelect(tabela + ".agendaGrade.agenda.empresa.descricao", "empresa.descricao");
        hql.addToSelect(tabela + ".agendaGrade.agenda.profissional.codigo", "profissional.codigo");
        hql.addToSelect(tabela + ".agendaGrade.agenda.profissional.referencia", "profissional.referencia");
        hql.addToSelect(tabela + ".agendaGrade.agenda.profissional.nome", "profissional.nome");
        hql.addToSelect(tabela + ".agendaGrade.agenda.empresa.sigla", "empresa.sigla");
        hql.addToSelect(AGENDA_GRADE_ATENDIMENTO + ".quantidadeAtendimento", "quantidadeAtendimento");
        if(filtroExame) {
            hql.addToSelect(tabela + ".exameProcedimento.descricaoProcedimento", "exameProcedimento.descricaoProcedimento");
        }
        final String subSelectQuantidadeUtilizadas = "( select sum (agah.quantidadeVagasOcupadas) from AgendaGradeAtendimentoHorario agah where agah.agendaGradeAtendimento = aega and agah.status <> " + AgendaGradeAtendimentoHorario.STATUS_CANCELADO + " )";
        hql.addToSelect(subSelectQuantidadeUtilizadas, "quantidadeUtilizadas");

        adicionarTipoRelatorio(hql, filtroExame, tabela);

        if(filtroExame) {
            hql.addToFrom("AgendaGradeExame age2");
        } else {
            hql.addToFrom("AgendaGradeAtendimento aega");
        }

        hql.addToWhereWhithAnd(tabela+".agendaGrade.data", Data.adjustRangeHour(this.param.getPeriodo()));
        hql.addToWhereWhithAnd(tabela+".agendaGrade.agenda.empresa in", this.param.getEmpresa());
        hql.addToWhereWhithAnd(tabela+".agendaGrade.agenda.tipoProcedimento ", this.param.getTipoProcedimento());
        hql.addToWhereWhithAnd(tabela+".agendaGrade.agenda.profissional ", this.param.getProfissional());
        hql.addToWhereWhithAnd(tabela+".tipoAtendimentoAgenda ", this.param.getTipoAtendimentoAgenda());
        hql.addToWhereWhithAnd(tabela+".agendaGrade.agenda.status =", Agenda.STATUS_CONFIRMADO);
        hql.addToWhereWhithAnd(tabela+".agendaGrade.agenda.tipoAgenda in ", this.param.getTipoAgenda().getTiposAgenda());

        if(filtroExame) {
            hql.addToWhereWhithAnd(tabela+".exameProcedimento in ", this.param.getExameProcedimentoList());

        }
        if (RelacaoAgendaDTOParam.Ordenacao.UNIDADE.equals(this.param.getOrdenacao())) {
            hql.addToOrder(tabela+".agendaGrade.agenda.empresa.descricao");
        } else if (RelacaoAgendaDTOParam.Ordenacao.TIPO_PROCEDIMENTO.equals(this.param.getOrdenacao())) {
            hql.addToOrder(tabela+".agendaGrade.agenda.tipoProcedimento.descricao");
        } else if(RelacaoAgendaDTOParam.Ordenacao.DATA.equals(this.param.getOrdenacao())) {
            hql.addToOrder(tabela+".agendaGrade.data");
        }

        if(filtroExame) {
            hql.addToOrder(tabela + ".exameProcedimento.descricaoProcedimento");
        }
        adicionarFormaApresentacao(hql, tabela);


    }

    private void adicionarFormaApresentacao(HQLHelper hql, String tabela) {
        if (RelacaoAgendaDTOParam.FormaApresentacao.UNIDADE.equals(this.param.getFormaApresentacao())) {
            hql.addToOrder(tabela+".agendaGrade.agenda.empresa.descricao");
        } else if (RelacaoAgendaDTOParam.FormaApresentacao.TIPO_PROCEDIMENTO.equals(this.param.getFormaApresentacao())) {
            hql.addToOrder(tabela+".agendaGrade.agenda.tipoProcedimento.descricao");
        } else if (RelacaoAgendaDTOParam.FormaApresentacao.PROFISSIONAL.equals(this.param.getFormaApresentacao())) {
            hql.addToWhereWhithAnd(tabela+".agendaGrade.agenda.profissional.codigo is not null");
            hql.addToOrder(tabela+".agendaGrade.agenda.profissional.nome");
        }
        hql.addToOrder(tabela+".agendaGrade.data");
    }

    private void adicionarTipoRelatorio(HQLHelper hql, boolean filtroExame, String tabela) {

        if (RelacaoAgendaDTOParam.TipoRelatorio.DETALHADO.equals(this.param.getTipoRelatorio())) {
            if (filtroExame) {
                hql.addToFrom("AgendaGradeAtendimento aega");
                hql.addToSelect(AGENDA_GRADE_ATENDIMENTO+".agendaGrade.data", "data");
                hql.addToSelect(AGENDA_GRADE_EXAME +".agendaGrade.horaInicial", "horaInicial");
                hql.addToWhereWhithAnd(AGENDA_GRADE_ATENDIMENTO +".agendaGrade "+ "=" + AGENDA_GRADE_EXAME + ".agendaGrade ");
            } else {
                hql.addToSelect(AGENDA_GRADE_ATENDIMENTO +".agendaGrade.data", "data");
                hql.addToSelect(AGENDA_GRADE_ATENDIMENTO +".agendaGrade.horaInicial", "horaInicial");
            }
            hql.addToSelect(AGENDA_GRADE_ATENDIMENTO +".tipoAtendimentoAgenda.codigo", "tipoAtendimentoAgenda.codigo");
            hql.addToSelect(AGENDA_GRADE_ATENDIMENTO +".tipoAtendimentoAgenda.descricao", "tipoAtendimentoAgenda.descricao");
            hql.addToSelect(AGENDA_GRADE_ATENDIMENTO +".tipoAtendimentoAgenda.tipoAtendimento", "tipoAtendimentoAgenda.tipoAtendimento");
        } else {
            if (RelacaoAgendaDTOParam.TipoRelatorio.RESUMIDO.equals(this.param.getTipoRelatorio())) {
                if(filtroExame) {
                    hql.addToFrom("AgendaGradeAtendimento aega");
                    hql.addToWhereWhithAnd(" aega.agendaGrade = age2.agendaGrade ");
                }
                hql.addToOrder(tabela+".agendaGrade.agenda.empresa.descricao");
                hql.addToOrder(tabela+".agendaGrade.agenda.tipoProcedimento.descricao");
                hql.addToOrder(tabela+".agendaGrade.agenda.profissional.nome");
            }
        }

    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        dtoList = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    public Collection getResult() {
        return dtoList;
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (RepositoryComponentDefault.SIM.equals(this.param.getApenasVagasDisponiveis())) {
            List<RelacaoAgendaDTO> dtoCalc = new ArrayList();
            for (RelacaoAgendaDTO relacaoAgendaDTO : this.dtoList) {
                if (relacaoAgendaDTO.getSaldo().compareTo(0L) > 0) {
                    dtoCalc.add(relacaoAgendaDTO);
                }
            }
            this.dtoList = dtoCalc;
        }
    }
}
