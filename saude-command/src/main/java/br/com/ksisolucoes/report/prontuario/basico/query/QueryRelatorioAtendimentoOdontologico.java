/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.prontuario.basico.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioAtendimentoOdontologicoDTO;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.QueryRelatorioAtendimentoOdontologicoDTOParam;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimentoHelper;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimentoHelper.TipoAtendimentoGrupo;
import java.util.Collection;
import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioAtendimentoOdontologico extends CommandQuery<QueryRelatorioAtendimentoOdontologico> implements ITransferDataReport<QueryRelatorioAtendimentoOdontologicoDTOParam, RelatorioAtendimentoOdontologicoDTO>{

    private List<RelatorioAtendimentoOdontologicoDTO> dtoList;
    private QueryRelatorioAtendimentoOdontologicoDTOParam param;

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {

/**
 * Recuperar os procedimentos agrupados da tabela atendimento_odontologico
 */
        HQLHelper hqlAtendimento = new HQLHelper();

        hqlAtendimento.addToSelectAndGroup("emp.referencia","codigoEmpresa");
        hqlAtendimento.addToSelectAndGroupAndOrder("emp.descricao","descricaoEmpresa");

        if(RepositoryComponentDefault.SIM.equals(param.getExibirUsuario())){
            hqlAtendimento.addToSelectAndGroup("usu.codigo","codigoUsuarioCadsus");
            hqlAtendimento.addToSelectAndGroupAndOrder("usu.nome","descricaoUsuarioCadsus");
        }

        hqlAtendimento.addToSelectAndGroup("ta.codigo","codigoTipoAtendimento");
        hqlAtendimento.addToSelectAndGroupAndOrder("ta.descricao","descricaoTipoAtendimento");

        hqlAtendimento.addToSelectAndGroup("proc.codigo","codigoProcedimento");
        hqlAtendimento.addToSelectAndGroupAndOrder("proc.descricao","descricaoProcedimento");

        hqlAtendimento.addToSelect("count(*)","quantidade");
        hqlAtendimento.addToSelect("sum(pc.valorServicoHospitalar)","totalServicoHospitalar");
        hqlAtendimento.addToSelect("sum(pc.valorServicoAmbulatorial)","totalServicoAmbulatorial");
        hqlAtendimento.addToSelect("sum(pc.valorServicoProfissional)","totalServicoProfissional");

        hqlAtendimento.setTypeSelect(RelatorioAtendimentoOdontologicoDTO.class.getName());
        hqlAtendimento.addToFrom("AtendimentoOdontologico ao" +
                " left join ao.atendimento at " +
                " left join at.naturezaProcuraTipoAtendimento npta" +
                " left join npta.tipoAtendimento ta " +
                " left join at.procedimentoCompetencia pc " +
                " left join pc.id.procedimento proc " +
                " left join at.usuarioCadsus usu" +
                " left join at.empresa emp " +
                " left join at.profissional prof ");

        hqlAtendimento.addToWhereWhithAnd("emp ",param.getEmpresa());
        hqlAtendimento.addToWhereWhithAnd("prof ",param.getProfissional());
        hqlAtendimento.addToWhereWhithAnd("at.dataAtendimento ",param.getPeriod());

        if (this.param.getTipoProcedimento()!=null) {
            hqlAtendimento.addToWhereWhithAnd("coalesce(proc.flagFaturavel, 'S') = ", this.param.getTipoProcedimento());
        }

        Query queryAtendimento = getSession().createQuery(hqlAtendimento.getQuery());

        hqlAtendimento.applyRestrictions(queryAtendimento);

        dtoList = hqlAtendimento.getBeanList(queryAtendimento.list());

/**
 * Recuperar os procedimentos agrupados da tabela atendimento_item
 */
        HQLHelper hql = new HQLHelper();

        hql.addToSelectAndGroup("emp.referencia","codigoEmpresa");
        hql.addToSelectAndGroupAndOrder("emp.descricao","descricaoEmpresa");

        if(RepositoryComponentDefault.SIM.equals(param.getExibirUsuario())){
            hql.addToSelectAndGroup("usu.codigo","codigoUsuarioCadsus");
            hql.addToSelectAndGroupAndOrder("usu.nome","descricaoUsuarioCadsus");
        }

        hql.addToSelectAndGroup("proc.codigo","codigoProcedimento");
        hql.addToSelectAndGroupAndOrder("proc.descricao","descricaoProcedimento");

        hql.addToSelect("count(*)","quantidade");
        hql.addToSelect("sum(pc.valorServicoHospitalar)","totalServicoHospitalar");
        hql.addToSelect("sum(pc.valorServicoAmbulatorial)","totalServicoAmbulatorial");
        hql.addToSelect("sum(pc.valorServicoProfissional)","totalServicoProfissional");

        hql.setTypeSelect(RelatorioAtendimentoOdontologicoDTO.class.getName());
        hql.addToFrom("AtendimentoItem ai " +
                " left join ai.atendimento at" +
                " left join at.naturezaProcuraTipoAtendimento npta" +
                " left join npta.tipoAtendimento ta " +
                " left join ai.procedimentoCompetencia pc " +
                " left join pc.id.procedimento proc " +
                " left join at.usuarioCadsus usu" +
                " left join at.empresa emp " +
                " left join at.profissional prof");

        hql.addToWhereWhithAnd("ta.tipoAtendimento in ",TipoAtendimentoHelper.getTipoAtendimentoGrupoValues(TipoAtendimentoGrupo.GRUPO_ODONTOLOGIA));
        hql.addToWhereWhithAnd("emp ",param.getEmpresa());
        hql.addToWhereWhithAnd("prof ",param.getProfissional());
        hql.addToWhereWhithAnd("at.dataAtendimento ",param.getPeriod());

        if (this.param.getTipoProcedimento()!=null) {
            hql.addToWhereWhithAnd("coalesce(proc.flagFaturavel, 'S') = ", this.param.getTipoProcedimento());
        }

        Query query = getSession().createQuery(hql.getQuery());

        hql.applyRestrictions(query);

        dtoList.addAll(hql.getBeanList(query.list()));

    }

    @Override
    public Collection getResult() {
        return dtoList;
    }

    public void setDTOParam(QueryRelatorioAtendimentoOdontologicoDTOParam param) {
        this.param = param;
    }
  
}
