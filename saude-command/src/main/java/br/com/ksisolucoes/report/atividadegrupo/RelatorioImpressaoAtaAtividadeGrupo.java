package br.com.ksisolucoes.report.atividadegrupo;

import br.com.ksisolucoes.bo.atividadegrupo.interfaces.dto.AtividadeGrupoDTO;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.atividadegrupo.interfaces.dto.AtividadeDTOParam;
import br.com.ksisolucoes.report.atividadegrupo.query.QueryRelatorioImpressaoAtaAtividadeGrupo;
import br.com.ksisolucoes.report.atividadegrupo.query.QueryRelatorioImpressaoBoletim;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoPaciente;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoProcedimento;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoProfissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;

import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoAtaAtividadeGrupo extends AbstractReport<AtividadeDTOParam> {


    public RelatorioImpressaoAtaAtividadeGrupo(AtividadeDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/atividadegrupo/jrxml/relatorio_impressao_ata_atividade_grupo.jrxml";
    }

    @Override
    public String getTitulo() {
        return "Ata da Atividade em Grupo";
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioImpressaoAtaAtividadeGrupo();
    }
}
