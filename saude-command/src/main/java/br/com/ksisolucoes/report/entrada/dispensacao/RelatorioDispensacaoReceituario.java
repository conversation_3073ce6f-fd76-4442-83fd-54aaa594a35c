package br.com.ksisolucoes.report.entrada.dispensacao;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioDispensacaoReceituarioParam;
import br.com.ksisolucoes.report.entrada.dispensacao.query.QueryRelatorioDispensacaoReceita;
import br.com.ksisolucoes.util.Bundle;

public class RelatorioDispensacaoReceituario extends AbstractReport<RelatorioDispensacaoReceituarioParam> {

    public RelatorioDispensacaoReceituario(RelatorioDispensacaoReceituarioParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioDispensacaoReceita();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relatorio_dispensacao_farmacia_externa");
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_dispensacao_receituario.jrxml";
    }

}
