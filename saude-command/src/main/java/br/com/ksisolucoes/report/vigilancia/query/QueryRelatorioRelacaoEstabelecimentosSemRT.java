package br.com.ksisolucoes.report.vigilancia.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioRelacaoEstabelecimentosSemRTDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioRelacaoEstabelecimentosSemRTDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import java.util.Collection;
import java.util.List;
import org.hibernate.Query;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioRelacaoEstabelecimentosSemRT extends CommandQuery<QueryRelatorioRelacaoEstabelecimentosSemRT> implements ITransferDataReport<RelatorioRelacaoEstabelecimentosSemRTDTOParam, RelatorioRelacaoEstabelecimentosSemRTDTO> {

    private RelatorioRelacaoEstabelecimentosSemRTDTOParam param;
    private List<RelatorioRelacaoEstabelecimentosSemRTDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(RelatorioRelacaoEstabelecimentosSemRTDTO.class.getName());

        hql.addToSelect("e.codigo", "estabelecimento.codigo");
        hql.addToSelect("e.razaoSocial", "estabelecimento.razaoSocial");
        
        hql.addToSelect("ve.codigo", "estabelecimento.vigilanciaEndereco.codigo");
        hql.addToSelect("ve.cep", "estabelecimento.vigilanciaEndereco.cep");
        hql.addToSelect("ve.bairro", "estabelecimento.vigilanciaEndereco.bairro");
        hql.addToSelect("ve.logradouro", "estabelecimento.vigilanciaEndereco.logradouro");
        
        hql.addToSelect("c.codigo", "estabelecimento.vigilanciaEndereco.cidade.codigo");
        hql.addToSelect("c.descricao", "estabelecimento.vigilanciaEndereco.cidade.descricao");
        
        hql.addToSelect("estado.descricao", "estabelecimento.vigilanciaEndereco.cidade.estado.descricao");
        hql.addToSelect("estado.sigla", "estabelecimento.vigilanciaEndereco.cidade.estado.sigla");
        
        hql.addToSelect("(select ae.descricao from EstabelecimentoAtividade ea "
                + "left join ea.atividadeEstabelecimento ae "
                + "left join ea.estabelecimento estabelecimento "
                + "where estabelecimento.codigo = e.codigo and ea.flagPrincipal = " + RepositoryComponentDefault.SIM_LONG + ")", "descricaoAtividadePrincipal");

        hql.addToFrom("Estabelecimento e "
                + " left join e.vigilanciaEndereco ve"
                + " left join ve.cidade c"
                + " left join c.estado estado");
        
        if(param.getEstabelecimento() != null){
            hql.addToWhereWhithAnd("(e = :estabelecimento or e.estabelecimentoPrincipal = :estabelecimento)");
        }
        
        if(param.getSetorVigilancia() != null){
            hql.addToWhereWhithAnd("exists(select 1 "
                    + "from EstabelecimentoAtividade ea "
                    + "left join ea.atividadeEstabelecimento ae "
                    + "left join ae.setorVigilancia sv "
                    + "where ea.estabelecimento = e AND ea.flagPrincipal = :sim AND sv.codigo = :codigoSetorVigilancia)"); 
        }

        if (this.param.getAtividadeEstabelecimento()!= null) {
            hql.addToWhereWhithAnd("(select ae.codigo "
                    + "from EstabelecimentoAtividade ea "
                    + "left join ea.atividadeEstabelecimento ae "
                    + "where ea.estabelecimento = e AND ea.flagPrincipal = :sim AND ae.exigeResponsavelTecnico = :sim) = :codigoAtividadeEstabelecimento");
        } else {
            hql.addToWhereWhithAnd("exists(select 1 "
                    + "from EstabelecimentoAtividade ea "
                    + "left join ea.atividadeEstabelecimento ae "
                    + "where ea.estabelecimento = e AND ea.flagPrincipal = :sim AND ae.exigeResponsavelTecnico = :sim)");
        }
        
        if(RepositoryComponentDefault.SIM.equals(param.getVenceuPrazo())){
            hql.addToWhereWhithAnd("(extract(day from(now() - coalesce(e.dataSemResponsavelTecnico, now())))) > :diasSemResponsavelTecnico");
        }
        
        hql.addToWhereWhithAnd("e.dataSemResponsavelTecnico is not null");
        hql.addToWhereWhithAnd("e.situacao = ", Estabelecimento.Situacao.ATIVO.value());
        
        hql.addToOrder("e.razaoSocial asc");
    }
    
    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        if(RepositoryComponentDefault.SIM.equals(param.getVenceuPrazo())){
            ConfiguracaoVigilancia cv = VigilanciaHelper.getConfiguracaoVigilancia();

            if(cv != null && cv.getDiasSemResponsavelTecnico() != null){
                query.setLong("diasSemResponsavelTecnico", cv.getDiasSemResponsavelTecnico());
            } else {
                query.setLong("diasSemResponsavelTecnico", 0L);
            }
        }
        
        if (this.param.getEstabelecimento() != null) {
            query.setParameter("estabelecimento", this.param.getEstabelecimento());
        }
        
        if(param.getSetorVigilancia() != null){
            query.setParameter("codigoSetorVigilancia", this.param.getSetorVigilancia().getCodigo());
        }
        
        if (this.param.getAtividadeEstabelecimento()!= null) {
            query.setParameter("codigoAtividadeEstabelecimento", this.param.getAtividadeEstabelecimento().getCodigo());
        }

        query.setLong("sim", RepositoryComponentDefault.SIM_LONG);
    }

    @Override
    public Collection getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List) result);
    }

    @Override
    public void setDTOParam(RelatorioRelacaoEstabelecimentosSemRTDTOParam param) {
        this.param = param;
    }
}
