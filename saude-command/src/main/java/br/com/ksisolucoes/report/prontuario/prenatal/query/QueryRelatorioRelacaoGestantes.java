package br.com.ksisolucoes.report.prontuario.prenatal.query;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioRelacaoGestantesDTO;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioRelacaoGestantesDTOParam;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.PreNatal;
import br.com.ksisolucoes.vo.vacina.VacinaAplicacao;
import org.hibernate.Query;

import java.util.*;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioRelacaoGestantes extends CommandQuery<QueryRelatorioRelacaoGestantes> implements ITransferDataReport<RelatorioRelacaoGestantesDTOParam, RelatorioRelacaoGestantesDTO> {

    private RelatorioRelacaoGestantesDTOParam param;
    private Collection<RelatorioRelacaoGestantesDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(RelatorioRelacaoGestantesDTO.class.getName());

        hql.addToSelect("preNatal.codigo", "preNatal.codigo");
        hql.addToSelect("preNatal.dataUltimaMenstruacao", "preNatal.dataUltimaMenstruacao");
        hql.addToSelect("preNatal.dataProvavelParto", "preNatal.dataProvavelParto");
        hql.addToSelect("preNatal.dataUltimaConsulta", "preNatal.dataUltimaConsulta");
        hql.addToSelect("preNatal.numeroSisprenatal", "preNatal.numeroSisprenatal");
        hql.addToSelect("preNatal.numeroConsulta", "preNatal.numeroConsulta");
        hql.addToSelect("preNatal.gravidezRisco", "preNatal.gravidezRisco");

        hql.addToSelect("usuarioCadsus.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.dataNascimento", "usuarioCadsus.dataNascimento");

        hql.addToSelect("(select ucd.idadeGestacional from UsuarioCadsusDado ucd where ucd.codigo = usuarioCadsus.codigo )", "idadeGestacional");

        hql.addToSelect("(SELECT t2.situacao FROM UsuarioCadsusDado t1 JOIN t1.indiceImc t2 WHERE t1.codigo = usuarioCadsus.codigo) ", "imc");

        hql.addToSelect("(SELECT max(t1.dataCadastro) FROM Puerperio t1 WHERE t1.preNatal.codigo = preNatal.codigo)", "dataPuerperio");

        hql.addToSelect("(select max(va.codigo) from VacinaAprazamento va "
                + " where va.usuarioCadsus.codigo = usuarioCadsus.codigo"
                + " and va.dataAprazamento < :dataAtual"
                + " and NOT EXISTS (SELECT 1 FROM VacinaAplicacao vap WHERE vap.vacinaCalendario = va.vacinaCalendario "
                + "AND vap.status = :status AND va.usuarioCadsus = usuarioCadsus and vap.vacinaCalendario is not null))", "codigoVacina");

        hql.addToSelect("enderecoUsuarioCadsus.logradouro", "enderecoUsuarioCadsus.logradouro");
        hql.addToSelect("enderecoUsuarioCadsus.numeroLogradouro", "enderecoUsuarioCadsus.numeroLogradouro");
        hql.addToSelect("enderecoUsuarioCadsus.bairro", "enderecoUsuarioCadsus.bairro");
        hql.addToSelect("tipoLogradouro.codigo", "enderecoUsuarioCadsus.tipoLogradouro.codigo");
        hql.addToSelect("tipoLogradouro.descricao", "enderecoUsuarioCadsus.tipoLogradouro.descricao");

        hql.addToSelect("equipeArea.codigo", "equipeMicroArea.equipeArea.codigo");
        hql.addToSelect("equipeArea.descricao", "equipeMicroArea.equipeArea.descricao");
        hql.addToSelect("equipeMicroArea.microArea", "equipeMicroArea.microArea");


        hql.addToSelect("equipeMicroArea.codigo", "equipeMicroArea.codigo");

        hql.addToSelect("equipeProfissional.codigo", "equipeMicroArea.equipeProfissional.codigo");

        hql.addToSelect("equipe.codigo", "equipeMicroArea.equipeProfissional.equipe.codigo");

        hql.addToSelect("empresaEquipe.codigo", "equipeMicroArea.equipeProfissional.equipe.empresa.codigo");
        hql.addToSelect("empresaEquipe.descricao", "equipeMicroArea.equipeProfissional.equipe.empresa.descricao");

        hql.addToFrom("PreNatal preNatal "
                + " left join preNatal.usuarioCadsus usuarioCadsus"
                + " left join usuarioCadsus.enderecoDomicilio enderecoDomicilio"
                + " left join enderecoDomicilio.equipeMicroArea equipeMicroArea"
                + " left join equipeMicroArea.equipeArea equipeArea"
                + " left join equipeMicroArea.equipeProfissional equipeProfissional"
                + " left join equipeProfissional.equipe equipe"
                + " left join equipe.empresa empresaEquipe"
                + " left join usuarioCadsus.enderecoUsuarioCadsus enderecoUsuarioCadsus"
                + " left join enderecoUsuarioCadsus.tipoLogradouro tipoLogradouro"
                + " left join enderecoUsuarioCadsus.empresa empresa");
        //Controle de empresa com acesso restrito
        if (this.param.getEmpresa().getValue().size() > 1) {
            hql.addToWhereWhithAnd("coalesce(coalesce(empresaEquipe, empresa), " + SessaoAplicacaoImp.getInstance().getEmpresa().getCodigo() + ") ", this.param.getEmpresa());
        } else {
            hql.addToWhereWhithAnd("coalesce(empresaEquipe, empresa) ", this.param.getEmpresa());
        }
        hql.addToWhereWhithAnd("usuarioCadsus = ", this.param.getPaciente());
        hql.addToWhereWhithAnd("equipeArea = ", this.param.getEquipeArea());
        hql.addToWhereWhithAnd("equipeMicroArea.microArea = ", this.param.getMicroArea());

        if (this.param.getMesesDum() != null) {
            Date date = Data.removeMeses(DataUtil.getDataAtual(), param.getMesesDum().intValue());
            hql.addToWhereWhithAnd("preNatal.dataUltimaMenstruacao <=", date);
        }

        if (this.param.getPeriodo() != null) {
            if (RelatorioRelacaoGestantesDTOParam.TipoPeriodo.DPP.equals(this.param.getTipoPeriodo())) {
                hql.addToWhereWhithAnd("preNatal.dataProvavelParto ", Data.adjustRangeHour(this.param.getPeriodo()));
            } else {
                hql.addToWhereWhithAnd("preNatal.dataUltimaConsulta ", Data.adjustRangeHour(this.param.getPeriodo()));
            }
        }

        if (RelatorioRelacaoGestantesDTOParam.Situacao.ABERTO.equals(this.param.getSituacao())) {
            hql.addToWhereWhithAnd("preNatal.status = ", PreNatal.Status.ABERTO.value());
        } else if (RelatorioRelacaoGestantesDTOParam.Situacao.CONCLUIDO.equals(this.param.getSituacao())) {
            hql.addToWhereWhithAnd("preNatal.status = ", PreNatal.Status.FINALIZADO.value());
            if (this.param.getDesfecho() != null) {
                hql.addToWhereWhithAnd("preNatal.desfecho = ", param.getDesfecho());
            }
        } else {
            hql.addToWhereWhithAnd("preNatal.status in ", Arrays.asList(PreNatal.Status.ABERTO.value(), PreNatal.Status.FINALIZADO.value()));
        }

        if (param.getFaixaEtaria() != null || RelatorioRelacaoGestantesDTOParam.FormaApresentacao.FAIXA_ETARIA.equals(this.param.getFormaApresentacao())) {
            hql.addToFrom("FaixaEtariaItem faixaEtariaItem"
                    + " join faixaEtariaItem.id.faixaEtaria faixaEtaria");

            hql.addToWhereWhithAnd("cast(extract(years from age(current_date, usuarioCadsus.dataNascimento)) * 12 + extract(months from age(current_date, usuarioCadsus.dataNascimento)) as long) between faixaEtariaItem.idadeInicial and faixaEtariaItem.idadeFinal ");
            hql.addToWhereWhithAnd("faixaEtaria = ", param.getFaixaEtaria());

            if (this.param.getFaixaEtariaItem() != null) {
                hql.addToWhereWhithAnd("faixaEtariaItem.id.sequencia = ", this.param.getFaixaEtariaItem().getId().getSequencia());
            }
        }

        if (RelatorioRelacaoGestantesDTOParam.FormaApresentacao.UNIDADE.equals(this.param.getFormaApresentacao())) {
            hql.addToSelectAndOrder("coalesce(empresaEquipe.codigo, empresa.codigo)", "empresa.codigo");
            hql.addToSelectAndOrder("coalesce(empresaEquipe.descricao, empresa.descricao)", "empresa.descricao");
        } else if (RelatorioRelacaoGestantesDTOParam.FormaApresentacao.FAIXA_ETARIA.equals(this.param.getFormaApresentacao())) {
            hql.addToSelect("faixaEtariaItem.idadeInicial", "faixaEtariaItem.idadeInicial");
            hql.addToSelect("faixaEtaria.codigo", "faixaEtariaItem.id.faixaEtaria.codigo");
            hql.addToSelect("faixaEtariaItem.id.sequencia", "faixaEtariaItem.id.sequencia");
            hql.addToSelect("faixaEtariaItem.descricao", "faixaEtariaItem.descricao");
            hql.addToOrder("faixaEtariaItem.idadeInicial");
        } else if (RelatorioRelacaoGestantesDTOParam.FormaApresentacao.AREA.equals(this.param.getFormaApresentacao())) {
            hql.addToSelect("equipeArea.codigo", "equipeMicroArea.equipeArea.codigo");
            hql.addToSelect("equipeArea.descricao", "equipeMicroArea.equipeArea.descricao");
            hql.addToOrder("equipeArea.descricao");
        } else if (RelatorioRelacaoGestantesDTOParam.FormaApresentacao.MICRO_AREA.equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("equipeArea.descricao");
            hql.addToOrder("equipeMicroArea.microArea");
        }

        hql.addToOrder("usuarioCadsus.nome");
    }

    @Override
    public void setDTOParam(RelatorioRelacaoGestantesDTOParam param) {
        this.param = param;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public Collection<RelatorioRelacaoGestantesDTO> getResult() {
        return this.result;
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setParameter("status", VacinaAplicacao.StatusVacinaAplicacao.APLICADA.value());
        query.setParameter("dataAtual", Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial());
    }
}
