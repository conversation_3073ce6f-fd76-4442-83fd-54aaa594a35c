/*
 * QueryMovimentacaoEmDeterminadaData.java
 *
 * Created on 18 de Outubro de 2005, 15:05
 *
 * To change this template, choose Tools | Options and locate the template under
 * the Source Creation and Management node. Right-click the template and choose
 * Open. You can then make changes to the template in the Source Editor.
 */

package br.com.ksisolucoes.report.entrada.estoque.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioDivergenciaInventarioDTO;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioRelacaoDivergenciaInventarioDTOParam;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryDivergenciaInventario extends CommandQuery<QueryDivergenciaInventario> implements ITransferDataReport<RelatorioRelacaoDivergenciaInventarioDTOParam, RelatorioDivergenciaInventarioDTO> {

    private RelatorioRelacaoDivergenciaInventarioDTOParam param;
    private List< RelatorioDivergenciaInventarioDTO > result;

    public QueryDivergenciaInventario(RelatorioRelacaoDivergenciaInventarioDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("ci.id.empresa.referencia", "codigoEmpresa");
        hql.addToSelect("ci.id.empresa.descricao", "nomeEmpresa");
        hql.addToSelect("ci.quantidade", "quantidade");
        hql.addToSelect("ci.contagem1", "contagem1");
        hql.addToSelect("ci.contagem2", "contagem2");
        hql.addToSelect("ci.contagem3", "contagem3");

        hql.addToSelect("coalesce(ci.estoqueFisico,0)", "estoque");
        hql.addToSelect("pro.referencia", "codigoProduto");
        hql.addToSelect("pro.descricao", "descricaoProduto");
        hql.addToSelect("ci.grupoEstoque", "grupoEstoque");
        hql.addToSelect("ci.localizacaoEstrutura.codigo", "localizacaoEstrutura.codigo");
        hql.addToSelect("ci.localizacaoEstrutura.mascara", "localizacaoEstrutura.mascara");
        hql.addToSelect("usu.codigo", "codigoUsuario");
        hql.addToSelect("usu.nome", "nomeUsuario");
        hql.addToSelect("sub.id.codigo", "codigoSubGrupo");
        hql.addToSelect("sub.descricao", "descricaoSubGrupo");
        hql.addToSelect("gp.codigo", "codigoGrupoProduto");
        hql.addToSelect("gp.descricao", "descricaoGrupoProduto");
        hql.addToSelect("inventario.codigo", "codigoInventario");
        hql.addToSelect("inventario.descricaoInventario", "descricaoInventario");

        HQLHelper hqlSub = hql.getNewInstanceSubQuery();
        hqlSub.addToSelect("Coalesce(estoqueEmpresa.ultimoPreco, 0)");
        hqlSub.addToFrom("EstoqueEmpresa estoqueEmpresa");
        hqlSub.addToWhereWhithAnd("estoqueEmpresa.id.empresa.codigo = ci.id.empresa.codigo");
        hqlSub.addToWhereWhithAnd("estoqueEmpresa.id.produto.codigo = pro.codigo");

        hql.addToSelect("( " + hqlSub.getQuery() + " )", "valorUnitario");

        hql.addToFrom("ControleInventario ci"
                + " left join ci.produto pro"
                + " left join pro.subGrupo sub"
                + " left join sub.roGrupoProduto gp"
                + " left join ci.inventario inventario"
                + " left join ci.usuario usu");

        hql.setTypeSelect(RelatorioDivergenciaInventarioDTO.class.getName());

        hql.addToWhereWhithAnd("ci.id.empresa ", this.param.getEmpresas());

        hql.addToWhereWhithAnd("usu", this.param.getUsuario());

        hql.addToWhereWhithAnd("gp ", this.param.getGruposProduto());

        if (this.param.getSubGrupo() != null) {
            hql.addToWhereWhithAnd(" sub.id.codigo = ", param.getSubGrupo().getId().getCodigo());
            hql.addToWhereWhithAnd(" sub.id.codigoGrupoProduto = ", param.getSubGrupo().getId().getCodigoGrupoProduto());
        }

        hql.addToWhereWhithAnd("ci.dataLancamento ", param.getPeriodo());

        if (this.param.getInventario() != null) {
            hql.addToWhereWhithAnd("ci.inventario = ", this.param.getInventario());
        }

        hql.addToOrder("ci.id.empresa.descricao");
        if (!ReportProperties.FORMA_APRESENTACAO_GERAL.equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("inventario.descricaoInventario");
        }

        hql.addToOrder("pro.descricao");
        hql.addToOrder("ci.localizacaoEstrutura.mascara");

        /*....................................*/
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public void setDTOParam(RelatorioRelacaoDivergenciaInventarioDTOParam param) {
        this.param = param;
    }


    @Override
    public List< RelatorioDivergenciaInventarioDTO > getResult() {
        return result;
    }
    
}