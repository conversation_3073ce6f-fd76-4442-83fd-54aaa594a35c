package br.com.ksisolucoes.report.encaminhamento.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.encaminhamento.dto.RelatorioImpressaoPedidoTfdDTO;
import br.com.ksisolucoes.report.encaminhamento.dto.RelatorioImpressaoPedidoTfdDTOParam;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEndereco;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioImpressaoPedidoTfd extends CommandQuery<QueryRelatorioImpressaoPedidoTfd> implements ITransferDataReport<RelatorioImpressaoPedidoTfdDTOParam, RelatorioImpressaoPedidoTfdDTO> {

    private RelatorioImpressaoPedidoTfdDTOParam param;
    private List<RelatorioImpressaoPedidoTfdDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("laudoTfd.codigo", "laudoTfd.codigo");
        hql.addToSelect("pedidoTfd.codigo", "laudoTfd.pedidoTfd.codigo");
        hql.addToSelect("pedidoTfd.tipoTfd", "laudoTfd.pedidoTfd.tipoTfd");
        hql.addToSelect("pedidoTfd.numeroPedido", "laudoTfd.pedidoTfd.numeroPedido");
        hql.addToSelect("pedidoTfd.peso", "laudoTfd.pedidoTfd.peso");
        hql.addToSelect("pedidoTfd.altura", "laudoTfd.pedidoTfd.altura");
        hql.addToSelect("pedidoTfd.dataPedido", "laudoTfd.pedidoTfd.dataPedido");
        hql.addToSelect("regionalSaude.codigo", "laudoTfd.pedidoTfd.regionalSaude.codigo");
        hql.addToSelect("regionalSaude.descricao", "laudoTfd.pedidoTfd.regionalSaude.descricao");
        hql.addToSelect("laudoTfd.dataCadastro", "laudoTfd.dataCadastro");
        hql.addToSelect("laudoTfd.numeroCartao", "laudoTfd.numeroCartao");

        hql.addToSelect("usuarioCadsus.codigo", "laudoTfd.usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "laudoTfd.usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.sexo", "laudoTfd.usuarioCadsus.sexo");
        hql.addToSelect("usuarioCadsus.dataNascimento", "laudoTfd.usuarioCadsus.dataNascimento");
        hql.addToSelect("usuarioCadsus.cpf", "laudoTfd.usuarioCadsus.cpf");
        hql.addToSelect("usuarioCadsus.rg", "laudoTfd.usuarioCadsus.rg");
        hql.addToSelect("usuarioCadsus.nomeMae", "laudoTfd.usuarioCadsus.nomeMae");
        hql.addToSelect("usuarioCadsus.telefone", "laudoTfd.usuarioCadsus.telefone");
        hql.addToSelect("usuarioCadsus.telefone2", "laudoTfd.usuarioCadsus.telefone2");
        hql.addToSelect("usuarioCadsus.celular", "laudoTfd.usuarioCadsus.celular");
        hql.addToSelect("usuarioCadsus.email", "laudoTfd.usuarioCadsus.email");

        hql.addToSelect("enderecoUsuarioCadsus.codigo", "laudoTfd.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.codigo");
        hql.addToSelect("enderecoUsuarioCadsus.logradouro", "laudoTfd.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.logradouro");
        hql.addToSelect("enderecoUsuarioCadsus.bairro", "laudoTfd.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.bairro");
        hql.addToSelect("enderecoUsuarioCadsus.cep", "laudoTfd.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.cep");
        hql.addToSelect("enderecoUsuarioCadsus.numeroLogradouro", "laudoTfd.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.numeroLogradouro");
        hql.addToSelect("enderecoUsuarioCadsus.complementoLogradouro", "laudoTfd.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.complementoLogradouro");

        hql.addToSelect("tipoLogradouro.codigo", "laudoTfd.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.tipoLogradouro.codigo");
        hql.addToSelect("tipoLogradouro.descricao", "laudoTfd.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.tipoLogradouro.descricao");

        hql.addToSelect("cidade.codigo", "laudoTfd.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.cidade.codigo");
        hql.addToSelect("cidade.descricao", "laudoTfd.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.cidade.descricao");

        hql.addToSelect("cidadeNascimento.codigo", "laudoTfd.usuarioCadsus.cidadeNascimento.codigo");
        hql.addToSelect("cidadeNascimento.descricao", "laudoTfd.usuarioCadsus.cidadeNascimento.descricao");

        hql.addToSelect("estado.sigla", "laudoTfd.usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.cidade.estado.sigla");
        hql.addToSelect("estadoNascimento.sigla", "laudoTfd.usuarioCadsus.cidadeNascimento.estado.sigla");

        hql.addToSelect("(select min(ucc.numeroCartao) from UsuarioCadsusCns ucc where ucc.usuarioCadsus = usuarioCadsus and ucc.excluido = 0 )","usuarioCadsusCns.numeroCartao");

//        hql.addToSelect("(select ucd.peso from UsuarioCadsusDado ucd where ucd.codigo = usuarioCadsus.codigo )","usuarioCadsusDado.peso");
//        hql.addToSelect("(select ucd.altura from UsuarioCadsusDado ucd where ucd.codigo = usuarioCadsus.codigo )","usuarioCadsusDado.altura");

        hql.setTypeSelect(RelatorioImpressaoPedidoTfdDTO.class.getName());
        hql.addToFrom("LaudoTfd laudoTfd"
                + " left join laudoTfd.pedidoTfd pedidoTfd"
                + " left join pedidoTfd.regionalSaude regionalSaude"
                + " left join laudoTfd.usuarioCadsus usuarioCadsus"
                + " left join usuarioCadsus.cidadeNascimento cidadeNascimento"
                + " left join cidadeNascimento.estado estadoNascimento,"
                + " UsuarioCadsusEndereco usuarioCadsusEndereco"
                + " left join usuarioCadsusEndereco.id.endereco enderecoUsuarioCadsus"
                + " left join enderecoUsuarioCadsus.tipoLogradouro tipoLogradouro"
                + " left join enderecoUsuarioCadsus.cidade cidade"
                + " left join cidade.estado estado");

        hql.addToWhereWhithAnd("usuarioCadsusEndereco.id.usuarioCadsus = usuarioCadsus");
        hql.addToWhereWhithAnd("coalesce(usuarioCadsusEndereco.status, " + UsuarioCadsusEndereco.STATUS_ABERTO + ") = ", UsuarioCadsusEndereco.STATUS_ABERTO);
        hql.addToWhereWhithAnd("laudoTfd in ", param.getLaudosTfd());

    }

    @Override
    public void setDTOParam(RelatorioImpressaoPedidoTfdDTOParam param) {
        this.param = param;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioImpressaoPedidoTfdDTO> getResult() {
        return result;
    }

}
