<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_impressao_prescricao_enfermagem" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="177911e4-d904-4868-ab85-22e162f3998f">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="caminhoImagem" class="java.lang.String" isForPrompting="false"/>
	<parameter name="exibirCabecalho" class="java.lang.Boolean"/>
	<field name="atendimento" class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"/>
	<field name="setor" class="br.com.ksisolucoes.vo.basico.Empresa">
		<fieldDescription><![CDATA[atendimento.empresa]]></fieldDescription>
	</field>
	<field name="leito" class="br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto">
		<fieldDescription><![CDATA[atendimento.leitoQuarto]]></fieldDescription>
	</field>
	<field name="quarto" class="br.com.ksisolucoes.vo.prontuario.hospital.QuartoInternacao">
		<fieldDescription><![CDATA[atendimento.leitoQuarto.quartoInternacao]]></fieldDescription>
	</field>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional">
		<fieldDescription><![CDATA[atendimento.profissional]]></fieldDescription>
	</field>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus">
		<fieldDescription><![CDATA[atendimento.usuarioCadsus]]></fieldDescription>
	</field>
	<field name="convenio" class="br.com.ksisolucoes.vo.prontuario.basico.Convenio">
		<fieldDescription><![CDATA[atendimento.convenio]]></fieldDescription>
	</field>
	<field name="dataChegada" class="java.util.Date">
		<fieldDescription><![CDATA[atendimento.atendimentoPrincipal.dataChegada]]></fieldDescription>
	</field>
	<field name="prescricaoEnfermagemAtendimento" class="br.com.ksisolucoes.vo.prontuario.prescricaoenfermagem.PrescricaoEnfermagemAtendimento"/>
	<field name="prescricaoEnfermagem" class="br.com.ksisolucoes.vo.prontuario.prescricaoenfermagem.PrescricaoEnfermagem"/>
	<field name="prescricaoEnfermagemGrupo" class="br.com.ksisolucoes.vo.prontuario.prescricaoenfermagem.PrescricaoEnfermagemGrupo"/>
	<field name="prescricaoEnfermagemAtendimentoItem" class="br.com.ksisolucoes.vo.prontuario.prescricaoenfermagem.PrescricaoEnfermagemAtendimentoItem"/>
	<group name="evolucao">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band height="35">
				<rectangle>
					<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="535" height="35" uuid="7c577d9b-d7af-4fe7-84ad-6bf8092a5e8b"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isStretchWithOverflow="true" isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="8" y="18" width="521" height="13" uuid="71e54d49-c009-4e15-89e7-195471ac5912"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{prescricaoEnfermagemAtendimento}.getEvolucaoPrescricao()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="4" y="3" width="174" height="13" uuid="960c6175-1a2e-4672-851c-bde75d6ebc32"/>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Evolução Enfermagem*/
Bundle.getStringApplication("rotulo_evolucao_enfermagem") + ": "]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="grupo">
		<groupExpression><![CDATA[$F{prescricaoEnfermagemGrupo}.getCodigo()]]></groupExpression>
		<groupHeader>
			<band height="25">
				<rectangle>
					<reportElement x="0" y="0" width="535" height="25" uuid="71a600fa-32b4-4df9-89ff-948059dce15a"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" positionType="Float" mode="Transparent" x="6" y="5" width="523" height="15" uuid="6ac2cb95-afd6-41e5-8a99-f33d9c7cb3dc"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{prescricaoEnfermagemGrupo}.getDescricao().toUpperCase()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="74" splitType="Stretch">
			<rectangle radius="10">
				<reportElement x="0" y="5" width="535" height="60" uuid="4d1592a0-e7de-457b-a721-5302fe8b2cb4"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-4" positionType="Float" x="288" y="34" width="83" height="13" uuid="91fcdbd7-1936-471d-b3a0-ee0de15f5c65"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Data prescrição*/
Bundle.getStringApplication("rotulo_data_prescricao") + ": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" positionType="Float" mode="Transparent" x="8" y="8" width="50" height="13" uuid="bbb1b902-3b3c-46cf-a6cf-3aec0e4be2e2"/>
				<box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Paciente*/
Bundle.getStringApplication("rotulo_paciente") + ": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" positionType="Float" mode="Transparent" x="8" y="47" width="51" height="13" uuid="bbb1b902-3b3c-46cf-a6cf-3aec0e4be2e2"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Convenio*/
Bundle.getStringApplication("rotulo_convenio") + ": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-4" positionType="Float" x="288" y="47" width="42" height="13" uuid="91fcdbd7-1936-471d-b3a0-ee0de15f5c65">
					<printWhenExpression><![CDATA[(null != $F{quarto}.getDescricao())]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Quarto*/
Bundle.getStringApplication("rotulo_quarto") + ": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-4" positionType="Float" x="288" y="21" width="64" height="13" uuid="91fcdbd7-1936-471d-b3a0-ee0de15f5c65"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Profissional*/
Bundle.getStringApplication("rotulo_profissional") + ": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-4" positionType="Float" x="417" y="47" width="33" height="13" uuid="91fcdbd7-1936-471d-b3a0-ee0de15f5c65">
					<printWhenExpression><![CDATA[(null != $F{leito}.getDescricao())]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Leito*/
Bundle.getStringApplication("rotulo_leito") + ": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" positionType="Float" mode="Transparent" x="8" y="21" width="35" height="13" uuid="bbb1b902-3b3c-46cf-a6cf-3aec0e4be2e2"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Idade*/
Bundle.getStringApplication("rotulo_idade") + ": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" positionType="Float" mode="Transparent" x="105" y="21" width="33" height="13" uuid="bbb1b902-3b3c-46cf-a6cf-3aec0e4be2e2"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Sexo*/
Bundle.getStringApplication("rotulo_sexo") + ": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-4" positionType="Float" x="8" y="34" width="35" height="13" uuid="91fcdbd7-1936-471d-b3a0-ee0de15f5c65"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Setor*/
Bundle.getStringApplication("rotulo_setor") + ": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" positionType="Float" mode="Transparent" x="58" y="8" width="465" height="13" uuid="b76c0840-a00f-4671-b9c6-8184fe821d3d"/>
				<box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getDescricaoFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" positionType="Float" mode="Transparent" x="43" y="21" width="37" height="13" uuid="182bc0be-276b-4943-93fa-9af358876cb0"/>
				<box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getIdade()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" positionType="Float" mode="Transparent" x="138" y="21" width="103" height="13" uuid="456087b0-e0a7-401e-a4a4-2d43514afaa1"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getSexoFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-4" positionType="Float" x="351" y="21" width="172" height="13" uuid="e88f9bd1-**************-8589159c8c6e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{profissional}.getNome()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-4" positionType="Float" x="371" y="34" width="152" height="13" uuid="e69b78ac-858c-42fa-b0f2-df344499b1d7"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.formatar($F{prescricaoEnfermagemAtendimento}.getDataPrescricao())]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-4" positionType="Float" x="330" y="47" width="80" height="13" uuid="70905495-6d69-4151-a32b-4f540a3835b2">
					<printWhenExpression><![CDATA[(null != $F{quarto}.getDescricao())]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quarto}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-4" positionType="Float" x="450" y="47" width="73" height="13" uuid="7824f131-02d5-4801-b68c-2b39d690d594">
					<printWhenExpression><![CDATA[(null != $F{leito}.getDescricao())]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{leito}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-4" positionType="Float" x="43" y="34" width="230" height="13" uuid="457539a4-cd98-417f-a393-9c08605c83f0"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{setor}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" positionType="Float" mode="Transparent" x="59" y="47" width="215" height="13" uuid="20b38c99-de4d-444c-aa33-413f94b18fc8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{convenio}.getDescricao()]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="16" splitType="Stretch">
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="535" height="16" uuid="fb513c23-b6b0-47e6-9e00-793a812e23bc"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement key="textField" positionType="Float" mode="Transparent" x="6" y="1" width="396" height="13" uuid="fb81d3b7-d74c-4c1b-8c4c-325af236e20f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{prescricaoEnfermagem}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" mode="Transparent" x="415" y="1" width="114" height="13" uuid="efe44e74-c131-424d-aac5-20d0ce3f4de2"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{prescricaoEnfermagemAtendimentoItem}.getDescricaoAprazamento()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement stretchType="RelativeToBandHeight" x="408" y="0" width="1" height="16" uuid="dc78edce-7969-4031-9d25-263557cf6794"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
		</band>
	</detail>
	<lastPageFooter>
		<band height="50">
			<line>
				<reportElement x="283" y="37" width="252" height="1" uuid="c09131d3-6aa7-4dc4-8f46-c801d26d8c63"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-76" mode="Transparent" x="283" y="38" width="252" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="3793aebd-029b-4ac8-b848-a434312c60b7"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Profissional */
$F{profissional}.getNome()]]></textFieldExpression>
			</textField>
		</band>
	</lastPageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
