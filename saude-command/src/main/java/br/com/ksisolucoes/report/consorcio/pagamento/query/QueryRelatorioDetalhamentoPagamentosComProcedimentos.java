package br.com.ksisolucoes.report.consorcio.pagamento.query;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoPagamentosDTOParam;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoPagamentosProcedimentosDTO;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimentoItem;
import br.com.ksisolucoes.vo.consorcio.PagamentoGuiaProcedimento;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioDetalhamentoPagamentosComProcedimentos extends CommandQuery<QueryRelatorioDetalhamentoPagamentosComProcedimentos> implements ITransferDataReport<RelatorioDetalhamentoPagamentosDTOParam, RelatorioDetalhamentoPagamentosProcedimentosDTO> {

    private RelatorioDetalhamentoPagamentosDTOParam param;
    private List<RelatorioDetalhamentoPagamentosProcedimentosDTO> result;
    private String glosa;
    
    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("pagamentoGuiaProcedimento.codigo", "pagamentoGuiaProcedimento.codigo");
        hql.addToSelect("pagamentoGuiaProcedimento.chave", "pagamentoGuiaProcedimento.chave");
        hql.addToSelect("pagamentoGuiaProcedimento.documento", "pagamentoGuiaProcedimento.documento");
        hql.addToSelect("pagamentoGuiaProcedimento.dataPagamento", "pagamentoGuiaProcedimento.dataPagamento");
        hql.addToSelect("pagamentoGuiaProcedimento.valorTotalPagamento", "pagamentoGuiaProcedimento.valorTotalPagamento");
        hql.addToSelect("pagamentoGuiaProcedimento.valorTotalImpostoRenda", "pagamentoGuiaProcedimento.valorTotalImpostoRenda");
        hql.addToSelect("pagamentoGuiaProcedimento.valorTotalImpostoInss", "pagamentoGuiaProcedimento.valorTotalImpostoInss");
        hql.addToSelect("pagamentoGuiaProcedimento.valorTotalImpostoIss", "pagamentoGuiaProcedimento.valorTotalImpostoIss");
        hql.addToSelect("coalesce(pagamentoGuiaProcedimento.valorTotalDescontoGlosa,0)","pagamentoGuiaProcedimento.valorTotalDescontoGlosa");
        hql.addToSelect("pagamentoGuiaProcedimentoItem.valorPagamento", "valorPagamento");
        hql.addToSelect("pagamentoGuiaProcedimentoItem.valorImpostoRenda", "valorImpostoRenda");
        hql.addToSelect("pagamentoGuiaProcedimentoItem.valorImpostoInss", "valorImpostoInss");
        hql.addToSelect("pagamentoGuiaProcedimentoItem.valorImpostoIss", "valorImpostoIss");
        hql.addToSelect("pagamentoGuiaProcedimentoItem.valorDescontoGlosa", "valorDescontoGlosa");
        hql.addToSelect("consorcioGuiaProcedimento.codigo", "consorcioGuiaProcedimento.codigo");
        hql.addToSelect("consorcioGuiaProcedimento.nomePaciente", "consorcioGuiaProcedimento.nomePaciente");
        hql.addToSelect("consorcioGuiaProcedimento.dataCadastro", "consorcioGuiaProcedimento.dataCadastro");
        hql.addToSelect("empresaPrestador.codigo", "pagamentoGuiaProcedimento.consorcioPrestador.empresaPrestador.codigo");
        hql.addToSelect("empresaPrestador.descricao", "pagamentoGuiaProcedimento.consorcioPrestador.empresaPrestador.descricao");
        hql.addToSelect("consorciado.codigo", "consorcioGuiaProcedimento.subConta.conta.consorciado.codigo");
        hql.addToSelect("consorciado.descricao", "consorcioGuiaProcedimento.subConta.conta.consorciado.descricao");
        hql.addToSelect("pagamentoGuiaProcedimento.agencia","pagamentoGuiaProcedimento.agencia");
        hql.addToSelect("pagamentoGuiaProcedimento.numeroConta","pagamentoGuiaProcedimento.numeroConta");
        hql.addToSelect("pagamentoGuiaProcedimento.nomeTitularConta", "pagamentoGuiaProcedimento.nomeTitularConta");
        hql.addToSelect("pagamentoGuiaProcedimento.cpfCnpjConta", "pagamentoGuiaProcedimento.cpfCnpjConta");
        hql.addToSelect("bancoPrestador.codigo","pagamentoGuiaProcedimento.bancoPrestador.codigo");
        hql.addToSelect("bancoPrestador.descricaoBanco","pagamentoGuiaProcedimento.bancoPrestador.descricaoBanco");
        hql.addToSelect("bancoPrestador.numeroBanco","pagamentoGuiaProcedimento.bancoPrestador.numeroBanco");

        hql.addToSelect("guiaItem.codigo","consorcioGuiaProcedimentoItem.codigo");
        hql.addToSelect("guiaItem.quantidadeAplicacao","consorcioGuiaProcedimentoItem.quantidadeAplicacao");
        hql.addToSelect("guiaItem.valorProcedimento","consorcioGuiaProcedimentoItem.valorProcedimento");
        hql.addToSelect("consorcioProcedimento.codigo","consorcioGuiaProcedimentoItem.consorcioProcedimento.codigo");
        hql.addToSelect("consorcioProcedimento.descricaoProcedimento","consorcioGuiaProcedimentoItem.consorcioProcedimento.descricaoProcedimento");
        hql.addToSelect("consorcioProcedimento.referencia","consorcioGuiaProcedimentoItem.consorcioProcedimento.referencia");
        hql.addToSelect("tipoConta.codigo", "consorcioGuiaProcedimento.subConta.tipoConta.codigo");
        hql.addToSelect("tipoConta.referencia", "consorcioGuiaProcedimento.subConta.tipoConta.referencia");
        hql.addToSelect("tipoConta.descricao", "consorcioGuiaProcedimento.subConta.tipoConta.descricao");

        hql.setTypeSelect(RelatorioDetalhamentoPagamentosProcedimentosDTO.class.getName());
        hql.addToFrom("PagamentoGuiaProcedimentoItem pagamentoGuiaProcedimentoItem"
                + " left join pagamentoGuiaProcedimentoItem.pagamentoGuiaProcedimento pagamentoGuiaProcedimento"
                + " left join pagamentoGuiaProcedimentoItem.consorcioGuiaProcedimento consorcioGuiaProcedimento"
                + " left join pagamentoGuiaProcedimento.consorcioPrestador consorcioPrestador"
                + " left join pagamentoGuiaProcedimento.bancoPrestador bancoPrestador"
                + " left join consorcioPrestador.empresaPrestador empresaPrestador"
                + " left join consorcioGuiaProcedimento.subConta subConta"
                + " left join subConta.conta conta"
                + " left join subConta.tipoConta tipoConta"
                + " left join conta.consorciado consorciado");

        hql.addToFrom("ConsorcioGuiaProcedimentoItem guiaItem "
                + " left join guiaItem.consorcioGuiaProcedimento guia"
                + " left join guiaItem.consorcioProcedimento consorcioProcedimento ");

        hql.addToWhereWhithAnd("consorcioGuiaProcedimento.codigo = guia.codigo ");
        hql.addToWhereWhithAnd("guiaItem.status <> ", ConsorcioGuiaProcedimentoItem.StatusGuiaProcedimentoItem.CANCELADA.value());
        hql.addToWhereWhithAnd("pagamentoGuiaProcedimento.chave = ", param.getCodigoPagamento());
        hql.addToWhereWhithAnd("empresaPrestador = ", param.getPrestador());
        hql.addToWhereWhithAnd("consorciado = ", param.getConsorciado());
        hql.addToWhereWhithAnd("tipoConta = ", param.getTipoConta());
        hql.addToWhereWhithAnd("pagamentoGuiaProcedimento.dataPagamento ", param.getPeriodo());
        
        hql.addToWhereWhithAnd("pagamentoGuiaProcedimento.status =", PagamentoGuiaProcedimento.StatusPagamentoGuiaProcedimento.CONFIRMADO.value());
        
        hql.addToOrder("pagamentoGuiaProcedimento.codigo desc");
        
        if (param.isAgruparConsorciado()) {
            hql.addToOrder("consorciado.descricao");
        }
        
        if (param.getOrdenacao().equals(RelatorioDetalhamentoPagamentosDTOParam.Ordenacao.GUIA)) {
            hql.addToOrder("consorcioGuiaProcedimento.codigo");
        }else if (param.getOrdenacao().equals(RelatorioDetalhamentoPagamentosDTOParam.Ordenacao.CONSORCIADO)) {
            hql.addToOrder("consorciado.descricao");
        }else if (param.getOrdenacao().equals(RelatorioDetalhamentoPagamentosDTOParam.Ordenacao.NOME_PACIENTE)) {
            hql.addToOrder("consorcioGuiaProcedimento.nomePaciente");
        }

        try {
            glosa = BOFactory.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("utilizarDescontoGlosa");
        } catch (DAOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
    }

    @Override
    public void setDTOParam(RelatorioDetalhamentoPagamentosDTOParam param) {
        this.param = param;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>)result);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(this.result)) {
            Group<RelatorioDetalhamentoPagamentosProcedimentosDTO> byCodigo = Lambda.group(this.result, Lambda.by(Lambda.on(RelatorioDetalhamentoPagamentosProcedimentosDTO.class).getConsorcioGuiaProcedimento().getCodigo()));
            List<RelatorioDetalhamentoPagamentosProcedimentosDTO> groupedList = new ArrayList<>();
            for (Group<RelatorioDetalhamentoPagamentosProcedimentosDTO> group : byCodigo.subgroups()) {
                RelatorioDetalhamentoPagamentosProcedimentosDTO dto = group.first();
                List<RelatorioDetalhamentoPagamentosProcedimentosDTO> all = group.findAll();
                List<ConsorcioGuiaProcedimentoItem> itens = Lambda.extract(all, Lambda.on(RelatorioDetalhamentoPagamentosProcedimentosDTO.class).getConsorcioGuiaProcedimentoItem());
                dto.setConsorcioGuiaProcedimentoItemList(itens);
                groupedList.add(dto);
            }

            this.result.clear();
            this.result.addAll(groupedList);
        }
    }

    @Override
    public List<RelatorioDetalhamentoPagamentosProcedimentosDTO> getResult() {
        return result;
    }

}
