package br.com.ksisolucoes.report.agendamento;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.agendamento.dto.RelacaoSolicitacoesViagemDTOParam;
import br.com.ksisolucoes.report.agendamento.query.QueryRelacaoSolicitacoesViagem;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelacaoSolicitacoesViagem extends AbstractReport<RelacaoSolicitacoesViagemDTOParam> {

    public RelacaoSolicitacoesViagem(RelacaoSolicitacoesViagemDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/agendamento/jrxml/relacao_solicitacoes_viagem.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_solicitacoes_viagem");
    }

    @Override
    public ITransferDataReport getQuery() {
        this.addParametro("FA", this.getParam().getFormaApresentacao());
        return new QueryRelacaoSolicitacoesViagem();
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return this.getParam().getTipoRelatorio();
    }
}
