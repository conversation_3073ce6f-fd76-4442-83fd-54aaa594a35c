package br.com.ksisolucoes.report.consorcio.pedidotransferencialicitacao.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioReciboEntregaProdutosDTO;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioReciboEntregaProdutosDTOParam;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioReciboEntregaProdutos extends CommandQuery<QueryRelatorioReciboEntregaProdutos> implements ITransferDataReport<RelatorioReciboEntregaProdutosDTOParam, RelatorioReciboEntregaProdutosDTO> {

    private RelatorioReciboEntregaProdutosDTOParam param;
    private List<RelatorioReciboEntregaProdutosDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        
        hql.addToSelect("pedidoTransferenciaLicitacao.codigo", "codigo");
        hql.addToSelect("pedidoTransferenciaLicitacaoEntrega.codigo", "romaneio");
        hql.addToSelect("pedidoTransferenciaLicitacaoEntrega.dataEntrega", "dataEntrega");
        hql.addToSelect("pedidoTransferenciaLicitacaoEntrega.responsavelTransporte", "responsavelEntrega");
        hql.addToSelect("empresaConsorciado.codigo", "empresaConsorciado.codigo");
        hql.addToSelect("empresaConsorciado.descricao", "empresaConsorciado.descricao");
        hql.addToSelect("produto.codigo", "produto.codigo");
        hql.addToSelect("produto.descricao", "produto.descricao");
        hql.addToSelect("unidade.codigo", "produto.unidade.codigo");
        hql.addToSelect("unidade.unidade", "produto.unidade.unidade");
        hql.addToSelect("pedidoTransferenciaLicitacaoEntregaItem.quantidade", "quantidadeTotalItem");
        hql.addToSelect("pedidoTransferenciaLicitacaoEntregaLote.quantidade", "quantidade");
        hql.addToSelect("pedidoTransferenciaLicitacaoEntregaItem.valor", "valor");
        hql.addToSelect("pedidoTransferenciaLicitacaoEntregaItem.precoUnitario", "precoUnitario");
        hql.addToSelect("pedidoTransferenciaLicitacaoEntregaLote.lote", "lote");
        hql.addToSelect("(select grupoEstoque.dataValidade from GrupoEstoque grupoEstoque where grupoEstoque.id.grupo = pedidoTransferenciaLicitacaoEntregaLote.lote"
                + " and grupoEstoque.id.codigoDeposito = deposito.codigo"
                + " and grupoEstoque.id.estoqueEmpresa.id.produto = produto"
                + " and grupoEstoque.id.estoqueEmpresa.id.empresa = empresaAlmoxarifado)", "dataValidade");
        hql.addToSelect("(select grupoEstoque.fabricante.descricao from GrupoEstoque grupoEstoque where grupoEstoque.id.grupo = pedidoTransferenciaLicitacaoEntregaLote.lote"
                + " and grupoEstoque.id.codigoDeposito = deposito.codigo"
                + " and grupoEstoque.id.estoqueEmpresa.id.produto = produto"
                + " and grupoEstoque.id.estoqueEmpresa.id.empresa = empresaAlmoxarifado)", "fabricante");
        hql.addToSelect("empresaConsorciado.rua", "empresaConsorciado.rua");
        hql.addToSelect("empresaConsorciado.numero", "empresaConsorciado.numero");
        hql.addToSelect("empresaConsorciado.complemento", "empresaConsorciado.complemento");
        hql.addToSelect("empresaConsorciado.bairro", "empresaConsorciado.bairro");
        hql.addToSelect("empresaConsorciado.cep", "empresaConsorciado.cep");
        hql.addToSelect("empresaConsorciado.cnpj", "empresaConsorciado.cnpj");
        hql.addToSelect("empresaConsorciadoCidade.descricao", "empresaConsorciado.cidade.descricao");
        hql.addToSelect("empresaConsorciadoEstado.sigla", "empresaConsorciado.cidade.estado.sigla");

        hql.addToSelect("empresaAlmoxarifado.descricao", "empresaAlmoxarifado.descricao");
        hql.addToSelect("empresaAlmoxarifado.rua", "empresaAlmoxarifado.rua");
        hql.addToSelect("empresaAlmoxarifado.numero", "empresaAlmoxarifado.numero");
        hql.addToSelect("empresaAlmoxarifado.complemento", "empresaAlmoxarifado.complemento");
        hql.addToSelect("empresaAlmoxarifado.bairro", "empresaAlmoxarifado.bairro");
        hql.addToSelect("empresaAlmoxarifado.cep", "empresaAlmoxarifado.cep");
        hql.addToSelect("empresaAlmoxarifado.cnpj", "empresaAlmoxarifado.cnpj");
        hql.addToSelect("empresaAlmoxarifadoCidade.descricao", "empresaAlmoxarifado.cidade.descricao");
        hql.addToSelect("empresaAlmoxarifadoEstado.sigla", "empresaAlmoxarifado.cidade.estado.sigla");
        hql.addToSelect("tipoConta.descricao", "tipoConta");

        hql.setTypeSelect(RelatorioReciboEntregaProdutosDTO.class.getName());
        hql.addToFrom("PedidoTransferenciaLicitacaoEntregaLote pedidoTransferenciaLicitacaoEntregaLote"
                + " right join pedidoTransferenciaLicitacaoEntregaLote.pedidoTransferenciaLicitacaoEntregaItem pedidoTransferenciaLicitacaoEntregaItem"
                + " left join pedidoTransferenciaLicitacaoEntregaItem.pedidoTransferenciaLicitacaoEntrega pedidoTransferenciaLicitacaoEntrega"
                + " left join pedidoTransferenciaLicitacaoEntregaItem.pedidoTransferenciaLicitacaoItem pedidoTransferenciaLicitacaoItem"
                + " left join pedidoTransferenciaLicitacaoItem.pedidoTransferenciaLicitacao pedidoTransferenciaLicitacao"
                + " left join pedidoTransferenciaLicitacao.empresaConsorciado empresaConsorciado"
                + " left join empresaConsorciado.cidade empresaConsorciadoCidade"
                + " left join empresaConsorciadoCidade.estado empresaConsorciadoEstado"
                + " left join pedidoTransferenciaLicitacao.empresaAlmoxarifado empresaAlmoxarifado"
                + " left join empresaAlmoxarifado.cidade empresaAlmoxarifadoCidade"
                + " left join empresaAlmoxarifadoCidade.estado empresaAlmoxarifadoEstado"
                + " left join empresaAlmoxarifado.empresaMaterial empresaMaterial"
                + " left join empresaMaterial.deposito deposito"
                + " left join pedidoTransferenciaLicitacaoItem.produto produto"
                + " left join produto.unidade unidade"
                + " left join pedidoTransferenciaLicitacao.licitacao licitacao"
                + " left join licitacao.tipoConta tipoConta");

        hql.addToWhereWhithAnd("pedidoTransferenciaLicitacaoEntrega.codigo =", param.getCodigoPedidoTransferenciaEntrega());
        
        hql.addToOrder("produto.descricao asc");
        hql.addToOrder("pedidoTransferenciaLicitacaoEntregaLote.lote asc");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>)result);
    }

    @Override
    public List<RelatorioReciboEntregaProdutosDTO> getResult() {
        return result;
    }
    
    @Override
    public void setDTOParam(RelatorioReciboEntregaProdutosDTOParam param) {
        this.param = param;
    }

}
