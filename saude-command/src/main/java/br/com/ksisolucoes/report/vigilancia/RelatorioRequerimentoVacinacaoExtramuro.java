package br.com.ksisolucoes.report.vigilancia;

import br.com.celk.io.FtpImageUtil;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.vigilancia.query.QueryRelatorioRequerimentoVacinacaoExtramuro;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaEnum;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 */
public class RelatorioRequerimentoVacinacaoExtramuro extends AbstractReport<Object> {

    private String urlQRCode;
    private boolean marcaDagua;
    private String pathMarcaDagua;

    public RelatorioRequerimentoVacinacaoExtramuro(Long codigoRequerimento, String urlQRCode) {
        super(codigoRequerimento);
        this.urlQRCode = urlQRCode;
    }

    @Override
    public ITransferDataReport getQuery() throws ValidacaoException {
        ConfiguracaoVigilancia configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        addParametro("textoAutorizacaoExumacao1", configuracaoVigilancia.getTextoReqExumacao1());
        addParametro("textoAutorizacaoExumacao2", configuracaoVigilancia.getTextoReqExumacao2());
        addParametro("cidade", SessaoAplicacaoImp.getInstance().getEmpresa().getCidade().getDescricao());
        addParametro("uf", SessaoAplicacaoImp.getInstance().getEmpresa().getCidade().getEstado().getSigla());

        if(urlQRCode != null) {
            addParametro("urlQRcode", urlQRCode);
        } else {
            addParametro("urlQRcode", "");
        }


        if (ConfiguracaoVigilanciaEnum.TipoMarcaDagua.IMAGEM_BRASAO.value().equals(configuracaoVigilancia.getMarcaDaguaAlvara())) {
            marcaDagua = true;
            try {
                GerenciadorArquivo brasaoMarcaDagua = configuracaoVigilancia.getBrasaoMarcaDagua();
                if (brasaoMarcaDagua != null && StringUtils.trimToNull(brasaoMarcaDagua.getCaminho()) != null) {
                    pathMarcaDagua = new FtpImageUtil().downloadImage(brasaoMarcaDagua.getCaminho());
                } else {
                    marcaDagua = false;
                    Loggable.log.warn("BRASÃO NAO DEFINIDO!");
                }
            } catch (Throwable ex) {
                marcaDagua = false;
                Loggable.log.warn("BRASÃO NAO ENCONTRADO!", ex);
            }
        }

        addParametro("marcaDagua", this.marcaDagua);
        addParametro("pathMarcaDagua", this.pathMarcaDagua);

        if (configuracaoVigilancia != null) {
            Empresa empresa = LoadManager.getInstance(Empresa.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_CODIGO, configuracaoVigilancia.getEmpresa().getCodigo())).setMaxResults(1).start().getVO();

            StringBuilder enderecoVigilanciaBuilder = new StringBuilder(Coalesce.asString(empresa.getEnderecoCidadeBairroFormatado()));
            enderecoVigilanciaBuilder.append(" - CEP:");
            enderecoVigilanciaBuilder.append(empresa.getCepFormatado());
            enderecoVigilanciaBuilder.append(" - Fone:");
            enderecoVigilanciaBuilder.append(empresa.getTelefoneFormatado());
            addParametro("ENDERECO_VIGILANCIA", enderecoVigilanciaBuilder.toString());

            addParametro("CONCEDIDO_POR", configuracaoVigilancia.getEmpresa());
        }

        try {
            String gerarDocumentoComAssinaturaFiscal = BOFactory.getBO(CommomFacade.class).modulo(Modulos.VIGILANCIA_SANITARIA).getParametro("gerarDocumentoComAssinaturaFiscal");
            if(RepositoryComponentDefault.SIM.equals(gerarDocumentoComAssinaturaFiscal)) {
                addParametro("gerarDocumentoComAssinaturaFiscal", true);
            } else {
                addParametro("gerarDocumentoComAssinaturaFiscal", false);
            }
        } catch (DAOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }

        return new QueryRelatorioRequerimentoVacinacaoExtramuro();
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/vigilancia/jrxml/requerimento_vacinacao_extramuro.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_termo_autorizacao_sanitaria_vacinacao_extramuro_esporadica");
    }

}
