package br.com.ksisolucoes.report.prontuario.basico.query;

import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioPerfilAtendimentoDTO;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioPerfilAtendimentoDTOParam;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo;
import java.util.List;
import java.util.Map;
import org.hibernate.Query;

/**
 *
 * <AUTHOR>
 */
public class QueryPerfilAtendimentoAtividadeGrupo extends QueryPerfilAtendimento {

    private List<RelatorioPerfilAtendimentoDTO> result;
    private RelatorioPerfilAtendimentoDTOParam param;

    public QueryPerfilAtendimentoAtividadeGrupo(RelatorioPerfilAtendimentoDTOParam param) {
        super(param);
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("ta.descricao ", "descricao");
        hql.addToSelect("sum(1)", "quantidade");
        {
            //subSelect para o total
            HQLHelper hqlTotal = hql.getNewInstanceSubQuery();
            hqlTotal.addToSelect("count(ag1.codigo)");
            hqlTotal.addToFrom("AtividadeGrupo ag1");
            hqlTotal.addToWhereWhithAnd("ag1.dataInicio ", param.getPeriodo());
            hqlTotal.addToWhereWhithAnd("ag1.situacao = ", AtividadeGrupo.SITUACAO_CONCLUIDA);

            if (this.param.getProfissionais() != null && CollectionUtils.isNotNullEmpty(this.param.getProfissionais())) {
                hqlTotal.addToWhereWhithAnd("exists (select 1 from AtividadeGrupoProfissional agp1 where agp1.profissional in (:profissionais) and agp1.atividadeGrupo = ag1)");
            }

            if (this.param.getTabelasCbo() != null && CollectionUtils.isNotNullEmpty(this.param.getTabelasCbo())) {
                hqlTotal.addToWhereWhithAnd("exists (select 1 from AtividadeGrupoProfissional agp1 where agp1.tabelaCbo in (:cbos) and agp1.atividadeGrupo = ag1)");
            }

            if  (this.param.getEmpresas() != null && CollectionUtils.isNotNullEmpty(this.param.getEmpresas())){
                hqlTotal.addToWhereWhithAnd("ag1.empresa in (:empresa)");
            }

            hql.addToSelect("(" + hqlTotal.getQuery() + ")", "total");
        }
        hql.addToFrom("AtividadeGrupo ag "
                + " left join ag.tipoAtividadeGrupo ta");
        hql.setTypeSelect(RelatorioPerfilAtendimentoDTO.class.getName());
        hql.addToWhereWhithAnd("ag.dataInicio ", param.getPeriodo());
        hql.addToWhereWhithAnd("ag.situacao = ", AtividadeGrupo.SITUACAO_CONCLUIDA);

        if (this.param.getProfissionais() != null && CollectionUtils.isNotNullEmpty(this.param.getProfissionais())) {
            hql.addToWhereWhithAnd("exists (select 1 from AtividadeGrupoProfissional agp where agp.profissional in (:profissionais) and agp.atividadeGrupo = ag)");
        }

        if (this.param.getTabelasCbo() != null && CollectionUtils.isNotNullEmpty(this.param.getTabelasCbo())) {
            hql.addToWhereWhithAnd("exists (select 1 from AtividadeGrupoProfissional agp where agp.tabelaCbo in (:cbos) and agp.atividadeGrupo = ag)");
        }

        if  (this.param.getEmpresas() != null && CollectionUtils.isNotNullEmpty(this.param.getEmpresas())){
            hql.addToWhereWhithAnd("ag.empresa in (:empresa)");
        }

        hql.addToGroup("ta.descricao");
        hql.addToOrder("3 desc");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) {
        if (this.param.getProfissionais() != null && CollectionUtils.isNotNullEmpty(this.param.getProfissionais())) {
            query.setParameterList("profissionais", this.param.getProfissionais());
        }

        if (this.param.getTabelasCbo() != null && CollectionUtils.isNotNullEmpty(this.param.getTabelasCbo())) {
            query.setParameterList("cbos", this.param.getTabelasCbo());
        }
        if  (this.param.getEmpresas() != null && CollectionUtils.isNotNullEmpty(this.param.getEmpresas())){
            query.setParameterList("empresa",this.param.getEmpresas());
        }
    }

    @Override
    public List<RelatorioPerfilAtendimentoDTO> getResult() {
        return result;
    }
}