package br.com.ksisolucoes.report.consorcio.consorcioguiaprocedimento.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioResumoProcedimentosDTO;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioResumoProcedimentosDTOParam;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioResumoProcedimentosDTOParam.FormaApresentacao;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioResumoProcedimentosDTOParam.TipoResumo;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Parametro;
import br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento;
import br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimentoItem;
import org.hibernate.Query;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioResumoProcedimentos extends CommandQuery implements ITransferDataReport<RelatorioResumoProcedimentosDTOParam, RelatorioResumoProcedimentosDTO> {

    private RelatorioResumoProcedimentosDTOParam param;
    private List<RelatorioResumoProcedimentosDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {

        HQLHelper hqlSub = hql.getNewInstanceSubQuery();
        try {
            Date dataCompProcedimento = (Date) CargaBasicoPadrao.getInstance().getParametroPadrao().getPropertyValue(Parametro.PROP_DATA_COMPETENCIA_PROCEDIMENTO);

            hqlSub.addToSelect("(case when procedimentoCompetencia.valorServicoAmbulatorial > 0 then procedimentoCompetencia.valorServicoAmbulatorial else procedimentoCompetencia.valorServicoProfissional end)");
            hqlSub.addToFrom("ProcedimentoCompetencia procedimentoCompetencia");
            hqlSub.addToWhereWhithAnd("procedimentoCompetencia.id.procedimento.codigo = procedimento.codigo");
            hqlSub.addToWhereWhithAnd("procedimentoCompetencia.id.dataCompetencia = ", dataCompProcedimento);

        } catch (ValidacaoException e) {
            Loggable.log.error(e.getMessage(), e);
        }

        hql.addToSelect("sum(coalesce(consorcioGuiaProcedimentoItem.quantidadeAplicacao, 0))", "quantidadeAplicada");
        hql.addToSelect("sum(coalesce(consorcioGuiaProcedimentoItem.quantidade, 0))", "quantidade");

        hql.addToSelect("sum("
                + "case when consorcioGuiaProcedimentoItem.status = :ITEM_ABERTO"
                + "     then consorcioGuiaProcedimentoItem.quantidade "
                + "     else (case when consorcioGuiaProcedimentoItem.status = :ITEM_CANCELADO"
                + "                then (case when consorcioGuiaProcedimento.status = :GUIA_CANCELADA"
                + "                           then consorcioGuiaProcedimentoItem.quantidade"
                + "                           else 0"
                + "                       end)"
                + "                else consorcioGuiaProcedimentoItem.quantidadeAplicacao"
                + "            end)"
                + " end)", "somaQuantidade");

        hql.addToSelect("sum("
                + "case when consorcioGuiaProcedimentoItem.status = :ITEM_ABERTO"
                + "     then (consorcioGuiaProcedimentoItem.valorProcedimento * consorcioGuiaProcedimentoItem.quantidade) "
                + "     else (case when consorcioGuiaProcedimentoItem.status = :ITEM_CANCELADO"
                + "                then (case when consorcioGuiaProcedimento.status = :GUIA_CANCELADA"
                + "                           then (consorcioGuiaProcedimentoItem.valorProcedimento * consorcioGuiaProcedimentoItem.quantidade)"
                + "                           else 0"
                + "                       end)"
                + "                else (consorcioGuiaProcedimentoItem.valorProcedimento * consorcioGuiaProcedimentoItem.quantidadeAplicacao)"
                + "            end)"
                + " end)", "valor");

        hql.addToSelect("sum("
                + "case when consorcioGuiaProcedimentoItem.status = :ITEM_ABERTO"
                + "     then (coalesce((" + hqlSub.getQuery() + "), 0) * consorcioGuiaProcedimentoItem.quantidade) "
                + "     else (case when consorcioGuiaProcedimentoItem.status = :ITEM_CANCELADO"
                + "                then (case when consorcioGuiaProcedimento.status = :GUIA_CANCELADA"
                + "                           then (coalesce((" + hqlSub.getQuery() + "), 0) * consorcioGuiaProcedimentoItem.quantidade) "
                + "                           else 0"
                + "                       end)"
                + "                else (coalesce((" + hqlSub.getQuery() + "), 0) * consorcioGuiaProcedimentoItem.quantidadeAplicacao)"
                + "            end)"
                + " end)", "valorSUS");

        hql.setTypeSelect(RelatorioResumoProcedimentosDTO.class.getName());
        hql.addToFrom("ConsorcioGuiaProcedimentoItem consorcioGuiaProcedimentoItem"
                + " left join consorcioGuiaProcedimentoItem.consorcioGuiaProcedimento consorcioGuiaProcedimento"
                + " left join consorcioGuiaProcedimento.usuarioCadastro usuarioCadastro"
                + " left join consorcioGuiaProcedimento.usuarioCadsus usuarioCadsus"
                + " left join consorcioGuiaProcedimentoItem.consorcioProcedimento consorcioProcedimento"
                + " left join consorcioGuiaProcedimento.subConta subConta"
                + " left join consorcioGuiaProcedimento.cidade cidade"
                + " left join cidade.estado estado"
                + " left join subConta.conta conta"
                + " left join subConta.tipoConta tipoConta"
                + " left join conta.consorciado consorciado"
                + " left join consorcioGuiaProcedimento.consorcioPrestador consorcioPrestador"
                + " left join consorcioProcedimento.procedimento procedimento"
                + " left join procedimento.procedimentoFormaOrganizacao procedimentoFormaOrganizacao"
                + " left join procedimentoFormaOrganizacao.roProcedimentoSubGrupo procedimentoSubGrupo"
                + " left join procedimentoSubGrupo.roGrupo procedimentoGrupo"
                + " left join consorcioPrestador.empresaPrestador prestador");

        hql.addToWhereWhithAnd("prestador =", param.getPrestador());
        hql.addToWhereWhithAnd("consorcioProcedimento =", param.getConsorcioProcedimento());
        hql.addToWhereWhithAnd("consorciado =", param.getConsorciado());
        hql.addToWhereWhithAnd("usuarioCadsus =", param.getPaciente());
        hql.addToWhereWhithAnd("consorcioGuiaProcedimento." + param.getTipoData().value(), param.getPeriodo());
        hql.addToWhereWhithAnd("consorcioGuiaProcedimento.status in ", param.getInSituacao());
        hql.addToWhereWhithAnd("cidade = ", param.getCidade());
        hql.addToWhereWhithAnd("tipoConta = ", param.getTipoConta());

        if (this.param.getConsorcioGrupo() != null) {
            if(this.param.getConsorcioGrupo().getProcedimentoFormaOrganizacao() != null){
                hql.addToWhereWhithAnd("procedimentoFormaOrganizacao.id.codigo = ", this.param.getConsorcioGrupo().getProcedimentoFormaOrganizacao().getId().getCodigo());
                hql.addToWhereWhithAnd("procedimentoFormaOrganizacao.id.codigoProcedimentoSubGrupo = ", this.param.getConsorcioGrupo().getSubGrupo().getId().getCodigo());
                hql.addToWhereWhithAnd("procedimentoFormaOrganizacao.id.codigoProcedimentoGrupo = ", this.param.getConsorcioGrupo().getGrupo().getCodigo());
            }else if(this.param.getConsorcioGrupo().getSubGrupo() != null){
                hql.addToWhereWhithAnd("procedimentoFormaOrganizacao.id.codigoProcedimentoSubGrupo = ", this.param.getConsorcioGrupo().getSubGrupo().getId().getCodigo());
                hql.addToWhereWhithAnd("procedimentoFormaOrganizacao.id.codigoProcedimentoGrupo = ", this.param.getConsorcioGrupo().getGrupo().getCodigo());
            }else if(this.param.getConsorcioGrupo().getGrupo() != null){
                hql.addToWhereWhithAnd("procedimentoFormaOrganizacao.id.codigoProcedimentoGrupo = ", this.param.getConsorcioGrupo().getGrupo().getCodigo());
            }
        }

        if (this.param.getProcedimentoGrupo() != null) {
            hql.addToWhereWhithAnd("procedimentoGrupo = ", param.getProcedimentoGrupo());
        }
        if (this.param.getProcedimentoSubGrupo() != null) {
            hql.addToWhereWhithAnd("procedimentoSubGrupo = ", param.getProcedimentoSubGrupo());
        }
        if (this.param.getProcedimentoFormaOrganizacao() != null) {
            hql.addToWhereWhithAnd("procedimentoFormaOrganizacao = ", param.getProcedimentoFormaOrganizacao());
        }

        if (FormaApresentacao.CONSORCIADO.equals(param.getFormaApresentacao())) {
            addFormaApresentacao("consorciado.descricao", "formaApresentacao", true);
        } else if (FormaApresentacao.PROCEDIMENTO.equals(param.getFormaApresentacao())) {
            addFormaApresentacao(" ('(' || consorcioProcedimento.referencia || ') ' || consorcioProcedimento.descricaoProcedimento)", "formaApresentacao", true);
        } else if (FormaApresentacao.PRESTADOR.equals(param.getFormaApresentacao())) {
            addFormaApresentacao("prestador.descricao", "formaApresentacao", true);
        } else if (FormaApresentacao.DIARIO.equals(param.getFormaApresentacao())) {
            addFormaApresentacao("year(consorcioGuiaProcedimento." + param.getTipoData().value() + ")", "anoForma", false);
            addFormaApresentacao("month(consorcioGuiaProcedimento." + param.getTipoData().value() + ")", "mesForma", false);
            addFormaApresentacao("day(consorcioGuiaProcedimento." + param.getTipoData().value() + ")", "diaForma", false);
        } else if (FormaApresentacao.MENSAL.equals(param.getFormaApresentacao())) {
            addFormaApresentacao("year(consorcioGuiaProcedimento." + param.getTipoData().value() + ")", "anoForma", false);
            addFormaApresentacao("month(consorcioGuiaProcedimento." + param.getTipoData().value() + ")", "mesForma", false);
        } else if (FormaApresentacao.USUARIO.equals(param.getFormaApresentacao())) {
            addFormaApresentacao("usuarioCadastro.nome", "formaApresentacao", true);
        }else if(FormaApresentacao.CIDADE.equals(param.getFormaApresentacao())){
            addFormaApresentacao("(cidade.descricao || ' - ' || estado.sigla)","formaApresentacao",true);
        }

        if (FormaApresentacao.GRUPO_PROCEDIMENTO.equals(param.getFormaApresentacao()) && !param.getFormaApresentacao().toString().equals(param.getTipoResumo().toString())) {
            HQLHelper formaApresentacaoGrupoProcedimento = formaApresentacaoTipoResumo(hql);
            hql.addToSelect("coalesce((" + formaApresentacaoGrupoProcedimento.getQuery() + "), consorcioProcedimento.descricaoProcedimento)", "formaApresentacao");
            hql.addToGroupAndOrder("6");
            hql.addToGroupAndOrder("7");
        }

        if (TipoResumo.CONSORCIADO.equals(param.getTipoResumo())) {
            addTipoResumo("consorciado.descricao", "tipoResumo", true);
        } else if (TipoResumo.PRESTADOR.equals(param.getTipoResumo())) {
            addTipoResumo("prestador.descricao", "tipoResumo", true);
        } else if (TipoResumo.PROCEDIMENTO.equals(param.getTipoResumo())) {
            addTipoResumo(" ('(' || consorcioProcedimento.referencia || ') ' || consorcioProcedimento.descricaoProcedimento)", "tipoResumo", true);
        } else if (TipoResumo.DIARIO.equals(param.getTipoResumo())) {
            addTipoResumo("year(consorcioGuiaProcedimento." + param.getTipoData().value() + ")", "anoResumo", false);
            addTipoResumo("month(consorcioGuiaProcedimento." + param.getTipoData().value() + ")", "mesResumo", false);
            addTipoResumo("day(consorcioGuiaProcedimento." + param.getTipoData().value() + ")", "diaResumo", false);
        } else if (TipoResumo.MENSAL.equals(param.getTipoResumo())) {
            addTipoResumo("year(consorcioGuiaProcedimento." + param.getTipoData().value() + ")", "anoResumo", false);
            addTipoResumo("month(consorcioGuiaProcedimento." + param.getTipoData().value() + ")", "mesResumo", false);
        } else if (TipoResumo.USUARIO.equals(param.getTipoResumo())) {
            addTipoResumo("usuarioCadastro.nome", "tipoResumo", true);
        } else if (TipoResumo.CIDADE.equals(param.getTipoResumo())){
            addTipoResumo("(cidade.descricao || ' - ' || estado.sigla)","tipoResumo",true);
        }

        if (TipoResumo.GRUPO_PROCEDIMENTO.equals(param.getTipoResumo())) {
            HQLHelper tipoResumoGrupoProcedimento = formaApresentacaoTipoResumo(hql);
            hql.addToSelect("coalesce((" + tipoResumoGrupoProcedimento.getQuery() + "), consorcioProcedimento.descricaoProcedimento)", "tipoResumo");
            if (!param.getFormaApresentacao().toString().equals(param.getTipoResumo().toString())) {
                hql.addToGroupAndOrder("6");
                hql.addToGroupAndOrder("7");
            } else {
                hql.addToGroupAndOrder("6");
            }
        }

        if (RelatorioResumoProcedimentosDTOParam.Ordenacao.VALOR.equals(param.getOrdenacao())) {
            hql.addToOrder("1 desc");
        } else if (RelatorioResumoProcedimentosDTOParam.Ordenacao.QUANTIDADE_APLICADA.equals(param.getOrdenacao())) {
            hql.addToOrder("2 desc");
        } else if (RelatorioResumoProcedimentosDTOParam.Ordenacao.QUANTIDADE.equals(param.getOrdenacao())) {
            hql.addToOrder("3 desc");
        }

    }

    private HQLHelper formaApresentacaoTipoResumo(HQLHelper hql) {
        HQLHelper subQuery = hql.getNewInstanceSubQuery();

        subQuery.addToSelect("cg.descricao");
        subQuery.addToFrom("ConsorcioGrupo cg");

        subQuery.addToWhereWhithOr(
                "cg.procedimentoFormaOrganizacao.id.codigo = procedimentoFormaOrganizacao.id.codigo and " +
                "cg.procedimentoFormaOrganizacao.id.codigoProcedimentoGrupo = procedimentoFormaOrganizacao.id.codigoProcedimentoGrupo and " +
                "cg.procedimentoFormaOrganizacao.id.codigoProcedimentoSubGrupo = procedimentoFormaOrganizacao.id.codigoProcedimentoSubGrupo"
        );

        subQuery.addToWhereWhithOr(
                "cg.procedimentoFormaOrganizacao.id.codigo is null and " +
                "cg.subGrupo.id.codigo = procedimentoFormaOrganizacao.id.codigoProcedimentoSubGrupo and " +
                "cg.subGrupo.id.codigoGrupo = procedimentoFormaOrganizacao.id.codigoProcedimentoGrupo"
        );

        subQuery.addToWhereWhithOr(
                "cg.subGrupo.id.codigo is null and " +
                "cg.procedimentoFormaOrganizacao.id.codigo is null and " +
                "cg.procedimentoFormaOrganizacao.id.codigoProcedimentoGrupo is null and " +
                "cg.procedimentoFormaOrganizacao.id.codigoProcedimentoSubGrupo is null and " +
                "cg.grupo.codigo = procedimentoFormaOrganizacao.id.codigoProcedimentoGrupo"
        );

        return subQuery;
    }

    private void addFormaApresentacao(String select, String prop, boolean asc) {
        hql.addToSelectAndGroup(select, prop);
        if (!param.getFormaApresentacao().toString().equals(param.getTipoResumo().toString())) {
            hql.addToOrder(select + " " + (asc ? "asc" : "desc"));
        }
    }

    private void addTipoResumo(String select, String prop, boolean asc) {
        hql.addToSelectAndGroup(select, prop);
        if (RelatorioResumoProcedimentosDTOParam.Ordenacao.TIPO_RESUMO.equals(param.getOrdenacao())) {
            hql.addToOrder(select + " " + (asc ? "asc" : "desc"));
        }
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setParameter("ITEM_ABERTO", ConsorcioGuiaProcedimentoItem.StatusGuiaProcedimentoItem.ABERTA.value());
        query.setParameter("ITEM_CANCELADO", ConsorcioGuiaProcedimentoItem.StatusGuiaProcedimentoItem.CANCELADA.value());
        query.setParameter("GUIA_CANCELADA", ConsorcioGuiaProcedimento.StatusGuiaProcedimento.CANCELADA.value());
    }

    @Override
    public Collection getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List) result);
    }

    @Override
    public void setDTOParam(RelatorioResumoProcedimentosDTOParam param) {
        this.param = param;
    }
}
