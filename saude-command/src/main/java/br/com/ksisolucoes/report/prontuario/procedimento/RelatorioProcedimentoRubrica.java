/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.prontuario.procedimento;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRubrica;
import java.util.Collection;

/**
 *
 * <AUTHOR>
 */
public class RelatorioProcedimentoRubrica extends AbstractReport {

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_rubrica.jrxml";
    }

    @Override
    public Collection getCollection() throws DAOException, ValidacaoException {
        return LoadManager.getInstance(ProcedimentoRubrica.class).start().getList();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_sub_tipo_finaciamento");
    }
}
