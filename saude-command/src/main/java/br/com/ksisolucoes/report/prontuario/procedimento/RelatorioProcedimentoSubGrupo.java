/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.prontuario.procedimento;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.prontuario.procedimento.query.QueryRelatorioProcedimentoSubGrupo;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioProcedimentoSubGrupoParam;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioProcedimentoSubGrupo extends AbstractReport<RelatorioProcedimentoSubGrupoParam>{

    public RelatorioProcedimentoSubGrupo(RelatorioProcedimentoSubGrupoParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_sub_grupo.jrxml";
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioProcedimentoSubGrupo();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relatorio_sub_grupo");
    }
}
