package br.com.ksisolucoes.report.prontuario.basico.query;

import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioPerfilAtendimentoDTO;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioPerfilAtendimentoDTOParam;
import br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryEncaminhamentoEspecialista extends QueryPerfilAtendimento {

    private List<RelatorioPerfilAtendimentoDTO> result;

    public QueryEncaminhamentoEspecialista(RelatorioPerfilAtendimentoDTOParam param) {
        super(param);
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelectAndGroup("te.codigo", "codigo");
        hql.addToSelectAndGroup("te.descricao ", "descricao");
        hql.addToSelect("sum(1)", "quantidade");
        {
            //subSelect para o total
            HQLHelper hqlTotal = hql.getNewInstanceSubQuery();
            hqlTotal.addToSelect("count(e1.codigo)");
            hqlTotal.addToFrom("Encaminhamento e1"
                    + " join e1.tipoEncaminhamento te1"
                    + " left join e1.atendimento a1");
            hqlTotal.addToWhereWhithAnd("e1.usuarioCadsus in ", getParam().getUsuarioCadsus());
            hqlTotal.addToWhereWhithAnd("e1.profissional in ", getParam().getProfissionais());
            hqlTotal.addToWhereWhithAnd("e1.status <> ", Encaminhamento.STATUS_CANCELADO);
            addWhereAtendimento(hqlTotal, "a1", false);
            hql.addToSelect("(" + hqlTotal.getQuery() + ")", "total");
        }
        hql.addToFrom("Encaminhamento e"
                + " left join e.atendimento a"
                + " join e.tipoEncaminhamento te");
        hql.setTypeSelect(RelatorioPerfilAtendimentoDTO.class.getName());
        hql.addToWhereWhithAnd("e.usuarioCadsus in ", getParam().getUsuarioCadsus());
        hql.addToWhereWhithAnd("e.profissional in ", getParam().getProfissionais());
        hql.addToWhereWhithAnd("e.status <> ", Encaminhamento.STATUS_CANCELADO);
        addWhereAtendimento(hql, "a", false);
        hql.addToOrder("3 desc");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioPerfilAtendimentoDTO> getResult() {
        return result;
    }
}
