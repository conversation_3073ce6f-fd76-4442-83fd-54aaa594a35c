<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="sub_relatorio_impressao_ficha_acolhimento_part_1" pageWidth="550" pageHeight="842" columnWidth="550" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="03f38e68-54e0-484e-aee2-d8c2366d83e8">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="453"/>
	<import value="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<import value="br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<parameter name="fichaAcolhimento" class="br.com.ksisolucoes.vo.prontuario.FichaAcolhimento"/>
	<parameter name="peso" class="java.lang.Double"/>
	<parameter name="pad" class="java.lang.Long"/>
	<parameter name="pas" class="java.lang.Long"/>
	<parameter name="atendimento" class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"/>
	<parameter name="altura" class="java.lang.Double"/>
	<parameter name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<parameter name="cidade" class="br.com.ksisolucoes.vo.basico.Cidade"/>
	<parameter name="cidadeNascimento" class="br.com.ksisolucoes.vo.basico.Cidade"/>
	<parameter name="enderecoUsuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus"/>
	<detail>
		<band height="569">
			<rectangle>
				<reportElement uuid="89a366c6-cdf5-4bd2-af2c-be17a2c61381" x="483" y="15" width="67" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="89a366c6-cdf5-4bd2-af2c-be17a2c61381" x="0" y="15" width="405" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="89a366c6-cdf5-4bd2-af2c-be17a2c61381" x="405" y="15" width="78" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="89a366c6-cdf5-4bd2-af2c-be17a2c61381" mode="Transparent" x="91" y="47" width="52" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="89a366c6-cdf5-4bd2-af2c-be17a2c61381" x="307" y="47" width="36" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="5e7ef14b-efe8-4569-ba4d-ef6bbf8c64b9" mode="Transparent" x="0" y="47" width="550" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="c6e72a7b-9706-4b7e-bbbb-8f8da38847ea" x="0" y="79" width="550" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="89a366c6-cdf5-4bd2-af2c-be17a2c61381" x="175" y="79" width="209" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="c7d39a0d-d959-4304-bf8a-cd431da0cf9f" x="0" y="111" width="550" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="89a366c6-cdf5-4bd2-af2c-be17a2c61381" x="89" y="111" width="86" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="89a366c6-cdf5-4bd2-af2c-be17a2c61381" x="305" y="111" width="117" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="89a366c6-cdf5-4bd2-af2c-be17a2c61381" x="175" y="111" width="130" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="89a366c6-cdf5-4bd2-af2c-be17a2c61381" mode="Transparent" x="291" y="143" width="117" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="a45a0562-3bf8-49d5-854c-5e345a790c57" mode="Transparent" x="0" y="143" width="550" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="f427da69-48f5-4253-8a86-497b7c6c5416" x="0" y="175" width="550" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="89a366c6-cdf5-4bd2-af2c-be17a2c61381" x="0" y="175" width="253" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="2141602a-8079-4119-bc37-c6aca1522bc3" mode="Transparent" x="0" y="207" width="550" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="89a366c6-cdf5-4bd2-af2c-be17a2c61381" x="253" y="207" width="155" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="89a366c6-cdf5-4bd2-af2c-be17a2c61381" x="408" y="207" width="142" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="f427da69-48f5-4253-8a86-497b7c6c5416" x="0" y="239" width="550" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="89a366c6-cdf5-4bd2-af2c-be17a2c61381" x="0" y="239" width="253" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="89a366c6-cdf5-4bd2-af2c-be17a2c61381" mode="Transparent" x="296" y="271" width="254" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="f427da69-48f5-4253-8a86-497b7c6c5416" mode="Transparent" x="0" y="271" width="550" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="89a366c6-cdf5-4bd2-af2c-be17a2c61381" x="253" y="303" width="153" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="f427da69-48f5-4253-8a86-497b7c6c5416" mode="Transparent" x="0" y="303" width="550" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="89a366c6-cdf5-4bd2-af2c-be17a2c61381" x="0" y="303" width="253" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="f427da69-48f5-4253-8a86-497b7c6c5416" mode="Transparent" x="80" y="361" width="116" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="f427da69-48f5-4253-8a86-497b7c6c5416" mode="Transparent" x="196" y="361" width="95" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="f427da69-48f5-4253-8a86-497b7c6c5416" mode="Transparent" x="1" y="361" width="79" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="f427da69-48f5-4253-8a86-497b7c6c5416" mode="Transparent" x="0" y="418" width="550" height="17"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="f427da69-48f5-4253-8a86-497b7c6c5416" mode="Transparent" x="0" y="460" width="550" height="32"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement uuid="f427da69-48f5-4253-8a86-497b7c6c5416" mode="Transparent" x="0" y="492" width="550" height="77"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement uuid="7103b3fc-476b-4b3c-a44f-6bd4ac883466" key="textField-9" positionType="Float" x="2" y="144" width="173" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Endereco*/Bundle.getStringApplication("rotulo_endereco")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="0" y="444" width="122" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["4. "+Bundle.getStringApplication("rotulo_historico_saude")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="3d992217-1da6-4d45-b69c-55da2fbf36e5" key="textField-9" positionType="Float" x="410" y="208" width="122" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*tel 2*/Bundle.getStringApplication("rotulo_cont_fone_2")]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="2" y="63" width="89" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{usuarioCadsus}.getDataNascimento()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="93" y="63" width="50" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{usuarioCadsus}.getIdade()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="255" y="523" width="295" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Exames*/Bundle.getStringApplication("rotulo_exames_realizados")+": "+
($P{fichaAcolhimento}.getExamesRealizadosNeuro() != null ? $P{fichaAcolhimento}.getExamesRealizadosNeuro() : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="307" y="112" width="88" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Trabalho*/Bundle.getStringApplication("rotulo_local_trabalho")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="2" y="508" width="253" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Psiquiatra*/Bundle.getStringApplication("rotulo_consultou_psiquiatra_outro_servico")+": "+
(RepositoryComponentDefault.SIM_LONG.equals($P{fichaAcolhimento}.getConsultouPsiquiatraOutros()) ?
Bundle.getStringApplication("rotulo_sim")
:
Bundle.getStringApplication("rotulo_nao"))]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="91" y="127" width="83" height="13">
					<printWhenExpression><![CDATA[$P{usuarioCadsus}.getEstadoCivil() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{usuarioCadsus}.getEstadoCivil().getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="309" y="48" width="34" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*UF*/Bundle.getStringApplication("rotulo_uf")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="0" y="402" width="122" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["3. "+Bundle.getStringApplication("rotulo_encaminhamento")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="386" y="95" width="162" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[UsuarioCadsusHelper.carregarNumeroCartao($P{usuarioCadsus})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="255" y="255" width="293" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{usuarioCadsus}.getNomeMae()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="177" y="95" width="207" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{usuarioCadsus}.getRg()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="255" y="223" width="150" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Util.getTelefoneFormatado($P{usuarioCadsus}.getTelefone())]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="82" y="362" width="114" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Pressao*/Bundle.getStringApplication("rotulo_pressao_arterial")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="177" y="127" width="128" height="13">
					<printWhenExpression><![CDATA[$P{usuarioCadsus}.getTabelaCbo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{usuarioCadsus}.getTabelaCbo().getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="145" y="63" width="161" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{cidadeNascimento}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="408" y="304" width="140" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Grau Parentesco*/Bundle.getStringApplication("rotulo_grau_parentesco")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="309" y="63" width="34" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{cidadeNascimento}.getEstado().getSigla()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="152ffd82-00bc-4ec2-88cf-ab72245beb47" key="textField-9" positionType="Float" x="2" y="48" width="89" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Dt Nasc*/Bundle.getStringApplication("rotulo_data_nascimento_abv")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="409" y="144" width="117" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Bairro*/Bundle.getStringApplication("rotulo_bairro")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="2" y="191" width="251" height="13">
					<printWhenExpression><![CDATA[$P{enderecoUsuarioCadsus} != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{enderecoUsuarioCadsus}.getPontoReferencia()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="298" y="287" width="250" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{usuarioCadsus}.getParentescoResponsavel()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="2" y="127" width="87" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{usuarioCadsus}.getReligiao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="2" y="272" width="122" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Responsavel*/Bundle.getStringApplication("rotulo_responsavel")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="407" y="31" width="76" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[UsuarioCadsusHelper.carregarProntuario($P{usuarioCadsus},$P{atendimento}.getEmpresa())]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="409" y="159" width="140" height="13">
					<printWhenExpression><![CDATA[$P{enderecoUsuarioCadsus} != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{enderecoUsuarioCadsus}.getBairro()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="1" y="0" width="122" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["1. "+Bundle.getStringApplication("rotulo_identificacao")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="255" y="304" width="122" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Telefone*/Bundle.getStringApplication("rotulo_telefone")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="386" y="80" width="162" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Cartao Sus*/Bundle.getStringApplication("rotulo_cartao_sus")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="2" y="538" width="548" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Cirurgias Feitas*/Bundle.getStringApplication("rotulo_cirurgias_feitas")+": "+
($P{fichaAcolhimento}.getCirurgiasFeitas() != null ? $P{fichaAcolhimento}.getCirurgiasFeitas() : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="177" y="80" width="77" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*RG*/Bundle.getStringApplication("rotulo_rg")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="2" y="159" width="286" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{enderecoUsuarioCadsus}.getRuaFormatada()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="485" y="16" width="63" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Sexo*/Bundle.getStringApplication("rotulo_sexo")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="408" y="319" width="140" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{usuarioCadsus}.getGrauParentescoUrgencia()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="2" y="419" width="547" height="13">
					<printWhenExpression><![CDATA[$P{fichaAcolhimento}.getTipoEncaminhamento() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{fichaAcolhimento}.getTipoEncaminhamento().getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="255" y="508" width="295" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Qual Servico*/Bundle.getStringApplication("rotulo_qual_servico")+": "+
($P{fichaAcolhimento}.getServicoPsiquiatra() != null ?  $P{fichaAcolhimento}.getServicoPsiquiatra(): "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="298" y="272" width="160" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Grau Parentesco*/Bundle.getStringApplication("rotulo_grau_parentesco")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="82" y="377" width="114" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{pas} != null ? $P{pas} : "")+" / "+
($P{pad} != null ? $P{pad} : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="424" y="112" width="117" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Tel trab*/Bundle.getStringApplication("rotulo_telefone_trabalho")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="3" y="362" width="77" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Peso*/Bundle.getStringApplication("rotulo_peso_kg")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="485" y="31" width="63" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{usuarioCadsus}.getSexoFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="2" y="461" width="547" height="30">
					<printWhenExpression><![CDATA[$P{fichaAcolhimento}.getTipoEncaminhamento() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{fichaAcolhimento}.getHistoricoSaudeDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="177" y="112" width="77" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Profissao*/Bundle.getStringApplication("rotulo_profissao")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="2" y="176" width="122" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Refe*/Bundle.getStringApplication("rotulo_ponto_referencia")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="255" y="319" width="150" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Util.getTelefoneFormatado($P{usuarioCadsus}.getTelefoneUrgencia())]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="2" y="523" width="253" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Neurologista*/Bundle.getStringApplication("rotulo_consultou_com_neurologista")+": "+
(RepositoryComponentDefault.SIM_LONG.equals($P{fichaAcolhimento}.getConsultouNeurologista()) ?
Bundle.getStringApplication("rotulo_sim")
:
Bundle.getStringApplication("rotulo_nao"))]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="d06dd779-596b-4a51-a77b-1ca61ba9fa36" key="textField-9" positionType="Float" x="2" y="112" width="87" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Religiao*/Bundle.getStringApplication("rotulo_religiao")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="2" y="31" width="404" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{usuarioCadsus}.getNome()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="293" y="144" width="112" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Complemento*/Bundle.getStringApplication("rotulo_complemento")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="2" y="16" width="173" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Nome*/Bundle.getStringApplication("rotulo_nome_usuario")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="145" y="48" width="161" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Natural*/Bundle.getStringApplication("rotulo_natural_de")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="345" y="63" width="204" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{usuarioCadsus}.getEscolaridade().getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="407" y="16" width="66" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Prontuario*/Bundle.getStringApplication("rotulo_prontuario")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="293" y="159" width="112" height="13">
					<printWhenExpression><![CDATA[$P{enderecoUsuarioCadsus} != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{enderecoUsuarioCadsus}.getComplementoLogradouro()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="d06dd779-596b-4a51-a77b-1ca61ba9fa36" key="textField-9" positionType="Float" x="91" y="112" width="83" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Estado Civil*/Bundle.getStringApplication("rotulo_estado_civil")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="2" y="240" width="122" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Pai*/Bundle.getStringApplication("rotulo_filiacao_pai")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="2" y="304" width="122" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Urgencia*/Bundle.getStringApplication("rotulo_em_caso_urgencia_nome")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="2" y="493" width="253" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Caps*/Bundle.getStringApplication("rotulo_frequentou_outros_servicos_caps")+": "+
(RepositoryComponentDefault.SIM_LONG.equals($P{fichaAcolhimento}.getFrequentouOutrosServicos()) ?
Bundle.getStringApplication("rotulo_sim")
:
Bundle.getStringApplication("rotulo_nao"))]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="3d992217-1da6-4d45-b69c-55da2fbf36e5" key="textField-9" positionType="Float" x="255" y="176" width="122" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Empresa*/Bundle.getStringApplication("rotulo_unidade_saude")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="3d992217-1da6-4d45-b69c-55da2fbf36e5" key="textField-9" positionType="Float" x="255" y="208" width="122" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Tel 1*/Bundle.getStringApplication("rotulo_cont_fone_1")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="2" y="208" width="122" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Cidade*/Bundle.getStringApplication("rotulo_cidade")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="3" y="377" width="77" height="13">
					<printWhenExpression><![CDATA[$P{peso} != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{peso}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="424" y="127" width="125" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Util.getTelefoneFormatado($P{usuarioCadsus}.getTelefoneTrabalho())]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="2" y="255" width="251" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{usuarioCadsus}.getNomePai()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="255" y="240" width="122" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Mae*/Bundle.getStringApplication("rotulo_filiacao_mae")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="345" y="48" width="196" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Escolaridade*/Bundle.getStringApplication("rotulo_escolaridade")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="93" y="48" width="50" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Idade*/Bundle.getStringApplication("rotulo_idade")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="198" y="362" width="93" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Altura*/Bundle.getStringApplication("rotulo_altura_cm")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="410" y="223" width="138" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Util.getTelefoneFormatado($P{usuarioCadsus}.getTelefone2())]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="255" y="191" width="286" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{empresa}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="198" y="377" width="90" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{altura}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="3d1d50f2-b716-4a63-a05d-f0fcc1dbf74f" key="textField-9" positionType="Float" x="2" y="80" width="173" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Cpf*/Bundle.getStringApplication("rotulo_cpf")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="1" y="345" width="122" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["2. "+Bundle.getStringApplication("rotulo_exame_fisico")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="2" y="223" width="251" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{cidade}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="255" y="493" width="295" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Quanto Tempo*/Bundle.getStringApplication("rotulo_quanto_tempo")+": "+
($P{fichaAcolhimento}.getTempoFrequentoOutrosServicos() != null ?  $P{fichaAcolhimento}.getTempoFrequentoOutrosServicos(): "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="2" y="319" width="251" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{usuarioCadsus}.getUrgenciaChamar()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="2" y="95" width="174" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{usuarioCadsus}.getCpfFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="2" y="287" width="294" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{usuarioCadsus}.getResponsavel()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="467d840d-73e0-467a-a289-16fad794eef7" key="textField-9" positionType="Float" x="307" y="127" width="116" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{usuarioCadsus}.getLocalTrabalho()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="2" y="553" width="548" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Outros Dados*/Bundle.getStringApplication("rotulo_outros_dados")+": "+
($P{fichaAcolhimento}.getOutrosDadosHistoricoSaude() != null ? $P{fichaAcolhimento}.getOutrosDadosHistoricoSaude() : "")]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
