/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.agendamento.tfd.requisicaoenvioexame;

import br.com.ksisolucoes.bo.agendamento.tfd.requisicaoenvioexame.QueryRelacaoExamesEnviados;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.agendamento.tfd.requisicaoenvioexame.dto.RelacaoExamesEnviadosDTOParam;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelacaoExamesEnviados extends AbstractReport<RelacaoExamesEnviadosDTOParam> {

    public RelacaoExamesEnviados(RelacaoExamesEnviadosDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/agendamento/tfd/requisicaoenvioexame/jrxml/relacao_exames_enviados.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_exames_enviados");
    }

    @Override
    public ITransferDataReport getQuery() {
        this.addParametro("formaApresentacao", this.getParam().getFormaApresentacao());
        return new QueryRelacaoExamesEnviados();
    }

}
