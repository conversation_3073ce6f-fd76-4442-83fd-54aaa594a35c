<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd"
              name="report name" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802"
              leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20"
              uuid="cf9c5a97-e039-436f-9356-437ba800ba38">
    <property name="ireport.zoom" value="3.0"/>
    <property name="ireport.x" value="559"/>
    <property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.QueryRelatorioPedidosDivergenciaEntregaDTOParam"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="formaApresentacao" class="br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.QueryRelatorioPedidosDivergenciaEntregaDTOParam.FormaApresentacao"/>
	<field name="unidadeOrigem" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="unidadeDestino" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="pedido" class="java.lang.Long"/>
    <field name="vacina" class="br.com.ksisolucoes.vo.vacina.TipoVacina"/>
	<field name="dataPedido" class="java.util.Date"/>
	<field name="dataRecebimento" class="java.util.Date"/>
	<field name="produto" class="br.com.ksisolucoes.vo.entradas.estoque.Produto"/>
	<field name="deposito" class="br.com.ksisolucoes.vo.entradas.estoque.Deposito"/>
	<field name="quantidadeSolicitada" class="java.lang.Double"/>
	<field name="quantidadeEnviada" class="java.lang.Double"/>
	<field name="quantidadeRecebida" class="java.lang.Double"/>
	<group name="formaApresentacao">
		<groupExpression><![CDATA[QueryRelatorioPedidosDivergenciaEntregaDTOParam.FormaApresentacao.UNIDADE_ORIGEM.equals($P{formaApresentacao})
?
    $F{unidadeOrigem}
:
    QueryRelatorioPedidosDivergenciaEntregaDTOParam.FormaApresentacao.UNIDADE_DESTINO.equals($P{formaApresentacao})
    ?
        $F{unidadeDestino}
    :
        null]]></groupExpression>
		<groupHeader>
			<band height="25">
				<textField>
                    <reportElement x="0" y="0" width="555" height="13" uuid="c4323804-50b8-4557-bec1-0ba4913d5d2e"/>
					<textElement>
						<font fontName="Arial" isBold="true" isUnderline="true"/>
					</textElement>
                    <textFieldExpression><![CDATA[QueryRelatorioPedidosDivergenciaEntregaDTOParam.FormaApresentacao.UNIDADE_ORIGEM.equals($P{formaApresentacao})
?
    Bundle.getStringApplication("rotulo_unidade_origem")+": "+$F{unidadeOrigem}.getDescricaoFormatado()
:
    QueryRelatorioPedidosDivergenciaEntregaDTOParam.FormaApresentacao.UNIDADE_DESTINO.equals($P{formaApresentacao})
    ?
        Bundle.getStringApplication("rotulo_unidade_destino")+": "+$F{unidadeDestino}.getDescricaoFormatado()
    :
        null]]></textFieldExpression>
				</textField>
				<textField>
                    <reportElement x="148" y="14" width="156" height="10" uuid="ea55e719-3eda-4c82-b9ca-5feff4ed88db"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
                    <textFieldExpression>
                        <![CDATA[Bundle.getStringApplication("rotulo_unidade_destino")]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00;-#,##0.00">
                    <reportElement x="667" y="14" width="45" height="10" uuid="5e80b6c3-9527-4d77-baaa-fa6e963315b9"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
                    <textFieldExpression>
                        <![CDATA[Bundle.getStringApplication("rotulo_quantidade_enviada_abv")]]></textFieldExpression>
				</textField>
				<textField>
                    <reportElement x="0" y="14" width="46" height="10" uuid="9904d52a-5f5b-4d57-b318-9217f643afb5"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
                    <textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_pedido")]]></textFieldExpression>
				</textField>
				<textField>
                    <reportElement x="522" y="14" width="100" height="10" uuid="b9c17d6d-d364-41f3-955e-6c57a6ed3e9d"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
                    <textFieldExpression>
                        <![CDATA[Bundle.getStringApplication("rotulo_deposito")]]></textFieldExpression>
				</textField>
				<textField>
                    <reportElement x="46" y="14" width="51" height="10" uuid="a13ca1b8-2a7c-407f-a7d0-e4eb21359cbd"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
                    <textFieldExpression>
                        <![CDATA[Bundle.getStringApplication("rotulo_data_pedido_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00;-#,##0.00">
                    <reportElement x="712" y="14" width="45" height="10" uuid="d2a999e4-4a3f-4113-9ae4-1257ff0f29cb"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
                    <textFieldExpression>
                        <![CDATA[Bundle.getStringApplication("rotulo_quantidade_recebida_abv")]]></textFieldExpression>
				</textField>
				<textField>
                    <reportElement x="304" y="14" width="192" height="10" uuid="21e8f20f-23ba-4625-9d1d-3bd92e1434d1"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
                    <textFieldExpression>
                        <![CDATA[Bundle.getStringApplication("rotulo_produto_vacina")]]></textFieldExpression>
				</textField>
				<textField>
                    <reportElement x="496" y="14" width="26" height="10" uuid="4bf1b038-32ae-4db7-8dae-f1fde92b102e"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
                    <textFieldExpression>
                        <![CDATA[Bundle.getStringApplication("rotulo_unidade_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00;-#,##0.00">
                    <reportElement x="622" y="14" width="45" height="10" uuid="68e255d7-22b9-471b-877d-b5d0b2132b7e"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
                    <textFieldExpression>
                        <![CDATA[Bundle.getStringApplication("rotulo_quantidade_solicitada_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00;-#,##0.00">
                    <reportElement x="757" y="14" width="45" height="10" uuid="37a0024a-fb9f-485b-bded-0bf8f0f2c02d"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
                    <textFieldExpression>
                        <![CDATA[Bundle.getStringApplication("rotulo_diferenca")]]></textFieldExpression>
				</textField>
				<textField>
                    <reportElement x="97" y="14" width="51" height="10" uuid="5765ae8b-ac4a-4d71-9063-feb7f562484c"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
                    <textFieldExpression>
                        <![CDATA[Bundle.getStringApplication("rotulo_data_recebimento_abv")]]></textFieldExpression>
				</textField>
				<line>
                    <reportElement x="0" y="24" width="802" height="1" uuid="ff40d5f7-f19a-4c67-8880-4abcc5a1719e"/>
				</line>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<detail>
		<band height="10" splitType="Stretch">
			<textField>
                <reportElement x="0" y="0" width="46" height="10" uuid="50020662-cafa-4c17-bbf6-298bdde45e92"/>
				<textElement>
					<font fontName="Arial" size="7"/>
				</textElement>
                <textFieldExpression><![CDATA[$F{pedido}]]></textFieldExpression>
			</textField>
			<textField>
                <reportElement x="46" y="0" width="51" height="10" uuid="072a3887-bcba-4a42-87e2-6452f80c4b59"/>
				<textElement>
					<font fontName="Arial" size="7"/>
				</textElement>
                <textFieldExpression><![CDATA[Data.formatar($F{dataPedido})]]></textFieldExpression>
			</textField>
			<textField>
                <reportElement x="97" y="0" width="51" height="10" uuid="e56c8b11-9827-47ac-80a9-7968f149bcdd"/>
				<textElement>
					<font fontName="Arial" size="7"/>
				</textElement>
                <textFieldExpression><![CDATA[Data.formatar($F{dataRecebimento})]]></textFieldExpression>
			</textField>
			<textField>
                <reportElement x="148" y="0" width="156" height="10" uuid="8dd3b81d-033e-4df5-bce6-c723c5068fdd"/>
				<textElement>
					<font fontName="Arial" size="7"/>
				</textElement>
                <textFieldExpression><![CDATA[$F{unidadeDestino}.getDescricaoFormatado()]]></textFieldExpression>
			</textField>
			<textField>
                <reportElement x="304" y="0" width="192" height="10" uuid="02daeebb-b7b5-4034-94f1-7ca1aafd6359"/>
				<textElement>
					<font fontName="Arial" size="7"/>
				</textElement>
                <textFieldExpression>
                    <![CDATA[( $F{produto}==null ? $F{vacina}.getDescricao():$F{produto}.getDescricaoFormatado())]]></textFieldExpression>
			</textField>
			<textField>
                <reportElement x="496" y="0" width="26" height="10" uuid="381d2931-e586-4376-ae13-6851c492f0a9">
                    <printWhenExpression><![CDATA[$F{produto}!=null]]></printWhenExpression>
                </reportElement>
				<textElement>
					<font fontName="Arial" size="7"/>
				</textElement>
                <textFieldExpression><![CDATA[$F{produto}.getUnidade().getUnidade()]]></textFieldExpression>
			</textField>
			<textField>
                <reportElement x="522" y="0" width="100" height="10" uuid="dce41577-ed5c-44e9-a628-3ee3f60d07f4"/>
				<textElement>
					<font fontName="Arial" size="7"/>
				</textElement>
                <textFieldExpression><![CDATA[$F{deposito}.getDescricaoFormatado()]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00">
                <reportElement x="622" y="0" width="45" height="10" uuid="ae5a80de-9478-4ef6-a850-db7bda0817a7"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="7"/>
				</textElement>
                <textFieldExpression><![CDATA[$F{quantidadeSolicitada}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00">
                <reportElement x="667" y="0" width="45" height="10" uuid="49580c34-5f2f-4de3-b0c2-bcf676ee02c2"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="7"/>
				</textElement>
                <textFieldExpression><![CDATA[$F{quantidadeEnviada}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00">
                <reportElement x="712" y="0" width="45" height="10" uuid="f9fc7a7c-990e-46fd-bb88-eee8b8e6dc27"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="7"/>
				</textElement>
                <textFieldExpression><![CDATA[$F{quantidadeRecebida}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00">
                <reportElement x="757" y="0" width="45" height="10" uuid="8334a540-31a6-42d9-958d-1c0e2dec6032"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="7"/>
				</textElement>
                <textFieldExpression><![CDATA[$F{quantidadeEnviada} - $F{quantidadeRecebida}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
