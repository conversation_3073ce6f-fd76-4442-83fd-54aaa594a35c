/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.vigilancia;

import br.com.celk.report.vigilancia.query.QueryFichaSars;
import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FichaInvestigacaoAgravoDTOParam;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;
import java.util.LinkedHashMap;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoFichaInvestigacaoAgravoSars extends AbstractReport<FichaInvestigacaoAgravoDTOParam> {

    private QueryFichaSars query;
    
    public ImpressaoFichaInvestigacaoAgravoSars(FichaInvestigacaoAgravoDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        if(query == null){
            query = new QueryFichaSars();
        }
        return query;
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> columnsMap = new LinkedHashMap<>();
        
        columnsMap.put("01 Data de preenchimento ficha", "_03_data_notificacao");
        columnsMap.put("02 Data dos sintomas", "_data_primeiros_sintomas");
        columnsMap.put("03 UF", "_04_uf_notificacao");
        columnsMap.put("04 IBGE Cidade Notificacao", "_05_codigo_ibge");
        columnsMap.put("04 Cidade Notificacao", "_05_municipio_notificacao");
        columnsMap.put("05 Unidade Notificacao", "_06_unidade_saude_notificacao");
        columnsMap.put("05 CNES Unidade Notificacao", "_06_codigo");
        columnsMap.put("07 CPF", "_cpf");
        columnsMap.put("09 CNS", "_15_numero_cartao_sus");
        columnsMap.put("10 Nome", "_08_nome");
        columnsMap.put("11 Sexo", "_11_sexo");
        columnsMap.put("12 Data Nascimento", "_09_dt_nascimento");
        columnsMap.put("14 Gestante", "_12_gestante");
        columnsMap.put("15 Raca", "_13_raca_cor");
        
        columnsMap.put("22 CEP", "_27_cep");
        columnsMap.put("23 UF", "_17_uf");
        columnsMap.put("24 Municipio Residencia", "_18_municipio");
        columnsMap.put("25 Bairro Residencia", "_20_bairro");
        columnsMap.put("26 Logradouro Residencia", "_21_logradouro");
        columnsMap.put("27 Numero Logradouro", "_22_numero");
        columnsMap.put("28 Complemento", "_23_complemento");
        columnsMap.put("29 Telefone", "_28_telefone");
        columnsMap.put("30 Zona", "_29_zona");
        columnsMap.put("31 Pais", "_30_pais");
        
        columnsMap.put("32 Caso nosocomial", "_32_caso_nosocomial");
        columnsMap.put("33 Contato direto animal", "_33_paciente_trabalha_contato_direto");
        columnsMap.put("33 Outro contato direto animal", "_33_outro_paciente_trabalha_contato_direto");
        
        columnsMap.put("34 Febre", "_34_presenca_sinais_febre");
        columnsMap.put("34 Tosse", "_34_presenca_sinais_sintomas_tosse");
        columnsMap.put("34 Dor de garganta", "_34_presenca_sinais_sintomas_dor_garganta");
        columnsMap.put("34 Dispneia", "_34_presenca_sinais_sintomas_dispneia");
        columnsMap.put("34 Desconforto respiratorio", "_34_presenca_sinais_sintomas_desconforto");
        columnsMap.put("34 Saturacao O2 baixa", "_34_presenca_sinais_sintomas_saturacao");
        columnsMap.put("34 Diarreia", "_34_presenca_sinais_sintomas_diarreia");
        columnsMap.put("34 Vomito", "_34_presenca_sinais_sintomas_vomito");
        columnsMap.put("34 Dor abdominal", "_34_presenca_sinais_sintomas_dor_abdominal");
        columnsMap.put("34 Fadiga", "_34_presenca_sinais_sintomas_fadiga");
        columnsMap.put("34 Perda de olfato", "_34_presenca_sinais_sintomas_perda_olfato");
        columnsMap.put("34 Perda do paladar", "_34_presenca_sinais_sintomas_perda_paladar");
        columnsMap.put("34 Outro", "_34_presenca_sinais_sintomas_outro");
        
        columnsMap.put("35 Possui fator de risco", "_35_possui_fatores_risco_comorbidades");
        columnsMap.put("35 Puerpera", "_35_possui_fatores_risco_puerpera");
        columnsMap.put("35 Sindrome de down", "_35_possui_fatores_risco_sindrome_down");
        columnsMap.put("35 Diabetes mellitus", "_35_possui_fatores_risco_diabetes");
        columnsMap.put("35 Imunodeficiencia", "_35_possui_fatores_risco_imunodeficiencia");
        columnsMap.put("35 Doenca cardiovascular", "_35_possui_fatores_risco_doenca_cardiovascular");
        columnsMap.put("35 Doenca hepatica", "_35_possui_fatores_risco_doenca_hepatite");
        columnsMap.put("35 Doenca Neurologica", "_35_possui_fatores_risco_doenca_neurologica");
        columnsMap.put("35 Doenca Renal", "_35_possui_fatores_risco_doenca_renal");
        columnsMap.put("35 Doenca Hematologica", "_35_possui_fatores_risco_doenca_hermatologica");
        columnsMap.put("35 Asma", "_35_possui_fatores_risco_asma");
        columnsMap.put("35 Outra Pneumopatia", "_35_possui_fatores_risco_outra_pneumopatia");
        columnsMap.put("35 Obesidade", "_35_possui_fatores_risco_obesidade");
        columnsMap.put("35 Outro fator de risco", "_35_possui_fatores_risco_outro");
        
        columnsMap.put("36 Recebeu Vacina COVID19", "_36_recebeu_vacina_covid19");
        columnsMap.put("37 Primeira dose vacina", "_37_data_dose1_vacina_covid19");
        columnsMap.put("37 Primeira segunda dose vacina", "_37_data_dose2_vacina_covid19");
        columnsMap.put("38 Fabricante vacina", "_38_fabricante_vacina_covid19");
        columnsMap.put("39 Lote primeira dose", "_39_lote1_vacina_covid19");
        columnsMap.put("39 Lote segunda dose", "_39_lote2_vacina_covid19");
        columnsMap.put("40 Recebeu vacina gripe", "_40_recebeu_vacina_gripe_ultima_campanha");
        columnsMap.put("41 Data vacina gripe", "_41_data_gripe_ultima_campanha");
        
        columnsMap.put("Menor 6 meses mae recebeu vacina", "_41_se_menor_6meses_mae_recebeu_vacina");
        columnsMap.put("Menor 6 meses Data mae recebeu vacina", "_41_data_se_sim_mae_menor_6meses_recebeu_vacina");
        columnsMap.put("Menor 6 meses mae amamenta", "_41_se_menor_6meses_mae_amamenta_crianca");
        columnsMap.put("Entre 6 meses e 8 anos Data dose unica", "_41_data_se_maior_igual_6meses_menor_igual_8meses");
        columnsMap.put("Entre 6 meses e 8 anos Data primeira dose", "_41_data_se_maior_igual_6meses_menor_igual_8meses_dose1");
        columnsMap.put("Entre 6 meses e 8 anos Data segunda dose", "_41_data_se_maior_igual_6meses_menor_igual_8meses_dose2");
        
        columnsMap.put("42 Usou antiviral pra gripe", "_42_uso_antiviral_gripe");
        columnsMap.put("43 Qual antiviral", "_43_qual_antiviral_gripe");
        columnsMap.put("43 Outro antiviral", "_43_outro_antiviral_gripe");
        columnsMap.put("44 Data Inicio Tratamento", "_44_data_inicio_tratamento");
        columnsMap.put("45 Houve Internacao", "_45_houve_internacao");
        columnsMap.put("46 Data Internacao", "_46_data_internacao_srag");
        
        columnsMap.put("48 IBGE Cidade Internacao", "_48_municipio_internecao_codigo");
        columnsMap.put("48 Cidade Internacao", "_48_municipio_internecao");
        columnsMap.put("49 Unidade Internacao", "_49_unidade_saude_internecao");
        columnsMap.put("49 CNES Unidade Internacao", "_49_unidade_saude_internecao_cnes");
        columnsMap.put("50 Internado em UTI", "_50_internacao_uti");
        columnsMap.put("51 Data entrada na UTI", "_46_data_entrada_uti");
        columnsMap.put("52 Data saide na UTI", "_47_data_saida_uti");
        columnsMap.put("53 Uso de suporte ventilatorio", "_53_uso_suporte_ventilatorio");
        columnsMap.put("54 Raio X de torax", "_54_raiox_torax");
        columnsMap.put("54 Outro Raio X", "_54_outro_raiox_torax");
        columnsMap.put("55 Data Raio X", "_55_data_raiox_torax");
        columnsMap.put("56 Aspecto Tomografria", "_56_aspecto_tomografia");
        columnsMap.put("56 Outro Aspecto Tomografria", "_56_outro_aspecto_tomografia");
        columnsMap.put("57 Data tomografia", "_57_data_tomografia");
        columnsMap.put("58 Coletou amostra", "_58_coletou_amostra");
        columnsMap.put("59 Data Coletou amostra", "_59_data_coletou_amostra");
        columnsMap.put("60 Tipo de Amostra", "_60_tipo_amostra");
        columnsMap.put("60 Outro Tipo de Amostra", "_60_outro_tipo_amostra");
        columnsMap.put("61 Requisicao GAL", "_61_numero_requisicao_gal");
        columnsMap.put("62 Tipo teste antigeno", "_62_tipo_teste_antigenos_virais");
        columnsMap.put("63 Data resultado", "_63_data_resultado_antigenos_virais");
        columnsMap.put("64 Resultado", "_64_resultado_teste_antigenico");
        columnsMap.put("65 Laboratorio", "_65_laboratorio_teste_antigenico");
        columnsMap.put("65 CNES Laboratorio", "_65_laboratorio_teste_antigenico_cnes");
        
        columnsMap.put("66 Positivo influenza", "_66_agente_etiologico_ta_pos_influenza");
        columnsMap.put("66 Qual influenza", "_66_se_sim_qual_influenza");
        columnsMap.put("66 Positivo outro virus", "_66_positivo_outro_virus_ta");
        columnsMap.put("66 Outro SARS-CoV-2", "_66_outro_virus_sars_cov2_ta");
        columnsMap.put("66 Outro Virus Sincicinal Respiratorio", "_66_outro_virus_sincicial_ta");
        columnsMap.put("66 Outro Parainfluenza 1", "_66_outro_virus_influenza1_ta");
        columnsMap.put("66 Outro Parainfluenza 2", "_66_outro_virus_influenza2_ta");
        columnsMap.put("66 Outro Parainfluenza 3", "_66_outro_virus_influenza3_ta");
        columnsMap.put("66 Outro Adenovirus", "_66_outro_virus_adenovirus_ta");
        columnsMap.put("66 Outro Virus respiratorio descricao", "_66_outro_virus_respiratorio_ta");
        
        columnsMap.put("67 Resutado RT-PCR", "_67_resultado_metodo_biologia_molecular");
        columnsMap.put("68 Data Resutado RT-PCR", "_68_data_resultado_metodo_biologia_molecular");
        
        columnsMap.put("69 Positivo Influenza", "_69_agente_etiologico_bm_positivo_influenza");
        columnsMap.put("69 Qual Influenza", "_69_se_sim_qual_influenza_bm");
        columnsMap.put("69 Influenza A subtipo", "_69_influenza_a_qual_subtipo_bm");
        columnsMap.put("69 Influenza A outro subtipo", "_69_outra_influenza_a_qual_subtipo_bm");
        columnsMap.put("69 Influenza B linhagem", "_69_influenza_b_qual_linhagem_bm");
        columnsMap.put("69 Influenza B outra linhagem", "_69_outra_influenza_b_qual_linhagem_bm");
        columnsMap.put("69 Positivo outro virus", "_69_positivo_outro_virus_bm");
        columnsMap.put("69 SARS-CoV-2", "_69_outro_virus_sars_cov2_bm");
        columnsMap.put("69 Virus Sincicial", "_69_outro_virus_sincicial_bm");
        columnsMap.put("69 Parainfluenza 1", "_69_outro_virus_influenza1_bm");
        columnsMap.put("69 Parainfluenza 2", "_69_outro_virus_influenza2_bm");
        columnsMap.put("69 Parainfluenza 3", "_69_outro_virus_influenza3_bm");
        columnsMap.put("69 Parainfluenza 4", "_69_outro_virus_influenza4_bm");
        columnsMap.put("69 Adenvirus", "_69_outro_virus_adenovirus_bm");
        columnsMap.put("69 Metapneumovirus", "_69_outro_virus_metapneumovirus_bm");
        columnsMap.put("69 Bocavirus", "_69_outro_virus_bocavirus_bm");
        columnsMap.put("69 Rinovirus", "_69_outro_virus_rinovirus_bm");
        columnsMap.put("69 Outro virus respiratorio descricao", "_69_outro_virus_respiratorio_bm");
        
        columnsMap.put("70 Laboratorio RT-PCR", "_70_laboratorio_que_realizaou_teste_pcr");
        columnsMap.put("70 CNES Laboratorio RT-PCR", "_70_laboratorio_que_realizaou_teste_pcr_cnes");
        columnsMap.put("71 Tipo Amostra Sars-Cov-2", "_71_tipo_amostra_sorologica_sars_cov2");
        columnsMap.put("71 Outra amostra", "_71_outro_tipo_amostra_sorologica_sars_cov2");
        columnsMap.put("72 Data coleta", "_72_data_tipo_amostra_sorologica_sars_cov2");
        
        columnsMap.put("73 Tipo Sorologia Sars-Cov-2", "_73_tipo_sorologica_sars_cov2");
        columnsMap.put("73 Outro Tipo Sorologia Sars-Cov-2", "_73_outro_tipo_sorologica_sars_cov2");
        columnsMap.put("73 Resultado Sorologia Sars-Cov-2 IGG", "_73_resultado_lgg");
        columnsMap.put("73 Resultado Sorologia Sars-Cov-2 IGM", "_73_resultado_lgm");
        columnsMap.put("73 Resultado Sorologia Sars-Cov-2 IGA", "_73_resultado_lga");
        
        columnsMap.put("74 Data resultado", "_74_data_tipo_sorologica_sars_cov2");
        columnsMap.put("75 Classificacao final caso", "_75_classificacao_final_caso");
        columnsMap.put("75 Classificacao final caso outro", "_75_outro_classificacao_final_caso");
        columnsMap.put("76 Criterio de encerramento", "_76_criterio_encerramento");
        columnsMap.put("77 Evolucao do caso", "_77_evolucao_caso");
        columnsMap.put("78 Data da alta", "_78_data_alta_obito");
        columnsMap.put("79 Data do encerramento", "_79_data_encerramento");
        columnsMap.put("80 Numero DO", "_80_numero_do");
        columnsMap.put("81 Observacao", "_81_observacoes_adicionais");
        columnsMap.put("82 Responsavel", "_82_profissional_responsavel");
        columnsMap.put("83 Numero de Registro no conselho", "_82_profissional_responsavel_registro");
        
        return columnsMap;
    }
    
    @Override
    public String getXML() {
        return "/br/com/celk/report/vigilancia/pdf/ficha_investigacao_sars.pdf";
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return param.getTipoArquivo();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_sars");
    }

}
