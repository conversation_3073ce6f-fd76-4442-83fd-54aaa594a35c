<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_boletim_procedimento" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="782" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="077d1719-c8af-483b-9658-bd31fb79fbc4">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.357947691000002"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<style name="Crosstab Data Text" hAlign="Center"/>
	<parameter name="FORMA_APRESENTACAO" class="java.lang.String"/>
	<parameter name="AGRUPAR" class="java.lang.String" isForPrompting="false"/>
	<field name="descricaoFormatado" class="java.lang.String"/>
	<field name="quantidade" class="java.lang.Long"/>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="tabelaCbo" class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"/>
	<field name="procedimento" class="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"/>
	<field name="codigoProcedimento" class="java.lang.String"/>
	<field name="data" class="java.util.Date"/>
	<field name="convenio" class="br.com.ksisolucoes.vo.prontuario.basico.Convenio"/>
	<variable name="formaApresentacao" class="java.lang.String">
		<variableExpression><![CDATA[$P{FORMA_APRESENTACAO}.equals(Bundle.getStringApplication("rotulo_profissional"))
?
    $F{profissional}.getDescricaoFormatado()
:
    $P{FORMA_APRESENTACAO}.equals(Bundle.getStringApplication("rotulo_usuario_cadsus"))
    ?
        $F{usuarioCadsus}.getDescricaoFormatado()
    :
        $P{FORMA_APRESENTACAO}.equals(Bundle.getStringApplication("rotulo_cbo"))
        ?
            $F{tabelaCbo}.getCbo()!=null
            ?
                $F{tabelaCbo}.getDescricaoFormatado()
            :
                Bundle.getStringApplication("rotulo_sem_cadastro")
        :
            ""]]></variableExpression>
	</variable>
	<group name="crosstab">
		<groupHeader>
			<band/>
		</groupHeader>
		<groupFooter>
			<band height="72">
				<crosstab>
					<reportElement key="crosstab" x="0" y="0" width="782" height="72" uuid="a3f5ea5d-a0d4-4414-ab86-ed651b6d95a9"/>
					<crosstabParameter name="formaApresentacao">
						<parameterValueExpression><![CDATA[$P{FORMA_APRESENTACAO}]]></parameterValueExpression>
					</crosstabParameter>
					<crosstabParameter name="agrupar">
						<parameterValueExpression><![CDATA[$P{AGRUPAR}]]></parameterValueExpression>
					</crosstabParameter>
					<crosstabHeaderCell>
						<cellContents mode="Transparent">
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField isBlankWhenNull="false">
								<reportElement key="textField-1" x="65" y="0" width="153" height="13" uuid="4974942a-5510-4fbe-ade6-64451f1b10f3"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Left" verticalAlignment="Middle">
									<font fontName="Arial" size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_procedimento")]]></textFieldExpression>
							</textField>
							<textField isBlankWhenNull="false">
								<reportElement key="textField-1" x="0" y="0" width="65" height="13" uuid="01bddf6f-5f59-48f7-9c07-b5aecc60992b"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Arial" size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_codigo")]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabHeaderCell>
					<rowGroup name="codigoProcedimento" width="65" totalPosition="End">
						<bucket class="java.lang.String">
							<bucketExpression><![CDATA[$F{codigoProcedimento}]]></bucketExpression>
						</bucket>
						<crosstabRowHeader>
							<cellContents backcolor="#FFFFFF" mode="Opaque">
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textField isBlankWhenNull="false">
									<reportElement key="textField" style="Crosstab Data Text" x="0" y="0" width="65" height="13" uuid="0de336f8-b478-4d63-a062-804059ccab2c"/>
									<box>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
									<textElement verticalAlignment="Middle">
										<font fontName="Arial" size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$V{codigoProcedimento}]]></textFieldExpression>
								</textField>
							</cellContents>
						</crosstabRowHeader>
						<crosstabTotalRowHeader>
							<cellContents backcolor="#FFFFFF" mode="Opaque">
								<box>
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textField isBlankWhenNull="false">
									<reportElement key="textField-1" x="0" y="3" width="203" height="13" uuid="480e8c1e-eaff-4ba4-acd6-4be4b7de64a4"/>
									<box>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
									<textElement textAlignment="Right" verticalAlignment="Middle">
										<font fontName="Arial" size="7" isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")]]></textFieldExpression>
								</textField>
							</cellContents>
						</crosstabTotalRowHeader>
					</rowGroup>
					<rowGroup name="descricaoFormatado" width="154" totalPosition="End">
						<bucket class="java.lang.String">
							<bucketExpression><![CDATA[$F{descricaoFormatado}]]></bucketExpression>
						</bucket>
						<crosstabRowHeader>
							<cellContents backcolor="#FFFFFF" mode="Opaque">
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textField isStretchWithOverflow="true" isBlankWhenNull="false">
									<reportElement key="textField" style="Crosstab Data Text" x="0" y="0" width="138" height="13" uuid="480fb3a3-30d5-43d1-8eb0-5600edb560c0"/>
									<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
									<textElement textAlignment="Left" verticalAlignment="Middle">
										<font fontName="Arial" size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$V{descricaoFormatado}]]></textFieldExpression>
								</textField>
							</cellContents>
						</crosstabRowHeader>
						<crosstabTotalRowHeader>
							<cellContents/>
						</crosstabTotalRowHeader>
					</rowGroup>
					<columnGroup name="dia" height="13" totalPosition="End" headerPosition="Center">
						<bucket class="java.util.Date">
							<bucketExpression><![CDATA[$F{data}]]></bucketExpression>
						</bucket>
						<crosstabColumnHeader>
							<cellContents backcolor="#FFFFFF" mode="Opaque">
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textField isBlankWhenNull="false">
                                    <reportElement key="textField" style="Crosstab Data Text" x="2" y="0" width="37"
                                                   height="13" uuid="ab8a3cf6-70a4-400e-9b33-************"/>
									<box>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
									<textElement verticalAlignment="Middle">
										<font fontName="Arial" size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dia").equals($P{agrupar})
?
	Data.formatarDia($V{dia})
:
	Bundle.getStringApplication("rotulo_competencia").equals($P{agrupar})
	?
		Data.formatarMes($V{dia})
	:
		null]]></textFieldExpression>
								</textField>
							</cellContents>
						</crosstabColumnHeader>
						<crosstabTotalColumnHeader>
							<cellContents backcolor="#FFFFFF" mode="Opaque">
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textField isBlankWhenNull="false">
                                    <reportElement key="textField-1" x="10" y="0" width="37" height="13"
                                                   uuid="be138d26-a881-4b3e-98c0-3ac8f3b22e95"/>
									<box>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font fontName="Arial" size="7" isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")]]></textFieldExpression>
								</textField>
							</cellContents>
						</crosstabTotalColumnHeader>
					</columnGroup>
					<measure name="quantidadeMeasure" class="java.lang.Long" calculation="Sum">
						<measureExpression><![CDATA[$F{quantidade}]]></measureExpression>
					</measure>
                    <crosstabCell width="39" height="13">
						<cellContents mode="Transparent">
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField isBlankWhenNull="false">
                                <reportElement key="textField" style="Crosstab Data Text" x="2" y="0" width="37"
                                               height="13" uuid="1edf20e6-588c-4448-858a-1e718ef50d15"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement verticalAlignment="Middle">
									<font fontName="Arial" size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{quantidadeMeasure}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabCell>
                    <crosstabCell width="39" height="19" rowTotalGroup="codigoProcedimento">
						<cellContents backcolor="#FFFFFF" mode="Opaque">
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField isBlankWhenNull="false">
                                <reportElement key="textField" style="Crosstab Data Text" x="2" y="3" width="37"
                                               height="13" forecolor="#000000"
                                               uuid="d0ceb785-f658-46a0-82c4-a829125d3bee"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement verticalAlignment="Middle">
									<font fontName="Arial" size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{quantidadeMeasure}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabCell>
                    <crosstabCell width="47" height="13" columnTotalGroup="dia">
						<cellContents backcolor="#FFFFFF" mode="Opaque">
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField isBlankWhenNull="false">
                                <reportElement key="textField" style="Crosstab Data Text" x="10" y="0" width="37"
                                               height="13" uuid="44010a93-1108-4ea3-b154-f620a98defa1"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement verticalAlignment="Middle">
									<font fontName="Arial" size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{quantidadeMeasure}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabCell>
                    <crosstabCell width="47" height="19" rowTotalGroup="codigoProcedimento" columnTotalGroup="dia">
						<cellContents backcolor="#FFFFFF" mode="Opaque">
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField isBlankWhenNull="false">
                                <reportElement key="textField" style="Crosstab Data Text" x="10" y="3" width="37"
                                               height="13" uuid="b8a84120-afab-43f5-bd58-df699af660b9"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement verticalAlignment="Middle">
									<font fontName="Arial" size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{quantidadeMeasure}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabCell>
                    <crosstabCell width="39" height="1" rowTotalGroup="descricaoFormatado">
						<cellContents backcolor="#FFFFFF" mode="Opaque">
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</cellContents>
					</crosstabCell>
                    <crosstabCell width="47" height="1" rowTotalGroup="descricaoFormatado" columnTotalGroup="dia">
						<cellContents backcolor="#FFFFFF" mode="Opaque">
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</cellContents>
					</crosstabCell>
					<whenNoDataCell>
						<cellContents mode="Transparent">
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</cellContents>
					</whenNoDataCell>
				</crosstab>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="15" splitType="Stretch">
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="220" y="0" width="127" height="15" uuid="229d2e33-945f-4f75-81f5-ca77f6b4c5c8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$P{AGRUPAR}.equals(Bundle.getStringApplication("rotulo_dia"))
?
	Bundle.getStringApplication("rotulo_dias_uteis")
:
	Bundle.getStringApplication("rotulo_competencia")]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band splitType="Stretch"/>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
