package br.com.ksisolucoes.report.hospital.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.AliasToBeanNestedResultTransformer;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioFPOPrestadorServicoDTO;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioFPOPrestadorServicoDTOParam;
import br.com.ksisolucoes.report.hospital.interfaces.dto.RelatorioFilaAihDTO;
import br.com.ksisolucoes.report.hospital.interfaces.dto.RelatorioFilaAihDTOParam;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.type.DateType;
import org.hibernate.type.DoubleType;
import org.hibernate.type.LongType;
import org.hibernate.type.StringType;

import java.util.List;
import java.util.Map;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR> Ferrari
 */
public class QueryRelatorioFilaAih extends CommandQuery<QueryRelatorioFilaAih> implements ITransferDataReport<RelatorioFilaAihDTOParam, RelatorioFilaAihDTO> {

    private RelatorioFilaAihDTOParam param;
    private List<RelatorioFilaAihDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(RelatorioFilaAihDTO.class.getName());

        hql.setUseSQL(true);

        hql.addToSelect("a.cd_aut_intern_hosp", "aih.codigo");
        hql.addToSelect("uc.nm_usuario", "usuarioCadsus.nome" );
        hql.addToSelect("max(p.ds_procedimento)", "procedimento.descricao");
        hql.addToSelect("max(cr.descricao)","classificacaoRisco.descricao");
        hql.addToSelect("max(e.descricao)", "aih.empresa.descricao");
        hql.addToSelect("max(a.estabelecimento_autorizado)", "empresa.descricao");
        hql.addToSelect("a.nm_profissional_solicitante", "aih.nomeProfissionalSolicitante");
        hql.addToSelect("a.dt_cadastro","aih.dataCadastro");
        hql.addToSelect("a.cd_carater_internacao", "aih.caraterInternacao");
        hql.addToSelect("a.status","aih.status");
        hql.addToFrom("Aih a " +
                "left join usuario_cadsus uc on a.cd_usu_cadsus = uc.cd_usu_cadsus "+
                "left join procedimento p on a.cd_procedimento_solicitado = p.cd_procedimento " +
                "left join classificacao_risco cr on a.classificacao_risco = cr.cd_classificacao_risco " +
                "left join empresa e on a.empresa = e.empresa  " +
                "left join empresa e2 on a.estabelecimento_autorizado = e2.empresa  "

        );

        hql.addToWhereWhithAnd("a.empresa  ", param.getUnidadeSolicitante());

        if ((param.getProcedimento() != null) && (param.getProcedimento().getCodigo() != null)) {
            hql.addToWhereWhithAnd("a.cd_procedimento_solicitado = ", param.getProcedimento().getCodigo());
        }
        if (param.getSituacao() != null) {
            hql.addToWhereWhithAnd("a.status = ", param.getSituacao());
        }
        if ((param.getEstabelecimentoAutorizado() != null) && (param.getEstabelecimentoAutorizado().getCodigo() != null)) {
            hql.addToWhereWhithAnd("a.estabelecimento_autorizado = ", param.getEstabelecimentoAutorizado().getCodigo());
        }
        if (param.getClassificacaoRisco() != null) {
            hql.addToWhereWhithAnd("a.classificacao_risco = ", param.getClassificacaoRisco().value());
        }
        if (param.getCaraterInternacao() != null) {
            hql.addToWhereWhithAnd("a.cd_carater_internacao = ", param.getCaraterInternacao().longValue());
        }
        hql.addToWhereWhithAnd("a.dt_cadastro ", this.param.getPeriodo());
        if (!RelatorioFilaAihDTOParam.FormaApresentacao.GERAL.equals(this.param.getFormaApresentacao())) {
            if (RelatorioFilaAihDTOParam.FormaApresentacao.UNIDADE_SOLICITANTE.equals(this.param.getFormaApresentacao())) {
                hql.addToGroup("a.estabelecimento_autorizado");
                hql.addToOrder("a.estabelecimento_autorizado ");
            } else if (RelatorioFilaAihDTOParam.FormaApresentacao.PROCEDIMENTO.equals(this.param.getFormaApresentacao())) {
                hql.addToGroup("a.cd_procedimento_solicitado");
                hql.addToOrder("a.cd_procedimento_solicitado ");
            } else if (RelatorioFilaAihDTOParam.FormaApresentacao.ESTABELECIMENTO_AUTORIZADO.equals(this.param.getFormaApresentacao())) {
                hql.addToGroup("a.estabelecimento_autorizado");
                hql.addToOrder("a.estabelecimento_autorizado ");
            } else if (RelatorioFilaAihDTOParam.FormaApresentacao.SITUACAO.equals(this.param.getFormaApresentacao())) {
                hql.addToGroup("a.status");
                hql.addToOrder("a.status ");
            }
        }
        hql.addToGroup("a.cd_aut_intern_hosp");
        hql.addToGroup("uc.nm_usuario");
        hql.addToOrder("a.dt_cadastro ");

    }


    @Override
    protected Object executeQuery(Query query) {
        SQLQuery sql = (SQLQuery) query;
        sql.addScalar("aih_codigo", LongType.INSTANCE);
        sql.addScalar("usuarioCadsus_nome", StringType.INSTANCE);
        sql.addScalar("procedimento_descricao", StringType.INSTANCE);
        sql.addScalar("classificacaoRisco_descricao", StringType.INSTANCE);
        sql.addScalar("aih_empresa_descricao", StringType.INSTANCE);
        sql.addScalar("empresa_descricao", StringType.INSTANCE);
        sql.addScalar("aih_nomeProfissionalSolicitante", StringType.INSTANCE);
        sql.addScalar("aih_dataCadastro", DateType.INSTANCE);
        sql.addScalar("aih_caraterInternacao", LongType.INSTANCE);
        sql.addScalar("aih_status", LongType.INSTANCE);
        sql.setResultTransformer(new AliasToBeanNestedResultTransformer(RelatorioFilaAihDTO.class, super.getHQL().getPropBindingList()));

        result = sql.list();
        return result;
    }
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = (List<RelatorioFilaAihDTO>) result;

    }

    @Override
    public void setDTOParam(RelatorioFilaAihDTOParam param) {
        this.param = param;
    }

    @Override
    public List<RelatorioFilaAihDTO> getResult() {
        return this.result;
    }

}
