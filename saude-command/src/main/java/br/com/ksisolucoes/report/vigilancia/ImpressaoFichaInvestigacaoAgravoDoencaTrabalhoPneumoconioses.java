/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.vigilancia;

import br.com.celk.report.vigilancia.query.QueryFichaAcidenteTrabalhoPneumoconioses;
import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FichaInvestigacaoAgravoDTOParam;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;

import java.util.LinkedHashMap;

public class ImpressaoFichaInvestigacaoAgravoDoencaTrabalhoPneumoconioses extends AbstractReport<FichaInvestigacaoAgravoDTOParam> {

    private QueryFichaAcidenteTrabalhoPneumoconioses query;

    public ImpressaoFichaInvestigacaoAgravoDoencaTrabalhoPneumoconioses(FichaInvestigacaoAgravoDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        if(query == null){
            query = new QueryFichaAcidenteTrabalhoPneumoconioses();
        }
        return query;
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> columnsMap = new LinkedHashMap<>(((QueryFichaAcidenteTrabalhoPneumoconioses)getQuery()).getMapeamentoPlanilhaBase());

        columnsMap.put("ocupacaoCbo", "_31_ocupacao");

        columnsMap.put("situacaoMercadoTrabalho", "_32_situacao_mercado_trab");
        columnsMap.put("tempoTrabalhoOcupacao", "_33_tempo_trabalho");
        columnsMap.put("tempoTrabalhoOcupacaoUnidadeMedida", "_33_tempo_trabalho_um");

        columnsMap.put("cnpj", "_34_cnpj");
        columnsMap.put("empresaContratante", "_35_empresa");
        columnsMap.put("atividade", "_36_cnae");

        columnsMap.put("estado", "_37_uf");
        columnsMap.put("cidade", "_38_municipio");
        columnsMap.put("ibge", "_38_ibge");
        columnsMap.put("distrito", "_39_distrito");
        columnsMap.put("bairro", "_40_bairro");
        columnsMap.put("rua", "_41_endereco");
        columnsMap.put("numero", "_42_numero");
        columnsMap.put("pontoReferencia", "_43_ponto_ref");
        columnsMap.put("telefone", "_44_telefone");
        columnsMap.put("empresaTerceirizada", "_45_empresa_terceirizada");

        columnsMap.put("agravosAssociadosLimitacaoCronica", "_46_limitacao_cronica");
        columnsMap.put("agravosAssociadosTuberculose", "_46_tuberculose");
        columnsMap.put("agravosAssociadosCancer", "_46_cancer");
        columnsMap.put("agravosAssociadosArtriteReumatoide", "_46_artrite_reumatoide");
        columnsMap.put("agravosAssociadosTireoide", "_46_tireoide");
        columnsMap.put("agravosAssociadosOutros", "_46_outro_descricao");
        columnsMap.put("tempoExposicaoAgenteRisco", "_47_tempo_exposicao");
        columnsMap.put("tempoExposicaoAgenteRiscoUnidadeMedida", "_47_tempo_exposicao_um");
        columnsMap.put("regimeTratamento", "_48_regime_tratamento");

        columnsMap.put("exposicaoPoeira", "_49_exposicao_poeira");
        columnsMap.put("exposicaoPoeiraDescricao", "_50_exposicao_poeira_descricao");

        columnsMap.put("agenteExposicaoSilica", "_51_exposicao_silica");
        columnsMap.put("agenteExposicaoAsbesto", "_51_exposicao_asbesto");
        columnsMap.put("agenteExposicaoCarvaoMineral", "_51_exposicao_carvao_mineral");
        columnsMap.put("agenteExposicaoPoeiraMista", "_51_exposicao_poeira_mista");
        columnsMap.put("investigacaoAgravo.agenteExposicaoMetaisDuros", "_51_exposicao_metais_duros");
        columnsMap.put("agenteExposicaoPoeirasAbrasivos", "_51_exposicao_poeira_abrasivos");
        columnsMap.put("agenteExposicaoBerilio", "_51_exposicao_poeira_berilio");
        columnsMap.put("agenteExposicaoPoeiraOrganica", "_51_exposicao_poeira_organica");

        columnsMap.put("habitoFumar", "_52_habito_fumar");
        columnsMap.put("tempoExposicaoTabaco", "_53_tempo_tabaco");
        columnsMap.put("tempoExposicaoTabacoUnidadeMedida", "_53_tempo_tabaco_um");

        columnsMap.put("confirmacaoDiagnosticaRadiografia", "_54_radiografia");
        columnsMap.put("confirmacaoDiagnosticaBiopsia", "_54_biopsia");
        columnsMap.put("confirmacaoDiagnosticaTomografia", "_54_tomografia");
        columnsMap.put("confirmacaoDiagnosticaOutros", "_54_outros");

        columnsMap.put("diagnosticoEspecifico", "_55_cid");

        columnsMap.put("outrosTrabalhadoresMesmaDoenca", "_56_outros_trabalhadores");
        columnsMap.put("avaliacaoFuncional", "_57_avaliacao_funcional");
        columnsMap.put("resultadoAvaliacao", "_58_resultado_avaliacao");

        columnsMap.put("condutaGeralAfastamentoAgenteRisco", "_57_conduta_geral_afastamento_risco");
        columnsMap.put("condutaGeralMudancaOrganizacaoTrabalho", "_57_conduta_geral_mudanca_organizacao");
        columnsMap.put("condutaGeralProtecaoColetiva", "_57_conduta_geral_protecao_coletiva");
        columnsMap.put("condutaGeralProtecaoIndividual", "_57_conduta_geral_afastamento_local");
        columnsMap.put("condutaGeralNenhum", "_57_conduta_geral_protecao_individual");
        columnsMap.put("condutaGeralAfastamentoLocalTrabalho", "_57_conduta_geral_nenhum");
        columnsMap.put("condutaGeralOutros", "_57_conduta_geral_outros");

        columnsMap.put("evolucaoCaso", "_58_evolucao_caso");
        columnsMap.put("dataObito", "_59_data_obito");
        columnsMap.put("emitidaCat", "_60_emitida_cat");
        columnsMap.put("observacao", "_observacao");

        return columnsMap;
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/vigilancia/pdf/ficha_investigacao_acidente_trabalho_pneumoconioses.pdf";
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return param.getTipoArquivo();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_acidente_trabalho_pnuemoconioses");
    }

}
