/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.entrada.dispensacao.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioPacienteDispensacaoAtrasoDTO;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioPacienteDispensacaoAtrasoDTOParam;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioPacienteDispensacaoAtraso extends CommandQuery<QueryRelatorioPacienteDispensacaoAtraso>
        implements ITransferDataReport<RelatorioPacienteDispensacaoAtrasoDTOParam, RelatorioPacienteDispensacaoAtrasoDTO> {

    private RelatorioPacienteDispensacaoAtrasoDTOParam param;
    private List<RelatorioPacienteDispensacaoAtrasoDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("usuarioCadsus.codigo", "paciente.codigo");
        hql.addToSelect("usuarioCadsus.nome", "paciente.nome");
        hql.addToSelect("usuarioCadsus.sexo", "paciente.sexo");
        hql.addToSelect("usuarioCadsus.dataNascimento", "paciente.dataNascimento");

        hql.addToSelect("empresaOrigem.codigo", "unidadeOrigem.codigo");
        hql.addToSelect("empresaOrigem.referencia", "unidadeOrigem.referencia");
        hql.addToSelect("empresaOrigem.descricao", "unidadeOrigem.descricao");
        hql.addToSelect("empresaOrigem.sigla", "unidadeOrigem.sigla");

        hql.addToSelect("dispensacaoMedicamento.codigo", "dispensacaoMedicamentoItem.dispensacaoMedicamento.codigo");
        hql.addToSelect("dispensacaoMedicamento.dataDispensacao", "dispensacaoMedicamentoItem.dispensacaoMedicamento.dataDispensacao");

        hql.addToSelect("dispensacaoMedicamentoItem.dataProximaDispensacao", "dispensacaoMedicamentoItem.dataProximaDispensacao");
        hql.addToSelect("dispensacaoMedicamentoItem.dataValidadeReceita", "dispensacaoMedicamentoItem.dataValidadeReceita");
        hql.addToSelect("dispensacaoMedicamentoItem.dataUltimaDispensacao", "dispensacaoMedicamentoItem.dataUltimaDispensacao");
        hql.addToSelect("dispensacaoMedicamentoItem.quantidadeDispensada", "dispensacaoMedicamentoItem.quantidadeDispensada");

        hql.addToSelect("produto.codigo", "dispensacaoMedicamentoItem.produto.codigo");
        hql.addToSelect("produto.referencia", "dispensacaoMedicamentoItem.produto.referencia");
        hql.addToSelect("produto.descricao", "dispensacaoMedicamentoItem.produto.descricao");
        hql.addToSelect("unidade.unidade", "dispensacaoMedicamentoItem.produto.unidade.unidade");
        hql.addToSelect("subGrupo.id", "dispensacaoMedicamentoItem.produto.subGrupo.id");


        hql.setTypeSelect(RelatorioPacienteDispensacaoAtrasoDTO.class.getName());
        hql.addToFrom("DispensacaoMedicamentoItem dispensacaoMedicamentoItem "
                + " left join dispensacaoMedicamentoItem.dispensacaoMedicamento dispensacaoMedicamento "
                + " left join dispensacaoMedicamentoItem.produto produto"
                + " left join produto.unidade unidade "
                + " left join produto.subGrupo subGrupo "
                + " left join dispensacaoMedicamento.usuarioCadsusDestino usuarioCadsus "
                + " left join dispensacaoMedicamento.empresaOrigem empresaOrigem ");

        hql.addToWhereWhithAnd("empresaOrigem ", this.param.getEmpresas());
        hql.addToWhereWhithAnd("produto ", this.param.getProdutos());
        hql.addToWhereWhithAnd("usuarioCadsus ", this.param.getPacientes());
        
        hql.addToWhereWhithAnd("dispensacaoMedicamentoItem.dataProximaDispensacao ", this.param.getPeriodo());
        hql.addToWhereWhithAnd("dispensacaoMedicamentoItem.dataProximaDispensacao < dispensacaoMedicamentoItem.dataValidadeReceita ");
        
        hql.addToWhereWhithAnd("dispensacaoMedicamentoItem.dataValidadeReceita >= ", Data.getDataAtual());
        
        hql.addToWhereWhithAnd("dispensacaoMedicamento.receitaContinua = ", RepositoryComponentDefault.SIM);
        
        if (RelatorioPacienteDispensacaoAtrasoDTOParam.FormaApresentacao.DATA.equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("dispensacaoMedicamentoItem.dataProximaDispensacao");
        } else if (RelatorioPacienteDispensacaoAtrasoDTOParam.FormaApresentacao.PRODUTO.equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("produto.codigo");
        }

        hql.addToOrder("usuarioCadsus.codigo");
        hql.addToOrder("dispensacaoMedicamento.codigo");
        hql.addToOrder("produto.codigo");

    }

    @Override
    public void setDTOParam(RelatorioPacienteDispensacaoAtrasoDTOParam param) {
        this.param = param;
    }

    @Override
    public Collection<RelatorioPacienteDispensacaoAtrasoDTO> getResult() {
        return this.result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }
}
