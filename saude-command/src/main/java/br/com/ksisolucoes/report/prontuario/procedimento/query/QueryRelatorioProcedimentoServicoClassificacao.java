/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.prontuario.procedimento.query;

import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioProcedimentoServicoClassificacaoParam;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoServicoClassificacao;
import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioProcedimentoServicoClassificacao extends CommandQuery implements ITransferDataReport<RelatorioProcedimentoServicoClassificacaoParam, ProcedimentoServicoClassificacao>{

    private RelatorioProcedimentoServicoClassificacaoParam dTOParam;
    private List<ProcedimentoServicoClassificacao> procedimentoServicoClassificacaos;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.setTypeSelect(ProcedimentoServicoClassificacao.class.getName());

        hql.addToSelect("psc.id.procedimentoServicoCadastro.codigo",true);
        hql.addToSelect("psc.id.procedimentoServicoCadastro.descricao",true);
        hql.addToSelect("psc.id.codigo",true);
        hql.addToSelect("psc.descricao",true);
        hql.addToSelect("psc.dataCompetencia",true);

        hql.addToFrom(ProcedimentoServicoClassificacao.class.getName(),"psc");

        hql.addToWhereWhithAnd("psc.id.procedimentoServicoCadastro in",this.dTOParam.getProcedimentoServicoCadastros());

        hql.addToOrder("psc.id.procedimentoServicoCadastro.codigo");
        hql.addToOrder("psc.id.codigo");

    }

    public void setDTOParam(RelatorioProcedimentoServicoClassificacaoParam arg0) {
        this.dTOParam = arg0;
    }

    public Collection<ProcedimentoServicoClassificacao> getResult() {
        return this.procedimentoServicoClassificacaos;
    }

    protected void result(HQLHelper hql, Object result) {
        this.procedimentoServicoClassificacaos =  hql.getBeanList((List)result);
    }

}
