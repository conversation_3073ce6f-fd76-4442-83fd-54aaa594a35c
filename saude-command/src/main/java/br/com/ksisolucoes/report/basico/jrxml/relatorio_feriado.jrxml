<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_feriado" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<import value="br.com.ksisolucoes.vo.basico.Feriado"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="Ano" class="java.lang.Long"/>
	<field name="descricao" class="java.lang.String"/>
	<field name="dataFeriado" class="java.util.Date"/>
	<field name="tipoFeriado" class="java.lang.String"/>
	<field name="fixoVariavel" class="java.lang.String"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="28" splitType="Stretch">
			<rectangle radius="10">
				<reportElement x="0" y="0" width="535" height="14"/>
			</rectangle>
			<textField>
				<reportElement x="0" y="1" width="535" height="12"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_ano")+": "+$P{Ano}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="7" y="17" width="49" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_dia")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="27" width="535" height="1"/>
			</line>
			<textField isStretchWithOverflow="true">
				<reportElement x="82" y="17" width="236" height="10"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_descricao")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="335" y="17" width="89" height="10"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_tipo_feriado")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="434" y="17" width="89" height="10"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_tipo")]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="11" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement x="7" y="1" width="49" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{fixoVariavel}.equals(Feriado.TIPO_DATA_FIXO))
?
    Data.formatarDiaMes($F{dataFeriado})
:
    ($F{fixoVariavel}.equals(Feriado.TIPO_DATA_VARIAVEL))
    ?
        Data.formatar($F{dataFeriado})
    :
        ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="82" y="1" width="236" height="10"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="335" y="1" width="89" height="10"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[(Feriado.TIPO_FEDERAL.equals($F{tipoFeriado}))
?
    Bundle.getStringApplication("rotulo_federal")
:
    (Feriado.TIPO_ESTADUAL.equals($F{tipoFeriado}))
    ?
        Bundle.getStringApplication("rotulo_estadual")
    :
        (Feriado.TIPO_MUNICIPAL.equals($F{tipoFeriado}))
        ?
            Bundle.getStringApplication("rotulo_municipal")
        :
            ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="434" y="1" width="89" height="10"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{fixoVariavel}.equals(Feriado.TIPO_DATA_FIXO))
?
    Bundle.getStringApplication("rotulo_fixo")
:
    ($F{fixoVariavel}.equals(Feriado.TIPO_DATA_VARIAVEL))
    ?
        Bundle.getStringApplication("rotulo_variavel")
    :
        ""]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
