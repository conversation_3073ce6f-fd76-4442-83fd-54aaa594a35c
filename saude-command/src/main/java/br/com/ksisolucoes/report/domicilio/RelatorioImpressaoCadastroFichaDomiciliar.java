package br.com.ksisolucoes.report.domicilio;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.domicilio.query.QueryRelatorioImpressaoCadastroFichaDomiciliar;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoCadastroFichaDomiciliar extends AbstractReport<Long> {

    public RelatorioImpressaoCadastroFichaDomiciliar(Long codigoEnderecoDomicilio) {
        super(codigoEnderecoDomicilio);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/domicilio/jrxml/relatorio_impressao_cadastro_ficha_domiciliar.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_cadastro_domiciliar");
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioImpressaoCadastroFichaDomiciliar();
    }

}