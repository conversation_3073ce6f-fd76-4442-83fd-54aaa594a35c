package br.com.ksisolucoes.report.exportacao;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.exportacao.consulta.dto.RelatorioExportacaoConsultaCsvDTO;
import br.com.ksisolucoes.report.exportacao.consulta.dto.RelatorioExportacaoConsultaCsvDTOParam;
import br.com.ksisolucoes.report.exportacao.query.QueryRelatorioExportacaoConsultaCsv;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import ch.lambdaj.Lambda;

import java.util.LinkedHashMap;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioExportacaoConsultaCsv extends AbstractReport<RelatorioExportacaoConsultaCsvDTOParam> {

    private final RelatorioExportacaoConsultaCsvDTOParam param;

    public RelatorioExportacaoConsultaCsv(RelatorioExportacaoConsultaCsvDTOParam param) {
        super(param);
        this.param = param;
    }

    @Override
    public String getXML() {
        return null;
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> columns = new LinkedHashMap<>();
        RelatorioExportacaoConsultaCsvDTO proxy = Lambda.on(RelatorioExportacaoConsultaCsvDTO.class);
        List<String> campos;

        try {
            campos = param.getCamposFromSql();
        } catch (ValidacaoException e) {
            throw new RuntimeException(e);
        }

        for (String campo : campos) {
            columns.put(campo, proxy.getValores().get(campo));
        }

        return columns;
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioExportacaoConsultaCsv();
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return param.getTipoArquivo();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_exportacao_consultas");
    }

    @Override
    public Boolean isDinamico() {
        return true;
    }
}