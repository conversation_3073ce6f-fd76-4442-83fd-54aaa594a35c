/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.vigilancia.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.basico.OrgaoEmissor;
import br.com.ksisolucoes.vo.grupoestabelecimento.GrupoEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.DocumentoRequerimento;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.ResponsavelTecnico;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.VeiculoEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import java.util.List;
import java.util.Map;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioImpressaoDocumentoRequerimento extends CommandQuery<QueryRelatorioImpressaoDocumentoRequerimento> implements ITransferDataReport<RequerimentoVigilancia, DocumentoRequerimento> {

    private RequerimentoVigilancia requerimentoVigilancia;
    private List<DocumentoRequerimento> result;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.setTypeSelect(DocumentoRequerimento.class.getName());
        hql.addToSelect("dr.numero", DocumentoRequerimento.PROP_NUMERO);
        hql.addToSelect("dr.protocolo", DocumentoRequerimento.PROP_PROTOCOLO);
        hql.addToSelect("dr.especificacao", DocumentoRequerimento.PROP_ESPECIFICACAO);
        hql.addToSelect("dr.nomeEstabelecimento", DocumentoRequerimento.PROP_NOME_ESTABELECIMENTO);
        hql.addToSelect("dr.fantasia", DocumentoRequerimento.PROP_FANTASIA);
        hql.addToSelect("dr.cnpjCpf", DocumentoRequerimento.PROP_CNPJ_CPF);
        hql.addToSelect("dr.endereco", DocumentoRequerimento.PROP_ENDERECO);
        hql.addToSelect("dr.cep", DocumentoRequerimento.PROP_CEP);
        hql.addToSelect("dr.telefone", DocumentoRequerimento.PROP_TELEFONE);
        hql.addToSelect("dr.bairro", DocumentoRequerimento.PROP_BAIRRO);
        hql.addToSelect("dr.responsavel", DocumentoRequerimento.PROP_RESPONSAVEL);
        hql.addToSelect("dr.tipoVeiculo", DocumentoRequerimento.PROP_TIPO_VEICULO);
        hql.addToSelect("dr.placa", DocumentoRequerimento.PROP_PLACA);
        hql.addToSelect("dr.dataFimValidade", DocumentoRequerimento.PROP_DATA_FIM_VALIDADE);
        hql.addToSelect("dr.dataInicioValidade", DocumentoRequerimento.PROP_DATA_INICIO_VALIDADE);
        hql.addToSelect("dr.dataImpressao", DocumentoRequerimento.PROP_DATA_IMPRESSAO);
        hql.addToSelect("dr.concedidoPor", DocumentoRequerimento.PROP_CONCEDIDO_POR);
        hql.addToSelect("dr.tipoDocumento", DocumentoRequerimento.PROP_TIPO_DOCUMENTO);
        hql.addToSelect("dr.restricoes", DocumentoRequerimento.PROP_RESTRICOES);
        hql.addToSelect("dr.tituloDocumentoPersonalizado", DocumentoRequerimento.PROP_TITULO_DOCUMENTO_PERSONALIZADO);
        hql.addToSelect("dr.textoDocumentoPersonalizado", DocumentoRequerimento.PROP_TEXTO_DOCUMENTO_PERSONALIZADO);
        hql.addToSelect("dr.observacaoDestaque", DocumentoRequerimento.PROP_OBSERVACAO_DESTAQUE);
        hql.addToSelect("dr.autorizacaoFuncionamento",DocumentoRequerimento.PROP_AUTORIZACAO_FUNCIONAMENTO);
        hql.addToSelect("cid.codigo", VOUtils.montarPath(DocumentoRequerimento.PROP_CIDADE, Cidade.PROP_CODIGO));
        hql.addToSelect("cid.descricao", VOUtils.montarPath(DocumentoRequerimento.PROP_CIDADE, Cidade.PROP_DESCRICAO));
        hql.addToSelect("ae.codigo", VOUtils.montarPath(DocumentoRequerimento.PROP_ATIVIDADE_ESTABELECIMENTO, AtividadeEstabelecimento.PROP_CODIGO));
        hql.addToSelect("ae.descricao", VOUtils.montarPath(DocumentoRequerimento.PROP_ATIVIDADE_ESTABELECIMENTO, AtividadeEstabelecimento.PROP_DESCRICAO));
        hql.addToSelect("est.codigo", VOUtils.montarPath(DocumentoRequerimento.PROP_CIDADE, Cidade.PROP_ESTADO, Estado.PROP_CODIGO));
        hql.addToSelect("est.sigla", VOUtils.montarPath(DocumentoRequerimento.PROP_CIDADE, Cidade.PROP_ESTADO, Estado.PROP_SIGLA));
        hql.addToSelect("ge.codigo", VOUtils.montarPath(DocumentoRequerimento.PROP_GRUPO_ESTABELECIMENTO, GrupoEstabelecimento.PROP_CODIGO));
        hql.addToSelect("ge.descricao", VOUtils.montarPath(DocumentoRequerimento.PROP_GRUPO_ESTABELECIMENTO, GrupoEstabelecimento.PROP_DESCRICAO));
        hql.addToSelect("ge.formatacaoAlvara", VOUtils.montarPath(DocumentoRequerimento.PROP_GRUPO_ESTABELECIMENTO, GrupoEstabelecimento.PROP_FORMATACAO_ALVARA));

        hql.addToSelect("rv.codigo", VOUtils.montarPath(DocumentoRequerimento.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_CODIGO));
        hql.addToSelect("ts.codigo", VOUtils.montarPath(DocumentoRequerimento.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_TIPO_SOLICITACAO, TipoSolicitacao.PROP_CODIGO));
        hql.addToSelect("ts.lei", VOUtils.montarPath(DocumentoRequerimento.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_TIPO_SOLICITACAO, TipoSolicitacao.PROP_LEI));
        hql.addToSelect("vc.codigo", VOUtils.montarPath(DocumentoRequerimento.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_VEICULO, VeiculoEstabelecimento.PROP_CODIGO));
        hql.addToSelect("vc.placa", VOUtils.montarPath(DocumentoRequerimento.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_VEICULO, VeiculoEstabelecimento.PROP_PLACA));
        hql.addToSelect("vc.renavam", VOUtils.montarPath(DocumentoRequerimento.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_VEICULO, VeiculoEstabelecimento.PROP_RENAVAM));
        hql.addToSelect("vc.tipoVeiculo", VOUtils.montarPath(DocumentoRequerimento.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_VEICULO, VeiculoEstabelecimento.PROP_TIPO_VEICULO));
        hql.addToSelect("vc.especificacao", VOUtils.montarPath(DocumentoRequerimento.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_VEICULO, VeiculoEstabelecimento.PROP_ESPECIFICACAO));

//        hql.addToSelect("rt.codigo", VOUtils.montarPath(DocumentoRequerimento.PROP_ESTABELECIMENTO, Estabelecimento.PROP_RESPONSAVEL_TECNICO, ResponsavelTecnico.PROP_CODIGO));
//        hql.addToSelect("rt.nome", VOUtils.montarPath(DocumentoRequerimento.PROP_ESTABELECIMENTO, Estabelecimento.PROP_RESPONSAVEL_TECNICO, ResponsavelTecnico.PROP_NOME));
//        hql.addToSelect("rt.cpf", VOUtils.montarPath(DocumentoRequerimento.PROP_ESTABELECIMENTO, Estabelecimento.PROP_RESPONSAVEL_TECNICO, ResponsavelTecnico.PROP_CPF));
//        hql.addToSelect("rt.numeroRegistro", VOUtils.montarPath(DocumentoRequerimento.PROP_ESTABELECIMENTO, Estabelecimento.PROP_RESPONSAVEL_TECNICO, ResponsavelTecnico.PROP_NUMERO_REGISTRO));
//        hql.addToSelect("oe.codigo", VOUtils.montarPath(DocumentoRequerimento.PROP_ESTABELECIMENTO, Estabelecimento.PROP_RESPONSAVEL_TECNICO, ResponsavelTecnico.PROP_ORGAO_EMISSOR, OrgaoEmissor.PROP_CODIGO));
//        hql.addToSelect("oe.sigla", VOUtils.montarPath(DocumentoRequerimento.PROP_ESTABELECIMENTO, Estabelecimento.PROP_RESPONSAVEL_TECNICO, ResponsavelTecnico.PROP_ORGAO_EMISSOR, OrgaoEmissor.PROP_SIGLA));
//        hql.addToSelect("cd.codigo", VOUtils.montarPath(DocumentoRequerimento.PROP_ESTABELECIMENTO, Estabelecimento.PROP_RESPONSAVEL_TECNICO, ResponsavelTecnico.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE, Cidade.PROP_CODIGO));
//        hql.addToSelect("est.codigo", VOUtils.montarPath(DocumentoRequerimento.PROP_ESTABELECIMENTO, Estabelecimento.PROP_RESPONSAVEL_TECNICO, ResponsavelTecnico.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE, Cidade.PROP_ESTADO, Estado.PROP_CODIGO));
//        hql.addToSelect("est.sigla", VOUtils.montarPath(DocumentoRequerimento.PROP_ESTABELECIMENTO, Estabelecimento.PROP_RESPONSAVEL_TECNICO, ResponsavelTecnico.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE, Cidade.PROP_ESTADO, Estado.PROP_SIGLA));

        hql.addToFrom("DocumentoRequerimento dr"
                + " left join dr.requerimentoVigilancia rv "
                + " left join rv.veiculo vc "
                + " left join rv.tipoSolicitacao ts "
                + " left join dr.cidade cid "
                + " left join dr.grupoEstabelecimento ge"
                + " left join cid.estado est "
                + " left join dr.atividadeEstabelecimento ae"
                + " left join dr.estabelecimento e"
                + " left join e.responsavelTecnico rt"
                + " left join rt.orgaoEmissor oe"
                + " left join rt.vigilanciaEndereco ve"
                + " left join ve.cidade cd"
                + " left join cd.estado est"
        );

        hql.addToWhereWhithAnd("rv =", this.requerimentoVigilancia);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        addCustomParams(session);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<DocumentoRequerimento> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(RequerimentoVigilancia requerimentoVigilancia) {
        this.requerimentoVigilancia = requerimentoVigilancia;
    }

    public void addCustomParams(Session session) {
    }

}
