<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_resumo_consumo_produto" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="f6eb8aea-3a21-48c0-ad10-853afb42f555">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.4641000000000008"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoque"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.basico.*"/>
	<import value="br.com.ksisolucoes.vo.basico.Empresa"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<import value="org.apache.commons.lang.StringUtils"/>
	<parameter name="QUANTIDADE_MES_MEDIA" class="java.lang.Long" isForPrompting="false"/>
	<parameter name="AGRUPAR_EMPRESA" class="java.lang.String"/>
	<field name="codigoEmpresa" class="java.lang.String"/>
	<field name="descricaoEmpresa" class="java.lang.String"/>
	<field name="descricaoProdutoFormatado" class="java.lang.String"/>
	<field name="descricaoGrupoFormatado" class="java.lang.String"/>
	<field name="descricaoSubGrupoFormatado" class="java.lang.String"/>
	<field name="codigoSubGrupo" class="java.lang.Long"/>
	<field name="codigoProduto" class="java.lang.String"/>
	<field name="quantidadeMedia" class="java.lang.Double"/>
	<field name="valorMedia" class="java.lang.Double"/>
	<field name="quantidade" class="java.lang.Double"/>
	<field name="valor" class="java.lang.Double"/>
	<field name="totalMesesMovimentados" class="java.lang.Integer"/>
	<field name="unidadeProduto" class="java.lang.String">
		<fieldDescription><![CDATA[unidadeProduto]]></fieldDescription>
	</field>
	<field name="descricaoSubGrupo" class="java.lang.String"/>
	<field name="codigoGrupo" class="java.lang.Long"/>
	<field name="descricaoGrupo" class="java.lang.String"/>
	<field name="descricaoProduto" class="java.lang.String"/>
	<variable name="TOTAL_CONSUMO_VALOR_GRUPO" class="java.lang.Double" resetType="Group" resetGroup="GrupoGrupo" calculation="Sum">
		<variableExpression><![CDATA[Valor.round($F{valor} == null ? 0.00 : $F{valor},2)]]></variableExpression>
	</variable>
	<variable name="TOTAL_CONSUMO_MEDIO_VALOR_GRUPO" class="java.lang.Double" resetType="Group" resetGroup="GrupoGrupo" calculation="Sum">
		<variableExpression><![CDATA[Valor.round($F{quantidadeMedia}==null || $F{quantidadeMedia} ==null || $F{quantidadeMedia}.equals(new Double(0)) || $F{quantidadeMedia}.equals(new Double(0)) ? 0.00 : $F{valorMedia} / $F{totalMesesMovimentados},2)]]></variableExpression>
	</variable>
	<variable name="TOTAL_CONSUMO_VALOR_EMPRESA" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[Valor.round($F{valor} == null ? 0.00 : $F{valor},2)]]></variableExpression>
	</variable>
	<variable name="TOTAL_CONSUMO_MEDIO_VALOR_EMPRESA" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[Valor.round($F{quantidadeMedia}==null || $F{quantidadeMedia} ==null || $F{quantidadeMedia}.equals(new Double(0)) || $F{quantidadeMedia}.equals(new Double(0)) ? 0.00 : $F{valorMedia} / $F{totalMesesMovimentados},2)]]></variableExpression>
	</variable>
	<variable name="TOTAL_CONSUMO_VALOR_GERAL" class="java.lang.Double" resetType="Group" resetGroup="GrupoGeral" calculation="Sum">
		<variableExpression><![CDATA[Valor.round($F{valor} == null ? 0.00 : $F{valor},2)]]></variableExpression>
	</variable>
	<variable name="TOTAL_CONSUMO_MEDIO_VALOR_GERAL" class="java.lang.Double" resetType="Group" resetGroup="GrupoGeral" calculation="Sum">
		<variableExpression><![CDATA[Valor.round($F{quantidadeMedia}==null || $F{quantidadeMedia} ==null || $F{quantidadeMedia}.equals(new Double(0)) || $F{quantidadeMedia}.equals(new Double(0)) ? 0.00 : $F{valorMedia} / $F{totalMesesMovimentados},2)]]></variableExpression>
	</variable>
	<group name="GrupoGeral">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band splitType="Stretch"/>
		</groupHeader>
		<groupFooter>
			<band height="13" splitType="Stretch">
				<textField evaluationTime="Group" evaluationGroup="GrupoGeral" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-121" mode="Transparent" x="305" y="2" width="49" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="98cf4bec-acc6-4aa9-ad7a-4ec8276c11fa">
						<printWhenExpression><![CDATA[$V{PAGE_NUMBER} == $V{PAGE_NUMBER}?new Boolean(true):new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TOTAL_CONSUMO_VALOR_GERAL}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoGeral" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-122" mode="Transparent" x="410" y="2" width="49" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="52114a08-**************-ffad88c99087">
						<printWhenExpression><![CDATA[$V{PAGE_NUMBER} == $V{PAGE_NUMBER}?new Boolean(true):new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TOTAL_CONSUMO_MEDIO_VALOR_GERAL}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-123" mode="Transparent" x="138" y="2" width="119" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="43862246-0e7a-49f4-b8e1-0cf9061b87d0"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_totais") + " " + Bundle.getStringApplication("rotulo_geral") + ":"]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="GrupoEmpresa" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band height="14" splitType="Stretch">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{AGRUPAR_EMPRESA})]]></printWhenExpression>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-43" mode="Opaque" x="0" y="0" width="555" height="14" forecolor="#000000" backcolor="#CCCCCC" uuid="6227b542-a95b-4918-b0db-39525b944f35"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_empresa")  + ":" +" " + "(" + $F{codigoEmpresa}.trim() + ")" + "" + $F{descricaoEmpresa}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="13" splitType="Stretch">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{AGRUPAR_EMPRESA})]]></printWhenExpression>
				<textField evaluationTime="Group" evaluationGroup="GrupoGrupo" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-118" mode="Transparent" x="305" y="2" width="49" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="6c774e4c-1bd8-450d-a90c-af23703b3b02"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TOTAL_CONSUMO_VALOR_EMPRESA}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoGrupo" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-119" mode="Transparent" x="410" y="2" width="49" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="da838cb0-c457-416b-8aeb-4050f978fede"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TOTAL_CONSUMO_MEDIO_VALOR_EMPRESA}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-120" mode="Transparent" x="138" y="2" width="119" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="5b3f25c9-d712-43e2-91a2-e6bf82464980"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_totais") + " " + Bundle.getStringApplication("rotulo_empresa") + ":"]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="GrupoGrupo" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{codigoGrupo} + $F{codigoSubGrupo}]]></groupExpression>
		<groupHeader>
			<band height="40" splitType="Stretch">
				<rectangle radius="8">
					<reportElement key="rectangle-1" mode="Opaque" x="0" y="16" width="555" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="5a66b228-603c-4d89-9a88-b98b125e4d36"/>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</rectangle>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GrupoGrupo" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-99" mode="Opaque" x="0" y="0" width="555" height="14" forecolor="#000000" backcolor="#CCCCCC" uuid="3bc9e1e0-3e25-45b8-9db7-8922744f6f17"/>
					<box leftPadding="5">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*grupo*/Bundle.getStringApplication("rotulo_grupo") + ":" + " " + $F{codigoGrupo}+" - "+$F{descricaoGrupo} + "   " + Bundle.getStringApplication("rotulo_subgrupo") + ":" + " " + $F{codigoSubGrupo}+"-"+$F{descricaoSubGrupo}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-95" mode="Transparent" x="4" y="17" width="223" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="1298da08-a6f6-4125-b278-5f1ae8f12c85"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*produto*/Bundle.getStringApplication("rotulo_produto")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-6" mode="Transparent" x="259" y="17" width="95" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="e4f3130f-e5c7-4949-b2a1-dbcc40a10eb0"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_consumo")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-2" mode="Transparent" x="259" y="28" width="46" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="dbe5c394-9d13-4972-8bf7-92b6ff04c548"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quantidade_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-3" mode="Transparent" x="306" y="28" width="48" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="5d900523-de36-493b-8f00-cab9442cd0a7"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_valor_total_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-101" mode="Transparent" x="363" y="17" width="96" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="ad970ce6-d5d4-44c2-b5b9-a6fe042ceb39"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_consumo_medio")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-102" mode="Transparent" x="363" y="28" width="46" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="ee8a9e60-0d86-4524-b593-65ee804ef41e"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quantidade_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-103" mode="Transparent" x="411" y="28" width="48" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="a1fe5c70-1863-40f8-98f8-4f723e23f9cb"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_valor_total_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-116" mode="Transparent" x="229" y="17" width="28" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="5ec43def-f998-4ed5-88f8-96bb93c1f802"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*unidade*/Bundle.getStringApplication("rotulo_unidade_abreviacao")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="12" splitType="Stretch">
				<textField evaluationTime="Group" evaluationGroup="GrupoGrupo" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-111" mode="Transparent" x="305" y="2" width="49" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="165152ff-0c49-4b7f-920d-b0db13e0880d"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TOTAL_CONSUMO_VALOR_GRUPO}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoGrupo" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-113" mode="Transparent" x="410" y="2" width="49" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="3d8af2d2-32fb-433a-9b3d-a62e2673415c"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TOTAL_CONSUMO_MEDIO_VALOR_GRUPO}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-115" mode="Transparent" x="138" y="2" width="119" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="e732eeaf-a71b-4176-bb4f-cfe36dce56f9"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_totais") + " " + Bundle.getStringApplication("rotulo_grupo") + ":"]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="11" splitType="Stretch">
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField" mode="Transparent" x="259" y="1" width="46" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="8fd5a95e-983b-4a1d-89ab-61889499e28d"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidade} == null ? 0.00 : $F{quantidade}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-104" mode="Transparent" x="363" y="1" width="46" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="2eeb6f7f-de31-4fa1-b41a-df8ac7f35fda"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidadeMedia} == null || $F{quantidadeMedia}.equals(new Double(0)) || $F{totalMesesMovimentados} == null || $F{totalMesesMovimentados}.equals(new Double(0)) ? 0.00 : $F{quantidadeMedia} / $F{totalMesesMovimentados}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-105" mode="Transparent" x="410" y="1" width="49" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="b9b67547-4736-419c-8702-60c60d59bdcb"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidadeMedia}==null || $F{quantidadeMedia} ==null || $F{quantidadeMedia}.equals(new Double(0)) || $F{quantidadeMedia}.equals(new Double(0)) ? 0.00 : Valor.round($F{valorMedia} / $F{totalMesesMovimentados},2)]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-117" mode="Transparent" x="229" y="1" width="28" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="786199d0-754c-4fec-b885-ac33fd803439"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unidadeProduto} == null ? Bundle.getStringApplication("rotulo_sem_cadastro"):
$F{unidadeProduto}]]></textFieldExpression>
			</textField>
			<elementGroup>
				<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="false">
					<reportElement key="textField-8" mode="Transparent" x="305" y="1" width="49" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="ed84ecdb-6765-4508-a221-4fbc4086559b"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{valor} == null ? 0.00 : Valor.round($F{valor},2)]]></textFieldExpression>
				</textField>
			</elementGroup>
			<elementGroup>
				<elementGroup>
					<textField pattern="" isBlankWhenNull="true">
						<reportElement key="textField-36" mode="Transparent" x="4" y="1" width="223" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="09b63a51-ad1e-454c-98c2-e35c8225e6ac"/>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
							<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
							<paragraph lineSpacing="Single"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{codigoProduto} == null ? Bundle.getStringApplication("rotulo_sem_cadastro"):
"("+$F{codigoProduto}+") "+$F{descricaoProduto}]]></textFieldExpression>
					</textField>
				</elementGroup>
			</elementGroup>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
