<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_requerimento_livro_vigilancia_termo" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="a1b57cfd-9055-4cac-88e7-9178d405e0e1">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.7715610000000166"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.util.Util"/>
	<import value="br.com.celk.util.DataUtil"/>
	<import value="br.com.celk.util.Coalesce"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="ABERTURA_LIVRO" class="java.lang.Boolean"/>
	<field name="requerimentoLivro" class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoLivro"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<detail>
		<band height="314" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement uuid="e4ce5ec0-b924-45bd-9dae-e18bd5c31be9" positionType="Float" x="34" y="17" width="487" height="15"/>
				<textElement textAlignment="Justified">
					<font fontName="Arial" size="12"/>
					<paragraph lineSpacing="Double"/>
				</textElement>
				<textFieldExpression><![CDATA["Aos " + ($P{ABERTURA_LIVRO} ? DataUtil.getDataFormatadaMesString($F{requerimentoLivro}.getDataCadastro()) : DataUtil.getDataFormatadaMesString($F{requerimentoLivro}.getDataFinalizacao()))
+ ", o estabelecimento " + $F{requerimentoLivro}.getEstabelecimento().getRazaoSocial() + " solicitou "
+ ($P{ABERTURA_LIVRO} ? "abertura" : "fechamento") + " do Livro de Registro de "
+ $F{requerimentoLivro}.getDescricaoTipo() + " com " + $F{requerimentoLivro}.getNumeroPaginas() + " páginas."]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
