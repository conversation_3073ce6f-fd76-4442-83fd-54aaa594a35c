package br.com.ksisolucoes.report.consorcio.pedidotransferencialicitacaoentrega;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoEntregasDTOParam;
import br.com.ksisolucoes.report.consorcio.pedidotransferencialicitacaoentrega.query.QueryRelatorioDetalhamentoEntregas;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioDetalhamentoEntregas extends AbstractReport<RelatorioDetalhamentoEntregasDTOParam> {

    public RelatorioDetalhamentoEntregas(RelatorioDetalhamentoEntregasDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        if (RelatorioDetalhamentoEntregasDTOParam.TipoRelatorio.DETALHADO.value().equals(getParam().getTipoRelatorio().value())) {
            return "/br/com/ksisolucoes/report/consorcio/pedidotransferencialicitacaoentrega/jrxml/relatorio_detalhamento_entregas.jrxml";
        } else {
            return "/br/com/ksisolucoes/report/consorcio/pedidotransferencialicitacaoentrega/jrxml/relatorio_detalhamento_entregas_resumido.jrxml";
        }
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_detalhamento_entregas");
    }

    @Override
    public ITransferDataReport getQuery() {
        this.addParametro("formaApresentacao", this.getParam().getFormaApresentacao());
        this.addParametro("tipoResumo", this.getParam().getTipoResumo());
        this.addParametro("tipoRelatorio", this.getParam().getTipoRelatorio());
        return new QueryRelatorioDetalhamentoEntregas();
    }
}
