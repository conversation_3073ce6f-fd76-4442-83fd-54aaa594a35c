package br.com.ksisolucoes.report.prontuario;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.enfermagem.interfaces.dto.ImpressaoPrescricaoEnfermagemDTOParam;
import br.com.ksisolucoes.report.prontuario.query.QueryImpressaoPrescricaoEnfermagem;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoPrescricaoEnfermagem extends AbstractReport<ImpressaoPrescricaoEnfermagemDTOParam> {

    public RelatorioImpressaoPrescricaoEnfermagem(ImpressaoPrescricaoEnfermagemDTOParam param){
      super(param);
    }

    @Override
    public ITransferDataReport getQuery(){   
        return new QueryImpressaoPrescricaoEnfermagem();
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/prontuario/enfermagem/jrxml/relatorio_impressao_prescricao_enfermagem.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_prescricao_enfermagem");
    }
}
