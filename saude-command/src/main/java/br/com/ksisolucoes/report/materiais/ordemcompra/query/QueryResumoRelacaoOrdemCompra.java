package br.com.ksisolucoes.report.materiais.ordemcompra.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.materiais.ordemcompra.interfaces.dto.ResumoRelacaoOrdemCompraDTO;
import br.com.ksisolucoes.report.materiais.ordemcompra.interfaces.dto.ResumoRelacaoOrdemCompraDTOParam;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryResumoRelacaoOrdemCompra extends CommandQuery<QueryResumoRelacaoOrdemCompra> implements ITransferDataReport<ResumoRelacaoOrdemCompraDTOParam, ResumoRelacaoOrdemCompraDTO> {

    private ResumoRelacaoOrdemCompraDTOParam param;
    private List<ResumoRelacaoOrdemCompraDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(ResumoRelacaoOrdemCompraDTO.class.getName());

        hql.addToSelect("sum(coalesce(oci.quantidadeCompra, 0))", "totalQuantidadeCompra");
        hql.addToSelect("sum(coalesce(oci.quantidadeRecebida, 0))", "totalQuantidadeRecebida");
        hql.addToSelect("sum(coalesce(oci.quantidadePregao, 0))", "totalQuantidadePregao");
        hql.addToSelect("sum(coalesce(oci.precoUnitario, 0))", "totalPrecoUnitario");

        if (!param.getFormaApresentacao().value().equals(param.getTipoResumo().value())) {
            if (ResumoRelacaoOrdemCompraDTOParam.FormaApresentacao.FORNECEDOR.equals(param.getFormaApresentacao())) {
                hql.addToSelectAndGroup("p.codigo", "fornecedor.codigo");
                hql.addToSelectAndGroupAndOrder("p.descricao", "fornecedor.descricao");
            } else if (ResumoRelacaoOrdemCompraDTOParam.FormaApresentacao.PRODUTO.equals(param.getFormaApresentacao())) {
                hql.addToSelectAndGroup("prod.codigo", "produto.codigo");
                hql.addToSelectAndGroupAndOrder("prod.descricao", "produto.descricao");
                hql.addToSelectAndGroup("u.codigo", "unidade.codigo");
                hql.addToSelectAndGroupAndOrder("u.unidade", "unidade.unidade");
            } else if (ResumoRelacaoOrdemCompraDTOParam.FormaApresentacao.NUMERO_PREGAO.equals(param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("oc.numeroPregao", "ordemCompra.numeroPregao");
                hql.addToSelectAndGroup("oc.codigo", "ordemCompra.codigo");
                hql.addToSelectAndGroup("oc.dataCadastro", "ordemCompra.dataCadastro");
            }
        }

        if (ResumoRelacaoOrdemCompraDTOParam.TipoResumo.FORNECEDOR.equals(param.getTipoResumo())) {
            hql.addToSelectAndGroup("p.codigo", "fornecedor.codigo");
            hql.addToSelectAndGroupAndOrder("p.descricao", "fornecedor.descricao");
        } else if (ResumoRelacaoOrdemCompraDTOParam.TipoResumo.PRODUTO.equals(param.getTipoResumo())) {
            hql.addToSelectAndGroup("prod.codigo", "produto.codigo");
            hql.addToSelectAndGroupAndOrder("prod.descricao", "produto.descricao");
            hql.addToSelectAndGroup("u.codigo", "unidade.codigo");
            hql.addToSelectAndGroupAndOrder("u.unidade", "unidade.unidade");
        }

        hql.addToFrom("OrdemCompraItem oci "
                + " left join oci.ordemCompra oc"
                + " left join oc.pessoa p"
                + " left join oci.produto prod"
                + " left join oc.empresa e"
                + " left join prod.unidade u");

        hql.addToWhereWhithAnd("p = ", param.getFornecedor());
        hql.addToWhereWhithAnd("prod = ", param.getProduto());
        hql.addToWhereWhithAnd("oc.dataCadastro ", param.getPeriodo());
        hql.addToWhereWhithAnd("oci.status in ", param.getInSituacao());

        if (param.getNrPregao() != null) {
            hql.addToWhereWhithAnd("oc.numeroPregao = ", param.getNrPregao().toString());
        }
        hql.addToWhereWhithAnd("e.codigo = ", getSessao().getCodigoEmpresa());
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<ResumoRelacaoOrdemCompraDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(ResumoRelacaoOrdemCompraDTOParam param) {
        this.param = param;
    }
}
