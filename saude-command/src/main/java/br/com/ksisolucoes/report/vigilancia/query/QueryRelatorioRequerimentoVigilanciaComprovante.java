/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.vigilancia.query;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioRequerimentoVigilanciaComprovanteDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioRequerimentoVigilanciaComprovanteDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia;
import br.com.ksisolucoes.vo.vigilancia.denuncia.TipoDenuncia;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.*;
import ch.lambdaj.Lambda;
import org.hibernate.Query;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioRequerimentoVigilanciaComprovante extends CommandQuery<QueryRelatorioRequerimentoVigilanciaComprovante> implements ITransferDataReport<RelatorioRequerimentoVigilanciaComprovanteDTOParam, RelatorioRequerimentoVigilanciaComprovanteDTO> {

    private RelatorioRequerimentoVigilanciaComprovanteDTOParam param;
    private List<RelatorioRequerimentoVigilanciaComprovanteDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.setTypeSelect(RelatorioRequerimentoVigilanciaComprovanteDTO.class.getName());
        hql.addToSelect("est.codigo", "documentoRequerimento.requerimentoVigilancia.estabelecimento.codigo");
        hql.addToSelect("est.razaoSocial", "documentoRequerimento.requerimentoVigilancia.estabelecimento.razaoSocial");
        hql.addToSelect("est.cnpjCpf", "documentoRequerimento.requerimentoVigilancia.estabelecimento.cnpjCpf");
        hql.addToSelect("est.fantasia", "documentoRequerimento.requerimentoVigilancia.estabelecimento.fantasia");
        hql.addToSelect("ts.codigo", "documentoRequerimento.requerimentoVigilancia.tipoSolicitacao.codigo");
        hql.addToSelect("ts.descricao", "documentoRequerimento.requerimentoVigilancia.tipoSolicitacao.descricao");

        hql.addToSelect("rv.codigo", "documentoRequerimento.requerimentoVigilancia.codigo");
        hql.addToSelect("rv.protocolo", "documentoRequerimento.requerimentoVigilancia.protocolo");
        hql.addToSelect("rv.situacao", "documentoRequerimento.requerimentoVigilancia.situacao");
        hql.addToSelect("rv.nomeSolicitante", "documentoRequerimento.requerimentoVigilancia.nomeSolicitante");
        hql.addToSelect("rv.rgCpfSolicitante", "documentoRequerimento.requerimentoVigilancia.rgCpfSolicitante");
        hql.addToSelect("rv.cargoSolicitante", "documentoRequerimento.requerimentoVigilancia.cargoSolicitante");
        hql.addToSelect("rv.telefoneSolicitante", "documentoRequerimento.requerimentoVigilancia.telefoneSolicitante");
        hql.addToSelect("rv.dataRequerimento", "documentoRequerimento.requerimentoVigilancia.dataRequerimento");
        hql.addToSelect("rv.tipoDocumento", "documentoRequerimento.requerimentoVigilancia.tipoDocumento");
        hql.addToSelect("rv.nome", "documentoRequerimento.requerimentoVigilancia.nome");
        hql.addToSelect("rv.telefone", "documentoRequerimento.requerimentoVigilancia.telefone");
        hql.addToSelect("rv.cnpjCpf", "documentoRequerimento.requerimentoVigilancia.cnpjCpf");
        hql.addToSelect("rv.numero", "documentoRequerimento.requerimentoVigilancia.numero");
        hql.addToSelect("rv.complemento", "documentoRequerimento.requerimentoVigilancia.complemento");
        hql.addToSelect("rv.observacaoRequerimento", "documentoRequerimento.requerimentoVigilancia.observacaoRequerimento");

        hql.addToSelect("setores.codigo", "documentoRequerimento.requerimentoVigilancia.estabelecimentoSetores.codigo");
        hql.addToSelect("setores.descricaoSetor", "documentoRequerimento.requerimentoVigilancia.estabelecimentoSetores.descricaoSetor");

        hql.addToSelect("ve.codigo", "documentoRequerimento.requerimentoVigilancia.vigilanciaEndereco.codigo");
        hql.addToSelect("ve.bairro", "documentoRequerimento.requerimentoVigilancia.vigilanciaEndereco.bairro");
        hql.addToSelect("ve.cep", "documentoRequerimento.requerimentoVigilancia.vigilanciaEndereco.cep");
        hql.addToSelect("ve.logradouro", "documentoRequerimento.requerimentoVigilancia.vigilanciaEndereco.logradouro");

        hql.addToSelect("c.codigo", "documentoRequerimento.requerimentoVigilancia.vigilanciaEndereco.cidade.codigo");
        hql.addToSelect("c.descricao", "documentoRequerimento.requerimentoVigilancia.vigilanciaEndereco.cidade.descricao");

        hql.addToSelect("es.codigo", "documentoRequerimento.requerimentoVigilancia.vigilanciaEndereco.cidade.estado.codigo");
        hql.addToSelect("es.descricao", "documentoRequerimento.requerimentoVigilancia.vigilanciaEndereco.cidade.estado.descricao");
        hql.addToSelect("es.sigla", "documentoRequerimento.requerimentoVigilancia.vigilanciaEndereco.cidade.estado.sigla");

        hql.addToSelect("ep.codigo", "documentoRequerimento.requerimentoVigilancia.estabelecimento.estabelecimentoPrincipal.codigo");
        hql.addToSelect("ep.razaoSocial", "documentoRequerimento.requerimentoVigilancia.estabelecimento.estabelecimentoPrincipal.razaoSocial");
        hql.addToSelect("ep.cnpjCpf", "documentoRequerimento.requerimentoVigilancia.estabelecimento.estabelecimentoPrincipal.cnpjCpf");
        hql.addToSelect("ep.fantasia", "documentoRequerimento.requerimentoVigilancia.estabelecimento.estabelecimentoPrincipal.fantasia");

        hql.addToSelect("vp.codigo", "documentoRequerimento.requerimentoVigilancia.vigilanciaPessoa.codigo");
        hql.addToSelect("vp.nome", "documentoRequerimento.requerimentoVigilancia.vigilanciaPessoa.nome");
        hql.addToSelect("vp.cpf", "documentoRequerimento.requerimentoVigilancia.vigilanciaPessoa.cpf");

        hql.addToSelect("vep.codigo", "documentoRequerimento.requerimentoVigilancia.vigilanciaPessoa.vigilanciaEndereco.codigo");
        hql.addToSelect("vep.bairro", "documentoRequerimento.requerimentoVigilancia.vigilanciaPessoa.vigilanciaEndereco.bairro");
        hql.addToSelect("vep.cep", "documentoRequerimento.requerimentoVigilancia.vigilanciaPessoa.vigilanciaEndereco.cep");
        hql.addToSelect("vep.logradouro", "documentoRequerimento.requerimentoVigilancia.vigilanciaPessoa.vigilanciaEndereco.logradouro");

        hql.addToSelect("tp.descricao", "documentoRequerimento.requerimentoVigilancia.vigilanciaEndereco.tipoLogradouro.descricao");

        hql.addToSelect("(select ae.descricao "
                + "from EstabelecimentoAtividade ea "
                + "left join ea.atividadeEstabelecimento ae "
                + "where ea.estabelecimento = est AND ea.flagPrincipal = :sim)", "descricaoAtividadePrincipal");

        hql.addToFrom("RequerimentoVigilancia rv "
                + " left join rv.tipoSolicitacao ts "
                + " left join rv.estabelecimento est "
                + " left join rv.vigilanciaPessoa vp "
                + " left join vp.vigilanciaEndereco vep "
                + " left join rv.vigilanciaEndereco ve "
                + " left join ve.tipoLogradouro tp"
                + " left join rv.estabelecimentoSetores setores "
                + " left join ve.cidade c"
                + " left join c.estado es"
                + " left join est.estabelecimentoPrincipal ep ");

        if (param.getRequerimentoVigilancia() != null) {
            hql.addToWhereWhithAnd("rv.codigo =", param.getRequerimentoVigilancia());
        }else{
            hql.addToWhereWhithAnd("rv in ", param.getRequerimentos());
        }

    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(this.result)) {
            for (RelatorioRequerimentoVigilanciaComprovanteDTO dto : this.result) {
                setEnderecoComprovante(dto);
            }
        }
    }

    private void setEnderecoComprovante(RelatorioRequerimentoVigilanciaComprovanteDTO dto) {
        RequerimentoVigilancia requerimentoVigilancia = dto.getDocumentoRequerimento().getRequerimentoVigilancia();
        switch (TipoSolicitacao.TipoDocumento.valueOf(requerimentoVigilancia.getTipoDocumento())) {
            case DENUNCIA_RECLAMACAO:
                setEnderecoDenunciante(dto);
                setDenuncia(dto);
                break;
            case PROJETO_BASICO_ARQUITETURA:
                setEnderecoProjetoBasicoArquitetura(dto);
                break;
            case VISTORIA_LAUDO_CONFORMIDADE_PBA:
                setEnderecoVistoriaProjetoBasicoArquitetura(dto);
                break;
            case ANALISE_PROJETO_HIDROSSANITARIO:
                setEnderecoRequerimentoProjetoHidrossanitario(dto);
                break;
            case ANALISE_PROJETO_HIDROSSANITARIO_DECLARATORIO:
                setEnderecoRequerimentoProjetoHidrossanitarioDeclaratorio(dto);
                break;
            case VISTORIA_HABITESE_SANITARIO:
                setEnderecoRequerimentoVistoriaHidrossanitario(dto);
                break;
            case HABITE_SE_DECLARATORIO:
                setEnderecoRequerimentoHabitiseDeclaratorio(dto);
                break;
            case PROJETO_ARQUITETONICO_SANITARIO:
                setEnderecoRequerimentoProjetoArquitetonicoSanitario(dto);
                break;
        }
    }

    private void setEnderecoDenunciante(RelatorioRequerimentoVigilanciaComprovanteDTO dto) {
        Denuncia proxy = Lambda.on(Denuncia.class);

        Denuncia denuncia = LoadManager.getInstance(Denuncia.class)
                .addProperties(new HQLProperties(Denuncia.class).getProperties())
                .addProperties(new HQLProperties(VigilanciaEndereco.class, Denuncia.PROP_ENDERECO_DENUNCIANTE).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(Denuncia.PROP_REQUERIMENTO_VIGILANCIA, dto.getDocumentoRequerimento().getRequerimentoVigilancia()))
                .start().getVO();

        String endereco = null;
        if (denuncia.getEnderecoDenunciante() != null) {
            endereco = denuncia.getEnderecoDenunciante().getEnderecoFormatado();
        }

        if (endereco == null || endereco.isEmpty()) {
            if (denuncia.getLogradouroDenunciante() != null) {
                List<String> list = new ArrayList<>();

                list.add(denuncia.getLogradouroDenunciante());

                if (denuncia.getNumeroLogradouroDenunciante() != null) {
                    list.add(denuncia.getNumeroLogradouroDenunciante());
                }

                if (denuncia.getComplementoLogradouroDenunciante() != null) {
                    list.add(denuncia.getComplementoLogradouroDenunciante());
                }

                endereco = Lambda.join(list, ", ");
            }
        }

        dto.setEndereco(endereco);
    }

    private void setEnderecoProjetoBasicoArquitetura(RelatorioRequerimentoVigilanciaComprovanteDTO dto) {
        RequerimentoAnaliseProjeto proxy = Lambda.on(RequerimentoAnaliseProjeto.class);

        RequerimentoAnaliseProjeto requerimentoAnaliseProjeto = LoadManager.getInstance(RequerimentoAnaliseProjeto.class)
                .addProperty(path(proxy.getNumeroObra()))
                .addProperty(path(proxy.getQuadraObra()))
                .addProperty(path(proxy.getNumeroObraAoLado()))
                .addProperty(path(proxy.getLoteObra()))
                .addProperty(path(proxy.getComplementoObra()))
                .addProperty(path(proxy.getNumeroLoteamentoObra()))
                .addProperties(new HQLProperties(VigilanciaEndereco.class, RequerimentoAnaliseProjeto.PROP_VIGILANCIA_ENDERECO).getProperties())
                .addProperties(new HQLProperties(Cidade.class, VOUtils.montarPath(RequerimentoAnaliseProjeto.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoAnaliseProjeto.PROP_REQUERIMENTO_VIGILANCIA, dto.getDocumentoRequerimento().getRequerimentoVigilancia()))
                .start().getVO();

        if (requerimentoAnaliseProjeto.getVigilanciaEndereco() != null) {
            dto.setEnderecoProjeto(requerimentoAnaliseProjeto.getVigilanciaEndereco().getEnderecoFormatadoComCidade());
        }

        Map<String, String> infoObra = new HashMap<>();

        if (requerimentoAnaliseProjeto.getNumeroObra() != null) {
            infoObra.put("Nº Obra", requerimentoAnaliseProjeto.getNumeroObra());
        }

        if (requerimentoAnaliseProjeto.getQuadraObra() != null) {
            infoObra.put("Quadra", requerimentoAnaliseProjeto.getQuadraObra());
        }

        if (requerimentoAnaliseProjeto.getNumeroObraAoLado() != null) {
            infoObra.put("Nº Obra ao Lado", requerimentoAnaliseProjeto.getNumeroObraAoLado());
        }

        if (requerimentoAnaliseProjeto.getLoteObra() != null) {
            infoObra.put("Lote", requerimentoAnaliseProjeto.getLoteObra());
        }

        if (requerimentoAnaliseProjeto.getComplementoObra() != null) {
            infoObra.put("Complemento", requerimentoAnaliseProjeto.getComplementoObra());
        }

        if (requerimentoAnaliseProjeto.getNumeroLoteamentoObra() != null) {
            infoObra.put("Nº Loteamento", requerimentoAnaliseProjeto.getNumeroLoteamentoObra());
        }

        List<String> infos = new ArrayList<>();

        for (Map.Entry<String, String> item : infoObra.entrySet()) {
            infos.add("<b>".concat(item.getKey()).concat(":</b> ").concat(item.getValue()));
        }

        if (!infos.isEmpty()) {
            String infoObraProjeto = Lambda.join(infos, ", ");
            dto.setInfoObraProjeto(infoObraProjeto);
        }
    }

    private void setEnderecoVistoriaProjetoBasicoArquitetura(RelatorioRequerimentoVigilanciaComprovanteDTO dto) {
        RequerimentoVistoriaProjetoBasicoArquitetura proxy = Lambda.on(RequerimentoVistoriaProjetoBasicoArquitetura.class);

        RequerimentoVistoriaProjetoBasicoArquitetura requerimentoVistoriaProjetoBasicoArquitetura = LoadManager.getInstance(RequerimentoVistoriaProjetoBasicoArquitetura.class)
                .addProperty(path(proxy.getNumeroObra()))
                .addProperty(path(proxy.getQuadraObra()))
                .addProperty(path(proxy.getNumeroObraAoLado()))
                .addProperty(path(proxy.getLoteObra()))
                .addProperty(path(proxy.getComplementoObra()))
                .addProperty(path(proxy.getNumeroLoteamentoObra()))
                .addProperties(new HQLProperties(VigilanciaEndereco.class, RequerimentoVistoriaProjetoBasicoArquitetura.PROP_VIGILANCIA_ENDERECO).getProperties())
                .addProperties(new HQLProperties(Cidade.class, VOUtils.montarPath(RequerimentoAnaliseProjeto.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVistoriaProjetoBasicoArquitetura.PROP_REQUERIMENTO_VIGILANCIA, dto.getDocumentoRequerimento().getRequerimentoVigilancia()))
                .start().getVO();

        if (requerimentoVistoriaProjetoBasicoArquitetura.getVigilanciaEndereco() != null) {
            dto.setEnderecoProjeto(requerimentoVistoriaProjetoBasicoArquitetura.getVigilanciaEndereco().getEnderecoFormatadoComCidade());
        }


        Map<String, String> infoObra = new HashMap<>();

        if (requerimentoVistoriaProjetoBasicoArquitetura.getNumeroObra() != null) {
            infoObra.put("Nº Obra", requerimentoVistoriaProjetoBasicoArquitetura.getNumeroObra());
        }

        if (requerimentoVistoriaProjetoBasicoArquitetura.getQuadraObra() != null) {
            infoObra.put("Quadra", requerimentoVistoriaProjetoBasicoArquitetura.getQuadraObra());
        }

        if (requerimentoVistoriaProjetoBasicoArquitetura.getNumeroObraAoLado() != null) {
            infoObra.put("Nº Obra ao Lado", requerimentoVistoriaProjetoBasicoArquitetura.getNumeroObraAoLado());
        }

        if (requerimentoVistoriaProjetoBasicoArquitetura.getLoteObra() != null) {
            infoObra.put("Lote", requerimentoVistoriaProjetoBasicoArquitetura.getLoteObra());
        }

        if (requerimentoVistoriaProjetoBasicoArquitetura.getComplementoObra() != null) {
            infoObra.put("Complemento", requerimentoVistoriaProjetoBasicoArquitetura.getComplementoObra());
        }

        if (requerimentoVistoriaProjetoBasicoArquitetura.getNumeroLoteamentoObra() != null) {
            infoObra.put("Nº Loteamento", requerimentoVistoriaProjetoBasicoArquitetura.getNumeroLoteamentoObra());
        }

        List<String> infos = new ArrayList<>();

        for (Map.Entry<String, String> item : infoObra.entrySet()) {
            infos.add("<b>".concat(item.getKey()).concat(":</b> ").concat(item.getValue()));
        }

        if (!infos.isEmpty()) {
            String infoObraProjeto = Lambda.join(infos, ", ");
            dto.setInfoObraProjeto(infoObraProjeto);
        }
    }

    private void setEnderecoRequerimentoProjetoHidrossanitarioDeclaratorio(RelatorioRequerimentoVigilanciaComprovanteDTO dto) {
        RequerimentoProjetoHidrossanitarioDeclaratorio proxy = Lambda.on(RequerimentoProjetoHidrossanitarioDeclaratorio.class);

        RequerimentoProjetoHidrossanitarioDeclaratorio requerimentoProjetoHidrossanitario = LoadManager.getInstance(RequerimentoProjetoHidrossanitarioDeclaratorio.class)
                .addProperty(path(proxy.getObraNumeroEndereco()))
                .addProperty(path(proxy.getObraQuadra()))
                .addProperty(path(proxy.getObraNumeroLado()))
                .addProperty(path(proxy.getObraLote()))
                .addProperty(path(proxy.getObraComplemento()))
                .addProperty(path(proxy.getObraNumeroLoteamento()))
                .addProperties(new HQLProperties(VigilanciaEndereco.class, RequerimentoProjetoHidrossanitarioDeclaratorio.PROP_VIGILANCIA_ENDERECO).getProperties())
                .addProperties(new HQLProperties(Cidade.class, VOUtils.montarPath(RequerimentoAnaliseProjeto.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoProjetoHidrossanitarioDeclaratorio.PROP_REQUERIMENTO_VIGILANCIA, dto.getDocumentoRequerimento().getRequerimentoVigilancia()))
                .start().getVO();

        if (requerimentoProjetoHidrossanitario.getVigilanciaEndereco() != null) {
            dto.setEnderecoProjeto(requerimentoProjetoHidrossanitario.getVigilanciaEndereco().getEnderecoFormatadoComCidade());
        }


        Map<String, String> infoObra = new HashMap<>();

        if (requerimentoProjetoHidrossanitario.getObraNumeroEndereco() != null) {
            infoObra.put("Nº Obra", requerimentoProjetoHidrossanitario.getObraNumeroEndereco());
        }

        if (requerimentoProjetoHidrossanitario.getObraQuadra() != null) {
            infoObra.put("Quadra", requerimentoProjetoHidrossanitario.getObraQuadra());
        }

        if (requerimentoProjetoHidrossanitario.getObraNumeroLado() != null) {
            infoObra.put("Nº Obra ao Lado", requerimentoProjetoHidrossanitario.getObraNumeroLado());
        }

        if (requerimentoProjetoHidrossanitario.getObraLote() != null) {
            infoObra.put("Lote", requerimentoProjetoHidrossanitario.getObraLote());
        }

        if (requerimentoProjetoHidrossanitario.getObraComplemento() != null) {
            infoObra.put("Complemento", requerimentoProjetoHidrossanitario.getObraComplemento());
        }

        if (requerimentoProjetoHidrossanitario.getObraNumeroLoteamento() != null) {
            infoObra.put("Nº Loteamento", requerimentoProjetoHidrossanitario.getObraNumeroLoteamento());
        }

        List<String> infos = new ArrayList<>();

        for (Map.Entry<String, String> item : infoObra.entrySet()) {
            infos.add("<b>".concat(item.getKey()).concat(":</b> ").concat(item.getValue()));
        }

        if (!infos.isEmpty()) {
            String infoObraProjeto = Lambda.join(infos, ", ");
            dto.setInfoObraProjeto(infoObraProjeto);
        }
    }

    private void setEnderecoRequerimentoProjetoHidrossanitario(RelatorioRequerimentoVigilanciaComprovanteDTO dto) {
        RequerimentoProjetoHidrossanitario proxy = Lambda.on(RequerimentoProjetoHidrossanitario.class);

        RequerimentoProjetoHidrossanitario requerimentoProjetoHidrossanitario = LoadManager.getInstance(RequerimentoProjetoHidrossanitario.class)
                .addProperty(path(proxy.getObraNumeroEndereco()))
                .addProperty(path(proxy.getObraQuadra()))
                .addProperty(path(proxy.getObraNumeroLado()))
                .addProperty(path(proxy.getObraLote()))
                .addProperty(path(proxy.getObraComplemento()))
                .addProperty(path(proxy.getObraNumeroLoteamento()))
                .addProperties(new HQLProperties(VigilanciaEndereco.class, RequerimentoProjetoHidrossanitario.PROP_VIGILANCIA_ENDERECO).getProperties())
                .addProperties(new HQLProperties(Cidade.class, VOUtils.montarPath(RequerimentoAnaliseProjeto.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoProjetoHidrossanitario.PROP_REQUERIMENTO_VIGILANCIA, dto.getDocumentoRequerimento().getRequerimentoVigilancia()))
                .start().getVO();

        if (requerimentoProjetoHidrossanitario.getVigilanciaEndereco() != null) {
            dto.setEnderecoProjeto(requerimentoProjetoHidrossanitario.getVigilanciaEndereco().getEnderecoFormatadoComCidade());
        }


        Map<String, String> infoObra = new HashMap<>();

        if (requerimentoProjetoHidrossanitario.getObraNumeroEndereco() != null) {
            infoObra.put("Nº Obra", requerimentoProjetoHidrossanitario.getObraNumeroEndereco());
        }

        if (requerimentoProjetoHidrossanitario.getObraQuadra() != null) {
            infoObra.put("Quadra", requerimentoProjetoHidrossanitario.getObraQuadra());
        }

        if (requerimentoProjetoHidrossanitario.getObraNumeroLado() != null) {
            infoObra.put("Nº Obra ao Lado", requerimentoProjetoHidrossanitario.getObraNumeroLado());
        }

        if (requerimentoProjetoHidrossanitario.getObraLote() != null) {
            infoObra.put("Lote", requerimentoProjetoHidrossanitario.getObraLote());
        }

        if (requerimentoProjetoHidrossanitario.getObraComplemento() != null) {
            infoObra.put("Complemento", requerimentoProjetoHidrossanitario.getObraComplemento());
        }

        if (requerimentoProjetoHidrossanitario.getObraNumeroLoteamento() != null) {
            infoObra.put("Nº Loteamento", requerimentoProjetoHidrossanitario.getObraNumeroLoteamento());
        }

        List<String> infos = new ArrayList<>();

        for (Map.Entry<String, String> item : infoObra.entrySet()) {
            infos.add("<b>".concat(item.getKey()).concat(":</b> ").concat(item.getValue()));
        }

        if (!infos.isEmpty()) {
            String infoObraProjeto = Lambda.join(infos, ", ");
            dto.setInfoObraProjeto(infoObraProjeto);
        }
    }

    private void setEnderecoRequerimentoVistoriaHidrossanitario(RelatorioRequerimentoVigilanciaComprovanteDTO dto) {
        RequerimentoVistoriaHidrossanitario proxy = Lambda.on(RequerimentoVistoriaHidrossanitario.class);

        RequerimentoVistoriaHidrossanitario requerimentoVistoriaHidrossanitario = LoadManager.getInstance(RequerimentoVistoriaHidrossanitario.class)
                .addProperty(path(proxy.getObraNumeroEndereco()))
                .addProperty(path(proxy.getObraQuadra()))
                .addProperty(path(proxy.getObraNumeroLado()))
                .addProperty(path(proxy.getObraLote()))
                .addProperty(path(proxy.getObraComplemento()))
                .addProperty(path(proxy.getObraNumeroLoteamento()))
                .addProperties(new HQLProperties(VigilanciaEndereco.class, RequerimentoVistoriaHidrossanitario.PROP_VIGILANCIA_ENDERECO).getProperties())
                .addProperties(new HQLProperties(Cidade.class, VOUtils.montarPath(RequerimentoAnaliseProjeto.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVistoriaHidrossanitario.PROP_REQUERIMENTO_VIGILANCIA, dto.getDocumentoRequerimento().getRequerimentoVigilancia()))
                .start().getVO();

        if (requerimentoVistoriaHidrossanitario.getVigilanciaEndereco() != null) {
            dto.setEnderecoProjeto(requerimentoVistoriaHidrossanitario.getVigilanciaEndereco().getEnderecoFormatadoComCidade());
        }


        Map<String, String> infoObra = new HashMap<>();

        if (requerimentoVistoriaHidrossanitario.getObraNumeroEndereco() != null) {
            infoObra.put("Nº Obra", requerimentoVistoriaHidrossanitario.getObraNumeroEndereco());
        }

        if (requerimentoVistoriaHidrossanitario.getObraQuadra() != null) {
            infoObra.put("Quadra", requerimentoVistoriaHidrossanitario.getObraQuadra());
        }

        if (requerimentoVistoriaHidrossanitario.getObraNumeroLado() != null) {
            infoObra.put("Nº Obra ao Lado", requerimentoVistoriaHidrossanitario.getObraNumeroLado());
        }

        if (requerimentoVistoriaHidrossanitario.getObraLote() != null) {
            infoObra.put("Lote", requerimentoVistoriaHidrossanitario.getObraLote());
        }

        if (requerimentoVistoriaHidrossanitario.getObraComplemento() != null) {
            infoObra.put("Complemento", requerimentoVistoriaHidrossanitario.getObraComplemento());
        }

        if (requerimentoVistoriaHidrossanitario.getObraNumeroLoteamento() != null) {
            infoObra.put("Nº Loteamento", requerimentoVistoriaHidrossanitario.getObraNumeroLoteamento());
        }

        List<String> infos = new ArrayList<>();

        for (Map.Entry<String, String> item : infoObra.entrySet()) {
            infos.add("<b>".concat(item.getKey()).concat(":</b> ").concat(item.getValue()));
        }

        if (!infos.isEmpty()) {
            String infoObraProjeto = Lambda.join(infos, ", ");
            dto.setInfoObraProjeto(infoObraProjeto);
        }
    }

    private void setEnderecoRequerimentoHabitiseDeclaratorio(RelatorioRequerimentoVigilanciaComprovanteDTO dto) {
        RequerimentoVistoriaHidrossanitarioDeclaratorio proxy = Lambda.on(RequerimentoVistoriaHidrossanitarioDeclaratorio.class);

        RequerimentoVistoriaHidrossanitarioDeclaratorio requerimentoVistoriaHidrossanitario = LoadManager.getInstance(RequerimentoVistoriaHidrossanitarioDeclaratorio.class)
                .addProperty(path(proxy.getObraNumeroEndereco()))
                .addProperty(path(proxy.getObraQuadra()))
                .addProperty(path(proxy.getObraNumeroLado()))
                .addProperty(path(proxy.getObraLote()))
                .addProperty(path(proxy.getObraComplemento()))
                .addProperty(path(proxy.getObraNumeroLoteamento()))
                .addProperties(new HQLProperties(VigilanciaEndereco.class, RequerimentoVistoriaHidrossanitarioDeclaratorio.PROP_VIGILANCIA_ENDERECO).getProperties())
                .addProperties(new HQLProperties(Cidade.class, VOUtils.montarPath(RequerimentoAnaliseProjeto.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVistoriaHidrossanitarioDeclaratorio.PROP_REQUERIMENTO_VIGILANCIA, dto.getDocumentoRequerimento().getRequerimentoVigilancia()))
                .start().getVO();

        if (requerimentoVistoriaHidrossanitario != null) {

            if (requerimentoVistoriaHidrossanitario.getVigilanciaEndereco() != null) {
                dto.setEnderecoProjeto(requerimentoVistoriaHidrossanitario.getVigilanciaEndereco().getEnderecoFormatadoComCidade());
            }

            Map<String, String> infoObra = new HashMap<>();

            if (requerimentoVistoriaHidrossanitario.getObraNumeroEndereco() != null) {
                infoObra.put("Nº Obra", requerimentoVistoriaHidrossanitario.getObraNumeroEndereco());
            }

            if (requerimentoVistoriaHidrossanitario.getObraQuadra() != null) {
                infoObra.put("Quadra", requerimentoVistoriaHidrossanitario.getObraQuadra());
            }

            if (requerimentoVistoriaHidrossanitario.getObraNumeroLado() != null) {
                infoObra.put("Nº Obra ao Lado", requerimentoVistoriaHidrossanitario.getObraNumeroLado());
            }

            if (requerimentoVistoriaHidrossanitario.getObraLote() != null) {
                infoObra.put("Lote", requerimentoVistoriaHidrossanitario.getObraLote());
            }

            if (requerimentoVistoriaHidrossanitario.getObraComplemento() != null) {
                infoObra.put("Complemento", requerimentoVistoriaHidrossanitario.getObraComplemento());
            }

            if (requerimentoVistoriaHidrossanitario.getObraNumeroLoteamento() != null) {
                infoObra.put("Nº Loteamento", requerimentoVistoriaHidrossanitario.getObraNumeroLoteamento());
            }

            List<String> infos = new ArrayList<>();

            for (Map.Entry<String, String> item : infoObra.entrySet()) {
                infos.add("<b>".concat(item.getKey()).concat(":</b> ").concat(item.getValue()));
            }

            if (!infos.isEmpty()) {
                String infoObraProjeto = Lambda.join(infos, ", ");
                dto.setInfoObraProjeto(infoObraProjeto);
            }

        }
    }

    private void setEnderecoRequerimentoProjetoArquitetonicoSanitario(RelatorioRequerimentoVigilanciaComprovanteDTO dto) {
        RequerimentoProjetoArquitetonicoSanitario proxy = Lambda.on(RequerimentoProjetoArquitetonicoSanitario.class);

        RequerimentoProjetoArquitetonicoSanitario requerimento = LoadManager.getInstance(RequerimentoProjetoArquitetonicoSanitario.class)
                .addProperty(path(proxy.getObraNumeroEndereco()))
                .addProperty(path(proxy.getObraQuadra()))
                .addProperty(path(proxy.getObraNumeroLado()))
                .addProperty(path(proxy.getObraLote()))
                .addProperty(path(proxy.getObraComplemento()))
                .addProperty(path(proxy.getObraNumeroLoteamento()))
                .addProperties(new HQLProperties(VigilanciaEndereco.class, RequerimentoProjetoArquitetonicoSanitario.PROP_VIGILANCIA_ENDERECO).getProperties())
                .addProperties(new HQLProperties(Cidade.class, VOUtils.montarPath(RequerimentoAnaliseProjeto.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoProjetoArquitetonicoSanitario.PROP_REQUERIMENTO_VIGILANCIA, dto.getDocumentoRequerimento().getRequerimentoVigilancia()))
                .start().getVO();

        if (requerimento != null) {

            if (requerimento.getVigilanciaEndereco() != null) {
                dto.setEnderecoProjeto(requerimento.getVigilanciaEndereco().getEnderecoFormatadoComCidade());
            }

            Map<String, String> infoObra = new HashMap<>();

            if (requerimento.getObraNumeroEndereco() != null) {
                infoObra.put("Nº Obra", requerimento.getObraNumeroEndereco());
            }

            if (requerimento.getObraQuadra() != null) {
                infoObra.put("Quadra", requerimento.getObraQuadra());
            }

            if (requerimento.getObraNumeroLado() != null) {
                infoObra.put("Nº Obra ao Lado", requerimento.getObraNumeroLado());
            }

            if (requerimento.getObraLote() != null) {
                infoObra.put("Lote", requerimento.getObraLote());
            }

            if (requerimento.getObraComplemento() != null) {
                infoObra.put("Complemento", requerimento.getObraComplemento());
            }

            if (requerimento.getObraNumeroLoteamento() != null) {
                infoObra.put("Loteamento", requerimento.getObraNumeroLoteamento());
            }

            List<String> infos = new ArrayList<>();

            for (Map.Entry<String, String> item : infoObra.entrySet()) {
                infos.add("<b>".concat(item.getKey()).concat(":</b> ").concat(item.getValue()));
            }

            if (!infos.isEmpty()) {
                String infoObraProjeto = Lambda.join(infos, ", ");
                dto.setInfoObraProjeto(infoObraProjeto);
            }

        }
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setLong("sim", RepositoryComponentDefault.SIM_LONG);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioRequerimentoVigilanciaComprovanteDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(RelatorioRequerimentoVigilanciaComprovanteDTOParam param) {
        this.param = param;
    }

    public void setDenuncia(RelatorioRequerimentoVigilanciaComprovanteDTO dto) {
        Denuncia denuncia = LoadManager.getInstance(Denuncia.class)
                .addProperties(new HQLProperties(Denuncia.class).getProperties())
                .addProperties(new HQLProperties(VigilanciaEndereco.class, Denuncia.PROP_ENDERECO_DENUNCIANTE).getProperties())
                .addProperties(new HQLProperties(VigilanciaEndereco.class, Denuncia.PROP_ENDERECO_DENUNCIADO).getProperties())
                .addProperties(new HQLProperties(TipoDenuncia.class, Denuncia.PROP_TIPO_DENUNCIA).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(Denuncia.PROP_REQUERIMENTO_VIGILANCIA, dto.getDocumentoRequerimento().getRequerimentoVigilancia()))
                .start().getVO();
        dto.setDenuncia(denuncia);
    }
}
