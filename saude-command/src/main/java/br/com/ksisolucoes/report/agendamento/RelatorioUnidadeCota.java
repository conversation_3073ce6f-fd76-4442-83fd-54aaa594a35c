package br.com.ksisolucoes.report.agendamento;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.agendamento.dto.RelatorioUnidadeCotaDTOParam;
import br.com.ksisolucoes.report.agendamento.query.QueryRelatorioUnidadeCota;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioUnidadeCota extends AbstractReport<RelatorioUnidadeCotaDTOParam> {

    public RelatorioUnidadeCota(RelatorioUnidadeCotaDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_unidade_cota.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relatorio_agenda_unidade_vaga");
    }

    @Override
    public ITransferDataReport getQuery() {
        addParametro("FORMA_APRESENTACAO", getParam().getFormaApresentacao());
        return new QueryRelatorioUnidadeCota();
    }

}
