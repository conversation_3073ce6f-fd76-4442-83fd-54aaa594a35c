<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_impressao_nota_alta" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="9211d19c-9cb7-4699-a35b-b3ac889c3b2f">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferencia"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.util.Coalesce"/>
	<import value="br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa"/>
	<import value="br.com.ksisolucoes.report.ReportProperties"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="br.com.ksisolucoes.vo.entradas.estoque.Produto"/>
	<import value="br.com.ksisolucoes.vo.basico.Empresa"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<field name="leitoQuarto" class="br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto"/>
	<field name="convenio" class="br.com.ksisolucoes.vo.prontuario.basico.Convenio"/>
	<field name="quartoInternacao" class="br.com.ksisolucoes.vo.prontuario.hospital.QuartoInternacao"/>
	<field name="numeroAtendimento" class="java.lang.Long"/>
	<field name="tipoAtendimento" class="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"/>
	<field name="dataChegada" class="java.util.Date"/>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="evolucaoProntuario" class="br.com.ksisolucoes.vo.prontuario.basico.EvolucaoProntuario"/>
	<field name="tabelaCbo" class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"/>
	<field name="atendimentoAlta" class="br.com.ksisolucoes.vo.prontuario.basico.AtendimentoAlta"/>
	<group name="tabelaCbo" isStartNewPage="true" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{tabelaCbo}.getTabelaCboGrupoAtendimento().getCodigo()]]></groupExpression>
		<groupHeader>
			<band height="146">
				<rectangle radius="5">
					<reportElement x="0" y="99" width="554" height="33" uuid="eaed94ec-622a-4cca-8819-44a4efdf69a4">
						<printWhenExpression><![CDATA[$F{atendimentoAlta} != null]]></printWhenExpression>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<rectangle radius="5">
					<reportElement x="0" y="7" width="555" height="78" uuid="eaed94ec-622a-4cca-8819-44a4efdf69a4"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-76" mode="Transparent" x="4" y="66" width="61" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Atendimento: */
Bundle.getStringApplication("rotulo_atendimento") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-76" mode="Transparent" x="294" y="118" width="251" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3">
						<printWhenExpression><![CDATA[$F{atendimentoAlta} != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{atendimentoAlta}.getMotivoFormatado()]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
					<reportElement key="textField-76" mode="Transparent" x="30" y="118" width="105" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3">
						<printWhenExpression><![CDATA[$F{atendimentoAlta} != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{atendimentoAlta}.getDataAlta()]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-76" mode="Transparent" x="4" y="40" width="47" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Convênio: */
Bundle.getStringApplication("rotulo_convenio") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-76" mode="Transparent" x="63" y="105" width="188" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3">
						<printWhenExpression><![CDATA[$F{atendimentoAlta} != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{profissional}.getNome()]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-77" mode="Transparent" x="4" y="27" width="92" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="62ccd2df-ed6d-4a6c-a5d5-9ca98adb6500"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Data de Nascimento: */
Bundle.getStringApplication("rotulo_data_de_nascimento") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-77" mode="Transparent" x="65" y="66" width="55" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{numeroAtendimento}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-77" mode="Transparent" x="246" y="66" width="254" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{tipoAtendimento}.getDescricao()]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy HH:mm" isBlankWhenNull="true">
					<reportElement key="textField-77" mode="Transparent" x="83" y="53" width="86" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{dataChegada}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-76" mode="Transparent" x="414" y="40" width="26" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Leito: */
Bundle.getStringApplication("rotulo_leito") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-77" mode="Transparent" x="466" y="14" width="88" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getCodigo()]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-76" mode="Transparent" x="318" y="105" width="230" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3">
						<printWhenExpression><![CDATA[$F{atendimentoAlta} != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{atendimentoAlta}.getCid().getDescricaoFormatado()]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-76" mode="Opaque" x="21" y="0" width="115" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
					<box leftPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Dados Atendimento*/
Bundle.getStringApplication("rotulo_dados_atendimento")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-77" mode="Transparent" x="249" y="53" width="251" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{empresa}.getDescricao()]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-77" mode="Transparent" x="440" y="27" width="114" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getSexoFormatado()]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-77" mode="Transparent" x="4" y="53" width="79" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="62ccd2df-ed6d-4a6c-a5d5-9ca98adb6500"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Data de Chegada: */
Bundle.getStringApplication("rotulo_data_da_chegada") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-76" mode="Transparent" x="221" y="40" width="35" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Quarto: */
Bundle.getStringApplication("rotulo_quarto") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-77" mode="Transparent" x="440" y="40" width="114" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{leitoQuarto}.getDescricao()]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
					<reportElement key="textField-77" mode="Transparent" x="96" y="27" width="88" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getDataNascimento()]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-76" mode="Transparent" x="221" y="27" width="29" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Idade: */
Bundle.getStringApplication("rotulo_idade") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-76" mode="Transparent" x="4" y="14" width="42" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Paciente: */
Bundle.getStringApplication("rotulo_paciente") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-76" mode="Transparent" x="260" y="118" width="34" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3">
						<printWhenExpression><![CDATA[$F{atendimentoAlta} != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Motivo: */
Bundle.getStringApplication("rotulo_motivo") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-77" mode="Transparent" x="250" y="27" width="128" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getDescricaoIdade()]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-76" mode="Transparent" x="414" y="27" width="26" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Sexo: */
Bundle.getStringApplication("rotulo_sexo") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-76" mode="Opaque" x="21" y="92" width="72" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3">
						<printWhenExpression><![CDATA[$F{atendimentoAlta} != null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Dados Alta*/
Bundle.getStringApplication("rotulo_dados_alta")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-76" mode="Transparent" x="260" y="105" width="58" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3">
						<printWhenExpression><![CDATA[$F{atendimentoAlta} != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Diagnóstico: */
Bundle.getStringApplication("rotulo_diagnostico") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-76" mode="Transparent" x="5" y="105" width="58" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3">
						<printWhenExpression><![CDATA[$F{atendimentoAlta} != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Profissional: */
Bundle.getStringApplication("rotulo_profissional") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-77" mode="Transparent" x="51" y="40" width="170" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{convenio}.getDescricao()]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-76" mode="Transparent" x="46" y="14" width="368" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNome()]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-76" mode="Transparent" x="221" y="53" width="28" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Setor: */
Bundle.getStringApplication("rotulo_setor") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-76" mode="Transparent" x="414" y="14" width="52" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Prontuário: */
Bundle.getStringApplication("rotulo_prontuario") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-76" mode="Transparent" x="5" y="118" width="25" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3">
						<printWhenExpression><![CDATA[$F{atendimentoAlta} != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Data: */
Bundle.getStringApplication("rotulo_data") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-77" mode="Transparent" x="256" y="40" width="150" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{quartoInternacao}.getDescricao()]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-76" mode="Transparent" x="221" y="66" width="25" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Tipo: */
Bundle.getStringApplication("rotulo_tipo") + ": "]]></textFieldExpression>
				</textField>
			</band>
			<band height="20">
				<printWhenExpression><![CDATA[$F{evolucaoProntuario}.getCodigo() != null]]></printWhenExpression>
				<rectangle radius="10">
					<reportElement x="0" y="1" width="555" height="15" uuid="782a804b-6da1-4665-bb5c-827a4d306c2b"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-76" mode="Transparent" x="2" y="2" width="555" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
					<box leftPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{tabelaCbo}.getTabelaCboGrupoAtendimento().getCodigo() != null)
?
    (Bundle.getStringApplication("rotulo_evolucoes")
        + " - " + $F{tabelaCbo}.getTabelaCboGrupoAtendimento().getDescricao())
:
    Bundle.getStringApplication("rotulo_evolucoes")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="46">
				<printWhenExpression><![CDATA[$F{atendimentoAlta}.getHistoricoResumido() != null]]></printWhenExpression>
				<rectangle radius="5">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="8" width="555" height="35" uuid="ca55f3db-7aa8-4a28-9d88-98e209900b30"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-76" mode="Opaque" x="22" y="1" width="100" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" uuid="589a941c-fb97-4ddc-87d9-af3732a6b325">
						<printWhenExpression><![CDATA[$F{atendimentoAlta} != null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Histórico Resumido*/
Bundle.getStringApplication("rotulo_historico_resumido")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement x="5" y="14" width="545" height="25" uuid="3bbcfbeb-bef6-4063-8c69-dc0df3afd10e"/>
					<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid"/>
						<leftPen lineWidth="0.0" lineStyle="Solid"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid"/>
						<rightPen lineWidth="0.0" lineStyle="Solid"/>
					</box>
					<textElement markup="html">
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{atendimentoAlta}.getHistoricoResumido()]]></textFieldExpression>
				</textField>
			</band>
                        <band height="46">
				<printWhenExpression><![CDATA[$F{atendimentoAlta}.getOrientacoesAlta() != null]]></printWhenExpression>
				<rectangle radius="5">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="7" width="555" height="35" uuid="53349c86-7a4d-4ecd-9a8c-8eb1b2b970ff"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement x="5" y="13" width="545" height="25" uuid="318c8dd7-df7a-4995-a3ba-91cfa22ed717"/>
					<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid"/>
						<leftPen lineWidth="0.0" lineStyle="Solid"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid"/>
						<rightPen lineWidth="0.0" lineStyle="Solid"/>
					</box>
					<textElement markup="html">
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{atendimentoAlta}.getOrientacoesAlta()]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-76" mode="Opaque" x="22" y="0" width="100" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" uuid="4093d94d-0f7a-4699-ad42-288d4e61e623">
						<printWhenExpression><![CDATA[$F{atendimentoAlta} != null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Orientações*/
Bundle.getStringApplication("rotulo_orientacoes_alta")]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<detail>
		<band height="81">
			<printWhenExpression><![CDATA[$F{evolucaoProntuario}.getCodigo() != null]]></printWhenExpression>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="330" y="14" width="224" height="12" uuid="74c6c044-ce5c-40ad-b767-b7bc8fd57a12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tabelaCbo}.getDescricaoFormatado()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="58" y="14" width="237" height="12" uuid="2b09f235-29e9-461b-883f-8cbf3cc660f7"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{profissional}.getDescricaoFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="303" y="2" width="27" height="12" uuid="171ba266-b680-480a-8635-2a9ef5b3d3fb"/>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Setor: */
Bundle.getStringApplication("rotulo_setor") + ": "]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH:mm" isBlankWhenNull="true">
				<reportElement x="58" y="2" width="85" height="12" uuid="63f0caac-17f5-4280-bec9-d07c46eb2ae5"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{evolucaoProntuario}.getDataHistorico()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="33" y="2" width="25" height="12" uuid="cb96124c-50ab-4215-8adc-1a7216777339"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Data: */
Bundle.getStringApplication("rotulo_data") + ": "]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="330" y="2" width="225" height="12" uuid="c224b5be-990b-4e3e-ac91-2aadb73840c4"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresa}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="303" y="14" width="27" height="12" uuid="a25a873c-bd67-4e42-8e87-97a84c634e47"/>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*CBO: */
Bundle.getStringApplication("rotulo_cbo") + ": "]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="0" y="26" width="555" height="49" uuid="fd36bced-0fc2-4739-b5ae-8899f57cc727"/>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement markup="html">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{evolucaoProntuario}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2" y="14" width="56" height="12" uuid="b3fe97c8-a3ab-462a-ba49-c8a5e032bae3"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Profissional: */
Bundle.getStringApplication("rotulo_profissional") + ": "]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<lastPageFooter>
		<band height="50">
			<line>
				<reportElement x="303" y="37" width="252" height="1" uuid="918e2070-2418-4498-8bf9-ecd0cffe9bb4"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-76" mode="Transparent" x="303" y="38" width="252" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="fd5d3777-3808-4e0d-846d-2d6014d299cf"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Profissional */
$F{profissional}.getNome()]]></textFieldExpression>
			</textField>
		</band>
	</lastPageFooter>
</jasperReport>
