package br.com.ksisolucoes.report.hospital.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.hospital.interfaces.dto.RelatorioLiberacaoLeitoDTO;
import br.com.ksisolucoes.report.hospital.interfaces.dto.RelatorioLiberacaoLeitoDTOParam;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioLiberacaoLeito extends CommandQuery<QueryRelatorioLiberacaoLeito> implements ITransferDataReport<RelatorioLiberacaoLeitoDTOParam, RelatorioLiberacaoLeitoDTO> {

    private RelatorioLiberacaoLeitoDTOParam param;
    private List<RelatorioLiberacaoLeitoDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("leitoLiberacao.codigo", "leitoLiberacao.codigo");
        hql.addToSelect("leitoLiberacao.tipoLiberacao", "leitoLiberacao.tipoLiberacao");
        hql.addToSelect("leitoLiberacao.motivo", "leitoLiberacao.motivo");
        hql.addToSelect("leitoLiberacao.dataLiberacao", "leitoLiberacao.dataLiberacao");

        hql.addToSelect("quartoInternacao.codigo", "quartoInternacao.codigo");
        hql.addToSelect("quartoInternacao.descricao", "quartoInternacao.descricao");

        hql.addToSelect("leitoQuarto.codigo", "leitoQuarto.codigo");
        hql.addToSelect("leitoQuarto.descricao", "leitoQuarto.descricao");

        hql.addToSelect("usuario.codigo", "usuario.codigo");
        hql.addToSelect("usuario.nome", "usuario.nome");

        hql.setTypeSelect(RelatorioLiberacaoLeitoDTO.class.getName());

        hql.addToFrom("LeitoLiberacao leitoLiberacao "
                + " left join leitoLiberacao.leitoQuarto leitoQuarto"
                + " left join leitoQuarto.quartoInternacao quartoInternacao"
                + " left join leitoLiberacao.usuario usuario");

        hql.addToWhereWhithAnd("quartoInternacao = ", this.param.getQuartoInternacao());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("leitoQuarto.descricao ", this.param.getLeito()));
        hql.addToWhereWhithAnd("usuario = ", this.param.getUsuario());
        hql.addToWhereWhithAnd("leitoLiberacao.dataLiberacao ", this.param.getPeriodo());
        
        hql.addToOrder("quartoInternacao.descricao");
        hql.addToOrder("leitoQuarto.descricao");

    }

    @Override
    public List<RelatorioLiberacaoLeitoDTO> getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List) result);
    }

    @Override
    public void setDTOParam(RelatorioLiberacaoLeitoDTOParam param) {
        this.param = param;
    }
}
