package br.com.ksisolucoes.report.vigilancia.query;

import br.com.celk.vigilancia.dto.RelatorioResumoAtividadeVeterinariaDTO;
import br.com.celk.vigilancia.dto.RelatorioResumoAtividadeVeterinariaDTOParam;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RegistroAtividadeVeterinaria;
import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioResumoAtividadeVeterinaria extends CommandQuery<QueryRelatorioResumoAtividadeVeterinaria> implements ITransferDataReport<RelatorioResumoAtividadeVeterinariaDTOParam, RelatorioResumoAtividadeVeterinariaDTO> {

    private RelatorioResumoAtividadeVeterinariaDTOParam param;
    private List<RelatorioResumoAtividadeVeterinariaDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {

        hql.setTypeSelect(RelatorioResumoAtividadeVeterinariaDTO.class.getName());

        hql.addToSelect("sum(coalesce(raa.quantidade,1))", "quantidade");

        if (!RelatorioResumoAtividadeVeterinariaDTOParam.FormaApresentacao.GERAL.equals(this.param.getFormaApresentacao())) {
            if (RelatorioResumoAtividadeVeterinariaDTOParam.FormaApresentacao.ESPECIE_ANIMAL.equals(this.param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("ea.descricao", "registroAtividadeAnimal.especieAnimal.descricao");
                hql.addToWhereWhithAnd("ea.descricao is not null");
            } else if (RelatorioResumoAtividadeVeterinariaDTOParam.FormaApresentacao.PROFISSIONAL.equals(this.param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("p.nome", "registroAtividadeVeterinaria.profissional.nome");
            } else if (RelatorioResumoAtividadeVeterinariaDTOParam.FormaApresentacao.TIPO_ATIVIDADE.equals(this.param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("at.descricao", "registroAtividadeVeterinaria.atividadeVeterinaria.descricao");
            } else if (RelatorioResumoAtividadeVeterinariaDTOParam.FormaApresentacao.SEXO.equals(this.param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("raa.sexo", "registroAtividadeAnimal.sexo");
                hql.addToWhereWhithAnd("raa.sexo is not null");
            }
        }
        
        if(CollectionUtils.isNotNullEmpty(param.getTipoResumo())){
            if (param.getTipoResumo().contains(RelatorioResumoAtividadeVeterinariaDTOParam.TipoResumo.TIPO_ATIVIDADE.value())) {
                hql.addToSelectAndGroup("at.descricao", "registroAtividadeVeterinaria.atividadeVeterinaria.descricao");
            }
            if (param.getTipoResumo().contains(RelatorioResumoAtividadeVeterinariaDTOParam.TipoResumo.PROFISSIONAL.value())) {
                hql.addToSelectAndGroup("p.nome", "registroAtividadeVeterinaria.profissional.nome");
            }
            if (param.getTipoResumo().contains(RelatorioResumoAtividadeVeterinariaDTOParam.TipoResumo.ESPECIE_ANIMAL.value())) {
                hql.addToSelectAndGroup("ea.descricao", "registroAtividadeAnimal.especieAnimal.descricao");
                hql.addToWhereWhithAnd("ea.descricao is not null");
            }
            if (param.getTipoResumo().contains(RelatorioResumoAtividadeVeterinariaDTOParam.TipoResumo.SEXO.value())) {
                hql.addToSelectAndGroup("raa.sexo", "registroAtividadeAnimal.sexo");
                hql.addToWhereWhithAnd("raa.sexo is not null");
            }
        }

        hql.addToWhereWhithAnd("at = ", param.getAtividadeVeterinaria());
        hql.addToWhereWhithAnd("ea = ", param.getEspecieAnimal());
        hql.addToWhereWhithAnd("rav.dataAtividade ", param.getPeriodo());
        if (!RelatorioResumoAtividadeVeterinariaDTOParam.Sexo.AMBOS.value().equals(param.getSexo())) {
            hql.addToWhereWhithAnd("raa.sexo = ", param.getSexo());
        }
        hql.addToWhereWhithAnd("rav.empresa = ", param.getUnidade());
        
        hql.addToWhereWhithAnd("rav.status <> ", RegistroAtividadeVeterinaria.Status.CANCELADO.value());

        hql.addToFrom("RegistroAtividadeAnimal raa "
                + " right join raa.registroAtividadeVeterinaria rav"
                + " left join rav.atividadeVeterinaria at"
                + " left join rav.profissional p"
                + " left join raa.especieAnimal ea");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List) result);
    }

    @Override
    public Collection getResult() {
        return result;
    }

    @Override
    public void setDTOParam(RelatorioResumoAtividadeVeterinariaDTOParam param) {
        this.param = param;
    }
}
