/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.agendamento.exame;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioImpressaoComprovanteEntregaExameDTOParam;
import br.com.ksisolucoes.report.agendamento.exame.query.QueryRelatorioImprimirComprovanteEntregaExame;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Parametro;
import java.util.Collection;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoComprovanteEntregaExame extends AbstractReport<RelatorioImpressaoComprovanteEntregaExameDTOParam> {

    public RelatorioImpressaoComprovanteEntregaExame(RelatorioImpressaoComprovanteEntregaExameDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/agendamento/exame/jrxml/relatorio_impressao_comprovante_entrega_exame.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_comprovante_entrega_exame_agendamento");
    }

    @Override
    public Collection getCollection() throws DAOException, ValidacaoException {
        Parametro parametro = CargaBasicoPadrao.getInstance().getParametroPadrao();

        this.addParametro("nomePrefeitura", parametro.getPropertyValue(Parametro.PROP_NOME_PREFEITURA));
        this.addParametro("descricaoEmpresa", SessaoAplicacaoImp.getInstance().<Empresa>getEmpresa().getDescricao());
        this.addParametro("caminhoLogo", br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp.getInstance().getReportSessaoAplicacao().getCaminhoImagemPadrao());

        QueryRelatorioImprimirComprovanteEntregaExame qriec = new QueryRelatorioImprimirComprovanteEntregaExame();
        qriec.setDTOParam(getParam());
        qriec.start();

        return qriec.getResult();
    }

}
