<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_hiperdia" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="488f6ce4-6527-4989-a141-e9cca28251db">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.771561000000001"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="138"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="br.com.ksisolucoes.vo.cadsus.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="caminhoLogo" class="java.lang.String" isForPrompting="false"/>
	<field name="usuarioCadSus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="codigo" class="java.lang.Long"/>
	<field name="tratamentoMedicamentoso" class="java.lang.String">
		<fieldDescription><![CDATA[tratamentoMedicamentoso]]></fieldDescription>
	</field>
	<field name="tratamentoHidroclorotiazida" class="java.lang.Long">
		<fieldDescription><![CDATA[tratamentoHidroclorotiazida]]></fieldDescription>
	</field>
	<field name="tratamentoInsulina" class="java.lang.Long">
		<fieldDescription><![CDATA[tratamentoInsulina]]></fieldDescription>
	</field>
	<field name="tratamentoPropranolol" class="java.lang.Long">
		<fieldDescription><![CDATA[tratamentoPropranolol]]></fieldDescription>
	</field>
	<field name="tratamentoCaptopril" class="java.lang.Long">
		<fieldDescription><![CDATA[tratamentoCaptopril]]></fieldDescription>
	</field>
	<field name="tratamentoGlibenclamida" class="java.lang.Long">
		<fieldDescription><![CDATA[tratamentoGlibenclamida]]></fieldDescription>
	</field>
	<field name="tratamentoMetformina" class="java.lang.Long">
		<fieldDescription><![CDATA[tratamentoMetformina]]></fieldDescription>
	</field>
	<field name="tratamentoOutros" class="java.lang.String">
		<fieldDescription><![CDATA[tratamentoOutros]]></fieldDescription>
	</field>
	<field name="pressaoSistolica" class="java.lang.String">
		<fieldDescription><![CDATA[pressaoSistolica]]></fieldDescription>
	</field>
	<field name="pressaoDiastolica" class="java.lang.String">
		<fieldDescription><![CDATA[pressaoDiastolica]]></fieldDescription>
	</field>
	<field name="cintura" class="java.lang.Long">
		<fieldDescription><![CDATA[cintura]]></fieldDescription>
	</field>
	<field name="peso" class="java.lang.Double">
		<fieldDescription><![CDATA[peso]]></fieldDescription>
	</field>
	<field name="altura" class="java.lang.Double">
		<fieldDescription><![CDATA[altura]]></fieldDescription>
	</field>
	<field name="glicemiaCapilar" class="java.lang.Long">
		<fieldDescription><![CDATA[glicemiaCapilar]]></fieldDescription>
	</field>
	<field name="emJejum" class="java.lang.String">
		<fieldDescription><![CDATA[emJejum]]></fieldDescription>
	</field>
	<field name="posPrandial" class="java.lang.String">
		<fieldDescription><![CDATA[posPrandial]]></fieldDescription>
	</field>
	<field name="antecedentesFamiliaresCardiovasculares" class="java.lang.String">
		<fieldDescription><![CDATA[antecedentesFamiliaresCardiovasculares]]></fieldDescription>
	</field>
	<field name="diabetesTipo1" class="java.lang.String">
		<fieldDescription><![CDATA[diabetesTipo1]]></fieldDescription>
	</field>
	<field name="diabetesTipo2" class="java.lang.String">
		<fieldDescription><![CDATA[diabetesTipo2]]></fieldDescription>
	</field>
	<field name="tabagismo" class="java.lang.String">
		<fieldDescription><![CDATA[tabagismo]]></fieldDescription>
	</field>
	<field name="sobrepesoObesidade" class="java.lang.String">
		<fieldDescription><![CDATA[sobrepesoObesidade]]></fieldDescription>
	</field>
	<field name="sedentarismo" class="java.lang.String">
		<fieldDescription><![CDATA[sedentarismo]]></fieldDescription>
	</field>
	<field name="hipertensaoArterial" class="java.lang.String">
		<fieldDescription><![CDATA[hipertensaoArterial]]></fieldDescription>
	</field>
	<field name="infartoAgudoMiocardio" class="java.lang.String">
		<fieldDescription><![CDATA[infartoAgudoMiocardio]]></fieldDescription>
	</field>
	<field name="outrasCoronariopatias" class="java.lang.String">
		<fieldDescription><![CDATA[outrasCoronariopatias]]></fieldDescription>
	</field>
	<field name="avc" class="java.lang.String">
		<fieldDescription><![CDATA[avc]]></fieldDescription>
	</field>
	<field name="peDiabetico" class="java.lang.String">
		<fieldDescription><![CDATA[peDiabetico]]></fieldDescription>
	</field>
	<field name="amputacaoDiabete" class="java.lang.String">
		<fieldDescription><![CDATA[amputacaoDiabete]]></fieldDescription>
	</field>
	<field name="doencaRenal" class="java.lang.String">
		<fieldDescription><![CDATA[doencaRenal]]></fieldDescription>
	</field>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="698" splitType="Stretch">
			<rectangle>
				<reportElement key="rectangle-4" x="16" y="544" width="211" height="82" uuid="356b187f-7f2d-4219-ba8d-40be92135836"/>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-2" x="6" y="93" width="342" height="10" uuid="887d9e5d-2c65-4f16-91f8-260072b34766"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_nome_unidade_saude")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-3" x="447" y="93" width="83" height="10" uuid="75e03986-1ce3-4150-9174-2aa22a50880d"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_numero_prontuario")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-5" x="6" y="103" width="342" height="10" uuid="d8820767-a5c8-41e6-a520-32fa3560958e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-6" x="447" y="103" width="83" height="10" uuid="ba1d1af6-f9f1-475e-985a-b940d7fed06f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{codigo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-13" x="6" y="117" width="523" height="12" uuid="09d44451-7862-4859-ab44-67beb52bdfd9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_identificacao_usuario")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-14" x="6" y="135" width="150" height="10" uuid="e51c1a93-587f-40d7-a109-dc8febfd0357"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_nome")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-15" x="6" y="145" width="373" height="10" uuid="0fecdf29-8e94-4ba8-a23a-cb4b7f5ab9b7"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadSus}.getNome() + " (" + $F{usuarioCadSus}.getCodigo() + ")"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-16" x="6" y="155" width="150" height="10" uuid="fb501644-9571-423a-85b0-8fd3d44d4acf"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_nome_completo_mae")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-17" x="6" y="165" width="236" height="10" uuid="fa0e6bfe-9a97-44cf-a565-c33c1090df76"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadSus}.getNomeMae()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-18" x="249" y="155" width="150" height="10" uuid="9c444fae-893d-40e7-9c08-eb090dab8213"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_nome_completo_pai")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-19" x="249" y="165" width="258" height="10" uuid="f88c46f0-fabc-4750-9b1b-5c19f5a0968f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadSus}.getNomePai()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-20" x="6" y="175" width="109" height="10" uuid="5561ada6-aa1c-429f-b6f4-4e8a1684e4a0"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_identidade")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-23" x="6" y="185" width="109" height="10" uuid="0c5082b9-2cbe-4722-83a5-eeef124e6c97"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadSus}.getRg()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-24" x="121" y="175" width="122" height="10" uuid="548d3769-f30b-46d3-9135-67e868957546"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_orgao_emissor")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-25" x="121" y="185" width="122" height="10" uuid="61cd1ff1-3cca-4682-9d16-56ab95951d69"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-26" x="248" y="175" width="48" height="10" uuid="aa3718b3-753e-4b27-b106-6fce2038b09a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_uf")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-27" x="248" y="185" width="48" height="10" uuid="0bf442b0-e745-4c33-81d5-da0e90720542"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-28" x="302" y="175" width="227" height="10" uuid="660e8064-c73c-40b0-a055-104387d00eb6"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_cnpj_cpf")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-29" x="302" y="185" width="227" height="10" uuid="f044b7e3-d113-4b4a-9c06-4ec105c916be"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadSus}.getCpf()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-30" x="6" y="195" width="109" height="10" uuid="762cf3e8-4a2d-49fa-a348-2248f83cfdca"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_data_nascimento")]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement key="textField-31" x="6" y="205" width="109" height="10" uuid="6c99a3b3-9149-4806-bdf6-2fbf923d2845"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadSus}.getDataNascimento()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-32" x="121" y="195" width="64" height="10" uuid="a506e2cc-c695-44f9-9a75-7b71b4d00653"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_idade")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-33" x="121" y="205" width="64" height="10" uuid="6419688e-af57-4ddc-9f4e-15234e49be92"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadSus}.getIdade()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-34" x="6" y="216" width="251" height="10" uuid="af5d013e-5e6f-4b68-987d-ecd17d835e05"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_dados_residenciais")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-35" x="6" y="226" width="251" height="10" uuid="d9ebc985-04c8-46cb-abfe-858bffb25eb6"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_logradouro")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-36" x="6" y="236" width="524" height="10" uuid="fcf2deeb-5fd0-4beb-be5a-f186e590a567"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadSus}.getEnderecoDomicilio().getEnderecoUsuarioCadsus().getLogradouro()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-37" x="6" y="246" width="124" height="10" uuid="47f6587a-5774-4609-afbd-483487078b27"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_numero")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-38" x="6" y="256" width="124" height="10" uuid="ca771027-d56d-4fbc-8d4c-2ecbf5184d1d"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadSus}.getEnderecoDomicilio().getEnderecoUsuarioCadsus().getNumeroLogradouro()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-39" x="132" y="246" width="398" height="10" uuid="3ed31f84-314a-4ef2-9a7f-4c0eda122128"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_complemento")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-40" x="132" y="256" width="398" height="10" uuid="4337c935-a068-42ac-9e65-dd4477671f90"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadSus}.getEnderecoDomicilio().getEnderecoUsuarioCadsus().getComplementoLogradouro()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-41" x="6" y="266" width="124" height="10" uuid="0d9308fe-43b6-4b18-add3-bb2d2b3a829b"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_municipio")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-42" x="132" y="266" width="350" height="10" uuid="dcda70ca-5dad-4059-af7d-749d947f5a46"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_bairro")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-43" x="488" y="266" width="42" height="10" uuid="f08fb397-c7ed-41f8-a504-bf71d6632c77"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_uf")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-44" x="6" y="276" width="124" height="10" uuid="8ed5eed6-46f4-42c6-bfa0-c2a5b91d435a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadSus}.getEnderecoDomicilio().getEnderecoUsuarioCadsus().getCidade().getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-45" x="132" y="276" width="350" height="10" uuid="5df68a3d-ec33-44b2-9b4d-425b89b1b311"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadSus}.getEnderecoDomicilio().getEnderecoUsuarioCadsus().getBairro()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-46" x="488" y="276" width="42" height="10" uuid="b90885da-f364-42f2-8c44-24b0fc89e1ff"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-47" x="6" y="286" width="124" height="10" uuid="e9a91419-5abf-47d0-80db-16042f01f26f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_cep")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-49" x="190" y="286" width="124" height="10" uuid="5e451fb9-d821-4fdd-ba5d-b7896a125749"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_telefone")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-50" x="6" y="296" width="124" height="10" uuid="3528f4fc-5426-45a2-89d8-7d6655bd3b78"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadSus}.getEnderecoDomicilio().getEnderecoUsuarioCadsus().getCep()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-52" x="190" y="296" width="124" height="10" uuid="64a52fc2-1d18-4080-b15c-7a81a8207df1"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadSus}.getEnderecoDomicilio().getEnderecoUsuarioCadsus().getTelefone()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-53" x="6" y="306" width="179" height="10" uuid="6ffadba8-50fc-4b61-95e0-1cbd793669e1"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_ponto_referencia")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-54" x="6" y="316" width="523" height="10" uuid="0133c0d5-7d27-4b8a-b99f-d0e585ffbbc5"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-56" x="55" y="326" width="124" height="11" uuid="8b7c1102-c11a-4011-a6ea-62666f04302c"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-57" x="6" y="509" width="524" height="12" uuid="a74a5582-a0ea-4ea5-8dec-f8c2572a646c"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_tratamento")]]></textFieldExpression>
			</textField>
			<image>
				<reportElement key="image-1" x="16" y="12" width="70" height="70" uuid="cda86220-6e93-4fb6-acd7-1e52fedd72e9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<imageExpression><![CDATA[$P{caminhoLogo}]]></imageExpression>
			</image>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-85" x="91" y="21" width="296" height="14" uuid="bb6dfbf4-fb1c-4227-acd9-995fa6492c6f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_ms_hiperdia")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-1" x="92" y="36" width="295" height="24" uuid="c9f36f63-ab86-499a-bfc1-f9077e28f48b"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<text><![CDATA[PLANO DE REORGANIZAÇÃO DA ATENÇÃO À HIPERTENSÃO ARTERIAL E AO DIABETES MELLITUS]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-3" x="387" y="21" width="143" height="14" uuid="02b6add1-2fe4-46c0-8fee-7277710b806d"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" isUnderline="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[1ª Via: Enviar para digitação]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-4" x="387" y="36" width="143" height="24" uuid="899223db-8898-41f2-9466-06f2ce52d1d7"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[CADASTRO DO HIPERTENSO E/OU DIABÉTICO]]></text>
			</staticText>
			<line>
				<reportElement key="line-1" x="6" y="131" width="524" height="1" uuid="1bf622be-f082-4711-b4ec-d7d798cc24e2"/>
			</line>
			<line>
				<reportElement key="line-2" x="6" y="522" width="524" height="1" uuid="8dcd47ad-5a9c-46fa-ad9a-c783257a30b3"/>
			</line>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-86" x="5" y="326" width="49" height="11" uuid="d3a23fa5-907a-4fd4-8b0c-6f44e496e773"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_escolaridade")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-87" x="348" y="93" width="83" height="10" uuid="fa7c2946-5487-4360-8d0f-9b8107927dc9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_codigo_sia_sus")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-88" x="348" y="103" width="83" height="10" uuid="219f0586-34f8-4417-b938-efd4e8caf084"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-89" x="387" y="135" width="84" height="10" uuid="625136fb-38e3-4807-a365-90fe3531eaf4"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_data_nascimento")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-90" x="387" y="145" width="84" height="10" uuid="52d10e07-a0ae-4524-928f-5d6986107f27"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.formatar($F{usuarioCadSus}.getDataNascimento())]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-91" x="477" y="135" width="29" height="10" uuid="df5582b8-5a94-4cd2-adb3-bcffa610fd62"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_sexo")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-92" x="477" y="145" width="29" height="10" uuid="a40fd4a9-73cf-4519-bde6-8afd210b802e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadSus}.getSexo()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-93" x="17" y="528" width="115" height="10" uuid="3ba752e0-cacd-4604-b020-3fbce40afadb"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_nao_medicamentoso")]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement key="rectangle-1" x="139" y="527" width="12" height="12" uuid="c56b150d-8342-4211-9b5d-97da55958705"/>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-94" x="139" y="527" width="12" height="12" uuid="b75bf48b-d3e9-41fa-9dca-8f845ac01f6e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.NAO.equals($F{tratamentoMedicamentoso}) ?
"X" : null]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-95" x="6" y="350" width="524" height="12" uuid="d30cc142-24a1-47ef-b6d4-93629ea6066f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_dados_clinicos_paciente")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="line-3" x="6" y="363" width="524" height="1" uuid="867db2d4-8a89-4f8c-bdca-a7e77b7bf913"/>
			</line>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-96" x="6" y="366" width="106" height="11" uuid="08ce88ae-9211-4e53-829c-678ac2740385"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_pressao_arterial_sistolica") + ": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-97" x="114" y="366" width="41" height="11" uuid="85eaa1c0-a231-48f6-a26e-c5e3c0e41baa"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{pressaoSistolica}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-98" x="156" y="366" width="111" height="11" uuid="22410bb7-f35a-430c-8f77-a5f1c8534c35"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_pressao_arterial_diastolica") + ": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-99" x="269" y="366" width="41" height="11" uuid="0df0a36a-9e83-4f79-8c85-a06d3863b6fa"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{pressaoDiastolica}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-100" x="318" y="366" width="67" height="11" uuid="77aa0d18-752e-4abd-8a03-408e63b7fc14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_cintura_cm") + ": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-101" x="387" y="366" width="39" height="11" uuid="522d5074-46ee-41a1-a0c5-1bb2f15a9d38"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cintura}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-102" x="433" y="366" width="56" height="11" uuid="99e70356-bbeb-4279-8969-e73a081cbb04"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_peso_kg") + ": "]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-103" x="491" y="366" width="34" height="11" uuid="65666ced-0e64-4d57-87bd-317cc9b1a2bf"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{peso}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-104" x="16" y="381" width="60" height="11" uuid="985b5406-cfe9-4767-b4b6-402069f16f1b"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_altura_cm") + ": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-105" x="78" y="381" width="41" height="11" uuid="681daeee-effb-4162-930a-49c7e76b947d"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{altura}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-106" x="144" y="381" width="71" height="11" uuid="5881bdc7-7757-4155-a359-4baed9af8517"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_glicemia_capilar_mg_d") + ": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-107" x="217" y="381" width="47" height="11" uuid="3709b443-b671-4bb1-b7ec-9b03cd4fc1a7"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{glicemiaCapilar}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-108" x="309" y="381" width="90" height="10" uuid="8f19f10b-5062-4dcf-9fd4-c3643af8c489"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_em_jejum")]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement key="rectangle-2" x="292" y="380" width="12" height="12" uuid="2dcd0c2d-6192-4c9b-b18e-cae204b3edb0"/>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-109" x="292" y="380" width="12" height="12" uuid="926bb42f-20c2-4136-a962-e053e9ecf4d5"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM.equals($F{emJejum}) ?
"X" : null]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-110" x="431" y="381" width="90" height="10" uuid="d8e81cef-cf08-4578-8b85-58300ab2f9ea"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_pos_prandial")]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement key="rectangle-3" x="414" y="380" width="12" height="12" uuid="0e0822df-e040-4c8f-9d49-03dd40ba4001"/>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-111" x="414" y="380" width="12" height="12" uuid="b955ef1c-da64-4cca-bb75-08b0b03239f9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM.equals($F{posPrandial}) ?
"X" : null]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-112" x="16" y="423" width="174" height="11" uuid="36dbc06b-9ca7-4539-bf5e-4b0e42e542a8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_antecendentes_familiares_cardiovasculares")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-113" x="196" y="423" width="25" height="11" uuid="95a17438-1163-452a-b152-2e7a1df15e9c"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM.equals($F{antecedentesFamiliaresCardiovasculares}) ?
Bundle.getStringApplication("rotulo_sim") :
RepositoryComponentDefault.NAO.equals($F{antecedentesFamiliaresCardiovasculares}) ?
Bundle.getStringApplication("rotulo_nao") :
null]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-114" x="16" y="434" width="174" height="11" uuid="d14b9257-d057-41eb-8257-777417e1ff4b"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_diabetes_tipo1")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-115" x="196" y="434" width="25" height="11" uuid="d4e2402d-0522-4366-b302-ae02db70d6fd"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM.equals($F{diabetesTipo1}) ?
Bundle.getStringApplication("rotulo_sim") :
RepositoryComponentDefault.NAO.equals($F{diabetesTipo1}) ?
Bundle.getStringApplication("rotulo_nao") :
null]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-116" x="16" y="445" width="174" height="11" uuid="a915530b-d41e-4048-8033-f9b6fb8d1b4c"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_diabetes_tipo2")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-117" x="196" y="445" width="25" height="11" uuid="0bf5230a-3297-4d05-948d-f2b108759b8e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM.equals($F{diabetesTipo2}) ?
Bundle.getStringApplication("rotulo_sim") :
RepositoryComponentDefault.NAO.equals($F{diabetesTipo2}) ?
Bundle.getStringApplication("rotulo_nao") :
null]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-118" x="16" y="456" width="174" height="11" uuid="729afcd7-5ab9-4bd1-8142-f4fb713bf798"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_tabagismo")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-119" x="196" y="456" width="25" height="11" uuid="c64baefd-4091-49ea-be5d-95cdf9a21762"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM.equals($F{tabagismo}) ?
Bundle.getStringApplication("rotulo_sim") :
RepositoryComponentDefault.NAO.equals($F{tabagismo}) ?
Bundle.getStringApplication("rotulo_nao") :
null]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-120" x="16" y="467" width="174" height="11" uuid="4ef09405-e741-48e2-b057-439df1c879ff"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_sedentarismo")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-121" x="196" y="467" width="25" height="11" uuid="fed980cc-66b8-4b41-8d2e-e152a67bec77"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM.equals($F{sedentarismo}) ?
Bundle.getStringApplication("rotulo_sim") :
RepositoryComponentDefault.NAO.equals($F{sedentarismo}) ?
Bundle.getStringApplication("rotulo_nao") :
null]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-122" x="16" y="478" width="174" height="11" uuid="7e2df2be-a10b-48ff-b984-806134309cb4"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_sobrepeso_obesidade")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-123" x="196" y="478" width="25" height="11" uuid="0b335670-cda1-4946-ba68-abe621baf23e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM.equals($F{sobrepesoObesidade}) ?
Bundle.getStringApplication("rotulo_sim") :
RepositoryComponentDefault.NAO.equals($F{sobrepesoObesidade}) ?
Bundle.getStringApplication("rotulo_nao") :
null]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-124" x="16" y="489" width="174" height="11" uuid="5a48e8df-afeb-4e41-b6b6-c2b989d915a8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_hipertensao_arterial")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-125" x="196" y="489" width="25" height="11" uuid="7b4036ab-966d-4462-a97b-5c4bf77cecf0"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM.equals($F{hipertensaoArterial}) ?
Bundle.getStringApplication("rotulo_sim") :
RepositoryComponentDefault.NAO.equals($F{hipertensaoArterial}) ?
Bundle.getStringApplication("rotulo_nao") :
null]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-126" x="237" y="423" width="172" height="11" uuid="51a643f5-1e29-42dc-b810-272531a46c8e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_infarto_agudo_miocardio")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-127" x="415" y="423" width="25" height="11" uuid="e9652a3c-eb52-4673-8b0a-7be7f8df758b"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM.equals($F{infartoAgudoMiocardio}) ?
Bundle.getStringApplication("rotulo_sim") :
RepositoryComponentDefault.NAO.equals($F{infartoAgudoMiocardio}) ?
Bundle.getStringApplication("rotulo_nao") :
null]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-128" x="237" y="434" width="172" height="11" uuid="08dc6d2b-db31-4160-9205-71bad454a882"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_outras_coronariopatias")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-129" x="415" y="434" width="25" height="11" uuid="81f50ec5-549d-4fae-af55-ae47781f54b2"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM.equals($F{outrasCoronariopatias}) ?
Bundle.getStringApplication("rotulo_sim") :
RepositoryComponentDefault.NAO.equals($F{outrasCoronariopatias}) ?
Bundle.getStringApplication("rotulo_nao") :
null]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-130" x="237" y="445" width="172" height="11" uuid="ed90c3f6-c82a-4418-b0dc-4162a65c18de"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_avc")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" x="415" y="445" width="25" height="11" uuid="7f7b0a7f-343a-4bc2-9645-f6a042bd59ea"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM.equals($F{avc}) ?
Bundle.getStringApplication("rotulo_sim") :
RepositoryComponentDefault.NAO.equals($F{avc}) ?
Bundle.getStringApplication("rotulo_nao") :
null]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-132" x="237" y="456" width="172" height="11" uuid="fe799dfb-4f7f-472b-bd6e-6f05b4a36185"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_pe_diabetico")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-133" x="415" y="456" width="25" height="11" uuid="4ef1d39f-4969-4d15-9f31-090ee44e027c"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM.equals($F{peDiabetico}) ?
Bundle.getStringApplication("rotulo_sim") :
RepositoryComponentDefault.NAO.equals($F{peDiabetico}) ?
Bundle.getStringApplication("rotulo_nao") :
null]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-134" x="237" y="467" width="172" height="11" uuid="7f48cf0d-57e3-4229-86bd-c240a48a4332"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_amputacao_por_diabetes")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-135" x="415" y="467" width="25" height="11" uuid="c26e5d91-68f6-40aa-961e-86698dd33cff"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM.equals($F{amputacaoDiabete}) ?
Bundle.getStringApplication("rotulo_sim") :
RepositoryComponentDefault.NAO.equals($F{amputacaoDiabete}) ?
Bundle.getStringApplication("rotulo_nao") :
null]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-136" x="237" y="478" width="172" height="11" uuid="5e716263-1d2d-479e-bd51-e6cbd9146f18"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_doenca_renal")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-137" x="415" y="478" width="25" height="11" uuid="1cd57ba4-36f2-4415-b365-120444158d65"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM.equals($F{doencaRenal}) ?
Bundle.getStringApplication("rotulo_sim") :
RepositoryComponentDefault.NAO.equals($F{doencaRenal}) ?
Bundle.getStringApplication("rotulo_nao") :
null]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-138" x="16" y="545" width="211" height="10" uuid="9f5c4d70-80d4-472f-a873-4ab800d543e8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_medicamentoso")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-139" x="68" y="558" width="53" height="10" uuid="46b315ac-e770-4be1-b066-09c8ae033f95"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_tipo")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-140" x="137" y="558" width="65" height="10" uuid="c29c3640-7e60-4802-af7a-2a9791dcee6d"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_comprimidos_dia")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-141" x="16" y="571" width="114" height="10" uuid="b1a627c7-4162-43a9-b870-5c210333a8a4"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_hidroclorotiazida_25_mg")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-142" x="16" y="582" width="114" height="10" uuid="4d0dfcf7-f9ec-4b47-b133-275154b8f1f1"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_propanolol_40_mg")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-143" x="16" y="593" width="114" height="10" uuid="d50766dc-6588-47bd-8060-9e10bd7a4351"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_captopril_25_mg")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-144" x="16" y="604" width="114" height="10" uuid="a4b684b9-5d80-4a00-8517-7ebdedd73b69"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_glibenclamida_5_mg")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-145" x="16" y="615" width="114" height="10" uuid="5f6e72f0-d1d0-4cca-bbd4-7ae8a1806c85"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_metformina_850_mg")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-146" x="150" y="571" width="36" height="10" uuid="b11bcf8e-3019-4ade-8745-9b5269f7f51e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[10L == $F{tratamentoHidroclorotiazida} ?
"1/2" : Coalesce.asString($F{tratamentoHidroclorotiazida})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-147" x="150" y="582" width="36" height="10" uuid="be598ae2-2a1e-47d4-9528-efd295d26037"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[10L == $F{tratamentoPropranolol} ?
"1/2" : Coalesce.asString($F{tratamentoPropranolol})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-148" x="150" y="593" width="36" height="10" uuid="765e2ca9-c7da-4517-84e1-21c0a44d5a4f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[10L == $F{tratamentoCaptopril} ?
"1/2" : Coalesce.asString($F{tratamentoCaptopril})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-149" x="150" y="604" width="36" height="10" uuid="d4789132-4f63-4f7d-b6f5-45bd1e5b7673"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[10L == $F{tratamentoGlibenclamida} ?
"1/2" : Coalesce.asString($F{tratamentoGlibenclamida})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-150" x="150" y="615" width="36" height="10" uuid="cc70aeed-5742-4b19-b1a6-03a0eb10b614"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[10L == $F{tratamentoMetformina} ?
"1/2" : Coalesce.asString($F{tratamentoMetformina})]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="line-4" x="17" y="569" width="211" height="1" uuid="a1c0860c-1123-4c6f-b066-973d28530d99"/>
			</line>
			<line>
				<reportElement key="line-5" x="17" y="556" width="211" height="1" uuid="9f2d3379-a233-4d05-986c-bbd01db3b75c"/>
			</line>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-151" x="17" y="630" width="81" height="11" uuid="c0e58197-e762-4937-b7ef-1f01f595d6c0"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_outros")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-152" x="101" y="630" width="25" height="11" uuid="eae14dd7-02af-4f0f-9bb7-406613d34626"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM.equals($F{tratamentoOutros}) ?
Bundle.getStringApplication("rotulo_sim") :
RepositoryComponentDefault.NAO.equals($F{tratamentoOutros}) ?
Bundle.getStringApplication("rotulo_nao") :
null]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-153" x="17" y="655" width="81" height="11" uuid="5945e43b-26f2-4a93-8540-ce6e28196f7a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_data_consulta")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-155" x="118" y="655" width="375" height="11" uuid="4b25250f-99e3-4bcd-b8d8-e1e5da30a93a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_assinatura_responsavel_atendimento")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-156" x="118" y="672" width="375" height="11" uuid="a60dc6c7-1200-4282-9246-efda0f8620cb"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-5" x="16" y="672" width="85" height="12" uuid="b0d3e409-95eb-4625-b39f-cf1672acbd59"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font isUnderline="true"/>
				</textElement>
				<text><![CDATA[     /     /     ]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-157" x="27" y="406" width="174" height="11" uuid="513ec918-e544-4fd2-abd7-e4fb7488eb66"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_fatores_risco_doencas_concomitantes")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-158" x="258" y="406" width="174" height="11" uuid="c3a16b0e-860f-4693-8ced-10cdb4df2cf9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_presenca_de_complicacoes")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="line-6" x="17" y="419" width="424" height="1" uuid="acb578e2-e95f-407e-8ccc-40567a068afd"/>
			</line>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-159" x="262" y="572" width="81" height="11" uuid="46ccc72a-8e7d-4d3d-9c95-a7e26c7480b2"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_unidades_dia")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-160" x="239" y="592" width="63" height="11" uuid="59dca873-dc17-4992-9971-e11135eb11b1"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_insulina") + ": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-161" x="306" y="592" width="63" height="11" uuid="19bfd939-2faf-4108-b774-ae09f750d3d8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tratamentoInsulina}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
