<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_resumo_exames" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="64961734-45a0-404b-adee-da03307f940f">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="5.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.celk.util.CollectionUtils"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupo"/>
	<import value="br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendIndividualItemProcedimento"/>
	<import value="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<import value="br.com.ksisolucoes.vo.basico.Empresa"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<subDataset name="examesEsus" uuid="5c83b35b-6771-41e6-b9e8-dc5f5fbc6e10">
		<field name="quantidadeAvaliado" class="java.lang.Long"/>
		<field name="quantidadeSolicitado" class="java.lang.Long"/>
		<field name="descricaoExame" class="java.lang.String"/>
	</subDataset>
	<field name="examesEsusList" class="java.util.List"/>
	<field name="outrosExamesList" class="java.util.List"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<detail>
		<band height="39" splitType="Stretch">
			<componentElement>
				<reportElement x="0" y="25" width="535" height="14" uuid="98a62ba6-80e4-4865-b7b0-c921b69046c8"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="examesEsus" uuid="05b206b2-6dc3-4600-9148-1dae11fe7c79">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{examesEsusList})]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="14" width="535">
						<textField>
							<reportElement x="0" y="1" width="438" height="12" uuid="7380ec15-f5fb-4e98-a32b-fd629aa2cf26"/>
							<textElement>
								<font fontName="Arial" size="9"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{descricaoExame}]]></textFieldExpression>
						</textField>
						<textField>
							<reportElement x="488" y="1" width="45" height="12" uuid="37d8dca1-7842-4153-b65c-60cd2212e0b7"/>
							<textElement textAlignment="Right"/>
							<textFieldExpression><![CDATA[$F{quantidadeAvaliado}]]></textFieldExpression>
						</textField>
						<textField>
							<reportElement x="438" y="1" width="45" height="12" uuid="0745a523-0acc-4c04-93e3-b7a6b95296d1"/>
							<textElement textAlignment="Right"/>
							<textFieldExpression><![CDATA[$F{quantidadeSolicitado}]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="438" y="12" width="45" height="12" uuid="4f80e38f-9805-41fc-a5a6-fd0813c5e4cb"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_solicitado")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="23" width="535" height="1" uuid="2c464c62-fcd1-4b32-a3e6-e819c29c150e"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="0" y="12" width="438" height="12" uuid="f5e0f52b-9158-4c5d-bf1d-cd612b95300e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_descricao")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="488" y="12" width="45" height="12" uuid="a22f7c55-8fb4-4e14-9a8c-5788fc508cc9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_avaliado")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="0" y="0" width="129" height="12" uuid="53ad0dc2-0417-4291-9bf5-26285d7da53b"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_exames")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="12" width="535" height="1" uuid="9adbf34a-2265-4f15-b963-91852b190101"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
		</band>
		<band height="50">
			<printWhenExpression><![CDATA[$F{outrosExamesList} != null && !$F{outrosExamesList}.isEmpty()]]></printWhenExpression>
			<componentElement>
				<reportElement x="0" y="35" width="535" height="14" uuid="e23ff65a-eb0a-420a-9ea4-82acc139f2f2"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="examesEsus" uuid="5dac4c33-b8d3-4146-9134-a04568b881ef">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{outrosExamesList})]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="14" width="535">
						<textField>
							<reportElement x="0" y="1" width="438" height="12" uuid="2e428c2c-9fea-47db-bce7-b04833618f2d"/>
							<textElement>
								<font fontName="Arial" size="9"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{descricaoExame}]]></textFieldExpression>
						</textField>
						<textField>
							<reportElement x="488" y="1" width="45" height="12" uuid="a1868962-a398-4e5f-abf0-280497237d53"/>
							<textElement textAlignment="Right"/>
							<textFieldExpression><![CDATA[$F{quantidadeAvaliado}]]></textFieldExpression>
						</textField>
						<textField>
							<reportElement x="438" y="1" width="45" height="12" uuid="258e069f-78e2-4c2e-a226-dd4b5921efe5"/>
							<textElement textAlignment="Right"/>
							<textFieldExpression><![CDATA[$F{quantidadeSolicitado}]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="438" y="22" width="45" height="12" uuid="e355f97b-4766-4b8d-ab6b-c1720e25f132"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_solicitado")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="33" width="535" height="1" uuid="12e1ad38-61d0-4032-9221-48e13679534f"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="0" y="22" width="438" height="12" uuid="5d79d5ee-cf35-4ab7-8c99-5801714d3468"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_descricao")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="488" y="22" width="45" height="12" uuid="0f1ad553-9893-43b6-a7e5-dd6620d158d4"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_avaliado")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="0" y="10" width="129" height="12" uuid="603102c3-afda-4ad6-9487-0b3f159d0dc2"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_outros_exames")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="23" width="535" height="1" uuid="1423764e-073b-4104-820f-075b103ce197"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
