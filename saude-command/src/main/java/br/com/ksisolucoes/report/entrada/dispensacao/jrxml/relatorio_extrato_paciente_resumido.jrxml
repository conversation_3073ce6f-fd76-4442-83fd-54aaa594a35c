<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_extrato_paciente_resumido" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="c2195d67-e019-48cb-916a-f6ea2e8b5623">
	<property name="ireport.zoom" value="2.593742460100028"/>
	<property name="ireport.x" value="17"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioPacienteDispensacaoAtrasoDTOParam"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<parameter name="FORMA_APRESENTACAO" class="br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioPacienteDispensacaoAtrasoDTOParam.FormaApresentacao"/>
	<parameter name="MOSTRAR_VALORES" class="java.lang.String"/>
	<field name="dataDispensacao" class="java.util.Date"/>
	<field name="setor" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="produto" class="br.com.ksisolucoes.vo.entradas.estoque.Produto"/>
	<field name="unidade" class="br.com.ksisolucoes.vo.entradas.estoque.Unidade"/>
	<field name="saldo" class="java.lang.Double"/>
	<field name="grupoProduto" class="br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto"/>
	<field name="preco" class="java.lang.Double"/>
	<field name="descricao" class="java.lang.String">
		<fieldDescription><![CDATA[grupoProduto.descricao]]></fieldDescription>
	</field>
	<variable name="totalSaldo" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{saldo}]]></variableExpression>
	</variable>
	<variable name="quantidadeProtudo" class="java.lang.Double" resetType="Group" resetGroup="produto" calculation="Sum">
		<variableExpression><![CDATA[$F{saldo}]]></variableExpression>
	</variable>
	<variable name="totalProduto" class="java.lang.Double" resetType="Group" resetGroup="produto" calculation="Sum">
		<variableExpression><![CDATA[$F{preco}*$F{saldo}]]></variableExpression>
	</variable>
	<variable name="totalGeral" class="java.lang.Double" resetType="Group" resetGroup="geral" calculation="Sum">
		<variableExpression><![CDATA[$F{preco}*$F{saldo}]]></variableExpression>
	</variable>
	<variable name="subTotalValor" class="java.lang.Double" resetType="Group" resetGroup="FA" calculation="Sum">
		<variableExpression><![CDATA[$F{preco}*$F{saldo}]]></variableExpression>
	</variable>
	<group name="geral">
		<groupFooter>
			<band height="14">
				<printWhenExpression><![CDATA[$P{MOSTRAR_VALORES}.equals(RepositoryComponentDefault.SIM)]]></printWhenExpression>
				<textField>
					<reportElement uuid="96683561-a781-4f0f-baee-8393c229d428" x="420" y="2" width="63" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")+":"]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;">
					<reportElement uuid="96683561-a781-4f0f-baee-8393c229d428" x="483" y="2" width="52" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalGeral}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement uuid="57da07a5-cf42-420f-a451-b5eb0606af27" x="483" y="1" width="52" height="1"/>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="FA" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{grupoProduto}]]></groupExpression>
		<groupHeader>
			<band height="31">
				<rectangle radius="5">
					<reportElement uuid="a70ccfdd-3925-4687-a705-bc0a2c6e548d" mode="Transparent" x="0" y="1" width="535" height="14"/>
				</rectangle>
				<line>
					<reportElement uuid="57da07a5-cf42-420f-a451-b5eb0606af27" x="0" y="30" width="535" height="1"/>
				</line>
				<textField>
					<reportElement uuid="96683561-a781-4f0f-baee-8393c229d428" x="0" y="19" width="422" height="11"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_produto")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="96683561-a781-4f0f-baee-8393c229d428" x="422" y="19" width="14" height="11"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_unidade_abv")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="96683561-a781-4f0f-baee-8393c229d428" x="436" y="19" width="47" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quantidade")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="96683561-a781-4f0f-baee-8393c229d428" x="483" y="19" width="52" height="11">
						<printWhenExpression><![CDATA[$P{MOSTRAR_VALORES}.equals(RepositoryComponentDefault.SIM)]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_valor_total")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement uuid="2da98140-7877-43b3-b914-88ad05550993" x="6" y="2" width="525" height="13" isRemoveLineWhenBlank="true"/>
					<textElement>
						<font fontName="Arial" size="10" isBold="true" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_grupo_produto")+": "+$F{grupoProduto}.getDescricao()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="14">
				<printWhenExpression><![CDATA[$P{MOSTRAR_VALORES}.equals(RepositoryComponentDefault.SIM)]]></printWhenExpression>
				<textField>
					<reportElement uuid="c3e76306-f8bc-47a8-8eef-e3bcb3d10552" x="420" y="2" width="63" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_subtotal")+":"]]></textFieldExpression>
				</textField>
				<line>
					<reportElement uuid="818243ea-469c-466f-9682-e1664e3afe3f" x="483" y="1" width="52" height="1"/>
				</line>
				<textField pattern="###0.00;">
					<reportElement uuid="095e40e5-5096-4366-aef8-20b6db7d5442" x="483" y="2" width="52" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{subTotalValor}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="produto">
		<groupExpression><![CDATA[$F{produto}]]></groupExpression>
		<groupHeader>
			<band height="11">
				<textField isStretchWithOverflow="true">
					<reportElement uuid="fa5a41f2-7704-497c-9447-27f8476f799d" x="0" y="0" width="422" height="11"/>
					<textElement>
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{produto}.getDescricao()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement uuid="fa5a41f2-7704-497c-9447-27f8476f799d" x="422" y="0" width="14" height="11"/>
					<textElement>
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{unidade}.getUnidade()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="produto" pattern="###0.00;">
					<reportElement uuid="fa5a41f2-7704-497c-9447-27f8476f799d" x="483" y="0" width="52" height="11">
						<printWhenExpression><![CDATA[$P{MOSTRAR_VALORES}.equals(RepositoryComponentDefault.SIM)]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalProduto}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="produto" pattern="###0.00;">
					<reportElement uuid="fa5a41f2-7704-497c-9447-27f8476f799d" x="436" y="0" width="47" height="11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{quantidadeProtudo}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
</jasperReport>
