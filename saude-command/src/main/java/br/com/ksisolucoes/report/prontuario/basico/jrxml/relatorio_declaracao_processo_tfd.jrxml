<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_declaracao_processo_tfd" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="45bfbb33-9028-4db6-82e9-62b6243c1a54">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.9965000000000006"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.StringUtilKsi"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="descricaoCidadeEmpresaLogada" class="java.lang.String" isForPrompting="false"/>
	<field name="laudoTfd" class="br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd">
		<fieldDescription><![CDATA[laudoTfd]]></fieldDescription>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="57" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement uuid="991392b9-d179-4d85-879c-b630735bee55" key="textField-1" x="11" y="17" width="512" height="35"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" markup="html">
					<font fontName="Arial" size="10" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[    "Declaro que o Sr(a). " + $F{laudoTfd}.getUsuarioCadsus().getNome() + ", portador do CNS " + StringUtilKsi.getNumeroCartaoFormatado($F{laudoTfd}.getNumeroCartao()) +", encaminhou para o setor de TFD do município de "+ $P{descricaoCidadeEmpresaLogada} +" a seguinte solicitação:"]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="145" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement uuid="991392b9-d179-4d85-879c-b630735bee55" key="textField-1" x="11" y="3" width="512" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" markup="html">
					<font fontName="Arial" size="10" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA["Número: " + $F{laudoTfd}.getPedidoTfd().getNumeroPedido()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="991392b9-d179-4d85-879c-b630735bee55" key="textField-1" x="11" y="20" width="512" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" markup="html">
					<font fontName="Arial" size="10" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA["Tipo do Procedimento: " + $F{laudoTfd}.getTipoProcedimento().getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="991392b9-d179-4d85-879c-b630735bee55" key="textField-1" x="11" y="37" width="512" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" markup="html">
					<font fontName="Arial" size="10" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA["Data Laudo: " + Data.formatar($F{laudoTfd}.getDataLaudo())]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="991392b9-d179-4d85-879c-b630735bee55" key="textField-1" x="11" y="54" width="512" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" markup="html">
					<font fontName="Arial" size="10" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA["Situação Atual: " + $F{laudoTfd}.getDescricaoStatus()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="991392b9-d179-4d85-879c-b630735bee55" key="textField-1" x="11" y="71" width="512" height="13">
					<printWhenExpression><![CDATA[$F{laudoTfd}.getPedidoTfd().getParecer() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" markup="html">
					<font fontName="Arial" size="10" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA["Parecer Atual: " + $F{laudoTfd}.getPedidoTfd().getDescricaoParecer()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="991392b9-d179-4d85-879c-b630735bee55" key="textField-1" x="11" y="110" width="512" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" markup="html">
					<font fontName="Arial" size="10" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA["E, por ser verdade, firmo o presente, a fim de que surta os efeitos legais."]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="991392b9-d179-4d85-879c-b630735bee55" key="textField-1" x="11" y="129" width="512" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" markup="html">
					<font fontName="Arial" size="10" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA["Atenciosamente."]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
