<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_mapa_leitos" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="30" bottomMargin="30" uuid="9155b179-bb03-426d-a648-bc310bac77b6">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="3.8906136901500172"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="58"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto.TipoLeito"/>
	<import value="br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto.Sexo"/>
	<import value="br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto.Situacao"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="leitoQuarto" class="br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="totalLiberado" class="java.lang.Integer" resetType="Group" resetGroup="setor" calculation="Sum">
		<variableExpression><![CDATA[Situacao.LIBERADO.value().equals($F{leitoQuarto}.getSituacao()) ? 1 : 0]]></variableExpression>
	</variable>
	<variable name="totalLimpeza" class="java.lang.Integer" resetType="Group" resetGroup="setor" calculation="Sum">
		<variableExpression><![CDATA[Situacao.AGUARDANDO_LIMPEZA.value().equals($F{leitoQuarto}.getSituacao()) ? 1 : 0]]></variableExpression>
	</variable>
	<variable name="totalOcupado" class="java.lang.Integer" resetType="Group" resetGroup="setor" calculation="Sum">
		<variableExpression><![CDATA[Situacao.OCUPADO.value().equals($F{leitoQuarto}.getSituacao()) ? 1 : 0]]></variableExpression>
	</variable>
	<variable name="totalDesativado" class="java.lang.Integer" resetType="Group" resetGroup="setor" calculation="Sum">
		<variableExpression><![CDATA[Situacao.DESATIVADO.value().equals($F{leitoQuarto}.getSituacao()) ? 1 : 0]]></variableExpression>
	</variable>
	<variable name="totalIsolado" class="java.lang.Integer" resetType="Group" resetGroup="setor" calculation="Sum">
		<variableExpression><![CDATA[Situacao.ISOLADO.value().equals($F{leitoQuarto}.getSituacao()) ? 1 : 0]]></variableExpression>
	</variable>
	<variable name="total" class="java.lang.Integer" resetType="Group" resetGroup="setor" calculation="Count">
		<variableExpression><![CDATA[$F{leitoQuarto}.getCodigo()]]></variableExpression>
	</variable>
	<group name="setor" isStartNewPage="true" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{empresa}.getDescricao()]]></groupExpression>
		<groupHeader>
			<band height="50">
				<rectangle>
					<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="277" height="30" uuid="50364c1d-c4a5-4026-935d-613364af8e75"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isStretchWithOverflow="true">
					<reportElement x="0" y="0" width="277" height="30" uuid="3f4b8e8c-d77b-4ccd-b49b-085c4a65c03f"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{empresa}.getDescricao()]]></textFieldExpression>
				</textField>
				<rectangle>
					<reportElement stretchType="RelativeToBandHeight" x="0" y="30" width="555" height="20" uuid="f74e3b29-97da-4703-9553-02d2cc510983"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<line>
					<reportElement stretchType="RelativeToBandHeight" x="179" y="30" width="1" height="20" uuid="8bcfd704-8aaf-434d-a5ba-0a85e5375032"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<line>
					<reportElement stretchType="RelativeToBandHeight" x="276" y="30" width="1" height="20" uuid="bb726738-5b1d-439c-9833-f877258682bf"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<line>
					<reportElement stretchType="RelativeToBandHeight" x="355" y="30" width="1" height="20" uuid="6267263b-8dc6-4a9f-b1e1-15a5d8e9aeac"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField isStretchWithOverflow="true">
					<reportElement x="276" y="30" width="80" height="20" uuid="a2c2e13e-32f1-43aa-84dc-41f450ff6c21"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="12" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_sexo") ]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="179" y="30" width="98" height="20" uuid="7ca402d6-5b42-4507-a51a-61436b237bb5"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="12" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_tipo_de_leito")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="355" y="30" width="200" height="20" uuid="ce3edca4-5b3a-41d1-8d79-4c1fe06f1c8f"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="12" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_situacao_paciente")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="1" y="30" width="179" height="20" uuid="3532ee0d-a27b-4093-84a2-767684a4088b"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="12" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_leito")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="60">
				<rectangle>
					<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="267" height="20" uuid="959a77c3-438b-4053-a45f-9ea072d1dceb"/>
					<graphicElement>
						<pen lineWidth="1.0" lineStyle="Solid"/>
					</graphicElement>
				</rectangle>
				<textField isStretchWithOverflow="true">
					<reportElement x="1" y="10" width="62" height="10" uuid="31d3af5b-0d7c-4ef7-a35c-846bb7210f50"/>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_ocupado") + ": "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="90" y="10" width="62" height="10" uuid="b74f07d1-031e-4337-910b-79914789c844"/>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_isolado") + ": "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="1" y="0" width="62" height="10" uuid="5979d8fc-52cd-462d-bbec-1d0fc41f50f3"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_liberado") + ": "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="90" y="0" width="62" height="10" uuid="5f526e57-8fb3-46dc-a1a3-02fca51da81e"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_limpeza") + ": "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="179" y="0" width="62" height="10" uuid="71afbc7a-c836-4dea-922b-ad97d8f36c45"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_desativado") + ": "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="179" y="10" width="62" height="10" uuid="778777c2-622e-4e33-abb9-0183a28ee206"/>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_total") + ": "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="64" y="0" width="25" height="10" uuid="40c0e8c1-edaf-48ab-84b9-3ea738c51710"/>
					<box leftPadding="1"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalLiberado}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="64" y="10" width="25" height="10" uuid="2e3b5c36-0285-4f78-b0d7-11934334f2fb"/>
					<box leftPadding="1"/>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalOcupado}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="153" y="0" width="25" height="10" uuid="8f97a647-8add-487c-9522-a2047df7b585"/>
					<box leftPadding="1"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalLimpeza}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="153" y="10" width="25" height="10" uuid="12743346-0251-4774-bb57-e303742db7df"/>
					<box leftPadding="1"/>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalIsolado}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="242" y="0" width="25" height="10" uuid="e5e7c7c6-84ff-4f07-884d-ebd19e0c812f"/>
					<box leftPadding="1"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalDesativado}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="242" y="10" width="25" height="10" uuid="47bd61d2-d34a-438c-80fe-fa4abcdcde52"/>
					<box leftPadding="1"/>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{total}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<detail>
		<band height="20" splitType="Stretch">
			<rectangle>
				<reportElement x="0" y="0" width="555" height="20" backcolor="#E6E6E6" uuid="83a0fb7b-5cd2-4933-adf8-a1de725849d9">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getNome() != null]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="555" height="20" uuid="72bbb117-6a8e-442f-8581-12ab34012b98">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getNome() == null]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<line>
				<reportElement stretchType="RelativeToBandHeight" x="179" y="0" width="1" height="20" uuid="b2c23041-a6c6-4f6a-908b-f8b75823377f"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement x="1" y="0" width="179" height="20" uuid="801f6227-7092-47a4-9007-f5d9920abe8c"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{leitoQuarto}.getDescricaoQuarto()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="179" y="0" width="98" height="20" uuid="0d50729d-6604-4cdd-a30b-a23c480c1285"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{leitoQuarto}.getDescricaoTipoLeito()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="276" y="0" width="80" height="20" uuid="4887e880-9f41-4b7f-9f08-5faa9d1a9d37"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{leitoQuarto}.getDescricaoSexo()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="355" y="0" width="200" height="20" backcolor="#FFFFFF" uuid="7fcb2160-1769-4c32-bc0f-2368ec053630"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNome() != null
?
    $F{usuarioCadsus}.getNome()
:
    $F{leitoQuarto}.getSituacaoDescricao()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement stretchType="RelativeToBandHeight" x="355" y="0" width="1" height="20" uuid="50dd9c6f-6757-4ada-8c32-82401ccfc304"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<line>
				<reportElement stretchType="RelativeToBandHeight" x="276" y="0" width="1" height="20" uuid="3aebed67-489e-45a3-8f0f-c55f76ddbfc6"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
