/*
 * RelatorioRelacaoProdutos
 *
 * Created on Marco 15, 2005
 */
package br.com.ksisolucoes.report.entrada.estoque;

import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioRelacaoProdutosUnidadeDTOParam;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.query.QueryListagemProdutosUnidade;
import br.com.ksisolucoes.system.controle.SGKException;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> <PERSON>
 */
public class RelatorioRelacaoProdutosUnidade extends AbstractReport implements ReportProperties{
    
    private RelatorioRelacaoProdutosUnidadeDTOParam bean;
    private String estoque;
    private String ordena;
    private String apresentacao;
    
    /**
     * Creates a new instance of RelatorioRelacaoProduto
     */
    public RelatorioRelacaoProdutosUnidade(RelatorioRelacaoProdutosUnidadeDTOParam bean) {
        
        //nao executa o construtor
        super(false);
        
        this.bean = bean;
        
        // Imprimi parametro da empresa
        
        if (this.bean.getEmpresaList().size() > 1){
            this.addDescricaoParametro(Bundle.getStringApplication("rotulo_empresa"),Bundle.getStringApplication("rotulo_multi_selecao"));
        }else if(this.bean.getEmpresaList().size() ==1) {
            this.addDescricaoParametro(Bundle.getStringApplication("rotulo_empresa"),((Empresa)this.bean.getEmpresaList().get(0)).getDescricao());
        }
        // Imprimi parametro do Produto

       //Grupo e SubGrupo.
        if(this.bean.getSubGrupo() != null){
//            if(this.bean.getGrupoList().size() > 1){
//                this.addDescricaoParametro(Bundle.getStringApplication("rotulo_grupo"),Bundle.getStringApplication( "rotulo_multipla_selecao",this.bean.getGrupoList().size() ));
//            }else if(this.bean.getGrupoList().size() == 1){
//                this.addDescricaoParametro(Bundle.getStringApplication("rotulo_grupo"),((GrupoProduto)this.bean.getGrupoList().get(0)).getDescricaoFormatado());
//            }else  if(this.bean.getGrupoList().size() == 0){
//                this.addDescricaoParametro(Bundle.getStringApplication("rotulo_grupo"),Bundle.getStringApplication( "rotulo_todos_grupos_produtos"));
//            }
//        }else{
            // comentario:imprimi o grupo que est� dentro de subgrupo..., pois est� filtrando do panel subgrupo que cont�m um grupo
//              this.addDescricaoParametro(Bundle.getStringApplication("rotulo_grupo"), this.bean.getSubGrupo().getRoGrupoProduto().getDescricao());
            this.addDescricaoParametro(Bundle.getStringApplication("rotulo_grupo"), this.bean.getGrupoProdutoSubGrupo().getDescricao());
            this.addDescricaoParametro(Bundle.getStringApplication("rotulo_subgrupo"),this.bean.getSubGrupo().getDescricao()); //imprimi o subgrupo...
        }

        //Imprimi parametro ordenacao

        if(this.bean.getOrdenacao().equals(Produto.PROP_CODIGO)){
            ordena = Bundle.getStringApplication( "rotulo_codigo" );
        }else{
            ordena = Bundle.getStringApplication( "rotulo_descricao" );
        }
        this.addDescricaoParametro(Bundle.getStringApplication( "rotulo_ordenacao" ), ordena);

        // Imprimi parametro Forma Apresentacao
        if (this.bean.getFormaApresentacao().intValue() == AGRUPAR_GRUPO){
            apresentacao = Bundle.getStringApplication( "rotulo_grupo" );
        }else{
            apresentacao = Bundle.getStringApplication( "rotulo_produto" );

        }
        this.addDescricaoParametro(Bundle.getStringApplication( "rotulo_forma_apresentacao" ), apresentacao);
        
        //Executa o metodo que passa os parametros para o Relatorio
        this.parametros();
        
        //executa o construtor
//        this.abrirReport();
        
        //abre a a tela
//        DesktopSingleton.abrirPrograma(this);
    }
    
    /**
     *Metodo que faz o filtro para a montagem do SQL
     */
    public Collection getCollection() {
        this.addParametro("FORMA_APRESENTACAO", this.bean.getFormaApresentacao() );
        this.addParametro("EXIBIR_QTD_PADRAO", this.bean.getExibirQtdPadrao() );
        this.addParametro("EMPRESA_NOVA_PAGINA", RepositoryComponentDefault.SIM.equals(this.bean.getIniciarNovaPaginaUnidade()));
        try {
            return new QueryListagemProdutosUnidade( this.bean).start().getDtoList();
        } catch (SGKException ex) {
            Loggable.log.error(ex);
            return null;
        }
    }
    
    /**
     * Metodo que seta o titulo do relatorio
     */
    public String getTitulo() {
        // Seta o titulo do relatorio
        /*String titulo = Bundle.getStringApplication( "rotulo_relatorio_relacao_de_produtos_por_grupo" );
        if( this.bean.getFormaApresentacao().intValue() == AGRUPAR_PRODUTO ){
            titulo = Bundle.getStringApplication( "rotulo_relatorio_relacao_de_produtos_por_produto" );
        }*/
        return Bundle.getStringApplication( "rotulo_produtos_unidade" );
    }
    
    /**
     * Metodo que seta o XML a ser usado no Relatorio
     */
    public String getXML() {
        String xml = "/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_produto_unidade.jrxml";
        
//        if(this.bean.getFormaApresentacao().intValue() == AGRUPAR_GRUPO){
//            //xml="/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_grupo_produto_estoque_empresa.jrxml";
//        }
        
        return xml;
    }
    
    /**
     * Metodo que seta os parametros para o relatorios
     *
     * N�o � usado mais os parametros dessa maneira, s� est� aqui como exemplo
     *
     */
    public void parametros(){
        Map map_param = new HashMap();
        
        //-- PARAMETRO FORMA APRESENTACAO --//
        
        String formaApresentacao  = null;
        
//        if(this.bean.getFormaApresentacao().intValue() == AGRUPAR_GRUPO){
//            // Seta a Forma de apresentacao como Grupo
//            formaApresentacao = Bundle.getStringApplication("rotulo_grupo");
//
//        } else if(this.bean.getFormaApresentacao().intValue() == AGRUPAR_PRODUTO){
//            // Seta a forma de apresentacao como Produto
//            formaApresentacao = Bundle.getStringApplication("rotulo_produto");
//        }else{
//            formaApresentacao = Bundle.getStringApplication("rotulo_grupo");
//        }
        map_param.put( "ParamFormaApresentacao", formaApresentacao );
        
        //----------------------------------------------------------------------
        //-- PARAMETRO ORDENACAO --//
        String ordenacao = null;
        
        if(this.bean.getOrdenacao().equals(Produto.PROP_CODIGO)){
            ordenacao = Bundle.getStringApplication( "rotulo_cod_produto" );
        } else if(this.bean.getOrdenacao().equals(Produto.PROP_DESCRICAO)){
            ordenacao = Bundle.getStringApplication("rotulo_descricao");
        }else{
            ordenacao = Bundle.getStringApplication("rotulo_cod_produto");
        }
        map_param.put( "ParamOrdenacao", ordenacao );
        
        // Seta a lista de parametros
//        this.setParametros(map_param);
        
    }
    
   
}