<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="sub_formulario_tabagismo_historia_patologica_pregressa" pageWidth="556" pageHeight="842" columnWidth="556" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="812a5b70-40d5-4117-bc4f-81ca6e8acef4">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.0000000000000346"/>
	<property name="ireport.x" value="19"/>
	<property name="ireport.y" value="339"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.FormasUsoHelper"/>
	<import value="br.com.celk.util.DataUtil"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="descricaoDeixarDeFumar" class="java.lang.String"/>
	<field name="codigo" class="java.lang.Long"/>
	<field name="temLesoesBoca" class="java.lang.Long"/>
	<field name="tratamentoLesoesBoca" class="java.lang.Long"/>
	<field name="temDiabete" class="java.lang.Long"/>
	<field name="tratamentoDiabete" class="java.lang.Long"/>
	<field name="temHipertensao" class="java.lang.Long"/>
	<field name="tratamentoHipertensao" class="java.lang.Long"/>
	<field name="temProblemaCardiaco" class="java.lang.Long"/>
	<field name="tratamentoCardiaco" class="java.lang.Long"/>
	<field name="cardiacoQual" class="java.lang.String"/>
	<field name="temProblemaEstomago" class="java.lang.Long"/>
	<field name="tratamentoEstomago" class="java.lang.Long"/>
	<field name="temProblemaPulmonar" class="java.lang.Long"/>
	<field name="pulmonarQual" class="java.lang.String"/>
	<field name="tratamentoPulmonar" class="java.lang.Long"/>
	<field name="temAlergiaRespiratoria" class="java.lang.Long"/>
	<field name="tratamentoRespiratoria" class="java.lang.Long"/>
	<field name="temAlergiaCutanea" class="java.lang.Long"/>
	<field name="tratamentoCutanea" class="java.lang.Long"/>
	<field name="temLesaoTumor" class="java.lang.Long"/>
	<field name="lesaoTumorOnde" class="java.lang.String"/>
	<field name="tratamentoLesaoTumor" class="java.lang.Long"/>
	<field name="temConvulsao" class="java.lang.Long"/>
	<field name="tratamentoConvulsao" class="java.lang.Long"/>
	<field name="temAnorexia" class="java.lang.Long"/>
	<field name="tratamentoAnorexia" class="java.lang.Long"/>
	<field name="temCriseDepressao" class="java.lang.Long"/>
	<field name="tratamentoCriseDepressao" class="java.lang.Long"/>
	<field name="fazTratamentoPsicologico" class="java.lang.Long"/>
	<field name="tratamentoPsicologico" class="java.lang.Long"/>
	<field name="descricaoBebidasAlcoolicas" class="java.lang.String"/>
	<field name="problemaSaudeSerio" class="java.lang.String"/>
	<field name="medicamentoEmUso" class="java.lang.Long"/>
	<field name="medicamentoQual" class="java.lang.String"/>
	<field name="proteseDentaria" class="java.lang.Long"/>
	<field name="estaGravida" class="java.lang.Long"/>
	<field name="descricaoMesesGravidez" class="java.lang.String"/>
	<field name="estaAmamentando" class="java.lang.Long"/>
	<field name="participouGrupo" class="java.lang.Long"/>
	<field name="viveFumante" class="java.lang.Long"/>
	<field name="viveFumanteParentesco" class="java.lang.String"/>
	<field name="ganharPeso" class="java.lang.Long"/>
	<group name="Cabeçalho" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band height="14">
				<textField isBlankWhenNull="true">
					<reportElement key="textField-9" mode="Opaque" x="0" y="1" width="555" height="12" backcolor="#F6F4F2" uuid="541d9f2e-4a88-4ae1-a4ea-96489b8b6c04"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
						<pen lineWidth="1.0"/>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_historia_patologica_pregressa").toUpperCase()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<detail>
		<band height="554" splitType="Stretch">
			<textField>
				<reportElement x="4" y="15" width="50" height="10" uuid="ab6516f9-c7b3-4677-8471-80a77e399666"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temLesoesBoca})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="64" y="15" width="226" height="10" uuid="53357e1c-46bf-4e3b-8476-cd61df89e185">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temLesoesBoca})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_esta_em_tratamento") + " " +
(RepositoryComponentDefault.SIM_LONG.equals($F{tratamentoLesoesBoca})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")
)]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="4" y="5" width="537" height="10" uuid="b7b4dfe9-2fb1-43c7-b534-d2a1cc8d87f6"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Você tem ou teve frequentemente aftas, lesões (feridas), e/ou sangramento na boca?]]></text>
			</staticText>
			<staticText>
				<reportElement x="4" y="29" width="274" height="10" uuid="d5cbf479-b848-4323-ba19-e4ad50963c38"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Você tem diabetes mellitus?]]></text>
			</staticText>
			<textField>
				<reportElement x="4" y="39" width="50" height="10" uuid="aa47ca5e-e085-490d-9950-3549d9ce3bf1"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temDiabete})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="64" y="39" width="226" height="10" uuid="283e4d62-81dc-4741-abd1-1ed24de94bba">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temDiabete})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_esta_em_tratamento") + " " +
(RepositoryComponentDefault.SIM_LONG.equals($F{tratamentoDiabete})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")
)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="321" y="87" width="166" height="10" uuid="b0ae9883-2ac9-4764-9a32-043b039a5d54">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temProblemaCardiaco})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_esta_em_tratamento") + " " +
(RepositoryComponentDefault.SIM_LONG.equals($F{tratamentoCardiaco})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")
)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="4" y="63" width="50" height="10" uuid="f51788fb-5dd5-4879-93eb-2d94ed9797fb"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temHipertensao})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="4" y="87" width="50" height="10" uuid="6bbf8f8c-e638-4df0-9026-3ab8ac0f32f1"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temProblemaCardiaco})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="64" y="63" width="226" height="10" uuid="6f0a25d2-0d2d-47e7-94bd-8396c8e68c0a">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temHipertensao})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_esta_em_tratamento") + " " +
(RepositoryComponentDefault.SIM_LONG.equals($F{tratamentoHipertensao})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")
)]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="4" y="53" width="274" height="10" uuid="d540560d-87c1-4f19-9d71-fe478747b752"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Você tem hipertensão arterial?]]></text>
			</staticText>
			<staticText>
				<reportElement x="4" y="77" width="274" height="10" uuid="dfc39db3-bdf6-4b96-8545-0e711142020c"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Você tem ou teve algum problema cardíaco?]]></text>
			</staticText>
			<textField>
				<reportElement x="64" y="87" width="25" height="10" uuid="edfe8d57-f93e-4a84-9de1-c6b76d5b59ce">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temProblemaCardiaco})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_qual_?")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="89" y="87" width="226" height="10" uuid="3f46cae9-937e-4b81-b54e-c7f1f2e0b335">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temProblemaCardiaco})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cardiacoQual}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="64" y="111" width="226" height="10" uuid="299b5bdd-d8a4-4f88-b221-501946f7c780">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temProblemaEstomago})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_esta_em_tratamento") + " " +
(RepositoryComponentDefault.SIM_LONG.equals($F{tratamentoEstomago})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")
)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="4" y="111" width="50" height="10" uuid="a75b3b57-5ae9-477d-83a3-7e38980ba539"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temProblemaEstomago})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="4" y="101" width="355" height="10" uuid="3b14d756-198b-4400-a3ea-27545d714acc"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Você tem ou teve frequentemente queimação, azia, dor no estômago ou úlcera ou gastrite?]]></text>
			</staticText>
			<textField>
				<reportElement x="4" y="135" width="50" height="10" uuid="05b3858e-ecb2-4866-9ce1-22b7017380f2"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temProblemaPulmonar})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="321" y="135" width="166" height="10" uuid="e3dadc4a-6715-4158-81fc-4017919f8f5f">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temProblemaPulmonar})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_esta_em_tratamento") + " " +
(RepositoryComponentDefault.SIM_LONG.equals($F{tratamentoPulmonar})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")
)]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="4" y="125" width="274" height="10" uuid="8990fbe0-0fbb-4d22-8fc9-ca987670ceb9"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Você tem ou teve algum problema pulmonar?]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="89" y="135" width="226" height="10" uuid="309497ec-8636-456b-a427-7c2bbde8c05e">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temProblemaPulmonar})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{pulmonarQual}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="64" y="135" width="25" height="10" uuid="2e83d750-203a-4e85-ac56-2a6e8ef0cea9">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temProblemaPulmonar})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_qual_?")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="64" y="159" width="226" height="10" uuid="61d672cc-c3a5-4023-8e41-522aff5cfac8">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temAlergiaRespiratoria})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_esta_em_tratamento") + " " +
(RepositoryComponentDefault.SIM_LONG.equals($F{tratamentoRespiratoria})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")
)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="4" y="159" width="50" height="10" uuid="4c6fa12f-f7c2-4d7a-941e-543fdfb2b2ed"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temAlergiaRespiratoria})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="4" y="149" width="274" height="10" uuid="969d67d8-b0e1-4aff-a339-ea24ca412b0a"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Você tem alergias respiratórias?]]></text>
			</staticText>
			<textField>
				<reportElement x="4" y="183" width="50" height="10" uuid="01cf11a5-b680-4568-9e6f-af7a445e65e5"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temAlergiaCutanea})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="64" y="183" width="226" height="10" uuid="2f03b633-ae16-47f0-852f-8ab377df06b9">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temAlergiaCutanea})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_esta_em_tratamento") + " " +
(RepositoryComponentDefault.SIM_LONG.equals($F{tratamentoCutanea})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")
)]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="4" y="173" width="274" height="10" uuid="6a2bc3f4-d96b-45ae-999c-871975455c55"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Você tem alergias cutâneas?]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="91" y="207" width="226" height="10" uuid="00a1dfe7-2a65-4965-a318-3c9ee12d2fff">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temLesaoTumor})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{lesaoTumorOnde}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="321" y="207" width="166" height="10" uuid="0e48df00-ffce-4c20-b486-0687df9f1f0e">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temLesaoTumor})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_esta_em_tratamento") + " " +
(RepositoryComponentDefault.SIM_LONG.equals($F{tratamentoLesaoTumor})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")
)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="64" y="207" width="25" height="10" uuid="539ee6fc-3f3c-40a9-8501-534c9991eb3d">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temLesaoTumor})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_onde")+"? "]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="4" y="197" width="274" height="10" uuid="03b3f57c-b180-4857-94d4-8a4ea00f42f7"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Você tem ou teve alguma lesão ou tumor maligno?]]></text>
			</staticText>
			<textField>
				<reportElement x="4" y="207" width="50" height="10" uuid="aae4493e-c77a-4e92-93ed-54032ed39a20"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temLesaoTumor})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="64" y="231" width="226" height="10" uuid="6bb43cda-897e-43b3-8d24-286b0b5d05d6">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temConvulsao})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_esta_em_tratamento") + " " +
(RepositoryComponentDefault.SIM_LONG.equals($F{tratamentoConvulsao})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")
)]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="4" y="221" width="298" height="10" uuid="69d4e1ee-ce26-4ecf-96ff-d7d9411baf97"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Você tem ou teve crise convulsiva, convulsão febril na infância ou epilepsia?]]></text>
			</staticText>
			<textField>
				<reportElement x="4" y="231" width="50" height="10" uuid="89f1c96e-3cc4-4621-a1c1-0c96a1388690"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temConvulsao})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="64" y="255" width="226" height="10" uuid="045c1b8c-68cb-4223-acc8-2ac2b567638b">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temAnorexia})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_esta_em_tratamento") + " " +
(RepositoryComponentDefault.SIM_LONG.equals($F{tratamentoAnorexia})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")
)]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="4" y="245" width="298" height="10" uuid="08ef3497-db3e-4677-858f-fa90d7a76289"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Você tem anorexia nervosa ou bulimia?]]></text>
			</staticText>
			<textField>
				<reportElement x="4" y="255" width="50" height="10" uuid="64f26c71-e2d3-4a82-8b64-2ef4b7ab56ea"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temAnorexia})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="64" y="279" width="226" height="10" uuid="0ac19bd6-c70a-45dd-86c4-61cc71d15226">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temCriseDepressao})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_esta_em_tratamento") + " " +
(RepositoryComponentDefault.SIM_LONG.equals($F{tratamentoCriseDepressao})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")
)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="4" y="279" width="50" height="10" uuid="81334e24-e78a-4d9b-893e-559b9b148311"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{temCriseDepressao})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="4" y="269" width="298" height="10" uuid="65fcfd03-87c5-4d74-87aa-fb8dd2d927c6"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Você costuma ter crises de depressão e/ou ansiedade?]]></text>
			</staticText>
			<textField>
				<reportElement x="4" y="303" width="50" height="10" uuid="8efade6b-90f4-468a-a41e-b7b7e8040e80"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{fazTratamentoPsicologico})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="4" y="293" width="298" height="10" uuid="51a3ed40-46f8-4e82-8466-70c0803704c2"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Você faz ou já fez algum tratamento psicológico ou psiquiátrico?]]></text>
			</staticText>
			<textField>
				<reportElement x="64" y="303" width="226" height="10" uuid="717b0f9c-3094-4388-9ec4-4425b04a4c23">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{fazTratamentoPsicologico})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_esta_em_tratamento") + " " +
(RepositoryComponentDefault.SIM_LONG.equals($F{tratamentoPsicologico})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")
)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="4" y="327" width="226" height="10" uuid="06066fc1-61af-41ce-a9c2-ac0577c638c1"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoBebidasAlcoolicas}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="4" y="317" width="298" height="10" uuid="f1e532bf-cfea-4619-bbfe-53b843520f5d"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Você costuma ingerir bebidas alcóolicas com que frequência?]]></text>
			</staticText>
			<staticText>
				<reportElement x="4" y="341" width="298" height="10" uuid="d5326526-a87a-4a53-8510-6539b1df55d2"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Você tem ou teve algum outro problema de saúde sério que não foi citado?]]></text>
			</staticText>
			<textField>
				<reportElement x="4" y="351" width="483" height="10" uuid="ede591dd-ebb7-4f7c-8eef-67546ba0f32b"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{problemaSaudeSerio} != null && !"".equals($F{problemaSaudeSerio}))
?
    $F{problemaSaudeSerio}
:
    Bundle.getStringApplication("rotulo_nao")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="89" y="375" width="466" height="10" uuid="a6899b11-41f0-4efe-a2c0-ca99b5df60cd">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{medicamentoEmUso})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{medicamentoQual}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="64" y="375" width="25" height="10" uuid="7172331d-f851-4ce7-8533-4ad7e8a32810">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{medicamentoEmUso})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_qual_?")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="4" y="365" width="274" height="10" uuid="e7b73bd4-3390-4438-a4c1-b7f89719c95c"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Algum medicamento em uso atual?]]></text>
			</staticText>
			<textField>
				<reportElement x="4" y="375" width="50" height="10" uuid="7adcbe15-873b-4ec0-bb6e-a21b88ac6384"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{medicamentoEmUso})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="4" y="389" width="274" height="10" uuid="bb7a3fe6-d67e-441d-a955-fa2a888176d4"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Tem prótese dentária móvel?]]></text>
			</staticText>
			<textField>
				<reportElement x="4" y="399" width="50" height="10" uuid="0bbe9b27-0304-4463-9327-b27804cb1fc2"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{proteseDentaria})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="4" y="413" width="274" height="10" uuid="9ae0156a-25a5-48f3-a441-179a3d81ec96"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Está grávida?]]></text>
			</staticText>
			<textField>
				<reportElement x="4" y="423" width="50" height="10" uuid="38dfbc63-3919-4121-90f1-6da9d812e15b"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{estaGravida})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="64" y="423" width="62" height="10" uuid="5748bbe1-df21-4156-a870-535c6941a345">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{estaGravida})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quantos_meses")+"? "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="127" y="423" width="226" height="10" uuid="c7c7dc4f-d73a-4788-b0af-8b77ca0fe4ea">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{estaGravida})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoMesesGravidez}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="4" y="437" width="274" height="10" uuid="edb76675-ae5f-4db9-84c6-f57b7e03da37"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Está amamentando?]]></text>
			</staticText>
			<textField>
				<reportElement x="4" y="447" width="50" height="10" uuid="ae549d3c-f307-4b93-915f-f897e4ba37a6"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{estaAmamentando})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="4" y="461" width="401" height="10" uuid="3866709f-bd58-4366-9b82-fe3bfae0a4fd"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Você já participou de algum grupo de apoio para abordagem e tratamento do tabagismo nessa unidade?]]></text>
			</staticText>
			<textField>
				<reportElement x="4" y="471" width="50" height="10" uuid="bd2daa9e-3e48-4360-8eb8-bb62659b6e4f"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{participouGrupo})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="4" y="485" width="401" height="10" uuid="383072b4-22f3-4bcc-8bd1-785bf9e67806"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Por que você quer deixar de fumar agora?]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement x="4" y="495" width="401" height="10" uuid="38d52487-14de-4eda-a12b-66c7b7433f3d"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{descricaoDeixarDeFumar}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="4" y="509" width="274" height="10" uuid="167b15ea-d934-434e-8392-8edfeeb01f41"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Você convive com fumantes na sua casa?]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="144" y="519" width="411" height="10" uuid="0d2ee785-049a-4631-a45f-b58b2a68c495">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{viveFumante})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{viveFumanteParentesco}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="64" y="519" width="80" height="10" uuid="7a570196-6695-410b-be6f-e752800c07c2">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{viveFumante})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_grau_parentesco")+": "]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="4" y="519" width="50" height="10" uuid="b65a1a55-3b5f-4b13-a062-ac3d733ba257"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{viveFumante})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="4" y="533" width="401" height="10" uuid="4a92303d-07ad-4777-b775-207ce66685d8"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Você se preocupa em ganhar peso ao deixar de fumar?]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="4" y="543" width="50" height="10" uuid="87f230ca-d242-4f39-8c43-f3c0ebc6acf7"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{ganharPeso})
?
    Bundle.getStringApplication("rotulo_sim")
:
    Bundle.getStringApplication("rotulo_nao")]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
