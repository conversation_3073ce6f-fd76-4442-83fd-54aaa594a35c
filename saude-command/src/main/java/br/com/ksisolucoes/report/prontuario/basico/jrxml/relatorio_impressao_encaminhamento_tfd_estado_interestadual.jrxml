<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_impressao_comprovante_agendamento" pageWidth="595" pageHeight="842" columnWidth="553" leftMargin="21" rightMargin="21" topMargin="20" bottomMargin="20" uuid="b2e21ca3-57b0-434b-857c-65ee95861f46">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.1269722013523735"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="375"/>
	<import value="br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp"/>
	<import value="br.com.ksisolucoes.vo.cadsus.TipoDocumentoUsuario"/>
	<import value="java.text.DecimalFormat"/>
	<import value="br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="NUMERO_UNIDADE" class="java.lang.String"/>
	<parameter name="TITULO_REPORT" class="java.lang.String"/>
	<parameter name="CABECALHO_ADICIONAL_1" class="java.lang.String"/>
	<parameter name="CABECALHO_ADICIONAL_2" class="java.lang.String"/>
	<parameter name="FONE_UNIDADE" class="java.lang.String"/>
	<parameter name="UNIDADE_ATENDIMENTO" class="java.lang.String"/>
	<parameter name="CIDADE_UNIDADE" class="java.lang.String"/>
	<parameter name="BAIRRO_UNIDADE" class="java.lang.String"/>
	<parameter name="DESC_CABECALHO_PADRAO" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="CEP_UNIDADE" class="java.lang.String"/>
	<parameter name="CAMINHO_IMAGEM_PADRAO" class="java.lang.String"/>
	<parameter name="RUA_UNIDADE" class="java.lang.String"/>
	<parameter name="UF_UNIDADE" class="java.lang.String"/>
	<parameter name="EXIBIR_HORARIO" class="java.lang.Boolean"/>
	<parameter name="estado" class="java.lang.String"/>
	<parameter name="CABECALHO_DIRETOR_TECNICO" class="java.lang.String"/>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus">
		<fieldDescription><![CDATA[laudoTfd.usuarioCadsus]]></fieldDescription>
	</field>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional">
		<fieldDescription><![CDATA[laudoTfd.profissional]]></fieldDescription>
	</field>
	<field name="tipoProcedimento" class="br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento">
		<fieldDescription><![CDATA[laudoTfd.tipoProcedimento]]></fieldDescription>
	</field>
	<field name="laudoTfd" class="br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd">
		<fieldDescription><![CDATA[laudoTfd]]></fieldDescription>
	</field>
	<field name="usuarioCadsusCns" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns"/>
	<field name="historicoDoenca" class="java.lang.String">
		<fieldDescription><![CDATA[laudoTfd.historicoDoenca]]></fieldDescription>
	</field>
	<field name="exameFisico" class="java.lang.String">
		<fieldDescription><![CDATA[laudoTfd.exameFisico]]></fieldDescription>
	</field>
	<field name="diagnosticoProvavel" class="java.lang.String">
		<fieldDescription><![CDATA[laudoTfd.diagnosticoProvavel]]></fieldDescription>
	</field>
	<field name="exameComplementarRealizado" class="java.lang.String">
		<fieldDescription><![CDATA[laudoTfd.exameComplementarRealizado]]></fieldDescription>
	</field>
	<field name="tratamentoRealizado" class="java.lang.String">
		<fieldDescription><![CDATA[laudoTfd.tratamentoRealizado]]></fieldDescription>
	</field>
	<field name="procTratamentoSolicitado1" class="java.lang.String">
		<fieldDescription><![CDATA[procTratamentoSolicitado1]]></fieldDescription>
	</field>
	<field name="justificativaTfd" class="java.lang.String">
		<fieldDescription><![CDATA[laudoTfd.justificativaTfd]]></fieldDescription>
	</field>
	<field name="justificativaAcompanhante" class="java.lang.String">
		<fieldDescription><![CDATA[laudoTfd.justificativaAcompanhante]]></fieldDescription>
	</field>
	<field name="justificativaTransporte" class="java.lang.String">
		<fieldDescription><![CDATA[laudoTfd.justificativaTransporte]]></fieldDescription>
	</field>
	<field name="transporteRecomendavel" class="java.lang.String"/>
	<field name="cid" class="br.com.ksisolucoes.vo.prontuario.basico.Cid">
		<fieldDescription><![CDATA[laudoTfd.cid]]></fieldDescription>
	</field>
	<field name="caraterAtendimento" class="java.lang.String"/>
	<field name="procTratamentoSolicitado2" class="java.lang.String">
		<fieldDescription><![CDATA[procTratamentoSolicitado2]]></fieldDescription>
	</field>
	<field name="procTratamentoSolicitado3" class="java.lang.String">
		<fieldDescription><![CDATA[procTratamentoSolicitado3]]></fieldDescription>
	</field>
	<field name="this" class="br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioImprimirLaudoTfdDTO"/>
	<field name="prioridade" class="java.lang.String">
		<fieldDescription><![CDATA[laudoTfd.prioridade]]></fieldDescription>
	</field>
	<field name="justificativaInternacaoConsulta" class="java.lang.String">
		<fieldDescription><![CDATA[laudoTfd.justificativaInternacao]]></fieldDescription>
	</field>
	<field name="avaliacaoClinicaGeral" class="java.lang.String">
		<fieldDescription><![CDATA[laudoTfd.avaliacaoClinicaGeral]]></fieldDescription>
	</field>
	<field name="medicamentoEmUsoDose" class="java.lang.String">
		<fieldDescription><![CDATA[laudoTfd.medicamentosUso]]></fieldDescription>
	</field>
	<field name="caracterizacaoUrgencia" class="java.lang.String">
		<fieldDescription><![CDATA[laudoTfd.caracterizacaoUrgencia]]></fieldDescription>
	</field>
	<field name="historiaPregressa" class="java.lang.String">
		<fieldDescription><![CDATA[laudoTfd.historiaPregressa]]></fieldDescription>
	</field>
	<field name="estadoNutricional" class="java.lang.String">
		<fieldDescription><![CDATA[laudoTfd.estadoNutricional]]></fieldDescription>
	</field>
	<detail>
		<band height="91" splitType="Stretch">
			<textField>
				<reportElement x="78" y="2" width="428" height="12" uuid="466b7078-09b2-48af-aa97-7c9854789edb"/>
				<textElement>
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_estado_x", $P{estado}).toUpperCase()]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement x="470" y="56" width="81" height="10" uuid="bfe691d9-5e63-456c-9e4a-5092d786ba81"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.getDataAtual()]]></textFieldExpression>
				<patternExpression><![CDATA[$P{EXIBIR_HORARIO}
?
"dd/MM/yyyy HH:mm"
:
"dd/MM/yyyy"]]></patternExpression>
			</textField>
			<image isUsingCache="true" isLazy="true">
				<reportElement key="image-1" x="2" y="2" width="71" height="62" uuid="12b893ea-92ae-4c27-a610-0902e17529b5"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<imageExpression><![CDATA[$P{CAMINHO_IMAGEM_PADRAO}]]></imageExpression>
			</image>
			<textField>
				<reportElement x="78" y="17" width="428" height="10" uuid="5a938224-55be-422d-a643-46a0b57ed39a"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{UNIDADE_ATENDIMENTO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="78" y="30" width="394" height="10" uuid="37da9724-c7a3-4656-8a09-be5827357251"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{CABECALHO_ADICIONAL_1}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="78" y="43" width="394" height="10" uuid="3acb3567-de0e-41a6-902f-4cc8b651f912"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{CABECALHO_ADICIONAL_2}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="78" y="56" width="394" height="10" uuid="6467320c-4cbb-4532-8b62-39a7cdc8f213"/>
				<textElement>
					<font fontName="Arial" size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{CABECALHO_DIRETOR_TECNICO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="335" y="65" width="217" height="13" uuid="698ebd6f-4814-485f-8b1d-0d8a902a85e8"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_laudo_medico").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="314" y="77" width="239" height="13" uuid="cd6e9ceb-6e82-439e-aa0d-313ea66a5130"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{TITULO_REPORT}]]></textFieldExpression>
			</textField>
		</band>
		<band height="111" splitType="Stretch">
			<rectangle>
				<reportElement x="0" y="0" width="553" height="28" uuid="48a20dd4-4208-48a8-98d0-18c39f405f7f"/>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="31" width="553" height="28" uuid="fc4574e4-1d06-4c3f-8021-86e6c130ce4c"/>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="62" width="553" height="28" uuid="0e5da69a-91d6-4b95-b095-36e0ea630d94"/>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="93" width="553" height="17" uuid="a6f828a2-3d45-4f7f-97ac-e2b6d7aa238c"/>
			</rectangle>
			<line>
				<reportElement x="419" y="62" width="1" height="28" uuid="d6816538-f06b-4da3-a308-ad5296b79f81"/>
			</line>
			<line>
				<reportElement x="274" y="31" width="1" height="28" uuid="524749cb-ba2a-450f-a56c-766244b13586"/>
			</line>
			<line>
				<reportElement x="325" y="0" width="1" height="28" uuid="c337e041-5cf1-4b4c-b0f3-20c5b4d96f25"/>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="3" y="64" width="311" height="12" uuid="f081a22e-0cad-4ed7-836f-3233a2e35b24"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_diagnostico_inicial")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="3" y="95" width="128" height="12" uuid="f78529b5-44a5-4f23-971c-78f86afc7634"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_carater_atendimento")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="423" y="64" width="54" height="12" uuid="bde77857-ac30-44a5-9414-f6634b0dfe2e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cid10")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="3" y="33" width="80" height="12" uuid="5d3d0820-77cd-4675-89a8-ee0499f430ce"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_procedimento_solicitado")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="3" y="2" width="90" height="12" uuid="05856cbe-7322-4ed6-b471-253b8d3028fd"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nome_paciente")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="329" y="2" width="93" height="12" uuid="0d448250-ed67-463a-9d6c-c253155079a1"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_idade")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="278" y="33" width="204" height="12" uuid="59c62fd3-4b8a-4436-a6ce-ed228db9d5c3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_codigo_sia_sih_sus")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-10" x="6" y="77" width="406" height="12" uuid="9b12a560-3bf8-44bb-b38a-76e3ab1d9376"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cid} != null?
$F{cid}.getDescricao()
:""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="131" y="95" width="143" height="12" uuid="bec223ad-4baa-482f-94aa-5e3c5651eb98"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{laudoTfd}.getDescricaoPrioridade()!=null
    ?
        $F{caraterAtendimento} + " (" + $F{laudoTfd}.getDescricaoPrioridade() + ")"
    :
        $F{caraterAtendimento}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-10" x="426" y="77" width="121" height="12" uuid="ee864652-d1fe-4684-afdb-20406a5062a3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cid} != null?
$F{cid}.getCodigo()
:""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-8" x="6" y="46" width="238" height="12" uuid="2cd19e47-f87f-46b2-b02c-575fd3e001c3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{laudoTfd} != null && $F{laudoTfd}.getComplemento() != null)
?
    $F{tipoProcedimento}.getDescricao() + " (" + $F{laudoTfd}.getComplemento() + ")"
:
    $F{tipoProcedimento}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-10" x="6" y="15" width="317" height="12" uuid="93415977-1baf-431e-81c5-45245fb4d83d"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNome()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-10" x="332" y="15" width="114" height="12" uuid="cd758057-d953-4e77-8718-7017e15bdbd5"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.getDescricaoIdade($F{usuarioCadsus}.getDataNascimento(), $F{laudoTfd}.getDataCadastro())]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-8" x="281" y="46" width="266" height="12" uuid="a0b0fb7f-86f0-4f25-b0df-95abd1f37748"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{laudoTfd}!=null && $F{laudoTfd}.getProcedimento()!=null)?
$F{laudoTfd}.getProcedimento().getCodigoFormatado():""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="461" y="2" width="86" height="12" uuid="c7a68050-fbbb-4a5f-ad5e-3bcabf782dc8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_tipo_sanguineo")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-10" x="464" y="14" width="84" height="12" uuid="4189a726-5a9a-44de-a921-0f39b380cb3c"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getDescricaoTipoSanguineo()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="453" y="0" width="1" height="28" uuid="6a705b6f-9950-4ff7-b885-ade34d94e1b7"/>
			</line>
		</band>
		<band height="33">
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="8" width="553" height="24" isPrintWhenDetailOverflows="true" uuid="af66e5cf-1a4e-4dab-ad5f-01799cd0ace6"/>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" mode="Opaque" x="4" y="3" width="260" height="12" uuid="0febee7f-bbf4-414f-a014-76a8e3b5eb60"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_historico_doenca_principais_sinais_sintomas_clinicos")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="7" y="16" width="540" height="12" uuid="d43f204d-2e76-4538-9c90-5d5c45f8ef2f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Top">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{historicoDoenca}]]></textFieldExpression>
			</textField>
		</band>
		<band height="32">
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="7" width="553" height="24" isPrintWhenDetailOverflows="true" uuid="af66e5cf-1a4e-4dab-ad5f-01799cd0ace6"/>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" mode="Opaque" x="4" y="2" width="63" height="12" uuid="0febee7f-bbf4-414f-a014-76a8e3b5eb60"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_exame_fisico")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="7" y="15" width="540" height="12" uuid="d43f204d-2e76-4538-9c90-5d5c45f8ef2f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Top">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{exameFisico}]]></textFieldExpression>
			</textField>
		</band>
		<band height="32">
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="7" width="553" height="24" isPrintWhenDetailOverflows="true" uuid="af66e5cf-1a4e-4dab-ad5f-01799cd0ace6"/>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" mode="Opaque" x="4" y="2" width="350" height="12" uuid="0febee7f-bbf4-414f-a014-76a8e3b5eb60"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_diagnostico_relacionado_procedimento_solicitado")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="7" y="15" width="540" height="12" uuid="d43f204d-2e76-4538-9c90-5d5c45f8ef2f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Top">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{diagnosticoProvavel}]]></textFieldExpression>
			</textField>
		</band>
		<band height="32">
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="7" width="553" height="24" isPrintWhenDetailOverflows="true" uuid="af66e5cf-1a4e-4dab-ad5f-01799cd0ace6"/>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" mode="Opaque" x="4" y="2" width="284" height="12" uuid="0febee7f-bbf4-414f-a014-76a8e3b5eb60"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_principais_resultados_exames_complementares")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="7" y="15" width="540" height="12" uuid="d43f204d-2e76-4538-9c90-5d5c45f8ef2f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Top">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{exameComplementarRealizado}]]></textFieldExpression>
			</textField>
		</band>
		<band height="32">
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="7" width="553" height="24" isPrintWhenDetailOverflows="true" uuid="af66e5cf-1a4e-4dab-ad5f-01799cd0ace6"/>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" mode="Opaque" x="4" y="2" width="108" height="12" uuid="0febee7f-bbf4-414f-a014-76a8e3b5eb60"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_tratamentos_realizados")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="7" y="15" width="540" height="12" uuid="d43f204d-2e76-4538-9c90-5d5c45f8ef2f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Top">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tratamentoRealizado}]]></textFieldExpression>
			</textField>
		</band>
		<band height="32">
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="7" width="553" height="24" isPrintWhenDetailOverflows="true" uuid="cfb535fd-7aee-43f1-a9fc-911d357e5dd7"/>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" mode="Opaque" x="4" y="2" width="214" height="12" uuid="b4b0bc2d-af03-4e84-ba02-d1ac089803b6"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_justificativa_internacao_consulta")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="7" y="15" width="540" height="12" uuid="8a79d784-c2e5-4c84-951b-926ff0e77897"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Top">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{justificativaInternacaoConsulta}]]></textFieldExpression>
			</textField>
		</band>
		<band height="32">
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="7" width="553" height="24" isPrintWhenDetailOverflows="true" uuid="cfb535fd-7aee-43f1-a9fc-911d357e5dd7"/>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" mode="Opaque" x="4" y="2" width="192" height="12" uuid="b4b0bc2d-af03-4e84-ba02-d1ac089803b6"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_avaliacao_clinica_geral")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="7" y="15" width="540" height="12" uuid="8a79d784-c2e5-4c84-951b-926ff0e77897"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Top">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{avaliacaoClinicaGeral}]]></textFieldExpression>
			</textField>
		</band>
		<band height="32">
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="7" width="553" height="24" isPrintWhenDetailOverflows="true" uuid="cfb535fd-7aee-43f1-a9fc-911d357e5dd7"/>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" mode="Opaque" x="4" y="2" width="248" height="12" uuid="b4b0bc2d-af03-4e84-ba02-d1ac089803b6"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_medicamento_uso_dose")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="7" y="15" width="540" height="12" uuid="8a79d784-c2e5-4c84-951b-926ff0e77897"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Top">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{medicamentoEmUsoDose}]]></textFieldExpression>
			</textField>
		</band>
		<band height="32">
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="7" width="553" height="24" isPrintWhenDetailOverflows="true" uuid="cfb535fd-7aee-43f1-a9fc-911d357e5dd7"/>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" mode="Opaque" x="4" y="2" width="152" height="12" uuid="b4b0bc2d-af03-4e84-ba02-d1ac089803b6"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_estado_nutricional_imc_outros")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="7" y="15" width="540" height="12" uuid="8a79d784-c2e5-4c84-951b-926ff0e77897"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Top">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{estadoNutricional}]]></textFieldExpression>
			</textField>
		</band>
		<band height="32">
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="7" width="553" height="24" isPrintWhenDetailOverflows="true" uuid="cfb535fd-7aee-43f1-a9fc-911d357e5dd7"/>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" mode="Opaque" x="4" y="2" width="302" height="12" uuid="b4b0bc2d-af03-4e84-ba02-d1ac089803b6"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_caracterizacao_eventual_urgencia_realizacao_procedimento")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="7" y="15" width="540" height="12" uuid="8a79d784-c2e5-4c84-951b-926ff0e77897"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Top">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{caracterizacaoUrgencia}]]></textFieldExpression>
			</textField>
		</band>
		<band height="44">
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="7" width="553" height="36" isPrintWhenDetailOverflows="true" uuid="cfb535fd-7aee-43f1-a9fc-911d357e5dd7"/>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" mode="Opaque" x="4" y="2" width="543" height="24" uuid="b4b0bc2d-af03-4e84-ba02-d1ac089803b6"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_historia_pregressa_atual_significativa")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="7" y="27" width="540" height="12" uuid="8a79d784-c2e5-4c84-951b-926ff0e77897"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Top">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{justificativaInternacaoConsulta}]]></textFieldExpression>
			</textField>
		</band>
		<band height="32">
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="7" width="553" height="24" isPrintWhenDetailOverflows="true" uuid="af66e5cf-1a4e-4dab-ad5f-01799cd0ace6"/>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" mode="Opaque" x="4" y="2" width="398" height="12" uuid="0febee7f-bbf4-414f-a014-76a8e3b5eb60"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_justificar_razoes_impossibilitam_realizacao_tratamento")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="7" y="16" width="540" height="12" uuid="d43f204d-2e76-4538-9c90-5d5c45f8ef2f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Top">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{justificativaTfd}]]></textFieldExpression>
			</textField>
		</band>
		<band height="32">
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="7" width="553" height="24" isPrintWhenDetailOverflows="true" uuid="af66e5cf-1a4e-4dab-ad5f-01799cd0ace6"/>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" mode="Opaque" x="4" y="2" width="236" height="12" uuid="0febee7f-bbf4-414f-a014-76a8e3b5eb60"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_justificar_caso_necessidade_acompanhante")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="7" y="16" width="540" height="12" uuid="d43f204d-2e76-4538-9c90-5d5c45f8ef2f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Top">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{justificativaAcompanhante}]]></textFieldExpression>
			</textField>
		</band>
		<band height="32">
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="7" width="553" height="24" isPrintWhenDetailOverflows="true" uuid="af66e5cf-1a4e-4dab-ad5f-01799cd0ace6"/>
			</rectangle>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="7" y="16" width="540" height="12" uuid="d43f204d-2e76-4538-9c90-5d5c45f8ef2f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Top">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{transporteRecomendavel}+": "+
($F{justificativaTransporte}!=null?$F{justificativaTransporte}:"")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" mode="Opaque" x="4" y="2" width="464" height="12" uuid="0febee7f-bbf4-414f-a014-76a8e3b5eb60"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_transporte_recomendavel_caso_aereo_ambulancia_obrigatorio_justificar")]]></textFieldExpression>
			</textField>
		</band>
		<band height="119">
			<rectangle>
				<reportElement x="0" y="33" width="553" height="35" uuid="f7d676d9-1582-45e2-9e38-f23e59dd8bf9"/>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="71" width="553" height="28" uuid="86e0f810-683a-4e2f-b423-f67fd6d5925d"/>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="0" y="2" width="553" height="28" uuid="4d211b64-9a38-4835-aedf-5b839556ef22"/>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-10" x="197" y="35" width="236" height="12" uuid="78afddb9-9a22-407b-8fdd-678cdd061d1e"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_assinatura_carimbo_profissional_solicitante")+":"]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="285" y="2" width="1" height="28" uuid="5f87a996-782c-4140-84dd-8f424af105b9"/>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-10" x="4" y="4" width="127" height="12" uuid="0b58b6ca-fc3d-4d70-9167-d26b8840553c"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_local_e_data")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="7" y="17" width="275" height="12" uuid="4266bf77-a703-4067-8c87-f33bcc6010f1"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{laudoTfd}.getEmpresa().getDescricao() + ", " + Data.formatar($F{laudoTfd}.getDataLaudo())]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="95" y="33" width="1" height="35" uuid="57b15253-b3a2-46b9-927a-ac84e176923f"/>
			</line>
			<line>
				<reportElement x="192" y="33" width="1" height="35" uuid="57b15253-b3a2-46b9-927a-ac84e176923f"/>
			</line>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-10" x="293" y="17" width="259" height="12" uuid="91272cf2-f266-4790-a41a-ed9b99d5b0bb"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{profissional}.getNome()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="261" y="73" width="96" height="12" uuid="cb21ef7b-aac2-4054-9e5e-997a5c23dfcb"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_telefone_unidade")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="3" y="35" width="70" height="12" uuid="3fe77035-17d2-4b48-b8c1-cf91243c0ed3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cpf")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="290" y="4" width="156" height="12" uuid="4995d424-ab63-4ee1-b72b-919958cb6fdd"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nome_medico_solicitante")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="264" y="86" width="140" height="12" uuid="b03a4f04-cbb0-4cd2-962b-dad64754e51b"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{laudoTfd}.getEmpresa().getTelefoneFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="6" y="48" width="87" height="12" uuid="491d565f-0321-49cf-bc37-5c31f30d345d"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{profissional}.getCpfFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="417" y="85" width="134" height="12" uuid="b03a4f04-cbb0-4cd2-962b-dad64754e51b"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="414" y="72" width="68" height="12" uuid="cb21ef7b-aac2-4054-9e5e-997a5c23dfcb"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_celular")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="4" y="73" width="121" height="12" uuid="c5d227a2-fc62-41d2-a363-d0f7976f8aac"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_numero_cns_medico")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="101" y="35" width="82" height="12" uuid="2fe4b0b5-cf3f-4667-8561-1d455040fbf8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_numero_cnes")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="104" y="48" width="84" height="12" uuid="46eb8693-058c-4a04-88a1-528d87369a61"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{laudoTfd}.getEmpresa().getCnes()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="409" y="71" width="1" height="28" uuid="65de411d-09fb-409a-b413-61f7b0b26492"/>
			</line>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-10" x="7" y="86" width="245" height="12" uuid="db3bbfd4-40f1-4a1e-9c58-836cfcf2b3fb"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{profissional}.getCodigoCns()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="255" y="71" width="1" height="28" uuid="437b3577-7c2d-4636-874e-9a19846154ff"/>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="0" y="101" width="553" height="18" uuid="8b90a12e-bd19-4aeb-bf54-1963681fbe53"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Top">
					<font fontName="Arial" size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_msg_obs_laudo")]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
