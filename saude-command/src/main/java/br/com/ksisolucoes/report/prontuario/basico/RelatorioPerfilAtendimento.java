package br.com.ksisolucoes.report.prontuario.basico;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.basico.query.QueryRelatorioPerfilAtendimento;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioPerfilAtendimentoDTOParam;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;

/**
 *
 * <AUTHOR>
 */
public class RelatorioPerfilAtendimento extends AbstractReport<RelatorioPerfilAtendimentoDTOParam> {

    public RelatorioPerfilAtendimento(RelatorioPerfilAtendimentoDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_perfil_atendimento.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relatorio_perfil_atendimento");
    }

    @Override
    public ITransferDataReport getQuery() {
        // Pegar parâmetro gem do projeto command
        try {
            String exibirMedicamentosDispensados=BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("ExibirMedicamentosDispensados");
            if(RepositoryComponentDefault.SIM.equals(exibirMedicamentosDispensados) || CollectionUtils.isEmpty(param.getProfissionais())){
                this.addParametro("exibirMedicamentosDispensados", true);
            }else{
                this.addParametro("exibirMedicamentosDispensados", false);
            }
            this.addParametro("visualizarBairros", RepositoryComponentDefault.SIM_LONG.equals(param.getVisualizarBairros()));
            this.addParametro("visualizarMotivoAlta", RepositoryComponentDefault.SIM_LONG.equals(param.getVisualizarMotivoAlta()));

        } catch (DAOException e) {
        }
        return new QueryRelatorioPerfilAtendimento();
    }



}
