package br.com.ksisolucoes.report.consorcio.licitacao;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.consorcio.dto.ResumoPedidosLicitacaoDTOParam;
import br.com.ksisolucoes.report.consorcio.licitacao.query.QueryRelatorioResumoPedidosLicitacao;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioResumoPedidosLicitacao extends AbstractReport<ResumoPedidosLicitacaoDTOParam> {

    public RelatorioResumoPedidosLicitacao(ResumoPedidosLicitacaoDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/consorcio/licitacao/jrxml/relatorio_resumo_pedidos_licitacao.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_resumo_pedidos_licitacao");
    }

    @Override
    public ITransferDataReport getQuery() {
        addParametro("formaApresentacao", getParam().getFormaApresentacao());
        
        return new QueryRelatorioResumoPedidosLicitacao();
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return getParam().getTipoArquivo();
    }
    
}
