package br.com.ksisolucoes.report.agendamento.solicitacaoagendamento;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.agendamento.solicitacaoagendamento.query.QueryImpressaoLoteSolicitacao;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.bo.UnidadeHelper;
import br.com.ksisolucoes.vo.agendamento.LoteSolicitacaoAgendamento;

/**
 *
 * <AUTHOR>
 */
public class RelatorioLoteSolicitacaoAgendamento extends AbstractReport<LoteSolicitacaoAgendamento> {

    
    
    public RelatorioLoteSolicitacaoAgendamento(LoteSolicitacaoAgendamento param) {
        super(param);
    }

    @Override
    public String getXML() {
        addParametro("isCentralAgendamento", UnidadeHelper.isCentralAgendamento());
        return "/br/com/ksisolucoes/report/agendamento/solicitacaoagendamento/jrxml/relatorio_impressao_lote_envio_solicitacoes.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_impressao_lote_envio_solicitacoes");
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryImpressaoLoteSolicitacao();
    }
}
