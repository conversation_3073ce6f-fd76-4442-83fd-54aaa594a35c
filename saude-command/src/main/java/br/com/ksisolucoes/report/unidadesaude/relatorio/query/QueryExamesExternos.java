package br.com.ksisolucoes.report.unidadesaude.relatorio.query;

import br.com.celk.examesExternosTestesRapidos.dto.ExameExternoTesteRapidoDTO;
import br.com.celk.unidadesaude.esus.relatorios.dto.RelatorioExamesExternosTestesRapidosDTOParam;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.unidadesaude.relatorio.utils.RelatorioExamesExternosTestesRapidosUtil;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import ch.lambdaj.Lambda;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.SQLQuery;
import org.hibernate.mapping.Collection;
import org.hibernate.transform.Transformers;
import org.hibernate.type.DateType;
import org.hibernate.type.StringType;

import java.util.List;

public class QueryExamesExternos extends AbstractCommandTransaction<ExameExternoTesteRapidoDTO> {

    private RelatorioExamesExternosTestesRapidosDTOParam param;
    private List<ExameExternoTesteRapidoDTO> examesExternos;

    public QueryExamesExternos(RelatorioExamesExternosTestesRapidosDTOParam param) {
        this.param = param;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        HQLHelper hql = this.createQuery();
        SQLQuery query = getSession().createSQLQuery(hql.getQuery());
        RelatorioExamesExternosTestesRapidosUtil.setQueryParameters(param, query);
        this.addScalar(query);
        examesExternos = query.list();
        if (CollectionUtils.isNotEmpty(examesExternos)) {
            Lambda.forEach(examesExternos).setCategoriaExame(RelatorioExamesExternosTestesRapidosDTOParam.CategoriaExame.EXAME_EXTERNO.descricao());
        }
    }

    private HQLHelper createQuery() {
        HQLHelper hql = new HQLHelper();
        hql.setTypeSelect(ExameExternoTesteRapidoDTO.class.getName());
        hql.setUseSQL(true);

        // CAMPOS
        hql.addToSelect("usuario_cadsus.nm_usuario", "nomePaciente");
        hql.addToSelect("exame_externo.ds_exame", "tipoExame");
        hql.addToSelect("exame_externo.ds_resultado", "resultado");
        hql.addToSelect("exame_externo.dt_exame", "dataExame");
        hql.addToSelect("empresa.descricao", "unidadeSaude");
        hql.addToSelect("equipe_area_1.ds_area", "equipeReferencia1");
        hql.addToSelect("equipe_area_3.ds_area", "equipeReferencia2");
        hql.addToSelect("equipe_area_3.ds_area", "equipeReferencia3");

        hql.addToFrom("exame_externo"
                + " left join usuario_cadsus on exame_externo.cd_usu_cadsus = usuario_cadsus.cd_usu_cadsus"
                + " left join atendimento on exame_externo.nr_atendimento = atendimento.nr_atendimento"
                + " left join empresa on atendimento.empresa = empresa.empresa"
                + " left join endereco_usuario_cadsus on endereco_usuario_cadsus.cd_endereco = usuario_cadsus.cd_endereco "
                + " left join endereco_estruturado on endereco_estruturado.cd_endereco_estruturado = endereco_usuario_cadsus.cd_endereco_estruturado "
                + " left join equipe_micro_area equipe_micro_area_1 on equipe_micro_area_1.cd_eqp_micro_area = endereco_estruturado.cd_eqp_micro_area "
                + " left join endereco_domicilio on endereco_domicilio.cd_endereco = endereco_usuario_cadsus.cd_endereco "
                + " left join equipe_micro_area equipe_micro_area_2 on equipe_micro_area_2.cd_eqp_micro_area = endereco_domicilio.cd_eqp_micro_area "
                + " left join equipe_area equipe_area_1 on usuario_cadsus.cd_equipe = equipe_area_1.cd_equipe_area "
                + " left join equipe_area equipe_area_2 on equipe_micro_area_1.cd_equipe_area = equipe_area_2.cd_equipe_area "
                + " left join equipe_area equipe_area_3 on equipe_micro_area_2.cd_equipe_area = equipe_area_3.cd_equipe_area "
        );

        // PARÂMETROS
        hql.addToWhereWhithAnd(hql.getConsultaLiked("exame_externo.ds_exame","Vírus Respiratórios RT-PCR"));
        if (param.getEmpresa() != null) {
            hql.addToWhereWhithAnd("empresa.empresa = :codigoEmpresa");
        }
        if (param.getPeriodo() != null) {
            hql.addToWhereWhithAnd("(exame_externo.dt_exame >= :dataInicial and exame_externo.dt_exame <= :dataFinal)");
        }
        return hql;
    }

    private void addScalar(SQLQuery sql) {
        sql.addScalar("nomePaciente", StringType.INSTANCE)
                .addScalar("tipoExame", StringType.INSTANCE)
                .addScalar("dataExame", DateType.INSTANCE)
                .addScalar("resultado", StringType.INSTANCE)
                .addScalar("unidadeSaude", StringType.INSTANCE)
                .addScalar("equipeReferencia1", StringType.INSTANCE)
                .addScalar("equipeReferencia2", StringType.INSTANCE)
                .addScalar("equipeReferencia3", StringType.INSTANCE)
                .setResultTransformer(Transformers.aliasToBean(ExameExternoTesteRapidoDTO.class));
    }

    public List<ExameExternoTesteRapidoDTO> getResult() {
        return examesExternos;
    }
}
