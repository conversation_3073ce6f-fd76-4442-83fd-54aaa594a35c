<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_atendimentos_odontologicos" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="782" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="6f31b925-6113-4871-9c16-6e61d4153d2a">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.3310000000000015"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.AtendimentoOdontoFicha.Status"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<style name="Crosstab Data Text" hAlign="Center"/>
	<parameter name="situacao" class="java.lang.Long"/>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<field name="atendimentoOdontoFicha" class="br.com.ksisolucoes.vo.prontuario.basico.AtendimentoOdontoFicha"/>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="idadeDataAtendimento" class="java.lang.String"/>
	<field name="profissionalConclusao" class="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<field name="profissionalCancelamento" class="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<field name="ultimaDataAtendimento" class="java.lang.String"/>
	<field name="nomeProfissional" class="java.lang.String"/>
	<field name="descricaoUnidade" class="java.lang.String"/>
	<variable name="total" class="java.lang.Long" resetType="Group" resetGroup="unidade" calculation="Count">
		<variableExpression><![CDATA[$F{usuarioCadsus}.getCodigo()]]></variableExpression>
	</variable>
	<variable name="totalGeral" class="java.lang.Long" calculation="Count">
		<variableExpression><![CDATA[$F{usuarioCadsus}.getCodigo()]]></variableExpression>
	</variable>
	<group name="padrao">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupFooter>
			<band height="15">
				<textField>
					<reportElement x="679" y="4" width="42" height="11" uuid="539362c3-abd4-4ca9-b755-3818a8a7c452"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_geral") + ":"]]></textFieldExpression>
				</textField>
				<textField pattern="###0;-###0">
					<reportElement x="721" y="4" width="61" height="11" uuid="18ecfb84-33d7-4afb-a40f-c6db1d0e6553"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalGeral}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="679" y="3" width="103" height="1" uuid="6917fd85-cdc4-4b3c-a051-504cb7eb9890"/>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="unidade">
		<groupExpression><![CDATA[$F{empresa}]]></groupExpression>
		<groupHeader>
			<band height="32">
				<rectangle radius="10">
					<reportElement x="2" y="0" width="780" height="14" uuid="87430372-fc82-4da8-a840-1a879cdf7f36"/>
				</rectangle>
				<textField>
					<reportElement x="9" y="1" width="44" height="13" uuid="3e0938ef-4db9-4c66-8f43-e9679e842216"/>
					<textElement>
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_unidade") + ":"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="56" y="1" width="331" height="13" uuid="3e0938ef-4db9-4c66-8f43-e9679e842216"/>
					<textElement>
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{empresa}.getDescricao()]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="0" y="19" width="171" height="11" uuid="3e0938ef-4db9-4c66-8f43-e9679e842216"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_paciente")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement positionType="Float" x="0" y="31" width="782" height="1" uuid="650fc19b-537b-4a79-aabb-00fb9f306ff7"/>
				</line>
				<textField>
					<reportElement positionType="Float" x="173" y="19" width="25" height="11" uuid="3e0938ef-4db9-4c66-8f43-e9679e842216"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_sexo")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement positionType="Float" x="200" y="19" width="50" height="11" uuid="3e0938ef-4db9-4c66-8f43-e9679e842216"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_idade")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement positionType="Float" x="251" y="19" width="148" height="11" uuid="3e0938ef-4db9-4c66-8f43-e9679e842216"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_profissional")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement positionType="Float" x="401" y="19" width="43" height="11" uuid="3e0938ef-4db9-4c66-8f43-e9679e842216"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dt_inicio")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement positionType="Float" x="447" y="19" width="68" height="11" uuid="*************-4e3a-a81d-7ec3331d9991"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[($P{situacao} == null || Status.CONCLUIDO.value().equals($P{situacao})) ?
    Bundle.getStringApplication("rotulo_data_conclusao")
:
    (Status.CANCELADO.value().equals($P{situacao})) ?
        Bundle.getStringApplication("rotulo_dt_cancelamento")
    :
        (Status.EM_ANDAMENTO.value().equals($P{situacao})) ?
            Bundle.getStringApplication("rotulo_ult_atendimento")
        :
            ""]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement positionType="Float" x="647" y="19" width="135" height="11" uuid="e43aa218-73f3-4e9b-9da0-4a534919d624"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[(Status.CONCLUIDO.value().equals($P{situacao})) ?
    Bundle.getStringApplication("rotulo_observacao")
:
    (Status.CANCELADO.value().equals($P{situacao})) ?
        Bundle.getStringApplication("rotulo_motivo")
    :
        (Status.EM_ANDAMENTO.value().equals($P{situacao})) ?
            Bundle.getStringApplication("rotulo_unidade")
        :
            ""]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement positionType="Float" x="517" y="19" width="129" height="11" uuid="37c0f5f5-47ba-4641-8c3a-56229114c6c8"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[(Status.CONCLUIDO.value().equals($P{situacao})) ?
    Bundle.getStringApplication("rotulo_profissional_conclusao")
:
    (Status.CANCELADO.value().equals($P{situacao})) ?
        Bundle.getStringApplication("rotulo_profissional_cancelamento")
    :
        (Status.EM_ANDAMENTO.value().equals($P{situacao})) ?
            Bundle.getStringApplication("rotulo_ult_profissional")
        :
            ($P{situacao} == null) ?
                Bundle.getStringApplication("rotulo_situacao")
            :
                ""]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="20">
				<textField>
					<reportElement x="700" y="4" width="21" height="11" uuid="2ed12f3e-3838-4b10-a934-25a928930d02"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total") + ":"]]></textFieldExpression>
				</textField>
				<textField pattern="###0;-###0">
					<reportElement x="721" y="4" width="61" height="11" uuid="f6250f95-8b88-417b-a06e-0dd66bd4105b"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{total}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="700" y="3" width="82" height="1" uuid="687a391d-bfed-41ff-996e-e992fe65e93d"/>
				</line>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="12" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="1" width="171" height="11" uuid="a482b973-ab90-4757-926b-15bc1d633e7b"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNome()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="173" y="1" width="25" height="11" uuid="83dea6e6-8e4c-4da3-b336-773480db7eec"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getSexoAbreviadoFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="200" y="1" width="50" height="11" uuid="3d10f03e-0dd6-45da-833e-32370c0d678c"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{idadeDataAtendimento}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="251" y="1" width="148" height="11" uuid="94da9e46-033b-440a-a9f0-d37c6539c922"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{profissional}.getNome()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement positionType="Float" x="401" y="1" width="43" height="11" uuid="0aa65618-8478-4c74-b970-e51848fa4a8e"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{atendimentoOdontoFicha}.getDataInicioTratamento()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement positionType="Float" x="447" y="1" width="68" height="11" uuid="a4810a99-5543-476b-898a-1a4ca5de3d39"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{situacao} == null || Status.CONCLUIDO.value().equals($P{situacao})) ?
    $F{atendimentoOdontoFicha}.getDataConclusao()
:
    (Status.CANCELADO.value().equals($P{situacao})) ?
        $F{atendimentoOdontoFicha}.getDataCancelamento()
    :
        (Status.EM_ANDAMENTO.value().equals($P{situacao})) ?
            $F{ultimaDataAtendimento}
        :
            ""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement positionType="Float" x="647" y="1" width="135" height="11" uuid="3918e48e-963f-486e-a0eb-56ca01a0f7ea"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[(Status.CONCLUIDO.value().equals($P{situacao})) ?
    $F{atendimentoOdontoFicha}.getObservacaoConclusao()
:
    (Status.CANCELADO.value().equals($P{situacao})) ?
        $F{atendimentoOdontoFicha}.getMotivoCancelamento()
    :
        (Status.EM_ANDAMENTO.value().equals($P{situacao})) ?
            $F{descricaoUnidade}
        :
            ""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement positionType="Float" x="517" y="1" width="129" height="11" uuid="8c5ac8af-0b30-4701-b55e-d910216e50c5"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[(Status.CONCLUIDO.value().equals($P{situacao})) ?
    $F{profissionalConclusao}.getNome()
:
    (Status.CANCELADO.value().equals($P{situacao})) ?
        $F{profissionalCancelamento}.getNome()
    :
        (Status.EM_ANDAMENTO.value().equals($P{situacao})) ?
            $F{nomeProfissional}
        :
            ($P{situacao} == null) ?
                $F{atendimentoOdontoFicha}.getDescricaoStatus()
            :
                ""]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
