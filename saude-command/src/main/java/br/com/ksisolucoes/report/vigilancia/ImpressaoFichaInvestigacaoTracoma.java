package br.com.ksisolucoes.report.vigilancia;

import br.com.celk.report.vigilancia.query.QueryFichaTracoma;
import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FichaInvestigacaoAgravoDTOParam;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;

import java.util.LinkedHashMap;

public class ImpressaoFichaInvestigacaoTracoma extends AbstractReport<FichaInvestigacaoAgravoDTOParam> {

    private QueryFichaTracoma query;

    public ImpressaoFichaInvestigacaoTracoma(FichaInvestigacaoAgravoDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        if(query == null){
            query = new QueryFichaTracoma();
        }
        return query;
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/vigilancia/pdf/ficha_investigacao_tracoma.pdf";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_tracoma");
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return param.getTipoArquivo();
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> campos = new LinkedHashMap<>(((QueryFichaTracoma)getQuery()).getMapeamentoPlanilhaBase());

        campos.put("codigo", "investigacaoAgravo_codigo");

        campos.put("inquerito", "_07_inquerito");
        campos.put("numeroPessoasExaminadas", "_08_nro_pessoas_examinadas");
        campos.put("numeroCasosPositivos", "_09_nro_casos_positivos");

        campos.put("observacao", "_observacao");

        return campos;
    }
}
