<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_produto_unidade" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.util.Util"/>
	<import value="br.com.ksisolucoes.util.Coalesce"/>
	<import value="br.com.ksisolucoes.report.ReportProperties"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="org.apache.commons.lang.StringUtils"/>
	<parameter name="FORMA_APRESENTACAO" class="java.lang.Long" isForPrompting="false"/>
	<parameter name="EXIBIR_QTD_PADRAO" class="java.lang.String" isForPrompting="false"/>
	<parameter name="EMPRESA_NOVA_PAGINA" class="java.lang.Boolean"/>
	<field name="empresaCodigo" class="java.lang.String"/>
	<field name="empresaDescricao" class="java.lang.String"/>
	<field name="grupoCodigo" class="java.lang.Long"/>
	<field name="grupoDescricao" class="java.lang.String"/>
	<field name="subGrupoCodigo" class="java.lang.Long"/>
	<field name="subGrupoDescricao" class="java.lang.String"/>
	<field name="produtoCodigo" class="java.lang.String"/>
	<field name="produtoDescricao" class="java.lang.String"/>
	<field name="grupoDescricaoFormatado" class="java.lang.String"/>
	<field name="subGrupoDescricaoFormatado" class="java.lang.String"/>
	<field name="empresaDescricaoFormatado" class="java.lang.String"/>
	<field name="unidadeUnidade" class="java.lang.String"/>
	<field name="produtoDescricaoFormatado" class="java.lang.String"/>
	<field name="estoqueMinimo" class="java.lang.Double"/>
	<field name="quantidadePadraoDispensacao" class="java.lang.Double"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<group name="GrupoEmpresaNovaPagina" isStartNewPage="true">
		<groupExpression><![CDATA[$P{EMPRESA_NOVA_PAGINA} ? $F{empresaCodigo} : null]]></groupExpression>
		<groupHeader>
			<band height="17">
				<printWhenExpression><![CDATA[$P{EMPRESA_NOVA_PAGINA}]]></printWhenExpression>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-43" mode="Opaque" x="0" y="1" width="535" height="15" printWhenGroupChanges="GrupoEmpresa" forecolor="#000000" backcolor="#CCCCCC"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*empresa*/$V{BUNDLE}.getStringApplication("rotulo_empresa") + ": " + Util.getDescricaoFormatado(
                $F{empresaCodigo},
                StringUtils.trimToEmpty( $F{empresaDescricao} ) )]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="GrupoEmpresa" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{empresaCodigo}]]></groupExpression>
		<groupHeader>
			<band height="17" splitType="Stretch">
				<printWhenExpression><![CDATA[!$P{EMPRESA_NOVA_PAGINA}]]></printWhenExpression>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-43" mode="Opaque" x="0" y="1" width="535" height="15" printWhenGroupChanges="GrupoEmpresa" forecolor="#000000" backcolor="#CCCCCC"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*empresa*/$V{BUNDLE}.getStringApplication("rotulo_empresa") + ": " + Util.getDescricaoFormatado(
                $F{empresaCodigo},
                StringUtils.trimToEmpty( $F{empresaDescricao} ) )]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<group name="GrupoGrupoProduto" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$P{FORMA_APRESENTACAO}.intValue() == ReportProperties.AGRUPAR_GRUPO ?
$F{grupoCodigo}.toString() + "." + $F{subGrupoCodigo}.toString() :
"1"]]></groupExpression>
		<groupHeader>
			<band height="18" splitType="Stretch">
				<printWhenExpression><![CDATA[$P{FORMA_APRESENTACAO}.intValue() == ReportProperties.AGRUPAR_GRUPO]]></printWhenExpression>
				<textField evaluationTime="Group" evaluationGroup="GrupoGrupoProduto" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-50" mode="Opaque" x="0" y="3" width="535" height="15" printWhenGroupChanges="GrupoGrupoProduto" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_grupo") + ": " + $F{grupoDescricao} + " - " + $V{BUNDLE}.getStringApplication("rotulo_subgrupo") + ": " + $F{subGrupoDescricao}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<group name="grupoCab" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[1]]></groupExpression>
		<groupHeader>
			<band height="15" splitType="Stretch">
				<rectangle radius="5">
					<reportElement key="rectangle-1" mode="Opaque" x="0" y="2" width="535" height="13" printWhenGroupChanges="grupoCab" forecolor="#000000" backcolor="#FFFFFF"/>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</rectangle>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-5" mode="Opaque" x="4" y="3" width="285" height="10" printWhenGroupChanges="grupoCab" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*produto*/$V{BUNDLE}.getStringApplication("rotulo_produto")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-6" mode="Opaque" x="293" y="3" width="40" height="10" printWhenGroupChanges="grupoCab" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*unidade*/$V{BUNDLE}.getStringApplication("rotulo_unidade_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-7" mode="Opaque" x="338" y="3" width="60" height="10" printWhenGroupChanges="grupoCab" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{EXIBIR_QTD_PADRAO}.equals(br.com.ksisolucoes.util.validacao.RepositoryComponentDefault.SIM)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*estoque fisico*/$V{BUNDLE}.getStringApplication("rotulo_quantidade_padrao")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-52" mode="Opaque" x="460" y="3" width="51" height="10" printWhenGroupChanges="grupoCab" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*pedido*/$V{BUNDLE}.getStringApplication("rotulo_pedido")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-54" mode="Opaque" x="404" y="3" width="51" height="10" printWhenGroupChanges="grupoCab" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[/*pedido*/$V{BUNDLE}.getStringApplication("rotulo_estoque_atual")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="15" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Opaque" x="4" y="5" width="285" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="styled">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{produtoDescricaoFormatado}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-46" mode="Opaque" x="293" y="5" width="40" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{unidadeUnidade}]]></textFieldExpression>
			</textField>
			<line direction="BottomUp">
				<reportElement key="line-1" mode="Opaque" x="471" y="14" width="39" height="1" forecolor="#000000" backcolor="#FFFFFF"/>
				<graphicElement fill="Solid">
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-53" mode="Opaque" x="337" y="5" width="61" height="10" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[$P{EXIBIR_QTD_PADRAO}.equals(br.com.ksisolucoes.util.validacao.RepositoryComponentDefault.SIM)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[Coalesce.asDouble($F{quantidadePadraoDispensacao})]]></textFieldExpression>
			</textField>
			<line direction="BottomUp">
				<reportElement key="line-2" mode="Opaque" x="416" y="14" width="39" height="1" forecolor="#000000" backcolor="#FFFFFF"/>
				<graphicElement fill="Solid">
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
