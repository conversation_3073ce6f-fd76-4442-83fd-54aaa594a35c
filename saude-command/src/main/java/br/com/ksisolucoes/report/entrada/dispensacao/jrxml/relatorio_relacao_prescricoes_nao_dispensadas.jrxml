<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_prescricoes_nao_dispensadas" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="782" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="c2195d67-e019-48cb-916a-f6ea2e8b5623">
	<property name="ireport.zoom" value="2.1435888100000207"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioRelacaoPrescricoesEmAbertoDTOParam.FormaApresentacao"/>
	<import value="br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioRelacaoPrescricoesEmAbertoDTOParam"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<parameter name="FORMA_APRESENTACAO" class="br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioRelacaoPrescricoesEmAbertoDTOParam.FormaApresentacao"/>
	<field name="dataCadastro" class="java.util.Date"/>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<field name="produto" class="br.com.ksisolucoes.vo.entradas.estoque.Produto"/>
	<field name="paciente" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="convenio" class="br.com.ksisolucoes.vo.prontuario.basico.Convenio"/>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="receituario" class="br.com.ksisolucoes.vo.prontuario.basico.Receituario"/>
	<group name="geral">
		<groupHeader>
			<band/>
		</groupHeader>
		<groupFooter>
			<band height="5"/>
		</groupFooter>
	</group>
	<group name="FA" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[FormaApresentacao.UNIDADE.equals($P{FORMA_APRESENTACAO})
?
    $F{empresa}
:
    (FormaApresentacao.PACIENTE.equals($P{FORMA_APRESENTACAO})
    ?
        $F{paciente}
    :
        (FormaApresentacao.PROFISSIONAL.equals($P{FORMA_APRESENTACAO})
        ?
            $F{profissional}
        :
            (FormaApresentacao.CONVENIO.equals($P{FORMA_APRESENTACAO})
            ?
                $F{convenio}.getCodigo()
            :
                "" ) ) )]]></groupExpression>
		<groupHeader>
			<band height="40">
				<rectangle radius="5">
					<reportElement uuid="a70ccfdd-3925-4687-a705-bc0a2c6e548d" mode="Transparent" x="0" y="7" width="782" height="14">
						<printWhenExpression><![CDATA[!FormaApresentacao.GERAL.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
					</reportElement>
				</rectangle>
				<line>
					<reportElement uuid="57da07a5-cf42-420f-a451-b5eb0606af27" x="0" y="39" width="782" height="1"/>
				</line>
				<textField isBlankWhenNull="true">
					<reportElement uuid="2da98140-7877-43b3-b914-88ad05550993" x="6" y="8" width="766" height="13" isRemoveLineWhenBlank="true">
						<printWhenExpression><![CDATA[!FormaApresentacao.GERAL.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="10" isBold="true" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[FormaApresentacao.UNIDADE.equals($P{FORMA_APRESENTACAO})
?
    Bundle.getStringApplication("rotulo_empresa") +": "+ $F{empresa}.getDescricaoFormatado()
:
    FormaApresentacao.PACIENTE.equals($P{FORMA_APRESENTACAO})
    ?
        Bundle.getStringApplication("rotulo_paciente") +": "+ $F{paciente}.getDescricaoFormatado()
    :
        FormaApresentacao.PROFISSIONAL.equals($P{FORMA_APRESENTACAO})
        ?
            Bundle.getStringApplication("rotulo_profissional") +": "+ $F{profissional}.getDescricaoFormatado()
        :
            FormaApresentacao.CONVENIO.equals($P{FORMA_APRESENTACAO})
            ?
                Bundle.getStringApplication("rotulo_convenio") +": "+ ($F{convenio}.getDescricao() != null ? $F{convenio}.getDescricaoFormatado() : "Sem convênio")
            :
                ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="f0242f12-0344-4542-93fd-c92eeb101078" x="0" y="28" width="50" height="11"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="cd94f951-7671-40f6-ba4b-2b959a30ef6c" x="50" y="28" width="210" height="11"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_profissional")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="90b2d27e-f766-482d-81cb-e2a5220a3fe7" x="261" y="28" width="216" height="11"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_paciente")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="4e87e7c4-3724-41ba-a370-d3fcaf2cd958" x="478" y="28" width="87" height="11"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_convenio")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="e470ce71-068b-47e6-9302-a0a860b2f67e" x="565" y="28" width="217" height="11"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_produto")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="8"/>
		</groupFooter>
	</group>
	<group name="receituario">
		<groupExpression><![CDATA[$F{receituario}.getCodigo()]]></groupExpression>
	</group>
	<detail>
		<band height="12" splitType="Stretch">
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement uuid="4dbcc0ac-7a0b-4cb1-a9c0-2d0b7e14eb25" isPrintRepeatedValues="false" x="0" y="0" width="50" height="11" printWhenGroupChanges="receituario"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataCadastro}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement uuid="7cd979e2-d046-4162-b827-d03a17a1122e" isPrintRepeatedValues="false" x="50" y="0" width="210" height="11" printWhenGroupChanges="receituario"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{profissional}.getNome()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement uuid="8bfc9ce5-96c2-4947-9cfc-6155155a53c7" isPrintRepeatedValues="false" x="261" y="0" width="216" height="11" printWhenGroupChanges="receituario"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{paciente}.getNome()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="7a88b4c3-2b91-4dcf-9f7b-9d8d6710546a" isPrintRepeatedValues="false" x="478" y="0" width="87" height="11" printWhenGroupChanges="receituario"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{convenio}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="5919bb9e-30fc-4dbe-b410-472a31c17d4a" x="565" y="0" width="217" height="11"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{produto}.getDescricao()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
