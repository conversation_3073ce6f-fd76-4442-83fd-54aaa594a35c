package br.com.ksisolucoes.report.agendamento.exame.query;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.AliasToBeanNestedResultTransformer;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioFPOPrestadorServicoDTO;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioFPOPrestadorServicoDTOParam;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.type.DoubleType;
import org.hibernate.type.LongType;
import org.hibernate.type.StringType;

import java.util.List;

public class QueryRelatorioFPOPrestadorServico extends CommandQuery<QueryRelatorioFPOPrestadorServico> implements ITransferDataReport<RelatorioFPOPrestadorServicoDTOParam, RelatorioFPOPrestadorServicoDTO> {

    private RelatorioFPOPrestadorServicoDTOParam param;
    private List<RelatorioFPOPrestadorServicoDTO> result;
    public QueryRelatorioFPOPrestadorServico(RelatorioFPOPrestadorServicoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setUseSQL(true);
        hql.setTypeSelect(RelatorioFPOPrestadorServicoDTO.class.getName());

        hql.addToSelect(" e.empresa", "empresa.codigo");
        hql.addToSelect(" e.descricao", "empresa.descricao");
        hql.addToSelect(" te.ds_tp_exame", "tipoExame.descricao");


        if (RelatorioFPOPrestadorServicoDTOParam.TipoTabela.DESCRICAO_PROCEDIMENTO.equals(param.getTipoTabela())) {
             hql.addToSelect(" max(ep.ds_procedimento)", "exameProcedimento.descricaoProcedimento");
             hql.addToSelect(" max(ep.referencia)", "exameProcedimento.referencia");
        } else {
             hql.addToSelect(" max(p.referencia)", "procedimento.referencia");
             hql.addToSelect(" max(p.ds_procedimento)", "procedimento.descricao");
        }
         hql.addToSelect(" max(coalesce(pc.vl_sa, 0))", "valorSus");
        hql.addToSelect(
                "case when max(coalesce(epp.vl_procedimento, 0)) != max(coalesce(pc.vl_sa, 0)) " +
                        "then max(coalesce(epp.vl_procedimento, 0)) else 0 end",
                "valorDiferenciado"
        );

        hql.addToFrom("exame_prestador_procedimento epp"
                + " left join exame_procedimento ep         on    epp.cd_exame_procedimento  =  ep.cd_exame_procedimento"
                + " left join exame_prestador ep2           on    epp.cd_exame_prestador     =  ep2.cd_exame_prestador"
                + " left join empresa e                     on    ep2.empresa                =  e.empresa"
                + " left join procedimento p                on    ep.cd_procedimento         =  p.cd_procedimento"
                + " left join procedimento_competencia pc   on    p.cd_procedimento          =  pc.cd_procedimento"
                + " left join tipo_procedimento_empresa tpe on    e.empresa                  =  tpe.empresa"
                + " left join tipo_exame te                 on    ep.cd_tp_exame             =  te.cd_tp_exame"
        );
        if (CollectionUtils.isNotNullEmpty(param.getPrestador().getValue())){
            hql.addToWhereWhithAnd("e.empresa in (:empresa)");
        }

        if (CollectionUtils.isNotNullEmpty(param.getTipoExame().getValue())) {
            hql.addToWhereWhithAnd("te.cd_tp_exame in (:grupoProcedimento)");
        }
        if (CollectionUtils.isNotNullEmpty(param.getExameProcedimentoList())) {
            hql.addToWhereWhithAnd("ep.cd_exame_procedimento in (:procedimento)");
        }

        hql.addToGroup("e.empresa,e.descricao, te.ds_tp_exame ,te.cd_tp_exame, ep.cd_exame_procedimento,ep.ds_procedimento,p.ds_procedimento");


        if (RelatorioFPOPrestadorServicoDTOParam.TipoTabela.DESCRICAO_PROCEDIMENTO.equals(param.getTipoTabela())) {
            hql.addToOrder( "e.empresa");
            hql.addToOrder( "te.ds_tp_exame");
            hql.addToOrder("ep.ds_procedimento");
        } else {
            hql.addToOrder( "e.empresa");
            hql.addToOrder( "te.ds_tp_exame");
            hql.addToOrder(" p.ds_procedimento");
        }
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(param.getPrestador().getValue())) {
            query.setParameterList("empresa", param.getCodigosPrestador());
        }

        if (CollectionUtils.isNotNullEmpty(param.getTipoExame().getValue())) {
            query.setParameterList("grupoProcedimento", param.getCodigosTipoExame());
        }

        if (CollectionUtils.isNotNullEmpty(param.getExameProcedimentoList())) {
            query.setParameterList("procedimento", param.getCodigosExameProcedimento());
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = (List<RelatorioFPOPrestadorServicoDTO>) result;
    }

    @Override
    public void setDTOParam(RelatorioFPOPrestadorServicoDTOParam param) {
        this.param = param;
    }

    @Override
    public List<RelatorioFPOPrestadorServicoDTO> getResult() {
        return this.result;
    }

    @Override
    protected Object executeQuery(Query query) {
        SQLQuery sql = (SQLQuery) query;
        sql.addScalar("empresa_codigo", LongType.INSTANCE);
        sql.addScalar("empresa_descricao", StringType.INSTANCE);
        sql.addScalar("tipoExame_descricao", StringType.INSTANCE);

        if (RelatorioFPOPrestadorServicoDTOParam.TipoTabela.DESCRICAO_PROCEDIMENTO.equals(param.getTipoTabela())) {
            sql.addScalar("exameProcedimento_descricaoProcedimento", StringType.INSTANCE);
            sql.addScalar("exameProcedimento_referencia", StringType.INSTANCE);
        } else {
            sql.addScalar("procedimento_referencia", StringType.INSTANCE);
            sql.addScalar("procedimento_descricao", StringType.INSTANCE);
        }

        sql.addScalar("valorSus", DoubleType.INSTANCE);
        sql.addScalar("valorDiferenciado", DoubleType.INSTANCE);

        sql.setResultTransformer(new AliasToBeanNestedResultTransformer(RelatorioFPOPrestadorServicoDTO.class, super.getHQL().getPropBindingList()));

        result = sql.list();
        return result;
    }
}
