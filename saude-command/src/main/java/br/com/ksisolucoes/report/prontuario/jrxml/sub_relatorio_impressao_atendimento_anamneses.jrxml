<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_impressao_atendimento" pageWidth="549" pageHeight="842" columnWidth="549" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="8535b7b5-30a5-4069-b6f9-f1cb0084414d">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<field name="descricao" class="java.lang.String"/>
	<field name="dataAnamnese" class="java.util.Date"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="38" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="6" y="19" width="543" height="11" uuid="8f3813f7-b3b6-48b7-8cf1-dfbcaf9184ed"/>
				<textElement markup="html">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH:mm:ss" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="1" y="3" width="101" height="13" uuid="1a750394-69b6-4454-a14d-66f78cdc6f03"/>
				<box leftPadding="0"/>
				<textElement>
					<font fontName="Arial" isBold="true" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataAnamnese}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
