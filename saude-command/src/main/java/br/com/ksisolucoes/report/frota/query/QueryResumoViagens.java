package br.com.ksisolucoes.report.frota.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.frota.interfaces.dto.ResumoViagensDTO;
import br.com.ksisolucoes.report.frota.interfaces.dto.ResumoViagensDTOParam;
import br.com.ksisolucoes.vo.frota.RoteiroViagem;
import br.com.ksisolucoes.vo.frota.RoteiroViagemPassageiro;

import static br.com.ksisolucoes.report.frota.interfaces.dto.ResumoViagensDTOParam.FormaApresentacao;
import static br.com.ksisolucoes.report.frota.interfaces.dto.ResumoViagensDTOParam.TipoResumo;
import static br.com.ksisolucoes.report.frota.interfaces.dto.ResumoViagensDTOParam.Ordenacao;
import static br.com.ksisolucoes.report.frota.interfaces.dto.ResumoViagensDTOParam.TipoOrdenacao;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryResumoViagens extends CommandQuery<QueryResumoViagens> implements ITransferDataReport<ResumoViagensDTOParam, ResumoViagensDTO> {

    private ResumoViagensDTOParam param;
    private List<ResumoViagensDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("count(distinct roteiroViagem.codigo)", "nrViagens");
        hql.addToSelect("count(passageiro.codigo)", "nrPassageiros");

        hql.setTypeSelect(ResumoViagensDTO.class.getName());

        hql.addToWhereWhithAnd("cidade = ", param.getDestino());
        hql.addToWhereWhithAnd("veiculo = ", param.getVeiculo());
        hql.addToWhereWhithAnd("motorista = ", param.getMotorista());
        hql.addToWhereWhithAnd("roteiroViagem.dataSaida", param.getPeriodo());
        hql.addToWhereWhithAnd("roteiroViagemPassageiro.status != ", RoteiroViagemPassageiro.Status.CANCELADO.value());
        hql.addToWhereWhithAnd("roteiroViagem.status != ", RoteiroViagem.Status.CANCELADO.value());

        addToFrom(hql);

        if (!param.isShowFormaApresentacao()) {
            addOrdenacao(hql);
            addTipoResumo(hql);
        } else {
            addFormaApresentacao(hql);
            addOrdenacao(hql);
            addTipoResumo(hql);
        }
    }

    private void addFormaApresentacao(HQLHelper hql) {
        String tipoOrdenacao = QueryCustom.QueryCustomSorter.CRESCENTE_NULLS_FIRST;
        if (FormaApresentacao.VEICULO.equals(param.getFormaApresentacao())) {
            hql.addToSelectAndGroup("veiculo.descricao", "roteiroViagem.veiculo.descricao");
            hql.addToOrder("veiculo.descricao" + tipoOrdenacao);
        } else if (FormaApresentacao.MOTORISTA.equals(param.getFormaApresentacao())) {
            hql.addToSelectAndGroup("motorista.nome", "roteiroViagem.motorista.nome");
            hql.addToOrder("motorista.nome" + tipoOrdenacao);
        } else if (FormaApresentacao.DESTINO.equals(param.getFormaApresentacao())) {
            hql.addToSelectAndGroup("cidade.descricao", "roteiroViagem.cidade.descricao");
            hql.addToSelectAndGroup("estado.sigla", "roteiroViagem.cidade.estado.sigla");
            hql.addToOrder("cidade.descricao" + tipoOrdenacao);
            hql.addToOrder("estado.sigla" + tipoOrdenacao);
        }
    }

    private void addTipoResumo(HQLHelper hql) {
        String tipoOrdenacao = QueryCustom.QueryCustomSorter.CRESCENTE_NULLS_FIRST;
        if (param.getTipoResumo().contains(TipoResumo.VEICULO.value())
                && (!param.isShowFormaApresentacao() || !FormaApresentacao.VEICULO.equals(param.getFormaApresentacao()))) {
            hql.addToSelectAndGroup("veiculo.descricao", "roteiroViagem.veiculo.descricao");
            hql.addToOrder("veiculo.descricao" + tipoOrdenacao);
        }
        if (param.getTipoResumo().contains(TipoResumo.MOTORISTA.value())
                && (!param.isShowFormaApresentacao() || !FormaApresentacao.MOTORISTA.equals(param.getFormaApresentacao()))) {
            hql.addToSelectAndGroup("motorista.nome", "roteiroViagem.motorista.nome");
            hql.addToOrder("motorista.nome" + tipoOrdenacao);
        }
        if (param.getTipoResumo().contains(TipoResumo.DESTINO.value())
                && (!param.isShowFormaApresentacao() || !FormaApresentacao.DESTINO.equals(param.getFormaApresentacao()))) {
            hql.addToSelectAndGroup("cidade.descricao", "roteiroViagem.cidade.descricao");
            hql.addToSelectAndGroup("estado.sigla", "roteiroViagem.cidade.estado.sigla");
            hql.addToOrder("cidade.descricao" + tipoOrdenacao);
            hql.addToOrder("estado.sigla" + tipoOrdenacao);
        }
        if (param.getTipoResumo().contains(TipoResumo.DATA_SAIDA.value())) {
            hql.addToSelectAndGroup("date(roteiroViagem.dataSaida)", "roteiroViagem.dataSaida");
            hql.addToOrder("date(roteiroViagem.dataSaida)" + tipoOrdenacao);
        }
    }

    private void addOrdenacao(HQLHelper hql) {
        if (Ordenacao.TIPO_RESUMO.equals(param.getOrdenacao())) {
            return;
        }

        String tipoOrdenacao = null;
        if (TipoOrdenacao.ASC.equals(param.getTipoOrdenacao())) {
            tipoOrdenacao = QueryCustom.QueryCustomSorter.CRESCENTE_NULLS_FIRST;
        } else if (TipoOrdenacao.DESC.equals(param.getTipoOrdenacao())) {
            tipoOrdenacao = QueryCustom.QueryCustomSorter.DECRESCENTE_NULLS_FIRST;
        }

        if (Ordenacao.NR_VIAGENS.equals(param.getOrdenacao())) {
            hql.addToOrder("1" + tipoOrdenacao);
        } else if (Ordenacao.NR_PASSAGEIROS.equals(param.getOrdenacao())) {
            hql.addToOrder("2" + tipoOrdenacao);
        }
    }

    private void addToFrom(HQLHelper hql) {
        StringBuilder from = new StringBuilder();
        from.append("RoteiroViagemPassageiro roteiroViagemPassageiro");
        from.append(" join roteiroViagemPassageiro.usuarioCadsus passageiro");
        from.append(" right join roteiroViagemPassageiro.roteiro roteiroViagem");

        if (param.getVeiculo() != null || FormaApresentacao.VEICULO.equals(param.getFormaApresentacao()) || param.getTipoResumo().contains(TipoResumo.VEICULO.value())) {
            from.append(" left join roteiroViagem.veiculo veiculo");
        }

        if (param.getMotorista() != null || FormaApresentacao.MOTORISTA.equals(param.getFormaApresentacao()) || param.getTipoResumo().contains(TipoResumo.MOTORISTA.value())) {
            from.append(" left join roteiroViagem.motorista motorista");
        }

        if (param.getDestino() != null || FormaApresentacao.DESTINO.equals(param.getFormaApresentacao()) || param.getTipoResumo().contains(TipoResumo.DESTINO.value())) {
            from.append(" left join roteiroViagem.cidade cidade");
            if (FormaApresentacao.DESTINO.equals(param.getFormaApresentacao()) || param.getTipoResumo().contains(TipoResumo.DESTINO.value())) {
                from.append(" left join cidade.estado estado");
            }
        }

        hql.addToFrom(from.toString());
    }

    @Override
    public void setDTOParam(ResumoViagensDTOParam param) {
        this.param = param;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<ResumoViagensDTO> getResult() {
        return result;
    }
}
