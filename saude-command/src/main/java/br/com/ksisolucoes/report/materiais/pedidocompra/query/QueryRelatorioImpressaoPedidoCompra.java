package br.com.ksisolucoes.report.materiais.pedidocompra.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.materiais.pedidocompra.interfaces.dto.RelatorioImpressaoPedidoCompraDTO;
import br.com.ksisolucoes.report.materiais.pedidocompra.interfaces.dto.RelatorioImpressaoPedidoCompraDTOParam;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.materiais.pedidocompra.PedidoCompraItem;
import org.hibernate.Session;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */

public class QueryRelatorioImpressaoPedidoCompra extends CommandQuery<QueryRelatorioImpressaoPedidoCompra> implements ITransferDataReport<RelatorioImpressaoPedidoCompraDTOParam, RelatorioImpressaoPedidoCompraDTO> {

    private RelatorioImpressaoPedidoCompraDTOParam param;
    private List<RelatorioImpressaoPedidoCompraDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(RelatorioImpressaoPedidoCompraDTO.class.getName());

        hql.addToSelect("pedidoCompra.codigo", "pedidoCompra.codigo");
        hql.addToSelect("pedidoCompra.dataCadastro", "pedidoCompra.dataCadastro");

        hql.addToSelect("empresa.codigo", "emitente.codigo");
        hql.addToSelect("empresa.descricao", "emitente.descricao");
        hql.addToSelect("empresa.contato", "emitente.contato");
        hql.addToSelect("empresa.cnpj", "emitente.cnpj");
        hql.addToSelect("empresa.telefone", "emitente.telefone");
        hql.addToSelect("empresa.fax", "emitente.fax");
        hql.addToSelect("empresa.numero", "emitente.numero");
        hql.addToSelect("empresa.rua", "emitente.rua");
        hql.addToSelect("empresa.complemento", "emitente.complemento");
        hql.addToSelect("empresa.cep", "emitente.cep");
        hql.addToSelect("empresa.bairro", "emitente.bairro");
        hql.addToSelect("empresa.email", "emitente.email");
        hql.addToSelect("cidadeEmpresa.codigo", "emitente.cidade.codigo");
        hql.addToSelect("cidadeEmpresa.descricao", "emitente.cidade.descricao");
        hql.addToSelect("estadoEmpresa.codigo", "emitente.cidade.estado.codigo");
        hql.addToSelect("estadoEmpresa.descricao", "emitente.cidade.estado.descricao");
        hql.addToSelect("estadoEmpresa.sigla", "emitente.cidade.estado.sigla");

        hql.addToFrom("PedidoCompra pedidoCompra"
                + " left join pedidoCompra.empresa empresa"
                + " left join empresa.cidade cidadeEmpresa"
                + " left join cidadeEmpresa.estado estadoEmpresa");

        hql.addToWhereWhithAnd("pedidoCompra = ", param.getPedidoCompra());
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        for (RelatorioImpressaoPedidoCompraDTO dto : result) {
            dto.setPedidoCompraItem(new QueryRelatorioPedidoCompraItem().start().getResult());
        }
    }

    private class QueryRelatorioPedidoCompraItem extends CommandQuery<QueryRelatorioPedidoCompraItem> {

        private List<PedidoCompraItem> result;

        @Override
        protected void createQuery(HQLHelper hql) {
            hql.setTypeSelect(PedidoCompraItem.class.getName());

            hql.addToSelect("pci.codigo", "codigo");
            hql.addToSelect("pci.quantidadeItem", "quantidadeItem");
            hql.addToSelect("pci.quantidadeRecebida", "quantidadeRecebida");
            hql.addToSelect("pci.observacao", "observacao");
            hql.addToSelect("produto.codigo", "produto.codigo");
            hql.addToSelect("produto.descricao", "produto.descricao");
            hql.addToSelect("unidade.codigo", "produto.unidade.codigo");
            hql.addToSelect("unidade.unidade", "produto.unidade.unidade");

            hql.addToFrom("PedidoCompraItem pci"
                    + " left join pci.produto produto"
                    + " left join produto.unidade unidade");

            hql.addToWhereWhithAnd("pci.pedidoCompra = ", param.getPedidoCompra());
            hql.addToWhereWhithAnd("pci.status <> ", PedidoCompraItem.Status.CANCELADA.value());
            
            hql.addToOrder("produto.descricao");
        }

        @Override
        protected void result(HQLHelper hql, Object result) {
            this.result = hql.getBeanList((List<Map<String, Object>>) result);
        }

        @Override
        public List<PedidoCompraItem> getResult() {
            return result;
        }

    }

    @Override
    public List<RelatorioImpressaoPedidoCompraDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(RelatorioImpressaoPedidoCompraDTOParam param) {
        this.param = param;
    }

}
