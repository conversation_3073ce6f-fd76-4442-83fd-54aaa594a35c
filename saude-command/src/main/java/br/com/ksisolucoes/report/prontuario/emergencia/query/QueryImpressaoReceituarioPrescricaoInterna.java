package br.com.ksisolucoes.report.prontuario.emergencia.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.prontuario.enfermagem.interfaces.dto.ImpressaoReceituarioDTO;
import br.com.ksisolucoes.report.prontuario.enfermagem.interfaces.dto.ImpressaoReceituarioDTOParam;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.TipoViaMedicamento;
import br.com.ksisolucoes.vo.entradas.estoque.Unidade;
import br.com.ksisolucoes.vo.prontuario.basico.*;

import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryImpressaoReceituarioPrescricaoInterna extends CommandQuery implements ITransferDataReport<ImpressaoReceituarioDTOParam, ImpressaoReceituarioDTO> {

    private ImpressaoReceituarioDTOParam param;
    private List<ImpressaoReceituarioDTO> listReceituario;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.setTypeSelect(ImpressaoReceituarioDTO.class.getName());

        hql.addToSelect("produto.codigo", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_PRODUTO, Produto.PROP_CODIGO));
        hql.addToSelect("produto.referencia", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_PRODUTO, Produto.PROP_REFERENCIA));
        hql.addToSelect("produto.descricao", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_PRODUTO, Produto.PROP_DESCRICAO));
        hql.addToSelect("receituarioItem.nomeProduto", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_NOME_PRODUTO));
        hql.addToSelect("receituarioItem.posologia", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_POSOLOGIA));
        hql.addToSelect("tipoViaMedicamento.codigo", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_TIPO_VIA_MEDICAMENTO, TipoViaMedicamento.PROP_CODIGO));
        hql.addToSelect("tipoViaMedicamento.referencia", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_TIPO_VIA_MEDICAMENTO, TipoViaMedicamento.PROP_REFERENCIA));
        hql.addToSelect("tipoViaMedicamento.descricao", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_TIPO_VIA_MEDICAMENTO, TipoViaMedicamento.PROP_DESCRICAO));
        hql.addToSelect("unidade.codigo", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_UNIDADE, Unidade.PROP_CODIGO));
        hql.addToSelect("unidade.descricao", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_UNIDADE, Unidade.PROP_DESCRICAO));
        hql.addToSelect("receituarioItem.quantidadePrescrita", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_QUANTIDADE_PRESCRITA));
        hql.addToSelect("receituarioItem.descricaoReceituario", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_DESCRICAO_RECEITUARIO));
        hql.addToSelect("receituarioItem.flagTratamentoContinuo", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_FLAG_TRATAMENTO_CONTINUO));

        hql.addToSelect("receituarioItem.esfericasDireitoLonge", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_ESFERICAS_DIREITO_LONGE));
        hql.addToSelect("receituarioItem.cilindricaDireitoLonge", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_CILINDRICA_DIREITO_LONGE));
        hql.addToSelect("receituarioItem.eixoDireitoLonge", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_EIXO_DIREITO_LONGE));
        hql.addToSelect("receituarioItem.esfericasEsquerdoLonge", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_ESFERICAS_ESQUERDO_LONGE));
        hql.addToSelect("receituarioItem.cilindricaEsquerdaLonge", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_CILINDRICA_ESQUERDA_LONGE));
        hql.addToSelect("receituarioItem.eixoEsquerdoLonge", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_EIXO_ESQUERDO_LONGE));

        hql.addToSelect("receituarioItem.esfericasDireitoPerto", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_ESFERICAS_DIREITO_PERTO));
        hql.addToSelect("receituarioItem.cilindricaDireitoPerto", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_CILINDRICA_DIREITO_PERTO));
        hql.addToSelect("receituarioItem.eixoDireitoPerto", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_EIXO_DIREITO_PERTO));
        hql.addToSelect("receituarioItem.esfericasEsquerdoPerto", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_ESFERICAS_ESQUERDO_PERTO));
        hql.addToSelect("receituarioItem.cilindricaEsquerdaPerto", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_CILINDRICA_ESQUERDA_PERTO));
        hql.addToSelect("receituarioItem.eixoEsquerdoPerto", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_EIXO_ESQUERDO_PERTO));

        hql.addToSelect("receituarioItem.dpLonge", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_DP_LONGE));
        hql.addToSelect("receituarioItem.dpPerto", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_DP_PERTO));
        hql.addToSelect("receituarioItem.prescricaoOculosObs", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_PRESCRICAO_OCULOS_OBS));

        hql.addToSelect("usuarioCadsus.codigo", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_RECEITUARIO, Receituario.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO));
        hql.addToSelect("usuarioCadsus.nome", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_RECEITUARIO, Receituario.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME));
        hql.addToSelect("usuarioCadsus.cpf", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_RECEITUARIO, Receituario.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CPF));
        hql.addToSelect("usuarioCadsus.apelido", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_RECEITUARIO, Receituario.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO));
        hql.addToSelect("usuarioCadsus.utilizaNomeSocial", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_RECEITUARIO, Receituario.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL));
        hql.addToSelect("usuarioCadsus.dataNascimento", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_RECEITUARIO, Receituario.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_DATA_NASCIMENTO));
        hql.addToSelect("receituarioItem.codigo", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_CODIGO));

        hql.addToSelect("receituario.codigo", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_RECEITUARIO, Receituario.PROP_CODIGO));
        hql.addToSelect("receituario.numeroReceita", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_RECEITUARIO, Receituario.PROP_NUMERO_RECEITA));
        hql.addToSelect("receituario.dataCadastro", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_RECEITUARIO, Receituario.PROP_DATA_CADASTRO));
        hql.addToSelect("receituario.anotacao", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_RECEITUARIO, Receituario.PROP_ANOTACAO));
        hql.addToSelect("receituario.receitaContinua", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_RECEITUARIO, Receituario.PROP_RECEITA_CONTINUA));
        hql.addToSelect("tipoReceita.codigo", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_RECEITUARIO, Receituario.PROP_TIPO_RECEITA, TipoReceita.PROP_CODIGO));
        hql.addToSelect("tipoReceita.descricao", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_RECEITUARIO, Receituario.PROP_TIPO_RECEITA, TipoReceita.PROP_DESCRICAO));
        hql.addToSelect("profissional.codigo", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_RECEITUARIO, Receituario.PROP_PROFISSIONAL, Profissional.PROP_CODIGO));
        hql.addToSelect("profissional.referencia", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_RECEITUARIO, Receituario.PROP_PROFISSIONAL, Profissional.PROP_REFERENCIA));
        hql.addToSelect("profissional.nome", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_RECEITUARIO, Receituario.PROP_PROFISSIONAL, Profissional.PROP_NOME));
        hql.addToSelect("empresa.codigo", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_RECEITUARIO, Receituario.PROP_EMPRESA, Empresa.PROP_CODIGO));
        hql.addToSelect("empresa.referencia", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_RECEITUARIO, Receituario.PROP_EMPRESA, Empresa.PROP_REFERENCIA));
        hql.addToSelect("empresa.descricao", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_RECEITUARIO, Receituario.PROP_EMPRESA, Empresa.PROP_DESCRICAO));
        hql.addToSelect("cid.codigo", VOUtils.montarPath(ImpressaoReceituarioDTO.PROP_RECEITUARIO, Receituario.PROP_CID, Cid.PROP_CODIGO));

        hql.addToSelect("(select min(ucc.numeroCartao) from UsuarioCadsusCns ucc where ucc.usuarioCadsus = usuarioCadsus and ucc.excluido = 0)", "cartaoCns");

        hql.addToFrom("ReceituarioItem receituarioItem "
                + " right join receituarioItem.receituario receituario"
                + " left join receituarioItem.produto produto"
                + " left join produto.tipoReceita tipoReceita  "
                + " left join receituarioItem.tipoViaMedicamento tipoViaMedicamento"
                + " left join receituarioItem.unidade unidade"
                + " left join receituario.atendimento atendimento"
                + " left join atendimento.usuarioCadsus usuarioCadsus"
                + " left join receituario.empresa empresa"
                + " left join receituario.profissional profissional"
                + " left join receituario.cid cid"
        );

        hql.addToWhereWhithAnd("tipoReceita.tipoReceita = ", TipoReceita.RECEITA_BRANCA);
        hql.addToWhereWhithAnd("receituarioItem.codigo in ", this.param.getReceituarioItens());

        hql.addToOrder("receituario.codigo");
    }

    @Override
    public Collection<ImpressaoReceituarioDTO> getResult() {
        return this.listReceituario;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        listReceituario = hql.getBeanList((List) result);
    }

    @Override
    public void setDTOParam(ImpressaoReceituarioDTOParam t) {
        this.param = t;
    }
}
