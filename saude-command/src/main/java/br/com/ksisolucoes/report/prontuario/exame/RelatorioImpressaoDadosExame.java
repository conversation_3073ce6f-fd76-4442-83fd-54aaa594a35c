/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.prontuario.exame;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.exame.interfaces.dto.ImpressaoDadosExameDTOParam;
import br.com.ksisolucoes.report.prontuario.exame.query.QueryImpressaoDadosExame;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoDadosExame extends AbstractReport<ImpressaoDadosExameDTOParam>{

    public RelatorioImpressaoDadosExame(ImpressaoDadosExameDTOParam param){
        super(param);
    }

    @Override
    public ITransferDataReport getQuery(){
        return new QueryImpressaoDadosExame();
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_impressao_dados_exame.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_ficha_paciente");
    }

}
