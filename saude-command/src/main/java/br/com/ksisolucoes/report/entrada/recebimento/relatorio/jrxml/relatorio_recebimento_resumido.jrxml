<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_recebimento_resumido" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="6e118362-7040-47f3-b626-e61ba527f9cf">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.vo.entradas.recebimento.RegistroNotaFiscal"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.entradas.estoque.Produto"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<import value="br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal"/>
	<parameter name="ParamFormaApresentacao" class="java.lang.String" isForPrompting="false"/>
	<parameter name="ParamTipoResumo" class="java.lang.String" isForPrompting="false"/>
	<parameter name="ParamVisualizaQtdade" class="java.lang.Boolean" isForPrompting="false"/>
	<field name="produto" class="br.com.ksisolucoes.vo.entradas.estoque.Produto"/>
	<field name="quantidade" class="java.lang.Double"/>
	<field name="precoUnitario" class="java.lang.Double"/>
	<field name="percentualIpi" class="java.lang.Double"/>
	<field name="descricaoProduto" class="java.lang.String"/>
	<field name="unidade" class="java.lang.String"/>
	<field name="valorIpi" class="java.lang.Double"/>
	<field name="valorTotal" class="java.lang.Double"/>
	<field name="valorIcms" class="java.lang.Double"/>
	<field name="percentualIcms" class="java.lang.Double"/>
	<field name="registroNotaFiscal" class="br.com.ksisolucoes.vo.entradas.recebimento.RegistroNotaFiscal"/>
	<field name="item" class="java.lang.Long"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="DATA" class="br.com.ksisolucoes.util.Data"/>
	<variable name="PRODUTO" class="br.com.ksisolucoes.vo.entradas.estoque.Produto">
		<variableExpression><![CDATA[$F{produto}]]></variableExpression>
	</variable>
	<variable name="SOMA_QUANTIDADE" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}]]></variableExpression>
	</variable>
	<variable name="SOMA_VALOR_TOTAL" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotal}]]></variableExpression>
	</variable>
	<variable name="TOTAL_QUANTIDADE" class="java.lang.Double" resetType="Group" resetGroup="GrupoFormaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}]]></variableExpression>
	</variable>
	<variable name="TOTAL_VALOR_TOTAL" class="java.lang.Double" resetType="Group" resetGroup="GrupoFormaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotal}]]></variableExpression>
	</variable>
	<variable name="TOTAL_PRECO_MEDIO" class="java.lang.Double" resetType="Group" resetGroup="GrupoFormaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[( $F{quantidade} * $F{precoUnitario} * ($F{percentualIpi} / 100 +1) ) / $F{quantidade}]]></variableExpression>
	</variable>
	<variable name="EMPRESA" class="br.com.ksisolucoes.vo.basico.Empresa">
		<variableExpression><![CDATA[$F{registroNotaFiscal}.getEmpresa()]]></variableExpression>
	</variable>
	<variable name="TOTAL_GERAL_QUANTIDADE" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}]]></variableExpression>
	</variable>
	<variable name="TOTAL_GERAL_VALOR_TOTAL" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotal}]]></variableExpression>
	</variable>
	<variable name="TOTAL_GERAL_PRECO_MEDIO" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[( $F{quantidade} * $F{precoUnitario} * ($F{percentualIpi} / 100 +1) ) / $F{quantidade}]]></variableExpression>
	</variable>
	<variable name="SOMA_PRECO_MEDIO" class="java.lang.Double">
		<variableExpression><![CDATA[(($V{SOMA_QUANTIDADE} == 0) && ( $V{SOMA_VALOR_TOTAL} == 0 ))  ?
 (0.00) :
 ( ($V{SOMA_QUANTIDADE} == 0) && ( $V{SOMA_VALOR_TOTAL} != 0 )) ?
( $V{SOMA_VALOR_TOTAL} /new Double(1)  ) :
 (($V{SOMA_QUANTIDADE} != 0) && ( $V{SOMA_VALOR_TOTAL} == 0 )) ?
 (new Double(0)/$V{SOMA_QUANTIDADE}) :
 ($V{SOMA_VALOR_TOTAL} /$V{SOMA_QUANTIDADE})]]></variableExpression>
	</variable>
	<variable name="VALOR_IP" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{valorIpi}!=null?$F{valorIpi}:0D]]></variableExpression>
	</variable>
	<variable name="TOTAL_IPI" class="java.lang.Double" resetType="Group" resetGroup="GrupoFormaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{valorIpi}]]></variableExpression>
	</variable>
	<variable name="TOTAL_GERAL_IPI" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[$F{valorIpi}]]></variableExpression>
	</variable>
	<variable name="TOTAL_FINAL_VALOR_TOTAL" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotal}]]></variableExpression>
	</variable>
	<variable name="TOTAL_FINAL_IPI" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{valorIpi}]]></variableExpression>
	</variable>
	<variable name="TOTAL_FRETE" class="java.lang.Double" resetType="Group" resetGroup="GrupoFormaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{registroNotaFiscal}.getValorFrete() != null?
$F{registroNotaFiscal}.getValorFrete():0D]]></variableExpression>
	</variable>
	<variable name="TOTAL_GERAL_FRETE" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[$F{registroNotaFiscal}.getValorFrete()]]></variableExpression>
	</variable>
	<variable name="TOTAL_FINAL_FRETE" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{registroNotaFiscal}.getValorFrete()]]></variableExpression>
	</variable>
	<variable name="VALOR_FRETE" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{registroNotaFiscal}.getValorFrete()]]></variableExpression>
	</variable>
	<variable name="VALOR_ICMS" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{valorIcms}!=null?$F{valorIcms}:0D]]></variableExpression>
	</variable>
	<variable name="TOTAL_ICMS" class="java.lang.Double" resetType="Group" resetGroup="GrupoFormaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{valorIcms}]]></variableExpression>
	</variable>
	<variable name="TOTAL_GERAL_ICMS" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[$F{valorIcms}]]></variableExpression>
	</variable>
	<variable name="TOTAL_FINAL_ICMS" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{valorIcms}]]></variableExpression>
	</variable>
	<group name="GrupoRelatorio">
		<groupExpression><![CDATA[]]></groupExpression>
		<groupHeader>
			<band splitType="Stretch"/>
		</groupHeader>
		<groupFooter>
			<band height="11" splitType="Stretch">
				<line>
					<reportElement key="line-9" mode="Opaque" x="278" y="1" width="211" height="1" forecolor="#000000" backcolor="#FFFFFF" uuid="6fd7270f-4541-4a43-8456-2e07e7151e43"/>
					<graphicElement fill="Solid"/>
				</line>
				<textField pattern=" #,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-118" mode="Opaque" x="330" y="2" width="71" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="9c378295-c693-40b2-b659-67647d95db6b"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TOTAL_FINAL_VALOR_TOTAL}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Auto" pattern=" #,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-120" mode="Opaque" x="449" y="2" width="40" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="b852ff55-5e4e-4bb8-9c2c-81069484a4fe"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TOTAL_FINAL_IPI}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern=" #,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-122" mode="Opaque" x="246" y="2" width="84" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="365fc81c-b08a-4d84-bcd7-b8c581f9d3e3"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_total_geral")+": "]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="GrupoEmpresa" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$V{EMPRESA}.getCodigo()]]></groupExpression>
		<groupHeader>
			<band height="29" splitType="Stretch">
				<rectangle radius="5">
					<reportElement key="rectangle-2" mode="Opaque" x="0" y="2" width="535" height="14" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="5e49cbaf-f6f2-4876-8135-69fcd43f4119"/>
					<graphicElement fill="Solid"/>
				</rectangle>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-7" mode="Transparent" x="0" y="2" width="535" height="14" forecolor="#000000" backcolor="#FFFFFF" uuid="0a581d99-ab45-40ec-a288-21d7156b862f"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_empresa") + ":" + $V{EMPRESA}.getDescricaoFormatado()]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-26" mode="Transparent" x="282" y="17" width="14" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="0f487463-fb4d-4c75-9344-2b3d22baf442">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( $P{ParamTipoResumo})
&& $P{ParamVisualizaQtdade}]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Unidade*/$V{BUNDLE}.getStringApplication("rotulo_unidade_abv")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-27" stretchType="RelativeToBandHeight" mode="Transparent" x="300" y="17" width="54" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="c66b35b5-e66f-44ae-9313-b832e8d577a8">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( $P{ParamTipoResumo})
&& $P{ParamVisualizaQtdade}]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Qtde.Total*/$V{BUNDLE}.getStringApplication("rotulo_quantidade_total_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-28" mode="Transparent" x="355" y="17" width="47" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="6417fc74-ca25-4c8c-88f5-9e3be7d4dc5f">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( $P{ParamTipoResumo})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Vlr.Total*/$V{BUNDLE}.getStringApplication("rotulo_valor_total")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-5" mode="Transparent" x="402" y="17" width="44" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="3e611f5b-9116-491e-8d91-b47b716bad7b">
						<printWhenExpression><![CDATA[$P{ParamVisualizaQtdade}]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Prc.Med.*/$V{BUNDLE}.getStringApplication("rotulo_preco_medio")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoFormaApresentacao" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-99" mode="Transparent" x="1" y="17" width="170" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="ac56a004-8e22-4687-8752-ef5c67ce1985">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( $P{ParamTipoResumo})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*TipoResumo*/
$P{ParamTipoResumo}.equals( RegistroItemNotaFiscal.PROP_PRODUTO ) ?
$V{BUNDLE}.getStringApplication("rotulo_produto")  :

$P{ParamTipoResumo}.equals( RegistroNotaFiscal.PROP_DATA_PORTARIA ) ?
$V{BUNDLE}.getStringApplication("rotulo_data_entrada") :

$P{ParamTipoResumo}.equals( RegistroNotaFiscal.PROP_FORNECEDOR ) ?
$V{BUNDLE}.getStringApplication("rotulo_fornecedor") :

$P{ParamTipoResumo}.equals( RegistroNotaFiscal.PROP_DATA_EMISSAO) ?
$V{BUNDLE}.getStringApplication("rotulo_data_emissao") :

" "]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-4" x="0" y="28" width="535" height="1" uuid="ae14b37b-14db-40c3-8db7-d0add3967d3a">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( $P{ParamTipoResumo})]]></printWhenExpression>
					</reportElement>
				</line>
				<textField evaluationTime="Group" evaluationGroup="GrupoFormaApresentacao" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-104" mode="Transparent" x="447" y="17" width="40" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="2288d2fd-167e-4720-a051-54490cbb64ae">
						<printWhenExpression><![CDATA[$P{ParamFormaApresentacao}.equals( $P{ParamTipoResumo})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*IPI.*/$V{BUNDLE}.getStringApplication("rotulo_ipi")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="11" splitType="Stretch">
				<line>
					<reportElement key="line-2" mode="Opaque" x="278" y="1" width="211" height="1" forecolor="#000000" backcolor="#FFFFFF" uuid="babc59a2-78f2-4c09-b59b-7c16553b5df7"/>
					<graphicElement fill="Solid"/>
				</line>
				<textField pattern=" #,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-31" mode="Opaque" x="331" y="2" width="70" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="903d3d26-4d20-446b-8741-373b78d00b69"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TOTAL_GERAL_VALOR_TOTAL}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Auto" pattern=" #,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-113" mode="Opaque" x="449" y="2" width="40" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="520b3e27-4a35-479c-8f3f-46febe54493d"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TOTAL_GERAL_IPI}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern=" #,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-121" mode="Opaque" x="246" y="2" width="84" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="de80b613-d279-4c55-be5b-e8cfefeaf963"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_total_unidade")+": "]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="GrupoFormaApresentacao" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$P{ParamFormaApresentacao}.equals( RegistroItemNotaFiscal.PROP_PRODUTO )
?
    ($V{PRODUTO}.getCodigo() == null
    ?
        ""
    :
        $V{PRODUTO}.getCodigo() )
:
    $P{ParamFormaApresentacao}.equals( RegistroNotaFiscal.PROP_DATA_PORTARIA )
    ?
        $F{registroNotaFiscal}.getDataPortaria()
    :
        $P{ParamFormaApresentacao}.equals( RegistroNotaFiscal.PROP_FORNECEDOR )
        ?
            $F{registroNotaFiscal}.getFornecedor().getCodigo()
        :
            $P{ParamFormaApresentacao}.equals( RegistroNotaFiscal.PROP_DATA_EMISSAO )
            ?
                $F{registroNotaFiscal}.getDataEmissao()
            :
                $P{ParamFormaApresentacao}.equals(Produto.PROP_SUB_GRUPO)
                ?
                    $F{produto}.getSubGrupo().getId().getCodigo()
                :
                    $P{ParamFormaApresentacao}.equals(RegistroNotaFiscal.PROP_TIPO_DOCUMENTO)
                    ?
                        $F{registroNotaFiscal}.getTipoDocumento().getCodigo()
                    :
                        null]]></groupExpression>
		<groupHeader>
			<band height="29" splitType="Stretch">
				<printWhenExpression><![CDATA[!$P{ParamFormaApresentacao}.equals( $P{ParamTipoResumo})]]></printWhenExpression>
				<textField evaluationTime="Group" evaluationGroup="GrupoFormaApresentacao" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-1" mode="Opaque" x="1" y="17" width="277" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="a76d4591-ab89-44a7-b3c6-e4d27531b1ce"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*TipoResumo*/
$P{ParamTipoResumo}.equals( RegistroItemNotaFiscal.PROP_PRODUTO ) ?
$V{BUNDLE}.getStringApplication("rotulo_produto")  :

$P{ParamTipoResumo}.equals( RegistroNotaFiscal.PROP_DATA_PORTARIA ) ?
$V{BUNDLE}.getStringApplication("rotulo_data_entrada") :

$P{ParamTipoResumo}.equals( RegistroNotaFiscal.PROP_FORNECEDOR ) ?
$V{BUNDLE}.getStringApplication("rotulo_fornecedor") :

$P{ParamTipoResumo}.equals( RegistroNotaFiscal.PROP_DATA_EMISSAO) ?
$V{BUNDLE}.getStringApplication("rotulo_data_emissao") :

""]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoFormaApresentacao" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-2" mode="Transparent" x="282" y="17" width="14" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="20b19e7c-97b6-4e83-8517-99724e4ef709">
						<printWhenExpression><![CDATA[$P{ParamVisualizaQtdade}]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Unidade*/$V{BUNDLE}.getStringApplication("rotulo_unidade_abv")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GrupoFormaApresentacao" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-3" stretchType="RelativeToBandHeight" mode="Transparent" x="299" y="17" width="54" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="aaba879f-27ad-42f3-aa2c-c4b00bc19dc5">
						<printWhenExpression><![CDATA[$P{ParamVisualizaQtdade}]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Qtde.Total*/$V{BUNDLE}.getStringApplication("rotulo_quantidade_total_abv")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoFormaApresentacao" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" mode="Transparent" x="354" y="17" width="47" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="039b1020-a7a9-434e-965f-b3ec7c5293b9"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Vlr.Total*/$V{BUNDLE}.getStringApplication("rotulo_valor_total")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoFormaApresentacao" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-22" mode="Transparent" x="0" y="1" width="535" height="14" forecolor="#000000" backcolor="#FFFFFF" uuid="58eee6ae-3789-49ba-b41b-e2e8173edcd3"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*FormaApresentacao*/
$P{ParamFormaApresentacao}.equals( RegistroItemNotaFiscal.PROP_PRODUTO )
?
    ($V{BUNDLE}.getStringApplication("rotulo_produto") + ": " + ($V{PRODUTO}.getCodigo() != null ? $F{produto}.getDescricaoFormatado() : $V{BUNDLE}.getStringApplication("rotulo_sem_cadastro"))  )
:
    $P{ParamFormaApresentacao}.equals( RegistroNotaFiscal.PROP_DATA_PORTARIA )
    ?
        ($V{BUNDLE}.getStringApplication("rotulo_data_entrada") + ": " + $V{DATA}.formatar($F{registroNotaFiscal}.getDataPortaria()) )
    :
        $P{ParamFormaApresentacao}.equals( RegistroNotaFiscal.PROP_FORNECEDOR )
        ?
            ($F{registroNotaFiscal}.getFornecedor().getCodigo() == null
            ?
                $V{BUNDLE}.getStringApplication("rotulo_fornecedor") + ":" + " "+ $V{BUNDLE}.getStringApplication("rotulo_sem_cadastro")
            :
                ( $V{BUNDLE}.getStringApplication("rotulo_fornecedor") + ": " + $F{registroNotaFiscal}.getFornecedor().getDescricaoFormatado() ) )
        :
            $P{ParamFormaApresentacao}.equals( RegistroNotaFiscal.PROP_DATA_EMISSAO )
            ?
                ($V{BUNDLE}.getStringApplication("rotulo_data_emissao") + ": " + $V{DATA}.formatar($F{registroNotaFiscal}.getDataEmissao()) )
            :
                $P{ParamFormaApresentacao}.equals(Produto.PROP_SUB_GRUPO)
                ?
                    ($F{produto}.getSubGrupo().getRoGrupoProduto().getCodigo() == null
                    ?
                        Bundle.getStringApplication("rotulo_grupo") + ": " +   Bundle.getStringApplication("rotulo_sem_descricao")
                    :
                        Bundle.getStringApplication("rotulo_grupo") + ": " + $F{produto}.getSubGrupo().getRoGrupoProduto().getDescricaoFormatado() + " - " + Bundle.getStringApplication("rotulo_subgrupo") + ": " + $F{produto}.getSubGrupo().getDescricaoFormatado())
                :
                    $P{ParamFormaApresentacao}.equals(RegistroNotaFiscal.PROP_TIPO_DOCUMENTO)
                    ?
                        $F{registroNotaFiscal}.getTipoDocumento().getDescricaoFormatado()
                    :
                        ""]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoFormaApresentacao" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-29" mode="Transparent" x="402" y="17" width="44" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="ff39726b-79d4-47f4-ba77-3c48bcbde45b">
						<printWhenExpression><![CDATA[$P{ParamVisualizaQtdade}]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Prc.Med.*/$V{BUNDLE}.getStringApplication("rotulo_preco_medio")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-3" x="0" y="28" width="535" height="1" uuid="42ba6d38-0ad8-4205-b7e5-1fd55ca53b98"/>
				</line>
				<textField evaluationTime="Group" evaluationGroup="GrupoFormaApresentacao" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-102" mode="Transparent" x="448" y="17" width="40" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="94411eae-a52c-4704-b6fe-d75073f66c13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*IPI.*/$V{BUNDLE}.getStringApplication("rotulo_ipi")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="15" splitType="Stretch">
				<printWhenExpression><![CDATA[!$P{ParamFormaApresentacao}.equals( $P{ParamTipoResumo})]]></printWhenExpression>
				<line>
					<reportElement key="line-16" mode="Opaque" x="278" y="1" width="211" height="1" forecolor="#000000" backcolor="#FFFFFF" uuid="0c478fad-8c83-47b9-a4ec-ab61fa585fe9"/>
					<graphicElement fill="Solid"/>
				</line>
				<textField pattern=" #,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-129" mode="Opaque" x="330" y="2" width="71" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="bb7b2c68-b878-462f-ab6e-d20465017753"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TOTAL_VALOR_TOTAL}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Auto" pattern=" #,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-131" mode="Opaque" x="449" y="2" width="40" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="30810615-dbff-4fb3-aaa0-deda5a50fa07"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TOTAL_IPI}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern=" #,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-121" mode="Opaque" x="246" y="2" width="84" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="a2bc3edf-b88a-4911-8946-db539fda434e"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_total")+": "]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="10" splitType="Stretch">
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-23" mode="Transparent" x="1" y="1" width="277" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="e5c270d9-40c0-4ab4-9c38-3a6a6770a13c"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[/*TipoResumo*/
$P{ParamTipoResumo}.equals( RegistroItemNotaFiscal.PROP_PRODUTO ) ?
$F{produto}.getDescricaoFormatado():

$P{ParamTipoResumo}.equals( RegistroNotaFiscal.PROP_DATA_PORTARIA ) ?
 $V{DATA}.formatar( $F{registroNotaFiscal}.getDataPortaria() ) :

$P{ParamTipoResumo}.equals( RegistroNotaFiscal.PROP_FORNECEDOR ) ?
 $F{registroNotaFiscal}.getFornecedor().getCodigo() == null ?
 $V{BUNDLE}.getStringApplication("rotulo_sem_cadastro") :
 $F{registroNotaFiscal}.getFornecedor().getDescricaoFormatado()  :

$P{ParamTipoResumo}.equals( RegistroNotaFiscal.PROP_DATA_EMISSAO ) ?
 $V{DATA}.formatar( $F{registroNotaFiscal}.getDataEmissao() ) :

""]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-24" mode="Transparent" x="282" y="1" width="14" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="a652131f-92fe-4b46-bfb6-138ff3f0eacd">
					<printWhenExpression><![CDATA[$P{ParamVisualizaQtdade}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unidade} == null ? $V{PRODUTO}.getUnidade().getUnidade() : $F{unidade}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern=" #,##0.00" isBlankWhenNull="true">
				<reportElement key="textField" stretchType="RelativeToBandHeight" mode="Opaque" x="299" y="1" width="54" height="9" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF" uuid="59555f9e-e6bd-442a-b056-c2d3c815e381">
					<printWhenExpression><![CDATA[$P{ParamVisualizaQtdade}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidade}
/*$V{SOMA_QUANTIDADE}*/]]></textFieldExpression>
			</textField>
			<textField pattern=" #,##0.00" isBlankWhenNull="true">
				<reportElement key="textField" mode="Opaque" x="354" y="1" width="47" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="3d3fae4a-3621-47e2-9f5d-1d68788b8eb3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{valorTotal}
/*$V{SOMA_VALOR_TOTAL}*/]]></textFieldExpression>
			</textField>
			<textField pattern=" #,##0.00" isBlankWhenNull="true">
				<reportElement key="textField" mode="Opaque" x="402" y="1" width="44" height="9" forecolor="#000000" backcolor="#FFFFFF" uuid="32cd52e3-c4a1-4b0f-9786-8152b43187ea">
					<printWhenExpression><![CDATA[$P{ParamVisualizaQtdade}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SOMA_PRECO_MEDIO}]]></textFieldExpression>
			</textField>
			<textField pattern=" #,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-101" x="449" y="1" width="40" height="9" uuid="9195b1d4-3208-4df1-b4d4-2e03252cc3cf"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{valorIpi}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
