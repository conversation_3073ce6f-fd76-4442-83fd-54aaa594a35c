package br.com.ksisolucoes.report.atividadegrupo.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.atividadegrupo.interfaces.dto.RelatorioRelacaoProcedimentoAtividadeDTO;
import br.com.ksisolucoes.report.atividadegrupo.interfaces.dto.RelatorioRelacaoProcedimentoAtividadeDTOParam;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo;
import java.util.List;
import java.util.Map;
import org.hibernate.Query;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioRelacaoProcedimentoAtividade extends CommandQuery<QueryRelatorioRelacaoProcedimentoAtividade> implements ITransferDataReport<RelatorioRelacaoProcedimentoAtividadeDTOParam, RelatorioRelacaoProcedimentoAtividadeDTO> {

    private RelatorioRelacaoProcedimentoAtividadeDTOParam param;
    private List<RelatorioRelacaoProcedimentoAtividadeDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        if (RelatorioRelacaoProcedimentoAtividadeDTOParam.TipoRelatorio.DETALHADO.equals(param.getTipoRelatorio())) {
            hql.addToSelect("atividadeGrupoProcedimento.codigo", "atividadeGrupoProcedimento.codigo");
            if (param.getQuantidadePor().equals(RelatorioRelacaoProcedimentoAtividadeDTOParam.QuantidadePor.PROCEDIMENTO)){
                hql.addToSelect("coalesce(atividadeGrupoProcedimento.quantidade, 0)", "atividadeGrupoProcedimento.quantidade");
            } else {
                hql.addToSelect("(select count(agpc.codigo) from AtividadeGrupoPaciente agpc where agpc.atividadeGrupo.codigo = atividadeGrupo.codigo)", "quantidadePaciente");
            }
            hql.addToSelect("atividadeGrupo.codigo", "atividadeGrupo.codigo");
            hql.addToSelect("atividadeGrupo.dataInicio", "atividadeGrupo.dataInicio");
            hql.addToSelect("atividadeGrupo.dataFim", "atividadeGrupo.dataFim");
            hql.addToSelect("atividadeGrupo.situacao", "atividadeGrupo.situacao");
            hql.addToSelect("atividadeGrupo.participantes", "atividadeGrupo.participantes");

            hql.addToSelect("empresa.codigo", "empresa.codigo");
            hql.addToSelect("empresa.referencia", "empresa.referencia");
            hql.addToSelect("empresa.descricao", "empresa.descricao");

            hql.addToSelect("tipoAtividadeGrupo.codigo", "tipoAtividadeGrupo.codigo");
            hql.addToSelect("tipoAtividadeGrupo.descricao", "tipoAtividadeGrupo.descricao");

            hql.addToSelect("tabelaCbo.cbo", "tabelaCbo.cbo");
            hql.addToSelect("tabelaCbo.descricao", "tabelaCbo.descricao");

            hql.addToSelect("procedimento.codigo", "procedimento.codigo");
            hql.addToSelect("procedimento.referencia", "procedimento.referencia");
            hql.addToSelect("procedimento.descricao", "procedimento.descricao");
        } else if (RelatorioRelacaoProcedimentoAtividadeDTOParam.TipoRelatorio.RESUMIDO.equals(param.getTipoRelatorio())) {
            if (param.getQuantidadePor().equals(RelatorioRelacaoProcedimentoAtividadeDTOParam.QuantidadePor.PROCEDIMENTO)){
                hql.addToSelect("sum(coalesce(atividadeGrupoProcedimento.quantidade, 0))", "atividadeGrupoProcedimento.quantidade");
            } else {
                hql.addToSelect("sum(coalesce((select count(agpc.codigo) from AtividadeGrupoPaciente agpc where agpc.atividadeGrupo.codigo = atividadeGrupo.codigo), 0))", "quantidadePaciente");
            }
            if (RelatorioRelacaoProcedimentoAtividadeDTOParam.FormaApresentacao.UNIDADE.equals(param.getFormaApresentacao())) {
                hql.addToSelectAndGroup("empresa.codigo", "empresa.codigo");
                hql.addToSelectAndGroup("empresa.referencia", "empresa.referencia");
                hql.addToSelectAndGroup("empresa.descricao", "empresa.descricao");
            }

            if (RelatorioRelacaoProcedimentoAtividadeDTOParam.FormaApresentacao.TIPO_ATIVIDADE.equals(param.getFormaApresentacao())) {
                hql.addToSelectAndGroup("tipoAtividadeGrupo.codigo", "tipoAtividadeGrupo.codigo");
                hql.addToSelectAndGroup("tipoAtividadeGrupo.descricao", "tipoAtividadeGrupo.descricao");
            }

            if (RelatorioRelacaoProcedimentoAtividadeDTOParam.FormaApresentacao.CBO.equals(param.getFormaApresentacao())) {
                hql.addToSelectAndGroup("tabelaCbo.cbo", "tabelaCbo.cbo");
                hql.addToSelectAndGroup("tabelaCbo.descricao", "tabelaCbo.descricao");
            }
            
            hql.addToSelectAndGroup("procedimento.codigo", "procedimento.codigo");
            hql.addToSelectAndGroup("procedimento.referencia", "procedimento.referencia");
            hql.addToSelectAndGroup("procedimento.descricao", "procedimento.descricao");
        }

        hql.setTypeSelect(RelatorioRelacaoProcedimentoAtividadeDTO.class.getName());
        hql.addToFrom("AtividadeGrupoProcedimento atividadeGrupoProcedimento"
                +"  left join atividadeGrupoProcedimento.atividadeGrupo atividadeGrupo"
                +"  left join atividadeGrupoProcedimento.procedimento procedimento"
                +"  left join atividadeGrupoProcedimento.tabelaCbo tabelaCbo"
                + " left join atividadeGrupo.empresa empresa"
                + " left join atividadeGrupo.tipoAtividadeGrupo tipoAtividadeGrupo");
        if (CollectionUtils.isNotNullEmpty(param.getProfissional())) {
            hql.addToWhereWhithAnd("exists (select 1 from AtividadeGrupoProfissional agp1 where agp1.profissional in (:profissionais) and agp1.atividadeGrupo = atividadeGrupo)");
        }
        if (param.getProfissional() != null) {
            hql.addToWhereWhithAnd("atividadeGrupoProcedimento.profissional =", param.getProfissional());
        }

        hql.addToWhereWhithAnd("empresa ", param.getEmpresa());
        hql.addToWhereWhithAnd("tipoAtividadeGrupo in ", param.getTipoAtividadeGrupo());
        hql.addToWhereWhithAnd("procedimento in ", param.getProcedimento());
        hql.addToWhereWhithAnd("tabelaCbo in ", param.getTabelaCbo());
        hql.addToWhereWhithAnd("atividadeGrupo.dataInicio ", param.getPeriodo());
        hql.addToWhereWhithAnd("atividadeGrupo.situacao =", AtividadeGrupo.SITUACAO_CONCLUIDA);
        
        if (RelatorioRelacaoProcedimentoAtividadeDTOParam.FormaApresentacao.UNIDADE.equals(param.getFormaApresentacao())) {
            hql.addToOrder("empresa.descricao");
            hql.addToOrder("empresa.codigo");
        } else if (RelatorioRelacaoProcedimentoAtividadeDTOParam.FormaApresentacao.TIPO_ATIVIDADE.equals(param.getFormaApresentacao())) {
            hql.addToOrder("tipoAtividadeGrupo.descricao");
            hql.addToOrder("tipoAtividadeGrupo.codigo");
        } else if (RelatorioRelacaoProcedimentoAtividadeDTOParam.FormaApresentacao.CBO.equals(param.getFormaApresentacao())) {
            hql.addToOrder("tabelaCbo.descricao");
            hql.addToOrder("tabelaCbo.cbo");
        }

        if (RelatorioRelacaoProcedimentoAtividadeDTOParam.TipoRelatorio.RESUMIDO.equals(param.getTipoRelatorio())) {
            hql.addToOrder("procedimento.descricao");
            hql.addToOrder("procedimento.codigo");
        } else {
            hql.addToOrder("atividadeGrupo.dataInicio");
            hql.addToOrder("atividadeGrupo.dataFim");
            hql.addToOrder("tipoAtividadeGrupo.descricao");
        }
    }
    
    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(param.getProfissional())) {
            query.setParameterList("profissionais", this.param.getProfissional());
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioRelacaoProcedimentoAtividadeDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(RelatorioRelacaoProcedimentoAtividadeDTOParam param) {
        this.param = param;
    }

}