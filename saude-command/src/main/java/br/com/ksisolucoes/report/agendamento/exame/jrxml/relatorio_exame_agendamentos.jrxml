<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_agendamentos" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="49d88a1f-fdaf-4a79-a138-931dd51cf29f">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.6105100000000099"/>
	<property name="ireport.x" value="770"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario"/>
	<import value="br.com.ksisolucoes.vo.basico.Empresa"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<import value="br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioExameAgendamentoDTOParam"/>
	<parameter name="formaApresentacao" class="br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioExameAgendamentoDTOParam.FormaApresentacao"/>
	<parameter name="informaProcedimentoCiapManual" class="java.lang.String"/>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="data" class="java.util.Date"/>
	<field name="descricaoData" class="java.lang.String"/>
	<field name="empresaOrigem" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="paciente" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="descricaoSituacao" class="java.lang.String"/>
	<field name="idadePaciente" class="java.lang.Long"/>
	<field name="tipoProcedimento" class="br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento"/>
	<field name="quantidadeVagasOcupadas" class="java.lang.Long"/>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<field name="descricaoTipoProcedimento" class="java.lang.String"/>
	<field name="nomePaciente" class="java.lang.String"/>
	<field name="descricaoArea" class="java.lang.String"/>
	<field name="microArea" class="java.lang.Long"/>
	<field name="nomeProfissional" class="java.lang.String"/>
	<field name="unidadeAgendamento" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="regionalSaude" class="br.com.ksisolucoes.vo.basico.RegionalSaude"/>
	<field name="procedimento" class="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"/>
	<field name="descricaoCancelamento" class="java.lang.String"/>
	<field name="status" class="java.lang.Long"/>
	<field name="exameProcedimento" class="br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento"/>
	<variable name="count" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[1]]></variableExpression>
	</variable>
	<variable name="quantidadeVagasOcupadas_1" class="java.lang.Long" resetType="Group" resetGroup="FormaApresentacao" calculation="Count">
		<variableExpression><![CDATA[$F{paciente}]]></variableExpression>
	</variable>
	<variable name="quantidadeVagasOcupadas_2" class="java.lang.Long" resetType="Group" resetGroup="geral" calculation="Count">
		<variableExpression><![CDATA[$F{paciente}]]></variableExpression>
	</variable>
	<group name="geral">
		<groupFooter>
			<band height="15">
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="510" y="1" width="241" height="12" uuid="11560142-22f4-4b73-a301-2b62b434173c"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_geral_agendamentos")+":"]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="632" y="0" width="168" height="1" uuid="b1512764-7a9b-4d3e-a912-5912535c184f"/>
				</line>
				<textField>
					<reportElement x="751" y="1" width="50" height="12" uuid="dc64caa3-ea18-42c8-b0b2-4d9c81aa3c11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{quantidadeVagasOcupadas_2}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="FormaApresentacao" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$P{formaApresentacao}.equals(RelatorioExameAgendamentoDTOParam.FormaApresentacao.UNIDADE)
?
    $F{empresa}.getDescricao()
:
    $P{formaApresentacao}.equals(RelatorioExameAgendamentoDTOParam.FormaApresentacao.TIPO_PROCEDIMENTO)
    ?
        $F{tipoProcedimento}.getDescricao()
    :
        $P{formaApresentacao}.equals(RelatorioExameAgendamentoDTOParam.FormaApresentacao.PROCEDIMENTO)
        ?
            $F{procedimento}.getDescricao()
        :
            $P{formaApresentacao}.equals(RelatorioExameAgendamentoDTOParam.FormaApresentacao.UNIDADE_ORIGEM)
            ?
                $F{empresaOrigem}.getDescricao()
            :
                $P{formaApresentacao}.equals(RelatorioExameAgendamentoDTOParam.FormaApresentacao.DATA)
                ?
                    new SimpleDateFormat("dd/MM/yyyy").format($F{data})
                :
                    $P{formaApresentacao}.equals(RelatorioExameAgendamentoDTOParam.FormaApresentacao.PROFISSIONAL)
                    ?
                        $F{profissional}.getDescricaoFormatado()
                    :
                        $P{formaApresentacao}.equals(RelatorioExameAgendamentoDTOParam.FormaApresentacao.AREA)
                        ?
                            $F{descricaoArea}
                        :
                            $P{formaApresentacao}.equals(RelatorioExameAgendamentoDTOParam.FormaApresentacao.UNIDADE_AGENDAMENTO)
                            ?
                                $F{unidadeAgendamento}.getDescricao()
                            :
                                $P{formaApresentacao}.equals(RelatorioExameAgendamentoDTOParam.FormaApresentacao.REGIONAL)
                                ?
                                    $F{regionalSaude}.getDescricao()
                                :
                                    $P{formaApresentacao}.equals(RelatorioExameAgendamentoDTOParam.FormaApresentacao.EXAME)
                                    ?
                                        $F{exameProcedimento}.getDescricaoProcedimento()
                                    :
                                        null]]></groupExpression>
		<groupHeader>
			<band height="15">
				<printWhenExpression><![CDATA[!$P{formaApresentacao}.equals(Bundle.getStringApplication("rotulo_geral"))]]></printWhenExpression>
				<rectangle radius="10">
					<reportElement mode="Transparent" x="0" y="0" width="802" height="15" uuid="83d5e501-1a01-454e-8c67-36498c74c478"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-4" x="0" y="0" width="801" height="13" uuid="42001c8f-66bd-41bb-8cd0-dc12c07b75bb"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{formaApresentacao}.equals(RelatorioExameAgendamentoDTOParam.FormaApresentacao.UNIDADE)
?
    Bundle.getStringApplication("rotulo_empresa")+": "+ $F{empresa}.getDescricaoFormatado()
:
    $P{formaApresentacao}.equals(RelatorioExameAgendamentoDTOParam.FormaApresentacao.TIPO_PROCEDIMENTO)
    ?
        Bundle.getStringApplication("rotulo_tipo_procedimento")+": "+$F{tipoProcedimento}.getDescricaoFormatado()
    :
        $P{formaApresentacao}.equals(RelatorioExameAgendamentoDTOParam.FormaApresentacao.PROCEDIMENTO)
        ?
            Bundle.getStringApplication("rotulo_procedimento")+": "+$F{procedimento}.getDescricaoFormatado()
        :
            $P{formaApresentacao}.equals(RelatorioExameAgendamentoDTOParam.FormaApresentacao.UNIDADE_ORIGEM)
            ?
                Bundle.getStringApplication("rotulo_unidade_origem")+": "+$F{empresaOrigem}.getDescricaoFormatado()
            :
                $P{formaApresentacao}.equals(RelatorioExameAgendamentoDTOParam.FormaApresentacao.DATA)
                ?
                    Bundle.getStringApplication("rotulo_data")+": "+new SimpleDateFormat("dd/MM/yyyy").format($F{data})
                :
                    $P{formaApresentacao}.equals(RelatorioExameAgendamentoDTOParam.FormaApresentacao.PROFISSIONAL)
                    ?
                        Bundle.getStringApplication("rotulo_profissional")+": "+$F{profissional}.getDescricaoFormatado()
                    :
                        $P{formaApresentacao}.equals(RelatorioExameAgendamentoDTOParam.FormaApresentacao.AREA)
                        ?
                            Bundle.getStringApplication("rotulo_area")+": "+$F{descricaoArea}
                        :
                            $P{formaApresentacao}.equals(RelatorioExameAgendamentoDTOParam.FormaApresentacao.UNIDADE_AGENDAMENTO)
                            ?
                                Bundle.getStringApplication("rotulo_unidade_agendamento")+": "+($F{unidadeAgendamento}.getDescricao() != null ? $F{unidadeAgendamento}.getDescricaoFormatado() : "Não Definido")
                            :
                                $P{formaApresentacao}.equals(RelatorioExameAgendamentoDTOParam.FormaApresentacao.REGIONAL)
                                ?
                                    Bundle.getStringApplication("rotulo_regional")+": "+ ($F{regionalSaude}.getDescricao() != null ? $F{regionalSaude}.getDescricao() : "Não Definido")
                                :
                                    $P{formaApresentacao}.equals(RelatorioExameAgendamentoDTOParam.FormaApresentacao.EXAME)
                                    ?
                                        Bundle.getStringApplication("rotulo_exame_procedimento")+": "+ ($F{exameProcedimento}.getDescricaoProcedimento() != null ? $F{exameProcedimento}.getDescricaoProcedimento() : "Não Definido")
                                    :
                                    ""]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="15">
				<line>
					<reportElement x="654" y="0" width="147" height="1" uuid="58274da9-5546-41b5-9916-25efbafac857"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="559" y="1" width="192" height="12" uuid="be0ce316-762e-43bb-ace3-8cf306ae36b8"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_agendamentos")+":"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="751" y="1" width="50" height="12" uuid="ec9e449e-c7a1-44e9-8891-b1fde5929aa4"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{quantidadeVagasOcupadas_1}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="DetailHeader" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band height="12">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{informaProcedimentoCiapManual})]]></printWhenExpression>
				<line>
					<reportElement x="0" y="11" width="781" height="1" uuid="030a8ce4-a826-4857-b072-d57cf3353ecf"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="372" y="0" width="120" height="12" uuid="2b9f3177-80fd-420c-9b44-d5f8159ff243"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_unidade_executante_abv2")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="492" y="0" width="64" height="12" uuid="864c8eed-7ac3-4f45-a6bf-990d5816ccc6"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_hora")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="0" y="0" width="122" height="12" uuid="5f9e3d5c-6a60-40d6-bb2f-ee88f09c8eb4"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_paciente")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="235" y="0" width="125" height="12" uuid="59e2abb8-4c4e-4fd3-a20e-6caf443c288a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_tipo_procedimento_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="671" y="0" width="72" height="12" uuid="c61662e9-c506-4b6d-ada3-bae4d3fb3e7c"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_unidade_solicitante_abv2")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="166" y="0" width="69" height="12" uuid="902a3106-e4c7-422a-b3a8-79c3a60c638d"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_telefone")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="746" y="0" width="56" height="12" uuid="2d53df11-e523-4b3a-b154-d5986d17edbe"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_situacao_2")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="122" y="0" width="41" height="12" uuid="3a179b3e-df4c-46fe-8368-96ee8ed98079"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_idade")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="560" y="0" width="106" height="12" uuid="5196f0bb-1114-48f9-9a87-5162abef3e73"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_profissional_executante")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="informaProcedimentoCiapManual" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band height="12">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{informaProcedimentoCiapManual})]]></printWhenExpression>
				<line>
					<reportElement x="0" y="11" width="781" height="1" uuid="030a8ce4-a826-4857-b072-d57cf3353ecf"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="372" y="0" width="120" height="12" uuid="2b9f3177-80fd-420c-9b44-d5f8159ff243"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_ciap")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="492" y="0" width="64" height="12" uuid="864c8eed-7ac3-4f45-a6bf-990d5816ccc6"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_hora")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="0" y="0" width="122" height="12" uuid="5f9e3d5c-6a60-40d6-bb2f-ee88f09c8eb4"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_paciente")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="235" y="0" width="125" height="12" uuid="59e2abb8-4c4e-4fd3-a20e-6caf443c288a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_procedimento")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="671" y="0" width="72" height="12" uuid="c61662e9-c506-4b6d-ada3-bae4d3fb3e7c"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_unidade_solicitante_abv2")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="166" y="0" width="69" height="12" uuid="902a3106-e4c7-422a-b3a8-79c3a60c638d"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_telefone")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="122" y="0" width="41" height="12" uuid="3a179b3e-df4c-46fe-8368-96ee8ed98079"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_idade")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="560" y="0" width="106" height="12" uuid="5196f0bb-1114-48f9-9a87-5162abef3e73"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_profissional_executante")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" x="746" y="0" width="56" height="12" uuid="2d53df11-e523-4b3a-b154-d5986d17edbe"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_situacao_2")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="24" splitType="Stretch">
			<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{informaProcedimentoCiapManual})]]></printWhenExpression>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="372" y="0" width="120" height="12" uuid="68442bcf-1ce6-4ca5-8acc-15de5213860c"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresa}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement key="textField-4" x="502" y="0" width="45" height="12" uuid="a0750e28-b0e9-4067-a293-1a4951ea071e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoData}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="0" y="0" width="122" height="12" uuid="9628e256-679c-4355-b984-c62c8c2f6fdc"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{paciente}.getReferenciaEtiquetaSocial()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="122" y="0" width="41" height="12" uuid="bb4453a1-fb14-4703-b21e-94edb3bea705"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{idadePaciente}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="235" y="0" width="125" height="12" uuid="8287ffe8-b66a-493e-9669-b6c37cd6ecfb"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoTipoProcedimento}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement key="textField-4" x="560" y="0" width="106" height="12" uuid="b11efad6-bf28-4b61-a47c-4b7563153fb5"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nomeProfissional}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="166" y="0" width="69" height="12" uuid="0f896247-a094-4a8e-ad34-82d52df78b79"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{paciente}.getCelularOuTelefonesFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="746" y="0" width="56" height="12" uuid="7cc391f0-2834-4f01-a017-b02fcf9b0406"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoSituacao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="671" y="0" width="72" height="12" uuid="52fc8a00-67b6-463c-be06-4feadd69b90e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresaOrigem}.getDescricao()]]></textFieldExpression>
			</textField>
			<elementGroup>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-4" stretchType="RelativeToBandHeight" x="0" y="12" width="95" height="12" isPrintWhenDetailOverflows="true" uuid="2fea994b-35d0-4b27-bb21-430d6f666838">
						<printWhenExpression><![CDATA[AgendaGradeAtendimentoHorario.STATUS_CANCELADO.equals($F{status})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_motivo_cancelamento_rel") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-4" stretchType="RelativeToBandHeight" x="95" y="12" width="704" height="12" isPrintWhenDetailOverflows="true" uuid="8b6bc5ec-4339-419f-89dd-39fc95d1a4c0">
						<printWhenExpression><![CDATA[AgendaGradeAtendimentoHorario.STATUS_CANCELADO.equals($F{status})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{descricaoCancelamento}.toUpperCase()]]></textFieldExpression>
				</textField>
			</elementGroup>
		</band>
		<band height="38">
			<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{informaProcedimentoCiapManual})]]></printWhenExpression>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="366" y="12" width="120" height="12" uuid="68442bcf-1ce6-4ca5-8acc-15de5213860c"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement key="textField-4" x="486" y="12" width="79" height="12" uuid="a0750e28-b0e9-4067-a293-1a4951ea071e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoData}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="0" y="12" width="125" height="12" uuid="9628e256-679c-4355-b984-c62c8c2f6fdc"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{paciente}.getDescricaoReferenciaSocialFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="125" y="12" width="41" height="12" uuid="bb4453a1-fb14-4703-b21e-94edb3bea705"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{idadePaciente}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="238" y="12" width="125" height="12" uuid="8287ffe8-b66a-493e-9669-b6c37cd6ecfb"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement key="textField-4" x="567" y="12" width="117" height="12" uuid="b11efad6-bf28-4b61-a47c-4b7563153fb5"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nomeProfissional}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="169" y="12" width="69" height="12" uuid="0f896247-a094-4a8e-ad34-82d52df78b79"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{paciente}.getCelularOuTelefonesFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="757" y="12" width="45" height="12" uuid="7cc391f0-2834-4f01-a017-b02fcf9b0406"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoSituacao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-4" x="684" y="12" width="73" height="12" uuid="52fc8a00-67b6-463c-be06-4feadd69b90e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresaOrigem}.getDescricao()]]></textFieldExpression>
			</textField>
			<elementGroup>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-4" stretchType="RelativeToBandHeight" x="0" y="24" width="95" height="12" isPrintWhenDetailOverflows="true" uuid="2fea994b-35d0-4b27-bb21-430d6f666838">
						<printWhenExpression><![CDATA[AgendaGradeAtendimentoHorario.STATUS_CANCELADO.equals($F{status})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_motivo_cancelamento_rel") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-4" stretchType="RelativeToBandHeight" x="95" y="24" width="704" height="12" isPrintWhenDetailOverflows="true" uuid="8b6bc5ec-4339-419f-89dd-39fc95d1a4c0">
						<printWhenExpression><![CDATA[AgendaGradeAtendimentoHorario.STATUS_CANCELADO.equals($F{status})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{descricaoCancelamento}.toUpperCase()]]></textFieldExpression>
				</textField>
			</elementGroup>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
