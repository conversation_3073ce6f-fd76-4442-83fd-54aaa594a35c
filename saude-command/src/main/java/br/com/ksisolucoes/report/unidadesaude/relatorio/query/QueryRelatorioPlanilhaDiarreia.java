package br.com.ksisolucoes.report.unidadesaude.relatorio.query;

import br.com.celk.report.unidadesaude.interfaces.dto.RelatorioPlanilhaDiarreiaDTO;
import br.com.celk.report.unidadesaude.interfaces.dto.RelatorioPlanilhaDiarreiaDTOParam;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.FaixaEtaria;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioPlanilhaDiarreia extends CommandQuery implements ITransferDataReport<RelatorioPlanilhaDiarreiaDTOParam, RelatorioPlanilhaDiarreiaDTO> {

    private RelatorioPlanilhaDiarreiaDTOParam param;
    private List<RelatorioPlanilhaDiarreiaDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.addToSelect("a.codigo", "atendimentoMDDA.atendimento.codigo");
        hql.addToSelect("a.dataAtendimento", "atendimentoMDDA.atendimento.dataAtendimento");

        hql.addToSelect("usuarioCadsus.codigo", "atendimentoMDDA.atendimento.usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "atendimentoMDDA.atendimento.usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.dataNascimento", "atendimentoMDDA.atendimento.usuarioCadsus.dataNascimento");

        hql.addToSelect("enderecoUsuarioCadsus.logradouro", "atendimentoMDDA.atendimento.enderecoUsuarioCadsus.logradouro");
        hql.addToSelect("enderecoUsuarioCadsus.tipoLogradouro", "atendimentoMDDA.atendimento.enderecoUsuarioCadsus.tipoLogradouro");
        hql.addToSelect("enderecoUsuarioCadsus.bairro", "atendimentoMDDA.atendimento.enderecoUsuarioCadsus.bairro");
        hql.addToSelect("enderecoUsuarioCadsus.numeroLogradouro", "atendimentoMDDA.atendimento.enderecoUsuarioCadsus.numeroLogradouro");

        hql.addToSelect("cidade.codigo", "atendimentoMDDA.atendimento.enderecoUsuarioCadsus.cidade.codigo");
        hql.addToSelect("cidade.descricao", "atendimentoMDDA.atendimento.enderecoUsuarioCadsus.cidade.descricao");

        hql.addToSelect("segmentoTerritorial.codigo", "segmentoTerritorial.codigo");
        hql.addToSelect("segmentoTerritorial.descricao", "segmentoTerritorial.descricao");

        hql.addToSelect("cid.codigo", "atendimentoMDDA.atendimento.cidPrincipal.codigo");
        hql.addToSelect("cid.descricao", "atendimentoMDDA.atendimento.cidPrincipal.descricao");

        hql.addToSelect("mdda.codigo", "atendimentoMDDA.codigo");
        hql.addToSelect("mdda.comSangue", "atendimentoMDDA.comSangue");
        hql.addToSelect("mdda.dataPrimeirosSintomas", "atendimentoMDDA.dataPrimeirosSintomas");
        hql.addToSelect("mdda.resultadoExameLaboratorial", "atendimentoMDDA.resultadoExameLaboratorial");
        hql.addToSelect("mdda.planoTratamento", "atendimentoMDDA.planoTratamento");

        hql.addToSelect("mdda.planoTratamento", "atendimentoMDDA.planoTratamento");

        hql.setTypeSelect(RelatorioPlanilhaDiarreiaDTO.class.getName());

        hql.addToFrom("AtendimentoMDDA mdda"
                + " join mdda.atendimento a"
                + " left join a.cidPrincipal cid"
                + " join a.empresa empresa"
                + " join a.usuarioCadsus usuarioCadsus"
                + " left join a.enderecoDomicilio enderecoDomicilio"
                + " left join enderecoDomicilio.equipeMicroArea equipeMicroArea"
                + " left join equipeMicroArea.equipeArea equipeArea"
                + " left join equipeArea.segmentoTerritorial segmentoTerritorial"
                + " left join a.enderecoUsuarioCadsus enderecoUsuarioCadsus"
                + " left join enderecoUsuarioCadsus.cidade cidade");

        hql.addToWhereWhithAnd("cid = ", param.getCid());
        hql.addToWhereWhithAnd("empresa = ", param.getEmpresa());
        hql.addToWhereWhithAnd("a.dataAtendimento", param.getPeriodo());
        hql.addToWhereWhithAnd("mdda.planoTratamento = ", param.getPlanoTratamento());
        hql.addToWhereWhithAnd("a.status = ", Atendimento.STATUS_FINALIZADO);

        if (!param.getFormaApresentacao().equals(RelatorioPlanilhaDiarreiaDTOParam.FormaApresentacao.GERAL)) {
            if (param.getFormaApresentacao().equals(RelatorioPlanilhaDiarreiaDTOParam.FormaApresentacao.UNIDADE)) {
                hql.addToSelectAndOrder("empresa.descricao", "atendimentoMDDA.atendimento.empresa.descricao");
                hql.addToSelectAndOrder("empresa.codigo", "atendimentoMDDA.atendimento.empresa.codigo");
            } else if (param.getFormaApresentacao().equals(RelatorioPlanilhaDiarreiaDTOParam.FormaApresentacao.PLANO_TRATAMENTO)) {
                hql.addToOrder("mdda.planoTratamento" + QueryCustom.QueryCustomSorter.CRESCENTE_NULLS_FIRST);
            } else if (param.getFormaApresentacao().equals(RelatorioPlanilhaDiarreiaDTOParam.FormaApresentacao.FAIXA_ETARIA)) {
                hql.addToSelectAndOrder("faixaEtariaItem.descricao", "faixaEtariaItem.descricao");
                hql.addToSelectAndOrder("faixaEtariaItem.id.sequencia", "faixaEtariaItem.id.sequencia");
                hql.addToSelectAndOrder("faixaEtaria.codigo", "faixaEtariaItem.id.faixaEtaria.codigo");
            } else if (param.getFormaApresentacao().equals(RelatorioPlanilhaDiarreiaDTOParam.FormaApresentacao.CID)) {
                hql.addToOrder("cid.descricao");
                hql.addToOrder("cid.codigo");
            }
        }

        if (param.getFaixaEtariaItem() != null || param.getFormaApresentacao().equals(RelatorioPlanilhaDiarreiaDTOParam.FormaApresentacao.FAIXA_ETARIA)) {
            hql.addToFrom("FaixaEtariaItem faixaEtariaItem "
                    + " join faixaEtariaItem.id.faixaEtaria faixaEtaria ");

            hql.addToWhereWhithAnd("faixaEtaria.codigo = ", FaixaEtaria.FAIXA_ETARIA_SIAB);
            hql.addToWhereWhithAnd("cast(extract(years from age(current_date, usuarioCadsus.dataNascimento)) * 12 + extract(months from age(current_date, usuarioCadsus.dataNascimento)) as long) between faixaEtariaItem.idadeInicial and faixaEtariaItem.idadeFinal");

            if (this.param.getFaixaEtariaItem() != null) {
                hql.addToWhereWhithAnd("faixaEtariaItem.id.sequencia = ", this.param.getFaixaEtariaItem().getId().getSequencia());
            }
        }
    }

    @Override
    public List<RelatorioPlanilhaDiarreiaDTO> getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public void setDTOParam(RelatorioPlanilhaDiarreiaDTOParam param) {
        this.param = param;
    }

}
