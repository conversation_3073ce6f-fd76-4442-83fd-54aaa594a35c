package br.com.ksisolucoes.report.encaminhamento;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.encaminhamento.dto.RelacaoAgendamentoTfdDTOParam;
import br.com.ksisolucoes.report.encaminhamento.query.QueryRelacaoAgendamentosTfd;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelacaoAgendamentoTfd extends AbstractReport<RelacaoAgendamentoTfdDTOParam> {

    public RelacaoAgendamentoTfd(RelacaoAgendamentoTfdDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/encaminhamento/jrxml/relacao_agendamentos_tfd.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_agendamento_tfd");
    }

    @Override
    public ITransferDataReport getQuery() {
        addParametro("formaApresentacao", getParam().getFormaApresentacao());

        return new QueryRelacaoAgendamentosTfd();
    }

}
