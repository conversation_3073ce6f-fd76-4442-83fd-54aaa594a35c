<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_transferencia_estoque" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="0e1167b4-a5ae-486b-b46c-52ab9980f3c2">
	<property name="ireport.zoom" value="1.500000000000006"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioTransferenciaEstoqueDTO"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<field name="empresaOrigem" class="java.lang.String"/>
	<field name="depositoOrigem" class="java.lang.String"/>
	<field name="produto" class="java.lang.String"/>
	<field name="lote" class="java.lang.String"/>
	<field name="quantidade" class="java.lang.Double"/>
	<field name="empresaDestino" class="java.lang.String"/>
	<field name="depositoDestino" class="java.lang.String"/>
	<field name="dataLancamento" class="java.lang.String"/>
	<field name="numeroDocumento" class="java.lang.String"/>
	<field name="observacao" class="java.lang.String"/>
	<group name="Transferencia" isReprintHeaderOnEachPage="true">
		<groupHeader>
			<band height="191">
				<rectangle radius="5">
					<reportElement x="0" y="7" width="554" height="179" uuid="30ff4716-0c00-4b67-a75a-cfe5429e3048"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField>
					<reportElement x="5" y="17" width="90" height="14" uuid="7ee469c0-3143-435f-85c5-4c7091875682"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_unidade_origem")+":"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="97" y="17" width="270" height="14" uuid="7ea9ea2a-**************-98685212f7b8"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{empresaOrigem}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
					<reportElement x="445" y="17" width="106" height="14" uuid="7ea9ea2a-**************-98685212f7b8"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{dataLancamento}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="368" y="17" width="74" height="14" uuid="7ee469c0-3143-435f-85c5-4c7091875682"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_transferencia_abv")+":"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="97" y="91" width="268" height="14" uuid="7ea9ea2a-**************-98685212f7b8"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{produto}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="91" width="90" height="14" uuid="7ee469c0-3143-435f-85c5-4c7091875682"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_produto")+":"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="17" y="0" width="86" height="14" uuid="7ee469c0-3143-435f-85c5-4c7091875682"/>
					<box leftPadding="2"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dados_gerais")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="108" width="90" height="14" uuid="b4fe1bf1-f104-4dc1-9e7e-8cbeea2de786"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_lote")+":"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="97" y="108" width="268" height="14" uuid="58cd4d75-55bd-4704-9b3f-26c4e5aa1e38"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{lote}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="368" y="108" width="74" height="14" uuid="f50f020b-9f08-4b8f-ba8d-2c445c68af75"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quantidade")+":"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="445" y="108" width="106" height="14" uuid="c311f7d1-843d-4f1c-949d-8a1f79ee25cc"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{quantidade}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="97" y="53" width="454" height="14" uuid="eed7fca8-83f6-46e1-b5a8-f607d31fc190"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{empresaDestino}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="97" y="69" width="454" height="14" uuid="3d22c2e0-00e0-439d-ad8e-d48670aac87e"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{depositoDestino}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="33" width="90" height="14" uuid="acc52a32-4a5d-4f28-b8ed-9b6466853f7c"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_deposito_origem")+":"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="97" y="33" width="454" height="14" uuid="14e19a08-b0a1-4041-b05d-396f7085ce45"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{depositoOrigem}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="53" width="90" height="14" uuid="43e89723-297f-4dba-8099-c3bae2d9a3b5"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_empresa_destino")+":"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="69" width="90" height="14" uuid="19d00b83-c47b-44dc-95a6-ef5044e9515f"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_deposito_destino")+":"]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="5" y="87" width="546" height="1" uuid="e3527a00-9241-48f2-a685-0cf492ecce63"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField>
					<reportElement x="368" y="91" width="74" height="14" uuid="3322f8ac-e1b7-49e1-b3ed-41c461bbaa0e"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_num_documento_abv")+":"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
					<reportElement x="445" y="91" width="106" height="14" uuid="d4e1917d-5ca7-43e8-859a-f8ed44acc64d"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{numeroDocumento}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="125" width="90" height="14" uuid="76a3938d-5de0-4816-aaa1-bcae4be442c8"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_observacao")+":"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="97" y="125" width="454" height="59" uuid="d11c0107-0d2e-4e38-a1c7-c66d97961bee"/>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Arial" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{observacao}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="192" splitType="Stretch"/>
	</detail>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
