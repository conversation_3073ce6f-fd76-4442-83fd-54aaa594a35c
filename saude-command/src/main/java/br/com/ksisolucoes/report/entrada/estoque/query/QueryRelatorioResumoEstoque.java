/*
 * QueryMovimentacaoEmDeterminadaData.java
 *
 * Created on 18 de Outubro de 2005, 15:05
 *
 * To change this template, choose Tools | Options and locate the template under
 * the Source Creation and Management node. Right-click the template and choose
 * Open. You can then make changes to the template in the Source Editor.
 */

package br.com.ksisolucoes.report.entrada.estoque.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioResumoEstoqueDTO;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioResumoEstoqueDTOParam;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaSetor;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import ch.lambdaj.Lambda;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @alterado  Jussara Possamai
 */
public class QueryRelatorioResumoEstoque extends CommandQuery < QueryRelatorioResumoEstoque > implements ReportProperties, ITransferDataReport<RelatorioResumoEstoqueDTOParam, RelatorioResumoEstoqueDTO> {
    
    private RelatorioResumoEstoqueDTOParam bean;
    private List< RelatorioResumoEstoqueDTO > listRelatorioResumoEstoque;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.setTypeSelect(RelatorioResumoEstoqueDTO.class.getName());
        hql.addToFrom("MovimentoEstoque me " +
                " left join me.empresaDestino ed "+
                " left join me.centroCusto cc");
        
        hql.addToSelect("me.id.empresa.referencia", "codigoEmpresa");
        hql.addToSelect("me.id.empresa.descricao", "descricaoEmpresa");
        hql.addToSelect("me.produto.referencia", "codigoProduto");
        hql.addToSelect("me.produto.descricao", "descricaoProduto");
        hql.addToSelect("me.produto.unidade.unidade", "unidadeProduto");
        hql.addToSelect("me.tipoDocumento.descricao", "descricaoTipoDocumento");
        hql.addToSelect("me.tipoDocumento.flagSigla", "flagSiglaTipoDocumento");
        hql.addToSelect("me.tipoDocumento.flagTipoMovimento", "flagTipoMovimentoTipoDocumento");
        hql.addToSelect("me.quantidade", "quantidade");
        hql.addToSelect("me.precoCusto", "precoCusto");
        hql.addToSelect("me.precoMedio", "precoMedio");
        hql.addToSelect("me.precoUnitario", "precoUnitario");
        hql.addToSelect("cc.codigo", "codigoCentroCusto");
        hql.addToSelect("cc.descricao", "descricaoCentroCusto");
        
        if ( !( RESUMIDO == this.bean.getTipoRelatorio().intValue() && this.bean.getFormaApresentacao().intValue() == GERAL ) ) {
            hql.addToSelect("me.produto.subGrupo.roGrupoProduto.codigo", "codigoGrupoProduto");
            hql.addToSelect("me.produto.subGrupo.roGrupoProduto.descricao", "descricaoGrupoProduto");
            hql.addToSelect("me.produto.subGrupo.id.codigo", "codigoSubGrupo");
            hql.addToSelect("me.produto.subGrupo.descricao", "descricaoSubGrupo");
            if (this.bean.getFormaApresentacao().intValue() == AGRUPAR_EMPRESA) {
                hql.addToSelect("ed.referencia", "codigoEmpresaDestino");
                hql.addToSelect("ed.descricao", "descricaoEmpresaDestino");
            }
        }

        if(bean.getLstEmpresa() != null && CollectionUtils.isNotNullEmpty(bean.getLstEmpresa().getValue())){
            if(bean.getLstEmpresa().getValue().size() > 1){
                hql.addToWhereWhithAnd("me.id.empresa ", bean.getLstEmpresa());
            } else {
                Empresa empresa = bean.getLstEmpresa().getValue().get(0);

                List<EmpresaSetor> empresaSetorList = LoadManager.getInstance(EmpresaSetor.class)
                        .addProperty(VOUtils.montarPath(EmpresaSetor.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(EmpresaSetor.PROP_SETOR, Empresa.PROP_CODIGO))
                        .addParameter(new QueryCustom.QueryCustomParameter(EmpresaSetor.PROP_EMPRESA, empresa))
                        .addParameter(new QueryCustom.QueryCustomParameter(EmpresaSetor.PROP_SITUACAO, EmpresaSetor.Situacao.ATIVO.value()))
                        .start().getList();

                List<Long> codigosEmpresasList = new ArrayList<>();
                if (CollectionUtils.isNotNullEmpty(empresaSetorList)) {
                    codigosEmpresasList = Lambda.extract(empresaSetorList, Lambda.on(EmpresaSetor.class).getSetor().getCodigo());
                }
                codigosEmpresasList.add(empresa.getCodigo());

                hql.addToWhereWhithAnd("me.id.empresa.codigo in ", codigosEmpresasList);
            }
        }

        if (bean.getLstEmpresaDestino() != null && bean.getLstEmpresaDestino().getValue().size() == 1) {
            hql.addToWhereWhithAnd("ed ", bean.getLstEmpresaDestino());
        }

        hql.addToWhereWhithAnd("me.produto ", bean.getLstProduto() );
        hql.addToWhereWhithAnd("me.dataLancamento ", bean.getPeriodo());

        if (CollectionUtils.isNotNullEmpty(bean.getLstGrupoProduto().getValue())) {
            hql.addToWhereWhithAnd("me.produto.subGrupo.roGrupoProduto ", bean.getLstGrupoProduto());
        }
        
        if (bean.getGrupoProdutoSubGrupo() != null) {
            hql.addToWhereWhithAnd("me.produto.subGrupo.roGrupoProduto = ", bean.getGrupoProdutoSubGrupo());
            if (this.bean.getSubGrupo() != null) {
                hql.addToWhereWhithAnd(" me.produto.subGrupo.id.codigo = ", bean.getSubGrupo().getId().getCodigo());
            }
        }

        //Localizacao
        HQLHelper hqlWhereLocalizacao = hql.getNewInstanceSubQuery();

        hqlWhereLocalizacao.addToSelect("1");
        hqlWhereLocalizacao.addToFrom("EstoqueEmpresa ee");
        hqlWhereLocalizacao.addToWhereWhithAnd("ee.id.empresa.codigo = me.id.empresa.codigo");
        hqlWhereLocalizacao.addToWhereWhithAnd("ee.id.produto.codigo = me.produto.codigo");
        hqlWhereLocalizacao.addToWhereWhithAnd("ee.localizacao ", this.bean.getLstLocalizacao());

        hql.addToWhereWhithAnd("exists("+hqlWhereLocalizacao.getQuery()+")");

        hql.addToWhereWhithAnd(" me.tipoDocumento ", bean.getLstTipoDocumento() );

        hql.addToWhereWhithAnd(" cc ", bean.getLstCentroCusto() );

        if(this.bean.getTipo().intValue() == TIPO_SIM){
            hql.addToWhereWhithAnd(" me.tipoDocumento.flagImprimeResumo = ", RepositoryComponentDefault.SIM );
        }

        if (!ReportProperties.AMBOS.equals(this.bean.getTipoMovimentacao())) {
            hql.addToWhereWhithAnd(" me.tipoDocumento.flagTipoMovimento = ", this.bean.getTipoMovimentacao() );
        }

        if (RepositoryComponentDefault.NAO.equals(this.bean.getAgruparEmpresa())) {
            hql.addToOrder("me.id.empresa.descricao");
        }
        
        if(this.bean.getTipoRelatorio().intValue() == DETALHADO){
            if (this.bean.getFormaApresentacao().intValue() == AGRUPAR_GRUPO){
                hql.addToOrder("me.produto.subGrupo.roGrupoProduto.descricao");
                hql.addToOrder("me.produto.subGrupo.descricao");
            } else if (this.bean.getFormaApresentacao().intValue() == AGRUPAR_EMPRESA){
                hql.addToOrder("ed.codigo");
                hql.addToOrder("ed.descricao");
            } else if (this.bean.getFormaApresentacao().intValue() == AGRUPAR_CENTRO_CUSTO){
                hql.addToOrder("cc.codigo");
                hql.addToOrder("cc.descricao");
            }
        } else if(this.bean.getTipoRelatorio().intValue() == RESUMIDO){
            
            if (this.bean.getFormaApresentacao().intValue() == AGRUPAR_GRUPO){
                hql.addToOrder("me.produto.subGrupo.roGrupoProduto.descricao");
                hql.addToOrder("me.produto.subGrupo.descricao");
            } else if (this.bean.getFormaApresentacao().intValue() == AGRUPAR_EMPRESA){
                hql.addToOrder("ed.codigo");
                hql.addToOrder("ed.descricao");
            } else if (this.bean.getFormaApresentacao().intValue() == AGRUPAR_CENTRO_CUSTO){
                hql.addToOrder("cc.codigo");
                hql.addToOrder("cc.descricao");
            }
        }

        if(this.bean.getOrdenacao().equals(Produto.PROP_CODIGO)){
            hql.addToOrder("me.produto.codigo");
            hql.addToOrder("me.produto.unidade.unidade");
            hql.addToOrder("me.tipoDocumento.descricao");
        } else{
            hql.addToOrder("me.produto.descricao");
            hql.addToOrder("me.produto.unidade.unidade");
            hql.addToOrder("me.tipoDocumento.descricao");
        }
        
    }
    
    public List< RelatorioResumoEstoqueDTO > getListRelatorioResumoEstoque() {
        return listRelatorioResumoEstoque;
    }

    @Override
    public void setDTOParam(RelatorioResumoEstoqueDTOParam arg0) {
        bean = arg0;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.listRelatorioResumoEstoque = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public Collection<RelatorioResumoEstoqueDTO> getResult() {
        return listRelatorioResumoEstoque;
    }
}