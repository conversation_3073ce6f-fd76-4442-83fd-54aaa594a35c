/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.vigilancia;

import br.com.celk.report.vigilancia.query.QueryFichaPeste;
import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FichaInvestigacaoAgravoDTOParam;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;

import java.util.LinkedHashMap;

public class ImpressaoFichaInvestigacaoAgravoPeste extends AbstractReport<FichaInvestigacaoAgravoDTOParam> {

    private QueryFichaPeste query;

    public ImpressaoFichaInvestigacaoAgravoPeste(FichaInvestigacaoAgravoDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        if(query == null){
            query = new QueryFichaPeste();
        }
        return query;
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> campos = new LinkedHashMap<>(((QueryFichaPeste)getQuery()).getMapeamentoPlanilhaBase());

        campos.put("dataInvestigacao", "_31_data_investigacao");
        campos.put("ocupacao", "_31_ocupacao");
        campos.put("cumpreCondicoesBasicasRisco", "_33_cumpreCondicoesBasicasRisco");
        campos.put("casoAssociadoEventosPositivos", "_34_casoAssociadoEventosPositivos");
        campos.put("sinaisSintomasCompativeis", "_35_sinaisSintomasCompativeis");
        campos.put("sintomalogiaEspecificaGanglionar", "_36_sintomalogiaEspecificaGanglionar");
        campos.put("sintomalogiaEspecificaPulmonar", "_36_sintomalogiaEspecificaPulmonar");
        campos.put("exameBacteriologicoHemocultura", "_37_exameBacteriologicoHemocultura");
        campos.put("exameBacteriologicoEsfregacaoDireto", "_37_exameBacteriologicoEsfregacaoDireto");
        campos.put("dataColetaS1", "_38_dataColetaS1");
        campos.put("dataColetaS2", "_39_dataColetaS2");
        campos.put("resultadoSorologiaS1", "_40_resultadoSorologiaS1");
        campos.put("resultadoSorologiaS2", "_40_resultadoSorologiaS2");
        campos.put("resultadoHemoaglutinacaoIgm", "_41_resultadoHemoaglutinacaoIgm");
        campos.put("resultadoHemoaglutinacaoIgmTitulo", "_41_resultadoHemoaglutinacaoIgmTitulo");
        campos.put("resultadoHemoaglutinacaoIgg", "_41_resultadoHemoaglutinacaoIgg");
        campos.put("resultadoHemoaglutinacaoIggTitulo", "_41_resultadoHemoaglutinacaoIggTitulo");

        campos.put("casoAutoctone", "_48_casoAutoctone");
        campos.put("estado", "_49_estadoLocalInfeccao");
        campos.put("paisLocalInfeccao", "_50_paisLocalInfeccao");
        campos.put("cidadeLocalInfeccao", "_51_cidadeLocalInfeccaoDesc");
        campos.put("Codigo cidadeLocalInfeccao", "_51_cidadeLocalInfeccaoCod");
        campos.put("distritoLocalInfeccao", "_52_distritoLocalInfeccao");
        campos.put("bairroLocalInfeccao", "_53_bairroLocalInfeccao");

        campos.put("casoTratado", "_42_casoTratado");
        campos.put("controleFocal", "_43_controleFocal");
        campos.put("classificacaoFinal", "_44_classificacaoFinal");
        campos.put("criterioConfirmacaoDescarte", "_45_criterioConfirmacaoDescarte");
        campos.put("classificacaoFormaClinica", "_46_classificacaoFormaClinica");
        campos.put("gravidade", "_47_gravidade");
        campos.put("doencaRelacionadaTrabalho", "_54_doencaRelacionadaTrabalho");
        campos.put("evolucaoCaso", "_55_evolucaoCaso");

        campos.put("observacao", "_observacao");

        return campos;
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/vigilancia/pdf/ficha_investigacao_peste.pdf";
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return param.getTipoArquivo();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_peste");
    }

}
