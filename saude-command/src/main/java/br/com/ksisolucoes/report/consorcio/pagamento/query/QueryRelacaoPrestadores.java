package br.com.ksisolucoes.report.consorcio.pagamento.query;

import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.consorcio.dto.ConsorcioPrestadorDTO;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioRelacaoPrestadoresDTOParam;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.consorcio.TabelaPrecoEdital;

import java.util.List;
import java.util.Map;

public class QueryRelacaoPrestadores extends CommandQuery<QueryRelacaoPrestadores> implements ITransferDataReport<RelatorioRelacaoPrestadoresDTOParam, ConsorcioPrestadorDTO> {

    private RelatorioRelacaoPrestadoresDTOParam param;
    private List<ConsorcioPrestadorDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("cPrestadorEdt.codigo", "consorcioPrestadorEdital.codigo");

        hql.addToSelect("contratoEdt.dataContrato", "contratoEdital.dataContrato");
        hql.addToSelect("contratoEdt.numeroContrato", "contratoEdital.numeroContrato");
        hql.addToSelect("contratoEdt.dataVencimentoRenovacao", "contratoEdital.dataVencimentoRenovacao");
        hql.addToSelect("contratoEdt.codigo", "contratoEdital.codigo");

        hql.addToSelect("cPrestador.codigo", "consorcioPrestador.codigo");

        hql.addToSelect("ePrestador.codigo", "consorcioPrestador.empresaPrestador.codigo");
        hql.addToSelect("trim(ePrestador.referencia)", "consorcioPrestador.empresaPrestador.referencia");
        hql.addToSelect("trim(ePrestador.descricao)", "consorcioPrestador.empresaPrestador.descricao");
        hql.addToSelect("ePrestador.cnes", "consorcioPrestador.empresaPrestador.cnes");
        hql.addToSelect("ePrestador.cnpj", "consorcioPrestador.empresaPrestador.cnpj");
        hql.addToSelect("ePrestador.telefone", "consorcioPrestador.empresaPrestador.telefone");

        hql.addToSelect("cidade.codigo", "consorcioPrestador.empresaPrestador.cidade.codigo");
        hql.addToSelect("cidade.descricao", "consorcioPrestador.empresaPrestador.cidade.descricao");

        hql.addToSelect("tabelaPrecoEdt.codigo", "tabelaPrecoEdital.codigo");
        hql.addToSelect("trim(tabelaPrecoEdt.edital)", "tabelaPrecoEdital.edital");
        hql.addToSelect("tabelaPrecoEdt.dataFim", "tabelaPrecoEdital.dataFim");

        hql.setTypeSelect(ConsorcioPrestadorDTO.class.getName());
        hql.addToFrom("ConsorcioPrestadorEdital cPrestadorEdt " +
                "left join cPrestadorEdt.tabelaPrecoEdital tabelaPrecoEdt " +
                "left join cPrestadorEdt.contratoEdital contratoEdt " +
                "left join cPrestadorEdt.consorcioPrestador cPrestador " +
                "left join cPrestador.empresaPrestador ePrestador " +
                "left join ePrestador.cidade cidade");

        hql.addToWhereWhithAnd("tabelaPrecoEdt =", param.getTabelaPrecoEdital());
        hql.addToWhereWhithAnd("tabelaPrecoEdt.situacao =", TabelaPrecoEdital.Situacao.ATIVO.value());
        hql.addToWhereWhithAnd("cidade =", param.getCidade());

        if (!RelatorioRelacaoPrestadoresDTOParam.StatusConsorcioPrestador.AMBOS.value().equals(param.getSituacaoPrestador())) {
            hql.addToWhereWhithAnd("cPrestador.status =", param.getSituacaoPrestador());
        }

        if (RelatorioRelacaoPrestadoresDTOParam.StatusContratoPrestador.ATIVO.value().equals(param.getSituacaoContrato())) {
            hql.addToWhereWhithAnd("contratoEdt.dataVencimentoRenovacao <=", Data.addDias(DataUtil.getDataAtualSemHora(), Coalesce.asLong(param.getDiasVencimentoContrato()).intValue()));
        } else if (RelatorioRelacaoPrestadoresDTOParam.StatusContratoPrestador.INATIVO.value().equals(param.getSituacaoContrato())) {
            hql.addToWhereWhithAnd("contratoEdt.dataVencimentoRenovacao <", DataUtil.getDataAtualSemHora());
        }

        if (RelatorioRelacaoPrestadoresDTOParam.FormaApresentacao.EDITAL.equals(param.getFormaApresentacao())) {
            hql.addToOrder("tabelaPrecoEdt.edital");
        } else if (RelatorioRelacaoPrestadoresDTOParam.FormaApresentacao.CIDADE.equals(param.getFormaApresentacao())) {
            hql.addToOrder("cidade.descricao");
        }

        if (RelatorioRelacaoPrestadoresDTOParam.Ordenacao.PRESTADOR.equals(param.getOrdenacao())) {
            hql.addToOrder("ePrestador.descricao " + param.getTipoOrdenacao().getCommand());
        } else {
            hql.addToOrder("contratoEdt.dataVencimentoRenovacao " + param.getTipoOrdenacao().getCommand());
        }
    }

    @Override
    public void setDTOParam(RelatorioRelacaoPrestadoresDTOParam param) {
        this.param = param;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<ConsorcioPrestadorDTO> getResult() {
        return result;
    }

}
