/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.vigilancia;

import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.MensagemDTO;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class GerarArquivoProcessoAdministrativoEnviarMensagemInterna extends AbstractCommandTransaction {

    private File fileProcesso;
    private ProcessoAdministrativo processoAdministrativo;
    private RequerimentoVigilancia requerimentoVigilancia;
    private GeradorArquivoRequerimentoVigilancia geradorArquivoRequerimentoVigilancia;
    private GeradorArquivosProcessoAdministrativo geradorArquivosProcessoAdministrativo;

    public GerarArquivoProcessoAdministrativoEnviarMensagemInterna(ProcessoAdministrativo processoAdministrativo) {
        this.processoAdministrativo = processoAdministrativo;
        geradorArquivosProcessoAdministrativo = new GeradorArquivosProcessoAdministrativo(processoAdministrativo);
    }

    public GerarArquivoProcessoAdministrativoEnviarMensagemInterna(RequerimentoVigilancia requerimentoVigilancia) {
        this.requerimentoVigilancia = requerimentoVigilancia;
        geradorArquivoRequerimentoVigilancia = new GeradorArquivoRequerimentoVigilancia(requerimentoVigilancia);
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        MensagemDTO dto = null;
        List<Usuario> usuarioList = new ArrayList<Usuario>();
        usuarioList.add(getSessao().getUsuario());
        try {
            if (this.requerimentoVigilancia != null) {
                this.fileProcesso = geradorArquivoRequerimentoVigilancia.gerarArquivosRequerimentoVigilancia();
            } else if (processoAdministrativo != null) {
                this.fileProcesso = geradorArquivosProcessoAdministrativo.gerarArquivoProcessoAdministrativo();
            }

            if (this.fileProcesso != null) {
                BOFactory.getBO(ComunicacaoFacade.class).enviarMensagem(setMensagemDto());
            } else {
                throw new ValidacaoException("Nenhum documento disponível para impressão");
            }
        } catch (DAOException e) {
            try {
                dto = getMensagemDTOErro(usuarioList, e.getMessage());
                BOFactory.getBO(ComunicacaoFacade.class).enviarMensagem(dto);
            } catch (DAOException ex) {
                Loggable.log.error(e.getMessage());
            }
        }
    }

    private MensagemDTO getMensagemDTOErro(List<Usuario> usuarios, String message) {
        MensagemDTO mensagemDTO = new MensagemDTO();
        mensagemDTO.setAssunto(Bundle.getStringApplication("assunto_falha_impressao", processoAdministrativo.getNumeroProcessoFormatado()));
        mensagemDTO.setUsuarios(usuarios);
        mensagemDTO.setMensagem(msg(message));
        return mensagemDTO;
    }

    private String msg(String message) {
        StringBuilder sb = new StringBuilder();
        sb.append(Bundle.getStringApplication("msg_falha_impressao", processoAdministrativo.getNumeroProcessoFormatado()));
        sb.append("\n");
        sb.append("\n");
        sb.append("Entre em contato com o suporte enviando o texto a seguir.").append("\n");
        sb.append(message);
        return sb.toString();
    }


    public MensagemDTO setMensagemDto() {
        List<Usuario> usuarioList = new ArrayList<Usuario>();
        usuarioList.add(getSessao().getUsuario());
        MensagemDTO dto;
        if (this.requerimentoVigilancia != null) {
            dto = geradorArquivoRequerimentoVigilancia.getMensagemDTORequerimento(usuarioList);
        } else {
            dto = geradorArquivosProcessoAdministrativo.getMensagemDTOProcesso(usuarioList);
        }
        return dto;
    }
}