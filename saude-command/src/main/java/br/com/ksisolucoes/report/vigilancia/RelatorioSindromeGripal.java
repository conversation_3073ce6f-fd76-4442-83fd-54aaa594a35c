package br.com.ksisolucoes.report.vigilancia;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioSindromeGripalDTOParam;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.vigilancia.query.QueryRelatorioSindromeGripal;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;

/**
 *
 * <AUTHOR>
 */
public class RelatorioSindromeGripal extends AbstractReport<RelatorioSindromeGripalDTOParam> {

    public RelatorioSindromeGripal(RelatorioSindromeGripalDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        this.addParametro("formaApresentacao", this.getParam().getFormaApresentacao());
        this.addParametro("dataInicial", this.getParam().getPeriodo() != null ? Data.formatar(this.getParam().getPeriodo().getDataInicial()) : "");
        this.addParametro("dataFinal", this.getParam().getPeriodo() != null ? Data.formatar(this.getParam().getPeriodo().getDataFinal()) : "");
        this.addParametro("semana", this.getParam().getSemana());
        this.addParametro("ano", this.getParam().getAno());

        return new QueryRelatorioSindromeGripal();
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_sindrome_gripal.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_sindrome_gripal");
    }

}
