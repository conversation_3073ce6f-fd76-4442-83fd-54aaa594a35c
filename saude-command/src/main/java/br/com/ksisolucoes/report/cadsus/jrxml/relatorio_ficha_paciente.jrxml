<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_ficha_paciente" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="70a3c586-d4d9-42bb-b9f3-c789422c5944">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.200000000000015"/>
	<property name="ireport.x" value="179"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.util.Util"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="cpf" class="java.lang.String"/>
	<field name="identidade" class="java.lang.String"/>
	<field name="pisPasep" class="java.lang.String"/>
	<field name="enderecoUsuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus"/>
	<field name="convenio" class="br.com.ksisolucoes.vo.prontuario.basico.Convenio"/>
	<field name="leitoQuarto" class="br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto"/>
	<field name="quartoInternacao" class="br.com.ksisolucoes.vo.prontuario.hospital.QuartoInternacao"/>
	<field name="dataChegada" class="java.util.Date"/>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="numeroAtendimento" class="java.lang.Long"/>
	<field name="tipoAtendimento" class="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"/>
	<field name="cns" class="java.lang.String"/>
	<field name="tipoCertidao" class="java.lang.String"/>
	<field name="folha" class="java.lang.String"/>
	<field name="termo" class="java.lang.String"/>
	<field name="livro" class="java.lang.String"/>
	<field name="cartorio" class="java.lang.String"/>
	<field name="dataEmissaoCertidao" class="java.util.Date"/>
	<field name="usuarioCadsusAcompanhante" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="enderecoAcompanhante" class="br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus"/>
	<field name="orgaoEmissor" class="br.com.ksisolucoes.vo.basico.OrgaoEmissor"/>
	<field name="numeroRegistro" class="java.lang.String">
		<fieldDescription><![CDATA[atendimento.numeroRegistroConvenio]]></fieldDescription>
	</field>
	<field name="atendimento" class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"/>
	<field name="acompanhanteDTO" class="br.com.ksisolucoes.bo.cadsus.interfaces.dto.AcompanhanteUsuarioCadsusHospitalDTO"/>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="UTIL" class="br.com.ksisolucoes.util.Util"/>
	<variable name="viewProcedimentos" class="java.lang.Boolean">
		<variableExpression><![CDATA[RepositoryComponentDefault.SIM_LONG
    .equals($F{atendimento}.getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getFlagVisualizarProcedimentosFichaAtendimento())]]></variableExpression>
	</variable>
	<detail>
		<band height="51">
			<rectangle radius="5">
				<reportElement x="0" y="7" width="555" height="40" uuid="eaed94ec-622a-4cca-8819-44a4efdf69a4"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</rectangle>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="250" y="34" width="54" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{numeroAtendimento}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-76" mode="Transparent" x="381" y="12" width="25" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_leito")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-76" mode="Transparent" x="197" y="34" width="52" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_atendimento")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="381" y="23" width="70" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="62ccd2df-ed6d-4a6c-a5d5-9ca98adb6500"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_da_chegada")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH:mm" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="451" y="23" width="78" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataChegada}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="336" y="34" width="199" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tipoAtendimento}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-76" mode="Opaque" x="21" y="0" width="103" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dados_atendimento")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-76" mode="Transparent" x="7" y="34" width="25" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_setor")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="406" y="12" width="133" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{leitoQuarto}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-76" mode="Transparent" x="314" y="34" width="20" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_tipo")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-76" mode="Transparent" x="7" y="23" width="42" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_convenio")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="54" y="12" width="128" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getCodigo()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-76" mode="Transparent" x="7" y="12" width="44" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_prontuario")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="32" y="34" width="154" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresa}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="228" y="12" width="141" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quartoInternacao}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-76" mode="Transparent" x="197" y="12" width="30" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quarto")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="50" y="23" width="135" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{convenio}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-76" mode="Transparent" x="197" y="23" width="55" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nr_convenio")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="254" y="23" width="114" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{numeroRegistro}]]></textFieldExpression>
			</textField>
		</band>
		<band height="106">
			<rectangle radius="10">
				<reportElement mode="Opaque" x="-1" y="6" width="556" height="94" uuid="96d2d33c-e530-4ddb-af10-c71d9c7efbe7"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="280" y="24" width="24" height="10" uuid="38d54bb1-8d4e-48ac-8d2b-89ed94da99bb"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*sexo*/$V{BUNDLE}.getStringApplication("rotulo_sexo")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="165" y="24" width="95" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getDescricaoIdade()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="305" y="24" width="44" height="10" uuid="d92e26df-293b-4108-bdf3-0e73a1795df1"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getSexoFormatado()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="8" y="24" width="50" height="10" uuid="f466982f-5b6b-40b6-a0a5-ce7d0ea92651"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*dt nasc*/$V{BUNDLE}.getStringApplication("rotulo_nascimento")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="58" y="24" width="46" height="10" uuid="cdfb2f82-329a-4a91-aeeb-139b3557bc26"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getDataNascimento()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="280" y="35" width="43" height="10" uuid="7b7a7f70-001c-4340-95d6-7a925ba247ab"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*raca/cor*/$V{BUNDLE}.getStringApplication("rotulo_raca_cor")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="324" y="35" width="134" height="10" uuid="e8033e90-4213-4c88-8c58-32e10d8c3fa9"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getRaca()!=null?
$F{usuarioCadsus}.getRaca().getDescricao():
""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="388" y="24" width="50" height="10" uuid="c8e039d5-08ac-4dc0-b636-22502962ff10"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*estCivil*/$V{BUNDLE}.getStringApplication("rotulo_estado_civil")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="439" y="24" width="114" height="10" uuid="ab82b0bc-8f90-4449-abe1-7d87f7928f3a"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getEstadoCivil()!=null?
$F{usuarioCadsus}.getEstadoCivil().getDescricao():
""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="49" y="57" width="481" height="10" uuid="662bc767-c5df-4fa1-8fc4-85c7c2acff8d"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getTabelaCbo()!=null?
$F{usuarioCadsus}.getTabelaCbo().getDescricaoFormatado():
""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="8" y="57" width="40" height="10" uuid="7cb1ede7-fc3d-4191-a2ae-f1535e6b8901"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Profissao*/$V{BUNDLE}.getStringApplication("rotulo_profissao")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="8" y="79" width="56" height="10" uuid="cfa16ffb-216f-4414-ab4a-deb3757c36cf"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*mae*/$V{BUNDLE}.getStringApplication("rotulo_nome_mae")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="65" y="79" width="459" height="10" uuid="c4b58c31-f8dc-46fb-bafb-b1cf57446af2"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNomeMae()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="61" y="68" width="462" height="10" uuid="657d258f-6a8c-4fc9-baa0-cf14b68e3e7e"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNomePai()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="8" y="68" width="52" height="10" uuid="4a2a4c5f-b42a-4b73-9eee-52f24dae5c85"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*pai*/$V{BUNDLE}.getStringApplication("rotulo_nome_pai")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="8" y="35" width="38" height="10" uuid="10bf1cc4-116c-47dc-9904-300b230ea49d"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*telefone*/$V{BUNDLE}.getStringApplication("rotulo_telefone")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="47" y="35" width="67" height="10" uuid="15bbdce3-07e0-4914-ba5b-ccdb0f8e0a93"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getTelefoneFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="172" y="35" width="67" height="10" uuid="6351f741-f57b-4e38-b156-be8a7d4006d9"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getCelularFormatado()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="139" y="35" width="32" height="10" uuid="8baba926-b1e7-456d-9192-f36f15f24112"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*celular*/
$V{BUNDLE}.getStringApplication("rotulo_celular") + ": "]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="21" y="0" width="83" height="12" uuid="b3e2e3f5-a090-41c5-8ed1-5af10e361a62"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*dadosPaciente*/$V{BUNDLE}.getStringApplication("rotulo_dados_paciente")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="8" y="12" width="29" height="12" uuid="38d54bb1-8d4e-48ac-8d2b-89ed94da99bb"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*nome*/$V{BUNDLE}.getStringApplication("rotulo_nome")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="38" y="12" width="497" height="12" uuid="d92e26df-293b-4108-bdf3-0e73a1795df1"/>
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNome()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-76" mode="Transparent" x="139" y="24" width="25" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_idade")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="8" y="46" width="73" height="10" uuid="4a2a4c5f-b42a-4b73-9eee-52f24dae5c85"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Nome do Cônjuge: */$V{BUNDLE}.getStringApplication("rotulo_nome_conjuge")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="82" y="46" width="360" height="10" uuid="657d258f-6a8c-4fc9-baa0-cf14b68e3e7e"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNomeConjuge()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="93" y="90" width="417" height="10" uuid="89c06a95-33b3-43d1-bc63-31e4812541f3"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{profissional}.getNome()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="8" y="90" width="85" height="10" uuid="a2b81843-7b60-4714-ad0d-d92a5e41cb94"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*responsavel*/$V{BUNDLE}.getStringApplication("rotulo_medico_responsavel")+":"]]></textFieldExpression>
			</textField>
		</band>
		<band height="50">
			<printWhenExpression><![CDATA[$F{tipoCertidao} != null]]></printWhenExpression>
			<rectangle radius="10">
				<reportElement mode="Transparent" x="0" y="6" width="555" height="40" uuid="c1b911eb-10df-4211-b454-5ba7d9997b88"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement mode="Transparent" x="9" y="11" width="20" height="10" uuid="1bdeeb77-4e29-41fe-b2ba-a6c63a151329"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Cpf*/$V{BUNDLE}.getStringApplication("rotulo_cpf")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="30" y="11" width="74" height="10" uuid="db7b5256-2b8a-4898-a677-c6680af2e07b"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cpf}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="289" y="11" width="46" height="10" uuid="1bdeeb77-4e29-41fe-b2ba-a6c63a151329"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Identidade*/Bundle.getStringApplication("rotulo_identidade")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="335" y="11" width="69" height="10" uuid="db7b5256-2b8a-4898-a677-c6680af2e07b"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{identidade}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="412" y="11" width="56" height="10" uuid="1bdeeb77-4e29-41fe-b2ba-a6c63a151329"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Org. Emissor*/$V{BUNDLE}.getStringApplication("rotulo_orgao_emissor_abv")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="468" y="11" width="45" height="10" uuid="db7b5256-2b8a-4898-a677-c6680af2e07b"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{orgaoEmissor}.getSigla()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="116" y="11" width="21" height="10" uuid="1bdeeb77-4e29-41fe-b2ba-a6c63a151329"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*cns*/$V{BUNDLE}.getStringApplication("rotulo_cns")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="138" y="11" width="91" height="10" uuid="db7b5256-2b8a-4898-a677-c6680af2e07b"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cns}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement mode="Opaque" x="8" y="23" width="378" height="10" uuid="a434725a-a2ce-4685-98af-02619af9bf44"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true" isItalic="true" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tipoCertidao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="21" y="0" width="56" height="12" uuid="2c669b84-2450-4637-aa89-414e6a017989"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*documentos*/$V{BUNDLE}.getStringApplication("rotulo_documentos")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="116" y="33" width="26" height="10" uuid="d83851f8-6a85-4dde-932b-6b1f5a60aa30"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Folha*/$V{BUNDLE}.getStringApplication("rotulo_folha")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="289" y="33" width="38" height="10" uuid="8450180c-dd5b-4564-a055-9f981d8e7db3"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*dtEmissao*/$V{BUNDLE}.getStringApplication("rotulo_emissao")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="33" y="33" width="45" height="10" uuid="39190483-a581-422b-ad6e-3f6e16a31520"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{livro}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="412" y="33" width="36" height="10" uuid="051091f9-ebbd-4135-96f9-8fd93cb1c433"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Cartorio*/$V{BUNDLE}.getStringApplication("rotulo_cartorio")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="196" y="33" width="30" height="10" uuid="34842b95-f958-400e-a16c-3bfa02a90166"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*termo*/$V{BUNDLE}.getStringApplication("rotulo_termo")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="9" y="33" width="24" height="10" uuid="16e63bf3-81a5-4bb9-9b52-77960d12f291"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Livro*/$V{BUNDLE}.getStringApplication("rotulo_livro")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="142" y="33" width="28" height="10" uuid="fccc9319-8dcc-44a2-a66d-4243b4648d36"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{folha}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="327" y="33" width="47" height="10" uuid="5bae91a1-2f98-4323-861f-cf696dea6ff3"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataEmissaoCertidao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="448" y="33" width="98" height="10" uuid="50d4c775-1c97-4277-af98-901ad5ca89ba"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cartorio}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="226" y="33" width="42" height="10" uuid="f3ed41bd-9d94-4f00-8c6a-b07d3088a674"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{termo}]]></textFieldExpression>
			</textField>
		</band>
		<band height="28">
			<printWhenExpression><![CDATA[$F{tipoCertidao} == null]]></printWhenExpression>
			<rectangle radius="10">
				<reportElement mode="Transparent" x="0" y="6" width="555" height="18" uuid="56d23e8d-6847-4a11-b40a-d3353d654f7b"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement x="468" y="11" width="45" height="10" uuid="fc334171-a3b4-4007-a6c2-5e4db27295eb"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{orgaoEmissor}.getSigla()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="116" y="11" width="21" height="10" uuid="f5233cec-dbb5-4014-828a-eb5f65676f78"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*cns*/$V{BUNDLE}.getStringApplication("rotulo_cns")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="9" y="11" width="20" height="10" uuid="b4a04071-4bd5-46b4-bb0d-bfe1391a5f32"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Cpf*/$V{BUNDLE}.getStringApplication("rotulo_cpf")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="412" y="11" width="56" height="10" uuid="a17f9ffc-8d3a-4ae0-a758-025d9fb8f84f"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Org. Emissor*/$V{BUNDLE}.getStringApplication("rotulo_orgao_emissor_abv")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="30" y="11" width="74" height="10" uuid="b5f656b0-78e6-475f-a837-486b66804779"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cpf}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="335" y="11" width="69" height="10" uuid="4b89acda-139f-4ed7-b3c8-cd319a9ee0d6"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{identidade}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="21" y="0" width="56" height="12" uuid="693dfa09-163f-4a23-9e2f-a179e147d57d"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*documentos*/$V{BUNDLE}.getStringApplication("rotulo_documentos")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="138" y="11" width="91" height="10" uuid="f4d5ddab-a02f-4e43-89f6-0825bcf62c71"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cns}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="289" y="11" width="46" height="10" uuid="1af991a1-5079-4e25-9cb7-9ed6def7d74d"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Identidade*/Bundle.getStringApplication("rotulo_identidade")+":"]]></textFieldExpression>
			</textField>
		</band>
		<band height="62">
			<rectangle radius="10">
				<reportElement mode="Transparent" x="0" y="6" width="555" height="52" uuid="99390016-3c3e-4d89-b241-7722c305db5c"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="8" y="23" width="28" height="10" uuid="c2375504-cfd1-4089-9d90-da3900e78e6a"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Bairro*/$V{BUNDLE}.getStringApplication("rotulo_bairro")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="8" y="45" width="60" height="10" uuid="1812e76b-b54d-4ea5-be16-03fab692baec"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*complemento*/$V{BUNDLE}.getStringApplication("rotulo_complemento")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="68" y="45" width="470" height="10" uuid="0592b8d0-1cab-437f-a6cd-0eb7c3d14d55"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{enderecoUsuarioCadsus}.getComplementoLogradouro()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="475" y="23" width="63" height="10" uuid="cf07a97c-dc5d-4a49-944b-fe8331e9c0a9"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{enderecoUsuarioCadsus}.getCep()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="455" y="23" width="20" height="10" uuid="9ef5f6ed-54af-494d-a78a-a936e78661e0"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*CEP*/$V{BUNDLE}.getStringApplication("rotulo_cep")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="21" y="0" width="46" height="12" uuid="97382ffc-7342-49a8-b45b-ffef78d0217a"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Endereço*/$V{BUNDLE}.getStringApplication("rotulo_endereco")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="8" y="34" width="31" height="10" uuid="4a464489-a323-4883-8aa5-8acd0adc4713"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Cidade*/$V{BUNDLE}.getStringApplication("rotulo_cidade")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="39" y="34" width="416" height="10" uuid="1b8c8ef6-f8e7-4e47-9f19-fa7c6d1ff255"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{enderecoUsuarioCadsus}.getCidade()!=null?
$F{enderecoUsuarioCadsus}.getCidade().getDescricaoFormatado()
:""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="470" y="34" width="68" height="10" uuid="45baf1b1-96f0-437a-82e9-c5d6d094c033"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{enderecoUsuarioCadsus}.getCidade()!=null?
$F{enderecoUsuarioCadsus}.getCidade().getEstado().getSigla()
:""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="455" y="34" width="15" height="10" uuid="9af707eb-3f08-4d97-9b27-595bb10e5049"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*UF*/$V{BUNDLE}.getStringApplication("rotulo_uf")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="58" y="12" width="480" height="10" uuid="f8b790cf-7436-4e2c-aa28-0f8d3e5c604f"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{enderecoUsuarioCadsus}.getRuaFormatada()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="8" y="12" width="50" height="10" uuid="e744ef60-89ab-464a-86f6-349c7ecb24c3"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Logradouro*/$V{BUNDLE}.getStringApplication("rotulo_logradouro")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="36" y="23" width="419" height="10" uuid="f63d166b-2c56-4a36-b52d-02603814fe8c"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{enderecoUsuarioCadsus}.getBairro()]]></textFieldExpression>
			</textField>
		</band>
		<band height="55">
			<printWhenExpression><![CDATA[!$V{viewProcedimentos}]]></printWhenExpression>
			<rectangle radius="10">
				<reportElement mode="Transparent" x="0" y="6" width="555" height="45" uuid="99390016-3c3e-4d89-b241-7722c305db5c"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement mode="Opaque" x="21" y="0" width="56" height="12" uuid="97382ffc-7342-49a8-b45b-ffef78d0217a"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Observacao*/$V{BUNDLE}.getStringApplication("rotulo_observacao")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="6" y="10" width="543" height="38" uuid="f8b790cf-7436-4e2c-aa28-0f8d3e5c604f"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{atendimento}.getObservacaoMarcacao()]]></textFieldExpression>
			</textField>
		</band>
		<band height="77">
			<printWhenExpression><![CDATA[$V{viewProcedimentos}]]></printWhenExpression>
			<rectangle radius="10">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" mode="Transparent" x="343" y="6" width="212" height="65" uuid="607672df-56e5-40ef-aaea-abe66f5f7be7"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</rectangle>
			<rectangle radius="10">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="6" width="330" height="65" uuid="fb9d3aaa-9839-404b-8762-6a85dfa2beeb"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement mode="Opaque" x="21" y="0" width="56" height="12" uuid="91b0280c-ec09-4d4d-9353-768437e8148f"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Observacao*/$V{BUNDLE}.getStringApplication("rotulo_observacao")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="6" y="11" width="318" height="57" uuid="c6ef3717-c4ec-406c-ae33-d407a32d8634"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{atendimento}.getObservacaoMarcacao()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="361" y="0" width="68" height="12" uuid="6d0f3ff4-7d59-44f7-81be-1654e5456e5a"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Procedimentos*/
$V{BUNDLE}.getStringApplication("rotulo_procedimentos")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="352" y="11" width="197" height="12" uuid="fcbe403c-c46a-4770-a618-caf89de93694"/>
				<textElement markup="none">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[(   ) Consulta 03.01.06.009-6]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="352" y="24" width="197" height="12" uuid="855539a0-9912-4871-99a2-c415494b7705"/>
				<textElement markup="none">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[(   ) Medicação 03.01.10.001-2]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="352" y="36" width="197" height="12" uuid="90d6e52c-e6d3-4593-b557-448bb001eb2a"/>
				<textElement markup="none">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[(   ) Observação 03.01.06.002-9]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="352" y="48" width="197" height="20" uuid="f9c2e67f-1069-4b94-90c9-e95dca5e7aed"/>
				<textElement markup="none">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[(   ) Outras: _________________________________
       _______________________________________]]></text>
			</staticText>
		</band>
		<band height="138">
			<rectangle radius="10">
				<reportElement mode="Transparent" x="0" y="5" width="555" height="127" uuid="4f7de83b-b5ad-42c1-9ca1-dd1dfa0196a7"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</rectangle>
			<rectangle radius="10">
				<reportElement x="6" y="107" width="540" height="20" uuid="78eeaee8-3e8f-436b-bf51-4868dcd65493"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement mode="Opaque" x="21" y="-1" width="68" height="12" uuid="2f322cfc-178a-4b98-9169-d5b2bb7c446d"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Acompanhante*/$V{BUNDLE}.getStringApplication("rotulo_acompanhante")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="193" y="26" width="44" height="10" uuid="f0c77052-2288-4eac-a9ae-33c3eeac1b0c"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{acompanhanteDTO}.getUsuarioCadsusAcompanhante().getSexoFormatado()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="168" y="26" width="24" height="10" uuid="72bd7aa4-9ae3-430b-b776-4a732a5d6d92"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*sexo*/$V{BUNDLE}.getStringApplication("rotulo_sexo")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="8" y="26" width="50" height="10" uuid="a4b342e5-f1b7-4a55-9587-7a18a508f4a1"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*dt nasc*/$V{BUNDLE}.getStringApplication("rotulo_nascimento")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="8" y="12" width="29" height="12" uuid="a2dc658a-f186-47a7-a1a5-e7479a4c41e7"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*nome*/$V{BUNDLE}.getStringApplication("rotulo_nome")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="58" y="26" width="46" height="10" uuid="c3946c51-b48f-4231-bd1b-97f1b6967346"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{acompanhanteDTO}.getUsuarioCadsusAcompanhante().getDataNascimento()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="38" y="12" width="497" height="12" uuid="ed4b3ca6-68a3-4883-b8f1-d33abb02ba19"/>
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{acompanhanteDTO}.getUsuarioCadsusAcompanhante().getNome()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="283" y="26" width="82" height="10" uuid="956875e5-0b89-456a-9994-8ea1c343e3cc"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*grauParantesco*/$V{BUNDLE}.getStringApplication("rotulo_grau_parentesco")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="365" y="26" width="170" height="10" uuid="975c3754-afc7-48f2-8e4d-fb4210b4beb7"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{acompanhanteDTO}.getUsuarioCadsusAcompanhante().getGrauParentesco()]]></textFieldExpression>
			</textField>
			<rectangle radius="10">
				<reportElement x="6" y="44" width="540" height="50" uuid="52efde14-4798-4b74-996e-25bba878cf41"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement mode="Opaque" x="26" y="38" width="56" height="12" uuid="abaad9da-4209-4cae-818c-843e17f652a7"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Documentos*/$V{BUNDLE}.getStringApplication("rotulo_documentos")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="17" y="50" width="20" height="10" uuid="ff40daa5-1d2f-4ab9-8a2d-14f470604945"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Cpf*/$V{BUNDLE}.getStringApplication("rotulo_cpf")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="38" y="50" width="170" height="10" uuid="b1e611bf-eaa3-489b-bda1-e8e3a254b666"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[Util.getCpfFormatado($F{acompanhanteDTO}.getUsuarioCadsusAcompanhante().getCpf())]]></textFieldExpression>
			</textField>
			<rectangle radius="10">
				<reportElement x="12" y="68" width="527" height="22" uuid="82d65e73-bb1c-4c26-8e16-65d85c2fda62"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement mode="Opaque" x="33" y="62" width="48" height="12" uuid="1b46d1d6-fbaf-4b53-a0e7-dccb11bb2b9f"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Identidade*/$V{BUNDLE}.getStringApplication("rotulo_identidade")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="56" y="75" width="63" height="10" uuid="04db7f22-651e-4d84-8cca-11be45612a8d"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{acompanhanteDTO}.getUsuarioCadsusAcompanhanteDocumento().getNumeroDocumento()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="21" y="75" width="36" height="10" uuid="9beb563b-5e53-4054-8a8f-2294d3c4f184"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Numero*/$V{BUNDLE}.getStringApplication("rotulo_numero")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="134" y="75" width="60" height="10" uuid="22c86911-f859-4ea5-b2c2-4e5e51a8bad3"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*complemento*/$V{BUNDLE}.getStringApplication("rotulo_complemento")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="193" y="75" width="63" height="10" uuid="bfbad35b-47ce-4beb-a0a1-29789fb44444"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{acompanhanteDTO}.getUsuarioCadsusAcompanhanteDocumento().getNumeroDocumentoComplementar()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="278" y="75" width="15" height="10" uuid="6d0367c8-eae5-43d9-82d4-100dde92cd8b"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*UF*/$V{BUNDLE}.getStringApplication("rotulo_uf")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="293" y="75" width="13" height="10" uuid="681b09e7-432e-4454-a8d6-39e52390e523"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{acompanhanteDTO}.getUsuarioCadsusAcompanhanteDocumento().getSiglaUf()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="500" y="75" width="36" height="10" uuid="699d391d-a64c-4eb1-a15c-d20c11b0a660"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{acompanhanteDTO}.getUsuarioCadsusAcompanhanteDocumento().getOrgaoEmissor().getSigla()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="333" y="75" width="38" height="10" uuid="b76ba11e-0e2e-4d66-a3fc-7cfaeef71d41"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*dtEmissao*/$V{BUNDLE}.getStringApplication("rotulo_emissao")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="444" y="75" width="56" height="10" uuid="220868c9-bbff-4ec1-aef2-ecb2833684c3"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Org. Emissor*/$V{BUNDLE}.getStringApplication("rotulo_orgao_emissor_abv")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="371" y="75" width="47" height="10" uuid="7baf9154-**************-32b0dff01452"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{acompanhanteDTO}.getUsuarioCadsusAcompanhanteDocumento().getDataEmissao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="56" y="113" width="170" height="10" uuid="8bfe8ce6-657b-4ba4-8d13-38dd847eba84"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{acompanhanteDTO}.getUsuarioCadsusAcompanhante().getTelefoneFormatado()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="17" y="113" width="39" height="10" uuid="95d3f66c-dd49-495b-aa89-0cef502c244f"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*telefone*/$V{BUNDLE}.getStringApplication("rotulo_telefone")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="26" y="101" width="37" height="12" uuid="90240a6a-f5bc-424b-b27a-7329389f44e6"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*contato*/$V{BUNDLE}.getStringApplication("rotulo_contato")]]></textFieldExpression>
			</textField>
		</band>
		<band height="197">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="31" y="18" width="494" height="124" uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top" markup="html">
					<font fontName="Arial" size="9" isBold="false" isUnderline="false"/>
					<paragraph lineSpacing="1_1_2" firstLineIndent="30"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_termo_responsabilidade_ficha_paciente_html")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="1" y="1" width="555" height="17" uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top" markup="html">
					<font fontName="Arial" size="12" isBold="true" isUnderline="false"/>
					<paragraph lineSpacing="Double" firstLineIndent="30"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_termo_responsabilidade")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="298" y="184" width="258" height="13" uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_responsavel")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-9" positionType="Float" x="1" y="184" width="237" height="13" uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_paciente")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" x="1" y="183" width="237" height="1" uuid="9fdd2385-e785-45c6-8c28-3f9b406c0810"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</line>
			<line>
				<reportElement positionType="Float" x="298" y="183" width="258" height="1" uuid="9fdd2385-e785-45c6-8c28-3f9b406c0810"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</line>
		</band>
	</detail>
</jasperReport>
