package br.com.ksisolucoes.report.prontuario.basico;

import br.com.celk.io.FtpImageUtil;
import br.com.celk.io.LogoHelper;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioAutorizacaoBaixaResponsabilidadeDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioBaixaResponsabilidadeDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioBaixaResponsabilidadeRevalidacaoAlvaraDTO;
import br.com.ksisolucoes.report.vigilancia.query.QueryRelatorioBaixaResponsabilidade;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoBaixaResponsabilidade;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

import java.io.File;
import java.util.ArrayList;
import java.util.Collection;

/**
 * <AUTHOR>
 */
public class RelatorioImprimirBaixaResponsabilidadeRevalidacaoAlvara extends AbstractReport<RelatorioAutorizacaoBaixaResponsabilidadeDTOParam> {

    RelatorioAutorizacaoBaixaResponsabilidadeDTOParam param;

    public RelatorioImprimirBaixaResponsabilidadeRevalidacaoAlvara(RelatorioAutorizacaoBaixaResponsabilidadeDTOParam param) {
        super(param);
        this.param = param;
    }

    @Override
    public Collection getCollection() throws DAOException, ValidacaoException {
        ArrayList<RelatorioBaixaResponsabilidadeRevalidacaoAlvaraDTO> dtos = new ArrayList();
        RelatorioBaixaResponsabilidadeRevalidacaoAlvaraDTO dto = new RelatorioBaixaResponsabilidadeRevalidacaoAlvaraDTO();
//        if (VigilanciaHelper.existAlvaraSistema(param.getEstabelecimento())) {
//            QueryRelatorioRequerimentoRevalidacaoAlvara queryRelatorioRequerimentoRevalidacaoAlvara = new QueryRelatorioRequerimentoRevalidacaoAlvara();
//            queryRelatorioRequerimentoRevalidacaoAlvara.setDTOParam(param.getEstabelecimento());
//            queryRelatorioRequerimentoRevalidacaoAlvara.start();
//            dto.setRequerimentoRevalidacaoAlvaraDTOList(new ArrayList<RequerimentoRevalidacaoAlvaraDTO>(queryRelatorioRequerimentoRevalidacaoAlvara.getResult()));
//        }

        QueryRelatorioBaixaResponsabilidade queryRelatorioBaixaResponsabilidade = new QueryRelatorioBaixaResponsabilidade();
        queryRelatorioBaixaResponsabilidade.setDTOParam(param);
        queryRelatorioBaixaResponsabilidade.start();
        dto.setRelatorioBaixaResponsabilidadeDTOList(new ArrayList<RelatorioBaixaResponsabilidadeDTO>(queryRelatorioBaixaResponsabilidade.getResult()));
        dtos.add(dto);

        try {
            String gerarDocumentoComAssinaturaFiscal = BOFactory.getBO(CommomFacade.class).modulo(Modulos.VIGILANCIA_SANITARIA).getParametro("certidaoBaixaResponsabilidadeTecnica");
            if (RepositoryComponentDefault.SIM.equals(gerarDocumentoComAssinaturaFiscal)) {
                addParametro("gerarDocumentoComAssinaturaFiscal", true);
            } else {
                addParametro("gerarDocumentoComAssinaturaFiscal", false);
            }
        } catch (DAOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }

        return dtos;
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_baixa_responsabilidade_revalidacao_alvara.jrxml";
    }

    @Override
    public String getTitulo() {
        return "";
    }

    @Override
    protected void customReport() throws DAOException, ValidacaoException {
        ConfiguracaoVigilancia configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        addParametro("textoAutorizacaoExumacao1", configuracaoVigilancia.getTextoReqExumacao1());
        addParametro("textoAutorizacaoExumacao2", configuracaoVigilancia.getTextoReqExumacao2());
        addParametro("cidade", SessaoAplicacaoImp.getInstance().getEmpresa().getCidade().getDescricao());
        addParametro("uf", SessaoAplicacaoImp.getInstance().getEmpresa().getCidade().getEstado().getSigla());
        addParametro("dataAtualFormatado", Data.formatar(DataUtil.getDataAtual()));
        addParametro("protocolo", VigilanciaHelper.formatarProtocolo(param.getRequerimentoVigilancia().getProtocolo()));
        addParametro("urlQRCode", param.getUrlQRCode());
        
        ConfiguracaoVigilancia cv = VigilanciaHelper.getConfiguracaoVigilancia();

        if(cv != null){
            String descLinha1 = Coalesce.asString(cv.getLinha1Cabecalho());
            String descLinha2 = Coalesce.asString(cv.getLinha2Cabecalho());
            String descLinha3 = Coalesce.asString(cv.getLinha3Cabecalho());
            String descLinha4 = Coalesce.asString(cv.getLinha4Cabecalho());

            addParametro("DESC_LINHA_1", descLinha1);
            addParametro("DESC_LINHA_2", descLinha2);
            addParametro("DESC_LINHA_3", descLinha3);   
            addParametro("DESC_LINHA_4", descLinha4);

            String caminhoLogo;
            if(cv.getLogoCabecalhoRelatorio() != null){
                caminhoLogo = new FtpImageUtil().downloadImage(cv.getLogoCabecalhoRelatorio().getCaminho());
            } else {
                caminhoLogo = getPathImageLogo();
            }
            addParametro("CAMINHO_IMAGEM_LOGO_VIGILANCIA_SANITARIA", caminhoLogo);
        }
        RequerimentoBaixaResponsabilidade rbr = LoadManager.getInstance(RequerimentoBaixaResponsabilidade.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RequerimentoBaixaResponsabilidade.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_CODIGO), param.getCodigoRequerimentoVigilancia()))
                .addProperty(RequerimentoBaixaResponsabilidade.PROP_NUMERACAO)
                .start().getVO();
        if(rbr != null){
            String numeracao = VigilanciaHelper.formatarProtocolo(rbr.getNumeracao());
            addParametro("TITULO_REPORT_1", Bundle.getStringApplication("rotulo_certidao_baixa_nr_X", numeracao));
        } else {
            addParametro("TITULO_REPORT_1", Bundle.getStringApplication("rotulo_certidao_baixa"));
        }
        addParametro("TITULO_REPORT_2", Bundle.getStringApplication("rotulo_alvara_sanitario_municipal"));
    }
    
    private String getPathImageLogo() {
        String path = null;

        File logoDocumento = LogoHelper.getLogoDocumento();
        if (logoDocumento != null) {
            path = logoDocumento.getPath();
        }

        return path;
    }
}
