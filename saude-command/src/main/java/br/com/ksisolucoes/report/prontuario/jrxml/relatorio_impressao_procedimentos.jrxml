<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_impressao_atendimento" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<import value="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="atendimentoItem" class="br.com.ksisolucoes.vo.prontuario.basico.AtendimentoItem"/>
	<field name="produto" class="br.com.ksisolucoes.vo.entradas.estoque.Produto"/>
	<field name="quantidade" class="java.lang.Double"/>
	<field name="formaUso" class="java.lang.Long"/>
	<field name="posologia" class="java.lang.String"/>
	<field name="produtoUtilizado" class="java.lang.String"/>
	<field name="descricaoFormaUso" class="java.lang.String"/>
	<field name="produtoUtilizadoFormatado" class="java.lang.String"/>
	<variable name="atendimento" class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento" resetType="None">
		<variableExpression><![CDATA[$F{atendimentoItem}.getAtendimento()]]></variableExpression>
	</variable>
	<variable name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa" resetType="None">
		<variableExpression><![CDATA[$V{atendimento}.getEmpresa()]]></variableExpression>
	</variable>
	<variable name="tipoAtendimento" class="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento" resetType="None">
		<variableExpression><![CDATA[$V{atendimento}.getNaturezaProcuraTipoAtendimento().getTipoAtendimento()]]></variableExpression>
	</variable>
	<variable name="naturezaProcura" class="br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcura" resetType="None">
		<variableExpression><![CDATA[$V{atendimento}.getNaturezaProcuraTipoAtendimento().getNaturezaProcura()]]></variableExpression>
	</variable>
	<variable name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus" resetType="None">
		<variableExpression><![CDATA[$V{atendimento}.getUsuarioCadsus()]]></variableExpression>
	</variable>
	<variable name="profissionalResponsavel" class="br.com.ksisolucoes.vo.cadsus.Profissional" resetType="None">
		<variableExpression><![CDATA[$V{atendimento}.getProfissionalResponsavel()]]></variableExpression>
	</variable>
	<variable name="profissionalAtendimento" class="br.com.ksisolucoes.vo.cadsus.Profissional" resetType="None">
		<variableExpression><![CDATA[$V{atendimento}.getProfissional()]]></variableExpression>
	</variable>
	<variable name="enderecoUsuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus" resetType="None">
		<variableExpression><![CDATA[$V{atendimento}.getEnderecoUsuarioCadsus()]]></variableExpression>
	</variable>
	<variable name="procedimento" class="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento" resetType="None">
		<variableExpression><![CDATA[$F{atendimentoItem}.getProcedimentoCompetencia().getId().getProcedimento()]]></variableExpression>
	</variable>
	<variable name="anotacao" class="java.lang.String" incrementType="Group" incrementGroup="AtendimentoItem" calculation="Sum">
		<variableExpression><![CDATA[$F{atendimentoItem}.getAnotacaoAtendimento()]]></variableExpression>
	</variable>
	<group name="Atendimento">
		<groupExpression><![CDATA[$V{atendimento}.getCodigo()]]></groupExpression>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<group name="AtendimentoItem">
		<groupExpression><![CDATA[$F{atendimentoItem}.getItem()]]></groupExpression>
		<groupHeader>
			<band height="16">
				<textField isBlankWhenNull="true">
					<reportElement x="260" y="4" width="35" height="12"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.Double"><![CDATA[$F{atendimentoItem}.getQuantidade()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="11" y="4" width="245" height="12"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{procedimento}.getDescricaoFormatado()]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="393" y="15" width="138" height="1"/>
				</line>
				<textField isBlankWhenNull="true">
					<reportElement x="299" y="4" width="90" height="12"/>
					<textElement textAlignment="Left">
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{atendimentoItem}.getAnotacaoAtendimento()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<group name="AtendimentoItemAcessorioHeader">
		<groupExpression><![CDATA[$F{atendimentoItem}.getCodigo()]]></groupExpression>
		<groupHeader>
			<band height="15">
				<printWhenExpression><![CDATA[$F{produto}.getCodigo()!=null]]></printWhenExpression>
				<textField>
					<reportElement x="242" y="3" width="23" height="12"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_quantidade_abv")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="227" y="3" width="15" height="12"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_unidade_abv")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="14" y="3" width="209" height="12"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_insumo_medicamento")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="464" y="3" width="71" height="12"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_lotes_utilizados")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="269" y="3" width="43" height="12"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_uso")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="314" y="3" width="99" height="12"/>
					<textElement textAlignment="Left">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_posologia")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="415" y="3" width="46" height="12"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_origem")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="17">
				<printWhenExpression><![CDATA[$F{produto}.getCodigo()!=null]]></printWhenExpression>
				<line>
					<reportElement x="14" y="8" width="520" height="1"/>
				</line>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="212" splitType="Stretch">
			<rectangle radius="10">
				<reportElement mode="Transparent" x="0" y="6" width="555" height="68"/>
			</rectangle>
			<rectangle radius="10">
				<reportElement mode="Transparent" x="0" y="86" width="555" height="87"/>
			</rectangle>
			<textField>
				<reportElement x="289" y="18" width="89" height="12"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_atendimento")+": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="426" y="110" width="111" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Data.getDescricaoIdade($V{usuarioCadsus}.getDataNascimento(), $V{atendimento}.getDataAtendimento())]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="204" y="110" width="85" height="12"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_data_nascimento_abv")+": "]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="32" y="0" width="115" height="13"/>
				<box leftPadding="2"/>
				<textElement>
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_dados_atendimento")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="36" y="110" width="166" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{usuarioCadsus}.getSexoFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="53" y="18" width="236" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{empresa}.getCnes() + " - " +$V{empresa}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="96" y="35" width="177" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{naturezaProcura}.getDescricao()+" / "+$V{tipoAtendimento}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="378" y="18" width="164" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{atendimento}.getCodigo()+ " - " +Data.formatarDataHora($V{atendimento}.getDataAtendimento())]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="53" y="96" width="484" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{usuarioCadsus}.getNome()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="13" y="18" width="40" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_empresa")+": "]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="110" width="25" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_sexo")+": "]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="295" y="35" width="83" height="12"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_profissional")+": "]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="32" y="80" width="100" height="13"/>
				<box leftPadding="2"/>
				<textElement>
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_dados_paciente")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="13" y="35" width="83" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_tipo_atendimento")+": "]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="289" y="110" width="78" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$V{usuarioCadsus}.getDataNascimento()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="96" width="42" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_usuario_cadsus")+": "]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="367" y="110" width="59" height="12"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_idade")+": "]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="13" y="52" width="83" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_profissional_responsavel_abv")+": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="378" y="35" width="164" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{profissionalAtendimento}.getNome()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="96" y="52" width="186" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{profissionalResponsavel}.getNome()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="289" y="52" width="89" height="12"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_crm")+": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="378" y="52" width="164" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{profissionalResponsavel}.getNumeroRegistro()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="124" width="22" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_rua")+": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="33" y="124" width="334" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{enderecoUsuarioCadsus}.getLogradouro()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="367" y="124" width="59" height="12"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_numero")+": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="426" y="124" width="111" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{enderecoUsuarioCadsus}.getNumeroLogradouro()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="46" y="137" width="177" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{enderecoUsuarioCadsus}.getBairro()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="137" width="35" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_bairro")+": "]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="289" y="137" width="173" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{enderecoUsuarioCadsus}.getCidade().getDescricaoFormatado()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="223" y="137" width="66" height="12"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_cidade")+": "]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="475" y="137" width="32" height="12"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_uf")+": "]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="507" y="137" width="27" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{enderecoUsuarioCadsus}.getCidade().getEstado().getSigla()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="56" y="151" width="167" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{usuarioCadsus}.getTelefoneFormatado()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="151" width="45" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_telefone")+": "]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="237" y="151" width="52" height="12"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_cpf")+": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="289" y="151" width="78" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{usuarioCadsus}.getCpfFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="426" y="151" width="78" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{usuarioCadsus}.getRg()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="374" y="151" width="52" height="12"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_rg")+": "]]></textFieldExpression>
			</textField>
			<rectangle radius="10">
				<reportElement mode="Transparent" x="0" y="185" width="555" height="27"/>
			</rectangle>
			<textField>
				<reportElement mode="Opaque" x="32" y="179" width="88" height="13"/>
				<box leftPadding="2"/>
				<textElement>
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_procedimentos")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="13" y="200" width="245" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_procedimento")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="260" y="200" width="35" height="12"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_quantidade_abv")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="393" y="200" width="140" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_profissional_aplicacao_abv")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="299" y="200" width="90" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_anotacao")]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="14" splitType="Stretch">
			<printWhenExpression><![CDATA[$F{produto}.getCodigo()!=null]]></printWhenExpression>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="14" y="2" width="209" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{produto}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="227" y="2" width="15" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{produto}.getUnidade().getUnidade()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="243" y="2" width="22" height="12"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{quantidade}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="269" y="2" width="43" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricaoFormaUso}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="314" y="2" width="99" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{posologia}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="415" y="2" width="46" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{produtoUtilizadoFormatado}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="464" y="13" width="71" height="1"/>
			</line>
		</band>
	</detail>
	<columnFooter>
		<band height="103" splitType="Stretch">
			<rectangle radius="10">
				<reportElement x="0" y="8" width="555" height="82"/>
			</rectangle>
			<textField>
				<reportElement mode="Opaque" x="32" y="1" width="64" height="13"/>
				<box leftPadding="2"/>
				<textElement>
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_anotacoes")]]></textFieldExpression>
			</textField>
		</band>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
