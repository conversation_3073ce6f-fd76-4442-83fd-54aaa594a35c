<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="sub_relatorio_impressao_prescricao_interna_cuidados" pageWidth="535" pageHeight="782" columnWidth="535" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="177911e4-d904-4868-ab85-22e162f3998f">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.0490403660952117"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<field name="frequencia" class="java.lang.String"/>
	<field name="descricao" class="java.lang.String"/>
	<field name="prescricaoEnfermagem" class="br.com.ksisolucoes.vo.prontuario.prescricaoenfermagem.PrescricaoEnfermagem"/>
	<group name="cabecalho">
		<groupExpression><![CDATA[""]]></groupExpression>
	</group>
	<group name="cuidados">
		<groupExpression><![CDATA[""]]></groupExpression>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<columnHeader>
		<band height="15">
			<textField isBlankWhenNull="false">
				<reportElement key="textField" positionType="Float" mode="Transparent" x="12" y="0" width="260" height="13" uuid="bbb1b902-3b3c-46cf-a6cf-3aec0e4be2e2"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_descricao")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" positionType="Float" mode="Transparent" x="275" y="0" width="159" height="13" uuid="bbb1b902-3b3c-46cf-a6cf-3aec0e4be2e2"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_frequencia_quantidade")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="12" y="13" width="523" height="1" uuid="2856212d-9092-43f0-9cbe-8bb92df51673"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" positionType="Float" mode="Transparent" x="439" y="0" width="96" height="13" uuid="bbb1b902-3b3c-46cf-a6cf-3aec0e4be2e2"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_horarios")]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="12" y="0" width="260" height="12" isRemoveLineWhenBlank="true" uuid="a8c8bb57-e29e-4986-914a-5240afcc0dc7"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" pdfFontName="Courier"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="275" y="0" width="159" height="12" isRemoveLineWhenBlank="true" uuid="a8c8bb57-e29e-4986-914a-5240afcc0dc7"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" pdfFontName="Courier"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{frequencia}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" x="12" y="12" width="523" height="1" uuid="2856212d-9092-43f0-9cbe-8bb92df51673"/>
				<graphicElement>
					<pen lineWidth="0.75" lineStyle="Dashed" lineColor="#BEBEBE"/>
				</graphicElement>
			</line>
		</band>
	</detail>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
