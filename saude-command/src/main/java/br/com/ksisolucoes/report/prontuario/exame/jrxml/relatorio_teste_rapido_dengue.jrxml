<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_teste_rapido_dengue" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="904ef39c-8037-443b-8222-af49929dd456">
	<property name="ireport.zoom" value="2.000000000000021"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="br.com.celk.util.Coalesce"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<import value="org.apache.commons.lang.StringUtils"/>
	<parameter name="FORMA_APRESENTACAO" class="br.com.ksisolucoes.report.geral.interfaces.dto.RelatorioTesteRapidoDengueDTOParam.FormaApresentacao"/>
	<field name="testeRapido" class="br.com.ksisolucoes.vo.prontuario.basico.TesteRapidoRealizado"/>
	<field name="nomeUsuario" class="java.lang.String"/>
	<field name="descricaoProcedimento" class="java.lang.String"/>
	<field name="descricaoResultado" class="java.lang.String"/>
	<field name="dataPrimeirosSintomas" class="java.util.Date"/>
	<field name="dataCadastro" class="java.util.Date"/>
	<field name="descricaoEmpresa" class="java.lang.String"/>
	<field name="descricaoEquipeArea" class="java.lang.String"/>
	<field name="flagNomeSocial" class="java.lang.Long"/>
	<field name="apelido" class="java.lang.String"/>
	<variable name="count" class="java.lang.Integer" resetType="Group" resetGroup="FA" calculation="Count">
		<variableExpression><![CDATA[$F{nomeUsuario}]]></variableExpression>
	</variable>
	<variable name="countTotal" class="java.lang.Integer" resetType="Group" resetGroup="FADefault" calculation="Count">
		<variableExpression><![CDATA[$F{nomeUsuario}]]></variableExpression>
	</variable>
	<variable name="FA" class="br.com.ksisolucoes.report.geral.interfaces.dto.RelatorioTesteRapidoDengueDTOParam.FormaApresentacao"/>
	<group name="FADefault">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band/>
		</groupHeader>
		<groupFooter>
			<band height="17">
				<textField>
					<reportElement x="599" y="5" width="165" height="11" uuid="8345a293-b452-4406-b95b-5ac985206b88"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_geral_pacientes") + ": "]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="668" y="3" width="134" height="1" uuid="74011bb7-90cb-42e4-9848-1230bc7c15e7"/>
				</line>
				<textField isBlankWhenNull="true">
					<reportElement x="764" y="5" width="38" height="11" uuid="c999ae50-144a-4d0f-87f6-c2241ed395b9"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{countTotal}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="FA" isReprintHeaderOnEachPage="true" keepTogether="true">
		<groupExpression><![CDATA[$V{FA}.UNIDADE.equals($P{FORMA_APRESENTACAO}) ?
   $F{descricaoEmpresa}
:
    $V{FA}.EQUIPE_REFERENCIA.equals($P{FORMA_APRESENTACAO}) ?
      $F{descricaoEquipeArea}
    : null]]></groupExpression>
		<groupHeader>
			<band height="26">
				<printWhenExpression><![CDATA[!($V{FA}.GERAL.equals($P{FORMA_APRESENTACAO}))]]></printWhenExpression>
				<rectangle radius="10">
					<reportElement x="0" y="2" width="802" height="20" uuid="9acf27c7-80b0-40ee-98f8-a67674c92b9c"/>
				</rectangle>
				<textField>
					<reportElement x="5" y="6" width="782" height="13" uuid="1845fe0c-3436-421e-a71d-6501e4bff356"/>
					<textElement>
						<font fontName="Arial" size="10" isBold="true" isUnderline="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{FA}.UNIDADE.equals($P{FORMA_APRESENTACAO}) ?
   $F{descricaoEmpresa}
:
    $V{FA}.EQUIPE_REFERENCIA.equals($P{FORMA_APRESENTACAO}) ?
      $F{descricaoEquipeArea}
    : null]]></textFieldExpression>
				</textField>
			</band>
			<band height="16">
				<line>
					<reportElement x="0" y="11" width="802" height="1" uuid="fb44fa67-4ef7-47e8-b53f-ca21c6313e0c"/>
				</line>
				<textField>
					<reportElement x="385" y="2" width="97" height="10" uuid="bbc19e09-5458-4cd3-9e87-a164c4143f53"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_primeiros_sintomas")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="140" y="2" width="152" height="10" uuid="f564cc3b-5f8a-42e3-8aae-0a3f43e953b4"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_tipo_exame")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="482" y="2" width="89" height="11" uuid="82721431-7ab2-44a6-afc5-fa58d6e3e00f"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_exame")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="572" y="1" width="114" height="11" uuid="fb84175f-3492-45f2-9050-921e7f23d069"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_unidade")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="1" width="140" height="11" uuid="985826df-b42d-4bcf-9c57-0f7e63ed7560"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nome_paciente")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="292" y="2" width="93" height="10" uuid="906742ec-d4ea-49ed-9bc3-1f9f35a714d3"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_resultado")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="686" y="1" width="114" height="11" uuid="d086798f-570e-4b15-aa0a-114755c752f4"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_equipe_area")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="17">
				<printWhenExpression><![CDATA[]]></printWhenExpression>
				<textField>
					<reportElement x="687" y="4" width="77" height="11" uuid="2bae9521-d953-4c9f-ab88-b051edb9372b"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_pacientes") + ": "]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="702" y="3" width="100" height="1" uuid="8bfe4a50-cb96-4e1d-a685-d65ba678c114"/>
				</line>
				<textField isBlankWhenNull="true">
					<reportElement x="764" y="4" width="38" height="11" uuid="1209e71d-faad-42ac-87dc-ecffd164fcd9"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{count}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<detail>
		<band height="17" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="0" width="140" height="11" uuid="c2f57e7f-b180-4855-9012-7fa5d4f14afa"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($F{flagNomeSocial})
? $F{apelido} : $F{nomeUsuario}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="482" y="0" width="89" height="11" uuid="a5a70672-a10a-49fc-b897-0a507f55807e"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataCadastro}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="571" y="0" width="114" height="11" uuid="978ca12b-0ff1-464e-b087-be0a3bce25cd"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoEmpresa}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="292" y="0" width="93" height="11" uuid="77aca98a-f273-4df4-9f3a-5d723537a751"/>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoResultado}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="140" y="0" width="152" height="17" uuid="2c27c4e4-d5fb-47b5-8a16-8a7c736691b1"/>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoProcedimento}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="385" y="0" width="97" height="11" uuid="0304db16-c378-43e7-8139-ac3751cbacb7"/>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataPrimeirosSintomas}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="685" y="0" width="114" height="11" uuid="45bf0459-1363-41d5-8e54-47bab037c535"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoEquipeArea}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
