/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.prontuario.procedimento.query;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioBoletimProducaoAmbulatorialDTO;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioBoletimProducaoAmbulatorialDTOParam;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atendimento.BpaProcesso;
import br.com.ksisolucoes.vo.basico.Parametro;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoItem;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExameItem;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoDetalheCadastro;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoFinanciamento;
import org.hibernate.Query;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryBoletimProducaoAmbulatorial extends CommandQuery<QueryBoletimProducaoAmbulatorial> implements ITransferDataReport<RelatorioBoletimProducaoAmbulatorialDTOParam, RelatorioBoletimProducaoAmbulatorialDTO> {

    private RelatorioBoletimProducaoAmbulatorialDTOParam param;
    private List<RelatorioBoletimProducaoAmbulatorialDTO> result;

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        List<RelatorioBoletimProducaoAmbulatorialDTO> procedimentos = new ArrayList<RelatorioBoletimProducaoAmbulatorialDTO>();

        Date competenciaInicioGeracaoBpaConta = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("competenciaInicioGeracaoBpaConta");

        if(competenciaInicioGeracaoBpaConta == null
                || (param.getPeriodo().getDataInicial() != null && Data.adjustRangeHour(param.getPeriodo().getDataInicial()).getDataInicial().compareTo(Data.adjustRangeHour(competenciaInicioGeracaoBpaConta).getDataInicial()) < 0)
                || (param.getPeriodo().getDataFinal() != null && Data.adjustRangeHour(param.getPeriodo().getDataFinal()).getDataInicial().compareTo(Data.adjustRangeHour(competenciaInicioGeracaoBpaConta).getDataInicial()) < 0)){
            procedimentos.addAll(getAtendimentos());
            procedimentos.addAll(getAtendimentosItem());
            procedimentos.addAll(getExameItem());
            procedimentos.addAll(getItemContaPaciente());
        } else {
            procedimentos.addAll(getItemContaPacienteNew());
        }

        result = groupList(procedimentos);
    }

    @Override
    public void setDTOParam(RelatorioBoletimProducaoAmbulatorialDTOParam param) {
        this.param = param;
    }

    @Override
    public Collection getResult() {
        return result;
    }

    private List<RelatorioBoletimProducaoAmbulatorialDTO> getItemContaPacienteNew() throws DAOException {
        Convenio convenio = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("convenioSUS");
        Date dataCompetencia = null;
        try {
            dataCompetencia = (Date) CargaBasicoPadrao.getInstance().getParametroPadrao().getPropertyValue(Parametro.PROP_DATA_COMPETENCIA_PROCEDIMENTO);
        } catch (ValidacaoException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
        HQLHelper hqlItemContaPaciente = new HQLHelper();
        hqlItemContaPaciente.setTypeSelect(RelatorioBoletimProducaoAmbulatorialDTO.class.getName());
        hqlItemContaPaciente.addToFrom(ItemContaPaciente.class.getName(), "icp "
                + " left join icp.profissional prof"
                + " left join icp.cbo cbo"
                + " left join icp.procedimento proce"
                + " left join icp.contaPaciente cp"
                + " left join cp.usuarioCadsus usu"
                + " left join cp.empresa emp"
                + " left join cp.convenio con "
        );

        hqlItemContaPaciente.addToFrom("ProcedimentoCompetencia pc"
                + " left join pc.financiamento pf"
                + " left join pc.id.procedimento proc");

        hqlItemContaPaciente.addToWhereWhithAnd("proc = proce");
        hqlItemContaPaciente.addToWhereWhithAnd("pc.id.dataCompetencia = ", dataCompetencia);

        hqlItemContaPaciente.addToSelect("cast(coalesce(lpad_cast_varchar(icp.idade, 3, '0'), to_char(age(icp.dataLancamento,usu.dataNascimento), 'yyy')) as long)", "idade");

        hqlItemContaPaciente.addToGroup("1");

        hqlItemContaPaciente.addToSelectAndGroup("emp.codigo", "empresa.codigo");
        hqlItemContaPaciente.addToSelectAndGroup("emp.referencia", "empresa.referencia");
        hqlItemContaPaciente.addToSelectAndGroupAndOrder("emp.descricao", "empresa.descricao");
        hqlItemContaPaciente.addToSelectAndGroup("emp.cnes", "empresa.cnes");
        hqlItemContaPaciente.addToSelectAndGroup("cbo.cbo", "tabelaCbo.cbo");
        hqlItemContaPaciente.addToSelectAndGroup("cbo.descricao", "tabelaCbo.descricao");
        hqlItemContaPaciente.addToSelectAndGroup("proc.codigo", "procedimento.codigo");
        hqlItemContaPaciente.addToSelectAndGroup("proc.descricao", "procedimento.descricao");
        hqlItemContaPaciente.addToSelect("sum(icp.quantidade)", "quantidade");

        if (Coalesce.asString(param.getFormaApresentacao()).equals(Profissional.REF)) {
            hqlItemContaPaciente.addToSelectAndGroup("prof.codigo", "profissional.codigo");
            hqlItemContaPaciente.addToSelectAndGroup("prof.referencia", "profissional.referencia");
            hqlItemContaPaciente.addToSelectAndGroupAndOrder("prof.nome", "profissional.nome");
            hqlItemContaPaciente.addToWhereWhithAnd("prof.codigo is not null");
        }

        hqlItemContaPaciente.addToOrder("proc.descricao");
        hqlItemContaPaciente.addToOrder("proc.codigo");
        hqlItemContaPaciente.addToOrder("cbo.descricao");
        hqlItemContaPaciente.addToOrder("1");

        hqlItemContaPaciente.addToWhereWhithAnd("coalesce(proc.flagFaturavel,'S') <> 'N' ");

        hqlItemContaPaciente.addToWhereWhithAnd("cp.status = ", ContaPaciente.Status.FECHADA.value());

        hqlItemContaPaciente.addToWhereWhithAnd("icp.status = ", ItemContaPaciente.Status.CONFIRMADO.value());

        hqlItemContaPaciente.addToWhereWhithAnd("icp.tipo = ", ItemContaPaciente.Tipo.PROCEDIMENTO.value());

        hqlItemContaPaciente.addToWhereWhithAnd("proc = ", this.param.getProcedimento());
        if (this.param.getPeriodo() != null) {
            hqlItemContaPaciente.addToWhereWhithAnd("cp.dataGeracao ", this.param.getPeriodo());
        } else {
            hqlItemContaPaciente.addToWhereWhithAnd("cp.dataGeracao is not null");
        }

        hqlItemContaPaciente.addToWhereWhithAnd("prof ", this.param.getProfissional());

        hqlItemContaPaciente.addToWhereWhithAnd("emp ", this.param.getEmpresa());

        hqlItemContaPaciente.addToWhereWhithAnd("cbo ", this.param.getCbo());

        if (!BpaProcesso.TipoFinanciamento.AMBOS.equals(this.param.getTipoFinanciamento())) {
            if (BpaProcesso.TipoFinanciamento.PAB.equals(param.getTipoFinanciamento())) {
                hqlItemContaPaciente.addToWhereWhithAnd("pf.codigo = ", ProcedimentoFinanciamento.PAB);
            } else if (BpaProcesso.TipoFinanciamento.MAC.equals(param.getTipoFinanciamento())) {
                hqlItemContaPaciente.addToWhereWhithAnd("pf.codigo <> ", ProcedimentoFinanciamento.PAB);
            }
        }

        if (convenio != null) {
            hqlItemContaPaciente.addToWhereWhithAnd("con =", convenio);
        }

        {
            HQLHelper hqlSub = hqlItemContaPaciente.getNewInstanceSubQuery();
            hqlSub.addToSelect("pm.id.procedimentoCompetencia.id.procedimento.codigo");
            hqlSub.addToFrom("ProcedimentoModalidade pm");
            hqlSub.addToWhereWhithAnd("pm.id.procedimentoModalidadeCadastro ", this.param.getModalidade());

            hqlItemContaPaciente.addToWhereWhithAnd("proc.codigo in (" + hqlSub.getQuery() + ")");
        }

        {
            HQLHelper hqlSub = hqlItemContaPaciente.getNewInstanceSubQuery();
            hqlSub.addToSelect("pm.id.procedimentoCompetencia.id.procedimento.codigo");
            hqlSub.addToFrom("ProcedimentoRegistro pm");
            hqlSub.addToWhereWhithAnd("pm.id.procedimentoRegistroCadastro ", this.param.getRegistro());

            hqlItemContaPaciente.addToWhereWhithAnd("proc.codigo in (" + hqlSub.getQuery() + ")");
        }

        Query query = getSession().createQuery(hqlItemContaPaciente.getQuery());

        hqlItemContaPaciente.applyRestrictions(query);

        return hqlItemContaPaciente.getBeanList((List) query.list(), false);
    }

    private List<RelatorioBoletimProducaoAmbulatorialDTO> getAtendimentos() throws DAOException {
        Convenio convenio = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("convenioSUS");
        HQLHelper hqlAtendimentos = new HQLHelper();
        hqlAtendimentos.setTypeSelect(RelatorioBoletimProducaoAmbulatorialDTO.class.getName());
        hqlAtendimentos.addToFrom(Atendimento.class.getName(), "pe"
                + " left join pe.tabelaCbo cbo"
                + " left join pe.convenio con"
                + " left join pe.procedimentoCompetencia pc"
                + " left join pc.id.procedimento proc"
                + " left join pe.profissional prof"
                + " left join pe.empresa emp"
                + " left join pe.usuarioCadsus usu");

        hqlAtendimentos.addToSelect("cast((case when (select count(*) from ProcedimentoDetalhe pd "
                + "where pd.id.procedimentoCompetencia = pc "
                + "and pd.id.detalhe.codigo = " + ProcedimentoDetalheCadastro.EXIGE_IDADE + ") > 0 then to_char(age(pe.dataAtendimento,usu.dataNascimento), 'yyy') else '999'end ) as long)", "idade");
        hqlAtendimentos.addToGroup("1");
        hqlAtendimentos.addToSelectAndGroup("emp.codigo", "empresa.codigo");
        hqlAtendimentos.addToSelectAndGroup("emp.referencia", "empresa.referencia");
        hqlAtendimentos.addToSelectAndGroupAndOrder("emp.descricao", "empresa.descricao");
        hqlAtendimentos.addToSelectAndGroup("emp.cnes", "empresa.cnes");
        hqlAtendimentos.addToSelectAndGroup("cbo.cbo", "tabelaCbo.cbo");
        hqlAtendimentos.addToSelectAndGroup("cbo.descricao", "tabelaCbo.descricao");
        hqlAtendimentos.addToSelectAndGroup("proc.codigo", "procedimento.codigo");
        hqlAtendimentos.addToSelectAndGroup("proc.descricao", "procedimento.descricao");
        hqlAtendimentos.addToSelect("cast(count(emp.codigo) as double)", "quantidade");

        if (Coalesce.asString(param.getFormaApresentacao()).equals(Profissional.REF)) {
            hqlAtendimentos.addToSelectAndGroup("prof.codigo", "profissional.codigo");
            hqlAtendimentos.addToSelectAndGroup("prof.referencia", "profissional.referencia");
            hqlAtendimentos.addToSelectAndGroupAndOrder("prof.nome", "profissional.nome");
            hqlAtendimentos.addToWhereWhithAnd("prof.codigo is not null");
        }

        hqlAtendimentos.addToOrder("proc.descricao");
        hqlAtendimentos.addToOrder("proc.codigo");
        hqlAtendimentos.addToOrder("cbo.descricao");
        hqlAtendimentos.addToOrder("1");

        hqlAtendimentos.addToWhereWhithAnd("coalesce(proc.flagFaturavel,'S') <> 'N' ");

        hqlAtendimentos.addToWhereWhithAnd("pe.status in ", Arrays.asList(Atendimento.STATUS_FINALIZADO));

        hqlAtendimentos.addToWhereWhithAnd("pe.dataAtendimento ", this.param.getPeriodo());

        hqlAtendimentos.addToWhereWhithAnd("prof ", this.param.getProfissional());

        hqlAtendimentos.addToWhereWhithAnd("emp ", this.param.getEmpresa());

        hqlAtendimentos.addToWhereWhithAnd("cbo ", this.param.getCbo());
        
        hqlAtendimentos.addToWhereWhithAnd("proc = ", this.param.getProcedimento());

        if (!BpaProcesso.TipoFinanciamento.AMBOS.equals(this.param.getTipoFinanciamento())) {
            if (BpaProcesso.TipoFinanciamento.PAB.equals(param.getTipoFinanciamento())) {
                hqlAtendimentos.addToWhereWhithAnd("pc.financiamento.codigo = ", ProcedimentoFinanciamento.PAB);
            } else if (BpaProcesso.TipoFinanciamento.MAC.equals(param.getTipoFinanciamento())) {
                hqlAtendimentos.addToWhereWhithAnd("pc.financiamento.codigo <> ", ProcedimentoFinanciamento.PAB);
            }
        }

        if (convenio != null) {
            hqlAtendimentos.addToWhereWhithAnd("con =", convenio);
        }

        {
            HQLHelper hqlSub = hqlAtendimentos.getNewInstanceSubQuery();
            hqlSub.addToSelect("pm.id.procedimentoCompetencia.id.procedimento.codigo");
            hqlSub.addToFrom("ProcedimentoModalidade pm");
            hqlSub.addToWhereWhithAnd("pm.id.procedimentoModalidadeCadastro ", this.param.getModalidade());
            hqlAtendimentos.addToWhereWhithAnd("proc.codigo in (" + hqlSub.getQuery() + ")");
        }

        {
            HQLHelper hqlSub = hqlAtendimentos.getNewInstanceSubQuery();
            hqlSub.addToSelect("pm.id.procedimentoCompetencia.id.procedimento.codigo");
            hqlSub.addToFrom("ProcedimentoRegistro pm");
            hqlSub.addToWhereWhithAnd("pm.id.procedimentoRegistroCadastro ", this.param.getRegistro());
            hqlAtendimentos.addToWhereWhithAnd("proc.codigo in (" + hqlSub.getQuery() + ")");
        }

        Query query = getSession().createQuery(hqlAtendimentos.getQuery());

        hqlAtendimentos.applyRestrictions(query);

        return hqlAtendimentos.getBeanList((List) query.list(), false);
    }

    private List<RelatorioBoletimProducaoAmbulatorialDTO> getAtendimentosItem() throws DAOException {
        Convenio convenio = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("convenioSUS");
        HQLHelper hqlItem = new HQLHelper();
        hqlItem.setTypeSelect(RelatorioBoletimProducaoAmbulatorialDTO.class.getName());
        hqlItem.addToFrom(AtendimentoItem.class.getName(), "pei "
                + " left join pei.atendimento pe"
                + " left join pe.convenio con"
                + " left join pei.profissional prof"
                + " left join pei.procedimentoBpa procedimentoBpa"
                + " left join pei.tabelaCbo cbo"
                + " left join pe.empresa emp"
                + " left join pe.usuarioCadsus usu");

        hqlItem.addToFrom("ProcedimentoCompetencia pc"
                + " left join pc.financiamento pf"
                + " left join pc.id.procedimento proc");

        hqlItem.addToSelect("cast((case when (select count(*) from ProcedimentoDetalhe pd "
                + "where pd.id.procedimentoCompetencia = pc "
                + "and pd.id.detalhe.codigo = " + ProcedimentoDetalheCadastro.EXIGE_IDADE + ") > 0 then to_char(age(pe.dataAtendimento,usu.dataNascimento), 'yyy') else '999' end ) as long)", "idade");
        hqlItem.addToGroup("1");
        hqlItem.addToSelectAndGroup("emp.codigo", "empresa.codigo");
        hqlItem.addToSelectAndGroup("emp.referencia", "empresa.referencia");
        hqlItem.addToSelectAndGroupAndOrder("emp.descricao", "empresa.descricao");
        hqlItem.addToSelectAndGroup("emp.cnes", "empresa.cnes");
        hqlItem.addToSelectAndGroup("cbo.cbo", "tabelaCbo.cbo");
        hqlItem.addToSelectAndGroup("cbo.descricao", "tabelaCbo.descricao");
        hqlItem.addToSelectAndGroup("proc.codigo", "procedimento.codigo");
        hqlItem.addToSelectAndGroup("proc.descricao", "procedimento.descricao");
        hqlItem.addToSelect("cast(count(emp.codigo) as double)", "quantidade");

        if (Coalesce.asString(param.getFormaApresentacao()).equals(Profissional.REF)) {
            hqlItem.addToSelectAndGroup("prof.codigo", "profissional.codigo");
            hqlItem.addToSelectAndGroup("prof.referencia", "profissional.referencia");
            hqlItem.addToSelectAndGroupAndOrder("prof.nome", "profissional.nome");
            hqlItem.addToWhereWhithAnd("prof.codigo is not null");
        }

        hqlItem.addToOrder("proc.descricao");
        hqlItem.addToOrder("proc.codigo");
        hqlItem.addToOrder("cbo.descricao");
        hqlItem.addToOrder("1");

        hqlItem.addToWhereWhithAnd("proc = procedimentoBpa");
        hqlItem.addToWhereWhithAnd("pc.id.dataCompetencia = pe.procedimentoCompetencia.id.dataCompetencia");

        hqlItem.addToWhereWhithAnd("coalesce(proc.flagFaturavel,'S') <> 'N' ");

        hqlItem.addToWhereWhithAnd("pei.status = ", AtendimentoItem.STATUS_APLICADO);

        hqlItem.addToWhereWhithAnd("pe.status in ", Arrays.asList(Atendimento.STATUS_FINALIZADO));

        hqlItem.addToWhereWhithAnd("pe.dataFechamento ", this.param.getPeriodo());

        hqlItem.addToWhereWhithAnd("prof ", this.param.getProfissional());

        hqlItem.addToWhereWhithAnd("emp ", this.param.getEmpresa());

        hqlItem.addToWhereWhithAnd("cbo ", this.param.getCbo());

        hqlItem.addToWhereWhithAnd("proc = ", this.param.getProcedimento());
        
        if (!BpaProcesso.TipoFinanciamento.AMBOS.equals(this.param.getTipoFinanciamento())) {
            if (BpaProcesso.TipoFinanciamento.PAB.equals(param.getTipoFinanciamento())) {
                hqlItem.addToWhereWhithAnd("pf.codigo = ", ProcedimentoFinanciamento.PAB);
            } else if (BpaProcesso.TipoFinanciamento.MAC.equals(param.getTipoFinanciamento())) {
                hqlItem.addToWhereWhithAnd("pf.codigo <> ", ProcedimentoFinanciamento.PAB);
            }
        }

        if (convenio != null) {
            hqlItem.addToWhereWhithAnd("con =", convenio);
        }

        {
            HQLHelper hqlSub = hqlItem.getNewInstanceSubQuery();
            hqlSub.addToSelect("pm.id.procedimentoCompetencia.id.procedimento.codigo");
            hqlSub.addToFrom("ProcedimentoModalidade pm");
            hqlSub.addToWhereWhithAnd("pm.id.procedimentoModalidadeCadastro ", this.param.getModalidade());

            hqlItem.addToWhereWhithAnd("proc.codigo in (" + hqlSub.getQuery() + ")");
        }

        {
            HQLHelper hqlSub = hqlItem.getNewInstanceSubQuery();
            hqlSub.addToSelect("pm.id.procedimentoCompetencia.id.procedimento.codigo");
            hqlSub.addToFrom("ProcedimentoRegistro pm");
            hqlSub.addToWhereWhithAnd("pm.id.procedimentoRegistroCadastro ", this.param.getRegistro());

            hqlItem.addToWhereWhithAnd("proc.codigo in (" + hqlSub.getQuery() + ")");
        }

        Query query = getSession().createQuery(hqlItem.getQuery());

        hqlItem.applyRestrictions(query);

        return hqlItem.getBeanList((List) query.list(), false);
    }

    private List<RelatorioBoletimProducaoAmbulatorialDTO> getExameItem() throws DAOException {
        Convenio convenio = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("convenioSUS");
        HQLHelper hqlItem = new HQLHelper();
        hqlItem.setTypeSelect(RelatorioBoletimProducaoAmbulatorialDTO.class.getName());
        hqlItem.addToFrom(AtendimentoExameItem.class.getName(), "aei "
                + " left join aei.exameProcedimento ep"
                + " join ep.procedimento proce"
                + " left join aei.atendimentoExame ae"
                + " join ae.atendimento pe"
                + " left join pe.convenio con"
                + " left join pe.profissional prof"
                + " left join pe.tabelaCbo cbo"
                + " left join pe.empresa emp"
                + " left join pe.usuarioCadsus usu");

        hqlItem.addToFrom("ProcedimentoCompetencia pc"
                + " left join pc.financiamento pf"
                + " left join pc.id.procedimento proc");

        hqlItem.addToWhereWhithAnd("proc = proce");
        hqlItem.addToWhereWhithAnd("pc.id.dataCompetencia = pe.procedimentoCompetencia.id.dataCompetencia");

        hqlItem.addToSelect("cast((case when (select count(*) from ProcedimentoDetalhe pd "
                + "where pd.id.procedimentoCompetencia = pc "
                + "and pd.id.detalhe.codigo = " + ProcedimentoDetalheCadastro.EXIGE_IDADE + ") > 0 then to_char(age(pe.dataAtendimento,usu.dataNascimento), 'yyy') else '999' end ) as long)", "idade");
        hqlItem.addToGroup("1");
        hqlItem.addToSelectAndGroup("emp.codigo", "empresa.codigo");
        hqlItem.addToSelectAndGroup("emp.referencia", "empresa.referencia");
        hqlItem.addToSelectAndGroupAndOrder("emp.descricao", "empresa.descricao");
        hqlItem.addToSelectAndGroup("emp.cnes", "empresa.cnes");
        hqlItem.addToSelectAndGroup("cbo.cbo", "tabelaCbo.cbo");
        hqlItem.addToSelectAndGroup("cbo.descricao", "tabelaCbo.descricao");
        hqlItem.addToSelectAndGroup("proc.codigo", "procedimento.codigo");
        hqlItem.addToSelectAndGroup("proc.descricao", "procedimento.descricao");
        hqlItem.addToSelect("cast(count(emp.codigo) as double)", "quantidade");

        if (Coalesce.asString(param.getFormaApresentacao()).equals(Profissional.REF)) {
            hqlItem.addToSelectAndGroup("prof.codigo", "profissional.codigo");
            hqlItem.addToSelectAndGroup("prof.referencia", "profissional.referencia");
            hqlItem.addToSelectAndGroupAndOrder("prof.nome", "profissional.nome");
            hqlItem.addToWhereWhithAnd("prof.codigo is not null");
        }

        hqlItem.addToOrder("proc.descricao");
        hqlItem.addToOrder("proc.codigo");
        hqlItem.addToOrder("cbo.descricao");
        hqlItem.addToOrder("1");

        hqlItem.addToWhereWhithAnd("coalesce(proc.flagFaturavel,'S') <> 'N' ");

        hqlItem.addToWhereWhithAnd("aei.status = ", AtendimentoExameItem.StatusAtendimentoExame.NORMAL.value());

        hqlItem.addToWhereWhithAnd("pe.status in ", Arrays.asList(Atendimento.STATUS_FINALIZADO));

        hqlItem.addToWhereWhithAnd("pe.dataFechamento ", this.param.getPeriodo());

        hqlItem.addToWhereWhithAnd("prof ", this.param.getProfissional());

        hqlItem.addToWhereWhithAnd("emp ", this.param.getEmpresa());

        hqlItem.addToWhereWhithAnd("cbo ", this.param.getCbo());

        hqlItem.addToWhereWhithAnd("proc = ", this.param.getProcedimento());
        
        if (!BpaProcesso.TipoFinanciamento.AMBOS.equals(this.param.getTipoFinanciamento())) {
            if (BpaProcesso.TipoFinanciamento.PAB.equals(param.getTipoFinanciamento())) {
                hqlItem.addToWhereWhithAnd("pf.codigo = ", ProcedimentoFinanciamento.PAB);
            } else if (BpaProcesso.TipoFinanciamento.MAC.equals(param.getTipoFinanciamento())) {
                hqlItem.addToWhereWhithAnd("pf.codigo <> ", ProcedimentoFinanciamento.PAB);
            }
        }

        if (convenio != null) {
            hqlItem.addToWhereWhithAnd("con =", convenio);
        }

        {
            HQLHelper hqlSub = hqlItem.getNewInstanceSubQuery();
            hqlSub.addToSelect("pm.id.procedimentoCompetencia.id.procedimento.codigo");
            hqlSub.addToFrom("ProcedimentoModalidade pm");
            hqlSub.addToWhereWhithAnd("pm.id.procedimentoModalidadeCadastro ", this.param.getModalidade());

            hqlItem.addToWhereWhithAnd("proc.codigo in (" + hqlSub.getQuery() + ")");
        }

        {
            HQLHelper hqlSub = hqlItem.getNewInstanceSubQuery();
            hqlSub.addToSelect("pm.id.procedimentoCompetencia.id.procedimento.codigo");
            hqlSub.addToFrom("ProcedimentoRegistro pm");
            hqlSub.addToWhereWhithAnd("pm.id.procedimentoRegistroCadastro ", this.param.getRegistro());

            hqlItem.addToWhereWhithAnd("proc.codigo in (" + hqlSub.getQuery() + ")");
        }

        Query query = getSession().createQuery(hqlItem.getQuery());

        hqlItem.applyRestrictions(query);

        return hqlItem.getBeanList((List) query.list(), false);
    }

    private List<RelatorioBoletimProducaoAmbulatorialDTO> getItemContaPaciente() throws DAOException {
        Convenio convenio = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("convenioSUS");
        Date dataCompetencia = null;
        try {
            dataCompetencia = (Date) CargaBasicoPadrao.getInstance().getParametroPadrao().getPropertyValue(Parametro.PROP_DATA_COMPETENCIA_PROCEDIMENTO);
        } catch (ValidacaoException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
        HQLHelper hqlItemContaPaciente = new HQLHelper();
        hqlItemContaPaciente.setTypeSelect(RelatorioBoletimProducaoAmbulatorialDTO.class.getName());
        hqlItemContaPaciente.addToFrom(ItemContaPaciente.class.getName(), "icp "
                + " left join icp.profissional prof"
                + " left join icp.cbo cbo"
                + " left join icp.procedimento proce"
                + " left join icp.contaPaciente cp"
                + " left join cp.usuarioCadsus usu"
                + " left join cp.empresa emp"
                + " left join cp.convenio con "
        );

        hqlItemContaPaciente.addToFrom("ProcedimentoCompetencia pc"
                + " left join pc.financiamento pf"
                + " left join pc.id.procedimento proc");

        hqlItemContaPaciente.addToWhereWhithAnd("proc = proce");
        hqlItemContaPaciente.addToWhereWhithAnd("pc.id.dataCompetencia = ", dataCompetencia);

        hqlItemContaPaciente.addToSelect("cast(coalesce(lpad_cast_varchar(icp.idade, 3, '0'), to_char(age(icp.dataLancamento,usu.dataNascimento), 'yyy')) as long)", "idade");

        hqlItemContaPaciente.addToGroup("1");

        hqlItemContaPaciente.addToSelectAndGroup("emp.codigo", "empresa.codigo");
        hqlItemContaPaciente.addToSelectAndGroup("emp.referencia", "empresa.referencia");
        hqlItemContaPaciente.addToSelectAndGroupAndOrder("emp.descricao", "empresa.descricao");
        hqlItemContaPaciente.addToSelectAndGroup("emp.cnes", "empresa.cnes");
        hqlItemContaPaciente.addToSelectAndGroup("cbo.cbo", "tabelaCbo.cbo");
        hqlItemContaPaciente.addToSelectAndGroup("cbo.descricao", "tabelaCbo.descricao");
        hqlItemContaPaciente.addToSelectAndGroup("proc.codigo", "procedimento.codigo");
        hqlItemContaPaciente.addToSelectAndGroup("proc.descricao", "procedimento.descricao");
        hqlItemContaPaciente.addToSelect("sum(icp.quantidade)", "quantidade");

        if (Coalesce.asString(param.getFormaApresentacao()).equals(Profissional.REF)) {
            hqlItemContaPaciente.addToSelectAndGroup("prof.codigo", "profissional.codigo");
            hqlItemContaPaciente.addToSelectAndGroup("prof.referencia", "profissional.referencia");
            hqlItemContaPaciente.addToSelectAndGroupAndOrder("prof.nome", "profissional.nome");
            hqlItemContaPaciente.addToWhereWhithAnd("prof.codigo is not null");
        }

        hqlItemContaPaciente.addToOrder("proc.descricao");
        hqlItemContaPaciente.addToOrder("proc.codigo");
        hqlItemContaPaciente.addToOrder("cbo.descricao");
        hqlItemContaPaciente.addToOrder("1");

        hqlItemContaPaciente.addToWhereWhithAnd("coalesce(proc.flagFaturavel,'S') <> 'N' ");

        hqlItemContaPaciente.addToWhereWhithAnd("cp.status = ", ContaPaciente.Status.FECHADA.value());
        hqlItemContaPaciente.addToWhereWhithAnd("icp.status = ", ItemContaPaciente.Status.CONFIRMADO.value());
        hqlItemContaPaciente.addToWhereWhithAnd("icp.tipo = ", ItemContaPaciente.Tipo.PROCEDIMENTO.value());
        hqlItemContaPaciente.addToWhereWhithAnd("cp.flagFechadaAutomatico = ", RepositoryComponentDefault.NAO_LONG);

        hqlItemContaPaciente.addToWhereWhithAnd("proc = ", this.param.getProcedimento());
        if (this.param.getPeriodo() != null) {
            hqlItemContaPaciente.addToWhereWhithAnd("cp.dataGeracao ", this.param.getPeriodo());
        } else {
            hqlItemContaPaciente.addToWhereWhithAnd("cp.dataGeracao is not null");
        }

        hqlItemContaPaciente.addToWhereWhithAnd("prof ", this.param.getProfissional());

        hqlItemContaPaciente.addToWhereWhithAnd("emp ", this.param.getEmpresa());

        hqlItemContaPaciente.addToWhereWhithAnd("cbo ", this.param.getCbo());

        if (!BpaProcesso.TipoFinanciamento.AMBOS.equals(this.param.getTipoFinanciamento())) {
            if (BpaProcesso.TipoFinanciamento.PAB.equals(param.getTipoFinanciamento())) {
                hqlItemContaPaciente.addToWhereWhithAnd("pf.codigo = ", ProcedimentoFinanciamento.PAB);
            } else if (BpaProcesso.TipoFinanciamento.MAC.equals(param.getTipoFinanciamento())) {
                hqlItemContaPaciente.addToWhereWhithAnd("pf.codigo <> ", ProcedimentoFinanciamento.PAB);
            }
        }

        if (convenio != null) {
            hqlItemContaPaciente.addToWhereWhithAnd("con =", convenio);
        }

        {
            HQLHelper hqlSub = hqlItemContaPaciente.getNewInstanceSubQuery();
            hqlSub.addToSelect("pm.id.procedimentoCompetencia.id.procedimento.codigo");
            hqlSub.addToFrom("ProcedimentoModalidade pm");
            hqlSub.addToWhereWhithAnd("pm.id.procedimentoModalidadeCadastro ", this.param.getModalidade());

            hqlItemContaPaciente.addToWhereWhithAnd("proc.codigo in (" + hqlSub.getQuery() + ")");
        }

        {
            HQLHelper hqlSub = hqlItemContaPaciente.getNewInstanceSubQuery();
            hqlSub.addToSelect("pm.id.procedimentoCompetencia.id.procedimento.codigo");
            hqlSub.addToFrom("ProcedimentoRegistro pm");
            hqlSub.addToWhereWhithAnd("pm.id.procedimentoRegistroCadastro ", this.param.getRegistro());

            hqlItemContaPaciente.addToWhereWhithAnd("proc.codigo in (" + hqlSub.getQuery() + ")");
        }

        Query query = getSession().createQuery(hqlItemContaPaciente.getQuery());

        hqlItemContaPaciente.applyRestrictions(query);

        return hqlItemContaPaciente.getBeanList((List) query.list(), false);
    }

    private List<RelatorioBoletimProducaoAmbulatorialDTO> groupList(List<RelatorioBoletimProducaoAmbulatorialDTO> procedimentos) throws DAOException {
        Comparator<RelatorioBoletimProducaoAmbulatorialDTO> comparator = new Comparator<RelatorioBoletimProducaoAmbulatorialDTO>() {
            @Override
            public int compare(RelatorioBoletimProducaoAmbulatorialDTO o1, RelatorioBoletimProducaoAmbulatorialDTO o2) {
                if (o1 == null && o2 == null) {
                    return 0;
                } else if (o1 == null && o2 != null) {
                    return 1;
                } else if (o1 != null && o2 == null) {
                    return -1;
                }

                int compare = o1.getEmpresa().getDescricao().compareTo(o2.getEmpresa().getDescricao());
                if (Coalesce.asString(param.getFormaApresentacao()).equals(Profissional.REF) && compare == 0) {
                    if (o1.getProfissional() == null && o2.getProfissional() == null) {
                        compare = 0;
                    } else if (o1.getProfissional() == null && o2.getProfissional() != null) {
                        compare = 1;
                    } else if (o1.getProfissional() != null && o2.getProfissional() == null) {
                        compare = -1;
                    } else {
                        compare = o1.getProfissional().getNome().compareTo(o2.getProfissional().getNome());
                    }
                }
                if (compare == 0) {
                    compare = o1.getProcedimento().getDescricao().compareTo(o2.getProcedimento().getDescricao());
                }
                if (compare == 0) {
                    if (o1.getTabelaCbo() == null && o2.getTabelaCbo() == null) {
                        compare = 0;
                    } else if (o1.getTabelaCbo() == null && o2.getTabelaCbo() != null) {
                        compare = 1;
                    } else if (o1.getTabelaCbo() != null && o2.getTabelaCbo() == null) {
                        compare = -1;
                    } else {
                        compare = o1.getTabelaCbo().getDescricao().compareTo(o2.getTabelaCbo().getDescricao());
                    }
                }
                if (compare == 0) {
                    compare = o1.getIdade().compareTo(o2.getIdade());
                }

                return compare;
            }
        };
        List<List<RelatorioBoletimProducaoAmbulatorialDTO>> groupList = CollectionUtils.groupList(procedimentos, comparator);

        List<RelatorioBoletimProducaoAmbulatorialDTO> dtoResult = new ArrayList(groupList.size());
        for (List<RelatorioBoletimProducaoAmbulatorialDTO> list : groupList) {
            RelatorioBoletimProducaoAmbulatorialDTO dtoBase = list.get(0);
            Double quantidade = 0D;
            for (RelatorioBoletimProducaoAmbulatorialDTO relatorioBoletimProducaoAmbulatorialDTO : list) {
                quantidade += relatorioBoletimProducaoAmbulatorialDTO.getQuantidade();
            }
            dtoBase.setQuantidade(quantidade);
            dtoResult.add(dtoBase);
        }

        Collections.sort(dtoResult, comparator);

        return dtoResult;
    }

}
