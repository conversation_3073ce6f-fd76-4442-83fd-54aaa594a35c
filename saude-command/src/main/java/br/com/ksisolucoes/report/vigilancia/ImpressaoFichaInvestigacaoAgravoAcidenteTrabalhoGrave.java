/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.vigilancia;

import br.com.celk.report.vigilancia.query.QueryFichaAcidenteTrabalhoGrave;
import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FichaInvestigacaoAgravoDTOParam;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;
import java.util.LinkedHashMap;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoFichaInvestigacaoAgravoAcidenteTrabalhoGrave extends AbstractReport<FichaInvestigacaoAgravoDTOParam> {

    private QueryFichaAcidenteTrabalhoGrave query;
            
    public ImpressaoFichaInvestigacaoAgravoAcidenteTrabalhoGrave(FichaInvestigacaoAgravoDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        if(query == null){
            query = new QueryFichaAcidenteTrabalhoGrave();
        }
        return query;
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> columnsMap = new LinkedHashMap<>(((QueryFichaAcidenteTrabalhoGrave)getQuery()).getMapeamentoPlanilhaBase());
        
        columnsMap.put("31 Ocupacao", "_31_ocupacao");
        columnsMap.put("32 Situacao Mercado Trabalho", "_32_situacao_mercado_trab");
        columnsMap.put("33 Tempo", "_33_tempos_trab_ocup");
        columnsMap.put("33 Unidade de Tempo", "_33_tempos_trab_ocup_unidade");
        columnsMap.put("34 Local do Acidente", "_34_local_acidente");
        columnsMap.put("35 Registro Contratante", "_35_cpfCnpj");
        columnsMap.put("36 Nome Contratante", "_36_nome");
        columnsMap.put("37 CNAE", "_37_cnae");
        columnsMap.put("38 UF", "_38_uf");
        columnsMap.put("39 Cidade Contratante", "_39_cidade");
        columnsMap.put("39 IBGE Cidade", "_39_codigo_cidade");
        columnsMap.put("40 Distrito", "_40_distrito");
        columnsMap.put("41 Bairro", "_41_bairro");
        columnsMap.put("42 Endereco", "_42_endereco");
        columnsMap.put("43 Numero", "_43_numero");
        columnsMap.put("44 Ponto Referencia", "_44_ponto_referencia");
        columnsMap.put("45 Telefone", "_45_telefone");
        columnsMap.put("46 Empreendedor Terceirizado", "_46_empreendedor_terceirizado");
        columnsMap.put("47 CNAE Empresa Principal", "_47_cnae_");
        columnsMap.put("48 CNPJ", "_48_cnpj");
        columnsMap.put("49 Razao Social", "_49_nome");
        columnsMap.put("50 Hora Acidente", "_50_hora_acidente");
        columnsMap.put("51 Horas Apos Inicio Jornada", "_51_horas_apos_inicio_jornada");
        columnsMap.put("52 UF", "_52_uf");
        columnsMap.put("53 Cidade Acidente", "_53_cidade");
        columnsMap.put("53 IBGE Cidade Acidente", "_53_codigo_cidade");
        columnsMap.put("54 CID Acidente", "_54_cid_acidente");
        columnsMap.put("55 Tipo de Acidente", "_55_tipo_acidente");
        columnsMap.put("56 Outro Atingidos", "_56_outros_atingidos");
        columnsMap.put("57 Quantidade Outros Atingidos", "_57_qtd_outros_atingidos");
        columnsMap.put("58 Ocorreu Atendimento Medico", "_58_ocorreu_atendimento_medico");
        columnsMap.put("59 Data Atendimento Medico", "_59_data_atendimento_medico");
        columnsMap.put("60 UF Atendimento", "_60_uf");
        columnsMap.put("61 Cidade Atendimento", "_61_cidade");
        columnsMap.put("61 IBGE Cidade Atendimento", "_61_codigo_cidade");
        columnsMap.put("62 Unidade Saude Atendimento", "_62_unidade");
        columnsMap.put("62 CNES Unidade Saude Atendimento", "_62_cnes_unidade");
        columnsMap.put("63 Partes Corpo Atingida 1", "_63_partes_corpo_atingida_1");
        columnsMap.put("63 Partes Corpo Atingida 2", "_63_partes_corpo_atingida_2");
        columnsMap.put("63 Partes Corpo Atingida 3", "_63_partes_corpo_atingida_3");
        columnsMap.put("64 CID Atendimento", "_64_cid_atendimento");
        columnsMap.put("65 Regime de Tratamento", "_65_regime_tratamento");
        columnsMap.put("66 Evolucao do Caso", "_66_evolucao_caso");
        columnsMap.put("67 Data do Obito", "_67_data_obito");
        columnsMap.put("68 Comunicacao CAT", "_68_emissao_cat");
        columnsMap.put("Descricao Sumaria", "descricao_sumaria");
        columnsMap.put("Outras Informacoes", "outras_informacoes");
        
        return columnsMap;
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/vigilancia/pdf/ficha_investigacao_acidente_trabalho_grave.pdf";
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return param.getTipoArquivo();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_acidente_trabalho");
    }

}
