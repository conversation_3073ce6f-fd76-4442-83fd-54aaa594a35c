package br.com.ksisolucoes.report.prontuario.basico.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioPerfilAtendimentoDTO;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioPerfilAtendimentoDTOParam;
import br.com.ksisolucoes.util.Coalesce;
import java.util.List;
import java.util.Map;
import org.hibernate.Query;

public class QueryMedicamentos extends CommandQuery {

    private RelatorioPerfilAtendimentoDTOParam param;
    private List<RelatorioPerfilAtendimentoDTO> result;

    public QueryMedicamentos(RelatorioPerfilAtendimentoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("p.descricao", "descricao");
        hql.addToSelect("sum(dmi.quantidadeDispensada)", "quantidadeDouble");
        hql.addToFrom("DispensacaoMedicamentoItem dmi"
                + " left join dmi.produto p"
                + " left join dmi.id.dispensacaoMedicamento dm");
        hql.setTypeSelect(RelatorioPerfilAtendimentoDTO.class.getName());
        hql.addToWhereWhithAnd("dm.id.empresa in ", param.getEmpresas());
        hql.addToWhereWhithAnd("dm.usuarioCadsusDestino in ", param.getUsuarioCadsus());
        hql.addToWhereWhithAnd("dm.dataDispensacao ", param.getPeriodo());
        hql.addToGroup("p.descricao");
        hql.addToOrder("2 desc");
    }

    @Override
    protected void customQuery(Query query) {
        query.setMaxResults(Coalesce.asLong(param.getNumeroDispensacoes(), 10L).intValue());
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioPerfilAtendimentoDTO> getResult() {
        return result;
    }
}
