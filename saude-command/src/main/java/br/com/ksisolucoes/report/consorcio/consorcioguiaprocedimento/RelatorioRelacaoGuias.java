package br.com.ksisolucoes.report.consorcio.consorcioguiaprocedimento;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.consorcio.consorcioguiaprocedimento.query.QueryRelatorioRelacaoGuias;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioRelacaoGuiasDTOParam;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoGuias extends AbstractReport<RelatorioRelacaoGuiasDTOParam> {

    public RelatorioRelacaoGuias(RelatorioRelacaoGuiasDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/consorcio/consorcioguiaprocedimento/jrxml/relatorio_relacao_guias.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_guias");
    }

    @Override
    public ITransferDataReport getQuery() {
        addParametro("tipoData", getParam().getTipoData().descricao());
        return new QueryRelatorioRelacaoGuias();
    }
    
}
