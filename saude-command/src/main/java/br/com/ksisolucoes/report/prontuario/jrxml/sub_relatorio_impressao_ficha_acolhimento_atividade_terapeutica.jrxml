<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="sub_relatorio_impressao_ficha_acolhimento_atividade_terapeutica" pageWidth="550" pageHeight="842" columnWidth="550" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="03f38e68-54e0-484e-aee2-d8c2366d83e8">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<field name="diaSemanaDescricao" class="java.lang.String"/>
	<field name="descricao" class="java.lang.String"/>
	<field name="hora" class="java.util.Date"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="14" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="0" y="0" width="110" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Dia*/Bundle.getStringApplication("rotulo_dia")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="116" y="0" width="110" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Hora*/Bundle.getStringApplication("rotulo_hora")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement uuid="21983266-7efb-49a0-8204-2abd51d1e0ca" x="0" y="13" width="550" height="1"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement uuid="26b31ea5-8b1c-46ab-8d44-9c33821cdc83" key="textField-9" positionType="Float" x="226" y="0" width="324" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Hora*/Bundle.getStringApplication("rotulo_atividade")]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="11" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="b58414e6-c033-48d5-888f-1c3a94337d6c" stretchType="RelativeToBandHeight" x="0" y="0" width="110" height="11"/>
				<textElement verticalAlignment="Top" markup="html">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{diaSemanaDescricao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="HH:mm" isBlankWhenNull="true">
				<reportElement uuid="b58414e6-c033-48d5-888f-1c3a94337d6c" stretchType="RelativeToBandHeight" x="116" y="0" width="110" height="11"/>
				<textElement verticalAlignment="Top" markup="html">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{hora}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="b58414e6-c033-48d5-888f-1c3a94337d6c" stretchType="RelativeToBandHeight" x="226" y="0" width="324" height="11"/>
				<textElement verticalAlignment="Top" markup="html">
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
