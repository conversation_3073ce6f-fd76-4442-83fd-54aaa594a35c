package br.com.ksisolucoes.report.hospital;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.hospital.interfaces.dto.RelatorioFaturamentoDTOParam;
import br.com.ksisolucoes.report.hospital.query.QueryRelatorioFaturamento;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioFaturamento extends AbstractReport<RelatorioFaturamentoDTOParam> {

    public RelatorioFaturamento(RelatorioFaturamentoDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        if (RelatorioFaturamentoDTOParam.TipoRelatorio.DETALHADO.equals(this.getParam().getTipoRelatorio())) {
            return "/br/com/ksisolucoes/report/hospital/jrxml/relatorio_faturamento_detalhado.jrxml";
        }
        return "/br/com/ksisolucoes/report/hospital/jrxml/relatorio_faturamento_resumido.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relatorio_faturamento");
    }

    @Override
    public ITransferDataReport getQuery() {
        this.addParametro("formaApresentacao", this.getParam().getFormaApresentacao());
        return new QueryRelatorioFaturamento();
    }
}
