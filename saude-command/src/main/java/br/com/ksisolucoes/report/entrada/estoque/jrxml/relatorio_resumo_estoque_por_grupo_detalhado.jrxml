<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.16.0.final using JasperReports Library version 6.16.0-48579d909b7943b64690c65c71e07e0b80981928  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_resumo_estoque_por_grupo_detalhado" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="72583fa0-2a16-48da-8e84-719795a17160">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.7715610000000013"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.report.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault.TipoPreco"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="FormaApresentacao" class="java.lang.Integer" isForPrompting="false"/>
	<parameter name="AgruparEmpresa" class="java.lang.String"/>
	<parameter name="tipoPreco" class="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault.TipoPreco"/>
	<field name="codigoEmpresa" class="java.lang.String"/>
	<field name="descricaoEmpresa" class="java.lang.String"/>
	<field name="codigoProduto" class="java.lang.String"/>
	<field name="descricaoProduto" class="java.lang.String"/>
	<field name="unidadeProduto" class="java.lang.String"/>
	<field name="descricaoTipoDocumento" class="java.lang.String"/>
	<field name="flagSiglaTipoDocumento" class="java.lang.String"/>
	<field name="flagTipoMovimentoTipoDocumento" class="java.lang.String"/>
	<field name="quantidade" class="java.lang.Double"/>
	<field name="precoMedio" class="java.lang.Double"/>
	<field name="precoCusto" class="java.lang.Double"/>
	<field name="precoUnitario" class="java.lang.Double"/>
	<field name="codigoGrupoProduto" class="java.lang.Long"/>
	<field name="descricaoGrupoProduto" class="java.lang.String"/>
	<field name="codigoSubGrupo" class="java.lang.Long"/>
	<field name="descricaoSubGrupo" class="java.lang.String"/>
	<field name="descricaoFormatadoSubGrupo" class="java.lang.String"/>
	<field name="descricaoFormatadoGrupoProduto" class="java.lang.String"/>
	<field name="descricaoFormatadoProduto" class="java.lang.String"/>
	<field name="descricaoFormatadoEmpresaDestino" class="java.lang.String"/>
	<field name="codigoEmpresaDestino" class="java.lang.String"/>
	<field name="descricaoFormatadoCentroCusto" class="java.lang.String"/>
	<field name="codigoCentroCusto" class="java.lang.Long"/>
	<field name="descricaoCentroCusto" class="java.lang.String"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="DATA" class="br.com.ksisolucoes.util.Data"/>
	<variable name="SOMA_VALOR_TOTAL" class="java.lang.Double" resetType="Group" resetGroup="GrupoProdutoTipo" calculation="Sum">
		<variableExpression><![CDATA[($F{quantidade} * (TipoPreco.PRECO_MEDIO.equals($P{tipoPreco}) ?
    $F{precoMedio} :
    $F{precoCusto}))]]></variableExpression>
	</variable>
	<variable name="SOMA_QUANTIDADE" class="java.lang.Double" resetType="Group" resetGroup="GrupoProdutoTipo" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}]]></variableExpression>
	</variable>
	<variable name="PRECO_MEDIO_CUSTO" class="java.lang.Double" resetType="Group" resetGroup="GrupoProdutoTipo" incrementType="Group" incrementGroup="GrupoProdutoTipo">
		<variableExpression><![CDATA[(($V{SOMA_VALOR_TOTAL} != 0 ) ? ($V{SOMA_VALOR_TOTAL}/$V{SOMA_QUANTIDADE}):0)]]></variableExpression>
	</variable>
	<variable name="TOTAL_SUBGRUPO" class="java.lang.Double" resetType="Group" resetGroup="SubGrupo" incrementType="Group" incrementGroup="GrupoProdutoTipo" calculation="Sum">
		<variableExpression><![CDATA[$V{SOMA_VALOR_TOTAL}]]></variableExpression>
	</variable>
	<variable name="TOTAL_GRUPO_EMPRESA" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" incrementType="Group" incrementGroup="GrupoProdutoTipo" calculation="Sum">
		<variableExpression><![CDATA[$V{SOMA_VALOR_TOTAL}]]></variableExpression>
	</variable>
	<variable name="FORMA_APRESENTACAO" class="java.lang.String">
		<variableExpression><![CDATA[$P{FormaApresentacao} == ReportProperties.AGRUPAR_GRUPO
?
    $V{BUNDLE}.getStringApplication("rotulo_grupo")
    :
    $P{FormaApresentacao} == ReportProperties.AGRUPAR_EMPRESA
    ?
       $V{BUNDLE}.getStringApplication("rotulo_unidade_destino_origem")
    :
       $V{BUNDLE}.getStringApplication("rotulo_centro_custo")]]></variableExpression>
	</variable>
	<variable name="TOTAL_GERAL" class="java.lang.Double" incrementType="Group" incrementGroup="GrupoProdutoTipo" calculation="Sum">
		<variableExpression><![CDATA[$V{SOMA_VALOR_TOTAL}]]></variableExpression>
	</variable>
	<group name="Geral">
		<groupFooter>
			<band height="11">
				<line>
					<reportElement x="315" y="0" width="220" height="1" uuid="496d7bdf-5868-4862-8dab-14500ad48c4b"/>
				</line>
				<textField evaluationTime="Auto" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-56" mode="Opaque" x="416" y="1" width="114" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="4705b88e-4e85-4597-aaec-caec6db5fa5d"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TOTAL_GERAL}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-55" mode="Opaque" x="284" y="1" width="133" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="adcb4390-5a0c-423c-b8cd-a069701fc161"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_total_geral")+":"]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="GrupoEmpresa" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{AgruparEmpresa})
?
    $F{codigoEmpresa}
:
    null]]></groupExpression>
		<groupHeader>
			<band height="15" splitType="Stretch">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{AgruparEmpresa})]]></printWhenExpression>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-57" mode="Opaque" x="1" y="0" width="534" height="14" forecolor="#333333" backcolor="#FFFFFF" uuid="2d99544d-878b-499a-921b-ddf9c74349db"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*empresa*/$V{BUNDLE}.getStringApplication("rotulo_unidade_movimentacao") + ":" + " " + $F{descricaoEmpresa} + " (" + $F{codigoEmpresa}.trim() + ") "]]></textFieldExpression>
				</textField>
				<rectangle radius="10">
					<reportElement mode="Transparent" x="0" y="0" width="535" height="15" uuid="f19963b4-1197-4701-bff0-e2fabe2bc512"/>
				</rectangle>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="11" splitType="Stretch">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{AgruparEmpresa})]]></printWhenExpression>
				<line>
					<reportElement x="315" y="0" width="220" height="1" uuid="278973e8-c900-4adb-9ebb-1f30a6917272"/>
				</line>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-55" mode="Opaque" x="283" y="1" width="133" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="ee0a4134-c3fb-4595-a93d-22e21bbb7a47"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_total")+" Unidade:"]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Auto" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-56" mode="Opaque" x="416" y="1" width="114" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="025a3d04-576c-4134-80b9-a640302ffbb9"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TOTAL_GRUPO_EMPRESA}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="SubGrupo" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$P{FormaApresentacao} == ReportProperties.AGRUPAR_GRUPO
?
    $F{codigoGrupoProduto} + $F{codigoSubGrupo}
:
    $P{FormaApresentacao} == ReportProperties.AGRUPAR_EMPRESA
    ?
        $F{codigoEmpresaDestino}
    :
        $F{codigoCentroCusto}]]></groupExpression>
		<groupHeader>
			<band height="27" splitType="Stretch">
				<textField evaluationTime="Group" evaluationGroup="SubGrupo" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-58" mode="Opaque" x="5" y="0" width="530" height="14" isRemoveLineWhenBlank="true" forecolor="#333333" backcolor="#FFFFFF" uuid="d4bc4374-db50-42f3-b8c2-14fe4e321409"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="true" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{FormaApresentacao} == ReportProperties.AGRUPAR_GRUPO
?
    /*grupo*/$V{BUNDLE}.getStringApplication("rotulo_grupo") + ": " + $F{descricaoFormatadoGrupoProduto}+" / "+/*subgrupo*/$V{BUNDLE}.getStringApplication("rotulo_subgrupo") + ": " + $F{descricaoFormatadoSubGrupo}
:
    $P{FormaApresentacao} == ReportProperties.AGRUPAR_EMPRESA
    ?
        /*unidade destino*/$V{BUNDLE}.getStringApplication("rotulo_unidade_destino_origem") + ": "+ $F{descricaoFormatadoEmpresaDestino}
    :
        /*centro custo*/$V{BUNDLE}.getStringApplication("rotulo_centro_custo") + ": "+ $F{descricaoFormatadoCentroCusto}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-45" mode="Opaque" x="2" y="15" width="303" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="25453fce-2c08-4348-ba8a-de98f1be1188"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*produto*/$V{BUNDLE}.getStringApplication("rotulo_produto")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-47" mode="Opaque" x="312" y="15" width="20" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="7cc5fd7f-ed6c-4a36-bb38-88f3f961f6ba"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*unidade*/$V{BUNDLE}.getStringApplication("rotulo_unidade_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-48" mode="Opaque" x="333" y="15" width="25" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="1117ec7b-cd46-4453-bb20-c5bebeddac83"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*sigla*/$V{BUNDLE}.getStringApplication("rotulo_sigla")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-51" mode="Opaque" x="359" y="15" width="55" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="78821487-ce7b-4991-9092-ce6d7ed64367"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*quantidade*/$V{BUNDLE}.getStringApplication("rotulo_qtdade")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-54" mode="Opaque" x="415" y="15" width="55" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="fd21b6ca-4ddb-4c9b-808f-a927633447ce"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*preco medio/custo*/
$P{tipoPreco}.getDescricaoAbreviada()]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-55" mode="Opaque" x="472" y="15" width="55" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="32bc8890-2118-464b-8ac2-f41bb69cc14e"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*total*/$V{BUNDLE}.getStringApplication("rotulo_total")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="26" width="535" height="1" uuid="7dc38fdf-010a-4c33-859b-1271c7cbb919"/>
				</line>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="11" splitType="Stretch">
				<textField evaluationTime="Auto" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-56" mode="Opaque" x="416" y="1" width="114" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="8445d5a4-ef32-42ed-b989-60782f1b4e03"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TOTAL_SUBGRUPO}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-55" mode="Opaque" x="283" y="1" width="133" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="188055a8-81cc-425b-851e-a0167713d0ee"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_total")+" "+$V{FORMA_APRESENTACAO}+":"]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="315" y="0" width="220" height="1" uuid="349e7209-4986-4929-899c-ce7fa9b1c591"/>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="GrupoProdutoTipo" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{descricaoProduto} + $F{descricaoTipoDocumento} + $F{unidadeProduto}]]></groupExpression>
		<groupHeader>
			<band height="12" splitType="Stretch">
				<textField evaluationTime="Group" evaluationGroup="GrupoProdutoTipo" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-4" mode="Opaque" x="336" y="0" width="25" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="fc8fc04a-b467-4b65-ab39-d539bf5b0965"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{flagSiglaTipoDocumento}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-5" mode="Opaque" x="5" y="0" width="306" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="6e07b464-2b05-457b-b8d1-8a0d30e4f61a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{descricaoFormatadoProduto}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoProdutoTipo" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-6" mode="Opaque" x="315" y="0" width="20" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="ab2914d4-99ef-4cd2-8b12-b58cfa16dd8d"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{unidadeProduto}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoProdutoTipo" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-52" mode="Opaque" x="362" y="0" width="55" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="6216dc34-f6a8-44a1-9eeb-861325534c08"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{SOMA_QUANTIDADE}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoProdutoTipo" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-56" mode="Opaque" x="475" y="0" width="55" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="300bd732-fd05-4b6d-8b9e-4b58fb34c7b3"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{SOMA_VALOR_TOTAL}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoProdutoTipo" pattern="###0.0000" isBlankWhenNull="true">
					<reportElement key="textField-53" mode="Opaque" x="418" y="0" width="55" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="2b123bbd-bf91-4c8f-ad2c-b0daebb6241d"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{PRECO_MEDIO_CUSTO}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band splitType="Stretch"/>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
