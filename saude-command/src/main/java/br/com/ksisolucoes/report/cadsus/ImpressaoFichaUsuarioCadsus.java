/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.cadsus;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.cadsus.interfaces.dto.UsuarioCadsusDTOParam;
import br.com.ksisolucoes.report.cadsus.query.QueryImpressaoFichaUsuarioCadsus;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoFichaUsuarioCadsus extends AbstractReport<UsuarioCadsusDTOParam>{

    private String parametroReferencia;

    public ImpressaoFichaUsuarioCadsus(UsuarioCadsusDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/cadsus/jrxml/ficha_usuario_cadsus.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_ficha_paciente");
    }

    @Override
    public ITransferDataReport getQuery() {

        addParametro("codigoOuReferencia", codigoOuReferencia());

        return new QueryImpressaoFichaUsuarioCadsus();
    }

    private boolean codigoOuReferencia(){
        try {
            parametroReferencia = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("referencia");
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage(), e);
        }

        return RepositoryComponentDefault.SIM.equals(parametroReferencia);
    }
}
