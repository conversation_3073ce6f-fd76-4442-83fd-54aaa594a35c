<?xml version="1.0" encoding="ISO-8859-1"?>
<jasperReport bottomMargin="20" columnCount="1" columnSpacing="0"
    columnWidth="535" isSummaryNewPage="false" isTitleNewPage="false"
    leftMargin="30" name="sub_relatorio_movimentacao_estoque"
    orientation="Portrait" pageHeight="842" pageWidth="595"
    printOrder="Vertical" rightMargin="30" topMargin="20" whenNoDataType="NoPages">
    <property name="ireport.scriptlethandling" value="0"/>
    <import value="java.util.*"/>
    <import value="net.sf.jasperreports.engine.*"/>
    <import value="net.sf.jasperreports.engine.data.*"/>
    <import value="br.com.ksisolucoes.util.*"/>
    <field class="java.lang.String" name="sigla"/>
    <field class="java.lang.Double" name="quantidade"/>
    <background>
        <band height="0" isSplitAllowed="true"/>
    </background>
    <title>
        <band height="0" isSplitAllowed="true"/>
    </title>
    <pageHeader>
        <band height="0" isSplitAllowed="true"/>
    </pageHeader>
    <columnHeader>
        <band height="25" isSplitAllowed="true">
            <rectangle radius="5">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="11" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="rectangle-1"
                    mode="Opaque" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="88" x="22" y="13"/>
                <graphicElement fill="Solid" pen="Thin" stretchType="NoStretch"/>
            </rectangle>
            <textField evaluationTime="Now" hyperlinkTarget="Self"
                hyperlinkType="None" isBlankWhenNull="false"
                isStretchWithOverflow="false" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="10" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="textField-1"
                    mode="Transparent" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="34" x="25" y="13"/>
                <box bottomBorder="None" bottomBorderColor="#000000"
                    leftBorder="None" leftBorderColor="#000000"
                    rightBorder="None" rightBorderColor="#000000"
                    topBorder="None" topBorderColor="#000000"/>
                <textElement lineSpacing="Single" rotation="None"
                    textAlignment="Center" verticalAlignment="Top">
                    <font fontName="Arial" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="Cp1252" pdfFontName="Helvetica" size="7"/>
                </textElement>
                <textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_sigla")]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Now" hyperlinkTarget="Self"
                hyperlinkType="None" isBlankWhenNull="false"
                isStretchWithOverflow="false" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="10" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="textField-3"
                    mode="Transparent" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="43" x="63" y="13"/>
                <box bottomBorder="None" bottomBorderColor="#000000"
                    leftBorder="None" leftBorderColor="#000000"
                    rightBorder="None" rightBorderColor="#000000"
                    topBorder="None" topBorderColor="#000000"/>
                <textElement lineSpacing="Single" rotation="None"
                    textAlignment="Right" verticalAlignment="Top">
                    <font fontName="Arial" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="Cp1252" pdfFontName="Helvetica" size="7"/>
                </textElement>
                <textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_quantidade_abv")]]></textFieldExpression>
            </textField>
            <line direction="TopDown">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="10" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="line-2"
                    mode="Opaque" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="0" x="62" y="14"/>
                <graphicElement fill="Solid" pen="Thin" stretchType="NoStretch"/>
            </line>
            <textField evaluationTime="Now" hyperlinkTarget="Self"
                hyperlinkType="None" isBlankWhenNull="false"
                isStretchWithOverflow="false" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="10" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="textField-5"
                    mode="Transparent" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="128" x="1" y="1"/>
                <box bottomBorder="None" bottomBorderColor="#000000"
                    leftBorder="None" leftBorderColor="#000000"
                    rightBorder="None" rightBorderColor="#000000"
                    topBorder="None" topBorderColor="#000000"/>
                <textElement lineSpacing="Single" rotation="None"
                    textAlignment="Center" verticalAlignment="Top">
                    <font fontName="Arial" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="Cp1252" pdfFontName="Helvetica" size="7"/>
                </textElement>
                <textFieldExpression class="java.lang.String"><![CDATA[Bundle.getStringApplication("rotulo_resumo_por_tipo_de_documento")]]></textFieldExpression>
            </textField>
        </band>
    </columnHeader>
    <detail>
        <band height="13" isSplitAllowed="true">
            <textField evaluationTime="Now" hyperlinkTarget="Self"
                hyperlinkType="None" isBlankWhenNull="true"
                isStretchWithOverflow="true" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="10" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="textField-2"
                    mode="Opaque" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="34" x="25" y="2"/>
                <box bottomBorder="None" bottomBorderColor="#000000"
                    leftBorder="None" leftBorderColor="#000000"
                    rightBorder="None" rightBorderColor="#000000"
                    topBorder="None" topBorderColor="#000000"/>
                <textElement lineSpacing="Single" rotation="None"
                    textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="SansSerif" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="CP1252" pdfFontName="Helvetica" size="7"/>
                </textElement>
                <textFieldExpression class="java.lang.String"><![CDATA[$F{sigla}]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Now" hyperlinkTarget="Self"
                hyperlinkType="None" isBlankWhenNull="true"
                isStretchWithOverflow="true" pattern="#,##0.00">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="10" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="textField-4"
                    mode="Opaque" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="43" x="63" y="2"/>
                <box bottomBorder="None" bottomBorderColor="#000000"
                    leftBorder="None" leftBorderColor="#000000"
                    rightBorder="None" rightBorderColor="#000000"
                    topBorder="None" topBorderColor="#000000"/>
                <textElement lineSpacing="Single" rotation="None"
                    textAlignment="Right" verticalAlignment="Middle">
                    <font fontName="SansSerif" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="CP1252" pdfFontName="Helvetica" size="7"/>
                </textElement>
                <textFieldExpression class="java.lang.Double"><![CDATA[$F{quantidade}]]></textFieldExpression>
            </textField>
            <line direction="TopDown">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="13" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="line-1"
                    mode="Opaque" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="0" x="62" y="0"/>
                <graphicElement fill="Solid" pen="Thin" stretchType="NoStretch"/>
            </line>
        </band>
    </detail>
    <columnFooter>
        <band height="0" isSplitAllowed="true"/>
    </columnFooter>
    <pageFooter>
        <band height="0" isSplitAllowed="true"/>
    </pageFooter>
    <summary>
        <band height="0" isSplitAllowed="true"/>
    </summary>
</jasperReport>
