package br.com.ksisolucoes.report.entrada.estoque.query;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioGiroEstoqueDTO;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioGiroEstoqueDTOParam;
import br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento;

/**
 * <AUTHOR>
 */
public class QueryRelatorioGiroEstoque extends CommandQuery<QueryRelatorioGiroEstoque> implements ReportProperties, ITransferDataReport<RelatorioGiroEstoqueDTOParam, RelatorioGiroEstoqueDTO> {

    private RelatorioGiroEstoqueDTOParam param;
    private List<RelatorioGiroEstoqueDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.setTypeSelect(RelatorioGiroEstoqueDTO.class.getName());

        if (param.getEmpresa() == null) {
            hql.addToSelectAndGroup("me.id.empresa.codigo", "empresa.codigo");
            hql.addToSelectAndGroup("me.id.empresa.descricao", "empresa.descricao");
        }
        hql.addToSelectAndGroup("me.produto.codigo", "produto.codigo");
        hql.addToSelectAndGroup("me.produto.descricao", "produto.descricao");
        hql.addToSelectAndGroup("me.produto.unidade.unidade", "produto.unidade.unidade");
        hql.addToSelectAndGroup("me.produto.subGrupo.roGrupoProduto.codigo", "grupoProduto.codigo");
        hql.addToSelectAndGroup("me.produto.subGrupo.roGrupoProduto.descricao", "grupoProduto.descricao");
        hql.addToSelectAndGroup("coalesce(ee.precoCusto, 0)", "precoCusto");
        hql.addToSelect("sum(me.quantidade)", "qtdeSaida");
        hql.addToSelectAndGroup("cc.codigo", "centroCusto.codigo");
        hql.addToSelectAndGroup("cc.descricao", "centroCusto.descricao");
        hql.addToSelect("(select coalesce(e.estoqueFisico,0) from EstoqueEmpresa e where e.id.produto = me.produto and e.id.empresa = me.id.empresa)", "estoqueAtual");
        hql.addToSelect("(select coalesce(e.estoqueMinimo,0) from EstoqueEmpresa e where e.id.produto = me.produto and e.id.empresa = me.id.empresa)", "estoqueMinimo");
        hql.addToSelectAndGroup("coalesce(ee.precoMedio, 0)", "precoMedio");
        hql.addToFrom("MovimentoEstoque me "
                + " left join me.empresaDestino ed "
                + " left join me.centroCusto cc, EstoqueEmpresa ee");

        hql.addToWhereWhithAnd("me.id.empresa =", param.getEmpresa());
        hql.addToWhereWhithAnd("me.dataLancamento ", param.getPeriodo());
        hql.addToWhereWhithAnd("me.tipoDocumento.flagTipoMovimento =", TipoDocumento.IS_SAIDA);
        hql.addToWhereWhithAnd("me.tipoDocumento.flagConsumo =", TipoDocumento.IS_CONSUMO);

        hql.addToWhereWhithAnd("ee.id.empresa = me.id.empresa");
        hql.addToWhereWhithAnd("ee.id.produto = me.produto");

        if (param.getEstoqueAtualAbaixoEstoqueMinimo()) {
            hql.addToWhereWhithAnd("(select coalesce(e.estoqueFisico,0) from EstoqueEmpresa e where e.id.produto = me.produto and e.id.empresa = me.id.empresa) < (select coalesce(e.estoqueMinimo,0) from EstoqueEmpresa e where e.id.produto = me.produto and e.id.empresa = me.id.empresa)");
        }

        if (param.getGrupoProduto() != null) {
            hql.addToWhereWhithAnd("me.produto.subGrupo.roGrupoProduto = ", param.getGrupoProduto());
            if (this.param.getSubGrupo() != null) {
                hql.addToWhereWhithAnd(" me.produto.subGrupo.id.codigo = ", param.getSubGrupo().getId().getCodigo());
            }
        }

        hql.addToGroup("me.id.empresa");
        hql.addToGroup("me.quantidade");

        //Localizacao
        hql.addToWhereWhithAnd(" cc =", param.getCentroCusto());
        RelatorioGiroEstoqueDTOParam.TipoOrdenacao tipoOrdenacao = this.param.getTipoOrdenacao();

        if (param.getEmpresa() == null) {
            hql.addToOrder("me.id.empresa.descricao ");
            hql.addToOrder("me.id.empresa.codigo ");
        }

        if (param.getFormaApresentacao().value().equals(RelatorioGiroEstoqueDTOParam.FormaApresentacao.CENTRO_CUSTO.value())) {
            hql.addToOrder("cc.descricao ");
        } else if (param.getFormaApresentacao().value().equals(RelatorioGiroEstoqueDTOParam.FormaApresentacao.GRUPO_PRODUTO.value())) {
            hql.addToOrder("me.produto.subGrupo.roGrupoProduto.descricao ");
        }

        if (param.getOrdenacao().equals(RelatorioGiroEstoqueDTOParam.Ordenacao.DESCRICAO_PRODUTO)) {
            hql.addToOrder("me.produto.descricao " + tipoOrdenacao.getCommand());
        } else if (param.getOrdenacao().equals(RelatorioGiroEstoqueDTOParam.Ordenacao.QTD_SAIDA)) {
            hql.addToOrder("sum(me.quantidade) " + tipoOrdenacao.getCommand());
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public Collection getResult() {
        return result;
    }

    @Override
    public void setDTOParam(RelatorioGiroEstoqueDTOParam param) {
        this.param = param;
    }
}
