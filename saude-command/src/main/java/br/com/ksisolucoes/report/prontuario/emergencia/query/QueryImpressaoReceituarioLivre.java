package br.com.ksisolucoes.report.prontuario.emergencia.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.prontuario.enfermagem.interfaces.dto.ImpressaoReceituarioLivreDTO;
import br.com.ksisolucoes.report.prontuario.enfermagem.interfaces.dto.ImpressaoReceituarioLivreDTOParam;

import java.util.Collection;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class QueryImpressaoReceituarioLivre extends CommandQuery implements ITransferDataReport<ImpressaoReceituarioLivreDTOParam, ImpressaoReceituarioLivreDTO> {

    private ImpressaoReceituarioLivreDTOParam param;
    private List<ImpressaoReceituarioLivreDTO> list;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.setTypeSelect(ImpressaoReceituarioLivreDTO.class.getName());

        ImpressaoReceituarioLivreDTO proxy = on(ImpressaoReceituarioLivreDTO.class);

        hql.addToSelect("receituarioItem.codigo", path(proxy.getReceituarioItem().getCodigo()));
        hql.addToSelect("receituarioItem.descricaoReceituario", path(proxy.getReceituarioItem().getDescricaoReceituario()));

        hql.addToSelect("receituario.dataCadastro", path(proxy.getReceituarioItem().getReceituario().getDataCadastro()));
        hql.addToSelect("receituario.token", path(proxy.getReceituarioItem().getReceituario().getToken()));

        hql.addToSelect("profissional.codigo", path(proxy.getProfissional().getCodigo()));
        hql.addToSelect("profissional.referencia", path(proxy.getProfissional().getReferencia()));
        hql.addToSelect("profissional.nome", path(proxy.getProfissional().getNome()));
        hql.addToSelect("profissional.unidadeFederacaoConselhoRegistro", path(proxy.getProfissional().getUnidadeFederacaoConselhoRegistro()));
        hql.addToSelect("profissional.numeroRegistro", path(proxy.getProfissional().getNumeroRegistro()));

        hql.addToSelect("conselhoClasse.sigla", path(proxy.getProfissional().getConselhoClasse().getSigla()));
        hql.addToSelect("conselhoClasse.codigo", path(proxy.getProfissional().getConselhoClasse().getCodigo()));
        hql.addToSelect("conselhoClasse.descricao", path(proxy.getProfissional().getConselhoClasse().getDescricao()));

        hql.addToSelect("usuarioCadsus.codigo", path(proxy.getUsuarioCadsus().getCodigo()));
        hql.addToSelect("usuarioCadsus.nome", path(proxy.getUsuarioCadsus().getNome()));
        hql.addToSelect("usuarioCadsus.cpf", path(proxy.getUsuarioCadsus().getCpf()));
        hql.addToSelect("usuarioCadsus.apelido", path(proxy.getUsuarioCadsus().getApelido()));
        hql.addToSelect("usuarioCadsus.utilizaNomeSocial", path(proxy.getUsuarioCadsus().getUtilizaNomeSocial()));
        hql.addToSelect("usuarioCadsus.dataNascimento", path(proxy.getUsuarioCadsus().getDataNascimento()));
        hql.addToSelect("usuarioCadsus.nomeMae", path(proxy.getUsuarioCadsus().getNomeMae()));

        hql.addToSelect("(select min(ucc.numeroCartao) from UsuarioCadsusCns ucc where ucc.usuarioCadsus = usuarioCadsus and ucc.excluido = 0)", path(proxy.getCartaoCns()));

        hql.addToFrom("ReceituarioItem receituarioItem "
                + " right join receituarioItem.receituario receituario"
                + " left join receituario.atendimento atendimento"
                + " left join atendimento.usuarioCadsus usuarioCadsus"
                + " left join receituario.profissional profissional"
                + " left join profissional.conselhoClasse conselhoClasse"
        );

        hql.addToWhereWhithAnd("receituario.codigo = ", this.param.getCodigoReceituario());

        hql.addToOrder("receituario.codigo");
    }

    @Override
    public Collection<ImpressaoReceituarioLivreDTO> getResult() {
        return this.list;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        list = hql.getBeanList((List) result);
    }

    public void setDTOParam(ImpressaoReceituarioLivreDTOParam t) {
        this.param = t;
    }
}
