package br.com.ksisolucoes.report.materiais.ordemcompra;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.materiais.ordemcompra.interfaces.dto.RelatorioImpressaoOrdemCompraDTOParam;
import br.com.ksisolucoes.report.materiais.ordemcompra.query.QueryRelatorioImpressaoOrdemCompra;
import br.com.ksisolucoes.report.materiais.ordemcompra.query.QueryRelatorioImpressaoOrdemCompraPregao;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoOrdemCompra extends AbstractReport<RelatorioImpressaoOrdemCompraDTOParam> {

    public RelatorioImpressaoOrdemCompra(RelatorioImpressaoOrdemCompraDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        if (param.isRelatorioPregao()){
            return "/br/com/ksisolucoes/report/materiais/ordemcompra/jrxml/relatorio_impressao_ordem_compra_pregao.jrxml";
        }
        else {
            return "/br/com/ksisolucoes/report/materiais/ordemcompra/jrxml/relatorio_impressao_ordem_compra.jrxml";
        }
    }

    @Override
    public ITransferDataReport getQuery() {
        if (param.isRelatorioPregao()) {
            return new QueryRelatorioImpressaoOrdemCompraPregao();
        }
        else{
            return new QueryRelatorioImpressaoOrdemCompra();
        }
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_impressao_ordem_compra");
    }

}
