<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_financeiro" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="9155b179-bb03-426d-a648-bc310bac77b6">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioRelacaoFinanceiroDTOParam.FormaApresentacao"/>
	<import value="br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.util.Dinheiro"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="FORMA_APRESENTACAO" class="br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioRelacaoFinanceiroDTOParam.FormaApresentacao"/>
	<field name="vigilanciaFinanceiro" class="br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro"/>
	<field name="requerimentoVigilancia" class="br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia">
		<fieldDescription><![CDATA[vigilanciaFinanceiro.requerimentoVigilancia]]></fieldDescription>
	</field>
	<field name="tipoDocumento" class="java.lang.String"/>
	<field name="protocolo" class="java.lang.String"/>
	<field name="valorTotal" class="java.lang.Double"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="valorPago" class="java.lang.Double">
		<variableExpression><![CDATA[VigilanciaFinanceiro.Status.PAGO.value().equals($F{vigilanciaFinanceiro}.getStatus())
? $F{valorTotal}
: 0D]]></variableExpression>
	</variable>
	<variable name="saldo" class="java.lang.Double">
		<variableExpression><![CDATA[VigilanciaFinanceiro.Status.PAGO.value().equals($F{vigilanciaFinanceiro}.getStatus())
? 0D
: $F{valorTotal}]]></variableExpression>
	</variable>
	<variable name="valorTotalFA" class="java.lang.Double" resetType="Group" resetGroup="FA" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotal}]]></variableExpression>
	</variable>
	<variable name="valorPagoTotalFA" class="java.lang.Double" resetType="Group" resetGroup="FA" calculation="Sum">
		<variableExpression><![CDATA[$V{valorPago}]]></variableExpression>
	</variable>
	<variable name="saldoTotalFA" class="java.lang.Double" resetType="Group" resetGroup="FA" calculation="Sum">
		<variableExpression><![CDATA[$V{saldo}]]></variableExpression>
	</variable>
	<variable name="valorTotal" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotal}]]></variableExpression>
	</variable>
	<variable name="valorPagoTotal" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$V{valorPago}]]></variableExpression>
	</variable>
	<variable name="saldoTotal" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$V{saldo}]]></variableExpression>
	</variable>
	<group name="Geral">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupFooter>
			<band height="15">
				<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="752" y="5" width="48" height="10" uuid="1e0507ec-648d-47ad-b73c-************"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{saldoTotal}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="586" y="3" width="216" height="1" uuid="1de992f2-fdbc-427a-b64a-6bd1b739b2d1"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="586" y="5" width="61" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="0e052180-1124-4859-bdbe-4932bbd5088a"/>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*TOTAL GERAL: */
$V{BUNDLE}.getStringApplication("rotulo_total_geral").toUpperCase() + ": "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="648" y="5" width="48" height="10" uuid="bb04a8fc-53cc-493f-a6e1-ae5e592f28e7"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorTotal}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="700" y="5" width="48" height="10" uuid="dda5e543-0986-46c6-87f1-88af40925ae2"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorPagoTotal}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="FA" isReprintHeaderOnEachPage="true" keepTogether="true">
		<groupExpression><![CDATA[FormaApresentacao.TIPO_SOLICITACAO.equals($P{FORMA_APRESENTACAO})
?
   ( $F{requerimentoVigilancia}  != null ?  $F{requerimentoVigilancia}.getTipoSolicitacao().getDescricao() : "Autos")
:
    FormaApresentacao.TIPO_DOCUMENTO.equals($P{FORMA_APRESENTACAO})
    ?
        $F{tipoDocumento}
    :
        null]]></groupExpression>
		<groupHeader>
			<band height="16" splitType="Stretch">
				<printWhenExpression><![CDATA[!FormaApresentacao.GERAL.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
				<textField evaluationTime="Group" evaluationGroup="FA" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-1" x="0" y="0" width="802" height="16" uuid="b9688033-a55f-4fb7-894e-a2be7b80d264"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="12" isBold="true" isItalic="false" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[FormaApresentacao.TIPO_SOLICITACAO.equals($P{FORMA_APRESENTACAO})
?
   ( $F{requerimentoVigilancia} != null && $F{requerimentoVigilancia}.getTipoSolicitacao() != null  && $F{requerimentoVigilancia}.getTipoSolicitacao().getDescricao() != null ? Bundle.getStringApplication("rotulo_tipo_solicitacao") + ": " + $F{requerimentoVigilancia}.getTipoSolicitacao().getDescricao() : Bundle.getStringApplication("rotulo_tipo_solicitacao") + ": Autos")
:
    FormaApresentacao.TIPO_DOCUMENTO.equals($P{FORMA_APRESENTACAO})
    ?
        Bundle.getStringApplication("rotulo_tipo_documento") + ": " + $F{tipoDocumento}
    :
        null]]></textFieldExpression>
				</textField>
			</band>
			<band height="20">
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="260" y="6" width="175" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="d6cac8f7-7f34-49d5-914a-98848eeb1a3c"/>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Estabelecimento/Pessoa/Profissional*/$V{BUNDLE}.getStringApplication("estabelecimentoPessoaProfissional")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="535" y="6" width="110" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="f59c8e79-1f15-4f8e-8d62-6f6db6cd2e33"/>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Isenção*/
$V{BUNDLE}.getStringApplication("rotulo_isencao")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="438" y="6" width="95" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="0117d60b-d305-4cd0-9f85-d8e80d7b2f26"/>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Situacao*/
$V{BUNDLE}.getStringApplication("rotulo_situacao")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="163" y="6" width="45" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="d4c9cb88-526c-42ba-8917-25ceffbb071f"/>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Emissao*/
$V{BUNDLE}.getStringApplication("rotulo_emissao")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="18" width="802" height="1" uuid="aea060cb-9f73-42da-a184-43e78ff6256f"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="1" y="6" width="45" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="3ea4f1b6-c206-4c22-b6a5-4d66527fee6d"/>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Protocolo*/
$V{BUNDLE}.getStringApplication("rotulo_protocolo")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="49" y="6" width="110" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="d02a99be-5901-4823-8c74-e8293c5365b5"/>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Tipo de Documento*/
$V{BUNDLE}.getStringApplication("rotulo_tipo_documento")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="209" y="6" width="47" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="1841e909-8769-424e-a9ff-307920c47c79"/>
					<textElement textAlignment="Justified" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Vencimento*/
$V{BUNDLE}.getStringApplication("rotulo_vencimento")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="648" y="6" width="48" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="27272280-fb70-459d-872d-43cdf2fc60c7"/>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Valor*/
$V{BUNDLE}.getStringApplication("rotulo_valor")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="700" y="6" width="48" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="269338a6-9bd9-48b0-b5a6-70e087423c71"/>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Valor Pago*/
$V{BUNDLE}.getStringApplication("rotulo_valor_pago")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="752" y="6" width="48" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="a0608499-9ad9-494f-8dc6-a01cba16b412"/>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Saldo*/
$V{BUNDLE}.getStringApplication("rotulo_saldo")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="12">
				<printWhenExpression><![CDATA[!FormaApresentacao.GERAL.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
				<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="648" y="2" width="48" height="10" uuid="7db7c25a-0afa-4ea3-8129-fbd9af995287"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorTotalFA}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="700" y="2" width="48" height="10" uuid="778852e9-54c3-4568-b627-2f0e7b78aeb1"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorPagoTotalFA}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="752" y="2" width="48" height="10" uuid="5a867a2b-2852-4079-a792-517b3b13f093"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{saldoTotalFA}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="616" y="0" width="186" height="1" uuid="84e7905a-88f1-4a70-874a-62c470670539"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="616" y="2" width="31" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="e6c0df76-9705-4080-9fb9-94d881430438"/>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*TOTAL: */
$V{BUNDLE}.getStringApplication("rotulo_total").toUpperCase() + ": "]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="802" height="12" backcolor="#DFDFDF" uuid="d6ff6062-6887-4367-80e0-73e4ab7202f4">
					<printWhenExpression><![CDATA[$V{COLUMN_COUNT} % 2 == 0]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="535" y="1" width="110" height="10" uuid="ac56f193-1921-43c6-937a-1987d89908a8"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia.Isencao.MEI.value().equals($F{requerimentoVigilancia}.getFlagIsentoMei())
    ? Bundle.getStringApplication("rotulo_mei")
    : $F{requerimentoVigilancia}.getDescricaoIsentoOutro()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="438" y="1" width="95" height="10" uuid="0566a3d1-65d1-41cb-82da-718ef45d44e6"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{vigilanciaFinanceiro}.getDescricaoSituacao()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="163" y="1" width="45" height="10" uuid="9483885e-0903-43e3-a4f2-ab11c0a9c867"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{vigilanciaFinanceiro}.getDataEmissao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="260" y="1" width="175" height="10" uuid="4e5f86a4-3c18-4686-9e74-e135c6665185"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{requerimentoVigilancia}.getNome()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="1" y="1" width="45" height="10" uuid="5be38b4b-01f5-473d-bd64-dfbac2e324fb"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{protocolo}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="49" y="1" width="110" height="10" uuid="76cd9be3-d35c-4f79-95a7-8e0ea627da73"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tipoDocumento}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="209" y="1" width="47" height="10" uuid="20dac1e1-89d6-4b47-b369-1743ceaf734d"/>
				<box leftPadding="3"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{vigilanciaFinanceiro}.getDataVencimento()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="648" y="1" width="48" height="10" uuid="1967a54b-b099-4a7f-9dda-b5bbe2178fa7"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{valorTotal}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="700" y="1" width="48" height="10" uuid="06a9be95-f1ad-400d-9d37-bc6dc75db8fb"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{valorPago}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="752" y="1" width="48" height="10" uuid="3731d237-ae94-4673-8b0f-285a4a3980da"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{saldo}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
