<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_exame_anti_hcv" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="812a5b70-40d5-4117-bc4f-81ca6e8acef4">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.5026296018031708"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="br.com.celk.bo.hospital.endereco.EnderecoHelper"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.FormasUsoHelper"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao"/>
	<import value="br.com.celk.util.DataUtil"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="motivo" class="br.com.ksisolucoes.vo.prontuario.basico.RequisicaoAntiHcv.Motivo"/>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus">
		<fieldDescription><![CDATA[exameRequisicao.exame.usuarioCadsus]]></fieldDescription>
	</field>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional">
		<fieldDescription><![CDATA[exameRequisicao.exame.profissional]]></fieldDescription>
	</field>
	<field name="exameRequisicao" class="br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao"/>
	<field name="requisicaoAntiHcv" class="br.com.ksisolucoes.vo.prontuario.basico.RequisicaoAntiHcv"/>
	<field name="enderecoUsuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus"/>
	<field name="empresaSolicitante" class="br.com.ksisolucoes.vo.basico.Empresa">
		<fieldDescription><![CDATA[exameRequisicao.exame.empresaSolicitante]]></fieldDescription>
	</field>
	<variable name="somaMotivo" class="java.util.List">
		<variableExpression><![CDATA[Valor.resolveSomatorio($F{requisicaoAntiHcv}.getSomaMotivo())]]></variableExpression>
	</variable>
	<group name="Exame" isStartNewPage="true">
		<groupExpression><![CDATA[$F{exameRequisicao}]]></groupExpression>
		<groupHeader>
			<band height="222">
				<textField isBlankWhenNull="true">
					<reportElement key="textField-10" positionType="Float" x="374" y="89" width="80" height="12" uuid="b0f68a33-29f8-458f-8e72-d561ff3fc2c5">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getSexoFormatado()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-8" x="288" y="89" width="80" height="12" uuid="78186ffb-8f76-4b97-89a8-300d1b379d81">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[Data.formatar($F{usuarioCadsus}.getDataNascimento())]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="459" y="89" width="94" height="12" uuid="58705b44-3760-4472-b2ae-2f4531da483f"/>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getRaca().getDescricao()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="2" y="104" width="551" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nome_mae2").toUpperCase()]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="2" y="118" width="551" height="12" uuid="58705b44-3760-4472-b2ae-2f4531da483f"/>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNomeMae()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="2" y="134" width="551" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_endereco").toUpperCase() + " " + Bundle.getStringApplication("rotulo_rua_numero_bairro")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="2" y="148" width="551" height="12" uuid="58705b44-3760-4472-b2ae-2f4531da483f"/>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{enderecoUsuarioCadsus}.getEnderecoFormatado()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="2" y="178" width="452" height="12" uuid="58705b44-3760-4472-b2ae-2f4531da483f"/>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{enderecoUsuarioCadsus}.getCidade().getDescricao()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="2" y="164" width="452" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_municipio_residencia").toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="459" y="178" width="25" height="12" uuid="58705b44-3760-4472-b2ae-2f4531da483f"/>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{enderecoUsuarioCadsus}.getCidade().getEstado().getSigla()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="459" y="164" width="25" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_uf").toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="489" y="164" width="64" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cep").toUpperCase()]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="489" y="178" width="64" height="12" uuid="58705b44-3760-4472-b2ae-2f4531da483f"/>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{enderecoUsuarioCadsus}.getCepFormatado()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="154" y="194" width="399" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_numero_notificacao").toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="154" y="208" width="399" height="12" uuid="58705b44-3760-4472-b2ae-2f4531da483f"/>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{requisicaoAntiHcv}.getNumeroNotificacao()]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="2" y="30" width="452" height="12" uuid="58705b44-3760-4472-b2ae-2f4531da483f"/>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNome() + " (" + $F{usuarioCadsus}.getCodigo() + ")"]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="370" y="74" width="1" height="29" uuid="e57a2bc0-a544-43bf-a524-bb95eed0e8d2"/>
				</line>
				<textField>
					<reportElement x="459" y="59" width="94" height="12" uuid="58705b44-3760-4472-b2ae-2f4531da483f"/>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getTelefoneFormatado()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="459" y="45" width="94" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_telefone").toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-9" mode="Opaque" x="-1" y="0" width="557" height="15" backcolor="#F6F4F2" uuid="541d9f2e-4a88-4ae1-a4ea-96489b8b6c04"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
						<pen lineWidth="1.0"/>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dados_paciente").toUpperCase()]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="555" y="0" width="1" height="222" uuid="4d1dc53e-3bde-4bcf-a1a9-103c9ca97f00"/>
				</line>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="2" y="16" width="452" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nome_paciente").toUpperCase()]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="-1" y="0" width="1" height="222" uuid="e57a2bc0-a544-43bf-a524-bb95eed0e8d2"/>
				</line>
				<line>
					<reportElement x="-1" y="73" width="557" height="1" uuid="9228121a-ceb0-4907-8533-7786499316cb"/>
				</line>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="2" y="75" width="280" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cartao_nacional_de_saude_cns").toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="288" y="75" width="80" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_nasc").toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="374" y="75" width="80" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_sexo").toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="459" y="75" width="94" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_raca").toUpperCase()]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="456" y="44" width="1" height="59" uuid="e57a2bc0-a544-43bf-a524-bb95eed0e8d2"/>
				</line>
				<line>
					<reportElement x="285" y="74" width="1" height="29" uuid="e57a2bc0-a544-43bf-a524-bb95eed0e8d2"/>
				</line>
				<line>
					<reportElement x="-1" y="103" width="557" height="1" uuid="9228121a-ceb0-4907-8533-7786499316cb"/>
				</line>
				<line>
					<reportElement x="-1" y="132" width="557" height="1" uuid="9228121a-ceb0-4907-8533-7786499316cb"/>
				</line>
				<line>
					<reportElement x="-1" y="162" width="557" height="1" uuid="9228121a-ceb0-4907-8533-7786499316cb"/>
				</line>
				<line>
					<reportElement x="486" y="162" width="1" height="31" uuid="e57a2bc0-a544-43bf-a524-bb95eed0e8d2"/>
				</line>
				<line>
					<reportElement x="456" y="162" width="1" height="31" uuid="e57a2bc0-a544-43bf-a524-bb95eed0e8d2"/>
				</line>
				<line>
					<reportElement x="-1" y="192" width="557" height="1" uuid="9228121a-ceb0-4907-8533-7786499316cb"/>
				</line>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-10" positionType="Float" x="2" y="89" width="280" height="12" uuid="b0f68a33-29f8-458f-8e72-d561ff3fc2c5">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getCns()]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="151" y="193" width="1" height="29" uuid="0f682501-6f91-4a41-bf1e-4d04050bd343"/>
				</line>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="2" y="194" width="143" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_codigo_ibge_municipio").toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="2" y="208" width="143" height="12" uuid="58705b44-3760-4472-b2ae-2f4531da483f"/>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[EnderecoHelper.getCodigoIbgeComDV($F{enderecoUsuarioCadsus}.getCidade().getCodigo())]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="43" width="556" height="1" uuid="198a0804-4d7a-4c5b-ab8d-1932d4ca14ba"/>
				</line>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="2" y="45" width="452" height="12" uuid="4ddd51af-4289-4c97-9dff-986003874fd5">
						<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nome_social").toUpperCase()]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="2" y="59" width="452" height="12" uuid="1eb12dd6-ec08-4d68-96bd-04a1ee534980"/>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{usuarioCadsus}.getDescricaoApelido()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="78" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" mode="Opaque" x="-1" y="35" width="557" height="15" backcolor="#F6F4F2" uuid="541d9f2e-4a88-4ae1-a4ea-96489b8b6c04"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_identificacao_estabelecimento_saude_solicitante").toUpperCase()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="-1" y="1" width="1" height="77" uuid="e57a2bc0-a544-43bf-a524-bb95eed0e8d2"/>
			</line>
			<line>
				<reportElement x="555" y="1" width="1" height="77" uuid="4d1dc53e-3bde-4bcf-a1a9-103c9ca97f00"/>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="2" y="51" width="180" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_municipio").toUpperCase()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="184" y="50" width="1" height="28" uuid="e57a2bc0-a544-43bf-a524-bb95eed0e8d2"/>
			</line>
			<line>
				<reportElement x="370" y="50" width="1" height="28" uuid="e57a2bc0-a544-43bf-a524-bb95eed0e8d2"/>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="187" y="51" width="180" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_estabelecimento_saude").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="373" y="51" width="180" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cnes").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="187" y="65" width="180" height="12" uuid="58705b44-3760-4472-b2ae-2f4531da483f"/>
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresaSolicitante}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2" y="65" width="180" height="12" uuid="58705b44-3760-4472-b2ae-2f4531da483f"/>
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresaSolicitante}.getCidade().getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="373" y="65" width="180" height="12" uuid="58705b44-3760-4472-b2ae-2f4531da483f"/>
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresaSolicitante}.getCnes()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="-1" y="1" width="557" height="17" uuid="1bc021e6-7fb3-46f5-a828-b953196fef76"/>
				<box topPadding="1" leftPadding="0" rightPadding="0">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[LAUDO MÉDICO PARA EMISSÃO DO BPA-I]]></text>
			</staticText>
			<staticText>
				<reportElement x="-1" y="17" width="557" height="16" uuid="1bc021e6-7fb3-46f5-a828-b953196fef76"/>
				<box topPadding="1" leftPadding="0" rightPadding="0">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Pesquisa de anticorpos contra o Vírus da Hepatite C (Anti-HCV)]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="368" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" mode="Opaque" x="-1" y="176" width="557" height="15" backcolor="#F6F4F2" uuid="541d9f2e-4a88-4ae1-a4ea-96489b8b6c04"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_solicitacao").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" mode="Opaque" x="-1" y="53" width="557" height="15" backcolor="#F6F4F2" uuid="541d9f2e-4a88-4ae1-a4ea-96489b8b6c04"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_justificativa_procedimento").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="14" y="147" width="10" height="10" uuid="74579f1f-5497-4a2e-94de-39e629e8874e"/>
				<box>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{somaMotivo}.contains($P{motivo}.EXPOSICAO_PERCUTANEA_MUCOSA.value()) ? "X" : ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="27" y="147" width="220" height="10" uuid="5e97fe9a-538c-41ac-8325-d6fed07d1b58"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_exposicao_percutanea_mucosa")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="14" y="117" width="10" height="10" uuid="74579f1f-5497-4a2e-94de-39e629e8874e"/>
				<box>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{somaMotivo}.contains($P{motivo}.CONTATO_DOMICILIAR.value()) ? "X" : ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="27" y="117" width="220" height="10" uuid="5e97fe9a-538c-41ac-8325-d6fed07d1b58"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_contato_domiciliar")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="14" y="162" width="10" height="10" uuid="74579f1f-5497-4a2e-94de-39e629e8874e"/>
				<box>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{somaMotivo}.contains($P{motivo}.CONTATO_SEXUAL.value()) ? "X" : ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="27" y="162" width="220" height="10" uuid="5e97fe9a-538c-41ac-8325-d6fed07d1b58"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_contato_sexual")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="14" y="132" width="10" height="10" uuid="74579f1f-5497-4a2e-94de-39e629e8874e"/>
				<box>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{somaMotivo}.contains($P{motivo}.USUARIO_DROGAS_INJETAVEIS.value()) ? "X" : ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="27" y="132" width="220" height="10" uuid="5e97fe9a-538c-41ac-8325-d6fed07d1b58"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_usuario_drogas_injetaveis_udi")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="257" y="117" width="10" height="10" uuid="74579f1f-5497-4a2e-94de-39e629e8874e"/>
				<box>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{somaMotivo}.contains($P{motivo}.TRANSMISSAO_VERTICAL.value()) ? "X" : ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="270" y="117" width="245" height="10" uuid="5e97fe9a-538c-41ac-8325-d6fed07d1b58"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_transmissao_vertical")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="257" y="132" width="10" height="10" uuid="74579f1f-5497-4a2e-94de-39e629e8874e"/>
				<box>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{somaMotivo}.contains($P{motivo}.DOADOR_ORGAOS.value()) ? "X" : ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="270" y="132" width="101" height="10" uuid="5e97fe9a-538c-41ac-8325-d6fed07d1b58"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_doador_orgaos")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" mode="Opaque" x="-1" y="0" width="557" height="15" backcolor="#F6F4F2" uuid="541d9f2e-4a88-4ae1-a4ea-96489b8b6c04"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dados_solicitacao").toUpperCase()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="555" y="0" width="1" height="346" uuid="4d1dc53e-3bde-4bcf-a1a9-103c9ca97f00"/>
			</line>
			<line>
				<reportElement x="-1" y="0" width="1" height="346" uuid="4d1dc53e-3bde-4bcf-a1a9-103c9ca97f00"/>
			</line>
			<line>
				<reportElement x="275" y="191" width="1" height="72" uuid="e57a2bc0-a544-43bf-a524-bb95eed0e8d2"/>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="2" y="192" width="270" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nome_do_profissional_solicitante").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="278" y="193" width="275" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_assinatura_e_carimbo").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="2" y="234" width="270" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_telefone").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2" y="207" width="270" height="12" uuid="58705b44-3760-4472-b2ae-2f4531da483f"/>
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{profissional}.getNome()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="232" width="275" height="1" uuid="e57a2bc0-a544-43bf-a524-bb95eed0e8d2"/>
			</line>
			<textField>
				<reportElement x="2" y="249" width="270" height="12" uuid="58705b44-3760-4472-b2ae-2f4531da483f"/>
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresaSolicitante}.getTelefoneFormatado()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="86" y="266" width="100" height="15" uuid="deabfc8d-a751-43a7-b669-0e835dc4c479"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[___ / ___ / ______]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="2" y="270" width="84" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_coleta").toUpperCase()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="345" width="555" height="1" uuid="9228121a-ceb0-4907-8533-7786499316cb"/>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement x="2" y="219" width="270" height="12" isRemoveLineWhenBlank="true" uuid="5b55b2ae-4ec5-49f1-af6e-a612d9f7ca53"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{profissional}.getRegistroFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="2" y="16" width="140" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_codigo_procedimento").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="2" y="69" width="115" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_hipotese_diagnostica").toUpperCase() + ":"]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="275" y="285" width="1" height="60" uuid="e57a2bc0-a544-43bf-a524-bb95eed0e8d2"/>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="278" y="287" width="266" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_assinatura_e_carimbo").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="14" y="32" width="107" height="14" uuid="5e97fe9a-538c-41ac-8325-d6fed07d1b58"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true" isUnderline="false"/>
				</textElement>
				<textFieldExpression><![CDATA["020203067-9"]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="166" y="15" width="1" height="38" uuid="51a892a1-ee45-4601-9790-c2352e20f637"/>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="169" y="16" width="140" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_procedimento").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="182" y="32" width="362" height="14" uuid="5e97fe9a-538c-41ac-8325-d6fed07d1b58"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true" isUnderline="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_pesquisa_anticorpos_contra_virus_hepatite_c")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="2" y="101" width="329" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_motivo_condicao_clinica").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="119" y="69" width="425" height="12" uuid="58705b44-3760-4472-b2ae-2f4531da483f"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{requisicaoAntiHcv}.getHipoteseDiagnostica()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="2" y="83" width="33" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cid10").toUpperCase() + ":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="36" y="83" width="508" height="12" uuid="58705b44-3760-4472-b2ae-2f4531da483f"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{requisicaoAntiHcv}.getCid().getDescricaoFormatado()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="99" width="555" height="1" uuid="9228121a-ceb0-4907-8533-7786499316cb"/>
			</line>
			<textField>
				<reportElement x="257" y="147" width="10" height="10" uuid="74579f1f-5497-4a2e-94de-39e629e8874e"/>
				<box>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.75"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{somaMotivo}.contains($P{motivo}.PACIENTES_HEMODIALISE.value()) ? "X" : ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="270" y="147" width="101" height="10" uuid="5e97fe9a-538c-41ac-8325-d6fed07d1b58"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_pacientes_hemodialise")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="263" width="555" height="1" uuid="9228121a-ceb0-4907-8533-7786499316cb"/>
			</line>
			<line>
				<reportElement x="0" y="284" width="555" height="1" uuid="9228121a-ceb0-4907-8533-7786499316cb"/>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="2" y="307" width="104" height="14" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_autorizacao").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="278" y="300" width="266" height="9" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["(" + Bundle.getStringApplication("rotulo_campo_reservado_lacen") + ")"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="-1" y="347" width="557" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33">
					<printWhenExpression><![CDATA[$F{usuarioCadsus}.getCodigo() != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_bpai_boletim_producao_ambulatorial_individualizado")]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
