<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_grupo_produto_estoque_empresa" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="4e962ba5-3f8a-47bb-b878-b580804ea815">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.8531167061100073"/>
	<property name="ireport.x" value="431"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault.TipoPreco"/>
	<import value="br.com.ksisolucoes.report.ReportProperties"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="ParamEstoqueFisico" class="java.lang.String" isForPrompting="false"/>
	<parameter name="FlagEstoqueNaoConforme" class="java.lang.String" isForPrompting="false"/>
	<parameter name="ListarPreco" class="java.lang.String" isForPrompting="false"/>
	<parameter name="TotalizarEstoque" class="java.lang.String" isForPrompting="false"/>
	<parameter name="tipoEstoque" class="java.lang.String" isForPrompting="false"/>
	<parameter name="ExibirLote" class="java.lang.String"/>
	<parameter name="utilizaLocalizacaoEstoque" class="java.lang.Boolean"/>
	<parameter name="AGRUPAR_EMPRESA" class="java.lang.String"/>
	<parameter name="tipoPreco" class="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault.TipoPreco"/>
	<field name="subGrupoDescricao" class="java.lang.String">
		<fieldDescription><![CDATA[subGrupoDescricao]]></fieldDescription>
	</field>
	<field name="quantidadeMultipla" class="java.lang.Double">
		<fieldDescription><![CDATA[quantidadeMultipla]]></fieldDescription>
	</field>
	<field name="produtoDescricaoFormatado" class="java.lang.String">
		<fieldDescription><![CDATA[produtoDescricaoFormatado]]></fieldDescription>
	</field>
	<field name="estoqueFisico" class="java.lang.Double">
		<fieldDescription><![CDATA[estoqueFisico]]></fieldDescription>
	</field>
	<field name="unidadeUnidade" class="java.lang.String">
		<fieldDescription><![CDATA[unidadeUnidade]]></fieldDescription>
	</field>
	<field name="totalPreco" class="java.lang.Double">
		<fieldDescription><![CDATA[totalPreco]]></fieldDescription>
	</field>
	<field name="grupoCodigo" class="java.lang.Long">
		<fieldDescription><![CDATA[grupoCodigo]]></fieldDescription>
	</field>
	<field name="produtoDescricao" class="java.lang.String">
		<fieldDescription><![CDATA[produtoDescricao]]></fieldDescription>
	</field>
	<field name="grupoDescricao" class="java.lang.String">
		<fieldDescription><![CDATA[grupoDescricao]]></fieldDescription>
	</field>
	<field name="subGrupoCodigo" class="java.lang.Long">
		<fieldDescription><![CDATA[subGrupoCodigo]]></fieldDescription>
	</field>
	<field name="empresaDescricaoFormatado" class="java.lang.String">
		<fieldDescription><![CDATA[empresaDescricaoFormatado]]></fieldDescription>
	</field>
	<field name="estoqueMinimo" class="java.lang.Double">
		<fieldDescription><![CDATA[estoqueMinimo]]></fieldDescription>
	</field>
	<field name="preco" class="java.lang.Double">
		<fieldDescription><![CDATA[preco]]></fieldDescription>
	</field>
	<field name="estoqueNaoConforme" class="java.lang.Double">
		<fieldDescription><![CDATA[estoqueNaoConforme]]></fieldDescription>
	</field>
	<field name="subGrupoDescricaoFormatado" class="java.lang.String">
		<fieldDescription><![CDATA[subGrupoDescricaoFormatado]]></fieldDescription>
	</field>
	<field name="produtoCodigo" class="java.lang.String">
		<fieldDescription><![CDATA[produtoCodigo]]></fieldDescription>
	</field>
	<field name="quantidadeIdeal" class="java.lang.Double">
		<fieldDescription><![CDATA[quantidadeIdeal]]></fieldDescription>
	</field>
	<field name="empresaDescricao" class="java.lang.String">
		<fieldDescription><![CDATA[empresaDescricao]]></fieldDescription>
	</field>
	<field name="empresaCodigo" class="java.lang.String">
		<fieldDescription><![CDATA[empresaCodigo]]></fieldDescription>
	</field>
	<field name="grupoDescricaoFormatado" class="java.lang.String">
		<fieldDescription><![CDATA[grupoDescricaoFormatado]]></fieldDescription>
	</field>
	<field name="codigoDeposito" class="java.lang.Long"/>
	<field name="descricaoDeposito" class="java.lang.String"/>
	<field name="descricaoDepositoFormatado" class="java.lang.String"/>
	<field name="grupoEstoque" class="java.lang.String"/>
	<field name="dataValidade" class="java.util.Date"/>
	<field name="mascaraLocalizacaoEstrutura" class="java.lang.String"/>
	<field name="dataProximaValidade" class="java.lang.Boolean">
		<fieldDescription><![CDATA[dataProximaValidade]]></fieldDescription>
	</field>
	<field name="dataVencido" class="java.lang.Boolean">
		<fieldDescription><![CDATA[dataVencido]]></fieldDescription>
	</field>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="preco" class="java.lang.Double">
		<variableExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{AGRUPAR_EMPRESA})
? ($F{preco} == null ? 0.00 : $F{preco})
: ($F{estoqueFisico} == null ? 0.00 : ($F{totalPreco} / $F{estoqueFisico}))]]></variableExpression>
	</variable>
	<variable name="somaTotalPrecoEmpresa" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[$F{totalPreco}]]></variableExpression>
	</variable>
	<variable name="somaTotalPrecoSubGrupo" class="java.lang.Double" resetType="Group" resetGroup="GrupoSubGrupo" calculation="Sum">
		<variableExpression><![CDATA[$F{totalPreco}]]></variableExpression>
	</variable>
	<variable name="somaTotalPrecoGrupo" class="java.lang.Double" resetType="Group" resetGroup="GrupoGrupo" calculation="Sum">
		<variableExpression><![CDATA[$F{totalPreco}]]></variableExpression>
	</variable>
	<variable name="somaTotalPrecoGeral" class="java.lang.Double" resetType="Group" resetGroup="GrupoPrincipal" calculation="Sum">
		<variableExpression><![CDATA[$F{totalPreco}]]></variableExpression>
	</variable>
	<variable name="somaEstoqueFisicoEmpresa" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueFisico}]]></variableExpression>
	</variable>
	<variable name="somaEstoqueFisicoGeral" class="java.lang.Double" resetType="Group" resetGroup="GrupoPrincipal" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueFisico}]]></variableExpression>
	</variable>
	<variable name="somaEstoqueFisicoGrupo" class="java.lang.Double" resetType="Group" resetGroup="GrupoGrupo" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueFisico}]]></variableExpression>
	</variable>
	<variable name="somaEstoqueFisicoSubGrupo" class="java.lang.Double" resetType="Group" resetGroup="GrupoSubGrupo" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueFisico}]]></variableExpression>
	</variable>
	<variable name="somaEstoqueNaoConformeSubGrupo" class="java.lang.Double" resetType="Group" resetGroup="GrupoGrupo" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueNaoConforme}]]></variableExpression>
	</variable>
	<variable name="somaEstoqueNaoConformeGrupo" class="java.lang.Double" resetType="Group" resetGroup="GrupoGrupo" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueNaoConforme}]]></variableExpression>
	</variable>
	<variable name="somaEstoqueNaoConformeEmpresa" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueNaoConforme}]]></variableExpression>
	</variable>
	<variable name="somaEstoqueNaoConformeGeral" class="java.lang.Double" resetType="Group" resetGroup="GrupoPrincipal" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueNaoConforme}]]></variableExpression>
	</variable>
	<variable name="somaEstoqueMinimoSubGrupo" class="java.lang.Double" resetType="Group" resetGroup="GrupoSubGrupo" calculation="Sum">
		<variableExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ExibirLote})
?
$V{estoqueMinimo}
:
$F{estoqueMinimo}]]></variableExpression>
	</variable>
	<variable name="somaEstoqueMinimoGrupo" class="java.lang.Double" resetType="Group" resetGroup="GrupoGrupo" calculation="Sum">
		<variableExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ExibirLote})
?
$V{estoqueMinimo}
:
$F{estoqueMinimo}]]></variableExpression>
	</variable>
	<variable name="somaEstoqueMinimoGeral" class="java.lang.Double" resetType="Group" resetGroup="GrupoPrincipal" calculation="Sum">
		<variableExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ExibirLote})
?
$V{estoqueMinimo}
:
$F{estoqueMinimo}]]></variableExpression>
	</variable>
	<variable name="somaEstoqueMinimoEmpresa" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ExibirLote})
?
$V{estoqueMinimo}
:
$F{estoqueMinimo}]]></variableExpression>
	</variable>
	<variable name="somaTotalQtdIdealEmpresa" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidadeIdeal}]]></variableExpression>
	</variable>
	<variable name="somaTotalQtdIdealSubGrupo" class="java.lang.Double" resetType="Group" resetGroup="GrupoSubGrupo" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidadeIdeal}]]></variableExpression>
	</variable>
	<variable name="somaTotalQtdIdealGrupo" class="java.lang.Double" resetType="Group" resetGroup="GrupoGrupo" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidadeIdeal}]]></variableExpression>
	</variable>
	<variable name="somaTotalQtdIdealGeral" class="java.lang.Double" resetType="Group" resetGroup="GrupoPrincipal" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidadeIdeal}]]></variableExpression>
	</variable>
	<variable name="somaTotalQtdMultGeral" class="java.lang.Double" resetType="Group" resetGroup="GrupoPrincipal" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidadeMultipla}]]></variableExpression>
	</variable>
	<variable name="somaTotalQtdMultGrupo" class="java.lang.Double" resetType="Group" resetGroup="GrupoGrupo" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidadeMultipla}]]></variableExpression>
	</variable>
	<variable name="somaTotalQtdMultSubGrupo" class="java.lang.Double" resetType="Group" resetGroup="GrupoSubGrupo" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidadeMultipla}]]></variableExpression>
	</variable>
	<variable name="somaTotalQtdMultEmpresa" class="java.lang.Double" resetType="Group" resetGroup="GrupoEmpresa" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidadeMultipla}]]></variableExpression>
	</variable>
	<variable name="estoqueFisico" class="java.lang.Double" resetType="Group" resetGroup="Produto" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueFisico} == null ? 0.00 :$F{estoqueFisico}]]></variableExpression>
	</variable>
	<variable name="estoqueMinimo" class="java.lang.Double" resetType="Group" resetGroup="Produto" calculation="Sum">
		<variableExpression><![CDATA[$F{estoqueMinimo} == null? 0.00 :$F{estoqueMinimo}]]></variableExpression>
	</variable>
	<variable name="totalPrecoProduto" class="java.lang.Double" resetType="Group" resetGroup="Produto" calculation="Sum">
		<variableExpression><![CDATA[$F{totalPreco}]]></variableExpression>
	</variable>
	<group name="GrupoPrincipal">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band splitType="Stretch"/>
		</groupHeader>
		<groupFooter>
			<band height="12" splitType="Stretch">
				<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-55" mode="Transparent" x="482" y="1" width="46" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="dd0e7263-0d8c-49eb-9bb9-e5579e1b8ecf">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{ListarPreco} )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{somaTotalPrecoGeral}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-63" mode="Transparent" x="221" y="1" width="117" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="6eae404a-d1c8-4ea8-a7f3-efb0ea5a0fff"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication( "rotulo_total_geral" ) + " =>"]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-67" mode="Transparent" x="340" y="1" width="50" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="cb3995fb-152c-40cc-90d9-053de2e5ed14">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{somaEstoqueFisicoGeral}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-11" mode="Opaque" x="393" y="1" width="46" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="*************-4451-bd4a-ea605495daa9">
						<printWhenExpression><![CDATA[( (!$P{FlagEstoqueNaoConforme}.equals(ReportProperties.NAO_VISUALIZAR) )&&
(RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} ) ) )]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<line>
					<reportElement key="line-12" mode="Opaque" x="482" y="1" width="46" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="379f102e-48ca-42fd-8ad1-92b0222c02fa">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{ListarPreco} )]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-74" mode="Transparent" x="394" y="1" width="44" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="06983f8c-d983-449e-b584-77a049a4af0f">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} ) &&
RepositoryComponentDefault.NAO.equals($P{ListarPreco})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{somaEstoqueMinimoGeral}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-13" mode="Opaque" x="340" y="1" width="51" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="97d16bf6-ffd3-44f8-9de6-3f11097b25e8">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} )]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="GrupoEmpresa" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{empresaCodigo}]]></groupExpression>
		<groupHeader>
			<band height="18" splitType="Stretch">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{AGRUPAR_EMPRESA})]]></printWhenExpression>
				<rectangle radius="10">
					<reportElement x="0" y="1" width="535" height="16" uuid="f5c1eeba-5bd4-406f-be97-cb312c54e31e"/>
				</rectangle>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-43" mode="Transparent" x="6" y="2" width="522" height="14" forecolor="#000000" backcolor="#CCCCCC" uuid="aa3bd244-2401-4415-a0eb-5d3a21ec9f45"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*empresa*/
$F{empresaCodigo}!=null
?
    $V{BUNDLE}.getStringApplication("rotulo_empresa") + ":" + " " + $F{empresaDescricao} + " (" + $F{empresaCodigo}.trim() + ") "
:
    $V{BUNDLE}.getStringApplication("rotulo_empresa") + ":" +$V{BUNDLE}.getStringApplication("rotulo_sem_cadastro")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="12" splitType="Stretch">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{AGRUPAR_EMPRESA})]]></printWhenExpression>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-54" mode="Transparent" x="482" y="2" width="46" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="5859a1ea-33c1-4090-9720-e02358e2cbe9">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{ListarPreco} )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{somaTotalPrecoEmpresa}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-62" mode="Transparent" x="221" y="2" width="117" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="76ed3494-a654-41b3-9701-1cf3f8d12a52"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication( "rotulo_total_por_empresa" ) + " =>"]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-66" mode="Transparent" x="340" y="2" width="50" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="07dc4c44-f29a-4570-a226-81adad4cfce1">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{somaEstoqueFisicoEmpresa}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-8" mode="Opaque" x="393" y="2" width="46" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="6447ec91-7e7c-42a9-b81f-0dd6a452ce4e">
						<printWhenExpression><![CDATA[( (!$P{FlagEstoqueNaoConforme}.equals(ReportProperties.NAO_VISUALIZAR) )&&
(RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} ) ) )]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<line>
					<reportElement key="line-9" mode="Opaque" x="482" y="2" width="46" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="e1f50d0e-9578-4917-991e-003a2cf72dee">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{ListarPreco} )]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<textField evaluationTime="Group" evaluationGroup="GrupoEmpresa" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-75" mode="Transparent" x="394" y="2" width="44" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="37fe9ad9-a70d-4364-b5b3-eacd4dee63ac">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} ) &&
RepositoryComponentDefault.NAO.equals($P{ListarPreco})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{somaEstoqueMinimoEmpresa}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-14" mode="Opaque" x="340" y="2" width="51" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="71a51700-eb68-4af1-bd48-990a7828ff29">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} )]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="Deposito" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{codigoDeposito}]]></groupExpression>
		<groupHeader>
			<band height="16">
				<textField evaluationTime="Group" evaluationGroup="Deposito" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-43" mode="Transparent" x="6" y="2" width="522" height="14" forecolor="#000000" backcolor="#CCCCCC" uuid="35ce20a2-cde1-4c2e-9b4b-529ad6f5a7ac"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_deposito")+": "+$F{descricaoDepositoFormatado}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<group name="GrupoGrupo" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{grupoCodigo}]]></groupExpression>
		<groupHeader>
			<band height="11" splitType="Stretch"/>
		</groupHeader>
		<groupFooter>
			<band height="12" splitType="Stretch">
				<textField evaluationTime="Group" evaluationGroup="GrupoGrupo" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-59" mode="Transparent" x="482" y="2" width="46" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="d6f68109-a154-4a5b-9b65-a48ba3f0b8e1">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{ListarPreco} )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{somaTotalPrecoGrupo}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-65" mode="Transparent" x="221" y="2" width="117" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="3e2fed1d-2ca4-4bc9-9142-cf99a9905bca"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication( "rotulo_total_por_grupo" ) + " =>"]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GrupoGrupo" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-69" mode="Transparent" x="340" y="2" width="50" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="1137ebfb-b118-431a-b285-c6c7ef9df295">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{somaEstoqueFisicoGrupo}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-5" mode="Opaque" x="393" y="2" width="46" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="0dec6092-4cd9-4551-8f39-9d5c25dd611e">
						<printWhenExpression><![CDATA[( (!$P{FlagEstoqueNaoConforme}.equals(ReportProperties.NAO_VISUALIZAR) )&&
(RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} ) ) )]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<line>
					<reportElement key="line-6" mode="Opaque" x="482" y="2" width="46" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="8df60d1b-fa91-4bb8-a950-da4ccbc20e1e">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{ListarPreco} )]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<textField evaluationTime="Group" evaluationGroup="GrupoGrupo" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-76" mode="Transparent" x="394" y="2" width="44" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="3444d35f-815c-45ec-8252-1c4f100a8b4d">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} )&&
RepositoryComponentDefault.NAO.equals($P{ListarPreco})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{somaEstoqueMinimoGrupo}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-15" mode="Opaque" x="340" y="2" width="51" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="eb301659-fa08-4087-b6e0-30947f16a163">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{TotalizarEstoque} )]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="GrupoSubGrupo" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{subGrupoCodigo}]]></groupExpression>
		<groupHeader>
			<band height="28" splitType="Stretch">
				<textField evaluationTime="Group" evaluationGroup="GrupoSubGrupo" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-43" mode="Transparent" x="12" y="0" width="516" height="14" forecolor="#000000" backcolor="#CCCCCC" uuid="583a18be-dea6-4ba2-b717-1f586b96cc28"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="true" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*grupo*/
$F{grupoCodigo} == null
?
    $V{BUNDLE}.getStringApplication("rotulo_grupo")  + ": " + $V{BUNDLE}.getStringApplication("rotulo_sem_cadastro") + " / " + $V{BUNDLE}.getStringApplication("rotulo_subgrupo") + ": " + $V{BUNDLE}.getStringApplication("rotulo_sem_cadastro")
:
    $F{subGrupoCodigo} == null
    ?
        $V{BUNDLE}.getStringApplication("rotulo_grupo") + ":" + " " + $F{grupoDescricaoFormatado}+" / "+$V{BUNDLE}.getStringApplication("rotulo_subgrupo") + ": " + $V{BUNDLE}.getStringApplication("rotulo_sem_cadastro")
    :
        $V{BUNDLE}.getStringApplication("rotulo_grupo") + ":" + " " + $F{grupoDescricaoFormatado}+" / "+$V{BUNDLE}.getStringApplication("rotulo_subgrupo") + ":" + " " + $F{subGrupoDescricao}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-45" mode="Transparent" x="0" y="16" width="150" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="d71f58e7-338b-4b37-9a5f-f8a1cc4d2e71"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*produto*/$V{BUNDLE}.getStringApplication("rotulo_produto")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-46" mode="Transparent" x="325" y="16" width="14" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="68d160c6-fdb1-4beb-8f46-4ed61a0fc5df"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*unidade*/$V{BUNDLE}.getStringApplication("rotulo_unidade_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-47" mode="Transparent" x="340" y="16" width="50" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="74a4a9eb-e6e6-4da8-ac82-60532d28603d"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*est.Fisico*/$P{tipoEstoque}/*$V{BUNDLE}.getStringApplication("rotulo_estoque_fisico")*/]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-51" mode="Transparent" x="440" y="16" width="42" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="e5cbe1ba-4c77-4b22-9fc4-cdc1002de5db">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{ListarPreco} )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*preco medio/custo*/
$P{tipoPreco}.getDescricaoAbreviada()]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-52" mode="Transparent" x="482" y="16" width="46" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="c7569bd7-7aea-4a71-a24d-48bb76c1a0b0">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{ListarPreco} )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*totalMedio*/$V{BUNDLE}.getStringApplication("rotulo_total")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-79" mode="Transparent" x="394" y="16" width="44" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="a4b174fd-0bd4-4d5c-a250-9f5d5e1592d2">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{ListarPreco})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*estMinimo*/$V{BUNDLE}.getStringApplication("rotulo_estoque_minimo_abv")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="26" width="535" height="1" uuid="927f08c8-dffb-4b98-aee4-64624f4f39e5"/>
				</line>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-7" mode="Transparent" x="275" y="16" width="50" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="4ff005d6-d648-4824-9c86-5d533427bd15">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ExibirLote})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*dataValidade*/$V{BUNDLE}.getStringApplication("rotulo_data_validade_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-7" mode="Transparent" x="213" y="16" width="62" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="52780279-1e42-478a-b597-699ff92c862e">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ExibirLote})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*grupoEstoque*/$V{BUNDLE}.getStringApplication("rotulo_lote")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-7" mode="Transparent" x="159" y="16" width="49" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="52780279-1e42-478a-b597-699ff92c862e">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ExibirLote}) && $P{utilizaLocalizacaoEstoque}]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*localização*/$V{BUNDLE}.getStringApplication("rotulo_localizacao")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="Produto" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{produtoCodigo}]]></groupExpression>
		<groupFooter>
			<band height="10">
				<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{ExibirLote})]]></printWhenExpression>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-5" mode="Transparent" x="2" y="0" width="322" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="0ac00158-0c8a-48a6-b4ac-8bc0c13c7d73"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{produtoDescricaoFormatado}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-6" mode="Transparent" x="325" y="0" width="14" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="1bb4fe31-ce67-482f-9bdd-94888da175d8"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{unidadeUnidade}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-7" mode="Transparent" x="340" y="0" width="50" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="1cb1e86b-faa2-428f-b7be-67ee3f8fbc3a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{estoqueFisico}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.0000;-#,##0.0000" isBlankWhenNull="true">
					<reportElement key="textField-50" mode="Transparent" x="440" y="0" width="42" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="01e695d9-2125-453a-b28d-bf9f724cdbfb">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{ListarPreco} )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{preco}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-53" mode="Transparent" x="482" y="0" width="46" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="53508e9a-00b8-44a9-a2af-fd19cdb94e2f">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{ListarPreco} )]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalPrecoProduto}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-78" mode="Transparent" x="394" y="0" width="44" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="88a4193e-d33a-4a99-b455-6f2d4a941088">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{ListarPreco})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ExibirLote})
?
$V{estoqueMinimo}
:
$F{estoqueMinimo}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="10" splitType="Stretch">
			<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ExibirLote}) && !$P{utilizaLocalizacaoEstoque}]]></printWhenExpression>
			<rectangle>
				<reportElement mode="Opaque" x="3" y="0" width="779" height="10" isPrintWhenDetailOverflows="true" forecolor="#FEFEFE" backcolor="#C45958" uuid="b0dd9da1-df96-4a4f-8364-8e8a2139f4b1">
					<printWhenExpression><![CDATA[$F{dataVencido}]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Opaque" x="1" y="0" width="779" height="10" isPrintWhenDetailOverflows="true" forecolor="#FEFEFE" backcolor="#FDED8D" uuid="452fe5ff-60ee-450c-afe1-255a0fb4984f">
					<printWhenExpression><![CDATA[$F{dataProximaValidade}]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-5" mode="Transparent" x="2" y="0" width="210" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="e407fe8a-b3f3-4ae4-9e1f-824cca96d9f2"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{produtoDescricaoFormatado}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-6" mode="Transparent" x="325" y="0" width="14" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="3513eafa-7e8a-491c-9350-7fa84de9d845"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unidadeUnidade}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-7" mode="Transparent" x="340" y="0" width="50" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="3bad08a2-75c3-4987-b295-9376495bfa9a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{estoqueFisico} == null ? 0.00 :$F{estoqueFisico}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.0000;-#,##0.0000" isBlankWhenNull="true">
				<reportElement key="textField-50" mode="Transparent" x="440" y="0" width="42" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="32c2ac7a-593a-4aae-a67a-45d7f5cee5dd">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{ListarPreco} )]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{preco}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-53" mode="Transparent" x="482" y="0" width="46" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="aba14073-655b-47bf-afcb-d9b67be95914">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{ListarPreco} )]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{totalPreco}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-78" mode="Transparent" x="394" y="0" width="44" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f0fa7f09-b6e4-4c95-b311-135226323f50">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{ListarPreco})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{estoqueMinimo} == null? 0.00 :$F{estoqueMinimo}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-46" mode="Transparent" x="212" y="0" width="63" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="2d364827-1e46-44e7-bfae-f7c8ea0ede2e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{grupoEstoque}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-46" mode="Transparent" x="275" y="0" width="49" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="f05cf948-ab67-4223-ab10-99328ea0fb26"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.formatar($F{dataValidade})]]></textFieldExpression>
			</textField>
		</band>
		<band height="10">
			<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ExibirLote}) && $P{utilizaLocalizacaoEstoque}]]></printWhenExpression>
			<rectangle>
				<reportElement mode="Opaque" x="2" y="0" width="779" height="10" isPrintWhenDetailOverflows="true" forecolor="#FEFEFE" backcolor="#C45958" uuid="13f721e3-755b-40f9-b2cb-db2cb0a0de77">
					<printWhenExpression><![CDATA[$F{dataVencido}]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Opaque" x="1" y="0" width="779" height="10" isPrintWhenDetailOverflows="true" forecolor="#FEFEFE" backcolor="#FDED8D" uuid="dbd406c6-4c0e-4532-8fa1-1d05a417af67">
					<printWhenExpression><![CDATA[$F{dataProximaValidade}]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-5" mode="Transparent" x="2" y="0" width="150" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="e407fe8a-b3f3-4ae4-9e1f-824cca96d9f2"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{produtoDescricaoFormatado}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-6" mode="Transparent" x="325" y="0" width="14" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="3513eafa-7e8a-491c-9350-7fa84de9d845"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unidadeUnidade}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-7" mode="Transparent" x="340" y="0" width="50" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="3bad08a2-75c3-4987-b295-9376495bfa9a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{estoqueFisico} == null ? 0.00 :$F{estoqueFisico}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.0000;-#,##0.0000" isBlankWhenNull="true">
				<reportElement key="textField-50" mode="Transparent" x="440" y="0" width="42" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="32c2ac7a-593a-4aae-a67a-45d7f5cee5dd">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{ListarPreco} )]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{preco}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-53" mode="Transparent" x="482" y="0" width="46" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="aba14073-655b-47bf-afcb-d9b67be95914">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals( $P{ListarPreco} )]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{totalPreco}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-78" mode="Transparent" x="394" y="0" width="44" height="10" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f0fa7f09-b6e4-4c95-b311-135226323f50">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.NAO.equals($P{ListarPreco})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{estoqueMinimo} == null? 0.00 :$F{estoqueMinimo}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-46" mode="Transparent" x="212" y="0" width="63" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="2d364827-1e46-44e7-bfae-f7c8ea0ede2e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{grupoEstoque}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-46" mode="Transparent" x="275" y="0" width="49" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="f05cf948-ab67-4223-ab10-99328ea0fb26"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.formatar($F{dataValidade})]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-46" mode="Transparent" x="159" y="0" width="49" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="2d364827-1e46-44e7-bfae-f7c8ea0ede2e">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{ExibirLote}) && $P{utilizaLocalizacaoEstoque}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mascaraLocalizacaoEstrutura}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
