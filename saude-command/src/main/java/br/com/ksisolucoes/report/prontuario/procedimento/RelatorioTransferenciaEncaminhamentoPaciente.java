/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.prontuario.procedimento;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioAtendimentosBairrosMunicipiosDTOParam;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioTransferenciaEncaminhamentoDTOParam;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioTransferenciaEncaminhamentoPacienteDTO;
import br.com.ksisolucoes.report.prontuario.procedimento.query.QueryRelatorioTransferenciaEncaminhamentoPaciente;
import br.com.ksisolucoes.util.Bundle;
import ch.lambdaj.Lambda;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 */
public class RelatorioTransferenciaEncaminhamentoPaciente extends AbstractReport<RelatorioTransferenciaEncaminhamentoDTOParam> {

    public RelatorioTransferenciaEncaminhamentoPaciente(RelatorioTransferenciaEncaminhamentoDTOParam param) {
        super(param);
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();

        RelatorioTransferenciaEncaminhamentoPacienteDTO proxy = Lambda.on(RelatorioTransferenciaEncaminhamentoPacienteDTO.class);

        if (RelatorioTransferenciaEncaminhamentoDTOParam.FormaApresentacao.UNIDADE_ATENDIMENTO.value().equals(param.getFormaApresentacao())) {
            map.put(Bundle.getStringApplication("rotulo_unidade_atendimento"), proxy.getAtendimentoTransferenciaSetor().getEmpresaTransferencia().getDescricao());
        } else if (RelatorioTransferenciaEncaminhamentoDTOParam.FormaApresentacao.UNIDADE_DESTINO.value().equals(param.getFormaApresentacao())) {
            map.put(Bundle.getStringApplication("rotulo_unidade_destino"), proxy.getAtendimentoTransferenciaSetor().getAtendimento().getEmpresa().getDescricao());
        } else if (RelatorioTransferenciaEncaminhamentoDTOParam.FormaApresentacao.PROFISSIONAL.value().equals(param.getFormaApresentacao())) {
            map.put(Bundle.getStringApplication("rotulo_profissional"), proxy.getAtendimentoTransferenciaSetor().getAtendimento().getProfissional().getNome());
        } else if (RelatorioTransferenciaEncaminhamentoDTOParam.FormaApresentacao.TIPO_ATENDIMENTO.value().equals(param.getFormaApresentacao())) {
            map.put(Bundle.getStringApplication("rotulo_tipo_atendimento"), proxy.getAtendimentoTransferenciaSetor().getAgendaGradeAtendimentoHorario().getTipoProcedimento().getDescricao());
        }

        if (RelatorioAtendimentosBairrosMunicipiosDTOParam.TipoRelatorio.DETALHADO.value().equals(getParam().getTipoRelatorio())) {
            map.put(Bundle.getStringApplication("rotulo_paciente"), proxy.getAtendimentoTransferenciaSetor().getAtendimento().getUsuarioCadsus().getNomeSocial());
            map.put(Bundle.getStringApplication("rotulo_data_nascimento_abv2"), proxy.getAtendimentoTransferenciaSetor().getAtendimento().getUsuarioCadsus().getDataNascimentoFormatado());
            map.put(Bundle.getStringApplication("rotulo_data"), proxy.getAtendimentoTransferenciaSetor().getDataRegistroFormatada());

            if (!RelatorioTransferenciaEncaminhamentoDTOParam.FormaApresentacao.UNIDADE_ATENDIMENTO.value().equals(param.getFormaApresentacao())) {
                map.put(Bundle.getStringApplication("rotulo_unidade_atendimento"), proxy.getAtendimentoTransferenciaSetor().getEmpresaTransferencia().getDescricao());
            }

            if (!RelatorioTransferenciaEncaminhamentoDTOParam.FormaApresentacao.UNIDADE_DESTINO.value().equals(param.getFormaApresentacao())) {
                map.put(Bundle.getStringApplication("rotulo_unidade_destino"), proxy.getAtendimentoTransferenciaSetor().getAtendimento().getEmpresa().getDescricao());
            }

            if (!RelatorioTransferenciaEncaminhamentoDTOParam.FormaApresentacao.PROFISSIONAL.value().equals(param.getFormaApresentacao())) {
                map.put(Bundle.getStringApplication("rotulo_profissional"), proxy.getAtendimentoTransferenciaSetor().getAtendimento().getProfissional().getNome());
            }

            if (!RelatorioTransferenciaEncaminhamentoDTOParam.FormaApresentacao.TIPO_ATENDIMENTO.value().equals(param.getFormaApresentacao())) {
                map.put(Bundle.getStringApplication("rotulo_tipo_atendimento"), proxy.getAtendimentoTransferenciaSetor().getAgendaGradeAtendimentoHorario().getTipoProcedimento().getDescricao());
            }
        } else {
            if (RelatorioTransferenciaEncaminhamentoDTOParam.TipoResumo.UNIDADE_ATENDIMENTO.value().equals(param.getTipoResumo()) && !RelatorioTransferenciaEncaminhamentoDTOParam.FormaApresentacao.UNIDADE_ATENDIMENTO.value().equals(param.getFormaApresentacao())) {
                map.put(Bundle.getStringApplication("rotulo_unidade_atendimento"), proxy.getAtendimentoTransferenciaSetor().getEmpresaTransferencia().getDescricao());
            } else if (RelatorioTransferenciaEncaminhamentoDTOParam.TipoResumo.UNIDADE_DESTINO.value().equals(param.getTipoResumo()) && !RelatorioTransferenciaEncaminhamentoDTOParam.FormaApresentacao.UNIDADE_DESTINO.value().equals(param.getFormaApresentacao())) {
                map.put(Bundle.getStringApplication("rotulo_unidade_destino"), proxy.getAtendimentoTransferenciaSetor().getAtendimento().getEmpresa().getDescricao());
            } else if (RelatorioTransferenciaEncaminhamentoDTOParam.TipoResumo.PROFISSIONAL.value().equals(param.getTipoResumo()) && !RelatorioTransferenciaEncaminhamentoDTOParam.FormaApresentacao.PROFISSIONAL.value().equals(param.getFormaApresentacao())) {
                map.put(Bundle.getStringApplication("rotulo_profissional"), proxy.getAtendimentoTransferenciaSetor().getAtendimento().getProfissional().getNome());
            } else if (RelatorioTransferenciaEncaminhamentoDTOParam.TipoResumo.TIPO_ATENDIMENTO.value().equals(param.getTipoResumo()) && !RelatorioTransferenciaEncaminhamentoDTOParam.FormaApresentacao.TIPO_ATENDIMENTO.value().equals(param.getFormaApresentacao())) {
                map.put(Bundle.getStringApplication("rotulo_tipo_atendimento"), proxy.getAtendimentoTransferenciaSetor().getAgendaGradeAtendimentoHorario().getTipoProcedimento().getDescricao());
            }
        }

        map.put(Bundle.getStringApplication("rotulo_quantidade"), proxy.getQuantidade());

        return map;
    }

    @Override
    public String getXML() {
        addParametro("tipoRelatorio", getParam().getTipoRelatorio());
        addParametro("formaApresentacao", getParam().getFormaApresentacao());
        addParametro("tipoResumo", getParam().getTipoResumo());

        if (getParam().getTipoRelatorio().equals(RelatorioAtendimentosBairrosMunicipiosDTOParam.TipoRelatorio.DETALHADO.value())) {
            return "/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_transferencia_encaminhamento_paciente.jrxml";
        } else {
            return "/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_transferencia_encaminhamento_paciente_resumido.jrxml";
        }
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relatorio_transferencia_encaminhamento_paciente");
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioTransferenciaEncaminhamentoPaciente();
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return getParam().getTipoArquivo();
    }
}
