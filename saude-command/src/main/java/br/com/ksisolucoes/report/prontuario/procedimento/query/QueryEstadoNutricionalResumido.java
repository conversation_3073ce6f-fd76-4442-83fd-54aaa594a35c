package br.com.ksisolucoes.report.prontuario.procedimento.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioEstadoNutricionalDTO;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioEstadoNutricionalDTOParam;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.avaliacao.EstadoNutricional;
import ch.lambdaj.Lambda;
import org.hibernate.Query;
import org.hibernate.Session;

import java.util.*;

/**
 *
 * <AUTHOR>
 */
public class QueryEstadoNutricionalResumido extends CommandQuery implements ITransferDataReport<RelatorioEstadoNutricionalDTOParam, RelatorioEstadoNutricionalDTO> {

    private RelatorioEstadoNutricionalDTOParam param;
    private List<RelatorioEstadoNutricionalDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(RelatorioEstadoNutricionalDTO.class.getName());

        hql.addToSelect("usuarioCadsus.dataNascimento"              ,   "usuarioCadsus.dataNascimento"                );
        hql.addToSelect("usuarioCadsus.codigo"                      ,   "usuarioCadsus.codigo"                        );
        hql.addToSelect("usuarioCadsus.nome"                        ,   "usuarioCadsus.nome"                          );
        hql.addToSelect("usuarioCadsus.sexo"                        ,   "usuarioCadsus.sexo"                          );

        hql.addToSelect("enderecoUsuarioCadsus.numeroLogradouro"    ,   "enderecoUsuarioCadsus.numeroLogradouro"      );
        hql.addToSelect("enderecoUsuarioCadsus.logradouro"          ,   "enderecoUsuarioCadsus.logradouro"            );
        hql.addToSelect("enderecoUsuarioCadsus.codigo"              ,   "enderecoUsuarioCadsus.codigo"                );
        hql.addToSelect("enderecoUsuarioCadsus.bairro"              ,   "enderecoUsuarioCadsus.bairro"                );
        hql.addToSelect("enderecoUsuarioCadsus.cep"                 ,   "enderecoUsuarioCadsus.cep"                   );


        hql.addToSelect("estadoNutricional.nivelEscolaridade"       ,   "estadoNutricional.nivelEscolaridade"         );
        hql.addToSelect("estadoNutricional.dataRegistro"            ,   "estadoNutricional.dataRegistro"              );
        hql.addToSelect("estadoNutricional.dataCadastro"            ,   "estadoNutricional.dataCadastro"              );
        hql.addToSelect("estadoNutricional.indiceImc"               ,   "estadoNutricional.indiceImc"                 );
        hql.addToSelect("estadoNutricional.gestante"                ,   "estadoNutricional.gestante"                  );
        hql.addToSelect("estadoNutricional.codigo"                  ,   "estadoNutricional.codigo"                    );
        hql.addToSelect("estadoNutricional.altura"                  ,   "estadoNutricional.altura"                    );
        hql.addToSelect("estadoNutricional.peso"                    ,   "estadoNutricional.peso"                      );

        hql.addToSelect("cidadeEmpresa.descricao"                   ,   "cidade.descricao"                            );
        hql.addToSelect("cidadeEmpresa.codigo"                      ,   "cidade.codigo"                               );

        hql.addToSelect("estadoEmpresa.descricao"                   ,   "estado.descricao"                            );
        hql.addToSelect("estadoEmpresa.codigo"                      ,   "estado.codigo"                               );
        hql.addToSelect("estadoEmpresa.sigla"                       ,   "estado.sigla"                                );

        hql.addToSelect("empresa.descricao"                         ,   "empresa.descricao"                           );
        hql.addToSelect("empresa.codigo"                            ,   "empresa.codigo"                              );
        hql.addToSelect("empresa.cnes"                              ,   "empresa.cnes"                                );


        hql.addToFrom("EstadoNutricional estadoNutricional "
                + " left join estadoNutricional.usuarioCadsus            usuarioCadsus"
                + " left join usuarioCadsus.enderecoDomicilio            enderecoDomicilio"
                + " left join usuarioCadsus.enderecoUsuarioCadsus        enderecoUsuarioCadsus"
                + " left join estadoNutricional.empresa                  empresa"
                + " left join empresa.cidade                             cidadeEmpresa"
                + " left join cidadeEmpresa.estado                       estadoEmpresa");


        hql.addToWhereWhithAnd("estadoNutricional.indiceImc is not null");
        hql.addToWhereWhithAnd("estadoNutricional.empresa   = ", this.param.getEmpresa());
        hql.addToWhereWhithAnd("enderecoDomicilio.area      = ", this.param.getEquipeArea());
        if (this.param.getEquipeMicroArea() != null){
            hql.addToWhereWhithAnd("enderecoDomicilio.microArea = ", this.param.getEquipeMicroArea().getMicroArea());
        }
        hql.addToWhereWhithAnd("usuarioCadsus.sexo          = ", this.param.getSexo());
        hql.addToWhereWhithAnd("usuarioCadsus.raca          = ", this.param.getRaca());
        hql.addToWhereWhithAnd("usuarioCadsus.etniaIndigena = ", this.param.getEtniaIndigena());
        hql.addToWhereWhithAnd("usuarioCadsus.etniaIndigena = ", this.param.getEtniaIndigena());
        hql.addToWhereWhithAnd("estadoNutricional.nivelEscolaridade = ", this.param.getEscolaridade());

        hql.addToWhereWhithAnd("estadoNutricional.codigo = (" + getSubQueryMaxDataAcompanhamento(hql).getQuery() + ")");


        if(param.getFaseVida() != null){
            hql.addToWhereWhithAnd("extract(years from age(current_date, usuarioCadsus.dataNascimento)) * 12 + extract(months from age(current_date, usuarioCadsus.dataNascimento))  between :idadeInicial and :idadeFinal");
        }
        if (RelatorioEstadoNutricionalDTOParam.FormaApresentacao.ESCOLARIDADE.value().equals(param.getFormaApresentacao())){
            hql.addToOrder("estadoNutricional.nivelEscolaridade asc");
        }
        if (RelatorioEstadoNutricionalDTOParam.FormaApresentacao.ESTADO_NUTRICIONAL.value().equals(param.getFormaApresentacao())){
            hql.addToOrder("estadoNutricional.codigo asc");
        }
        if (RelatorioEstadoNutricionalDTOParam.FormaApresentacao.RACA.value().equals(param.getFormaApresentacao())){
            hql.addToOrder("usuarioCadsus.raca asc ");
        }
        if (RelatorioEstadoNutricionalDTOParam.FormaApresentacao.UNIDADE.value().equals(param.getFormaApresentacao())){
            hql.addToOrder("empresa.codigo asc");
        }


    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        Double baixoPeso = 0D;
        Double adequado = 0D;
        Double sobrePeso = 0D;
        Double obesidadeUm = 0D;
        Double obesidadeDois = 0D;
        Double obesidadeTres = 0D;
        Double total = 0D;
//        Double totalEspecificoEstadoNutricional = 0D;

        if (this.param.getDescricaoEstadoNutricional() != null && RelatorioEstadoNutricionalDTOParam.TipoRelatorio.RESUMIDO.value().equals(param.getTipoRelatorio()) && CollectionUtils.isNotNullEmpty(this.result)) {
            List<RelatorioEstadoNutricionalDTO> listaAux = new ArrayList<>();
            for (RelatorioEstadoNutricionalDTO relatorioEstadoNutricionalDTO : result) {
//                    totalEspecificoEstadoNutricional++;
                EstadoNutricional.Tipo tipoIndiceImc = relatorioEstadoNutricionalDTO.getEstadoNutricional().resolveTipoIndiceImc();
                if (tipoIndiceImc != null && this.param.getDescricaoEstadoNutricional().equals(tipoIndiceImc.value().toString())) {
                    listaAux.add(relatorioEstadoNutricionalDTO);
                }
            }
            this.result = listaAux;
        }


        if (CollectionUtils.isNotNullEmpty(this.result)) {
            if (RelatorioEstadoNutricionalDTOParam.TipoRelatorio.RESUMIDO.value().equals(param.getTipoRelatorio())) {
                for (RelatorioEstadoNutricionalDTO relatorioEstadoNutricionalDTO : result) {
                    if (relatorioEstadoNutricionalDTO.getEstadoNutricional().resolveTipoIndiceImc() != null ) {
                        if (EstadoNutricional.Tipo.BAIXO_PESO.equals(relatorioEstadoNutricionalDTO.getEstadoNutricional().resolveTipoIndiceImc())) {
                            baixoPeso = baixoPeso + 1D;
                        }
                        if (EstadoNutricional.Tipo.ADEQUADO.equals(relatorioEstadoNutricionalDTO.getEstadoNutricional().resolveTipoIndiceImc())) {
                            adequado = adequado + 1D;
                        }
                        if (EstadoNutricional.Tipo.SOBREPESO.equals(relatorioEstadoNutricionalDTO.getEstadoNutricional().resolveTipoIndiceImc())) {
                            sobrePeso = sobrePeso + 1D;
                        }
                        if (EstadoNutricional.Tipo.OBESIDADE_I.equals(relatorioEstadoNutricionalDTO.getEstadoNutricional().resolveTipoIndiceImc())) {
                            obesidadeUm = obesidadeUm + 1D;
                        }
                        if (EstadoNutricional.Tipo.OBESIDADE_II.equals(relatorioEstadoNutricionalDTO.getEstadoNutricional().resolveTipoIndiceImc())) {
                            obesidadeDois = obesidadeDois + 1D;
                        }
                        if (EstadoNutricional.Tipo.OBESIDADE_III.equals(relatorioEstadoNutricionalDTO.getEstadoNutricional().resolveTipoIndiceImc())) {
                            obesidadeTres = obesidadeTres + 1D;
                        }
                    }
                }
                total = baixoPeso + adequado + sobrePeso + obesidadeUm + obesidadeDois + obesidadeTres;
//                if (totalEspecificoEstadoNutricional > total){
//                    total = totalEspecificoEstadoNutricional;
//                }
                Lambda.forEach(this.result).setQtdBaixoPeso(baixoPeso);
                Lambda.forEach(this.result).setQtdAdequado(adequado);
                Lambda.forEach(this.result).setQtdSobrePeso(sobrePeso);
                Lambda.forEach(this.result).setQtdObesidadeUm(obesidadeUm);
                Lambda.forEach(this.result).setQtdObesidadeDois(obesidadeDois);
                Lambda.forEach(this.result).setQtdObesidadeTres(obesidadeTres);
                Lambda.forEach(this.result).setTotal(total);
            }
        }
    }

    private HQLHelper getSubQueryMaxDataAcompanhamento(HQLHelper hql) {
        HQLHelper subQueryCodigo = hql.getNewInstanceSubQuery();
        subQueryCodigo.addToSelect("min(enCodigo.codigo)");
        subQueryCodigo.addToFrom("EstadoNutricional enCodigo");
        subQueryCodigo.addToWhereWhithAnd("enCodigo.usuarioCadsus = usuarioCadsus");
        subQueryCodigo.addToWhereWhithAnd("enCodigo.empresa = ", param.getEmpresa());

        HQLHelper subQueryMaxData = subQueryCodigo.getNewInstanceSubQuery();
        subQueryMaxData.addToSelect("max(enData.dataAcompanhamento)");
        subQueryMaxData.addToFrom("EstadoNutricional enData");
        subQueryMaxData.addToWhereWhithAnd("enData.usuarioCadsus = usuarioCadsus");
        subQueryMaxData.addToWhereWhithAnd("enData.indiceImc is not null");
        subQueryMaxData.addToWhereWhithAnd("enData.empresa = ", param.getEmpresa());
        subQueryMaxData.addToWhereWhithAnd("enData.dataAcompanhamento ", Data.adjustRangeHour(param.getPeriodo()));

        subQueryCodigo.addToWhereWhithAnd("enCodigo.dataAcompanhamento = (" + subQueryMaxData.getQuery() + ")");

        return subQueryCodigo;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }
    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        if(param.getFaseVida() != null){
            query.setLong("idadeInicial", RelatorioEstadoNutricionalDTOParam.FaseVida.valueOf(param.getFaseVida()).getIdadeInicial());
            query.setLong("idadeFinal",  RelatorioEstadoNutricionalDTOParam.FaseVida.valueOf(param.getFaseVida()).getIdadeFinal());
        }
    }

    public void addParameter(String key, Object object) {
    }

    @Override
    public Collection getResult() {
        return this.result;
    }

    @Override
    public void setDTOParam(RelatorioEstadoNutricionalDTOParam param) {
        this.param = param;
    }

}
