<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_impressao_dispensacao_medicamento" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="233c8663-c4a3-4442-84b2-9e84c5c8f231">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoHelper"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="CIDADE_UNIDADE" class="java.lang.String"/>
	<parameter name="UF_UNIDADE" class="java.lang.String"/>
	<parameter name="UNIDADE_ATENDIMENTO" class="java.lang.String"/>
	<parameter name="CAMINHO_IMAGEM_PADRAO" class="java.lang.String"/>
	<parameter name="BAIRRO_UNIDADE" class="java.lang.String"/>
	<parameter name="CABECALHO_ADICIONAL_2" class="java.lang.String"/>
	<parameter name="RUA_UNIDADE" class="java.lang.String"/>
	<parameter name="CEP_UNIDADE" class="java.lang.String"/>
	<parameter name="DESC_CABECALHO_PADRAO" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="EXIBIR_CABECALHO" class="java.lang.Boolean"/>
	<parameter name="CABECALHO_ADICIONAL_1" class="java.lang.String"/>
	<parameter name="FONE_UNIDADE" class="java.lang.String"/>
	<parameter name="TITULO_REPORT" class="java.lang.String"/>
	<parameter name="NUMERO_UNIDADE" class="java.lang.String"/>
	<parameter name="EXIBIR_RODAPE" class="java.lang.Boolean"/>
	<parameter name="CABECALHO_DIRETOR_TECNICO" class="java.lang.String"/>
	<field name="numeroDispensacao" class="java.lang.Long"/>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="empresaOrigem" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<field name="usuario" class="br.com.ksisolucoes.vo.controle.Usuario"/>
	<field name="usuarioCadsusDestino" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="produto" class="br.com.ksisolucoes.vo.entradas.estoque.Produto"/>
	<field name="dataReceita" class="java.util.Date"/>
	<field name="dataDispensacao" class="java.util.Date"/>
	<field name="tipoReceita" class="br.com.ksisolucoes.vo.prontuario.basico.TipoReceita"/>
	<field name="dataProximaDispensacao" class="java.util.Date"/>
	<field name="posologia" class="java.lang.Double"/>
	<field name="quantidadePrescrita" class="java.lang.Double"/>
	<field name="quantidadeDispensada" class="java.lang.Double"/>
	<field name="receita" class="java.lang.String"/>
	<field name="nomeUsuarioDestino" class="java.lang.String"/>
	<field name="lote" class="java.lang.String"/>
	<field name="itens" class="java.util.List"/>
	<variable name="produto" class="br.com.ksisolucoes.vo.entradas.estoque.Produto">
		<variableExpression><![CDATA[$F{produto}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="327" splitType="Prevent">
			<subreport isUsingCache="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="555" height="325" uuid="7862e9f9-b83f-4e93-8bfd-254c928737c7"/>
				<subreportParameter name="CAMINHO_IMAGEM_PADRAO">
					<subreportParameterExpression><![CDATA[$P{CAMINHO_IMAGEM_PADRAO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="CEP_UNIDADE">
					<subreportParameterExpression><![CDATA[$P{CEP_UNIDADE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="EXIBIR_RODAPE">
					<subreportParameterExpression><![CDATA[$P{EXIBIR_RODAPE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="CABECALHO_ADICIONAL_1">
					<subreportParameterExpression><![CDATA[$P{CABECALHO_ADICIONAL_1}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="CABECALHO_ADICIONAL_2">
					<subreportParameterExpression><![CDATA[$P{CABECALHO_ADICIONAL_2}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_UNIDADE">
					<subreportParameterExpression><![CDATA[$P{NUMERO_UNIDADE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="CIDADE_UNIDADE">
					<subreportParameterExpression><![CDATA[$P{CIDADE_UNIDADE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="UF_UNIDADE">
					<subreportParameterExpression><![CDATA[$P{UF_UNIDADE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="BAIRRO_UNIDADE">
					<subreportParameterExpression><![CDATA[$P{BAIRRO_UNIDADE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="TITULO_REPORT">
					<subreportParameterExpression><![CDATA[$P{TITULO_REPORT}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="DESC_CABECALHO_PADRAO">
					<subreportParameterExpression><![CDATA[$P{DESC_CABECALHO_PADRAO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="CABECALHO_DIRETOR_TECNICO">
					<subreportParameterExpression><![CDATA[$P{CABECALHO_DIRETOR_TECNICO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="RUA_UNIDADE">
					<subreportParameterExpression><![CDATA[$P{RUA_UNIDADE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="FONE_UNIDADE">
					<subreportParameterExpression><![CDATA[$P{FONE_UNIDADE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="EXIBIR_CABECALHO">
					<subreportParameterExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="UNIDADE_ATENDIMENTO">
					<subreportParameterExpression><![CDATA[$P{UNIDADE_ATENDIMENTO}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{itens})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/sub_relatorio_impressao_dispensacao_medicamento.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="50">
			<staticText>
				<reportElement x="4" y="31" width="535" height="9" uuid="e0009c7f-23c8-4720-84c0-ce41a171d65c"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7"/>
				</textElement>
				<text><![CDATA[-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-16" mode="Transparent" x="4" y="13" width="247" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="0b11974d-f4ac-4403-b007-731c88d96522"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Declaro ter recebido todos os itens descritos neste documento."]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="357" y="22" width="192" height="1" uuid="2dec7e90-d123-458a-9a3a-97af7d48c1af"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-18" mode="Transparent" x="256" y="13" width="97" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="0156a28f-bfcd-46ca-a531-faaab9612182"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_assinatura")+": "]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
