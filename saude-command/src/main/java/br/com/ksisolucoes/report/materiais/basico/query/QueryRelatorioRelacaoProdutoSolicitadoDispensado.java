package br.com.ksisolucoes.report.materiais.basico.query;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.materiais.judicial.interfaces.dto.RelatorioRelacaoProdutoSolicitadoDispensadoDTO;
import br.com.ksisolucoes.report.materiais.judicial.interfaces.dto.RelatorioRelacaoProdutoSolicitadoDispensadoDTOParam;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.ProdutoSolicitadoMovimentoLote;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoquePK;
import org.hibernate.Session;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioRelacaoProdutoSolicitadoDispensado extends CommandQuery<QueryRelatorioRelacaoProdutoSolicitadoDispensado> implements ITransferDataReport<RelatorioRelacaoProdutoSolicitadoDispensadoDTOParam, RelatorioRelacaoProdutoSolicitadoDispensadoDTO> {

    private RelatorioRelacaoProdutoSolicitadoDispensadoDTOParam param;
    private List<RelatorioRelacaoProdutoSolicitadoDispensadoDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("p.codigo", "produto.codigo");
        hql.addToSelect("p.referencia", "produto.referencia");
        hql.addToSelect("p.descricao", "produto.descricao");
        hql.addToSelect("uni.unidade", "produto.unidade.unidade");

        hql.addToSelect("prof.codigo", "produtoSolicitado.profissional.codigo");
        hql.addToSelect("prof.referencia", "produtoSolicitado.profissional.referencia");
        hql.addToSelect("prof.nome", "produtoSolicitado.profissional.nome");

        hql.addToSelect("empSol.codigo", "produtoSolicitado.empresa.codigo");
        hql.addToSelect("empSol.referencia", "produtoSolicitado.empresa.referencia");
        hql.addToSelect("empSol.descricao", "produtoSolicitado.empresa.descricao");

        hql.addToSelect("empDisp.codigo", "produtoSolicitadoMovimento.empresa.codigo");
        hql.addToSelect("empDisp.referencia", "produtoSolicitadoMovimento.empresa.referencia");
        hql.addToSelect("empDisp.descricao", "produtoSolicitadoMovimento.empresa.descricao");

        hql.addToSelect("uc.codigo", "produtoSolicitado.usuarioCadsus.codigo");
        hql.addToSelect("uc.nome", "produtoSolicitado.usuarioCadsus.nome");

        hql.addToSelect("ps.codigo", "produtoSolicitado.codigo");
        hql.addToSelect("ps.dataReceita", "produtoSolicitado.dataReceita");

        hql.addToSelect("psi.dataValidade", "produtoSolicitadoItem.dataValidade");
        hql.addToSelect("psi.quantidadeMensal", "produtoSolicitadoItem.quantidadeMensal");

        hql.addToSelect("psm.codigo", "produtoSolicitadoMovimento.codigo");
        hql.addToSelect("psm.dataMovimento", "produtoSolicitadoMovimento.dataMovimento");
        hql.addToSelect("psm.quantidade", "produtoSolicitadoMovimento.quantidade");

        hql.addToSelect("tsp.codigo", "produtoSolicitado.tipoSolicitacaoProduto.codigo");
        hql.addToSelect("tsp.descricao", "produtoSolicitado.tipoSolicitacaoProduto.descricao");

        hql.setTypeSelect(RelatorioRelacaoProdutoSolicitadoDispensadoDTO.class.getName());
        hql.addToFrom("ProdutoSolicitadoMovimento psm"
                + " left join psm.produtoSolicitadoItem psi"
                + " left join psm.empresa empDisp"
                + " left join psi.produto p"
                + " left join p.unidade uni"
                + " left join psi.produtoSolicitado ps"
                + " left join ps.usuarioCadsus uc"
                + " left join ps.empresa empSol"
                + " left join ps.profissional prof"
                + " left join ps.tipoSolicitacaoProduto tsp");

        hql.addToWhereWhithAnd("empDisp = ", param.getEmpresaDispensacao());
        hql.addToWhereWhithAnd("empSol = ", param.getEmpresaSolicitante());
        hql.addToWhereWhithAnd("prof = ", param.getProfissional());
        hql.addToWhereWhithAnd("uc = ", this.param.getUsuarioCadsus());
        hql.addToWhereWhithAnd("tsp = ", param.getTipoSolicitacaoProduto());
        hql.addToWhereWhithAnd("p = ", param.getProduto());
        hql.addToWhereWhithAnd("psm.dataMovimento ", Data.adjustRangeHour(this.param.getPeriodo()));

        if (RelatorioRelacaoProdutoSolicitadoDispensadoDTOParam.FormaApresentacao.PACIENTE.equals(param.getFormaApresentacao())) {
            hql.addToOrder("uc.nome");
        } else if (RelatorioRelacaoProdutoSolicitadoDispensadoDTOParam.FormaApresentacao.PRODUTO.equals(param.getFormaApresentacao())) {
            hql.addToOrder("p.descricao");
        } else if (RelatorioRelacaoProdutoSolicitadoDispensadoDTOParam.FormaApresentacao.UNIDADE_SOLICITANTE.equals(param.getFormaApresentacao())) {
            hql.addToOrder("empSol.descricao");
        } else if (RelatorioRelacaoProdutoSolicitadoDispensadoDTOParam.FormaApresentacao.UNIDADE_DISPENSACAO.equals(param.getFormaApresentacao())) {
            hql.addToOrder("empDisp.descricao");
        } else if (RelatorioRelacaoProdutoSolicitadoDispensadoDTOParam.FormaApresentacao.PROFISSIONAL.equals(param.getFormaApresentacao())) {
            hql.addToOrder("prof.nome");
        }
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(this.result)) {
            for (RelatorioRelacaoProdutoSolicitadoDispensadoDTO dto : this.result) {
                List<ProdutoSolicitadoMovimentoLote> listProdutoSolicitadoMovimentoLote = LoadManager.getInstance(ProdutoSolicitadoMovimentoLote.class)
                        .addProperty(ProdutoSolicitadoMovimentoLote.PROP_CODIGO)
                        .addProperty(ProdutoSolicitadoMovimentoLote.PROP_QUANTIDADE)
                        .addProperty(VOUtils.montarPath(ProdutoSolicitadoMovimentoLote.PROP_GRUPO_ESTOQUE, GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_GRUPO))
                        .addProperties(new HQLProperties(GrupoEstoque.class, ProdutoSolicitadoMovimentoLote.PROP_GRUPO_ESTOQUE).getProperties())
                        .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProdutoSolicitadoMovimentoLote.PROP_PRODUTO_SOLICITADO_MOVIMENTO), dto.getProdutoSolicitadoMovimento()))
                        .start().getList();

                dto.setProdutoSolicitadoMovimentoLoteList(listProdutoSolicitadoMovimentoLote);
            }
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioRelacaoProdutoSolicitadoDispensadoDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(RelatorioRelacaoProdutoSolicitadoDispensadoDTOParam param) {
        this.param = param;
    }
}
