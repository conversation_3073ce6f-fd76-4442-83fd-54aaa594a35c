package br.com.ksisolucoes.report.entrada.estoque;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelacaoProdutosProgramaDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.query.QueryRelacaoProdutosPrograma;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelacaoProdutosPrograma extends AbstractReport<RelacaoProdutosProgramaDTOParam> {

    public RelacaoProdutosPrograma(RelacaoProdutosProgramaDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/entrada/estoque/jrxml/relacao_produtos_programa.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_produtos_programa");
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelacaoProdutosPrograma();
    }
}
