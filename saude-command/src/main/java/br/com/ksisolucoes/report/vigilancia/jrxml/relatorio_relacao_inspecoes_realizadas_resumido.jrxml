<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_equipes" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="11f7aedf-8e58-4668-8dea-f121a31c2b60">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.948717100000024"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="br.com.ksisolucoes.vo.basico.TipoEquipe"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.basico.Equipe"/>
	<import value="br.com.ksisolucoes.report.ReportProperties"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="formaApresentacao" class="java.lang.Long"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["/home/<USER>/projetos/saude-server/saude-command/src/main/java/br/com/ksisolucoes/report/vigilancia/jrxml/"]]></defaultValueExpression>
	</parameter>
	<parameter name="LABEL_ATIVIDADE" class="java.lang.String"/>
	<field name="lancamentoAtividadesVigilanciaItem" class="br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilanciaItem"/>
	<field name="lancamentoAtividadesVigilancia" class="br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilancia">
		<fieldDescription><![CDATA[lancamentoAtividadesVigilanciaItem.lancamentoAtividadesVigilancia]]></fieldDescription>
	</field>
	<field name="setorVigilancia" class="br.com.ksisolucoes.vo.vigilancia.SetorVigilancia"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="FA" class="br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioRelacaoInspecoesRealizadasDTOParam.FormaApresentacao"/>
	<variable name="sumQuantidadeFA" class="java.lang.Long" resetType="Group" resetGroup="FormaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{lancamentoAtividadesVigilanciaItem}.getQuantidade()]]></variableExpression>
	</variable>
	<variable name="totalQuantidade" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{lancamentoAtividadesVigilanciaItem}.getQuantidade()]]></variableExpression>
	</variable>
	<group name="FormaApresentacao" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[!$V{FA}.GERAL.value().equals($P{formaApresentacao})
    ?
        $V{FA}.PROFISSIONAL.value().equals($P{formaApresentacao})
        ?
            $F{lancamentoAtividadesVigilancia}.getProfissional() != null && $F{lancamentoAtividadesVigilancia}.getProfissional().getNome() != null
                ? $F{lancamentoAtividadesVigilancia}.getProfissional().getNome()
                : "Não informado"
        :
            $V{FA}.ATIVIDADE.value().equals($P{formaApresentacao})
            ?
                  $F{lancamentoAtividadesVigilancia}.getAtividadeEstabelecimento()!= null && $F{lancamentoAtividadesVigilancia}.getAtividadeEstabelecimento().getDescricao() != null
                ?  $F{lancamentoAtividadesVigilancia}.getAtividadeEstabelecimento().getDescricao()
                : "Não informado"

            :
                $V{FA}.SETOR_RESPONSAVEL.value().equals($P{formaApresentacao})
                ?
                  $F{setorVigilancia} != null && $F{setorVigilancia}.getDescricao() != null
                        ?  $F{setorVigilancia}.getDescricao()
                        : "Não informado"
                :
                $F{lancamentoAtividadesVigilancia}.getDataAtividade()
    :
        $F{lancamentoAtividadesVigilancia}.getDataAtividade()]]></groupExpression>
		<groupHeader>
			<band height="36" splitType="Prevent">
				<printWhenExpression><![CDATA[!$V{FA}.GERAL.value().equals($P{formaApresentacao})]]></printWhenExpression>
				<rectangle radius="8">
					<reportElement stretchType="RelativeToBandHeight" x="1" y="2" width="534" height="18" uuid="6e975e1f-6a2f-4f18-bd9e-7f3952237014">
						<printWhenExpression><![CDATA[!$V{FA}.GERAL.value().equals($P{formaApresentacao})]]></printWhenExpression>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy">
					<reportElement x="7" y="5" width="525" height="13" uuid="c8ddfca8-42d9-430f-abf7-fdbb24f819d7">
						<printWhenExpression><![CDATA[!$V{FA}.GERAL.value().equals($P{formaApresentacao})]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="11" isBold="true" isUnderline="true"/>
					</textElement>
					<textFieldExpression><![CDATA[!$V{FA}.GERAL.value().equals($P{formaApresentacao})
    ?
        $V{FA}.PROFISSIONAL.value().equals($P{formaApresentacao})
        ?
            $F{lancamentoAtividadesVigilancia}.getProfissional() != null && $F{lancamentoAtividadesVigilancia}.getProfissional().getNome() != null
                ? $F{lancamentoAtividadesVigilancia}.getProfissional().getNome()
                : "Não informado"
        :
            $V{FA}.ATIVIDADE.value().equals($P{formaApresentacao})
            ?
                  $F{lancamentoAtividadesVigilancia}.getAtividadeEstabelecimento()!= null && $F{lancamentoAtividadesVigilancia}.getAtividadeEstabelecimento().getDescricao() != null
                ?  $F{lancamentoAtividadesVigilancia}.getAtividadeEstabelecimento().getDescricao()
                : "Não informado"

            :
                $V{FA}.SETOR_RESPONSAVEL.value().equals($P{formaApresentacao})
                ?
                  $F{setorVigilancia} != null && $F{setorVigilancia}.getDescricao() != null
                        ?  $F{setorVigilancia}.getDescricao()
                        : "Não informado"
                :
                $F{lancamentoAtividadesVigilancia}.getDataAtividade()
    :
        ""]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="1" y="35" width="534" height="1" uuid="cc8f5f8d-993b-4520-be9f-5ba8788e3bd6">
						<printWhenExpression><![CDATA[$V{FA}.GERAL.value().equals($P{formaApresentacao}) ? $V{COLUMN_COUNT} == 0 : true]]></printWhenExpression>
					</reportElement>
				</line>
				<textField>
					<reportElement x="458" y="24" width="73" height="12" uuid="f611b9d8-f4ea-4f00-ab95-69f2162c36ad">
						<printWhenExpression><![CDATA[$V{FA}.GERAL.value().equals($P{formaApresentacao}) ? $V{COLUMN_COUNT} == 0 : true]]></printWhenExpression>
					</reportElement>
					<box rightPadding="0"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Quantidade"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="6" y="24" width="100" height="12" uuid="53c91fc5-fab0-483d-b322-a3141304f260">
						<printWhenExpression><![CDATA[$V{FA}.GERAL.value().equals($P{formaApresentacao}) ? $V{COLUMN_COUNT} == 0 : true]]></printWhenExpression>
					</reportElement>
					<box leftPadding="0"/>
					<textElement>
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Data da Inspeção"]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="17" splitType="Stretch">
				<printWhenExpression><![CDATA[!$V{FA}.GERAL.value().equals($P{formaApresentacao})]]></printWhenExpression>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" x="407" y="5" width="123" height="12" uuid="f23ce7a6-6690-40de-908a-4ec85bc03586"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Total: "+ $V{sumQuantidadeFA}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="398" y="1" width="133" height="1" uuid="7e547cca-6f84-4730-8637-87df7fce9e78"/>
				</line>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<detail>
		<band height="14">
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="1" width="535" height="13" forecolor="#FFFFFF" backcolor="#DFDFDF" uuid="cbeba49e-9c75-408d-b5a0-fe90405eeb3f">
					<printWhenExpression><![CDATA[$V{REPORT_COUNT}%2 == 1]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<textField pattern="###0">
				<reportElement stretchType="RelativeToBandHeight" x="458" y="1" width="73" height="12" uuid="0a8b8cee-da2a-48d7-b83a-a0dc528e8d29"/>
				<box rightPadding="2"/>
				<textElement textAlignment="Right" markup="html">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[!$V{FA}.GERAL.value().equals($P{formaApresentacao}) ?
    $F{lancamentoAtividadesVigilanciaItem}.getQuantidade()
    :
    "<b> Quantidade:    </b>" +  $F{lancamentoAtividadesVigilanciaItem}.getQuantidade()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy">
				<reportElement stretchType="RelativeToBandHeight" x="6" y="1" width="273" height="12" uuid="9f6ee1d9-d8f9-4518-adcf-402caa93aa11">
					<property name="net.sf.jasperreports.text.truncate.at.char" value="true"/>
				</reportElement>
				<box leftPadding="2"/>
				<textElement markup="html">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[!$V{FA}.GERAL.value().equals($P{formaApresentacao}) ?
    $F{lancamentoAtividadesVigilanciaItem}.getLancamentoAtividadesVigilancia().getDataAtividade()
    :
    "<b> Data da Inspeção:    </b>" +  Data.formatar($F{lancamentoAtividadesVigilanciaItem}.getLancamentoAtividadesVigilancia().getDataAtividade())]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<summary>
		<band height="17" splitType="Stretch">
			<line>
				<reportElement positionType="Float" x="398" y="0" width="133" height="1" uuid="05e8191f-ff0a-45f6-b5d1-3dabf11b54ef"/>
			</line>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" x="407" y="4" width="123" height="12" uuid="72446072-ae8e-4188-b4b3-4c24934e4b07"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Total Geral: " + $V{totalQuantidade}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
