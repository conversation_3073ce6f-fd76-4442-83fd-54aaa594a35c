package br.com.ksisolucoes.report.consorcio.consorcioguiaprocedimento.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoGuiaProcedimentoDTO;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoGuiaProcedimentoDTOParam;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoGuiaProcedimentoDTOParam.FormaApresentacao;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoGuiaProcedimentoDTOParam.TipoResumo;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Parametro;
import br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento;
import br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimentoItem;
import org.hibernate.Query;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioDetalhamentoGuiaProcedimento extends CommandQuery implements ITransferDataReport<RelatorioDetalhamentoGuiaProcedimentoDTOParam, RelatorioDetalhamentoGuiaProcedimentoDTO> {

    private RelatorioDetalhamentoGuiaProcedimentoDTOParam param;
    private List<RelatorioDetalhamentoGuiaProcedimentoDTO> result;
    
    @Override
    protected void createQuery(HQLHelper hql) {

        HQLHelper hqlSub = hql.getNewInstanceSubQuery();
        try {
            Date dataCompProcedimento = (Date) CargaBasicoPadrao.getInstance().getParametroPadrao().getPropertyValue(Parametro.PROP_DATA_COMPETENCIA_PROCEDIMENTO);

            hqlSub.addToSelect("(case when procedimentoCompetencia.valorServicoAmbulatorial > 0 then procedimentoCompetencia.valorServicoAmbulatorial else procedimentoCompetencia.valorServicoProfissional end)");
            hqlSub.addToFrom("ProcedimentoCompetencia procedimentoCompetencia");
            hqlSub.addToWhereWhithAnd("procedimentoCompetencia.id.procedimento.codigo = procedimento.codigo");
            hqlSub.addToWhereWhithAnd("procedimentoCompetencia.id.dataCompetencia = ", dataCompProcedimento);

        } catch (ValidacaoException e) {
            Loggable.log.error(e.getMessage(), e);
        }

        hql.addToSelectAndGroup("consorcioGuiaProcedimento.codigo", "codigo");
        if (RepositoryComponentDefault.SIM.equals(param.getApresentarChave())) {
            hql.addToSelectAndGroup("consorcioGuiaProcedimento.numeroChave", "numeroChave");
        }
        hql.addToSelectAndGroup("consorcioGuiaProcedimento.nomePaciente", "nomePaciente");
        hql.addToSelectAndGroup("consorciado.codigo", "consorciado.codigo");
        hql.addToSelectAndGroup("consorciado.descricao", "consorciado.descricao");
        hql.addToSelectAndGroup("prestador.codigo", "prestador.codigo");
        hql.addToSelectAndGroup("prestador.descricao", "prestador.descricao");
        hql.addToSelectAndGroup("usuarioCadsus.codigo", "paciente.codigo");
        hql.addToSelectAndGroup("usuarioCadsus.nome", "paciente.nome");
        hql.addToSelectAndGroup("consorcioGuiaProcedimento.status", "status");
        hql.addToSelectAndGroup("cidade.codigo","cidade.codigo");
        hql.addToSelectAndGroup("cidade.descricao","cidade.descricao");
        hql.addToSelectAndGroup("estado.sigla","cidade.estado.sigla");
        hql.addToSelectAndGroup("consorcioGuiaProcedimento."+param.getTipoData().value(), "data");

        hql.addToSelect("sum("
                + "case when consorcioGuiaProcedimentoItem.status = :ITEM_ABERTO"
                + "     then (consorcioGuiaProcedimentoItem.valorProcedimento * consorcioGuiaProcedimentoItem.quantidade) "
                + "     else (case when consorcioGuiaProcedimentoItem.status = :ITEM_CANCELADO"
                + "                then (case when consorcioGuiaProcedimento.status = :GUIA_CANCELADA"
                + "                           then (consorcioGuiaProcedimentoItem.valorProcedimento * consorcioGuiaProcedimentoItem.quantidade)"
                + "                           else 0"
                + "                       end)"
                + "                else (consorcioGuiaProcedimentoItem.valorProcedimento * consorcioGuiaProcedimentoItem.quantidadeAplicacao)"
                + "            end)"
                + " end)", "valor");

        hql.addToSelect("sum("
                + "case when consorcioGuiaProcedimentoItem.status = :ITEM_ABERTO"
                + "     then (coalesce((" + hqlSub.getQuery() + "), 0) * consorcioGuiaProcedimentoItem.quantidade) "
                + "     else (case when consorcioGuiaProcedimentoItem.status = :ITEM_CANCELADO"
                + "                then (case when consorcioGuiaProcedimento.status = :GUIA_CANCELADA"
                + "                           then (coalesce((" + hqlSub.getQuery() + "), 0) * consorcioGuiaProcedimentoItem.quantidade) "
                + "                           else 0"
                + "                       end)"
                + "                else (coalesce((" + hqlSub.getQuery() + "), 0) * consorcioGuiaProcedimentoItem.quantidadeAplicacao)"
                + "            end)"
                + " end)", "valorSUS");

        hql.setTypeSelect(RelatorioDetalhamentoGuiaProcedimentoDTO.class.getName());
        hql.addToFrom("ConsorcioGuiaProcedimentoItem consorcioGuiaProcedimentoItem"
                + " left join consorcioGuiaProcedimentoItem.consorcioGuiaProcedimento consorcioGuiaProcedimento"
                + " left join consorcioGuiaProcedimentoItem.consorcioProcedimento consorcioProcedimento"
                + " left join consorcioProcedimento.procedimento procedimento"
                + " left join consorcioGuiaProcedimento.usuarioCadsus usuarioCadsus"
                + " left join consorcioGuiaProcedimento.subConta subConta"
                + " left join consorcioGuiaProcedimento.cidade cidade"
                + " left join cidade.estado estado"
                + " left join subConta.conta conta"
                + " left join subConta.tipoConta tipoConta"
                + " left join conta.consorciado consorciado"
                + " left join consorcioGuiaProcedimento.consorcioPrestador consorcioPrestador"
                + " left join consorcioPrestador.empresaPrestador prestador");
        
        hql.addToWhereWhithAnd("consorciado =", param.getConsorciado());
        hql.addToWhereWhithAnd("consorcioGuiaProcedimento.status in ", param.getInSituacao());
        hql.addToWhereWhithAnd("usuarioCadsus =", param.getPaciente());
        if (param.getNomePaciente() != null) {
            hql.addToWhereWhithAnd("(" + hql.getConsultaLiked(" consorcioGuiaProcedimento.nomePaciente", param.getNomePaciente(), true)
                    + " OR " + hql.getConsultaLiked("usuarioCadsus.nome", param.getNomePaciente(), true)
                    + " OR (usuarioCadsus.utilizaNomeSocial = 1 AND " + hql.getConsultaLiked("usuarioCadsus.apelido", param.getNomePaciente(), true) + "))");
        }
        hql.addToWhereWhithAnd("consorcioGuiaProcedimento."+param.getTipoData().value(), param.getPeriodo());
        hql.addToWhereWhithAnd("prestador =", param.getPrestador());
        hql.addToWhereWhithAnd("cidade = ", param.getCidade());
        hql.addToWhereWhithAnd("tipoConta = ", param.getTipoConta());

        if(FormaApresentacao.CONSORCIADO.equals(param.getFormaApresentacao())){
            hql.addToOrder("consorciado.descricao");
        }else if(FormaApresentacao.DIARIO.equals(param.getFormaApresentacao())){
            hql.addToOrder("year(consorcioGuiaProcedimento."+param.getTipoData().value()+")");
            hql.addToOrder("month(consorcioGuiaProcedimento."+param.getTipoData().value()+")");
            hql.addToOrder("day(consorcioGuiaProcedimento."+param.getTipoData().value()+")");
        }else if(FormaApresentacao.MENSAL.equals(param.getFormaApresentacao())){
            hql.addToOrder("year(consorcioGuiaProcedimento."+param.getTipoData().value()+")");
            hql.addToOrder("month(consorcioGuiaProcedimento."+param.getTipoData().value()+")");
        }else if(FormaApresentacao.PRESTADOR.equals(param.getFormaApresentacao())){
            hql.addToOrder("prestador.descricao");
        }else if(FormaApresentacao.SITUACAO.equals(param.getFormaApresentacao())){
            hql.addToOrder("consorcioGuiaProcedimento.status");
        }else if(FormaApresentacao.CIDADE.equals(param.getFormaApresentacao())){
            hql.addToOrder("cidade.descricao");
        }
        
        if(TipoResumo.CONSORCIADO.equals(param.getTipoResumo())){
            hql.addToOrder("consorciado.descricao");
        }else if(TipoResumo.DIARIO.equals(param.getTipoResumo())){
            hql.addToOrder("year(consorcioGuiaProcedimento."+param.getTipoData().value()+")");
            hql.addToOrder("month(consorcioGuiaProcedimento."+param.getTipoData().value()+")");
            hql.addToOrder("day(consorcioGuiaProcedimento."+param.getTipoData().value()+")");
        }else if(TipoResumo.MENSAL.equals(param.getTipoResumo())){
            hql.addToOrder("year(consorcioGuiaProcedimento."+param.getTipoData().value()+")");
            hql.addToOrder("month(consorcioGuiaProcedimento."+param.getTipoData().value()+")");
        }else if(TipoResumo.PRESTADOR.equals(param.getTipoResumo())){
            hql.addToOrder("prestador.descricao");
        }else if(TipoResumo.SITUACAO.equals(param.getTipoResumo())){
            hql.addToOrder("consorcioGuiaProcedimento.status");
        }else if (TipoResumo.CIDADE.equals(param.getTipoResumo())){
            hql.addToOrder("cidade.descricao");
        }
        
        if(RelatorioDetalhamentoGuiaProcedimentoDTOParam.Ordenacao.CONSORCIADO.equals(param.getOrdenacao())){
            hql.addToOrder("consorciado.descricao "+param.getTipoOrdenacao());
        }else if(RelatorioDetalhamentoGuiaProcedimentoDTOParam.Ordenacao.DATA.equals(param.getOrdenacao())){
            hql.addToOrder("consorcioGuiaProcedimento."+param.getTipoData().value()+" "+param.getTipoOrdenacao());
        }else if(RelatorioDetalhamentoGuiaProcedimentoDTOParam.Ordenacao.NUMERO_GUIA.equals(param.getOrdenacao())){
            hql.addToOrder("consorcioGuiaProcedimento.codigo "+param.getTipoOrdenacao());
        }else if(RelatorioDetalhamentoGuiaProcedimentoDTOParam.Ordenacao.PACIENTE.equals(param.getOrdenacao())){
            hql.addToOrder("consorcioGuiaProcedimento.nomePaciente "+param.getTipoOrdenacao());
        }else if(RelatorioDetalhamentoGuiaProcedimentoDTOParam.Ordenacao.PRESTADOR.equals(param.getOrdenacao())){
            hql.addToOrder("prestador.descricao "+param.getTipoOrdenacao());
        }else if(RelatorioDetalhamentoGuiaProcedimentoDTOParam.Ordenacao.SITUACAO.equals(param.getOrdenacao())){
            hql.addToOrder("consorcioGuiaProcedimento.status "+param.getTipoOrdenacao());
        }
        
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setParameter("ITEM_ABERTO", ConsorcioGuiaProcedimentoItem.StatusGuiaProcedimentoItem.ABERTA.value());
        query.setParameter("ITEM_CANCELADO", ConsorcioGuiaProcedimentoItem.StatusGuiaProcedimentoItem.CANCELADA.value());
        query.setParameter("GUIA_CANCELADA", ConsorcioGuiaProcedimento.StatusGuiaProcedimento.CANCELADA.value());
    }

    @Override
    public Collection getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List)result);
    }
    
    @Override
    public void setDTOParam(RelatorioDetalhamentoGuiaProcedimentoDTOParam param) {
        this.param = param;
    }
}
