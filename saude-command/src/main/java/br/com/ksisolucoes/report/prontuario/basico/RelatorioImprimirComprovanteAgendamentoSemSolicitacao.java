/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.prontuario.basico;

import br.com.celk.util.Util;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.integracao.util.AgendamentoUtils;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioImprimirComprovanteAgendamentoDTOParam;
import br.com.ksisolucoes.report.prontuario.basico.query.QueryRelatorioImprimirComprovanteAgendamentoSemSolicitacao;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEndereco;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.enderecoestruturado.EnderecoEstruturado;
import br.com.ksisolucoes.vo.enderecoestruturado.EnderecoEstruturadoLogradouro;
import br.com.ksisolucoes.vo.esus.helper.EsusIntegracaoHelper;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Criteria;
import org.hibernate.criterion.Restrictions;

import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.ksisolucoes.server.HibernateSessionFactory.getSession;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImprimirComprovanteAgendamentoSemSolicitacao extends AbstractReport<RelatorioImprimirComprovanteAgendamentoDTOParam> {

    private Empresa empresa;
    private Profissional profissional;
    private Usuario usuario;
    private List<EquipeProfissional> equipesProfissional;
    private UsuarioCadsusEndereco usuarioCadsusEndereco;
    private EnderecoEstruturado enderecoEstruturado;
    private UsuarioCadsus usuarioCadsus;

    public RelatorioImprimirComprovanteAgendamentoSemSolicitacao(RelatorioImprimirComprovanteAgendamentoDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {

        String modeloImpressaoComprovante = getParametroGem(Modulos.AGENDAMENTO,"ModeloImpressaoComprovante");
        try {
            // Impressora Térmica
            if ("T".equals(modeloImpressaoComprovante)) {
                return "/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_impressao_comprovante_agendamento_sem_solicitacao_termica.jrxml";
            }

            if (param.isPacienteSemCadastro()) {
                return "/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_impressao_comprovante_agendamento_sem_cadastro.jrxml";
            }

            Long codigoTipoProcedimento = null;
            if (getParam().getAgendaGradeAtendimentoHorario() != null) {
                codigoTipoProcedimento = getParam().getAgendaGradeAtendimentoHorario().getTipoProcedimento().getCodigo();
            } else if (CollectionUtils.isNotNullEmpty(getParam().getAgendaGradeAtendimentoHorarioList())) {
                codigoTipoProcedimento = getParam().getAgendaGradeAtendimentoHorarioList().get(0).getTipoProcedimento().getCodigo();
            }

            if (codigoTipoProcedimento != null) {
                Criteria criteria = getSession().createCriteria(TipoProcedimento.class)
                        .add(Restrictions.eq(TipoProcedimento.PROP_CODIGO, codigoTipoProcedimento));
                TipoProcedimento tipoProcedimento = (TipoProcedimento) criteria.uniqueResult();
                if (tipoProcedimento.getFlagFichaCadastro().equals(RepositoryComponentDefault.SIM_LONG)) {
                    return "/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_impressao_comprovante_agendamento_sem_solicitacao_com_ficha.jrxml";
                }
            }
        } catch (DAOException ex) {
            Logger.getLogger(RelatorioImprimirComprovanteAgendamento.class.getName()).log(Level.SEVERE, null, ex);
        }

        String comprovanteAgendamentoA5 = "/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_impressao_comprovante_agendamento_sem_solicitacao.jrxml";
        String comprovanteAgendamentoA4 = "/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_impressao_comprovante_agendamento_sem_solicitacao_a4.jrxml";

        return "A5".equals(modeloImpressaoComprovante) ? comprovanteAgendamentoA5 : comprovanteAgendamentoA4;
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_comprovante_de_agendamento");
    }

    @Override
    protected void customDTOParam(RelatorioImprimirComprovanteAgendamentoDTOParam param) throws ValidacaoException {
        Parametro parametro = CargaBasicoPadrao.getInstance().getParametroPadrao();
        this.addParametro("nomePrefeitura", parametro.getPropertyValue(Parametro.PROP_NOME_PREFEITURA));
        this.addParametro("descricaoEmpresa", SessaoAplicacaoImp.getInstance().<Empresa>getEmpresa().getDescricao());
        this.addParametro("nomeEmpresaRelatorio", getNomeEmpresaRelatorio());
        this.addParametro("nomeUsuario", getNomeUsuario());
        this.addParametro("titulo2", Bundle.getStringApplication("rotulo_ficha_cadastral"));
        this.addParametro("modelComprovanteAgendamento", getParametroGem(Modulos.GERAL, "ModeloComprovanteAgendamento"));
        this.addParametro("mensagemPadraoComprovanteAgendamento", getParametroGem(Modulos.AGENDAMENTO, "MensagemPadraoComprovanteAgendamento"));

        try {
            this.addParametro("utilizaEnderecoEstruturado", getUtilizaEnderecoEstruturado());
            this.addParametro("equipeReferenciaEnderecoEstruturado", AgendamentoUtils.getEquipeReferenciaEnderecoEstruturado(getUsuarioCadsus(), getUtilizaEnderecoEstruturado()));
        } catch (DAOException e) {
            throw new RuntimeException(e);
        }
    }

    private UsuarioCadsus getUsuarioCadsus() throws DAOException {
        if (usuarioCadsus == null) {
            if (param.getSolicitacaoAgendamento() != null && param.getSolicitacaoAgendamento().getUsuarioCadsus() != null) {
                usuarioCadsus = param.getSolicitacaoAgendamento().getUsuarioCadsus();
            } else if (!param.getAgendaGradeAtendimentoHorarioList().isEmpty()) {
                usuarioCadsus = (UsuarioCadsus) getSession().get(UsuarioCadsus.class, param.getAgendaGradeAtendimentoHorarioList().get(0).getUsuarioCadsus().getCodigo());
            }
        }

        return usuarioCadsus;
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioImprimirComprovanteAgendamentoSemSolicitacao();
    }

    public String getNomeEmpresaRelatorio() {
        Empresa empresa = SessaoAplicacaoImp.getInstance().getEmpresa();

        if (StringUtils.trimToNull(empresa.getNomeRelatorio()) != null) {
            return empresa.getNomeRelatorio();
        }

        return empresa.getDescricao();
    }

    public String getNomeUsuario() {
        Usuario usuario = SessaoAplicacaoImp.getInstance().getUsuario();

        return usuario.getNome();
    }

    private String getParametroGem(Modulos modulo, String parametro) {
        try {
            return BOFactory.getBO(CommomFacade.class).modulo(modulo).getParametro(parametro);
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage());
        }
        return null;
    }

    public Profissional getProfissional() {
        if(profissional == null){
            profissional = SessaoAplicacaoImp.getInstance().getUsuario().getProfissional();
        }

        return profissional;
    }

    public Usuario getUsuario() {
        if(usuario == null){
            usuario = SessaoAplicacaoImp.getInstance().getUsuario();
        }

        return usuario;
    }

    private Empresa getEmpresa() {
        if(empresa == null){
            empresa = SessaoAplicacaoImp.getInstance().getEmpresa();
        }

        return empresa;
    }

    private UsuarioCadsusEndereco getUsuarioCadsusEndereco() {
        if (usuarioCadsusEndereco == null &&
            Util.isNotNull(param.getSolicitacaoAgendamento()) &&
            Util.isNotNull(param.getSolicitacaoAgendamento().getUsuarioCadsus())) {
            UsuarioCadsusEndereco usuarioCadsusEnderecoProxy = on(UsuarioCadsusEndereco.class);

            usuarioCadsusEndereco = LoadManager.getInstance(UsuarioCadsusEndereco.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(path(usuarioCadsusEnderecoProxy.getRoUsuarioCadsus()), param.getSolicitacaoAgendamento().getUsuarioCadsus()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(usuarioCadsusEnderecoProxy.getStatus()), BuilderQueryCustom.QueryParameter.DIFERENTE, UsuarioCadsusEndereco.STATUS_CANCELADO, HQLHelper.NOT_RESOLVE_TYPE, UsuarioCadsusEndereco.STATUS_ABERTO))
                    .addProperties(new HQLProperties(EnderecoUsuarioCadsus.class, path(usuarioCadsusEnderecoProxy.getId().getEndereco())).getProperties())
                    .setMaxResults(1)
                    .start().getVO();
        }

        return usuarioCadsusEndereco;
    }

    private EnderecoEstruturado getEnderecoEstruturado() {
        if (Util.isNotNull(getUsuarioCadsusEndereco()) && Util.isNotNull(getUsuarioCadsusEndereco().getId()) && Util.isNotNull(getUsuarioCadsusEndereco().getId().getEndereco().getEnderecoEstruturado()) && enderecoEstruturado == null) {
            EnderecoEstruturado enderecoEstruturadoProxy = on(EnderecoEstruturado.class);

            enderecoEstruturado = LoadManager.getInstance(EnderecoEstruturado.class)
                    .addProperties(new HQLProperties(EnderecoEstruturado.class).getProperties())
                    .addProperties(new HQLProperties(Bairro.class, path(enderecoEstruturadoProxy.getBairro())).getProperties())
                    .addProperties(new HQLProperties(EquipeMicroArea.class, path(enderecoEstruturadoProxy.getEquipeMicroArea())).getProperties())
                    .addProperties(new HQLProperties(EnderecoEstruturadoLogradouro.class, path(enderecoEstruturadoProxy.getEnderecoEstruturadoLogradouro())).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(path(enderecoEstruturadoProxy.getCodigo()), getUsuarioCadsusEndereco().getId().getEndereco().getEnderecoEstruturado().getCodigo()))
                    .start().getVO();
        }

        return enderecoEstruturado;
    }

    private Boolean getUtilizaEnderecoEstruturado() throws DAOException {
        return RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("utilizaEnderecoEstruturado"));
    }

    public String getEquipeReferenciaEnderecoEstruturado() throws DAOException {
        // Mesma regra implementada no CadastroPacientePanel para busca do endereço estruturado
        Boolean exigeEquipeAcompanhamento = RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("ExigeEquipeAcompanhamento"));

        try {
            getEquipeProfisionalLogado();
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage());
        }

        if(!getEquipeProfisionalLogado().isEmpty() &&
            exigeEquipeAcompanhamento &&
            getUtilizaEnderecoEstruturado() &&
            param.getSolicitacaoAgendamento() != null &&
            param.getSolicitacaoAgendamento().getUsuarioCadsus() == null){
            Equipe equipeProfisional = getEquipeProfisionalLogado().get(0).getEquipe();
            return Equipe.TIPOS_SAUDE_DA_FAMILIA.contains(equipeProfisional.getTipoEquipe().getCodigo()) ? equipeProfisional.getReferencia() : null;
        }
        else if (Util.isNotNull(param.getSolicitacaoAgendamento()) &&
                Util.isNotNull(param.getSolicitacaoAgendamento().getUsuarioCadsus()) &&
                Util.isNotNull(param.getSolicitacaoAgendamento().getUsuarioCadsus().getEquipe()) &&
                Util.isNotNull(param.getSolicitacaoAgendamento().getUsuarioCadsus().getEquipe().getReferencia()))
            return param.getSolicitacaoAgendamento().getUsuarioCadsus().getEquipe().getReferencia();
        else if (Util.isNotNull(getEnderecoEstruturado())  &&
                Util.isNotNull(getEnderecoEstruturado().getEquipeMicroArea()) &&
                Util.isNotNull(getEnderecoEstruturado().getEquipeMicroArea().getEquipeProfissional()) &&
                Util.isNotNull(getEnderecoEstruturado().getEquipeMicroArea().getEquipeProfissional().getEquipe()))
            return getEnderecoEstruturado().getEquipeMicroArea().getEquipeProfissional().getEquipe().getReferencia();
        return null;
    }

    private List<EquipeProfissional> getEquipeProfisionalLogado() throws DAOException {
        if (equipesProfissional == null) {
            equipesProfissional = EsusIntegracaoHelper.findEquipesProfissional(getProfissional(), getEmpresa());
        }

        return equipesProfissional;
    }
}
