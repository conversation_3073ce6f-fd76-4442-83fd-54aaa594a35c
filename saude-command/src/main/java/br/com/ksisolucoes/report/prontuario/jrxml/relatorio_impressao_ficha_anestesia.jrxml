<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_encaminhamento" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="8727c5ee-dacc-4501-9bff-1afe095fc3c3">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.AtendimentoAnamnese"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="convenio" class="br.com.ksisolucoes.vo.prontuario.basico.Convenio"/>
	<field name="leitoQuarto" class="br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto"/>
	<field name="quartoInternacao" class="br.com.ksisolucoes.vo.prontuario.hospital.QuartoInternacao">
		<fieldDescription><![CDATA[leitoQuarto.quartoInternacao]]></fieldDescription>
	</field>
	<field name="dataAtendimento" class="java.util.Date"/>
	<background>
		<band height="802" splitType="Stretch">
			<image>
				<reportElement uuid="20e683dd-81a7-4a6c-8246-a48e8c0ba741" x="14" y="87" width="529" height="715"/>
				<imageExpression><![CDATA["/br/com/ksisolucoes/imagens/anestesia.png"]]></imageExpression>
			</image>
		</band>
	</background>
	<columnHeader>
		<band height="99" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement uuid="93710bb9-5f98-4a86-a16d-fd6e659e2f60" mode="Transparent" x="17" y="17" width="233" height="12"/>
				<box leftPadding="2"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empresa}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="93710bb9-5f98-4a86-a16d-fd6e659e2f60" mode="Transparent" x="17" y="45" width="233" height="12"/>
				<box leftPadding="2"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNome()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="93710bb9-5f98-4a86-a16d-fd6e659e2f60" mode="Transparent" x="17" y="70" width="233" height="12"/>
				<box leftPadding="2"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{convenio}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="93710bb9-5f98-4a86-a16d-fd6e659e2f60" mode="Transparent" x="260" y="70" width="64" height="12"/>
				<box leftPadding="2"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getDescricaoIdadeSimples()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="93710bb9-5f98-4a86-a16d-fd6e659e2f60" mode="Transparent" x="332" y="70" width="72" height="12"/>
				<box leftPadding="2"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quartoInternacao}.getDescricao()+" / "+$F{leitoQuarto}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement uuid="93710bb9-5f98-4a86-a16d-fd6e659e2f60" mode="Transparent" x="420" y="70" width="72" height="12"/>
				<box leftPadding="2"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataAtendimento}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="93710bb9-5f98-4a86-a16d-fd6e659e2f60" mode="Transparent" x="412" y="45" width="72" height="12"/>
				<box leftPadding="2"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getCodigo()]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
</jasperReport>
