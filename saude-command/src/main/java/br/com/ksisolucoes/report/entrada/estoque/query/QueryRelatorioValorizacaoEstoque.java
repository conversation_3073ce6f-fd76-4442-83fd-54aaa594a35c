package br.com.ksisolucoes.report.entrada.estoque.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioValorizacaoEstoqueDTO;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioValorizacaoEstoqueDTOParam;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;

import java.util.List;
import java.util.Map;

public class QueryRelatorioValorizacaoEstoque extends CommandQuery<QueryRelatorioValorizacaoEstoque> implements ITransferDataReport<RelatorioValorizacaoEstoqueDTOParam, RelatorioValorizacaoEstoqueDTO> {

    private List<RelatorioValorizacaoEstoqueDTO> result;
    private RelatorioValorizacaoEstoqueDTOParam param;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect(" sum(estoqueEmpresa.estoqueFisico)", "quantidadeTotalEstoque");
        hql.addToSelect(" coalesce(sum(estoqueEmpresa.estoqueFisico * estoqueEmpresa.precoMedio), 0)", "valorTotalEstoque");
        
        if (RepositoryComponentDefault.SIM.equals(this.param.getAgruparUnidade())) {
            hql.addToGroup(" empresa.codigo");
            hql.addToSelectAndGroup(" empresa.referencia ", "codigoEmpresa");
            hql.addToSelectAndGroup(" empresa.descricao ", "descricaoEmpresa");
        }

        if (this.param.getFormaApresentacao() == ReportProperties.AGRUPAR_PRODUTO) {
            hql.addToGroup(" produto.codigo "); 
            hql.addToSelectAndGroup(" produto.referencia ", "codigoProduto"); 
            hql.addToSelectAndGroup(" produto.descricao ", "descricaoProduto"); 
        } else if (this.param.getFormaApresentacao() == ReportProperties.AGRUPAR_GRUPO) {
            hql.addToSelectAndGroup(" roGrupoProduto.codigo ", "codigoGrupoProduto");
            hql.addToSelectAndGroup(" roGrupoProduto.descricao ", "descricaoGrupoProduto");
            hql.addToSelectAndGroup(" subGrupo.id.codigo ", "codigoSubGrupoProduto");
            hql.addToGroup(" subGrupo.id.codigoGrupoProduto ");
            hql.addToSelectAndGroup(" subGrupo.descricao ", "descricaoSubGrupoProduto");
        } 

        hql.setTypeSelect(RelatorioValorizacaoEstoqueDTO.class.getName());

        hql.addToFrom("EstoqueEmpresa estoqueEmpresa "
                + " left join estoqueEmpresa.id.produto produto"
                + " left join produto.subGrupo subGrupo"
                + " left join subGrupo.roGrupoProduto roGrupoProduto"
                + " left join estoqueEmpresa.id.empresa empresa");


        hql.addToWhereWhithAnd("empresa ", param.getEmpresa());
        hql.addToWhereWhithAnd("produto ", param.getProduto());
        hql.addToWhereWhithAnd("roGrupoProduto ", this.param.getGrupoProduto());
        
        hql.addToWhereWhithAnd("estoqueEmpresa.estoqueFisico > 0 ");

        if (RepositoryComponentDefault.SIM.equals(this.param.getAgruparUnidade())) {
            hql.addToOrder("empresa.descricao ");
        }
        
        hql.addToOrder(" 2 desc "); 
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioValorizacaoEstoqueDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(RelatorioValorizacaoEstoqueDTOParam param) {
        this.param = param;
    }

}
