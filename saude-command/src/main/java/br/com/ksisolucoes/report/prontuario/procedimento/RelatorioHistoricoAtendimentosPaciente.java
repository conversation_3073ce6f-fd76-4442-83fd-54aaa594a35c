/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.prontuario.procedimento;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioHistoricoAtendimentosPacienteDTOParam;
import br.com.ksisolucoes.report.prontuario.procedimento.query.QueryRelatorioHistoricoAtendimentosPaciente;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioHistoricoAtendimentosPaciente extends AbstractReport<RelatorioHistoricoAtendimentosPacienteDTOParam> {

    public RelatorioHistoricoAtendimentosPaciente(RelatorioHistoricoAtendimentosPacienteDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/unidadesaude/jrxml/relatorio_historico_atendimentos_paciente.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_historico_atendimentos_paciente");
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioHistoricoAtendimentosPaciente();
    }
    
}
