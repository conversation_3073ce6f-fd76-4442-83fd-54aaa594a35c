/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.prontuario.procedimento.query;

import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioProcedimentoModalidadeParam;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoModalidade;
import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioProcedimentoModalidade extends CommandQuery implements ITransferDataReport<RelatorioProcedimentoModalidadeParam, ProcedimentoModalidade>{

    private RelatorioProcedimentoModalidadeParam dTOParam;
    private List<ProcedimentoModalidade> procedimentoModalidades;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.setTypeSelect(ProcedimentoModalidade.class.getName());

        hql.addToSelect("pm.id.procedimentoCompetencia.id.procedimento.codigo",true);
        hql.addToSelect("pm.id.procedimentoCompetencia.id.procedimento.descricao",true);
        hql.addToSelect("pm.id.procedimentoModalidadeCadastro.codigo",true);
        hql.addToSelect("pm.id.procedimentoModalidadeCadastro.descricao",true);
        hql.addToSelect("pm.id.procedimentoCompetencia.id.dataCompetencia",true);

        hql.addToFrom(ProcedimentoModalidade.class.getName(),"pm");

        hql.addToWhereWhithAnd("pm.id.procedimentoCompetencia.id.procedimento in ",this.dTOParam.getProcedimentos());
        hql.addToWhereWhithAnd("pm.id.procedimentoModalidadeCadastro in",this.dTOParam.getProcedimentoModalidadeCadastros());

        hql.addToOrder("pm.id.procedimentoCompetencia.id.procedimento.codigo");
        hql.addToOrder("pm.id.procedimentoModalidadeCadastro.codigo");

    }

    public void setDTOParam(RelatorioProcedimentoModalidadeParam arg0) {
        this.dTOParam = arg0;
    }

    public Collection<ProcedimentoModalidade> getResult() {
        return this.procedimentoModalidades;
    }

    protected void result(HQLHelper hql, Object result) {
        this.procedimentoModalidades =  hql.getBeanList((List)result);
    }

}
