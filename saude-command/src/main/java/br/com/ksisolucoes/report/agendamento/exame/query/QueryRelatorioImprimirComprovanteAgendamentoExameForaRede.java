/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.agendamento.exame.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioImpressaoComprovanteAgendamentoExameForaRedeDTO;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioImpressaoComprovanteAgendamentoExameForaRedeDTOParam;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioImprimirComprovanteAgendamentoExameForaRede extends CommandQuery implements ITransferDataReport<RelatorioImpressaoComprovanteAgendamentoExameForaRedeDTOParam,RelatorioImpressaoComprovanteAgendamentoExameForaRedeDTO>{


    private RelatorioImpressaoComprovanteAgendamentoExameForaRedeDTOParam param;
    private List<RelatorioImpressaoComprovanteAgendamentoExameForaRedeDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.setTypeSelect(RelatorioImpressaoComprovanteAgendamentoExameForaRedeDTO.class.getName());

        hql.addToSelect("profissional.codigo", "exameRequisicao.exame.profissional.codigo");
        hql.addToSelect("profissional.referencia", "exameRequisicao.exame.profissional.referencia");
        hql.addToSelect("profissional.nome", "exameRequisicao.exame.profissional.nome");
        
        hql.addToSelect("empresaSolicitante.codigo", "exameRequisicao.exame.empresaSolicitante.codigo");
        hql.addToSelect("empresaSolicitante.referencia", "exameRequisicao.exame.empresaSolicitante.referencia");
        hql.addToSelect("empresaSolicitante.descricao", "exameRequisicao.exame.empresaSolicitante.descricao");
        hql.addToSelect("empresaSolicitante.sigla", "exameRequisicao.exame.empresaSolicitante.sigla");
        hql.addToSelect("empresaSolicitante.cnes", "exameRequisicao.exame.empresaSolicitante.cnes");

        hql.addToSelect("tipoExame.codigo", "exameRequisicao.exame.tipoExame.codigo");
        hql.addToSelect("tipoExame.descricao", "exameRequisicao.exame.tipoExame.descricao");

        hql.addToSelect("usuarioCadsus.codigo", "exameRequisicao.exame.usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "exameRequisicao.exame.usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.sexo", "exameRequisicao.exame.usuarioCadsus.sexo");
        hql.addToSelect("usuarioCadsus.dataNascimento", "exameRequisicao.exame.usuarioCadsus.dataNascimento");
        hql.addToSelect("usuarioCadsus.cpf", "exameRequisicao.exame.usuarioCadsus.cpf");
        hql.addToSelect("usuarioCadsus.rg", "exameRequisicao.exame.usuarioCadsus.rg");
        hql.addToSelect("usuarioCadsus.nomeMae", "exameRequisicao.exame.usuarioCadsus.nomeMae");
        hql.addToSelect("usuarioCadsus.telefone", "exameRequisicao.exame.usuarioCadsus.telefone");
        
        hql.addToSelect("exameProcedimento.codigo", "exameRequisicao.exameProcedimento.codigo");
        hql.addToSelect("exameProcedimento.procedimento.codigo", "exameRequisicao.exameProcedimento.procedimento.codigo");
        hql.addToSelect("exameProcedimento.descricaoProcedimento", "exameRequisicao.exameProcedimento.descricaoProcedimento");
        
        hql.addToSelect("exameRequisicao.codigo", "exameRequisicao.codigo");
        
        hql.addToSelect("exame.codigo", "exameRequisicao.exame.codigo");
        hql.addToSelect("exame.numeroProtocoloAutorizacao", "exameRequisicao.exame.numeroProtocoloAutorizacao");
        hql.addToSelect("exame.dataCadastro", "exameRequisicao.exame.dataCadastro");
        hql.addToSelect("exame.dataAutorizacao", "exameRequisicao.exame.dataAutorizacao");

        hql.addToSelect("exame.dataAgendamento", "exameRequisicao.exame.dataAgendamento");

        hql.addToSelect("usuarioAutorizacao.codigo", "exameRequisicao.exame.usuarioAutorizacao.codigo");
        hql.addToSelect("usuarioAutorizacao.nome", "exameRequisicao.exame.usuarioAutorizacao.nome");
        
        hql.addToSelect("localExame.codigo", "exameRequisicao.exame.localExame.codigo");
        hql.addToSelect("localExame.referencia", "exameRequisicao.exame.localExame.referencia");
        hql.addToSelect("localExame.cnes", "exameRequisicao.exame.localExame.cnes");
        hql.addToSelect("localExame.descricao", "exameRequisicao.exame.localExame.descricao");
        hql.addToSelect("localExame.rua", "exameRequisicao.exame.localExame.rua");
        hql.addToSelect("localExame.numero", "exameRequisicao.exame.localExame.numero");
        hql.addToSelect("localExame.complemento", "exameRequisicao.exame.localExame.complemento");
        hql.addToSelect("localExame.telefone", "exameRequisicao.exame.localExame.telefone");
        hql.addToSelect("localExame.bairro", "exameRequisicao.exame.localExame.bairro");

        hql.addToSelect("cidadeLocalExame.codigo", "exameRequisicao.exame.localExame.cidade.codigo");
        hql.addToSelect("cidadeLocalExame.descricao", "exameRequisicao.exame.localExame.cidade.descricao");

        hql.addToSelect("(select min(ucc.numeroCartao) from UsuarioCadsusCns ucc where ucc.usuarioCadsus = usuarioCadsus and ucc.excluido = 0 )","usuarioCadsusCns.numeroCartao");

        hql.addToFrom("ExameRequisicao exameRequisicao"
                + " left join exameRequisicao.exame exame"
                + " left join exame.tipoExame tipoExame"
                + " left join exameRequisicao.exameProcedimento exameProcedimento"
                + " left join exame.localExame localExame"
                + " left join localExame.cidade cidadeLocalExame"
                + " left join exame.usuarioCadsus usuarioCadsus"
                + " left join exame.profissional profissional"
                + " left join exame.usuarioAutorizacao usuarioAutorizacao"
                + " left join exame.empresaSolicitante empresaSolicitante");

        hql.addToWhereWhithAnd("exame.codigo = ", this.param.getCodigoExame());

        hql.addToOrder("exame.codigo");
        hql.addToOrder("exameRequisicao.codigo");
        hql.addToOrder("exameProcedimento.codigo");
    }

    @Override
    public void setDTOParam(RelatorioImpressaoComprovanteAgendamentoExameForaRedeDTOParam t) {
        this.param = t;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String,Object>>)result);
    }
    
    @Override
    public Collection<RelatorioImpressaoComprovanteAgendamentoExameForaRedeDTO> getResult() {
        return result;
    }

}
