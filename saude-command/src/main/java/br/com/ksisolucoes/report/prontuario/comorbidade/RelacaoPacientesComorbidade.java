package br.com.ksisolucoes.report.prontuario.comorbidade;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.celk.unidadesaude.doenca.interfaces.dto.QueryRelatorioPacienteCondicaoSituacaoSaudeDTOParam;
import br.com.ksisolucoes.report.prontuario.comorbidade.query.QueryRelatorioPacienteCondicaoSituacaoSaude;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelacaoPacientesComorbidade extends AbstractReport<QueryRelatorioPacienteCondicaoSituacaoSaudeDTOParam> {

    public RelacaoPacientesComorbidade(QueryRelatorioPacienteCondicaoSituacaoSaudeDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/prontuario/comorbidade/jrxml/relacao_pacientes_condicao_situacao_saude.jrxml";
    }

    @Override
    public ITransferDataReport getQuery() {
        this.addParametro("FORMA_APRESENTACAO", this.getParam().getFormaApresentacao());
        this.addParametro("IMPRIMIR_ATENDIMENTO", param.getImprimirDataultimoAtendimento());
        return new QueryRelatorioPacienteCondicaoSituacaoSaude();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relatorio_paciente_condicao_situacao_saude");
    }
}
