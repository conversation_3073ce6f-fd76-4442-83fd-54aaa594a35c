package br.com.ksisolucoes.report.unidadesaude.relatorio.query;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioRiscoCardiovascularDTO;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioRiscoCardiovascularDTOParam;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Doenca;
import br.com.ksisolucoes.vo.cadsus.VisitaDomiciliar;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;
import org.hibernate.Query;
import org.hibernate.Session;

import java.util.*;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioRiscoCardiovascular extends CommandQuery<QueryRelatorioRiscoCardiovascular> implements ITransferDataReport<RelatorioRiscoCardiovascularDTOParam, RelatorioRiscoCardiovascularDTO>{

    private RelatorioRiscoCardiovascularDTOParam param;
    private List<RelatorioRiscoCardiovascularDTO> result;

    @Override
    public void setDTOParam(RelatorioRiscoCardiovascularDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(RelatorioRiscoCardiovascularDTO.class.getName());

        hql.addToSelect("ucd.codigo", "usuarioCadsusDoenca.codigo");
        hql.addToSelect("doenca.codigo", "usuarioCadsusDoenca.doenca.codigo");
        hql.addToSelect("doenca.descricao", "usuarioCadsusDoenca.doenca.descricao");
        hql.addToSelect("doencaPrincipal.codigo", "usuarioCadsusDoenca.doenca.doencaPrincipal.codigo");
        hql.addToSelect("doencaPrincipal.descricao", "usuarioCadsusDoenca.doenca.doencaPrincipal.descricao");
        hql.addToSelect("ucs.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("ucs.nome", "usuarioCadsus.nome");
        hql.addToSelect("ucs.sexo", "usuarioCadsus.sexo");
        hql.addToSelect("ucs.dataNascimento", "usuarioCadsus.dataNascimento");
        hql.addToSelect("ema.microArea","usuarioCadsus.enderecoDomicilio.equipeMicroArea.microArea");
        hql.addToSelect("ea.codigo","usuarioCadsus.enderecoDomicilio.equipeMicroArea.equipeArea.codigo");
        hql.addToSelect("ea.descricao","usuarioCadsus.enderecoDomicilio.equipeMicroArea.equipeArea.descricao");
        hql.addToSelect("edu.enderecoInterno","usuarioCadsus.enderecoDomicilio.enderecoUsuarioCadsus.enderecoInterno");
        hql.addToSelect("empresa","usuarioCadsus.empresaResponsavel");

        hql.addToSelect("(select max(at.dataAtendimento) from Atendimento at where at.usuarioCadsus.codigo = ucs.codigo"
                + " and at.status in ("  + Atendimento.STATUS_FINALIZADO + "))", "dataUltimoAtendimento");

        hql.addToSelect("(select max(vd.dataVisita) from VisitaDomiciliar vd where vd.usuarioCadsus.codigo = ucs.codigo"
                + " and vd.situacao in ("  + VisitaDomiciliar.Situacao.CADASTRADO.value() + "))", "dataUltimaVisita");

        hql.addToSelect("(SELECT ap.imc"
                + "         FROM AtendimentoPrimario ap"
                + "        WHERE ap.codigo = (SELECT max(app.codigo) FROM AtendimentoPrimario app JOIN app.atendimento atd WHERE atd.usuarioCadsus.codigo = ucs.codigo))"
                , "imc");

        hql.addToSelect("uce.codigo","usuarioCadsusEsus.codigo");
        hql.addToSelect("uce.doencaCardiaca","usuarioCadsusEsus.doencaCardiaca");
        hql.addToSelect("uce.temHipertensao","usuarioCadsusEsus.temHipertensao");
        hql.addToSelect("uce.temDiabetes","usuarioCadsusEsus.temDiabetes");
        hql.addToSelect("uce.teveInfarto","usuarioCadsusEsus.teveInfarto");
        hql.addToSelect("uce.fumante","usuarioCadsusEsus.fumante");
//        HQLHelper subSelectIMC = hql.getNewInstanceSubQuery();
//        subSelectIMC.addToSelect("ap.imc");
//        subSelectIMC.addToFrom("AtendimentoPrimario ap");
//        subSelectIMC.addToWhereWhithAnd("ap.atendimento.usuarioCadsus = usuarioCadsus");
//        subSelectIMC.addToOrder("ap.dataAvaliacao desc");
//
//        Query query = getSession().createQuery(subSelectIMC.getQuery());
//        query.setMaxResults(1);

        hql.addToFrom("UsuarioCadsusDoenca ucd "
                +   "LEFT JOIN ucd.doenca doenca "
                +   "LEFT JOIN doenca.doencaPrincipal doencaPrincipal "
                +   "LEFT JOIN ucd.usuarioCadsus ucs "
                +   "LEFT JOIN ucs.usuarioCadsusEsus uce "
                +   "LEFT JOIN ucs.enderecoDomicilio ed "
                +   "LEFT JOIN ed.equipeMicroArea ema "
                +   "LEFT JOIN ema.equipeArea ea "
                +   "LEFT JOIN ed.enderecoUsuarioCadsus edu "
                +   "LEFT JOIN ucs.empresaResponsavel empresa");

        hql.addToWhereWhithAnd("empresa in ", param.getEstabelecimento());
        hql.addToWhereWhithAnd("ea = ", param.getEquipeArea());
        hql.addToWhereWhithAnd("ema = ", param.getEquipeMicroArea());
        hql.addToWhereWhithAnd("ucs = " , param.getUsuarioCadsus());

        hql.addToWhereWhithAnd("(doenca.condicaoEsus in :condicaoEsusList OR uce.teveInfarto = :sim)");

        hql.addToOrder("ea.descricao");
        hql.addToOrder("ucs.nome");
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setParameterList("condicaoEsusList", Arrays.asList(Doenca.CondicaoEsus.FUMANTE.value(),
                                                                    Doenca.CondicaoEsus.HIPERTENSO.value(),
                                                                    Doenca.CondicaoEsus.DIABETE.value(),
                                                                    Doenca.CondicaoEsus.CORACAO_INSUFICIENCIA.value(),
                                                                    Doenca.CondicaoEsus.CORACAO_NAO_SABE.value(),
                                                                    Doenca.CondicaoEsus.CORACAO_OUTROS.value()
        ));
        query.setParameter("sim", RepositoryComponentDefault.SIM_LONG);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(result)) {
            List<RelatorioRiscoCardiovascularDTO> groupedList = new ArrayList<>();

            Group<RelatorioRiscoCardiovascularDTO> byUsuarioCadsus = Lambda.group(result, Lambda.by(Lambda.on(RelatorioRiscoCardiovascularDTO.class).getUsuarioCadsus()));
            for (Group<RelatorioRiscoCardiovascularDTO> group : byUsuarioCadsus.subgroups()) {
                RelatorioRiscoCardiovascularDTO dto = group.first();

                List<String> descricaoDoencasList = Lambda.extract(group.findAll(), Lambda.on(RelatorioRiscoCardiovascularDTO.class).getUsuarioCadsusDoenca().getDoenca().getDescricao());

                String doencasFormatado = Lambda.join(descricaoDoencasList, ", ");
                dto.setDoencaFormatada(doencasFormatado);

                groupedList.add(dto);
            }

            result.clear();
            result.addAll(groupedList);
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public Collection getResult() {
        return result;
    }
}