/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.vigilancia;

import br.com.celk.report.vigilancia.query.QueryFichaFebreNiloOcidental;
import br.com.celk.report.vigilancia.query.QueryFichaTetanoAcidental;
import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FichaInvestigacaoAgravoDTOParam;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;

import java.util.LinkedHashMap;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoFichaInvestigacaoAgravoFebreNiloOcidental extends AbstractReport<FichaInvestigacaoAgravoDTOParam> {

    private QueryFichaFebreNiloOcidental query;

    public ImpressaoFichaInvestigacaoAgravoFebreNiloOcidental(FichaInvestigacaoAgravoDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        if(query == null){
            query = new QueryFichaFebreNiloOcidental();
        }
        return query;
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> campos = new LinkedHashMap<>(((QueryFichaFebreNiloOcidental)getQuery()).getMapeamentoPlanilhaBase());

        campos.put("dataInvestigacao", "_31_data_investigacao");
        campos.put("ocupacaoCbo", "_32_ocupacao");

        campos.put("viajouUltimos15Dias", "_33_viajou_15_dias");
        campos.put("dataIda", "_34_data_ida");
        campos.put("dataRetorno", "_35_data_retorno");

        campos.put("estadoViajou", "_36_estado");
        campos.put("cidadeViajou", "_38_cidade");
        campos.put("ibge", "_38_ibge");
        campos.put("paisViajou", "_37_pais");

        campos.put("vacinadoFebreAmarela", "_39_vacinado_febre_amarela");
        campos.put("dataVacinacao", "_39_data_vacinacao");

        campos.put("infeccaoDengue", "_40_infeccao_dengue");
        campos.put("dataInfeccaoDengue", "_40_data_infeccao_dengue");
        campos.put("infeccaoFebreAmarela", "_40_infeccao_febre_amarela");
        campos.put("dataInfeccaoFebreAmarela", "_40_data_infeccao_febre_amarela");
        campos.put("infeccaoArbovirose", "_40_infeccao_arbovirose");
        campos.put("infeccaoArboviroseDescricao", "_40_infeccao_arbovirose_desc");
        campos.put("dataInfeccaoArbovirose", "_40_data_infeccao_arbovirose");

        campos.put("transfusaoSanguinea", "_41_transfusao_sanguinea");
        campos.put("dataTransfusaoSanguinea", "_42_data_transfusao");

        campos.put("hospitalTransfusao", "_45_nome_hospital");
        campos.put("cidadeTransfusao", "_44_cidade_transfusao");
        campos.put("estadoTransfusao", "_43_estado_transfusao");

        campos.put("aleitamentoMaterno", "_46_aleitamento_materno");
        campos.put("areasCavalosAvesMortas", "_47_areas_cavalos_aves_mortas");

        campos.put("hospitalizacao", "_48_hospitalizacao");
        campos.put("dataInternacao", "_49_data_internacao");
        campos.put("estado", "_50_estado");
        campos.put("cidade", "_51_cidade");
        campos.put("hospital", "_52_nome_hospital");

        campos.put("sinaisSintomasConvulsoes", "_53_sinais_sintomas_convulsoes");
        campos.put("sinaisSintomasRigidezNuca", "_53_sinais_sintomas_rigidez_nuca");
        campos.put("sinaisSintomasConfusaoMental", "_53_sinais_sintomas_confusao_mental");
        campos.put("sinaisSintomasComa", "_53_sinais_sintomas_coma");
        campos.put("sinaisSintomasDiarreia", "_53_sinais_sintomas_diarreia");
        campos.put("sinaisSintomasVomito", "_53_sinais_sintomas_vomito");
        campos.put("sinaisSintomasNausea", "_53_sinais_sintomas_nausea");
        campos.put("sinaisSintomasDorAbdominal", "_53_sinais_sintomas_dor_abdominal");
        campos.put("sinaisSintomasMialgia", "_53_sinais_sintomas_mialgia");
        campos.put("sinaisSintomasAstralgia", "_53_sinais_sintomas_artralgia");
        campos.put("sinaisSintomasCefaleia", "_53_sinais_sintomas_cefaleia");
        campos.put("sinaisSintomasExantema", "_53_sinais_sintomas_exantema");
        campos.put("sinaisSintomasFebre", "_53_sinais_sintomas_febre");
        campos.put("sinaisSintomasDorOcular", "_53_sinais_sintomas_dor_ocular");
        campos.put("sinaisSintomasLinfadenopatia", "_53_sinais_sintomas_linfadenopatia");
        campos.put("sinaisSintomasFraquezaMuscular", "_53_sinais_sintomas_fraqueza_muscular");
        campos.put("sinaisSintomasFraquezaMuscularMmss", "_53_sinais_sintomas_fraqueza_muscular_mmss");
        campos.put("sinaisSintomasFraquezaMuscularMmii", "_53_sinais_sintomas_fraqueza_muscular_mmii");
        campos.put("sinaisSintomasParalisia", "_53_sinais_sintomas_paralisia");
        campos.put("sinaisSintomasParalisiaLocal", "_53_sinais_sintomas_paralisia_local");
        campos.put("sinaisSintomasProstracao", "_53_sinais_sintomas_prostracao");
        campos.put("sinaisSintomasTremoresExtremidades", "_53_sinais_sintomas_tremores_extremidades");
        campos.put("sinaisSintomasOutros", "_53_sinais_sintomas_outros");

        campos.put("leucogramaLeucocitos", "_54_leucograma_leucocitos");
        campos.put("leucogramaMonocitos", "_54_leucograma_monocitos");
        campos.put("leucogramaNeutrofilos", "_54_leucograma_neutrofilos");
        campos.put("leucogramaEosinofilos", "_54_leucograma_aosinofilos");
        campos.put("leucogramaLinfocitos", "_54_leucograma_linfocitos");

        campos.put("hemogramaHemacias", "_55_hemograma_hemacias");
        campos.put("hemogramaHemoglobina", "_55_hemograma_hemoglobina");
        campos.put("hemogramaHematocrito", "_55_hemograma_hematrocito");
        campos.put("hemogramaPlaquetas", "_55_hemograma_plaquetas");

        campos.put("puncaoLombar", "_56_puncao_lombar");
        campos.put("dataPuncao", "_57_data_puncao");
        campos.put("aspectoLiquor", "_58_aspecto_liquor");

        campos.put("citoquimicaHemacias", "_59_citoquimica_hemacias");
        campos.put("citoquimicaLeucocitos", "_59_citoquimica_leucocitos");
        campos.put("citoquimicaMonocitos", "_59_citoquimica_monocitos");
        campos.put("citoquimicaGlicose", "_59_citoquimica_glicose");
        campos.put("citoquimicaCloreto", "_59_citoquimica_cloreto");
        campos.put("citoquimicaNeutrofilos", "_59_citoquimica_neutrofilos");
        campos.put("citoquimicaEosinofilos", "_59_citoquimica_aosinofilos");
        campos.put("citoquimicaLinfocitos", "_59_citoquimica_linfocitos");
        campos.put("citoquimicaProteinas", "_59_citoquimica_proteinas");

        campos.put("liquorElisaIgm", "_60_liquor_elisa_igm");
        campos.put("liquorElisaIgg", "_60_liquor_elisa_igg");
        campos.put("liquorSoroneutralizacao", "_61_liquor_soro");

        campos.put("dataColetaS1", "_62_data_coleta_s1");
        campos.put("elisaS1Igm", "_63_soro_elisa_s1_igm");
        campos.put("elisaS1Igg", "_63_soro_elisa_s1_igg");
        campos.put("elisaSoroneutralizacaoS1", "_64_soro_s1_soro");

        campos.put("dataColetaS2", "_65_data_coleta_s2");
        campos.put("elisaS2Igm", "_66_soro_elisa_s2_igm");
        campos.put("elisaS2Igg", "_66_soro_elisa_s2_igg");
        campos.put("elisaSoroneutralizacaoS2", "_67_soro_s2_soro");

        campos.put("pcrSangue", "_68_pcr_sangue");
        campos.put("pcrLiquor", "_68_pcr_liquor");
        campos.put("pcrTecido", "_68_pcr_tecido");
        campos.put("pcrTecidoOutros", "_68_pcr_tecido_outros");
        campos.put("dataColetaPcr", "_69_data_pcr");
        campos.put("pcr", "_70_pcr");

        campos.put("ivSangue", "_71_iv_sangue");
        campos.put("ivLiquor", "_71_iv_liquor");
        campos.put("ivTecido", "_71_iv_tecido");
        campos.put("ivTecidoOutros", "_71_iv_tecido_outros");
        campos.put("dataColetaIv", "_72_data_iv");
        campos.put("iv", "_73_iv");

        campos.put("apCerebro", "_74_ap_cerebro");
        campos.put("apVisceras", "_74_ap_visceras");
        campos.put("apViscerasOutros", "_74_ap_visceras_outros");
        campos.put("apHistopatologico", "_75_ap_histopatologico");
        campos.put("apImunohistoquimica", "_75_ap_imunohistoquimica");
        campos.put("dataColetaAp", "_76_data_ap");

        campos.put("classificacaofinal", "_77_classificacao_final");
        campos.put("criterioConfirmacaoDescarte", "_78_confirmacao_descarte");

        campos.put("casoAutoctone", "_79_caso_autoctone");
        campos.put("estado", "_80_estado");
        campos.put("paisLocalInfeccao", "_81_pais");
        campos.put("cidadeLocalInfeccao", "_82_cidade");
        campos.put("ibge", "_82_ibge");
        campos.put("distritoLocalInfeccao", "_83_distrito");
        campos.put("bairroLocalInfeccao", "_84_bairro");

        campos.put("doencaRelacionadaTrabalho", "_85_doenca_relacionada_trabalho");

        campos.put("evolucaoCaso", "_86_evolucao_caso");
        campos.put("investigacaoAgravo.dataObito", "_87_data_obito");

        campos.put("observacao", "_observacao");

        return campos;
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/vigilancia/pdf/ficha_investigacao_agravo_febre_nilo_ocidental.pdf";
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return param.getTipoArquivo();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_febre_nilo_ocidental");
    }

}
