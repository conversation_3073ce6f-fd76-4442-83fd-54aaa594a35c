<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_produto_solicitado" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="9907a5e6-34ef-4b89-accb-6af0a7b3f74d">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.zoom" value="1.9487171000000139"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.vo.entradas.dispensacao.*"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.report.materiais.judicial.interfaces.dto.RelatorioRelacaoMedicamentoJudicialDTOParam.FormaApresentacao"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="formaApresentacao" class="br.com.ksisolucoes.report.materiais.judicial.interfaces.dto.RelatorioRelacaoMedicamentoJudicialDTOParam.FormaApresentacao"/>
	<parameter name="visualizarEntregas" class="java.lang.String"/>
	<parameter name="visualizarFA" class="java.lang.Long"/>
	<parameter name="exibeEndPaciente" class="java.lang.Long">
		<parameterDescription><![CDATA[exibeEndPaciente]]></parameterDescription>
	</parameter>
	<parameter name="exibeTelefonePaciente" class="java.lang.Long">
		<parameterDescription><![CDATA[exibeTelefonePaciente]]></parameterDescription>
	</parameter>
	<field name="produtoSolicitado" class="br.com.ksisolucoes.vo.basico.ProdutoSolicitado"/>
	<field name="estoque" class="java.lang.Double"/>
	<field name="movimentosList" class="java.util.Collection"/>
	<field name="produto" class="br.com.ksisolucoes.vo.entradas.estoque.Produto"/>
	<field name="quantidadeMensal" class="java.lang.Double"/>
	<field name="produtoSolicitadoItem" class="br.com.ksisolucoes.vo.basico.ProdutoSolicitadoItem">
		<fieldDescription><![CDATA[produtoSolicitadoItem]]></fieldDescription>
	</field>
	<group name="legenda">
		<groupFooter>
			<band height="15">
				<rectangle>
					<reportElement x="0" y="2" width="149" height="12" uuid="2ec01bd6-9112-451d-9bdf-5c5f4ae4b74d"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isBlankWhenNull="true">
					<reportElement x="2" y="2" width="146" height="12" uuid="5f71c898-8b21-4ba1-994f-4b7a8d911906"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_situacao_inicial")+
" = "+Bundle.getStringApplication("rotulo_situacao")+" | "+
Bundle.getStringApplication("rotulo_ativo").substring(0,1)+" = "+
Bundle.getStringApplication("rotulo_ativo")+"; "+
Bundle.getStringApplication("rotulo_excluido").substring(0,1)+" = "+
Bundle.getStringApplication("rotulo_excluido")]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="Group" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($P{visualizarFA})
?
    FormaApresentacao.PACIENTE.equals($P{formaApresentacao})
    ?
        $F{produtoSolicitado}.getUsuarioCadsus().getCodigo()
    :
        FormaApresentacao.UNIDADE.equals($P{formaApresentacao})
        ?
            $F{produtoSolicitado}.getEmpresa().getCodigo()
        :
            FormaApresentacao.PROFISSIONAL.equals($P{formaApresentacao})
            ?
                $F{produtoSolicitado}.getProfissional().getCodigo()
            :
                FormaApresentacao.PRODUTO.equals($P{formaApresentacao})
                ?
                    $F{produto}.getCodigo()
                :
                    ""
:
    null]]></groupExpression>
		<groupHeader>
			<band height="27">
				<rectangle radius="10">
					<reportElement x="0" y="0" width="802" height="14" isRemoveLineWhenBlank="true" uuid="4c612082-b796-45bf-adfd-7b10e61b1344">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($P{visualizarFA})]]></printWhenExpression>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<line>
					<reportElement x="0" y="26" width="802" height="1" uuid="ac52159e-9ec7-4454-90c3-a3f29ada3d1a"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField>
					<reportElement x="123" y="14" width="50" height="12" uuid="7278ddc8-f5c4-4785-8c79-35b46b657a02"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_cadastro_abv")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="174" y="14" width="172" height="12" uuid="64e3f776-693b-49dc-8ad9-19da05d05678"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_produto")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="349" y="14" width="22" height="12" uuid="2b92aaef-7b28-4c42-ba61-acec5bc5833b"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_unidade_abv")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="373" y="14" width="52" height="12" uuid="13b9c055-70c4-4be9-8f42-2b54440e5ada"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quantidade_abv")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="0" width="802" height="14" isRemoveLineWhenBlank="true" uuid="b9edff20-9933-47a6-8726-d8e1a045c276">
						<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($P{visualizarFA})]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[FormaApresentacao.PACIENTE.equals($P{formaApresentacao})
?
    Bundle.getStringApplication("rotulo_paciente")+": "+$F{produtoSolicitado}.getUsuarioCadsus().getDescricaoFormatado()
:
    FormaApresentacao.UNIDADE.equals($P{formaApresentacao})
    ?
        Bundle.getStringApplication("rotulo_empresa")+": "+$F{produtoSolicitado}.getEmpresa().getDescricaoFormatado()
    :
        FormaApresentacao.PROFISSIONAL.equals($P{formaApresentacao})
        ?
            Bundle.getStringApplication("rotulo_profissional")+": "+$F{produtoSolicitado}.getProfissional().getDescricaoFormatado()
        :
            FormaApresentacao.PRODUTO.equals($P{formaApresentacao})
            ?
                Bundle.getStringApplication("rotulo_produto")+": "+$F{produto}.getDescricaoFormatado()

            :
                ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="2" y="14" width="120" height="12" uuid="36702bd1-909e-4653-9312-0b02bba86746"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{formaApresentacao}.equals("UsuarioCadsus")
?
    Bundle.getStringApplication("rotulo_assistente_social")
:
    Bundle.getStringApplication("rotulo_paciente")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="479" y="14" width="105" height="12" uuid="37060a75-**************-a42335b1f968"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_unidade")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="584" y="14" width="120" height="12" uuid="7cb8e8d1-4922-4463-9f72-ef1b64f8ab48"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_profissional")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="706" y="14" width="42" height="12" uuid="18cef04e-a479-4409-9b9c-563cc4923229"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_solicitacao_abv")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="791" y="15" width="10" height="12" uuid="ec7f8ba5-909c-475b-b7b4-542b805e180f"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_situacao_inicial")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="749" y="14" width="42" height="12" uuid="fd2319b8-3271-448e-b7b9-9c041cea8aa8"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_validade_abv")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="425" y="14" width="50" height="12" uuid="a06bb2b5-6d7e-46f8-aa26-eab478b64ddf"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_estoque")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="21" splitType="Stretch">
			<subreport>
				<reportElement positionType="Float" x="2" y="20" width="799" height="1" isPrintWhenDetailOverflows="true" uuid="e5c2e23d-80de-4fc3-8812-e3ea8a90f17f"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{movimentosList})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/basico/jrxml/sub_relatorio_produto_solicitado_entregas.jasper"]]></subreportExpression>
			</subreport>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="2" y="0" width="120" height="12" uuid="c44c3b96-d102-4ad4-8133-da9ccd4fac64"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{produtoSolicitado}.getUsuarioCadsus().getDescricaoFormatado()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="123" y="0" width="50" height="12" uuid="57810ab0-82d7-4018-910a-6070522bafae"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{produtoSolicitado}.getDataCadastro()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="479" y="0" width="105" height="12" uuid="fc946675-9f1f-41cc-980c-8786d8c3a9ab"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{produtoSolicitado}.getEmpresa().getDescricaoFormatado()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="706" y="0" width="42" height="12" uuid="648643c7-c19a-4c5c-9da2-d85d3b6c2bed"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{produtoSolicitado}.getDataReceita()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="791" y="0" width="10" height="12" uuid="8c44ce4a-717c-4b08-bea2-a667f437d8fe"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{produtoSolicitado}.getDescricaoStatus().substring(0,1)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="174" y="0" width="172" height="12" uuid="c44c3b96-d102-4ad4-8133-da9ccd4fac64"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{produto}.getDescricaoFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="349" y="0" width="22" height="12" uuid="c44c3b96-d102-4ad4-8133-da9ccd4fac64"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{produto}.getUnidade().getUnidade()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement x="373" y="0" width="52" height="12" uuid="c44c3b96-d102-4ad4-8133-da9ccd4fac64"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidadeMensal}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="2" y="12" width="120" height="8" uuid="b461bd57-08c2-46ca-8109-371aa12ad876">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($P{exibeEndPaciente})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA["Endereço: " + $F{produtoSolicitado}.getUsuarioCadsus().getEnderecoUsuarioCadsus().getEnderecoFormatadoComCidade()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="124" y="12" width="120" height="8" uuid="837ea3bc-e987-4002-adec-e5b52c954864">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($P{exibeTelefonePaciente})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{produtoSolicitado}.getUsuarioCadsus().getTelefone() != null || $F{produtoSolicitado}.getUsuarioCadsus().getCelular() != null
    ? "Telefone: " + $F{produtoSolicitado}.getUsuarioCadsus().getCelularOuTelefoneFormatado() : null]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="584" y="0" width="120" height="12" uuid="6b9537c3-0180-41c0-8418-6b777a274563"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{produtoSolicitado}.getProfissional().getDescricaoFormatado()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="749" y="0" width="42" height="12" uuid="2336cc71-6fa1-47c4-af62-07480de17e9a"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{produtoSolicitadoItem}.getDataValidade()]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="425" y="0" width="50" height="12" uuid="1368ad9a-cb79-42b8-bcc5-e3d937395437"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{estoque}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
