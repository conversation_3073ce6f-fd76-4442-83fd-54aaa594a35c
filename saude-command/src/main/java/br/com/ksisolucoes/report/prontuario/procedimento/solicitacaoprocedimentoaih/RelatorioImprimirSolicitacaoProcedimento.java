package br.com.ksisolucoes.report.prontuario.procedimento.solicitacaoprocedimentoaih;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.procedimento.solicitacaoprocedimentoaih.query.QueryRelatorioImprimirSolicitacaoProcedimento;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaMantenedora;
import br.com.ksisolucoes.vo.prontuario.procedimento.SolicitacaoProcedimentos;
import br.com.ksisolucoes.vo.prontuario.procedimento.SolicitacaoProcedimentosItem;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImprimirSolicitacaoProcedimento extends AbstractReport<SolicitacaoProcedimentos> {

    public RelatorioImprimirSolicitacaoProcedimento(SolicitacaoProcedimentos param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/prontuario/procedimento/solicitacaoprocedimentoaih/jrxml/relatorio_imprimir_solicitacao_procedimento.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_solicitacao_mudanca_procedimento_especial");
    }

    @Override
    public ITransferDataReport getQuery() {

        Empresa emp = getSessao().getEmpresa();
        Empresa empresa = LoadManager.getInstance(Empresa.class)
                .addProperties(Empresa.PROP_CODIGO)
                .addProperties(Empresa.PROP_DESCRICAO)
                .addProperties(VOUtils.montarPath(Empresa.PROP_EMPRESA_MANTENEDORA, EmpresaMantenedora.PROP_CODIGO))
                .addProperties(VOUtils.montarPath(Empresa.PROP_EMPRESA_MANTENEDORA, EmpresaMantenedora.PROP_NOME_MANTENEDORA))
                .addProperties(VOUtils.montarPath(Empresa.PROP_EMPRESA_MANTENEDORA, EmpresaMantenedora.PROP_CNES))
                .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_CODIGO, emp.getCodigo()))
                .start().getVO();
        addParametro("EMPRESA", empresa);

        return new QueryRelatorioImprimirSolicitacaoProcedimento();
    }
}
