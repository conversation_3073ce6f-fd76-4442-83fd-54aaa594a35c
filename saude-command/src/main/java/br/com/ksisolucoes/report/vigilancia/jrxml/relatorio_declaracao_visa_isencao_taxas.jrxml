<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_declaracao_visa_isencao_taxas" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="a1b57cfd-9055-4cac-88e7-9178d405e0e1">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.7715610000000206"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="br.com.ksisolucoes.util.Util"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="br.com.celk.util.Coalesce"/>
	<import value="br.com.celk.util.DataUtil"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="textoAutorizacaoExumacao1" class="java.lang.String"/>
	<parameter name="textoAutorizacaoExumacao2" class="java.lang.String"/>
	<parameter name="cidade" class="java.lang.String"/>
	<field name="requerimentoDeclaracaoVisaIsencaoTaxas" class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoDeclaracaoVisaIsencaoTaxas"/>
	<field name="profissionalImpressao" class="br.com.ksisolucoes.vo.cadsus.Profissional">
		<fieldDescription><![CDATA[profissionalImpressao]]></fieldDescription>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<detail>
		<band height="99">
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="34" y="0" width="487" height="25" uuid="e4ce5ec0-b924-45bd-9dae-e18bd5c31be9"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="16" isBold="true"/>
					<paragraph lineSpacing="Double"/>
				</textElement>
				<textFieldExpression><![CDATA["DECLARAÇÃO Nº " + $F{requerimentoDeclaracaoVisaIsencaoTaxas}.getRequerimentoVigilancia().getProtocoloFormatado()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" isPrintRepeatedValues="false" x="34" y="34" width="487" height="65" uuid="d519313e-974e-49ca-9b71-a422fb229f68"/>
				<textElement textAlignment="Justified">
					<font fontName="Arial" size="12"/>
					<paragraph lineSpacing="Double"/>
				</textElement>
				<textFieldExpression><![CDATA["Declaramos para os devidos fins e efeitos legais, que a Vigilância Sanitária concede alvará para prestação de serviços sujeitos a esta fiscalização desde que haja planta física a ser inspecionada, em consonância com o disposto na legislação sanitária."]]></textFieldExpression>
			</textField>
		</band>
		<band height="42">
			<textField isStretchWithOverflow="true">
				<reportElement isPrintRepeatedValues="false" x="34" y="1" width="487" height="41" isPrintWhenDetailOverflows="true" uuid="47be724e-e61a-4434-8e79-796426ac32fb"/>
				<textElement textAlignment="Justified" markup="none">
					<font fontName="Arial" size="12"/>
					<paragraph lineSpacing="Double"/>
				</textElement>
				<textFieldExpression><![CDATA["No que diz respeito à empresa "
+ ($F{requerimentoDeclaracaoVisaIsencaoTaxas}.getEstabelecimento().getRazaoSocial() != null
    ? $F{requerimentoDeclaracaoVisaIsencaoTaxas}.getEstabelecimento().getRazaoSocial()
        : $F{requerimentoDeclaracaoVisaIsencaoTaxas}.getEstabelecimento().getFantasia())  + ", " + Bundle.getStringApplication("rotulo_cpf_cnpj") + " "
+ $F{requerimentoDeclaracaoVisaIsencaoTaxas}.getEstabelecimento().getCnpjCpfFormatado() + ", localizada na "
+ $F{requerimentoDeclaracaoVisaIsencaoTaxas}.getEstabelecimento().getEnderecoFormatado()]]></textFieldExpression>
			</textField>
		</band>
		<band height="97">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" isPrintRepeatedValues="false" x="34" y="0" width="487" height="19" uuid="926c1417-647b-4998-981f-3c8740917b49"/>
				<textElement textAlignment="Justified">
					<font fontName="Arial" size="12"/>
					<paragraph lineSpacing="Double"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{requerimentoDeclaracaoVisaIsencaoTaxas}.getMotivo()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" isPrintRepeatedValues="false" x="34" y="30" width="487" height="39" uuid="9fb1a25d-97ef-4193-b681-f0eeeb81cf15"/>
				<textElement textAlignment="Justified">
					<font fontName="Arial" size="12"/>
					<paragraph lineSpacing="Double"/>
				</textElement>
				<textFieldExpression><![CDATA["Assim sendo, esta empresa, funcionando nestas condições, não necessita de alvará sanitário."]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" isPrintRepeatedValues="false" x="34" y="74" width="487" height="21" uuid="a24c3605-0443-4fd1-9ca7-a0da8e1aeb9c"/>
				<textElement textAlignment="Justified">
					<font fontName="Arial" size="12"/>
					<paragraph lineSpacing="Double"/>
				</textElement>
				<textFieldExpression><![CDATA["Era o que tínhamos a declarar!"]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="125" splitType="Stretch">
			<elementGroup>
				<line>
					<reportElement positionType="Float" x="140" y="56" width="275" height="1" isPrintWhenDetailOverflows="true" uuid="29201466-e46f-4982-8c82-6e01ef0ca79e">
						<printWhenExpression><![CDATA[VigilanciaHelper.exibirLinhaAssinatura()]]></printWhenExpression>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="140" y="71" width="275" height="13" uuid="caf466c6-3225-4c7d-8b59-a151312b6744"/>
					<box topPadding="1" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font fontName="Arial" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{profissionalImpressao}.getReferenciaRegistroFormatado()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement mode="Transparent" x="140" y="56" width="275" height="15" isPrintWhenDetailOverflows="true" uuid="2f47f64f-0dce-44ee-9d22-511ce99a3eb1"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{profissionalImpressao} != null ? $F{profissionalImpressao}.getNome() : "Assinatura da Vigilância Sanitária"]]></textFieldExpression>
				</textField>
			</elementGroup>
			<elementGroup>
				<textField isStretchWithOverflow="true" pattern="EEEEE dd &apos;de&apos; MMMMM &apos;de&apos; yyyy">
					<reportElement positionType="FixRelativeToBottom" isPrintRepeatedValues="false" x="234" y="108" width="267" height="15" uuid="28da5331-1ea9-45bc-9c54-d04bed09f646"/>
					<textElement>
						<font fontName="Arial" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[DataUtil.getDataAtual()]]></textFieldExpression>
				</textField>
				<textField pattern="EEEEE dd MMMMM yyyy">
					<reportElement positionType="FixRelativeToBottom" isPrintRepeatedValues="false" x="65" y="108" width="166" height="15" uuid="4a9adbe7-0eb9-4f43-85ed-ec00537a0fc8"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{cidade} + ", "]]></textFieldExpression>
				</textField>
			</elementGroup>
		</band>
	</columnFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
