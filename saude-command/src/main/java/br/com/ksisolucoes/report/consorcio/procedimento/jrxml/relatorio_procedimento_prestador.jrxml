<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_procedimento_prestador" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="e25ea69a-dae4-447a-b32f-1790a19ce1e5">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.8181818181818186"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.report.consorcio.dto.RelatorioProcedimentoPrestadorDTOParam.FormaApresentacao"/>
	<import value="br.com.ksisolucoes.vo.consorcio.LicitacaoItem.StatusLicitacaoItem"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.util.Dinheiro"/>
	<import value="br.com.ksisolucoes.report.ReportProperties"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="formaApresentacao" class="java.lang.Long"/>
	<field name="consorcioPrestadorServico" class="br.com.ksisolucoes.vo.consorcio.ConsorcioPrestadorServico"/>
	<field name="valor" class="java.lang.Double"/>
	<field name="consorcioGrupoDescricao" class="java.lang.String"/>
	<field name="edital" class="java.lang.String"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="FA" class="br.com.ksisolucoes.report.consorcio.dto.RelatorioProcedimentoPrestadorDTOParam.FormaApresentacao"/>
	<group name="FormaApresentacao" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[FormaApresentacao.PROCEDIMENTO.value().equals($P{formaApresentacao})
?
    $F{consorcioPrestadorServico}.getConsorcioProcedimento().getDescricaoProcedimentoFormatado()
:
    FormaApresentacao.PRESTADOR.value().equals($P{formaApresentacao})
    ?
        $F{consorcioPrestadorServico}.getConsorcioPrestador().getEmpresaPrestador().getDescricao()
    :
        FormaApresentacao.GRUPO_PROCEDIMENTO.value().equals($P{formaApresentacao})
        ?
           $F{consorcioGrupoDescricao}
        :
            null]]></groupExpression>
		<groupHeader>
			<band height="14">
				<rectangle radius="10">
					<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="555" height="14" isPrintWhenDetailOverflows="true" uuid="0a6ebe40-db4d-4c04-af8a-667eb846df72"/>
					<graphicElement>
						<pen lineWidth="1.0"/>
					</graphicElement>
				</rectangle>
				<textField isStretchWithOverflow="true">
					<reportElement stretchType="RelativeToBandHeight" x="0" y="1" width="555" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="e18bf2bb-b64f-4511-be8b-d3da0a048fd4"/>
					<textElement textAlignment="Center"/>
					<textFieldExpression><![CDATA[FormaApresentacao.PROCEDIMENTO.value().equals($P{formaApresentacao})
?
    Bundle.getStringApplication("rotulo_procedimento")+": "+$F{consorcioPrestadorServico}.getConsorcioProcedimento().getDescricaoProcedimentoFormatado()
:
    FormaApresentacao.PRESTADOR.value().equals($P{formaApresentacao})
    ?
        $F{consorcioPrestadorServico}.getConsorcioPrestador().getEmpresaPrestador().getDescricao() != null ? Bundle.getStringApplication("rotulo_prestador")+": "+$F{consorcioPrestadorServico}.getConsorcioPrestador().getEmpresaPrestador().getDescricao() : "Sem Prestador Definido"
    :
        FormaApresentacao.GRUPO_PROCEDIMENTO.value().equals($P{formaApresentacao})
        ?
            Bundle.getStringApplication("rotulo_grupo_procedimento")+": "+$F{consorcioGrupoDescricao}
        :
            $P{formaApresentacao}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<group name="TipoResumo" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[FormaApresentacao.GRUPO_PROCEDIMENTO.value().equals($P{formaApresentacao})
   ?
      Bundle.getStringApplication("rotulo_procedimento")+": "+$F{consorcioPrestadorServico}.getConsorcioProcedimento().getDescricaoProcedimentoFormatado()
   :
      ""]]></groupExpression>
		<groupHeader>
			<band height="12">
				<printWhenExpression><![CDATA[FormaApresentacao.GRUPO_PROCEDIMENTO.value().equals($P{formaApresentacao})]]></printWhenExpression>
				<textField>
					<reportElement x="0" y="0" width="555" height="12" uuid="3ee06fe3-378c-4404-a299-deae215978c0"/>
					<box leftPadding="2"/>
					<textElement>
						<font isItalic="true" isUnderline="true"/>
					</textElement>
					<textFieldExpression><![CDATA[FormaApresentacao.GRUPO_PROCEDIMENTO.value().equals($P{formaApresentacao})
   ?
      Bundle.getStringApplication("rotulo_procedimento")+": "+$F{consorcioPrestadorServico}.getConsorcioProcedimento().getDescricaoProcedimentoFormatado()
   :
      ""]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<group name="Header" isReprintHeaderOnEachPage="true">
		<groupHeader>
			<band height="17">
				<printWhenExpression><![CDATA[FormaApresentacao.PRESTADOR.value().equals($P{formaApresentacao})]]></printWhenExpression>
				<line>
					<reportElement x="0" y="15" width="555" height="1" uuid="3ad6a786-4146-4164-accf-6a85752d7438"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="0" y="3" width="419" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="b6ce4d8a-a079-482f-8fe1-8a5217237a1a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_procedimento")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="419" y="3" width="62" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="b6ce4d8a-a079-482f-8fe1-8a5217237a1a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_edital")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="481" y="3" width="74" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="b6ce4d8a-a079-482f-8fe1-8a5217237a1a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_valor")]]></textFieldExpression>
				</textField>
			</band>
			<band height="17">
				<printWhenExpression><![CDATA[!FormaApresentacao.PRESTADOR.value().equals($P{formaApresentacao})]]></printWhenExpression>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="0" y="3" width="197" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="b259fe47-092e-4469-ab41-f9caaf3b3dfa"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_prestador")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="197" y="3" width="161" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="309d1f8c-08b6-447d-ae73-6395f311597a"/>
					<box leftPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cidade")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="358" y="3" width="123" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="b66523f8-9574-4932-ada4-3fddaa730df8"/>
					<box leftPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_contato")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="15" width="555" height="1" uuid="e268c52c-c0fa-4cff-8b2c-63bbc19a351f"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="481" y="3" width="74" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="1575554b-196a-4d8f-9cb7-0b88a7fa6006"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_valor")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="14" splitType="Stretch">
			<printWhenExpression><![CDATA[$P{formaApresentacao}.equals(FormaApresentacao.PRESTADOR.value())]]></printWhenExpression>
			<textField isStretchWithOverflow="true" pattern="###0.0000;-###0.0000" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="419" y="0" width="62" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="63a34d7a-7a89-4426-9059-809e39affd9e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{edital}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="481" y="0" width="74" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="63a34d7a-7a89-4426-9059-809e39affd9e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new Dinheiro($F{valor})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="0" y="0" width="419" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="0cd6b30e-d148-4487-be5f-d9b643af888f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{consorcioPrestadorServico}.getConsorcioProcedimento().getDescricaoProcedimentoFormatado()]]></textFieldExpression>
			</textField>
		</band>
		<band height="14">
			<printWhenExpression><![CDATA[!$P{formaApresentacao}.equals(FormaApresentacao.PRESTADOR.value())]]></printWhenExpression>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement key="textField-45" mode="Transparent" x="0" y="0" width="197" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="f05b0db0-b284-462c-b444-bde854205281"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{consorcioPrestadorServico}.getConsorcioPrestador().getEmpresaPrestador().getDescricao() != null
    ?   $F{consorcioPrestadorServico}.getConsorcioPrestador().getEmpresaPrestador().getDescricao()
        : "Sem Prestador Definido"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="197" y="0" width="161" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="906f041f-572f-4a34-92ee-58be1c83543a"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{consorcioPrestadorServico}.getConsorcioPrestador().getEmpresaPrestador().getCidade().getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="481" y="0" width="74" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="b0106381-f913-49e8-9df5-62e51f0caba5"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new Dinheiro($F{valor})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.0000;-###0.0000" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="358" y="0" width="123" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="65b67791-a4db-483b-bd41-4ee4cd36e22b"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{consorcioPrestadorServico}.getConsorcioPrestador().getEmpresaPrestador().getCelular() != null
    ?
        $F{consorcioPrestadorServico}.getConsorcioPrestador().getEmpresaPrestador().getTelefoneFormatado() + " / " + $F{consorcioPrestadorServico}.getConsorcioPrestador().getEmpresaPrestador().getCelularFormatado()
    :
        $F{consorcioPrestadorServico}.getConsorcioPrestador().getEmpresaPrestador().getTelefoneFormatado()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
