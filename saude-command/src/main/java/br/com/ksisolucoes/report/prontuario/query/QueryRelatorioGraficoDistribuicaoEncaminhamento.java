package br.com.ksisolucoes.report.prontuario.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioGraficoDistribuicaoEncaminhamentoDTO;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioGraficoDistribuicaoEncaminhamentoDTOParam;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.FaixaEtaria;
import br.com.ksisolucoes.vo.basico.FaixaEtariaItem;
import br.com.ksisolucoes.vo.basico.FaixaEtariaItemPK;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoEncaminhamento;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.hibernate.Criteria;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioGraficoDistribuicaoEncaminhamento extends CommandQuery<QueryRelatorioGraficoDistribuicaoEncaminhamento> implements ITransferDataReport<RelatorioGraficoDistribuicaoEncaminhamentoDTOParam, RelatorioGraficoDistribuicaoEncaminhamentoDTO> {

    private RelatorioGraficoDistribuicaoEncaminhamentoDTOParam param;
    private List<RelatorioGraficoDistribuicaoEncaminhamentoDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("count(encaminhamento.codigo)", "quantidade");

        if (Empresa.REF.equals(param.getFormaApresentacao())) {
            hql.addToSelectAndGroup("unidadeEncaminhamento.descricao", "descricaoFormaApresentacao");
        } else if (FaixaEtaria.REF.equals(param.getFormaApresentacao())) {
            String descricao = "";
            try {
                Criteria cFaixaEtariaItem = getSession().createCriteria(FaixaEtariaItem.class);
                cFaixaEtariaItem.add(Restrictions.eq(VOUtils.montarPath(FaixaEtariaItem.PROP_ID, FaixaEtariaItemPK.PROP_FAIXA_ETARIA), param.getFaixaEtaria()));
                List<FaixaEtariaItem> faixas = cFaixaEtariaItem.list();
                int count = 0;
                if (CollectionUtils.isNotNullEmpty(faixas)) {
                    for (FaixaEtariaItem faixaEtariaItem : faixas) {
                        descricao += " (case when ((extract(years from age(encaminhamento.dataCadastro,  usuarioCadsus.dataNascimento)) * 12 + extract(months from age(encaminhamento.dataCadastro,  usuarioCadsus.dataNascimento))) >= " + faixaEtariaItem.getIdadeInicial() + " " + " and (extract(years from age(encaminhamento.dataCadastro,  usuarioCadsus.dataNascimento)) * 12 + extract(months from age(encaminhamento.dataCadastro,  usuarioCadsus.dataNascimento))) <= " + faixaEtariaItem.getIdadeFinal() + " ) " + " then '" + faixaEtariaItem.getDescricao() + "' " + " else ";
                        count++;
                        if (count == faixas.size()) {
                            descricao += " 'outra' ";
                        }
                    }
                    for (FaixaEtariaItem faixaEtariaItem : faixas) {
                        descricao += " end) ";
                    }
                }
            } catch (DAOException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
            hql.addToSelectAndGroup(descricao, "descricaoFormaApresentacao");
        } else if (TipoEncaminhamento.REF.equals(param.getFormaApresentacao())) {
            hql.addToSelectAndGroup("tipoEncaminhamento.descricao", "descricaoFormaApresentacao");
        } else if (Profissional.REF.equals(param.getFormaApresentacao())) {
            hql.addToSelectAndGroup("profissional.nome", "descricaoFormaApresentacao");
        } else if (UsuarioCadsus.PROP_SEXO.equals(param.getFormaApresentacao())) {
            hql.addToSelectAndGroup("(case when usuarioCadsus.sexo = 'M' "
                    + "               then 'Masculino' "
                    + "               else (case when usuarioCadsus.sexo = 'F' then 'Feminino' else 'Outro' end) "
                    + "               end)", "descricaoFormaApresentacao");
        }

        HQLHelper hqlSub = hql.getNewInstanceSubQuery();

            hqlSub.addToSelect("count(encaminhamento2.codigo)");

            hqlSub.addToFrom("Encaminhamento encaminhamento2"
                    + " left join encaminhamento2.unidadeEncaminhamento unidadeEncaminhamento2"
                    + " left join encaminhamento2.tipoEncaminhamento tipoEncaminhamento2"
                    + " left join encaminhamento2.profissional profissional2"
                    + " left join encaminhamento2.encaminhamentoAgendamento encaminhamentoAgendamento2");

            hqlSub.addToWhereWhithAnd("encaminhamento2.status <>", Encaminhamento.STATUS_CANCELADO);
            hqlSub.addToWhereWhithAnd("unidadeEncaminhamento2 in", param.getEmpresa());
            hqlSub.addToWhereWhithAnd("profissional2 in", param.getProfissional());
            hqlSub.addToWhereWhithAnd("tipoEncaminhamento2 in", param.getTipoEncaminhamento());
            if (Encaminhamento.STATUS_AGENDADO.equals(param.getSituacao())) {
                hqlSub.addToWhereWhithAnd("encaminhamentoAgendamento2.dataAgendamento", Data.adjustRangeHour(Data.adjustRangeDay(param.getPeriodo())));
            } else {
                hqlSub.addToWhereWhithAnd("encaminhamento2.dataCadastro", Data.adjustRangeHour(Data.adjustRangeDay(param.getPeriodo())));
            }

            if (Encaminhamento.STATUS_PENDENTE.equals(param.getSituacao())) {
                hqlSub.addToWhereWhithAnd("encaminhamento2.status in", Arrays.asList(Encaminhamento.STATUS_PENDENTE, Encaminhamento.STATUS_AUTORIZADO));
            } else if (Encaminhamento.STATUS_AGENDADO.equals(param.getSituacao())) {
                hqlSub.addToWhereWhithAnd("encaminhamento2.status in", Arrays.asList(Encaminhamento.STATUS_AGENDADO, Encaminhamento.STATUS_CONCLUIDO));
            }

        hql.addToSelect("("+hqlSub.getQuery()+")", "total");

        hql.setTypeSelect(RelatorioGraficoDistribuicaoEncaminhamentoDTO.class.getName());
        hql.addToFrom("Encaminhamento encaminhamento"
                + " left join encaminhamento.unidadeEncaminhamento unidadeEncaminhamento"
                + " left join encaminhamento.usuarioCadsus usuarioCadsus"
                + " left join encaminhamento.tipoEncaminhamento tipoEncaminhamento"
                + " left join encaminhamento.encaminhamentoAgendamento encaminhamentoAgendamento"
                + " left join encaminhamento.profissional profissional");

        hql.addToWhereWhithAnd("encaminhamento.status <>", Encaminhamento.STATUS_CANCELADO);
        hql.addToWhereWhithAnd("unidadeEncaminhamento in", param.getEmpresa());
        hql.addToWhereWhithAnd("profissional in", param.getProfissional());
        hql.addToWhereWhithAnd("tipoEncaminhamento in", param.getTipoEncaminhamento());

        if (Encaminhamento.STATUS_AGENDADO.equals(param.getSituacao())) {
            hql.addToWhereWhithAnd("encaminhamentoAgendamento.dataAgendamento", Data.adjustRangeHour(Data.adjustRangeDay(param.getPeriodo())));
        } else {
            hql.addToWhereWhithAnd("encaminhamento.dataCadastro", Data.adjustRangeHour(Data.adjustRangeDay(param.getPeriodo())));
        }

        if (Encaminhamento.STATUS_PENDENTE.equals(param.getSituacao())) {
            hql.addToWhereWhithAnd("encaminhamento.status in", Arrays.asList(Encaminhamento.STATUS_PENDENTE, Encaminhamento.STATUS_AUTORIZADO));
        } else if (Encaminhamento.STATUS_AGENDADO.equals(param.getSituacao())) {
            hql.addToWhereWhithAnd("encaminhamento.status in", Arrays.asList(Encaminhamento.STATUS_AGENDADO, Encaminhamento.STATUS_CONCLUIDO));
        }

        hql.addToOrder("2");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioGraficoDistribuicaoEncaminhamentoDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(RelatorioGraficoDistribuicaoEncaminhamentoDTOParam param) {
        this.param = param;
    }

}
