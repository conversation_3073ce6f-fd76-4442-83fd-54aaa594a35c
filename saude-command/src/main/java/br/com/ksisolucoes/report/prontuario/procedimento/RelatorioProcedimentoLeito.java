/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.prontuario.procedimento;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.prontuario.procedimento.query.QueryRelatorioProcedimentoLeito;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioProcedimentoLeito extends AbstractReport{

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_leito.jrxml";
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioProcedimentoLeito();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_leito");
    }
}
