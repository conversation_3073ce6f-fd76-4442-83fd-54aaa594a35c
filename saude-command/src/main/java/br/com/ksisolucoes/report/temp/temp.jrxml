<?xml version="1.0" encoding="ISO-8859-1"?>
<!DOCTYPE jasperReport PUBLIC "//JasperReports//DTD Report Design//EN" "http://jasperreports.sourceforge.net/dtds/jasperreport.dtd">
<jasperReport bottomMargin="20" columnCount="1" columnSpacing="0"
    columnWidth="535" isSummaryNewPage="false" isTitleNewPage="false"
    leftMargin="30" name="registro_item_nota_fiscal"
    orientation="Landscape" pageHeight="595" pageWidth="842"
    printOrder="Vertical" rightMargin="30" topMargin="20" whenNoDataType="NoPages">
    <property name="ireport.scriptlethandling" value="2"/>
    <parameter class="java.lang.String" isForPrompting="false" name="TITULO_REPORT"/>
    <parameter class="java.lang.String" isForPrompting="false" name="USUARIO_REPORT"/>
    <parameter class="java.lang.String" isForPrompting="false" name="CLIENTE_REPORT"/>
    <parameter class="java.lang.String" isForPrompting="false" name="ParamPeriodo"/>
    <parameter class="java.lang.String" isForPrompting="false" name="ParamFormaApresentacao"/>
    <parameter class="java.lang.String" isForPrompting="false" name="ParamOrdenacao"/>
    <parameter class="java.lang.String" isForPrompting="false" name="ParamStatus"/>
    <parameter class="java.lang.String" isForPrompting="false" name="ParamAgrupamento"/>
    <field class="java.lang.Object" name="Id"/>
    <field class="java.lang.Object" name="Produto"/>
    <field class="java.lang.Double" name="Quantidade"/>
    <field class="java.lang.Object" name="CentroCusto"/>
    <field class="java.lang.Long" name="Status"/>
    <field class="java.lang.Double" name="QuantidadeEstoque"/>
    <field class="java.lang.Double" name="PrecoUnitario"/>
    <field class="java.lang.Double" name="PercentualIpi"/>
    <variable calculation="Nothing"
        class="br.com.ksisolucoes.util.Bundle" name="BUNDLE" resetType="Report"/>
    <variable calculation="Nothing" class="br.com.ksisolucoes.util.Data"
        name="DATA" resetType="Report"/>
    <variable calculation="Nothing"
        class="br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscalPK"
        name="ID" resetType="Report">
        <variableExpression><![CDATA[$F{Id}]]></variableExpression>
    </variable>
    <variable calculation="Nothing"
        class="br.com.ksisolucoes.vo.entradas.estoque.Produto"
        name="PRODUTO" resetType="Report">
        <variableExpression><![CDATA[$F{Produto}]]></variableExpression>
    </variable>
    <variable calculation="Nothing"
        class="br.com.ksisolucoes.vo.entradas.estoque.CentroCusto"
        name="CENTRO_CUSTO" resetType="Report">
        <variableExpression><![CDATA[$F{CentroCusto}]]></variableExpression>
    </variable>
    <variable calculation="Nothing"
        class="br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal"
        name="REGISTRO_ITEM_NOTA_FISCAL" resetType="Report"/>
    <variable calculation="Sum" class="java.lang.Double"
        name="SomaValorTotal" resetGroup="GrupoFormaApresentacao" resetType="Group">
        <variableExpression><![CDATA[( $F{Quantidade} * $F{PrecoUnitario} * ( $F{PercentualIpi} / 100 + 1 )  )]]></variableExpression>
    </variable>
    <group isReprintHeaderOnEachPage="false" isResetPageNumber="false"
        isStartNewColumn="false" isStartNewPage="false"
        minHeightToStartNewPage="0" name="GrupoEmpresa">
        <groupExpression><![CDATA[$P{CLIENTE_REPORT}]]></groupExpression>
        <groupHeader>
            <band height="18" isSplitAllowed="true">
                <textField evaluationTime="Now" hyperlinkType="None"
                    isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                    <reportElement backcolor="#CCCCCC"
                        forecolor="#000000" height="15"
                        isPrintInFirstWholeBand="false"
                        isPrintRepeatedValues="true"
                        isPrintWhenDetailOverflows="false"
                        isRemoveLineWhenBlank="false" key="textField-1"
                        mode="Opaque" positionType="FixRelativeToTop"
                        stretchType="NoStretch" width="780" x="1" y="1"/>
                    <textElement lineSpacing="Single" rotation="None"
                        textAlignment="Center" verticalAlignment="Top">
                        <font fontName="Arial" isBold="true"
                            isItalic="false" isPdfEmbedded="false"
                            isStrikeThrough="false" isUnderline="false"
                            pdfEncoding="Cp1252" pdfFontName="Helvetica" size="12"/>
                    </textElement>
                    <textFieldExpression class="java.lang.String"><![CDATA[/*Empresa*/ $P{CLIENTE_REPORT}]]></textFieldExpression>
                </textField>
            </band>
        </groupHeader>
        <groupFooter>
            <band height="0" isSplitAllowed="true"/>
        </groupFooter>
    </group>
    <group isReprintHeaderOnEachPage="false" isResetPageNumber="false"
        isStartNewColumn="false" isStartNewPage="false"
        minHeightToStartNewPage="0" name="GrupoFormaApresentacao">
        <groupExpression><![CDATA[$P{ParamFormaApresentacao}.equals( $V{REGISTRO_ITEM_NOTA_FISCAL}.PROP_PRODUTO ) ? $V{PRODUTO}.getCodigo() : $P{ParamFormaApresentacao}.equals($V{ID}.getNotaFiscal().PROP_RO_FORNECEDOR) ? $V{ID}.getNotaFiscal().getId().getFornecedor().getCodigo() : $V{PRODUTO}.getCodigo()]]></groupExpression>
        <groupHeader>
            <band height="33" isSplitAllowed="true">
                <rectangle radius="5">
                    <reportElement backcolor="#FFFFFF"
                        forecolor="#000000" height="14"
                        isPrintInFirstWholeBand="false"
                        isPrintRepeatedValues="true"
                        isPrintWhenDetailOverflows="false"
                        isRemoveLineWhenBlank="false" key="rectangle-1"
                        mode="Opaque" positionType="FixRelativeToTop"
                        stretchType="NoStretch" width="780" x="1" y="18"/>
                    <graphicElement fill="Solid" pen="Thin" stretchType="NoStretch"/>
                </rectangle>
                <textField evaluationTime="Now" hyperlinkType="None"
                    isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                    <reportElement backcolor="#FFFFFF"
                        forecolor="#000000" height="12"
                        isPrintInFirstWholeBand="false"
                        isPrintRepeatedValues="true"
                        isPrintWhenDetailOverflows="false"
                        isRemoveLineWhenBlank="false" key="textField-35"
                        mode="Opaque" positionType="FixRelativeToTop"
                        stretchType="NoStretch" width="55" x="3" y="19"/>
                    <textElement lineSpacing="Single" rotation="None"
                        textAlignment="Left" verticalAlignment="Middle">
                        <font fontName="Arial" isBold="false"
                            isItalic="false" isPdfEmbedded="false"
                            isStrikeThrough="false" isUnderline="false"
                            pdfEncoding="Cp1252" pdfFontName="Helvetica" size="8"/>
                    </textElement>
                    <textFieldExpression class="java.lang.String"><![CDATA[/*Num. NF*/$V{BUNDLE}.getStringApplication("rotulo_num_nota_fiscal")]]></textFieldExpression>
                </textField>
                <textField evaluationGroup="GrupoFormaApresentacao"
                    evaluationTime="Group" hyperlinkType="None"
                    isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                    <reportElement backcolor="#CCCCCC"
                        forecolor="#000000" height="15"
                        isPrintInFirstWholeBand="false"
                        isPrintRepeatedValues="true"
                        isPrintWhenDetailOverflows="false"
                        isRemoveLineWhenBlank="false" key="textField-60"
                        mode="Opaque" positionType="FixRelativeToTop"
                        stretchType="NoStretch" width="780" x="1" y="1"/>
                    <textElement lineSpacing="Single" rotation="None"
                        textAlignment="Left" verticalAlignment="Top">
                        <font fontName="Arial" isBold="true"
                            isItalic="false" isPdfEmbedded="false"
                            isStrikeThrough="false" isUnderline="false"
                            pdfEncoding="Cp1252" pdfFontName="Helvetica" size="12"/>
                    </textElement>
                    <textFieldExpression class="java.lang.String"><![CDATA[/*FormaApresentacao*/ $P{ParamFormaApresentacao}.equals( $V{REGISTRO_ITEM_NOTA_FISCAL}.PROP_PRODUTO ) ? ( $V{BUNDLE}.getStringApplication("rotulo_produto") + ": " + $V{PRODUTO}.getDescricao() + " (" + $V{PRODUTO}.getCodigo() + ")" ) : $P{ParamFormaApresentacao}.equals( $V{ID}.getNotaFiscal().PROP_RO_FORNECEDOR ) ? ( $V{BUNDLE}.getStringApplication("rotulo_fornecedor") + ": " + $V{ID}.getNotaFiscal().getId().getFornecedor().getDescricao() + " (" + $V{ID}.getNotaFiscal().getId().getFornecedor().getCodigo() + ")" ) : ( $V{BUNDLE}.getStringApplication("rotulo_produto") + ": " + $V{PRODUTO}.getDescricao() + " (" + $V{PRODUTO}.getCodigo() + ")" )]]></textFieldExpression>
                </textField>
                <textField evaluationTime="Now" hyperlinkType="None"
                    isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                    <reportElement backcolor="#FFFFFF"
                        forecolor="#000000" height="12"
                        isPrintInFirstWholeBand="false"
                        isPrintRepeatedValues="true"
                        isPrintWhenDetailOverflows="false"
                        isRemoveLineWhenBlank="false" key="textField-69"
                        mode="Opaque" positionType="FixRelativeToTop"
                        stretchType="NoStretch" width="180" x="58" y="19"/>
                    <textElement lineSpacing="Single" rotation="None"
                        textAlignment="Left" verticalAlignment="Top">
                        <font fontName="Arial" isBold="false"
                            isItalic="false" isPdfEmbedded="false"
                            isStrikeThrough="false" isUnderline="false"
                            pdfEncoding="Cp1252" pdfFontName="Helvetica" size="8"/>
                    </textElement>
                    <textFieldExpression class="java.lang.String"><![CDATA[/*Fornecedor*/$V{BUNDLE}.getStringApplication("rotulo_fornecedor")]]></textFieldExpression>
                </textField>
                <textField evaluationTime="Now" hyperlinkType="None"
                    isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                    <reportElement backcolor="#FFFFFF"
                        forecolor="#000000" height="12"
                        isPrintInFirstWholeBand="false"
                        isPrintRepeatedValues="true"
                        isPrintWhenDetailOverflows="false"
                        isRemoveLineWhenBlank="false" key="textField-70"
                        mode="Opaque" positionType="FixRelativeToTop"
                        stretchType="NoStretch" width="57" x="238" y="19"/>
                    <textElement lineSpacing="Single" rotation="None"
                        textAlignment="Left" verticalAlignment="Top">
                        <font fontName="Arial" isBold="false"
                            isItalic="false" isPdfEmbedded="false"
                            isStrikeThrough="false" isUnderline="false"
                            pdfEncoding="Cp1252" pdfFontName="Helvetica" size="8"/>
                    </textElement>
                    <textFieldExpression class="java.lang.String"><![CDATA[/*Dt Emissao*/$V{BUNDLE}.getStringApplication("rotulo_data_emissao_abv")]]></textFieldExpression>
                </textField>
                <textField evaluationTime="Now" hyperlinkType="None"
                    isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                    <reportElement backcolor="#FFFFFF"
                        forecolor="#000000" height="12"
                        isPrintInFirstWholeBand="false"
                        isPrintRepeatedValues="true"
                        isPrintWhenDetailOverflows="false"
                        isRemoveLineWhenBlank="false" key="textField-71"
                        mode="Opaque" positionType="FixRelativeToTop"
                        stretchType="NoStretch" width="33" x="295" y="19"/>
                    <textElement lineSpacing="Single" rotation="None"
                        textAlignment="Center" verticalAlignment="Top">
                        <font fontName="Arial" isBold="false"
                            isItalic="false" isPdfEmbedded="false"
                            isStrikeThrough="false" isUnderline="false"
                            pdfEncoding="Cp1252" pdfFontName="Helvetica" size="8"/>
                    </textElement>
                    <textFieldExpression class="java.lang.String"><![CDATA[/*Item*/$V{BUNDLE}.getStringApplication("rotulo_item")]]></textFieldExpression>
                </textField>
                <textField evaluationTime="Now" hyperlinkType="None"
                    isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                    <reportElement backcolor="#FFFFFF"
                        forecolor="#000000" height="12"
                        isPrintInFirstWholeBand="false"
                        isPrintRepeatedValues="true"
                        isPrintWhenDetailOverflows="false"
                        isRemoveLineWhenBlank="false" key="textField-72"
                        mode="Opaque" positionType="FixRelativeToTop"
                        stretchType="NoStretch" width="170" x="328" y="19"/>
                    <textElement lineSpacing="Single" rotation="None"
                        textAlignment="Left" verticalAlignment="Top">
                        <font fontName="Arial" isBold="false"
                            isItalic="false" isPdfEmbedded="false"
                            isStrikeThrough="false" isUnderline="false"
                            pdfEncoding="Cp1252" pdfFontName="Helvetica" size="8"/>
                    </textElement>
                    <textFieldExpression class="java.lang.String"><![CDATA[/*Produto*/$V{BUNDLE}.getStringApplication("rotulo_produto")]]></textFieldExpression>
                </textField>
                <textField evaluationTime="Now" hyperlinkType="None"
                    isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                    <reportElement backcolor="#FFFFFF"
                        forecolor="#000000" height="12"
                        isPrintInFirstWholeBand="false"
                        isPrintRepeatedValues="true"
                        isPrintWhenDetailOverflows="false"
                        isRemoveLineWhenBlank="false" key="textField-73"
                        mode="Opaque" positionType="FixRelativeToTop"
                        stretchType="NoStretch" width="56" x="589" y="19"/>
                    <textElement lineSpacing="Single" rotation="None"
                        textAlignment="Right" verticalAlignment="Top">
                        <font fontName="Arial" isBold="false"
                            isItalic="false" isPdfEmbedded="false"
                            isStrikeThrough="false" isUnderline="false"
                            pdfEncoding="Cp1252" pdfFontName="Helvetica" size="8"/>
                    </textElement>
                    <textFieldExpression class="java.lang.String"><![CDATA[/*QtdeEst*/$V{BUNDLE}.getStringApplication("rotulo_quantidade_estoque_abv")]]></textFieldExpression>
                </textField>
                <textField evaluationTime="Now" hyperlinkType="None"
                    isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                    <reportElement backcolor="#FFFFFF"
                        forecolor="#000000" height="12"
                        isPrintInFirstWholeBand="false"
                        isPrintRepeatedValues="true"
                        isPrintWhenDetailOverflows="false"
                        isRemoveLineWhenBlank="false" key="textField-74"
                        mode="Opaque" positionType="FixRelativeToTop"
                        stretchType="NoStretch" width="40" x="645" y="19"/>
                    <textElement lineSpacing="Single" rotation="None"
                        textAlignment="Right" verticalAlignment="Top">
                        <font fontName="Arial" isBold="false"
                            isItalic="false" isPdfEmbedded="false"
                            isStrikeThrough="false" isUnderline="false"
                            pdfEncoding="Cp1252" pdfFontName="Helvetica" size="8"/>
                    </textElement>
                    <textFieldExpression class="java.lang.String"><![CDATA[/*Preco*/$V{BUNDLE}.getStringApplication("rotulo_preco")]]></textFieldExpression>
                </textField>
                <textField evaluationTime="Now" hyperlinkType="None"
                    isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                    <reportElement backcolor="#FFFFFF"
                        forecolor="#000000" height="12"
                        isPrintInFirstWholeBand="false"
                        isPrintRepeatedValues="true"
                        isPrintWhenDetailOverflows="false"
                        isRemoveLineWhenBlank="false" key="textField-75"
                        mode="Opaque" positionType="FixRelativeToTop"
                        stretchType="NoStretch" width="39" x="685" y="19"/>
                    <textElement lineSpacing="Single" rotation="None"
                        textAlignment="Right" verticalAlignment="Top">
                        <font fontName="Arial" isBold="false"
                            isItalic="false" isPdfEmbedded="false"
                            isStrikeThrough="false" isUnderline="false"
                            pdfEncoding="Cp1252" pdfFontName="Helvetica" size="8"/>
                    </textElement>
                    <textFieldExpression class="java.lang.String"><![CDATA[/*%IPI*/$V{BUNDLE}.getStringApplication("rotulo_percentual_ipi")]]></textFieldExpression>
                </textField>
                <textField evaluationTime="Now" hyperlinkType="None"
                    isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                    <reportElement backcolor="#FFFFFF"
                        forecolor="#000000" height="12"
                        isPrintInFirstWholeBand="false"
                        isPrintRepeatedValues="true"
                        isPrintWhenDetailOverflows="false"
                        isRemoveLineWhenBlank="false" key="textField-80"
                        mode="Opaque" positionType="FixRelativeToTop"
                        stretchType="NoStretch" width="59" x="530" y="19"/>
                    <textElement lineSpacing="Single" rotation="None"
                        textAlignment="Right" verticalAlignment="Top">
                        <font fontName="Arial" isBold="false"
                            isItalic="false" isPdfEmbedded="false"
                            isStrikeThrough="false" isUnderline="false"
                            pdfEncoding="Cp1252" pdfFontName="Helvetica" size="8"/>
                    </textElement>
                    <textFieldExpression class="java.lang.String"><![CDATA[/*Qtde*/$V{BUNDLE}.getStringApplication("rotulo_quantidade")]]></textFieldExpression>
                </textField>
                <textField evaluationTime="Now" hyperlinkType="None"
                    isBlankWhenNull="true" isStretchWithOverflow="false" pattern=" #,##0.00">
                    <reportElement backcolor="#FFFFFF"
                        forecolor="#000000" height="12"
                        isPrintInFirstWholeBand="false"
                        isPrintRepeatedValues="true"
                        isPrintWhenDetailOverflows="false"
                        isRemoveLineWhenBlank="false" key="textField-85"
                        mode="Opaque" positionType="FixRelativeToTop"
                        stretchType="NoStretch" width="54" x="724" y="19"/>
                    <textElement lineSpacing="Single" rotation="None"
                        textAlignment="Right" verticalAlignment="Top">
                        <font fontName="Arial" isBold="false"
                            isItalic="false" isPdfEmbedded="false"
                            isStrikeThrough="false" isUnderline="false"
                            pdfEncoding="Cp1252" pdfFontName="Helvetica" size="8"/>
                    </textElement>
                    <textFieldExpression class="java.lang.String"><![CDATA[/*ValorTotal*/$V{BUNDLE}.getStringApplication("rotulo_valor_total")]]></textFieldExpression>
                </textField>
                <textField evaluationTime="Now" hyperlinkType="None"
                    isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                    <reportElement backcolor="#FFFFFF"
                        forecolor="#000000" height="12"
                        isPrintInFirstWholeBand="false"
                        isPrintRepeatedValues="true"
                        isPrintWhenDetailOverflows="false"
                        isRemoveLineWhenBlank="false" key="textField-88"
                        mode="Opaque" positionType="FixRelativeToTop"
                        stretchType="NoStretch" width="32" x="498" y="19"/>
                    <textElement lineSpacing="Single" rotation="None"
                        textAlignment="Center" verticalAlignment="Top">
                        <font fontName="Arial" isBold="false"
                            isItalic="false" isPdfEmbedded="false"
                            isStrikeThrough="false" isUnderline="false"
                            pdfEncoding="Cp1252" pdfFontName="Helvetica" size="8"/>
                    </textElement>
                    <textFieldExpression class="java.lang.String"><![CDATA[/*UN*/$V{BUNDLE}.getStringApplication("rotulo_unidade_abv")]]></textFieldExpression>
                </textField>
            </band>
        </groupHeader>
        <groupFooter>
            <band height="15" isSplitAllowed="true">
                <line direction="TopDown">
                    <reportElement backcolor="#FFFFFF"
                        forecolor="#000000" height="0"
                        isPrintInFirstWholeBand="false"
                        isPrintRepeatedValues="true"
                        isPrintWhenDetailOverflows="false"
                        isRemoveLineWhenBlank="false" key="line-1"
                        mode="Opaque" positionType="FixRelativeToTop"
                        stretchType="NoStretch" width="76" x="703" y="2"/>
                    <graphicElement fill="Solid" pen="Thin" stretchType="NoStretch"/>
                </line>
                <textField evaluationTime="Now" hyperlinkType="None"
                    isBlankWhenNull="true" isStretchWithOverflow="false" pattern="#,##0.00">
                    <reportElement backcolor="#FFFFFF"
                        forecolor="#000000" height="12"
                        isPrintInFirstWholeBand="false"
                        isPrintRepeatedValues="true"
                        isPrintWhenDetailOverflows="false"
                        isRemoveLineWhenBlank="false" key="textField-87"
                        mode="Opaque" positionType="FixRelativeToTop"
                        stretchType="NoStretch" width="54" x="725" y="3"/>
                    <textElement lineSpacing="Single" rotation="None"
                        textAlignment="Right" verticalAlignment="Top">
                        <font fontName="Arial" isBold="false"
                            isItalic="false" isPdfEmbedded="false"
                            isStrikeThrough="false" isUnderline="false"
                            pdfEncoding="Cp1252" pdfFontName="Helvetica" size="8"/>
                    </textElement>
                    <textFieldExpression class="java.lang.Double"><![CDATA[$V{SomaValorTotal}]]></textFieldExpression>
                </textField>
            </band>
        </groupFooter>
    </group>
    <background>
        <band height="0" isSplitAllowed="true"/>
    </background>
    <title>
        <band height="0" isSplitAllowed="true"/>
    </title>
    <pageHeader>
        <band height="50" isSplitAllowed="true">
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="false" isStretchWithOverflow="false" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="18" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" mode="Transparent"
                    positionType="FixRelativeToTop" width="263" x="2" y="6"/>
                <textElement lineSpacing="Single" textAlignment="Left" verticalAlignment="Top">
                    <font fontName="SansSerif" isBold="true"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="CP1252" pdfFontName="Helvetica" size="12"/>
                </textElement>
                <textFieldExpression><![CDATA[$P{CLIENTE_REPORT}]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="false" isStretchWithOverflow="false" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="21" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" mode="Transparent"
                    positionType="FixRelativeToTop" width="779" x="1" y="22"/>
                <textElement lineSpacing="Single" textAlignment="Center" verticalAlignment="Top">
                    <font fontName="SansSerif" isBold="true"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="CP1252" pdfFontName="Helvetica" size="12"/>
                </textElement>
                <textFieldExpression><![CDATA[$P{TITULO_REPORT}]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="false" isStretchWithOverflow="false" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="18" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" mode="Transparent"
                    positionType="FixRelativeToTop" width="38" x="740" y="6"/>
                <textElement lineSpacing="Single" textAlignment="Left" verticalAlignment="Top">
                    <font fontName="SansSerif" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="CP1252" pdfFontName="Helvetica" size="12"/>
                </textElement>
                <textFieldExpression><![CDATA[""+$V{PAGE_NUMBER}]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="false" isStretchWithOverflow="false" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="18" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" mode="Transparent"
                    positionType="FixRelativeToTop" width="99" x="670" y="6"/>
                <textElement lineSpacing="Single" textAlignment="Left" verticalAlignment="Top">
                    <font fontName="SansSerif" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="CP1252" pdfFontName="Helvetica" size="12"/>
                </textElement>
                <textFieldExpression><![CDATA["Pgina " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
            </textField>
            <line direction="TopDown">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="0" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" mode="Opaque"
                    positionType="FixRelativeToTop" width="779" x="1" y="4"/>
                <graphicElement fill="Solid" pen="Thin" stretchType="NoStretch"/>
            </line>
            <line direction="TopDown">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="0" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" mode="Opaque"
                    positionType="FixRelativeToTop" width="779" x="1" y="46"/>
                <graphicElement fill="Solid" pen="Thin" stretchType="NoStretch"/>
            </line>
        </band>
    </pageHeader>
    <columnHeader>
        <band height="32" isSplitAllowed="true">
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="16" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="textField-27"
                    mode="Opaque" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="216" x="48" y="0"/>
                <textElement lineSpacing="Single" rotation="None"
                    textAlignment="Left" verticalAlignment="Top">
                    <font fontName="SansSerif" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="CP1252" pdfFontName="Helvetica" size="10"/>
                </textElement>
                <textFieldExpression class="java.lang.String"><![CDATA[$P{ParamPeriodo}]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="16" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="textField-31"
                    mode="Opaque" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="46" x="1" y="0"/>
                <textElement lineSpacing="Single" rotation="None"
                    textAlignment="Left" verticalAlignment="Top">
                    <font fontName="Arial" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="Cp1252" pdfFontName="Helvetica" size="10"/>
                </textElement>
                <textFieldExpression class="java.lang.String"><![CDATA[/*Periodo*/$V{BUNDLE}.getStringApplication("rotulo_periodo") + ":"]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="16" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="textField-63"
                    mode="Opaque" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="129" x="652" y="16"/>
                <textElement lineSpacing="Single" rotation="None"
                    textAlignment="Left" verticalAlignment="Top">
                    <font fontName="SansSerif" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="CP1252" pdfFontName="Helvetica" size="10"/>
                </textElement>
                <textFieldExpression class="java.lang.String"><![CDATA[$P{ParamOrdenacao}]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="16" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="textField-64"
                    mode="Opaque" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="70" x="581" y="16"/>
                <textElement lineSpacing="Single" rotation="None"
                    textAlignment="Right" verticalAlignment="Top">
                    <font fontName="Arial" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="Cp1252" pdfFontName="Helvetica" size="10"/>
                </textElement>
                <textFieldExpression class="java.lang.String"><![CDATA[/*Ordenacao*/$V{BUNDLE}.getStringApplication("rotulo_ordenacao") + ":"]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="16" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="textField-65"
                    mode="Opaque" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="129" x="652" y="0"/>
                <textElement lineSpacing="Single" rotation="None"
                    textAlignment="Left" verticalAlignment="Top">
                    <font fontName="SansSerif" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="CP1252" pdfFontName="Helvetica" size="10"/>
                </textElement>
                <textFieldExpression class="java.lang.String"><![CDATA[$P{ParamAgrupamento}]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="16" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="textField-66"
                    mode="Opaque" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="153" x="498" y="0"/>
                <textElement lineSpacing="Single" rotation="None"
                    textAlignment="Right" verticalAlignment="Top">
                    <font fontName="Arial" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="Cp1252" pdfFontName="Helvetica" size="10"/>
                </textElement>
                <textFieldExpression class="java.lang.String"><![CDATA[/*FormaApresentacao*/$V{BUNDLE}.getStringApplication("rotulo_forma_apresentacao") + ":"]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="16" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="textField-67"
                    mode="Opaque" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="216" x="48" y="16"/>
                <textElement lineSpacing="Single" rotation="None"
                    textAlignment="Left" verticalAlignment="Top">
                    <font fontName="SansSerif" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="CP1252" pdfFontName="Helvetica" size="10"/>
                </textElement>
                <textFieldExpression class="java.lang.String"><![CDATA[$P{ParamStatus}]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="16" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="textField-68"
                    mode="Opaque" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="46" x="1" y="16"/>
                <textElement lineSpacing="Single" rotation="None"
                    textAlignment="Left" verticalAlignment="Top">
                    <font fontName="Arial" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="Cp1252" pdfFontName="Helvetica" size="10"/>
                </textElement>
                <textFieldExpression class="java.lang.String"><![CDATA[/*Status*/$V{BUNDLE}.getStringApplication("rotulo_status") + ":"]]></textFieldExpression>
            </textField>
        </band>
    </columnHeader>
    <detail>
        <band height="11" isSplitAllowed="true">
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="10" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="textField-57"
                    mode="Opaque" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="55" x="3" y="0"/>
                <textElement lineSpacing="Single" rotation="None"
                    textAlignment="Left" verticalAlignment="Middle">
                    <font fontName="Arial" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="Cp1252" pdfFontName="Helvetica" size="8"/>
                </textElement>
                <textFieldExpression class="java.lang.Long"><![CDATA[$V{ID}.getNotaFiscal().getId().getNumeroNotaFiscal()]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="10" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="textField-76"
                    mode="Opaque" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="180" x="58" y="0"/>
                <textElement lineSpacing="Single" rotation="None"
                    textAlignment="Left" verticalAlignment="Middle">
                    <font fontName="Arial" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="Cp1252" pdfFontName="Helvetica" size="8"/>
                </textElement>
                <textFieldExpression class="java.lang.String"><![CDATA[$V{ID}.getNotaFiscal().getId().getFornecedor().getDescricao() + " (" + $V{ID}.getNotaFiscal().getId().getFornecedor().getCodigo() + ")"]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="10" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="textField-77"
                    mode="Opaque" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="57" x="238" y="0"/>
                <textElement lineSpacing="Single" rotation="None"
                    textAlignment="Left" verticalAlignment="Middle">
                    <font fontName="Arial" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="Cp1252" pdfFontName="Helvetica" size="8"/>
                </textElement>
                <textFieldExpression class="java.lang.String"><![CDATA[$V{DATA}.formatar( $V{ID}.getNotaFiscal().getDataEmissao() )]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="10" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="textField-78"
                    mode="Opaque" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="33" x="295" y="0"/>
                <textElement lineSpacing="Single" rotation="None"
                    textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="Cp1252" pdfFontName="Helvetica" size="8"/>
                </textElement>
                <textFieldExpression class="java.lang.Long"><![CDATA[$V{ID}.getItem()]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="10" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="textField-79"
                    mode="Opaque" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="170" x="328" y="0"/>
                <textElement lineSpacing="Single" rotation="None"
                    textAlignment="Left" verticalAlignment="Top">
                    <font fontName="Arial" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="Cp1252" pdfFontName="Helvetica" size="8"/>
                </textElement>
                <textFieldExpression class="java.lang.String"><![CDATA[$V{PRODUTO}.getDescricao() + " (" + $V{PRODUTO}.getDescricao() + ")"]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="true" isStretchWithOverflow="false" pattern=" #,##0.00">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="10" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="textField-81"
                    mode="Opaque" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="59" x="530" y="0"/>
                <textElement lineSpacing="Single" rotation="None"
                    textAlignment="Right" verticalAlignment="Top">
                    <font fontName="Arial" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="Cp1252" pdfFontName="Helvetica" size="8"/>
                </textElement>
                <textFieldExpression class="java.lang.Double"><![CDATA[$F{Quantidade}]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="true" isStretchWithOverflow="false" pattern=" #,##0.00">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="10" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="textField-82"
                    mode="Opaque" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="56" x="589" y="0"/>
                <textElement lineSpacing="Single" rotation="None"
                    textAlignment="Right" verticalAlignment="Top">
                    <font fontName="Arial" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="Cp1252" pdfFontName="Helvetica" size="8"/>
                </textElement>
                <textFieldExpression class="java.lang.Double"><![CDATA[$F{QuantidadeEstoque}]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="true" isStretchWithOverflow="false" pattern=" #,##0.00">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="10" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="textField-83"
                    mode="Opaque" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="40" x="645" y="0"/>
                <textElement lineSpacing="Single" rotation="None"
                    textAlignment="Right" verticalAlignment="Top">
                    <font fontName="Arial" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="Cp1252" pdfFontName="Helvetica" size="8"/>
                </textElement>
                <textFieldExpression class="java.lang.Double"><![CDATA[$F{PrecoUnitario}]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="10" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="textField-84"
                    mode="Opaque" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="39" x="685" y="0"/>
                <textElement lineSpacing="Single" rotation="None"
                    textAlignment="Right" verticalAlignment="Top">
                    <font fontName="Arial" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="Cp1252" pdfFontName="Helvetica" size="8"/>
                </textElement>
                <textFieldExpression class="java.lang.Double"><![CDATA[$F{PercentualIpi}]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="true" isStretchWithOverflow="false" pattern=" #,##0.00">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="10" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="textField-86"
                    mode="Opaque" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="54" x="724" y="0"/>
                <textElement lineSpacing="Single" rotation="None"
                    textAlignment="Right" verticalAlignment="Top">
                    <font fontName="Arial" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="Cp1252" pdfFontName="Helvetica" size="8"/>
                </textElement>
                <textFieldExpression class="java.lang.Double"><![CDATA[( $F{Quantidade} * $F{PrecoUnitario} * ( $F{PercentualIpi} / 100 + 1 )  )]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="true" isStretchWithOverflow="false" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="10" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" key="textField-89"
                    mode="Opaque" positionType="FixRelativeToTop"
                    stretchType="NoStretch" width="32" x="498" y="0"/>
                <textElement lineSpacing="Single" rotation="None"
                    textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="Cp1252" pdfFontName="Helvetica" size="8"/>
                </textElement>
                <textFieldExpression class="java.lang.String"><![CDATA[$V{PRODUTO}.getUnidade().getUnidade()]]></textFieldExpression>
            </textField>
        </band>
    </detail>
    <columnFooter>
        <band height="0" isSplitAllowed="true"/>
    </columnFooter>
    <pageFooter>
        <band height="20" isSplitAllowed="true">
            <staticText>
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="17" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" mode="Transparent"
                    positionType="FixRelativeToTop" width="255" x="525" y="1"/>
                <textElement lineSpacing="Single" textAlignment="Left" verticalAlignment="Top">
                    <font fontName="SansSerif" isBold="true"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="CP1252" pdfFontName="Helvetica" size="12"/>
                </textElement>
                <text><![CDATA[KSI Solues - www.ksisolucoes.com.br]]></text>
            </staticText>
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="false" isStretchWithOverflow="false" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="17" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" mode="Transparent"
                    positionType="FixRelativeToTop" width="161" x="393" y="1"/>
                <textElement lineSpacing="Single" textAlignment="Left" verticalAlignment="Top">
                    <font fontName="SansSerif" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="CP1252" pdfFontName="Helvetica" size="12"/>
                </textElement>
                <textFieldExpression><![CDATA[new SimpleDateFormat("dd/MM/yyyy - HH:mm").format(new Date())]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Now" hyperlinkType="None"
                isBlankWhenNull="false" isStretchWithOverflow="false" pattern="">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="17" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" mode="Transparent"
                    positionType="FixRelativeToTop" width="200" x="0" y="1"/>
                <textElement lineSpacing="Single" textAlignment="Left" verticalAlignment="Top">
                    <font fontName="SansSerif" isBold="false"
                        isItalic="false" isPdfEmbedded="false"
                        isStrikeThrough="false" isUnderline="false"
                        pdfEncoding="CP1252" pdfFontName="Helvetica" size="12"/>
                </textElement>
                <textFieldExpression><![CDATA[$P{USUARIO_REPORT}]]></textFieldExpression>
            </textField>
            <line direction="TopDown">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="0" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" mode="Transparent"
                    positionType="FixRelativeToTop" width="779" x="1" y="16"/>
                <graphicElement fill="Solid" pen="Thin" stretchType="NoStretch"/>
            </line>
            <line direction="TopDown">
                <reportElement backcolor="#FFFFFF" forecolor="#000000"
                    height="0" isPrintInFirstWholeBand="false"
                    isPrintRepeatedValues="true"
                    isPrintWhenDetailOverflows="false"
                    isRemoveLineWhenBlank="false" mode="Transparent"
                    positionType="FixRelativeToTop" width="779" x="1" y="0"/>
                <graphicElement fill="Solid" pen="Thin" stretchType="NoStretch"/>
            </line>
        </band>
    </pageFooter>
    <summary>
        <band height="0" isSplitAllowed="true"/>
    </summary>
</jasperReport>
