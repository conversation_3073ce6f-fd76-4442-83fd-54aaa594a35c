/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.agendamento;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelacaoVagasAgendaDTOParam;
import br.com.ksisolucoes.report.agendamento.query.QueryRelacaoVagasAgenda;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;

/**
 *
 * <AUTHOR>
 */
public class RelacaoVagasAgenda extends AbstractReport<RelacaoVagasAgendaDTOParam> {

    public RelacaoVagasAgenda(RelacaoVagasAgendaDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        addParametro("EXIBIR_PROFISSIONAL", RepositoryComponentDefault.SIM_LONG.equals(this.getParam().getExibirProfissional()));
        return "/br/com/ksisolucoes/report/agendamento/jrxml/relacao_vagas_agenda.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_vagas_agendas");
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelacaoVagasAgenda();
    }
    
}
