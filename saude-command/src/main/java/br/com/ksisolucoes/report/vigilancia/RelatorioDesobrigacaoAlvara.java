package br.com.ksisolucoes.report.vigilancia;

import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ImpressaoAlvaraDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QRCodeGenerateDTOParam;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.vigilancia.query.QueryRelatorioDesobrigacaoAlvara;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaEnum;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

public class RelatorioDesobrigacaoAlvara extends AbstractReport<Object> {

    private boolean exibirCnae;
    private boolean exibirGrupo;
    private boolean gestaoAtividadeEstabelecimentoPorCnae;
    private ImpressaoAlvaraDTOParam dtoParam;

    public RelatorioDesobrigacaoAlvara(ImpressaoAlvaraDTOParam param) {
        super(param);
        this.dtoParam = param;
    }

    @Override
    public ITransferDataReport getQuery() throws ValidacaoException {
        ConfiguracaoVigilancia configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        exibirCnae = ConfiguracaoVigilanciaEnum.TipoAtividadeAlvara.CNAE.value().equals(configuracaoVigilancia.getTipoAtividadeAlvara())
                && !ConfiguracaoVigilanciaEnum.TipoGestaoAtividade.CNAE.value().equals(configuracaoVigilancia.getFlagTipoGestaoAtividade());
        exibirGrupo = RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getExibirGrupoAlvara());
        gestaoAtividadeEstabelecimentoPorCnae = ConfiguracaoVigilanciaEnum.TipoGestaoAtividade.CNAE.value().equals(configuracaoVigilancia.getFlagTipoGestaoAtividade());

        addParametro("exibirCnae", this.exibirCnae);
        addParametro("exibirGrupo", this.exibirGrupo);
        addParametro("gestaoAtividadeEstabelecimentoPorCnae", this.gestaoAtividadeEstabelecimentoPorCnae);
        addParametro("dataAtual", DataUtil.getDataAtual());

        QRCodeGenerateDTOParam qrCodeParamAlvara = new QRCodeGenerateDTOParam(VigilanciaHelper.getURLQRCodePageAlvara(), dtoParam.getChaveQrCode());
        if (qrCodeParamAlvara != null) {
            addParametro("urlQRcode", qrCodeParamAlvara.generateURL());
        } else {
            addParametro("urlQRcode", "");
        }

        Empresa empresa = LoadManager.getInstance(Empresa.class)
                .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_CODIGO, configuracaoVigilancia.getEmpresa().getCodigo()))
                .setMaxResults(1).start().getVO();

        Cidade cidade = LoadManager.getInstance(Cidade.class)
                .addParameter(new QueryCustom.QueryCustomParameter(Cidade.PROP_CODIGO, empresa.getCidade().getCodigo()))
                .setMaxResults(1).start().getVO();

        addParametro("cidade", empresa.getCidade().getDescricao());
        addParametro("uf", cidade.getEstado().getSigla());

        StringBuilder enderecoVigilanciaBuilder = new StringBuilder(Coalesce.asString(empresa.getEnderecoRuaNumeroBairroCidadeEstadoFormatado()));
        enderecoVigilanciaBuilder.append("\n");
        enderecoVigilanciaBuilder.append(" TELEFONE: ");
        enderecoVigilanciaBuilder.append(empresa.getTelefoneFormatado());
        enderecoVigilanciaBuilder.append(" / ");
        enderecoVigilanciaBuilder.append(empresa.getCelularFormatado());
        addParametro("ENDERECO_VIGILANCIA", enderecoVigilanciaBuilder.toString());


        return new QueryRelatorioDesobrigacaoAlvara();
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/vigilancia/jrxml/desobrigacao_alvara.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_titulo_desobrigacao_alvara");
    }
}
