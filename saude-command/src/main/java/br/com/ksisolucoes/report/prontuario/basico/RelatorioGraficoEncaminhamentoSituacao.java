package br.com.ksisolucoes.report.prontuario.basico;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioGraficoEncaminhamentoSituacaoDTOParam;
import br.com.ksisolucoes.report.prontuario.basico.query.QueryRelatorioGraficoEncaminhamentoSituacao;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioGraficoEncaminhamentoSituacao extends AbstractReport<RelatorioGraficoEncaminhamentoSituacaoDTOParam> {

    public RelatorioGraficoEncaminhamentoSituacao(RelatorioGraficoEncaminhamentoSituacaoDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_grafico_encaminhamento_situacao.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_encaminhamentos_por_situacao");
    }

    @Override
    public ITransferDataReport getQuery() {
        addParametro("tipoDado", getParam().getTipoDado());
        return new QueryRelatorioGraficoEncaminhamentoSituacao();
    }

}
