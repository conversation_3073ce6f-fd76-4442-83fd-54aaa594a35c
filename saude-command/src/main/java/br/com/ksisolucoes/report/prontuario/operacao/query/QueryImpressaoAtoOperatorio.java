package br.com.ksisolucoes.report.prontuario.operacao.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.prontuario.operacao.interfaces.dto.QueryImpressaoAtoOperatorioDTO;
import br.com.ksisolucoes.report.prontuario.operacao.interfaces.dto.QueryImpressaoAtoOperatorioDTOParam;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryImpressaoAtoOperatorio extends CommandQuery<QueryImpressaoAtoOperatorio> implements ITransferDataReport<QueryImpressaoAtoOperatorioDTOParam, QueryImpressaoAtoOperatorioDTO> {

    private QueryImpressaoAtoOperatorioDTOParam param;
    private List<QueryImpressaoAtoOperatorioDTO> result;
    
    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("atoOperatorioItem.descricaoAtoCirurgico", "atoOperatorioItem.descricaoAtoCirurgico");
        
        hql.addToSelect("cirurgiao.codigo", "atoOperatorioItem.atoOperatorio.profissionalCirurgiao.codigo");
        hql.addToSelect("cirurgiao.nome", "atoOperatorioItem.atoOperatorio.profissionalCirurgiao.nome");
        hql.addToSelect("anestesista.codigo", "atoOperatorioItem.atoOperatorio.profissionalAnestesista.codigo");
        hql.addToSelect("anestesista.nome", "atoOperatorioItem.atoOperatorio.profissionalAnestesista.nome");
        hql.addToSelect("auxiliar.codigo", "atoOperatorioItem.atoOperatorio.segundoAuxiliar.codigo");
        hql.addToSelect("auxiliar.nome", "atoOperatorioItem.atoOperatorio.segundoAuxiliar.nome");
        hql.addToSelect("instrumentador.codigo", "atoOperatorioItem.atoOperatorio.primeiroAuxiliar.codigo");
        hql.addToSelect("instrumentador.nome", "atoOperatorioItem.atoOperatorio.primeiroAuxiliar.nome");
        hql.addToSelect("terceiroAuxiliar.codigo", "atoOperatorioItem.atoOperatorio.terceiroAuxiliar.codigo");
        hql.addToSelect("terceiroAuxiliar.nome", "atoOperatorioItem.atoOperatorio.terceiroAuxiliar.nome");

        hql.addToSelect("atoOperatorio.tipoAnestesia", "atoOperatorioItem.atoOperatorio.tipoAnestesia");
        hql.addToSelect("atoOperatorio.diagnosticoPreOperatorio", "atoOperatorioItem.atoOperatorio.diagnosticoPreOperatorio");
        hql.addToSelect("atoOperatorio.tipoOperacao", "atoOperatorioItem.atoOperatorio.tipoOperacao");
        hql.addToSelect("atoOperatorio.diagnosticoPosOperatorio", "atoOperatorioItem.atoOperatorio.diagnosticoPosOperatorio");
        hql.addToSelect("atoOperatorio.relatorioImediatoPatologista", "atoOperatorioItem.atoOperatorio.relatorioImediatoPatologista");
        hql.addToSelect("atoOperatorio.exameRadiologicoAto", "atoOperatorioItem.atoOperatorio.exameRadiologicoAto");
        hql.addToSelect("atoOperatorio.acidenteDuranteOperacao", "atoOperatorioItem.atoOperatorio.acidenteDuranteOperacao");
        
        hql.addToSelect("usuarioCadsus.codigo", "atoOperatorioItem.atoOperatorio.usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "atoOperatorioItem.atoOperatorio.usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.sexo", "atoOperatorioItem.atoOperatorio.usuarioCadsus.sexo");
        hql.addToSelect("usuarioCadsus.dataNascimento", "atoOperatorioItem.atoOperatorio.usuarioCadsus.dataNascimento");
        
        hql.setTypeSelect(QueryImpressaoAtoOperatorioDTO.class.getName());
        hql.addToFrom("AtoOperatorioItem atoOperatorioItem"
                + " right join atoOperatorioItem.atoOperatorio atoOperatorio"
                + " left join atoOperatorio.profissionalCirurgiao cirurgiao"
                + " left join atoOperatorio.profissionalAnestesista anestesista"
                + " left join atoOperatorio.segundoAuxiliar auxiliar"
                + " left join atoOperatorio.primeiroAuxiliar instrumentador"
                + " left join atoOperatorio.terceiroAuxiliar terceiroAuxiliar"
                + " left join atoOperatorio.usuarioCadsus usuarioCadsus"
                + " left join atoOperatorio.atendimento atendimento"
                + " left join atendimento.empresa empresa"
                );
        
        hql.addToWhereWhithAnd("atoOperatorio.codigo = ", param.getCodigoAtoOperacao());
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>)result);
    }

    @Override
    public void setDTOParam(QueryImpressaoAtoOperatorioDTOParam param) {
        this.param = param;
    }

    @Override
    public List<QueryImpressaoAtoOperatorioDTO> getResult() {
        return result;
    }

}
