package br.com.ksisolucoes.report.atividadegrupo;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.atividadegrupo.interfaces.dto.RelatorioRelacaoProcedimentoAtividadeDTOParam;
import br.com.ksisolucoes.report.atividadegrupo.query.QueryRelatorioRelacaoProcedimentoAtividade;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoProcedimentoAtividade extends AbstractReport<RelatorioRelacaoProcedimentoAtividadeDTOParam> {

    public RelatorioRelacaoProcedimentoAtividade(RelatorioRelacaoProcedimentoAtividadeDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        if (RelatorioRelacaoProcedimentoAtividadeDTOParam.TipoRelatorio.DETALHADO.equals(getParam().getTipoRelatorio())) {
            return "/br/com/ksisolucoes/report/atividadegrupo/jrxml/relatorio_relacao_procedimento_atividade.jrxml";
        } else{
            return "/br/com/ksisolucoes/report/atividadegrupo/jrxml/relatorio_relacao_procedimento_atividade_resumido.jrxml";
        }
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_procedimentos_atividades");
    }

    @Override
    public ITransferDataReport getQuery() {
        addParametro("formaApresentacao", getParam().getFormaApresentacao());
        addParametro("quantidadePor", getParam().getQuantidadePor());
        return new QueryRelatorioRelacaoProcedimentoAtividade();
    }

}