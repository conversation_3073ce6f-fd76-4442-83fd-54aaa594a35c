/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.prontuario.procedimento;

import br.com.ksisolucoes.report.prontuario.procedimento.query.QueryRelatorioPrecedimentoCid;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioPrecedimentoCidParam;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioProcedimentoCid extends AbstractReport<RelatorioPrecedimentoCidParam> {

    public RelatorioProcedimentoCid(RelatorioPrecedimentoCidParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_cid.jrxml";
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioPrecedimentoCid();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relatorio_procedimento_cid");
    }
}
