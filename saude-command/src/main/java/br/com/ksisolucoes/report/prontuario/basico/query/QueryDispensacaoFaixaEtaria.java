package br.com.ksisolucoes.report.prontuario.basico.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioPerfilAtendimentoDTO;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioPerfilAtendimentoDTOParam;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.basico.FaixaEtariaItem;
import br.com.ksisolucoes.vo.basico.FaixaEtariaItemPK;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.hibernate.Criteria;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;

public class QueryDispensacaoFaixaEtaria extends CommandQuery {

    private List<RelatorioPerfilAtendimentoDTO> result;
    private RelatorioPerfilAtendimentoDTOParam param;

    public QueryDispensacaoFaixaEtaria(RelatorioPerfilAtendimentoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        String descricao = "";
        try {
            Criteria cFaixaEtariaItem = getSession().createCriteria(FaixaEtariaItem.class);
            cFaixaEtariaItem.add(Restrictions.eq(VOUtils.montarPath(FaixaEtariaItem.PROP_ID, FaixaEtariaItemPK.PROP_FAIXA_ETARIA), param.getFaixaEtaria()));
            cFaixaEtariaItem.addOrder(Order.desc(VOUtils.montarPath(FaixaEtariaItem.PROP_ID, FaixaEtariaItemPK.PROP_SEQUENCIA)));
            List<FaixaEtariaItem> faixas = cFaixaEtariaItem.list();
            int count = 0;
            if (CollectionUtils.isNotNullEmpty(faixas)) {
                for (FaixaEtariaItem faixaEtariaItem : faixas) {
                    descricao += " (case when ((extract(years from age(dm.dataDispensacao, usu.dataNascimento)) * 12 + extract(months from age(dm.dataDispensacao, usu.dataNascimento))) >= " + faixaEtariaItem.getIdadeInicial() + " " + " and (extract(years from age(dm.dataDispensacao, usu.dataNascimento)) * 12 + extract(months from age(dm.dataDispensacao, usu.dataNascimento))) <= " + faixaEtariaItem.getIdadeFinal() + " ) " + " then '" + faixaEtariaItem.getDescricao() + "%*" + faixaEtariaItem.getId().getSequencia() + "' " + " else ";
                    count++;
                    if (count == faixas.size()) {
                        descricao += " 'outra' ";
                    }
                }
                for (int i = 0; i < faixas.size(); i++) {
                    descricao += " end) ";
                }
            }
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        hql.addToSelect(descricao, "descricao");
        hql.addToSelect("count(dmi.codigo)", "quantidade");
        {
            //subSelect para o total
            HQLHelper hqlTotal = hql.getNewInstanceSubQuery();
            hqlTotal.addToSelect("count(usu2.codigo)");
            hqlTotal.addToFrom("DispensacaoMedicamentoItem dmi2 "
                    + "left join dmi2.dispensacaoMedicamento dm2 "
                    + "left join dm2.usuarioCadsusDestino usu2 ");
            hqlTotal.addToWhereWhithAnd("dm2.id.empresa in ", param.getEmpresas());
            hqlTotal.addToWhereWhithAnd("usu2 in ", param.getUsuarioCadsus());
            hqlTotal.addToWhereWhithAnd("dm2.dataDispensacao ", param.getPeriodo());
            hql.addToSelect("(" + hqlTotal.getQuery() + ")", "total");
        }
        hql.addToFrom("DispensacaoMedicamentoItem dmi "
                + "left join dmi.dispensacaoMedicamento dm "
                + "left join dm.usuarioCadsusDestino usu ");
        hql.setTypeSelect(RelatorioPerfilAtendimentoDTO.class.getName());
        hql.addToWhereWhithAnd("dm.id.empresa in ", param.getEmpresas());
        hql.addToWhereWhithAnd("usu in ", param.getUsuarioCadsus());
        hql.addToWhereWhithAnd("dm.dataDispensacao ", param.getPeriodo());
        hql.addToGroupAndOrder("1");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
        for (RelatorioPerfilAtendimentoDTO dto : this.result) {
            if (dto.getDescricao() != null) {
                int a;
                Long sequencia;
                String des = dto.getDescricao().trim();
                a = des.indexOf("%*");
                if (a > 0) {
                    sequencia = Long.parseLong(des.substring(a + 2, des.length()));
                    dto.setSequencia(sequencia);
                    dto.setDescricao(des.substring(0, a));
                }
            } else {
                dto.setDescricao(Bundle.getStringApplication("rotulo_nao_informada"));
            }
        }
        if (CollectionUtils.isNotNullEmpty(this.result)) {
            Collections.sort(this.result, new CollectionUtils.BeanComparator("sequencia"));
        }
    }

    @Override
    public List<RelatorioPerfilAtendimentoDTO> getResult() {
        return result;
    }
}
