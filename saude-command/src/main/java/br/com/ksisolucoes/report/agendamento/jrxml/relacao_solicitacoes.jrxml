<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relacao_solicitacoes" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="0138a79c-10af-4338-a8d5-09d5c5e7d210">
	<property name="ireport.zoom" value="2.853116706110004"/>
	<property name="ireport.x" value="362"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento"/>
	<import value="br.com.ksisolucoes.report.agendamento.dto.RelacaoSolicitacoesDTOParam"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<parameter name="FA" class="br.com.ksisolucoes.report.agendamento.dto.RelacaoSolicitacoesDTOParam.FormaApresentacao"/>
	<parameter name="visualizarOcorrencia" class="java.lang.String"/>
	<field name="solicitacaoAgendamento" class="br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento"/>
	<field name="idade" class="java.lang.Long"/>
	<field name="usuarioCadsusOcorrencias" class="java.util.List"/>
	<variable name="solicitacaoAgendamentoFA" class="java.lang.Integer" resetType="Group" resetGroup="FA" calculation="Count">
		<variableExpression><![CDATA[$F{solicitacaoAgendamento}]]></variableExpression>
	</variable>
	<variable name="solicitacaoAgendamentoGeral" class="java.lang.Integer" resetType="Column" calculation="Count">
		<variableExpression><![CDATA[$F{solicitacaoAgendamento}]]></variableExpression>
	</variable>
	<group name="padrao" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band height="10">
				<printWhenExpression><![CDATA[RelacaoSolicitacoesDTOParam.FormaApresentacao.GERAL.equals($P{FA})]]></printWhenExpression>
				<line>
					<reportElement x="0" y="9" width="802" height="1" uuid="abf2f009-ff64-4ee8-ab1c-64c83b44b7d6"/>
				</line>
				<textField>
					<reportElement x="582" y="0" width="54" height="10" uuid="edec8fbf-3711-420b-8d18-d19d57f320a1"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_tipo_consulta")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="160" y="0" width="65" height="10" uuid="ed5b9bc5-7e52-4700-a2e9-dd9b2e9075fe"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_telefone")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="636" y="0" width="45" height="10" uuid="b48181c7-01d9-4f98-8956-fd26d0675c34"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_agendamento_abv2")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="682" y="0" width="120" height="10" uuid="a6e61df6-35a7-4c56-8207-f4799f437e9b"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_unidade_executante_abv")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="498" y="0" width="38" height="10" uuid="21030c85-05e2-4658-905e-7846b8bc1c23"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_prioridade")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="135" y="0" width="23" height="10" uuid="925c96c9-039b-4973-872d-06b9ae3ca527"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_idade")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="452" y="0" width="45" height="10" uuid="1b15450f-5703-4e0f-9e52-1e94a26e3110"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_solicitacao_abv")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="536" y="0" width="45" height="10" uuid="6e039d03-e3a0-4a26-aa01-b27179cd310b"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_situacao_abv")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="225" y="0" width="127" height="10" uuid="32935b28-68ed-4741-be98-95473612e10b"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_unidade")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="1" y="0" width="36" height="10" uuid="626bbd1d-7a3e-435e-af32-0bd768f708a1"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_numero_solicitacao_abv2")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="38" y="0" width="102" height="10" uuid="1f87960c-1274-4b44-ab39-73829f2124de"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_paciente")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="352" y="0" width="100" height="10" uuid="04928532-8c1e-40d0-92f6-e41145f01a60"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_procedimento")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="13">
				<textField>
					<reportElement x="759" y="3" width="43" height="10" uuid="9b32e45b-6814-460b-b523-ea3bdbd4887a"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="651" y="3" width="105" height="10" uuid="bc124ec9-9580-4460-b2f3-a4923a664b11"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_geral_solicitacoes")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="744" y="1" width="58" height="1" uuid="8dcdc3ab-9500-419a-b9c4-8bc13e6db3ca"/>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="FA" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[RelacaoSolicitacoesDTOParam.FormaApresentacao.UNIDADE_SOLICITACAO.equals($P{FA}) ?
    $F{solicitacaoAgendamento}.getEmpresa().getCodigo()
: RelacaoSolicitacoesDTOParam.FormaApresentacao.PROFISSIONAL_SOLICITANTE.equals($P{FA}) ?
    $F{solicitacaoAgendamento}.getProfissional().getCodigo()
: RelacaoSolicitacoesDTOParam.FormaApresentacao.TIPO_PROCEDIMENTO.equals($P{FA}) ?
    $F{solicitacaoAgendamento}.getTipoProcedimento().getTipoProcedimentoClassificacao().getCodigo()+" - "+$F{solicitacaoAgendamento}.getTipoProcedimento().getCodigo()
: RelacaoSolicitacoesDTOParam.FormaApresentacao.CID.equals($P{FA}) ?
    $F{solicitacaoAgendamento}.getCid().getCodigo()
: null]]></groupExpression>
		<groupHeader>
			<band height="24">
				<printWhenExpression><![CDATA[!RelacaoSolicitacoesDTOParam.FormaApresentacao.GERAL.equals($P{FA})]]></printWhenExpression>
				<rectangle radius="10">
					<reportElement x="0" y="0" width="802" height="13" uuid="fc166e9e-fa55-4b28-a202-ce0cef7b96b5"/>
				</rectangle>
				<textField>
					<reportElement x="682" y="14" width="120" height="10" uuid="1a91691c-0ae7-4b68-a884-5b1b42926a44"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_unidade_executante_abv")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="636" y="14" width="45" height="10" uuid="3a9f684d-0064-4bf1-aa4a-939cbc8f6d6f"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_agendamento_abv2")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="452" y="14" width="45" height="10" uuid="f763e176-a250-4e85-b514-672e83263039"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_solicitacao_abv")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="225" y="14" width="127" height="10" uuid="041baaaf-950f-4281-bcce-3d66ac6cd44d"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_unidade")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="352" y="14" width="100" height="10" uuid="e036f4de-7be7-485b-ac96-a3e9826b3b55"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_procedimento")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="582" y="14" width="54" height="10" uuid="dcedeb0c-e67b-4421-94c8-9438f2a97dc9"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_tipo_consulta")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="536" y="14" width="45" height="10" uuid="3461566b-0bfb-43d3-adf0-035f018ce72d"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_situacao_abv")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="135" y="14" width="23" height="10" uuid="93f17d3d-fcb8-46c7-8056-f8e873e72551"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_idade")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="498" y="14" width="38" height="10" uuid="99e62030-addf-4bd7-95a3-453592a38310"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_prioridade")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="38" y="14" width="102" height="10" uuid="55394db8-dd26-403e-a931-b5047793b8e5"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_paciente")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="23" width="802" height="1" uuid="737ab4ae-524b-4fb5-9cdf-dd972e34080a"/>
				</line>
				<textField>
					<reportElement x="0" y="1" width="802" height="11" uuid="67d95818-65e0-4922-9a3a-6562389b0401"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[RelacaoSolicitacoesDTOParam.FormaApresentacao.UNIDADE_SOLICITACAO.equals($P{FA}) ?
    Bundle.getStringApplication("rotulo_empresa_solicitante")+": "+$F{solicitacaoAgendamento}.getEmpresa().getDescricaoFormatadaCnesDescricao()
: RelacaoSolicitacoesDTOParam.FormaApresentacao.PROFISSIONAL_SOLICITANTE.equals($P{FA}) ?
    Bundle.getStringApplication("rotulo_profissional")+": "+$F{solicitacaoAgendamento}.getProfissional().getDescricaoFormatado()
: RelacaoSolicitacoesDTOParam.FormaApresentacao.TIPO_PROCEDIMENTO.equals($P{FA}) ?
    Bundle.getStringApplication("rotulo_tipo_encaminhamento")+": "+
    $F{solicitacaoAgendamento}.getTipoProcedimento().getTipoProcedimentoClassificacao().getDescricaoFormatado()+" / "+$F{solicitacaoAgendamento}.getTipoProcedimento().getDescricaoFormatado()
: RelacaoSolicitacoesDTOParam.FormaApresentacao.CID.equals($P{FA}) ?
Bundle.getStringApplication("rotulo_cid")+": "+
    $F{solicitacaoAgendamento}.getCid().getDescricaoFormatado()
: null]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="1" y="14" width="36" height="10" uuid="32c16012-8e53-4da7-9018-1be933ff15e8"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_numero_solicitacao_abv2")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="160" y="14" width="65" height="10" uuid="599c8c09-620f-42e7-b41e-69b9f08d8f43"/>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_telefone")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="14">
				<printWhenExpression><![CDATA[!RelacaoSolicitacoesDTOParam.FormaApresentacao.GERAL.equals($P{FA})]]></printWhenExpression>
				<textField>
					<reportElement x="759" y="3" width="43" height="10" uuid="db3a6b75-2d17-466d-af7b-3ba884fe1e76"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{solicitacaoAgendamentoFA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="651" y="3" width="105" height="10" uuid="374ac3ac-f7f7-47be-a1ce-a506427fc566"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_solicitacoes")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="744" y="1" width="58" height="1" uuid="26da2b8c-2c6e-4e11-bd32-2385687794e1"/>
				</line>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="24" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="38" y="0" width="102" height="10" uuid="2912d7a3-7310-48b5-b680-210677cac528"/>
				<textElement>
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{solicitacaoAgendamento}.getUsuarioCadsus().getNome()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="135" y="0" width="23" height="10" uuid="d510d049-c808-41e7-ae85-f93ef8c07138"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{idade}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="225" y="0" width="127" height="10" uuid="906b2763-bcf1-427a-9aa8-bacabd17483a"/>
				<textElement>
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{solicitacaoAgendamento}.getEmpresa().getDescricaoFormatadaCnesDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="352" y="0" width="100" height="10" uuid="1b9c7e61-9e81-47bd-9d22-7eb58528ce57"/>
				<textElement>
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{solicitacaoAgendamento}.getTipoProcedimento().getTipoProcedimentoClassificacao().getDescricao()+" / "+$F{solicitacaoAgendamento}.getTipoProcedimento().getDescricao()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="498" y="0" width="38" height="10" uuid="03a819c3-21b3-4504-a727-9521befebd41"/>
				<textElement>
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{solicitacaoAgendamento}.getDescricaoPrioridade()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="452" y="0" width="45" height="10" uuid="1cc44077-07da-4e4f-a5e4-5a07989d0f33"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.formatar($F{solicitacaoAgendamento}.getDataSolicitacao())]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="536" y="0" width="45" height="10" uuid="7f485a35-6788-4ab2-9985-828644bef72b"/>
				<textElement>
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[SolicitacaoAgendamento.getDescricaoSituacaoAbreviada($F{solicitacaoAgendamento}.getStatus())]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="582" y="0" width="54" height="10" uuid="1a8b205f-4430-433f-9259-cac7fdb8a54a"/>
				<textElement>
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{solicitacaoAgendamento}.getDescricaoTipoConsulta()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="636" y="0" width="45" height="10" uuid="a3818233-5808-448d-8a43-9826d55fc7a6"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.formatar($F{solicitacaoAgendamento}.getDataAgendamento())]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="682" y="0" width="120" height="10" uuid="cae9c561-1408-4884-a238-d2c3c178a07a"/>
				<textElement>
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{solicitacaoAgendamento}.getUnidadeExecutante().getDescricao()]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement positionType="Float" x="85" y="10" width="717" height="13" isRemoveLineWhenBlank="true" uuid="81eecb4a-4598-488f-b804-1f9464715985">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM.equals($P{visualizarOcorrencia})]]></printWhenExpression>
				</reportElement>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource( $F{usuarioCadsusOcorrencias} )]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/basico/jrxml/sub_relatorio_usuariocadsus_ocorrencia.jasper"]]></subreportExpression>
			</subreport>
			<textField isBlankWhenNull="true">
				<reportElement x="1" y="0" width="36" height="10" uuid="c59ec162-89a0-40bd-ab94-2c419c101f8b"/>
				<textElement>
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{solicitacaoAgendamento}.getCodigo()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="160" y="0" width="65" height="10" uuid="5b180dcf-e766-4e14-ac85-60b05f411380"/>
				<textElement>
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{solicitacaoAgendamento}.getUsuarioCadsus().getCelularOuTelefonesFormatado()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
