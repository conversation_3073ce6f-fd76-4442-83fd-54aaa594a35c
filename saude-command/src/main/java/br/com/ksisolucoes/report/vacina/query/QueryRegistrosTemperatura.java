package br.com.ksisolucoes.report.vacina.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.vacina.dto.RelatorioRegistrosTemperaturaDTOParam;
import br.com.ksisolucoes.vo.vacina.ControleTemperaturaVacina;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRegistrosTemperatura extends CommandQuery<QueryRegistrosTemperatura> implements ITransferDataReport<RelatorioRegistrosTemperaturaDTOParam, ControleTemperaturaVacina> {

    private RelatorioRegistrosTemperaturaDTOParam param;
    private List<ControleTemperaturaVacina> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(ControleTemperaturaVacina.class.getName());

        hql.addToSelect("controleTemperaturaVacina.codigo", "codigo");
        hql.addToSelect("controleTemperaturaVacina.dia", "dia");
        hql.addToSelect("controleTemperaturaVacina.hora", "hora");
        hql.addToSelect("controleTemperaturaVacina.turno", "turno");
        hql.addToSelect("controleTemperaturaVacina.temperaturaMomento", "temperaturaMomento");
        hql.addToSelect("controleTemperaturaVacina.temperaturaMinima", "temperaturaMinima");
        hql.addToSelect("controleTemperaturaVacina.temperaturaMaxima", "temperaturaMaxima");
        hql.addToSelect("controleTemperaturaVacina.observacao", "observacao");

        hql.addToSelect("equipamento.codigo", "equipamento.codigo");
        hql.addToSelect("equipamento.descricao", "equipamento.descricao");

        hql.addToSelect("profissional.codigo", "profissional.codigo");
        hql.addToSelect("profissional.nome", "profissional.nome");

        hql.addToSelect("empresa.codigo", "empresa.codigo");
        hql.addToSelect("empresa.descricao", "empresa.descricao");

        hql.addToFrom("ControleTemperaturaVacina controleTemperaturaVacina"
                + " left join controleTemperaturaVacina.equipamento equipamento"
                + " left join controleTemperaturaVacina.profissional profissional"
                + " left join controleTemperaturaVacina.empresa empresa"
        );

        hql.addToWhereWhithAnd("controleTemperaturaVacina.dia", this.param.getPeriodo());
        hql.addToWhereWhithAnd("controleTemperaturaVacina.turno = ", this.param.getTurno());
        hql.addToWhereWhithAnd("profissional =", param.getProfissional());
        hql.addToWhereWhithAnd("equipamento =", param.getEquipamento());
        hql.addToWhereWhithAnd("empresa =", param.getEmpresa());

        hql.addToOrder("empresa.descricao");
        hql.addToOrder("equipamento.descricao");
        hql.addToOrder("controleTemperaturaVacina.dia");
        hql.addToOrder("controleTemperaturaVacina.hora");
        hql.addToOrder("profissional.nome");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public Collection getResult() {
        return result;
    }

    @Override
    public void setDTOParam(RelatorioRegistrosTemperaturaDTOParam param) {
        this.param = param;
    }

}
