<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_atestado_medico" columnCount="2" printOrder="Horizontal" pageWidth="535" pageHeight="842" columnWidth="267" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="1e4cd960-28ca-4d2c-8316-46030f3fc899">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.102963396888356"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="descricao" class="java.lang.String"/>
	<field name="quantidadeDouble" class="java.lang.Double"/>
	<field name="totalDouble" class="java.lang.Double"/>
	<variable name="quantidadeDoubleSum" class="java.lang.Double" resetType="Column" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidadeDouble}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="13" splitType="Stretch">
			<textField>
				<reportElement uuid="7f3d470d-af9b-406e-8f14-2100027bd646" x="1" y="0" width="192" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_descricao")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="7319ee8f-221d-4061-a0c4-40853f516f7b" x="193" y="0" width="37" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quantidade_abv")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="a4219d40-4208-4a25-88f5-67b6af51222b" x="229" y="0" width="35" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["%"]]></textFieldExpression>
			</textField>
			<line>
				<reportElement uuid="b79886f9-c15c-42fc-b9b0-fa5a47973ee3" x="0" y="12" width="267" height="1"/>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</line>
			<line>
				<reportElement uuid="657e685c-e724-4514-9357-704eadba42f6" x="266" y="0" width="1" height="13">
					<printWhenExpression><![CDATA[$V{COLUMN_NUMBER}!=2]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</line>
		</band>
	</columnHeader>
	<detail>
		<band height="12" splitType="Stretch">
			<textField>
				<reportElement uuid="fda7d0f8-9e14-49fe-b3ab-136ad18396c8" x="1" y="0" width="192" height="12">
					<property name="net.sf.jasperreports.text.truncate.at.char" value="true"/>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
			<textField pattern="###0">
				<reportElement uuid="ffe8bf71-d718-4362-88ec-1e69f09600f4" x="193" y="0" width="37" height="12"/>
				<textElement textAlignment="Right">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidadeDouble}]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00;-###0.00">
				<reportElement uuid="d8e09634-e869-47f8-b4bf-3d6bf2a89e91" x="229" y="0" width="35" height="12"/>
				<textElement textAlignment="Right">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{quantidadeDouble}*100)/$F{totalDouble}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement uuid="8d1ab145-ffac-4bb7-9fe1-553831ea5dc2" x="266" y="0" width="1" height="12">
					<printWhenExpression><![CDATA[$V{COLUMN_NUMBER}!=2]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</line>
		</band>
	</detail>
	<columnFooter>
		<band height="12" splitType="Stretch">
			<textField pattern="###0.00;-###0.00">
				<reportElement uuid="18cb3d96-38fc-45ff-849c-1322901b7001" x="229" y="0" width="35" height="12">
					<printWhenExpression><![CDATA[$V{COLUMN_NUMBER}==2]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[($V{quantidadeDoubleSum}*100)/$F{totalDouble}]]></textFieldExpression>
			</textField>
			<textField pattern="###0">
				<reportElement uuid="32812670-a0e8-42d2-90a3-af75f8fbacbf" x="193" y="0" width="37" height="12">
					<printWhenExpression><![CDATA[$V{COLUMN_NUMBER}==2]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{quantidadeDoubleSum}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="0a453dfb-b4fc-4beb-9114-c5b33f222464" x="1" y="0" width="192" height="12">
					<printWhenExpression><![CDATA[$V{COLUMN_NUMBER}==2]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement uuid="4a36a761-2eb3-4848-ac64-895df7aee415" x="266" y="0" width="1" height="12">
					<printWhenExpression><![CDATA[$V{COLUMN_NUMBER}!=2]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</line>
		</band>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
