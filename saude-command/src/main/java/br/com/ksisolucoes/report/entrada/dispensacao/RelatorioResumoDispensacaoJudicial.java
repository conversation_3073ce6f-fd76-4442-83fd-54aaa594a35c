/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.entrada.dispensacao;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioResumoDispensacaoJudicialDTOParam;
import br.com.ksisolucoes.report.entrada.dispensacao.query.QueryRelatorioResumoDispensacaoJudicial;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioResumoDispensacaoJudicial extends AbstractReport<RelatorioResumoDispensacaoJudicialDTOParam> {

    public RelatorioResumoDispensacaoJudicial(RelatorioResumoDispensacaoJudicialDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_resumo_dispensacao_judicial.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication( "rotulo_resumo_dispensacoes_judiciais" );
    }

    @Override
    public ITransferDataReport getQuery() {
        addParametro("formaApresentacao", getParam().getFormaApresentacao());
        return new QueryRelatorioResumoDispensacaoJudicial();
    }

}
