package br.com.ksisolucoes.report.vacina.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.AliasToBeanNestedResultTransformer;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.vacina.dto.RelatorioCertificadoVacinacaoDigitalDTO;
import br.com.ksisolucoes.report.vacina.dto.RelatorioCertificadoVacinacaoDigitalDTOParam;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vacina.VacinaAplicacao;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.type.DateType;
import org.hibernate.type.LongType;
import org.hibernate.type.StringType;

import java.util.Collection;
import java.util.List;

public class QueryRelatorioCertificadoDigitalVacinacao extends CommandQuery<RelatorioCertificadoVacinacaoDigitalDTO> implements ITransferDataReport<RelatorioCertificadoVacinacaoDigitalDTOParam, RelatorioCertificadoVacinacaoDigitalDTO> {

//    private SQLQuery sqlQuery;
    private RelatorioCertificadoVacinacaoDigitalDTOParam param;
    private List<RelatorioCertificadoVacinacaoDigitalDTO> result;

    public QueryRelatorioCertificadoDigitalVacinacao(RelatorioCertificadoVacinacaoDigitalDTOParam param){
        this.param = param;
    }



    @Override
    protected void createQuery(HQLHelper hql) throws DAOException {
        hql.setTypeSelect(RelatorioCertificadoVacinacaoDigitalDTO.class.getName());
        hql.setUseSQL(true);
        hql.addToSelect("dom.cns", "cns");
        hql.addToSelect("vac_ap.cd_vac_aplicacao", "vacinaAplicacao.codigo");
        hql.addToSelect("vac_ap.ds_vacina", "vacinaAplicacao.descricaoVacina");
        hql.addToSelect("ge.dt_validade", "vacinaAplicacao.dataValidade");
        hql.addToSelect("tp_vac.cd_vacina","tipoVacina.codigo");
        hql.addToSelect("emp.empresa","empresa.codigo");
        hql.addToSelect("usu.nm_usuario","nomeVacinado");
        hql.addToSelect("usu.dt_nascimento","dataNascimento");
        hql.addToSelect("emp.descricao","empresa.descricao");
        hql.addToSelect("vac_ap.lote","vacinaAplicacao.lote");
        hql.addToSelect("usu.sg_sexo","paciente.sexo");
        hql.addToSelect("pro.nm_profissional","profissional.nome");
        hql.addToSelect("emp.cnes","empresa.cnes");
        hql.addToSelect("usu.cpf","cpf");
        hql.addToSelect("pro.cd_cns","profissional.codigoCns");
        hql.addToSelect("vac_ap.cd_doses","vacinaAplicacao.dose");
        hql.addToSelect("usu.nacionalidade","paciente.nacionalidade");
        hql.addToSelect("pro.cd_profissional","profissional.codigo");
        hql.addToSelect("vac_ap.dt_aplicacao","vacinaAplicacao.dataAplicacao");
        hql.addToSelect("tp_vac.ds_vacina","tipoVacina.descricao");
        hql.addToSelect("fm.ds_fabricante","laboratorio");

        hql.setFrom(
                " vac_aplicacao vac_ap\n" +

                " left outer join usuario_cadsus usu on\n" +
                " vac_ap.cd_usu_cadsus = usu.cd_usu_cadsus\n" +

                " left outer join dom_usuario_cadsus dom on\n" +
                " usu.cd_usu_cadsus = dom.cd_usu_cadsus\n" +

                " left outer join tipo_vacina tp_vac on\n" +
                " vac_ap.cd_vacina = tp_vac.cd_vacina\n" +

                " left outer join profissional pro on\n" +
                " vac_ap.cd_profissional_aplicacao = pro.cd_profissional\n" +

                " left outer join empresa emp on\n" +
                " vac_ap.empresa = emp.empresa\n" +

                " left outer join produto_vacina pv on\n" +
                " vac_ap.cd_produto_vacina = pv.cd_produto_vacina\n" +

                " left outer join produtos prod on\n" +
                " pv.cod_pro = prod.cod_pro\n" +

                " left outer join fabricante_medicamento fm on\n" +
                " prod.cd_fabricante = fm.cd_fabricante "+

                " left outer join grupo_estoque ge\n" +
                        "on ge.empresa = vac_ap.empresa\n" +
                        "and ge.cod_pro = prod.cod_pro\n" +
                        "and ge.grupo_estoque = vac_ap.lote"
                );

        hql.addToWhereWhithAnd("tp_vac.cd_vacina = :tipoVacinaCodigo");
        hql.addToWhereWhithAnd("usu.cd_usu_cadsus = :usuCadsusCodigo");
        hql.addToWhereWhithAnd("vac_ap.status in ("+ VacinaAplicacao.StatusVacinaAplicacao.APLICADA.value()
        +","+ VacinaAplicacao.StatusVacinaAplicacao.REAPLICADA.value()+")");

        hql.addToOrder("vac_ap.ds_vacina asc, vac_ap.cd_doses asc, vac_ap.dt_aplicacao asc ");
    }



    private void setParameters(Query query) {
        if (param.getTipoVacina() != null && param.getTipoVacina().getCodigo() != null) {
            query.setParameter("tipoVacinaCodigo", param.getTipoVacina().getCodigo());
        }

        if (param.getUsuarioCadsus() != null && param.getUsuarioCadsus().getCodigo() != null) {
            query.setParameter("usuCadsusCodigo", param.getUsuarioCadsus().getCodigo());
        }
    }


    protected void customProcess(Session session) throws ValidacaoException, DAOException {

    }


    @Override
    public Collection getResult() {
        return result;
    }

    @Override
    public void setDTOParam(RelatorioCertificadoVacinacaoDigitalDTOParam param) {
        this.param = param;
    }

    @Override
    protected Object executeQuery(Query query) {
        SQLQuery sql = (SQLQuery) query;
        setParameters(sql);
        sql.addScalar("cns", LongType.INSTANCE)
                .addScalar("vacinaAplicacao_codigo", LongType.INSTANCE)
                .addScalar("vacinaAplicacao_descricaoVacina", StringType.INSTANCE)
                .addScalar("vacinaAplicacao_dataValidade", DateType.INSTANCE)
                .addScalar("tipoVacina_codigo", LongType.INSTANCE)
                .addScalar("empresa_codigo", LongType.INSTANCE)
                .addScalar("nomeVacinado", StringType.INSTANCE)
                .addScalar("dataNascimento", DateType.INSTANCE)
                .addScalar("empresa_descricao", StringType.INSTANCE)
                .addScalar("vacinaAplicacao_lote", StringType.INSTANCE)
                .addScalar("paciente_sexo", StringType.INSTANCE)
                .addScalar("profissional_nome", StringType.INSTANCE)
                .addScalar("empresa_cnes", StringType.INSTANCE)
                .addScalar("cpf", StringType.INSTANCE)
                .addScalar("profissional_codigoCns", StringType.INSTANCE)
                .addScalar("vacinaAplicacao_dose", LongType.INSTANCE)
                .addScalar("paciente_nacionalidade", LongType.INSTANCE)
                .addScalar("profissional_codigo", LongType.INSTANCE)
                .addScalar("vacinaAplicacao_dataAplicacao", DateType.INSTANCE)
                .addScalar("tipoVacina_descricao", StringType.INSTANCE)
                .addScalar("laboratorio", StringType.INSTANCE)
                .setResultTransformer(new AliasToBeanNestedResultTransformer(RelatorioCertificadoVacinacaoDigitalDTO.class, super.getHQL().getPropBindingList()));

        result= sql.list();
        return result;
    }
}
