package br.com.ksisolucoes.vo.vigilancia.pendencia.pendenciadia;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.pendencia.PendenciaDia;

/**
 *
 * <AUTHOR>
 */
public class SavePendenciaDia extends SaveVO<PendenciaDia>{

    public SavePendenciaDia(PendenciaDia vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if(vo.getDataCadastro() == null){
            vo.setDataCadastro(Data.getDataAtual());
        }
        
        if(vo.getUsuario() == null){
            vo.setUsuario(SessaoAplicacaoImp.getInstance().getUsuario());
        }
        
        if(vo.getTipoRotina() == null){
            vo.setTipoRotina(PendenciaDia.TipoRotina.PENDENCIA_DIA.value());
        }
    }
}
