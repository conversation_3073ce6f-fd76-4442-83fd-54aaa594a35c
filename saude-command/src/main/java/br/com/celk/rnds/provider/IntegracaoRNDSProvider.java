package br.com.celk.rnds.provider;

import br.com.celk.io.FtpImageUtil;
import br.com.celk.rnds.RNDS;
import br.com.celk.rnds.RNDSBuilder;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.AES256Utils;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.ParametroRnds;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;

public class IntegracaoRNDSProvider {

    public static RNDS getRNDSAPI() throws ValidacaoException {
        ParametroRnds parametroRnds = CargaBasicoPadrao.getInstance(TenantContext.getRealContext()).getParametroRnds();

        RNDSBuilder rndsBuilder = carregarParametros(parametroRnds);
        validarInstanciaAPI(rndsBuilder);

        String ambienteParamJBoss = System.getProperty("ambiente");
        if ("producao".equals(ambienteParamJBoss) && ParametroRnds.Ambiente.PRODUCAO.value().equals(parametroRnds.getAmbiente())) {
            return rndsBuilder.producao();
        }
        Loggable.log.info("RNDS - Homologação");
        return rndsBuilder.homologacao();
    }

    private static void validarInstanciaAPI(RNDSBuilder rndsBuilder) throws ValidacaoException {
        if (rndsBuilder.getPassword() == null) {
            throw new ValidacaoException("Uma senha para o certificado digital deve ser informada nos parmetros da integração.");
        }
        if (rndsBuilder.getEstado() == null) {
            throw new ValidacaoException("Uma UF do solicitante deve ser informada nos parametros da integração.");
        }
        if (rndsBuilder.getKeystore() == null) {
            throw new ValidacaoException("Um certificado digital deve ser informada nos parametros da integração.");
        }
        if (rndsBuilder.getRequisitante() == null) {
            throw new ValidacaoException("Um CNS do solicitante deve ser informada nos parametros da integração.");
        }
    }

    private static RNDSBuilder carregarParametros(ParametroRnds parametroRnds) {
        RNDSBuilder rndsBuilder = new RNDSBuilder()
                .password(getDecryptPass(parametroRnds.getPasswordCertificado()))
                .requisitante(parametroRnds.getCnsSolicitanteSemPonto())
                .estado(parametroRnds.getUfSolicitante())
                .keystore(getPathFile(parametroRnds.getCertificadoDigital()));
        return rndsBuilder;
    }

    private static char[] getDecryptPass(String passwordCertificado) {
        String decryptPass = AES256Utils.decrypt(passwordCertificado);

        return decryptPass.toCharArray();
    }

    private static String getPathFile(GerenciadorArquivo gerenciadorArquivo) {
        if (gerenciadorArquivo != null) {
            String path = new FtpImageUtil().downloadImage(gerenciadorArquivo.getCaminho());
            if (path != null) return path;
        }
        return null;
    }
}
