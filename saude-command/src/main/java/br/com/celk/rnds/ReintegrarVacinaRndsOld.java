package br.com.celk.rnds;

import br.com.celk.rnds.builder.ImmunizationParam;
import br.com.celk.rnds.builder.ImmunizationParamBuilder;
import br.com.celk.rnds.helper.IntegracaoFCSHelper;
import br.com.celk.rnds.helper.ValidacoesIntegracaoVacinaRNDSHelper;
import br.com.celk.rnds.provider.IntegracaoRNDSProvider;
import br.com.celk.rnds.rnds.IntegracaoVacinaRNDSHelperOld;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.rnds.RndsUtil;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vacina.VacinaAplicacao;
import br.com.ksisolucoes.vo.vacina.rnds.RndsIntegracaoVacina;

/**
 * <AUTHOR>
 * @deprecated usar nova integração, parametro UsaNovoModeloIntegracaoRnds
 */
@Deprecated
public class ReintegrarVacinaRndsOld extends AbstractCommandTransaction<ReintegrarVacinaRndsOld> {

    private VacinaAplicacao vacinaAplicacao;
    private RndsIntegracaoVacina rndsIntegracaoVacina;

    public ReintegrarVacinaRndsOld(VacinaAplicacao vacinaAplicacao, RndsIntegracaoVacina rndsIntegracaoVacina) {
        this.vacinaAplicacao = vacinaAplicacao;
        this.rndsIntegracaoVacina = recarregarRndsIntegracaoVacina(rndsIntegracaoVacina);
        this.rndsIntegracaoVacina.setGrupoAtendimento(vacinaAplicacao.getGrupoAtendimentoRnds());
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        ImmunizationParam immunizationParam = getImmunizationParam();
        if (immunizationParam == null) return;

        String jsonParam = RndsUtil.convertDtoToJson(immunizationParam);

        String fhirHl7Pattern = IntegracaoFCSHelper.getFhirHl7Pattern(jsonParam, this.rndsIntegracaoVacina);
        if (fhirHl7Pattern == null) return;

        RNDS rndsapi = IntegracaoRNDSProvider.getRNDSAPI();
        RNDS.Resposta documentoEnviado = rndsapi.enviarDocumento(fhirHl7Pattern);

        IntegracaoVacinaRNDSHelperOld.gerarRetornoIntegracaoVacina(this.rndsIntegracaoVacina, documentoEnviado, documentoEnviado.code);
    }

    public ImmunizationParam getImmunizationParam() {
        ImmunizationParam immunizationParam = ImmunizationParamBuilder.builder()
                .vacinaAplicacao(vacinaAplicacao, true)
                .rndsIntegracaoVacina(rndsIntegracaoVacina)
                .build();

        if (ValidacoesIntegracaoVacinaRNDSHelper.validarDadosIntegracao(this.rndsIntegracaoVacina, immunizationParam))
            return immunizationParam;

        return null;
    }

    private RndsIntegracaoVacina recarregarRndsIntegracaoVacina(RndsIntegracaoVacina rndsIntegracaoVacina) {
        return LoadManager.getInstance(RndsIntegracaoVacina.class)
                .setId(rndsIntegracaoVacina.getCodigo())
                .start().getVO();
    }
}


