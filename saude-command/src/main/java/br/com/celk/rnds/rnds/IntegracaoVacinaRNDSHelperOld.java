package br.com.celk.rnds.rnds;

import br.com.celk.rnds.RNDS;
import br.com.celk.rnds.helper.ValidacoesIntegracaoVacinaRNDSHelper;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vacina.VacinaAplicacao;
import br.com.ksisolucoes.vo.vacina.rnds.RndsIntegracaoVacina;

/**
 * <AUTHOR>
 * @deprecated usar nova integração, parametro UsaNovoModeloIntegracaoRnds
 */
@Deprecated
public class IntegracaoVacinaRNDSHelperOld {

    public static void gerarRetornoIntegracaoVacina(RndsIntegracaoVacina rndsIntegracaoVacina, RNDS.Resposta resposta, int codigoRetorno) {
        rndsIntegracaoVacina.setCodigoRetorno(Long.valueOf(codigoRetorno));
        if (codigoRetorno == 201) {
            rndsIntegracaoVacina.setSituacao(getStatusRetornoOk(rndsIntegracaoVacina));
            rndsIntegracaoVacina.setUuidRnds(resposta.retorno);
        } else {
            rndsIntegracaoVacina.setSituacao(RndsIntegracaoVacina.Situacao.FALHA.value());
            rndsIntegracaoVacina.setDiagnostico(ValidacoesIntegracaoVacinaRNDSHelper.addErro(Coalesce.asString(rndsIntegracaoVacina.getDiagnostico()), resposta.diagnostics));
        }
        saveRndsIntegracaoVacina(rndsIntegracaoVacina);
    }

    public static void gerarRetornoCancelamentoVacina(RndsIntegracaoVacina rndsIntegracaoVacina, RNDS.Resposta resposta, VacinaAplicacao vacinaAplicacao) {
        if (resposta == null) {
            rndsIntegracaoVacina.setSituacao(RndsIntegracaoVacina.Situacao.CANCELADO.value());
            addMsgCancelamento(rndsIntegracaoVacina, vacinaAplicacao.getDescricaoCancelamento());
            saveRndsIntegracaoVacina(rndsIntegracaoVacina);
            return;
        }
        rndsIntegracaoVacina.setCodigoRetorno(Long.valueOf(resposta.code));
        if (resposta.code == 204) {
            rndsIntegracaoVacina.setUuidRnds(resposta.retorno);
            rndsIntegracaoVacina.setSituacao(RndsIntegracaoVacina.Situacao.CANCELADO.value());
        } else {
            addMsgCancelamento(rndsIntegracaoVacina, resposta.diagnostics);
        }
        saveRndsIntegracaoVacina(rndsIntegracaoVacina);
    }

    private static void addMsgCancelamento(RndsIntegracaoVacina rndsIntegracaoVacina, String mensagem) {
        String msgRetornoCancelamento = "Cancelamento: " + mensagem;
        if (rndsIntegracaoVacina.getDiagnostico() == null || rndsIntegracaoVacina.getDiagnostico().isEmpty()) {
            rndsIntegracaoVacina.setDiagnostico(msgRetornoCancelamento);
        } else {
            rndsIntegracaoVacina.setDiagnostico(ValidacoesIntegracaoVacinaRNDSHelper.addErro(Coalesce.asString(rndsIntegracaoVacina.getDiagnostico()), msgRetornoCancelamento));
        }
    }

    public static Long getStatusRetornoOk(RndsIntegracaoVacina rndsIntegracaoVacina) {
        if (RndsIntegracaoVacina.Situacao.REENVIADO.value().equals(rndsIntegracaoVacina.getSituacao()))
            return RndsIntegracaoVacina.Situacao.REENVIADO.value();

        if (RndsIntegracaoVacina.Situacao.ENVIADO.value().equals(rndsIntegracaoVacina.getSituacao()))
            return RndsIntegracaoVacina.Situacao.REENVIADO.value();

        return RndsIntegracaoVacina.Situacao.ENVIADO.value();
    }

    private static void saveRndsIntegracaoVacina(RndsIntegracaoVacina rndsIntegracaoVacina) {
        try {
            BOFactory.save(rndsIntegracaoVacina);
        } catch (DAOException | ValidacaoException e) {
            Loggable.log.error(e.getMessage());
        }
    }
}