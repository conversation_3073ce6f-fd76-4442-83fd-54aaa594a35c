package br.com.celk.rnds.datapaging;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.vacina.pni.interfaces.dto.ConsultaIntegracaoVacinaRndsDTO;
import br.com.ksisolucoes.bo.vacina.pni.interfaces.dto.ConsultaIntegracaoVacinaRndsDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryConsultaIntegracaoVacinaRnds extends CommandQueryPager<QueryConsultaIntegracaoVacinaRnds> {

    private ConsultaIntegracaoVacinaRndsDTOParam param;

    public QueryConsultaIntegracaoVacinaRnds(ConsultaIntegracaoVacinaRndsDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(ConsultaIntegracaoVacinaRndsDTO.class.getName());

        hql.addToSelect("rndsIntegracaoVacina.codigo", "rndsIntegracaoVacina.codigo");
        hql.addToSelect("rndsIntegracaoVacina.uuidOrigem", "rndsIntegracaoVacina.uuidOrigem");
        hql.addToSelect("rndsIntegracaoVacina.uuidRnds", "rndsIntegracaoVacina.uuidRnds");
        hql.addToSelect("rndsIntegracaoVacina.situacao", "rndsIntegracaoVacina.situacao");
        hql.addToSelect("rndsIntegracaoVacina.diagnostico", "rndsIntegracaoVacina.diagnostico");
        hql.addToSelect("rndsIntegracaoVacina.dataUsuario", "rndsIntegracaoVacina.dataUsuario");
        hql.addToSelect("rndsIntegracaoVacina.grupoAtendimento", "rndsIntegracaoVacina.grupoAtendimento");
        hql.addToSelect("rndsIntegracaoVacina.estrategiaRia", "rndsIntegracaoVacina.estrategiaRia");

        hql.addToSelect("rndsIntegracaoVacina.usuario.nome", "rndsIntegracaoVacina.usuario.nome");

        hql.addToSelect("vacinaAplicacao.codigo", "rndsIntegracaoVacina.vacinaAplicacao.codigo");
        hql.addToSelect("vacinaAplicacao.status", "rndsIntegracaoVacina.vacinaAplicacao.status");
        hql.addToSelect("vacinaAplicacao.descricaoVacina", "rndsIntegracaoVacina.vacinaAplicacao.descricaoVacina");
        hql.addToSelect("vacinaAplicacao.lote", "rndsIntegracaoVacina.vacinaAplicacao.lote");
        hql.addToSelect("vacinaAplicacao.dose", "rndsIntegracaoVacina.vacinaAplicacao.dose");
        hql.addToSelect("vacinaAplicacao.dataAplicacao", "rndsIntegracaoVacina.vacinaAplicacao.dataAplicacao");
        hql.addToSelect("vacinaAplicacao.dataAplicacaoFim", "rndsIntegracaoVacina.vacinaAplicacao.dataAplicacaoFim");
        hql.addToSelect("vacinaAplicacao.dataCancelamento", "rndsIntegracaoVacina.vacinaAplicacao.dataCancelamento");
        hql.addToSelect("vacinaAplicacao.flagGestante", "rndsIntegracaoVacina.vacinaAplicacao.flagGestante");
        hql.addToSelect("vacinaAplicacao.localAtendimento", "rndsIntegracaoVacina.vacinaAplicacao.localAtendimento");
        hql.addToSelect("vacinaAplicacao.grupoAtendimento", "rndsIntegracaoVacina.vacinaAplicacao.grupoAtendimento");
        hql.addToSelect("vacinaAplicacao.flagHistorico", "rndsIntegracaoVacina.vacinaAplicacao.flagHistorico");
        hql.addToSelect("vacinaAplicacao.vacinaAplicadaExterior", "rndsIntegracaoVacina.vacinaAplicacao.vacinaAplicadaExterior");

        hql.addToSelect("estrategia.codigo", "rndsIntegracaoVacina.vacinaAplicacao.estrategia.codigo");
        hql.addToSelect("estrategia.codigoEstrategiaPni", "rndsIntegracaoVacina.vacinaAplicacao.estrategia.codigoEstrategiaPni");
        hql.addToSelect("estrategia.estrategiaEsus", "rndsIntegracaoVacina.vacinaAplicacao.estrategia.estrategiaEsus");

        hql.addToSelect("vacinaCalendario.codigo", "rndsIntegracaoVacina.vacinaAplicacao.vacinaCalendario.codigo");
        hql.addToSelect("vacinaCalendario.idade", "rndsIntegracaoVacina.vacinaAplicacao.vacinaCalendario.idade");

        hql.addToSelect("tipoVacCalendario.codigo", "rndsIntegracaoVacina.vacinaAplicacao.vacinaCalendario.tipoVacina.codigo");
        hql.addToSelect("tipoVacCalendario.descricao", "rndsIntegracaoVacina.vacinaAplicacao.vacinaCalendario.tipoVacina.descricao");
        hql.addToSelect("tipoVacCalendario.tipoEsus", "rndsIntegracaoVacina.vacinaAplicacao.vacinaCalendario.tipoVacina.tipoEsus");

        hql.addToSelect("calendario.codigo", "rndsIntegracaoVacina.vacinaAplicacao.vacinaCalendario.calendario.codigo");
        hql.addToSelect("calendario.descricao", "rndsIntegracaoVacina.vacinaAplicacao.vacinaCalendario.calendario.descricao");
        hql.addToSelect("calendario.estrategiaEsus", "rndsIntegracaoVacina.vacinaAplicacao.vacinaCalendario.calendario.estrategiaEsus");
        hql.addToSelect("calendario.codigoEstrategiaPni", "rndsIntegracaoVacina.vacinaAplicacao.vacinaCalendario.calendario.codigoEstrategiaPni");

        hql.addToSelect("tipoVacina.codigo", "rndsIntegracaoVacina.vacinaAplicacao.tipoVacina.codigo");
        hql.addToSelect("tipoVacina.descricao", "rndsIntegracaoVacina.vacinaAplicacao.tipoVacina.descricao");
        hql.addToSelect("tipoVacina.tipoEsus", "rndsIntegracaoVacina.vacinaAplicacao.tipoVacina.tipoEsus");

        hql.addToSelect("empresa.codigo", "rndsIntegracaoVacina.vacinaAplicacao.empresa.codigo");
        hql.addToSelect("empresa.descricao", "rndsIntegracaoVacina.vacinaAplicacao.empresa.descricao");
        hql.addToSelect("empresa.cnes", "rndsIntegracaoVacina.vacinaAplicacao.empresa.cnes");

        hql.addToSelect("usuarioCadsus.codigo", "rndsIntegracaoVacina.vacinaAplicacao.usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "rndsIntegracaoVacina.vacinaAplicacao.usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.dataNascimento", "rndsIntegracaoVacina.vacinaAplicacao.usuarioCadsus.dataNascimento");
        hql.addToSelect("usuarioCadsus.cpf", "rndsIntegracaoVacina.vacinaAplicacao.usuarioCadsus.cpf");
        hql.addToSelect("usuarioCadsus.grupoVacinacao", "rndsIntegracaoVacina.vacinaAplicacao.usuarioCadsus.grupoVacinacao");

        hql.addToSelect("profissionalAplicacao.codigo", "rndsIntegracaoVacina.vacinaAplicacao.profissionalAplicacao.codigo");
        hql.addToSelect("profissionalAplicacao.nome", "rndsIntegracaoVacina.vacinaAplicacao.profissionalAplicacao.nome");
        hql.addToSelect("profissionalAplicacao.codigoCns", "rndsIntegracaoVacina.vacinaAplicacao.profissionalAplicacao.codigoCns");

        hql.addToSelect("produto.codigo", "rndsIntegracaoVacina.vacinaAplicacao.produtoVacina.produto.codigo");
        hql.addToSelect("produto.fabricanteEsus", "rndsIntegracaoVacina.vacinaAplicacao.produtoVacina.produto.fabricanteEsus");

        hql.addToSelect("fabricante.codigo", "rndsIntegracaoVacina.vacinaAplicacao.produtoVacina.produto.fabricante.codigo");
        hql.addToSelect("fabricante.descricao", "rndsIntegracaoVacina.vacinaAplicacao.produtoVacina.produto.fabricante.descricao");
        hql.addToSelect("fabricante.codigoPni", "rndsIntegracaoVacina.vacinaAplicacao.produtoVacina.produto.fabricante.codigoPni");

        hql.addToSelect("vacinaAplicacao.localAplicacao.codigo", "rndsIntegracaoVacina.vacinaAplicacao.localAplicacao.codigo");
        hql.addToSelect("vacinaAplicacao.localAplicacao.descricao", "rndsIntegracaoVacina.vacinaAplicacao.localAplicacao.descricao");

        hql.addToSelect("vacinaAplicacao.viaAdministracao.codigo", "rndsIntegracaoVacina.vacinaAplicacao.viaAdministracao.codigo");
        hql.addToSelect("vacinaAplicacao.viaAdministracao.descricao", "rndsIntegracaoVacina.vacinaAplicacao.viaAdministracao.descricao");

        hql.addToFrom("RndsIntegracaoVacina rndsIntegracaoVacina"
                + " LEFT JOIN rndsIntegracaoVacina.usuario usuario"
                + " LEFT JOIN rndsIntegracaoVacina.vacinaAplicacao vacinaAplicacao"
                + " LEFT JOIN vacinaAplicacao.usuarioCadsus usuarioCadsus"
                + " LEFT JOIN vacinaAplicacao.empresa empresa"
                + " LEFT JOIN vacinaAplicacao.tipoVacina tipoVacina"
                + " LEFT JOIN vacinaAplicacao.profissionalAplicacao profissionalAplicacao"
                + " LEFT JOIN vacinaAplicacao.estrategia estrategia"
                + " LEFT JOIN vacinaAplicacao.vacinaCalendario vacinaCalendario"
                + " LEFT JOIN vacinaCalendario.calendario calendario"
                + " LEFT JOIN vacinaCalendario.tipoVacina tipoVacCalendario"
                + " LEFT JOIN vacinaAplicacao.localAplicacao localAplicacao"
                + " LEFT JOIN vacinaAplicacao.viaAdministracao viaAdministracao"
                + " LEFT JOIN vacinaAplicacao.produtoVacina produtoVacina"
                + " LEFT JOIN produtoVacina.produto produto"
                + " LEFT JOIN produto.fabricante fabricante"
        );

        hql.addToWhereWhithAnd("vacinaAplicacao.dataAplicacao", param.getPeriodo());
        hql.addToWhereWhithAnd("rndsIntegracaoVacina.situacao = ", param.getSituacao());
        hql.addToWhereWhithAnd("empresa = ", param.getEmpresa());
        hql.addToWhereWhithAnd("tipoVacina = ", param.getTipoVacina());

        if (param.getUsuarioCadsus() != null && param.getUsuarioCadsus().getCodigo() != null) {
            hql.addToWhereWhithAnd("usuarioCadsus.codigo = ", param.getUsuarioCadsus().getCodigo());
        }

        hql.addToOrder("rndsIntegracaoVacina.codigo desc");

    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
