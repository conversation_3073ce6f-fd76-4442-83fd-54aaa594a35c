package br.com.celk.rnds;

import br.com.celk.rnds.rnds.IntegracaoVacinaRNDSHelper;
import br.com.celk.service.async.RndsAssincronoQueueDTO;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.Util;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.server.HibernateSessionFactory;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper;
import br.com.ksisolucoes.vo.vacina.VacinaAplicacao;
import br.com.ksisolucoes.vo.vacina.rnds.RndsIntegracaoVacina;
import br.com.ksisolucoes.vo.vacina.rnds.RndsVacinaOcorrencias;
import ch.lambdaj.Lambda;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDate;
import org.joda.time.LocalTime;

import javax.ws.rs.HttpMethod;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class AtualizarIntegracaoVacinaRnds extends AbstractCommandTransaction<AtualizarIntegracaoVacinaRnds> {

    private RndsIntegracaoVacina rndsIntegracaoVacina;
    private RndsAssincronoQueueDTO dtoMessageSqs;

    public AtualizarIntegracaoVacinaRnds(RndsAssincronoQueueDTO rndsAssincronoQueueDTO) {
        this.dtoMessageSqs = rndsAssincronoQueueDTO;
        this.rndsIntegracaoVacina = recarregarRndsIntegracaoVacina(rndsAssincronoQueueDTO.getIdRegistro());
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (rndsIntegracaoVacina == null) return;
        if (isSalvarCNS(rndsIntegracaoVacina.getVacinaAplicacao().getUsuarioCadsus(), dtoMessageSqs.getCnsPaciente())) {
            salvarCnsPaciente(rndsIntegracaoVacina.getVacinaAplicacao().getUsuarioCadsus(), dtoMessageSqs.getCnsPaciente());
        }
        rndsIntegracaoVacina.setSituacao(IntegracaoVacinaRNDSHelper.getStatusRetorno(dtoMessageSqs));
        rndsIntegracaoVacina.setUuidRnds(dtoMessageSqs.getUuidRnds());
        List<RndsVacinaOcorrencias> rndsVacinaOcorrencias = addOcorrencias(dtoMessageSqs.getMensagens());
        Lambda.forEach(rndsVacinaOcorrencias).setRndsIntegracaoVacina(rndsIntegracaoVacina);
        for (RndsVacinaOcorrencias rndsVacinaOcorrencia : rndsVacinaOcorrencias) {
            BOFactory.save(rndsVacinaOcorrencia);
        }

        BOFactory.newTransactionSave(rndsIntegracaoVacina);
    }

    private List<RndsVacinaOcorrencias> addOcorrencias(List<RndsAssincronoQueueDTO.MensagemIntegracao> mensagens) {
        List<RndsVacinaOcorrencias> ocorrencias = new ArrayList<>();
        RndsVacinaOcorrencias ocorrenciasPadrao = new RndsVacinaOcorrencias();
        ocorrenciasPadrao.setDataOcorrencia(LocalDate.now().toDateTime(LocalTime.now()).toDate());
        ocorrenciasPadrao.setDescricao(getDescricaoRegistroAtualizado());
        ocorrenciasPadrao.setDocumentoEnviado(dtoMessageSqs.getDocumentoEnviado());
        ocorrenciasPadrao.setJsonRetorno(dtoMessageSqs.getJsonRetornoRnds());
        ocorrencias.add(ocorrenciasPadrao);
        if (CollectionUtils.isNotNullEmpty(dtoMessageSqs.getMensagens())) {
            for (RndsAssincronoQueueDTO.MensagemIntegracao mensagen : mensagens) {
                if (mensagen.getMensagem() == null || mensagen.getMensagem().isEmpty()) continue;
                RndsVacinaOcorrencias rndsVacinaOcorrencias = new RndsVacinaOcorrencias();
                rndsVacinaOcorrencias.setDataOcorrencia(LocalDate.now().toDateTime(LocalTime.now()).toDate());
                rndsVacinaOcorrencias.setDescricao(mensagen.getMensagem());
                ocorrencias.add(rndsVacinaOcorrencias);
            }
        }
        return ocorrencias;
    }

    private String getDescricaoRegistroAtualizado() {
        StringBuilder sb = new StringBuilder();
        sb.append("Registro Atualizado.");
        if (dtoMessageSqs.getUuidRnds() != null && HttpMethod.POST.equals(dtoMessageSqs.getHttpMethod())) {
            sb.append(" Sucesso - UUID - ").append(dtoMessageSqs.getUuidRnds());
        }
        return sb.toString();
    }

    private void salvarCnsPaciente(UsuarioCadsus usuarioCadsus, String numeroCartao) {
        UsuarioCadsusCns cns = new UsuarioCadsusCns();
        cns.setUsuarioCadsus(usuarioCadsus);
        cns.setExcluido(RepositoryComponentDefault.NAO_LONG);
        cns.setNumeroCartao(Long.valueOf(numeroCartao));
        try {
            usuarioCadsus = (UsuarioCadsus) HibernateSessionFactory.getSession().get(UsuarioCadsus.class, usuarioCadsus.getCodigo());
            usuarioCadsus.setFlagNaoPossuiCns(RepositoryComponentDefault.NAO_LONG);
            BOFactory.save(usuarioCadsus);
            BOFactory.save(cns);
        } catch (ValidacaoException | DAOException e) {
            Loggable.log.error(e.getMessage());
        }
    }

    /**
     * Para salvar o CNS vindo da fila, esse não pode ser null ou vazio nem igual ao que já temos salvo.
     * @param usuarioCadsus
     * @param numeroCartao
     * @return
     */
    private boolean isSalvarCNS(UsuarioCadsus usuarioCadsus, String numeroCartao) {
        if (StringUtils.isNotEmpty(numeroCartao) && !Util.isCpf(numeroCartao)) {
            try {
                Long numeroCartaoUsuario = UsuarioCadsusHelper.carregarNumeroCartao(usuarioCadsus);

                if (numeroCartaoUsuario == null) {
                    return true;
                }
                String numeroCartaoUsuarioString = String.valueOf(numeroCartaoUsuario);
                if (!StringUtils.equalsIgnoreCase(numeroCartaoUsuarioString, numeroCartao)) {
                    return true;
                }
            } catch (Exception e) {
                Loggable.log.error(e.getMessage(), e.getCause());
            }
            return false;
        }
        return false;
    }


    private RndsIntegracaoVacina recarregarRndsIntegracaoVacina(String idRegistro) {
        return LoadManager.getInstance(RndsIntegracaoVacina.class)
                .addProperties(new HQLProperties(RndsIntegracaoVacina.class).getProperties())
                .addProperty(VOUtils.montarPath(RndsIntegracaoVacina.PROP_VACINA_APLICACAO, VacinaAplicacao.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(RndsIntegracaoVacina.PROP_UUID_ORIGEM, idRegistro))
                .start().getVO();
    }
}


