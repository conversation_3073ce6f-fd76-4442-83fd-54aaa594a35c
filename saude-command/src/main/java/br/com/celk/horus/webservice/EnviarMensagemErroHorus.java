package br.com.celk.horus.webservice;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.MensagemDTO;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class EnviarMensagemErroHorus extends AbstractCommandTransaction {

    private Throwable ex;
    private String inconsistencias;

    public EnviarMensagemErroHorus(Throwable ex) {
        this.ex = ex;
    }

    public EnviarMensagemErroHorus(String inconsistencias) {
        this.inconsistencias = inconsistencias;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        Usuario usuarioResponsavelAgendamento = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("usuarioResponsavelAvisoHorus");

        MensagemDTO mensagemDTO = new MensagemDTO();
        mensagemDTO.setAssunto(Bundle.getStringApplication("msg_erro_sincronizacao_horus"));
        mensagemDTO.setUsuarios(Arrays.asList(usuarioResponsavelAgendamento));
        StringBuilder mensagem = new StringBuilder();

        mensagem.append("Erro gerado em ").append(new SimpleDateFormat("dd/MM/yyyy HH:mm").format(new Date())).append("hs.");
        mensagem.append("\n");
        mensagem.append("\n");

        if (ex != null) {
            if (ex instanceof DAOException) { // Se o erro for proveniente de uma transação, deve registrar a mensagem obtida da exception
                mensagem.append(Bundle.getStringApplication("msg_sistema_encontrou_erro_valid_horus_erro_X", ex.getCause() != null ? ex.getCause().getMessage() : ex.getMessage()));
                mensagem.append("\n").append(Arrays.toString(ex.getStackTrace()));
            } else { // Se for qualquer outro erro, registra uma mensagem pré-definida e a stacktrace
                mensagem.append(Bundle.getStringApplication("msg_sistema_encontrou_erro_valid_horus_erro_X", ex));
            }
        } else {
            mensagem.append(inconsistencias);
        }
        mensagemDTO.setMensagem(mensagem.toString());

        try {
            BOFactory.getBO(ComunicacaoFacade.class).enviarMensagem(mensagemDTO);
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage(), e);
        } catch (ValidacaoException e) {
            Loggable.log.error(e.getMessage(), e);
        }
    }
}
