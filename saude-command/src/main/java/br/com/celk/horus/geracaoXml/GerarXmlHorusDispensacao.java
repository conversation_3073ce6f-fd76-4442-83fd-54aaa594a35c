package br.com.celk.horus.geracaoXml;

import br.com.celk.horus.dispensacao.Identificador;
import br.com.celk.horus.dispensacao.ObjectFactory;
import br.com.celk.horus.dispensacao.Root;
import br.com.celk.horus.dto.EnvioXmlHorusDTO;
import br.com.celk.horus.dto.GeracaoXmlHorusDTO;
import br.com.celk.horus.dto.QueryXmlHorusDispensacaoDTO;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class GerarXmlHorusDispensacao extends AbstractCommandTransaction {

    private final GeracaoXmlHorusDTO dto;

    public GerarXmlHorusDispensacao(GeracaoXmlHorusDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        try {
            String enderecoEnvioHorus = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("enderecoEnvioHorus");
            if (enderecoEnvioHorus == null || enderecoEnvioHorus.isEmpty()) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informar_enderecoEnvioHorus_parametro_gem"));
            }
            String usuario = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("usuarioSCPA");
            if (usuario == null || usuario.isEmpty()) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informar_usuarioSCPA_parametro_gem"));
            }
            String senha = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("senhaSCPA");
            if (senha == null || senha.isEmpty()) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informar_senhaSCPA_parametro_gem"));
            }
            /*Consultar Itens*/
            QueryXmlHorusDispensacao query = new QueryXmlHorusDispensacao(dto);
            query.start();
            List<QueryXmlHorusDispensacaoDTO> itens = query.getItens();

            if (CollectionUtils.isNotNullEmpty(itens)) {
                ObjectFactory fac = new ObjectFactory();
                Root root = fac.createRoot();
                Identificador identificador = fac.createIdentificador();
                identificador.setStEsferaEnvio(RepositoryComponentDefault.ESFERA_MUNICIPAL);
                identificador.setNoUsuario(usuario);
                identificador.setTpXML(RepositoryComponentDefault.TP_XML_DISPENSACAO_PACIENTE);
                identificador.setStHorus(RepositoryComponentDefault.NAO);
                root.setIdentificador(identificador);
//                for (QueryXmlHorusDispensacaoDTO item : itens) {
//                    if (identificador.getCoMunicipioIbge() == null && item.getMovimentoEstoque().getId().getEmpresa().getCidade() != null) {
//                        identificador.setCoMunicipioIbge(item.getMovimentoEstoque().getId().getEmpresa().getCidade().getCodigo().toString());
//                    }
//                    boolean itemInconsistente = false;
//                    if (item.getNumeroCns() == null) {
//                        String inconsistenciaCNS = "DISPENSAÇÃO: CNS do paciente não definido.";
//                        if (item.getMovimentoEstoque().getUsuarioCadsus() != null) {
//                            inconsistenciaCNS += " PACIENTE: " + item.getMovimentoEstoque().getUsuarioCadsus().getDescricaoFormatado() + ".";
//                        }
//                        if (item.getMovimentoEstoque().getId().getNumeroLancamento() != null) {
//                            inconsistenciaCNS += " MOVIMENTO: " + item.getMovimentoEstoque().getId().getNumeroLancamento();
//                        }
//                        addInconsistencia(inconsistenciaCNS);
//                        itemInconsistente = true;
//                    }
//                    if (item.getMovimentoEstoque().getId().getEmpresa().getCnes() == null) {
//                        addInconsistencia("DISPENSAÇÃO: CNES da empresa " + item.getMovimentoEstoque().getId().getEmpresa().getDescricao() + " não definido.");
//                        itemInconsistente = true;
//                    }
//                    if (item.getMovimentoEstoque().getProduto().getCatmat() == null) {
//                        addInconsistencia("DISPENSAÇÃO: Código CATMAT do produto " + item.getMovimentoEstoque().getProduto().getDescricaoFormatado()+ " não definido.");
//                        itemInconsistente = true;
//                    }
//                    if (item.getMovimentoEstoque().getProduto().getTipoProdutoCatmat() == null) {
//                        addInconsistencia("DISPENSAÇÃO: Tipo CATMAT do produto " + item.getMovimentoEstoque().getProduto().getDescricaoFormatado() + " não definido.");
//                        itemInconsistente = true;
//                    }
//                    if (item.getMovimentoEstoque().getPrecoMedio() == null) {
//                        addInconsistencia("DISPENSAÇÃO: Preço (médio) do item se encontra vazio ou nulo. PRODUTO: " + item.getMovimentoEstoque().getProduto().getDescricaoFormatado());
//                        itemInconsistente = true;
//                    }
//                    if (item.getGrupoEstoque().getDataValidade() == null) {
//                        String inconsistenciaValidade = "DISPENSAÇÃO: Data de validade vazia ou nula. PRODUTO: " + item.getMovimentoEstoque().getProduto().getDescricaoFormatado();
//                        if (item.getGrupoEstoque().getId().getGrupo() != null) {
//                            inconsistenciaValidade += " LOTE: " + item.getGrupoEstoque().getId().getGrupo();
//                        }
//                        addInconsistencia(inconsistenciaValidade);
//                        itemInconsistente = true;
//                    }
//                    if (item.getGrupoEstoque().getId().getGrupo() == null) {
//                        String inconsistenciaLote = "DISPENSAÇÃO: Lote não definido. PRODUTO: " + item.getMovimentoEstoque().getProduto().getDescricaoFormatado() + ".";
//                        if (item.getMovimentoEstoque().getId().getNumeroLancamento() != null) {
//                            inconsistenciaLote += " MOVIMENTO: " + item.getMovimentoEstoque().getId().getNumeroLancamento() + ".";
//                        }
//                        addInconsistencia(inconsistenciaLote);
//                        itemInconsistente = true;
//                    }
//                    if (item.getMovimentoEstoque().getQuantidade() == null) {
//                        String inconsistenciaLote = "DISPENSAÇÃO: Quantidade não definida. PRODUTO: " + item.getMovimentoEstoque().getProduto().getDescricaoFormatado() + ".";
//                        if (item.getMovimentoEstoque().getId().getNumeroLancamento() != null) {
//                            inconsistenciaLote += " MOVIMENTO: " + item.getMovimentoEstoque().getId().getNumeroLancamento() + ".";
//                        }
//                        addInconsistencia(inconsistenciaLote);
//                        itemInconsistente = true;
//                    }
//                    if (item.getMovimentoEstoque().getDataLancamento() == null) {
//                        String inconsistenciaLote = "DISPENSAÇÃO: Data de lançamento não definida. PRODUTO: " + item.getMovimentoEstoque().getProduto().getDescricaoFormatado() + ".";
//                        if (item.getMovimentoEstoque().getId().getNumeroLancamento() != null) {
//                            inconsistenciaLote += " MOVIMENTO: " + item.getMovimentoEstoque().getId().getNumeroLancamento() + ".";
//                        }
//                        addInconsistencia(inconsistenciaLote);
//                        itemInconsistente = true;
//                    }
//                    if (itemInconsistente) {
//                        continue;
//                    }
//                    Dispensacao dispensacao = fac.createDispensacao();
//                    dispensacao.setCoUnidadeCnes(item.getMovimentoEstoque().getId().getEmpresa().getCnes());
//                    dispensacao.setNuProduto(item.getMovimentoEstoque().getProduto().getCatmat());
//                    dispensacao.setTpProduto(item.getMovimentoEstoque().getProduto().getTipoProdutoCatmat());
//                    dispensacao.setVlItem(item.getMovimentoEstoque().getPrecoMedio());
//                    dispensacao.setDtValidade(DataUtil.converterData(item.getGrupoEstoque().getDataValidade()));
//                    dispensacao.setNuLote(item.getGrupoEstoque().getId().getGrupo());
//                    dispensacao.setQtMedicamentoDispensada(item.getMovimentoEstoque().getQuantidade().longValue());
//                    dispensacao.setDtDispensacao(DataUtil.converterData(item.getMovimentoEstoque().getDataLancamento()));
//                    dispensacao.setNuCnsPaciente(item.getNumeroCns().toString());
//                    root.getDispensacao().add(dispensacao);
//
//                    /**
//                     * Cria vinculo do movimento com o processo
//                     */
//                    SincronizacaoHorusProcesso shp = (SincronizacaoHorusProcesso) getSession().get(SincronizacaoHorusProcesso.class, dto.getCodigoSincronizacaoHorusProcesso());
//                    item.getMovimentoEstoque().setSincronizacaoHorusProcesso(shp);
//                    getSession().saveOrUpdate(item.getMovimentoEstoque());
//                }

                if (identificador.getStEsferaEnvio() == null) {
                    addInconsistencia("CABEÇALHO: Esfera de envio não definida.");
                }
                if (identificador.getNoUsuario() == null) {
                    addInconsistencia("CABEÇALHO: Nome de usuário não definido.");
                }
                if (identificador.getTpXML() == null) {
                    addInconsistencia("CABEÇALHO: Tipo de XML não definido.");
                }
                if (identificador.getStHorus() == null) {
                    addInconsistencia("CABEÇALHO: Situação Hórus não definida.");
                }
                if (identificador.getCoMunicipioIbge() == null) {
                    addInconsistencia("CABEÇALHO: Código do município não definido.");
                }

                if (CollectionUtils.isNotNullEmpty(dto.getLstInconsistencia())) {
                    SincronizacaoHorusProcesso sincronizacaoHorusProcesso = (SincronizacaoHorusProcesso) getSession().get(SincronizacaoHorusProcesso.class, dto.getCodigoSincronizacaoHorusProcesso());
                    sincronizacaoHorusProcesso.setMensagemErro(dto.getMensagemInconsistencia());
                    BOFactory.save(sincronizacaoHorusProcesso);
                }
                if (CollectionUtils.isNotNullEmpty(root.getDispensacao())) {
                    /**
                     * Gera o arquivo XML e envia para o webservice
                     */
                    File file = criarXml(root);
                    EnvioXmlHorusDTO envioXmlHorusDTO = new EnvioXmlHorusDTO();
                    envioXmlHorusDTO.setFile(file);
                    envioXmlHorusDTO.setCodigoSincronizacaoHorusProcesso(dto.getCodigoSincronizacaoHorusProcesso());

                    BOFactory.getBO(MaterialBasicoFacade.class).enviarXmlHorus(envioXmlHorusDTO);
                } else {
                    atualizaSemRegistro();
                }
            } else {
                atualizaSemRegistro();
            }

        } catch (IllegalArgumentException ex) {
            atualizarErro(ex);
        } catch (JAXBException ex) {
            atualizarErro(ex);
        } catch (IOException ex) {
            atualizarErro(ex);
//        } catch (ParseException ex) {
//            atualizarErro(ex);
//        } catch (DatatypeConfigurationException ex) {
//            atualizarErro(ex);
        } catch (DAOException ex) {
            atualizarErro(ex);
        } catch (ValidacaoException ex) {
            SincronizacaoHorusProcesso sincronizacaoHorusProcesso = LoadManager.getInstance(SincronizacaoHorusProcesso.class)
                    .setId(dto.getCodigoSincronizacaoHorusProcesso())
                    .start().getVO();
            sincronizacaoHorusProcesso.setStatus(SincronizacaoHorusProcesso.Status.ERRO.value());
            sincronizacaoHorusProcesso.setMensagemErro(ex.getMessage());
            BOFactory.newTransactionSave(sincronizacaoHorusProcesso);
            throw new ValidacaoException(ex.getMessage(), ex);
        }
    }

    private File criarXml(Root root) throws ValidacaoException, JAXBException, IOException {
        JAXBContext context;
        context = JAXBContext.newInstance("br.com.celk.horus.dispensacao");
        Marshaller marshaller = context.createMarshaller();

        File file = new File("dados.xml");
        marshaller.marshal(root, file);

        return file;
    }

    private void addInconsistencia(String str) {
        dto.getLstInconsistencia().add(str);
    }

    private void atualizarErro(Exception ex) throws DAOException, ValidacaoException {
        SincronizacaoHorusProcesso sincronizacaoHorusProcesso = LoadManager.getInstance(SincronizacaoHorusProcesso.class)
                .setId(dto.getCodigoSincronizacaoHorusProcesso())
                .startNewTransaction().getVO();
        sincronizacaoHorusProcesso.setStatus(SincronizacaoHorusProcesso.Status.ERRO.value());
        if(dto.getMensagemInconsistencia() != null) {
            sincronizacaoHorusProcesso.setMensagemErro(dto.getMensagemInconsistencia().concat(ex.getMessage()));
        } else {
            sincronizacaoHorusProcesso.setMensagemErro(ex.getMessage());
        }
        BOFactory.newTransactionSave(sincronizacaoHorusProcesso);
        throw new DAOException(ex.getMessage(), ex);
    }

    private void atualizaSemRegistro() throws DAOException, ValidacaoException {
        SincronizacaoHorusProcesso sincronizacaoHorusProcesso = (SincronizacaoHorusProcesso) getSession().get(SincronizacaoHorusProcesso.class, dto.getCodigoSincronizacaoHorusProcesso());
        sincronizacaoHorusProcesso.setStatus(SincronizacaoHorusProcesso.Status.SEM_REGISTRO.value());
        BOFactory.save(sincronizacaoHorusProcesso);
        Loggable.log.warn(Bundle.getStringApplication("msg_nao_existem_itens_para_exportar"));
    }

}
