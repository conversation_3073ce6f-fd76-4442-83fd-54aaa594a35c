package br.com.celk.horus.geracaoXml;

import br.com.celk.horus.dto.GeracaoXmlHorusDTO;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso;

/**
 *
 * <AUTHOR>
 */
public class SincronizarHorus extends AbstractCommandTransaction {

    private final GeracaoXmlHorusDTO dto;

    public SincronizarHorus(GeracaoXmlHorusDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        /**
         * salva o processo
         */
        SincronizacaoHorusProcesso sincronizacaoHorusProcesso = new SincronizacaoHorusProcesso();
        sincronizacaoHorusProcesso.setCompetencia(dto.getPeriodo().getDataInicial());
        sincronizacaoHorusProcesso.setDataGeracao(DataUtil.getDataAtual());
        sincronizacaoHorusProcesso.setStatus(SincronizacaoHorusProcesso.Status.GERANDO.value());
        sincronizacaoHorusProcesso.setTipoSincronizacao(dto.getTipoSincronizacao());
        sincronizacaoHorusProcesso.setUsuario(SessaoAplicacaoImp.getInstance().getUsuario());
        
        SincronizacaoHorusProcesso shp = BOFactory.newTransactionSave(sincronizacaoHorusProcesso);
        dto.setCodigoSincronizacaoHorusProcesso(shp.getCodigo());

        /**
         * inicia processo assíncrono
         */
        if (RepositoryComponentDefault.TP_XML_DISPENSACAO_PACIENTE.equals(dto.getTipoXml())) {
//            BOFactory.getBO(MaterialBasicoFacade.class).gerarXmlDispensacao(dto);
            BOFactory.getBO(MaterialBasicoFacade.class).gerarXmlDispensacaoNew(dto);
        } else if (RepositoryComponentDefault.TP_XML_ENTRADA.equals(dto.getTipoXml())) {
//            BOFactory.getBO(MaterialBasicoFacade.class).gerarXmlEntrada(dto);
            BOFactory.getBO(MaterialBasicoFacade.class).gerarXmlEntradaNew(dto);
        } else if (RepositoryComponentDefault.TP_XML_SAIDA.equals(dto.getTipoXml())) {
//            BOFactory.getBO(MaterialBasicoFacade.class).gerarXmlSaida(dto);
            BOFactory.getBO(MaterialBasicoFacade.class).gerarXmlSaidaNew(dto);
        } else if (RepositoryComponentDefault.TP_XML_POSICAO_ESTOQUE.equals(dto.getTipoXml())) {
            BOFactory.getBO(MaterialBasicoFacade.class).gerarXmlPosicaoEstoqueNew(dto);
        }
    }

}
