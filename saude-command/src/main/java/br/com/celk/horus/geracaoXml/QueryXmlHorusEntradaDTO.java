package br.com.celk.horus.geracaoXml;

import br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoque;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryXmlHorusEntradaDTO implements Serializable {

    private MovimentoEstoque movimentoEstoque;
    private GrupoEstoque grupoEstoque;

    public MovimentoEstoque getMovimentoEstoque() {
        return movimentoEstoque;
    }

    public void setMovimentoEstoque(MovimentoEstoque movimentoEstoque) {
        this.movimentoEstoque = movimentoEstoque;
    }

    public GrupoEstoque getGrupoEstoque() {
        return grupoEstoque;
    }

    public void setGrupoEstoque(GrupoEstoque grupoEstoque) {
        this.grupoEstoque = grupoEstoque;
    }

}
