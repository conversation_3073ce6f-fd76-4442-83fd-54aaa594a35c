package br.com.celk.bo.cadsus.exclusao;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import java.util.List;
import java.util.Map;
import org.hibernate.Query;

/**
 *
 * <AUTHOR>
 */
public class QueryUsuarioCadsusExclusaoAtendimento extends CommandQuery {

    private final List<Long> codigoProcessoList;
    private List<Atendimento> list;

    public QueryUsuarioCadsusExclusaoAtendimento(List<Long> codigoProcessoList) {
        this.codigoProcessoList = codigoProcessoList;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(Atendimento.class.getName());
        hql.addToSelect("atend.codigo", "codigo");
        hql.addToSelect("atend.dataAtendimento", "dataAtendimento");
        hql.addToSelect("atend.status", "status");
        hql.addToSelect("atend.sequencialCiclo", "sequencialCiclo");
        hql.addToSelect("atend.version", "version");
        
        hql.addToSelect("leitoQuarto.codigo", "leitoQuarto.codigo");
        
        hql.addToSelect("atendimentoPrincipal.codigo", "atendimentoPrincipal.codigo");
        hql.addToSelect("atendimentoPrincipal.dataAtendimento", "atendimentoPrincipal.dataAtendimento");
        hql.addToSelect("atendimentoPrincipal.status", "atendimentoPrincipal.status");
        hql.addToSelect("atendimentoPrincipal.sequencialCiclo", "atendimentoPrincipal.sequencialCiclo");
        hql.addToSelect("atendimentoPrincipal.version", "atendimentoPrincipal.version");
        
        hql.addToSelect("leitoQuartoPrin", "atendimentoPrincipal.leitoQuarto.codigo");

        hql.addToSelect("profissional.codigo", "profissional.codigo");
        hql.addToSelect("profissional.nome", "profissional.nome");

        hql.addToSelect("usuarioCadsus.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.recemNascido", "usuarioCadsus.recemNascido");

        hql.addToSelect("tipoAtendimento.codigo", "naturezaProcuraTipoAtendimento.tipoAtendimento.codigo");
        hql.addToSelect("tipoAtendimento.descricao", "naturezaProcuraTipoAtendimento.tipoAtendimento.descricao");
        hql.addToSelect("tipoAtendimento.flagCancelaPrimeiroAtendimento", "naturezaProcuraTipoAtendimento.tipoAtendimento.flagCancelaPrimeiroAtendimento");

        hql.addToSelect("naturezaProcura.codigo", "naturezaProcuraTipoAtendimento.naturezaProcura.codigo");
        
        hql.addToSelect("empresa.codigo", "empresa.codigo");
        hql.addToSelect("empresa.referencia", "empresa.referencia");
        hql.addToSelect("empresa.descricao", "empresa.descricao");

        hql.addToFrom("Atendimento atend "
                + " left join atend.atendimentoPrincipal atendimentoPrincipal"
                + " left join atendimentoPrincipal.leitoQuarto leitoQuartoPrin"
                + " left join atend.profissional profissional"
                + " left join atend.usuarioCadsus usuarioCadsus"
                + " left join atend.empresa empresa"
                + " left join atend.leitoQuarto leitoQuarto"
                + " left join atend.naturezaProcuraTipoAtendimento naturezaProcuraTipoAtendimento"
                + " left join naturezaProcuraTipoAtendimento.tipoAtendimento tipoAtendimento"
                + " left join naturezaProcuraTipoAtendimento.naturezaProcura naturezaProcura"
        );
        
        hql.addToWhereWhithAnd("atend.codigo in :codigoProcessoList");
        hql.addToOrder("atend.codigo asc");
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setParameterList("codigoProcessoList", codigoProcessoList);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<Atendimento> getResult() {
        return this.list;
    }

}
