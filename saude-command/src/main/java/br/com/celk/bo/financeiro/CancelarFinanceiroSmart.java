package br.com.celk.bo.financeiro;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.financeiro.IntegracaoFinanceiroSmartHelper;
import br.com.ksisolucoes.util.financeiro.smartCariacica.IntegracaoSmartAuthResultDTO;
import br.com.ksisolucoes.util.financeiro.smartCariacica.IntegracaoSmartCancelarPrecoPublicoDTO;
import br.com.ksisolucoes.util.financeiro.smartCariacica.IntegracaoSmartResultDTO;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;
import org.apache.commons.lang3.StringUtils;

import javax.ws.rs.core.Response;

public class CancelarFinanceiroSmart extends AbstractCommandTransaction {

    private VigilanciaFinanceiro vigilanciaFinanceiro;
    private IntegracaoSmartResultDTO result;

    public CancelarFinanceiroSmart(VigilanciaFinanceiro vigilanciaFinanceiro) {
        this.vigilanciaFinanceiro = vigilanciaFinanceiro;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (IntegracaoFinanceiroSmartHelper.isIntegrarFinanceiroSmart()) {
            IntegracaoSmartAuthResultDTO authResultDTO = IntegracaoFinanceiroSmartHelper.autenticarSmart();

            if (Response.Status.OK.getStatusCode() == authResultDTO.getStatusCode() &&
                    authResultDTO.getOkDTO() != null &&
                    StringUtils.isNotEmpty(authResultDTO.getOkDTO().getAccessToken())
            ) {
                IntegracaoSmartCancelarPrecoPublicoDTO dto = IntegracaoFinanceiroSmartHelper.criarDtoCancelamentoSmart(vigilanciaFinanceiro);

                IntegracaoSmartResultDTO resultDTO = IntegracaoFinanceiroSmartHelper.cancelarFinanceiroSmart(dto, authResultDTO.getOkDTO().getAccessToken());

                if (Response.Status.OK.getStatusCode() == resultDTO.getStatusCode()) {
                    throw new ValidacaoException("Cancelei!");
                }
            }
        }
    }

    public IntegracaoSmartResultDTO getResult() {
        return result;
    }

    public void setResult(IntegracaoSmartResultDTO result) {
        this.result = result;
    }
}
