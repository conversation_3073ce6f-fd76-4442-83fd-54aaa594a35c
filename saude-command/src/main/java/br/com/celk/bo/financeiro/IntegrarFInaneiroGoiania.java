package br.com.celk.bo.financeiro;

import br.com.celk.boleto.dto.boletocloud.request.DadosGeracaoBoletoDTO;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.financeiro.IntegracaoFinanceiroGoianiaHelper;
import br.com.ksisolucoes.util.financeiro.integracaogoiania.IntegracaoFinanceiraDadosEnvioDTO;
import br.com.ksisolucoes.util.financeiro.integracaogoiania.IntegracaoFinanceiroSolicitacaoRespostaDTO;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.financeiro.IntegracaoFinanceiroGoiania;
import br.com.ksisolucoes.vo.vigilancia.financeiro.IntegracaoFinanceiroGoianiaOcorrencia;
import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;

public class IntegrarFInaneiroGoiania extends AbstractCommandTransaction {

    private DadosGeracaoBoletoDTO dados;
    private VigilanciaFinanceiro vigilanciaFinanceiro;
    private IntegracaoFinanceiroSolicitacaoRespostaDTO result;

    public IntegrarFInaneiroGoiania(DadosGeracaoBoletoDTO dados, VigilanciaFinanceiro vigilanciaFinanceiro) {
        this.dados = dados;
        this.vigilanciaFinanceiro = vigilanciaFinanceiro;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (IntegracaoFinanceiroGoianiaHelper.isIntegrarFinanceiroGoiania()) {
            try {
                IntegracaoFinanceiroSolicitacaoRespostaDTO dto = IntegracaoFinanceiroGoianiaHelper.sendFinanceiro(dados, vigilanciaFinanceiro);
                setResult(dto);
                IntegracaoFinanceiroGoiania integracao = saveIntegracao(dto);
                saveOcorrencia(dto,integracao);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private  void saveOcorrencia(IntegracaoFinanceiroSolicitacaoRespostaDTO dto,IntegracaoFinanceiroGoiania financeiroGoiania) throws Exception {
        IntegracaoFinanceiroGoianiaOcorrencia ocorrencia = new IntegracaoFinanceiroGoianiaOcorrencia();
        if(dto.getDadosResposta() != null && dto.getDadosEnvio() != null) {
            ocorrencia.setIdDebito(dto.getDadosResposta().getIdDebito());
            ocorrencia.setDebitoStatus(dto.getDadosResposta().getDebitoStatus());
            ocorrencia.setXmlEnviado(IntegracaoFinanceiroGoianiaHelper.convertObjectToXML(dto.getDadosEnvio(), IntegracaoFinanceiraDadosEnvioDTO.class));
        }

        ocorrencia.setMensagem(dto.getMensagem().getTexto());
        ocorrencia.setVigilanciaFinanceiro(vigilanciaFinanceiro);
        ocorrencia.setIntegracaoFinanceiroGoiania(financeiroGoiania);
        ocorrencia.setUsuarioCadastro(getSessao().getUsuario());
        ocorrencia.setDataCadastro(DataUtil.getDataAtual());
        ocorrencia.setXmlRecebido(IntegracaoFinanceiroGoianiaHelper.convertObjectToXML(dto, IntegracaoFinanceiroSolicitacaoRespostaDTO.class));

        BOFactory.save(ocorrencia);

    }

    private IntegracaoFinanceiroGoiania saveIntegracao(IntegracaoFinanceiroSolicitacaoRespostaDTO dto) throws DAOException, ValidacaoException {
        IntegracaoFinanceiroGoiania integracao = IntegracaoFinanceiroGoianiaHelper.getIntegracaoFinanceiroGoiania(vigilanciaFinanceiro);
        if (integracao == null) {
            integracao = new IntegracaoFinanceiroGoiania();
            integracao.setVigilanciaFinanceiro(vigilanciaFinanceiro);
            integracao.setDataCadastro(DataUtil.getDataAtual());
            integracao.setUsuarioCadastro(getSessao().getUsuario());
        }
        if(dto.getDadosResposta() != null && dto.getDadosEnvio() != null) {
            if (dto.getDadosResposta().getIdDebito().equals(0L)) {
                integracao.setIdDebito(dto.getDadosResposta().getIdDebito());
            }
            integracao.setDebitoStatus(dto.getDadosResposta().getDebitoStatus());
        }

        integracao.setDataAtualizacao(DataUtil.getDataAtual());

        integracao = BOFactory.save(integracao);

        if(dto.getDadosResposta() != null) {
            if(IntegracaoFinanceiroGoianiaOcorrencia.Status.PAGO.value().equals(dto.getDadosResposta().getDebitoStatus())) {
                BOFactory.getBO(VigilanciaFacade.class).efetuarPagamentoRequerimentoVigilancia(getRequerimentoVigilancia(), vigilanciaFinanceiro, RequerimentoVigilancia.SituacaoAprovacao.COMPROVANTE_PAGAMENTO, true);
            }
        }
        return integracao;
    }


    public IntegracaoFinanceiroSolicitacaoRespostaDTO getResult() {
        return result;
    }

    public void setResult(IntegracaoFinanceiroSolicitacaoRespostaDTO result) {
        this.result = result;
    }

    private RequerimentoVigilancia getRequerimentoVigilancia() {
        return LoadManager.getInstance(RequerimentoVigilancia.class)
                .addProperties(new HQLProperties(RequerimentoVigilancia.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVigilancia.PROP_CODIGO, vigilanciaFinanceiro.getRequerimentoVigilancia().getCodigo()))
                .start().getVO();
    }

}
