package br.com.celk.bo.appcidadao.agenda;

import br.com.celk.appcidadao.dto.agenda.ProcedimentoDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.TipoAtendimentoAgenda;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.hibernate.type.LongType;
import org.hibernate.type.StringType;

import java.util.List;

/**
 * <AUTHOR>
 */
public class QueryConsultaProcedimentoAgendaExternaDisponivel extends AbstractCommandTransaction<QueryConsultaProcedimentoAgendaExternaDisponivel> {

    private List<ProcedimentoDTO> resultado;
    private Long codigoUnidade;

    public QueryConsultaProcedimentoAgendaExternaDisponivel(Long codigoUnidade) {
        this.codigoUnidade = codigoUnidade;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        HQLHelper hql = new HQLHelper();
        createSQLQuery(hql);
        SQLQuery query = getSessionLeitura().createSQLQuery(hql.getQuery());
        setParameters(hql, query);
        this.addScalar(query);
        resultado = query.list();
    }

    protected void createSQLQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.addToSelectAndGroup("tipo_procedimento.cd_tp_procedimento", "codigo");
        hql.addToSelectAndGroup("tipo_procedimento.ds_tp_procedimento", "descricao");
        hql.addToSelectAndGroup("empresa.empresa", "careUnitId");
        hql.addToSelectAndGroup("empresa.descricao", "careUnitName");
        hql.addToSelectAndGroup("empresa.telefone", "careUnitPhone");
        hql.addToSelectAndGroup("empresa.descricao", "careUnitDescription");
        hql.addToSelectAndGroup("empresa.rua", "careUnitStreet");
        hql.addToSelectAndGroup("empresa.bairro", "careUnitNeighborhood");
        hql.addToSelectAndGroup("empresa.numero", "careUnitNumber");
        hql.addToSelectAndGroup("empresa.complemento", "careUnitComplement");

        hql.setTypeSelect(ProcedimentoDTO.class.getName());
        hql.setUseSQL(true);

        hql.addToFrom("agenda_grade_horario" +
                " left join agenda_grade_atendimento " +
                " on agenda_grade_horario.cd_ag_gra_atendimento = agenda_grade_atendimento.cd_ag_gra_atendimento" +
                " left join tipo_atendimento_agenda on" +
                " tipo_atendimento_agenda.cd_tipo = agenda_grade_atendimento.cd_tipo" +
                " left join agenda_grade on" +
                " agenda_grade_atendimento.cd_ag_grade = agenda_grade.cd_ag_grade" +
                " left join agenda on" +
                " agenda_grade.cd_agenda = agenda.cd_agenda" +
                " left join tipo_procedimento on" +
                " agenda.cd_tp_procedimento = tipo_procedimento.cd_tp_procedimento" +
                " left join empresa on" +
                " agenda.empresa = empresa.empresa");

        if (codigoUnidade != null)
            hql.addToWhereWhithAnd("empresa.empresa = :codigoUnidade");
        hql.addToWhereWhithAnd("tipo_atendimento_agenda.tp_atendimento = :tipoAtendimento");
        hql.addToWhereWhithAnd("tipo_procedimento.flag_visualiza_acolhimento_externo = 1 ");
        hql.addToWhereWhithAnd("(agenda_grade_horario.hora - NOW()) <= agenda.visibilidade_agenda * interval '1 week'");
        hql.addToWhereWhithAnd("agenda_grade_horario.hora >= NOW()");
    }

    protected void setParameters(HQLHelper hql, SQLQuery query) throws ValidacaoException, DAOException {
        query.setParameter("tipoAtendimento", TipoAtendimentoAgenda.TipoAtendimento.TIPO_CONSULTA.value());
        if (codigoUnidade != null)
            query.setParameter("codigoUnidade", codigoUnidade);
    }

    private void addScalar(SQLQuery sql) {
        sql.addScalar("codigo", LongType.INSTANCE)
                .addScalar("descricao", StringType.INSTANCE)
                .addScalar("careUnitId", LongType.INSTANCE)
                .addScalar("careUnitName", StringType.INSTANCE)
                .addScalar("careUnitPhone", StringType.INSTANCE)
                .addScalar("careUnitDescription", StringType.INSTANCE)
                .addScalar("careUnitStreet", StringType.INSTANCE)
                .addScalar("careUnitNeighborhood", StringType.INSTANCE)
                .addScalar("careUnitNumber", StringType.INSTANCE)
                .addScalar("careUnitComplement", StringType.INSTANCE)
                .setResultTransformer(Transformers.aliasToBean(ProcedimentoDTO.class));
    }

    public List<ProcedimentoDTO> getResultado() {
        return resultado;
    }

}

