package br.com.celk.bo.cadsus.exclusao;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento; 
import java.util.List;
import java.util.Map;
import org.hibernate.Query;

/**
 *
 * <AUTHOR>
 */
public class QueryUsuarioCadsusExclusaoEncaminhamento extends CommandQuery {

    private final List<Long> codigoProcessoList;
    private List<Encaminhamento> list;

    public QueryUsuarioCadsusExclusaoEncaminhamento(List<Long> codigoProcessoList) {
        this.codigoProcessoList = codigoProcessoList;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(Encaminhamento.class.getName());
        
        hql.addToSelect("e.codigo", "codigo");
        hql.addToSelect("e.status", "status");
        hql.addToSelect("e.version", "version");
        hql.addToSelect("e.dataInicioAgendamento", "dataInicioAgendamento");
        
        hql.addToSelect("te.codigo", "tipoEncaminhamento.codigo");
        hql.addToSelect("te.descricao", "tipoEncaminhamento.descricao");
        
        hql.addToSelect("uc.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("uc.nome", "usuarioCadsus.nome");

        hql.addToFrom("Encaminhamento e "
                + " left join e.tipoEncaminhamento te"
                + " left join e.usuarioCadsus uc"
        );

        hql.addToWhereWhithAnd("e.codigo in :codigoProcessoList");
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setParameterList("codigoProcessoList", codigoProcessoList);        
    }
    
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<Encaminhamento> getResult() {
        return this.list;
    }
}