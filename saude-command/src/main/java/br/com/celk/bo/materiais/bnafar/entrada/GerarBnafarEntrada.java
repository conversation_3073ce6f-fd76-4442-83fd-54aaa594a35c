package br.com.celk.bo.materiais.bnafar.entrada;

import br.com.celk.materiais.bnafar.dto.GeracaoBnafarDTO;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.agendagradeatendimentohorario.CancelarAgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.vo.materiais.bnafar.interfaces.BnafarHelper;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import br.com.ksisolucoes.vo.materiais.bnafar.entrada.BnafarEntrada;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by sulivan on 04/01/18.
 */
public class GerarBnafarEntrada extends AbstractCommandTransaction {

    private final GeracaoBnafarDTO dto;
    private GrupoEstoque grupoEstoque;

    private BnafarEntrada bnafarEntrada;
    private Empresa empresa = null;

    public GerarBnafarEntrada(GeracaoBnafarDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        bnafarEntrada = new BnafarEntrada();

        validarRegistro(dto.getMovimentoEstoque());

        bnafarEntrada.setDataEntrada(dto.getMovimentoEstoque().getDataLancamento());
        bnafarEntrada.setTipoEntrada(dto.getMovimentoEstoque().getTipoDocumento().getFlagTipoMovimentacaoHorus());
        bnafarEntrada.setProdutoOrigem(dto.getMovimentoEstoque().getProduto());
        bnafarEntrada.setGrupoEstoque(dto.getMovimentoEstoque().getGrupoEstoque());
        bnafarEntrada.setNumeroLancamentoEstoque(dto.getMovimentoEstoque().getId().getNumeroLancamento());
        bnafarEntrada.setQuantidade(dto.getMovimentoEstoque().getQuantidade());
        bnafarEntrada.setValorUnitario(new Dinheiro(br.com.celk.util.Coalesce.asDouble(dto.getMovimentoEstoque().getPrecoUnitario()), 4).doubleValue());
        bnafarEntrada.setDataValidade(grupoEstoque.getDataValidade());
        bnafarEntrada.setFabricante(grupoEstoque.getFabricante());
        bnafarEntrada.setStatusRegistro(BnafarHelper.StatusRegistro.GERADO.value());
        bnafarEntrada.setDataCadastro(DataUtil.getDataAtual());
        bnafarEntrada.setUsuario(getSessao().getUsuario());

        BOFactory.save(bnafarEntrada);
    }

    private void validarRegistro(MovimentoEstoque movimentoEstoque) throws ValidacaoException {
        Long paramQueryEmpresa = movimentoEstoque.getId() != null ? movimentoEstoque.getId().getEmpresa().getCodigo() : getSessao().getCodigoEmpresa();

        if (paramQueryEmpresa != null) {
            empresa = LoadManager.getInstance(Empresa.class)
                    .addProperties(new HQLProperties(Empresa.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_CODIGO, paramQueryEmpresa))
                    .start().getVO();
        }

        if (empresa == null) {
            addInconsistencia("BNAFAR ENTRADAS: Estabelecimento não definido no registro da movimentação ");
            throw new ValidacaoException(dto.getMensagemInconsistencia());
        }

        bnafarEntrada.setEmpresa(empresa);

        if (empresa.getCnes() == null || empresa.getCnes().isEmpty()) {
            addInconsistencia("BNAFAR ENTRADAS: CNES da empresa " + empresa.getDescricao() + " não definido.");
        } else {
            bnafarEntrada.setCnes(empresa.getCnes());
        }

        if (BnafarHelper.TIPO_ESTABELECIMENTO_ALMOXARIFADO.contains(empresa.getTipoUnidade())) {
            bnafarEntrada.setTipoEstabelecimento(RepositoryComponentDefault.COD_TIPO_ESTABELECIMENTO_ALMOXARIFADO_CENTRAL);
        } else {
            bnafarEntrada.setTipoEstabelecimento(RepositoryComponentDefault.COD_TIPO_ESTABELECIMENTO_FARMACIA_UNIDADE_SAUDE);
        }

        EstoqueEmpresaPK estoqueEmpresaPK = new EstoqueEmpresaPK();
        estoqueEmpresaPK.setEmpresa(empresa);
        estoqueEmpresaPK.setProduto(movimentoEstoque.getProduto());

        GrupoEstoquePK grupoEstoquePK = new GrupoEstoquePK();
        grupoEstoquePK.setGrupo(movimentoEstoque.getGrupoEstoque());
        grupoEstoquePK.setCodigoDeposito(movimentoEstoque.getDeposito().getCodigo());
        grupoEstoquePK.setEstoqueEmpresa(new EstoqueEmpresa(estoqueEmpresaPK));
        grupoEstoquePK.setLocalizacaoEstrutura(movimentoEstoque.getLocalizacaoEstrutura());

        grupoEstoque = LoadManager.getInstance(GrupoEstoque.class)
                .addProperties(new HQLProperties(GrupoEstoque.class).getProperties())
                .addProperties(new HQLProperties(EstoqueEmpresa.class, VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_ESTOQUE_EMPRESA)).getProperties())
                .setId(grupoEstoquePK)
                .start().getVO();

        if (grupoEstoque == null) {
            addInconsistencia("BNAFAR ENTRADAS: Grupo estoque não encontrado");
        }

        if (movimentoEstoque.getProduto().getMedicamentoCatmat() == null ||
                movimentoEstoque.getProduto().getMedicamentoCatmat().getCatmat() == null) {
            addInconsistencia("BNAFAR ENTRADAS: Código CATMAT do produto ");
        }
        if (movimentoEstoque.getProduto().getTipoProdutoCatmat() == null) {
            addInconsistencia("BNAFAR ENTRADAS: Tipo do CATMAT do produto ");
        }
        if (grupoEstoque.getId().getGrupo() == null) {
            addInconsistencia("BNAFAR ENTRADAS: Lote não definido. Produto: ");
        }
        if (grupoEstoque.getDataValidade() == null) {
            addInconsistencia("BNAFAR ENTRADAS: Data de validade vazia ou nula. ");
        }
        if (movimentoEstoque.getQuantidade() == null) {
            addInconsistencia("BNAFAR ENTRADAS: Quantidade não definida.");
        }
        if (movimentoEstoque.getDataLancamento() == null) {
            addInconsistencia("BNAFAR ENTRADAS: Data de lançamento não definida.");
        }

        String numeroDoc = movimentoEstoque.getNumeroDocumento();
        if (numeroDoc == null) {
            if (movimentoEstoque.getId().getNumeroLancamento() != null) {
                numeroDoc = movimentoEstoque.getId().getNumeroLancamento().toString();
            }
        }
        if (numeroDoc == null) {
            addInconsistencia("BNAFAR ENTRADAS: O número do documento fiscal não foi definido para o documento.");
        }

        String cnesCnpj = null;

        //Avaliar a situação da Transferencia do Almoxarifado para as Unidades
        //  Envia o CNPJ do fornecedor da nota ou CNPJ do Almoxarifado, sendo que é o da prefeitura.
        if (movimentoEstoque.getPessoa() != null &&
                Pessoa.PESSOA_JURIDICA.equals(movimentoEstoque.getPessoa().getFlagPessoaFisicaJuridica())) {

            //Quando for entrada por nota e transferência, vai setar o fornecedor na movimentação
            if(!Coalesce.asString(movimentoEstoque.getPessoa().getCnpjCpf()).isEmpty()) {
                cnesCnpj = dto.getMovimentoEstoque().getPessoa().getCnpjCpf();
                bnafarEntrada.setPessoa(dto.getMovimentoEstoque().getPessoa());
            } else {
                String inconsistencia = "BNAFAR ENTRADAS: O CNPJ do fornecedor " + movimentoEstoque.getPessoa().getDescricao() +
                        " não foi definido.";
                addInconsistencia(inconsistencia);
            }
        } else if (isTipoPedidoTransferenciaEntrada(movimentoEstoque) || isTipoTransferenciaEntrada(movimentoEstoque)) {
            //Quando transferência é setado na empresa destino a empresa origem da transferência.
            Empresa empresaOrigem = movimentoEstoque.getEmpresaDestino();

            if (empresaOrigem == null ) {
                String inconsistencia = "BNAFAR ENTRADAS: Não foi definido o estabelecimento de origem para a transferência" +
                        " Estabelecimento Destino: " + empresa.getDescricao();
                addInconsistencia(inconsistencia);
            } else {
                if (Coalesce.asString(empresaOrigem.getCnes()).isEmpty()) {
                    empresaOrigem  = LoadManager.getInstance(Empresa.class)
                            .addProperties(Empresa.PROP_CODIGO)
                            .addProperties(Empresa.PROP_DESCRICAO)
                            .addProperties(Empresa.PROP_CNPJ)
                            .addProperties(Empresa.PROP_FLAG_FISICA_JURIDICA)
                            .addProperties(Empresa.PROP_CNES)
                            .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_CODIGO, empresaOrigem.getCodigo()))
                            .start().getVO();
                }

                if (empresaOrigem != null) {
                    bnafarEntrada.setEmpresaDistribuidor(empresaOrigem);
                }

                 if (!Coalesce.asString(empresaOrigem.getCnes()).isEmpty()) {
                    cnesCnpj = empresaOrigem.getCnes();
                } else if (Pessoa.PESSOA_JURIDICA.equals(empresaOrigem.getFlagFisicaJuridica()) &&
                        !Coalesce.asString(empresaOrigem.getCnpj()).isEmpty()) {
                    cnesCnpj = empresaOrigem.getCnpj();
                } else {
                    String inconsistencia = "BNAFAR ENTRADAS: O CNES ou CNPJ do estabelecimento " +
                            empresaOrigem.getDescricao() + " não foi definido";
                    addInconsistencia(inconsistencia);
                }
            }
        } else {
            //Caso seja uma entrada diferente de NF e Transferência, vai setar o CNES do Estabelecimento da movimentação
            //Avaliar os casos de doações, empréstimo, NF emitida por pessoa fisica.
            cnesCnpj = empresa.getCnes();
            bnafarEntrada.setEmpresaDistribuidor(empresa);
        }

        // Código do registro do item na base do consumidor.
        bnafarEntrada.setOrigem(movimentoEstoque.getId().getNumeroLancamento());

        bnafarEntrada.setCnesCnpjDistribuidor(cnesCnpj);
        bnafarEntrada.setNumeroDocumento(numeroDoc);

        if (grupoEstoque.getFabricante() == null) {
            if (grupoEstoque.getId().getGrupo() != null) {
                GrupoEstoque grupoEstoqueFabricante = LoadManager.getInstance(GrupoEstoque.class)
                        .addProperty(VOUtils.montarPath(GrupoEstoque.PROP_FABRICANTE, Fabricante.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(GrupoEstoque.PROP_FABRICANTE, Fabricante.PROP_DESCRICAO))
                        .addProperty(VOUtils.montarPath(GrupoEstoque.PROP_FABRICANTE, Fabricante.PROP_CNPJ))
                        .addProperty(VOUtils.montarPath(GrupoEstoque.PROP_FABRICANTE, Fabricante.PROP_FLAG_INTERNACIONAL))
                        .addParameter(new QueryCustom.QueryCustomParameter(GrupoEstoque.PROP_FABRICANTE, BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_GRUPO), grupoEstoque.getId().getGrupo()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_ESTOQUE_EMPRESA, EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO), movimentoEstoque.getProduto()))
                        .setMaxResults(1)
                        .start().getVO();

                if (grupoEstoqueFabricante != null) {
                    grupoEstoque.setFabricante(grupoEstoqueFabricante.getFabricante());
                }
            }
        }

        if (movimentoEstoque.getTipoDocumento().getFlagTipoMovimentacaoHorus() == null) {
            addInconsistencia("BNAFAR ENTRADAS: O tipo de movimentação Hórus não está definido para o tipo de documento " +
                    movimentoEstoque.getTipoDocumento().getDescricao());
        }

        //Avalia se possui inconsistências
        if (CollectionUtils.isNotNullEmpty(dto.getLstInconsistencia())) {
            throw new ValidacaoException(dto.getMensagemInconsistencia());
        }
    }

    private boolean isTipoPedidoTransferenciaEntrada(MovimentoEstoque movimentoEstoque) {
        try {
            TipoDocumento tipoDocumento = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("tipoDocumentoPedidoTransferenciaEntrada");

            if (tipoDocumento != null &&
                    tipoDocumento.getCodigo().equals(movimentoEstoque.getTipoDocumento().getCodigo())) {
                return true;
            }
        } catch (DAOException e) {
            Logger.getLogger(CancelarAgendaGradeAtendimentoHorario.class.getName()).log(Level.WARNING, null, e);
        }

        return false;
    }

    private boolean isTipoTransferenciaEntrada(MovimentoEstoque movimentoEstoque) {
        try {
            TipoDocumento tipoDocumento = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("tipoDocumentoTransferenciaEntrada");

            if (tipoDocumento != null &&
                    tipoDocumento.getCodigo().equals(movimentoEstoque.getTipoDocumento().getCodigo())) {
                return true;
            }
        } catch (DAOException e) {
            Logger.getLogger(CancelarAgendaGradeAtendimentoHorario.class.getName()).log(Level.WARNING, null, e);
        }

        return false;
    }

    private void addInconsistencia(String str) {
        MovimentoEstoque movimentoEstoque = dto.getMovimentoEstoque();

        //Valida caso não tenha carregado a empresa.
        if (empresa != null) {
            str += "\nEstablecimento: " + empresa.getDescricao() +
                    "\nProduto: " + movimentoEstoque.getProduto().getDescricaoFormatado() +
                    "\nLote:  " + movimentoEstoque.getGrupoEstoque();
        }

        if (!dto.getLstInconsistencia().contains(str)) {
            dto.getLstInconsistencia().add(str);
        }
    }


}