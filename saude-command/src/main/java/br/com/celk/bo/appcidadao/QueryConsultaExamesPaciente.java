package br.com.celk.bo.appcidadao;

import br.com.celk.appcidadao.dto.exame.ExameDTO;
import br.com.celk.appcidadao.dto.exame.enums.StatusExame;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.hibernate.type.BooleanType;
import org.hibernate.type.DateType;
import org.hibernate.type.LongType;
import org.hibernate.type.StringType;

import java.util.List;

public class QueryConsultaExamesPaciente extends AbstractCommandTransaction {

    private Long codigoPaciente;
    private List<ExameDTO> exameDTOS;

    public QueryConsultaExamesPaciente(Long codigoUsuarioCadsus) {
        this.codigoPaciente = codigoUsuarioCadsus;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        HQLHelper hql = this.getHQL();
        SQLQuery query = getSession().createSQLQuery(hql.getQuery());
        addParameters(query);
        this.addScalar(query);
        exameDTOS = (List<ExameDTO>) query.list();
    }

    private void addParameters(SQLQuery query) {
        query.setLong("codigoPaciente", codigoPaciente);
        query.setLong("statusConcluido", StatusExame.CONCLUIDO.getValor());
    }

    private HQLHelper getHQL() {
        HQLHelper hql = new HQLHelper();
        hql.setUseSQL(true);
        hql.setDistinct(true);//caso exista mais de um pdf, retorna apenas um registro de exame
        hql
                .addToSelect("exame_procedimento.ds_procedimento", "descricao")
                .addToSelect("exame_requisicao.dt_resultado", "dataResultadoFormatoData")
                .addToSelectAndOrder("exame.dt_cadastro", "dataSolicitacaoFormatoData")
                .addToSelect("exame_requisicao.status", "statusFormatoLong")
                .addToSelect("exame_requisicao.ds_resultado", "resultado")
                .addToSelect("exame_requisicao.cd_exame_requisicao", "idExameRequisicao")
                .addToSelect("case when resultado_exame_requisicao_rtf.cd_res_ex_req_rtf notnull then true else false end", "possuiPdf");

        hql.addToFrom("exame_requisicao " +
                "left join " +
                "exame " +
                "on " +
                "exame.cd_exame = exame_requisicao.cd_exame " +
                "left join " +
                "exame_procedimento " +
                "on " +
                "exame_requisicao.cd_exame_procedimento = exame_procedimento.cd_exame_procedimento " +
                "left join resultado_exame_requisicao_rtf on " +
                "exame_requisicao.cd_exame_requisicao = resultado_exame_requisicao_rtf.cd_exame_requisicao " +
                "and " +
                "resultado_exame_requisicao_rtf.rtf like '%pdf' "
        );
        hql.addToWhereWhithAnd("exame.cd_usu_cadsus = :codigoPaciente ");
        hql.addToWhereWhithAnd("exame_requisicao.status = :statusConcluido ");


        return hql;
    }

    private void addScalar(SQLQuery sql) {
        sql
                .addScalar("idExameRequisicao", LongType.INSTANCE)
                .addScalar("descricao", StringType.INSTANCE)
                .addScalar("dataResultadoFormatoData", DateType.INSTANCE)
                .addScalar("dataSolicitacaoFormatoData", DateType.INSTANCE)
                .addScalar("statusFormatoLong", LongType.INSTANCE)
                .addScalar("resultado", StringType.INSTANCE)
                .addScalar("possuiPdf", BooleanType.INSTANCE)
                .setResultTransformer(Transformers.aliasToBean(ExameDTO.class));
    }

    public List<ExameDTO> getExamesDTOS() {
        return exameDTOS;
    }
}