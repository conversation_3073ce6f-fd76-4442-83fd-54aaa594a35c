package br.com.celk.bo.atendimento.prontuario;

import br.com.celk.atendimento.prontuario.interfaces.dto.AtendimentoProntuarioDTO;
import br.com.celk.atendimento.prontuario.interfaces.dto.ConsultaProntuariosDemmandPaggingDTOParam;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoProntuario;
import org.hibernate.Query;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ConsultaProntuariosDemmandPaggingExterno extends AbstractCommandTransaction {

    private ConsultaProntuariosDemmandPaggingDTOParam param;
    private List<AtendimentoProntuarioDTO> prontuarios;

    public ConsultaProntuariosDemmandPaggingExterno(ConsultaProntuariosDemmandPaggingDTOParam param) {
        this.param = param;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        QueryConsultaProntuariosDemmandPaggingExternos queryConsultaProntuariosDemmandPaggingExternos = new QueryConsultaProntuariosDemmandPaggingExternos();
        queryConsultaProntuariosDemmandPaggingExternos.start();
        prontuarios = queryConsultaProntuariosDemmandPaggingExternos.getResult();
    }

    public List<AtendimentoProntuarioDTO> getProntuarios() {
        return prontuarios;
    }

    private class QueryConsultaProntuariosDemmandPaggingExternos extends CommandQuery<QueryConsultaProntuariosDemmandPaggingExternos> {

        private List<AtendimentoProntuarioDTO> result;
        private List<Long> codigosAtendimentos;

        public QueryConsultaProntuariosDemmandPaggingExternos() {
        }

        @Override
        protected void createQuery(HQLHelper hql) {
            hql.addToSelect(new HQLProperties(AtendimentoProntuario.class, "ap").getSingleProperties());

            hql.addToSelect("ap.codigo", "codigo");

            hql.addToSelect("aPrinc.codigo", "atendimento.atendimentoPrincipal.codigo");
            hql.addToSelect("aPrinc.dataAtendimento", "atendimento.atendimentoPrincipal.dataAtendimento");

            hql.addToSelect("prof.codigo", "profissional.codigo");
            hql.addToSelect("prof.nome", "profissional.nome");
            hql.addToSelect("prof.unidadeFederacaoConselhoRegistro", "profissional.unidadeFederacaoConselhoRegistro");
            hql.addToSelect("prof.numeroRegistro", "profissional.numeroRegistro");

            hql.addToSelect("cbo.cbo", "tabelaCbo.cbo");
            hql.addToSelect("cbo.descricao", "tabelaCbo.descricao");

            hql.addToSelect("conselhoClasse.codigo", "profissional.conselhoClasse.codigo");
            hql.addToSelect("conselhoClasse.descricao", "profissional.conselhoClasse.descricao");
            hql.addToSelect("conselhoClasse.sigla", "profissional.conselhoClasse.sigla");

            hql.addToSelect("emp.codigo", "empresa.codigo");
            hql.addToSelect("emp.descricao", "empresa.descricao");

            hql.addToSelect("npta.codigo", "atendimento.atendimentoPrincipal.naturezaProcuraTipoAtendimento.codigo");
            hql.addToSelect("ta.codigo", "atendimento.atendimentoPrincipal.naturezaProcuraTipoAtendimento.tipoAtendimento.codigo");
            hql.addToSelect("ta.descricao", "atendimento.atendimentoPrincipal.naturezaProcuraTipoAtendimento.tipoAtendimento.descricao");

            hql.setTypeSelect(AtendimentoProntuarioDTO.class.getName());

            StringBuilder sbFrom = new StringBuilder();
            sbFrom.append("AtendimentoProntuario ap ");
            sbFrom.append(" left join ap.profissional prof ");
            sbFrom.append(" left join ap.tabelaCbo cbo ");
            sbFrom.append(" left join ap.usuarioCadsus uc ");
            sbFrom.append(" left join prof.conselhoClasse conselhoClasse ");
            sbFrom.append(" left join ap.atendimento a ");
            sbFrom.append(" left join a.atendimentoPrincipal aPrinc ");
            sbFrom.append(" left join ap.empresa emp ");
            sbFrom.append(" left join aPrinc.naturezaProcuraTipoAtendimento npta ");
            sbFrom.append(" left join npta.tipoAtendimento ta ");

            hql.addToFrom(sbFrom.toString());

            hql.addToWhereWhithAnd("uc.codigo = ", param.getUsuarioCadsus().getCodigo());

            if (Coalesce.asString(param.getDescricao()).trim().length() > 0) {
                hql.addToWhereWhithAnd(hql.getConsultaLiked("ap.descricao", param.getDescricao()));
            }

            if (param.getTipoRegistroProntuario() != null) {
                hql.addToWhereWhithAnd("coalesce(ap.tipoRegistro,1) = ", param.getTipoRegistroProntuario());
            }

            hql.addToWhereWhithAnd("ap.flagOrigem in ", Arrays.asList(AtendimentoProntuario.Origem.EXTERNO.value(), AtendimentoProntuario.Origem.IMPORTADO.value()));

            hql.addToOrder("ap.data desc");
        }

        @Override
        public List<AtendimentoProntuarioDTO> getResult() {
            return this.result;
        }

        @Override
        protected void result(HQLHelper hql, Object result) {
            this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
        }

        @Override
        protected void customQuery(Query query) {
            query.setFirstResult(param.getStartFirst());
            query.setMaxResults(param.getMaxResultsLoading());
        }
    }

}
