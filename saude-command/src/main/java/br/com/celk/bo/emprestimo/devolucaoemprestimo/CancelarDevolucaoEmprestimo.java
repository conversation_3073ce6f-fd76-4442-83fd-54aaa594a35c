package br.com.celk.bo.emprestimo.devolucaoemprestimo;

import br.com.celk.bo.emprestimo.interfaces.dto.DevolucaoEmprestimoItensEloDTO;
import br.com.celk.bo.emprestimo.interfaces.facade.EmprestimoFacade;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.MovimentoEstoqueFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimo;
import br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimoElo;
import br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimoItem;
import br.com.ksisolucoes.vo.emprestimo.TipoDevolucao;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoque;
import br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento;
import java.util.ArrayList;
import java.util.List;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class CancelarDevolucaoEmprestimo extends AbstractCommandTransaction {

    private DevolucaoEmprestimo devolucaoEmprestimo;
    private String motivo;
    private TipoDocumento tipoDocumentoEstorno;

    public CancelarDevolucaoEmprestimo(DevolucaoEmprestimo devolucaoEmprestimo, String motivo) {
        this.devolucaoEmprestimo = devolucaoEmprestimo;
        this.motivo = motivo;
    }
        
    @Override
    public void execute() throws DAOException, ValidacaoException {
        List<DevolucaoEmprestimoItem> list = this.getSession().createCriteria(DevolucaoEmprestimoItem.class)
            .add(Restrictions.eq(DevolucaoEmprestimoItem.PROP_DEVOLUCAO_EMPRESTIMO, devolucaoEmprestimo))
            .add(Restrictions.eq(DevolucaoEmprestimoItem.PROP_STATUS, DevolucaoEmprestimoItem.Status.NORMAL.value()))
            .list();
        
        List<DevolucaoEmprestimoItensEloDTO> itensDTO = new ArrayList<DevolucaoEmprestimoItensEloDTO>();
        
        for(DevolucaoEmprestimoItem item : list){
            DevolucaoEmprestimoItensEloDTO itemDTO = new DevolucaoEmprestimoItensEloDTO();
            itemDTO.setDevolucaoEmprestimoItem(item);
            
            List<DevolucaoEmprestimoElo> eloList = this.getSession().createCriteria(DevolucaoEmprestimoElo.class)
                .add(Restrictions.eq(DevolucaoEmprestimoElo.PROP_DEVOLUCAO_EMPRESTIMO_ITEM, item))
                .list();
            
            if(CollectionUtils.isNotNullEmpty(eloList)){
                itemDTO.setDevolucaoEmprestimoEloList(eloList);
            }
            
            itensDTO.add(itemDTO);
        }
        
        devolucaoEmprestimo.setStatus(DevolucaoEmprestimo.Status.CANCELADA.value());
        BOFactory.save(devolucaoEmprestimo);
        
        for (DevolucaoEmprestimoItensEloDTO eloDTO : itensDTO) {
            eloDTO.getDevolucaoEmprestimoItem().setMotivoCancelamento(motivo);
            eloDTO.getDevolucaoEmprestimoItem().setDataCancelamento(DataUtil.getDataAtual());
            eloDTO.getDevolucaoEmprestimoItem().setUsuarioCancelamento(getSessao().<Usuario>getUsuario());
            eloDTO.getDevolucaoEmprestimoItem().setStatus(DevolucaoEmprestimoItem.Status.CANCELADA.value());
            
            DevolucaoEmprestimoItem devolucaoEmprestimoItem = BOFactory.save(eloDTO.getDevolucaoEmprestimoItem());

            gerarMovimentoEstoque(devolucaoEmprestimoItem);
            
            BOFactory.getBO(EmprestimoFacade.class).cancelarDevolucaoEmprestimoElo(devolucaoEmprestimoItem, eloDTO.getDevolucaoEmprestimoEloList());
        }
    }
    
    private void gerarMovimentoEstoque(DevolucaoEmprestimoItem devolucaoEmprestimoItem) throws DAOException, ValidacaoException{
        MovimentoEstoque me = new MovimentoEstoque();

        me.setTipoDocumento(getTipoDocumentoEstorno());
        me.setNumeroDocumento(devolucaoEmprestimo.getCodigo().toString());
        me.setProduto(devolucaoEmprestimoItem.getProduto());
        me.setQuantidade(devolucaoEmprestimoItem.getQuantidade());
        me.setGrupoEstoque(devolucaoEmprestimoItem.getGrupoEstoque());
        me.setDataValidadeGrupoEstoque(devolucaoEmprestimoItem.getDataValidade());

        BOFactory.getBO(MovimentoEstoqueFacade.class).gerarMovimentoEstoque(me);
    }
    
    private TipoDocumento getTipoDocumentoEstorno() throws DAOException{
        if(this.tipoDocumentoEstorno == null){
            TipoDevolucao tipoDevolucao = (TipoDevolucao) getSession().get(TipoDevolucao.class, devolucaoEmprestimo.getTipoDevolucao().getCodigo());
            this.tipoDocumentoEstorno = tipoDevolucao.getTipoDocumentoCancelamento();

            return this.tipoDocumentoEstorno;
        } else {
            return this.tipoDocumentoEstorno;
        }
    }
}