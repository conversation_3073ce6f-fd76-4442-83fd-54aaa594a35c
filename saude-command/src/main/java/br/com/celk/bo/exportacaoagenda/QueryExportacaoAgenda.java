package br.com.celk.bo.exportacaoagenda;

import br.com.celk.unidadesaude.esus.relatorios.dto.ExportacaAgendaDTOParam;
import br.com.celk.unidadesaude.esus.relatorios.dto.RelatorioSictecDTO;
import br.com.celk.util.ArquivoSictecUtil;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.TipoAtendimentoAgenda;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;

import java.util.List;
import java.util.Map;

public class QueryExportacaoAgenda extends CommandQuery<QueryExportacaoAgenda> implements ITransferDataReport<ExportacaAgendaDTOParam, RelatorioSictecDTO> {
    ExportacaAgendaDTOParam param;
    List<RelatorioSictecDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {

        hql.addToSelect("coalesce(usuarioCadsus.prontuario, '')", "prontuario");
        hql.addToSelect("''", "cns");
        hql.addToSelect("to_char(sa.dataAgendamento,'dd/mm/yyyy')", "data");
        hql.addToSelect("to_char(sa.dataAgendamento,'HH:MM')", "hora");
        hql.addToSelect("profissionalExecutante.numeroRegistro", "numeroProfissional");
        hql.addToSelect("coalesce(oe.codigo, 0)", "conselhoClasse");
        hql.addToSelect("''", "especialidadeMedico");
        hql.addToSelect("''", "prestadorAgendado");
        hql.addToSelect("''", "vlAmb");
        hql.addToSelect("''", "vlSa");
        hql.addToSelect("''", "codPaciente");
        hql.addToSelect("usuarioCadsus.nome", "nomePaciente");
        hql.addToSelect("usuarioCadsus.sexo", "sexo");
        hql.addToSelect("to_char(usuarioCadsus.dataNascimento, 'dd/mm/yyyy')", "dataNascimento");
        hql.addToSelect("usuarioCadsus.nomeMae", "maePaciente");
        hql.addToSelect("''", "pisPasep");
        hql.addToSelect("coalesce(substring(usuarioCadsus.celular, 0, 3), substring(usuarioCadsus.telefone, 0, 3))", "ddd");
        hql.addToSelect("coalesce(substring(usuarioCadsus.celular, 4), substring(usuarioCadsus.telefone, 4))", "telefone");
        hql.addToSelect("cid.descricao", "municipioPaciente");
        hql.addToSelect("est.sigla", "uf");
        hql.addToSelect("enuc.bairro", "bairro");
        hql.addToSelect("enuc.logradouro", "logradouro");
        hql.addToSelect("enuc.cep", "cep");
        hql.addToSelect("enuc.numeroLogradouro", "numeroLogradouro");
        hql.addToSelect("coalesce(cidadeNasc.descricao, '')", "municipioNascimento");
        hql.addToSelect("estadoNasc.sigla", "ufMunicipioNascimento");
        hql.addToSelect("cast(tp.codigo as text)", "tipoProcedimento");
        hql.addToSelect("(case when taa.tipoAtendimento = 5 or taa.tipoAtendimento = 2 then '01' else cast(taa.tipoAtendimento as text) end)", "tipoAtendimento");
        hql.addToSelect("tlc.descricao", "tipoLogradouro");
        hql.addToSelect("(select min(cns.numeroCartao) from UsuarioCadsusCns cns"
                        + " left join cns.usuarioCadsus usuCadsus "
                        + "where usuCadsus = usuarioCadsus)", "cns2");

        hql.setTypeSelect(RelatorioSictecDTO.class.getName());

        hql.addToFrom(" AgendaGradeAtendimentoHorario agah" +
                " left join agah.solicitacaoAgendamento sa" +
                " left join sa.tipoProcedimento tp" +
                " left join sa.empresa emp" +
                " left join tp.procedimento p" +
                " left join sa.profissionalExecutante profissionalExecutante" +
                " left join sa.usuarioCadsus usuarioCadsus" +
                " left join usuarioCadsus.enderecoUsuarioCadsus enuc" +
                " left join enuc.tipoLogradouro tlc" +
                " left join enuc.cidade cid" +
                " left join sa.empresa empresa" +
                " left join empresa.cidade cidade" +
                " left join cidade.estado est" +
                " left join sa.usuarioAutorizador usuarios" +
                " left join sa.profissional profissional" +
                " left join agah.agendaGradeAtendimento aga" +
                " left join aga.tipoAtendimentoAgenda taa" +
                " left join profissional.orgaoEmissor oe" +
                " left join usuarioCadsus.cidadeNascimento cidadeNasc" +
                " left join cidade.estado estadoNasc");

        hql.addToWhereWhithAnd("sa.status = " + SolicitacaoAgendamento.STATUS_AGENDADO.longValue());
        hql.addToWhereWhithAnd("taa.tipoAtendimento = " + TipoAtendimentoAgenda.TIPO_REGULACAO.longValue());
        hql.addToWhereWhithAnd("tp.regulado = '" + RepositoryComponentDefault.SIM + "'");
        hql.addToWhereWhithAnd("tp.controleCota = '" + RepositoryComponentDefault.SIM + "'");
        hql.addToWhereWhithAnd("agah.dataConfirmacao is null ");
        hql.addToWhereWhithAnd("agah.dataCancelamento is null ");
        hql.addToWhereWhithAnd("sa.unidadeExecutante = " + param.getEstabelecimento().getCodigo());
        hql.addToWhereWhithAnd("sa.tipoConsulta = " + param.getTipoAtendimento());
        hql.addToWhereWhithAnd("sa.dataAgendamento ", param.getPeriodo());
        hql.addToWhereWhithAnd("enuc.ativo  = " + RepositoryComponentDefault.SIM_LONG);
    }

    @Override
    public void setDTOParam(ExportacaAgendaDTOParam param) {
        this.param = param;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
        ArquivoSictecUtil arquivoSictecUtil = new ArquivoSictecUtil();
        arquivoSictecUtil.formatarPadraoSictec(this.result);
    }

    @Override
    public List<RelatorioSictecDTO> getResult() {
        return result;
    }
}
