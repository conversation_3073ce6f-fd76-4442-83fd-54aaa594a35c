package br.com.celk.bo.hospital.financeiro.contafinanceira;

import br.com.celk.bo.hospital.financeiro.interfaces.dto.ContaFinanceiraItemDTO;
import br.com.celk.bo.hospital.financeiro.interfaces.dto.GerarContaFinanceiraDTO;
import br.com.celk.bo.hospital.financeiro.interfaces.dto.GerarContaFinanceiraItemDTO;
import br.com.celk.bo.hospital.financeiro.interfaces.dto.QueryConsultaContaFinanceiraDTOParam;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.hospital.financeiro.ContaFinanceira;
import java.util.Arrays;

/**
 *
 * <AUTHOR>
 */
public class GerarContaFinanceira extends AbstractCommandTransaction {
    
    private ContaFinanceira contaFinanceira;
    private final GerarContaFinanceiraDTO dto;
    
    public GerarContaFinanceira(GerarContaFinanceiraDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        consultarContaFinanceira();
        
        if(contaFinanceira != null){
            gerarItemFinanceiro();
        } else {
            contaFinanceira = new ContaFinanceira();
            contaFinanceira.setAtendimentoPrincipal(dto.getAtendimentoPrincipal());
            contaFinanceira.setContaPaciente(BOFactory.getBO(HospitalFacade.class).encontrarContaPaciente(dto.getAtendimentoPrincipal(), false));
            contaFinanceira.setFormaPagamento(dto.getFormaPagamento());
            contaFinanceira.setUsuarioCadsus(dto.getUsuarioCadsus());
            contaFinanceira.setValorPago(Coalesce.asDouble(dto.getValor()));
            
            contaFinanceira = BOFactory.save(contaFinanceira);
            gerarItemFinanceiro();
        }
    }

    public ContaFinanceira getContaFinanceira() {
        return contaFinanceira;
    }

    public void setContaFinanceira(ContaFinanceira contaFinanceira) {
        this.contaFinanceira = contaFinanceira;
    }
    
    private void consultarContaFinanceira() throws DAOException, ValidacaoException{
        QueryConsultaContaFinanceiraDTOParam param = new QueryConsultaContaFinanceiraDTOParam();
        param.setAtendimentoPrincipal(dto.getAtendimentoPrincipal());
        param.setUsuarioCadsus(dto.getUsuarioCadsus());
        param.setStatusList(Arrays.asList(ContaFinanceira.Status.ABERTO.value(), ContaFinanceira.Status.FATURADO.value()));
        
        contaFinanceira = BOFactory.getBO(HospitalFacade.class).consultarContaFinanceira(param);
    }
    
    private void gerarItemFinanceiro() throws DAOException, ValidacaoException{
        ContaFinanceiraItemDTO itemFinanceiro = new ContaFinanceiraItemDTO();
        itemFinanceiro.setQuantidade(1L);
        itemFinanceiro.setFormaPagamento(dto.getFormaPagamento());
        itemFinanceiro.setTipoMovimentoContaFinanceira(dto.getFormaPagamento().getTipoMovimentoContaFinanceira());
        itemFinanceiro.setValor(dto.getValor());

        GerarContaFinanceiraItemDTO itemDTO = new GerarContaFinanceiraItemDTO();
        itemDTO.setContaFinanceira(contaFinanceira);
        itemDTO.setGerarContaFinanceiraDTO(dto);
        itemDTO.getItemFinanceiroList().add(itemFinanceiro);

        BOFactory.getBO(HospitalFacade.class).gerarContaFinanceiraItem(itemDTO);
    }
    
}
