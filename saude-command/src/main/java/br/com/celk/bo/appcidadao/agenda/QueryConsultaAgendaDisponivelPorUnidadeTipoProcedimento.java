package br.com.celk.bo.appcidadao.agenda;

import br.com.celk.appcidadao.dto.agenda.AgendaDTO;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeHorario;
import br.com.ksisolucoes.vo.agendamento.TipoAtendimentoAgenda;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.hibernate.type.LongType;
import org.hibernate.type.StringType;
import org.hibernate.type.TimestampType;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> Rodrigues
 */
public class QueryConsultaAgendaDisponivelPorUnidadeTipoProcedimento extends AbstractCommandTransaction<QueryConsultaAgendaDisponivelPorUnidadeTipoProcedimento> {

    private List<AgendaDTO> resultado;
    private Long codigoUnidade;
    private Long codigoProcedimento;
    private Long tipoAtendimento;

    public QueryConsultaAgendaDisponivelPorUnidadeTipoProcedimento(Long codigoUnidade, Long codigoProcedimento) {
        this.codigoUnidade = codigoUnidade;
        this.codigoProcedimento = codigoProcedimento;
    }

    public QueryConsultaAgendaDisponivelPorUnidadeTipoProcedimento(Long codigoUnidade, Long codigoProcedimento, Long tipoAtendimento) {
        this.codigoUnidade = codigoUnidade;
        this.codigoProcedimento = codigoProcedimento;
        this.tipoAtendimento = tipoAtendimento;

    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        HQLHelper hql = new HQLHelper();
        createSQLQuery(hql);
        SQLQuery query = getSession().createSQLQuery(hql.getQuery());
        setParameters(hql, query);
        this.addScalar(query);
        resultado = query.list();
    }

    protected void createSQLQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.addToSelect("agenda.cd_agenda", "idAgenda");
        hql.addToSelect("tipo_procedimento.cd_tp_procedimento", "idProcedimento");
        hql.addToSelect("tipo_procedimento.ds_tp_procedimento", "descricacaoProcedimento");
        hql.addToSelect("empresa.empresa", "idUnidadeSaude");
        hql.addToSelect("empresa.descricao", "descricacaoUnidadeSaude");
        hql.addToSelect("profissional.cd_profissional", "idProfissional");
        hql.addToSelect("profissional.nm_profissional", "nomeProfissional");
        hql.addToSelect("agenda_grade_atendimento.cd_ag_gra_atendimento", "idAgendaGradeAtendimento");
        hql.addToSelect("agenda_grade_atendimento.qt_ate_original", "agendaGradeAtendimentoNumeroVagas");
        hql.addToSelect("agenda_grade_atendimento.tempo_medio", "agendaGradeAtendimentotempoMedio");
        hql.addToSelect("agenda_grade.cd_ag_grade", "idAgendaGrade");
        hql.addToSelect("agenda_grade.hora_inicial", "agendaGradeHoraInicial");
        hql.addToSelect("agenda_grade.hora_final", "agendaGradeHoraFinal");
        hql.addToSelect("agenda_grade_horario.cd_agenda_horario", "idAgendaGradeHorario");
        hql.addToSelect("agenda_grade_horario.hora", "hora");

        hql.setTypeSelect(AgendaDTO.class.getName());
        hql.setUseSQL(true);

        hql.addToFrom("agenda_grade_horario" +
                " left join agenda_grade_atendimento on agenda_grade_horario.cd_ag_gra_atendimento = agenda_grade_atendimento.cd_ag_gra_atendimento" +
                " left join tipo_atendimento_agenda on tipo_atendimento_agenda.cd_tipo = agenda_grade_atendimento.cd_tipo" +
                " left join agenda_grade on agenda_grade_atendimento.cd_ag_grade = agenda_grade.cd_ag_grade" +
                " left join agenda on agenda_grade.cd_agenda = agenda.cd_agenda" +
                " left join tipo_procedimento on agenda.cd_tp_procedimento = tipo_procedimento.cd_tp_procedimento" +
                " left join empresa on agenda.empresa = empresa.empresa" +
                " left join profissional on agenda.cd_profissional = profissional.cd_profissional");
        if (codigoUnidade != null) {
            hql.addToWhereWhithAnd("empresa.empresa = :codigoUnidade");
        }
        hql.addToWhereWhithAnd("agenda_grade_horario.hora >= NOW()");
        hql.addToWhereWhithAnd("(agenda_grade_horario.hora - NOW()) <= agenda.visibilidade_agenda * interval '1 week'");
        hql.addToWhereWhithAnd("agenda_grade_horario.status = :statusAgendaGradeHorario");
        hql.addToWhereWhithAnd("agenda.status in (:statusAgenda)");
        hql.addToWhereWhithAnd("tipo_procedimento.cd_tp_procedimento = :codigoProcedimento");
        if(isAgendarConsultaViaPDA()){
            hql.addToWhereWhithAnd("tipo_atendimento_agenda.tp_atendimento in :tipoAtendimento");
        }else{
            hql.addToWhereWhithAnd("tipo_atendimento_agenda.tp_atendimento = :tipoAtendimento");
        }
    }

    protected void setParameters(HQLHelper hql, SQLQuery query) throws ValidacaoException, DAOException {
        query.setParameter("codigoProcedimento", codigoProcedimento);
        query.setParameterList("statusAgenda", Arrays.asList(Agenda.STATUS_CONFIRMADO, Agenda.STATUS_ABERTO));
        query.setParameter("statusAgendaGradeHorario", AgendaGradeHorario.Status.PENDENTE.value());
        if (codigoUnidade != null)
            query.setParameter("codigoUnidade", codigoUnidade);
        if(isAgendarConsultaViaPDA()){
            query.setParameterList("tipoAtendimento",
                    Arrays.asList(TipoAtendimentoAgenda.TipoAtendimento.TIPO_CONSULTA.value(),
                            TipoAtendimentoAgenda.TipoAtendimento.APP_CIDADAO.value(),
                            TipoAtendimentoAgenda.TipoAtendimento.APP_CIDADAO_GERAL.value()));
        }else {
            if (TipoAtendimentoAgenda.TipoAtendimento.TIPO_CONSULTA.value().equals(tipoAtendimento)) {
                query.setParameter("tipoAtendimento", TipoAtendimentoAgenda.TipoAtendimento.TIPO_CONSULTA.value());
            } else if (TipoAtendimentoAgenda.TipoAtendimento.APP_CIDADAO_GERAL.value().equals(tipoAtendimento)) {
                query.setParameter("tipoAtendimento", TipoAtendimentoAgenda.TipoAtendimento.APP_CIDADAO_GERAL.value());
            } else {
                query.setParameter("tipoAtendimento", TipoAtendimentoAgenda.TipoAtendimento.APP_CIDADAO.value());
            }
        }

    }


    private void addScalar(SQLQuery sql) {
        sql.addScalar("idAgenda", LongType.INSTANCE)
                .addScalar("idProcedimento", LongType.INSTANCE)
                .addScalar("descricacaoProcedimento", StringType.INSTANCE)
                .addScalar("idUnidadeSaude", LongType.INSTANCE)
                .addScalar("descricacaoUnidadeSaude", StringType.INSTANCE)
                .addScalar("idProfissional", LongType.INSTANCE)
                .addScalar("nomeProfissional", StringType.INSTANCE)
                .addScalar("idAgendaGradeAtendimento", LongType.INSTANCE)
                .addScalar("agendaGradeAtendimentoNumeroVagas", LongType.INSTANCE)
                .addScalar("agendaGradeAtendimentotempoMedio", LongType.INSTANCE)
                .addScalar("idAgendaGrade", LongType.INSTANCE)
                .addScalar("agendaGradeHoraInicial", TimestampType.INSTANCE)
                .addScalar("agendaGradeHoraFinal", TimestampType.INSTANCE)
                .addScalar("idAgendaGradeHorario", LongType.INSTANCE)
                .addScalar("hora", TimestampType.INSTANCE)
                .setResultTransformer(Transformers.aliasToBean(AgendaDTO.class));
    }

    public List<AgendaDTO> getResultado() {
        return resultado;
    }

    private boolean isAgendarConsultaViaPDA(){
        String agendarConsultaViaPDA = null;
        try {
            agendarConsultaViaPDA = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MOBILE).getParametro("AgendarConsultaViaPDA");
        }catch (Exception e){
            e.printStackTrace();
        }
        return RepositoryComponentDefault.SIM.equals(agendarConsultaViaPDA);
    }

}

