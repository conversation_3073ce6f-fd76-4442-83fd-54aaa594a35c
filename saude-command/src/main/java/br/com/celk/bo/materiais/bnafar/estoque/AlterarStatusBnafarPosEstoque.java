package br.com.celk.bo.materiais.bnafar.estoque;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.materiais.bnafar.interfaces.BnafarHelper;
import org.hibernate.Query;
import org.hibernate.type.StandardBasicTypes;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class AlterarStatusBnafarPosEstoque extends AbstractCommandTransaction<AlterarStatusBnafarPosEstoque> {

    private Long codigo;
    private Long codigoElo;
    private BnafarHelper.StatusRegistro status;

    public AlterarStatusBnafarPosEstoque(Long codigo, Long codigoElo, BnafarHelper.StatusRegistro status) {
        this.codigo = codigo;
        this.codigoElo = codigoElo;
        this.status = status;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        String hqlBnafarPosEstoque = "UPDATE BnafarPosEstoque bpe"
                + " SET flag_status_registro = :novoStatus , "
                + " cd_bnafar_pos_estoque_elo = :codigoElo, "
                + " dt_ultimo_envio = :dtUltimoEnvio "
                + " WHERE cd_bnafar_pos_estoque = :codigo ";

        Query qBnafarPosEstoque = getSession().createQuery(hqlBnafarPosEstoque);
        qBnafarPosEstoque.setParameter("novoStatus", status.value());
        qBnafarPosEstoque.setParameter("codigo", codigo);
        qBnafarPosEstoque.setParameter("dtUltimoEnvio", new Date());
        if (codigoElo == null) {
            qBnafarPosEstoque.setParameter("codigoElo", codigoElo, StandardBasicTypes.LONG);
        } else {
            qBnafarPosEstoque.setParameter("codigoElo", codigoElo);
        }
        qBnafarPosEstoque.executeUpdate();
    }
}