package br.com.celk.bo.appcidadao.agenda;

import br.com.celk.appcidadao.dto.agenda.ProcedimentoDTO;
import br.com.celk.appcidadao.dto.agenda.QueryConsultaProcedimentoAgendaDisponivelDTOParam;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.agendamento.TipoAtendimentoAgenda;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.hibernate.type.LongType;
import org.hibernate.type.StringType;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class QueryConsultaProcedimentoAgendaDisponivel extends AbstractCommandTransaction<QueryConsultaProcedimentoAgendaDisponivel> {

    private List<ProcedimentoDTO> resultado;
    private QueryConsultaProcedimentoAgendaDisponivelDTOParam param;

    public QueryConsultaProcedimentoAgendaDisponivel(QueryConsultaProcedimentoAgendaDisponivelDTOParam param) {
        this.param = param;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if(this.param.isDisponibilizaAgendaUnidadeReferencia() && !this.param.hasArea() && this.param.getTipoAtendimentoAgenda() == null){
            resultado = new ArrayList<>();
        }else{
            HQLHelper hql = new HQLHelper();
            createSQLQuery(hql);
            SQLQuery query = getSession().createSQLQuery(hql.getQuery());
            setParameters(hql, query);
            this.addScalar(query);
            resultado = query.list();
        }
    }

    protected void createSQLQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.addToSelectAndGroup("tipo_procedimento.cd_tp_procedimento", "codigo");
        hql.addToSelectAndGroup("tipo_procedimento.ds_tp_procedimento", "descricao");
        hql.addToSelectAndGroup("empresa.empresa", "careUnitId");
        hql.addToSelectAndGroup("empresa.descricao", "careUnitName");
        hql.addToSelectAndGroup("empresa.telefone", "careUnitPhone");
        hql.addToSelectAndGroup("empresa.descricao", "careUnitDescription");
        hql.addToSelectAndGroup("empresa.rua", "careUnitStreet");
        hql.addToSelectAndGroup("empresa.bairro", "careUnitNeighborhood");
        hql.addToSelectAndGroup("empresa.numero", "careUnitNumber");
        hql.addToSelectAndGroup("empresa.complemento", "careUnitComplement");

        hql.setTypeSelect(ProcedimentoDTO.class.getName());
        hql.setUseSQL(true);

        hql.addToFrom("agenda_grade_horario" +
                " left join agenda_grade_atendimento on agenda_grade_horario.cd_ag_gra_atendimento = agenda_grade_atendimento.cd_ag_gra_atendimento" +
                " left join tipo_atendimento_agenda on tipo_atendimento_agenda.cd_tipo = agenda_grade_atendimento.cd_tipo" +
                " left join agenda_grade on agenda_grade_atendimento.cd_ag_grade = agenda_grade.cd_ag_grade" +
                " left join agenda on agenda_grade.cd_agenda = agenda.cd_agenda" +
                " left join tipo_procedimento on agenda.cd_tp_procedimento = tipo_procedimento.cd_tp_procedimento" +
                " left join empresa on agenda.empresa = empresa.empresa");

        if(this.param.isDisponibilizaAgendaUnidadeReferencia() && this.param.getTipoAtendimentoAgenda() == null){
            if (this.param.getUsuarioCadsusEquipe() != null) {
                hql.addToWhereWhithAnd("agenda.empresa = " + param.getUsuarioCadsusEquipe().getEmpresa().getCodigo());
                filtrarAgendasByAreasUsuario(hql);
            }
        }

        hql.addToWhereWhithAnd("(agenda_grade_horario.hora - NOW()) <= agenda.visibilidade_agenda * interval '1 week'");
        hql.addToWhereWhithAnd("agenda_grade_horario.hora >= NOW()");
        hql.addToWhereWhithAnd("agenda_grade_atendimento.qt_ate_original > coalesce(qtdade_ate_bloqueado,0) + coalesce(qtdade_ate_reservado,0)");
        if(this.param.isAgendarConsultaViaPDA() && param.getTipoAtendimentoAgenda() == null) {
            hql.addToWhereWhithAnd("tipo_atendimento_agenda.tp_atendimento in :tipoAtendimento");
        }else {
            hql.addToWhereWhithAnd("tipo_atendimento_agenda.tp_atendimento = :tipoAtendimento");
        }
        if (param.getUsuarioCadsus() != null) {
            hql.addToWhereWhithAnd("(agenda.idade_inicio is null or " + param.getUsuarioCadsus().getIdade() + " >= agenda.idade_inicio)");
            hql.addToWhereWhithAnd("(agenda.idade_fim is null or " + param.getUsuarioCadsus().getIdade() + " <= agenda.idade_fim)");
            hql.addToWhereWhithAnd("(coalesce(agenda.sexo, 'N') = 'N' or '" + param.getUsuarioCadsus().getSexo() + "' = agenda.sexo)");
        }

        hql.addToWhereWhithAnd("agenda.status = :status");

    }

    private void filtrarAgendasByAreasUsuario(HQLHelper hql) {
        ArrayList<String> codigosArea = new ArrayList();
        if (this.param.hasAreaEquipe()){
            codigosArea.add(this.param.getCdAreaEquipe().toString());
        }
        if (this.param.hasAreaEnderecoDomicilio()){
            codigosArea.add(this.param.getCdAreaEnderecoDomicilio().toString());
        }
        hql.addToWhereWhithAnd("exists(SELECT 1 FROM equipe, equipe_area WHERE equipe.empresa = agenda.empresa and cd_area in ("+String.join(",", codigosArea)+"))");
    }

    protected void setParameters(HQLHelper hql, SQLQuery query) throws ValidacaoException, DAOException {
        if(this.param.isAgendarConsultaViaPDA() && param.getTipoAtendimentoAgenda() == null){
            query.setParameterList("tipoAtendimento",
                    Arrays.asList(TipoAtendimentoAgenda.TipoAtendimento.APP_CIDADAO.value(),
                            TipoAtendimentoAgenda.TipoAtendimento.TIPO_CONSULTA.value()));
        } else {
            query.setParameter("tipoAtendimento", param.getTipoAtendimentoAgenda() != null ? param.getTipoAtendimentoAgenda() : TipoAtendimentoAgenda.TipoAtendimento.APP_CIDADAO.value());
        }
        query.setParameter("status", Agenda.STATUS_CONFIRMADO);
    }

    private void addScalar(SQLQuery sql) {
        sql.addScalar("codigo", LongType.INSTANCE)
                .addScalar("descricao", StringType.INSTANCE)
                .addScalar("careUnitId", LongType.INSTANCE)
                .addScalar("careUnitName", StringType.INSTANCE)
                .addScalar("careUnitPhone", StringType.INSTANCE)
                .addScalar("careUnitDescription", StringType.INSTANCE)
                .addScalar("careUnitStreet", StringType.INSTANCE)
                .addScalar("careUnitNeighborhood", StringType.INSTANCE)
                .addScalar("careUnitNumber", StringType.INSTANCE)
                .addScalar("careUnitComplement", StringType.INSTANCE)
                .setResultTransformer(Transformers.aliasToBean(ProcedimentoDTO.class));
    }

    public List<ProcedimentoDTO> getResultado() {
        return resultado;
    }

}

