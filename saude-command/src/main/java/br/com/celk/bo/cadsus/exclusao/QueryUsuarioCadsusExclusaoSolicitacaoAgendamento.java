package br.com.celk.bo.cadsus.exclusao;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import java.util.List;
import java.util.Map;
import org.hibernate.Query;

/**
 *
 * <AUTHOR>
 */
public class QueryUsuarioCadsusExclusaoSolicitacaoAgendamento extends CommandQuery {

    private final List<Long> codigoProcessoList;
    private List<SolicitacaoAgendamento> list;

    public QueryUsuarioCadsusExclusaoSolicitacaoAgendamento(List<Long> codigoProcessoList) {
        this.codigoProcessoList = codigoProcessoList;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(SolicitacaoAgendamento.class.getName());
        
        hql.addToSelect("sa.codigo", "codigo");
        hql.addToSelect("sa.dataSolicitacao", "dataSolicitacao");
        hql.addToSelect("sa.dataCadastro", "dataCadastro");
        hql.addToSelect("sa.prioridade", "prioridade");
        hql.addToSelect("sa.tipoConsulta", "tipoConsulta");
        hql.addToSelect("sa.status", "status");
        hql.addToSelect("sa.version", "version");
        
        hql.addToSelect("tp.codigo", "tipoProcedimento.codigo");
        hql.addToSelect("tp.descricao", "tipoProcedimento.descricao");
        
        hql.addToSelect("p.codigo", "procedimento.codigo");
        hql.addToSelect("p.descricao", "procedimento.descricao");
        
        hql.addToFrom("SolicitacaoAgendamento sa "
                + " left join sa.tipoProcedimento tp"
                + " left join sa.procedimento p"
        );

        hql.addToWhereWhithAnd("sa.codigo in :codigoProcessoList");
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setParameterList("codigoProcessoList", codigoProcessoList);        
    }
    
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<SolicitacaoAgendamento> getResult() {
        return this.list;
    }
    
}