package br.com.celk.bo.materiais.bnafar.saida;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.materiais.bnafar.interfaces.BnafarHelper;
import org.hibernate.Query;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class AlterarStatusBnafarSaida extends AbstractCommandTransaction {

    private Long codigo;
    private Long codigoElo;
    private Long status;

    public AlterarStatusBnafarSaida(Long codigo, Long codigoElo) {
        this.codigo = codigo;
        this.codigoElo = codigoElo;
        this.status = null;
    }

    public AlterarStatusBnafarSaida(Long codigo, Long codigoElo, Long status) {
        this.codigo = codigo;
        this.codigoElo = codigoElo;
        this.status = status;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        StringBuilder update = new StringBuilder();
        update.append("UPDATE bnafar_saida be");
        update.append("   SET flag_status_registro = :novoStatus,");
        update.append("       cd_bnafar_saida_elo = :novoElo,");
        update.append("       dt_ultimo_envio = :dtUltimoEnvio");
        if (BnafarHelper.StatusRegistro.CANCELADO.value().equals(this.status)) {
            update.append(", dt_cancelamento = :dtCancelamento");
            update.append(", cd_usuario_cancelamento = :codigoUsuario");
        }
        update.append(" WHERE cd_bnafar_saida = :codigo ");

        Query query = getSession().createSQLQuery(update.toString());
        query.setLong("novoStatus",  this.status == null ? BnafarHelper.StatusRegistro.ENVIADO.value() : this.status);
        query.setLong("novoElo", codigoElo);
        query.setDate("dtUltimoEnvio", new Date());
        if (BnafarHelper.StatusRegistro.CANCELADO.value().equals(this.status)) {
            query.setParameter("dtCancelamento", new Date());
            query.setParameter("codigoUsuario", getSessao().getCodigoUsuario());
        }
        query.setLong("codigo", codigo);

        query.executeUpdate();
    }
}