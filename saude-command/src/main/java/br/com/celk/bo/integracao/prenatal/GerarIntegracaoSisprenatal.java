package br.com.celk.bo.integracao.prenatal;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.integracao.prenatal.SisprenatalProcesso;

/**
 *
 * <AUTHOR>
 */
public class GerarIntegracaoSisprenatal extends AbstractCommandTransaction {

    public GerarIntegracaoSisprenatal() {
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        /**
         * salva o processo
         */
        SisprenatalProcesso sisprenatalProcesso = new SisprenatalProcesso();
        sisprenatalProcesso.setDataGeracao(DataUtil.getDataAtual());
        sisprenatalProcesso.setStatus(SisprenatalProcesso.Status.GERANDO.value());
        sisprenatalProcesso.setUsuario(SessaoAplicacaoImp.getInstance().getUsuario());
        
        SisprenatalProcesso sp = BOFactory.newTransactionSave(sisprenatalProcesso);
        
        /**
         * inicia processo assíncrono
         */
        
        BOFactory.getBO(AtendimentoFacade.class).gerarXmlIntegracaoSisprenatal(sp.getCodigo());
    }
    
}
