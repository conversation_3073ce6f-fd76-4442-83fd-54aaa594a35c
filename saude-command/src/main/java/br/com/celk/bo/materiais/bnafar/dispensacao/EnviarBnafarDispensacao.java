package br.com.celk.bo.materiais.bnafar.dispensacao;

import br.com.celk.materiais.bnafar.dto.*;
import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.AwsUtils;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.materiais.bnafar.dispensacao.BnafarDispensacao;
import br.com.ksisolucoes.vo.materiais.bnafar.dispensacao.BnafarDispensacaoElo;
import br.com.ksisolucoes.vo.materiais.bnafar.dispensacao.BnafarDispensacaoIntegracao;
import br.com.ksisolucoes.vo.materiais.bnafar.interfaces.BnafarHelper;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.SendMessageResult;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR> Prado
 */

public class EnviarBnafarDispensacao extends AbstractCommandTransaction<EnviarBnafarDispensacao> {

    private static final String MSG_ENVIO_SQS = "Envio de mensagens para SQS - BNAFAR Dispensacao.";
    private CaracterizacaoDispensacaoDto caracterizacaoDispensacaoDto;
    private EstabelecimentoDispensacaoDto estabelecimentoDto;
    private List<ItemDispensacaoDto> itemDispensacaoDtos;
    private UsuarioSusDispensacaoDto usuarioCadsusDispensacaoDto;
    private Long PKBnafarDispensacaoIntegracao;
    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (BnafarHelper.verifcarDataDeInicioDeIntegracaoBnafar()) {
            int limit = 500;
            int retorno;
            try {
                do {
                    flushAndClearSession();
                    retorno = processarDispensacaoBnafar(limit);
                } while (retorno > 0);
            } catch (ValidacaoException ex) {
                Loggable.log.error(MSG_ENVIO_SQS, ex);
            }
            Loggable.log.info(MSG_ENVIO_SQS.concat(BnafarHelper.TipoSincronizacao.DISPENSASAO.descricao()).concat(" - Envio finalizado."));
        }
    }

    private void flushAndClearSession() {
        getSession().flush();
        getSession().clear();
    }

    private int processarDispensacaoBnafar(int limit) throws DAOException, ValidacaoException {
        List<BnafarDispensacao> bnafarDispensacaos = BOFactory.getBO(MaterialBasicoFacade.class).queryBnafarDispensacaoStatusGerado(BnafarHelper.getDateInicioIntegracaoBnafar(), limit);

        if (CollectionUtils.isAllEmpty(bnafarDispensacaos)) {
            return 0;
        }

        enviar(bnafarDispensacaos);
        return bnafarDispensacaos.size();
    }

    private void enviar(List<BnafarDispensacao> bnafarDispensacaos) throws DAOException, ValidacaoException {
        if (CollectionUtils.isNotNullEmpty(bnafarDispensacaos)) {
            Loggable.log.info(MSG_ENVIO_SQS.concat(" Iniciando ").concat(String.valueOf(bnafarDispensacaos.size()).concat(" Registros")));

            List<List<BnafarDispensacao>> gruposDispensacao = montaGrupoDispensacao(bnafarDispensacaos);
            for (List<BnafarDispensacao> dispensacaoGrupo : gruposDispensacao) {
                if (dispensacaoGrupo.size() > BnafarHelper.TAMANHO_MAXIMO_AGRUPADOR_DISPENSACAO)
                    gruposDispensacao.add(montaSubgrupo(dispensacaoGrupo));

                novoAgrupamento(dispensacaoGrupo.get(0));
                for (BnafarDispensacao bnafarDispensacao : dispensacaoGrupo) {
                    this.itemDispensacaoDtos.add(BnafarDispensacaoHelper.inicializarItemDispensacaoDTO(bnafarDispensacao));
                    Long codigoBnafarDispensacaoElo = inicializarBnafarDispensacaoElo(bnafarDispensacao);
                    BOFactory.getBO(MaterialBasicoFacade.class).alterarStatusBnafarDispensacao(bnafarDispensacao.getCodigo(), codigoBnafarDispensacaoElo);
                }
            }

            if (!bnafarDispensacaos.isEmpty()) {
                EnvioDispensacaoDTO envioDTO = getEnvioDTO();
                enviarFilaAWS(envioDTO.toJson());
                BOFactory.getBO(MaterialBasicoFacade.class).alterarBnafarDispensacaoIntegracao(PKBnafarDispensacaoIntegracao, envioDTO.toJson());
            }
            Loggable.log.info(MSG_ENVIO_SQS.concat(" FIM."));
        }
    }

    private List<List<BnafarDispensacao>> montaGrupoDispensacao(List<BnafarDispensacao> bnafarDispensacaos) {
        BnafarDispensacao proxy = on(BnafarDispensacao.class);

        return CollectionUtils.groupList(bnafarDispensacaos,
                path(proxy.getDispensacaoMedicamento().getEmpresa().getCodigo()),
                path(proxy.getDataDispensacao()),
                path(proxy.getDispensacaoMedicamento().getNumeroRegistro()),
                path(proxy.getProfissional().getCodigo()),
                path(proxy.getUsuarioCadsus().getCodigo()),
                path(proxy.getProfissional().getCodigo()),
                path(proxy.getUsuarioDispensador().getCodigo())
        );
    }

    private List<BnafarDispensacao> montaSubgrupo(List<BnafarDispensacao> dispensacaoGrupo) {
        List<BnafarDispensacao> dispensacaoSubGrupo = new ArrayList<>();
        for (int index = (BnafarHelper.TAMANHO_MAXIMO_AGRUPADOR_DISPENSACAO - 1); index < dispensacaoGrupo.size(); index++)
            dispensacaoSubGrupo.add(dispensacaoGrupo.remove(index));
        return dispensacaoSubGrupo;
    }

    private void enviarFilaAWS(String mensagem) throws DAOException, ValidacaoException {
        try {
            AmazonSQS amazonSQS = AwsUtils.getSQSClient();
            SendMessageResult result = amazonSQS.sendMessage(AwsUtils.BNAFAR_SQS_NAME, mensagem);
        } catch (Exception e) {
            List<BnafarDispensacaoElo> bnafarDispensacaoEloList = LoadManager.getInstance(BnafarDispensacaoElo.class)
                    .addProperties(new HQLProperties(BnafarDispensacaoElo.class).getProperties())
                    .addProperty(VOUtils.montarPath(BnafarDispensacaoElo.PROP_BNAFAR_DISPENSACAO, BnafarDispensacao.PROP_CODIGO))
                    .addParameter(new QueryCustom.QueryCustomParameter(BnafarDispensacaoElo.PROP_BNAFAR_DISPENSACAO_INTEGRACAO, PKBnafarDispensacaoIntegracao))
                    .start().getList();

            for (BnafarDispensacaoElo elo : bnafarDispensacaoEloList) {
                BOFactory.getBO(MaterialBasicoFacade.class).retornarStatusBnafarDispensacao(elo.getBnafarDispensacao().getCodigo());
                BOFactory.delete(elo);
            }

            BnafarDispensacaoIntegracao bnafarDispensacaoIntegracao = LoadManager.getInstance(BnafarDispensacaoIntegracao.class)
                    .addProperties(new HQLProperties(BnafarDispensacaoIntegracao.class).getProperties())
                    .setId(PKBnafarDispensacaoIntegracao).start().getVO();

            BOFactory.delete(bnafarDispensacaoIntegracao);
        }
    }

    private void novoAgrupamento(BnafarDispensacao bnafarDispensacao) throws DAOException, ValidacaoException {
        if (PKBnafarDispensacaoIntegracao != null) {
            EnvioDispensacaoDTO envioDTO = getEnvioDTO();
            enviarFilaAWS(envioDTO.toJson());
            BOFactory.getBO(MaterialBasicoFacade.class).alterarBnafarDispensacaoIntegracao(PKBnafarDispensacaoIntegracao, envioDTO.toJson());
        }

        inicializarEstabelecimentoDTO(bnafarDispensacao);
        inicializarCaracterizacaoDispensacaoDTO(bnafarDispensacao);
        inicializarUsuarioCadsusDTO(bnafarDispensacao);
        itemDispensacaoDtos = new ArrayList<>();

        PKBnafarDispensacaoIntegracao = inicializarBnafarDispensacaoIntegracao();
    }

    private Long inicializarBnafarDispensacaoIntegracao() throws DAOException, ValidacaoException {
        BnafarDispensacaoIntegracao bnafarDispensacaoIntegracao = new BnafarDispensacaoIntegracao();
        bnafarDispensacaoIntegracao.setJsonEnvio("");
        bnafarDispensacaoIntegracao.setDataEnvio(new Date());
        bnafarDispensacaoIntegracao.setUsuario(getSessao().getUsuario());
        BnafarDispensacaoIntegracao bnafarDispensacaoIntegracaoSaved = BOFactory.newTransactionSave(bnafarDispensacaoIntegracao);

        return bnafarDispensacaoIntegracaoSaved.getCodigo();

    }

    private Long inicializarBnafarDispensacaoElo(BnafarDispensacao bnafarDispensacao) throws DAOException, ValidacaoException {
        BnafarDispensacaoElo bnafarDispensacaoElo = new BnafarDispensacaoElo();
        bnafarDispensacaoElo.setBnafarDispensacao(bnafarDispensacao);
        bnafarDispensacaoElo.setBnafarDispensacaoIntegracao(new BnafarDispensacaoIntegracao(PKBnafarDispensacaoIntegracao));
        BnafarDispensacaoElo bnafarDispensacaoEloSaved = BOFactory.newTransactionSave(bnafarDispensacaoElo);

        return bnafarDispensacaoEloSaved.getCodigo();
    }

    private EnvioDispensacaoDTO getEnvioDTO() {
        EnvioDispensacaoDTO envioDispensacaoDTO = new EnvioDispensacaoDTO();
        envioDispensacaoDTO.setTenant(TenantContext.getRealContext());
        envioDispensacaoDTO.setCluster(RepositoryComponentDefault.SystemProperty.CLUSTER.value());
        envioDispensacaoDTO.setServico(BnafarHelper.SERVICO_DISPENSACAO);
        envioDispensacaoDTO.setLote(PKBnafarDispensacaoIntegracao);
        envioDispensacaoDTO.setConteudo(getDispensacaoDTO());

        return envioDispensacaoDTO;
    }

    private DispensacaoDto getDispensacaoDTO() {
        DispensacaoDto dispensacaoDto = new DispensacaoDto();
        dispensacaoDto.setCaracterizacao(caracterizacaoDispensacaoDto);
        dispensacaoDto.setEstabelecimentoDispensador(estabelecimentoDto);
        dispensacaoDto.setUsuarioSus(usuarioCadsusDispensacaoDto);
        dispensacaoDto.setItens(itemDispensacaoDtos);

        return dispensacaoDto;
    }

    private void inicializarEstabelecimentoDTO(BnafarDispensacao bnafarDispensacao) {
        estabelecimentoDto = new EstabelecimentoDispensacaoDto();

        /** Código do Cadastro Nacional de Estabelecimentos de Saúde que realizou a dispensação*/
        estabelecimentoDto.setCnes(bnafarDispensacao.getEmpresa().getCnes());
    }

    private void inicializarUsuarioCadsusDTO(BnafarDispensacao bnafarDispensacao) {
        usuarioCadsusDispensacaoDto = new UsuarioSusDispensacaoDto();

        /** Obrigatório informar um dos dois campos: {@code cns} ou {@code cpf}, Somente um desses campos deve ser informado*/
        boolean cpfInformado = bnafarDispensacao.getUsuarioCadsus().getCpf() != null && !bnafarDispensacao.getUsuarioCadsus().getCpf().trim().isEmpty();
        if (cpfInformado) {
            usuarioCadsusDispensacaoDto.setCpf(bnafarDispensacao.getUsuarioCadsus().getCpfSemPontos());
        }
        if (!cpfInformado) {
            usuarioCadsusDispensacaoDto.setCns(bnafarDispensacao.getUsuarioCadsus().getCnsSemPontos());
        }

        /** Campos obrigatório para os medicamentos do Componente Especializado da Assistência Farmacêutica*/
        usuarioCadsusDispensacaoDto.setAltura(bnafarDispensacao.getAlturaUsuarioCadsus());
        usuarioCadsusDispensacaoDto.setPeso(bnafarDispensacao.getPesoUsuarioCadsus());
    }

    private void inicializarCaracterizacaoDispensacaoDTO(BnafarDispensacao bnafarDispensacao) {
        caracterizacaoDispensacaoDto = new CaracterizacaoDispensacaoDto();
        caracterizacaoDispensacaoDto.setCodigoOrigem(String.valueOf(bnafarDispensacao.getCodigo()));
        caracterizacaoDispensacaoDto.setDataDispensacao(sdf.format(bnafarDispensacao.getDataDispensacao()));
    }

}
