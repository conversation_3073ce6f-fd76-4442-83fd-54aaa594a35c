package br.com.celk.bo.hospital.financeiro.contafinanceira;

import br.com.celk.bo.hospital.financeiro.interfaces.dto.CancelarContaFinanceiraDTO;
import br.com.celk.bo.hospital.financeiro.interfaces.dto.QueryConsultaContaFinanceiraDTOParam;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.hospital.financeiro.Adiantamentos;
import br.com.ksisolucoes.vo.hospital.financeiro.ContaFinanceira;
import br.com.ksisolucoes.vo.hospital.financeiro.ContaFinanceiraItem;
import java.util.Arrays;
import java.util.List;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class CancelarContaFinanceira extends AbstractCommandTransaction {
    
    private final CancelarContaFinanceiraDTO dto;
    
    public CancelarContaFinanceira(CancelarContaFinanceiraDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        ContaFinanceira contaFinanceira = consultarContaFinanceira();
        
        if(contaFinanceira != null){
            List<ContaFinanceiraItem> itemList = getSession().createCriteria(ContaFinanceiraItem.class)
                .add(Restrictions.eq(ContaFinanceiraItem.PROP_CONTA_FINANCEIRA, contaFinanceira))
                .list();
            
            // Descontar o valor do item no adiantamento
            for(ContaFinanceiraItem item : itemList){
                if(item.getAdiantamento() != null && item.getAdiantamento().getCodigo() != null){
                    Adiantamentos adiantamento = (Adiantamentos) getSession().get(Adiantamentos.class, item.getAdiantamento().getCodigo());

                    adiantamento.setValorPago(new Dinheiro(Coalesce.asDouble(item.getAdiantamento().getValorPago())).subtrair(Coalesce.asDouble(item.getValor())).doubleValue());
                    if(Adiantamentos.Situacao.FATURADO.value().equals(adiantamento.getStatus()) 
                            || Adiantamentos.Situacao.FECHADO.value().equals(adiantamento.getStatus())){
                        adiantamento.setStatus(Adiantamentos.Situacao.ABERTO.value());
                    }

                    BOFactory.save(adiantamento);
                }
                
                item.setStatus(ContaFinanceiraItem.Status.CANCELADO.value());
                BOFactory.save(item);
            }
            
            if(contaFinanceira.getContaPaciente() != null && contaFinanceira.getContaPaciente().getCodigo() == null){
                contaFinanceira.setContaPaciente(null);
            }
            contaFinanceira.setStatus(ContaFinanceira.Status.CANCELADO.value());
            BOFactory.save(contaFinanceira);
        }
    }
    
    private ContaFinanceira consultarContaFinanceira() throws DAOException, ValidacaoException{
        QueryConsultaContaFinanceiraDTOParam param = new QueryConsultaContaFinanceiraDTOParam();
        param.setAtendimentoPrincipal(dto.getAtendimentoPrincipal());
        param.setUsuarioCadsus(dto.getUsuarioCadsus());
        param.setStatusList(Arrays.asList(ContaFinanceira.Status.ABERTO.value(), ContaFinanceira.Status.FATURADO.value()));
        
        return BOFactory.getBO(HospitalFacade.class).consultarContaFinanceira(param);
    }
}