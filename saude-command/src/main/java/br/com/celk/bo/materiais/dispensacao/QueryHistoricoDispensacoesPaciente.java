package br.com.celk.bo.materiais.dispensacao;

import br.com.celk.materiais.dispensacao.dto.HistoricoDispensacoesPacienteDTO;
import br.com.celk.materiais.dispensacao.dto.HistoricoDispensacoesPacienteDTOParam;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.parametrogem.IParameterModuleContainer;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItemHelper;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.hibernate.type.*;

import java.util.Date;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class QueryHistoricoDispensacoesPaciente extends AbstractCommandTransaction {

    private Usuario usuario;
    private HistoricoDispensacoesPacienteDTOParam param;

    private List<HistoricoDispensacoesPacienteDTO> result;

    public QueryHistoricoDispensacoesPaciente(HistoricoDispensacoesPacienteDTOParam param) {
        this.param = param;
        this.usuario = SessaoAplicacaoImp.getInstance().<Usuario>getUsuario();
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        SQLQuery sqlQuery = getSQLQuery();

        setScalar(sqlQuery);
        setParameters(sqlQuery);
        sqlQuery.setResultTransformer(Transformers.aliasToBean(HistoricoDispensacoesPacienteDTO.class));

        this.result = sqlQuery.list();

        if (CollectionUtils.isNotNullEmpty(this.result)) {
            calcularDataProximaDispensacao();
        }
    }

    private void calcularDataProximaDispensacao() throws DAOException {
        IParameterModuleContainer materiais = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS);

        Long dataProximaDispensacaoMenosDiasTolerancia = materiais.getParametro("dataProximaDispensacaoMenosDiasTolerancia");
        Long diasToleranciaParaPermissao = materiais.getParametro("diasToleranciaParaDispensacao");

        for (HistoricoDispensacoesPacienteDTO dto : this.result) {
            Date dataProximaDispensacaoCalculada = DispensacaoMedicamentoItemHelper.calculaDataProximaDispensacao(
                    dto.getSubgrupo(),
                    dto.getDataDispensacao(),
                    dto.getDataProximaDispensacao(),
                    dto.getDataUltimaDispensacao(),
                    dataProximaDispensacaoMenosDiasTolerancia, diasToleranciaParaPermissao
            );

            dto.setDataProximaDispensacaoCalculada(dataProximaDispensacaoCalculada);
        }
    }

    private void setParameters(SQLQuery sqlQuery) {
        sqlQuery.setParameter("codigoUsuarioCadsus", param.getUsuarioCadsus().getCodigo(), LongType.INSTANCE);
        sqlQuery.setParameter("statusSemEstoque", DispensacaoMedicamentoItem.STATUS_SEM_ESTOQUE, LongType.INSTANCE);

        if (param.getProduto() != null) {
            sqlQuery.setParameter("codigoProduto", param.getProduto().getCodigo());
        }

        if (!usuario.isNivelAdminOrMaster()) {
            if (CollectionUtils.isNotNullEmpty(usuario.getEmpresasUsuario())) {
                sqlQuery.setParameter("naoLong", RepositoryComponentDefault.NAO_LONG);

                sqlQuery.setParameterList("empresasUsuario", usuario.getEmpresasUsuario());
            } else if (param.getEstabelecimentoExecutante() != null) {
                sqlQuery.setParameter("codigoEstabelecimentoExecutante", param.getEstabelecimentoExecutante().getCodigo());
            }
        }
    }

    private void setScalar(SQLQuery sqlQuery) {
        HistoricoDispensacoesPacienteDTO proxy = on(HistoricoDispensacoesPacienteDTO.class);

        sqlQuery.addScalar(path(proxy.getTipoUso()), LongType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getPosologia()), DoubleType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getQuantidadeDispensada()), DoubleType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getQuantidadePrescrita()), DoubleType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getQuantidadeDevolvida()), DoubleType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getDataProximaDispensacao()), DateType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getDescricaoProduto()), StringType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getFlagDispensacaoEspecial()), StringType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getFlagEmiteLme()), StringType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getCodigoUnidadeProduto()), LongType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getUnidadeProduto()), StringType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getCodigoSubgrupo()), LongType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getCodigoGrupoProduto()), LongType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getFlagMedicamento()), StringType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getReceita()), StringType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getDataReceita()), DateType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getDataDispensacao()), TimestampType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getNomeUsuarioOrigem()), StringType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getDescricaoEmpresa()), StringType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getDescricaoEmpresaOrigem()), StringType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getNomeUsuario()), StringType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getDescricaoTipoReceita()), StringType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getNomeProfissional()), StringType.INSTANCE);

    }

    private SQLQuery getSQLQuery() throws ValidacaoException, DAOException {

        boolean apenasUltimasDispensacoes = RepositoryComponentDefault.NAO.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("exibirTodoHistoricoDispensacaoPaciente"));

        String query = getQueryHistoricoDispensacoes(apenasUltimasDispensacoes);

        SQLQuery sqlQuery = getSession().createSQLQuery(query);

        return sqlQuery;
    }

    private String getQueryHistoricoDispensacoes(boolean apenasUltimasDispensacoes) throws ValidacaoException, DAOException {
        HistoricoDispensacoesPacienteDTO proxy = on(HistoricoDispensacoesPacienteDTO.class);

        HQLHelper helper = new HQLHelper();
        helper.setUseSQL(true);
        helper.setTypeSelect(HistoricoDispensacoesPacienteDTO.class.getName());

        helper.addToSelect("dmi.tp_uso", path(proxy.getTipoUso()));
        helper.addToSelect("dmi.posologia", path(proxy.getPosologia()));
        helper.addToSelect("dmi.quantidade_dispensada", path(proxy.getQuantidadeDispensada()));
        helper.addToSelect("dmi.quantidade_prescrita", path(proxy.getQuantidadePrescrita()));
        helper.addToSelect("dmi.quantidade_devolvida", path(proxy.getQuantidadeDevolvida()));
        helper.addToSelect("dmi.dt_ultima_dispensacao", path(proxy.getDataUltimaDispensacao()));
        helper.addToSelect("dmi.dt_prox_dispensacao", path(proxy.getDataProximaDispensacao()));
        helper.addToSelect("prod.descricao", path(proxy.getDescricaoProduto()));
        helper.addToSelect("prod.flag_disp_esp", path(proxy.getFlagDispensacaoEspecial()));
        helper.addToSelect("(CASE WHEN prod.flag_emite_lme = 1 THEN 'Sim' ELSE 'Não' END)", path(proxy.getFlagEmiteLme()));
        helper.addToSelect("un.cod_uni", path(proxy.getCodigoUnidadeProduto()));
        helper.addToSelect("un.unidade", path(proxy.getUnidadeProduto()));
        helper.addToSelect("subg.cod_sub", path(proxy.getCodigoSubgrupo()));
        helper.addToSelect("subg.cod_gru", path(proxy.getCodigoGrupoProduto()));
        helper.addToSelect("subg.flag_medicamento", path(proxy.getFlagMedicamento()));
        helper.addToSelect("dm.nr_receita", path(proxy.getReceita()));
        helper.addToSelect("dm.dt_receita", path(proxy.getDataReceita()));
        helper.addToSelect("dm.dt_dispensacao", path(proxy.getDataDispensacao()));
        helper.addToSelect("dm.nm_usuario_origem", path(proxy.getNomeUsuarioOrigem()));
        helper.addToSelect("emp.descricao", path(proxy.getDescricaoEmpresa()));
        helper.addToSelect("empOrigem.descricao", path(proxy.getDescricaoEmpresaOrigem()));
        helper.addToSelect("u.nm_usuario", path(proxy.getNomeUsuario()));
        helper.addToSelect("tr.nm_receita", path(proxy.getDescricaoTipoReceita()));
        helper.addToSelect("COALESCE(pro.nm_profissional, dm.nm_profissional, psv.nm_profissional)", path(proxy.getNomeProfissional()));

        if (apenasUltimasDispensacoes) {
            helper.addToFrom("temp_produtos temp"
                + " LEFT JOIN dispensacao_medicamento_item dmi ON dmi.cd_dis_med_item = temp.cd_dis_med_item"
                + " LEFT JOIN dispensacao_medicamento dm   ON dmi.nr_dispensacao=dm.nr_dispensacao"
                + " LEFT JOIN empresa emp                  ON dm.empresa=emp.empresa"
                + " LEFT JOIN empresa empOrigem            ON dm.empresa_origem=empOrigem.empresa"
                + " LEFT JOIN profissional pro             ON dm.cd_profissional=pro.cd_profissional"
                + " LEFT JOIN profissional_sem_vinculo psv ON dm.cd_profissional_sem_vinculo=psv.cd_profissional_sem_vinculo"
                + " LEFT JOIN produtos prod                ON dmi.cod_pro=prod.cod_pro"
                + " LEFT JOIN unidade un                   ON prod.cod_uni=un.cod_uni"
                + " LEFT JOIN subgrupo subg                ON prod.cod_sub=subg.cod_sub and prod.cod_gru=subg.cod_gru"
                + " LEFT JOIN usuarios u                   ON dm.cd_usuario=u.cd_usuario"
                + " LEFT JOIN tipo_receita tr              ON dm.cd_receita=tr.cd_receita"
            );
        
        }else{
            helper.addToFrom("dispensacao_medicamento_item dmi"
                + " JOIN dispensacao_medicamento dm        ON dmi.nr_dispensacao=dm.nr_dispensacao"
                + " LEFT JOIN empresa emp                  ON dm.empresa=emp.empresa"
                + " LEFT JOIN empresa empOrigem            ON dm.empresa_origem=empOrigem.empresa"
                + " LEFT JOIN profissional pro             ON dm.cd_profissional=pro.cd_profissional"
                + " LEFT JOIN profissional_sem_vinculo psv ON dm.cd_profissional_sem_vinculo=psv.cd_profissional_sem_vinculo"
                + " LEFT JOIN produtos prod                ON dmi.cod_pro=prod.cod_pro"
                + " LEFT JOIN unidade un                   ON prod.cod_uni=un.cod_uni"
                + " LEFT JOIN subgrupo subg                ON prod.cod_sub=subg.cod_sub and prod.cod_gru=subg.cod_gru"
                + " LEFT JOIN usuarios u                   ON dm.cd_usuario=u.cd_usuario"
                + " LEFT JOIN tipo_receita tr              ON dm.cd_receita=tr.cd_receita"
            );
        }

        if (!usuario.isNivelAdminOrMaster()) {
            if (CollectionUtils.isEmpty(usuario.getEmpresasUsuario())) {
                usuario.setEmpresasUsuario(BOFactory.getBO(UsuarioFacade.class).getEmpresasUsuario(usuario));
            }

            if (CollectionUtils.isNotNullEmpty(usuario.getEmpresasUsuario())) {
                helper.addToWhereWhithAnd("(emp.acesso_restrito = :naoLong OR emp.empresa IN (:empresasUsuario))");
            } else if (param.getEstabelecimentoExecutante() != null) {
                helper.addToWhereWhithAnd("emp.empresa = :codigoEstabelecimentoExecutante");
            }
        }

        helper.addToWhereWhithAnd("dmi.quantidade_dispensada > 0");
        helper.addToWhereWhithAnd("dmi.status <> :statusSemEstoque");

        helper.addToOrder("dm.dt_dispensacao desc");
        helper.addToOrder("dmi.dt_prox_dispensacao asc");
        helper.addToOrder("dm.nr_dispensacao asc");
        helper.addToOrder("dm.empresa asc");
        helper.addToOrder("dmi.item asc");

        String query;
        if (apenasUltimasDispensacoes) {

            query = new StringBuilder()
                        .append("with ")
                            .append("temp_usuario  as (").append(getTempQueryUsuario()).append("),")
                            .append("temp_produtos as (").append(getTempQueryProduto()).append(")")
                            .append("                 (").append(helper.getQuery()).append(")")
                    .toString();
        } else {
            helper.addToWhereWhithAnd("dm.cd_usu_cadsus_destino = :codigoUsuarioCadsus");

            if (param.getProduto() != null) {
                helper.addToWhereWhithAnd("prod.cod_pro = :codigoProduto");
            }

            query = helper.getQuery();
        }

        query = StringUtils.normalizeSpace(query);

        return query;
    }

    private String getTempQueryUsuario() {
        StringBuilder query = new StringBuilder();

        query.append("SELECT dmi.cd_dis_med_item, dmi.cod_pro, dm.dt_dispensacao ");
        query.append("FROM dispensacao_medicamento_item dmi, dispensacao_medicamento dm ");
        query.append("WHERE dmi.nr_dispensacao = dm.nr_dispensacao AND dm.cd_usu_cadsus_destino = :codigoUsuarioCadsus");

        return query.toString();
    }

    private String getTempQueryProduto() {
        StringBuilder query = new StringBuilder();

        query.append("SELECT max(tmp.cd_dis_med_item) as cd_dis_med_item ");
        query.append("FROM temp_usuario tmp ");
        query.append("WHERE tmp.dt_dispensacao = (select max(tmp2.dt_dispensacao) from temp_usuario tmp2 where tmp2.cod_pro = tmp.cod_pro) ");

        if (param.getProduto() != null) {
            query.append("AND tmp.cod_pro = :codigoProduto ");
        }

        query.append("GROUP BY tmp.cod_pro");

        return query.toString();
    }

    public List<HistoricoDispensacoesPacienteDTO> getResult() {
        return result;
    }

}
