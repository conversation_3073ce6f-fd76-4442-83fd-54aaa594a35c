package br.com.celk.bo.agenda.manutencaoagenda;

import br.com.celk.bo.agenda.interfaces.dto.CadastroManutencaoAgendaDiarioDTO;
import br.com.celk.bo.agenda.interfaces.dto.ManutencaoAgendaDiarioDTO;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento;

/**
 *
 * <AUTHOR>
 */
public class BloquearVagasManutencaoAgendaDiario extends AbstractCommandTransaction{

    private CadastroManutencaoAgendaDiarioDTO dto;

    public BloquearVagasManutencaoAgendaDiario(CadastroManutencaoAgendaDiarioDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if(CollectionUtils.isNotNullEmpty(dto.getHorariosSelecionadosDTOList())){
            
            AgendaGradeAtendimento aga;
            String descricaoOcorrencia;
            
            Long vagasBloqueadas;
            for(ManutencaoAgendaDiarioDTO madDTO: dto.getHorariosSelecionadosDTOList()){
                if(dto.getVagas().equals(Coalesce.asLong(madDTO.getVagasDisponiveis())) || dto.getVagas() < (Coalesce.asLong(madDTO.getVagasDisponiveis()))){
                    vagasBloqueadas = dto.getVagas();
                } else {
                    vagasBloqueadas = Coalesce.asLong(madDTO.getVagasDisponiveis());
                }
                
                descricaoOcorrencia = Bundle.getStringApplication("rotulo_vagas_bloqueadas_X_data_X", vagasBloqueadas, madDTO.getAgendaGradeAtendimento().getAgendaGrade().getDescricaoDataHoraInicial());
                
                aga = madDTO.getAgendaGradeAtendimento();
                aga.setQuantidadeAtendimentoBloqueado(new Dinheiro(aga.getQuantidadeAtendimentoBloqueado()).somar(vagasBloqueadas.doubleValue()).longValue());
                aga.setQuantidadeAtendimento(new Dinheiro(aga.getQuantidadeAtendimento()).subtrair(vagasBloqueadas.doubleValue()).longValue());
                BOFactory.save(aga);
                
                BOFactory.getBO(AgendamentoFacade.class).gerarAgendaOcorrencia(dto.getMotivo(), dto.getTipoOcorrencia(), dto.getCodigoAgenda(), descricaoOcorrencia);
            }
        }
    }
    
}
