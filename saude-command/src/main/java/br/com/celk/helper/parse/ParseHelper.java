package br.com.celk.helper.parse;

import br.com.celk.services.mobile.integracao.bindimportacao.atividadeGrupo.AtividadeGrupoBind;
import br.com.ksisolucoes.bo.atividadegrupo.interfaces.dto.GestaoAtividadeGrupoDTO;

import java.text.ParseException;
import java.util.ArrayList;

public class ParseHelper {
    private static ArrayList<ParserMobile> parsers;
    public static  void parseAtividadeGrupo(AtividadeGrupoBind atividadeGrupoBind, GestaoAtividadeGrupoDTO gestaoAtividadeGrupoDTO) throws ParseException {
        for (ParserMobile parser: getParsers(atividadeGrupoBind, gestaoAtividadeGrupoDTO)) {
            parser.parse(atividadeGrupoBind, gestaoAtividadeGrupoDTO);
        }
    }

    public static ArrayList<ParserMobile> getParsers(AtividadeGrupoBind atividadeGrupoBind, GestaoAtividadeGrupoDTO gestaoAtividadeGrupoDTO){
        if(parsers == null){
            parsers = new ArrayList<>();
            parsers.add(new AtividadeGrupoProfissionalParser());
            parsers.add(new AtividadeGrupoPacienteParser());
            parsers.add(new AtividadeEloPublicoParser());
            parsers.add(new AtividadeEloTemaParser());
            parsers.add(new AtividadeEloPraticaParser());
        }
        return parsers;
    }

}
