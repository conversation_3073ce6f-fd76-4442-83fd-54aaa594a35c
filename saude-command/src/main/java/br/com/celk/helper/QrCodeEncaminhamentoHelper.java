package br.com.celk.helper;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.util.EncryptorUtils;
import br.com.ksisolucoes.util.QrCodeUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento;

import javax.ws.rs.core.UriBuilder;

import java.util.Objects;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class QrCodeEncaminhamentoHelper {

    private QrCodeEncaminhamentoHelper() {}

    public static String getURL(Long codigoEncaminhamento) {
        UriBuilder uriBuilder = UriBuilder.fromPath("http://" + QrCodeUtils.getHost() + "/validaQRCodeEncaminhamento")
                .queryParam("codigoEncaminhamento", EncryptorUtils.encrypt(codigoEncaminhamento.toString()));
        return uriBuilder.build().toString();
    }

    public static boolean shouldPrintQrCode(Long codigoEncaminhamento) {
        if (Objects.isNull(codigoEncaminhamento)) return false;

        Encaminhamento encaminhamento = getFlagQrCodeEncaminhamento(codigoEncaminhamento);
        return !Objects.isNull(encaminhamento)
                && !Objects.isNull(encaminhamento.getTipoEncaminhamento())
                && RepositoryComponentDefault.SIM_LONG.equals(encaminhamento.getTipoEncaminhamento().getFlagQRCode());
    }

    public static Encaminhamento getFlagQrCodeEncaminhamento(Long codigoEncaminhamento) {
        Encaminhamento encaminhamentoProxy = on(Encaminhamento.class);
        return LoadManager.getInstance(Encaminhamento.class)
                .addProperty(path(encaminhamentoProxy.getCodigo()))
                .addProperty(path(encaminhamentoProxy.getTipoEncaminhamento().getCodigo()))
                .addProperty(path(encaminhamentoProxy.getTipoEncaminhamento().getFlagQRCode()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(encaminhamentoProxy.getCodigo()), codigoEncaminhamento))
                .start()
                .getVO();
    }
}
