package br.com.celk.helper.parse;

import br.com.celk.services.mobile.integracao.bindimportacao.atividadeGrupo.AtividadeGrupoBind;
import br.com.ksisolucoes.bo.atividadegrupo.interfaces.dto.GestaoAtividadeGrupoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.server.HibernateSessionFactory;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.util.validacao.exception.DAORuntimeException;
import br.com.ksisolucoes.vo.esus.AtividadeEloTema;
import br.com.ksisolucoes.vo.esus.TipoAtividadePublico;
import br.com.ksisolucoes.vo.esus.TipoAtividadeTema;
import org.hibernate.Session;

import java.util.ArrayList;

public class AtividadeEloTemaParser implements ParserMobile {

    private ArrayList<AtividadeEloTema> atividadeEloTemaList;
    private Session session;

    @Override
    public void parse(AtividadeGrupoBind atividadeGrupoBind, GestaoAtividadeGrupoDTO gestaoAtividadeGrupoDTO) {
        if(!atividadeGrupoBind.getLstAtividadeEloTema().isEmpty()) {
            atividadeEloTemaList = new ArrayList<>();
            String[] sAtividadeEloTemaList = atividadeGrupoBind.getLstAtividadeEloTema().split("\\$");
            for (int i = 0; i < sAtividadeEloTemaList.length; i++) {
                AtividadeEloTema atividadeEloTema = new AtividadeEloTema();
                String[] sAtividadeEloTema = sAtividadeEloTemaList[i].split("%");

                Long codTipoAtividadeTema = ParseLong.parseLong(sAtividadeEloTema[0]);
                TipoAtividadeTema tipoAtividadeTema = (TipoAtividadeTema) getSession().createCriteria(TipoAtividadeTema.class).add(Restrictions.eq(TipoAtividadeTema.PROP_CODIGO, codTipoAtividadeTema)).uniqueResult();
                atividadeEloTema.setTipoAtividadeTema(tipoAtividadeTema);
                atividadeEloTemaList.add(atividadeEloTema);
            }
            gestaoAtividadeGrupoDTO.setLstAtividadeEloTema(atividadeEloTemaList);
        }
    }
    private Session getSession(){
        try{
            if(session == null)
                session = HibernateSessionFactory.getSession();
            return session;
        }catch(DAOException ex){
            throw new DAORuntimeException(ex);
        }
    }
}
