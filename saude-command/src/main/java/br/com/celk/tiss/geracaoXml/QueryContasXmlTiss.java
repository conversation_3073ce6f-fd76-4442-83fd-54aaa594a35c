package br.com.celk.tiss.geracaoXml;

import br.com.celk.tiss.dto.GeracaoXmlTissDTO;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import ch.lambdaj.Lambda;
import java.util.ArrayList;
import java.util.List;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class QueryContasXmlTiss extends CommandQuery {

    private final GeracaoXmlTissDTO dto;
    private List<QueryContasXmlTissDTO> itens;

    public QueryContasXmlTiss(GeracaoXmlTissDTO dto) {
        this.dto = dto;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        List<Long> codigosContas = Lambda.extract(dto.getContas(), Lambda.on(ContaPaciente.class).getCodigo());

        hql.setTypeSelect(QueryContasXmlTissDTO.class.getName());

        hql.addToSelect("view.codigoItemContaPaciente", "codigoItemContaPaciente");
        hql.addToSelect("view.codigoContaPaciente", "codigoConta");
        hql.addToSelect("view.tipoRegistro", "tipoRegistro");

        hql.addToSelect("view.numeroGuiaPrincipal", "numeroGuiaPrincipal");
        hql.addToSelect("view.dataAutorizacao", "dataAutorizacao");
        hql.addToSelect("view.senhaAutorizacao", "senha");
        hql.addToSelect("view.validadeSenha", "dataValidadeSenha");
        hql.addToSelect("view.nomePaciente", "nomePaciente");
        hql.addToSelect("view.cnsPaciente", "cnsPaciente");

        hql.addToSelect("view.nomeProfissional", "nomeProfissional");
        hql.addToSelect("view.conselhoClasseProfissional", "conselhoClasseProfissional");
        hql.addToSelect("view.numeroRegistroProfissional", "numeroConselhoProfissional");
        hql.addToSelect("view.cboProfissional", "cboProfissional");
        hql.addToSelect("view.caraterAtendimentoTiss", "caraterAtendimento");
        hql.addToSelect("view.tipoAtendimentoTiss", "tipoAtendimento");
        hql.addToSelect("view.numeroGuiaPrestador", "numeroGuiaPrestador");
        hql.addToSelect("view.carteiraBeneficiario", "carteiraBeneficiario");
        hql.addToSelect("view.ufConselho", "ufConselho");
        hql.addToSelect("view.dataSolicitacao", "dataSolicitacao");
        hql.addToSelect("view.tipoConsulta", "tipoConsulta");
        hql.addToSelect("view.motivoEncerramento", "motivoEncerramento");
        hql.addToSelect("view.codigoPrestadorSolicitanteNaOperadora", "codigoPrestadorSolicitanteNaOperadora");
        hql.addToSelect("view.nomeContratadoSolicitante", "nomeContratadoSolicitante");
        hql.addToSelect("view.codigoPrestadorExecutanteNaOperadora", "codigoPrestadorExecutanteNaOperadora");
        hql.addToSelect("view.nomeContratadoExecutante", "nomeContratadoExecutante");
        hql.addToSelect("view.cnesContratadoExecutante", "cnesContratadoExecutante");
        hql.addToSelect("view.numeroGuiaOperadora", "numeroGuiaOperadora");
        hql.addToSelect("view.dataInicialExecucaoProcedimento", "dataInicialExecucaoProcedimento");
        hql.addToSelect("view.codigoProcedimento", "codigoProcedimento");
        hql.addToSelect("view.codigoProcedimentoRealizado", "codigoProcedimentoRealizado");
        hql.addToSelect("view.descricaoProcedimentoRealizado", "descricaoProcedimentoRealizado");
        hql.addToSelect("view.quantidadeProcedimento", "quantidadeProcedimento");
        hql.addToSelect("view.valorUnitarioProcedimento", "valorUnitarioProcedimento");
        hql.addToSelect("view.valorTotalProcedimento", "valorTotalProcedimento");
        hql.addToSelect("view.dataInicialDespesa", "dataInicialDespesa");
        hql.addToSelect("view.codigoProcedimentoDespesa", "codigoProcedimentoDespesa");
        hql.addToSelect("view.quantidadeExecutadaDespesa", "quantidadeExecutadaDespesa");
        hql.addToSelect("view.unidadeMedidaDespesa", "unidadeMedidaDespesa");
        hql.addToSelect("view.reducaoAcrescimoDespesa", "reducaoAcrescimoDespesa");
        hql.addToSelect("view.valorUnitarioDespesa", "valorUnitarioDespesa");
        hql.addToSelect("view.valorTotalDespesa", "valorTotalDespesa");
        hql.addToSelect("view.descricaoProcedimentoDespesa", "descricaoProcedimentoDespesa");
        hql.addToSelect("view.numeroGuiaSolicitacaoInternacao", "numeroGuiaSolicitacaoInternacao");
        hql.addToSelect("view.tipoFaturamento", "tipoFaturamento");
        hql.addToSelect("view.dataInicioFaturamento", "dataInicioFaturamento");
        hql.addToSelect("view.horaInicioFaturamento", "horaInicioFaturamento");
        hql.addToSelect("view.dataFinalFaturamento", "dataFinalFaturamento");
        hql.addToSelect("view.horaFinalFaturamento", "horaFinalFaturamento");
        hql.addToSelect("view.tipoInternacao", "tipoInternacao");
        hql.addToSelect("view.regimeInternacao", "regimeInternacao");
        hql.addToSelect("view.indicadorAcidente", "indicadorAcidente");
        hql.addToSelect("view.diagnosticoPrincipal", "diagnosticoPrincipal");
        hql.addToSelect("view.numeroGuiaPrestadorHonorario", "numeroGuiaPrestadorHonorario");
        hql.addToSelect("view.nomePacienteHonorario", "nomePacienteHonorario");
        hql.addToSelect("view.carteiraBeneficiarioHonorario", "carteiraBeneficiarioHonorario");
        hql.addToSelect("view.cnesContratadoExecutanteHonorario", "cnesContratadoExecutanteHonorario");
        hql.addToSelect("view.codigoPrestadorExecutanteOperadoraHonorario", "codigoPrestadorExecutanteNaOperadoraHonorario");
        hql.addToSelect("view.nomeContratadoExecutanteHonorario", "nomeContratadoExecutanteHonorario");
        hql.addToSelect("view.dataInicioFaturamentoHonorario", "dataInicioFaturamentoHonorario");
        hql.addToSelect("view.dataFinalFaturamentoHonorario", "dataFinalFaturamentoHonorario");
        hql.addToSelect("view.dataEmissaoGuiaHonorario", "dataEmissaoGuiaHonorario");
        hql.addToSelect("view.guiaSolicitacaoInternacaoHonorario", "guiaSolicitacaoInternacaoHonorario");
        hql.addToSelect("view.numeroGuiaOperadoraHonorario", "numeroGuiaOperadoraHonorario");
        hql.addToSelect("view.senhaHonorario", "senhaHonorario");
        hql.addToSelect("view.codigoDespesa", "codigoDespesa");
        hql.addToSelect("view.tabelaReferenciaItemDespesa", "tabelaReferenciaItemDespesa");
        hql.addToSelect("view.tipoItemContaPaciente", "tipoItemContaPaciente");
        hql.addToSelect("view.tipoDespesa", "tipoDespesa");
        hql.addToSelect("view.flagMedicamento", "flagMedicamento");
        hql.addToSelect("view.statusItemContaPaciente", "statusItemContaPaciente");
        hql.addToSelect("view.statusContaPaciente", "statusContaPaciente");
        hql.addToSelect("view.tipoLayoutTiss", "tipoLayoutTiss");
        hql.addToSelect("view.viaAcesso", "viaAcesso");
        hql.addToSelect("view.tecnicaUtilizada", "tecnicaUtilizada");
        hql.addToSelect("view.horaInicialRealizado", "horaInicialRealizado");
        hql.addToSelect("view.horaFinalRealizado", "horaFinalRealizado");
        hql.addToSelect("view.totalGasMedicinal", "totalGasMedicinal");
        hql.addToSelect("view.totalMedicamento", "totalMedicamento");
        hql.addToSelect("view.totalDiaria", "totalDiaria");
        hql.addToSelect("view.totalTaxaAluguel", "totalTaxaAluguel");
        hql.addToSelect("view.totalOpme", "totalOpme");
        hql.addToSelect("view.totalMaterial", "totalMaterial");
        hql.addToSelect("view.valorTotalHonorario", "totalHonorario");
        hql.addToSelect("view.totalProcedimento", "totalProcedimento");
        hql.addToSelect("view.atendimentoRn", "atendimentoRN");
        hql.addToSelect("view.declaracaoObito", "declaracaoObito");
        hql.addToSelect("view.indicadorObitoRn", "indicadorObitoRn");
        hql.addToSelect("view.declaracaoNascido", "declaracaoNascido");

        hql.addToFrom("ViewContasTiss view");

        hql.addToWhereWhithAnd("view.codigoContaPaciente in", codigosContas);
        hql.addToWhereWhithAnd("view.statusItemContaPaciente =", ItemContaPaciente.Status.CONFIRMADO.value());
        hql.addToWhereWhithAnd("view.statusContaPaciente =", ContaPaciente.Status.FECHADA.value());
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        List<QueryContasXmlTissDTO> contas = new ArrayList<QueryContasXmlTissDTO>();

        List<List<QueryContasXmlTissDTO>> viewList = CollectionUtils.groupList(itens, "codigoConta");
        for (List<QueryContasXmlTissDTO> contasList : viewList) {
            QueryContasXmlTissDTO conta = contasList.get(0);
            contas.add(conta);

            List<List<QueryContasXmlTissDTO>> subContas = CollectionUtils.groupList(contasList, "tipoRegistro");
            for (List<QueryContasXmlTissDTO> subLis : subContas) {
                QueryContasXmlTissDTO sub = subLis.get(0);

                if (QueryContasXmlTissDTO.TipoRegistro.OUTRAS_DESPESAS.value().equals(sub.getTipoRegistro())) {
                    conta.setDespesas(subLis);
                } else if (QueryContasXmlTissDTO.TipoRegistro.PROCEDIMENTOS.value().equals(sub.getTipoRegistro())) {
                    for (QueryContasXmlTissDTO item : subLis) {
                        item.setProfissionaisExecutantes(getProfissionaisExecutantes(item.getCodigoItemContaPaciente()));
                    }

                    conta.setProcedimentos(subLis);
                } else if (QueryContasXmlTissDTO.TipoRegistro.HONORARIOS.value().equals(sub.getTipoRegistro())) {
                    for (QueryContasXmlTissDTO item : subLis) {
                        item.setProfissionaisExecutantes(getProfissionaisExecutantes(item.getCodigoItemContaPaciente()));
                    }

                    conta.setProcedimentosHonorarios(subLis);
                }
            }
        }

        itens = contas;
    }

    private List<QueryContasXmlTissDTO> getProfissionaisExecutantes(Long codigoItemContaPaciente) throws DAOException, ValidacaoException {
        QueryProfissionaisExecutantesXmlTiss queryProfissionais = new QueryProfissionaisExecutantesXmlTiss(codigoItemContaPaciente);
        queryProfissionais.start();
        return queryProfissionais.getResult();
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        itens = hql.getBeanList((List) result);
    }

    public List<QueryContasXmlTissDTO> getItens() {
        return itens;
    }
}
