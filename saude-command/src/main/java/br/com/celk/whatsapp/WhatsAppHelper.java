package br.com.celk.whatsapp;

import br.com.celk.bo.service.sms.interfaces.SubstituirVariaveisMensagemSMSFactory;
import br.com.celk.bo.service.sms.interfaces.facade.ComprovanteGradeAtendimentoHorario;
import br.com.celk.sms.restservice.response.MensagemSmsDTO;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.frota.RoteiroViagemPassageiro;
import br.com.ksisolucoes.vo.service.sms.SmsControleIntegracao;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class WhatsAppHelper {
    private static final String ENDPOINT_ENVIAR = "/send-message";
    private static final String ENDPOINT_RECEBER_RESPOSTA = "/message-answer";
    private static final String ENDPOINT_STATUS_MENSAGEM = "/message-status";
    public static final String MENSAGEM_PADRAO_WHATSAPP = "Mensagem padrão WhatsApp para ";


    public static void logarProcessoWhatsApp(TipoMensagemWhatsApp tipoMensagemWhatsApp, int quantidadeMensagem) throws DAOException {
        if (isPlataformaWhatsApp()) {
            Loggable.log.info("WhatsApp - Processando o envio de mensagens de ".concat(tipoMensagemWhatsApp.descricao()).concat(" - ").concat(String.valueOf(quantidadeMensagem).concat(" Mensagens.")));
        }
    }

    public static boolean isPlataformaWhatsApp() throws DAOException {
        Long plataformaMensagens = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("plataformaEnvioMensagens");
        return SmsControleIntegracao.PlataformaMensagem.WHATSAPP.value().equals(plataformaMensagens);
    }

    private WhatsAppHelper() {
    }

    public final String CONTATO_SUPORTE = getContatoSuporte();
    public static final List MENSAGEM_PADRAO_WHATSAPP_LIST = Arrays.asList(
            MENSAGEM_PADRAO_WHATSAPP.concat(TipoMensagemWhatsAppEnum.ENVIO.descricao().concat(".")),
            MENSAGEM_PADRAO_WHATSAPP.concat(TipoMensagemWhatsAppEnum.CONFIRMACAO.descricao().concat(".")),
            MENSAGEM_PADRAO_WHATSAPP.concat(TipoMensagemWhatsAppEnum.REAGENDAMENTO.descricao().concat(".")),
            MENSAGEM_PADRAO_WHATSAPP.concat(TipoMensagemWhatsAppEnum.CANCELAMENTO.descricao().concat("."))
    );

    private static String getContatoSuporte() {
        try {
            return BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("contatoPadraoSuporteWhatsApp");
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage(), e);
        }
        return null;
    }

    public static boolean isPlataformaTwilio() throws DAOException {
        Long plataformaMensagens = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("plataformaEnvioMensagens");
        return SmsControleIntegracao.PlataformaMensagem.TWILIO.value().equals(plataformaMensagens);
    }

    public enum TipoMensagemWhatsApp {
        ENVIO("envio"),
        CONFIRMACAO("confirmacao"),
        REAGENDAMENTO("reagendamento"),
        CANCELAMENTO("cancelamento"),
        AVISO_AGENDAMENTO_ROTEIRO_VIAGEM("aviso_agendamento_veiculo")
        ;

        TipoMensagemWhatsApp(String descricao) {
            this.descricao = descricao;
        }

        private final String descricao;

        public String descricao() {
            return descricao;
        }
    }

    public static String getURLProviderEnviar() throws DAOException {
        String urlBaseApiWhatsApp = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("urlBaseApiWhatsApp");
        return urlBaseApiWhatsApp.concat(ENDPOINT_ENVIAR);
    }

    public static String getURLProviderResposta() throws DAOException {
        String urlBaseApiWhatsApp = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("urlBaseApiWhatsApp");
        return urlBaseApiWhatsApp.concat(ENDPOINT_RECEBER_RESPOSTA);
    }

    public static String getURLProviderStatus() throws DAOException {
        String urlBaseApiWhatsApp = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("urlBaseApiWhatsApp");
        return urlBaseApiWhatsApp.concat(ENDPOINT_STATUS_MENSAGEM);
    }

    public static MensagemEnvioWhatsAppPadraoV1DTO getMensagemWhatsAppEnvioPadraoV1DTO(MensagemSmsDTO mensagemDTO) {
        MensagemEnvioWhatsAppPadraoV1DTO whatsAppDTO = new MensagemEnvioWhatsAppPadraoV1DTO();
        whatsAppDTO.setCelkMessageId(mensagemDTO.getMessageId());
        whatsAppDTO.setPhone(mensagemDTO.getMobileComPaisBR());
        whatsAppDTO.setVariables(Arrays.asList(mensagemDTO.getMsg()));
        return whatsAppDTO;
    }

    public static MensagemEnvioWhatsAppDTO getMensagemWhatsAppEnvioDTO(MensagemSmsDTO mensagemDTO) {
        MensagemEnvioWhatsAppDTO whatsAppDTO = new MensagemEnvioWhatsAppDTO();
        whatsAppDTO.setPhone(mensagemDTO.getMobileComPaisBR());
        whatsAppDTO.setName(mensagemDTO.getMensagemWhatsAppDTO().getNomeSocial());
        whatsAppDTO.setData(mensagemDTO.getMensagemWhatsAppDTO().getDataAgendamento());
        whatsAppDTO.setHora(mensagemDTO.getMensagemWhatsAppDTO().getHoraAgendamento());
        whatsAppDTO.setTipoConsulta(mensagemDTO.getMensagemWhatsAppDTO().getTipoProcedimento());
        whatsAppDTO.setCodigo(mensagemDTO.getMensagemWhatsAppDTO().getChaveValidacao());
        whatsAppDTO.setLink(mensagemDTO.getMensagemWhatsAppDTO().getUrlComprovanteAgendamento());
        whatsAppDTO.setLocal(mensagemDTO.getMensagemWhatsAppDTO().getLocalAgendamento());
        whatsAppDTO.setTelefone(getContatoSuporte());
        whatsAppDTO.setCelkMessageId(mensagemDTO.getMessageId());

        return whatsAppDTO;
    }

    public static MensagemAvisoAgendamentoViagemWhatsAppDTO getMensagemWhatsAppAvisoAgendamentoVeiculoDTO(MensagemSmsDTO mensagemDTO) {
        MensagemAvisoAgendamentoViagemWhatsAppDTO whatsAppDTO = new MensagemAvisoAgendamentoViagemWhatsAppDTO();
        whatsAppDTO.setPhone(mensagemDTO.getMobileComPaisBR());
        whatsAppDTO.setCelkMessageId(mensagemDTO.getMessageId());
        whatsAppDTO.setTelefone(getContatoSuporte());
        whatsAppDTO.setMensagem(mensagemDTO.getMsg());
        whatsAppDTO.setEmpresa(mensagemDTO.getEmpresa());
        whatsAppDTO.setCelkMessageId(mensagemDTO.getMessageId());
        return whatsAppDTO;

    }

    public static MensagemConfirmacaoWhatsAppDTO getMensagemWhatsAppConfirmacaoDTO(MensagemSmsDTO mensagemDTO) {
        MensagemConfirmacaoWhatsAppDTO whatsAppDTO = new MensagemConfirmacaoWhatsAppDTO();
        whatsAppDTO.setPhone(mensagemDTO.getMobileComPaisBR());
        whatsAppDTO.setName(mensagemDTO.getMensagemWhatsAppDTO().getNomeSocial());
        whatsAppDTO.setData(mensagemDTO.getMensagemWhatsAppDTO().getDataAgendamento());
        whatsAppDTO.setHora(mensagemDTO.getMensagemWhatsAppDTO().getHoraAgendamento());
        whatsAppDTO.setTipoConsulta(mensagemDTO.getMensagemWhatsAppDTO().getTipoProcedimento());
        whatsAppDTO.setCodigo(mensagemDTO.getMensagemWhatsAppDTO().getChaveValidacao());
        whatsAppDTO.setLink(mensagemDTO.getMensagemWhatsAppDTO().getUrlComprovanteAgendamento());
        whatsAppDTO.setLocal(mensagemDTO.getMensagemWhatsAppDTO().getLocalAgendamento());
        whatsAppDTO.setTelefone(getContatoSuporte());
        whatsAppDTO.setCelkMessageId(mensagemDTO.getMessageId());

        return whatsAppDTO;
    }

    public static MensagemCancelamentoWhatsAppDTO getMensagemWhatsAppCancelamentoDTO(MensagemSmsDTO mensagemDTO) {
        MensagemCancelamentoWhatsAppDTO whatsAppDTO = new MensagemCancelamentoWhatsAppDTO();
        whatsAppDTO.setPhone(mensagemDTO.getMobileComPaisBR());
        whatsAppDTO.setName(mensagemDTO.getMensagemWhatsAppDTO().getNomeSocial());
        whatsAppDTO.setData(mensagemDTO.getMensagemWhatsAppDTO().getDataAgendamento());
        whatsAppDTO.setHora(mensagemDTO.getMensagemWhatsAppDTO().getHoraAgendamento());
        whatsAppDTO.setTipoConsulta(mensagemDTO.getMensagemWhatsAppDTO().getTipoProcedimento());
        whatsAppDTO.setLocal(mensagemDTO.getMensagemWhatsAppDTO().getLocalAgendamento());
        whatsAppDTO.setCelkMessageId(mensagemDTO.getMessageId());

        return whatsAppDTO;
    }

    public static MensagemReagendamentoWhatsAppDTO getMensagemWhatsAppRemanejamentoDTO(MensagemSmsDTO mensagemDTO) {
        MensagemReagendamentoWhatsAppDTO whatsAppDTO = new MensagemReagendamentoWhatsAppDTO();
        whatsAppDTO.setName(mensagemDTO.getMensagemWhatsAppDTO().getNomeSocial());

        whatsAppDTO.setData(mensagemDTO.getMensagemWhatsAppDTO().getAgendaRemanejadoData());
        whatsAppDTO.setHora(mensagemDTO.getMensagemWhatsAppDTO().getAgendaRemanejadoHora());
        whatsAppDTO.setReagendamentoData(mensagemDTO.getMensagemWhatsAppDTO().getDataAgendamento());
        whatsAppDTO.setReagendamentoHora(mensagemDTO.getMensagemWhatsAppDTO().getHoraAgendamento());

        whatsAppDTO.setTipoConsulta(mensagemDTO.getMensagemWhatsAppDTO().getTipoProcedimento());
        whatsAppDTO.setLink(mensagemDTO.getMensagemWhatsAppDTO().getUrlComprovanteAgendamento());
        whatsAppDTO.setLocal(mensagemDTO.getMensagemWhatsAppDTO().getLocalAgendamento());
        whatsAppDTO.setPhone(mensagemDTO.getMobileComPaisBR());
        whatsAppDTO.setCelkMessageId(mensagemDTO.getMessageId());
        return whatsAppDTO;
    }

    public static void setMensagemWhatsAppDTO(AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario, MensagemSmsDTO mensagemDTO, MensagemSmsDTO.TipoMensagemWhatsApp tipoMensagemWhatsApp) {
        mensagemDTO.setTipoMensagemWhatsApp(tipoMensagemWhatsApp);
        mensagemDTO.getMensagemWhatsAppDTO().setNomeSocial(agendaGradeAtendimentoHorario.getUsuarioCadsus().getNomeSocial());
        mensagemDTO.getMensagemWhatsAppDTO().setUrlComprovanteAgendamento(new ComprovanteGradeAtendimentoHorario(agendaGradeAtendimentoHorario).getURLComprovante());
        mensagemDTO.getMensagemWhatsAppDTO().setLocalAgendamento(agendaGradeAtendimentoHorario.getLocalAgendamento().getDescricao());
        mensagemDTO.getMensagemWhatsAppDTO().setDataAgendamento(agendaGradeAtendimentoHorario.getDataAgendamentoFormatado());
        mensagemDTO.getMensagemWhatsAppDTO().setHoraAgendamento(agendaGradeAtendimentoHorario.getHoraAgendamentoFormatado());
        mensagemDTO.getMensagemWhatsAppDTO().setTipoProcedimento(agendaGradeAtendimentoHorario.getTipoProcedimento().getDescricao());
        mensagemDTO.getMensagemWhatsAppDTO().setChaveValidacao(Coalesce.asString(agendaGradeAtendimentoHorario.getChaveValidacao(), agendaGradeAtendimentoHorario.getCodigo().toString()));

        if (MensagemSmsDTO.TipoMensagemWhatsApp.REAGENDAMENTO.equals(tipoMensagemWhatsApp)) {
            mensagemDTO.getMensagemWhatsAppDTO().setAgendaRemanejadoData(Data.formatar(agendaGradeAtendimentoHorario.getAgendaRemanejado().getDataAgendamento()));
            mensagemDTO.getMensagemWhatsAppDTO().setAgendaRemanejadoHora(Data.formatarHora(agendaGradeAtendimentoHorario.getAgendaRemanejado().getDataAgendamento()));
        }
    }

}
