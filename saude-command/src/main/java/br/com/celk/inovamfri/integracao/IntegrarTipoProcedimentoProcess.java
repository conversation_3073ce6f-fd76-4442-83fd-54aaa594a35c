package br.com.celk.inovamfri.integracao;

import br.com.celk.inovamfri.integracao.dto.BadRequestDTO;
import br.com.celk.inovamfri.integracao.dto.InternalServerErrorDTO;
import br.com.celk.inovamfri.integracao.dto.ParameterViolation;
import br.com.celk.inovamfri.integracao.dto.ProcedimentosIntegrationDTO;
import br.com.celk.inovamfri.util.InovamfriHelper;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.hibernate.Query;

import javax.ws.rs.core.Response;
import java.util.List;
import java.util.ListIterator;

/**
 * Created by sulivan on 15/09/17.
 */
public class IntegrarTipoProcedimentoProcess extends AbstractIntegrationProcess<ProcedimentosIntegrationDTO> {

    private List<Usuario> usuariosList;
    private List<ProcedimentosIntegrationDTO> procedimentosIntegrationDTOList;
    StringBuilder sb = new StringBuilder();

    public IntegrarTipoProcedimentoProcess(List<Usuario> usuariosList, List<ProcedimentosIntegrationDTO> procedimentosIntegrationDTOList) {
        this.usuariosList = usuariosList;
        this.procedimentosIntegrationDTOList = procedimentosIntegrationDTOList;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        String hqlTipoProcedimento;
        Query qTipoProcedimento;
        Response response;
        createInitialMessageLog();
        for (ProcedimentosIntegrationDTO procedimentosIntegrationDTO : this.procedimentosIntegrationDTOList) {
            response = conectarRecurso(procedimentosIntegrationDTO);
            if (response == null) {
                return;
            }
            if (Response.Status.INTERNAL_SERVER_ERROR.getStatusCode() == response.getStatus()) {
                sb.append(procedimentosIntegrationDTO.toString());
                sb.append(" - ");
                InternalServerErrorDTO internalServerErrorDTO = response.readEntity(InternalServerErrorDTO.class);
                sb.append(internalServerErrorDTO.getMessage());
                sb.append("; \n");
            } else if(Response.Status.BAD_REQUEST.getStatusCode() == response.getStatus()) {
                sb.append(procedimentosIntegrationDTO.toString());
                sb.append(" - ");
                List<ParameterViolation> parameterViolations = response.readEntity(BadRequestDTO.class).getParameterViolations();
                ListIterator<ParameterViolation> parameterViolationListIterator = parameterViolations.listIterator();
                while (parameterViolationListIterator.hasNext()){
                    ParameterViolation next = parameterViolationListIterator.next();
                    sb.append(next.getMessage());
                    if(parameterViolationListIterator.hasNext()){
                        sb.append(", ");
                    }

                }
                sb.append("; \n");
            }
            hqlTipoProcedimento = "UPDATE TipoProcedimento "
                    + " SET dataIntegracaoInovamfri = :dataAtual "
                    + " WHERE codigo = :codigoTipoProcedimento ";

            qTipoProcedimento = getSession().createQuery(hqlTipoProcedimento);
            qTipoProcedimento.setParameter("dataAtual", DataUtil.getDataAtual());
            qTipoProcedimento.setParameter("codigoTipoProcedimento", new Long(procedimentosIntegrationDTO.getIdRegistroClient()));
            qTipoProcedimento.executeUpdate();
        }

        createFinalMessageLog();
    }

    @Override
    public InovamfriHelper.Resources getResources() {
        return InovamfriHelper.Resources.PROCEDIMENTOS;
    }

    @Override
    public List<Usuario> getUsuariosList() {
        return this.usuariosList;
    }

    public String getMensagem() {
        return this.sb.toString();
    }

}