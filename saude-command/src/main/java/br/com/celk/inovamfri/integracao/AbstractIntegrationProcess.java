package br.com.celk.inovamfri.integracao;

import br.com.celk.inovamfri.util.InovamfriHelper;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.MensagemDTO;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;

import javax.ws.rs.client.Entity;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.List;

/**
 * Created by roger on 10/08/16.
 */
public abstract class AbstractIntegrationProcess<T> extends AbstractCommandTransaction {

    public static final String PROCESSO_DE_INTEGRAÇÃO_INOVAMFRI_INICIADO = "----- PROCESSO DE INTEGRAÇÃO INOVAMFRI INICIADO ----- ";
    public static final String PROCESSO_DE_INTEGRAÇÃO_INOVAMFRI_FINALIZADO = "----- PROCESSO DE INTEGRAÇÃO INOVAMFRI FINALIZADO ----- ";

    protected Response conectarRecurso(T dto) throws ValidacaoException, DAOException {

        if (CollectionUtils.isEmpty(getUsuariosList())) {

            throw new ValidacaoException(Bundle.getStringApplication("inclua_usuario_agendador_processos"));
        }

        Response response = new InovamfriHelper().startConnection(Entity.entity(dto, MediaType.APPLICATION_JSON_TYPE), getResources());

        if (Response.Status.OK.getStatusCode() != response.getStatus() &&
                Response.Status.INTERNAL_SERVER_ERROR.getStatusCode() != response.getStatus() &&
                Response.Status.BAD_REQUEST.getStatusCode() != response.getStatus()) {

            String jsonErro = response.readEntity(String.class);

            Loggable.log.error(jsonErro);

            if (Response.Status.NOT_FOUND.getStatusCode() != response.getStatus() &&
                    Response.Status.BAD_GATEWAY.getStatusCode() != response.getStatus() &&
                    Response.Status.SERVICE_UNAVAILABLE.getStatusCode() != response.getStatus() &&
                    Response.Status.GATEWAY_TIMEOUT.getStatusCode() != response.getStatus()) {

                notificarUsuariosErro(jsonErro);
            }

            return null;
        }

        return response;
    }

    private void notificarUsuariosErro(String jsonErro) throws ValidacaoException, DAOException {
        MensagemDTO mensagemDTO = new MensagemDTO();
        mensagemDTO.setAssunto(Bundle.getStringApplication("integracao_inovamfri"));
        mensagemDTO.setUsuarios(getUsuariosList());

        StringBuilder sb = new StringBuilder(Bundle.getStringApplication("msg_integracao_inovamfri_erro"));
        sb.append(" - ").append(jsonErro);

        mensagemDTO.setMensagem(sb.toString());
        BOFactory.getBO(ComunicacaoFacade.class).enviarMensagem(mensagemDTO);
    }

//    public abstract List<T> getEntityDTOList();

    public abstract InovamfriHelper.Resources getResources();

    public abstract List<Usuario> getUsuariosList();

    public void createInitialMessageLog(){
        Loggable.log.info(PROCESSO_DE_INTEGRAÇÃO_INOVAMFRI_INICIADO + TenantContext.getContext() + ": " + getResources().getDescricao());
    }

    public void createFinalMessageLog(){
        Loggable.log.info(PROCESSO_DE_INTEGRAÇÃO_INOVAMFRI_FINALIZADO + TenantContext.getContext() + ": " + getResources().getDescricao());
    }
}
