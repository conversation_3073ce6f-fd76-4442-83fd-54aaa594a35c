package br.com.celk.inovamfri.integracao;

import br.com.celk.inovamfri.integracao.dto.AtendimentoIntegrationDTO;
import br.com.celk.inovamfri.integracao.dto.BadRequestDTO;
import br.com.celk.inovamfri.integracao.dto.InternalServerErrorDTO;
import br.com.celk.inovamfri.integracao.dto.ParameterViolation;
import br.com.celk.inovamfri.util.InovamfriHelper;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.EloAtendimentoCsCidadao;
import org.hibernate.Query;

import javax.ws.rs.core.Response;
import java.util.List;
import java.util.ListIterator;

/**
 * Created by roger on 10/08/16.
 */
public class IntegrarAtendimentoProcess extends AbstractIntegrationProcess<AtendimentoIntegrationDTO> {

    private List<Usuario> usuariosList;
    private List<AtendimentoIntegrationDTO> atendimentoIntegrationDTOList;
    StringBuilder sb = new StringBuilder();

    public IntegrarAtendimentoProcess(List<Usuario> usuariosList, List<AtendimentoIntegrationDTO> atendimentoIntegrationDTOList) {
        this.usuariosList = usuariosList;
        this.atendimentoIntegrationDTOList = atendimentoIntegrationDTOList;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        String hqlAtendimento;
        Query qAtendimentoAplicacao;
        Response response;
        EloAtendimentoCsCidadao elo;
        Atendimento atendimento;

        createInitialMessageLog();
        for (AtendimentoIntegrationDTO atendimentoIntegrationDTO : this.atendimentoIntegrationDTOList) {
            response = conectarRecurso(atendimentoIntegrationDTO);
            if (response == null) {
                return;
            }
            if (Response.Status.INTERNAL_SERVER_ERROR.getStatusCode() == response.getStatus()) {
                sb.append(atendimentoIntegrationDTO.toString());
                sb.append(" - ");
                InternalServerErrorDTO internalServerErrorDTO = response.readEntity(InternalServerErrorDTO.class);
                sb.append(internalServerErrorDTO.getMessage());
                sb.append("; \n");
            } else if (Response.Status.BAD_REQUEST.getStatusCode() == response.getStatus()) {
                sb.append(atendimentoIntegrationDTO.toString());
                sb.append(" - ");
                List<ParameterViolation> parameterViolations = response.readEntity(BadRequestDTO.class).getParameterViolations();
                ListIterator<ParameterViolation> parameterViolationListIterator = parameterViolations.listIterator();
                while (parameterViolationListIterator.hasNext()) {
                    ParameterViolation next = parameterViolationListIterator.next();
                    sb.append(next.getMessage());
                    if (parameterViolationListIterator.hasNext()) {
                        sb.append(", ");
                    }

                }
                sb.append("; \n");
            }

            atendimento = (Atendimento) getSession().get(Atendimento.class, new Long(atendimentoIntegrationDTO.getIdRegistroClient()));

            if(atendimento != null){
                elo = new EloAtendimentoCsCidadao();
                elo.setAtendimento(atendimento);
                elo.setDataIntegracao(DataUtil.getDataAtual());

                BOFactory.save(elo);
            }

//            hqlAtendimento = "UPDATE Atendimento "
//                    + " SET dataIntegracaoInovamfri = :dataAtual "
//                    + " WHERE codigo = :codigoAtendimento ";
//
//            qAtendimentoAplicacao = getSession().createQuery(hqlAtendimento);
//            qAtendimentoAplicacao.setParameter("dataAtual", DataUtil.getDataAtual());
//            qAtendimentoAplicacao.setParameter("codigoAtendimento", new Long(atendimentoIntegrationDTO.getIdRegistroClient()));
//            qAtendimentoAplicacao.executeUpdate();
        }
        createFinalMessageLog();
    }

    @Override
    public InovamfriHelper.Resources getResources() {
        return InovamfriHelper.Resources.ATENDIMENTO;
    }

    @Override
    public List<Usuario> getUsuariosList() {
        return usuariosList;
    }

    public String getMensagem() {
        return this.sb.toString();
    }

}