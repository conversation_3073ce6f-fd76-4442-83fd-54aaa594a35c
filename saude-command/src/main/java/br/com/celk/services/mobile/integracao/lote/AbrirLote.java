package br.com.celk.services.mobile.integracao.lote;

import br.com.celk.util.MobileCoreUtil;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoRuntimeException;
import br.com.ksisolucoes.vo.basico.Equipe;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.integracao.mobile.IntegracaoMobile;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.hibernate.criterion.Restrictions;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class AbrirLote extends AbstractCommandTransaction {

    private final Long idProfissional;
    private IntegracaoMobile integracao;
    private boolean gerarLote;
    private IntegracaoMobile.TipoIntegracao tipoIntegracao;
    private String versao;

    public AbrirLote(final Long idProfissional, final IntegracaoMobile.TipoIntegracao tipoIntegracao, String versao) {
        this.idProfissional = idProfissional;
        this.tipoIntegracao = tipoIntegracao;
        this.versao = versao;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        try {
            BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("UnidadePadraoAgendadorProcessos");
        } catch (ValidacaoRuntimeException e) {
            throw new ValidacaoException(e.getMessage());
        }


        List<IntegracaoMobile> integracaoMobiles = LoadManager.getInstance(IntegracaoMobile.class)
                .addProperties(new HQLProperties(IntegracaoMobile.class).getProperties())
                .addProperties(VOUtils.montarPath(IntegracaoMobile.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                .addProperties(VOUtils.montarPath(IntegracaoMobile.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                .addProperties(VOUtils.montarPath(IntegracaoMobile.PROP_PROFISSIONAL, Profissional.PROP_REFERENCIA))
                .addParameter(new QueryCustom.QueryCustomParameter(IntegracaoMobile.PROP_DATA_RESPOSTA, BuilderQueryCustom.QueryParameter.IS_NULL))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(IntegracaoMobile.PROP_PROFISSIONAL, Profissional.PROP_CODIGO), idProfissional))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(IntegracaoMobile.PROP_STATUS),
                        BuilderQueryCustom.QueryParameter.NOT_IN,
                        Arrays.asList(
                                IntegracaoMobile.Status.RECEBIDO.value(), IntegracaoMobile.Status.CANCELADO.value(), IntegracaoMobile.Status.CONCLUIDO_SEM_RETORNO.value()))
                )
                .start()
                .getList();

        gerarLote = true;

        if (CollectionUtils.isNotEmpty(integracaoMobiles)) {
            for (IntegracaoMobile integracaoMobile : integracaoMobiles) {
                if (IntegracaoMobile.Status.ABERTO.value().equals(integracaoMobile.getStatus())) {
                    MobileCoreUtil.cancelado(integracaoMobile, Bundle.getStringApplication("msg_lote_duplicado", integracaoMobile.getProfissional().getDescricaoFormatado()));
                } else {
                    integracaoMobile.setStatus(IntegracaoMobile.Status.CONCLUIDO_SEM_RETORNO.value());
                    integracaoMobile.setDataResposta(new Date());

                }
                getSession().flush();
                getSession().clear();
                BOFactory.save(integracaoMobile);
            }
        }

        if (gerarLote) {
            Profissional profissional = LoadManager.getInstance(Profissional.class).setId(idProfissional).start().getVO();

            Usuario usuario = LoadManager.getInstance(Usuario.class)
                    .addProperties(new HQLProperties(Usuario.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Usuario.PROP_PROFISSIONAL, Profissional.PROP_CODIGO), idProfissional))
                    .addParameter(new QueryCustom.QueryCustomParameter(Usuario.PROP_FLAG_USUARIO_TEMPORARIO, RepositoryComponentDefault.NAO_LONG))
                    .addParameter(new QueryCustom.QueryCustomParameter(Usuario.PROP_STATUS, Usuario.STATUS_ATIVO))
                    .start()
                    .getVO();
            if (usuario == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_nao_encontrado_usuario_sistema_para_profissional", profissional.getDescricaoFormatado()));
            }

            if (StringUtils.isEmpty(profissional.getCodigoCns())) {
                throw new ValidacaoException("Favor definir o CNS para o profissional " + profissional.getDescricaoFormatado());
            }

            EquipeProfissional equipeProfissional = (EquipeProfissional) getSession().createCriteria(EquipeProfissional.class)
                    .add(Restrictions.eq(EquipeProfissional.PROP_STATUS, EquipeProfissional.STATUS_ATIVO))
                    .add(Restrictions.eq(EquipeProfissional.PROP_PROFISSIONAL, profissional))
                    .createCriteria(EquipeProfissional.PROP_EQUIPE)
                    .add(Restrictions.eq(Equipe.PROP_ATIVO, RepositoryComponentDefault.SIM))
                    .setMaxResults(1)
                    .uniqueResult();

            if (equipeProfissional == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_nao_encontrado_equipe_para_profissional", profissional.getDescricaoFormatado()));
            }

            if (equipeProfissional.getEquipe().getEmpresa() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_equipe_sem_unidade_definida", equipeProfissional.getEquipe().getReferencia()));
            }

            IntegracaoMobile integracaoMobile = new IntegracaoMobile();
            integracaoMobile.setProfissional(profissional);
            integracaoMobile.setStatus(IntegracaoMobile.Status.ABERTO.value());
            integracaoMobile.setTipoIntegracao(tipoIntegracao.value());
            integracaoMobile.setDataIntegracao(new Date());
            integracaoMobile.setUsuario(usuario);
            integracaoMobile.setEmpresa(equipeProfissional.getEquipe().getEmpresa());

            getSession().flush();
            getSession().clear();

            integracaoMobile.setVersaoMobile(versao);

            integracao = BOFactory.save(integracaoMobile);
        }

    }

    public IntegracaoMobile getResult() {
        return integracao;
    }
}
