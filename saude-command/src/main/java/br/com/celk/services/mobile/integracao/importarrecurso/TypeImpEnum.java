package br.com.celk.services.mobile.integracao.importarrecurso;

import br.com.celk.services.mobile.integracao.bindimportacao.anexopaciente.AnexoPacienteBind;
import br.com.celk.services.mobile.integracao.bindimportacao.anexopacienteelo.AnexoPacienteEloBind;
import br.com.celk.services.mobile.integracao.bindimportacao.atividadeGrupo.AtividadeGrupoBind;
import br.com.celk.services.mobile.integracao.bindimportacao.coordenadas.CoordenadasBind;
import br.com.celk.services.mobile.integracao.bindimportacao.enderecodomicilio.EnderecoDomicilioBind;
import br.com.celk.services.mobile.integracao.bindimportacao.enderecodomicilioesus.EnderecoDomicilioEsusBind;
import br.com.celk.services.mobile.integracao.bindimportacao.enderecousuariocadsus.EnderecoUsuarioCadsusBind;
import br.com.celk.services.mobile.integracao.bindimportacao.esusfichamarcadoresconsumoalimentar.EsusFichaMarcadoresConsumoAlimentarBind;
import br.com.celk.services.mobile.integracao.bindimportacao.mobilelog.MobileLogBind;
import br.com.celk.services.mobile.integracao.bindimportacao.nascidosvivos.NascidosVivosBind;
import br.com.celk.services.mobile.integracao.bindimportacao.notificacaoAgendas.NotificacaoAgendasBind;
import br.com.celk.services.mobile.integracao.bindimportacao.pesquisavisita.PesquisaVisitaBind;
import br.com.celk.services.mobile.integracao.bindimportacao.registrovacinas.RegistroVacinasBind;
import br.com.celk.services.mobile.integracao.bindimportacao.usuario.UsuarioBind;
import br.com.celk.services.mobile.integracao.bindimportacao.usuariocadsus.UsuarioCadsusBind;
import br.com.celk.services.mobile.integracao.bindimportacao.usuariocadsuscns.UsuarioCadsusCnsBind;
import br.com.celk.services.mobile.integracao.bindimportacao.usuariocadsusdado.UsuarioCadsusDadoBind;
import br.com.celk.services.mobile.integracao.bindimportacao.usuariocadsusdocumento.UsuarioCadsusDocumentoBind;
import br.com.celk.services.mobile.integracao.bindimportacao.usuariocadsusesus.UsuarioCadsusEsusBind;
import br.com.celk.services.mobile.integracao.bindimportacao.usuariocadsusv2.UsuarioCadsusV2Bind;
import br.com.celk.services.mobile.integracao.bindimportacao.vacinasimagens.VacinasImagensBind;
import br.com.celk.services.mobile.integracao.bindimportacao.visitadomiciliar.VisitaDomiciliarBind;
import br.com.celk.services.mobile.integracao.bindimportacao.visitaterritorial.VisitaTerritorialBind;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo;
import br.com.ksisolucoes.vo.basico.pesquisa.PesquisaVisita;
import br.com.ksisolucoes.vo.cadsus.*;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaMarcadoresConsumoAlimentar;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.mobile.MobileLog;
import br.com.ksisolucoes.vo.prontuario.basico.AnexoPaciente;
import br.com.ksisolucoes.vo.prontuario.basico.AnexoPacienteElo;
import br.com.ksisolucoes.vo.prontuario.basico.FichaNascidoVivo;
import br.com.ksisolucoes.vo.service.maps.Coordenadas;
import br.com.ksisolucoes.vo.vacina.RegistroVacina;
import br.com.ksisolucoes.vo.vacina.VacinasImagem;

/**
 *
 * <AUTHOR>
 */
public enum TypeImpEnum {
    
    USUARIO_CADSUS_ESUS("PACIENTE_ESUS", UsuarioCadsusEsusBind.class, UsuarioCadsusEsus.class),
    USUARIO_CADSUS_DOCUMENTO("DOCUMENTOS", UsuarioCadsusDocumentoBind.class, UsuarioCadsusDocumento.class),
    USUARIO_CADSUS_CNS("CNS", UsuarioCadsusCnsBind.class, UsuarioCadsusCns.class),
    USUARIO_CADSUSV2("PACIENTEV2", UsuarioCadsusV2Bind.class, UsuarioCadsus.class),
    USUARIO_CADSUS("PACIENTE", UsuarioCadsusBind.class, UsuarioCadsus.class),
    ENDERECO("ENDERECO", EnderecoUsuarioCadsusBind.class, EnderecoUsuarioCadsus.class),
    DOMICILIO("DOMICILIO", EnderecoDomicilioBind.class, EnderecoDomicilio.class),
    DOMICILIO_ESUS("DOMICILIO_ESUS", EnderecoDomicilioEsusBind.class, EnderecoDomicilioEsus.class),
    VISITA_DOMICILIAR("VISITA_DOMICILIAR", VisitaDomiciliarBind.class, VisitaDomiciliar.class),
    VISITA_TERRITORIAL("VISITA_TERRITORIAL", VisitaTerritorialBind.class, VisitaDomiciliar.class),
    MARCADORES_CONSUMO_ALIMENTAR("MARCADORES_CONSUMO_ALIMENTAR", EsusFichaMarcadoresConsumoAlimentarBind.class, EsusFichaMarcadoresConsumoAlimentar.class),
    ATIVIDADE_EM_GRUPO("ATIVIDADE_EM_GRUPO", AtividadeGrupoBind.class, AtividadeGrupo.class),
    MOBILE_LOG("MOBILE_LOG", MobileLogBind.class, MobileLog.class),
    USUARIO_CADSUS_DADO("PACIENTE_DADO", UsuarioCadsusDadoBind.class, UsuarioCadsusDado.class),
    COORDENADAS("COORDENADAS", CoordenadasBind.class, Coordenadas.class),
    NOTIFICACAO_AGENDAS("NOTIFICACAO_AGENDAS", NotificacaoAgendasBind.class, NotificacaoAgendas.class),
    REGISTRO_VACINAS("REGISTRO_VACINAS", RegistroVacinasBind.class, RegistroVacina.class),
    VACINAS_IMAGENS("VACINAS_IMAGENS", VacinasImagensBind.class, VacinasImagem.class),
    PESQUISA_VISITA("PESQUISA_VISITA", PesquisaVisitaBind.class, PesquisaVisita.class),
    ANEXO_PACIENTE("ANEXO_PACIENTE", AnexoPacienteBind.class, AnexoPaciente.class),
    ANEXO_PACIENTE_ELO("ANEXO_IMAGENS", AnexoPacienteEloBind.class, AnexoPacienteElo.class),
    NASCIDOS_VIVOS("NASCIDOS_VIVOS", NascidosVivosBind.class, FichaNascidoVivo.class),
    USUARIOS("USUARIOS", UsuarioBind.class, Usuario.class);

    private final String name;
    private final Class<? extends IBindVoImp> bindClass;
    private final Class convertedClass;

     TypeImpEnum(String name, Class<? extends IBindVoImp> bindClass, Class convertedClass) {
        this.name = name;
        this.bindClass = bindClass;
        this.convertedClass = convertedClass;
    }

    public String getName() {
        return name;
    }

    public Class<? extends IBindVoImp> getBindClass() {
        return bindClass;
    }

    public Class getNotTypedBindClass() {
        return bindClass;
    }

    public Class getConvertedClass() {
        return convertedClass;
    }


    public static TypeImpEnum resolveType(String name){
        for (TypeImpEnum typeEnum : values()) {
            if(typeEnum.name.equalsIgnoreCase(Coalesce.asString(name))){
                return typeEnum;
            }
        }
        return null;
    }
}
