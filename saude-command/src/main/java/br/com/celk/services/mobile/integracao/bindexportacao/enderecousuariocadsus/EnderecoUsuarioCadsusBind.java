package br.com.celk.services.mobile.integracao.bindexportacao.enderecousuariocadsus;

import br.com.celk.services.mobile.integracao.exportarrecurso.IBindVoExport;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import org.apache.camel.dataformat.bindy.annotation.CsvRecord;
import org.apache.camel.dataformat.bindy.annotation.DataField;

/**
 *
 * <AUTHOR>
 */
@CsvRecord(separator = "\\|",  crlf = "UNIX")
public class EnderecoUsuarioCadsusBind implements IBindVoExport<EnderecoUsuarioCadsus>{

    @DataField(pos = 1, required = true)
    private Long codigoSistema;
    @DataField(pos = 2, required = true)
    private Long versao;
    @DataField(pos = 3)
    private Long cidade;
    @DataField(pos = 4)
    private String cep;
    @DataField(pos = 5)
    private String bairro;
    @DataField(pos = 6)
    private Long tipoLogradouro;
    @DataField(pos = 7, required = true)
    private String logradouro;
    @DataField(pos = 8)
    private String complementoLogradouro;
    @DataField(pos = 9)
    private String numeroLogradouro;
    @DataField(pos = 10)
    private String telefone;
    @DataField(pos = 11)
    private String telefoneReferencia;
    @DataField(pos = 12)
    private String pontoReferencia;

    @Override
    public void buildProperties(EnderecoUsuarioCadsus vo) {
        codigoSistema = vo.getCodigo();
        versao = vo.getVersionAll();
        if(vo.getCidade()!= null){
            cidade = vo.getCidade().getCodigo();
        }
        cep = Coalesce.asString(vo.getCep()).replaceAll("[^0-9]", "");
        bairro = vo.getBairro();
        if(vo.getTipoLogradouro()!= null){
            tipoLogradouro = vo.getTipoLogradouro().getCodigo();
        }
        logradouro = vo.getLogradouro();
        complementoLogradouro = vo.getComplementoLogradouro();
        numeroLogradouro = vo.getNumeroLogradouro();
        telefone = vo.getTelefone();
        telefoneReferencia = vo.getTelefoneReferencia();
        pontoReferencia = vo.getPontoReferencia();
    }
    
}
