package br.com.celk.services.frotamobile;

import br.com.celk.bo.service.rest.frota.ImportarPassageirosRestDTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoContext;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoServidor;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.frota.RoteiroViagemPassageiro;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ImportarPassageiros extends AbstractCommandTransaction {

    private final List<ImportarPassageirosRestDTO> passageiros;
    private final String ipClient;
    private SessaoAplicacaoServidor sessaoAplicacao;

    public ImportarPassageiros(List<ImportarPassageirosRestDTO> passageiros, String ipClient) {
        this.passageiros = passageiros;
        this.ipClient = ipClient;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        for (ImportarPassageirosRestDTO item : this.passageiros) {
            RoteiroViagemPassageiro passageiro = LoadManager.getInstance(RoteiroViagemPassageiro.class)
                    .addProperties(new HQLProperties(RoteiroViagemPassageiro.class).getProperties())
                    .setId(item.getCodigo()).start().getVO();
            //update
            if(passageiro !=null && passageiro.getCodigo() !=null && !passageiro.getStatus().equals(item.getStatus())){
                gerarSessaoAplicacao(new Usuario(item.getCodigoUsuarioRegistro() == null ? item.getCodigoUsuario() : item.getCodigoUsuarioRegistro()));
                passageiro.setStatus(item.getStatus());
                BOFactory.save(passageiro);
            }

        }
    }

    private void gerarSessaoAplicacao(Usuario usuarioRest) throws ValidacaoException {
        Long codigoUsuario = usuarioRest.getCodigo();

        Usuario usuario = LoadManager.getInstance(Usuario.class)
                .addProperties(new HQLProperties(Usuario.class).getProperties())
                .addProperties(new HQLProperties(Empresa.class, Usuario.PROP_EMPRESA_PADRAO).getProperties())
                .setId(codigoUsuario).setMaxResults(1).start().getVO();
        if (usuario == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_usuario_nao_definido_registro"));
        }
        Empresa empresa = usuario.getEmpresaPadrao();
        if (empresa == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_unidade_nao_definida_usuario", usuario.getNome()));
        }

        sessaoAplicacao = SessaoAplicacaoServidor.getNewInstance(usuario, empresa, Bundle.getLocale());
        sessaoAplicacao.setIpClient(ipClient);
        SessaoAplicacaoContext.setContext(sessaoAplicacao);
    }

}
