package br.com.celk.services.mobile.integracao.bindexportacao.tipoAtividadeTema;

import br.com.celk.services.mobile.integracao.exportarrecurso.IBindVoExport;
import br.com.ksisolucoes.vo.esus.TipoAtividadeTema;
import org.apache.camel.dataformat.bindy.annotation.CsvRecord;
import org.apache.camel.dataformat.bindy.annotation.DataField;

/**
 * <AUTHOR>
 */
@CsvRecord(separator = "\\|", crlf = "UNIX")
public class TipoAtividadeTemaBind implements IBindVoExport<TipoAtividadeTema> {

    @DataField(pos = 1, required = true)
    private Long codigoSistema;
    @DataField(pos = 2)
    private Long versao;
    @DataField(pos = 3)
    private Long cd_tema_atividade;
    @DataField(pos = 4)
    private String ds_tema_atividade;
    @DataField(pos = 5)
    private Long cd_esus;
    @DataField(pos = 6)
    private Long cd_esus_pratica_saude;
    @DataField(pos = 7)
    private Long tipo;

    @Override
    public void buildProperties(TipoAtividadeTema vo) {
        codigoSistema = vo.getCodigo();
        versao = vo.getVersionAll();
        cd_tema_atividade = vo.getCodigo();
        ds_tema_atividade = vo.getDescricaoTemaAtividade();
        cd_esus = vo.getCodigoEsus();
        cd_esus_pratica_saude = vo.getCodigoEsusPraticaSaude();
        tipo = vo.getTipo();
    }
}
