package br.com.celk.services.mobile.integracao.bindexportacao.profissional;

import br.com.celk.services.mobile.integracao.exportarrecurso.IBindVoExport;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import org.apache.camel.dataformat.bindy.annotation.CsvRecord;
import org.apache.camel.dataformat.bindy.annotation.DataField;

/**
 *
 * <AUTHOR>
 */
@CsvRecord(separator = "\\|",  crlf = "UNIX")
public class ProfissionalBind implements IBindVoExport<Profissional>{

    @DataField(pos = 1, required = true)
    private Long codigoSistema;
    @DataField(pos = 2, required = true)
    private Long versao;
    @DataField(pos = 3, required = true)
    private String nome;

    @Override
    public void buildProperties(Profissional vo) {
        codigoSistema = vo.getCodigo();
        versao = vo.getVersionAll();
        nome = vo.getNome();
    }
    
}
