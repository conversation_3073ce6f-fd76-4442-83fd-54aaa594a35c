package br.com.celk.services.mobile.integracao;

import br.com.celk.mobile.dto.RecursoDTO;
import br.com.celk.util.MobileCoreUtil;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.integracao.mobile.IntegracaoMobile;
import br.com.ksisolucoes.vo.integracao.mobile.IntegracaoMobileItem;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.io.File;
import java.io.IOException;
import java.util.Collections;

/**
 * <AUTHOR>
 */
public class SalvarRecursos extends AbstractCommandTransaction {

    private final IntegracaoMobile.TipoIntegracao tipoIntegracao;
    private final Long idProfissional;
    private final RecursoDTO dto;
    private IntegracaoMobile integracaoMobile;

    public SalvarRecursos(IntegracaoMobile.TipoIntegracao tipoIntegracao, Long idProfissional, RecursoDTO dto) {
        this.tipoIntegracao = tipoIntegracao;
        this.idProfissional = idProfissional;
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        carregarIntegracaoEmAberto();

        if (integracaoMobile == null) {
            return;
        }

        try {
            salvarArquivosRecursos();
        } catch (Exception e) {
            MobileCoreUtil.erro(integracaoMobile, ExceptionUtils.getStackTrace(e), "SalvarRecursos");
            BOFactory.save(integracaoMobile);
        }

    }

    private void salvarArquivosRecursos() throws IOException, ValidacaoException, DAOException {

        File file = MobileCoreUtil.toJsonFile(dto, dto.getNomeRecurso());

        GerenciadorArquivo gerenciadorArquivo = BOFactory.getBO(ComunicacaoFacade.class).enviarArquivoFtp(file, GerenciadorArquivo.OrigemArquivo.MOBILE_RECURSO.value());

        IntegracaoMobileItem integracaoMobileItem = new IntegracaoMobileItem();

        integracaoMobileItem.setIntegracaoMobile(integracaoMobile);
        integracaoMobileItem.setGerenciadorArquivo(gerenciadorArquivo);
        integracaoMobileItem.setNomeRecurso(dto.getNomeRecurso().replace("v2", ""));
        integracaoMobileItem.setStatus(0L);

        BOFactory.save(integracaoMobileItem);
    }

    private void carregarIntegracaoEmAberto(){
        integracaoMobile = LoadManager.getInstance(IntegracaoMobile.class)
                .addProperties(new HQLProperties(IntegracaoMobile.class).getProperties())
                .addProperties(VOUtils.montarPath(IntegracaoMobile.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                .addProperties(VOUtils.montarPath(IntegracaoMobile.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                .addProperties(VOUtils.montarPath(IntegracaoMobile.PROP_PROFISSIONAL, Profissional.PROP_REFERENCIA))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(IntegracaoMobile.PROP_PROFISSIONAL, Profissional.PROP_CODIGO), idProfissional))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(IntegracaoMobile.PROP_TIPO_INTEGRACAO), tipoIntegracao.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(IntegracaoMobile.PROP_STATUS),
                        BuilderQueryCustom.QueryParameter.IN, Collections.singletonList(IntegracaoMobile.Status.ABERTO.value())))
                .start()
                .getVO();
    }

}
