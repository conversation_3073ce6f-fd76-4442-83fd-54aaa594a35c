package br.com.celk.services.mobile.integracao.bindexportacao.tipoAtividadeEloPublico;

import br.com.celk.services.mobile.integracao.exportarrecurso.IBindVoExport;
import br.com.ksisolucoes.vo.esus.TipoAtividadeEloPublico;
import org.apache.camel.dataformat.bindy.annotation.CsvRecord;
import org.apache.camel.dataformat.bindy.annotation.DataField;

/**
 * <AUTHOR>
 */
@CsvRecord(separator = "\\|", crlf = "UNIX")
public class TipoAtividadeEloPublicoBind implements IBindVoExport<TipoAtividadeEloPublico> {

    @DataField(pos = 1, required = true)
    private Long codigoSistema;
    @DataField(pos = 2)
    private Long versao;
    @DataField(pos = 3)
    private Long cd_tipo_elo_publico;
    @DataField(pos = 4)
    private Long cd_publico_alvo;
    @DataField(pos = 5)
    private Long cd_tp_atv_grupo;

    @Override
    public void buildProperties(TipoAtividadeEloPublico vo) {
        codigoSistema = vo.getCodigo();
        versao = vo.getVersionAll();
        cd_tipo_elo_publico = vo.getCodigo();
        if (vo.getTipoAtividadePublico() != null) {
            cd_publico_alvo = vo.getTipoAtividadePublico().getCodigo();
        }
        if (vo.getTipoAtividadeGrupo() != null) {
            cd_tp_atv_grupo = vo.getTipoAtividadeGrupo().getCodigo();
        }
    }
}
