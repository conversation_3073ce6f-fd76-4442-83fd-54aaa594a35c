package br.com.celk.services.mobile.integracao.exportarrecurso;

import br.com.celk.integracao.IntegracaoRest;
import br.com.celk.services.mobile.integracao.bindexportacao.cepbrasil.CepBrasilBind;
import br.com.celk.services.mobile.integracao.bindexportacao.cidade.CidadeBind;
import br.com.celk.services.mobile.integracao.bindexportacao.comunidadeTradicional.ComunidadeTradicionalBind;
import br.com.celk.services.mobile.integracao.bindexportacao.dominioPaciente.DominioPacienteBind;
import br.com.celk.services.mobile.integracao.bindexportacao.empresa.EmpresaBind;
import br.com.celk.services.mobile.integracao.bindexportacao.enderecodomicilio.EnderecoDomicilioBind;
import br.com.celk.services.mobile.integracao.bindexportacao.enderecodomicilioesus.EnderecoDomicilioEsusBind;
import br.com.celk.services.mobile.integracao.bindexportacao.enderecousuariocadsus.EnderecoUsuarioCadsusBind;
import br.com.celk.services.mobile.integracao.bindexportacao.equipe.EquipeBind;
import br.com.celk.services.mobile.integracao.bindexportacao.equipemicroarea.EquipeMicroAreaBind;
import br.com.celk.services.mobile.integracao.bindexportacao.equipeprofissional.EquipeProfissionalBind;
import br.com.celk.services.mobile.integracao.bindexportacao.estado.EstadoBind;
import br.com.celk.services.mobile.integracao.bindexportacao.etniaindigena.EtniaIndigenaBind;
import br.com.celk.services.mobile.integracao.bindexportacao.fichanascidosvivos.NascidosVivosBind;
import br.com.celk.services.mobile.integracao.bindexportacao.integracaoexclusaomobile.IntegracaoExclusaoMobileBind;
import br.com.celk.services.mobile.integracao.bindexportacao.localAtividadeGrupo.LocalAtividadeGrupoBind;
import br.com.celk.services.mobile.integracao.bindexportacao.motivonaocomparecimento.MotivoNaoComparecimentoBind;
import br.com.celk.services.mobile.integracao.bindexportacao.notificacaoagendas.NotificacaoAgendasBind;
import br.com.celk.services.mobile.integracao.bindexportacao.orgaoemissor.OrgaoEmissorBind;
import br.com.celk.services.mobile.integracao.bindexportacao.pais.PaisBind;
import br.com.celk.services.mobile.integracao.bindexportacao.perguntapesquisa.PerguntaPesquisaBind;
import br.com.celk.services.mobile.integracao.bindexportacao.perguntaresposta.PerguntaRespostaBind;
import br.com.celk.services.mobile.integracao.bindexportacao.pesquisa.PesquisaBind;
import br.com.celk.services.mobile.integracao.bindexportacao.pesquisapergunta.PesquisaPerguntaBind;
import br.com.celk.services.mobile.integracao.bindexportacao.procedimentoEsus.ProcedimentoEsusBind;
import br.com.celk.services.mobile.integracao.bindexportacao.profissional.ProfissionalBind;
import br.com.celk.services.mobile.integracao.bindexportacao.profissionalCargaHoraria.ProfissionalCargaHorariaBind;
import br.com.celk.services.mobile.integracao.bindexportacao.raca.RacaBind;
import br.com.celk.services.mobile.integracao.bindexportacao.tabelacbo.TabelaCboBind;
import br.com.celk.services.mobile.integracao.bindexportacao.tipoAtividadeEloPublico.TipoAtividadeEloPublicoBind;
import br.com.celk.services.mobile.integracao.bindexportacao.tipoAtividadeEloTema.TipoAtividadeEloTemaBind;
import br.com.celk.services.mobile.integracao.bindexportacao.tipoAtividadeGrupo.TipoAtividadeGrupoBind;
import br.com.celk.services.mobile.integracao.bindexportacao.tipoAtividadePublico.TipoAtividadePublicoBind;
import br.com.celk.services.mobile.integracao.bindexportacao.tipoAtividadeTema.TipoAtividadeTemaBind;
import br.com.celk.services.mobile.integracao.bindexportacao.tipologradourocadsus.TipoLogradouroCadsusBind;
import br.com.celk.services.mobile.integracao.bindexportacao.tipovacina.TipoVacinaBind;
import br.com.celk.services.mobile.integracao.bindexportacao.usuario.UsuarioBind;
import br.com.celk.services.mobile.integracao.bindexportacao.usuariocadsus.UsuarioCadsusBind;
import br.com.celk.services.mobile.integracao.bindexportacao.usuariocadsuscns.UsuarioCadsusCnsBind;
import br.com.celk.services.mobile.integracao.bindexportacao.usuariocadsusdado.UsuarioCadsusDadoBind;
import br.com.celk.services.mobile.integracao.bindexportacao.usuariocadsusdocumento.UsuarioCadsusDocumentoBind;
import br.com.celk.services.mobile.integracao.bindexportacao.usuariocadsusesus.UsuarioCadsusEsusBind;
import br.com.celk.services.mobile.integracao.bindexportacao.usuariocadsusnotificacao.UsuarioCadsusNotificacaoBind;
import br.com.celk.services.mobile.integracao.bindexportacao.vacinaAplicacao.VacinaAplicacaoBind;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoRuntimeException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.agendamento.MotivoNaoComparecimento;
import br.com.ksisolucoes.vo.atividadegrupo.LocalAtividadeGrupo;
import br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupo;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.basico.pesquisa.PerguntaPesquisa;
import br.com.ksisolucoes.vo.basico.pesquisa.PerguntaResposta;
import br.com.ksisolucoes.vo.basico.pesquisa.Pesquisa;
import br.com.ksisolucoes.vo.basico.pesquisa.PesquisaPergunta;
import br.com.ksisolucoes.vo.cadsus.*;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.esus.*;
import br.com.ksisolucoes.vo.mobile.DominioPacienteMobile;
import br.com.ksisolucoes.vo.mobile.IntegracaoExclusaoMobile;
import br.com.ksisolucoes.vo.prontuario.basico.FichaNascidoVivo;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vacina.TipoVacina;
import br.com.ksisolucoes.vo.vacina.VacinaAplicacao;

import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public enum TypeExpEnum {
    
    REGISTROS_EXCLUIDOS("EXCLUSOES", IntegracaoExclusaoMobileBind.class, IntegracaoExclusaoMobile.class),
    CIDADE("CIDADE", CidadeBind.class, Cidade.class),
    ESTADO("ESTADO", EstadoBind.class, Estado.class),
    TIPO_LOGRADOURO("TIPO_LOGRADOURO", TipoLogradouroCadsusBind.class, TipoLogradouroCadsus.class),
    TIPO_ATIVIDADE_GRUPO("TIPO_ATIVIDADE_GRUPO", TipoAtividadeGrupoBind.class, TipoAtividadeGrupo.class),
    TIPO_ATIVIDADE_ELO_TEMA("TIPO_ATIVIDADE_ELO_TEMA", TipoAtividadeEloTemaBind.class, TipoAtividadeEloTema.class),
    TIPO_ATIVIDADE_TEMA("TIPO_ATIVIDADE_TEMA", TipoAtividadeTemaBind.class, TipoAtividadeTema.class),
    TIPO_ATIVIDADE_ELO_PUBLICO("TIPO_ATIVIDADE_ELO_PUBLICO", TipoAtividadeEloPublicoBind.class, TipoAtividadeEloPublico.class),
    TIPO_ATIVIDADE_PUBLICO("TIPO_ATIVIDADE_PUBLICO", TipoAtividadePublicoBind.class, TipoAtividadePublico.class),
    PROCEDIMENTO_ESUS("PROCEDIMENTO_ESUS", ProcedimentoEsusBind.class, ProcedimentoEsus.class),
    EMPRESA("EMPRESA", EmpresaBind.class, Empresa.class),
    LOCAL_ATIVIDADE_GRUPO("LOCAL_ATIVIDADE_GRUPO", LocalAtividadeGrupoBind.class, LocalAtividadeGrupo.class),
    PROFISSIONAL_CARGA_HORARIA("PROFISSIONAL_CARGA_HORARIA", ProfissionalCargaHorariaBind.class, ProfissionalCargaHoraria.class),
    PAIS("PAIS", PaisBind.class, Pais.class),
    RACA("RACA", RacaBind.class, Raca.class),
    USUARIO_CADSUS("PACIENTE", UsuarioCadsusBind.class, UsuarioCadsus.class),
    USUARIO_CADSUS_CNS("CNS", UsuarioCadsusCnsBind.class, UsuarioCadsusCns.class),
    ORGAO_EMISSOR("ORGAO_EMISSOR", OrgaoEmissorBind.class, OrgaoEmissor.class),
    USUARIO_CADSUS_DOCUMENTO("DOCUMENTOS", UsuarioCadsusDocumentoBind.class, UsuarioCadsusDocumento.class),
    TABELA_CBO("CBO", TabelaCboBind.class, TabelaCbo.class),
    PACIENTE_ESUS("PACIENTE_ESUS", UsuarioCadsusEsusBind.class, UsuarioCadsusEsus.class, Arrays.asList(UsuarioCadsusEsus.PROP_USUARIO_CADSUS, VOUtils.montarPath(UsuarioCadsusEsus.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_TABELA_CBO))),
    USUARIOS("USUARIOS", UsuarioBind.class, Usuario.class),
    PROFISSIONAL("PROFISSIONAL", ProfissionalBind.class, Profissional.class),
    MICRO_AREA("MICRO_AREA", EquipeMicroAreaBind.class, EquipeMicroArea.class),
    EQUIPE("EQUIPE", EquipeBind.class, Equipe.class),
    EQUIPE_PROFISSIONAL("EQUIPE_PROFISSIONAL", EquipeProfissionalBind.class, EquipeProfissional.class),
    ENDERECO("ENDERECO", EnderecoUsuarioCadsusBind.class, EnderecoUsuarioCadsus.class),
    DOMICILIO("DOMICILIO", EnderecoDomicilioBind.class, EnderecoDomicilio.class, Arrays.asList(EnderecoDomicilio.PROP_ENDERECO_USUARIO_CADSUS)),
    DOMICILIO_ESUS("DOMICILIO_ESUS", EnderecoDomicilioEsusBind.class, EnderecoDomicilioEsus.class),
    USUARIO_CADSUS_DADO("PACIENTE_DADO", UsuarioCadsusDadoBind.class, UsuarioCadsusDado.class),
    ETNIA_INDIGENA("ETNIA_INDIGENA", EtniaIndigenaBind.class, EtniaIndigena.class),
    COMUNIDADE_TRADICIONAL("COMUNIDADE_TRADICIONAL", ComunidadeTradicionalBind.class, ComunidadeTradicional.class),
    NOTIFICACAO_PACIENTE("NOTIFICACAO_PACIENTE", UsuarioCadsusNotificacaoBind.class, UsuarioCadsusNotificacao.class),
    NOTIFICACAO_AGENDA("NOTIFICACAO_AGENDA", NotificacaoAgendasBind.class, NotificacaoAgendas.class, Arrays.asList(NotificacaoAgendas.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, VOUtils.montarPath(NotificacaoAgendas.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_LOCAL_AGENDAMENTO), VOUtils.montarPath(NotificacaoAgendas.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_TIPO_PROCEDIMENTO))),
    MOTIVO_NAO_COMPARECIMENTO("MOTIVO_NAO_COMPARECIMENTO", MotivoNaoComparecimentoBind.class, MotivoNaoComparecimento.class),
    TIPO_VACINA("TIPO_VACINA", TipoVacinaBind.class, TipoVacina.class),
    PESQUISA("PESQUISA", PesquisaBind.class, Pesquisa.class),
    PERGUNTA_PESQUISA("PERGUNTA_PESQUISA", PerguntaPesquisaBind.class, PerguntaPesquisa.class),
    PERGUNTA_RESPOSTA("PERGUNTA_RESPOSTA", PerguntaRespostaBind.class, PerguntaResposta.class),
    PESQUISA_PERGUNTA("PESQUISA_PERGUNTA", PesquisaPerguntaBind.class, PesquisaPergunta.class),
    DOMINIO_PACIENTE("DOMINIO_PACIENTE", DominioPacienteBind.class, DominioPacienteMobile.class),
    CEP_BRASIL("CEP_BRASIL", CepBrasilBind.class, CepBrasil.class),
    NASCIDOS_VIVOS("NASCIDOS_VIVOS", NascidosVivosBind.class, FichaNascidoVivo.class),
    VAC_APLICACAO("VAC_APLICACAO", VacinaAplicacaoBind.class, VacinaAplicacao.class);


    //<editor-fold defaultstate="collapsed" desc="Variaveis locais">
    private final String name;
    private final Class<? extends IBindVoExport> bindClass;
    private final Class voClass;
    private String propVersionAll;
    private List<String> firstLayerFetchProps;
    private static boolean global;
    //</editor-fold>

    //<editor-fold defaultstate="collapsed" desc="Construtores">
    TypeExpEnum(String name, Class<? extends IBindVoExport> bindClass, Class voClass, List firstLayerFetchProps) {
        this.name = name;
        this.bindClass = bindClass;
        this.voClass = voClass;
        this.firstLayerFetchProps = firstLayerFetchProps;
    }

    TypeExpEnum(String name, Class<? extends IBindVoExport> bindClass, Class voClass) {
        this.name = name;
        this.bindClass = bindClass;
        this.voClass = voClass;
    }
    //</editor-fold>

    //<editor-fold defaultstate="collapsed" desc="Metodos publicos">

    /**
     * Nome do recurso a ser exportado
     * @return o nome do recurso
     */
    public String getName() {
        return name;
    }

    /**
     * A propriedade que representa o versionAll
     * @return a propriedade versionAll
     */
    public String getPropVersionAll() {
        if(propVersionAll == null){
            IntegracaoRest a = (IntegracaoRest) voClass.getAnnotation(IntegracaoRest.class);
            if(a == null){
                throw new ValidacaoRuntimeException(Bundle.getStringApplication("msg_annotation_integracao_mobile", name));
            }   
            propVersionAll = a.versionAll();
        }
        return propVersionAll;
    }


    public boolean isGlobal(){
        return global;
    }

    public List<String> getFirstLayerFetchProps() {
        return firstLayerFetchProps;
    }

    public Class<? extends IBindVoExport> getBindClass() {
        return bindClass;
    }

    public Class getVoClass() {
        return voClass;
    }
    
    public static TypeExpEnum resolveType(String nomeRecurso){
        for (TypeExpEnum typeEnum : values()) {
            if(typeEnum.name.equals(Coalesce.asString(nomeRecurso).toUpperCase())){
                global = typeEnum.equals(DOMINIO_PACIENTE);
                return typeEnum;
            }
        }
        return null;
    }
    //</editor-fold>
}