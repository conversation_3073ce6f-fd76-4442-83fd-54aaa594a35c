package br.com.celk.services.mobile.integracao.importarrecurso;

import br.com.celk.bo.service.mobile.interfaces.facade.MobileFacade;
import br.com.celk.integracao.IntegracaoRest;
import br.com.celk.integracao.dto.ResourceProcessDTO;
import br.com.celk.services.mobile.integracao.bindimportacao.retorno.RetornoBind;
import br.com.celk.util.MobileCoreUtil;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Reflection;
import br.com.ksisolucoes.util.StringUtilKsi;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoRuntimeException;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import org.apache.axis.utils.StringUtils;
import org.apache.camel.Exchange;
import org.apache.camel.Message;
import org.apache.camel.Processor;
import org.apache.camel.TypeConversionException;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.hibernate.Session;

import javax.ejb.EJBTransactionRolledbackException;
import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ItemProcessor implements Processor {

    private final TypeImpEnum typeEnum;
    private final Session session;
    private final String ipClient;
    private Long codigoIntegracaoItem;
    private Long codigoLote;

    public ItemProcessor(TypeImpEnum typeEnum, Session session, String ipClient, Long codigoIntegracaoItem, Long codigoLote) {
        this.typeEnum = typeEnum;
        this.session = session;
        this.ipClient = ipClient;
        this.codigoIntegracaoItem = codigoIntegracaoItem;
        this.codigoLote = codigoLote;
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        //Não ficar processando no caso da transação estiver inativa, Rollback.
        if (!session.getTransaction().isActive()) return;

        RetornoBind retornoBind = new RetornoBind();
        Object vo;
        try {
            Message in = exchange.getIn();
            Map<String, Object> modelMap = (Map<String, Object>) in.getBody();
            IBindVoImp item = (IBindVoImp) modelMap.get(typeEnum.getBindClass().getName());
            in.setBody(item);

            retornoBind.setCodigoMobile(item.getCodigoMobile());
            if (codigoIntegracaoItem == null) {
                MobileCoreUtil.createApplicationSession(ipClient);
            }

            vo = in.getBody(typeEnum.getConvertedClass());
            vo = BOFactory.getBO(MobileFacade.class).importarCustomCommand(item.customProcess(vo));

            IntegracaoRest annotation = vo.getClass().getAnnotation(IntegracaoRest.class);
            if (annotation == null) {
                throw new ValidacaoRuntimeException(Bundle.getStringApplication("msg_annotation_integracao_mobile", vo.getClass().getSimpleName()));
            }

            session.flush();
            session.clear();

            retornoBind.setCodigoSistema((Long) ((CodigoManager) vo).getCodigoManager());
            retornoBind.setStatus(1L);
            retornoBind.setVersaoSistema((Long) Reflection.getValueByPattern(vo, annotation.versionAll()));

        } catch (Exception ex) {
            Loggable.log.error(ex);
            retornoBind = ajustarErro(retornoBind, typeEnum.getName().toLowerCase().replace("V2", ""), ex);
        }

        exchange.getOut().setBody(retornoBind);
    }

    private RetornoBind ajustarErro(RetornoBind retornoBind, String nomeRecurso, Throwable ex) throws ValidacaoException, DAOException, IOException {
        retornoBind.setStatus(0L);

        StringBuilder mensagem = new StringBuilder();

        if (ex instanceof ValidacaoException) {
            mensagem.append(ex.getMessage());
        } else {
            StringBuilder sb = new StringBuilder();

            if (ex.getCause() != null) {
                sb.append(ExceptionUtils.getStackTrace(ex.getCause()));
            }

            sb.append(ExceptionUtils.getStackTrace(ex));
            mensagem.append(sb);
        }

        String s = StringUtil.removeHtmlString(mensagem.toString()).replaceAll("\n", ", ");
        retornoBind.setMensagem(s);

        if (codigoIntegracaoItem != null && !StringUtils.isEmpty(retornoBind.getMensagem())) {
            ResourceProcessDTO dto = ResourceProcessDTO.ResourceProcessDTOBuilder
                    .aResourceProcessDTO()
                    .withMsg(retornoBind.getMensagem())
                    .withCodigoMobile(retornoBind.getCodigoMobile())
                    .withNomeRecurso(nomeRecurso)
                    .withCodigoIntegracao(codigoIntegracaoItem)
                    .withCodigoLote(codigoLote)
                    .build();

            MobileCoreUtil.saveError(dto);
        }

        return retornoBind;
    }
}
