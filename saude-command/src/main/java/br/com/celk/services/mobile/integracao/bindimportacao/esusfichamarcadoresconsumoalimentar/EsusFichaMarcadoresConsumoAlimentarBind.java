package br.com.celk.services.mobile.integracao.bindimportacao.esusfichamarcadoresconsumoalimentar;

import br.com.celk.services.mobile.integracao.importarrecurso.DefaultBindVoImp;
import br.com.celk.services.mobile.integracao.importarrecurso.ImpUtil;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.ImportacaoMobileCommand;
import br.com.ksisolucoes.system.sessao.AbstractSessaoAplicacao;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.DAORuntimeException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Equipe;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaMarcadoresConsumoAlimentar;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import org.apache.camel.dataformat.bindy.annotation.CsvRecord;
import org.apache.camel.dataformat.bindy.annotation.DataField;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

/**
 * <AUTHOR>
 */
@CsvRecord(separator = "\\|", crlf = "UNIX")
public class EsusFichaMarcadoresConsumoAlimentarBind extends DefaultBindVoImp<EsusFichaMarcadoresConsumoAlimentar> {


    //Atrubutos do CSV
    @DataField(pos = 1, required = true)
    private Long codigoUsuario;
    @DataField(pos = 2, required = true)
    private Long codigoMobile;

    @DataField(pos = 3, required = true)
    private Long empresa;
    @DataField(pos = 4, required = true)
    private Long codigoProfissional;
    @DataField(pos = 5, required = true)
    private String codigoCBO; //Pegar no saude server
    @DataField(pos = 6, required = true)
    private String codigoINE; //Pegar no saude server
    @DataField(pos = 7, pattern = "yyyy-MM-dd HH:mm:ss", required = true)
    private Date dataDoAtendimento;

    @DataField(pos = 8)
    private Long codigoNumeroCartao; //Pegar no saude server
    @DataField(pos = 9)
    private Long codigoUsuarioCadSUS; //Pegar no saude server
    @DataField(pos = 10, pattern = "dd/MM/yyyy", required = true)
    private Date dataDeNascimento;
    @DataField(pos = 11)
    private char sexo;
    @DataField(pos = 12)
    private Long localAtendimento;
    @DataField(pos = 13)
    private Long leitePeito1;
    @DataField(pos = 14)
    private Long mingau;

    @DataField(pos = 15)
    private Long aguaCha;
    @DataField(pos = 16)
    private Long leiteVaca;
    @DataField(pos = 17)
    private Long formulaInfantil;
    @DataField(pos = 18)
    private Long sucoFruta;
    @DataField(pos = 19)
    private Long fruta;
    @DataField(pos = 20)
    private Long comidaSal;
    @DataField(pos = 21)
    private Long outrosAlimentosEBebidas;

    @DataField(pos = 22)
    private Long refeicoesUsandoTv;
    @DataField(pos = 23)
    private Long refeicoeslongodia;
    @DataField(pos = 24)
    private Long feijao14;
    @DataField(pos = 25)
    private Long frutasFrescas;
    @DataField(pos = 26)
    private Long verdurasLegumes;
    @DataField(pos = 27)
    private Long hamburguerEmbutidos17;

    @DataField(pos = 28)
    private Long bebidasAdocadas18;
    @DataField(pos = 29)
    private Long macarraoSalgadinhosBiscoitos19;
    @DataField(pos = 30)
    private Long biscoitoDocesGuloseimas20;
    @DataField(pos = 31)
    private Long leitePeito21;
    @DataField(pos = 32)
    private Long frutaInteiraPedacoAmassado;
    @DataField(pos = 33)
    private Long seComeuFutaQuantas;
    @DataField(pos = 34)
    private Long comeuComidaSal;
    @DataField(pos = 35)
    private Long seComeuComidaSalQuantas;

    @DataField(pos = 36)
    private Long seSimComidaFoiOferecida;
    @DataField(pos = 37)
    private Long leiteNaoDoPeito;
    @DataField(pos = 38)
    private Long mingauLeite;
    @DataField(pos = 39)
    private Long iogurte;
    @DataField(pos = 40)
    private Long legumes;
    @DataField(pos = 41)
    private Long vegetalFrutasAlaranjada;

    @DataField(pos = 42)
    private Long verduraFolha;
    @DataField(pos = 43)
    private Long carne;
    @DataField(pos = 44)
    private Long figado;
    @DataField(pos = 45)
    private Long feijao36;
    @DataField(pos = 46)
    private Long arrozBatataMacarrao;
    @DataField(pos = 47)
    private Long hamburguerEmbutidos38;
    @DataField(pos = 48)
    private Long bebidasAdocadas39;

    @DataField(pos = 49)
    private Long macarraoSalgadinhosBiscoitos40;
    @DataField(pos = 50)
    private Long biscoitoDocesGuloseimas41;
    @DataField(pos = 51)
    private Long cns;
    @DataField(pos = 52, pattern = "yyyy-MM-dd HH:mm:ss", required = true)
    private Date dataCadastro;
    @DataField(pos = 53)
    private String uuidPaciente;

    @Override
    public Class getClassVo() {
        return EsusFichaMarcadoresConsumoAlimentar.class;
    }

    //Converte uma linha do CSV do RECURSO (tabela) EsusFichaMarcadoresConsumoAlimentar em objeto e salva no banco
    @Override
    public EsusFichaMarcadoresConsumoAlimentar customProperties(EsusFichaMarcadoresConsumoAlimentar vo, ImpUtil impUtil) {
        //*Carrega a sessao para pegar dados de empresa, do funcionario e etc ..
        AbstractSessaoAplicacao sessao = SessaoAplicacaoImp.getInstance();

        //*Trata a datas
        Calendar c = GregorianCalendar.getInstance();
        c.setTime(dataDoAtendimento);
        vo.setDataAtendimento(c.getTime());

        c.setTime(dataCadastro);
        vo.setDataCadastro(c.getTime());

        c.setTime(dataDeNascimento);
        vo.setDataNascimento(c.getTime());
        c = null;

        //*Codigos FK
        //Empresa
        vo.setEmpresa(sessao.getEmpresa());
        //Profissional
        vo.setProfissional(sessao.getUsuario().getProfissional());
        //CBO
        TabelaCbo cbo = sessao.getUsuario().getProfissional().getCboProfissional(sessao.getEmpresa());
        vo.setCbo(cbo);
        //Usuario cadsus

        UsuarioCadsus usuarioCadsus = null;

        if (Coalesce.asLong(codigoUsuarioCadSUS, 0L) != 0) {
            usuarioCadsus = LoadManager.getInstance(UsuarioCadsus.class)
                    .addProperties(UsuarioCadsus.PROP_CODIGO)
                    .addProperty(UsuarioCadsus.PROP_SEXO)
                    .addProperty(UsuarioCadsus.PROP_NOME)
                    .addProperty(UsuarioCadsus.PROP_DATA_NASCIMENTO)
                    .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_CODIGO, codigoUsuarioCadSUS))
                    .start().getVO();
        }

        if (usuarioCadsus == null && uuidPaciente != null) {
            List<UsuarioCadsus> usuarioCadsusList = LoadManager.getInstance(UsuarioCadsus.class)
                    .addProperties(UsuarioCadsus.PROP_CODIGO)
                    .addProperty(UsuarioCadsus.PROP_SEXO)
                    .addProperty(UsuarioCadsus.PROP_NOME)
                    .addProperty(UsuarioCadsus.PROP_DATA_NASCIMENTO)
                    .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_UUID_TABLET, uuidPaciente))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(usuarioCadsusList)) {
                usuarioCadsus = usuarioCadsusList.get(0);
            }
        }

        if (usuarioCadsus == null) {
            UsuarioCadsusCns usuarioCadsusCns = LoadManager.getInstance(UsuarioCadsusCns.class)
                    .addProperty(UsuarioCadsusCns.PROP_NUMERO_CARTAO)
                    .addProperty(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_SEXO))
                    .addProperty(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME))
                    .addProperty(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_DATA_NASCIMENTO))
                    .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusCns.PROP_NUMERO_CARTAO, cns))
                    .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusCns.PROP_EXCLUIDO, RepositoryComponentDefault.NAO_EXCLUIDO))
                    .setMaxResults(1)
                    .start().getVO();

            if (usuarioCadsusCns != null) {
                usuarioCadsus = usuarioCadsusCns.getUsuarioCadsus();
            }
        }

        if (usuarioCadsus == null) {
            throw new DAORuntimeException(Bundle.getStringApplication("msg_ficha_marcadores_consumo_alimentar_paciente_nao_encontrado", cns));
        }

        List<EquipeProfissional> equipeProfissionalList = LoadManager.getInstance(EquipeProfissional.class)
                .addProperty(EquipeProfissional.PROP_CODIGO)
                .addProperty(EquipeProfissional.PROP_EQUIPE)
                .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_CNES))
                .addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_PROFISSIONAL, codigoProfissional))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA), empresa))
                .addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_STATUS, EquipeProfissional.STATUS_ATIVO))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(equipeProfissionalList)) {
            vo.setCodigoIne(equipeProfissionalList.get(0).getEquipe().getEquipeCnes());
        }

        if (usuarioCadsus != null) {
            vo.setUsuarioCadsus(usuarioCadsus);
            //Conver sexo do usuario de F ou M para 1 ou 0
            vo.setSexo(usuarioCadsus.getSexo().equals("F") ? new Long(1) : new Long(0));
            vo.setNomeUsuarioCadsus(usuarioCadsus.getNome());
        }

        vo.setNumeroCartao(cns);
        vo.setLocalAtendimento(localAtendimento);
        vo.setLeitePeito1(leitePeito1);
        vo.setMingau(mingau);
        vo.setAguaCha(aguaCha);
        vo.setLeiteVaca(leiteVaca);
        vo.setFormulaInfantil(formulaInfantil);
        vo.setSucoFruta(sucoFruta);
        vo.setFruta(fruta);
        vo.setComidaSal(comidaSal);
        vo.setOutrosAlimentosBebidas(outrosAlimentosEBebidas);
        vo.setRefeicoesUsandoTv(refeicoesUsandoTv);
        vo.setRefeicoesLongoDia(refeicoeslongodia);
        vo.setFeijao14(feijao14);
        vo.setFrutasFrescas(frutasFrescas);
        vo.setVerdurasLegumes(verdurasLegumes);
        vo.setHamburguerEmbutidos17(hamburguerEmbutidos17);
        vo.setBebidasAdocadas18(bebidasAdocadas18);
        vo.setMacarraoSalgadinhosBiscoitos19(macarraoSalgadinhosBiscoitos19);
        vo.setBiscoitoDocesGuloseimas20(biscoitoDocesGuloseimas20);
        vo.setLeitePeito21(leitePeito21);
        vo.setFrutaInteiraPedacoAmassado(frutaInteiraPedacoAmassado);
        vo.setSeComeuFrutaQuantas(seComeuFutaQuantas);
        vo.setComeuComidaSal(comeuComidaSal);
        vo.setSeComeuComidaSalQuantas(seComeuComidaSalQuantas);
        vo.setSeSimComidaFoiOferecida(seSimComidaFoiOferecida);
        vo.setLeiteNaoDoPeito(leiteNaoDoPeito);
        vo.setMingauLeite(mingauLeite);
        vo.setIogurte(iogurte);
        vo.setLegumes(legumes);
        vo.setVegetalFrutasAlaranjada(vegetalFrutasAlaranjada);
        vo.setVerduraFolha(verduraFolha);
        vo.setCarne(carne);
        vo.setFigado(figado);
        vo.setFeijao36(feijao36);
        vo.setArrozBatataMacarrao(arrozBatataMacarrao);
        vo.setHamburguerEmbutidos38(hamburguerEmbutidos38);
        vo.setBebidasAdocadas39(bebidasAdocadas39);
        vo.setMacarraoSalgadinhosBiscoitos40(macarraoSalgadinhosBiscoitos40);
        vo.setBiscoitoDocesGuloseimas41(biscoitoDocesGuloseimas41);
        return vo;
    }

    @Override
    public ImportacaoMobileCommand customProcess(EsusFichaMarcadoresConsumoAlimentar vo) throws Exception {
        return new ImportarEsusFichaMarcadoresConsumoAlimentar(vo);
    }

    @Override
    public Long getCodigoMobile() {
        return codigoMobile;
    }

    public void setCodigoMobile(Long codigoMobile) {
        this.codigoMobile = codigoMobile;
    }

    @Override
    public Long getCodigoSistema() {
        return null;
    }

    @Override
    public Long getCodigoUsuario() {
        return codigoUsuario;
    }

    public void setCodigoUsuario(Long codigoUsuario) {
        this.codigoUsuario = codigoUsuario;
    }

    public Long getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Long empresa) {
        this.empresa = empresa;
    }

    public Long getCodigoProfissional() {
        return codigoProfissional;
    }

    public void setCodigoProfissional(Long codigoProfissional) {
        this.codigoProfissional = codigoProfissional;
    }

    public String getCodigoCBO() {
        return codigoCBO;
    }

    public void setCodigoCBO(String codigoCBO) {
        this.codigoCBO = codigoCBO;
    }

    public String getCodigoINE() {
        return codigoINE;
    }

    public void setCodigoINE(String codigoINE) {
        this.codigoINE = codigoINE;
    }

    public Date getDataDoAtendimento() {
        return dataDoAtendimento;
    }

    public void setDataDoAtendimento(Date dataDoAtendimento) {
        this.dataDoAtendimento = dataDoAtendimento;
    }

    public Long getCodigoNumeroCartao() {
        return codigoNumeroCartao;
    }

    public void setCodigoNumeroCartao(Long codigoNumeroCartao) {
        this.codigoNumeroCartao = codigoNumeroCartao;
    }

    public Long getCodigoUsuarioCadSUS() {
        return codigoUsuarioCadSUS;
    }

    public void setCodigoUsuarioCadSUS(Long codigoUsuarioCadSUS) {
        this.codigoUsuarioCadSUS = codigoUsuarioCadSUS;
    }

    public Date getDataDeNascimento() {
        return dataDeNascimento;
    }

    public void setDataDeNascimento(Date dataDeNascimento) {
        this.dataDeNascimento = dataDeNascimento;
    }

    public char getSexo() {
        return sexo;
    }

    public void setSexo(char sexo) {
        this.sexo = sexo;
    }

    public Long getLocalAtendimento() {
        return localAtendimento;
    }

    public void setLocalAtendimento(Long localAtendimento) {
        this.localAtendimento = localAtendimento;
    }

    public Long getLeitePeito1() {
        return leitePeito1;
    }

    public void setLeitePeito1(Long leitePeito1) {
        this.leitePeito1 = leitePeito1;
    }

    public Long getMingau() {
        return mingau;
    }

    public void setMingau(Long mingau) {
        this.mingau = mingau;
    }

    public Long getAguaCha() {
        return aguaCha;
    }

    public void setAguaCha(Long aguaCha) {
        this.aguaCha = aguaCha;
    }

    public Long getLeiteVaca() {
        return leiteVaca;
    }

    public void setLeiteVaca(Long leiteVaca) {
        this.leiteVaca = leiteVaca;
    }

    public Long getFormulaInfantil() {
        return formulaInfantil;
    }

    public void setFormulaInfantil(Long formulaInfantil) {
        this.formulaInfantil = formulaInfantil;
    }

    public Long getSucoFruta() {
        return sucoFruta;
    }

    public void setSucoFruta(Long sucoFruta) {
        this.sucoFruta = sucoFruta;
    }

    public Long getFruta() {
        return fruta;
    }

    public void setFruta(Long fruta) {
        this.fruta = fruta;
    }

    public Long getComidaSal() {
        return comidaSal;
    }

    public void setComidaSal(Long comidaSal) {
        this.comidaSal = comidaSal;
    }

    public Long getOutrosAlimentosEBebidas() {
        return outrosAlimentosEBebidas;
    }

    public void setOutrosAlimentosEBebidas(Long outrosAlimentosEBebidas) {
        this.outrosAlimentosEBebidas = outrosAlimentosEBebidas;
    }

    public Long getRefeicoesUsandoTv() {
        return refeicoesUsandoTv;
    }

    public void setRefeicoesUsandoTv(Long refeicoesUsandoTv) {
        this.refeicoesUsandoTv = refeicoesUsandoTv;
    }

    public Long getRefeicoeslongodia() {
        return refeicoeslongodia;
    }

    public void setRefeicoeslongodia(Long refeicoeslongodia) {
        this.refeicoeslongodia = refeicoeslongodia;
    }

    public Long getFeijao14() {
        return feijao14;
    }

    public void setFeijao14(Long feijao14) {
        this.feijao14 = feijao14;
    }

    public Long getFrutasFrescas() {
        return frutasFrescas;
    }

    public void setFrutasFrescas(Long frutasFrescas) {
        this.frutasFrescas = frutasFrescas;
    }

    public Long getVerdurasLegumes() {
        return verdurasLegumes;
    }

    public void setVerdurasLegumes(Long verdurasLegumes) {
        this.verdurasLegumes = verdurasLegumes;
    }

    public Long getHamburguerEmbutidos17() {
        return hamburguerEmbutidos17;
    }

    public void setHamburguerEmbutidos17(Long hamburguerEmbutidos17) {
        this.hamburguerEmbutidos17 = hamburguerEmbutidos17;
    }

    public Long getBebidasAdocadas18() {
        return bebidasAdocadas18;
    }

    public void setBebidasAdocadas18(Long bebidasAdocadas18) {
        this.bebidasAdocadas18 = bebidasAdocadas18;
    }

    public Long getMacarraoSalgadinhosBiscoitos19() {
        return macarraoSalgadinhosBiscoitos19;
    }

    public void setMacarraoSalgadinhosBiscoitos19(Long macarraoSalgadinhosBiscoitos19) {
        this.macarraoSalgadinhosBiscoitos19 = macarraoSalgadinhosBiscoitos19;
    }

    public Long getBiscoitoDocesGuloseimas20() {
        return biscoitoDocesGuloseimas20;
    }

    public void setBiscoitoDocesGuloseimas20(Long biscoitoDocesGuloseimas20) {
        this.biscoitoDocesGuloseimas20 = biscoitoDocesGuloseimas20;
    }

    public Long getLeitePeito21() {
        return leitePeito21;
    }

    public void setLeitePeito21(Long leitePeito21) {
        this.leitePeito21 = leitePeito21;
    }

    public Long getFrutaInteiraPedacoAmassado() {
        return frutaInteiraPedacoAmassado;
    }

    public void setFrutaInteiraPedacoAmassado(Long frutaInteiraPedacoAmassado) {
        this.frutaInteiraPedacoAmassado = frutaInteiraPedacoAmassado;
    }

    public Long getSeComeuFutaQuantas() {
        return seComeuFutaQuantas;
    }

    public void setSeComeuFutaQuantas(Long seComeuFutaQuantas) {
        this.seComeuFutaQuantas = seComeuFutaQuantas;
    }

    public Long getComeuComidaSal() {
        return comeuComidaSal;
    }

    public void setComeuComidaSal(Long comeuComidaSal) {
        this.comeuComidaSal = comeuComidaSal;
    }

    public Long getSeComeuComidaSalQuantas() {
        return seComeuComidaSalQuantas;
    }

    public void setSeComeuComidaSalQuantas(Long seComeuComidaSalQuantas) {
        this.seComeuComidaSalQuantas = seComeuComidaSalQuantas;
    }

    public Long getSeSimComidaFoiOferecida() {
        return seSimComidaFoiOferecida;
    }

    public void setSeSimComidaFoiOferecida(Long seSimComidaFoiOferecida) {
        this.seSimComidaFoiOferecida = seSimComidaFoiOferecida;
    }

    public Long getLeiteNaoDoPeito() {
        return leiteNaoDoPeito;
    }

    public void setLeiteNaoDoPeito(Long leiteNaoDoPeito) {
        this.leiteNaoDoPeito = leiteNaoDoPeito;
    }

    public Long getMingauLeite() {
        return mingauLeite;
    }

    public void setMingauLeite(Long mingauLeite) {
        this.mingauLeite = mingauLeite;
    }

    public Long getIogurte() {
        return iogurte;
    }

    public void setIogurte(Long iogurte) {
        this.iogurte = iogurte;
    }

    public Long getLegumes() {
        return legumes;
    }

    public void setLegumes(Long legumes) {
        this.legumes = legumes;
    }

    public Long getVegetalFrutasAlaranjada() {
        return vegetalFrutasAlaranjada;
    }

    public void setVegetalFrutasAlaranjada(Long vegetalFrutasAlaranjada) {
        this.vegetalFrutasAlaranjada = vegetalFrutasAlaranjada;
    }

    public Long getVerduraFolha() {
        return verduraFolha;
    }

    public void setVerduraFolha(Long verduraFolha) {
        this.verduraFolha = verduraFolha;
    }

    public Long getCarne() {
        return carne;
    }

    public void setCarne(Long carne) {
        this.carne = carne;
    }

    public Long getFigado() {
        return figado;
    }

    public void setFigado(Long figado) {
        this.figado = figado;
    }

    public Long getFeijao36() {
        return feijao36;
    }

    public void setFeijao36(Long feijao36) {
        this.feijao36 = feijao36;
    }

    public Long getArrozBatataMacarrao() {
        return arrozBatataMacarrao;
    }

    public void setArrozBatataMacarrao(Long arrozBatataMacarrao) {
        this.arrozBatataMacarrao = arrozBatataMacarrao;
    }

    public Long getHamburguerEmbutidos38() {
        return hamburguerEmbutidos38;
    }

    public void setHamburguerEmbutidos38(Long hamburguerEmbutidos38) {
        this.hamburguerEmbutidos38 = hamburguerEmbutidos38;
    }

    public Long getBebidasAdocadas39() {
        return bebidasAdocadas39;
    }

    public void setBebidasAdocadas39(Long bebidasAdocadas39) {
        this.bebidasAdocadas39 = bebidasAdocadas39;
    }

    public Long getMacarraoSalgadinhosBiscoitos40() {
        return macarraoSalgadinhosBiscoitos40;
    }

    public void setMacarraoSalgadinhosBiscoitos40(Long macarraoSalgadinhosBiscoitos40) {
        this.macarraoSalgadinhosBiscoitos40 = macarraoSalgadinhosBiscoitos40;
    }

    public Long getBiscoitoDocesGuloseimas41() {
        return biscoitoDocesGuloseimas41;
    }

    public void setBiscoitoDocesGuloseimas41(Long biscoitoDocesGuloseimas41) {
        this.biscoitoDocesGuloseimas41 = biscoitoDocesGuloseimas41;
    }

    public Long getCns() {
        return cns;
    }

    public void setCns(Long cns) {
        this.cns = cns;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }
}
