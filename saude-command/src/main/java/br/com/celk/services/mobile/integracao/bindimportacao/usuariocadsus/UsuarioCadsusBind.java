package br.com.celk.services.mobile.integracao.bindimportacao.usuariocadsus;

import br.com.celk.services.mobile.integracao.importarrecurso.ImpUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.ImportacaoMobileCommand;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Pais;
import br.com.ksisolucoes.vo.cadsus.*;

import java.util.Date;
import java.util.List;

import org.apache.camel.dataformat.bindy.annotation.CsvRecord;
import org.apache.camel.dataformat.bindy.annotation.DataField;
import org.apache.commons.collections.CollectionUtils;

/**
 *
 * <AUTHOR>
 */
@CsvRecord(separator = "\\|", crlf = "UNIX")
public class UsuarioCadsusBind extends br.com.celk.services.mobile.integracao.importarrecurso.DefaultBindVoImp<UsuarioCadsus> {

    @DataField(pos = 1)
    private Long codigoSistema;
    @DataField(pos = 2, required = true)
    private Long codigoMobile;
    @DataField(pos = 3, required = true)
    private Long codigoUsuario;
    @DataField(pos = 4, required = true)
    private String excluido;
    @DataField(pos = 5, required = true)
    private String nome;
    @DataField(pos = 6, required = true, pattern = "dd-MM-yyyy")
    private Date dataNascimento;
    @DataField(pos = 7, required = true)
    private String sexo;
    @DataField(pos = 8)
    private String nomeMae;
    @DataField(pos = 9)
    private Long cidadeNascimento;
    @DataField(pos = 10)
    private String cpf;
    @DataField(pos = 11)
    private String rg;
    @DataField(pos = 12)
    private String telefone;
    @DataField(pos = 13)
    private String celular;
    @DataField(pos = 14)
    private Long raca;
    @DataField(pos = 15)
    private Long paisNascimento;
    @DataField(pos = 16)
    private String email;
    @DataField(pos = 17)
    private String apelido;
    @DataField(pos = 18)
    private Long flagResponsavelFamiliar;
    @DataField(pos = 19)
    private Long codigoResponsavelFamiliar;
    @DataField(pos = 20)
    private Long nacionalidade;
    @DataField(pos = 21)
    private Long domicilio;

    @DataField(pos = 22)
    private String telefone2;
    @DataField(pos = 23)
    private String telefone3;
    @DataField(pos = 24)
    private String telefone4;
    @DataField(pos = 25)
    private String religiao;
    @DataField(pos = 26)
    private String localTrabalho;
    @DataField(pos = 27)
    private String telefoneTrabalho;
    @DataField(pos = 28)
    private String responsavel;
    @DataField(pos = 29)
    private String parentescoResponsavel;
    @DataField(pos = 30)
    private String urgenciaNome;
    @DataField(pos = 31)
    private String urgenciaTelefone;
    @DataField(pos = 32)
    private String urgenciaParentesco;

    @DataField(pos = 33)
    private Long rendaFamiliar;
    @DataField(pos = 34, pattern = "dd-MM-yyyy")
    private Date resideDesde;
    @DataField(pos = 35)
    private Long situacao;
    @DataField(pos = 36)
    private String nis;
    @DataField(pos = 37)
    private String prontuario;
    @DataField(pos = 38)
    private Long motivoExclusaoDomicilio;
    @DataField(pos = 39)
    private Long motivoExclusao;
    @DataField(pos = 40)
    private Long etniaIndigena;
    @DataField(pos = 41)
    private String uuidTablet;
    @DataField(pos = 42)
    private String uuidResponsavel;
    @DataField(pos = 43)
    private String uuidDomicilio;
    @DataField(pos = 44)
    private Long flagNomeSocial;

    @Override
    public Long getCodigoMobile() {
        return codigoMobile;
    }

    @Override
    public Class getClassVo() {
        return UsuarioCadsus.class;
    }

    @Override
    public Long getCodigoSistema() {
        return codigoSistema;
    }

    @Override
    public UsuarioCadsus customProperties(UsuarioCadsus vo, ImpUtil impUtil) {
        vo = impUtil.reloadUUIDTablet(UsuarioCadsus.class, uuidTablet, codigoSistema, vo);

        vo.setExcluido(excluido.equals(RepositoryComponentDefault.SIM) ? RepositoryComponentDefault.SIM_LONG : RepositoryComponentDefault.NAO_LONG);
        vo.setNome(nome);
        vo.setDataNascimento(dataNascimento);
        vo.setSexo(sexo);
        vo.setNomeMae(nomeMae);
        vo.setCidadeNascimento(impUtil.loadOnlyId(Cidade.class, cidadeNascimento));
        vo.setCpf(cpf);
        vo.setRg(rg);
        vo.setTelefone(telefone);
        vo.setCelular(celular);
        vo.setRaca(impUtil.loadOnlyId(Raca.class, raca));
        vo.setEtniaIndigena(impUtil.loadOnlyId(EtniaIndigena.class, etniaIndigena));
        vo.setPaisNascimento(impUtil.loadOnlyId(Pais.class, paisNascimento));
        vo.setEmail(email);
        vo.setApelido(apelido);
        vo.setFlagResponsavelFamiliar(flagResponsavelFamiliar);

        vo.setUtilizaNomeSocial(flagNomeSocial);

        if (codigoResponsavelFamiliar !=null) {
            vo.setResponsavelFamiliar(impUtil.loadOnlyId(UsuarioCadsus.class, codigoResponsavelFamiliar));
        } else if (uuidResponsavel != null) {
            List<UsuarioCadsus> usuarioCadsus = LoadManager.getInstance(UsuarioCadsus.class)
                    .addProperties(UsuarioCadsus.PROP_CODIGO)
                    .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_UUID_TABLET, uuidResponsavel))
                    .start().getList();
            if (CollectionUtils.isNotEmpty(usuarioCadsus)) {
                log(usuarioCadsus, impUtil, uuidTablet);
                vo.setResponsavelFamiliar(usuarioCadsus.get(0));
            }
        }

        vo.setNacionalidade(nacionalidade != null ? nacionalidade : UsuarioCadsus.Nacionalidade.BRASILEIRO.value());

        if (domicilio != null) {
            vo.setEnderecoDomicilio(impUtil.loadOnlyId(EnderecoDomicilio.class, domicilio));
        } else if (uuidDomicilio != null) {
            EnderecoDomicilio enderecoDomicilio = LoadManager.getInstance(EnderecoDomicilio.class)
                    .addProperties(EnderecoDomicilio.PROP_CODIGO)
                    .addParameter(new QueryCustom.QueryCustomParameter(EnderecoDomicilio.PROP_UUID_TABLET, uuidDomicilio))
                    .start().getVO();
            vo.setEnderecoDomicilio(enderecoDomicilio);
        }
        vo.setSituacao(UsuarioCadsus.SITUACAO_ATIVO);

        vo.setTelefone2(telefone2);
        vo.setTelefone3(telefone3);
        vo.setTelefone4(telefone4);
        vo.setReligiao(religiao);
        vo.setLocalTrabalho(localTrabalho);
        vo.setTelefoneTrabalho(telefoneTrabalho);
        vo.setResponsavel(responsavel);
        vo.setParentescoResponsavel(parentescoResponsavel);
        vo.setUrgenciaChamar(urgenciaNome);
        vo.setTelefoneUrgencia(urgenciaTelefone);
        vo.setGrauParentescoUrgencia(urgenciaParentesco);

        vo.setRendaFamiliar(rendaFamiliar);
        vo.setResideDesde(resideDesde);
        vo.setSituacao(situacao);
        vo.setNis(nis);
        vo.setProntuarioDomicilioTmp(prontuario);
        vo.setMotivoExclusaoDomicilioTmp(motivoExclusaoDomicilio);
        vo.setMotivoExclusao(motivoExclusao);
        vo.setUuidTablet(uuidTablet);

        validarCelular(celular, vo);
        return vo;
    }

    @Override
    public ImportacaoMobileCommand customProcess(UsuarioCadsus convertedVo) throws Exception {
        return new ImportarUsuarioCadsus(convertedVo);
    }

    public void validarCelular(String celular, UsuarioCadsus vo) {

        if (null != celular && celular.length() >= 10 && celular.length() <= 11) {

            if (celular.charAt(2) != '9'
                    && celular.charAt(2) != '8'
                    && celular.charAt(2) != '7') {

                if (vo.getTelefone() == null) {
                    vo.setTelefone(celular);
                    vo.setCelular(null);
                } else if (vo.getTelefone2() == null) {
                    vo.setTelefone2(celular);
                    vo.setCelular(null);
                } else if (vo.getTelefone3() == null) {
                    vo.setTelefone3(celular);
                    vo.setCelular(null);
                } else if (vo.getTelefone4() == null) {
                    vo.setTelefone4(celular);
                    vo.setCelular(null);
                } else {
                    vo.setCelular(null);
                }
            }
        }
    }

    @Override
    public Long getCodigoUsuario() {
        return codigoUsuario;
    }

    public void setCodigoSistema(Long codigoSistema) {
        this.codigoSistema = codigoSistema;
    }

    public void setCodigoMobile(Long codigoMobile) {
        this.codigoMobile = codigoMobile;
    }

    public void setCodigoUsuario(Long codigoUsuario) {
        this.codigoUsuario = codigoUsuario;
    }

    public String getExcluido() {
        return excluido;
    }

    public void setExcluido(String excluido) {
        this.excluido = excluido;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getNomeMae() {
        return nomeMae;
    }

    public void setNomeMae(String nomeMae) {
        this.nomeMae = nomeMae;
    }

    public Long getCidadeNascimento() {
        return cidadeNascimento;
    }

    public void setCidadeNascimento(Long cidadeNascimento) {
        this.cidadeNascimento = cidadeNascimento;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getRg() {
        return rg;
    }

    public void setRg(String rg) {
        this.rg = rg;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getCelular() {
        return celular;
    }

    public void setCelular(String celular) {
        this.celular = celular;
    }

    public Long getRaca() {
        return raca;
    }

    public void setRaca(Long raca) {
        this.raca = raca;
    }

    public Long getPaisNascimento() {
        return paisNascimento;
    }

    public void setPaisNascimento(Long paisNascimento) {
        this.paisNascimento = paisNascimento;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getApelido() {
        return apelido;
    }

    public void setApelido(String apelido) {
        this.apelido = apelido;
    }

    public Long getFlagResponsavelFamiliar() {
        return flagResponsavelFamiliar;
    }

    public void setFlagResponsavelFamiliar(Long flagResponsavelFamiliar) {
        this.flagResponsavelFamiliar = flagResponsavelFamiliar;
    }

    public Long getCodigoResponsavelFamiliar() {
        return codigoResponsavelFamiliar;
    }

    public void setCodigoResponsavelFamiliar(Long codigoResponsavelFamiliar) {
        this.codigoResponsavelFamiliar = codigoResponsavelFamiliar;
    }

    public Long getNacionalidade() {
        return nacionalidade;
    }

    public void setNacionalidade(Long nacionalidade) {
        this.nacionalidade = nacionalidade;
    }

    public Long getDomicilio() {
        return domicilio;
    }

    public void setDomicilio(Long domicilio) {
        this.domicilio = domicilio;
    }

    public String getTelefone2() {
        return telefone2;
    }

    public void setTelefone2(String telefone2) {
        this.telefone2 = telefone2;
    }

    public String getTelefone3() {
        return telefone3;
    }

    public void setTelefone3(String telefone3) {
        this.telefone3 = telefone3;
    }

    public String getTelefone4() {
        return telefone4;
    }

    public void setTelefone4(String telefone4) {
        this.telefone4 = telefone4;
    }

    public String getReligiao() {
        return religiao;
    }

    public void setReligiao(String religiao) {
        this.religiao = religiao;
    }

    public String getLocalTrabalho() {
        return localTrabalho;
    }

    public void setLocalTrabalho(String localTrabalho) {
        this.localTrabalho = localTrabalho;
    }

    public String getTelefoneTrabalho() {
        return telefoneTrabalho;
    }

    public void setTelefoneTrabalho(String telefoneTrabalho) {
        this.telefoneTrabalho = telefoneTrabalho;
    }

    public String getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(String responsavel) {
        this.responsavel = responsavel;
    }

    public String getParentescoResponsavel() {
        return parentescoResponsavel;
    }

    public void setParentescoResponsavel(String parentescoResponsavel) {
        this.parentescoResponsavel = parentescoResponsavel;
    }

    public String getUrgenciaNome() {
        return urgenciaNome;
    }

    public void setUrgenciaNome(String urgenciaNome) {
        this.urgenciaNome = urgenciaNome;
    }

    public String getUrgenciaTelefone() {
        return urgenciaTelefone;
    }

    public void setUrgenciaTelefone(String urgenciaTelefone) {
        this.urgenciaTelefone = urgenciaTelefone;
    }

    public String getUrgenciaParentesco() {
        return urgenciaParentesco;
    }

    public void setUrgenciaParentesco(String urgenciaParentesco) {
        this.urgenciaParentesco = urgenciaParentesco;
    }

    public Long getRendaFamiliar() {
        return rendaFamiliar;
    }

    public void setRendaFamiliar(Long rendaFamiliar) {
        this.rendaFamiliar = rendaFamiliar;
    }

    public Date getResideDesde() {
        return resideDesde;
    }

    public void setResideDesde(Date resideDesde) {
        this.resideDesde = resideDesde;
    }

    public Long getSituacao() {
        return situacao;
    }

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }

    public String getNis() {
        return nis;
    }

    public void setNis(String nis) {
        this.nis = nis;
    }

    public String getProntuario() {
        return prontuario;
    }

    public void setProntuario(String prontuario) {
        this.prontuario = prontuario;
    }

    public Long getMotivoExclusaoDomicilio() {
        return motivoExclusaoDomicilio;
    }

    public void setMotivoExclusaoDomicilio(Long motivoExclusaoDomicilio) {
        this.motivoExclusaoDomicilio = motivoExclusaoDomicilio;
    }

    public Long getMotivoExclusao() {
        return motivoExclusao;
    }

    public void setMotivoExclusao(Long motivoExclusao) {
        this.motivoExclusao = motivoExclusao;
    }

    public Long getEtniaIndigena() {
        return etniaIndigena;
    }

    public void setEtniaIndigena(Long etniaIndigena) {
        this.etniaIndigena = etniaIndigena;
    }

    public String getUuidTablet() {
        return uuidTablet;
    }

    public void setUuidTablet(String uuidTablet) {
        this.uuidTablet = uuidTablet;
    }

    public String getUuidResponsavel() {
        return uuidResponsavel;
    }

    public void setUuidResponsavel(String uuidResponsavel) {
        this.uuidResponsavel = uuidResponsavel;
    }

    public String getUuidDomicilio() {
        return uuidDomicilio;
    }

    public void setUuidDomicilio(String uuidDomicilio) {
        this.uuidDomicilio = uuidDomicilio;
    }

    public Long getFlagNomeSocial() {
        return flagNomeSocial;
    }

    public void setFlagNomeSocial(Long flagNomeSocial) {
        this.flagNomeSocial = flagNomeSocial;
    }
}
