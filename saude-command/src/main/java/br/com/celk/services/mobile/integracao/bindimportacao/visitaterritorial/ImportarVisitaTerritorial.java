package br.com.celk.services.mobile.integracao.bindimportacao.visitaterritorial;

import br.com.celk.services.mobile.integracao.importarrecurso.DefaultImportacaoCommand;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Equipe;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.cadsus.MotivoVisitaDomiciliar;
import br.com.ksisolucoes.vo.cadsus.VisitaDomiciliar;
import br.com.ksisolucoes.vo.cadsus.VisitaTerritorial;
import org.hibernate.criterion.Order;
import org.hibernate.sql.JoinType;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ImportarVisitaTerritorial extends DefaultImportacaoCommand<VisitaDomiciliar> {

    private final VisitaTerritorialBind bind;
    private final VisitaTerritorial visitaTerritorial = new VisitaTerritorial();

    public ImportarVisitaTerritorial(VisitaDomiciliar object, VisitaTerritorialBind bind) {
        super(object);
        this.bind = bind;
    }

    @Override
    protected VisitaDomiciliar execute(VisitaDomiciliar visitaDomiciliar) throws DAOException, ValidacaoException {

        visitaTerritorial.setTipoImovel(bind.getTipoImovel());
        visitaTerritorial.setCep(bind.getCep());
        visitaTerritorial.setLogradouro(bind.getLogradouro());
        visitaTerritorial.setBairro(bind.getBairro());
        visitaTerritorial.setPontoReferencia(bind.getPontoReferencia());
        visitaTerritorial.setForaArea(bind.getForaArea());
        visitaTerritorial.setEquipeMicroArea(bind.getEquipeMicroArea());

        List<MotivoVisitaDomiciliar> motivosSelecionados = new ArrayList<MotivoVisitaDomiciliar>();

        List<MotivoVisitaDomiciliar> motivos = getSession().createCriteria(MotivoVisitaDomiciliar.class)
                .addOrder(Order.asc(MotivoVisitaDomiciliar.PROP_INTEGRACAO))
                .list();

        Map<Long, MotivoVisitaDomiciliar> map = new HashMap<Long, MotivoVisitaDomiciliar>();
        for (MotivoVisitaDomiciliar motivoVisitaDomiciliar : motivos) {
            map.put(motivoVisitaDomiciliar.getIntegracao(), motivoVisitaDomiciliar);
        }
        if (RepositoryComponentDefault.SIM_LONG.equals(bind.getMotivoAtualizacao())) {
            motivosSelecionados.add(map.get(MotivoVisitaDomiciliar.Motivo.CADASTRAMENTO_ATUALIZACAO.value()));
        }
        if (RepositoryComponentDefault.SIM_LONG.equals(bind.getMotivoCampanhaSaude())) {
            motivosSelecionados.add(map.get(MotivoVisitaDomiciliar.Motivo.CONVITE_ATIVIDADES_COLETIVAS_CAMPANHA_SAUDE.value()));
        }
        if (RepositoryComponentDefault.SIM_LONG.equals(bind.getMotivoPrevencao())) {
            motivosSelecionados.add(map.get(MotivoVisitaDomiciliar.Motivo.ORIENTACAO_PREVENCAO.value()));
        }
        if (RepositoryComponentDefault.SIM_LONG.equals(bind.getMotivoOutros())) {
            motivosSelecionados.add(map.get(MotivoVisitaDomiciliar.Motivo.OUTROS.value()));
        }
        if (CollectionUtils.isEmpty(motivosSelecionados)) {
            motivosSelecionados.add(map.get(MotivoVisitaDomiciliar.Motivo.OUTROS.value()));
        }
        if (RepositoryComponentDefault.SIM_LONG.equals(Long.valueOf(bind.getMotivoImovelComFoco()))) {
            motivosSelecionados.add(map.get(MotivoVisitaDomiciliar.Motivo.IMOVEL_FOCO.value()));
        }
        if (RepositoryComponentDefault.SIM_LONG.equals(Long.valueOf(bind.getMotivoAcaoMecanica()))) {
            motivosSelecionados.add(map.get(MotivoVisitaDomiciliar.Motivo.ACAO_MECANICA.value()));
        }
        if (RepositoryComponentDefault.SIM_LONG.equals(Long.valueOf(bind.getMotivoTratamentoFocal()))) {
            motivosSelecionados.add(map.get(MotivoVisitaDomiciliar.Motivo.TRATAMENTO_FOCAL.value()));
        }
        if (RepositoryComponentDefault.SIM_LONG.equals(bind.getMotivoAcaoEducativa())) {
            motivosSelecionados.add(map.get(MotivoVisitaDomiciliar.Motivo.ACAO_EDUCATIVA.value()));
        }

        List<EquipeProfissional> equipeProfissionalList = getSession().createCriteria(EquipeProfissional.class)
                .add(Restrictions.eq(EquipeProfissional.PROP_PROFISSIONAL, visitaDomiciliar.getProfissional()))
                .add(Restrictions.eq(EquipeProfissional.PROP_STATUS, EquipeProfissional.STATUS_ATIVO))
                .createCriteria(EquipeProfissional.PROP_EQUIPE, JoinType.LEFT_OUTER_JOIN)
                .add(Restrictions.eq(Equipe.PROP_EMPRESA, visitaDomiciliar.getEmpresa())).list();

        if (CollectionUtils.isNotNullEmpty(equipeProfissionalList)) {
            visitaDomiciliar.setEquipeMicroArea(equipeProfissionalList.get(0).getEquipeMicroArea());
            visitaDomiciliar.setEquipe(equipeProfissionalList.get(0).getEquipe());
        } else {
            throw new ValidacaoException(Bundle.getStringApplication("msg_profissional_nao_esta_relacionado_area"));
        }

        return BOFactory.getBO(UsuarioCadsusFacade.class).cadastrarVisitaDomiciliar(visitaDomiciliar, motivosSelecionados, this.visitaTerritorial);
    }
}
