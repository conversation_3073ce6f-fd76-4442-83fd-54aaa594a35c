package br.com.celk.services.mobile.integracao.bindimportacao.usuario;

import br.com.celk.services.mobile.integracao.importarrecurso.DefaultBindVoImp;
import br.com.celk.services.mobile.integracao.importarrecurso.ImpUtil;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.camel.dataformat.bindy.annotation.CsvRecord;
import org.apache.camel.dataformat.bindy.annotation.DataField;

import java.util.Date;

@CsvRecord(separator = "\\|", crlf = "UNIX")
public class UsuarioBind extends DefaultBindVoImp<Usuario> {

    @DataField(pos = 1, required = true)
    private Long codigoSistema;
    @DataField(pos = 2)
    private Long codigoMobile;
    @DataField(pos = 3)
    private Long codigoUsuario;
    @DataField(pos = 4, pattern = "dd-MM-yyyy HH:mm:ss")
    private Date dataAceiteTermoUsoMobile;

    @Override
    public Usuario customProperties(Usuario vo, ImpUtil impUtil) {
        vo = impUtil.loadVo(Usuario.class, codigoSistema);
        vo.setDataAceiteTermoUsoMobile(dataAceiteTermoUsoMobile);

        if (dataAceiteTermoUsoMobile == null) {
            vo.setAceiteTermoUsoMobile(RepositoryComponentDefault.NAO_LONG);
        } else {
            vo.setAceiteTermoUsoMobile(RepositoryComponentDefault.SIM_LONG);
        }

        return vo;
    }

    @Override
    public Class getClassVo() {
        return Usuario.class;
    }

    @Override
    public Long getCodigoMobile() {
        return codigoMobile;
    }

    @Override
    public Long getCodigoSistema() {
        return codigoSistema;
    }

    public void setCodigoSistema(Long codigoSistema) {
        this.codigoSistema = codigoSistema;
    }

    @Override
    public Long getCodigoUsuario() {
        return codigoUsuario;
    }

    public Date getDataAceiteTermoUsoMobile() {
        return dataAceiteTermoUsoMobile;
    }

    public void setDataAceiteTermoUsoMobile(Date dataAceiteTermoUsoMobile) {
        this.dataAceiteTermoUsoMobile = dataAceiteTermoUsoMobile;
    }
}
