/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.celk.services.frotamobile;

import br.com.celk.bo.service.rest.frota.VeiculoRestDTO;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.frota.Veiculo;
import com.google.common.base.Function;
import com.google.common.collect.Collections2;
import com.google.common.collect.ImmutableList;
import java.util.List;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class QueryExportarVeiculos extends CommandQuery<QueryExportarVeiculos>{
    
    private List<VeiculoRestDTO> result;

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
           this.result = null;
        List<Veiculo> veiculos = LoadManager.getInstance(Veiculo.class)
                .addProperty(Veiculo.PROP_CODIGO)
                .addProperty(Veiculo.PROP_DESCRICAO)
                .addProperty(Veiculo.PROP_PLACA)
                .addProperty(Veiculo.PROP_FABRICANTE)
                .addProperty(Veiculo.PROP_KM)
                .start().getList();
        
        this.result = ImmutableList.<VeiculoRestDTO> copyOf(Collections2.transform(veiculos, new Function<Veiculo, VeiculoRestDTO>(){
            @Override
            public VeiculoRestDTO apply(Veiculo f) {
                return VeiculoRestDTO.of(f.getCodigo(), f.getDescricao(), f.getPlaca(), f.getFabricante(), f.getKm());
            }
        }));
    }

    @Override
    public List<VeiculoRestDTO> getResult() {
        return this.result; 
    }
    
    
}
