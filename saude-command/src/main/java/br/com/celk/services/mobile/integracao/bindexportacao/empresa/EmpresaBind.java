package br.com.celk.services.mobile.integracao.bindexportacao.empresa;

import br.com.celk.services.mobile.integracao.exportarrecurso.IBindVoExport;
import br.com.ksisolucoes.vo.basico.Empresa;
import org.apache.camel.dataformat.bindy.annotation.CsvRecord;
import org.apache.camel.dataformat.bindy.annotation.DataField;

/**
 * <AUTHOR>
 */
@CsvRecord(separator = "\\|", crlf = "UNIX")
public class EmpresaBind implements IBindVoExport<Empresa> {

    @DataField(pos = 1, required = true)
    private Long codigoSistema;
    @DataField(pos = 2)
    private Long versao;
    @DataField(pos = 3)
    private Long empresa;
    @DataField(pos = 4)
    private String descricao;

    @Override
    public void buildProperties(Empresa vo) {
        codigoSistema = vo.getCodigo();
        versao = vo.getVersionAll();
        empresa = vo.getCodigo();
        descricao = vo.getDescricao();
    }
}
