package br.com.celk.services.rest;

import br.com.celk.integracao.IntegracaoRest;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoRuntimeException;
import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class ConsultarRecurso extends AbstractCommandTransaction{

    private String voName;
    private Object retorno;
    private String id;
    private Integer versao;

    public ConsultarRecurso(String voName, String id, Integer versao) {
        this.voName = voName;
        this.id = id;
        this.versao = versao;
    }
            
    @Override
    public void execute() throws DAOException, ValidacaoException {
        try {
            Class clazz = BOFactory.getBO(CommomFacade.class).resolveEntityClass(voName);
            if(clazz == null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_recurso_desconhecido"));
            }

            LoadManager load = LoadManager.getInstance(clazz);
            if(id != null){
                    Class identifierClass = BOFactory.getBO(CommomFacade.class).resolveEntityIdentifierClass(clazz);
                    Object param = (Serializable) identifierClass.getConstructor(String.class).newInstance(id);
                    load.setId((Serializable) param);
            }
            if(versao != null){
                IntegracaoRest a = (IntegracaoRest) clazz.getAnnotation(IntegracaoRest.class);
                if(a == null){
                    throw new ValidacaoRuntimeException(Bundle.getStringApplication("msg_annotation_integracao_mobile", clazz.getSimpleName()));
                }   

                load.addParameter(new QueryCustom.QueryCustomParameter(a.versionAll(), BuilderQueryCustom.QueryParameter.MAIOR, versao));
            }
            retorno = load.start().getList();
        } catch (NoSuchMethodException ex) {
            new DAOException(ex.getMessage(), ex);
        } catch (SecurityException ex) {
            new DAOException(ex.getMessage(), ex);
        } catch (InstantiationException ex) {
            new DAOException(ex.getMessage(), ex);
        } catch (IllegalAccessException ex) {
            new DAOException(ex.getMessage(), ex);
        } catch (IllegalArgumentException ex) {
            new DAOException(ex.getMessage(), ex);
        } catch (InvocationTargetException ex) {
            new DAOException(ex.getMessage(), ex);
        }
    }

    public Object getRetorno() {
        return retorno;
    }
}
