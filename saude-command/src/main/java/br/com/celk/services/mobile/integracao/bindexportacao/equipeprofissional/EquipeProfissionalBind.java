package br.com.celk.services.mobile.integracao.bindexportacao.equipeprofissional;

import br.com.celk.services.mobile.integracao.exportarrecurso.IBindVoExport;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import org.apache.camel.dataformat.bindy.annotation.CsvRecord;
import org.apache.camel.dataformat.bindy.annotation.DataField;

/**
 *
 * <AUTHOR>
 */
@CsvRecord(separator = "\\|",  crlf = "UNIX")
public class EquipeProfissionalBind implements IBindVoExport<EquipeProfissional>{

    @DataField(pos = 1, required = true)
    private Long codigoSistema;
    @DataField(pos = 2, required = true)
    private Long versao;
    @DataField(pos = 3)
    private Long profissional;
    @DataField(pos = 4)
    private Long equipeMicroarea;
    @DataField(pos = 5)
    private Long equipe;
    @DataField(pos = 6)
    private Long status;

    @Override
    public void buildProperties(EquipeProfissional vo) {
        codigoSistema = vo.getCodigo();
        versao = vo.getVersionAll();
        status = vo.getStatus();
        if(vo.getProfissional()!= null){
            profissional = vo.getProfissional().getCodigo();
        }
        if(vo.getEquipeMicroArea()!= null){
            equipeMicroarea = vo.getEquipeMicroArea().getCodigo();
        }
        if(vo.getEquipe()!= null){
            equipe = vo.getEquipe().getCodigo();
        }
    }
    
}
