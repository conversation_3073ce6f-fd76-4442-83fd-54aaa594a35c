package br.com.celk.services.mobile.integracao.bindimportacao.notificacaoAgendas;

import br.com.celk.services.mobile.integracao.importarrecurso.DefaultBindVoImp;
import br.com.celk.services.mobile.integracao.importarrecurso.DefaultImportacaoCommand;
import br.com.celk.services.mobile.integracao.importarrecurso.ImpUtil;
import br.com.ksisolucoes.command.ImportacaoMobileCommand;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.agendamento.MotivoNaoComparecimento;
import br.com.ksisolucoes.vo.cadsus.NotificacaoAgendas;
import org.apache.camel.dataformat.bindy.annotation.CsvRecord;
import org.apache.camel.dataformat.bindy.annotation.DataField;

/**
 *
 * <AUTHOR>
 */
@CsvRecord(separator = "\\|",  crlf = "UNIX")
public class NotificacaoAgendasBind extends DefaultBindVoImp<NotificacaoAgendas>{

    private static final Integer ABERTO = 0;//nunca é retornado
    private static final Integer COMPARECEU = 1;
    private static final Integer NAO_COMPARECEU = 2;
    
    @DataField(pos = 1)
    private Long codigoSistema;
    @DataField(pos = 2, required = true)
    private Long codigoMobile;
    @DataField(pos = 3, required = true)
    private Long codigoUsuario;
    @DataField(pos = 4)
    private Integer statusMobile;
    @DataField(pos = 5)
    private Long codigoMotivo;
    @DataField(pos = 6)
    private String descricaoOutroMotivo;

    @Override
    public NotificacaoAgendas customProperties(NotificacaoAgendas vo, ImpUtil impUtil) {
        AgendaGradeAtendimentoHorario agah = impUtil.loadVo(AgendaGradeAtendimentoHorario.class, vo.getAgendaGradeAtendimentoHorario().getCodigo());
        if(COMPARECEU.equals(statusMobile)){
            agah.setStatus(AgendaGradeAtendimentoHorario.STATUS_CONCLUIDO);
        }else{
            agah.setStatus(AgendaGradeAtendimentoHorario.STATUS_NAO_COMPARECEU);
            if(codigoMotivo != null){
                agah.setMotivoNaoComparecimento(impUtil.loadOnlyId(MotivoNaoComparecimento.class, codigoMotivo));
            }
            agah.setOutroMotivo(descricaoOutroMotivo);
        }
        vo.setAgendaGradeAtendimentoHorario(agah);

        vo.setStatus(NotificacaoAgendas.Status.EM_DIA.value());
        return vo;
    }

    @Override
    public ImportacaoMobileCommand customProcess(NotificacaoAgendas convertedVo) throws Exception {
        return new DefaultImportacaoCommand<NotificacaoAgendas>(convertedVo) {
            @Override
            protected NotificacaoAgendas execute(NotificacaoAgendas na) throws DAOException, ValidacaoException {
                BOFactory.save(na.getAgendaGradeAtendimentoHorario());
                BOFactory.save(na);

                return na;
            }
        };
    }

    @Override
    public Long getCodigoMobile() {
        return codigoMobile;
    }

    @Override
    public Class getClassVo() {
        return NotificacaoAgendas.class;
    }

    @Override
    public Long getCodigoSistema() {
        return codigoSistema;
    }

    @Override
    public Long getCodigoUsuario() {
        return codigoUsuario;
    }

    public static Integer getABERTO() {
        return ABERTO;
    }

    public static Integer getCOMPARECEU() {
        return COMPARECEU;
    }

    public static Integer getNaoCompareceu() {
        return NAO_COMPARECEU;
    }

    public void setCodigoSistema(Long codigoSistema) {
        this.codigoSistema = codigoSistema;
    }

    public void setCodigoMobile(Long codigoMobile) {
        this.codigoMobile = codigoMobile;
    }

    public void setCodigoUsuario(Long codigoUsuario) {
        this.codigoUsuario = codigoUsuario;
    }

    public Integer getStatusMobile() {
        return statusMobile;
    }

    public void setStatusMobile(Integer statusMobile) {
        this.statusMobile = statusMobile;
    }

    public Long getCodigoMotivo() {
        return codigoMotivo;
    }

    public void setCodigoMotivo(Long codigoMotivo) {
        this.codigoMotivo = codigoMotivo;
    }

    public String getDescricaoOutroMotivo() {
        return descricaoOutroMotivo;
    }

    public void setDescricaoOutroMotivo(String descricaoOutroMotivo) {
        this.descricaoOutroMotivo = descricaoOutroMotivo;
    }
}
