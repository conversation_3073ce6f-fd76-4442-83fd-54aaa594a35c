package br.com.celk.bnafar;

import br.com.ksisolucoes.bo.command.dynamodb.UsuariosBnafar;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.AwsUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB;
import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.amazonaws.services.dynamodbv2.model.DeleteItemRequest;
import com.amazonaws.services.dynamodbv2.model.PutItemRequest;


import java.util.HashMap;

public class ArmazenarCredenciaisBnafar extends AbstractCommandTransaction<ProcessarRetornoBnafar> {

   private UsuariosBnafar usuariosBnafar;

    public ArmazenarCredenciaisBnafar(UsuariosBnafar usuariosBnafar) {
        this.usuariosBnafar = usuariosBnafar;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        AmazonDynamoDB dynamoDB = AwsUtils.getDynamoDbClient();
        //excluir registro dno dynamo
        HashMap<String, AttributeValue> keyToGet = new HashMap<>();
        keyToGet.put("tenant", new AttributeValue(usuariosBnafar.getTenant()));
        DeleteItemRequest deleteReq = new DeleteItemRequest("usuarios_bnafar", keyToGet);
        dynamoDB.deleteItem(deleteReq);
        //incluir registro no dynamo
        HashMap<String,AttributeValue> itemValues = new HashMap<>();
        itemValues.put("tenant", new AttributeValue(usuariosBnafar.getTenant()));
        itemValues.put("codigo_ibge", new AttributeValue(usuariosBnafar.getCodigoIbge()));
        itemValues.put("login", new AttributeValue(usuariosBnafar.getLogin()));
        itemValues.put("senha", new AttributeValue(usuariosBnafar.getSenha()));
        PutItemRequest request = new PutItemRequest("usuarios_bnafar", itemValues);
        dynamoDB.putItem(request);
    }
}
