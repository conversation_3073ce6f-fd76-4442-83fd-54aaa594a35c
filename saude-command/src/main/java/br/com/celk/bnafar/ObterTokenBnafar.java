package br.com.celk.bnafar;

import br.com.celk.system.SystemHelper;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import java.util.Base64;

public class ObterTokenBnafar extends AbstractCommandTransaction<ProcessarRetornoBnafar> {

    public static final String AUTH = "jwtauth/auth";


    private String login;
    private String senha;

    private Boolean retorno = false;

    public ObterTokenBnafar(String login, String senha) {
        this.login = login;
        this.senha = senha;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            String DOMAIN = SystemHelper.isProducao() ? "https://servicos.saude.gov.br" : "http://servicoshm.saude.gov.br";
            HttpPost httpPost = new HttpPost(postMethod(DOMAIN, AUTH));
            String auth = login.concat(":").concat(senha);
            String base64Auth = Base64.getEncoder().encodeToString(auth.getBytes());
            httpPost.setHeader("Authorization", "Basic ".concat(base64Auth));

            CloseableHttpResponse response = httpClient.execute(httpPost);

            int statusCode = response.getStatusLine().getStatusCode();

            setRetorno(statusCode == 200);

        } catch (Exception e) {
            setRetorno(false);
            Loggable.log.error(e.getMessage());
        }
    }


    public String getLogin() {
        return login;
    }

    public void setLogin(String login) {
        this.login = login;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public Boolean getRetorno() {
        return retorno;
    }

    public void setRetorno(Boolean retorno) {
        this.retorno = retorno;
    }

    private String postMethod(String baseLink, String path) {
        return String.format("%s/%s", baseLink, path);
    }

}
