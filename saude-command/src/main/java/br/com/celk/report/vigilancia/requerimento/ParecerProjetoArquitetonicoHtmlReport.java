package br.com.celk.report.vigilancia.requerimento;

import br.com.celk.report.HtmlReport;
import br.com.celk.report.HtmlReportTemplateType;
import br.com.celk.report.templatebuilder.IHtmlTemplateBuilder;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ImpressaoParecerProjetoArquitetonicoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

import java.util.Collection;

public class ParecerProjetoArquitetonicoHtmlReport extends HtmlReport {

    private IHtmlTemplateBuilder template;
    private HtmlTemplateUtil templateUtil;
    private ImpressaoParecerProjetoArquitetonicoDTO impressaoParecerProjetoArquitetonicoDTO;
    public ParecerProjetoArquitetonicoHtmlReport(Collection<ImpressaoParecerProjetoArquitetonicoDTO> impressaoParecerProjetoArquitetonicoDTOList) {
        super(HtmlReportTemplateType.CAB_ROD_TIMPRADO_VIGILANCIA);
        this.template = HtmlReportTemplateType.PARECER_PROJETO_ARQUITETONICO_VIGILANCIA.templateBuilderInstance();
        this.templateUtil = new HtmlTemplateUtil(this.template);
        this.impressaoParecerProjetoArquitetonicoDTO = impressaoParecerProjetoArquitetonicoDTOList.iterator().next();
    }

    @Override
    public void customizeReport(IHtmlTemplateBuilder templateBuilder) {
        templateUtil.preencherCampo("titulo", impressaoParecerProjetoArquitetonicoDTO.getDescricaoStatus().toUpperCase(), "");
        templateUtil.preencherCampo("rotulo_protocolo", Bundle.getStringApplication("rotulo_protocolo").toUpperCase() + ": " +
                impressaoParecerProjetoArquitetonicoDTO.getRequerimentoVigilancia().getProtocoloFormatado(), "");
        templateUtil.preencherCampo("rotulo_data_cadastro_abv", Bundle.getStringApplication("rotulo_data_cadastro_abv").toUpperCase() + ": " +
                Data.formatar(impressaoParecerProjetoArquitetonicoDTO.getDataCadastro()), "");
        templateUtil.preencherCampo("estabelecimento_pessoa", impressaoParecerProjetoArquitetonicoDTO.getRequerimentoVigilancia().getEstabelecimento().getRazaoSocial() != null ?
                "Razão Social: " + impressaoParecerProjetoArquitetonicoDTO.getRequerimentoVigilancia().getEstabelecimento().getRazaoSocial() :
                "Nome: " + impressaoParecerProjetoArquitetonicoDTO.getRequerimentoVigilancia().getVigilanciaPessoa().getNome(), "");
        templateUtil.preencherCampo("endereco",impressaoParecerProjetoArquitetonicoDTO.getEnderecoObra(), "");
        templateUtil.preencherCampo("numero", impressaoParecerProjetoArquitetonicoDTO.getObraNumeroEndereco(), "");
        templateUtil.preencherCampo("quadra", impressaoParecerProjetoArquitetonicoDTO.getObraQuadra(), "");
        templateUtil.preencherCampo("numero_lado", impressaoParecerProjetoArquitetonicoDTO.getObraNumeroLado() , "");
        templateUtil.preencherCampo("lote", impressaoParecerProjetoArquitetonicoDTO.getObraLote(), "");
        templateUtil.preencherCampo("complemento", impressaoParecerProjetoArquitetonicoDTO.getObraComplemento() , "");
        templateUtil.preencherCampo("lote_condominio", impressaoParecerProjetoArquitetonicoDTO.getObraNumeroLoteamento(), "");
        templateUtil.preencherCampo("data_parecer", Data.formatar(impressaoParecerProjetoArquitetonicoDTO.getDataParecer()), "");
        templateUtil.preencherCampo("descricao_parecer", impressaoParecerProjetoArquitetonicoDTO.getDescricaoParecer(), "");
        templateUtil.preencherCampo("data_retorno", Data.formatar(impressaoParecerProjetoArquitetonicoDTO.getDataRetorno()), "");
        templateUtil.preencherCampo("resposta", impressaoParecerProjetoArquitetonicoDTO.getResposta(), "");
        templateUtil.preencherCampo("anexo", impressaoParecerProjetoArquitetonicoDTO.getAnexos(), "");
        if (CollectionUtils.isNotNullEmpty(impressaoParecerProjetoArquitetonicoDTO.getFiscais())){
            try {
                templateUtil.preencherCampo("fiscais", templateUtil.montarFiscais(impressaoParecerProjetoArquitetonicoDTO.getFiscais(),
                        VigilanciaHelper.exibirLinhaAssinatura() && RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class)
                                .modulo(Modulos.VIGILANCIA_SANITARIA).getParametro("gerarDocumentoComAssinaturaFiscal"))), "display_rotulo_assinaturas");
            } catch (DAOException e) {
                Loggable.log.error(e);
            }
        }
        templateUtil.preencherCampo("qrCode",templateUtil.gerarQrCode(impressaoParecerProjetoArquitetonicoDTO.getUrlQrCode()),"");
    }
}