package br.com.celk.report.emprestimo.query;

import br.com.celk.bo.emprestimo.interfaces.dto.RelatorioComprovanteDevolucaoDTO;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimo;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryComprovanteDevolucaoEmprestimo extends CommandQuery<QueryComprovanteDevolucaoEmprestimo> implements ITransferDataReport<DevolucaoEmprestimo, RelatorioComprovanteDevolucaoDTO> {

    private DevolucaoEmprestimo param;
    private List<RelatorioComprovanteDevolucaoDTO> result;

    //alterar aqui tbm ainda... trocar tabela
    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("deElo.quantidade", "quantidade");

        hql.addToSelectAndOrder("dei.codigo", "devolucaoEmprestimoItem.codigo");
        hql.addToSelect("dei.grupoEstoque", "devolucaoEmprestimoItem.grupoEstoque");
        hql.addToSelect("dei.quantidade", "devolucaoEmprestimoItem.quantidade");

        hql.addToSelect("p.codigo", "devolucaoEmprestimoItem.produto.codigo");
        hql.addToSelect("p.descricao", "devolucaoEmprestimoItem.produto.descricao");

        hql.addToSelect("u.unidade", "devolucaoEmprestimoItem.produto.unidade.unidade");

        hql.addToSelect("de.codigo", "devolucaoEmprestimoItem.devolucaoEmprestimo.codigo");
        hql.addToSelect("de.dataDevolucao", "devolucaoEmprestimoItem.devolucaoEmprestimo.dataDevolucao");
        hql.addToSelect("de.observacao", "devolucaoEmprestimoItem.devolucaoEmprestimo.observacao");
        hql.addToSelect("usuario.nome", "devolucaoEmprestimoItem.devolucaoEmprestimo.usuario.nome");

        hql.addToSelectAndOrder("lei.codigo", "lancamentoEmprestimoItem.codigo");
        hql.addToSelect("lei.dataCadastro", "lancamentoEmprestimoItem.dataCadastro");
        hql.addToSelect("le.codigo", "lancamentoEmprestimoItem.lancamentoEmprestimo.codigo");

        hql.addToSelect("pLei.codigo", "lancamentoEmprestimoItem.produto.codigo");
        hql.addToSelect("pLei.descricao", "lancamentoEmprestimoItem.produto.descricao");

        hql.addToSelect("uLei.unidade", "lancamentoEmprestimoItem.produto.unidade.unidade");

        hql.addToSelect("usuarioCadsus.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.sexo", "usuarioCadsus.sexo");
        hql.addToSelect("usuarioCadsus.dataNascimento", "usuarioCadsus.dataNascimento");
        hql.addToSelect("usuarioCadsus.telefone", "usuarioCadsus.telefone");

        hql.addToSelect("euc.logradouro", "enderecoUsuarioCadsus.logradouro");
        hql.addToSelect("euc.numeroLogradouro", "enderecoUsuarioCadsus.numeroLogradouro");
        hql.addToSelect("euc.bairro", "enderecoUsuarioCadsus.bairro");
        hql.addToSelect("euc.cep", "enderecoUsuarioCadsus.cep");
        hql.addToSelect("cidadeUsuario.descricao", "enderecoUsuarioCadsus.cidade.descricao");
        hql.addToSelect("tl.codigo", "enderecoUsuarioCadsus.tipoLogradouro.codigo");
        hql.addToSelect("tl.descricao", "enderecoUsuarioCadsus.tipoLogradouro.descricao");

        hql.addToSelect("e.codigo", "empresaDevolucao.codigo");
        hql.addToSelect("e.descricao", "empresaDevolucao.descricao");
        hql.addToSelect("e.rua", "empresaDevolucao.rua");
        hql.addToSelect("e.numero", "empresaDevolucao.numero");
        hql.addToSelect("e.complemento", "empresaDevolucao.complemento");
        hql.addToSelect("e.bairro", "empresaDevolucao.bairro");
        hql.addToSelect("e.telefone", "empresaDevolucao.telefone");
        hql.addToSelect("e.cep", "empresaDevolucao.cep");
        hql.addToSelect("cidadeEmpresa.descricao", "empresaDevolucao.cidade.descricao");

        hql.addToSelect("td.codigo", "devolucaoEmprestimoItem.devolucaoEmprestimo.tipoDevolucao.codigo");
        hql.addToSelect("td.descricao", "devolucaoEmprestimoItem.devolucaoEmprestimo.tipoDevolucao.descricao");

        hql.setTypeSelect(RelatorioComprovanteDevolucaoDTO.class.getName());

        hql.addToFrom("DevolucaoEmprestimoElo deElo"
                + " left join deElo.lancamentoEmprestimoItem lei"
                + " left join lei.produto pLei"
                + " left join pLei.unidade uLei"
                + " left join lei.lancamentoEmprestimo le"
                + " left join deElo.devolucaoEmprestimoItem dei"
                + " left join dei.produto p"
                + " left join p.unidade u"
                + " left join dei.devolucaoEmprestimo de"
                + " left join de.tipoDevolucao td"
                + " left join de.usuario usuario"
                + " left join de.usuarioCadsus usuarioCadsus"
                + " left join usuarioCadsus.enderecoDomicilio ed"
                + " left join ed.enderecoUsuarioCadsus euc"
                + " left join euc.cidade cidadeUsuario"
                + " left join euc.tipoLogradouro tl"
                + " left join de.empresaDevolucao e"
                + " left join e.cidade cidadeEmpresa"
        );

        hql.addToWhereWhithAnd("de.codigo = ", param.getCodigo());
        //hql.addToWhereWhithAnd("dei.status <> ", DevolucaoEmprestimoItem.Status.CANCELADA.value());
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioComprovanteDevolucaoDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(DevolucaoEmprestimo param) {
        this.param = param;
    }

}
