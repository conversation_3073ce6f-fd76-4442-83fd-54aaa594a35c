package br.com.celk.report.vigilancia.declaratorios;

import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;

public class ImpressaoCarimboAprovacaoDTO implements Serializable {

    private String inscricaoImobiliaria;
    private Date dataEmissao;
    private String numeroAprovacao;
    private Long protocolo;
    private String tipoAprovacao;

    public ImpressaoCarimboAprovacaoDTO() {}

    public ImpressaoCarimboAprovacaoDTO(
            String inscricaoImobiliaria,
            Date dataEmissao,
            String numeroAprovacao,
            Long protocolo,
            String tipoAprovacao
    ) {
        this.inscricaoImobiliaria = inscricaoImobiliaria;
        this.dataEmissao = dataEmissao;
        this.numeroAprovacao = numeroAprovacao;
        this.protocolo = protocolo;
        this.tipoAprovacao = tipoAprovacao;
    }

    public String getDataEmissaoFormatada() {
        if (dataEmissao != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
            return sdf.format(dataEmissao);
        }
        return "";
    }

    public Date getDataEmissao() {
        return dataEmissao;
    }

    public void setDataEmissao(Date dataEmissao) {
        this.dataEmissao = dataEmissao;
    }

    public String getInscricaoImobiliaria() {
        return inscricaoImobiliaria == null ? "" : inscricaoImobiliaria;
    }

    public void setInscricaoImobiliaria(String inscricaoImobiliaria) {
        this.inscricaoImobiliaria = inscricaoImobiliaria;
    }

    public String getNumeroAprovacao() {
        return numeroAprovacao == null ? "" : numeroAprovacao;
    }

    public void setNumeroAprovacao(String numeroAprovacao) {
        this.numeroAprovacao = numeroAprovacao;
    }

    public String getProtocoloFormatado() {
        return VigilanciaHelper.formatarProtocolo(protocolo);
    }

    public Long getProtocolo() {
        return protocolo;
    }

    public void setProtocolo(Long protocolo) {
        this.protocolo = protocolo;
    }

    public String getTipoAprovacao() {
        return tipoAprovacao;
    }

    public void setTipoAprovacao(String tipoAprovacao) {
        this.tipoAprovacao = tipoAprovacao;
    }
}