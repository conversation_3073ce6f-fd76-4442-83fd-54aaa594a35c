<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_fichas_atend_domiciliar_resumido" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="904ef39c-8037-443b-8222-af49929dd456">
	<property name="ireport.zoom" value="1.8181818181818303"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="br.com.celk.util.Coalesce"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<import value="org.apache.commons.lang.StringUtils"/>
	<subDataset name="condicoesAvaliadas" uuid="c9c8a146-f579-4103-b3df-d528faff0ac9">
		<field name="quantidade" class="java.lang.Long"/>
		<field name="descricaoCondicao" class="java.lang.String"/>
	</subDataset>
	<parameter name="FORMA_APRESENTACAO" class="br.com.celk.unidadesaude.esus.relatorios.RelatorioFichasAtendimentoDomiciliarDTOParam.FormaApresentacao"/>
	<parameter name="TIPO_RESUMO" class="br.com.celk.unidadesaude.esus.relatorios.RelatorioFichasAtendimentoDomiciliarDTOParam.TipoResumo"/>
	<field name="quantidade" class="java.lang.Long"/>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<field name="esusFichaAtendDomiciliarItem" class="br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendDomiciliarItem"/>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="esusFichaAtendDomiciliar" class="br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendDomiciliar">
		<fieldDescription><![CDATA[esusFichaAtendDomiciliarItem.esusFichaAtendDomiciliar]]></fieldDescription>
	</field>
	<field name="listCondicaoAvaliadas" class="java.util.List"/>
	<field name="totalCondicoesFA" class="java.lang.Long"/>
	<field name="totalCondicoesGeral" class="java.lang.Long"/>
	<variable name="totalFA" class="java.lang.Integer" resetType="Group" resetGroup="FA" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}]]></variableExpression>
	</variable>
	<variable name="totalGeral" class="java.lang.Integer" resetType="Group" resetGroup="FADefault" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}]]></variableExpression>
	</variable>
	<variable name="FA" class="br.com.celk.unidadesaude.esus.relatorios.RelatorioFichasAtendimentoDomiciliarDTOParam.FormaApresentacao"/>
	<variable name="TP" class="br.com.celk.unidadesaude.esus.relatorios.RelatorioFichasAtendimentoDomiciliarDTOParam.TipoResumo"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<group name="FADefault">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band/>
		</groupHeader>
		<groupFooter>
			<band height="12">
				<textField>
					<reportElement x="518" y="1" width="36" height="11" uuid="ae4251ef-856e-42b3-809b-396a92c7e146"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{totalCondicoesGeral}!= null
    ?
        $F{totalCondicoesGeral}
    :
        $V{totalGeral}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="440" y="1" width="77" height="11" uuid="8345a293-b452-4406-b95b-5ac985206b88"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_geral") + ": "]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="469" y="0" width="85" height="1" uuid="74011bb7-90cb-42e4-9848-1230bc7c15e7"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="FA" isReprintHeaderOnEachPage="true" keepTogether="true">
		<groupExpression><![CDATA[!$V{FA}.GERAL.equals($P{FORMA_APRESENTACAO})
    ?
        $V{FA}.PROFISSIONAL.equals($P{FORMA_APRESENTACAO})
        ?
           $V{BUNDLE}.getStringApplication("rotulo_profissional") + ": " + $F{profissional}.getNome()
        :
            $V{FA}.MODALIDADE.equals($P{FORMA_APRESENTACAO})
            ?
                $V{BUNDLE}.getStringApplication("rotulo_modalidade") + ": " + $F{esusFichaAtendDomiciliarItem}.getDescricaoModalidade()
            :
                $V{FA}.DATA_ATENDIMENTO.equals($P{FORMA_APRESENTACAO})
                ?
                    $V{BUNDLE}.getStringApplication("rotulo_data_atendimento") + ": " + $F{esusFichaAtendDomiciliarItem}.getEsusFichaAtendDomiciliar().getDataAtendimento()

                :
                    $V{FA}.ESTABELECIMENTO.equals($P{FORMA_APRESENTACAO})
                    ?
                        $V{BUNDLE}.getStringApplication("rotulo_estabelecimento") + ": " + $F{empresa}.getDescricao()
                    :
                        $V{FA}.TURNO.equals($P{FORMA_APRESENTACAO})
                        ?
                            $V{BUNDLE}.getStringApplication("rotulo_turno") + ": " + $F{esusFichaAtendDomiciliarItem}.getDescricaoTurno()
                        :
                            $V{FA}.LOCAL_ATENDIMENTO.equals($P{FORMA_APRESENTACAO})
                            ?
                                $V{BUNDLE}.getStringApplication("rotulo_local_atendimento") + ": " + $F{esusFichaAtendDomiciliarItem}.getDescricaoLocalAtendimento()
                            :
                                $V{FA}.CONDUTA.equals($P{FORMA_APRESENTACAO})
                                ?
                                    $V{BUNDLE}.getStringApplication("rotulo_conduta") + ": " + $F{esusFichaAtendDomiciliarItem}.getDescricaoConduta()
                                :
                                    $V{FA}.TIPO_ATENDIMENTO.equals($P{FORMA_APRESENTACAO})
                                    ?
                                        $V{BUNDLE}.getStringApplication("rotulo_tipo_atendimento") + ": " + $F{esusFichaAtendDomiciliarItem}.getDescricaoTipoAtendimento()
                                    :
                                    null

    :
        null]]></groupExpression>
		<groupHeader>
			<band height="20">
				<printWhenExpression><![CDATA[!$V{FA}.GERAL.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
				<rectangle radius="10">
					<reportElement x="0" y="0" width="555" height="18" uuid="9acf27c7-80b0-40ee-98f8-a67674c92b9c"/>
					<graphicElement>
						<pen lineWidth="1.0"/>
					</graphicElement>
				</rectangle>
				<textField>
					<reportElement x="8" y="3" width="540" height="13" uuid="e199520c-c239-4028-a5d2-b36929bfb86b"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="10" isBold="true" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[!$V{FA}.GERAL.equals($P{FORMA_APRESENTACAO})
    ?
        $V{FA}.PROFISSIONAL.equals($P{FORMA_APRESENTACAO})
        ?
           $V{BUNDLE}.getStringApplication("rotulo_profissional") + ": " + $F{profissional}.getNome()
        :
            $V{FA}.MODALIDADE.equals($P{FORMA_APRESENTACAO})
            ?
                $V{BUNDLE}.getStringApplication("rotulo_modalidade") + ": " + $F{esusFichaAtendDomiciliarItem}.getDescricaoModalidade()
            :
                $V{FA}.DATA_ATENDIMENTO.equals($P{FORMA_APRESENTACAO})
                ?
                    $V{BUNDLE}.getStringApplication("rotulo_data_atendimento") + ": " + br.com.celk.util.DataUtil.getFormatarDiaMesAno($F{esusFichaAtendDomiciliar}.getDataAtendimento())

                :
                    $V{FA}.ESTABELECIMENTO.equals($P{FORMA_APRESENTACAO})
                    ?
                        $V{BUNDLE}.getStringApplication("rotulo_estabelecimento") + ": " + $F{empresa}.getDescricao()
                    :
                        $V{FA}.TURNO.equals($P{FORMA_APRESENTACAO})
                        ?
                            $V{BUNDLE}.getStringApplication("rotulo_turno") + ": " + $F{esusFichaAtendDomiciliarItem}.getDescricaoTurno()
                        :
                            $V{FA}.LOCAL_ATENDIMENTO.equals($P{FORMA_APRESENTACAO})
                            ?
                                $V{BUNDLE}.getStringApplication("rotulo_local_atendimento") + ": " + $F{esusFichaAtendDomiciliarItem}.getDescricaoLocalAtendimento()
                            :
                                $V{FA}.CONDUTA.equals($P{FORMA_APRESENTACAO})
                                ?
                                    $V{BUNDLE}.getStringApplication("rotulo_conduta") + ": " + $F{esusFichaAtendDomiciliarItem}.getDescricaoConduta()
                                :
                                    $V{FA}.TIPO_ATENDIMENTO.equals($P{FORMA_APRESENTACAO})
                                    ?
                                        $V{BUNDLE}.getStringApplication("rotulo_tipo_atendimento") + ": " + $F{esusFichaAtendDomiciliarItem}.getDescricaoTipoAtendimento()
                                    :
                                    null

    :
        null]]></textFieldExpression>
				</textField>
			</band>
			<band height="13">
				<line>
					<reportElement x="0" y="11" width="555" height="1" uuid="fb44fa67-4ef7-47e8-b53f-ca21c6313e0c"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField>
					<reportElement x="470" y="0" width="83" height="11" uuid="990a2955-d81e-47d6-8a1c-26bcfa19fc49"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_qtde_atendimentos")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="3" y="0" width="436" height="10" uuid="bbc19e09-5458-4cd3-9e87-a164c4143f53"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TP}.MODALIDADE.equals($P{TIPO_RESUMO})
?
    Bundle.getStringApplication("rotulo_modalidade")
:
    $V{TP}.DATA_ATENDIMENTO.equals($P{TIPO_RESUMO})
    ?
        Bundle.getStringApplication("rotulo_data_atendimento")
    :
        $V{TP}.PROFISSIONAL.equals($P{TIPO_RESUMO})
        ?
            Bundle.getStringApplication("rotulo_profissional")
        :
            $V{TP}.ESTABELECIMENTO.equals($P{TIPO_RESUMO})
            ?
                Bundle.getStringApplication("rotulo_estabelecimento")
            :
                $V{TP}.CONDICOES_AVALIADAS.equals($P{TIPO_RESUMO})
                ?
                    Bundle.getStringApplication("rotulo_condicoes_avaliadas")
                :
                    ""]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="15">
				<textField>
					<reportElement x="517" y="4" width="36" height="11" uuid="61c7b548-9c05-4407-af90-365421551d7d"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{totalCondicoesFA} != null
    ?
        $F{totalCondicoesFA}
    :
        $V{totalFA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="439" y="4" width="77" height="11" uuid="2bae9521-d953-4c9f-ab88-b051edb9372b"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total") + ": "]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="493" y="3" width="62" height="1" uuid="8bfe4a50-cb96-4e1d-a685-d65ba678c114"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
			</band>
		</groupFooter>
	</group>
	<detail>
		<band height="12" splitType="Stretch">
			<printWhenExpression><![CDATA[!$V{TP}.CONDICOES_AVALIADAS.equals($P{TIPO_RESUMO})]]></printWhenExpression>
			<textField pattern="######">
				<reportElement x="517" y="0" width="36" height="12" uuid="d2ac5de9-0cd7-4cbb-a2b3-e9d514b7e5a8"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidade}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="3" y="0" width="436" height="12" uuid="f277ee39-f0cc-4bd1-a138-f6e196316e03"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{TP}.MODALIDADE.equals($P{TIPO_RESUMO})
?
    $F{esusFichaAtendDomiciliarItem}.getDescricaoModalidade()
:
    $V{TP}.PROFISSIONAL.equals($P{TIPO_RESUMO})
    ?
        $F{profissional}.getNome()
    :
        $V{TP}.DATA_ATENDIMENTO.equals($P{TIPO_RESUMO})
        ?
            br.com.celk.util.DataUtil.getFormatarDiaMesAno($F{esusFichaAtendDomiciliar}.getDataAtendimento())
        :
            $V{TP}.ESTABELECIMENTO.equals($P{TIPO_RESUMO})
            ?
                $F{empresa}.getDescricao()
            :
            $V{TP}.CONDICOES_AVALIADAS.equals($P{TIPO_RESUMO})
            ?
                $F{esusFichaAtendDomiciliarItem}.getSomatorioCondicao()
            :
                ""]]></textFieldExpression>
			</textField>
		</band>
		<band height="12">
			<printWhenExpression><![CDATA[$V{TP}.CONDICOES_AVALIADAS.equals($P{TIPO_RESUMO})]]></printWhenExpression>
			<componentElement>
				<reportElement x="0" y="0" width="555" height="12" uuid="8975b641-e3d7-412e-96b8-71de582187dd"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="condicoesAvaliadas" uuid="17e608ee-0e52-41a0-8bb8-d90ff02aa32e">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{listCondicaoAvaliadas})]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="12" width="555">
						<textField>
							<reportElement x="514" y="0" width="39" height="12" uuid="105f51dc-c2b0-4e14-9d20-67578168f69d"/>
							<textElement textAlignment="Right">
								<font fontName="Arial" size="8"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{quantidade}]]></textFieldExpression>
						</textField>
						<textField>
							<reportElement x="3" y="0" width="436" height="12" uuid="474d70bb-51ca-4158-a298-e160477427b1"/>
							<textElement>
								<font fontName="Arial" size="8"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{descricaoCondicao}]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
		</band>
	</detail>
</jasperReport>
