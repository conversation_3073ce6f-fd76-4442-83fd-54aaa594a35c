<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="comprovante_auto_infracao" columnDirection="RTL" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="56eab6e4-5301-4f11-a723-bfb165341f5f">
	<property name="ireport.zoom" value="2.2000000000000073"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="576"/>
	<import value="br.com.ksisolucoes.vo.vigilancia.termoajustamentoconduta.TermoAjustamentoConduta"/>
	<import value="br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<subDataset name="ds-profissionais" uuid="f06827ab-7d0f-4505-8c1c-48ae175fc33c">
		<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional"/>
		<variable name="exibirComoAssinatura" class="java.lang.Boolean" calculation="First">
			<variableExpression><![CDATA[VigilanciaHelper.exibirLinhaAssinatura()]]></variableExpression>
		</variable>
	</subDataset>
	<subDataset name="ds-providencia" uuid="4a08c702-526b-4649-b519-35808cc7fdd9">
		<field name="infracao" class="java.lang.String"/>
		<field name="observacao" class="java.lang.String"/>
		<field name="legislacao" class="java.lang.String"/>
	</subDataset>
	<subDataset name="ds-assinaturas" uuid="1f55cf83-05b9-4a11-b0d4-807598e045ef">
		<field name="file" class="java.io.File"/>
		<variable name="exibirComoAssinatura" class="java.lang.Boolean" calculation="First">
			<variableExpression><![CDATA[VigilanciaHelper.exibirLinhaAssinatura()]]></variableExpression>
		</variable>
	</subDataset>
	<parameter name="CLAUSULA" class="java.lang.String"/>
	<parameter name="USUARIO" class="java.lang.String"/>
	<parameter name="EMPRESA_LOGADA" class="java.lang.String"/>
	<parameter name="CIENCIA" class="java.lang.String"/>
	<parameter name="EMPRESA_LOGADA_ENDERECO" class="java.lang.String"/>
	<parameter name="MENSAGEM_CHAVE_CONSULTA_REQUERIMENTO" class="java.lang.String"/>
	<parameter name="exibirNomeResponsavel" class="java.lang.Boolean"/>
	<parameter name="urlQRCode" class="java.lang.String"/>
	<parameter name="ASSINATURA_RESPONSAVEL" class="java.io.File" isForPrompting="false"/>
	<parameter name="ASSINATURA_FISCAL" class="java.util.List"/>
	<parameter name="ASSINATURA_TESTEMUNHA1" class="java.util.List"/>
	<parameter name="ASSINATURA_TESTEMUNHA2" class="java.io.File"/>
	<field name="listDescricaoCondutaAjustada" class="java.util.List"/>
	<field name="listFiscalTermoAjustamentoConduta" class="java.util.List"/>
	<field name="descricaoAtividadePrincipal" class="java.lang.String"/>
	<field name="termoAjustamentoConduta" class="br.com.ksisolucoes.vo.vigilancia.termoajustamentoconduta.TermoAjustamentoConduta"/>
	<variable name="RASCUNHO" class="java.lang.Boolean">
		<variableExpression><![CDATA[$F{termoAjustamentoConduta}.getSituacao() == null
||
$F{termoAjustamentoConduta}.getSituacao().equals(2L)]]></variableExpression>
	</variable>
	<variable name="exibirNomeResponsavel" class="java.lang.Boolean" calculation="First">
		<variableExpression><![CDATA[VigilanciaHelper.exibirNomeResponsavelAuto()]]></variableExpression>
		<initialValueExpression><![CDATA[Boolean.FALSE]]></initialValueExpression>
	</variable>
	<group name="Dados_estabelecimento" isStartNewPage="true">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band height="50">
				<printWhenExpression><![CDATA[$V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<rectangle radius="0">
					<reportElement x="0" y="14" width="555" height="32" uuid="9770723e-e9b9-40dc-9142-7a5f46007c5a"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isBlankWhenNull="true">
					<reportElement mode="Opaque" x="6" y="8" width="80" height="12" uuid="e9b363fe-53fc-4573-96b4-a68b9ea458e3"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_orgao_autuante")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="4" y="19" width="64" height="12" uuid="a74aeec0-6835-45f0-81bc-8baf1af31fe2"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_denominacao_maiusculo") + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="72" y="19" width="480" height="12" uuid="8d89e83a-3f8e-477f-9882-6299f79f4d1e"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{EMPRESA_LOGADA}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="4" y="32" width="49" height="12" uuid="fe52965e-8f15-4d93-bb9d-2c1e1b408479"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_endereco").toUpperCase() + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="57" y="32" width="495" height="12" uuid="a9c0cf16-6a05-40f6-9c31-d19c3bfd20a4"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{EMPRESA_LOGADA_ENDERECO}]]></textFieldExpression>
				</textField>
			</band>
			<band height="143">
				<printWhenExpression><![CDATA[$F{termoAjustamentoConduta}.getTipoEstabelecimentoPessoa() == 0L]]></printWhenExpression>
				<rectangle radius="0">
					<reportElement stretchType="RelativeToBandHeight" x="0" y="8" width="555" height="121" uuid="75521546-4ed3-44ca-af20-400bb3991b73"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="69" width="547" height="11" uuid="e5bbac40-e61c-4549-aeea-89ecc882d256"/>
					<box topPadding="1" leftPadding="2"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{termoAjustamentoConduta}.getEnderecoPartesEnvolvidasFormatado()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="82" width="417" height="11" uuid="634a99dc-fadd-45e4-a86f-e896dc179b62"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Atividade: ".toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="59" width="417" height="11" uuid="c245898f-1d51-4124-9428-514f872621c6"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_endereco").toUpperCase() + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement mode="Opaque" x="6" y="2" width="45" height="11" uuid="b42ba35f-be4d-4f6d-ae9b-1d313a6da50b"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_autuado").toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="92" width="547" height="11" uuid="9996cb9f-5be8-4937-8654-8f21e61be9f8"/>
					<box topPadding="1" leftPadding="2"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{termoAjustamentoConduta}.getVigilanciaPessoa().getAtividade()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="36" width="417" height="11" uuid="eda5f3fd-c9c6-48c2-8a37-7812374a311b"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Fantasia: ".toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="427" y="23" width="125" height="11" uuid="3756b22e-50cf-4bf7-87f2-8b0a5a72ccfd"/>
					<box topPadding="1" leftPadding="2"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{termoAjustamentoConduta}.getVigilanciaPessoa().getCpfFormatado()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="23" width="417" height="11" uuid="d1a6aaad-84e1-44eb-a9ae-4a9aeed9c024"/>
					<box topPadding="1" leftPadding="2"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{termoAjustamentoConduta}.getEstabelecimentoPessoa()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="115" width="547" height="11" uuid="1e5f2196-ab4a-4f62-8b80-7bfad626ab2b"/>
					<box topPadding="1" leftPadding="2"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{termoAjustamentoConduta}.getVigilanciaPessoa().getRepresentanteLegal()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="105" width="417" height="11" uuid="3fce755f-bbbd-422b-996e-83d07e571aaf"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Representante Legal: ".toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="13" width="417" height="11" uuid="58a2f741-3d9e-4c63-9f36-4a8d0160e272"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_pessoa_fissica_juridica") + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="46" width="547" height="11" uuid="6f537fef-c387-4524-9f3f-b4c47795ee73"/>
					<box topPadding="1" leftPadding="2"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{termoAjustamentoConduta}.getEnderecoPartesEnvolvidasFormatado()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="427" y="13" width="125" height="11" uuid="3edcfd2b-7c6d-4f86-aa1b-0fe876f1adb7"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["CPF ou CNPJ: "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="1" y="131" width="257" height="11" uuid="c7148b56-a2a2-4538-8ccd-2096ff8b930e"/>
					<textElement verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_auto_intimacao_subsistente").toUpperCase() + ":"]]></textFieldExpression>
				</textField>
			</band>
			<band height="131">
				<printWhenExpression><![CDATA[$F{termoAjustamentoConduta}.getTipoEstabelecimentoPessoa() == 1L]]></printWhenExpression>
				<rectangle radius="0">
					<reportElement stretchType="RelativeToBandHeight" x="0" y="8" width="555" height="120" uuid="432ba63a-1d49-498d-8ff9-7058a5fb87a8"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="13" width="417" height="11" uuid="e8bbfa49-5399-498e-afa7-6607bc2f14d6"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_pessoa_fissica_juridica") + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="23" width="417" height="11" uuid="94d28041-fdb7-4ee9-85ff-2eed3bdb2fe4"/>
					<box topPadding="1" leftPadding="2"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{termoAjustamentoConduta}.getEstabelecimentoPessoa()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="427" y="13" width="125" height="11" uuid="63449a2d-2f1b-4f81-9ed1-64153af08c20">
						<printWhenExpression><![CDATA[$F{termoAjustamentoConduta}.getTipoEstabelecimentoPessoa() == 1L]]></printWhenExpression>
					</reportElement>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cnpj") + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="427" y="23" width="125" height="11" uuid="db4707b3-6233-469c-9b96-d4f3d6e0665a">
						<printWhenExpression><![CDATA[$F{termoAjustamentoConduta}.getTipoEstabelecimentoPessoa() == 1L]]></printWhenExpression>
					</reportElement>
					<box topPadding="1" leftPadding="2"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{termoAjustamentoConduta}.getEstabelecimento().getCnpjCpfFormatado()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="46" width="417" height="11" uuid="776ab0a9-c716-47ad-9bc5-b1906031f192">
						<printWhenExpression><![CDATA[$F{termoAjustamentoConduta}.getTipoEstabelecimentoPessoa() == 1L]]></printWhenExpression>
					</reportElement>
					<box topPadding="1" leftPadding="2"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{termoAjustamentoConduta}.getEstabelecimento().getFantasia()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="36" width="417" height="11" uuid="4ca78843-4f43-4bbe-a148-0ec9eb39e280">
						<printWhenExpression><![CDATA[$F{termoAjustamentoConduta}.getTipoEstabelecimentoPessoa() == 1L]]></printWhenExpression>
					</reportElement>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nome_fantasia").toUpperCase() + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="427" y="46" width="125" height="11" uuid="d0ea534e-8771-4a86-af41-fcba91f3586f">
						<printWhenExpression><![CDATA[$F{termoAjustamentoConduta}.getTipoEstabelecimentoPessoa() == 1L]]></printWhenExpression>
					</reportElement>
					<box topPadding="1" leftPadding="2"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{termoAjustamentoConduta}.getEstabelecimento().getInscricaoEstadual()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="427" y="36" width="125" height="11" uuid="fd1eadb2-745c-4260-a1a0-ff5e20910b27">
						<printWhenExpression><![CDATA[$F{termoAjustamentoConduta}.getTipoEstabelecimentoPessoa() == 1L]]></printWhenExpression>
					</reportElement>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_inscricao_estadual").toUpperCase() + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="59" width="417" height="11" uuid="360cbec0-8775-4111-b8b3-5699cd8d53ee"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_endereco").toUpperCase() + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="69" width="547" height="11" uuid="478978ab-75b0-4ac9-bf83-f0034f2c76c4">
						<printWhenExpression><![CDATA[$F{termoAjustamentoConduta}.getTipoEstabelecimentoPessoa() == 1L]]></printWhenExpression>
					</reportElement>
					<box topPadding="1" leftPadding="2"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{termoAjustamentoConduta}.getEnderecoPartesEnvolvidasFormatado()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="92" width="417" height="11" uuid="84ff4ea6-e1ef-4a25-9328-d61554d0da3d">
						<printWhenExpression><![CDATA[$F{termoAjustamentoConduta}.getTipoEstabelecimentoPessoa() == 1L]]></printWhenExpression>
					</reportElement>
					<box topPadding="1" leftPadding="2"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{descricaoAtividadePrincipal}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="82" width="417" height="11" uuid="63d9b20b-e435-460f-af50-a5e6a81f968a"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_atividade").toUpperCase() + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="427" y="82" width="125" height="11" uuid="6692f43c-f645-4d12-9b9c-69b8b7575bf5"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_alvara").toUpperCase() + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="427" y="92" width="125" height="11" uuid="6741761a-4db3-46c3-b609-d6ae294e4432">
						<printWhenExpression><![CDATA[$F{termoAjustamentoConduta}.getTipoEstabelecimentoPessoa() == 1L]]></printWhenExpression>
					</reportElement>
					<box topPadding="1" leftPadding="2"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{termoAjustamentoConduta}.getEstabelecimento().getAlvara()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement mode="Opaque" x="6" y="2" width="45" height="11" uuid="667b1380-4d3e-46c9-96f0-6044a8f59233"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_autuado").toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="4" y="105" width="417" height="11" uuid="0cce51a0-2300-4bd1-bb1b-5a8e35a75319"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_responsavel_legal").toUpperCase() + ":"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="4" y="115" width="417" height="11" uuid="ced584a9-59ad-4695-8a1c-c9031b69fc46"/>
					<box topPadding="1" leftPadding="2"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{termoAjustamentoConduta}.getEstabelecimento().getRepresentanteNome()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="427" y="105" width="125" height="11" uuid="e974dd9e-53eb-4dd2-95dc-5d25743e0a4b"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cpf").toUpperCase() + ":"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="427" y="115" width="125" height="11" uuid="4bb9dd01-4d16-4240-a9fb-77d1e993fafa"/>
					<box topPadding="1" leftPadding="2"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{termoAjustamentoConduta}.getEstabelecimento().getCpfRepresentanteFormatado()]]></textFieldExpression>
				</textField>
			</band>
			<band height="32">
				<rectangle radius="0">
					<reportElement stretchType="RelativeToBandHeight" x="0" y="8" width="555" height="20" isPrintWhenDetailOverflows="true" uuid="c00fb1e6-d252-421e-b906-3edffb465c6f"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isBlankWhenNull="true">
					<reportElement mode="Opaque" x="7" y="2" width="123" height="12" isPrintWhenDetailOverflows="true" uuid="e1c0652f-980a-4987-bc5d-a42a31796a92"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_enquadramento_legal").toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="5" y="13" width="545" height="15" uuid="b89cb238-3f61-485e-b937-63bf1d34bfd9"/>
					<box bottomPadding="3"/>
					<textElement textAlignment="Justified">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{termoAjustamentoConduta}.getBaseLegalTac()]]></textFieldExpression>
				</textField>
			</band>
			<band height="62">
				<printWhenExpression><![CDATA[$P{CLAUSULA} != null]]></printWhenExpression>
				<rectangle radius="0">
					<reportElement stretchType="RelativeToBandHeight" x="0" y="10" width="555" height="50" isPrintWhenDetailOverflows="true" uuid="4d6cec86-4512-4dcc-bb6a-24e45d8663c9"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="7" y="15" width="545" height="45" uuid="e06917a6-8b93-40da-9462-15dd0934b50e"/>
					<box bottomPadding="3"/>
					<textElement textAlignment="Justified">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{CLAUSULA}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement mode="Opaque" x="5" y="3" width="101" height="12" isPrintWhenDetailOverflows="true" uuid="c35070f6-708f-4ec4-ac2d-7b42c448140a"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_clausulas_gerais").toUpperCase()]]></textFieldExpression>
				</textField>
			</band>
			<band height="34">
				<rectangle radius="0">
					<reportElement stretchType="RelativeToBandHeight" x="0" y="8" width="555" height="24" isPrintWhenDetailOverflows="true" uuid="22c46dd2-5c11-42dc-a422-3472462c1a1c"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isBlankWhenNull="true">
					<reportElement mode="Opaque" x="6" y="1" width="186" height="12" uuid="dd62e9e6-c9c4-4538-b507-237b7ca97220"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_descricao_conduta_ajustada").toUpperCase()]]></textFieldExpression>
				</textField>
				<subreport>
					<reportElement x="0" y="14" width="555" height="15" uuid="aca81acf-0e71-46cd-bbb4-a7e2f1a1ad8e"/>
					<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{listDescricaoCondutaAjustada})]]></dataSourceExpression>
					<subreportExpression><![CDATA["br/com/celk/report/vigilancia/termoajustamentoconduta/jrxml/sub_rel_descricao_conduta_ajustada.jasper"]]></subreportExpression>
				</subreport>
			</band>
			<band height="44">
				<rectangle radius="0">
					<reportElement x="0" y="10" width="555" height="32" isPrintWhenDetailOverflows="true" uuid="db9dbf1f-e79d-4f09-bf76-3733c2015cbb"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isBlankWhenNull="true">
					<reportElement x="349" y="31" width="200" height="11" isPrintWhenDetailOverflows="true" uuid="9dfe6890-7161-4f8f-a93e-27620bbdbb18"/>
					<box topPadding="1">
						<topPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font fontName="Arial" size="6" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_assinatura").toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement mode="Opaque" x="6" y="3" width="64" height="12" isPrintWhenDetailOverflows="true" uuid="95486601-179f-42e2-b6a1-1f5056588dcf"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_responsavel").toUpperCase()]]></textFieldExpression>
				</textField>
				<image scaleImage="FillFrame">
					<reportElement x="421" y="13" width="62" height="17" uuid="a54fd28b-0268-4a2f-84c4-29eee25ba2da"/>
					<imageExpression><![CDATA[$P{ASSINATURA_RESPONSAVEL}]]></imageExpression>
				</image>
				<textField isBlankWhenNull="true">
					<reportElement x="11" y="30" width="226" height="11" isPrintWhenDetailOverflows="true" uuid="d804cee7-6443-432a-bade-dff72d657eb9"/>
					<box topPadding="1">
						<topPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font fontName="Arial" size="6" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nome_legivel").toUpperCase()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="72">
				<printWhenExpression><![CDATA[$P{urlQRCode} != null]]></printWhenExpression>
				<line>
					<reportElement x="57" y="71" width="10" height="1" uuid="a56f39ae-f341-43e9-92f4-9720a17d743c"/>
					<graphicElement>
						<pen lineWidth="0.75"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="0" y="62" width="1" height="10" uuid="7c4072a6-dfd7-4887-ac7c-5cd0179ab7bd"/>
					<graphicElement>
						<pen lineWidth="0.75"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="57" y="4" width="10" height="1" uuid="4e361662-acb2-461a-a4b6-5fb3366e503e"/>
					<graphicElement>
						<pen lineWidth="0.75"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="1" y="71" width="10" height="1" uuid="e9bfb44c-0a9e-4435-86f5-3f359b20a03d"/>
					<graphicElement>
						<pen lineWidth="0.75"/>
					</graphicElement>
				</line>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="80" y="3" width="475" height="11" uuid="c4cc7edc-d059-406f-b07b-4b0553d29a9d"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Verifique a autenticidade deste documento, diretamente do seu dispositivo móvel, através do QR Code ao lado."]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="4" width="10" height="1" uuid="fec76b85-a403-4b01-96f6-1c4b26d69c9f"/>
					<graphicElement>
						<pen lineWidth="0.75"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="67" y="4" width="1" height="10" uuid="*************-4495-91fe-57598f410078"/>
					<graphicElement>
						<pen lineWidth="0.75"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="67" y="62" width="1" height="10" uuid="7ac0a637-6176-44bb-b4fc-ca660540f0aa"/>
					<graphicElement>
						<pen lineWidth="0.75"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="0" y="4" width="1" height="10" uuid="c71618f4-493d-4157-869f-95004f8287da"/>
					<graphicElement>
						<pen lineWidth="0.75"/>
					</graphicElement>
				</line>
				<image scaleImage="RealSize">
					<reportElement x="4" y="8" width="60" height="60" uuid="a978372d-ab0c-45c4-a09d-98fa34ba0be4"/>
					<imageExpression><![CDATA[com.google.zxing.client.j2se.MatrixToImageWriter.toBufferedImage(
    new com.google.zxing.qrcode.QRCodeWriter().encode(
            $P{urlQRCode},
            com.google.zxing.BarcodeFormat.QR_CODE, 600, 600))]]></imageExpression>
				</image>
				<componentElement>
					<reportElement x="7" y="8" width="542" height="56" uuid="025c61c0-d324-4fea-a443-ae36ca8d78ac"/>
					<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
						<datasetRun subDataset="ds-assinaturas" uuid="3f9fe36f-e633-4023-bd37-0f43f4e3fce2">
							<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($P{ASSINATURA_FISCAL})]]></dataSourceExpression>
						</datasetRun>
						<jr:listContents height="56" width="542">
							<image scaleImage="FillFrame">
								<reportElement x="405" y="4" width="62" height="17" uuid="5b99c435-5d18-440c-b445-8572d8e9a311"/>
								<imageExpression><![CDATA[$F{file}]]></imageExpression>
							</image>
						</jr:listContents>
					</jr:list>
				</componentElement>
			</band>
		</groupFooter>
	</group>
	<background>
		<band height="559">
			<printWhenExpression><![CDATA[$F{termoAjustamentoConduta}.getSituacao().equals(2L)]]></printWhenExpression>
		</band>
	</background>
	<pageHeader>
		<band height="12" splitType="Stretch">
			<printWhenExpression><![CDATA[$V{PAGE_NUMBER} > 1]]></printWhenExpression>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="280" height="12" isRemoveLineWhenBlank="true" uuid="20937e58-55bb-48ea-9420-e627aff91988">
					<printWhenExpression><![CDATA[$V{PAGE_NUMBER} > 1]]></printWhenExpression>
				</reportElement>
				<box topPadding="1"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{termoAjustamentoConduta}.getNumero() != null ?
Bundle.getStringApplication("rotulo_continuacao_termo_ajustamento_conduta") + " Nº "+$F{termoAjustamentoConduta}.getNumeroFormatado()   :
Bundle.getStringApplication("rotulo_continuacao_termo_ajustamento_conduta")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="455" y="0" width="100" height="12" isRemoveLineWhenBlank="true" uuid="f466949c-d87d-4cc4-94da-372c0cc16e11">
					<printWhenExpression><![CDATA[$V{PAGE_NUMBER} > 1]]></printWhenExpression>
				</reportElement>
				<box topPadding="1"/>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[(
    Bundle.getStringApplication("rotulo_folha_n") + ": " + $V{PAGE_NUMBER}
).toUpperCase()]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="64">
			<rectangle radius="0">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="8" width="555" height="56" isPrintWhenDetailOverflows="true" uuid="486b234e-53a1-4c5b-8266-d4e2d0f20bc3"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<componentElement>
				<reportElement x="5" y="13" width="545" height="51" uuid="ed67478f-689d-4648-acf8-383362a033db"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="ds-profissionais" uuid="9a80c37c-7f4d-46ef-a05d-51daf2461a83">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{listFiscalTermoAjustamentoConduta})]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="51" width="545">
						<textField pattern="" isBlankWhenNull="true">
							<reportElement key="" mode="Transparent" x="3" y="6" width="325" height="11" uuid="81859f67-6048-471b-a2ba-452f35ded151"/>
							<box topPadding="1"/>
							<textElement verticalAlignment="Top">
								<font fontName="Arial" size="8" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{profissional}.getNome()]]></textFieldExpression>
						</textField>
						<textField pattern="" isBlankWhenNull="true">
							<reportElement key="" mode="Transparent" x="3" y="17" width="325" height="11" uuid="c12ac21d-5b54-4f5c-9bc5-cc56c5cf75a2"/>
							<box topPadding="1"/>
							<textElement verticalAlignment="Top">
								<font fontName="Arial" size="8" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{profissional}.getReferenciaRegistroFormatado()]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement x="344" y="22" width="200" height="11" isPrintWhenDetailOverflows="true" uuid="d6b8889e-5054-48bb-9d59-fbaf15abb66c">
								<printWhenExpression><![CDATA[$V{exibirComoAssinatura}]]></printWhenExpression>
							</reportElement>
							<box topPadding="1">
								<topPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Top">
								<font fontName="Arial" size="6" isBold="true" pdfFontName="Helvetica-Bold"/>
							</textElement>
							<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_assinatura").toUpperCase()]]></textFieldExpression>
						</textField>
						<textField pattern="" isBlankWhenNull="true">
							<reportElement key="" mode="Transparent" x="2" y="28" width="325" height="11" uuid="9105d95f-9237-49c2-81c5-56c45b3d6f49"/>
							<box topPadding="1"/>
							<textElement verticalAlignment="Top">
								<font fontName="Arial" size="8" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{profissional}.getCargoFormatado()]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
			<textField isBlankWhenNull="true">
				<reportElement mode="Opaque" x="6" y="1" width="100" height="12" uuid="399bd391-b9ae-4592-b556-c4843b84dfb3"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_autoridade_saude").toUpperCase()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
