package br.com.celk.report.vigilancia.agravo;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Order;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;

import java.util.List;

public class AtualizarLocalizacaoRegistroAgravo extends AbstractCommandTransaction {
    @Override
    public void execute() throws DAOException, ValidacaoException {
        /*
            Rotina para atualizar registros antigos de Registro de Agravo, para executar a implementação da lógica de
            localização (latitude e longitude) no Save do registro de agravo
         */
//        int contador = 0;

        List<RegistroAgravo> registroAgravoList = getSession().createCriteria(RegistroAgravo.class)
                .add(Restrictions.or(Restrictions.isNull(RegistroAgravo.PROP_LATITUDE), Restrictions.isNull(RegistroAgravo.PROP_LONGITUDE)))
                .addOrder(Order.desc(RegistroAgravo.PROP_DATA_CADASTRO))
                .setMaxResults(500)
                .list();

        if (CollectionUtils.isNotNullEmpty(registroAgravoList)) {
            Loggable.log.info("INICIANDO ATUALIZAÇÃO DA GEOLOCALIZAÇÃO DE " + registroAgravoList.size() + " REGISTROS DE AGRAVO");
            for (RegistroAgravo registroAgravo : registroAgravoList) {
                if (registroAgravo.getLatitude() == null && registroAgravo.getLongitude() == null){
                    BOFactory.save(registroAgravo);
                }
            }
        }
    }
}
