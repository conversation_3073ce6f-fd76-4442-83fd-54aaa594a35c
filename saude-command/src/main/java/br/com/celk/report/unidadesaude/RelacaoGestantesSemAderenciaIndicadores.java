package br.com.celk.report.unidadesaude;

import br.com.celk.report.unidadesaude.query.QueryRelacaoGestantesSemAderenciaIndicadores;
import br.com.celk.report.unidadesaude.query.QueryRelacaoGestantesSemAderenciaIndicadoresHelper;
import br.com.celk.system.report.TipoRelatorio;
import br.com.celk.unidadesaude.esus.relatorios.RelacaoGestantesSemAderenciaIndicadoresDTOParam;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

import java.util.LinkedHashMap;

public class RelacaoGestantesSemAderenciaIndicadores extends AbstractReport<RelacaoGestantesSemAderenciaIndicadoresDTOParam> {

    public RelacaoGestantesSemAderenciaIndicadores(RelacaoGestantesSemAderenciaIndicadoresDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/unidadesaude/jrxml/relacao_gestantes_sem_aderencia.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("relacao_gestantes_sem_aderencia");
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelacaoGestantesSemAderenciaIndicadores();
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        return QueryRelacaoGestantesSemAderenciaIndicadoresHelper.getColumnMapping();
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return param.getTipoArquivo();
    }
}
