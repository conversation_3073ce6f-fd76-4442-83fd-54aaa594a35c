<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="impressao_decisao_processo_administrativo" columnDirection="RTL" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="56eab6e4-5301-4f11-a723-bfb165341f5f">
	<property name="ireport.zoom" value="2.9282000000000017"/>
	<property name="ireport.x" value="30"/>
	<property name="ireport.y" value="633"/>
	<import value="br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoDecisao"/>
	<import value="br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper"/>
	<import value="br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="USUARIO" class="java.lang.String"/>
	<parameter name="EMPRESA_LOGADA" class="java.lang.String"/>
	<parameter name="EMPRESA_LOGADA_ENDERECO" class="java.lang.String"/>
	<parameter name="urlQRcode" class="java.lang.String"/>
	<field name="processoAdministrativo" class="br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo">
		<fieldDescription><![CDATA[processoAdministrativo]]></fieldDescription>
	</field>
	<field name="processoAdministrativoDecisao" class="br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoDecisao">
		<fieldDescription><![CDATA[processoAdministrativoDecisao]]></fieldDescription>
	</field>
	<field name="vigilanciaEndereco" class="br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco">
		<fieldDescription><![CDATA[vigilanciaEndereco]]></fieldDescription>
	</field>
	<field name="enderecoFormatado" class="java.lang.String"/>
	<field name="descricaoAtividadePrincipal" class="java.lang.String"/>
	<field name="profissionalImpressao" class="br.com.ksisolucoes.vo.cadsus.Profissional">
		<fieldDescription><![CDATA[profissionalImpressao]]></fieldDescription>
	</field>
	<group name="Dados_estabelecimento">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band height="131">
				<printWhenExpression><![CDATA[$F{processoAdministrativo}.getTipoAutuado() == 0L]]></printWhenExpression>
				<rectangle radius="0">
					<reportElement x="0" y="7" width="555" height="120" isPrintWhenDetailOverflows="true" uuid="75521546-4ed3-44ca-af20-400bb3991b73"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="68" width="547" height="11" uuid="e5bbac40-e61c-4549-aeea-89ecc882d256"/>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{enderecoFormatado}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="81" width="417" height="11" uuid="634a99dc-fadd-45e4-a86f-e896dc179b62"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Atividade: ".toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="58" width="417" height="11" uuid="c245898f-1d51-4124-9428-514f872621c6"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_endereco").toUpperCase() + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement mode="Opaque" x="6" y="0" width="45" height="12" isPrintWhenDetailOverflows="true" uuid="b42ba35f-be4d-4f6d-ae9b-1d313a6da50b"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_autuado").toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="91" width="547" height="11" uuid="9996cb9f-5be8-4937-8654-8f21e61be9f8"/>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{enderecoFormatado}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="35" width="417" height="11" uuid="eda5f3fd-c9c6-48c2-8a37-7812374a311b"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Fantasia: ".toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="427" y="22" width="125" height="11" uuid="3756b22e-50cf-4bf7-87f2-8b0a5a72ccfd"/>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{processoAdministrativo}.getVigilanciaPessoa().getCpfFormatado()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="22" width="417" height="11" uuid="d1a6aaad-84e1-44eb-a9ae-4a9aeed9c024"/>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{processoAdministrativo}.getAutuado()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="114" width="547" height="11" uuid="1e5f2196-ab4a-4f62-8b80-7bfad626ab2b"/>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{processoAdministrativo}.getVigilanciaPessoa().getRepresentanteLegal()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="104" width="417" height="11" uuid="3fce755f-bbbd-422b-996e-83d07e571aaf"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Representante Legal: ".toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="12" width="417" height="11" uuid="58a2f741-3d9e-4c63-9f36-4a8d0160e272"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_pessoa_fissica_juridica") + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="45" width="547" height="11" uuid="6f537fef-c387-4524-9f3f-b4c47795ee73"/>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{processoAdministrativo}.getVigilanciaPessoa().getNomeFantasia()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="427" y="12" width="125" height="11" uuid="3edcfd2b-7c6d-4f86-aa1b-0fe876f1adb7"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["CPF ou CNPJ: "]]></textFieldExpression>
				</textField>
			</band>
			<band height="109">
				<printWhenExpression><![CDATA[$F{processoAdministrativo}.getTipoAutuado() == 1L]]></printWhenExpression>
				<rectangle radius="0">
					<reportElement x="0" y="7" width="555" height="98" isPrintWhenDetailOverflows="true" uuid="432ba63a-1d49-498d-8ff9-7058a5fb87a8"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="12" width="417" height="11" uuid="e8bbfa49-5399-498e-afa7-6607bc2f14d6"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_pessoa_fissica_juridica") + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="22" width="417" height="11" uuid="94d28041-fdb7-4ee9-85ff-2eed3bdb2fe4"/>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{processoAdministrativo}.getAutuado()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="427" y="12" width="125" height="11" uuid="63449a2d-2f1b-4f81-9ed1-64153af08c20">
						<printWhenExpression><![CDATA[$F{processoAdministrativo}.getTipoAutuado() == 1L]]></printWhenExpression>
					</reportElement>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cnpj") + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="427" y="22" width="125" height="11" uuid="db4707b3-6233-469c-9b96-d4f3d6e0665a">
						<printWhenExpression><![CDATA[$F{processoAdministrativo}.getTipoAutuado() == 1L]]></printWhenExpression>
					</reportElement>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{processoAdministrativo}.getEstabelecimento().getCnpjCpfFormatado()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="45" width="417" height="11" uuid="776ab0a9-c716-47ad-9bc5-b1906031f192">
						<printWhenExpression><![CDATA[$F{processoAdministrativo}.getTipoAutuado() == 1L]]></printWhenExpression>
					</reportElement>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{processoAdministrativo}.getEstabelecimento().getFantasia()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="35" width="417" height="11" uuid="4ca78843-4f43-4bbe-a148-0ec9eb39e280">
						<printWhenExpression><![CDATA[$F{processoAdministrativo}.getTipoAutuado() == 1L]]></printWhenExpression>
					</reportElement>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nome_fantasia").toUpperCase() + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="427" y="45" width="125" height="11" uuid="d0ea534e-8771-4a86-af41-fcba91f3586f">
						<printWhenExpression><![CDATA[$F{processoAdministrativo}.getTipoAutuado() == 1L]]></printWhenExpression>
					</reportElement>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{processoAdministrativo}.getEstabelecimento().getInscricaoEstadual()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="427" y="35" width="125" height="11" uuid="fd1eadb2-745c-4260-a1a0-ff5e20910b27">
						<printWhenExpression><![CDATA[$F{processoAdministrativo}.getTipoAutuado() == 1L]]></printWhenExpression>
					</reportElement>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_inscricao_estadual").toUpperCase() + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="58" width="417" height="11" uuid="360cbec0-8775-4111-b8b3-5699cd8d53ee"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_endereco").toUpperCase() + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="68" width="547" height="11" uuid="478978ab-75b0-4ac9-bf83-f0034f2c76c4">
						<printWhenExpression><![CDATA[$F{processoAdministrativo}.getTipoAutuado() == 1L]]></printWhenExpression>
					</reportElement>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{enderecoFormatado}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="91" width="417" height="11" uuid="84ff4ea6-e1ef-4a25-9328-d61554d0da3d">
						<printWhenExpression><![CDATA[$F{processoAdministrativo}.getTipoAutuado() == 1L]]></printWhenExpression>
					</reportElement>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{descricaoAtividadePrincipal}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="81" width="417" height="11" uuid="63d9b20b-e435-460f-af50-a5e6a81f968a"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_atividade").toUpperCase() + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="427" y="81" width="125" height="11" uuid="6692f43c-f645-4d12-9b9c-69b8b7575bf5"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_alvara").toUpperCase() + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="427" y="91" width="125" height="11" uuid="6741761a-4db3-46c3-b609-d6ae294e4432">
						<printWhenExpression><![CDATA[$F{processoAdministrativo}.getTipoAutuado() == 1L]]></printWhenExpression>
					</reportElement>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{processoAdministrativo}.getEstabelecimento().getAlvara()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement mode="Opaque" x="6" y="0" width="45" height="12" isPrintWhenDetailOverflows="true" uuid="cd98ef75-99c3-4e63-bc11-0aff550ce2d5"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_autuado").toUpperCase()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<pageHeader>
		<band height="12" splitType="Stretch">
			<printWhenExpression><![CDATA[$V{PAGE_NUMBER} > 1]]></printWhenExpression>
			<textField isBlankWhenNull="true">
				<reportElement key="" mode="Transparent" x="455" y="0" width="100" height="12" isRemoveLineWhenBlank="true" uuid="be83c82e-11fc-4bc2-8450-60b37b160645">
					<printWhenExpression><![CDATA[$V{PAGE_NUMBER} > 1]]></printWhenExpression>
				</reportElement>
				<box topPadding="1" leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[(
    Bundle.getStringApplication("rotulo_folha_n") +" : " + $V{PAGE_NUMBER}
).toUpperCase()]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="29">
			<rectangle radius="0">
				<reportElement x="0" y="7" width="555" height="18" uuid="9770723e-e9b9-40dc-9142-7a5f46007c5a"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement mode="Opaque" x="6" y="0" width="96" height="12" uuid="e9b363fe-53fc-4573-96b4-a68b9ea458e3"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dados_processuais").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="5" y="11" width="123" height="12" uuid="a74aeec0-6835-45f0-81bc-8baf1af31fe2"/>
				<box topPadding="1"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_processo_administrativo").toUpperCase() + ":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="128" y="11" width="88" height="12" uuid="8d89e83a-3f8e-477f-9882-6299f79f4d1e"/>
				<box topPadding="1"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{processoAdministrativo}.getNumeroProcessoFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="274" y="11" width="103" height="11" uuid="fe52965e-8f15-4d93-bb9d-2c1e1b408479"/>
				<box topPadding="1" rightPadding="4"/>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[ProcessoAdministrativo.Tipo.AUTO_INFRACAO.value().equals($F{processoAdministrativo}.getTipo()) ?
Bundle.getStringApplication("rotulo_auto_infracao").toUpperCase() + ":"
 :
 (ProcessoAdministrativo.Tipo.AUTO_MULTA.value().equals($F{processoAdministrativo}.getTipo()) ? Bundle.getStringApplication("rotulo_auto_multa").toUpperCase() + ":" :
     Bundle.getStringApplication("rotulo_solicitacao_juridica").toUpperCase())]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" mode="Transparent" x="377" y="11" width="88" height="11" uuid="a9c0cf16-6a05-40f6-9c31-d19c3bfd20a4"/>
				<box topPadding="1"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[ProcessoAdministrativo.Tipo.AUTO_INFRACAO.value().equals($F{processoAdministrativo}.getTipo()) ?
    $F{processoAdministrativo}.getAutoInfracao().getNumeroFormatado()
 :
 (ProcessoAdministrativo.Tipo.AUTO_MULTA.value().equals($F{processoAdministrativo}.getTipo()) ? $F{processoAdministrativo}.getAutoMulta().getNumeroFormatado() :
     $F{processoAdministrativo}.getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getProtocoloFormatado())]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="68">
			<rectangle radius="0">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="7" width="555" height="61" isPrintWhenDetailOverflows="true" uuid="b885f3ee-af1c-40e6-8c0a-8a5256af0225"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement mode="Opaque" x="6" y="0" width="45" height="12" isPrintWhenDetailOverflows="true" uuid="a071f2f2-a448-4dff-aa61-c0364c5d96b4"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_decisao").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="6" y="15" width="77" height="15" uuid="b1a2058d-bfa8-4846-8bd7-190dd6a8c566"/>
				<box bottomPadding="3"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle" markup="html">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{processoAdministrativoDecisao}.getCodigo()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="5" y="32" width="545" height="15" uuid="7cfa1ed0-6dcd-4470-bff2-b8bbe4f76c4a"/>
				<box bottomPadding="3"/>
				<textElement textAlignment="Justified" markup="html">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{processoAdministrativoDecisao}.getDecisaoAdministrativa()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="86" y="15" width="110" height="15" uuid="768aa2dc-5502-4b75-a4cc-a82699cc2d1f"/>
				<box bottomPadding="3"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle" markup="html">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{processoAdministrativoDecisao}.getDataDecisao()]]></textFieldExpression>
			</textField>
		</band>
		<band height="70">
			<textField isBlankWhenNull="true">
				<reportElement mode="Transparent" x="140" y="39" width="275" height="15" isPrintWhenDetailOverflows="true" uuid="7bb97dc8-a8a9-49ff-8d49-55bb2c7cb478"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{profissionalImpressao} != null ? $F{profissionalImpressao}.getNome() : "Autoridade Sanitária"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="140" y="54" width="275" height="13" uuid="91876ee8-e408-4bf1-99a1-2ae97992405b"/>
				<box topPadding="1" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{profissionalImpressao}.getReferenciaRegistroFormatado()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" x="140" y="38" width="275" height="1" isPrintWhenDetailOverflows="true" uuid="6156a71c-22ad-4e58-931d-e14897d06e52">
					<printWhenExpression><![CDATA[VigilanciaHelper.exibirLinhaAssinatura()]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
		</band>
		<band height="68">
			<image scaleImage="RealSize">
				<reportElement x="7" y="5" width="60" height="60" uuid="56f46d90-3d6a-4d74-830e-1e464a60d534"/>
				<imageExpression><![CDATA[com.google.zxing.client.j2se.MatrixToImageWriter.toBufferedImage(
    new com.google.zxing.qrcode.QRCodeWriter().encode(
            $P{urlQRcode},
            com.google.zxing.BarcodeFormat.QR_CODE, 600, 600))]]></imageExpression>
			</image>
			<line>
				<reportElement x="60" y="66" width="10" height="1" uuid="02d3ee7d-4d4b-48eb-9991-a6aed7e1a216"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="69" y="57" width="1" height="10" uuid="e3f0a9af-19b4-46a8-a820-efdfe829b727"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="69" y="2" width="1" height="10" uuid="2a4b7ccc-e0b9-4e29-bd1b-4147d6454260"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="60" y="2" width="10" height="1" uuid="f7f890c8-2133-4f7c-b94f-1d724c5ba707"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="4" y="2" width="1" height="10" uuid="fff169fd-7586-44f0-8327-0a649a78500a"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="4" y="2" width="10" height="1" uuid="d639a5a0-ae00-4b4b-a197-38052d675324"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="4" y="66" width="10" height="1" uuid="e8cf77a8-d8c0-4b4c-baae-05a6a9a01ad8"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="4" y="57" width="1" height="10" uuid="ab13827d-8b49-4b65-9709-925455ca0b6b"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="83" y="10" width="305" height="19" uuid="25b42cae-c78e-460c-842d-1fb3b9a5299c"/>
				<textElement textAlignment="Justified" verticalAlignment="Top">
					<font fontName="Arial" size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Verifique a autenticidade do documento no seu dispositivo móvel, através do QR Code ao lado."]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
