package br.com.celk.report.vigilancia.query;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoRubeolaCongenita;
import org.hibernate.Session;

import java.util.HashMap;
import java.util.Map;

public class QueryFichaRubeolaCongenita extends QueryFichaInvestigacaoBase {

    @Override
    protected Class getClasseFichaInvestigacao() { return InvestigacaoAgravoRubeolaCongenita.class; }

    @Override
    protected Map<String, String> getCamposInvestigacaoAgravo() {
        Map<String, String> campos = new HashMap<>();

        campos.put("investigacaoAgravo.codigo", "investigacaoAgravo_codigo");

        campos.put(formatarData("investigacaoAgravo.dataInvestigacao"), "_31_dataInvestigacao");

        campos.put("investigacaoAgravo.recemNascido", "_32_recemNascido");
        campos.put("investigacaoAgravo.pesoAoNascer", "_33_pesoAoNascer");

        campos.put("investigacaoAgravo.sinalMaiorCatarata", "_34_sinalMaiorCatarata");
        campos.put("investigacaoAgravo.sinalMaiorRetinopatiaPigmentar", "_34_sinalMaiorRetinopatiaPigmentar");
        campos.put("investigacaoAgravo.sinalMaiorGlaucomaCongenito", "_34_sinalMaiorGlaucomaCongenito");
        campos.put("investigacaoAgravo.sinalMaiorDeficienciaAuditiva", "_34_sinalMaiorDeficienciaAuditiva");
        campos.put("investigacaoAgravo.sinalMaiorCardiopatiaCongenita", "_34_sinalMaiorCardiopatiaCongenita");
        campos.put("investigacaoAgravo.sinalMaiorCardiopatiaCongenitaOutro", "_34_sinalMaiorCardiopatiaCongenitaOutro");

        campos.put("investigacaoAgravo.sinalMenorRetardoPsicomotor", "_35_sinalMenorRetardoPsicomotor");
        campos.put("investigacaoAgravo.sinalMenorMicrocefalia", "_35_sinalMenorMicrocefalia");
        campos.put("investigacaoAgravo.sinalMenorMeningoencefalite", "_35_sinalMenorMeningoencefalite");
        campos.put("investigacaoAgravo.sinalMenorHepatoesplenomegalia", "_35_sinalMenorHepatoesplenomegalia");
        campos.put("investigacaoAgravo.sinalMenorIctericia", "_35_sinalMenorIctericia");
        campos.put("investigacaoAgravo.sinalMenorPurpura", "_35_sinalMenorPurpura");
        campos.put("investigacaoAgravo.sinalMenorAlteracoesOsseas", "_35_sinalMenorAlteracoesOsseas");

        campos.put(formatarData("investigacaoAgravo.dataColetaSangue1"), "_36_dataColetaSangue1");
        campos.put(formatarData("investigacaoAgravo.dataColetaSangue2"), "_37_dataColetaSangue2");
        campos.put(formatarData("investigacaoAgravo.dataColetaSangue3"), "_38_dataColetaSangue3");


        campos.put("investigacaoAgravo.resultadoExameSorologicoS1Igm", "_39_resultadoExameSorologicoS1Igm");
        campos.put("investigacaoAgravo.resultadoExameSorologicoS1Igg", "_39_resultadoExameSorologicoS1Igg");
        campos.put("investigacaoAgravo.resultadoExameSorologicoS2Igm", "_39_resultadoExameSorologicoS2Igm");
        campos.put("investigacaoAgravo.resultadoExameSorologicoS2Igg", "_39_resultadoExameSorologicoS2Igg");
        campos.put("investigacaoAgravo.resultadoExameSorologicoS3Igm", "_39_resultadoExameSorologicoS3Igm");
        campos.put("investigacaoAgravo.resultadoExameSorologicoS3Igg", "_39_resultadoExameSorologicoS3Igg");


        campos.put("investigacaoAgravo.amostraClinicaSangueTotal", "_40_amostraClinicaSangueTotal");
        campos.put("investigacaoAgravo.amostraClinicaSecrecaoNasofaringea", "_40_amostraClinicaSecrecaoNasofaringea");
        campos.put("investigacaoAgravo.amostraClinicaUrina", "_40_amostraClinicaUrina");
        campos.put("investigacaoAgravo.amostraClinicaLiquor", "_40_amostraClinicaLiquor");

        campos.put("investigacaoAgravo.deteccaoViralResultado", "_41_deteccaoViralResultado");
        campos.put("investigacaoAgravo.deteccaoViralResultadoOutro", "_41_deteccaoViralResultadoOutro");


        campos.put("investigacaoAgravo.bloqueioVacinalContatos", "_42_bloqueioVacinalContatos");
        campos.put("investigacaoAgravo.isolamentoRecemNascido", "_43_isolamentoRecemNascido");


        campos.put("investigacaoAgravo.maeIdade", "_44_maeIdade");
        campos.put("investigacaoAgravo.vacinacaoTripliceViral", "_45_vacinacaoTripliceViral");
        campos.put("investigacaoAgravo.vacinacaoRubeola", "_45_vacinacaoRubeola");
        campos.put("investigacaoAgravo.vacinacaoDuplaViral", "_45_vacinacaoDuplaViral");
        campos.put(formatarData("investigacaoAgravo.dataUltimaDoseVacina"), "_46_dataUltimaDoseVacina");
        campos.put("investigacaoAgravo.sinalSintoma", "_47_sinalSintoma");
        campos.put("investigacaoAgravo.periodoGestacaoAcometida", "_48_periodoGestacaoAcometida");
        campos.put("investigacaoAgravo.criterioConfirmacaoDiagnosticoMae", "_49_criterioConfirmacaoDiagnosticoMae");
        campos.put("investigacaoAgravo.classificacaoFinal", "_50_classificacaoFinal");
        campos.put("investigacaoAgravo.criterioConfirmacaoDescarte", "_51_criterioConfirmacaoDescarte");
        campos.put("investigacaoAgravo.diagnosticoDescarte", "_52_diagnosticoDescarte");
        campos.put("investigacaoAgravo.diagnosticoDescarteOutros", "_52_diagnosticoDescarteOutros");
        campos.put("investigacaoAgravo.evolucao", "_53_evolucao");
        campos.put(formatarData("investigacaoAgravo.dataObito"), "_53_dataObito");

        campos.put("investigacaoAgravo.observacao", "_observacao");

        campos.put("investigacaoAgravo.usuarioEncerramento.nome", "usuarioEncerramentoNome");

        return campos;
    }

    @Override
    protected String getJuncoesFicha() {
        return "";
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        super.customProcess(session);
        for (Map<String, Object> map : getResult()) {
        }
    }


    private String coalesce(String one, String two) {
        return (one != null) ? one : ((two != null) ? two : "");
    }

}
