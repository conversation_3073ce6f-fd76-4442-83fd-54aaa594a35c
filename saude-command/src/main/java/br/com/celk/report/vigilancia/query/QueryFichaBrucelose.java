package br.com.celk.report.vigilancia.query;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoBrucelose;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoBruceloseExame;
import org.hibernate.Session;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

public class QueryFichaBrucelose extends QueryFichaInvestigacaoBase {

    @Override
    protected Class getClasseFichaInvestigacao() {
        return InvestigacaoAgravoBrucelose.class;
    }

    @Override
    protected Map<String, String> getCamposInvestigacaoAgravo() {
        Map<String, String> campos = new HashMap<>();

//        ANTECENDENTES EPIDEMIOLOGICOS
        campos.put("ocupacaoCbo.descricao", "_24_ocupacao");
        campos.put("investigacaoAgravo.ddViajouUltimos6Meses", "_25_viagens_ultimos_6_meses_dd");
        campos.put("investigacaoAgravo.txtViajouUltimos6Meses", "_25_viagens_ultimos_6_meses_txt");
        campos.put("investigacaoAgravo.ddConsumoAlimentosLeiteNaoPasteurizado", "_26_consumo_alimento_leite_nao_pasteurizado");
        campos.put("investigacaoAgravo.ddConsumoAlimentosComQueijo", "_26_consumo_alimento_com_queijo");
        campos.put("investigacaoAgravo.ddConsumoAlimentosQueijoFresco", "_26_consumo_alimento_queijo_fresco");
        campos.put("investigacaoAgravo.ddConsumoAlimentosComLeite", "_26_consumo_alimento_com_leite");
        campos.put("investigacaoAgravo.ddConsumoAlimentosCrus", "_26_consumo_alimento_crus_dd");
        campos.put("investigacaoAgravo.txtConsumoAlimentosCrus", "_26_consumo_alimento_crus_txt");
        campos.put("investigacaoAgravo.ddConsumoAlimentosLugarCompraConsumo", "_26_consumo_alimento_lugar_compra_consumo_dd");
        campos.put("investigacaoAgravo.txtConsumoAlimentosLugarCompraConsumoEspecificar", "_26_consumo_alimento_lugar_compra_consumo_txt");

        campos.put("investigacaoAgravo.ddContatoOcupacionalTrabalhaCampo", "_27_contato_ocupacional_trabalha_campo_dd");
        campos.put("investigacaoAgravo.ddContatoOcupacionalOrdenha", "_27_contato_ocupacional_ordenha_dd");
        campos.put("investigacaoAgravo.ddContatoOcupacionalAbortoAnimais", "_27_contato_ocupacional_abordo_animais_dd");
        campos.put("investigacaoAgravo.ddContatoOcupacionalTrabalhaLaboratorio", "_27_contato_ocupacional_laboratorio_dd");
        campos.put("investigacaoAgravo.ddContatoOcupacionalTrabalhaVacina", "_27_contato_ocupacional_vacina_dd");
        campos.put("investigacaoAgravo.txtContatoOcupacionalTipoContato", "_27_contato_ocupacional_tipo_contato_txt");

        campos.put("investigacaoAgravo.ddDiagnosticoAnteriorBrucelose", "_28_diagnostico_anterior_dd");
        campos.put("investigacaoAgravo.txtDiagnosticoAnteriorQuando", "_28_diagnosticvo_anterior_quando_txt");
        campos.put("investigacaoAgravo.txtDiagnosticoAnteriorOnde", "_28_diagnostico_anterior_onde_txt");
        campos.put("investigacaoAgravo.txtDiagnosticoAnteriorTratamento", "_28_diagnostico_anterior_tratamento_txt");
        campos.put("investigacaoAgravo.txtDiagnosticoAnteriorDuracao", "_28_diagnostico_anterior_duracao_txt");
        campos.put("investigacaoAgravo.txtDiagnosticoAnteriorObservacao", "_28_diagnostico_anterior_observacao_txt");


//        INFORMAÇÕES CLÍNICAS
        campos.put(formatarData("investigacaoAgravo.dataInicioSintomas"), "_29_data_inicio_sintomas");
        campos.put("investigacaoAgravo.txtTempoInfermidade", "_30_tempo_enfermidade");
        campos.put("investigacaoAgravo.ddFormaInicioEnfermidade", "_33_forma_inicio_enfermidade");

        campos.put("investigacaoAgravo.ddDadosClinicosFebre", "_32_dados_clinicos_febre_dd");
        campos.put("investigacaoAgravo.txtDadosClinicosFebre", "_32_dados_clinicos_febre_txt");
        campos.put("investigacaoAgravo.ddDadosClinicosCalafrios", "_32_dados_clinicos_calafrios");
        campos.put("investigacaoAgravo.ddDadosClinicosSudoreseProfusa", "_32_dados_clinicos_sudorese_profusa");
        campos.put("investigacaoAgravo.ddDadosClinicosAnorexia", "_32_dados_clinicos_anorexia");
        campos.put("investigacaoAgravo.ddDadosClinicosCefaleia", "_32_dados_clinicos_cefaleia");
        campos.put("investigacaoAgravo.ddDadosClinicosDiarreia", "_32_dados_clinicos_diarreia");
        campos.put("investigacaoAgravo.ddDadosClinicosDorArticular", "_32_dados_clinicos_dor_articular");
        campos.put("investigacaoAgravo.ddDadosClinicosMalEstarGeral", "_32_dados_clinicos_mal_estar_geral");
        campos.put("investigacaoAgravo.ddDadosClinicosNauseas", "_32_dados_clinicos_nauseas");
        campos.put("investigacaoAgravo.ddDadosClinicosDorMuscular", "_32_dados_clinicos_dor_muscular");
        campos.put("investigacaoAgravo.ddDadosClinicosVomitos", "_32_sinais_clinicos_vomito");
        campos.put("investigacaoAgravo.ddDadosClinicosAstenia", "_32_dados_clinicos_astenia");
        campos.put("investigacaoAgravo.txtDadosClinicosOutro", "_32_dados_clinicos_outro_txt");

        campos.put("investigacaoAgravo.ddSinaisClinicosAdenopatias", "_32_sinais_clinicos_adenopatias");
        campos.put("investigacaoAgravo.ddSinaisClinicosHepatomegalia", "_32_sinais_clinicos_hepatomegalia");
        campos.put("investigacaoAgravo.ddSinaisClinicosComprometimentoCardiovascular", "_32_sinais_clinicos_comprome_cardiovascular");
        campos.put("investigacaoAgravo.ddSinaisClinicosEsplenomegalia", "_32_sinais_clinicos_esplenomegalia");
        campos.put("investigacaoAgravo.ddSinaisClinicosComprometimentoOsteoarticular", "_32_sinais_clinicos_comprome_osteoarticular");
        campos.put("investigacaoAgravo.ddSinaisClinicosComprometimentoNeurologico", "_32_sinais_clinicos_comprome_neurologico");
        campos.put("investigacaoAgravo.ddSinaisClinicosGenitoUrinario", "_32_sinais_clinicos_comprome_genito_urinario");
        campos.put("investigacaoAgravo.txtSinaisClinicosOutros", "_32_sinais_clinicos_outros_txt");


//        HOSPITALIZACAO/TRATAMENTO
        campos.put("investigacaoAgravo.RdHospitalizacao", "_32_hospitalizado");
        campos.put(formatarData("investigacaoAgravo.dataInternacao"), "_32_data_internacao");
        campos.put("investigacaoAgravo.txtTempoInternadoEmDias", "_32_tempo_internado");
        campos.put("investigacaoAgravo.txtObsExameLab", "_32_obs_exame_lab");
        campos.put("investigacaoAgravo.classificacao", "_35_classificacao");
        campos.put("investigacaoAgravo.formaClinica", "_36_forma_clinica");
        campos.put("investigacaoAgravo.formaClinicaCronica", "_36_forma_clinica_cronica");


//        TRATAMENTO
        campos.put("investigacaoAgravo.tratamentoAnterior", "_37_tratamento_anteriores");
        campos.put(formatarData("investigacaoAgravo.dataTratamentoAnterior"), "_37_data_tratamento_anterior");

        campos.put("investigacaoAgravo.ddTratamentoAtualDoxiciclina", "_38_tratamento_atual_doxiciclina_dd");
        campos.put("investigacaoAgravo.txtTratamentoAtualDiasDoxiciclina", "_38_tratamento_atual_doxiciclina_dias_txt");
        campos.put(formatarData("investigacaoAgravo.dtTratamentoAtualDataInicioDoxiciclina"), "_38_tratamento_atual_doxiciclina_data_inicio");

        campos.put("investigacaoAgravo.ddTratamentoAtualCiprofloxacina", "_38_tratamento_atual_ciprofloxacina_dd");
        campos.put("investigacaoAgravo.txtTratamentoAtualDiasCiprofloxacina", "_38_tratamento_atual_ciprofloxacina_dias_txt");
        campos.put(formatarData("investigacaoAgravo.dtTratamentoAtualDataInicioCiprofloxacina"), "_38_tratamento_atual_ciprofloxacina_data_inicio");

        campos.put("investigacaoAgravo.ddTratamentoAtualGentamicina", "_38_tratamento_atual_gentamicina_dd");
        campos.put("investigacaoAgravo.txtTratamentoAtualDiasGentamicina", "_38_tratamento_atual_gentamicina_dias_txt");
        campos.put(formatarData("investigacaoAgravo.dtTratamentoAtualDataInicioGentamicina"), "_38_tratamento_atual_gentamicina_data_inicio");

        campos.put("investigacaoAgravo.ddTratamentoAtualSmzTmp", "_38_tratamento_atual_smzTmp_dd");
        campos.put("investigacaoAgravo.txtTratamentoAtualDiasSmzTmp", "_38_tratamento_atual_smzTmp_dias_txt");
        campos.put(formatarData("investigacaoAgravo.dtTratamentoAtualDataInicioSmzTmp"), "_38_tratamento_atual_smzTmp_data_inicio");

        campos.put("investigacaoAgravo.ddTratamentoAtualRifampicina", "_38_tratamento_atual_rifampicina_dd");
        campos.put("investigacaoAgravo.txtTratamentoAtualDiasRifampicina", "_38_tratamento_atual_rifampicina_dias_txt");
        campos.put(formatarData("investigacaoAgravo.dtTratamentoAtualDataInicioRifampicina"), "_38_tratamento_atual_rifampicina_data_inicio");

        campos.put("investigacaoAgravo.ddTratamentoAtualEstreptomicina", "_38_tratamento_atual_estreptomicina_dd");
        campos.put("investigacaoAgravo.txtTratamentoAtualDiasEstreptomicina", "_38_tratamento_atual_estreptomicina_dias_txt");
        campos.put(formatarData("investigacaoAgravo.dtTratamentoAtualDataInicioEstreptomicina"), "_38_tratamento_atual_estreptomicina_data_inicio");

        campos.put("investigacaoAgravo.ddTratamentoAtualCotrimoxazol", "_38_tratamento_atual_cotrimoxazol_dd");
        campos.put("investigacaoAgravo.txtTratamentoAtualDiasCotrimoxazol", "_38_tratamento_atual_cotrimoxazol_dias_txt");
        campos.put(formatarData("investigacaoAgravo.dtTratamentoAtualDataInicioCotrimoxazol"), "_38_tratamento_atual_cotrimoxazol_data_inicio");

        campos.put("investigacaoAgravo.ddTratamentoAtualEritromicina", "_38_tratamento_atual_eritromicina_dd");
        campos.put("investigacaoAgravo.txtTratamentoAtualDiasEritromicina", "_38_tratamento_atual_eritromicina_dias_txt");
        campos.put(formatarData("investigacaoAgravo.dtTratamentoAtualDataInicioEritromicina"), "_38_tratamento_atual_eritromicina_data_inicio");


//        ENCERRAMENTO
        campos.put("investigacaoAgravo.evolucaoCaso", "_39_evolucao_paciente");
        campos.put(formatarData("investigacaoAgravo.dataObito"), "_39_data_obito");
        campos.put("investigacaoAgravo.encerramentoObservacao", "_39_obs");
        campos.put(formatarData("investigacaoAgravo.dataEncerramento"), "_40_data_encerramento");
        campos.put("usuario.nome", "_40_usuario_encerramento");

        return campos;
    }

    @Override
    protected String getJuncoesFicha() {
        return
                "left join investigacaoAgravo.ocupacaoCbo ocupacaoCbo " +
                        "left join investigacaoAgravo.ocupacaoCbo ocupacaoCbo " +
                        "left join investigacaoAgravo.usuarioEncerramento usuario "
                ;
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        super.customProcess(session);
        for (Map<String, Object> map : getResult()) {
            addTabelaCBO(map);
            addListaExames(map);
        }
    }

    private void addTabelaCBO(Map<String, Object> map) {
        TabelaCbo cbo = (TabelaCbo) map.get("ocupacaoCbo.descricao");
        if (cbo != null) {
            map.put("ocupacaoCbo.descricao", cbo.getDescricaoFormatado());
        }
    }

    private void addListaExames(Map<String, Object> map) {
        int i;
        Long codigoInvestigacaoAgravoBrucelose = (Long) map.get("investigacaoAgravo_codigo");

        List<InvestigacaoAgravoBruceloseExame> exameList =
                LoadManager.getInstance(InvestigacaoAgravoBruceloseExame.class)
                        .addProperties(new HQLProperties(InvestigacaoAgravoBruceloseExame.class).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(
                                VOUtils.montarPath(InvestigacaoAgravoBruceloseExame.PROP_INVESTIGACAO_AGRAVO_BRUCELOSE, InvestigacaoAgravoBrucelose.PROP_CODIGO),
                                codigoInvestigacaoAgravoBrucelose))
                        .start().getList();

        for (i = 1; i <= exameList.size(); i++) {
            //Subtrai 1, pois a lista no pdf começa com pelo 1 e em código por 0.
            int x = i -1;
            InvestigacaoAgravoBruceloseExame investigacaoAgravoBruceloseExame = exameList.get(x);

            map.put("_exame_exame_" + i, (investigacaoAgravoBruceloseExame.getExame()));
            map.put("_exame_data_" + i, Data.formatar(investigacaoAgravoBruceloseExame.getDataExame()));
            map.put("_exame_resultado_" + i, (investigacaoAgravoBruceloseExame.getResultadoExame()));
        }
    }

}
