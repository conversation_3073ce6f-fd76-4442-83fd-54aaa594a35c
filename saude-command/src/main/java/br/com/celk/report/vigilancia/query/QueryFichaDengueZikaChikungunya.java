package br.com.celk.report.vigilancia.query;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDengueZikaChikungunya;
import org.hibernate.Session;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class QueryFichaDengueZikaChikungunya extends QueryFichaInvestigacaoBase  {

    @Override
    protected Class getClasseFichaInvestigacao() {
        return InvestigacaoAgravoDengueZikaChikungunya.class;
    }

    @Override
    protected Map<String, String> getCamposInvestigacaoAgravo() {
        Map<String, String> campos = new HashMap<>();
        campos.put("investigacaoAgravo.dengueChikungunyaZika", "_2_dengue_chikungunya_zika");

        campos.put(formatarData("investigacaoAgravo.dataInvestigacao"), "_31_data_investigacao");
        campos.put("investigacaoAgravo.ocupacaoCbo", "_32_ocupacao_cbo");

        campos.put("investigacaoAgravo.febre", "_33_febre");
        campos.put("investigacaoAgravo.mialgia", "_33_mialgia");
        campos.put("investigacaoAgravo.cefaleia", "_33_cefaleia");
        campos.put("investigacaoAgravo.exantema", "_33_exantema");
        campos.put("investigacaoAgravo.vomito", "_33_vomito");
        campos.put("investigacaoAgravo.nauseas", "_33_nauseas");
        campos.put("investigacaoAgravo.dorCostas", "_33_dor_costas");
        campos.put("investigacaoAgravo.conjuntivite", "_33_conjuntivite");
        campos.put("investigacaoAgravo.artrite", "_33_artrite");
        campos.put("investigacaoAgravo.artralgiaIntensa", "_33_artralgiaIntensa");
        campos.put("investigacaoAgravo.petequias", "_33_petequias");
        campos.put("investigacaoAgravo.leucopenia", "_33_leucopenia");
        campos.put("investigacaoAgravo.provaLacoPositiva", "_33_prova_laco_positiva");
        campos.put("investigacaoAgravo.dorRetroorbital", "_33_dor_retroorbital");

        campos.put("investigacaoAgravo.diabetes", "_34_diabetes");
        campos.put("investigacaoAgravo.doencasHematologicas", "_34_doencas_hematologicas");
        campos.put("investigacaoAgravo.hepatopatias", "_34_hepatopatias");
        campos.put("investigacaoAgravo.doencaRenalCronica", "_34_doenca_renal_cronica");
        campos.put("investigacaoAgravo.hipertensaoArterial", "_34_hipertensao_arterial");
        campos.put("investigacaoAgravo.doencaAcidoPeptica", "_34_doenca_acido_peptica");
        campos.put("investigacaoAgravo.doencaAutoImunes", "_34_doenca_auto_imunes");

        campos.put(formatarData("investigacaoAgravo.dataColeta1AmostraChik"), "_35_data_coleta_1_amostra");
        campos.put(formatarData("investigacaoAgravo.dataColeta2AmostraChik"), "_36_data_coleta_2_amostra");
        campos.put(formatarData("investigacaoAgravo.dataColetaPrntChik"), "_37_data_coleta_prnt");
        campos.put("investigacaoAgravo.resultadosLaborais1AmostraChik", "_38_resultados_laborais_1_amostra");
        campos.put("investigacaoAgravo.resultadosLaborais2AmostraChik", "_38_resultados_laborais_2_amostra");
        campos.put("investigacaoAgravo.resultadosLaboraisPrntChik", "_38_resultados_laborais_prnt");
        campos.put(formatarData("investigacaoAgravo.dataSorologiaDengue"), "_39_data_coleta_sorologia");
        campos.put("investigacaoAgravo.resultadoSorologiaDengue", "_40_resultados_sorologia");
        campos.put(formatarData("investigacaoAgravo.dataColetaNs1Dengue"), "_41_data_coleta_ns1");
        campos.put("investigacaoAgravo.resultadoNs1Dengue", "_42_resultados_ns1");
        campos.put(formatarData("investigacaoAgravo.dataIsolamentoDengue"), "_43_data_coleta_isolamento");
        campos.put("investigacaoAgravo.resultadoIsolamentoDengue", "_44_resultados_isolamento");
        campos.put(formatarData("investigacaoAgravo.dataRtPcrDengue"), "_45_data_coleta_rtpcr");
        campos.put("investigacaoAgravo.resultadoRtPcrDengue", "_46_resultados_rtpcr");
        campos.put("investigacaoAgravo.sorotipoLaboralDengue", "_47_sorotipo");
        campos.put("investigacaoAgravo.histopatologiaDengue", "_48_resultados_histopatologia");
        campos.put("investigacaoAgravo.imunohistoquimicaDengue", "_49_resultados_imunohistoquimica");

        campos.put("investigacaoAgravo.hospitalizacao", "_50_hospitalizacao");
        campos.put(formatarData("investigacaoAgravo.dataInternacao"), "_51_data_internacao");
        campos.put("estadoHospital.sigla", "_52_estado");
        campos.put("unidadeHospital.cidade.descricao", "_53_cidade");
        campos.put("unidadeHospital.cidade.codigo", "_53_ibge");
        campos.put("investigacaoAgravo.unidadeHospital.descricao", "_54_hospital");
        campos.put("investigacaoAgravo.unidadeHospital.cnes", "_54_cnes");
        campos.put("investigacaoAgravo.unidadeHospital.telefone", "_55_telefone");

        campos.put("investigacaoAgravo.casoAutoctone", "_56_caso_autoctone");
        campos.put("estadoLocalInfeccao.sigla", "_57_estado");
        campos.put("investigacaoAgravo.paisLocalInfeccao", "_58_pais");
        campos.put("investigacaoAgravo.cidadeLocalInfeccao.descricao", "_59_cidade");
        campos.put("investigacaoAgravo.cidadeLocalInfeccao.codigo", "_59_ibge");
        campos.put("investigacaoAgravo.distritoLocalInfeccao", "_60_distrito");
        campos.put("investigacaoAgravo.bairroLocalInfeccao", "_61_bairro");

        campos.put("investigacaoAgravo.classificacao", "_62_classificacao");
        campos.put("investigacaoAgravo.criterioDescarteConfirmacao", "_63_criterio_confirmacao_descarte");
        campos.put("investigacaoAgravo.apresentacaoClinica", "_64_apresentacao_clinica");
        campos.put("investigacaoAgravo.evolucaoCaso", "_65_evolucao_caso");
        campos.put(formatarData("investigacaoAgravo.dataObito"), "_66_data_obito");

        campos.put("investigacaoAgravo.hipotensao", "_68_hipotensao");
        campos.put("investigacaoAgravo.quedaPlaquetas", "_68_queda_plaquetas");
        campos.put("investigacaoAgravo.vomitosPersistentes", "_68_vomitos_persistentes");
        campos.put("investigacaoAgravo.dorAbdominal", "_68_dor_abdominal");
        campos.put("investigacaoAgravo.letargia", "_68_letargia");
        campos.put("investigacaoAgravo.sangramentoMucosa", "_68_sangramento_mucosa");
        campos.put("investigacaoAgravo.aumentoHematocrito", "_68_aumento_hematocrito");
        campos.put("investigacaoAgravo.hepatomegalia", "_68_hepatomegalia");
        campos.put("investigacaoAgravo.acumuloLiquido", "_68_acumulo_liquido");
        campos.put(formatarData("investigacaoAgravo.dataSinaisAlarme"), "_69_data_sinais_alarme");

        campos.put("investigacaoAgravo.pulsoDebil", "_70_pulso_debil");
        campos.put("investigacaoAgravo.paConvergente", "_70_pa_convergente");
        campos.put("investigacaoAgravo.enchimentoCapilar", "_70_enchimento_capilar");
        campos.put("investigacaoAgravo.liquidoInsuficiencia", "_70_liquido_insuficiencia");
        campos.put("investigacaoAgravo.taquicardia", "_70_taquicardia");
        campos.put("investigacaoAgravo.extremidadesFrias", "_70_extremidades_frias");
        campos.put("investigacaoAgravo.hipotensaoTardia", "_70_hipotensao_tardia");
        campos.put("investigacaoAgravo.hematemese", "_70_hematemese");
        campos.put("investigacaoAgravo.melena", "_70_melena");
        campos.put("investigacaoAgravo.metrorragia", "_70_metrorragia");
        campos.put("investigacaoAgravo.sangramentoSnc", "_70_sangramento_snc");
        campos.put("investigacaoAgravo.astAlt", "_70_ast_alt");
        campos.put("investigacaoAgravo.miocardite", "_70_miocardite");
        campos.put("investigacaoAgravo.alteracaoConsciencia", "_70_alteracao_consciencia");
        campos.put("investigacaoAgravo.outrosOrgaos", "_70_outros_orgaos");
        campos.put("investigacaoAgravo.outrosOrgaosDescricao", "_70_outros_orgaos_descricao");
        campos.put(formatarData("investigacaoAgravo.dataGravidade"), "_71_data_gravidade");
        campos.put(formatarObservacao("investigacaoAgravo.observacao"), "_observacao");

        return campos;
    }

    @Override
    protected String getJuncoesFicha() {
        return "left join investigacaoAgravo.ocupacaoCbo ocupacaoCbo "
                + "left join investigacaoAgravo.unidadeHospital unidadeHospital "
                + "left join unidadeHospital.cidade cidadeHospital "
                + "left join cidadeHospital.estado estadoHospital "
                + "left join investigacaoAgravo.cidadeLocalInfeccao cidadeLocalInfeccao "
                + "left join cidadeLocalInfeccao.estado estadoLocalInfeccao "
                + "left join investigacaoAgravo.paisLocalInfeccao paisLocalInfeccao ";
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        super.customProcess(session);
        for (Map<String, Object> map : (List<Map<String, Object>>) getResult()) {
            addTabelaCBO(map);
            addNaoComboBox("_33_febre", "_33_febre", map);
            addNaoComboBox("_33_mialgia", "_33_mialgia", map);
            addNaoComboBox("_33_cefaleia", "_33_cefaleia", map);
            addNaoComboBox("_33_exantema", "_33_exantema", map);
            addNaoComboBox("_33_vomito", "_33_vomito", map);
            addNaoComboBox("_33_nauseas", "_33_nauseas", map);
            addNaoComboBox("_33_dor_costas", "_33_dor_costas", map);
            addNaoComboBox("_33_conjuntivite", "_33_conjuntivite", map);
            addNaoComboBox("_33_artrite", "_33_artrite", map);
            addNaoComboBox("_33_artralgiaIntensa", "_33_artralgiaIntensa", map);
            addNaoComboBox("_33_petequias", "_33_petequias", map);
            addNaoComboBox("_33_leucopenia", "_33_leucopenia", map);
            addNaoComboBox("_33_prova_laco_positiva", "_33_prova_laco_positiva", map);
            addNaoComboBox("_33_dor_retroorbital", "_33_dor_retroorbital", map);

            addNaoComboBox("_34_diabetes", "_34_diabetes", map);
            addNaoComboBox("_34_doencas_hematologicas", "_34_doencas_hematologicas", map);
            addNaoComboBox("_34_hepatopatias", "_34_hepatopatias", map);
            addNaoComboBox("_34_doenca_renal_cronica", "_34_doenca_renal_cronica", map);
            addNaoComboBox("_34_hipertensao_arterial", "_34_hipertensao_arterial", map);
            addNaoComboBox("_34_doenca_acido_peptica", "_34_doenca_acido_peptica", map);
            addNaoComboBox("_34_doenca_auto_imunes", "_34_doenca_auto_imunes", map);

            addNaoComboBox("_68_hipotensao", "_68_hipotensao", map);
            addNaoComboBox("_68_queda_plaquetas", "_68_queda_plaquetas", map);
            addNaoComboBox("_68_vomitos_persistentes", "_68_vomitos_persistentes", map);
            addNaoComboBox("_68_dor_abdominal", "_68_dor_abdominal", map);
            addNaoComboBox("_68_letargia", "_68_letargia", map);
            addNaoComboBox("_68_sangramento_mucosa", "_68_sangramento_mucosa", map);
            addNaoComboBox("_68_aumento_hematocrito", "_68_aumento_hematocrito", map);
            addNaoComboBox("_68_hepatomegalia", "_68_hepatomegalia", map);
            addNaoComboBox("_68_acumulo_liquido", "_68_acumulo_liquido", map);

            addNaoComboBox("_70_pulso_debil", "_70_pulso_debil", map);
            addNaoComboBox("_70_pa_convergente", "_70_pa_convergente", map);
            addNaoComboBox("_70_enchimento_capilar", "_70_enchimento_capilar", map);
            addNaoComboBox("_70_liquido_insuficiencia", "_70_liquido_insuficiencia", map);
            addNaoComboBox("_70_taquicardia", "_70_taquicardia", map);
            addNaoComboBox("_70_extremidades_frias", "_70_extremidades_frias", map);
            addNaoComboBox("_70_hipotensao_tardia", "_70_hipotensao_tardia", map);
            addNaoComboBox("_70_hematemese", "_70_hematemese", map);
            addNaoComboBox("_70_melena", "_70_melena", map);
            addNaoComboBox("_70_metrorragia", "_70_metrorragia", map);
            addNaoComboBox("_70_sangramento_snc", "_70_sangramento_snc", map);
            addNaoComboBox("_70_ast_alt", "_70_ast_alt", map);
            addNaoComboBox("_70_miocardite", "_70_miocardite", map);
            addNaoComboBox("_70_alteracao_consciencia", "_70_alteracao_consciencia", map);
            addNaoComboBox("_70_outros_orgaos", "_70_outros_orgaos", map);

        }
    }

    private void addTabelaCBO(Map<String, Object> map) {
        TabelaCbo cbo = (TabelaCbo) map.get("investigacaoAgravoDengueZikaChikungunya_tabelaCbo");
        if (cbo != null) {
            map.put("investigacaoAgravoDengueZikaChikungunya_tabelaCbo", cbo.getDescricaoFormatado());
        }
    }
}
