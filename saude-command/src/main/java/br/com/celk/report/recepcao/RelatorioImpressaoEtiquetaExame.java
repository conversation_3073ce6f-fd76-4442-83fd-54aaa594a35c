package br.com.celk.report.recepcao;

import br.com.celk.report.recepcao.interfaces.dto.RelatorioImpressaoEtiquetaExameDTOParam;
import br.com.celk.report.recepcao.query.QueryRelatorioImpressaoEtiquetaExame;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoEtiquetaExame extends AbstractReport<RelatorioImpressaoEtiquetaExameDTOParam> {

    private RelatorioImpressaoEtiquetaExameDTOParam param;

    public RelatorioImpressaoEtiquetaExame(RelatorioImpressaoEtiquetaExameDTOParam param) {
        super(param);
        this.param = param;
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/recepcao/jrxml/relatorio_impressao_etiqueta_exame.jrxml";
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioImpressaoEtiquetaExame();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_etiqueta");
    }
}
