package br.com.celk.report.unidadesaude;

import br.com.celk.report.unidadesaude.query.QueryRelatorioEstratificacaoRiscoFamiliar;
import br.com.celk.unidadesaude.esus.relatorios.RelatorioEstratificacaoRiscoFamiliarDTOParam;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioEstratificacaoRiscoFamiliar extends AbstractReport<RelatorioEstratificacaoRiscoFamiliarDTOParam> {

    public RelatorioEstratificacaoRiscoFamiliar(RelatorioEstratificacaoRiscoFamiliarDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        this.addParametro("AREA", this.getParam().getArea());
        this.addParametro("MICRO_AREA", this.getParam().getMicroArea());
        this.addParametro("FORMA_APRESENTACAO", this.getParam().getFormaApresentacao());
        this.addParametro("RESULTADO_ESTRATIFICACAO", this.getParam().getResultadoEstratificacao() != null ? this.getParam().getResultadoEstratificacao().toString() : this.getParam().getResultadoEstratificacao());
        if (RelatorioEstratificacaoRiscoFamiliarDTOParam.TipoRelatorio.RESUMIDO.equals(this.getParam().getTipoRelatorio())) {
            return "/br/com/celk/report/unidadesaude/jrxml/relatorio_estratificacao_familiar_resumido.jrxml";
        } else {
            return "/br/com/celk/report/unidadesaude/jrxml/relatorio_estratificacao_familiar_detalhado.jrxml";
        }
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioEstratificacaoRiscoFamiliar();
    }

    @Override
    public String getTitulo() {
        if (RelatorioEstratificacaoRiscoFamiliarDTOParam.TipoRelatorio.RESUMIDO.name().equals(this.getParam().getTipoRelatorio().name())) {
            return Bundle.getStringApplication("rotulo_relatorio_estrat_risco_familiar_resumido");
        } else {
            return Bundle.getStringApplication("rotulo_relatorio_estrat_risco_familiar_paciente");
        }
    }

}
