package br.com.celk.report.vigilancia.query;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoAcidenteTrabalhoTranstornoMental;
import org.hibernate.Session;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Lucas
 */

public class QueryFichaAcidenteTrabalhoTranstornoMental extends QueryFichaInvestigacaoBase {

    @Override
    protected Class getClasseFichaInvestigacao() { return InvestigacaoAgravoAcidenteTrabalhoTranstornoMental.class; }

    @Override
    protected Map<String, String> getCamposInvestigacaoAgravo() {
        Map<String, String> campos = new HashMap<>();

        campos.put("ocupacaoCbo.descricao", "_31_ocupacao");
        campos.put("investigacaoAgravo.situacaoMercadoTrabalho", "_32_situacao_mercado_trab");
        campos.put("investigacaoAgravo.tempoMercadoTrabalho", "_33_tempo_trabalho");
        campos.put("investigacaoAgravo.tempoMercadoTrabalhoUm", "_33_tempo_trabalho_um");

        campos.put("empresaContratante.cnpj", "_34_cnpj");
        campos.put("empresaContratante.descricao", "_35_empresa");
        campos.put("atividade.descricao", "_36_cnae");

        campos.put("cidade.estado.sigla", "_37_uf");
        campos.put("cidade.descricao", "_38_municipio");
        campos.put("cidade.codigo", "_38_ibge");

        campos.put("enderecoEstruturadoDistrito.descricao", "_39_distrito");

        campos.put("empresaContratante.bairro", "_40_bairro");
        campos.put("empresaContratante.rua", "_41_endereco");
        campos.put("empresaContratante.numero", "_42_numero");
        campos.put("empresaContratante.referencia", "_43_ponto_ref");
        campos.put("empresaContratante.telefone", "_44_telefone");

        campos.put("investigacaoAgravo.empregadorEmpresaTerceirizada", "_45_empresa_terceirizada");

        campos.put("investigacaoAgravo.tempoExposicaoAgenteRisco", "_46_tempo_exposicao_agente_risco");
        campos.put("investigacaoAgravo.tempoExposicaoAgenteRiscoUnidadeMedida", "_46_tempo_exposicao_agente_risco_um");
        campos.put("investigacaoAgravo.regimeTratamento", "_47_regime_tratamento");

        campos.put("cidDiagnosticoEspecifico.codigo", "_48_cid");
        campos.put("investigacaoAgravo.habitosAlcool", "_49_habitos_alcool");
        campos.put("investigacaoAgravo.habitosDrogasPsicoativas", "_49_habitos_drogas_psicoativas");
        campos.put("investigacaoAgravo.habitosPsicofarmacos", "_49_habitos_psicofarmacos");
        campos.put("investigacaoAgravo.habitosFumar", "_50_habitos_fumar");
        campos.put("investigacaoAgravo.tempoExposicaoTabaco", "_51_tempo_exposicao_tabaco");
        campos.put("investigacaoAgravo.tempoExposicaoTabacoUnidadeMedida", "_51_tempo_exposicao_tabaco_um");

        campos.put("investigacaoAgravo.condutaGeralAfastamentoSituacaoDesgasteMental", "_52_conduta_geral_afastamente_situacao_desgaste_mental");
        campos.put("investigacaoAgravo.condutaGeralProtecaoIndividual", "_52_conduta_geral_potecao_individual");
        campos.put("investigacaoAgravo.condutaGeralMudancaOrganizacaoTrabalho", "_52_conduta_geral_mudanca_organizacao_trabalho");
        campos.put("investigacaoAgravo.condutaGeralNenhum", "_52_conduta_geral_nenhum");
        campos.put("investigacaoAgravo.condutaGeralProtecaoColetiva", "_52_conduta_geral_protecao_coletiva");
        campos.put("investigacaoAgravo.condutaGeralAfastamentoLocalTrabalho", "_52_conduta_geral_afastamento_local_trabalho");
        campos.put("investigacaoAgravo.condutaGeralOutros", "_52_conduta_geral_outros");
        campos.put("investigacaoAgravo.outrosTrabalhadoresMesmaDoencaLocalTrabalho", "_53_outros_mesma_doenca_trabalho");
        campos.put("investigacaoAgravo.pacienteEncaminhadoCapes", "_54_capes");
        campos.put("investigacaoAgravo.evolucaoCaso", "_55_evolucao_caso");
        campos.put(formatarData("investigacaoAgravo.dataObito"), "_56_obito");
        campos.put("investigacaoAgravo.emitidaCAT", "_57_cat");
        campos.put("investigacaoAgravo.observacao", "_observacao");
        return campos;
    }

    @Override
    protected String getJuncoesFicha() {
        return "left join investigacaoAgravo.ocupacaoCbo ocupacaoCbo "
                + "left join investigacaoAgravo.empresaContratante empresaContratante "
                + "left join investigacaoAgravo.cidDiagnosticoEspecifico cidDiagnosticoEspecifico "
                + "left join empresaContratante.atividade atividade "
                + "left join empresaContratante.cidade cidade "
                + "left join cidade.estado estado "
                + "left join empresaContratante.enderecoEstruturadoDistrito enderecoEstruturadoDistrito ";
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        super.customProcess(session);
        for (Map<String, Object> map : getResult()) {
            addTabelaCBO(map);
        }
    }

    private void addTabelaCBO(Map<String, Object> map) {
        TabelaCbo cbo = (TabelaCbo) map.get("ocupacaoCbo.descricao");
        if (cbo != null) {
            map.put("ocupacaoCbo.descricao", cbo.getDescricaoFormatado());
        }
    }

}
