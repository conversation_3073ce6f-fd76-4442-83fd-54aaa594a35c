package br.com.celk.report.vigilancia.query;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaCreutzfeldtJacob;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaCreutzfeldtJacobExames;
import org.hibernate.Session;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class QueryFichaDoencaCreutzfeldtJacob extends QueryFichaInvestigacaoBase {

    @Override
    protected Class getClasseFichaInvestigacao() { return InvestigacaoAgravoDoencaCreutzfeldtJacob.class; }

    @Override
    protected Map<String, String> getCamposInvestigacaoAgravo() {
        Map<String, String> campos = new HashMap<>();

        campos.put("investigacaoAgravo.codigo", "investigacaoAgravo_codigo");

        campos.put("investigacaoAgravo.criteriosSuspeitaClinica", "_31_criterio_suspeita_clinica");
        campos.put("ocupacaoCbo.descricao", "_33_ocupacao");

        campos.put("investigacaoAgravo.sinalSintomaDemenciaProgressiva", "_34_demencia");
        campos.put("investigacaoAgravo.sinalSintomaMioclonias", "_34_mioclonias");
        campos.put("investigacaoAgravo.sinalSintomaDisturbiosVisuais", "_34_disturbios_visuais");
        campos.put("investigacaoAgravo.sinalSintomaDisturbiosCerebelares", "_34_disturbios_cerebelares");
        campos.put("investigacaoAgravo.sinalSintomaDisestesiasDolorosasPersistentes", "_34_disestesias_dolorosas");
        campos.put("investigacaoAgravo.sinalSintomaAtaxia", "_34_ataxia");
        campos.put("investigacaoAgravo.sinalSintomaSinaisPiramidais", "_34_sinais_piramidais");
        campos.put("investigacaoAgravo.sinalSintomaSinaisExtrapiramidais", "_34_sinais_extrapiramidais");
        campos.put("investigacaoAgravo.sinalSintomaMutismoAcinetico", "_34_mutismo_acinetico");
        campos.put("investigacaoAgravo.sinalSintomaTranstornosPsiquiatricos", "_34_transtornos_psiquiatricos");
        campos.put("investigacaoAgravo.sinalSintomaAlteracoesSono", "_34_alteracoes_sono");

        campos.put("investigacaoAgravo.viagemExterior", "_35_viagem_exterior");
        campos.put(formatarData("investigacaoAgravo.dataViagemExterior"), "_36_dt_ultima_viagem");
        campos.put("investigacaoAgravo.viagemExteriorPais", "_37_pais_viagem_exterior");
        campos.put("investigacaoAgravo.familiarApresentouQuadroSemelhante", "_38_familiar_caso_semelhante");
        campos.put("investigacaoAgravo.pacienteComeCarneBovina", "_39_paciente_carne_1984");
        campos.put("investigacaoAgravo.pacienteVegetariano", "_40_paciente_vegetariano");

        campos.put("investigacaoAgravo.exposicaoIantrogenicaDuraMater", "_41_dura_mater");
        campos.put("investigacaoAgravo.exposicaoIantrogenicaHormonioCrescimento", "_41_hormonio_crescimento");
        campos.put("investigacaoAgravo.exposicaoIantrogenicaTransplanteCorneas", "_41_transplante_corneas");
        campos.put("investigacaoAgravo.exposicaoIantrogenicaNeurocirurgias", "_41_neurocirurgias");
        campos.put("investigacaoAgravo.exposicaoIantrogenicaTransfusaoSangue", "_41_transfusao_sangue");

        campos.put("investigacaoAgravo.resultadoEeg", "_42_resultado_Eeg");
        campos.put("investigacaoAgravo.resultadoRessonanciaMagnetica", "_43_resultado_ressonancia");
        campos.put("investigacaoAgravo.resultadoProteinaLcr", "_44_resultado_proteina_lcr");
        campos.put("investigacaoAgravo.resultadoProteinaTauLcr", "_45_resultado_proteina_tau");
        campos.put("investigacaoAgravo.resultadoBiopsiaCerebral", "_46_resultado_biopsia_cerebral");
        campos.put("investigacaoAgravo.resultadoNecropsiaCerebral", "_47_resultado_necropsia");
        campos.put("investigacaoAgravo.resultadoImunohistoquimicaProteinaPrionica", "_48_resultado_imunohistoquimica");
        campos.put("investigacaoAgravo.resultadoAnaliseGenetica", "_49_resultado_genetica");

        campos.put("investigacaoAgravo.conclusaoDiagnosticoFinalCid.descricao", "_50_diagnostico_final");
        campos.put("investigacaoAgravo.conclusaoFormaClinica", "_51_forma_clinica");
        campos.put("investigacaoAgravo.conclusaoEvolucaoCaso", "_52_evolucao_caso");
        campos.put(formatarData("investigacaoAgravo.conclusaoDataObito"), "_53_dt_obito");
        campos.put(formatarData("investigacaoAgravo.dataEncerramento"), "_54_dt_encerramento");

        campos.put("investigacaoAgravo.observacao", "_observacao");

        return campos;
    }

    @Override
    protected String getJuncoesFicha() {
        return " left join investigacaoAgravo.ocupacaoCbo ocupacaoCbo "
                + " left join investigacaoAgravo.conclusaoDiagnosticoFinalCid cid ";
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        super.customProcess(session);
        for (Map<String, Object> map : getResult()) {
            addToSelectTabelaCBO(map, "investigacaoAgravoDoencaCreutzfeldtJacob", "ocupacaoCbo");
            addExames(map);
        }
    }

    private void addExames(Map<String, Object> map) {
        InvestigacaoAgravoDoencaCreutzfeldtJacobExames proxy = on(InvestigacaoAgravoDoencaCreutzfeldtJacobExames.class);
        long codigo = (long) map.get("investigacaoAgravo_codigo");

        List<InvestigacaoAgravoDoencaCreutzfeldtJacobExames> examesList =
                LoadManager.getInstance(InvestigacaoAgravoDoencaCreutzfeldtJacobExames.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getInvestigacaoAgravoDoencaCreutzFeldtJacob()), codigo))
                        .startLeitura()
                        .getList();

        int i = 0;
        for (InvestigacaoAgravoDoencaCreutzfeldtJacobExames exame : examesList) {
            String tipoExame = coalesce(exame.getTipoExame(), "");
            String resultadoExame = coalesce(exame.getResultadoExame(), "");
            map.put("_tipo_exame"+i, tipoExame);
            map.put("_resultado"+i, resultadoExame);
            i++;
        }
    }

    private String coalesce(String one, String two) {
        return (one != null) ? one : ((two != null) ? two : "");
    }

}
