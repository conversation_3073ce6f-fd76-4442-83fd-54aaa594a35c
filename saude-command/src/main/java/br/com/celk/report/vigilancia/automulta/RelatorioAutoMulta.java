package br.com.celk.report.vigilancia.automulta;

import br.com.celk.report.vigilancia.automulta.dto.RelatorioAutoMultaDTOParam;
import br.com.celk.report.vigilancia.automulta.query.QueryRelatorioAutoMulta;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaEnum;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

/**
 *
 * <AUTHOR>
 */
public class RelatorioAutoMulta extends AbstractReport<RelatorioAutoMultaDTOParam> {

    public RelatorioAutoMulta(RelatorioAutoMultaDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() throws ValidacaoException {
        this.addParametro("FORMA_APRESENTACAO", this.getParam().getFormaApresentacao());
        this.addParametro("gestaoAtividadeEstabelecimentoPorCnae", ConfiguracaoVigilanciaEnum.TipoGestaoAtividade.CNAE.value().equals(VigilanciaHelper.getConfiguracaoVigilancia().getFlagTipoGestaoAtividade()));
        return new QueryRelatorioAutoMulta();
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/vigilancia/automulta/jrxml/relatorio_auto_multa.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relatorio_auto_multa");
    }

}
