package br.com.celk.report.esus.query.planilha;

import br.com.celk.unidadesaude.esus.cds.interfaces.dto.DetalhesItensIntegracaoEsusDTO;
import br.com.celk.unidadesaude.esus.cds.interfaces.dto.DetalhesItensIntegracaoEsusDTOParam;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.esus.EsusIntegracaoCds;
import org.hibernate.Query;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Created by Leonardo.
 */
public class QueryPlanilhaDetalhesEsusIntegracaoCdsMarcadoresConsumoAlimentar extends CommandQuery<QueryPlanilhaDetalhesEsusIntegracaoCdsMarcadoresConsumoAlimentar> {

    private DetalhesItensIntegracaoEsusDTOParam param;
    private List<DetalhesItensIntegracaoEsusDTO> list;

    public QueryPlanilhaDetalhesEsusIntegracaoCdsMarcadoresConsumoAlimentar(DetalhesItensIntegracaoEsusDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(DetalhesItensIntegracaoEsusDTO.class.getName());

        hql.addToSelect("esusIntegracaoCds.codigo", "esusIntegracaoCds.codigo");
        hql.addToSelect("esusIntegracaoCds.uuid", "esusIntegracaoCds.uuid");
        hql.addToSelect("esusIntegracaoCds.descricaoInconsistenciaEsus", "esusIntegracaoCds.descricaoInconsistenciaEsus");

        hql.addToSelect("atendimento.codigo", "esusIntegracaoCds.atendimento.codigo");

        hql.addToSelect("esusFichaMarcadoresConsumoAlimentar.codigo", "esusIntegracaoCds.esusFichaMarcadoresConsumoAlimentar.codigo");
        hql.addToSelect("esusFichaMarcadoresConsumoAlimentar.dataAtendimento", "esusIntegracaoCds.esusFichaMarcadoresConsumoAlimentar.dataAtendimento");
        hql.addToSelect("esusFichaMarcadoresConsumoAlimentar.nomeUsuarioCadsus", "esusIntegracaoCds.esusFichaMarcadoresConsumoAlimentar.nomeUsuarioCadsus");

        hql.addToSelect("esusFichaMarcadoresConsumoAlimentar.numeroCartao", "numeroCartao");

        hql.addToSelect("empresa.codigo", "esusIntegracaoCds.esusFichaMarcadoresConsumoAlimentar.empresa.codigo");
        hql.addToSelect("empresa.descricao", "esusIntegracaoCds.esusFichaMarcadoresConsumoAlimentar.empresa.descricao");

        hql.addToSelect("profissional.codigo", "esusIntegracaoCds.esusFichaMarcadoresConsumoAlimentar.profissional.codigo");
        hql.addToSelect("profissional.nome", "esusIntegracaoCds.esusFichaMarcadoresConsumoAlimentar.profissional.nome");

        hql.addToFrom("EsusIntegracaoCds esusIntegracaoCds "
                + " left join esusIntegracaoCds.exportacaoEsusProcesso exportacaoEsusProcesso "
                + " left join esusIntegracaoCds.atendimento atendimento "
                + " left join esusIntegracaoCds.esusFichaMarcadoresConsumoAlimentar esusFichaMarcadoresConsumoAlimentar "
                + " left join esusFichaMarcadoresConsumoAlimentar.empresa empresa "
                + " left join esusFichaMarcadoresConsumoAlimentar.profissional profissional ");

        hql.addToWhereWhithAnd("esusIntegracaoCds.tipo = ", EsusIntegracaoCds.Tipo.MARCADORES_CONSUMO_ALIMENTAR.value());
        hql.addToWhereWhithAnd("exportacaoEsusProcesso.codigo = ", param.getCodigoExportacaoEsusProcesso());

        hql.addToWhereWhithAnd(hql.getConsultaLiked("esusFichaMarcadoresConsumoAlimentar.nomeUsuarioCadsus", this.param.getPaciente()));
        hql.addToWhereWhithAnd("esusIntegracaoCds.uuid = ", param.getUuid());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("esusIntegracaoCds.descricaoInconsistenciaEsus", this.param.getInconsistencia()));
        hql.addToWhereWhithAnd("atendimento.codigo = ", param.getCodigoAtendimento());
        hql.addToWhereWhithAnd("esusFichaMarcadoresConsumoAlimentar.dataAtendimento = ", param.getDataAtendimento());
        hql.addToWhereWhithAnd("empresa = ", param.getEmpresa());
        hql.addToWhereWhithAnd("profissional = ", param.getProfissional());

        if(RepositoryComponentDefault.SIM_LONG.equals(param.getComInconsistencia())) {
            hql.addToWhereWhithAnd("esusIntegracaoCds.descricaoInconsistenciaEsus is not null");
        } else if(RepositoryComponentDefault.NAO_LONG.equals(param.getComInconsistencia())){
            hql.addToWhereWhithAnd("esusIntegracaoCds.descricaoInconsistenciaEsus is null");
        }

        if (this.param.getNumeroCartao()!= null
                && !this.param.getNumeroCartao().replaceAll("[^0-9]", "").isEmpty()) {
            hql.addToWhereWhithAnd("esusFichaMarcadoresConsumoAlimentar.numeroCartao = :numeroCartao");
        }

        if (param.getPropSort() != null) {
            hql.addToOrder(param.getPropSort() + " " + (param.isAscending() ? "asc" : "desc"));
        } else {
            hql.addToOrder("esusIntegracaoCds.dataCadastro desc");
        }
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) {
        if (this.param.getNumeroCartao()!= null
                && !this.param.getNumeroCartao().replaceAll("[^0-9]", "").isEmpty()) {
            query.setParameter("numeroCartao", Long.parseLong(this.param.getNumeroCartao().replaceAll("[^0-9]", "")));
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    public Collection getResult() {
        return this.list;
    }
}