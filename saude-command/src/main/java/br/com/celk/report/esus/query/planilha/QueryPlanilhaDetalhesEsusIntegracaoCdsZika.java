package br.com.celk.report.esus.query.planilha;

import br.com.celk.unidadesaude.esus.cds.interfaces.dto.DetalhesItensIntegracaoEsusDTO;
import br.com.celk.unidadesaude.esus.cds.interfaces.dto.DetalhesItensIntegracaoEsusDTOParam;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.esus.EsusIntegracaoCds;
import org.hibernate.Query;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Created by mauricley on 25/04/18.
 */
public class QueryPlanilhaDetalhesEsusIntegracaoCdsZika extends CommandQuery<QueryPlanilhaDetalhesEsusIntegracaoCdsZika> {

    private DetalhesItensIntegracaoEsusDTOParam param;
    private List<DetalhesItensIntegracaoEsusDTO> list;

    public QueryPlanilhaDetalhesEsusIntegracaoCdsZika(DetalhesItensIntegracaoEsusDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(DetalhesItensIntegracaoEsusDTO.class.getName());

        hql.addToSelect("esusIntegracaoCds.codigo", "esusIntegracaoCds.codigo");
        hql.addToSelect("esusIntegracaoCds.uuid", "esusIntegracaoCds.uuid");
        hql.addToSelect("esusIntegracaoCds.descricaoInconsistenciaEsus", "esusIntegracaoCds.descricaoInconsistenciaEsus");

        hql.addToSelect("atendimento.codigo", "esusIntegracaoCds.atendimento.codigo");

        hql.addToSelect("esusFichaZika.codigo", "esusIntegracaoCds.esusFichaZika.codigo");
        hql.addToSelect("esusFichaZika.dataAtendimento", "esusIntegracaoCds.esusFichaZika.dataAtendimento");

        hql.addToSelect("esusFichaZika.cns", "numeroCartao");

        hql.addToSelect("empresa.codigo", "esusIntegracaoCds.esusFichaZika.empresa.codigo");
        hql.addToSelect("empresa.descricao", "esusIntegracaoCds.esusFichaZika.empresa.descricao");

        hql.addToSelect("profissional.codigo", "esusIntegracaoCds.esusFichaZika.profissional.codigo");
        hql.addToSelect("profissional.nome", "esusIntegracaoCds.esusFichaZika.profissional.nome");

        hql.addToSelect("usuarioCadsus.codigo", "esusIntegracaoCds.esusFichaZika.usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "esusIntegracaoCds.esusFichaZika.usuarioCadsus.nome");

        hql.addToFrom("EsusIntegracaoCds esusIntegracaoCds "
                + " left join esusIntegracaoCds.exportacaoEsusProcesso exportacaoEsusProcesso "
                + " left join esusIntegracaoCds.atendimento atendimento "
                + " left join esusIntegracaoCds.esusFichaZika esusFichaZika "
                + " left join esusFichaZika.empresa empresa "
                + " left join esusFichaZika.profissional profissional "
                + " left join esusFichaZika.usuarioCadsus usuarioCadsus ");

        hql.addToWhereWhithAnd("esusIntegracaoCds.tipo = ", EsusIntegracaoCds.Tipo.ZIKA.value());
        hql.addToWhereWhithAnd("exportacaoEsusProcesso.codigo = ", param.getCodigoExportacaoEsusProcesso());

        hql.addToWhereWhithAnd(hql.getConsultaLiked("usuarioCadsus.nome", this.param.getPaciente()));
        hql.addToWhereWhithAnd("esusIntegracaoCds.uuid = ", param.getUuid());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("esusIntegracaoCds.descricaoInconsistenciaEsus", this.param.getInconsistencia()));
        hql.addToWhereWhithAnd("atendimento.codigo = ", param.getCodigoAtendimento());
        hql.addToWhereWhithAnd("esusFichaZika.dataAtendimento = ", param.getDataAtendimento());
        hql.addToWhereWhithAnd("empresa = ", param.getEmpresa());
        hql.addToWhereWhithAnd("profissional = ", param.getProfissional());

        if(RepositoryComponentDefault.SIM_LONG.equals(param.getComInconsistencia())) {
            hql.addToWhereWhithAnd("esusIntegracaoCds.descricaoInconsistenciaEsus is not null");
        } else if(RepositoryComponentDefault.NAO_LONG.equals(param.getComInconsistencia())){
            hql.addToWhereWhithAnd("esusIntegracaoCds.descricaoInconsistenciaEsus is null");
        }

        if (this.param.getNumeroCartao()!= null
                && !this.param.getNumeroCartao().replaceAll("[^0-9]", "").isEmpty()) {
            hql.addToWhereWhithAnd("esusFichaZika.cns = :numeroCartao");
        }

        if (param.getPropSort() != null) {
            if("esusIntegracaoCds.esusFichaZika.usuarioCadsus.nome".equals(param.getPropSort())){
                hql.addToOrder("usuarioCadsus.nome " + (param.isAscending() ? "asc" : "desc"));
            } else {
                hql.addToOrder(param.getPropSort() + " " + (param.isAscending() ? "asc" : "desc"));
            }
        } else {
            hql.addToOrder("esusFichaZika.dataAtendimento desc");
        }
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) {
        if (this.param.getNumeroCartao()!= null
                && !this.param.getNumeroCartao().replaceAll("[^0-9]", "").isEmpty()) {
            query.setParameter("numeroCartao", Long.parseLong(this.param.getNumeroCartao().replaceAll("[^0-9]", "")));
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    public Collection getResult() {
        return this.list;
    }
}
