<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_equipes" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="11f7aedf-8e58-4668-8dea-f121a31c2b60">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="3.897434200000011"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="88"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="br.com.ksisolucoes.vo.basico.TipoEquipe"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.basico.Equipe"/>
	<import value="br.com.ksisolucoes.report.ReportProperties"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="tipoResumo" class="java.lang.Long"/>
	<parameter name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<field name="requerimentoVigilancia" class="br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia"/>
	<field name="mes" class="java.lang.Integer"/>
	<field name="cadastrado" class="java.lang.Integer"/>
	<field name="deferido" class="java.lang.Integer"/>
	<field name="indeferido" class="java.lang.Integer"/>
	<field name="emAnalise" class="java.lang.Integer"/>
	<field name="emInspecao" class="java.lang.Integer"/>
	<field name="quantidade" class="java.lang.Integer"/>
	<field name="mesStr" class="java.lang.String"/>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<field name="descricaoAtividadePrincipal" class="java.lang.String"/>
	<field name="tipoSolicitacao" class="br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="FA" class="br.com.ksisolucoes.bo.vigilancia.interfaces.dto.LancamentoAtividadesVigilanciaDTOParam.FormaApresentacao"/>
	<variable name="TipoResumo" class="br.com.celk.report.vigilancia.acoesrealizadas.dto.RelatorioAcoesRealizadasDTOParam.TipoResumo"/>
	<variable name="totalCadastroMes" class="java.lang.Integer" resetType="Group" resetGroup="DetailHeaderDetalhado" calculation="Sum">
		<variableExpression><![CDATA[$F{cadastrado}]]></variableExpression>
	</variable>
	<variable name="totalAnaliseMes" class="java.lang.Integer" resetType="Group" resetGroup="DetailHeaderDetalhado" calculation="Sum">
		<variableExpression><![CDATA[$F{emAnalise}]]></variableExpression>
	</variable>
	<variable name="totalInspecaoMes" class="java.lang.Integer" resetType="Group" resetGroup="DetailHeaderDetalhado" calculation="Sum">
		<variableExpression><![CDATA[$F{emInspecao}]]></variableExpression>
	</variable>
	<variable name="totalDeferidoMes" class="java.lang.Integer" resetType="Group" resetGroup="DetailHeaderDetalhado" calculation="Sum">
		<variableExpression><![CDATA[$F{deferido}]]></variableExpression>
	</variable>
	<variable name="totalindeferidoMes" class="java.lang.Integer" resetType="Group" resetGroup="DetailHeaderDetalhado" calculation="Sum">
		<variableExpression><![CDATA[$F{indeferido}]]></variableExpression>
	</variable>
	<variable name="totalMes" class="java.lang.Integer" resetType="Group" resetGroup="DetailHeaderDetalhado" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}]]></variableExpression>
	</variable>
	<variable name="totalCadastroGeral" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{cadastrado}]]></variableExpression>
	</variable>
	<variable name="totalAnaliseGeral" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{emAnalise}]]></variableExpression>
	</variable>
	<variable name="totalInspecaoGeral" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{emInspecao}]]></variableExpression>
	</variable>
	<variable name="totalDeferidoGeral" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{deferido}]]></variableExpression>
	</variable>
	<variable name="totalindeferidoGeral" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{indeferido}]]></variableExpression>
	</variable>
	<variable name="totalGeral" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}]]></variableExpression>
	</variable>
	<group name="GERAL" isReprintHeaderOnEachPage="true">
		<groupHeader>
			<band height="30">
				<rectangle>
					<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="555" height="29" isPrintWhenDetailOverflows="true" backcolor="#DFDFDF" uuid="587160d3-2cdf-4f8c-9062-0e1d3fb86b12"/>
					<graphicElement>
						<pen lineWidth="0.0"/>
					</graphicElement>
				</rectangle>
				<textField isBlankWhenNull="true">
					<reportElement x="0" y="1" width="237" height="14" uuid="e8d95513-09fd-4f7c-ba96-43a88601274c"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Mês"]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="29" width="555" height="1" uuid="582c594d-49d7-440b-8c52-a707d056f77f"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField isBlankWhenNull="true">
					<reportElement x="238" y="1" width="247" height="14" uuid="c195ca13-bd9c-48de-a1e9-37aeafe06a2b"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Situação"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="486" y="1" width="69" height="14" uuid="287dee40-a3fd-4795-ac8f-967b1caf2597"/>
					<box rightPadding="0"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Quantidade"]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="239" y="18" width="45" height="12" uuid="6034ab2c-e176-44d6-8ddc-3b25dc8adc62"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Cadastro]]></text>
				</staticText>
				<staticText>
					<reportElement x="289" y="18" width="45" height="12" uuid="3c000a4e-0776-4cb4-b0c9-7058d7550644"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Análise]]></text>
				</staticText>
				<staticText>
					<reportElement x="339" y="18" width="45" height="12" uuid="96e0b4d0-e9d8-4a86-b8ae-cb4977b01c5d"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Inspeção]]></text>
				</staticText>
				<staticText>
					<reportElement x="389" y="18" width="45" height="12" uuid="2bac8ac2-fa27-44c4-9e05-f6be97c84210"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Deferido]]></text>
				</staticText>
				<staticText>
					<reportElement x="439" y="18" width="45" height="12" uuid="09be84af-6c12-4fa1-a76c-5405647eee90"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Indeferido]]></text>
				</staticText>
				<line direction="BottomUp">
					<reportElement x="237" y="0" width="1" height="29" uuid="cd137e00-c839-4299-be00-9b87f626ecc8"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<line direction="BottomUp">
					<reportElement x="485" y="0" width="1" height="29" uuid="25519974-f3d5-4299-bed6-8b078a1f779a"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
			</band>
		</groupHeader>
	</group>
	<group name="DetailHeaderDetalhado" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[!$V{TipoResumo}.GERAL.value().equals($P{tipoResumo}) && $P{tipoResumo} != null
? $F{mes}
: null]]></groupExpression>
		<groupHeader>
			<band height="16">
				<printWhenExpression><![CDATA[!$V{TipoResumo}.GERAL.value().equals($P{tipoResumo})]]></printWhenExpression>
				<textField isBlankWhenNull="true">
					<reportElement x="0" y="1" width="113" height="14" uuid="2d345629-72c8-4b9d-a3fc-1322fa672888">
						<printWhenExpression><![CDATA[!$V{TipoResumo}.GERAL.value().equals($P{tipoResumo}) && $P{tipoResumo} != null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="2"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{mesStr}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="19">
				<printWhenExpression><![CDATA[!$V{TipoResumo}.GERAL.value().equals($P{tipoResumo})]]></printWhenExpression>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement x="487" y="3" width="68" height="14" uuid="d10512bf-7b91-4c06-9fab-0fa19be6c701"/>
					<box rightPadding="2"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalMes}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="168" y="3" width="69" height="14" uuid="49b88267-0a91-4601-8335-1033ad0ae772"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Total " + $F{mesStr} + ": "]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="168" y="1" width="387" height="1" uuid="17368ad7-dd35-41d7-8a5e-fefc31272d5f"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<elementGroup>
					<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
						<reportElement stretchType="RelativeToBandHeight" x="389" y="3" width="45" height="14" uuid="a4895d33-eb26-439a-bd93-f07c14da8ea3"/>
						<box topPadding="0" bottomPadding="0"/>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Arial" size="9"/>
						</textElement>
						<textFieldExpression><![CDATA[$V{totalDeferidoMes}]]></textFieldExpression>
					</textField>
					<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
						<reportElement stretchType="RelativeToBandHeight" x="439" y="3" width="45" height="14" uuid="ee9d782d-0d19-4085-a4b5-e454d6201f20"/>
						<box topPadding="0" bottomPadding="0"/>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Arial" size="9"/>
						</textElement>
						<textFieldExpression><![CDATA[$V{totalindeferidoMes}]]></textFieldExpression>
					</textField>
					<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
						<reportElement stretchType="RelativeToBandHeight" x="289" y="3" width="45" height="14" uuid="f67cd93b-000f-4c18-8318-fe2b5c68f267"/>
						<box topPadding="0" bottomPadding="0"/>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Arial" size="9"/>
						</textElement>
						<textFieldExpression><![CDATA[$V{totalAnaliseMes}]]></textFieldExpression>
					</textField>
					<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
						<reportElement stretchType="RelativeToBandHeight" x="339" y="3" width="45" height="14" uuid="59897aee-7cb6-44b6-b9f0-db49e0d82a3a"/>
						<box topPadding="0" bottomPadding="0"/>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Arial" size="9"/>
						</textElement>
						<textFieldExpression><![CDATA[$V{totalInspecaoMes}]]></textFieldExpression>
					</textField>
					<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
						<reportElement stretchType="RelativeToBandHeight" x="239" y="3" width="45" height="14" uuid="69097c4a-43b5-4e86-876e-31113efa2f95"/>
						<box topPadding="0" bottomPadding="0"/>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Arial" size="9"/>
						</textElement>
						<textFieldExpression><![CDATA[$V{totalCadastroMes}]]></textFieldExpression>
					</textField>
				</elementGroup>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="16" splitType="Stretch">
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="555" height="16" isPrintWhenDetailOverflows="true" backcolor="#DFDFDF" uuid="b0116965-1318-46fb-86be-40ffd372f17c">
					<printWhenExpression><![CDATA[$V{COLUMN_COUNT} % 2 == 0]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="237" height="16" uuid="d6916f3e-61b8-4a80-92c9-1a338b573f7b"/>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoAtividadePrincipal} != null ?
    $F{descricaoAtividadePrincipal} != null ? $F{descricaoAtividadePrincipal}.toUpperCase() : "NÃO DEFINIDO"
:
    $F{profissional} != null ?
        $F{profissional}.getNome() != null ? $F{profissional}.getNome().toUpperCase() : "NÃO DEFINIDO"
    :
        $F{tipoSolicitacao} != null ?
            $F{tipoSolicitacao}.getDescricao() != null ? $F{tipoSolicitacao}.getDescricao().toUpperCase() : "NÃO DEFINIDO"
        :
            null]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="389" y="0" width="45" height="16" uuid="844e7577-1dc0-4719-8b26-dbf45633a926"/>
				<box topPadding="2" bottomPadding="2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{deferido}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="439" y="0" width="45" height="16" uuid="2b7120d7-0ba8-4683-8c4f-30d980357838"/>
				<box topPadding="2" bottomPadding="2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{indeferido}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="113" height="16" uuid="0835a8ad-9569-4b81-895b-f2deea34dea2">
					<printWhenExpression><![CDATA[$V{TipoResumo}.GERAL.value().equals($P{tipoResumo}) || $P{tipoResumo} == null]]></printWhenExpression>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mesStr}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="486" y="0" width="69" height="16" uuid="8f1647c8-d281-4cce-98d2-6a3d31436eb6"/>
				<box topPadding="2" bottomPadding="2" rightPadding="2"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidade}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="239" y="0" width="45" height="16" uuid="69107d50-efe4-4078-9f9a-040d9fee4cdd"/>
				<box topPadding="2" bottomPadding="2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cadastrado}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="289" y="0" width="45" height="16" uuid="255f4a32-3df6-4eaa-9f28-b89a3c6d2bce"/>
				<box topPadding="2" bottomPadding="2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{emAnalise}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="339" y="0" width="45" height="16" uuid="59a4f47c-f01f-4c0e-8d19-01a62e365508"/>
				<box topPadding="2" bottomPadding="2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{emInspecao}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<summary>
		<band height="18" splitType="Stretch">
			<line>
				<reportElement x="168" y="1" width="387" height="1" uuid="05e8191f-ff0a-45f6-b5d1-3dabf11b54ef"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement x="168" y="3" width="69" height="14" uuid="23ea1acd-4053-42b2-9fa9-abae77fd5489"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Total Geral: "]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="487" y="3" width="68" height="14" uuid="6f5f8391-7ebe-4585-8275-188479d17e7f"/>
				<box rightPadding="2"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalGeral}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="239" y="3" width="45" height="14" uuid="d1940b1f-b09f-41ed-a4e3-921ce9b81993"/>
				<box topPadding="0" bottomPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalCadastroGeral}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="389" y="3" width="45" height="14" uuid="e89fbf38-3c3d-41a0-bb6e-8d627208bcc3"/>
				<box topPadding="0" bottomPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalDeferidoGeral}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="339" y="3" width="45" height="14" uuid="11bbcbb6-635f-4e34-8b69-219e8c864b51"/>
				<box topPadding="0" bottomPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalInspecaoGeral}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="289" y="3" width="45" height="14" uuid="733ce6da-6c06-49b0-b11a-c60caa4cef15"/>
				<box topPadding="0" bottomPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalAnaliseGeral}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="439" y="3" width="45" height="14" uuid="d2389d69-5775-427e-8a41-6cc24ffd8902"/>
				<box topPadding="0" bottomPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalindeferidoGeral}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
