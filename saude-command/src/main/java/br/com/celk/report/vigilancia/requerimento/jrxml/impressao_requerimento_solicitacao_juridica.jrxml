<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="impressao_requerimento_solicitacao_juridica" columnDirection="RTL" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="56eab6e4-5301-4f11-a723-bfb165341f5f">
	<property name="ireport.zoom" value="1.5026296018031562"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoRestituicaoTaxa"/>
	<import value="br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="USUARIO" class="java.lang.String"/>
	<parameter name="EMPRESA_LOGADA" class="java.lang.String"/>
	<parameter name="EMPRESA_LOGADA_ENDERECO" class="java.lang.String"/>
	<field name="requerimentoRestituicaoTaxa" class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoRestituicaoTaxa">
		<fieldDescription><![CDATA[requerimentoRestituicaoTaxa]]></fieldDescription>
	</field>
	<field name="vigilanciaEndereco" class="br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco">
		<fieldDescription><![CDATA[vigilanciaEndereco]]></fieldDescription>
	</field>
	<field name="enderecoFormatado" class="java.lang.String"/>
	<field name="descricaoAtividadePrincipal" class="java.lang.String"/>
	<field name="profissionalImpressao" class="br.com.ksisolucoes.vo.cadsus.Profissional">
		<fieldDescription><![CDATA[profissionalImpressao]]></fieldDescription>
	</field>
	<field name="processoAdministrativo" class="br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo">
		<fieldDescription><![CDATA[processoAdministrativo]]></fieldDescription>
	</field>
	<group name="Dados_estabelecimento" isStartNewPage="true" isResetPageNumber="true" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band height="131">
				<printWhenExpression><![CDATA[$F{requerimentoRestituicaoTaxa}.getRequerimentoVigilancia().getVigilanciaPessoa().getCodigo() != null]]></printWhenExpression>
				<rectangle radius="0">
					<reportElement x="0" y="7" width="555" height="120" isPrintWhenDetailOverflows="true" uuid="75521546-4ed3-44ca-af20-400bb3991b73"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="68" width="547" height="11" uuid="e5bbac40-e61c-4549-aeea-89ecc882d256"/>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{enderecoFormatado}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="81" width="417" height="11" uuid="634a99dc-fadd-45e4-a86f-e896dc179b62"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Atividade: ".toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="58" width="417" height="11" uuid="c245898f-1d51-4124-9428-514f872621c6"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_endereco").toUpperCase() + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement mode="Opaque" x="6" y="0" width="45" height="12" isPrintWhenDetailOverflows="true" uuid="b42ba35f-be4d-4f6d-ae9b-1d313a6da50b"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_autuado").toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="91" width="547" height="11" uuid="9996cb9f-5be8-4937-8654-8f21e61be9f8"/>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{requerimentoRestituicaoTaxa}.getRequerimentoVigilancia().getVigilanciaPessoa().getAtividade()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="35" width="417" height="11" uuid="eda5f3fd-c9c6-48c2-8a37-7812374a311b"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Fantasia: ".toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="427" y="22" width="125" height="11" uuid="3756b22e-50cf-4bf7-87f2-8b0a5a72ccfd"/>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{requerimentoRestituicaoTaxa}.getRequerimentoVigilancia().getVigilanciaPessoa().getCpfFormatado()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="114" width="547" height="11" uuid="1e5f2196-ab4a-4f62-8b80-7bfad626ab2b"/>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{requerimentoRestituicaoTaxa}.getRequerimentoVigilancia().getVigilanciaPessoa().getRepresentanteLegal()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="104" width="417" height="11" uuid="3fce755f-bbbd-422b-996e-83d07e571aaf"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Representante Legal: ".toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="22" width="417" height="11" uuid="d1a6aaad-84e1-44eb-a9ae-4a9aeed9c024"/>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{requerimentoRestituicaoTaxa}.getRequerimentoVigilancia().getVigilanciaPessoa().getNome()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="12" width="417" height="11" uuid="58a2f741-3d9e-4c63-9f36-4a8d0160e272"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_pessoa_fissica_juridica") + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="45" width="547" height="11" uuid="6f537fef-c387-4524-9f3f-b4c47795ee73"/>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{requerimentoRestituicaoTaxa}.getRequerimentoVigilancia().getVigilanciaPessoa().getNomeFantasia()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="427" y="12" width="125" height="11" uuid="3edcfd2b-7c6d-4f86-aa1b-0fe876f1adb7"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["CPF ou CNPJ: "]]></textFieldExpression>
				</textField>
			</band>
			<band height="109">
				<printWhenExpression><![CDATA[1L == $F{requerimentoRestituicaoTaxa}.getRequerimentoVigilancia().getTipoPessoa()]]></printWhenExpression>
				<rectangle radius="0">
					<reportElement x="0" y="7" width="555" height="98" isPrintWhenDetailOverflows="true" uuid="432ba63a-1d49-498d-8ff9-7058a5fb87a8"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="12" width="417" height="11" uuid="e8bbfa49-5399-498e-afa7-6607bc2f14d6"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_pessoa_fissica_juridica") + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="22" width="417" height="11" uuid="94d28041-fdb7-4ee9-85ff-2eed3bdb2fe4"/>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{requerimentoRestituicaoTaxa}.getRequerimentoVigilancia().getEstabelecimento().getRazaoSocial()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="427" y="12" width="125" height="11" uuid="63449a2d-2f1b-4f81-9ed1-64153af08c20"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cnpj") + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="427" y="22" width="125" height="11" uuid="db4707b3-6233-469c-9b96-d4f3d6e0665a"/>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{requerimentoRestituicaoTaxa}.getRequerimentoVigilancia().getEstabelecimento().getCnpjCpfFormatado()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="45" width="417" height="11" uuid="776ab0a9-c716-47ad-9bc5-b1906031f192"/>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{requerimentoRestituicaoTaxa}.getRequerimentoVigilancia().getEstabelecimento().getFantasia()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="35" width="417" height="11" uuid="4ca78843-4f43-4bbe-a148-0ec9eb39e280"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nome_fantasia").toUpperCase() + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="427" y="45" width="125" height="11" uuid="d0ea534e-8771-4a86-af41-fcba91f3586f"/>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{requerimentoRestituicaoTaxa}.getRequerimentoVigilancia().getEstabelecimento().getInscricaoEstadual()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="427" y="35" width="125" height="11" uuid="fd1eadb2-745c-4260-a1a0-ff5e20910b27">
						<printWhenExpression><![CDATA[$F{requerimentoRestituicaoTaxa}.getRequerimentoVigilancia().getEstabelecimento().getInscricaoEstadual() != null]]></printWhenExpression>
					</reportElement>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_inscricao_estadual").toUpperCase() + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="58" width="417" height="11" uuid="360cbec0-8775-4111-b8b3-5699cd8d53ee"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_endereco").toUpperCase() + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="68" width="547" height="11" uuid="478978ab-75b0-4ac9-bf83-f0034f2c76c4"/>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{enderecoFormatado}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="5" y="91" width="417" height="11" uuid="84ff4ea6-e1ef-4a25-9328-d61554d0da3d"/>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{descricaoAtividadePrincipal}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="81" width="417" height="11" uuid="63d9b20b-e435-460f-af50-a5e6a81f968a"/>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_atividade").toUpperCase() + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="427" y="81" width="125" height="11" uuid="6692f43c-f645-4d12-9b9c-69b8b7575bf5">
						<printWhenExpression><![CDATA[$F{requerimentoRestituicaoTaxa}.getRequerimentoVigilancia().getEstabelecimento().getAlvara() != null]]></printWhenExpression>
					</reportElement>
					<box topPadding="1"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_alvara").toUpperCase() + ":"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="" mode="Transparent" x="427" y="91" width="125" height="11" uuid="6741761a-4db3-46c3-b609-d6ae294e4432"/>
					<box topPadding="1" leftPadding="3"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{requerimentoRestituicaoTaxa}.getRequerimentoVigilancia().getEstabelecimento().getAlvara()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement mode="Opaque" x="6" y="0" width="45" height="12" isPrintWhenDetailOverflows="true" uuid="667b1380-4d3e-46c9-96f0-6044a8f59233"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_autuado").toUpperCase()]]></textFieldExpression>
				</textField>
			</band>
			<band height="28">
				<rectangle radius="0">
					<reportElement stretchType="RelativeToBandHeight" x="0" y="7" width="555" height="20" isPrintWhenDetailOverflows="true" uuid="b885f3ee-af1c-40e6-8c0a-8a5256af0225"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isBlankWhenNull="true">
					<reportElement mode="Opaque" x="6" y="0" width="58" height="12" isPrintWhenDetailOverflows="true" uuid="a071f2f2-a448-4dff-aa61-c0364c5d96b4"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_solicitacao").toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="5" y="12" width="545" height="15" uuid="745e93ee-2913-4fe8-9ef8-67566a16f61a"/>
					<box bottomPadding="3"/>
					<textElement textAlignment="Justified" verticalAlignment="Top" markup="html">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{requerimentoRestituicaoTaxa}.getSolicitacaoProtocoloFormatado()]]></textFieldExpression>
				</textField>
			</band>
			<band height="30">
				<rectangle radius="0">
					<reportElement stretchType="RelativeToBandHeight" x="0" y="9" width="555" height="20" isPrintWhenDetailOverflows="true" uuid="947b780e-a82c-4267-ac66-41fb98641648"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isBlankWhenNull="true">
					<reportElement mode="Opaque" x="6" y="2" width="45" height="12" isPrintWhenDetailOverflows="true" uuid="8b6befa8-b35f-43d0-b65c-56f4f447c4a1"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_motivo").toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="5" y="14" width="545" height="15" uuid="58a61fe6-a504-4a4e-b40c-f178e310a0ca"/>
					<box bottomPadding="3"/>
					<textElement textAlignment="Justified" verticalAlignment="Top" markup="html">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{requerimentoRestituicaoTaxa}.getMotivoRestituicao()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<pageHeader>
		<band height="12" splitType="Stretch">
			<printWhenExpression><![CDATA[$V{PAGE_NUMBER} > 1]]></printWhenExpression>
			<textField isBlankWhenNull="true">
				<reportElement key="" mode="Transparent" x="455" y="0" width="100" height="12" isRemoveLineWhenBlank="true" uuid="be83c82e-11fc-4bc2-8450-60b37b160645">
					<printWhenExpression><![CDATA[$V{PAGE_NUMBER} > 1]]></printWhenExpression>
				</reportElement>
				<box topPadding="1" leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[(
    Bundle.getStringApplication("rotulo_folha_n") +" : " + $V{PAGE_NUMBER}
).toUpperCase()]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="29">
			<rectangle radius="0">
				<reportElement x="0" y="7" width="555" height="18" uuid="9770723e-e9b9-40dc-9142-7a5f46007c5a"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement mode="Opaque" x="6" y="0" width="96" height="12" uuid="e9b363fe-53fc-4573-96b4-a68b9ea458e3"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dados_processuais").toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="115" y="11" width="101" height="12" uuid="0000f224-1fdd-4cda-9fc2-af9bd9d52211"/>
				<box topPadding="1"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{requerimentoRestituicaoTaxa}.getRequerimentoVigilancia().getProtocoloFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="5" y="11" width="110" height="12" uuid="a48183c4-9ab0-45ae-9244-a6bac94263df"/>
				<box topPadding="1"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_solicitacao_juridica").toUpperCase() + ":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="426" y="11" width="101" height="12" uuid="12ede5da-9c7d-4843-b672-5a202db721bf"/>
				<box topPadding="1"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{processoAdministrativo}.getNumeroProcessoFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="253" y="11" width="162" height="12" uuid="632825b8-5813-4f9f-9e12-88170551298e"/>
				<box topPadding="1"/>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_processo_administrativo").toUpperCase() + ":"]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="71">
			<line>
				<reportElement positionType="Float" x="140" y="39" width="275" height="1" isPrintWhenDetailOverflows="true" uuid="74cb9b60-2af3-4e0e-8b17-7972c89a528f">
					<printWhenExpression><![CDATA[VigilanciaHelper.exibirLinhaAssinatura()]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement mode="Transparent" x="140" y="40" width="275" height="15" isPrintWhenDetailOverflows="true" uuid="88c19d95-1028-4aaf-a40d-fe35a3574a7d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{profissionalImpressao} != null ? $F{profissionalImpressao}.getNome() : ""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="140" y="55" width="275" height="13" uuid="9b1a91cf-6199-41bb-9531-68f7019256ae"/>
				<box topPadding="1" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{profissionalImpressao}.getReferenciaRegistroFormatado()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
