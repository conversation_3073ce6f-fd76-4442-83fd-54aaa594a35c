package br.com.celk.report.unidadesaude.query;

import br.com.ksisolucoes.report.agendamento.exame.dto.RelacaoHipertensosSemAderenciaDTO;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelacaoPacientesSemAderenciaDTOParam;
import br.com.ksisolucoes.util.Bundle;

import java.util.LinkedHashMap;

import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */

public class QueryRelacaoHipertensosSemAderenciaHelper {

    private QueryRelacaoHipertensosSemAderenciaHelper() { }

    public static String getQuery(RelacaoPacientesSemAderenciaDTOParam param) {
        String whereClause = "WHERE 1 = 1 ";
        String query = "WITH cidades as ( \n" +
                "         SELECT cod_cid \n" +
                "         FROM parametro_gem t1  \n" +
                "                  join empresa t2 on t1.valor\\:\\:int = t2.empresa \n" +
                "                  where parametro like '%UnidadeCentralIntegracaoEsus%'  \n" +
                "), \n" +
                "dt AS ( \n" +
                "    SELECT \n" +
                "        date(generate_series( \n" +
                "                        (date '2020-01-01')\\:\\:date, \n" +
                "                (now())\\:\\:date, \n" +
                "                interval '4 months' \n" +
                "    ))  \n" +
                "    AS inicio_quadrimestre \n" +
                "), \n" +
                "quadrimestre AS( \n" +
                "    SELECT \n" +
                "        inicio_quadrimestre \n" +
                "        ,CASE  \n" +
                "            WHEN LEAD(inicio_quadrimestre,1) OVER (ORDER BY inicio_quadrimestre) IS NULL   \n" +
                "                THEN DATE(inicio_quadrimestre + INTERVAL '4 months')  \n" +
                "                ELSE LEAD(inicio_quadrimestre,1) OVER (ORDER BY inicio_quadrimestre)  \n" +
                "        END AS quadrimestre_posterior \n" +
                "    FROM dt \n" +
                "), \n" +
                "hipertensos as ( \n" +
                "         SELECT  \n" +
                "                  cd_usu_cadsus \n" +
                "                  ,cd_endereco \n" +
                "                  ,cd_equipe_esus \n" +
                "                  ,tem_hipertensao \n" +
                "         FROM ( \n" +
                "                  SELECT  \n" +
                "                           t1.cd_usu_cadsus \n" +
                "                           ,t1.cd_endereco  \n" +
                "                           ,t3.cd_equipe as cd_equipe_esus \n" +
                "                           ,t2.tem_hipertensao \n" +
                "                           ,ROW_NUMBER() OVER(PARTITION BY t1.cd_usu_cadsus ORDER BY t3.dt_cadastro desc) AS rowno \n" +
                "                  FROM usuario_cadsus t1 \n" +
                "                           join usuario_cadsus_esus t2 on t1.cd_usu_cadsus = t2.cd_usu_cadsus \n" +
                "                           left join esus_ficha_usuario_cadsus_esus t3 on t2.cd_usu_cadsus_esus = t3.cd_usu_cadsus_esus  \n" +
                "                           join cidades t4 on t1.cd_municipio_residencia = t4.cod_cid \n" +
                "                           where t1.situacao IN (0,1) \n" +
                "                           and t1.st_vivo = 1 \n" +
                "                           and t2.tem_hipertensao = 1 ) t \n" +
                "                  where t.rowno = 1 \n" +
                "), \n" +
                "equipe_de_atendimento as ( \n" +
                "    SELECT t.cd_usu_cadsus, cd_equipe_atendimento FROM( \n" +
                "        SELECT  \n" +
                "            t2.cd_usu_cadsus  \n" +
                "                ,t4.cd_equipe as cd_equipe_atendimento \n" +
                "            ,ROW_NUMBER() OVER(PARTITION BY t2.cd_usu_cadsus ORDER BY count(*) DESC, max(dt_atendimento) DESC) AS rowno \n" +
                "        FROM esus_ficha_atend_individual_item t2  \n" +
                "            JOIN esus_ficha_atend_individual t3 ON t2.cd_esus_ficha_atend_individual = t3.cd_esus_ficha_atend_individual \n" +
                "            JOIN equipe t4 ON t3.cod_ine = t4.cd_equipe_cnes \n" +
                "                AND t4.cd_tp_equipe IN ('70','76','73','74') \n" +
                "                and dt_Atendimento >= current_date - interval '2 year' \n" +
                "        GROUP BY 1,2) t \n" +
                "                  WHERE t.rowno = 1 \n" +
                "), \n" +
                "atendimento_hipertensao as ( \n" +
                "         SELECT distinct t2.cd_usu_cadsus FROM esus_ficha_atend_individual_item t2  \n" +
                "                  join esus_ficha_atend_individual t3 on t2.cd_esus_ficha_atend_individual = t3.cd_esus_ficha_atend_individual \n" +
                "                  left join ciap t4 on t2.cd_ciap = t4.cd_ciap \n" +
                "                  join empresa t5 on t3.empresa = t5.empresa \n" +
                "                  where t3.dt_Atendimento\\:\\:date between ((SELECT MAX(quadrimestre_posterior) FROM quadrimestre) - INTERVAL '6 months')\\:\\:date AND current_date \n" +
                "                  and cod_ine is not null \n" +
                "                  and cd_cbo_prin similar to '(225|2231|2235)%' \n" +
                "                  and  ((substring(problema_condicao_avaliada\\:\\:int8\\:\\:bit(8)\\:\\:char(8),1, 1) = '1')  \n" +
                "                           or t2.cd_cid between 'I10' and 'I159'  \n" +
                "                           or t2.cd_cid in ('I270','I272','O10','O100','O101','O102','O103','O104','O109') \n" +
                "                           or t4.referencia in ('K86','K87', 'W81')) \n" +
                "                  and (cod_ine is not null or cod_atv in (1, 2, 15,22, 32, 71, 74)) \n" +
                "         ), \n" +
                "procedimento_afericao as (SELECT t1.cd_usu_cadsus, sum(pa_6m) as pa_6m FROM ( \n" +
                "                  select  \n" +
                "                           t2.cd_usu_cadsus, \n" +
                "                           CASE WHEN t1.dt_atendimento\\:\\:date BETWEEN (q.quadrimestre_posterior - INTERVAL '6 months') AND (q.quadrimestre_posterior) THEN 1 ELSE 0 END AS pa_6m \n" +
                "                  FROM \n" +
                "                           esus_ficha_procedimento t1 \n" +
                "                           left join esus_ficha_procedimento_item t2 on t1.cd_esus_ficha_proc = t2.cd_esus_ficha_proc \n" +
                "                           left join esus_ficha_procedimento_item_sigtap t3 on t2.cd_esus_ficha_proc_it = t3.cd_esus_ficha_proc_it \n" +
                "                           LEFT JOIN procedimento t4 ON t3.cd_procedimento = t4.cd_procedimento \n" +
                "                           INNER JOIN quadrimestre q ON t1.dt_atendimento\\:\\:date BETWEEN ((SELECT MAX(quadrimestre_posterior) FROM quadrimestre) - INTERVAL '6 months')\\:\\:date  AND (SELECT MAX(quadrimestre_posterior) FROM quadrimestre) \n" +
                "                           join empresa t5 on t1.empresa = t5.empresa \n" +
                "                  WHERE \n" +
                "                           (t1.cd_cbo like '225%' \n" +
                "                                    or t1.cd_cbo like '2231%' \n" +
                "                                    or t1.cd_cbo like '2235%' \n" +
                "                                    or t1.cd_cbo like '3222%') \n" +
                "                           AND t4.referencia = '0301100039' \n" +
                "                  AND q.inicio_quadrimestre = (SELECT MAX(inicio_quadrimestre) FROM quadrimestre) \n" +
                "                  and (cod_ine is not null or cod_atv in (1, 2, 15,22, 32, 71, 74)) \n" +
                "                  group by 1,2 ) t1 \n" +
                "                  where exists (select cd_usu_Cadsus from hipertensos) \n" +
                "                  group by 1 \n" +
                "         ), \n" +
                "hipertensos_Atendidos as ( \n" +
                "         SELECT  \n" +
                "                  t1.cd_usu_Cadsus \n" +
                "                  ,t1.cd_endereco \n" +
                "                  ,cd_equipe_esus \n" +
                "                  ,cd_equipe_atendimento \n" +
                "                  ,case when t3.cd_usu_cadsus is not null then 'SIM'  \n" +
                "                           else 'NAO' end as com_atendimento \n" +
                "         FROM hipertensos t1 \n" +
                "                  left join equipe_de_atendimento t2 on t1.cd_usu_cadsus  = t2.cd_usu_cadsus  \n" +
                "                  left join atendimento_hipertensao t3 on t1.cd_usu_cadsus = t3.cd_usu_cadsus \n" +
                "),  \n" +
                "relatorio_indicador as ( \n" +
                "         SELECT  \n" +
                "                  t1.cd_usu_cadsus \n" +
                "                  ,t1.cd_endereco \n" +
                "                  ,t1.cd_equipe_esus \n" +
                "                  ,t1.cd_equipe_atendimento \n" +
                "                  ,coalesce(cd_equipe_atendimento,cd_equipe_esus) as cd_equipe_referencia \n" +
                "                  ,com_atendimento \n" +
                "                  ,case when (SUM(pa_6m)) >= 1 then 'SIM' \n" +
                "                           else 'NAO' end as pa_em_6m \n" +
                "         FROM hipertensos_Atendidos t1 \n" +
                "                  left join procedimento_afericao t4 on t1.cd_usu_cadsus = t4.cd_usu_cadsus \n" +
                "                  group by 1,2,3,4,5,6 \n" +
                ") \n" +

                "  SELECT distinct \n" +
                "          t1.cd_usu_cadsus                         as \"codigoUsuarioCadsus\"                      \n"+
                "         ,t6.nm_usuario                            as \"nomePaciente\"                             \n"+
                "         ,(current_date - t6.dt_nascimento)/365    as \"idade\"                                    \n"+
                "         ,t6.sg_sexo                               as \"sexo\"                                     \n"+
                "         ,case when (com_atendimento = 'SIM' and pa_em_6m = 'SIM') then 'SIM'                      \n"+
                "                  else 'NAO' end                   as \"indicadorSeis\"                            \n"+
                "          ,com_atendimento                         as \"consultaHipertensao\"                      \n"+
                "          ,pa_em_6m                                as \"consultaPressaoArterialUltimosSeisMeses\"  \n"+
                "         ,t4.nm_referencia                         as \"nomeEquipe\"                               \n"+
                "          ,t6.celular                              as \"celular\"                                  \n"+
                "	       ,t5.keyword                              as \"endereco\"                                 \n"+
                "          ,nm_bairro                               as \"bairro\"                                   \n"+
	            "          ,t2.nm_referencia                        as \"nomeEquipeAtendimento\"                    \n"+
                "          ,(SELECT ea.ds_area FROM equipe_area ea WHERE ea.cd_equipe_area = t4.cd_equipe_area LIMIT 1) as \"nomeEquipeResidencia\" \n" +

                "FROM relatorio_indicador t1 \n" +
                "         left join equipe t2 on t1.cd_equipe_esus = t2.cd_equipe \n" +
                "         left join equipe t3 on t1.cd_equipe_atendimento = t3.cd_equipe \n" +
                "         left join equipe t4 on t1.cd_equipe_referencia = t4.cd_equipe \n" +
                "         left join endereco_usuario_cadsus t5 on t1.cd_endereco = t5.cd_endereco \n" +
                "         left join usuario_cadsus t6 on t1.cd_usu_cadsus = t6.cd_usu_cadsus \n" +
                "         left join tipo_equipe t7 on t4.cd_tp_equipe = t7.cd_tp_equipe \n" +
                "         left join equipe_area t8 on t3.cd_equipe_area = t8.cd_equipe_area \n" +
                "         left join endereco_domicilio t10 on t6.cd_domicilio = t10.cd_domicilio \n" +
                "         left join equipe_micro_area t11 on t10.cd_eqp_micro_area = t11.cd_eqp_micro_area \n" +
                "         left join empresa t9 on t9.empresa = t4.empresa \n";

            if (param.getSixIndicator() != null) {
                 whereClause += " AND (CASE WHEN (com_atendimento = 'SIM' AND pa_em_6m = 'SIM') THEN 'S' ELSE 'N' END = :flagSixIndicator) ";
            }

             if (param.getEquipeArea() != null) {
                 whereClause += " AND (t8.ds_area = :descricaoArea) ";
            }

             query += whereClause;

        return query;
    }


    public static LinkedHashMap<String, Object> getColumnMapping() {
        LinkedHashMap<String, Object> columns = new LinkedHashMap<>();
        RelacaoHipertensosSemAderenciaDTO proxy = on(RelacaoHipertensosSemAderenciaDTO.class);

        columns.put((Bundle.getStringApplication("cod_usu_cadsus")), proxy.getCodigoUsuarioCadsus());
        columns.put((Bundle.getStringApplication("rotulo_nome")), proxy.getNomePaciente());
        columns.put((Bundle.getStringApplication("rotulo_idade")), proxy.getIdade());
        columns.put((Bundle.getStringApplication("rotulo_sexo")), proxy.getSexo());
        columns.put((Bundle.getStringApplication("rotulo_equipe")), proxy.getNomeEquipe());
        columns.put((Bundle.getStringApplication("rotulo_indicador_seis")), proxy.getIndicadorSeis());
        columns.put((Bundle.getStringApplication("rotulo_consulta_hipertensao")), proxy.getConsultaHipertensao());
        columns.put((Bundle.getStringApplication("pa_aferida_ate_6_meses")), proxy.getConsultaPressaoArterialUltimosSeisMeses());
        columns.put((Bundle.getStringApplication("rotulo_celular")), proxy.getCelular());
        columns.put((Bundle.getStringApplication("rotulo_telefone")), proxy.getTelefone());
        columns.put((Bundle.getStringApplication("rotulo_endereco")), proxy.getEndereco());
        columns.put((Bundle.getStringApplication("rotulo_bairro")), proxy.getBairro());
        columns.put((Bundle.getStringApplication("rotulo_equipe_atendimento")), proxy.getNomeEquipeAtendimento());
        columns.put((Bundle.getStringApplication("rotulo_equipe_residencia")), proxy.getNomeEquipeResidencia());

        return columns;
    }
}
