<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relacao_gestantes_sem_aderencia" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="8727c5ee-dacc-4501-9bff-1afe095fc3c3">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="br.com.celk.util.DataUtil"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="formaApresentacao" class="java.lang.String"/>
	<parameter name="visualizarOcorrencia" class="java.lang.String"/>
	<field name="dtNascimento" class="java.util.Date">
		<fieldDescription><![CDATA[dtNascimento]]></fieldDescription>
	</field>
	<field name="telefone" class="java.lang.String">
		<fieldDescription><![CDATA[telefone]]></fieldDescription>
	</field>
	<field name="dpp" class="java.util.Date">
		<fieldDescription><![CDATA[dpp]]></fieldDescription>
	</field>
	<field name="consulta1AntesDaSemana12" class="java.lang.String">
		<fieldDescription><![CDATA[consulta1AntesDaSemana12]]></fieldDescription>
	</field>
	<field name="seisOuMaisConsultas" class="java.lang.String">
		<fieldDescription><![CDATA[seisOuMaisConsultas]]></fieldDescription>
	</field>
	<field name="indicador3ConsultaOdontologica" class="java.lang.String">
		<fieldDescription><![CDATA[indicador3ConsultaOdontologica]]></fieldDescription>
	</field>
	<field name="indicador2ExameHIV" class="java.lang.String">
		<fieldDescription><![CDATA[indicador2ExameHIV]]></fieldDescription>
	</field>
	<field name="dsEquipe" class="java.lang.String">
		<fieldDescription><![CDATA[dsEquipe]]></fieldDescription>
	</field>
	<field name="cdUsuarioCadsus" class="java.lang.Long">
		<fieldDescription><![CDATA[cdUsuarioCadsus]]></fieldDescription>
	</field>
	<field name="endereco" class="java.lang.String">
		<fieldDescription><![CDATA[endereco]]></fieldDescription>
	</field>
	<field name="idade" class="java.lang.Integer">
		<fieldDescription><![CDATA[idade]]></fieldDescription>
	</field>
	<field name="indicador1PrimeiraConsultaPN" class="java.lang.String">
		<fieldDescription><![CDATA[indicador1PrimeiraConsultaPN]]></fieldDescription>
	</field>
	<field name="indicador1QtdeAtendimentos" class="java.lang.String">
		<fieldDescription><![CDATA[indicador1QtdeAtendimentos]]></fieldDescription>
	</field>
	<field name="indicador2ExameSifilis" class="java.lang.String">
		<fieldDescription><![CDATA[indicador2ExameSifilis]]></fieldDescription>
	</field>
	<field name="nmUsuarioCadsus" class="java.lang.String">
		<fieldDescription><![CDATA[nmUsuarioCadsus]]></fieldDescription>
	</field>
	<field name="tipoEquipe" class="java.lang.String">
		<fieldDescription><![CDATA[tipoEquipe]]></fieldDescription>
	</field>
	<field name="dum" class="java.util.Date">
		<fieldDescription><![CDATA[dum]]></fieldDescription>
	</field>
	<field name="bairro" class="java.lang.String">
		<fieldDescription><![CDATA[bairro]]></fieldDescription>
	</field>
	<field name="equipeAtiva" class="java.lang.String">
		<fieldDescription><![CDATA[equipeAtiva]]></fieldDescription>
	</field>
	<field name="indicador2" class="java.lang.String">
		<fieldDescription><![CDATA[indicador2]]></fieldDescription>
	</field>
	<field name="equipeAtendimento" class="java.lang.String">
		<fieldDescription><![CDATA[equipeAtendimento]]></fieldDescription>
	</field>
	<field name="equipeResidencia" class="java.lang.String">
		<fieldDescription><![CDATA[equipeResidencia]]></fieldDescription>
	</field>
	<field name="microArea" class="java.lang.String">
		<fieldDescription><![CDATA[microArea]]></fieldDescription>
	</field>
	<field name="celular" class="java.lang.String">
		<fieldDescription><![CDATA[celular]]></fieldDescription>
	</field>
	<group name="Geral"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="170" splitType="Stretch">
			<rectangle>
				<reportElement x="0" y="0" width="555" height="112" uuid="011c64be-3be1-47b5-b583-0fa9f6c4376d"/>
			</rectangle>
			<rectangle>
				<reportElement x="2" y="112" width="553" height="44" uuid="e20eec0c-32b4-4536-a43e-1800e07702a4"/>
			</rectangle>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="73" y="1" width="181" height="14" uuid="35a11fa8-95f5-49d1-a488-49e3e62192e6"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["(" + $F{cdUsuarioCadsus} + ") " + $F{nmUsuarioCadsus}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="73" y="53" width="181" height="14" uuid="ba7e8c03-92eb-4f85-88ce-61a5c5a59a41"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dsEquipe}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="25" y="40" width="48" height="14" uuid="b0139e06-c013-4e6a-ae93-69203e1ce4af"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[DataUtil.getFormatarDiaMesAno($F{dum})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="141" y="40" width="48" height="14" uuid="b11acd7e-b139-4cdd-a142-0cfa5e2353ad"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[DataUtil.getFormatarDiaMesAno($F{dpp})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="281" y="1" width="28" height="14" uuid="fd8467a8-4723-4c56-9adf-9fefd9b723dc"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{idade}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="324" y="53" width="181" height="14" uuid="5c34add1-02a8-4698-a141-cf379f4496a1"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tipoEquipe}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="480" y="40" width="48" height="14" uuid="9e40803a-cf25-48be-8034-3097cdf1b02b"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{consulta1AntesDaSemana12}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="356" y="1" width="48" height="14" uuid="e54fa945-c33d-45a3-a46d-901a891672ae"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[DataUtil.getFormatarDiaMesAno($F{dtNascimento})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="324" y="40" width="44" height="14" uuid="d4dc3c54-ad0e-4e66-85cc-330759067a47"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{seisOuMaisConsultas}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="73" y="14" width="482" height="14" uuid="380c2d1d-856b-4925-8dc2-950ec6451f85"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{endereco}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="159" y="112" width="194" height="14" uuid="d29251fc-2639-432d-9431-cf3403406cbc"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{indicador1PrimeiraConsultaPN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="475" y="112" width="48" height="14" uuid="cd87fa30-5ed1-417a-a865-86994f495be0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{indicador1QtdeAtendimentos}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="73" y="79" width="479" height="14" uuid="613fe44f-0ef5-44dd-9067-d695c978c775"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{equipeAtendimento}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="159" y="127" width="39" height="14" uuid="d4950f5c-a423-4b76-b503-fe3c36fd5c81"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{indicador2ExameHIV}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="73" y="27" width="482" height="14" uuid="3d0bda88-ba48-48dd-887d-0846682f4918"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{bairro}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="464" y="1" width="88" height="14" uuid="7215d479-3d56-42a8-bd8c-37867de3d9c1"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{celular} != null ? $F{celular} : $F{telefone}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="2" y="53" width="72" height="14" uuid="d59dcbb2-2d61-4438-9939-f14d15b33e04"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_equipe_referencia")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="2" y="40" width="24" height="14" uuid="5fc1d099-bf2b-4ca5-ac65-10f810cb2105"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("dum")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="118" y="40" width="24" height="14" uuid="641c775c-76cb-4077-b133-89d56a2c45d9"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("dpp")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="254" y="1" width="28" height="14" uuid="8b351e10-a3b1-4b05-a37c-c5ecddefca9e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_idade")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="253" y="53" width="72" height="14" uuid="b7e444b4-b38c-47fd-8365-99132decc7f3"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_tipo_equipe")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="2" y="1" width="72" height="14" uuid="21e8cd0f-9d96-425b-8ab1-48a6d0c24341"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_usuario_cidadao")+ ":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="368" y="40" width="112" height="14" uuid="06b74a12-1ab6-4b11-bc1d-a6d939eda334"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_consulta_1_antes_da_semana_12")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="312" y="1" width="44" height="14" uuid="cac41344-4295-4cdb-a9d0-08f7ffa09cba"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_nascimento_abv2")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="253" y="40" width="72" height="14" uuid="33a7ede4-b9d7-47db-b7b2-54adef953675"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_seis_ou_mais_consultas")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="2" y="14" width="72" height="14" uuid="cf2dcf4f-ae05-4295-8fa8-9e8571e64c24"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_endereco")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="353" y="112" width="123" height="14" uuid="f4f7aee7-94ed-451c-97f5-238d51ae0516"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("indicador1_qtde_atendimentos")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="2" y="79" width="72" height="14" uuid="51dcd8c7-cf19-4d5f-b7b6-1e40a21b20ab"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_equipe_atendimento")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="99" y="127" width="60" height="14" uuid="7d6c6811-ede7-447b-a222-f09410c90c62"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("indicador2_exame_HIV")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="2" y="27" width="72" height="14" uuid="09f0a396-dbfd-45f5-9ad6-cd336170e51a"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_bairro")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="411" y="1" width="55" height="14" uuid="74379fd4-1666-4e9b-a2bd-d9e6a5cc068d"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_telefone")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="74" y="112" width="85" height="14" uuid="5525c573-224a-494f-a5bd-e6edac2dd17e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("indicador1_primeira_consulta_PN")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="418" y="126" width="42" height="14" uuid="c87939bb-ab74-4851-9331-937790a9f621"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{indicador2ExameSifilis}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="353" y="126" width="65" height="14" uuid="6b1663fc-3858-440c-9f64-ebf3e4cf3f3e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("indicador2_exame_sifilis")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="167" y="142" width="42" height="14" uuid="383809bc-a20b-453f-b0e3-2888b7c3264c"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{indicador3ConsultaOdontologica}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="74" y="142" width="93" height="14" uuid="23b85e03-dd33-441a-9661-962b3aab7713"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("indicador3_consulta_odontologica")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="74" y="127" width="28" height="14" uuid="e24e7e9e-9703-470c-b797-5f85b1261b06"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{indicador2}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="2" y="92" width="72" height="14" uuid="5853e551-9bce-408b-b9d4-4458feee5f91"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_equipe_residencia")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="73" y="92" width="479" height="14" uuid="96ea3209-6f7f-4673-830f-e59b7588895b"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{equipeResidencia}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="2" y="66" width="72" height="14" uuid="402ee289-7410-4c5b-a3c9-6edcdc135baf"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_microarea")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="73" y="66" width="207" height="14" uuid="08e2c6dc-5688-4a02-9f43-ddf763b984c8"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{microArea}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="0" y="112" width="70" height="44" uuid="eb5a0877-3f64-4dde-a788-dd123184a96e"/>
			</rectangle>
			<line>
				<reportElement x="0" y="126" width="555" height="1" uuid="e080ed47-a4b8-493b-94dd-2e82ab376873"/>
			</line>
			<textField isStretchWithOverflow="true">
				<reportElement x="2" y="142" width="61" height="14" uuid="be9cec04-e18c-40c7-9fbf-f63f5fbd4466"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("indicador3")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="2" y="127" width="61" height="14" uuid="948aace4-81b8-402e-aa14-88e41891eaeb"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("indicador2")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="2" y="112" width="61" height="14" uuid="374134b7-e7da-4c9e-8082-4147d870fb6e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("indicador1")+":"]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="141" width="555" height="1" uuid="5899ec33-2bf8-42c2-9d79-f6d167c0b211"/>
			</line>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
