<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_declaracao_comparecimento" pageWidth="496" pageHeight="283" columnWidth="486" leftMargin="5" rightMargin="5" topMargin="5" bottomMargin="5" uuid="3c0caae4-fcc0-4815-b8b4-00f94b47f149">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.1435888100000096"/>
	<property name="ireport.x" value="0"/>
    <property name="ireport.y" value="103"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<subDataset name="Table Dataset 1" uuid="107797ee-ac26-4ea3-9c13-76dafe095a7c"/>
	<parameter name="caminhoLogo" class="java.lang.String" isForPrompting="false"/>
	<parameter name="urlQRCode" class="java.lang.String"/>
	<parameter name="textoDescricao" class="java.lang.String"/>
	<parameter name="caminhoLogoDeclaratorios" class="java.lang.String"/>
	<parameter name="tipoAprovacao" class="java.lang.String"/>
	<parameter name="textoPecaGrafica" class="java.lang.String"/>
	<field name="protocoloFormatado" class="java.lang.String"/>
	<field name="inscricaoImobiliaria" class="java.lang.String"/>
	<field name="numeroAprovacao" class="java.lang.String"/>
	<field name="dataEmissaoFormatada" class="java.lang.String"/>
	<field name="tipoAprovacao" class="java.lang.String"/>
	<detail>
		<band height="251" splitType="Stretch">
			<textField isBlankWhenNull="false">
				<reportElement key="textField-1" x="82" y="41" width="316" height="23" uuid="40fca500-47cb-465a-aaf8-6ce50ba6a31b"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="16" isBold="true" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA["PROCESSO " + $F{protocoloFormatado}]]></textFieldExpression>
			</textField>
			<image scaleImage="RetainShape">
				<reportElement key="image-1" x="7" y="2" width="75" height="63" uuid="637a478d-e312-48f5-97de-c183c2e2783e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<imageExpression><![CDATA[$P{caminhoLogo}]]></imageExpression>
			</image>
			<elementGroup>
				<line>
					<reportElement x="388" y="222" width="10" height="1" uuid="c47411d0-fba2-41d0-b29c-c7c6c3baeb2e"/>
					<graphicElement>
						<pen lineWidth="0.75"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="463" y="222" width="10" height="1" uuid="46009c0d-2621-4973-a720-956698c247f6"/>
					<graphicElement>
						<pen lineWidth="0.75"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="463" y="141" width="10" height="1" uuid="0a192f07-5237-407f-8a25-89e9f4d49b5f"/>
					<graphicElement>
						<pen lineWidth="0.75"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="387" y="142" width="1" height="10" uuid="3f11729a-a410-441f-8658-16f5a7805bf4"/>
					<graphicElement>
						<pen lineWidth="0.75"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="473" y="141" width="1" height="10" uuid="dc453ea2-6ec9-4d54-a1d9-b3cf66246d25"/>
					<graphicElement>
						<pen lineWidth="0.75"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="387" y="141" width="10" height="1" uuid="2cf99725-7f4c-4ee8-9825-587a86334e75"/>
					<graphicElement>
						<pen lineWidth="0.75"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="387" y="213" width="1" height="10" uuid="279abb84-2dc0-47ca-aedc-7990e9b0713b"/>
					<graphicElement>
						<pen lineWidth="0.75"/>
					</graphicElement>
				</line>
				<image scaleImage="RealSize">
					<reportElement x="390" y="142" width="80" height="80" uuid="37efba94-d407-4d2d-b2d3-0fd1203f67c0"/>
					<box leftPadding="0">
						<pen lineWidth="0.0"/>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<imageExpression><![CDATA[com.google.zxing.client.j2se.MatrixToImageWriter.toBufferedImage(
    new com.google.zxing.qrcode.QRCodeWriter().encode(
            $P{urlQRCode},
            com.google.zxing.BarcodeFormat.QR_CODE, 600, 600))]]></imageExpression>
				</image>
				<line>
					<reportElement x="473" y="213" width="1" height="10" uuid="df25a003-15ca-42e2-b541-250f8ae12906"/>
					<graphicElement>
						<pen lineWidth="0.75"/>
					</graphicElement>
				</line>
			</elementGroup>
			<rectangle>
				<reportElement x="7" y="143" width="370" height="78" forecolor="#808080" uuid="c0928949-56cd-4756-87b6-c262105efb53"/>
			</rectangle>
			<line>
				<reportElement x="7" y="176" width="370" height="1" forecolor="#808080" uuid="85a67b02-4552-4e63-93e6-aaca507c9954"/>
			</line>
			<line>
				<reportElement x="7" y="193" width="370" height="1" forecolor="#808080" uuid="ac4537db-d3c1-4ab3-9849-bd12cdfcfe0b"/>
			</line>
			<line>
				<reportElement x="192" y="159" width="1" height="17" forecolor="#808080" uuid="343e51f4-1724-484c-8373-e8c2ba9e8450"/>
			</line>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-3" x="7" y="161" width="185" height="15" uuid="e6d8201e-9357-441b-a9cb-7e10622611b4"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="10" isBold="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA["Nº Aprovação: "+ $F{numeroAprovacao}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-3" x="193" y="161" width="183" height="15" uuid="0ad30a5b-c644-4c28-9f8c-604ba91b9791"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="10" isBold="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA["Emitido em: " + $F{dataEmissaoFormatada}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-3" x="7" y="225" width="469" height="22" forecolor="#808080" uuid="8b496308-dc36-4bd3-aac6-246e02e29688"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8" isBold="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA["Verifique a autenticidade deste documento através QR CODE (Posicionando a câmera do celular sobre ele)"]]></textFieldExpression>
			</textField>
            <textField isBlankWhenNull="true">
                <reportElement key="textField-3" x="7" y="178" width="369" height="14" isRemoveLineWhenBlank="true"
                               uuid="0f908b6d-28a2-44ca-9196-c19143d98a5b"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="10" isBold="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
                <textFieldExpression><![CDATA[$F{inscricaoImobiliaria}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="7" y="159" width="370" height="1" forecolor="#808080" uuid="b1ee8e68-a439-427f-b6a4-8fea6dc542b9"/>
			</line>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-3" x="8" y="144" width="368" height="15" uuid="7ea5929c-f6a5-477d-8de9-6b79ef2a008e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="10" isBold="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
                <textFieldExpression><![CDATA["FINALIDADE: " + $P{tipoAprovacao}]]></textFieldExpression>
			</textField>
			<image scaleImage="RetainShape">
				<reportElement key="image-1" x="401" y="2" width="75" height="63" uuid="810ae9cc-6a79-4ed0-af32-d9e115d83dc6"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<imageExpression><![CDATA[$P{caminhoLogoDeclaratorios}]]></imageExpression>
			</image>
			<rectangle>
				<reportElement x="8" y="75" width="468" height="58" uuid="e616c8c8-c805-496a-97ba-9ad69b35dfe9"/>
			</rectangle>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement key="textField-5" positionType="Float" x="13" y="76" width="457" height="55" uuid="288ff604-3fe3-417a-a789-590469c2699d"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified">
					<font fontName="Arial" size="10" isBold="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{textoDescricao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="13" y="194" width="350" height="27" uuid="92670e6e-99ff-45b0-8905-d58af739c65f"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{textoPecaGrafica}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
