package br.com.celk.report.unidadesaude.query;

import br.com.celk.unidadesaude.esus.relatorios.RelatorioVisitaDomiciliarAcsDTO;
import br.com.celk.unidadesaude.esus.relatorios.RelatorioVisitaDomiciliarAcsDTOParam;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.VisitaDomiciliar;
import br.com.ksisolucoes.vo.esus.EsusFicha;
import org.hibernate.Session;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryRelatorioVisitaDomiciliarACS extends CommandQuery<QueryRelatorioVisitaDomiciliarACS> implements ITransferDataReport<RelatorioVisitaDomiciliarAcsDTOParam, RelatorioVisitaDomiciliarAcsDTO> {

    private RelatorioVisitaDomiciliarAcsDTOParam param;
    private List<RelatorioVisitaDomiciliarAcsDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        if (!RelatorioVisitaDomiciliarAcsDTOParam.FormaApresentacao.GERAL.equals(param.getFormaApresentacao())) {
            if (RelatorioVisitaDomiciliarAcsDTOParam.FormaApresentacao.AREA.equals(param.getFormaApresentacao())) {
                hql.addToSelectAndGroup("equipeArea.codigo", "equipeMicroArea.equipeArea.codigo");
                hql.addToSelectAndGroupAndOrder("equipeArea.descricao", "equipeMicroArea.equipeArea.descricao");
            } else if (RelatorioVisitaDomiciliarAcsDTOParam.FormaApresentacao.MICROAREA.equals(param.getFormaApresentacao())) {
                hql.addToSelectAndGroup("equipeArea.codigo", "equipeMicroArea.equipeArea.codigo");
                hql.addToSelectAndGroupAndOrder("equipeArea.descricao", "equipeMicroArea.equipeArea.descricao");
                hql.addToSelectAndGroup("equipeMicroArea.codigo", "equipeMicroArea.codigo");
                hql.addToSelectAndGroupAndOrder("equipeMicroArea.microArea", "equipeMicroArea.microArea");
                hql.addToSelectAndGroup("equipeProfissional.codigo", "equipeMicroArea.equipeProfissional.codigo");
                hql.addToSelectAndGroup("profissionalMicroArea.codigo", "equipeMicroArea.equipeProfissional.profissional.codigo");
                hql.addToSelectAndGroupAndOrder("profissionalMicroArea.nome", "equipeMicroArea.equipeProfissional.profissional.nome");
            } else if (RelatorioVisitaDomiciliarAcsDTOParam.FormaApresentacao.PROFISSIONAL.equals(param.getFormaApresentacao())) {
                hql.addToSelectAndGroup("profissional.codigo", "profissional.codigo");
                hql.addToSelectAndGroupAndOrder("profissional.nome", "profissional.nome");
            }
        }
        if (RelatorioVisitaDomiciliarAcsDTOParam.TipoRelatorio.DETALHADO.equals(param.getTipoRelatorio())) {
            if (param.getFormaOrdenacao().equals(RelatorioVisitaDomiciliarAcsDTOParam.FormaOrdenacao.ALFABETICA)) {
                hql.addToOrder("usuarioCadsus.nome asc");
            }
            if (param.getFormaOrdenacao().equals(RelatorioVisitaDomiciliarAcsDTOParam.FormaOrdenacao.DATA)) {
                hql.addToOrder("visitaDomiciliar.dataVisita");
            }
            hql.addToSelectAndGroupAndOrder("visitaDomiciliar.uuidTablet", "visitaDomiciliar.uuidTablet");
            hql.addToSelectAndGroupAndOrder("visitaDomiciliar.dataVisita", "visitaDomiciliar.dataVisita");
            hql.addToSelectAndGroupAndOrder("visitaDomiciliar.dataCadastro", "visitaDomiciliar.dataCadastro");
            hql.addToSelectAndGroupAndOrder("visitaDomiciliar.turno", "visitaDomiciliar.turno");
            hql.addToSelectAndGroupAndOrder("visitaDomiciliar.desfecho", "visitaDomiciliar.desfecho");
            hql.addToSelectAndGroupAndOrder("visitaDomiciliar.observacao", "visitaDomiciliar.observacao");
            //hql.addToSelectAndGroup("motivoVisitaDomiciliar.codigo", "motivoVisitaDomiciliar.codigo");
            hql.addToSelectAndGroupAndOrder("motivoVisitaDomiciliar.descricao", "motivoVisitaDomiciliar.descricao");

            hql.addToSelectAndGroup("usuarioCadsus.codigo", "usuarioCadsus.codigo");
            hql.addToSelectAndGroupAndOrder("usuarioCadsus.nome", "usuarioCadsus.nome");
            hql.addToSelectAndGroupAndOrder("usuarioCadsus.telefone", "usuarioCadsus.telefone");
            hql.addToSelectAndGroupAndOrder("usuarioCadsus.celular", "usuarioCadsus.celular");

            if (!RelatorioVisitaDomiciliarAcsDTOParam.FormaApresentacao.MICROAREA.equals(param.getFormaApresentacao())) {
                if (!RelatorioVisitaDomiciliarAcsDTOParam.FormaApresentacao.AREA.equals(param.getFormaApresentacao())) {
                    hql.addToSelectAndGroup("equipeArea.codigo", "equipeMicroArea.equipeArea.codigo");
                    hql.addToSelectAndGroupAndOrder("equipeArea.descricao", "equipeMicroArea.equipeArea.descricao");
                }

                hql.addToSelectAndGroup("equipeMicroArea.codigo", "equipeMicroArea.codigo");
                hql.addToSelectAndGroupAndOrder("equipeMicroArea.microArea", "equipeMicroArea.microArea");
            }

            if (!RelatorioVisitaDomiciliarAcsDTOParam.FormaApresentacao.PROFISSIONAL.equals(param.getFormaApresentacao())) {
                hql.addToSelectAndGroup("profissional.codigo", "profissional.codigo");
                hql.addToSelectAndGroupAndOrder("profissional.nome", "profissional.nome");
            }

            hql.addToSelectAndGroupAndOrder("enderecoUsuarioCadsus.codigo", "visitaDomiciliar.enderecoDomicilio.enderecoUsuarioCadsus.codigo");
            hql.addToSelectAndGroupAndOrder("enderecoUsuarioCadsus.logradouro", "visitaDomiciliar.enderecoDomicilio.enderecoUsuarioCadsus.logradouro");
            hql.addToSelectAndGroupAndOrder("enderecoUsuarioCadsus.bairro", "visitaDomiciliar.enderecoDomicilio.enderecoUsuarioCadsus.bairro");
            hql.addToSelectAndGroupAndOrder("enderecoUsuarioCadsus.cep", "visitaDomiciliar.enderecoDomicilio.enderecoUsuarioCadsus.cep");
            hql.addToSelectAndGroupAndOrder("cidade.codigo", "visitaDomiciliar.enderecoDomicilio.enderecoUsuarioCadsus.cidade.codigo");
            hql.addToSelectAndGroupAndOrder("cidade.descricao", "visitaDomiciliar.enderecoDomicilio.enderecoUsuarioCadsus.cidade.descricao");
            hql.addToSelectAndGroupAndOrder("estado.sigla", "visitaDomiciliar.enderecoDomicilio.enderecoUsuarioCadsus.cidade.estado.sigla");

        } else {
            if (RelatorioVisitaDomiciliarAcsDTOParam.TipoResumo.MOTIVO.equals(param.getTipoResumo())) {
                //hql.addToSelectAndGroup("motivoVisitaDomiciliar.codigo", "motivoVisitaDomiciliar.codigo");
                hql.addToSelectAndGroupAndOrder("motivoVisitaDomiciliar.descricao", "motivoVisitaDomiciliar.descricao");
            } else if (param.isTipoResumoProfissional() && !RelatorioVisitaDomiciliarAcsDTOParam.FormaApresentacao.PROFISSIONAL.equals(param.getFormaApresentacao())) {
                hql.addToSelectAndGroup("profissional.codigo", "profissional.codigo");
                hql.addToSelectAndGroupAndOrder("profissional.nome", "profissional.nome");
            } else if (RelatorioVisitaDomiciliarAcsDTOParam.TipoResumo.AREA.equals(param.getTipoResumo())
                    && !RelatorioVisitaDomiciliarAcsDTOParam.FormaApresentacao.AREA.equals(param.getFormaApresentacao())
                    && !RelatorioVisitaDomiciliarAcsDTOParam.FormaApresentacao.MICROAREA.equals(param.getFormaApresentacao())) {
                hql.addToSelectAndGroup("equipeArea.codigo", "equipeMicroArea.equipeArea.codigo");
                hql.addToSelectAndGroupAndOrder("equipeArea.descricao", "equipeMicroArea.equipeArea.descricao");
            } else if (RelatorioVisitaDomiciliarAcsDTOParam.TipoResumo.DATA.equals(param.getTipoResumo())) {
                hql.addToSelectAndGroupAndOrder("visitaDomiciliar.dataVisita", "visitaDomiciliar.dataVisita");
            }
        }

        if (RelatorioVisitaDomiciliarAcsDTOParam.TipoRelatorio.RESUMIDO.equals(param.getTipoRelatorio()) && param.isTipoResumoProfissional()) {
            hql.addToSelect("(SELECT count(distinct ed.codigo) FROM UsuarioCadsusDomicilio ucd "
                    + " LEFT JOIN ucd.enderecoDomicilio ed"
                    + " LEFT JOIN ed.equipeMicroArea ema"
                    + " LEFT JOIN ema.equipeProfissional ep"
                    + " LEFT JOIN ep.profissional p"
                    + " WHERE ucd.status <> 3 and p.codigo = profissional.codigo)", "quantidadeFamiliaMicroArea");
        }

        hql.addToSelect("count(visitaDomiciliar.codigo)", "quantidade");
        hql.addToSelect("count(distinct visitaDomiciliar.enderecoDomicilio.codigo)", "quantidadeDomicilio");
        hql.addToSelect("COUNT(DISTINCT CASE WHEN visitaDomiciliar.visitaForaMicroarea = 0 THEN visitaDomiciliar.enderecoDomicilio.codigo END)", "quantidadeDentroArea");
        hql.addToSelect("(COUNT(DISTINCT visitaDomiciliar.enderecoDomicilio.codigo) - COUNT(DISTINCT CASE WHEN visitaDomiciliar.visitaForaMicroarea = 0 THEN visitaDomiciliar.enderecoDomicilio.codigo END))", "quantidadeForaArea");
        hql.addToSelect("count_distinct(concat(visitaDomiciliar.enderecoDomicilio.codigo,'/', visitaDomiciliar.dataVisita,'/', visitaDomiciliar.turno ))", "nrVisitas");

        hql.setTypeSelect(RelatorioVisitaDomiciliarAcsDTO.class.getName());

        if (param.getMotivoVisitaDomiciliar() == null
                && RelatorioVisitaDomiciliarAcsDTOParam.TipoRelatorio.RESUMIDO.equals(param.getTipoRelatorio())
                && !RelatorioVisitaDomiciliarAcsDTOParam.TipoResumo.MOTIVO.equals(param.getTipoResumo())) {
            hql.addToFrom("VisitaDomiciliar visitaDomiciliar"
                    + " left join visitaDomiciliar.empresa empresa"
                    + " left join visitaDomiciliar.profissional profissional"
                    + " left join visitaDomiciliar.equipeMicroArea equipeMicroArea"
                    + " left join equipeMicroArea.equipeArea equipeArea"
                    + " left join equipeMicroArea.equipeProfissional equipeProfissional"
                    + " left join equipeProfissional.profissional profissionalMicroArea");
        } else {

            StringBuilder from = new StringBuilder();

            from.append("VisitaDomiciliarMotivo visitaDomiciliarMotivo"
                    + " inner join visitaDomiciliarMotivo.motivoVisitaDomicilar motivoVisitaDomiciliar");

            if (RelatorioVisitaDomiciliarAcsDTOParam.TipoRelatorio.RESUMIDO.equals(param.getTipoRelatorio())) {
                from.append(" inner join visitaDomiciliarMotivo.visita visitaDomiciliar");
            } else {
                from.append(" right join visitaDomiciliarMotivo.visita visitaDomiciliar");
            }

            from.append(" left join visitaDomiciliar.empresa empresa"
                    + " left join visitaDomiciliar.enderecoDomicilio enderecoDomicilio"
                    + " left join enderecoDomicilio.enderecoUsuarioCadsus enderecoUsuarioCadsus"
                    + " left join enderecoUsuarioCadsus.tipoLogradouro tipoLogradouro"
                    + " left join enderecoUsuarioCadsus.cidade cidade"
                    + " left join cidade.estado estado"
                    + " left join visitaDomiciliar.profissional profissional"
                    + " left join visitaDomiciliar.usuarioCadsus usuarioCadsus"
                    + " left join visitaDomiciliar.equipeMicroArea equipeMicroArea"
                    + " left join equipeMicroArea.equipeArea equipeArea"
                    + " left join equipeMicroArea.equipeProfissional equipeProfissional"
                    + " left join equipeProfissional.profissional profissionalMicroArea");

            hql.addToFrom(from.toString());

        }

        hql.addToWhereWhithAnd("empresa in ", param.getEstabelecimento());
        hql.addToWhereWhithAnd("profissional = ", param.getProfissional());
        hql.addToWhereWhithAnd("motivoVisitaDomiciliar = ", param.getMotivoVisitaDomiciliar());
        hql.addToWhereWhithAnd("equipeArea = ", param.getArea());
        hql.addToWhereWhithAnd("equipeMicroArea  = ", param.getMicroArea());
        hql.addToWhereWhithAnd("visitaDomiciliar.desfecho = ", param.getDesfecho());
        hql.addToWhereWhithAnd("visitaDomiciliar.turno = ", param.getTurno());
        hql.addToWhereWhithAnd("visitaDomiciliar.dataVisita ", param.getPeriodo());
        hql.addToWhereWhithAnd("visitaDomiciliar.situacao <> ", VisitaDomiciliar.Situacao.CANCELADO.value());
        hql.addToWhereWhithAnd("visitaDomiciliar.flagVisitaTerritorial <> ", RepositoryComponentDefault.SIM_LONG);

        if (EsusFicha.EsusFichaSituacao.FICHAS_EXPORTADAS.value().equals(this.param.getEsusFichaSituacao())) {
            hql.addToWhereWhithAnd(" exists (select 1 from EsusFicha esusFicha WHERE esusFicha.visitaDomiciliar = visitaDomiciliar) ");
        } else if (EsusFicha.EsusFichaSituacao.FICHAS_PENDENTES.value().equals(this.param.getEsusFichaSituacao())) {
            hql.addToWhereWhithAnd(" not exists (select 1 from EsusFicha esusFicha WHERE esusFicha.visitaDomiciliar = visitaDomiciliar) ");
        }
    }

    @Override
    public void setDTOParam(RelatorioVisitaDomiciliarAcsDTOParam param) {
        this.param = param;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (param.isTipoResumoProfissional() && param.isExibeProfissionalSemProducao()) {
            this.result = new BuscarProfissionaisSemProducao(this.result).buscar(param);
        }
    }

    @Override
    public List<RelatorioVisitaDomiciliarAcsDTO> getResult() {
        return this.result;
    }
}
