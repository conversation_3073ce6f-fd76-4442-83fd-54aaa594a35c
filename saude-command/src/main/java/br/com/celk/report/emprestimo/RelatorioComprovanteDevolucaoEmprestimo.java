package br.com.celk.report.emprestimo;

import br.com.celk.report.emprestimo.query.QueryComprovanteDevolucaoEmprestimo;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimo;

/**
 *
 * <AUTHOR>
 */
public class RelatorioComprovanteDevolucaoEmprestimo extends AbstractReport<DevolucaoEmprestimo> {

    public RelatorioComprovanteDevolucaoEmprestimo(DevolucaoEmprestimo devolucaoEmprestimo) {
        super(devolucaoEmprestimo);
    }

    @Override
    public ITransferDataReport getQuery() {
        addParametro("EXIBIR_NUMERO_PAGINAS", true);
        return new QueryComprovanteDevolucaoEmprestimo();
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/emprestimo/jrxml/relatorio_comprovante_devolucao_emprestimo.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_comprovante_devolucao_emprestimo");
    }

}
