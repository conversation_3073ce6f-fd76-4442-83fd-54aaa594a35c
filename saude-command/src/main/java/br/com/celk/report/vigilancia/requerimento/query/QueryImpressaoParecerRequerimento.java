package br.com.celk.report.vigilancia.requerimento.query;

import br.com.celk.util.CollectionUtils;
import br.com.celk.vigilancia.dto.ImpressaoParecerRequerimentoDTO;
import br.com.celk.vigilancia.dto.ImpressaoParecerRequerimentoDTOParam;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaParecerFiscal;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaParecerResposta;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAnaliseProjetoParecerResposta;
import ch.lambdaj.Lambda;
import com.ibm.icu.lang.UCharacter;
import com.ibm.icu.text.BreakIterator;
import org.hibernate.Query;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;

/**
 * <AUTHOR>
 */
public abstract class QueryImpressaoParecerRequerimento extends CommandQuery implements ITransferDataReport<ImpressaoParecerRequerimentoDTOParam, ImpressaoParecerRequerimentoDTO> {

    private ImpressaoParecerRequerimentoDTOParam param;
    private List<ImpressaoParecerRequerimentoDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {

        if (param.getCodigoParecer() != null) {

            hql.setTypeSelect(ImpressaoParecerRequerimentoDTO.class.getName());

            hql.addToSelect("requerimentoVigilanciaParecer.codigo", "requerimentoVigilanciaParecer.codigo");
            hql.addToSelect("requerimentoVigilanciaParecer.descricao", "requerimentoVigilanciaParecer.descricao");
            hql.addToSelect("requerimentoVigilanciaParecer.numeroParecer", "requerimentoVigilanciaParecer.numeroParecer");
            hql.addToSelect("requerimentoVigilanciaParecer.descricaoParecer", "requerimentoVigilanciaParecer.descricaoParecer");
            hql.addToSelect("requerimentoVigilanciaParecer.descricao", "requerimentoVigilanciaParecer.descricao");
            hql.addToSelect("requerimentoVigilanciaParecer.dataRetorno", "requerimentoVigilanciaParecer.dataRetorno");

            hql.addToSelect("requerimentoVigilancia.codigo", "requerimentoVigilancia.codigo");
            hql.addToSelect("requerimentoVigilancia.nome", "requerimentoVigilancia.nome");

            hql.addToSelect("estabelecimento.codigo", "requerimentoVigilancia.estabelecimento.codigo");
            hql.addToSelect("estabelecimento.fantasia", "requerimentoVigilancia.estabelecimento.fantasia");
            hql.addToSelect("estabelecimento.cnpjCpf", "requerimentoVigilancia.estabelecimento.cnpjCpf");
            hql.addToSelect("estabelecimento.inscricaoEstadual", "requerimentoVigilancia.estabelecimento.inscricaoEstadual");
            hql.addToSelect("estabelecimento.alvara", "requerimentoVigilancia.estabelecimento.alvara");
            hql.addToSelect("estabelecimento.numeroLogradouro", "requerimentoVigilancia.estabelecimento.numeroLogradouro");

            hql.addToSelect("vigilanciaPessoa.codigo", "requerimentoVigilancia.vigilanciaPessoa.codigo");
            hql.addToSelect("vigilanciaPessoa.nome", "requerimentoVigilancia.vigilanciaPessoa.nome");
            hql.addToSelect("vigilanciaPessoa.nomeFantasia", "requerimentoVigilancia.vigilanciaPessoa.nomeFantasia");
            hql.addToSelect("vigilanciaPessoa.cpf", "requerimentoVigilancia.vigilanciaPessoa.cpf");
            hql.addToSelect("vigilanciaPessoa.numeroLogradouro", "requerimentoVigilancia.vigilanciaPessoa.numeroLogradouro");

            hql.addToSelect("atividadeEstabelecimento.codigo", "requerimentoVigilancia.estabelecimento.atividadeEstabelecimento.codigo");
            hql.addToSelect("atividadeEstabelecimento.descricao", "requerimentoVigilancia.estabelecimento.atividadeEstabelecimento.descricao");

            hql.addToSelect("vigilanciaEndereco.codigo", "vigilanciaEndereco.codigo");
            hql.addToSelect("vigilanciaEndereco.cep", "vigilanciaEndereco.cep");
            hql.addToSelect("vigilanciaEndereco.bairro", "vigilanciaEndereco.bairro");
            hql.addToSelect("vigilanciaEndereco.logradouro", "vigilanciaEndereco.logradouro");

            hql.addToSelect("cidade.codigo", "vigilanciaEndereco.cidade.codigo");
            hql.addToSelect("cidade.descricao", "vigilanciaEndereco.cidade.descricao");
            hql.addToSelect("estado.codigo", "vigilanciaEndereco.cidade.estado.codigo");
            hql.addToSelect("estado.sigla", "vigilanciaEndereco.cidade.estado.sigla");

            hql.addToSelect("(select ae.descricao "
                    + "from EstabelecimentoAtividade ea "
                    + "left join ea.atividadeEstabelecimento ae "
                    + "where ea.estabelecimento = estabelecimento AND ea.flagPrincipal = :sim)", "descricaoAtividadePrincipal");

            hql.addToFrom("RequerimentoVigilanciaParecer requerimentoVigilanciaParecer"
                    + " left join requerimentoVigilanciaParecer.requerimentoVigilancia requerimentoVigilancia"
                    + " left join requerimentoVigilancia.vigilanciaPessoa vigilanciaPessoa"
                    + " left join requerimentoVigilancia.estabelecimento estabelecimento"
                    + " left join estabelecimento.atividadeEstabelecimento atividadeEstabelecimento"
                    + " left join requerimentoVigilancia.vigilanciaEndereco vigilanciaEndereco"
                    + " left join vigilanciaEndereco.cidade cidade"
                    + " left join cidade.estado estado"
            );

            hql.addToWhereWhithAnd("requerimentoVigilanciaParecer.codigo = ", param.getCodigoParecer());
        }

    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setLong("sim", RepositoryComponentDefault.SIM_LONG);
    }

    @Override
    public List<ImpressaoParecerRequerimentoDTO> getResult() {
        return this.result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List) result);
    }

    @Override
    public void setDTOParam(ImpressaoParecerRequerimentoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        List<Profissional> fiscais = new ArrayList<>();
        if (this.param.getCodigoParecer() != null) {
            RequerimentoVigilanciaParecerFiscal proxyFiscal = Lambda.on(RequerimentoVigilanciaParecerFiscal.class);

            List<RequerimentoVigilanciaParecerFiscal> list = LoadManager.getInstance(RequerimentoVigilanciaParecerFiscal.class)
                    .addProperty(path(proxyFiscal.getProfissional().getCodigo()))
                    .addProperty(path(proxyFiscal.getProfissional().getReferencia()))
                    .addProperty(path(proxyFiscal.getProfissional().getNome()))
                    .addProperty(path(proxyFiscal.getProfissional().getNumeroRegistro()))
                    .addProperty(path(proxyFiscal.getProfissional().getUnidadeFederacaoConselhoRegistro()))
                    .addProperty(path(proxyFiscal.getProfissional().getConselhoClasse().getCodigo()))
                    .addProperty(path(proxyFiscal.getProfissional().getConselhoClasse().getSigla()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxyFiscal.getRequerimentoVigilanciaParecer().getCodigo()), this.param.getCodigoParecer()))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(list)) {
                List<Profissional> extract = Lambda.extract(list, proxyFiscal.getProfissional());
                for (Profissional fiscal : extract) {
                    fiscal.setNome(UCharacter.toTitleCase(fiscal.getNome().toLowerCase(), BreakIterator.getTitleInstance()));
                    fiscais.add(fiscal);
                }
                addParam("fiscais", fiscais);
            }
        }

        if (this.result != null && this.result.size() > 0) {

            for (ImpressaoParecerRequerimentoDTO impressaoParecerRequerimentoDTO : this.result) {
                impressaoParecerRequerimentoDTO.setFiscais(fiscais);
                impressaoParecerRequerimentoDTO.setUrlQrCode(param.getUrlQRcode());
                if (impressaoParecerRequerimentoDTO.getRequerimentoVigilanciaParecer().getCodigo() != null) {
                    RequerimentoVigilanciaParecerResposta proxy = Lambda.on(RequerimentoVigilanciaParecerResposta.class);
                    RequerimentoVigilanciaParecerResposta respostaParecer = LoadManager.getInstance(RequerimentoVigilanciaParecerResposta.class)
                            .addProperty(path(proxy.getCodigo()))
                            .addProperty(path(proxy.getDescricaoResposta()))
                            .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getRequerimentoVigilanciaParecer().getCodigo()), impressaoParecerRequerimentoDTO.getRequerimentoVigilanciaParecer().getCodigo()))
                            .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getSituacao()), RequerimentoAnaliseProjetoParecerResposta.Situacao.ENVIADO.value()))
                            .setMaxResults(1).start().getVO();
                    if (respostaParecer != null) {
                        impressaoParecerRequerimentoDTO.setResposta(respostaParecer.getDescricaoResposta());
                        RequerimentoVigilanciaAnexo proxyAnexo = Lambda.on(RequerimentoVigilanciaAnexo.class);
                        List<RequerimentoVigilanciaAnexo> requerimentoVigilanciaAnexoList = LoadManager.getInstance(RequerimentoVigilanciaAnexo.class)
                                .addProperty(path(proxyAnexo.getCodigo()))
                                .addProperty(path(proxyAnexo.getDescricao()))
                                .addProperty(path(proxyAnexo.getGerenciadorArquivo().getCodigo()))
                                .addProperty(path(proxyAnexo.getGerenciadorArquivo().getNomeArquivo()))
                                .addParameter(new QueryCustom.QueryCustomParameter(path(proxyAnexo.getRequerimentoVigilanciaParecerResposta().getCodigo()), respostaParecer.getCodigo()))
                                .addParameter(new QueryCustom.QueryCustomParameter(path(proxyAnexo.getStatus()), RequerimentoVigilanciaAnexo.Status.CADASTRADO.value()))
                                .start().getList();
                        if (CollectionUtils.isNotNullEmpty(requerimentoVigilanciaAnexoList)) {
                            StringBuilder anexos = new StringBuilder();
                            for (RequerimentoVigilanciaAnexo requerimentoVigilanciaAnexo : requerimentoVigilanciaAnexoList) {
                                anexos.append(requerimentoVigilanciaAnexo.getDescricao());
                                anexos.append(": ");
                                anexos.append(requerimentoVigilanciaAnexo.getGerenciadorArquivo().getNomeArquivo());
                                anexos.append("<br/>");
                            }
                            impressaoParecerRequerimentoDTO.setAnexos(anexos.toString());
                        }
                    }
                }
            }
        }
    }

    public abstract void addParam(String key, Object object);
}
