<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_pacientes_atendimento_atraso" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="904ef39c-8037-443b-8222-af49929dd456">
	<property name="ireport.zoom" value="2.928200000000036"/>
	<property name="ireport.x" value="11"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="br.com.celk.util.Coalesce"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<import value="org.apache.commons.lang.StringUtils"/>
	<parameter name="FORMA_APRESENTACAO" class="br.com.celk.report.unidadesaude.interfaces.dto.RelacaoPacientesAtendimentoAtrasoDTOParam.FormaApresentacao"/>
	<field name="dataUltimoAtendimento" class="java.util.Date"/>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<field name="diasEmAtraso" class="java.lang.Integer"/>
	<field name="tipoAtendimento" class="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"/>
	<variable name="FA" class="br.com.celk.report.unidadesaude.interfaces.dto.RelacaoPacientesAtendimentoAtrasoDTOParam.FormaApresentacao"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<group name="FA" isReprintHeaderOnEachPage="true" keepTogether="true">
		<groupExpression><![CDATA[$V{FA}.ESTABELECIMENTO.equals($P{FORMA_APRESENTACAO})
?
    $F{empresa}.getCodigo().toString()
    :
        $V{FA}.TIPO_ATENDIMENTO.equals($P{FORMA_APRESENTACAO})
        ?
            $F{tipoAtendimento}.getCodigo().toString()
        :
            $V{FA}.PROFISSIONAL.equals($P{FORMA_APRESENTACAO})
            ?
                $F{profissional}.getCodigo().toString()
            :
                null]]></groupExpression>
		<groupHeader>
			<band height="22">
				<printWhenExpression><![CDATA[!$V{FA}.GERAL.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
				<rectangle radius="10">
					<reportElement x="0" y="1" width="555" height="18" uuid="9acf27c7-80b0-40ee-98f8-a67674c92b9c"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField>
					<reportElement x="10" y="2" width="534" height="16" uuid="e199520c-c239-4028-a5d2-b36929bfb86b"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true" isUnderline="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{FA}.ESTABELECIMENTO.equals($P{FORMA_APRESENTACAO})
?
    $V{BUNDLE}.getStringApplication("rotulo_estabelecimento") + ": " + $F{empresa}.getDescricao()
    :
        $V{FA}.TIPO_ATENDIMENTO.equals($P{FORMA_APRESENTACAO})
        ?
            $V{BUNDLE}.getStringApplication("rotulo_tipo_atendimento") + ": " + $F{tipoAtendimento}.getDescricao()
        :
            $V{FA}.PROFISSIONAL.equals($P{FORMA_APRESENTACAO})
            ?
                $V{BUNDLE}.getStringApplication("rotulo_profissional") + ": " + $F{profissional}.getNome()
            :
                null]]></textFieldExpression>
				</textField>
			</band>
			<band height="23">
				<line>
					<reportElement x="0" y="21" width="555" height="1" uuid="fb44fa67-4ef7-47e8-b53f-ca21c6313e0c"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField isStretchWithOverflow="true">
					<reportElement x="1" y="2" width="180" height="19" uuid="36c27d1a-5ef6-46d4-ba6b-6be52713415b"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_paciente")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="294" y="2" width="41" height="19" uuid="98220092-e278-4e44-86ce-0067bbff3318"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_sexo")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="337" y="2" width="50" height="19" uuid="0d96a9a6-43e0-4328-aadf-93e500adc2c8"/>
					<box leftPadding="0">
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dt_ultimo_atendimento")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="391" y="2" width="120" height="19" uuid="30c1f07d-caee-4e01-86fa-7fe777c51ace">
						<printWhenExpression><![CDATA[!$V{FA}.TIPO_ATENDIMENTO.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
					</reportElement>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_tipo_atendimento")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="513" y="2" width="41" height="19" uuid="f68e51f7-ac8f-4d39-b822-f03d536778ba"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dias_atraso")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="184" y="2" width="108" height="19" uuid="a8c22e78-b96d-4820-92b5-c93311eda089"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Bottom">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_idade")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<detail>
		<band height="15" splitType="Stretch">
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="555" height="14" backcolor="#DFDFDF" uuid="57dcc369-332c-42e7-b1d6-6d8ac10e30c7">
					<printWhenExpression><![CDATA[$V{COLUMN_COUNT} % 2 == 0]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="1" y="0" width="180" height="14" uuid="d99a197a-a0d6-44d2-ac10-161c31df6587"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNome()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="294" y="0" width="41" height="14" uuid="2ea5dd97-9ad4-4892-92aa-167fe4e6c379"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getSexoFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="337" y="0" width="50" height="14" uuid="9e92fc7d-a1f9-4a85-8a08-b72657974a45"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataUltimoAtendimento}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="391" y="0" width="120" height="14" uuid="f45c083d-7e0e-4d0b-bda8-d324add668df">
					<printWhenExpression><![CDATA[!$V{FA}.TIPO_ATENDIMENTO.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tipoAtendimento}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="513" y="0" width="41" height="14" uuid="9df24f72-6da9-45e4-859f-0bb2115bf1f9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{diasEmAtraso}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="184" y="0" width="108" height="14" uuid="5f6a8578-006b-44c1-91f2-8df21e8f3ca7"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[Data.getDescricaoIdadeSimples($F{usuarioCadsus}.getDataNascimento())]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
