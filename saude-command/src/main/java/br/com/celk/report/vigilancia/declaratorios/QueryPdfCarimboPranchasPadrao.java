package br.com.celk.report.vigilancia.declaratorios;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.PdfCarimboPranchaDTOParam;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaInscricaoImob;
import org.hibernate.Query;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class QueryPdfCarimboPranchasPadrao extends CommandQuery implements ITransferDataReport<PdfCarimboPranchaDTOParam, ImpressaoCarimboAprovacaoDTO> {

    private PdfCarimboPranchaDTOParam param;
    private List<ImpressaoCarimboAprovacaoDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        ImpressaoCarimboAprovacaoDTO proxy = on(ImpressaoCarimboAprovacaoDTO.class);
        hql.setTypeSelect(ImpressaoCarimboAprovacaoDTO.class.getName());

        hql.addToSelect("cast(requerimentoVigilancia.dataFinalizacao as date)", path(proxy.getDataEmissao()));
        hql.addToSelect("requerimentoVigilancia.protocolo", path(proxy.getProtocolo()));
        hql.addToSelect("cast(hidrossanitarioPadrao.numeroAprovacao as text)", path(proxy.getNumeroAprovacao()));

        hql.addToFrom("RequerimentoProjetoHidrossanitario hidrossanitarioPadrao "
                + " left join hidrossanitarioPadrao.requerimentoVigilancia requerimentoVigilancia");

        hql.addToWhereWhithAnd("requerimentoVigilancia.codigo = ", param.getCodigo());
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(result)) {
            for (ImpressaoCarimboAprovacaoDTO item : result) {
                List<RequerimentoVigilanciaInscricaoImob> listInscricoesImobiliarias =
                        this.getSession().createCriteria(RequerimentoVigilanciaInscricaoImob.class)
                        .add(Restrictions.eq(VOUtils.montarPath(RequerimentoVigilanciaInscricaoImob.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_CODIGO), param.getCodigo()))
                        .list();
                if (CollectionUtils.isNotNullEmpty(listInscricoesImobiliarias)) {
                    item.setInscricaoImobiliaria(listInscricoesImobiliarias.get(0).getNumeroInscricaoImobiliaria());
                }
            }
        }

        if (param.isPreVisualizar() && param.getCodigo() == null) {
            ArrayList<ImpressaoCarimboAprovacaoDTO> listaFicticia = new ArrayList<>();
            ImpressaoCarimboAprovacaoDTO objetoFicticio = new ImpressaoCarimboAprovacaoDTO(
                    "Inscrição imobiliaria: 1111111111111",
                    DataUtil.getDataAtual(),
                    "123456",
                    1234562022L,
                    "PADRÃO"
            );
            listaFicticia.add(objetoFicticio);
            result = listaFicticia;
        }
    }

    @Override
    public List<ImpressaoCarimboAprovacaoDTO> getResult() {
        return this.result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List) result);
    }

    @Override
    public void setDTOParam(PdfCarimboPranchaDTOParam param) {
        this.param = param;
    }

    @Override
    protected void customQuery(Query query) {
        query.setMaxResults(1);
    }
}
