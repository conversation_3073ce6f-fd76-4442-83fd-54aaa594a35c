package br.com.celk.report.unidadesaude;

import br.com.celk.report.unidadesaude.query.QueryRelatorioMonitoramentoUPA;
import br.com.celk.unidadesaude.esus.relatorios.dto.RelatorioMonitoramentoUPADTOParam;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioMonitoramentoUpa extends AbstractReport<RelatorioMonitoramentoUPADTOParam> {

    public RelatorioMonitoramentoUpa(RelatorioMonitoramentoUPADTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/unidadesaude/jrxml/relatorio_monitoramento_upa.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_monitoramento_upa");
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioMonitoramentoUPA();
    }



}
