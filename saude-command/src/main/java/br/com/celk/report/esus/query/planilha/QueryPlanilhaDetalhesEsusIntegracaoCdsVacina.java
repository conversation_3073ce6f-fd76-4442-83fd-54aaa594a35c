package br.com.celk.report.esus.query.planilha;

import br.com.celk.unidadesaude.esus.cds.interfaces.dto.DetalhesItensIntegracaoEsusDTO;
import br.com.celk.unidadesaude.esus.cds.interfaces.dto.DetalhesItensIntegracaoEsusDTOParam;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.esus.EsusIntegracaoCds;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Query;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Created by Leonardo.
 */
public class QueryPlanilhaDetalhesEsusIntegracaoCdsVacina extends CommandQuery<QueryPlanilhaDetalhesEsusIntegracaoCdsVacina> {

    private DetalhesItensIntegracaoEsusDTOParam param;
    private List<DetalhesItensIntegracaoEsusDTO> list;

    public QueryPlanilhaDetalhesEsusIntegracaoCdsVacina(DetalhesItensIntegracaoEsusDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        DetalhesItensIntegracaoEsusDTO proxy = on(DetalhesItensIntegracaoEsusDTO.class);
        hql.setTypeSelect(DetalhesItensIntegracaoEsusDTO.class.getName());

        hql.addToSelect("esusIntegracaoCds.codigo", "esusIntegracaoCds.codigo");
        hql.addToSelect("esusIntegracaoCds.uuid", "esusIntegracaoCds.uuid");
        hql.addToSelect("esusIntegracaoCds.descricaoInconsistenciaEsus", "esusIntegracaoCds.descricaoInconsistenciaEsus");

        hql.addToSelect("cast(esusFichaVacina.cnsProfissional as long)", "numeroCartao");

        hql.addToSelect("atendimento.codigo", "esusIntegracaoCds.atendimento.codigo");

        hql.addToSelect("contaPaciente.codigo", "esusIntegracaoCds.contaPaciente.codigo");

        hql.addToSelect("esusFichaVacina.codigo", "esusIntegracaoCds.esusFichaVacina.codigo");
        hql.addToSelect("esusFichaVacina.dataAplicacao", "esusIntegracaoCds.esusFichaVacina.dataAplicacao");
        hql.addToSelect("atendimento.dataAtendimento", "esusIntegracaoCds.esusFichaVacina.dataAtendimento");

        hql.addToSelect("usuarioCadsusVacina.nome", "esusIntegracaoCds.esusFichaVacina.usuarioCadsus.nome");

        hql.addToSelect("usuarioCadsus.codigo", "esusIntegracaoCds.atendimento.usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "esusIntegracaoCds.atendimento.usuarioCadsus.nome");

        hql.addToSelect("usuarioCadsusContaPaciente.codigo", "esusIntegracaoCds.contaPaciente.usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsusContaPaciente.nome", "esusIntegracaoCds.contaPaciente.usuarioCadsus.nome");

        hql.addToSelect("empresa.codigo", "esusIntegracaoCds.esusFichaVacina.empresa.codigo");
        hql.addToSelect("empresa.descricao", "esusIntegracaoCds.esusFichaVacina.empresa.descricao");

        hql.addToSelect("profissional.codigo", "esusIntegracaoCds.esusFichaVacina.profissional.codigo");
        hql.addToSelect("profissional.nome", "esusIntegracaoCds.esusFichaVacina.profissional.nome");

        hql.addToSelect("tipoVacina.descricao", "esusIntegracaoCds.esusFichaVacina.vacinaAplicacao.tipoVacina.descricao");

        hql.addToFrom("EsusIntegracaoCds esusIntegracaoCds "
                + " left join esusIntegracaoCds.exportacaoEsusProcesso exportacaoEsusProcesso "
                + " left join esusIntegracaoCds.atendimento atendimento "
                + " left join esusIntegracaoCds.contaPaciente contaPaciente "
                + " left join atendimento.usuarioCadsus usuarioCadsus "
                + " left join contaPaciente.usuarioCadsus usuarioCadsusContaPaciente "
                + " left join esusIntegracaoCds.esusFichaVacina esusFichaVacina "
                + " left join esusFichaVacina.usuarioCadsus usuarioCadsusVacina "
                + " left join esusFichaVacina.vacinaAplicacao vacinaAplicacao "
                + " left join vacinaAplicacao.tipoVacina tipoVacina "
                + " left join esusFichaVacina.empresa empresa "
                + " left join esusFichaVacina.profissional profissional ");

        hql.addToWhereWhithAnd("esusIntegracaoCds.tipo = ", EsusIntegracaoCds.Tipo.VACINA.value());
        hql.addToWhereWhithAnd("exportacaoEsusProcesso.codigo = ", param.getCodigoExportacaoEsusProcesso());

        hql.addToWhereWhithAnd(hql.getConsultaLiked("usuarioCadsus.nome", this.param.getPaciente()));
        hql.addToWhereWhithAnd("esusIntegracaoCds.uuid = ", param.getUuid());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("esusIntegracaoCds.descricaoInconsistenciaEsus", this.param.getInconsistencia()));
        hql.addToWhereWhithAnd("atendimento.codigo = ", param.getCodigoAtendimento());
        hql.addToWhereWhithAnd("cast(esusFichaVacina.dataAtendimento as date) = ", param.getDataAtendimento());
        hql.addToWhereWhithAnd("empresa = ", param.getEmpresa());
        hql.addToWhereWhithAnd("profissional = ", param.getProfissional());

        if(RepositoryComponentDefault.SIM_LONG.equals(param.getComInconsistencia())) {
            hql.addToWhereWhithAnd("esusIntegracaoCds.descricaoInconsistenciaEsus is not null");
        } else if(RepositoryComponentDefault.NAO_LONG.equals(param.getComInconsistencia())){
            hql.addToWhereWhithAnd("esusIntegracaoCds.descricaoInconsistenciaEsus is null");
        }

        if (this.param.getNumeroCartao()!= null
                && !this.param.getNumeroCartao().replaceAll("[^0-9]", "").isEmpty()) {
            hql.addToWhereWhithAnd("EXISTS(SELECT 1 FROM esus_ficha_vacina WHERE cns_profissional = :numeroCartao)");
        }

        String orderType;
        String orderField = param.getPropSort();

        if(param.isAscending()){
            orderType = "asc";
        } else {
            orderType = "desc";
        }

        if (StringUtils.trimToNull(param.getPropSort()) != null) {
            if (param.getPropSort().startsWith(path(proxy.getNomePaciente()))) {
                hql.addToOrder("usuarioCadsus.nome " + orderType);
                hql.addToOrder("usuarioCadsusContaPaciente.nome " + orderType);
            } else {
                hql.addToOrder(orderField + " " + orderType);
            }
        } else {
            hql.addToOrder("esusIntegracaoCds.dataCadastro desc");
        }
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) {
        if (this.param.getNumeroCartao()!= null
                && !this.param.getNumeroCartao().replaceAll("[^0-9]", "").isEmpty()) {
            query.setParameter("numeroCartao", Long.parseLong(this.param.getNumeroCartao().replaceAll("[^0-9]", "")));
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    public Collection getResult() {
        return this.list;
    }
}