package br.com.celk.report.vigilancia.roteiroinspecao.query;

import br.com.celk.report.vigilancia.roteiroinspecao.dto.ImpressaoRoteiroInspecaoDTO;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecao;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecaoRoteiro;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecaoRoteiroItemPerguntaResposta;
import org.hibernate.Query;
import org.hibernate.Session;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public abstract class QueryImpressaoRoteiroInspecao extends CommandQuery<QueryImpressaoRoteiroInspecao> implements ITransferDataReport<RegistroInspecao, ImpressaoRoteiroInspecaoDTO> {

    private RegistroInspecao registroInspecao;
    private List<ImpressaoRoteiroInspecaoDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(ImpressaoRoteiroInspecaoDTO.class.getName());

        hql.addToSelect("estabelecimento.codigo", "registroInspecao.estabelecimento.codigo");
        hql.addToSelect("estabelecimento.razaoSocial", "registroInspecao.estabelecimento.razaoSocial");
        hql.addToSelect("estabelecimento.fantasia", "registroInspecao.estabelecimento.fantasia");
        hql.addToSelect("estabelecimento.cnpjCpf", "registroInspecao.estabelecimento.cnpjCpf");
        hql.addToSelect("estabelecimento.numeroLogradouro", "registroInspecao.estabelecimento.numeroLogradouro");
        hql.addToSelect("estabelecimento.telefone", "registroInspecao.estabelecimento.telefone");
        hql.addToSelect("estabelecimento.representanteNome", "registroInspecao.estabelecimento.representanteNome");
        hql.addToSelect("estabelecimento.alvara", "registroInspecao.estabelecimento.alvara");
        hql.addToSelect("estabelecimento.horaInicioPrimeiroTurno", "registroInspecao.estabelecimento.horaInicioPrimeiroTurno");
        hql.addToSelect("estabelecimento.horaFimPrimeiroTurno", "registroInspecao.estabelecimento.horaFimPrimeiroTurno");
        hql.addToSelect("estabelecimento.horaInicioSegundoTurno", "registroInspecao.estabelecimento.horaInicioSegundoTurno");
        hql.addToSelect("estabelecimento.horaFimSegundoTurno", "registroInspecao.estabelecimento.horaFimSegundoTurno");
        hql.addToSelect("estabelecimento.numeroFuncionarios", "registroInspecao.estabelecimento.numeroFuncionarios");

        hql.addToSelect("endereco.codigo", "registroInspecao.vigilanciaEndereco.codigo");
        hql.addToSelect("endereco.logradouro", "registroInspecao.vigilanciaEndereco.logradouro");
        hql.addToSelect("endereco.cep", "registroInspecao.vigilanciaEndereco.cep");
        hql.addToSelect("endereco.bairro", "registroInspecao.vigilanciaEndereco.bairro");
        hql.addToSelect("cidade.codigo", "registroInspecao.vigilanciaEndereco.cidade.codigo");
        hql.addToSelect("cidade.descricao", "registroInspecao.vigilanciaEndereco.cidade.descricao");
        hql.addToSelect("estado.codigo", "registroInspecao.vigilanciaEndereco.cidade.estado.codigo");
        hql.addToSelect("estado.sigla", "registroInspecao.vigilanciaEndereco.cidade.estado.sigla");

        hql.addToSelect("motivoInspecao.codigo", "motivoInspecao.codigo");
        hql.addToSelect("motivoInspecao.descricao", "motivoInspecao.descricao");

        hql.addToSelect("registroInspecaoRoteiro.codigo", "registroInspecaoRoteiro.codigo");
        hql.addToSelect("registroInspecaoRoteiro.nomeRoteiro", "registroInspecaoRoteiro.nomeRoteiro");
        hql.addToSelect("registroInspecaoRoteiro.observacaoInicial", "registroInspecaoRoteiro.observacaoInicial");
        hql.addToSelect("registroInspecaoRoteiro.observacaoFinal", "registroInspecaoRoteiro.observacaoFinal");
        hql.addToSelect("registroInspecaoRoteiro.enquadramentoLegal", "registroInspecaoRoteiro.enquadramentoLegal");

        hql.addToSelect("registroInspecaoRoteiroItem.codigo", "registroInspecaoRoteiroItem.codigo");
        hql.addToSelect("registroInspecaoRoteiroItem.subtitulo", "registroInspecaoRoteiroItem.subtitulo");
        hql.addToSelect("registroInspecaoRoteiroItem.enquadramentoLegal", "registroInspecaoRoteiroItem.enquadramentoLegal");

        hql.addToSelect("perguntaResposta.codigo", "perguntaResposta.codigo");
        hql.addToSelect("perguntaResposta.pergunta", "perguntaResposta.pergunta");
        hql.addToSelect("perguntaResposta.resposta", "perguntaResposta.resposta");
        hql.addToSelect("perguntaResposta.classificacao", "perguntaResposta.classificacao");
        hql.addToSelect("perguntaResposta.leiArtigo", "perguntaResposta.leiArtigo");
        hql.addToSelect("perguntaResposta.observacao", "perguntaResposta.observacao");
        hql.addToSelect("perguntaResposta.providencia", "perguntaResposta.providencia");

        hql.addToSelect("registroInspecao.codigo", "registroInspecao.codigo");
        hql.addToSelect("registroInspecao.dataInspecao", "registroInspecao.dataInspecao");
        hql.addToSelect("registroInspecao.observacao", "registroInspecao.observacao");

        hql.addToSelect("pessoa.codigo", "registroInspecao.vigilanciaPessoa.codigo");
        hql.addToSelect("pessoa.nome", "registroInspecao.vigilanciaPessoa.nome");
        hql.addToSelect("pessoa.nomeFantasia", "registroInspecao.vigilanciaPessoa.nomeFantasia");
        hql.addToSelect("pessoa.cpf", "registroInspecao.vigilanciaPessoa.cpf");
        hql.addToSelect("pessoa.celular", "registroInspecao.vigilanciaPessoa.celular");
        hql.addToSelect("pessoa.numeroLogradouro", "registroInspecao.vigilanciaPessoa.numeroLogradouro");
        hql.addToSelect("pessoa.representanteLegal", "registroInspecao.vigilanciaPessoa.representanteLegal");

        hql.addToFrom("RegistroInspecaoRoteiroItemPerguntaResposta perguntaResposta "
                + " left join perguntaResposta.registroInspecaoRoteiroItem registroInspecaoRoteiroItem"
                + " left join registroInspecaoRoteiroItem.registroInspecaoRoteiro registroInspecaoRoteiro"
                + " left join registroInspecaoRoteiro.registroInspecao registroInspecao"
                + " left join registroInspecao.motivoInspecao motivoInspecao"
                + " left join registroInspecao.estabelecimento estabelecimento"
                + " left join registroInspecao.vigilanciaPessoa pessoa"
                + " left join registroInspecao.vigilanciaEndereco endereco"
                + " left join endereco.cidade cidade"
                + " left join cidade.estado estado");

        hql.addToWhereWhithAnd("registroInspecao.codigo = ", registroInspecao.getCodigo());

        hql.addToWhereWhithAnd("(perguntaResposta.codigo = perguntaResposta.registroInspecaoRoteiroItemPerguntaRespostaOrigem.codigo)");

        hql.addToWhereWhithAnd("(registroInspecaoRoteiro.status = :inspecao OR (registroInspecaoRoteiro.status = :reinspecao AND perguntaResposta.resposta = :nao))");

        hql.addToOrder("registroInspecaoRoteiro.nomeRoteiro");
        hql.addToOrder("registroInspecaoRoteiroItem.codigo");
        hql.addToOrder("perguntaResposta.codigo");
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(result)) {
            useSession(session);
        }
    }

    public abstract void useSession(Session session);

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        super.setParameters(hql, query);
        query.setLong("inspecao", RegistroInspecaoRoteiro.Status.EM_INSPECAO.value());
        query.setLong("reinspecao", RegistroInspecaoRoteiro.Status.EM_REINSPECAO.value());
        query.setLong("nao", RegistroInspecaoRoteiroItemPerguntaResposta.Resposta.NAO.value());
    }

    @Override
    public List<ImpressaoRoteiroInspecaoDTO> getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public void setDTOParam(RegistroInspecao param) {
        this.registroInspecao = param;
    }
}
