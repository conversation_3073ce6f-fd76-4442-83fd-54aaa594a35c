<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_encaminhamento" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="8727c5ee-dacc-4501-9bff-1afe095fc3c3">
	<property name="ireport.zoom" value="2.000000000000015"/>
	<property name="ireport.x" value="380"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="br.com.celk.util.DataUtil"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="formaApresentacao" class="java.lang.String"/>
	<parameter name="visualizarOcorrencia" class="java.lang.String"/>
	<queryString>
		<![CDATA[comorbidades]]>
	</queryString>
	<field name="codigoUsuarioCadsus" class="java.lang.Long"/>
	<field name="nomePaciente" class="java.lang.String"/>
	<field name="idade" class="java.lang.Long"/>
	<field name="indicadorQuatro" class="java.lang.String"/>
	<field name="dataUltimoPreventivo" class="java.lang.String"/>
	<field name="ultimoResultado" class="java.lang.String"/>
	<field name="observacaoUltimoResultado" class="java.lang.String"/>
	<field name="nomeEquipe" class="java.lang.String"/>
	<field name="telefone" class="java.lang.String"/>
	<field name="celular" class="java.lang.String"/>
	<field name="endereco" class="java.lang.String"/>
	<field name="nomeEquipeAtendimento" class="java.lang.String"/>
	<field name="nomeEquipeResidencia" class="java.lang.String"/>
	<field name="dataUltimoPreventivoEsusComIne" class="java.lang.String"/>
	<group name="Geral"/>
	<group name="detailHeader" isReprintHeaderOnEachPage="true">
		<groupHeader>
			<band height="30">
				<line>
					<reportElement x="0" y="29" width="802" height="1" uuid="bc4c6e85-dcf4-4687-b93e-de4f34764a96"/>
				</line>
				<textField>
					<reportElement x="0" y="1" width="50" height="28" uuid="21e8cd0f-9d96-425b-8ab1-48a6d0c24341"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_codigo_usuario_cadsus")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="50" y="1" width="101" height="28" uuid="6ee35731-8d17-4a4b-981f-f853bd8ca6e6"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nome_mulher")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="151" y="1" width="32" height="28" uuid="1eb1d543-f368-4dd1-a611-6ce8a4b5046e"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_idade")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="183" y="1" width="37" height="28" uuid="0fb22d61-a57c-4ae5-b2bc-fec56e17b67b"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_indicador_quatro")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="220" y="1" width="49" height="28" uuid="12c99a08-16d1-4c27-955a-b48e53b91b05"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_ultimo_preventivo")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="269" y="1" width="49" height="28" uuid="8317e079-086b-46b4-a94a-edd6bd42da46"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_ultimo_preventivo_esus_com_ine")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="508" y="1" width="64" height="28" uuid="ef755b0a-1eff-4152-b56d-452b2936b3bc"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_telefone_celular")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="318" y="1" width="78" height="28" uuid="fd7b8960-4f89-4f91-98a7-82361910f881"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_ultimo_resultado")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="396" y="1" width="59" height="28" uuid="218c8c20-f14c-4153-bb10-2d1ca6e47d58"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_observacao_ultimo_resultado")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="572" y="1" width="138" height="28" uuid="c8373df6-eba8-425f-bee6-f19b9df3f25a"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_endereco")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="455" y="1" width="53" height="28" uuid="7e976a6c-4418-428e-b024-910120ff4a8f"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_equipe")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="710" y="1" width="47" height="28" uuid="30b21b06-fea2-4b7e-b3fd-95dfeec1b881"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_equipe_atendimento")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="757" y="1" width="45" height="28" uuid="70c328bb-d8e3-4f3a-8b38-17903d105bd9"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_equipe_residencia")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="1" splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="21" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="2" width="50" height="14" uuid="35a11fa8-95f5-49d1-a488-49e3e62192e6"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{codigoUsuarioCadsus}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="50" y="2" width="101" height="14" uuid="fd44123e-aeed-4217-a5be-63ac62eb9cbd"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nomePaciente}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="151" y="2" width="32" height="14" uuid="1b9ac860-6a95-4df9-b9d6-6eb2a408179f"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{idade}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="183" y="2" width="37" height="14" uuid="82caa57f-be4b-4950-8ee6-b5d76fffe282"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{indicadorQuatro}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="220" y="2" width="49" height="14" uuid="2ab9b4b6-bb30-4144-8d55-6dccf0187684"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataUltimoPreventivo}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="318" y="2" width="78" height="14" uuid="d41b59e6-eedc-4d11-bd57-e8d574b34e4d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ultimoResultado}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="396" y="2" width="59" height="14" uuid="82517089-7a2c-4e90-a7ce-a698d515834d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{observacaoUltimoResultado}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="508" y="2" width="64" height="14" uuid="507aa07a-8098-4251-8c84-84f82c5c7360"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[( $F{celular}!=null ? $F{celular}: $F{telefone} )]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="572" y="2" width="138" height="14" uuid="3e0d14a0-00fd-4693-b288-16d0b57715dc"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{endereco}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="710" y="2" width="47" height="14" uuid="eb8e7a90-2d3c-4bfa-9637-6b5f028bade6"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nomeEquipeAtendimento}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="757" y="2" width="45" height="14" uuid="0ca140bb-8c21-4349-8bf2-72fc981a1652"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nomeEquipeResidencia}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="455" y="2" width="53" height="14" uuid="6a0658e1-d678-48d8-8958-f99a8ff0c58d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nomeEquipe}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="269" y="2" width="49" height="14" uuid="0bae37f9-be90-4269-8f00-b35b61f945e8"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataUltimoPreventivoEsusComIne}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
