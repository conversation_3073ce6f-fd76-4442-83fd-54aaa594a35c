package br.com.celk.report.vigilancia.query;

import br.com.celk.bo.hospital.endereco.EnderecoHelper;
import br.com.celk.util.CollectionUtils;
import br.com.celk.vigilancia.dto.FichaInvestigacaoAgravoHivGestanteDTO;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FichaInvestigacaoAgravoDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioAgravosDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import org.hibernate.Session;

import java.util.Date;
import java.util.List;
import java.util.Map;

public class QueryFichaHivGestante extends CommandQuery implements ITransferDataReport<FichaInvestigacaoAgravoDTOParam, Map<String, Object>> {

    private FichaInvestigacaoAgravoDTOParam param;
    private List<Map<String, Object>> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        QueryFichasinvestigacaoHelper fichasHelper = new QueryFichasinvestigacaoHelper();
        hql.addToSelect("TO_CHAR(registroAgravo.dataRegistro, 'dd/mm/yyyy')", "registroAgravo.dataRegistro");
        hql.addToSelect("TO_CHAR(registroAgravo.dataCadastro, 'dd/mm/yyyy')", "registroAgravo.dataCadastro");
        hql.addToSelect("TO_CHAR(registroAgravo.dataEncerramento, 'dd/mm/yyyy')", "registroAgravo.dataEncerramento");
        hql.addToSelect("registroAgravo.escolaridade", "registroAgravo.escolaridade");
        hql.addToSelect("estadoRegistroAgravo.sigla", "estadoRegistroAgravo.sigla");
        hql.addToSelect("cidadeRegistroAgravo.codigo", "cidadeRegistroAgravo.codigo");
        hql.addToSelect("cidadeRegistroAgravo.descricao", "cidadeRegistroAgravo.descricao");
        hql.addToSelect("unidadeRegistroAgravo.descricao", "unidadeRegistroAgravo.descricao");
        hql.addToSelect("unidadeRegistroAgravo.codigo", "unidadeRegistroAgravo.codigo");
        hql.addToSelect("unidadeRegistroAgravo.cnes", "unidadeRegistroAgravo.cnes");
        hql.addToSelect("paciente.nome", "paciente.nome");
        hql.addToSelect("TO_CHAR(paciente.dataNascimento, 'dd/mm/yyyy')", "paciente.dataNascimento");
        hql.addToSelect("paciente.sexo", "paciente.sexo");
        hql.addToSelect("escolaridadePaciente.codigo", "escolaridadePaciente.codigo");
        hql.addToSelect("escolaridadePaciente.descricao", "escolaridadePaciente.descricao");
        hql.addToSelect("paciente.nomeMae", "paciente.nomeMae");
        hql.addToSelect("(case " +
                "when racaPaciente.codigo = 3 then 4 " +
                "when racaPaciente.codigo = 4 then 3 " +
                "else racaPaciente.codigo end)", "racaPaciente.codigo");// o codigo do banco esta diferente do codigo da ficha
        hql.addToSelect("estadoPaciente.sigla", "estadoPaciente.sigla");
        hql.addToSelect("cidadePaciente.codigo", "cidadePaciente.codigo");
        hql.addToSelect("cidadePaciente.descricao", "cidadePaciente.descricao");
        hql.addToSelect("investigacaoAgravoHivGestante.distrito", "investigacaoAgravoHivGestante.distrito");
        hql.addToSelect("investigacaoAgravoHivGestante.geoCampo1", "investigacaoAgravoHivGestante.geoCampo1");
        hql.addToSelect("investigacaoAgravoHivGestante.geoCampo2", "investigacaoAgravoHivGestante.geoCampo2");
        hql.addToSelect("investigacaoAgravoHivGestante.zona", "investigacaoAgravoHivGestante.zona");
        hql.addToSelect("registroAgravo.codigo", "registroAgravo.codigo");
        hql.addToSelect("registroAgravo.idadeGestacional", "registroAgravo.idadeGestacional");
        hql.addToSelect("ocupacao.descricao", "ocupacao.descricao");
        hql.addToSelect("investigacaoAgravoHivGestante.evidenciaLaboratorialHiv", "investigacaoAgravoHivGestante.evidenciaLaboratorialHiv");
        hql.addToSelect("investigacaoAgravoHivGestante.fezFazPreNatal", "investigacaoAgravoHivGestante.fezFazPreNatal");
        hql.addToSelect("investigacaoAgravoHivGestante.numeroSisPreNatal", "investigacaoAgravoHivGestante.numeroSisPreNatal");
        hql.addToSelect("investigacaoAgravoHivGestante.usoAntiRetroviraisPreNatal", "investigacaoAgravoHivGestante.usoAntiRetroviraisPreNatal");
        hql.addToSelect("TO_CHAR(investigacaoAgravoHivGestante.dataInicioUsoAntiRetroviral, 'dd/mm/yyyy')", "investigacaoAgravoHivGestante.dataInicioUsoAntiRetroviral");
        hql.addToSelect("TO_CHAR(investigacaoAgravoHivGestante.dataParto, 'dd/mm/yyyy')", "investigacaoAgravoHivGestante.dataParto");
        hql.addToSelect("investigacaoAgravoHivGestante.tipoParto", "investigacaoAgravoHivGestante.tipoParto");
        hql.addToSelect("investigacaoAgravoHivGestante.fezUsoProfilaxiaAntiRetroviralDuranteParto", "investigacaoAgravoHivGestante.fezUsoProfilaxiaAntiRetroviralDuranteParto");
        hql.addToSelect("investigacaoAgravoHivGestante.evolucaoGravidez", "investigacaoAgravoHivGestante.evolucaoGravidez");
        hql.addToSelect("investigacaoAgravoHivGestante.inicioProfilaxiaAntiRetroviralCrianca", "investigacaoAgravoHivGestante.inicioProfilaxiaAntiRetroviralCrianca");
        hql.addToSelect("enderecoPaciente.bairro", "enderecoPaciente.bairro");
        hql.addToSelect("enderecoPaciente.logradouro", "enderecoPaciente.logradouro");
        hql.addToSelect("enderecoPaciente.numeroLogradouro", "enderecoPaciente.numeroLogradouro");
        hql.addToSelect("enderecoPaciente.pontoReferencia", "enderecoPaciente.pontoReferencia");
        hql.addToSelect("enderecoPaciente.cep", "enderecoPaciente.cep");
        hql.addToSelect("enderecoPaciente.telefone", "enderecoPaciente.telefone");
        hql.addToSelect("paisPaciente.descricao", "paisPaciente.descricao");
        hql.addToSelect("estadoUnidadePrenatal.sigla", "estadoUnidadePrenatal.sigla");
        hql.addToSelect("cidadeUnidadePrenatal.codigo", "cidadeUnidadePrenatal.codigo");
        hql.addToSelect("cidadeUnidadePrenatal.descricao", "cidadeUnidadePrenatal.descricao");
        hql.addToSelect("unidadeRealizacaoPreNatal.codigo", "unidadeRealizacaoPreNatal.codigo");
        hql.addToSelect("unidadeRealizacaoPreNatal.descricao", "unidadeRealizacaoPreNatal.descricao");
        hql.addToSelect("unidadeRealizacaoPreNatal.cnes", "unidadeRealizacaoPreNatal.cnes");
        hql.addToSelect("localRealizacaoParto.codigo", "localRealizacaoParto.codigo");
        hql.addToSelect("localRealizacaoParto.descricao", "localRealizacaoParto.descricao");
        hql.addToSelect("localRealizacaoParto.cnes", "localRealizacaoParto.cnes");
        hql.addToSelect("municipioParto.descricao", "municipioParto.descricao");
        hql.addToSelect("municipioParto.codigo", "municipioParto.codigo");
        hql.addToSelect("estadoParto.sigla", "estadoParto.sigla");
        hql.addToSelect("profissionalInvestigacao.codigo", "profissionalInvestigacao.codigo");
        hql.addToSelect("profissionalInvestigacao.nome", "profissionalInvestigacao.nome");
        hql.addToSelect("unidadeProfissionalInvestigacao.descricao", "unidadeProfissionalInvestigacao.descricao");
        hql.addToSelect("unidadeProfissionalInvestigacao.codigo", "unidadeProfissionalInvestigacao.codigo");
        hql.addToSelect("unidadeProfissionalInvestigacao.cnes", "unidadeProfissionalInvestigacao.cnes");
        hql.addToSelect("(select max(usuarioCadsusCns.numeroCartao) from UsuarioCadsusCns usuarioCadsusCns left join usuarioCadsusCns.usuarioCadsus usuarioCadsus where usuarioCadsus.codigo = paciente.codigo)", "usuarioCadsus.cns");
        hql.setTypeSelect(FichaInvestigacaoAgravoHivGestanteDTO.class.getName());
        hql.setConvertToLeftJoin(true);
        hql.addToWhereWhithAnd("paciente = ", this.param.getUsuarioCadsus());
        hql.addToWhereWhithAnd("unidadeRegistroAgravo = ", this.param.getUnidade());
        hql.addToWhereWhithAnd("cid.cidClassificacao = ", this.param.getClassificacaoCids());

        if (RelatorioAgravosDTOParam.Status.TODOS.value().equals(this.param.getStatus())) {
            hql.addToWhereWhithAnd("registroAgravo.status <> ", RegistroAgravo.Status.CANCELADO.value());
        } else {
            hql.addToWhereWhithAnd("registroAgravo.status = ", this.param.getStatus());
        }

        if (this.param.getUnidadeReferencia() != null) {
            fichasHelper.addFiltroUnidadeReferencia(hql,this.param.getUnidadeReferencia().getCodigo());
        }

        if (CollectionUtils.isNotNullEmpty(this.param.getListCid())) {
            hql.addToWhereWhithAnd(" cid in ", param.getListCid());
        }

        hql.addToWhereWhithAnd("registroAgravo.dataRegistro", this.param.getPeriodo());
        hql.addToFrom("" +
                "InvestigacaoAgravoHivGestante investigacaoAgravoHivGestante " +
                "left join investigacaoAgravoHivGestante.registroAgravo registroAgravo " +
                "left join investigacaoAgravoHivGestante.ocupacao ocupacao " +
                "left join investigacaoAgravoHivGestante.unidadeRealizacaoPreNatal unidadeRealizacaoPreNatal " +
                "left join investigacaoAgravoHivGestante.unidadeRealizacaoPreNatal.cidade cidadeUnidadePrenatal " +
                "left join investigacaoAgravoHivGestante.unidadeRealizacaoPreNatal.cidade.estado estadoUnidadePrenatal " +
                "left join investigacaoAgravoHivGestante.localRealizacaoParto localRealizacaoParto " +
                "left join investigacaoAgravoHivGestante.municipioParto municipioParto " +
                "left join investigacaoAgravoHivGestante.municipioParto.estado estadoParto " +
                "left join registroAgravo.empresa unidadeRegistroAgravo " +
                "left join registroAgravo.empresa.cidade cidadeRegistroAgravo " +
                "left join registroAgravo.empresa.cidade.estado estadoRegistroAgravo " +
                "left join registroAgravo.usuarioCadsus paciente " +
                "left join registroAgravo.profissionalInvestigacao profissionalInvestigacao " +
                "left join registroAgravo.unidadeProfissionalInvestigacao unidadeProfissionalInvestigacao " +
                "left join registroAgravo.usuarioCadsus.raca racaPaciente " +
                "left join registroAgravo.usuarioCadsus.escolaridade escolaridadePaciente " +
                "left join registroAgravo.usuarioCadsus.enderecoUsuarioCadsus enderecoPaciente " +
                "left join registroAgravo.usuarioCadsus.enderecoUsuarioCadsus.cidade cidadePaciente " +
                "left join registroAgravo.usuarioCadsus.enderecoUsuarioCadsus.cidade.estado estadoPaciente " +
                "left join registroAgravo.usuarioCadsus.paisNascimento paisPaciente " +
                " left join registroAgravo.cid cid" +
                " left join cid.cidClassificacao cidClassificacao");
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        super.customProcess(session);
        for (Map<String, Object> map : result) {
            addToSelectIdade(map);
            addToSelectCodigoIbgeEmpresa("cidadeRegistroAgravo_codigo", "cidadeRegistroAgravo_codigoIbge", map);
            addToSelectCodigoIbgeEmpresa("cidadePaciente_codigo", "cidadePaciente_codigoIbge", map);
            addToSelectCodigoIbgeEmpresa("cidadeUnidadePrenatal_codigo", "cidadeUnidadePrenatal_codigoIbge", map);
            addToSelectCodigoIbgeEmpresa("municipioParto_codigo", "municipioParto_codigoIbge", map);
            addToSelectEscolaridade(map);
            addFuncaoProfissional(map);
            addUsuarioImpressao(map);
            addToSelectTelefoneFormatado("paciente_telefone", "paciente_telefoneFormatado", map);
        }
    }

    private void addToSelectIdade(Map<String, Object> map) {
        Date dataNascimento = new Date((String)map.get("paciente_dataNascimento"));
        if (dataNascimento != null) {
            Long idade = this.getIdade(dataNascimento);
            map.put("paciente_idade", idade);
        }
    }

    private void addToSelectEscolaridade(Map<String, Object> map) {
        Long codigoNivelEscolaridade = (Long) map.get("registroAgravo_escolaridade");
        String descricaoEscolaridade = "";
        if (codigoNivelEscolaridade != null) {
            RegistroAgravo registroAgravo = new RegistroAgravo();
            registroAgravo.setEscolaridade(codigoNivelEscolaridade);
            descricaoEscolaridade = registroAgravo.getDescricaoEquivalenciaEscolaridade();
        }
        map.put("paciente_descricaoEscolaridade", descricaoEscolaridade);
    }

    private void addFuncaoProfissional(Map<String, Object> map) {
        Long profissionalId = (Long) map.get("profissionalInvestigacao_codigo");
        String profissionalFuncao = "";
        if (profissionalId != null) {
            Profissional profissional = LoadManager.getInstance(Profissional.class)
                    .addProperties(new HQLProperties(Profissional.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Profissional.PROP_CODIGO), profissionalId))
                    .start().getVO();
            if (profissional != null && profissional.getCboProfissional() != null) {
                TabelaCbo cbo = profissional.getCboProfissional();
                profissionalFuncao = cbo.getCbo() + " - " + cbo.getDescricao();
            }
            map.put("profissional_funcao", profissionalFuncao);
        }
    }

    private void addToSelectCodigoIbgeEmpresa(String field, String outField, Map<String, Object> map) {
        Long codigoCidadeEmpresa = (Long) map.get(field);
        if(codigoCidadeEmpresa != null) {
            Long codigoIbgeComDV = this.getCodigoIbgeEmpresa(codigoCidadeEmpresa);
            map.put(outField, codigoIbgeComDV);
        }
    }

    private Long getCodigoIbgeEmpresa(Long codigoCidadeEmpresa) {
        if(codigoCidadeEmpresa != null) {
            try {
                return EnderecoHelper.getCodigoIbgeComDV(codigoCidadeEmpresa);
            } catch (DAOException e) {
                Loggable.log.error(e.getMessage());
            } catch (ValidacaoException e) {
                Loggable.log.warn(e.getMessage());
            }
        }
        return null;
    }

    private void addUsuarioImpressao(Map<String, Object> map) {
        map.put("usuario_impressao", getSessao().getUsuario().getNome());
    }

    private void addToSelectTelefoneFormatado(String field, String outField, Map<String, Object> map) {
        String telefone = (String) map.get(field);
        if (telefone != null) {
            String telefoneFormatado = Util.getTelefoneFormatado(telefone);
            map.put(outField, telefoneFormatado);
        }
    }

    private Long getIdade(Date dataNascimento) {
        return Data.getIdade(Coalesce.asDate(dataNascimento));
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = (List<Map<String, Object>>) result;
    }

    @Override
    public void setDTOParam(FichaInvestigacaoAgravoDTOParam param) {
        this.param = param;
    }

    @Override
    public List<Map<String, Object>> getResult() {
        return result;
    }

}
