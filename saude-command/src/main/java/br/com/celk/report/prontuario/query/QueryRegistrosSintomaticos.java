package br.com.celk.report.prontuario.query;

import br.com.celk.prontuario.interfaces.dto.RelatorioRegistrosSintomaticosDTO;
import br.com.celk.prontuario.interfaces.dto.RelatorioRegistrosSintomaticosDTOParam;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseSintomaticoExames;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRegistrosSintomaticos extends CommandQuery<QueryRegistrosSintomaticos> implements ITransferDataReport<RelatorioRegistrosSintomaticosDTOParam, RelatorioRegistrosSintomaticosDTO> {

    private List<RelatorioRegistrosSintomaticosDTO> result;
    private RelatorioRegistrosSintomaticosDTOParam param;

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.addToSelect("ts.codigo", "tuberculoseSintomatico.codigo");
        hql.addToSelect("ts.dataRegistro", "tuberculoseSintomatico.dataRegistro");
        hql.addToSelect("ts.observacao", "tuberculoseSintomatico.observacao");

        hql.addToSelect("uc.codigo", "tuberculoseSintomatico.usuarioCadsus.codigo");
        hql.addToSelect("uc.nome", "tuberculoseSintomatico.usuarioCadsus.nome");
        hql.addToSelect("uc.apelido", "tuberculoseSintomatico.usuarioCadsus.apelido");
        hql.addToSelect("uc.utilizaNomeSocial", "tuberculoseSintomatico.usuarioCadsus.utilizaNomeSocial");
        hql.addToSelect("uc.sexo", "tuberculoseSintomatico.usuarioCadsus.sexo");
        hql.addToSelect("uc.dataNascimento", "tuberculoseSintomatico.usuarioCadsus.dataNascimento");
        hql.addToSelect("uc.prontuario", "tuberculoseSintomatico.usuarioCadsus.prontuario");

        hql.addToSelect("euc.codigo", "tuberculoseSintomatico.usuarioCadsus.enderecoUsuarioCadsus.codigo");
        hql.addToSelect("euc.logradouro", "tuberculoseSintomatico.usuarioCadsus.enderecoUsuarioCadsus.logradouro");
        hql.addToSelect("euc.numeroLogradouro", "tuberculoseSintomatico.usuarioCadsus.enderecoUsuarioCadsus.numeroLogradouro");
        hql.addToSelect("euc.bairro", "tuberculoseSintomatico.usuarioCadsus.enderecoUsuarioCadsus.bairro");
        hql.addToSelect("euc.cep", "tuberculoseSintomatico.usuarioCadsus.enderecoUsuarioCadsus.cep");

        hql.addToSelect("c.codigo", "tuberculoseSintomatico.usuarioCadsus.enderecoUsuarioCadsus.cidade.codigo");
        hql.addToSelect("c.descricao", "tuberculoseSintomatico.usuarioCadsus.enderecoUsuarioCadsus.cidade.descricao");

        hql.addToSelect("uf.codigo", "tuberculoseSintomatico.usuarioCadsus.enderecoUsuarioCadsus.cidade.estado.codigo");
        hql.addToSelect("uf.sigla", "tuberculoseSintomatico.usuarioCadsus.enderecoUsuarioCadsus.cidade.estado.sigla");

        hql.addToSelect("a.codigo", "tuberculoseSintomatico.atendimento.codigo");

        hql.addToSelect("e.codigo", "tuberculoseSintomatico.atendimento.empresa.codigo");
        hql.addToSelect("e.descricao", "tuberculoseSintomatico.atendimento.empresa.descricao");

        hql.setTypeSelect(RelatorioRegistrosSintomaticosDTO.class.getName());
        hql.addToFrom("TuberculoseSintomatico ts "
                + "left join ts.atendimento a "
                + "left join a.empresa e "
                + "left join ts.usuarioCadsus uc "
                + "left join uc.enderecoUsuarioCadsus euc "
                + "left join euc.cidade c "
                + "left join c.estado uf "
        );

        if (param.getPaciente() != null) {
            hql.addToWhereWhithAnd("(" + hql.getConsultaLiked(" uc.nome", param.getPaciente(), true)
                    + " OR (uc.utilizaNomeSocial = 1 AND " + hql.getConsultaLiked("uc.apelido", param.getPaciente(),true) + "))");
        }

        hql.addToWhereWhithAnd("ts.dataRegistro", param.getPeriodo());
        hql.addToWhereWhithAnd("e = ", param.getEmpresa());

        hql.addToOrder("ts.dataRegistro");
        hql.addToOrder("ts.codigo");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(this.result)) {
            for (RelatorioRegistrosSintomaticosDTO dto : this.result) {
                List<TuberculoseSintomaticoExames> exames = session.createCriteria(TuberculoseSintomaticoExames.class)
                        .add(Restrictions.eq(TuberculoseSintomaticoExames.PROP_TUBERCULOSE_SINTOMATICO, dto.getTuberculoseSintomatico()))
                        .addOrder(Order.asc(TuberculoseSintomaticoExames.PROP_EXAME))
                        .addOrder(Order.asc(TuberculoseSintomaticoExames.PROP_TIPO_AMOSTRA))
                        .list();
                dto.setExames(exames);
            }
        }
    }

    @Override
    public void setDTOParam(RelatorioRegistrosSintomaticosDTOParam param) {
        this.param = param;
    }

    @Override
    public List<RelatorioRegistrosSintomaticosDTO> getResult() {
        return result;
    }
}
