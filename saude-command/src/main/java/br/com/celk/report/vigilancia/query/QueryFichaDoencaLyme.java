package br.com.celk.report.vigilancia.query;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaLyme;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaLymeSituacaoRisco;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoRubeolaSarampo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoRubeolaSarampoDeslocamento;
import org.hibernate.Session;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class QueryFichaDoencaLyme extends QueryFichaInvestigacaoBase {

    @Override
    protected Class getClasseFichaInvestigacao() { return InvestigacaoAgravoDoencaLyme.class; }

    @Override
    protected Map<String, String> getCamposInvestigacaoAgravo() {
        Map<String, String> campos = new HashMap<>();

        campos.put(formatarData("investigacaoAgravo.dataInvestigacao"), "_31_data_investigacao");
        campos.put("ocupacaoCbo.descricao", "_32_ocupacao");

        campos.put("investigacaoAgravo.situacaoRiscoPicadaCarrapato", "_32_situacao_risco_picada_carrapato");
        campos.put(formatarData("investigacaoAgravo.dataPicadaCarrapato"), "_32_situacao_risco_picada_carrapato_data");
        campos.put("investigacaoAgravo.situacaoRiscoPresencaCarrapato", "_32_situacao_risco_presenca_carrapato");
        campos.put(formatarData("investigacaoAgravo.dataPresencaCarrapato"), "_32_situacao_risco_presenca_carrapato_data");
        campos.put("investigacaoAgravo.situacaoRiscoAreasMata", "_32_situacao_risco_areas_mata");
        campos.put(formatarData("investigacaoAgravo.dataAreasMata"), "_32_situacao_risco_areas_matas_data");
        campos.put("investigacaoAgravo.situacaoRiscoContatoAnimais", "_32_situacao_risco_contato_animais");
        campos.put(formatarData("investigacaoAgravo.dataContatoAnimais"), "_32_situacao_risco_contato_animais_data");
        campos.put("investigacaoAgravo.contatoAnimaisSilvestres", "_32_situacao_risco_contato_animais_silvestres");
        campos.put("investigacaoAgravo.contatoAnimaisDomesticos", "_32_situacao_risco_contato_animais_domesticos");
        campos.put("investigacaoAgravo.animaisDomesticosDesc", "_32_situacao_risco_contato_animais_domesticos_desc");
        campos.put("investigacaoAgravo.situacaoRiscoAnimaisDoentes", "_32_situacao_risco_contato_animais_doentes");
        campos.put(formatarData("investigacaoAgravo.dataAnimaisDoentes"), "_32_situacao_risco_contato_animais_doentes_data");
        campos.put("investigacaoAgravo.situacaoRiscoMorteAnimal", "_32_situacao_risco_morte_animal");
        campos.put(formatarData("investigacaoAgravo.dataMorteAnimal"), "_32_situacao_risco_morte_animal_data");
        campos.put("investigacaoAgravo.situacaoRiscoCasosHumanos", "_32_situacao_risco_casos_humanos");
        campos.put(formatarData("investigacaoAgravo.dataCasosHumanos"), "_32_situacao_risco_casos_humanos_data");

        campos.put("investigacaoAgravo.sinaisSintomasFebre", "_34_sinais_sintomas_febre");
        campos.put("investigacaoAgravo.sinaisSintomasMialgia", "_34_sinais_sintomas_mialgia");
        campos.put("investigacaoAgravo.sinaisSintomasAstralgia", "_34_sinais_sintomas_astralgia");
        campos.put("investigacaoAgravo.sinaisSintomasCefaleia", "_34_sinais_sintomas_cefaleia");
        campos.put("investigacaoAgravo.sinaisSintomasMalEstar", "_34_sinais_sintomas_mal_estar");
        campos.put("investigacaoAgravo.sinaisSintomasPetequias", "_34_sinais_sintomas_petequias");
        campos.put("investigacaoAgravo.sinaisSintomasRigidezNuca", "_34_sinais_sintomas_rigidez_nuca");
        campos.put("investigacaoAgravo.sinaisSintomasFadiga", "_34_sinais_sintomas_fadiga");
        campos.put("investigacaoAgravo.sinaisSintomasEritmeCronico", "_34_sinais_sintomas_eritme_cronico");
        campos.put("investigacaoAgravo.sinaisSintomasAumentoGanglios", "_34_sinais_sintomas_aumento_ganglios");

        campos.put("investigacaoAgravo.manifestacoesNaurologicasMeningite", "_35_manifestacoes_neurologicas_meningite");
        campos.put("investigacaoAgravo.manifestacoesNaurologicasNeuriteCraniana", "_35_manifestacoes_neurologicas_craniana");
        campos.put("investigacaoAgravo.manifestacoesNaurologicasPeriferica", "_35_manifestacoes_neurologicas_periferica");

        campos.put("investigacaoAgravo.manifestacoesCardiacasCardiomegalia", "_36_manifestacoes_cardiacas_cardiomegalia");
        campos.put("investigacaoAgravo.manifestacoesCardiacasArritmia", "_36_manifestacoes_cardiacas_arritmia");

        campos.put("investigacaoAgravo.casosAnterioresLyme", "_37_casos_anteriores_lyme");

        campos.put("investigacaoAgravo.hospitalizacao", "_38_hospitalizacao");
        campos.put(formatarData("investigacaoAgravo.dataInternacao"), "_39_data_internacao");
        campos.put(formatarData("investigacaoAgravo.dataAlta"), "_40_data_alta");
        campos.put("cidade.estado.sigla", "_41_estado");
        campos.put("investigacaoAgravo.hospital.descricao", "_52_hospital");

        campos.put(formatarData("investigacaoAgravo.sorologiaElisaData"), "_43_sorologia_elisa_data");
        campos.put("investigacaoAgravo.sorologiaElisaResultadoIgm", "_44_sorologia_elisa_resultado_igm");
        campos.put("investigacaoAgravo.sorologiaElisaResultadoIgmDesc", "_44_sorologia_elisa_resultado_igm_desc");
        campos.put("investigacaoAgravo.sorologiaElisaResultadoIgg", "_44_sorologia_elisa_resultado_igg");
        campos.put("investigacaoAgravo.sorologiaElisaResultadoIggDesc", "_44_sorologia_elisa_resultado_igg_desc");

        campos.put(formatarData("investigacaoAgravo.sorologiaWesternBlotData"), "_45_sorologia_western_blot_data");
        campos.put("investigacaoAgravo.sorologiaWesternBlotResultadoIgm", "_45_sorologia_western_blot_resultado_igm");
        campos.put("investigacaoAgravo.sorologiaWesternBlotResultadoIgmDesc", "_45_sorologia_western_blot_resultado_igm_desc");
        campos.put("investigacaoAgravo.sorologiaWesternBlotIgg", "_45_sorologia_western_blot_resultado_igg");
        campos.put("investigacaoAgravo.sorologiaWesternBlotIggDesc", "_45_sorologia_western_blot_resultado_igg_desc");

        campos.put("investigacaoAgravo.casoAutoctone", "_49_caso_autoctone");
        campos.put("cidadeLocalInfeccao.estado.sigla", "_50_estado");
        campos.put("investigacaoAgravo.cidadeLocalInfeccao.descricao", "_51_cidade");
        campos.put("investigacaoAgravo.localProvavelInfeccao", "_52_local_provavel_infeccao");
        campos.put("investigacaoAgravo.ambienteInfeccao", "_53_ambiente_infeccao");
        campos.put("investigacaoAgravo.doencaRelacionadaTrabalho", "_53_doenca_relacionada_trabalho");

        campos.put("investigacaoAgravo.classificacaoFinal", "_47_classificacao_final");
        campos.put("investigacaoAgravo.criterioConfirmacaoDescarte", "_48_criterio_confirmacao_descarte");
        campos.put("investigacaoAgravo.evolucaoCaso", "_54_evolucao_caso");
        campos.put(formatarData("investigacaoAgravo.dataObito"), "_55_data_obito");

        campos.put("investigacaoAgravo.observacao", "_observacao");

        return campos;
    }

    @Override
    protected String getJuncoesFicha() {
        return "left join investigacaoAgravo.ocupacaoCbo ocupacaoCbo "
                + "left join investigacaoAgravo.hospital hospital "
                + "left join hospital.cidade cidade "
                + "left join cidade.estado estado "
                + "left join investigacaoAgravo.cidadeLocalInfeccao cidadeLocalInfeccao "
                + "left join cidadeLocalInfeccao.estado estadoLocalInfeccao ";
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        for (Map<String, Object> map : getResult()) {
            addTabelaCBO(map);
            addListaDeslocamento(map);
        }
    }

    private void addTabelaCBO(Map<String, Object> map) {
        TabelaCbo cbo = (TabelaCbo) map.get("ocupacaoCbo.descricao");
        if (cbo != null) {
            map.put("ocupacaoCbo.descricao", cbo.getDescricaoFormatado());
        }
    }

    private void addListaDeslocamento(Map<String, Object> map) {
        int i;
        Long codigoInvestigacaoAgravoDoencaLyme = (Long) map.get("investigacaoAgravo_codigo");

        List<InvestigacaoAgravoDoencaLymeSituacaoRisco> deslocamentoList =
                LoadManager.getInstance(InvestigacaoAgravoDoencaLymeSituacaoRisco.class)
                        .addProperties(new HQLProperties(InvestigacaoAgravoDoencaLymeSituacaoRisco.class).getProperties())
                        .addProperty(VOUtils.montarPath(InvestigacaoAgravoDoencaLymeSituacaoRisco.PROP_CIDADE, Cidade.PROP_ESTADO, Estado.PROP_SIGLA))
                        .addParameter(new QueryCustom.QueryCustomParameter(
                                VOUtils.montarPath(InvestigacaoAgravoDoencaLymeSituacaoRisco.PROP_INVESTIGACAO_AGRAVO_DOENCA_LYME, InvestigacaoAgravoDoencaLyme.PROP_CODIGO),
                                codigoInvestigacaoAgravoDoencaLyme))
                        .start().getList();

        for (i = 0; i < deslocamentoList.size(); i++) {
            InvestigacaoAgravoDoencaLymeSituacaoRisco investigacaoAgravoDoencaLymeSituacaoRisco = deslocamentoList.get(i);

            map.put("_situacao_risco_data" + i, Data.formatar(investigacaoAgravoDoencaLymeSituacaoRisco.getData()));
            map.put("_situacao_risco_uf" + i, investigacaoAgravoDoencaLymeSituacaoRisco.getCidade().getEstado().getSigla());
            map.put("_situacao_risco_cidade" + i, investigacaoAgravoDoencaLymeSituacaoRisco.getCidade().getDescricao());
            map.put("_situacao_risco_endereco" + i, investigacaoAgravoDoencaLymeSituacaoRisco.getEndereco());
            map.put("_situacao_risco_localidade" + i, investigacaoAgravoDoencaLymeSituacaoRisco.getLocalidade());
        }
    }

}
