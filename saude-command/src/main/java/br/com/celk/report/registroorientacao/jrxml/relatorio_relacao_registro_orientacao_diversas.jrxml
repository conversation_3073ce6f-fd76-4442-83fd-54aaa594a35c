<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_registro_orientacao_diversas" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="30" bottomMargin="30" uuid="70b103f2-1238-47df-930d-20f73bd78afa">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.3579476910000023"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.celk.report.registroorientacao.interfaces.dto.RelatorioRelacaoRegistroOrientacaoDiversasDTO"/>
	<import value="br.com.celk.report.registroorientacao.interfaces.dto.RelatorioRelacaoRegistroOrientacaoDiversasDTOParam.FormaApresentacao"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.prontuario.basico.FormasUsoHelper"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="formaApresentacao" class="br.com.celk.report.registroorientacao.interfaces.dto.RelatorioRelacaoRegistroOrientacaoDiversasDTOParam.FormaApresentacao"/>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<field name="registroOrientacao" class="br.com.ksisolucoes.vo.atendimento.RegistroOrientacao"/>
	<field name="quantidade" class="java.lang.Long"/>
	<variable name="totalQuantidadeFA" class="java.lang.Long" resetType="Group" resetGroup="formaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}]]></variableExpression>
	</variable>
	<variable name="totalGeralQuantidade" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}]]></variableExpression>
	</variable>
	<group name="geral">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupFooter>
			<band height="18">
				<line>
					<reportElement uuid="ea615b6c-0324-426d-84a8-41a9fcfecfe6" x="432" y="1" width="103" height="1"/>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="07dc6e7d-c8cf-455d-90d8-8b7e568c1fad" key="textField-4" x="368" y="3" width="109" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_geral") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="6d20825d-bffa-493e-bf4d-7258b24963ee" key="textField-4" x="478" y="3" width="57" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalGeralQuantidade}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="formaApresentacao">
		<groupExpression><![CDATA[FormaApresentacao.PROFISSIONAL.equals($P{formaApresentacao}) ?
    $F{profissional}
:
    FormaApresentacao.DATA_ORIENTACAO.equals($P{formaApresentacao}) ?
        $F{registroOrientacao}.getDataOrientacao()
    :
        null]]></groupExpression>
		<groupHeader>
			<band height="38">
				<rectangle radius="10">
					<reportElement uuid="73faf3b9-98d3-4f90-8444-6397ec746eb8" x="0" y="1" width="535" height="18"/>
				</rectangle>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="8198b390-2f93-4bdc-b681-4292a3168041" key="textField-4" x="0" y="26" width="47" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement uuid="a479e575-158d-42f3-b072-532f91ad1a8c" x="0" y="37" width="535" height="1"/>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="b0047bd8-3042-44e7-a53f-da08f67a75fa" key="textField-4" x="52" y="26" width="203" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_profissional_orientador")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="e70df8d4-aa94-445b-aa29-1d366a6bd66f" key="textField-4" mode="Transparent" x="0" y="4" width="535" height="13"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[FormaApresentacao.PROFISSIONAL.equals($P{formaApresentacao}) ?
    Bundle.getStringApplication("rotulo_profissional_orientador")+": "+$F{profissional}.getNome()
:
    FormaApresentacao.DATA_ORIENTACAO.equals($P{formaApresentacao}) ?
        Bundle.getStringApplication("rotulo_data_orientacao")+": "+Data.formatar($F{registroOrientacao}.getDataOrientacao())
    :
        null]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="773836b7-769a-4fe3-8501-610f06fa1636" key="textField-4" x="258" y="26" width="225" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_orientado")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="773836b7-769a-4fe3-8501-610f06fa1636" key="textField-4" x="483" y="26" width="52" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quantidade")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="21">
				<line>
					<reportElement uuid="be452fc1-001d-4f2d-abaf-15a74dfa7df1" x="455" y="3" width="80" height="1"/>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="448a6f58-35c7-4136-ad23-b95264fe5d63" key="textField-4" x="392" y="5" width="85" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="5d3b4b49-f9c4-4578-9b35-9195e1b80701" key="textField-4" x="478" y="5" width="57" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalQuantidadeFA}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="16" splitType="Stretch">
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement uuid="8147e3ad-7d5c-4cb4-8fd9-92a0bc871830" x="0" y="2" width="47" height="12"/>
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{registroOrientacao}.getDataOrientacao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement uuid="ad0b9976-66c1-4e01-8e6a-c4f03ecfbbb3" x="52" y="2" width="203" height="12"/>
				<textElement>
					<font fontName="Arial" size="9" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{profissional}.getNome()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement uuid="2be8f044-6339-4dce-82e3-d2c6d8ea01d2" x="258" y="2" width="225" height="12"/>
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{registroOrientacao}.getDescricaoOrientado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement uuid="2be8f044-6339-4dce-82e3-d2c6d8ea01d2" x="483" y="2" width="52" height="12"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidade}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
