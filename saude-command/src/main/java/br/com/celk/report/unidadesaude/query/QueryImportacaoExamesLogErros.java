/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.celk.report.unidadesaude.query;

import br.com.celk.importacaoExame.dto.ImportacaoExameLacenDTO;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.prontuario.basico.ImportacaoExame;
import br.com.ksisolucoes.vo.prontuario.basico.ImportacaoExameLogErro;
import ch.lambdaj.Lambda;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryImportacaoExamesLogErros extends CommandQuery<ImportacaoExameLacenDTO> {

    private ImportacaoExame param;
    private List<ImportacaoExameLogErro> logsErros;

    public QueryImportacaoExamesLogErros(ImportacaoExameLacenDTO param) {
        ImportacaoExame importacaoExame = new ImportacaoExame();
        importacaoExame.setCodigo(param.getCodigo());
        this.param = importacaoExame;
    }

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.setTypeSelect(ImportacaoExameLogErro.class.getName());

        hql.addToSelect("importacaoExameLogErro.descricao", "descricao");

        hql.addToFrom("ImportacaoExameLogErro importacaoExameLogErro");

        hql.addToWhereWhithAnd("importacaoExameLogErro.importacaoExame = ", this.param);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.logsErros = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    public List<String> getImportacoesExamesLogDTO() {
        return Lambda.extract(logsErros, Lambda.on(ImportacaoExameLogErro.class).getDescricao());
    }
}
