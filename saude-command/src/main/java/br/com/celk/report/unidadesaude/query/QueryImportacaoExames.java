/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.celk.report.unidadesaude.query;

import br.com.celk.importacaoExame.dto.ImportacaoExameLacenDTO;
import br.com.celk.unidadesaude.esus.relatorios.dto.RelatorioErrosImportacaoExamesExternosDTOParam;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.hibernate.Query;
import org.hibernate.Session;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryImportacaoExames extends CommandQuery<RelatorioErrosImportacaoExamesExternosDTOParam> {

    private RelatorioErrosImportacaoExamesExternosDTOParam param;
    private List<ImportacaoExameLacenDTO> importacaoExameLacenDTOS;

    public QueryImportacaoExames(RelatorioErrosImportacaoExamesExternosDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(ImportacaoExameLacenDTO.class.getName());

        hql.addToSelect("importacaoExame.codigo", "codigo");
        hql.addToSelect("importacaoExame.nomePaciente", "paciente");
        hql.addToSelect("importacaoExame.cns", "cns");
        hql.addToSelect("importacaoExame.municipioResidencia", "municipioResidencia");
        hql.addToSelect("importacaoExame.ufResidencia", "ufResidencia");
        hql.addToSelect("importacaoExame.descricaoExame", "exame");
        hql.addToSelect("importacaoExame.dataLiberacao", "dataLiberacao");
        hql.addToSelect("importacaoExame.resultado", "resultado");
        hql.addToSelect("importacaoExame.nomeUsuario", "nomeUsuario");

        hql.addToFrom("ImportacaoExame importacaoExame");

        hql.addToOrder("importacaoExame.dataLiberacao desc");

        if (param.getPeriodo() != null) {
            hql.addToWhereWhithAnd("(importacaoExame.dataImportacao >= :dataInicial and importacaoExame.dataImportacao <= :dataFinal)");
        }

    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {

        for (ImportacaoExameLacenDTO importacaoExameLacenDTO : importacaoExameLacenDTOS) {
            QueryImportacaoExamesLogErros queryImportacaoExamesLogErros = new QueryImportacaoExamesLogErros(importacaoExameLacenDTO);
            queryImportacaoExamesLogErros.start();
            importacaoExameLacenDTO.setInconsistencias(queryImportacaoExamesLogErros.getImportacoesExamesLogDTO());
        }
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) {
        if (param.getPeriodo() != null) {
            query.setParameter("dataInicial", param.getPeriodo().getDataInicial());
            query.setParameter("dataFinal", param.getPeriodo().getDataFinal());
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.importacaoExameLacenDTOS = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    public List<ImportacaoExameLacenDTO> getImportacoesExamesLogDTO() {
        return importacaoExameLacenDTOS;
    }
}
