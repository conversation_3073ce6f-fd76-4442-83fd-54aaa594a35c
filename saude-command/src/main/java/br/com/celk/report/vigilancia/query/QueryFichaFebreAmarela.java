package br.com.celk.report.vigilancia.query;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.basico.Pais;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoFebreAmarela;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoFebreAmarelaDeslocamento;
import org.hibernate.Session;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class QueryFichaFebreAmarela extends QueryFichaInvestigacaoBase {

    @Override
    protected Class getClasseFichaInvestigacao() {
        return InvestigacaoAgravoFebreAmarela.class;
    }

    @Override
    protected Map<String, String> getCamposInvestigacaoAgravo() {
        Map<String, String> campos = new HashMap<>();

        //OCUPACAO
        campos.put("investigacaoAgravo.codigo", "investigacaoAgravo_codigo");
        campos.put(formatarData("investigacaoAgravo.dataInvestigacao"), "_31_data_investigacao");
        campos.put("ocupacaoCbo.descricao", "_32_ocupacao");

        //ANTECEDENTES EPIDEMIOLÓGICOS
        campos.put("investigacaoAgravo.ocorrenciaEpizootias", "_33_ocorrencia_epizootias");
        campos.put("investigacaoAgravo.isolamentoVirusMosquito", "_33_isolamento_virus_mosquito");
        campos.put("investigacaoAgravo.aedesAegyptiUrbano", "_33_aedes_aegypti_urbano");
        campos.put("investigacaoAgravo.vacinadoFrebreAmarelaSN", "_34_vacinado_febre_amarela");
        campos.put(formatarData("investigacaoAgravo.dataVacinacao"), "_35_data_vacinacao");
        campos.put("estadoUnidadeSaudeVacinacao.sigla", "_36_estado_unidade_saude_vacinacao");
        campos.put("cidadeUnidadeSaudeVacinacao.descricao", "_37_cidade_unidade_saude_vacinacao");
        campos.put("cidadeUnidadeSaudeVacinacao.codigo", "_37_cidade_unidade_saude_vacinacao_codigo");
        campos.put("unidadeSaudeVacinacao.descricao", "_38_unidade_saude_vacinacao");
        campos.put("unidadeSaudeVacinacao.codigo", "_38_unidade_saude_vacinacao_codigo");

        //DADOS CLÍNICOS
        campos.put("investigacaoAgravo.sinalSintomaDorAbdominal", "_39_dor_abdominal");
        campos.put("investigacaoAgravo.sinalSintomaSinalFaget", "_39_sinal_faget");
        campos.put("investigacaoAgravo.sinalSintomaSinalHemorragico", "_39_sinais_hemorragicos");
        campos.put("investigacaoAgravo.sinalSintomaDisturbioExcrecaoRenal", "_39_disturbio_excrecao_renal");

        //ATENDIMENTO
        campos.put("investigacaoAgravo.hospitalizacao", "_40_ocorreu_hospitalizacao");
        campos.put(formatarData("investigacaoAgravo.dataInternacao"), "_41_data_internacao");
        campos.put("estadohospitalHospitalizacao.sigla", "_42_uf_hospitalizacao");
        campos.put("cidadehospitalHospitalizacao.descricao", "_43_municipio_hospitalizacao");
        campos.put("cidadehospitalHospitalizacao.codigo", "_43_ibge_hospitalizacao");
        campos.put("hospitalHospitalizacao.descricao", "_44_unidade_saude_hospitalizacao");
        campos.put("hospitalHospitalizacao.codigo", "_44_unidade_saude_hospitalizacao_codigo");

        //DADOS DO LABORATÓRIO
        campos.put("investigacaoAgravo.exameBilirrubinaTotal", "_45_bilirrubina_total_exame");
        campos.put("investigacaoAgravo.exameBilirrubinaDireta", "_45_bilirrubina_direita_exame");
        campos.put("investigacaoAgravo.exameAstTgo", "_45_ast_exame");
        campos.put("investigacaoAgravo.exameAltTgp", "_45_alt_exame");

        //DADOS LABORATORIAIS
        //exame sorológico igm
        campos.put(formatarData("investigacaoAgravo.exameSoroDataPrimeiraAmostra"), "_46_primeira_amostra_soro_data_coleta");
        campos.put("investigacaoAgravo.exameSoroResultadoPrimeiraAmostra", "_47_primeira_amostra_soro_resultado");
        campos.put(formatarData("investigacaoAgravo.exameSoroDataSegundaAmostra"), "_48_segunda_amostra_soro_data_coleta");
        campos.put("investigacaoAgravo.exameSoroResultadoSegundaAmostra", "_49_segunda_amostra_soro_resultado");

        //isolamento viral
        campos.put("investigacaoAgravo.isolamentoViralMaterialColetado", "_50_material_coletado_isolamento");
        campos.put(formatarData("investigacaoAgravo.isolamentoViralDataColetada"), "_51_data_coleta_isolamento");
        campos.put("investigacaoAgravo.isolamentoViralResultado", "_52_resultado_isolamento");

        //histopatologia
        campos.put("investigacaoAgravo.histopatologiaResultado", "_53_resultado_histopatologia");
        //imunohistoquimica
        campos.put("investigacaoAgravo.imunohistoquimicaResultado", "_54_resultado_imunohistoquimica");
        //RT-PCR
        campos.put(formatarData("investigacaoAgravo.rtPcrDataColeta"), "_55_data_coleta_rtpcr");
        campos.put("investigacaoAgravo.rtPcrResultado", "_56_resultado_rtpcr");

        //CONCLUSAO
        campos.put("investigacaoAgravo.classificacaoFinal", "_57_classificacao_final");
        campos.put("investigacaoAgravo.classificacaoFinalEspecificacao", "_57_classificao_final_especificacao");
        campos.put("investigacaoAgravo.criterioConfirmacaoDescarte", "_58_criteiro_confirmacao_descarte");
        campos.put("investigacaoAgravo.casoAutoctone", "_59_caso_autoctone");
        campos.put("estadoLocalInfeccao.sigla", "_60_estado_local_infeccao");
        campos.put("paisLocalInfeccao.descricao", "_61_pais_local_infeccao");
        campos.put("cidadeLocalInfeccao.descricao", "_62_cidade_local_infeccao");
        campos.put("cidadeLocalInfeccao.codigo", "_62_codigo_ibge");
        campos.put("investigacaoAgravo.distritoLocalInfeccao", "_63_distrito");
        campos.put("investigacaoAgravo.bairroLocalInfeccao", "_64_bairro_local_infeccao");
        campos.put("investigacaoAgravo.localidadeLocalInfeccao", "_65_localidade");
        campos.put("investigacaoAgravo.doencaRelacionadaTrabalho", "_66_doenca_relacionada_trabalho");
        campos.put("investigacaoAgravo.atividadeDesenvolvidaLocalInfeccao", "_67_atividade_desenvolvida_local_infeccao");
        campos.put("investigacaoAgravo.evolucaoCaso", "_68_evolucao_caso");
        campos.put(formatarData("investigacaoAgravo.dataObito"), "_69_data_obito");
        campos.put(formatarData("investigacaoAgravo.dataEncerramento"), "_70_data_encerramento");
        campos.put("investigacaoAgravo.observacao", "observacoes");

        return campos;
    }

    @Override
    protected String getJuncoesFicha() {
        return "left join investigacaoAgravo.ocupacaoCbo ocupacaoCbo "
                + "left join investigacaoAgravo.hospital hospitalHospitalizacao "
                + "left join hospitalHospitalizacao.cidade cidadehospitalHospitalizacao "
                + "left join cidadehospitalHospitalizacao.estado estadohospitalHospitalizacao "
                + "left join investigacaoAgravo.cidadeLocalInfeccao cidadeLocalInfeccao "
                + "left join cidadeLocalInfeccao.estado estadoLocalInfeccao "
                + "left join investigacaoAgravo.paisLocalInfeccao paisLocalInfeccao "
                + "left join investigacaoAgravo.unidadeSaude unidadeSaudeVacinacao "
                + "left join unidadeSaudeVacinacao.cidade cidadeUnidadeSaudeVacinacao "
                + "left join cidadeUnidadeSaudeVacinacao.estado estadoUnidadeSaudeVacinacao ";
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        super.customProcess(session);
        for (Map<String, Object> map : getResult()) {
            addListaInformacoesComplementares(map);
        }
    }

    private void addListaInformacoesComplementares(Map<String, Object> map) {
        int i;

        Long codigoFebreAmarela = (Long) map.get("investigacaoAgravo_codigo");

        List<InvestigacaoAgravoFebreAmarelaDeslocamento> deslocamentoList =
                LoadManager.getInstance(InvestigacaoAgravoFebreAmarelaDeslocamento.class)
                        .addProperties(new HQLProperties(InvestigacaoAgravoFebreAmarelaDeslocamento.class).getProperties())
                        .addProperty(VOUtils.montarPath(InvestigacaoAgravoFebreAmarelaDeslocamento.PROP_CIDADE_DESLOCAMENTO, Cidade.PROP_ESTADO, Estado.PROP_SIGLA))
                        .addProperty(VOUtils.montarPath(InvestigacaoAgravoFebreAmarelaDeslocamento.PROP_PAIS_DESLOCAMENTO, Pais.PROP_DESCRICAO))
                        .addParameter(new QueryCustom.QueryCustomParameter(
                                VOUtils.montarPath(InvestigacaoAgravoFebreAmarelaDeslocamento.PROP_INVESTIGACAO_AGRAVO_FEBRE_AMARELA, InvestigacaoAgravoFebreAmarela.PROP_CODIGO),
                                codigoFebreAmarela))
                        .start().getList();

        for (i = 0; i < deslocamentoList.size(); i++) {
            InvestigacaoAgravoFebreAmarelaDeslocamento febreAmarelaDeslocamento = deslocamentoList.get(i);

            map.put("_data_deslocamento" + i, Data.formatar(febreAmarelaDeslocamento.getDataDeslocamento()));
            map.put("_uf_deslocamento" + i, febreAmarelaDeslocamento.getCidadeDeslocamento().getEstado().getSigla());
            map.put("_municipio_deslocamento" + i, febreAmarelaDeslocamento.getCidadeDeslocamento().getDescricao());
            map.put("_pais_deslocamento" + i, febreAmarelaDeslocamento.getPaisDeslocamento().getDescricao());
            map.put("_meio_transporte_deslocamento"+i, febreAmarelaDeslocamento.getMeioTransporte());

            i++;
        }
    }
}