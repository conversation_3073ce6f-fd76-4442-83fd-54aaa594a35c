package br.com.celk.report.vigilancia.query;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoBotulismo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoBotulismoAlimentos;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoParalisiaFlacidaAguda;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoParalisiaFlacidaAgudaLiquor;
import org.hibernate.Session;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class QueryFichaParalisiaFlacidaAguda extends QueryFichaInvestigacaoBase {

    @Override
    protected Class getClasseFichaInvestigacao() { return InvestigacaoAgravoParalisiaFlacidaAguda.class; }

    @Override
    protected Map<String, String> getCamposInvestigacaoAgravo() {
        Map<String, String> campos = new HashMap<>();

        campos.put(formatarData("investigacaoAgravo.dataInvestigacao"), "_32_data_investigacao");

        campos.put(formatarData("investigacaoAgravo.dt1Consulta"), "_31_data_1_consulta");
        campos.put("investigacaoAgravo.tomouVacinaPoliomelite", "_33_tomou_vacina_poliomelite");
        campos.put("investigacaoAgravo.numeroDose", "_34_num_dose");
        campos.put(formatarData("investigacaoAgravo.dtUltimaDose"), "_35_data_ultima_dose");
        campos.put("investigacaoAgravo.viajouRecebeuVisitas", "_36_viajou_recebeu_visitas");
        campos.put("pais.descricao", "_37_pais");

        campos.put("investigacaoAgravo.sinaisSintomasFebre", "_38_sinais_sintomas_febre");
        campos.put("investigacaoAgravo.sinaisSintomasVomitos", "_38_sinais_sintomas_vomito");
        campos.put("investigacaoAgravo.sinaisSintomasDiarreia", "_38_sinais_sintomas_diarreia");
        campos.put("investigacaoAgravo.sinaisSintomasObstipacao", "_38_sinais_sintomas_obstipacao");
        campos.put("investigacaoAgravo.sinaisSintomasDoresMusculares", "_38_sinais_sintomas_dores_musc");
        campos.put("investigacaoAgravo.sinaisSintomasCefaleia", "_38_sinais_sintomas_cefaleia");
        campos.put("investigacaoAgravo.sinaisSintomasSintomasResp", "_38_sinais_sintomas_sintomas_resp");
        campos.put("investigacaoAgravo.sinaisSintomasOutros", "_38_sinais_sintomas_outros");

        campos.put(formatarData("investigacaoAgravo.dtInicioDefMotora"), "_39_data_inicio_def_motora");

        campos.put("investigacaoAgravo.deficienciaMotoraAguda", "_40_def_motora_aguda");
        campos.put("investigacaoAgravo.deficienciaMotoraFlacida", "_40_def_motora_flacida");
        campos.put("investigacaoAgravo.deficienciaMotoraAssimetrica", "_40_def_motora_assimetrica");
        campos.put("investigacaoAgravo.deficienciaMotoraProgressao3Dias", "_40_def_motora_progressao_3_dias");
        campos.put("investigacaoAgravo.deficienciaMotoraAscendente", "_40_def_motora_ascendente");
        campos.put("investigacaoAgravo.deficienciaMotoraDescendente", "_40_def_motora_descendente");

        campos.put("investigacaoAgravo.forcaMuscularMie", "_41_forca_muscular_mie");
        campos.put("investigacaoAgravo.forcaMuscularMse", "_41_forca_muscular_mse");
        campos.put("investigacaoAgravo.forcaMuscularMid", "_41_forca_muscular_mid");
        campos.put("investigacaoAgravo.forcaMuscularMsd", "_41_forca_muscular_msd");

        campos.put("investigacaoAgravo.localizacaoMie", "_42_localizacao_mie");
        campos.put("investigacaoAgravo.localizacaoMse", "_42_localizacao_mse");
        campos.put("investigacaoAgravo.localizacaoMid", "_42_localizacao_mid");
        campos.put("investigacaoAgravo.localizacaoMsd", "_42_localizacao_msd");

        campos.put("investigacaoAgravo.comprometimentoMuscRespiratoria", "_43_comprometimento_musc_resp");
        campos.put("investigacaoAgravo.comprometimentoMuscCervical", "_43_comprometimento_musc_cervical");
        campos.put("investigacaoAgravo.comprometimentoFace", "_43_comprometimento_face");

        campos.put(formatarData("investigacaoAgravo.exameAgudaDtExame"), "_44_data_exame_aguda");

        campos.put("investigacaoAgravo.forcaMuscularAgudaMie", "_45_forca_muscular_aguda_mie");
        campos.put("investigacaoAgravo.forcaMuscularAgudaMse", "_45_forca_muscular_aguda_mse");
        campos.put("investigacaoAgravo.forcaMuscularAgudaMid", "_45_forca_muscular_aguda_mid");
        campos.put("investigacaoAgravo.forcaMuscularAgudaMsd", "_45_forca_muscular_aguda_msd");

        campos.put("investigacaoAgravo.tonusMuscularMie", "_46_tonus_musc_mie");
        campos.put("investigacaoAgravo.tonusMuscularMse", "_46_tonus_musc_mse");
        campos.put("investigacaoAgravo.tonusMuscularMid", "_46_tonus_musc_mid");
        campos.put("investigacaoAgravo.tonusMuscularMsd", "_46_tonus_musc_msd");
        campos.put("investigacaoAgravo.tonusMuscularCervical", "_46_tonus_musc_cervical");
        campos.put("investigacaoAgravo.tonusMuscularFace", "_46_tonus_musc_face");

        campos.put("investigacaoAgravo.sensibilidadeMie", "_47_sensibilidade_mie");
        campos.put("investigacaoAgravo.sensibilidadeMse", "_47_sensibilidade_mse");
        campos.put("investigacaoAgravo.sensibilidadeMid", "_47_sensibilidade_mid");
        campos.put("investigacaoAgravo.sensibilidadeMsd", "_47_sensibilidade_msd");
        campos.put("investigacaoAgravo.sensibilidadeFace", "_47_sensibilidade_face");

        campos.put("investigacaoAgravo.reflexosAquileuE", "_48_reflexos_aquileu_e");
        campos.put("investigacaoAgravo.reflexosAquileuD", "_48_reflexos_aquileu_d");
        campos.put("investigacaoAgravo.reflexosPatelarE", "_48_reflexos_patelar_e");
        campos.put("investigacaoAgravo.reflexosPatelarD", "_48_reflexos_patelar_d");
        campos.put("investigacaoAgravo.reflexosBicipalE", "_48_reflexos_bicipal_e");
        campos.put("investigacaoAgravo.reflexosBicipalD", "_48_reflexos_bicipal_d");
        campos.put("investigacaoAgravo.reflexosTricipitalE", "_48_reflexos_tricipital_e");
        campos.put("investigacaoAgravo.reflexosTricipitalD", "_48_reflexos_tricipital_d");

        campos.put("investigacaoAgravo.reflexoCutaneoPlantarFlexaoE", "_49_reflexos_plantar_flexao_e");
        campos.put("investigacaoAgravo.reflexoCutaneoPlantarFlexaoD", "_49_reflexos_plantar_flexao_d");
        campos.put("investigacaoAgravo.reflexoCutaneoPlantarExtensaoE", "_49_reflexos_plantar_extensao_e");
        campos.put("investigacaoAgravo.reflexoCutaneoPlantarExtensaoD", "_49_reflexos_plantar_extensao_d");

        campos.put("investigacaoAgravo.sinaisIrritacaoMeningeaKerning", "_50_sinais_irritacao_meningea_kerning");
        campos.put("investigacaoAgravo.sinaisIrritacaoMeningeaRigidezNuca", "_50_sinais_irritacao_meningea_rigidez_nuca");
        campos.put("investigacaoAgravo.sinaisIrritacaoMeningeaBrudzinski", "_50_sinais_irritacao_meningea_brudzinski");

        campos.put("investigacaoAgravo.contatoIngestaoSubstancia", "_51_contato_ingestao_substancia");
        campos.put("investigacaoAgravo.contatoIngestaoSubstanciaEspecifique", "_52_contato_ingestao_substancia_desc");
        campos.put("investigacaoAgravo.historiaInjecaoIntramuscular", "_53_historia_injecao_intramuscular");
        campos.put("investigacaoAgravo.localAplicacao", "_54_local_aplicacao");

        campos.put("hipoteseDiagnosticaCid.codigo", "_55_hipotese_diagnostica_cid");
        campos.put("investigacaoAgravo.hospitalizacao", "_56_hospitalizacao");
        campos.put(formatarData("investigacaoAgravo.dataInternacao"), "_57_data_internacao");
        campos.put("cidade.estado.sigla", "_58_estado");
        campos.put("hospital.cidade.descricao", "_59_cidade");
        campos.put("hospital.cidade.codigo", "_59_ibge");
        campos.put("investigacaoAgravo.hospital.descricao", "_59_hospital");

        campos.put(formatarData("investigacaoAgravo.dtColeta"), "_60_data_coleta");
        campos.put(formatarData("investigacaoAgravo.dtEnvioLocalEstadual"), "_61_data_envio_local_estadual");
        campos.put(formatarData("investigacaoAgravo.dtEnvioEstadualLrr"), "_62_data_envio_estadual_lrr");
        campos.put(formatarData("investigacaoAgravo.dtRecebimentoLrr"), "_63_data_recebimento_lrr");

        campos.put("investigacaoAgravo.quantidade", "_64_quantidade");
        campos.put("investigacaoAgravo.condicoes", "_65_condicoes");
        campos.put(formatarData("investigacaoAgravo.dtResultado"), "_66_data_resultado");
        campos.put("investigacaoAgravo.resultado", "_67_resultado");

        campos.put(formatarData("investigacaoAgravo.dtRealizacao"), "_69_data_realizacao");
        campos.put("diagosticoSugestivo.codigo", "_70_diagostico_sugestivo");

        campos.put("investigacaoAgravo.coletadoMaterialCerebro", "_71_coletado_material_cerebro");
        campos.put("investigacaoAgravo.coletadoMaterialMedula", "_71_coletado_material_medula");
        campos.put("investigacaoAgravo.coletadoMaterialIntestino", "_71_coletado_material_intestino");

        campos.put(formatarData("investigacaoAgravo.dtColetaMaterial"), "_72_data_coleta");
        campos.put("investigacaoAgravo.resultadoMaterial", "_73_resultado_material");

        campos.put(formatarData("investigacaoAgravo.dtRevisita"), "_74_data_revista");

        campos.put("investigacaoAgravo.forcaMuscularRevisitaMie", "_75_forca_muscular_mie");
        campos.put("investigacaoAgravo.forcaMuscularRevisitaMse", "_75_forca_muscular_mse");
        campos.put("investigacaoAgravo.forcaMuscularRevisitaMid", "_75_forca_muscular_mid");
        campos.put("investigacaoAgravo.forcaMuscularRevisitaMsd", "_75_forca_muscular_msd");

        campos.put("investigacaoAgravo.tonusMuscularRevisitaMie", "_76_tonus_musc_mie");
        campos.put("investigacaoAgravo.tonusMuscularRevisitaMse", "_76_tonus_musc_mse");
        campos.put("investigacaoAgravo.tonusMuscularRevisitaMid", "_76_tonus_musc_mid");
        campos.put("investigacaoAgravo.tonusMuscularRevisitaMsd", "_76_tonus_musc_msd");
        campos.put("investigacaoAgravo.tonusMuscularRevisitaMuscCervical", "_76_tonus_musc_cervical");
        campos.put("investigacaoAgravo.tonusMuscularRevisitaFace", "_76_tonus_musc_face");

        campos.put("investigacaoAgravo.reflexosRevisitaAquileuE", "_77_reflexos_aquileu_e");
        campos.put("investigacaoAgravo.reflexosRevisitaAquileuD", "_77_reflexos_aquileu_d");
        campos.put("investigacaoAgravo.reflexosRevisitaPatelarE", "_77_reflexos_patelar_e");
        campos.put("investigacaoAgravo.reflexosRevisitaPatelarD", "_77_reflexos_patelar_d");
        campos.put("investigacaoAgravo.reflexosRevisitaBicipalE", "_77_reflexos_bicipal_e");
        campos.put("investigacaoAgravo.reflexosRevisitaBicipalD", "_77_reflexos_bicipal_d");
        campos.put("investigacaoAgravo.reflexosRevisitaTricipitalE", "_77_reflexos_tricipital_e");
        campos.put("investigacaoAgravo.reflexosRevisitaTricipitalD", "_77_reflexos_tricipital_d");

        campos.put("investigacaoAgravo.reflexoRevisitaCutaneoPlantarFlexaoE", "_78_reflexos_plantar_flexao_e");
        campos.put("investigacaoAgravo.reflexoRevisitaCutaneoPlantarFlexaoD", "_78_reflexos_plantar_flexao_d");
        campos.put("investigacaoAgravo.reflexoRevisitaCutaneoPlantarExtensaoE", "_78_reflexos_plantar_extensao_e");
        campos.put("investigacaoAgravo.reflexoRevisitaCutaneoPlantarExtensaoD", "_78_reflexos_plantar_extensao_d");

        campos.put("investigacaoAgravo.atrofiaMie", "_79_atrofia_mie");
        campos.put("investigacaoAgravo.atrofiaMse", "_79_atrofia_mse");
        campos.put("investigacaoAgravo.atrofiaMid", "_79_atrofia_mid");
        campos.put("investigacaoAgravo.atrofiaMsd", "_79_atrofia_msd");

        campos.put("investigacaoAgravo.sensibilidadeRevisitaMie", "_80_sensibilidade_mie");
        campos.put("investigacaoAgravo.sensibilidadeRevisitaMse", "_80_sensibilidade_mse");
        campos.put("investigacaoAgravo.sensibilidadeRevisitaMid", "_80_sensibilidade_mid");
        campos.put("investigacaoAgravo.sensibilidadeRevisitaMsd", "_80_sensibilidade_msd");
        campos.put("investigacaoAgravo.sensibilidadeRevisitaFace", "_80_sensibilidade_face");

        campos.put(formatarData("investigacaoAgravo.dataRevisao"), "_81_data_revisao");
        campos.put("investigacaoAgravo.classificacaoFinal", "_82_classificacao_final");
        campos.put("investigacaoAgravo.criterioClassificacao", "_83_criterio_classificacao");
        campos.put("diagosticoCasoDescartado.codigo", "_84_diagostico_descartado");
        campos.put("investigacaoAgravo.evolucaoCaso", "_85_evolucao_caso");
        campos.put(formatarData("investigacaoAgravo.dataObito"), "_86_data_obito");

        campos.put("investigacaoAgravo.observacao", "_observacao");

        return campos;
    }

    @Override
    protected String getJuncoesFicha() {
        return "left join investigacaoAgravo.ocupacaoCbo ocupacaoCbo "
                + "left join investigacaoAgravo.cdPaisOrigem pais "
                + "left join investigacaoAgravo.hospital hospital "
                + "left join hospital.cidade cidade "
                + "left join cidade.estado estado "
                + "left join investigacaoAgravo.hipoteseDiagnosticaCid hipoteseDiagnosticaCid "
                + "left join investigacaoAgravo.diagosticoSugestivo diagosticoSugestivo "
                + "left join investigacaoAgravo.diagosticoCasoDescartado diagosticoCasoDescartado ";
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        super.customProcess(session);
        for (Map<String, Object> map : getResult()) {
            addTabelaCBO(map);
            addListaLiquor(map);
        }
    }

    private void addTabelaCBO(Map<String, Object> map) {
        TabelaCbo cbo = (TabelaCbo) map.get("ocupacaoCbo.descricao");
        if (cbo != null) {
            map.put("ocupacaoCbo.descricao", cbo.getDescricaoFormatado());
        }
    }

    private void addListaLiquor(Map<String, Object> map) {
        int i;
        Long codigoInvestigacaoAgravoParalisiaFlacidaAguda= (Long) map.get("investigacaoAgravo_codigo");

        List<InvestigacaoAgravoParalisiaFlacidaAgudaLiquor> liquorList =
                LoadManager.getInstance(InvestigacaoAgravoParalisiaFlacidaAgudaLiquor.class)
                        .addProperties(new HQLProperties(InvestigacaoAgravoParalisiaFlacidaAgudaLiquor.class).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(
                                VOUtils.montarPath(InvestigacaoAgravoParalisiaFlacidaAgudaLiquor.PROP_INVESTIGACAO_AGRAVO_PARALISIA_FLACIDA_AGUDA, InvestigacaoAgravoParalisiaFlacidaAguda.PROP_CODIGO),
                                codigoInvestigacaoAgravoParalisiaFlacidaAguda))
                        .start().getList();

        for (i = 0; i < liquorList.size(); i++) {
            InvestigacaoAgravoParalisiaFlacidaAgudaLiquor investigacaoAgravoParalisiaFlacidaAgudaLiquor = liquorList.get(i);

            map.put("_data_coleta" + i, investigacaoAgravoParalisiaFlacidaAgudaLiquor.getDataColeta());
            map.put("_num_celulas" + i, investigacaoAgravoParalisiaFlacidaAgudaLiquor.getNumCelulas());
            map.put("_linfocitos" + i, investigacaoAgravoParalisiaFlacidaAgudaLiquor.getLinfocitos());
            map.put("_proteinas" + i, investigacaoAgravoParalisiaFlacidaAgudaLiquor.getProteinas());
            map.put("_glicose" + i, investigacaoAgravoParalisiaFlacidaAgudaLiquor.getGlicose());
            map.put("_cloreto" + i, investigacaoAgravoParalisiaFlacidaAgudaLiquor.getCloreto());

            i++;
        }
    }
}
