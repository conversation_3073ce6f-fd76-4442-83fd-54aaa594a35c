package br.com.celk.report.vigilancia.query;

import br.com.celk.report.vigilancia.dto.RelatorioDenunciasDTO;
import br.com.celk.report.vigilancia.dto.RelatorioDenunciasDTOParam;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Projections;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.EloRequerimentoVigilanciaSetorVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaFiscal;
import br.com.ksisolucoes.vo.vigilancia.SetorVigilancia;
import ch.lambdaj.Lambda;
import org.hibernate.Session;
import org.hibernate.criterion.Restrictions;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioDenuncias extends CommandQuery<QueryRelatorioDenuncias> implements ITransferDataReport<RelatorioDenunciasDTOParam, RelatorioDenunciasDTO> {

    private RelatorioDenunciasDTOParam param;
    private List<RelatorioDenunciasDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("denuncia.dataCadastro ", "data");

        if (!RelatorioDenunciasDTOParam.FormaApresentacao.GERAL.equals(param.getFormaApresentacao())) {
            if (RelatorioDenunciasDTOParam.FormaApresentacao.DATA_REGISTRO.equals(param.getFormaApresentacao())) {
                hql.addToOrder("1");
            }
        }

        hql.addToSelect("tipoDenuncia.codigo", "denuncia.tipoDenuncia.codigo");
        hql.addToSelect("tipoDenuncia.descricao", "denuncia.tipoDenuncia.descricao");

        hql.addToSelect("requerimentoVigilancia.codigo", "requerimentoVigilancia.codigo");
        hql.addToSelect("requerimentoVigilancia.protocolo", "requerimentoVigilancia.protocolo");

        hql.addToSelect("denuncia.codigo", "denuncia.codigo");
        hql.addToSelect("denuncia.status", "denuncia.status");
        hql.addToSelect("denuncia.observacao", "denuncia.observacao");
        hql.addToSelect("denuncia.flagAnonimo", "denuncia.flagAnonimo");

        hql.addToSelect("denuncia.denunciante", "denuncia.denunciante");
        hql.addToSelect("denuncia.emailDenunciante", "denuncia.emailDenunciante");
        hql.addToSelect("denuncia.cnpjCpfDenunciante", "denuncia.cnpjCpfDenunciante");
        hql.addToSelect("denuncia.logradouroDenunciante", "denuncia.logradouroDenunciante");
        hql.addToSelect("denuncia.complementoLogradouroDenunciante", "denuncia.complementoLogradouroDenunciante");
        hql.addToSelect("denuncia.numeroLogradouroDenunciante", "denuncia.numeroLogradouroDenunciante");
        hql.addToSelect("denuncia.pontoReferencia", "denuncia.pontoReferencia");

        hql.addToSelect("denuncia.denunciado", "denuncia.denunciado");
        hql.addToSelect("denuncia.logradouroDenunciado", "denuncia.logradouroDenunciado");
        hql.addToSelect("denuncia.complementoLogradouroDenunciado", "denuncia.complementoLogradouroDenunciado");
        hql.addToSelect("denuncia.numeroLogradouroDenunciado", "denuncia.numeroLogradouroDenunciado");

        hql.addToSelect("enderecoDenunciado.codigo", "denuncia.enderecoDenunciado.codigo");
        hql.addToSelect("enderecoDenunciado.logradouro", "denuncia.enderecoDenunciado.logradouro");
        hql.addToSelect("enderecoDenunciado.cep", "denuncia.enderecoDenunciado.cep");
        hql.addToSelect("enderecoDenunciado.bairro", "denuncia.enderecoDenunciado.bairro");
        hql.addToSelect("cidadeDenunciado.codigo", "denuncia.enderecoDenunciado.cidade.codigo");
        hql.addToSelect("cidadeDenunciado.descricao", "denuncia.enderecoDenunciado.cidade.descricao");
        hql.addToSelect("estadoDenunciado.codigo", "denuncia.enderecoDenunciado.cidade.estado.codigo");
        hql.addToSelect("estadoDenunciado.sigla", "denuncia.enderecoDenunciado.cidade.estado.sigla");

        hql.setTypeSelect(RelatorioDenunciasDTO.class.getName());

        hql.addToFrom("Denuncia denuncia"
                + " left join denuncia.requerimentoVigilancia requerimentoVigilancia"
                + " left join denuncia.enderecoDenunciado enderecoDenunciado"
                + " left join enderecoDenunciado.cidade cidadeDenunciado"
                + " left join cidadeDenunciado.estado estadoDenunciado"
                + " left join denuncia.enderecoDenunciante enderecoDenunciante"
                + " left join enderecoDenunciante.cidade cidadeDenunciante"
                + " left join cidadeDenunciante.estado estadoDenunciante"
                + " left join denuncia.tipoDenuncia tipoDenuncia");

        setParameters(hql);

        if (RelatorioDenunciasDTOParam.Ordenacao.DENUNCIADO.equals(param.getOrdenacao())) {
            hql.addToOrder("denuncia.denunciado");
        } else if (RelatorioDenunciasDTOParam.Ordenacao.DATA_REGISTRO.equals(param.getOrdenacao())
                && !RelatorioDenunciasDTOParam.FormaApresentacao.DATA_REGISTRO.equals(param.getFormaApresentacao())) {
            hql.addToOrder("1");
        }
    }

    private void setParameters(HQLHelper hql) {
        hql.addToWhereWhithAnd("requerimentoVigilancia.protocolo = ", param.getProtocolo());
        if(param.getProfissional() !=  null) {
            HQLHelper hqlExistsProfissional = hql.getNewInstanceSubQuery();
            hqlExistsProfissional.addToFrom("RequerimentoVigilanciaFiscal requerimentoVigilanciaFiscal" +
                    " left join requerimentoVigilanciaFiscal.requerimentoVigilancia rv" +
                    " left join requerimentoVigilanciaFiscal.profissional prof");
            hqlExistsProfissional.addToWhereWhithAnd("requerimentoVigilancia = rv");
            hqlExistsProfissional.addToWhereWhithAnd("prof = ", param.getProfissional());
            hql.addToWhereWhithAnd("exists (" + hqlExistsProfissional.getQuery() + ")");
        }

        if(param.getSetorVigilancia() != null) {
            HQLHelper hqlExistsSetor = hql.getNewInstanceSubQuery();
            hqlExistsSetor.addToFrom("EloRequerimentoVigilanciaSetorVigilancia elo" +
                    " left join elo.requerimentoVigilancia rv" +
                    " left join elo.setorVigilancia setor");
            hqlExistsSetor.addToWhereWhithAnd("requerimentoVigilancia = rv");
            hqlExistsSetor.addToWhereWhithAnd("setor = ", param.getSetorVigilancia());
            hql.addToWhereWhithAnd("exists (" + hqlExistsSetor.getQuery() + ")");
        }

        hql.addToWhereWhithAnd("denuncia.status = ", param.getSituacao());
        hql.addToWhereWhithAnd("denuncia.tipoDenuncia = ", param.getTipoDenuncia());
        hql.addToWhereWhithAnd("denuncia.dataCadastro ", param.getPeriodo());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("denuncia.denunciado", param.getDenunciado()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("denuncia.denunciante", param.getDenunciante()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("enderecoDenunciado.bairro", param.getBairro()));
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if(CollectionUtils.isNotNullEmpty(this.result)) {
            for (RelatorioDenunciasDTO relatorioDenunciasDTO : this.result) {
                RequerimentoVigilancia requerimentoVigilancia = relatorioDenunciasDTO.getRequerimentoVigilancia();
                List<Profissional> fiscaisRequerimentoList = getFiscaisRequerimentoList(requerimentoVigilancia);
                if (CollectionUtils.isNotNullEmpty(fiscaisRequerimentoList)) {
                    List<String> nomeProfissionalList = Lambda.extract(fiscaisRequerimentoList, Lambda.on(Profissional.class).getNome());
                    String nomeProfissionais = Lambda.join(nomeProfissionalList, ", ");
                    relatorioDenunciasDTO.setProfissionais(nomeProfissionais);
                }

                List<SetorVigilancia> setoresRequerimentoList = getSetoresRequerimentoList(requerimentoVigilancia);
                if (CollectionUtils.isNotNullEmpty(setoresRequerimentoList)) {
                    List<String> descricaoSetoresList = Lambda.extract(setoresRequerimentoList, Lambda.on(SetorVigilancia.class).getDescricao());
                    String descricaoSetores = Lambda.join(descricaoSetoresList, ", ");
                    relatorioDenunciasDTO.setSetores(descricaoSetores);
                }
            }
        }
    }

    private List<Profissional> getFiscaisRequerimentoList(RequerimentoVigilancia requerimentoVigilancia) throws DAOException {
        return getSession().createCriteria(RequerimentoVigilanciaFiscal.class)
                .add(Restrictions.eq(RequerimentoVigilanciaFiscal.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                .setProjection(Projections.property(RequerimentoVigilanciaFiscal.PROP_PROFISSIONAL))
                .list();
    }

    private List<SetorVigilancia> getSetoresRequerimentoList(RequerimentoVigilancia requerimentoVigilancia) throws DAOException {
        return getSession().createCriteria(EloRequerimentoVigilanciaSetorVigilancia.class)
                .add(Restrictions.eq(EloRequerimentoVigilanciaSetorVigilancia.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                .setProjection(Projections.property(EloRequerimentoVigilanciaSetorVigilancia.PROP_SETOR_VIGILANCIA))
                .list();
    }

    @Override
    public List<RelatorioDenunciasDTO> getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List) result);
    }

    @Override
    public void setDTOParam(RelatorioDenunciasDTOParam param) {
        this.param = param;
    }

}
