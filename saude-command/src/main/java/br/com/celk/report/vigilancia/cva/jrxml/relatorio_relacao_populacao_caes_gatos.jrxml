<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_populacao_caes_gatos" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="6593bd24-0947-4e1e-bf45-a8c14dfdbcd2">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.542173157718136"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="FORMA_APRESENTACAO" class="br.com.celk.vigilancia.dto.RelatorioRelacaoPopulacaoCaesGatosDTOParam.FormaApresentacao"/>
	<field name="populacaoCaesGatos" class="br.com.ksisolucoes.vo.vigilancia.PopulacaoCaesGatos"/>
	<field name="populacaoCaesGatosTipoAnimal" class="br.com.ksisolucoes.vo.vigilancia.PopulacaoCaesGatosTipoAnimal"/>
	<field name="quantidade" class="java.lang.Long"/>
	<variable name="totalGeral" class="java.lang.Long" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}]]></variableExpression>
	</variable>
	<variable name="FA" class="br.com.celk.vigilancia.dto.RelatorioRelacaoPopulacaoCaesGatosDTOParam.FormaApresentacao"/>
	<variable name="totalFA" class="java.lang.Long" resetType="Group" resetGroup="FormaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{quantidade}]]></variableExpression>
	</variable>
	<group name="GERAL">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupFooter>
			<band height="12" splitType="Stretch">
				<line>
					<reportElement key="line-4" x="666" y="1" width="136" height="1" uuid="9c8a0b72-ead6-46c3-b629-6eb2b3c9799d"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField>
					<reportElement x="758" y="2" width="43" height="10" uuid="449581f7-48a8-443f-898b-d532f5464b5b"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalGeral} == null ? "0": $V{totalGeral}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="666" y="2" width="90" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="8d8e578a-4b53-44b9-b904-1a3cffcd9b9a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_geral")]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="FormaApresentacao" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[!$V{FA}.GERAL.equals($P{FORMA_APRESENTACAO})
			?
		    	$V{FA}.BAIRRO.equals($P{FORMA_APRESENTACAO})
				?
					$F{populacaoCaesGatos}.getCodigoEndereco().getBairro()
				:
					$V{FA}.TIPO_ANIMAL.equals($P{FORMA_APRESENTACAO})
					?
						$F{populacaoCaesGatosTipoAnimal}.getTipoAnimal()
					:
						$V{FA}.SEXO.equals($P{FORMA_APRESENTACAO})
						?
							$F{populacaoCaesGatosTipoAnimal}.getSexoAnimal()
						:
							null
			:
			   null]]></groupExpression>
		<groupHeader>
			<band height="14" splitType="Stretch">
				<textField evaluationTime="Group" evaluationGroup="FormaApresentacao" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-60" x="0" y="0" width="802" height="14" uuid="5c6578ea-1862-442f-913a-6d475e42a00f"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="true" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[!$V{FA}.GERAL.equals($P{FORMA_APRESENTACAO})
						?
							$V{FA}.BAIRRO.equals($P{FORMA_APRESENTACAO})
							?
								Bundle.getStringApplication("rotulo_bairro") +": "+ $F{populacaoCaesGatos}.getCodigoEndereco().getBairro()
							:
								$V{FA}.TIPO_ANIMAL.equals($P{FORMA_APRESENTACAO})
								?
									Bundle.getStringApplication("rotulo_tipo_animal") +": "+ ($F{populacaoCaesGatosTipoAnimal}.getTipoAnimal() == 0 ? Bundle.getStringApplication("rotulo_cao") : Bundle.getStringApplication("rotulo_gato"))
								:
									$V{FA}.SEXO.equals($P{FORMA_APRESENTACAO})
									?
										Bundle.getStringApplication("sexo") +": "+ ($F{populacaoCaesGatosTipoAnimal}.getSexoAnimal().equals("M") ? Bundle.getStringApplication("rotulo_macho") : Bundle.getStringApplication("rotulo_femea"))
									:
										null
						:
			   				null]]></textFieldExpression>
				</textField>
			</band>
			<band height="11">
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="728" y="1" width="73" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="8d8e578a-4b53-44b9-b904-1a3cffcd9b9a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quantidade")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-4" x="0" y="10" width="802" height="1" uuid="9c8a0b72-ead6-46c3-b629-6eb2b3c9799d"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="1" y="0" width="87" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="8d8e578a-4b53-44b9-b904-1a3cffcd9b9a">
						<printWhenExpression><![CDATA[$V{FA}.GERAL.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_cadastro_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="88" y="0" width="151" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="8d8e578a-4b53-44b9-b904-1a3cffcd9b9a">
						<printWhenExpression><![CDATA[$V{FA}.GERAL.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_profissional")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" positionType="Float" mode="Transparent" x="329" y="0" width="106" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="8d8e578a-4b53-44b9-b904-1a3cffcd9b9a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_tipo_animal")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" positionType="Float" mode="Transparent" x="435" y="0" width="112" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="64e6eacb-c2ed-4e8d-8987-051ea9aaff0e"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("sexo")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" positionType="Float" mode="Transparent" x="239" y="0" width="87" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="3630bab9-47fc-4632-a3cf-41ce7910c26d">
						<printWhenExpression><![CDATA[$V{FA}.GERAL.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_bairro")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="12" splitType="Stretch">
				<line>
					<reportElement key="line-4" x="666" y="1" width="136" height="1" uuid="9c8a0b72-ead6-46c3-b629-6eb2b3c9799d"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField>
					<reportElement x="758" y="2" width="43" height="10" uuid="449581f7-48a8-443f-898b-d532f5464b5b"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalFA} == null ? "0": $V{totalFA}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-35" mode="Transparent" x="666" y="2" width="90" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="8d8e578a-4b53-44b9-b904-1a3cffcd9b9a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="11" splitType="Stretch">
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" mode="Transparent" x="728" y="0" width="73" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="427722c5-fb41-4aa1-a3bd-3d9ea66ec616"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidade}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement key="textField-57" mode="Transparent" x="1" y="0" width="87" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="427722c5-fb41-4aa1-a3bd-3d9ea66ec616"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{populacaoCaesGatos}.getDataCadastro()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" mode="Transparent" x="88" y="0" width="151" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="427722c5-fb41-4aa1-a3bd-3d9ea66ec616"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{populacaoCaesGatos}.getCodigoProfissional().getNome()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" positionType="Float" mode="Transparent" x="329" y="0" width="106" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="427722c5-fb41-4aa1-a3bd-3d9ea66ec616"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{populacaoCaesGatosTipoAnimal}.getTipoAnimal() == 0 ? Bundle.getStringApplication("rotulo_cao") : Bundle.getStringApplication("rotulo_gato")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" positionType="Float" mode="Transparent" x="239" y="0" width="87" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="e1d0c447-a234-4f87-b3e0-940e0175fa82">
					<printWhenExpression><![CDATA[$V{FA}.GERAL.equals($P{FORMA_APRESENTACAO})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{populacaoCaesGatos}.getCodigoEndereco().getBairro()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-57" positionType="Float" mode="Transparent" x="435" y="0" width="112" height="11" forecolor="#000000" backcolor="#FFFFFF" uuid="9b31f0c6-135b-481d-b82c-6ff730a41dbf"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{populacaoCaesGatosTipoAnimal}.getSexoAnimal().equals("M") ? Bundle.getStringApplication("rotulo_macho") : Bundle.getStringApplication("rotulo_femea")]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
