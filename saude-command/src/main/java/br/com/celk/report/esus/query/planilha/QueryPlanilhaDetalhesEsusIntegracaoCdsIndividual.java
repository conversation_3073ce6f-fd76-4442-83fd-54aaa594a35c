package br.com.celk.report.esus.query.planilha;

import br.com.celk.unidadesaude.esus.cds.interfaces.dto.DetalhesItensIntegracaoEsusDTO;
import br.com.celk.unidadesaude.esus.cds.interfaces.dto.DetalhesItensIntegracaoEsusDTOParam;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.esus.EsusIntegracaoCds;
import org.hibernate.Query;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Created by Leonardo.
 */
public class QueryPlanilhaDetalhesEsusIntegracaoCdsIndividual extends CommandQuery<QueryPlanilhaDetalhesEsusIntegracaoCdsIndividual> {

    private DetalhesItensIntegracaoEsusDTOParam param;
    private List<DetalhesItensIntegracaoEsusDTO> list;

    public QueryPlanilhaDetalhesEsusIntegracaoCdsIndividual(DetalhesItensIntegracaoEsusDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(DetalhesItensIntegracaoEsusDTO.class.getName());

        hql.addToSelect("esusIntegracaoCds.codigo", "esusIntegracaoCds.codigo");
        hql.addToSelect("esusIntegracaoCds.uuid", "esusIntegracaoCds.uuid");
        hql.addToSelect("esusIntegracaoCds.descricaoInconsistenciaEsus", "esusIntegracaoCds.descricaoInconsistenciaEsus");

        hql.addToSelect("usuarioCadsus.codigo", "esusIntegracaoCds.esusFichaUsuarioCadsusEsus.usuarioCadsusEsus.usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.referencia", "esusIntegracaoCds.esusFichaUsuarioCadsusEsus.usuarioCadsusEsus.usuarioCadsus.referencia");
        hql.addToSelect("usuarioCadsus.dataNascimento", "esusIntegracaoCds.esusFichaUsuarioCadsusEsus.usuarioCadsusEsus.usuarioCadsus.dataNascimento");
        hql.addToSelect("usuarioCadsus.rg", "esusIntegracaoCds.esusFichaUsuarioCadsusEsus.usuarioCadsusEsus.usuarioCadsus.rg");
        hql.addToSelect("usuarioCadsus.nome", "esusIntegracaoCds.esusFichaUsuarioCadsusEsus.usuarioCadsusEsus.usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.nomeMae", "esusIntegracaoCds.esusFichaUsuarioCadsusEsus.usuarioCadsusEsus.usuarioCadsus.nomeMae");
        hql.addToSelect("usuarioCadsus.situacao", "esusIntegracaoCds.esusFichaUsuarioCadsusEsus.usuarioCadsusEsus.usuarioCadsus.situacao");

        hql.addToSelect("equipeMicroArea.codigo", "esusIntegracaoCds.esusFichaUsuarioCadsusEsus.equipeMicroArea.codigo");
        hql.addToSelect("equipeMicroArea.microArea", "esusIntegracaoCds.esusFichaUsuarioCadsusEsus.equipeMicroArea.microArea");

        hql.addToSelect("equipeArea.codigo", "esusIntegracaoCds.esusFichaUsuarioCadsusEsus.equipeMicroArea.equipeArea.codigo");
        hql.addToSelect("equipeArea.descricao", "esusIntegracaoCds.esusFichaUsuarioCadsusEsus.equipeMicroArea.equipeArea.descricao");

        hql.addToFrom("EsusIntegracaoCds esusIntegracaoCds "
                + " left join esusIntegracaoCds.exportacaoEsusProcesso exportacaoEsusProcesso "
                + " left join esusIntegracaoCds.esusFichaUsuarioCadsusEsus esusFichaUsuarioCadsusEsus "
                + " left join esusFichaUsuarioCadsusEsus.usuarioCadsusEsus usuarioCadsusEsus "
                + " left join usuarioCadsusEsus.usuarioCadsus usuarioCadsus "
                + " left join esusFichaUsuarioCadsusEsus.equipeMicroArea equipeMicroArea"
                + " left join equipeMicroArea.equipeArea equipeArea"
                + " left join equipeArea.cidade cidade");

        hql.addToWhereWhithAnd("esusIntegracaoCds.tipo = ", EsusIntegracaoCds.Tipo.INDIVIDUAL.value());
        hql.addToWhereWhithAnd("exportacaoEsusProcesso.codigo = ", param.getCodigoExportacaoEsusProcesso());

        hql.addToWhereWhithAnd(hql.getConsultaLiked("usuarioCadsus.nome", this.param.getPaciente()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("usuarioCadsus.nomeMae", this.param.getNomeMae()));
        hql.addToWhereWhithAnd("esusIntegracaoCds.uuid = ", param.getUuid());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("esusIntegracaoCds.descricaoInconsistenciaEsus", this.param.getInconsistencia()));
        hql.addToWhereWhithAnd("usuarioCadsus.codigo = ", param.getCodigoPaciente());
        hql.addToWhereWhithAnd("usuarioCadsus.dataNascimento = ", param.getDataNascimento());

        hql.addToWhereWhithAnd("esusFichaUsuarioCadsusEsus.equipeMicroArea =", param.getEquipeMicroArea());

        if (param.getArea() != null) {
            hql.addToWhereWhithAnd("equipeArea.codigo =", param.getArea().getCodigo());
            hql.addToWhereWhithAnd("cidade.codigo =", param.getArea().getCidade().getCodigo());
        }

        if(RepositoryComponentDefault.SIM_LONG.equals(param.getComInconsistencia())) {
            hql.addToWhereWhithAnd("esusIntegracaoCds.descricaoInconsistenciaEsus is not null");
        } else if(RepositoryComponentDefault.NAO_LONG.equals(param.getComInconsistencia())){
            hql.addToWhereWhithAnd("esusIntegracaoCds.descricaoInconsistenciaEsus is null");
        }

        if (this.param.getNumeroCartao()!= null
                && !this.param.getNumeroCartao().replaceAll("[^0-9]", "").isEmpty()) {
            hql.addToWhereWhithAnd("exists(select 1 from UsuarioCadsusCns ucc where ucc.usuarioCadsus = usuarioCadsus and ucc.excluido = 0 and ucc.numeroCartao = :numeroCartao)");
        }

        if (param.getPropSort() != null) {
            hql.addToOrder(param.getPropSort() + " " + (param.isAscending() ? "asc" : "desc"));
        } else {
            hql.addToOrder("esusIntegracaoCds.dataCadastro desc");
        }
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) {
        if (this.param.getNumeroCartao()!= null
                && !this.param.getNumeroCartao().replaceAll("[^0-9]", "").isEmpty()) {
            query.setParameter("numeroCartao", Long.parseLong(this.param.getNumeroCartao().replaceAll("[^0-9]", "")));
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    public Collection getResult() {
        return this.list;
    }
}