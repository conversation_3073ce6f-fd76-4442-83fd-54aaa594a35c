<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="roteiro_inspecao_externo" columnDirection="RTL" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="56eab6e4-5301-4f11-a723-bfb165341f5f">
	<property name="ireport.zoom" value="4.287177620000048"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<style name="style1"/>
	<subDataset name="perguntas" uuid="e423a229-3ec0-4544-a011-44057a72cc39">
		<field name="pergunta" class="java.lang.String"/>
		<field name="classificacao" class="java.lang.Long"/>
		<field name="leiArtigo" class="java.lang.String"/>
	</subDataset>
	<parameter name="fiscais" class="java.util.List"/>
	<field name="roteiroInspecao" class="br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RoteiroInspecao"/>
	<field name="itemInspecao" class="br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.ItemInspecao"/>
	<field name="itemInspecaoPerguntaList" class="java.util.List"/>
	<group name="roteiro" isStartNewPage="true" isReprintHeaderOnEachPage="true" keepTogether="true">
		<groupExpression><![CDATA[$F{roteiroInspecao}.getCodigo()]]></groupExpression>
		<groupHeader>
			<band height="28">
				<rectangle radius="5">
					<reportElement x="0" y="10" width="555" height="18" uuid="b12257ac-532f-4326-9f14-ddd9acb2f271"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isBlankWhenNull="true">
					<reportElement positionType="FixRelativeToBottom" mode="Opaque" x="0" y="14" width="555" height="14" uuid="03ff7f96-7379-4d8d-9d71-305f15bfb4bc"/>
					<box leftPadding="7" rightPadding="7">
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{roteiroInspecao}.getNomeRoteiro()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="ItemRoteiro" isReprintHeaderOnEachPage="true" minHeightToStartNewPage="1" keepTogether="true">
		<groupExpression><![CDATA[$F{itemInspecao}.getCodigo()]]></groupExpression>
		<groupHeader>
			<band height="18">
				<rectangle>
					<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="555" height="18" backcolor="#DFDFDF" uuid="efac630c-4a1f-4721-b219-30d3f53bd7d9"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="FixRelativeToBottom" stretchType="RelativeToTallestObject" x="391" y="0" width="164" height="18" uuid="6e47b4ed-2bd2-4a16-8b76-c7fc935bcc98"/>
					<box leftPadding="3" rightPadding="3">
						<leftPen lineWidth="0.5"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_enquadramento_legal").toUpperCase()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="FixRelativeToBottom" stretchType="RelativeToTallestObject" x="1" y="0" width="306" height="18" uuid="3d7a3d62-f9c0-4261-a6ca-9a6c930d574d"/>
					<box leftPadding="3" rightPadding="3">
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{itemInspecao}.getSubtitulo()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="FixRelativeToBottom" stretchType="RelativeToTallestObject" x="307" y="0" width="84" height="18" uuid="5fb6a2b6-0928-42cd-adc0-3f075b2baf25"/>
					<box leftPadding="3" rightPadding="3">
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_classificacao").toUpperCase()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<detail>
		<band height="20">
			<componentElement>
				<reportElement x="0" y="0" width="555" height="20" uuid="cff5f511-dcb8-48a7-9cb2-234600deb9a8"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="perguntas" uuid="fa0e7adb-2d9d-4462-b93d-084485eac5ea">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource( $F{itemInspecaoPerguntaList} )]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="20" width="555">
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement x="0" y="0" width="307" height="20" uuid="ca6f8746-f5a9-4f9e-b7f7-7c6d12154851"/>
							<box topPadding="3" leftPadding="3" bottomPadding="3" rightPadding="3"/>
							<textElement textAlignment="Justified" verticalAlignment="Top">
								<font fontName="Arial" size="8" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{pergunta}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
							<reportElement mode="Transparent" x="307" y="0" width="84" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="7d73d710-bb7a-4ce1-802f-4165aaa1f415"/>
							<box topPadding="3" leftPadding="3" bottomPadding="3" rightPadding="3"/>
							<textElement textAlignment="Justified" verticalAlignment="Top" rotation="None" markup="none">
								<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
								<paragraph lineSpacing="Single"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{classificacao} == null ? "" :
    ($F{classificacao} == 1L ? Bundle.getStringApplication("rotulo_necessario")
    :(
        ($F{classificacao} == 2L ? Bundle.getStringApplication("rotulo_imprescindivel") : "")
    )
)]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement x="391" y="0" width="164" height="20" uuid="03954d82-4129-40c5-af88-2c3d512202da"/>
							<box topPadding="3" leftPadding="3" bottomPadding="3" rightPadding="3"/>
							<textElement textAlignment="Justified" verticalAlignment="Top">
								<font fontName="Arial" size="8" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{leiArtigo}]]></textFieldExpression>
						</textField>
						<rectangle>
							<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="0" width="307" height="20" isPrintWhenDetailOverflows="true" uuid="2f3b9677-d91b-422a-a3bd-14c7f67dfd36"/>
							<graphicElement>
								<pen lineWidth="0.5"/>
							</graphicElement>
						</rectangle>
						<rectangle>
							<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="307" y="0" width="84" height="20" isPrintWhenDetailOverflows="true" uuid="d4a4a51e-5d06-4146-bf9e-8824e083c2c4"/>
							<graphicElement>
								<pen lineWidth="0.5"/>
							</graphicElement>
						</rectangle>
						<rectangle>
							<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="391" y="0" width="164" height="20" isPrintWhenDetailOverflows="true" uuid="2ee3fe74-b218-4afb-b409-3ba2f8aa3732"/>
							<graphicElement>
								<pen lineWidth="0.5"/>
							</graphicElement>
						</rectangle>
					</jr:listContents>
				</jr:list>
			</componentElement>
		</band>
	</detail>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
