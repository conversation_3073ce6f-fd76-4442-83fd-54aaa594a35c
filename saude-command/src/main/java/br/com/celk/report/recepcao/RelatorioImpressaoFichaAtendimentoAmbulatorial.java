package br.com.celk.report.recepcao;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.celk.report.recepcao.interfaces.dto.RelatorioImpressaoFichaAtendimentoAmbulatorialDTOParam;
import br.com.celk.report.recepcao.query.QueryRelatorioImpressaoFichaAtendimentoAmbulatorial;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoFichaAtendimentoAmbulatorial extends AbstractReport<RelatorioImpressaoFichaAtendimentoAmbulatorialDTOParam> {

    private RelatorioImpressaoFichaAtendimentoAmbulatorialDTOParam param;

    public RelatorioImpressaoFichaAtendimentoAmbulatorial(RelatorioImpressaoFichaAtendimentoAmbulatorialDTOParam param) {
        super(param);
        this.param = param;
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/recepcao/jrxml/relatorio_impressao_ficha_atendimento_ambulatorial.jrxml";
    }

    @Override
    public ITransferDataReport getQuery() {
        addParametro("usuario", SessaoAplicacaoImp.getInstance().getUsuario().getNome());

        return new QueryRelatorioImpressaoFichaAtendimentoAmbulatorial();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relatorio_ficha_atendimento_ambulatorial");
    }
}
