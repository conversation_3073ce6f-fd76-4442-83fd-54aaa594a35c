package br.com.celk.report.emprestimo.query;

import br.com.celk.bo.emprestimo.interfaces.dto.RelacaoEmprestimoDTO;
import br.com.celk.bo.emprestimo.interfaces.dto.RelacaoEmprestimoDTOParam;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.*;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoque;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import org.hibernate.Session;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryRelacaoEmprestimo extends CommandQuery implements ITransferDataReport<RelacaoEmprestimoDTOParam, RelacaoEmprestimoDTO> {

    private RelacaoEmprestimoDTOParam param;
    private List<RelacaoEmprestimoDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(RelacaoEmprestimoDTO.class.getName());

        hql.addToSelectAndGroup("le.codigo", "codigoEmprestimo");
        hql.addToSelectAndGroup("le.dataEmprestimo", "dataEmprestimo");
        hql.addToSelectAndGroup("e.descricao", "estabelecimento");
        hql.addToSelectAndGroup("p.descricao", "produto");
        hql.addToSelectAndGroup("un.unidade", "unidade");
        hql.addToSelect("sum(lei.quantidade)", "quantidade");
        hql.addToSelect("sum(lei.quantidadeDevolvida)", "quantidadeDevolvida");
        hql.addToSelectAndGroup("lei.status", "status");
        hql.addToSelectAndGroup("eE.descricao", "empresaEmprestimo");
        hql.addToSelectAndGroup("u.nome", "paciente");
        hql.addToSelectAndGroup("p.codigo", "codigoProduto");

        hql.addToFrom("LancamentoEmprestimoItem lei "
                + "left join lei.produto p "
                + "left join lei.lancamentoEmprestimo le "
                + "left join le.empresa e "
                + "left join le.empresaEmprestimo eE "
                + "left join le.usuarioCadsus u "
                + "left join p.unidade un "
                + "join le.tipoEmprestimo tp");

        if (param.getOrdenacao().getCommand().equals("u.nome")) {
            hql.addToOrder(param.getOrdenacao().getCommand() + " " + param.getTipoOrdenacao().getCommand());
        } else {
            hql.addToOrder("le." + param.getOrdenacao().getCommand() + " " + param.getTipoOrdenacao().getCommand());
        }

        hql.addToWhereWhithAnd("le.dataEmprestimo", param.getPeriodo());
        hql.addToWhereWhithAnd("le.empresa", param.getEstabelecimento());
        hql.addToWhereWhithAnd("lei.produto=", param.getProduto());
        hql.addToWhereWhithAnd("le.usuarioCadsus=", param.getPaciente());
        hql.addToWhereWhithAnd("le.empresaEmprestimo=", param.getEstabelecimentoEmprestimo());
        hql.addToWhereWhithAnd("le.tipoEmprestimo=", param.getTipoEmprestimo());

        if (RelacaoEmprestimoDTOParam.FormaApresentacao.TIPOEMPRESTIMO.equals(this.param.getFormaApresentacao())) {
            hql.addToSelectAndGroupAndOrder("tp.descricaoTipoEmprestimo", "descricaoTipoEmprestimo");
        } else if (RelacaoEmprestimoDTOParam.FormaApresentacao.ESTABELECIMENTOEMPRESTIMO.equals(this.param.getFormaApresentacao())) {
            hql.addToWhereWhithAnd("le.empresaEmprestimo is not null");
            hql.addToOrder("eE.descricao");
        } else if (RelacaoEmprestimoDTOParam.FormaApresentacao.PRODUTO.equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("p.descricao");
        }

        if (!RelacaoEmprestimoDTOParam.Situacao.TODOS.value().equals(this.param.getSituacao().value())) {
            hql.addToWhereWhithAnd("lei.status=", param.getSituacao().value());
        }

        if (!RelacaoEmprestimoDTOParam.TipoOperacao.AMBOS.value().equals(param.getTipoOperacao().value())) {
            hql.addToWhereWhithAnd("tp.tipoEmprestimo=", param.getTipoOperacao().value());
        }
    }

    @Override
    public void setDTOParam(RelacaoEmprestimoDTOParam param) {
        this.param = param;
    }

    @Override
    public Collection getResult() {
        return this.result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(this.result)) {
            for (RelacaoEmprestimoDTO dto : result) {
                MovimentoEstoque me = LoadManager.getInstance(MovimentoEstoque.class)
                        .addProperties(new HQLProperties(MovimentoEstoque.class).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(MovimentoEstoque.PROP_PRODUTO, Produto.PROP_CODIGO),dto.getCodigoProduto()))
                        .addSorter(new QueryCustom.QueryCustomSorter(MovimentoEstoque.PROP_DATA_CADASTRO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                        .setMaxResults(1)
                        .start()
                        .getVO();

                if (me != null && me.getPrecoMedio() != null) {
                    dto.setPrecoMedio(me.getPrecoMedio());
                }
            }
        }
    }
}
