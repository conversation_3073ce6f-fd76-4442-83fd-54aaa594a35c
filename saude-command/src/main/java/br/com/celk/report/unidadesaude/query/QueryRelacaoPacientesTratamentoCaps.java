package br.com.celk.report.unidadesaude.query;

import br.com.celk.report.unidadesaude.interfaces.dto.RelatorioRelacaoPacientesTratamentoCapsDTO;
import br.com.celk.report.unidadesaude.interfaces.dto.RelatorioRelacaoPacientesTratamentoCapsDTOParam;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Equipe;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.FichaAcolhimento;
import org.hibernate.Session;
import org.josql.QueryExecutionException;
import org.josql.QueryParseException;
import org.josql.QueryResults;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryRelacaoPacientesTratamentoCaps extends CommandQuery<QueryRelacaoPacientesTratamentoCaps> implements ITransferDataReport<RelatorioRelacaoPacientesTratamentoCapsDTOParam, RelatorioRelacaoPacientesTratamentoCapsDTO> {

    private RelatorioRelacaoPacientesTratamentoCapsDTOParam param;
    private List<RelatorioRelacaoPacientesTratamentoCapsDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("fa.codigo", "fichaAcolhimento.codigo");
        hql.addToSelect("fa.dataAdmissao", "fichaAcolhimento.dataAdmissao");
        hql.addToSelect("fa.dataConclusao", "fichaAcolhimento.dataConclusao");

        hql.addToSelect("ua.dataAtendimento", "dataAtendimento");

        hql.addToSelect("uc.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("uc.nome", "usuarioCadsus.nome");
        hql.addToSelect("uc.dataNascimento", "usuarioCadsus.dataNascimento");
        hql.addToSelect("uc.sexo", "usuarioCadsus.sexo");
        hql.addToSelect("uc.nomeMae", "usuarioCadsus.nomeMae");
        hql.addToSelect("endUc.codigo", "usuarioCadsus.enderecoUsuarioCadsus.codigo");
        hql.addToSelect("endDom.codigo", "usuarioCadsus.enderecoDomicilio.codigo");

        hql.addToSelect("mds.codigo", "motivoDestinoSaida.codigo");
        hql.addToSelect("mds.descricao", "motivoDestinoSaida.descricao");

        hql.addToSelect("e.codigo", "empresa.codigo");
        hql.addToSelect("e.descricao", "empresa.descricao");

        hql.addToSelect("cid.codigo", "cid.codigo");
        hql.addToSelect("cid.descricao", "cid.descricao");

        hql.addToSelect("p.codigo", "profissional.codigo");
        hql.addToSelect("p.nome", "profissional.nome");

        hql.setTypeSelect(RelatorioRelacaoPacientesTratamentoCapsDTO.class.getName());

        hql.addToFrom("FichaAcolhimento fa"
                + " left join fa.cid cid"
                + " left join fa.motivoDestinoSaida mds"
                + " left join fa.atendimento a"
                + " left join a.empresa e"
                + " left join a.profissional p"
                + " left join a.usuarioCadsus uc "
                + " left join uc.enderecoUsuarioCadsus endUc "
                + " left join uc.enderecoDomicilio endDom "
                + " left join fa.ultimoAtendimento ua"
        );

        hql.addToWhereWhithAnd("e", this.param.getEmpresa());
        hql.addToWhereWhithAnd("uc = ", this.param.getPaciente());
        hql.addToWhereWhithAnd("cid = ", this.param.getCid());
        hql.addToWhereWhithAnd("mds = ", this.param.getMotivoDestinoSaida());

        if (this.param.getPeriodo() != null) {
            if (RelatorioRelacaoPacientesTratamentoCapsDTOParam.TipoPeriodo.DATA_ADMISSAO.equals(this.param.getTipoPeriodo())) {
                hql.addToWhereWhithAnd("fa.dataAdmissao", this.param.getPeriodo());
            } else if (RelatorioRelacaoPacientesTratamentoCapsDTOParam.TipoPeriodo.DATA_ATENDIMENTO.equals(this.param.getTipoPeriodo())) {
                hql.addToWhereWhithAnd("ua.dataAtendimento", this.param.getPeriodo());
            } else if (RelatorioRelacaoPacientesTratamentoCapsDTOParam.TipoPeriodo.DATA_CONCLUSAO.equals(this.param.getTipoPeriodo())) {
                hql.addToWhereWhithAnd("fa.dataConclusao", this.param.getPeriodo());
            }
        }

        if (this.param.getDiasUltimaConsulta() != null) {
            Date ultimaConsultaRealizada = Data.adjustRangeHour(Data.removeDias(DataUtil.getDataAtual(), this.param.getDiasUltimaConsulta().intValue())).getDataFinal();
            hql.addToWhereWhithAnd("ua.dataAtendimento <= ", ultimaConsultaRealizada);
            hql.addToWhereWhithAnd("fa.status = ", FichaAcolhimento.Status.PENDENTE.value());
        }

        if (RelatorioRelacaoPacientesTratamentoCapsDTOParam.FormaApresentacao.CID.equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("cid.descricao" + QueryCustom.QueryCustomSorter.CRESCENTE_NULLS_FIRST);
        } else if (RelatorioRelacaoPacientesTratamentoCapsDTOParam.FormaApresentacao.DESTINO_PACIENTE.equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("mds.descricao" + QueryCustom.QueryCustomSorter.CRESCENTE_NULLS_FIRST);
        }

        hql.addToOrder("fa.dataAdmissao" + QueryCustom.QueryCustomSorter.DECRESCENTE_NULLS_FIRST);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        for (RelatorioRelacaoPacientesTratamentoCapsDTO dto : result) {
            Empresa unidadeOrigem = null;
            final UsuarioCadsus usuarioCadsus = dto.getUsuarioCadsus();
            EnderecoDomicilio enderecoDomicilio = usuarioCadsus.getEnderecoDomicilio();
            if (enderecoDomicilio != null && enderecoDomicilio.getCodigo() != null) {
                enderecoDomicilio = LoadManager.getInstance(EnderecoDomicilio.class)
                        .addProperties(new HQLProperties(EnderecoDomicilio.class).getProperties())
                        .setId(enderecoDomicilio.getCodigo()).start().getVO();

                if (enderecoDomicilio.getEquipeMicroArea() != null) {
                    EquipeProfissional equipeProfissional = LoadManager.getInstance(EquipeProfissional.class)
                            .addProperties(new HQLProperties(Profissional.class, VOUtils.montarPath(EquipeProfissional.PROP_PROFISSIONAL)).getProperties())
                            .addProperties(new HQLProperties(Empresa.class, VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA)).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE_MICRO_AREA), enderecoDomicilio.getEquipeMicroArea()))
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_CIDADE), enderecoDomicilio.getCidade()))
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_ATIVO), RepositoryComponentDefault.SIM))
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_STATUS), EquipeProfissional.STATUS_ATIVO))
                            .setMaxResults(1).start().getVO();

                    if (equipeProfissional != null) {
                        unidadeOrigem = equipeProfissional.getEquipe().getEmpresa();
                    }
                }

            }

            if (unidadeOrigem == null) {
                EnderecoUsuarioCadsus enderecoUsuarioCadsus = usuarioCadsus.getEnderecoUsuarioCadsus();
                if (enderecoUsuarioCadsus != null) {
                    enderecoUsuarioCadsus = LoadManager.getInstance(EnderecoUsuarioCadsus.class)
                            .addProperties(new HQLProperties(Empresa.class, EnderecoUsuarioCadsus.PROP_EMPRESA).getProperties())
                            .setMaxResults(1)
                            .setId(enderecoUsuarioCadsus.getCodigo()).start().getVO();
                    unidadeOrigem = enderecoUsuarioCadsus.getEmpresa();
                }
            }

            dto.setUnidadeOrigem(unidadeOrigem);
        }

        try {
            if (this.param.getUnidadeOrigem() != null
                    || RelatorioRelacaoPacientesTratamentoCapsDTOParam.FormaApresentacao.UNIDADE_ORIGEM.equals(this.param.getFormaApresentacao())) {
                QueryResults qr = null;
                org.josql.Query query = new org.josql.Query();
                String sql =
                        " SELECT * "
                                + " FROM " + RelatorioRelacaoPacientesTratamentoCapsDTO.class.getName() + " ";

                if (this.param.getUnidadeOrigem() != null) {
                    sql += "where unidadeOrigem.codigo = :codigoOrigem  ";
                    query.setVariable("codigoOrigem", this.param.getUnidadeOrigem().getCodigo());
                }
                if (RelatorioRelacaoPacientesTratamentoCapsDTOParam.FormaApresentacao.UNIDADE_ORIGEM.equals(this.param.getFormaApresentacao())) {
                    sql += " ORDER BY unidadeOrigem.descricao";
                }


                query.parse(sql);
                qr = query.execute(result);

                result = qr.getResults();

            }
        } catch (QueryParseException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        } catch (QueryExecutionException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
    }

    @Override
    public List<RelatorioRelacaoPacientesTratamentoCapsDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(RelatorioRelacaoPacientesTratamentoCapsDTOParam param) {
        this.param = param;
    }

}
