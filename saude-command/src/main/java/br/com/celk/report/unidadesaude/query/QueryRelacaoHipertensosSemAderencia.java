package br.com.celk.report.unidadesaude.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelacaoHipertensosSemAderenciaDTO;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelacaoPacientesSemAderenciaDTOParam;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.hibernate.type.LongType;
import org.hibernate.type.StringType;

import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class QueryRelacaoHipertensosSemAderencia extends CommandQuery<QueryRelacaoHipertensosSemAderencia> implements ITransferDataReport<RelacaoPacientesSemAderenciaDTOParam, RelacaoHipertensosSemAderenciaDTO> {

    private transient SQLQuery sqlQuery;
    private RelacaoPacientesSemAderenciaDTOParam param;

    @Override
    public void setDTOParam(RelacaoPacientesSemAderenciaDTOParam param) {
        this.param = param;
    }


    @Override
    protected void createQuery(HQLHelper hql) throws DAOException {
        sqlQuery = getSession().createSQLQuery(QueryRelacaoHipertensosSemAderenciaHelper.getQuery(this.param));
        setParameters(sqlQuery, this.param);
        addScalar(sqlQuery);
    }

    protected void addScalar(SQLQuery sqlQuery) {
        RelacaoHipertensosSemAderenciaDTO proxy = on(RelacaoHipertensosSemAderenciaDTO.class);

        sqlQuery.addScalar(path(proxy.getCodigoUsuarioCadsus()), LongType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getNomePaciente()), StringType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getIdade()), LongType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getSexo()), StringType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getIndicadorSeis()), StringType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getConsultaHipertensao()), StringType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getConsultaPressaoArterialUltimosSeisMeses()), StringType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getNomeEquipe()), StringType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getCelular()), StringType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getCelular()), StringType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getEndereco()), StringType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getBairro()), StringType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getNomeEquipeAtendimento()), StringType.INSTANCE);
        sqlQuery.addScalar(path(proxy.getNomeEquipeResidencia()), StringType.INSTANCE);

        sqlQuery.setResultTransformer(Transformers.aliasToBean(RelacaoHipertensosSemAderenciaDTO.class));
    }

    private void setParameters(SQLQuery query, RelacaoPacientesSemAderenciaDTOParam param) {
        if (param.getEquipeArea() != null) {
            query.setParameter("descricaoArea", param.getEquipeArea().getDescricao());
        }
        if (param.getSixIndicator() != null) {
            query.setParameter("flagSixIndicator", param.getSixIndicator());
        }
    }

    @Override
    public List<RelacaoHipertensosSemAderenciaDTO> getResult() {
        return sqlQuery.list();
    }
}
