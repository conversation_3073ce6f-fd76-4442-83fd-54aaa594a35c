package br.com.celk.pharos;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.PharosConsultaIntegracaoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.pharos.PharosIntegracaoRegistro;


/**
 * <AUTHOR>
 * Classe para consultar os registros que foram enviados ao microsserviço de integração, que podem estar com diversos status
 */
public class QueryConsultarRegistrosPharos extends CommandQueryPager<QueryConsultarRegistrosPharos> {

    private PharosConsultaIntegracaoDTOParam param;

    public QueryConsultarRegistrosPharos(PharosConsultaIntegracaoDTOParam dto) {
        this.param = dto;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(PharosIntegracaoRegistro.class.getName());

        hql.addToFrom("PharosIntegracaoRegistro pir");

        hql.addToWhereWhithAnd("pir.tipoIntegracao = ", param.getTipoIntegracao());
        hql.addToWhereWhithAnd("pir.dataEnvioPharos", param.getPeriodo());

        if (PharosIntegracaoRegistro.SituacaoIntegracao.isErro(param.getSituacao())) {
            hql.addToWhereWhithAnd("pir.situacao in (" + PharosIntegracaoRegistro.SituacaoIntegracao.getErrorValues()+")");
        } else {
            hql.addToWhereWhithAnd("pir.situacao = ", param.getSituacao());
        }

        hql.addToOrder("pir.dataEnvioPharos desc");
    }
}
