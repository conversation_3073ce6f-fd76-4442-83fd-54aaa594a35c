package br.com.celk.cscidadao.integracao.query;

import br.com.celk.cscidadao.integracao.dto.VacinaIntegrationDTO;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import org.hibernate.Query;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaVacinaIntegracaoInovamfri extends CommandQuery<QueryConsultaVacinaIntegracaoInovamfri> {

    private List<VacinaIntegrationDTO> result;
    private int first;
    private int limit;

    public QueryConsultaVacinaIntegracaoInovamfri(int first, int limit) {
        this.first = first;
        this.limit = limit;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(VacinaIntegrationDTO.class.getName());

        hql.addToSelectAndGroup("cast(va.codigo as text)", "idRegistroClient");
        hql.addToSelectAndGroup("to_char(va.dataAplicacao, 'dd/MM/yyyy')", "dataAplicacao");
        hql.addToSelectAndGroup("va.descricaoVacina", "descricaoVacina");
        hql.addToSelectAndGroup("va.dose", "codigoDose");
        hql.addToSelectAndGroup("cast(va.status as text)", "situacao");

        hql.addToSelectAndGroup("uc.codigo", "codigoUsuarioCadSus");
        hql.addToSelectAndGroup("uc.nome", "nomePaciente");
        hql.addToSelect("(select cast(min(ucc.numeroCartao) as text) from UsuarioCadsusCns ucc where ucc.usuarioCadsus = uc and ucc.excluido = 0)", "cnsPaciente");

        hql.addToFrom("VacinaAplicacao va  left join va.usuarioCadsus uc ");

        hql.addToWhereWhithAnd("va.dataIntegracaoInovamfri IS NULL");
        hql.addToWhereWhithAnd("va.status IN (0,1,3)");
        hql.addToWhereWhithAnd("exists(select 1 from UsuarioCadsusCns ucc where ucc.usuarioCadsus = uc and ucc.excluido = 0) ");
        hql.addToWhereWhithAnd("va.dataAplicacao >= '2015-01-01'"); // Somente deve ser integrado registros após esta data
    }

    @Override
    public List<VacinaIntegrationDTO> getResult() {
        return result;
    }

    @Override
    protected void customQuery(Query query) {
        super.customQuery(query.setFirstResult(this.first).setMaxResults(this.limit));
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}