package br.com.celk.cscidadao.integracao.process;

import br.com.celk.cscidadao.integracao.abstracts.AbstractProcess;
import br.com.celk.cscidadao.integracao.dto.EstoqueIntegrationDTO;
import br.com.celk.cscidadao.integracao.util.CsCidadaoHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;

import java.util.List;

/**
 * <AUTHOR>
 */
public class EstoqueProcess extends AbstractProcess<EstoqueIntegrationDTO> {

    public EstoqueProcess(List<Usuario> usuariosList) throws ValidacaoException, DAOException {
        super(usuariosList);
    }

    @Override
    public List<EstoqueIntegrationDTO> executeQueryProcess(int first, int limit) throws ValidacaoException, DAOException {
        return INTEGRACAO_CS_CIDADAO_FACADE.consultarEstoqueIntegracao(first, limit);
    }

    @Override
    public String executeIntegrationProcess(List<Usuario> usuarioList, List<EstoqueIntegrationDTO> dtoList) throws ValidacaoException, DAOException {
        return INTEGRACAO_CS_CIDADAO_FACADE.integrarEstoque(usuarioList, dtoList);
    }

    @Override
    public CsCidadaoHelper.Resources getResources() {
        return CsCidadaoHelper.Resources.ESTOQUE;
    }
}