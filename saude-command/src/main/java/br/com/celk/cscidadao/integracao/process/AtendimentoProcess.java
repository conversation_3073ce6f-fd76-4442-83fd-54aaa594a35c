package br.com.celk.cscidadao.integracao.process;

import br.com.celk.cscidadao.integracao.abstracts.AbstractProcess;
import br.com.celk.cscidadao.integracao.dto.AtendimentoIntegrationDTO;
import br.com.celk.cscidadao.integracao.util.CsCidadaoHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AtendimentoProcess extends AbstractProcess<AtendimentoIntegrationDTO> {

    public AtendimentoProcess(List<Usuario> usuariosList) {
        super(usuariosList);
    }

    @Override
    public List<AtendimentoIntegrationDTO> executeQueryProcess(int first, int limit) throws ValidacaoException, DAOException {
        return INTEGRACAO_CS_CIDADAO_FACADE.consultarAtendimentoIntegracao(first, limit);
    }

    @Override
    public String executeIntegrationProcess(List<Usuario> usuarioList, List<AtendimentoIntegrationDTO> dtoList) throws ValidacaoException, DAOException {
        return INTEGRACAO_CS_CIDADAO_FACADE.integrarAtendimento(getUsuariosList(), dtoList);
    }

    @Override
    public CsCidadaoHelper.Resources getResources() {
        return CsCidadaoHelper.Resources.ATENDIMENTO;
    }
}