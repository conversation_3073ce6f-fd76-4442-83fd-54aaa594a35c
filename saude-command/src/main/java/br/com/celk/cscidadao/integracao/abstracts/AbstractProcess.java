package br.com.celk.cscidadao.integracao.abstracts;

import br.com.celk.cscidadao.integracao.interfaces.IIntegrationProcess;
import br.com.celk.cscidadao.integracao.util.CsCidadaoConnection;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.basico.interfaces.facade.IntegracaoCsCidadaoFacade;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.MensagemDTO;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;

import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class AbstractProcess<T> extends AbstractCommandTransaction implements IIntegrationProcess {

    private static final String PROCESSO_DE_INTEGRACAO_CS_CIDADAO_INICIADO = "----- PROCESSO DE INTEGRAÇÃO CS-CIDADÃO INICIADO ----- ";
    private static final String PROCESSO_DE_INTEGRACAO_CS_CIDADAO_FINALIZADO = "----- PROCESSO DE INTEGRAÇÃO CS-CIDADÃO FINALIZADO ----- ";
    private final CsCidadaoConnection<T> csCidadaoConnection;
    private StringBuilder mensagemBuilder;
    private List<Usuario> usuariosList;
    private Integer first = 0;
    private Integer limit = 100;
    private List<T> list;
    protected static final IntegracaoCsCidadaoFacade INTEGRACAO_CS_CIDADAO_FACADE = BOFactory.getBO(IntegracaoCsCidadaoFacade.class);

    protected AbstractProcess(List<Usuario> usuariosList) {
        this.csCidadaoConnection = new CsCidadaoConnection<>(this);
        this.mensagemBuilder = new StringBuilder();
        this.usuariosList = usuariosList;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        createInitialMessageLog();

        executeProcess();

        createFinalMessageLog();

    }

    protected void executeProcess() throws ValidacaoException, DAOException {
        do {
            if(getFirst() == null){
                setLimit(0);
            }
            if(getLimit() == null){
                setLimit(100);
            }
            list = executeQueryProcess(getFirst(), getLimit());
            if (CollectionUtils.isNotNullEmpty(list)) {
                getMensagemBuilder().append(executeIntegrationProcess(getUsuariosList(), list));
            }
        } while (CollectionUtils.isNotNullEmpty(list));
        if (!getMensagemBuilder().toString().isEmpty()) {
            MensagemDTO mensagemDTO = new MensagemDTO();
            mensagemDTO.setAssunto(Bundle.getStringApplication("msg_problema_integracao_cs_cidadao_X", getResources().getDescricao()));
            mensagemDTO.setUsuarios(getUsuariosList());
            mensagemDTO.setMensagem(getMensagemBuilder().toString());
            BOFactory.getBO(ComunicacaoFacade.class).enviarMensagem(mensagemDTO);
        }
    }

    public void notificarUsuariosErro(String jsonErro) throws ValidacaoException, DAOException {
        MensagemDTO mensagemDTO = new MensagemDTO();
        mensagemDTO.setAssunto(Bundle.getStringApplication("rotulo_integracao_cs_cidadao"));
        mensagemDTO.setUsuarios(getUsuariosList());

        StringBuilder sb = new StringBuilder(Bundle.getStringApplication("msg_integracao_cs_cidadao_erro"));
        sb.append(" - ").append(jsonErro);

        mensagemDTO.setMensagem(sb.toString());
        BOFactory.getBO(ComunicacaoFacade.class).enviarMensagem(mensagemDTO);
    }


    public List<T> executeQueryProcess(int first, int limit) throws ValidacaoException, DAOException{
       return null;
    }

    public String executeIntegrationProcess(List<Usuario>  usuarioList, List<T> dtoList) throws ValidacaoException, DAOException{
        return null;
    }

    public void createInitialMessageLog(){
        Loggable.log.info(PROCESSO_DE_INTEGRACAO_CS_CIDADAO_INICIADO + TenantContext.getContext() + ": " + getResources().getDescricao());
    }

    public void createFinalMessageLog(){
        Loggable.log.info(PROCESSO_DE_INTEGRACAO_CS_CIDADAO_FINALIZADO + TenantContext.getContext() + ": " + getResources().getDescricao());
    }

    public StringBuilder getMensagemBuilder() {
        return mensagemBuilder;
    }

    @Override
    public List<Usuario> getUsuariosList() {
        return usuariosList;
    }

    public CsCidadaoConnection<T> getCsCidadaoConnection() {
        return csCidadaoConnection;
    }

    public Integer getFirst() {
        return first;
    }

    public void setFirst(Integer first) {
        this.first = first;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }

    public String getMensagem(){
        return getMensagemBuilder().toString();
    }
}
