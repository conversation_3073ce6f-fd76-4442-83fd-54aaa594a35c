package br.com.celk.cscidadao.integracao.process;

import br.com.celk.cscidadao.integracao.abstracts.AbstractProcess;
import br.com.celk.cscidadao.integracao.dto.AgendamentoIntegrationDTO;
import br.com.celk.cscidadao.integracao.util.CsCidadaoHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AgendamentoProcess extends AbstractProcess<AgendamentoIntegrationDTO> {

    public AgendamentoProcess(List<Usuario> usuariosList) {
        super(usuariosList);
    }

    @Override
    public List<AgendamentoIntegrationDTO> executeQueryProcess(int first, int limit) throws ValidacaoException, DAOException {
        return INTEGRACAO_CS_CIDADAO_FACADE.consultarAgendamentoIntegracao(first, limit);
    }

    @Override
    public String executeIntegrationProcess(List<Usuario> usuarioList, List<AgendamentoIntegrationDTO> dtoList) throws ValidacaoException, DAOException {
        return INTEGRACAO_CS_CIDADAO_FACADE.integrarAgendamento(getUsuariosList(), dtoList);
    }

    @Override
    public CsCidadaoHelper.Resources getResources() {
        return CsCidadaoHelper.Resources.AGENDAMENTO;
    }
}