package br.com.celk.cscidadao.integracao.integrar;

import br.com.celk.cscidadao.integracao.abstracts.AbstractIntegrationProcess;
import br.com.celk.cscidadao.integracao.dto.*;
import br.com.celk.cscidadao.integracao.util.CsCidadaoHelper;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.hibernate.Query;

import javax.ws.rs.client.Entity;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.List;
import java.util.ListIterator;

/**
 * <AUTHOR>
 */
public class IntegrarFilaEsperaProcess extends AbstractIntegrationProcess<FilaEsperaIntegrationDTO> {

    public IntegrarFilaEsperaProcess(List<Usuario> usuariosList, List<FilaEsperaIntegrationDTO> dto) {
        super(usuariosList, dto);
    }

    @Override
    public void executeUpdate(FilaEsperaIntegrationDTO dto) {

        String update = "UPDATE solicitacao_agendamento SET dt_integracao_inovamfri = ?  WHERE cd_solicitacao = ? ";
        Query query1 = getSession().createSQLQuery(update);
        query1.setTimestamp(0, DataUtil.getDataAtual());
        query1.setLong(1, new Long(dto.getIdRegistroClient()));

        query1.executeUpdate();
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        Response response;
        LotesEnvioDTO lotesEnvioDTO = new LotesEnvioDTO();
        lotesEnvioDTO.setTipo("FILA_ESPERA");
        Response responseLote = new CsCidadaoHelper().startConnection(Entity.entity(lotesEnvioDTO, MediaType.APPLICATION_JSON_TYPE), CsCidadaoHelper.Resources.LOTE);
        LotesRecebimentoDTO lotesRecebimentoDTO = responseLote.readEntity(LotesRecebimentoDTO.class);

        createInitialMessageLog();
        for (FilaEsperaIntegrationDTO filaEsperaIntegrationDTO : getList()) {
            filaEsperaIntegrationDTO.setIdLote(lotesRecebimentoDTO.getId());
            response = getCsCidadaoConnection().conectarRecurso(filaEsperaIntegrationDTO);
            if (response == null) {
                return;
            }
            if (Response.Status.INTERNAL_SERVER_ERROR.getStatusCode() == response.getStatus()) {
                getMensagemBuilder().append(filaEsperaIntegrationDTO.toString());
                getMensagemBuilder().append(" - ");
                InternalServerErrorDTO internalServerErrorDTO = response.readEntity(InternalServerErrorDTO.class);
                getMensagemBuilder().append(internalServerErrorDTO.getMessage());
                getMensagemBuilder().append("; \n");
            } else if (Response.Status.BAD_REQUEST.getStatusCode() == response.getStatus()) {
                getMensagemBuilder().append(filaEsperaIntegrationDTO.toString());
                getMensagemBuilder().append(" - ");
                List<ParameterViolation> parameterViolations = response.readEntity(BadRequestDTO.class).getParameterViolations();
                ListIterator<ParameterViolation> parameterViolationListIterator = parameterViolations.listIterator();
                while (parameterViolationListIterator.hasNext()) {
                    ParameterViolation next = parameterViolationListIterator.next();
                    getMensagemBuilder().append(next.getMessage());
                    if (parameterViolationListIterator.hasNext()) {
                        getMensagemBuilder().append(", ");
                    }

                }
                getMensagemBuilder().append("; \n");
            }

            executeUpdate(filaEsperaIntegrationDTO);
        }
        new CsCidadaoHelper().startPutConnection(Entity.entity(lotesRecebimentoDTO, MediaType.APPLICATION_JSON_TYPE), CsCidadaoHelper.Resources.LOTE, lotesRecebimentoDTO.getId() + "?status=FINALIZADO");
        createFinalMessageLog();
    }

    @Override
    public CsCidadaoHelper.Resources getResources() {
        return CsCidadaoHelper.Resources.FILA_ESPERA;
    }



}