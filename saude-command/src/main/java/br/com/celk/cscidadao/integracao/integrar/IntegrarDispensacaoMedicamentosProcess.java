package br.com.celk.cscidadao.integracao.integrar;

import br.com.celk.cscidadao.integracao.abstracts.AbstractIntegrationProcess;
import br.com.celk.cscidadao.integracao.dto.DispensacaoMedicamentosIntegrationDTO;
import br.com.celk.cscidadao.integracao.util.CsCidadaoHelper;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.hibernate.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public class IntegrarDispensacaoMedicamentosProcess extends AbstractIntegrationProcess<DispensacaoMedicamentosIntegrationDTO> {

    public IntegrarDispensacaoMedicamentosProcess(List<Usuario> usuariosList, List<DispensacaoMedicamentosIntegrationDTO> dto) {
        super(usuariosList, dto);
    }

    @Override
    public CsCidadaoHelper.Resources getResources() {
        return CsCidadaoHelper.Resources.DISPENSACAO_MEDICAMENTO;
    }

    @Override
    public void executeUpdate(DispensacaoMedicamentosIntegrationDTO dto) {

        String update = "UPDATE dispensacao_medicamento_item SET dt_integracao_inovamfri = ?  WHERE cd_dis_med_item = ? ";
        Query query1 = getSession().createSQLQuery(update);
        query1.setTimestamp(0, DataUtil.getDataAtual());
        query1.setLong(1, new Long(dto.getIdRegistroClient()));

        query1.executeUpdate();
    }

}