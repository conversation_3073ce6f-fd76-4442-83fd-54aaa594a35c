package br.com.celk.cscidadao.integracao.query;

import br.com.celk.cscidadao.integracao.dto.UnidadesEspecialidadesIntegrationDTO;
import br.com.celk.cscidadao.integracao.dto.UnidadesIntegrationDTO;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaEspecialidades;
import org.hibernate.Query;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryConsultaUnidadesIntegracaoInovamfri extends CommandQuery<QueryConsultaUnidadesIntegracaoInovamfri> {

    private List<UnidadesIntegrationDTO> result;
    private int first;
    private int limit;

    public QueryConsultaUnidadesIntegracaoInovamfri(int first, int limit) {
        this.first = first;
        this.limit = limit;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(UnidadesIntegrationDTO.class.getName());

        hql.addToSelect("cast(empresa.codigo as text)", "idRegistroClient");
        hql.addToSelect("empresa.codigo", "codigoEmpresa");
        hql.addToSelect("empresa.descricao", "nomeUnidade");
        hql.addToSelect("empresa.horarioAtendimento", "horarioAtendimento");
        hql.addToSelect("cast(empresa.telefone as text)", "telefone");
        hql.addToSelect("empresa.email", "email");
        hql.addToSelect("empresa.rua", "rua");
        hql.addToSelect("empresa.numero", "numero");
        hql.addToSelect("empresa.complemento", "complemento");
        hql.addToSelect("empresa.bairro", "bairro");
        hql.addToSelect("empresa.cep", "cep");
        hql.addToSelect("empresa.flagIntegrar", "flagConsulta");
        hql.addToSelect("case empresa.flagExibirEstoque when 1 then true else false end", "exibirEstoque");

        hql.addToSelect("cast(cidade.codigo as text)", "cidadeCodigo");
        hql.addToSelect("cidade.descricao", "cidadeDesc");

        hql.addToFrom("Empresa empresa"
                + " left join empresa.cidade cidade"
        );

        hql.addToWhereWhithAnd("empresa.dataIntegracaoInovamfri IS NULL");
        hql.addToWhereWhithAnd("empresa.ativo = 'S'");
//        hql.addToWhereWhithAnd("empresa.flagIntegrar = 1");
//        hql.addToWhereWhithAnd("va.dataAplicacao >= '2015-01-01'"); // Somente deve ser integrado registros após esta data
    }

    @Override
    public List<UnidadesIntegrationDTO> getResult() {
        return result;
    }

    @Override
    protected void customQuery(Query query) {
        super.customQuery(query.setFirstResult(this.first).setMaxResults(this.limit));
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(result)) {
            for (UnidadesIntegrationDTO unidadesIntegrationDTO : result) {
                unidadesIntegrationDTO.setEspecialidadesList(gerarEmpresaEspecialidade(session, unidadesIntegrationDTO.getCodigoEmpresa()));
            }
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    private List<UnidadesEspecialidadesIntegrationDTO> gerarEmpresaEspecialidade(Session session, Long codigoEmpresa) {
        ArrayList<UnidadesEspecialidadesIntegrationDTO> list = new ArrayList<>();
        List<EmpresaEspecialidades> lstItemProcedimento = LoadManager.getInstance(EmpresaEspecialidades.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EmpresaEspecialidades.PROP_EMPRESA, Empresa.PROP_CODIGO), codigoEmpresa))
                .addParameter(new QueryCustom.QueryCustomParameter(EmpresaEspecialidades.PROP_ATIVO, RepositoryComponentDefault.SIM_LONG))
                .start().getList();

        for (EmpresaEspecialidades empresaEspecialidades : lstItemProcedimento) {
            UnidadesEspecialidadesIntegrationDTO especialidade = new UnidadesEspecialidadesIntegrationDTO();
            especialidade.setIdRegistroClient(empresaEspecialidades.getCodigo().toString());
            especialidade.setDescricao(empresaEspecialidades.getDescricao());
            especialidade.setHorarioAtendimento(empresaEspecialidades.getHorarioAtendimento());

            list.add(especialidade);
        }
        return list;
    }
}