package br.com.celk.esus.query;

import br.com.celk.unidadesaude.esus.cds.interfaces.dto.ConsultaDetalheMedicamentoPublicoDTO;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.dao.AliasToBeanNestedResultTransformer;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.type.DoubleType;
import org.hibernate.type.StringType;

import java.util.List;

/**
 * Created by murilo.
 */
public class QueryDetalhesListaPublicaMedicamento extends CommandQueryPager<QueryDetalhesListaPublicaMedicamento> {

    private String param;

    private List<ConsultaDetalheMedicamentoPublicoDTO> result;

    public QueryDetalhesListaPublicaMedicamento(String param) {
        this.param = param;
        this.setRealizarCountListaPublica(RepositoryComponentDefault.SIM_LONG);
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(ConsultaDetalheMedicamentoPublicoDTO.class.getName());
        hql.setUseSQL(true);

        hql.addToSelect("emp.descricao", "descricao");
        hql.addToSelect("emp.rua", "rua");
        hql.addToSelect("emp.bairro", "bairro");
        hql.addToSelect("emp.complemento", "complemento");
        hql.addToSelect("emp.telefone", "telefone");
        hql.addToSelect("emp.numero", "numero");
        hql.addToSelect("(sum(case when grupoEstoque.dt_validade >= current_date and emp.flag_lista_medicamento_publico = :flagListaMedicamentoPublicoSim then coalesce(grupoEstoque.estoque_fisico, 0) else 0 end) - sum(case when grupoEstoque.dt_validade >= current_date and emp.flag_lista_medicamento_publico = :flagListaMedicamentoPublicoSim then coalesce(grupoEstoque.estoque_reservado , 0) else 0 end))", "saldoDisponivel");

        hql.addToFrom("estoque_empresa estoqueEmpresa" +
                "  join produtos produto on produto.cod_pro = estoqueEmpresa.cod_pro" +
                "  join subgrupo subg on subg.cod_sub = produto.cod_sub and produto.cod_gru = subg.cod_gru" +
                "  join empresa emp on estoqueEmpresa.empresa = emp.empresa" +
                "  left join grupo_estoque grupoEstoque on grupoEstoque.empresa = estoqueEmpresa.empresa and grupoEstoque.cod_pro = estoqueEmpresa.cod_pro");

        hql.addToWhereWhithAnd("(estoqueEmpresa.estoque_fisico - estoqueEmpresa.estoque_reservado) >= :valorEstoque");
        hql.addToWhereWhithAnd("estoqueEmpresa.flag_ativo = :valorAtivo" );
        hql.addToWhereWhithAnd("produto.cod_pro = :valorProduto");
        hql.addToWhereWhithAnd("emp.flag_lista_medicamento_publico = :flagListaMedicamentoPublicoSim");
        hql.addToWhereWhithAnd("((coalesce(subg.flag_lista_medicamento_publico, :flagListaMedicamentoPublicoNao) = :flagListaMedicamentoPublicoSim and coalesce(produto.flag_lista_medicamento_publico, :flagListaMedicamentoPublicoSim) = :flagListaMedicamentoPublicoSim)"
                + " or"
                + " (coalesce(subg.flag_lista_medicamento_publico, :flagListaMedicamentoPublicoNao) = :flagListaMedicamentoPublicoNao and coalesce(produto.flag_lista_medicamento_publico, :flagListaMedicamentoPublicoNao) = :flagListaMedicamentoPublicoSim))");

        hql.addToGroup("emp.descricao");
        hql.addToGroup("emp.rua");
        hql.addToGroup("emp.bairro");
        hql.addToGroup("emp.complemento");
        hql.addToGroup("emp.telefone");
        hql.addToGroup("emp.numero");

        hql.addToOrder("saldoDisponivel desc");
    }

    @Override
    protected void setParameters(Query query) {
        super.setParameters(query);
        query.setParameter("valorEstoque", 0L);
        query.setParameter("valorAtivo", RepositoryComponentDefault.SIM);
        query.setParameter("flagListaMedicamentoPublicoSim", RepositoryComponentDefault.SIM_LONG);
        query.setParameter("flagListaMedicamentoPublicoNao", RepositoryComponentDefault.NAO_LONG);
        if (param != null) {
            query.setParameter("valorProduto", param);
        }
    }

    @Override
    protected Object executeQuery(Query query) {
        SQLQuery sql = (SQLQuery) query;
        sql.addScalar("descricao", StringType.INSTANCE)
                .addScalar( "rua", StringType.INSTANCE)
                .addScalar( "bairro", StringType.INSTANCE)
                .addScalar( "complemento", StringType.INSTANCE)
                .addScalar( "telefone", StringType.INSTANCE)
                .addScalar( "numero", StringType.INSTANCE)
                .addScalar( "saldoDisponivel", DoubleType.INSTANCE)

                .setResultTransformer(new AliasToBeanNestedResultTransformer(ConsultaDetalheMedicamentoPublicoDTO.class, super.getHQL().getPropBindingList()));

        result = sql.list();
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = (List<ConsultaDetalheMedicamentoPublicoDTO>) result;
    }
}