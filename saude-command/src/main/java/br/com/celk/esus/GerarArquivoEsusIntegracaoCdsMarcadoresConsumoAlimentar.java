package br.com.celk.esus;

import br.com.celk.bo.esus.interfaces.facade.EsusFacade;
import br.com.celk.bo.hospital.endereco.EnderecoHelper;
import br.com.celk.helper.IntegracaoCdsHelper;
import br.com.celk.unidadesaude.esus.cds.interfaces.dto.QueryConsultaEsusIntegracaoCdsDTO;
import br.com.celk.unidadesaude.esus.cds.interfaces.dto.QueryConsultaEsusIntegracaoCdsDTOParam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Valor;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaMarcadoresConsumoAlimentar;
import br.com.ksisolucoes.vo.esus.CboFichaEsus;
import br.com.ksisolucoes.vo.esus.CboFichaEsusItem;
import br.com.ksisolucoes.vo.esus.dto.EsusValidacoesFichasDTOParam;
import br.com.ksisolucoes.util.esus.EsusValidacoesMarcadoresConsumoAlimentarHelper;
import br.gov.saude.esus.cds.transport.generated.thrift.common.UnicaLotacaoHeaderThrift;
import br.gov.saude.esus.cds.transport.generated.thrift.consumoalimentar.*;
import br.gov.saude.esus.transport.common.generated.thrift.DadoTransporteThrift;
import org.apache.commons.lang.StringUtils;
import org.apache.thrift.TException;
import org.jrimum.utilix.Objects;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.ksisolucoes.util.Data.getMesDiferenca;

/**
 * Created by sulivan on 16/08/17.
 */
public class GerarArquivoEsusIntegracaoCdsMarcadoresConsumoAlimentar extends GerarArquivoEsusIntegracaoCds implements IExportacaoEsusDetalhado {

    public GerarArquivoEsusIntegracaoCdsMarcadoresConsumoAlimentar(ExportacaoEsusDTOParam param) {
        this.param = param;
        this.integrationName = "Marcadores de Consumo Alimentar";
    }

    @Override
    protected List<QueryConsultaEsusIntegracaoCdsDTO> getIntegrations(QueryConsultaEsusIntegracaoCdsDTOParam queryParam) throws DAOException, ValidacaoException {
        return BOFactory.getBO(EsusFacade.class).queryConsultaEsusIntegracaoCdsMarcadoresConsumoAlimentar(queryParam);
    }

    @Override
    protected boolean isIntegrationInvalid(QueryConsultaEsusIntegracaoCdsDTO integration, Empresa centralCareUnit) throws ValidacaoException, DAOException {
        EsusValidacoesFichasDTOParam validationParam = getValidationParam(integration);

        String inconsistencies = "";

        inconsistencies += EsusValidacoesMarcadoresConsumoAlimentarHelper.validate(validationParam);

        if (!inconsistencies.isEmpty()) {
            addInconsistency(inconsistencies, integration.getEsusIntegracaoCds());
            return true;
        }

        return false;
    }

    private EsusValidacoesFichasDTOParam getValidationParam(QueryConsultaEsusIntegracaoCdsDTO integration) {
        EsusValidacoesFichasDTOParam validationParam = new EsusValidacoesFichasDTOParam();
        List<CboFichaEsusItem> cboFichaEsusItemList = getCboFichaEsusItems(CboFichaEsus.TipoFicha.MARCADORES_CONSUMO_ALIMENTAR.value());
        EsusFichaMarcadoresConsumoAlimentar esusFichaMarcadoresConsumoAlimentar = integration.getEsusIntegracaoCds().getEsusFichaMarcadoresConsumoAlimentar();

        validationParam.setCboFichaEsusItemList(cboFichaEsusItemList);
        validationParam.setRetorno(EsusValidacoesFichasDTOParam.Retorno.INCONSISTENCIA);
        validationParam.setEmpresa(esusFichaMarcadoresConsumoAlimentar.getEmpresa());
        validationParam.setProfissional(esusFichaMarcadoresConsumoAlimentar.getProfissional());
        validationParam.setEsusFichaMarcadoresConsumoAlimentar(esusFichaMarcadoresConsumoAlimentar);
        validationParam.setTabelaCbo(esusFichaMarcadoresConsumoAlimentar.getCbo());

        return validationParam;
    }

    @Override
    protected DadoTransporteThrift getDadoTransporteThrift(Empresa centralCareUnit, QueryConsultaEsusIntegracaoCdsDTO integration, String uuid) throws DAOException, ValidacaoException {
        EsusFichaMarcadoresConsumoAlimentar esusFichaMarcadoresConsumoAlimentar = integration.getEsusIntegracaoCds().getEsusFichaMarcadoresConsumoAlimentar();
        Empresa empresa = esusFichaMarcadoresConsumoAlimentar.getEmpresa();

        DadoTransporteThrift dadoTransporteThrift = new DadoTransporteThrift();
        dadoTransporteThrift.clear();

        dadoTransporteThrift.setUuidDadoSerializado(uuid);
        dadoTransporteThrift.setTipoDadoSerializado(IExportacaoEsus.TipoDadoSerializadoEsus.MARCADORES_CONSUMO_ALIMENTAR.getValue());
        dadoTransporteThrift.setCnesDadoSerializado(empresa.getCnes());
        dadoTransporteThrift.setCodIbge(EnderecoHelper.getCodigoIbgeComDV(empresa.getCidade().getCodigo()).toString());
        if (Objects.isNotNull(esusFichaMarcadoresConsumoAlimentar.getCodigoIne())) {
            dadoTransporteThrift.setIneDadoSerializado(esusFichaMarcadoresConsumoAlimentar.getCodigoIne());
        }

        try {
            byte[] serialize = serialize(getFichaConsumoAlimentarThrift(integration, uuid));
            dadoTransporteThrift.setDadoSerializado(serialize);
        } catch (TException ex) {
            throw new ValidacaoException(ex);
        }

        dadoTransporteThrift.setRemetente(IntegracaoCdsHelper.getDadoInstalacaoThriftRefatorado(centralCareUnit));
        dadoTransporteThrift.setOriginadora(IntegracaoCdsHelper.getDadoInstalacaoThriftRefatorado(empresa));
        dadoTransporteThrift.setVersao(IntegracaoCdsHelper.getVersaoThriftRefatorado());

        return dadoTransporteThrift;
    }

    private FichaConsumoAlimentarThrift getFichaConsumoAlimentarThrift(QueryConsultaEsusIntegracaoCdsDTO integration, String uuid) throws ValidacaoException, DAOException {
        EsusFichaMarcadoresConsumoAlimentar esusFichaMarcadoresConsumoAlimentar = integration.getEsusIntegracaoCds().getEsusFichaMarcadoresConsumoAlimentar();
        Empresa empresa = esusFichaMarcadoresConsumoAlimentar.getEmpresa();

        FichaConsumoAlimentarThrift fichaConsumoAlimentarThrift = new FichaConsumoAlimentarThrift();

        fichaConsumoAlimentarThrift.setHeaderTransport(getUnicaLotacaoHeaderThrift(integration)); // 1

        if (esusFichaMarcadoresConsumoAlimentar.getUsuarioCadsus() != null && StringUtils.isNotBlank(esusFichaMarcadoresConsumoAlimentar.getUsuarioCadsus().getCpf())) {
            fichaConsumoAlimentarThrift.setCpfCidadao(esusFichaMarcadoresConsumoAlimentar.getUsuarioCadsus().getCpf()); // 12
        } else if (esusFichaMarcadoresConsumoAlimentar.getNumeroCartao() != null) {
            fichaConsumoAlimentarThrift.setCnsCidadao(esusFichaMarcadoresConsumoAlimentar.getNumeroCartao().toString()); // 2
        }
        //removido na versão 5.3.0
//        fichaConsumoAlimentarThrift.setIdentificacaoUsuario(esusFichaMarcadoresConsumoAlimentar.getNomeUsuarioCadsus()); // 3
        fichaConsumoAlimentarThrift.setDataNascimento(esusFichaMarcadoresConsumoAlimentar.getDataNascimento().getTime()); // 4
        fichaConsumoAlimentarThrift.setSexo(esusFichaMarcadoresConsumoAlimentar.getSexo());
        if (empresa.getLocalAtendimento() == null){
            throw new ValidacaoException(Bundle.getStringApplication("msg_erro_local_atendimento_empresa_null", empresa.getDescricao(), empresa.getCodigo()));
        }

        fichaConsumoAlimentarThrift.setLocalAtendimento(empresa.getLocalAtendimento());

        long idadeEmMeses = getIdadeEmMeses(esusFichaMarcadoresConsumoAlimentar);

        if (idadeEmMeses < 6L && idadeEmMeses > -1L) {
            fichaConsumoAlimentarThrift.setPerguntasQuestionarioCriancasMenoresSeisMeses(getQuestionarioCriancasMenoresSeisMeses(esusFichaMarcadoresConsumoAlimentar)); // 7
        } else if (idadeEmMeses >= 6 && idadeEmMeses <= 23) {
            fichaConsumoAlimentarThrift.setPerguntasQuestionarioCriancasDeSeisVinteTresMeses(getQuestionarioCriancasDeSeisVinteTresMeses(esusFichaMarcadoresConsumoAlimentar)); // 8
        } else {
            fichaConsumoAlimentarThrift.setPerguntasQuestionarioCriancasComMaisDoisAnos(getQuestionarioCriancasComMaisDoisAnos(esusFichaMarcadoresConsumoAlimentar)); // 9
        }

        fichaConsumoAlimentarThrift.setUuidFicha(uuid); // 10
        fichaConsumoAlimentarThrift.setTpCdsOrigem(3); // 11

        return fichaConsumoAlimentarThrift;
    }

    private long getIdadeEmMeses(EsusFichaMarcadoresConsumoAlimentar esusFicha) {
        long idadeEmMeses = -1L;
        try {
            idadeEmMeses = getMesDiferenca(esusFicha.getDataNascimento(), esusFicha.getDataAtendimento());
        } catch (ParseException ex) {
            Logger.getLogger(GerarArquivoEsusIntegracaoCdsMarcadoresConsumoAlimentar.class.getName()).log(Level.SEVERE, null, ex);
        }
        return idadeEmMeses;
    }

    private UnicaLotacaoHeaderThrift getUnicaLotacaoHeaderThrift(QueryConsultaEsusIntegracaoCdsDTO integration) throws DAOException, ValidacaoException {
        EsusFichaMarcadoresConsumoAlimentar esusFichaMarcadoresConsumoAlimentar = integration.getEsusIntegracaoCds().getEsusFichaMarcadoresConsumoAlimentar();
        Empresa empresa = esusFichaMarcadoresConsumoAlimentar.getEmpresa();
        Profissional profissional = esusFichaMarcadoresConsumoAlimentar.getProfissional();

        UnicaLotacaoHeaderThrift unicaLotacaoHeaderThrift = new UnicaLotacaoHeaderThrift();
        unicaLotacaoHeaderThrift.setProfissionalCNS(profissional.getCodigoCns());
        unicaLotacaoHeaderThrift.setCnes(empresa.getCnes());
        unicaLotacaoHeaderThrift.setCboCodigo_2002(esusFichaMarcadoresConsumoAlimentar.getCbo().getCbo());
        if (esusFichaMarcadoresConsumoAlimentar.getCodigoIne() != null) {
            unicaLotacaoHeaderThrift.setIne(esusFichaMarcadoresConsumoAlimentar.getCodigoIne());
        }

        unicaLotacaoHeaderThrift.setCodigoIbgeMunicipio(EnderecoHelper.getCodigoIbgeComDV(empresa.getCidade().getCodigo()).toString());
        unicaLotacaoHeaderThrift.setDataAtendimento(esusFichaMarcadoresConsumoAlimentar.getDataAtendimento().getTime());
        return unicaLotacaoHeaderThrift;
    }

    private List<PerguntaQuestionarioCriancasComMaisDoisAnosThrift> getQuestionarioCriancasComMaisDoisAnos(EsusFichaMarcadoresConsumoAlimentar esusFicha) {
        List<PerguntaQuestionarioCriancasComMaisDoisAnosThrift> perguntas = new ArrayList<>();

        perguntas.add(getPerguntaMaisDoisAnosUnicaResposta(PerguntaCriancasComMaisDoisAnosEnumThrift.VOCE_TEM_COSTUME_DE_REALIZAR_AS_REFEICOES_ASSISTINDO_TV_MEXENDO_NO_COMPUTADOR_E_OU_CELULAR, esusFicha.getRefeicoesUsandoTv()));
        perguntas.add(getPerguntaMaisDoisAnosMultiplaResposta(PerguntaCriancasComMaisDoisAnosEnumThrift.QUAIS_REFEICOES_VOCE_FAZ_AO_LONGO_DO_DIA, esusFicha.getRefeicoesLongoDia()));
        perguntas.add(getPerguntaMaisDoisAnosUnicaResposta(PerguntaCriancasComMaisDoisAnosEnumThrift.FEIJAO, esusFicha.getFeijao14()));
        perguntas.add(getPerguntaMaisDoisAnosUnicaResposta(PerguntaCriancasComMaisDoisAnosEnumThrift.FRUTAS_FRESCAS, esusFicha.getFrutasFrescas()));
        perguntas.add(getPerguntaMaisDoisAnosUnicaResposta(PerguntaCriancasComMaisDoisAnosEnumThrift.VERDURAS_E_OU_LEGUMES, esusFicha.getVerdurasLegumes()));
        perguntas.add(getPerguntaMaisDoisAnosUnicaResposta(PerguntaCriancasComMaisDoisAnosEnumThrift.HAMBURGUER_E_OU_EMBUTIDOS, esusFicha.getHamburguerEmbutidos17()));
        perguntas.add(getPerguntaMaisDoisAnosUnicaResposta(PerguntaCriancasComMaisDoisAnosEnumThrift.BEBIDAS_ADOCADAS, esusFicha.getBebidasAdocadas18()));
        perguntas.add(getPerguntaMaisDoisAnosUnicaResposta(PerguntaCriancasComMaisDoisAnosEnumThrift.MACARRAO_INSTANTANEO_SALGADINHOS_BISCOITOS, esusFicha.getMacarraoSalgadinhosBiscoitos19()));
        perguntas.add(getPerguntaMaisDoisAnosUnicaResposta(PerguntaCriancasComMaisDoisAnosEnumThrift.BISCOITO_RECHEADO_DOCES_OU_GULOSEIMAS, esusFicha.getBiscoitoDocesGuloseimas20()));

        return perguntas;
    }

    private PerguntaQuestionarioCriancasComMaisDoisAnosThrift getPerguntaMaisDoisAnosUnicaResposta(PerguntaCriancasComMaisDoisAnosEnumThrift question, Long answer) {
        PerguntaQuestionarioCriancasComMaisDoisAnosThrift perguntaQuestionarioCriancasComMaisDoisAnosThrift = new PerguntaQuestionarioCriancasComMaisDoisAnosThrift();

        perguntaQuestionarioCriancasComMaisDoisAnosThrift.setPergunta(question);
        if (answer != null) {
            perguntaQuestionarioCriancasComMaisDoisAnosThrift.setRespostaUnicaEscolha(RespostaUnicaEscolhaEnumThrift.findByValue(answer.intValue()));
        }

        return perguntaQuestionarioCriancasComMaisDoisAnosThrift;
    }

    private PerguntaQuestionarioCriancasComMaisDoisAnosThrift getPerguntaMaisDoisAnosMultiplaResposta(PerguntaCriancasComMaisDoisAnosEnumThrift question, Long answer) {
        PerguntaQuestionarioCriancasComMaisDoisAnosThrift perguntaQuestionarioCriancasComMaisDoisAnosThrift = new PerguntaQuestionarioCriancasComMaisDoisAnosThrift();

        perguntaQuestionarioCriancasComMaisDoisAnosThrift.setPergunta(question);
        if (answer != null) {
            perguntaQuestionarioCriancasComMaisDoisAnosThrift.setRespostaMultiplaEscolha(getRespostaMultiplaEscolha(answer));
        }

        return perguntaQuestionarioCriancasComMaisDoisAnosThrift;
    }

    public List<RespostaMultiplaEscolhaEnumThrift> getRespostaMultiplaEscolha(Long somatorio) {
        List<RespostaMultiplaEscolhaEnumThrift> respostaMultiplaEscolhaEnumThriftArrayList = new ArrayList<>();
        List<Long> respostas = Valor.resolveSomatorio(somatorio);

        for (Long resposta : respostas) {
            EsusFichaMarcadoresConsumoAlimentar.RefeicoesLongoDia refeicao = EsusFichaMarcadoresConsumoAlimentar.RefeicoesLongoDia.valeuOf(resposta);

            if (refeicao != null) {
                respostaMultiplaEscolhaEnumThriftArrayList.add(RespostaMultiplaEscolhaEnumThrift.findByValue(refeicao.valorEsus()));
            }
        }

        return respostaMultiplaEscolhaEnumThriftArrayList;
    }

    private List<PerguntaQuestionarioCriancasDeSeisVinteTresMesesThrift> getQuestionarioCriancasDeSeisVinteTresMeses(EsusFichaMarcadoresConsumoAlimentar esusFicha) {
        List<PerguntaQuestionarioCriancasDeSeisVinteTresMesesThrift> perguntas = new ArrayList<>();

        perguntas.add(getPerguntaDeSeisVinteTresMesesUnicaResposta(PerguntaCriancasDeSeisVinteTresMesesEnumThrift.A_CRIANCA_ONTEM_TOMOU_LEITE_PEITO, esusFicha.getLeitePeito21()));
        perguntas.add(getPerguntaDeSeisVinteTresMesesUnicaResposta(PerguntaCriancasDeSeisVinteTresMesesEnumThrift.ONTEM_A_CRIANCA_COMEU_FRUTA_INTEIRA_PEDACO_AMASSADO, esusFicha.getFrutaInteiraPedacoAmassado()));

        if (EsusFichaMarcadoresConsumoAlimentar.SimNaoNaoSabe.SIM.value().equals(esusFicha.getFrutaInteiraPedacoAmassado()) && esusFicha.getSeComeuFrutaQuantas() != null) {
            perguntas.add(getPerguntaDeSeisVinteTresMesesUnicaResposta(PerguntaCriancasDeSeisVinteTresMesesEnumThrift.SE_SIM_QUANTAS_VEZES, esusFicha.getSeComeuFrutaQuantas()));
        }

        perguntas.add(getPerguntaDeSeisVinteTresMesesUnicaResposta(PerguntaCriancasDeSeisVinteTresMesesEnumThrift.ONTEM_A_CRIANCA_COMEU_COMIDA_DE_SAL, esusFicha.getComeuComidaSal()));

        if (EsusFichaMarcadoresConsumoAlimentar.SimNaoNaoSabe.SIM.value().equals(esusFicha.getComeuComidaSal())) {
            perguntas.add(getPerguntaDeSeisVinteTresMesesUnicaResposta(PerguntaCriancasDeSeisVinteTresMesesEnumThrift.SE_COMEU_COMIDA_DE_SAL_QUANTAS_VEZES, esusFicha.getSeComeuComidaSalQuantas()));
            perguntas.add(getPerguntaDeSeisVinteTresMesesUnicaResposta(PerguntaCriancasDeSeisVinteTresMesesEnumThrift.SE_SIM_ESSA_COMIDA_FOI_OFERECIDA, esusFicha.getSeSimComidaFoiOferecida()));
        }

        perguntas.add(getPerguntaDeSeisVinteTresMesesUnicaResposta(PerguntaCriancasDeSeisVinteTresMesesEnumThrift.OUTRO_LEITE_QUE_NAO_LEITE_DO_PEITO, esusFicha.getLeiteNaoDoPeito()));
        perguntas.add(getPerguntaDeSeisVinteTresMesesUnicaResposta(PerguntaCriancasDeSeisVinteTresMesesEnumThrift.MINGAU_COM_LEITE, esusFicha.getMingauLeite()));
        perguntas.add(getPerguntaDeSeisVinteTresMesesUnicaResposta(PerguntaCriancasDeSeisVinteTresMesesEnumThrift.IOGURTE, esusFicha.getIogurte()));
        perguntas.add(getPerguntaDeSeisVinteTresMesesUnicaResposta(PerguntaCriancasDeSeisVinteTresMesesEnumThrift.LEGUMES, esusFicha.getLegumes()));
        perguntas.add(getPerguntaDeSeisVinteTresMesesUnicaResposta(PerguntaCriancasDeSeisVinteTresMesesEnumThrift.VEGETAL_OU_FRUTAS_COR_ALARANJADA_OU_FOLHAS_ESCURAS, esusFicha.getVegetalFrutasAlaranjada()));
        perguntas.add(getPerguntaDeSeisVinteTresMesesUnicaResposta(PerguntaCriancasDeSeisVinteTresMesesEnumThrift.VERDURA_DE_FOLHA, esusFicha.getVerduraFolha()));
        perguntas.add(getPerguntaDeSeisVinteTresMesesUnicaResposta(PerguntaCriancasDeSeisVinteTresMesesEnumThrift.CARNE, esusFicha.getCarne()));
        perguntas.add(getPerguntaDeSeisVinteTresMesesUnicaResposta(PerguntaCriancasDeSeisVinteTresMesesEnumThrift.FIGADO, esusFicha.getFigado()));
        perguntas.add(getPerguntaDeSeisVinteTresMesesUnicaResposta(PerguntaCriancasDeSeisVinteTresMesesEnumThrift.FEIJAO, esusFicha.getFeijao36()));
        perguntas.add(getPerguntaDeSeisVinteTresMesesUnicaResposta(PerguntaCriancasDeSeisVinteTresMesesEnumThrift.ARROZ_BATATA_INHAME_MANDIOCA_FARINHA_MACARRAO, esusFicha.getArrozBatataMacarrao()));
        perguntas.add(getPerguntaDeSeisVinteTresMesesUnicaResposta(PerguntaCriancasDeSeisVinteTresMesesEnumThrift.HAMBURGUER_E_OU_EMBUTIDOS, esusFicha.getHamburguerEmbutidos38()));
        perguntas.add(getPerguntaDeSeisVinteTresMesesUnicaResposta(PerguntaCriancasDeSeisVinteTresMesesEnumThrift.BEBIDAS_ADOCADAS, esusFicha.getBebidasAdocadas39()));
        perguntas.add(getPerguntaDeSeisVinteTresMesesUnicaResposta(PerguntaCriancasDeSeisVinteTresMesesEnumThrift.MACARRAO_INSTANTANEO_SALGADINHOS_BISCOITOS, esusFicha.getMacarraoSalgadinhosBiscoitos40()));
        perguntas.add(getPerguntaDeSeisVinteTresMesesUnicaResposta(PerguntaCriancasDeSeisVinteTresMesesEnumThrift.BISCOITO_RECHEADO_DOCES_OU_GULOSEIMAS, esusFicha.getBiscoitoDocesGuloseimas41()));

        return perguntas;
    }

    private PerguntaQuestionarioCriancasDeSeisVinteTresMesesThrift getPerguntaDeSeisVinteTresMesesUnicaResposta(PerguntaCriancasDeSeisVinteTresMesesEnumThrift question, Long answer) {
        PerguntaQuestionarioCriancasDeSeisVinteTresMesesThrift perguntaQuestionarioCriancasComMaisDoisAnosThrift = new PerguntaQuestionarioCriancasDeSeisVinteTresMesesThrift();

        perguntaQuestionarioCriancasComMaisDoisAnosThrift.setPergunta(question);
        if (answer != null) {
            perguntaQuestionarioCriancasComMaisDoisAnosThrift.setRespostaUnicaEscolha(RespostaUnicaEscolhaEnumThrift.findByValue(answer.intValue()));
        }

        return perguntaQuestionarioCriancasComMaisDoisAnosThrift;
    }

    private List<PerguntaQuestionarioCriancasMenoresSeisMesesThrift> getQuestionarioCriancasMenoresSeisMeses(EsusFichaMarcadoresConsumoAlimentar esusFicha) {
        List<PerguntaQuestionarioCriancasMenoresSeisMesesThrift> perguntas = new ArrayList<>();

        perguntas.add(getPerguntaMenoresSeisMesesUnicaResposta(PerguntaCriancasMenoresSeisMesesEnumThrift.A_CRIANCA_ONTEM_TOMOU_LEITE_DO_PEITO, esusFicha.getLeitePeito1()));
        perguntas.add(getPerguntaMenoresSeisMesesUnicaResposta(PerguntaCriancasMenoresSeisMesesEnumThrift.MINGAU, esusFicha.getMingau()));
        perguntas.add(getPerguntaMenoresSeisMesesUnicaResposta(PerguntaCriancasMenoresSeisMesesEnumThrift.AGUA_CHA, esusFicha.getAguaCha()));
        perguntas.add(getPerguntaMenoresSeisMesesUnicaResposta(PerguntaCriancasMenoresSeisMesesEnumThrift.LEITE_VACA, esusFicha.getLeiteVaca()));
        perguntas.add(getPerguntaMenoresSeisMesesUnicaResposta(PerguntaCriancasMenoresSeisMesesEnumThrift.FORMULA_INFANTIL, esusFicha.getFormulaInfantil()));
        perguntas.add(getPerguntaMenoresSeisMesesUnicaResposta(PerguntaCriancasMenoresSeisMesesEnumThrift.SUCO_FRUTA, esusFicha.getSucoFruta()));
        perguntas.add(getPerguntaMenoresSeisMesesUnicaResposta(PerguntaCriancasMenoresSeisMesesEnumThrift.FRUTA, esusFicha.getFruta()));
        perguntas.add(getPerguntaMenoresSeisMesesUnicaResposta(PerguntaCriancasMenoresSeisMesesEnumThrift.COMIDA_DE_SAL, esusFicha.getComidaSal()));
        perguntas.add(getPerguntaMenoresSeisMesesUnicaResposta(PerguntaCriancasMenoresSeisMesesEnumThrift.OUTROS_ALIMENTOS_BEBIDAS, esusFicha.getOutrosAlimentosBebidas()));

        return perguntas;
    }

    private PerguntaQuestionarioCriancasMenoresSeisMesesThrift getPerguntaMenoresSeisMesesUnicaResposta(PerguntaCriancasMenoresSeisMesesEnumThrift question, Long answer) {
        PerguntaQuestionarioCriancasMenoresSeisMesesThrift perguntaQuestionarioCriancasComMaisDoisAnosThrift = new PerguntaQuestionarioCriancasMenoresSeisMesesThrift();

        perguntaQuestionarioCriancasComMaisDoisAnosThrift.setPergunta(question);
        if (answer != null) {
            perguntaQuestionarioCriancasComMaisDoisAnosThrift.setRespostaUnicaEscolha(RespostaUnicaEscolhaEnumThrift.findByValue(answer.intValue()));
        }

        return perguntaQuestionarioCriancasComMaisDoisAnosThrift;
    }
}
