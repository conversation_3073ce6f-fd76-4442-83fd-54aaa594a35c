package br.com.celk.esus.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import org.hibernate.Query;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryConsultaAtendimentosArquivoEsus extends CommandQuery<QueryConsultaAtendimentosArquivoEsus> {

    private int first;
    private int limit;
    private DatePeriod periodo;
    private List<Atendimento> result;

    public QueryConsultaAtendimentosArquivoEsus(int first, int limit, DatePeriod periodo) {
        this.first = first;
        this.limit = limit;
        this.periodo = periodo;
    }

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("atendimento.codigo", "codigo");
        hql.addToSelect("atendimento.dataAtendimento", "dataAtendimento");
        hql.addToSelect("atendimento.status", "status");
        hql.addToSelect("atendimento.tipoDemanda", "tipoDemanda");
        hql.addToSelect("atendimento.vacinaEmDia", "vacinaEmDia");
        hql.addToSelect("atendimento.atendimentoPrincipal", "atendimentoPrincipal");
        hql.addToSelect("atendimento.nasfs", "nasfs");
        hql.addToSelect("atendimento.localAtendimento", "localAtendimento");

        hql.addToSelect("profissional.codigo", "profissional.codigo");
        hql.addToSelect("profissional.nome", "profissional.nome");
        hql.addToSelect("profissional.cpf", "profissional.cpf");
        hql.addToSelect("profissional.codigoCns", "profissional.codigoCns");

        hql.addToSelect("profissionalAuxiliar.codigo", "profissionalAuxiliar.codigo");
        hql.addToSelect("profissionalAuxiliar.nome", "profissionalAuxiliar.nome");
        hql.addToSelect("profissionalAuxiliar.cpf", "profissionalAuxiliar.cpf");
        hql.addToSelect("profissionalAuxiliar.codigoCns", "profissionalAuxiliar.codigoCns");

        hql.addToSelect("cidadeProfissional.codigo", "profissional.cidade.codigo");
        hql.addToSelect("cidadeProfissional.descricao", "profissional.cidade.descricao");
        hql.addToSelect("cidadeProfissional.codigoEsus", "profissional.cidade.codigoEsus");

        hql.addToSelect("empresa.codigo", "empresa.codigo");
        hql.addToSelect("empresa.descricao", "empresa.descricao");
        hql.addToSelect("empresa.cnes", "empresa.cnes");
        hql.addToSelect("empresa.cnpj", "empresa.cnpj");
        hql.addToSelect("empresa.localAtendimento", "empresa.localAtendimento");

        hql.addToSelect("cidade.codigo", "empresa.cidade.codigo");
        hql.addToSelect("cidade.descricao", "empresa.cidade.descricao");
        hql.addToSelect("cidade.codigoEsus", "empresa.cidade.codigoEsus");

        hql.addToSelect("usuarioCadsus.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.dataNascimento", "usuarioCadsus.dataNascimento");

        hql.addToSelect("classificacaoRisco.codigo", "classificacaoRisco.codigo");
        hql.addToSelect("classificacaoRisco.nivelGravidade", "classificacaoRisco.nivelGravidade");

        hql.addToSelect("classificacaoAtendimento.codigo", "classificacaoAtendimento.codigo");
        hql.addToSelect("classificacaoAtendimento.codigoEsus", "classificacaoAtendimento.codigoEsus");

        hql.addToSelect("naturezaProcuraTipoAtendimento.codigo", "naturezaProcuraTipoAtendimento.codigo");

        hql.addToSelect("conduta.codigo", "conduta.codigo");
        hql.addToSelect("conduta.codigoEsus", "conduta.codigoEsus");
        hql.addToSelect("conduta.classificacaoEsus", "conduta.classificacaoEsus");

        hql.addToSelect("tipoAtendimento.codigo", "naturezaProcuraTipoAtendimento.tipoAtendimento.codigo");
        hql.addToSelect("tipoAtendimento.tipoAtendimentoEsus", "naturezaProcuraTipoAtendimento.tipoAtendimento.tipoAtendimentoEsus");

        hql.addToSelect("cidPrincipal.codigo", "cidPrincipal.codigo");
        hql.addToSelect("cidPrincipal.descricao", "cidPrincipal.descricao");

        hql.addToSelect("tabelaCbo.cbo", "tabelaCbo.cbo");
        hql.addToSelect("tabelaCbo.descricao", "tabelaCbo.descricao");
        hql.addToSelect("tabelaCbo.nivelEnsino", "tabelaCbo.nivelEnsino");

        hql.addToSelect("ciap.codigo", "ciap.codigo");
        hql.addToSelect("ciap.referencia", "ciap.referencia");

        hql.setTypeSelect(Atendimento.class.getName());
        hql.addToFrom("Atendimento atendimento "
                + " left join atendimento.profissional profissional "
                + " left join atendimento.profissionalAuxiliar profissionalAuxiliar "
                + " left join atendimento.empresaBpa empresa "
                + " left join atendimento.cidPrincipal cidPrincipal "
                + " left join empresa.cidade cidade "
                + " left join profissional.cidade cidadeProfissional "
                + " left join atendimento.usuarioCadsus usuarioCadsus "
                + " left join atendimento.classificacaoRisco classificacaoRisco "
                + " left join atendimento.classificacaoAtendimento classificacaoAtendimento "
                + " left join atendimento.conduta conduta "
                + " left join atendimento.naturezaProcuraTipoAtendimento naturezaProcuraTipoAtendimento "
                + " left join naturezaProcuraTipoAtendimento.tipoAtendimento tipoAtendimento "
                + " left join atendimento.procedimentoCompetencia procedimentoCompetencia "
                + " left join procedimentoCompetencia.id.procedimento procedimento"
                + " left join atendimento.tabelaCbo tabelaCbo"
                + " left join atendimento.ciap ciap");

        hql.addToWhereWhithAnd("atendimento.status = ", Atendimento.STATUS_FINALIZADO);
        hql.addToWhereWhithAnd("tipoAtendimento.tipoClassificacao <> ", TipoAtendimento.TipoClassificacao.ODONTOLOGICA.value());
        hql.addToWhereWhithAnd("tipoAtendimento.tipoAtendimentoEsus is not null");
//        hql.addToWhereWhithAnd("coalesce(procedimento.flagFaturavel,'S') <> 'N' "); #9454

        if (periodo != null) {
            if (periodo.getDataInicial() != null) {
                hql.addToWhereWhithAnd("atendimento.dataAtendimento >= ", periodo.getDataInicial());
            }
            if (periodo.getDataFinal() != null) {
                hql.addToWhereWhithAnd("atendimento.dataAtendimento <= ", periodo.getDataFinal());
            }
        }

        hql.addToOrder("empresa.codigo asc");
        hql.addToOrder("profissional.codigo asc");
        hql.addToOrder("atendimento.dataAtendimento asc");
    }

    @Override
    public List<Atendimento> getResult() {
        return this.result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    protected void customQuery(Query query) {
        query.setFirstResult(first);
        query.setMaxResults(limit);
    }

}
