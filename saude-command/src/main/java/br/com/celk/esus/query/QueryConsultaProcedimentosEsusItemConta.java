package br.com.celk.esus.query;

import br.com.celk.esus.ProcedimentosEsusDTO;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.esus.ProcedimentoEsus;
import br.com.ksisolucoes.vo.prontuario.basico.ItemContaEsusDTO;

import java.util.List;
import java.util.Map;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.extract;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class QueryConsultaProcedimentosEsusItemConta extends CommandQuery<QueryConsultaProcedimentosEsusItemConta> {

    private List<ItemContaEsusDTO> itemContaEsusDTOList;
    private Long fichaIntegracao;
    private List<ProcedimentosEsusDTO> result;

    public QueryConsultaProcedimentosEsusItemConta(List<ItemContaEsusDTO> itemContaEsusDTOList, Long fichaIntegracao) {
        this.itemContaEsusDTOList = itemContaEsusDTOList;
        this.fichaIntegracao = fichaIntegracao;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        ProcedimentosEsusDTO proxy = on(ProcedimentosEsusDTO.class);

        hql.addToSelectAndGroupAndOrder("procedimento.codigo", path(proxy.getProcedimento().getCodigo()));
        hql.addToSelectAndGroupAndOrder("contaPaciente.codigo", path(proxy.getContaPaciente().getCodigo()));
        hql.addToSelectAndGroupAndOrder("procedimentoEsus.codigoEsus", path(proxy.getCodigoEsus()));
        hql.addToSelectAndGroupAndOrder("procedimentoEsus.codigoClassificacao", path(proxy.getCodigoClassificacao()));
        hql.addToSelectAndGroupAndOrder("profissional.codigo", path(proxy.getProfissional().getCodigo()));
        hql.addToSelect("count(*)", path(proxy.getQuantidade()));

        hql.setTypeSelect(ProcedimentosEsusDTO.class.getName());
        hql.addToFrom(" ItemContaPaciente itemContaPaciente "
                + " left join itemContaPaciente.procedimento procedimento " +
                " join itemContaPaciente.contaPaciente contaPaciente " +
                " left join itemContaPaciente.profissional profissional");

        hql.addToFrom("ProcedimentoEloEsus procedimentoEloEsus "
                + " left join procedimentoEloEsus.procedimentoEsus procedimentoEsus "
                + " left join procedimentoEloEsus.procedimento procedimento2 ");

        List<Long> codigoContaPacienteList = extract(itemContaEsusDTOList, on(ItemContaEsusDTO.class).getContaPaciente().getCodigo());
        hql.addToWhereWhithAnd("contaPaciente.codigo in ", codigoContaPacienteList);
        hql.addToWhereWhithAnd("procedimento =  procedimento2");
        hql.addToWhereWhithAnd("procedimentoEsus.codigoEsus is not null");
        hql.addToWhereWhithAnd("procedimentoEsus.fichaIntegracao = ", fichaIntegracao);
        if (ProcedimentoEsus.FichaIntegracao.PROCEDIMENTO.value().equals(fichaIntegracao)) {
            hql.addToWhereWhithAnd("procedimento.flagConsultaEsus = ", RepositoryComponentDefault.NAO_LONG);
        }
    }

    @Override
    public List<ProcedimentosEsusDTO> getResult() {
        return this.result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

}