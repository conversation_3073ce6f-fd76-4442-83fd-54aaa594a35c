package br.com.celk.esus;

import br.com.celk.bo.esus.interfaces.facade.EsusFacade;
import br.com.celk.bo.hospital.endereco.EnderecoHelper;
import br.com.celk.helper.IntegracaoCdsHelper;
import br.com.celk.unidadesaude.esus.cds.interfaces.dto.QueryConsultaEsusIntegracaoCdsDTO;
import br.com.celk.unidadesaude.esus.cds.interfaces.dto.QueryConsultaEsusIntegracaoCdsDTOParam;
import br.com.celk.util.Coalesce;
import br.com.celk.util.Util;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.esus.EsusHelper;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Equipe;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.esus.CboFichaEsus;
import br.com.ksisolucoes.vo.esus.CboFichaEsusItem;
import br.com.ksisolucoes.vo.esus.TermoRecusaCadastroIndividual;
import br.com.ksisolucoes.vo.esus.dto.EsusValidacoesFichasDTOParam;
import br.com.ksisolucoes.vo.esus.helper.EsusIntegracaoHelper;
import br.com.ksisolucoes.util.esus.EsusValidacoesFichaTermoRecusaCadastroIndividualHelper;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.gov.saude.esus.cds.transport.generated.thrift.cadastroindividual.CadastroIndividualThrift;
import br.gov.saude.esus.cds.transport.generated.thrift.cadastroindividual.IdentificacaoUsuarioCidadaoThrift;
import br.gov.saude.esus.cds.transport.generated.thrift.common.UnicaLotacaoHeaderThrift;
import br.gov.saude.esus.transport.common.generated.thrift.DadoTransporteThrift;
import org.apache.commons.lang.StringUtils;
import org.apache.thrift.TException;

import java.util.List;

/**
 * Created by laudecir on 10/10/17.
 */
public class GerarArquivoEsusIntegracaoCdsTermoRecusaIndividual extends GerarArquivoEsusIntegracaoCds implements IExportacaoEsusDetalhado {

    private static final long BRAZIL_COUNTRY_ID = 31L;

    public GerarArquivoEsusIntegracaoCdsTermoRecusaIndividual(ExportacaoEsusDTOParam param) {
        this.param = param;
        this.integrationName = "Recusa Individual";
    }

    @Override
    protected List<QueryConsultaEsusIntegracaoCdsDTO> getIntegrations(QueryConsultaEsusIntegracaoCdsDTOParam queryParam) throws DAOException, ValidacaoException {
        return BOFactory.getBO(EsusFacade.class).queryConsultaEsusIntegracaoCdsTermoRecusaIndividual(queryParam);
    }

    @Override
    protected boolean isIntegrationInvalid(QueryConsultaEsusIntegracaoCdsDTO integration, Empresa centralCareUnit) throws ValidacaoException, DAOException {
        EsusValidacoesFichasDTOParam validationParam = getEsusValidacoesFichasDTOParam(integration, centralCareUnit);

        String inconsistencia = EsusValidacoesFichaTermoRecusaCadastroIndividualHelper.executarValidacoesComuns(validationParam);
        inconsistencia += EsusIntegracaoHelper.validateCentralCareUnit(validationParam);

        TermoRecusaCadastroIndividual termoRecusaCadastroIndividual = integration.getEsusIntegracaoCds().getTermoRecusaCadastroIndividual();

        if (UsuarioCadsus.Nacionalidade.BRASILEIRO.value().equals(termoRecusaCadastroIndividual.getNacionalidade())
            && Util.isNotNull(termoRecusaCadastroIndividual.getCidadeNascimento())
            && termoRecusaCadastroIndividual.getCidadeNascimento().getCodigo().toString().length() != 6) {

            inconsistencia += Bundle.getStringApplication("msg_termo_recusa_cidade_nascimento_sem_codigo_IBGE_valido");
            inconsistencia += "</br>";

        }

        if (!inconsistencia.isEmpty()) {
            addInconsistency(inconsistencia, integration.getEsusIntegracaoCds());
            return true;
        }

        return false;
    }

    private EsusValidacoesFichasDTOParam getEsusValidacoesFichasDTOParam(QueryConsultaEsusIntegracaoCdsDTO integration, Empresa centralCareUnit) {
        List<CboFichaEsusItem> cboFichaEsusItemList = getCboFichaEsusItems(CboFichaEsus.TipoFicha.FICHA_CADASTRO_INDIVIDUAL.value());
        EsusValidacoesFichasDTOParam validationParam = getEsusValidacoesFichasDTOParam(cboFichaEsusItemList);

        TermoRecusaCadastroIndividual termoRecusaCadastroIndividual = integration.getEsusIntegracaoCds().getTermoRecusaCadastroIndividual();

        validationParam.setEmpresa(termoRecusaCadastroIndividual.getEmpresa());
        validationParam.setUnidadeCentral(centralCareUnit);
        validationParam.setProfissional(termoRecusaCadastroIndividual.getProfissional());
        validationParam.setTabelaCbo(termoRecusaCadastroIndividual.getTabelaCbo());
        validationParam.setTermoRecusaCadastroIndividual(termoRecusaCadastroIndividual);

        return validationParam;
    }

    @Override
    protected DadoTransporteThrift getDadoTransporteThrift(Empresa centralCareUnit, QueryConsultaEsusIntegracaoCdsDTO integration, String uuid) throws DAOException, ValidacaoException {
        Empresa careUnit = integration.getEsusIntegracaoCds().getTermoRecusaCadastroIndividual().getEmpresa();

        DadoTransporteThrift dadoTransporteThrift = new DadoTransporteThrift();
        dadoTransporteThrift.clear();

        dadoTransporteThrift.setUuidDadoSerializado(uuid);
        dadoTransporteThrift.setTipoDadoSerializado(IExportacaoEsus.TipoDadoSerializadoEsus.FICHA_CADASTRO_INDIVIDUAL.getValue());
        dadoTransporteThrift.setCnesDadoSerializado(careUnit.getCnes());
        dadoTransporteThrift.setCodIbge(EnderecoHelper.getCodigoIbgeComDV(careUnit.getCidade().getCodigo()).toString());

        Equipe team = integration.getEsusIntegracaoCds().getTermoRecusaCadastroIndividual().getEquipeProfissional().getEquipe();
        String cnesTeam = Util.isNotNull(team) ? team.getEquipeCnes() : null;

        dadoTransporteThrift.setIneDadoSerializado(StringUtils.leftPad(cnesTeam, 10, "0"));

        try {
            byte[] serialize = serialize(getCadastroIndividualThrift(integration, uuid, cnesTeam));
            dadoTransporteThrift.setDadoSerializado(serialize);
        } catch (TException ex) {
            throw new ValidacaoException(ex);
        }

        dadoTransporteThrift.setRemetente(IntegracaoCdsHelper.getDadoInstalacaoThriftRefatorado(centralCareUnit));
        dadoTransporteThrift.setOriginadora(IntegracaoCdsHelper.getDadoInstalacaoThriftRefatorado(careUnit));
        dadoTransporteThrift.setVersao(IntegracaoCdsHelper.getVersaoThriftRefatorado());

        return dadoTransporteThrift;
    }

    private CadastroIndividualThrift getCadastroIndividualThrift(QueryConsultaEsusIntegracaoCdsDTO integration, String uuid, String cnesTeam) throws DAOException, ValidacaoException {
        CadastroIndividualThrift cadastroIndividualThrift = new CadastroIndividualThrift();
        cadastroIndividualThrift.clear();

        cadastroIndividualThrift.setFichaAtualizada(false); // 4
        cadastroIndividualThrift.setIdentificacaoUsuarioCidadao(getIdentificacaoUsuarioCidadaoThrift(integration.getEsusIntegracaoCds().getTermoRecusaCadastroIndividual())); // 5
        cadastroIndividualThrift.setStatusTermoRecusaCadastroIndividualAtencaoBasica(true); // 7
        cadastroIndividualThrift.setTpCdsOrigem(0); // 8 // Utilizar valor 3 para sistemas terceiros
        cadastroIndividualThrift.setUuid(uuid); // 9
        cadastroIndividualThrift.setUuidFichaOriginadora(uuid); // 10
        cadastroIndividualThrift.setHeaderTransport(getUnicaLotacaoHeaderThrift(integration, cnesTeam)); // 13

        return cadastroIndividualThrift;
    }

    private IdentificacaoUsuarioCidadaoThrift getIdentificacaoUsuarioCidadaoThrift(TermoRecusaCadastroIndividual termoRecusaCadastroIndividual) throws DAOException, ValidacaoException {
        IdentificacaoUsuarioCidadaoThrift identificacaoUsuarioCidadaoThrift = new IdentificacaoUsuarioCidadaoThrift();

        setNameInfo(termoRecusaCadastroIndividual, identificacaoUsuarioCidadaoThrift);
        identificacaoUsuarioCidadaoThrift.setSexoCidadao(RepositoryComponentDefault.SEXO_MASCULINO.equals(termoRecusaCadastroIndividual.getSexo()) ? 0L : 1L); // 5.15
        setAccountableInfo(termoRecusaCadastroIndividual, identificacaoUsuarioCidadaoThrift);
        setParentsInfo(termoRecusaCadastroIndividual, identificacaoUsuarioCidadaoThrift);
        setContactInfo(termoRecusaCadastroIndividual, identificacaoUsuarioCidadaoThrift);
        setCitizenshipInfo(termoRecusaCadastroIndividual, identificacaoUsuarioCidadaoThrift);
        setMicroAreaInfo(termoRecusaCadastroIndividual, identificacaoUsuarioCidadaoThrift);

        if (Util.isNotNull(termoRecusaCadastroIndividual.getRaca()) && Util.isNotNull(termoRecusaCadastroIndividual.getRaca().getCodigo())) {
            identificacaoUsuarioCidadaoThrift.setRacaCorCidadao(EsusHelper.getRaceColor(termoRecusaCadastroIndividual.getRaca())); // 5.14
        }

        if (Util.isNotNull(termoRecusaCadastroIndividual.getDataNascimento())) {
            identificacaoUsuarioCidadaoThrift.setDataNascimentoCidadao(termoRecusaCadastroIndividual.getDataNascimento().getTime()); // 5.3
        }

        if (Util.isNotNull(termoRecusaCadastroIndividual.getCns())) {
            identificacaoUsuarioCidadaoThrift.setCnsCidadao(Coalesce.asString(termoRecusaCadastroIndividual.getCns())); // 5.9
        }

        if (Util.isNotNull(StringUtils.trimToNull(termoRecusaCadastroIndividual.getNis()))) {
            identificacaoUsuarioCidadaoThrift.setNumeroNisPisPasep(termoRecusaCadastroIndividual.getNis()); // 5.12
        }

        if (Util.isNotNull(termoRecusaCadastroIndividual.getEtniaIndigena())) {
            identificacaoUsuarioCidadaoThrift.setEtnia(Long.parseLong(termoRecusaCadastroIndividual.getEtniaIndigena().getCodigoSus())); // 5.17
        }

        if (Util.isNotNull(termoRecusaCadastroIndividual.getFlagForaArea())) {
            identificacaoUsuarioCidadaoThrift.setStForaArea(RepositoryComponentDefault.SIM_LONG.equals(termoRecusaCadastroIndividual.getFlagForaArea())); // 5.24
        }

        return identificacaoUsuarioCidadaoThrift;
    }

    private void setMicroAreaInfo(TermoRecusaCadastroIndividual termoRecusaCadastroIndividual, IdentificacaoUsuarioCidadaoThrift identificacaoUsuarioCidadaoThrift) {
        if (!identificacaoUsuarioCidadaoThrift.isStForaArea() && Util.isNotNull(termoRecusaCadastroIndividual.getEquipeMicroArea()) && Util.isNotNull(termoRecusaCadastroIndividual.getEquipeMicroArea().getMicroArea())) {
            if (termoRecusaCadastroIndividual.getEquipeMicroArea().getMicroArea().toString().length() < 2) {
                identificacaoUsuarioCidadaoThrift.setMicroArea(termoRecusaCadastroIndividual.getEquipeMicroArea().getMicroArea().toString().replaceFirst("", "0")); // 5.23
            } else {
                identificacaoUsuarioCidadaoThrift.setMicroArea(termoRecusaCadastroIndividual.getEquipeMicroArea().getMicroArea().toString()); // 5.23
            }
        }
    }

    private void setCitizenshipInfo(TermoRecusaCadastroIndividual termoRecusaCadastroIndividual, IdentificacaoUsuarioCidadaoThrift identificacaoUsuarioCidadaoThrift) throws DAOException, ValidacaoException {
        if (Util.isNotNull(termoRecusaCadastroIndividual.getNacionalidade())) {
            identificacaoUsuarioCidadaoThrift.setNacionalidadeCidadao(termoRecusaCadastroIndividual.getNacionalidade().intValue()); // 5.6
        }

        if (Util.isNotNull(termoRecusaCadastroIndividual.getNacionalidade())) {
            if (UsuarioCadsus.Nacionalidade.NATURALIZADO.value().equals(termoRecusaCadastroIndividual.getNacionalidade())) {
                setNaturalizedInfo(termoRecusaCadastroIndividual, identificacaoUsuarioCidadaoThrift);
            } else if (UsuarioCadsus.Nacionalidade.ESTRANGEIRO.value().equals(termoRecusaCadastroIndividual.getNacionalidade())) {
                setForeignInfo(termoRecusaCadastroIndividual, identificacaoUsuarioCidadaoThrift);
            } else if (UsuarioCadsus.Nacionalidade.BRASILEIRO.value().equals(termoRecusaCadastroIndividual.getNacionalidade())) {
                setBrasilianInfo(termoRecusaCadastroIndividual, identificacaoUsuarioCidadaoThrift);
            }
        }
    }

    private void setBrasilianInfo(TermoRecusaCadastroIndividual termoRecusaCadastroIndividual, IdentificacaoUsuarioCidadaoThrift identificacaoUsuarioCidadaoThrift) throws DAOException, ValidacaoException {
        identificacaoUsuarioCidadaoThrift.setPaisNascimento(BRAZIL_COUNTRY_ID); // 5.13

        if (Util.isNotNull(termoRecusaCadastroIndividual.getCidadeNascimento())) {
            identificacaoUsuarioCidadaoThrift.setCodigoIbgeMunicipioNascimento(EnderecoHelper.getCodigoIbgeComDV(termoRecusaCadastroIndividual.getCidadeNascimento().getCodigo()).toString()); // 5.2
        }
    }

    private void setForeignInfo(TermoRecusaCadastroIndividual termoRecusaCadastroIndividual, IdentificacaoUsuarioCidadaoThrift identificacaoUsuarioCidadaoThrift) {
        if (Util.isNotNull(termoRecusaCadastroIndividual.getPaisNascimento()) && Util.isNotNull(termoRecusaCadastroIndividual.getPaisNascimento().getCodigoEsus())) {
            identificacaoUsuarioCidadaoThrift.setPaisNascimento(termoRecusaCadastroIndividual.getPaisNascimento().getCodigoEsus()); // 5.13
        }
        if (Util.isNotNull(termoRecusaCadastroIndividual.getDataEntradaBrasil())) {
            identificacaoUsuarioCidadaoThrift.setDtEntradaBrasil(termoRecusaCadastroIndividual.getDataEntradaBrasil().getTime()); // 5.22
        }
    }

    private void setNaturalizedInfo(TermoRecusaCadastroIndividual termoRecusaCadastroIndividual, IdentificacaoUsuarioCidadaoThrift identificacaoUsuarioCidadaoThrift) {
        if (Util.isNotNull(termoRecusaCadastroIndividual.getDataNaturalizado())) {
            identificacaoUsuarioCidadaoThrift.setDtNaturalizacao(termoRecusaCadastroIndividual.getDataNaturalizado().getTime()); // 5.20
        }

        if (Util.isNotNull(StringUtils.trimToNull(termoRecusaCadastroIndividual.getPortariaNaturalizacao()))) {
            identificacaoUsuarioCidadaoThrift.setPortariaNaturalizacao(termoRecusaCadastroIndividual.getPortariaNaturalizacao()); // 5.21
        }
    }

    private void setContactInfo(TermoRecusaCadastroIndividual termoRecusaCadastroIndividual, IdentificacaoUsuarioCidadaoThrift identificacaoUsuarioCidadaoThrift) {
        if (Util.isNotNull(StringUtils.trimToNull(termoRecusaCadastroIndividual.getEmail()))) {
            identificacaoUsuarioCidadaoThrift.setEmailCidadao(termoRecusaCadastroIndividual.getEmail()); // 5.5
        }

        if (Util.isNotNull(StringUtils.trimToNull(termoRecusaCadastroIndividual.getCelular()))) {
            identificacaoUsuarioCidadaoThrift.setTelefoneCelular(termoRecusaCadastroIndividual.getCelular()); // 5.11
        }
    }

    private void setParentsInfo(TermoRecusaCadastroIndividual termoRecusaCadastroIndividual, IdentificacaoUsuarioCidadaoThrift identificacaoUsuarioCidadaoThrift) {
        if (Util.isNotNull(termoRecusaCadastroIndividual.getMaeDesconhecido())) {
            identificacaoUsuarioCidadaoThrift.setDesconheceNomeMae(RepositoryComponentDefault.SIM_LONG.equals(termoRecusaCadastroIndividual.getMaeDesconhecido())); // 5.4
        }

        if (!identificacaoUsuarioCidadaoThrift.isDesconheceNomeMae() && Util.isNotNull(termoRecusaCadastroIndividual.getNomeMae())) {
            identificacaoUsuarioCidadaoThrift.setNomeMaeCidadao(Util.removerCaracteresEspeciais(termoRecusaCadastroIndividual.getNomeMae(), false, true, true, false)); // 5.8
        }

        if (Util.isNotNull(termoRecusaCadastroIndividual.getPaiDesconhecido())) {
            identificacaoUsuarioCidadaoThrift.setDesconheceNomePai(RepositoryComponentDefault.SIM_LONG.equals(termoRecusaCadastroIndividual.getPaiDesconhecido())); // 5.19
        }

        if (!identificacaoUsuarioCidadaoThrift.isDesconheceNomePai()) {
            identificacaoUsuarioCidadaoThrift.setNomePaiCidadao(Util.removerCaracteresEspeciais(termoRecusaCadastroIndividual.getNomePai(), false, true, true, false)); // 5.18
        }
    }

    private void setAccountableInfo(TermoRecusaCadastroIndividual termoRecusaCadastroIndividual, IdentificacaoUsuarioCidadaoThrift identificacaoUsuarioCidadaoThrift) {
        if (Util.isNotNull(termoRecusaCadastroIndividual.getResponsavel())) {
            identificacaoUsuarioCidadaoThrift.setStatusEhResponsavel(RepositoryComponentDefault.SIM_LONG.equals(termoRecusaCadastroIndividual.getResponsavel())); // 5.16
        }

        if (!identificacaoUsuarioCidadaoThrift.isStatusEhResponsavel() && Util.isNotNull(termoRecusaCadastroIndividual.getCnsResponsavel())) {
            identificacaoUsuarioCidadaoThrift.setCnsResponsavelFamiliar(Coalesce.asString(termoRecusaCadastroIndividual.getCnsResponsavel())); // 5.10
        }
    }

    private void setNameInfo(TermoRecusaCadastroIndividual termoRecusaCadastroIndividual, IdentificacaoUsuarioCidadaoThrift identificacaoUsuarioCidadaoThrift) {
        if (Util.isNotNull(StringUtils.trimToNull(termoRecusaCadastroIndividual.getNome()))) {
            identificacaoUsuarioCidadaoThrift.setNomeCidadao(Util.removerCaracteresEspeciais(termoRecusaCadastroIndividual.getNome(), false, true, true, false)); // 5.7
        }

        if (Util.isNotNull(StringUtils.trimToNull(termoRecusaCadastroIndividual.getNomeSocial()))) {
            identificacaoUsuarioCidadaoThrift.setNomeSocial(Util.removerCaracteresEspeciais(termoRecusaCadastroIndividual.getNomeSocial(), false, true, true, false)); // 5.1
        }
    }

    private UnicaLotacaoHeaderThrift getUnicaLotacaoHeaderThrift(QueryConsultaEsusIntegracaoCdsDTO integration, String cnesTeam) throws DAOException, ValidacaoException {
        Empresa careUnit = integration.getEsusIntegracaoCds().getTermoRecusaCadastroIndividual().getEmpresa();
        Profissional professional = integration.getEsusIntegracaoCds().getTermoRecusaCadastroIndividual().getProfissional();
        TabelaCbo tabelaCbo = integration.getEsusIntegracaoCds().getTermoRecusaCadastroIndividual().getTabelaCbo();
        TermoRecusaCadastroIndividual termoRecusaCadastroIndividual = integration.getEsusIntegracaoCds().getTermoRecusaCadastroIndividual();

        UnicaLotacaoHeaderThrift unicaLotacaoHeaderThrift = new UnicaLotacaoHeaderThrift();

        unicaLotacaoHeaderThrift.setProfissionalCNS(professional.getCodigoCns()); // 13.1
        unicaLotacaoHeaderThrift.setCboCodigo_2002(tabelaCbo.getCbo()); //13.2
        unicaLotacaoHeaderThrift.setCnes(careUnit.getCnes()); // 13.3
        unicaLotacaoHeaderThrift.setIne(StringUtils.leftPad(cnesTeam, 10, "0")); // 13.4
        unicaLotacaoHeaderThrift.setDataAtendimento(termoRecusaCadastroIndividual.getDataPreenchimento().getTime()); // 13.5
        unicaLotacaoHeaderThrift.setCodigoIbgeMunicipio(EnderecoHelper.getCodigoIbgeComDV(careUnit.getCidade().getCodigo()).toString()); // 13.6

        return unicaLotacaoHeaderThrift;
    }
}