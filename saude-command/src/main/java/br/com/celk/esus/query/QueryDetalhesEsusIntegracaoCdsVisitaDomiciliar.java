package br.com.celk.esus.query;

import br.com.celk.unidadesaude.esus.cds.interfaces.dto.DetalhesItensIntegracaoEsusDTO;
import br.com.celk.unidadesaude.esus.cds.interfaces.dto.DetalhesItensIntegracaoEsusDTOParam;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.VisitaDomiciliar;
import br.com.ksisolucoes.vo.esus.EsusIntegracaoCds;

import java.util.List;
import java.util.Map;

/**
 * Created by sulivan on 24/07/17.
 */
public class QueryDetalhesEsusIntegracaoCdsVisitaDomiciliar extends CommandQueryPager<QueryDetalhesEsusIntegracaoCdsVisitaDomiciliar> {

    private DetalhesItensIntegracaoEsusDTOParam param;

    public QueryDetalhesEsusIntegracaoCdsVisitaDomiciliar(DetalhesItensIntegracaoEsusDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(DetalhesItensIntegracaoEsusDTO.class.getName());

        hql.addToSelect("esusIntegracaoCds.codigo", "esusIntegracaoCds.codigo");
        hql.addToSelect("esusIntegracaoCds.uuid", "esusIntegracaoCds.uuid");
        hql.addToSelect("esusIntegracaoCds.descricaoInconsistenciaEsus", "esusIntegracaoCds.descricaoInconsistenciaEsus");

        hql.addToSelect("visitaDomiciliar.codigo", "esusIntegracaoCds.visitaDomiciliar.codigo");
        hql.addToSelect("visitaDomiciliar.dataVisita", "esusIntegracaoCds.visitaDomiciliar.dataVisita");

        hql.addToSelect("usuarioCadsus.codigo", "esusIntegracaoCds.visitaDomiciliar.usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "esusIntegracaoCds.visitaDomiciliar.usuarioCadsus.nome");

        hql.addToSelect("empresa.codigo", "esusIntegracaoCds.visitaDomiciliar.empresa.codigo");
        hql.addToSelect("empresa.descricao", "esusIntegracaoCds.visitaDomiciliar.empresa.descricao");

        hql.addToSelect("profissional.codigo", "esusIntegracaoCds.visitaDomiciliar.profissional.codigo");
        hql.addToSelect("profissional.nome", "esusIntegracaoCds.visitaDomiciliar.profissional.nome");

        hql.addToSelect("enderecoDomicilio.codigo", "esusIntegracaoCds.visitaDomiciliar.enderecoDomicilio.codigo");
        hql.addToSelect("enderecoDomicilio.numeroFamilia", "esusIntegracaoCds.visitaDomiciliar.enderecoDomicilio.numeroFamilia");

        hql.addToSelect("equipeMicroArea.codigo", "esusIntegracaoCds.visitaDomiciliar.equipeMicroArea.codigo");
        hql.addToSelect("equipeMicroArea.microArea", "esusIntegracaoCds.visitaDomiciliar.equipeMicroArea.microArea");

        hql.addToSelect("equipeArea.codigo", "esusIntegracaoCds.visitaDomiciliar.equipeMicroArea.equipeArea.codigo");
        hql.addToSelect("equipeArea.descricao", "esusIntegracaoCds.visitaDomiciliar.equipeMicroArea.equipeArea.descricao");

        hql.addToFrom("EsusIntegracaoCds esusIntegracaoCds "
                + " left join esusIntegracaoCds.exportacaoEsusProcesso exportacaoEsusProcesso "
                + " left join esusIntegracaoCds.visitaDomiciliar visitaDomiciliar "
                + " left join visitaDomiciliar.empresa empresa "
                + " left join visitaDomiciliar.usuarioCadsus usuarioCadsus "
                + " left join visitaDomiciliar.enderecoDomicilio enderecoDomicilio "
                + " left join visitaDomiciliar.profissional profissional "
                + " left join visitaDomiciliar.equipeMicroArea equipeMicroArea"
                + " left join equipeMicroArea.equipeArea equipeArea"
                + " left join equipeArea.cidade cidade");

        hql.addToWhereWhithAnd("esusIntegracaoCds.tipo = ", EsusIntegracaoCds.Tipo.VISITA_DOMICILIAR.value());
        hql.addToWhereWhithAnd("exportacaoEsusProcesso.codigo = ", param.getCodigoExportacaoEsusProcesso());
        hql.addToWhereWhithAnd("usuarioCadsus.codigo = ", param.getCodigoUsuarioCadsus());

        hql.addToWhereWhithAnd(hql.getConsultaLiked("usuarioCadsus.nome", this.param.getPaciente()));
        hql.addToWhereWhithAnd("esusIntegracaoCds.uuid = ", param.getUuid());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("esusIntegracaoCds.descricaoInconsistenciaEsus", this.param.getInconsistencia()));
        hql.addToWhereWhithAnd("visitaDomiciliar.codigo = ", param.getCodigoVisitaDomiciliar());
        hql.addToWhereWhithAnd("visitaDomiciliar.dataVisita = ", param.getDataVisita());
        hql.addToWhereWhithAnd("empresa = ", param.getEmpresa());
        hql.addToWhereWhithAnd("enderecoDomicilio.numeroFamilia = ", param.getNumeroFamilia());

        if (RepositoryComponentDefault.SIM_LONG.equals(param.getForaDeArea())) {
            hql.addToWhereWhithAnd("visitaDomiciliar.tipoVisita = ", VisitaDomiciliar.VISITA_FORA_AREA);
        }

        hql.addToWhereWhithAnd("visitaDomiciliar.equipeMicroArea =", param.getEquipeMicroArea());

        if (param.getArea() != null) {
            hql.addToWhereWhithAnd("equipeArea.codigo =", param.getArea().getCodigo());
            hql.addToWhereWhithAnd("cidade.codigo =", param.getArea().getCidade().getCodigo());
        }

        if(RepositoryComponentDefault.SIM_LONG.equals(param.getComInconsistencia())) {
            hql.addToWhereWhithAnd("esusIntegracaoCds.descricaoInconsistenciaEsus is not null");
        } else if(RepositoryComponentDefault.NAO_LONG.equals(param.getComInconsistencia())){
            hql.addToWhereWhithAnd("esusIntegracaoCds.descricaoInconsistenciaEsus is null");
        }

        if (param.getPropSort() != null) {
            hql.addToOrder(param.getPropSort() + " " + (param.isAscending() ? "asc" : "desc"));
        } else {
            hql.addToOrder("esusIntegracaoCds.dataCadastro desc");
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}