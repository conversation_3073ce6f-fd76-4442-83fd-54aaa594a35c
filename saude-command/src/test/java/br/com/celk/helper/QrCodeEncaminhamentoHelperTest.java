package br.com.celk.helper;


import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.util.EncryptorUtils;
import br.com.ksisolucoes.util.QrCodeUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoEncaminhamento;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({QrCodeUtils.class, EncryptorUtils.class, LoadManager.class})
public class QrCodeEncaminhamentoHelperTest {

    @Mock
    LoadManager loadManagerMock;

    @Before
    public void setup() {
        PowerMockito.mockStatic(QrCodeUtils.class);
        PowerMockito.mockStatic(EncryptorUtils.class);
        PowerMockito.mockStatic(LoadManager.class);

        when(QrCodeUtils.getHost()).thenReturn("localhost:8080");
        when(EncryptorUtils.encrypt(anyString())).thenReturn("encrypted text");

        when(LoadManager.getInstance(any())).thenReturn(loadManagerMock);
        when(loadManagerMock.addProperty(any())).thenReturn(loadManagerMock);
        when(loadManagerMock.addParameter(any())).thenReturn(loadManagerMock);
        when(loadManagerMock.start()).thenReturn(loadManagerMock);
    }

    @Test
    public void shouldReturnUrl() {
        Assert.assertTrue("Should return Url", QrCodeEncaminhamentoHelper.getURL(123L).contains("validaQRCodeEncaminhamento"));
        Assert.assertTrue("Should return Url", QrCodeEncaminhamentoHelper.getURL(123L).contains("codigoEncaminhamento"));
    }

    @Test
    public void shouldVerifyIfShouldPrintQrCode() {
        Assert.assertFalse("Shouldn't print QrCode if couldn't verify the print flag", QrCodeEncaminhamentoHelper.shouldPrintQrCode(null));

        Encaminhamento encaminhamento = new Encaminhamento();

        when(loadManagerMock.getVO()).thenReturn(encaminhamento);
        Assert.assertFalse("Shouldn't print QrCode if couldn't verify the print flag", QrCodeEncaminhamentoHelper.shouldPrintQrCode(123L));

        encaminhamento.setTipoEncaminhamento(new TipoEncaminhamento());
        Assert.assertFalse("Shouldn't print QrCode if QrCode flag is null", QrCodeEncaminhamentoHelper.shouldPrintQrCode(123L));


        encaminhamento.getTipoEncaminhamento().setFlagQRCode(RepositoryComponentDefault.NAO_LONG);
        Assert.assertFalse("Shouldn't print QrCode if QrCode flag is 0L", QrCodeEncaminhamentoHelper.shouldPrintQrCode(123L));

        encaminhamento.getTipoEncaminhamento().setFlagQRCode(RepositoryComponentDefault.SIM_LONG);
        Assert.assertTrue("Should print QrCode if QrCode flag is 1L", QrCodeEncaminhamentoHelper.shouldPrintQrCode(123L));
    }

}