package br.com.celk.helper;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class LoadManagerUtils {
    
    private LoadManagerUtils() {}

    public static void mockLoadManager(LoadManager loadManager, Class clazz) throws ValidacaoException, DAOException {
        when(LoadManager.getInstance(clazz)).thenReturn(loadManager);
        when(loadManager.setId(any())).thenReturn(loadManager);
        when(loadManager.setStrategyRecovery(any())).thenReturn(loadManager);
        when(loadManager.addProperties(any())).thenReturn(loadManager);
        when(loadManager.addGroup(any())).thenReturn(loadManager);
        when(loadManager.addProperty(any())).thenReturn(loadManager);
        when(loadManager.addParameter(any())).thenReturn(loadManager);
        when(loadManager.addInterceptor(any())).thenReturn(loadManager);
        when(loadManager.addSorter(any())).thenReturn(loadManager);
        when(loadManager.setParameters(any())).thenReturn(loadManager);
        when(loadManager.setInterceptors(any())).thenReturn(loadManager);
        when(loadManager.setSorters(any())).thenReturn(loadManager);
        when(loadManager.setMaxResults(any())).thenReturn(loadManager);
        when(loadManager.setFirstResult(any())).thenReturn(loadManager);
        when(loadManager.start()).thenReturn(loadManager);
        when(loadManager.startLeitura()).thenReturn(loadManager);
        when(loadManager.startNewTransaction()).thenReturn(loadManager);
    }
}
