package br.com.celk.services.mobile.integracao.bindimportacao.usuario;

import br.com.celk.services.mobile.integracao.importarrecurso.ImpUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.hibernate.Session;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Date;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

@RunWith(PowerMockRunner.class)
@PrepareForTest({
        LoadManager.class,
        ImpUtil.class
})
public class UsuarioBindTest {
    @Mock
    Session sessionMock;
    @Mock
    LoadManager loadManagerMock;
    @Mock
    private ImpUtil impUtilMock;
    @Mock
    private Usuario usuarioMock;

    private UsuarioBind usuarioBind;

    private void mockLoadManager() {
        when(LoadManager.getInstance(Usuario.class)).thenReturn(loadManagerMock);
        when(loadManagerMock.setId(any())).thenReturn(loadManagerMock);
        when(loadManagerMock.start()).thenReturn(loadManagerMock);
        when(loadManagerMock.addProperties(ArgumentMatchers.<String[]>any())).thenReturn(loadManagerMock);
        when(loadManagerMock.addParameter(ArgumentMatchers.any())).thenReturn(loadManagerMock);
    }

    @Before
    public void setUp() throws Exception {
        mockStatic(LoadManager.class);
        mockLoadManager();
        usuarioBind = new UsuarioBind();
        usuarioBind.setCodigoSistema(1L);
    }

    @Test
    public void deveRetornarDataAceiteTermoUsoTest() {
        Usuario usuario = new Usuario();
        usuario.setAceiteTermoUsoMobile(RepositoryComponentDefault.SIM_LONG);

        when(loadManagerMock.getVO()).thenReturn(usuario);

        usuarioBind.setDataAceiteTermoUsoMobile(new Date());
        usuario = usuarioBind.customProperties(new Usuario(), new ImpUtil(sessionMock));

        Assert.assertEquals(RepositoryComponentDefault.SIM_LONG, usuario.getAceiteTermoUsoMobile());
        Assert.assertNotNull(usuario.getDataAceiteTermoUsoMobile());
    }

    @Test
    public void naoDeveRetornarDataAceiteTermoUsoTest() {
        Usuario usuario = new Usuario();
        when(loadManagerMock.getVO()).thenReturn(usuario);
        usuario = usuarioBind.customProperties(usuario, new ImpUtil(sessionMock));

        Assert.assertEquals(RepositoryComponentDefault.NAO_LONG, usuario.getAceiteTermoUsoMobile());
        Assert.assertNull(usuario.getDataAceiteTermoUsoMobile());


        usuario.setAceiteTermoUsoMobile(RepositoryComponentDefault.SIM_LONG);
        usuario = usuarioBind.customProperties(new Usuario(), new ImpUtil(sessionMock));

        Assert.assertEquals(RepositoryComponentDefault.NAO_LONG, usuario.getAceiteTermoUsoMobile());
        Assert.assertNull(usuario.getDataAceiteTermoUsoMobile());
    }
}