package br.com.celk.services.mobile.integracao.bindimportacao.nascidosvivos;

import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.FichaNascidoVivo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

@RunWith(PowerMockRunner.class)
@PrepareForTest({
        BOFactory.class
})
public class ImportarNascidosVivosTest {

    @Mock
    private Object fichaNascidoVivoMock;

    FichaNascidoVivo fichaNascidoVivo;
    NascidosVivosBind nascidosVivosBind;
    ImportarNascidosVivos importarNascidosVivos;

    @Before
    public void setUp() throws Exception {
        mockStatic(BOFactory.class);
        when(BOFactory.save(fichaNascidoVivoMock)).thenReturn(fichaNascidoVivoMock);

        fichaNascidoVivo = new FichaNascidoVivo();
        nascidosVivosBind = new NascidosVivosBind();
        importarNascidosVivos = new ImportarNascidosVivos(fichaNascidoVivo, nascidosVivosBind);
    }

    @Test(expected = ValidacaoException.class)
    public void executeDeveLancarExcecaoUsuarioCadsusNascidoNuloTest() throws Exception {
        importarNascidosVivos.execute(fichaNascidoVivo);
    }

    @Test(expected = ValidacaoException.class)
    public void executeDeveLancarExcecaoTipoPartoNuloTest() throws Exception{
        fichaNascidoVivo.setUsuarioCadsusNascido(getUsuarioCadsusNascidoVivo());
        importarNascidosVivos.execute(fichaNascidoVivo);
    }

    @Test(expected = ValidacaoException.class)
        public void executeDeveLancarExcecaoDuracaoGestacaoNuloTest() throws Exception{
        fichaNascidoVivo.setUsuarioCadsusNascido(getUsuarioCadsusNascidoVivo());
        nascidosVivosBind.setTipoParto(1l);
        importarNascidosVivos.execute(fichaNascidoVivo);
    }


    @Test(expected = ValidacaoException.class)
        public void executeDeveLancarExcecaoQtdConsultasPrenatalNuloTest() throws Exception{
        fichaNascidoVivo.setUsuarioCadsusNascido(getUsuarioCadsusNascidoVivo());
        nascidosVivosBind.setTipoParto(1l);
        nascidosVivosBind.setDuracaoGestacao(1l);
        importarNascidosVivos.execute(fichaNascidoVivo);
    }


    @Test(expected = ValidacaoException.class)
        public void executeDeveLancarExcecaoTipoGravidezNuloTest() throws Exception{
        fichaNascidoVivo.setUsuarioCadsusNascido(getUsuarioCadsusNascidoVivo());
        nascidosVivosBind.setTipoParto(1l);
        nascidosVivosBind.setDuracaoGestacao(1l);
        nascidosVivosBind.setQtdConsultasPrenatal(1l);
        //nascidosVivosBind.setTipoGravidez(1l);
        importarNascidosVivos.execute(fichaNascidoVivo);
    }

    @Test(expected = ValidacaoException.class)
    public void executeDeveLancarExcecaoPrematuroNuloTest() throws Exception{
        fichaNascidoVivo.setUsuarioCadsusNascido(getUsuarioCadsusNascidoVivo());
        nascidosVivosBind.setTipoParto(1l);
        nascidosVivosBind.setDuracaoGestacao(1l);
        nascidosVivosBind.setQtdConsultasPrenatal(1l);
        nascidosVivosBind.setTipoGravidez(1l);
        importarNascidosVivos.execute(fichaNascidoVivo);
    }

    @Test(expected = ValidacaoException.class)
    public void executeDeveLancarExcecaoUuidTabletAndCodigoSistemaNascidoVivoNuloTest() throws Exception{
        fichaNascidoVivo.setUsuarioCadsusNascido(getUsuarioCadsusNascidoVivo());
        nascidosVivosBind.setTipoParto(1l);
        nascidosVivosBind.setDuracaoGestacao(1l);
        nascidosVivosBind.setQtdConsultasPrenatal(1l);
        nascidosVivosBind.setTipoGravidez(1l);
        nascidosVivosBind.setPrematuro(1l);
        importarNascidosVivos.execute(fichaNascidoVivo);
    }

    @Test(expected = ValidacaoException.class)
    public void executeDeveLancarExcecaoUuidTabletAndCodigoSistemaMaeNuloTest() throws Exception{
        fichaNascidoVivo.setUsuarioCadsusNascido(getUsuarioCadsusNascidoVivo());
        nascidosVivosBind.setTipoParto(1l);
        nascidosVivosBind.setDuracaoGestacao(1l);
        nascidosVivosBind.setQtdConsultasPrenatal(1l);
        nascidosVivosBind.setTipoGravidez(1l);
        nascidosVivosBind.setPrematuro(1l);
        nascidosVivosBind.setCodigoSistemaNascido(1l);
        importarNascidosVivos.execute(fichaNascidoVivo);
    }

    private UsuarioCadsus getUsuarioCadsusNascidoVivo(){
        return new UsuarioCadsus(){{
            setCodigo(2L);
            setNome("LUIS ALVES RN");
        }};
    }

    private UsuarioCadsus getUsuarioCadsusMaeNascido(){
        return new UsuarioCadsus(){{
            setCodigo(2L);
            setNome("MAE DO LUIS ALVES");
        }};
    }
}