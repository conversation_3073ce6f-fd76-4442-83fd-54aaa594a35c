package br.com.celk.esus;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.util.esus.EsusValidacoesFichaCadastroDomiciliarHelper;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.cadsus.EnderecoDomicilioEsus;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.TipoLogradouroCadsus;
import br.com.ksisolucoes.vo.esus.dto.EsusValidacoesFichasDTOParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static br.com.ksisolucoes.vo.esus.dto.EsusValidacoesFichasDTOParam.Retorno.INCONSISTENCIA;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

@RunWith(PowerMockRunner.class)
@PrepareForTest({LoadManager.class})
public class EsusValidacoesFichaCadastroDomiciliarHelperTest {

    @Mock
    private LoadManager loadManager;

    private EsusValidacoesFichasDTOParam param;
    private List<String> listaCnsCadastrados;
    private List<String> listaCpfCadastrados;

    @Before
    public void setUp() throws Exception {
        mockLoadManager();

        listaCnsCadastrados = new ArrayList<>();
        listaCpfCadastrados = new ArrayList<>();

        param = new EsusValidacoesFichasDTOParam();
        param.setRetorno(INCONSISTENCIA);
        param.setResponsavelFamiliar(GerarArquivoEsusIntegracaoCdsTestHelper.getEsusFichaUsuarioCadsusDomicilio());
        param.setEsusFichaEnderecoDomicilioEsus(GerarArquivoEsusIntegracaoCdsDomicilioTestHelper.getEsusFichaEnderecoDomicilioEsus());
    }

    private void mockLoadManager() {
        mockStatic(LoadManager.class);

        when(LoadManager.getInstance(any())).thenReturn(loadManager);
        when(loadManager.setId(any())).thenReturn(loadManager);
        when(loadManager.addProperty(any())).thenReturn(loadManager);
        when(loadManager.start()).thenReturn(loadManager);
    }


    @Test
    public void familyAccountableCnsCpfCantBeRepeated() throws Exception {
        String inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateCpfCnsAccountableAlreadyUsed(param, listaCnsCadastrados, listaCpfCadastrados, EnderecoDomicilioEsus.TipoImovel.DOMICILIO.value());
        assertEquals("", inconsistency);

        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateCpfCnsAccountableAlreadyUsed(param, listaCnsCadastrados, listaCpfCadastrados, EnderecoDomicilioEsus.TipoImovel.DOMICILIO.value());
        assertEquals("Family accountable Cpf should not be set in more than one family", "CPF do responsável já cadastrado (***********)</br>", inconsistency);

        param.getResponsavelFamiliar().getUsuarioCadsusDomicilio().getUsuarioCadsus().setCpf(null);
        EsusValidacoesFichaCadastroDomiciliarHelper.validateCpfCnsAccountableAlreadyUsed(param, listaCnsCadastrados, listaCpfCadastrados, EnderecoDomicilioEsus.TipoImovel.DOMICILIO.value());
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateCpfCnsAccountableAlreadyUsed(param, listaCnsCadastrados, listaCpfCadastrados, EnderecoDomicilioEsus.TipoImovel.DOMICILIO.value());
        assertEquals("Family accountable Cns should not be set in more than one family", "CNS do responsável já cadastrado (***************)</br>", inconsistency);
    }

    @Test
    public void shouldValidateCareUnit() throws Exception {
        String inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateCareUnit(param);
        assertEquals("Care Unit should be informed", "Estabelecimento não informado</br>", inconsistency);

        param.setEmpresa(new Empresa());
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateCareUnit(param);
        assertEquals("Care Unit CNES and CNPJ/CPF should be informed", "Estabelecimento null sem CNES definido.</br>Estabelecimento null sem CNPJ/CPF definido.</br>", inconsistency);

        param.getEmpresa().setCnpj("cnpj");
        param.getEmpresa().setCnes("cnes");
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateCareUnit(param);
        assertEquals("Care Unit should be correct", "", inconsistency);
    }

    @Test
    public void shouldValidateAddress() throws Exception {
        param.getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus().getEnderecoDomicilio().getEnderecoUsuarioCadsus().setBairro(null);
        String inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateAddress(param);
        assertEquals("Neighborhood should be informed", "Sem bairro definido.</br>", inconsistency);

        param.getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus().getEnderecoDomicilio().getEnderecoUsuarioCadsus().setBairro("Neighborhood");
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateAddress(param);
        assertEquals("Neighborhood should be informed", "", inconsistency);

        param.getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus().getEnderecoDomicilio().getEnderecoUsuarioCadsus().setCep(null);
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateAddress(param);
        assertEquals("Zip code should be informed", "Sem CEP definido.</br>", inconsistency);
        param.getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus().getEnderecoDomicilio().getEnderecoUsuarioCadsus().setCep("zip code");
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateAddress(param);
        assertEquals("Zip code should be informed", "", inconsistency);

        param.getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus().getEnderecoDomicilio().getEnderecoUsuarioCadsus().setTipoLogradouro(null);
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateAddress(param);
        assertEquals("Street type should be informed", "Sem Tipo de Logradouro definido.</br>", inconsistency);
        param.getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus().getEnderecoDomicilio().getEnderecoUsuarioCadsus().setTipoLogradouro(new TipoLogradouroCadsus());
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateAddress(param);
        assertEquals("Street type should be informed", "", inconsistency);

        Cidade cidade = new Cidade();
        cidade.setEstado(new Estado());

        param.getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus().getEnderecoDomicilio().getEnderecoUsuarioCadsus().setCidade(null);
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateAddress(param);
        assertEquals("City should be informed", "Sem cidade definida.</br>", inconsistency);

        when(loadManager.getVO()).thenReturn(null);
        param.getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus().getEnderecoDomicilio().getEnderecoUsuarioCadsus().setCidade(new Cidade());
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateAddress(param);
        assertEquals("City should be informed", "Sem cidade definida.</br>", inconsistency);

        when(loadManager.getVO()).thenReturn(cidade);
        param.getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus().getEnderecoDomicilio().getEnderecoUsuarioCadsus().setCidade(new Cidade());
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateAddress(param);
        assertEquals("State Esus code should be informed", "O estado null, não possui o código e-SUS definido.</br>", inconsistency);

        cidade.getEstado().setCodigoEsus(1L);
        when(loadManager.getVO()).thenReturn(cidade);
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateAddress(param);
        assertEquals("State Esus code should be informed", "", inconsistency);

        param.getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus().getEnderecoDomicilio().getEnderecoUsuarioCadsus().getCidade().setEstado(new Estado());
        param.getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus().getEnderecoDomicilio().getEnderecoUsuarioCadsus().getCidade().getEstado().setCodigoEsus(1L);
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateAddress(param);
        assertEquals("City, State and State esus code should be correct", "", inconsistency);

        param.getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus().setNumeroComodos(105L);
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateAddress(param);
        assertEquals("Rooms number should be between 1 and 99", "O número de comodos do domicílio deve estar entre 1 e 99.</br>", inconsistency);

        param.getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus().setNumeroComodos(5L);
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateAddress(param);
        assertEquals("Rooms number should be between 1 and 99", "", inconsistency);

        param.getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus().getEnderecoDomicilio().getEnderecoUsuarioCadsus().setTelefone("123456789");
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateAddress(param);
        assertEquals("Should validate phone", "Telefone do domicílio não é um número válido.</br>", inconsistency);

        param.getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus().getEnderecoDomicilio().getEnderecoUsuarioCadsus().setTelefone("***********2");
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateAddress(param);
        assertEquals("Should validate phone", "Telefone do domicílio não é um número válido.</br>", inconsistency);

        param.getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus().getEnderecoDomicilio().getEnderecoUsuarioCadsus().setTelefone("1234567890");
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateAddress(param);
        assertEquals("Should validate phone", "", inconsistency);

        param.getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus().getEnderecoDomicilio().getEnderecoUsuarioCadsus().setTelefone("***********");
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateAddress(param);
        assertEquals("Should validate phone", "", inconsistency);
    }

    @Test
    public void shouldValidateFamilyAccountable() throws Exception {
        String inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateFamilyAccountable(param);
        assertEquals("should not validate if validarComponenteDomicilio is false", "", inconsistency);

        param.setValidarComponenteDomicilio(true);
        param.getResponsavelFamiliar().setDataNascimento(new Date(-5555555555555L));
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateFamilyAccountable(param);
        assertEquals("should validate age", "A idade do componente null é maior que 130 anos.</br>", inconsistency);

        param.getResponsavelFamiliar().setDataNascimento(new Date());
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateFamilyAccountable(param);
        assertEquals("should validate age", "", inconsistency);

        param.getResponsavelFamiliar().setProntuario("sad.,.,.3432");
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateFamilyAccountable(param);
        assertEquals("prontuario should be valid", "O prontuário do componente null é inválido.</br>", inconsistency);

        param.getResponsavelFamiliar().setProntuario("aB12");
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateFamilyAccountable(param);
        assertEquals("prontuario should be valid", "", inconsistency);

        param.getResponsavelFamiliar().getUsuarioCadsusDomicilio().getUsuarioCadsus().setCpf(null);
        param.getResponsavelFamiliar().setNumeroCartao(null);
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateFamilyAccountable(param);
        assertEquals("should have cpf or cns", "Obrigatório informar CNS ou CPF para o responsável null</br>", inconsistency);

        param.getResponsavelFamiliar().getUsuarioCadsusDomicilio().getUsuarioCadsus().setCpf(null);
        param.getResponsavelFamiliar().setNumeroCartao(***************L);
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateFamilyAccountable(param);
        assertEquals("should have cpf or cns", "", inconsistency);

        param.getResponsavelFamiliar().getUsuarioCadsusDomicilio().getUsuarioCadsus().setCpf("***********");
        param.getResponsavelFamiliar().setNumeroCartao(null);
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateFamilyAccountable(param);
        assertEquals("should have cpf or cns", "", inconsistency);

        param.getResponsavelFamiliar().getUsuarioCadsusDomicilio().getUsuarioCadsus().setCpf(null);
        param.getResponsavelFamiliar().setNumeroCartao(123L);
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateFamilyAccountable(param);
        assertEquals("cns should be valid", "CNS inválido, responsável: null</br>", inconsistency);

        param.getResponsavelFamiliar().setNumeroCartao(***************L);
        param.getResponsavelFamiliar().getUsuarioCadsusDomicilio().getUsuarioCadsus().setCpf("***********");

        param.getResponsavelFamiliar().setResideDesde(new Date(-5555555555555L));
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateFamilyAccountable(param);
        assertEquals("should validate reside since", "O campo, Reside Desde do responsável, possui valor inválido.</br>", inconsistency);

        param.getResponsavelFamiliar().setResideDesde(new Date(new Date().getTime() + ********));
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateFamilyAccountable(param);
        assertEquals("should validate reside since", "O campo Reside Desde do responsável não pode ser maior que o Mês/Ano atual.</br>", inconsistency);

        param.getResponsavelFamiliar().setResideDesde(new Date(new Date().getTime() - ********));
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateFamilyAccountable(param);
        assertEquals("should validate reside since", "", inconsistency);
    }

    @Test
    public void shouldValidatePropertyType() throws Exception {
        param.getEsusFichaEnderecoDomicilioEsus().setTipoImovel(null);
        String inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validatePropertyType(param);
        assertEquals("tipo imovel should be informed", "Tipo do imóvel não definido para domicílio número: 777</br>", inconsistency);

        param.getEsusFichaEnderecoDomicilioEsus().setTipoImovel(EnderecoDomicilioEsus.TipoImovel.DOMICILIO.value());
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validatePropertyType(param);
        assertEquals("tipo imovel should be informed", "", inconsistency);
    }

    @Test
    public void shouldValidateStayingInstitution() throws Exception {
        param.getEsusFichaEnderecoDomicilioEsus().setTipoImovel(EnderecoDomicilioEsus.TipoImovel.ABRIGO.sum());
        param.getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus().setNomeResponsavelTecnico(null);
        String inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateStayingInstitution(param);
        assertEquals("tipo imovel should be informed", "Nome do Responsável Técnico não informado</br>", inconsistency);

        param.getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus().setNomeResponsavelTecnico("Name");
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateStayingInstitution(param);
        assertEquals("tipo imovel should be informed", "", inconsistency);
    }

    @Test
    public void shouldValidateShouldSetStayingInstitution() throws Exception {
        assertFalse("validate if should set staying institution info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetStayingInstitution(EnderecoDomicilioEsus.TipoImovel.DOMICILIO.sum()));
        assertFalse("validate if should set staying institution info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetStayingInstitution(EnderecoDomicilioEsus.TipoImovel.COMERCIO.sum()));
        assertFalse("validate if should set staying institution info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetStayingInstitution(EnderecoDomicilioEsus.TipoImovel.TERRENO_BALDIO.sum()));
        assertFalse("validate if should set staying institution info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetStayingInstitution(EnderecoDomicilioEsus.TipoImovel.PONTO_ESTRATEGICO.sum()));
        assertFalse("validate if should set staying institution info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetStayingInstitution(EnderecoDomicilioEsus.TipoImovel.ESCOLA.sum()));
        assertFalse("validate if should set staying institution info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetStayingInstitution(EnderecoDomicilioEsus.TipoImovel.CRECHE.sum()));
        assertTrue("validate if should set staying institution info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetStayingInstitution(EnderecoDomicilioEsus.TipoImovel.ABRIGO.sum()));
        assertTrue("validate if should set staying institution info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetStayingInstitution(EnderecoDomicilioEsus.TipoImovel.INSTITUICAO_LONGA_PERMANENCIA_IDOSOS.sum()));
        assertTrue("validate if should set staying institution info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetStayingInstitution(EnderecoDomicilioEsus.TipoImovel.UNIDADE_PRISIONAL.sum()));
        assertTrue("validate if should set staying institution info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetStayingInstitution(EnderecoDomicilioEsus.TipoImovel.UNIDADE_MEDIDA_SOCIO_EDUCATIVA.sum()));
        assertTrue("validate if should set staying institution info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetStayingInstitution(EnderecoDomicilioEsus.TipoImovel.DELEGACIA.sum()));
        assertFalse("validate if should set staying institution info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetStayingInstitution(EnderecoDomicilioEsus.TipoImovel.ESTABELECIMENTO_RELIGIOSO.sum()));
        assertFalse("validate if should set staying institution info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetStayingInstitution(EnderecoDomicilioEsus.TipoImovel.OUTROS.sum()));
    }

    @Test
    public void shouldValidateShouldSetLivingCondition() throws Exception {
        assertTrue("validate if should set living condition info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetLivingCondition(EnderecoDomicilioEsus.TipoImovel.DOMICILIO.sum()));
        assertFalse("validate if should set living condition info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetLivingCondition(EnderecoDomicilioEsus.TipoImovel.COMERCIO.sum()));
        assertFalse("validate if should set living condition info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetLivingCondition(EnderecoDomicilioEsus.TipoImovel.TERRENO_BALDIO.sum()));
        assertFalse("validate if should set living condition info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetLivingCondition(EnderecoDomicilioEsus.TipoImovel.PONTO_ESTRATEGICO.sum()));
        assertFalse("validate if should set living condition info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetLivingCondition(EnderecoDomicilioEsus.TipoImovel.ESCOLA.sum()));
        assertFalse("validate if should set living condition info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetLivingCondition(EnderecoDomicilioEsus.TipoImovel.CRECHE.sum()));
        assertTrue("validate if should set living condition info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetLivingCondition(EnderecoDomicilioEsus.TipoImovel.ABRIGO.sum()));
        assertTrue("validate if should set living condition info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetLivingCondition(EnderecoDomicilioEsus.TipoImovel.INSTITUICAO_LONGA_PERMANENCIA_IDOSOS.sum()));
        assertTrue("validate if should set living condition info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetLivingCondition(EnderecoDomicilioEsus.TipoImovel.UNIDADE_PRISIONAL.sum()));
        assertTrue("validate if should set living condition info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetLivingCondition(EnderecoDomicilioEsus.TipoImovel.UNIDADE_MEDIDA_SOCIO_EDUCATIVA.sum()));
        assertTrue("validate if should set living condition info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetLivingCondition(EnderecoDomicilioEsus.TipoImovel.DELEGACIA.sum()));
        assertFalse("validate if should set living condition info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetLivingCondition(EnderecoDomicilioEsus.TipoImovel.ESTABELECIMENTO_RELIGIOSO.sum()));
        assertFalse("validate if should set living condition info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetLivingCondition(EnderecoDomicilioEsus.TipoImovel.OUTROS.sum()));
    }

    @Test
    public void shouldValidateShouldSetPets() throws Exception {
        assertTrue("validate if should set pets info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetPets(EnderecoDomicilioEsus.TipoImovel.DOMICILIO.sum()));
        assertFalse("validate if should set pets info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetPets(EnderecoDomicilioEsus.TipoImovel.COMERCIO.sum()));
        assertFalse("validate if should set pets info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetPets(EnderecoDomicilioEsus.TipoImovel.TERRENO_BALDIO.sum()));
        assertFalse("validate if should set pets info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetPets(EnderecoDomicilioEsus.TipoImovel.PONTO_ESTRATEGICO.sum()));
        assertFalse("validate if should set pets info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetPets(EnderecoDomicilioEsus.TipoImovel.ESCOLA.sum()));
        assertFalse("validate if should set pets info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetPets(EnderecoDomicilioEsus.TipoImovel.CRECHE.sum()));
        assertFalse("validate if should set pets info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetPets(EnderecoDomicilioEsus.TipoImovel.ABRIGO.sum()));
        assertFalse("validate if should set pets info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetPets(EnderecoDomicilioEsus.TipoImovel.INSTITUICAO_LONGA_PERMANENCIA_IDOSOS.sum()));
        assertFalse("validate if should set pets info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetPets(EnderecoDomicilioEsus.TipoImovel.UNIDADE_PRISIONAL.sum()));
        assertFalse("validate if should set pets info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetPets(EnderecoDomicilioEsus.TipoImovel.UNIDADE_MEDIDA_SOCIO_EDUCATIVA.sum()));
        assertFalse("validate if should set pets info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetPets(EnderecoDomicilioEsus.TipoImovel.DELEGACIA.sum()));
        assertFalse("validate if should set pets info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetPets(EnderecoDomicilioEsus.TipoImovel.ESTABELECIMENTO_RELIGIOSO.sum()));
        assertFalse("validate if should set pets info", EsusValidacoesFichaCadastroDomiciliarHelper.shouldSetPets(EnderecoDomicilioEsus.TipoImovel.OUTROS.sum()));
    }

    @Test
    public void shouldValidateProfessional() throws Exception {
        param.setProfissional(null);
        String inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateProfessional(param);
        assertEquals("Profissional should be informed", "Profissional não informado</br>", inconsistency);

        param.setProfissional(new Profissional());
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateProfessional(param);
        assertEquals("Profissional Cns should be informed", "Profissional null sem CNS definido.</br>Profissional null sem Cidade definida.</br>", inconsistency);

        param.getProfissional().setCodigoCns("***************");
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateProfessional(param);
        assertEquals("Profissional City should be informed", "Profissional null sem Cidade definida.</br>", inconsistency);

        param.getProfissional().setCidade(new Cidade());
        inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.validateProfessional(param);
        assertEquals("Profissional City should be informed", "", inconsistency);
    }

    @Test
    public void shouldExecutarValidacoesComuns() throws Exception {
        param.getEsusFichaEnderecoDomicilioEsus().setEquipeMicroArea(new EquipeMicroArea());
        param.getEsusFichaEnderecoDomicilioEsus().getEquipeMicroArea().setEquipeArea(new EquipeArea());
        param.getEsusFichaEnderecoDomicilioEsus().getEquipeMicroArea().getEquipeArea().setDescricao("description");
        param.getEsusFichaEnderecoDomicilioEsus().getEquipeMicroArea().setMicroArea(1L);
        param.setProfissional(GerarArquivoEsusIntegracaoCdsTestHelper.getProfissional());
        param.getEsusFichaEnderecoDomicilioEsus().getEquipeMicroArea().setEquipeProfissional(new EquipeProfissional());
        String inconsistency = EsusValidacoesFichaCadastroDomiciliarHelper.executarValidacoesComuns(param);
        assertEquals("Should excute comum validations", "", inconsistency);
    }
}