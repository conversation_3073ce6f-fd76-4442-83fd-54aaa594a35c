package br.com.celk.esus;

import br.com.celk.bo.esus.interfaces.facade.EsusFacade;
import br.com.celk.bo.hospital.endereco.EnderecoHelper;
import br.com.celk.unidadesaude.esus.cds.interfaces.dto.QueryConsultaEsusIntegracaoCdsDTOParam;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.server.interfaces.facade.HibernateSessionFactoryFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.basico.ParameterModuleContainer;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Raca;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDocumento;
import br.gov.saude.esus.cds.transport.generated.thrift.cadastroindividual.CadastroIndividualThrift;
import br.gov.saude.esus.cds.transport.generated.thrift.cadastroindividual.IdentificacaoUsuarioCidadaoThrift;
import br.gov.saude.esus.cds.transport.generated.thrift.common.UnicaLotacaoHeaderThrift;
import br.gov.saude.esus.transport.common.generated.thrift.DadoTransporteThrift;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Date;
import java.util.List;
import java.util.UUID;

import static junit.framework.Assert.assertEquals;
import static junit.framework.Assert.assertTrue;
import static org.junit.Assert.assertFalse;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest(fullyQualifiedNames = {
        "br.com.ksisolucoes.bo.command.LoadManager",
        "java.util.UUID",
        "br.com.ksisolucoes.system.factory.BOFactory",
        "br.com.celk.bo.hospital.endereco.EnderecoHelper"
})
public class GerarArquivoEsusIntegracaoCdsTermoRecusaIndividualTest {

    GerarArquivoEsusIntegracaoCdsTermoRecusaIndividual gerarArquivoEsusIntegracaoCdsTermoRecusaIndividual;
    CadastroIndividualThrift cadastroIndividualThrift = new CadastroIndividualThrift();
    DadoTransporteThrift dadoTransporteThrift = new DadoTransporteThrift();

    @Mock
    private CommomFacade mockCommomFacade;

    @Mock
    private ParameterModuleContainer mockParameterContainer;

    @Mock
    private EsusFacade mockEsusFacade;

    @Mock
    private HibernateSessionFactoryFacade mockHibernateSessionFactoryFacade;

    @Mock
    private LoadManager mockLoadManager;

    @Mock
    private Session mockSession;

    @Mock
    private SQLQuery mockSqlQuery;

    ExportacaoEsusDTOParam esusDTOParam;

    @Before
    public void setup() throws Exception {
        setMocks();

        this.cadastroIndividualThrift = new CadastroIndividualThrift();
        this.esusDTOParam = new ExportacaoEsusDTOParam();
        this.esusDTOParam.setCodigoProcesso(1L);
        this.esusDTOParam.setIndividual(true);

        createIntegrationFile();
    }

    private void setMocks() throws DAOException, ValidacaoException {
        PowerMockito.mockStatic(BOFactory.class);
        PowerMockito.mockStatic(LoadManager.class);
        PowerMockito.mockStatic(EnderecoHelper.class);
        PowerMockito.mockStatic(UUID.class);

        GerarArquivoEsusIntegracaoCdsTermoRecusaIndividualTestHelper.mockLoadManager(mockLoadManager);

        when(BOFactory.getBO(CommomFacade.class)).thenReturn(mockCommomFacade);
        when(mockCommomFacade.modulo(Mockito.any(Modulos.class))).thenReturn(mockParameterContainer);
        when(BOFactory.getBO(EsusFacade.class)).thenReturn(mockEsusFacade);
        when( BOFactory.getBO(HibernateSessionFactoryFacade.class)).thenReturn(mockHibernateSessionFactoryFacade);
        when((List) mockLoadManager.getList()).thenReturn(GerarArquivoEsusIntegracaoCdsTermoRecusaIndividualTestHelper.getCboFichaEsusItems());
        when(mockParameterContainer.getParametro("UnidadeCentralIntegracaoEsus")).thenReturn(GerarArquivoEsusIntegracaoCdsTermoRecusaIndividualTestHelper.getUnidadeCentral());
        when(mockEsusFacade.queryConsultaEsusIntegracaoCdsTermoRecusaIndividual(Mockito.any(QueryConsultaEsusIntegracaoCdsDTOParam.class))).thenReturn(GerarArquivoEsusIntegracaoCdsTermoRecusaIndividualTestHelper.getIntegrations());
        UsuarioCadsusDocumento usuarioCadsusDocumento = new UsuarioCadsusDocumento();
        usuarioCadsusDocumento.setNumeroDocumento("12345678901");
        when(EnderecoHelper.getCodigoIbgeComDV(Mockito.anyLong())).thenReturn(420190L);
        when(UUID.randomUUID()).thenReturn(new UUID(1L, 2L));
    }

    private void createIntegrationFile() throws Exception {
        this.gerarArquivoEsusIntegracaoCdsTermoRecusaIndividual = new GerarArquivoEsusIntegracaoCdsTermoRecusaIndividual(this.esusDTOParam);

        when(this.gerarArquivoEsusIntegracaoCdsTermoRecusaIndividual.getSession()).thenReturn(mockSession);
        when(this.gerarArquivoEsusIntegracaoCdsTermoRecusaIndividual.getSessionLeitura()).thenReturn(mockSession);
        when(mockSession.createSQLQuery(Mockito.anyString())).thenReturn(mockSqlQuery);

        this.gerarArquivoEsusIntegracaoCdsTermoRecusaIndividual.execute();

        if (this.gerarArquivoEsusIntegracaoCdsTermoRecusaIndividual.getArquivoListRefatorado().size() > 0) {
            this.dadoTransporteThrift = this.gerarArquivoEsusIntegracaoCdsTermoRecusaIndividual.getArquivoListRefatorado().get(0);
            ExportarArquivoEsusHelper.unserialize(this.dadoTransporteThrift.getDadoSerializado(), this.cadastroIndividualThrift);
        }
    }

    @Test
    public void shouldCreateInconsistencies() {
        assertEquals("should not have inconcistency",0, this.gerarArquivoEsusIntegracaoCdsTermoRecusaIndividual.getInconsistencyList().size());
    }

    @Test
    public void shouldCreateIneDadoSerializado() {
        assertEquals("should set Ine info", "Equipe Cnes", this.dadoTransporteThrift.getIneDadoSerializado());
    }

    //    @Test
    public void shouldCreateVersion() {
        assertEquals("should set major version", 5, this.dadoTransporteThrift.getVersao().getMajor());
        assertEquals("should set minor version", 6, this.dadoTransporteThrift.getVersao().getMinor());
        assertEquals("should set revision version", 1, this.dadoTransporteThrift.getVersao().getRevision());
    }

    @Test
    public void shouldCreateSenderInfo() {
        assertEquals("should set sender's contra chave", "Software de Terceiros", this.dadoTransporteThrift.getRemetente().getContraChave());
        assertEquals("should set sender's CPF or CNPJ", "69491604000184", this.dadoTransporteThrift.getRemetente().getCpfOuCnpj());
        assertEquals("should set sender's company name", "Company Name", this.dadoTransporteThrift.getRemetente().getNomeOuRazaoSocial());
        assertEquals("should set sender's phone", "99999999999", this.dadoTransporteThrift.getRemetente().getFone());
        assertEquals("should set sender's email", "<EMAIL>", this.dadoTransporteThrift.getRemetente().getEmail());
    }

    @Test
    public void shouldCreateAddresseeInfo() {
        assertEquals("should set addressee's contra chave", "Software de Terceiros", this.dadoTransporteThrift.getOriginadora().getContraChave());
        assertEquals("should set addressee's CPF or CNPJ", "69491604000184", this.dadoTransporteThrift.getOriginadora().getCpfOuCnpj());
        assertEquals("should set addressee's company name", "Empresa Name", this.dadoTransporteThrift.getOriginadora().getNomeOuRazaoSocial());
        assertEquals("should set addressee's phone", "99999999999", this.dadoTransporteThrift.getOriginadora().getFone());
        assertEquals("should set addressee's email", "<EMAIL>", this.dadoTransporteThrift.getOriginadora().getEmail());
    }

    @Test
    public void shouldCreateTipoDadoSerializado() {
        assertEquals("should set data type", IExportacaoEsus.TipoDadoSerializadoEsus.FICHA_CADASTRO_INDIVIDUAL.getValue(), new Long(this.dadoTransporteThrift.getTipoDadoSerializado()));
    }

    @Test
    public void shouldCreateCnesDadoSerializado() {
        assertEquals("should set CNES serialized data", "2672367", this.dadoTransporteThrift.getCnesDadoSerializado());
    }

    @Test
    public void shouldCreateCodIbge() {
        assertEquals("should set IBGE id", "420190", this.dadoTransporteThrift.getCodIbge());
    }

    @Test
    public void shouldCreateCadastroIndividualThriftInfo() {
        assertFalse("should set is ficha updated", this.cadastroIndividualThrift.isFichaAtualizada());
        assertTrue("should set is refuse term", this.cadastroIndividualThrift.isStatusTermoRecusaCadastroIndividualAtencaoBasica());
        assertEquals("should set origen", 0, this.cadastroIndividualThrift.getTpCdsOrigem());
    }

    @Test
    public void shouldCreateIdentificacaoUsuarioCidadaoThrifInfo() {
        IdentificacaoUsuarioCidadaoThrift identificacaoUsuarioCidadao = this.cadastroIndividualThrift.getIdentificacaoUsuarioCidadao();

        assertEquals("should set social name", "Social Name", identificacaoUsuarioCidadao.getNomeSocial());
        assertEquals("should set birthdate", new Date(555680800000L).getTime(), identificacaoUsuarioCidadao.getDataNascimentoCidadao());
        assertEquals("should set email", "<EMAIL>", identificacaoUsuarioCidadao.getEmailCidadao());
        assertEquals("should set nationality", UsuarioCadsus.Nacionalidade.BRASILEIRO.value(), new Long(identificacaoUsuarioCidadao.getNacionalidadeCidadao()));
        assertEquals("should set name", "Patient Name", identificacaoUsuarioCidadao.getNomeCidadao());
        assertEquals("should set phone", "99999999999", identificacaoUsuarioCidadao.getTelefoneCelular());
        assertEquals("should set race", Raca.TipoRaca.INDIGENA.value(), Long.valueOf(identificacaoUsuarioCidadao.getRacaCorCidadao()));
        assertEquals("should set gender", 1L, identificacaoUsuarioCidadao.getSexoCidadao());
        assertEquals("should set micro area", "01", identificacaoUsuarioCidadao.getMicroArea());
        assertFalse("should set is not from area", identificacaoUsuarioCidadao.isStForaArea());
        assertFalse("should set is mother's name unknown", identificacaoUsuarioCidadao.isDesconheceNomeMae());
        assertEquals("should set mother's name", "Mother Name", identificacaoUsuarioCidadao.getNomeMaeCidadao());
        assertEquals("should set birth country", 31L, identificacaoUsuarioCidadao.getPaisNascimento());
        assertEquals("should set cns", "***************", identificacaoUsuarioCidadao.getCnsCidadao());
        assertFalse("should set is accountable", identificacaoUsuarioCidadao.isStatusEhResponsavel());
        assertEquals("should set PIS/PASEP", "***********", identificacaoUsuarioCidadao.getNumeroNisPisPasep());
        assertFalse("should set is father's name unknown", identificacaoUsuarioCidadao.isDesconheceNomePai());
        assertEquals("should set father's name", "Father Name", identificacaoUsuarioCidadao.getNomePaiCidadao());
    }

    @Test
    public void shouldUnicaLotacaoHeaderThriftInfo() {
        UnicaLotacaoHeaderThrift headerTransport = this.cadastroIndividualThrift.getHeaderTransport();

        assertEquals("should set professional cns", "***************", headerTransport.getProfissionalCNS());
        assertEquals("should set cbo id 2002", "322205", headerTransport.getCboCodigo_2002());
        assertEquals("should set CNES", "2672367", headerTransport.getCnes());
        assertEquals("should set team's INE", "Equipe Cnes", headerTransport.getIne());
        assertEquals("should set care date", 855680800000L, headerTransport.getDataAtendimento());
        assertEquals("should set IBGE city id", "420190", headerTransport.getCodigoIbgeMunicipio());
    }
}