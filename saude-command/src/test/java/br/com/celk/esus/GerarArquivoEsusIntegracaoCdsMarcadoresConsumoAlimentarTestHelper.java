package br.com.celk.esus;

import br.com.celk.unidadesaude.esus.cds.interfaces.dto.QueryConsultaEsusIntegracaoCdsDTO;
import br.com.celk.unidadesaude.esus.cds.interfaces.dto.QueryConsultaEsusIntegracaoCdsVacinaDTO;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaMarcadoresConsumoAlimentar;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaProcedimentoItem;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaVacina;
import br.com.ksisolucoes.vo.entradas.estoque.FabricanteMedicamento;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.esus.EsusIntegracaoCds;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vacina.*;
import org.assertj.core.util.DateUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class GerarArquivoEsusIntegracaoCdsMarcadoresConsumoAlimentarTestHelper {

    private GerarArquivoEsusIntegracaoCdsMarcadoresConsumoAlimentarTestHelper() { }

    public static ExportacaoEsusDTOParam getEsusDtoParam() {
        ExportacaoEsusDTOParam esusDTOParam = new ExportacaoEsusDTOParam();
        esusDTOParam.setCodigoProcesso(1L);
        esusDTOParam.setIndividual(true);
        esusDTOParam.setIndividual(true);
        esusDTOParam.setPeriodo(new DatePeriod());

        return esusDTOParam;
    }

    public static List<QueryConsultaEsusIntegracaoCdsDTO> getQueryConsultaEsusIntegracaoCdsDTOList() throws Exception {
        List<QueryConsultaEsusIntegracaoCdsDTO> queryConsultaEsusIntegracaoCdsVacinaDTOList = new ArrayList<>();

        queryConsultaEsusIntegracaoCdsVacinaDTOList.add(getQueryConsultaEsusIntegracaoCdsDTO());

        return queryConsultaEsusIntegracaoCdsVacinaDTOList;
    }

    public static QueryConsultaEsusIntegracaoCdsDTO getQueryConsultaEsusIntegracaoCdsDTO() throws Exception {
        QueryConsultaEsusIntegracaoCdsDTO queryConsultaEsusIntegracaoCdsDTO = new QueryConsultaEsusIntegracaoCdsDTO();

        queryConsultaEsusIntegracaoCdsDTO.setEsusIntegracaoCds(getEsusIntegracaoCds());

        return queryConsultaEsusIntegracaoCdsDTO;
    }

    private static EsusIntegracaoCds getEsusIntegracaoCds() throws Exception {
        EsusIntegracaoCds esusIntegracaoCds = new EsusIntegracaoCds();
        
        esusIntegracaoCds.setCodigo(1L);
        esusIntegracaoCds.setEsusFichaMarcadoresConsumoAlimentar(getEsusFichaMarcadoresConsumoAlimentar());

        return esusIntegracaoCds;
    }

    private static EsusFichaMarcadoresConsumoAlimentar getEsusFichaMarcadoresConsumoAlimentar() throws Exception {
        EsusFichaMarcadoresConsumoAlimentar esusFichaMarcadoresConsumoAlimentar = new EsusFichaMarcadoresConsumoAlimentar();

        esusFichaMarcadoresConsumoAlimentar.setEmpresa(GerarArquivoEsusIntegracaoCdsTestHelper.getEmpresa());
        esusFichaMarcadoresConsumoAlimentar.setProfissional(GerarArquivoEsusIntegracaoCdsTestHelper.getProfissional());
        esusFichaMarcadoresConsumoAlimentar.setCbo(GerarArquivoEsusIntegracaoCdsTestHelper.getTabelaCbo());
        esusFichaMarcadoresConsumoAlimentar.setDataAtendimento(DataUtil.stringToDate("21/01/2021"));
        esusFichaMarcadoresConsumoAlimentar.setDataNascimento(DataUtil.stringToDate("21/04/2020"));
        esusFichaMarcadoresConsumoAlimentar.setSexo(EsusFichaProcedimentoItem.Sexo.MASCULINO.value());
        esusFichaMarcadoresConsumoAlimentar.setCodigoIne("1234567890");
        esusFichaMarcadoresConsumoAlimentar.setUsuarioCadsus(GerarArquivoEsusIntegracaoCdsTestHelper.getUsuarioCadsus());
        esusFichaMarcadoresConsumoAlimentar.setNomeUsuarioCadsus("Nome usuario cadsus");

        esusFichaMarcadoresConsumoAlimentar.setLeitePeito1(1L);
        esusFichaMarcadoresConsumoAlimentar.setMingau(1L);
        esusFichaMarcadoresConsumoAlimentar.setAguaCha(1L);
        esusFichaMarcadoresConsumoAlimentar.setLeiteVaca(1L);
        esusFichaMarcadoresConsumoAlimentar.setFormulaInfantil(1L);
        esusFichaMarcadoresConsumoAlimentar.setSucoFruta(1L);
        esusFichaMarcadoresConsumoAlimentar.setFruta(1L);
        esusFichaMarcadoresConsumoAlimentar.setComidaSal(1L);
        esusFichaMarcadoresConsumoAlimentar.setOutrosAlimentosBebidas(1L);

        esusFichaMarcadoresConsumoAlimentar.setLeitePeito21(1L);
        esusFichaMarcadoresConsumoAlimentar.setFrutaInteiraPedacoAmassado(1L);
        esusFichaMarcadoresConsumoAlimentar.setSeComeuFrutaQuantas(3L);
        esusFichaMarcadoresConsumoAlimentar.setComeuComidaSal(1L);
        esusFichaMarcadoresConsumoAlimentar.setSeComeuComidaSalQuantas(3L);
        esusFichaMarcadoresConsumoAlimentar.setSeSimComidaFoiOferecida(3L);
        esusFichaMarcadoresConsumoAlimentar.setLeiteNaoDoPeito(1L);
        esusFichaMarcadoresConsumoAlimentar.setMingauLeite(1L);
        esusFichaMarcadoresConsumoAlimentar.setIogurte(1L);
        esusFichaMarcadoresConsumoAlimentar.setLegumes(1L);
        esusFichaMarcadoresConsumoAlimentar.setVegetalFrutasAlaranjada(1L);
        esusFichaMarcadoresConsumoAlimentar.setVerduraFolha(1L);
        esusFichaMarcadoresConsumoAlimentar.setCarne(1L);
        esusFichaMarcadoresConsumoAlimentar.setFigado(1L);
        esusFichaMarcadoresConsumoAlimentar.setFeijao36(1L);
        esusFichaMarcadoresConsumoAlimentar.setArrozBatataMacarrao(1L);
        esusFichaMarcadoresConsumoAlimentar.setHamburguerEmbutidos38(1L);
        esusFichaMarcadoresConsumoAlimentar.setBebidasAdocadas39(1L);
        esusFichaMarcadoresConsumoAlimentar.setMacarraoSalgadinhosBiscoitos40(1L);
        esusFichaMarcadoresConsumoAlimentar.setBiscoitoDocesGuloseimas41(1L);

        esusFichaMarcadoresConsumoAlimentar.setRefeicoesUsandoTv(1L);
        esusFichaMarcadoresConsumoAlimentar.setRefeicoesLongoDia(1L);
        esusFichaMarcadoresConsumoAlimentar.setFeijao14(1L);
        esusFichaMarcadoresConsumoAlimentar.setFrutasFrescas(1L);
        esusFichaMarcadoresConsumoAlimentar.setVerdurasLegumes(1L);
        esusFichaMarcadoresConsumoAlimentar.setHamburguerEmbutidos17(1L);
        esusFichaMarcadoresConsumoAlimentar.setBebidasAdocadas18(1L);
        esusFichaMarcadoresConsumoAlimentar.setMacarraoSalgadinhosBiscoitos19(1L);
        esusFichaMarcadoresConsumoAlimentar.setBiscoitoDocesGuloseimas20(1L);

        return esusFichaMarcadoresConsumoAlimentar;
    }

    private static UsuarioCadsusCns getUsuarioCadsusCns() {
        UsuarioCadsusCns usuarioCadsusCns = new UsuarioCadsusCns();
        
        usuarioCadsusCns.setCodigo(1L);
        usuarioCadsusCns.setNumeroCartao(12345678901L);

        return usuarioCadsusCns;
    }

}