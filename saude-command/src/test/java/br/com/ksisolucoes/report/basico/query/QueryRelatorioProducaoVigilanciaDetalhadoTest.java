package br.com.ksisolucoes.report.basico.query;

import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.LancamentoAtividadesVigilanciaDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import junit.framework.TestCase;
import org.junit.Before;
import org.junit.Test;

public class QueryRelatorioProducaoVigilanciaDetalhadoTest extends TestCase {

    private QueryRelatorioProducaoVigilanciaDetalhado queryImpressao;
    private HQLHelper hqlHelper;

    @Before
    public void setUp() {
        this.queryImpressao = new QueryRelatorioProducaoVigilanciaDetalhado();
        this.hqlHelper = new HQLHelper();
    }

    @Test
    public void test_AgrupamentoFormaApresentacaoGeral() {
        LancamentoAtividadesVigilanciaDTOParam param = new LancamentoAtividadesVigilanciaDTOParam();
        param.setFormaApresentacao(LancamentoAtividadesVigilanciaDTOParam.FormaApresentacao.GERAL.value());
        queryImpressao.setDTOParam(param);
        queryImpressao.createQuery(hqlHelper);
        assertEquals(hqlHelper.getGroup().toString(),"profissional.nome, lancamentoAtividadesVigilancia.dataAtividade, atividadesVigilancia.descricao, lancamentoAtividadesVigilancia.nomePessoa, requerimentoVigilancia.protocolo, tipoSolicitacao.descricao");
    }

    @Test
    public void test_AgrupamentoFormaApresentacaoProfissional() {
        LancamentoAtividadesVigilanciaDTOParam param = new LancamentoAtividadesVigilanciaDTOParam();
        param.setFormaApresentacao(LancamentoAtividadesVigilanciaDTOParam.FormaApresentacao.PROFISSIONAL.value());
        queryImpressao.setDTOParam(param);
        queryImpressao.createQuery(hqlHelper);
        assertEquals(hqlHelper.getGroup().toString(), "profissional.nome, lancamentoAtividadesVigilancia.dataAtividade, atividadesVigilancia.descricao, lancamentoAtividadesVigilancia.nomePessoa, requerimentoVigilancia.protocolo, tipoSolicitacao.descricao");
    }

    @Test
    public void test_AgrupamentoFormaApresentacaoAtividade() {
        LancamentoAtividadesVigilanciaDTOParam param = new LancamentoAtividadesVigilanciaDTOParam();
        param.setFormaApresentacao(LancamentoAtividadesVigilanciaDTOParam.FormaApresentacao.ATIVIDADE.value());
        queryImpressao.setDTOParam(param);
        queryImpressao.createQuery(hqlHelper);
        assertEquals(hqlHelper.getGroup().toString(), "profissional.nome, lancamentoAtividadesVigilancia.dataAtividade, atividadesVigilancia.descricao, lancamentoAtividadesVigilancia.nomePessoa, requerimentoVigilancia.protocolo, tipoSolicitacao.descricao");
    }


}