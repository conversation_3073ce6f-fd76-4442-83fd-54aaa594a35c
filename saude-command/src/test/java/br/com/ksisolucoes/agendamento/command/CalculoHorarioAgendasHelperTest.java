package br.com.ksisolucoes.agendamento.command;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.parametrogem.IParameterModuleContainer;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static junit.framework.Assert.assertFalse;
import static junit.framework.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BOFactory.class})
public class CalculoHorarioAgendasHelperTest {
    
    private static final long TEN_DAYS = 10L;
    private static final long ZERO_DAYS = 0L;

    @Mock
    private CommomFacade commomFacade;
    
    @Mock
    private IParameterModuleContainer parameterContainer;

    @Test
    public void testEstaPeriodoLiberacao() {
        assertTrue(CalculoHorarioAgendasHelper.estaPeriodoLiberacaoCotas(DataUtil.getDataAtual(), TEN_DAYS));
    }
    
    @Test
    public void testEstaPeriodoLiberacaoFromParameter() throws DAOException {
        mockParameterDays(TEN_DAYS);
        assertTrue(CalculoHorarioAgendasHelper.estaPeriodoLiberacaoCotas(DataUtil.getDataAtual()));
    }
    
    @Test
    public void testNaoEstaPeriodoLiberacao() {
        assertFalse(CalculoHorarioAgendasHelper.estaPeriodoLiberacaoCotas(DataUtil.getDataAtual(), ZERO_DAYS));
    }
    
    @Test
    public void testNaoEstaPeriodoLiberacaoFromParameter() throws DAOException {
        mockParameterDays(ZERO_DAYS);
        assertFalse(CalculoHorarioAgendasHelper.estaPeriodoLiberacaoCotas(DataUtil.getDataAtual()));
    }

    private void mockParameterDays(long days) throws DAOException {
        mockStatic(BOFactory.class);
        when(BOFactory.getBO(eq(CommomFacade.class))).thenReturn(commomFacade);
        when(commomFacade.modulo(eq(Modulos.AGENDAMENTO))).thenReturn(parameterContainer);
        when(parameterContainer.getParametro(eq("DiasLimiteCotasUnidade"))).thenReturn(days);
    }
}
