package br.com.ksisolucoes.bo.appcidadao.service;

import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.jboss.resteasy.client.jaxrs.internal.ClientResponse;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import javax.ws.rs.core.Response;
import java.util.HashMap;

import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class AppClientCredationTokenServiceTest {
    Response response;

    @Before
    public void setUp() throws Exception {
        HashMap<String, String> responseMap = new HashMap<>();
        responseMap.put("access_token", "access_token");
        response = ClientResponse.ok().entity(responseMap).build();
    }

    @Ignore
    @Test
    public void getToken() throws ValidacaoException {
        AppClientCredationTokenService appClientCredationTokenService = new AppClientCredationTokenServiceMock();
        String access_token = appClientCredationTokenService.getToken();
        assertEquals("access_token", access_token);
    }

    class AppClientCredationTokenServiceMock extends AppClientCredationTokenService {
        @Override
        public Response sendRequest() {
            return response;
        }
    }

}