package br.com.ksisolucoes.bo.atividadegrupo;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.RecoveryProperties;
import br.com.ksisolucoes.bo.atividadegrupo.interfaces.dto.AtividadeGrupoPacienteWebDTO;
import br.com.ksisolucoes.bo.atividadegrupo.interfaces.dto.GestaoAtividadeGrupoDTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.interfaces.FacadeBO;
import br.com.ksisolucoes.bo.prontuario.avaliacao.estadonutricional.RegistrarEstadoNutricional;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.server.interfaces.facade.CommandServerFacade;
import br.com.ksisolucoes.bo.server.interfaces.facade.HibernateSessionFactoryFacade;
import br.com.ksisolucoes.command.AbstractCommandExtends;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.CacheEntityProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.server.HibernateSessionFactory;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.AbstractSessaoAplicacao;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.parametrogem.IParameterModuleContainer;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atividadegrupo.*;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusOcorrencia;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.controle.UsuarioEmpresa;
import br.com.ksisolucoes.vo.esus.*;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import br.com.ksisolucoes.vo.prontuario.procedimento.*;
import junit.framework.TestCase;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

@RunWith(PowerMockRunner.class)
@PrepareForTest({
        AbstractCommandExtends.class,
        AbstractCommandTransaction.class,
        AbstractSessaoAplicacao.class,
        AtendimentoFacade.class,
        BOFactory.class,
        CacheEntityProperties.class,
        CadastroFacade.class,
        CargaBasicoPadrao.class,
        CommandServerFacade.class,
        CommomFacade.class,
        FacadeBO.class,
        HibernateSessionFactory.class,
        HibernateSessionFactoryFacade.class,
        HospitalFacade.class,
        IParameterModuleContainer.class,
        LoadManager.class,
        Parametro.class,
        RecoveryProperties.class,
        SessaoAplicacaoImp.class
})
@PowerMockIgnore("javax.security.*")
public class SalvarAtividadeGrupoTest extends TestCase {

    private SalvarAtividadeGrupo sag;
    private GestaoAtividadeGrupoDTO dto;

    @Mock   AtendimentoFacade atendimentoFacadeMock;
    @Mock   CommandServerFacade commandServerFacadeMock;
    @Mock   SessaoAplicacaoImp abstractSessaoAplicacaoMock;
    @Mock   CacheEntityProperties cacheEntityPropertiesMock;
    @Mock   CadastroFacade cadastroFacadeMock;
    @Mock   CargaBasicoPadrao cargaBasicoPadraoMock;
    @Mock   CommomFacade commomFacadeMock;
    @Mock   Criteria criteriaMock;
    @Mock   HibernateSessionFactoryFacade hibernateSessionFactoryFacadeMock;
    @Mock   HospitalFacade hospitalFacadeMock;
    @Mock   IParameterModuleContainer iParameterModuleContainerMock;
    @Mock   Session sessionMock;
    @Mock   LoadManager loadManagerCboMock;
    @Mock   LoadManager loadManagerIMCMock;
    @Mock   LoadManager loadManagerDadoMock;
    @Mock   LoadManager loadManagerEquipeMock;
    @Mock   LoadManager loadManagerEquipeProfissionalMock;
    @Mock   LoadManager loadManagerMockAG;
    @Mock   LoadManager loadManagerMockAGProf;
    @Mock   LoadManager loadManagerMockAGProc;
    @Mock   LoadManager loadManagerMockAGP;
    @Mock   LoadManager loadManagerMockAEP;
    @Mock   LoadManager loadManagerMockAET;
    @Mock   LoadManager loadManagerMockREN;
    @Mock   LoadManager loadManagerMockPC;
    @Mock   LoadManager loadManagerUEmpresaMock;
    @Mock   LoadManager loadManagerEmpresaMock;
    @Mock   LoadManager loadManagerUsuarioCadsusOcorrenciaMock;
    @Mock   Parametro parametroMock;
    @Mock   RecoveryProperties recoveryProperties;



    @Before
    public void setUp() throws Exception {
        mockStatic(AtendimentoFacade.class);
        mockStatic(BOFactory.class);
        mockStatic(CacheEntityProperties.class);
        mockStatic(CadastroFacade.class);
        mockStatic(CargaBasicoPadrao.class);
        mockStatic(CommandServerFacade.class);
        mockStatic(CommomFacade.class);
        mockStatic(FacadeBO.class);
        mockStatic(HospitalFacade.class);
        mockStatic(LoadManager.class);
        mockStatic(RecoveryProperties.class);
        mockStatic(SessaoAplicacaoImp.class);

        when(SessaoAplicacaoImp.getInstance()).thenReturn(abstractSessaoAplicacaoMock);
        when(abstractSessaoAplicacaoMock.getUsuario()).thenReturn(getUsuario());

        when(CargaBasicoPadrao.getInstance()).thenReturn(cargaBasicoPadraoMock);
        when(cargaBasicoPadraoMock.getParametroPadrao()).thenReturn(parametroMock);
        when(parametroMock.getDataCompetenciaProcedimento()).thenReturn(DataUtil.getDataAtual());

        when(BOFactory.getBO(CommomFacade.class)).thenReturn(commomFacadeMock);
        when(commomFacadeMock.modulo(Modulos.UNIDADE_SAUDE)).thenReturn(iParameterModuleContainerMock);
        when(iParameterModuleContainerMock.getParametro("procedimentoPadraoAtividadeGrupoProducao")).thenReturn(getProcedimento3());

        when(BOFactory.getBO(AtendimentoFacade.class)).thenReturn(atendimentoFacadeMock);
        when(BOFactory.getBO(CadastroFacade.class)).thenReturn(cadastroFacadeMock);
        when(cadastroFacadeMock.save(getAtividadeGrupo())).thenReturn(getAtividadeGrupo());
        when(BOFactory.getBO(HibernateSessionFactoryFacade.class)).thenReturn(hibernateSessionFactoryFacadeMock);
        when(hibernateSessionFactoryFacadeMock.getSession()).thenReturn(sessionMock);

        when(BOFactory.getBO(HospitalFacade.class)).thenReturn(hospitalFacadeMock);
        when(hospitalFacadeMock.encontrarContaPaciente((AtividadeGrupo) any())).thenReturn(getContaPaciente());

        when(BOFactory.getBO(CommandServerFacade.class)).thenReturn(commandServerFacadeMock);

        when(HibernateSessionFactory.getSession()).thenReturn(sessionMock);
        when(criteriaMock.add(any())).thenReturn(criteriaMock);

        mockCriteria(sessionMock, ProcedimentoCbo.class);
        mockCriteria(sessionMock, Usuario.class);
        mockCriteria(sessionMock, UsuarioEmpresa.class);
        mockCriteria(sessionMock, ItemContaPaciente.class);
        mockCriteria(sessionMock, AtividadeGrupo.class);

        mockLoadManager(loadManagerIMCMock, IndiceImc.class);
        mockLoadManager(loadManagerCboMock, ProcedimentoCbo.class);
        mockLoadManager(loadManagerCboMock, CboFichaEsusItem.class);
        mockLoadManager(loadManagerEquipeProfissionalMock, EquipeProfissional.class);
        mockLoadManager(loadManagerEquipeMock, Equipe.class);
        mockLoadManager(loadManagerMockAG, AtividadeGrupo.class);
        mockLoadManager(loadManagerMockAGP, AtividadeGrupoPaciente.class);
        mockLoadManager(loadManagerMockAGProf, AtividadeGrupoProfissional.class);
        mockLoadManager(loadManagerMockAGProc, AtividadeGrupoProcedimento.class);
        mockLoadManager(loadManagerMockAEP, AtividadeEloPublico.class);
        mockLoadManager(loadManagerMockAET, AtividadeEloTema.class);
        mockLoadManager(loadManagerMockREN, RegistrarEstadoNutricional.class);
        mockLoadManager(loadManagerMockPC, ProcedimentoCompetencia.class);
        mockLoadManager(loadManagerDadoMock, UsuarioCadsusDado.class);
        mockLoadManager(loadManagerUEmpresaMock, UsuarioEmpresa.class);
        mockLoadManager(loadManagerEmpresaMock, Empresa.class);
        mockLoadManager(loadManagerUsuarioCadsusOcorrenciaMock, UsuarioCadsusOcorrencia.class);

        initGestaoAtividadeGrupoDTO();
        initSalvarAtividadeGrupo();
    }

    public void mockCriteria(Session sessionMock, Class clazz) throws Exception {
        Criteria criteriaMock = mock(Criteria.class);
        when(HibernateSessionFactory.getSession()).thenReturn(sessionMock);
        when(sessionMock.createCriteria(clazz)).thenReturn(criteriaMock);
        when(criteriaMock.add(any())).thenReturn(criteriaMock);
        when(criteriaMock.uniqueResult()).thenReturn(getObject(clazz));
        when(criteriaMock.list()).thenReturn((List) getObjectList(clazz));
    }

    @Test
    public void test_executeSalvarAtividadeGrupo() throws ValidacaoException, DAOException {
        sag.execute();
        AtividadeGrupo atividadeGrupoSaved = sag.getAtividadeGrupoSave();
        assertNotNull(atividadeGrupoSaved);
    }

    @Test
    public void test_gerarIndiceIMC() throws Exception {
        AtividadeGrupoPaciente atividadeGrupoPaciente = sag.gerarIndiceIMC(getAtividadeGrupoPacienteGestante());
        Assert.assertNotNull(atividadeGrupoPaciente);
    }
//
//    @Test(expected = Test.None.class)
//    public void test_excluirItemContaPaciente_NaoDeveRetornarExeption() throws ValidacaoException, DAOException {
//        sag.excluirItemContaPaciente();
//    }
//
//    @Test
//    public void test_getEmpresaBPA_DeveTerCodigoEDescricaoDiferentes() {
//        Empresa empresa = sag.getEmpresaBpa(getEmpresa(), getProfissional1());
//        assertThat(empresa.getCodigo(),is(not(getEmpresa().getCodigo())));
//        assertThat(empresa.getDescricao(),is(not(getEmpresa().getDescricao())));
//    }
//
//    @Test
//    public void test_newAtividadeGrupoProcedimento_NaoDeveRetornarNull() throws Exception {
//        dto.setAtividadeGrupo(getAtividadeGrupoProcedimentoColetivo());
//        initSalvarAtividadeGrupo();
//        Assert.assertNotNull(sag.newAtividadeGrupoProcedimento(getProfissional1(), getTabelaCbo()));
//        initGestaoAtividadeGrupoDTO();
//        initSalvarAtividadeGrupo();
//    }
//
//    @Test(expected = AssertionError.class)
//    public void test_newAtividadeGrupoProcedimento_DeveRetornarException() throws Exception {
//        dto.setAtividadeGrupo(getAtividadeGrupoProcedimentoColetivo());
//        initSalvarAtividadeGrupo();
//        Assert.assertNull(sag.newAtividadeGrupoProcedimento(getProfissional1(), getTabelaCbo()));
//    }
//
//    @Test(expected = AssertionError.class)
//    public void test_newAtividadeGrupoProcedimento_SemOutroProcedimentoColetivo_DeveRetornarException() throws Exception {
//        dto.setAtividadeGrupo(getAtividadeGrupoSemProcedimentoColetivo());
//        initSalvarAtividadeGrupo();
//        Assert.assertNull(sag.newAtividadeGrupoProcedimento(getProfissional1(), getTabelaCbo()));
//    }
//
//    @Test
//    public void test_carregaProcedimento_NaoDeveRetornarNull() {
//        Assert.assertNotNull(sag.carregarProcedimentoCompetencia(getProcedimento1()));
//    }
//
//    @Test
//    public void test_getProcedimentoCbo_DeveRetornarNull() {
//        Assert.assertNull(sag.getProcedimentoCbo(null, null));
//    }
//
//    @Test
//    public void test_getProcedimentoCbo_NaoDeveRetornarNull() {
//        Assert.assertNotNull(sag.getProcedimentoCbo(getProcedimentoCompetencia(),getTabelaCbo()));
//    }
//
//    @Test
//    public void test_procedimentoJaAdicionado_DeveRetornarTrue() {
//        Assert.assertTrue(sag.procedimentoJaAdicionado(getProcedimento1(),getProfissional1()));
//    }
//
//    @Test
//    public void test_procedimentoJaAdicionado_DeveRetornarFalse() {
//        sag = new SalvarAtividadeGrupo(new GestaoAtividadeGrupoDTO(),true);
//        Assert.assertFalse(sag.procedimentoJaAdicionado(getProcedimento1(),getProfissional2()));
//    }
//
//    @Test(expected = Test.None.class)
//    public void test_registrarEstadoNutricional_NaoDeveRetornarException() throws Exception {
//       sag.registrarEstadoNutricional(getAtividadeGrupoPacienteList());
//    }
//
//    @Test(expected = NullPointerException.class)
//    public void test_RegistrarOcorrencia_SemAtividadeGrupo_DeveRetorarException() throws ValidacaoException, DAOException {
//        sag.registrarOcorrenciaPacientesFaltantes(getAtividadeGrupoPacienteList());
//    }
//
    @Test(expected = Test.None.class)
    public void test_RegistrarOcorrencia_ComAtividadeGrupo_NaoDeveRetorarException() throws Exception {
        sag.setAtividadeGrupoSave(getAtividadeGrupo());
        sag.registrarOcorrenciaPacientesFaltantes(getAtividadeGrupoPacienteList());
    }

    private void initSalvarAtividadeGrupo() throws Exception {
        boolean finalizar = true;
        sag = new SalvarAtividadeGrupo(dto, finalizar);
    }

    @NotNull
    private GestaoAtividadeGrupoDTO initGestaoAtividadeGrupoDTO() throws Exception {
        dto = new GestaoAtividadeGrupoDTO();
        dto.setAtividadeGrupo(getAtividadeGrupo());
        dto.setLstAtividadeGrupoProcedimento(getLstAtividadeGrupoProcedimento());
        dto.setLstAtividadeGrupoProfissional(getLstAtividadeGrupoProfissional());
        dto.setLstAtividadeEloPratica(getAtividadeEloPraticaList());
        dto.setLstAtividadeGrupoPaciente(getAtividadeGrupoPacienteWebDTO());
        return dto;
    }

    private List<AtividadeGrupoProfissional> getLstAtividadeGrupoProfissional() throws Exception {
        List<AtividadeGrupoProfissional> lista = new ArrayList<>();
        lista.add(getAtividadeGrupoProfissional());
        lista.add(getAtividadeGrupoProfissional2());
        return lista;
    }

    private AtividadeGrupoProfissional getAtividadeGrupoProfissional() throws Exception {
        AtividadeGrupoProfissional agp = new AtividadeGrupoProfissional(2345L);
        agp.setFlagResponsavel(RepositoryComponentDefault.SIM_LONG);
        agp.setTabelaCbo(getTabelaCbo());
        agp.setProfissional(getProfissional1());
        agp.setAtividadeGrupo(getAtividadeGrupo());
        return agp;
    }
    private AtividadeGrupoProfissional getAtividadeGrupoProfissional2() throws Exception {
        AtividadeGrupoProfissional agp = new AtividadeGrupoProfissional(1234L);
        agp.setFlagResponsavel(RepositoryComponentDefault.NAO_LONG);
        agp.setTabelaCbo(getTabelaCbo());
        agp.setProfissional(getProfissional2());
        agp.setAtividadeGrupo(getAtividadeGrupo());
        return agp;
    }

    private List<EquipeProfissional> getEquipeProfissionalList() {
        List<EquipeProfissional> retorno = new ArrayList<>();
        retorno.add(getEquipeProfissional());
        return retorno;
    }

    private EquipeProfissional getEquipeProfissional() {
        EquipeProfissional ep = new EquipeProfissional();
        ep.setCodigo(1234L);
        ep.setEquipe(getEquipe());
        ep.setProfissional(getProfissional2());
        return ep;
    }

    private Equipe getEquipe() {
        Equipe e = new Equipe(1234L);
        e.setAtivo("S");
        e.setEquipeCnes("1234567890");
        return e;
    }


    private List<AtividadeGrupoPacienteWebDTO> getAtividadeGrupoPacienteWebDTO() {
        AtividadeGrupoPacienteWebDTO dto = new AtividadeGrupoPacienteWebDTO();
        dto.setAtividadeGrupoPaciente(getAtividadeGrupoPaciente());

        return Collections.singletonList(dto);
    }

    private ProcedimentoCompetencia getProcedimentoCompetencia() {
        ProcedimentoCompetenciaPK id = new ProcedimentoCompetenciaPK();
        ProcedimentoCompetencia pc = new ProcedimentoCompetencia();

        id.setDataCompetencia(DataUtil.getDataAtual());
        id.setProcedimento(getProcedimento3());
        pc.setId(id);
        return pc;
    }

    private TabelaCbo getTabelaCbo() {
        TabelaCbo tc = new TabelaCbo();
        tc.setCbo("CBO_TESTE");
        return tc;
    }

    private Usuario getUsuario() {
        Usuario u = new Usuario();
        u.setNome("Cristian");
        u.setCodigo(1234L);
        return u;
    }

    private UsuarioEmpresa getUsuarioEmpresa(){
        UsuarioEmpresa ue = new UsuarioEmpresa();
        ue.setCodigo(1234L);
        ue.setUsuario(getUsuario());
        ue.setEmpresa(getEmpresa());
        ue.setEmpresaBpa(getEmpresaBPA());
        return ue;
    }

    private ProcedimentoCbo getProcedimentoCbo() {
        ProcedimentoCbo procedimentoCbo = new ProcedimentoCbo();
        ProcedimentoCboPK id = new ProcedimentoCboPK();
        id.setTabelaCbo(getTabelaCbo());
        id.setProcedimentoCompetencia(getProcedimentoCompetencia());
        procedimentoCbo.setId(id);
        return procedimentoCbo;
    }

    private List<Usuario> getUsuarioList() {
        return Collections.singletonList(getUsuario());
    }

    private List<UsuarioEmpresa> getUsuarioEmpresaList() {
        return Collections.singletonList(getUsuarioEmpresa());
    }

    private List<AtividadeGrupoProcedimento> getLstAtividadeGrupoProcedimento() {
        List<AtividadeGrupoProcedimento> retorno = new ArrayList<>();
        retorno.add(getAtividadeGrupoProcedimento1());
        retorno.add(getAtividadeGrupoProcedimento2());
        return retorno;
    }

    private AtividadeGrupoProcedimento getAtividadeGrupoProcedimento1() {
        AtividadeGrupoProcedimento agp = new AtividadeGrupoProcedimento();
        agp.setProfissional(getProfissional1());
        agp.setProcedimento(getProcedimento1());
        return agp;
    }

    private AtividadeGrupoProcedimento getAtividadeGrupoProcedimento2() {
        AtividadeGrupoProcedimento agp = new AtividadeGrupoProcedimento();
        agp.setProfissional(getProfissional2());
        agp.setProcedimento(getProcedimento2());
        return agp;
    }

    private Profissional getProfissional1() {
        Profissional p = new Profissional();
        p.setCodigo(1234L);
        p.setCodigoCns("822388557640004");
        p.setCidade(getCidade());
        return p;
    }

    private Profissional getProfissional2() {
        Profissional p = new Profissional();
        p.setCodigo(4567L);
        p.setCodigoCns("822388557640004");
        p.setCidade(getCidade());
        return p;
    }

    private Cidade getCidade() {
        Cidade c = new Cidade();
        c.setCodigo(1234L);
        c.setDescricao("CIDADE TESTE");
        return c;
    }


    private Procedimento getProcedimento1() {
        Procedimento p = new Procedimento();
        p.setDescricao("Procedimento 1");
        p.setCodigo(1234L);
        return p;
    }

    private Procedimento getProcedimento2() {
        Procedimento p = new Procedimento();
        p.setDescricao("Procedimento 2");
        p.setCodigo(4567L);
        return p;
    }
    private Procedimento getProcedimento3() {
        Procedimento p = new Procedimento();
        p.setDescricao("Procedimento 3");
        p.setCodigo(7890L);
        return p;
    }

    private void mockLoadManager(LoadManager loadManager, Class clazz) throws Exception {
        String[] properties = {};
        when(LoadManager.getInstance(clazz)).thenReturn(loadManager);
        when(loadManager.start()).thenReturn(loadManager);
        when(loadManager.startLeitura()).thenReturn(loadManager);
        when(loadManager.exists()).thenReturn(true);
        when(loadManager.setId(any())).thenReturn(loadManager);
        when(loadManager.setMaxResults(any())).thenReturn(loadManager);
        when(loadManager.addProperty(any())).thenReturn(loadManager);
        when(loadManager.addProperties(any())).thenReturn(loadManager);
        when(loadManager.addParameter(any())).thenReturn(loadManager);
        when(loadManager.addParameter(any())).thenReturn(loadManager);
        when(loadManager.addGroup(any())).thenReturn(loadManager);
        when(loadManager.addGroup(any())).thenReturn(loadManager);
        when(loadManager.addSorter(any())).thenReturn(loadManager);
        when(loadManager.addSorter(any())).thenReturn(loadManager);
        when((List) loadManager.getList()).thenReturn((List) getObjectList(clazz));
        when(RecoveryProperties.applyAlias("", properties)).thenReturn(properties);
        when(CacheEntityProperties.getInstance()).thenReturn(cacheEntityPropertiesMock);
        when(cacheEntityPropertiesMock.getRecoveryProperties(clazz)).thenReturn(recoveryProperties);
        when(recoveryProperties.getPropertiesValues()).thenReturn(properties);
        when(loadManager.getVO()).thenReturn(getObject(clazz));
    }

    private Object getObject(Class clazz) throws Exception {
        if (clazz.isInstance(getAtividadeGrupo())) {
            return getAtividadeGrupo();
        } else if (clazz.isInstance(getIndiceImc())) {
            return getIndiceImc();
        } else if (clazz.isInstance(getAtividadeGrupoPaciente())) {
            return getAtividadeGrupoPaciente();
        } else if (clazz.isInstance(getProcedimentoCompetencia())) {
            return getProcedimentoCompetencia();
        } else if (clazz.isInstance(getProcedimentoCbo())) {
            return getProcedimentoCbo();
        } else if (clazz.isInstance(getUsuario())) {
            return getUsuario();
        } else if (clazz.isInstance(getUsuarioEmpresa())) {
            return getUsuarioEmpresa();
        } else if (clazz.isInstance(getContaPaciente())) {
            return getContaPaciente();
        } else if (clazz.isInstance(getItemContaPaciente())) {
            return getItemContaPaciente();
        } else if (clazz.isInstance(getUsuarioCadSusDado())) {
            return getUsuarioCadSusDado();
        } else if (clazz.isInstance(getEquipeProfissional())) {
            return getEquipeProfissional();
        } else if (clazz.isInstance(getCboFichaEsusItem())) {
            return getCboFichaEsusItem();
        } else if (clazz.isInstance(getEquipe())) {
            return getEquipe();
        } else if (clazz.isInstance(getAtividadeGrupoProfissional())) {
            return getAtividadeGrupoProfissional();
        } else if (clazz.isInstance(getAtividadeGrupoProcedimento1())) {
            return getAtividadeGrupoProcedimento1();
        } else if (clazz.isInstance(getAtividadeEloPublico())) {
            return getAtividadeEloPublico();
        } else if (clazz.isInstance(getAtividadeEloTema())) {
            return getAtividadeEloTema();
        }
        return new Object();
    }

    private Object getObjectList(Class clazz) throws Exception {
        if (clazz.isInstance(getAtividadeGrupo())) {
            return getAtividadeGrupoList();
        } else if (clazz.isInstance(getAtividadeGrupoPaciente())) {
            return getAtividadeGrupoPacienteList();
        } else if (clazz.isInstance(getProcedimentoCompetencia())) {
            return Collections.singletonList(getProcedimentoCompetencia());
        } else if (clazz.isInstance(getProcedimentoCbo())) {
            return Collections.singletonList(getProcedimentoCbo());
        } else if (clazz.isInstance(getUsuario())) {
            return getUsuarioList();
        } else if (clazz.isInstance(getUsuarioEmpresa())) {
            return getUsuarioEmpresaList();
        } else if (clazz.isInstance(getContaPaciente())) {
            return getContaPacienteList();
        } else if (clazz.isInstance(getItemContaPaciente())) {
            return getItemContaPacienteList();
        } else if (clazz.isInstance(getUsuarioCadSusDado())) {
            return getUsuarioCadSusDadoList();
        } else if (clazz.isInstance(getIndiceImc())) {
            return getIndiceImcList();
        } else if (clazz.isInstance(getEquipeProfissional())) {
            return getEquipeProfissionalList();
        } else if (clazz.isInstance(getCboFichaEsusItem())) {
            return getCboFichaEsusItemList();
        } else if (clazz.isInstance(getEquipe())) {
            return getEquipeList();
        } else if (clazz.isInstance(getAtividadeGrupoProfissional())) {
            return getLstAtividadeGrupoProfissional();
        } else if (clazz.isInstance(getAtividadeGrupoProcedimento1())) {
            return getLstAtividadeGrupoProcedimento();
        } else if (clazz.isInstance(getAtividadeEloPublico())) {
            return getAtividadeEloPublicoList();
        } else if (clazz.isInstance(getAtividadeEloTema())) {
            return getAtividadeEloTemaList();
        } else if (clazz.isInstance(getEmpresa())) {
            return getEmpresaList();
        }
        return new ArrayList<>();
    }

    private Object getAtividadeEloPublicoList() {
        return Collections.singletonList(getAtividadeEloPublico());
    }

    private Object getAtividadeEloTemaList() {
        List<AtividadeEloTema> lista = new ArrayList<>();
        lista.add(getAtividadeEloTema());
        lista.add(getAtividadeEloTema2());
        return lista;
    }

    private AtividadeEloPublico getAtividadeEloPublico() {
        AtividadeEloPublico aep = new AtividadeEloPublico(1234L);
        return aep;
    }

    private AtividadeEloTema getAtividadeEloTema() {
        AtividadeEloTema aet = new AtividadeEloTema(1234L);
        return aet;
    }
    private AtividadeEloTema getAtividadeEloTema2() {
        AtividadeEloTema aet = new AtividadeEloTema(2345L);
        aet.setTipoAtividadeTema(getTipoAtividadeTema());
        return aet;
    }




    private List<Equipe> getEquipeList() {
        return Collections.singletonList(getEquipe());
    }

    private List<CboFichaEsusItem> getCboFichaEsusItemList() {
        return Collections.singletonList(getCboFichaEsusItem());
    }

    private CboFichaEsusItem getCboFichaEsusItem() {
        CboFichaEsusItem cboFichaEsusItem = new CboFichaEsusItem();
        cboFichaEsusItem.setCodigo(1234L);
        cboFichaEsusItem.setTabelaCbo(getTabelaCbo());
        return cboFichaEsusItem;
    }

    private AtividadeGrupo getAtividadeGrupo() throws Exception {
        AtividadeGrupo atividadeGrupo = new AtividadeGrupo();
        atividadeGrupo.setCodigo(1234L);
        atividadeGrupo.setParticipantes(1L);
        atividadeGrupo.setDataInicio(DataUtil.stringToDate("01/01/2020"));
        atividadeGrupo.setDataFim(DataUtil.getDataAtual());
        atividadeGrupo.setEmpresa(getEmpresa());
        atividadeGrupo.setTipoAtividadeGrupo(getTipoAtividadeGrupo());
        atividadeGrupo.setLocalAtividadeGrupo(getLocalAtividadeGrupo());
        return atividadeGrupo;
    }

    private LocalAtividadeGrupo getLocalAtividadeGrupo() {
        LocalAtividadeGrupo lag = new LocalAtividadeGrupo();
        lag.setCodigo(1234L);
        lag.setDescricao("LOCAL ATIVIDADE GRUPO TESTE");
        lag.setTipoLocal(LocalAtividadeGrupo.TipoLocal.UNIDADE.value());
        return lag;
    }

    private TipoAtividadeGrupo getTipoAtividadeGrupo(){
        TipoAtividadeGrupo tag = new TipoAtividadeGrupo();
        tag.setCodigo(1234L);
        tag.setCodigoEsus(12345L);
        return tag;
    }

    private AtividadeGrupo getAtividadeGrupoProcedimentoColetivo() throws Exception {
        AtividadeGrupo atividadeGrupo = new AtividadeGrupo();
        atividadeGrupo.setCodigo(1234L);
        atividadeGrupo.setDataInicio(DataUtil.stringToDate("01/01/2020"));
        atividadeGrupo.setEmpresa(getEmpresa());
        atividadeGrupo.setOutroProcedimentoColetivo(procedimentoEloEsusColetivo());
        return atividadeGrupo;
    }

    private AtividadeGrupo getAtividadeGrupoSemProcedimentoColetivo() throws Exception {
        AtividadeGrupo atividadeGrupo = new AtividadeGrupo();
        atividadeGrupo.setCodigo(1234L);
        atividadeGrupo.setDataInicio(DataUtil.stringToDate("01/01/2020"));
        atividadeGrupo.setEmpresa(getEmpresa());
        return atividadeGrupo;
    }

    private AtividadeEloTema getAtividadeEloPratica() {
        AtividadeEloTema atividadeEloTema = new AtividadeEloTema();
        atividadeEloTema.setCodigo(1234L);
        atividadeEloTema.setTipoAtividadeTema(getTipoAtividadeTema());
        return atividadeEloTema;
    }

    private TipoAtividadeTema getTipoAtividadeTema() {
        TipoAtividadeTema tat = new TipoAtividadeTema();
        tat.setCodigo(1234L);
        tat.setCodigoEsus(TipoAtividadeTema.CodigoESUSPraticasSaude.PNCT1.value());
        return tat;
    }

    private List<AtividadeEloTema> getAtividadeEloPraticaList(){
        return Collections.singletonList(getAtividadeEloPratica());
    }

    private ProcedimentoEloEsus procedimentoEloEsusColetivo() {
        ProcedimentoEloEsus pee = new ProcedimentoEloEsus();
        pee.setCodigo(1234L);
        pee.setProcedimento(getProcedimento3());
        return pee;
    }

    private AtividadeGrupoPaciente getAtividadeGrupoPaciente() {
        AtividadeGrupoPaciente atividadeGrupoPaciente = new AtividadeGrupoPaciente();
        atividadeGrupoPaciente.setUsuarioCadsus(getUsuarioCadSus());
        atividadeGrupoPaciente.setSituacao(AtividadeGrupoPaciente.Situacao.PRESENTE.value());
        atividadeGrupoPaciente.setPeso(123D);
        atividadeGrupoPaciente.setAltura(123D);
        atividadeGrupoPaciente.setAbandonouGrupo(RepositoryComponentDefault.NAO_LONG);
        atividadeGrupoPaciente.setCessouHabitoFumar(RepositoryComponentDefault.SIM_LONG);
        return atividadeGrupoPaciente;
    }
    private AtividadeGrupoPaciente getAtividadeGrupoPacienteGestante() throws Exception {
        AtividadeGrupoPaciente atividadeGrupoPaciente = new AtividadeGrupoPaciente();
        atividadeGrupoPaciente.setUsuarioCadsus(getUsuarioCadSus());
        atividadeGrupoPaciente.setSituacao(AtividadeGrupoPaciente.Situacao.PRESENTE.value());
        atividadeGrupoPaciente.setPeso(123D);
        atividadeGrupoPaciente.setAltura(123D);
        atividadeGrupoPaciente.setDum(DataUtil.stringToDate("01/08/2020"));
        atividadeGrupoPaciente.setGestante(RepositoryComponentDefault.SIM_LONG);
        atividadeGrupoPaciente.setAbandonouGrupo(RepositoryComponentDefault.NAO_LONG);
        atividadeGrupoPaciente.setCessouHabitoFumar(RepositoryComponentDefault.SIM_LONG);
        return atividadeGrupoPaciente;
    }
    private AtividadeGrupoPaciente getAtividadeGrupoPacienteAusente() {
        AtividadeGrupoPaciente atividadeGrupoPaciente = new AtividadeGrupoPaciente();
        atividadeGrupoPaciente.setUsuarioCadsus(getUsuarioCadSus());
        atividadeGrupoPaciente.setSituacao(AtividadeGrupoPaciente.Situacao.AUSENTE.value());
        return atividadeGrupoPaciente;
    }

    private List<AtividadeGrupo> getAtividadeGrupoList() throws Exception {
        return Collections.singletonList(getAtividadeGrupo());
    }

    private List<AtividadeGrupoPaciente> getAtividadeGrupoPacienteList() {
        List<AtividadeGrupoPaciente> retorno = new ArrayList<>();
        retorno.add(getAtividadeGrupoPaciente());
        retorno.add(getAtividadeGrupoPacienteAusente());
        return retorno;
    }

    private UsuarioCadsus getUsuarioCadSus() {
        return new UsuarioCadsus(1234L);
    }

    private Empresa getEmpresa() {
        Empresa e = new Empresa(1234L);
        e.setDescricao("EMPRESA_TESTE");
        e.setCnpj("01947063000112");
        e.setCnes("123123456");
        e.setAtividade(getAtividadeEmpresa());
        return e;
    }

    private List<Empresa> getEmpresaList() {
        return Collections.singletonList(getEmpresa());
    }

    private Empresa getEmpresaBPA() {
        Empresa e = new Empresa(456L);
        e.setDescricao("EMPRESA_TESTE_BPA");
        e.setAtividade(getAtividadeEmpresa());
        return e;
    }

    private Atividade getAtividadeEmpresa() {
        return new Atividade(70L);
    }

    private List<IndiceImc> getIndiceImcList() {
        return Collections.singletonList(getIndiceImc());
    }

    private IndiceImc getIndiceImc() {
        IndiceImc imc = new IndiceImc();
        imc.setCodigo(1234L);
        imc.setIdadeGestacional(1234);
        imc.setSexo(RepositoryComponentDefault.SEXO_FEMININO);
        return imc;
    }

    private ContaPaciente getContaPaciente() {
        ContaPaciente cp = new ContaPaciente();
        cp.setCodigo(1234L);
        return cp;
    }

    private List<ContaPaciente> getContaPacienteList() {
        return Collections.singletonList(getContaPaciente());
    }

    private ItemContaPaciente getItemContaPaciente() {
        ItemContaPaciente itemContaPaciente = new ItemContaPaciente();
        itemContaPaciente.setCodigo(1234L);
        return itemContaPaciente;
    }

    private List<ItemContaPaciente> getItemContaPacienteList() {
        return Collections.singletonList(getItemContaPaciente());
    }

    private UsuarioCadsusDado getUsuarioCadSusDado() {
        UsuarioCadsusDado dado = new UsuarioCadsusDado(1234L);
        dado.setAltura(170D);
        dado.setPeso(123D);
        return dado;
    }

    private List<UsuarioCadsusDado> getUsuarioCadSusDadoList() {
        return Collections.singletonList(getUsuarioCadSusDado());
    }

}