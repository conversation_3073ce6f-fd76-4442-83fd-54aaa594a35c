package br.com.ksisolucoes.bo.cadsus.usuariocadsus.enums;

import ch.lambdaj.Lambda;
import ch.lambdaj.function.convert.Converter;
import com.google.common.base.CaseFormat;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;

import java.util.Collection;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;

@RunWith(value = Parameterized.class)
public class UnPredefinedUsuarioCadsusAliasSortTest {

    private final String column;
    private String sort;

    public UnPredefinedUsuarioCadsusAliasSortTest(String column) {
        this.column = column;
    }

    @Parameters
    public static Collection<Object[]> generateParams() {
        return Lambda.convert(UsuarioCadsusAliasSort.values(), new Converter<UsuarioCadsusAliasSort, Object[]>() {
            @Override
            public Object[] convert(UsuarioCadsusAliasSort from) {
                return new Object[]{CaseFormat.UPPER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, from.name() + "x")};
            }
        });
    }

    @Test
    public void isPredefined() {
        assertFalse(UsuarioCadsusAliasSort.isPredefined(column));
    }

    @Test
    public void getSortFieldTest() {
        assertEquals(UsuarioCadsusAliasSort.getSortField(column), column);
    }

}
