package br.com.ksisolucoes.bo.cadsus.usuariocadsus;

import br.com.celk.atendimento.recepcao.marcacao.MarcacaoConsultaDTOParam;
import br.com.ksisolucoes.bo.RecoveryProperties;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.ConsultaUsuarioCadsusDTO;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QuerySorter;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomSorter;
import br.com.ksisolucoes.dao.CacheEntityProperties;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPaging.Type;
import br.com.ksisolucoes.dao.paginacao.DataPagingImpl;
import br.com.ksisolucoes.system.consulta.ConsultaQuerySupportImpl;
import br.com.ksisolucoes.system.consulta.IConsultaQuerySupport;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import org.junit.Test;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

import static junit.framework.Assert.assertEquals;
import static junit.framework.Assert.assertTrue;

public class QueryConsultaUsuarioCadsusMarcacaoTest {

    @Test
    public void testSortNome() throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
        testOrder("nome", "dom.nomeReferencia asc");
    }
    
    @Test
    public void testSortTestNotPredefined() throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
        testOrder("teste", "");
    }
    
    @Test
    public void testSortCodigo() throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
        testOrder("codigo", "dom.usuarioCadsus.codigo asc");
    }
    
    @Test
    public void testSortDataNascimento() throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
        testOrder("dataNascimento", "dom.dataNascimento asc");
    }
    
    @Test
    public void testSortSexo() throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
        testOrder("sexo", "u.sexo asc");
    }
    
    @Test
    public void testSortNomeMae() throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
        testOrder("nomeMae", "dom.nomeMae asc");
    }
    
    @Test
    public void testSortSituacao() throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
        testOrder("situacao", "u.situacao asc");
    }
    
    @Test
    public void testNomeFilter() throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
        MarcacaoConsultaDTOParam param = new MarcacaoConsultaDTOParam();
        param.setNome("teste");
        assertEquals(" dom.situacao not in  ( :_restriction1 )  and dom.nomeReferencia like '%TESTE%' ", buildHQLHelper(param, "nome").getWhere().toString());
    }
    
    @Test
    public void testNumemroCartaoFilter() throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
        MarcacaoConsultaDTOParam param = new MarcacaoConsultaDTOParam();
        param.setNumeroCartao("1234");
        assertEquals(" dom.situacao not in  ( :_restriction1 )  and dom.cns = :_restriction2", buildHQLHelper(param).getWhere().toString());
    }
    
    @Test
    public void testNumemroCartaoZeroFilter() throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
        MarcacaoConsultaDTOParam param = new MarcacaoConsultaDTOParam();
        param.setNumeroCartao("0");
        assertEquals(" dom.situacao not in  ( :_restriction1 ) ", buildHQLHelper(param).getWhere().toString());
    }
    
    @Test
    public void testInativosFilter() throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
        MarcacaoConsultaDTOParam param = new MarcacaoConsultaDTOParam();
        param.setExibirInativos(RepositoryComponentDefault.SIM);
        assertEquals(" dom.situacao <> :_restriction1", buildHQLHelper(param).getWhere().toString());
    }
    
    @SuppressWarnings("unchecked")
    @Test
    public void testCustomSortersPredefined() throws NoSuchMethodException, SecurityException, IllegalAccessException, IllegalArgumentException, InvocationTargetException {
        QueryConsultaUsuarioCadsusMarcacao queryMarcacao = new QueryConsultaUsuarioCadsusMarcacao(new MarcacaoConsultaDTOParam());
        Method declaredCustomSorters = QueryConsultaUsuarioCadsusMarcacao.class.getDeclaredMethod("customSorters", List.class);
        List<QuerySorter> sorters = new ArrayList<>();
        sorters.add(new QueryCustomSorter("nome", QuerySorter.CRESCENTE));
        List<QuerySorter> result = (List<QuerySorter>) declaredCustomSorters.invoke(queryMarcacao, sorters);
        assertTrue(result.isEmpty());
    }
    
    @SuppressWarnings("unchecked")
    @Test
    public void testCustomSortersNotPredefined() throws NoSuchMethodException, SecurityException, IllegalAccessException, IllegalArgumentException, InvocationTargetException {
        QueryConsultaUsuarioCadsusMarcacao queryMarcacao = new QueryConsultaUsuarioCadsusMarcacao(new MarcacaoConsultaDTOParam());
        Method declaredCustomSorters = QueryConsultaUsuarioCadsusMarcacao.class.getDeclaredMethod("customSorters", List.class);
        List<QuerySorter> sorters = new ArrayList<>();
        QueryCustomSorter testeItem = new QueryCustomSorter("teste", QuerySorter.CRESCENTE);
        sorters.add(testeItem);
        List<QuerySorter> result = (List<QuerySorter>) declaredCustomSorters.invoke(queryMarcacao, sorters);
        assertTrue(result.contains(testeItem));
    }
    
    private void testOrder(String name, String result) throws NoSuchFieldException, IllegalAccessException {
        assertEquals(result, buildHQLHelper(new MarcacaoConsultaDTOParam(), name).getOrder().toString());
    }
    
    private HQLHelper buildHQLHelper(MarcacaoConsultaDTOParam param, String name) throws NoSuchFieldException, IllegalAccessException {
        QueryConsultaUsuarioCadsusMarcacao queryMarcacao = new QueryConsultaUsuarioCadsusMarcacao(param);
        DataPaging<ConsultaUsuarioCadsusDTO> dataPaging = new DataPagingImpl<ConsultaUsuarioCadsusDTO>(Type.ALVO_LIST);
        List<QuerySorter> sorters = new ArrayList<>();
        QuerySorter querySorter = new QueryCustomSorter(name, QuerySorter.CRESCENTE);
        sorters.add(querySorter);
        IConsultaQuerySupport querySupport = new   ConsultaQuerySupportImpl(ConsultaUsuarioCadsusDTO.class, null, null, sorters);
        dataPaging.putClientProperty(IConsultaQuerySupport.class.getName(), querySupport);
        buildCacheInstance();
        queryMarcacao.setDataPaging(dataPaging);
        HQLHelper hql = new HQLHelper();
        queryMarcacao.createQuery(hql);
        return hql;
    }
    
    private HQLHelper buildHQLHelper(MarcacaoConsultaDTOParam param) throws NoSuchFieldException, IllegalAccessException {
        return buildHQLHelper(param, "nome");
    }

    private void buildCacheInstance() throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
        Field declaredField = CacheEntityProperties.class.getDeclaredField("instance");
        declaredField.setAccessible(true);
        declaredField.set(CacheEntityProperties.class, CacheEntityProperties.create());
        CacheEntityProperties.getInstance().addRecoveryProperties(buildRecoveryProperties());
    }

    private RecoveryProperties buildRecoveryProperties()
            throws NoSuchFieldException, IllegalAccessException {
        RecoveryProperties recoveryProperties = new RecoveryProperties(UsuarioCadsus.class);
        Field declaredFieldSsingleProperties = RecoveryProperties.class.getDeclaredField("singleProperties");
        declaredFieldSsingleProperties.setAccessible(true);
        declaredFieldSsingleProperties.set(recoveryProperties, new String[] {"codigo", "usuario", "usuarioCadastro", "nome", "sexo", "nomeMae", "dataNascimento"});
        return recoveryProperties;
    }
}
