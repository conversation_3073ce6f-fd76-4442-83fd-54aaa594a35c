package br.com.ksisolucoes.bo.controle.usuario;

import br.com.ksisolucoes.vo.controle.Usuario;
import junit.framework.TestCase;
import org.junit.Before;
import org.junit.Test;

public class ReiniciarSenhaTest extends TestCase {

    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void testIsUsuarioVigilancia_deveRetornarCorretamenteSemQuebrar() {
        ReiniciarSenha reiniciarSenha = new ReiniciarSenha(new Usuario(), "");
        assertFalse(reiniciarSenha.isUsuarioVigilancia(null));

        Usuario usuario = new Usuario();
        usuario.setTipoUsuario(Usuario.TipoUsuario.USUARIO_VIGILANCIA.value());
        assertTrue(reiniciarSenha.isUsuarioVigilancia(usuario));

        for (Usuario.TipoUsuario value : Usuario.TipoUsuario.values()) {
            if (!Usuario.TipoUsuario.USUARIO_VIGILANCIA.value().equals(value.value())) {
                usuario.setTipoUsuario(value.value());
                assertFalse(reiniciarSenha.isUsuarioVigilancia(usuario));
            }
        }
    }
}