package br.com.ksisolucoes.bo.agendamento.exame.quebrarsolicitacao.cenarios;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.agendamento.exame.dto.angedamentosolictacaolote.AgendaDTO;
import br.com.ksisolucoes.agendamento.exame.dto.angedamentosolictacaolote.GradeDTO;
import br.com.ksisolucoes.agendamento.exame.dto.angedamentosolictacaolote.GradeHorarioDTO;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeHorario;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.exame.SolicitacaoAgendamentoExame;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class BuildCenario5 extends BuildCenarios {

    public static TipoProcedimento tipoProcedimento;
    public static AgendaDTO agendaDTO;
    public static GradeDTO grade1;
    public static GradeDTO grade2;

    public static List<ExameProcedimento> examesPrestadorRealiza;
    public static List<SolicitacaoAgendamentoExame> examesDaSolicitacao;
    public static ExamePrestadorCompetencia examePrestadorCompetencia;
    public static List<SolicitacaoAgendamentoExame> examesAtendidos;
    public static SolicitacaoAgendamentoExame exameSolicitacao_1;
    public static SolicitacaoAgendamentoExame exameSolicitacao_2;
    public static SolicitacaoAgendamentoExame exameSolicitacao_3;
    public static SolicitacaoAgendamentoExame exameSolicitacao_4;
    public static SolicitacaoAgendamentoExame exameSolicitacao_5;
    public static SolicitacaoAgendamentoExame exameSolicitacao_6;

    public static SolicitacaoAgendamento solicitacaoAgendamento;
    public static SolicitacaoAgendamento novaSolicitacao_1;
    public static SolicitacaoAgendamento novaSolicitacao_2;

    public static Long tipoTetoFinanceiro = ExameCotaPpi.TipoTeto.FINANCEIRO.value();
    public static List<ExamePrestadorProcedimento> examesPrestadorProcedimentos;

    static {
        tipoProcedimento = tipoProcedimento(2L);
        grade1 = gradeDTO(1L);
        grade2 = gradeDTO(2L);
        examePrestadorCompetencia = examePrestadorCompetencia();
        examesPrestadorRealiza = examesPrestadorRealiza();
        examesAtendidos = examesAtendidos();
        exameSolicitacao_1 = exameSolicitacao(1L);
        exameSolicitacao_2 = exameSolicitacao(2L);
        exameSolicitacao_3 = exameSolicitacao(3L);
        exameSolicitacao_4 = exameSolicitacao(4L);
        exameSolicitacao_5 = exameSolicitacao(5L);
        exameSolicitacao_6 = exameSolicitacao(6L);
        examesDaSolicitacao = examesDaSolicitacao();
        agendaDTO = agendaDTO(1L);

        solicitacaoAgendamento = solicitacaoAgendamento(1L);
        novaSolicitacao_1 = solicitacaoAgendamento(2L);
        novaSolicitacao_2 = solicitacaoAgendamento(3L);
        examesPrestadorProcedimentos = examesPrestadorProcedimentos(examesPrestadorRealiza);
    }

    private static List<SolicitacaoAgendamentoExame> examesAtendidos() {
        List<SolicitacaoAgendamentoExame> examesAtendidos = new ArrayList<>();
        examesAtendidos.add(exameSolicitacao(1L));
        examesAtendidos.add(exameSolicitacao(2L));
        examesAtendidos.add(exameSolicitacao(3L));
        return examesAtendidos;
    }

    private static ExamePrestadorCompetencia examePrestadorCompetencia() {
        ExamePrestadorCompetencia examePrestadorCompetencia = new ExamePrestadorCompetencia();
        examePrestadorCompetencia.setCodigo(1L);
        return examePrestadorCompetencia;
    }

    private static List<SolicitacaoAgendamentoExame> examesDaSolicitacao() {
        List<SolicitacaoAgendamentoExame> solicitacaoAgendamentoExames = new ArrayList<>();
        solicitacaoAgendamentoExames.add(exameSolicitacao_1);
        solicitacaoAgendamentoExames.add(exameSolicitacao_2);
        solicitacaoAgendamentoExames.add(exameSolicitacao_3);
        solicitacaoAgendamentoExames.add(exameSolicitacao_4);
        solicitacaoAgendamentoExames.add(exameSolicitacao_5);
        solicitacaoAgendamentoExames.add(exameSolicitacao_6);
        return solicitacaoAgendamentoExames;
    }

    private static List<ExameProcedimento> examesPrestadorRealiza() {
        List<ExameProcedimento> examesPrestadorRealiza = new ArrayList<>();
        examesPrestadorRealiza.add(exameProcedimento(1L));
        examesPrestadorRealiza.add(exameProcedimento(2L));
        examesPrestadorRealiza.add(exameProcedimento(3L));
        examesPrestadorRealiza.add(exameProcedimento(4L));
        examesPrestadorRealiza.add(exameProcedimento(5L));
        examesPrestadorRealiza.add(exameProcedimento(6L));
        return examesPrestadorRealiza;
    }

    public static AgendaDTO agendaDTO(Long id) {
        return new AgendaDTO()
                .setCodigo(id)
                .setGrades(grades())
                .setExamesPrestadorRealiza(examesPrestadorRealiza)
                .setExamePrestadorCompetencia(examePrestadorCompetencia);
    }

    private static List<GradeDTO> grades() {
        return Arrays.asList(grade1, grade2);
    }

    private static GradeDTO gradeDTO(Long id) {
        return new GradeDTO()
                .setCodigo(id)
                .setData(DataUtil.addDiasUteis(DataUtil.getDataAtual(), id.intValue()))
                .setExames(Collections.singletonList(gradeExame(id)))
                .setQuantidadeVagasDisponivel(1L)
                .setHorarios(horarios());
    }

    private static List<GradeHorarioDTO> horarios() {
        List<GradeHorarioDTO> horarios = new ArrayList<>();
        horarios.add(gradeHorarioDTO());
        return horarios;
    }

    private static GradeHorarioDTO gradeHorarioDTO() {
        return new GradeHorarioDTO().setCodigo(12L).setStatus(AgendaGradeHorario.Status.PENDENTE);
    }
}
