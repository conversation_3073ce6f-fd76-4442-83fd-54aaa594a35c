package br.com.ksisolucoes.bo.agendamento.exame.ordenarAgenda;

import br.com.ksisolucoes.TipoEstabelecimento;
import br.com.ksisolucoes.vo.basico.Empresa;

public class BuildEmpresa {


    public static Empresa unidadeQuitandinha() {
        Empresa quitandinha = new Empresa(1L);
        quitandinha.setDescricao("Quitandinha");
        quitandinha.setTipoUnidade(TipoEstabelecimento.UNIDADE.value());
        return quitandinha;
    }

    public static Empresa unidadeCentroIntegradoSaude() {
        Empresa centroIntegrado = new Empresa(2L);
        centroIntegrado.setDescricao("Centro Integrado de Saúde");
        centroIntegrado.setTipoUnidade(TipoEstabelecimento.UNIDADE_FILANTROPICA.value());
        return centroIntegrado;
    }

    public static Empresa unidadeVilaNova() {
        Empresa unidadeVilaNova = new Empresa(3L);
        unidadeVilaNova.setDescricao("Vila Nova");
        unidadeVilaNova.setTipoUnidade(TipoEstabelecimento.PRESTADOR_SERVICO.value());
        return unidadeVilaNova;
    }

    public static Empresa unidadeDiamantina() {
        Empresa quitandinha = new Empresa(4L);
        quitandinha.setDescricao("Diamantina");
        quitandinha.setTipoUnidade(TipoEstabelecimento.UNIDADE.value());
        return quitandinha;
    }

    public static Empresa unidadeTrindade() {
        Empresa trindade = new Empresa(5L);
        trindade.setDescricao("Trindade");
        trindade.setTipoUnidade(TipoEstabelecimento.EXTERNO.value());
        return trindade;
    }
}
