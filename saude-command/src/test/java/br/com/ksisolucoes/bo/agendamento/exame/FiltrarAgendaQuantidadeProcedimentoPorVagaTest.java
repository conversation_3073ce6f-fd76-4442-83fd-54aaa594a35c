package br.com.ksisolucoes.bo.agendamento.exame;

import br.com.celk.agendamento.AgendamentoHelper;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTO;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTOParam;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoPacienteDTO;
import br.com.ksisolucoes.bo.agendamento.exame.cenarios.BuildCenarioFiltrarAgendaVagasProcedimentoTest;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({AgendamentoHelper.class})
public class FiltrarAgendaQuantidadeProcedimentoPorVagaTest {

    @Before
    public void setup() {
        PowerMockito.mockStatic(AgendamentoHelper.class);
    }

    @Test
    public void deveRetornarListaAgendaVaziaTipoAgendaHorario() throws ValidacaoException {
        when(AgendamentoHelper.getTipoAgenda(BuildCenarioFiltrarAgendaVagasProcedimentoTest.agenda)).thenReturn(BuildCenarioFiltrarAgendaVagasProcedimentoTest.tipoAgendaHorario.value());

        AgendaGradeAtendimentoDTOParam params = BuildCenarioFiltrarAgendaVagasProcedimentoTest.buildParam(2L);
        List<AgendaGradeAtendimentoDTO> agendas = BuildCenarioFiltrarAgendaVagasProcedimentoTest.buildAgendaGradeAtendimentos();
        List<AgendaGradeAtendimentoPacienteDTO> horarios = BuildCenarioFiltrarAgendaVagasProcedimentoTest.buildHorariosCenario1();

        List<AgendaGradeAtendimentoDTO> agendasFiltradas = FiltrarAgendaQuantidadeProcedimentoPorVaga.filtrar(agendas, horarios, params);

        assertNotNull("lista de agenda não deve ser null", agendasFiltradas);
        assertTrue("lista de agenda deve ser vazia", agendasFiltradas.isEmpty());
    }

    @Test
    public void deveRetornarListaAgendaPopuladaTipoAgendaHorario() throws ValidacaoException {
        when(AgendamentoHelper.getTipoAgenda(BuildCenarioFiltrarAgendaVagasProcedimentoTest.agenda)).thenReturn(BuildCenarioFiltrarAgendaVagasProcedimentoTest.tipoAgendaHorario.value());

        AgendaGradeAtendimentoDTOParam params = BuildCenarioFiltrarAgendaVagasProcedimentoTest.buildParam(2L);
        List<AgendaGradeAtendimentoDTO> agendas = BuildCenarioFiltrarAgendaVagasProcedimentoTest.buildAgendaGradeAtendimentos();
        List<AgendaGradeAtendimentoPacienteDTO> horarios = BuildCenarioFiltrarAgendaVagasProcedimentoTest.buildHorariosCenario2();

        List<AgendaGradeAtendimentoDTO> agendasFiltradas = FiltrarAgendaQuantidadeProcedimentoPorVaga.filtrar(agendas, horarios, params);

        assertNotNull("lista de agenda não pode ser null", agendasFiltradas);
        assertFalse("lista de agenda não pode ser vazia", agendasFiltradas.isEmpty());
        assertEquals("deve ter uma agenda", 1, agendasFiltradas.size());
    }

    @Test
    public void deveRetornarListaAgendaPopuladaTipoAgendaDiaria() throws ValidacaoException {
        when(AgendamentoHelper.getTipoAgenda(BuildCenarioFiltrarAgendaVagasProcedimentoTest.agenda)).thenReturn(BuildCenarioFiltrarAgendaVagasProcedimentoTest.tipoAgendaDiario.value());

        AgendaGradeAtendimentoDTOParam params = BuildCenarioFiltrarAgendaVagasProcedimentoTest.buildParam(2L);
        List<AgendaGradeAtendimentoDTO> agendas = BuildCenarioFiltrarAgendaVagasProcedimentoTest.buildAgendaGradeAtendimentos();
        agendas.get(0).setVagasDisponiveisAgenda(2L);
        agendas.get(0).setCotaUnidadeSemana(4L);

        List<AgendaGradeAtendimentoDTO> agendasFiltradas = FiltrarAgendaQuantidadeProcedimentoPorVaga.filtrar(agendas, null, params);

        assertNotNull("lista de agenda não pode ser null", agendasFiltradas);
        assertFalse("lista de agenda não pode ser vazia", agendasFiltradas.isEmpty());
        assertEquals("deve ter uma agenda", 1, agendasFiltradas.size());
    }

    @Test
    public void naoDeveRetornarListaAgendaPopuladaTipoAgendaDiaria() throws ValidacaoException {
        when(AgendamentoHelper.getTipoAgenda(BuildCenarioFiltrarAgendaVagasProcedimentoTest.agenda)).thenReturn(BuildCenarioFiltrarAgendaVagasProcedimentoTest.tipoAgendaDiario.value());

        AgendaGradeAtendimentoDTOParam params = BuildCenarioFiltrarAgendaVagasProcedimentoTest.buildParam(2L);
        List<AgendaGradeAtendimentoDTO> agendas = BuildCenarioFiltrarAgendaVagasProcedimentoTest.buildAgendaGradeAtendimentos();
        agendas.get(0).setVagasDisponiveisAgenda(4L);
        agendas.get(0).setCotaUnidadeSemana(0L);

        List<AgendaGradeAtendimentoDTO> agendasFiltradas = FiltrarAgendaQuantidadeProcedimentoPorVaga.filtrar(agendas, null, params);

        assertNotNull("lista de agenda não deve ser null", agendasFiltradas);
        assertTrue("lista de agenda deve ser vazia", agendasFiltradas.isEmpty());
    }

    @Test
    public void deveRetornarListaAgendaVaziaAoPassarListaAgendaEmpty() throws ValidacaoException {
        AgendaGradeAtendimentoDTOParam params = BuildCenarioFiltrarAgendaVagasProcedimentoTest.buildParam(2L);
        List<AgendaGradeAtendimentoDTO> agendas = new ArrayList<>();
        List<AgendaGradeAtendimentoPacienteDTO> horarios = new ArrayList<>();

        List<AgendaGradeAtendimentoDTO> agendasFiltradas = FiltrarAgendaQuantidadeProcedimentoPorVaga.filtrar(agendas, horarios, params);

        assertNotNull("lista de agenda não deve ser null", agendasFiltradas);
        assertTrue("lista de agenda deve ser vazia", agendasFiltradas.isEmpty());
    }


}