package br.com.ksisolucoes.bo.agendamento.exame.quebrarsolicitacao;

import br.com.ksisolucoes.agendamento.exame.dto.angedamentosolictacaolote.AgendaDTO;
import br.com.ksisolucoes.agendamento.exame.dto.angedamentosolictacaolote.GradeDTO;
import br.com.ksisolucoes.agendamento.exame.dto.angedamentosolictacaolote.GradeExameDTO;
import br.com.ksisolucoes.agendamento.exame.dto.angedamentosolictacaolote.GradeHorarioDTO;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeHorario;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorCompetencia;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import br.com.ksisolucoes.vo.prontuario.exame.SolicitacaoAgendamentoExame;
import org.apache.commons.lang.time.DateUtils;

import java.util.*;

public class FiltrarExamesAtendidosQuebraSolicitacaoBuildCenarios {

    public static SolicitacaoAgendamento buildSolicitacaoAgendamento(int id) {
        TipoProcedimento tipoProcedimento = new TipoProcedimento();
        tipoProcedimento.setCodigo(1L);
        SolicitacaoAgendamento solicitacaoAgendamento = new SolicitacaoAgendamento();
        solicitacaoAgendamento.setCodigo((long) id);
        solicitacaoAgendamento.setTipoProcedimento(tipoProcedimento);
        return solicitacaoAgendamento;
    }

    private static GradeExameDTO buildGradeExameDTO(Long id) {
        return new GradeExameDTO()
                .setCodigo(id)
                .setCodigoExameProcedimento(id)
                .setCodigoProcedimento(id)
                .setDescricaoExameProcedimento("Exame Procedimento " + id);
    }

    public static List<SolicitacaoAgendamentoExame> buildSolicitacaoAgendamentoExames(SolicitacaoAgendamento solicitacaoAgendamento, int... ids) {
        List<SolicitacaoAgendamentoExame> solicitacaoAgendamentoExames = new ArrayList<>();
        for (int id : ids) {
            solicitacaoAgendamentoExames.add(buildSolicitacaoAgendamentoExame(solicitacaoAgendamento, id));
        }
        return solicitacaoAgendamentoExames;
    }

    public static SolicitacaoAgendamentoExame buildSolicitacaoAgendamentoExame(SolicitacaoAgendamento solicitacaoAgendamento, int id) {
        ExameProcedimento exameProcedimento = new ExameProcedimento();
        exameProcedimento.setCodigo((long) id);
        SolicitacaoAgendamentoExame solicitacaoAgendamentoExame = new SolicitacaoAgendamentoExame();
        solicitacaoAgendamentoExame.setCodigo((long) id);
        solicitacaoAgendamentoExame.setExameProcedimento(exameProcedimento);
        solicitacaoAgendamentoExame.setSolicitacaoAgendamento(solicitacaoAgendamento);
        return solicitacaoAgendamentoExame;
    }

    public static List<AgendaDTO> buildAgendasDTO(int qtdeAgendas, boolean criarGrades) {
        List<AgendaDTO> agendas = new ArrayList<>();
        for (int i = 0; i < qtdeAgendas; i++) {
            AgendaDTO agendaDTO = buidAgenda(criarGrades, i);
            agendas.add(agendaDTO);
        }
        return agendas;
    }

    private static AgendaDTO buidAgenda(boolean criarGrades, int id) {
        AgendaDTO agendaDTO = new AgendaDTO();
        agendaDTO.setCodigo((long) id);
        agendaDTO.setExamePrestadorCompetencia(buildExamePrestadorCompetencia(id));
        if (criarGrades) {
            agendaDTO.setGrades(buildGrades(id));
        }
        return agendaDTO;
    }

    private static ExamePrestadorCompetencia buildExamePrestadorCompetencia(int id) {
        ExamePrestadorCompetencia examePrestadorCompetencia = new ExamePrestadorCompetencia();
        examePrestadorCompetencia.setCodigo((long) id);
        examePrestadorCompetencia.setEmpresa(buildEmpresa(id));
        return examePrestadorCompetencia;
    }

    private static Empresa buildEmpresa(int id) {
        Empresa empresa = new Empresa();
        empresa.setCodigo((long) id);
        empresa.setDescricao("Empresa " + id);
        return empresa;
    }

    private static int getRandomInt() {
        return (int) (Math.random() * (10));
    }

    private static List<GradeDTO> buildGrades(int q) {
        List<GradeDTO> grades = new ArrayList<>();
        int sizeGrades = getRandomInt() + 1;
        for (int i = 0; i < sizeGrades; i++) {
            GradeDTO gradeDTO = buildGradeDTO(true, true, q * i);
            grades.add(gradeDTO);
        }

        return grades;
    }

    private static GradeDTO buildGradeDTO(boolean buildGradeExames, boolean buildHorarios, int id) {
        GradeDTO gradeDTO = new GradeDTO();
        gradeDTO.setCodigo(((long) id));
        if (buildGradeExames) {
            gradeDTO.setExames(buildGradeExamesDto(5));
        }
        if (buildHorarios) {
            gradeDTO.setHorarios(buildGradeHorariosDto(id));
        }
        return gradeDTO;
    }

    public static List<GradeExameDTO> buildGradeExamesDto(int qtdeExamesProcedimento) {
        List<GradeExameDTO> exames = new ArrayList<>();
        for (int i = 0; i < qtdeExamesProcedimento; i++) {
            exames.add(buildGradeExameDTO((long) i));
        }

        return exames;
    }

    public static List<GradeHorarioDTO> buildGradeHorariosDto(int q) {
        List<GradeHorarioDTO> gradeHorarios = new ArrayList<>();
        int sizeHorarios = getRandomInt();
        for (int i = 0; i < sizeHorarios; i++) {
            GradeHorarioDTO gradeHorarioDTO = buildGradeHorarioDTO(q, i);
            gradeHorarios.add(gradeHorarioDTO);
        }
        return gradeHorarios;
    }

    private static GradeHorarioDTO buildGradeHorarioDTO(int q, int id) {
        return new GradeHorarioDTO()
                .setHorario(DateUtils.addDays(new Date(), id + 1))
                .setCodigo((long) id * q)
                .setStatus(AgendaGradeHorario.Status.PENDENTE);
    }

    public static List<AgendaDTO> buildAgendasDTOCenarioQuebraDuasSolicitacoes() {
        AgendaDTO agenda1 = buidAgenda(true, 2);
        AgendaDTO agenda2 = buidAgenda(false, 3);
        GradeDTO gradeDTO = buildGradeDTO(false, false, 2);
        gradeDTO.setExames(Collections.singletonList(buildGradeExameDTO(6L)));
        gradeDTO.setHorarios(Collections.singletonList(buildGradeHorarioDTO(2, 6)));
        agenda2.setGrades(Collections.singletonList(gradeDTO));

        return Arrays.asList(agenda1, agenda2);
    }

    public static List<AgendaDTO> buildAgendasDTOCenarioNenhumExameAtendido() {
        AgendaDTO agenda1 = buidAgenda(false, 4);
        GradeDTO gradeDTO1 = buildGradeDTO(false, false, 1);
        gradeDTO1.setExames(Collections.singletonList(buildGradeExameDTO(1L)));
        gradeDTO1.setHorarios(Collections.singletonList(buildGradeHorarioDTO(4, 1)));
        agenda1.setGrades(Collections.singletonList(gradeDTO1));

        AgendaDTO agenda2 = buidAgenda(false, 5);
        GradeDTO gradeDTO2 = buildGradeDTO(false, false, 2);
        gradeDTO2.setExames(Collections.singletonList(buildGradeExameDTO(3L)));
        gradeDTO2.setHorarios(Collections.singletonList(buildGradeHorarioDTO(5, 3)));
        agenda2.setGrades(Collections.singletonList(gradeDTO2));

        return Arrays.asList(agenda1, agenda2);
    }
}
