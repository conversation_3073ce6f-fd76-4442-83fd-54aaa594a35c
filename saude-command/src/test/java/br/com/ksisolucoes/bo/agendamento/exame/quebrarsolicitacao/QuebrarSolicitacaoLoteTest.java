package br.com.ksisolucoes.bo.agendamento.exame.quebrarsolicitacao;

import br.com.celk.agendamento.FiltrarAgendasParametroUnidadeOrigemESolicitante;
import br.com.ksisolucoes.agendamento.exame.dto.angedamentosolictacaolote.AgendaDTO;
import br.com.ksisolucoes.bo.agendamento.exame.CalcularTipoAgenda;
import br.com.ksisolucoes.bo.agendamento.exame.quebrarsolicitacao.cenarios.*;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.exame.SolicitacaoAgendamentoExame;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Collections;
import java.util.List;

import static br.com.ksisolucoes.bo.agendamento.exame.quebrarsolicitacao.FiltrarExamesAtendidosQuebraSolicitacao.examesAtendidos;
import static java.util.Arrays.asList;
import static java.util.Collections.singletonList;
import static org.junit.Assert.*;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({
        FiltrarExamesAtendidosQuebraSolicitacao.class,
        BOFactory.class,
        CalcularTipoAgenda.class,
        FiltrarAgendasParametroUnidadeOrigemESolicitante.class,
})
public class QuebrarSolicitacaoLoteTest {

    @Mock
    private AgendamentoFacade agendamentoBO;

    @Before
    public void setUp() throws DAOException {
        PowerMockito.mockStatic(FiltrarExamesAtendidosQuebraSolicitacao.class);
        PowerMockito.mockStatic(BOFactory.class);
        PowerMockito.mockStatic(CalcularTipoAgenda.class);
        PowerMockito.mockStatic(FiltrarAgendasParametroUnidadeOrigemESolicitante.class);
        when(FiltrarAgendasParametroUnidadeOrigemESolicitante.utilizaRegraUnidadeOrigemESolicitante()).thenReturn(false);
    }

    /* # CENARIO-1 #
        solicitação com 3 exames
        tipo procedimento configurado para 2 procedimentos por vaga
        Prestador realiza todos os exames
        Prestador tem cota para todos os exames

        agenda disponível com 1 grade dia 14
        com 3 vagas sem exame definido

        esperado:
            NÃO DEVE QUEBRAR a solicitação e DEVE ser agendada utilizando 2 horários
            *** este cenário não irá entrar no método de quebra.
     */

    /* # CENARIO-2 #
        solicitação com 3 exames
        tipo procedimento configurado para 2 procedimentos por vaga
        Prestador realiza todos os exames
        Prestador tem cota para todos os exames

        agenda disponível com 2 grades uma dia 14 e uma dia 15
        dia 14 tem duas vagas com 1 exame definido
        dia 15 tem duas vagas com 1 exame definido

        esperado:
            solicitacao 1 nova
            solicitacao 2 nova
            solicitacao 3 original
     */
    @Test
    public void testQuebrarSolicitacao_cenario2() throws ValidacaoException, DAOException {
        List<AgendaDTO> agendas = singletonList(BuildCenario2.agendaDTO);

        when(examesAtendidos(BuildCenario2.grade1, BuildCenario2.examesPrestadorRealiza, asList(BuildCenario2.exameSolicitacao_1, BuildCenario2.exameSolicitacao_2, BuildCenario2.exameSolicitacao_3), BuildCenario2.examePrestadorCompetencia, BuildCenario2.tipoTetoFinanceiro, BuildCenario2.tipoProcedimento)).thenReturn(singletonList(BuildCenario2.exameSolicitacao_1));
        when(examesAtendidos(BuildCenario2.grade2, BuildCenario2.examesPrestadorRealiza, asList(BuildCenario2.exameSolicitacao_2, BuildCenario2.exameSolicitacao_3), BuildCenario2.examePrestadorCompetencia, BuildCenario2.tipoTetoFinanceiro, BuildCenario2.tipoProcedimento)).thenReturn(singletonList(BuildCenario2.exameSolicitacao_2));

        when(BOFactory.getBO(AgendamentoFacade.class)).thenReturn(agendamentoBO);
        when(BOFactory.getBO(AgendamentoFacade.class).gerarSolicitacaoAgendamento(BuildCenario2.solicitacaoAgendamento, singletonList(BuildCenario2.exameSolicitacao_1))).thenReturn(BuildCenario2.novaSolicitacao_1);
        when(BOFactory.getBO(AgendamentoFacade.class).gerarSolicitacaoAgendamento(BuildCenario2.solicitacaoAgendamento, singletonList(BuildCenario2.exameSolicitacao_2))).thenReturn(BuildCenario2.novaSolicitacao_2);
        when(CalcularTipoAgenda.isTipoAgendaDiaria(any(Agenda.class))).thenReturn(false);

        List<SolicitacaoAgendamento> agendamentos = QuebrarSolicitacaoLote.quebrarSolicitacao(BuildCenario2.solicitacaoAgendamento, agendas, BuildCenario2.examesDaSolicitacao, BuildCenario2.tipoTetoFinanceiro, BuildCenario2.tipoProcedimento, BuildCenario2.examesPrestadorProcedimentos);

        assertNotNull(agendamentos);
        assertEquals(3, agendamentos.size());
        assertSame(2L, agendamentos.get(0).getCodigo());
        assertSame(3L, agendamentos.get(1).getCodigo());
        assertSame(1L, agendamentos.get(2).getCodigo());
    }

    /* # CENARIO-3 #
        solicitação com 3 exames
        tipo procedimento configurado para 2 procedimentos por vaga
        Prestador realiza todos os exames
        Prestador tem cota para todos os exames

        agenda disponível com 1 vaga

        esperado:
            solicitacao 1 nova
            solicitacao 2 original
     */
    @Test
    public void testQuebrarSolicitacao_cenario3() throws ValidacaoException, DAOException {
        List<AgendaDTO> agendas = singletonList(BuildCenario3.agendaDTO);

        when(examesAtendidos(BuildCenario3.grade1, BuildCenario3.examesPrestadorRealiza, asList(BuildCenario3.exameSolicitacao_1, BuildCenario3.exameSolicitacao_2, BuildCenario3.exameSolicitacao_3), BuildCenario3.examePrestadorCompetencia, BuildCenario3.tipoTetoFinanceiro, BuildCenario3.tipoProcedimento)).thenReturn(asList(BuildCenario3.exameSolicitacao_1, BuildCenario3.exameSolicitacao_2));

        when(BOFactory.getBO(AgendamentoFacade.class)).thenReturn(agendamentoBO);
        when(BOFactory.getBO(AgendamentoFacade.class).gerarSolicitacaoAgendamento(BuildCenario3.solicitacaoAgendamento, asList(BuildCenario3.exameSolicitacao_1, BuildCenario3.exameSolicitacao_2))).thenReturn(BuildCenario3.novaSolicitacao_1);
        when(CalcularTipoAgenda.isTipoAgendaDiaria(any(Agenda.class))).thenReturn(false);

        List<SolicitacaoAgendamento> agendamentos = QuebrarSolicitacaoLote.quebrarSolicitacao(BuildCenario3.solicitacaoAgendamento, agendas, BuildCenario3.examesDaSolicitacao, BuildCenario3.tipoTetoFinanceiro, BuildCenario3.tipoProcedimento, BuildCenario3.examesPrestadorProcedimentos);

        assertNotNull(agendamentos);
        assertEquals(2, agendamentos.size());
        assertSame(2L, agendamentos.get(0).getCodigo());
        assertSame(1L, agendamentos.get(1).getCodigo());
    }

    /* # CENARIO-4 #
        solicitação com 3 exames
        tipo procedimento configurado para 2 procedimentos por vaga
        Prestador realiza todos os exames
        Prestador tem cota para apenas 1 exame

        agenda disponível com 1 vaga

        esperado:
            solicitacao 1 nova
            solicitacao 2 original
     */
    @Test
    public void testQuebrarSolicitacao_cenario4() throws ValidacaoException, DAOException {
        List<AgendaDTO> agendas = singletonList(BuildCenario4.agendaDTO);

        when(examesAtendidos(BuildCenario4.grade1, BuildCenario4.examesPrestadorRealiza, asList(BuildCenario4.exameSolicitacao_1, BuildCenario4.exameSolicitacao_2, BuildCenario4.exameSolicitacao_3), BuildCenario4.examePrestadorCompetencia, BuildCenario4.tipoTetoFinanceiro, BuildCenario4.tipoProcedimento)).thenReturn(singletonList(BuildCenario4.exameSolicitacao_1));

        when(BOFactory.getBO(AgendamentoFacade.class)).thenReturn(agendamentoBO);
        when(BOFactory.getBO(AgendamentoFacade.class).gerarSolicitacaoAgendamento(BuildCenario4.solicitacaoAgendamento, singletonList(BuildCenario4.exameSolicitacao_1))).thenReturn(BuildCenario4.novaSolicitacao_1);
        when(CalcularTipoAgenda.isTipoAgendaDiaria(any(Agenda.class))).thenReturn(false);

        List<SolicitacaoAgendamento> agendamentos = QuebrarSolicitacaoLote.quebrarSolicitacao(BuildCenario4.solicitacaoAgendamento, agendas, BuildCenario4.examesDaSolicitacao, BuildCenario4.tipoTetoFinanceiro, BuildCenario4.tipoProcedimento, BuildCenario4.examesPrestadorProcedimentos);

        assertNotNull(agendamentos);
        assertEquals(2, agendamentos.size());
        assertSame(2L, agendamentos.get(0).getCodigo());
        assertSame(1L, agendamentos.get(1).getCodigo());
    }

    /*  # CENARIO-5 #
        solicitação com 6 exames
        tipo procedimento configurado para 2 procedimentos por vaga
        Prestador realiza todos os exames
        Prestador tem cota para todos os exames

        agenda com 2 grades
        dia 14 com 1 vaga e com dois exames definidos
        dia 15 com 1 vaga e sem exame definido

        esperado:
            solicitacao 1 nova
            solicitacao 2 nova
            solicitacao 3 original
     */
    @Test
    public void testQuebrarSolicitacao_cenario5() throws ValidacaoException, DAOException {
        List<AgendaDTO> agendas = singletonList(BuildCenario5.agendaDTO);

        when(examesAtendidos(BuildCenario5.grade1, BuildCenario5.examesPrestadorRealiza, asList(BuildCenario5.exameSolicitacao_1, BuildCenario5.exameSolicitacao_2, BuildCenario5.exameSolicitacao_3, BuildCenario5.exameSolicitacao_4, BuildCenario5.exameSolicitacao_5, BuildCenario5.exameSolicitacao_6), BuildCenario5.examePrestadorCompetencia, BuildCenario5.tipoTetoFinanceiro, BuildCenario5.tipoProcedimento)).thenReturn(asList(BuildCenario5.exameSolicitacao_1, BuildCenario5.exameSolicitacao_2));
        when(examesAtendidos(BuildCenario5.grade2, BuildCenario5.examesPrestadorRealiza, asList(BuildCenario5.exameSolicitacao_3, BuildCenario5.exameSolicitacao_4, BuildCenario5.exameSolicitacao_5, BuildCenario5.exameSolicitacao_6), BuildCenario5.examePrestadorCompetencia, BuildCenario5.tipoTetoFinanceiro, BuildCenario5.tipoProcedimento)).thenReturn(asList(BuildCenario5.exameSolicitacao_3, BuildCenario5.exameSolicitacao_4));

        when(BOFactory.getBO(AgendamentoFacade.class)).thenReturn(agendamentoBO);
        when(BOFactory.getBO(AgendamentoFacade.class).gerarSolicitacaoAgendamento(BuildCenario5.solicitacaoAgendamento, asList(BuildCenario5.exameSolicitacao_1, BuildCenario5.exameSolicitacao_2))).thenReturn(BuildCenario5.novaSolicitacao_1);
        when(BOFactory.getBO(AgendamentoFacade.class).gerarSolicitacaoAgendamento(BuildCenario5.solicitacaoAgendamento, asList(BuildCenario5.exameSolicitacao_3, BuildCenario5.exameSolicitacao_4))).thenReturn(BuildCenario5.novaSolicitacao_2);
        when(CalcularTipoAgenda.isTipoAgendaDiaria(any(Agenda.class))).thenReturn(false);

        List<SolicitacaoAgendamento> agendamentos = QuebrarSolicitacaoLote.quebrarSolicitacao(BuildCenario5.solicitacaoAgendamento, agendas, BuildCenario5.examesDaSolicitacao, BuildCenario5.tipoTetoFinanceiro, BuildCenario5.tipoProcedimento, BuildCenario5.examesPrestadorProcedimentos);

        assertNotNull(agendamentos);
        assertEquals(3, agendamentos.size());
        assertSame(2L, agendamentos.get(0).getCodigo());
        assertSame(3L, agendamentos.get(1).getCodigo());
        assertSame(1L, agendamentos.get(2).getCodigo());
    }

    /* # CENARIO-6 #
        solicitação com 6 exames
        tipo procedimento configurado para 3 procedimentos por vaga
        Prestador realiza todos os exames
        Prestador tem cota para todos os exames

        agenda com 2 grades
        dia 14 com 1 vaga e com dois exames definidos
        dia 15 com 1 vaga e sem exame definido

        esperado:
            solicitacao 1 nova
            solicitacao 2 nova
            solicitacao 3 original
     */
    @Test
    public void testQuebrarSolicitacao_cenario6() throws ValidacaoException, DAOException {
        List<AgendaDTO> agendas = singletonList(BuildCenario6.agendaDTO);

        when(examesAtendidos(BuildCenario6.grade1, BuildCenario6.examesPrestadorRealiza, asList(BuildCenario6.exameSolicitacao_1, BuildCenario6.exameSolicitacao_2, BuildCenario6.exameSolicitacao_3, BuildCenario6.exameSolicitacao_4, BuildCenario6.exameSolicitacao_5, BuildCenario6.exameSolicitacao_6), BuildCenario6.examePrestadorCompetencia, BuildCenario6.tipoTetoFinanceiro, BuildCenario6.tipoProcedimento)).thenReturn(asList(BuildCenario6.exameSolicitacao_1, BuildCenario6.exameSolicitacao_2));
        when(examesAtendidos(BuildCenario6.grade2, BuildCenario6.examesPrestadorRealiza, asList(BuildCenario6.exameSolicitacao_3, BuildCenario6.exameSolicitacao_4, BuildCenario6.exameSolicitacao_5, BuildCenario6.exameSolicitacao_6), BuildCenario6.examePrestadorCompetencia, BuildCenario6.tipoTetoFinanceiro, BuildCenario6.tipoProcedimento)).thenReturn(asList(BuildCenario6.exameSolicitacao_3, BuildCenario6.exameSolicitacao_4, BuildCenario6.exameSolicitacao_5, BuildCenario6.exameSolicitacao_6));

        when(BOFactory.getBO(AgendamentoFacade.class)).thenReturn(agendamentoBO);
        when(BOFactory.getBO(AgendamentoFacade.class).gerarSolicitacaoAgendamento(BuildCenario6.solicitacaoAgendamento, asList(BuildCenario6.exameSolicitacao_1, BuildCenario6.exameSolicitacao_2))).thenReturn(BuildCenario6.novaSolicitacao_1);
        when(BOFactory.getBO(AgendamentoFacade.class).gerarSolicitacaoAgendamento(BuildCenario6.solicitacaoAgendamento, asList(BuildCenario6.exameSolicitacao_3, BuildCenario6.exameSolicitacao_4, BuildCenario6.exameSolicitacao_5))).thenReturn(BuildCenario6.novaSolicitacao_2);
        when(CalcularTipoAgenda.isTipoAgendaDiaria(any(Agenda.class))).thenReturn(false);

        List<SolicitacaoAgendamento> agendamentos = QuebrarSolicitacaoLote.quebrarSolicitacao(BuildCenario6.solicitacaoAgendamento, agendas, BuildCenario6.examesDaSolicitacao, BuildCenario6.tipoTetoFinanceiro, BuildCenario6.tipoProcedimento, BuildCenario6.examesPrestadorProcedimentos);

        assertNotNull(agendamentos);
        assertEquals(3, agendamentos.size());
        assertSame(2L, agendamentos.get(0).getCodigo());
        assertSame(3L, agendamentos.get(1).getCodigo());
        assertSame(1L, agendamentos.get(2).getCodigo());
    }

    /* # CENARIO-7 #
        Solicitação com 7 exames
        Agenda sem exame definido
        Prestador atende todos os exames
        Tipo do procedimento configurado para 2 exames por vaga

        Agenda disponível:
        14 - 3 horários em sequência
        15 - 3 horários em sequência

        esperado:
            solicitacao 1 nova
            solicitacao 2 original
     */
    @Test
    public void testQuebrarSolicitacao_cenario7() throws ValidacaoException, DAOException {
        List<AgendaDTO> agendas = singletonList(BuildCenario7.agendaDTO);

        when(examesAtendidos(BuildCenario7.grade1, BuildCenario7.examesPrestadorRealiza, asList(BuildCenario7.exameSolicitacao_1, BuildCenario7.exameSolicitacao_2, BuildCenario7.exameSolicitacao_3, BuildCenario7.exameSolicitacao_4, BuildCenario7.exameSolicitacao_5, BuildCenario7.exameSolicitacao_6, BuildCenario7.exameSolicitacao_7), BuildCenario7.examePrestadorCompetencia, BuildCenario7.tipoTetoFinanceiro, BuildCenario7.tipoProcedimento)).thenReturn(BuildCenario7.examesDaSolicitacao);
        when(examesAtendidos(BuildCenario7.grade2, BuildCenario7.examesPrestadorRealiza, singletonList(BuildCenario7.exameSolicitacao_7), BuildCenario7.examePrestadorCompetencia, BuildCenario7.tipoTetoFinanceiro, BuildCenario7.tipoProcedimento)).thenReturn(singletonList(BuildCenario7.exameSolicitacao_7));

        when(BOFactory.getBO(AgendamentoFacade.class)).thenReturn(agendamentoBO);
        when(BOFactory.getBO(AgendamentoFacade.class).gerarSolicitacaoAgendamento(BuildCenario7.solicitacaoAgendamento, asList(BuildCenario7.exameSolicitacao_1, BuildCenario7.exameSolicitacao_2, BuildCenario7.exameSolicitacao_3, BuildCenario7.exameSolicitacao_4, BuildCenario7.exameSolicitacao_5, BuildCenario7.exameSolicitacao_6))).thenReturn(BuildCenario7.novaSolicitacao_1);
        when(CalcularTipoAgenda.isTipoAgendaDiaria(any(Agenda.class))).thenReturn(false);

        List<SolicitacaoAgendamento> agendamentos = QuebrarSolicitacaoLote.quebrarSolicitacao(BuildCenario7.solicitacaoAgendamento, agendas, BuildCenario7.examesDaSolicitacao, BuildCenario7.tipoTetoFinanceiro, BuildCenario7.tipoProcedimento, BuildCenario7.examesPrestadorProcedimentos);

        assertNotNull(agendamentos);
        assertEquals(2, agendamentos.size());
        assertSame(2L, agendamentos.get(0).getCodigo());
        assertSame(1L, agendamentos.get(1).getCodigo());
    }

    /* # CENARIO-8 #
        Solicitação com 7 exames
        Agenda sem exame definido
        Prestador atende todos os exames
        Tipo do procedimento configurado para 2 exames por vaga

        Agenda disponível:
        14 - 2 horários mas não em sequência
        15 - 3 horários em sequência

        esperado:
            solicitacao 1 nova
            solicitacao 2 original
     */
    @Test
    public void testQuebrarSolicitacao_cenario8() throws ValidacaoException, DAOException {
        List<AgendaDTO> agendas = singletonList(BuildCenario8.agendaDTO);

        when(examesAtendidos(BuildCenario8.grade1, BuildCenario8.examesPrestadorRealiza, asList(BuildCenario8.exameSolicitacao_1, BuildCenario8.exameSolicitacao_2, BuildCenario8.exameSolicitacao_3, BuildCenario8.exameSolicitacao_4, BuildCenario8.exameSolicitacao_5, BuildCenario8.exameSolicitacao_6, BuildCenario8.exameSolicitacao_7), BuildCenario8.examePrestadorCompetencia, BuildCenario8.tipoTetoFinanceiro, BuildCenario8.tipoProcedimento)).thenReturn(BuildCenario8.examesDaSolicitacao);
        when(examesAtendidos(BuildCenario8.grade2, BuildCenario8.examesPrestadorRealiza, singletonList(BuildCenario8.exameSolicitacao_7), BuildCenario8.examePrestadorCompetencia, BuildCenario8.tipoTetoFinanceiro, BuildCenario8.tipoProcedimento)).thenReturn(singletonList(BuildCenario8.exameSolicitacao_7));

        when(BOFactory.getBO(AgendamentoFacade.class)).thenReturn(agendamentoBO);
        when(BOFactory.getBO(AgendamentoFacade.class).gerarSolicitacaoAgendamento(BuildCenario8.solicitacaoAgendamento, asList(BuildCenario8.exameSolicitacao_1, BuildCenario8.exameSolicitacao_2))).thenReturn(BuildCenario8.novaSolicitacao_1);
        when(CalcularTipoAgenda.isTipoAgendaDiaria(any(Agenda.class))).thenReturn(false);

        List<SolicitacaoAgendamento> agendamentos = QuebrarSolicitacaoLote.quebrarSolicitacao(BuildCenario8.solicitacaoAgendamento, agendas, BuildCenario8.examesDaSolicitacao, BuildCenario8.tipoTetoFinanceiro, BuildCenario8.tipoProcedimento, BuildCenario8.examesPrestadorProcedimentos);

        assertNotNull(agendamentos);
        assertEquals(2, agendamentos.size());
        assertSame(2L, agendamentos.get(0).getCodigo());
        assertSame(1L, agendamentos.get(1).getCodigo());
    }

    /* # CENARIO-9 #
        Solicitação com 7 exames
        Prestador atende todos os exames
        Tipo do procedimento configurado para "0" exames por vaga

        Agenda disponível:
        14 - 2 horários mas não em sequência e com dois exames definidos
        15 - 3 horários em sequência e com outros 3 exames definidos

        esperado:
            solicitacao 1 nova
            solicitacao 2 nova
            solicitacao 3 original
     */
    @Test
    public void testQuebrarSolicitacao_cenario9() throws ValidacaoException, DAOException {
        List<AgendaDTO> agendas = singletonList(BuildCenario9.agendaDTO);

        when(examesAtendidos(BuildCenario9.grade1, BuildCenario9.examesPrestadorRealiza, asList(BuildCenario9.exameSolicitacao_1, BuildCenario9.exameSolicitacao_2, BuildCenario9.exameSolicitacao_3, BuildCenario9.exameSolicitacao_4, BuildCenario9.exameSolicitacao_5, BuildCenario9.exameSolicitacao_6, BuildCenario9.exameSolicitacao_7), BuildCenario9.examePrestadorCompetencia, BuildCenario9.tipoTetoFinanceiro, BuildCenario9.tipoProcedimento)).thenReturn(asList(BuildCenario9.exameSolicitacao_1, BuildCenario9.exameSolicitacao_3));
        when(examesAtendidos(BuildCenario9.grade2, BuildCenario9.examesPrestadorRealiza, asList(BuildCenario9.exameSolicitacao_2, BuildCenario9.exameSolicitacao_4, BuildCenario9.exameSolicitacao_5, BuildCenario9.exameSolicitacao_6, BuildCenario9.exameSolicitacao_7), BuildCenario9.examePrestadorCompetencia, BuildCenario9.tipoTetoFinanceiro, BuildCenario9.tipoProcedimento)).thenReturn(asList(BuildCenario9.exameSolicitacao_4, BuildCenario9.exameSolicitacao_5, BuildCenario9.exameSolicitacao_6));

        when(BOFactory.getBO(AgendamentoFacade.class)).thenReturn(agendamentoBO);
        when(BOFactory.getBO(AgendamentoFacade.class).gerarSolicitacaoAgendamento(BuildCenario9.solicitacaoAgendamento, asList(BuildCenario9.exameSolicitacao_1, BuildCenario9.exameSolicitacao_3))).thenReturn(BuildCenario9.novaSolicitacao_1);
        when(BOFactory.getBO(AgendamentoFacade.class).gerarSolicitacaoAgendamento(BuildCenario9.solicitacaoAgendamento, asList(BuildCenario9.exameSolicitacao_4, BuildCenario9.exameSolicitacao_5, BuildCenario9.exameSolicitacao_6))).thenReturn(BuildCenario9.novaSolicitacao_2);
        when(CalcularTipoAgenda.isTipoAgendaDiaria(any(Agenda.class))).thenReturn(false);

        List<SolicitacaoAgendamento> agendamentos = QuebrarSolicitacaoLote.quebrarSolicitacao(BuildCenario9.solicitacaoAgendamento, agendas, BuildCenario9.examesDaSolicitacao, BuildCenario9.tipoTetoFinanceiro, BuildCenario9.tipoProcedimento, BuildCenario9.examesPrestadorProcedimentos);

        assertNotNull(agendamentos);
        assertEquals(3, agendamentos.size());
        assertSame(2L, agendamentos.get(0).getCodigo());
        assertSame(3L, agendamentos.get(1).getCodigo());
        assertSame(1L, agendamentos.get(2).getCodigo());
    }

    /* # CENARIO-10 #
        solicitação com 3 exames
        tipo procedimento configurado para 2 procedimentos por vaga
        Prestador realiza todos os exames
        Prestador tem cota para todos os exames
        tipo de agenda configurado para "DIARIO"

        agenda disponível com 2 grades uma dia 14 e uma dia 15
        dia 14 tem duas vagas com 1 exame definido
        dia 15 tem duas vagas com 1 exame definido

        esperado:
            Solicitação 1 nova
            Solicitação 2 nova
            Solicitação 3 original
     */
    @Test
    public void testQuebrarSolicitacao_cenario10() throws ValidacaoException, DAOException {
        List<AgendaDTO> agendas = singletonList(BuildCenario10.agendaDTO);

        when(examesAtendidos(BuildCenario10.grade1, BuildCenario10.examesPrestadorRealiza, asList(BuildCenario10.exameSolicitacao_1, BuildCenario10.exameSolicitacao_2, BuildCenario10.exameSolicitacao_3), BuildCenario10.examePrestadorCompetencia, BuildCenario10.tipoTetoFinanceiro, BuildCenario10.tipoProcedimento)).thenReturn(singletonList(BuildCenario10.exameSolicitacao_1));
        when(examesAtendidos(BuildCenario10.grade2, BuildCenario10.examesPrestadorRealiza, asList(BuildCenario10.exameSolicitacao_2, BuildCenario10.exameSolicitacao_3), BuildCenario10.examePrestadorCompetencia, BuildCenario10.tipoTetoFinanceiro, BuildCenario10.tipoProcedimento)).thenReturn(singletonList(BuildCenario10.exameSolicitacao_2));

        when(BOFactory.getBO(AgendamentoFacade.class)).thenReturn(agendamentoBO);
        when(BOFactory.getBO(AgendamentoFacade.class).gerarSolicitacaoAgendamento(BuildCenario10.solicitacaoAgendamento, singletonList(BuildCenario10.exameSolicitacao_1))).thenReturn(BuildCenario10.novaSolicitacao_1);
        when(BOFactory.getBO(AgendamentoFacade.class).gerarSolicitacaoAgendamento(BuildCenario10.solicitacaoAgendamento, singletonList(BuildCenario10.exameSolicitacao_2))).thenReturn(BuildCenario10.novaSolicitacao_2);
        when(CalcularTipoAgenda.isTipoAgendaDiaria(any(Agenda.class))).thenReturn(true);

        List<SolicitacaoAgendamento> agendamentos = QuebrarSolicitacaoLote.quebrarSolicitacao(BuildCenario10.solicitacaoAgendamento, agendas, BuildCenario10.examesDaSolicitacao, BuildCenario10.tipoTetoFinanceiro, BuildCenario10.tipoProcedimento, BuildCenario10.examesPrestadorProcedimentos);

        assertNotNull(agendamentos);
        assertEquals(3, agendamentos.size());
        assertSame(2L, agendamentos.get(0).getCodigo());
        assertSame(3L, agendamentos.get(1).getCodigo());
        assertSame(1L, agendamentos.get(2).getCodigo());
    }

    /* # CENARIO-11 #
        solicitação com 3 exames
        tipo procedimento configurado para 2 procedimentos por vaga
        Prestador realiza todos os exames
        Prestador tem cota para todos os exames

        agenda disponível com 2 grades, ambas no mesmo dia mas com horários diferentes
        grade1 tem 5 vagas com 1 exame definido
        grade2 tem 5 vagas com 1 exame definido

        esperado:
            Solicitação 1 nova
            Solicitação 2 original
     */
    @Test
    public void testQuebrarSolicitacao_cenario11() throws ValidacaoException, DAOException {
        List<AgendaDTO> agendas = singletonList(BuildCenario11.agendaDTO);

        when(examesAtendidos(BuildCenario11.grade1, BuildCenario11.examesPrestadorRealiza, asList(BuildCenario11.exameSolicitacao_1, BuildCenario11.exameSolicitacao_2, BuildCenario11.exameSolicitacao_3), BuildCenario11.examePrestadorCompetencia, BuildCenario11.tipoTetoFinanceiro, BuildCenario11.tipoProcedimento)).thenReturn(singletonList(BuildCenario11.exameSolicitacao_1));
        when(examesAtendidos(BuildCenario11.grade2, BuildCenario11.examesPrestadorRealiza, asList(BuildCenario11.exameSolicitacao_2, BuildCenario11.exameSolicitacao_3), BuildCenario11.examePrestadorCompetencia, BuildCenario11.tipoTetoFinanceiro, BuildCenario11.tipoProcedimento)).thenReturn(singletonList(BuildCenario11.exameSolicitacao_2));

        when(BOFactory.getBO(AgendamentoFacade.class)).thenReturn(agendamentoBO);
        when(BOFactory.getBO(AgendamentoFacade.class).gerarSolicitacaoAgendamento(BuildCenario11.solicitacaoAgendamento, singletonList(BuildCenario11.exameSolicitacao_1))).thenReturn(BuildCenario11.novaSolicitacao_1);
        when(CalcularTipoAgenda.isTipoAgendaDiaria(any(Agenda.class))).thenReturn(false);

        List<SolicitacaoAgendamento> agendamentos = QuebrarSolicitacaoLote.quebrarSolicitacao(BuildCenario11.solicitacaoAgendamento, agendas, BuildCenario11.examesDaSolicitacao, BuildCenario11.tipoTetoFinanceiro, BuildCenario11.tipoProcedimento, BuildCenario11.examesPrestadorProcedimentos);

        assertNotNull(agendamentos);
        assertEquals(2, agendamentos.size());
        assertSame(2L, agendamentos.get(0).getCodigo());
        assertSame(1L, agendamentos.get(1).getCodigo());
    }

    /* # CENARIO-12 #
        solicitação com 3 exames
        tipo procedimento configurado para 2 procedimentos por vaga
        Prestador realiza todos os exames
        Prestador tem cota para todos os exames

        agenda disponível com 4 grades, ambas no mesmo dia mas com horários diferentes
        grade1 - Dia 1 - 5 vagas - exame 1
        grade2 - Dia 1 - 5 vagas - exame 3
        grade3 - Dia 2 - 5 vagas - exame 1
        grade4 - Dia 2 - 5 vagas - exame 2

        esperado:
            Solicitação 1 nova
            Solicitação 2 nova
            Solicitação 3 original
     */
    @Test
    public void testQuebrarSolicitacao_cenario12() throws ValidacaoException, DAOException {
        List<AgendaDTO> agendas = singletonList(BuildCenario12.agendaDTO);

        when(examesAtendidos(BuildCenario12.grade1, BuildCenario12.examesPrestadorRealiza, asList(BuildCenario12.exameSolicitacao_1, BuildCenario12.exameSolicitacao_2, BuildCenario12.exameSolicitacao_3), BuildCenario12.examePrestadorCompetencia, BuildCenario12.tipoTetoFinanceiro, BuildCenario12.tipoProcedimento)).thenReturn(singletonList(BuildCenario12.exameSolicitacao_1));
        when(examesAtendidos(BuildCenario12.grade2, BuildCenario12.examesPrestadorRealiza, asList(BuildCenario12.exameSolicitacao_2, BuildCenario12.exameSolicitacao_3), BuildCenario12.examePrestadorCompetencia, BuildCenario12.tipoTetoFinanceiro, BuildCenario12.tipoProcedimento)).thenReturn(singletonList(BuildCenario12.exameSolicitacao_3));
        when(examesAtendidos(BuildCenario12.grade3, BuildCenario12.examesPrestadorRealiza, asList(BuildCenario12.exameSolicitacao_2, BuildCenario12.exameSolicitacao_3), BuildCenario12.examePrestadorCompetencia, BuildCenario12.tipoTetoFinanceiro, BuildCenario12.tipoProcedimento)).thenReturn(Collections.<SolicitacaoAgendamentoExame>emptyList());
        when(examesAtendidos(BuildCenario12.grade4, BuildCenario12.examesPrestadorRealiza, asList(BuildCenario12.exameSolicitacao_2, BuildCenario12.exameSolicitacao_3), BuildCenario12.examePrestadorCompetencia, BuildCenario12.tipoTetoFinanceiro, BuildCenario12.tipoProcedimento)).thenReturn(singletonList(BuildCenario12.exameSolicitacao_2));

        when(BOFactory.getBO(AgendamentoFacade.class)).thenReturn(agendamentoBO);
        when(BOFactory.getBO(AgendamentoFacade.class).gerarSolicitacaoAgendamento(BuildCenario12.solicitacaoAgendamento, singletonList(BuildCenario12.exameSolicitacao_1))).thenReturn(BuildCenario12.novaSolicitacao_1);
        when(BOFactory.getBO(AgendamentoFacade.class).gerarSolicitacaoAgendamento(BuildCenario12.solicitacaoAgendamento, singletonList(BuildCenario12.exameSolicitacao_2))).thenReturn(BuildCenario12.novaSolicitacao_2);
        when(CalcularTipoAgenda.isTipoAgendaDiaria(any(Agenda.class))).thenReturn(false);

        List<SolicitacaoAgendamento> agendamentos = QuebrarSolicitacaoLote.quebrarSolicitacao(BuildCenario12.solicitacaoAgendamento, agendas, BuildCenario12.examesDaSolicitacao, BuildCenario12.tipoTetoFinanceiro, BuildCenario12.tipoProcedimento, BuildCenario12.examesPrestadorProcedimentos);

        assertNotNull(agendamentos);
        assertEquals(3, agendamentos.size());
        assertSame(2L, agendamentos.get(0).getCodigo());
        assertSame(3L, agendamentos.get(1).getCodigo());
        assertSame(1L, agendamentos.get(2).getCodigo());
    }

    /* # CENARIO-13 #
        solicitação com 5 exames (2 exames com procedimento "1" e 3 com procedimento "2")
        tipo procedimento configurado para 2 procedimentos por vaga
        Prestador realiza todos os exames
        Prestador tem cota para todos os exames

        agenda disponível com 1 grade
        dia 1 com uma vaga apenas e nenhum exame definido
        dia 2 com uma vaga apenas e nenhum exame definido

        esperado:
            Solicitação 1 nova
            Solicitação 2 original
     */
    @Test
    public void testQuebrarSolicitacao_cenario13() throws ValidacaoException, DAOException {
        List<AgendaDTO> agendas = singletonList(BuildCenario13.agendaDTO);

        when(examesAtendidos(BuildCenario13.grade1, BuildCenario13.examesPrestadorRealiza, asList(BuildCenario13.exameSolicitacao_1, BuildCenario13.exameSolicitacao_2, BuildCenario13.exameSolicitacao_3, BuildCenario13.exameSolicitacao_4, BuildCenario13.exameSolicitacao_5), BuildCenario13.examePrestadorCompetencia, BuildCenario13.tipoTetoFinanceiro, BuildCenario13.tipoProcedimento)).thenReturn(asList(BuildCenario13.exameSolicitacao_1, BuildCenario13.exameSolicitacao_2));
        when(examesAtendidos(BuildCenario13.grade2, BuildCenario13.examesPrestadorRealiza, asList(BuildCenario13.exameSolicitacao_3, BuildCenario13.exameSolicitacao_4, BuildCenario13.exameSolicitacao_5), BuildCenario13.examePrestadorCompetencia, BuildCenario13.tipoTetoFinanceiro, BuildCenario13.tipoProcedimento)).thenReturn(asList(BuildCenario13.exameSolicitacao_3, BuildCenario13.exameSolicitacao_4, BuildCenario13.exameSolicitacao_5));

        when(BOFactory.getBO(AgendamentoFacade.class)).thenReturn(agendamentoBO);
        when(BOFactory.getBO(AgendamentoFacade.class).gerarSolicitacaoAgendamento(BuildCenario13.solicitacaoAgendamento, asList(BuildCenario13.exameSolicitacao_1, BuildCenario13.exameSolicitacao_2))).thenReturn(BuildCenario13.novaSolicitacao_1);
        when(CalcularTipoAgenda.isTipoAgendaDiaria(any(Agenda.class))).thenReturn(false);

        List<SolicitacaoAgendamento> agendamentos = QuebrarSolicitacaoLote.quebrarSolicitacao(BuildCenario13.solicitacaoAgendamento, agendas, BuildCenario13.examesDaSolicitacao, BuildCenario13.tipoTetoFinanceiro, BuildCenario13.tipoProcedimento, BuildCenario13.examesPrestadorProcedimentos);

        assertNotNull(agendamentos);
        assertEquals(2, agendamentos.size());
        assertSame(2L, agendamentos.get(0).getCodigo());
        assertSame(1L, agendamentos.get(1).getCodigo());
    }

    /* # CENARIO-14 #
        solicitação com 5 exames (2 exames com procedimento "1" e 3 com procedimento "2")
        tipo procedimento configurado para 2 procedimentos por vaga
        Prestador realiza todos os exames
        Prestador tem cota para todos os exames

        agenda disponível com 1 grade
        dia 1 com uma vaga apenas e nenhum exame definido
        dia 2 com uma vaga apenas e nenhum exame definido

        esperado:
            Solicitação 1 nova
            Solicitação 2 original
     */
    @Test
    public void testQuebrarSolicitacao_cenario14() throws ValidacaoException, DAOException {
        List<AgendaDTO> agendas = singletonList(BuildCenario14.agendaDTO);

        when(examesAtendidos(BuildCenario14.grade1, BuildCenario14.examesPrestadorRealiza, asList(BuildCenario14.exameSolicitacao_1, BuildCenario14.exameSolicitacao_2, BuildCenario14.exameSolicitacao_3, BuildCenario14.exameSolicitacao_4), BuildCenario14.examePrestadorCompetencia, BuildCenario14.tipoTetoFinanceiro, BuildCenario14.tipoProcedimento)).thenReturn(asList(BuildCenario14.exameSolicitacao_1, BuildCenario14.exameSolicitacao_2));
        when(examesAtendidos(BuildCenario14.grade2, BuildCenario14.examesPrestadorRealiza, asList(BuildCenario14.exameSolicitacao_3, BuildCenario14.exameSolicitacao_4), BuildCenario14.examePrestadorCompetencia, BuildCenario14.tipoTetoFinanceiro, BuildCenario14.tipoProcedimento)).thenReturn(asList(BuildCenario14.exameSolicitacao_3, BuildCenario14.exameSolicitacao_4));

        when(BOFactory.getBO(AgendamentoFacade.class)).thenReturn(agendamentoBO);
        when(BOFactory.getBO(AgendamentoFacade.class).gerarSolicitacaoAgendamento(BuildCenario14.solicitacaoAgendamento, asList(BuildCenario14.exameSolicitacao_1, BuildCenario14.exameSolicitacao_2))).thenReturn(BuildCenario14.novaSolicitacao_1);
        when(CalcularTipoAgenda.isTipoAgendaDiaria(any(Agenda.class))).thenReturn(false);

        List<SolicitacaoAgendamento> agendamentos = QuebrarSolicitacaoLote.quebrarSolicitacao(BuildCenario14.solicitacaoAgendamento, agendas, BuildCenario14.examesDaSolicitacao, BuildCenario14.tipoTetoFinanceiro, BuildCenario14.tipoProcedimento, BuildCenario14.examesPrestadorProcedimentos);

        assertNotNull(agendamentos);
        assertEquals(2, agendamentos.size());
        assertSame(2L, agendamentos.get(0).getCodigo());
        assertSame(1L, agendamentos.get(1).getCodigo());
    }

    /* # CENARIO-15 #
        solicitação com 3 exames
        tipo procedimento configurado para 2 procedimentos por vaga com 2 exames duplicados (1, 2, 2)
        Prestador realiza todos os exames
        Prestador tem cota para todos os exames

        agenda disponível com 1 grade
        dia 1 tem uma vaga sem exame definido

        esperado:
            solicitacao 1 nova
            solicitacao 2 original
     */
    @Test
    public void testQuebrarSolicitacao_cenario15() throws ValidacaoException, DAOException {
        List<AgendaDTO> agendas = singletonList(BuildCenario15.agendaDTO);

        when(examesAtendidos(BuildCenario15.grade1, BuildCenario15.examesPrestadorRealiza, asList(BuildCenario15.exameSolicitacao_1, BuildCenario15.exameSolicitacao_2, BuildCenario15.exameSolicitacao_3), BuildCenario15.examePrestadorCompetencia, BuildCenario15.tipoTetoFinanceiro, BuildCenario15.tipoProcedimento)).thenReturn(asList(BuildCenario15.exameSolicitacao_1, BuildCenario15.exameSolicitacao_2, BuildCenario15.exameSolicitacao_3));
        when(BOFactory.getBO(AgendamentoFacade.class)).thenReturn(agendamentoBO);
        when(BOFactory.getBO(AgendamentoFacade.class).gerarSolicitacaoAgendamento(BuildCenario15.solicitacaoAgendamento, singletonList(BuildCenario15.exameSolicitacao_1))).thenReturn(BuildCenario15.novaSolicitacao_1);
        when(CalcularTipoAgenda.isTipoAgendaDiaria(any(Agenda.class))).thenReturn(false);

        List<SolicitacaoAgendamento> agendamentos = QuebrarSolicitacaoLote.quebrarSolicitacao(BuildCenario15.solicitacaoAgendamento, agendas, BuildCenario15.examesDaSolicitacao, BuildCenario15.tipoTetoFinanceiro, BuildCenario15.tipoProcedimento, BuildCenario15.examesPrestadorProcedimentos);

        assertNotNull(agendamentos);
        assertEquals(2, agendamentos.size());
        assertSame(2L, agendamentos.get(0).getCodigo());
        assertSame(1L, agendamentos.get(1).getCodigo());
    }

    /* # CENARIO-16 #
        Solicitação com 7 exames (1, 1, 1, 2, 2, 2, 3)
        Agenda sem exame definido
        Prestador atende todos os exames
        Tipo do procedimento configurado para 1 procedimento por vaga

        Agenda disponível:
            Dia 1 - 5 horários em sequência

        esperado:
            solicitacao 1 - nova
            solicitacao 2 - original
     */
    @Test
    public void testQuebrarSolicitacao_cenario16() throws ValidacaoException, DAOException {
        List<AgendaDTO> agendas = singletonList(BuildCenario16.agendaDTO);

        when(examesAtendidos(BuildCenario16.grade1, BuildCenario16.examesPrestadorRealiza, asList(BuildCenario16.exameSolicitacao_1, BuildCenario16.exameSolicitacao_2, BuildCenario16.exameSolicitacao_3, BuildCenario16.exameSolicitacao_4, BuildCenario16.exameSolicitacao_5, BuildCenario16.exameSolicitacao_6, BuildCenario16.exameSolicitacao_7), BuildCenario16.examePrestadorCompetencia, BuildCenario16.tipoTetoFinanceiro, BuildCenario16.tipoProcedimento)).thenReturn(BuildCenario16.examesDaSolicitacao);

        when(BOFactory.getBO(AgendamentoFacade.class)).thenReturn(agendamentoBO);
        when(BOFactory.getBO(AgendamentoFacade.class).gerarSolicitacaoAgendamento(BuildCenario16.solicitacaoAgendamento, asList(BuildCenario16.exameSolicitacao_1, BuildCenario16.exameSolicitacao_2, BuildCenario16.exameSolicitacao_5, BuildCenario16.exameSolicitacao_3))).thenReturn(BuildCenario16.novaSolicitacao_1);
        when(CalcularTipoAgenda.isTipoAgendaDiaria(any(Agenda.class))).thenReturn(false);

        List<SolicitacaoAgendamento> agendamentos = QuebrarSolicitacaoLote.quebrarSolicitacao(BuildCenario16.solicitacaoAgendamento, agendas, BuildCenario16.examesDaSolicitacao, BuildCenario16.tipoTetoFinanceiro, BuildCenario16.tipoProcedimento, BuildCenario16.examesPrestadorProcedimentos);

        assertNotNull(agendamentos);
        assertEquals(2, agendamentos.size());
        assertSame(2L, agendamentos.get(0).getCodigo());
        assertSame(1L, agendamentos.get(1).getCodigo());
    }

    /* # CENARIO-17 #
        solicitação com 4 exames (1, 2, 1, 3)
        tipo procedimento configurado para 1 procedimento por vaga
        Prestador realiza todos os exames
        Prestador tem cota para todos os exames

        agenda disponível com 1 grade
            dia 1 - uma vaga com exame definido (1)

        esperado:
            não deve quebrar
     */
    @Test
    public void testQuebrarSolicitacao_cenario17() throws ValidacaoException, DAOException {
        List<AgendaDTO> agendas = singletonList(BuildCenario17.agendaDTO);

        when(examesAtendidos(BuildCenario17.grade1, BuildCenario17.examesPrestadorRealiza, asList(BuildCenario17.exameSolicitacao_1, BuildCenario17.exameSolicitacao_2, BuildCenario17.exameSolicitacao_3, BuildCenario17.exameSolicitacao_4), BuildCenario17.examePrestadorCompetencia, BuildCenario17.tipoTetoFinanceiro, BuildCenario17.tipoProcedimento)).thenReturn(asList(BuildCenario17.exameSolicitacao_1, BuildCenario17.exameSolicitacao_3));

        when(CalcularTipoAgenda.isTipoAgendaDiaria(any(Agenda.class))).thenReturn(false);

        List<SolicitacaoAgendamento> agendamentos = QuebrarSolicitacaoLote.quebrarSolicitacao(BuildCenario17.solicitacaoAgendamento, agendas, BuildCenario17.examesDaSolicitacao, BuildCenario17.tipoTetoFinanceiro, BuildCenario17.tipoProcedimento, BuildCenario17.examesPrestadorProcedimentos);

        assertNotNull(agendamentos);
        assertEquals(0, agendamentos.size());
    }
}
