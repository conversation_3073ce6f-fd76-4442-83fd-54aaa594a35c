package br.com.ksisolucoes.bo.agendamento.exame.quebrarsolicitacao.cenarios;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.agendamento.exame.dto.angedamentosolictacaolote.AgendaDTO;
import br.com.ksisolucoes.agendamento.exame.dto.angedamentosolictacaolote.GradeDTO;
import br.com.ksisolucoes.agendamento.exame.dto.angedamentosolictacaolote.GradeHorarioDTO;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeHorario;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.exame.SolicitacaoAgendamentoExame;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class BuildCenario3 extends BuildCenarios {

    public static TipoProcedimento tipoProcedimento;
    public static AgendaDTO agendaDTO;
    public static GradeDTO grade1;

    public static List<ExamePrestadorProcedimento> examesPrestadorProcedimentos;
    public static List<ExameProcedimento> examesPrestadorRealiza;
    public static List<SolicitacaoAgendamentoExame> examesDaSolicitacao;
    public static ExamePrestadorCompetencia examePrestadorCompetencia;
    public static List<SolicitacaoAgendamentoExame> examesAtendidos;
    public static SolicitacaoAgendamentoExame exameSolicitacao_1;
    public static SolicitacaoAgendamentoExame exameSolicitacao_2;
    public static SolicitacaoAgendamentoExame exameSolicitacao_3;

    public static SolicitacaoAgendamento solicitacaoAgendamento;
    public static SolicitacaoAgendamento novaSolicitacao_1;

    public static Long tipoTetoFinanceiro = ExameCotaPpi.TipoTeto.FINANCEIRO.value();

    static {
        tipoProcedimento = tipoProcedimento(2L);
        grade1 = gradeDTO();
        examePrestadorCompetencia = examePrestadorCompetencia();
        examesPrestadorRealiza = examesPrestadorRealiza();
        examesAtendidos = examesAtendidos();
        exameSolicitacao_1 = exameSolicitacao(1L);
        exameSolicitacao_2 = exameSolicitacao(2L);
        exameSolicitacao_3 = exameSolicitacao(3L);
        examesDaSolicitacao = examesDaSolicitacao();
        agendaDTO = agendaDTO(1L);

        solicitacaoAgendamento = solicitacaoAgendamento(1L);
        novaSolicitacao_1 = solicitacaoAgendamento(2L);
        examesPrestadorProcedimentos = examesPrestadorProcedimentos(examesPrestadorRealiza);
    }

    private static List<SolicitacaoAgendamentoExame> examesAtendidos() {
        List<SolicitacaoAgendamentoExame> examesAtendidos = new ArrayList<>();
        examesAtendidos.add(exameSolicitacao(1L));
        examesAtendidos.add(exameSolicitacao(2L));
        examesAtendidos.add(exameSolicitacao(3L));
        return examesAtendidos;
    }

    private static ExamePrestadorCompetencia examePrestadorCompetencia() {
        ExamePrestadorCompetencia examePrestadorCompetencia = new ExamePrestadorCompetencia();
        examePrestadorCompetencia.setCodigo(1L);
        return examePrestadorCompetencia;
    }

    private static List<SolicitacaoAgendamentoExame> examesDaSolicitacao() {
        List<SolicitacaoAgendamentoExame> solicitacaoAgendamentoExames = new ArrayList<>();
        solicitacaoAgendamentoExames.add(exameSolicitacao_1);
        solicitacaoAgendamentoExames.add(exameSolicitacao_2);
        solicitacaoAgendamentoExames.add(exameSolicitacao_3);
        return solicitacaoAgendamentoExames;
    }

    private static List<ExameProcedimento> examesPrestadorRealiza() {
        List<ExameProcedimento> examesPrestadorRealiza = new ArrayList<>();
        examesPrestadorRealiza.add(exameProcedimento(1L));
        examesPrestadorRealiza.add(exameProcedimento(2L));
        examesPrestadorRealiza.add(exameProcedimento(3L));
        examesPrestadorRealiza.add(exameProcedimento(4L));
        return examesPrestadorRealiza;
    }

    public static AgendaDTO agendaDTO(Long id) {
        return new AgendaDTO()
                .setCodigo(id)
                .setGrades(grades())
                .setExamesPrestadorRealiza(examesPrestadorRealiza)
                .setExamePrestadorCompetencia(examePrestadorCompetencia);
    }

    private static List<GradeDTO> grades() {
        return Collections.singletonList(grade1);
    }

    private static GradeDTO gradeDTO() {
        return new GradeDTO()
                .setCodigo(1L)
                .setData(DataUtil.addDiasUteis(DataUtil.getDataAtual(), 1))
                .setQuantidadeVagasDisponivel(1L)
                .setHorarios(horarios());
    }

    private static List<GradeHorarioDTO> horarios() {
        List<GradeHorarioDTO> horarios = new ArrayList<>();
        horarios.add(gradeHorarioDTO(10L, AgendaGradeHorario.Status.AGENDADO));
        horarios.add(gradeHorarioDTO(20L, AgendaGradeHorario.Status.PENDENTE));
        horarios.add(gradeHorarioDTO(30L, AgendaGradeHorario.Status.AGENDADO));
        return horarios;
    }

    private static GradeHorarioDTO gradeHorarioDTO(Long id, AgendaGradeHorario.Status status) {
        return new GradeHorarioDTO().setCodigo(id).setStatus(status);
    }
}
