package br.com.ksisolucoes.bo.vigilancia.financeiro;

import br.com.celk.boleto.dto.boletocloud.request.PagadorDTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.EmissaoBoletoVigilanciaDTO;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.cadsus.TipoLogradouroCadsus;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import junit.framework.TestCase;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({LoadManager.class})
public class GerarBoletoVigilanciaTest extends TestCase {

    @Mock
    LoadManager vigilanciaEnderecoLoadManager;

    @Mock
    private VigilanciaEndereco vigilanciaEnderecoMock;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(LoadManager.class);
    }

    private void mockLoadManager(Class clazz, LoadManager loadManager) {
        when(LoadManager.getInstance(clazz)).thenReturn(loadManager);
        when(loadManager.addProperties((String) any())).thenReturn(loadManager);
        when(loadManager.addProperty(any())).thenReturn(loadManager);
        when(loadManager.setId(any())).thenReturn(loadManager);
        when(loadManager.start()).thenReturn(loadManager);
        when(loadManager.getVO()).thenReturn(vigilanciaEnderecoMock);
    }

    @Test
    public void testGetEnderecoVigilanciaPagador_parametroNullNaoDeveQuebrar() {
        GerarBoletoVigilancia gerarBoletoVigilancia = new GerarBoletoVigilancia(new EmissaoBoletoVigilanciaDTO());

        VigilanciaEndereco vigilanciaEndereco = null;
        String numeroLogradouro = null;
        PagadorDTO pagadorDTO = null;

        assertNull(gerarBoletoVigilancia.getEnderecoVigilanciaPagador(vigilanciaEndereco, numeroLogradouro, pagadorDTO));

    }

    @Test
    public void testBuscarFiscais_parametroNotNullDeveRetornarFiscais() {
        mockLoadManager(VigilanciaEndereco.class, vigilanciaEnderecoLoadManager);

        GerarBoletoVigilancia gerarBoletoVigilancia = new GerarBoletoVigilancia(new EmissaoBoletoVigilanciaDTO());

        VigilanciaEndereco vigilanciaEndereco = getVigilanciaEndereco();
        when(vigilanciaEnderecoLoadManager.start()).thenReturn(vigilanciaEnderecoLoadManager);
        when(vigilanciaEnderecoLoadManager.getVO()).thenReturn(vigilanciaEndereco);

        VigilanciaEndereco vigilanciaEnderecoLoad = gerarBoletoVigilancia.carregarEnderecoVigilancia(1L);
        assertEquals(Long.valueOf(1), vigilanciaEnderecoLoad.getCodigo());
        assertEquals("cep", vigilanciaEnderecoLoad.getCep());
        assertEquals("bairro", vigilanciaEnderecoLoad.getBairro());
        assertEquals("logradouro", vigilanciaEnderecoLoad.getLogradouro());
        assertEquals("Rua", vigilanciaEnderecoLoad.getTipoLogradouro().getDescricao());
        assertEquals("cidade", vigilanciaEnderecoLoad.getCidade().getDescricao());
        assertEquals("Estado", vigilanciaEnderecoLoad.getCidade().getEstado().getDescricao());
    }

    @Test
    public void testGetEnderecoVigilanciaPagador_parametroNotNullDeveRetornarPagadorDTOPreenchido() {
        mockLoadManager(VigilanciaEndereco.class, vigilanciaEnderecoLoadManager);

        GerarBoletoVigilancia gerarBoletoVigilancia = new GerarBoletoVigilancia(new EmissaoBoletoVigilanciaDTO());

        VigilanciaEndereco vigilanciaEndereco = getVigilanciaEndereco();
        when(vigilanciaEnderecoLoadManager.start()).thenReturn(vigilanciaEnderecoLoadManager);
        when(vigilanciaEnderecoLoadManager.getVO()).thenReturn(vigilanciaEndereco);

        PagadorDTO pagadorDTO = new PagadorDTO();
        pagadorDTO = gerarBoletoVigilancia.getEnderecoVigilanciaPagador(vigilanciaEndereco, "10", pagadorDTO);

        assertEquals("cep", pagadorDTO.getCep());
        assertEquals("bairro", pagadorDTO.getBairro());
        assertEquals("Rua logradouro", pagadorDTO.getLogradouro());
        assertEquals("cidade", pagadorDTO.getCidade());
        assertEquals("SC", pagadorDTO.getUf());
        assertEquals("10", pagadorDTO.getNumeroLogradouro());

        pagadorDTO = gerarBoletoVigilancia.getEnderecoVigilanciaPagador(vigilanciaEndereco, null, pagadorDTO);
        assertEquals("S/N", pagadorDTO.getNumeroLogradouro());
    }

    @Test
    public void testGetEnderecoVigilanciaPagador_parametroNumeroLogradouroNullDeveRetornarPagadorDTOPreenchidoComNumeroSN() {
        mockLoadManager(VigilanciaEndereco.class, vigilanciaEnderecoLoadManager);

        GerarBoletoVigilancia gerarBoletoVigilancia = new GerarBoletoVigilancia(new EmissaoBoletoVigilanciaDTO());

        VigilanciaEndereco vigilanciaEndereco = getVigilanciaEndereco();
        when(vigilanciaEnderecoLoadManager.start()).thenReturn(vigilanciaEnderecoLoadManager);
        when(vigilanciaEnderecoLoadManager.getVO()).thenReturn(vigilanciaEndereco);

        PagadorDTO pagadorDTO = new PagadorDTO();
        pagadorDTO = gerarBoletoVigilancia.getEnderecoVigilanciaPagador(vigilanciaEndereco, null, pagadorDTO);

        assertEquals("cep", pagadorDTO.getCep());
        assertEquals("bairro", pagadorDTO.getBairro());
        assertEquals("Rua logradouro", pagadorDTO.getLogradouro());
        assertEquals("cidade", pagadorDTO.getCidade());
        assertEquals("SC", pagadorDTO.getUf());
        assertEquals("S/N", pagadorDTO.getNumeroLogradouro());

    }

    @Test
    public void testRemoveCaracterEspecial_deveRetornarLogradouroSemCaracterEspecial() {
        GerarBoletoVigilancia gerarBoletoVigilancia = new GerarBoletoVigilancia(new EmissaoBoletoVigilanciaDTO());

        assertNull(gerarBoletoVigilancia.removeCaracterEspecial(null));

        assertEquals("N", gerarBoletoVigilancia.removeCaracterEspecial("N°"));
        assertEquals("Rua", gerarBoletoVigilancia.removeCaracterEspecial("Rua_"));
        assertEquals("Rua ", gerarBoletoVigilancia.removeCaracterEspecial("Rua $"));
        assertEquals("Rua ", gerarBoletoVigilancia.removeCaracterEspecial("Rua %"));
        assertEquals("Rua ", gerarBoletoVigilancia.removeCaracterEspecial("Rua -"));
        assertEquals("Rua ", gerarBoletoVigilancia.removeCaracterEspecial("Rua /"));
        assertEquals("Rua", gerarBoletoVigilancia.removeCaracterEspecial("Ruá"));
        assertEquals("Rua", gerarBoletoVigilancia.removeCaracterEspecial("Ruâ"));
        assertEquals("Rua", gerarBoletoVigilancia.removeCaracterEspecial("Rua*"));
        assertEquals("Rua 1", gerarBoletoVigilancia.removeCaracterEspecial("Rua 1"));
    }

    private VigilanciaEndereco getVigilanciaEndereco() {
        VigilanciaEndereco vigilanciaEndereco = new VigilanciaEndereco();
        vigilanciaEndereco.setCodigo(1L);
        vigilanciaEndereco.setCep("cep");
        vigilanciaEndereco.setBairro("bairro");
        vigilanciaEndereco.setLogradouro("logradouro");
        vigilanciaEndereco.setTipoLogradouro(new TipoLogradouroCadsus(1l, "Rua", 0l));
        vigilanciaEndereco.setCidade(new Cidade(1l, new Estado(1l, "SC", "Estado", 0l, 0l), "cidade"));

        return vigilanciaEndereco;
    }
}