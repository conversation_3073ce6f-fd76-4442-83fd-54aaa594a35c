package br.com.celk.im.integracoes.prontuario.basico.solicitacaoagendamento;

import br.com.celk.im.core.AbstractIntregratingModules;
import br.com.celk.im.core.IMFromTo;
import br.com.celk.im.core.IMTableConfiguration;
import br.com.celk.im.vo.prontuario.basico.IMTableSolicitacaoAgendamentoToAih;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;

/**
 *
 * <AUTHOR>
 */
@IMFromTo(from=SolicitacaoAgendamento.class, to=Aih.class)
@IMTableConfiguration(table= IMTableSolicitacaoAgendamentoToAih.class, pathFrom="SolicitacaoAgendamento", pathTo="Aih")
public class IMSolicitacaoAgendamentoToAih extends AbstractIntregratingModules<SolicitacaoAgendamento, Aih>{

    @Override
    public Aih insert(SolicitacaoAgendamento origin) throws DAOException, ValidacaoException {
        return null;
    }

    @Override
    public void update(SolicitacaoAgendamento origin, Aih destination) throws DAOException, ValidacaoException {
        if (SolicitacaoAgendamento.STATUS_CANCELADO.equals(origin.getStatus())) {
            destination.setStatus(Aih.Status.CANCELADA.value());
            destination.setDataAlteracao(DataUtil.getDataAtual());
            destination.setUsuarioCancelamento(SessaoAplicacaoImp.getInstance().getUsuario());
            destination.setMotivoCancelamento("Solicitação de Agendamento Cancelada");
            BOFactory.save(destination);
        } else if (SolicitacaoAgendamento.STATUS_AGENDADO.equals(origin.getStatus())
                    || SolicitacaoAgendamento.STATUS_AGENDADO_FORA_REDE.equals(origin.getStatus())){
            destination.setStatus(Aih.Status.AUTORIZADA.value());
            BOFactory.save(destination);
        } 
    }

    @Override
    public void delete(SolicitacaoAgendamento origin, Aih destination) throws DAOException, ValidacaoException {

    }

}