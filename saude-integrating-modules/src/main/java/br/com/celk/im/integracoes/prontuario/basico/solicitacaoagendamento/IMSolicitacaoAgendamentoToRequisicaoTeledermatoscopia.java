package br.com.celk.im.integracoes.prontuario.basico.solicitacaoagendamento;

import br.com.celk.im.core.AbstractIntregratingModules;
import br.com.celk.im.core.IMFromTo;
import br.com.celk.im.core.IMTableConfiguration;
import br.com.celk.im.integracoes.prontuario.basico.solicitacaoagendamento.utils.ExameUtils;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import br.com.celk.im.vo.prontuario.basico.IMTableSolicitacaoAgendamentoToRequisicaoTeledermatoscopia;
import br.com.ksisolucoes.vo.prontuario.basico.RequisicaoTeledermatoscopia;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
@IMFromTo(from=SolicitacaoAgendamento.class, to=RequisicaoTeledermatoscopia.class)
@IMTableConfiguration(table=IMTableSolicitacaoAgendamentoToRequisicaoTeledermatoscopia.class, pathFrom="SolicitacaoAgendamento", pathTo="RequisicaoTeledermatoscopia")
public class IMSolicitacaoAgendamentoToRequisicaoTeledermatoscopia extends AbstractIntregratingModules<SolicitacaoAgendamento, RequisicaoTeledermatoscopia>{

    @Override
    public RequisicaoTeledermatoscopia insert(SolicitacaoAgendamento origin) throws DAOException, ValidacaoException {
        return null;
    }

    @Override
    public void update(SolicitacaoAgendamento origin, RequisicaoTeledermatoscopia destination) throws DAOException, ValidacaoException {
        if (SolicitacaoAgendamento.STATUS_CANCELADO.equals(origin.getStatus())) {
            destination.getExameRequisicao().setStatus(ExameRequisicao.Status.NAO_REALIZADO.value());
            BOFactory.getBO(ExameFacade.class).salvarExameRequisicao(destination.getExameRequisicao());
        } else if (SolicitacaoAgendamento.STATUS_AGENDADO.equals(origin.getStatus())
                    || SolicitacaoAgendamento.STATUS_AGENDADO_FORA_REDE.equals(origin.getStatus())){
            if (destination.getExameRequisicao().getExame().getStatus() == null) {
                Exame exameCompleto = ExameUtils.getExameCompleto(getSession(), destination.getExameRequisicao().getExame().getCodigo());
                if (exameCompleto != null) {
                    destination.getExameRequisicao().setExame(exameCompleto);
                }
            }
            if(destination.getExameRequisicao().getExame().getStatus() < Exame.STATUS_AUTORIZADO){
                destination.getExameRequisicao().getExame().setStatus(Exame.STATUS_AUTORIZADO);                
            }
            destination.getExameRequisicao().getExame().setDataAgendamento(origin.getDataAgendamento());
            BOFactory.save(destination.getExameRequisicao());
        } 
    }

    @Override
    public void delete(SolicitacaoAgendamento origin, RequisicaoTeledermatoscopia destination) throws DAOException, ValidacaoException {

    }

}