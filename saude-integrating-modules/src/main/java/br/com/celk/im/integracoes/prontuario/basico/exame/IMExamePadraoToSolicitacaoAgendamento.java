package br.com.celk.im.integracoes.prontuario.basico.exame;

import br.com.celk.agendamento.AgendamentoHelper;
import br.com.celk.im.core.AbstractIntregratingModules;
import br.com.celk.im.core.IMFromTo;
import br.com.celk.im.core.IMTableConfiguration;
import br.com.celk.im.vo.prontuario.basico.IMTableSolicitacaoAgendamentoToExamePadrao;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.exame.SolicitacaoAgendamentoExame;
import ch.lambdaj.Lambda;
import org.hibernate.criterion.Restrictions;

import java.util.List;

import static ch.lambdaj.Lambda.having;
import static ch.lambdaj.Lambda.on;
import static org.hamcrest.Matchers.equalTo;

@IMFromTo(from = Exame.class, to = SolicitacaoAgendamento.class)
@IMTableConfiguration(table = IMTableSolicitacaoAgendamentoToExamePadrao.class, pathFrom = "Exame", pathTo = "SolicitacaoAgendamento")
public class IMExamePadraoToSolicitacaoAgendamento extends AbstractIntregratingModules<Exame, SolicitacaoAgendamento> {

    private String tipoControleRegulacao;
    public static final Long CLASSIFICACAO_MUITO_URGENTE = 5L;

    @Override
    public SolicitacaoAgendamento insert(Exame origin) throws DAOException, ValidacaoException {
        if (Exame.Origem.LAUDO_APAC.value().equals(origin.getOrigem()) || Exame.Origem.BPAI.value().equals(origin.getOrigem()))
            return null;
        return gerarSolicitacaoAgendamento(origin);
    }

    @Override
    public void update(Exame origin, SolicitacaoAgendamento destination) throws DAOException, ValidacaoException {
        TipoExame tipoExame = (TipoExame) this.getSession().get(TipoExame.class, origin.getTipoExame().getCodigo());

        Long habilitaClassificacaoRiscoEncaminhamentoEspecialista = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("habilitaClassificaçãoRiscoEncaminhamentoEspecialista");
        String habilitaClassificacaoRiscoExame = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("habilitaClassificacaoRiscoExame");

        if (TipoExame.Classificacao.EXAMES_PADRAO.value().equals(tipoExame.getClassificacao())) {
            if (Exame.STATUS_SOLICITADO.equals(origin.getStatus()) && RepositoryComponentDefault.NAO_LONG.equals(Coalesce.asLong(origin.getNaoColocarListaEsperaSus(), RepositoryComponentDefault.NAO_LONG))) {
                if (destination != null) {
                    if (RepositoryComponentDefault.SIM_LONG.equals(origin.getFlagEnviarRegulacao())) {
                        if (origin.getDescricaoEnviarRegulacao() == null && origin.getDescricaoDadoClinico() == null) {
                            throw new ValidacaoException(Bundle.getStringApplication("msg_para_continuar_informe_dados_clinicos_ou_descricao_envio_regulacao_obrigatoria"));
                        }
                        destination.setStatus(SolicitacaoAgendamento.STATUS_REGULACAO_PENDENTE);
                        destination.setTipoFila(SolicitacaoAgendamento.TIPO_FILA_REGULACAO);
                    } else {
                        destination.setStatus(SolicitacaoAgendamento.STATUS_FILA_ESPERA);
                        destination.setPrioridade(SolicitacaoAgendamento.PRIORIDADE_ELETIVO);
                        destination.setTipoFila(SolicitacaoAgendamento.TIPO_FILA_NORMAL);
                    }

                    destination.setDataProcessamentoLote(null);
                    destination.setFlagEnviarRegulacao(Coalesce.asLong(origin.getFlagEnviarRegulacao(), RepositoryComponentDefault.NAO_LONG));
                    destination.setDescricaoEnviarRegulacao(origin.getDescricaoEnviarRegulacao());
                    destination.setObservacaoUrgente(origin.getDescricaoDadoClinico());

                    ajustaPrioridadeSolicitacao(origin.getClassificacaoDeRisco(), destination);

                    getSession().save(destination);
                    setProcedimentos(origin, destination);
                }
            } else if (Exame.STATUS_CANCELADO.equals(origin.getStatus())) {
                if (!SolicitacaoAgendamento.STATUS_CANCELADO.equals(destination.getStatus()) && origin != null && origin.getDescricaoCancelamento() != null) {
                    BOFactory.getBO(AgendamentoFacade.class).gerarOcorrenciaSolicitacaoAgendamento(SolicitacaoAgendamentoOcorrencia.TipoOcorrencia.SOLICITACAO, Bundle.getStringApplication("rotulo_cancelado_solicitacao_motivo_x", origin.getDescricaoCancelamento()), destination);
                }
                destination.setStatus(SolicitacaoAgendamento.STATUS_CANCELADO);
                getSession().save(destination);
            }
        }
    }

    @Override
    public void delete(Exame exame, SolicitacaoAgendamento destination) throws DAOException, ValidacaoException {
        TipoExame tipoExame = (TipoExame) this.getSession().get(TipoExame.class, exame.getTipoExame().getCodigo());
        if (TipoExame.Classificacao.EXAMES_PADRAO.value().equals(tipoExame.getClassificacao())) {
            BOFactory.delete(destination);
        }
    }

    private boolean gerarSolicitacao(Exame exame) {
        TipoExame tipoExame = (TipoExame) this.getSession().get(TipoExame.class, exame.getTipoExame().getCodigo());

        return RepositoryComponentDefault.SIM.equals(tipoExame.getAgendado());
    }

    public void setProcedimentos(Exame exame, SolicitacaoAgendamento solicitacaoAgendamento) throws ValidacaoException, DAOException {
        if (gerarSolicitacao(exame)
                && RepositoryComponentDefault.NAO_LONG.equals(Coalesce.asLong(exame.getNaoColocarListaEsperaSus(), RepositoryComponentDefault.NAO_LONG))
                && exame.getAtendimento() != null) {
            TipoExame tipoExame = (TipoExame) this.getSession().get(TipoExame.class, exame.getTipoExame().getCodigo());
            if (TipoExame.Classificacao.EXAMES_PADRAO.value().equals(tipoExame.getClassificacao())) {
                TipoProcedimento tipoProcedimento = tipoExame.getTipoProcedimento();
                if (tipoProcedimento == null) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_tipo_procedimento_nao_configurado_tipo_exame_X", tipoExame.getDescricaoFormatado()));
                }
                boolean procedimentoClassificacaoExame = tipoProcedimento.getTipoProcedimentoClassificacao().pertenceClassificacaoExame();
                if (procedimentoClassificacaoExame) {
                    solicitacaoAgendamento.setValidarExisteOutraSolicitacao(false);
                    validarTipoProcedimentoUsuarioList(solicitacaoAgendamento);
                }
                if (procedimentoClassificacaoExame) {
                    salvarRequisicoesDoExame(solicitacaoAgendamento, exame);
                }
            }
        }
    }

    private void salvarRequisicoesDoExame(SolicitacaoAgendamento solicitacaoAgendamento, Exame exame) throws DAOException, ValidacaoException {
        List<ExameRequisicao> requisicoes = getRequisicoesDoExame(exame);
        if (CollectionUtils.isNotNullEmpty(requisicoes)) {
            for (ExameRequisicao requisicao : requisicoes) {
                salvarExameNaSolicitacao(solicitacaoAgendamento, requisicao);
                requisicao.setSolicitacaoAgendamento(solicitacaoAgendamento);
                BOFactory.save(requisicao);
            }
        }
    }

    private List<ExameRequisicao> getRequisicoesDoExame(Exame exame) {
        return this.getSession().createCriteria(ExameRequisicao.class)
                .add(Restrictions.eq(ExameRequisicao.PROP_EXAME, exame))
                .list();
    }

    private void salvarExameNaSolicitacao(SolicitacaoAgendamento solicitacaoAgendamento, ExameRequisicao requisicao) throws DAOException, ValidacaoException {
        for (int i = 0; i < requisicao.getQuantidade(); i++) {
            if (!solicitacaoAgendamentoExameExists(solicitacaoAgendamento, requisicao)) {
                SolicitacaoAgendamentoExame solicitacaoAgendamentoExame = new SolicitacaoAgendamentoExame();
                solicitacaoAgendamentoExame.setSolicitacaoAgendamento(solicitacaoAgendamento);
                solicitacaoAgendamentoExame.setExameProcedimento(requisicao.getExameProcedimento());
                solicitacaoAgendamentoExame.setComplemento(requisicao.getComplemento());
                BOFactory.save(solicitacaoAgendamentoExame);
            }
        }
    }

    private boolean solicitacaoAgendamentoExameExists(SolicitacaoAgendamento solicitacaoAgendamento, ExameRequisicao exameRequisicao) {
        return LoadManager.getInstance(SolicitacaoAgendamentoExame.class)
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoExame.PROP_SOLICITACAO_AGENDAMENTO, solicitacaoAgendamento))
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoExame.PROP_EXAME_PROCEDIMENTO, exameRequisicao.getExameProcedimento()))
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoExame.PROP_COMPLEMENTO, exameRequisicao.getComplemento()))
                .start().exists();
    }

    private SolicitacaoAgendamento gerarSolicitacaoAgendamento(Exame exame) throws DAOException, ValidacaoException {
        if (gerarSolicitacao(exame)
                && RepositoryComponentDefault.NAO_LONG.equals(Coalesce.asLong(exame.getNaoColocarListaEsperaSus(), RepositoryComponentDefault.NAO_LONG))
                && exame.getAtendimento() != null) {
            TipoExame tipoExame = (TipoExame) this.getSession().get(TipoExame.class, exame.getTipoExame().getCodigo());
            if (TipoExame.Classificacao.EXAMES_PADRAO.value().equals(tipoExame.getClassificacao())) {
                List<SolicitacaoAgendamento> solicitacoesAgendamento = carregaSolicitacoesAgendamento(exame, tipoExame);

                SolicitacaoAgendamento solicitacaoAgendamento = new SolicitacaoAgendamento();

                ExameRequisicao exameRequisicao = (ExameRequisicao) this.getSession().createCriteria(ExameRequisicao.class)
                        .add(Restrictions.eq(ExameRequisicao.PROP_EXAME, exame))
                        .setMaxResults(1)
                        .uniqueResult();

                if (!solicitacaoAgendamentoExameProcedimentoExists(solicitacoesAgendamento, exameRequisicao)) {
                    TipoProcedimento tipoProcedimento = tipoExame.getTipoProcedimento();

                    if (tipoProcedimento == null) {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_tipo_procedimento_nao_configurado_tipo_exame_X", tipoExame.getDescricaoFormatado()));
                    }

                    boolean procedimentoClassificacaoExame = tipoProcedimento.getTipoProcedimentoClassificacao().pertenceClassificacaoExame();
                    if (procedimentoClassificacaoExame) {
                        solicitacaoAgendamento.setValidarExisteOutraSolicitacao(false);
                        validarTipoProcedimentoUsuarioList(solicitacaoAgendamento);
                    }

                    solicitacaoAgendamento.setTipoProcedimento(tipoProcedimento);
                    solicitacaoAgendamento.setProcedimento(tipoProcedimento.getProcedimento());
                    if (solicitacaoAgendamento.getEmpresa() == null) {
                        solicitacaoAgendamento.setEmpresa(this.getEmpresa(exame));
                    }
                    solicitacaoAgendamento.setUnidadeOrigem(exame.getAtendimento().getEmpresa());
                    solicitacaoAgendamento.setProfissional(exame.getProfissional());
                    solicitacaoAgendamento.setUsuarioCadsus(exame.getUsuarioCadsus());
                    solicitacaoAgendamento.setAtendimentoOrigem(exame.getAtendimento());
                    solicitacaoAgendamento.setDataSolicitacao(exame.getDataCadastro());
                    solicitacaoAgendamento.setObservacaoUrgente(exame.getDescricaoDadoClinico());
                    solicitacaoAgendamento.setTipoConsulta(SolicitacaoAgendamento.TIPO_CONSULTA_NORMAL);
                    solicitacaoAgendamento.setPrioridade(Exame.URGENTE_SIM.equals(exame.getFlagUrgente()) ? SolicitacaoAgendamento.PRIORIDADE_URGENTE : SolicitacaoAgendamento.PRIORIDADE_ELETIVO);
                    solicitacaoAgendamento.setObservacao(exame.getDescricaoDadoClinico());
                    solicitacaoAgendamento.setObservacaoUrgente(exame.getDescricaoDadoClinico());
                    if (solicitacaoAgendamento.getEmpresa() == null && exame.getAtendimento() != null) {
                        Atendimento a = (Atendimento) getSession().get(Atendimento.class, exame.getAtendimento().getCodigo());
                        solicitacaoAgendamento.setEmpresa(a.getEmpresaSolicitante());
                    }
                    if (RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao())) {
                        if (RepositoryComponentDefault.SIM_LONG.equals(exame.getFlagEnviarRegulacao())) {
                            if (exame.getDescricaoEnviarRegulacao() == null && exame.getDescricaoDadoClinico() == null) {
                                throw new ValidacaoException(Bundle.getStringApplication("msg_para_continuar_informe_dados_clinicos_ou_descricao_envio_regulacao_obrigatoria"));
                            }
                            solicitacaoAgendamento.setStatus(SolicitacaoAgendamento.STATUS_REGULACAO_PENDENTE);
                            solicitacaoAgendamento.setTipoFila(SolicitacaoAgendamento.TIPO_FILA_REGULACAO);
                        } else {
                            solicitacaoAgendamento.setStatus(SolicitacaoAgendamento.STATUS_FILA_ESPERA);
                            solicitacaoAgendamento.setPrioridade(SolicitacaoAgendamento.PRIORIDADE_ELETIVO);
                            solicitacaoAgendamento.setTipoFila(SolicitacaoAgendamento.TIPO_FILA_NORMAL);
                        }
                        solicitacaoAgendamento.setFlagEnviarRegulacao(Coalesce.asLong(exame.getFlagEnviarRegulacao(), RepositoryComponentDefault.NAO_LONG));
                        solicitacaoAgendamento.setDescricaoEnviarRegulacao(exame.getDescricaoEnviarRegulacao());
                        solicitacaoAgendamento.setObservacaoUrgente(exame.getDescricaoDadoClinico());
                    }

                    ajustaPrioridadeSolicitacao(exame.getClassificacaoDeRisco(), solicitacaoAgendamento);

                    SolicitacaoAgendamento savedSolicitacaoAgendamento = BOFactory.getBO(CadastroFacade.class).save(solicitacaoAgendamento);

                    if (RepositoryComponentDefault.SIM_LONG.equals(solicitacaoAgendamento.getSolicitarPrioridade())) {
                        solicitacaoAgendamento.setStatus(SolicitacaoAgendamento.STATUS_FILA_ESPERA);
                        solicitacaoAgendamento.setPrioridade(SolicitacaoAgendamento.PRIORIDADE_ELETIVO);
                        BOFactory.getBO(AgendamentoFacade.class).gerarOcorrenciaSolicitacaoAgendamento(SolicitacaoAgendamentoOcorrencia.TipoOcorrencia.SOLICITACAO, Bundle.getStringApplication("rotulo_gerado_prioridade_solicitacao", solicitacaoAgendamento.getUsuario()), solicitacaoAgendamento);
                    }
                    if (RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao())) {
                        String descricaoOcorrencia;
                        if (RepositoryComponentDefault.SIM_LONG.equals(exame.getFlagEnviarRegulacao())) {
                            descricaoOcorrencia = Bundle.getStringApplication("rotulo_solicitacao_encaminhada_regulacao") + " Descrição: " + exame.getDescricaoEnviarRegulacao();
                        } else {
                            descricaoOcorrencia = Bundle.getStringApplication("rotulo_solicitacao_encaminhada_fila_espera");
                        }
                        BOFactory.getBO(AgendamentoFacade.class).gerarOcorrenciaSolicitacaoAgendamento(SolicitacaoAgendamentoOcorrencia.TipoOcorrencia.SOLICITACAO, descricaoOcorrencia, solicitacaoAgendamento);
                    }
                    if (exameRequisicao != null && exameRequisicao.getCodigo() != null) {
                        if (exameRequisicao.getExameProcedimento().getProcedimento() == null) {
                            throw new ValidacaoException(Bundle.getStringApplication("msg_procedimento_nao_definido_para_o_tipo_de_procedimento_selecionado", exameRequisicao.getExameProcedimento().getDescricaoFormatado()));
                        }
                        exameRequisicao.setSolicitacaoAgendamento(savedSolicitacaoAgendamento);
                        BOFactory.save(exameRequisicao);
                    }
                    if (procedimentoClassificacaoExame) {
                        salvarRequisicoesDoExame(savedSolicitacaoAgendamento, exame);
                    }
                    return savedSolicitacaoAgendamento;
                }
            }
        }
        return null;
    }

    private List<SolicitacaoAgendamento> carregaSolicitacoesAgendamento(Exame exame, TipoExame tipoExame) {
        if (exame.getUsuarioCadsus() == null ||
                tipoExame == null ||
                tipoExame.getTipoProcedimento() == null) return null;
        return LoadManager.getInstance(SolicitacaoAgendamento.class)
                .addProperties(new HQLProperties(SolicitacaoAgendamento.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamento.PROP_USUARIO_CADSUS, exame.getUsuarioCadsus()))
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamento.PROP_TIPO_PROCEDIMENTO, tipoExame.getTipoProcedimento()))
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamento.PROP_STATUS, BuilderQueryCustom.QueryParameter.IN, SolicitacaoAgendamento.STATUS_PENDENTES))
                .start().getList();
    }

    private boolean solicitacaoAgendamentoExameProcedimentoExists(List<SolicitacaoAgendamento> solicitacaoAgendamentos, ExameRequisicao exameRequisicao) {
        if (!CollectionUtils.isNotNullEmpty(solicitacaoAgendamentos) || exameRequisicao == null) return false;
        return LoadManager.getInstance(SolicitacaoAgendamentoExame.class)
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoExame.PROP_SOLICITACAO_AGENDAMENTO, BuilderQueryCustom.QueryParameter.IN, solicitacaoAgendamentos))
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoExame.PROP_EXAME_PROCEDIMENTO, exameRequisicao.getExameProcedimento()))
                .start().exists();
    }

    private void validarTipoProcedimentoUsuarioList(SolicitacaoAgendamento solicitacaoAgendamento) throws ValidacaoException {
        List<TipoProcedimentoUsuario> tipoProcedimentoUsuarioList = this.getSession().createCriteria(TipoProcedimentoUsuario.class)
                .add(Restrictions.eq(TipoProcedimentoUsuario.PROP_TIPO_PROCEDIMENTO, solicitacaoAgendamento.getTipoProcedimento()))
                .list();
        if (CollectionUtils.isNotNullEmpty(tipoProcedimentoUsuarioList)) {
            Usuario usuario = SessaoAplicacaoImp.getInstance().getUsuario();
            boolean existePermissaoUsuario = Lambda.exists(tipoProcedimentoUsuarioList, having(on(TipoProcedimentoUsuario.class).getUsuario().getCodigo(), equalTo(usuario.getCodigo())));
            if (!existePermissaoUsuario) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_usuario_X_nao_tem_permissao_para_solicitar_tipo_procedimento_X",
                        usuario.getNome(), solicitacaoAgendamento.getTipoProcedimento().getDescricao()));
            }
        }
    }

    public String getTipoControleRegulacao() {
        if (tipoControleRegulacao == null) {
            try {
                tipoControleRegulacao = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("tipoControleRegulação");
            } catch (DAOException e) {
                Loggable.log.error(e);
            }
        }
        return tipoControleRegulacao;
    }

    private Empresa getEmpresa(Exame exame) {
        return AgendamentoHelper.empresaResponsavelSolicitacaoAgendamentoPorExame(exame, getTipoControleRegulacao());
    }

    private void ajustaPrioridadeSolicitacao(ClassificacaoRisco classificacaoRisco, SolicitacaoAgendamento solicitacaoAgendamento) throws DAOException {
        if (classificacaoRisco == null) return;

        Long habilitaClassificacaoRiscoEncaminhamentoEspecialista = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("habilitaClassificaçãoRiscoEncaminhamentoEspecialista");
        String habilitaClassificacaoRiscoExame = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("habilitaClassificacaoRiscoExame");

        if (RepositoryComponentDefault.SIM_LONG.equals(habilitaClassificacaoRiscoEncaminhamentoEspecialista) ||
                RepositoryComponentDefault.SIM.equals(habilitaClassificacaoRiscoExame)) {

            if (ClassificacaoRisco.Classificacao.EMERGENCIA.value().equals(classificacaoRisco.getNivelGravidade())) {
                solicitacaoAgendamento.setPrioridade(SolicitacaoAgendamento.PRIORIDADE_URGENTE);
            } else if (ClassificacaoRisco.Classificacao.MUITO_URGENTE.value().equals(classificacaoRisco.getNivelGravidade())) {
                solicitacaoAgendamento.setPrioridade(SolicitacaoAgendamento.PRIORIDADE_BREVIDADE);
            } else if (ClassificacaoRisco.Classificacao.URGENTE.value().equals(classificacaoRisco.getNivelGravidade())) {
                solicitacaoAgendamento.setPrioridade(SolicitacaoAgendamento.PRIORIDADE_ELETIVO);
            } else if (ClassificacaoRisco.Classificacao.POUCO_URGENTE.value().equals(classificacaoRisco.getNivelGravidade())) {
                solicitacaoAgendamento.setPrioridade(SolicitacaoAgendamento.PRIORIDADE_POUCO_URGENTE);
            } else if (ClassificacaoRisco.Classificacao.NAO_URGENTE.value().equals(classificacaoRisco.getNivelGravidade())) {
                solicitacaoAgendamento.setPrioridade(SolicitacaoAgendamento.PRIORIDADE_NAO_URGENTE);
            } else {
                solicitacaoAgendamento.setPrioridade(SolicitacaoAgendamento.SEM_PRIORIDADE);
            }
            solicitacaoAgendamento.setClassificacaoRisco(classificacaoRisco);
        }
    }
}