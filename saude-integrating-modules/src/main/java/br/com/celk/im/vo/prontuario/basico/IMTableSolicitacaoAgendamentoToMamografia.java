package br.com.celk.im.vo.prontuario.basico;

import javax.persistence.*;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "im_solic_agend_to_mamografia")
public class IMTableSolicitacaoAgendamentoToMamografia implements Serializable {
    
    @Id
    @Column(name="cd_im_mamografia")
    @GeneratedValue(strategy=GenerationType.SEQUENCE, generator="seq_im_solic_agend_to_mam_req")
    @SequenceGenerator(name="seq_im_solic_agend_to_mam_req", sequenceName="seq_im_solic_agend_to_mam_req", allocationSize = 1)
    private Long codigo;
    
    @Version
    @Column(name="version")
    private Long version;
    
    @Column(name="cd_solicitacao")
    private Long codigoSolicitacaoAgendamento;
    
    @Column(name="cd_mamografia")
    private Long codigoMamografia;

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public Long getCodigoSolicitacaoAgendamento() {
        return codigoSolicitacaoAgendamento;
    }

    public void setCodigoSolicitacaoAgendamento(Long codigoSolicitacaoAgendamento) {
        this.codigoSolicitacaoAgendamento = codigoSolicitacaoAgendamento;
    }

    public Long getCodigoMamografia() {
        return codigoMamografia;
    }

    public void setCodigoMamografia(Long codigoMamografia) {
        this.codigoMamografia = codigoMamografia;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 97 * hash + (this.codigo != null ? this.codigo.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final IMTableSolicitacaoAgendamentoToMamografia other = (IMTableSolicitacaoAgendamentoToMamografia) obj;
        if (this.codigo != other.codigo && (this.codigo == null || !this.codigo.equals(other.codigo))) {
            return false;
        }
        return true;
    }
    
}
