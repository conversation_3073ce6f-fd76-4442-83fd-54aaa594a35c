package br.com.celk.im.vo.prontuario.basico;

import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Version;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "im_solic_agend_to_exame_apac")
public class IMTableSolicitacaoAgendamentoToExameApac implements Serializable {
    
    @Id
    @Column(name="cd_im_apac")
    @GeneratedValue(strategy=GenerationType.SEQUENCE, generator="seq_im_solic_agend_to_exame_apac_")
    @SequenceGenerator(name="seq_im_solic_agend_to_exame_apac_", sequenceName="seq_im_solic_agend_to_exame_apac", allocationSize = 1)
    private Long codigo;
    
    @Version
    @Column(name="version")
    private Long version;
    
    @Column(name="cd_solicitacao")
    private Long codigoSolicitacaoAgendamento;
    
    @Column(name="cd_exame_apac")
    private Long codigoExameApac;

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public Long getCodigoSolicitacaoAgendamento() {
        return codigoSolicitacaoAgendamento;
    }

    public void setCodigoSolicitacaoAgendamento(Long codigoSolicitacaoAgendamento) {
        this.codigoSolicitacaoAgendamento = codigoSolicitacaoAgendamento;
    }

    public Long getCodigoExameApac() {
        return codigoExameApac;
    }

    public void setCodigoExameApac(Long codigoExameApac) {
        this.codigoExameApac = codigoExameApac;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 97 * hash + (this.codigo != null ? this.codigo.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final IMTableSolicitacaoAgendamentoToExameApac other = (IMTableSolicitacaoAgendamentoToExameApac) obj;
        if (this.codigo != other.codigo && (this.codigo == null || !this.codigo.equals(other.codigo))) {
            return false;
        }
        return true;
    }
    
}
