/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.celk.im.integracoes.prontuario.basico.solicitacaoagendamento;

import br.com.celk.im.core.AbstractIntregratingModules;
import br.com.celk.im.core.IMFromTo;
import br.com.celk.im.core.IMTableConfiguration;
import br.com.celk.im.vo.prontuario.basico.IMTableSolicitacaoAgendamentoToEncaminhamentoAgendamento;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento;
import br.com.ksisolucoes.vo.prontuario.basico.EncaminhamentoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;

import java.util.Arrays;

/**
 *
 * <AUTHOR>
 */
@IMFromTo(from=SolicitacaoAgendamento.class, to=EncaminhamentoAgendamento.class)
@IMTableConfiguration(table=IMTableSolicitacaoAgendamentoToEncaminhamentoAgendamento.class, pathFrom="SolicitacaoAgendamento", pathTo="EncaminhamentoAgendamento")
public class IMSolicitacaoAgendamentoToEncaminhamentoAgendamento extends AbstractIntregratingModules<SolicitacaoAgendamento, EncaminhamentoAgendamento>{

    @Override
    public EncaminhamentoAgendamento insert(SolicitacaoAgendamento origin) throws DAOException, ValidacaoException {
        return null;
    }

    @Override
    public void update(SolicitacaoAgendamento origin, EncaminhamentoAgendamento destination) throws DAOException, ValidacaoException {
        if (SolicitacaoAgendamento.STATUS_AGENDADO.equals(origin.getStatus())
                || SolicitacaoAgendamento.STATUS_REGULACAO_AGENDADO.equals(origin.getStatus())
                || SolicitacaoAgendamento.STATUS_AGENDADO_FORA_REDE.equals(origin.getStatus())) {
            destination.setLocalAgendamento(origin.getUnidadeExecutante());
            destination.setProfissional(origin.getProfissional());
            destination.setDataAgendamento(origin.getDataAgendamento());
            if(origin.getDataFechamento() != null){
                AgendaGradeAtendimentoHorario agenda = (AgendaGradeAtendimentoHorario) getSession().createCriteria(AgendaGradeAtendimentoHorario.class)
                    .add(Restrictions.eq(AgendaGradeAtendimentoHorario.PROP_SOLICITACAO_AGENDAMENTO, origin))
                    .add(Restrictions.not(Restrictions.in(AgendaGradeAtendimentoHorario.PROP_STATUS, Arrays.asList(AgendaGradeAtendimentoHorario.STATUS_CANCELADO, AgendaGradeAtendimentoHorario.STATUS_REMANEJADO))))
                    .addOrder(Order.asc(AgendaGradeAtendimentoHorario.PROP_DATA_AGENDAMENTO))
                    .setMaxResults(1)
                    .uniqueResult();
                if(agenda != null && AgendaGradeAtendimentoHorario.STATUS_NAO_COMPARECEU.equals(agenda.getStatus())){
                    
                    destination.getEncaminhamento().setStatus(Encaminhamento.STATUS_NAO_COMPARECEU);
                }else{
                    destination.getEncaminhamento().setStatus(Encaminhamento.STATUS_CONCLUIDO);
                }
            }else{
                destination.getEncaminhamento().setStatus(Encaminhamento.STATUS_AGENDADO);
            }

        } else if (SolicitacaoAgendamento.STATUS_REGULACAO_NEGADO.equals(origin.getStatus())) {
                destination.setStatus(EncaminhamentoAgendamento.STATUS_CANCELADO);
                destination.getEncaminhamento().setStatus(Encaminhamento.STATUS_NAO_AUTORIZADO);
                destination.getEncaminhamento().setObservacao(origin.getObservacaoAutorizador());

        } else if (SolicitacaoAgendamento.STATUS_REGULACAO_DEVOLVIDO.equals(origin.getStatus())) {
            destination.setStatus(EncaminhamentoAgendamento.STATUS_CANCELADO);
            destination.getEncaminhamento().setObservacao(origin.getObservacaoAutorizador());
            
        } else if (SolicitacaoAgendamento.STATUS_FILA_ESPERA.equals(origin.getStatus())){
            destination.setStatus(EncaminhamentoAgendamento.STATUS_NORMAL);
            destination.getEncaminhamento().setStatus(Encaminhamento.STATUS_PENDENTE);
            
        } else if (SolicitacaoAgendamento.STATUS_REGULACAO_PENDENTE.equals(origin.getStatus())){
            destination.getEncaminhamento().setStatus(Encaminhamento.STATUS_PENDENTE);
            
        } else if (SolicitacaoAgendamento.STATUS_CANCELADO.equals(origin.getStatus()) && !Encaminhamento.STATUS_CANCELADO.equals(destination.getEncaminhamento().getStatus())){
            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_possivel_cancelar_solicitacao_relacionada_encaminhamento_entrar_contato_com_unidade_solicitante_cancela_encaminhamento"));
        } 
    }


    @Override
    public void delete(SolicitacaoAgendamento origin, EncaminhamentoAgendamento destination) throws DAOException, ValidacaoException {
        
    }
    
}
