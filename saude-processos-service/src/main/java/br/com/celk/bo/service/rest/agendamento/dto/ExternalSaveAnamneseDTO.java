package br.com.celk.bo.service.rest.agendamento.dto;

import java.io.Serializable;

public class ExternalSaveAnamneseDTO implements Serializable {

    private Long patientId;
    private Long careUnitId;
    private String anamneseObs;

    public Long getPatientId() {
        return patientId;
    }

    public void setPatientId(Long patientId) {
        this.patientId = patientId;
    }

    public Long getCareUnitId() {
        return careUnitId;
    }

    public void setCareUnitId(Long careUnitId) {
        this.careUnitId = careUnitId;
    }

    public String getAnamneseObs() {
        return anamneseObs;
    }

    public void setAnamneseObs(String anamneseObs) {
        this.anamneseObs = anamneseObs;
    }

    @Override
    public String toString() {
        return "ExternalSaveAnamneseDTO{" +
                ", patientId=" + patientId +
                ", careUnitId=" + careUnitId +
                ", anamneseObs='" + anamneseObs + '\'' +
                '}';
    }
}
