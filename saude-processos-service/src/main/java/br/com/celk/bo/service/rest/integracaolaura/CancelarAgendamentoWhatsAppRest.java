package br.com.celk.bo.service.rest.integracaolaura;

import br.com.celk.bo.service.rest.RestRetorno;
import br.com.celk.bo.service.rest.whatsapp.CancelamentoAgendamentoWhatsappRestDTO;
import br.com.celk.bo.service.rest.whatsapp.UpdateStatusMensagemWhatsappRestDTO;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.log.Loggable;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * REST Web Service
 *
 * <AUTHOR>
 */
@Path("/whatsapp/v1")
public class CancelarAgendamentoWhatsAppRest {

    @POST
    @Consumes({MediaType.APPLICATION_JSON})
    @Produces({MediaType.APPLICATION_JSON})
    @Path("/cancelamento")
    public Response registrarMonitoramento(CancelamentoAgendamentoWhatsappRestDTO restDto) {
        try {
            BOFactoryWicket.getBO(ComunicacaoFacade.class).cancelarAgendamentoViaWhatsApp(restDto);

            return Response.status(RestRetorno.DISPOSITIVO_REGISTRADO_COM_SUCESSO.getCodigo()).entity(RestRetorno.DADOS_SALVOS.getMensagem())
                    .type(MediaType.APPLICATION_JSON).build();
        } catch (SGKException ex) {
            Loggable.log.warn(ex.getMessage(), ex);
            return Response.notAcceptable(null).entity(ex.getMessage())
                    .header("codeError", RestRetorno.DADOS_INVALIDOS.getCodigo())
                    .header("codeMessage", RestRetorno.DADOS_INVALIDOS.getMensagem())
                    .header("codeDetail", ex.getMessage())
                    .build();
        }
    }

    @POST
    @Consumes({MediaType.APPLICATION_JSON})
    @Produces({MediaType.APPLICATION_JSON})
    @Path("/updateStatus")
    public Response updateStatus(UpdateStatusMensagemWhatsappRestDTO restDto) {
        try {
            BOFactoryWicket.getBO(ComunicacaoFacade.class).updateStatusMensagemWhatsApp(restDto);

            return Response.status(RestRetorno.DADOS_SALVOS.getCodigo()).entity(RestRetorno.DADOS_SALVOS.getMensagem())
                    .type(MediaType.APPLICATION_JSON).build();
        } catch (SGKException ex) {
            Loggable.log.warn(ex.getMessage(), ex);
            return Response.notAcceptable(null).entity(ex.getMessage())
                    .header("codeError", RestRetorno.DADOS_INVALIDOS.getCodigo())
                    .header("codeMessage", RestRetorno.DADOS_INVALIDOS.getMensagem())
                    .header("codeDetail", ex.getMessage())
                    .build();
        }
    }
}
