package br.com.celk.bo.service.rest.hospital;

import br.com.ksisolucoes.bo.prontuario.basico.restservice.dto.RegistroAgravoCelkSaudeDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionType;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.List;

/**
 * REST Web Service
 *
 * <AUTHOR>
 *
 */
@Path("/registro-agravo/")
public class RegistroAgravoHospitalRestServices {

    @POST
    @Path("integrar/")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response consultarRegistroAgravoHospital(List<RegistroAgravoCelkSaudeDTO> list) throws ValidacaoException, DAOException {
        //VSA-1242
//        List<IntegracaoRegistroAgravoCelkSaudeDTO> integracaoRegistroAgravoCelkSaudeDTOs = BOFactoryWicket.getBO(AtendimentoFacade.class).salvarDadosRegistroAgravoHospitalService(list);
        return Response.ok().build();
    }

    private String convertListToJSON(List list, Class typeList) {
        String json = null;
        if (CollectionUtils.isNotNullEmpty(list)) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                CollectionType collectionType = objectMapper.getTypeFactory().constructCollectionType(List.class, typeList);
                json = objectMapper.writerWithType(collectionType).writeValueAsString(list);
            } catch (JsonProcessingException e) {
                Loggable.log.error(e);
            }
        }
        return json;
    }
}
