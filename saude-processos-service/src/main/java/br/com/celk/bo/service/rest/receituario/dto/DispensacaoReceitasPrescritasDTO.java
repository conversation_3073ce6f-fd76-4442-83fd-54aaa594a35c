package br.com.celk.bo.service.rest.receituario.dto;

import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.Receituario;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DispensacaoReceitasPrescritasDTO {
    private Long idReceita;
    private String sexo;
    private String dataEmissao;
    private String paciente;
    private String cpfPaciente;
    private Date dataNascimentoPaciente;
    private String enderecoPaciente;
    private String medico;
    private String crm;
    private String enderecoMedico;
    private List<MedicamentoDTO> medicamentos = new ArrayList<>();

    public DispensacaoReceitasPrescritasDTO(Receituario receituario, List<MedicamentoDTO> dispensacaoMedicamentoItem, EnderecoUsuarioCadsus enderecoUsuarioCadsus, Profissional profissional) {
        this.dataEmissao = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(receituario.getDataCadastro());
        this.idReceita = receituario.getCodigo();
        this.paciente = receituario.getUsuarioCadsus().getNome();
        this.cpfPaciente = receituario.getUsuarioCadsus().getCpf();
        this.dataNascimentoPaciente = receituario.getUsuarioCadsus().getDataNascimento();
        this.enderecoPaciente = enderecoUsuarioCadsus.getEnderecoComplementoComCepFormatado();
        this.medico = profissional.getNome();
        this.crm = profissional.getRegistroFormatado();
        this.sexo = receituario.getUsuarioCadsus().getSexoFormatado();
        this.enderecoMedico = profissional.getRuaFormatada().isEmpty() ? "Endereço não cadastrado no sistema." : profissional.getRuaFormatada() + " - " + profissional.getCidade().getDescricao();
        this.medicamentos.addAll(dispensacaoMedicamentoItem);
    }

    public String getDataEmissao() {
        return dataEmissao;
    }

    public void setDataEmissao(String dataEmissao) {
        this.dataEmissao = dataEmissao;
    }

    public String getPaciente() {
        return paciente;
    }

    public void setPaciente(String paciente) {
        this.paciente = paciente;
    }

    public Date getDataNascimentoPaciente() {
        return dataNascimentoPaciente;
    }

    public void setDataNascimentoPaciente(Date dataNascimentoPaciente) {
        this.dataNascimentoPaciente = dataNascimentoPaciente;
    }

    public String getEnderecoPaciente() {
        return enderecoPaciente;
    }

    public void setEnderecoPaciente(String enderecoPaciente) {
        this.enderecoPaciente = enderecoPaciente;
    }

    public String getMedico() {
        return medico;
    }

    public void setMedico(String medico) {
        this.medico = medico;
    }

    public String getCrm() {
        return crm;
    }

    public void setCrm(String crm) {
        this.crm = crm;
    }

    public String getEnderecoMedico() {
        return enderecoMedico;
    }

    public void setEnderecoMedico(String enderecoMedico) {
        this.enderecoMedico = enderecoMedico;
    }

    public List<MedicamentoDTO> getMedicamentos() {
        return medicamentos;
    }

    public void setMedicamentos(List<MedicamentoDTO> medicamentos) {
        this.medicamentos = medicamentos;
    }

    public String getCpfPaciente() {
        return cpfPaciente;
    }

    public void setCpfPaciente(String cpfPaciente) {
        this.cpfPaciente = cpfPaciente;
    }

    public Long getIdReceita() {
        return idReceita;
    }

    public void setIdReceita(Long idReceita) {
        this.idReceita = idReceita;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }
}
