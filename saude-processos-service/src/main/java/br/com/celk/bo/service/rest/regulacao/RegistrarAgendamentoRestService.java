package br.com.celk.bo.service.rest.regulacao;

import br.com.celk.agendamento.AgendamentoHelper;
import br.com.celk.bo.service.rest.util.ResponseUtil;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTO;
import br.com.ksisolucoes.agendamento.regulacao.dto.RegistrarAgendamentoRequest;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoWQRException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.basico.Empresa;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

@Path("/registrar-agendamento")
public class RegistrarAgendamentoRestService {

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response registrarAgendamento(RegistrarAgendamentoRequest dtoReq) {
        try {
            Empresa unidadeOrigem = new Empresa(dtoReq.getUnidadeOrigemID());
            Empresa unidadeAgenda = new Empresa(dtoReq.getUnidadeAgendaID());
            if (dtoReq.getAgendaGradeHorariosIDs().length > 1) {
                dtoReq.setChaveValidacao(AgendamentoHelper.getChaveValidacao());
            }
            Long solicitacaoId = BOFactoryWicket.getBO(AgendamentoFacade.class).quebrarSolicitacaoAgendamentoWQR(dtoReq, dtoReq.getSolicitacaoAgendamentoID());
            dtoReq.setSolicitacaoAgendamentoID(solicitacaoId);
            AgendaGradeAtendimentoDTO dto = null;
            for (int i = 0; i < dtoReq.getAgendaGradeHorariosIDs().length; i++) {
                dto = BOFactoryWicket.getBO(AgendamentoFacade.class).montarRegistrarAgendamentoWQR(dtoReq, i);
                AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario = BOFactoryWicket.getBO(AgendamentoFacade.class).registrarAgendamento(dto, unidadeOrigem, unidadeAgenda);
                if (i == 0) dtoReq.setAgendaGradeAtendimentoPrincipal(agendaGradeAtendimentoHorario);
            }
            return Response.ok(dto == null ? 0 : dto.getSolicitacaoAgendamento().getCodigo()).build();
        } catch (ValidacaoWQRException ex) {
            Loggable.regulacao.error(ex.getMessage(), ex);
            return ResponseUtil.getRestError(ex.getCodigo(), Response.Status.NOT_ACCEPTABLE);
        } catch (Exception ex) {
            Loggable.regulacao.error(ex.getMessage(), ex);
            return ResponseUtil.getGenericError(ex);
        }
    }
}
