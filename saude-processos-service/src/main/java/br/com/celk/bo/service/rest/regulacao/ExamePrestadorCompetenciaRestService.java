package br.com.celk.bo.service.rest.regulacao;

import br.com.celk.bo.service.rest.regulacao.mapper.ExamePrestadorCompetenciaMapper;
import br.com.celk.bo.service.rest.util.ResponseUtil;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.agendamento.regulacao.dto.ExamePrestadorCompetenciaRequest;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorCompetencia;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.Objects;

@Path("/exame-prestador-competencia")
public class ExamePrestadorCompetenciaRestService {

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response examePrestadorCompetencia(ExamePrestadorCompetenciaRequest dto) {
        try {
            ExamePrestadorCompetencia entity = buscarExamePrestadorCompetencia(dto);
            if (Objects.isNull(entity)) {
                return ResponseUtil.getMessageNotFound();
            }
            return Response.ok(ExamePrestadorCompetenciaMapper.toResponse(entity)).build();
        } catch (Exception ex) {
            Loggable.regulacao.error(ex.getMessage(), ex);
            return ResponseUtil.getGenericError(ex);
        }
    }

    private ExamePrestadorCompetencia buscarExamePrestadorCompetencia(ExamePrestadorCompetenciaRequest dto) throws DAOException, ValidacaoException {
        return BOFactoryWicket.getBO(AgendamentoFacade.class).buscarExamePrestadorCompetencia(dto.getEmpresa(), dto.getTipoExame(), dto.getData());
    }
}
