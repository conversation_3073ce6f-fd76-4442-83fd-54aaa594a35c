package br.com.celk.bo.service.rest.receituario;

import br.com.celk.bo.service.rest.receituario.dto.DispencacaoBodyDTO;
import br.com.celk.bo.service.rest.receituario.servise.ReceituarioServise;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

import javax.inject.Inject;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

@Path("/v1/receitas")
public class ReceituarioRest {

    @Inject
    private ReceituarioServise receitasPrescritasService;

    @GET
    @Path("/consulta-prescricao/{token}/{id_receiturario_saude}")
    @Produces({MediaType.APPLICATION_JSON})
    public Response consultaPrescricao(@PathParam("token") String token,@PathParam("id_receiturario_saude") String idReceiturarioSaude) throws DAOException, ValidacaoException {
        if (token == null || token.isEmpty() || (idReceiturarioSaude == null || idReceiturarioSaude.isEmpty() )) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\":\"Token ou idReceiturarioSaude não pode ser nulo ou vazio.\"}")
                    .type(MediaType.APPLICATION_JSON)
                    .build();
        }

        return receitasPrescritasService.consultaPrescricao(token,Long.parseLong(idReceiturarioSaude));
    }


    @POST
    @Path("/dispensacao")
    @Produces({MediaType.APPLICATION_JSON})
    public Response dispensacaoPrescricaoPublica(DispencacaoBodyDTO dispencacao) throws DAOException, ValidacaoException {
        if (dispencacao.getToken() == null || dispencacao.getToken().isEmpty() ||
                (dispencacao.getIdReceiturarioSaude() == null || dispencacao.getIdReceiturarioSaude().isEmpty() )) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\":\"Token ou idReceiturarioSaude não pode ser nulo ou vazio.\"}")
                    .type(MediaType.APPLICATION_JSON)
                    .build();
        }

        return receitasPrescritasService.salvarDispensacao(dispencacao.getToken(),Long.parseLong(dispencacao.getIdReceiturarioSaude()));
    }



}
