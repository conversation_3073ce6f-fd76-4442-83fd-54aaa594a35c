package br.com.celk.bo.service.rest.atendimento.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Date;


@JsonIgnoreProperties(ignoreUnknown = true)
public class EvolucaoProntuarioDTO {

    private Date dataHistorico;
    private String descricao;
    private Date dataRegistro;
    private Date dataConclusao;

    private String cnesEmpresa;

    private String cnsProfissional;

    // Os demais campos do profissional são necessário apenas se o CNS do profissional não for localizado em nosso sistema, criando um cadastro básico
    private String nomeProfissional;
    private String cboProfissional;
    private String numeroRegistroProfissional;
    private String ufRegistroProfissional;
    private String codigoConselhoClasse;

    private Long codigoPaciente;

    // Os demais campos do paciente são necessários apenas se o código do paciente não for informado
    private String nomePaciente;
    private String sexoPaciente;
    private String cpfPaciente;
    private Date dataNascimentoPaciente;
    private Long racaPaciente;

    private String codigoSigtap;
    private String cid;

    private Long tipoAtendimento;
    private NotificacaoCompulsoriaDTO notificacaoCompulsoriaDTO;

    private Long nacionalidade;

    public Date getDataHistorico() {
        return dataHistorico;
    }

    public void setDataHistorico(Date dataHistorico) {
        this.dataHistorico = dataHistorico;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Date getDataConclusao() {
        return dataConclusao;
    }

    public void setDataConclusao(Date dataConclusao) {
        this.dataConclusao = dataConclusao;
    }

    public String getCnesEmpresa() {
        return cnesEmpresa;
    }

    public void setCnesEmpresa(String cnesEmpresa) {
        this.cnesEmpresa = cnesEmpresa;
    }

    public String getNomeProfissional() {
        return nomeProfissional;
    }

    public void setNomeProfissional(String nomeProfissional) {
        this.nomeProfissional = nomeProfissional;
    }

    public String getCnsProfissional() {
        return cnsProfissional;
    }

    public void setCnsProfissional(String cnsProfissional) {
        this.cnsProfissional = cnsProfissional;
    }

    public String getCboProfissional() {
        return cboProfissional;
    }

    public void setCboProfissional(String cboProfissional) {
        this.cboProfissional = cboProfissional;
    }

    public String getNumeroRegistroProfissional() {
        return numeroRegistroProfissional;
    }

    public void setNumeroRegistroProfissional(String numeroRegistroProfissional) {
        this.numeroRegistroProfissional = numeroRegistroProfissional;
    }

    public String getUfRegistroProfissional() {
        return ufRegistroProfissional;
    }

    public void setUfRegistroProfissional(String ufRegistroProfissional) {
        this.ufRegistroProfissional = ufRegistroProfissional;
    }

    public String getCodigoConselhoClasse() {
        return codigoConselhoClasse;
    }

    public void setCodigoConselhoClasse(String codigoConselhoClasse) {
        this.codigoConselhoClasse = codigoConselhoClasse;
    }

    public Long getCodigoPaciente() {
        return codigoPaciente;
    }

    public void setCodigoPaciente(Long codigoPaciente) {
        this.codigoPaciente = codigoPaciente;
    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    public String getSexoPaciente() {
        return sexoPaciente;
    }

    public void setSexoPaciente(String sexoPaciente) {
        this.sexoPaciente = sexoPaciente;
    }

    public String getCpfPaciente() {
        return cpfPaciente;
    }

    public void setCpfPaciente(String cpfPaciente) {
        this.cpfPaciente = cpfPaciente;
    }

    public Date getDataNascimentoPaciente() {
        return dataNascimentoPaciente;
    }

    public void setDataNascimentoPaciente(Date dataNascimentoPaciente) {
        this.dataNascimentoPaciente = dataNascimentoPaciente;
    }

    public Long getRacaPaciente() {
        return racaPaciente;
    }

    public void setRacaPaciente(Long racaPaciente) {
        this.racaPaciente = racaPaciente;
    }

    public String getCodigoSigtap() {
        return codigoSigtap;
    }

    public void setCodigoSigtap(String codigoSigtap) {
        this.codigoSigtap = codigoSigtap;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public Long getTipoAtendimento() {
        return tipoAtendimento;
    }

    public void setTipoAtendimento(Long tipoAtendimento) {
        this.tipoAtendimento = tipoAtendimento;
    }

    public NotificacaoCompulsoriaDTO getNotificacaoCompulsoriaDTO() {
        return notificacaoCompulsoriaDTO;
    }

    public void setNotificacaoCompulsoriaDTO(NotificacaoCompulsoriaDTO notificacaoCompulsoriaDTO) {
        this.notificacaoCompulsoriaDTO = notificacaoCompulsoriaDTO;
    }

    public Long getNacionalidade() {
        return nacionalidade;
    }

    public void setNacionalidade(Long nacionalidade) {
        this.nacionalidade = nacionalidade;
    }

    @Override
    public String toString() {
        return "EvolucaoProntuarioDTO{" +
                "dataHistorico=" + dataHistorico +
                ", descricao='" + descricao + '\'' +
                ", dataRegistro=" + dataRegistro +
                ", dataConclusao=" + dataConclusao +
                ", cnesEmpresa='" + cnesEmpresa + '\'' +
                ", cnsProfissional='" + cnsProfissional + '\'' +
                ", nomeProfissional='" + nomeProfissional + '\'' +
                ", cboProfissional='" + cboProfissional + '\'' +
                ", numeroRegistroProfissional='" + numeroRegistroProfissional + '\'' +
                ", ufRegistroProfissional='" + ufRegistroProfissional + '\'' +
                ", codigoConselhoClasse='" + codigoConselhoClasse + '\'' +
                ", codigoPaciente=" + codigoPaciente +
                ", nomePaciente='" + nomePaciente + '\'' +
                ", sexoPaciente='" + sexoPaciente + '\'' +
                ", cpfPaciente='" + cpfPaciente + '\'' +
                ", dataNascimentoPaciente=" + dataNascimentoPaciente +
                ", racaPaciente=" + racaPaciente +
                ", codigoSigtap='" + codigoSigtap + '\'' +
                ", cid='" + cid + '\'' +
                ", tipoAtendimento=" + tipoAtendimento +
                ", notificacaoCompulsoriaDTO=" + notificacaoCompulsoriaDTO +
                ", nacionalidade=" + nacionalidade +
                '}';
    }
}
