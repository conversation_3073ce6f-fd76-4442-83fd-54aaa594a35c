package br.com.celk.bo.service.rest.agendamento.builder;

import br.com.celk.bo.service.rest.agendamento.dto.ClkError;
import br.com.celk.bo.service.rest.agendamento.dto.ExternalSchedulingResponseDTO;

import java.util.List;

public class ExternalSchedulingResponseBuilder {

    private ExternalSchedulingResponseDTO response;

    public ExternalSchedulingResponseBuilder builder() {
        this.response = new ExternalSchedulingResponseDTO();
        return this;
    }

    public ExternalSchedulingResponseBuilder setCode(Long code) {
        this.response.setCode(code);
        return this;
    }

    public ExternalSchedulingResponseBuilder setSchedulingId(Long schedulingId) {
        this.response.setSchedulingId(schedulingId);
        return this;
    }

    public ExternalSchedulingResponseBuilder setCorrelationId(Long correlationId) {
        this.response.setCorrelationId(correlationId);
        return this;
    }

    public ExternalSchedulingResponseBuilder setMessage(String message) {
        this.response.setMessage(message);
        return this;
    }

    public ExternalSchedulingResponseBuilder setErrors(List<ClkError> errors) {
        this.response.setErrors(errors);
        return this;
    }

    public ExternalSchedulingResponseDTO build() {
        return this.response;
    }
}
