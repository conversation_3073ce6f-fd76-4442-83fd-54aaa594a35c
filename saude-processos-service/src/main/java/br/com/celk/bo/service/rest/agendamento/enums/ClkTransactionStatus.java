package br.com.celk.bo.service.rest.agendamento.enums;


public enum ClkTransactionStatus {

    SUCCESS(2000L, "Transaction saved successfully"),
    FAILED(4000L, "Transaction saving failed");

    private final Long code;
    private final String message;

    ClkTransactionStatus(Long code, String message) {
        this.code = code;
        this.message = message;
    }

    public Long getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
