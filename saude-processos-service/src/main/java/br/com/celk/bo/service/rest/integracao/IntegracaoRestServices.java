package br.com.celk.bo.service.rest.integracao;

import br.com.celk.bo.service.rest.RestRetorno;
import br.com.celk.bo.service.rest.usuario.QueryUsuariosRestDTOParam;
import br.com.celk.bo.service.rest.usuario.QueryUsuariosRestDTOParam;
import br.com.celk.bo.service.rest.usuario.UsuarioRestDTO;
import br.com.celk.bo.service.rest.usuario.UsuarioRestDTO;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.util.List;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 *
 * <AUTHOR>
 */
@Path("/ext/{version}")
public class IntegracaoRestServices {

    @GET
    @Path("/usuario")
    @Produces({MediaType.APPLICATION_JSON})
    public Response listarUsuarios() {
        try {
            List<UsuarioRestDTO> retorno;
            retorno = BOFactoryWicket.getBO(UsuarioFacade.class).consultarUsuarios(null);

            return Response.ok(retorno).build();
        } catch (SGKException ex) {
            Loggable.log.warn(ex.getMessage(), ex);
            return Response.notAcceptable(null).entity(ex.getMessage())
                    .header("codeMessage", RestRetorno.ERRO_NAO_CATALOGADO.getMensagem())
                    .header("codeDetail", ex.getMessage())
                    .build();
        }
    }
    
    @GET
    @Produces({MediaType.APPLICATION_JSON})
    @Path("/usuario/{id}")
    public Response consultarUsuario(@PathParam("id") final String id) {
        try {
            QueryUsuariosRestDTOParam param = new QueryUsuariosRestDTOParam();
            param.setCodigo(Long.parseLong(id));
            
            Object resultado = null;
            List<UsuarioRestDTO> retorno = BOFactoryWicket.getBO(UsuarioFacade.class).consultarUsuarios(param);
            if(retorno.size() > 0){
                resultado = retorno.get(0);
                return Response.ok(resultado).build();
            }else{
                throw new ValidacaoException("Usuário não encontrado");
            }
        } catch (SGKException ex) {
            Loggable.log.warn(ex.getMessage(), ex);
            return Response.notAcceptable(null).entity(ex.getMessage())
                    .header("codeError", RestRetorno.ERRO_NAO_CATALOGADO.getCodigo())
                    .header("codeMessage", RestRetorno.ERRO_NAO_CATALOGADO.getMensagem())
                    .header("codeDetail", ex.getMessage())
                    .build();
        } catch (Throwable ex) {
            String erro = ex.getMessage();
            while(ex.getCause() != null){
                if(! (ex.getCause() instanceof DAOException)){
                    erro += ex.getCause().getMessage()+", ";
                }
                ex = ex.getCause();
            }
            
            Loggable.log.error(ex.getMessage(), ex);
            return Response.notAcceptable(null).entity(erro)
                    .header("codeError", RestRetorno.ERRO_NAO_CATALOGADO.getCodigo())
                    .header("codeMessage", RestRetorno.ERRO_NAO_CATALOGADO.getMensagem())
                    .header("codeDetail", erro)
                    .build();
        }
    }
    
    @POST
    @Consumes({MediaType.APPLICATION_JSON})
    @Produces({MediaType.APPLICATION_JSON})
    @Path("/usuario")
    public Response cadastrarUsuario(UsuarioRestDTO restDto) {
        try{
            Long codigoNovoUsuario = BOFactoryWicket.getBO(UsuarioFacade.class).cadastrarUsuarioIntegracao(restDto);

            return Response.created(null).entity(codigoNovoUsuario).build();

        } catch (SGKException ex) {
            Loggable.log.warn(ex.getMessage(), ex);
            return Response.notAcceptable(null).entity(ex.getMessage())
                    .header("codeError", RestRetorno.ERRO_NAO_CATALOGADO.getCodigo())
                    .header("codeMessage", RestRetorno.ERRO_NAO_CATALOGADO.getMensagem())
                    .header("codeDetail", ex.getMessage())
                    .build();
        } catch (Throwable ex) {
            String erro = "";
            while(ex.getCause() != null){
                if(! (ex.getCause() instanceof DAOException)){
                    erro += ex.getCause().getMessage()+", ";
                }
                ex = ex.getCause();
            }
            
            Loggable.log.error(ex.getMessage(), ex);
            return Response.notAcceptable(null).entity(erro)
                    .header("codeError", RestRetorno.ERRO_NAO_CATALOGADO.getCodigo())
                    .header("codeMessage", RestRetorno.ERRO_NAO_CATALOGADO.getMensagem())
                    .header("codeDetail", erro)
                    .build();
        }
    }

    @PUT
    @Consumes({MediaType.APPLICATION_JSON})
    @Produces({MediaType.APPLICATION_JSON})
    @Path("/usuario/{id}")
    public Response editarUsuario(UsuarioRestDTO restDto, @PathParam("id") final long id) {
        try{
            BOFactoryWicket.getBO(UsuarioFacade.class).editarUsuarioIntegracao(restDto, id);

            return Response.ok().entity(null).build();
        } catch (SGKException ex) {
            Loggable.log.warn(ex.getMessage(), ex);
            return Response.notAcceptable(null).entity(ex.getMessage())
                    .header("codeError", RestRetorno.ERRO_NAO_CATALOGADO.getCodigo())
                    .header("codeMessage", RestRetorno.ERRO_NAO_CATALOGADO.getMensagem())
                    .header("codeDetail", ex.getMessage())
                    .build();
        } catch (Throwable ex) {
            String erro = "";
            while(ex.getCause() != null){
                if(! (ex.getCause() instanceof DAOException)){
                    erro += ex.getCause().getMessage()+", ";
                }
                ex = ex.getCause();
            }
            
            Loggable.log.error(ex.getMessage(), ex);
            return Response.notAcceptable(null).entity(erro)
                    .header("codeError", RestRetorno.ERRO_NAO_CATALOGADO.getCodigo())
                    .header("codeMessage", RestRetorno.ERRO_NAO_CATALOGADO.getMensagem())
                    .header("codeDetail", erro)
                    .build();
        }
    }
}
