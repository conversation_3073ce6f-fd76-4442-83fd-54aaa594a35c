package br.com.celk.bo.service.rest.agendamento.service;

import br.com.ksisolucoes.vo.basico.Empresa;

import javax.ejb.Stateless;
import javax.inject.Inject;

@Stateless
public class UnidadeSaudeService {

    @Inject
    LoadManagerService loadManagerService;

    public Empresa findEmpresaByCodigo(Long codigoEmpresa) {
        return loadManagerService.getInstance(Empresa.class)
                .addProperties(loadManagerService.getHqlProperties(Empresa.class))
                .setId(codigoEmpresa)
                .startLeitura()
                .getVO();
    }
}
