package br.com.celk.bo.service.rest.atendimento;

import br.com.celk.bo.service.rest.RestRetorno;
import br.com.celk.bo.service.rest.atendimento.dto.EvolucaoProntuarioDTO;
import br.com.celk.bo.service.rest.atendimento.util.AtendimentoProntuarioRestUtil;
import br.com.celk.bo.service.rest.historicopaciente.AtendimentoProntuarioRestDTO;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ReceituarioFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.*;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.List;

/**
 * REST Web Service
 *
 * <AUTHOR>
 */
@Path("/atendimentoProntuario/{version}/{clientId}")
public class AtendimentoProntuarioRestServices {

    @GET
    @Path("/consultar/{revisao}/{cns}")
    @Produces({MediaType.APPLICATION_JSON})
    public Response consultarAtendimentoProntuario(@PathParam("cns") Long cns, @PathParam("revisao") Long revisao) {
        return carregarProntuarios(cns, null, revisao);
    }

    @GET
    @Path("/consultarProntuarios/{revisao}")
    @Produces({MediaType.APPLICATION_JSON})
    public Response consultarAtendimentoProntuarioByCpfOrCns(@QueryParam("cns") String cns, @QueryParam("cpf") String cpf, @PathParam("revisao") Long revisao) {
        if ((cpf == null || cpf.isEmpty()) && (cns == null || cns.isEmpty())) {
            return respostaErro("CPF ou CNS obrigatórios.");
        }

        Long cnsLong = null;
        if (cns != null && !cns.isEmpty()) {
            cnsLong = Long.parseLong(cns);
        }

        return carregarProntuarios(cnsLong, cpf, revisao);
    }

    @POST
    @Path("salvar")
    @Produces({MediaType.APPLICATION_JSON})
    public Response salvarProntuario(EvolucaoProntuarioDTO evolucaoProntuario) {
        try {
            AtendimentoProntuarioRestUtil util = new AtendimentoProntuarioRestUtil(evolucaoProntuario);

            Empresa empresa = carregarEmpresa(util);
            if (empresa == null) {
                return respostaErro("Empresa não localizada.");
            }

            NaturezaProcuraTipoAtendimento tipoAtendimento = carregarTipoAtendimento(evolucaoProntuario, util);
            if (tipoAtendimento == null) {
                return respostaErro("Natureza de tipo de procedimento ou tipo de atendimento não cadastrado.");
            }

            Cidade cidade = empresa.getCidade();
            TabelaCbo cbo = util.carregarTabelaCbo();
            if (cbo == null) {
                return respostaErro("CBO inválido ou não cadastrado.");
            }

            Atendimento atendimento = prepararAtendimento(util, empresa, cidade, evolucaoProntuario, cbo);
            EvolucaoProntuario prontuario = prepararProntuario(util, atendimento, evolucaoProntuario, cbo);

            saveAtendimentoProntuario(cbo, atendimento.getProfissional(), atendimento, evolucaoProntuario, empresa, atendimento.getUsuarioCadsus());

            EvolucaoProntuario retorno = BOFactory.getBO(ReceituarioFacade.class).salvarEvolucaoProntuario(prontuario);

            if (evolucaoProntuario.getNotificacaoCompulsoriaDTO() != null

                    && ((evolucaoProntuario.getNotificacaoCompulsoriaDTO().getCid() != null && !evolucaoProntuario.getNotificacaoCompulsoriaDTO().getCid().isEmpty())
                    || ((evolucaoProntuario.getCid() != null && !evolucaoProntuario.getCid().isEmpty())))

                    && (util.isCidCompulsorio(evolucaoProntuario.getNotificacaoCompulsoriaDTO().getCid()) || util.isCidCompulsorio(evolucaoProntuario.getCid()))) {

                RegistroAgravo registroAgravo = util.getRegistroAgravoPendente(atendimento, evolucaoProntuario);

                if (registroAgravo == null) {
                    if (evolucaoProntuario.getNotificacaoCompulsoriaDTO().getDataPrimeirosSintomas() == null) {
                        return respostaErro(Bundle.getStringApplication("msgDataPrimeirosSintomasObrigatoriaParaCidCompulsorio"));
                    }

                    if (evolucaoProntuario.getNotificacaoCompulsoriaDTO().getTelefone() == null
                            || evolucaoProntuario.getNotificacaoCompulsoriaDTO().getTelefone().isEmpty()) {
                        return respostaErro(Bundle.getStringApplication("msgTelefoneObrigatorioParaCidCompulsorio"));
                    }

                    if (evolucaoProntuario.getNotificacaoCompulsoriaDTO().getEscolaridade() == null) {
                        return respostaErro(Bundle.getStringApplication("msgEscolaridadeObrigatoriaParaCidCompulsorio"));
                    }

                    setRegistroAgravo(atendimento, evolucaoProntuario, util);
                }
            }
            return Response.ok().entity(retorno.getCodigo()).build();
        } catch (Exception e) {
            Loggable.log.error("Erro ao salvar prontuário: ", e);
            return respostaErro("Erro ao salvar prontuário: " + e.getMessage());
        }
    }

    public void setRegistroAgravo(Atendimento atendimento, EvolucaoProntuarioDTO evolucaoProntuario, AtendimentoProntuarioRestUtil util) throws DAOException, ValidacaoException {
        RegistroAgravo registroAgravo = new RegistroAgravo();
        Cid cid;
        if (evolucaoProntuario.getCid() != null) {
            cid = util.carregarCid(evolucaoProntuario.getCid());
        } else {
            cid = util.carregarCid(evolucaoProntuario.getNotificacaoCompulsoriaDTO().getCid());
        }

        registroAgravo.setUsuarioCadsus(atendimento.getUsuarioCadsus());
        registroAgravo.setCid(cid);
        registroAgravo.setDataRegistro(DataUtil.getDataAtual());
        registroAgravo.setProfissional(atendimento.getProfissional());
        registroAgravo.setEmpresa(atendimento.getEmpresa());
        registroAgravo.setAtendimento(atendimento);
        registroAgravo.setDataPrimeirosSintomas(evolucaoProntuario.getNotificacaoCompulsoriaDTO().getDataPrimeirosSintomas());
        registroAgravo.setEscolaridade(evolucaoProntuario.getNotificacaoCompulsoriaDTO().getEscolaridade());
        atendimento.getUsuarioCadsus().setTelefone(evolucaoProntuario.getNotificacaoCompulsoriaDTO().getTelefone());

        UsuarioCadsusDado usuarioCadsusDado = util.getUsuarioCadsusDado(atendimento);

        if (usuarioCadsusDado != null) {
            registroAgravo.setGestante(usuarioCadsusDado.getGestante());
        } else {
            registroAgravo.setGestante(RepositoryComponentDefault.NAO_LONG);
        }

        UsuarioCadsusEndereco enderecoUsuarioCadsus = util.carregarEnderecoUsuarioCadsus(atendimento.getUsuarioCadsus());

        BOFactory.getBO(UsuarioCadsusFacade.class)
                .atualizarEnderecoUsuarioCadsusRegistroAgravo(registroAgravo, atendimento.getUsuarioCadsus(), enderecoUsuarioCadsus, false, null);
    }


    private Empresa carregarEmpresa(AtendimentoProntuarioRestUtil util) {
        return util.carregarEmpresa();
    }

    private NaturezaProcuraTipoAtendimento carregarTipoAtendimento(EvolucaoProntuarioDTO evolucaoProntuario, AtendimentoProntuarioRestUtil util) throws DAOException, ValidacaoException {
        checkField(evolucaoProntuario.getTipoAtendimento(), "tipoAtendimento");
        return util.carregarTipoAtendimento();
    }

    private Response respostaErro(String mensagem) {
        return Response.notAcceptable(null)
                .entity(mensagem)
                .header("codeMessage", RestRetorno.ERRO_NAO_CATALOGADO.getMensagem())
                .build();
    }

    private void checkField(Object object, String field) throws ValidacaoException {
        if (object == null || "".equals(object.toString())) throw new ValidacaoException("Campo " + field + " é obrigatório");
    }

    private Atendimento prepararAtendimento(AtendimentoProntuarioRestUtil util, Empresa empresa, Cidade cidade, EvolucaoProntuarioDTO evolucaoProntuario, TabelaCbo cbo) throws Exception {
        Atendimento atendimento = new Atendimento();
        atendimento.setEmpresa(empresa);

        UsuarioCadsus usuarioCadsus = carregarOuCadastrarUsuarioCadsus(util, evolucaoProntuario, cidade);
        atendimento.setUsuarioCadsus(usuarioCadsus);

        Profissional profissional = carregarOuCadastrarProfissional(util, evolucaoProntuario, cidade);
        atendimento.setProfissional(profissional);

        Usuario usuario = LoadManager.getInstance(Usuario.class).setId(Usuario.USUARIO_ADMINISTRADOR).startLeitura().getVO();
        ProcedimentoCompetencia procedimentoCompetencia = util.carregarProcedimentoSigtap();

        atendimento.setClassificacaoRisco(util.carregarClassificacaoRiscoPadrao());
        atendimento.setTabelaCbo(cbo);
        if (evolucaoProntuario.getCid() != null) {
            atendimento.setCidPrincipal(util.carregarCid(evolucaoProntuario.getCid()));
        }
        atendimento.setNaturezaProcuraTipoAtendimento(util.carregarTipoAtendimento());
        atendimento.setDataAtendimento(evolucaoProntuario.getDataRegistro());
        atendimento.setStatus(Atendimento.STATUS_FINALIZADO);
        atendimento.setProcedimentoCompetencia(procedimentoCompetencia);
        atendimento.setUsuario(usuario);
        return BOFactory.save(atendimento);
    }

    private UsuarioCadsus carregarOuCadastrarUsuarioCadsus(AtendimentoProntuarioRestUtil util, EvolucaoProntuarioDTO evolucaoProntuarioDTO, Cidade cidade) throws Exception {
        UsuarioCadsus usuarioCadsus = null;
        if (evolucaoProntuarioDTO.getCodigoPaciente() != null)
            usuarioCadsus = util.carregarUsuarioCadsus();

        if (usuarioCadsus == null) {
            usuarioCadsus = new UsuarioCadsus();
            usuarioCadsus.setCidadeNascimento(cidade);

            checkField(evolucaoProntuarioDTO.getNomePaciente(), "nomePaciente");
            usuarioCadsus.setNome(evolucaoProntuarioDTO.getNomePaciente());

            checkField(evolucaoProntuarioDTO.getDataNascimentoPaciente(), "dataNascimentoPaciente");
            usuarioCadsus.setDataNascimento(evolucaoProntuarioDTO.getDataNascimentoPaciente());

            checkField(evolucaoProntuarioDTO.getSexoPaciente(), "sexoPaciente");
            usuarioCadsus.setSexo(evolucaoProntuarioDTO.getSexoPaciente());

            if (!UsuarioCadsus.Nacionalidade.ESTRANGEIRO.value().equals(evolucaoProntuarioDTO.getNacionalidade())) {
                checkField(evolucaoProntuarioDTO.getCpfPaciente(), "cpfPaciente");
            }
            usuarioCadsus.setCpf(evolucaoProntuarioDTO.getCpfPaciente());
            usuarioCadsus.setNacionalidade(evolucaoProntuarioDTO.getNacionalidade());

            checkField(evolucaoProntuarioDTO.getRacaPaciente(), "racaPaciente");
            Raca.TipoRaca tipoRaca = Raca.TipoRaca.valueOf(evolucaoProntuarioDTO.getRacaPaciente());
            if (tipoRaca == null) {
                throw new ValidacaoException("Raça do Paciente precisa ter um código válido entre 1 e 5: " + evolucaoProntuarioDTO.getRacaPaciente());
            }
            usuarioCadsus.setRaca(new Raca(evolucaoProntuarioDTO.getRacaPaciente()));

            usuarioCadsus.setSituacao(UsuarioCadsus.SITUACAO_ATIVO);
            usuarioCadsus = BOFactory.save(usuarioCadsus);
        }
        return usuarioCadsus;
    }

    private Profissional carregarOuCadastrarProfissional(AtendimentoProntuarioRestUtil util, EvolucaoProntuarioDTO evolucaoProntuario, Cidade cidade) throws Exception {
        Profissional profissional = null;
        if (evolucaoProntuario.getCnsProfissional() != null)
            profissional = util.carregarProfissional();

        if (profissional == null) {
            profissional = new Profissional();

            checkField(evolucaoProntuario.getNomeProfissional(), "nomeProfissional");
            profissional.setNome(evolucaoProntuario.getNomeProfissional());

            checkField(evolucaoProntuario.getCnsProfissional(), "cnsProfissional");
            profissional.setCodigoCns(evolucaoProntuario.getCnsProfissional());

            checkField(evolucaoProntuario.getUfRegistroProfissional(), "ufRegistroProfissional");
            profissional.setUnidadeFederacaoConselhoRegistro(evolucaoProntuario.getUfRegistroProfissional());

            checkField(evolucaoProntuario.getNumeroRegistroProfissional(), "numeroRegistroProfissional");
            profissional.setNumeroRegistro(evolucaoProntuario.getNumeroRegistroProfissional());

            profissional.setConselhoClasse(util.carregarConselhoClasse());
            if (profissional.getConselhoClasse() == null) {
                throw new ValidacaoException("Conselho de classe não localizado");
            }

            profissional.setCidadeNascimento(cidade);
            profissional = BOFactory.getBO(CadastroFacade.class).save(profissional);
        }
        return profissional;
    }

    private EvolucaoProntuario prepararProntuario(AtendimentoProntuarioRestUtil util, Atendimento atendimento, EvolucaoProntuarioDTO evolucaoProntuario, TabelaCbo cbo) throws Exception {
        EvolucaoProntuario prontuario = new EvolucaoProntuario();
        prontuario.setUsuario(atendimento.getUsuario());

        Cid cid = null;
        if (evolucaoProntuario.getCid() != null) {
            cid = util.carregarCid(evolucaoProntuario.getCid());
        } else if (evolucaoProntuario.getNotificacaoCompulsoriaDTO() != null) {
            cid = util.carregarCid(evolucaoProntuario.getNotificacaoCompulsoriaDTO().getCid());
        }

        checkField(evolucaoProntuario.getDataConclusao(), "dataConclusao");
        prontuario.setDataConclusao(evolucaoProntuario.getDataConclusao());
        prontuario.setDataHistorico(evolucaoProntuario.getDataHistorico());
        prontuario.setDataRegistro(Coalesce.asDate(evolucaoProntuario.getDataRegistro(), DataUtil.getDataAtual()));
        prontuario.setTabelaCbo(cbo);

        checkField(evolucaoProntuario.getDescricao(), "descricao");
        if (cid != null) {
            prontuario.setDescricao(evolucaoProntuario.getDescricao() + "<br/>" + "CID: " + cid.getDescricaoFormatado());
        } else {
            prontuario.setDescricao(evolucaoProntuario.getDescricao());
        }
        prontuario.setProfissional(atendimento.getProfissional());
        prontuario.setAtendimento(atendimento);
        return prontuario;
    }

    private void saveAtendimentoProntuario(TabelaCbo cbo, Profissional profissional, Atendimento a, EvolucaoProntuarioDTO evolucaoProntuario, Empresa empresa, UsuarioCadsus usuarioCadsus) throws DAOException, ValidacaoException {
        AtendimentoProntuario atendimentoProntuario = new AtendimentoProntuario();
        atendimentoProntuario.setTabelaCbo(cbo);
        atendimentoProntuario.setProfissional(profissional);
        atendimentoProntuario.setAtendimento(a);
        atendimentoProntuario.setData(evolucaoProntuario.getDataHistorico());
        atendimentoProntuario.setDescricao(evolucaoProntuario.getDescricao());
        atendimentoProntuario.setEmpresa(empresa);
        atendimentoProntuario.setTipoRegistro(AtendimentoProntuario.TipoRegistro.TELEMEDICINA.value());
        atendimentoProntuario.setUsuarioCadsus(usuarioCadsus);
        atendimentoProntuario.setNomeProfissional(profissional.getNome());
        atendimentoProntuario.setDescricaoTipoAtendimento(AtendimentoProntuario.TipoRegistro.TELEMEDICINA.descricao());
        BOFactory.save(atendimentoProntuario);
    }

    private Response carregarProntuarios(Long cns, String cpf, Long revisao) {
        try {
            List<AtendimentoProntuarioRestDTO> lstAtendimentoProntuario = new ArrayList<>();

            UsuarioCadsusCns usuarioCadsusCnsValidacao;
            LoadManager lm = LoadManager.getInstance(UsuarioCadsusCns.class)
                    .addProperties(new HQLProperties(UsuarioCadsusCns.class).getProperties())
                    .addProperty(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_EXCLUIDO), BuilderQueryCustom.QueryParameter.DIFERENTE, RepositoryComponentDefault.EXCLUIDO, HQLHelper.NOT_RESOLVE_TYPE, RepositoryComponentDefault.NAO_EXCLUIDO));

            if (cns != null && !"".equals(cns))
                lm.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_NUMERO_CARTAO), Coalesce.asLong(cns)));

            if (cpf != null && !"".equals(cpf))
                lm.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CPF), cpf));

            usuarioCadsusCnsValidacao = lm.start().getVO();
            if (usuarioCadsusCnsValidacao != null) {
                lstAtendimentoProntuario = BOFactoryWicket.getBO(AtendimentoFacade.class).consultarAtendimentoProntuarioService(usuarioCadsusCnsValidacao.getUsuarioCadsus().getCodigo(), revisao);
            }
            if (CollectionUtils.isAllEmpty(lstAtendimentoProntuario)) {
                return Response.noContent().build();
            }
            return Response.ok(lstAtendimentoProntuario).build();
        } catch (SGKException ex) {
            Loggable.log.warn(ex.getMessage(), ex);
            return Response.notAcceptable(null).entity(ex.getMessage())
                    .header("codeMessage", RestRetorno.ERRO_NAO_CATALOGADO.getMensagem())
                    .header("codeDetail", ex.getMessage())
                    .build();
        }
    }
}
