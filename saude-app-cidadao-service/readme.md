# API para uso do aplicativo do cidadão

Está api é utilizada para o aplicativo do cidadão: 
https://gitlab.com/celksistemas/app-cidadao.
O aplicativo realiza consultas na api. Além do 
aplicativo, a API é utilizada pelos seguintes microserviços:

* Agendamento (https://gitlab.com/celksistemas/sma)
* Fila (https://gitlab.com/celksistemas/sqr)

Para autenticação, a api utiliza o serviço do
keycloak. O token gerado pelo keycloak é validado
através do uso da chave pública do 
keycloack.

## Configuração

Para configurar a api para uso do aplicativo,
é necessário ajustar alguns parametros.
Nos parametros gem ficam configurações de url
de serviços dependentes: keycloack e microserviço
de agendamento. Na tabela de parametros ficam
o client id e o client secret do saúde no keycloack.
Exemplo de configuração:
* Parametros gem:
    * URLComunicaçãoAuthCidadão = https://cidadao.apoio.celk.info/v1/api
    * URLKeycloackCidadao = https://keycloak.apoio.celk.info/auth/realms/celk-realm/protocol/openid-connect/token
* Tabela parametro
    * keycloack_client_id = ck_saude
    * keycloack_client_secret = fce8f8eb-71a1-4f2c-a38b-e6270b14e118
    * keycloack_public_key = MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1aoJDACMj3I8N225DRLhUsiCXvwkaDwSSfv+uSHcgK4aIMZXkcfaKc2gfexBsF++tPZ1bU2VCXzrfdnpQ0Gl5pVAtxvXJzaPxJ7mIIWMdDMSfCIdfyJ8hjLL8xVQdL5Kk1E8zWH9oLRTVBrI1PMsCuZXt2gJzxqPRP5dpOW/dSNGpD42jHbC9Xyc52Rod/3qWZZK+RtCIhM+d+I1glEdQpLpDWIIJhQyGI++omWQVYGTCeLZWuejX/utddsCPtS3Xz9xqN//wb7kVUS+F60AfefucUU8OeUoo6cphRDuN7eedwE4+zoGF/ogClQN0IyRMMDLzUMoZC2l0VwpanwdyQIDAQAB
    
## Resources

Existem dois tipos de resources na aplicação. Os
públicos, onde qualquer aplicação pode fazer 
acesso. Eles são identificados path: 
'/v1/publico/'. Os demais resources são protegidos,
e utilizam autenticação de token. As regras de 
autenticação de resources estão em 
'InterceptorAuth.java'.

## Versão obrigatória

O app possui um recurso para validar a versão 
obrigatória do saúde. Para incrementar a versão,
basta alterar o endpoint na classe 
'VersionResource.java'.
