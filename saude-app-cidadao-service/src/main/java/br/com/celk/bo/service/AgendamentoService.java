package br.com.celk.bo.service;

import br.com.celk.agendamento.dto.CancelamentoAgendamentoDTO;
import br.com.celk.bo.builder.SchedulingResponseBuilder;
import br.com.celk.bo.dto.*;
import br.com.celk.bo.dto.transaction.enums.ClkTransactionStatus;
import br.com.celk.bo.mapper.ScheduleDTOMapper;
import br.com.celk.bo.mapper.SchedulingMapper;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTO;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.server.HibernateSessionFactory;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeHorario;
import br.com.ksisolucoes.vo.agendamento.TipoAtendimentoAgenda;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import org.hibernate.*;

import javax.ejb.Stateless;
import javax.inject.Inject;
import javax.ws.rs.QueryParam;
import java.util.*;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static br.com.ksisolucoes.vo.cadsus.base.BaseUsuarioCadsus.*;
import static ch.lambdaj.Lambda.on;
import static org.hibernate.criterion.CriteriaSpecification.ALIAS_TO_ENTITY_MAP;

@Stateless
public class AgendamentoService {

    @Inject
    private AgendaService agendaService;
    @Inject
    private UnidadeSaudeService unidadeSaudeService;
    @Inject
    private PacienteService pacienteService;
    @Inject
    private BOFactoryService boFactoryService;
    @Inject
    private NotificacaoService notificacaoService;

    static final String GRID_SCHEDULE_SERVICE_WITH_INVALID_APPOINTMENT_TYPE = "Grid Schedule Service with invalid appointmentType";
    static final String SCHEDULING_WITH_INVALID_APPOINTMENT_TYPE = "Scheduling with invalid appointmentType";
    static final String SCHEDULING_ID = "schedulingId";
    static final String GRID_SCHEDULE_SERVICE_ID = "gridScheduleServiceId";
    static final String SCHEDULING_ALREADY_REGISTERED = "Scheduling already registered";
    static final String A_AGENDA_SELECIONADA_NAO_EXISTE = "A agenda selecionada não existe";
    static final String O_HORARIO_SELECIONADO_NAO_EXISTE = "O horário selecionado não existe ou indisponível";
    static final String GRID_SCHEDULE_TIME_INFORMED_COULD_NOT_BE_FOUND = "Grid Schedule Time informed could not be found";
    static final String MSG_ADDING_DATE_PREVIOUS_TO_NOW_NOT_ALLOWED = "Adding a time previous to the current one is not allowed";
    static final String MSG_PATIENT_AGE_INCOMPATIBLE_WITH_SCHEDULE = "This patient doesn't meet the schedule age requirements";
    static final String MSG_PATIENT_SEX_INCOMPATIBLE_WITH_SCHEDULE = "This patient doesn't meet the schedule sex requirements";

    public ClkBatchResponse<List<SchedulingResponseDTO>> saveScheduling(List<SchedulingDTO> schedulingDTOS) {
        ClkBatchResponse<List<SchedulingResponseDTO>> schedulingListResponse = new ClkBatchResponse<>();

        List<SchedulingResponseDTO> schedulingResponseDTOS = new ArrayList<>();
        schedulingDTOS.forEach((SchedulingDTO schedulingDTO) -> {
            try {
                validateAge(schedulingDTO);
                validateSex(schedulingDTO);
                validateTimeScheduleDTO(schedulingDTO);
                validateExistingSchedule(schedulingDTO);

                List<AgendaGradeAtendimentoHorario> agendaGradeAtendimentoHorarios = registrarAgendamentos(new SchedulingMapper().toSchedulingReserveDTO(schedulingDTO), true);

                agendaGradeAtendimentoHorarios.forEach((AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario) ->
                    schedulingResponseDTOS.add(getSuccess(schedulingDTO.getCorrelationId(), agendaGradeAtendimentoHorario.getCodigo()))
                );
            } catch (ValidacaoException | DAOException exception) {
                schedulingListResponse.setContainErrors(true);
                schedulingResponseDTOS.add(getError(schedulingDTO.getCorrelationId(), exception));
                Loggable.log.error(exception.getMessage());
            }
        });

        schedulingListResponse.setResults(schedulingResponseDTOS);
        return schedulingListResponse;
    }

    public ClkBatchResponse<List<SchedulingResponseDTO>> cancelScheduling(List<CancelSchedulingDTO> cancelSchedulingDTOS) {
        ClkBatchResponse<List<SchedulingResponseDTO>> clkCreationListResponse = new ClkBatchResponse<>();
        List<SchedulingResponseDTO> clkCreationResponseDTOS = new ArrayList<>();

        cancelSchedulingDTOS.forEach((CancelSchedulingDTO cancelSchedulingDTO) -> {

            try {
                validateCancelScheduling(cancelSchedulingDTO);

                getAgendamentoFacade().cancelarAgendamento(getCancelamentoAgendamentoDTO(cancelSchedulingDTO));

                clkCreationResponseDTOS.add(getSuccess(cancelSchedulingDTO.getCorrelationId(), cancelSchedulingDTO.getSchedulingId()));
            } catch (DAOException | ValidacaoException exception) {
                clkCreationListResponse.setContainErrors(true);
                clkCreationResponseDTOS.add(getError(cancelSchedulingDTO.getCorrelationId(), exception));
            }

        });

        clkCreationListResponse.setResults(clkCreationResponseDTOS);
        return clkCreationListResponse;
    }

    private CancelamentoAgendamentoDTO getCancelamentoAgendamentoDTO(CancelSchedulingDTO cancelSchedulingDTO) {
        CancelamentoAgendamentoDTO dtoCancelamento = new CancelamentoAgendamentoDTO();
        dtoCancelamento.setCodigoAgendaGradeAtendimentoHorario(cancelSchedulingDTO.getSchedulingId());
        dtoCancelamento.setValidarUsuarioAgendamento(false);
        dtoCancelamento.setReabrirSolicitacaoAgendamento(true);
        dtoCancelamento.setMotivoCancelamento(cancelSchedulingDTO.getCancellationReason());
        return dtoCancelamento;
    }

    private SchedulingResponseDTO getSuccess(Long correlationId, Long schedulingId) {
        return new SchedulingResponseBuilder().builder()
                .setCode(ClkTransactionStatus.SUCCESS.getCode())
                .setMessage(ClkTransactionStatus.SUCCESS.getMessage())
                .setCorrelationId(correlationId)
                .setSchedulingId(schedulingId)
                .build();
    }

    private SchedulingResponseDTO getError(Long correlationId, SGKException exception) {
        return new SchedulingResponseBuilder().builder()
                .setCode(ClkTransactionStatus.FAILED.getCode())
                .setMessage(ClkTransactionStatus.FAILED.getMessage())
                .setCorrelationId(correlationId)
                .setErrors(getErrors(exception))
                .build();
    }

    private void validateCancelScheduling(CancelSchedulingDTO cancelSchedulingDTO) throws ValidacaoException {
        AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario = agendaService.getAgendaGradeAtendimentoHorarioByCodigoSimplified(cancelSchedulingDTO.getSchedulingId());
        if (agendaGradeAtendimentoHorario == null) {
            throw new ValidacaoException(A_AGENDA_SELECIONADA_NAO_EXISTE);
        } else {
            validateSchedulingAppointmentType(agendaGradeAtendimentoHorario.getAgendaGradeAtendimento(), new ValidacaoException(SCHEDULING_WITH_INVALID_APPOINTMENT_TYPE));
        }
    }

    private void validateExistingSchedule(SchedulingDTO schedulingDTO) throws ValidacaoException {
        AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorarioProxy = on(AgendaGradeAtendimentoHorario.class);

        AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario = LoadManager.getInstance(AgendaGradeAtendimentoHorario.class)
                .addProperty(path(agendaGradeAtendimentoHorarioProxy.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(agendaGradeAtendimentoHorarioProxy.getAgendaGradeHorario().getCodigo()), schedulingDTO.getGridScheduleTimeId()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(agendaGradeAtendimentoHorarioProxy.getStatus()), BuilderQueryCustom.QueryParameter.DIFERENTE, AgendaGradeAtendimentoHorario.STATUS_CANCELADO))
                .startLeitura()
                .getVO();

        if (agendaGradeAtendimentoHorario != null) {
            throw new ValidacaoException(SCHEDULING_ALREADY_REGISTERED);
        }
    }

    private void validateTimeScheduleDTO(SchedulingDTO schedulingDTO) throws ValidacaoException {
        AgendaGradeHorario agendaGradeHorarioProxy = on(AgendaGradeHorario.class);

        AgendaGradeHorario agendaGradeHorario = LoadManager.getInstance(AgendaGradeHorario.class)
                .addProperty(path(agendaGradeHorarioProxy.getCodigo()))
                .addProperty(path(agendaGradeHorarioProxy.getHora()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(agendaGradeHorarioProxy.getCodigo()), schedulingDTO.getGridScheduleTimeId()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(agendaGradeHorarioProxy.getAgendaGradeAtendimento().getCodigo()), schedulingDTO.getGridScheduleServiceId()))
                .startLeitura()
                .getVO();

        if (agendaGradeHorario == null) {
            throw new ValidacaoException(GRID_SCHEDULE_TIME_INFORMED_COULD_NOT_BE_FOUND);
        }

        if (new Date().after(agendaGradeHorario.getHora())) {
            throw new ValidacaoException(Bundle.getStringApplication(MSG_ADDING_DATE_PREVIOUS_TO_NOW_NOT_ALLOWED));
        }
    }

    private void validateAge(SchedulingDTO schedulingDTO) throws ValidacaoException{
        Map<String, Short> ageInterval =getValidAgeInterval(schedulingDTO);
        if(ageInterval !=null){
            UsuarioCadsus pacient = getUsuarioCadsusAge(schedulingDTO.getPatientId());
            if(pacient == null)
                return;
            Short beginAge = ageInterval.get("idade_inicio");
            Short endAge = ageInterval.get("idade_fim");
            if((beginAge != null && beginAge > pacient.getIdade()) ||
                    (endAge != null && endAge < pacient.getIdade())){
                throw new ValidacaoException(Bundle.getStringApplication(MSG_PATIENT_AGE_INCOMPATIBLE_WITH_SCHEDULE));
            }
        }
    }

    private void validateSex(SchedulingDTO schedulingDTO) throws ValidacaoException{
        String validSex = getValidSex(schedulingDTO);
        if(validSex !=null){
            UsuarioCadsus pacient = getUsuarioCadsusSex(schedulingDTO.getPatientId());
            if(pacient == null)
                return;
            if(!validSex.equals(pacient.getSexo())){
                throw new ValidacaoException(Bundle.getStringApplication(MSG_PATIENT_SEX_INCOMPATIBLE_WITH_SCHEDULE));
            }
        }
    }

    private Map<String, Short> getValidAgeInterval(SchedulingDTO schedulingDTO){
        Session session = HibernateSessionFactory.getSessionLeitura();
        String sql = "select a.idade_inicio, a.idade_fim from \n" +
                "\tagenda_grade_horario agh \n" +
                "\tjoin agenda_grade_atendimento aga on agh.cd_ag_gra_atendimento = aga.cd_ag_gra_atendimento \n" +
                "\tjoin agenda_grade ag on ag.cd_ag_grade = aga.cd_ag_grade \n" +
                "\tjoin agenda a on ag.cd_agenda = a.cd_agenda\n" +
                "where agh.cd_agenda_horario = :cd_horario_agenda";
        SQLQuery query = session.createSQLQuery(sql);
        query.setParameter("cd_horario_agenda", schedulingDTO.getGridScheduleTimeId());
        query.setResultTransformer(ALIAS_TO_ENTITY_MAP);
        List< Map<String, Short> > data = query.list();
        if(!data.isEmpty()){
            return data.get(0);
        }
        else {
            return null;
        }
    }

    private String getValidSex(SchedulingDTO schedulingDTO){
        Session session = HibernateSessionFactory.getSessionLeitura();
        String sql = "select a.sexo from \n" +
                "\tagenda_grade_horario agh \n" +
                "\tjoin agenda_grade_atendimento aga on agh.cd_ag_gra_atendimento = aga.cd_ag_gra_atendimento \n" +
                "\tjoin agenda_grade ag on ag.cd_ag_grade = aga.cd_ag_grade \n" +
                "\tjoin agenda a on ag.cd_agenda = a.cd_agenda\n" +
                "where agh.cd_agenda_horario = :cd_horario_agenda";
        SQLQuery query = session.createSQLQuery(sql);
        query.setParameter("cd_horario_agenda", schedulingDTO.getGridScheduleTimeId());
        query.setResultTransformer(ALIAS_TO_ENTITY_MAP);
        List< Map<String, String> > data = query.list();
        if(!data.isEmpty() && data.get(0).get("sexo") != null){
            return data.get(0).get("sexo");
        }
        else {
            return null;
        }
    }

    private UsuarioCadsus getUsuarioCadsusAge(Long pacientId) {
        return LoadManager.getInstance(UsuarioCadsus.class)
                .addProperty(PROP_DATA_NASCIMENTO)
                .addParameter(new QueryCustom.QueryCustomParameter(PROP_CODIGO, pacientId))
                .startLeitura().getVO();
    }

    private UsuarioCadsus getUsuarioCadsusSex(Long pacientId) {
        return LoadManager.getInstance(UsuarioCadsus.class)
                .addProperty(PROP_SEXO)
                .addParameter(new QueryCustom.QueryCustomParameter(PROP_CODIGO, pacientId))
                .startLeitura().getVO();
    }

    private List<ClkError> getErrors(SGKException exception) {
        List<ClkError> errors = new ArrayList<>();

        if (exception.getMessage().contains("Não é permitido cancelar Agendamentos com menos de")) {
            errors.add(new ClkError("Invalid notice period for cancellation", GRID_SCHEDULE_SERVICE_WITH_INVALID_APPOINTMENT_TYPE));
        }
        else if (exception.getMessage().contains("Somente agendamentos com a situação \"Agendado\" podem ser cancelados.")) {
            errors.add(new ClkError("Only Scheduling with status as Scheduled(0) can be canceled", GRID_SCHEDULE_SERVICE_WITH_INVALID_APPOINTMENT_TYPE));
        }
        else if (exception.getMessage().contains("Somente agendamentos cadastrados hoje podem ser cancelados")) {
            errors.add(new ClkError("Scheduling can only be canceled at the same day they were created", GRID_SCHEDULE_SERVICE_WITH_INVALID_APPOINTMENT_TYPE));
        }
        else if (exception.getMessage().contains("O paciente não existe")) {
            errors.add(new ClkError("Patient informed could not be found", "patientId"));
        }
        else if (exception.getMessage().contains("A unidade de saúde selecionada não existe")) {
            errors.add(new ClkError("Care Unit informed could not be found", "careUnitId"));
        }
        else if (exception.getMessage().contains("Procedimento selecionado não existe")) {
            errors.add(new ClkError("Procedure informed could not be found", "procedureId"));
        }
        else if (exception.getMessage().contains(A_AGENDA_SELECIONADA_NAO_EXISTE)) {
            errors.add(new ClkError("Schedule informed could not be found", GRID_SCHEDULE_SERVICE_ID));
        }
        else if (exception.getMessage().contains("Solicitação já agendada por outro processo")) {
            errors.add(new ClkError("Schedule already taken", GRID_SCHEDULE_SERVICE_ID));
        }
        else if (exception.getMessage().contains("Não existem vagas disponíveis para agenda")) {
            errors.add(new ClkError("There are no vacancies for this schedule", GRID_SCHEDULE_SERVICE_ID));
        }
        else if (exception.getMessage().contains("O paciente já possui uma agenda para este tipo de procedimento")) {
            errors.add(new ClkError("Patient already have a scheduling for this day and care unit", "patientId"));
        }
        else if (exception.getMessage().contains(GRID_SCHEDULE_SERVICE_WITH_INVALID_APPOINTMENT_TYPE)) {
            errors.add(new ClkError(GRID_SCHEDULE_SERVICE_WITH_INVALID_APPOINTMENT_TYPE, GRID_SCHEDULE_SERVICE_ID));
        }
        else if (exception.getMessage().contains(SCHEDULING_WITH_INVALID_APPOINTMENT_TYPE)) {
            errors.add(new ClkError(SCHEDULING_WITH_INVALID_APPOINTMENT_TYPE, SCHEDULING_ID));
        }
        else if (exception.getMessage().contains(GRID_SCHEDULE_TIME_INFORMED_COULD_NOT_BE_FOUND)) {
            errors.add(new ClkError(GRID_SCHEDULE_TIME_INFORMED_COULD_NOT_BE_FOUND, "gridScheduleTimeId"));
        }
        else if (exception.getMessage().contains(SCHEDULING_ALREADY_REGISTERED)) {
            errors.add(new ClkError(SCHEDULING_ALREADY_REGISTERED, GRID_SCHEDULE_SERVICE_ID));
        }
        else if(exception.getMessage().contains(MSG_ADDING_DATE_PREVIOUS_TO_NOW_NOT_ALLOWED)){
            errors.add(new ClkError(MSG_ADDING_DATE_PREVIOUS_TO_NOW_NOT_ALLOWED,null));
        }
        else if(exception.getMessage().contains(MSG_PATIENT_AGE_INCOMPATIBLE_WITH_SCHEDULE)){
            errors.add(new ClkError(MSG_PATIENT_AGE_INCOMPATIBLE_WITH_SCHEDULE,null));
        }
        else if(exception.getMessage().contains(MSG_PATIENT_SEX_INCOMPATIBLE_WITH_SCHEDULE)){
            errors.add(new ClkError(MSG_PATIENT_SEX_INCOMPATIBLE_WITH_SCHEDULE,null));
        }
        else{
            errors.add(new ClkError("Invalid Scheduling solicitation", null));
        }
        return errors;
    }

    public Long cadastraAgendamento(SchedulingReserveDTO schedulingReserveDTO) throws ValidacaoException, DAOException {
        try {
            List<AgendaGradeAtendimentoHorario> list = registrarAgendamentos(schedulingReserveDTO, false);

            return list.isEmpty() ? null : list.get(0).getCodigo();
        } catch (Exception e) {
            updateSchedule(schedulingReserveDTO);
            notificacaoService.sendNotification(schedulingReserveDTO);
            Loggable.log.error(e.getMessage(), e);
            throw e;
        }
    }

    protected List<AgendaGradeAtendimentoHorario> registrarAgendamentos(SchedulingReserveDTO schedulingReserveDTO, boolean isValidateExternalScheduling) throws ValidacaoException, DAOException {
        AgendaGradeAtendimentoDTO agendaGradeAtendimentoDTO = getAgendaService()
                .getAgendaGradeAtendimentoDTO(schedulingReserveDTO.getGridScheduleServiceId(), schedulingReserveDTO.getProcedureId(), schedulingReserveDTO.getTimeSchedule().getTimeScheduleId());

        if (isValidateExternalScheduling) validateSchedulingAppointmentType(agendaGradeAtendimentoDTO.getAgendaGradeAtendimento(), new ValidacaoException(GRID_SCHEDULE_SERVICE_WITH_INVALID_APPOINTMENT_TYPE));
        
        Empresa empresa = getUnidadeSaudeService().findEmpresaByCodigo(schedulingReserveDTO.getCareUnitId());
        UsuarioCadsus paciente = getPacienteService().getPacienteByIdAsUsuarioCadsus(schedulingReserveDTO.getPatientSaudeId());
        validaCadastro(agendaGradeAtendimentoDTO, empresa, paciente);
        agendaGradeAtendimentoDTO.setEmpresaAgenda(empresa);

        return getAgendamentoFacade().registrarAgendamentosSelecionados(Collections.singletonList(agendaGradeAtendimentoDTO),
                empresa,
                paciente,
                agendaGradeAtendimentoDTO.getTipoProcedimento().getProcedimento(),
                null,
                null);
    }

    private void validateSchedulingAppointmentType(AgendaGradeAtendimento agendaGradeAtendimento, ValidacaoException validacaoException) throws ValidacaoException {
        if (agendaGradeAtendimento != null
                && agendaGradeAtendimento.getTipoAtendimentoAgenda() != null
                && agendaGradeAtendimento.getTipoAtendimentoAgenda().getTipoAtendimento() != null) {
            Long tipoAtendimento = agendaGradeAtendimento.getTipoAtendimentoAgenda().getTipoAtendimento();

            if (TipoAtendimentoAgenda.TipoAtendimento.TELEAGENDAMENTO_NORMAL.value().equals(tipoAtendimento)
                    || TipoAtendimentoAgenda.TipoAtendimento.TELEAGENDAMENTO_RETORNO.value().equals(tipoAtendimento)) {
                return;
            }
        }

        throw validacaoException;
    }

    private void updateSchedule(SchedulingReserveDTO schedulingReserveDTO) throws DAOException {
        AgendaGradeHorario agendaGradeHorario = agendaService
                .findAgendaGradeHorarioByCodigo(
                        schedulingReserveDTO
                                .getTimeSchedule()
                                .getTimeScheduleId(),
                        false);
        if (agendaGradeHorario != null) {
            ScheduleDTOMapper scheduleDTOMapper = new ScheduleDTOMapper();
            agendaService.updateSchedule(
                    scheduleDTOMapper
                            .fromAgendaGradeHorario(agendaGradeHorario)
                            .map(),
                    schedulingReserveDTO
                            .getCityId());
        }
    }

    public void cancelarAgendamento(Long idAgah, Long idMunicipio, String motivoCancelamento, Long reabrirSolicitacao) throws ValidacaoException, DAOException {
        AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario = agendaService.findAgendaGradeAtendimentoHorarioByCodigo(idAgah);

        if (agendaGradeAtendimentoHorario == null) {
            throw new ValidacaoException(A_AGENDA_SELECIONADA_NAO_EXISTE);
        }

        if (!agendaGradeAtendimentoHorario.getStatus().equals(AgendaGradeAtendimentoHorario.STATUS_CANCELADO)) {
            CancelamentoAgendamentoDTO dtoCancelamento = new CancelamentoAgendamentoDTO();
            dtoCancelamento.setCodigoAgendaGradeAtendimentoHorario(idAgah);
            dtoCancelamento.setValidarUsuarioAgendamento(false);
            dtoCancelamento.setReabrirSolicitacaoAgendamento(!RepositoryComponentDefault.NAO_LONG.equals(reabrirSolicitacao));
            dtoCancelamento.setCancelarSolicitacaoAgendamento(RepositoryComponentDefault.NAO_LONG.equals(reabrirSolicitacao));
            dtoCancelamento.setMotivoCancelamento(motivoCancelamento);
            getAgendamentoFacade().cancelarAgendamento(dtoCancelamento);

            if (agendaGradeAtendimentoHorario.getAgendaGradeAtendimento() != null
                    && isAgendaCidadao(agendaGradeAtendimentoHorario))
                updateSchedule(agendaGradeAtendimentoHorario, idMunicipio);
        }
    }

    private boolean isAgendaCidadao(AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario) {
        return TipoAtendimentoAgenda
                .TipoAtendimento
                .APP_CIDADAO.value().equals(
                        agendaGradeAtendimentoHorario
                                .getAgendaGradeAtendimento()
                                .getTipoAtendimentoAgenda().getTipoAtendimento()
                );
    }

    private void updateSchedule(AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario, Long idMunicipio) {
        ScheduleDTOMapper scheduleDTOMapper = new ScheduleDTOMapper();
        if (agendaGradeAtendimentoHorario != null) {
            agendaService.updateSchedule(scheduleDTOMapper
                            .fromAgendaGradeAtendimentoHorario(agendaGradeAtendimentoHorario)
                            .map(),
                    idMunicipio);
        }
    }

    private void validaCadastro(AgendaGradeAtendimentoDTO agendaGradeAtendimentoDTO, Empresa empresa, UsuarioCadsus paciente) throws ValidacaoException {
        if (!existeAgenda(agendaGradeAtendimentoDTO))
            throw new ValidacaoException(A_AGENDA_SELECIONADA_NAO_EXISTE);
        if (!existeAgendaHorario(agendaGradeAtendimentoDTO))
            throw new ValidacaoException(O_HORARIO_SELECIONADO_NAO_EXISTE);
        if (!existeProcedimento(agendaGradeAtendimentoDTO))
            throw new ValidacaoException("Procedimento selecionado não existe");
        if (empresa == null)
            throw new ValidacaoException("A unidade de saúde selecionada não existe");
        if (paciente == null)
            throw new ValidacaoException("O paciente não existe");
    }

    private boolean existeProcedimento(AgendaGradeAtendimentoDTO agendaGradeAtendimentoDTO) {
        return agendaGradeAtendimentoDTO.getTipoProcedimento() != null && agendaGradeAtendimentoDTO.getTipoProcedimento().getProcedimento() != null;
    }

    private boolean existeAgenda(AgendaGradeAtendimentoDTO agendaGradeAtendimentoDTO) {
        return agendaGradeAtendimentoDTO != null && agendaGradeAtendimentoDTO.getAgendaGradeAtendimento() != null;
    }

    private boolean existeAgendaHorario(AgendaGradeAtendimentoDTO agendaGradeAtendimentoDTO) {
        return agendaGradeAtendimentoDTO != null && agendaGradeAtendimentoDTO.getAgendaGradeAtendimento() != null
                && agendaGradeAtendimentoDTO.getAgendaGradeHorario() != null;
    }

    private AgendaService getAgendaService() {
        return agendaService;
    }

    private UnidadeSaudeService getUnidadeSaudeService() {
        return unidadeSaudeService;
    }

    private PacienteService getPacienteService() {
        return pacienteService;
    }

    private AgendamentoFacade getAgendamentoFacade() {
        return boFactoryService.getBO(AgendamentoFacade.class);
    }

    public static String debugSchedulingReserveDTO(SchedulingReserveDTO schedulingReserveDTO){
        String mensagem = "Cidade: "+schedulingReserveDTO.getCityId()+
                "; Unidade: "+schedulingReserveDTO.getCareUnitId()+
                "; Procedimento: "+schedulingReserveDTO.getProcedureId()+
                "; IdPaciente: "+schedulingReserveDTO.getPatientSaudeId()+
                "; GridSchedule: "+schedulingReserveDTO.getGridScheduleServiceId()+
                "; ";
        return mensagem;
    }
}
