package br.com.celk.bo.mapper;

import br.com.celk.appcidadao.dto.agenda.AgendaDTO;
import br.com.celk.bo.dto.TimeScheduleDTO;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeHorario;

public class TimeScheduleDTOMapper {
    TimeScheduleDTO timeScheduleDTO;

    public TimeScheduleDTOMapper fromAgendaDTO(AgendaDTO agendaDTO) {
        timeScheduleDTO = new TimeScheduleDTO();
        timeScheduleDTO.setTimeScheduleId(agendaDTO.getIdAgendaGradeHorario());
        timeScheduleDTO.setTimeSchedule(agendaDTO.getHora());
        return this;
    }

    public TimeScheduleDTOMapper fromAgendaGradeHorario(AgendaGradeHorario agendaGradeHorario) {
        timeScheduleDTO = new TimeScheduleDTO();
        timeScheduleDTO.setTimeScheduleId(agendaGradeHorario.getCodigo());
        timeScheduleDTO.setTimeSchedule(agendaGradeHorario.getHora());
        return this;
    }

    public TimeScheduleDTO map() {
        return timeScheduleDTO;
    }
}
