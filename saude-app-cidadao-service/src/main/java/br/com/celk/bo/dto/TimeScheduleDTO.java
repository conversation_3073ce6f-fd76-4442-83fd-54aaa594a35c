package br.com.celk.bo.dto;

import br.com.celk.util.DataUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Date;
@JsonIgnoreProperties(ignoreUnknown = true)
public class TimeScheduleDTO {
    private Long timeScheduleId;
    private String timeSchedule;

    public Long getTimeScheduleId() {
        return timeScheduleId;
    }

    public void setTimeScheduleId(Long timeScheduleId) {
        this.timeScheduleId = timeScheduleId;
    }

    public String getTimeSchedule() {
        return timeSchedule;
    }

    public void setTimeSchedule(Date timeSchedule) {
        this.timeSchedule = DataUtil.toIso8601(timeSchedule);
    }
}
