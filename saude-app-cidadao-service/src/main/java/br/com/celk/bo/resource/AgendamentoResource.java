package br.com.celk.bo.resource;

import br.com.celk.bo.dto.SchedulingReserveDTO;
import br.com.celk.bo.service.AgendamentoService;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoCodigoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

import javax.inject.Inject;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 */

@Path("/v1/agendamento/municipio/{idMunicipio}")
public class AgendamentoResource {

    @Inject
    private AgendamentoService agendamentoService;

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    public Response postAgendamento(SchedulingReserveDTO schedulingReserveDTO, @PathParam("idMunicipio") Long idMunicipio) throws ValidacaoException, DAOException {
        Loggable.log.info(agendamentoService.debugSchedulingReserveDTO(schedulingReserveDTO));
        schedulingReserveDTO.setCityId(idMunicipio);

        Long codigoAgendamento = agendamentoService.cadastraAgendamento(schedulingReserveDTO);

        if (codigoAgendamento == null) return Response.status(Response.Status.BAD_REQUEST).build();

        AgendaGradeAtendimentoCodigoDTO codigoDTO = new AgendaGradeAtendimentoCodigoDTO();
        codigoDTO.setCodigoAgendamento(codigoAgendamento);

        return Response.ok(codigoDTO).build();
    }

    @DELETE
    @Path("cancelar/{id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response cancelarAgendamento(@PathParam("idMunicipio") Long idMunicipio,
                                        @PathParam("id") Long idAgah,
                                        @QueryParam("motivoCancelamento") String motivoCancelamento,
                                        @QueryParam("reabrirSolicitacao") Long reabrirSolicitacao
    ) throws ValidacaoException, DAOException {
        agendamentoService.cancelarAgendamento(idAgah, idMunicipio, motivoCancelamento, reabrirSolicitacao);
        return Response.ok().build();
    }
}
