package br.com.celk.bo.service;

import br.com.celk.bo.dto.SchedulingReserveDTO;
import br.com.ksisolucoes.bo.appcidadao.DTO.EnviaNotificacaoDTO;
import br.com.ksisolucoes.bo.appcidadao.DTO.MunicipioUrlDto;
import br.com.ksisolucoes.bo.appcidadao.DTO.NotificacaoDTO;
import br.com.ksisolucoes.bo.appcidadao.EnviaNotificacaoAppCidadaoHelper;
import br.com.ksisolucoes.bo.appcidadao.utils.AppCidadaoUtils;
import br.com.ksisolucoes.dao.exception.DAOException;

import javax.ejb.*;
import java.util.HashMap;

@Stateless
@TransactionManagement(TransactionManagementType.CONTAINER)
public class NotificacaoService {
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public void sendNotification(SchedulingReserveDTO schedulingReserveDTO) throws DAOException {
        EnviaNotificacaoDTO enviaNotificacaoDTO = new EnviaNotificacaoDTO();
        enviaNotificacaoDTO.setIdPaciente(schedulingReserveDTO.getPatientSaudeId());
        enviaNotificacaoDTO.setMunicipio(new MunicipioUrlDto(AppCidadaoUtils.getHost()));
        enviaNotificacaoDTO.setNotificacao(getNotificacao(schedulingReserveDTO));
        EnviaNotificacaoAppCidadaoHelper.enviaNotificacao(enviaNotificacaoDTO);
    }

    private NotificacaoDTO getNotificacao(SchedulingReserveDTO schedulingReserveDTO) {
        NotificacaoDTO notificacaoDTO = new NotificacaoDTO();
        notificacaoDTO.setTitle("Houve um problema no cadastro do seu agendamento");
        notificacaoDTO.setMessage("Houve um problema no cadastro do seu agendamento, por favor, entre em contato com a unidade.");
        notificacaoDTO.setModulo("agendamento");
        notificacaoDTO.setData(new HashMap<String, Long>());
        notificacaoDTO.getData().put("AGENDAMENTO_NAO_CONFIRMADO", schedulingReserveDTO.getTimeSchedule().getTimeScheduleId());
        return notificacaoDTO;
    }


}
