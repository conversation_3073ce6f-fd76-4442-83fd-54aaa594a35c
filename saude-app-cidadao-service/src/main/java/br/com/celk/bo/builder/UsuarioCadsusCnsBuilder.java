package br.com.celk.bo.builder;

import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;

public class UsuarioCadsusCnsBuilder {

    private UsuarioCadsusCns usuarioCadsusCns;

    public UsuarioCadsusCnsBuilder builder() {
        usuarioCadsusCns = new UsuarioCadsusCns();
        return this;
    }

    public UsuarioCadsusCnsBuilder setUsuarioCadsus(UsuarioCadsus usuarioCadsus){
        usuarioCadsusCns.setUsuarioCadsus(usuarioCadsus);
        return this;
    }

    public UsuarioCadsusCnsBuilder setNumeroCartao(Long numeroCartao){
        usuarioCadsusCns.setNumeroCartao(numeroCartao);
        return this;
    }

    public UsuarioCadsusCnsBuilder setExcluido(Long cartaoExcluido){
        usuarioCadsusCns.setExcluido(cartaoExcluido);
        return this;
    }

    public UsuarioCadsusCns build() {
        return this.usuarioCadsusCns;
    }
}
