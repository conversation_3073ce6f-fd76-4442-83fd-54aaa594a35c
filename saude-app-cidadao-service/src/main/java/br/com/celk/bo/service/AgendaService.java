package br.com.celk.bo.service;

import br.com.celk.appcidadao.dto.agenda.AgendaDTO;
import br.com.celk.appcidadao.dto.agenda.CareUnitDTO;
import br.com.celk.appcidadao.dto.agenda.ProcedimentoDTO;
import br.com.celk.appcidadao.dto.agenda.QueryConsultaProcedimentoAgendaDisponivelDTOParam;
import br.com.celk.bo.appcidadao.interfaces.facade.agenda.AgendaFacade;
import br.com.celk.bo.client.QuarkusScheduleClient;
import br.com.celk.bo.dto.*;
import br.com.celk.bo.mapper.ScheduleDTOMapper;
import br.com.celk.bo.mapper.TimeScheduleDTOMapper;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTO;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.io.FileUtils;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.facade.AtendimentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioImprimirComprovanteAgendamentoDTOParam;
import br.com.ksisolucoes.server.HibernateSessionFactory;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoUtil;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.*;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamentoOcorrencia;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperExportManager;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.LockMode;
import org.hibernate.Session;
import org.hibernate.criterion.Restrictions;

import javax.ejb.Stateless;
import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.inject.Inject;
import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

@Stateless
public class AgendaService {

    @Inject
    ProcedimentoService procedimentoService;
    @Inject
    BOFactoryService boFactoryService;
    @Inject
    LoadManagerService loadManagerService;
    @Inject
    QuarkusScheduleClient quarkusScheduleClient;
    @Inject
    PacienteService pacienteService;

    public List<ProcedureUnitsDTO> getProcedimentoListAgendaDisponivel(QueryConsultaProcedimentoAgendaDisponivelDTOParam param, Long idUsuario) throws ValidacaoException, DAOException {
        if (idUsuario != null) {
            getUsuarioCadsuseEmpresa(param, idUsuario);
        }
        HashMap<Long, ProcedureUnitsDTO> procedureMap = new HashMap<>();
        List<String> procedureUnitList = new ArrayList<>();
        List<ProcedimentoDTO> procedimentoDTOList = getProcedimentoList(param);
        for (ProcedimentoDTO procedimentoDTO : procedimentoDTOList) {
            if (procedureMap.containsKey(procedimentoDTO.getCodigo())) {
                if (!procedureUnitList.contains(getChaveProcedimentoUnidade(procedimentoDTO))) {
                    procedureMap.get(procedimentoDTO.getCodigo()).getCareUnitDTOS().add(procedimentoDTO.getCareUnit());
                    procedureUnitList.add(getChaveProcedimentoUnidade(procedimentoDTO));
                }
            } else {
                procedureMap.put(procedimentoDTO.getCodigo(), getProcedureUnitFromProcedimentoDTO(procedimentoDTO));
                procedureUnitList.add(getChaveProcedimentoUnidade(procedimentoDTO));
            }
        }
        return new ArrayList<>(procedureMap.values());
    }

    public void getUsuarioCadsuseEmpresa(QueryConsultaProcedimentoAgendaDisponivelDTOParam param, Long usuarioId) throws DAOException, ValidacaoException {
        PacienteDTO pacienteDTO = pacienteService.getPacienteById(usuarioId);
        if (pacienteDTO != null) {
            param.setUsuarioCadsus(pacienteDTO.getUsuarioCadsus());
            if (pacienteDTO.getEquipeDTO() != null && pacienteDTO.getEquipeDTO().getEquipeArea() != null) {
                param.setCdAreaEquipe(pacienteDTO.getEquipeDTO().getEquipeArea().getCodigoArea());
                param.setUsuarioCadsusEquipe(pacienteDTO.getEquipe());
            }
        }
    }

    private ProcedureUnitsDTO getProcedureUnitFromProcedimentoDTO(ProcedimentoDTO procedimentoDTO) {
        ProcedureUnitsDTO procedureUnitsDTO = new ProcedureUnitsDTO();
        procedureUnitsDTO.setProcedureId(procedimentoDTO.getCodigo());
        procedureUnitsDTO.setProcedureName(procedimentoDTO.getDescricao());
        List<CareUnitDTO> careUnitDTOList = new ArrayList<>();
        careUnitDTOList.add(procedimentoDTO.getCareUnit());
        procedureUnitsDTO.setCareUnitDTOS(careUnitDTOList);
        return procedureUnitsDTO;
    }

    private String getChaveProcedimentoUnidade(ProcedimentoDTO procedimentoDTO) {
        return procedimentoDTO.getCodigo().toString() + procedimentoDTO.getCareUnit().getCareUnitId().toString();
    }

    private List<ProcedimentoDTO> getProcedimentoList(QueryConsultaProcedimentoAgendaDisponivelDTOParam param) throws ValidacaoException, DAOException {
        List<ProcedimentoDTO> procedimentoDTOList;

        if (param.isDisponibilizaAgendaUnidadeReferencia()) {
            if (param.getUsuarioCadsusEquipe() != null || param.hasAreaEquipe() || param.hasAreaEnderecoDomicilio()) {
                procedimentoDTOList = getAgendaFacade().findProcedimentoAgendaDisponivel(param);
                if (procedimentoDTOList.isEmpty()) {
                    procedimentoDTOList = findProcedAgendDisponivelsetTipoAtendimentoParam(param, TipoAtendimentoAgenda.TipoAtendimento.APP_CIDADAO_GERAL.value());
                }
            } else {
                procedimentoDTOList = findProcedAgendDisponivelsetTipoAtendimentoParam(param, TipoAtendimentoAgenda.TipoAtendimento.APP_CIDADAO_GERAL.value());
            }
        } else {
            procedimentoDTOList = getAgendaFacade().findProcedimentoAgendaDisponivel(param);
        }
        return procedimentoDTOList == null ? new ArrayList<>() : procedimentoDTOList;
    }

    private List<ProcedimentoDTO> findProcedAgendDisponivelsetTipoAtendimentoParam(QueryConsultaProcedimentoAgendaDisponivelDTOParam param, Long tipoProcedimentoAgenda) throws DAOException, ValidacaoException {
        param.setTipoAtendimentoAgenda(tipoProcedimentoAgenda);
        return getAgendaFacade().findProcedimentoAgendaDisponivel(param);
    }

    public List<ScheduleDTO> findHorarioListByUnidadeAndProcedimento(Long codigoUnidade, Long codigoProcedimento) throws ValidacaoException, DAOException {
        validaBuscaHorario(codigoProcedimento);
        List<AgendaDTO> agendaDTOList = getHorariosList(codigoUnidade, codigoProcedimento);
        HashMap<String, ScheduleDTO> scheduleHashMap = new HashMap<>();
        for (AgendaDTO agendaDTO : agendaDTOList) {
            String chaveAgenda = getChaveAgenda(agendaDTO);
            if (!scheduleHashMap.containsKey(chaveAgenda)) {
                scheduleHashMap.put(chaveAgenda, getScheduleFromAgendaDTO(agendaDTO));
            }
            scheduleHashMap.get(chaveAgenda).getTimeScheduleDTOS().add(agendaDTOToTimeSchedule(agendaDTO));
        }
        return new ArrayList<>(scheduleHashMap.values());
    }

    public void registrarOcorrencia(RegistroOcorrenciaDTO registroOcorrenciaDTO) throws ValidacaoException, DAOException {

        validarRegistroOcorrencia(registroOcorrenciaDTO);
        validarTipoOcorrencia(registroOcorrenciaDTO);
        java.util.Date dataSolicitacao = validarData(registroOcorrenciaDTO.getDataHora());
        SolicitacaoAgendamento solicitacaoAgendamento = validaSolicitacao(registroOcorrenciaDTO.getIdSolicitacao());
        registroOcorrenciaDTO.setDescricao("Usuário: " + registroOcorrenciaDTO.getNomeUsuario() + " - " + registroOcorrenciaDTO.getDescricao());

        if (SolicitacaoAgendamentoOcorrencia.TipoOcorrencia.CONFIRMACAO_CONTATO.value().equals(registroOcorrenciaDTO.getTipoOcorrencia())) {
            confirmarContato(registroOcorrenciaDTO, dataSolicitacao);
        } else if (SolicitacaoAgendamentoOcorrencia.TipoOcorrencia.LANCAR_HISTORICO.value().equals(registroOcorrenciaDTO.getTipoOcorrencia())) {
            gerarOcorrencia(registroOcorrenciaDTO, solicitacaoAgendamento, dataSolicitacao);
        }
    }

    private void validarRegistroOcorrencia(RegistroOcorrenciaDTO registroOcorrenciaDTO) throws ValidacaoException {
        if (Objects.isNull(registroOcorrenciaDTO.getIdSolicitacao())) {
            throw new ValidacaoException(Bundle.getStringApplication("rotulo_id_solicitacao_obrigatorio"));
        }
        if (StringUtils.isBlank(registroOcorrenciaDTO.getDataHora())) {
            throw new ValidacaoException(Bundle.getStringApplication("rotulo_data_hora_obrigatorio"));
        }
        if (StringUtils.isBlank(registroOcorrenciaDTO.getDescricao())) {
            throw new ValidacaoException(Bundle.getStringApplication("rotulo_descricao_obrigatorio"));
        }
        if (StringUtils.isBlank(registroOcorrenciaDTO.getNomeUsuario())) {
            throw new ValidacaoException(Bundle.getStringApplication("rotulo_nome_usuario_obrigatorio"));
        }
        if (Objects.isNull(registroOcorrenciaDTO.getTipoOcorrencia())) {
            throw new ValidacaoException(Bundle.getStringApplication("rotulo_tipo_ocorrencia_obrigatorio"));
        }
    }

    private void validarTipoOcorrencia(RegistroOcorrenciaDTO registroOcorrenciaDTO) throws ValidacaoException {

        if (!SolicitacaoAgendamentoOcorrencia.TipoOcorrencia.CONFIRMACAO_CONTATO.value().equals(registroOcorrenciaDTO.getTipoOcorrencia())
                && !SolicitacaoAgendamentoOcorrencia.TipoOcorrencia.LANCAR_HISTORICO.value().equals(registroOcorrenciaDTO.getTipoOcorrencia())) {
            throw new ValidacaoException(Bundle.getStringApplication("rotulo_tipo_ocorrencia_invalido"));
        }
    }

    private void gerarOcorrencia(RegistroOcorrenciaDTO registroOcorrenciaDTO, SolicitacaoAgendamento solicitacaoAgendamento, java.util.Date dataSolicitacao) throws DAOException, ValidacaoException {
        BOFactory.getBO(AgendamentoFacade.class)
                .gerarOcorrenciaSolicitacaoAgendamento(
                        SolicitacaoAgendamentoOcorrencia.TipoOcorrencia.SOLICITACAO,
                        Bundle.getStringApplication("rotulo_contato_X", registroOcorrenciaDTO.getDescricao()),
                        solicitacaoAgendamento,
                        dataSolicitacao
                );
        solicitacaoAgendamento.setDataUltimoContato(dataSolicitacao);
        //É somado 100 ao valor do status para que na classe SaveSolicitacaoAgendamento seja possivel pular a validação do metodo validaProcedimento;
        solicitacaoAgendamento.setStatus(solicitacaoAgendamento.getStatus() + 100);
        BOFactory.save(solicitacaoAgendamento);
    }

    private void confirmarContato(RegistroOcorrenciaDTO registroOcorrenciaDTO, java.util.Date dataSolicitacao) throws ValidacaoException, DAOException {
        BOFactory.getBO(AgendamentoFacade.class).confirmarContatoSolicitacaoAgendamento(registroOcorrenciaDTO.getIdSolicitacao(), registroOcorrenciaDTO.getDescricao(), dataSolicitacao);
    }

    private java.util.Date validarData(String data) throws ValidacaoException {
        java.util.Date dataFormatada;

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm");
            sdf.setLenient(false);
            dataFormatada = sdf.parse(data);

        } catch (ParseException e) {
            throw new ValidacaoException(Bundle.getStringApplication("rotulo_formato_data_invalido"));
        }

        if (DataUtil.isDateAfter(dataFormatada, DataUtil.getDataAtual())) {
            throw new ValidacaoException(Bundle.getStringApplication("rotulo_maior_data_atual"));
        }

        return dataFormatada;
    }

    private SolicitacaoAgendamento validaSolicitacao(Long idSolicitacao) throws ValidacaoException {
        SolicitacaoAgendamento solicitacaoAgendamento = LoadManager.getInstance(SolicitacaoAgendamento.class)
                .addProperties(new HQLProperties(SolicitacaoAgendamento.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamento.PROP_CODIGO, idSolicitacao))
                .setMaxResults(1)
                .start().getVO();

        if (Objects.isNull(solicitacaoAgendamento)) {
            throw new ValidacaoException(Bundle.getStringApplication("rotulo_solicitacao_inexistente"));
        }

        if (SolicitacaoAgendamento.STATUS_CANCELADO.equals(solicitacaoAgendamento.getStatus())) {
            throw new ValidacaoException(Bundle.getStringApplication("rotulo_ag_solicitacao_cancelada"));
        }

        return solicitacaoAgendamento;
    }

    private List<AgendaDTO> getHorariosList(Long codigoUnidade, Long codigoProcedimento) throws ValidacaoException, DAOException {
        List<AgendaDTO> agendaDTOList = getAgendaFacade()
                .findAgendaDisponivelByProcedimentoUnidade(codigoUnidade, codigoProcedimento);

        if (isDisponibilizaAgendaUnidadeReferencia() && (agendaDTOList == null || agendaDTOList.isEmpty())) {
            agendaDTOList = getAgendaFacade()
                    .findAgendaDisponivelByProcedimentoUnidade(codigoUnidade, codigoProcedimento,
                            TipoAtendimentoAgenda.TipoAtendimento.APP_CIDADAO_GERAL.value());
        }
        return agendaDTOList == null ? new ArrayList<>() : agendaDTOList;
    }

    private ScheduleDTO getScheduleFromAgendaDTO(AgendaDTO agendaDTO) {
        ScheduleDTOMapper scheduleDTOMapper = new ScheduleDTOMapper();
        return scheduleDTOMapper.fromAgendaDTO(agendaDTO).map();
    }

    private TimeScheduleDTO agendaDTOToTimeSchedule(AgendaDTO agendaDTO) {
        TimeScheduleDTOMapper timeScheduleDTOMapper = new TimeScheduleDTOMapper();
        return timeScheduleDTOMapper
                .fromAgendaDTO(agendaDTO)
                .map();
    }

    private String getChaveAgenda(AgendaDTO agendaDTO) {
        return agendaDTO.getIdAgenda().toString() + agendaDTO.getIdAgendaGrade().toString() + agendaDTO.getIdAgendaGradeAtendimento();
    }

    private void validaBuscaHorario(Long codigoProcedimento) throws ValidacaoException {
        if (codigoProcedimento == null)
            throw new ValidacaoException("É obrigatório filtrar por procedimento");
    }

    public AgendaGradeAtendimentoDTO getAgendaGradeAtendimentoDTO(Long codigoAgendaGradeAtendimento, Long codigoTipoProcedimento, Long codigoAgendaGradeHorario) throws DAOException {
        AgendaGradeAtendimentoDTO agendaGradeAtendimentoDTO = new AgendaGradeAtendimentoDTO();

        agendaGradeAtendimentoDTO.setAgendaGradeAtendimento(this.findAgendaGradeAtendimentoByCodigo(codigoAgendaGradeAtendimento));
        agendaGradeAtendimentoDTO.setTipoProcedimento(procedimentoService.findTipoProcedimentoOnlyCodigoById(codigoTipoProcedimento));
        agendaGradeAtendimentoDTO.setAgendaGradeHorario(this.findAgendaGradeHorarioByCodigo(codigoAgendaGradeHorario, true));
        return agendaGradeAtendimentoDTO;
    }

    public AgendaGradeAtendimento findAgendaGradeAtendimentoByCodigo(Long codigo) {
        AgendaGradeAtendimento agendaGradeAtendimento = on(AgendaGradeAtendimento.class);
        return loadManagerService.getInstance(AgendaGradeAtendimento.class)
                .addProperties(loadManagerService.getHqlProperties(AgendaGradeAtendimento.class))
                .addProperty(path(agendaGradeAtendimento.getTipoAtendimentoAgenda().getTipoAtendimento()))
                .addProperties(loadManagerService.getHqlProperties(AgendaGrade.class, path(agendaGradeAtendimento.getAgendaGrade())))
                .addProperties(loadManagerService.getHqlProperties(Agenda.class, path(agendaGradeAtendimento.getAgendaGrade().getAgenda())))
                .addProperties(loadManagerService.getHqlProperties(Profissional.class, path(agendaGradeAtendimento.getAgendaGrade().getAgenda().getProfissional())))
                .setId(codigo)
                .startLeitura()
                .getVO();
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public AgendaGradeHorario findAgendaGradeHorarioByCodigo(Long codigo, Boolean bloqueiaHorario) throws DAOException {
        //Controle de concorrência, caso de agendamento no mesmo horário.
        if (bloqueiaHorario) {
            Session session = HibernateSessionFactory.getSession();
            session.createCriteria(AgendaGradeHorario.class)
                    .add(Restrictions.eq(AgendaGradeHorario.PROP_CODIGO, codigo))
                    .setLockMode(LockMode.PESSIMISTIC_WRITE)
                    .setMaxResults(1)
                    .uniqueResult();
            session.flush();
            session.clear();
        }

        LoadManager loadManager = loadManagerService.getInstance(AgendaGradeHorario.class)
                .addProperties(loadManagerService.getHqlProperties(AgendaGradeHorario.class))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_AGENDA_GRADE,
                                AgendaGrade.PROP_AGENDA,
                                Agenda.PROP_CODIGO))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_AGENDA_GRADE,
                                AgendaGrade.PROP_AGENDA,
                                Agenda.PROP_EMPRESA,
                                Empresa.PROP_CODIGO))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_AGENDA_GRADE,
                                AgendaGrade.PROP_AGENDA,
                                Agenda.PROP_EMPRESA,
                                Empresa.PROP_DESCRICAO))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_AGENDA_GRADE,
                                AgendaGrade.PROP_AGENDA,
                                Agenda.PROP_PROFISSIONAL,
                                Profissional.PROP_CODIGO))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_AGENDA_GRADE,
                                AgendaGrade.PROP_AGENDA,
                                Agenda.PROP_PROFISSIONAL,
                                Profissional.PROP_NOME))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_AGENDA_GRADE,
                                AgendaGrade.PROP_AGENDA,
                                Agenda.PROP_TIPO_PROCEDIMENTO,
                                TipoProcedimento.PROP_CODIGO))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_AGENDA_GRADE,
                                AgendaGrade.PROP_AGENDA,
                                Agenda.PROP_TIPO_PROCEDIMENTO,
                                TipoProcedimento.PROP_DESCRICAO))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_AGENDA_GRADE,
                                AgendaGrade.PROP_CODIGO))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_CODIGO))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_TEMPO_MEDIO))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_QUANTIDADE_ATENDIMENTO_ORIGINAL));

        if (bloqueiaHorario) {
            //Carrega apenas se o horário estiver disponível
            loadManager.addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeHorario.PROP_STATUS, AgendaGradeHorario.Status.PENDENTE.value()));
        }

        return loadManager.setId(codigo)
                .start()
                .getVO();
    }

    public boolean existsAgendaGradeAtendimentoHorarioByCodigo(Long codigo) {
        return loadManagerService.getInstance(AgendaGradeAtendimentoHorario.class)
                .setId(codigo)
                .startLeitura()
                .exists();
    }

    public AgendaGradeAtendimentoHorario getAgendaGradeAtendimentoHorarioByCodigoSimplified(Long codigo) {
        AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorarioProxy = on(AgendaGradeAtendimentoHorario.class);

        return loadManagerService.getInstance(AgendaGradeAtendimentoHorario.class)
                .addProperty(path(agendaGradeAtendimentoHorarioProxy.getCodigo()))
                .addProperty(path(agendaGradeAtendimentoHorarioProxy.getAgendaGradeAtendimento().getTipoAtendimentoAgenda().getTipoAtendimento()))
                .setId(codigo)
                .start()
                .getVO();
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public AgendaGradeAtendimentoHorario findAgendaGradeAtendimentoHorarioByCodigo(Long codigo) {
        return loadManagerService.getInstance(AgendaGradeAtendimentoHorario.class)
                .addProperties(loadManagerService.getHqlProperties(AgendaGradeAtendimentoHorario.class))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_AGENDA_GRADE,
                                AgendaGrade.PROP_AGENDA,
                                Agenda.PROP_CODIGO))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_AGENDA_GRADE,
                                AgendaGrade.PROP_AGENDA,
                                Agenda.PROP_EMPRESA,
                                Empresa.PROP_CODIGO))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_AGENDA_GRADE,
                                AgendaGrade.PROP_AGENDA,
                                Agenda.PROP_EMPRESA,
                                Empresa.PROP_DESCRICAO))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_AGENDA_GRADE,
                                AgendaGrade.PROP_AGENDA,
                                Agenda.PROP_PROFISSIONAL,
                                Profissional.PROP_CODIGO))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_AGENDA_GRADE,
                                AgendaGrade.PROP_AGENDA,
                                Agenda.PROP_PROFISSIONAL,
                                Profissional.PROP_NOME))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_AGENDA_GRADE,
                                AgendaGrade.PROP_AGENDA,
                                Agenda.PROP_TIPO_PROCEDIMENTO,
                                TipoProcedimento.PROP_CODIGO))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_AGENDA_GRADE,
                                AgendaGrade.PROP_AGENDA,
                                Agenda.PROP_TIPO_PROCEDIMENTO,
                                TipoProcedimento.PROP_DESCRICAO))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_AGENDA_GRADE,
                                AgendaGrade.PROP_CODIGO))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_CODIGO))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_TEMPO_MEDIO))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_QUANTIDADE_ATENDIMENTO_ORIGINAL))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_TIPO_ATENDIMENTO_AGENDA,
                                TipoAtendimentoAgenda.PROP_CODIGO))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_TIPO_ATENDIMENTO_AGENDA,
                                TipoAtendimentoAgenda.PROP_TIPO_ATENDIMENTO))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_HORARIO,
                                AgendaGradeHorario.PROP_CODIGO))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_HORARIO,
                                AgendaGradeHorario.PROP_HORA))
                .addProperty(VOUtils
                        .montarPath(
                                AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_HORARIO,
                                AgendaGradeHorario.PROP_STATUS))
                .setId(codigo)
                .start()
                .getVO();
    }

    public void updateSchedule(ScheduleDTO scheduleDTO, Long cityId) {
        quarkusScheduleClient.updateSchedule(scheduleDTO, cityId);
    }

    public String getComprovanteAgendamento(Long cdAgendamento, String ipClient, String tipoRetorno) throws ReportException, JRException, IOException, ValidacaoException {
        if (cdAgendamento == null) {
            throw new ValidacaoException("Código de agendamento é obrigatório");
        }

        List<AgendaGradeAtendimentoHorario> agendamentosList = agendamentosList(cdAgendamento);

        if (CollectionUtils.isNotNullEmpty(agendamentosList)) {
            for (AgendaGradeAtendimentoHorario agendamento : agendamentosList) {
                if (AgendaGradeAtendimentoHorario.STATUS_CANCELADO.equals(agendamento.getStatus())) {
                    throw new ValidacaoException("O agendamento informado está com status cancelado.");
                }
            }
            RelatorioImprimirComprovanteAgendamentoDTOParam reportParam = new RelatorioImprimirComprovanteAgendamentoDTOParam();
            reportParam.setAgendaGradeAtendimentoHorarioList(agendamentosList);

            SessaoUtil.createApplicationSession(ipClient);

            DataReport dataReport = BOFactory.getBO(AtendimentoReportFacade.class).relatorioImprimirComprovanteAgendamentoSemSolicitacao(reportParam);

            return exportComprovanteAgendamento(dataReport, tipoRetorno);
        } else {
            throw new ValidacaoException("Nenhum registro foi encontrado com o código informado.");
        }
    }

    private List<AgendaGradeAtendimentoHorario> agendamentosList(Long cdAgendamento) {
        return LoadManager.getInstance(AgendaGradeAtendimentoHorario.class)
                .addProperties(new HQLProperties(AgendaGradeAtendimentoHorario.class).getProperties())
                .addProperties(new HQLProperties(UsuarioCadsus.class, VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_USUARIO_CADSUS)).getProperties())
                .addProperties(new HQLProperties(TipoProcedimento.class, VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO, AgendaGradeAtendimento.PROP_AGENDA_GRADE, AgendaGrade.PROP_AGENDA, Agenda.PROP_TIPO_PROCEDIMENTO)).getProperties())
                .addProperties(new HQLProperties(GerenciadorArquivo.class, AgendaGradeAtendimentoHorario.PROP_GERENCIADOR_ARQUIVO).getProperties())
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO, AgendaGradeAtendimento.PROP_AGENDA_GRADE, AgendaGrade.PROP_AGENDA, Agenda.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_CODIGO, cdAgendamento))
                .start().getList();
    }

    private String exportComprovanteAgendamento(DataReport dataReport, String tipoRetorno) throws JRException, IOException {
        File tempFile = File.createTempFile(UUID.randomUUID().toString(), ".pdf");
        JasperExportManager.exportReportToPdfFile(dataReport.getJasperPrint(), tempFile.getAbsolutePath());
        if (tipoRetorno != null && tipoRetorno.equalsIgnoreCase("link")) {
            return FileUtils.sendToBucket(tempFile);
        }
        return FileUtils.getFileToBase64(tempFile);
    }

    private AgendaFacade getAgendaFacade() {
        return boFactoryService.getBO(AgendaFacade.class);
    }

    public boolean isDisponibilizaAgendaUnidadeReferencia() {
        String disponibilizaUnidade = null;
        try {
            disponibilizaUnidade = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MOBILE).getParametro("DisponibilizaAgendaUnidadeReferencia");
        } catch (Exception e) {
            Loggable.log.error(e.getMessage());
        }
        return RepositoryComponentDefault.SIM.equals(disponibilizaUnidade);
    }

}
