<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>saude-2</artifactId>
        <groupId>br.com.celk</groupId>
<version>3.1.285.1-SNAPSHOT</version>
    </parent>

    <artifactId>saude-core</artifactId>
    <name>saude-core</name>

    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <targetPath>hbms</targetPath>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>../resource</directory>
                <excludes>
                    <exclude>**/*.*</exclude>
                </excludes>
            </resource>
        </resources>

        <plugins>
            <!--Criar Arquivo de sql da versão, para executar: mvn antrun:run-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-cli</id>
                        <configuration>
                            <target>
                                <echo message="Create file sql version..."/>
                                <property name="file.version" value="${project.version}"/>
                                <echo file="src/main/resources/db/migration/tempFile.txt"
                                      message="versiontmp=${file.version}"/>
                                <replaceregexp file="src/main/resources/db/migration/tempFile.txt"
                                               match="(.*)(-SNAPSHOT)" replace="\1"/>
                                <replaceregexp file="src/main/resources/db/migration/tempFile.txt" match="([.])"
                                               replace="_" flags="g"/>
                                <loadproperties srcFile="src/main/resources/db/migration/tempFile.txt"/>
                                <touch file="src/main/resources/db/migration/V${versiontmp}__b_${project.version}.sql"/>
                                <delete file="src/main/resources/db/migration/tempFile.txt"/>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.zeroturnaround</groupId>
                <artifactId>jrebel-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <!-- //////////////////////JASPER/////////////////////////// -->
        <dependency>
            <groupId>org.jfree</groupId>
            <artifactId>jfreechart</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-digester</groupId>
            <artifactId>commons-digester</artifactId>
        </dependency>
        <dependency>
            <groupId>br.com.celk.jasper</groupId>
            <artifactId>commons-javaflow</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
        </dependency>

        <dependency>
            <groupId>br.com.celk.jasper</groupId>
            <artifactId>jasperreports-javaflow</artifactId>
        </dependency>

        <dependency>
            <groupId>br.com.celk.jasper</groupId>
            <artifactId>jasperreports-fonts</artifactId>
        </dependency>

        <dependency>
            <groupId>jasper</groupId>
            <artifactId>batik-awt-util</artifactId>
        </dependency>
        <dependency>
            <groupId>jasper</groupId>
            <artifactId>batik-bridge</artifactId>
        </dependency>
        <dependency>
            <groupId>jasper</groupId>
            <artifactId>batik-css</artifactId>
        </dependency>
        <dependency>
            <groupId>jasper</groupId>
            <artifactId>batik-dom</artifactId>
        </dependency>
        <dependency>
            <groupId>jasper</groupId>
            <artifactId>batik-gvt</artifactId>
        </dependency>
        <dependency>
            <groupId>jasper</groupId>
            <artifactId>batik-script</artifactId>
        </dependency>
        <dependency>
            <groupId>jasper</groupId>
            <artifactId>batik-util</artifactId>
        </dependency>
        <dependency>
            <groupId>jasper</groupId>
            <artifactId>xml-apis-ext</artifactId>
        </dependency>
        <dependency>
            <groupId>jasper</groupId>
            <artifactId>batik-svg-dom</artifactId>
        </dependency>
        <dependency>
            <groupId>jasper</groupId>
            <artifactId>batik-ext</artifactId>
        </dependency>
        <dependency>
            <groupId>jasper</groupId>
            <artifactId>batik-xml</artifactId>
        </dependency>
        <dependency>
            <groupId>jasper</groupId>
            <artifactId>batik-parser</artifactId>
        </dependency>
        <dependency>
            <groupId>jasper</groupId>
            <artifactId>batik-anim</artifactId>
        </dependency>

        <dependency>
            <groupId>net.sf.barcode4j</groupId>
            <artifactId>barcode4j</artifactId>
        </dependency>
        <!-- /////////////////////////////////////////////////////// -->
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.17.2</version>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <!--<scope>provided</scope> SE NAO FOR ADICIONADO AO LIB DO EAR QUANDO COMMONS LANG PRECISA INTANCIAR UMA CLASSE DE SAUDE-CORE OCORRE UM CLASSNOTFOUDEXCEPTION-->
        </dependency>
        <dependency>
            <groupId>commons</groupId>
            <artifactId>i18n</artifactId>
        </dependency>
        <dependency>
            <artifactId>commons-collections</artifactId>
            <groupId>commons-collections</groupId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <artifactId>commons-beanutils</artifactId>
            <groupId>commons-beanutils</groupId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>jasper</groupId>
            <artifactId>itext</artifactId>
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ibm.icu</groupId>
            <artifactId>icu4j</artifactId>
        </dependency>
        <dependency>
            <groupId>other</groupId>
            <artifactId>josql</artifactId>
        </dependency>

        <dependency>
            <groupId>org.picketbox</groupId>
            <artifactId>picketbox</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>bsf</groupId>
            <artifactId>bsf</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>javax.mail</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jboss.spec.javax.annotation</groupId>
            <artifactId>jboss-annotations-api_1.2_spec</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.jboss.spec.javax.ejb</groupId>
            <artifactId>jboss-ejb-api_3.2_spec</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.jboss.spec.javax.security.jacc</groupId>
            <artifactId>jboss-jacc-api_1.5_spec</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.jboss.spec.javax.jms</groupId>
            <artifactId>jboss-jms-api_2.0_spec</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.jboss.as</groupId>
            <artifactId>jboss-as-messaging</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-core</artifactId>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>xml-apis</artifactId>
                    <groupId>xml-apis</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>
        <dependency>
            <groupId>web</groupId>
            <artifactId>jsf-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jzlib</artifactId>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>rhino</groupId>
            <artifactId>js</artifactId>
        </dependency>
        <dependency>
            <groupId>br.com.ksisolucoes</groupId>
            <artifactId>integracao-SIGTAP</artifactId>
        </dependency>
        <dependency>
            <groupId>other</groupId>
            <artifactId>gentlyweb-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.linuxense</groupId>
            <artifactId>javadbf</artifactId>
        </dependency>
        <dependency>
            <artifactId>spring-core</artifactId>
            <groupId>org.springframework</groupId>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>saude-ejb-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>saude-sms</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpcore</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.ws.rs</groupId>
                    <artifactId>jsr311-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>saude-utils</artifactId>
        </dependency>

        <dependency>
            <groupId>com.googlecode.lambdaj</groupId>
            <artifactId>lambdaj</artifactId>
        </dependency>

        <dependency>
            <groupId>com.googlecode.lambdaj</groupId>
            <artifactId>lambdaj</artifactId>
            <classifier>javadoc</classifier>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-hibernate4</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-core</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-annotations</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-databind</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>jnlp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.camel</groupId>
            <artifactId>camel-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.camel</groupId>
            <artifactId>camel-csv</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.camel</groupId>
            <artifactId>camel-bindy</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache-extras.camel-extra</groupId>
            <artifactId>camel-jboss6</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.camel</groupId>
            <artifactId>camel-stream</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.camel</groupId>
            <artifactId>camel-ejb</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jboss.spec.javax.websocket</groupId>
            <artifactId>jboss-websocket-api_1.0_spec</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>ziputils</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-jaxrs</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-httpclient</groupId>
                    <artifactId>commons-httpclient</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpclient</artifactId>
                </exclusion>
            </exclusions>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-multipart-provider</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-client</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>jaxrs-api</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-sns</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-dynamodb</artifactId>
        </dependency>
        <dependency>
            <groupId>net.lingala.zip4j</groupId>
            <artifactId>zip4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jrimum</groupId>
            <artifactId>jrimum-bopepo</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jrimum</groupId>
            <artifactId>jrimum-domkee</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jrimum</groupId>
            <artifactId>jrimum-vallia</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jrimum</groupId>
            <artifactId>jrimum-utilix</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jrimum</groupId>
            <artifactId>jrimum-texgit</artifactId>
        </dependency>

        <!-- ############################# -->
        <!-- DEPENDENCIAS PARA A API PDFBOX -->
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk15on</artifactId>
        </dependency>
        <!-- ############################# -->
        <!-- Begin - FCM client sender for mobile push notifications-->
        <dependency>
            <groupId>de.bytefish.fcmjava</groupId>
            <artifactId>fcmjava-core-java7</artifactId>
        </dependency>
        <dependency>
            <groupId>de.bytefish.fcmjava</groupId>
            <artifactId>fcmjava-client-java7</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
        </dependency>
        <!-- End - FCM client sender for mobile push notifications-->
        <!-- Begin AmazonAWS SQS-->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-sqs</artifactId>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>amazon-sqs-java-messaging-lib</artifactId>
        </dependency>   
        <dependency>
            <groupId>javax.inject</groupId>
            <artifactId>javax.inject</artifactId>
        </dependency>
        <!--End AmazonAWS SQS-->

        <!-- Begin CADSUS PIX/PDQ -->
        <dependency>
            <groupId>br.com.celk.cadsus_v5</groupId>
            <artifactId>cadsus_v5</artifactId>
        </dependency>
        <dependency>
            <groupId>br.com.celk.cadsus_pixpdq</groupId>
            <artifactId>cadsus_pixpdq</artifactId>
        </dependency>
        <!-- End CADSUS PIX/PDQ -->
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
        </dependency>

        <dependency>
            <groupId>org.wicketstuff</groupId>
            <artifactId>wicketstuff-gmap3</artifactId>
        </dependency>

        <dependency>
            <groupId>br.com.celk.boleto</groupId>
            <artifactId>integracao</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.jboss.resteasy</groupId>
                    <artifactId>resteasy-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>br.com.celk.boleto</groupId>
            <artifactId>common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- lib de criptografia -->
        <dependency>
            <groupId>org.jasypt</groupId>
            <artifactId>jasypt</artifactId>
            <scope>compile</scope>
        </dependency>

        <!-- iText Core -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext7-core</artifactId>
            <version>7.1.18</version>
            <type>pom</type>
        </dependency>

        <!-- iText pdfHTML add-on -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>html2pdf</artifactId>
            <version>2.1.7</version>
        </dependency>

        <!-- Test -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.axis</groupId>
            <artifactId>axis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>
    </dependencies>
</project>
