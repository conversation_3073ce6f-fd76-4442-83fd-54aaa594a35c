# DEBUG
# INFO
# WARN
# ERROR
# FATAL

log4j.rootLogger= ERROR, console
log4j.rootCategory= ERROR, console

log4j.appender.console=org.apache.log4j.ConsoleAppender
log4j.appender.console.layout= org.apache.log4j.PatternLayout
log4j.appender.console.layout.ConversionPattern=%d{ISO8601} [%c] %F:%L %-5p - %m%n

#log4j.appender.arquivo=org.apache.log4j.RollingFileAppender
#log4j.appender.arquivo.File=log/app.log
#log4j.appender.arquivo.MaxFileSize=100KB
#log4j.appender.arquivo.ImmediateFlush=true
# Keep one backup file
#log4j.appender.arquivo.MaxBackupIndex=2
#log4j.appender.arquivo.layout=org.apache.log4j.PatternLayout
#log4j.appender.arquivo.layout.ConversionPattern=%d [%t] %-5p %-5c{3}(%L) %x -> %m%n
#log4j.appender.arquivo.layout.ConversionPattern=%d{ISO8601} [%c] %F:%L %-5p -> %m%n

#Geral
log4j.logger.ksisolucoes=DEBUG
log4j.logger.br.com.ksisolucoes=DEBUG
#-------------------------------------------------------------------------------

#Log all SQL DML statements as they are executed
log4j.logger.org.hibernate.SQL=ERROR
#Log all JDBC parameters
log4j.logger.org.hibernate.type=ERROR
#Log all SQL DDL statements as they are executed
log4j.logger.org.hibernate.tool.hbm2ddl=ERROR
#Log the state of all entities (max 20 entities) associated with the session at flush time
log4j.logger.org.hibernate.pretty=ERROR
#Log all second-level cache activity
log4j.logger.org.hibernate.cache=ERROR
#Log transaction related activity
log4j.logger.org.hibernate.transaction=ERROR
#Log all JDBC resource acquisition
log4j.logger.org.hibernate.jdbc=ERROR
#Log HQL and SQL ASTs during query parsing
log4j.logger.org.hibernate.hql.ast.AST=ERROR
#Log all JAAS authorization requests
log4j.logger.org.hibernate.secure=ERROR
#Log everything (a lot of information, but very useful for troubleshooting)
log4j.logger.org.hibernate=WARN

