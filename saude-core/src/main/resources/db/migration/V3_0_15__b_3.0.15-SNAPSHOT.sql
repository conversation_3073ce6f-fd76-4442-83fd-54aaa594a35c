/*
 PostgreSQL
 Everton - 24/03/2014 - #7331
*/

ALTER TABLE atendimento_transferencia_setor ADD motivo_transferencia VARCHAR(512)         null;
ALTER TABLE atendimento_transferencia_setor ADD nr_atendimento_destino INT8                 null;
alter table atendimento_transferencia_setor
   add constraint FK_ATE_TR_SETOR_REF_ATE_DES foreign key (nr_atendimento_destino)
      references atendimento (nr_atendimento)
      on delete restrict on update restrict;

alter table atendimento_transferencia_setor
   add constraint FK_ATE_TR_SETOR_REF_NT_PROC foreign key (cd_nat_procura)
      references natureza_procura (cd_nat_procura)
      on delete restrict on update restrict;

alter table atendimento_transferencia_setor
   add constraint FK_ATE_TR_SETOR_REF_TP_AT foreign key (cd_tp_atendimento)
      references tipo_atendimento (cd_tp_atendimento)
      on delete restrict on update restrict;

/*
 PostgreSQL
 Everton - 26/03/2014 - #7336
*/

/*==============================================================*/
/* Table: ordem_compra_elo_nota                                 */
/*==============================================================*/
create table ordem_compra_elo_nota (
cd_elo_nota_oc       INT8                 not null,
cd_reg_it_nf         INT8                 not null,
cd_oc_item           INT8                 not null,
quantidade           NUMERIC(12,2)        not null,
version              INT8                 not null
);

alter table ordem_compra_elo_nota
   add constraint PK_ORDEM_COMPRA_ELO_NOTA primary key (cd_elo_nota_oc);

alter table ordem_compra_elo_nota
   add constraint FK_OC_ELO_NF_REF_NOTA foreign key (cd_reg_it_nf)
      references registro_itens_nota_fiscal (cd_reg_it_nf)
      on delete restrict on update restrict;

alter table ordem_compra_elo_nota
   add constraint FK_OC_ELO_NF_REF_OC foreign key (cd_oc_item)
      references ordem_compra_item (cd_oc_item)
      on delete restrict on update restrict;

/*
    Sulivan - 27/03/2014 - #7336
*/
INSERT INTO programa_pagina VALUES (630, 'br.com.celk.view.materiais.recebimento.notafiscalentrada.CadastroNotaFiscalStep4Page', 'N');
INSERT INTO programa_pagina VALUES (631, 'br.com.celk.view.materiais.recebimento.notafiscalentrada.CadastroNotaFiscalStep5Page', 'N');
INSERT INTO programa_web_pagina VALUES (635, 97, 630);
INSERT INTO programa_web_pagina VALUES (636, 97, 631);

/*
    Pietro - 27/03/2014 - #7334
*/
INSERT INTO programa_pagina VALUES (629, 'br.com.celk.view.materiais.compras.relatorio.RelatorioRelacaoOrdemCompraPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (359, 'Relação da Ordem de Compra', 629, 'N');
INSERT INTO programa_web_pagina VALUES (634, 359, 629);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (499,'Relatório','relatorio',490,null,14,0);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (500,'Relação da Ordem de Compra','relacaoOrdemCompra',499,359,14,0);

/*
    Laudecir - 27/03/2014 - #7340
*/
INSERT INTO programa_pagina VALUES (632, 'br.com.celk.view.consorcio.pedidotransferencialicitacao.geracaoordemcompra.GeracaoOrdemCompraPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (360, 'Geração da Ordem de Compra', 632, 'N');
INSERT INTO programa_web_pagina VALUES (637, 360, 632);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (501, 'Geração da Ordem de Compra', 'geracaoOrdemCompra', 301, 360, 111, 0);

/*
    PostgreSQL
    Everton - 31/03/2014 - #7340
*/
alter table ped_transf_licitacao add flag_oc INT2 null;

/*
    PostgreSQL
    Everton - 31/03/2014 - #7344
*/
alter table licitacao_item add nr_item_pregao VARCHAR(10) null;

/*
    Pietro - 28/03/2014 - #7335
*/
INSERT INTO programa_pagina VALUES (633, 'br.com.celk.view.materiais.compras.relatorio.ResumoRelacaoOrdemCompraPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (361, 'Resumo da Ordem de Compra', 633, 'N');
INSERT INTO programa_web_pagina VALUES (638, 361, 633);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (502,'Resumo da Ordem de Compra','resumoOrdemCompra',499,361,14,0);

/*
    PostgreSQL
    Everton - 01/04/2014 - #7378
*/

alter table tipo_procedimento add flag_ctr_lote        INT2;
update tipo_procedimento set flag_ctr_lote = 1 where coalesce(tfd, 'N') = 'N';
update tipo_procedimento set flag_ctr_lote = 0 where tfd = 'S';
alter table tipo_procedimento alter flag_ctr_lote set not null;

/*
    Sulivan - 01/04/2014 - #7378
*/
update menu_web set cd_menu_pai = 473, cd_menu_modulo = 3 where cd_menu = 462;

/*
    PostgreSQL
    Everton - 02/04/2014 - #7348
*/

/*==============================================================*/
/* Table: sms_controle_integracao                               */
/*==============================================================*/
create table sms_controle_integracao (
cd_sms_integracao    INT8                 not null,
cd_ag_gra_ate_hor    INT8                 null,
cd_sms_mensagem      INT8                 not null,
cd_solicitacao       INT8                 null,
tp_mensagem          INT2                 not null,
status_sms           INT2                 not null,
version              INT8                 not null
);

alter table sms_controle_integracao
   add constraint PK_SMS_CONTROLE_INTEGRACAO primary key (cd_sms_integracao);

alter table sms_controle_integracao
   add constraint FK_SMS_INT_REF_AG_ATE_HR foreign key (cd_ag_gra_ate_hor)
      references agenda_gra_ate_horario (cd_ag_gra_ate_hor)
      on delete restrict on update restrict;

alter table sms_controle_integracao
   add constraint FK_SMS_INT_REF_SMS_MSG foreign key (cd_sms_mensagem)
      references sms_mensagem (cd_sms_mensagem)
      on delete restrict on update restrict;

alter table sms_controle_integracao
   add constraint FK_SMS_INT_REF_SOL_AG foreign key (cd_solicitacao)
      references solicitacao_agendamento (cd_solicitacao)
      on delete restrict on update restrict;

ALTER TABLE agenda_gra_ate_horario ADD cd_sms_integracao    INT8                 null;

alter table agenda_gra_ate_horario
   add constraint FK_AG_HR_REF_SMS_INT foreign key (cd_sms_integracao)
      references sms_controle_integracao (cd_sms_integracao)
      on delete restrict on update restrict;

ALTER TABLE agenda_gra_ate_horario DROP dt_envio_sms;

/*
    Sulivan - 02/04/2014 - #7348
*/
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (503,'SMS','sms',102,null,null,0);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (504,'Processo','processo',503,null,102,0);
update programa_pagina set cam_pagina = 'br.com.celk.view.service.sms.IntegracaoSmsPage' where cd_prg_pagina = 547;
update programa_web set ds_prg_web = 'Integração SMS' where cd_prg_web = 321;
update menu_web set ds_menu = 'Integração SMS', ds_bundle = 'integracaoSms', cd_menu_pai = 504, cd_menu_modulo = 102 where cd_menu = 445;

/*
    Pietro - 03/04/2014 - #7363
*/
INSERT INTO programa_pagina VALUES (634, 'br.com.celk.view.service.sms.manutencao.ManutencaoSmsAgendamentosPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (362, 'Manutenção de SMS dos Agendamentos', 634, 'N');
INSERT INTO programa_web_pagina VALUES (639, 362, 634);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (505,'Manutenção de SMS dos Agendamentos','manutencaoSmsAgendamentos',504,362,102,0);

/*
    PostgreSQL
    Everton - 03/04/2014 - #7364
*/
alter table sms_controle_integracao add status_resposta INT2 null;
