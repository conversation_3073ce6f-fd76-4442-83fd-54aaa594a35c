SET application_name = 'flyway|*********';

/*
    Sulivan - 18/07/2019 #25810
*/
update parametro_gem set observacao = 'Define o tipo de controle que será utilizado para os processos de encaminhamento e regulação: TIPO PROCEDIMENTO: No momento da solicitação, será possível definir a prioridade da solicitação: Eletivo, Brevidade ou Urgente. SOLICITAÇÃO DE PRIORIDADE: No momento da solicitação, não será possível definir a prioridade, apenas solicitar para a regulação uma avaliação de prioridade, que poderá ser atendida ou não pelo regulador. SOLICITAÇÃO DO PROFISSIONAL: No momento da solicitação, não será possível definir a prioridade, apenas indicar se a solicitação deverá ir para a fila cronológica ou para a avaliação do regulador.' where parametro = 'tipoControleRegulação';

alter table encaminhamento add column flag_enviar_regulacao INT2;
alter table auditschema.encaminhamento add column flag_enviar_regulacao INT2;
alter table encaminhamento disable trigger user;
update encaminhamento set flag_enviar_regulacao = 0;
alter table encaminhamento alter column flag_enviar_regulacao set not null;
alter table encaminhamento enable trigger user;

alter table encaminhamento add column ds_enviar_regulacao VARCHAR;
alter table auditschema.encaminhamento add column ds_enviar_regulacao VARCHAR;

alter table solicitacao_agendamento add column flag_enviar_regulacao INT2;
alter table auditschema.solicitacao_agendamento add column flag_enviar_regulacao INT2;
alter table solicitacao_agendamento disable trigger user;
update solicitacao_agendamento set flag_enviar_regulacao = 0;
alter table solicitacao_agendamento alter column flag_enviar_regulacao set not null;
alter table solicitacao_agendamento enable trigger user;

alter table solicitacao_agendamento add column ds_enviar_regulacao VARCHAR;
alter table auditschema.solicitacao_agendamento add column ds_enviar_regulacao VARCHAR;

/*
    Silvio - #26137 - 25/07/2019
*/
INSERT INTO programa_pagina VALUES (1717, 'br.com.celk.view.agenda.agendamento.relatorio.RelatorioRelacaoVagasAgendaPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (953, 'Relação das Vagas nas Agendas', 1717, 'N');
INSERT INTO programa_web_pagina VALUES (1831, 953, 1717);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1205,'Relação das Vagas nas Agendas','',160,953,3,0,0);

/*
    Silvio - #26168 - 26/07/2019
*/
ALTER TABLE laudo_medicamentos_especiais ADD COLUMN peso numeric(12,2);
ALTER TABLE auditschema.laudo_medicamentos_especiais ADD COLUMN peso numeric(12,2);
ALTER TABLE laudo_medicamentos_especiais ADD COLUMN altura numeric(12,2);
ALTER TABLE auditschema.laudo_medicamentos_especiais ADD COLUMN altura numeric(12,2);

/*
    Elton 01/08/2019 - #26874
*/
INSERT INTO permissao_web values (147, 'Não Permite Enviar Pedido', 0);
INSERT INTO programa_pagina_permissao VALUES(514, 147, 8, 0, 'naoPermiteEnviarPedido');

/*
    Everton 05/08/2019 - #25627
*/

ALTER TABLE usuario_cadsus DISABLE TRIGGER USER;
update usuario_cadsus t1 set cd_municipio_residencia = t2.cod_cid
    from endereco_usuario_cadsus t2 
  where t2.cd_endereco = t1.cd_endereco
    and t1.cd_municipio_residencia is null;
ALTER TABLE usuario_cadsus ENABLE TRIGGER USER;

/*
    Maicon 02/08/2019 - #26828
*/
alter table tipo_atendimento add flag_exige_unidade_origem int2;
alter table auditschema.tipo_atendimento add flag_exige_unidade_origem int2;
update tipo_atendimento set flag_exige_unidade_origem = 0;