SET application_name = 'flyway|3.1.263.1';

SET statement_timeout TO 600000;

  alter table bnafar_entrada add column cd_usuario_cancelamento int8;
  alter table bnafar_entrada add constraint FK_USU_CADSUS_CANC_REF_USUARIO foreign key (cd_usuario_cancelamento) references usuarios (cd_usuario) on delete restrict on update restrict;
  alter table bnafar_entrada add column dt_cancelamento timestamp;
  alter table auditschema.bnafar_entrada add column cd_usuario_cancelamento int8;
  alter table auditschema.bnafar_entrada add column dt_cancelamento timestamp;

  alter table bnafar_saida add column cd_usuario_cancelamento int8;
  alter table bnafar_saida add constraint FK_USU_CADSUS_CANC_REF_USUARIO foreign key (cd_usuario_cancelamento) references usuarios (cd_usuario) on delete restrict on update restrict;
  alter table bnafar_saida add column dt_cancelamento timestamp;
  alter table auditschema.bna<PERSON>_saida add column cd_usuario_cancelamento int8;
  alter table auditschema.bna<PERSON>_saida add column dt_cancelamento timestamp;

  alter table bnafar_dispensacao add column cd_usuario_cancelamento int8;
  alter table bnafar_dispensacao add constraint FK_USU_CADSUS_CANC_REF_USUARIO foreign key (cd_usuario_cancelamento) references usuarios (cd_usuario) on delete restrict on update restrict;
  alter table bnafar_dispensacao add column dt_cancelamento timestamp;
  alter table auditschema.bnafar_dispensacao add column cd_usuario_cancelamento int8;
  alter table auditschema.bnafar_dispensacao add column dt_cancelamento timestamp;

alter table tipo_atendimento add column flag_valida_ficha_acolhimento int2;
alter table auditschema.tipo_atendimento add column flag_valida_ficha_acolhimento int2;