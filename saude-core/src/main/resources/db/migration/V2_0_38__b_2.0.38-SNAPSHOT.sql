/*
    Sulivan - 02/07/2013
*/
INSERT INTO programa_pagina VALUES(473, 'br.com.celk.view.unidadesaude.modeloDocumento.ConsultaModeloDocumentoPage', 'N');
INSERT INTO programa_pagina VALUES(474, 'br.com.celk.view.unidadesaude.modeloDocumento.CadastroModeloDocumentoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (279,'Consulta Modelo de Documento',473,'N');
INSERT INTO programa_web_pagina VALUES (474, 279, 473);
INSERT INTO programa_web_pagina VALUES (475, 279, 474);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (389,'Consulta Modelo de Documento','modeloDocumento',324,279,143);

/*
    Sulivan - 03/07/2013
*/
INSERT INTO programa_pagina_permissao VALUES (43,18,86,0,null);
INSERT INTO programa_pagina_permissao VALUES (44,18,88,0,null);
INSERT INTO programa_pagina_permissao VALUES (45,18,83,0,null);
INSERT INTO programa_pagina_permissao VALUES (46,18,95,0,null);
INSERT INTO programa_pagina_permissao VALUES (47,18,101,0,null);
INSERT INTO programa_pagina_permissao VALUES (48,18,93,0,null);
INSERT INTO programa_pagina_permissao VALUES (49,18,49,0,null);
INSERT INTO programa_pagina_permissao VALUES (50,18,89,0,null);
INSERT INTO programa_pagina_permissao VALUES (51,18,48,0,null);
INSERT INTO programa_pagina_permissao VALUES (52,18,94,0,null);
INSERT INTO programa_pagina_permissao VALUES (53,18,97,0,null);
INSERT INTO programa_pagina_permissao VALUES (54,18,96,0,null);

/*
 Leandro - 05/07/2013
*/

update tabela_cbo set cd_cbo = cd_cbo;

/*
    Sulivan - 09/07/2013
*/
/*==============================================================*/
/* Index: IDX_DESCRICAO_CONVENIO                                */
/*==============================================================*/
create unique index IDX_DESCRICAO_CONVENIO on convenio (
ds_convenio,
subconvenio
);

/*
    Leandro - 10/07/2013
*/
/*==============================================================*/
/* Table: atendimento_informacao                                */
/*==============================================================*/
create table atendimento_informacao (
cd_atend_inf         INT8                 not null,
cd_atendimento_alta  INT8                 null,
nr_atendimento_principal INT8                 null,
cd_usu_cadsus        NUMERIC(8)           null,
cd_convenio          INT8                 null,
cd_leito             INT8                 null,
cd_profissional_alta INT4                 null,
nm_paciente          VARCHAR(70)          null,
data_chegada         TIMESTAMP            null,
data_saida           TIMESTAMP            null,
status_atendimento   INT8                 null,
version              INT8                 not null
);

alter table atendimento_informacao
   add constraint PK_ATENDIMENTO_INFORMACAO primary key (cd_atend_inf);

/*==============================================================*/
/* Index: idx_atendimento_principal                             */
/*==============================================================*/
create unique index idx_atendimento_principal on atendimento_informacao (
nr_atendimento_principal
);

alter table atendimento_informacao
   add constraint FK_ATEND_INF_REF_ATEND_ALTA foreign key (cd_atendimento_alta)
      references atendimento_alta (cd_atendimento_alta)
      on delete restrict on update restrict;

alter table atendimento_informacao
   add constraint FK_ATEND_INF_REF_ATEND_PRIN foreign key (nr_atendimento_principal)
      references atendimento (nr_atendimento)
      on delete restrict on update restrict;

alter table atendimento_informacao
   add constraint FK_ATEND_INF_REF_CONVENIO foreign key (cd_convenio)
      references convenio (cd_convenio)
      on delete restrict on update restrict;

alter table atendimento_informacao
   add constraint FK_ATEND_INF_REF_LEITO foreign key (cd_leito)
      references leito_quarto (cd_leito)
      on delete restrict on update restrict;

alter table atendimento_informacao
   add constraint FK_ATEND_INF_REF_PACIENTE foreign key (cd_usu_cadsus)
      references usuario_cadsus (cd_usu_cadsus)
      on delete restrict on update restrict;

alter table atendimento_informacao
   add constraint FK_ATEND_INF_REF_PROF_ALTA foreign key (cd_profissional_alta)
      references profissional (cd_profissional)
      on delete restrict on update restrict;

/*
    Sulivan 10/07/2013
*/
INSERT INTO programa_pagina VALUES(476, 'br.com.celk.view.hospital.relatorio.RelatorioRelacaoAtendimentosPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (281,'Relatório Relação de Atendimentos',476,'N');
INSERT INTO programa_web_pagina VALUES (477, 281, 476);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (391,'Relatórios','relatorios',353,null,null);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (392,'','',391,null,null);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (393,'Relatório Relação de Atendimentos','relatorioRelacaoAtendimentos',392,281,353);
INSERT INTO programa_pagina_permissao VALUES (55,18,476,0,null);

/*
    Marcus - 10/07/2013
*/
INSERT INTO programa_pagina (cd_prg_pagina,cam_pagina,publico,version) VALUES (475,'br.com.celk.view.unidadesaude.relatorio.RelatorioPerfilAtendimentoHospitalPage','N',0);
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo,version) VALUES (280,'Relatório Perfil de Atendimento Hospitalar',475,'N',0);
INSERT INTO programa_web_pagina (cd_prg_web_pagina,cd_prg_web,cd_prg_pagina,version) VALUES (476,280,475,0);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (390,'Relatório Perfil de Atendimento Hospitalar','relatorioPerfilAtendimentoHospitalar',392,280,353,0);

/*
 PostgreSQL
 Everton - 11/07/2013
*/

ALTER TABLE equipe_area ADD gera_siab int2;
update equipe_area set gera_siab = 0;
ALTER TABLE equipe_area alter gera_siab set not null;

/*
    Sulivan - 12/07/2013
*/
INSERT INTO programa_pagina VALUES(477, 'br.com.celk.view.unidadesaude.receituario.ConsultaAtendimentosPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (282,'Consulta dos Atendimentos',477,'N');
INSERT INTO programa_web_pagina VALUES (478, 282, 477);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (394,'Consulta dos Atendimentos','consultaAtendimentos',382,282,353);

/*
 Leandro - 11/07/2013
*/
alter table usuario_cadsus add nome_conjuge varchar(70);

/*
    Marcus - 15/07/2013
*/
INSERT INTO programa_pagina_permissao VALUES (56,18,315,0,null);

/*
 Leandro - 23/07/2013
*/
alter table tipo_atendimento add cd_tp_atendimento_observacao int8;

alter table tipo_atendimento 
    add constraint FK_TP_AT_TP_AT_OBS
    foreign key (cd_tp_atendimento_observacao) references tipo_atendimento(cd_tp_atendimento);

alter table tabela_cbo_grupo_atendimento add flag_medico int2;

alter table tipo_atendimento add flag_medico int2;