SET application_name = 'flyway|3.1.0';

/*
    Leonardo  -  #16877
*/


INSERT INTO programa_pagina VALUES (1480, 'br.com.celk.view.unidadesaude.relatorio.RelatorioAtendimentosCompartilhadosPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (816, 'Relatório de Atendimentos Compartilhados', 1480, 'N');
INSERT INTO programa_web_pagina VALUES (1558, 816, 1480);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1058,'Relatório de Atendimentos Compartilhados','atendimentosCompartilhados',265,816,143,0,0);
INSERT INTO programa_pagina_permissao VALUES(371, 18, 1480, 0, 'estabelecimento');

/*
    Leonardo - #16888
*/

ALTER TABLE movimentacao_financeira alter ds_movimentacao type VARCHAR(100);
ALTER TABLE auditschema.movimentacao_financeira alter ds_movimentacao type VARCHAR(100);

/*
    Leonardo - #16867
*/

INSERT INTO programa_pagina_permissao VALUES(372, 9, 163, 0, '');

alter table registro_notas_fiscais add column dt_cadastro timestamp;
alter table auditschema.registro_notas_fiscais add column dt_cadastro timestamp;
update registro_notas_fiscais set dt_cadastro = coalesce(dt_usuario, now());
alter table registro_notas_fiscais alter column dt_cadastro set not null;

alter table registro_notas_fiscais add column dt_estorno timestamp;
alter table auditschema.registro_notas_fiscais add column dt_estorno timestamp;

alter table registro_notas_fiscais add column cd_usuario_estorno NUMERIC(6);
alter table auditschema.registro_notas_fiscais add column cd_usuario_estorno NUMERIC(6);

alter table registro_notas_fiscais
   add constraint FK_REG_NOTA_FISCAL_USU_ESTORNO foreign key (cd_usuario_estorno)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;


/*
    LEONARDO - #16921
*/

-- CRIACAO DA NOVA TABELA DE ELO DA CONFIGURAÇÃO COM A ATIVIDADE DEFAULT PARA O FATURAMENTO
create table configuracao_vigilancia_atividades (
    cd_configuracao_vig_atividades  INT8 not null,
    cd_configuracao_vigilancia      INT8 not null,
	cd_atividades_vigilancia        INT8 not null,
	tipo_processo_padrao            INT2 not null,
    version                         INT8 not null
);

alter table configuracao_vigilancia_atividades
   add constraint PK_CONFIGURACAO_VIGILANCIA_ATIVIDADES primary key (cd_configuracao_vig_atividades);

alter table configuracao_vigilancia_atividades
   add constraint FK_CONF_VIGILANCIA_ATIVIDADES_REF_CONFIGURACAO foreign key (cd_configuracao_vigilancia)
      references configuracao_vigilancia (cd_configuracao_vigilancia)
      on delete restrict on update restrict;

alter table configuracao_vigilancia_atividades
   add constraint FK_CONF_VIGILANCIA_ATIVIDADES_REF_ATIVIDADES foreign key (cd_atividades_vigilancia)
      references atividades_vigilancia (cd_atividades_vigilancia)
      on delete restrict on update restrict;

CREATE TABLE auditschema.configuracao_vigilancia_atividades AS SELECT t2.*, t1.* FROM configuracao_vigilancia_atividades t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_configuracao_vigilancia_atividades;alter table auditschema.configuracao_vigilancia_atividades add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON configuracao_vigilancia_atividades FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

INSERT INTO configuracao_vigilancia_atividades (SELECT nextval('seq_gem'), cd_configuracao_vigilancia, cd_atividades_vigilancia_analis_proj_cad, 0 , 0 FROM configuracao_vigilancia WHERE cd_atividades_vigilancia_analis_proj_cad IS NOT NULL);
INSERT INTO configuracao_vigilancia_atividades (SELECT nextval('seq_gem'), cd_configuracao_vigilancia, cd_atividades_vigilancia_analis_proj_def, 1 , 0 FROM configuracao_vigilancia WHERE cd_atividades_vigilancia_analis_proj_def IS NOT NULL);
INSERT INTO configuracao_vigilancia_atividades (SELECT nextval('seq_gem'), cd_configuracao_vigilancia, cd_atividades_vigilancia_denuncia_cad, 2 , 0 FROM configuracao_vigilancia WHERE cd_atividades_vigilancia_denuncia_cad IS NOT NULL);
INSERT INTO configuracao_vigilancia_atividades (SELECT nextval('seq_gem'), cd_configuracao_vigilancia, cd_atividades_vigilancia_denuncia_fin, 3 , 0 FROM configuracao_vigilancia WHERE cd_atividades_vigilancia_denuncia_fin IS NOT NULL);
INSERT INTO configuracao_vigilancia_atividades (SELECT nextval('seq_gem'), cd_configuracao_vigilancia, cd_atividades_vigilancia_estab_cad, 4 , 0 FROM configuracao_vigilancia WHERE cd_atividades_vigilancia_estab_cad IS NOT NULL);
INSERT INTO configuracao_vigilancia_atividades (SELECT nextval('seq_gem'), cd_configuracao_vigilancia, cd_atividades_vigilancia_estab_inativ, 5 , 0 FROM configuracao_vigilancia WHERE cd_atividades_vigilancia_estab_inativ IS NOT NULL);
INSERT INTO configuracao_vigilancia_atividades (SELECT nextval('seq_gem'), cd_configuracao_vigilancia, cd_atividades_vigilancia_inspecao, 6 , 0 FROM configuracao_vigilancia WHERE cd_atividades_vigilancia_inspecao IS NOT NULL);
INSERT INTO configuracao_vigilancia_atividades (SELECT nextval('seq_gem'), cd_configuracao_vigilancia, cd_atividades_vigilancia_licenca, 7 , 0 FROM configuracao_vigilancia WHERE cd_atividades_vigilancia_licenca IS NOT NULL);

ALTER TABLE lancamento_atividades_vigilancia add column ds_tipo VARCHAR(100);
ALTER TABLE auditschema.lancamento_atividades_vigilancia add column ds_tipo VARCHAR(100);

UPDATE lancamento_atividades_vigilancia l
   SET ds_tipo = ts.ds_tipo_solicitacao
  FROM requerimento_vigilancia rv
  JOIN tipo_solicitacao ts on (rv.cd_tipo_solicitacao = ts.cd_tipo_solicitacao)
 WHERE l.cd_req_vigilancia = rv.cd_req_vigilancia;

/*
    Laudecir
    #17006 / ODONTOGRAMA - 22/01/2018
*/
alter table situacao_dente add column cor varchar(6);
alter table situacao_dente add column tp_aplicacao int2;
alter table situacao_dente add column tp_preenchimento int2;
alter table situacao_dente add column flag_inativar_dente int2;
alter table auditschema.situacao_dente add column cor varchar(6);
alter table auditschema.situacao_dente add column tp_aplicacao int2;
alter table auditschema.situacao_dente add column tp_preenchimento int2;
alter table auditschema.situacao_dente add column flag_inativar_dente int2;

create table elo_dente_situacao (
	cd_elo_dente_situacao int8 not null,
	cd_dente int8 not null,
	cd_situacao int8 not null,
	version int8 not null
);

ALTER TABLE elo_dente_situacao
        ADD CONSTRAINT PK_ELO_DENTE_SITUACAO
        PRIMARY KEY (cd_elo_dente_situacao);

ALTER TABLE elo_dente_situacao
        ADD CONSTRAINT FK_ELO_DENTE_SITUACAO_REF_DENTE FOREIGN KEY (cd_dente)
            REFERENCES dente (cd_dente)
            on delete restrict on update restrict;

ALTER TABLE elo_dente_situacao
        ADD CONSTRAINT FK_ELO_DENTE_SITUACAO_REF_SITUACAO FOREIGN KEY (cd_situacao)
            REFERENCES situacao_dente (cd_situacao)
            on delete restrict on update restrict;

CREATE TABLE auditschema.elo_dente_situacao AS SELECT t2.*, t1.* FROM elo_dente_situacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_elo_dente_situacao;alter table auditschema.elo_dente_situacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON elo_dente_situacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

alter table atendimento_odonto_plano add column faces varchar(50);
alter table auditschema.atendimento_odonto_plano add column faces varchar(50);
alter table atendimento_odonto_plano add column tp_evolucao int2;
alter table auditschema.atendimento_odonto_plano add column tp_evolucao int2;
alter table atendimento_odonto_plano add column sextante_s1 int2;
alter table auditschema.atendimento_odonto_plano add column sextante_s1 int2;
alter table atendimento_odonto_plano add column sextante_s2 int2;
alter table auditschema.atendimento_odonto_plano add column sextante_s2 int2;
alter table atendimento_odonto_plano add column sextante_s3 int2;
alter table auditschema.atendimento_odonto_plano add column sextante_s3 int2;
alter table atendimento_odonto_plano add column sextante_s4 int2;
alter table auditschema.atendimento_odonto_plano add column sextante_s4 int2;
alter table atendimento_odonto_plano add column sextante_s5 int2;
alter table auditschema.atendimento_odonto_plano add column sextante_s5 int2;
alter table atendimento_odonto_plano add column sextante_s6 int2;
alter table auditschema.atendimento_odonto_plano add column sextante_s6 int2;
alter table atendimento_odonto_plano add column arcada_inferior int2;
alter table auditschema.atendimento_odonto_plano add column arcada_inferior int2;
alter table atendimento_odonto_plano add column arcada_superior int2;
alter table auditschema.atendimento_odonto_plano add column arcada_superior int2;
alter table atendimento_odonto_plano add column outro varchar(50);
alter table auditschema.atendimento_odonto_plano add column outro varchar(50);

alter table atendimento_odonto_ficha add column odontograma_json text;
alter table auditschema.atendimento_odonto_ficha add column odontograma_json text;

create table ficha_odonto_elo_atendimento  (
	cd_ficha_odonto_elo_atendimento int8 not null,
	cd_ficha_odonto int4 not null,
	nr_atendimento	int8 not null,
	odontograma_json text,
	version int8 not null
);

ALTER TABLE ficha_odonto_elo_atendimento
	   ADD CONSTRAINT PK_FICHA_ODONTO_ELO_ATENDIMENTO
	   PRIMARY KEY (cd_ficha_odonto_elo_atendimento);

ALTER TABLE ficha_odonto_elo_atendimento
        ADD CONSTRAINT FK_FICHA_ODONTO_ELO_ATEND_REF_ATEND_ODONTO_FICHA FOREIGN KEY (cd_ficha_odonto)
            REFERENCES atendimento_odonto_ficha (cd_ficha_odonto)
            on delete restrict on update restrict;

ALTER TABLE ficha_odonto_elo_atendimento
        ADD CONSTRAINT FK_FICHA_ODONTO_ELO_ATEND_REF_ATENDIMENTO FOREIGN KEY (nr_atendimento)
            REFERENCES atendimento (nr_atendimento)
            on delete restrict on update restrict;

CREATE TABLE auditschema.ficha_odonto_elo_atendimento AS SELECT t2.*, t1.* FROM ficha_odonto_elo_atendimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_ficha_odonto_elo_atendimento;alter table auditschema.ficha_odonto_elo_atendimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ficha_odonto_elo_atendimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

/*
    Evandro - #17148
*/

INSERT INTO programa_pagina_permissao VALUES(373, 2, 462, 0, 'salvarReceituarioPadrao');