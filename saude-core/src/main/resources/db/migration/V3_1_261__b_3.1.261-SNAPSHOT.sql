SET application_name = 'flyway|3.1.261';

SET statement_timeout TO 600000;

create table if not exists public.integracao_financeiro_goiania (
    codigo int8 primary key not null,
    version int8 not null,
    cd_vigilancia_financeiro int8 not null references vigilancia_financeiro(cd_vigilancia_financeiro),
    id_debito int8 null,
    debito_status int2 null,
    dt_cadastro timestamp not null,
    dt_atualizacao timestamp null,
    cd_usuario_cadastro int8 not null references usuarios(cd_usuario)
);

create sequence  seq_id_integracao_financeiro_goiania;

CREATE TABLE auditschema.integracao_financeiro_goiania AS SELECT t2.*, t1.* FROM integracao_financeiro_goiania t1, AUDIT_TEMP t2 WHERE 1 = 2;
CREATE sequence seq_audit_id_integracao_financeiro_goiania;
ALTER TABLE auditschema.integracao_financeiro_goiania ADD PRIMARY KEY(audit_id);
CREATE trigger EMP_AUDIT after INSERT OR UPDATE OR DELETE ON integracao_financeiro_goiania FOR each row execute procedure process_emp_audit();

create table if not exists public.integracao_financeiro_goiania_ocorrencia(
    codigo int8 primary key not null,
    version int8 not null,
    cd_vigilancia_financeiro int8 not null references vigilancia_financeiro(cd_vigilancia_financeiro),
    cd_integracao_financeiro_goiania int8 not null references integracao_financeiro_goiania(codigo),
    status_code int2 null,
    xml_enviado text null,
    xml_recebido text null,
    msg text null,
    id_debito int8 null,
    debito_status int2 null,
    dt_cadastro timestamp not null,
    cd_usuario_cadastro int8 not null references usuarios(cd_usuario)
);

create sequence  seq_id_integracao_financeiro_goiania_ocorrencia;

CREATE TABLE auditschema.integracao_financeiro_goiania_ocorrencia AS SELECT t2.*, t1.* FROM integracao_financeiro_goiania_ocorrencia t1, AUDIT_TEMP t2 WHERE 1 = 2;
CREATE sequence seq_audit_id_integracao_financeiro_goiania_ocorrencia;
ALTER TABLE auditschema.integracao_financeiro_goiania_ocorrencia ADD PRIMARY KEY(audit_id);
CREATE trigger EMP_AUDIT after INSERT OR UPDATE OR DELETE ON integracao_financeiro_goiania_ocorrencia FOR each row execute procedure process_emp_audit();

INSERT INTO permissao_web (cd_permissao, ds_permissao, version) VALUES (185, 'Revalidação Licença Sanitária', 0);
INSERT INTO programa_pagina_permissao (cd_prog_pag_perm,cd_permissao,cd_prg_pagina,version,ds_bundle) VALUES (686,185,1248,0,'rotuloRevalidacaoLicencaSanitaria');