SET application_name = 'flyway|3.1.19.4';

/*
    Elton - 14/09/2018 - #21292
*/
create index  idx_esus_ficha_usuario_cadsus_esus ON esus_ficha_usuario_cadsus_esus (cd_esus_ficha_usu_cadsus_esus);
create index  idx_Integracao_cds_esus_ficha_usuario_cadsus_esus ON esus_integracao_cds (cd_esus_ficha_usu_cadsus_esus);
create index  idx_esus_ficha_endereco_domicilio_esus_cds ON esus_integracao_cds (cd_esus_ficha_endereco_domicilio_esus);
create index  idx_esus_ficha_usuario_cadsus_domicilio ON esus_ficha_usuario_cadsus_domicilio (cd_esus_ficha_endereco_domicilio_esus);
create index  idx_esus_ficha_endereco_domicilio_esus ON esus_ficha_endereco_domicilio_esus (cd_esus_ficha_endereco_domicilio_esus);
create index  idx_esus_integracao_cds_exp_proc ON esus_integracao_cds (cd_exp_proc);
create index  idx_atv_grupo ON esus_integracao_cds (cd_atv_grupo);
create index  idx_conta_paciente ON esus_integracao_cds (cd_conta_paciente);
create index  idx_esus_ficha_atend_individual ON esus_integracao_cds (cd_esus_ficha_atend_individual);
create index  idx_esus_ficha_atend_individual_item ON esus_integracao_cds (cd_esus_ficha_atend_individual_item);
create index  idx_esus_ficha_atendimento_domiciliar ON esus_integracao_cds (cd_esus_ficha_atendimento_domiciliar);
create index  idx_esus_ficha_atendimento_domiciliar_item ON esus_integracao_cds (cd_esus_ficha_atendimento_domiciliar_item);
create index  idx_esus_ficha_aval_eleg_adm ON esus_integracao_cds (cd_esus_ficha_aval_eleg_adm);
create index  idx_esus_ficha_odonto ON esus_integracao_cds (cd_esus_ficha_odonto);
create index  idx_esus_ficha_odonto_item ON esus_integracao_cds (cd_esus_ficha_odonto_item);
create index  idx_esus_ficha_proc ON esus_integracao_cds (cd_esus_ficha_proc);
create index  idx_esus_ficha_proc_it ON esus_integracao_cds (cd_esus_ficha_proc_it);
create index  idx_esus_ficha_usu_cadsus_esus ON esus_integracao_cds (cd_esus_ficha_usu_cadsus_esus);
create index  idx_esus_integracao_cds ON esus_integracao_cds (cd_esus_integracao_cds);
create index  idx_esus_marcadores_consumo_alimentar ON esus_integracao_cds (cd_esus_marcadores_consumo_alimentar);
create index  idx_termo_rec_cad_dom ON esus_integracao_cds (cd_termo_rec_cad_dom);
create index  idx_termo_rec_cad_ind ON esus_integracao_cds (cd_termo_rec_cad_ind);
create index  idx_usuario ON esus_integracao_cds (cd_usuario);
create index  idx_visita ON esus_integracao_cds (cd_visita);

/*
    Sulivan - 17/01/2019 - #23503
*/
alter table esus_integracao_cds disable trigger user;
update esus_integracao_cds set versao_esus = 3 where dt_cadastro >= {ts '2018-11-26 00:00:00'} and versao_esus <> 3 and tipo = 1;
alter table esus_integracao_cds enable trigger user;

/*
    Elton - 06/12/18 - #22848
*/
INSERT INTO integracao_identificacao (codigo,nm_integracao,id,chave,version,habilitado) VALUES (8,'CONSORCIO_PROCEDIMENTO_PRESTADOR','aoisd773864b27364cx0230976xcn28x72n3','9wm8e4wne1ry34r094u043bwawro83720384', 0, 0);