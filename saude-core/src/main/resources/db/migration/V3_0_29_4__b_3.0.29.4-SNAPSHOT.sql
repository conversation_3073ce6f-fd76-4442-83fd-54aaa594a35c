/*
    Laudecir - 18/11/2014
*/
create table mot_ocorrencia_conta_paciente (
    cd_motivo_ocorrencia    INT8           not null,
    ds_motivo_ocorrencia    VARCHAR(50)    not null,
    version                 INT8           not null
);

alter table mot_ocorrencia_conta_paciente
        add constraint PK_MOTIVO_OCORRENCIA primary key (cd_motivo_ocorrencia);

insert into mot_ocorrencia_conta_paciente values(1, 'Abertura de conta', 0);
insert into mot_ocorrencia_conta_paciente values(2, 'Fechamento de conta', 0);
insert into mot_ocorrencia_conta_paciente values(3, 'Reabertura de conta', 0);

alter table ocorrencia_conta_paciente rename cd_ocorr_conta_paciente to cd_ocorrencia;
alter table ocorrencia_conta_paciente rename date to dt_ocorrencia;
alter table ocorrencia_conta_paciente add column cd_motivo_ocorrencia INT8 null;

alter table ocorrencia_conta_paciente
        add constraint FK_OCOR_CONTA_PAC_REF_MOTIVO_OCOR foreign key (cd_motivo_ocorrencia)
        references mot_ocorrencia_conta_paciente (cd_motivo_ocorrencia)
        on delete restrict on update restrict;

update ocorrencia_conta_paciente set cd_motivo_ocorrencia = 2
 where tipo_ocorrencia in ('Fechamento de Conta ABERTA', 'Fechamento de Conta REABERTA');

update ocorrencia_conta_paciente set cd_motivo_ocorrencia = 3
 where tipo_ocorrencia = 'Reabertura de Conta';

alter table ocorrencia_conta_paciente alter cd_motivo_ocorrencia set not null;
alter table ocorrencia_conta_paciente drop tipo_ocorrencia;

INSERT INTO programa_pagina VALUES (865, 'br.com.celk.view.hospital.motivoocorrencia.ConsultaMotivoOcorrenciaContaPacientePage', 'N');
INSERT INTO programa_pagina VALUES (866, 'br.com.celk.view.hospital.motivoocorrencia.CadastroMotivoOcorrenciaContaPacientePage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (485, 'Motivo de Ocorrência da Conta do Paciente', 865, 'N');
INSERT INTO programa_web_pagina VALUES (873, 485, 865);
INSERT INTO programa_web_pagina VALUES (874, 485, 866);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (661,'Motivo de Ocorrência da Conta do Paciente','motivoOcorrenciaContaPaciente',356,485,353,0);
