/*
    Diego - 03/10/2014 - Alta Recepcao
*/

ALTER TABLE atendimento_alta ADD cd_usuario      NUMERIC(6)                 null;
alter table atendimento_alta
   add constraint FK_USU_ALTA foreign key (cd_usuario)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

/*
    Diego - 03-10/2014 - Controle de permissao da recepção
*/
insert into permissao_web VALUES(46, 'Alta', 0);
INSERT INTO programa_pagina_permissao VALUES(141, 12, 541, 0, 'cancelarAtendimento');
INSERT INTO programa_pagina_permissao VALUES(142, 40, 541, 0, 'transferirLeito');
INSERT INTO programa_pagina_permissao VALUES(143, 46, 541, 0, 'alta');

/*
    Leandro - 03/10/2014 - #8463
*/

create index idx_procedimento on procedimento_cbo(cd_procedimento);