SET application_name = 'flyway|3.1.154';

SET statement_timeout TO 600000;

/*
	<PERSON><PERSON><PERSON> - REG-1281 - 05/10/2022
*/
create table public.microrregiao(
     cd_microrregiao int8 PRIMARY KEY NOT NULL,
     descricao varchar(128),
     cd_regional int8 NULL REFERENCES public.regional_saude(cd_regional),
     "version" int8 NOT NULL
);

CREATE SEQUENCE seq_microrregiao INCREMENT 1 START 1;
CREATE TABLE auditschema.microrregiao AS (SELECT t2.*, t1.* FROM microrregiao t1, audit_temp t2 WHERE 1=2);
CREATE sequence seq_audit_id_microrregiao;
ALTER TABLE auditschema.microrregiao ADD PRIMARY KEY (audit_id);
CREATE trigger emp_audit AFTER INSERT OR UPDATE OR DELETE ON public.microrregiao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

alter table microrregiao
        add constraint FK_MICRORREGIAO_REGIONAL_SAUDE foreign key (cd_regional)
        references regional_saude (cd_regional)
            on delete restrict on update restrict;

alter table empresa add column cd_microrregiao int8 null;
alter table auditschema.empresa add column cd_microrregiao int8 null;

alter table empresa
        add constraint FK_EMPRESA_MICRORREGIAO foreign key (cd_microrregiao)
        references microrregiao (cd_microrregiao)
            on delete restrict on update restrict;

/*
    Fernanda Bianchini - 03/10/2022 - VSA-91
*/
INSERT INTO public.ficha_investigacao_agravo(cd_ficha_investigacao_agravo, ordem, dt_cadastro, descricao, status, "version")
VALUES (39, 1, NOW(), 'BOTULISMO', 1, 0);

CREATE TABLE public.investigacao_agr_botulismo(
     cd_investigacao_agr_botulismo int8 PRIMARY KEY NOT NULL,
     "version" int8 NOT NULL,
     flag_informacoes_complementares varchar(1) NOT NULL DEFAULT 'S'::character varying,
     cd_registro_agravo int8 NOT NULL REFERENCES public.registro_agravo(cd_registro_agravo),

    --INVESTIGAÇÃO
     dt_investigacao date NULL,
     ocupacao_cbo varchar(10) NULL REFERENCES public.tabela_cbo(cd_cbo),

    --ANTECEDENTES EPIDEMIOLOGICOS
     dt_primeiro_atendimento date NULL,
     num_atendimento_suspeicao_clinica varchar(2) NULL,
     dt_suspeicao_clinica date NULL,

    --HOSPITALIZACAO
     hospitalizacao int2 NULL,
     dt_internacao DATE NULL,
     dt_alta DATE NULL,
     unidade_hospital int8 NULL REFERENCES public.empresa(empresa),

    --DADOS CLÍNICOS
     sinais_sintomas_febre int2 NULL,
     sinais_sintomas_nausea int2 NULL,
     sinais_sintomas_vomito int2 NULL,
     sinais_sintomas_diarreia int2 NULL,
     sinais_sintomas_constipacao int2 NULL,
     sinais_sintomas_cefaleia int2 NULL,
     sinais_sintomas_tontura int2 NULL,
     sinais_sintomas_visao_turva int2 NULL,
     sinais_sintomas_diplopia int2 NULL,
     sinais_sintomas_disartria int2 NULL,
     sinais_sintomas_disfonia int2 NULL,
     sinais_sintomas_disfagia int2 NULL,
     sinais_sintomas_boca_seca int2 NULL,
     sinais_sintomas_ferimento int2 NULL,
     sinais_sintomas_flacidez_pescoco int2 NULL,
     sinais_sintomas_dispneia int2 NULL,
     sinais_sintomas_insuficiencia_respiratoria int2 NULL,
     sinais_sintomas_insuficiencia_cardiaca int2 NULL,
     sinais_sintomas_coma int2 NULL,
     sinais_sintomas_parestesia int2 NULL,
     sinais_sintomas_parestesia_desc varchar(50),
     sinais_sintomas_outros varchar(50),

     exame_neurologico_ptose_palpebral int2 NULL,
     exame_neurologico_oftalmoparesia int2 NULL,
     exame_neurologico_midriase int2 NULL,
     exame_neurologico_paralisia_facial int2 NULL,
     exame_neurologico_comprometimento_musculatura_bulbar int2 NULL,
     exame_neurologico_fraqueza_membros_sup int2 NULL,
     exame_neurologico_fraqueza_membros_inf int2 NULL,
     exame_neurologico_fraqueza_descendente int2 NULL,
     exame_neurologico_fraqueza_simetrica int2 NULL,
     exame_neurologico_alteracoes_sensibilidade int2 NULL,

     reflexos_neurologicos int2 NULL,

     --FONTE TRANSMISSAO
     suspeita_transmissao_alimentar int2 NULL,
     suspeita_transmissao_alimentar_alimento varchar(50),

     alimento_suspeito_industrial int2 NULL,
     alimento_suspeito_caseira int2 NULL,

     alimento_suspeito_industrial_marca varchar(50),
     alimento_suspeito_industrial_dt_validade date NULL,
     alimento_suspeito_industrial_lote varchar(50),

     exposicao_alimento int2 NULL,
     tempo_ingestao_sintomas_unica varchar(2),
     tempo_primeira_ingestao_sintomas_multipla varchar(2),
     tempo_ultima_ingestao_sintomas_multipla varchar(2),

     local_ingestao_domicilio int2 NULL,
     local_ingestao_creche_escola int2 NULL,
     local_ingestao_trabalho int2 NULL,
     local_ingestao_restaurante int2 NULL,
     local_ingestao_festa int2 NULL,
     local_ingestao_outro varchar(50),

     cd_municipio_ingestao_alimento int8 NULL REFERENCES public.cidade(cod_cid),

     num_pessoas_consumiram_alimento varchar(3),

    --TRATAMENTO
     tratamento_assistencia_ventilatoria int2 NULL,
     tratamento_assistencia_soro_antibotulinico int2 NULL,
     tratamento_assistencia_antibioticoterapia int2 NULL,
     tratamento_assistencia_outro varchar(50),

     dt_administracao date NULL,
     soro_antibotulinico int2 NULL,

    --DADOS DO LABORATÓRIO
     toxina_botulinica_soro int2 NULL,
     dt_coleta_toxina_botulinica_soro date NULL,
     toxina_botulinica_soro_resultado int2 NULL,
     toxina_botulinica_soro_tipo int2 NULL,

     toxina_botulinica_fezes int2 NULL,
     dt_coleta_toxina_botulinica_fezes date NULL,
     toxina_botulinica_fezes_resultado int2 NULL,
     toxina_botulinica_fezes_tipo int2 NULL,

     toxina_botulinica_alimento_1_nome varchar(50),
     toxina_botulinica_alimento_1 int2 NULL,
     dt_coleta_toxina_botulinica_alimento_1 date NULL,
     toxina_botulinica_alimento_1_resultado int2 NULL,
     toxina_botulinica_alimento_1_tipo int2 NULL,

     toxina_botulinica_alimento_2_nome varchar(50),
     toxina_botulinica_alimento_2 int2 NULL,
     dt_coleta_toxina_botulinica_alimento_2 date NULL,
     toxina_botulinica_alimento_2_resultado int2 NULL,
     toxina_botulinica_alimento_2_tipo int2 NULL,

     toxina_botulinica_outros_nome varchar(50),
     toxina_botulinica_outros int2 NULL,
     dt_coleta_toxina_botulinica_outros date NULL,
     toxina_botulinica_outros_resultado int2 NULL,
     toxina_botulinica_outros_tipo int2 NULL,

     exames_complementares_liquor int2 NULL,
     dt_coleta_exames_complementares_liquor date NULL,
     exames_complementares_liquor_num_celular varchar(20),
     exames_complementares_liquor_proteinas varchar(20),

     eletroneuromiografia int2 NULL,
     dt_realizacao_eletroneuromiografia date NULL,
     neuroconducao_sensitiva int2 NULL,
     neuroconducao_motora int2 NULL,
     estimulacao_repetitiva int2 NULL,

     --CONCLUSAO
     classificacao_final int2 NULL,
     classificacao_final_especificar varchar(50),
     criterio_confirmacao_descarte int2 NULL,
     forma_botulismo int2 NULL,

     toxina_botulitica_clinica int2 NULL,
     toxina_botulitica_bromatologica int2 NULL,

     tipo_toxina_isolada_clinica int2 NULL,
     tipo_toxina_isolada_bromatologica int2 NULL,

     causa_alimento varchar(50),
     doenca_relacionada_trabalho int2 NULL,
     evolucao_caso int2 NULL,
     dt_obito date NULL,

    --OBSERVAÇÃO
     observacao text NULL,

    --para os alimentos e locais de consumo, foi criado uma tabela adicional
    --ENCERRAMENTO
     dt_encerramento date NULL,
     cd_usuario_encerramento int8 NULL
);

CREATE SEQUENCE seq_investigacao_agr_botulismo INCREMENT 1 START 1;
CREATE TABLE auditschema.investigacao_agr_botulismo AS
    (SELECT t2.*, t1.* FROM investigacao_agr_botulismo t1, audit_temp t2 WHERE 1=2);
CREATE sequence seq_audit_id_investigacao_agr_botulismo;
ALTER TABLE auditschema.investigacao_agr_botulismo ADD PRIMARY KEY (audit_id);
CREATE trigger emp_audit AFTER INSERT OR UPDATE OR DELETE
    ON public.investigacao_agr_botulismo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE TABLE public.investigacao_agr_botulismo_alimentos(
    cd_botulismo_alimentos int8 PRIMARY KEY NOT NULL,
    cd_investigacao_agr_botulismo int8 NOT NULL REFERENCES public.investigacao_agr_botulismo(cd_investigacao_agr_botulismo),
    "version" int8 NOT NULL,
    tipo_alimento varchar(100) NULL,
    local_consumo varchar(100) NULL
);

CREATE SEQUENCE seq_investigacao_agr_botulismo_alimentos INCREMENT 1 START 1;
CREATE TABLE auditschema.investigacao_agr_botulismo_alimentos AS
    (SELECT t2.*, t1.* FROM investigacao_agr_botulismo_alimentos t1, audit_temp t2 WHERE 1=2);
CREATE sequence seq_audit_id_investigacao_agr_botulismo_alimentos;
ALTER TABLE auditschema.investigacao_agr_botulismo_alimentos ADD PRIMARY KEY (audit_id);
CREATE trigger emp_audit AFTER INSERT OR UPDATE OR DELETE
    ON public.investigacao_agr_botulismo_alimentos FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();