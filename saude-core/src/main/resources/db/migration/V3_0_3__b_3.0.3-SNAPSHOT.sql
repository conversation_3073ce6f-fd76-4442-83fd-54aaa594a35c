/*
    Sulivan - 22/08/13
*/
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (409,'AIH','aih',353,null,null);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (410,'Consultas','consultas',409,null,null,0);
INSERT INTO programa_pagina VALUES(492, 'br.com.celk.view.hospital.aih.ConsultaAihPage', 'N');
INSERT INTO programa_pagina VALUES(493, 'br.com.celk.view.hospital.aih.CadastroAihPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (293,'Consulta de AIH',492,'N');
INSERT INTO programa_web_pagina VALUES (493, 293, 492);
INSERT INTO programa_web_pagina VALUES (494, 293, 493);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (411,'Consulta de AIH','consultaAih',410,293,353);
INSERT INTO programa_pagina_permissao (cd_prog_pag_perm,cd_permissao,cd_prg_pagina,version,ds_bundle) VALUES (66,12,492,0,'cancelar');
INSERT INTO programa_pagina_permissao (cd_prog_pag_perm,cd_permissao,cd_prg_pagina,version,ds_bundle) VALUES (67,3,492,0,'editar');

alter table autorizacao_internacao_hospitalar rename to aih;

create sequence seq_lote_aih;

/*==============================================================*/
/* Table: lote_aih                                              */
/*==============================================================*/
create table lote_aih (
cd_lote_aih          INT8                 not null,
cd_usuario_cadastro  NUMERIC(6)           null,
data_cadastro        TIMESTAMP            null,
status               INT8                 not null,
version              INT8                 not null
);

alter table lote_aih
   add constraint PK_LOTE_AIH primary key (cd_lote_aih);

alter table lote_aih
   add constraint FK_LOTE_AIH_REF_USU_CAD foreign key (cd_usuario_cadastro)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

alter table aih add cd_lote_aih INT8 null;
alter table aih add cd_usu_cadsus NUMERIC(8) null;
alter table aih add status INT8 null;
alter table aih add nm_profissional_autorizador VARCHAR(80) null;
alter table aih add cod_orgao_emissor VARCHAR(10) null;
alter table aih add tp_doc_prof_aut INT8 null;
alter table aih add nro_doc_prof_aut VARCHAR(15) null;
alter table aih add dt_autorizacao DATE null;
alter table aih add nro_autorizacao VARCHAR null;
alter table aih add cd_usuario_cadastro NUMERIC(6) null;
alter table aih add cd_usuario_alteracao NUMERIC(6) null;
alter table aih add dt_cadastro TIMESTAMP null;
alter table aih add dt_alteracao TIMESTAMP null;
alter table aih add dt_cancelamento TIMESTAMP null;
alter table aih add motivo_cancelamento VARCHAR null;
alter table aih add cd_usuario_cancelamento NUMERIC(6) null;

alter table aih
   add constraint FK_AIH_REF_USU_CANC foreign key (cd_usuario_cancelamento)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

alter table aih
   add constraint FK_AIH_REF_LOTE_AIH foreign key (cd_lote_aih)
      references lote_aih (cd_lote_aih)
      on delete restrict on update restrict;

alter table aih
   add constraint FK_AIH_REF_PACIENTE foreign key (cd_usu_cadsus)
      references usuario_cadsus (cd_usu_cadsus)
      on delete restrict on update restrict;

alter table aih
   add constraint FK_AIH_REF_USU_ALTERACAO foreign key (cd_usuario_alteracao)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

alter table aih
   add constraint FK_AIH_REF_USU_CADASTRO foreign key (cd_usuario_cadastro)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

/*
 PostgreSQL
 Everton - 23/08/2013
*/

/*==============================================================*/
/* Table: exame_prestador_unidade                               */
/*==============================================================*/
create table exame_prestador_unidade (
cd_prestador_unidade INT8                 not null,
cd_exame_prestador   INT4                 not null,
empresa              INT4                 not null,
version              INT8                 not null
);

alter table exame_prestador_unidade
   add constraint PK_EXAME_PRESTADOR_UNIDADE primary key (cd_prestador_unidade);

/*==============================================================*/
/* Index: idx_exame_prestador_unidade                           */
/*==============================================================*/
create unique index idx_exame_prestador_unidade on exame_prestador_unidade (
cd_exame_prestador,
empresa
);

alter table exame_prestador_unidade
   add constraint FK_EXAPRE_UNI_REF_EMPRESA foreign key (empresa)
      references empresa (empresa)
      on delete restrict on update restrict;

alter table exame_prestador_unidade
   add constraint FK_EXAPRE_UNI_REF_EXA_PRESTADOR foreign key (cd_exame_prestador)
      references exame_prestador (cd_exame_prestador)
      on delete restrict on update restrict;

/*
 Everton - 26/08/2013
*/

alter table elo_tipo_atend_grupo_atend_cbo add exige_encaminhamento INT2;
update elo_tipo_atend_grupo_atend_cbo set exige_encaminhamento = 0;
alter table elo_tipo_atend_grupo_atend_cbo alter exige_encaminhamento set not null;

/*
 Felipe - 26/08/2013
*/
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (413,'Usuários','usuarios',396,11,0);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (414,'Controle de Horários','controleHorarios',396,15,0);

/*
 PostgreSQL
 Everton - 26/08/2013
*/

alter table profissional drop column numero_crm;

alter table profissional add observacao           VARCHAR(250)         null;
alter table profissional add flag_possui_cns      INT2                 null;
alter table profissional add cd_con_classe        INT2                 null;

alter table profissional
   add constraint FK_PROF_REF_CON_CLA foreign key (cd_con_classe)
      references orgao_emissor (cd_orgao_emissor)
      on delete restrict on update restrict;

alter table profissional_carga_horaria alter tp_sus_nao_sus drop not null;
alter table profissional_carga_horaria alter cd_vinculacao drop not null;
alter table profissional_carga_horaria alter cd_tipo_vinculo drop not null;
alter table profissional_carga_horaria alter cd_subtipo_vinculo drop not null;

ALTER TABLE profissional_historico DROP CONSTRAINT FK_PRO_HIS_REF_PRO_CARGA_HOR;
alter table profissional_historico drop cd_prof_carga_horaria;

alter table profissional drop column flag_possui_cns;
alter table profissional add flag_nao_possui_cns int2;

/*
 Everton - 27/08/2013
*/

/*==============================================================*/
/* Table: local_permanencia                                     */
/*==============================================================*/
create table local_permanencia (
cd_local_permanencia INT8                 not null,
ds_local_permanencia VARCHAR(100)         not null,
version              INT8                 not null
);

alter table local_permanencia
   add constraint PK_LOCAL_PERMANENCIA primary key (cd_local_permanencia);

alter table usuario_cadsus add flag_simplificado    INT2                 null;
alter table usuario_cadsus add flag_estrangeiro     INT2                 null;
alter table usuario_cadsus add flag_nao_possui_cns  INT2                 null;
alter table usuario_cadsus add cd_local_permanencia INT8                 null;

alter table endereco_usuario_cadsus alter cd_tipo_logradouro drop not null;
alter table endereco_usuario_cadsus alter nr_logradouro drop not null;
alter table endereco_usuario_cadsus alter cep drop not null;

alter table usuario_cadsus
   add constraint FK_CADSUS_REF_LOC_PER foreign key (cd_local_permanencia)
      references local_permanencia (cd_local_permanencia)
      on delete restrict on update restrict;

/*
 PostgreSQL
 Marcus - 27/08/2013
*/
INSERT INTO programa_pagina VALUES(494, 'br.com.celk.view.cadsus.usuariocadsus.ConsultaPacienteSimplificadoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES(294,'Paciente Simplificado',494,'N');
INSERT INTO programa_web_pagina VALUES(495, 294, 494);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (412,'Paciente Simplificado','pacienteSimplificado',161,294,0,0);
INSERT INTO programa_pagina VALUES(495, 'br.com.celk.view.cadsus.usuariocadsus.CadastroPacienteSimplificadoPage', 'N');
INSERT INTO programa_web_pagina VALUES(496, 294, 495);

/*
 PostgreSQL
<<<<<<< Updated upstream
 Everton - 28/08/2013
*/

alter table unidade add flag_medicamento     INT2;
update unidade set flag_medicamento = 0;
alter table unidade alter flag_medicamento set not null;

ALTER TABLE receituario_item ADD cod_uni              NUMERIC(6)           null;
alter table receituario_item
   add constraint FK_REC_ITEM_REF_UNIDADE foreign key (cod_uni)
      references unidade (cod_uni)
      on delete restrict on update restrict;

ALTER TABLE PRODUTOS ADD qtd_unidade          INT4                 null;
ALTER TABLE PRODUTOS ADD cod_uni_rec          NUMERIC(6)           null;
alter table produtos
   add constraint FK_PRO_REF_UNI_REC foreign key (cod_uni_rec)
      references unidade (cod_uni)
      on delete restrict on update restrict;

/*
 Marcus - 28/08/2013
*/
alter table usuario_empresas add dt_ativacao          date                 null;
alter table usuario_empresas add dt_desativacao       date                 null;

/*
 Marcus - 29/08/2013
*/
INSERT INTO programa_pagina_permissao (cd_prog_pag_perm,cd_permissao,cd_prg_pagina,version,ds_bundle) VALUES (68,18,127,0,null);

/*
/* Leandro - 29/08/2013 */
update aih set cd_usu_cadsus = (select cd_usu_cadsus from atendimento a where a.nr_atendimento = aih.nr_atendimento) where cd_usu_cadsus is null;
update aih set status = 1 where status is null;
update aih set dt_cadastro = (select a.dt_atendimento from atendimento a where a.nr_atendimento = aih.nr_atendimento) where dt_cadastro is null;

/*
 PostgreSQL
 Everton - 29/08/2013
*/
alter table grupo_atendimento_cbo add tp_filtro_atendimento INT2;
update grupo_atendimento_cbo set tp_filtro_atendimento = 2;
alter table grupo_atendimento_cbo alter tp_filtro_atendimento set not null;

/*
 Claudio - 28/08/2013
*/
INSERT INTO programa_pagina VALUES(496, 'br.com.celk.view.controle.usuario.CadastroUsuarioProfissionalPage', 'N');
INSERT INTO programa_web_pagina VALUES(497, 91, 496);

/*
 PostgreSQL
 Everton - 28/08/2013
*/

alter table usuarios add cd_usuario_cad       NUMERIC(6)           null;
update usuarios set cd_usuario_cad = 0;
alter table usuarios alter cd_usuario_cad set not null;

alter table usuarios
   add constraint FK_USUARIOS_REF_USU_CAD foreign key (cd_usuario_cad)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

INSERT INTO programa_pagina VALUES(497, 'br.com.celk.view.controle.usuario.CadastroUsuarioProfissionalHorarioPage', 'N');
INSERT INTO programa_web_pagina VALUES(498, 91, 497);

/*
 PostgreSQL
 Everton - 30/08/2013
*/

alter table usuarios add flag_usu_temporario  INT2;
update usuarios set flag_usu_temporario = 0;
alter table usuarios alter flag_usu_temporario set not null;

/*
 PostgreSQL
 Claudio - 02/09/2013
*/
INSERT INTO PERMISSAO_WEB VALUES (29,'Configurar', 0);
INSERT INTO programa_pagina_permissao (cd_prog_pag_perm,cd_permissao,cd_prg_pagina,version,ds_bundle) VALUES (69,29,152,0,null);

/*
    Marcus - 03/09/2013
*/
INSERT INTO programa_pagina VALUES (503, 'br.com.celk.view.unidadesaude.bpa.processo.ConfiguracaoBPAPage', 'N');
INSERT INTO programa_web_pagina VALUES (504, 287, 503);

/*
    Marcus - 05/09/2013
*/
INSERT INTO programa_pagina VALUES(510, 'br.com.celk.view.materiais.pedidotransferencia.DetalhesPedidoAlmoxarifadoPage', 'N');
INSERT INTO programa_web_pagina (cd_prg_web_pagina,cd_prg_web,cd_prg_pagina,version) VALUES (511,106,510,0);