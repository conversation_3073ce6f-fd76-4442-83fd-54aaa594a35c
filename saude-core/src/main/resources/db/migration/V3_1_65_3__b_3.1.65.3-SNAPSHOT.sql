SET application_name = 'flyway|3.1.65.3';

SET statement_timeout TO 600000;

/*==============================================================*/
/*   Create indexes for date filter in the atendimento table    */
/*   autor: <PERSON>                                           */
/*   tarefa: AMB-1276                                           */
/*==============================================================*/
CREATE INDEX idx_atendimento_dt_atendimento ON public.atendimento USING btree (dt_atendimento);
CREATE INDEX idx_atendimento_dt_chegada ON public.atendimento USING btree (dt_chegada);
CREATE INDEX idx_atendimento_status ON public.atendimento USING btree (status);

/*==============================================================*/
/*   Ativa API do cidadão nos cliente                           */
/*   autor: Hallan                                              */
/*   tarefa: CSC-131                                            */
/*==============================================================*/

UPDATE public.integracao_identificacao SET habilitado = 1 WHERE nm_integracao = 'CMS';

/*
    Rony - 07/01/2021
*/
CREATE SEQUENCE seq_usuario_cadsus_referencia
	INCREMENT BY 1
	MINVALUE 1000000000
	START 1000000000;