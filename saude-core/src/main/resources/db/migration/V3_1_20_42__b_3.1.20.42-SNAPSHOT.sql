SET application_name = 'flyway|3.1.20.42';

/*
	Sulivan - 03/09/2019 - #27114
*/
create sequence seq_agenda_grade_exame;

create table agenda_grade_exame (
    cd_agenda_grade_exame         INT8              not null,
    cd_ag_grade                   INT8              not null,
    cd_exame_procedimento         INT4              not null,
    cd_usuario                    NUMERIC(6)        not null,
    dt_cadastro                   timestamp         not null,
    version                       INT8              not null
);

alter table agenda_grade_exame
  add constraint PK_AG_GRA_EX primary key (cd_agenda_grade_exame);

alter table agenda_grade_exame
   add constraint FK_AG_GRA_EX_REF_AG_GRA foreign key (cd_ag_grade)
      references agenda_grade (cd_ag_grade)
      on delete restrict on update restrict;

alter table agenda_grade_exame
   add constraint FK_AG_GRA_EX_REF_EX_PROC foreign key (cd_exame_procedimento)
      references exame_procedimento (cd_exame_procedimento)
      on delete restrict on update restrict;

CREATE TABLE auditschema.agenda_grade_exame AS SELECT t2.*, t1.* FROM agenda_grade_exame t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_agenda_grade_exame;alter table auditschema.agenda_grade_exame add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON agenda_grade_exame FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();