CREATE OR REPLACE FUNCTION schema_auditoria_ativo()
  RETURNS text AS
$BODY$
select cast('auditschema' as text);
$BODY$
  LANGUAGE sql IMMUTABLE STRICT
  COST 100;

CREATE SCHEMA auditschema;

CREATE OR REPLACE FUNCTION process_emp_audit()
  R<PERSON><PERSON><PERSON> trigger AS
$BODY$
DECLARE 
	param text[3];
	auditSchema varchar; 
BEGIN
   auditSchema = public.schema_auditoria_ativo();
   SELECT string_to_array(setting, '|') INTO param FROM pg_settings WHERE name = 'application_name';
   IF (TG_OP = 'DELETE') THEN
	    EXECUTE 'INSERT INTO ' || auditSchema || '.' || TG_TABLE_NAME || ' SELECT nextval(''seq_audit_id_' || TG_TABLE_NAME || '''), ''D'', now(), '''||param[1]||''', '''||param[2]||''', ($1).*'
	    USING OLD;
       RETURN OLD;
   ELSIF (TG_OP = 'UPDATE') THEN
	    EXECUTE 'INSERT INTO ' || auditSchema || '.' || TG_TABLE_NAME || ' SELECT nextval(''seq_audit_id_' || TG_TABLE_NAME || '''), ''U'', now(), '''||param[1]||''', '''||param[2]||''', ($1).*'
	    USING NEW;
       RETURN NEW;
   ELSIF (TG_OP = 'INSERT') THEN
	    EXECUTE 'INSERT INTO ' || auditSchema || '.' || TG_TABLE_NAME || ' SELECT nextval(''seq_audit_id_' || TG_TABLE_NAME || '''), ''I'', now(), '''||param[1]||''', '''||param[2]||''', ($1).*'
	    USING NEW;
       RETURN NEW;
   END IF;
   RETURN NULL; END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;

ALTER FUNCTION process_emp_audit()
  OWNER TO celk;

CREATE TABLE IF NOT EXISTS audit_temp(
	audit_id int8 not null,
	audit_type varchar not null,
	audit_timestamp varchar not null,
	audit_user varchar not null,
	audit_obs varchar
);

CREATE TABLE auditschema.requisicao_tuberculose AS SELECT t2.*, t1.* FROM requisicao_tuberculose t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requisicao_tuberculose;alter table auditschema.requisicao_tuberculose add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requisicao_tuberculose FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.complemento_posologia AS SELECT t2.*, t1.* FROM complemento_posologia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_complemento_posologia;alter table auditschema.complemento_posologia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON complemento_posologia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.config_balanco_hidrico AS SELECT t2.*, t1.* FROM config_balanco_hidrico t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_config_balanco_hidrico;alter table auditschema.config_balanco_hidrico add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON config_balanco_hidrico FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.medicamento_nao_padronizado AS SELECT t2.*, t1.* FROM medicamento_nao_padronizado t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_medicamento_nao_padronizado;alter table auditschema.medicamento_nao_padronizado add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON medicamento_nao_padronizado FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atividade_grupo_paciente AS SELECT t2.*, t1.* FROM atividade_grupo_paciente t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atividade_grupo_paciente;alter table auditschema.atividade_grupo_paciente add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atividade_grupo_paciente FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.processo_cid_notificavel AS SELECT t2.*, t1.* FROM processo_cid_notificavel t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_processo_cid_notificavel;alter table auditschema.processo_cid_notificavel add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON processo_cid_notificavel FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.planejamento_visita AS SELECT t2.*, t1.* FROM planejamento_visita t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_planejamento_visita;alter table auditschema.planejamento_visita add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON planejamento_visita FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.planejamento_visita_domicilio AS SELECT t2.*, t1.* FROM planejamento_visita_domicilio t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_planejamento_visita_domicilio;alter table auditschema.planejamento_visita_domicilio add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON planejamento_visita_domicilio FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.painel AS SELECT t2.*, t1.* FROM painel t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_painel;alter table auditschema.painel add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON painel FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.especialidade_leito AS SELECT t2.*, t1.* FROM especialidade_leito t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_especialidade_leito;alter table auditschema.especialidade_leito add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON especialidade_leito FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.sms_cadastro AS SELECT t2.*, t1.* FROM sms_cadastro t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_sms_cadastro;alter table auditschema.sms_cadastro add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON sms_cadastro FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.elo_usuario_cadsus_acompanhante AS SELECT t2.*, t1.* FROM elo_usuario_cadsus_acompanhante t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_elo_usuario_cadsus_acompanhante;alter table auditschema.elo_usuario_cadsus_acompanhante add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON elo_usuario_cadsus_acompanhante FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.prescricao_enfermagem_grupo AS SELECT t2.*, t1.* FROM prescricao_enfermagem_grupo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_prescricao_enfermagem_grupo;alter table auditschema.prescricao_enfermagem_grupo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON prescricao_enfermagem_grupo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.trabalho_parto AS SELECT t2.*, t1.* FROM trabalho_parto t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_trabalho_parto;alter table auditschema.trabalho_parto add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON trabalho_parto FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.agendamento_procedimento AS SELECT t2.*, t1.* FROM agendamento_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_agendamento_procedimento;alter table auditschema.agendamento_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON agendamento_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tabela_cbo_grupo_atendimento AS SELECT t2.*, t1.* FROM tabela_cbo_grupo_atendimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tabela_cbo_grupo_atendimento;alter table auditschema.tabela_cbo_grupo_atendimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tabela_cbo_grupo_atendimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.prescricao_enfermagem AS SELECT t2.*, t1.* FROM prescricao_enfermagem t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_prescricao_enfermagem;alter table auditschema.prescricao_enfermagem add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON prescricao_enfermagem FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_recem_nascido AS SELECT t2.*, t1.* FROM atendimento_recem_nascido t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_recem_nascido;alter table auditschema.atendimento_recem_nascido add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_recem_nascido FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.prescricao_enfermagem_atend AS SELECT t2.*, t1.* FROM prescricao_enfermagem_atend t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_prescricao_enfermagem_atend;alter table auditschema.prescricao_enfermagem_atend add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON prescricao_enfermagem_atend FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.obstetricia_exame_geral AS SELECT t2.*, t1.* FROM obstetricia_exame_geral t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_obstetricia_exame_geral;alter table auditschema.obstetricia_exame_geral add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON obstetricia_exame_geral FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.prescricao_enfermagem_atend_it AS SELECT t2.*, t1.* FROM prescricao_enfermagem_atend_it t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_prescricao_enfermagem_atend_it;alter table auditschema.prescricao_enfermagem_atend_it add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON prescricao_enfermagem_atend_it FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.usuario_cadsus_acompanhante_documento AS SELECT t2.*, t1.* FROM usuario_cadsus_acompanhante_documento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_usuario_cadsus_acompanhante_documento;alter table auditschema.usuario_cadsus_acompanhante_documento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON usuario_cadsus_acompanhante_documento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.controle_infeccao_hospitalar AS SELECT t2.*, t1.* FROM controle_infeccao_hospitalar t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_controle_infeccao_hospitalar;alter table auditschema.controle_infeccao_hospitalar add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON controle_infeccao_hospitalar FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.usuario_cadsus_acompanhante AS SELECT t2.*, t1.* FROM usuario_cadsus_acompanhante t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_usuario_cadsus_acompanhante;alter table auditschema.usuario_cadsus_acompanhante add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON usuario_cadsus_acompanhante FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.motivo_ocorrencia_tipo_samu AS SELECT t2.*, t1.* FROM motivo_ocorrencia_tipo_samu t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_motivo_ocorrencia_tipo_samu;alter table auditschema.motivo_ocorrencia_tipo_samu add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON motivo_ocorrencia_tipo_samu FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.temp_store AS SELECT t2.*, t1.* FROM temp_store t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_temp_store;alter table auditschema.temp_store add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON temp_store FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.elo_grupo_atend_cbo AS SELECT t2.*, t1.* FROM elo_grupo_atend_cbo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_elo_grupo_atend_cbo;alter table auditschema.elo_grupo_atend_cbo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON elo_grupo_atend_cbo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_samu_endereco AS SELECT t2.*, t1.* FROM atendimento_samu_endereco t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_samu_endereco;alter table auditschema.atendimento_samu_endereco add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_samu_endereco FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.motivo_ocorrencia_samu AS SELECT t2.*, t1.* FROM motivo_ocorrencia_samu t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_motivo_ocorrencia_samu;alter table auditschema.motivo_ocorrencia_samu add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON motivo_ocorrencia_samu FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_anexo AS SELECT t2.*, t1.* FROM tipo_anexo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_anexo;alter table auditschema.tipo_anexo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_anexo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.encaminhamento_tipo AS SELECT t2.*, t1.* FROM encaminhamento_tipo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_encaminhamento_tipo;alter table auditschema.encaminhamento_tipo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON encaminhamento_tipo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.anexo_paciente AS SELECT t2.*, t1.* FROM anexo_paciente t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_anexo_paciente;alter table auditschema.anexo_paciente add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON anexo_paciente FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_tabela_procedimento AS SELECT t2.*, t1.* FROM tipo_tabela_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_tabela_procedimento;alter table auditschema.tipo_tabela_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_tabela_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.anexo_paciente_elo AS SELECT t2.*, t1.* FROM anexo_paciente_elo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_anexo_paciente_elo;alter table auditschema.anexo_paciente_elo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON anexo_paciente_elo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_samu_gasto AS SELECT t2.*, t1.* FROM atendimento_samu_gasto t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_samu_gasto;alter table auditschema.atendimento_samu_gasto add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_samu_gasto FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.codigo_barras_produto AS SELECT t2.*, t1.* FROM codigo_barras_produto t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_codigo_barras_produto;alter table auditschema.codigo_barras_produto add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON codigo_barras_produto FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.encaminhamento_samu AS SELECT t2.*, t1.* FROM encaminhamento_samu t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_encaminhamento_samu;alter table auditschema.encaminhamento_samu add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON encaminhamento_samu FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_samu AS SELECT t2.*, t1.* FROM atendimento_samu t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_samu;alter table auditschema.atendimento_samu add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_samu FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.estado_tmp AS SELECT t2.*, t1.* FROM estado_tmp t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_estado_tmp;alter table auditschema.estado_tmp add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON estado_tmp FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.despesas_ipe AS SELECT t2.*, t1.* FROM despesas_ipe t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_despesas_ipe;alter table auditschema.despesas_ipe add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON despesas_ipe FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_emprestimo AS SELECT t2.*, t1.* FROM tipo_emprestimo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_emprestimo;alter table auditschema.tipo_emprestimo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_emprestimo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.honorarios_ipe AS SELECT t2.*, t1.* FROM honorarios_ipe t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_honorarios_ipe;alter table auditschema.honorarios_ipe add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON honorarios_ipe FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.lancamento_emprestimo AS SELECT t2.*, t1.* FROM lancamento_emprestimo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_lancamento_emprestimo;alter table auditschema.lancamento_emprestimo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON lancamento_emprestimo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.raas_processo AS SELECT t2.*, t1.* FROM raas_processo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_raas_processo;alter table auditschema.raas_processo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON raas_processo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.produto_brasindice_item AS SELECT t2.*, t1.* FROM produto_brasindice_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_produto_brasindice_item;alter table auditschema.produto_brasindice_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON produto_brasindice_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.export_xml_cadsus AS SELECT t2.*, t1.* FROM export_xml_cadsus t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_export_xml_cadsus;alter table auditschema.export_xml_cadsus add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON export_xml_cadsus FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.raas_procedimento AS SELECT t2.*, t1.* FROM raas_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_raas_procedimento;alter table auditschema.raas_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON raas_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.usuario_portal AS SELECT t2.*, t1.* FROM usuario_portal t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_usuario_portal;alter table auditschema.usuario_portal add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON usuario_portal FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.elo_produto_brasindice AS SELECT t2.*, t1.* FROM elo_produto_brasindice t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_elo_produto_brasindice;alter table auditschema.elo_produto_brasindice add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON elo_produto_brasindice FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.controle_importacao_produto_brasindice AS SELECT t2.*, t1.* FROM controle_importacao_produto_brasindice t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_controle_importacao_produto_brasindice;alter table auditschema.controle_importacao_produto_brasindice add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON controle_importacao_produto_brasindice FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.produto_brasindice AS SELECT t2.*, t1.* FROM produto_brasindice t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_produto_brasindice;alter table auditschema.produto_brasindice add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON produto_brasindice FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_devolucao AS SELECT t2.*, t1.* FROM tipo_devolucao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_devolucao;alter table auditschema.tipo_devolucao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_devolucao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.elo_tiss_empresa_convenio AS SELECT t2.*, t1.* FROM elo_tiss_empresa_convenio t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_elo_tiss_empresa_convenio;alter table auditschema.elo_tiss_empresa_convenio add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON elo_tiss_empresa_convenio FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.devolucao_emprestimo AS SELECT t2.*, t1.* FROM devolucao_emprestimo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_devolucao_emprestimo;alter table auditschema.devolucao_emprestimo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON devolucao_emprestimo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.elo_tiss_profissional_convenio AS SELECT t2.*, t1.* FROM elo_tiss_profissional_convenio t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_elo_tiss_profissional_convenio;alter table auditschema.elo_tiss_profissional_convenio add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON elo_tiss_profissional_convenio FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.regra_preco_produto_convenio AS SELECT t2.*, t1.* FROM regra_preco_produto_convenio t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_regra_preco_produto_convenio;alter table auditschema.regra_preco_produto_convenio add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON regra_preco_produto_convenio FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.ped_transf_licitacao_entrega_lote AS SELECT t2.*, t1.* FROM ped_transf_licitacao_entrega_lote t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_ped_transf_licitacao_entrega_lote;alter table auditschema.ped_transf_licitacao_entrega_lote add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ped_transf_licitacao_entrega_lote FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.ped_transf_licitacao_entrega_item AS SELECT t2.*, t1.* FROM ped_transf_licitacao_entrega_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_ped_transf_licitacao_entrega_item;alter table auditschema.ped_transf_licitacao_entrega_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ped_transf_licitacao_entrega_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.lancamento_emprestimo_item AS SELECT t2.*, t1.* FROM lancamento_emprestimo_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_lancamento_emprestimo_item;alter table auditschema.lancamento_emprestimo_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON lancamento_emprestimo_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.convenio_paciente AS SELECT t2.*, t1.* FROM convenio_paciente t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_convenio_paciente;alter table auditschema.convenio_paciente add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON convenio_paciente FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.devolucao_emprestimo_elo AS SELECT t2.*, t1.* FROM devolucao_emprestimo_elo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_devolucao_emprestimo_elo;alter table auditschema.devolucao_emprestimo_elo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON devolucao_emprestimo_elo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.devolucao_emprestimo_item AS SELECT t2.*, t1.* FROM devolucao_emprestimo_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_devolucao_emprestimo_item;alter table auditschema.devolucao_emprestimo_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON devolucao_emprestimo_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.bpa_exame_item AS SELECT t2.*, t1.* FROM bpa_exame_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_bpa_exame_item;alter table auditschema.bpa_exame_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON bpa_exame_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_procedimento_tabela AS SELECT t2.*, t1.* FROM exame_procedimento_tabela t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_procedimento_tabela;alter table auditschema.exame_procedimento_tabela add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_procedimento_tabela FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.elo_dispensacao_devolucao AS SELECT t2.*, t1.* FROM elo_dispensacao_devolucao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_elo_dispensacao_devolucao;alter table auditschema.elo_dispensacao_devolucao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON elo_dispensacao_devolucao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.dados_autorizacao_tiss AS SELECT t2.*, t1.* FROM dados_autorizacao_tiss t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_dados_autorizacao_tiss;alter table auditschema.dados_autorizacao_tiss add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON dados_autorizacao_tiss FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_exame_item AS SELECT t2.*, t1.* FROM atendimento_exame_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_exame_item;alter table auditschema.atendimento_exame_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_exame_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.parecer_atendimento AS SELECT t2.*, t1.* FROM parecer_atendimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_parecer_atendimento;alter table auditschema.parecer_atendimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON parecer_atendimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.pedido_tfd_sequencia AS SELECT t2.*, t1.* FROM pedido_tfd_sequencia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_pedido_tfd_sequencia;alter table auditschema.pedido_tfd_sequencia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON pedido_tfd_sequencia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.receituario_modelo_item AS SELECT t2.*, t1.* FROM receituario_modelo_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_receituario_modelo_item;alter table auditschema.receituario_modelo_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON receituario_modelo_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.mot_ocorrencia_conta_paciente AS SELECT t2.*, t1.* FROM mot_ocorrencia_conta_paciente t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_mot_ocorrencia_conta_paciente;alter table auditschema.mot_ocorrencia_conta_paciente add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON mot_ocorrencia_conta_paciente FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.receituario_modelo AS SELECT t2.*, t1.* FROM receituario_modelo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_receituario_modelo;alter table auditschema.receituario_modelo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON receituario_modelo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.teste_rapido_tipo AS SELECT t2.*, t1.* FROM teste_rapido_tipo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_teste_rapido_tipo;alter table auditschema.teste_rapido_tipo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON teste_rapido_tipo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.lancamento_despesa AS SELECT t2.*, t1.* FROM lancamento_despesa t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_lancamento_despesa;alter table auditschema.lancamento_despesa add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON lancamento_despesa FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_oxigenoterapia AS SELECT t2.*, t1.* FROM atendimento_oxigenoterapia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_oxigenoterapia;alter table auditschema.atendimento_oxigenoterapia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_oxigenoterapia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.encaminhamento_internacao AS SELECT t2.*, t1.* FROM encaminhamento_internacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_encaminhamento_internacao;alter table auditschema.encaminhamento_internacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON encaminhamento_internacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.receituario_item_kit AS SELECT t2.*, t1.* FROM receituario_item_kit t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_receituario_item_kit;alter table auditschema.receituario_item_kit add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON receituario_item_kit FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.receituario_item_componente AS SELECT t2.*, t1.* FROM receituario_item_componente t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_receituario_item_componente;alter table auditschema.receituario_item_componente add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON receituario_item_componente FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.motivo_visita AS SELECT t2.*, t1.* FROM motivo_visita t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_motivo_visita;alter table auditschema.motivo_visita add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON motivo_visita FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.sala_unidade AS SELECT t2.*, t1.* FROM sala_unidade t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_sala_unidade;alter table auditschema.sala_unidade add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON sala_unidade FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_detalhe_cad AS SELECT t2.*, t1.* FROM procedimento_detalhe_cad t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_detalhe_cad;alter table auditschema.procedimento_detalhe_cad add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_detalhe_cad FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.agenda_cirurgia AS SELECT t2.*, t1.* FROM agenda_cirurgia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_agenda_cirurgia;alter table auditschema.agenda_cirurgia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON agenda_cirurgia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_indicador AS SELECT t2.*, t1.* FROM tipo_indicador t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_indicador;alter table auditschema.tipo_indicador add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_indicador FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.cadastro_indicador AS SELECT t2.*, t1.* FROM cadastro_indicador t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_cadastro_indicador;alter table auditschema.cadastro_indicador add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON cadastro_indicador FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.portal_menu_pagina AS SELECT t2.*, t1.* FROM portal_menu_pagina t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_portal_menu_pagina;alter table auditschema.portal_menu_pagina add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON portal_menu_pagina FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.portal_pagina AS SELECT t2.*, t1.* FROM portal_pagina t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_portal_pagina;alter table auditschema.portal_pagina add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON portal_pagina FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.portal_menu AS SELECT t2.*, t1.* FROM portal_menu t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_portal_menu;alter table auditschema.portal_menu add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON portal_menu FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.registro_visita_profissional AS SELECT t2.*, t1.* FROM registro_visita_profissional t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_registro_visita_profissional;alter table auditschema.registro_visita_profissional add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON registro_visita_profissional FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.registro_visita AS SELECT t2.*, t1.* FROM registro_visita t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_registro_visita;alter table auditschema.registro_visita add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON registro_visita FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.alta_ocorrencia AS SELECT t2.*, t1.* FROM alta_ocorrencia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_alta_ocorrencia;alter table auditschema.alta_ocorrencia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON alta_ocorrencia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.teste_rapido_realizado AS SELECT t2.*, t1.* FROM teste_rapido_realizado t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_teste_rapido_realizado;alter table auditschema.teste_rapido_realizado add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON teste_rapido_realizado FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.agenda_cota AS SELECT t2.*, t1.* FROM agenda_cota t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_agenda_cota;alter table auditschema.agenda_cota add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON agenda_cota FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.produto_solicitado AS SELECT t2.*, t1.* FROM produto_solicitado t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_produto_solicitado;alter table auditschema.produto_solicitado add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON produto_solicitado FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.modelo_laudo_exame AS SELECT t2.*, t1.* FROM modelo_laudo_exame t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_modelo_laudo_exame;alter table auditschema.modelo_laudo_exame add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON modelo_laudo_exame FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tabela_feriado AS SELECT t2.*, t1.* FROM tabela_feriado t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tabela_feriado;alter table auditschema.tabela_feriado add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tabela_feriado FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.horarios_turno AS SELECT t2.*, t1.* FROM horarios_turno t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_horarios_turno;alter table auditschema.horarios_turno add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON horarios_turno FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_ganho_perda AS SELECT t2.*, t1.* FROM tipo_ganho_perda t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_ganho_perda;alter table auditschema.tipo_ganho_perda add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_ganho_perda FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.produto_solicitado_movimento AS SELECT t2.*, t1.* FROM produto_solicitado_movimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_produto_solicitado_movimento;alter table auditschema.produto_solicitado_movimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON produto_solicitado_movimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.balanco_hidrico AS SELECT t2.*, t1.* FROM balanco_hidrico t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_balanco_hidrico;alter table auditschema.balanco_hidrico add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON balanco_hidrico FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.gerenciador_arquivo AS SELECT t2.*, t1.* FROM gerenciador_arquivo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_gerenciador_arquivo;alter table auditschema.gerenciador_arquivo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON gerenciador_arquivo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.im_solic_agend_to_exame_apac AS SELECT t2.*, t1.* FROM im_solic_agend_to_exame_apac t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_im_solic_agend_to_exame_apac;alter table auditschema.im_solic_agend_to_exame_apac add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON im_solic_agend_to_exame_apac FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.coordenadas AS SELECT t2.*, t1.* FROM coordenadas t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_coordenadas;alter table auditschema.coordenadas add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON coordenadas FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.elo_nodo_tipo_exame AS SELECT t2.*, t1.* FROM elo_nodo_tipo_exame t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_elo_nodo_tipo_exame;alter table auditschema.elo_nodo_tipo_exame add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON elo_nodo_tipo_exame FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.resumo_saldo_estoque AS SELECT t2.*, t1.* FROM resumo_saldo_estoque t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_resumo_saldo_estoque;alter table auditschema.resumo_saldo_estoque add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON resumo_saldo_estoque FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.esferas_administrativas AS SELECT t2.*, t1.* FROM esferas_administrativas t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_esferas_administrativas;alter table auditschema.esferas_administrativas add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON esferas_administrativas FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.cond_pagamento AS SELECT t2.*, t1.* FROM cond_pagamento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_cond_pagamento;alter table auditschema.cond_pagamento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON cond_pagamento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_historico AS SELECT t2.*, t1.* FROM atendimento_historico t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_historico;alter table auditschema.atendimento_historico add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_historico FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.itens_cond_pag AS SELECT t2.*, t1.* FROM itens_cond_pag t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_itens_cond_pag;alter table auditschema.itens_cond_pag add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON itens_cond_pag FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.agenda_grade AS SELECT t2.*, t1.* FROM agenda_grade t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_agenda_grade;alter table auditschema.agenda_grade add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON agenda_grade FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.agenda_ppi_vaga AS SELECT t2.*, t1.* FROM agenda_ppi_vaga t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_agenda_ppi_vaga;alter table auditschema.agenda_ppi_vaga add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON agenda_ppi_vaga FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.arquivo_siab AS SELECT t2.*, t1.* FROM arquivo_siab t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_arquivo_siab;alter table auditschema.arquivo_siab add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON arquivo_siab FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_enfermagem AS SELECT t2.*, t1.* FROM atendimento_enfermagem t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_enfermagem;alter table auditschema.atendimento_enfermagem add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_enfermagem FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_anamnese AS SELECT t2.*, t1.* FROM atendimento_anamnese t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_anamnese;alter table auditschema.atendimento_anamnese add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_anamnese FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_medico AS SELECT t2.*, t1.* FROM atendimento_medico t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_medico;alter table auditschema.atendimento_medico add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_medico FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_ocorrencia AS SELECT t2.*, t1.* FROM atendimento_ocorrencia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_ocorrencia;alter table auditschema.atendimento_ocorrencia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_ocorrencia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_cid AS SELECT t2.*, t1.* FROM atendimento_cid t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_cid;alter table auditschema.atendimento_cid add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_cid FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_odontologico AS SELECT t2.*, t1.* FROM atendimento_odontologico t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_odontologico;alter table auditschema.atendimento_odontologico add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_odontologico FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_exame AS SELECT t2.*, t1.* FROM atendimento_exame t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_exame;alter table auditschema.atendimento_exame add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_exame FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.bpa_atendimento_item AS SELECT t2.*, t1.* FROM bpa_atendimento_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_bpa_atendimento_item;alter table auditschema.bpa_atendimento_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON bpa_atendimento_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.bpa_atendimento AS SELECT t2.*, t1.* FROM bpa_atendimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_bpa_atendimento;alter table auditschema.bpa_atendimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON bpa_atendimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.usuario_cadsus_doenca AS SELECT t2.*, t1.* FROM usuario_cadsus_doenca t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_usuario_cadsus_doenca;alter table auditschema.usuario_cadsus_doenca add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON usuario_cadsus_doenca FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_documento_usuario AS SELECT t2.*, t1.* FROM tipo_documento_usuario t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_documento_usuario;alter table auditschema.tipo_documento_usuario add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_documento_usuario FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.usuario_cadsus_historico AS SELECT t2.*, t1.* FROM usuario_cadsus_historico t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_usuario_cadsus_historico;alter table auditschema.usuario_cadsus_historico add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON usuario_cadsus_historico FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.raas_corpo AS SELECT t2.*, t1.* FROM raas_corpo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_raas_corpo;alter table auditschema.raas_corpo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON raas_corpo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_ocorrencia AS SELECT t2.*, t1.* FROM tipo_ocorrencia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_ocorrencia;alter table auditschema.tipo_ocorrencia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_ocorrencia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.usuario_cadsus_ocorrencia AS SELECT t2.*, t1.* FROM usuario_cadsus_ocorrencia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_usuario_cadsus_ocorrencia;alter table auditschema.usuario_cadsus_ocorrencia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON usuario_cadsus_ocorrencia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.usuario_cadsus_prontuario AS SELECT t2.*, t1.* FROM usuario_cadsus_prontuario t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_usuario_cadsus_prontuario;alter table auditschema.usuario_cadsus_prontuario add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON usuario_cadsus_prontuario FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.calendario_usuario_cadsus AS SELECT t2.*, t1.* FROM calendario_usuario_cadsus t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_calendario_usuario_cadsus;alter table auditschema.calendario_usuario_cadsus add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON calendario_usuario_cadsus FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tabela_cbo_grupo AS SELECT t2.*, t1.* FROM tabela_cbo_grupo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tabela_cbo_grupo;alter table auditschema.tabela_cbo_grupo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tabela_cbo_grupo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_endereco AS SELECT t2.*, t1.* FROM tipo_endereco t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_endereco;alter table auditschema.tipo_endereco add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_endereco FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_profissional AS SELECT t2.*, t1.* FROM tipo_profissional t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_profissional;alter table auditschema.tipo_profissional add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_profissional FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.consorcio_guia_proc_item AS SELECT t2.*, t1.* FROM consorcio_guia_proc_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_consorcio_guia_proc_item;alter table auditschema.consorcio_guia_proc_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON consorcio_guia_proc_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.usuario_cadsus_patologia AS SELECT t2.*, t1.* FROM usuario_cadsus_patologia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_usuario_cadsus_patologia;alter table auditschema.usuario_cadsus_patologia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON usuario_cadsus_patologia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.consorcio_provisao_orcamentaria AS SELECT t2.*, t1.* FROM consorcio_provisao_orcamentaria t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_consorcio_provisao_orcamentaria;alter table auditschema.consorcio_provisao_orcamentaria add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON consorcio_provisao_orcamentaria FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.acesso_ip AS SELECT t2.*, t1.* FROM acesso_ip t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_acesso_ip;alter table auditschema.acesso_ip add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON acesso_ip FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.consorcio_prestador_servico AS SELECT t2.*, t1.* FROM consorcio_prestador_servico t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_consorcio_prestador_servico;alter table auditschema.consorcio_prestador_servico add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON consorcio_prestador_servico FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_receita AS SELECT t2.*, t1.* FROM tipo_receita t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_receita;alter table auditschema.tipo_receita add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_receita FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.controle_inventario AS SELECT t2.*, t1.* FROM controle_inventario t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_controle_inventario;alter table auditschema.controle_inventario add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON controle_inventario FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.controle_permissao_web_grupo AS SELECT t2.*, t1.* FROM controle_permissao_web_grupo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_controle_permissao_web_grupo;alter table auditschema.controle_permissao_web_grupo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON controle_permissao_web_grupo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.usuario_cadsus_his_aprovacao AS SELECT t2.*, t1.* FROM usuario_cadsus_his_aprovacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_usuario_cadsus_his_aprovacao;alter table auditschema.usuario_cadsus_his_aprovacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON usuario_cadsus_his_aprovacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.produto_posologia AS SELECT t2.*, t1.* FROM produto_posologia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_produto_posologia;alter table auditschema.produto_posologia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON produto_posologia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.empresa_convenio_bpa AS SELECT t2.*, t1.* FROM empresa_convenio_bpa t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_empresa_convenio_bpa;alter table auditschema.empresa_convenio_bpa add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON empresa_convenio_bpa FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_padrao AS SELECT t2.*, t1.* FROM exame_padrao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_padrao;alter table auditschema.exame_padrao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_padrao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.elo_grupo_atend_cbo_usuario AS SELECT t2.*, t1.* FROM elo_grupo_atend_cbo_usuario t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_elo_grupo_atend_cbo_usuario;alter table auditschema.elo_grupo_atend_cbo_usuario add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON elo_grupo_atend_cbo_usuario FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.controle_permissoes_grupo AS SELECT t2.*, t1.* FROM controle_permissoes_grupo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_controle_permissoes_grupo;alter table auditschema.controle_permissoes_grupo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON controle_permissoes_grupo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.controle_programas_grupo AS SELECT t2.*, t1.* FROM controle_programas_grupo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_controle_programas_grupo;alter table auditschema.controle_programas_grupo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON controle_programas_grupo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.dispensacao_estatistica AS SELECT t2.*, t1.* FROM dispensacao_estatistica t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_dispensacao_estatistica;alter table auditschema.dispensacao_estatistica add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON dispensacao_estatistica FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.doenca AS SELECT t2.*, t1.* FROM doenca t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_doenca;alter table auditschema.doenca add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON doenca FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.dispensacao_item_lote AS SELECT t2.*, t1.* FROM dispensacao_item_lote t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_dispensacao_item_lote;alter table auditschema.dispensacao_item_lote add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON dispensacao_item_lote FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_prestador_unidade AS SELECT t2.*, t1.* FROM exame_prestador_unidade t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_prestador_unidade;alter table auditschema.exame_prestador_unidade add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_prestador_unidade FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.local_permanencia AS SELECT t2.*, t1.* FROM local_permanencia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_local_permanencia;alter table auditschema.local_permanencia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON local_permanencia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.dom_cbo AS SELECT t2.*, t1.* FROM dom_cbo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_dom_cbo;alter table auditschema.dom_cbo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON dom_cbo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.dom_cidade AS SELECT t2.*, t1.* FROM dom_cidade t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_dom_cidade;alter table auditschema.dom_cidade add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON dom_cidade FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.consorcio_procedimento AS SELECT t2.*, t1.* FROM consorcio_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_consorcio_procedimento;alter table auditschema.consorcio_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON consorcio_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.dom_consorcio_procedimento AS SELECT t2.*, t1.* FROM dom_consorcio_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_dom_consorcio_procedimento;alter table auditschema.dom_consorcio_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON dom_consorcio_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.bpa_processo AS SELECT t2.*, t1.* FROM bpa_processo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_bpa_processo;alter table auditschema.bpa_processo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON bpa_processo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.dom_usuario_cadsus AS SELECT t2.*, t1.* FROM dom_usuario_cadsus t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_dom_usuario_cadsus;alter table auditschema.dom_usuario_cadsus add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON dom_usuario_cadsus FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.dom_estabelecimento AS SELECT t2.*, t1.* FROM dom_estabelecimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_dom_estabelecimento;alter table auditschema.dom_estabelecimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON dom_estabelecimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.dom_produto AS SELECT t2.*, t1.* FROM dom_produto t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_dom_produto;alter table auditschema.dom_produto add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON dom_produto FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_profissional AS SELECT t2.*, t1.* FROM exame_profissional t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_profissional;alter table auditschema.exame_profissional add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_profissional FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_profissional_competencia AS SELECT t2.*, t1.* FROM exame_profissional_competencia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_profissional_competencia;alter table auditschema.exame_profissional_competencia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_profissional_competencia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.dom_profissional AS SELECT t2.*, t1.* FROM dom_profissional t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_dom_profissional;alter table auditschema.dom_profissional add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON dom_profissional FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.dom_tipo_conta AS SELECT t2.*, t1.* FROM dom_tipo_conta t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_dom_tipo_conta;alter table auditschema.dom_tipo_conta add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON dom_tipo_conta FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.elo_it_ent_it_ped_ins AS SELECT t2.*, t1.* FROM elo_it_ent_it_ped_ins t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_elo_it_ent_it_ped_ins;alter table auditschema.elo_it_ent_it_ped_ins add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON elo_it_ent_it_ped_ins FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.leito_desativacao AS SELECT t2.*, t1.* FROM leito_desativacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_leito_desativacao;alter table auditschema.leito_desativacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON leito_desativacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.elo_it_ent_vac_it_ped_vac AS SELECT t2.*, t1.* FROM elo_it_ent_vac_it_ped_vac t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_elo_it_ent_vac_it_ped_vac;alter table auditschema.elo_it_ent_vac_it_ped_vac add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON elo_it_ent_vac_it_ped_vac FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_prestado AS SELECT t2.*, t1.* FROM atendimento_prestado t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_prestado;alter table auditschema.atendimento_prestado add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_prestado FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_convenio AS SELECT t2.*, t1.* FROM tipo_convenio t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_convenio;alter table auditschema.tipo_convenio add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_convenio FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.empresa_atendimento_prestado AS SELECT t2.*, t1.* FROM empresa_atendimento_prestado t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_empresa_atendimento_prestado;alter table auditschema.empresa_atendimento_prestado add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON empresa_atendimento_prestado FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.empresa_equipamento AS SELECT t2.*, t1.* FROM empresa_equipamento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_empresa_equipamento;alter table auditschema.empresa_equipamento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON empresa_equipamento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.grupo_mensagem AS SELECT t2.*, t1.* FROM grupo_mensagem t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_grupo_mensagem;alter table auditschema.grupo_mensagem add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON grupo_mensagem FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.impedimento_equipamento AS SELECT t2.*, t1.* FROM impedimento_equipamento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_impedimento_equipamento;alter table auditschema.impedimento_equipamento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON impedimento_equipamento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.grupo_mensagem_usuario AS SELECT t2.*, t1.* FROM grupo_mensagem_usuario t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_grupo_mensagem_usuario;alter table auditschema.grupo_mensagem_usuario add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON grupo_mensagem_usuario FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.empresa_instalacao_fisica AS SELECT t2.*, t1.* FROM empresa_instalacao_fisica t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_empresa_instalacao_fisica;alter table auditschema.empresa_instalacao_fisica add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON empresa_instalacao_fisica FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.empresa_material AS SELECT t2.*, t1.* FROM empresa_material t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_empresa_material;alter table auditschema.empresa_material add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON empresa_material FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.elo_nodo_tipo_atend_grupo_atend_cbo AS SELECT t2.*, t1.* FROM elo_nodo_tipo_atend_grupo_atend_cbo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_elo_nodo_tipo_atend_grupo_atend_cbo;alter table auditschema.elo_nodo_tipo_atend_grupo_atend_cbo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON elo_nodo_tipo_atend_grupo_atend_cbo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.elo_nodo_convenio AS SELECT t2.*, t1.* FROM elo_nodo_convenio t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_elo_nodo_convenio;alter table auditschema.elo_nodo_convenio add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON elo_nodo_convenio FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.empresa_servico_class AS SELECT t2.*, t1.* FROM empresa_servico_class t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_empresa_servico_class;alter table auditschema.empresa_servico_class add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON empresa_servico_class FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.empresa_bpa AS SELECT t2.*, t1.* FROM empresa_bpa t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_empresa_bpa;alter table auditschema.empresa_bpa add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON empresa_bpa FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.empresa_procedimento AS SELECT t2.*, t1.* FROM empresa_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_empresa_procedimento;alter table auditschema.empresa_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON empresa_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.elo_procedimento_classificacao AS SELECT t2.*, t1.* FROM elo_procedimento_classificacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_elo_procedimento_classificacao;alter table auditschema.elo_procedimento_classificacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON elo_procedimento_classificacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_classificacao AS SELECT t2.*, t1.* FROM procedimento_classificacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_classificacao;alter table auditschema.procedimento_classificacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_classificacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.grupo_atendimento_cbo_acesso AS SELECT t2.*, t1.* FROM grupo_atendimento_cbo_acesso t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_grupo_atendimento_cbo_acesso;alter table auditschema.grupo_atendimento_cbo_acesso add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON grupo_atendimento_cbo_acesso FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.esfera_administrativa AS SELECT t2.*, t1.* FROM esfera_administrativa t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_esfera_administrativa;alter table auditschema.esfera_administrativa add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON esfera_administrativa FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_gestacao AS SELECT t2.*, t1.* FROM tipo_gestacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_gestacao;alter table auditschema.tipo_gestacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_gestacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.nivel_hierarquia AS SELECT t2.*, t1.* FROM nivel_hierarquia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_nivel_hierarquia;alter table auditschema.nivel_hierarquia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON nivel_hierarquia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.turno_atendimento AS SELECT t2.*, t1.* FROM turno_atendimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_turno_atendimento;alter table auditschema.turno_atendimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON turno_atendimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.elo_tipo_unidade AS SELECT t2.*, t1.* FROM elo_tipo_unidade t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_elo_tipo_unidade;alter table auditschema.elo_tipo_unidade add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON elo_tipo_unidade FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.encaminhamento_atendimento AS SELECT t2.*, t1.* FROM encaminhamento_atendimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_encaminhamento_atendimento;alter table auditschema.encaminhamento_atendimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON encaminhamento_atendimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.cidade_cep_padrao AS SELECT t2.*, t1.* FROM cidade_cep_padrao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_cidade_cep_padrao;alter table auditschema.cidade_cep_padrao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON cidade_cep_padrao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.encaminhamento_agendamento AS SELECT t2.*, t1.* FROM encaminhamento_agendamento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_encaminhamento_agendamento;alter table auditschema.encaminhamento_agendamento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON encaminhamento_agendamento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.produto_grupo_atendimento AS SELECT t2.*, t1.* FROM produto_grupo_atendimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_produto_grupo_atendimento;alter table auditschema.produto_grupo_atendimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON produto_grupo_atendimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.encaminhamento_consulta AS SELECT t2.*, t1.* FROM encaminhamento_consulta t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_encaminhamento_consulta;alter table auditschema.encaminhamento_consulta add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON encaminhamento_consulta FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_dieta AS SELECT t2.*, t1.* FROM tipo_dieta t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_dieta;alter table auditschema.tipo_dieta add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_dieta FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.elo_tp_dieta_receituario AS SELECT t2.*, t1.* FROM elo_tp_dieta_receituario t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_elo_tp_dieta_receituario;alter table auditschema.elo_tp_dieta_receituario add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON elo_tp_dieta_receituario FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.medicamento_procedimento AS SELECT t2.*, t1.* FROM medicamento_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_medicamento_procedimento;alter table auditschema.medicamento_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON medicamento_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.ped_transf_licitacao_entrega AS SELECT t2.*, t1.* FROM ped_transf_licitacao_entrega t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_ped_transf_licitacao_entrega;alter table auditschema.ped_transf_licitacao_entrega add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ped_transf_licitacao_entrega FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.relacao_cintura_quadril AS SELECT t2.*, t1.* FROM relacao_cintura_quadril t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_relacao_cintura_quadril;alter table auditschema.relacao_cintura_quadril add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON relacao_cintura_quadril FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.equipamento_procedimento AS SELECT t2.*, t1.* FROM equipamento_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_equipamento_procedimento;alter table auditschema.equipamento_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON equipamento_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.equipe_area AS SELECT t2.*, t1.* FROM equipe_area t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_equipe_area;alter table auditschema.equipe_area add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON equipe_area FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_equipamento AS SELECT t2.*, t1.* FROM tipo_equipamento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_equipamento;alter table auditschema.tipo_equipamento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_equipamento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.equipamento AS SELECT t2.*, t1.* FROM equipamento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_equipamento;alter table auditschema.equipamento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON equipamento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.motivo_desativacao AS SELECT t2.*, t1.* FROM motivo_desativacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_motivo_desativacao;alter table auditschema.motivo_desativacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON motivo_desativacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_equipe AS SELECT t2.*, t1.* FROM tipo_equipe t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_equipe;alter table auditschema.tipo_equipe add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_equipe FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.localizacao AS SELECT t2.*, t1.* FROM localizacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_localizacao;alter table auditschema.localizacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON localizacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.estrutura_equipamento_revisao AS SELECT t2.*, t1.* FROM estrutura_equipamento_revisao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_estrutura_equipamento_revisao;alter table auditschema.estrutura_equipamento_revisao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON estrutura_equipamento_revisao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.estrutura_equipamentos AS SELECT t2.*, t1.* FROM estrutura_equipamentos t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_estrutura_equipamentos;alter table auditschema.estrutura_equipamentos add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON estrutura_equipamentos FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.evento_sistema AS SELECT t2.*, t1.* FROM evento_sistema t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_evento_sistema;alter table auditschema.evento_sistema add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON evento_sistema FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.evolucao_prontuario AS SELECT t2.*, t1.* FROM evolucao_prontuario t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_evolucao_prontuario;alter table auditschema.evolucao_prontuario add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON evolucao_prontuario FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_extra_cota_prestador AS SELECT t2.*, t1.* FROM exame_extra_cota_prestador t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_extra_cota_prestador;alter table auditschema.exame_extra_cota_prestador add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_extra_cota_prestador FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_cota_realizado AS SELECT t2.*, t1.* FROM exame_cota_realizado t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_cota_realizado;alter table auditschema.exame_cota_realizado add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_cota_realizado FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_prestador_competencia AS SELECT t2.*, t1.* FROM exame_prestador_competencia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_prestador_competencia;alter table auditschema.exame_prestador_competencia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_prestador_competencia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.elo_nat_procura_tp_atendimento_enc_tipo AS SELECT t2.*, t1.* FROM elo_nat_procura_tp_atendimento_enc_tipo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_elo_nat_procura_tp_atendimento_enc_tipo;alter table auditschema.elo_nat_procura_tp_atendimento_enc_tipo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON elo_nat_procura_tp_atendimento_enc_tipo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.elo_procedimento_classificacao_tipo_atendimento AS SELECT t2.*, t1.* FROM elo_procedimento_classificacao_tipo_atendimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_elo_procedimento_classificacao_tipo_atendimento;alter table auditschema.elo_procedimento_classificacao_tipo_atendimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON elo_procedimento_classificacao_tipo_atendimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_atendimento_cbo_proc AS SELECT t2.*, t1.* FROM tipo_atendimento_cbo_proc t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_atendimento_cbo_proc;alter table auditschema.tipo_atendimento_cbo_proc add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_atendimento_cbo_proc FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.unidade_cota AS SELECT t2.*, t1.* FROM unidade_cota t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_unidade_cota;alter table auditschema.unidade_cota add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON unidade_cota FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_cota_ppi AS SELECT t2.*, t1.* FROM exame_cota_ppi t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_cota_ppi;alter table auditschema.exame_cota_ppi add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_cota_ppi FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_anatomopatologico AS SELECT t2.*, t1.* FROM exame_anatomopatologico t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_anatomopatologico;alter table auditschema.exame_anatomopatologico add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_anatomopatologico FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_transferencia_cota AS SELECT t2.*, t1.* FROM exame_transferencia_cota t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_transferencia_cota;alter table auditschema.exame_transferencia_cota add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_transferencia_cota FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_encaminhamento AS SELECT t2.*, t1.* FROM tipo_encaminhamento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_encaminhamento;alter table auditschema.tipo_encaminhamento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_encaminhamento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.agenda AS SELECT t2.*, t1.* FROM agenda t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_agenda;alter table auditschema.agenda add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON agenda FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.agenda_cota_unidade AS SELECT t2.*, t1.* FROM agenda_cota_unidade t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_agenda_cota_unidade;alter table auditschema.agenda_cota_unidade add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON agenda_cota_unidade FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.laudo_tfd_copia AS SELECT t2.*, t1.* FROM laudo_tfd_copia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_laudo_tfd_copia;alter table auditschema.laudo_tfd_copia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON laudo_tfd_copia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.faixa_etaria AS SELECT t2.*, t1.* FROM faixa_etaria t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_faixa_etaria;alter table auditschema.faixa_etaria add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON faixa_etaria FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.faixa_etaria_item AS SELECT t2.*, t1.* FROM faixa_etaria_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_faixa_etaria_item;alter table auditschema.faixa_etaria_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON faixa_etaria_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.grupo_estoque AS SELECT t2.*, t1.* FROM grupo_estoque t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_grupo_estoque;alter table auditschema.grupo_estoque add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON grupo_estoque FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.formulario_item AS SELECT t2.*, t1.* FROM formulario_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_formulario_item;alter table auditschema.formulario_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON formulario_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.formulario_item_dado AS SELECT t2.*, t1.* FROM formulario_item_dado t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_formulario_item_dado;alter table auditschema.formulario_item_dado add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON formulario_item_dado FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.formulario_valor_item AS SELECT t2.*, t1.* FROM formulario_valor_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_formulario_valor_item;alter table auditschema.formulario_valor_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON formulario_valor_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.formulario AS SELECT t2.*, t1.* FROM formulario t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_formulario;alter table auditschema.formulario add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON formulario FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.formulario_valor AS SELECT t2.*, t1.* FROM formulario_valor t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_formulario_valor;alter table auditschema.formulario_valor add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON formulario_valor FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.entrada_vacina AS SELECT t2.*, t1.* FROM entrada_vacina t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_entrada_vacina;alter table auditschema.entrada_vacina add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON entrada_vacina FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.hiperdia_medicamento AS SELECT t2.*, t1.* FROM hiperdia_medicamento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_hiperdia_medicamento;alter table auditschema.hiperdia_medicamento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON hiperdia_medicamento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.item_entrada_vacina AS SELECT t2.*, t1.* FROM item_entrada_vacina t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_item_entrada_vacina;alter table auditschema.item_entrada_vacina add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON item_entrada_vacina FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.impedimento_agenda AS SELECT t2.*, t1.* FROM impedimento_agenda t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_impedimento_agenda;alter table auditschema.impedimento_agenda add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON impedimento_agenda FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.hiperdia AS SELECT t2.*, t1.* FROM hiperdia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_hiperdia;alter table auditschema.hiperdia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON hiperdia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.subtipo_instalacao AS SELECT t2.*, t1.* FROM subtipo_instalacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_subtipo_instalacao;alter table auditschema.subtipo_instalacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON subtipo_instalacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.instalacao_fisica AS SELECT t2.*, t1.* FROM instalacao_fisica t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_instalacao_fisica;alter table auditschema.instalacao_fisica add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON instalacao_fisica FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.grupo_atendimento AS SELECT t2.*, t1.* FROM grupo_atendimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_grupo_atendimento;alter table auditschema.grupo_atendimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON grupo_atendimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.acao_programatica AS SELECT t2.*, t1.* FROM acao_programatica t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_acao_programatica;alter table auditschema.acao_programatica add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON acao_programatica FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.item_pedido_insumo AS SELECT t2.*, t1.* FROM item_pedido_insumo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_item_pedido_insumo;alter table auditschema.item_pedido_insumo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON item_pedido_insumo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.acao_programatica_grupo AS SELECT t2.*, t1.* FROM acao_programatica_grupo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_acao_programatica_grupo;alter table auditschema.acao_programatica_grupo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON acao_programatica_grupo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.item_pedido_vacina AS SELECT t2.*, t1.* FROM item_pedido_vacina t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_item_pedido_vacina;alter table auditschema.item_pedido_vacina add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON item_pedido_vacina FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.nodo_recepcao_unidade AS SELECT t2.*, t1.* FROM nodo_recepcao_unidade t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_nodo_recepcao_unidade;alter table auditschema.nodo_recepcao_unidade add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON nodo_recepcao_unidade FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.requisicao_eletrocardiograma AS SELECT t2.*, t1.* FROM requisicao_eletrocardiograma t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requisicao_eletrocardiograma;alter table auditschema.requisicao_eletrocardiograma add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requisicao_eletrocardiograma FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tabela_preco_procedimento AS SELECT t2.*, t1.* FROM tabela_preco_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tabela_preco_procedimento;alter table auditschema.tabela_preco_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tabela_preco_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.leito_unidade AS SELECT t2.*, t1.* FROM leito_unidade t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_leito_unidade;alter table auditschema.leito_unidade add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON leito_unidade FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.local_atividade_grupo AS SELECT t2.*, t1.* FROM local_atividade_grupo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_local_atividade_grupo;alter table auditschema.local_atividade_grupo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON local_atividade_grupo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.motivos_cancelamento AS SELECT t2.*, t1.* FROM motivos_cancelamento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_motivos_cancelamento;alter table auditschema.motivos_cancelamento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON motivos_cancelamento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.sms_consulta AS SELECT t2.*, t1.* FROM sms_consulta t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_sms_consulta;alter table auditschema.sms_consulta add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON sms_consulta FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.lista_espera_programa_saude AS SELECT t2.*, t1.* FROM lista_espera_programa_saude t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_lista_espera_programa_saude;alter table auditschema.lista_espera_programa_saude add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON lista_espera_programa_saude FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.sms_resposta AS SELECT t2.*, t1.* FROM sms_resposta t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_sms_resposta;alter table auditschema.sms_resposta add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON sms_resposta FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.menu_web AS SELECT t2.*, t1.* FROM menu_web t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_menu_web;alter table auditschema.menu_web add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON menu_web FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.modelo_resultado_lab AS SELECT t2.*, t1.* FROM modelo_resultado_lab t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_modelo_resultado_lab;alter table auditschema.modelo_resultado_lab add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON modelo_resultado_lab FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atividade_grupo_procedimento AS SELECT t2.*, t1.* FROM atividade_grupo_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atividade_grupo_procedimento;alter table auditschema.atividade_grupo_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atividade_grupo_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.bpa_ativ_gr_proc AS SELECT t2.*, t1.* FROM bpa_ativ_gr_proc t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_bpa_ativ_gr_proc;alter table auditschema.bpa_ativ_gr_proc add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON bpa_ativ_gr_proc FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_encaminhamento AS SELECT t2.*, t1.* FROM atendimento_encaminhamento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_encaminhamento;alter table auditschema.atendimento_encaminhamento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_encaminhamento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_movimentacao AS SELECT t2.*, t1.* FROM tipo_movimentacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_movimentacao;alter table auditschema.tipo_movimentacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_movimentacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.documento_parecer_medico AS SELECT t2.*, t1.* FROM documento_parecer_medico t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_documento_parecer_medico;alter table auditschema.documento_parecer_medico add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON documento_parecer_medico FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.elo_pedido_lote_recebto_lote AS SELECT t2.*, t1.* FROM elo_pedido_lote_recebto_lote t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_elo_pedido_lote_recebto_lote;alter table auditschema.elo_pedido_lote_recebto_lote add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON elo_pedido_lote_recebto_lote FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.requisicao_hepatite AS SELECT t2.*, t1.* FROM requisicao_hepatite t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requisicao_hepatite;alter table auditschema.requisicao_hepatite add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requisicao_hepatite FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.cid_classificacao AS SELECT t2.*, t1.* FROM cid_classificacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_cid_classificacao;alter table auditschema.cid_classificacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON cid_classificacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.natureza_procura_tp_atendimento AS SELECT t2.*, t1.* FROM natureza_procura_tp_atendimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_natureza_procura_tp_atendimento;alter table auditschema.natureza_procura_tp_atendimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON natureza_procura_tp_atendimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.noticia AS SELECT t2.*, t1.* FROM noticia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_noticia;alter table auditschema.noticia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON noticia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.nodo_atendimento AS SELECT t2.*, t1.* FROM nodo_atendimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_nodo_atendimento;alter table auditschema.nodo_atendimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON nodo_atendimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.comorbidade AS SELECT t2.*, t1.* FROM comorbidade t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_comorbidade;alter table auditschema.comorbidade add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON comorbidade FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.comorbidade_paciente AS SELECT t2.*, t1.* FROM comorbidade_paciente t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_comorbidade_paciente;alter table auditschema.comorbidade_paciente add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON comorbidade_paciente FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.movimentacao_financeira AS SELECT t2.*, t1.* FROM movimentacao_financeira t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_movimentacao_financeira;alter table auditschema.movimentacao_financeira add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON movimentacao_financeira FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.pagamento_guia_procedimento AS SELECT t2.*, t1.* FROM pagamento_guia_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_pagamento_guia_procedimento;alter table auditschema.pagamento_guia_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON pagamento_guia_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.consorcio_guia_procedimento AS SELECT t2.*, t1.* FROM consorcio_guia_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_consorcio_guia_procedimento;alter table auditschema.consorcio_guia_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON consorcio_guia_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_prenatal AS SELECT t2.*, t1.* FROM atendimento_prenatal t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_prenatal;alter table auditschema.atendimento_prenatal add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_prenatal FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.escolaridade AS SELECT t2.*, t1.* FROM escolaridade t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_escolaridade;alter table auditschema.escolaridade add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON escolaridade FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.consorcio_prestador AS SELECT t2.*, t1.* FROM consorcio_prestador t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_consorcio_prestador;alter table auditschema.consorcio_prestador add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON consorcio_prestador FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.estado_civil AS SELECT t2.*, t1.* FROM estado_civil t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_estado_civil;alter table auditschema.estado_civil add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON estado_civil FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.pagamento_guia_proc_item AS SELECT t2.*, t1.* FROM pagamento_guia_proc_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_pagamento_guia_proc_item;alter table auditschema.pagamento_guia_proc_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON pagamento_guia_proc_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.indice_imc AS SELECT t2.*, t1.* FROM indice_imc t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_indice_imc;alter table auditschema.indice_imc add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON indice_imc FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.grupo_atendimento_cbo AS SELECT t2.*, t1.* FROM grupo_atendimento_cbo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_grupo_atendimento_cbo;alter table auditschema.grupo_atendimento_cbo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON grupo_atendimento_cbo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.medicamento_descricao AS SELECT t2.*, t1.* FROM medicamento_descricao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_medicamento_descricao;alter table auditschema.medicamento_descricao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON medicamento_descricao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.classificacao_risco AS SELECT t2.*, t1.* FROM classificacao_risco t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_classificacao_risco;alter table auditschema.classificacao_risco add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON classificacao_risco FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.parametro_material AS SELECT t2.*, t1.* FROM parametro_material t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_parametro_material;alter table auditschema.parametro_material add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON parametro_material FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.nodo_consulta_prontuario AS SELECT t2.*, t1.* FROM nodo_consulta_prontuario t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_nodo_consulta_prontuario;alter table auditschema.nodo_consulta_prontuario add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON nodo_consulta_prontuario FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.parametro_gem AS SELECT t2.*, t1.* FROM parametro_gem t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_parametro_gem;alter table auditschema.parametro_gem add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON parametro_gem FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.pedido_tfd_passageiro AS SELECT t2.*, t1.* FROM pedido_tfd_passageiro t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_pedido_tfd_passageiro;alter table auditschema.pedido_tfd_passageiro add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON pedido_tfd_passageiro FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.parametro AS SELECT t2.*, t1.* FROM parametro t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_parametro;alter table auditschema.parametro add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON parametro FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.parametro_procedimento AS SELECT t2.*, t1.* FROM parametro_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_parametro_procedimento;alter table auditschema.parametro_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON parametro_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.parametro_agenda AS SELECT t2.*, t1.* FROM parametro_agenda t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_parametro_agenda;alter table auditschema.parametro_agenda add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON parametro_agenda FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_cbo AS SELECT t2.*, t1.* FROM exame_cbo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_cbo;alter table auditschema.exame_cbo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_cbo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_cbo_competencia AS SELECT t2.*, t1.* FROM exame_cbo_competencia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_cbo_competencia;alter table auditschema.exame_cbo_competencia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_cbo_competencia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_medicamento AS SELECT t2.*, t1.* FROM atendimento_medicamento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_medicamento;alter table auditschema.atendimento_medicamento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_medicamento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.mamografia_requisicao AS SELECT t2.*, t1.* FROM mamografia_requisicao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_mamografia_requisicao;alter table auditschema.mamografia_requisicao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON mamografia_requisicao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.receituario AS SELECT t2.*, t1.* FROM receituario t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_receituario;alter table auditschema.receituario add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON receituario FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame AS SELECT t2.*, t1.* FROM exame t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame;alter table auditschema.exame add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_apac AS SELECT t2.*, t1.* FROM exame_apac t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_apac;alter table auditschema.exame_apac add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_apac FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.pedido_vacina_insumo AS SELECT t2.*, t1.* FROM pedido_vacina_insumo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_pedido_vacina_insumo;alter table auditschema.pedido_vacina_insumo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON pedido_vacina_insumo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.localidade AS SELECT t2.*, t1.* FROM localidade t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_localidade;alter table auditschema.localidade add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON localidade FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.pequena_cirurgia_produto AS SELECT t2.*, t1.* FROM pequena_cirurgia_produto t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_pequena_cirurgia_produto;alter table auditschema.pequena_cirurgia_produto add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON pequena_cirurgia_produto FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.encaminhamento AS SELECT t2.*, t1.* FROM encaminhamento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_encaminhamento;alter table auditschema.encaminhamento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON encaminhamento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.pessoa_endereco AS SELECT t2.*, t1.* FROM pessoa_endereco t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_pessoa_endereco;alter table auditschema.pessoa_endereco add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON pessoa_endereco FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.pequena_cirurgia AS SELECT t2.*, t1.* FROM pequena_cirurgia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_pequena_cirurgia;alter table auditschema.pequena_cirurgia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON pequena_cirurgia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.sisvan_doenca AS SELECT t2.*, t1.* FROM sisvan_doenca t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_sisvan_doenca;alter table auditschema.sisvan_doenca add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON sisvan_doenca FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.sisvan_intercorrencia AS SELECT t2.*, t1.* FROM sisvan_intercorrencia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_sisvan_intercorrencia;alter table auditschema.sisvan_intercorrencia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON sisvan_intercorrencia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.preventivo AS SELECT t2.*, t1.* FROM preventivo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_preventivo;alter table auditschema.preventivo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON preventivo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.produto_programa_saude AS SELECT t2.*, t1.* FROM produto_programa_saude t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_produto_programa_saude;alter table auditschema.produto_programa_saude add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON produto_programa_saude FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.painel_controle_programa_web AS SELECT t2.*, t1.* FROM painel_controle_programa_web t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_painel_controle_programa_web;alter table auditschema.painel_controle_programa_web add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON painel_controle_programa_web FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.preparacao_exame AS SELECT t2.*, t1.* FROM preparacao_exame t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_preparacao_exame;alter table auditschema.preparacao_exame add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON preparacao_exame FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.bolsa_familia AS SELECT t2.*, t1.* FROM bolsa_familia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_bolsa_familia;alter table auditschema.bolsa_familia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON bolsa_familia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.sisvan AS SELECT t2.*, t1.* FROM sisvan t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_sisvan;alter table auditschema.sisvan add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON sisvan FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.preparacao_procedimento AS SELECT t2.*, t1.* FROM preparacao_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_preparacao_procedimento;alter table auditschema.preparacao_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON preparacao_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.im_solic_agend_to_exame_bpai AS SELECT t2.*, t1.* FROM im_solic_agend_to_exame_bpai t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_im_solic_agend_to_exame_bpai;alter table auditschema.im_solic_agend_to_exame_bpai add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON im_solic_agend_to_exame_bpai FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.programa_campos AS SELECT t2.*, t1.* FROM programa_campos t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_programa_campos;alter table auditschema.programa_campos add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON programa_campos FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.programa_web AS SELECT t2.*, t1.* FROM programa_web t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_programa_web;alter table auditschema.programa_web add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON programa_web FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.controle_programa_web_grupo AS SELECT t2.*, t1.* FROM controle_programa_web_grupo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_controle_programa_web_grupo;alter table auditschema.controle_programa_web_grupo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON controle_programa_web_grupo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.lote_solicitacao_agendamento AS SELECT t2.*, t1.* FROM lote_solicitacao_agendamento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_lote_solicitacao_agendamento;alter table auditschema.lote_solicitacao_agendamento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON lote_solicitacao_agendamento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.programa_web_pagina AS SELECT t2.*, t1.* FROM programa_web_pagina t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_programa_web_pagina;alter table auditschema.programa_web_pagina add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON programa_web_pagina FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.controle_programa_web AS SELECT t2.*, t1.* FROM controle_programa_web t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_controle_programa_web;alter table auditschema.controle_programa_web add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON controle_programa_web FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.controle_prg_web_emp AS SELECT t2.*, t1.* FROM controle_prg_web_emp t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_controle_prg_web_emp;alter table auditschema.controle_prg_web_emp add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON controle_prg_web_emp FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.programa_saude_usuario AS SELECT t2.*, t1.* FROM programa_saude_usuario t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_programa_saude_usuario;alter table auditschema.programa_saude_usuario add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON programa_saude_usuario FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.grupo_programa_saude AS SELECT t2.*, t1.* FROM grupo_programa_saude t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_grupo_programa_saude;alter table auditschema.grupo_programa_saude add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON grupo_programa_saude FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_cid AS SELECT t2.*, t1.* FROM procedimento_cid t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_cid;alter table auditschema.procedimento_cid add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_cid FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.dispensacao_medicamento_item AS SELECT t2.*, t1.* FROM dispensacao_medicamento_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_dispensacao_medicamento_item;alter table auditschema.dispensacao_medicamento_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON dispensacao_medicamento_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.lote_solicitacao_ag_item AS SELECT t2.*, t1.* FROM lote_solicitacao_ag_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_lote_solicitacao_ag_item;alter table auditschema.lote_solicitacao_ag_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON lote_solicitacao_ag_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_compativel AS SELECT t2.*, t1.* FROM procedimento_compativel t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_compativel;alter table auditschema.procedimento_compativel add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_compativel FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.prenatal AS SELECT t2.*, t1.* FROM prenatal t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_prenatal;alter table auditschema.prenatal add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON prenatal FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_detalhe AS SELECT t2.*, t1.* FROM procedimento_detalhe t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_detalhe;alter table auditschema.procedimento_detalhe add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_detalhe FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.laudo_tfd AS SELECT t2.*, t1.* FROM laudo_tfd t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_laudo_tfd;alter table auditschema.laudo_tfd add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON laudo_tfd FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_bpai AS SELECT t2.*, t1.* FROM exame_bpai t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_bpai;alter table auditschema.exame_bpai add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_bpai FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.prontuario_eventos AS SELECT t2.*, t1.* FROM prontuario_eventos t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_prontuario_eventos;alter table auditschema.prontuario_eventos add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON prontuario_eventos FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.grafico_desenvolvimento AS SELECT t2.*, t1.* FROM grafico_desenvolvimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_grafico_desenvolvimento;alter table auditschema.grafico_desenvolvimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON grafico_desenvolvimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.documento_atend AS SELECT t2.*, t1.* FROM documento_atend t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_documento_atend;alter table auditschema.documento_atend add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON documento_atend FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.documento_enc_protese AS SELECT t2.*, t1.* FROM documento_enc_protese t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_documento_enc_protese;alter table auditschema.documento_enc_protese add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON documento_enc_protese FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_excecao_comp AS SELECT t2.*, t1.* FROM procedimento_excecao_comp t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_excecao_comp;alter table auditschema.procedimento_excecao_comp add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_excecao_comp FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_gru_habilitacao AS SELECT t2.*, t1.* FROM procedimento_gru_habilitacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_gru_habilitacao;alter table auditschema.procedimento_gru_habilitacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_gru_habilitacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.receituario_item AS SELECT t2.*, t1.* FROM receituario_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_receituario_item;alter table auditschema.receituario_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON receituario_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_documento_atend AS SELECT t2.*, t1.* FROM tipo_documento_atend t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_documento_atend;alter table auditschema.tipo_documento_atend add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_documento_atend FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_habilitacao AS SELECT t2.*, t1.* FROM procedimento_habilitacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_habilitacao;alter table auditschema.procedimento_habilitacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_habilitacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.profissional_historico AS SELECT t2.*, t1.* FROM profissional_historico t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_profissional_historico;alter table auditschema.profissional_historico add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON profissional_historico FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.assistente_social AS SELECT t2.*, t1.* FROM assistente_social t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_assistente_social;alter table auditschema.assistente_social add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON assistente_social FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_habilitacao_cad AS SELECT t2.*, t1.* FROM procedimento_habilitacao_cad t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_habilitacao_cad;alter table auditschema.procedimento_habilitacao_cad add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_habilitacao_cad FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.siab_ssa2 AS SELECT t2.*, t1.* FROM siab_ssa2 t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_siab_ssa2;alter table auditschema.siab_ssa2 add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON siab_ssa2 FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_incremento AS SELECT t2.*, t1.* FROM procedimento_incremento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_incremento;alter table auditschema.procedimento_incremento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_incremento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.siab_pma2 AS SELECT t2.*, t1.* FROM siab_pma2 t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_siab_pma2;alter table auditschema.siab_pma2 add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON siab_pma2 FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.siab_pma2c AS SELECT t2.*, t1.* FROM siab_pma2c t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_siab_pma2c;alter table auditschema.siab_pma2c add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON siab_pma2c FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_tipo_leito AS SELECT t2.*, t1.* FROM procedimento_tipo_leito t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_tipo_leito;alter table auditschema.procedimento_tipo_leito add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_tipo_leito FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_leito AS SELECT t2.*, t1.* FROM procedimento_leito t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_leito;alter table auditschema.procedimento_leito add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_leito FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_modalidade_cad AS SELECT t2.*, t1.* FROM procedimento_modalidade_cad t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_modalidade_cad;alter table auditschema.procedimento_modalidade_cad add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_modalidade_cad FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_modalidade AS SELECT t2.*, t1.* FROM procedimento_modalidade t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_modalidade;alter table auditschema.procedimento_modalidade add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_modalidade FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_origem AS SELECT t2.*, t1.* FROM procedimento_origem t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_origem;alter table auditschema.procedimento_origem add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_origem FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.cidade_bairro AS SELECT t2.*, t1.* FROM cidade_bairro t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_cidade_bairro;alter table auditschema.cidade_bairro add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON cidade_bairro FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.dente_ref_atendimento_item AS SELECT t2.*, t1.* FROM dente_ref_atendimento_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_dente_ref_atendimento_item;alter table auditschema.dente_ref_atendimento_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON dente_ref_atendimento_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.fabricante_medicamento AS SELECT t2.*, t1.* FROM fabricante_medicamento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_fabricante_medicamento;alter table auditschema.fabricante_medicamento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON fabricante_medicamento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.produto_procedimento AS SELECT t2.*, t1.* FROM produto_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_produto_procedimento;alter table auditschema.produto_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON produto_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_produto AS SELECT t2.*, t1.* FROM tipo_produto t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_produto;alter table auditschema.tipo_produto add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_produto FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_registro_cad AS SELECT t2.*, t1.* FROM procedimento_registro_cad t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_registro_cad;alter table auditschema.procedimento_registro_cad add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_registro_cad FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_registro AS SELECT t2.*, t1.* FROM procedimento_registro t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_registro;alter table auditschema.procedimento_registro add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_registro FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_servico AS SELECT t2.*, t1.* FROM procedimento_servico t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_servico;alter table auditschema.procedimento_servico add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_servico FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_sia_sih_cad AS SELECT t2.*, t1.* FROM procedimento_sia_sih_cad t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_sia_sih_cad;alter table auditschema.procedimento_sia_sih_cad add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_sia_sih_cad FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_sia_sih AS SELECT t2.*, t1.* FROM procedimento_sia_sih t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_sia_sih;alter table auditschema.procedimento_sia_sih add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_sia_sih FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.dente AS SELECT t2.*, t1.* FROM dente t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_dente;alter table auditschema.dente add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON dente FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.produto_tipo_via AS SELECT t2.*, t1.* FROM produto_tipo_via t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_produto_tipo_via;alter table auditschema.produto_tipo_via add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON produto_tipo_via FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.unidade AS SELECT t2.*, t1.* FROM unidade t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_unidade;alter table auditschema.unidade add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON unidade FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.profissional_carga_horaria AS SELECT t2.*, t1.* FROM profissional_carga_horaria t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_profissional_carga_horaria;alter table auditschema.profissional_carga_horaria add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON profissional_carga_horaria FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_cbo AS SELECT t2.*, t1.* FROM procedimento_cbo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_cbo;alter table auditschema.procedimento_cbo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_cbo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_grupo AS SELECT t2.*, t1.* FROM procedimento_grupo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_grupo;alter table auditschema.procedimento_grupo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_grupo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_subgrupo AS SELECT t2.*, t1.* FROM procedimento_subgrupo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_subgrupo;alter table auditschema.procedimento_subgrupo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_subgrupo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_financiamento AS SELECT t2.*, t1.* FROM procedimento_financiamento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_financiamento;alter table auditschema.procedimento_financiamento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_financiamento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_rubrica AS SELECT t2.*, t1.* FROM procedimento_rubrica t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_rubrica;alter table auditschema.procedimento_rubrica add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_rubrica FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.motivo_desligamento AS SELECT t2.*, t1.* FROM motivo_desligamento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_motivo_desligamento;alter table auditschema.motivo_desligamento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON motivo_desligamento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.ordem_compra AS SELECT t2.*, t1.* FROM ordem_compra t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_ordem_compra;alter table auditschema.ordem_compra add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ordem_compra FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.permissao_web AS SELECT t2.*, t1.* FROM permissao_web t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_permissao_web;alter table auditschema.permissao_web add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON permissao_web FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.programa_pagina_permissao AS SELECT t2.*, t1.* FROM programa_pagina_permissao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_programa_pagina_permissao;alter table auditschema.programa_pagina_permissao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON programa_pagina_permissao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_logradouro_cnes AS SELECT t2.*, t1.* FROM tipo_logradouro_cnes t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_logradouro_cnes;alter table auditschema.tipo_logradouro_cnes add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_logradouro_cnes FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.programa_pagina AS SELECT t2.*, t1.* FROM programa_pagina t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_programa_pagina;alter table auditschema.programa_pagina add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON programa_pagina FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_odonto_plano AS SELECT t2.*, t1.* FROM atendimento_odonto_plano t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_odonto_plano;alter table auditschema.atendimento_odonto_plano add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_odonto_plano FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.situacao_dente AS SELECT t2.*, t1.* FROM situacao_dente t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_situacao_dente;alter table auditschema.situacao_dente add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON situacao_dente FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.situacao_dente_procedimento AS SELECT t2.*, t1.* FROM situacao_dente_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_situacao_dente_procedimento;alter table auditschema.situacao_dente_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON situacao_dente_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.pedido_transferencia_item AS SELECT t2.*, t1.* FROM pedido_transferencia_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_pedido_transferencia_item;alter table auditschema.pedido_transferencia_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON pedido_transferencia_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_solicitado_tfd AS SELECT t2.*, t1.* FROM procedimento_solicitado_tfd t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_solicitado_tfd;alter table auditschema.procedimento_solicitado_tfd add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_solicitado_tfd FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_via_medicamento AS SELECT t2.*, t1.* FROM tipo_via_medicamento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_via_medicamento;alter table auditschema.tipo_via_medicamento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_via_medicamento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.registro_itnf_pro_solicitado AS SELECT t2.*, t1.* FROM registro_itnf_pro_solicitado t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_registro_itnf_pro_solicitado;alter table auditschema.registro_itnf_pro_solicitado add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON registro_itnf_pro_solicitado FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.pedido_transferencia_item_lote AS SELECT t2.*, t1.* FROM pedido_transferencia_item_lote t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_pedido_transferencia_item_lote;alter table auditschema.pedido_transferencia_item_lote add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON pedido_transferencia_item_lote FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_odonto_ficha AS SELECT t2.*, t1.* FROM atendimento_odonto_ficha t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_odonto_ficha;alter table auditschema.atendimento_odonto_ficha add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_odonto_ficha FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_prontuario AS SELECT t2.*, t1.* FROM atendimento_prontuario t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_prontuario;alter table auditschema.atendimento_prontuario add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_prontuario FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.recebto_grupo_estoque AS SELECT t2.*, t1.* FROM recebto_grupo_estoque t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_recebto_grupo_estoque;alter table auditschema.recebto_grupo_estoque add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON recebto_grupo_estoque FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_odonto_execucao AS SELECT t2.*, t1.* FROM atendimento_odonto_execucao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_odonto_execucao;alter table auditschema.atendimento_odonto_execucao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_odonto_execucao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.usuario_cadsus_endereco AS SELECT t2.*, t1.* FROM usuario_cadsus_endereco t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_usuario_cadsus_endereco;alter table auditschema.usuario_cadsus_endereco add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON usuario_cadsus_endereco FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.natureza_procura AS SELECT t2.*, t1.* FROM natureza_procura t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_natureza_procura;alter table auditschema.natureza_procura add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON natureza_procura FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.modelo_documento AS SELECT t2.*, t1.* FROM modelo_documento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_modelo_documento;alter table auditschema.modelo_documento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON modelo_documento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.registro_manutencao_item AS SELECT t2.*, t1.* FROM registro_manutencao_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_registro_manutencao_item;alter table auditschema.registro_manutencao_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON registro_manutencao_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.sms_mensagem AS SELECT t2.*, t1.* FROM sms_mensagem t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_sms_mensagem;alter table auditschema.sms_mensagem add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON sms_mensagem FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.registro_manutencao AS SELECT t2.*, t1.* FROM registro_manutencao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_registro_manutencao;alter table auditschema.registro_manutencao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON registro_manutencao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.registro_notas_fiscais AS SELECT t2.*, t1.* FROM registro_notas_fiscais t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_registro_notas_fiscais;alter table auditschema.registro_notas_fiscais add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON registro_notas_fiscais FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.serie AS SELECT t2.*, t1.* FROM serie t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_serie;alter table auditschema.serie add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON serie FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.ordem_compra_item AS SELECT t2.*, t1.* FROM ordem_compra_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_ordem_compra_item;alter table auditschema.ordem_compra_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ordem_compra_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.ordem_compra_elo_nota AS SELECT t2.*, t1.* FROM ordem_compra_elo_nota t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_ordem_compra_elo_nota;alter table auditschema.ordem_compra_elo_nota add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ordem_compra_elo_nota FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_requisicao_exame AS SELECT t2.*, t1.* FROM tipo_requisicao_exame t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_requisicao_exame;alter table auditschema.tipo_requisicao_exame add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_requisicao_exame FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.requisicao_padrao AS SELECT t2.*, t1.* FROM requisicao_padrao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requisicao_padrao;alter table auditschema.requisicao_padrao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requisicao_padrao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.requisicao_envio_exame AS SELECT t2.*, t1.* FROM requisicao_envio_exame t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requisicao_envio_exame;alter table auditschema.requisicao_envio_exame add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requisicao_envio_exame FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.requisicao_envio_ocorrencia AS SELECT t2.*, t1.* FROM requisicao_envio_ocorrencia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requisicao_envio_ocorrencia;alter table auditschema.requisicao_envio_ocorrencia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requisicao_envio_ocorrencia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.dispositivos_moveis AS SELECT t2.*, t1.* FROM dispositivos_moveis t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_dispositivos_moveis;alter table auditschema.dispositivos_moveis add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON dispositivos_moveis FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.requisicao_padrao_exame AS SELECT t2.*, t1.* FROM requisicao_padrao_exame t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requisicao_padrao_exame;alter table auditschema.requisicao_padrao_exame add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requisicao_padrao_exame FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.deposito AS SELECT t2.*, t1.* FROM deposito t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_deposito;alter table auditschema.deposito add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON deposito FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.segmento_territorial AS SELECT t2.*, t1.* FROM segmento_territorial t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_segmento_territorial;alter table auditschema.segmento_territorial add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON segmento_territorial FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.pedido_tfd AS SELECT t2.*, t1.* FROM pedido_tfd t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_pedido_tfd;alter table auditschema.pedido_tfd add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON pedido_tfd FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.motorista AS SELECT t2.*, t1.* FROM motorista t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_motorista;alter table auditschema.motorista add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON motorista FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.reserva AS SELECT t2.*, t1.* FROM reserva t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_reserva;alter table auditschema.reserva add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON reserva FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.raca AS SELECT t2.*, t1.* FROM raca t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_raca;alter table auditschema.raca add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON raca FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.nacionalidade AS SELECT t2.*, t1.* FROM nacionalidade t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_nacionalidade;alter table auditschema.nacionalidade add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON nacionalidade FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.usuario_cadsus_cns AS SELECT t2.*, t1.* FROM usuario_cadsus_cns t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_usuario_cadsus_cns;alter table auditschema.usuario_cadsus_cns add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON usuario_cadsus_cns FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.servico_terceiro_brasil AS SELECT t2.*, t1.* FROM servico_terceiro_brasil t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_servico_terceiro_brasil;alter table auditschema.servico_terceiro_brasil add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON servico_terceiro_brasil FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_servico_cad AS SELECT t2.*, t1.* FROM procedimento_servico_cad t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_servico_cad;alter table auditschema.procedimento_servico_cad add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_servico_cad FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_servico_cla AS SELECT t2.*, t1.* FROM procedimento_servico_cla t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_servico_cla;alter table auditschema.procedimento_servico_cla add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_servico_cla FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.conta AS SELECT t2.*, t1.* FROM conta t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_conta;alter table auditschema.conta add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON conta FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.grupo_produto AS SELECT t2.*, t1.* FROM grupo_produto t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_grupo_produto;alter table auditschema.grupo_produto add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON grupo_produto FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.orgao_emissor AS SELECT t2.*, t1.* FROM orgao_emissor t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_orgao_emissor;alter table auditschema.orgao_emissor add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON orgao_emissor FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.solicitacao_agendamento AS SELECT t2.*, t1.* FROM solicitacao_agendamento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_solicitacao_agendamento;alter table auditschema.solicitacao_agendamento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON solicitacao_agendamento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.solicitacao_ag_ocorrencia AS SELECT t2.*, t1.* FROM solicitacao_ag_ocorrencia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_solicitacao_ag_ocorrencia;alter table auditschema.solicitacao_ag_ocorrencia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON solicitacao_ag_ocorrencia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_conta AS SELECT t2.*, t1.* FROM tipo_conta t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_conta;alter table auditschema.tipo_conta add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_conta FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.subconta AS SELECT t2.*, t1.* FROM subconta t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_subconta;alter table auditschema.subconta add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON subconta FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.integracao_exclusao_mobile AS SELECT t2.*, t1.* FROM integracao_exclusao_mobile t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_integracao_exclusao_mobile;alter table auditschema.integracao_exclusao_mobile add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON integracao_exclusao_mobile FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tabela_subgrupo_cbo AS SELECT t2.*, t1.* FROM tabela_subgrupo_cbo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tabela_subgrupo_cbo;alter table auditschema.tabela_subgrupo_cbo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tabela_subgrupo_cbo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.teste_pezinho AS SELECT t2.*, t1.* FROM teste_pezinho t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_teste_pezinho;alter table auditschema.teste_pezinho add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON teste_pezinho FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.teste_pezinho_historico AS SELECT t2.*, t1.* FROM teste_pezinho_historico t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_teste_pezinho_historico;alter table auditschema.teste_pezinho_historico add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON teste_pezinho_historico FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.regional_saude AS SELECT t2.*, t1.* FROM regional_saude t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_regional_saude;alter table auditschema.regional_saude add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON regional_saude FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_atend_acesso AS SELECT t2.*, t1.* FROM tipo_atend_acesso t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_atend_acesso;alter table auditschema.tipo_atend_acesso add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_atend_acesso FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.usuario_cadsus_documento AS SELECT t2.*, t1.* FROM usuario_cadsus_documento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_usuario_cadsus_documento;alter table auditschema.usuario_cadsus_documento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON usuario_cadsus_documento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_atividade_grupo_cbo AS SELECT t2.*, t1.* FROM tipo_atividade_grupo_cbo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_atividade_grupo_cbo;alter table auditschema.tipo_atividade_grupo_cbo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_atividade_grupo_cbo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.plano_conta AS SELECT t2.*, t1.* FROM plano_conta t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_plano_conta;alter table auditschema.plano_conta add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON plano_conta FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.ppi_grupo AS SELECT t2.*, t1.* FROM ppi_grupo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_ppi_grupo;alter table auditschema.ppi_grupo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ppi_grupo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_operacao AS SELECT t2.*, t1.* FROM tipo_operacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_operacao;alter table auditschema.tipo_operacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_operacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.usuario_cadsus_domicilio AS SELECT t2.*, t1.* FROM usuario_cadsus_domicilio t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_usuario_cadsus_domicilio;alter table auditschema.usuario_cadsus_domicilio add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON usuario_cadsus_domicilio FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_procedimento_cla AS SELECT t2.*, t1.* FROM tipo_procedimento_cla t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_procedimento_cla;alter table auditschema.tipo_procedimento_cla add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_procedimento_cla FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_receita_cbo AS SELECT t2.*, t1.* FROM tipo_receita_cbo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_receita_cbo;alter table auditschema.tipo_receita_cbo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_receita_cbo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_receita_empresa AS SELECT t2.*, t1.* FROM tipo_receita_empresa t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_receita_empresa;alter table auditschema.tipo_receita_empresa add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_receita_empresa FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_forma_organizacao AS SELECT t2.*, t1.* FROM procedimento_forma_organizacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_forma_organizacao;alter table auditschema.procedimento_forma_organizacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_forma_organizacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_prestador_procedimento AS SELECT t2.*, t1.* FROM exame_prestador_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_prestador_procedimento;alter table auditschema.exame_prestador_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_prestador_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.trajeto_cidade AS SELECT t2.*, t1.* FROM trajeto_cidade t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_trajeto_cidade;alter table auditschema.trajeto_cidade add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON trajeto_cidade FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.unidade_cota_ppi AS SELECT t2.*, t1.* FROM unidade_cota_ppi t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_unidade_cota_ppi;alter table auditschema.unidade_cota_ppi add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON unidade_cota_ppi FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.siab_ocupacao AS SELECT t2.*, t1.* FROM siab_ocupacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_siab_ocupacao;alter table auditschema.siab_ocupacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON siab_ocupacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.usuario_grupo AS SELECT t2.*, t1.* FROM usuario_grupo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_usuario_grupo;alter table auditschema.usuario_grupo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON usuario_grupo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.importacao_xml_cadsus AS SELECT t2.*, t1.* FROM importacao_xml_cadsus t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_importacao_xml_cadsus;alter table auditschema.importacao_xml_cadsus add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON importacao_xml_cadsus FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.usuario_programa AS SELECT t2.*, t1.* FROM usuario_programa t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_usuario_programa;alter table auditschema.usuario_programa add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON usuario_programa FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.produto_vacina AS SELECT t2.*, t1.* FROM produto_vacina t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_produto_vacina;alter table auditschema.produto_vacina add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON produto_vacina FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.programa_saude AS SELECT t2.*, t1.* FROM programa_saude t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_programa_saude;alter table auditschema.programa_saude add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON programa_saude FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_veiculo AS SELECT t2.*, t1.* FROM tipo_veiculo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_veiculo;alter table auditschema.tipo_veiculo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_veiculo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.veiculo_combustivel AS SELECT t2.*, t1.* FROM veiculo_combustivel t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_veiculo_combustivel;alter table auditschema.veiculo_combustivel add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON veiculo_combustivel FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.vac_calendario AS SELECT t2.*, t1.* FROM vac_calendario t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_vac_calendario;alter table auditschema.vac_calendario add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON vac_calendario FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.calendario AS SELECT t2.*, t1.* FROM calendario t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_calendario;alter table auditschema.calendario add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON calendario FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_vacina AS SELECT t2.*, t1.* FROM tipo_vacina t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_vacina;alter table auditschema.tipo_vacina add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_vacina FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.veiculo_dados AS SELECT t2.*, t1.* FROM veiculo_dados t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_veiculo_dados;alter table auditschema.veiculo_dados add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON veiculo_dados FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.veiculo AS SELECT t2.*, t1.* FROM veiculo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_veiculo;alter table auditschema.veiculo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON veiculo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.vinculacao_subtipo AS SELECT t2.*, t1.* FROM vinculacao_subtipo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_vinculacao_subtipo;alter table auditschema.vinculacao_subtipo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON vinculacao_subtipo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.vinculacao AS SELECT t2.*, t1.* FROM vinculacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_vinculacao;alter table auditschema.vinculacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON vinculacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.vinculacao_tipo AS SELECT t2.*, t1.* FROM vinculacao_tipo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_vinculacao_tipo;alter table auditschema.vinculacao_tipo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON vinculacao_tipo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.modulo AS SELECT t2.*, t1.* FROM modulo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_modulo;alter table auditschema.modulo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON modulo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_unidade_cota AS SELECT t2.*, t1.* FROM exame_unidade_cota t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_unidade_cota;alter table auditschema.exame_unidade_cota add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_unidade_cota FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_unidade_procedimento AS SELECT t2.*, t1.* FROM exame_unidade_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_unidade_procedimento;alter table auditschema.exame_unidade_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_unidade_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.barra_tarefas AS SELECT t2.*, t1.* FROM barra_tarefas t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_barra_tarefas;alter table auditschema.barra_tarefas add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON barra_tarefas FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.auditoria AS SELECT t2.*, t1.* FROM auditoria t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_auditoria;alter table auditschema.auditoria add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON auditoria FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_pessoa AS SELECT t2.*, t1.* FROM tipo_pessoa t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_pessoa;alter table auditschema.tipo_pessoa add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_pessoa FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atividade AS SELECT t2.*, t1.* FROM atividade t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atividade;alter table auditschema.atividade add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atividade FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.grupos AS SELECT t2.*, t1.* FROM grupos t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_grupos;alter table auditschema.grupos add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON grupos FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_documento AS SELECT t2.*, t1.* FROM tipo_documento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_documento;alter table auditschema.tipo_documento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_documento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.controle_menus_grupo AS SELECT t2.*, t1.* FROM controle_menus_grupo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_controle_menus_grupo;alter table auditschema.controle_menus_grupo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON controle_menus_grupo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.programas AS SELECT t2.*, t1.* FROM programas t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_programas;alter table auditschema.programas add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON programas FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.permissoes AS SELECT t2.*, t1.* FROM permissoes t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_permissoes;alter table auditschema.permissoes add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON permissoes FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.menus AS SELECT t2.*, t1.* FROM menus t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_menus;alter table auditschema.menus add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON menus FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.controle_menus AS SELECT t2.*, t1.* FROM controle_menus t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_controle_menus;alter table auditschema.controle_menus add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON controle_menus FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_exame_unidade AS SELECT t2.*, t1.* FROM tipo_exame_unidade t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_exame_unidade;alter table auditschema.tipo_exame_unidade add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_exame_unidade FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.pedido_tfd_agendamento AS SELECT t2.*, t1.* FROM pedido_tfd_agendamento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_pedido_tfd_agendamento;alter table auditschema.pedido_tfd_agendamento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON pedido_tfd_agendamento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.programa_favorito AS SELECT t2.*, t1.* FROM programa_favorito t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_programa_favorito;alter table auditschema.programa_favorito add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON programa_favorito FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_unidade_competencia AS SELECT t2.*, t1.* FROM exame_unidade_competencia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_unidade_competencia;alter table auditschema.exame_unidade_competencia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_unidade_competencia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_unidade_proc_competencia AS SELECT t2.*, t1.* FROM exame_unidade_proc_competencia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_unidade_proc_competencia;alter table auditschema.exame_unidade_proc_competencia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_unidade_proc_competencia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.pedido_licitacao AS SELECT t2.*, t1.* FROM pedido_licitacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_pedido_licitacao;alter table auditschema.pedido_licitacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON pedido_licitacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.equipe_profissional AS SELECT t2.*, t1.* FROM equipe_profissional t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_equipe_profissional;alter table auditschema.equipe_profissional add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON equipe_profissional FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.agendador_processo AS SELECT t2.*, t1.* FROM agendador_processo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_agendador_processo;alter table auditschema.agendador_processo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON agendador_processo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.endereco_domicilio_esus AS SELECT t2.*, t1.* FROM endereco_domicilio_esus t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_endereco_domicilio_esus;alter table auditschema.endereco_domicilio_esus add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON endereco_domicilio_esus FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.licitacao AS SELECT t2.*, t1.* FROM licitacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_licitacao;alter table auditschema.licitacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON licitacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.dom_exame_procedimento AS SELECT t2.*, t1.* FROM dom_exame_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_dom_exame_procedimento;alter table auditschema.dom_exame_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON dom_exame_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.bpa_atividade AS SELECT t2.*, t1.* FROM bpa_atividade t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_bpa_atividade;alter table auditschema.bpa_atividade add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON bpa_atividade FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.elo_licitacao_pedido AS SELECT t2.*, t1.* FROM elo_licitacao_pedido t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_elo_licitacao_pedido;alter table auditschema.elo_licitacao_pedido add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON elo_licitacao_pedido FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.pedido_licitacao_item AS SELECT t2.*, t1.* FROM pedido_licitacao_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_pedido_licitacao_item;alter table auditschema.pedido_licitacao_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON pedido_licitacao_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.lote_encaminhamento_tfd AS SELECT t2.*, t1.* FROM lote_encaminhamento_tfd t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_lote_encaminhamento_tfd;alter table auditschema.lote_encaminhamento_tfd add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON lote_encaminhamento_tfd FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.brasindice_process AS SELECT t2.*, t1.* FROM brasindice_process t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_brasindice_process;alter table auditschema.brasindice_process add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON brasindice_process FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tabela_preco AS SELECT t2.*, t1.* FROM tabela_preco t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tabela_preco;alter table auditschema.tabela_preco add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tabela_preco FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_procedimento_atendimento AS SELECT t2.*, t1.* FROM tipo_procedimento_atendimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_procedimento_atendimento;alter table auditschema.tipo_procedimento_atendimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_procedimento_atendimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.parametro_atendimento AS SELECT t2.*, t1.* FROM parametro_atendimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_parametro_atendimento;alter table auditschema.parametro_atendimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON parametro_atendimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_procedimento_item AS SELECT t2.*, t1.* FROM atendimento_procedimento_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_procedimento_item;alter table auditschema.atendimento_procedimento_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_procedimento_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.licitacao_item AS SELECT t2.*, t1.* FROM licitacao_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_licitacao_item;alter table auditschema.licitacao_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON licitacao_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.vac_aplicacao AS SELECT t2.*, t1.* FROM vac_aplicacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_vac_aplicacao;alter table auditschema.vac_aplicacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON vac_aplicacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.bpa_vacina AS SELECT t2.*, t1.* FROM bpa_vacina t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_bpa_vacina;alter table auditschema.bpa_vacina add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON bpa_vacina FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.horario_dia_semana AS SELECT t2.*, t1.* FROM horario_dia_semana t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_horario_dia_semana;alter table auditschema.horario_dia_semana add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON horario_dia_semana FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.registro_it_nf_licitacao AS SELECT t2.*, t1.* FROM registro_it_nf_licitacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_registro_it_nf_licitacao;alter table auditschema.registro_it_nf_licitacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON registro_it_nf_licitacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.local_fornecimento AS SELECT t2.*, t1.* FROM local_fornecimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_local_fornecimento;alter table auditschema.local_fornecimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON local_fornecimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.ped_transf_licitacao AS SELECT t2.*, t1.* FROM ped_transf_licitacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_ped_transf_licitacao;alter table auditschema.ped_transf_licitacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ped_transf_licitacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.usuario_empresas AS SELECT t2.*, t1.* FROM usuario_empresas t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_usuario_empresas;alter table auditschema.usuario_empresas add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON usuario_empresas FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_primario AS SELECT t2.*, t1.* FROM atendimento_primario t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_primario;alter table auditschema.atendimento_primario add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_primario FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.elo_tipo_atend_grupo_atend_cbo AS SELECT t2.*, t1.* FROM elo_tipo_atend_grupo_atend_cbo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_elo_tipo_atend_grupo_atend_cbo;alter table auditschema.elo_tipo_atend_grupo_atend_cbo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON elo_tipo_atend_grupo_atend_cbo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.elo_ped_l_it_ped_tr_lic_it AS SELECT t2.*, t1.* FROM elo_ped_l_it_ped_tr_lic_it t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_elo_ped_l_it_ped_tr_lic_it;alter table auditschema.elo_ped_l_it_ped_tr_lic_it add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON elo_ped_l_it_ped_tr_lic_it FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.responsavel_tecnico AS SELECT t2.*, t1.* FROM responsavel_tecnico t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_responsavel_tecnico;alter table auditschema.responsavel_tecnico add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON responsavel_tecnico FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.ped_transf_licitacao_item AS SELECT t2.*, t1.* FROM ped_transf_licitacao_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_ped_transf_licitacao_item;alter table auditschema.ped_transf_licitacao_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ped_transf_licitacao_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.grupo_estabelecimento AS SELECT t2.*, t1.* FROM grupo_estabelecimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_grupo_estabelecimento;alter table auditschema.grupo_estabelecimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON grupo_estabelecimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atividade_estabelecimento AS SELECT t2.*, t1.* FROM atividade_estabelecimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atividade_estabelecimento;alter table auditschema.atividade_estabelecimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atividade_estabelecimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_logradouro_cadsus AS SELECT t2.*, t1.* FROM tipo_logradouro_cadsus t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_logradouro_cadsus;alter table auditschema.tipo_logradouro_cadsus add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_logradouro_cadsus FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tabela_cnae AS SELECT t2.*, t1.* FROM tabela_cnae t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tabela_cnae;alter table auditschema.tabela_cnae add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tabela_cnae FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.estabelecimento_cnae AS SELECT t2.*, t1.* FROM estabelecimento_cnae t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_estabelecimento_cnae;alter table auditschema.estabelecimento_cnae add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON estabelecimento_cnae FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.estabelecimento AS SELECT t2.*, t1.* FROM estabelecimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_estabelecimento;alter table auditschema.estabelecimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON estabelecimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.manutencao_lic_item AS SELECT t2.*, t1.* FROM manutencao_lic_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_manutencao_lic_item;alter table auditschema.manutencao_lic_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON manutencao_lic_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.im_solic_agend_to_enca_agend AS SELECT t2.*, t1.* FROM im_solic_agend_to_enca_agend t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_im_solic_agend_to_enca_agend;alter table auditschema.im_solic_agend_to_enca_agend add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON im_solic_agend_to_enca_agend FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.centro_custo AS SELECT t2.*, t1.* FROM centro_custo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_centro_custo;alter table auditschema.centro_custo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON centro_custo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.solicit_mudanca_procedimento AS SELECT t2.*, t1.* FROM solicit_mudanca_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_solicit_mudanca_procedimento;alter table auditschema.solicit_mudanca_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON solicit_mudanca_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.veiculo_estabelecimento AS SELECT t2.*, t1.* FROM veiculo_estabelecimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_veiculo_estabelecimento;alter table auditschema.veiculo_estabelecimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON veiculo_estabelecimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.manutencao_ped_lic_item AS SELECT t2.*, t1.* FROM manutencao_ped_lic_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_manutencao_ped_lic_item;alter table auditschema.manutencao_ped_lic_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON manutencao_ped_lic_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.aliquota_imposto_item AS SELECT t2.*, t1.* FROM aliquota_imposto_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_aliquota_imposto_item;alter table auditschema.aliquota_imposto_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON aliquota_imposto_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.aliquota_imposto AS SELECT t2.*, t1.* FROM aliquota_imposto t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_aliquota_imposto;alter table auditschema.aliquota_imposto add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON aliquota_imposto FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.bem_forma_aquisicao AS SELECT t2.*, t1.* FROM bem_forma_aquisicao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_bem_forma_aquisicao;alter table auditschema.bem_forma_aquisicao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON bem_forma_aquisicao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.pessoa AS SELECT t2.*, t1.* FROM pessoa t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_pessoa;alter table auditschema.pessoa add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON pessoa FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.ocorrencia_req_vigilancia AS SELECT t2.*, t1.* FROM ocorrencia_req_vigilancia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_ocorrencia_req_vigilancia;alter table auditschema.ocorrencia_req_vigilancia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ocorrencia_req_vigilancia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.bem_grupo AS SELECT t2.*, t1.* FROM bem_grupo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_bem_grupo;alter table auditschema.bem_grupo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON bem_grupo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.bem_motivo_baixa AS SELECT t2.*, t1.* FROM bem_motivo_baixa t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_bem_motivo_baixa;alter table auditschema.bem_motivo_baixa add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON bem_motivo_baixa FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.setor AS SELECT t2.*, t1.* FROM setor t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_setor;alter table auditschema.setor add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON setor FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.setor_localizacao AS SELECT t2.*, t1.* FROM setor_localizacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_setor_localizacao;alter table auditschema.setor_localizacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON setor_localizacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.template_doc_vigilancia AS SELECT t2.*, t1.* FROM template_doc_vigilancia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_template_doc_vigilancia;alter table auditschema.template_doc_vigilancia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON template_doc_vigilancia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_solicitacao AS SELECT t2.*, t1.* FROM tipo_solicitacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_solicitacao;alter table auditschema.tipo_solicitacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_solicitacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.bem_patrimonio AS SELECT t2.*, t1.* FROM bem_patrimonio t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_bem_patrimonio;alter table auditschema.bem_patrimonio add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON bem_patrimonio FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.classificacao_grupo_estab AS SELECT t2.*, t1.* FROM classificacao_grupo_estab t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_classificacao_grupo_estab;alter table auditschema.classificacao_grupo_estab add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON classificacao_grupo_estab FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.documento_sequencial AS SELECT t2.*, t1.* FROM documento_sequencial t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_documento_sequencial;alter table auditschema.documento_sequencial add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON documento_sequencial FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.solicitacao_procedimentos AS SELECT t2.*, t1.* FROM solicitacao_procedimentos t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_solicitacao_procedimentos;alter table auditschema.solicitacao_procedimentos add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON solicitacao_procedimentos FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.nodo_atendimento_web AS SELECT t2.*, t1.* FROM nodo_atendimento_web t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_nodo_atendimento_web;alter table auditschema.nodo_atendimento_web add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON nodo_atendimento_web FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.setor_vigilancia AS SELECT t2.*, t1.* FROM setor_vigilancia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_setor_vigilancia;alter table auditschema.setor_vigilancia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON setor_vigilancia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.usuario_setor_vigilancia AS SELECT t2.*, t1.* FROM usuario_setor_vigilancia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_usuario_setor_vigilancia;alter table auditschema.usuario_setor_vigilancia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON usuario_setor_vigilancia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.bem_patrimonio_ocorrencia AS SELECT t2.*, t1.* FROM bem_patrimonio_ocorrencia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_bem_patrimonio_ocorrencia;alter table auditschema.bem_patrimonio_ocorrencia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON bem_patrimonio_ocorrencia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.liberacao_receita AS SELECT t2.*, t1.* FROM liberacao_receita t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_liberacao_receita;alter table auditschema.liberacao_receita add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON liberacao_receita FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.solicitacao_procedimentos_item AS SELECT t2.*, t1.* FROM solicitacao_procedimentos_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_solicitacao_procedimentos_item;alter table auditschema.solicitacao_procedimentos_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON solicitacao_procedimentos_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.endereco_domicilio_ocorrencia AS SELECT t2.*, t1.* FROM endereco_domicilio_ocorrencia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_endereco_domicilio_ocorrencia;alter table auditschema.endereco_domicilio_ocorrencia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON endereco_domicilio_ocorrencia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_atendimento_procedimento AS SELECT t2.*, t1.* FROM tipo_atendimento_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_atendimento_procedimento;alter table auditschema.tipo_atendimento_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_atendimento_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.ipe_process AS SELECT t2.*, t1.* FROM ipe_process t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_ipe_process;alter table auditschema.ipe_process add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ipe_process FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.dispensacao_cbo_procedimento AS SELECT t2.*, t1.* FROM dispensacao_cbo_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_dispensacao_cbo_procedimento;alter table auditschema.dispensacao_cbo_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON dispensacao_cbo_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.bpa AS SELECT t2.*, t1.* FROM bpa t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_bpa;alter table auditschema.bpa add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON bpa FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.endereco_usuario_cadsus AS SELECT t2.*, t1.* FROM endereco_usuario_cadsus t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_endereco_usuario_cadsus;alter table auditschema.endereco_usuario_cadsus add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON endereco_usuario_cadsus FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.requerimento_vigilancia AS SELECT t2.*, t1.* FROM requerimento_vigilancia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requerimento_vigilancia;alter table auditschema.requerimento_vigilancia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requerimento_vigilancia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_competencia AS SELECT t2.*, t1.* FROM procedimento_competencia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_competencia;alter table auditschema.procedimento_competencia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_competencia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.cid AS SELECT t2.*, t1.* FROM cid t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_cid;alter table auditschema.cid add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON cid FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.documento_requerimento AS SELECT t2.*, t1.* FROM documento_requerimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_documento_requerimento;alter table auditschema.documento_requerimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON documento_requerimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.dados_clinicos AS SELECT t2.*, t1.* FROM dados_clinicos t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_dados_clinicos;alter table auditschema.dados_clinicos add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON dados_clinicos FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.medicamento_paciente AS SELECT t2.*, t1.* FROM medicamento_paciente t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_medicamento_paciente;alter table auditschema.medicamento_paciente add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON medicamento_paciente FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.uso_medicacao AS SELECT t2.*, t1.* FROM uso_medicacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_uso_medicacao;alter table auditschema.uso_medicacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON uso_medicacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.relacao_familiar_acolhimento AS SELECT t2.*, t1.* FROM relacao_familiar_acolhimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_relacao_familiar_acolhimento;alter table auditschema.relacao_familiar_acolhimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON relacao_familiar_acolhimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_informacao AS SELECT t2.*, t1.* FROM atendimento_informacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_informacao;alter table auditschema.atendimento_informacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_informacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.droga_uso AS SELECT t2.*, t1.* FROM droga_uso t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_droga_uso;alter table auditschema.droga_uso add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON droga_uso FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atividade_terapeutica AS SELECT t2.*, t1.* FROM atividade_terapeutica t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atividade_terapeutica;alter table auditschema.atividade_terapeutica add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atividade_terapeutica FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.equipe AS SELECT t2.*, t1.* FROM equipe t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_equipe;alter table auditschema.equipe add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON equipe FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.ecomapa_item AS SELECT t2.*, t1.* FROM ecomapa_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_ecomapa_item;alter table auditschema.ecomapa_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ecomapa_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.sugestao_sintoma AS SELECT t2.*, t1.* FROM sugestao_sintoma t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_sugestao_sintoma;alter table auditschema.sugestao_sintoma add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON sugestao_sintoma FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_encaminhamento_acolhimento AS SELECT t2.*, t1.* FROM tipo_encaminhamento_acolhimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_encaminhamento_acolhimento;alter table auditschema.tipo_encaminhamento_acolhimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_encaminhamento_acolhimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.sintoma AS SELECT t2.*, t1.* FROM sintoma t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_sintoma;alter table auditschema.sintoma add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON sintoma FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_sintoma AS SELECT t2.*, t1.* FROM tipo_sintoma t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_sintoma;alter table auditschema.tipo_sintoma add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_sintoma FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.sms_controle_integracao AS SELECT t2.*, t1.* FROM sms_controle_integracao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_sms_controle_integracao;alter table auditschema.sms_controle_integracao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON sms_controle_integracao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.integracao_exame AS SELECT t2.*, t1.* FROM integracao_exame t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_integracao_exame;alter table auditschema.integracao_exame add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON integracao_exame FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.confirmacao_inventario_processo AS SELECT t2.*, t1.* FROM confirmacao_inventario_processo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_confirmacao_inventario_processo;alter table auditschema.confirmacao_inventario_processo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON confirmacao_inventario_processo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_internacao AS SELECT t2.*, t1.* FROM atendimento_internacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_internacao;alter table auditschema.atendimento_internacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_internacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_alta AS SELECT t2.*, t1.* FROM atendimento_alta t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_alta;alter table auditschema.atendimento_alta add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_alta FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.leito_liberacao AS SELECT t2.*, t1.* FROM leito_liberacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_leito_liberacao;alter table auditschema.leito_liberacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON leito_liberacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.leito_quarto AS SELECT t2.*, t1.* FROM leito_quarto t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_leito_quarto;alter table auditschema.leito_quarto add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON leito_quarto FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.ficha_acolhimento AS SELECT t2.*, t1.* FROM ficha_acolhimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_ficha_acolhimento;alter table auditschema.ficha_acolhimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ficha_acolhimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.convenio_quarto AS SELECT t2.*, t1.* FROM convenio_quarto t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_convenio_quarto;alter table auditschema.convenio_quarto add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON convenio_quarto FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.quarto_internacao AS SELECT t2.*, t1.* FROM quarto_internacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_quarto_internacao;alter table auditschema.quarto_internacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON quarto_internacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.devolucao_medicamento_item_lote AS SELECT t2.*, t1.* FROM devolucao_medicamento_item_lote t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_devolucao_medicamento_item_lote;alter table auditschema.devolucao_medicamento_item_lote add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON devolucao_medicamento_item_lote FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.cuidados AS SELECT t2.*, t1.* FROM cuidados t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_cuidados;alter table auditschema.cuidados add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON cuidados FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.devolucao_medicamento AS SELECT t2.*, t1.* FROM devolucao_medicamento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_devolucao_medicamento;alter table auditschema.devolucao_medicamento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON devolucao_medicamento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.devolucao_medicamento_item AS SELECT t2.*, t1.* FROM devolucao_medicamento_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_devolucao_medicamento_item;alter table auditschema.devolucao_medicamento_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON devolucao_medicamento_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_transferencia_leito AS SELECT t2.*, t1.* FROM atendimento_transferencia_leito t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_transferencia_leito;alter table auditschema.atendimento_transferencia_leito add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_transferencia_leito FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.nodo_cbo AS SELECT t2.*, t1.* FROM nodo_cbo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_nodo_cbo;alter table auditschema.nodo_cbo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON nodo_cbo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.ato_operatorio AS SELECT t2.*, t1.* FROM ato_operatorio t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_ato_operatorio;alter table auditschema.ato_operatorio add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ato_operatorio FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.ato_operatorio_item AS SELECT t2.*, t1.* FROM ato_operatorio_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_ato_operatorio_item;alter table auditschema.ato_operatorio_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ato_operatorio_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exportacao_esus_processo AS SELECT t2.*, t1.* FROM exportacao_esus_processo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exportacao_esus_processo;alter table auditschema.exportacao_esus_processo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exportacao_esus_processo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.reversao_inventario_processo AS SELECT t2.*, t1.* FROM reversao_inventario_processo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_reversao_inventario_processo;alter table auditschema.reversao_inventario_processo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON reversao_inventario_processo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.endereco_domicilio AS SELECT t2.*, t1.* FROM endereco_domicilio t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_endereco_domicilio;alter table auditschema.endereco_domicilio add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON endereco_domicilio FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.aih AS SELECT t2.*, t1.* FROM aih t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_aih;alter table auditschema.aih add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON aih FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.motivo_visita_domiciliar AS SELECT t2.*, t1.* FROM motivo_visita_domiciliar t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_motivo_visita_domiciliar;alter table auditschema.motivo_visita_domiciliar add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON motivo_visita_domiciliar FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.visita_domiciliar_motivo AS SELECT t2.*, t1.* FROM visita_domiciliar_motivo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_visita_domiciliar_motivo;alter table auditschema.visita_domiciliar_motivo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON visita_domiciliar_motivo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.visita_domiciliar AS SELECT t2.*, t1.* FROM visita_domiciliar t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_visita_domiciliar;alter table auditschema.visita_domiciliar add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON visita_domiciliar FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.usuario_cadsus_esus AS SELECT t2.*, t1.* FROM usuario_cadsus_esus t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_usuario_cadsus_esus;alter table auditschema.usuario_cadsus_esus add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON usuario_cadsus_esus FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.ocorrencia_conta_paciente AS SELECT t2.*, t1.* FROM ocorrencia_conta_paciente t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_ocorrencia_conta_paciente;alter table auditschema.ocorrencia_conta_paciente add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ocorrencia_conta_paciente FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.produtos_brasindice AS SELECT t2.*, t1.* FROM produtos_brasindice t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_produtos_brasindice;alter table auditschema.produtos_brasindice add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON produtos_brasindice FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.pedido_transferencia AS SELECT t2.*, t1.* FROM pedido_transferencia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_pedido_transferencia;alter table auditschema.pedido_transferencia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON pedido_transferencia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_unidade_prestador AS SELECT t2.*, t1.* FROM exame_unidade_prestador t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_unidade_prestador;alter table auditschema.exame_unidade_prestador add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_unidade_prestador FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.producao_cadastro_equipe_cfg AS SELECT t2.*, t1.* FROM producao_cadastro_equipe_cfg t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_producao_cadastro_equipe_cfg;alter table auditschema.producao_cadastro_equipe_cfg add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON producao_cadastro_equipe_cfg FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.producao_cadastro_equipe AS SELECT t2.*, t1.* FROM producao_cadastro_equipe t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_producao_cadastro_equipe;alter table auditschema.producao_cadastro_equipe add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON producao_cadastro_equipe FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.agenda_grade_atendimento AS SELECT t2.*, t1.* FROM agenda_grade_atendimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_agenda_grade_atendimento;alter table auditschema.agenda_grade_atendimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON agenda_grade_atendimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.agenda_grade_horario AS SELECT t2.*, t1.* FROM agenda_grade_horario t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_agenda_grade_horario;alter table auditschema.agenda_grade_horario add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON agenda_grade_horario FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.equipe_micro_area AS SELECT t2.*, t1.* FROM equipe_micro_area t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_equipe_micro_area;alter table auditschema.equipe_micro_area add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON equipe_micro_area FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.profissional AS SELECT t2.*, t1.* FROM profissional t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_profissional;alter table auditschema.profissional add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON profissional FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_prestador AS SELECT t2.*, t1.* FROM exame_prestador t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_prestador;alter table auditschema.exame_prestador add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_prestador FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_procedimento AS SELECT t2.*, t1.* FROM tipo_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_procedimento;alter table auditschema.tipo_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_unidade_prestador_comp AS SELECT t2.*, t1.* FROM exame_unidade_prestador_comp t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_unidade_prestador_comp;alter table auditschema.exame_unidade_prestador_comp add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_unidade_prestador_comp FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.agenda_gra_ate_horario AS SELECT t2.*, t1.* FROM agenda_gra_ate_horario t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_agenda_gra_ate_horario;alter table auditschema.agenda_gra_ate_horario add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON agenda_gra_ate_horario FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_prestador_ipe AS SELECT t2.*, t1.* FROM tipo_prestador_ipe t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_prestador_ipe;alter table auditschema.tipo_prestador_ipe add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_prestador_ipe FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_transferencia_setor AS SELECT t2.*, t1.* FROM atendimento_transferencia_setor t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_transferencia_setor;alter table auditschema.atendimento_transferencia_setor add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_transferencia_setor FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.sequencial_conta_paciente AS SELECT t2.*, t1.* FROM sequencial_conta_paciente t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_sequencial_conta_paciente;alter table auditschema.sequencial_conta_paciente add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON sequencial_conta_paciente FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.dispensacao_medicamento AS SELECT t2.*, t1.* FROM dispensacao_medicamento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_dispensacao_medicamento;alter table auditschema.dispensacao_medicamento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON dispensacao_medicamento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.requisicao_hiv AS SELECT t2.*, t1.* FROM requisicao_hiv t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requisicao_hiv;alter table auditschema.requisicao_hiv add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requisicao_hiv FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.aih_processo AS SELECT t2.*, t1.* FROM aih_processo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_aih_processo;alter table auditschema.aih_processo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON aih_processo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.lote_aih AS SELECT t2.*, t1.* FROM lote_aih t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_lote_aih;alter table auditschema.lote_aih add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON lote_aih FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.convenio AS SELECT t2.*, t1.* FROM convenio t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_convenio;alter table auditschema.convenio add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON convenio FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.empresa_mantenedora AS SELECT t2.*, t1.* FROM empresa_mantenedora t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_empresa_mantenedora;alter table auditschema.empresa_mantenedora add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON empresa_mantenedora FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_requisicao AS SELECT t2.*, t1.* FROM exame_requisicao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_requisicao;alter table auditschema.exame_requisicao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_requisicao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_exame AS SELECT t2.*, t1.* FROM tipo_exame t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_exame;alter table auditschema.tipo_exame add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_exame FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.estado AS SELECT t2.*, t1.* FROM estado t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_estado;alter table auditschema.estado add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON estado FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.estoque_empresa AS SELECT t2.*, t1.* FROM estoque_empresa t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_estoque_empresa;alter table auditschema.estoque_empresa add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON estoque_empresa FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.produtos AS SELECT t2.*, t1.* FROM produtos t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_produtos;alter table auditschema.produtos add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON produtos FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.produto_manutencao_preco AS SELECT t2.*, t1.* FROM produto_manutencao_preco t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_produto_manutencao_preco;alter table auditschema.produto_manutencao_preco add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON produto_manutencao_preco FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.registro_itens_nota_fiscal AS SELECT t2.*, t1.* FROM registro_itens_nota_fiscal t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_registro_itens_nota_fiscal;alter table auditschema.registro_itens_nota_fiscal add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON registro_itens_nota_fiscal FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_atendimento_agenda AS SELECT t2.*, t1.* FROM tipo_atendimento_agenda t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_atendimento_agenda;alter table auditschema.tipo_atendimento_agenda add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_atendimento_agenda FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.cidade AS SELECT t2.*, t1.* FROM cidade t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_cidade;alter table auditschema.cidade add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON cidade FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.async_process AS SELECT t2.*, t1.* FROM async_process t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_async_process;alter table auditschema.async_process add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON async_process FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.producao_ipe_processo AS SELECT t2.*, t1.* FROM producao_ipe_processo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_producao_ipe_processo;alter table auditschema.producao_ipe_processo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON producao_ipe_processo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.mensagem_anexo AS SELECT t2.*, t1.* FROM mensagem_anexo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_mensagem_anexo;alter table auditschema.mensagem_anexo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON mensagem_anexo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.mensagem AS SELECT t2.*, t1.* FROM mensagem t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_mensagem;alter table auditschema.mensagem add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON mensagem FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.empresa AS SELECT t2.*, t1.* FROM empresa t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_empresa;alter table auditschema.empresa add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON empresa FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.usuarios AS SELECT t2.*, t1.* FROM usuarios t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_usuarios;alter table auditschema.usuarios add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON usuarios FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tabela_cbo AS SELECT t2.*, t1.* FROM tabela_cbo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tabela_cbo;alter table auditschema.tabela_cbo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tabela_cbo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.subgrupo AS SELECT t2.*, t1.* FROM subgrupo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_subgrupo;alter table auditschema.subgrupo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON subgrupo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.usuario_cadsus AS SELECT t2.*, t1.* FROM usuario_cadsus t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_usuario_cadsus;alter table auditschema.usuario_cadsus add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON usuario_cadsus FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.processo_avaliacao_estoque AS SELECT t2.*, t1.* FROM processo_avaliacao_estoque t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_processo_avaliacao_estoque;alter table auditschema.processo_avaliacao_estoque add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON processo_avaliacao_estoque FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.usuario_cadsus_dado AS SELECT t2.*, t1.* FROM usuario_cadsus_dado t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_usuario_cadsus_dado;alter table auditschema.usuario_cadsus_dado add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON usuario_cadsus_dado FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento_item AS SELECT t2.*, t1.* FROM atendimento_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_item;alter table auditschema.atendimento_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.empresa_nat_proc_tp_atendimento AS SELECT t2.*, t1.* FROM empresa_nat_proc_tp_atendimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_empresa_nat_proc_tp_atendimento;alter table auditschema.empresa_nat_proc_tp_atendimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON empresa_nat_proc_tp_atendimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atividade_grupo_profissional AS SELECT t2.*, t1.* FROM atividade_grupo_profissional t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atividade_grupo_profissional;alter table auditschema.atividade_grupo_profissional add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atividade_grupo_profissional FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.item_conta_paciente AS SELECT t2.*, t1.* FROM item_conta_paciente t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_item_conta_paciente;alter table auditschema.item_conta_paciente add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON item_conta_paciente FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_atividade_elo_tema AS SELECT t2.*, t1.* FROM tipo_atividade_elo_tema t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_atividade_elo_tema;alter table auditschema.tipo_atividade_elo_tema add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_atividade_elo_tema FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_atividade_grupo AS SELECT t2.*, t1.* FROM tipo_atividade_grupo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_atividade_grupo;alter table auditschema.tipo_atividade_grupo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_atividade_grupo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_atividade_elo_publico AS SELECT t2.*, t1.* FROM tipo_atividade_elo_publico t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_atividade_elo_publico;alter table auditschema.tipo_atividade_elo_publico add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_atividade_elo_publico FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_atividade_publico AS SELECT t2.*, t1.* FROM tipo_atividade_publico t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_atividade_publico;alter table auditschema.tipo_atividade_publico add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_atividade_publico FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atividade_elo_publico AS SELECT t2.*, t1.* FROM atividade_elo_publico t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atividade_elo_publico;alter table auditschema.atividade_elo_publico add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atividade_elo_publico FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_atividade_tema AS SELECT t2.*, t1.* FROM tipo_atividade_tema t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_atividade_tema;alter table auditschema.tipo_atividade_tema add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_atividade_tema FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atividade_elo_tema AS SELECT t2.*, t1.* FROM atividade_elo_tema t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atividade_elo_tema;alter table auditschema.atividade_elo_tema add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atividade_elo_tema FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atividade_grupo AS SELECT t2.*, t1.* FROM atividade_grupo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atividade_grupo;alter table auditschema.atividade_grupo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atividade_grupo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.sisvan_alimentacao AS SELECT t2.*, t1.* FROM sisvan_alimentacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_sisvan_alimentacao;alter table auditschema.sisvan_alimentacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON sisvan_alimentacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento AS SELECT t2.*, t1.* FROM procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento;alter table auditschema.procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_esus AS SELECT t2.*, t1.* FROM procedimento_esus t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_esus;alter table auditschema.procedimento_esus add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_esus FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_atendimento AS SELECT t2.*, t1.* FROM tipo_atendimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_atendimento;alter table auditschema.tipo_atendimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_atendimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.conduta AS SELECT t2.*, t1.* FROM conduta t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_conduta;alter table auditschema.conduta add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON conduta FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.atendimento AS SELECT t2.*, t1.* FROM atendimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento;alter table auditschema.atendimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.conta_paciente AS SELECT t2.*, t1.* FROM conta_paciente t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_conta_paciente;alter table auditschema.conta_paciente add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON conta_paciente FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_esus AS SELECT t2.*, t1.* FROM exame_esus t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_esus;alter table auditschema.exame_esus add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_esus FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.exame_procedimento AS SELECT t2.*, t1.* FROM exame_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_exame_procedimento;alter table auditschema.exame_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON exame_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.classificacao_atendimento AS SELECT t2.*, t1.* FROM classificacao_atendimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_classificacao_atendimento;alter table auditschema.classificacao_atendimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON classificacao_atendimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.movimento_estoque AS SELECT t2.*, t1.* FROM movimento_estoque t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_movimento_estoque;alter table auditschema.movimento_estoque add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON movimento_estoque FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.procedimento_elo_esus AS SELECT t2.*, t1.* FROM procedimento_elo_esus t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_procedimento_elo_esus;alter table auditschema.procedimento_elo_esus add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON procedimento_elo_esus FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.formulario_tabagismo AS SELECT t2.*, t1.* FROM formulario_tabagismo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_formulario_tabagismo;alter table auditschema.formulario_tabagismo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON formulario_tabagismo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.teste_rapido AS SELECT t2.*, t1.* FROM teste_rapido t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_teste_rapido;alter table auditschema.teste_rapido add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON teste_rapido FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.classificacao_contabil AS SELECT t2.*, t1.* FROM classificacao_contabil t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_classificacao_contabil;alter table auditschema.classificacao_contabil add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON classificacao_contabil FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.diario_bordo_veiculo AS SELECT t2.*, t1.* FROM diario_bordo_veiculo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_diario_bordo_veiculo;alter table auditschema.diario_bordo_veiculo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON diario_bordo_veiculo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.produto_complementar_prescricao AS SELECT t2.*, t1.* FROM produto_complementar_prescricao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_produto_complementar_prescricao;alter table auditschema.produto_complementar_prescricao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON produto_complementar_prescricao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.roteiro_viagem_passageiro AS SELECT t2.*, t1.* FROM roteiro_viagem_passageiro t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_roteiro_viagem_passageiro;alter table auditschema.roteiro_viagem_passageiro add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON roteiro_viagem_passageiro FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.roteiro_viagem AS SELECT t2.*, t1.* FROM roteiro_viagem t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_roteiro_viagem;alter table auditschema.roteiro_viagem add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON roteiro_viagem FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.solicitacao_viagem_ocorrencia AS SELECT t2.*, t1.* FROM solicitacao_viagem_ocorrencia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_solicitacao_viagem_ocorrencia;alter table auditschema.solicitacao_viagem_ocorrencia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON solicitacao_viagem_ocorrencia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.solicitacao_viagem AS SELECT t2.*, t1.* FROM solicitacao_viagem t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_solicitacao_viagem;alter table auditschema.solicitacao_viagem add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON solicitacao_viagem FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.mobile_log AS SELECT t2.*, t1.* FROM mobile_log t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_mobile_log;alter table auditschema.mobile_log add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON mobile_log FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.tipo_transporte_viagem AS SELECT t2.*, t1.* FROM tipo_transporte_viagem t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_transporte_viagem;alter table auditschema.tipo_transporte_viagem add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_transporte_viagem FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.item_kit_produto AS SELECT t2.*, t1.* FROM item_kit_produto t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_item_kit_produto;alter table auditschema.item_kit_produto add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON item_kit_produto FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.visita_domiciliar_procedimento AS SELECT t2.*, t1.* FROM visita_domiciliar_procedimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_visita_domiciliar_procedimento;alter table auditschema.visita_domiciliar_procedimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON visita_domiciliar_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.nodo_recepcao_grupo AS SELECT t2.*, t1.* FROM nodo_recepcao_grupo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_nodo_recepcao_grupo;alter table auditschema.nodo_recepcao_grupo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON nodo_recepcao_grupo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.veiculo_empresa AS SELECT t2.*, t1.* FROM veiculo_empresa t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_veiculo_empresa;alter table auditschema.veiculo_empresa add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON veiculo_empresa FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE TABLE auditschema.kit_produto AS SELECT t2.*, t1.* FROM kit_produto t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_kit_produto;alter table auditschema.kit_produto add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON kit_produto FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();