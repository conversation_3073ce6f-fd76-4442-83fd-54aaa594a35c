SET application_name = 'flyway|3.1.21.21';
SET statement_timeout TO 600000;

/*
    <PERSON> 08/11/2019 - #AMB-89
/*
DELETE FROM cbo_ficha_esus_item cfei WHERE cd_cbo_ficha_esus_item = (SELECT cd_cbo_ficha_esus_item FROM cbo_ficha_esus_item t1 LEFT JOIN cbo_ficha_esus t2 ON(t1.cd_cbo_ficha_esus = t2.cd_cbo_ficha_esus) WHERE t2.ficha = 3 AND t1.cd_cbo IN('515305'));

INSERT INTO cbo_ficha_esus_item (cd_cbo_ficha_esus_item,cd_cbo_ficha_esus,cd_cbo,version) SELECT nextval('seq_gem'),3,'226305',0;
INSERT INTO cbo_ficha_esus_item (cd_cbo_ficha_esus_item,cd_cbo_ficha_esus,cd_cbo,version) SELECT nextval('seq_gem'),3,'226110',0;
INSERT INTO cbo_ficha_esus_item (cd_cbo_ficha_esus_item,cd_cbo_ficha_esus,cd_cbo,version) SELECT nextval('seq_gem'),3,'234410',0;
INSERT INTO cbo_ficha_esus_item (cd_cbo_ficha_esus_item,cd_cbo_ficha_esus,cd_cbo,version) SELECT nextval('seq_gem'),3,'226105',0;

/*
    Adriano - 11/11/2019 - #AMB-95
*/

alter table esus_ficha_procedimento add column dt_atendimento_fim timestamp;
alter table auditschema.esus_ficha_procedimento add column dt_atendimento_fim timestamp;

INSERT INTO cbo_ficha_esus_item (cd_cbo_ficha_esus_item,cd_cbo_ficha_esus,cd_cbo,version) SELECT nextval('seq_gem'),6,'226310',0;
INSERT INTO cbo_ficha_esus_item (cd_cbo_ficha_esus_item,cd_cbo_ficha_esus,cd_cbo,version) SELECT nextval('seq_gem'),6,'226315',0;
INSERT INTO cbo_ficha_esus_item (cd_cbo_ficha_esus_item,cd_cbo_ficha_esus,cd_cbo,version) SELECT nextval('seq_gem'),6,'226320',0;
INSERT INTO cbo_ficha_esus_item (cd_cbo_ficha_esus_item,cd_cbo_ficha_esus,cd_cbo,version) SELECT nextval('seq_gem'),6,'324205',0;
INSERT INTO cbo_ficha_esus_item (cd_cbo_ficha_esus_item,cd_cbo_ficha_esus,cd_cbo,version) SELECT nextval('seq_gem'),6,'226110',0;
INSERT INTO cbo_ficha_esus_item (cd_cbo_ficha_esus_item,cd_cbo_ficha_esus,cd_cbo,version) SELECT nextval('seq_gem'),6,'226105',0;
INSERT INTO cbo_ficha_esus_item (cd_cbo_ficha_esus_item,cd_cbo_ficha_esus,cd_cbo,version) SELECT nextval('seq_gem'),6,'322125',0;
INSERT INTO cbo_ficha_esus_item (cd_cbo_ficha_esus_item,cd_cbo_ficha_esus,cd_cbo,version) SELECT nextval('seq_gem'),6,'324205',0;