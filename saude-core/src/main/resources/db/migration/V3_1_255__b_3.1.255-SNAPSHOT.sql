SET application_name = 'flyway|3.1.255';

SET statement_timeout TO 600000;

/* <PERSON> 24/09/2024 */

--<PERSON><PERSON><PERSON> tabela requerimento_inspecao_sanitaria_area_fisica
CREATE TABLE IF NOT EXISTS requerimento_inspecao_sanitaria_area_fisica(
    cd_requerimento_inspecao_sanitaria_area_fisica int8 PRIMARY KEY NOT NULL,
    cd_requerimento_inspecao_sanitaria int8,
    area_construida int8,
    area NUMERIC(12,4),
    version int8,
    FOREIGN KEY (cd_requerimento_inspecao_sanitaria) REFERENCES requerimento_inspecao_sanitaria(cd_requerimento_inspecao_sanitaria) ON DELETE CASCADE
);

--<PERSON><PERSON><PERSON> tabela requerimento_inspecao_sanitaria_fornecedor
CREATE TABLE IF NOT EXISTS requerimento_inspecao_sanitaria_fornecedor(
    cd_requerimento_inspecao_sanitaria_fornecedor int8 PRIMARY KEY NOT NULL,
    cd_requerimento_inspecao_sanitaria int8,
    ds_empresa varchar(60),
    endereco varchar,
    etapa_fabricacao_processo varchar,
    version int8,
    FOREIGN KEY (cd_requerimento_inspecao_sanitaria) REFERENCES requerimento_inspecao_sanitaria(cd_requerimento_inspecao_sanitaria) ON DELETE CASCADE
);

--Criando tabela requerimento_inspecao_sanitaria_funcionario_envolvido
CREATE TABLE IF NOT EXISTS requerimento_inspecao_sanitaria_funcionario_envolvido(
    cd_requerimento_inspecao_sanitaria_funcionario_envolvido int8 PRIMARY KEY NOT NULL,
    cd_requerimento_inspecao_sanitaria int8,
    numero_turnos_horarios varchar(60),
    area int8,
    numero_funcionarios int8,
    version int8,
    FOREIGN KEY (cd_requerimento_inspecao_sanitaria) REFERENCES requerimento_inspecao_sanitaria(cd_requerimento_inspecao_sanitaria) ON DELETE CASCADE
);

--Criando tabela requerimento_inspecao_sanitaria_funcionario_responsavel
CREATE TABLE IF NOT EXISTS requerimento_inspecao_sanitaria_funcionario_responsavel(
    cd_requerimento_inspecao_sanitaria_funcionario_responsavel int8 PRIMARY KEY NOT NULL,
    cd_requerimento_inspecao_sanitaria int8,
    nm_funcionario varchar(70),
    formacao varchar,
    area int8,
    version int8,
    FOREIGN KEY (cd_requerimento_inspecao_sanitaria) REFERENCES requerimento_inspecao_sanitaria(cd_requerimento_inspecao_sanitaria) ON DELETE CASCADE
);
--Criando tabela requerimento_inspecao_sanitaria_pessoa_contato
CREATE TABLE IF NOT EXISTS requerimento_inspecao_sanitaria_pessoa_contato(
    cd_requerimento_inspecao_sanitaria_pessoa_contato int8 PRIMARY KEY NOT NULL,
    cd_requerimento_inspecao_sanitaria int8,
    nome varchar(50),
    cargo varchar,
    telefone varchar(15),
    email varchar(50),
    fax varchar(20),
    version int8,
    FOREIGN KEY (cd_requerimento_inspecao_sanitaria) REFERENCES requerimento_inspecao_sanitaria(cd_requerimento_inspecao_sanitaria) ON DELETE CASCADE
);

--Criando tabela requerimento_inspecao_sanitaria_prestador
CREATE TABLE IF NOT EXISTS requerimento_inspecao_sanitaria_prestador(
    cd_requerimento_inspecao_sanitaria_prestador int8 PRIMARY KEY NOT NULL,
    ds_empresa varchar(60),
    endereco varchar,
    etapa_fabricacao_processo varchar,
    cd_requerimento_inspecao_sanitaria int8,
    version int8,
    FOREIGN KEY (cd_requerimento_inspecao_sanitaria) REFERENCES requerimento_inspecao_sanitaria(cd_requerimento_inspecao_sanitaria) ON DELETE CASCADE
);
--Criando tabela requerimento_inspecao_sanitaria_produto
CREATE TABLE IF NOT EXISTS requerimento_inspecao_sanitaria_produto(
    cd_requerimento_inspecao_sanitaria_produto int8 PRIMARY KEY NOT NULL,
    cd_requerimento_inspecao_sanitaria int8,
    tipo_produto int8,
    linha int8,
    classificacao_risco int8,
    version int8,
    FOREIGN KEY (cd_requerimento_inspecao_sanitaria) REFERENCES requerimento_inspecao_sanitaria(cd_requerimento_inspecao_sanitaria) ON DELETE CASCADE
);
--Criando tabela requerimento_inspecao_sanitaria_relacao_produto
CREATE TABLE IF NOT EXISTS requerimento_inspecao_sanitaria_relacao_produto (
    cd_requerimento_inspecao_sanitaria_relacao_produto int8 PRIMARY KEY NOT NULL,
    tipo_produto int,
    ds_produto varchar(100),
    nm_tecnico varchar,
    registro varchar,
    classificacao_risco int,
    dt_registro timestamp,
    cd_requerimento_inspecao_sanitaria int8,
    version int8,
    FOREIGN KEY (cd_requerimento_inspecao_sanitaria) REFERENCES requerimento_inspecao_sanitaria(cd_requerimento_inspecao_sanitaria) ON DELETE CASCADE
);

--Criando tabela requerimento_inspecao_sanitaria_transportador
CREATE TABLE IF NOT EXISTS requerimento_inspecao_sanitaria_transportador(
    cd_requerimento_inspecao_sanitaria_transportador int8 PRIMARY KEY NOT NULL,
    ds_empresa varchar(60),
    endereco varchar,
    numero_afe varchar,
    cd_requerimento_inspecao_sanitaria int8,
    version int8,
    FOREIGN KEY (cd_requerimento_inspecao_sanitaria) REFERENCES requerimento_inspecao_sanitaria(cd_requerimento_inspecao_sanitaria) ON DELETE CASCADE
);

ALTER TABLE requerimento_inspecao_sanitaria ADD COLUMN dt_inspecao TIMESTAMP;
ALTER TABLE auditschema.requerimento_inspecao_sanitaria ADD COLUMN dt_inspecao TIMESTAMP;

create sequence seq_requerimento_inspecao_sanitaria_area_fisica start with 1;
create sequence seq_requerimento_inspecao_sanitaria_fornecedor start with 1;
create sequence seq_requerimento_inspecao_sanitaria_funcionario_envolvido start with 1;
create sequence seq_requerimento_inspecao_sanitaria_funcionario_responsavel start with 1;
create sequence seq_requerimento_inspecao_sanitaria_pessoa_contato start with 1;
create sequence seq_requerimento_inspecao_sanitaria_prestador start with 1;
create sequence seq_requerimento_inspecao_sanitaria_produto start with 1;
create sequence seq_requerimento_inspecao_sanitaria_relacao_produto start with 1;
create sequence seq_requerimento_inspecao_sanitaria_transportador start with 1;


CREATE TABLE auditschema.requerimento_inspecao_sanitaria_area_fisica AS SELECT t2.*, t1.* FROM requerimento_inspecao_sanitaria_area_fisica t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requerimento_inspecao_sanitaria_area_fisica;
alter table auditschema.requerimento_inspecao_sanitaria_area_fisica add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requerimento_inspecao_sanitaria_area_fisica FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE TABLE auditschema.requerimento_inspecao_sanitaria_fornecedor AS SELECT t2.*, t1.* FROM requerimento_inspecao_sanitaria_fornecedor t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requerimento_inspecao_sanitaria_fornecedor;
alter table auditschema.requerimento_inspecao_sanitaria_fornecedor add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requerimento_inspecao_sanitaria_fornecedor FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE TABLE auditschema.requerimento_inspecao_sanitaria_funcionario_envolvido AS SELECT t2.*, t1.* FROM requerimento_inspecao_sanitaria_funcionario_envolvido t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requerimento_inspecao_sanitaria_funcionario_envolvido;
alter table auditschema.requerimento_inspecao_sanitaria_funcionario_envolvido add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requerimento_inspecao_sanitaria_funcionario_envolvido FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE TABLE auditschema.requerimento_inspecao_sanitaria_funcionario_responsavel AS SELECT t2.*, t1.* FROM requerimento_inspecao_sanitaria_funcionario_responsavel t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requerimento_inspecao_sanitaria_funcionario_responsavel;
alter table auditschema.requerimento_inspecao_sanitaria_funcionario_responsavel add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requerimento_inspecao_sanitaria_funcionario_responsavel FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE TABLE auditschema.requerimento_inspecao_sanitaria_pessoa_contato AS SELECT t2.*, t1.* FROM requerimento_inspecao_sanitaria_pessoa_contato t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requerimento_inspecao_sanitaria_pessoa_contato;
alter table auditschema.requerimento_inspecao_sanitaria_pessoa_contato add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requerimento_inspecao_sanitaria_pessoa_contato FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE TABLE auditschema.requerimento_inspecao_sanitaria_prestador AS SELECT t2.*, t1.* FROM requerimento_inspecao_sanitaria_prestador t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requerimento_inspecao_sanitaria_prestador;
alter table auditschema.requerimento_inspecao_sanitaria_prestador add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requerimento_inspecao_sanitaria_prestador FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE TABLE auditschema.requerimento_inspecao_sanitaria_produto AS SELECT t2.*, t1.* FROM requerimento_inspecao_sanitaria_produto t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requerimento_inspecao_sanitaria_produto;
alter table auditschema.requerimento_inspecao_sanitaria_produto add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requerimento_inspecao_sanitaria_produto FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE TABLE auditschema.requerimento_inspecao_sanitaria_relacao_produto AS SELECT t2.*, t1.* FROM requerimento_inspecao_sanitaria_relacao_produto t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requerimento_inspecao_sanitaria_relacao_produto;
alter table auditschema.requerimento_inspecao_sanitaria_relacao_produto add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requerimento_inspecao_sanitaria_relacao_produto FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE TABLE auditschema.requerimento_inspecao_sanitaria_transportador AS SELECT t2.*, t1.* FROM requerimento_inspecao_sanitaria_transportador t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requerimento_inspecao_sanitaria_transportador;
alter table auditschema.requerimento_inspecao_sanitaria_transportador add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requerimento_inspecao_sanitaria_transportador FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

/*
    Carlos Silva
*/

alter table atendimento_primario add column score_news_sinais_vitais int2;
alter table auditschema.atendimento_primario add column score_news_sinais_vitais int2;

alter table atendimento_primario add column nivel_consciencia_avpu int2;
alter table auditschema.atendimento_primario add column nivel_consciencia_avpu int2;

alter table atendimento_primario add column uso_oxigenacao_suplementar int2;
alter table auditschema.atendimento_primario add column uso_oxigenacao_suplementar int2;