SET application_name = 'flyway|3.1.276';

SET statement_timeout TO 600000;

/*
    feature/AMB-4440 - <PERSON> | 27/03/2025
*/

INSERT INTO agendador_processo (cd_agendador_processo, cd_processo, nm_servico, ds_servico, status, version, tp_processo)
values (99, 99, 'Reenvio de Aviso dos Agendamentos Locais (Sem Solicitação)',
        'SMS que será reenviado alguns dias antes do agendamento para lembrar o paciente.', 1, 1, 0);

/* feature/REG-2005 - Carlos */

alter table tipo_procedimento add column direcionamento_fila varchar(1);
alter table auditschema.tipo_procedimento add column direcionamento_fila varchar(1);

/*
    Roni Al<PERSON> 04/04/2025 - REG-2094
*/

create table ocorrencia_passageiro_viagem(
	cd_ocorrencia_roteiro_viagem int8 not null,
	cd_roteiro_viagem int8 not null,
	cd_usuario_cadastro numeric(6) NOT null,
	tipo_ocorrencia int8 not null,
	dt_cadastro timestamp NOT null,
	ds_ocorrencia varchar(200) not null,
	"version" int8 NULL
);


alter table ocorrencia_passageiro_viagem
  add constraint PK_OCORRENCIA_PASSAGEIRO_VIAGEM primary key (cd_ocorrencia_roteiro_viagem);

alter table ocorrencia_passageiro_viagem
        add constraint FK_OCORRENCIA_PASSAGEIRO_VIAGEM_REF_USUARIO foreign key (cd_usuario_cadastro)
        references usuarios (cd_usuario)
        on delete RESTRICT on update RESTRICT;

alter table ocorrencia_passageiro_viagem
        add constraint FK_OCORRENCIA_PASSAGEIRO_VIAGEM_REF_ROT_VIAGEM foreign key (cd_roteiro_viagem)
        references roteiro_viagem (cd_roteiro)
        on delete restrict on update restrict;

CREATE TABLE auditschema.ocorrencia_passageiro_viagem AS SELECT t2.*, t1.* FROM ocorrencia_passageiro_viagem t1, audit_temp t2 WHERE 1=2;
create sequence seq_ocorrencia_passageiro_viagem;
create sequence seq_audit_id_ocorrencia_passageiro_viagem;
alter table auditschema.ocorrencia_passageiro_viagem add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ocorrencia_passageiro_viagem FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

-- VAC-839 Haila 09/04/2025
insert into grupo_atendimento_vacinacao_esus (cd_grupo_atendimento_vac_esus,descricao,version,codigo_esus,codigo_rnds,ativo) values (134, 'Profissionais dos Correios', 0, 134, 002101,1);
