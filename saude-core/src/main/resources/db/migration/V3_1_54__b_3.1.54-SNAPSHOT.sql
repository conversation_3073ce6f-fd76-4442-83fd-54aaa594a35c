SET application_name = 'flyway|3.1.54';

SET statement_timeout TO 600000;

/*
    Maicon - 15/10/2020 - VSA-1213
*/
ALTER TABLE requerimento_vigilancia ADD COLUMN tp_inspecao int2;
ALTER TABLE auditschema.requerimento_vigilancia ADD COLUMN tp_inspecao int2;

update requerimento_vigilancia set tp_inspecao = 0 where tp_inspecao is null and tipo_documento in (0,1,2,40,41) and situacao in (12,13);

/*
    Maicon - 07/10/2020 - VSA-1154
*/

alter table classificacao_grupo_estab add column cor varchar(6) null;
alter table auditschema.classificacao_grupo_estab add column cor varchar(6) null;

alter table classificacao_grupo_estab add column prazo_inspecao_presencial int4 null;
alter table auditschema.classificacao_grupo_estab add column prazo_inspecao_presencial int4 null;

alter table classificacao_grupo_estab add column tp_prazo_inspecao int2 null;
alter table auditschema.classificacao_grupo_estab add column tp_prazo_inspecao int2 null;

update classificacao_grupo_estab
	set ds_cla_grupo_estab = 'Não se aplica a VISA',
		cor = 'd1d1d1',
		prazo_inspecao_presencial = 0,
		tp_prazo_inspecao = 1
where cd_cla_grupo_estab = 1;

INSERT INTO classificacao_grupo_estab (cd_cla_grupo_estab,ds_cla_grupo_estab,"version",cor,prazo_inspecao_presencial,tp_prazo_inspecao) VALUES (2,'Baixo risco',0,'78d3a9',3,1);
INSERT INTO classificacao_grupo_estab (cd_cla_grupo_estab,ds_cla_grupo_estab,"version",cor,prazo_inspecao_presencial,tp_prazo_inspecao) VALUES (3,'Médio risco',0,'ffff99',2,1);
INSERT INTO classificacao_grupo_estab (cd_cla_grupo_estab,ds_cla_grupo_estab,"version",cor,prazo_inspecao_presencial,tp_prazo_inspecao) VALUES (4,'Alto risco', 0,'ff8989',1,1);