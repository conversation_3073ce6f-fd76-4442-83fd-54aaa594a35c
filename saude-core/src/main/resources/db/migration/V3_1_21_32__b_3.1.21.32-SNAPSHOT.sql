SET application_name = 'flyway|3.1.21.32';
SET statement_timeout TO 600000;

/*
    Laudecir - AMB-126
*/
create index idx_dom_usu_cadsus_situacao on dom_usuario_cadsus (situacao);

/*
    Everton - AMB-187 / AMB-126
*/

CREATE TABLE dom_endereco_domicilio
(
   id int8 not null,
   ds_logradouro varchar not null,
   nr_logradouro varchar,
   ds_bairro varchar,
   cep varchar(10),
   ds_comp_logradouro varchar,
   ds_cidade varchar,
   ds_uf varchar,
   ponto_referencia varchar,
   nr_familia int4,
   ds_pac_nome varchar,
   ds_pac_nome_social varchar,
   flag_responsavel int4,
   dt_nascimento date,
   ds_area varchar,
   micro_area int4,
   keyword varchar,
   cd_endereco int8,
   cd_domicilio int8,
   cd_usu_cadsus int8,
   cd_usu_cadsus_responsavel int8,
   cod_cid int8,
   cd_equipe_area int8,
   cd_eqp_micro_area int8
);

ALTER TABLE dom_endereco_domicilio
    ADD CONSTRAINT pk_dom_endereco_domicilio PRIMARY KEY (id);

ALTER TABLE dom_endereco_domicilio
    ADD CONSTRAINT fk_dom_enddom_ref_endereco FOREIGN KEY (cd_endereco)
    REFERENCES endereco_usuario_cadsus (cd_endereco)
    ON DELETE restrict
    ON UPDATE restrict;

ALTER TABLE dom_endereco_domicilio
    ADD CONSTRAINT fk_dom_enddom_ref_domicilio FOREIGN KEY (cd_domicilio)
    REFERENCES endereco_domicilio (cd_domicilio)
    ON DELETE restrict
    ON UPDATE restrict;

ALTER TABLE dom_endereco_domicilio
    ADD CONSTRAINT fk_dom_enddom_ref_cadsus FOREIGN KEY (cd_usu_cadsus)
    REFERENCES usuario_cadsus (cd_usu_cadsus)
    ON DELETE restrict
    ON UPDATE restrict;

ALTER TABLE dom_endereco_domicilio
    ADD CONSTRAINT fk_dom_enddom_ref_cadsus_resp FOREIGN KEY (cd_usu_cadsus_responsavel)
    REFERENCES usuario_cadsus (cd_usu_cadsus)
    ON DELETE restrict
    ON UPDATE restrict;

ALTER TABLE dom_endereco_domicilio
    ADD CONSTRAINT fk_dom_enddom_ref_cidade FOREIGN KEY (cod_cid)
    REFERENCES cidade (cod_cid)
    ON DELETE restrict
    ON UPDATE restrict;

ALTER TABLE dom_endereco_domicilio
    ADD CONSTRAINT fk_dom_enddom_ref_area FOREIGN KEY (cd_equipe_area)
    REFERENCES equipe_area (cd_equipe_area)
    ON DELETE restrict
    ON UPDATE restrict;

ALTER TABLE dom_endereco_domicilio
    ADD CONSTRAINT fk_dom_enddom_ref_microarea FOREIGN KEY (cd_eqp_micro_area)
    REFERENCES equipe_micro_area (cd_eqp_micro_area)
    ON DELETE restrict
    ON UPDATE restrict;

create index idx_dom_endereco_domicilio_01 on dom_endereco_domicilio(cd_endereco, cd_domicilio, cd_usu_cadsus);
create index idx_dom_endereco_domicilio_02 on dom_endereco_domicilio(cd_usu_cadsus);

create sequence seq_dominio_endereco;

insert into dom_endereco_domicilio (id, ds_logradouro, nr_logradouro, ds_bairro, cep, ds_comp_logradouro,
   ds_cidade, ds_uf, ponto_referencia, nr_familia, ds_pac_nome, ds_pac_nome_social, dt_nascimento, ds_area, micro_area, keyword,
   cd_endereco, cd_domicilio, cd_usu_cadsus, cd_usu_cadsus_responsavel, cod_cid, cd_equipe_area, cd_eqp_micro_area)
 SELECT
   nextval('seq_dominio_endereco'),
   sem_acento(UPPER(trim(coalesce(t9.ds_tipo_logradouro, '')) || ' ' || trim(t1.nm_logradouro))) as logradouro,
   nr_logradouro as numero,
   nm_bairro as bairro,
   cep as cep,
   nm_comp_logradouro as complemento,
   t2.descricao as cidade,
   t3.sigla as uf,
   ponto_referencia as ponto_referencia,
   t4.nr_familia,
   sem_acento(TRIM(UPPER(t8.nm_usuario))),
   sem_acento(TRIM(UPPER(t8.apelido))),
   t8.dt_nascimento,
   t6.ds_area,
   t5.micro_area,
   sem_acento(UPPER(trim(coalesce(t9.ds_tipo_logradouro, '')) || ' ' || trim(t1.nm_logradouro))) || ' ' ||
     TRIM(UPPER(coalesce(nr_logradouro, ''))) || ' ' ||
     sem_acento(TRIM(UPPER(coalesce(nm_bairro, '')))) || ' ' ||
     TRIM(UPPER(coalesce(cep, ''))) as keyword,
   t1.cd_endereco,
   t4.cd_domicilio,
   t8.cd_usu_cadsus,
   t8.cd_usu_cadsus_responsavel,
   t1.cod_cid,
   t6.cd_equipe_area,
   t5.cd_eqp_micro_area
  FROM
    endereco_usuario_cadsus t1
      left join tipo_logradouro_cadsus t9 on (t9.cd_tipo_logradouro = t1.cd_tipo_logradouro),
    cidade t2,
    estado t3,
    endereco_domicilio t4
      left join equipe_micro_area t5 on (t5.cd_eqp_micro_area = t4.cd_eqp_micro_area)
      left join equipe_area t6 on (t6.cd_equipe_area = t5.cd_equipe_area)
      left join usuario_cadsus_domicilio t7 on (t7.cd_domicilio = t4.cd_domicilio and t7.status <> 3)
      left join usuario_cadsus t8 on (t8.cd_usu_cadsus = t7.cd_usu_cadsus)
  where t1.cod_cid = t2.cod_cid
    and t3.cod_est = t2.cod_est
    and t4.cd_endereco = t1.cd_endereco;


insert into dom_endereco_domicilio (id, ds_logradouro, nr_logradouro, ds_bairro, cep, ds_comp_logradouro, ponto_referencia, ds_cidade, ds_uf, keyword, cod_cid, cd_endereco)
 SELECT
  nextval('seq_dominio_endereco'),
  sem_acento(UPPER(trim(coalesce(t4.ds_tipo_logradouro, '')) || ' ' || trim(t1.nm_logradouro))) as logradouro,
  nr_logradouro as numero,
  nm_bairro as bairro,
  cep as cep,
  nm_comp_logradouro as complemento,
  ponto_referencia as ponto_referencia,
  t2.descricao as cidade,
  t3.sigla as uf,
  sem_acento(UPPER(trim(coalesce(t4.ds_tipo_logradouro, '')) || ' ' || trim(t1.nm_logradouro))) || ' ' ||
    TRIM(UPPER(coalesce(nr_logradouro, ''))) || ' ' ||
    sem_acento(TRIM(UPPER(coalesce(nm_bairro, '')))) || ' ' ||
    TRIM(UPPER(coalesce(cep, ''))) as keyword,
  t1.cod_cid,
  t1.cd_endereco
  FROM
    endereco_usuario_cadsus t1
      left join tipo_logradouro_cadsus t4 on (t4.cd_tipo_logradouro = t1.cd_tipo_logradouro),
    cidade t2,
    estado t3
  where t1.cod_cid = t2.cod_cid
    and t3.cod_est = t2.cod_est
    and t1.st_ativo = 1
    and not exists (select 1 from dom_endereco_domicilio where cd_endereco = t1.cd_endereco);

/*------------------------------------------------------------------------*/

CREATE OR REPLACE FUNCTION public.ftg_usuario_cadsus_domicilio()
    RETURNS trigger
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE NOT LEAKPROOF
AS $BODY$
DECLARE
   vexiste integer;
   vcod_endereco integer;
   vcadsus integer;
   c1 record;
BEGIN
   if TG_OP = 'DELETE' THEN
      delete from dom_endereco_domicilio where cd_usu_cadsus_dom = old.cd_usu_cadsus_dom;
   end if;

   if (TG_OP = 'INSERT' or TG_OP = 'UPDATE') then
      if new.status = 3 then
         select cd_endereco into vcod_endereco
           from endereco_domicilio
          where cd_domicilio = new.cd_domicilio;

         DELETE FROM dom_endereco_domicilio
          WHERE cd_endereco   = vcod_endereco
            and cd_domicilio  = new.cd_domicilio
            and cd_usu_cadsus = new.cd_usu_cadsus;
      else
       for c1 in
         SELECT
           sem_acento(UPPER(trim(t9.ds_tipo_logradouro) || ' ' || trim(nm_logradouro))) as logradouro,
           nr_logradouro as numero,
           nm_bairro as bairro,
           cep as cep,
           nm_comp_logradouro as complemento,
           ponto_referencia as ponto,
           t2.descricao as cidade,
           t3.sigla as uf,
           t4.nr_familia as familia,
           sem_acento(TRIM(UPPER(t8.nm_usuario))) as nome,
           sem_acento(TRIM(UPPER(t8.apelido))) as nome_social,
           t8.dt_nascimento as dt_nascimento,
           t8.flag_responsavel_familiar as responsavel,
           t6.ds_area as area,
           t5.micro_area as microarea,
           t1.cod_cid as cod_cid,
           t1.cd_endereco as cod_endereco,
           t8.cd_usu_cadsus as cod_cadsus,
           t8.cd_usu_cadsus_responsavel as cod_cadsus_responsavel,
           t6.cd_equipe_area as eqp_area,
           t5.cd_eqp_micro_area as eqp_microarea,
           t4.cd_domicilio as cod_domicilio
         FROM
           endereco_usuario_cadsus t1
             left join tipo_logradouro_cadsus t9 on (t9.cd_tipo_logradouro = t1.cd_tipo_logradouro),
           cidade t2,
           estado t3,
           endereco_domicilio t4
             left join equipe_micro_area t5 on (t5.cd_eqp_micro_area = t4.cd_eqp_micro_area)
             left join equipe_area t6 on (t6.cd_equipe_area = t5.cd_equipe_area)
             left join usuario_cadsus_domicilio t7 on (t7.cd_domicilio = t4.cd_domicilio and t7.status <> 3)
             left join usuario_cadsus t8 on (t8.cd_usu_cadsus = t7.cd_usu_cadsus)
         where t1.cod_cid = t2.cod_cid
           and t3.cod_est = t2.cod_est
           and t4.cd_endereco = t1.cd_endereco
           and t4.cd_domicilio = t7.cd_domicilio
           and t7.cd_usu_cadsus_dom = new.cd_usu_cadsus_dom
        loop
         delete from dom_endereco_domicilio
             where cd_endereco = c1.cod_endereco
               and cd_usu_cadsus is null;

         -- Avalia se ja existe o dominio cadastrado
         SELECT count(*)
           INTO vexiste
           FROM dom_endereco_domicilio
          WHERE cd_endereco  = c1.cod_endereco
            and cd_domicilio = new.cd_domicilio
            and cd_usu_cadsus   = c1.cod_cadsus;

         if vexiste = 0 then
            insert into dom_endereco_domicilio (id, ds_logradouro, nr_logradouro, ds_bairro, cep, ds_comp_logradouro, ponto_referencia, ds_cidade, ds_uf,
               nr_familia, ds_pac_nome, ds_pac_nome_social, dt_nascimento, flag_responsavel, ds_area, micro_area, keyword, cd_endereco, cd_domicilio, cd_usu_cadsus,
               cd_usu_cadsus_responsavel, cod_cid, cd_equipe_area, cd_eqp_micro_area)
             values (
               nextval('seq_dominio_endereco'),
               c1.logradouro, c1.numero, c1.bairro, c1.cep, c1.complemento, c1.ponto, c1.cidade, c1.uf,
               c1.familia, c1.nome, c1.nome_social, c1.dt_nascimento, c1.responsavel, c1.area, c1.microarea,
               sem_acento(TRIM(UPPER(c1.logradouro))) || ' ' ||
                 TRIM(UPPER(coalesce(c1.numero, ''))) || ' ' ||
                 sem_acento(TRIM(UPPER(coalesce(c1.bairro, '')))) || ' ' ||
                 TRIM(UPPER(coalesce(c1.cep, ''))),
               c1.cod_endereco, new.cd_domicilio, c1.cod_cadsus, c1.cod_cadsus_responsavel, c1.cod_cid, c1.eqp_area, c1.eqp_microarea
              );
         else
            update dom_endereco_domicilio
             set
               ds_logradouro = c1.logradouro,
               nr_logradouro =  c1.numero,
               ds_bairro = c1.bairro,
               cep = c1.cep,
               ds_comp_logradouro = c1.complemento,
               ponto_referencia = c1.ponto,
               ds_cidade = c1.cidade,
               ds_uf = c1.uf,
               nr_familia = c1.familia,
               ds_pac_nome = c1.nome,
               ds_pac_nome_social = c1.nome_social,
               dt_nascimento = c1.dt_nascimento,
               flag_responsavel = c1.responsavel,
               ds_area = c1.area,
               micro_area = c1.microarea,
               keyword = sem_acento(TRIM(UPPER(c1.logradouro))) || ' ' ||
                  TRIM(UPPER(coalesce(c1.numero, ''))) || ' ' ||
                  sem_acento(TRIM(UPPER(coalesce(c1.bairro, '')))) || ' ' ||
                  TRIM(UPPER(coalesce(c1.cep, ''))),
               cd_endereco = c1.cod_endereco,
               cd_domicilio = new.cd_domicilio,
               cd_usu_cadsus = c1.cod_cadsus,
               cd_usu_cadsus_responsavel = c1.cod_cadsus_responsavel,
               cod_cid = c1.cod_cid,
               cd_equipe_area = c1.eqp_area,
               cd_eqp_micro_area = c1.eqp_microarea
            where cd_endereco = c1.cod_endereco
              and cd_domicilio = new.cd_domicilio
              and cd_usu_cadsus   = c1.cod_cadsus;
         end if;
       end loop;
      end if;
      return new;
   end if;
   return old;
END;
$BODY$;

CREATE TRIGGER tbd_dom_usuario_cadsus_domicilio
    BEFORE DELETE
    ON public.usuario_cadsus_domicilio
    FOR EACH ROW
    EXECUTE PROCEDURE public.ftg_usuario_cadsus_domicilio();

CREATE TRIGGER tg_dom_usuario_cadsus_domicilio
    AFTER INSERT OR UPDATE
    ON public.usuario_cadsus_domicilio
    FOR EACH ROW
    EXECUTE PROCEDURE public.ftg_usuario_cadsus_domicilio();

/*------------------------------------------------------------------------*/

CREATE OR REPLACE FUNCTION public.ftg_endereco_usu_cadsus()
    RETURNS trigger
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE NOT LEAKPROOF
AS $BODY$
DECLARE
   vcidade varchar;
   vuf varchar;
   vtipo_logradouro varchar;
   vcod_cid integer;
   vexiste integer;
   c1 record;
BEGIN
   if TG_OP = 'DELETE' THEN
      delete from dom_endereco_domicilio where cd_endereco = old.cd_endereco;
   end if;

   IF (TG_OP = 'UPDATE' and coalesce(new.st_ativo,0) = 0) then
      delete from dom_endereco_domicilio where cd_endereco = new.cd_endereco;
   end if;

   if (TG_OP = 'INSERT' or (TG_OP = 'UPDATE' and coalesce(new.st_ativo,0) = 1)) then
      SELECT
        T4.ds_tipo_logradouro,
        t2.descricao as cidade,
        t3.sigla as uf,
        t1.cod_cid
      INTO
         vtipo_logradouro, vcidade, vuf, vcod_cid
      FROM
        endereco_usuario_cadsus t1
          left join tipo_logradouro_cadsus t4 on (t4.cd_tipo_logradouro = t1.cd_tipo_logradouro),
        cidade t2,
        estado t3
      where
        t1.cod_cid = t2.cod_cid
        and t3.cod_est = t2.cod_est;

      -- Avalia se ja existe o dominio cadastrado
      SELECT count(*)
        INTO vexiste
        FROM dom_endereco_domicilio
       WHERE cd_endereco = new.cd_endereco;

      if vexiste = 0 then
         insert into dom_endereco_domicilio (id, ds_logradouro, nr_logradouro, ds_bairro, cep, ds_comp_logradouro, ds_cidade, ds_uf, ponto_referencia, keyword, cod_cid, cd_endereco)
          values (
            nextval('seq_dominio_endereco'),
            sem_acento(UPPER(trim(vtipo_logradouro) || ' ' || trim(new.nm_logradouro))),
            new.nr_logradouro,
            new.nm_bairro,
            new.cep,
            new.nm_comp_logradouro,
            vcidade,
            vuf,
            new.ponto_referencia,
            sem_acento(UPPER(trim(vtipo_logradouro) || ' ' || trim(new.nm_logradouro))) || ' ' ||
              TRIM(UPPER(coalesce(new.nr_logradouro, ''))) || ' ' ||
              sem_acento(TRIM(UPPER(coalesce(new.nm_bairro, '')))) || ' ' ||
              TRIM(UPPER(coalesce(new.cep, ''))),
            new.cod_cid,
            new.cd_endereco
          );
      else
         update dom_endereco_domicilio
            set
              ds_logradouro = sem_acento(UPPER(trim(vtipo_logradouro) || ' ' || trim(new.nm_logradouro))),
              nr_logradouro = new.nr_logradouro,
              ds_bairro = new.nm_bairro,
              cep = new.cep,
              ds_comp_logradouro = new.nm_comp_logradouro,
              ds_cidade = vcidade,
              ds_uf = vuf,
              ponto_referencia = new.ponto_referencia,
              keyword = sem_acento(UPPER(trim(vtipo_logradouro) || ' ' || trim(new.nm_logradouro))) || ' ' ||
                 TRIM(UPPER(coalesce(new.nr_logradouro, ''))) || ' ' ||
                 sem_acento(TRIM(UPPER(coalesce(new.nm_bairro, '')))) || ' ' ||
                 TRIM(UPPER(coalesce(new.cep, ''))),
              cod_cid = new.cod_cid
         where
            cd_endereco = new.cd_endereco;
      end if;
      return new;
   end if;
   return old;
END;
$BODY$;

CREATE TRIGGER tbd_dom_endereco_domicilio
    BEFORE DELETE
    ON public.endereco_usuario_cadsus
    FOR EACH ROW
    EXECUTE PROCEDURE public.ftg_endereco_usu_cadsus();

CREATE TRIGGER tg_dom_endereco_domicilio
    AFTER INSERT OR UPDATE
    ON public.endereco_usuario_cadsus
    FOR EACH ROW
    EXECUTE PROCEDURE public.ftg_endereco_usu_cadsus();

/*------------------------------------------------------------------------*/

CREATE OR REPLACE FUNCTION public.ftg_endereco_domicilio()
    RETURNS trigger
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE NOT LEAKPROOF
AS $BODY$
DECLARE
   vexiste integer;
   varea varchar;
   vmicroarea integer;
   vcod_eqp_area integer;
   c1 record;
BEGIN
   if TG_OP = 'DELETE' THEN
      update dom_endereco_domicilio
         set
           ds_area = null,
           micro_area = null,
           cd_domicilio = null,
           cd_equipe_area = null,
           cd_eqp_micro_area = null
        where cd_endereco = old.cd_endereco;
   end if;

   if (TG_OP = 'INSERT' or TG_OP = 'UPDATE') then
   	SELECT
         t2.ds_area as area,
         t1.micro_area as microarea,
         t2.cd_equipe_area as eqp_area
        INTO
        	varea, vmicroarea, vcod_eqp_area
        FROM
       	equipe_micro_area t1
      		left join equipe_area t2 on (t2.cd_equipe_area = t1.cd_equipe_area)
 		  where
       	t1.cd_eqp_micro_area	= new.cd_eqp_micro_area;

		update dom_endereco_domicilio
         set
           nr_familia = new.nr_familia,
           ds_area = varea,
           micro_area = vmicroarea,
           cd_domicilio = new.cd_domicilio,
           cd_equipe_area = vcod_eqp_area,
           cd_eqp_micro_area = new.cd_eqp_micro_area
        where cd_endereco = new.cd_endereco;
      return new;
   end if;
   return old;
END;
$BODY$;

CREATE TRIGGER tbd_dom_endereco_domicilio
    BEFORE DELETE
    ON public.endereco_domicilio
    FOR EACH ROW
    EXECUTE PROCEDURE public.ftg_endereco_domicilio();

CREATE TRIGGER tg_dom_endereco_domicilio
    AFTER INSERT OR UPDATE
    ON public.endereco_domicilio
    FOR EACH ROW
    EXECUTE PROCEDURE public.ftg_endereco_domicilio();

/*------------------------------------------------------------------------*/

CREATE OR REPLACE FUNCTION public.ftg_usuario_cadsus_dom_endereco()
    RETURNS trigger
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE NOT LEAKPROOF
AS $BODY$
BEGIN
   if TG_OP = 'UPDATE' then
      if (new.nm_usuario <> old.nm_usuario or coalesce(new.apelido, '') <> coalesce(old.apelido, '') or new.dt_nascimento <> old.dt_nascimento) then
   		update dom_endereco_domicilio
            set
               ds_pac_nome = sem_acento(TRIM(UPPER(new.nm_usuario))),
               ds_pac_nome_social = sem_acento(TRIM(UPPER(new.apelido))),
               dt_nascimento = new.dt_nascimento
         where cd_usu_cadsus = new.cd_usu_cadsus;
     end if;
     return new;
   end if;
   return old;
END;
$BODY$;

CREATE TRIGGER tbd_usuario_cadsus_dom_endereco
    BEFORE DELETE
    ON public.usuario_cadsus
    FOR EACH ROW
    EXECUTE PROCEDURE public.ftg_usuario_cadsus_dom_endereco();

CREATE TRIGGER tg_usuario_cadsus_dom_endereco
    AFTER INSERT OR UPDATE
    ON public.usuario_cadsus
    FOR EACH ROW
    EXECUTE PROCEDURE public.ftg_usuario_cadsus_dom_endereco();

/*
    Silvio - 15/10/2019 #25588
*/
create table configuracao_estratificacao (
     cd_configuracao_estratificacao INT8                 not null,
     cd_doenca_alto_risco           INT4,
     cd_doenca_medio_risco          INT4,
     cd_doenca_baixo_risco          INT4,
     formulario                     INT2                 not null,
     situacao                       int2                 not null,
     cd_usuario                     NUMERIC(6)           not null,
     dt_cadastro                    timestamp            not null,
     version                        int8                 not null
);

alter table configuracao_estratificacao
   add constraint PK_CONFIG_ESTRATIFICACAO primary key (cd_configuracao_estratificacao);

alter table configuracao_estratificacao
   add constraint FK_CONF_ESTR_REF_USUARIO foreign key (cd_usuario)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

alter table configuracao_estratificacao
   add constraint FK_CONF_ESTR_REF_DOENCA_AL foreign key (cd_doenca_alto_risco)
      references doenca (cd_doenca)
      on delete restrict on update restrict;

alter table configuracao_estratificacao
   add constraint FK_CONF_ESTR_REF_DOENCA_ME foreign key (cd_doenca_medio_risco)
      references doenca (cd_doenca)
      on delete restrict on update restrict;

alter table configuracao_estratificacao
   add constraint FK_CONF_ESTR_REF_DOENCA_BA foreign key (cd_doenca_baixo_risco)
      references doenca (cd_doenca)
      on delete restrict on update restrict;

CREATE TABLE auditschema.configuracao_estratificacao AS SELECT t2.*, t1.* FROM configuracao_estratificacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_configuracao_estratificacao;alter table auditschema.configuracao_estratificacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON configuracao_estratificacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();


create table configuracao_estratificacao_cbos (
     cd_configuracao_estratificacao_cbos    INT8                 not null,
     cd_configuracao_estratificacao         INT8                 not null,
     cd_cbo                                 VARCHAR(10)          not null,
     version                                int8                 not null
);

alter table configuracao_estratificacao_cbos
   add constraint PK_CONFIG_ESTRATIFICACAO_CBOS primary key (cd_configuracao_estratificacao_cbos);

alter table configuracao_estratificacao_cbos
   add constraint FK_CONF_ESTR_CBOS_REF_CONF_ESTRAT foreign key (cd_configuracao_estratificacao)
      references configuracao_estratificacao (cd_configuracao_estratificacao)
      on delete restrict on update restrict;

alter table configuracao_estratificacao_cbos
   add constraint FK_CONF_ESTR_CBOS_REF_TABELA_CBO foreign key (cd_cbo)
      references tabela_cbo (cd_cbo)
      on delete restrict on update restrict;

CREATE TABLE auditschema.configuracao_estratificacao_cbos AS SELECT t2.*, t1.* FROM configuracao_estratificacao_cbos t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_configuracao_estratificacao_cbos;alter table auditschema.configuracao_estratificacao_cbos add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON configuracao_estratificacao_cbos FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

INSERT INTO programa_pagina VALUES (1752, 'br.com.celk.view.unidadesaude.estratificacao.configuracao.ConsultaConfiguracaoEstratificacaoPage', 'N');
INSERT INTO programa_pagina VALUES (1753, 'br.com.celk.view.unidadesaude.estratificacao.configuracao.CadastroConfiguracaoEstratificacaoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (971, 'Configuração Estratificação PR', 1752, 'N');
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1221,'Configuração Estratificação PR','',373,971,143,0,0);
INSERT INTO programa_web_pagina VALUES (1874, 971, 1752);
INSERT INTO programa_web_pagina VALUES (1875, 971, 1753);

/*
    Silvio - 15/10/2019 #25466
*/
create table estratificacao_risco (
     cd_estratificacao_risco 		INT8                 not null,
     nr_atendimento           	    INT8                 not null,
     flag_classificacao_risco       INT2                 not null,
     formulario                     INT2                 not null,
     cd_profissional			    INT4                 not null,
     cd_cbo	                        VARCHAR(10)          not null,
     empresa					    INT4			  not null,
     cd_usuario                     NUMERIC(6)           not null,
     dt_cadastro                    timestamp            not null,
     version                        int8                 not null
);

alter table estratificacao_risco
   add constraint PK_ESTRATIFICACAO_RISCO primary key (cd_estratificacao_risco);

alter table estratificacao_risco
   add constraint FK_ESTR_RISCO_REF_ATEND foreign key (nr_atendimento)
      references atendimento (nr_atendimento)
      on delete restrict on update restrict;

alter table estratificacao_risco
   add constraint FK_ESTR_RISCO_REF_PROFISSIONAL foreign key (cd_profissional)
      references profissional (cd_profissional)
      on delete restrict on update restrict;

alter table estratificacao_risco
   add constraint FK_ESTR_RISCO_REF_CBO foreign key (cd_cbo)
      references tabela_cbo (cd_cbo)
      on delete restrict on update restrict;

alter table estratificacao_risco
   add constraint FK_ESTR_RISCO_REF_USUARIO foreign key (cd_usuario)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

CREATE TABLE auditschema.estratificacao_risco AS SELECT t2.*, t1.* FROM estratificacao_risco t1, audit_temp t2 WHERE 1=2; create sequence seq_audit_id_estratificacao_risco; alter table auditschema.estratificacao_risco add primary key (audit_id); CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON estratificacao_risco FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE SEQUENCE seq_estratificacao_risco start with 1;

/*
    Rafael Santos - 13/11/2019 #AMB-41
*/
create table formulario_saude_mental (
     cd_formulario_saude_mental 	 	                                                    INT8    not null,
     cd_estratificacao_risco        	                                                    INT8,
     flag_sensacao_morte_iminente_panico                                                    INT2,
     flag_medo_intenso                                                                      INT2,
     flag_desrealizacao                                                                     INT2,
     flag_despersonalizacao                                                                 INT2,
     flag_crises_conversivas                                                                INT2,
     flag_crise_dissociativa                                                                INT2,
     flag_queixa_somatica_persistente_hipocondriaca                                         INT2,
     flag_pensamentos_comportamentos_repetitivos_conjunto_rituais                           INT2,
     flag_pensamentos_inutilidade_sentimento_culpa                                          INT2,
     flag_tistesa_persistente_acompanhada_nao_choro                                         INT2,
     flag_ideacao_suicida_tentativa_suicidio                                                INT2,
     flag_isolamento_social                                                                 INT2,
     flag_heteroagressividade_autoagressividade                                             INT2,
     flag_desinibicao_social_sexual                                                         INT2,
     flag_hiperatividade_associada_nao_atos_impulsivos                                      INT2,
     flag_euforia                                                                           INT2,
     flag_elevacao_desproporcional_autoestima                                               INT2,
     flag_delirio                                                                           INT2,
     flag_alucinacao                                                                        INT2,
     flag_alteracao_curso_pensamento                                                        INT2,
     flag_perda_juizo_critico_realidade                                                     INT2,
     flag_delirium_tremens                                                                  INT2,
     flag_tremor_associado_halito_etilico_sudorese_etilica                                  INT2,
     flag_incapacidade_reducao_controle_uso_drogas                                          INT2,
     flag_manifestacao_cmportamento_risco_si_terceiros                                      INT2,
     flag_tolerancia                                                                        INT2,
     flagd_dificuldade_manifestada_infancia_adolescencia_compreender_transmitir_informacao  INT2,
     flag_movimentos_corporais_comportamentais_esteriotipados                               INT2,
     flag_desatencao_manifestada_infancia_adolescencia                                      INT2,
     flag_inquietacao_constante_manifestada_infancia_adolescencia                           INT2,
     flag_regrecao                                                                          INT2,
     flag_perda_memoria                                                                     INT2,
     flag_perda_progressiva_capacidade_funcional_ocupacional_social                         INT2,
     flag_desorientacao_temporal_espacial                                                   INT2,
     flag_resistencia_tratamento_refratariedade                                             INT2,
     flag_recorrencia_recaida                                                               INT2,
     flag_uso_abusivo_substancia_psicoativa                                                 INT2,
     flag_exposicao_continua_stresse                                                        INT2,
     flag_precariedade_suporte_social                                                       INT2,
     flag_precariedade_suporte_familiar                                                     INT2,
     flag_testemunha_violencia                                                              INT2,
     flag_autor_vitima_violencia                                                            INT2,
     flag_perda_funcionalidade_familiar_afetiva                                             INT2,
     flag_vulnerabilidade_economica_ambiental                                               INT2,
     flag_comorbidade_outra_condicao_cronica_associada                                      INT2,
     flag_faixa_etaria_6_a_18                                                               INT2,
     flag_abando_atraso_escolar                                                             INT2,
	 observacao                                                                             TEXT,
	 score                                                                                  INT2,
	 flag_risco                                                                             INT2,
     version                       	                                                        INT8    not null
);

alter table formulario_saude_mental
   add constraint PK_FORM_SAUDE_MENTAL primary key (cd_formulario_saude_mental);

alter table formulario_saude_mental
   add constraint FK_FORM_SAU_MENTAL_REF_EST_RISCO foreign key (cd_estratificacao_risco)
      references estratificacao_risco (cd_estratificacao_risco)
      on delete restrict on update restrict;

CREATE TABLE auditschema.formulario_saude_mental AS SELECT t2.*, t1.* FROM formulario_saude_mental t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_formulario_saude_mental;alter table auditschema.formulario_saude_mental add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON formulario_saude_mental FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE SEQUENCE seq_formulario_saude_mental start with 1;

/*
    Adriano - 13/11/2019 #AMB-46
*/
create table formulario_gestantes (
     cd_formulario_gestantes 		    INT8                 not null,
     cd_estratificacao_risco   	        INT8                 not null,
     gestante_negra_indigena            INT2                         ,
     gestante_nao_alfabetizada          INT2                         ,
     gestante_mais_40                   INT2                         ,
     gestante_historico_obito           INT2                         ,
     hipertensao_arterial               INT2                         ,
     dependencia_drogas_ilicitas        INT2                         ,
     cardiopatias                       INT2                         ,
     pneumopatias                       INT2                         ,
     nefropatias                        INT2                         ,
     diabetes                           INT2                         ,
     hipertiroidismo                    INT2                         ,
     maformacao_utero                   INT2                         ,
     epilepsia                          INT2                         ,
     hemopatias                         INT2                         ,
     doencas_infecciosas                INT2                         ,
     doencas_autoimunes                 INT2                         ,
     cirurgia_utero                     INT2                         ,
     hipotiroidismo                     INT2                         ,
     neoplasias                         INT2                         ,
     obesidade_morbida                  INT2                         ,
     cirurgia_bariatrica                INT2                         ,
     psicose                            INT2                         ,
     dependencia_drogas_licitas         INT2                         ,
     doencas_infectocontagiosas         INT2                         ,
     sindrome_hipertensiva              INT2                         ,
     gestacao_gemelar                   INT2                         ,
     isoimunizacao                      INT2                         ,
     diabetes_mellitus                  INT2                         ,
     retardo_crescimento                INT2                         ,
     parto_prematuro                    INT2                         ,
     amniorrexe                         INT2                         ,
     placenta                           INT2                         ,
     sangramento_origem_uterina         INT2                         ,
     maformacao_fetal                   INT2                         ,
     mudanca_imc                        INT2                         ,
     observacao                         varchar(1000)                ,
     flag_risco                         INT2                 not null,
     version                            int8                 not null
);

CREATE TABLE auditschema.formulario_gestantes AS SELECT t2.*, t1.* FROM formulario_gestantes t1, audit_temp t2 WHERE 1=2; create sequence seq_audit_id_formulario_gestantes; alter table auditschema.formulario_gestantes add primary key (audit_id); CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON formulario_gestantes FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE SEQUENCE seq_formulario_gestantes start with 1;

/*
    Silvio - 25/10/2019 #25510
*/
create table formulario_diabetes (
     cd_formulario_diabetes 	 	    INT8                 not null,
     cd_estratificacao_risco        	INT8,
     flag_glicemia_alterada             INT2,
     flag_tolerancia_diminuida          INT2,
     flag_diagnostico                   INT2,
     flag_controle_metabolico           INT2,
     flag_controle_pressorico           INT2,
     flag_hipoglicemia                  INT2,
     flag_cetoacidose                   INT2,
     flag_sindrome_hiperosmolar         INT2,
     flag_retinopatia_diabetica         INT2,
     flag_doenca_renal                  INT2,
     flag_insuficiencia_renal           INT2,
     flag_neuropatia_diabetica          INT2,
     flag_pe_diabetico                  INT2,
     flag_neuropatia_sensitivo          INT2,
     flag_doenca_arterial               INT2,
     flag_acidente_vascular             INT2,
     flag_doenca_vascular               INT2,
	 observacao                         TEXT,
	 score                              INT2,
	 flag_risco                         INT2,
     version                       	    INT8                 not null
);

alter table formulario_diabetes
   add constraint PK_FORM_DIABETES primary key (cd_formulario_diabetes);

alter table formulario_diabetes
   add constraint FK_FORM_DIABETES_REF_EST_RISCO foreign key (cd_estratificacao_risco)
      references estratificacao_risco (cd_estratificacao_risco)
      on delete restrict on update restrict;

CREATE TABLE auditschema.formulario_diabetes AS SELECT t2.*, t1.* FROM formulario_diabetes t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_formulario_diabetes;alter table auditschema.formulario_diabetes add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON formulario_diabetes FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE SEQUENCE seq_formulario_diabetes start with 1;

/*
    Rafael Santos - 14/11/2019 #AMB-41
*/
create table formulario_criancas (
     cd_formulario_criancas 	 	                                                    INT8    not null,
     cd_estratificacao_risco        	                                                    INT8,
     flag_filho_mae_negra                                                                   INT2,
     flag_filho_mae_menos_15_mais_40_anos                                                   INT2,
     flag_filho_mae_historico_obito_gestacao_anterior                                       INT2,
     flag_filho_mae_menos_20_anos_mais_3_partos                                             INT2,
     flag_filho_mae_morreu_parto_puerperio                                                  INT2,
     flag_afeccoes_perinatais                                                               INT2,
     flag_triagem_neonatal_positiva                                                         INT2,
     flag_filho_mae_analfabeta_menos_3_anos_estudo                                          INT2,
     flag_mas_formacoes_congenitas                                                          INT2,
     flag_doenca_transmissao_vertical_confirmada                                            INT2,
     flag_desnutricao_grave                                                                 INT2,
     flag_obesidade                                                                         INT2,
     flag_atraso_desenvolvimento_neuropsicomotor                                            INT2,
     flag_intercorrencias_repetidas_repercucao_clinica                                      INT2,
	 observacao                                                                             TEXT,
	 score                                                                                  INT2,
	 flag_risco                                                                             INT2,
     version                       	                                                        INT8    not null
);

alter table formulario_criancas
   add constraint PK_FORM_CRIANCAS primary key (cd_formulario_criancas);

alter table formulario_criancas
   add constraint FK_FORM_CRIANCAS_REF_EST_RISCO foreign key (cd_estratificacao_risco)
      references estratificacao_risco (cd_estratificacao_risco)
      on delete restrict on update restrict;

CREATE TABLE auditschema.formulario_criancas AS SELECT t2.*, t1.* FROM formulario_criancas t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_formulario_criancas;alter table auditschema.formulario_criancas add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON formulario_criancas FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE SEQUENCE seq_formulario_criancas start with 1;

/*
    Rafael Santos - 14/11/2019 #AMB-41
*/
create table formulario_populacao_exposta_agrotoxico_risco (
     cd_formulario_populacao_exposta_agrotoxico_risco 	 	                                           INT8    not null,
     cd_estratificacao_risco        	                                                               INT8,
     flag_tipo_trabalho_desepenhado                                                                    INT2,
     flag_tempo_exposicao                                                                              INT2,
     flag_natureza_exposicao                                                                           INT2,
     flag_classe_principio_ativo_exposto                                                               INT2,
     flag_historico_intoxicacao_aguda_previa                                                           INT2,
     flagSuspeitaIntoxicacaoAgudaLeveAposUltimoContato                                                 INT2,
     flag_suspeita_intoxicacao_aguda_leve_apos_ultimo_contato                                          INT2,
     flag_sinais_intoxicacao_cronica                                                                   INT2,
     flag_historico_tentativa_suicidio_agrotoxico                                                      INT2,
     flag_gestante                                                                                     INT2,
     flag_menor_idade                                                                                  INT2,
     flag_agricutor_sem_acompanhamento_esf_mais_40_anos                                                INT2,
     flag_agricutor_sem_acompanhamento_esf_menos_40_anos                                               INT2,
     flag_agricutor_com_neoplasia                                                                      INT2,
     flag_agricutor_Com_doenca_psiquiatrica                                                            INT2,
     flag_exames_inespecificos_alterados                                                               INT2,
     flag_exames_especificos_alterados                                                                 INT2,
	 observacao                                                                                        TEXT,
	 score                                                                                             INT2,
	 flag_risco                                                                                        INT2,
     version                       	                                                                   INT8    not null
);

alter table formulario_populacao_exposta_agrotoxico_risco
   add constraint PK_FORM_POPULACAO_EXPOSTA_AGROTOXICO_RICO primary key (cd_formulario_populacao_exposta_agrotoxico_risco);

alter table formulario_populacao_exposta_agrotoxico_risco
   add constraint FK_FORM_POPULACAO_EXPOSTA_AGROTOXICO_RICO_REF_EST_RISCO foreign key (cd_estratificacao_risco)
      references estratificacao_risco (cd_estratificacao_risco)
      on delete restrict on update restrict;

CREATE TABLE auditschema.formulario_populacao_exposta_agrotoxico_risco AS SELECT t2.*, t1.* FROM formulario_populacao_exposta_agrotoxico_risco t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_formulario_populacao_exposta_agrotoxico_risco;alter table auditschema.formulario_populacao_exposta_agrotoxico_risco add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON formulario_populacao_exposta_agrotoxico_risco FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE SEQUENCE seq_formulario_populacao_exposta_agrotoxico_risco start with 1;

/*
    Laudecir - 23/10/2019 - #25494
*/
CREATE TABLE formulario_hipertensao_arterial (
	cd_form_hip_art                                 INT8    NOT NULL,
	cd_estratificacao_risco                         INT2    NOT NULL,
	sexo_masc                                       INT2    NOT NULL,
	idade                                           INT2    NOT NULL,
	tabagismo                                       INT2    NOT NULL,
	doenca_cardio_prematura                         INT2    NOT NULL,
	dislipidemia                                    INT2    NOT NULL,
	resistencia_insulina                            INT2    NOT NULL,
	obesidade                                       INT2    NOT NULL,
	hipertrofia_ventricular_esq                     INT2    NOT NULL,
	espessura_mediointimal_carotida_placa_carotidea INT2    NOT NULL,
	velocidade_onda_pulso_carotido_femoral          INT2    NOT NULL,
	indice_tornozelo_branquial                      INT2    NOT NULL,
    doenca_renal_cronica                            INT2    NOT NULL,
	albuminuria                                     INT2    NOT NULL,
	doenca_cerebrovascular                          INT2    NOT NULL,
	doenca_arteria_coronaria                        INT2    NOT NULL,
	pas                                             INT2    NOT NULL,
	pad                                             INT2    NOT NULL,
    score                                           INT2    NOT NULL,
    flag_risco                                      INT2    NOT NULL,
	observacao                                      TEXT,
	version                                         INT8    NOT NULL
);

ALTER TABLE formulario_hipertensao_arterial
        ADD CONSTRAINT PK_FORM_HIPERTENSAO_ARTERIAL PRIMARY KEY (cd_form_hip_art);

ALTER TABLE formulario_hipertensao_arterial
        ADD CONSTRAINT FK_FORM_HIP_ART_REF_ESTRAT_RISCO FOREIGN KEY (cd_estratificacao_risco)
        REFERENCES estratificacao_risco (cd_estratificacao_risco)
        ON DELETE RESTRICT ON UPDATE RESTRICT;

CREATE TABLE auditschema.formulario_hipertensao_arterial AS SELECT t2.*, t1.* FROM formulario_hipertensao_arterial t1, audit_temp t2 WHERE 1=2; CREATE sequence seq_audit_id_formulario_hipertensao_arterial; ALTER TABLE auditschema.formulario_hipertensao_arterial ADD PRIMARY KEY (audit_id); CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON formulario_hipertensao_arterial FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE SEQUENCE seq_formulario_hipertensao_arterial start WITH 1;

/*
    Rafael Santos - 21/11/2019 #AMB-41
*/
create table formulario_idoso (
     cd_formulario_idoso 	 	                                                                        INT8    not null,
     cd_estratificacao_risco      	                                                                    INT8,
     flag_idade                                                                                         INT2,
     flag_saude_comparando_outros_sua_idade                                                             INT2,
     flag_deixou_fazer_compras_saude_condicao_fisica                                                    INT2,
     flag_deixou_controlar_dinheiro_saude_condicao_fisica                                               INT2,
     flag_deixou_realizar_pequenos_trabalhos_saude_condicao_fisica                                      INT2,
     flag_deixou_tomar_banho_sozinho_saude_condicao_fisica                                              INT2,
     flag_amigo_familiar_falou_ficando_esquecido                                                        INT2,
     flag_esquecimento_piorando_ultimos_meses                                                           INT2,
     flag_esquecimento_impedindo_atividades_cotidiano                                                   INT2,
     flag_ultimo_mes_desanimo_tristeza_desesperanca                                                     INT2,
     flag_perdeu_interesse_prazer_atividades_ant_prazerosas                                             INT2,
     flag_incapaz_levantar_braco_nivel_ombro                                                            INT2,
     flag_incapaz_manusear_segurar_pequenos_objetos                                                     INT2,
     flag_perda_peso_nao_intencional                                                                    INT2,
     flag_imc_menor_22                                                                                  INT2,
     flag_circunferencia_panturrilha_menor_31                                                           INT2,
     flag_problemas_visao_impedir_realizacao_atividades_cotidiano                                       INT2,
     flag_tempo_gasto_teste_velocidade_marcha                                                           INT2,
     flag_dificuldade_caminhar_impeca_realizar_atividades_cotidiano                                     INT2,
     flag_duas_mais_quedas_ano                                                                          INT2,
     flag_perde_urina_fezes_sem_querer                                                                  INT2,
     flag_problemas_audicao_impedir_realizacao_atividades_cotidiano                                     INT2,
     flag_cinco_mais_doencas_cronicas                                                                   INT2,
     flag_uso_regular_cinco_mais_medicamentos_diferentes_dia                                            INT2,
     flag_internacao_recente_ultimos_6_meses                                                            INT2,
	 observacao                                                                                         TEXT,
	 score                                                                                              INT2,
	 flag_risco                                                                                         INT2,
     version                       	                                                                    INT8    not null
);

alter table formulario_idoso
   add constraint PK_FORM_IDOSO primary key (cd_formulario_idoso);

alter table formulario_idoso
   add constraint FK_FORM_IDOSO_REF_EST_RISCO foreign key (cd_estratificacao_risco)
      references estratificacao_risco (cd_estratificacao_risco)
      on delete restrict on update restrict;

CREATE TABLE auditschema.formulario_idoso AS SELECT t2.*, t1.* FROM formulario_idoso t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_formulario_idoso;alter table auditschema.formulario_idoso add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON formulario_idoso FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE SEQUENCE seq_formulario_idoso start with 1;

/*
    Adriano - 30/11/2019 #amb-42
*/
create table formulario_idoso_acs (
     cd_formulario_idoso_acs 	 	    INT8                 not null,
     cd_estratificacao_risco        	INT8,
     idade          	INT2,
     saude_comparado_outras_pessoas   	INT2,
	 curvar_agachar_ajoelhar		           	    INT2,
	 carregar_objetos	           	    INT2,
	 elevar_bracos       	INT2,
	 escrever		           	    INT2,
	 andar       	INT2,
	 servico_domestico		           	    INT2,
	 compras_itens_pessoais		 	    INT2,
	 recebe_ajuda_compras	 	INT2,
	 ajuda_compras_causa_saude          	INT2,
	 lidar_dineheiro	            	INT2,
	 ajuda_lidar_dinheiro          	INT2,
	 ajuda_lidar_dinheiro_causa_saude     	INT2,
	 quarto_andando          		 	    INT2,
	 ajuda_quarto_andando           	INT2,
	 ajuda_quarto_andando_causa_saude	           	    INT2,
	 tarefas_domesticas_leves      	INT2,
	 ajuda_tarefas_domesticas_leves     	INT2,
	 ajuda_tarefas_domesticas_leves_causa_saude	INT2,
	 tomar_banho	    	INT2,
	 ajuda_tomar_banho     	INT2,
	 ajuda_tomar_banho_causa_saude  	INT2,
	 observacao                         TEXT,
	 score                              INT2,
	 flag_risco                         INT2,
     version                       	    INT8                 not null
);

alter table formulario_idoso_acs
   add constraint PK_FORM_IDOSO_ACS primary key (cd_formulario_idoso_acs);

alter table formulario_idoso_acs
   add constraint FK_FORM_IDOSO_ACS_REF_EST_RISCO foreign key (cd_estratificacao_risco)
      references estratificacao_risco (cd_estratificacao_risco)
      on delete restrict on update restrict;

CREATE TABLE auditschema.formulario_idoso_acs AS SELECT t2.*, t1.* FROM formulario_idoso_acs t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_formulario_idoso_acs;alter table auditschema.formulario_idoso_acs add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON formulario_idoso_acs FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE SEQUENCE seq_formulario_idoso_acs start with 1;

/*
    Adriano - 19/11/2019 #AMB-44
*/
create table formulario_populacao_exposta_agrotoxico_rastreio (
     cd_formulario_populacao_exposta_agrotoxico_rastreio 	 	    INT8                 not null,
     cd_estratificacao_risco        	INT8,
     zona_rural          	INT2,
     gestante   	INT2,
	 ocupacao		           	    VARCHAR(80),
	 contato_agrotoxico	           	    INT2,
	 contato_por       	INT2,
	 atividade_agricultura		           	    INT2,
	 atividade_pecuaria       	INT2,
	 atividade_avicultura		           	    INT2,
	 atividade_piscicultura		 	    INT2,
	 atividade_outras	 	INT2,
	 outra_atividade          	VARCHAR(40),
	 nome_agrotoxico	            	VARCHAR(40),
	 tempo_exposicao          	INT2,
	 unidade_tempo_exposicao     	INT2,
	 tem_contato_agrotoxico          		 	    INT2,
	 contato_por_atual           	VARCHAR(100),
	 tempo_exposicao_atual	           	    INT2,
	 tipo_tempo_exposicao_atual      	INT2,
	 data_ultimo_contato     	TIMESTAMP,
	 agrotoxicos_tem_contato	VARCHAR(100),
	 atividade_atual_agricultura	    	INT2,
	 atividade_atual_pecuaria     	INT2,
	 atividade_atual_industria  	INT2,
	 atividade_atual_desinsetizacao        	INT2,
	 atividade_atual_agente_endemias   	INT2,
	 atividade_atual_domestico  INT2,
	 atividade_atual_outros 	INT2,
	 outras_atividades_atual  	VARCHAR(50),
	 forma_contato_atual_preparo    	    INT2,
	 forma_contato_atual_diluicao           	INT2,
	 forma_contato_atual_tratamento_sementes	 	    INT2,
	 forma_contato_atual_aplicacao  	INT2,
	 forma_contato_atual_colheita		INT2,
	 forma_contato_atual_supervisao		INT2,
	 forma_contato_atual_armazenamento		INT2,
	 forma_contato_atual_descarte_embalagem		INT2,
	 forma_contato_atual_limpeza_equipamento		INT2,
	 forma_contato_atual_lavagem_roupa		INT2,
	 forma_contato_atual_carga		INT2,
	 forma_contato_atual_transporte		INT2,
	 forma_contato_atual_controle_expedicao		INT2,
	 forma_contato_atual_producao		INT2,
	 forma_contato_atual_contaminacao_ambiental		INT2,
	 forma_contato_atual_outras		INT2,
	 outras_formas_contato_atual		VARCHAR(50),
	 nro_intoxicacoes		INT4,
	 sintomas_gastrointestinais		INT2,
	 sintomas_sensorio		INT2,
	 sintomas_pele		INT2,
	 sintomas_cardiovascular		INT2,
	 sintomas_resíratoria		INT2,
	 sintomas_nao_lembra		INT2,
	 sintomas_outros		INT2,
	 outros_sintomas        VARCHAR(100),
	 tem_agrotoxico_unidade		INT2,
	 observacao                         TEXT,
     version                       	    INT8                 not null
);

alter table formulario_populacao_exposta_agrotoxico_rastreio
   add constraint PK_FORM_POPULACAO_EXPOSTA_AGROTOXICO_RASTREIO primary key (cd_formulario_populacao_exposta_agrotoxico_rastreio);

alter table formulario_populacao_exposta_agrotoxico_rastreio
   add constraint FK_FORM_POPULACAO_EXPOSTA_AGROTOXICO_RASTREIO_REF_EST_RISCO foreign key (cd_estratificacao_risco)
      references estratificacao_risco (cd_estratificacao_risco)
      on delete restrict on update restrict;

CREATE TABLE auditschema.formulario_populacao_exposta_agrotoxico_rastreio AS SELECT t2.*, t1.* FROM formulario_populacao_exposta_agrotoxico_rastreio t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_formulario_populacao_exposta_agrotoxico_rastreio;alter table auditschema.formulario_populacao_exposta_agrotoxico_rastreio add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON formulario_populacao_exposta_agrotoxico_rastreio FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE SEQUENCE seq_formulario_populacao_exposta_agrotoxico_rastreio start with 1;

/*
    Silvio - 22/10/2019 #25490
*/
create table formulario_saude_bucal (
     cd_formulario_saude_bucal 	 	    INT8                 not null,
     cd_estratificacao_risco        	INT8,
     flag_condicao_cronica          	INT2,
     flag_deficiencia_intelectual   	INT2,
	 flag_acamado		           	    INT2,
	 flag_usa_drogas	           	    INT2,
	 flag_crianca_adolescente       	INT2,
	 flag_dor_dente		           	    INT2,
	 flag_mancha_branca_ativa       	INT2,
	 flag_boca_seca		           	    INT2,
	 flag_lesao_carie_oito		 	    INT2,
	 flag_lesao_carie_quatro_sete	 	INT2,
	 flag_lesao_carie_tres          	INT2,
	 flag_ferida_boca	            	INT2,
	 flag_sangramento_boca          	INT2,
	 flag_dente_permanente_mole     	INT2,
	 flag_protese          		 	    INT2,
	 flag_protese_parcial           	INT2,
	 flag_perda_dente	           	    INT2,
	 flag_necessita_endodontia      	INT2,
	 flag_necessita_periodontia     	INT2,
	 flag_necessita_cirurgia_complexa	INT2,
	 flag_necessita_diagnostico	    	INT2,
	 flag_portador_necessidade_esp     	INT2,
	 flag_cuidador_baixa_escolaridade  	INT2,
	 flag_come_doce_diariamente        	INT2,
	 flag_nao_tem_consumo_agua_fluor   	INT2,
	 flag_nao_tem_consumo_cr_den_fluor  INT2,
	 flag_nao_escova_dente_diariamente 	INT2,
	 flag_so_procura_servico_com_dor  	INT2,
	 flag_mamadeira_sem_higiene    	    INT2,
	 flag_chupeta_adocada           	INT2,
	 flag_nao_colabora_higiene	 	    INT2,
	 flag_faz_higiene_sem_supervisao  	INT2,
	 flag_crianca_dentes_defeitos		INT2,
	 observacao                         TEXT,
	 score                              INT2,
	 flag_risco                         INT2,
     version                       	    INT8                 not null
);

alter table formulario_saude_bucal
   add constraint PK_FORM_SAUDE_BUCAL primary key (cd_formulario_saude_bucal);

alter table formulario_saude_bucal
   add constraint FK_FORM_SAU_BUCAL_REF_EST_RISCO foreign key (cd_estratificacao_risco)
      references estratificacao_risco (cd_estratificacao_risco)
      on delete restrict on update restrict;

CREATE TABLE auditschema.formulario_saude_bucal AS SELECT t2.*, t1.* FROM formulario_saude_bucal t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_formulario_saude_bucal;alter table auditschema.formulario_saude_bucal add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON formulario_saude_bucal FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE SEQUENCE seq_formulario_saude_bucal start with 1;

/*
    Claudio - 12/12/2019
*/
alter table registro_agravo alter column latitude type numeric(15,10);
alter table registro_agravo alter column longitude type numeric(15,10);