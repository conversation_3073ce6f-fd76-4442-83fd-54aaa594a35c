SET application_name = 'flyway|3.1.135';

SET statement_timeout TO 600000;


UPDATE classificacao_atendimento ca
set  cd_esus = '-'
where ca.cd_esus = 'ABP005' and ds_cla_atendimento = 'HIPERDIA';


ALTER TABLE atendimento ADD motivo_consulta varchar(50);
ALTER TABLE auditschema.atendimento ADD motivo_consulta varchar(50);


INSERT INTO programa_pagina VALUES (1940, 'br.com.celk.view.basico.relacaoDiarreiaCid.CadastroRelacionamentoCidRegistroDiarreiaPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (1076, 'Relacionamento de CID x Registro de Diarreia (MDDA)', 1940, 'N');
INSERT INTO programa_web_pagina VALUES (2030, 1076, 1940);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1000085,'Relacionamento CID x registro diarreia','relacionamentoCidxDiarreia',324,1076,143,0,0);

ALTER TABLE cid ADD flag_registro_diarreia INT2 null;
ALTER TABLE auditschema.cid ADD flag_registro_diarreia INT2 null;

/*
    Pedro de Aguiar Marques - GMT-258 - 23/05/2022
*/
alter table tipo_documento add column tipo_rel_livro int2;
alter table auditschema.tipo_documento add column tipo_rel_livro int2;