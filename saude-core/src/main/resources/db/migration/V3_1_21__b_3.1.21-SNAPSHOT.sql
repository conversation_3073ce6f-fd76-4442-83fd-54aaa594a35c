SET application_name = 'flyway|3.1.21';

/*
    Maicon - 15/12/18 - #23062
*/

-- TABLE: vigilancia_profissional
ALTER TABLE vigilancia_profissional DISABLE TRIGGER USER;

alter table vigilancia_profissional add column flag_solic_receita_b1 INT2;
alter table auditschema.vigilancia_profissional add column flag_solic_receita_b1 INT2;
update vigilancia_profissional set flag_solic_receita_b1 = 1;

alter table vigilancia_profissional add column flag_solic_receita_b2 INT2;
alter table auditschema.vigilancia_profissional add column flag_solic_receita_b2 INT2;
update vigilancia_profissional set flag_solic_receita_b2 = 1;

alter table vigilancia_profissional add column flag_solic_receita_c2 INT2;
alter table auditschema.vigilancia_profissional add column flag_solic_receita_c2 INT2;
update vigilancia_profissional set flag_solic_receita_c2 = 1;

alter table vigilancia_profissional add column flag_solic_receita_talidomida INT2;
alter table auditschema.vigilancia_profissional add column flag_solic_receita_talidomida INT2;
update vigilancia_profissional set flag_solic_receita_talidomida = 1;

ALTER TABLE vigilancia_profissional ENABLE TRIGGER USER;

-- TABLE: estabelecimento
ALTER TABLE estabelecimento DISABLE TRIGGER USER;

alter table estabelecimento add column flag_solic_receita_b1 INT2;
alter table auditschema.estabelecimento add column flag_solic_receita_b1 INT2;
update estabelecimento set flag_solic_receita_b1 = 1;

alter table estabelecimento add column flag_solic_receita_b2 INT2;
alter table auditschema.estabelecimento add column flag_solic_receita_b2 INT2;
update estabelecimento set flag_solic_receita_b2 = 1;

alter table estabelecimento add column flag_solic_receita_c2 INT2;
alter table auditschema.estabelecimento add column flag_solic_receita_c2 INT2;
update estabelecimento set flag_solic_receita_c2 = 1;

alter table estabelecimento add column flag_solic_receita_talidomida INT2;
alter table auditschema.estabelecimento add column flag_solic_receita_talidomida INT2;
update estabelecimento set flag_solic_receita_talidomida = 1;

ALTER TABLE estabelecimento ENABLE TRIGGER USER;

/*
    Silvio - 14/12/18 - #23057
*/
alter table configuracao_vigilancia add column flag_seleciona_talao_manual INT2;
alter table auditschema.configuracao_vigilancia add column flag_seleciona_talao_manual INT2;
update configuracao_vigilancia set flag_seleciona_talao_manual = 0;
alter table configuracao_vigilancia alter column flag_seleciona_talao_manual set not null;

/*
    Silvio - 17/12/18 - #23070
*/
alter table configuracao_vigilancia add column flag_tipo_gestao_req INT2;
alter table auditschema.configuracao_vigilancia add column flag_tipo_gestao_req INT2;
update configuracao_vigilancia set flag_tipo_gestao_req = 1;
alter table configuracao_vigilancia alter column flag_tipo_gestao_req set not null;


/*
    Silvio - 17/12/18 - #23125
*/
alter table configuracao_vigilancia add column nr_parecer_inical INT8;
alter table auditschema.configuracao_vigilancia add column nr_parecer_inical INT8;
update configuracao_vigilancia set nr_parecer_inical = 1;

INSERT INTO programa_pagina_permissao VALUES(454, 124, 403, 0, 'parecer');
INSERT INTO programa_pagina_permissao VALUES(456, 124, 1403, 0, 'parecer');

-- TABLE: requerimento_vig_parecer
CREATE TABLE requerimento_vig_parecer (
    cd_req_vig_parecer       INT8         NOT NULL,
    descricao                VARCHAR(100)  NOT NULL,
    cd_req_vigilancia   	    INT8         NOT NULL,
    cd_usuario               NUMERIC(6)   NOT NULL,
    dt_cadastro              TIMESTAMP         NOT NULL,
    dt_parecer              TIMESTAMP         NOT NULL,
    nr_parecer				VARCHAR(50)		NOT NULL,
    ds_parecer			   TEXT 		NOT NULL,
    version                  INT8         NOT NULL
);

ALTER TABLE requerimento_vig_parecer
    ADD CONSTRAINT pk_req_vig_parecer PRIMARY KEY (cd_req_vig_parecer);

ALTER TABLE requerimento_vig_parecer
    ADD CONSTRAINT fk_parecer_ref_req_vig FOREIGN KEY (cd_req_vigilancia)
    REFERENCES requerimento_vigilancia (cd_req_vigilancia)
    ON DELETE restrict
    ON UPDATE restrict;

ALTER TABLE requerimento_vig_parecer
    ADD CONSTRAINT fk_parecer_ref_usuario FOREIGN KEY (cd_usuario)
    REFERENCES usuarios (cd_usuario)
    ON DELETE restrict
    ON UPDATE restrict;

CREATE TABLE AUDITSCHEMA.requerimento_vig_parecer AS SELECT T2.*, T1.* FROM requerimento_vig_parecer T1, AUDIT_TEMP T2 WHERE 1=2;CREATE SEQUENCE SEQ_AUDIT_ID_REQUERIMENTO_VIG_PARECER;ALTER TABLE AUDITSCHEMA.requerimento_vig_parecer ADD PRIMARY KEY (AUDIT_ID);CREATE TRIGGER EMP_AUDIT AFTER INSERT OR UPDATE OR DELETE ON requerimento_vig_parecer FOR EACH ROW EXECUTE PROCEDURE PROCESS_EMP_AUDIT();

CREATE SEQUENCE seq_requerimento_vig_parecer start with 1;

-- TABLE: req_vig_parecer_fiscal
CREATE TABLE req_vig_parecer_fiscal (
    cd_req_vig_parecer_fiscal       INT8         NOT NULL,
    cd_req_vig_parecer              INT8  	    NOT NULL,
    cd_profissional   	            INT8         NOT NULL,
    version                         INT8         NOT NULL
);

ALTER TABLE req_vig_parecer_fiscal
    ADD CONSTRAINT pk_req_vig_par_fisc PRIMARY KEY (cd_req_vig_parecer_fiscal);

ALTER TABLE req_vig_parecer_fiscal
    ADD CONSTRAINT fk_parec_fisc_ref_parecer FOREIGN KEY (cd_req_vig_parecer)
    REFERENCES requerimento_vig_parecer (cd_req_vig_parecer)
    ON DELETE restrict
    ON UPDATE restrict;

ALTER TABLE req_vig_parecer_fiscal
    ADD CONSTRAINT fk_parec_fisc_req_fiscal FOREIGN KEY (cd_profissional)
    REFERENCES profissional (cd_profissional)
    ON DELETE restrict
    ON UPDATE restrict;

CREATE TABLE AUDITSCHEMA.req_vig_parecer_fiscal AS SELECT T2.*, T1.* FROM req_vig_parecer_fiscal T1, AUDIT_TEMP T2 WHERE 1=2;CREATE SEQUENCE SEQ_AUDIT_ID_REQ_VIG_PARECER_FISCAL;ALTER TABLE AUDITSCHEMA.req_vig_parecer_fiscal ADD PRIMARY KEY (AUDIT_ID);CREATE TRIGGER EMP_AUDIT AFTER INSERT OR UPDATE OR DELETE ON req_vig_parecer_fiscal FOR EACH ROW EXECUTE PROCEDURE PROCESS_EMP_AUDIT();
CREATE SEQUENCE seq_req_vig_parecer_fiscal start with 1;

-- TABLE: req_vig_parecer_ativ
CREATE TABLE req_vig_parecer_ativ (
    cd_req_vig_parecer_ativ       INT8         NOT NULL,
    cd_req_vig_parecer              INT8  	    NOT NULL,
    cd_atividades_vigilancia   	    INT8         NOT NULL,
    version                  INT8         NOT NULL
);

ALTER TABLE req_vig_parecer_ativ
    ADD CONSTRAINT pk_req_vig_par_ativ PRIMARY KEY (cd_req_vig_parecer_ativ);

ALTER TABLE req_vig_parecer_ativ
    ADD CONSTRAINT fk_parec_ativ_ref_parecer FOREIGN KEY (cd_req_vig_parecer)
    REFERENCES requerimento_vig_parecer (cd_req_vig_parecer)
    ON DELETE restrict
    ON UPDATE restrict;

ALTER TABLE req_vig_parecer_ativ
    ADD CONSTRAINT fk_parec_ativ_req_ativ FOREIGN KEY (cd_atividades_vigilancia)
    REFERENCES atividades_vigilancia (cd_atividades_vigilancia)
    ON DELETE restrict
    ON UPDATE restrict;

CREATE TABLE AUDITSCHEMA.req_vig_parecer_ativ AS SELECT T2.*, T1.* FROM req_vig_parecer_ativ T1, AUDIT_TEMP T2 WHERE 1=2;CREATE SEQUENCE SEQ_AUDIT_ID_REQ_VIG_PARECER_ATIV;ALTER TABLE AUDITSCHEMA.req_vig_parecer_ativ ADD PRIMARY KEY (AUDIT_ID);CREATE TRIGGER EMP_AUDIT AFTER INSERT OR UPDATE OR DELETE ON req_vig_parecer_ativ FOR EACH ROW EXECUTE PROCEDURE PROCESS_EMP_AUDIT();
CREATE SEQUENCE seq_req_vig_parecer_ativ start with 1;

INSERT INTO programa_pagina VALUES (1624, 'br.com.celk.view.vigilancia.rotinas.parecer.CadastroParecerPage', 'N');
INSERT INTO programa_web_pagina VALUES (1721, 245, 1624);
INSERT INTO programa_web_pagina VALUES (1722, 764, 1624);


alter table auto_penalidade add column cd_req_vigilancia INT8;
alter table auditschema.auto_penalidade add column cd_req_vigilancia INT8;

ALTER TABLE auto_penalidade
    ADD CONSTRAINT fk_auto_pen_ref_req_vig FOREIGN KEY (cd_req_vigilancia)
    REFERENCES requerimento_vigilancia (cd_req_vigilancia)
    ON DELETE restrict
    ON UPDATE restrict;

INSERT INTO permissao_web values (131, 'Auto de Multa', 0);

INSERT INTO programa_pagina_permissao VALUES(459, 131, 1403, 0, '');

/*
    Jonas Américo - 19/12/2018 - #23102
*/

INSERT INTO controle_num_receita_b VALUES(nextval('seq_gem'), 3, 0, 0);

/*
    Leonardo - #23076 - 17/12/18
*/

alter table configuracao_vigilancia add column inicio_prox_dia_util_intimacao int2 null;
alter table auditschema.configuracao_vigilancia add column inicio_prox_dia_util_intimacao int2 null;

alter table configuracao_vigilancia add column inicio_prox_dia_util_infracao int2 null;
alter table auditschema.configuracao_vigilancia add column inicio_prox_dia_util_infracao int2 null;

alter table configuracao_vigilancia add column inicio_prox_dia_util_penalidade int2 null;
alter table auditschema.configuracao_vigilancia add column inicio_prox_dia_util_penalidade int2 null;

alter table configuracao_vigilancia add column inicio_prox_dia_util_multa int2 null;
alter table auditschema.configuracao_vigilancia add column inicio_prox_dia_util_multa int2 null;

UPDATE configuracao_vigilancia SET inicio_prox_dia_util_intimacao = 0;
UPDATE configuracao_vigilancia SET inicio_prox_dia_util_infracao = 0;
UPDATE configuracao_vigilancia SET inicio_prox_dia_util_penalidade = 0;
UPDATE configuracao_vigilancia SET inicio_prox_dia_util_multa = 0;

ALTER TABLE configuracao_vigilancia ADD COLUMN num_auto_multa int8 null;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN num_auto_multa int8 null;
UPDATE configuracao_vigilancia SET num_auto_multa = 0;

ALTER TABLE configuracao_vigilancia ADD COLUMN ciencia_auto_multa varchar(1000) NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN ciencia_auto_multa varchar(1000) NULL;

ALTER TABLE configuracao_vigilancia ADD COLUMN ds_enquad_legal_multa varchar(1000) NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN ds_enquad_legal_multa varchar(1000) NULL;

ALTER TABLE configuracao_vigilancia ADD COLUMN ds_auto_multa varchar(1000) NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN ds_auto_multa varchar(1000) NULL;

ALTER TABLE configuracao_vigilancia ADD COLUMN prazo_auto_multa int4 NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN prazo_auto_multa int4 NULL;

alter table configuracao_vigilancia add column tp_calc_prazo_multa int2;
alter table auditschema.configuracao_vigilancia add column tp_calc_prazo_multa int2;

alter table configuracao_vigilancia add column tp_database_calc_prazo_multa int2;
alter table auditschema.configuracao_vigilancia add column tp_database_calc_prazo_multa int2;

/*
    Leonardo - #23058 - 17/12/18
*/

/*==============================================================*/
/* TABLE: AUTO_MULTA                                   */
/*==============================================================*/
CREATE TABLE AUTO_MULTA (
    CD_AUTO_MULTA                       INT8           NOT NULL,
    NUM_AUTO_MULTA                      INT8           NULL,
    CD_ESTABELECIMENTO                  INT8           NULL,
    CD_VIGILANCIA_PESSOA                INT8           NULL,
    CD_VIGILANCIA_ENDERECO              INT8           NULL,
    CD_AUTO_INTIMACAO                   INT8           NULL,
    CD_AUTO_INFRACAO                    INT8           NULL,
    DS_AUTUADO                          VARCHAR(200)   NULL,
    DS_ENQUADRAMENTO_LEGAL              VARCHAR        NULL,
    DS_CIENCIA                          VARCHAR(1000)  NULL,
    SITUACAO                            INT2           NOT NULL,
    DT_MULTA                            DATE           NOT NULL,
    DS_MULTA                            VARCHAR(5000)  NOT NULL,
    VALOR_MULTA                         NUMERIC(12,2)  NOT NULL,
    TIPO_AUTUADO                        INT2           NOT NULL,
    DT_RECEBIMENTO                      TIMESTAMP      NULL,
    DT_PRAZO_RECURSO                    TIMESTAMP      NULL,
    CD_MOTIVO_RETORNO                   INT8           NULL,
    ENVIADO_AR                          INT2           NULL,
    DT_CADASTRO                         TIMESTAMP      NOT NULL,
    DT_USUARIO                          TIMESTAMP      NOT NULL,
    CD_USUARIO                          NUMERIC(6)     NOT NULL,
    CD_USUARIO_EDICAO                   NUMERIC(6)     NOT NULL,
    CD_PROCESSO_ADM_AUTENTICACAO        INT8           NULL,
    VERSION                             INT8           NOT NULL
);

ALTER TABLE AUTO_MULTA ADD PRIMARY KEY (CD_AUTO_MULTA);
ALTER TABLE AUTO_MULTA ADD CONSTRAINT FK_AUTO_MULTA_REF_VIGILANCIA_END    FOREIGN KEY (CD_VIGILANCIA_ENDERECO)       REFERENCES VIGILANCIA_ENDERECO (CD_VIGILANCIA_ENDERECO);
ALTER TABLE AUTO_MULTA ADD CONSTRAINT FK_AUTO_MULTA_REF_AUTO_INTIMACAO    FOREIGN KEY (CD_AUTO_INTIMACAO)            REFERENCES AUTO_INTIMACAO (CD_AUTO_INTIMACAO);
ALTER TABLE AUTO_MULTA ADD CONSTRAINT FK_AUTO_MULTA_REF_AUTO_INFRACAO     FOREIGN KEY (CD_AUTO_INFRACAO)             REFERENCES AUTO_INFRACAO (CD_AUTO_INFRACAO);
ALTER TABLE AUTO_MULTA ADD CONSTRAINT FK_AUTO_MULTA_REF_ESTABELECIMENTO   FOREIGN KEY (CD_ESTABELECIMENTO)           REFERENCES ESTABELECIMENTO (CD_ESTABELECIMENTO);
ALTER TABLE AUTO_MULTA ADD CONSTRAINT FK_AUTO_MULTA_REF_VIG_PESSOA        FOREIGN KEY (CD_VIGILANCIA_PESSOA)         REFERENCES VIGILANCIA_PESSOA (CD_VIGILANCIA_PESSOA);
ALTER TABLE AUTO_MULTA ADD CONSTRAINT FK_AUTO_MULTA_REF_USUARIO           FOREIGN KEY (CD_USUARIO)                   REFERENCES USUARIOS (CD_USUARIO);
ALTER TABLE AUTO_MULTA ADD CONSTRAINT FK_AUTO_MULTA_REF_USUARIO_EDICAO    FOREIGN KEY (CD_USUARIO_EDICAO)            REFERENCES USUARIOS (CD_USUARIO);
ALTER TABLE AUTO_MULTA ADD CONSTRAINT FK_AUTO_MULTA_REF_PROC_ADM_AUT      FOREIGN KEY (CD_PROCESSO_ADM_AUTENTICACAO) REFERENCES PROCESSO_ADM_AUTENTICACAO (CD_PROCESSO_ADM_AUTENTICACAO);
ALTER TABLE AUTO_MULTA ADD CONSTRAINT FK_AUTO_MULTA_REF_MOTIVO_RETORNO    FOREIGN KEY (CD_MOTIVO_RETORNO)            REFERENCES MOTIVO_RETORNO (CD_MOTIVO_RETORNO);

CREATE TABLE AUDITSCHEMA.AUTO_MULTA AS SELECT T2.*, T1.* FROM AUTO_MULTA T1, AUDIT_TEMP T2 WHERE 1=2;CREATE SEQUENCE SEQ_AUDIT_ID_AUTO_MULTA;ALTER TABLE AUDITSCHEMA.AUTO_MULTA ADD PRIMARY KEY (AUDIT_ID);CREATE TRIGGER EMP_AUDIT AFTER INSERT OR UPDATE OR DELETE ON AUTO_MULTA FOR EACH ROW EXECUTE PROCEDURE PROCESS_EMP_AUDIT();

/*==============================================================*/
/* TABLE: AUTO_MULTA_FISCAL                                */
/*==============================================================*/
CREATE TABLE AUTO_MULTA_FISCAL (
    CD_AUTO_MULTA_FISCAL   INT8           NOT NULL,
    CD_PROFISSIONAL             INT8           NOT NULL,
    CD_AUTO_MULTA          INT8           NOT NULL,
    VERSION                     INT8           NOT NULL
);

ALTER TABLE AUTO_MULTA_FISCAL ADD PRIMARY KEY (CD_AUTO_MULTA_FISCAL);
ALTER TABLE AUTO_MULTA_FISCAL ADD CONSTRAINT FK_AUTO_MULTA_FISCAL_REF_PROFISSIONAL       FOREIGN KEY (CD_PROFISSIONAL)    REFERENCES PROFISSIONAL (CD_PROFISSIONAL);
ALTER TABLE AUTO_MULTA_FISCAL ADD CONSTRAINT FK_AUTO_MULTA_FISCAL_REF_AUTO_MULTA    FOREIGN KEY (CD_AUTO_MULTA) REFERENCES AUTO_MULTA (CD_AUTO_MULTA);

CREATE TABLE AUDITSCHEMA.AUTO_MULTA_FISCAL AS SELECT T2.*, T1.* FROM AUTO_MULTA_FISCAL T1, AUDIT_TEMP T2 WHERE 1=2;CREATE SEQUENCE SEQ_AUDIT_ID_AUTO_MULTA_FISCAL;ALTER TABLE AUDITSCHEMA.AUTO_MULTA_FISCAL ADD PRIMARY KEY (AUDIT_ID);CREATE TRIGGER EMP_AUDIT AFTER INSERT OR UPDATE OR DELETE ON AUTO_MULTA_FISCAL FOR EACH ROW EXECUTE PROCEDURE PROCESS_EMP_AUDIT();

ALTER TABLE auto_penalidade_item ADD COLUMN vl_multa numeric(12,2) NULL;
ALTER TABLE auditschema.auto_penalidade_item ADD COLUMN vl_multa numeric(12,2) NULL;
create sequence seq_auto_multa start with 1;
create sequence seq_auto_multa_fiscal start with 1;

INSERT INTO programa_pagina VALUES (1621, 'br.com.celk.view.vigilancia.rotinas.automulta.ConsultaAutoMultaPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (933, 'Auto de Multa', 1621, 'N');
INSERT INTO programa_web_pagina VALUES (1717, 933, 1621);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (1183,'Auto de Multa','autoDeMulta',818, 933, 307,0,0);
INSERT INTO programa_pagina VALUES (1622, 'br.com.celk.view.vigilancia.rotinas.automulta.CadastroAutoMultaPage', 'N');
INSERT INTO programa_web_pagina VALUES (1718, 933, 1622);

/*
    Mauricley - #23041 - 17/12/2018
*/

alter table configuracao_vigilancia add column flag_obriga_dados_veiculo INT2;
alter table auditschema.configuracao_vigilancia add column flag_obriga_dados_veiculo INT2;
update configuracao_vigilancia set flag_obriga_dados_veiculo = 1;
alter table configuracao_vigilancia alter column flag_obriga_dados_veiculo set not null;

alter table veiculo_estabelecimento alter column placa drop not null;
alter table veiculo_estabelecimento alter column especificacao drop not null;

update tipo_solicitacao set ds_tipo_solicitacao = 'Licença de Transporte/Autorização Sanitária' where tp_requerimento = 4;

insert into permissao_web (cd_permissao, ds_permissao, version) values(128, 'Autorização Sanitária', 0);
insert into programa_pagina_permissao values(451, 128, 1210, 0, 'autorizacaoSanitaria');
insert into programa_pagina_permissao values(452, 128, 1248, 0, 'autorizacaoSanitaria');

/*
    Silvio - 22/12/2018 - #23188
*/
create table talonario_receita_talidomida (
    cd_talonario_rec_talidomida      INT8          not null,
    cd_vigilancia_profissional  INT8          null,
    cd_estabelecimento          INT8          null,
    nro_inicial                 INT8          not null,
    nro_final                   INT8          not null,
    dt_entrada                  TIMESTAMP     not null,
    status                      INT2          not null,
    cd_usuario                  NUMERIC(6)    not null,
    dt_cadastro                 TIMESTAMP     not null,
    version                     INT8          not null
);

alter table talonario_receita_talidomida
     add constraint PK_TAL_RECEITA_talidomida primary key (cd_talonario_rec_talidomida);

alter table talonario_receita_talidomida
     add constraint FK_TAL_RECEITA_TALIDOMIDA_REF_VIG_PRO foreign key (cd_vigilancia_profissional)
     references vigilancia_profissional (cd_vigilancia_profissional);

alter table talonario_receita_talidomida
     add constraint FK_TAL_RECEITA_TALIDOMIDA_REF_USUARIO foreign key (cd_usuario)
     references usuarios (cd_usuario);

alter table talonario_receita_talidomida
    add constraint FK_TAL_RECEITA_TALIDOMIDA_REF_ESTAB foreign key (cd_estabelecimento)
     references estabelecimento (cd_estabelecimento) on delete restrict on update restrict;

CREATE TABLE auditschema.talonario_receita_talidomida AS SELECT t2.*, t1.* FROM talonario_receita_talidomida t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_talonario_receita_talidomida;alter table auditschema.talonario_receita_talidomida add primary key (audit_id);CREATE TRIGGER EMP_AUDIT AFTER INSERT OR UPDATE OR DELETE ON talonario_receita_talidomida FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
create sequence seq_talonario_receita_talidomida start with 1;

INSERT INTO programa_pagina VALUES (1625, 'br.com.celk.view.vigilancia.talonario.ConsultaTalonarioReceitaTalidomidaPage', 'N');
INSERT INTO programa_pagina VALUES (1626, 'br.com.celk.view.vigilancia.talonario.CadastroTalonarioReceitaTalidomidaPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (911, 'Talonário Receita Talidomida', 1625, 'N');
INSERT INTO programa_web_pagina VALUES (1723, 911, 1625);
INSERT INTO programa_web_pagina VALUES (1724, 911, 1626);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1165,'Talonário Receita Talidomida','talonarioReceitaTalidomida',654,911,307,0,0);


create table requerimento_receita_talidomida (
    cd_req_receita_talidomida           INT8          not null,
    cd_req_vigilancia       	         INT8          not null,
    cd_vigilancia_profissional       	INT8          null,
    cd_vigilancia_endereco       	    INT8          null,
    nome_solicitante                    VARCHAR(80)   null,
    cpf_solicitante                     VARCHAR(14)   null,
    rg_solicitante                      VARCHAR(20)   null,
    email_solicitante                   VARCHAR(100)  null,
    cargo_solicitante                   VARCHAR(80)   null,
    cd_usuario                          NUMERIC(6)    not null,
    dt_cadastro                         TIMESTAMP     not null,
    cd_usuario_can                      NUMERIC(6)    null,
    dt_cancelamento                     TIMESTAMP     null,
    version                             INT8          not null
);

alter table requerimento_receita_talidomida
   add constraint PK_REQ_REC_TALIDOMIDA primary key (cd_req_receita_talidomida);

alter table requerimento_receita_talidomida
   add constraint FK_REQ_REC_TALID_REF_REQ_VIG foreign key (cd_req_vigilancia)
      references requerimento_vigilancia (cd_req_vigilancia)
      on delete restrict on update restrict;

alter table requerimento_receita_talidomida
   add constraint FK_REQ_REC_TALID_REF_VIG_PROF foreign key (cd_vigilancia_profissional)
      references vigilancia_profissional (cd_vigilancia_profissional)
      on delete restrict on update restrict;

alter table requerimento_receita_talidomida
   add constraint FK_REQ_REC_TALID_REF_VIG_ENDE foreign key (cd_vigilancia_endereco)
      references vigilancia_endereco (cd_vigilancia_endereco)
      on delete restrict on update restrict;

alter table requerimento_receita_talidomida
   add constraint FK_REQ_REC_TALID_REF_USU foreign key (cd_usuario)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

alter table requerimento_receita_talidomida
   add constraint FK_REQ_REC_TALIDOMIDA_REF_USU_CAN foreign key (cd_usuario_can)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

CREATE TABLE auditschema.requerimento_receita_talidomida AS SELECT t2.*, t1.* FROM requerimento_receita_talidomida t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requerimento_receita_talidomida;alter table auditschema.requerimento_receita_talidomida add primary key (audit_id);CREATE TRIGGER EMP_AUDIT AFTER INSERT OR UPDATE OR DELETE ON requerimento_receita_talidomida FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
create sequence seq_requerimento_receita_talidomida start with 1;

ALTER TABLE requerimento_receita_talidomida ADD COLUMN protocolo INT8 NULL;
ALTER TABLE auditschema.requerimento_receita_talidomida ADD COLUMN protocolo INT8 NULL;

ALTER TABLE configuracao_vigilancia ADD COLUMN iniciar_protocolo_receita_talidomida INT2 NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN iniciar_protocolo_receita_talidomida INT2 NULL;

ALTER TABLE configuracao_vigilancia ADD COLUMN estoque_minimo_receita_talidomida INT2 NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN estoque_minimo_receita_talidomida INT2 NULL;

ALTER TABLE configuracao_vigilancia ADD COLUMN flag_selec_talao_man_talidomida INT2;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN flag_selec_talao_man_talidomida INT2;
UPDATE configuracao_vigilancia SET flag_selec_talao_man_talidomida = 0;
ALTER TABLE configuracao_vigilancia ALTER COLUMN flag_selec_talao_man_talidomida SET not null;

create table requerimento_receita_talidomida_talao (
    cd_req_receita_talid_talao  INT8 not null,
    cd_req_receita_talidomida        INT8 not null,
    cd_talonario_rec_talidomida           INT8 not null,
    version                          INT8 not null
);

alter table requerimento_receita_talidomida_talao
   add constraint PK_REQ_REC_TALID_TALAO primary key (cd_req_receita_talid_talao);

alter table requerimento_receita_talidomida_talao
   add constraint FK_REQ_REC_TALAO_REF_REQ_REC_TALIDOMIDA foreign key (cd_req_receita_talidomida)
      references requerimento_receita_talidomida (cd_req_receita_talidomida);

alter table requerimento_receita_talidomida_talao
   add constraint FK_REQ_REC_TALAO_REF_TALO_TALIDOMIDA foreign key (cd_talonario_rec_talidomida)
      references talonario_receita_talidomida (cd_talonario_rec_talidomida);

CREATE TABLE auditschema.requerimento_receita_talidomida_talao AS SELECT t2.*, t1.* FROM requerimento_receita_talidomida_talao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requerimento_receita_talidomida_talao;alter table auditschema.requerimento_receita_talidomida_talao add primary key (audit_id);CREATE TRIGGER EMP_AUDIT AFTER INSERT OR UPDATE OR DELETE ON requerimento_receita_talidomida_talao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
create sequence seq_requerimento_receita_talidomida_talao start with 1;

INSERT INTO permissao_web values (132, 'Receita Talidomida', 0);
INSERT INTO programa_pagina_permissao (cd_prog_pag_perm,cd_permissao,cd_prg_pagina,version,ds_bundle) VALUES (476, 132, 1210, 0, 'receitaTalidomida');

INSERT INTO programa_pagina VALUES (1627, 'br.com.celk.view.vigilancia.requerimentos.receituario.RequerimentoReceitaTalidomidaPage', 'N');
INSERT INTO programa_web_pagina VALUES (1725, 684, 1627);
INSERT INTO programa_web_pagina VALUES (1726, 245, 1627);
INSERT INTO programa_web_pagina VALUES (1727, 764, 1627);

alter table requerimento_receita_talidomida add column cd_estabelecimento INT8;
alter table auditschema.requerimento_receita_talidomida add column cd_estabelecimento INT8;

alter table requerimento_receita_talidomida
 add constraint FK_REQ_RECEITA_TAL_REF_ESTAB foreign key (cd_estabelecimento)
    references estabelecimento (cd_estabelecimento)
    on delete restrict on update restrict;

ALTER TABLE requerimento_receita_talidomida ADD COLUMN cd_resp_tecnico  BIGINT NULL;
ALTER TABLE auditschema.requerimento_receita_talidomida ADD COLUMN cd_resp_tecnico  BIGINT NULL;
ALTER TABLE requerimento_receita_talidomida ADD COLUMN num_inicial INT8 NULL;
ALTER TABLE auditschema.requerimento_receita_talidomida ADD COLUMN num_inicial INT8 NULL;
ALTER TABLE requerimento_receita_talidomida ADD COLUMN num_final INT8 NULL;
ALTER TABLE auditschema.requerimento_receita_talidomida ADD COLUMN num_final INT8 NULL;

/*
    Laudecir - 27/12/2018
*/

alter table requerimento_vigilancia add column cd_vigilancia_pessoa int8;
alter table auditschema.requerimento_vigilancia add column cd_vigilancia_pessoa int8;

ALTER TABLE requerimento_vigilancia
        ADD CONSTRAINT FK_REQ_VIGILANCIA_REF_VIG_PESSOA
        FOREIGN KEY (cd_vigilancia_pessoa)
        REFERENCES vigilancia_pessoa (cd_vigilancia_pessoa) on delete restrict on update restrict;

/*
    Leonardo - 21/12/2018 -  #23156
*/

ALTER TABLE processo_adm ALTER COLUMN cd_auto_infracao DROP NOT NULL;
ALTER TABLE processo_adm ALTER COLUMN dt_infracao DROP NOT NULL;

ALTER TABLE processo_adm rename dt_infracao to dt_inicio;
ALTER TABLE auditschema.processo_adm rename dt_infracao to dt_inicio;

ALTER TABLE processo_adm ADD tipo INT2 NULL;
ALTER TABLE auditschema.processo_adm ADD tipo INT2 NULL;
UPDATE processo_adm SET tipo = 0 where tipo is null;

ALTER TABLE processo_adm ADD COLUMN cd_auto_multa int8 null;
ALTER TABLE auditschema.processo_adm ADD COLUMN cd_auto_multa int8 null;
ALTER TABLE processo_adm
   add constraint FK_PROC_ADM_REF_AUTO_MULTA foreign key (cd_auto_multa)
      references auto_multa (cd_auto_multa)
      on delete restrict on update restrict;

INSERT INTO programa_pagina_permissao VALUES(465, 21, 1564, 0, 'receber');

alter table processo_adm add column dt_prazo_seg_instancia timestamp;
alter table auditschema.processo_adm add column dt_prazo_seg_instancia timestamp;

alter table processo_adm add column dt_prazo_ter_instancia timestamp;
alter table auditschema.processo_adm add column dt_prazo_ter_instancia timestamp;

alter table configuracao_vigilancia add column flag_terceira_inst_proc_adm INT2;
alter table auditschema.configuracao_vigilancia add column flag_terceira_inst_proc_adm INT2;
UPDATE configuracao_vigilancia SET flag_terceira_inst_proc_adm = 0;


alter table auto_multa add column cd_req_vigilancia INT8;
alter table auditschema.auto_multa add column cd_req_vigilancia INT8;

ALTER TABLE auto_multa
    ADD CONSTRAINT fk_auto_mul_ref_req_vig FOREIGN KEY (cd_req_vigilancia)
    REFERENCES requerimento_vigilancia (cd_req_vigilancia)
    ON DELETE restrict
    ON UPDATE restrict;

/*
    Silvio - 27/12/18 - #23150
*/
update permissao_web set ds_permissao = 'Projeto Básico de Arquitetura' WHERE cd_permissao = 101;
INSERT INTO permissao_web values (133, 'Vistoria para Laudo de conformidade Técnica de PBA', 0);
INSERT INTO permissao_web values (134, 'Análise de Projeto Hidrossanitário', 0);
INSERT INTO permissao_web values (135, 'Vistoria para Habite-se Sanitário', 0);

INSERT INTO programa_pagina_permissao VALUES(466, 133, 1210, 0, 'vistoriaLaudoConformidadePba');
INSERT INTO programa_pagina_permissao VALUES(467, 134, 1210, 0, 'analiseProjetoHidrossanitario');
INSERT INTO programa_pagina_permissao VALUES(468, 135, 1210, 0, 'vistoriaHabiteseSanitario');
INSERT INTO programa_pagina_permissao VALUES(469, 133, 1248, 0, 'vistoriaLaudoConformidadePba');
INSERT INTO programa_pagina_permissao VALUES(470, 134, 1248, 0, 'analiseProjetoHidrossanitario');
INSERT INTO programa_pagina_permissao VALUES(471, 135, 1248, 0, 'vistoriaHabiteseSanitario');

alter table configuracao_vigilancia add column flag_visualizar_dados_obra INT2;
alter table auditschema.configuracao_vigilancia add column flag_visualizar_dados_obra INT2;
update configuracao_vigilancia set flag_visualizar_dados_obra = 1;
alter table configuracao_vigilancia alter column flag_visualizar_dados_obra set not null;

alter table requerimento_analise_projeto add column cd_vigilancia_endereco INT8;
alter table auditschema.requerimento_analise_projeto add column cd_vigilancia_endereco INT8;
alter table requerimento_analise_projeto
   add constraint FK_REQ_AN_PROJETO_REF_VIG_END foreign key (cd_vigilancia_endereco)
      references vigilancia_endereco (cd_vigilancia_endereco)
      on delete restrict on update restrict;

alter table requerimento_analise_projeto add column nr_obra VARCHAR(10);
alter table auditschema.requerimento_analise_projeto add column nr_obra VARCHAR(10);

alter table requerimento_analise_projeto add column quadra_obra VARCHAR(100);
alter table auditschema.requerimento_analise_projeto add column quadra_obra VARCHAR(100);

alter table requerimento_analise_projeto add column nr_lado_obra VARCHAR(10);
alter table auditschema.requerimento_analise_projeto add column nr_lado_obra VARCHAR(10);

alter table requerimento_analise_projeto add column lote_obra VARCHAR(10);
alter table auditschema.requerimento_analise_projeto add column lote_obra VARCHAR(10);

alter table requerimento_analise_projeto add column complemento_obra VARCHAR(100);
alter table auditschema.requerimento_analise_projeto add column complemento_obra VARCHAR(100);

alter table requerimento_analise_projeto add column nr_loteamento_obra VARCHAR(10);
alter table auditschema.requerimento_analise_projeto add column nr_loteamento_obra VARCHAR(10);

ALTER TABLE requerimento_vigilancia ADD COLUMN tp_requerente int2 null;
ALTER TABLE auditschema.requerimento_vigilancia ADD COLUMN tp_requerente int2 null;
ALTER TABLE requerimento_vigilancia DISABLE TRIGGER USER;
UPDATE requerimento_vigilancia SET tp_requerente = 1;
ALTER TABLE requerimento_vigilancia ENABLE TRIGGER USER;

CREATE TABLE req_analise_projeto_parecer (
    cd_req_ana_pro_parecer       INT8         NOT NULL,
    cd_requerimento_analise_projeto   	    INT8         NOT NULL,
    status   	    					    INT2         NOT NULL,
    dt_parecer              			    TIMESTAMP    NOT NULL,
    ds_assunto_parecer             	    TEXT   	  NOT NULL,
    ds_dados_projeto               	    TEXT   	  NOT NULL,
    ds_documentacao_apresentada    	    TEXT   	  NOT NULL,
    ds_adequacao_projeto			   	    TEXT 		  NOT NULL,
    ds_especificacoes_basicas			    TEXT 		  NOT NULL,
    version                			    INT8         NOT NULL
);

ALTER TABLE req_analise_projeto_parecer
    ADD CONSTRAINT pk_req_ana_projeto_parecer PRIMARY KEY (cd_req_ana_pro_parecer);

ALTER TABLE req_analise_projeto_parecer
    ADD CONSTRAINT fk_parecer_ref_req_ana_proj FOREIGN KEY (cd_requerimento_analise_projeto)
    REFERENCES requerimento_analise_projeto (cd_requerimento_analise_projeto)
    ON DELETE restrict
    ON UPDATE restrict;

CREATE TABLE AUDITSCHEMA.req_analise_projeto_parecer AS SELECT T2.*, T1.* FROM req_analise_projeto_parecer T1, AUDIT_TEMP T2 WHERE 1=2;CREATE SEQUENCE SEQ_AUDIT_ID_REQ_ANALISE_PROJETO_PARECER;ALTER TABLE AUDITSCHEMA.req_analise_projeto_parecer ADD PRIMARY KEY (AUDIT_ID);CREATE TRIGGER EMP_AUDIT AFTER INSERT OR UPDATE OR DELETE ON req_analise_projeto_parecer FOR EACH ROW EXECUTE PROCEDURE PROCESS_EMP_AUDIT();
CREATE SEQUENCE seq_req_analise_projeto_parecer start with 1;

CREATE TABLE req_ana_pro_parecer_fiscal (
    cd_req_ana_pro_parecer_fiscal      INT8         NOT NULL,
    cd_req_ana_pro_parecer              INT8  	    NOT NULL,
    cd_req_vig_fiscal   	    INT8         NOT NULL,
    version                  INT8         NOT NULL
);

ALTER TABLE req_ana_pro_parecer_fiscal
    ADD CONSTRAINT pk_req_ana_proj_par_fisc PRIMARY KEY (cd_req_ana_pro_parecer_fiscal);

ALTER TABLE req_ana_pro_parecer_fiscal
    ADD CONSTRAINT fk_proj_par_fiscal_ref_parecer FOREIGN KEY (cd_req_ana_pro_parecer)
    REFERENCES req_analise_projeto_parecer (cd_req_ana_pro_parecer)
    ON DELETE restrict
    ON UPDATE restrict;

ALTER TABLE req_ana_pro_parecer_fiscal
    ADD CONSTRAINT fk_proj_par_fiscal_ref_fiscal FOREIGN KEY (cd_req_vig_fiscal)
    REFERENCES requerimento_vigilancia_fiscal (cd_req_vig_fiscal)
    ON DELETE restrict
    ON UPDATE restrict;

CREATE TABLE AUDITSCHEMA.req_ana_pro_parecer_fiscal AS SELECT T2.*, T1.* FROM req_ana_pro_parecer_fiscal T1, AUDIT_TEMP T2 WHERE 1=2;CREATE SEQUENCE SEQ_AUDIT_ID_REQ_ANA_PRO_PARECER_FISCAL;ALTER TABLE AUDITSCHEMA.req_ana_pro_parecer_fiscal ADD PRIMARY KEY (AUDIT_ID);CREATE TRIGGER EMP_AUDIT AFTER INSERT OR UPDATE OR DELETE ON req_ana_pro_parecer_fiscal FOR EACH ROW EXECUTE PROCEDURE PROCESS_EMP_AUDIT();
CREATE SEQUENCE seq_req_ana_pro_parecer_fiscal start with 1;

alter table requerimento_analise_projeto add column status_parecer INT2;
alter table auditschema.requerimento_analise_projeto add column status_parecer INT2;

CREATE TABLE req_ana_pro_parecer_ativ (
    cd_req_ana_pro_parecer_ativ       INT8         NOT NULL,
    cd_req_ana_pro_parecer              INT8  	    NOT NULL,
    cd_atividades_vigilancia   	    INT8         NOT NULL,
    version                  INT8         NOT NULL
);

ALTER TABLE req_ana_pro_parecer_ativ
    ADD CONSTRAINT pk_req_ana_proj_par_ativ PRIMARY KEY (cd_req_ana_pro_parecer_ativ);

ALTER TABLE req_ana_pro_parecer_ativ
    ADD CONSTRAINT fk_proj_par_ativ_ref_parecer FOREIGN KEY (cd_req_ana_pro_parecer)
    REFERENCES req_analise_projeto_parecer (cd_req_ana_pro_parecer)
    ON DELETE restrict
    ON UPDATE restrict;

ALTER TABLE req_ana_pro_parecer_ativ
    ADD CONSTRAINT fk_proj_par_ativ_ref_ativ FOREIGN KEY (cd_atividades_vigilancia)
    REFERENCES atividades_vigilancia (cd_atividades_vigilancia)
    ON DELETE restrict
    ON UPDATE restrict;

CREATE TABLE AUDITSCHEMA.req_ana_pro_parecer_ativ AS SELECT T2.*, T1.* FROM req_ana_pro_parecer_ativ T1, AUDIT_TEMP T2 WHERE 1=2;CREATE SEQUENCE SEQ_AUDIT_ID_REQ_ANA_PRO_PARECER_ATIV;ALTER TABLE AUDITSCHEMA.req_ana_pro_parecer_ativ ADD PRIMARY KEY (AUDIT_ID);CREATE TRIGGER EMP_AUDIT AFTER INSERT OR UPDATE OR DELETE ON req_ana_pro_parecer_ativ FOR EACH ROW EXECUTE PROCEDURE PROCESS_EMP_AUDIT();
CREATE SEQUENCE seq_req_ana_pro_parecer_ativ start with 1;

/*
    Sulivan - 27/12/18 - #23152
*/
-- TABLE: tipo_enquadramento_projeto
CREATE TABLE tipo_enquadramento_projeto (
    cd_tipo_enquadramento_projeto      INT8                  NOT NULL,
    descricao                          VARCHAR(50)           NOT NULL,
    cd_usuario                         NUMERIC(6)            NOT NULL,
    dt_cadastro                        TIMESTAMP             NOT NULL,
    version                            INT8                  NOT NULL
);

alter table tipo_enquadramento_projeto
   add constraint PK_TIPO_ENQUA_PROJE primary key (cd_tipo_enquadramento_projeto);

ALTER TABLE tipo_enquadramento_projeto
    ADD CONSTRAINT FK_TIPO_ENQUA_PROJE_REF_USU FOREIGN KEY (cd_usuario)
    REFERENCES usuarios (cd_usuario)
    ON DELETE restrict ON UPDATE restrict;

CREATE TABLE auditschema.tipo_enquadramento_projeto AS SELECT t2.*, t1.* FROM tipo_enquadramento_projeto t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_enquadramento_projeto;alter table auditschema.tipo_enquadramento_projeto add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_enquadramento_projeto FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

-- TABLE: requerimento_projeto_hidro
CREATE TABLE requerimento_projeto_hidro (
    cd_requerimento_projeto_hidro       INT8                  NOT NULL,
    cd_req_vigilancia                   INT8                  NOT NULL,
    cd_tipo_enquadramento_projeto       INT8                  NOT NULL,
    obra_endereco                       VARCHAR(100)          NOT NULL,
    obra_nr_endereco                    VARCHAR(5)            NOT NULL,
    obra_quadra                         VARCHAR(10)           NULL,
    obra_nr_lado                        VARCHAR(5)            NULL,
    obra_lote                           VARCHAR(20)           NULL,
    obra_nr_inscricao_imob              VARCHAR(10)           NOT NULL,
    obra_complemento                    VARCHAR(20)           NULL,
    obra_nr_loteamento                  VARCHAR(10)           NULL,
    nr_processo_projeto                 VARCHAR(10)           NOT NULL,
    regiao_coberta_rede_esgoto          INT2                  NOT NULL,
    regiao_abastecida_agua              INT2                  NOT NULL,
    sistema_agua_pluvial                INT2                  NOT NULL,
    uso_edificacao                      INT2                  NULL,
    obs_uso_edificacao                  VARCHAR(100)          NULL,
    parcelamento_solo                   INT2                  NULL,
    parcelamento_solo_nr_lotes          INT4                  NULL,
    area                                NUMERIC(12,4)         NOT NULL,
    area_comercial                      NUMERIC(12,4)         NULL,
    area_residencial                    NUMERIC(12,4)         NULL,
    area_total_construcao               NUMERIC(12,4)         NOT NULL,
    version                             INT8                  NOT NULL
);

alter table requerimento_projeto_hidro
   add constraint PK_REQ_PROJ_HIDRO primary key (cd_requerimento_projeto_hidro);

alter table requerimento_projeto_hidro
   add constraint FK_REQ_PROJ_HIDRO_REF_REQ_VIGI foreign key (cd_req_vigilancia)
      references requerimento_vigilancia (cd_req_vigilancia)
      on delete restrict on update restrict;

alter table requerimento_projeto_hidro
   add constraint FK_REQ_PROJ_HIDRO_REF_TP_ENQ_PROJ foreign key (cd_tipo_enquadramento_projeto)
      references tipo_enquadramento_projeto (cd_tipo_enquadramento_projeto)
      on delete restrict on update restrict;

CREATE TABLE auditschema.requerimento_projeto_hidro AS SELECT t2.*, t1.* FROM requerimento_projeto_hidro t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requerimento_projeto_hidro;alter table auditschema.requerimento_projeto_hidro add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requerimento_projeto_hidro FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE SEQUENCE seq_requerimento_projeto_hidro start with 1;

INSERT INTO programa_pagina VALUES (1630, 'br.com.celk.view.vigilancia.requerimentos.analiseprojetos.projetohidrossanitario.RequerimentoProjetoHidrossanitarioPage', 'N');
INSERT INTO programa_web_pagina VALUES (1731, 764, 1630);
INSERT INTO programa_web_pagina VALUES (1732, 245, 1630);
INSERT INTO programa_web_pagina VALUES (1733, 684, 1630);

INSERT INTO programa_pagina VALUES (1631, 'br.com.celk.view.vigilancia.externo.view.servicos.RequerimentoProjetoHidrossanitarioExternoPage', 'N');
INSERT INTO programa_web_pagina VALUES (1734, 692, 1631);

alter table tipo_projeto_vigilancia add column tipo INT2;
alter table auditschema.tipo_projeto_vigilancia add column tipo INT2;
update tipo_projeto_vigilancia set tipo = 1;
alter table tipo_projeto_vigilancia alter column tipo set not null;


/*
    Silvio - 03/01/2019 - #23151
*/
CREATE TABLE requerimento_vistoria_pba (
    cd_requerimento_vistoria_pba       INT8         	NOT NULL,
    cd_req_vigilancia		   	       INT8         	NOT NULL,
    cd_requerimento_analise_projeto    INT8,
    cd_tp_projeto_vigilancia   	       INT8,
    cd_vigilancia_endereco		       INT8 	        NOT NULL,
    nr_projeto_arquitetonico	       VARCHAR(20),
    area              			       NUMERIC(12,4),
    dt_vistoria             	       TIMESTAMP   	    NOT NULL,
    nr_obra    	                       VARCHAR(10) ,
    quadra_obra    	                   VARCHAR(100),
    nr_lado_obra   	                   VARCHAR(10) ,
    lote_obra    	                   VARCHAR(100),
    complemento_obra                   VARCHAR(100),
    nr_loteamento_obra                 VARCHAR(10) ,
    version             			   INT8             NOT NULL
);

ALTER TABLE requerimento_vistoria_pba
    ADD CONSTRAINT pk_requerimento_vistoria_pba PRIMARY KEY (cd_requerimento_vistoria_pba);

ALTER TABLE requerimento_vistoria_pba
    ADD CONSTRAINT fk_vist_pba_ref_req_vig FOREIGN KEY (cd_req_vigilancia)
    REFERENCES requerimento_vigilancia (cd_req_vigilancia)
    ON DELETE restrict
    ON UPDATE restrict;

ALTER TABLE requerimento_vistoria_pba
    ADD CONSTRAINT fk_vist_pba_ref_req_ana_proj FOREIGN KEY (cd_requerimento_analise_projeto)
    REFERENCES requerimento_analise_projeto (cd_requerimento_analise_projeto)
    ON DELETE restrict
    ON UPDATE restrict;

ALTER TABLE requerimento_vistoria_pba
    ADD CONSTRAINT fk_vist_pba_ref_tp_proj_vig FOREIGN KEY (cd_tp_projeto_vigilancia)
    REFERENCES tipo_projeto_vigilancia (cd_tp_projeto_vigilancia)
    ON DELETE restrict
    ON UPDATE restrict;

ALTER TABLE requerimento_vistoria_pba
    ADD CONSTRAINT fk_vist_pba_ref_vig_endereco FOREIGN KEY (cd_vigilancia_endereco)
    REFERENCES vigilancia_endereco (cd_vigilancia_endereco)
    ON DELETE restrict
    ON UPDATE restrict;

CREATE TABLE AUDITSCHEMA.requerimento_vistoria_pba AS SELECT T2.*, T1.* FROM requerimento_vistoria_pba T1, AUDIT_TEMP T2 WHERE 1=2;CREATE SEQUENCE SEQ_AUDIT_ID_REQUERIMENTO_VISTORIA_PBA;ALTER TABLE AUDITSCHEMA.requerimento_vistoria_pba ADD PRIMARY KEY (AUDIT_ID);CREATE TRIGGER EMP_AUDIT AFTER INSERT OR UPDATE OR DELETE ON requerimento_vistoria_pba FOR EACH ROW EXECUTE PROCEDURE PROCESS_EMP_AUDIT();
CREATE SEQUENCE seq_requerimento_vistoria_pba start with 1;

CREATE TABLE requerimento_vistoria_pba_fiscal (
    cd_req_vistoria_pba_fiscal       INT8         NOT NULL,
    cd_requerimento_vistoria_pba              INT8  	    NOT NULL,
    cd_req_vig_fiscal   	    INT8         NOT NULL,
    version                  INT8         NOT NULL
);

ALTER TABLE requerimento_vistoria_pba_fiscal
    ADD CONSTRAINT pk_req_vis_pba_fisc PRIMARY KEY (cd_req_vistoria_pba_fiscal);

ALTER TABLE requerimento_vistoria_pba_fiscal
    ADD CONSTRAINT fk_vist_pba_fisc_ref_vist_pba FOREIGN KEY (cd_requerimento_vistoria_pba)
    REFERENCES requerimento_vistoria_pba (cd_requerimento_vistoria_pba)
    ON DELETE restrict
    ON UPDATE restrict;

ALTER TABLE requerimento_vistoria_pba_fiscal
    ADD CONSTRAINT fk_vist_pba_fisc_req_fiscal FOREIGN KEY (cd_req_vig_fiscal)
    REFERENCES requerimento_vigilancia_fiscal (cd_req_vig_fiscal)
    ON DELETE restrict
    ON UPDATE restrict;

CREATE TABLE AUDITSCHEMA.requerimento_vistoria_pba_fiscal AS SELECT T2.*, T1.* FROM requerimento_vistoria_pba_fiscal T1, AUDIT_TEMP T2 WHERE 1=2;CREATE SEQUENCE SEQ_AUDIT_ID_REQUERIMENTO_VISTORIA_PBA_FISCAL;ALTER TABLE AUDITSCHEMA.requerimento_vistoria_pba_fiscal ADD PRIMARY KEY (AUDIT_ID);CREATE TRIGGER EMP_AUDIT AFTER INSERT OR UPDATE OR DELETE ON requerimento_vistoria_pba_fiscal FOR EACH ROW EXECUTE PROCEDURE PROCESS_EMP_AUDIT();
CREATE SEQUENCE seq_requerimento_vistoria_pba_fiscal start with 1;

CREATE TABLE requerimento_vistoria_pba_ativ (
    cd_req_vistoria_pba_ativ       INT8         NOT NULL,
    cd_requerimento_vistoria_pba              INT8  	    NOT NULL,
    cd_atividades_vigilancia   	    INT8         NOT NULL,
    version                  INT8         NOT NULL
);

ALTER TABLE requerimento_vistoria_pba_ativ
    ADD CONSTRAINT pk_req_vist_pba_ativ PRIMARY KEY (cd_req_vistoria_pba_ativ);

ALTER TABLE requerimento_vistoria_pba_ativ
    ADD CONSTRAINT fk_vist_pba_ativ_ref_vist_pba FOREIGN KEY (cd_requerimento_vistoria_pba)
    REFERENCES requerimento_vistoria_pba (cd_requerimento_vistoria_pba)
    ON DELETE restrict
    ON UPDATE restrict;

ALTER TABLE requerimento_vistoria_pba_ativ
    ADD CONSTRAINT fk_vist_pba_ativ_req_fiscal FOREIGN KEY (cd_atividades_vigilancia)
    REFERENCES atividades_vigilancia (cd_atividades_vigilancia)
    ON DELETE restrict
    ON UPDATE restrict;

CREATE TABLE AUDITSCHEMA.requerimento_vistoria_pba_ativ AS SELECT T2.*, T1.* FROM requerimento_vistoria_pba_ativ T1, AUDIT_TEMP T2 WHERE 1=2;CREATE SEQUENCE SEQ_AUDIT_ID_REQUERIMENTO_VISTORIA_PBA_ATIV;ALTER TABLE AUDITSCHEMA.requerimento_vistoria_pba_ativ ADD PRIMARY KEY (AUDIT_ID);CREATE TRIGGER EMP_AUDIT AFTER INSERT OR UPDATE OR DELETE ON requerimento_vistoria_pba_ativ FOR EACH ROW EXECUTE PROCEDURE PROCESS_EMP_AUDIT();
CREATE SEQUENCE seq_requerimento_vistoria_pba_ativ start with 1;

alter table requerimento_vistoria_pba add column ds_caracterizacao TEXT;
alter table auditschema.requerimento_vistoria_pba add column ds_caracterizacao TEXT;

alter table requerimento_vistoria_pba add column num_parecer_conformidade_tecnica TEXT;
alter table auditschema.requerimento_vistoria_pba add column num_parecer_conformidade_tecnica TEXT;

alter table requerimento_vistoria_pba add column dt_inspecao date;
alter table auditschema.requerimento_vistoria_pba add column dt_inspecao date;

alter table requerimento_vistoria_pba add column ds_objetivo TEXT;
alter table auditschema.requerimento_vistoria_pba add column ds_objetivo TEXT;

alter table requerimento_vistoria_pba add column ds_constatacoes TEXT;
alter table auditschema.requerimento_vistoria_pba add column ds_constatacoes TEXT;

alter table requerimento_vistoria_pba add column ds_ressalvas TEXT;
alter table auditschema.requerimento_vistoria_pba add column ds_ressalvas TEXT;

alter table requerimento_vistoria_pba add column ds_conclusao TEXT;
alter table auditschema.requerimento_vistoria_pba add column ds_conclusao TEXT;

INSERT INTO programa_pagina VALUES (1636, 'br.com.celk.view.vigilancia.externo.view.servicos.RequerimentoVistoriaProjetoBasicoArquiteturaExternoPage', 'N');
INSERT INTO programa_web_pagina VALUES (1747, 692, 1636);


/*
    Sulivan - 03/01/19 - #23153
*/
-- TABLE: requerimento_vistoria_hidro
CREATE TABLE requerimento_vistoria_hidro (
    cd_requerimento_vistoria_hidro      INT8                  NOT NULL,
    cd_requerimento_projeto_hidro       INT8                  NULL,
    cd_req_vigilancia                   INT8                  NOT NULL,
    cd_tipo_enquadramento_projeto       INT8                  NOT NULL,
    nr_projeto_aprovado                 VARCHAR(20)           NULL,
    obra_endereco                       VARCHAR(100)          NOT NULL,
    obra_nr_endereco                    VARCHAR(5)            NOT NULL,
    obra_quadra                         VARCHAR(10)           NULL,
    obra_nr_lado                        VARCHAR(5)            NULL,
    obra_lote                           VARCHAR(20)           NULL,
    obra_nr_inscricao_imob              VARCHAR(10)           NOT NULL,
    obra_complemento                    VARCHAR(20)           NULL,
    obra_nr_loteamento                  VARCHAR(10)           NULL,
    nr_processo_projeto                 VARCHAR(10)           NOT NULL,
    regiao_coberta_rede_esgoto          INT2                  NOT NULL,
    regiao_abastecida_agua              INT2                  NOT NULL,
    sistema_agua_pluvial                INT2                  NOT NULL,
    uso_edificacao                      INT2                  NULL,
    obs_uso_edificacao                  VARCHAR(100)          NULL,
    parcelamento_solo                   INT2                  NULL,
    parcelamento_solo_nr_lotes          INT4                  NULL,
    area                                NUMERIC(12,4)         NOT NULL,
    area_comercial                      NUMERIC(12,4)         NULL,
    area_residencial                    NUMERIC(12,4)         NULL,
    area_total_construcao               NUMERIC(12,4)         NOT NULL,
    version                             INT8                  NOT NULL
);

alter table requerimento_vistoria_hidro
   add constraint PK_REQ_VIST_HIDRO primary key (cd_requerimento_vistoria_hidro);

alter table requerimento_vistoria_hidro
   add constraint FK_REQ_VIST_HIDRO_REF_REQ_PROJ_HIDRO foreign key (cd_requerimento_projeto_hidro)
      references requerimento_projeto_hidro (cd_requerimento_projeto_hidro)
      on delete restrict on update restrict;

alter table requerimento_vistoria_hidro
   add constraint FK_REQ_VIST_HIDRO_REF_REQ_VIGI foreign key (cd_req_vigilancia)
      references requerimento_vigilancia (cd_req_vigilancia)
      on delete restrict on update restrict;

alter table requerimento_vistoria_hidro
   add constraint FK_REQ_VIST_HIDRO_REF_TP_ENQ_PROJ foreign key (cd_tipo_enquadramento_projeto)
      references tipo_enquadramento_projeto (cd_tipo_enquadramento_projeto)
      on delete restrict on update restrict;

CREATE TABLE auditschema.requerimento_vistoria_hidro AS SELECT t2.*, t1.* FROM requerimento_vistoria_hidro t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requerimento_vistoria_hidro;alter table auditschema.requerimento_vistoria_hidro add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requerimento_vistoria_hidro FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE SEQUENCE seq_requerimento_vistoria_hidro start with 1;

INSERT INTO programa_pagina VALUES (1633, 'br.com.celk.view.vigilancia.requerimentos.analiseprojetos.vistoriahidrossanitario.RequerimentoVistoriaHidrossanitarioPage', 'N');
INSERT INTO programa_web_pagina VALUES (1740, 245, 1633);
INSERT INTO programa_web_pagina VALUES (1741, 684, 1633);
INSERT INTO programa_web_pagina VALUES (1742, 764, 1633);

INSERT INTO programa_pagina VALUES (1634, 'br.com.celk.view.vigilancia.externo.view.servicos.RequerimentoVistoriaHidrossanitarioExternoPage', 'N');
INSERT INTO programa_web_pagina VALUES (1743, 692, 1634);

/*
    Maicon - 03/01/19 - #23152
*/
INSERT INTO programa_pagina VALUES (1632, 'br.com.celk.view.vigilancia.requerimentos.analiseprojetos.projetohidrossanitario.RequerimentoProjetosHidrossanitarioParecerTecnicoPage', 'N');
INSERT INTO programa_web_pagina VALUES (1735, 764, 1632);
INSERT INTO programa_web_pagina VALUES (1736, 245, 1632);
INSERT INTO programa_web_pagina VALUES (1737, 684, 1632);

CREATE TABLE req_projeto_hidrossanitario_parecer (
    cd_req_pro_hidros_parecer               INT8                  NOT NULL,
    cd_requerimento_projeto_hidro           INT8                  NOT NULL,
    ds_parecer                              VARCHAR               NULL,
    tipo_aprovacao                          INT2                  NULL,
    dt_parecer                              TIMESTAMP             NOT NULL,
    status                                  INT2                  NULL,
    abastecimento_agua_somatorio            INT4                  NULL,
    ds_abastecimento_agua                   VARCHAR               NULL,
    uso_abastecimento_agua                  VARCHAR               NULL,
    tratamento_efluentes_somatorio          INT4                  NULL,
    ds_tratamento_efluentes                 VARCHAR               NULL,
    uso_tratamento_efluentes                VARCHAR               NULL,
    licenciamento_ambiental_somatorio       INT4                  NULL,
    nr_lai_lic_ambiental                    VARCHAR               NULL,
    nr_lao_lic_ambiental                    VARCHAR               NULL,
    observacao                              VARCHAR               NULL,
    cd_usuario                              NUMERIC(6)            NOT NULL,
    dt_cadastro                             TIMESTAMP             NOT NULL,
    version                                 INT8                  NOT NULL
);
alter table req_projeto_hidrossanitario_parecer
   add constraint PK_REQ_PROJ_HIDRO_PARECER primary key (cd_req_pro_hidros_parecer);

alter table req_projeto_hidrossanitario_parecer
   add constraint FK_REQ_PROJ_HIDRO_PARECER_REF_REQ_PROJ_HIDRO foreign key (cd_requerimento_projeto_hidro)
      references requerimento_projeto_hidro (cd_requerimento_projeto_hidro)
      on delete restrict on update restrict;

ALTER TABLE req_projeto_hidrossanitario_parecer
    ADD CONSTRAINT FK_REQ_PROJ_HIDRO_PARECER_REF_USUARIO FOREIGN KEY (cd_usuario)
    REFERENCES usuarios (cd_usuario)
    ON DELETE restrict
    ON UPDATE restrict;

CREATE TABLE auditschema.req_projeto_hidrossanitario_parecer AS SELECT t2.*, t1.* FROM req_projeto_hidrossanitario_parecer t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_req_projeto_hidrossanitario_parecer;alter table auditschema.req_projeto_hidrossanitario_parecer add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON req_projeto_hidrossanitario_parecer FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE SEQUENCE seq_req_projeto_hidrossanitario_parecer start with 1;

CREATE TABLE req_pro_hidros_parecer_fiscal (
    cd_req_pro_hidros_parecer_fiscal       INT8         NOT NULL,
    cd_req_pro_hidros_parecer              INT8  	    NOT NULL,
    cd_req_vig_fiscal   	               INT8         NOT NULL,
    version                                INT8         NOT NULL
);
ALTER TABLE req_pro_hidros_parecer_fiscal    ADD CONSTRAINT pk_req_hidros_par_fisc PRIMARY KEY (cd_req_pro_hidros_parecer_fiscal);
ALTER TABLE req_pro_hidros_parecer_fiscal    ADD CONSTRAINT fk_parec_fisc_ref_hidros FOREIGN KEY (cd_req_pro_hidros_parecer)    REFERENCES req_projeto_hidrossanitario_parecer (cd_req_pro_hidros_parecer)    ON DELETE restrict    ON UPDATE restrict;
ALTER TABLE req_pro_hidros_parecer_fiscal    ADD CONSTRAINT fk_req_hidros_par_fis_ref_fiscal FOREIGN KEY (cd_req_vig_fiscal)    REFERENCES requerimento_vigilancia_fiscal (cd_req_vig_fiscal)    ON DELETE restrict    ON UPDATE restrict;

CREATE TABLE auditschema.req_pro_hidros_parecer_fiscal AS SELECT t2.*, t1.* FROM req_pro_hidros_parecer_fiscal t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_req_pro_hidros_parecer_fiscal;alter table auditschema.req_pro_hidros_parecer_fiscal add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON req_pro_hidros_parecer_fiscal FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE SEQUENCE seq_req_pro_hidros_parecer_fiscal start with 1;

CREATE TABLE req_pro_hidros_parecer_ativ (
    cd_req_pro_hidros_parecer_ativ       INT8         NOT NULL,
    cd_req_pro_hidros_parecer              INT8  	    NOT NULL,
    cd_atividades_vigilancia   	    INT8         NOT NULL,
    version                  INT8         NOT NULL
);
ALTER TABLE req_pro_hidros_parecer_ativ    ADD CONSTRAINT pk_req_hidros_par_atvd PRIMARY KEY (cd_req_pro_hidros_parecer_ativ);
ALTER TABLE req_pro_hidros_parecer_ativ    ADD CONSTRAINT fk_parec_atvd_ref_hidros FOREIGN KEY (cd_req_pro_hidros_parecer)    REFERENCES req_projeto_hidrossanitario_parecer (cd_req_pro_hidros_parecer)    ON DELETE restrict    ON UPDATE restrict;
ALTER TABLE req_pro_hidros_parecer_ativ    ADD CONSTRAINT fk_parec_ativ_req_ativ FOREIGN KEY (cd_atividades_vigilancia)    REFERENCES atividades_vigilancia (cd_atividades_vigilancia)    ON DELETE restrict    ON UPDATE restrict;

CREATE TABLE auditschema.req_pro_hidros_parecer_ativ AS SELECT t2.*, t1.* FROM req_pro_hidros_parecer_ativ t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_req_pro_hidros_parecer_ativ;alter table auditschema.req_pro_hidros_parecer_ativ add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON req_pro_hidros_parecer_ativ FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE SEQUENCE seq_req_pro_hidros_parecer_ativ start with 1;

alter table requerimento_projeto_hidro add column nr_parecer_tecnico int4 null;
alter table auditschema.requerimento_projeto_hidro add column nr_parecer_tecnico int4 null;

alter table configuracao_vigilancia add column nr_aprovacao_laudo_hidrosanit_inical INT8;
alter table auditschema.configuracao_vigilancia add column nr_aprovacao_laudo_hidrosanit_inical INT8;


/*
    Maicon - 03/01/19 - #23152
*/
INSERT INTO programa_pagina VALUES (1635, 'br.com.celk.view.vigilancia.requerimentos.analiseprojetos.projetohidrossanitario.RequerimentoVistoriaHidrossanitarioParecerTecnicoPage', 'N');
INSERT INTO programa_web_pagina VALUES (1744, 764, 1635);
INSERT INTO programa_web_pagina VALUES (1745, 245, 1635);
INSERT INTO programa_web_pagina VALUES (1746, 684, 1635);

CREATE TABLE req_vistoria_hidro_parecer (
    cd_req_vistoria_hidro_parecer           INT8        NOT NULL,
    cd_requerimento_vistoria_hidro          INT8        NOT NULL,
    ds_parecer                              VARCHAR     NULL,
    dt_parecer                              TIMESTAMP   NOT NULL,
    status                                  INT2        NULL,
    cd_usuario                              NUMERIC(6)  NOT NULL,
    dt_cadastro                             TIMESTAMP   NOT NULL,
    version                                 INT8        NOT NULL
);
alter table req_vistoria_hidro_parecer   add constraint PK_REQ_VISTORIA_HIDRO_PARECER primary key (cd_req_vistoria_hidro_parecer);
alter table req_vistoria_hidro_parecer   add constraint FK_REQ_VISTORIA_HIDRO_PARECER_REF_REQ_VIST_HIDRO foreign key (cd_requerimento_vistoria_hidro)      references requerimento_vistoria_hidro (cd_requerimento_vistoria_hidro) on delete restrict on update restrict;
ALTER TABLE req_vistoria_hidro_parecer   ADD CONSTRAINT FK_REQ_VISTORIA_HIDRO_PARECER_REF_USUARIO FOREIGN KEY (cd_usuario)    REFERENCES usuarios (cd_usuario)    ON DELETE restrict    ON UPDATE restrict;

CREATE TABLE auditschema.req_vistoria_hidro_parecer AS SELECT t2.*, t1.* FROM req_vistoria_hidro_parecer t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_req_vistoria_hidro_parecer;alter table auditschema.req_vistoria_hidro_parecer add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON req_vistoria_hidro_parecer FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE SEQUENCE seq_req_vistoria_hidro_parecer start with 1;

CREATE TABLE req_vistoria_hidro_parecer_fiscal (
    cd_req_vistoria_hidro_parecer_fiscal   INT8         NOT NULL,
    cd_req_vistoria_hidro_parecer          INT8  	    NOT NULL,
    cd_req_vig_fiscal   	               INT8         NOT NULL,
    version                                INT8         NOT NULL
);
ALTER TABLE req_vistoria_hidro_parecer_fiscal    ADD CONSTRAINT pk_req_vistoria_par_fisc PRIMARY KEY (cd_req_vistoria_hidro_parecer_fiscal);
ALTER TABLE req_vistoria_hidro_parecer_fiscal    ADD CONSTRAINT fk_parec_fisc_ref_hidros_parecer FOREIGN KEY (cd_req_vistoria_hidro_parecer)    REFERENCES req_vistoria_hidro_parecer (cd_req_vistoria_hidro_parecer)    ON DELETE restrict    ON UPDATE restrict;
ALTER TABLE req_vistoria_hidro_parecer_fiscal    ADD CONSTRAINT fk_req_vistoria_hidros_par_fis_ref_fiscal FOREIGN KEY (cd_req_vig_fiscal)    REFERENCES requerimento_vigilancia_fiscal (cd_req_vig_fiscal)    ON DELETE restrict    ON UPDATE restrict;

CREATE TABLE auditschema.req_vistoria_hidro_parecer_fiscal AS SELECT t2.*, t1.* FROM req_vistoria_hidro_parecer_fiscal t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_req_vistoria_hidro_parecer_fiscal;alter table auditschema.req_vistoria_hidro_parecer_fiscal add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON req_vistoria_hidro_parecer_fiscal FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE SEQUENCE seq_req_vistoria_hidro_parecer_fiscal start with 1;

CREATE TABLE req_vistoria_hidro_parecer_ativ (
    cd_req_vistoria_hidro_parecer_ativ      INT8       NOT NULL,
    cd_req_vistoria_hidro_parecer           INT8  	   NOT NULL,
    cd_atividades_vigilancia   	            INT8       NOT NULL,
    version                                 INT8       NOT NULL
);
ALTER TABLE req_vistoria_hidro_parecer_ativ    ADD CONSTRAINT pk_req_vistoria_hidros_par_atvd PRIMARY KEY (cd_req_vistoria_hidro_parecer_ativ);
ALTER TABLE req_vistoria_hidro_parecer_ativ    ADD CONSTRAINT fk_vistoria_parec_atvd_ref_hidros_parecer FOREIGN KEY (cd_req_vistoria_hidro_parecer)    REFERENCES req_vistoria_hidro_parecer (cd_req_vistoria_hidro_parecer)    ON DELETE restrict    ON UPDATE restrict;
ALTER TABLE req_vistoria_hidro_parecer_ativ    ADD CONSTRAINT fk_vistoria_parec_ativ_req_ativ FOREIGN KEY (cd_atividades_vigilancia)    REFERENCES atividades_vigilancia (cd_atividades_vigilancia)    ON DELETE restrict    ON UPDATE restrict;

CREATE TABLE auditschema.req_vistoria_hidro_parecer_ativ AS SELECT t2.*, t1.* FROM req_vistoria_hidro_parecer_ativ t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_req_vistoria_hidro_parecer_ativ;alter table auditschema.req_vistoria_hidro_parecer_ativ add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON req_vistoria_hidro_parecer_ativ FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE SEQUENCE seq_req_vistoria_hidro_parecer_ativ start with 1;

alter table requerimento_vistoria_hidro add column nr_parecer_tecnico int4 null;
alter table auditschema.requerimento_vistoria_hidro add column nr_parecer_tecnico int4 null;

alter table configuracao_vigilancia add column habitese_importante varchar;
alter table auditschema.configuracao_vigilancia add column habitese_importante varchar;

alter table configuracao_vigilancia add column habitese_para varchar;
alter table auditschema.configuracao_vigilancia add column habitese_para varchar;
/*
    Maicon - 18/01/2017 - #14235
*/

CREATE TABLE requerimento_treinamento(
  cd_requerimento_treinamento       int8 NOT NULL,
  cd_req_vigilancia                 int8 NOT NULL,
  cd_vigilancia_endereco            int8 NULL,
  cd_resp_tecnico                   int8 NULL,
  ds_resp_tecnico                   varchar(200)  NULL,
  hr_ini_prim_turno                 timestamp null,
  hr_fim_prim_turno                 timestamp null,
  hr_ini_seg_turno                  timestamp null,
  hr_fim_seg_turno                  timestamp null,
  material_didatico                 int4 null,
  material_didatico_outros          varchar null,
  dt_validade                       timestamp null,
  nr_credenciamento                 int8 null,
  credenciamento_para               varchar null,
  version                           int8 NOT NULL,
  CONSTRAINT pk_requerimento_treinamento PRIMARY KEY (cd_requerimento_treinamento),
  CONSTRAINT fk_req_treinamento_ref_req_vigi FOREIGN KEY (cd_req_vigilancia)      REFERENCES requerimento_vigilancia (cd_req_vigilancia) ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_req_treinamento_ref_vig_end FOREIGN KEY (cd_vigilancia_endereco)  REFERENCES vigilancia_endereco (cd_vigilancia_endereco) ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_req_treinamento_ref_resp_tec FOREIGN KEY (cd_resp_tecnico)      REFERENCES responsavel_tecnico (cd_resp_tecnico) ON UPDATE RESTRICT ON DELETE RESTRICT
);
CREATE TABLE auditschema.requerimento_treinamento AS SELECT t2.*, t1.* FROM requerimento_treinamento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requerimento_treinamento;alter table auditschema.requerimento_treinamento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requerimento_treinamento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE SEQUENCE seq_requerimento_treinamento start with 1;

CREATE TABLE requerimento_treinamento_ministrantes(
  cd_requerimento_treinamento_ministrantes  int8 NOT NULL,
  cd_requerimento_treinamento               int8 NOT NULL,
  nome                                      varchar NOT NULL,
  cpf                                       varchar(15) NULL,
  formacao                                  varchar NULL,
  version                                   int8 NOT NULL,
  CONSTRAINT pk_requerimento_treinamento_datas PRIMARY KEY (cd_requerimento_treinamento_ministrantes),
  CONSTRAINT fk_req_trei_minist_ref_req_treinamento FOREIGN KEY (cd_requerimento_treinamento)      REFERENCES requerimento_treinamento (cd_requerimento_treinamento) ON UPDATE RESTRICT ON DELETE RESTRICT
);
CREATE TABLE auditschema.requerimento_treinamento_ministrantes AS SELECT t2.*, t1.* FROM requerimento_treinamento_ministrantes t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requerimento_treinamento_ministrantes;alter table auditschema.requerimento_treinamento_ministrantes add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requerimento_treinamento_ministrantes FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();


INSERT INTO programa_pagina VALUES (1678, 'br.com.celk.view.vigilancia.requerimentos.RequerimentoTreinamentoPage', 'N');
INSERT INTO programa_web_pagina VALUES (1714, 684, 1678);
INSERT INTO programa_web_pagina VALUES (1715, 245, 1678);
INSERT INTO programa_web_pagina VALUES (1754, 764, 1678);

INSERT INTO programa_pagina VALUES (1620, 'br.com.celk.view.vigilancia.externo.view.servicos.RequerimentoTreinamentoExternoPage', 'N');
INSERT INTO programa_web_pagina VALUES (1716, 692, 1620);

INSERT INTO permissao_web (cd_permissao, ds_permissao, version) VALUES (129, 'Credenciamento para Treinamento', 0);
INSERT INTO programa_pagina_permissao VALUES(450, 129, 1210, 0, 'requerimentoCredenciamentoAlimento');
INSERT INTO programa_pagina_permissao VALUES(453, 129, 1248, 0, 'requerimentoCredenciamentoAlimento');

INSERT INTO tipo_solicitacao (cd_tipo_solicitacao,ds_tipo_solicitacao,tipo_documento,tipo_acao,version,cd_templ_doc_vig,lei,registrar_palestra,valor_ufm,tp_requerimento,ativo,flag_inspeciona) VALUES (nextval('seq_gem'),'Credenciamento para Treinamento',null,null,0,null,null,null,null,18,1,0);

ALTER TABLE configuracao_vigilancia ADD COLUMN orientacao_treinamento VARCHAR NULL;
ALTER TABLE configuracao_vigilancia ADD COLUMN obs_destaque_treinamento VARCHAR NULL;
ALTER TABLE configuracao_vigilancia ADD COLUMN validade_treinamento_data_fixa  int2 null;
ALTER TABLE configuracao_vigilancia ADD COLUMN validade_treinamento_data_vencimento timestamp null;
ALTER TABLE configuracao_vigilancia ADD COLUMN iniciar_seq_treinamento int4 null;
ALTER TABLE configuracao_vigilancia ADD COLUMN marca_dagua_treinamento int2 null;
ALTER TABLE configuracao_vigilancia ADD COLUMN treinamento_para VARCHAR NULL;
ALTER TABLE configuracao_vigilancia ADD COLUMN validade_treinamento_periodo  int2 null;
ALTER TABLE configuracao_vigilancia ADD COLUMN qtd_periodo_validade_treinamento int4 null;

ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN orientacao_treinamento VARCHAR NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN obs_destaque_treinamento VARCHAR NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN validade_treinamento_data_fixa  int2 null;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN validade_treinamento_data_vencimento timestamp null;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN iniciar_seq_treinamento int4 null;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN marca_dagua_treinamento int2 null;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN treinamento_para VARCHAR NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN validade_treinamento_periodo  int2 null;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN qtd_periodo_validade_treinamento  int4 null;

alter table configuracao_vigilancia add column tp_database_calc_credenciamento int2;
alter table auditschema.configuracao_vigilancia add column tp_database_calc_credenciamento int2;
UPDATE configuracao_vigilancia SET tp_database_calc_credenciamento = 0;

update configuracao_vigilancia set validade_treinamento_data_fixa = 0;
update configuracao_vigilancia set validade_treinamento_periodo = 12;
update configuracao_vigilancia set qtd_periodo_validade_treinamento = 2;
update configuracao_vigilancia set tp_database_calc_credenciamento = 1;

alter table estabelecimento add column dt_validade_pri_credenciamento date;
alter table auditschema.estabelecimento add column dt_validade_pri_credenciamento date;

alter table requerimento_treinamento drop column credenciamento_para;
alter table auditschema.requerimento_treinamento drop column credenciamento_para;

alter table requerimento_treinamento add column obs_destaque_credenciamento varchar;
alter table auditschema.requerimento_treinamento add column obs_destaque_credenciamento varchar;

alter table configuracao_vigilancia rename treinamento_para to credenciamento_para;
alter table auditschema.configuracao_vigilancia rename treinamento_para to credenciamento_para;

/*
    Elton - 21/12/18 - #23077
*/
alter table estabelecimento add column tp_grupo_drogaria_somatorio int4;
alter table auditschema.estabelecimento add column tp_grupo_drogaria_somatorio int4;

alter table estabelecimento add column tp_grupo_farmacia_somatorio int4;
alter table auditschema.estabelecimento add column tp_grupo_farmacia_somatorio int4;

alter table estabelecimento add column tp_grupo_farm_grupoIII_somat int4;
alter table auditschema.estabelecimento add column tp_grupo_farm_grupoIII_somat int4;

alter table estabelecimento add flag_drogaria int2 null;
alter table auditschema.estabelecimento add flag_drogaria int2 null;

alter table estabelecimento add flag_farmacia int2 null;
alter table auditschema.estabelecimento add flag_farmacia int2 null;

/*
    Mauricley - 21/12/18 - #23147
*/

alter table atividades_vigilancia add column pontuacao INT2;
alter table auditschema.atividades_vigilancia add column pontuacao INT2;

alter table lancamento_atividades_vigilancia_item add column pontuacao INT2;
alter table auditschema.lancamento_atividades_vigilancia_item add column pontuacao INT2;

/*
    Maicon - 27/12/2018 - #23044
*/

CREATE TABLE requerimento_restituicao_taxa (
  cd_requerimento_restituicao_taxa  int8 NOT NULL,
  cd_req_vigilancia                 int8 NOT NULL,
  nr_processo                       varchar null,
  motivo_restituicao                varchar not null,
  version                           int8 NOT NULL,
  CONSTRAINT pk_requerimento_restituicao_taxa PRIMARY KEY (cd_requerimento_restituicao_taxa),
  CONSTRAINT fk_req_rest_taxa_ref_req_vigi FOREIGN KEY (cd_req_vigilancia)      REFERENCES requerimento_vigilancia (cd_req_vigilancia) ON UPDATE RESTRICT ON DELETE RESTRICT
);
CREATE TABLE auditschema.requerimento_restituicao_taxa AS SELECT t2.*, t1.* FROM requerimento_restituicao_taxa t1, audit_temp t2 WHERE 1=2;
create sequence seq_audit_id_requerimento_restituicao_taxa;
alter table auditschema.requerimento_restituicao_taxa add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requerimento_restituicao_taxa FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE SEQUENCE seq_requerimento_restituicao_taxa start with 1;

INSERT INTO programa_pagina VALUES (1628, 'br.com.celk.view.vigilancia.requerimentos.RequerimentoRestituicaoTaxaPage', 'N');
INSERT INTO programa_web_pagina VALUES (1728, 684, 1628);
INSERT INTO programa_web_pagina VALUES (1730, 245, 1628);
INSERT INTO programa_web_pagina VALUES (1755, 764, 1628);

INSERT INTO programa_pagina VALUES (1629, 'br.com.celk.view.vigilancia.externo.view.servicos.RequerimentoRestituicaoTaxaExternoPage', 'N');
INSERT INTO programa_web_pagina VALUES (1729, 692, 1629);

INSERT INTO permissao_web (cd_permissao, ds_permissao, version) VALUES (136, 'Restituicao de Taxa', 0);
INSERT INTO programa_pagina_permissao VALUES(472, 136, 1210, 0, 'requerimentoRestituicaoTaxa');
INSERT INTO programa_pagina_permissao VALUES(473, 136, 1248, 0, 'requerimentoRestituicaoTaxa');

ALTER TABLE processo_adm ADD COLUMN cd_requerimento_restituicao_taxa int8 null;
ALTER TABLE auditschema.processo_adm ADD COLUMN cd_requerimento_restituicao_taxa int8 null;
ALTER TABLE processo_adm
   add constraint FK_PROC_ADM_REF_REQ_REST_TAXA foreign key (cd_requerimento_restituicao_taxa)
      references requerimento_restituicao_taxa (cd_requerimento_restituicao_taxa)
      on delete restrict on update restrict;

INSERT INTO tipo_solicitacao (cd_tipo_solicitacao,ds_tipo_solicitacao,version,tp_requerimento,ativo, flag_inspeciona)
     VALUES (nextval('seq_gem'),'Restituição de Taxa', 0 , 19, 1, 0);

/*
    Elton - 17/12/18 - #23073
*/
alter table configuracao_vigilancia add column tp_database_calc_alvara int2;
alter table auditschema.configuracao_vigilancia add column tp_database_calc_alvara int2;
UPDATE configuracao_vigilancia SET tp_database_calc_alvara = 0;

alter table configuracao_vigilancia add column tp_database_calc_lic_transp int2;
alter table auditschema.configuracao_vigilancia add column tp_database_calc_lic_transp int2;
UPDATE configuracao_vigilancia SET tp_database_calc_lic_transp = 0;

alter table estabelecimento add column dt_validade_pri_alvara date;
alter table auditschema.estabelecimento add column dt_validade_pri_alvara date;

alter table estabelecimento add column dt_validade_pri_licenca date;
alter table auditschema.estabelecimento add column dt_validade_pri_licenca date;


/*
    Sulivan - 18/12/2018 - #23116
*/
alter table configuracao_vigilancia add column msg_denuncia_anonima varchar(300);
alter table auditschema.configuracao_vigilancia add column msg_denuncia_anonima varchar(300);

update configuracao_vigilancia set msg_denuncia_anonima = 'Se o denunciante for sigiloso, a denúncia não terá prioridade de atendimento e não serão enviadas informações acerca do atendimento realizado. Porém caso este houver sido identificado, será mantido sigilo e receberá informações do andamento do processo através do e-mail informado.';

alter table denuncia add column flag_anonimo INT2;
alter table denuncia add column tp_pessoa_denunciante char(1);
alter table denuncia add column cnpj_cpf_denunciante varchar(15);
alter table denuncia add column email_denunciante varchar(100);
ALTER TABLE denuncia ADD COLUMN cd_req_vigilancia INT8 NULL;

alter table auditschema.denuncia add column flag_anonimo INT2;
alter table auditschema.denuncia add column tp_pessoa_denunciante char(1);
alter table auditschema.denuncia add column cnpj_cpf_denunciante varchar(15);
alter table auditschema.denuncia add column email_denunciante varchar(100);
ALTER TABLE auditschema.denuncia ADD COLUMN cd_req_vigilancia INT8 NULL;

alter table denuncia alter column observacao type text;
alter table auditschema.denuncia alter column observacao type text;

ALTER TABLE denuncia
        add constraint FK_DENUNCIA_REF_REQ_VIGILANCIA foreign key (cd_req_vigilancia)
        references requerimento_vigilancia (cd_req_vigilancia)
        on delete restrict on update restrict;

/*
    Laudecir - 19/12/2018 - #23116
*/
INSERT INTO programa_pagina VALUES (1623, 'br.com.celk.view.vigilancia.requerimentos.RequerimentoDenunciaReclamacaoPage', 'N');
INSERT INTO programa_web_pagina VALUES (1719, 684, 1623);
INSERT INTO programa_web_pagina VALUES (1720, 245, 1623);
INSERT INTO programa_web_pagina VALUES (1738, 692, 1623);
INSERT INTO programa_web_pagina VALUES (1739, 764, 1623);

INSERT INTO permissao_web (cd_permissao, ds_permissao, version) VALUES (130, 'Denúncia/Reclamação', 0);

INSERT INTO programa_pagina_permissao VALUES(455, 130, 1210, 0, 'denunciaReclamacao');

INSERT INTO tipo_solicitacao (cd_tipo_solicitacao,ds_tipo_solicitacao,version,tp_requerimento,ativo, flag_inspeciona)
     VALUES (nextval('seq_gem'),'Denúncia/Reclamação',0,20,1, 0);

alter table denuncia add column logradouro_denunciado varchar(50);
alter table denuncia add column logradouro_denunciante varchar(50);

alter table auditschema.denuncia add column logradouro_denunciado varchar(50);
alter table auditschema.denuncia add column logradouro_denunciante varchar(50);

alter table denuncia alter column empresa drop not null;
alter table denuncia alter column cd_vigilancia_endereco drop not null;
alter table denuncia alter column cd_tipo_denuncia drop not null;

alter table auditschema.denuncia alter column empresa drop not null;
alter table auditschema.denuncia alter column cd_vigilancia_endereco drop not null;
alter table auditschema.denuncia alter column cd_tipo_denuncia drop not null;

alter table denuncia add column cd_vigilancia_pessoa_denunciada int8;
alter table auditschema.denuncia add column cd_vigilancia_pessoa_denunciada int8;

ALTER TABLE denuncia
        ADD CONSTRAINT FK_DENUNCIA_REF_VIG_PESSOA_DENUNCIADA
        FOREIGN KEY (cd_vigilancia_pessoa_denunciada)
        REFERENCES vigilancia_pessoa (cd_vigilancia_pessoa) on delete restrict on update restrict;

alter table requerimento_vigilancia add column cnpj_solicitante varchar(14);
alter table auditschema.requerimento_vigilancia add column cnpj_solicitante varchar(14);

alter table requerimento_vigilancia alter column nm_solicitante drop not null;
alter table auditschema.requerimento_vigilancia alter column nm_solicitante drop not null;

alter table requerimento_vigilancia alter column rg_cpf_solicitante drop not null;
alter table auditschema.requerimento_vigilancia alter column rg_cpf_solicitante drop not null;

alter table tipo_denuncia add column cd_setor_vigilancia int8;
alter table auditschema.tipo_denuncia add column cd_setor_vigilancia int8;

alter table tipo_denuncia
        add constraint FK_TIPO_DENUNCIA_REF_SETOR_VIGILANCIA foreign key (cd_setor_vigilancia)
        references setor_vigilancia (cd_setor_vigilancia)
        on delete restrict on update restrict;

alter table auto_multa add column cd_denuncia int8;
alter table auditschema.auto_multa add column cd_denuncia int8;

alter table auto_multa
        add constraint FK_AUTO_MULTA_REF_DENUNCIA foreign key (cd_denuncia)
        references denuncia (cd_denuncia)
        on delete restrict on update restrict;

-- Deleta o menu do programa Registro de Denúncias/Reclamações
DELETE FROM controle_programa_web WHERE cd_prg_web = 587;
DELETE FROM controle_programa_web_grupo WHERE cd_prg_web = 587;
DELETE FROM painel_controle_programa_web WHERE cd_prg_web = 587;
DELETE FROM programa_favorito WHERE cd_prg_web = 587;
DELETE FROM programa_web_pagina WHERE cd_prg_web = 587;
DELETE FROM menu_web WHERE cd_menu = 798;
DELETE FROM programa_web_pagina WHERE cd_prg_web_pagina in(1043,1044,1049,1050,1502);
DELETE FROM programa_web WHERE cd_prg_web = 587;
DELETE FROM programa_pagina_permissao WHERE cd_prg_pagina in (1035,1036,1041,1042,1425);
DELETE FROM programa_pagina WHERE cd_prg_pagina in (1035,1036,1041,1042,1425);

-- Seta para Pendente as denúncias que estão em Monitoramento
update denuncia set status = 1 where cd_req_vigilancia is null and status = 0;

/*
    Sulivan - 08/01/19 - #23151
*/
INSERT INTO programa_pagina VALUES (1637, 'br.com.celk.view.vigilancia.requerimentos.analiseprojetos.RequerimentoVistoriaProjetoBasicoArquiteturaPage', 'N');
INSERT INTO programa_web_pagina VALUES (1748, 245, 1637);
INSERT INTO programa_web_pagina VALUES (1749, 684, 1637);
INSERT INTO programa_web_pagina VALUES (1750, 764, 1637);

INSERT INTO programa_pagina VALUES (1638, 'br.com.celk.view.vigilancia.requerimentos.analiseprojetos.RequerimentoAnaliseProjetosConformidadeTecnicaVistoriaPage', 'N');
INSERT INTO programa_web_pagina VALUES (1751, 245, 1638);
INSERT INTO programa_web_pagina VALUES (1752, 684, 1638);
INSERT INTO programa_web_pagina VALUES (1753, 764, 1638);

INSERT INTO permissao_web values (139, 'Informar Fiscais', 0);
INSERT INTO programa_pagina_permissao VALUES(477, 139, 403, 0, 'informarFiscais');

/*
    Maicon - 07/01/19 - #23058
*/
alter table configuracao_vigilancia alter column ds_enquad_legal_multa type varchar;
alter table configuracao_vigilancia alter column ciencia_auto_multa type varchar;
alter table configuracao_vigilancia alter column ds_auto_multa type varchar;
alter table auditschema.configuracao_vigilancia alter column ds_enquad_legal_multa type varchar;
alter table auditschema.configuracao_vigilancia alter column ciencia_auto_multa type varchar;
alter table auditschema.configuracao_vigilancia alter column ds_auto_multa type varchar;

/*
    Elton = 08/01/19 - #23346
*/
ALTER TABLE configuracao_vigilancia ADD COLUMN cd_setor_vigi_geral_denuncia BIGINT NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN cd_setor_vigi_geral_denuncia BIGINT NULL;

alter table configuracao_vigilancia
   add constraint FK_CONFIG_VIG_REF_GERAL_DENUNCIA foreign key (cd_setor_vigi_geral_denuncia)
      references setor_vigilancia (cd_setor_vigilancia)
      on delete restrict on update restrict;

/*
    Laudecir - 08/01/19 - #23116
*/

alter table denuncia alter column denunciado type varchar;
alter table auditschema.denuncia alter column denunciado type varchar;

alter table denuncia alter column denunciante type varchar;
alter table auditschema.denuncia alter column denunciante type varchar;

/*
    Elton - 09/01/2019
*/
alter table atividades_vigilancia alter column descricao type varchar(300);
alter table auditschema.atividades_vigilancia alter column descricao type varchar(300);

/*
    Leonardo - 03/01/19 - #23290
*/

-- TODO ####### DEIXAR ESSE SCRIPT POR ULTIMO. DEVIDO A CRIAÇÃO DE INSERT NA TABELA tipo_solicitacao SEM OS CAMPOS NOT NULL NOVOS ########

ALTER TABLE tipo_solicitacao ADD COLUMN flag_gera_intimacao INT2 NULL;
ALTER TABLE auditschema.tipo_solicitacao ADD COLUMN flag_gera_intimacao INT2 NULL;
update tipo_solicitacao set flag_gera_intimacao = 0;
ALTER TABLE tipo_solicitacao ALTER COLUMN flag_gera_intimacao SET NOT NULL;

ALTER TABLE tipo_solicitacao ADD COLUMN flag_gera_infracao INT2 NULL;
ALTER TABLE auditschema.tipo_solicitacao ADD COLUMN flag_gera_infracao INT2 NULL;
update tipo_solicitacao set flag_gera_infracao = 0;
ALTER TABLE tipo_solicitacao ALTER COLUMN flag_gera_infracao SET NOT NULL;

ALTER TABLE tipo_solicitacao ADD COLUMN flag_gera_multa INT2 NULL;
ALTER TABLE auditschema.tipo_solicitacao ADD COLUMN flag_gera_multa INT2 NULL;
update tipo_solicitacao set flag_gera_multa = 0;
ALTER TABLE tipo_solicitacao ALTER COLUMN flag_gera_multa SET NOT NULL;

INSERT INTO permissao_web values (137, 'Auto de Intimação', 0);
INSERT INTO permissao_web values (138, 'Auto de Infração', 0);
INSERT INTO programa_pagina_permissao VALUES(474, 137, 1403, 0, '');
INSERT INTO programa_pagina_permissao VALUES(475, 138, 1403, 0, '');

-- INICIO IMPLEMENTAÇÃO DOS AJUSTES EM JANEIRO/FEVEREIRO/MARÇO

/*
    Laudecir - 19/11/2018 - #22549
*/
INSERT INTO AGENDADOR_PROCESSO (cd_agendador_processo,cd_processo,nm_servico,ds_servico,status,version,tp_processo)
     VALUES (54,54,'Consultar Retorno das Remessas dos Boletos','Consulta na API se há retorno de remessas para datas anteriores.',0,0,12);

/*
    Leonardo - 10/01/2019 - #23356
*/
ALTER TABLE configuracao_vigilancia DROP CONSTRAINT IF EXISTS fk_config_vig_ref_taxa_veiculo_nao_refrigerado;
ALTER TABLE configuracao_vigilancia DROP CONSTRAINT IF EXISTS fk_config_vig_ref_taxa;
ALTER TABLE configuracao_vigilancia DROP CONSTRAINT IF EXISTS fk_config_vig_ref_taxa_insp_natu_simples;
ALTER TABLE configuracao_vigilancia DROP CONSTRAINT IF EXISTS fk_config_vig_ref_taxa_alvara_evento;
ALTER TABLE configuracao_vigilancia DROP CONSTRAINT IF EXISTS fk_config_vig_ref_taxa_livro;
ALTER TABLE configuracao_vigilancia DROP CONSTRAINT IF EXISTS fk_config_vig_ref_taxa_insp_natu_complexa;
ALTER TABLE configuracao_vigilancia DROP CONSTRAINT IF EXISTS fk_config_vig_vacin_extramuro_ref_taxa;
ALTER TABLE configuracao_vigilancia DROP CONSTRAINT IF EXISTS fk_config_vig_ref_taxa_veiculo_refrigerado;
ALTER TABLE configuracao_vigilancia DROP CONSTRAINT IF EXISTS fk_config_vig_tax_unic_lic_transp_ref_taxa;
ALTER TABLE configuracao_vigilancia DROP CONSTRAINT IF EXISTS fk_config_vig_ref_taxa_baixa_rt;

CREATE TABLE configuracao_vigilancia_financeiro (
   cd_config_vigilancia_financeiro INT8 PRIMARY KEY NOT NULL,
   forma_cobranca INT2,
   anexar_comprovante INT2,
   iniciar_seq_memorando INT4,
   ano_base_seq_memorando INT4,
   token_conta_banc_homolog VARCHAR(64),
   token_conta_banc_prod VARCHAR(64),
   boleto_instrucao_1 VARCHAR(250),
   boleto_instrucao_2 VARCHAR(250),
   boleto_instrucao_3 VARCHAR(250),
   boleto_instrucao_4 VARCHAR(250),
   boleto_instrucao_5 VARCHAR(250),
   boleto_instrucao_6 VARCHAR(250),
   boleto_instrucao_7 VARCHAR(250),
   boleto_instrucao_8 VARCHAR(250),
   cd_taxa_receita INT8,
   qtd_taxa_receita NUMERIC(12,4),
   tp_calculo_taxa_receita INT2,
   nro_folhas_talao INT8,
   cd_taxa_livro INT8,
   qtd_taxa_livro NUMERIC(12,4),
   cd_taxa_baixa_rt INT8,
   qtd_taxa_baixa_rt NUMERIC(12,4),
   tp_calculo_taxa_licenca_transporte INT2,
   cd_taxa_unica_licenca_transporte INT8,
   qtd_taxa_unica_licenca_transporte NUMERIC(12,4),
   cd_taxa_veiculo_refrigerado INT8,
   qtd_taxa_veiculo_refrigerado NUMERIC(12,4),
   cd_taxa_veiculo_nao_refrigerado INT8,
   qtd_taxa_veiculo_nao_refrigerado NUMERIC(12,4),
   cd_taxa_inspecao_natureza_simples INT8,
   qtd_taxa_inspecao_natureza_simples NUMERIC(12,4),
   cd_taxa_inspecao_natureza_complexa INT8,
   qtd_taxa_inspecao_natureza_complexa NUMERIC(12,4),
   cd_taxa_vacinacao_extramuro INT8,
   qtd_taxa_vacinacao_extramuro NUMERIC(12,4),
   cd_taxa_alvara_evento INT8,
   qtd_taxa_alvara_evento NUMERIC(12,4),

   flag_taxa_unica_inspecao INT2,
   cd_taxa_unica_inspecao INT8,
   qtd_taxa_unica_inspecao NUMERIC(12,4),

   cd_taxa_inclusao_rt INT8,
   qtd_taxa_inclusao_rt NUMERIC(12,4),

   cd_taxa_inspecao_rotina INT8,
   qtd_taxa_inspecao_rotina NUMERIC(12,4),

   cd_taxa_autorizacao_sanitaria INT8,
   qtd_taxa_autorizacao_sanitaria NUMERIC(12,4),

   cd_taxa_cred_treinamento INT8,
   qtd_taxa_cred_treinamento NUMERIC(12,4),

   cd_taxa_baixa_estabelecimento INT8,
   qtd_taxa_baixa_estabelecimento NUMERIC(12,4),

   cd_usuario_alteracao NUMERIC(6),
   dt_alteracao TIMESTAMP,
   version INT8 NOT NULL
);

CREATE UNIQUE INDEX pk_configuracao_vigilancia_financeiro ON configuracao_vigilancia_financeiro(cd_config_vigilancia_financeiro);

ALTER TABLE configuracao_vigilancia_financeiro
    ADD CONSTRAINT fk_config_vig_finan_ref_taxa_veiculo_nao_refrigerado
    FOREIGN KEY (cd_taxa_veiculo_nao_refrigerado)
    REFERENCES taxa(cd_taxa)
    ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE configuracao_vigilancia_financeiro
    ADD CONSTRAINT fk_config_vig_finan_ref_taxa
    FOREIGN KEY (cd_taxa_receita)
    REFERENCES taxa(cd_taxa)
    ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE configuracao_vigilancia_financeiro
    ADD CONSTRAINT fk_config_vig_finan_ref_taxa_insp_natu_simples
    FOREIGN KEY (cd_taxa_inspecao_natureza_simples)
    REFERENCES taxa(cd_taxa)
    ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE configuracao_vigilancia_financeiro
    ADD CONSTRAINT fk_config_vig_finan_ref_taxa_alvara_evento
    FOREIGN KEY (cd_taxa_alvara_evento)
    REFERENCES taxa(cd_taxa)
    ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE configuracao_vigilancia_financeiro
    ADD CONSTRAINT fk_config_vig_finan_ref_taxa_livro
    FOREIGN KEY (cd_taxa_livro)
    REFERENCES taxa(cd_taxa)
    ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE configuracao_vigilancia_financeiro
    ADD CONSTRAINT fk_config_vig_finan_ref_taxa_insp_natu_complexa
    FOREIGN KEY (cd_taxa_inspecao_natureza_complexa)
    REFERENCES taxa(cd_taxa)
    ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE configuracao_vigilancia_financeiro
    ADD CONSTRAINT fk_config_vig_finan_vacin_extramuro_ref_taxa
    FOREIGN KEY (cd_taxa_vacinacao_extramuro)
    REFERENCES taxa(cd_taxa)
    ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE configuracao_vigilancia_financeiro
    ADD CONSTRAINT fk_config_vig_finan_ref_taxa_veiculo_refrigerado
    FOREIGN KEY (cd_taxa_veiculo_refrigerado)
    REFERENCES taxa(cd_taxa)
    ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE configuracao_vigilancia_financeiro
    ADD CONSTRAINT fk_config_vig_finan_tax_unic_lic_transp_ref_taxa
    FOREIGN KEY (cd_taxa_unica_licenca_transporte)
    REFERENCES taxa(cd_taxa)
    ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE configuracao_vigilancia_financeiro
    ADD CONSTRAINT fk_config_vig_finan_ref_taxa_baixa_rt
    FOREIGN KEY (cd_taxa_baixa_rt)
    REFERENCES taxa(cd_taxa)
    ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE configuracao_vigilancia_financeiro
    ADD CONSTRAINT fk_config_vig_finan_ref_taxa_unica_inspecao
    FOREIGN KEY (cd_taxa_unica_inspecao)
    REFERENCES taxa(cd_taxa)
    ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE configuracao_vigilancia_financeiro
    ADD CONSTRAINT fk_config_vig_finan_ref_taxa_inclusao_rt
    FOREIGN KEY (cd_taxa_inclusao_rt)
    REFERENCES taxa(cd_taxa)
    ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE configuracao_vigilancia_financeiro
    ADD CONSTRAINT fk_config_vig_finan_ref_taxa_insp_rotina
    FOREIGN KEY (cd_taxa_inspecao_rotina)
    REFERENCES taxa(cd_taxa)
    ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE configuracao_vigilancia_financeiro
    ADD CONSTRAINT fk_config_vig_finan_ref_taxa_aut_san
    FOREIGN KEY (cd_taxa_autorizacao_sanitaria)
    REFERENCES taxa(cd_taxa)
    ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE configuracao_vigilancia_financeiro
    ADD CONSTRAINT fk_config_vig_finan_ref_taxa_cred_trein
    FOREIGN KEY (cd_taxa_cred_treinamento)
    REFERENCES taxa(cd_taxa)
    ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE configuracao_vigilancia_financeiro
    ADD CONSTRAINT fk_config_vig_finan_ref_taxa_baixa_estab
    FOREIGN KEY (cd_taxa_baixa_estabelecimento)
    REFERENCES taxa(cd_taxa)
    ON DELETE RESTRICT ON UPDATE RESTRICT;

CREATE TABLE AUDITSCHEMA.configuracao_vigilancia_financeiro AS SELECT T2.*, T1.* FROM configuracao_vigilancia_financeiro T1, AUDIT_TEMP T2 WHERE 1=2;
CREATE SEQUENCE SEQ_AUDIT_ID_configuracao_vigilancia_financeiro;
ALTER TABLE AUDITSCHEMA.configuracao_vigilancia_financeiro ADD PRIMARY KEY (AUDIT_ID);
CREATE TRIGGER EMP_AUDIT AFTER INSERT OR UPDATE OR DELETE ON configuracao_vigilancia_financeiro FOR EACH ROW EXECUTE PROCEDURE PROCESS_EMP_AUDIT();

INSERT INTO configuracao_vigilancia_financeiro
     (SELECT nextval('seq_gem'),
            forma_cobranca,
            obrigatoriedade_anexo,
            iniciar_seq_memorando,
            ano_base_seq_memorando,
            token_conta_banc_homolog,
            token_conta_banc_prod,
            boleto_instrucao_1,
            boleto_instrucao_2,
            boleto_instrucao_3,
            boleto_instrucao_4,
            boleto_instrucao_5,
            boleto_instrucao_6,
            boleto_instrucao_7,
            boleto_instrucao_8,
            cd_taxa_receita,
            qtd_taxa_receita,
            tp_calculo_taxa,
            nro_folhas_talao,
            cd_taxa_livro,
            qtd_taxa_livro,
            cd_taxa_baixa_rt,
            qtd_taxa_baixa_rt,
            flag_taxa_unica_licenca_transporte,
            cd_taxa_indice_unica_licenca_transporte,
            taxa_unica_licenca_transporte,
            cd_taxa_veiculo_refrigerado,
            qtd_taxa_veiculo_refrigerado,
            cd_taxa_veiculo_nao_refrigerado,
            qtd_taxa_veiculo_nao_refrigerado,
            cd_taxa_inspecao_natureza_simples,
            qtd_taxa_inspecao_natureza_simples,
            cd_taxa_inspecao_natureza_complexa,
            qtd_taxa_inspecao_natureza_complexa,
            cd_taxa_vacinacao_extramuro,
            qtd_taxa_vacinacao_extramuro,
            cd_taxa_alvara_evento,
            qtd_taxa_alvara_evento,
            0,
            NULL,
            NULL,
            NULL,
            NULL,
            NULL,
            NULL,
            NULL,
            NULL,
            NULL,
            NULL,
            NULL,
            NULL,
            0,
            CURRENT_TIMESTAMP,
            0
       FROM configuracao_vigilancia);


UPDATE configuracao_vigilancia SET
            forma_cobranca  = NULL,
            obrigatoriedade_anexo = NULL,
            iniciar_seq_memorando = NULL,
            ano_base_seq_memorando = NULL,
            token_conta_banc_homolog = NULL,
            token_conta_banc_prod = NULL,
            boleto_instrucao_1 = NULL,
            boleto_instrucao_2 = NULL,
            boleto_instrucao_3 = NULL,
            boleto_instrucao_4 = NULL,
            boleto_instrucao_5 = NULL,
            boleto_instrucao_6 = NULL,
            boleto_instrucao_7 = NULL,
            boleto_instrucao_8 = NULL,
            cd_taxa_receita = NULL,
            qtd_taxa_receita = NULL,
            tp_calculo_taxa = NULL,
            nro_folhas_talao = NULL,
            cd_taxa_livro = NULL,
            qtd_taxa_livro = NULL,
            cd_taxa_baixa_rt = NULL,
            qtd_taxa_baixa_rt = NULL,
            flag_taxa_unica_licenca_transporte = NULL,
            cd_taxa_indice_unica_licenca_transporte = NULL,
            taxa_unica_licenca_transporte = NULL ,
            cd_taxa_veiculo_refrigerado = NULL,
            qtd_taxa_veiculo_refrigerado = NULL,
            cd_taxa_veiculo_nao_refrigerado = NULL,
            qtd_taxa_veiculo_nao_refrigerado = NULL,
            cd_taxa_inspecao_natureza_simples = NULL,
            qtd_taxa_inspecao_natureza_simples = NULL,
            cd_taxa_inspecao_natureza_complexa = NULL ,
            qtd_taxa_inspecao_natureza_complexa = NULL,
            cd_taxa_vacinacao_extramuro = NULL,
            qtd_taxa_vacinacao_extramuro = NULL,
            cd_taxa_alvara_evento = NULL,
            qtd_taxa_alvara_evento = NULL;


/*
    Laudecir - 10/01/2019 - #23101
*/
create table integracao_boleto (
	token           varchar         not null,
	cd_integracao   int8            not null,
	cod_banco       int2            not null,
	nr_protocolo    int4            not null,
	dt_geracao      timestamp       not null,
	dt_remessa      date            not null,
	version         int8            not null
);

alter table integracao_boleto add constraint PK_INTEGRACAO_BOLETO PRIMARY KEY (cd_integracao);

CREATE TABLE AUDITSCHEMA.integracao_boleto AS SELECT T2.*, T1.* FROM integracao_boleto T1, AUDIT_TEMP T2 WHERE 1=2;
CREATE SEQUENCE SEQ_AUDIT_ID_INTEGRACAO_BOLETO;
ALTER TABLE AUDITSCHEMA.integracao_boleto ADD PRIMARY KEY (AUDIT_ID);
CREATE TRIGGER EMP_AUDIT AFTER INSERT OR UPDATE OR DELETE ON integracao_boleto FOR EACH ROW EXECUTE PROCEDURE PROCESS_EMP_AUDIT();
CREATE SEQUENCE seq_integracao_boleto start with 1;

create table boleto (
	cd_boleto	    int8            not null,
	cd_integracao   int8,
	token           varchar         not null,
	numero          varchar,
	nr_documento    varchar,
	dt_vencto       date            not null,
	situacao        int2            not null,
	valor           numeric(12,2)   not null,
	valor_pago      numeric(12,2),
	juros_mora      numeric(12,2),
	dt_pagto        date,
	dt_credito      date,
    version         int8            not null
);

alter table boleto add constraint PK_BOLETO PRIMARY KEY (cd_boleto);

ALTER TABLE boleto
    ADD CONSTRAINT FK_BOLETO_REF_INTEGRACAO_BOLETO FOREIGN KEY (cd_integracao)
    REFERENCES integracao_boleto (cd_integracao)
    ON DELETE restrict
    ON UPDATE restrict;

CREATE TABLE AUDITSCHEMA.boleto AS SELECT T2.*, T1.* FROM boleto T1, AUDIT_TEMP T2 WHERE 1=2;
CREATE SEQUENCE SEQ_AUDIT_ID_BOLETO;
ALTER TABLE AUDITSCHEMA.boleto ADD PRIMARY KEY (AUDIT_ID);
CREATE TRIGGER EMP_AUDIT AFTER INSERT OR UPDATE OR DELETE ON boleto FOR EACH ROW EXECUTE PROCEDURE PROCESS_EMP_AUDIT();
CREATE SEQUENCE seq_boleto start with 1;


create table boleto_ocorrencia (
    cd_ocorrencia   int8            not null,
    cd_boleto       int8            not null,
    data            timestamp       not null,
    descricao       varchar         not null,
    situacao        int2            null,
    motivo          varchar         null,
    cd_usuario      NUMERIC(6)      null,
    version         int8            not null
);

alter table boleto_ocorrencia add constraint PK_BOLETO_OCORRENCIA PRIMARY KEY (cd_ocorrencia);

ALTER TABLE boleto_ocorrencia
    ADD CONSTRAINT FK_BOLETO_OCORRENCIA_REF_BOLETO FOREIGN KEY (cd_boleto)
    REFERENCES boleto (cd_boleto)
    ON DELETE restrict
    ON UPDATE restrict;

ALTER TABLE boleto_ocorrencia
    ADD CONSTRAINT FK_BOLETO_OCORRENCIA_REF_USUARIO FOREIGN KEY (cd_usuario)
    REFERENCES usuarios (cd_usuario)
    ON DELETE restrict
    ON UPDATE restrict;

CREATE TABLE AUDITSCHEMA.boleto_ocorrencia AS SELECT T2.*, T1.* FROM boleto_ocorrencia T1, AUDIT_TEMP T2 WHERE 1=2;
CREATE SEQUENCE SEQ_AUDIT_ID_BOLETO_OCORRENCIA;
ALTER TABLE AUDITSCHEMA.boleto_ocorrencia ADD PRIMARY KEY (AUDIT_ID);
CREATE TRIGGER EMP_AUDIT AFTER INSERT OR UPDATE OR DELETE ON boleto_ocorrencia FOR EACH ROW EXECUTE PROCEDURE PROCESS_EMP_AUDIT();
CREATE SEQUENCE seq_boleto_ocorrencia start with 1;

alter table vigilancia_financeiro add column cd_boleto int8;
alter table auditschema.vigilancia_financeiro add column cd_boleto int8;

ALTER TABLE vigilancia_financeiro
    ADD CONSTRAINT FK_VIG_FINAN_REF_BOLETO FOREIGN KEY (cd_boleto)
    REFERENCES boleto (cd_boleto)
    ON DELETE restrict
    ON UPDATE restrict;



/*
    Leonardo - 18/02/2019 - #24024
*/

alter table configuracao_vigilancia add column flag_tipo_gestao_atividade INT2;
alter table auditschema.configuracao_vigilancia add column flag_tipo_gestao_atividade INT2;
update configuracao_vigilancia set flag_tipo_gestao_atividade = 0;
alter table configuracao_vigilancia alter column flag_tipo_gestao_atividade set not null;

alter table atividade_estabelecimento alter ds_atividade_estabelecimento type varchar(220);
alter table auditschema.atividade_estabelecimento alter ds_atividade_estabelecimento type varchar(220);

alter table atividade_estabelecimento ADD cd_cnae INT8 null;
alter table auditschema.atividade_estabelecimento ADD cd_cnae INT8 null;
alter table atividade_estabelecimento
  add constraint FK_ATIV_ESTAB_REF_CNAE foreign key (cd_cnae)
     references tabela_cnae (cd_cnae)
     on delete restrict on update restrict;

ALTER TABLE estabelecimento_setores ADD cd_atividade_estabelecimento        INT8     null;
ALTER TABLE auditschema.estabelecimento_setores ADD cd_atividade_estabelecimento      INT8     null;
alter table estabelecimento_setores
   add constraint FK_ESTAB_SETOR_REF_ATIV_ESTAB foreign key (cd_atividade_estabelecimento)
      references atividade_estabelecimento (cd_atividade_estabelecimento)
      on delete restrict on update restrict;

/*
    Leonardo - 20/02/2019 - #24091
*/
ALTER TABLE atividades_vigilancia ADD COLUMN cd_taxa INT8 NULL;
ALTER TABLE auditschema.atividades_vigilancia ADD COLUMN cd_taxa INT8 NULL;

ALTER TABLE atividades_vigilancia ADD COLUMN qtd_taxa numeric(12,4) NULL;
ALTER TABLE auditschema.atividades_vigilancia ADD COLUMN qtd_taxa numeric(12,4) NULL;

alter table atividades_vigilancia
   add constraint FK_ATVD_VIG_REF_TAXA foreign key (cd_taxa)
      references taxa (cd_taxa)
      on delete restrict on update restrict;


ALTER TABLE atividades_vigilancia ADD COLUMN flag_solic_diversas INT2 NULL;
ALTER TABLE auditschema.atividades_vigilancia ADD COLUMN flag_solic_diversas INT2 NULL;

ALTER TABLE atividades_vigilancia disable trigger user;
UPDATE atividades_vigilancia SET flag_solic_diversas = 0;



ALTER TABLE atividades_vigilancia ENABLE trigger user;
ALTER TABLE atividades_vigilancia ALTER COLUMN flag_solic_diversas set not null;

ALTER TABLE requerimento_pedido_documento ADD COLUMN cd_atividades_vigilancia INT8 NULL;
ALTER TABLE auditschema.requerimento_pedido_documento ADD COLUMN cd_atividades_vigilancia INT8 NULL;
ALTER TABLE requerimento_pedido_documento
    ADD CONSTRAINT fk_req_ped_doc_ref_ativ_vig FOREIGN KEY (cd_atividades_vigilancia)
    REFERENCES atividades_vigilancia (cd_atividades_vigilancia)
    ON DELETE restrict
    ON UPDATE restrict;

update tipo_solicitacao set ds_tipo_solicitacao = 'Pedido de Documento/Solicitações Diversas' where tp_requerimento = 13;
update permissao_web set ds_permissao = 'Pedido de Documento/Solicitações Diversas' where cd_permissao = 99;

/*
    Leonardo - 21/02/2019 - #24116
*/
alter table configuracao_vigilancia add column tp_ordenacao_requerimentos int2;
alter table auditschema.configuracao_vigilancia add column tp_ordenacao_requerimentos int2;
UPDATE configuracao_vigilancia SET tp_ordenacao_requerimentos = 0;

alter table configuracao_vigilancia add column qtd_dias_inspecao_retroativa int2;
alter table auditschema.configuracao_vigilancia add column qtd_dias_inspecao_retroativa int2;
UPDATE configuracao_vigilancia SET qtd_dias_inspecao_retroativa = 30;


INSERT INTO permissao_web values (140, 'Editar Fiscais', 0);
INSERT INTO programa_pagina_permissao VALUES(482, 140, 1210, 0, '');

/*
    Sulivan - 26/02/2019 - #24081
*/
-- Table: tipo_projeto_req_vig
create table tipo_projeto_req_vig (
    cd_tipo_projeto_req_vig     INT8           not null,
    cd_req_vigilancia           INT8           not null,
    cd_tp_projeto_vigilancia    INT8           not null,
    version                     INT8           not null
);

alter table tipo_projeto_req_vig
   add constraint PK_TP_PROJ_REQ_VIG primary key (cd_tipo_projeto_req_vig);

alter table tipo_projeto_req_vig
    add constraint FK_TP_PROJ_REQ_VIG_REF_REQ_VIG foreign key (cd_req_vigilancia)
    references requerimento_vigilancia (cd_req_vigilancia)
       on delete restrict on update restrict;

alter table tipo_projeto_req_vig
    add constraint FK_TP_PROJ_REQ_VIG_REF_TP_PROJ_VIG foreign key (cd_tp_projeto_vigilancia)
    references tipo_projeto_vigilancia (cd_tp_projeto_vigilancia)
     on delete restrict on update restrict;

CREATE TABLE auditschema.tipo_projeto_req_vig AS SELECT t2.*, t1.* FROM tipo_projeto_req_vig t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_tipo_projeto_req_vig;alter table auditschema.tipo_projeto_req_vig add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON tipo_projeto_req_vig FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE SEQUENCE seq_tipo_projeto_req_vig start with 1;

/*
    Leonardo - 26/02/2019 -#23480
*/

alter table configuracao_vigilancia add column considerar_dt_solic_alvara int2;
alter table auditschema.configuracao_vigilancia add column considerar_dt_solic_alvara int2;
UPDATE configuracao_vigilancia SET considerar_dt_solic_alvara = 0;


alter table configuracao_vigilancia add column considerar_dt_solic_cred int2;
alter table auditschema.configuracao_vigilancia add column considerar_dt_solic_cred int2;
UPDATE configuracao_vigilancia SET considerar_dt_solic_cred = 0;

alter table configuracao_vigilancia add column considerar_dt_solic_licenca int2;
alter table auditschema.configuracao_vigilancia add column considerar_dt_solic_licenca int2;
UPDATE configuracao_vigilancia SET considerar_dt_solic_licenca = 0;


alter table configuracao_vigilancia add column finalizar_requerimento_receita int2;
alter table auditschema.configuracao_vigilancia add column finalizar_requerimento_receita int2;
UPDATE configuracao_vigilancia SET finalizar_requerimento_receita = 0;


/*
    Silvio - 26/02/2019 - #24195
*/
create table elo_req_vigi_resp_tec (
    cd_elo_req_vigi_resp_tec  INT8          not null,
    cd_req_vigilancia      		INT8          not null,
    cd_resp_tecnico      		INT8          not null,
    version                     INT8          not null
);

alter table elo_req_vigi_resp_tec
   add constraint PK_ELO_REQ_VIG_RESP_TEC primary key (cd_elo_req_vigi_resp_tec);

alter table elo_req_vigi_resp_tec
   add constraint FK_ELO_REQ_VIG_REF_REQ_VIG foreign key (cd_req_vigilancia)
      references requerimento_vigilancia (cd_req_vigilancia)
      on delete restrict on update restrict;

alter table elo_req_vigi_resp_tec
   add constraint FK_ELO_REQ_VIG_REF_RESP_TEC foreign key (cd_resp_tecnico)
      references responsavel_tecnico (cd_resp_tecnico)
      on delete restrict on update restrict;

CREATE TABLE auditschema.elo_req_vigi_resp_tec AS SELECT t2.*, t1.* FROM elo_req_vigi_resp_tec t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_elo_req_vigi_resp_tec;alter table auditschema.elo_req_vigi_resp_tec add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON elo_req_vigi_resp_tec FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE SEQUENCE seq_elo_req_vigi_resp_tec start with 1;


create table req_vig_inscricao_imob (
    cd_req_vig_inscricao_imob		INT8          not null,
    cd_req_vigilancia		     INT8          not null,
    nr_inscricao_imobiliaria  	VARCHAR(25)   not null,
    version                   	INT8          not null
);

alter table req_vig_inscricao_imob
   add constraint PK_REQ_VIG_INSCRICAO_IMOB primary key (cd_req_vig_inscricao_imob);

alter table req_vig_inscricao_imob
   add constraint FK_REQ_VIG_INSC_REF_REQ_VIG foreign key (cd_req_vigilancia)
      references requerimento_vigilancia (cd_req_vigilancia)
      on delete restrict on update restrict;

CREATE TABLE auditschema.req_vig_inscricao_imob AS SELECT t2.*, t1.* FROM req_vig_inscricao_imob t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_req_vig_inscricao_imob;alter table auditschema.req_vig_inscricao_imob add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON req_vig_inscricao_imob FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE SEQUENCE seq_req_vig_inscricao_imob start with 1;


alter table requerimento_analise_projeto alter column nr_loteamento_obra type varchar(100);
alter table auditschema.requerimento_analise_projeto alter column nr_loteamento_obra type varchar(100);

alter table req_analise_projeto_parecer add column ds_parecer text null;
alter table auditschema.req_analise_projeto_parecer add column ds_parecer text null;
alter table req_analise_projeto_parecer alter column ds_assunto_parecer drop not null;
alter table req_analise_projeto_parecer alter column ds_dados_projeto drop not null;
alter table req_analise_projeto_parecer alter column ds_adequacao_projeto drop not null;
alter table req_analise_projeto_parecer alter column ds_especificacoes_basicas drop not null;
alter table req_analise_projeto_parecer alter column ds_documentacao_apresentada drop not null;

alter table req_analise_projeto_parecer add column tp_aprovacao INT2 null;
alter table auditschema.req_analise_projeto_parecer add column tp_aprovacao INT2 null;

ALTER TABLE req_analise_projeto_parecer ADD COLUMN num_parecer varchar(20);
ALTER TABLE auditschema.req_analise_projeto_parecer ADD COLUMN num_parecer varchar(20);

/*
    Silvio - 26/02/2019 - #24230
*/
alter table requerimento_vistoria_pba alter column nr_loteamento_obra type varchar(100);
alter table auditschema.requerimento_vistoria_pba alter column nr_loteamento_obra type varchar(100);

create table req_ana_pba_conf_tec (
    cd_req_ana_pba_conf_tec			INT8           not null,
    cd_requerimento_vistoria_pba	     INT8           not null,
    num_parecer_conformidade_tecnica	INT8			null,
    situacao_conformidade			INT2			null,
    dt_inspecao					TIMESTAMP		null,
    ds_objetivo					TEXT			null,
    ds_constatacoes					TEXT			null,
    ds_ressalvas					TEXT			null,
    ds_conclusao					TEXT			null,
    ds_caracterizacao				TEXT			null,
    version                   	INT8          not null
);

alter table req_ana_pba_conf_tec
   add constraint PK_REQ_ANA_PBA_CONF_TEC primary key (cd_req_ana_pba_conf_tec);

alter table req_ana_pba_conf_tec
   add constraint FK_REQ_ANA_CNTC_REF_REQ_VIS_PBA foreign key (cd_requerimento_vistoria_pba)
      references requerimento_vistoria_pba (cd_requerimento_vistoria_pba)
      on delete restrict on update restrict;

CREATE TABLE auditschema.req_ana_pba_conf_tec AS SELECT t2.*, t1.* FROM req_ana_pba_conf_tec t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_req_ana_pba_conf_tec;alter table auditschema.req_ana_pba_conf_tec add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON req_ana_pba_conf_tec FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
CREATE SEQUENCE seq_req_ana_pba_conf_tec start with 1;

/*
    Leonardo - 07/03/2019 - #23929
*/

ALTER TABLE configuracao_vigilancia ADD COLUMN iniciar_seq_habitese INT8 NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN iniciar_seq_habitese INT8 NULL;

ALTER TABLE req_vistoria_hidro_parecer ADD COLUMN numero_habitese INT8 NULL;
ALTER TABLE auditschema.req_vistoria_hidro_parecer ADD COLUMN numero_habitese INT8 NULL;

/*
    Silvio - 08/03/2019 - #24250
*/
alter table requerimento_projeto_hidro drop column area;
alter table auditschema.requerimento_projeto_hidro drop column area;

alter table tipo_projeto_req_vig add column area NUMERIC(12,4);
alter table auditschema.tipo_projeto_req_vig add column area NUMERIC(12,4);

alter table requerimento_projeto_hidro drop column obra_endereco;
alter table auditschema.requerimento_projeto_hidro drop column obra_endereco;

alter table requerimento_projeto_hidro add column cd_vigilancia_endereco INT8;
alter table auditschema.requerimento_projeto_hidro add column cd_vigilancia_endereco INT8;

alter table requerimento_projeto_hidro
   add constraint FK_REQ_PROJETO_HID_REF_VIG_END foreign key (cd_vigilancia_endereco)
      references vigilancia_endereco (cd_vigilancia_endereco)
      on delete restrict on update restrict;

  alter table requerimento_vistoria_hidro drop column obra_endereco;
  alter table auditschema.requerimento_vistoria_hidro drop column obra_endereco;

  alter table requerimento_vistoria_hidro add column cd_vigilancia_endereco INT8;
  alter table auditschema.requerimento_vistoria_hidro add column cd_vigilancia_endereco INT8;

  alter table requerimento_vistoria_hidro
     add constraint FK_REQ_VIST_HID_REF_VIG_END foreign key (cd_vigilancia_endereco)
        references vigilancia_endereco (cd_vigilancia_endereco)
        on delete restrict on update restrict;

alter table requerimento_projeto_hidro alter column obra_nr_loteamento type varchar(100);
alter table auditschema.requerimento_projeto_hidro alter column obra_nr_loteamento type varchar(100);
alter table requerimento_vistoria_hidro alter column obra_nr_loteamento type varchar(100);
alter table auditschema.requerimento_vistoria_hidro alter column obra_nr_loteamento type varchar(100);

alter table requerimento_projeto_hidro drop column obra_nr_inscricao_imob;
alter table auditschema.requerimento_projeto_hidro drop column obra_nr_inscricao_imob;

alter table requerimento_projeto_hidro add column num_projeto_aprovado VARCHAR;
alter table auditschema.requerimento_projeto_hidro add column num_projeto_aprovado VARCHAR;

alter table requerimento_projeto_hidro add column num_proj_urbanistico VARCHAR(20);
alter table auditschema.requerimento_projeto_hidro add column num_proj_urbanistico VARCHAR(20);

alter table requerimento_projeto_hidro add column num_lic_ambiental VARCHAR(20);
alter table auditschema.requerimento_projeto_hidro add column num_lic_ambiental VARCHAR(20);

alter table requerimento_projeto_hidro add column num_proj_esgoto VARCHAR(20);
alter table auditschema.requerimento_projeto_hidro add column num_proj_esgoto VARCHAR(20);

alter table requerimento_projeto_hidro add column num_proj_agua VARCHAR(20);
alter table auditschema.requerimento_projeto_hidro add column num_proj_agua VARCHAR(20);

alter table req_projeto_hidrossanitario_parecer add column populacao_lote INT8;
alter table auditschema.req_projeto_hidrossanitario_parecer add column populacao_lote INT8;

alter table req_projeto_hidrossanitario_parecer add column nr_lap_lic_ambiental VARCHAR;
alter table auditschema.req_projeto_hidrossanitario_parecer add column nr_lap_lic_ambiental VARCHAR;

alter table req_projeto_hidrossanitario_parecer add column abastecimento_agua_somatorio_itens INT4;
alter table auditschema.req_projeto_hidrossanitario_parecer add column abastecimento_agua_somatorio_itens INT4;

alter table req_projeto_hidrossanitario_parecer add column tratamento_efluentes_somatorio_itens INT4;
alter table auditschema.req_projeto_hidrossanitario_parecer add column tratamento_efluentes_somatorio_itens INT4;

alter table req_projeto_hidrossanitario_parecer add column abastecimento_agua_outros VARCHAR(200);
alter table auditschema.req_projeto_hidrossanitario_parecer add column abastecimento_outros VARCHAR(200);

alter table req_projeto_hidrossanitario_parecer add column tratamento_efluentes_outros VARCHAR(200);
alter table auditschema.req_projeto_hidrossanitario_parecer add column tratamento_efluentes_outros VARCHAR(200);

alter table req_projeto_hidrossanitario_parecer add column area_captacao VARCHAR(200);
alter table auditschema.req_projeto_hidrossanitario_parecer add column area_captacao VARCHAR(200);

/*
    Silvio - 11/03/2019 - #24313
*/
alter table requerimento_vistoria_hidro add column num_projeto_aprovado VARCHAR;
alter table auditschema.requerimento_vistoria_hidro add column num_projeto_aprovado VARCHAR;

alter table requerimento_vistoria_hidro add column num_proj_urbanistico VARCHAR(20);
alter table auditschema.requerimento_vistoria_hidro add column num_proj_urbanistico VARCHAR(20);

alter table requerimento_vistoria_hidro add column num_lic_ambiental VARCHAR(20);
alter table auditschema.requerimento_vistoria_hidro add column num_lic_ambiental VARCHAR(20);

alter table requerimento_vistoria_hidro add column num_proj_esgoto VARCHAR(20);
alter table auditschema.requerimento_vistoria_hidro add column num_proj_esgoto VARCHAR(20);

alter table requerimento_vistoria_hidro add column num_proj_agua VARCHAR(20);
alter table auditschema.requerimento_vistoria_hidro add column num_proj_agua VARCHAR(20);

alter table requerimento_vistoria_hidro drop column obra_nr_inscricao_imob;
alter table auditschema.requerimento_vistoria_hidro drop column obra_nr_inscricao_imob;

alter table requerimento_vistoria_hidro drop column area;
alter table auditschema.requerimento_vistoria_hidro drop column area;

alter table req_vistoria_hidro_parecer add column tipo_concessao INT2;
alter table auditschema.req_vistoria_hidro_parecer add column tipo_concessao INT2;

alter table requerimento_projeto_hidro add column nr_aprovacao int4 null;
alter table auditschema.requerimento_projeto_hidro add column nr_aprovacao int4 null;

alter table requerimento_licenca_transporte_veiculo add column cd_vigilancia_financeiro INT8;
alter table auditschema.requerimento_licenca_transporte_veiculo add column cd_vigilancia_financeiro INT8;

alter table requerimento_licenca_transporte_veiculo
   add constraint FK_REQ_LIC_VEI_REF_VIG_FIN foreign key (cd_vigilancia_financeiro)
      references vigilancia_financeiro (cd_vigilancia_financeiro)
      on delete restrict on update restrict;


UPDATE requerimento_licenca_transporte_veiculo rltv set cd_vigilancia_financeiro = rv.cd_vigilancia_financeiro
FROM requerimento_licenca_transporte rlt
JOIN requerimento_vigilancia rv on(rv.cd_req_vigilancia = rlt.cd_req_vigilancia)
JOIN vigilancia_financeiro vf on(vf.cd_vigilancia_financeiro = rv.cd_vigilancia_financeiro)
WHERE rlt.cd_requerimento_licenca_transporte  = rltv.cd_requerimento_licenca_transporte;


ALTER TABLE requerimento_vigilancia DISABLE TRIGGER USER;
UPDATE requerimento_vigilancia SET cd_vigilancia_financeiro = null;
ALTER TABLE requerimento_vigilancia ENABLE TRIGGER USER;
ALTER TABLE requerimento_vigilancia DROP CONSTRAINT FK_REQ_VIG_REF_VIG_FINANC;


ALTER TABLE configuracao_vigilancia_financeiro ADD COLUMN tp_cobranca_lic_transp INT2 null;
ALTER TABLE auditschema.configuracao_vigilancia_financeiro ADD COLUMN tp_cobranca_lic_transp INT2 null;
UPDATE configuracao_vigilancia_financeiro SET tp_cobranca_lic_transp = 0;

ALTER TABLE vigilancia_financeiro ADD COLUMN ds_instrucoes_pagador VARCHAR NULL;
ALTER TABLE auditschema.vigilancia_financeiro ADD COLUMN ds_instrucoes_pagador VARCHAR NULL;

ALTER TABLE vigilancia_financeiro ADD COLUMN ds_motivo_cancelamento VARCHAR NULL;
ALTER TABLE auditschema.vigilancia_financeiro ADD COLUMN ds_motivo_cancelamento VARCHAR NULL;

INSERT INTO programa_pagina VALUES (1702, 'br.com.celk.view.vigilancia.financeiro.ConsultaVigilanciaFinanceiroPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (947, 'Gestão Financeira', 1702, 'N');
INSERT INTO programa_web_pagina VALUES (1814, 947, 1702);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1197,'Gestão Financeira','gestaoFinanceira',836,947,307,0,0);

INSERT INTO programa_pagina VALUES (1703, 'br.com.celk.view.vigilancia.financeiro.DetalhesVigilanciaFinanceiroPage', 'N');
INSERT INTO programa_web_pagina VALUES (1815, 947, 1703);

INSERT INTO programa_pagina_permissao VALUES(485, 12, 1702, 0, '');
INSERT INTO programa_pagina_permissao VALUES(486, 1, 1702, 0, '');
INSERT INTO programa_pagina_permissao VALUES(487, 6, 1702, 0, '');
INSERT INTO programa_pagina_permissao VALUES(488, 49, 1702, 0, 'alterarVencimentoBoleto');
INSERT INTO programa_pagina_permissao VALUES(489, 44, 1702, 0, 'consultarBanco');
INSERT INTO programa_pagina_permissao VALUES(492, 126, 1702, 0, 'comprovantePagamento');


UPDATE programa_web SET ds_prg_web= 'Relação Financeira' where cd_prg_web=760;
UPDATE menu_web SET ds_menu = 'Relação Financeira', ds_bundle='relacaoFinanceira' where cd_menu=1001;


/*
    Silvio - 22/04/2019 - #25238
*/
alter table configuracao_vigilancia add column flag_formato_sequencial INT2;
alter table auditschema.configuracao_vigilancia add column flag_formato_sequencial INT2;
UPDATE configuracao_vigilancia SET flag_formato_sequencial = 0;

INSERT INTO controle_num_receita_b VALUES(nextval('seq_gem'), 4, 0, 0);

/*
    Leonardo - 23/04/2019 - #24087
*/

alter table vigilancia_financeiro alter column cd_req_vigilancia drop not null;

ALTER TABLE vigilancia_financeiro add column cd_auto_multa INT8 null;
ALTER TABLE auditschema.vigilancia_financeiro add column cd_auto_multa INT8 null;
ALTER TABLE vigilancia_financeiro
        ADD CONSTRAINT FK_VIG_FIN_REF_AUTO_MULTA FOREIGN KEY (cd_auto_multa)
        REFERENCES auto_multa (cd_auto_multa)
        ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE boleto ADD COLUMN ds_pagador VARCHAR NULL;
ALTER TABLE auditschema.boleto ADD COLUMN ds_pagador VARCHAR NULL;

ALTER TABLE vigilancia_financeiro ADD COLUMN situacao_cad INT2;
ALTER TABLE auditschema.vigilancia_financeiro ADD COLUMN situacao_cad INT2;

/*
    Leonardo - 30/04/2019 - #25291
*/


ALTER TABLE vigilancia_financeiro add column cd_auto_penalidade INT8 null;
ALTER TABLE auditschema.vigilancia_financeiro add column cd_auto_penalidade INT8 null;
ALTER TABLE vigilancia_financeiro
        ADD CONSTRAINT FK_VIG_FIN_REF_AUTO_PEN FOREIGN KEY (cd_auto_penalidade)
        REFERENCES auto_penalidade (cd_auto_penalidade)
        ON DELETE RESTRICT ON UPDATE RESTRICT;


INSERT INTO permissao_web values (141, 'Boleto', 0);
INSERT INTO programa_pagina_permissao VALUES(490, 141, 1076, 0, '');
INSERT INTO programa_pagina_permissao VALUES(491, 141, 1621, 0, '');


ALTER TABLE configuracao_vigilancia_financeiro ADD COLUMN cod_beneficiario INT4 NULL;
ALTER TABLE auditschema.configuracao_vigilancia_financeiro ADD COLUMN cod_beneficiario INT4 NULL;

ALTER TABLE configuracao_vigilancia_financeiro ADD COLUMN cpf_cnpj_beneficiario CHAR(14) NULL;
ALTER TABLE auditschema.configuracao_vigilancia_financeiro ADD COLUMN cpf_cnpj_beneficiario CHAR(14) NULL;

INSERT INTO programa_pagina_permissao VALUES(493, 126, 403, 0, 'anexarComprovantePagamento');
INSERT INTO programa_pagina_permissao VALUES(494, 126, 1403, 0, 'anexarComprovantePagamento');

INSERT INTO programa_pagina VALUES (1711, 'br.com.celk.view.vigilancia.financeiro.BoletoMultiploVigilanciaPage', 'N');
INSERT INTO programa_web_pagina VALUES (1824, 245, 1711);
INSERT INTO programa_web_pagina VALUES (1825, 764, 1711);

/*
    Silvio - 23/04/2019 - #25289
*/
ALTER TABLE ocorrencia_req_vigilancia ADD COLUMN flag_publico_privado INT2;
ALTER TABLE auditschema.ocorrencia_req_vigilancia ADD COLUMN flag_publico_privado INT2;
UPDATE ocorrencia_req_vigilancia SET flag_publico_privado = 0;


/*
    Laudecir - 06/05/2019
*/
INSERT INTO AGENDADOR_PROCESSO (cd_agendador_processo, cd_processo, nm_servico, ds_servico, status, tp_processo, version)
     VALUES (57, 57, 'Atualizar Situação dos Boletos - VIA WEB SERVICE', 'Atualiza a Situação dos Boletos que estão aguardando pagamento com base no status retornado através do webservice da caixa.', 0, 12, 0);

DELETE FROM AGENDADOR_PROCESSO WHERE cd_agendador_processo = 54; -- Consultar Retorno das Remessas

/*
    Silvio - 09/05/2019 - #25262
*/

alter table vigilancia_financeiro add column isento int2;
alter table auditschema.vigilancia_financeiro add column isento int2;
UPDATE vigilancia_financeiro SET isento = case when (valor <= 0 and numeracao is null) then 1 else 0 end;

alter table vigilancia_financeiro add column ds_isencao VARCHAR;
alter table auditschema.vigilancia_financeiro add column ds_isencao VARCHAR;

UPDATE vigilancia_financeiro SET dt_vencimento = date_trunc('day', dt_vencimento) + '23:59:59.999';

/*
    Leonardo - 15/05/2019 - #24260
*/

ALTER TABLE tipo_projeto_vigilancia ADD COLUMN tp_cobranca INT2;
ALTER TABLE auditschema.tipo_projeto_vigilancia ADD COLUMN tp_cobranca INT2;
UPDATE tipo_projeto_vigilancia SET tp_cobranca = 0;
alter table tipo_projeto_vigilancia alter column tp_cobranca set not null;


ALTER TABLE atividade_estabelecimento ADD COLUMN ds_complementar VARCHAR NULL;
ALTER TABLE auditschema.atividade_estabelecimento ADD COLUMN ds_complementar VARCHAR NULL;

INSERT INTO boleto (cd_boleto,cd_integracao,token,numero,nr_documento,dt_vencto,situacao,valor,valor_pago,juros_mora,dt_pagto,dt_credito,version,ds_pagador)
SELECT nextval('seq_boleto'), null, token_boleto, null, cd_vigilancia_financeiro, cast(dt_vencimento AS DATE),
CASE WHEN status = 2 THEN 4 WHEN status = 3 THEN 3 WHEN status = 1 THEN 1 WHEN status = 0 THEN 0 END, valor, null, null, null, null, 0,
(SELECT max(rv.nome) FROM requerimento_vigilancia rv WHERE rv.cd_req_vigilancia = vf.cd_req_vigilancia) FROM vigilancia_financeiro vf WHERE token_boleto is not null;

update vigilancia_financeiro vf set cd_boleto = (SELECT cd_boleto FROM boleto bo where bo.token = vf.token_boleto);

INSERT INTO AGENDADOR_PROCESSO (cd_agendador_processo, cd_processo, nm_servico, ds_servico, status, tp_processo, version)
     VALUES (58, 58, 'Atualizar Situação dos Boletos - VIA RETORNO DE REMESSA', 'Atualiza a Situação dos Boletos via retorno de remessa das datas anteriores.', 0, 12, 0);


alter table configuracao_vigilancia add column flag_cadastra_req_externo INT2;
alter table auditschema.configuracao_vigilancia add column flag_cadastra_req_externo INT2;
update configuracao_vigilancia set flag_cadastra_req_externo = 1;

/*
   Alterações para o dia 18/06
*/

ALTER TABLE auto_intimacao ADD COLUMN cd_auto_multa int8 null;
ALTER TABLE auditschema.auto_intimacao ADD COLUMN cd_auto_multa int8 null;
ALTER TABLE auto_intimacao
   add constraint FK_AUTO_INT_REF_AUTO_MULTA foreign key (cd_auto_multa)
      references auto_multa (cd_auto_multa)
      on delete restrict on update restrict;

ALTER TABLE relatorio_inspecao ADD COLUMN cd_auto_multa int8 null;
ALTER TABLE auditschema.relatorio_inspecao ADD COLUMN cd_auto_multa int8 null;
ALTER TABLE relatorio_inspecao
   add constraint FK_REL_INSP_REF_AUTO_MULTA foreign key (cd_auto_multa)
      references auto_multa (cd_auto_multa)
      on delete restrict on update restrict;

INSERT INTO programa_pagina_permissao VALUES(495, 131, 1068, 0, '');
INSERT INTO programa_pagina_permissao VALUES(496, 131, 1078, 0, '');

INSERT INTO programa_pagina_permissao VALUES(497, 98, 1564, 0, 'exibirSomenteProcessosFiscal');

alter table configuracao_vigilancia add column url_roteiro_inspecao varchar;
alter table auditschema.configuracao_vigilancia add column url_roteiro_inspecao varchar;


ALTER TABLE processo_adm ADD COLUMN situacao_financeira int8 null;
ALTER TABLE auditschema.processo_adm ADD COLUMN situacao_financeira int8 null;

/*
    Claudio
*/
INSERT INTO agendador_processo
(cd_agendador_processo,cd_processo,nm_servico                             ,ds_servico                                                                                                                                                                                        ,status,intervalo_minutos,hora_mes,minuto_mes,dia_mes,segunda,terca,quarta,quinta,sexta,sabado,domingo,hora_semana,minuto_semana,version,tp_config_processo,tp_processo) VALUES
(62                   ,62         ,'Notificação de Requerimentos Parados','Processo com finalidade de enviar notificação por mensagem interna para os fiscais sobre os requerimentos sem movimentação por determinado tempo',0     ,null             ,null    ,null      ,null   ,null   ,null ,null  ,null  ,null ,null  ,null   ,null       ,null         ,0      ,null              ,12          );


/*
    Marco

*/

alter table req_projeto_hidrossanitario_parecer add column data_saida date null;

alter table req_projeto_hidrossanitario_parecer add column data_retorno date null;

alter table auditschema.req_projeto_hidrossanitario_parecer add column data_saida date null;

alter table auditschema.req_projeto_hidrossanitario_parecer add column data_retorno date null;

/*
    Laudecir - 16/06/2019
*/
alter table configuracao_vigilancia_financeiro add column qtd_dias_vencto_boleto int2;
alter table configuracao_vigilancia_financeiro add column qtd_dias_vencto_boleto_autos int2;
alter table auditschema.configuracao_vigilancia_financeiro add column qtd_dias_vencto_boleto int2;
alter table auditschema.configuracao_vigilancia_financeiro add column qtd_dias_vencto_boleto_autos int2;

ALTER TABLE configuracao_vigilancia ALTER COLUMN obs_destaque_alvara TYPE VARCHAR;
ALTER TABLE auditschema.configuracao_vigilancia ALTER COLUMN obs_destaque_alvara TYPE VARCHAR;


/*
    Melhoria campos de anexo de documentacao
*/

ALTER TABLE requerimento_vigilancia_anexo ADD COLUMN cd_req_vig_parecer int8 null;
ALTER TABLE auditschema.requerimento_vigilancia_anexo ADD COLUMN cd_req_vig_parecer int8 null;
ALTER TABLE requerimento_vigilancia_anexo
   add constraint FK_VIGILANCIA_ANEXO_REF_REQ_VIG_PARECER foreign key (cd_req_vig_parecer)
      references requerimento_vig_parecer (cd_req_vig_parecer)
      on delete restrict on update restrict;

ALTER TABLE requerimento_vigilancia_anexo ADD COLUMN cd_req_ana_pro_parecer int8 null;
ALTER TABLE auditschema.requerimento_vigilancia_anexo ADD COLUMN cd_req_ana_pro_parecer int8 null;
ALTER TABLE requerimento_vigilancia_anexo
   add constraint FK_VIGILANCIA_ANEXO_REF_REQ_PBA_PARECER foreign key (cd_req_ana_pro_parecer)
      references req_analise_projeto_parecer (cd_req_ana_pro_parecer)
      on delete restrict on update restrict;

ALTER TABLE requerimento_vigilancia_anexo ADD COLUMN cd_req_ana_pba_conf_tec int8 null;
ALTER TABLE auditschema.requerimento_vigilancia_anexo ADD COLUMN cd_req_ana_pba_conf_tec int8 null;
ALTER TABLE requerimento_vigilancia_anexo
   add constraint FK_VIGILANCIA_ANEXO_REF_REQ_PBA_CONF foreign key (cd_req_ana_pba_conf_tec)
      references req_ana_pba_conf_tec (cd_req_ana_pba_conf_tec)
      on delete restrict on update restrict;

ALTER TABLE requerimento_vigilancia_anexo ADD COLUMN cd_req_pro_hidros_parecer int8 null;
ALTER TABLE auditschema.requerimento_vigilancia_anexo ADD COLUMN cd_req_pro_hidros_parecer int8 null;
ALTER TABLE requerimento_vigilancia_anexo
   add constraint FK_VIGILANCIA_ANEXO_REF_REQ_HIDRO_PARECER foreign key (cd_req_pro_hidros_parecer)
      references req_projeto_hidrossanitario_parecer (cd_req_pro_hidros_parecer)
      on delete restrict on update restrict;

ALTER TABLE requerimento_vigilancia_anexo ADD COLUMN cd_req_vistoria_hidro_parecer int8 null;
ALTER TABLE auditschema.requerimento_vigilancia_anexo ADD COLUMN cd_req_vistoria_hidro_parecer int8 null;
ALTER TABLE requerimento_vigilancia_anexo
   add constraint FK_VIGILANCIA_ANEXO_REF_REQ_VIST_HIDRO_PARECER foreign key (cd_req_vistoria_hidro_parecer)
      references req_vistoria_hidro_parecer (cd_req_vistoria_hidro_parecer)
      on delete restrict on update restrict;

ALTER TABLE requerimento_vigilancia_anexo ADD COLUMN cd_auto_multa int8 null;
ALTER TABLE auditschema.requerimento_vigilancia_anexo ADD COLUMN cd_auto_multa int8 null;
ALTER TABLE requerimento_vigilancia_anexo
   add constraint FK_VIGILANCIA_ANEXO_REF_AUTO_MULTA foreign key (cd_auto_multa)
      references auto_multa (cd_auto_multa)
      on delete restrict on update restrict;


alter table configuracao_vigilancia add column flag_linha_assinatura INT2;
alter table auditschema.configuracao_vigilancia add column flag_linha_assinatura INT2;
UPDATE configuracao_vigilancia SET flag_linha_assinatura = 1;

/*
    Elton - 19/06/2019
*/
INSERT INTO agendador_processo VALUES
(63, 63, 'Notificação do Vencimento da Validade de Alvarás/Licenças','Executa o processo de notificação, por email, com o Vencimento da Validade de Alvarás/Licenças.', 0, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, 12);

INSERT INTO agendador_processo VALUES
(64, 64, 'Notificação do Vencimento do Prazo de Defesas/Recursos','Executa o processo de notificação, por email, com o Vencimento do Prazo de Defesas/Recursos.', 0, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, 12);


ALTER TABLE processo_adm_parecer ADD COLUMN cd_usuario_fin NUMERIC(6) null;
ALTER TABLE auditschema.processo_adm_parecer ADD COLUMN cd_usuario_fin NUMERIC(6) null;

ALTER TABLE processo_adm_decisao ADD COLUMN cd_usuario_fin NUMERIC(6) null;
ALTER TABLE auditschema.processo_adm_decisao ADD COLUMN cd_usuario_fin NUMERIC(6) null;

ALTER TABLE requerimento_vigilancia_fiscal ADD COLUMN cd_profissional_cad INT8 null;
ALTER TABLE auditschema.requerimento_vigilancia_fiscal ADD COLUMN cd_profissional_cad INT8 null;

ALTER TABLE requerimento_vigilancia_fiscal
    ADD CONSTRAINT fk_req_vig_fiscal_ref_prof_cad FOREIGN KEY (cd_profissional_cad)
    REFERENCES profissional (cd_profissional)
    ON DELETE restrict
    ON UPDATE restrict;


INSERT INTO permissao_web values (142, 'Remover Fiscais', 0);
INSERT INTO programa_pagina_permissao VALUES(498, 142, 1210, 0, '');

INSERT INTO programa_pagina_permissao VALUES(499, 139, 1403, 0, 'encaminharFiscais');
-- PROCESSO ANTIGO DE DEFESA PREVIA, DEPRECIADO
DELETE FROM configuracao_vigilancia_atividades where tipo_processo_padrao = 21;


alter table auto_multa add column nro_formulario int8 null;
alter table auditschema.auto_multa add column nro_formulario int8 null;

/*
    Silvio - 01/07/2019 #24098
*/
ALTER TABLE atividades_vigilancia ADD COLUMN flag_solic_juridica INT2 NULL;
ALTER TABLE auditschema.atividades_vigilancia ADD COLUMN flag_solic_juridica INT2 NULL;

ALTER TABLE atividades_vigilancia disable trigger user;
UPDATE atividades_vigilancia SET flag_solic_juridica = 0;
ALTER TABLE atividades_vigilancia ENABLE trigger user;
ALTER TABLE atividades_vigilancia ALTER COLUMN flag_solic_juridica set not null;

ALTER TABLE requerimento_restituicao_taxa ADD COLUMN cd_atividades_vigilancia INT8 NULL;
ALTER TABLE auditschema.requerimento_restituicao_taxa ADD COLUMN cd_atividades_vigilancia INT8 NULL;
ALTER TABLE requerimento_restituicao_taxa
    ADD CONSTRAINT fk_req_res_taxa_ref_ativ_vig FOREIGN KEY (cd_atividades_vigilancia)
    REFERENCES atividades_vigilancia (cd_atividades_vigilancia)
    ON DELETE restrict
    ON UPDATE restrict;

update tipo_solicitacao set ds_tipo_solicitacao = 'Requerimentos Diversos / COMATS' WHERE tp_requerimento = 19;

update permissao_web set ds_permissao = 'Solicitações Jurídicas' WHERE cd_permissao = 136;
update programa_pagina_permissao set ds_bundle = 'requerimentoSolicitacaoJuridica' WHERE cd_prog_pag_perm = 472;
update programa_pagina_permissao set ds_bundle = 'requerimentoSolicitacaoJuridica' WHERE cd_prog_pag_perm = 473;

alter table processo_adm_ocorrencia alter column ds_ocorrencia type varchar(1500);
alter table auditschema.processo_adm_ocorrencia alter column ds_ocorrencia type varchar(1500);

INSERT INTO programa_pagina_permissao VALUES(500, 122, 403, 0, 'historicoContribuinte');
INSERT INTO programa_pagina_permissao VALUES(501, 122, 1403, 0, 'historicoContribuinte');


alter table configuracao_vigilancia add column tipo_fluxo_externo INT2;
alter table auditschema.configuracao_vigilancia add column tipo_fluxo_externo INT2;
update configuracao_vigilancia set tipo_fluxo_externo = 0;
alter table configuracao_vigilancia alter column tipo_fluxo_externo set not null;

/*
    Silvio - 10/07/2019 #26516
*/
alter table req_analise_projeto_parecer add column ds_dimensionamento_ambientes text;
alter table auditschema.req_analise_projeto_parecer add column ds_dimensionamento_ambientes text;

alter table req_analise_projeto_parecer add column ds_funcionalidade_edificacao text;
alter table auditschema.req_analise_projeto_parecer add column ds_funcionalidade_edificacao text;

alter table req_analise_projeto_parecer add column ds_inst_ordinarias_especiais text;
alter table auditschema.req_analise_projeto_parecer add column ds_inst_ordinarias_especiais text;

alter table req_analise_projeto_parecer add column ds_obrigacoes text;
alter table auditschema.req_analise_projeto_parecer add column ds_obrigacoes text;

UPDATE req_analise_projeto_parecer
	SET ds_dimensionamento_ambientes = t1.txt_dimensionamento , ds_funcionalidade_edificacao = t1.txt_funcionalidade_edificacao , ds_inst_ordinarias_especiais = t1.txt_instalacoes_ordinarias , ds_obrigacoes = t1.txt_recomendacoes
	FROM  configuracao_vigilancia t1 WHERE req_analise_projeto_parecer.status = 2;

/*
    Silvio - 12/07/2019 #26481
*/
alter table denuncia alter column logradouro_denunciado type text;
alter table auditschema.denuncia alter column logradouro_denunciado type text;

alter table denuncia alter column logradouro_denunciante type text;
alter table auditschema.denuncia alter column logradouro_denunciante type text;


INSERT INTO programa_pagina VALUES (1715, 'br.com.celk.view.vigilancia.requerimentos.RequerimentoVigilanciaAnalisarDocumentacaoPage', 'N');
INSERT INTO programa_web_pagina VALUES (1829, 764, 1715);

/*
    Silvio - 15/07/2019 #26589
*/
INSERT INTO programa_pagina_permissao VALUES(502, 14, 1415, 0, 'visualizarTodosRegistros');

/*
    Silvio - 15/07/2019 #26590
*/
INSERT INTO permissao_web values (143, 'Visualizar Cadastro Completo', 0);
INSERT INTO programa_pagina_permissao VALUES(503, 143, 1210, 0, 'naoVizualizarObservacaoImpressaoAlvaraSetorResponsavelAnoBase');
INSERT INTO programa_pagina_permissao VALUES(505, 143, 1248, 0, 'naoVizualizarObservacaoImpressaoAlvaraSetorResponsavelAnoBase');

/*
    Silvio - 15/07/2019 #26588
*/
CREATE SEQUENCE seq_registro_visita_atividade_vig start with 1;

CREATE TABLE registro_visita_atividade_vig (
 cd_registro_visita_atividade_vig   INT8    NOT NULL,
 cd_registro_visita			        INT8    NOT NULL,
 cd_atividades_vigilancia   	        INT8    NOT NULL,
 version         INT8    NOT NULL
);
ALTER TABLE registro_visita_atividade_vig ADD CONSTRAINT pk_reg_visita_atividade_vig PRIMARY KEY (cd_registro_visita_atividade_vig);
ALTER TABLE registro_visita_atividade_vig ADD CONSTRAINT fk_reg_vis_at_vig_ref_reg_vis FOREIGN KEY (cd_registro_visita) REFERENCES registro_visita (cd_registro_visita) ON DELETE restrict ON UPDATE restrict;
ALTER TABLE registro_visita_atividade_vig ADD CONSTRAINT fk_reg_vis_at_vig_ref_atividade FOREIGN KEY (cd_atividades_vigilancia) REFERENCES atividades_vigilancia (cd_atividades_vigilancia) ON DELETE restrict ON UPDATE restrict;

CREATE TABLE auditschema.registro_visita_atividade_vig AS SELECT T2.*, T1.* FROM registro_visita_atividade_vig T1, audit_temp T2 WHERE 1=2;CREATE sequence seq_audit_id_registro_visita_atividade_vig;ALTER TABLE AUDITSCHEMA.registro_visita_atividade_vig ADD PRIMARY KEY (AUDIT_ID);CREATE TRIGGER EMP_AUDIT AFTER INSERT OR UPDATE OR DELETE ON registro_visita_atividade_vig FOR EACH ROW EXECUTE PROCEDURE PROCESS_EMP_AUDIT();

ALTER TABLE lancamento_atividades_vigilancia ADD COLUMN cd_registro_visita INT8 null;
ALTER TABLE auditschema.lancamento_atividades_vigilancia ADD COLUMN cd_registro_visita INT8 null;

ALTER TABLE lancamento_atividades_vigilancia
    ADD CONSTRAINT fk_lanc_ativ_vig_ref_reg_visita FOREIGN KEY (cd_registro_visita)
    REFERENCES registro_visita (cd_registro_visita)
    ON DELETE restrict
    ON UPDATE restrict;

/*
    Laudecir - 16/07/2019 - #26592
*/
insert into programa_pagina values(1716, 'br.com.celk.view.vigilancia.rotinas.automulta.relatorio.RelatorioAutoMultaPage', 'N', 0);
insert into programa_web values(952, 'Relatório de Auto de Multa', 1716, 'N', 0);
insert into programa_web_pagina values(1830, 952, 1716, 0);
insert into menu_web values (1204, 'Relatório de Auto de Multa', 'relatorioAutoMulta', 837, 952, 307, 0, 0);

/*
    Sulivan - 19/07/2019 - #26674
*/
alter table requerimento_receita_a add column cd_profissional_endereco INT8;
alter table auditschema.requerimento_receita_a add column cd_profissional_endereco INT8;

alter table requerimento_receita_a
   add constraint FK_REQ_REC_A_REF_PROF_END foreign key (cd_profissional_endereco)
      references profissional_endereco (cd_profissional_endereco)
      on delete restrict on update restrict;

alter table requerimento_receita_b add column cd_profissional_endereco INT8;
alter table auditschema.requerimento_receita_b add column cd_profissional_endereco INT8;

alter table requerimento_receita_b
   add constraint FK_REQ_REC_B_REF_PROF_END foreign key (cd_profissional_endereco)
      references profissional_endereco (cd_profissional_endereco)
      on delete restrict on update restrict;

alter table requerimento_receita_talidomida add column cd_profissional_endereco INT8;
alter table auditschema.requerimento_receita_talidomida add column cd_profissional_endereco INT8;

alter table requerimento_receita_talidomida
   add constraint FK_REQ_REC_TA_REF_PROF_END foreign key (cd_profissional_endereco)
      references profissional_endereco (cd_profissional_endereco)
      on delete restrict on update restrict;

/*
    Silvio - 15/07/2019 #26588
*/
INSERT INTO permissao_web values (144, 'Editar Requerimento de Analise de Projeto', 0);
INSERT INTO programa_pagina_permissao VALUES(506, 144, 1210, 0, 'editarAnaliseProjetos');

/*
    Laudecir - 20/07/2019 - #26692
*/
alter table configuracao_vigilancia_financeiro add column qtd_dias_desc_prim_faixa_auto int2;
alter table configuracao_vigilancia_financeiro add column percentual_desc_prim_faixa_auto numeric(4,2);
alter table auditschema.configuracao_vigilancia_financeiro add column qtd_dias_desc_prim_faixa_auto int2;
alter table auditschema.configuracao_vigilancia_financeiro add column percentual_desc_prim_faixa_auto numeric(4,2);

alter table configuracao_vigilancia_financeiro add column qtd_dias_desc_seg_faixa_auto int2;
alter table configuracao_vigilancia_financeiro add column percentual_desc_seg_faixa_auto numeric(4,2);
alter table auditschema.configuracao_vigilancia_financeiro add column qtd_dias_desc_seg_faixa_auto int2;
alter table auditschema.configuracao_vigilancia_financeiro add column percentual_desc_seg_faixa_auto numeric(4,2);

alter table configuracao_vigilancia_financeiro add column qtd_dias_desc_terc_faixa_auto int2;
alter table configuracao_vigilancia_financeiro add column percentual_desc_terc_faixa_auto numeric(4,2);
alter table auditschema.configuracao_vigilancia_financeiro add column qtd_dias_desc_terc_faixa_auto int2;
alter table auditschema.configuracao_vigilancia_financeiro add column percentual_desc_terc_faixa_auto numeric(4,2);

/*
    Everton - 19/07/2019
*/
alter table usuario_vigilancia alter column cpf drop not null;
alter table usuario_vigilancia alter column nome drop not null;

/*
    Carol - 22/07/2019
*/
alter table denuncia alter column logradouro_denunciado type varchar(200);
alter table auditschema.denuncia alter column logradouro_denunciado type varchar(200);

alter table denuncia alter column logradouro_denunciante type varchar(200);
alter table auditschema.denuncia alter column logradouro_denunciante type varchar(200);

/*
    Silvio - 22/07/2019 #26588
*/
UPDATE programa_pagina_permissao set cd_prg_pagina = 1403 WHERE cd_prog_pag_perm = 506;
INSERT INTO programa_pagina_permissao VALUES(507, 144, 403, 0, 'editarAnaliseProjetos');

/*
    Silvio - 23/07/2019 #26575
*/
INSERT INTO permissao_web values (145, 'Editar Requerimento de Alvarás', 0);
INSERT INTO programa_pagina_permissao VALUES(509, 145, 1403, 0, 'editarAlvaras');
INSERT INTO programa_pagina_permissao VALUES(510, 145, 403, 0, 'editarAlvaras');

/*
    Silvio - 23/07/2019 #26573
*/
alter table requerimento_vigilancia add column situacao_anterior_conclusao int2;
alter table auditschema.requerimento_vigilancia add column situacao_anterior_conclusao int2;
alter table requerimento_vigilancia add column motivo_reversao_finalizacao text;
alter table auditschema.requerimento_vigilancia add column motivo_reversao_finalizacao text;

INSERT INTO permissao_web values (146, 'Permitir Reverter Requerimento Finalizado', 0);
INSERT INTO programa_pagina_permissao VALUES(511, 146, 1403, 0, 'permitirReverterRequerimentoFinalizado');
INSERT INTO programa_pagina_permissao VALUES(512, 146, 403, 0, 'permitirReverterRequerimentoFinalizado');

/*
    Sulivan - 25/07/2019 #26317 e #26318
*/
alter table tipo_solicitacao_anexo add column regiao_abastecida_agua INT2;
alter table auditschema.tipo_solicitacao_anexo add column regiao_abastecida_agua INT2;

alter table tipo_solicitacao_anexo add column regiao_coberta_rede_esgoto INT2;
alter table auditschema.tipo_solicitacao_anexo add column regiao_coberta_rede_esgoto INT2;

alter table tipo_solicitacao_anexo add column uso_edificacao INT2;
alter table auditschema.tipo_solicitacao_anexo add column uso_edificacao INT2;

-- RODADO ATÉ AQUI NO VIGILANCIAFLORIANOPOLIS.CELK.COM.BR

-- V1

/*
    Leonardo - 29/07/2019 #26242
*/
INSERT INTO programa_pagina_permissao VALUES(513, 126, 1313, 0, 'anexarComprovantePagamento');

/*
    Leonardo - 01/08/2019 #26863
*/
create table requerimento_autorizacao_sanitaria (
    cd_requerimento_autorizacao_sanitaria   INT8            not null,
    cd_req_vigilancia       	            INT8            not null,
    cd_estabelecimento       	            INT8            not null,
    dt_validade                             TIMESTAMP       not null,
    numero                                  INT8            not null,
    ano_base                                INT4            not null,
    estrutura                               INT2            null,
    ds_comercializacao                      VARCHAR         null,
    ds_observacao_destaque                  VARCHAR         null,
    cd_usuario                              NUMERIC(6)      not null,
    dt_cadastro                             TIMESTAMP       not null,
    version                                 INT8            not null
);

alter table requerimento_autorizacao_sanitaria
   add constraint PK_REQ_AUT_SANITARIA primary key (cd_requerimento_autorizacao_sanitaria);

alter table requerimento_autorizacao_sanitaria
   add constraint FK_REQ_AUT_SANITARIA_REF_REQ_VIGI foreign key (cd_req_vigilancia)
      references requerimento_vigilancia (cd_req_vigilancia);

alter table requerimento_autorizacao_sanitaria
   add constraint FK_REQ_AUT_SANITARIA_REF_ESTAB foreign key (cd_estabelecimento)
      references estabelecimento (cd_estabelecimento);

alter table requerimento_autorizacao_sanitaria
       add constraint FK_REQ_AUT_SANITARIA_REF_USUARIO foreign key (cd_usuario)
        references usuarios (cd_usuario);

CREATE TABLE auditschema.requerimento_autorizacao_sanitaria AS SELECT t2.*, t1.* FROM requerimento_autorizacao_sanitaria t1, audit_temp t2 WHERE 1=2;
create sequence seq_audit_id_requerimento_autorizacao_sanitaria;
alter table auditschema.requerimento_autorizacao_sanitaria add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requerimento_autorizacao_sanitaria FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

update tipo_solicitacao set ds_tipo_solicitacao = 'Licença de Transporte' where tp_requerimento = 4;


ALTER TABLE configuracao_vigilancia ADD COLUMN obs_destaque_autorizacao_san VARCHAR NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN obs_destaque_autorizacao_san VARCHAR NULL;

alter table estabelecimento                         add column nr_autorizacao_san                VARCHAR(15)  null;
alter table auditschema.estabelecimento             add column nr_autorizacao_san                VARCHAR(15)  null;
alter table estabelecimento                         add column validade_autorizacao_san          date         null;
alter table auditschema.estabelecimento             add column validade_autorizacao_san          date         null;
alter table estabelecimento                         add column dt_validade_ult_alvara            date         null;
alter table auditschema.estabelecimento             add column dt_validade_ult_alvara            date         null;

INSERT INTO programa_pagina VALUES (1721, 'br.com.celk.view.vigilancia.requerimentos.autorizacaosanitaria.RequerimentoAutorizacaoSanitariaPage', 'N');
INSERT INTO programa_web_pagina VALUES (1835, 684, 1721);
INSERT INTO programa_web_pagina VALUES (1836, 245, 1721);
INSERT INTO programa_web_pagina VALUES (1837, 764, 1721);

INSERT INTO programa_pagina VALUES (1722, 'br.com.celk.view.vigilancia.externo.view.servicos.RequerimentoAutorizacaoSanitariaExternoPage', 'N');
INSERT INTO programa_web_pagina VALUES (1838, 692, 1722);

-- V2

/*
    Leonardo - 08/08/2019 #26923
*/

ALTER TABLE vigilancia_pessoa ADD COLUMN cd_usuario_responsavel NUMERIC(6);
ALTER TABLE auditschema.vigilancia_pessoa ADD COLUMN cd_usuario_responsavel NUMERIC(6);
ALTER TABLE vigilancia_pessoa
   add constraint FK_VIG_PESSOA_REF_USU foreign key (cd_usuario_responsavel)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

-- Sant'Clear - 12/08/2019 #27018

ALTER TABLE registro_visita ADD COLUMN ds_estabelecimento VARCHAR NULL;
ALTER TABLE auditschema.registro_visita ADD COLUMN ds_estabelecimento VARCHAR NULL;
ALTER TABLE registro_visita ALTER COLUMN cd_estabelecimento DROP NOT NULL;


-- V3

/*
    Leonardo - 13/08/2019 #27068
*/

INSERT INTO permissao_web values (148, 'Boleto Complementar', 0);
INSERT INTO programa_pagina_permissao VALUES(515, 148, 1403, 0, 'boletoComplementar');
INSERT INTO programa_pagina_permissao VALUES(516, 22, 1403, 0, 'registrarVisita');

ALTER TABLE requerimento_vigilancia ADD COLUMN situacao_fin_complementar int2 null;
ALTER TABLE auditschema.requerimento_vigilancia ADD COLUMN situacao_fin_complementar int2 null;
ALTER TABLE requerimento_vigilancia DISABLE TRIGGER USER;
UPDATE requerimento_vigilancia SET situacao_fin_complementar = 0;
ALTER TABLE requerimento_vigilancia ENABLE TRIGGER USER;

ALTER TABLE vigilancia_financeiro ADD COLUMN flag_complementar int2 null;
ALTER TABLE auditschema.vigilancia_financeiro ADD COLUMN flag_complementar int2 null;
ALTER TABLE vigilancia_financeiro DISABLE TRIGGER USER;
UPDATE vigilancia_financeiro SET flag_complementar = 0;
ALTER TABLE vigilancia_financeiro ENABLE TRIGGER USER;

-- Sant'Clear - 15/08/2019

INSERT INTO programa_pagina_permissao VALUES(517, 32, 1314, 0, 'naoPermitirCadastrar');

INSERT INTO programa_pagina VALUES (1727, 'br.com.celk.view.vigilancia.processoadministrativo.relatorio.RelatorioProcessoAdministrativoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (957, 'Relatório de Processo Administrativo', 1727, 'N');
INSERT INTO programa_web_pagina VALUES (1843, 957, 1727);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1209,'Relatório de Processo Administrativo','relatorioProcessoAdministrativo',837,957,307,0,0);

-- Sant'Clear - 16/08/2019

UPDATE parametro_gem SET observacao = 'Define a URL do link de download do manual da Vigilância Sanitária' WHERE parametro='urlManualVS';

/*
    Elton - 14/08/2019 - #26164
*/
create table escala_plantao (
    cd_escala_plantao           INT8          not null,
    cd_profissional 		    INT8          not null,
    dt_inicial                  TIMESTAMP     not null,
    dt_final                    TIMESTAMP     not null,
    hora_inicial                TIMESTAMP     not null,
    hora_final                  TIMESTAMP     not null,
    ds_dia_inicio               varchar       not null,
    ds_dia_fim                  varchar       not null,
    cd_usuario                  NUMERIC(6)    not null,
    dt_cadastro                 TIMESTAMP     not null,
    version                     INT8          not null
);

alter table escala_plantao add constraint PK_ESCALA_PLANTAO primary key (cd_escala_plantao);

alter table escala_plantao add constraint FK_ESCALA_PLANTAO_REF_PRO foreign key (cd_profissional) references profissional (cd_profissional);

alter table escala_plantao add constraint FK_ESCALA_PLANTAO_REF_USUARIO foreign key (cd_usuario) references usuarios (cd_usuario);

CREATE TABLE auditschema.escala_plantao AS SELECT t2.*, t1.* FROM escala_plantao t1, audit_temp t2 WHERE 1=2;
create sequence seq_audit_id_escala_plantao;
alter table auditschema.escala_plantao add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON escala_plantao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

create sequence seq_escala_plantao start with 1;

INSERT INTO programa_pagina VALUES (1729, 'br.com.celk.view.vigilancia.escalaplantao.ConsultaEscalaPlantaoPage', 'N');
INSERT INTO programa_pagina VALUES (1730, 'br.com.celk.view.vigilancia.escalaplantao.CadastroEscalaPlantaoPage', 'N');
INSERT INTO programa_web VALUES (959, 'Escala Plantão', 1729, 'N');
INSERT INTO programa_web_pagina VALUES (1845, 959, 1729);
INSERT INTO programa_web_pagina VALUES (1846, 959, 1730);
INSERT INTO menu_web VALUES (1211, 'Escala Plantão', 'escalaPlantao', 654, 959, 307, 0, 0);

INSERT INTO programa_pagina VALUES (1728, 'br.com.celk.view.vigilancia.escalaplantao.RelatorioRelacaoEscalaPlantaoPage', 'N');
INSERT INTO programa_web VALUES (958, 'Relatório Relação Escala de Plantão', 1728, 'N');
INSERT INTO programa_web_pagina VALUES (1844, 958, 1728);
insert into menu_web values (1210, 'Relatório Relação Escala de Plantão', 'relacaoEscalaPlantao', 837, 958, 307, 0, 0);

/*
    Carol - 23/08/2019 #27246
*/

alter table requerimento_evento alter column ds_prep_local type varchar;
alter table auditschema.requerimento_evento alter column ds_prep_local type varchar;

alter table requerimento_evento alter column ds_prep_fora_local type varchar;
alter table auditschema.requerimento_evento alter column ds_prep_fora_local type varchar;

-- V4

-- Sant'Clear - 21/08/2019

INSERT INTO programa_pagina_permissao VALUES(518, 14, 1415, 0, 'profissional');

-- V5

UPDATE estabelecimento e set cd_usuario_responsavel = u.cd_usuario
  FROM usuario_vigilancia uv
  JOIN usuarios u on u.cd_usuario = uv.cd_usuario
 WHERE e.cd_usuario_responsavel is null
   AND e.email = u.ds_email
   AND (e.cnpj_cpf = uv.cpf OR e.cnpj_cpf = uv.cnpj);

UPDATE vigilancia_pessoa vp set cd_usuario_responsavel = u.cd_usuario
  FROM usuario_vigilancia uv
  JOIN usuarios u on u.cd_usuario = uv.cd_usuario
 WHERE vp.cd_usuario_responsavel is null
   AND vp.email = u.ds_email
   AND vp.cpf = uv.cpf;

/*
    Leonardo - 21/08/2019 #27188
*/

UPDATE permissao_web set ds_permissao = 'Alvará de Participante de Evento' where cd_permissao = 65;

INSERT INTO permissao_web values (149, 'Alvará de Evento', 0);
INSERT INTO programa_pagina_permissao VALUES(523, 149, 1248, 0, null);
INSERT INTO programa_pagina_permissao VALUES(524, 149, 1210, 0, null);

alter table eventos_vigilancia add column cd_req_vigilancia INT8;
alter table auditschema.eventos_vigilancia add column cd_req_vigilancia INT8;
alter table eventos_vigilancia
   add constraint FK_EVENTO_VIG_REF_REQ_VIGI foreign key (cd_req_vigilancia)
      references requerimento_vigilancia (cd_req_vigilancia);

ALTER TABLE eventos_vigilancia ADD COLUMN situacao INT2;
ALTER TABLE auditschema.eventos_vigilancia ADD COLUMN situacao INT2;
ALTER TABLE eventos_vigilancia ADD COLUMN ds_observacao_destaque VARCHAR;
ALTER TABLE auditschema.eventos_vigilancia ADD COLUMN ds_observacao_destaque VARCHAR;

ALTER TABLE eventos_vigilancia DISABLE TRIGGER USER;
UPDATE eventos_vigilancia SET situacao = 1;
ALTER TABLE eventos_vigilancia ENABLE TRIGGER USER;

ALTER TABLE configuracao_vigilancia ADD COLUMN obs_destaque_cad_evento VARCHAR NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN obs_destaque_cad_evento VARCHAR NULL;

INSERT INTO programa_pagina VALUES (1732, 'br.com.celk.view.vigilancia.requerimentos.RequerimentoCadastroEventoPage', 'N');
INSERT INTO programa_web_pagina VALUES (1848, 684, 1732);
INSERT INTO programa_web_pagina VALUES (1849, 245, 1732);
INSERT INTO programa_web_pagina VALUES (1850, 764, 1732);

INSERT INTO programa_pagina VALUES (1733, 'br.com.celk.view.vigilancia.externo.view.servicos.RequerimentoCadastroEventoExternoPage', 'N');
INSERT INTO programa_web_pagina VALUES (1851, 692, 1733);

INSERT INTO programa_pagina_permissao VALUES(525, 32, 1216, 0, 'naoPermitirCadastrar');

/*
    Laudecir - 02/09/2019 - #27465 - Juros e Multa
*/
alter table configuracao_vigilancia_financeiro add column percentual_juros_vencimento numeric(7,4);
alter table auditschema.configuracao_vigilancia_financeiro add column percentual_juros_vencimento numeric(7,4);
alter table configuracao_vigilancia_financeiro add column percentual_multa_vencimento numeric(5,2);
alter table auditschema.configuracao_vigilancia_financeiro add column percentual_multa_vencimento numeric(5,2);

-- RODADO ATÉ AQUI NO PETROLINA.CELK.COM.BR

--V6
--V7
--V8

-- Sant'Clear - 05/09/2019
INSERT INTO programa_pagina VALUES(1735,'br.com.celk.view.vigilancia.relatorio.RelatorioTempoAtendimentoPage','N',0);
INSERT INTO programa_web VALUES(962,'Relatório de Tempo de Atendimento',1735,'S',0);
INSERT INTO programa_web_pagina VALUES(1853, 962, 1735, 0);
INSERT INTO menu_web VALUES(1213, 'Relatório de Tempo de Atendimento','relatorioTempoAtendimento',837,962,307,0,0);

/*
    Leonardo - 09/09/2019 - #27546
*/

ALTER TABLE eventos_vigilancia ADD COLUMN nro_alvara INT8;
ALTER TABLE auditschema.eventos_vigilancia ADD COLUMN nro_alvara INT8;

/*
    Sulivan - 12/09/2019 - #27321
*/
alter table tabela_cnae add column situacao INT2;
alter table auditschema.tabela_cnae add column situacao INT2;

alter table tabela_cnae disable trigger user;
update tabela_cnae set situacao = 1;
alter table tabela_cnae alter column situacao set not null;

create table layout_cnae(cnae varchar, inserido varchar, descricao varchar);

insert into layout_cnae values('0111-3/01',null,null);
insert into layout_cnae values('0111-3/02',null,null);
insert into layout_cnae values('0111-3/03',null,null);
insert into layout_cnae values('0111-3/99',null,null);
insert into layout_cnae values('0112-1/01',null,null);
insert into layout_cnae values('0112-1/02',null,null);
insert into layout_cnae values('0112-1/99',null,null);
insert into layout_cnae values('0113-0/00',null,null);
insert into layout_cnae values('0114-8/00',null,null);
insert into layout_cnae values('0115-6/00',null,null);
insert into layout_cnae values('0116-4/01',null,null);
insert into layout_cnae values('0116-4/02',null,null);
insert into layout_cnae values('0116-4/03',null,null);
insert into layout_cnae values('0116-4/99',null,null);
insert into layout_cnae values('0119-9/01',null,null);
insert into layout_cnae values('0119-9/02',null,null);
insert into layout_cnae values('0119-9/03',null,null);
insert into layout_cnae values('0119-9/04',null,null);
insert into layout_cnae values('0119-9/05',null,null);
insert into layout_cnae values('0119-9/06',null,null);
insert into layout_cnae values('0119-9/07',null,null);
insert into layout_cnae values('0119-9/08',null,null);
insert into layout_cnae values('0119-9/09',null,null);
insert into layout_cnae values('0119-9/99',null,null);
insert into layout_cnae values('0121-1/01',null,null);
insert into layout_cnae values('0121-1/02',null,null);
insert into layout_cnae values('0122-9/00',null,null);
insert into layout_cnae values('0131-8/00',null,null);
insert into layout_cnae values('0132-6/00',null,null);
insert into layout_cnae values('0133-4/01',null,null);
insert into layout_cnae values('0133-4/02',null,null);
insert into layout_cnae values('0133-4/03',null,null);
insert into layout_cnae values('0133-4/04',null,null);
insert into layout_cnae values('0133-4/05',null,null);
insert into layout_cnae values('0133-4/06',null,null);
insert into layout_cnae values('0133-4/07',null,null);
insert into layout_cnae values('0133-4/08',null,null);
insert into layout_cnae values('0133-4/09',null,null);
insert into layout_cnae values('0133-4/10',null,null);
insert into layout_cnae values('0133-4/11',null,null);
insert into layout_cnae values('0133-4/99',null,null);
insert into layout_cnae values('0134-2/00',null,null);
insert into layout_cnae values('0135-1/00',null,null);
insert into layout_cnae values('0139-3/01',null,null);
insert into layout_cnae values('0139-3/02',null,null);
insert into layout_cnae values('0139-3/03',null,null);
insert into layout_cnae values('0139-3/04',null,null);
insert into layout_cnae values('0139-3/05',null,null);
insert into layout_cnae values('0139-3/06',null,null);
insert into layout_cnae values('0139-3/99',null,null);
insert into layout_cnae values('0141-5/01',null,null);
insert into layout_cnae values('0141-5/02',null,null);
insert into layout_cnae values('0142-3/00',null,null);
insert into layout_cnae values('0151-2/01',null,null);
insert into layout_cnae values('0151-2/02',null,null);
insert into layout_cnae values('0151-2/03',null,null);
insert into layout_cnae values('0152-1/01',null,null);
insert into layout_cnae values('0152-1/02',null,null);
insert into layout_cnae values('0152-1/03',null,null);
insert into layout_cnae values('0153-9/01',null,null);
insert into layout_cnae values('0153-9/02',null,null);
insert into layout_cnae values('0154-7/00',null,null);
insert into layout_cnae values('0155-5/01',null,null);
insert into layout_cnae values('0155-5/02',null,null);
insert into layout_cnae values('0155-5/03',null,null);
insert into layout_cnae values('0155-5/04',null,null);
insert into layout_cnae values('0155-5/05',null,null);
insert into layout_cnae values('0159-8/01',null,null);
insert into layout_cnae values('0159-8/02',null,null);
insert into layout_cnae values('0159-8/03',null,null);
insert into layout_cnae values('0159-8/04',null,null);
insert into layout_cnae values('0159-8/99',null,null);
insert into layout_cnae values('0161-0/01',null,null);
insert into layout_cnae values('0161-0/02',null,null);
insert into layout_cnae values('0161-0/03',null,null);
insert into layout_cnae values('0161-0/99',null,null);
insert into layout_cnae values('0162-8/01',null,null);
insert into layout_cnae values('0162-8/02',null,null);
insert into layout_cnae values('0162-8/03',null,null);
insert into layout_cnae values('0162-8/99',null,null);
insert into layout_cnae values('0163-6/00',null,null);
insert into layout_cnae values('0170-9/00',null,null);
insert into layout_cnae values('0210-1/01',null,null);
insert into layout_cnae values('0210-1/02',null,null);
insert into layout_cnae values('0210-1/03',null,null);
insert into layout_cnae values('0210-1/04',null,null);
insert into layout_cnae values('0210-1/05',null,null);
insert into layout_cnae values('0210-1/06',null,null);
insert into layout_cnae values('0210-1/07',null,null);
insert into layout_cnae values('0210-1/08',null,null);
insert into layout_cnae values('0210-1/09',null,null);
insert into layout_cnae values('0210-1/99',null,null);
insert into layout_cnae values('0220-9/01',null,null);
insert into layout_cnae values('0220-9/02',null,null);
insert into layout_cnae values('0220-9/03',null,null);
insert into layout_cnae values('0220-9/04',null,null);
insert into layout_cnae values('0220-9/05',null,null);
insert into layout_cnae values('0220-9/06',null,null);
insert into layout_cnae values('0220-9/99',null,null);
insert into layout_cnae values('0230-6/00',null,null);
insert into layout_cnae values('0311-6/01',null,null);
insert into layout_cnae values('0311-6/02',null,null);
insert into layout_cnae values('0311-6/03',null,null);
insert into layout_cnae values('0311-6/04',null,null);
insert into layout_cnae values('0312-4/01',null,null);
insert into layout_cnae values('0312-4/02',null,null);
insert into layout_cnae values('0312-4/03',null,null);
insert into layout_cnae values('0312-4/04',null,null);
insert into layout_cnae values('0321-3/01',null,null);
insert into layout_cnae values('0321-3/02',null,null);
insert into layout_cnae values('0321-3/03',null,null);
insert into layout_cnae values('0321-3/04',null,null);
insert into layout_cnae values('0321-3/05',null,null);
insert into layout_cnae values('0321-3/99',null,null);
insert into layout_cnae values('0322-1/01',null,null);
insert into layout_cnae values('0322-1/02',null,null);
insert into layout_cnae values('0322-1/03',null,null);
insert into layout_cnae values('0322-1/04',null,null);
insert into layout_cnae values('0322-1/05',null,null);
insert into layout_cnae values('0322-1/06',null,null);
insert into layout_cnae values('0322-1/07',null,null);
insert into layout_cnae values('0322-1/99',null,null);
insert into layout_cnae values('0500-3/01',null,null);
insert into layout_cnae values('0500-3/02',null,null);
insert into layout_cnae values('0600-0/01',null,null);
insert into layout_cnae values('0600-0/02',null,null);
insert into layout_cnae values('0600-0/03',null,null);
insert into layout_cnae values('0710-3/01',null,null);
insert into layout_cnae values('0710-3/02',null,null);
insert into layout_cnae values('0721-9/01',null,null);
insert into layout_cnae values('0721-9/02',null,null);
insert into layout_cnae values('0722-7/01',null,null);
insert into layout_cnae values('0722-7/02',null,null);
insert into layout_cnae values('0723-5/01',null,null);
insert into layout_cnae values('0723-5/02',null,null);
insert into layout_cnae values('0724-3/01',null,null);
insert into layout_cnae values('0724-3/02',null,null);
insert into layout_cnae values('0725-1/00',null,null);
insert into layout_cnae values('0729-4/01',null,null);
insert into layout_cnae values('0729-4/02',null,null);
insert into layout_cnae values('0729-4/03',null,null);
insert into layout_cnae values('0729-4/04',null,null);
insert into layout_cnae values('0729-4/05',null,null);
insert into layout_cnae values('0810-0/01',null,null);
insert into layout_cnae values('0810-0/02',null,null);
insert into layout_cnae values('0810-0/03',null,null);
insert into layout_cnae values('0810-0/04',null,null);
insert into layout_cnae values('0810-0/05',null,null);
insert into layout_cnae values('0810-0/06',null,null);
insert into layout_cnae values('0810-0/07',null,null);
insert into layout_cnae values('0810-0/08',null,null);
insert into layout_cnae values('0810-0/09',null,null);
insert into layout_cnae values('0810-0/10',null,null);
insert into layout_cnae values('0810-0/99',null,null);
insert into layout_cnae values('0891-6/00',null,null);
insert into layout_cnae values('0892-4/01',null,null);
insert into layout_cnae values('0892-4/02',null,null);
insert into layout_cnae values('0892-4/03',null,null);
insert into layout_cnae values('0893-2/00',null,null);
insert into layout_cnae values('0899-1/01',null,null);
insert into layout_cnae values('0899-1/02',null,null);
insert into layout_cnae values('0899-1/03',null,null);
insert into layout_cnae values('0899-1/99',null,null);
insert into layout_cnae values('0910-6/00',null,null);
insert into layout_cnae values('0990-4/01',null,null);
insert into layout_cnae values('0990-4/02',null,null);
insert into layout_cnae values('0990-4/03',null,null);
insert into layout_cnae values('1011-2/01',null,null);
insert into layout_cnae values('1011-2/02',null,null);
insert into layout_cnae values('1011-2/03',null,null);
insert into layout_cnae values('1011-2/04',null,null);
insert into layout_cnae values('1011-2/05',null,null);
insert into layout_cnae values('1012-1/01',null,null);
insert into layout_cnae values('1012-1/02',null,null);
insert into layout_cnae values('1012-1/03',null,null);
insert into layout_cnae values('1012-1/04',null,null);
insert into layout_cnae values('1013-9/01',null,null);
insert into layout_cnae values('1013-9/02',null,null);
insert into layout_cnae values('1020-1/01',null,null);
insert into layout_cnae values('1020-1/02',null,null);
insert into layout_cnae values('1031-7/00',null,null);
insert into layout_cnae values('1032-5/01',null,null);
insert into layout_cnae values('1032-5/99',null,null);
insert into layout_cnae values('1033-3/01',null,null);
insert into layout_cnae values('1033-3/02',null,null);
insert into layout_cnae values('1041-4/00',null,null);
insert into layout_cnae values('1042-2/00',null,null);
insert into layout_cnae values('1043-1/00',null,null);
insert into layout_cnae values('1051-1/00',null,null);
insert into layout_cnae values('1052-0/00',null,null);
insert into layout_cnae values('1053-8/00',null,null);
insert into layout_cnae values('1061-9/01',null,null);
insert into layout_cnae values('1061-9/02',null,null);
insert into layout_cnae values('1062-7/00',null,null);
insert into layout_cnae values('1063-5/00',null,null);
insert into layout_cnae values('1064-3/00',null,null);
insert into layout_cnae values('1065-1/01',null,null);
insert into layout_cnae values('1065-1/02',null,null);
insert into layout_cnae values('1065-1/03',null,null);
insert into layout_cnae values('1066-0/00',null,null);
insert into layout_cnae values('1069-4/00',null,null);
insert into layout_cnae values('1071-6/00',null,null);
insert into layout_cnae values('1072-4/01',null,null);
insert into layout_cnae values('1072-4/02',null,null);
insert into layout_cnae values('1081-3/01',null,null);
insert into layout_cnae values('1081-3/02',null,null);
insert into layout_cnae values('1082-1/00',null,null);
insert into layout_cnae values('1091-1/01',null,null);
insert into layout_cnae values('1091-1/02',null,null);
insert into layout_cnae values('1092-9/00',null,null);
insert into layout_cnae values('1093-7/01',null,null);
insert into layout_cnae values('1093-7/02',null,null);
insert into layout_cnae values('1094-5/00',null,null);
insert into layout_cnae values('1095-3/00',null,null);
insert into layout_cnae values('1096-1/00',null,null);
insert into layout_cnae values('1099-6/01',null,null);
insert into layout_cnae values('1099-6/02',null,null);
insert into layout_cnae values('1099-6/03',null,null);
insert into layout_cnae values('1099-6/04',null,null);
insert into layout_cnae values('1099-6/05',null,null);
insert into layout_cnae values('1099-6/06',null,null);
insert into layout_cnae values('1099-6/07',null,null);
insert into layout_cnae values('1099-6/99',null,null);
insert into layout_cnae values('1111-9/01',null,null);
insert into layout_cnae values('1111-9/02',null,null);
insert into layout_cnae values('1112-7/00',null,null);
insert into layout_cnae values('1113-5/01',null,null);
insert into layout_cnae values('1113-5/02',null,null);
insert into layout_cnae values('1121-6/00',null,null);
insert into layout_cnae values('1122-4/01',null,null);
insert into layout_cnae values('1122-4/02',null,null);
insert into layout_cnae values('1122-4/03',null,null);
insert into layout_cnae values('1122-4/04',null,null);
insert into layout_cnae values('1122-4/99',null,null);
insert into layout_cnae values('1210-7/00',null,null);
insert into layout_cnae values('1220-4/01',null,null);
insert into layout_cnae values('1220-4/02',null,null);
insert into layout_cnae values('1220-4/03',null,null);
insert into layout_cnae values('1220-4/99',null,null);
insert into layout_cnae values('1311-1/00',null,null);
insert into layout_cnae values('1312-0/00',null,null);
insert into layout_cnae values('1313-8/00',null,null);
insert into layout_cnae values('1314-6/00',null,null);
insert into layout_cnae values('1321-9/00',null,null);
insert into layout_cnae values('1322-7/00',null,null);
insert into layout_cnae values('1323-5/00',null,null);
insert into layout_cnae values('1330-8/00',null,null);
insert into layout_cnae values('1340-5/01',null,null);
insert into layout_cnae values('1340-5/02',null,null);
insert into layout_cnae values('1340-5/99',null,null);
insert into layout_cnae values('1351-1/00',null,null);
insert into layout_cnae values('1352-9/00',null,null);
insert into layout_cnae values('1353-7/00',null,null);
insert into layout_cnae values('1354-5/00',null,null);
insert into layout_cnae values('1359-6/00',null,null);
insert into layout_cnae values('1411-8/01',null,null);
insert into layout_cnae values('1411-8/02',null,null);
insert into layout_cnae values('1412-6/01',null,null);
insert into layout_cnae values('1412-6/02',null,null);
insert into layout_cnae values('1412-6/03',null,null);
insert into layout_cnae values('1413-4/01',null,null);
insert into layout_cnae values('1413-4/02',null,null);
insert into layout_cnae values('1413-4/03',null,null);
insert into layout_cnae values('1414-2/00',null,null);
insert into layout_cnae values('1421-5/00',null,null);
insert into layout_cnae values('1422-3/00',null,null);
insert into layout_cnae values('1510-6/00',null,null);
insert into layout_cnae values('1521-1/00',null,null);
insert into layout_cnae values('1529-7/00',null,null);
insert into layout_cnae values('1531-9/01',null,null);
insert into layout_cnae values('1531-9/02',null,null);
insert into layout_cnae values('1532-7/00',null,null);
insert into layout_cnae values('1533-5/00',null,null);
insert into layout_cnae values('1539-4/00',null,null);
insert into layout_cnae values('1540-8/00',null,null);
insert into layout_cnae values('1610-2/03','*','Produção de madeira bruta desdobrada ou serrada em bruto');
insert into layout_cnae values('1610-2/04','*','Produção de madeira resserrada');
insert into layout_cnae values('1610-2/05','*','Serviços de tratamento de madeira bruta desdobrada ou serrada em bruto realizada sob contrato');
insert into layout_cnae values('1610-2/05','*','Serviços de tratamento de madeira resserrada realizada sob contrato');
insert into layout_cnae values('1621-8/00',null,null);
insert into layout_cnae values('1622-6/01',null,null);
insert into layout_cnae values('1622-6/02',null,null);
insert into layout_cnae values('1622-6/99',null,null);
insert into layout_cnae values('1623-4/00',null,null);
insert into layout_cnae values('1629-3/01',null,null);
insert into layout_cnae values('1629-3/02',null,null);
insert into layout_cnae values('1710-9/00',null,null);
insert into layout_cnae values('1721-4/00',null,null);
insert into layout_cnae values('1722-2/00',null,null);
insert into layout_cnae values('1731-1/00',null,null);
insert into layout_cnae values('1732-0/00',null,null);
insert into layout_cnae values('1733-8/00',null,null);
insert into layout_cnae values('1741-9/01',null,null);
insert into layout_cnae values('1741-9/02',null,null);
insert into layout_cnae values('1742-7/01',null,null);
insert into layout_cnae values('1742-7/02',null,null);
insert into layout_cnae values('1742-7/99',null,null);
insert into layout_cnae values('1749-4/00',null,null);
insert into layout_cnae values('1811-3/01',null,null);
insert into layout_cnae values('1811-3/02',null,null);
insert into layout_cnae values('1812-1/00',null,null);
insert into layout_cnae values('1813-0/01',null,null);
insert into layout_cnae values('1813-0/99',null,null);
insert into layout_cnae values('1821-1/00',null,null);
insert into layout_cnae values('1822-9/00','*','Serviços de acabamentos gráficos');
insert into layout_cnae values('1822-9/01',null,null);
insert into layout_cnae values('1822-9/99','*','Serviços de acabamentos gráficos, exceto encadernação e plastificação');
insert into layout_cnae values('1830-0/01',null,null);
insert into layout_cnae values('1830-0/02',null,null);
insert into layout_cnae values('1830-0/03',null,null);
insert into layout_cnae values('1910-1/00',null,null);
insert into layout_cnae values('1921-7/00',null,null);
insert into layout_cnae values('1922-5/01',null,null);
insert into layout_cnae values('1922-5/02',null,null);
insert into layout_cnae values('1922-5/99',null,null);
insert into layout_cnae values('1931-4/00',null,null);
insert into layout_cnae values('1932-2/00',null,null);
insert into layout_cnae values('2011-8/00',null,null);
insert into layout_cnae values('2012-6/00',null,null);
insert into layout_cnae values('2013-4/01',null,null);
insert into layout_cnae values('2013-4/02',null,null);
insert into layout_cnae values('2014-2/00',null,null);
insert into layout_cnae values('2019-3/01',null,null);
insert into layout_cnae values('2019-3/99',null,null);
insert into layout_cnae values('2021-5/00',null,null);
insert into layout_cnae values('2022-3/00',null,null);
insert into layout_cnae values('2029-1/00',null,null);
insert into layout_cnae values('2031-2/00',null,null);
insert into layout_cnae values('2032-1/00',null,null);
insert into layout_cnae values('2033-9/00',null,null);
insert into layout_cnae values('2040-1/00',null,null);
insert into layout_cnae values('2051-7/00',null,null);
insert into layout_cnae values('2052-5/00',null,null);
insert into layout_cnae values('2061-4/00',null,null);
insert into layout_cnae values('2062-2/00',null,null);
insert into layout_cnae values('2063-1/00',null,null);
insert into layout_cnae values('2071-1/00',null,null);
insert into layout_cnae values('2072-0/00',null,null);
insert into layout_cnae values('2073-8/00',null,null);
insert into layout_cnae values('2091-6/00',null,null);
insert into layout_cnae values('2092-4/01',null,null);
insert into layout_cnae values('2092-4/02',null,null);
insert into layout_cnae values('2092-4/03',null,null);
insert into layout_cnae values('2093-2/00',null,null);
insert into layout_cnae values('2094-1/00',null,null);
insert into layout_cnae values('2099-1/01',null,null);
insert into layout_cnae values('2099-1/99',null,null);
insert into layout_cnae values('2110-6/00',null,null);
insert into layout_cnae values('2121-1/01',null,null);
insert into layout_cnae values('2121-1/02',null,null);
insert into layout_cnae values('2121-1/03',null,null);
insert into layout_cnae values('2122-0/00',null,null);
insert into layout_cnae values('2123-8/00',null,null);
insert into layout_cnae values('2211-1/00',null,null);
insert into layout_cnae values('2212-9/00',null,null);
insert into layout_cnae values('2219-6/00',null,null);
insert into layout_cnae values('2221-8/00',null,null);
insert into layout_cnae values('2222-6/00',null,null);
insert into layout_cnae values('2223-4/00',null,null);
insert into layout_cnae values('2229-3/01',null,null);
insert into layout_cnae values('2229-3/02',null,null);
insert into layout_cnae values('2229-3/03',null,null);
insert into layout_cnae values('2229-3/99',null,null);
insert into layout_cnae values('2311-7/00',null,null);
insert into layout_cnae values('2312-5/00',null,null);
insert into layout_cnae values('2319-2/00',null,null);
insert into layout_cnae values('2320-6/00',null,null);
insert into layout_cnae values('2330-3/01',null,null);
insert into layout_cnae values('2330-3/02',null,null);
insert into layout_cnae values('2330-3/03',null,null);
insert into layout_cnae values('2330-3/04',null,null);
insert into layout_cnae values('2330-3/05',null,null);
insert into layout_cnae values('2330-3/99',null,null);
insert into layout_cnae values('2341-9/00',null,null);
insert into layout_cnae values('2342-7/01',null,null);
insert into layout_cnae values('2342-7/02',null,null);
insert into layout_cnae values('2349-4/01',null,null);
insert into layout_cnae values('2349-4/99',null,null);
insert into layout_cnae values('2391-5/01',null,null);
insert into layout_cnae values('2391-5/02',null,null);
insert into layout_cnae values('2391-5/03',null,null);
insert into layout_cnae values('2392-3/00',null,null);
insert into layout_cnae values('2399-1/01',null,null);
insert into layout_cnae values('2399-1/02',null,null);
insert into layout_cnae values('2399-1/99',null,null);
insert into layout_cnae values('2411-3/00',null,null);
insert into layout_cnae values('2412-1/00',null,null);
insert into layout_cnae values('2421-1/00',null,null);
insert into layout_cnae values('2422-9/01',null,null);
insert into layout_cnae values('2422-9/02',null,null);
insert into layout_cnae values('2423-7/01',null,null);
insert into layout_cnae values('2423-7/02',null,null);
insert into layout_cnae values('2424-5/01',null,null);
insert into layout_cnae values('2424-5/02',null,null);
insert into layout_cnae values('2431-8/00',null,null);
insert into layout_cnae values('2439-3/00',null,null);
insert into layout_cnae values('2441-5/01',null,null);
insert into layout_cnae values('2441-5/02',null,null);
insert into layout_cnae values('2442-3/00',null,null);
insert into layout_cnae values('2443-1/00',null,null);
insert into layout_cnae values('2449-1/01',null,null);
insert into layout_cnae values('2449-1/02',null,null);
insert into layout_cnae values('2449-1/03',null,null);
insert into layout_cnae values('2449-1/99',null,null);
insert into layout_cnae values('2451-2/00',null,null);
insert into layout_cnae values('2452-1/00',null,null);
insert into layout_cnae values('2511-0/00',null,null);
insert into layout_cnae values('2512-8/00',null,null);
insert into layout_cnae values('2513-6/00',null,null);
insert into layout_cnae values('2521-7/00',null,null);
insert into layout_cnae values('2522-5/00',null,null);
insert into layout_cnae values('2531-4/01',null,null);
insert into layout_cnae values('2531-4/02',null,null);
insert into layout_cnae values('2532-2/01',null,null);
insert into layout_cnae values('2532-2/02',null,null);
insert into layout_cnae values('2539-0/01',null,null);
insert into layout_cnae values('2539-0/02',null,null);
insert into layout_cnae values('2541-1/00',null,null);
insert into layout_cnae values('2542-0/00',null,null);
insert into layout_cnae values('2543-8/00',null,null);
insert into layout_cnae values('2550-1/01',null,null);
insert into layout_cnae values('2550-1/02',null,null);
insert into layout_cnae values('2591-8/00',null,null);
insert into layout_cnae values('2592-6/01',null,null);
insert into layout_cnae values('2592-6/02',null,null);
insert into layout_cnae values('2593-4/00',null,null);
insert into layout_cnae values('2599-3/01',null,null);
insert into layout_cnae values('2599-3/02','*','Serviço de corte e dobra de metais');
insert into layout_cnae values('2599-3/99',null,null);
insert into layout_cnae values('2610-8/00',null,null);
insert into layout_cnae values('2621-3/00',null,null);
insert into layout_cnae values('2622-1/00',null,null);
insert into layout_cnae values('2631-1/00',null,null);
insert into layout_cnae values('2632-9/00',null,null);
insert into layout_cnae values('2640-0/00',null,null);
insert into layout_cnae values('2651-5/00',null,null);
insert into layout_cnae values('2652-3/00',null,null);
insert into layout_cnae values('2660-4/00',null,null);
insert into layout_cnae values('2670-1/01',null,null);
insert into layout_cnae values('2670-1/02',null,null);
insert into layout_cnae values('2680-9/00',null,null);
insert into layout_cnae values('2710-4/01',null,null);
insert into layout_cnae values('2710-4/02',null,null);
insert into layout_cnae values('2710-4/03',null,null);
insert into layout_cnae values('2721-0/00',null,null);
insert into layout_cnae values('2722-8/01',null,null);
insert into layout_cnae values('2722-8/02',null,null);
insert into layout_cnae values('2731-7/00',null,null);
insert into layout_cnae values('2732-5/00',null,null);
insert into layout_cnae values('2733-3/00',null,null);
insert into layout_cnae values('2740-6/01',null,null);
insert into layout_cnae values('2740-6/02',null,null);
insert into layout_cnae values('2751-1/00',null,null);
insert into layout_cnae values('2759-7/01',null,null);
insert into layout_cnae values('2759-7/99',null,null);
insert into layout_cnae values('2790-2/01',null,null);
insert into layout_cnae values('2790-2/02',null,null);
insert into layout_cnae values('2790-2/99',null,null);
insert into layout_cnae values('2811-9/00',null,null);
insert into layout_cnae values('2812-7/00',null,null);
insert into layout_cnae values('2813-5/00',null,null);
insert into layout_cnae values('2814-3/01',null,null);
insert into layout_cnae values('2814-3/02',null,null);
insert into layout_cnae values('2815-1/01',null,null);
insert into layout_cnae values('2815-1/02',null,null);
insert into layout_cnae values('2821-6/01',null,null);
insert into layout_cnae values('2821-6/02',null,null);
insert into layout_cnae values('2822-4/01',null,null);
insert into layout_cnae values('2822-4/02',null,null);
insert into layout_cnae values('2823-2/00',null,null);
insert into layout_cnae values('2824-1/01',null,null);
insert into layout_cnae values('2824-1/02',null,null);
insert into layout_cnae values('2825-9/00',null,null);
insert into layout_cnae values('2829-1/01',null,null);
insert into layout_cnae values('2829-1/99',null,null);
insert into layout_cnae values('2831-3/00',null,null);
insert into layout_cnae values('2832-1/00',null,null);
insert into layout_cnae values('2833-0/00',null,null);
insert into layout_cnae values('2840-2/00',null,null);
insert into layout_cnae values('2851-8/00',null,null);
insert into layout_cnae values('2852-6/00',null,null);
insert into layout_cnae values('2853-4/00',null,null);
insert into layout_cnae values('2854-2/00',null,null);
insert into layout_cnae values('2861-5/00',null,null);
insert into layout_cnae values('2862-3/00',null,null);
insert into layout_cnae values('2863-1/00',null,null);
insert into layout_cnae values('2864-0/00',null,null);
insert into layout_cnae values('2865-8/00',null,null);
insert into layout_cnae values('2866-6/00',null,null);
insert into layout_cnae values('2869-1/00',null,null);
insert into layout_cnae values('2910-7/01',null,null);
insert into layout_cnae values('2910-7/02',null,null);
insert into layout_cnae values('2910-7/03',null,null);
insert into layout_cnae values('2920-4/01',null,null);
insert into layout_cnae values('2920-4/02',null,null);
insert into layout_cnae values('2930-1/01',null,null);
insert into layout_cnae values('2930-1/02',null,null);
insert into layout_cnae values('2930-1/03',null,null);
insert into layout_cnae values('2941-7/00',null,null);
insert into layout_cnae values('2942-5/00',null,null);
insert into layout_cnae values('2943-3/00',null,null);
insert into layout_cnae values('2944-1/00',null,null);
insert into layout_cnae values('2945-0/00',null,null);
insert into layout_cnae values('2949-2/01',null,null);
insert into layout_cnae values('2949-2/99',null,null);
insert into layout_cnae values('2950-6/00',null,null);
insert into layout_cnae values('3011-3/01',null,null);
insert into layout_cnae values('3011-3/02',null,null);
insert into layout_cnae values('3012-1/00',null,null);
insert into layout_cnae values('3031-8/00',null,null);
insert into layout_cnae values('3032-6/00',null,null);
insert into layout_cnae values('3041-5/00',null,null);
insert into layout_cnae values('3042-3/00',null,null);
insert into layout_cnae values('3050-4/00',null,null);
insert into layout_cnae values('3091-1/01',null,null);
insert into layout_cnae values('3091-1/02',null,null);
insert into layout_cnae values('3092-0/00',null,null);
insert into layout_cnae values('3099-7/00',null,null);
insert into layout_cnae values('3101-2/00',null,null);
insert into layout_cnae values('3102-1/00',null,null);
insert into layout_cnae values('3103-9/00',null,null);
insert into layout_cnae values('3104-7/00',null,null);
insert into layout_cnae values('3211-6/01',null,null);
insert into layout_cnae values('3211-6/02',null,null);
insert into layout_cnae values('3211-6/03',null,null);
insert into layout_cnae values('3212-4/00',null,null);
insert into layout_cnae values('3220-5/00',null,null);
insert into layout_cnae values('3230-2/00',null,null);
insert into layout_cnae values('3240-0/01',null,null);
insert into layout_cnae values('3240-0/02',null,null);
insert into layout_cnae values('3240-0/03',null,null);
insert into layout_cnae values('3240-0/99',null,null);
insert into layout_cnae values('3250-7/01',null,null);
insert into layout_cnae values('3250-7/02',null,null);
insert into layout_cnae values('3250-7/03',null,null);
insert into layout_cnae values('3250-7/04',null,null);
insert into layout_cnae values('3250-7/05',null,null);
insert into layout_cnae values('3250-7/06',null,null);
insert into layout_cnae values('3250-7/07',null,null);
insert into layout_cnae values('3250-7/09',null,null);
insert into layout_cnae values('3291-4/00',null,null);
insert into layout_cnae values('3292-2/01',null,null);
insert into layout_cnae values('3292-2/02',null,null);
insert into layout_cnae values('3299-0/01',null,null);
insert into layout_cnae values('3299-0/02',null,null);
insert into layout_cnae values('3299-0/03',null,null);
insert into layout_cnae values('3299-0/04',null,null);
insert into layout_cnae values('3299-0/05',null,null);
insert into layout_cnae values('3299-0/06',null,null);
insert into layout_cnae values('3299-0/99',null,null);
insert into layout_cnae values('3311-2/00',null,null);
insert into layout_cnae values('3312-1/02',null,null);
insert into layout_cnae values('3312-1/03',null,null);
insert into layout_cnae values('3312-1/04',null,null);
insert into layout_cnae values('3313-9/01',null,null);
insert into layout_cnae values('3313-9/02',null,null);
insert into layout_cnae values('3313-9/99',null,null);
insert into layout_cnae values('3314-7/01',null,null);
insert into layout_cnae values('3314-7/02',null,null);
insert into layout_cnae values('3314-7/03',null,null);
insert into layout_cnae values('3314-7/04',null,null);
insert into layout_cnae values('3314-7/05',null,null);
insert into layout_cnae values('3314-7/06',null,null);
insert into layout_cnae values('3314-7/07',null,null);
insert into layout_cnae values('3314-7/08',null,null);
insert into layout_cnae values('3314-7/09',null,null);
insert into layout_cnae values('3314-7/10',null,null);
insert into layout_cnae values('3314-7/11',null,null);
insert into layout_cnae values('3314-7/12',null,null);
insert into layout_cnae values('3314-7/13',null,null);
insert into layout_cnae values('3314-7/14',null,null);
insert into layout_cnae values('3314-7/15',null,null);
insert into layout_cnae values('3314-7/16',null,null);
insert into layout_cnae values('3314-7/17',null,null);
insert into layout_cnae values('3314-7/18',null,null);
insert into layout_cnae values('3314-7/19',null,null);
insert into layout_cnae values('3314-7/20',null,null);
insert into layout_cnae values('3314-7/21',null,null);
insert into layout_cnae values('3314-7/22',null,null);
insert into layout_cnae values('3314-7/99',null,null);
insert into layout_cnae values('3315-5/00',null,null);
insert into layout_cnae values('3316-3/01',null,null);
insert into layout_cnae values('3316-3/02',null,null);
insert into layout_cnae values('3317-1/01',null,null);
insert into layout_cnae values('3317-1/02',null,null);
insert into layout_cnae values('3319-8/00',null,null);
insert into layout_cnae values('3321-0/00',null,null);
insert into layout_cnae values('3329-5/01',null,null);
insert into layout_cnae values('3329-5/99',null,null);
insert into layout_cnae values('3511-5/01',null,null);
insert into layout_cnae values('3511-5/02',null,null);
insert into layout_cnae values('3512-3/00',null,null);
insert into layout_cnae values('3513-1/00',null,null);
insert into layout_cnae values('3514-0/00',null,null);
insert into layout_cnae values('3520-4/01',null,null);
insert into layout_cnae values('3520-4/02',null,null);
insert into layout_cnae values('3530-1/00',null,null);
insert into layout_cnae values('3600-6/01',null,null);
insert into layout_cnae values('3600-6/02',null,null);
insert into layout_cnae values('3701-1/00',null,null);
insert into layout_cnae values('3702-9/00',null,null);
insert into layout_cnae values('3811-4/00',null,null);
insert into layout_cnae values('3812-2/00',null,null);
insert into layout_cnae values('3821-1/00',null,null);
insert into layout_cnae values('3822-0/00',null,null);
insert into layout_cnae values('3831-9/01',null,null);
insert into layout_cnae values('3831-9/99',null,null);
insert into layout_cnae values('3832-7/00',null,null);
insert into layout_cnae values('3839-4/01',null,null);
insert into layout_cnae values('3839-4/99',null,null);
insert into layout_cnae values('3900-5/00',null,null);
insert into layout_cnae values('4110-7/00',null,null);
insert into layout_cnae values('4120-4/00',null,null);
insert into layout_cnae values('4211-1/01',null,null);
insert into layout_cnae values('4211-1/02',null,null);
insert into layout_cnae values('4212-0/00',null,null);
insert into layout_cnae values('4213-8/00',null,null);
insert into layout_cnae values('4221-9/01',null,null);
insert into layout_cnae values('4221-9/02',null,null);
insert into layout_cnae values('4221-9/03',null,null);
insert into layout_cnae values('4221-9/04',null,null);
insert into layout_cnae values('4221-9/05',null,null);
insert into layout_cnae values('4222-7/01',null,null);
insert into layout_cnae values('4222-7/02',null,null);
insert into layout_cnae values('4223-5/00',null,null);
insert into layout_cnae values('4291-0/00',null,null);
insert into layout_cnae values('4292-8/01',null,null);
insert into layout_cnae values('4292-8/02',null,null);
insert into layout_cnae values('4299-5/01',null,null);
insert into layout_cnae values('4299-5/99',null,null);
insert into layout_cnae values('4311-8/01',null,null);
insert into layout_cnae values('4311-8/02',null,null);
insert into layout_cnae values('4312-6/00',null,null);
insert into layout_cnae values('4313-4/00',null,null);
insert into layout_cnae values('4319-3/00',null,null);
insert into layout_cnae values('4321-5/00',null,null);
insert into layout_cnae values('4322-3/01',null,null);
insert into layout_cnae values('4322-3/02',null,null);
insert into layout_cnae values('4322-3/03',null,null);
insert into layout_cnae values('4329-1/01',null,null);
insert into layout_cnae values('4329-1/02',null,null);
insert into layout_cnae values('4329-1/03',null,null);
insert into layout_cnae values('4329-1/04',null,null);
insert into layout_cnae values('4329-1/05',null,null);
insert into layout_cnae values('4329-1/99',null,null);
insert into layout_cnae values('4330-4/01',null,null);
insert into layout_cnae values('4330-4/02',null,null);
insert into layout_cnae values('4330-4/03',null,null);
insert into layout_cnae values('4330-4/04',null,null);
insert into layout_cnae values('4330-4/05',null,null);
insert into layout_cnae values('4330-4/99',null,null);
insert into layout_cnae values('4391-6/00',null,null);
insert into layout_cnae values('4399-1/01',null,null);
insert into layout_cnae values('4399-1/02',null,null);
insert into layout_cnae values('4399-1/03',null,null);
insert into layout_cnae values('4399-1/04',null,null);
insert into layout_cnae values('4399-1/05',null,null);
insert into layout_cnae values('4399-1/99',null,null);
insert into layout_cnae values('4511-1/01',null,null);
insert into layout_cnae values('4511-1/02',null,null);
insert into layout_cnae values('4511-1/03',null,null);
insert into layout_cnae values('4511-1/04',null,null);
insert into layout_cnae values('4511-1/05',null,null);
insert into layout_cnae values('4511-1/06',null,null);
insert into layout_cnae values('4512-9/01',null,null);
insert into layout_cnae values('4512-9/02',null,null);
insert into layout_cnae values('4520-0/01',null,null);
insert into layout_cnae values('4520-0/02',null,null);
insert into layout_cnae values('4520-0/03',null,null);
insert into layout_cnae values('4520-0/04',null,null);
insert into layout_cnae values('4520-0/05',null,null);
insert into layout_cnae values('4520-0/06',null,null);
insert into layout_cnae values('4520-0/07',null,null);
insert into layout_cnae values('4520-0/08',null,null);
insert into layout_cnae values('4530-7/01',null,null);
insert into layout_cnae values('4530-7/02',null,null);
insert into layout_cnae values('4530-7/03',null,null);
insert into layout_cnae values('4530-7/04',null,null);
insert into layout_cnae values('4530-7/05',null,null);
insert into layout_cnae values('4530-7/06',null,null);
insert into layout_cnae values('4541-2/01',null,null);
insert into layout_cnae values('4541-2/02',null,null);
insert into layout_cnae values('4541-2/03',null,null);
insert into layout_cnae values('4541-2/04',null,null);
insert into layout_cnae values('4541-2/06','*','Comércio a varejo de peças e acessórios novos para motocicletas e motonetas');
insert into layout_cnae values('4541-2/07','*','Comércio a varejo de peças e acessórios usados para motocicletas e motonetas');
insert into layout_cnae values('4542-1/01',null,null);
insert into layout_cnae values('4542-1/02',null,null);
insert into layout_cnae values('4543-9/00',null,null);
insert into layout_cnae values('4611-7/00',null,null);
insert into layout_cnae values('4612-5/00',null,null);
insert into layout_cnae values('4613-3/00',null,null);
insert into layout_cnae values('4614-1/00',null,null);
insert into layout_cnae values('4615-0/00',null,null);
insert into layout_cnae values('4616-8/00',null,null);
insert into layout_cnae values('4617-6/00',null,null);
insert into layout_cnae values('4618-4/01',null,null);
insert into layout_cnae values('4618-4/02',null,null);
insert into layout_cnae values('4618-4/03',null,null);
insert into layout_cnae values('4618-4/99',null,null);
insert into layout_cnae values('4619-2/00',null,null);
insert into layout_cnae values('4621-4/00',null,null);
insert into layout_cnae values('4622-2/00',null,null);
insert into layout_cnae values('4623-1/01',null,null);
insert into layout_cnae values('4623-1/02',null,null);
insert into layout_cnae values('4623-1/03',null,null);
insert into layout_cnae values('4623-1/04',null,null);
insert into layout_cnae values('4623-1/05',null,null);
insert into layout_cnae values('4623-1/06',null,null);
insert into layout_cnae values('4623-1/07',null,null);
insert into layout_cnae values('4623-1/08',null,null);
insert into layout_cnae values('4623-1/09',null,null);
insert into layout_cnae values('4623-1/99',null,null);
insert into layout_cnae values('4631-1/00',null,null);
insert into layout_cnae values('4632-0/01',null,null);
insert into layout_cnae values('4632-0/02',null,null);
insert into layout_cnae values('4632-0/03',null,null);
insert into layout_cnae values('4633-8/01',null,null);
insert into layout_cnae values('4633-8/02',null,null);
insert into layout_cnae values('4633-8/03',null,null);
insert into layout_cnae values('4634-6/01',null,null);
insert into layout_cnae values('4634-6/02',null,null);
insert into layout_cnae values('4634-6/03',null,null);
insert into layout_cnae values('4634-6/99',null,null);
insert into layout_cnae values('4635-4/01',null,null);
insert into layout_cnae values('4635-4/02',null,null);
insert into layout_cnae values('4635-4/03',null,null);
insert into layout_cnae values('4635-4/99',null,null);
insert into layout_cnae values('4636-2/01',null,null);
insert into layout_cnae values('4636-2/02',null,null);
insert into layout_cnae values('4637-1/01',null,null);
insert into layout_cnae values('4637-1/02',null,null);
insert into layout_cnae values('4637-1/03',null,null);
insert into layout_cnae values('4637-1/04',null,null);
insert into layout_cnae values('4637-1/05',null,null);
insert into layout_cnae values('4637-1/06',null,null);
insert into layout_cnae values('4637-1/07',null,null);
insert into layout_cnae values('4637-1/99',null,null);
insert into layout_cnae values('4639-7/01',null,null);
insert into layout_cnae values('4639-7/02',null,null);
insert into layout_cnae values('4641-9/01',null,null);
insert into layout_cnae values('4641-9/02',null,null);
insert into layout_cnae values('4641-9/03',null,null);
insert into layout_cnae values('4642-7/01',null,null);
insert into layout_cnae values('4642-7/02',null,null);
insert into layout_cnae values('4643-5/01',null,null);
insert into layout_cnae values('4643-5/02',null,null);
insert into layout_cnae values('4644-3/01',null,null);
insert into layout_cnae values('4644-3/02',null,null);
insert into layout_cnae values('4645-1/01',null,null);
insert into layout_cnae values('4645-1/02',null,null);
insert into layout_cnae values('4645-1/03',null,null);
insert into layout_cnae values('4646-0/01',null,null);
insert into layout_cnae values('4646-0/02',null,null);
insert into layout_cnae values('4647-8/01',null,null);
insert into layout_cnae values('4647-8/02',null,null);
insert into layout_cnae values('4649-4/01',null,null);
insert into layout_cnae values('4649-4/02',null,null);
insert into layout_cnae values('4649-4/03',null,null);
insert into layout_cnae values('4649-4/04',null,null);
insert into layout_cnae values('4649-4/05',null,null);
insert into layout_cnae values('4649-4/06',null,null);
insert into layout_cnae values('4649-4/07',null,null);
insert into layout_cnae values('4649-4/08',null,null);
insert into layout_cnae values('4649-4/09',null,null);
insert into layout_cnae values('4649-4/10',null,null);
insert into layout_cnae values('4649-4/99',null,null);
insert into layout_cnae values('4651-6/01',null,null);
insert into layout_cnae values('4651-6/02',null,null);
insert into layout_cnae values('4652-4/00',null,null);
insert into layout_cnae values('4661-3/00',null,null);
insert into layout_cnae values('4662-1/00',null,null);
insert into layout_cnae values('4663-0/00',null,null);
insert into layout_cnae values('4664-8/00',null,null);
insert into layout_cnae values('4665-6/00',null,null);
insert into layout_cnae values('4669-9/01',null,null);
insert into layout_cnae values('4669-9/99',null,null);
insert into layout_cnae values('4671-1/00',null,null);
insert into layout_cnae values('4672-9/00',null,null);
insert into layout_cnae values('4673-7/00',null,null);
insert into layout_cnae values('4674-5/00',null,null);
insert into layout_cnae values('4679-6/01',null,null);
insert into layout_cnae values('4679-6/02',null,null);
insert into layout_cnae values('4679-6/03',null,null);
insert into layout_cnae values('4679-6/04',null,null);
insert into layout_cnae values('4679-6/99',null,null);
insert into layout_cnae values('4681-8/01',null,null);
insert into layout_cnae values('4681-8/02',null,null);
insert into layout_cnae values('4681-8/03',null,null);
insert into layout_cnae values('4681-8/04',null,null);
insert into layout_cnae values('4681-8/05',null,null);
insert into layout_cnae values('4682-6/00',null,null);
insert into layout_cnae values('4683-4/00',null,null);
insert into layout_cnae values('4684-2/01',null,null);
insert into layout_cnae values('4684-2/02',null,null);
insert into layout_cnae values('4684-2/99',null,null);
insert into layout_cnae values('4685-1/00',null,null);
insert into layout_cnae values('4686-9/01',null,null);
insert into layout_cnae values('4686-9/02',null,null);
insert into layout_cnae values('4687-7/01',null,null);
insert into layout_cnae values('4687-7/02',null,null);
insert into layout_cnae values('4687-7/03',null,null);
insert into layout_cnae values('4689-3/01',null,null);
insert into layout_cnae values('4689-3/02',null,null);
insert into layout_cnae values('4689-3/99',null,null);
insert into layout_cnae values('4691-5/00',null,null);
insert into layout_cnae values('4692-3/00',null,null);
insert into layout_cnae values('4693-1/00',null,null);
insert into layout_cnae values('4711-3/01',null,null);
insert into layout_cnae values('4711-3/02',null,null);
insert into layout_cnae values('4712-1/00',null,null);
insert into layout_cnae values('4713-0/02',null,null);
insert into layout_cnae values('4713-0/04','*','Lojas de departamentos ou magazines, exceto lojas francas (Duty free)');
insert into layout_cnae values('4713-0/05','*','Lojas francas (Duty Free) de portos e em fronteiras terrestres');
insert into layout_cnae values('4721-1/02',null,null);
insert into layout_cnae values('4721-1/03',null,null);
insert into layout_cnae values('4721-1/04',null,null);
insert into layout_cnae values('4722-9/01',null,null);
insert into layout_cnae values('4722-9/02',null,null);
insert into layout_cnae values('4723-7/00',null,null);
insert into layout_cnae values('4724-5/00',null,null);
insert into layout_cnae values('4729-6/01',null,null);
insert into layout_cnae values('4729-6/02',null,null);
insert into layout_cnae values('4729-6/99',null,null);
insert into layout_cnae values('4731-8/00',null,null);
insert into layout_cnae values('4732-6/00',null,null);
insert into layout_cnae values('4741-5/00',null,null);
insert into layout_cnae values('4742-3/00',null,null);
insert into layout_cnae values('4743-1/00',null,null);
insert into layout_cnae values('4744-0/01',null,null);
insert into layout_cnae values('4744-0/02',null,null);
insert into layout_cnae values('4744-0/03',null,null);
insert into layout_cnae values('4744-0/04',null,null);
insert into layout_cnae values('4744-0/05',null,null);
insert into layout_cnae values('4744-0/06',null,null);
insert into layout_cnae values('4744-0/99',null,null);
insert into layout_cnae values('4751-2/01',null,null);
insert into layout_cnae values('4751-2/02',null,null);
insert into layout_cnae values('4752-1/00',null,null);
insert into layout_cnae values('4753-9/00',null,null);
insert into layout_cnae values('4754-7/01',null,null);
insert into layout_cnae values('4754-7/02',null,null);
insert into layout_cnae values('4754-7/03',null,null);
insert into layout_cnae values('4755-5/01',null,null);
insert into layout_cnae values('4755-5/02',null,null);
insert into layout_cnae values('4755-5/03',null,null);
insert into layout_cnae values('4756-3/00',null,null);
insert into layout_cnae values('4757-1/00',null,null);
insert into layout_cnae values('4759-8/01',null,null);
insert into layout_cnae values('4759-8/99',null,null);
insert into layout_cnae values('4761-0/01',null,null);
insert into layout_cnae values('4761-0/02',null,null);
insert into layout_cnae values('4761-0/03',null,null);
insert into layout_cnae values('4762-8/00',null,null);
insert into layout_cnae values('4763-6/01',null,null);
insert into layout_cnae values('4763-6/02',null,null);
insert into layout_cnae values('4763-6/03',null,null);
insert into layout_cnae values('4763-6/04',null,null);
insert into layout_cnae values('4763-6/05',null,null);
insert into layout_cnae values('4771-7/01',null,null);
insert into layout_cnae values('4771-7/02',null,null);
insert into layout_cnae values('4771-7/03',null,null);
insert into layout_cnae values('4771-7/04',null,null);
insert into layout_cnae values('4772-5/00',null,null);
insert into layout_cnae values('4773-3/00',null,null);
insert into layout_cnae values('4774-1/00',null,null);
insert into layout_cnae values('4781-4/00',null,null);
insert into layout_cnae values('4782-2/01',null,null);
insert into layout_cnae values('4782-2/02',null,null);
insert into layout_cnae values('4783-1/01',null,null);
insert into layout_cnae values('4783-1/02',null,null);
insert into layout_cnae values('4784-9/00',null,null);
insert into layout_cnae values('4785-7/01',null,null);
insert into layout_cnae values('4785-7/99',null,null);
insert into layout_cnae values('4789-0/01',null,null);
insert into layout_cnae values('4789-0/02',null,null);
insert into layout_cnae values('4789-0/03',null,null);
insert into layout_cnae values('4789-0/04',null,null);
insert into layout_cnae values('4789-0/05',null,null);
insert into layout_cnae values('4789-0/06',null,null);
insert into layout_cnae values('4789-0/07',null,null);
insert into layout_cnae values('4789-0/08',null,null);
insert into layout_cnae values('4789-0/09',null,null);
insert into layout_cnae values('4789-0/99',null,null);
insert into layout_cnae values('4911-6/00',null,null);
insert into layout_cnae values('4912-4/01',null,null);
insert into layout_cnae values('4912-4/02',null,null);
insert into layout_cnae values('4912-4/03',null,null);
insert into layout_cnae values('4921-3/01',null,null);
insert into layout_cnae values('4921-3/02',null,null);
insert into layout_cnae values('4922-1/01',null,null);
insert into layout_cnae values('4922-1/02',null,null);
insert into layout_cnae values('4922-1/03',null,null);
insert into layout_cnae values('4923-0/01',null,null);
insert into layout_cnae values('4923-0/02',null,null);
insert into layout_cnae values('4924-8/00',null,null);
insert into layout_cnae values('4929-9/01',null,null);
insert into layout_cnae values('4929-9/02',null,null);
insert into layout_cnae values('4929-9/03',null,null);
insert into layout_cnae values('4929-9/04',null,null);
insert into layout_cnae values('4929-9/99',null,null);
insert into layout_cnae values('4930-2/01',null,null);
insert into layout_cnae values('4930-2/02',null,null);
insert into layout_cnae values('4930-2/03',null,null);
insert into layout_cnae values('4930-2/04',null,null);
insert into layout_cnae values('4940-0/00',null,null);
insert into layout_cnae values('4950-7/00',null,null);
insert into layout_cnae values('5011-4/01',null,null);
insert into layout_cnae values('5011-4/02',null,null);
insert into layout_cnae values('5012-2/01',null,null);
insert into layout_cnae values('5012-2/02',null,null);
insert into layout_cnae values('5021-1/01',null,null);
insert into layout_cnae values('5021-1/02',null,null);
insert into layout_cnae values('5022-0/01',null,null);
insert into layout_cnae values('5022-0/02',null,null);
insert into layout_cnae values('5030-1/01',null,null);
insert into layout_cnae values('5030-1/02',null,null);
insert into layout_cnae values('5030-1/03',null,null);
insert into layout_cnae values('5091-2/01',null,null);
insert into layout_cnae values('5091-2/02',null,null);
insert into layout_cnae values('5099-8/01',null,null);
insert into layout_cnae values('5099-8/99',null,null);
insert into layout_cnae values('5111-1/00',null,null);
insert into layout_cnae values('5112-9/01',null,null);
insert into layout_cnae values('5112-9/99',null,null);
insert into layout_cnae values('5120-0/00',null,null);
insert into layout_cnae values('5130-7/00',null,null);
insert into layout_cnae values('5211-7/01',null,null);
insert into layout_cnae values('5211-7/02',null,null);
insert into layout_cnae values('5211-7/99',null,null);
insert into layout_cnae values('5212-5/00',null,null);
insert into layout_cnae values('5221-4/00',null,null);
insert into layout_cnae values('5222-2/00',null,null);
insert into layout_cnae values('5223-1/00',null,null);
insert into layout_cnae values('5229-0/01',null,null);
insert into layout_cnae values('5229-0/02',null,null);
insert into layout_cnae values('5229-0/99',null,null);
insert into layout_cnae values('5231-1/01',null,null);
insert into layout_cnae values('5231-1/02',null,null);
insert into layout_cnae values('5231-1/03',null,null);
insert into layout_cnae values('5232-0/00',null,null);
insert into layout_cnae values('5239-7/01',null,null);
insert into layout_cnae values('5239-7/99',null,null);
insert into layout_cnae values('5240-1/01',null,null);
insert into layout_cnae values('5240-1/99',null,null);
insert into layout_cnae values('5250-8/01',null,null);
insert into layout_cnae values('5250-8/02',null,null);
insert into layout_cnae values('5250-8/03',null,null);
insert into layout_cnae values('5250-8/04',null,null);
insert into layout_cnae values('5250-8/05',null,null);
insert into layout_cnae values('5310-5/01',null,null);
insert into layout_cnae values('5310-5/02',null,null);
insert into layout_cnae values('5320-2/01',null,null);
insert into layout_cnae values('5320-2/02',null,null);
insert into layout_cnae values('5510-8/01',null,null);
insert into layout_cnae values('5510-8/02',null,null);
insert into layout_cnae values('5510-8/03',null,null);
insert into layout_cnae values('5590-6/01',null,null);
insert into layout_cnae values('5590-6/02',null,null);
insert into layout_cnae values('5590-6/03',null,null);
insert into layout_cnae values('5590-6/99',null,null);
insert into layout_cnae values('5611-2/01',null,null);
insert into layout_cnae values('5611-2/03',null,null);
insert into layout_cnae values('5611-2/04','*','Bares e outros estabelecimentos especializados em servir bebidas, sem entretenimento');
insert into layout_cnae values('5611-2/05','*','Bares e outros estabelecimentos especializados em servir bebidas, com entretenimento');
insert into layout_cnae values('5612-1/00',null,null);
insert into layout_cnae values('5620-1/01',null,null);
insert into layout_cnae values('5620-1/02',null,null);
insert into layout_cnae values('5620-1/03',null,null);
insert into layout_cnae values('5620-1/04',null,null);
insert into layout_cnae values('5811-5/00',null,null);
insert into layout_cnae values('5812-3/01',null,null);
insert into layout_cnae values('5812-3/02',null,null);
insert into layout_cnae values('5813-1/00',null,null);
insert into layout_cnae values('5819-1/00',null,null);
insert into layout_cnae values('5821-2/00',null,null);
insert into layout_cnae values('5822-1/01',null,null);
insert into layout_cnae values('5822-1/02',null,null);
insert into layout_cnae values('5823-9/00',null,null);
insert into layout_cnae values('5829-8/00',null,null);
insert into layout_cnae values('5911-1/01',null,null);
insert into layout_cnae values('5911-1/02',null,null);
insert into layout_cnae values('5911-1/99',null,null);
insert into layout_cnae values('5912-0/01',null,null);
insert into layout_cnae values('5912-0/02',null,null);
insert into layout_cnae values('5912-0/99',null,null);
insert into layout_cnae values('5913-8/00',null,null);
insert into layout_cnae values('5914-6/00',null,null);
insert into layout_cnae values('5920-1/00',null,null);
insert into layout_cnae values('6010-1/00',null,null);
insert into layout_cnae values('6021-7/00',null,null);
insert into layout_cnae values('6022-5/01',null,null);
insert into layout_cnae values('6022-5/02',null,null);
insert into layout_cnae values('6110-8/01',null,null);
insert into layout_cnae values('6110-8/02',null,null);
insert into layout_cnae values('6110-8/03',null,null);
insert into layout_cnae values('6110-8/99',null,null);
insert into layout_cnae values('6120-5/01',null,null);
insert into layout_cnae values('6120-5/02',null,null);
insert into layout_cnae values('6120-5/99',null,null);
insert into layout_cnae values('6130-2/00',null,null);
insert into layout_cnae values('6141-8/00',null,null);
insert into layout_cnae values('6142-6/00',null,null);
insert into layout_cnae values('6143-4/00',null,null);
insert into layout_cnae values('6190-6/01',null,null);
insert into layout_cnae values('6190-6/02',null,null);
insert into layout_cnae values('6190-6/99',null,null);
insert into layout_cnae values('6201-5/01',null,null);
insert into layout_cnae values('6201-5/02',null,null);
insert into layout_cnae values('6202-3/00',null,null);
insert into layout_cnae values('6203-1/00',null,null);
insert into layout_cnae values('6204-0/00',null,null);
insert into layout_cnae values('6209-1/00',null,null);
insert into layout_cnae values('6311-9/00',null,null);
insert into layout_cnae values('6319-4/00',null,null);
insert into layout_cnae values('6391-7/00',null,null);
insert into layout_cnae values('6399-2/00',null,null);
insert into layout_cnae values('6410-7/00',null,null);
insert into layout_cnae values('6421-2/00',null,null);
insert into layout_cnae values('6422-1/00',null,null);
insert into layout_cnae values('6423-9/00',null,null);
insert into layout_cnae values('6424-7/01',null,null);
insert into layout_cnae values('6424-7/02',null,null);
insert into layout_cnae values('6424-7/03',null,null);
insert into layout_cnae values('6424-7/04',null,null);
insert into layout_cnae values('6431-0/00',null,null);
insert into layout_cnae values('6432-8/00',null,null);
insert into layout_cnae values('6433-6/00',null,null);
insert into layout_cnae values('6434-4/00',null,null);
insert into layout_cnae values('6435-2/01',null,null);
insert into layout_cnae values('6435-2/02',null,null);
insert into layout_cnae values('6435-2/03',null,null);
insert into layout_cnae values('6436-1/00',null,null);
insert into layout_cnae values('6437-9/00',null,null);
insert into layout_cnae values('6438-7/01',null,null);
insert into layout_cnae values('6438-7/99',null,null);
insert into layout_cnae values('6440-9/00',null,null);
insert into layout_cnae values('6450-6/00',null,null);
insert into layout_cnae values('6461-1/00',null,null);
insert into layout_cnae values('6462-0/00',null,null);
insert into layout_cnae values('6463-8/00',null,null);
insert into layout_cnae values('6470-1/01',null,null);
insert into layout_cnae values('6470-1/02',null,null);
insert into layout_cnae values('6470-1/03',null,null);
insert into layout_cnae values('6491-3/00',null,null);
insert into layout_cnae values('6492-1/00',null,null);
insert into layout_cnae values('6493-0/00',null,null);
insert into layout_cnae values('6499-9/01',null,null);
insert into layout_cnae values('6499-9/02',null,null);
insert into layout_cnae values('6499-9/03',null,null);
insert into layout_cnae values('6499-9/04',null,null);
insert into layout_cnae values('6499-9/05',null,null);
insert into layout_cnae values('6499-9/99',null,null);
insert into layout_cnae values('6511-1/01',null,null);
insert into layout_cnae values('6511-1/02',null,null);
insert into layout_cnae values('6512-0/00',null,null);
insert into layout_cnae values('6520-1/00',null,null);
insert into layout_cnae values('6530-8/00',null,null);
insert into layout_cnae values('6541-3/00',null,null);
insert into layout_cnae values('6542-1/00',null,null);
insert into layout_cnae values('6550-2/00',null,null);
insert into layout_cnae values('6611-8/01',null,null);
insert into layout_cnae values('6611-8/02',null,null);
insert into layout_cnae values('6611-8/03',null,null);
insert into layout_cnae values('6611-8/04',null,null);
insert into layout_cnae values('6612-6/01',null,null);
insert into layout_cnae values('6612-6/02',null,null);
insert into layout_cnae values('6612-6/03',null,null);
insert into layout_cnae values('6612-6/04',null,null);
insert into layout_cnae values('6612-6/05',null,null);
insert into layout_cnae values('6613-4/00',null,null);
insert into layout_cnae values('6619-3/01',null,null);
insert into layout_cnae values('6619-3/02',null,null);
insert into layout_cnae values('6619-3/03',null,null);
insert into layout_cnae values('6619-3/04',null,null);
insert into layout_cnae values('6619-3/05',null,null);
insert into layout_cnae values('6619-3/99',null,null);
insert into layout_cnae values('6621-5/01',null,null);
insert into layout_cnae values('6621-5/02',null,null);
insert into layout_cnae values('6622-3/00',null,null);
insert into layout_cnae values('6629-1/00',null,null);
insert into layout_cnae values('6630-4/00',null,null);
insert into layout_cnae values('6810-2/01',null,null);
insert into layout_cnae values('6810-2/02',null,null);
insert into layout_cnae values('6810-2/03',null,null);
insert into layout_cnae values('6821-8/01',null,null);
insert into layout_cnae values('6821-8/02',null,null);
insert into layout_cnae values('6822-6/00',null,null);
insert into layout_cnae values('6911-7/01',null,null);
insert into layout_cnae values('6911-7/02',null,null);
insert into layout_cnae values('6911-7/03',null,null);
insert into layout_cnae values('6912-5/00',null,null);
insert into layout_cnae values('6920-6/01',null,null);
insert into layout_cnae values('6920-6/02',null,null);
insert into layout_cnae values('7020-4/00',null,null);
insert into layout_cnae values('7111-1/00',null,null);
insert into layout_cnae values('7112-0/00',null,null);
insert into layout_cnae values('7119-7/01',null,null);
insert into layout_cnae values('7119-7/02',null,null);
insert into layout_cnae values('7119-7/03',null,null);
insert into layout_cnae values('7119-7/04',null,null);
insert into layout_cnae values('7119-7/99',null,null);
insert into layout_cnae values('7120-1/00',null,null);
insert into layout_cnae values('7210-0/00',null,null);
insert into layout_cnae values('7220-7/00',null,null);
insert into layout_cnae values('7311-4/00',null,null);
insert into layout_cnae values('7312-2/00',null,null);
insert into layout_cnae values('7319-0/01',null,null);
insert into layout_cnae values('7319-0/02',null,null);
insert into layout_cnae values('7319-0/03',null,null);
insert into layout_cnae values('7319-0/04',null,null);
insert into layout_cnae values('7319-0/99',null,null);
insert into layout_cnae values('7320-3/00',null,null);
insert into layout_cnae values('7410-2/02',null,null);
insert into layout_cnae values('7410-2/03',null,null);
insert into layout_cnae values('7410-2/99',null,null);
insert into layout_cnae values('7420-0/01',null,null);
insert into layout_cnae values('7420-0/02',null,null);
insert into layout_cnae values('7420-0/03',null,null);
insert into layout_cnae values('7420-0/04',null,null);
insert into layout_cnae values('7420-0/05',null,null);
insert into layout_cnae values('7490-1/01',null,null);
insert into layout_cnae values('7490-1/02',null,null);
insert into layout_cnae values('7490-1/03',null,null);
insert into layout_cnae values('7490-1/04',null,null);
insert into layout_cnae values('7490-1/05',null,null);
insert into layout_cnae values('7490-1/99',null,null);
insert into layout_cnae values('7500-1/00',null,null);
insert into layout_cnae values('7711-0/00',null,null);
insert into layout_cnae values('7719-5/01',null,null);
insert into layout_cnae values('7719-5/02',null,null);
insert into layout_cnae values('7719-5/99',null,null);
insert into layout_cnae values('7721-7/00',null,null);
insert into layout_cnae values('7722-5/00',null,null);
insert into layout_cnae values('7723-3/00',null,null);
insert into layout_cnae values('7729-2/01',null,null);
insert into layout_cnae values('7729-2/02',null,null);
insert into layout_cnae values('7729-2/03',null,null);
insert into layout_cnae values('7729-2/99',null,null);
insert into layout_cnae values('7731-4/00',null,null);
insert into layout_cnae values('7732-2/01',null,null);
insert into layout_cnae values('7732-2/02',null,null);
insert into layout_cnae values('7733-1/00',null,null);
insert into layout_cnae values('7739-0/01',null,null);
insert into layout_cnae values('7739-0/02',null,null);
insert into layout_cnae values('7739-0/03',null,null);
insert into layout_cnae values('7739-0/99',null,null);
insert into layout_cnae values('7740-3/00',null,null);
insert into layout_cnae values('7810-8/00',null,null);
insert into layout_cnae values('7820-5/00',null,null);
insert into layout_cnae values('7830-2/00',null,null);
insert into layout_cnae values('7911-2/00',null,null);
insert into layout_cnae values('7912-1/00',null,null);
insert into layout_cnae values('7990-2/00',null,null);
insert into layout_cnae values('8011-1/01',null,null);
insert into layout_cnae values('8011-1/02',null,null);
insert into layout_cnae values('8012-9/00',null,null);
insert into layout_cnae values('8020-0/01',null,null);
insert into layout_cnae values('8020-0/02',null,null);
insert into layout_cnae values('8030-7/00',null,null);
insert into layout_cnae values('8111-7/00',null,null);
insert into layout_cnae values('8112-5/00',null,null);
insert into layout_cnae values('8121-4/00',null,null);
insert into layout_cnae values('8122-2/00',null,null);
insert into layout_cnae values('8129-0/00',null,null);
insert into layout_cnae values('8130-3/00',null,null);
insert into layout_cnae values('8211-3/00',null,null);
insert into layout_cnae values('8219-9/01',null,null);
insert into layout_cnae values('8219-9/99',null,null);
insert into layout_cnae values('8220-2/00',null,null);
insert into layout_cnae values('8230-0/01',null,null);
insert into layout_cnae values('8230-0/02',null,null);
insert into layout_cnae values('8291-1/00',null,null);
insert into layout_cnae values('8292-0/00',null,null);
insert into layout_cnae values('8299-7/01',null,null);
insert into layout_cnae values('8299-7/02',null,null);
insert into layout_cnae values('8299-7/03',null,null);
insert into layout_cnae values('8299-7/04',null,null);
insert into layout_cnae values('8299-7/05',null,null);
insert into layout_cnae values('8299-7/06',null,null);
insert into layout_cnae values('8299-7/07',null,null);
insert into layout_cnae values('8299-7/99',null,null);
insert into layout_cnae values('8411-6/00',null,null);
insert into layout_cnae values('8412-4/00',null,null);
insert into layout_cnae values('8413-2/00',null,null);
insert into layout_cnae values('8421-3/00',null,null);
insert into layout_cnae values('8422-1/00',null,null);
insert into layout_cnae values('8423-0/00',null,null);
insert into layout_cnae values('8424-8/00',null,null);
insert into layout_cnae values('8425-6/00',null,null);
insert into layout_cnae values('8511-2/00',null,null);
insert into layout_cnae values('8512-1/00',null,null);
insert into layout_cnae values('8513-9/00',null,null);
insert into layout_cnae values('8520-1/00',null,null);
insert into layout_cnae values('8531-7/00',null,null);
insert into layout_cnae values('8532-5/00',null,null);
insert into layout_cnae values('8533-3/00',null,null);
insert into layout_cnae values('8541-4/00',null,null);
insert into layout_cnae values('8542-2/00',null,null);
insert into layout_cnae values('8550-3/01',null,null);
insert into layout_cnae values('8550-3/02',null,null);
insert into layout_cnae values('8591-1/00',null,null);
insert into layout_cnae values('8592-9/01',null,null);
insert into layout_cnae values('8592-9/02',null,null);
insert into layout_cnae values('8592-9/03',null,null);
insert into layout_cnae values('8592-9/99',null,null);
insert into layout_cnae values('8593-7/00',null,null);
insert into layout_cnae values('8599-6/01',null,null);
insert into layout_cnae values('8599-6/02',null,null);
insert into layout_cnae values('8599-6/03',null,null);
insert into layout_cnae values('8599-6/04',null,null);
insert into layout_cnae values('8599-6/05',null,null);
insert into layout_cnae values('8599-6/99',null,null);
insert into layout_cnae values('8610-1/01',null,null);
insert into layout_cnae values('8610-1/02',null,null);
insert into layout_cnae values('8621-6/01',null,null);
insert into layout_cnae values('8621-6/02',null,null);
insert into layout_cnae values('8622-4/00',null,null);
insert into layout_cnae values('8630-5/01',null,null);
insert into layout_cnae values('8630-5/02',null,null);
insert into layout_cnae values('8630-5/03',null,null);
insert into layout_cnae values('8630-5/04',null,null);
insert into layout_cnae values('8630-5/06',null,null);
insert into layout_cnae values('8630-5/07',null,null);
insert into layout_cnae values('8630-5/99',null,null);
insert into layout_cnae values('8640-2/01',null,null);
insert into layout_cnae values('8640-2/02',null,null);
insert into layout_cnae values('8640-2/03',null,null);
insert into layout_cnae values('8640-2/04',null,null);
insert into layout_cnae values('8640-2/05',null,null);
insert into layout_cnae values('8640-2/06',null,null);
insert into layout_cnae values('8640-2/07',null,null);
insert into layout_cnae values('8640-2/08',null,null);
insert into layout_cnae values('8640-2/09',null,null);
insert into layout_cnae values('8640-2/10',null,null);
insert into layout_cnae values('8640-2/11',null,null);
insert into layout_cnae values('8640-2/12',null,null);
insert into layout_cnae values('8640-2/13',null,null);
insert into layout_cnae values('8640-2/14',null,null);
insert into layout_cnae values('8640-2/99',null,null);
insert into layout_cnae values('8650-0/01',null,null);
insert into layout_cnae values('8650-0/02',null,null);
insert into layout_cnae values('8650-0/03',null,null);
insert into layout_cnae values('8650-0/04',null,null);
insert into layout_cnae values('8650-0/05',null,null);
insert into layout_cnae values('8650-0/06',null,null);
insert into layout_cnae values('8650-0/07',null,null);
insert into layout_cnae values('8650-0/99',null,null);
insert into layout_cnae values('8660-7/00',null,null);
insert into layout_cnae values('8690-9/01',null,null);
insert into layout_cnae values('8690-9/02',null,null);
insert into layout_cnae values('8690-9/03',null,null);
insert into layout_cnae values('8690-9/04',null,null);
insert into layout_cnae values('8690-9/99',null,null);
insert into layout_cnae values('8711-5/01',null,null);
insert into layout_cnae values('8711-5/02',null,null);
insert into layout_cnae values('8711-5/03',null,null);
insert into layout_cnae values('8711-5/04',null,null);
insert into layout_cnae values('8711-5/05',null,null);
insert into layout_cnae values('8712-3/00',null,null);
insert into layout_cnae values('8720-4/01',null,null);
insert into layout_cnae values('8720-4/99',null,'Alteração de denominação do código');
insert into layout_cnae values('8730-1/01',null,null);
insert into layout_cnae values('8730-1/02',null,null);
insert into layout_cnae values('8730-1/99',null,null);
insert into layout_cnae values('8800-6/00',null,null);
insert into layout_cnae values('9001-9/01',null,null);
insert into layout_cnae values('9001-9/02',null,null);
insert into layout_cnae values('9001-9/03',null,null);
insert into layout_cnae values('9001-9/04',null,null);
insert into layout_cnae values('9001-9/05',null,null);
insert into layout_cnae values('9001-9/06',null,null);
insert into layout_cnae values('9001-9/99',null,null);
insert into layout_cnae values('9002-7/01',null,null);
insert into layout_cnae values('9002-7/02',null,null);
insert into layout_cnae values('9003-5/00',null,null);
insert into layout_cnae values('9101-5/00',null,null);
insert into layout_cnae values('9102-3/01',null,null);
insert into layout_cnae values('9102-3/02',null,null);
insert into layout_cnae values('9103-1/00',null,null);
insert into layout_cnae values('9200-3/01',null,null);
insert into layout_cnae values('9200-3/02',null,null);
insert into layout_cnae values('9200-3/99',null,null);
insert into layout_cnae values('9311-5/00',null,null);
insert into layout_cnae values('9312-3/00',null,null);
insert into layout_cnae values('9313-1/00',null,null);
insert into layout_cnae values('9319-1/01',null,null);
insert into layout_cnae values('9319-1/99',null,null);
insert into layout_cnae values('9321-2/00',null,null);
insert into layout_cnae values('9329-8/01',null,null);
insert into layout_cnae values('9329-8/02',null,null);
insert into layout_cnae values('9329-8/03',null,null);
insert into layout_cnae values('9329-8/04',null,null);
insert into layout_cnae values('9329-8/99',null,null);
insert into layout_cnae values('9411-1/00',null,null);
insert into layout_cnae values('9412-0/01',null,null);
insert into layout_cnae values('9412-0/99',null,null);
insert into layout_cnae values('9420-1/00',null,null);
insert into layout_cnae values('9430-8/00',null,null);
insert into layout_cnae values('9491-0/00',null,null);
insert into layout_cnae values('9492-8/00',null,null);
insert into layout_cnae values('9493-6/00',null,null);
insert into layout_cnae values('9499-5/00',null,null);
insert into layout_cnae values('9511-8/00',null,null);
insert into layout_cnae values('9512-6/00',null,null);
insert into layout_cnae values('9521-5/00',null,null);
insert into layout_cnae values('9529-1/01',null,null);
insert into layout_cnae values('9529-1/02',null,null);
insert into layout_cnae values('9529-1/03',null,null);
insert into layout_cnae values('9529-1/04',null,null);
insert into layout_cnae values('9529-1/05',null,null);
insert into layout_cnae values('9529-1/06',null,null);
insert into layout_cnae values('9529-1/99',null,null);
insert into layout_cnae values('9601-7/01',null,null);
insert into layout_cnae values('9601-7/02',null,null);
insert into layout_cnae values('9601-7/03',null,null);
insert into layout_cnae values('9602-5/01',null,null);
insert into layout_cnae values('9602-5/02',null,null);
insert into layout_cnae values('9603-3/01',null,null);
insert into layout_cnae values('9603-3/02',null,null);
insert into layout_cnae values('9603-3/03',null,null);
insert into layout_cnae values('9603-3/04',null,null);
insert into layout_cnae values('9603-3/05',null,null);
insert into layout_cnae values('9603-3/99',null,null);
insert into layout_cnae values('9609-2/01','*','Clinicas de estetica e similares');
insert into layout_cnae values('9609-2/02',null,null);
insert into layout_cnae values('9609-2/04',null,null);
insert into layout_cnae values('9609-2/05',null,null);
insert into layout_cnae values('9609-2/06',null,null);
insert into layout_cnae values('9609-2/07',null,null);
insert into layout_cnae values('9609-2/08',null,null);
insert into layout_cnae values('9609-2/99',null,null);
insert into layout_cnae values('9700-5/00',null,null);
insert into layout_cnae values('9900-8/00',null,null);

update tabela_cnae set situacao = 0
where cnae not in(select cnae from layout_cnae);

insert into tabela_cnae
select nextval('seq_gem'), -- cd_cnae int8 NOT NULL,
       cnae,               -- cnae varchar(10) NOT NULL,
       descricao,          -- ds_atividade varchar(200) NOT NULL,
       null,               -- version int8 NULL,
       substr(cnae, 1, 4), -- grupo varchar(5) NULL,
       1                   -- situacao int2 NOT NULL,
from layout_cnae
where not exists(select 1 from tabela_cnae
                 where tabela_cnae.cnae = layout_cnae.cnae);

drop table layout_cnae;
alter table tabela_cnae enable trigger user;

-- V11

/*
    Sulivan - 12/09/2019 - #27723
*/
-- TABLE: vigilancia_alto_baixo_risco
CREATE TABLE vigilancia_alto_baixo_risco (
    cd_vigilancia_alto_baixo_risco       INT8         NOT NULL,
    regiao_abastecida_agua               INT2         NOT NULL,
    regiao_coberta_rede_esgoto           INT2         NOT NULL,
    classificacao_risco                  INT2         NOT NULL,
    version                              INT8         NOT NULL
);

ALTER TABLE vigilancia_alto_baixo_risco
    ADD CONSTRAINT pk_req_vig_alto_baixo_risco PRIMARY KEY (cd_vigilancia_alto_baixo_risco);

CREATE TABLE AUDITSCHEMA.vigilancia_alto_baixo_risco AS SELECT T2.*, T1.* FROM vigilancia_alto_baixo_risco T1, AUDIT_TEMP T2 WHERE 1=2;
CREATE SEQUENCE SEQ_AUDIT_ID_VIGILANCIA_ALTO_BAIXO_RISCO;
ALTER TABLE AUDITSCHEMA.vigilancia_alto_baixo_risco ADD PRIMARY KEY (AUDIT_ID);
CREATE TRIGGER EMP_AUDIT AFTER INSERT OR UPDATE OR DELETE ON vigilancia_alto_baixo_risco FOR EACH ROW EXECUTE PROCEDURE PROCESS_EMP_AUDIT();

INSERT INTO vigilancia_alto_baixo_risco VALUES(nextval('seq_gem'), 1, 1, 1, 0);
INSERT INTO vigilancia_alto_baixo_risco VALUES(nextval('seq_gem'), 1, 0, 0, 0);
INSERT INTO vigilancia_alto_baixo_risco VALUES(nextval('seq_gem'), 0, 1, 0, 0);
INSERT INTO vigilancia_alto_baixo_risco VALUES(nextval('seq_gem'), 0, 0, 0, 0);

alter table requerimento_projeto_hidro add column cd_vigilancia_alto_baixo_risco INT8;
alter table auditschema.requerimento_projeto_hidro add column cd_vigilancia_alto_baixo_risco INT8;

alter table requerimento_projeto_hidro
   add constraint FK_REQ_PROJ_HIDR_REF_VIGI_ALT_BAI_RIS foreign key (cd_vigilancia_alto_baixo_risco)
      references vigilancia_alto_baixo_risco (cd_vigilancia_alto_baixo_risco);

alter table requerimento_vistoria_hidro add column cd_vigilancia_alto_baixo_risco INT8;
alter table auditschema.requerimento_vistoria_hidro add column cd_vigilancia_alto_baixo_risco INT8;

alter table requerimento_vistoria_hidro
   add constraint FK_REQ_VIST_HIDR_REF_VIGI_ALT_BAI_RIS foreign key (cd_vigilancia_alto_baixo_risco)
      references vigilancia_alto_baixo_risco (cd_vigilancia_alto_baixo_risco);

alter table requerimento_projeto_hidro disable trigger user;
update requerimento_projeto_hidro t1 set cd_vigilancia_alto_baixo_risco =
(select cd_vigilancia_alto_baixo_risco from vigilancia_alto_baixo_risco where regiao_abastecida_agua = t1.regiao_abastecida_agua and regiao_coberta_rede_esgoto = t1.regiao_coberta_rede_esgoto);
alter table requerimento_projeto_hidro enable trigger user;

alter table requerimento_vistoria_hidro disable trigger user;
update requerimento_vistoria_hidro t1 set cd_vigilancia_alto_baixo_risco =
(select cd_vigilancia_alto_baixo_risco from vigilancia_alto_baixo_risco where regiao_abastecida_agua = t1.regiao_abastecida_agua and regiao_coberta_rede_esgoto = t1.regiao_coberta_rede_esgoto);
alter table requerimento_vistoria_hidro enable trigger user;