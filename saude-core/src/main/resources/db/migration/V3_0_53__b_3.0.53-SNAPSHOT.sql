SET application_name = 'flyway|3.0.53';

/*
    Dropar fk na auditoria
*/
ALTER TABLE AUDITSCHEMA.ESTABELECIMENTO drop CONSTRAINT if exists FK_ESTABELECIMENTO_REF_VIGILANCIA_END;

/*
    Laudecir - 03/03/2016 - #11737
*/
alter table tipo_via_medicamento add column ordem int2;
alter table AUDITSCHEMA.tipo_via_medicamento add column ordem int2;

/*
    Wilson - 08/03/2016 - #11667
*/

UPDATE programa_pagina set cam_pagina = 'br.com.celk.view.unidadesaude.relatorio.RelatorioPacienteCondicaoSituacaoSaudePage' where cd_prg_pagina = 762;
UPDATE programa_web set ds_prg_web = 'Relatório de Pacientes por Condições/Situação de Saúde' where cd_prg_web = 430;
UPDATE menu_web set ds_menu = 'Relatório de Pacientes por Condições/Situação de Saúde', ds_bundle = null where cd_menu = 594;

/*
    Maicon - 08/03/2016 - #11635
*/

create table puerperio (
    cd_puerperio                    int8            not null,
    cd_prenatal                     int8            null,
    cd_usu_cadsus                   numeric(8)      not null,
    nr_atendimento                  int8            null,
    desfecho                        int2            null,
    dt_registro                     timestamp       null,
    tp_parto                        int2            null,
    dt_parto                        timestamp       null,
    idade_gestacional               int4            null,
    peso                            numeric(5,2)    null,
    local_ocorrencia                int2            null,
    estabelecimento                 varchar(100)    null,
    presenca_acompanhante           int2            null,
    febre                           int2            null,
    infeccao                        int2            null,
    hemorragia                      int2            null,
    problema_mamas                  int2            null,
    atendimento_domiciliar          int2            null,
    dt_cadastro                     timestamp       not null,
    cd_usuario                      numeric(6)      not null,
    version                         int8            not null
);

alter table puerperio add constraint pk_puerperio primary key (cd_puerperio);
alter table puerperio add constraint fk_puerperio_ref_prenatal foreign key (cd_prenatal) references prenatal(cd_prenatal);
alter table puerperio add constraint fk_puerperio_ref_atend foreign key (nr_atendimento) references atendimento(nr_atendimento);

CREATE TABLE auditschema.puerperio AS SELECT t2.*, t1.* FROM puerperio t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_puerperio;alter table auditschema.puerperio add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON puerperio FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();


/*
    Maicon - 08/03/2016 - #11422
*/

alter table usuario_cadsus add column tipo_sanguineo int2 null;
alter table auditschema.usuario_cadsus add column tipo_sanguineo int2 null;

/* Alterações PRE_NATAL */
alter table prenatal add column gravidez_planejada          int2 null;
alter table prenatal add column tp_gravidez                 int2 null;
alter table prenatal add column alcool                      int2 null;
alter table prenatal add column violencia_domestica         int2 null;
alter table prenatal add column outras_drogas               int2 null;
alter table prenatal add column qual_droga                  varchar(50) null;
alter table prenatal add column pes_doenca_mental           int2 null;
alter table prenatal add column pes_tromboembolismo         int2 null;
alter table prenatal add column antitetanica_imuni          int2 null;
alter table prenatal add column hepatite_b_imuni            int2 null;
alter table prenatal add column influenza_imuni             int2 null;
alter table prenatal add column dt_antite_reforco           date null;
alter table prenatal add column dt_hepatite_b_pri_dose      date null;
alter table prenatal add column dt_hepatite_b_seg_dose      date null;
alter table prenatal add column dt_hepatite_b_ter_dose      date null;
alter table prenatal add column dt_aplicacao_influenza      date null;
alter table prenatal add column participou_atvd_educativa   int2 null;
alter table prenatal add column dt_atvd_1                   date null;
alter table prenatal add column dt_atvd_2                   date null;
alter table prenatal add column dt_atvd_3                   date null;
alter table prenatal add column realizou_visita_maternidade int2 null;
alter table prenatal add column dt_visita_maternidade       date null;
alter table prenatal add column realizou_atend_odonto       int2 null;
alter table prenatal add column dt_atend_odonto             date null;
alter table prenatal add column aborto_molar                int4 null;
alter table prenatal add column aborto_ectopico             int4 null;
alter table prenatal add column aborto_espontaneo           int4 null;
alter table prenatal add column aborto_outros               int4 null;
alter table prenatal add column partos_forceps              int4 null;
alter table prenatal add column gestacao_anterior           int2 null;
alter table prenatal add column final_gestacao_ant_1_ano    int2 null;
alter table prenatal add column dt_ocorrencia               timestamp       null;

alter table prenatal add column hist_aids                   int2 null;
alter table prenatal add column hist_toxoplasmose           int2 null;
alter table prenatal add column hist_anemia                 int2 null;
alter table prenatal add column hist_isoimunizacao          int2 null;
alter table prenatal add column hist_oligo_polidramnio      int2 null;
alter table prenatal add column hist_ciur                   int2 null;
alter table prenatal add column hist_hipertensao_arterial   int2 null;
alter table prenatal add column hist_diabetes_gestacional   int2 null;
alter table prenatal add column hist_hemorragia_1_semestre  int2 null;
alter table prenatal add column hist_hemorragia_3_semestre  int2 null;
alter table prenatal add column hist_hemorragia_2_semestre  int2 null;
alter table prenatal add column hist_transfusao             int2 null;
alter table prenatal add column hist_sifilis                int2 null;
alter table prenatal add column hist_infeccao_urinaria      int2 null;
alter table prenatal add column hist_incompet_istmo_cervical int2 null;
alter table prenatal add column hist_ameaca_parto_prematuro int2 null;
alter table prenatal add column hist_rotura_prematura_membrana int2 null;
alter table prenatal add column hist_pos_datismo            int2 null;
alter table prenatal add column hist_eclampsia              int2 null;
alter table prenatal add column hist_uso_insulina           int2 null;

alter table auditschema.prenatal add column gravidez_planejada          int2 null;
alter table auditschema.prenatal add column tp_gravidez                 int2 null;
alter table auditschema.prenatal add column alcool                      int2 null;
alter table auditschema.prenatal add column violencia_domestica         int2 null;
alter table auditschema.prenatal add column outras_drogas               int2 null;
alter table auditschema.prenatal add column qual_droga                  varchar(50) null;
alter table auditschema.prenatal add column pes_doenca_mental           int2 null;
alter table auditschema.prenatal add column pes_tromboembolismo         int2 null;
alter table auditschema.prenatal add column antitetanica_imuni          int2 null;
alter table auditschema.prenatal add column hepatite_b_imuni            int2 null;
alter table auditschema.prenatal add column influenza_imuni             int2 null;
alter table auditschema.prenatal add column dt_antite_reforco           date null;
alter table auditschema.prenatal add column dt_hepatite_b_pri_dose      date null;
alter table auditschema.prenatal add column dt_hepatite_b_seg_dose      date null;
alter table auditschema.prenatal add column dt_hepatite_b_ter_dose      date null;
alter table auditschema.prenatal add column dt_aplicacao_influenza      date null;
alter table auditschema.prenatal add column participou_atvd_educativa   int2 null;
alter table auditschema.prenatal add column dt_atvd_1                   date null;
alter table auditschema.prenatal add column dt_atvd_2                   date null;
alter table auditschema.prenatal add column dt_atvd_3                   date null;
alter table auditschema.prenatal add column realizou_visita_maternidade int2 null;
alter table auditschema.prenatal add column dt_visita_maternidade       date null;
alter table auditschema.prenatal add column realizou_atend_odonto       int2 null;
alter table auditschema.prenatal add column dt_atend_odonto             date null;
alter table auditschema.prenatal add column aborto_molar                int4 null;
alter table auditschema.prenatal add column aborto_ectopico             int4 null;
alter table auditschema.prenatal add column aborto_espontaneo           int4 null;
alter table auditschema.prenatal add column aborto_outros               int4 null;
alter table auditschema.prenatal add column partos_forceps              int4 null;
alter table auditschema.prenatal add column gestacao_anterior           int2 null;
alter table auditschema.prenatal add column final_gestacao_ant_1_ano    int2 null;
alter table auditschema.prenatal add column dt_ocorrencia               timestamp       null;

alter table auditschema.prenatal add column hist_aids                   int2 null;
alter table auditschema.prenatal add column hist_toxoplasmose           int2 null;
alter table auditschema.prenatal add column hist_anemia                 int2 null;
alter table auditschema.prenatal add column hist_isoimunizacao          int2 null;
alter table auditschema.prenatal add column hist_oligo_polidramnio      int2 null;
alter table auditschema.prenatal add column hist_ciur                   int2 null;
alter table auditschema.prenatal add column hist_hipertensao_arterial   int2 null;
alter table auditschema.prenatal add column hist_diabetes_gestacional   int2 null;
alter table auditschema.prenatal add column hist_hemorragia_1_semestre  int2 null;
alter table auditschema.prenatal add column hist_hemorragia_3_semestre  int2 null;
alter table auditschema.prenatal add column hist_hemorragia_2_semestre  int2 null;
alter table auditschema.prenatal add column hist_transfusao             int2 null;
alter table auditschema.prenatal add column hist_sifilis                int2 null;
alter table auditschema.prenatal add column hist_infeccao_urinaria      int2 null;
alter table auditschema.prenatal add column hist_incompet_istmo_cervical int2 null;
alter table auditschema.prenatal add column hist_ameaca_parto_prematuro int2 null;
alter table auditschema.prenatal add column hist_rotura_prematura_membrana int2 null;
alter table auditschema.prenatal add column hist_pos_datismo            int2 null;
alter table auditschema.prenatal add column hist_eclampsia              int2 null;
alter table auditschema.prenatal add column hist_uso_insulina           int2 null;

alter table prenatal add column tp_parto                    int2            null;
alter table prenatal add column idade_gestacional           int4            null;
alter table prenatal add column peso                        numeric(5,2)    null;
alter table prenatal add column local_ocorrencia            int2            null;
alter table prenatal add column estabelecimento             varchar(100)    null;
alter table prenatal add column presenca_acompanhante       int2            null;
alter table prenatal add column febre                       int2            null;
alter table prenatal add column infeccao                    int2            null;
alter table prenatal add column hemorragia                  int2            null;
alter table prenatal add column problema_mamas              int2            null;

alter table auditschema.prenatal add column tp_parto                    int2            null;
alter table auditschema.prenatal add column idade_gestacional           int4            null;
alter table auditschema.prenatal add column peso                        numeric(5,2)    null;
alter table auditschema.prenatal add column local_ocorrencia            int2            null;
alter table auditschema.prenatal add column estabelecimento             varchar(100)    null;
alter table auditschema.prenatal add column presenca_acompanhante       int2            null;
alter table auditschema.prenatal add column febre                       int2            null;
alter table auditschema.prenatal add column infeccao                    int2            null;
alter table auditschema.prenatal add column hemorragia                  int2            null;
alter table auditschema.prenatal add column problema_mamas              int2            null;

/*
    Wilson - 16/03/2016 - #
*/

CREATE TABLE relacao_produto_consrcio (
    cd_relacao_produto_consrcio INT8 not null,
    cod_pro varchar(13) not null,
    cod_pro_consorcio varchar(13) not null,
    cod_pessoa INT8 not null,
    dt_cadastro timestamp not null,
    cd_usuario NUMERIC(6) not null,
    version int8 not null
);
alter table relacao_produto_consrcio
	add constraint PK_REL_PROD_CONS primary key (cd_relacao_produto_consrcio);

alter table relacao_produto_consrcio
	add constraint FK_REL_PROD_CON_REF_PROD foreign key (cod_pro)
	references produtos (cod_pro)
     on delete restrict on update restrict;

alter table relacao_produto_consrcio
	add constraint FK_REL_PROD_CON_REF_PESSOA foreign key (cod_pessoa)
	references pessoa (cod_pessoa)
     on delete restrict on update restrict;

alter table relacao_produto_consrcio
	add constraint FK_REL_PROD_CON_REF_USUARIO foreign key (cd_usuario)
	references usuarios (cd_usuario)
     on delete restrict on update restrict;

CREATE TABLE auditschema.relacao_produto_consrcio AS SELECT t2.*, t1.* FROM relacao_produto_consrcio t1, audit_temp t2 WHERE 1=2;
create sequence seq_audit_id_relacao_produto_consrcio;
alter table auditschema.relacao_produto_consrcio add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON relacao_produto_consrcio FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

alter table registro_notas_fiscais add flag_origem int2;
alter table auditschema.registro_notas_fiscais add flag_origem int2;

UPDATE registro_notas_fiscais SET flag_origem = 0;

alter table relacao_produto_consrcio add fator_conversao NUMERIC(14,4);
alter table auditschema.relacao_produto_consrcio add fator_conversao NUMERIC(14,4);

/*
    Laudecir - 15/03/2016 - #11702
*/
INSERT INTO agendador_processo (cd_agendador_processo, cd_processo, nm_servico, ds_servico, status, version, tp_processo)
VALUES (21, 21, 'Inativar Profissionais', 'Executa o processo de inativação de profissionais.', 0, 0, 9);

/*
    Wilson - 17/03/2016 - #11835
*/

alter table nodo_atendimento_web add cd_grupo_atend_cbo int8 null;
alter table auditschema.nodo_atendimento_web add cd_grupo_atend_cbo int8 null;

/*
    Laudecir - 18/03/2016 - #11623
*/
/*==============================================================*/
/* TABLE: AUTO_INTIMACAO_EXIGENCIA                              */
/*==============================================================*/
CREATE TABLE auto_intimacao_exigencia (
    cd_auto_intimacao_exigencia INT8           NOT NULL,
    cd_auto_intimacao		INT8           NOT NULL,
    ds_irregularidade       	VARCHAR(500)   NOT NULL,
    ds_exigencia            	VARCHAR(500)   NOT NULL,
    prazo                   	NUMERIC(4)     NOT NULL,
    dt_cumprimento_prazo   	DATE           NOT NULL,
    ds_observacao_prazo     	VARCHAR(250)   NULL,
    dt_cadastro             	TIMESTAMP      NOT NULL,
    dt_usuario              	TIMESTAMP      NOT NULL,
    cd_usuario              	NUMERIC(6)     NOT NULL,
    version                 	INT8           NOT NULL
);

ALTER TABLE auto_intimacao_exigencia ADD PRIMARY KEY(cd_auto_intimacao_exigencia);
ALTER TABLE auto_intimacao_exigencia ADD CONSTRAINT FK_AUTO_INTIMACAO_EXIGENCIA_REF_AUTO_INTIMACAO FOREIGN KEY(cd_auto_intimacao) REFERENCES auto_intimacao (cd_auto_intimacao);
ALTER TABLE auto_intimacao_exigencia ADD CONSTRAINT FK_AUTO_INTIMACAO_EXIGENCIA_REF_USUARIO FOREIGN KEY(cd_usuario) REFERENCES usuarios (cd_usuario);

CREATE TABLE auditschema.auto_intimacao_exigencia AS SELECT t2.*, t1.* FROM auto_intimacao_exigencia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_auto_intimacao_exigencia;alter table auditschema.auto_intimacao_exigencia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON auto_intimacao_exigencia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

alter table auto_intimacao add column lavrado_papel int2;
alter table AUDITSCHEMA.auto_intimacao add column lavrado_papel int2;
update auto_intimacao set lavrado_papel = 0;
alter table auto_intimacao alter column lavrado_papel set not null;

/*
    Sulivan - 21/03/2016 - #11869
*/
alter table esus_ficha_atendimento_domiciliar_item alter COLUMN conduta drop not null;

/*
    Laudecir - 06/04/2016 - #11702
*/
insert into motivo_desligamento values (11, 'DESVINCULADO AUTOMATICAMENTE PROCESSO DE INATIVAÇÃO', 0);

/*
    Wilson - 21/03/2016 - #11521
*/

alter table auto_infracao add cd_roteiro_inspecao int8;
alter table auditschema.auto_infracao add cd_roteiro_inspecao int8;

alter table auto_infracao
	add constraint FK_AUTO_INFRA_REF_REG_INSP foreign key (cd_roteiro_inspecao)
	references registro_inspecao (cd_reg_insp)
     on delete restrict on update restrict;

alter table auto_intimacao add cd_registro_inspecao int8;
alter table auditschema.auto_intimacao add cd_registro_inspecao int8;

alter table auto_intimacao
	add constraint FK_AUTO_INTIMA_REF_REG_INSP foreign key (cd_registro_inspecao)
	references registro_inspecao (cd_reg_insp)
     on delete restrict on update restrict;

alter table auto_penalidade add cd_registro_inspecao int8;
alter table auditschema.auto_penalidade add cd_registro_inspecao int8;

alter table auto_penalidade
	add constraint FK_AUTO_INTIMA_REF_REG_INSP foreign key (cd_registro_inspecao)
	references registro_inspecao (cd_reg_insp)
     on delete restrict on update restrict;

/*
    Sulivan - 01/04/2016 - #11969
*/
alter table atendimento add column cd_usuario_atendendo NUMERIC(6);
alter table AUDITSCHEMA.atendimento add column cd_usuario_atendendo NUMERIC(6);
ALTER TABLE atendimento ADD CONSTRAINT FK_ATEND_REF_USU_ATEND FOREIGN KEY(cd_usuario_atendendo) REFERENCES usuarios (cd_usuario);

/*
     Everton - 04/04/2016 - #11906
*/

update empresa set local_atendimento = 1 where local_atendimento is null;

/*
     Everton - 04/04/2016 - #11969
*/

update atendimento t1 set cd_usuario_atendendo = (select min(cd_usuario) from usuarios where cd_profissional = t1.cd_profissional)
  where status = 2 and cd_usuario_atendendo is null; 
