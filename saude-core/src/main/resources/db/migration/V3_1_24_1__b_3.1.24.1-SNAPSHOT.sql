SET application_name = 'flyway|3.1.24.1';

SET statement_timeout TO 600000;

/*==============================================================*/
/*   TABLE: FORMULARIO_TRIAGEM_COVID19                          */
/*   autor: <PERSON>                                    */
/*   tarefa: AMB-590                                            */
/*==============================================================*/
CREATE TABLE formulario_triagem_covid19 (
    cd_formulario_triagem_covid19                INT8            not null,
    nr_atendimento                               INT8            not null,
    data_notificacao                             TIMESTAMP       not null,
    data_primeiros_sintomas                      TIMESTAMP       not null,
    flag_utilizou_analgesico                     INT2            null,
    flag_foi_hospitalizado                       INT2            null,
    flag_realizado_coleto_amostra                INT2            null,
    flag_historico_viagem_fora_brasil            INT2            null,
    contato_paciente_suspeito                    INT2            null,
    contato_paciente_confirmado                  INT2            null,
    esteve_alguma_unidade_saude                  INT2            null,
    ocupacao_caso_suspeito                       INT2            null,
    situacao_saude_paciente                      INT2            null,
    data_internacao                              TIMESTAMP       null,
    data_alta                                    TIMESTAMP       null,
    data_isolamento                              TIMESTAMP       null,
    data_chegada_brasil                          TIMESTAMP       null,
    nome_hospital                                varchar         null,
    outros_sintomas                              varchar         null,
    outros_sinais                                varchar         null,
    descritivo_historico_deslocamento            varchar         null,
    outro_local_caso_suspeito                    varchar         null,
    outro_local_caso_confirmado                  varchar         null,
    outra_ocupacao_casou_suspeito                varchar         null,
    unidade_saude_frequentada                    varchar         null,
    version                                      INT8            null
);


ALTER TABLE formulario_triagem_covid19
   ADD CONSTRAINT PK_TRIAGEM_COVID PRIMARY KEY (cd_formulario_triagem_covid19);

ALTER TABLE formulario_triagem_covid19
 ADD CONSTRAINT FK_FORMULARIO_TRIAGEM_COVID19_ATENDIMENTO FOREIGN KEY (nr_atendimento)
  REFERENCES atendimento (nr_atendimento);

/*==============================================================*/
/*   TABLE: AUDITORIA FORMULARIO_TRIAGEM_COVID19                */
/*   autor: Eduardo Ximendes                                    */
/*   tarefa: AMB-590                                            */
/*==============================================================*/
CREATE TABLE AUDITSCHEMA.formulario_triagem_covid19 AS SELECT T2.*, T1.* FROM formulario_triagem_covid19 T1, AUDIT_TEMP T2 WHERE 1 = 2;
CREATE sequence SEQ_AUDIT_ID_FORMULARIO_TRIAGEM_COVID19;
ALTER TABLE AUDITSCHEMA.formulario_triagem_covid19 ADD PRIMARY KEY(AUDIT_ID);
CREATE trigger EMP_AUDIT after INSERT OR UPDATE OR DELETE ON formulario_triagem_covid19 FOR each row execute procedure PROCESS_EMP_AUDIT();

/*==============================================================*/
/*   TABLE: SINTOMAS_COVID19                                    */
/*   autor: Eduardo Ximendes                                    */
/*   tarefa: AMB-590                                            */
/*==============================================================*/

CREATE TABLE sintomas_covid19 (
    cd_sintomas_covid19                INT8            not null,
    descricao       varchar                 not null
);

ALTER TABLE sintomas_covid19
   ADD CONSTRAINT PK_SINTOMAS_COVID PRIMARY KEY (cd_sintomas_covid19);

/*==============================================================*/
/*   TABLE: AUDITORIA SINTOMAS_COVID19                          */
/*   autor: Eduardo Ximendes                                    */
/*   tarefa: AMB-590                                            */
/*==============================================================*/
CREATE TABLE AUDITSCHEMA.sintomas_covid19 AS SELECT T2.*, T1.* FROM sintomas_covid19 T1, AUDIT_TEMP T2 WHERE 1 = 2;
CREATE sequence SEQ_AUDIT_ID_SINTOMAS_COVID19;
ALTER TABLE AUDITSCHEMA.sintomas_covid19 ADD PRIMARY KEY(AUDIT_ID);
CREATE trigger EMP_AUDIT after INSERT OR UPDATE OR DELETE ON sintomas_covid19 FOR each row execute procedure PROCESS_EMP_AUDIT();

/*==============================================================*/
/*   TABLE: FORMULARIO_TRIAGEM_SINTOMAS                         */
/*   autor: Eduardo Ximendes                                    */
/*   tarefa: AMB-590                                            */
/*==============================================================*/
CREATE TABLE formulario_triagem_sintomas_covid19 (
 cd_formulario_triagem_sintomas_covid19  INT8 not null,
 cd_formulario_triagem_covid19  INT8 not null,
 cd_sintomas_covid19 INT8 not null,
 version                     INT8            not null
);
ALTER TABLE formulario_triagem_sintomas_covid19
   ADD CONSTRAINT PK_FORMULARIO_TRIAGEM_SINTOMAS_COVID19 PRIMARY KEY (cd_formulario_triagem_sintomas_covid19);

ALTER TABLE formulario_triagem_sintomas_covid19
   ADD CONSTRAINT FK_FORMULARIO_TRIAGEM_COVID19 FOREIGN KEY (cd_formulario_triagem_covid19)
      REFERENCES formulario_triagem_covid19 (cd_formulario_triagem_covid19)
      ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE formulario_triagem_sintomas_covid19
   ADD CONSTRAINT FK_SINTOMAS_COVID19 FOREIGN KEY (cd_sintomas_covid19)
      REFERENCES sintomas_covid19 (cd_sintomas_covid19)
      ON DELETE RESTRICT ON UPDATE RESTRICT;

/*==============================================================*/
/*   TABLE: AUDITORIA FORMULARIO_TRIAGEM_SINTOMAS               */
/*   autor: Eduardo Ximendes                                    */
/*   tarefa: AMB-590                                            */
/*==============================================================*/
CREATE TABLE AUDITSCHEMA.formulario_triagem_sintomas_covid19 AS SELECT T2.*, T1.* FROM formulario_triagem_sintomas_covid19 T1, AUDIT_TEMP T2 WHERE 1 = 2;
CREATE sequence seq_audit_id_formulario_triagem_sintomas_covid19;
ALTER TABLE AUDITSCHEMA.formulario_triagem_sintomas_covid19 ADD PRIMARY KEY(AUDIT_ID);
CREATE trigger EMP_AUDIT after INSERT OR UPDATE OR DELETE ON formulario_triagem_sintomas_covid19 FOR each row execute procedure PROCESS_EMP_AUDIT();

/*==============================================================*/
/*   TABLE: SINAIS_CLINICOS_COVID19                             */
/*   autor: Eduardo Ximendes                                    */
/*   tarefa: AMB-590                                            */
/*==============================================================*/
CREATE TABLE sinais_clinicos_covid19 (
    cd_sinais_clinicos_covid19                INT8            not null,
    descricao       varchar                 not null
);

ALTER TABLE sinais_clinicos_covid19
   ADD CONSTRAINT PK_SINAIS_CLINICOS_COVID PRIMARY KEY (cd_sinais_clinicos_covid19);

/*==============================================================*/
/*   TABLE: AUDITORIA SINAIS_CLINICOS_COVID19               */
/*   autor: Eduardo Ximendes                                    */
/*   tarefa: AMB-590                                            */
/*==============================================================*/
CREATE TABLE AUDITSCHEMA.sinais_clinicos_covid19 AS SELECT T2.*, T1.* FROM sinais_clinicos_covid19 T1, AUDIT_TEMP T2 WHERE 1 = 2;
CREATE sequence SEQ_AUDIT_ID_SINAIS_CLINICOS_COVID19;
ALTER TABLE AUDITSCHEMA.sinais_clinicos_covid19 ADD PRIMARY KEY(AUDIT_ID);
CREATE trigger EMP_AUDIT after INSERT OR UPDATE OR DELETE ON sinais_clinicos_covid19 FOR each row execute procedure PROCESS_EMP_AUDIT();

/*==============================================================*/
/*   TABLE: FORMULARIO_TRIAGEM_SINAIS_CLINICOS                  */
/*   autor: Eduardo Ximendes                                    */
/*   tarefa: AMB-590                                            */
/*==============================================================*/
CREATE TABLE formulario_triagem_sinais_clinicos_covid19 (
 cd_formulario_triagem_sinais_clinicos    INT8 NOT NULL,
 cd_formulario_triagem_covid19    INT8 NOT NULL,
 cd_sinais_clinicos_covid19 INT8 NOT NULL,
 version                     INT8            not null
);

ALTER TABLE formulario_triagem_sinais_clinicos_covid19
   ADD CONSTRAINT PK_FORMULARIO_SINAIS_CLINICOS PRIMARY KEY (cd_formulario_triagem_sinais_clinicos);

ALTER TABLE formulario_triagem_sinais_clinicos_covid19
   ADD CONSTRAINT FK_FORMULARIO_TRIAGEM_COVID19 FOREIGN KEY (cd_formulario_triagem_covid19)
      REFERENCES formulario_triagem_covid19 (cd_formulario_triagem_covid19)
      ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE formulario_triagem_sinais_clinicos_covid19
   ADD CONSTRAINT FK_FORMULARIO_TRIAGEM_SINAIS FOREIGN KEY (cd_sinais_clinicos_covid19)
      REFERENCES sinais_clinicos_covid19 (cd_sinais_clinicos_covid19)
      ON DELETE RESTRICT ON UPDATE RESTRICT;

/*==============================================================*/
/*   TABLE: AUDITORIA FORMULARIO_TRIAGEM_SINAIS_CLINICOS               */
/*   autor: Eduardo Ximendes                                    */
/*   tarefa: AMB-590                                            */
/*==============================================================*/
CREATE TABLE AUDITSCHEMA.formulario_triagem_sinais_clinicos_covid19 AS SELECT T2.*, T1.* FROM formulario_triagem_sinais_clinicos_covid19 T1, AUDIT_TEMP T2 WHERE 1 = 2;
CREATE sequence seq_audit_id_formulario_triagem_sinais_clinicos_covid19;
ALTER TABLE AUDITSCHEMA.formulario_triagem_sinais_clinicos_covid19 ADD PRIMARY KEY(AUDIT_ID);
CREATE trigger EMP_AUDIT after INSERT OR UPDATE OR DELETE ON formulario_triagem_sinais_clinicos_covid19 FOR each row execute procedure PROCESS_EMP_AUDIT();

/*==============================================================*/
/*   TABLE: MORBIDADE_PREVIA_COVID_19                           */
/*   autor: Eduardo Ximendes                                    */
/*   tarefa: AMB-590                                            */
/*==============================================================*/
  CREATE TABLE morbidade_previa_covid19 (
    cd_morbidade_previa_covid19                INT8            not null,
    descricao       varchar                 not null
);

ALTER TABLE morbidade_previa_covid19
   ADD CONSTRAINT PK_MORBIDADE_PREVIA_COVID PRIMARY KEY (cd_morbidade_previa_covid19);

/*==============================================================*/
/*   TABLE: AUDITORIA MORBIDADE_PREVIA_COVID_19               */
/*   autor: Eduardo Ximendes                                    */
/*   tarefa: AMB-590                                            */
/*==============================================================*/
CREATE TABLE AUDITSCHEMA.morbidade_previa_covid19 AS SELECT T2.*, T1.* FROM morbidade_previa_covid19 T1, AUDIT_TEMP T2 WHERE 1 = 2;
CREATE sequence SEQ_AUDIT_ID_MORBIDADE_PREVIA_COVID19;
ALTER TABLE AUDITSCHEMA.morbidade_previa_covid19 ADD PRIMARY KEY(AUDIT_ID);
CREATE trigger EMP_AUDIT after INSERT OR UPDATE OR DELETE ON morbidade_previa_covid19 FOR each row execute procedure PROCESS_EMP_AUDIT();

/*==============================================================*/
/*   TABLE: FORMULARIO_TRIAGEM_MORBIDADE_PREVIA                 */
/*   autor: Eduardo Ximendes                                    */
/*   tarefa: AMB-590                                            */
/*==============================================================*/
CREATE TABLE formulario_triagem_morbidade_previa_covid19 (
 cd_formulario_triagem_morbidade_covid19    INT8 NOT NULL,
 cd_formulario_triagem_covid19    INT8 NOT NULL,
 cd_morbidade_previa_covid19 INT8 NOT NULL,
 version                     INT8            not null
);

ALTER TABLE formulario_triagem_morbidade_previa_covid19
   ADD CONSTRAINT PK_FORMULARIO_TRIAGEM_MORBIDADE PRIMARY KEY (cd_formulario_triagem_morbidade_covid19);

ALTER TABLE formulario_triagem_morbidade_previa_covid19
   ADD CONSTRAINT FK_FORMULARIO_TRIAGEM_COVID19 FOREIGN KEY (cd_formulario_triagem_covid19)
      REFERENCES formulario_triagem_covid19 (cd_formulario_triagem_covid19)
      ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE formulario_triagem_morbidade_previa_covid19
   ADD CONSTRAINT FK_FORMULARIO_TRIAGEM_MORBIDADE FOREIGN KEY (cd_morbidade_previa_covid19)
      REFERENCES morbidade_previa_covid19 (cd_morbidade_previa_covid19)
      ON DELETE RESTRICT ON UPDATE RESTRICT;

/*==============================================================*/
/*   TABLE: AUDITORIA FORMULARIO_TRIAGEM_MORBIDADE_PREVIA               */
/*   autor: Eduardo Ximendes                                    */
/*   tarefa: AMB-590                                            */
/*==============================================================*/
CREATE TABLE AUDITSCHEMA.formulario_triagem_morbidade_previa_covid19 AS SELECT T2.*, T1.* FROM formulario_triagem_morbidade_previa_covid19 T1, AUDIT_TEMP T2 WHERE 1 = 2;
CREATE sequence SEQ_AUDIT_ID_FORMULARIO_TRIAGEM_MORBIDADE_PREVIA_COVID19;
ALTER TABLE AUDITSCHEMA.formulario_triagem_morbidade_previa_covid19 ADD PRIMARY KEY(AUDIT_ID);
CREATE trigger EMP_AUDIT after INSERT OR UPDATE OR DELETE ON formulario_triagem_morbidade_previa_covid19 FOR each row execute procedure PROCESS_EMP_AUDIT();

/*==============================================================*/
/*   INSERT SINTOMAS_COVID19                                    */
/*   autor: Eduardo Ximendes                                    */
/*   tarefa: AMB-590                                            */
/*==============================================================*/
INSERT INTO sintomas_covid19 (cd_sintomas_covid19, descricao) VALUES(1,'Febre');
INSERT INTO sintomas_covid19 (cd_sintomas_covid19, descricao) VALUES( 2,'Tosse');
INSERT INTO sintomas_covid19 (cd_sintomas_covid19, descricao) VALUES( 3,'Dor de garganta');
INSERT INTO sintomas_covid19 (cd_sintomas_covid19, descricao) VALUES( 4,'Dificuldade de respirar');
INSERT INTO sintomas_covid19 (cd_sintomas_covid19, descricao) VALUES( 5,'Mialgia/artralgia');
INSERT INTO sintomas_covid19 (cd_sintomas_covid19, descricao) VALUES( 6,'Diarreia');
INSERT INTO sintomas_covid19 (cd_sintomas_covid19, descricao) VALUES( 7,'Náusea/vômitos');
INSERT INTO sintomas_covid19 (cd_sintomas_covid19, descricao) VALUES( 8,'Cefaleia (dor de cabeça)');
INSERT INTO sintomas_covid19 (cd_sintomas_covid19, descricao) VALUES( 9,'Coriza');
INSERT INTO sintomas_covid19 (cd_sintomas_covid19, descricao) VALUES( 10,'Irritabilidade/confusão');
INSERT INTO sintomas_covid19 (cd_sintomas_covid19, descricao) VALUES( 11,'Adinamia (fraqueza)');
INSERT INTO sintomas_covid19 (cd_sintomas_covid19, descricao) VALUES( 12,'Produção de escarro');
INSERT INTO sintomas_covid19 (cd_sintomas_covid19, descricao) VALUES( 13,'Calafrios');
INSERT INTO sintomas_covid19 (cd_sintomas_covid19, descricao) VALUES( 14,'Congestão nasal');
INSERT INTO sintomas_covid19 (cd_sintomas_covid19, descricao) VALUES( 15,'Congestão conjuntival');
INSERT INTO sintomas_covid19 (cd_sintomas_covid19, descricao) VALUES( 16,'Dificuldade para deglutir');
INSERT INTO sintomas_covid19 (cd_sintomas_covid19, descricao) VALUES( 17,'Manchas vermelhas pelo corpo');
INSERT INTO sintomas_covid19 (cd_sintomas_covid19, descricao) VALUES( 18,'Gânglios linfáticos aumentados');
INSERT INTO sintomas_covid19 (cd_sintomas_covid19, descricao) VALUES( 19,'Batimento das asas nasais');
INSERT INTO sintomas_covid19 (cd_sintomas_covid19, descricao) VALUES( 20,'Saturação de O2 < 95%');
INSERT INTO sintomas_covid19 (cd_sintomas_covid19, descricao) VALUES( 21,'Sinais de cianose');
INSERT INTO sintomas_covid19 (cd_sintomas_covid19, descricao) VALUES( 22,'Tiragem intercostal');
INSERT INTO sintomas_covid19 (cd_sintomas_covid19, descricao) VALUES( 23,'Dispneia');
INSERT INTO sintomas_covid19 (cd_sintomas_covid19, descricao) VALUES( 24,'Outros');

/*==============================================================*/
/*   INSERT SINAIS_CLINICOS_COVID19                             */
/*   autor: Eduardo Ximendes                                    */
/*   tarefa: AMB-590                                            */
/*==============================================================*/
INSERT INTO sinais_clinicos_covid19 (cd_sinais_clinicos_covid19, descricao) VALUES(1,  'Febre');
INSERT INTO sinais_clinicos_covid19 (cd_sinais_clinicos_covid19, descricao)VALUES(2, 'Exsudato faríngeo');
INSERT INTO sinais_clinicos_covid19 (cd_sinais_clinicos_covid19, descricao)VALUES(3, 'Convulsão');
INSERT INTO sinais_clinicos_covid19 (cd_sinais_clinicos_covid19, descricao)VALUES(4, 'Conjuntivite');
INSERT INTO sinais_clinicos_covid19 (cd_sinais_clinicos_covid19, descricao)VALUES(5, 'Coma');
INSERT INTO sinais_clinicos_covid19 (cd_sinais_clinicos_covid19, descricao)VALUES(6, 'Dispneia Taquipneia');
INSERT INTO sinais_clinicos_covid19 (cd_sinais_clinicos_covid19, descricao)VALUES(7, 'Alteração de ausculta pulmonar');
INSERT INTO sinais_clinicos_covid19 (cd_sinais_clinicos_covid19, descricao)VALUES(8, 'Alteração na radiologia de tórax');
INSERT INTO sinais_clinicos_covid19 (cd_sinais_clinicos_covid19, descricao)VALUES(9, 'Outros');

/*==============================================================*/
/*   INSERT MORBIDADE_PREVIA_COVID19                            */
/*   autor: Eduardo Ximendes                                    */
/*   tarefa: AMB-590                                            */
/*==============================================================*/
INSERT INTO morbidade_previa_covid19 (cd_morbidade_previa_covid19, descricao) VALUES(1, 'Doença cardiovascular, incluindo hipertensão');
INSERT INTO morbidade_previa_covid19 (cd_morbidade_previa_covid19, descricao) VALUES(2, 'Diabetes');
INSERT INTO morbidade_previa_covid19 (cd_morbidade_previa_covid19, descricao) VALUES(3, 'Doença hepática');
INSERT INTO morbidade_previa_covid19 (cd_morbidade_previa_covid19, descricao) VALUES(4, 'Doença neurológica crônica ou neuromuscular');
INSERT INTO morbidade_previa_covid19 (cd_morbidade_previa_covid19, descricao) VALUES(5, 'Imunodeficiência');
INSERT INTO morbidade_previa_covid19 (cd_morbidade_previa_covid19, descricao) VALUES(6, 'Infecção pelo HIV');
INSERT INTO morbidade_previa_covid19 (cd_morbidade_previa_covid19, descricao) VALUES(7, 'Doença renal');
INSERT INTO morbidade_previa_covid19 (cd_morbidade_previa_covid19, descricao) VALUES(8, 'Doença pulmonar crônica');
INSERT INTO morbidade_previa_covid19 (cd_morbidade_previa_covid19, descricao) VALUES(9, 'Neoplasia (tumor sólido ou hematológico)');

/*
    Adriano - AMB-339 - 20/03/2020
*/

ALTER TABLE tipo_documento_atend ADD COLUMN atestado boolean default false;
ALTER TABLE auditschema.tipo_documento_atend ADD COLUMN atestado boolean default false;