SET application_name = 'flyway|********';

/*
    Sulivan - #14999 - 01/06/2017
*/
create index idx_conta_paciente_01 on conta_paciente (documento);
create index idx_conta_paciente_02 on conta_paciente (dt_geracao);
create index idx_conta_paciente_03 on conta_paciente (competencia_atendimento);

INSERT INTO programa_pagina_permissao VALUES(337, 32, 1031, 0, 'tipoContaConsolidadeOutros');

/*
    Everton - #14999 - 12/06/2017
*/

alter table conta_paciente disable trigger user;
update conta_paciente t1 set cd_tp_atendimento = (select tp_atend_faturamento from atendimento_informacao where cd_atend_inf = t1.cd_atend_inf)
  where cd_tp_atendimento is null and cd_atend_inf is not null;
alter table conta_paciente enable trigger user;

