SET application_name = 'flyway|3.1.173';

SET statement_timeout TO 600000;

/*
    Fernanda Bianchini - VSA-2100
 */

ALTER TABLE requerimento_autorizacao_sanitaria ADD observacao_estrutura VARCHAR(50) NULL;
ALTER TABLE auditschema.requerimento_autorizacao_sanitaria ADD observacao_estrutura VARCHAR(50) NULL;

/*
    Fernanda Bianchini - 10/02/2023 - VSA-1632
*/

INSERT INTO public.ficha_investigacao_agravo(cd_ficha_investigacao_agravo, ordem, dt_cadastro, descricao, status, "version")
VALUES (59, 1, NOW(), 'NOTIFICAÇÃO NEGATIVA', 1, 0);

CREATE TABLE public.investigacao_agr_notificacao_negativa(
     cd_investigacao_agr_notificacao_negativa int8 PRIMARY KEY NOT NULL,
     "version" int8 NOT NULL,
     flag_informacoes_complementares varchar(1) NOT NULL DEFAULT 'S'::character varying,
     cd_registro_agravo int8 NOT NULL REFERENCES public.registro_agravo(cd_registro_agravo),

    --DADOS GERAIS
     tipo_notificacao int2 NULL,

    --INVESTIGAÇÃO
     dt_investigacao date NULL,

    --NOTIFICACAO DE SURTO
     dt_1_sintomas date NULL,
     num_casos_suspeitos varchar(5),
     local_inicial_ocorrencia_surto int2 NULL,
     local_inicial_ocorrencia_surto_outros varchar(50),

    --NOTIFICACAO INDIVIDUAL
     dt_1_amostra_sorologia date NULL,
     dt_1_amostra_outra_amostra date NULL,
     tipo_exame varchar(50),

     obito int2 NULL,
     contato_semelhante int2 NULL,

     presenca_exantema int2 NULL,
     dt_inicio_exantema date NULL,
     presenca_petequias int2 NULL,
     liquor int2 NULL,
     resultado_bacterioscopia varchar(50),

     tomou_vacina int2 NULL,
     dt_ultima_dose_tomada date NULL,

    --HOSPITALIZACAO
     hospitalizacao int2 NULL,
     dt_internacao DATE NULL,
     unidade_hospital int8 NULL REFERENCES public.empresa(empresa),

     hipotese_diagnostica_1 bpchar(8) null references public.cid(cd_cid),
     hipotese_diagnostica_2 bpchar(8) null references public.cid(cd_cid),

     --CASO AUTÓCTONE
     caso_autoctone int8 NULL,
     cd_cidade_infeccao int8 NULL REFERENCES public.cidade(cod_cid),
     cd_pais_infeccao int8 NULL REFERENCES public.nacionalidade(cd_pais),
     str_distrito_infeccao varchar(200) NULL,
     str_bairro_infeccao varchar(200) NULL,
     local_provavel_infeccao int2 NULL,
     ambiente_infeccao int2 NULL,

    --OBSERVAÇÃO
     observacao text NULL,

    --ENCERRAMENTO
     dt_encerramento date NULL,
     cd_usuario_encerramento int8 NULL
);

CREATE SEQUENCE seq_investigacao_agr_notificacao_negativa INCREMENT 1 START 1;
CREATE TABLE auditschema.investigacao_agr_notificacao_negativa AS (SELECT t2.*, t1.* FROM investigacao_agr_notificacao_negativa t1, audit_temp t2 WHERE 1=2);
CREATE sequence seq_audit_id_investigacao_agr_notificacao_negativa;
ALTER TABLE auditschema.investigacao_agr_notificacao_negativa ADD PRIMARY KEY (audit_id);
CREATE trigger emp_audit AFTER INSERT OR UPDATE OR DELETE ON public.investigacao_agr_notificacao_negativa FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();












