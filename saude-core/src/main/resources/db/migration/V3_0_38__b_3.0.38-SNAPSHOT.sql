/*
    Diego - 30/03/2015 - #9577
*/
alter table atendimento_informacao add column seq_ciclo int4;
alter table atendimento_alta alter column cd_cid drop not null;
drop index idx_atendimento_principal;
update atendimento_informacao set seq_ciclo = 0 where seq_ciclo is null;
create index idx_atendimento_informacao_atendimento_principal on atendimento_informacao(nr_atendimento_principal);

/*
 PostgreSQL
 Everton - 08/04/2015 - #9632
*/

alter table tipo_procedimento add cd_exame_procedimento INT8                 null;
alter table tipo_procedimento add procedimento_vaga            INT4                 null;
update tipo_procedimento set procedimento_vaga = 1;
alter table tipo_procedimento alter procedimento_vaga set not null;

alter table tipo_procedimento
   add constraint FK_TP_PROCLA_REF_EXAME foreign key (cd_exame_procedimento)
      references exame_procedimento (cd_exame_procedimento)
      on delete restrict on update restrict;

/*
 PostgreSQL
 Everton - 10/04/2015 - #9606
*/
alter table agenda alter observacao type varchar;
alter table agenda alter recomendacoes type varchar;

/*
    Sulivan - 13/04/2015 - #9639
*/
update programa_web set ds_prg_web = 'Anexar Documentos para o Paciente' where cd_prg_web = 506;
update menu_web set ds_menu = 'Anexar Documentos para o Paciente', ds_bundle = 'anexarDocumentosParaPaciente', cd_menu_pai = 182 where cd_menu = 682;

/*
    Sulivan - 15/04/2015 - #9762
*/
/*==============================================================*/
/* Table: agenda_cirurgia                                */
/*==============================================================*/
create table agenda_cirurgia (
    cd_agenda_cirurgia      INT8                 not null,
    duracao                  INT8                 not null,
    anestesista             INT2                  not null,
    circulante              INT2                  not null,
    lado                    INT2                  null,
    raio_x                  INT2                  not null,
    opm                    INT2                  not null,
    banco_sangue            INT2                  not null,
    servicos_especias       INT2                  not null,
    dt_cadastro             TIMESTAMP            not null,
    cd_usuario              NUMERIC(6)           not null,
    version                 INT8                 not null
);

alter table agenda_cirurgia
   add constraint PK_AGENDA_CIRURGIA primary key (cd_agenda_cirurgia);

alter table agenda_cirurgia
   add constraint FK_AG_CIRUR_REF_USUARIO foreign key (cd_usuario)
   references usuarios (cd_usuario)
   on delete restrict on update restrict;

alter table agenda_gra_ate_horario add cd_agenda_cirurgia     INT8     null;

alter table agenda_gra_ate_horario
   add constraint FK_AG_HR_REF_AG_CIRUR foreign key (cd_agenda_cirurgia)
   references agenda_cirurgia (cd_agenda_cirurgia)
   on delete restrict on update restrict;

/*
 Everton - 01/04/2015 - #9632
*/

alter table agenda_gra_ate_horario add cd_ag_hr_principal   INT8                 null;
alter table agenda_gra_ate_horario
   add constraint FK_AG_HR_REF_AG_ATE_HR foreign key (cd_ag_hr_principal)
      references agenda_gra_ate_horario (cd_ag_gra_ate_hor)
      on delete restrict on update restrict;

/*
    PostgreSQL
    Everton - 14/04/2015 - #9719
*/
/*==============================================================*/
/* Table: lancamento_despesa                                    */
/*==============================================================*/
create table lancamento_despesa (
    cd_lancto_despesa    INT8                 not null,
    cd_programa_saude    INT4                 not null,
    cod_pessoa           INT8                 not null,
    cd_tp_movimentacao   INT8                 not null,
    cod_centro_custo     INT4                 not null,
    empresa              INT4                 not null,
    ds_despesa           VARCHAR(512)         not null,
    dt_despesa           date                 not null,
    vl_despesa           NUMERIC(12,2)        not null,
    version              int8                 not null
);

comment on column lancamento_despesa.cod_pessoa is 'Codigo do Cliente';

alter table lancamento_despesa
   add constraint PK_LANCAMENTO_DESPESA primary key (cd_lancto_despesa);

alter table lancamento_despesa
   add constraint FK_LAN_DESP_REF_C_CUSTO foreign key (cod_centro_custo)
      references centro_custo (cod_centro_custo)
      on delete restrict on update restrict;

alter table lancamento_despesa
   add constraint FK_LAN_DESP_REF_EMPRESA foreign key (empresa)
      references empresa (empresa)
      on delete restrict on update restrict;

alter table lancamento_despesa
   add constraint FK_LAN_DESP_REF_FORNEC foreign key (cod_pessoa)
      references Pessoa (cod_pessoa)
      on delete restrict on update restrict;

alter table lancamento_despesa
   add constraint FK_LAN_DESP_REF_PRG_SAUDE foreign key (cd_programa_saude)
      references programa_saude (cd_programa_saude)
      on delete restrict on update restrict;

alter table lancamento_despesa
   add constraint FK_LAN_DESP_REF_TP_MOV foreign key (cd_tp_movimentacao)
      references tipo_movimentacao (cd_tp_movimentacao)
      on delete restrict on update restrict;

INSERT INTO programa_pagina VALUES (917, 'br.com.celk.view.geral.despesa.ConsultaDespesaPage', 'N');
INSERT INTO programa_pagina VALUES (918, 'br.com.celk.view.geral.despesa.CadastroDespesaPage', 'N');
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (692,'Gastos','gastos',0,null,null,0,0);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (693,'Cadastros','cadastros',692,null,null,0,0);
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (514, 'Despesas', 917, 'N');
INSERT INTO programa_web_pagina VALUES (925, 514, 917);
INSERT INTO programa_web_pagina VALUES (926, 514, 918);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (694,'Despesa','despesa',693,514,0,0,0);

/*
    Laudecir - 14/04/2015 - #9720
*/
INSERT INTO programa_pagina VALUES (919, 'br.com.celk.view.geral.despesa.relatorios.RelatorioDespesasPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (515, 'Relação das Despesas', 919, 'N');
INSERT INTO programa_web_pagina VALUES (927, 515, 919);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (695,'Relatórios','relatorios',692,null,null,0,0);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (696,'Relação das Despesas','relacaoDespesas',695,515,0,0,0);

/*
    PostgreSQL
    Everton - 21/11/2014 - #9722
*/
/*==============================================================*/
/* Table: tipo_indicador                                        */
/*==============================================================*/
create table tipo_indicador (
    cd_tp_indicador      INT8                 not null,
    ds_tp_indicador      VARCHAR(50)          not null,
    version              INT8                 not null
);

alter table tipo_indicador
        add constraint PK_TIPO_INDICADOR primary key (cd_tp_indicador);

/*
    Laudecir - 21/11/2014 - #9722
*/
INSERT INTO programa_pagina VALUES (920, 'br.com.celk.view.indicadores.cadastro.tipoindicador.ConsultaTipoIndicadorPage', 'N');
INSERT INTO programa_pagina VALUES (921, 'br.com.celk.view.indicadores.cadastro.tipoindicador.CadastroTipoIndicadorPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (516, 'Tipo de Indicadores', 920, 'N');
INSERT INTO programa_web_pagina VALUES (928, 516, 920);
INSERT INTO programa_web_pagina VALUES (929, 516, 921);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (697,'Cadastro','cadastro',184,null,null,0,0);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (698,'','',697,null,184,0,0);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (699,'Tipo de Indicadores','tipoIndicadores',698,516,184,0,0);

/*
    Diego - 15/04/2015 - #9739
*/

alter table receituario_item_kit add column cod_uni NUMERIC(6);
alter table receituario_item_kit
   add constraint FK_RECEITUA_REFERENCE_UNIDADE foreign key (cod_uni)
      references unidade (cod_uni)
      on delete restrict on update restrict;

/*
    Diego
*/

alter table atendimento_exame add column crm_solicitante varchar(10);

/*
 PostgreSQL
 Everton - 15/04/2014 - #9615
*/

update usuario_cadsus set nr_telefone = celular, celular = null where coalesce(nr_telefone, ' ') = ' ' and  coalesce(celular, ' ') <> ''  and substr(celular, 3, 1) not in ('9','8','7');
update usuario_cadsus set nr_telefone_2 = celular, celular = null where coalesce(nr_telefone_2, ' ') = ' ' and  coalesce(celular, ' ') <> ''  and substr(celular, 3, 1) not in ('9','8','7');
update usuario_cadsus set telefone3 = celular, celular = null where coalesce(telefone3, ' ') = ' ' and  coalesce(celular, ' ') <> ''  and substr(celular, 3, 1) not in ('9','8','7');
update usuario_cadsus set telefone4 = celular, celular = null where coalesce(telefone4, ' ') = ' ' and  coalesce(celular, ' ') <> ''  and substr(celular, 3, 1) not in ('9','8','7');
update usuario_cadsus set celular = null where coalesce(celular, ' ') <> ''  and substr(celular, 3, 1) not in ('9','8','7');


/*
    Sulivan 15/04/2014 - #9768
*/
drop table sala_unidade;
/*==============================================================*/
/* Table: sala_unidade                                          */
/*==============================================================*/
create table sala_unidade (
    cd_sala                INT8                 not null,
    empresa                 INT4                  not null,
    ds_sala                 VARCHAR(50)          not null,
    version                 INT8                 not null
);

alter table sala_unidade
   add constraint PK_SALA_UNIDADE primary key (cd_sala);

alter table sala_unidade
   add constraint FK_SALA_UNIDADE_REF_EMPRESA foreign key (empresa)
   references empresa (empresa)
   on delete restrict on update restrict;

INSERT INTO programa_pagina VALUES (922, 'br.com.celk.view.agenda.cadastro.salaunidade.ConsultaSalaUnidadePage', 'N');
INSERT INTO programa_pagina VALUES (923, 'br.com.celk.view.agenda.cadastro.salaunidade.CadastroSalaUnidadePage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (517, 'Sala X Unidade', 922, 'N');
INSERT INTO programa_web_pagina VALUES (930, 517, 922);
INSERT INTO programa_web_pagina VALUES (931, 517, 923);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (700,'Sala X Unidade','salaXUnidade',473,517,3,0,0);
INSERT INTO programa_pagina_permissao VALUES(161, 18, 922, 0, '');

/*
    Laudecir - 16/04/2015 - #9686
*/
INSERT INTO programa_pagina VALUES (925, 'br.com.celk.view.vigilancia.relatorio.RelatorioAlvarasPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (519, 'Relatório de Alvarás', 925, 'N');
INSERT INTO programa_web_pagina VALUES (933, 519, 925);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (702,'Relatório de Alvarás','relatorioAlvaras',658,519,307,0,0);

/*
    Sulivan - 16/04/2015 - #9762
*/
alter table agenda_cirurgia add cd_sala     INT8     not null;

alter table agenda_cirurgia
   add constraint FK_AG_CIRUR_REF_SALA foreign key (cd_sala)
   references sala_unidade (cd_sala)
   on delete restrict on update restrict;

alter table agenda_cirurgia add hora_final     timestamp     not null;

/*
    Sulivan - 16/04/2015 - #9770
*/
INSERT INTO programa_pagina VALUES (924, 'br.com.celk.view.hospital.consulta.agendamentocirurgico.ConsultaAgendamentoCirurgicoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (518, 'Consulta de Agendamentos Cirúrgicos', 924, 'N');
INSERT INTO programa_web_pagina VALUES (932, 518, 924);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (701,'Consulta de Agendamentos Cirúrgicos','consultaAgendamentosCirurgicos',382,518,353,0,0);
INSERT INTO programa_pagina VALUES (926, 'br.com.celk.view.hospital.consulta.agendamentocirurgico.EdicaoAgendamentoCirurgicoPage', 'N');
INSERT INTO programa_web_pagina VALUES (934, 518, 926);

alter table agenda_cirurgia rename column servicos_especias to servicos_especiais;

/*
    PostgreSQL
    Everton - 17/04/2015 - #9723
*/
/*==============================================================*/
/* Table: cadastro_indicador                                    */
/*==============================================================*/
create table cadastro_indicador (
    cd_indicador         INT8                 not null,
    data_exercicio       date                 not null,
    cd_tp_indicador      INT8                 not null,
    objetivo             VARCHAR              not null,
    diretrizes           VARCHAR              not null,
    acoes                VARCHAR              not null,
    meta                 VARCHAR              not null,
    prazo_meta           date                 not null,
    realizado            VARCHAR              null,
    version              INT8                 not null
);

alter table cadastro_indicador
        add constraint PK_CADASTRO_INDICADOR primary key (cd_indicador);

alter table cadastro_indicador
        add constraint FK_CAD_IND_REF_TP_INDICADOR foreign key (cd_tp_indicador)
        references tipo_indicador (cd_tp_indicador)
        on delete restrict on update restrict;

/*
    Laudecir - 17/04/2015 - #9723
*/
INSERT INTO programa_pagina VALUES (929, 'br.com.celk.view.indicadores.cadastro.indicador.ConsultaIndicadorPage', 'N');
INSERT INTO programa_pagina VALUES (930, 'br.com.celk.view.indicadores.cadastro.indicador.CadastroIndicadorPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (521, 'Indicadores', 929, 'N');
INSERT INTO programa_web_pagina VALUES (937, 521, 929);
INSERT INTO programa_web_pagina VALUES (938, 521, 930);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (704,'Metas','metas',184,null,null,0,0);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (705,'Cadastro','cadastro',704,null,184,0,0);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (706,'Indicadores','indicadores',705,521,184,0,0);

/*
    Diego - 16/04/2015 - #9726
*/
INSERT INTO programa_pagina VALUES (927, 'br.com.celk.view.materiais.processojudicial.ConsultaProdutoSolicitadoPage', 'N');
INSERT INTO programa_pagina VALUES (928, 'br.com.celk.view.materiais.processojudicial.CadastroProdutoSolicitadoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (520, 'Processo Judicial', 927, 'N');
INSERT INTO programa_web_pagina VALUES (935, 520, 928);
INSERT INTO programa_web_pagina VALUES (936, 520, 927);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (703,'Processo Judicial','processoJudicial',166,520,14,0,0);

/*
    Diego - 17/04/2015 - #9732
*/

INSERT INTO programa_pagina VALUES (931, 'br.com.celk.view.materiais.dispensacao.medicamentojudicial.DispensacaoMedicamentoJudicialStep1Page', 'N');
INSERT INTO programa_pagina VALUES (932, 'br.com.celk.view.materiais.dispensacao.medicamentojudicial.DispensacaoMedicamentoJudicialStep2Page', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (522, 'Dispensação Medicamento Judicial', 931, 'N');
INSERT INTO programa_web_pagina VALUES (939, 522, 931);
INSERT INTO programa_web_pagina VALUES (940, 522, 932);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (707,'Dispensação de Medicamento Judicial','dispensacaoMedicamentoJudicial',174,522,14,0,0);

drop table if exists dispensacao_geral;
drop table produto_solicitado_movimento;
drop table produto_solicitado;
/*==============================================================*/
/* Table: produto_solicitado                                    */
/*==============================================================*/
create table produto_solicitado (
cd_prod_solic        INT8                 not null,
cd_usu_cadsus        NUMERIC(8)           not null,
cod_pro              CHAR(13)             not null,
cd_assistente        INT2                 null,
posologia            VARCHAR(50)          not null,
qtdade               NUMERIC(12,2)        not null,
status               INT2                 not null,
dt_cadastro          date                 not null,
cd_usuario           NUMERIC(6)           not null,
dt_usuario           TIMESTAMP            not null,
cd_usuario_bai       NUMERIC(6)           null,
cd_profissional      INT4                 not null,
empresa              INT4                 not null,
dt_baixa             TIMESTAMP            null,
ds_motivo            VARCHAR(100)         null,
nr_receita           VARCHAR(20)          null,
dt_receita           date                 null,
origem               INT2                 not null,
qtdade_prescrita     NUMERIC(12,2)        null,
version              INT8                 not null
);

alter table produto_solicitado
   add constraint PK_PRODUTO_SOLICITADO primary key (cd_prod_solic);

alter table produto_solicitado
   add constraint FK_PRO_JUD_REF_ASS_SOC foreign key (cd_assistente)
      references assistente_social (cd_assistente)
      on delete restrict on update restrict;

alter table produto_solicitado
   add constraint FK_PRO_JUD_REF_CADSUS foreign key (cd_usu_cadsus)
      references usuario_cadsus (cd_usu_cadsus)
      on delete restrict on update restrict;

alter table produto_solicitado
   add constraint FK_PRO_JUD_REF_EMPRESA foreign key (empresa)
      references empresa (empresa)
      on delete restrict on update restrict;

alter table produto_solicitado
   add constraint FK_PRO_JUD_REF_PROD foreign key (cod_pro)
      references produtos (cod_pro)
      on delete restrict on update restrict;

alter table produto_solicitado
   add constraint FK_PRO_JUD_REF_PROFISSIONAL foreign key (cd_profissional)
      references profissional (cd_profissional)
      on delete restrict on update restrict;

alter table produto_solicitado
   add constraint FK_PRO_JUD_REF_USUARIO foreign key (cd_usuario)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

alter table produto_solicitado
   add constraint FK_PRO_JUD_REF_USU_BAI foreign key (cd_usuario_bai)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;
/*==============================================================*/
/* Table: produto_solicitado_movimento                          */
/*==============================================================*/
create table produto_solicitado_movimento (
id                   INT4                 not null,
dt_movimento         TIMESTAMP            not null,
cd_usuario           NUMERIC(6)           not null,
qtdade               NUMERIC(12,2)        not null,
empresa              INT4                 not null,
cd_profissional      INT4                 not null,
empresa_unidade      INT4                 not null,
posologia            VARCHAR(50)          not null,
qtdade_original      NUMERIC(12,2)        not null,
dt_cadastro          date                 not null,
nr_receita           VARCHAR(10)          null,
dt_receita           date                 null,
cd_assistente        INT2                 null,
cd_prod_solic        INT8                 not null,
responsavel          char                 null,
num_baixa            INT8                 not null,
origem               INT2                 null,
version              INT8                 not null
);

alter table produto_solicitado_movimento
   add constraint PK_PRODUTO_SOLICITADO_MOVIMENT primary key (id);

alter table produto_solicitado_movimento
   add constraint FK_PJUD_MOV_REF_ASOCIAL foreign key (cd_assistente)
      references assistente_social (cd_assistente)
      on delete restrict on update restrict;

alter table produto_solicitado_movimento
   add constraint FK_PJUD_MOV_REF_EMPRESA foreign key (empresa)
      references empresa (empresa)
      on delete restrict on update restrict;

alter table produto_solicitado_movimento
   add constraint FK_PJUD_MOV_REF_PROD foreign key (cd_prod_solic)
      references produto_solicitado (cd_prod_solic)
      on delete restrict on update restrict;

alter table produto_solicitado_movimento
   add constraint FK_PJUD_MOV_REF_PROF foreign key (cd_profissional)
      references profissional (cd_profissional)
      on delete restrict on update restrict;

alter table produto_solicitado_movimento
   add constraint FK_PJUD_MOV_REF_UNIDADE foreign key (empresa_unidade)
      references empresa (empresa)
      on delete restrict on update restrict;

alter table produto_solicitado_movimento
   add constraint FK_PJUD_MOV_REF_USUARIO foreign key (cd_usuario)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

alter table produto_solicitado_movimento rename column id to cd_prod_solic_mov;
alter table produto_solicitado_movimento alter column cd_prod_solic_mov type int8;

/*
    Sulivan - 17/04/2015 - #9684
*/
insert into agendador_processo(cd_agendador_processo, cd_processo, nm_servico, ds_servico,status,tp_processo,version) values(14,14,'Gera Agendamento Lista de Espera','geraAgendamentoListaEspera',0,4,0);

/*
    Diego - 17/04/2015 - #9724
*/
INSERT INTO programa_pagina VALUES (933, 'br.com.celk.view.indicadores.relatorio.relacaometas.RelatorioRelacaoMetasPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (523, 'Relação das metas', 933, 'N');
INSERT INTO programa_web_pagina VALUES (941, 523, 933);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (708,'Relatórios','relatorios',704,null,184,0,0);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (709,'Relação das metas','relacaoMetas',708,523,184,0,0);

/*
    Laudecir - 17/04/2015 - #9773
*/
INSERT INTO programa_pagina VALUES (934, 'br.com.celk.view.hospital.relatorio.RelatorioAgendamentosCirurgicosPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (524, 'Relatório de Agendamentos Cirúrgicos', 934, 'N');
INSERT INTO programa_web_pagina VALUES (942, 524, 934);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (710,'Agendamentos Cirúrgicos','agendamentosCirurgicos',392,524,353,0,0);

/*
    Diego - 17/04/2015 - #9661
*/
alter table agenda add column cd_gerenciador_arquivo int8;
alter table agenda
   add constraint FK_GERENCIADOR_ARQUIVO foreign key (cd_gerenciador_arquivo)
      references gerenciador_arquivo (cd_gerenciador_arquivo)
      on delete restrict on update restrict;
alter table agenda add column flag_visualiza_agendar int2;

/*
    PostgreSQL
    Everton - 22/04/2015 - #9642
*/

alter table exame_apac add status               INT2;
update exame_apac set status = 0;
alter table exame_apac alter status set not null;

/*==============================================================*/
/* Table: im_solic_agend_to_exame_apac                          */
/*==============================================================*/
create table im_solic_agend_to_exame_apac (
cd_im_apac           INT8                 not null,
cd_exame_apac        INT8                 not null,
cd_solicitacao       INT8                 not null,
version              INT8                 not null
);

alter table im_solic_agend_to_exame_apac
   add constraint PK_IM_SOLIC_AGEND_TO_EXAME_APA primary key (cd_im_apac);

alter table im_solic_agend_to_exame_apac
   add constraint fk_im_sol_apac_ref_exame_apac foreign key (cd_exame_apac)
      references exame_apac (cd_exame_apac)
      on delete restrict on update restrict;

alter table im_solic_agend_to_exame_apac
   add constraint fk_im_sol_apac_ref_sol_ag foreign key (cd_solicitacao)
      references solicitacao_agendamento (cd_solicitacao)
      on delete restrict on update restrict;

create sequence seq_im_solic_agend_to_exame_apac;

/*
    Diego - 22/04/2015 - #9733
*/

INSERT INTO programa_pagina VALUES (935, 'br.com.celk.view.materiais.relatorios.judicial.ConsultaImpressaoReciboJudicialPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (525, 'Recibo de Entrega Judicial', 935, 'N');
INSERT INTO programa_web_pagina VALUES (943, 525, 935);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (711,'Judicial','judicial',370,null,null,0,0);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (712,'Recibo de Entrega Judicial','reciboEntregaJudicial',711,525,14,0,0);

/*
    Sulivan - 22/04/2015 - #9734
*/
INSERT INTO programa_pagina VALUES (937, 'br.com.celk.view.materiais.relatorios.judicial.RelatorioRelacaoMedicamentoJudicialPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (527, 'Relação do Medicamento Judicial', 937, 'N');
INSERT INTO programa_web_pagina VALUES (945, 527, 937);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (716,'Relação do Medicamento Judicial','relacaoMedicamentoJudicial',711,527,14,0,0);

/*
    Wilson - 22/04/2015 - #9721
*/
INSERT INTO programa_pagina VALUES (936, 'br.com.celk.view.vacina.processo.pni.GeracaoPNIPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (526, 'Geração PNI', 936, 'N');
INSERT INTO programa_web_pagina VALUES (944, 526, 936);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (713,'PNI','pni',18,null,null,0,0);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (714,'Processo','processo',713,null,18,0,0);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (715,'Geração PNI','geracaoPNI',714,526,18,0,0);

/*
    Laudecir - 24/04/2015 - #9685
*/
create table coordenadas (
    cd_coordenada	int8            not null,
    ds_profissional	varchar(50)     not null,
    ds_veiculo          varchar(20)     null,
    latitude            numeric(8,6)    not null,
    longitude           numeric(8,6)    not null,
    dt_registro         timestamp       not null,
    version             int8            not null
);

alter table coordenadas
        add constraint PK_COORDENADAS primary key (cd_coordenada);

insert into coordenadas values(1, 'João da Silva', 'VW Gol', -28.685136, -49.369415, '2015-03-12 16:20:05', 0);
insert into coordenadas values(2, 'José de Souza', 'VW Gol', -28.675538, -49.372499, '2015-04-06 15:12:45', 0);
insert into coordenadas values(3, 'João da Silva', 'Fiat Uno', -28.692277, -49.379216, '2015-04-02 13:48:02', 0);
insert into coordenadas values(4, 'José de Souza', 'Fiat Uno', -28.681688, -49.380729, '2015-01-24 17:53:30', 0);
insert into coordenadas values(5, 'Mário de Oliveira', 'Renault Clio', -28.675533, -49.382617, '2015-01-29 10:13:55', 0);
insert into coordenadas values(18, 'Antônio Vieira', 'Renault Clio', -28.682130, -49.334846, '2015-04-27 15:23:55', 0);
insert into coordenadas values(19, 'Mário de Oliveira', 'Fiat Pálio', -28.700992, -49.368776, '2015-03-23 12:27:33', 0);
insert into coordenadas values(20, 'Antônio Vieira', 'Fiat Pálio', -28.719360, -49.382337, '2015-04-19 13:58:31', 0);

insert into coordenadas values(6, 'Maria de Lurdes', null, -28.704332, -49.401974, '2015-04-01 13:01:10', 0);
insert into coordenadas values(13, 'Maria de Lurdes', null, -28.703654, -49.398455, '2015-04-01 13:10:33', 0);
insert into coordenadas values(14, 'Maria de Lurdes', null, -28.703843, -49.400300, '2015-04-01 13:25:45', 0);
insert into coordenadas values(7, 'Joana Rocha', null, -28.685509, -49.394035, '2015-04-10 15:20:11', 0);
insert into coordenadas values(10, 'Joana Rocha', null, -28.683796, -49.394206, '2015-04-10 15:40:49', 0);
insert into coordenadas values(17, 'Joana Rocha', null, -28.684719, -49.391996, '2015-04-10 15:59:13', 0);
insert into coordenadas values(8, 'Fernanda Abreu', null, -28.684587, -49.339936, '2015-04-22 14:17:18', 0);
insert into coordenadas values(11, 'Fernanda Abreu', null, -28.683213, -49.342039, '2015-04-22 14:26:38', 0);
insert into coordenadas values(16, 'Fernanda Abreu', null, -28.687731, -49.339915, '2015-04-22 14:37:24', 0);
insert into coordenadas values(9, 'Jurema de Barros', null, -28.691407, -49.431162, '2015-04-24 16:05:03', 0);
insert into coordenadas values(12, 'Jurema de Barros', null, -28.692988, -49.429682, '2015-04-24 16:22:17', 0);
insert into coordenadas values(15, 'Jurema de Barros', null, -28.691407, -49.428158, '2015-04-24 16:35:34', 0);

INSERT INTO programa_pagina (cd_prg_pagina,cam_pagina,publico,version) VALUES (916,'br.com.celk.view.frota.rota.RotasPage','N',0);
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo,version) VALUES (513,'Rotas',916,'N',0);
INSERT INTO programa_web_pagina (cd_prg_web_pagina,cd_prg_web,cd_prg_pagina,version) VALUES (924,513,916,0);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (689,'Rotas','rotas',44,null,null,0,0);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (690,'','',689,null,44,0,0);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (691,'Rotas','rotas',690,513,44,0,0);

INSERT INTO programa_pagina VALUES (938, 'br.com.celk.view.siab.relatorios.PercursoVisitasPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (528, 'Percurso das Visitas', 938, 'N');
INSERT INTO programa_web_pagina VALUES (946, 528, 938);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (717,'Percurso das Visitas','percursoVisitas',333,528,null,0,0);

/*
    Claudio - 27/04/2015
*/
alter table usuario_cadsus add prontuario varchar(30);

CREATE OR REPLACE FUNCTION ftg_prontuario_cadsus()
  RETURNS trigger AS
$BODY$
BEGIN
if (TG_OP = 'INSERT' or TG_OP = 'UPDATE') then
	if (new.status = 2) then
		update usuario_cadsus set
		prontuario = new.prontuario,
		reside_desde = new.reside_desde
		where cd_usu_cadsus = new.cd_usu_cadsus;
	end if;
end if;
return new;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;

CREATE TRIGGER tg_prontuario_cadsus
  AFTER INSERT OR UPDATE
  ON usuario_cadsus_domicilio
  FOR EACH ROW
  EXECUTE PROCEDURE ftg_prontuario_cadsus();

alter table usuario_cadsus disable trigger tg_version_all;
update usuario_cadsus_domicilio set cd_domicilio = cd_domicilio where status = 2;
alter table usuario_cadsus enable trigger tg_version_all;

/*
    Diego
    Se inserir um tipo de documento com codigo maior que precision 10^6 da erro
*/

alter table parametro_material alter column cod_tip_doc_sai_jud type numeric(9);
alter table parametro_material alter column cod_tip_doc_ent_jud type numeric(9);