SET application_name = 'flyway|********';

ALTER TABLE empresa ADD COLUMN flag_exibir_estoque INT2 NULL;
AlTER TABLE auditschema.empresa ADD COLUMN flag_exibir_estoque INT2 NULL;

/*
    Sulivan - 15/09/2017 - #15937
*/
ALTER TABLE tipo_procedimento ADD COLUMN dt_integracao_inovamfri timestamp NULL;
AlTER TABLE auditschema.tipo_procedimento ADD COLUMN dt_integracao_inovamfri timestamp NULL;

update agendador_processo set nm_servico = 'Gerais Integração InovAmfri', ds_servico = 'Executa o processo de integração InovAmfri: Unidades, Tipo de Procedimentos.' where cd_agendador_processo = 38;

/*
   Everton - 18/09/2017
*/
alter table solicitacao_agendamento disable trigger user;
update solicitacao_agendamento set dt_integracao_inovamfri = current_timestamp where dt_integracao_inovamfri is null and dt_solicitacao < '2015-01-01';
alter table solicitacao_agendamento enable trigger user;

update parametro_gem set valor = 'http://inovamfri.celk.com.br:7080/api/1.3/' where cd_modulo = 32 and parametro = 'urlConexao';