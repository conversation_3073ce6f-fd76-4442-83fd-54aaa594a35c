/*
    Claudio - 25/02/2014
*/
alter table usuario_cadsus add chave_biometria text;

/*
 PostgreSQL
 Everton - 27/02/2014 - #7239
*/

alter table receituario_item alter posologia type varchar(512);

/*
 PostgreSQL
 Everton - 28/02/2014 - #7264
*/

alter table usuario_cadsus ADD cd_endereco          INT8                 null;
alter table usuario_cadsus
   add constraint FK_CADSUS_REF_ENDERECO foreign key (cd_endereco)
      references endereco_usuario_cadsus (cd_endereco)
      on delete restrict on update restrict;

CREATE INDEX IDX_USU_END ON usuario_cadsus_endereco (cd_usu_cadsus);
update usuario_cadsus T1 
   set cd_endereco = (select cd_endereco from usuario_cadsus_endereco where cd_usu_cadsus = t1.cd_usu_cadsus and coalesce(status,0) = 0 order by dt_alteracao desc limit 1);
DROP INDEX IDX_USU_END;

update endereco_usuario_cadsus en
   set empresa = (SELECT t3.empresa FROM endereco_domicilio t1, equipe_micro_area t2, equipe t3 
                       where t1.cd_eqp_micro_area = t2.cd_eqp_micro_area 
                         and t3.cod_cid = t2.eqp_cod_cid 
                         and t3.cd_area = t2.eqp_cd_area 
                         and t3.seq_equipe = t2.eqp_seq_equipe
                         and t1.cd_endereco = en.cd_endereco)
 where empresa is null and exists (select 1 from endereco_domicilio where cd_endereco = en.cd_endereco);

/*
 PostgreSQL
 Everton - 27/02/2014 - #7263
*/
alter table ped_transf_licitacao_item add prc_unitario NUMERIC(12,4) null;

/*
    PostgreSQL
    Everton - 28/02/2014 - #7265
*/
ALTER TABLE cidade_bairro ADD empresa INT4 null;
alter table cidade_bairro
        add constraint FK_CID_BAIRRO_REF_EMPRESA foreign key (empresa)
        references empresa (empresa)
        on delete restrict on update restrict;

/*
    Laudecir - 28/02/2014 - #7267
*/
INSERT INTO programa_pagina_permissao VALUES(98, 18, 122, 0, 'manutencaoPreco');
INSERT INTO programa_pagina_permissao VALUES(99, 18, 158, 0, 'manutencaoPreco');
INSERT INTO programa_pagina_permissao VALUES(100, 18, 299, 0, 'manutencaoPreco');
INSERT INTO programa_pagina VALUES (614, 'br.com.celk.view.materiais.produtomanutencaopreco.ManutencaoPrecoProdutoMateriaisPage', 'N');
INSERT INTO programa_web_pagina VALUES (616, 74, 614);
INSERT INTO programa_web_pagina VALUES (617, 94, 614);
INSERT INTO programa_web_pagina VALUES (618, 179, 614);

/*
 PostgreSQL
 Everton - 06/03/2014 - #7271
*/

drop table profissional_unidade;
drop table profissional_tp_atendimento;

/*
 PostgreSQL
 Everton - 07/03/2014 - #7278
*/

alter table usuario_cadsus_dado drop vacina_em_dia;

/*
 PostgreSQL
 Everton - 07/03/2014
*/

drop table observacao_analise;
drop table elo_ligacao_nota_oc;
drop table itens_ordem_compra;
drop table ordem_compra;
drop table fornecedor_potencial;


/*
 PostgreSQL
 Everton - 10/03/2014 - #7285
*/

alter table equipe_micro_area drop constraint fk_eqp_marea_ref_eqp_pro;
alter table siab_ssa2 drop constraint FK_SIAB_SSA_REF_EQ_PRO;
alter table siab_pma2 drop constraint FK_SIAB_PMA_REF_EQ_PRO;
alter table siab_pma2c drop constraint FK_SIAB_PMA2C_REF_EQ_PRO;
alter table equipe_profissional drop constraint PK_EQUIPE_PROFISSIONAL;

alter table equipe_profissional drop constraint FK_EQP_PROF_REF_EQUIPE;
alter table equipe drop constraint PK_EQUIPE;

create sequence codigo_equipe;
alter table equipe add cd_equipe int8;
update equipe set cd_equipe = nextval('codigo_equipe');
alter table equipe alter cd_equipe set not null;
alter table equipe add constraint PK_EQUIPE primary key (cd_equipe);
drop sequence codigo_equipe;

create sequence codigo_equipe;
alter table equipe_profissional add cd_equipe_profissional INT8;
update equipe_profissional set cd_equipe_profissional = nextval('codigo_equipe');
alter table equipe_profissional alter cd_equipe_profissional set not null;
alter table equipe_profissional add constraint PK_EQUIPE_PROFISSIONAL primary key (cd_equipe_profissional);
drop sequence codigo_equipe;

alter table equipe_profissional add cd_equipe INT8;
update equipe_profissional t1
   set cd_equipe = (select cd_equipe from equipe 
                     where cod_cid         = t1.cod_cid
                       and cd_area         = t1.cd_area
                       and seq_equipe      = t1.seq_equipe);
alter table equipe_profissional alter cd_equipe set not null;
alter table equipe_profissional
   add constraint FK_EQP_PROF_REF_EQUIPE foreign key (cd_equipe)
      references equipe (cd_equipe)
      on delete restrict on update restrict;

-----------------------------------

alter table equipe_micro_area add cd_equipe_profissional INT8;
update equipe_micro_area t1 
   set cd_equipe_profissional = (select cd_equipe_profissional from equipe_profissional 
                                  where cd_profissional = t1.eqp_cd_profissional
                                    and cod_cid         = t1.eqp_cod_cid
                                    and cd_area         = t1.eqp_cd_area
                                    and seq_equipe      = t1.eqp_seq_equipe);
alter table equipe_micro_area
   add constraint FK_EQP_MAREA_REF_EQP_PRO foreign key (cd_equipe_profissional)
      references equipe_profissional (cd_equipe_profissional);

alter table siab_ssa2 add cd_equipe_profissional INT8;
update siab_ssa2 t1 
   set cd_equipe_profissional = (select cd_equipe_profissional from equipe_profissional 
                                  where cd_profissional = t1.cd_profissional
                                    and cod_cid         = t1.cod_cid
                                    and cd_area         = t1.cd_area
                                    and seq_equipe      = t1.seq_equipe);
alter table siab_ssa2 alter cd_equipe_profissional set not null;
alter table siab_ssa2
   add constraint FK_SIAB_SSA_REF_EQ_PRO foreign key (cd_equipe_profissional)
      references equipe_profissional (cd_equipe_profissional)
      on delete restrict on update restrict;

alter table siab_pma2 add cd_equipe_profissional INT8;
update siab_pma2 t1 
   set cd_equipe_profissional = (select cd_equipe_profissional from equipe_profissional 
                                  where cd_profissional = t1.cd_profissional
                                    and cod_cid         = t1.cod_cid
                                    and cd_area         = t1.cd_area
                                    and seq_equipe      = t1.seq_equipe);
alter table siab_pma2 alter cd_equipe_profissional set not null;                                    
alter table siab_pma2
   add constraint FK_SIAB_PMA_REF_EQ_PRO foreign key (cd_equipe_profissional)
      references equipe_profissional (cd_equipe_profissional)
      on delete restrict on update restrict;

alter table siab_pma2c add cd_equipe_profissional INT8;
update siab_pma2c t1 
   set cd_equipe_profissional = (select cd_equipe_profissional from equipe_profissional 
                                  where cd_profissional = t1.cd_profissional
                                    and cod_cid         = t1.cod_cid
                                    and cd_area         = t1.cd_area
                                    and seq_equipe      = t1.seq_equipe);
alter table siab_pma2c alter cd_equipe_profissional set not null;
alter table siab_pma2c
   add constraint FK_SIAB_PMA2C_REF_EQ_PRO foreign key (cd_equipe_profissional)
      references equipe_profissional (cd_equipe_profissional)
      on delete restrict on update restrict;

alter table equipe_micro_area drop eqp_cd_profissional;
alter table equipe_micro_area drop eqp_cod_cid;
alter table equipe_micro_area drop eqp_cd_area;
alter table equipe_micro_area drop eqp_seq_equipe;

alter table siab_ssa2 drop cd_profissional;
alter table siab_ssa2 drop cod_cid;
alter table siab_ssa2 drop cd_area;
alter table siab_ssa2 drop seq_equipe;

alter table siab_pma2 drop cd_profissional;
alter table siab_pma2 drop cod_cid;
alter table siab_pma2 drop cd_area;
alter table siab_pma2 drop seq_equipe;

alter table siab_pma2c drop cd_profissional;
alter table siab_pma2c drop cod_cid;
alter table siab_pma2c drop cd_area;
alter table siab_pma2c drop seq_equipe;

alter table equipe_profissional drop cod_cid;
alter table equipe_profissional drop cd_area;
alter table equipe_profissional drop seq_equipe;

/*
 PostgreSQL
 Everton - 10/03/2014 - #7257
*/

update agenda_gra_ate_horario set cd_ag_gra_atendimento = null where status = 3 and cd_ag_gra_atendimento is not null;

/*
    Sulivan - 07/03/2014 - #7281
*/
INSERT INTO programa_pagina VALUES (615, 'br.com.celk.view.materiais.compras.ConsultaOrdemCompraPage', 'N');
INSERT INTO programa_pagina VALUES (616, 'br.com.celk.view.materiais.compras.CadastroOrdemCompraStep1Page', 'N');
INSERT INTO programa_pagina VALUES (617, 'br.com.celk.view.materiais.compras.CadastroOrdemCompraStep2Page', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (353, 'Ordem de Compra', 615, 'N');
INSERT INTO programa_web_pagina VALUES (619, 353, 615);
INSERT INTO programa_web_pagina VALUES (620, 353, 616);
INSERT INTO programa_web_pagina VALUES (621, 353, 617);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (490,'Compras','compras',14,null,null,0);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (491,'Cadastro','cadastro',490,null,14,0);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (492,'Ordem de Compra','ordemCompra',491,353,14,0);



/*
    PostgreSQL
    Everton - 07/03/2014 - #7281
*/

/*==============================================================*/
/* Table: ordem_compra                                          */
/*==============================================================*/
create table ordem_compra (
cd_ordem_compra      INT8                 not null,
empresa              INT4                 not null,
cod_pessoa           INT8                 not null,
cd_usuario           NUMERIC(6)           not null,
dt_cadastro          TIMESTAMP            not null,
observacao           VARCHAR(512)         null,
num_pregao           VARCHAR(20)          null,
status               INT2                 not null,
version              INT8                 not null
);

comment on table ordem_compra is
'Tabela Ordem de Compra';

comment on column ordem_compra.cod_pessoa is
'Codigo da Pessoa';

alter table ordem_compra
   add constraint PK_ORDEM_COMPRA primary key (cd_ordem_compra);

alter table ordem_compra
   add constraint FK_OC_REF_EMPRESA foreign key (empresa)
      references empresa (empresa)
      on delete restrict on update restrict;

alter table ordem_compra
   add constraint FK_OC_REF_PESSOA foreign key (cod_pessoa)
      references Pessoa (cod_pessoa)
      on delete restrict on update restrict;

alter table ordem_compra
   add constraint FK_OC_REF_USUARIO foreign key (cd_usuario)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

/*==============================================================*/
/* Table: ordem_compra_item                                     */
/*==============================================================*/
create table ordem_compra_item (
cd_oc_item           INT8                 not null,
cod_pro              VARCHAR(13)          not null,
qtd_compra           NUMERIC(12,2)        not null,
preco_unitario       NUMERIC(12,4)        not null,
status               INT4                 not null,
qtd_recebida         NUMERIC(12,2)        null,
nr_item_pregao       VARCHAR(10)          null,
cd_usuario           NUMERIC(6)           null,
dt_usuario           TIMESTAMP            not null default 'now()',
dt_cancelamento      TIMESTAMP            null,
cd_usu_can           NUMERIC(6)           null,
qtd_cancelada        NUMERIC(12,2)        null,
version              INT8                 not null
);

comment on table ordem_compra_item is
'Tabela itens da ordem de compra';

alter table ordem_compra_item
   add constraint PK_ORDEM_COMPRA_ITEM primary key (cd_oc_item);

alter table ordem_compra_item
   add constraint FK_OC_IT_REF_PRODUTO foreign key (cod_pro)
      references produtos (cod_pro)
      on delete restrict on update restrict;

alter table ordem_compra_item
   add constraint FK_OC_IT_REF_USUARIO foreign key (cd_usuario)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

alter table ordem_compra_item
   add constraint FK_OC_IT_REF_USU_CAN foreign key (cd_usu_can)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

/*
    Laudecir - 11/03/2014 - #7286
*/
INSERT INTO programa_pagina VALUES (619, 'br.com.celk.view.basico.equipe.ConsultaEquipePage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (355, 'Equipe', 619, 'N');
INSERT INTO programa_web_pagina VALUES (623, 355, 619);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (494, 'Equipe', 'equipe', 177, 355, null, 0);
INSERT INTO programa_pagina VALUES (620, 'br.com.celk.view.basico.equipe.CadastroEquipeStep1Page', 'N');
INSERT INTO programa_web_pagina VALUES (624, 355, 620);
INSERT INTO programa_pagina VALUES (621, 'br.com.celk.view.basico.equipe.CadastroEquipeStep2Page', 'N');
INSERT INTO programa_web_pagina VALUES (625, 355, 621);
INSERT INTO programa_pagina VALUES (622, 'br.com.celk.view.basico.equipe.DetalhesEquipePage', 'N');
INSERT INTO programa_web_pagina VALUES (626, 355, 622);

/*
    Sulivan - 07/03/2014 - #7281
*/
INSERT INTO programa_pagina VALUES (623, 'br.com.celk.view.materiais.compras.DetalhesOrdemCompraPage', 'N');
INSERT INTO programa_web_pagina VALUES (627, 353, 623);

/*
    Pietro - 11/03/2014 - #7287
*/
INSERT INTO programa_pagina VALUES (618, 'br.com.celk.view.siab.microarea.ConsultaMicroAreaPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (354, 'Cadastro Micro-Área', 618, 'N');
INSERT INTO programa_web_pagina VALUES (622, 354, 618);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (493,'Cadastro da Micro-Área','cadastroMicroArea',171,354,null,0);

/*
    Pietro  - 12/03/2014 - #7266
*/
alter table cidade_bairro drop constraint pk_cidade_bairro;
alter table cidade_bairro add cd_bairro int8;
create sequence bairro_temp;
update cidade_bairro set cd_bairro = nextval('bairro_temp');
alter table cidade_bairro alter column cd_bairro set not null;
create unique index idx_cidade_bairro on cidade_bairro (ds_bairro, cod_cid);
alter table cidade_bairro add constraint fk_cidade_bairro primary key (cd_bairro);
drop sequence bairro_temp;

delete from cidade_bairro where cd_bairro in (
SELECT t1.cd_bairro FROM cidade_bairro t1, cidade_bairro t2 
where upper(t1.ds_bairro) = upper(t2.ds_bairro) 
and t1.cd_bairro <> t2.cd_bairro
and t1.cod_cid = t2.cod_cid
group by t1.cd_bairro
having t1.cd_bairro <> 
(SELECT max(t1.cd_bairro) FROM cidade_bairro t1, cidade_bairro t2 
where upper(t1.ds_bairro) = upper(t2.ds_bairro) 
and t1.cd_bairro <> t2.cd_bairro
and t1.cod_cid = t2.cod_cid));

update cidade_bairro set ds_bairro = upper(ds_bairro);

ALTER TABLE equipe_micro_area ALTER COLUMN version SET DEFAULT 0;

/*
    Sulivan - 13/03/2014 - #7267
*/
update programa_pagina_permissao set cd_permissao = 19 where cd_prog_pag_perm in(98,99,100);

/*
    PostgreSQL
    Everton - 13/03/2014 - #7292
*/

/*==============================================================*/
/* Table: situacao_dente                                        */
/*==============================================================*/
create table situacao_dente (
cd_situacao          INT8                 not null,
ds_situacao          VARCHAR(30)          not null,
referencia           VARCHAR(10)          not null,
ordem                INT2                 null,
tp_situacao          INT2                 not null,
version              INT8                 not null
);

comment on column situacao_dente.tp_situacao is
'Histórico / Procedimento';

alter table situacao_dente
   add constraint PK_SITUACAO_DENTE primary key (cd_situacao);

delete from dente;
alter table dente alter cd_dente type int8;
alter table dente alter nm_dente set not null;

insert into dente values (11, '11', null, 0);
insert into dente values (12, '12', null, 0);
insert into dente values (13, '13', null, 0);
insert into dente values (14, '14', null, 0);
insert into dente values (15, '15', null, 0);
insert into dente values (16, '16', null, 0);
insert into dente values (17, '17', null, 0);
insert into dente values (18, '18', null, 0);

insert into dente values (21, '21', null, 0);
insert into dente values (22, '22', null, 0);
insert into dente values (23, '23', null, 0);
insert into dente values (24, '24', null, 0);
insert into dente values (25, '25', null, 0);
insert into dente values (26, '26', null, 0);
insert into dente values (27, '27', null, 0);
insert into dente values (28, '28', null, 0);

insert into dente values (51, '51', null, 0);
insert into dente values (52, '52', null, 0);
insert into dente values (53, '53', null, 0);
insert into dente values (54, '54', null, 0);
insert into dente values (55, '55', null, 0);

insert into dente values (61, '61', null, 0);
insert into dente values (62, '62', null, 0);
insert into dente values (63, '63', null, 0);
insert into dente values (64, '64', null, 0);
insert into dente values (65, '65', null, 0);

insert into dente values (81, '81', null, 0);
insert into dente values (82, '82', null, 0);
insert into dente values (83, '83', null, 0);
insert into dente values (84, '84', null, 0);
insert into dente values (85, '85', null, 0);

insert into dente values (71, '71', null, 0);
insert into dente values (72, '72', null, 0);
insert into dente values (73, '73', null, 0);
insert into dente values (74, '74', null, 0);
insert into dente values (75, '75', null, 0);

insert into dente values (41, '41', null, 0);
insert into dente values (42, '42', null, 0);
insert into dente values (43, '43', null, 0);
insert into dente values (44, '44', null, 0);
insert into dente values (45, '45', null, 0);
insert into dente values (46, '46', null, 0);
insert into dente values (47, '47', null, 0);
insert into dente values (48, '48', null, 0);

insert into dente values (31, '31', null, 0);
insert into dente values (32, '32', null, 0);
insert into dente values (33, '33', null, 0);
insert into dente values (34, '34', null, 0);
insert into dente values (35, '35', null, 0);
insert into dente values (36, '36', null, 0);
insert into dente values (37, '37', null, 0);
insert into dente values (38, '38', null, 0);

/*==============================================================*/
/* Table: atendimento_odonto_ficha                              */
/*==============================================================*/
create table atendimento_odonto_ficha (
cd_ficha_odonto      INT4                 not null,
nr_atendimento       INT8                 not null,
dt_inicio_tratamento date                 not null,
observacao           VARCHAR(200)         null,
status               INT2                 not null,
tratamento_medico    INT2                 not null,
doente_hospitalizado INT2                 not null,
doente_hospitalizado_motivo VARCHAR(50)          null,
usa_medicamento      INT2                 not null,
usa_medicamento_qual VARCHAR(50)          null,
prb_cardiacos        INT2                 not null,
prb_figado           INT2                 not null,
prb_rins             INT2                 not null,
prb_pulmonar         INT2                 not null,
diabete              INT2                 not null,
anemia               INT2                 not null,
tontura              INT2                 not null,
hipertensao          INT2                 not null,
febre_reumatica      INT2                 not null,
hepatite             INT2                 not null,
sifilis              INT2                 not null,
tuberculose          INT2                 not null,
enfermidade_nao_mencionada INT2                 not null,
enfermidade_nao_mensionada_qual VARCHAR(50)          null,
sangrou_extracao_ferimento INT2                 not null,
alergico_medicamento INT2                 not null,
alergico_anestesia  INT2                 not null,
gravida              INT2                 null,
gravida_mes_gestacao INT2                 null,
dt_conclusao         date                 null,
obs_conclusao        VARCHAR(200)         null,
nr_atendimento_conclusao INT8                 null,
motivo_cancelamento  VARCHAR(200)         null,
dt_cancelamento      TIMESTAMP            null,
cd_usu_can           NUMERIC(6)           null,
nr_atendimento_can   INT8                 null,
version              INT8                 not null
);

alter table atendimento_odonto_ficha
   add constraint PK_ATENDIMENTO_ODONTO_FICHA primary key (cd_ficha_odonto);

alter table atendimento_odonto_ficha
   add constraint FK_ODONTO_FICHA_REF_ATEND foreign key (nr_atendimento)
      references atendimento (nr_atendimento)
      on delete restrict on update restrict;

alter table atendimento_odonto_ficha
   add constraint FK_ODONTO_FICHA_REF_ATE_CAN foreign key (nr_atendimento_can)
      references atendimento (nr_atendimento)
      on delete restrict on update restrict;

alter table atendimento_odonto_ficha
   add constraint FK_ODONTO_FICHA_REF_ATE_CONC foreign key (nr_atendimento_conclusao)
      references atendimento (nr_atendimento)
      on delete restrict on update restrict;

alter table atendimento_odonto_ficha
   add constraint FK_ODONTO_FICHA_REF_USU_CAN foreign key (cd_usu_can)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

/*==============================================================*/
/* Table: atendimento_odonto_plano                              */
/*==============================================================*/
create table atendimento_odonto_plano (
cd_atendimento_plano INT8                 not null,
cd_ficha_odonto      INT4                 not null,
cd_dente             INT4                 not null,
face                 INT2                 null,
cd_situacao          INT8                 not null,
nr_atendimento       INT8                 not null,
observacao           VARCHAR(100)         null,
status               INT2                 not null,
dt_cadastro          TIMESTAMP            not null
);

alter table atendimento_odonto_plano
   add constraint PK_ATENDIMENTO_ODONTO_PLANO primary key (cd_atendimento_plano);

alter table atendimento_odonto_plano
   add constraint FK_ODONTO_PLANO_REF_ATEND foreign key (nr_atendimento)
      references atendimento (nr_atendimento)
      on delete restrict on update restrict;

alter table atendimento_odonto_plano
   add constraint FK_ODONTO_PLANO_REF_DENTE foreign key (cd_dente)
      references dente (cd_dente)
      on delete restrict on update restrict;

alter table atendimento_odonto_plano
   add constraint FK_ODONTO_PLANO_REF_ODO_FICHA foreign key (cd_ficha_odonto)
      references atendimento_odonto_ficha (cd_ficha_odonto)
      on delete restrict on update restrict;

alter table atendimento_odonto_plano
   add constraint FK_ODONTO_PLANO_REF_SIT_DENTE foreign key (cd_situacao)
      references situacao_dente (cd_situacao)
      on delete restrict on update restrict;

ALTER TABLE atendimento_prontuario ADD cd_ficha_odonto      INT4                 null;
alter table atendimento_prontuario
   add constraint FK_ATE_PRONT_REF_ODO_FICHA foreign key (cd_ficha_odonto)
      references atendimento_odonto_ficha (cd_ficha_odonto)
      on delete restrict on update restrict;

alter table atendimento_odonto_plano add version              INT8                 not null;

/*
    Everton - 14/03/2014 - #7281
*/

ALTER TABLE ordem_compra_item ADD cd_ordem_compra      INT8   not null;
alter table ordem_compra_item
   add constraint FK_OC_IT_REF_OC foreign key (cd_ordem_compra)
      references ordem_compra (cd_ordem_compra)
      on delete restrict on update restrict;

/*
    Laudecir - 14/03/2014 - #7286
*/
alter table equipe alter column seq_equipe drop not null;

/*
    PostgreSQL
    Everton - 14/03/2014 - #7297
*/
ALTER TABLE atendimento_odonto_plano ADD motivo_cancelamento  VARCHAR(200)  null;
ALTER TABLE atendimento_odonto_plano ADD dt_cancelamento      TIMESTAMP     null;
ALTER TABLE atendimento_odonto_plano ADD nr_atendimento_can   INT8          null;
ALTER TABLE atendimento_odonto_plano ADD nr_atendimento_exec  INT8          null;

alter table atendimento_odonto_plano
        add constraint FK_ODONTO_PLANO_REF_ATE_CAN foreign key (nr_atendimento_can)
        references atendimento (nr_atendimento)
        on delete restrict on update restrict;

alter table atendimento_odonto_plano
        add constraint FK_ODONTO_PLANO_REF_ATE_EXEC foreign key (nr_atendimento_exec)
        references atendimento (nr_atendimento)
        on delete restrict on update restrict;

/*==============================================================*/
/* Table: atendimento_odonto_execucao                           */
/*==============================================================*/
create table atendimento_odonto_execucao (
    cd_execucao          INT8           not null,
    cd_atendimento_plano INT8           not null,
    nr_atendimento       INT8           not null,
    dt_cadastro          TIMESTAMP      not null,
    ds_trabalho          VARCHAR(512)   null,
    version              INT8           not null
);

alter table atendimento_odonto_execucao
        add constraint PK_ATENDIMENTO_ODONTO_EXECUCAO primary key (cd_execucao);

alter table atendimento_odonto_execucao
        add constraint FK_ODONTO_EXEC_REF_ATEND foreign key (nr_atendimento)
        references atendimento (nr_atendimento)
        on delete restrict on update restrict;

alter table atendimento_odonto_execucao
        add constraint FK_ODONTO_EXEC_REF_ODO_PLANO foreign key (cd_atendimento_plano)
        references atendimento_odonto_plano (cd_atendimento_plano)
        on delete restrict on update restrict;

/*==============================================================*/
/* Table: situacao_dente_procedimento                           */
/*==============================================================*/
create table situacao_dente_procedimento (
    cd_situacao_procedimento    INT8        not null,
    cd_situacao                 INT8        not null,
    cd_procedimento             NUMERIC(10) not null,
    version                     INT8        not null
);

alter table situacao_dente_procedimento
        add constraint PK_SITUACAO_DENTE_PROCEDIMENTO primary key (cd_situacao_procedimento);

alter table situacao_dente_procedimento
        add constraint FK_DENTE_PROC_REF_PROCED foreign key (cd_procedimento)
        references procedimento (cd_procedimento)
        on delete restrict on update restrict;

alter table situacao_dente_procedimento
        add constraint FK_DENTE_PROC_REF_SIT_DENTE foreign key (cd_situacao)
        references situacao_dente (cd_situacao)
        on delete restrict on update restrict;

ALTER TABLE atendimento_item ADD cd_execucao INT8 null;
alter table atendimento_item
        add constraint FK_ATE_ENFITE_REF_ODO_EXEC foreign key (cd_execucao)
        references atendimento_odonto_execucao (cd_execucao)
        on delete restrict on update restrict;

ALTER TABLE atendimento_prontuario ADD cd_execucao INT8 null;
alter table atendimento_prontuario
        add constraint FK_ATE_PRONT_REF_ODO_EXEC foreign key (cd_execucao)
        references atendimento_odonto_execucao (cd_execucao)
        on delete restrict on update restrict;

/*
    Everton - 14/03/2014 - #7281
*/
ALTER TABLE atendimento_odonto_ficha ADD tratamento_medico_qual VARCHAR(50)          null;
ALTER TABLE atendimento_odonto_ficha ADD alergico_medicamento_qual VARCHAR(50)       null;
ALTER TABLE atendimento_odonto_ficha ADD alergico_anestesia_qual VARCHAR(50)        null;

/*
    Claudio - 18/03/2014
*/
update pessoa_endereco set cep = regexp_replace(cep, '[^0-9]+', '');
update empresa set cep = regexp_replace(cep, '[^0-9]+', '');
alter table pessoa alter column descricao_cliente_ant type varchar(80);

/*
    Pietro - 18/03/2014 - #7301
*/
INSERT INTO programa_pagina VALUES (624, 'br.com.celk.view.unidadesaude.odonto.ConsultaDentePage', 'N');
INSERT INTO programa_pagina VALUES (625, 'br.com.celk.view.unidadesaude.odonto.CadastroDentePage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (356, 'Cadastro do Dente', 624, 'N');
INSERT INTO programa_web_pagina VALUES (628, 356, 624);
INSERT INTO programa_web_pagina VALUES (629, 356, 625);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (495,'Cadastro do Dente','cadastroDente',324,356,143,0);

/*
    Pietro - 18/03/2014 - #7302
*/
INSERT INTO programa_pagina VALUES (626, 'br.com.celk.view.unidadesaude.odonto.ConsultaSituacaoDentePage', 'N');
INSERT INTO programa_pagina VALUES (627, 'br.com.celk.view.unidadesaude.odonto.CadastroSituacaoDentePage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (357, 'Cadastro da Situação do Dente', 626, 'N');
INSERT INTO programa_web_pagina VALUES (630, 357, 626);
INSERT INTO programa_web_pagina VALUES (631, 357, 627);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (496,'Cadastro da Situação do Dente','cadastroSituacaoDente',324,357,143,0);

/*
 PostgreSQL
 Everton - 19/03/2014
*/

ALTER TABLE usuario_cadsus_endereco ADD dt_cadastro          TIMESTAMP            null;
ALTER TABLE usuario_cadsus_endereco ADD dt_usuario           TIMESTAMP            null;
ALTER TABLE usuario_cadsus_endereco ADD cd_usuario           NUMERIC(6)           null;
alter table usuario_cadsus_endereco
   add constraint FK_CADSUS_END_REF_USUARIO foreign key (cd_usuario)
      references usuarios (cd_usuario);

update usuario_cadsus set situacao = 0 where situacao = 1 and coalesce(flag_simplificado,0) <> 1;

/*
 Claudio - 19/03/2014
*/
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (497,'Relatórios','relatorios',8,null,null,0);
INSERT INTO programa_pagina VALUES (628, 'br.com.celk.view.cadsus.relatorio.RelatorioPacientesConferenciaPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (358, 'Relação dos Pacientes para Conferência', 628, 'N');
INSERT INTO programa_web_pagina VALUES (632, 358, 628);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (498,'Relação dos Pacientes para Conferência','relacaoPacientesConferencia',497,358,0,0);


/*
 Claudio - 21/03/2014
*/
update programa_web_pagina set cd_prg_web = 326 where cd_prg_web_pagina = 557;
update programa_web_pagina set cd_prg_web = 326 where cd_prg_web_pagina = 558;
insert into programa_web_pagina select 633, 4, 557 from programa_web_pagina where not exists(
select * from programa_web_pagina where cd_prg_web_pagina = 633) limit 1;