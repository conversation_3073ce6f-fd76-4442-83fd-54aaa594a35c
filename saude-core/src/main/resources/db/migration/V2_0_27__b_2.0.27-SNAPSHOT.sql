/*
    Claudio 05/02/2013
*/
alter table registro_notas_fiscais add cd_reg_nf int8;

create sequence seq_registro_nf;

update registro_notas_fiscais set cd_reg_nf = nextval('seq_registro_nf');

alter table registro_notas_fiscais alter column cd_reg_nf set not null;

alter table registro_itens_nota_fiscal add cd_reg_nf int8;

alter table registro_itens_nota_fiscal add cd_reg_it_nf int8;

create sequence seq_registro_it_nf;

update registro_itens_nota_fiscal set cd_reg_it_nf = nextval('seq_registro_it_nf');
update registro_itens_nota_fiscal set cd_reg_nf = rnf.cd_reg_nf
from (select * from registro_notas_fiscais ) rnf
where rnf.empresa = registro_itens_nota_fiscal.empresa
and rnf.num_nf = registro_itens_nota_fiscal.num_nf
and rnf.cod_pessoa = registro_itens_nota_fiscal.cod_pessoa
and rnf.serie = registro_itens_nota_fiscal.serie;

alter table registro_itens_nota_fiscal alter column cd_reg_it_nf set not null;

alter table elo_ligacao_nota_oc add cd_reg_it_nf int8;
update elo_ligacao_nota_oc set cd_reg_it_nf = rnf.cd_reg_it_nf
from (select * from registro_itens_nota_fiscal ) rnf
where rnf.empresa = elo_ligacao_nota_oc.empresa
and rnf.num_nf = elo_ligacao_nota_oc.num_nf
and rnf.cod_pessoa = elo_ligacao_nota_oc.cod_pessoa
and rnf.item_nf = elo_ligacao_nota_oc.item_nf
and rnf.serie = elo_ligacao_nota_oc.serie;

alter table elo_ligacao_nota_oc alter column cd_reg_it_nf set not null;

alter table registro_itnf_pro_solicitado add cd_reg_it_nf int8;
update registro_itnf_pro_solicitado set cd_reg_it_nf = rnf.cd_reg_it_nf
from (select * from registro_itens_nota_fiscal ) rnf
where rnf.empresa = registro_itnf_pro_solicitado.empresa
and rnf.num_nf = registro_itnf_pro_solicitado.num_nf
and rnf.cod_pessoa = registro_itnf_pro_solicitado.cod_pessoa
and rnf.item_nf = registro_itnf_pro_solicitado.item_nf
and rnf.serie = registro_itnf_pro_solicitado.serie;

alter table registro_itnf_pro_solicitado alter column cd_reg_it_nf set not null;

alter table recebto_grupo_estoque add cd_reg_it_nf int8;
update recebto_grupo_estoque set cd_reg_it_nf = rnf.cd_reg_it_nf
from (select * from registro_itens_nota_fiscal ) rnf
where rnf.empresa = recebto_grupo_estoque.empresa
and rnf.num_nf = recebto_grupo_estoque.num_nf
and rnf.cod_pessoa = recebto_grupo_estoque.cod_pessoa
and rnf.item_nf = recebto_grupo_estoque.item_nf
and rnf.serie = recebto_grupo_estoque.serie;

alter table recebto_grupo_estoque alter column cd_reg_it_nf set not null;

alter table elo_ligacao_nota_oc drop constraint pk_elo_ligacao_nota_oc;
alter table elo_ligacao_nota_oc drop constraint fk_elo_nota_oc_ref_nota_ent;
alter table registro_itnf_pro_solicitado drop constraint fk_r_itnf_prosol_ref_regi_itnf;
alter table registro_itnf_pro_solicitado drop constraint pk_registro_itnf_pro_solicitad;
alter table recebto_grupo_estoque drop constraint fk_recebto_gru_est_ref_nf;
alter table recebto_grupo_estoque drop constraint pk_recebto_grupo_estoque;
alter table registro_itens_nota_fiscal drop constraint fk_it_nf_ent_ref_nota_entrada;
alter table registro_itens_nota_fiscal drop constraint pk_registro_itens_nota_fiscal;

alter table registro_notas_fiscais drop constraint pk_registro_notas_fiscais;
alter table registro_notas_fiscais add constraint pk_registro_notas_fiscais primary key (cd_reg_nf);

alter table registro_itens_nota_fiscal add constraint pk_registro_itens_nota_fiscal primary key (cd_reg_it_nf);
alter table registro_itens_nota_fiscal add constraint fk_it_nf_ref_nota_entrada foreign key (cd_reg_nf) references registro_notas_fiscais(cd_reg_nf);

alter table recebto_grupo_estoque add constraint pk_recebto_grupo_estoque primary key (cd_reg_it_nf,grupo_estoque);
alter table recebto_grupo_estoque add CONSTRAINT fk_recebto_gru_est_ref_nf foreign key (cd_reg_it_nf) references registro_itens_nota_fiscal(cd_reg_it_nf); 

alter table registro_itnf_pro_solicitado add constraint pk_registro_itnf_pro_solicitad primary key (cd_reg_it_nf,cd_usu_cadsus, lote);
alter table registro_itnf_pro_solicitado add CONSTRAINT fk_r_itnf_prosol_ref_regi_itnf foreign key (cd_reg_it_nf) references registro_itens_nota_fiscal(cd_reg_it_nf); 

alter table elo_ligacao_nota_oc add constraint pk_elo_ligacao_nota_oc primary key (cd_reg_it_nf,empresa_oc,cod_oc,item);
alter table elo_ligacao_nota_oc add CONSTRAINT fk_elo_nota_oc_ref_nota_ent foreign key (cd_reg_it_nf) references registro_itens_nota_fiscal(cd_reg_it_nf); 

alter table recebto_grupo_estoque drop column empresa;
alter table recebto_grupo_estoque drop column num_nf;
alter table recebto_grupo_estoque drop column cod_pessoa;
alter table recebto_grupo_estoque drop column serie;
alter table recebto_grupo_estoque drop column item_nf;

alter table registro_itnf_pro_solicitado drop column empresa;
alter table registro_itnf_pro_solicitado drop column num_nf;
alter table registro_itnf_pro_solicitado drop column cod_pessoa;
alter table registro_itnf_pro_solicitado drop column serie;
alter table registro_itnf_pro_solicitado drop column item_nf;

alter table elo_ligacao_nota_oc drop column empresa;
alter table elo_ligacao_nota_oc drop column num_nf;
alter table elo_ligacao_nota_oc drop column cod_pessoa;
alter table elo_ligacao_nota_oc drop column serie;
alter table elo_ligacao_nota_oc drop column item_nf;

alter table registro_itens_nota_fiscal drop column empresa;
alter table registro_itens_nota_fiscal drop column num_nf;
alter table registro_itens_nota_fiscal drop column cod_pessoa;
alter table registro_itens_nota_fiscal drop column serie;

alter table registro_notas_fiscais add constraint IDX_REG_NF unique  (empresa,num_nf,cod_pessoa,serie);
alter table registro_itens_nota_fiscal add constraint IDX_IT_REG_NF unique (cd_reg_nf,item_nf);

/*
    Felipe 05/02/2013
*/
alter table registro_itens_nota_fiscal drop column qtdade_balanca;
alter table registro_itens_nota_fiscal drop column cod_centro_custo;
alter table registro_itens_nota_fiscal drop column cod_cfo;
alter table registro_itens_nota_fiscal drop column cod_cla;

/*
    Claudio 06/02/2013
*/
alter table tipo_documento add licitacao char;

/*==============================================================*/
/* Table: registro_it_nf_licitacao                              */
/*==============================================================*/
create table registro_it_nf_licitacao (
cd_reg_it_nf_lic     INT8                 not null,
cd_lic_item          INT8                 not null,
cd_reg_it_nf         INT8                 not null,
lote                 VARCHAR(20)          not null,
version              INT8                 not null
);

alter table registro_it_nf_licitacao
   add constraint PK_REGISTRO_IT_NF_LICITACAO primary key (cd_reg_it_nf_lic);

alter table registro_it_nf_licitacao
   add constraint FK_REG_IT_LIC_REF_REG_IT foreign key (cd_reg_it_nf)
      references registro_itens_nota_fiscal (cd_reg_it_nf)
      on delete restrict on update restrict;

alter table registro_it_nf_licitacao
   add constraint FK_REG_IT_NF_LIC_REF_LIC foreign key (cd_lic_item)
      references licitacao_item (cd_lic_item)
      on delete restrict on update restrict;

/*
    Felipe 06/02/2013
*/
insert into programa_pagina values(305, 'br.com.celk.view.materiais.recebimento.notafiscalentrada.CadastroNotaFiscalStep1Page', 'N');
insert into programa_pagina values(306, 'br.com.celk.view.materiais.recebimento.notafiscalentrada.CadastroNotaFiscalStep2Page', 'N');
insert into programa_web_pagina values (303, 97, 305);
insert into programa_web_pagina values (304, 97, 306);

/*
    Marcus - 07/02/2013
*/

INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (259,'Gráficos','graficos',57,null,null,0);
insert into programa_pagina values(307, 'br.com.celk.view.materiais.dispensacao.relatorio.GraficoDemonstrativoDispensacaoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (183,'Gráfico Demonstrativo da Dispensação',307,'N');
insert into programa_web_pagina values (305, 183, 307);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (260,'Gráfico Demonstrativo da Dispensação','graficoDemonstrativoDispensacao',259,183,14,0);

/*
 PostgreSQL
 Everton - 07/02/2013
*/

alter table pedido_tfd add dt_pedido date;
update pedido_tfd set dt_pedido = dt_cadastro;
alter table pedido_tfd alter column dt_pedido set not null;

/*
    Marcus - 07/02/2013
*/
insert into programa_pagina values(308, 'br.com.celk.view.materiais.dispensacao.relatorio.GraficoCruzamentoDispensacaoPeriodoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (184,'Gráfico Demonstrativo da Dispensação',308,'N');
insert into programa_web_pagina values (306, 184, 308);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (261,'Gráfico Cruzamento da Dispensação por Período','graficoCruzamentoDispensacaoPeriodo',259,184,14,0);

/*
    Marcus - 07/02/2013
*/
insert into programa_pagina values(309, 'br.com.celk.view.materiais.dispensacao.relatorio.GraficoCruzamentoDispensacaoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (185,'Gráfico Cruzamento da Dispensação',309,'N');
insert into programa_web_pagina values (307, 185, 309);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (262,'Gráfico Cruzamento da Dispensação','graficoCruzamentoDispensacao',259,185,14,0);

/*
    Marcus - 08/02/2013
*/
insert into programa_pagina values(310, 'br.com.celk.view.unidadesaude.relatorio.RelatorioBoletimDiarioAtendimentosPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (186,'Boletim Diário dos Atendimentos',310,'N');
insert into programa_web_pagina values (308, 186, 310);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (263,'Relatórios','relatorios',143,null,null);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (265,'','',263,null,null);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (264,'Boletim Diário dos Atendimentos','boletimDiarioAtendimentos',265,186,143);

/*
    João Miguel - 08/02/2013
*/

INSERT INTO programas (cd_programa,cd_menu,nm_programa,ds_programa,ds_classe,ds_imagem,sequencia,rotulo,visivel,ativo,version) 
VALUES (1275,250,'Alterar Número Pedido TFD','Alterar Número Pedido TFD','br.com.ksisolucoes.gui.tfd.frame.JIFAlterarNumeroPedidoTfd','aplic.png',null,null,0,'N',0);

/*
    Marcus - 08/02/2013
*/
insert into programa_pagina values(311, 'br.com.celk.view.unidadesaude.relatorio.RelatorioBoletimProcedimentosPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (187,'Boletim de Procedimentos',311,'N');
insert into programa_web_pagina values (309, 187, 311);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (266,'Boletim de Procedimentos','boletimProcedimentos',265,187,143);

/*
    Marcus - 08/02/2013
*/
insert into programa_pagina values(312, 'br.com.celk.view.unidadesaude.relatorio.RelatorioProcedimentosPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (188,'Procedimentos',312,'N');
insert into programa_web_pagina values (310, 188, 312);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (267,'Procedimentos','procedimentos',265,188,143);

/*
    Marcus - 08/02/2013
*/
insert into programa_pagina values(313, 'br.com.celk.view.unidadesaude.relatorio.RelatorioAtendimentoOdontologicoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (189,'Relatório Atendimento Odontológico',313,'N');
insert into programa_web_pagina values (311, 189, 313);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (268,'Relatório Atendimento Odontológico','relatorioAtendimentoOdontologico',265,189,143);

/*
    Marcus - 08/02/2013
*/
insert into programa_pagina values(314, 'br.com.celk.view.unidadesaude.relatorio.RelatorioPendenciasOdontologicasPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (190,'Relatório Pendências Odontológicas',314,'N');
insert into programa_web_pagina values (312, 190, 314);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (269,'Relatório Pendências Odontológicas','relatorioPendenciasOdontologicas',265,190,143);

/*
    Marcus - 08/02/2013
*/
insert into programa_pagina values(315, 'br.com.celk.view.unidadesaude.relatorio.RelatorioPerfilAtendimentoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (191,'Relatório Perfil Atendimento',315,'N');
insert into programa_web_pagina values (313, 191, 315);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (270,'Relatório Perfil Atendimento','relatorioPerfilAtendimento',265,191,143);

/*
    Marcus - 08/02/2013
*/
insert into programa_pagina values(316, 'br.com.celk.view.unidadesaude.relatorio.RelatorioRelacaoUsuariosAtendidosPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (192,'Relação Usuarios Atendidos',316,'N');
insert into programa_web_pagina values (314, 192, 316);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (271,'Relação Usuarios Atendidos','relacaoUsuariosAtendidos',265,192,143);

/*
    Marcus - 13/02/2013
*/
insert into programa_pagina values(317, 'br.com.celk.view.unidadesaude.relatorio.RelatorioRelacaoAtendimentosPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (193,'Relação dos Atendimentos',317,'N');
insert into programa_web_pagina values (315, 193, 317);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (272,'Relação dos Atendimentos','relacaoAtendimentos',265,193,143);

/*
    Marcus - 13/02/2013
*/
insert into programa_pagina values(318, 'br.com.celk.view.agenda.agendamento.relatorio.RelatorioImpressaoFichaAgendamentoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (194,'Impressão da Ficha Agendamento',318,'N');
insert into programa_web_pagina values (316, 194, 318);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (273,'Impressão da Ficha Agendamento','impressaoFichaAgendamento',160,194,3);

/*
    Marcus - 13/02/2013
*/
insert into programa_pagina values(319, 'br.com.celk.view.agenda.agendamento.relatorio.RelatorioRelacaoEncaminhamentoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (195,'Relação dos Encaminhamentos',319,'N');
insert into programa_web_pagina values (317, 195, 319);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (274,'Relação dos Encaminhamentos','relacaoEncaminhamentos',160,195,3);

/*
 PostgreSQL
 Everton - 13/02/2013
*/

ALTER TABLE receituario_item ALTER COLUMN qtd_posologia TYPE NUMERIC(12,2);

/*
  João Miguel - 13/02/2013
*/
INSERT INTO programas (cd_programa,cd_menu,nm_programa,ds_programa,ds_classe,ds_imagem,sequencia,rotulo,visivel,ativo,version) 
VALUES (1276,250,'Avaliação dos Pedidos Inconclusivos','Avaliação dos Pedidos Inconclusivos','br.com.ksisolucoes.gui.tfd.frame.JIFAvaliacaoPedidosInconclusivos','aplic.png',null,null,0,'N',0);

/*
 PostgreSQL
 Everton - 14/02/2013
*/

alter table agenda_gra_ate_horario add nm_profissional             VARCHAR(80)          null;
update agenda_gra_ate_horario t1 set nm_profissional = (select nm_profissional from profissional where cd_profissional = t1.cd_profissional);
alter table agenda_gra_ate_horario alter column nm_profissional set not null;

/*
  João Miguell - 14/02/2013
*/
INSERT INTO PROGRAMAS (cd_programa,cd_menu,nm_programa,ds_programa,ds_classe,ds_imagem,sequencia,rotulo,visivel,ativo,version) 
VALUES (1277,250,'Encaminhar TFD a Regional','Encaminhar TFD a Regional','br.com.ksisolucoes.gui.tfd.frame.JIFEncaminharTfdRegional','aplic.png',null,null,0,'N',0);

/*
    Marcus - 14/02/2013
*/
insert into programa_pagina values(320, 'br.com.celk.view.agenda.agendamento.relatorio.RelatorioAgendaUnidadeVagaPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (196,'Relação de Vagas por Unidade',320,'N');
insert into programa_web_pagina values (318, 196, 320);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (275,'Relação de Vagas por Unidade','relacaoVagasUnidade',160,196,3);

/*
    Marcus - 14/02/2013
*/
insert into programa_pagina values(321, 'br.com.celk.view.agenda.agendamento.relatorio.RelatorioRelacaoAgendaContatoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (197,'Relação da Agenda para Contato',321,'N');
insert into programa_web_pagina values (319, 197, 321);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (276,'Relação da Agenda para Contato','relacaoAgendaContato',160,197,3);

/*
    Marcus - 14/02/2013 
*/
update programa_pagina
set cam_pagina = 'br.com.celk.view.agenda.agendamento.relatorio.RelatorioRelacaoAgendaExamesPage'
where cam_pagina = 'br.com.celk.view.agenda.agendamento.relatorio.RelacaoAgendasPage';

/*
    Marcus - 14/02/2013
*/
insert into programa_pagina values(322, 'br.com.celk.view.agenda.agendamento.relatorio.RelatorioRelacaoAgendasUnidadePage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (198,'Relação das Agendas por Unidade',322,'N');
insert into programa_web_pagina values (320, 198, 322);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (277,'Relação das Agendas por Unidade','relacaoAgendasUnidade',160,198,3);

/*
    Marcus - 14/02/2013
*/
insert into programa_pagina values(323, 'br.com.celk.view.agenda.agendamento.relatorio.RelatorioRelacaoVagasCotasPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (199,'Relação das Vagas X Cotas',323,'N');
insert into programa_web_pagina values (321, 199, 323);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (278,'Relação das Vagas X Cotas','relacaoVagasXCotas',160,199,3);

/*
    Marcus - 14/02/2013
*/
insert into programa_pagina values(324, 'br.com.celk.view.agenda.agendamento.relatorio.RelatorioAgendamentoExamePage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (200,'Relação dos Agendamentos',324,'N');
insert into programa_web_pagina values (322, 200, 324);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (279,'Relação dos Agendamentos','relacaoAgendamentos',160,200,3);

/*
    Marcus - 14/02/2013
*/
insert into programa_pagina values(325, 'br.com.celk.view.agenda.agendamento.relatorio.RelatorioResumoAgendamentoExamePage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (201,'Resumo dos Agendamentos',325,'N');
insert into programa_web_pagina values (323, 201, 325);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (280,'Resumo dos Agendamentos','resumoAgendamentos',160,201,3);

/*
    Marcus - 14/02/2013
*/
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (281,'Gráficos','graficos',3,null,null);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (282,'','',281,null,null);

insert into programa_pagina values(326, 'br.com.celk.view.agenda.agendamento.relatorio.GraficoDistribuicaoMensalExameAgendamentosPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (202,'Distribuição Mensal dos Agendamentos',326,'N');
insert into programa_web_pagina values (324, 202, 326);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (283,'Distribuição Mensal dos Agendamentos','distribuicaoMensalAgendamentos',282,202,3);

/*
    Marcus - 15/02/2013
*/
insert into programa_pagina values(327, 'br.com.celk.view.agenda.agendamento.relatorio.GraficoDistribuicaoExameAgendamentosPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (203,'Distribuição dos Agendamentos',327,'N');
insert into programa_web_pagina values (325, 203, 327);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (284,'Distribuição dos Agendamentos','distribuicaoAgendamentos',282,203,3);

insert into programa_pagina values(328, 'br.com.celk.view.agenda.agendamento.relatorio.GraficoEstatisticasNaoComparecimentoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (204,'Estatísticas de Não Comparecimento',328,'N');
insert into programa_web_pagina values (326, 204, 328);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (285,'Estatísticas de Não Comparecimento','estatisticaNaoComparecimento',282,204,3);

/*
    Marcus - 15/02/2013
*/
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (286,'TFD','tfd',3,null,null);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (287,'Relatórios','relatorios',286,null,null);

insert into programa_pagina values(329, 'br.com.celk.view.agenda.agendamento.tfd.relatorio.RelatorioRelacaoAgendamentoTfdPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (205,'Relação Agendamento TFD',329,'N');
insert into programa_web_pagina values (327, 205, 329);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (288,'Relação Agendamento TFD','relacaoAgendaTfd',287,205,3);

/*
    Marcus - 15/02/2013
*/
insert into programa_pagina values(330, 'br.com.celk.view.agenda.agendamento.tfd.relatorio.RelatorioRelacaoPacientesNaoCompareceramPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (206,'Realação Pacientes Não Compareceram',330,'N');
insert into programa_web_pagina values (328, 206, 330);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (289,'Realação Pacientes Não Compareceram','relacaoPacientesNaoCompareceram',287,206,3);

/*
    Marcus - 15/02/2013
*/
insert into programa_pagina values(331, 'br.com.celk.view.agenda.agendamento.tfd.relatorio.RelatorioEncaminhamentoTfdPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (207,'Relação dos TFDs',331,'N');
insert into programa_web_pagina values (329, 207, 331);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (290,'Relação dos TFDs','relacaoTfds',287,207,3);

/*
    Marcus - 15/02/2013
*/
insert into programa_pagina values(332, 'br.com.celk.view.agenda.agendamento.tfd.relatorio.RelatorioResumoEncaminhamentosTfdPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (208,'Resumo TFD',332,'N');
insert into programa_web_pagina values (330, 208, 332);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (291,'Resumo TFD','resumoTfd',287,208,3);

/*
    Marcus - 15/02/2013
*/
insert into permissao_web VALUES(13,'Período','0');
insert into programa_pagina_permissao values(19,13,228,0);

INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (209,'Acompanhamento das Solicitações',228,'N');
insert into programa_web_pagina values (331, 209, 228);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (292,'Acompanhamento das Solicitações','acompanhamentoSolicitacoes',162,209,3);

/*
    Marcus - 18/02/2013
*/
INSERT INTO permissao_web (cd_permissao,ds_permissao,version) VALUES (14,'Profissional',0);
INSERT INTO permissao_web (cd_permissao,ds_permissao,version) VALUES (15,'Paciente',0);
INSERT INTO permissao_web (cd_permissao,ds_permissao,version) VALUES (16,'Tipo Resumo',0);
INSERT INTO permissao_web (cd_permissao,ds_permissao,version) VALUES (17,'Ordenação',0);

insert into programa_pagina_permissao values(20,14,233);
insert into programa_pagina_permissao values(21,15,233);
insert into programa_pagina_permissao values(22,16,233);
insert into programa_pagina_permissao values(23,17,233);

INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (210,'Resumo das Solicitações',233,'N');
insert into programa_web_pagina values (333, 210, 233);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (293,'Resumo das Solicitações','resumoSolicitacoes',162,210,3);

/*
    Marcus - 18/02/2013
*/
insert into programa_pagina values(335, 'br.com.celk.view.agenda.solicitacao.relatorio.RelatorioRelacaoSolicitacoesPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (211,'Relação das Solicitações',335,'N');
insert into programa_web_pagina values (335, 211, 335);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (294,'Relação das Solicitações','relacaoSolicitacoes',162,211,3);

INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (295,'Relatórios','relatorios',10,null,null,0);

update menu_web
set cd_menu_pai = 295
where cd_menu in (294,293,292);

/*
    Marcus - 18/02/2013
*/
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (296,'Encaminhamento','encaminhamento',3,null,null);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (297,'Gráficos','graficos',296,null,null);
insert into programa_pagina values(336, 'br.com.celk.view.agenda.encaminhamento.relatorio.GraficoDistribuicaoExameAgendamentosPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (212,'Distribuição Encaminhamentos',336,'N');
insert into programa_web_pagina values (336, 212, 336);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (298,'Distribuição Encaminhamentos','distribuicaoEncaminhamentos',297,212,3);

/*
    Felipe - 18/02/2013
*/
insert into programa_pagina values(333, 'br.com.celk.view.materiais.recebimento.notafiscalentrada.CadastroNotaFiscalResumoPage', 'N');
insert into programa_web_pagina values (332, 97, 333); 
insert into programa_pagina values(334, 'br.com.celk.view.materiais.recebimento.notafiscalentrada.CadastroNotaFiscalStep3Page', 'N');
insert into programa_web_pagina values (334, 97, 334); 

/*
PostgreSQL
Everton - 20/02/2013
*/

ALTER TABLE tipo_documento_atend ALTER COLUMN modelo TYPE TEXT;
alter table documento_atend ALTER COLUMN descricao TYPE TEXT; 

/*
    João Miguel - 20/02/2013
*/
INSERT INTO programas (cd_programa,cd_menu,nm_programa,ds_programa,ds_classe,ds_imagem,sequencia,rotulo,visivel,ativo,version)
VALUES (1278,250,'Cadastro do Processo TFD','Cadastro do Processo TFD','br.com.ksisolucoes.gui.tfd.frame.JIFCadastroProcessoTfd','aplic.png',null,null,0,'S',0);

/*
    CLAUDIO - 21/02/2013
*/
alter table tipo_conta alter column referencia drop not null;
alter table dom_tipo_conta alter column referencia drop not null;
alter table conta alter column referencia drop not null;

/*
PostgreSQL
Everton - 25/02/2013
*/

alter table dom_usuario_cadsus alter nome type varchar(200);

CREATE OR REPLACE FUNCTION ftg_dom_usuario_cadsus()
RETURNS trigger AS
$BODY$
DECLARE
vcns varchar;
vexiste integer;
c1 record;
BEGIN
if TG_OP = 'DELETE' THEN
delete from dom_usuario_cadsus where cd_usu_cadsus = old.cd_usu_cadsus;
end if;

if (TG_OP = 'INSERT' or TG_OP = 'UPDATE') then
vcns = '';
for c1 in SELECT cd_numero_cartao FROM usuario_cadsus_cns
WHERE st_excluido = 0 and cd_usu_cadsus = new.cd_usu_cadsus
ORDER BY cd_numero_cartao loop
vcns = vcns || c1.cd_numero_cartao || ' ';
end loop;

-- Avalia se ja existe o dominio cadastrado
SELECT count(*)
INTO vexiste
FROM dom_usuario_cadsus
WHERE cd_usu_cadsus = new.cd_usu_cadsus;

if vexiste = 0 then
insert into dom_usuario_cadsus (cd_dominio, cd_usu_cadsus, key_word, nome, nome_mae, rg, cns, cpf, situacao, dt_nascimento)
values (
nextval('seq_dominio_consulta'),
new.cd_usu_cadsus,
sem_acento(TRIM(UPPER(coalesce(new.nm_usuario, '')))) ||' '|| sem_acento(TRIM(UPPER(coalesce(new.nm_mae, '')))) || ' ' || coalesce(new.rg,'') || ' ' || coalesce(new.cpf, '') || ' ' || coalesce(vcns, '') || ' ' || new.cd_usu_cadsus || ' ' || coalesce(to_char(new.dt_nascimento, 'DD/MM/YYYY'), ''),
TRIM(UPPER(new.nm_usuario)) || ' (' || new.cd_usu_cadsus || ')',
sem_acento(TRIM(UPPER(new.nm_mae))),
new.rg,
(SELECT min(cd_numero_cartao) FROM usuario_cadsus_cns where st_excluido = 0 and cd_usu_cadsus = new.cd_usu_cadsus),
new.cpf,
new.situacao,
new.dt_nascimento
);
else
update dom_usuario_cadsus set
key_word = sem_acento(TRIM(UPPER(coalesce(new.nm_usuario, '')))) ||' '|| sem_acento(TRIM(UPPER(coalesce(new.nm_mae, '')))) || ' ' || coalesce(new.rg,'') || ' ' || coalesce(new.cpf, '') || ' ' || coalesce(vcns, '') || ' ' || new.cd_usu_cadsus || ' ' || coalesce(to_char(new.dt_nascimento, 'DD/MM/YYYY'), ''),
nome = TRIM(UPPER(new.nm_usuario)) || ' (' || new.cd_usu_cadsus || ')',
nome_mae = sem_acento(TRIM(UPPER(new.nm_mae))),
rg = new.rg,
cns = (SELECT min(cd_numero_cartao) FROM usuario_cadsus_cns where st_excluido = 0 and cd_usu_cadsus = new.cd_usu_cadsus),
cpf = new.cpf,
situacao = new.situacao,
dt_nascimento = new.dt_nascimento
where cd_usu_cadsus = new.cd_usu_cadsus;
end if;

return new;
end if;
return old;
END;
$BODY$
LANGUAGE plpgsql VOLATILE
COST 100;

update usuario_cadsus set nm_usuario = nm_usuario; 

/*
    CLAUDIO - 26/02/2013
*/
delete from programa_web_pagina where cd_prg_web_pagina = 165;
delete from programa_pagina where cd_prg_pagina = 167;
update programa_web set ds_prg_web = 'Entrada de Materiais/Medicamentos' where cd_prg_web = 97;
update menu_web set ds_menu = 'Entrada de Materiais/Medicamentos', ds_bundle='entradaMateriaisMedicamentos' where cd_menu = 118;
update programa_web set ds_prg_web = 'Relação das Entradas' where cd_prg_web = 102;
update programa_web set ds_prg_web = 'Resumo das Entradas' where cd_prg_web = 103;
update menu_web set ds_menu = 'Relação das Entradas' where cd_menu = 123;
update menu_web set ds_menu = 'Resumo das Entradas' where cd_menu = 124;

/*
 PostgreSQL
 Everton - 27/02/2013
*/

alter table atendimento add tp_demanda     INT2;
update atendimento set tp_demanda = 0;
update atendimento t1 set tp_demanda = 1 where exists (select 1 from agenda_gra_ate_horario where nr_atendimento = t1.nr_atendimento and status = 2);
alter table atendimento alter tp_demanda set not null;

/*
 PostgreSQL
 Everton - 27/02/2013
*/

/*==============================================================*/
/* Table: local_fornecimento                                    */
/*==============================================================*/
create table local_fornecimento (
cd_local_fornecimento INT8                 not null,
ds_local_fornecimento VARCHAR(50)          not null,
version              INT8                 not null
);

comment on table local_fornecimento is
'Local onde será fornecido os medicamentos para poder separar as receitas.';

alter table local_fornecimento
   add constraint PK_LOCAL_FORNECIMENTO primary key (cd_local_fornecimento);

INSERT INTO local_fornecimento VALUES (1, 'FARMACIA BÁSICA', 0);
INSERT INTO local_fornecimento VALUES (2, 'PARTICULAR', 0);

ALTER TABLE medicamento_paciente ADD cd_local_fornecimento INT8;
update medicamento_paciente set cd_local_fornecimento = 1 where tp_fornecimento = 0;
update medicamento_paciente set cd_local_fornecimento = 2 where tp_fornecimento = 1;
ALTER TABLE medicamento_paciente ALTER cd_local_fornecimento SET not null;
alter table medicamento_paciente
   add constraint FK_MED_PAC_REF_LOC_FOR foreign key (cd_local_fornecimento)
      references local_fornecimento (cd_local_fornecimento)
      on delete restrict on update restrict;

ALTER TABLE medicamento_paciente drop column tp_fornecimento;


/*
 João Miguel - 27/02/2013
*/
INSERT INTO permissao_web (cd_permissao,ds_permissao,version) VALUES (18,'Empresa',0);
INSERT INTO programa_pagina_permissao (cd_prog_pag_perm,cd_permissao,cd_prg_pagina,version) VALUES (24,18,177,0);
INSERT INTO programa_pagina_permissao (cd_prog_pag_perm,cd_permissao,cd_prg_pagina,version) VALUES (25,18,178,0);

/*
 Everton - 05/02/2013
*/
alter table agenda_gra_ate_horario alter cd_profissional drop not null;