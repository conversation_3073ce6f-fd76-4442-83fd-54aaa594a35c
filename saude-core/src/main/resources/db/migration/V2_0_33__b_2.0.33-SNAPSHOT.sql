/*
 PostgreSQL
 Claudio - 17/04/2013
*/
INSERT INTO programas (cd_programa,cd_menu,nm_programa,ds_programa,ds_classe,ds_imagem,sequencia,rotulo,visivel,ativo,version) 
VALUES (1280,207,'Atendimento Internação','Atendimento Internação','br.com.ksisolucoes.gui.prontuario.hospital.lancamento.frame.JIFListaEsperaAtendimentoInternacao','aplic.png',null,null,0,'N',0);

/*
 PostgreSQL
 Everton - 17/04/2013
*/

/*==============================================================*/
/* Table: convenio                                              */
/*==============================================================*/
create table convenio (
cd_convenio          INT8                 not null,
ds_convenio          VARCHAR(50)          not null,
version              INT8                 not null
);

alter table convenio
   add constraint PK_CONVENIO primary key (cd_convenio);

/*==============================================================*/
/* Table: quarto_internacao                                     */
/*==============================================================*/
create table quarto_internacao (
cd_quarto_internacao INT8                 not null,
cd_convenio          INT8                 not null,
empresa              INT4                 not null,
ds_quarto_internacao VARCHAR(50)          not null,
referencia           VARCHAR(10)          null,
tp_quarto            INT2                 not null,
version              INT8                 not null
);

alter table quarto_internacao
   add constraint PK_QUARTO_INTERNACAO primary key (cd_quarto_internacao);

alter table quarto_internacao
   add constraint FK_LEITO_INT_REF_CONVENIO foreign key (cd_convenio)
      references convenio (cd_convenio)
      on delete restrict on update restrict;

alter table quarto_internacao
   add constraint FK_LEITO_INT_REF_EMPRESA foreign key (empresa)
      references empresa (empresa)
      on delete restrict on update restrict;

/*==============================================================*/
/* Table: leito_quarto                                          */
/*==============================================================*/
create table leito_quarto (
cd_leito             INT8                 not null,
cd_quarto_internacao INT8                 not null,
ds_leito             VARCHAR(50)          not null,
situacao             INT2                 not null,
version              INT8                 not null
);

alter table leito_quarto
   add constraint PK_LEITO_QUARTO primary key (cd_leito);

alter table leito_quarto
   add constraint FK_LEITO_REF_QUARTO_INT foreign key (cd_quarto_internacao)
      references quarto_internacao (cd_quarto_internacao)
      on delete restrict on update restrict;

ALTER TABLE atendimento ADD cd_leito             INT8                 null;
alter table atendimento
   add constraint FK_ATEND_REF_LEITO foreign key (cd_leito)
      references leito_quarto (cd_leito)
      on delete restrict on update restrict;

ALTER TABLE ATENDIMENTO ADD cd_convenio          INT8                 null;
alter table atendimento
   add constraint FK_ATEND_REF_CONVENIO foreign key (cd_convenio)
      references convenio (cd_convenio)
      on delete restrict on update restrict;

/*==============================================================*/
/* Table: atendimento_internacao                                */
/*==============================================================*/
create table atendimento_internacao (
cd_atendimento_internacao INT8                 not null,
nr_atendimento       INT8                 not null,
version              INT8                 not null
);

alter table atendimento_internacao
   add constraint PK_ATENDIMENTO_INTERNACAO primary key (cd_atendimento_internacao);

alter table atendimento_internacao
   add constraint FK_ATEND_INT_REF_ATENDIMENTO foreign key (nr_atendimento)
      references atendimento (nr_atendimento)
      on delete restrict on update restrict;

/*
 Marcus - 17/04/2013
*/
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (353,'Hospital','hospital',null,null,null,0);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (354,'Cadastros','cadastros',353,null,null,0);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (356,'','',354,null,null,0);


insert into programa_pagina values(424, 'br.com.celk.view.hospital.convenio.ConsultaConvenioPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (255,'Convênio',424,'N');
insert into programa_web_pagina values (425, 255, 424);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (355,'Convênio','convenio',356,255,353);

insert into programa_pagina values(425, 'br.com.celk.view.hospital.convenio.CadastroConvenioPage', 'N');
insert into programa_web_pagina values (426, 255, 425);

/*
 Claudio - 18/04/2013
*/
alter table tipo_atendimento add empresa              INT4                 null;
alter table tipo_atendimento
   add constraint FK_TP_ATEND_REF_EMPRESA foreign key (empresa)
      references empresa (empresa)
      on delete restrict on update restrict;

alter table leito_quarto add nr_atendimento int8;
alter table leito_quarto
   add constraint FK_LEITO_Q_REF_ATENDIMENTO foreign key (nr_atendimento)
      references atendimento (nr_atendimento)
      on delete restrict on update restrict;

/*
    Felipe - 18/04/2013
*/
insert into programa_pagina values(429, 'br.com.celk.view.materiais.dispensacao.prescricaoatendimento.ConsultaPrescricaoAtendimentoPage', 'N');
insert into programa_pagina values(430, 'br.com.celk.view.materiais.dispensacao.prescricaoatendimento.DispensacaoPrescricaoAtendimentoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (257,'Dispensação da Prescrição',429,'N');
insert into programa_web_pagina values (430, 257, 429);
insert into programa_web_pagina values (431, 257, 430);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (358,'Dispensação da Prescrição','dispensacaoPrescricao',174,257,14);

/*
 Marcus - 18/04/2013
*/
insert into programa_pagina values(426, 'br.com.celk.view.hospital.quartosleitos.ConsultaQuartosLeitos', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (256,'Cadastro de Quartos / Leitos',426,'N');
insert into programa_web_pagina values (427, 256, 426);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (357,'Cadastro de Quartos / Leitos','cadastroQuartosLeitos',356,256,353);
insert into programa_pagina values(427, 'br.com.celk.view.hospital.quartosleitos.CadastroQuartosLeitosStep1', 'N');
insert into programa_web_pagina values (428, 256, 427);
insert into programa_pagina values(428, 'br.com.celk.view.hospital.quartosleitos.CadastroQuartosLeitosStep2', 'N');
insert into programa_web_pagina values (429, 256, 428);
insert into programa_pagina values(431, 'br.com.celk.view.hospital.quartosleitos.CadastroQuartosLeitosDetalhes', 'N');
insert into programa_web_pagina values (432, 256, 431);

/*
 PostgreSQL
 Everton - 19/04/2013
*/

/*==============================================================*/
/* Table: leito_limpeza                                         */
/*==============================================================*/
create table leito_limpeza (
cd_leito_limpeza     INT8                 not null,
cd_leito             INT8                 not null,
cd_usuario           NUMERIC(6)           not null,
dt_limpeza           TIMESTAMP            not null,
version              INT8                 not null
);

alter table leito_limpeza
   add constraint PK_LEITO_LIMPEZA primary key (cd_leito_limpeza);

alter table leito_limpeza
   add constraint FK_LEITO_LIMP_REF_LEITO foreign key (cd_leito)
      references leito_quarto (cd_leito)
      on delete restrict on update restrict;

alter table leito_limpeza
   add constraint FK_LEITO_LIMP_REF_USUARIO foreign key (cd_usuario)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

/*
    Felipe - 19/04/2013
*/
insert into programa_pagina values(432, 'br.com.celk.view.hospital.leitolimpeza.LiberacaoLeitoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (258,'Liberação dos Leitos',432,'N');
insert into programa_web_pagina values (433, 258, 432);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (359,'Processos','processos',353,null,null);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (360,'','',359,null,null);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (361,'Liberação dos Leitos','liberacaoLeitos',360,258,353);


/*
 PostgreSQL
 Everton - 19/04/2013
*/

/*==============================================================*/
/* Table: atendimento_alta                                      */
/*==============================================================*/
create table atendimento_alta (
cd_atendimento_alta  INT8                 not null,
nr_atendimento       INT8                 not null,
cd_cid               CHAR(8)              not null,
dt_alta              date                 not null,
motivo_alta              INT2                 not null,
version              INT8                 not null
);

alter table atendimento_alta
   add constraint PK_ATENDIMENTO_ALTA primary key (cd_atendimento_alta);

alter table atendimento_alta
   add constraint FK_ATE_ALTA_REF_ATENDIMENTO foreign key (nr_atendimento)
      references atendimento (nr_atendimento)
      on delete restrict on update restrict;

alter table atendimento_alta
   add constraint FK_ATE_ALTA_REF_CID foreign key (cd_cid)
      references cid (cd_cid)
      on delete restrict on update restrict;

/*
 PostgreSQL
 Everton - 22/04/2013
*/
/*==============================================================*/
/* Table: atendimento_transferencia_leito                       */
/*==============================================================*/
create table atendimento_transferencia_leito (
cd_atendimento_transferencia INT8                 not null,
nr_atendimento       INT8                 not null,
cd_leito             INT8                 not null,
obs_transferencia    VARCHAR(512)         null,
version              INT8                 not null
);

alter table atendimento_transferencia_leito
   add constraint PK_ATENDIMENTO_TRANSFERENCIA_L primary key (cd_atendimento_transferencia);

alter table atendimento_transferencia_leito
   add constraint FK_ATE_TRA_LEITO_REF_ATENDIMENTO foreign key (nr_atendimento)
      references atendimento (nr_atendimento)
      on delete restrict on update restrict;

alter table atendimento_transferencia_leito
   add constraint FK_ATE_TRA_LEITO_REF_LEITO foreign key (cd_leito)
      references leito_quarto (cd_leito)
      on delete restrict on update restrict;

/*
 PostgreSQL
 Claudio - 23/04/2013
*/
alter table tipo_atendimento add cd_tp_atendimento_alta int8;
alter table tipo_atendimento 
	add constraint FK_TP_AT_TP_AT_ALTA 
	foreign key (cd_tp_atendimento_alta) references tipo_atendimento(cd_tp_atendimento);
/*
 PostgreSQL
 Claudio - 23/04/2013
*/
alter table atendimento_alta add dt_cadastro timestamp;

/*
 Leandro - 24/04/2013
*/
/*==============================================================*/
/* Table: convenio_quarto                                       */
/*==============================================================*/
create table convenio_quarto (
cd_convenio_quarto   INT8                 not null,
cd_convenio          INT8                 not null,
cd_quarto_internacao INT8                 not null,
version              INT8                 not null
);

alter table convenio_quarto
   add constraint PK_CONVENIO_QUARTO primary key (cd_convenio_quarto);

alter table convenio_quarto
   add constraint FK_CONV_QUARTO_REF_CONVENIO foreign key (cd_convenio)
      references convenio (cd_convenio)
      on delete restrict on update restrict;

alter table convenio_quarto
   add constraint FK_CONV_QUARTO_REF_QUARTO foreign key (cd_quarto_internacao)
      references quarto_internacao (cd_quarto_internacao)
      on delete restrict on update restrict;

/*
  Leandro - 24/04/2013
*/
alter table quarto_internacao drop constraint FK_LEITO_INT_REF_CONVENIO;
alter table quarto_internacao drop cd_convenio;

/* 
 Postgres
 Everton - 24/04/2013
*/

ALTER TABLE  atendimento_transferencia_leito ADD DT_TRANSFERENCIA TIMESTAMP;
UPDATE atendimento_transferencia_leito set dt_transferencia = CURRENT_timestamp;
ALTER TABLE  atendimento_transferencia_leito alter DT_TRANSFERENCIA set not null;

/* 
 Postgres
 Claudio - 25/04/2013
*/
ALTER TABLE ATENDIMENTO ADD enc_alta INT8;

/*
 PostgreSQL
 Everton - 06/05/2013
*/

ALTER TABLE ficha_acolhimento ADD cd_cid               CHAR(8)              null;
alter table ficha_acolhimento
   add constraint FK_FICHA_ACOLH_REF_CID foreign key (cd_cid)
      references cid (cd_cid)
      on delete restrict on update restrict;