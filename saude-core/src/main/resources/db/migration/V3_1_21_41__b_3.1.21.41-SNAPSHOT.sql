SET application_name = 'flyway|3.1.21.41';
SET statement_timeout TO 600000;

/*
    <PERSON> - 14/11/2019 AMB-102
*/
INSERT INTO procedimento_esus (select nextval('seq_gem'), 'ATIVIDADE EDUCATIVA / ORIENTAÇÃO EM GRUPO NA ATENÇÃO BÁSICA', 4, 0, null, 0, null from procedimento where referencia = '0101010010');
INSERT INTO procedimento_esus (select nextval('seq_gem'), 'ATIVIDADE EDUCATIVA / ORIENTAÇÃO EM GRUPO NA ATENÇÃO ESPECIALIZADA', 4, 0, null, 0, null from procedimento where referencia = '0101010028');
INSERT INTO procedimento_esus (select nextval('seq_gem'), 'SESSÃO DE BIODANÇA', 4, 0, null, 0, null from procedimento where referencia = '0101050100');
INSERT INTO procedimento_esus (select nextval('seq_gem'), 'SESSÃO DE BIOENERGÉTICA', 4, 0, null, 0, null from procedimento where referencia = '0101050119');
INSERT INTO procedimento_esus (select nextval('seq_gem'), 'SESSÃO DE CONSTELAÇÃO FAMILIAR', 4, 0, null, 0, null from procedimento where referencia = '0101050127');
INSERT INTO procedimento_esus (select nextval('seq_gem'), 'SESSÃO DE DANÇA CIRCULAR', 4, 0, null, 0, null from procedimento where referencia = '0101050135');
INSERT INTO procedimento_esus (select nextval('seq_gem'), 'ATIVIDADE EDUCATIVA EM SAÚDE DO TRABALHADOR', 4, 0, null, 0, null from procedimento where referencia = '0102020027');

INSERT INTO procedimento_elo_esus (select nextval('seq_gem'), (select cd_procediment_esus from procedimento_esus where ds_procedimento = 'ATIVIDADE EDUCATIVA / ORIENTAÇÃO EM GRUPO NA ATENÇÃO BÁSICA'), cd_procedimento, 0 FROM procedimento where referencia = '0101010010');
INSERT INTO procedimento_elo_esus (select nextval('seq_gem'), (select cd_procediment_esus from procedimento_esus where ds_procedimento = 'ATIVIDADE EDUCATIVA / ORIENTAÇÃO EM GRUPO NA ATENÇÃO ESPECIALIZADA'), cd_procedimento, 0 FROM procedimento where referencia = '0101010028');
INSERT INTO procedimento_elo_esus (select nextval('seq_gem'), (select cd_procediment_esus from procedimento_esus where ds_procedimento = 'SESSÃO DE BIODANÇA'), cd_procedimento, 0 FROM procedimento where referencia = '0101050100');
INSERT INTO procedimento_elo_esus (select nextval('seq_gem'), (select cd_procediment_esus from procedimento_esus where ds_procedimento = 'SESSÃO DE BIOENERGÉTICA'), cd_procedimento, 0 FROM procedimento where referencia = '0101050119');
INSERT INTO procedimento_elo_esus (select nextval('seq_gem'), (select cd_procediment_esus from procedimento_esus where ds_procedimento = 'SESSÃO DE CONSTELAÇÃO FAMILIAR'), cd_procedimento, 0 FROM procedimento where referencia = '0101050127');
INSERT INTO procedimento_elo_esus (select nextval('seq_gem'), (select cd_procediment_esus from procedimento_esus where ds_procedimento = 'SESSÃO DE DANÇA CIRCULAR'), cd_procedimento, 0 FROM procedimento where referencia = '0101050135');
INSERT INTO procedimento_elo_esus (select nextval('seq_gem'), (select cd_procediment_esus from procedimento_esus where ds_procedimento = 'ATIVIDADE EDUCATIVA EM SAÚDE DO TRABALHADOR'), cd_procedimento, 0 FROM procedimento where referencia = '0102020027');

/*
    Hallan - 06/12/2019 #AMB-200
*/

-- Index: public.idx_cd_bpa_processo

-- DROP INDEX public.idx_cd_bpa_processo;

CREATE INDEX idx_cd_bpa_processo
  ON public.item_conta_paciente
  USING btree
  (cd_bpa_processo);