SET application_name = 'flyway|*********';

SET statement_timeout TO 600000;

/*
    <PERSON><PERSON> - 14/12/2021 - hotfix/REG-992
*/
CREATE TABLE "public".solicitacao_agendamento_apac (
   cd_solicitacao_agendamento_apac bigserial NOT NULL,
   cd_solicitacao_agendamento int8 NOT NULL,
   ds_diagnostico varchar(100) NOT NULL,
   cd_cid_principal varchar(8) NULL,
   cd_cid_secundario varchar(8) NULL,
   cd_cid_causa varchar(8) NULL,
   resumo_anamnese varchar(512) NULL,
   justificativa_procedimento varchar(512) NULL,
   exame_complementar varchar(512) NULL,
   "version" int8 NOT NULL,
   CONSTRAINT pk_solicitacao_agendamento_apac PRIMARY KEY (cd_solicitacao_agendamento_apac)
);

ALTER TABLE "public".solicitacao_agendamento_apac ADD CONSTRAINT fk_solicitacao_agendamento_apac_ref_solicitacao_agendamento FOREIGN KEY (cd_solicitacao_agendamento) REFERENCES "public".solicitacao_agendamento(cd_solicitacao) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE "public".solicitacao_agendamento_apac ADD CONSTRAINT fk_solicitacao_agendamento_apac_ref_cid_causa FOREIGN KEY (cd_cid_causa) REFERENCES "public".cid(cd_cid) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE "public".solicitacao_agendamento_apac ADD CONSTRAINT fk_solicitacao_agendamento_apac_ref_cid_pri FOREIGN KEY (cd_cid_principal) REFERENCES "public".cid(cd_cid) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE "public".solicitacao_agendamento_apac ADD CONSTRAINT fk_solicitacao_agendamento_apac_ref_cid_sec FOREIGN KEY (cd_cid_secundario) REFERENCES "public".cid(cd_cid) ON DELETE RESTRICT ON UPDATE RESTRICT;

CREATE TABLE auditschema.solicitacao_agendamento_apac
AS SELECT t2.*, t1.* FROM solicitacao_agendamento_apac t1, audit_temp t2 WHERE 1=2;
create sequence seq_audit_id_solicitacao_agendamento_apac;
alter table auditschema.solicitacao_agendamento_apac add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON solicitacao_agendamento_apac FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
