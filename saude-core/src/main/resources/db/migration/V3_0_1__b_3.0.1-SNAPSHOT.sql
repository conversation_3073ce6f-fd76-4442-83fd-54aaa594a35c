/*
    Marcus - 16/07/2013
*/

alter table profissional add numero_crm varchar(30);
alter table profissional_carga_horaria
   drop constraint PK_PROFISSIONAL_CARGA_HORARIA;

alter table profissional_carga_horaria add cd_prof_carga_horaria int8;

update profissional_carga_horaria set cd_prof_carga_horaria = nextval('seq_gem');

alter table profissional_carga_horaria alter column cd_prof_carga_horaria set not null;

alter table profissional_carga_horaria
   add constraint PK_PROFISSIONAL_CARGA_HORARIA primary key (cd_prof_carga_horaria);

/*==============================================================*/
/* Index: idx_prof_carga_horaria                                */
/*==============================================================*/
create unique index idx_prof_carga_horaria on profissional_carga_horaria (
empresa,
cd_profissional,
cd_cbo,
tp_sus_nao_sus,
cd_vinculacao,
cd_tipo_vinculo,
cd_subtipo_vinculo
);

alter table profissional_historico rename seq_prof_historico to cd_prof_historico;

alter table profissional_historico
   drop constraint PK_PROFISSIONAL_HISTORICO;

update profissional_historico set cd_prof_historico = nextval('seq_gem');

alter table profissional_historico
   add constraint PK_PROFISSIONAL_HISTORICO primary key (cd_prof_historico);

alter table profissional_historico add cd_prof_carga_horaria INT8 null;

alter table profissional_historico
   add constraint FK_PRO_HIS_REF_PRO_CARGA_HOR foreign key (cd_prof_carga_horaria)
      references profissional_carga_horaria (cd_prof_carga_horaria)
      on delete restrict on update restrict;

update profissional_historico ph set cd_prof_carga_horaria =
(
   select
   min(pch.cd_prof_carga_horaria)
   from profissional_carga_horaria pch
   where pch.empresa = ph.empresa
   and pch.cd_profissional = ph.cd_profissional
   and pch.cd_cbo = ph.cd_cbo
   and pch.tp_sus_nao_sus = ph.tp_sus_nao_sus
   and pch.cd_vinculacao = ph.cd_vinculacao
   and pch.cd_tipo_vinculo = ph.cd_tipo_vinculo
   and pch.cd_subtipo_vinculo = ph.cd_subtipo_vinculo
);


/*
    Sulivan - 23/07/2013
*/
alter table receituario add situacao int8;
alter table receituario add dt_baixa timestamp;
alter table receituario add cd_usuario_baixa numeric(6);
alter table receituario add motivo_baixa VARCHAR(200) null;

update receituario set situacao = 0 where situacao is null;

alter table receituario alter column situacao set not null;

alter table receituario
   add constraint FK_RECEIT_REF_USU_BAIXA foreign key (cd_usuario_baixa)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

/*
    Sulivan - 25/07/2013
*/
INSERT INTO programa_pagina VALUES(479, 'br.com.celk.view.materiais.dispensacao.relatorio.RelatorioRelacaoPrescricoesNaoDispensadasPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES(284,'Relação das Prescrições não Dispensadas',479,'N');
INSERT INTO programa_web_pagina VALUES(480, 284, 479);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES(398,'Relação das Prescrições não Dispensadas','relatorioRelacaoPrescricoesNaoDispensadas',252,284,14,0);

/*
    Sulivan - 26/07/2013
*/
DELETE FROM controle_programa_web WHERE cd_prg_web = 277;
DELETE FROM controle_programa_web_grupo WHERE cd_prg_web = 277;
DELETE FROM painel_controle_programa_web WHERE cd_prg_web = 277;
DELETE FROM programa_favorito WHERE cd_prg_web = 277;
DELETE FROM programa_web_pagina WHERE cd_prg_web = 277;
DELETE FROM menu_web WHERE cd_menu = 387;
DELETE FROM programa_web_pagina WHERE cd_prg_web_pagina = 472;
DELETE FROM programa_web WHERE cd_prg_web = 277;
DELETE FROM programa_pagina_permissao WHERE cd_prg_pagina = 471;
DELETE FROM programa_pagina WHERE cd_prg_pagina = 471;

/*
    Sulivan - 26/07/2013
*/
INSERT INTO programa_pagina_permissao (cd_prog_pag_perm,cd_permissao,cd_prg_pagina,version,ds_bundle) VALUES (58,3,456,0,null);
INSERT INTO programa_pagina_permissao (cd_prog_pag_perm,cd_permissao,cd_prg_pagina,version,ds_bundle) VALUES (59,4,456,0,null);

/*
    Claudio - 31/07/2013
*/
INSERT INTO programa_pagina VALUES(481, 'br.com.celk.view.vacina.vacinaaplicacao.AplicarVacinaPage', 'N');
INSERT INTO programa_pagina VALUES(482, 'br.com.celk.view.vacina.vacinaaplicacao.RegistrarHistoricoVacinacaoPage', 'N');

INSERT INTO programa_web_pagina VALUES(482, 36, 481);
INSERT INTO programa_web_pagina VALUES(483, 36, 482);

alter table vac_calendario add version int8 not null;

/*
 PostgreSQL
 Everton - 01/08/2013
*/

ALTER TABLE usuario_cadsus_dado ADD cd_cla_atendimento INT4 null;
alter table usuario_cadsus_dado
   add constraint FK_USUSUS_DADO_REF_CLA_ATE foreign key (cd_cla_atendimento)
      references classificacao_atendimento (cd_cla_atendimento)
      on delete restrict on update restrict;

/*
    Sulivan - 05/08/2013
*/
INSERT INTO programa_pagina VALUES(483, 'br.com.celk.view.unidadesaude.receituario.ConsultaPosologiaPadraoMedicamentosPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (286,'Medicamento Posologia',483,'N');
INSERT INTO programa_web_pagina VALUES (484, 286, 483);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (400,'Medicamento Posologia','medicamentoPosologia',324,286,143,0);

/*
    PostgreSQL
    Everton - 05/08/2013
*/

/*==============================================================*/
/* Table: produto_posologia                                     */
/*==============================================================*/
create table produto_posologia (
cd_prd_posologia     INT8                 not null,
cod_pro              CHAR(13)             not null,
qtdade               INT4                 not null,
frequencia           INT2                 not null,
intervalo            INT2                 null,
periodo_tratamento   INT4                 not null,
version              INT8                 not null
);

alter table produto_posologia
   add constraint PK_PRODUTO_POSOLOGIA primary key (cd_prd_posologia);

/*==============================================================*/
/* Index: idx_prd_posologia                                     */
/*==============================================================*/
create unique index idx_prd_posologia on produto_posologia (
cod_pro
);

alter table produto_posologia
   add constraint FK_PRD_POSOLOGIA_REF_PRODUTO foreign key (cod_pro)
      references produtos (cod_pro)
      on delete restrict on update restrict;

/*
 PostgreSQL
 Everton - 05/08/2013
*/

alter table tipo_atendimento add can_pri_atendimento    INT2;
update tipo_atendimento set can_pri_atendimento = 0;
alter table tipo_atendimento alter can_pri_atendimento set not null;

/*
 PostgreSQL
 Everton - 05/08/2013
*/

alter table tipo_receita add flag_lista_receita   INT2;
update tipo_receita set flag_lista_receita = 1;
alter table tipo_receita alter flag_lista_receita set not null;

/*
    Sulivan 06/08/2013
*/
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (401,'Processo','processo',312,null,null,0);
INSERT INTO programa_pagina VALUES (484, 'br.com.celk.view.unidadesaude.bpa.processo.GeracaoBPAPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (287,'Geração do BPA',484,'N');
INSERT INTO programa_web_pagina VALUES (485, 287, 484);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (402,'Geração do BPA','geracaoBPA',401,287,143);
DELETE FROM empresa_bpa;

/*
 PostgreSQL
 Everton - 06/08/2013
*/

/*==============================================================*/
/* Table: empresa_convenio_bpa                                  */
/*==============================================================*/
create table empresa_convenio_bpa (
cd_emp_conv_bpa      INT8                 not null,
cd_convenio          INT8                 null,
empresa              INT4                 null,
version              INT8                 not null
);

alter table empresa_convenio_bpa
   add constraint PK_EMPRESA_CONVENIO_BPA primary key (cd_emp_conv_bpa);

/*==============================================================*/
/* Index: IDX_EMPRESA_CONVENIO                                  */
/*==============================================================*/
create unique index IDX_EMPRESA_CONVENIO on empresa_convenio_bpa (
cd_convenio,
empresa
);

alter table empresa_convenio_bpa
   add constraint FK_EMP_CONV_BPA_REF_CONV foreign key (cd_convenio)
      references convenio (cd_convenio)
      on delete restrict on update restrict;

alter table empresa_convenio_bpa
   add constraint FK_EMP_CONV_BPA_REF_EMP foreign key (empresa)
      references empresa (empresa)
      on delete restrict on update restrict;
/*
 PostgreSQL
 Everton - 07/08/2013
*/

alter table laudo_tfd alter cd_numero_cartao drop not null;

/*
 PostgreSQL
 Claudio - 08/08/2013
*/
INSERT INTO programa_pagina_permissao (cd_prog_pag_perm,cd_permissao,cd_prg_pagina,version,ds_bundle) VALUES (60,9,240,0,null);

alter table pagamento_guia_procedimento add status int2;
update pagamento_guia_procedimento set status = 1;
alter table pagamento_guia_procedimento alter column status set not null;
alter table pagamento_guia_procedimento add dt_estorno timestamp;
alter table pagamento_guia_procedimento add cd_usu_estorno numeric(6);
alter table pagamento_guia_procedimento
   add constraint FK_PAG_GUIA_USU_ESTORNO foreign key (cd_usu_estorno)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

alter table pagamento_guia_procedimento add chave int8;
create sequence seq_pagamento_guia_procedimento;
update pagamento_guia_procedimento set chave = t1.sequencia from 
(select p1.cd_pagamento, nextval('seq_pagamento_guia_procedimento') as sequencia from pagamento_guia_procedimento p1 order by p1.cd_pagamento) as t1
where t1.cd_pagamento = pagamento_guia_procedimento.cd_pagamento;
alter table pagamento_guia_procedimento alter column chave set not null;

/*
 Leandro - 08/08/2013
*/
/*==============================================================*/
/* Table: exame_padrao                                          */
/*==============================================================*/
create table exame_padrao (
cd_exame_padrao      INT8                 not null,
cd_exame_procedimento INT4                 not null,
posicao              INT8                 not null,
version              INT8                 not null
);

alter table exame_padrao
   add constraint PK_EXAME_PADRAO primary key (cd_exame_padrao);

/*==============================================================*/
/* Index: idx_exame_procedimento                                */
/*==============================================================*/
create unique index idx_exame_procedimento on exame_padrao (
cd_exame_procedimento
);

alter table exame_padrao
   add constraint FK_EXA_RANK_REF_EXA_PROC foreign key (cd_exame_procedimento)
      references exame_procedimento (cd_exame_procedimento)
      on delete restrict on update restrict;

INSERT INTO programa_pagina VALUES(485, 'br.com.celk.view.basico.examepadrao.CadastroExamePadraoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES(288,'Exames Padrão',485,'N');
INSERT INTO programa_web_pagina VALUES(486, 288, 485);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (403,'Exames Padrão','examesPadrao',218,288,143,0);

/*
 Marcus - 09/08/2013
*/
insert into permissao_web VALUES(25,'Troca Convênio',0);
insert into programa_pagina_permissao values(61,25,477,0,null);

/*
 Felipe - 09/08/2013
*/
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (212,'Detalhamento dos Pagamentos','detalhamentoPagamentos',183,150,111,0);

/*
    Sulivan 09/08/2013
*/
ALTER TABLE usuario_cadsus_dado DROP COLUMN cd_cla_atendimento;

/*
    Sulivan 12/08/2013
*/
INSERT INTO programa_pagina VALUES(486, 'br.com.celk.view.unidadesaude.receituario.CadastroPosologiaPadraoMedicamentosPage', 'N');
INSERT INTO programa_web_pagina VALUES(487, 286, 486);

/*
 Marcus - 13/08/2013
*/
alter TABLE permissao_web alter column ds_permissao type varchar(30);
insert into permissao_web values('27','Reimpressão Prontuário',0);
insert into programa_pagina_permissao values (63,27,477,0,null);

/*
    Sulivan 14/08/2013
*/
UPDATE programa_pagina SET cam_pagina = 'br.com.celk.view.materiais.dispensacao.relatorio.RelatorioRelacaoPrescricoesEmAbertoPage' WHERE cd_prg_pagina = 479;
UPDATE programa_web SET ds_prg_web = 'Relação das Prescrições em Aberto' WHERE cd_prg_web = 284;
UPDATE menu_web SET ds_menu = 'Relação das Prescrições em Aberto', ds_bundle = 'relatorioRelacaoPrescricoesEmAberto' WHERE cd_menu = 398;

/*
    Claudio - 15/07/2013
*/
create table elo_grupo_atend_cbo_usuario (
cd_elo int8 not null,
cd_grupo_atend_cbo int8 not null,
cd_usuario numeric(6) not null,
version int8 not null
);

alter table elo_grupo_atend_cbo_usuario
   add constraint PK_elo_gp_at_cbo_usuario primary key (cd_elo);
alter table elo_grupo_atend_cbo_usuario add constraint FK_ELO_GP_AT_GP_CBO FOREIGN KEY (cd_grupo_atend_cbo) REFERENCES GRUPO_ATENDIMENTO_CBO(cd_grupo_atend_cbo);
alter table elo_grupo_atend_cbo_usuario add constraint FK_ELO_GP_AT_USU FOREIGN KEY (cd_usuario) REFERENCES usuarios(cd_usuario);