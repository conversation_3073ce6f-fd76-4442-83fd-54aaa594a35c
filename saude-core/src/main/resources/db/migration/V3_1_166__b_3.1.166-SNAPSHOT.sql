SET application_name = 'flyway|3.1.166';

SET statement_timeout TO 600000;

/*==============================================================*/
/* Table: auxilio_brasil                                        */
/*==============================================================*/
create table auxilio_brasil (
    cd_auxilio_brasil                           INT4              not null,
    cd_usu_cadsus                               NUMERIC(8)        null,
    dt_atendimento                              TIMESTAMP         null,
    peso                                        NUMERIC(12,2)     null,
    altura                                      INT4              null,
    vacina_em_dia                               INT2              null,
    gestante                                    INT2              null,
    dt_pre_natal     	                        date              null,
    dum                                         date              null,
    situacao                                    INT2              null,
    ano                                         varchar(4)        null,
    semestre                                    varchar(4)        null,
    motivo_nao_acompanhamento                   INT8              null,
    motivo_de_dado_nutricional_nao_coletado     INT8              null,
    motivo_de_nao_vacinacao                     INT8              null,
    motivo_de_nao_realizacao_do_pre_natal       INT8              null,
    tipo_integrante                             INT8              null,
    dt_cadastro 		                        TIMESTAMP 		  null,
    version 			                        INT8 			  not null
);

alter table auxilio_brasil
        add constraint PK_AUXILIO_BRASIL primary key (cd_auxilio_brasil);

alter table auxilio_brasil
        add constraint FK_AB_REF_CADSUS foreign key (cd_usu_cadsus)
        references usuario_cadsus (cd_usu_cadsus)
        on delete restrict on update restrict;

create table auditschema.auxilio_brasil as select t2.*, t1.* from auxilio_brasil t1, audit_temp t2 where 1=2;create sequence seq_audit_id_auxilio_brasil;alter table auditschema.auxilio_brasil add primary key (audit_id);create trigger emp_audit after insert or update or delete on auxilio_brasil for each row execute procedure process_emp_audit();


/*==============================================================*/
/* Table: motivos_descumprimento_auxilio_brasil                 */
/*==============================================================*/
create table motivos_descumprimento_auxilio_brasil (
    cd_motivo_descumprimento                    INT4              not null,
    descricao                                   varchar(350)      not null,
    motivo_de_dado_nutricional_nao_coletado     INT8              not null,
    motivo_de_nao_vacinacao                     INT8              not null,
    motivo_de_nao_realizacao_do_pre_natal       INT8              not null,
    version 			                        INT8 			  not null
);

alter table motivos_descumprimento_auxilio_brasil
        add constraint PK_MOTIVO_DESCUMPRIMENTO primary key (cd_motivo_descumprimento);

create table auditschema.motivos_descumprimento_auxilio_brasil as select t2.*, t1.* from motivos_descumprimento_auxilio_brasil t1, audit_temp t2 where 1=2;create sequence seq_audit_id_motivos_descumprimento_auxilio_brasil;alter table auditschema.motivos_descumprimento_auxilio_brasil add primary key (audit_id);create trigger emp_audit after insert or update or delete on motivos_descumprimento_auxilio_brasil for each row execute procedure process_emp_audit();

INSERT INTO MOTIVOS_DESCUMPRIMENTO_AUXILIO_BRASIL(cd_motivo_descumprimento,descricao,motivo_de_dado_nutricional_nao_coletado,motivo_de_nao_vacinacao,motivo_de_nao_realizacao_do_pre_natal,version) VALUES(1,'Condições de saúde que impedem a ida à UBS',1,1,1,0);
INSERT INTO MOTIVOS_DESCUMPRIMENTO_AUXILIO_BRASIL(cd_motivo_descumprimento,descricao,motivo_de_dado_nutricional_nao_coletado,motivo_de_nao_vacinacao,motivo_de_nao_realizacao_do_pre_natal,version) VALUES(2,'Fatos que impedem o deslocamento/acesso à UBS (enchente, falta de transporte, violência no território, etc.)',1,1,1,0);
INSERT INTO MOTIVOS_DESCUMPRIMENTO_AUXILIO_BRASIL(cd_motivo_descumprimento,descricao,motivo_de_dado_nutricional_nao_coletado,motivo_de_nao_vacinacao,motivo_de_nao_realizacao_do_pre_natal,version) VALUES(3,'Horário de atendimento na UBS inviável para o(a) responsável/beneficiário(a)',1,1,1,0);
INSERT INTO MOTIVOS_DESCUMPRIMENTO_AUXILIO_BRASIL(cd_motivo_descumprimento,descricao,motivo_de_dado_nutricional_nao_coletado,motivo_de_nao_vacinacao,motivo_de_nao_realizacao_do_pre_natal,version) VALUES(4,'Responsável/Beneficiário(a) não cumpriu as condicionalidades por questões sociais, culturais, étnicas ou religiosas',1,1,1,0);
INSERT INTO MOTIVOS_DESCUMPRIMENTO_AUXILIO_BRASIL(cd_motivo_descumprimento,descricao,motivo_de_dado_nutricional_nao_coletado,motivo_de_nao_vacinacao,motivo_de_nao_realizacao_do_pre_natal,version) VALUES(5,'Condições de saúde que dificultam a coleta dos dados nutricionais (edema, amputação, acamado(a), cadeirante, etc.)',1,0,0,0);
INSERT INTO MOTIVOS_DESCUMPRIMENTO_AUXILIO_BRASIL(cd_motivo_descumprimento,descricao,motivo_de_dado_nutricional_nao_coletado,motivo_de_nao_vacinacao,motivo_de_nao_realizacao_do_pre_natal,version) VALUES(6,'Falta de equipamentos antropométricos (balança, antropômetro, etc.)',1,0,0,0);
INSERT INTO MOTIVOS_DESCUMPRIMENTO_AUXILIO_BRASIL(cd_motivo_descumprimento,descricao,motivo_de_dado_nutricional_nao_coletado,motivo_de_nao_vacinacao,motivo_de_nao_realizacao_do_pre_natal,version) VALUES(7,'Falta de profissionais capacitados para realizar a coleta dos dados nutricionais',1,0,0,0);
INSERT INTO MOTIVOS_DESCUMPRIMENTO_AUXILIO_BRASIL(cd_motivo_descumprimento,descricao,motivo_de_dado_nutricional_nao_coletado,motivo_de_nao_vacinacao,motivo_de_nao_realizacao_do_pre_natal,version) VALUES(8,'Responsável/Beneficiário(a) foi informado (a) pessoalmente de que deveria comparecer à UBS para realizar o acompanhamento das condicionalidades de saúde, mas não o fez',1,1,1,0);
INSERT INTO MOTIVOS_DESCUMPRIMENTO_AUXILIO_BRASIL(cd_motivo_descumprimento,descricao,motivo_de_dado_nutricional_nao_coletado,motivo_de_nao_vacinacao,motivo_de_nao_realizacao_do_pre_natal,version) VALUES(9,'Houve recusa em realizar o acompanhamento das condicionalidades dentro da rotina de Atenção Básica de Saúde.',1,1,1,0);
INSERT INTO MOTIVOS_DESCUMPRIMENTO_AUXILIO_BRASIL(cd_motivo_descumprimento,descricao,motivo_de_dado_nutricional_nao_coletado,motivo_de_nao_vacinacao,motivo_de_nao_realizacao_do_pre_natal,version) VALUES(10,'Indícios de situação de risco social tal como negligência, abuso sexual, violência intrafamiliar ou outras.',1,1,1,0);
INSERT INTO MOTIVOS_DESCUMPRIMENTO_AUXILIO_BRASIL(cd_motivo_descumprimento,descricao,motivo_de_dado_nutricional_nao_coletado,motivo_de_nao_vacinacao,motivo_de_nao_realizacao_do_pre_natal,version) VALUES(11,'Responsável/Beneficiário (a) afirma que não é mais do programa',1,1,1,0);
INSERT INTO MOTIVOS_DESCUMPRIMENTO_AUXILIO_BRASIL(cd_motivo_descumprimento,descricao,motivo_de_dado_nutricional_nao_coletado,motivo_de_nao_vacinacao,motivo_de_nao_realizacao_do_pre_natal,version) VALUES(12,'Criança com condição específica de saúde que necessita de vacina especial (CRIE)',0,1,0,0);
INSERT INTO MOTIVOS_DESCUMPRIMENTO_AUXILIO_BRASIL(cd_motivo_descumprimento,descricao,motivo_de_dado_nutricional_nao_coletado,motivo_de_nao_vacinacao,motivo_de_nao_realizacao_do_pre_natal,version) VALUES(13,'Falta de oferta de vacina ou de insumos necessários para vacinação (seringas, luvas, algodão, etc.)',0,1,0,0);
INSERT INTO MOTIVOS_DESCUMPRIMENTO_AUXILIO_BRASIL(cd_motivo_descumprimento,descricao,motivo_de_dado_nutricional_nao_coletado,motivo_de_nao_vacinacao,motivo_de_nao_realizacao_do_pre_natal,version) VALUES(14,'Falta de oferta de serviço de pré-natal',0,0,1,0);

/*==============================================================*/
/*                     CRIAÇÃO DO MENU                          */
/*==============================================================*/

INSERT INTO programa_pagina VALUES (1987, 'br.com.celk.view.atendimento.programas.auxilioBrasil.ConsultaManutencaoAuxilioBrasilPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (1102, 'Manutenção auxílio Brasil', 1987, 'N');
INSERT INTO programa_web_pagina VALUES (2074, 1102, 1987);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1000118,'Manutenção Auxílio Brasil','consulta_manutencao_auxilio_brasil',464,1102,143,0,0);

/* AMB-2071 */
alter table teste_rapido_realizado add column nr_teste_rapido int4;
alter table auditschema.teste_rapido_realizado add column nr_teste_rapido int4;