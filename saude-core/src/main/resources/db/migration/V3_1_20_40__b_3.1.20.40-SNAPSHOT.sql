SET application_name = 'flyway|3.1.20.40';

/*
    Claudio - 02/09/2019
*/
DO
$$
BEGIN
   IF to_regclass('public.idxdis_med_ref_empresa') IS NULL THEN
    CREATE INDEX idxdis_med_ref_empresa on dispensacao_medicamento (empresa);
   END IF;
END
$$;

/*
    #27037 - Elton - 30/08/2019
*/
ALTER TABLE atendimento ADD COLUMN nr_gestas_vaginal int2 null;
ALTER TABLE auditschema.atendimento ADD COLUMN nr_gestas_vaginal int2 null;

ALTER TABLE atendimento ADD COLUMN nr_gestas_cesariana int2 null;
ALTER TABLE auditschema.atendimento ADD COLUMN nr_gestas_cesariana int2 null;

ALTER TABLE atendimento ADD COLUMN nr_gestas_aborto int2 null;
ALTER TABLE auditschema.atendimento ADD COLUMN nr_gestas_aborto int2 null;

ALTER TABLE atendimento ADD COLUMN dt_primeira_usg date null;
ALTER TABLE auditschema.atendimento ADD COLUMN dt_primeira_usg date null;

ALTER TABLE atendimento ADD COLUMN idade_gestacional_usg int2 null;
ALTER TABLE auditschema.atendimento ADD COLUMN idade_gestacional_usg int2 null;

ALTER TABLE atendimento ADD COLUMN dpp_dum date null;
ALTER TABLE auditschema.atendimento ADD COLUMN dpp_dum date null;

ALTER TABLE atendimento ADD COLUMN dpp_usg date null;
ALTER TABLE auditschema.atendimento ADD COLUMN dpp_usg date null;

/*
    Maicon - 22/08/2019 - #27163
*/
create table produto_cid_documento (
    cd_produto_cid_documento int8 not null,
    cod_pro varchar(13) not null,
    cd_cid varchar(8) not null,
    cd_gerenciador_arquivo INT8 null,
    cd_usuario numeric(6) not null,
    dt_cadastro timestamp not null,
    version int8 not null
);
create sequence seq_produto_cid_documento;

alter table produto_cid_documento
   add constraint pk_produto_cid_documento primary key (cd_produto_cid_documento);

alter table produto_cid_documento
        add constraint FK_PROD_CID_DOC_REF_PRODUTO foreign key (cod_pro)
        references produtos (cod_pro)
        on delete restrict on update restrict;

alter table produto_cid_documento
   add constraint FK_PROD_CID_DOC_REF_CID foreign key (cd_cid)
      references cid (cd_cid)
      on delete restrict on update restrict;

alter table produto_cid_documento
   add constraint FK_PROD_CID_DOC_REF_USUARIO foreign key (cd_usuario)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

alter table produto_cid_documento
   add constraint FK_PROD_CID_DOC_REF_ANEXO foreign key (cd_gerenciador_arquivo)
      references gerenciador_arquivo (cd_gerenciador_arquivo)
      on delete restrict on update restrict;

CREATE TABLE auditschema.produto_cid_documento AS SELECT t2.*, t1.* FROM produto_cid_documento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_produto_cid_documento;alter table auditschema.produto_cid_documento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON produto_cid_documento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();