/*
    Laudecir - 27/11/2014
*/
alter table atendimento_primario add pam int4 null;
alter table atendimento_primario drop padm;
alter table atendimento_primario drop pasm;

create table atendimento_oxigenoterapia (
    cd_atend_oxg		INT8	not null,
    cd_atendimento_primario	INT8	not null,
    tp_oxg			INT2	not null,
    freq_oxg 			INT4	null,
    freq_resp 			INT4 	null,
    peep 			INT4 	null,
    pip				INT4	null,
    oxigenio			INT4	null,
    ar_comp			INT4	null,
    version			INT8	not null
);

alter table atendimento_oxigenoterapia
        add constraint PK_ATEND_OXG primary key (cd_atend_oxg);

alter table atendimento_oxigenoterapia
        add constraint FK_ATEND_OXG_REF_ATEND_PRIM foreign key (cd_atendimento_primario)
        references atendimento_primario (cd_atendimento_primario)
        on delete restrict on update restrict;
