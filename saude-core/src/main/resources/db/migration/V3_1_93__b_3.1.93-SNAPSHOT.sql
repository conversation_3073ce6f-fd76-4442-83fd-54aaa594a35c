SET application_name = 'flyway|3.1.93';

SET statement_timeout TO 600000;

/*VSA-1505     Cristian      2021-05-31*/

CREATE SEQUENCE public.seq_id_estabelecimento_setores_atividade
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	NO CYCLE;

CREATE TABLE public.estabelecimento_setores_atividade (
	cd_estabelecimento_setores_atividade int8 NOT NULL,
	cd_estabelecimento_setores int8 NOT NULL,
	cd_estabelecimento_atividade int8 NOT NULL,
	"version" int8 NOT null DEFAULT 0,
	CONSTRAINT pk_cd_estabelecimento_setores_atividade PRIMARY KEY (cd_estabelecimento_setores_atividade),
	CONSTRAINT fk_est_set_atv_ref_est_set FOREIGN KEY (cd_estabelecimento_setores) REFERENCES public.estabelecimento_setores(cd_estabelecimento_setores),
	CONSTRAINT fk_est_set_atv_ref_atv_est FOREIGN KEY (cd_estabelecimento_atividade) REFERENCES public.estabelecimento_atividade(cd_estabelecimento_atividade)
);

CREATE TABLE auditschema.estabelecimento_setores_atividade AS SELECT t2.*, t1.* FROM estabelecimento_setores_atividade t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_estabelecimento_setores_atividade;
alter table auditschema.estabelecimento_setores_atividade add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON estabelecimento_setores_atividade FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

do $VSA1505$
declare
	est_set_grp RECORD;
begin
	raise notice '--iniciando script - agrupando setores por estabelecimento';
	for est_set_grp in (select est.cd_estabelecimento
							 , est.cd_estabelecimento_setores
							 , ea.cd_estabelecimento_atividade
						from estabelecimento_setores est
						join estabelecimento_atividade ea
							on	est.cd_estabelecimento = ea.cd_estabelecimento
							and est.cd_atividade_estabelecimento = ea.cd_atividade_estabelecimento
						order by est.cd_estabelecimento)
	loop
		raise notice 'estabelecimento: % - setor: % - atividade: % ',
				  est_set_grp.cd_estabelecimento
				, est_set_grp.cd_estabelecimento_setores
				, est_set_grp.cd_estabelecimento_atividade;
		raise notice 'insert into public.estabelecimento_setores_atividade(cd_estabelecimento_setores_atividade, cd_estabelecimento_setores, cd_estabelecimento_atividade) values(%,%,%)', nextVal('seq_id_estabelecimento_setores_atividade'),
				est_set_grp.cd_estabelecimento_setores, est_set_grp.cd_estabelecimento_atividade;
		insert into public.estabelecimento_setores_atividade(cd_estabelecimento_setores_atividade, cd_estabelecimento_setores, cd_estabelecimento_atividade, "version")
			   values (currVal('seq_id_estabelecimento_setores_atividade'), est_set_grp.cd_estabelecimento_setores, est_set_grp.cd_estabelecimento_atividade, 0);
	end loop;
end $VSA1505$;