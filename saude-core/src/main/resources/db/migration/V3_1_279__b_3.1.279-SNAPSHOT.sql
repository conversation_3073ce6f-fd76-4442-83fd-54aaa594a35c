SET application_name = 'flyway|3.1.279';

SET statement_timeout TO 600000;

/*
    feature/REG-2096 - <PERSON><PERSON><PERSON> Palma | 14/04/2025
*/

alter table  public.roteiro_viagem_passageiro  add column flag_impresso int2;
alter table  auditschema.roteiro_viagem_passageiro  add column flag_impresso int2;

alter table roteiro_viagem_passageiro disable trigger emp_audit;
update roteiro_viagem_passageiro set flag_impresso  = 1 where status <> 4;
alter table roteiro_viagem_passageiro enable trigger emp_audit;

/*
    feature/REG-2091 - <PERSON> | 08/04/2025
*/

ALTER TABLE leito_quarto ADD COLUMN cd_aut_intern_hosp_secundaria INT8;
ALTER TABLE auditschema.leito_quarto ADD COLUMN cd_aut_intern_hosp_secundaria INT8;

ALTER TABLE leito_quarto
ADD CONSTRAINT fk_cd_aut_intern_hosp_secundaria FOREIGN KEY (cd_aut_intern_hosp_secundaria) REFERENCES aih(cd_aut_intern_hosp);

-- feature/GMT-1428 - <PERSON>  24/04/2025

INSERT INTO programa_pagina VALUES (2134, 'br.com.celk.view.materiais.obm.ConsultaMedicamentoObmAmppPage', 'N');
INSERT INTO programa_pagina VALUES (2135, 'br.com.celk.view.materiais.obm.CadastroMedicamentoObmAmppPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (1187, 'Consulta OBM-AMPP', 2134, 'N');
INSERT INTO programa_web_pagina VALUES (2200, 1187, 2134);
INSERT INTO programa_web_pagina VALUES (2201, 1187, 2135);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version)
VALUES (1000257,'Consulta OBM-AMPP','consultaObmAmpp',166,1187,14,0,0);

-- feature/GMT-1427 - Ian Pires da Silva  15/04/2025

CREATE TABLE IF NOT EXISTS medicamento_obm_ampp (
    cd_medicamento_obm_ampp int8 not null PRIMARY KEY,
   	codigo_obm_ampp varchar,
   	descricao varchar,
   	codigo_registro_sanitario_ampp int8,
   	flag_uso_exlusivo_hpt int2,
   	version int8 not null
);
CREATE SEQUENCE IF NOT exists seq_medicamento_obm_ampp START WITH 1;

drop table IF exists auditschema.medicamento_obm_ampp;
drop SEQUENCE IF exists seq_audit_id_medicamento_obm_ampp;
drop trigger if exists emp_audit on medicamento_obm_ampp;

CREATE TABLE auditschema.medicamento_obm_ampp AS SELECT t2.*, t1.* FROM medicamento_obm_ampp t1, audit_temp t2 where 1=2;
CREATE SEQUENCE seq_audit_id_medicamento_obm_ampp;
ALTER TABLE auditschema.medicamento_obm_ampp add primary key (audit_id);
CREATE TRIGGER emp_audit after insert or update or delete on medicamento_obm_ampp for each row execute procedure process_emp_audit();

-- Adiciona a coluna
ALTER TABLE produtos ADD COLUMN cd_medicamento_obm_ampp int8;
ALTER TABLE auditschema.produtos ADD COLUMN cd_medicamento_obm_ampp int8;

-- Cria a foreign key
ALTER TABLE produtos ADD CONSTRAINT fk_produtos_medicamento_obm_ampp FOREIGN KEY (cd_medicamento_obm_ampp) REFERENCES medicamento_obm_ampp(cd_medicamento_obm_ampp);

-- Adiciona a coluna

ALTER TABLE produtos ADD COLUMN flag_habilitar_obm int2;
ALTER TABLE auditschema.produtos ADD COLUMN flag_habilitar_obm int2;
