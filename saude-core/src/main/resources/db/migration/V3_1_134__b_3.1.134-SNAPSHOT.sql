SET application_name = 'flyway|3.1.134';

SET statement_timeout TO 600000;

/*
 * <PERSON><PERSON><PERSON> - feature/AMB-2371 - 13/05/2022
 */

ALTER TABLE tipo_atendimento ADD COLUMN flag_notificacao_avulsa_atendimento int2;
ALTER TABLE auditschema.tipo_atendimento ADD COLUMN flag_notificacao_avulsa_atendimento int2;


CREATE TABLE IF NOT EXISTS public.atendimento_notificacao_avulsa(
	cd_atendimento_notificacao_avulsa bigint not null,
	nr_atendimento int8,
	cd_cid bpchar(8),
    version bigint,
    CONSTRAINT pk_atendimento_notificacao_avulsa PRIMARY KEY (cd_atendimento_notificacao_avulsa),
    constraint fk_atendimento_notificacao_avulsa_ref_atendimento foreign key (nr_atendimento)
    	references public.atendimento (nr_atendimento) match simple
    	on update restrict
    	on delete restrict,
	constraint fk_atendimento_notificacao_avulsa_ref_cid foreign key (cd_cid)
		references public.cid (cd_cid) match simple
		on update restrict
		on delete restrict
);

CREATE TABLE auditschema.atendimento_notificacao_avulsa
AS SELECT t2.*, t1.* FROM atendimento_notificacao_avulsa t1, audit_temp t2 WHERE 1=2;

create sequence seq_audit_id_atendimento_notificacao_avulsa;

alter table auditschema.atendimento_notificacao_avulsa add primary key (audit_id);

CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_notificacao_avulsa FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

create sequence seq_atendimento_notificacao_avulsa;

/*
 * Everton - AMB-2450 - 03/06/2022
 */

alter table usuario_cadsus disable trigger user;
update usuario_cadsus
   set cd_equipe_profissional = null
 where cd_equipe_profissional is not null and cd_equipe is null;
alter table usuario_cadsus enable trigger user;