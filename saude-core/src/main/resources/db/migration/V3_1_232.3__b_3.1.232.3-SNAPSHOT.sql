SET application_name = 'flyway|3.1.232.3';

SET statement_timeout TO 600000;

/* <PERSON> */

--  alter -> rnds_integracao_vacina
alter table rnds_integracao_vacina add column estrategia_ria int4 ;
alter table auditschema.rnds_integracao_vacina add column estrategia_ria int4;

-- create -> via_administracao
create table if not exists via_administracao  (
   cd_via_administracao int8 primary key not null,
   descricao 		  	varchar not null,
   version int8 not null
);

create table auditschema.via_administracao
as select t2.*, t1.* from via_administracao t1, audit_temp t2 where 1=2;
create sequence seq_audit_id_via_administracao;
alter table auditschema.via_administracao add primary key (audit_id);
create trigger emp_audit after insert or update or delete on via_administracao for each row execute procedure  process_emp_audit();

-- insert -> via_administracao
insert  into  via_administracao (cd_via_administracao, descricao, version)
values
    (10885, 'Intradérmica', 0),
    (10890, 'Intramuscular', 0),
    (10898, 'Intravenosa', 0),
    (10907, 'Oral', 0),
    (10916, 'Subcutânea', 0);

-- alter -> vac_aplicacao
alter table vac_aplicacao add  column  cd_via_administracao int8;
alter table vac_aplicacao add  constraint fk_cd_via_administracao foreign  key  (cd_via_administracao) references  via_administracao (cd_via_administracao);

alter table auditschema.vac_aplicacao add  column  cd_via_administracao int8;

-- create local_aplicacao
create table if not exists local_aplicacao (
   cd_local_aplicacao int8 primary key not null,
   descricao 		  varchar not null,
   version int8 not null
);

create table auditschema.local_aplicacao
as select t2.*, t1.* from local_aplicacao t1, audit_temp t2 where 1=2;
create sequence seq_audit_id_local_aplicacao;
alter table auditschema.local_aplicacao add primary key (audit_id);
create trigger emp_audit after insert or update or delete on local_aplicacao for each row execute procedure  process_emp_audit();

-- insert -> local_aplicacao
insert into local_aplicacao (cd_local_aplicacao, descricao, version) values

(0,'Sem registro no sistema de informação de origem',0),
(1, 'Deltóide Direito', 0),
(2, 'Deltóide Esquerdo', 0),
(3, 'Vasto Lateral da Coxa Direita', 0),
(4, 'Vasto Lateral da Coxa Esquerda', 0),
(5, 'Ventroglúteo Direito', 0),
(6, 'Ventroglúteo Esquerdo', 0),
(7, 'Glúteo (descontinuado)', 0),
(8, 'Ferimento Local', 0),
(9, 'Boca', 0),
(10, 'Dorso Glúteo Direito', 0),
(11, 'Dorso Glúteo Esquerdo', 0),
(12, 'Face Externa Inferior do Braço Direito', 0),
(13, 'Face Externa Inferior do Braço Esquerdo', 0),
(14, 'Face Externa Superior do Braço Direito', 0),
(15, 'Face Externa Superior do Braço Esquerdo', 0),
(16, 'Rede Venosa', 0),
(17, 'Face Anterolateral Externa da Coxa Direita', 0),
(18, 'Face Anterolateral Externa da Coxa Esquerda', 0),
(19, 'Face Anterolateral Externa do Braço Direito', 0),
(20, 'Face Anterolateral Externa do Braço Esquerdo', 0),
(21, 'Face Anterolateral Externa do Antebraço Direito', 0),
(22, 'Face Anterolateral Externa do Antebraço Esquerdo', 0),
(99, 'Outro', 0);

-- alter -> vac_aplicacao
alter table vac_aplicacao add  column  cd_local_aplicacao int8;
alter table vac_aplicacao add  constraint fk_cd_local_aplicacao foreign  key  (cd_local_aplicacao) references  local_aplicacao (cd_local_aplicacao);

alter table auditschema.vac_aplicacao add  column  cd_local_aplicacao int8;

-- create -> via_adm_local_aplicacao(Tabela auxiliar)
create table if not exists via_adm_local_aplicacao (
   cd_via_adm_local_aplicacao int8  not null,
   cd_local_aplicacao int8  not null,
   cd_via_administracao int8 not null,
   version int8 not null
);

alter table via_adm_local_aplicacao add constraint pk_via_adm_local_aplicacao primary key (cd_via_adm_local_aplicacao);
create  sequence seq_via_adm_local_aplicacao start with 1;
alter table via_adm_local_aplicacao add  constraint fk_cd_via_administracao foreign  key  (cd_via_administracao) references  via_administracao (cd_via_administracao);
alter table via_adm_local_aplicacao add  constraint fk_cd_local_aplicacao foreign  key  (cd_local_aplicacao) references  local_aplicacao (cd_local_aplicacao);

create table auditschema.via_adm_local_aplicacao
as select t2.*, t1.* from via_adm_local_aplicacao t1, audit_temp t2 where 1=2;
create sequence seq_audit_id_via_adm_local_aplicacao;
alter table auditschema.via_adm_local_aplicacao add primary key (audit_id);
create trigger emp_audit after insert or update or delete on via_adm_local_aplicacao for each row execute procedure  process_emp_audit();

-- insert -> via_adm_local_aplicacao(Tabela auxiliar)
insert  into  via_adm_local_aplicacao (cd_via_adm_local_aplicacao, cd_local_aplicacao, cd_via_administracao, version)
values
    -- Intravenosa
    (nextval('seq_via_adm_local_aplicacao'), 16, 10898, 0),
    -- Intradérmica
    (nextval('seq_via_adm_local_aplicacao'), 17, 10885, 0),
    (nextval('seq_via_adm_local_aplicacao'), 18, 10885, 0),
    (nextval('seq_via_adm_local_aplicacao'), 21, 10885, 0),
    (nextval('seq_via_adm_local_aplicacao'), 22, 10885, 0),
    (nextval('seq_via_adm_local_aplicacao'), 19, 10885, 0),
    (nextval('seq_via_adm_local_aplicacao'), 20, 10885, 0),
    (nextval('seq_via_adm_local_aplicacao'), 12, 10885, 0),
    (nextval('seq_via_adm_local_aplicacao'), 13, 10885, 0),
    (nextval('seq_via_adm_local_aplicacao'), 14, 10885, 0),
    (nextval('seq_via_adm_local_aplicacao'), 15, 10885, 0),
    (nextval('seq_via_adm_local_aplicacao'), 99, 10885, 0),
    -- Intramuscular
    (nextval('seq_via_adm_local_aplicacao'), 1, 10890, 0),
    (nextval('seq_via_adm_local_aplicacao'), 2, 10890, 0),
    (nextval('seq_via_adm_local_aplicacao'), 10, 10890, 0),
    (nextval('seq_via_adm_local_aplicacao'), 11, 10890, 0),
    (nextval('seq_via_adm_local_aplicacao'), 3, 10890, 0),
    (nextval('seq_via_adm_local_aplicacao'), 4, 10890, 0),
    (nextval('seq_via_adm_local_aplicacao'), 5, 10890, 0),
    (nextval('seq_via_adm_local_aplicacao'), 6, 10890, 0),
    (nextval('seq_via_adm_local_aplicacao'), 99, 10890, 0),
    -- Oral
    (nextval('seq_via_adm_local_aplicacao'), 9, 10907, 0),
    -- Subcutânea
    (nextval('seq_via_adm_local_aplicacao'), 17, 10916, 0),
    (nextval('seq_via_adm_local_aplicacao'), 18, 10916, 0),
    (nextval('seq_via_adm_local_aplicacao'), 21, 10916, 0),
    (nextval('seq_via_adm_local_aplicacao'), 22, 10916, 0),
    (nextval('seq_via_adm_local_aplicacao'), 19, 10916, 0),
    (nextval('seq_via_adm_local_aplicacao'), 20, 10916, 0),
    (nextval('seq_via_adm_local_aplicacao'), 12, 10916, 0),
    (nextval('seq_via_adm_local_aplicacao'), 13, 10916, 0);

-- alter -> fabricante_medicamento
alter table fabricante_medicamento add column codigo_pni int8;
alter table auditschema.fabricante_medicamento add column codigo_pni int8;
