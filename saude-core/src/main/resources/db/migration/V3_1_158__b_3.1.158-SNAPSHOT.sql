SET application_name = 'flyway|3.1.158';

SET statement_timeout TO 600000;

/*
 * <PERSON><PERSON><PERSON> - AMB-2744 - 24/11/22
*/
ALTER TABLE public.formulario_saude_mental ADD flag_ansiedade_medo_persistente int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_insonia_hipersonia int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_crises_conversivas_dissociativas int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_alteracao_apetite int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_preocupacao_excessiva_peso int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_tristeza_persistente int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_prejuizo_atividade_sexual int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_ideacao_suicida_sem_planejamento int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_apatia_diminuicao_desempenho_social int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_humor_instavel int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_heteroagressividade_autolesivo int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_aumento_atividade_motora int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_humor_anormalmente_elevado int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_perda_capacidade_julgamento_realidade int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_alteracao_memoria int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_consumo_progressivo_substancia_psicoativa int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_dificuldade_compreender_transmitir_informacao int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_movimentos_corporais_comportamentais_repetitivos int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_dificuldade_adquirir_desenvolver_habilidades_escolares int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_dificuldade_adquirir_desenvolver_habilidades_motoras int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_severa_dificuldade_interacao_social int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_desatencao_interrupcao_prematura_tarefas int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_comportamento_provocativo int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_comportamento_reacoes_emocionais int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_perda_funcionalidade_familiar_social int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_vulnerabilidade_social int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_historico_familiar_transtorno_mental int2 NULL;
ALTER TABLE public.formulario_saude_mental ADD flag_faixa_etaria_menores_18_maiores_60 int2 NULL;

ALTER TABLE auditschema.formulario_saude_mental ADD flag_ansiedade_medo_persistente int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_insonia_hipersonia int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_crises_conversivas_dissociativas int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_alteracao_apetite int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_preocupacao_excessiva_peso int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_tristeza_persistente int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_prejuizo_atividade_sexual int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_ideacao_suicida_sem_planejamento int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_apatia_diminuicao_desempenho_social int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_humor_instavel int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_heteroagressividade_autolesivo int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_aumento_atividade_motora int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_humor_anormalmente_elevado int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_perda_capacidade_julgamento_realidade int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_alteracao_memoria int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_consumo_progressivo_substancia_psicoativa int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_dificuldade_compreender_transmitir_informacao int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_movimentos_corporais_comportamentais_repetitivos int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_dificuldade_adquirir_desenvolver_habilidades_escolares int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_dificuldade_adquirir_desenvolver_habilidades_motoras int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_severa_dificuldade_interacao_social int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_desatencao_interrupcao_prematura_tarefas int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_comportamento_provocativo int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_comportamento_reacoes_emocionais int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_perda_funcionalidade_familiar_social int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_vulnerabilidade_social int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_historico_familiar_transtorno_mental int2 NULL;
ALTER TABLE auditschema.formulario_saude_mental ADD flag_faixa_etaria_menores_18_maiores_60 int2 NULL;


CREATE OR REPLACE PROCEDURE public.update_resumo_movimentacao_estoque_sp()
 LANGUAGE plpgsql
AS $procedure$
DECLARE
    rec              RECORD;
    total_resumos    INTEGER;
    dt_limite        DATE;
    dt_processamento DATE;
    dt_saldo         DATE;
    dt_fechamento    DATE;
BEGIN
    dt_limite := DATE_TRUNC('month', NOW())::DATE;
    SELECT INTO total_resumos COUNT(*) FROM resumo_movimentacao_estoque LIMIT 1;
    IF (total_resumos = 0) THEN
        SELECT INTO dt_processamento DATE_TRUNC('month', min(me.dt_movimentacao))::DATE FROM movimento_estoque me;
    ELSE
        dt_processamento := (dt_limite - interval '1 month')::DATE;
    END IF;
    dt_saldo := (dt_processamento - interval '1 month')::DATE;
    dt_fechamento := (dt_processamento + interval '1 month')::DATE;

    IF (total_resumos > 0) THEN
	   delete FROM resumo_movimentacao_estoque where data_referencia =  dt_fechamento;
	END IF;

    LOOP
        EXIT WHEN dt_processamento >= dt_limite;

        FOR rec in (
            SELECT CASE WHEN b.num_lancto IS NULL THEN a.empresa ELSE b.empresa END,
                   CASE WHEN b.num_lancto IS NULL THEN a.cod_pro ELSE b.cod_pro END,
                   CASE WHEN b.num_lancto IS NULL THEN a.cod_gru ELSE b.cod_gru END,
                   CASE WHEN b.num_lancto IS NULL THEN a.cod_sub ELSE b.cod_sub END,
                   CASE WHEN b.num_lancto IS NULL THEN a.estoque_fisico ELSE b.estoque_fisico END,
                   CASE WHEN b.num_lancto IS NULL THEN a.preco_medio ELSE b.preco_medio END,
                   CASE WHEN b.num_lancto IS NULL THEN a.preco_custo ELSE b.preco_custo END,
                   CASE WHEN b.num_lancto IS NULL THEN a.num_lancto ELSE b.num_lancto END,
                   CASE WHEN b.num_lancto IS NULL THEN a.dt_movimentacao ELSE b.dt_movimentacao END
            FROM (
                     SELECT rme.empresa
                          , rme.cod_pro
                          , rme.cod_gru
                          , rme.cod_sub
                          , rme.estoque_fisico
                          , rme.preco_medio
                          , rme.preco_custo
                          , rme.num_lancto
                          , rme.dt_movimentacao
                     FROM public.resumo_movimentacao_estoque rme
                     WHERE rme.data_referencia = dt_saldo) a
                     FULL OUTER JOIN (
                SELECT me.empresa
                     , me.cod_pro
                     , p.cod_gru
                     , p.cod_sub
                     , me.estoque_fisico
                     , me.preco_medio
                     , me.preco_custo
                     , me.num_lancto
                     , me.dt_movimentacao
                FROM movimento_estoque me
                         JOIN produtos p ON me.cod_pro = p.cod_pro
                         INNER JOIN (
                    SELECT ume.empresa
                         , p2.cod_pro
                         , p2.cod_gru
                         , p2.cod_sub
                         , MAX(ume.num_lancto) AS num_lancto
                      FROM movimento_estoque ume
                      JOIN empresa_material em ON ume.empresa = em.empresa
                      JOIN produtos p2 ON ume.cod_pro = p2.cod_pro
                     WHERE DATE_TRUNC('month', ume.dt_movimentacao)::DATE = dt_processamento
                       AND ume.cod_deposito = em.cod_deposito
                     GROUP BY ume.empresa, p2.cod_pro, p2.cod_gru, p2.cod_sub
                ) x ON me.empresa = x.empresa AND me.num_lancto = x.num_lancto
            ) b ON a.empresa = b.empresa AND a.cod_pro = b.cod_pro AND a.cod_gru = b.cod_gru AND a.cod_sub = b.cod_sub
        )
            LOOP
                INSERT INTO resumo_movimentacao_estoque
                    (cd_resumo_movimentacao_estoque, empresa, cod_pro, cod_gru, cod_sub, data_referencia, estoque_fisico, preco_medio, preco_custo, num_lancto, dt_movimentacao)
                VALUES
                    (nextval('seq_gem'), rec.empresa, rec.cod_pro, rec.cod_gru, rec.cod_sub, dt_fechamento, rec.estoque_fisico, rec.preco_medio, rec.preco_custo, rec.num_lancto, rec.dt_movimentacao);
            END LOOP;

        dt_processamento := (dt_processamento + interval '1 month')::DATE;
        dt_saldo := (dt_processamento - interval '1 month')::DATE;
        dt_fechamento := (dt_processamento + interval '1 month')::DATE;
    END LOOP;
END;
$procedure$
;
