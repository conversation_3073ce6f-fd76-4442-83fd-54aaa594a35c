SET application_name = 'flyway|3.1.79.3';

SET statement_timeout TO 600000;

/*
    Maicon - 14/04/2021
*/
delete from grupo_atendimento_vacinacao_esus;
INSERT INTO grupo_atendimento_vacinacao_esus (cd_grupo_atendimento_vac_esus,descricao,"version") VALUES
	 (4,'Militares',0),
	 (5,'<PERSON>uil<PERSON>olas',0),
	 (6,'População Privada de Liberdade',0),
	 (8,'<PERSON><PERSON><PERSON><PERSON>',0),
	 (9,'Gestantes',0),
	 (10,'Out<PERSON>',0),
	 (11,'<PERSON><PERSON><PERSON><PERSON><PERSON>',0),
	 (12,'Idosos',0),
	 (13,'Funcionários do Sistema Prisional',0),
	 (19,'Doença Hepática Crônica',0),
	 (20,'<PERSON><PERSON>ça Neurológica Crônica',0),
	 (24,'Trans<PERSON><PERSON><PERSON>s',0),
	 (25,'<PERSON>sso<PERSON><PERSON>',0),
	 (26,'<PERSON><PERSON> - <PERSON><PERSON><PERSON> Básico e Superior',0),
	 (31,'Caminhoneiro',0),
	 (32,'Coleti<PERSON> Rodoviário Passageiros Urbano e de Longo Curso',0),
	 (33,'Portuá<PERSON>',0),
	 (34,'An<PERSON> Falci<PERSON><PERSON>',0),
	 (35,'<PERSON><PERSON>cer',0),
	 (36,'<PERSON>abe<PERSON>',0),
	 (37,'<PERSON><PERSON> de <PERSON><PERSON><PERSON>',0),
	 (38,'<PERSON><PERSON><PERSON> <PERSON><PERSON>',0),
	 (39,'<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',0),
	 (40,'<PERSON><PERSON><PERSON><PERSON> <PERSON>',0),
	 (41,'<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',0),
	 (42,'Obesidade Grave',0),
	 (43,'Pacientes de 60 a 64  Anos',0),
	 (44,'Pacientes de 65 a 69 Anos',0),
	 (45,'Pacientes de 70 a 74 Anos',0),
	 (46,'Pacientes de 75 a 79 Anos',0),
	 (47,'Pacientes de 80 anos ou Mais',0),
	 (48,'Idosos Institucionalizados',0),
	 (49,'Marinha',0),
	 (50,'Exército',0),
	 (51,'Força Aérea',0),
	 (52,'Bombeiro Civil',0),
	 (53,'Bombeiro Militar',0),
	 (54,'Guarda  Municipal',0),
	 (55,'Policial Rod. Federal',0),
	 (56,'Policial Civil',0),
	 (57,'Policial Federal',0),
	 (58,'Policial Militar',0),
	 (59,'Ribeirinha',0),
	 (60,'Povos indígenas',0),
	 (61,'Ensino Básico',0),
	 (62,'Ensino Superior',0),
	 (63,'Auxiliar de Veterinário',0),
	 (64,'Biólogo',0),
	 (65,'Biomédico',0),
	 (66,'Cozinheiro e Auxiliares',0),
	 (67,'Cuidador de Idosos',0),
	 (68,'Doula/Parteira',0),
	 (69,'Enfermeiro(a)',0),
	 (70,'Farmacêutico',0),
	 (71,'Fisioterapeutas',0),
	 (72,'Fonoaudiólogo',0),
	 (73,'Funcionário do Sist. funerário',0),
	 (74,'Médico',0),
	 (75,'Med.  Veterinário',0),
	 (76,'Motorista de Ambulância',0),
	 (77,'Nutricionista',0),
	 (78,'Odontologista',0),
	 (79,'Aux. limpeza',0),
	 (80,'Educador Físico',0),
	 (81,'Psicólogo',0),
	 (82,'Recepcionista',0),
	 (83,'Segurança',0),
	 (84,'Assistente Social',0),
	 (85,'Téc. de Enfermagem',0),
	 (86,'Técnico de Veterinário',0),
	 (87,'Terapeuta Ocup.',0),
	 (88,'Prof. Transporte Aéreo Coletivo',0),
	 (89,'Ferroviário',0),
	 (90,'Metroviário',0),
	 (91,'Aquaviário',0),
	 (92,'Pessoas com Deficiência Institucionalizadas',0),
	 (93,'Deficiência Grave',0),
	 (94,'Situação  de  Rua',0),
	 (95,'Trab. industriais',0),
	 (96,'Síndrome de Down',0),
	 (97,'Aux.de Enfermagem',0),
	 (98,'Téc. de Odontologia',0),
	 (99,'Estudante',0);