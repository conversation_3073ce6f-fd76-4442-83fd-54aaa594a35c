SET application_name = 'flyway|3.0.54.5';

/*
    Roger - 13/05/2016 - #12372
*/

ALTER TABLE usuario_cadsus ADD COLUMN referencia varchar(10) NULL;
ALTER TABLE auditschema.usuario_cadsus ADD COLUMN referencia varchar(10) NULL;

drop trigger tg_dom_usuario_cadsus on usuario_cadsus;
DROP TRIGGER tgd_dom_usuario_cadsus ON usuario_cadsus;

DROP FUNCTION ftg_dom_usuario_cadsus();

CREATE OR REPLACE FUNCTION ftg_dom_usuario_cadsus()
  RETURNS trigger AS
$BODY$
DECLARE
   vcns varchar;
   vexiste integer;
   c1 record;
BEGIN
   if TG_OP = 'DELETE' THEN
      delete from dom_usuario_cadsus where cd_usu_cadsus = old.cd_usu_cadsus;
   end if;

   if (TG_OP = 'INSERT' or TG_OP = 'UPDATE') then
      vcns = '';
      for c1 in SELECT cd_numero_cartao FROM usuario_cadsus_cns
         WHERE st_excluido = 0 and cd_usu_cadsus = new.cd_usu_cadsus
         ORDER BY cd_numero_cartao loop
         vcns = vcns || c1.cd_numero_cartao || ' ';
      end loop;

      -- Avalia se ja existe o dominio cadastrado
      SELECT count(*)
        INTO vexiste
        FROM dom_usuario_cadsus
       WHERE cd_usu_cadsus = new.cd_usu_cadsus;

      if vexiste = 0 then
         insert into dom_usuario_cadsus (cd_dominio, cd_usu_cadsus, key_word, nome, nome_mae, rg, cns, cpf, situacao, dt_nascimento)
          values (
            nextval('seq_dominio_consulta'),
            new.cd_usu_cadsus,
            sem_acento(TRIM(UPPER(coalesce(new.nm_usuario, '')))) ||' '|| coalesce(new.rg,'') || ' ' || coalesce(new.cpf, '') || ' ' || coalesce(vcns, '') || ' ' || new.cd_usu_cadsus || ' ' || coalesce(to_char(new.dt_nascimento, 'DD/MM/YYYY'), '') || ' ' || coalesce(new.referencia, ''),
            TRIM(UPPER(new.nm_usuario)),
            sem_acento(TRIM(UPPER(new.nm_mae))),
            new.rg,
            (SELECT min(cd_numero_cartao) FROM usuario_cadsus_cns where st_excluido = 0 and cd_usu_cadsus = new.cd_usu_cadsus),
            new.cpf,
            new.situacao,
            new.dt_nascimento
          );
      else
         update dom_usuario_cadsus set
                key_word = sem_acento(TRIM(UPPER(coalesce(new.nm_usuario, '')))) || ' ' || coalesce(new.rg,'') || ' ' || coalesce(new.cpf, '') || ' ' || coalesce(vcns, '') || ' ' || new.cd_usu_cadsus || ' ' || coalesce(to_char(new.dt_nascimento, 'DD/MM/YYYY'), '') || ' ' || coalesce(new.referencia, ''),
                nome = TRIM(UPPER(new.nm_usuario)),
                nome_mae = sem_acento(TRIM(UPPER(new.nm_mae))),
                rg = new.rg,
                cns = (SELECT min(cd_numero_cartao) FROM usuario_cadsus_cns where st_excluido = 0 and cd_usu_cadsus = new.cd_usu_cadsus),
                cpf = new.cpf,
                situacao = new.situacao,
                dt_nascimento = new.dt_nascimento
         where
            cd_usu_cadsus = new.cd_usu_cadsus;
      end if;
      return new;
   end if;
   return old;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;

CREATE TRIGGER tg_dom_usuario_cadsus
  AFTER INSERT OR UPDATE
  ON usuario_cadsus
  FOR EACH ROW
  EXECUTE PROCEDURE ftg_dom_usuario_cadsus();

CREATE TRIGGER tgd_dom_usuario_cadsus
  BEFORE DELETE
  ON usuario_cadsus
  FOR EACH ROW
  EXECUTE PROCEDURE ftg_dom_usuario_cadsus();

DROP TRIGGER tg_version_all ON usuario_cadsus;

update usuario_cadsus set referencia = cd_usu_cadsus;

CREATE TRIGGER tg_version_all
  BEFORE INSERT OR UPDATE
  ON usuario_cadsus
  FOR EACH ROW
  EXECUTE PROCEDURE ftg_version_all();

/*
    Roger - 13/05/2016 - #12387
*/

create unique index idx_referencia_cadsus on usuario_cadsus(referencia);