SET application_name = 'flyway|3.1.212';

SET statement_timeout TO 600000;

--<PERSON><PERSON><PERSON> - feature/REG-1584 - 03/10/2023

INSERT INTO programa_pagina VALUES (2047, 'br.com.celk.view.controle.importacaoFpo.ImportacaoFpoPage', 'N');
INSERT INTO programa_web VALUES (1136, 'Importação FPO', 2047, 'N');
INSERT INTO programa_web_pagina VALUES (2134, 1136, 2047);
INSERT INTO menu_web VALUES (1000154, 'Importação FPO', 'importacaoFpo', 396, 1136, 0, 0, 0);

create table if not exists public.arquivo_fpo (
    cd_arquivo_fpo					int8 primary key not null,
    nome_arquivo					varchar(200),
    competencia						date,
    cd_usuario						int8 references public.usuarios(cd_usuario),
    dt_geracao						TIMESTAMP,
    situacao						int8,
    cd_process                      int8 references public.async_process(cd_process),
    cd_gerenciador_arquivo			int8 references public.gerenciador_arquivo(cd_gerenciador_arquivo),
    logs							text,
    version             			int8 not null
);

CREATE TABLE if not exists auditschema.arquivo_fpo AS SELECT t2.*, t1.* FROM arquivo_fpo t1, audit_temp t2 WHERE 1=2;
create sequence if not exists seq_fpo start with 1;
create sequence if not exists seq_audit_id_arquivo_fpo;
alter table auditschema.arquivo_fpo add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON arquivo_fpo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
