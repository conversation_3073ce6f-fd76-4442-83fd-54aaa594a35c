SET application_name = 'flyway|********';

INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1178,'Endereço','endereco',1,null,null,0,0);

INSERT INTO programa_pagina VALUES (1669, 'br.com.celk.view.basico.distrito.ConsultaDistritoPage', 'N');
INSERT INTO programa_pagina VALUES (1670, 'br.com.celk.view.basico.distrito.CadastroDistritoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (929, 'Cadastro de Distrito', 1669, 'N');
INSERT INTO programa_web_pagina VALUES (1783, 929, 1669);
INSERT INTO programa_web_pagina VALUES (1784, 929, 1670);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1179,'Distrito','distrito',1178,929,0,0,0);

INSERT INTO programa_pagina VALUES (1674,	'br.com.celk.view.basico.enderecoestruturado.CadastroEnderecoEstruturado', 'N');
INSERT INTO programa_pagina VALUES (1673,	'br.com.celk.view.basico.enderecoestruturado.ConsultaEnderecoEstruturado', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (931, 'Endereço Estruturado', 1673, 'N');
INSERT INTO programa_web_pagina VALUES (1787, 931, 1673);
INSERT INTO programa_web_pagina VALUES (1788, 931, 1674);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1181,'Endereço Estruturado','enderecoEstruturado',1178,931,0,0,0);

INSERT INTO programa_pagina VALUES (1672,	'br.com.celk.view.basico.logradouro.CadastroLogradouroPage', 'N');
INSERT INTO programa_pagina VALUES (1671,	'br.com.celk.view.basico.logradouro.ConsultaLogradouroPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (930, 'Cadastro de Logradouro', 1671, 'N');
INSERT INTO programa_web_pagina VALUES (1785, 930, 1671);
INSERT INTO programa_web_pagina VALUES (1786, 930, 1672);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1180,'Logradouro','logradouro',1178,930,0,0,0);

update menu_web set cd_menu_pai = 1178 where cd_menu = 127;

alter table endereco_estruturado alter column cep set not null;
alter table endereco_estruturado alter column cd_eqp_micro_area drop not null;

alter table endereco_estruturado alter column cd_endereco_estruturado type int8;
alter table endereco_estruturado alter column cd_eqp_micro_area type int8;
alter table endereco_estruturado alter column cd_bairro type int8;
alter table endereco_estruturado alter column cd_end_estruturado_distrito type int8;
alter table endereco_estruturado alter column cd_end_estruturado_logradouro type int8;
alter table endereco_estruturado alter column cd_usuario_alteracao type int8;

alter table endereco_usuario_cadsus drop column IF EXISTS cd_endereco_estruturado;
alter table auditschema.endereco_usuario_cadsus drop column IF EXISTS cd_endereco_estruturado;
alter table endereco_usuario_cadsus add column cd_endereco_estruturado int8 null;
alter table auditschema.endereco_usuario_cadsus add column cd_endereco_estruturado int8 null;
alter table endereco_usuario_cadsus add CONSTRAINT fk_endereco_usuario_cadsus_ref_endereco_estruturado    FOREIGN KEY (cd_endereco_estruturado)    REFERENCES endereco_estruturado (cd_endereco_estruturado)    ON DELETE NO ACTION    ON UPDATE NO action;