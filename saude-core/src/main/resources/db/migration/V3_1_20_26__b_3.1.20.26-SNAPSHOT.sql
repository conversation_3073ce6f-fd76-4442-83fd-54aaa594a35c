SET application_name = 'flyway|3.1.20.26';

/*
    Sulivan - #25606 - 05/07/2019
*/

ALTER TABLE solicitacao_agendamento ADD COLUMN vl_subclassificacao_fila_espera int2 null;
ALTER TABLE auditschema.solicitacao_agendamento ADD COLUMN vl_subclassificacao_fila_espera int2 null;

ALTER TABLE solicitacao_agendamento ADD COLUMN ds_subclassificacao_fila_espera varchar(30) null;
ALTER TABLE auditschema.solicitacao_agendamento ADD COLUMN ds_subclassificacao_fila_espera varchar(30) null;

create table subclassificacao_fila_espera (
    cd_subclassificacao_fila_espera      int8            not null,
    cd_classificacao_risco               int8            not null,
    descricao                            varchar(30)     not null,
    valor                                int2            not null,
    cd_usuario                           numeric(6)      not null,
    dt_cadastro                          timestamp       not null,
    version                              int8            not null
);

alter table subclassificacao_fila_espera
   add constraint PK_SUBCLASS_FIL_ESP primary key (cd_subclassificacao_fila_espera);

alter table subclassificacao_fila_espera
    add constraint FK_SUBCLASS_FIL_ESP_REF_CLASS_RISCO foreign key (cd_classificacao_risco)
    references classificacao_risco (cd_classificacao_risco) on delete restrict on update restrict;

alter table subclassificacao_fila_espera
   add constraint FK_SUBCLASS_FIL_ESP_REF_USU foreign key (cd_usuario)
   references usuarios (cd_usuario)
   on delete restrict on update restrict;

CREATE TABLE auditschema.subclassificacao_fila_espera AS SELECT t2.*, t1.* FROM subclassificacao_fila_espera t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_subclassificacao_fila_espera;alter table auditschema.subclassificacao_fila_espera add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON subclassificacao_fila_espera FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

INSERT INTO programa_pagina VALUES (1713, 'br.com.celk.view.unidadesaude.subclassificacaofilaespera.CadastroSubClassificacaoFilaEsperaPage', 'N');
INSERT INTO programa_web_pagina VALUES (1827, 422, 1713);