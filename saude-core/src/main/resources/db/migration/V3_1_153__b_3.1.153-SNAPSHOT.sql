SET application_name = 'flyway|3.1.153';

SET statement_timeout TO 600000;

/*
*					<PERSON><PERSON><PERSON> Neves - feature/REG-1322 - 05/10/2022
*/

ALTER TABLE ultima_alteracao_agendamento ADD COLUMN cd_tp_procedimento INT8;
ALTER TABLE auditschema.ultima_alteracao_agendamento ADD COLUMN cd_tp_procedimento INT8;

CREATE OR REPLACE FUNCTION public.update_ultima_alteracao_agendamento()
RETURNS trigger
LANGUAGE plpgsql
AS $function$
BEGIN
    IF (SELECT pg.valor
         FROM public.parametro_gem pg
         WHERE pg.parametro = 'utilizaResetManualFilaEspera') = '0'
         THEN
            UPDATE ultima_alteracao_agendamento SET dt_referencia = NOW() WHERE cd_tp_procedimento is null;

            IF (TG_OP = 'DELETE') THEN RETURN OLD;
            ELSIF (TG_OP IN ('UPDATE', 'INSERT')) THEN RETURN NEW;
            END IF;
    END IF;

   RETURN NULL;
END;
$function$
;

/*
 * Maicon- VSA-1944
 */

alter table endereco_coordenadas alter column latitude type numeric(8,6) USING latitude::numeric;
alter table auditschema.endereco_coordenadas alter column latitude type numeric(8,6) USING latitude::numeric;

alter table endereco_coordenadas alter column longitude type numeric(8,6) USING latitude::numeric;
alter table auditschema.endereco_coordenadas alter column longitude type numeric(8,6) USING latitude::numeric;