SET application_name = 'flyway|3.1.20.24';

/*
    Silvio - 20/06/2019 #26243
*/
alter table esus_ficha_atendimento_domiciliar_item add column cd_ciap INT8;
alter table auditschema.esus_ficha_atendimento_domiciliar_item add column cd_ciap INT8;

alter table esus_ficha_atendimento_domiciliar_item
   add constraint FK_ATEND_IND_CIAP_REF_CIAP foreign key (cd_ciap)
      references ciap (cd_ciap)
      on delete restrict on update restrict;

UPDATE esus_ficha_atendimento_domiciliar_item set cd_ciap = (SELECT cd_ciap FROM ciap WHERE UPPER(referencia) = UPPER(esus_ficha_atendimento_domiciliar_item.ciap)) WHERE ciap is not null;



/*
    Claudio - 19/06/2019 #26233
*/
CREATE OR REPLACE FUNCTION ftg_dom_produto_ee()
  RETURNS trigger AS
$BODY$
DECLARE
  vregistro varchar;
  vexiste integer;
  vdescricao produtos.descricao%TYPE;
  vcod_gru produtos.cod_gru%TYPE;
  vcod_sub produtos.cod_sub%TYPE;
  vreferencia produtos.referencia%TYPE;
  vflag_ativo varchar;
BEGIN
   if (TG_OP = 'UPDATE') then

	SELECT situacao INTO vflag_ativo
	FROM dom_produto WHERE cod_pro = new.cod_pro AND empresa = new.empresa LIMIT 1;

	if vflag_ativo is null then
		SELECT descricao, cod_gru, cod_sub, referencia INTO vdescricao, vcod_gru, vcod_sub, vreferencia
	     FROM produtos
	     WHERE cod_pro = new.cod_pro;

		insert into dom_produto (cd_dominio, cod_pro, keyword, nome, situacao, empresa, cod_gru, cod_sub)
		values (
		   nextval('seq_dominio_consulta'),
		   new.cod_pro,
		   sem_acento(TRIM(UPPER(coalesce(vdescricao, '')))) ||' '|| UPPER(TRIM(coalesce(vreferencia,''))),
		   sem_acento(TRIM(UPPER(vdescricao))),
		   UPPER(new.flag_ativo),
		   new.empresa,
		   vcod_gru,
		   vcod_sub
		);
	else
		if vflag_ativo <> new.flag_ativo then
			SELECT descricao, cod_gru, cod_sub, referencia INTO vdescricao, vcod_gru, vcod_sub, vreferencia
		     FROM produtos
		     WHERE cod_pro = new.cod_pro;

			update dom_produto set
				keyword = sem_acento(TRIM(UPPER(coalesce(vdescricao, '')))) ||' '|| UPPER(TRIM(coalesce(vreferencia,''))),
				nome = sem_acento(TRIM(UPPER(vdescricao))),
				situacao = UPPER(new.flag_ativo),
				empresa = new.empresa,
				cod_gru = vcod_gru,
				cod_sub = vcod_sub
			where cod_pro = new.cod_pro
			and empresa = new.empresa;
		end if;
	end if;
   end if;
   if (TG_OP = 'INSERT') then
	    SELECT count(*) INTO vexiste
         FROM (SELECT 1 FROM dom_produto WHERE cod_pro = new.cod_pro AND empresa = new.empresa LIMIT 1) AS t;

         SELECT descricao, cod_gru, cod_sub, referencia
           INTO vdescricao, vcod_gru, vcod_sub, vreferencia
           FROM produtos
          WHERE cod_pro = new.cod_pro;

         if vexiste = 0 then
            insert into dom_produto (cd_dominio, cod_pro, keyword, nome, situacao, empresa, cod_gru, cod_sub)
               values (
                  nextval('seq_dominio_consulta'),
                  new.cod_pro,
                  sem_acento(TRIM(UPPER(coalesce(vdescricao, '')))) ||' '|| UPPER(TRIM(coalesce(vreferencia,''))),
                  sem_acento(TRIM(UPPER(vdescricao))),
                  UPPER(new.flag_ativo),
                  new.empresa,
                  vcod_gru,
                  vcod_sub
               );
         else
            update dom_produto set
               keyword = sem_acento(TRIM(UPPER(coalesce(vdescricao, '')))) ||' '|| UPPER(TRIM(coalesce(vreferencia,''))),
               nome = sem_acento(TRIM(UPPER(vdescricao))),
               situacao = UPPER(new.flag_ativo),
               empresa = new.empresa,
               cod_gru = vcod_gru,
               cod_sub = vcod_sub
            where cod_pro = new.cod_pro
              AND empresa = new.empresa;
         end if;
      return new;
   end if;
   return new;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;

DROP TRIGGER tg_estoque_empresa ON estoque_empresa;
CREATE TRIGGER tg_estoque_empresa
  AFTER INSERT OR UPDATE
  ON estoque_empresa
  FOR EACH ROW
  EXECUTE PROCEDURE ftg_dom_produto_ee();