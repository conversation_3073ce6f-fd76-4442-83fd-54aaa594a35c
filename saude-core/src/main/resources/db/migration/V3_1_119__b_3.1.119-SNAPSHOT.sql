SET application_name = 'flyway|3.1.119';

SET statement_timeout TO 600000;

/*
 CLAUDIO - 01/02/2022 - AMB-2097
*/
alter table laudo_medicamentos_especiais add empresa int8;
alter table auditschema.laudo_medicamentos_especiais add empresa int8;

alter table laudo_medicamentos_especiais
   add constraint FK_LME_REF_EMPRESA foreign key (empresa)
      references empresa (empresa);

alter table laudo_medicamentos_especiais alter column anamnese drop not null;

alter table laudo_medicamentos_especiais add cd_profissional int8;
alter table auditschema.laudo_medicamentos_especiais add cd_profissional int8;

alter table laudo_medicamentos_especiais
   add constraint FK_LME_REF_PROFISSIONAL foreign key (cd_profissional)
      references profissional (cd_profissional);

alter table laudo_medicamentos_especiais add cd_usu_cadastro numeric(6);
alter table auditschema.laudo_medicamentos_especiais add cd_usu_cadastro numeric(6);

alter table laudo_medicamentos_especiais
   add constraint FK_LME_REF_USUARIO_CADASTRO foreign key (cd_usu_cadastro)
      references usuarios (cd_usuario);

with itens as (
	select t2.empresa, t2.cd_profissional, t1.cd_laudo_medicamentos_especiais from laudo_medicamentos_especiais t1, atendimento t2 where t1.nr_atendimento = t2.nr_atendimento
)
update laudo_medicamentos_especiais set empresa = itens.empresa, cd_profissional = itens.cd_profissional, cd_usu_cadastro = cd_usuario
from itens
where laudo_medicamentos_especiais.cd_laudo_medicamentos_especiais = itens.cd_laudo_medicamentos_especiais;


/*
    Luis Seidel - 28/01 : VSA-1806
*/

--requerimento_projeto_hidro
ALTER TABLE public.requerimento_projeto_hidro ADD edificacao_unifamiliar int2 NULL;
ALTER TABLE public.requerimento_projeto_hidro ADD processo_baixo_risco_decreto int2 NULL;
ALTER TABLE public.requerimento_projeto_hidro ADD projeto_licenciavel_ambiental int2 NULL;
ALTER TABLE public.requerimento_projeto_hidro ADD termo_aceite int2 NULL;

--AUDITSCHEMA
ALTER TABLE AUDITSCHEMA.requerimento_projeto_hidro ADD edificacao_unifamiliar int2 NULL;
ALTER TABLE AUDITSCHEMA.requerimento_projeto_hidro ADD processo_baixo_risco_decreto int2 NULL;
ALTER TABLE AUDITSCHEMA.requerimento_projeto_hidro ADD projeto_licenciavel_ambiental int2 NULL;
ALTER TABLE AUDITSCHEMA.requerimento_projeto_hidro ADD termo_aceite int2 NULL;
