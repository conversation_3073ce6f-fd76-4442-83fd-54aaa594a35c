SET application_name = 'flyway|3.1.26.1';

SET statement_timeout TO 600000;

/*
    Adriano - CSC-41 - 19/03/2020
*/
CREATE INDEX ON resultado_exame_requisicao_rtf (cd_exame_requisicao);

/*==============================================================*/
/*   ALTER TABLE FORMULARIO_TRIAGE_COVID19                      */
/*   autor: <PERSON>                                    */
/*   tarefa: AMB-640                                            */
/*==============================================================*/
ALTER TABLE formulario_triagem_covid19 ADD COLUMN resultado_pontuacao int8 null;

/*==============================================================*/
/*   ALTER TABLE AUDITSCHEMA.FORMULARIO_TRIAGE_COVID19          */
/*   autor: Eduardo Ximendes                                    */
/*   tarefa: AMB-640                                            */
/*==============================================================*/
ALTER TABLE auditschema.formulario_triagem_covid19 ADD COLUMN resultado_pontuacao int8 null;

/*==============================================================*/
/*   ALTER TABLE SINTOMAS COVID19                               */
/*   autor: Eduardo Ximendes                                    */
/*   tarefa: AMB-640                                            */
/*==============================================================*/
ALTER TABLE sintomas_covid19 ADD COLUMN pontuacao int8 null;
ALTER TABLE sintomas_covid19 ADD COLUMN version   int8 null;

/*==============================================================*/
/*   ALTER TABLE AUDITSCHEMA.SINTOMAS_COVID19                   */
/*   autor: Eduardo Ximendes                                    */
/*   tarefa: AMB-640                                            */
/*==============================================================*/
ALTER TABLE auditschema.sintomas_covid19 ADD COLUMN pontuacao int8 null;
ALTER TABLE auditschema.sintomas_covid19 ADD COLUMN version   int8 null;

/*==============================================================*/
/*   UPDATE TABLE SINTOMAS COVID19  COLUMN VERSION              */
/*   autor: Eduardo Ximendes                                    */
/*   tarefa: AMB-640                                            */
/*==============================================================*/
update sintomas_covid19 set version = 0 where version is null;

/*==============================================================*/
/*   CRIAR MENU CADASTRO SINTOMAS COVID-19                      */
/*   autor: Eduardo Ximendes                                    */
/*   tarefa: AMB-640                                            */
/*==============================================================*/
INSERT INTO programa_pagina VALUES (1797, 'br.com.celk.view.unidadesaude.sintomasCovid.ConsultaSintomasCovid19Page', 'N');
INSERT INTO programa_pagina VALUES (1798, 'br.com.celk.view.unidadesaude.sintomasCovid.CadastroSintomasCovid19Page', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (990, 'Cadastro dos Sintomas Covid-19', 1797, 'N');
INSERT INTO programa_web_pagina VALUES (1898, 990, 1797);
INSERT INTO programa_web_pagina VALUES (1899, 990, 1798);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version,layout_menu) VALUES (1242,'Cadastro dos Sintomas Covid-19','consultaSintomasCovid',324,990,143,0,0);

/*
    Elton - GMT-222
*/

alter table tipo_receita add column flag_atend_port_ses_208_2020 int2;
alter table auditschema.tipo_receita add column flag_atend_port_ses_208_2020 int2;

/*==============================================================*/
/*   TABLE: IMPORTACAO_EXAME                                    */
/*   autor: Eduardo Ximendes                                    */
/*   tarefa: AMB-648                                            */
/*==============================================================*/
CREATE TABLE importacao_exame (
    cd_importacao_exame                INT8            not null,
    requisicao                         INT8            null,
    nome_paciente                      varchar         null,
    cns                                INT8            null,
    municipio_residencia               varchar         null,
    uf_residencia                      varchar         null,
    requisitante                       varchar         null,
    municipio_requisitante             varchar         null,
    descricao_exame                    varchar         null,
    metodo                             varchar         null,
    material                           varchar         null,
    amostra                            smallint        null,
    restricao                          varchar         null,
    laboratorio_cadastro               varchar         null,
    laboratorio_executor               varchar         null,
    dt_cadastro                        TIMESTAMP       null,
    dt_recebimento                     TIMESTAMP       null,
    dt_liberacao                       TIMESTAMP       null,
    status_exame                       varchar         null,
    resultado                          varchar         null,
    version                            INT8            null
);


ALTER TABLE importacao_exame
   ADD CONSTRAINT PK_IMPORTACAO_EXAME PRIMARY KEY (cd_importacao_exame);


/*==============================================================*/
/*   TABLE: AUDITORIA IMPORTACAO_EXAME                          */
/*   autor: Eduardo Ximendes                                    */
/*   tarefa: AMB-648                                            */
/*==============================================================*/
CREATE TABLE AUDITSCHEMA.importacao_exame AS SELECT T2.*, T1.* FROM importacao_exame T1, AUDIT_TEMP T2 WHERE 1 = 2;
CREATE sequence SEQ_AUDIT_ID_IMPORTACAO_EXAME;
ALTER TABLE AUDITSCHEMA.importacao_exame ADD PRIMARY KEY(AUDIT_ID);
CREATE trigger EMP_AUDIT after INSERT OR UPDATE OR DELETE ON importacao_exame FOR each row execute procedure PROCESS_EMP_AUDIT();


/*
    Rafael Santos - AMB-648 - 08/04/2020
*/

INSERT INTO programa_pagina VALUES (1799, 'br.com.celk.view.unidadesaude.exames.relatorio.relatorioErrosImportacaoExameExterno.RelatorioErrosImportacaoExamesExternosPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (991, 'Relatório Erros Importação Exames', 1799, 'N');
INSERT INTO programa_web_pagina VALUES (1900, 991, 1799);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1243,'Relatório Erros Importação Exames','relatorioErrosImportacaoExames',243,991,143,0,0);


/*==============================================================*/
/*   TABLE: IMPORTACAO_EXAME_LOG_ERRO                           */
/*   autor: Rafael Santos                                       */
/*   tarefa: AMB-648                                            */
/*==============================================================*/
CREATE TABLE importacao_exame_log_erro (
    cd_importacao_exame_log_erro       INT8            not null,
    cd_importacao_exame                INT8            not null,
    descricao                          varchar         not null,
    version                            INT8            null
);

ALTER TABLE importacao_exame_log_erro
   ADD CONSTRAINT PK_IMPORTACAO_EXAME_LOG_ERRO PRIMARY KEY (cd_importacao_exame_log_erro);

ALTER TABLE importacao_exame_log_erro
ADD CONSTRAINT FK_CD_IMPORTACAO_EXAME FOREIGN KEY (cd_importacao_exame)
  REFERENCES importacao_exame (cd_importacao_exame)
  ON DELETE RESTRICT ON UPDATE RESTRICT;

/*==============================================================*/
/*   TABLE: AUDITORIA IMPORTACAO_EXAME_LOG_ERRO                 */
/*   autor: Rafael Santos                                       */
/*   tarefa: AMB-648                                            */
/*==============================================================*/
CREATE TABLE AUDITSCHEMA.importacao_exame_log_erro AS SELECT T2.*, T1.* FROM importacao_exame_log_erro T1, AUDIT_TEMP T2 WHERE 1 = 2;
CREATE sequence SEQ_AUDIT_ID_IMPORTACAO_EXAME_LOG_ERRO;
ALTER TABLE AUDITSCHEMA.importacao_exame_log_erro ADD PRIMARY KEY(AUDIT_ID);
CREATE trigger EMP_AUDIT after INSERT OR UPDATE OR DELETE ON importacao_exame_log_erro FOR each row execute procedure PROCESS_EMP_AUDIT();

/*
    Adriano Rodrigues - AMB-648 - 08/04/2020
*/


INSERT INTO
	programa_pagina
values
	(1800,'br.com.celk.view.importacaoExame.ImportacaoExamePage','N',0);
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (992, 'Importação de Exames', 1800, 'N');
INSERT INTO programa_web_pagina VALUES (1901, 992, 1800);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1244,'Importação de Exames','importacaoExames',215,992,143,0,0);

/*
    Rafael Santos - AMB-648 - 09/04/2020
*/

alter table importacao_exame add column dt_importacao TIMESTAMP;
alter table auditschema.importacao_exame add column dt_importacao TIMESTAMP;
