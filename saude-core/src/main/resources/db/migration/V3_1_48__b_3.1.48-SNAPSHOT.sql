SET application_name = 'flyway|3.1.48';

SET statement_timeout TO 600000;

/*
    Marcus - 15/08/2020 - VSA-1012
*/
CREATE TABLE ficha_nascido_vivo (
	cd_ficha_nascido_vivo int8 NOT NULL,
	cd_usuario_integracao int8 NOT NULL,
	cd_usu_cadsus_mae int8 NOT NULL,
	cd_usu_cadsus_nascido int8 NOT NULL,
	uuid_tablet varchar NULL,
	dt_cadastro timestamp NOT NULL,
	local_parto int2 NOT NULL,
	tp_parto int2 NOT NULL,
	duracao_gestacao int2 NOT NULL,
	qtd_consultas_prenatal int2 NULL,
	tipo_gravidez int2 NULL,
	num_declaracao_nascido varchar(11) NULL,
	prematuro int2 NULL,
	"version" int8 NOT NULL,
	version_all int8 NOT NULL
);

ALTER TABLE public.ficha_nascido_vivo ADD CONSTRAINT ficha_nascido_vivo_pk PRIMARY KEY (cd_ficha_nascido_vivo);
ALTER TABLE ficha_nascido_vivo ADD CONSTRAINT ficha_nascido_vivo_fk_1 FOREIGN KEY (cd_usu_cadsus_mae) REFERENCES usuario_cadsus(cd_usu_cadsus);
ALTER TABLE ficha_nascido_vivo ADD CONSTRAINT ficha_nascido_vivo_fk_2 FOREIGN KEY (cd_usu_cadsus_nascido) REFERENCES usuario_cadsus(cd_usu_cadsus);
ALTER TABLE ficha_nascido_vivo ADD CONSTRAINT ficha_nascido_vivo_fk_3 FOREIGN KEY (cd_usuario_integracao) REFERENCES usuarios(cd_usuario);

CREATE SEQUENCE seq_ficha_nascido_vivo;

CREATE SEQUENCE seq_version_ficha_nascido_vivo;
UPDATE ficha_nascido_vivo set version_all = 1;
CREATE TRIGGER tg_version_all
    BEFORE INSERT OR UPDATE
    ON ficha_nascido_vivo
    FOR EACH ROW
    EXECUTE PROCEDURE ftg_version_all();

CREATE TABLE auditschema.ficha_nascido_vivo AS SELECT t2.*, t1.* FROM ficha_nascido_vivo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_ficha_nascido_vivo;alter table auditschema.ficha_nascido_vivo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ficha_nascido_vivo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

