SET application_name = 'flyway|3.1.75.4';

SET statement_timeout TO 600000;

/*VAC-222  Cristian 2021-03-17*/

ALTER TABLE auditschema.produtos ADD column fabricante_esus varchar(150);
ALTER TABLE public.produtos ADD column fabricante_esus varchar(150);

DO $vac222$
DECLARE
 	prodvac RECORD;
 	num_vacinas INTEGER = 0;
/*calula tempo execucao*/
	StartTime timestamptz;
	EndTime timestamptz;
	FinishTime numeric;
BEGIN
	StartTime := clock_timestamp();
	raise notice '/************************************************************************************/';
	raise notice '/***********						INICIANDO SCRIPT						*********/';
	raise notice '/***********			VAC222 - ATUALIZA FABRICANTE E-SUS VACINA			*********/';
	raise notice '/************************************************************************************/';
	FOR prodvac IN(	select fm.ds_fabricante,p.cod_pro, p.descricao , tv.tipo_esus
					from produtos p
					join fabricante_medicamento fm on p.cd_fabricante = fm.cd_fabricante
					join produto_vacina pv on p.cod_pro = pv.cod_pro
					join tipo_vacina tv on pv.cd_vacina = tv.cd_vacina)
	loop
		if (prodvac.tipo_esus <> 85 and prodvac.tipo_esus <> 86 and prodvac.tipo_esus <> 87 and prodvac.tipo_esus <> 88) then

--			raise notice 'update produtos set fabricante_esus = ''%'' where cod_pro = ''%'';',prodvac.ds_fabricante, prodvac.cod_pro;
			update produtos set fabricante_esus = prodvac.ds_fabricante where cod_pro = prodvac.cod_pro;
			raise notice 'PRODUTO % - % ,TIPO_ESUS % ,FABRICANTE_ESUS ATUALIZADO PARA ''%'';',prodvac.cod_pro, prodvac.descricao, prodvac.tipo_esus,prodvac.ds_fabricante;

		elseif (prodvac.tipo_esus = 85) then

--			raise notice 'update produtos set fabricante_esus = ''%'' where cod_pro = ''%'';','OXFORD-ASTRAZENECA', prodvac.cod_pro;
			update produtos set fabricante_esus = 'OXFORD-ASTRAZENECA' where cod_pro = prodvac.cod_pro ;
			raise notice 'PRODUTO % - % ,TIPO_ESUS % ,FABRICANTE_ESUS ATUALIZADO PARA: ''%'';',prodvac.cod_pro, prodvac.descricao, prodvac.tipo_esus,'OXFORD-ASTRAZENECA';

		elseif (prodvac.tipo_esus = 86) then

--			raise notice 'update produtos set fabricante_esus = ''%'' where cod_pro = ''%'';','SINOVAC/BUTANTAN', prodvac.cod_pro;
			update produtos set fabricante_esus = 'SINOVAC/BUTANTAN' where cod_pro = prodvac.cod_pro ;
			raise notice 'PRODUTO % - % ,TIPO_ESUS % ,FABRICANTE_ESUS ATUALIZADO PARA ''%'';',prodvac.cod_pro, prodvac.descricao, prodvac.tipo_esus,'SINOVAC/BUTANTAN';

		elseif (prodvac.tipo_esus = 87) then

--			raise notice 'update produtos set fabricante_esus = ''%'' where cod_pro = ''%'';','PFIZER/BIONTECH', prodvac.cod_pro;
			update produtos set fabricante_esus = 'PFIZER/BIONTECH' where cod_pro = prodvac.cod_pro ;
			raise notice 'PRODUTO % - % ,TIPO_ESUS % ,FABRICANTE_ESUS ATUALIZADO PARA ''%'';',prodvac.cod_pro, prodvac.descricao, prodvac.tipo_esus,'PFIZER/BIONTECH';

		elseif (prodvac.tipo_esus = 88) then

--			raise notice 'update produtos set fabricante_esus = ''%'' where cod_pro = ''%'';','JANSSEN-CILAG', prodvac.cod_pro;
			update produtos set fabricante_esus = 'JANSSEN-CILAG' where cod_pro = prodvac.cod_pro;
			raise notice 'PRODUTO % - % ,TIPO_ESUS % ,FABRICANTE_ESUS ATUALIZADO PARA ''%'';',prodvac.cod_pro, prodvac.descricao, prodvac.tipo_esus,'JANSSEN-CILAG';

		end if;
		num_vacinas = num_vacinas+1;
	END LOOP;

	EndTime := clock_timestamp();
	FinishTime := 1000 * ( extract(epoch FROM EndTime) - extract(epoch FROM StartTime) );
	raise notice '/************************************************************************************/';
	raise notice '/***********						SCRIPT FINALIZADO						*********/';
	raise notice '/************************************************************************************/';
	RAISE notice 'Registros Atualizados: %;', num_vacinas;
	RAISE NOTICE 'Duração: % ms.', round(FinishTime,3);
	RAISE NOTICE 'Duração: % s.', round(FinishTime/1000,2);
	RAISE NOTICE 'Duração: % min.', round(FinishTime/1000/60,2);
END $vac222$;
