SET application_name = 'flyway|3.1.126';

SET statement_timeout TO 600000;

/*
    Seidel - 16/03/2022 - VSA-72
*/

INSERT INTO public.ficha_investigacao_agravo(
    cd_ficha_investigacao_agravo, ordem, dt_cadastro, descricao, status, "version")
VALUES (22, 1, NOW(), 'COQUELUCHE', 1, 0);

CREATE TABLE public.investigacao_agr_coqueluche(
    cd_invest_agr_coqueluche int8 PRIMARY KEY NOT NULL,
    "version" int8 NOT NULL,
    flag_informacoes_complementares varchar(1) NOT NULL DEFAULT 'S'::character varying,
    cd_registro_agravo int8 NOT NULL REFERENCES public.registro_agravo(cd_registro_agravo),

    --investigacao
    dt_investigacao date NULL,
    ocupacao_cbo varchar(10) NULL REFERENCES public.tabela_cbo(cd_cbo),

    --antecedentes epidemiologicos
    unidade_notificante_sentinela int2 NULL,
    contato_caso_suspeito int2 NULL,
    contato_caso_suspeito_outros VARCHAR(50) NULL,
    contato_nome VARCHAR(50) NULL,
    contato_endereco VARCHAR(200) NULL,
    nro_doses_vacina_triplice int2 NULL,
    dt_ultima_dose DATE NULL,


    --dados clinicos
    dt_inicio_tosse DATE NULL,
    sinais_sintomas_tosse int2 NULL,
    sinais_sintomas_tosse_paroxistica int2 NULL,
    sinais_sintomas_respiracao_ruidosa int2 NULL,
    sinais_sintomas_cianose int2 NULL,
    sinais_sintomas_vomitos int2 NULL,
    sinais_sintomas_apneia int2 NULL,
    sinais_sintomas_temperatura_abaixo int2 NULL,
    sinais_sintomas_temperatura_acima int2 NULL,
    sinais_sintomas_outros VARCHAR(50) NULL,

    complicacoes_pneumonia int2 NULL,
    complicacoes_desidratacao int2 NULL,
    complicacoes_desnutricao int2 NULL,
    complicacoes_encefalopatia int2 NULL,
    complicacoes_otite int2 NULL,
    complicacoes_outros VARCHAR(50) NULL,

    --hospitalização
    hospitalizacao int2 NULL,
    dt_internacao date null,
    unidade_hospital int8 NULL REFERENCES public.empresa(empresa),

    --tratamento
    utilizou_antibiotico int2 NULL,
    data_administracao_antibiotico DATE NULL,

    --dados laboratoriais
    coleta_material_nasofaringe int2 NULL,
    dt_coleta_material DATE NULL,
    resultado_cultura int2 NULL,

    --medidas de controle
    realizada_identificacao_comunicantes int2 NULL,
    qtd_comunicantes VARCHAR(3) NULL,
    qtd_casos_secundarios_comunicantes int2 NULL,

    coleta_material_nasofaringe_comunicantes int2 NULL,
    qtd_coleta_material_nasofaringe_comunicantes VARCHAR(3) NULL,
    qtd_comunicantes_resultato_positivo VARCHAR(3) NULL,
    medidas_prevencao int2 NULL,

    --conclusao
    classificacao_final int2 NULL,
    criterio_confirmacao int2 NULL,
    doenca_relacionada_trabalho int2 NULL,
    evolucao_caso int2 NULL,
    data_obito DATE NULL,

    --observacao
    observacao text NULL,

    --encerramento
    dt_encerramento date NULL,
    cd_usuario_encerramento int8 REFERENCES public.usuarios(cd_usuario)
);

CREATE SEQUENCE seq_investigacao_agr_coqueluche INCREMENT 1 START 1;

CREATE TABLE auditschema.investigacao_agr_coqueluche AS
    (SELECT t2.*, t1.* FROM investigacao_agr_coqueluche t1, audit_temp t2 WHERE 1=2);

CREATE sequence seq_audit_id_investigacao_agr_coqueluche;

ALTER TABLE auditschema.investigacao_agr_coqueluche ADD PRIMARY KEY (audit_id);

CREATE trigger emp_audit AFTER INSERT OR UPDATE OR DELETE
    ON public.investigacao_agr_coqueluche FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();


/*
     Seidel - 16/03/2022 - VSA-73
 */

INSERT INTO public.ficha_investigacao_agravo(
    cd_ficha_investigacao_agravo, ordem, dt_cadastro, descricao, status, "version")
VALUES (23, 1, NOW(), 'LEPTOSPIROSE', 1, 0);

CREATE TABLE public.investigacao_agr_leptospirose(
    cd_invest_agr_leptospirose int8 PRIMARY KEY NOT NULL,
    "version" int8 NOT NULL,
    flag_informacoes_complementares varchar(1) NOT NULL DEFAULT 'S'::character varying,
    cd_registro_agravo int8 NOT NULL REFERENCES public.registro_agravo(cd_registro_agravo),

--investigacao
    dt_investigacao date NULL,
    ocupacao_cbo varchar(10) NULL REFERENCES public.tabela_cbo(cd_cbo),

--antecedentes epidemiologicos
    situacao_risco_agua_lama_enchente int2 NULL,
    situacao_risco_criacao_animais int2 NULL,
    situacao_risco_caixa_agua int2 NULL,
    situacao_risco_fossa_gordura_esgoto int2 NULL,
    situacao_risco_local_roedores int2 NULL,
    situacao_risco_plantio_colheita int2 NULL,
    situacao_risco_rio_corrego_lagoa_represa int2 NULL,
    situacao_risco_roedores_diretamente int2 NULL,
    situacao_risco_armazenamento_graos_alimentos int2 NULL,
    situacao_risco_terreno_baldio int2 NULL,
    situacao_risco_lixo_entulho int2 NULL,
    situacao_risco_outtos VARCHAR(50) NULL,

    casos_anteriores_local_humanos int2 NULL,
    casos_anteriores_local_animais int2 NULL,

--dados clinicos
    dt_atendimento DATE NULL,
    sinais_sintomas_febre int2 NULL,
    sinais_sintomas_mialgia int2 NULL,
    sinais_sintomas_cefaleia int2 NULL,
    sinais_sintomas_prostracao int2 NULL,
    sinais_sintomas_congestao_conjuntival int2 NULL,
    sinais_sintomas_dor_panturrilha int2 NULL,
    sinais_sintomas_vomito int2 NULL,
    sinais_sintomas_diarreia int2 NULL,
    sinais_sintomas_ictericia int2 NULL,
    sinais_sintomas_insuficiencia_renal int2 NULL,
    sinais_sintomas_alteracoes_respiratorias int2 NULL,
    sinais_sintomas_alteracoes_cardiacas int2 NULL,
    sinais_sintomas_hemorragia_pulmonar int2 NULL,
    sinais_sintomas_outras_hemorragias int2 NULL,
    sinais_sintomas_meningismo int2 NULL,
    sinais_sintomas_outros VARCHAR(50) NULL,

--hospitalização
    hospitalizacao int2 NULL,
    dt_internacao DATE NULL,
    dt_alta DATE NULL,
    unidade_hospital int8 NULL REFERENCES public.empresa(empresa),

--dados laboratoriais
    sorologia_igm_elisa_sn int2 NULL,
    sorologia_igm_dt_coleta_1 DATE NULL,
    sorologia_igm_resultado_1 int2 NULL,
    sorologia_igm_dt_coleta_2 DATE NULL,
    sorologia_igm_resultado_2 int2 NULL,

    microaglutinacao_sn int2 NULL,
    microaglutinacao_dt_coleta_1 DATE NULL,
    microaglutinacao_amostra_1_sorovar_1 VARCHAR(6),
    microaglutinacao_amostra_1_sorovar_2 VARCHAR(6),
    microaglutinacao_resultado_amostra_1 int2 NULL,
    microaglutinacao_dt_coleta_2 DATE NULL,
    microaglutinacao_amostra_2_sorovar_1 VARCHAR(6),
    microaglutinacao_amostra_2_sorovar_2 VARCHAR(6),
    microaglutinacao_resultado_amostra_2 int2 NULL,

    isolamento_sn int2 NULL,
    isolamento_dt_coleta DATE NULL,
    isolamento_resultado int2 NULL,

    imunohistoquimica_sn int2 NULL,
    imunohistoquimica_dt_coleta DATE NULL,
    imunohistoquimica_resultado int2 NULL,

    rtpcr_sn int2 NULL,
    rtpcr_dt_coleta DATE NULL,
    rtpcr_resultado int2 NULL,

--caso autoctone
    caso_autoctone int2 NULL,
    cd_pais_infeccao int8 NULL REFERENCES public.nacionalidade(cd_pais),
    cd_cidade_infeccao int8 NULL REFERENCES public.cidade(cod_cid),
    str_distrito_infeccao varchar(200) NULL,
    str_bairro_infeccao varchar(200) NULL,

--conclusao
    classificacao_final int2 NULL,
    criterio_confirmacao int2 NULL,
    area_provavel_infeccao int2 NULL,
    ambiente_infeccao int2 NULL,
    doenca_relacionada_trabalho int2 NULL,
    evolucao_caso int2 NULL,
    data_obito DATE NULL,

--observacao
    observacao text NULL,

--encerramento
    dt_encerramento date NULL,
    cd_usuario_encerramento int8 REFERENCES public.usuarios(cd_usuario)
);

CREATE SEQUENCE seq_investigacao_agr_leptospirose INCREMENT 1 START 1;

CREATE TABLE auditschema.investigacao_agr_leptospirose AS
    (SELECT t2.*, t1.* FROM investigacao_agr_leptospirose t1, audit_temp t2 WHERE 1=2);

CREATE sequence seq_audit_id_investigacao_agr_leptospirose;

ALTER TABLE auditschema.investigacao_agr_leptospirose ADD PRIMARY KEY (audit_id);

CREATE trigger emp_audit AFTER INSERT OR UPDATE OR DELETE
    ON public.investigacao_agr_leptospirose FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();


CREATE TABLE public.investigacao_agr_leptospirose_local_situacao_risco(
    cd_invest_agr_leptospirose_local_situacao_risco int8 PRIMARY KEY NOT NULL,
    cd_invest_agr_leptospirose int8 NOT NULL REFERENCES public.investigacao_agr_leptospirose(cd_invest_agr_leptospirose),
    dt_local_situacao DATE NULL,
    cd_cidade int8 NULL REFERENCES public.cidade(cod_cid),
    str_endereco VARCHAR(200) NULL,
    str_localidade VARCHAR(200) NULL
);

CREATE SEQUENCE seq_investigacao_agr_leptospirose_local_situacao_risco INCREMENT 1 START 1;

CREATE TABLE auditschema.investigacao_agr_leptospirose_local_situacao_risco AS
    (SELECT t2.*, t1.* FROM investigacao_agr_leptospirose_local_situacao_risco t1, audit_temp t2 WHERE 1=2);

CREATE sequence seq_audit_id_investigacao_agr_leptospirose_local_situacao_risco;

ALTER TABLE auditschema.investigacao_agr_leptospirose_local_situacao_risco ADD PRIMARY KEY (audit_id);

CREATE trigger emp_audit AFTER INSERT OR UPDATE OR DELETE
    ON public.investigacao_agr_leptospirose_local_situacao_risco FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();



/*
    Seidel - 16/03/2022 - VSA-77
*/


INSERT INTO public.ficha_investigacao_agravo(
    cd_ficha_investigacao_agravo, ordem, dt_cadastro, descricao, status, "version")
VALUES (32, 1, NOW(), 'LEISHMANIOSE TEGUMENTAR AMERICANA', 1, 0);

CREATE TABLE public.investigacao_agr_leishmaniose_tegumentar_americana(
    cd_invest_agr_leish_tegumentar int8 PRIMARY KEY NOT NULL,
    "version" int8 NOT NULL,
    flag_informacoes_complementares varchar(1) NOT NULL DEFAULT 'S'::character varying,
    cd_registro_agravo int8 NOT NULL REFERENCES public.registro_agravo(cd_registro_agravo),

--INVESTIGAÇÃO
    dt_investigacao date NULL,
    ocupacao_cbo varchar(10) NULL REFERENCES public.tabela_cbo(cd_cbo),

--DADOS CLÍNICOS
    presenca_lesao_cutanea int2 null,
    presenca_lesao_mucosa int2 null,
    presenca_lesao_mucosa_cicatriz int2 null,
    coinfeccao_hiv int2 null,

--DADOS LABORATORIAIS
    parasitologico_direto int2 null,
    irm int2 null,
    histopatologia int2 null,

--CLASSIFICAÇÃO DO CASO
    tipo_entrada int2 null,
    forma_clinica int2 null,

--TRATAMENTO
    dt_inicio_tratamento date null,
    droga_inicial_administrada int2 null,
    peso varchar(3) null,
    dose_prescrita int2 null,
    nro_ampolas_prescritas varchar(3) null,
    outra_droga_utilizada int2 null,


--CASO AUTÓCTONE
    caso_autoctone int2 NULL,
    cd_pais_infeccao int8 NULL REFERENCES public.nacionalidade(cd_pais),
    cd_cidade_infeccao int8 NULL REFERENCES public.cidade(cod_cid),
    str_distrito_infeccao varchar(200) NULL,
    str_bairro_infeccao varchar(200) NULL,

--CONCLUSÃO
    criterio_confirmacao int2 null,
    classificacao_epidemiologica int2 null,
    doenca_relacionada_trabalho int2 null,
    evolucao_caso int2 null,
    dt_obito date null,

--OBSERVAÇÃO
    observacao text NULL,

--ENCERRAMENTO
    dt_encerramento date NULL,
    cd_usuario_encerramento int8 REFERENCES public.usuarios(cd_usuario)
);

CREATE SEQUENCE seq_investigacao_agr_leishmaniose_tegumentar_americana INCREMENT 1 START 1;

CREATE TABLE auditschema.investigacao_agr_leishmaniose_tegumentar_americana AS
    (SELECT t2.*, t1.* FROM investigacao_agr_leishmaniose_tegumentar_americana t1, audit_temp t2 WHERE 1=2);

CREATE sequence seq_audit_id_investigacao_agr_leishmaniose_tegumentar_americana;

ALTER TABLE auditschema.investigacao_agr_leishmaniose_tegumentar_americana ADD PRIMARY KEY (audit_id);

CREATE trigger emp_audit AFTER INSERT OR UPDATE OR DELETE
    ON public.investigacao_agr_leishmaniose_tegumentar_americana FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();



CREATE TABLE public.investigacao_agr_leishmaniose_tegumentar_americana_deslocamento
(
    cd_invest_agr_leish_tegumentar_deslocamento int8 PRIMARY KEY NOT NULL,
    "version"                                   int8             NOT NULL,
    cd_invest_agr_leish_tegumentar              int8             NOT NULL REFERENCES public.investigacao_agr_leishmaniose_tegumentar_americana (cd_invest_agr_leish_tegumentar),

    dt_deslocamento                             DATE             NULL,
    cd_cidade_deslocamento                      int8             NULL REFERENCES public.cidade (cod_cid),
    cd_pais_deslocamento                        int8             NULL REFERENCES public.nacionalidade (cd_pais)
);

CREATE SEQUENCE seq_investigacao_agr_leish_tegumentar_americana_desloc INCREMENT 1 START 1;

CREATE TABLE auditschema.investigacao_agr_leishmaniose_tegumentar_americana_deslocamento AS
    (SELECT t2.*, t1.*
     FROM investigacao_agr_leishmaniose_tegumentar_americana_deslocamento t1,
          audit_temp t2
     WHERE 1 = 2);

CREATE SEQUENCE seq_audit_id_investigacao_agr_leish_tegumentar_americana_desloc;

ALTER TABLE auditschema.investigacao_agr_leishmaniose_tegumentar_americana_deslocamento
    ADD PRIMARY KEY (audit_id);

CREATE TRIGGER emp_audit
    AFTER INSERT OR UPDATE OR DELETE
    ON public.investigacao_agr_leishmaniose_tegumentar_americana_deslocamento
    FOR EACH ROW
EXECUTE PROCEDURE process_emp_audit();


/*
    Seidel - 28/03/2022 - Feature VSA-1847
*/


INSERT INTO public.ficha_investigacao_agravo(
    cd_ficha_investigacao_agravo, ordem, dt_cadastro, descricao, status, "version")
VALUES (28, 1, NOW(), 'HANSENÍASE', 1, 0);

CREATE TABLE public.investigacao_agr_hanseniase (

    cd_invest_agr_hanseniase int8 PRIMARY KEY NOT NULL,
    "version" int8 NOT NULL,
    flag_informacoes_complementares varchar(1) NOT NULL DEFAULT 'S'::character varying,
    cd_registro_agravo int8 NOT NULL REFERENCES public.registro_agravo(cd_registro_agravo),

--investigacao
    dt_investigacao date NULL,
    ocupacao_cbo varchar(10) NULL REFERENCES public.tabela_cbo(cd_cbo),

--observacao
    observacao varchar(5000) NULL,

--encerramento
    dt_encerramento date NULL,
    cd_usuario_encerramento int8 NULL,

--dados clinicos
    nr_lesoes_cutaneas varchar(3) null,
    forma_clinica int2 null,
    classificacao_operacional int2 null,
    nr_nervos_afetados varchar(2) null,

--atendimento
    grau_incapacidade_fisica int2 null,
    modo_entrada int2 null,
    modo_deteccao_caso_novo int2 null,

--dados laboratoriais
    baciloscopia int2 null,

--tratamento
    dt_inicio_tratamento date null,
    esquema_terapeutico_inicial int2 null,

--medidas controle
    nr_contatos_registrados varchar(2) null

);

CREATE SEQUENCE seq_investigacao_agr_hanseniase INCREMENT 1 START 1;

CREATE TABLE auditschema.investigacao_agr_hanseniase AS
    (SELECT t2.*, t1.* FROM investigacao_agr_hanseniase t1, audit_temp t2 WHERE 1=2);

CREATE sequence seq_audit_id_investigacao_agr_hanseniase;

ALTER TABLE auditschema.investigacao_agr_hanseniase ADD PRIMARY KEY (audit_id);

CREATE trigger emp_audit AFTER INSERT OR UPDATE OR DELETE
    ON public.investigacao_agr_hanseniase FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();


/*
    Seidel - 28/03/2022 - Feature VSA-1851
*/

INSERT INTO public.ficha_investigacao_agravo(
    cd_ficha_investigacao_agravo, ordem, dt_cadastro, descricao, status, "version")
VALUES (29, 1, NOW(), 'SÍNDROME DO CORRIMENTO URETRAL MASCULINO', 1, 0);

CREATE TABLE public.investigacao_agr_sindrome_corrimento_uretral_masc (
    cd_invest_agr_sindrome_corrimento_uretral_masc int8 PRIMARY KEY NOT NULL,
    "version" int8 NOT NULL,
    flag_informacoes_complementares varchar(1) NOT NULL DEFAULT 'S'::character varying,
    cd_registro_agravo int8 NOT NULL REFERENCES public.registro_agravo(cd_registro_agravo),

--investigacao
    dt_investigacao date NULL,
    ocupacao_cbo varchar(10) NULL REFERENCES public.tabela_cbo(cd_cbo),

--observacao
    observacao varchar(5000) NULL,

--encerramento
    dt_encerramento date NULL,
    cd_usuario_encerramento int8 REFERENCES public.usuarios(cd_usuario)
);

CREATE SEQUENCE seq_investigacao_agr_sindrome_corrimento_uretral_masc INCREMENT 1 START 1;

CREATE TABLE auditschema.investigacao_agr_sindrome_corrimento_uretral_masc AS
    (SELECT t2.*, t1.* FROM investigacao_agr_sindrome_corrimento_uretral_masc t1, audit_temp t2 WHERE 1=2);

CREATE sequence seq_audit_id_investigacao_agr_sindrome_corrimento_uretral_masc;

ALTER TABLE auditschema.investigacao_agr_sindrome_corrimento_uretral_masc ADD PRIMARY KEY (audit_id);

CREATE trigger emp_audit AFTER INSERT OR UPDATE OR DELETE
    ON public.investigacao_agr_sindrome_corrimento_uretral_masc FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

/*
    Seidel - 04/04/2022 - Feature VSA-1862
*/

ALTER TABLE auditschema.investigacao_agr_sifilis_adquirida DROP COLUMN encerramento_vdrl_3;
ALTER TABLE auditschema.investigacao_agr_sifilis_adquirida DROP COLUMN encerramento_vdrl_6;
ALTER TABLE auditschema.investigacao_agr_sifilis_adquirida DROP COLUMN encerramento_vdrl_9;
ALTER TABLE auditschema.investigacao_agr_sifilis_adquirida DROP COLUMN encerramento_vdrl_12;
ALTER TABLE auditschema.investigacao_agr_sifilis_adquirida DROP COLUMN encerramento_vdrl_18;
ALTER TABLE auditschema.investigacao_agr_sifilis_adquirida DROP COLUMN encerramento_vdrl_24;


ALTER TABLE public.investigacao_agr_sifilis_adquirida DROP COLUMN encerramento_vdrl_3;
ALTER TABLE public.investigacao_agr_sifilis_adquirida DROP COLUMN encerramento_vdrl_6;
ALTER TABLE public.investigacao_agr_sifilis_adquirida DROP COLUMN encerramento_vdrl_9;
ALTER TABLE public.investigacao_agr_sifilis_adquirida DROP COLUMN encerramento_vdrl_12;
ALTER TABLE public.investigacao_agr_sifilis_adquirida DROP COLUMN encerramento_vdrl_18;
ALTER TABLE public.investigacao_agr_sifilis_adquirida DROP COLUMN encerramento_vdrl_24;