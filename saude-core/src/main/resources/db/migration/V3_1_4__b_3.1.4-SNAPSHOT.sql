SET application_name = 'flyway|3.1.4';

/*
    Leonardo - #18130
*/

ALTER TABLE pedido_transferencia_item ADD saldo_atual_empresa  numeric(12,2)  null;
ALTER TABLE pedido_transferencia_item ADD consumo_trinta_dias  numeric(12,2)  null;
ALTER TABLE pedido_transferencia_item ADD consumo_noventa_dias  numeric(12,2)  null;
ALTER TABLE pedido_transferencia_item ADD quantidade_ultimo_pedido  numeric(12,2)  null;

ALTER TABLE auditschema.pedido_transferencia_item ADD saldo_atual_empresa  numeric(12,2)  null;
ALTER TABLE auditschema.pedido_transferencia_item ADD consumo_trinta_dias  numeric(12,2)  null;
ALTER TABLE auditschema.pedido_transferencia_item ADD consumo_noventa_dias  numeric(12,2)  null;
ALTER TABLE auditschema.pedido_transferencia_item ADD quantidade_ultimo_pedido  numeric(12,2)  null;

/*
    Leonardo - #18159
*/

alter table pedido_transferencia add column pedido_pai numeric(8) null;
alter table auditschema.pedido_transferencia add column pedido_pai numeric(8) null;

alter table pedido_transferencia
        add constraint FK_PEDIDO_REF_PEDIDO_PAI foreign key (pedido_pai)
        references pedido_transferencia (pedido)
        on delete restrict on update restrict;

/*
    Everton - 13/04/2018
*/

drop index if exists idx_atendimento_04;
create index idx_atendimento_04 on atendimento (cd_nat_proc_tp_atendimento);

/*
    Sulivan - 08/01/2018 - #16950
*/
ALTER TABLE fabricante ADD COLUMN cnpj varchar(15) null;
ALTER TABLE auditschema.fabricante ADD COLUMN cnpj varchar(15) null;

ALTER TABLE fabricante ADD COLUMN flag_internacional int2 null;
ALTER TABLE auditschema.fabricante ADD COLUMN flag_internacional int2 null;
UPDATE fabricante set flag_internacional = 0;
ALTER TABLE fabricante alter column flag_internacional set not null;

ALTER TABLE grupo_estoque ADD COLUMN cd_fabricante int8;
ALTER TABLE auditschema.grupo_estoque ADD COLUMN cd_fabricante int8;

ALTER TABLE grupo_estoque
ADD CONSTRAINT fk_gru_esto_ref_fabricante FOREIGN KEY (cd_fabricante)
REFERENCES fabricante (cd_fabricante)
on delete restrict on update restrict;

ALTER TABLE dispensacao_medicamento ADD COLUMN uf_conselho_reg varchar(2) null;
ALTER TABLE auditschema.dispensacao_medicamento ADD COLUMN uf_conselho_reg varchar(2) null;

ALTER TABLE historico_validade_lote ADD COLUMN cd_fabricante int8;
ALTER TABLE auditschema.historico_validade_lote ADD COLUMN cd_fabricante int8;

ALTER TABLE historico_validade_lote
ADD CONSTRAINT fk_his_val_lot_ref_fabricante FOREIGN KEY (cd_fabricante)
REFERENCES fabricante (cd_fabricante)
on delete restrict on update restrict;

ALTER TABLE controle_inventario ADD COLUMN cd_fabricante int8;
ALTER TABLE auditschema.controle_inventario ADD COLUMN cd_fabricante int8;

ALTER TABLE controle_inventario
ADD CONSTRAINT fk_cont_inven_ref_fabricante FOREIGN KEY (cd_fabricante)
REFERENCES fabricante (cd_fabricante)
on delete restrict on update restrict;

ALTER TABLE movimento_estoque ADD COLUMN cd_fabricante int8;
ALTER TABLE auditschema.movimento_estoque ADD COLUMN cd_fabricante int8;

ALTER TABLE movimento_estoque
ADD CONSTRAINT fk_mov_esto_ref_fabricante FOREIGN KEY (cd_fabricante)
REFERENCES fabricante (cd_fabricante)
on delete restrict on update restrict;

ALTER TABLE recebto_grupo_estoque ADD COLUMN cd_fabricante int8;
ALTER TABLE auditschema.recebto_grupo_estoque ADD COLUMN cd_fabricante int8;

ALTER TABLE recebto_grupo_estoque
ADD CONSTRAINT fk_reb_gru_est_ref_fabricante FOREIGN KEY (cd_fabricante)
REFERENCES fabricante (cd_fabricante)
on delete restrict on update restrict;

ALTER TABLE tipo_documento ADD COLUMN flag_tp_movimentacao_horus int2 null;
ALTER TABLE auditschema.tipo_documento ADD COLUMN flag_tp_movimentacao_horus int2 null;

alter table dispensacao_item_lote add column cd_horus_sincronizacao_processo int8;
alter table auditschema.dispensacao_item_lote add column cd_horus_sincronizacao_processo int8;

alter table dispensacao_item_lote
   add constraint fk_dis_it_ref_hor_sinc_proc foreign key (cd_horus_sincronizacao_processo)
   references horus_sincronizacao_processo (cd_horus_sincronizacao_processo)
   on delete restrict on update restrict;

ALTER TABLE horus_sincronizacao_processo ADD COLUMN nr_protocolo_recebimento varchar(20) null;
ALTER TABLE auditschema.horus_sincronizacao_processo ADD COLUMN nr_protocolo_entrada varchar(20) null;

ALTER TABLE horus_sincronizacao_processo ADD COLUMN dt_protocolo_recebimento varchar(20) null;
ALTER TABLE auditschema.horus_sincronizacao_processo ADD COLUMN dt_protocolo_recebimento varchar(20) null;


/*
    Mauricley - 17/04/2018 - #18169
*/

create index idx_temp_store_tabela_referencia on temp_store (cd_tabela_referencia, tabela_referencia);
create index idx_atendimento_prontuario_02 on atendimento_prontuario (nr_atendimento);
create index idx_evolucao_prontuario_01 on evolucao_prontuario (nr_atendimento);
create index idx_receituario_item_04 on receituario_item(cd_receituario);
create index idx_atendimento_encaminhamento_01 on atendimento_encaminhamento (nr_atendimento);
create index idx_conta_paciente_04 on conta_paciente (cd_atend_inf);
create index idx_esus_integracao_cds_01 on esus_integracao_cds (cd_esus_ficha_atend_individual);
create index idx_esus_integracao_cds_02 on esus_integracao_cds (cd_esus_ficha_atend_individual_item);

-- coluna com tipo incorreto (int8), impossibilitando a utilização do index
alter table usuario_cadsus_esus alter column cd_usu_cadsus type numeric(8) using cd_usu_cadsus::numeric(8);


/*
    Silvio - 26/04/2018 - #18416
*/
CREATE TABLE medicamento_catmat (
    cd_medicamento_catmat    int8            not null,
    descricao                varchar(200)    not null,
    catmat                   varchar(15)     not null,
    version                  int8            not null
);

ALTER TABLE medicamento_catmat
        ADD CONSTRAINT PK_MEDI_CATMAT PRIMARY KEY (cd_medicamento_catmat);

CREATE TABLE auditschema.medicamento_catmat AS SELECT t2.*, t1.* FROM medicamento_catmat t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_medicamento_catmat;alter table auditschema.medicamento_catmat add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON medicamento_catmat FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

ALTER TABLE medicamento_catmat DISABLE TRIGGER USER;

INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ABATACEPTE 125 MG/ML SOL INJ (SER PREENC) 1 ML','BR0434765U0234', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ABATACEPTE 250 MG PÓ P/ SUSP INJ (FR-AMP)','BR0365451U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ACETATO DE SODIO 2 MEQ/ML SOLUÇÃO INJETÁVEL 10 ML','BR0276774U0004', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ACETAZOLAMIDA 250 MG COMP','BR0278283U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ACETAZOLAMIDA 250 MG COMPRIMIDO ELENCO ESTADUAL','BR0278283U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ACICLOVIR 200 MG COMPRIMIDO ELENCO ESTADUAL','BR0268370U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ACICLOVIR 250 MG PÓ PARA SOLUÇÃO INJETÁVEL ELENCO ESTADUAL','BR0268374U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ACICLOVIR 5 % CREME  10 G','BR0268375U0015', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ACICLOVIR 5 % CREME  5 G','BR0268375U0233', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁCIDO ACETILSALICÍLICO  100 MG COMPRIMIDO','BR0267502U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁCIDO ACETILSALICÍLICO  500 MG COMPRIMIDO','BR0267501U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁCIDO FÓLICO 0,2 MG/ML SOLUÇÃO ORAL 30 ML','BR0278489U0097', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁCIDO FÓLICO 5 MG COMPRIMIDO ELENCO ESTADUAL','BR0267503U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁCIDO FOLÍNICO (FOLINATO DE CÁLCIO) 15 MG COMPRIMIDO ELENCO ESTADUAL','BR0268292U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁCIDO NICOTÍNICO 250 MG COMP DE LIB PROL','BR0395601U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁCIDO NICOTÍNICO 500 MG COMP DE LIB PROL','BR0367692U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁCIDO NICOTÍNICO 750 MG COMP DE LIB PROL','BR0367693U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁCIDO SALICÍLICO 5 % POMADA','BR0395558U0285', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁCIDO VALPRÓICO (VALPROATO DE SÓDIO)  250 MG CÁPSULA','BR0267504U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁCIDO VALPRÓICO (VALPROATO DE SÓDIO)  250 MG COMPRIMIDO ELENCO ESTADUAL','BR0267504U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁCIDO VALPRÓICO (VALPROATO DE SÓDIO)  500 MG COMPRIMIDO ELENCO ESTADUAL','BR0267505U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁCIDO VALPRÓICO (VALPROATO DE SÓDIO)  50 MG/ML XAROPE  100 ML ELENCO ESTADUAL','BR0308732U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ACITRETINA 10 MG CAP','BR0308719U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ACITRETINA 25 MG CAP','BR0308718U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ADALIMUMABE 40 MG/ML SOL INJ (SER PREENC)','BR0290058U0135', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁGUA BIDESTILADA, APIROGÊNICA, ESTÉRIL 100 ML','BR0315056U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁGUA BIDESTILADA, APIROGÊNICA, ESTÉRIL 10 ML','BR0315056U0004', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁGUA BIDESTILADA, APIROGÊNICA, ESTÉRIL 5 ML','BR0315056U0013', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'AGUA DESTILADA, BIDESTILADA, ESTÉRIL,  APIROGÊNICA 500ML','BR0315056U0103', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁGUA DESTILADA SOLUÇÃO INJETÁVEL 100 ML','BR0276839U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁGUA DESTILADA SOLUÇÃO INJETÁVEL 10 ML','BR0276839U0063', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁGUA DESTILADA SOLUÇÃO INJETÁVEL 500 ML','BR0276839U0103', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALBENDAZOL 400 MG COMPRIMIDO MASTIGÁVEL','BR0267506U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALBENDAZOL 40 MG/ML SUSPENSÃO ORAL  10 ML','BR0267507U0063', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALBENDAZOL 40 MG/ML SUSPENSÃO ORAL  20 ML','BR0440862U0086', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALCATRÃO MINERAL 1% POMADA','BR0395837U0018', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁLCOOL ETÍLICO 70 GEL 1000 ML','BR0269943U0060', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁLCOOL ETÍLICO 70% GEL 5000 ML','BR0269943U0144', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁLCOOL ETÍLICO 70% GEL 500 ML','BR0269943U0103', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁLCOOL ETÍLICO 70% GEL 800 ML','BR0269943U0114', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁLCOOL ETÍLICO 70% SOLUÇÃO 100 ML','BR0269941U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÁLCOOL ETÍLICO 70% SOLUÇÃO 1 L','BR0269941U0060', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALENDRONATO DE SÓDIO  10 MG COMPRIMIDO','BR0308721U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALENDRONATO DE SÓDIO  70 MG COMPRIMIDO','BR0269462U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALFACALCIDOL 0,25 MCG CAP','BR0292400U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALFACALCIDOL 1 MCG CAP','BR0292401U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALFADORNASE 2,5 MG AMP','BR0272787U0006', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALFAEPOETINA 10000 UI SOL INJ (FR-AMP)','BR0266630U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALFAEPOETINA 1000 UI SOL INJ (FR-AMP)','BR0266627U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALFAEPOETINA 2000 UI SOL INJ (FR-AMP)','BR0266628U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALFAEPOETINA 3000 UI SOL INJ (FR-AMP)','BR0406520U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALFAEPOETINA 4000 UI SOL INJ (FR-AMP)','BR0266629U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALFAINTERFERONA 2B 10000000 UI PÓ LIOF INJ (FR-AMP)','BR0266766U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALFAINTERFERONA 2B 3000000 UI PÓ LIOF INJ (FR-AMP)','BR0266767U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALFAINTERFERONA 2B 5000000 UI PÓ LIOF INJ (FR-AMP)','BR0266765U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALFAPEGINTERFERONA 2A 180 MCG SOL INJ (FR-AMP)','BR0267470U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALFAPEGINTERFERONA 2B 100 MCG PÓ LIOF INJ (FR-AMP)','BR0267473U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALFAPEGINTERFERONA 2B 120 MCG PÓ LIOF INJ (FR-AMP)','BR0267471U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALFAPEGINTERFERONA 2B 80 MCG PÓ LIOF INJ (FR-AMP)','BR0267472U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALFATALIGLICERASE 200 U PÓ LIOF INJ (FR-AMP)','BR0391336U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALFAVELAGLICERASE 200 U PÓ LIOF INJ (FR-AMP)','BR0394677U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALFAVELAGLICERASE 400 U PÓ LIOF INJ (FR-AMP)','BR0394678U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALOE VERA 5 % POMADA 30 G','BR0395847U0023', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALOPURINOL 100 MG COMPRIMIDO','BR0267508U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ALOPURINOL 300 MG COMPRIMIDO','BR0267509U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'AMANTADINA 100 MG COMP','BR0268079U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'AMBRISENTANA 10 MG COMP REV','BR0404656U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'AMBRISENTANA 5 MG COMP REV','BR0404655U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'AMIODARONA, CLORIDRATO 200 MG COMPRIMIDO','BR0267510U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'AMIODARONA, CLORIDRATO 50 MG/ML SOLUÇÃO INJETÁVEL 3 ML','BR0271710U0010', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'AMITRIPTILINA, CLORIDRATO 25 MG COMPRIMIDO ELENCO ESTADUAL','BR0267512U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'AMITRIPTILINA, CLORIDRATO 75 MG COMPRIMIDO','BR0276333U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'AMOXICILINA 500 MG CÁPSULA','BR0271089U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'AMOXICILINA 500 MG COMPRIMIDO','BR0271089U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'AMOXICILINA 50 MG/ML PÓ PARA SUSPENSÃO 150 ML','BR0271111U0074', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'AMOXICILINA 50 MG/ML PÓ PARA SUSPENSÃO 60 ML','BR0271111U0110', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'AMOXICILINA 50 MG/ML PÓ PARA SUSPENSÃO 80 ML','BR0271111U0115', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'AMOXICILINA 50 MG/ML PÓ PARA SUSPENSÃO ORAL  100 ML','BR0271111U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'AMOXICILINA + CLAVULANATO DE POTÁSSIO 500 MG + 125 MG COMPRIMIDO','BR0271217U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'AMOXICILINA + CLAVULANATO DE POTÁSSIO 50 MG+ 12,5 MG/ML SUSPENSÃO ORAL  100 ML','BR0281135U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'AMOXICILINA + CLAVULANATO DE POTÁSSIO 50 MG+ 12,5 MG/ML SUSPENSÃO ORAL  75 ML','BR0281135U0113', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ANLODIPINO, BESILATO 10 MG COMPRIMIDO','BR0268896U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ANLODIPINO, BESILATO 5 MG COMPRIMIDO','BR0272434U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ATENOLOL 100 MG COMPRIMIDO','BR0267518U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ATENOLOL 50 MG COMPRIMIDO','BR0267517U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ATORVASTATINA 10 MG COMP','BR0268080U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ATORVASTATINA 20 MG COMP','BR0268081U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ATORVASTATINA 40 MG COMP','BR0268082U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ATORVASTATINA 80 MG COMP','BR0291549U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ATROPINA, SULFATO 0,25 MG/ML SOLUÇÃO INJETÁVEL 1 ML','BR0268214U0005', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'AZATIOPRINA 50 MG COMP','BR0268083U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'AZITROMICINA 40 MG/ML PÓ PARA SUSPENSÃO 1200 MG','BR0314517U0155', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'AZITROMICINA 40 MG/ML PÓ PARA SUSPENSÃO 1500 MG','BR0314517U0059', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'AZITROMICINA 40 MG/ML PÓ PARA SUSPENSÃO 900 MG','BR0314517U0117', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'AZITROMICINA 500 MG COMPRIMIDO ELENCO ESTADUAL','BR0267140U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'AZITROMICINA PÓ PARA SUSPENSÃO 600ML','BR0314517U0108', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BECLOMETASONA, DIPROPIONATO 200 MCG CÁPSULA  60 DOSES','BR0267586U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BECLOMETASONA, DIPROPIONATO 200 MCG/DOSE PÓ PARA INALAÇÃO ORAL 100 DOSES','BR0267587U0061', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BECLOMETASONA, DIPROPIONATO 250 MCG/DOSE SOLUÇÃO ORAL 200 DOSES','BR0267581U0084', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BECLOMETASONA, DIPROPIONATO 400 MCG CÁPSULA  60 DOSES ELENCO ESTADUAL','BR0267585U0109', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BECLOMETASONA, DIPROPIONATO 400 MCG PÓ PARA INALAÇÃO ORAL 100 DOSES','BR0267589U0061', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BECLOMETASONA, DIPROPIONATO 50 MCG/DOSE AEROSSOL NASAL 130 DOSES','BR0267582U0573', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BECLOMETASONA, DIPROPIONATO 50 MCG/DOSE AEROSSOL ORAL 130 DOSES','BR0346586U0573', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BECLOMETASONA, DIPROPIONATO 50 MCG/DOSE AEROSSOL ORAL 200 DOSES','BR0346586U0084', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BECLOMETASONA, DIPROPIONATO 50 MG/DOSE AEROSSOL NASAL 200 DOSES','BR0267582U0084', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BENZILPENICILINA BENZATINA 1.200.000 UI PÓ PARA SUSPENSÃO INJETÁVEL','BR0270612U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BENZILPENICILINA BENZATINA 600.000 UI PÓ PARA SUSPENSÃO INJETÁVEL','BR0270613U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BENZILPENICILINA POTÁSSICA 5.000.000 UI PÓ PARA SOLUÇÃO INJETÁVEL','BR0270616U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BENZILPENICILINA PROCAÍNA + BENZILPENICILINA POTÁSSICA 400.000 UI PÓ PARA SUSPENSÃO INJETÁVEL','BR0270614U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BETAINTERFERONA 1A 12.000.000UI (44MCG) SOL INJ (SER PREENC)','BR0277434U0135', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BETAINTERFERONA 1A 6000000 UI (22MCG) SOL INJ (SER PREENC)','BR0277435U0135', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BETAINTERFERONA 1A 6000000 UI (30MCG) PÓ LIOF INJ (FR-AMP OU SER PREENC OU CAN PREENC)','BR0277743U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BETAINTERFERONA 1B 9.600.000UI (300MG) PÓ LIOF INJ (FR-AMP)','BR0277438U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BETAMETASONA, ACETATO + BETAMETASONA, FOSFATO DISSÓDICO 3 + 3 MG/ML SUSPENSÃO INJETÁVEL 1 ML','BR0270597U0005', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BEZAFIBRATO 200 MG DRÁGEA OU COMP','BR0267077U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BEZAFIBRATO 400 MG COMP DE DESINT LENTA','BR0308729U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BICARBONATO DE SÓDIO 8,4 % SOLUÇÃO INJETÁVEL 10 ML','BR0268222U0004', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BICARBONATO DE SÓDIO 8,4 % SOLUÇÃO INJETÁVEL 250 ML','BR0268222U0090', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BICARBONATO DE SÓDIO 8,4 % SOLUÇÃO INJETÁVEL 500 ML','BR0268222U0103', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BIMATOPROSTA 0,3 MG/ML SOL OFT (FR) 3 ML','BR0271848U0098', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BIPERIDENO, CLORIDRATO  2 MG COMPRIMIDO ELENCO ESTADUAL','BR0270140U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BIPERIDENO, CLORIDRATO  4 MG COMPRIMIDO DE LIBERAÇÃO PROLONGADA','BR0270141U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BIPERIDENO, LACTATO 5 MG/ML SOLUÇÃO INJETÁVEL 1 ML','BR0270138U0005', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BOSENTANA 125 MG COMP REV','BR0280115U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BOSENTANA 62,5 MG COMP REV','BR0280116U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BRIMONIDINA 2 MG/ML SOL OFT (FR) 5 ML','BR0268352U0106', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BRINZOLAMIDA 10 MG/ML SUSP OFT (FR) 5 ML','BR0271866U0106', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BROMOCRIPTINA 2,5 MG COMP','BR0270143U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BUDESONIDA 200 MCG CAP INAL','BR0352397U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BUDESONIDA 200 MCG PÓ INAL OU AER BUCAL (FR DE 100 DOSES)','BR0266700U0061', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BUDESONIDA 200 MCG PÓ INAL OU AER BUCAL (FR DE 200 DOSES)','BR0352395U0084', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BUDESONIDA 32 MCG/DOSE SUSPENSÃO NASAL 120 DOSES','BR0266706U0066', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BUDESONIDA 400 MCG CAP INAL','BR0352394U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BUDESONIDA 50 MCG/DOSE SOLUÇÃO NASAL 100 DOSES','BR0266701U0061', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BUDESONIDA 50 MCG/DOSE SOLUÇÃO NASAL 60 DOSES','BR0266701U0109', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BUDESONIDA 50 MCG/DOSE SUSPENSÃO NASAL 120 DOSES','BR0266701U0066', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BUDESONIDA 50 MCG/DOSE SUSPENSÃO NASAL 200 DOSES','BR0266701U0084', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BUDESONIDA 64 MCG/DOSE SUSPENSÃO NASAL 120 DOSES','BR0266707U0066', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BUPIVACAÍNA, CLORIDRATO 0,25 % SOLUÇÃO INJETÁVEL 20 ML','BR0269573U0086', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'BUPIVACAÍNA, CLORIDRATO 0,5 % SOLUÇÃO INJETÁVEL 20 ML','BR0269574U0007', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CABERGOLINA 0,5 MG COMP','BR0268084U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CABERGOLINA 0,5 MG COMPRIMIDO ELENCO ESTADUAL','BR0268084U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CALCIPOTRIOL 50 MCG/G POM (BIS) 30 G','BR0332849U0023', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CALCITONINA 100 UI SOL INJ (AMP)','BR0338890U0005', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CALCITONINA 200 UI POR DOSE SPRAY NAS (FR)','BR0338883U0391', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CALCITONINA 50 UI SOL INJ (AMP)','BR0338888U0005', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CALCITRIOL 0,25 MCG CAP','BR0271101U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CALCITRIOL 1 MCG SOL INJ (AMP)','BR0292416U0005', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CAPTOPRIL  25 MG COMPRIMIDO','BR0267613U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CARBAMAZEPINA  200 MG COMPRIMIDO ELENCO ESTADUAL','BR0267618U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CARBAMAZEPINA  20 MG/ML SUSPENSÃO ORAL  100 ML ELENCO ESTADUAL','BR0272454U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CARBAMAZEPINA  20 MG/ML SUSPENSÃO ORAL  120 ML','BR0440867U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CARBAMAZEPINA  400 MG COMPRIMIDO','BR0267617U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CARBONATO DE CÁLCIO (CÁLCIO ELEMENTAR) 500 MG COMPRIMIDO ELENCO ESTADUAL','BR0270895U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CARBONATO DE CÁLCIO + COLECALCIFEROL  500 MG + 200 UI COMPRIMIDO','BR0270893U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CARBONATO DE CÁLCIO + COLECALCIFEROL  500 MG + 400 UI COMPRIMIDO','BR0296876U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CARBONATO DE LÍTIO 300 MG COMPRIMIDO ELENCO ESTADUAL','BR0267621U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CARVÃO VEGETAL ATIVADO PÓ 1000 G','BR0395721U0189', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CARVÃO VEGETAL ATIVADO PÓ 250 G','BR0395721U0317', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CARVÃO VEGETAL ATIVADO PÓ 25 G','BR0395721U0547', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CARVÃO VEGETAL ATIVADO PÓ 500  G','BR0395721U0123', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CARVÃO VEGETAL ATIVADO PÓ 50 G','BR0395721U0329', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CARVEDILOL 12,5 MG COMPRIMIDO','BR0267564U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CARVEDILOL 25 MG  COMPRIMIDO','BR0267567U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CARVEDILOL 3,125 MG  COMPRIMIDO','BR0267566U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CARVEDILOL 6,25 MG  COMPRIMIDO','BR0267565U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CEFALEXINA 500 MG  CÁPSULA','BR0267625U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CEFALEXINA 500 MG  COMPRIMIDO','BR0267625U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CEFALEXINA 50 MG/ML SUSPENSÃO ORAL  100 ML','BR0331555U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CEFALEXINA 50 MG/ML SUSPENSÃO ORAL  60 ML','BR0331555U0110', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CEFOTAXIMA 500 MG  PÓ LIOFILIZADO PARA SOLUÇÃO INJETÁVEL','BR0268410U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CEFTRIAXONA SÓDICA 1G PÓ PARA SOLUÇÃO INJETÁVEL   IM','BR0268415U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CEFTRIAXONA SÓDICA 250 mg MG PÓ PARA SOLUÇÃO INJETÁVEL   IV','BR0294096U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CEFTRIAXONA SÓDICA 250 MG PÓ PARA SOLUÇÃO INJETÁVEL','BR0294097U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CEFTRIAXONA SÓDICA 500 mg  PÓ PARA SOLUÇÃO INJETÁVEL','BR0268417U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CERTOLIZUMABE PEGOL 200 MG/ML SOL INJ (SER PREENC)','BR0414435U0135', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CETOCONAZOL 2% XAMPU  100 ML','BR0271103U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CETOCONAZOL 2% XAMPU  120 ML','BR0271103U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CIANOCOBALAMINA, 1000 MCG/ML, SOLUÇÃO INJETÁVEL, AMPOLA 1 ML','BR0426098U0005', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CICLOFOSFAMIDA 50 MG DRÁGEA','BR0268427U0046', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CICLOSPORINA 100 MG CAP','BR0271107U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CICLOSPORINA 100 MG/ML SOL ORAL (FR) 50 ML','BR0302942U0105', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CICLOSPORINA 10 MG CAP','BR0271105U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CICLOSPORINA 25 MG CAP','BR0271104U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CICLOSPORINA 50 MG CAP','BR0271106U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CINACALCETE 30 MG COMP','BR0304788U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CINACALCETE 60 MG COMP','BR0304790U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CIPROFIBRATO 100 MG COMP','BR0308738U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CIPROFLOXACINO, CLORIDRATO 250 MG  COMPRIMIDO','BR0267631U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CIPROFLOXACINO, CLORIDRATO 500 MG  COMPRIMIDO ELENCO ESTADUAL','BR0267632U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CIPROTERONA 50 MG COMP','BR0268077U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLARITROMICINA 250 MG  COMPRIMIDO','BR0269986U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLARITROMICINA 500 MG  CÁPSULA','BR0440864U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLARITROMICINA 500 MG  COMPRIMIDO','BR0268439U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLARITROMICINA 50 MG/ML PÓ PARA SUSPENSÃO 100 ML','BR0440866U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLARITROMICINA 50 MG/ML PÓ PARA SUSPENSÃO 60 ML','BR0269988U0110', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLINDAMICINA, CLORIDRATO 150 MG  CÁPSULA','BR0268955U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLINDAMICINA, CLORIDRATO 300 MG CÁPSULA','BR0268436U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLOBAZAM 10 MG COMP','BR0272901U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLOBAZAM 20 MG COMP','BR0272902U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLOBETASOL 0,5 MG/G CREM (BIS) 30 G','BR0284458U0023', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLOBETASOL 0,5 MG/G SOL CAPI (FR) 50 G','BR0284460U0104', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLOMIPRAMINA, CLORIDRATO 10 MG  COMPRIMIDO','BR0267523U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLOMIPRAMINA, CLORIDRATO 25 MG  COMPRIMIDO','BR0267522U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLONAZEPAM 2,5 MG/ML SOLUÇÃO ORAL 20 ML','BR0270120U0086', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLOPIDOGREL 75 MG COMP','BR0272045U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLORANFENICOL 250 MG CÁPSULA','BR0335097U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLORANFENICOL 250 MG  COMPRIMIDO','BR0335097U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLORETO DE POTÁSSIO 19,10% SOLUÇÃO INJETÁVEL 10 ML','BR0267162U0004', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLORETO DE SÓDIO 0,9% SOLUÇÃO INJETÁVEL 1000 ML SISTEMA FECHADO','BR0268236U0033', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLORETO DE SÓDIO 0,9% SOLUÇÃO INJETÁVEL 100 ML SISTEMA FECHADO','BR0268236U0034', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLORETO DE SÓDIO 0,9% SOLUÇÃO INJETÁVEL 10 ML ELENCO ESTADUAL','BR0368654U0004', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLORETO DE SÓDIO 0,9% SOLUÇÃO INJETÁVEL 125 ML SISTEMA FECHADO','BR0268236U0491', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLORETO DE SÓDIO 0,9% SOLUÇÃO INJETÁVEL 250 ML SISTEMA FECHADO','BR0268236U0037', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLORETO DE SÓDIO 0,9 % SOLUÇÃO NASAL 10 ML','BR0375474U0063', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLORETO DE SÓDIO 0,9 % SOLUÇÃO NASAL 30 ML','BR0375474U0097', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLORETO DE SÓDIO 0,9 % SOLUÇÃO NASAL 50 ML','BR0375474U0105', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLORETO DE SÓDIO 20% SOLUÇÃO INJETÁVEL 10 ML','BR0267574U0004', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLOREXIDINA, DIGLICONATO 0,12% COLUTÓRIO 100 ML','BR0341174U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLOREXIDINA, DIGLICONATO 0,12% COLUTÓRIO 1 L','BR0341174U0060', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLOREXIDINA, DIGLICONATO 0,12% COLUTÓRIO 250 ML','BR0341174U0090', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLOREXIDINA, DIGLICONATO 0,12% COLUTÓRIO 500 ML','BR0341174U0103', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLOREXIDINA, DIGLICONATO 2% SOLUÇÃO DEGERMANTE 1000 ML','BR0269876U0060', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLOREXIDINA, DIGLICONATO 2 % SOLUÇÃO DEGERMANTE 100 ML','BR0269876U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLOREXIDINA DIGLICONATO, 2%, SOLUÇÃO TÓPICA, FRASCO 1000 ML','BR0269880U0060', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLOREXIDINA DIGLICONATO, 2%, SOLUÇÃO TÓPICA, FRASCO 500 ML','BR0269880U0103', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLOREXIDINA, DIGLICONATO 4 % SOLUÇÃO DEGERMANTE 1000 ML','BR0269877U0060', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLOREXIDINA, DIGLUCONATO 2% DEGERMANTE 120 ML','BR0269876U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLOREXIDINA, DIGLUCONATO 4%  SOLUÇÃO 100ML','BR0269877U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLOROQUINA 150 MG COMP','BR0272780U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLORPROMAZINA, CLORIDRATO 100 MG  COMPRIMIDO ELENCO ESTADUAL','BR0267638U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLORPROMAZINA, CLORIDRATO 25 MG  COMPRIMIDO','BR0267635U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLORPROMAZINA, CLORIDRATO 40 MG/ML SOLUÇÃO ORAL 20 ML','BR0340207U0086', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLORPROMAZINA, CLORIDRATO 5 MG/ML SOLUÇÃO INJETÁVEL 5 ML','BR0268069U0013', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLOZAPINA 100 MG COMP','BR0272431U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CLOZAPINA 25 MG COMP','BR0272429U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CODEÍNA 30 MG COMP','BR0272782U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CODEÍNA 30 MG/ML SOL INJ (AMP) 2 ML','BR0268443U0009', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CODEÍNA 3 MG/ML SOL ORAL (FR) 120 ML','BR0272784U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CODEÍNA 60 MG COMP','BR0268103U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'COMPLEMENTO ALIMENTAR P/ PACIENTE FENILCETONURICO MAIOR DE 1 ANO - FORMULA DE AMINOÁCIDOS ISENTA DE FENILALANINA 1 G PÓ','BR0267498U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'COMPLEMENTO ALIMENTAR P/ PACIENTE FENILCETONURICO MENOR DE 1 ANO - FORMULA DE AMINOÁCIDOS ISENTA DE FENILALANINA 1 G PÓ','BR0267499U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CYNARA SCOLYMUS (ALCACHOFRA) 1,5 MG/ML TINTURA 100 ML','BR0429303U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CYNARA SCOLYMUS (ALCACHOFRA) 200 MG CÁPSULA','BR0367515U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CYNARA SCOLYMUS  (ALCACHOFRA) 250 MG CÁPSULA','BR0429325U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CYNARA SCOLYMUS  (ALCACHOFRA) 300 MG CÁPSULA','BR0395618U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'CYNARA SCOLYMUS (ALCACHOFRA) 500 MG CÁPSULA','BR0399066U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DACLATASVIR 30 MG COMP REV','BR0435334U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DACLATASVIR 60 MG COMP REV','BR0431732U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DANAZOL 100 MG CAP','BR0272473U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DANAZOL 200 MG CAP','BR0272472U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DEFERASIROX 125 MG COMP','BR0325838U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DEFERASIROX 250 MG COMP','BR0325836U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DEFERASIROX 500 MG COMP','BR0325837U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DEFERIPRONA 500 MG COMP','BR0361382U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DESFERROXAMINA 500 MG PÓ LIOF INJ (FR-AMP)','BR0266736U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DESMOPRESSINA 0,1 MG/ML SOL OU SPRAY NAS (FR) 2,5 ML','BR0268074U0081', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DEXAMETASONA  0,1 % CREME  10 G','BR0267643U0015', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DEXAMETASONA  0,1 MG/ML ELIXIR 100 ML','BR0268243U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DEXAMETASONA  0,1 MG/ML ELIXIR 120 ML','BR0268243U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DEXAMETASONA 1 MG/G POMADA OFTALMICA  3,5 G','BR0271570U0022', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DEXAMETASONA 1 MG/ML SOLUÇÃO OFTÁLMICA 5 ML','BR0267187U0106', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DEXAMETASONA  4 MG COMPRIMIDO','BR0269388U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DEXAMETASONA, FOSFATO DISSÓDICO 4 MG/ML SOLUÇÃO INJETÁVEL 2,5 ML','BR0292427U0006', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DEXCLORFENIRAMINA, MALEATO 0,4 MG/ML SOLUÇÃO ORAL 100 ML','BR0267646U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DEXCLORFENIRAMINA, MALEATO 0,4 MG/ML SOLUÇÃO ORAL 120 ML','BR0267646U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DEXCLORFENIRAMINA, MALEATO 0,4 MG/ML SOLUÇÃO ORAL 150 ML','BR0440863U0074', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DEXCLORFENIRAMINA, MALEATO 0,4 MG/ML XAROPE  100 ML','BR0298454U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DEXCLORFENIRAMINA, MALEATO 0,4 MG/ML XAROPE  120 ML','BR0298454U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DEXCLORFENIRAMINA, MALEATO 2 MG COMPRIMIDO','BR0267645U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DIAFRAGMA, SILICONE, 65 MM, USO GINECOLÓGICO, COM ARO ESPIRAL EM METAL TRATADO, EMBALADO INDIVIDUALMENTE','BR0287595U0140', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DIAFRAGMA, SILICONE, 70 MM, USO GINECOLÓGICO, COM ARO ESPIRAL EM METAL TRATADO, EMBALADO INDIVIDUALMENTE','BR0287593U0140', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DIAFRAGMA, SILICONE, 75 MM, USO GINECOLÓGICO, COM ARO ESPIRAL EM METAL TRATADO, EMBALADO INDIVIDUALMENTE','BR0287594U0140', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DIAFRAGMA, SILICONE, 80 MM, USO GINECOLÓGICO, COM ARO ESPIRAL EM METAL TRATADO, EMBALADO INDIVIDUALMENTE','BR0287592U0140', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DIAZEPAM 10 MG COMPRIMIDO ELENCO ESTADUAL','BR0267197U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DIAZEPAM 5 MG COMPRIMIDO','BR0267195U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DIAZEPAM 5 MG/ML SOLUÇÃO INJETÁVEL 2 ML','BR0267194U0009', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DIGOXINA  0,05 MG/ML ELIXIR 60 ML','BR0267648U0110', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DIGOXINA  0,25 MG COMPRIMIDO','BR0267647U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DIPIRONA SÓDICA  500 MG COMPRIMIDO','BR0267203U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DIPIRONA SÓDICA  500 MG/ML SOLUÇÃO INJETÁVEL 2 ML','BR0268252U0009', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DIPIRONA SÓDICA  500 MG/ML SOLUÇÃO ORAL 10 ML','BR0267205U0063', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DIPIRONA SÓDICA  500 MG/ML SOLUÇÃO ORAL 15 ML','BR0267205U0075', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DIPIRONA SÓDICA  500 MG/ML SOLUÇÃO ORAL 20 ML','BR0267205U0086', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DISPOSITIVO INTRA-UTERINO (DIU), EM "T", FLEXÍVEL, POLETILENO, FIO COBRE ENROLADO HASTE,CONE COBRE NOS BRAÇOS "T", 2 FIOS POLIETILENO BRANCO 2 A 3CM','BR0297746U0140', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DOBUTAMINA, CLORIDRATO 12,5 MG/ML SOLUÇÃO INJETÁVEL 20 ML','BR0268446U0007', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DONEPEZILA 10 MG COMP','BR0272786U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DONEPEZILA 5 MG COMP','BR0272785U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DOPAMINA, CLORIDRATO 5 MG/ML SOLUÇÃO INJETÁVEL 10 ML','BR0268960U0004', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DORZOLAMIDA 20 MG/ML SOL OFT (FR) 5 ML','BR0272580U0106', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DOXAZOSINA, MESILATO 2 MG COMPRIMIDO','BR0268493U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'DOXAZOSINA, MESILATO 4 MG COMPRIMIDO','BR0268495U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ENALAPRIL, MALEATO 10 MG COMPRIMIDO ELENCO ESTADUAL','BR0267651U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ENALAPRIL, MALEATO 20 MG COMPRIMIDO','BR0267652U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ENALAPRIL, MALEATO 5 MG COMPRIMIDO','BR0267650U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ENTACAPONA 200 MG COMP','BR0272653U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ENTECAVIR 0,5 MG COMP','BR0315088U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ENTECAVIR 1 MG COMP','BR0315089U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'EPINEFRINA 1 MG/ML SOLUÇÃO INJETÁVEL 1 ML','BR0268255U0005', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ERITROMICINA, ESTOLATO 25 MG/ML SUSPENSÃO ORAL  60 ML','BR0269997U0110', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ERITROMICINA, ESTOLATO 500 MG COMPRIMIDO','BR0269996U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ERITROMICINA, ESTOLATO 50 MG/ML SUSPENSÃO ORAL  100 ML','BR0269998U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ERITROMICINA, ESTOLATO 50 MG/ML SUSPENSÃO ORAL  50 ML','BR0269998U0105', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ERITROMICINA, ESTOLATO 50 MG/ML SUSPENSÃO ORAL  60 ML','BR0269998U0110', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ESPIRONOLACTONA  100 MG COMPRIMIDO','BR0267654U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ESPIRONOLACTONA  25 MG COMPRIMIDO','BR0267653U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ESTRIOL 1 MG/G CREME VAGINAL  50 G','BR0267208U0029', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ESTROGÊNIOS CONJUGADOS 0,3 MG COMPRIMIDO','BR0356602U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ESTROGÊNIOS CONJUGADOS 0,625 MG/G CREME VAGINAL  25 G','BR0271435U0019', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ETANERCEPTE 25 MG PÓ LIOF INJ (FR-AMP)','BR0343607U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ETANERCEPTE 50 MG PÓ LIOF INJ (FR-AMP OU SER PREENC)','BR0343608U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ETOFIBRATO 500 MG CAP','BR0268181U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ETOSSUXIMIDA 50 MG/ML XPE (FR) 120 ML','BR0272792U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'EVEROLIMO 0,5 MG COMP','BR0369176U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'EVEROLIMO 0,75 MG COMP','BR0369178U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'EVEROLIMO 1 MG COMP','BR0369179U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FENITOÍNA SÓDICA  100 MG COMPRIMIDO ELENCO ESTADUAL','BR0267657U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FENITOÍNA SÓDICA  20 MG/ML SUSPENSÃO ORAL  120 ML','BR0269391U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FENITOÍNA SÓDICA  50 MG/ML SOLUÇÃO INJETÁVEL 5 ML','BR0267107U0013', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FENOBARBITAL  100 MG COMPRIMIDO ELENCO ESTADUAL','BR0267660U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FENOBARBITAL  100 MG/ML SOLUÇÃO INJETÁVEL 2 ML','BR0300725U0009', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FENOBARBITAL  40 MG/ML SOLUÇÃO ORAL 20 ML ELENCO ESTADUAL','BR0300723U0086', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FENOFIBRATO 200 MG CAP','BR0267081U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FENOFIBRATO 250 MG CAP DE LIB RETARD','BR0342501U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FENOTEROL 100 MCG AER BUCAL (FR DE 200 DOSES)','BR0270785U0084', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FILGRASTIM 300 MCG SOL INJ (FR-AMP OU SER PREENC)','BR0268118U0135', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FINASTERIDA 5 MG COMPRIMIDO','BR0275963U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FINGOLIMODE 0,5 MG CAP','BR0412094U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FLUCONAZOL 100 MG CÁPSULA  ELENCO ESTADUAL','BR0267661U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FLUCONAZOL, 10 MG/ML, SUSPENSÃO, FRASCO 100 ML','BR0274437U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FLUCONAZOL 150 MG CÁPSULA','BR0267662U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FLUDROCORTISONA 0,1 MG COMP','BR0272793U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FLUMAZENIL 0,1 MG/ML SOLUÇÃO INJETÁVEL 5 ML','BR0268510U0013', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FLUOXETINA, CLORIDRATO 20 MG CÁPSULA  ELENCO ESTADUAL','BR0273009U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FLUOXETINA, CLORIDRATO 20 MG COMPRIMIDO ELENCO ESTADUAL','BR0273009U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FLUVASTATINA 20 MG CAP','BR0267083U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FLUVASTATINA 40 MG CAP','BR0267084U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FORMOTEROL 12 MCG CAP INAL','BR0271373U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FORMOTEROL 12 MCG PÓ INAL (FR DE 60 DOSES)','BR0266599U0109', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FORMOTEROL + BUDESONIDA 12 + 400 MCG CAP INAL','BR0305649U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FORMOTEROL + BUDESONIDA 12 + 400 MCG PÓ INAL (FR DE 60 DOSES)','BR0305652U0109', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FORMOTEROL + BUDESONIDA 6 + 200 MCG CAP INAL','BR0387341U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FORMOTEROL + BUDESONIDA 6 + 200 MCG PÓ INAL (FR DE 60 DOSES)','BR0609095U0109', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FOSFATO DE CÁLCIO TRIBÁSICO + COLECALCIFEROL 600 MG + 400 UI COMPRIMIDO','BR0386482U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FOSFATO DE POTÁSSIO, MONOBÁSICO E DIBÁSICO 2 MEQ/ML SOLUÇÃO INJETÁVEL 10 ML','BR0313689U0004', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FUROSEMIDA 10 MG/ML SOLUÇÃO INJETÁVEL 2 ML','BR0267666U0009', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'FUROSEMIDA 40 MG COMPRIMIDO','BR0267663U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GABAPENTINA 300 MG CAP','BR0268107U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GABAPENTINA 400 MG CAP','BR0268106U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GALANTAMINA 16 MG CÁPSULA DE LIBERAÇÃO CONTROLADA','BR0315735U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GALANTAMINA 24 MG CAP DE LIB PROL','BR0315734U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GALANTAMINA 8 MG CAP DE LIB PROL','BR0276388U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GEL LUBRIFICANTE, ÍNTIMO, INCOLOR, INODORO, SOLÚVEL EM ÁGUA, TRANSPARENTE E NÃO GORDUROSO, SACHÊ 10 G','BR0325424U0127', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GEL LUBRIFICANTE, ÍNTIMO, INCOLOR, INODORO, SOLÚVEL EM ÁGUA, TRANSPARENTE E NÃO GORDUROSO, SACHÊ 20 G','BR0325424U0424', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GEL LUBRIFICANTE, ÍNTIMO, INCOLOR, INODORO, SOLÚVEL EM ÁGUA, TRANSPARENTE E NÃO GORDUROSO, SACHÊ 5 G','BR0325424U0541', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GENFIBROZILA 600 MG COMP','BR0267088U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GENFIBROZILA 900 MG COMP','BR0267087U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GENTAMICINA, SULFATO 5 MG/ML SOLUÇÃO OFTÁLMICA 5 ML','BR0372372U0106', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLATIRAMER 20 MG PÓ LIOF INJ (FR-AMP OU SER PREENC)','BR0342746U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLIBENCLAMIDA 5 MG COMPRIMIDO','BR0267671U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLICEROL   120 MG/ML ENEMA  1 L','BR0269622U0060', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLICEROL   120 MG/ML ENEMA  250 ML','BR0269622U0090', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLICEROL   120 MG/ML ENEMA  500 ML','BR0269622U0103', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLICLAZIDA 30 MG COMPRIMIDO DE LIBERAÇÃO PROLONGADA','BR0273116U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLICLAZIDA 60 MG COMPRIMIDO DE LIBERAÇÃO PROLONGADA','BR0417713U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLICLAZIDA 80 MG COMPRIMIDO','BR0273115U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLICOSE 10 % SOLUÇÃO INJETÁVEL 1000 ML SISTEMA FECHADO','BR0267544U0033', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLICOSE 10 % SOLUÇÃO INJETÁVEL 100 ML SISTEMA FECHADO','BR0267544U0034', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLICOSE 10 % SOLUÇÃO INJETÁVEL 250 ML SISTEMA FECHADO','BR0267544U0037', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLICOSE 10 % SOLUÇÃO INJETÁVEL 500 ML SISTEMA FECHADO','BR0267544U0039', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLICOSE 50 % SOLUÇÃO INJETÁVEL 10 ML','BR0267541U0004', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLICOSE 50 % SOLUÇÃO INJETÁVEL  500 ML SISTEMA FECHADO','BR0353564U0039', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLICOSE 5 % SOLUÇÃO INJETÁVEL 1000 ML SISTEMA FECHADO','BR0270092U0033', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLICOSE 5 % SOLUÇÃO INJETÁVEL 100 ML SISTEMA FECHADO','BR0270092U0034', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLICOSE 5 % SOLUÇÃO INJETÁVEL 250 ML SISTEMA FECHADO','BR0270092U0037', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLICOSE 5 % SOLUÇÃO INJETÁVEL 500 ML SISTEMA FECHADO','BR0270092U0039', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLUTARALDEÍDO SOLUÇÃO 2% COM PÓ ATIVADOR PARA 14 DIAS 1 L','BR0269882U0079', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLUTARALDEÍDO SOLUÇÃO 2% COM PÓ ATIVADOR PARA 14 DIAS 5L','BR0269882U0144', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLUTARALDEÍDO, SOLUÇÃO A 2%, COM PÓ ATIVADOR PARA 28 DIAS, GALÃO 1 L','BR0269883U0079', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLUTARALDEÍDO, SOLUÇÃO A 2%, COM PÓ ATIVADOR PARA 28 DIAS, GALÃO 5 L','BR0269883U0144', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLYCINE MAX (ISOFLAVONA DE SOJA) 125 MG COMP REV','BR0432197U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLYCINE MAX (ISOFLAVONA DE SOJA) 150 MG CÁPSULA','BR0395620U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GLYCINE MAX (ISOFLAVONA DE SOJA) 75 MG CÁPSULA','BR0404690U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GOLIMUMABE 50 MG SOL INJ (SER PREENC)','BR0414431U0135', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GOSSERRELINA 10,8 MG DEPOT (SER PREENC)','BR0268108U0135', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'GOSSERRELINA 3,6 MG DEPOT (SER PREENC)','BR0268109U0135', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HALOPERIDOL 1 MG COMPRIMIDO','BR0267670U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HALOPERIDOL 2 MG/ML SOLUÇÃO ORAL 10 ML','BR0292195U0063', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HALOPERIDOL 2 MG/ML SOLUÇÃO ORAL 20 ML','BR0292195U0086', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HALOPERIDOL 2 MG/ML SOLUÇÃO ORAL 30 ML','BR0292195U0097', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HALOPERIDOL 5 MG COMPRIMIDO','BR0267669U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HALOPERIDOL 5 MG/ML SOLUÇÃO INJETÁVEL 1 ML','BR0292196U0005', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HALOPERIDOL, DECANOATO 50MG/ML SOLUÇÃO INJETÁVEL 1 ML ELENCO ESTADUAL','BR0292194U0005', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HARPAGOPHYTUM PROCUBENS (GARRA DO DIABO) 200 MG CÁPSULA','BR0417285U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HARPAGOPHYTUM PROCUBENS (GARRA DO DIABO) 400 MG COMPRIMIDO','BR0393339U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HARPAGOPHYTUM PROCUBENS (GARRA DO DIABO) 500 MG CÁPSULA','BR0399410U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HEPARINA SÓDICA 5000 UI/ML SOLUÇÃO INJETÁVEL  5 ML','BR0272796U0106', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIDRALAZINA, CLORIDRATO 25 MG COMPRIMIDO','BR0268111U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIDRALAZINA, CLORIDRATO 25 MG DRÁGEA','BR0268111U0046', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIDRALAZINA, CLORIDRATO 50 MG COMPRIMIDO','BR0268112U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIDRALAZINA, CLORIDRATO 50 MG DRÁGEA','BR0268112U0046', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIDROCLOROTIAZIDA  12,5 MG COMPRIMIDO','BR0364324U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIDROCLOROTIAZIDA  25MG COMPRIMIDO','BR0267674U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIDROCORTISONA, ACETATO 10 MG/G CREME  15 G','BR0345240U0017', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIDROCORTISONA, ACETATO 10 MG/G CREME  20 G','BR0345240U0018', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIDROCORTISONA, ACETATO 10MG/G CREME  30 G','BR0345240U0023', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIDROCORTISONA, SUCCINATO SÓDICO 100MG PÓ PARA SOLUÇÃO INJETÁVEL','BR0270220U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIDROCORTISONA, SUCCINATO SÓDICO 500MG PÓ PARA SOLUÇÃO INJETÁVEL','BR0342134U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIDROXICLOROQUINA 400 MG COMP','BR0268119U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIDRÓXIDO DE ALUMÍNIO 230 MG COMP','BR0354627U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIDRÓXIDO DE ALUMÍNIO 230 MG COMPRIMIDO','BR0367514U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIDRÓXIDO DE ALUMÍNIO 300 MG COMP','BR0267271U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIDRÓXIDO DE ALUMÍNIO 300 MG COMPRIMIDO','BR0267271U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIDRÓXIDO DE ALUMÍNIO 61,5 MG/ML SUSPENSÃO ORAL  100 ML','BR0340783U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIDRÓXIDO DE ALUMÍNIO 61,5 MG/ML SUSPENSÃO ORAL  150 ML','BR0340783U0074', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIDRÓXIDO DE ALUMÍNIO 61,5 MG/ML SUSPENSÃO ORAL  240 ML','BR0340783U0087', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIDRÓXIDO DE ALUMÍNIO 61,5 MG SUSP ORAL (FR) 100 ML','BR0340783U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIDRÓXIDO DE ALUMÍNIO 61,5 MG SUSP ORAL (FR) 150 ML','BR0340783U0074', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIDRÓXIDO DE ALUMÍNIO 61,5 MG SUSP ORAL (FR) 240 ML','BR0340783U0087', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIDROXIURÉIA 500 MG CAP','BR0268110U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIPOCLORITO DE SÓDIO 1% SOLUÇÃO 1 L','BR0437161U0060', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIPOCLORITO DE SÓDIO 2,0 A 2,5 % SOLUÇÃO 30 ML','BR0437156U0097', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIPOCLORITO DE SÓDIO 2,0 A 2,5 % SOLUÇÃO 50 ML','BR0437156U0105', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIPOCLORITO DE SÓDIO 2,0 A 2,5 % SOLUÇÃO 60 ML','BR0437156U0110', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIPROMELOSE 3 MG/ML SOLUÇÃO OFTÁLMICA 10 ML','BR0373909U0063', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIPROMELOSE 3 MG/ML SOLUÇÃO OFTÁLMICA 15 ML','BR0373909U0075', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIPROMELOSE 3 MG/ML SOLUÇÃO OFTÁLMICA 5 ML','BR0373909U0106', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'HIPROMELOSE 5 MG/ML SOLUÇÃO OFTÁLMICA 10 ML','BR0270042U0063', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'IBUPROFENO  200 MG COMPRIMIDO','BR0294648U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'IBUPROFENO  300 MG COMPRIMIDO','BR0267677U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'IBUPROFENO  50 MG/ML SUSPENSÃO ORAL  30 ML','BR0332754U0097', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'IBUPROFENO  600 MG COMPRIMIDO','BR0267676U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ILOPROSTA 10 MCG/ML SOL P/ NEBUL (AMP) 1 ML','BR0294881U0005', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'IMIGLUCERASE 200 U PÓ LIOF INJ (FR-AMP)','BR0266752U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'IMIGLUCERASE 400 U PÓ LIOF INJ (FR-AMP)','BR0383272U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'IMUNOGLOBULINA ANTI-HEPATITE B 100 UI SOL INJ (FR)','BR0260160U0229', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'IMUNOGLOBULINA ANTI-HEPATITE B 500 UI SOL INJ (FR)','BR0260160U0063', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'IMUNOGLOBULINA ANTI-HEPATITE B 600 UI SOL INJ (FR)','BR0273567U0010', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'IMUNOGLOBULINA HUMANA 0,5 G INJ (FR)','BR0266825U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'IMUNOGLOBULINA HUMANA 1 G INJ (FR)','BR0266823U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'IMUNOGLOBULINA HUMANA 2,5 G INJ (FR)','BR0266822U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'IMUNOGLOBULINA HUMANA 3 G INJ (FR)','BR0266824U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'IMUNOGLOBULINA HUMANA 5 G INJ (FR)','BR0266820U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'IMUNOGLOBULINA HUMANA 6 G INJ (FR)','BR0266821U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'INFLIXIMABE 10 MG/ML PÓ LIOF INJ (FR-AMP) 10 ML','BR0333447U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'INSULINA HUMANA NPH 100 UI/ML SUSPENSÃO INJETÁVEL 10 ML ELENCO ESTADUAL','BR0271157U0063', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'INSULINA HUMANA NPH 100 UI/ML SUSPENSÃO INJETÁVEL 3 ML','BR0271157U0137', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'INSULINA HUMANA REGULAR 100 UI/ML SOLUÇÃO INJETÁVEL 10 ML ELENCO ESTADUAL','BR0271154U0063', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'INSULINA HUMANA REGULAR 100 UI/ML SOLUÇÃO INJETÁVEL 3 ML','BR0271154U0137', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'IODO + IODETO DE POTÁSSIO 20 + 40 MG/ML (LUGOL 2 %) SOLUÇÃO 1 L','BR0327215U0060', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'IODO + IODETO DE POTASSIO 20 + 40 MG/ML (LUGOL 2%) SOLUÇÃO 250 ML','BR0327215U0090', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'IODO IODETO DE POTÁSSIO  SOLUÇÃO 500ML','BR0327215U0103', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'IPRATRÓPIO, BROMETO 0,02 MG/DOSE PÓ PARA INALAÇÃO ORAL 200 DOSES','BR0268332U0084', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'IPRATRÓPIO, BROMETO  0,25 MG/ML SOLUÇÃO PARA INALAÇÃO 15 ML','BR0268331U0075', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'IPRATRÓPIO, BROMETO  0,25 MG/ML SOLUÇÃO PARA INALAÇÃO 20 ML','BR0268331U0086', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ISOSSORBIDA, DINITRATO 5 MG COMPRIMIDO SUBLINGUAL','BR0273395U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ISOSSORBIDA, MONONITRATO 20 MG COMPRIMIDO','BR0273400U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ISOSSORBIDA, MONONITRATO 40 MG COMPRIMIDO','BR0273401U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ISOTRETINOÍNA 10 MG CAP','BR0272807U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ISOTRETINOÍNA 20 MG CAP','BR0272808U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ITRACONAZOL 100 MG CÁPSULA','BR0268861U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ITRACONAZOL 10 MG/ML SUSP ORAL (FR) 150 ML','BR0394789U0074', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'IVERMECTINA 6 MG COMPRIMIDO','BR0273328U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LACTULOSE 667 MG/ML XAROPE  120 ML','BR0305247U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LAMIVUDINA 10 MG/ML SOL ORAL (FR) 240 ML','BR0328810U0087', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LAMIVUDINA 150 MG COMP','BR0268345U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LAMOTRIGINA 100 MG COMP','BR0272809U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LAMOTRIGINA 25 MG COMP','BR0295040U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LAMOTRIGINA 50 MG COMP','BR0324414U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LANCETA, AÇO INOXIDÁVEL,PONTA AFIADA,TRIFACETADA, DESCARTÁVEL, ESTÉRIL, EMBALAGEM INDIVIDUAL','BR0303151U0140', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LANCETA, AÇO INOXIDÁVEL,PONTA AFIADA,TRIFACETADA, DESCARTÁVEL, ESTÉRIL, EMBALAGEM INDIVIDUAL, COM SISTEMA RETRÁTIL','BR0338605U0140', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LANCETA, AÇO INOXIDÁVEL, TRIFACETADA, ULTRA FINA, DESCARTÁVEL, ESTÉRIL','BR0389338U0140', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LANREOTIDA 120 MG SOL INJ (SER PREENC)','BR0404606U0135', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LANREOTIDA 60 MG SOL INJ (SER PREENC)','BR0404603U0135', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LANREOTIDA 90 MG SOL INJ (SER PREENC)','BR0404605U0135', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LATANOPROSTA 0,05 MG/ML SOL OFT (FR) 2,5 ML','BR0294417U0081', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LEFLUNOMIDA 20 MG COMP','BR0268114U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LEUPRORRELINA 11,25 MG PÓ LIOF INJ (SER PREENC)','BR0305260U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LEUPRORRELINA 3,75 MG PÓ LIOF INJ (FR-AMP)','BR0305257U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LEVODOPA + BENSERAZIDA  100 + 25 MG CÁPSULA  ELENCO ESTADUAL','BR0270127U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LEVODOPA + BENSERAZIDA  100 + 25 MG COMPRIMIDO ELENCO ESTADUAL','BR0270128U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LEVODOPA + BENSERAZIDA  200 + 50 MG COMPRIMIDO ELENCO ESTADUAL','BR0270126U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LEVODOPA + CARBIDOPA  200 + 50  MG COMPRIMIDO ELENCO ESTADUAL','BR0270129U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LEVODOPA + CARBIDOPA  250 + 25 MG COMPRIMIDO ELENCO ESTADUAL','BR0270130U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LEVONORGESTREL 0,75 MG COMPRIMIDO','BR0268956U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LEVONORGESTREL 1,5 MG COMPRIMIDO','BR0295853U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LEVONORGESTREL + ETINILESTRADIOL  0,15 + 0,03 MG COMPRIMIDO','BR0272789U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LEVOTIROXINA SÓDICA 100 MCG COMPRIMIDO ELENCO ESTADUAL','BR0268125U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LEVOTIROXINA SÓDICA 25 MCG COMPRIMIDO ELENCO ESTADUAL','BR0268124U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LEVOTIROXINA SÓDICA 50 MCG COMPRIMIDO ELENCO ESTADUAL','BR0268123U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LIDOCAÍNA, CLORIDRATO 100 MG/ML SOLUÇÃO TÓPICA 20 ML','BR0269845U0086', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LIDOCAÍNA, CLORIDRATO 100 MG/ML SOLUÇÃO TÓPICA 40 ML','BR0269845U0101', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LIDOCAÍNA, CLORIDRATO 100 MG/ML SOLUÇÃO TÓPICA 50 ML','BR0269845U0105', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LIDOCAÍNA, CLORIDRATO 100 MG/ML SOLUÇÃO TÓPICA 70 ML','BR0440865U0112', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LIDOCAÍNA, CLORIDRATO 10 MG/ML SOLUÇÃO INJETÁVEL 20 ML','BR0269842U0086', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LIDOCAÍNA, CLORIDRATO 10 MG/ML SOLUÇÃO INJETÁVEL 50 ML','BR0269842U0105', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LIDOCAÍNA, CLORIDRATO 10 MG/ML SOLUÇÃO INJETÁVEL 5 ML','BR0269842U0013', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LIDOCAÍNA, CLORIDRATO 20 MG/G GEL 10 G','BR0269846U0015', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LIDOCAÍNA, CLORIDRATO 20 MG/G GEL 30 G','BR0269846U0023', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LIDOCAÍNA, CLORIDRATO 20 MG/ML SOLUÇÃO INJETÁVEL 1,8 ML','BR0269843U0165', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LIDOCAÍNA, CLORIDRATO 20 MG/ML SOLUÇÃO INJETÁVEL 20 ML','BR0269843U0086', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LIDOCAÍNA, CLORIDRATO 20 MG/ML SOLUÇÃO INJETÁVEL 5 ML','BR0269843U0013', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LIDOCAÍNA, CLORIDRATO + EPINEFRINA 10 MG/ML + 1.200.000  UI SOLUÇÃO INJETÁVEL 20 ML','BR0275402U0086', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LIDOCAÍNA, CLORIDRATO + EPINEFRINA 20 MG/ML + 1:200.000 UI SOLUÇÃO INJETÁVEL 20 ML','BR0269852U0086', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LIDOCAÍNA, CLORIDRATO + EPINEFRINA 20 MG/ML + 1:200.000 UI SOLUÇÃO INJETÁVEL 5 ML','BR0269852U0013', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LIDOCAÍNA, CLORIDRATO + EPINEFRINA 2% + 1:80.000 SOLUÇÃO INJETÁVEL 1,8 ML','BR0397428U0165', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LIDOCAÍNA, CLORIDRATO + EPINEFRINA 2% + 1:80.000 SOLUÇÃO INJETÁVEL 20 ML','BR0397428U0086', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LIDOCAÍNA, CLORIDRATO + GLICOSE 50 + 75 MG/ML SOLUÇÃO INJETÁVEL 2 ML','BR0269848U0009', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LORATADINA 10 MG COMPRIMIDO','BR0273466U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LORATADINA 1 MG/ML XAROPE  100 ML','BR0273467U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LORATADINA 1 MG/ML XAROPE  120 ML','BR0273467U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LORATADINA 1 MG/ML XAROPE  60 ML','BR0273467U0110', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LOSARTANA POTÁSSICA 50 MG COMPRIMIDO','BR0268856U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LOVASTATINA 10 MG COMP','BR0268037U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LOVASTATINA 20 MG COMP','BR0268038U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'LOVASTATINA 40 MG COMP','BR0267092U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MAGNÉSIO, SULFATO 10 % SOLUÇÃO INJETÁVEL  10 ML','BR0268076U0004', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MAGNÉSIO, SULFATO 50 % SOLUÇÃO INJETÁVEL  10 ML','BR0268075U0004', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MAGNÉSIO, SULFATO 990 MG/G PÓ PARA SOLUÇÃO ORAL 15 G','BR0407030U0388', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MAGNÉSIO, SULFATO 990 MG/G PÓ PARA SOLUÇÃO ORAL 30 G','BR0407030U0053', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MAYTENUS ILICIFOLIA (ESPINHEIRA SANTA) 12 MG/ML SOLUÇÃO ORAL 100 ML','BR0399411U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MAYTENUS ILICIFOLIA (ESPINHEIRA SANTA) 380 MG CÁPSULA','BR0396450U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MAYTENUS ILICIFOLIA (ESPINHEIRA SANTA) 500 MG MG CÁPSULA','BR0399412U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MEDROXIPROGESTERONA, ACETATO 10 MG COMPRIMIDO','BR0271445U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MEDROXIPROGESTERONA, ACETATO 150 MG/ML SOLUÇÃO INJETÁVEL 1 ML','BR0292228U0005', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MEDROXIPROGESTERONA ACETATO, 150 MG/ML, SUSPENSÃO INJETÁVEL, AMPOLA 1 ML','BR0398702U0005', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MEDROXIPROGESTERONA, ACETATO 50 MG/ML SOLUÇÃO INJETÁVEL 1 ML','BR0292227U0005', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MEDROXIPROGESTERONA ACETATO, 50 MG/ML, SUSPENSÃO INJETÁVEL, AMPOLA 1 ML','BR0398703U0005', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MENTHA X PIPERITA L. (HORTELÃ) 300 MG CÁPSULA','BR0429853U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MESALAZINA 1000 MG SUPOS','BR0292240U0136', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MESALAZINA 1 G ENEMA 100 ML','BR0369175U0052', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MESALAZINA 250 MG SUPOS','BR0292239U0136', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MESALAZINA 3 G ENEMA 100 ML','BR0292242U0052', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MESALAZINA 400 MG COMP','BR0292237U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MESALAZINA 500 MG COMP','BR0292236U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MESALAZINA 500 MG SUPOS','BR0352414U0136', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MESALAZINA 800 MG COMP','BR0292238U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'METADONA 10 MG COMP','BR0268093U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'METADONA 10 MG/ML SOL INJ (AMP) 1 ML','BR0268094U0005', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'METADONA 5 MG COMP','BR0268092U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'METFORMINA, CLORIDRATO 500 MG COMPRIMIDO','BR0267690U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'METFORMINA, CLORIDRATO 850 MG COMPRIMIDO','BR0267691U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'METILDOPA  250 MG MG COMPRIMIDO','BR0267689U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'METILPREDNISOLONA 500 MG PÓ LIOF INJ (AMP)','BR0271599U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'METOCLOPRAMIDA, CLORIDRATO 10 MG COMPRIMIDO','BR0267312U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'METOCLOPRAMIDA, CLORIDRATO 4 MG/ML SOLUÇÃO ORAL 10 ML','BR0267311U0063', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'METOCLOPRAMIDA, CLORIDRATO 5 MG/ML SOLUÇÃO INJETÁVEL 1 ML','BR0267310U0005', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'METOCLOPRAMIDA, CLORIDRATO 5 MG/ML SOLUÇÃO INJETÁVEL 2 ML','BR0267310U0009', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'METOPROLOL, SUCCINATO 100 MG COMPRIMIDO DE LIBERAÇÃO PROLONGADA','BR0276658U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'METOPROLOL, SUCCINATO 25 MG COMPRIMIDO DE LIBERAÇÃO PROLONGADA','BR0276656U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'METOPROLOL, SUCCINATO 50 MG COMPRIMIDO DE LIBERAÇÃO PROLONGADA','BR0276657U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'METOPROLOL, TARTARATO 100 MG COMPRIMIDO','BR0394650U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'METOTREXATO 2,5 MG COMP','BR0270436U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'METOTREXATO 25 MG/ML SOL INJ (AMP) 2 ML','BR0292249U0391', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'METRONIDAZOL 100 MG/G GEL 50 G','BR0372335U0029', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'METRONIDAZOL 250 MG COMPRIMIDO','BR0267717U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'METRONIDAZOL 400 MG COMPRIMIDO','BR0268499U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'METRONIDAZOL (BENZOILMETRONIDAZOL) 40 MG/ML SUSPENSÃO ORAL  100 ML','BR0266863U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'METRONIDAZOL (BENZOILMETRONIDAZOL) 40 MG/ML SUSPENSÃO ORAL  120 ML','BR0266863U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'METRONIDAZOL (BENZOILMETRONIDAZOL) 40 MG/ML SUSPENSÃO ORAL  80 ML','BR0266863U0115', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MICOFENOLATO DE MOFETILA 500 MG COMP','BR0268143U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MICOFENOLATO DE SÓDIO 180 MG COMP','BR0288640U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MICOFENOLATO DE SÓDIO 360 MG COMP','BR0288641U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MICONAZOL, NITRATO 2 % CREME  28 G','BR0268286U0020', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MICONAZOL, NITRATO 2 % CREME  30 G','BR0268286U0023', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MICONAZOL, NITRATO 2 % CREME  80 G','BR0268286U0031', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MICONAZOL, NITRATO 2%  CREME VAGINAL  50G','BR0268162U0029', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MICONAZOL, NITRATO 2 % CREME VAGINAL  60 G','BR0268162U0030', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MICONAZOL, NITRATO 2 % CREME VAGINAL  80 G','BR0268162U0031', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MICONAZOL, NITRATO 2 % GEL 40 G','BR0268269U0027', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MICONAZOL, NITRATO 2 % PÓ 30 G','BR0268268U0096', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MIDAZOLAM, CLORIDRATO 2 MG/ML SOLUÇÃO ORAL 10 ML','BR0271556U0063', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MIGLUSTATE 100 MG CAP','BR0345001U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MIKANIA GLOMERATA (GUACO) 0,05 ML/ML XAROPE  100 ML','BR0395631U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MIKANIA GLOMERATA (GUACO) 0,05 ML/ML XAROPE  150 ML','BR0395631U0074', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MIKANIA GLOMERATA (GUACO) 0,1 ML/ML XAROPE  100 ML','BR0397280U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MIKANIA GLOMERATA (GUACO) 0,1 ML/ML XAROPE  120 ML','BR0397280U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MIKANIA GLOMERATA (GUACO) 0,25 ML/ML XAROPE  100 ML','BR0395728U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MIKANIA GLOMERATA (GUACO) 0,25 ML/ML XAROPE  120 ML','BR0395728U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MIKANIA GLOMERATA (GUACO) 117,6 MG/ML XAROPE  120 ML','BR0368779U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MIKANIA GLOMERATA (GUACO) 117,6 MG/ML XAROPE  150 ML','BR0368779U0074', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MIKANIA GLOMERATA (GUACO) 35 MG/ML XAROPE  100 ML','BR0433940U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MIKANIA GLOMERATA (GUACO) 35 MG/ML XAROPE  150 ML','BR0433940U0074', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MIKANIA GLOMERATA (GUACO) 60 MG/ML SOLUÇÃO ORAL 120 ML','BR0437668U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MIKANIA GLOMERATA (GUACO) 60 MG/ML XPE (FR) 100 ML','BR0437668U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MIKANIA GLOMERATA (GUACO) 81,5 MG/ML SOLUÇÃO ORAL 120 ML','BR0413681U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MISOPROSTOL 200 MCG COMPRIMIDO','BR0358755U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MISOPROSTOL 25 MCG COMPRIMIDO','BR0358753U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MORFINA 100 MG CAP LIBERAÇÃO CONTROLADA','BR0292262U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MORFINA 10 MG COMP','BR0271392U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MORFINA 10 MG/ML SOL INJ (AMP) 1 ML','BR0304871U0005', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MORFINA 10 MG/ML SOL ORAL (FR) 60 ML','BR0271394U0110', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MORFINA 30 MG CAP LIBERAÇÃO CONTROLADA','BR0292263U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MORFINA 30 MG COMP','BR0271391U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'MORFINA 60 MG CAP LIBERAÇÃO CONTROLADA','BR0292264U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'NALOXONA, CLORIDRATO 0,4 MG/ML SOLUÇÃO INJETÁVEL 1 ML','BR0272326U0005', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'NAPROXENO 250 MG COMP','BR0273702U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'NAPROXENO 500 MG COMP','BR0273703U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'NATALIZUMABE 300 MG SOL INJ (FR-AMP)','BR0367664U0075', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'NIFEDIPINO 10 MG CÁPSULA','BR0267728U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'NIFEDIPINO 10 MG COMPRIMIDO','BR0267728U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'NISTATINA 100.000 UI/ML SUSPENSÃO ORAL  120  ML','BR0267378U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'NISTATINA 100.000 UI/ML SUSPENSÃO ORAL  30 ML','BR0267378U0097', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'NISTATINA 100.000 UI/ML SUSPENSÃO ORAL  40  ML','BR0267378U0101', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'NISTATINA 100.000 UI/ML SUSPENSÃO ORAL  50  ML','BR0267378U0105', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'NISTATINA 100.000 UI/ML SUSPENSÃO ORAL  60  ML','BR0267378U0110', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'NITROFURANTOÍNA 100 MG CÁPSULA','BR0268273U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'NITROFURANTOÍNA 5 MG/ML SUSPENSÃO ORAL  120  ML','BR0305714U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'NOREPINEFRINA 2 MG/ML SOLUÇÃO INJETÁVEL 4  ML','BR0305718U0011', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'NORETISTERONA 0,35 MG COMPRIMIDO','BR0267733U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'NORETISTERONA, ENANTATO DE  + ESTRADIOL, VALERATO DE   50 + 5 MG/ML SOLUÇÃO INJETÁVEL 1 ML','BR0270846U0005', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'NORTRIPTILINA, CLORIDRATO 10 MG CÁPSULA','BR0271609U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'NORTRIPTILINA, CLORIDRATO 25 MG CÁPSULA','BR0271606U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'NORTRIPTILINA, CLORIDRATO 50 MG CÁPSULA','BR0271610U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'NORTRIPTILINA, CLORIDRATO 75 MG CÁPSULA','BR0271607U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'OCTREOTIDA 0,1 MG/ML SOL INJ (AMP)','BR0305725U0005', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'OCTREOTIDA LAR 10 MG PÓ P/ SUSP INJ (FR-AMP)','BR0342979U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'OCTREOTIDA LAR 20 MG PÓ P/ SUSP INJ (FR-AMP)','BR0342980U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'OCTREOTIDA LAR 30 MG PÓ P/ SUSP INJ (FR-AMP)','BR0342981U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'OLANZAPINA 10 MG COMP','BR0271621U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'OLANZAPINA 5 MG COMP','BR0271620U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÓLEO MINERAL (VASELINA LÍQUIDA / PETROLATO) SOLUÇÃO ORAL 100 ML','BR0233632U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÓLEO MINERAL (VASELINA LÍQUIDA / PETROLATO) SOLUÇÃO ORAL 120 ML','BR0233632U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÓLEO MINERAL (VASELINA LÍQUIDA / PETROLATO) SOLUÇÃO ORAL 200 ML','BR0233632U0085', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ÓLEO MINERAL (VASELINA LÍQUIDA / PETROLATO) SOLUÇÃO ORAL 500 ML','BR0233632U0103', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'OMBITASVIR/VERUPREVIR/RITONAVIR + DASABUVIR 12,5/75/50 MG + 250 MG CARTELA (04 COMP REV)','BR0435700U0032', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'OMEPRAZOL 10 MG CÁPSULA','BR0267711U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'OMEPRAZOL 20 MG CÁPSULA','BR0267712U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ONDANSETRONA, CLORIDRATO 4 MG COMPRIMIDO','BR0268506U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ONDANSETRONA, CLORIDRATO 8 MG COMPRIMIDO','BR0268505U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PAMIDRONATO 30 MG PÓ LIOF INJ (FR-AMP)','BR0268580U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PAMIDRONATO 60 MG PÓ LIOF INJ (FR-AMP)','BR0268581U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PANCREATINA 10000 UI CAP','BR0297775U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PANCREATINA 25000 UI CAP','BR0300745U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PARACETAMOL  200 MG/ML SOLUÇÃO ORAL 10 ML','BR0267777U0063', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PARACETAMOL  200 MG/ML SOLUÇÃO ORAL 15 ML','BR0267777U0075', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PARACETAMOL  200 MG/ML SOLUÇÃO ORAL 20 ML','BR0267777U0086', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PARACETAMOL  500 MG COMPRIMIDO','BR0267778U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PARICALCITOL 5 MCG/ML SOL INJ (AMP) 1 ML','BR0400476U0005', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PASTA DÁGUA PASTA  100 G','BR0274648U0239', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PASTA DÁGUA PASTA  30 G','BR0274648U0023', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PASTA DÁGUA PASTA  40 G','BR0274648U0027', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PASTA DÁGUA PASTA  80 G','BR0274648U0031', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PASTA DÁGUA PASTA  90 G','BR0274648U0344', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PENICILAMINA 250 MG CAP','BR0272815U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PERMANGANATO DE POTÁSSIO 100 MG COMPRIMIDO','BR0327699U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PERMETRINA 10 MG/ML LOÇÃO 100 ML','BR0267773U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PERMETRINA 10 MG/ML LOÇÃO 60 ML','BR0267773U0110', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PERMETRINA 50 MG/ML LOÇÃO 100 ML','BR0363597U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PERMETRINA 50 MG/ML LOÇÃO 60 ML','BR0363597U0110', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PERÓXIDO DE BENZOÍLA 2,5 % GEL 20 G','BR0384537U0018', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PERÓXIDO DE BENZOÍLA 2,5 % GEL 60 G','BR0384537U0030', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PERÓXIDO DE BENZOÍLA 5 % GEL 20 G','BR0366861U0018', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PERÓXIDO DE BENZOÍLA 5 % GEL 45 G','BR0366861U0028', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PERÓXIDO DE BENZOÍLA 5 % GEL 60 G','BR0366861U0030', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PILOCARPINA 20 MG/ML SOL OFT (FR) 10 MG','BR0271353U0063', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PILOCARPINA, CLORIDRATO 2 % SOLUÇÃO OFTÁLMICA 10 ML','BR0271353U0063', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PILOCARPINA, CLORIDRATO 2 % SOLUÇÃO OFTÁLMICA 15 ML','BR0271353U0075', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PILOCARPINA, CLORIDRATO 2 % SOLUÇÃO OFTÁLMICA 5 ML','BR0271353U0106', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PILOCARPINA, CLORIDRATO 2 % SOLUÇÃO OFTÁLMICA 5 ML','BR0271353U0106', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PIRIDOSTIGMINA 60 MG COMP','BR0271764U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PIRIDOXINA, CLORIDRATO 40 MG COMPRIMIDO','BR0267774U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PIRIMETAMINA 25 MG COMPRIMIDO ELENCO ESTADUAL','BR0268158U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PLANTAGO OVATA (PSYLLIUM) PÓ PARA DISPERSÃO ORAL 174 G','BR0396414U0334', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PLANTAGO OVATA (PSYLLIUM) PÓ PARA DISPERSÃO ORAL 3,5 G','BR0396414U0243', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PLANTAGO OVATA (PSYLLIUM) PÓ PARA DISPERSÃO ORAL 5,00 G','BR0396414U0541', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PRALIDOXIMA, MESILATO 200 MG PÓ LIOFILIZADO PARA SOLUÇÃO INJETÁVEL','BR0273668U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PRAMIPEXOL 0,125 MG COMP','BR0272824U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PRAMIPEXOL 0,25 MG COMP','BR0272825U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PRAMIPEXOL 1 MG COMP','BR0272826U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PRAVASTATINA 10 MG COMP','BR0268147U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PRAVASTATINA 20 MG COMP','BR0268148U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PRAVASTATINA 40 MG COMP','BR0271727U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PREDNISOLONA 1 MG/ML SOLUÇÃO ORAL 100 ML','BR0268151U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PREDNISOLONA 1 MG/ML SOLUÇÃO ORAL 120 ML','BR0268151U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PREDNISOLONA 1 MG/ML SOLUÇÃO ORAL 60 ML','BR0268151U0110', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PREDNISOLONA 3 MG/ML SOLUÇÃO ORAL 100 ML','BR0268150U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PREDNISOLONA 3 MG/ML SOLUÇÃO ORAL 120 ML','BR0268150U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PREDNISOLONA 3 MG/ML SOLUÇÃO ORAL 60 ML','BR0268150U0110', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PREDNISONA  20 MG COMPRIMIDO ELENCO ESTADUAL','BR0267743U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PREDNISONA  5 MG COMPRIMIDO ELENCO ESTADUAL','BR0267741U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PRESERVATIVO FEMININO, BORRACHA NITRÍLICA, COMPRIMENTO 17 CM, ESPESSURA DE 0,041 A 0,061 MM, ARGOLA FLEXÍVEL INTERNA E EXTERNA, SILICONIZADA,S/ODOR E MACIO','BR0356509U0140', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PRESERVATIVO FEMININO, POLIURETANO, COMPRIMENTO 17 CM, ESPESSURA DE 0,041 A 0,061 MM, ARGOLA FLEXÍVEL INTERNA E EXTERNA,SILICONIZADA,S/ODOR E MACIO','BR0325435U0140', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PRESERVATIVO MASCULINO BORRACHA NATURAL LUBRIFICADO 49MM','BR0325430U0140', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PRESERVATIVO MASCULINO LÁTEX LUBRIFICADO 52MM','BR0272914U0140', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PRILOCAÍNA + FELIPRESSINA 3 % + 0,03 UI/ML SOLUÇÃO INJETÁVEL 1,8 ML','BR0269833U0165', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PRIMIDONA 100 MG COMP','BR0319128U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PRIMIDONA 250 MG COMP','BR0278482U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PROMETAZINA, CLORIDRATO 25 MG COMPRIMIDO','BR0267768U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PROMETAZINA, CLORIDRATO 25 MG/ML SOLUÇÃO INJETÁVEL 2 ML','BR0267769U0009', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PROPAFENONA, CLORIDRATO 150 MG COMPRIMIDO','BR0384894U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PROPAFENONA, CLORIDRATO 300 MG COMPRIMIDO','BR0272412U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PROPILTIOURACILA 100 MG COMPRIMIDO','BR0273589U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PROPRANOLOL, CLORIDRATO 10 MG COMPRIMIDO','BR0267771U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PROPRANOLOL, CLORIDRATO 40 MG COMPRIMIDO','BR0267772U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'PROTAMINA, CLORIDRATO 10 MG/ML SOLUÇÃO INJETÁVEL 5 ML','BR0272362U0013', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'QUETIAPINA 100 MG COMP','BR0272832U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'QUETIAPINA 200 MG COMP','BR0272833U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'QUETIAPINA 25 MG COMP','BR0272831U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'QUETIAPINA 300 MG COMP','BR0390006U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RALOXIFENO 60 MG COMP','BR0272834U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RANITIDINA, CLORIDRATO  150 MG COMPRIMIDO ELENCO ESTADUAL','BR0267736U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RANITIDINA, CLORIDRATO  15 MG/ML XAROPE  120 ML','BR0267734U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RANITIDINA, CLORIDRATO  25 MG/ML SOLUÇÃO INJETÁVEL 2 ML','BR0267735U0009', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RETINOL, PALMITATO 150.000 UI/ML SOLUÇÃO ORAL 20 ML','BR0272565U0086', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RHAMNUS PURSHIANA (CÁSCARA SAGRADA) 10 MG/ML TINTURA 100 ML','BR0281453U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RHAMNUS PURSHIANA (CÁSCARA SAGRADA) 150 MG CÁPSULA','BR0399409U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RHAMNUS PURSHIANA (CÁSCARA SAGRADA) 380 MG CÁPSULA','BR0426292U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RHAMNUS PURSHIANA (CÁSCARA SAGRADA) 500 MG MG CÁPSULA','BR0399413U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RHAMNUS PURSHIANA (CÁSCARA SAGRADA) 75 MG CÁPSULA','BR0399636U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RIBAVIRINA 250 MG CAP','BR0272835U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RILUZOL 50 MG COMP','BR0272838U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RINGER LACTATO SÓDICO SOLUÇÃO INJETÁVEL 1000 ML SISTEMA FECHADO','BR0303292U0033', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RINGER LACTATO SÓDICO SOLUÇÃO INJETÁVEL 250 ML SISTEMA FECHADO','BR0303292U0037', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RINGER LACTATO SÓDICO SOLUÇÃO INJETÁVEL 500 ML SISTEMA FECHADO','BR0303292U0039', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RISEDRONATO 35 MG COMP','BR0296717U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RISEDRONATO 5 MG COMP','BR0296715U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RISPERIDONA 1 MG COMP','BR0272839U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RISPERIDONA 1 MG/ML SOL ORAL (FR) 30 ML','BR0284106U0097', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RISPERIDONA 2 MG COMP','BR0268149U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RISPERIDONA 3 MG COMP','BR0284105U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RITUXIMABE 500 MG SOL INJ (FR-AMP) 50 ML','BR0268520U0105', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RIVASTIGMINA 1,5 MG CAP','BR0267896U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RIVASTIGMINA 2 MG/ML SOL ORAL (FR) 120 ML','BR0266750U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RIVASTIGMINA 3 MG CAP','BR0267894U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RIVASTIGMINA 4,5 MG CAP','BR0267897U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'RIVASTIGMINA 6 MG CAP','BR0267895U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SACARATO DE HIDRÓXIDO FÉRRICO 100 MG SOL INJ (FR) 5 ML','BR0274989U0013', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SAIS PARA REIDRATAÇÃO ORAL (NaCl 3,5G + Glicose 20G + Citrato Na 2,9G + KCl 1,5G)                                 27,9 G PÓ PARA SOLUÇÃO','BR0268390U0052', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SALBUTAMOL, SULFATO 0,5 MG/ML SOLUÇÃO INJETÁVEL 1 ML','BR0268523U0005', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SALBUTAMOL, SULFATO 100 MCG/DOSE AEROSSOL 200 DOSES ELENCO ESTADUAL','BR0294887U0084', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SALBUTAMOL, SULFATO 5 MG/ML SOLUÇÃO PARA NEBULIZAÇÃO 10 ML','BR0268303U0063', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SALBUTAMOL, SULFATO 5 MG/ML SOLUÇÃO PARA NEBULIZAÇÃO 5 ML','BR0268303U0106', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SALMETEROL 50 MCG PÓ INAL OU AER BUCAL (FR DE 60 DOSES)','BR0266809U0109', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SCHINUS TEREBENTHIFOLIUS (AROEIRA) 0,67 ML/G CREME  10 G','BR0395716U0015', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SCHINUS TEREBENTHIFOLIUS (AROEIRA) 0,67 ML/G CREME  30 G','BR0395716U0023', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SCHINUS TEREBENTHIFOLIUS (AROEIRA) 0,67 ML/G CREME  60 G','BR0395716U0030', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SCHINUS TEREBENTHIFOLIUS (AROEIRA) 7% CREME VAGINAL  60 G','BR0434051U0030', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SELEGILINA 10 MG COMP','BR0268073U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SELEGILINA 5 MG COMP','BR0268072U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SERINGA PARA INSULINA, 1 ML, APIROGÊNICA E ATÓXICA, 100 UI COM BOA VISUALIZAÇÃO, DESCARTÁVEL, ESTÉRIL, AGULHA 13 X 3,8','BR0285040U0140', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SEVELAMER 800 MG COMP','BR0272083U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SILDENAFILA 20 MG COMP','BR0319883U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SILDENAFILA 25 MG COMP','BR0273820U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SILDENAFILA 50 MG COMP','BR0273821U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SIMEPREVIR 150 MG CAP','BR0431752U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SINVASTATINA  10 MG MG COMPRIMIDO','BR0267746U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SINVASTATINA  20 MG COMPRIMIDO','BR0267747U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SINVASTATINA  40 MG COMPRIMIDO','BR0267745U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SIROLIMO 1 MG DRÁGEA','BR0285817U0046', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SIROLIMO 2 MG DRÁGEA','BR0285818U0046', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SOFOSBUVIR 400 MG COMP REV','BR0431599U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SOMATROPINA 12 UI SOL INJ OU PÓ LIOF INJ (FR-AMP)','BR0266963U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SOMATROPINA 4 UI SOL INJ OU PÓ LIOF INJ (FR-AMP)','BR0266962U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SULFADIAZINA DE PRATA 1% CREME  30 G','BR0272089U0023', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SULFADIAZINA DE PRATA 1% CREME  500 G','BR0272089U0123', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SULFADIAZINA DE PRATA 1% CREME  50 G','BR0272089U0029', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SULFADIAZINA DE PRATA 1 % PASTA  100 G','BR0272089U0121', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SULFADIAZINA DE PRATA 1 % PASTA  400 G','BR0272089U0146', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SULFAMETOXAZOL + TRIMETOPRIMA  400 + 80 MG COMPRIMIDO ELENCO ESTADUAL','BR0308882U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SULFAMETOXAZOL + TRIMETOPRIMA  40 + 8 MG/ML SUSPENSÃO ORAL  100 ML ELENCO ESTADUAL','BR0308884U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SULFAMETOXAZOL + TRIMETOPRIMA  40 + 8 MG/ML SUSPENSÃO ORAL  50 ML','BR0308884U0105', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SULFAMETOXAZOL + TRIMETOPRIMA  40 + 8 MG/ML SUSPENSÃO ORAL  60 ML','BR0308884U0110', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SULFAMETOXAZOL + TRIMETOPRIMA  40MG/ML + 8MG/ML SUSPENSÃO ORAL  120 ML','BR0308884U0067', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SULFAMETOXAZOL + TRIMETOPRIMA  80MG/ML + 16MG/ML SOLUÇÃO INJETÁVEL 5 ML','BR0308885U0013', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SULFASSALAZINA 500 MG COMP','BR0268153U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SULFATO FERROSO (25MG/ML DE FERRO ELEMENTAR) 125 MG/ML SOLUÇÃO ORAL 30 ML ELENCO ESTADUAL','BR0292345U0097', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SULFATO FERROSO (25MG/ML DE FERRO ELEMENTAR) 125 MG/ML SOLUÇÃO ORAL 60 ML','BR0292345U0110', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SULFATO FERROSO (40MG DE FERRO ELEMENTAR) 109 MG COMPRIMIDO ELENCO ESTADUAL','BR0292344U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SULFATO FERROSO (5MG/ML DE FERRO ELEMENTAR) 25 MG/ML XAROPE  100 ML','BR0332468U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'SULFATO FERROSO (5MG/ML DE FERRO ELEMENTAR) 25 MG/ML XAROPE  60 ML','BR0332468U0110', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'TACROLIMO 1 MG CAP','BR0268098U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'TACROLIMO 5 MG CAP','BR0268097U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'TECLOZANA 10 MG/ML SUSPENSÃO ORAL  90 ML','BR0273618U0211', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'TECLOZANA 500 MG COMPRIMIDO','BR0273619U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'TENOFOVIR 300 MG COMP','BR0268825U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'TETRACICLINA, CLORIDRATO 500 MG COMPRIMIDO','BR0267393U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'TIAMINA, CLORIDRATO 300 MG COMPRIMIDO','BR0272341U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'TIMOLOL 5 MG/ML SOL OFT (FR) 5 ML','BR0272581U0106', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'TIMOLOL, MALEATO 0,25 % SOLUÇÃO OFTÁLMICA 5 ML ELENCO ESTADUAL','BR0272582U0106', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'TIMOLOL, MALEATO 0,5 % SOLUÇÃO OFTÁLMICA 5 ML','BR0272581U0106', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'TIRA REAGENTE PARA MEDIR GLICEMIA CAPILAR','BR0339565U0140', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'TOCILIZUMABE 20 MG/ML SOL INJ (FR-AMP) 4 ML','BR0388383U0162', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'TOLCAPONA 100 MG COMP','BR0272847U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'TOPIRAMATO 100 MG COMP','BR0272851U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'TOPIRAMATO 25 MG COMP','BR0272849U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'TOPIRAMATO 50 MG COMP','BR0272850U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'TOXINA BOTULINICA TIPO A 100 U PÓ LIOF INJ (FR-AMP)','BR0292372U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'TOXINA BOTULINICA TIPO A 500 U PÓ LIOF INJ (FR-AMP)','BR0292373U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'TRAVOPROSTA 0,04 MG/ML SOL OFT (FR) 2,5 ML','BR0268005U0081', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'TRIEXIFENIDIL 5 MG COMP','BR0272852U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'TRIPTORRELINA 11,25 MG SUSP INJ (FR-AMP)','BR0342680U0118', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'TRIPTORRELINA 3,75 MG SUSP INJ (FR-AMP)','BR0342682U0135', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'UNCARIA TOMENTOSA (UNHA DE GATO) 100 MG CÁPSULA','BR0399442U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'UNCARIA TOMENTOSA (UNHA DE GATO) 450MG CÁPSULA','BR0432210U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'UNCARIA TOMENTOSA (UNHA DE GATO) 50 MG/G GEL 5 G','BR0399441U0233', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'VARFARINA SÓDICA  1 MG COMPRIMIDO','BR0279271U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'VARFARINA SÓDICA  5 MG COMPRIMIDO','BR0279269U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'VERAPAMIL, CLORIDRATO 120 MG COMPRIMIDO','BR0267573U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'VERAPAMIL, CLORIDRATO 2,5 MG/ML SOLUÇÃO INJETÁVEL 2 ML','BR0267424U0009', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'VERAPAMIL, CLORIDRATO 80 MG COMPRIMIDO','BR0267425U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'VIGABATRINA 500 MG COMP','BR0272853U0042', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ZINCO, SULFATO 200MCG INJ (FR)','BR0311465U0013', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ZINCO, SULFATO 4 MG/ML XAROPE  100 ML','BR0433249U0062', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ZIPRASIDONA 40 MG CAP','BR0342319U0041', 0);
INSERT INTO medicamento_catmat VALUES (nextval('seq_gem'), 'ZIPRASIDONA 80 MG CAP','BR0342320U0041', 0);

ALTER TABLE medicamento_catmat ENABLE TRIGGER USER;

ALTER TABLE produtos ADD COLUMN cd_medicamento_catmat int8;
ALTER TABLE auditschema.produtos ADD COLUMN cd_medicamento_catmat int8;

ALTER TABLE produtos
        ADD CONSTRAINT FK_PROD_REF_MEDI_CATMAT FOREIGN KEY (cd_medicamento_catmat)
        REFERENCES medicamento_catmat (cd_medicamento_catmat)
        ON DELETE RESTRICT ON UPDATE RESTRICT;


/*
    Everton - 27/04/2018
*/
drop index if exists idx_estoque_empresa_cod_pro;
create index idx_estoque_empresa_cod_pro on estoque_empresa (cod_pro);

/*
    Elton   - 18390 - 27/04/2018
*/
alter table atendimento_prontuario add cd_dor_cronica INT8 null;
alter table auditschema.atendimento_prontuario add cd_dor_cronica INT8 null;
alter table atendimento_prontuario
       add constraint FK_ATE_PRONT_REF_DOR_CRO foreign key (cd_dor_cronica)
          references dor_cronica (cd_dor_cronica)
          on delete restrict on update restrict;