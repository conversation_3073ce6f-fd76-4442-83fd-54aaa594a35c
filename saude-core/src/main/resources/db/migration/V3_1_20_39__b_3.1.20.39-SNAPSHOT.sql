SET application_name = 'flyway|3.1.20.39';

/*
    Silvio - 05/08/2019 #25812
*/
alter table encaminhamento add column dt_desejada timestamp;
alter table auditschema.encaminhamento add column dt_desejada timestamp;

/*
    Elton - 06/08/2019 #25639
*/

create table indisponibilidade_prof_unidade (
	cd_indisponib_prof_unidade  int8            not null,
    cd_profissional_solic       int8            null,
	empresa                     int8            null,
	dt_inicial                  date     		not null,
	dt_final                    date            not null,
	hora_inicial	            time            not null,
	hora_final            		time            not null,
	motivo_indisponibilidade    varchar(500)    not null,
	motivo_cancelamento      	varchar         null,
	situacao					int2            null,
	dt_cadastro	          		date            not null,
	dt_usuario            		date            not null,
    cd_usuario            		int8            not null,
	version               		int8            not null
);

alter table indisponibilidade_prof_unidade add constraint PK_INDISPONIB_PROF_UNIDADE primary key (cd_indisponib_prof_unidade);
alter table indisponibilidade_prof_unidade add constraint FK_INDISP_PROF_UNIDADE_EMPRESA foreign key (empresa) references empresa (empresa) on delete restrict on update restrict;
alter table indisponibilidade_prof_unidade add constraint FK_INDISP_PROF_UNIDADE_PROFISS foreign key (cd_profissional_solic) references profissional (cd_profissional) on delete restrict on update restrict;
alter table indisponibilidade_prof_unidade add constraint FK_INDISP_PROF_UNIDADE_USUARIO foreign key (cd_usuario) references usuarios (cd_usuario) on delete restrict on update restrict;

CREATE TABLE auditschema.indisponibilidade_prof_unidade AS SELECT t2.*, t1.* FROM indisponibilidade_prof_unidade t1, audit_temp t2 WHERE 1=2;
create sequence seq_audit_id_indisponibilidade_prof_unidade;alter table auditschema.indisponibilidade_prof_unidade add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON indisponibilidade_prof_unidade FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

create sequence seq_indisponibilidade_prof_unidade;

INSERT INTO programa_pagina VALUES (1724, 'br.com.celk.view.agenda.indisponibilidadeprofissionalunidade.ConsultaIndisponibilidadeProfissionalUnidadePage', 'N');
INSERT INTO programa_pagina VALUES (1725, 'br.com.celk.view.agenda.indisponibilidadeprofissionalunidade.CadastroIndisponibilidadeProfissionalUnidadePage', 'N');
INSERT INTO programa_web VALUES (956, 'Indisponibilidade de Profissionais e Unidades', 1724, 'N');
INSERT INTO programa_web_pagina VALUES (1840, 956, 1724);
INSERT INTO programa_web_pagina VALUES (1841, 956, 1725);
INSERT INTO menu_web VALUES (1208, 'Indisponibilidade de Profissionais e Unidades', 'indisponibilidadeProfissionalUnidade', 473, 956, 3, 0, 0);

/*
    Silvio - 16/08/2019 #26885
*/
alter table cid_classificacao add column prazo_encerramento INT4 null;
alter table auditschema.cid_classificacao add column prazo_encerramento INT4 null;

alter table registro_agravo add column escolaridade_equivalencia INT2 null;
alter table auditschema.registro_agravo add column escolaridade_equivalencia INT2 null;

/*
    Elton - 25704 - 05/08/2019
*/

alter table tipo_procedimento add column dias_validacao_vagas_internas int2;
alter table auditschema.tipo_procedimento add column dias_validacao_vagas_internas int2;

/*
    Sulivan - 31/08/2019 - #25704
*/
INSERT INTO programa_pagina_permissao VALUES(526, 123, 541, 0, 'agendaVagaInterna');

/*
    Sulivan - 01/08/2019 #27052
*/
alter table exame_bpai add column flag_enviar_regulacao INT2;
alter table auditschema.exame_bpai add column flag_enviar_regulacao INT2;
alter table exame_bpai disable trigger user;
update exame_bpai set flag_enviar_regulacao = 0;
alter table exame_bpai alter column flag_enviar_regulacao set not null;
alter table exame_bpai enable trigger user;

alter table exame_bpai add column ds_enviar_regulacao VARCHAR;
alter table auditschema.exame_bpai add column ds_enviar_regulacao VARCHAR;

alter table mamografia_requisicao add column flag_enviar_regulacao INT2;
alter table auditschema.mamografia_requisicao add column flag_enviar_regulacao INT2;
alter table mamografia_requisicao disable trigger user;
update mamografia_requisicao set flag_enviar_regulacao = 0;
alter table mamografia_requisicao alter column flag_enviar_regulacao set not null;
alter table mamografia_requisicao enable trigger user;

alter table mamografia_requisicao add column ds_enviar_regulacao VARCHAR;
alter table auditschema.mamografia_requisicao add column ds_enviar_regulacao VARCHAR;