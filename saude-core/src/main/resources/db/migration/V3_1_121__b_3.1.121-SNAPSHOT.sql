SET application_name = 'flyway|3.1.121';

SET statement_timeout TO 600000;

/*
    Alexandre - 09/02/2022 - GMT-666
*/

INSERT INTO permissao_web VALUES (166, 'Impressão Etiqueta Nota Fiscal', 0);
INSERT INTO programa_pagina_permissao VALUES(590, 166, 163, 0, '');

/*
    Saldanha - REG-1011 - 09/02/2022
*/

alter table aih add cota_direta varchar null;
alter table auditschema.aih add cota_direta varchar null;


/*
<PERSON><PERSON> - REG-1019 - 02/03/2022
*/

alter table solicit_mudanca_procedimento add dt_analise timestamp null;
alter table auditschema.solicit_mudanca_procedimento add dt_analise timestamp null;

alter table solicit_mudanca_procedimento add status int8 null;
alter table auditschema.solicit_mudanca_procedimento add status int8 null;

alter table solicit_mudanca_procedimento add cd_estabelecimento_solicitante int8 null;
alter table auditschema.solicit_mudanca_procedimento add cd_estabelecimento_solicitante int8 null;

ALTER TABLE solicit_mudanca_procedimento
        ADD CONSTRAINT fk_solicit_mudanca_procedimento_ref_empresa FOREIGN KEY(cd_estabelecimento_solicitante)
        REFERENCES empresa (empresa)
        ON DELETE RESTRICT ON UPDATE RESTRICT;

alter table solicit_mudanca_procedimento add cd_usuario_analise int8 null;
alter table auditschema.solicit_mudanca_procedimento add cd_usuario_analise int8 null;

ALTER TABLE solicit_mudanca_procedimento
        ADD CONSTRAINT fk_solicit_mudanca_procedimento_ref_usuario FOREIGN KEY(cd_usuario_analise)
        REFERENCES usuarios (cd_usuario)
        ON DELETE RESTRICT ON UPDATE RESTRICT;

insert into programa_pagina values (1923, 'br.com.celk.view.hospital.aih.AnalisarRegulacaoAihPage', 'N');
insert into programa_web_pagina values (2017, 1034, 1923);


/*
Matheus de Oliveira Saldanha - REG-1031 - 02/03/2022
*/


CREATE TABLE IF NOT EXISTS public.solicitacao_agendamento_anexo
(
    cd_solicitacao_agendamento_anexo bigint NOT NULL,
    cd_solicitacao_agendamento bigint not null,
    cd_gerenciador_arquivo bigint not null,
    version bigint,
    CONSTRAINT pk_solicitacao_agendamento_anexo PRIMARY KEY (cd_solicitacao_agendamento_anexo),
    CONSTRAINT fk_solicitacao_agendamento_anexo_ref_solicitacao_agendamento FOREIGN KEY (cd_solicitacao_agendamento)
        REFERENCES public.solicitacao_agendamento (cd_solicitacao) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT,
    CONSTRAINT fk_solicitacao_agendamento_anexo_ref_ger_arquivo FOREIGN KEY (cd_gerenciador_arquivo)
        REFERENCES public.gerenciador_arquivo (cd_gerenciador_arquivo) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT
);

CREATE TABLE auditschema.solicitacao_agendamento_anexo
AS SELECT t2.*, t1.*
FROM solicitacao_agendamento_anexo t1, audit_temp t2
WHERE 1=2;
create sequence seq_audit_id_solicitacao_agendamento_anexo;
alter table auditschema.solicitacao_agendamento_anexo add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON solicitacao_agendamento_anexo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

/*
Letícia Fernandes - REG-1053 - 24/02/2022
*/

/*numeração de acordo com o banco de constantes do saude*/

insert into programa_web_pagina values (2007, 1034, 1876);

insert into programa_pagina values (1921, 'br.com.celk.view.hospital.aih.AutorizacaoDoLeitoPage', 'N');

insert into programa_web_pagina values (2015, 1034, 1921);

/*
    Danubio Ferrari - FEATURE AMB-2093 - 09/02/2022
*/
/*==============================================================*/
/* Table: usuario_cadsus_motivo_cpf                             */
/*==============================================================*/

CREATE TABLE public.usuario_cadsus_motivo_cpf (
cd_motivo_cpf    int8        NOT NULL,
descricao		 varchar(255)NOT NULL,
ativo            int8        NOT NULL,
"version"        int8        NULL,
CONSTRAINT pk_usuario_cadsus_motivo_cpf PRIMARY KEY (cd_motivo_cpf)
);

CREATE SEQUENCE seq_usuario_cadsus_motivo_cpf INCREMENT 1 START 1;
CREATE TABLE auditschema.usuario_cadsus_motivo_cpf AS SELECT t2.*, t1.* FROM usuario_cadsus_motivo_cpf t1, audit_temp t2 WHERE 1=2;
CREATE sequence seq_audit_id_usuario_cadsus_motivo_cpf;
ALTER TABLE auditschema.usuario_cadsus_motivo_cpf
ADD PRIMARY KEY (audit_id);
CREATE trigger emp_audit AFTER INSERT OR UPDATE OR DELETE ON public.usuario_cadsus_motivo_cpf for each ROW EXECUTE PROCEDURE process_emp_audit();

ALTER TABLE public.usuario_cadsus  ADD cd_motivo_cpf    int8;
ALTER TABLE public.usuario_cadsus ADD CONSTRAINT fk_usu_cad_esus_cd_motivo_cpf FOREIGN KEY (cd_motivo_cpf) REFERENCES public.usuario_cadsus_motivo_cpf(cd_motivo_cpf);
ALTER TABLE auditschema.usuario_cadsus  ADD cd_motivo_cpf    int8;

INSERT INTO usuario_cadsus_motivo_cpf VALUES (1, 'Criança',1,0);
INSERT INTO usuario_cadsus_motivo_cpf VALUES (2, 'Com Transtorno Mental',1,0);
INSERT INTO usuario_cadsus_motivo_cpf VALUES (3, 'Estrangeiro',1,0);
INSERT INTO usuario_cadsus_motivo_cpf VALUES (4, 'Não Domiciliado (morador de rua)',1,0);
INSERT INTO usuario_cadsus_motivo_cpf VALUES (5, 'Urgência/emergência',1,0);



/*
    Roberta - CSF-371
*/

alter table vac_aplicacao ADD COLUMN IF NOT EXISTS version_all int8;

alter table auditschema.vac_aplicacao ADD COLUMN IF NOT EXISTS version_all int8;

create trigger tg_version_all before
insert
or
update
on
public.vac_aplicacao for each row execute procedure ftg_version_all();

CREATE SEQUENCE public.seq_version_vac_aplicacao
INCREMENT BY 1
MINVALUE 1
MAXVALUE 9223372036854775807
START 1
CACHE 1
NO CYCLE;