SET application_name = 'flyway|3.0.71.14';

/*
    Sulivan - 06/12/2017
*/
CREATE TABLE fabricante (
    cd_fabricante       int8            not null,
    descricao           varchar(60)     not null,
    dt_cadastro		    timestamp	    not null,
    cd_usuario          NUMERIC(6)      not null,
    version             int8            not null
);

ALTER TABLE fabricante
        ADD CONSTRAINT PK_FABRICANTE PRIMARY KEY (cd_fabricante);

ALTER TABLE fabricante
        ADD CONSTRAINT FK_FABRICANTE_REF_USUARIO FOREIGN KEY (cd_usuario)
        REFERENCES usuarios (cd_usuario)
        ON DELETE RESTRICT ON UPDATE RESTRICT;

CREATE TABLE auditschema.fabricante AS SELECT t2.*, t1.* FROM fabricante t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_fabricante;alter table auditschema.fabricante add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON fabricante FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

INSERT INTO programa_pagina VALUES (1474, 'br.com.celk.view.basico.fabricante.ConsultaFabricantePage', 'N');
INSERT INTO programa_pagina VALUES (1475, 'br.com.celk.view.basico.fabricante.CadastroFabricantePage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (811, 'Fabricantes', 1474, 'N');
INSERT INTO programa_web_pagina VALUES (1552, 811, 1474);
INSERT INTO programa_web_pagina VALUES (1553, 811, 1475);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1053,'Fabricantes','fabricantes',166,811,14,0,0);

ALTER TABLE movimento_estoque ADD COLUMN justificativa_lote_posterior varchar(200);
ALTER TABLE auditschema.movimento_estoque ADD COLUMN justificativa_lote_posterior varchar(200);
ALTER TABLE pedido_transferencia_item_lote ADD COLUMN justificativa_lote_posterior varchar(200);
ALTER TABLE auditschema.pedido_transferencia_item_lote ADD COLUMN justificativa_lote_posterior varchar(200);

/*
    Laudecir - 14/12/2017
*/
alter table subgrupo alter descricao type varchar(70);
alter table auditschema.subgrupo alter descricao type varchar(70);

/*
    Maicon - 19/12/2017
*/

update solicitacao_agendamento t1
set status = 6, dt_fechamento = '2017-12-19 00:00:00'
where exists (select 1 from im_solic_agend_to_exame_bpai t2
		left join exame_bpai t3 on (t2.cd_exame_bpai =  t3.cd_exame_bpai)
		left join exame t4 on (t3.cd_exame = t4.cd_exame)
		where t1.cd_solicitacao = t2.cd_solicitacao
		and t4.status = 7
	)
and t1.status <> 6;

update solicitacao_agendamento t1
set status = 6, dt_fechamento = '2017-12-19 00:00:00'
where exists (select 1 from im_solic_agend_to_exame_apac t2
		left join exame_apac t3 on (t2.cd_exame_apac =  t3.cd_exame_apac)
		left join exame t4 on (t3.cd_exame = t4.cd_exame)
		where t1.cd_solicitacao = t2.cd_solicitacao
		and t4.status = 7
	)
and t1.status <> 6;

/*
   Hilton - 08/12/2017 - #16786
*/
alter table integracao_mobile add column versao_mobile varchar;
alter table auditschema.integracao_mobile add column versao_mobile varchar;

/*
   Hilton - 15/12/2017 - #16786
*/

alter table integracao_mobile_item add column status int2;
alter table auditschema.integracao_mobile_item add column status int2;

/*
   Joel - 21/12/2017 - #16786
*/

alter table integracao_mobile_item disable trigger user;
update integracao_mobile_item set status = 0;
alter table integracao_mobile_item enable trigger user;


/*
   Everton - 22/12/2017 - Geração registro para acertar problema na sincronização do tablet.
*/

insert into endereco_domicilio_esus (cd_end_dom_esus, cd_domicilio, tp_imovel, situacao_moradia, localizacao, version)
  SELECT nextval('seq_gem'), cd_domicilio, 1, 75, 83, 0 FROM usuario_cadsus t1
   where t1.cd_domicilio is not null
     and not exists (select 1 from endereco_domicilio_esus where cd_domicilio = t1.cd_domicilio)
  group by 2,3,4,5,6;

/*
   Maicon - 22/12/2017 - Importação prontuários Picada Café.
*/
alter table atendimento_prontuario alter column desc_tipo_atendimento type varchar(150);
alter table atendimento_prontuario alter column ds_tp_atend_princ_extern type varchar(150);

alter table auditschema.atendimento_prontuario alter column desc_tipo_atendimento type varchar(150);
alter table auditschema.atendimento_prontuario alter column ds_tp_atend_princ_extern type varchar(150);

/*
   Sulivan - 22/12/2017
*/
alter table controle_inventario add column laboratorio_fabricante VARCHAR(100);
alter table auditschema.controle_inventario add column laboratorio_fabricante VARCHAR(100);
alter table movimento_estoque add column laboratorio_fabricante VARCHAR(100);
alter table auditschema.movimento_estoque add column laboratorio_fabricante VARCHAR(100);