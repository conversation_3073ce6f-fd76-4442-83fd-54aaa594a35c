/*
    Felipe - 01/04/2013
*/
insert into programa_pagina values(414, 'br.com.celk.view.atendimento.prontuario.medico.ConsultaAtendimentoMedicoPage', 'N');
insert into programa_pagina values(415, 'br.com.celk.view.atendimento.prontuario.medico.ProntuarioMedicoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (251,'Atendimento Médico',414,'N');
insert into programa_web_pagina values (415, 251, 414);
insert into programa_web_pagina values (416, 251, 415);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (349,'Atendimento Médico','atendimentoMedico',182,251,143);

/*
    Felipe - 02/04/2013
*/
insert into programa_pagina values(416, 'br.com.celk.view.atendimento.prontuario.DadosConsultaMedicaPage', 'N');
insert into programa_web_pagina values (417, 251, 416);

/*
    Leandro - 04/04/2013
*/
/*==============================================================*/
/* Table: ficha_acolhimento                                     */
/*==============================================================*/
create table ficha_acolhimento (
cd_ficha_acolhimento INT8                 not null,
nr_atendimento       INT8                 null,
tipo_encaminhamento  INT2                 not null,
somatorio_historico_saude INT8                 not null,
tempo_tratamento     VARCHAR(50)          null,
frequentou_outros_servicos VARCHAR(100)         null,
tempo_freq_outros_servicos VARCHAR(50)          null,
consultou_psiquiatra_outros INT2                 not null,
servico_psiquiatra   VARCHAR(50)          null,
consultou_neurologista INT2                 not null,
    exames_realizados_neuro VARCHAR(50)          null,
cirurgias_feitas     VARCHAR(100)         null,
outros_dados_hist_saude VARCHAR(100)         null,
relacoes_trabalho    VARCHAR              null,
como_comecou         VARCHAR(100)         null,
com_o_que_comecou    VARCHAR(100)         null,
quando_usa           VARCHAR(100)         null,
pra_que_usa          VARCHAR(100)         null,
acompanhado          VARCHAR(100)         null,
onde_usa             VARCHAR(100)         null,
reacoes_substancia   VARCHAR(100)         null,
nota_historia_uso    varchar              null,
quantas_internacoes  varchar(100)         null,
onde_internacao      varchar(100)         null,
quando_internacao    VARCHAR(100)         null,
observacao_estado_mental VARCHAR              null,
avaliacao_motivacional VARCHAR              null,
avaliacao_testagem   VARCHAR              null,
plano_terapeutico    INT2                 not null,
observacao_atividades_terapeuti VARCHAR              null,
version              INT8                 not null
);

alter table ficha_acolhimento
   add constraint PK_FICHA_ACOLHIMENTO primary key (cd_ficha_acolhimento);

alter table ficha_acolhimento
   add constraint FK_FICHA_ACOLH_REF_ATENDIM foreign key (nr_atendimento)
      references atendimento (nr_atendimento)
      on delete restrict on update restrict;

/*==============================================================*/
/* Table: uso_medicacao                                         */
/*==============================================================*/
create table uso_medicacao (
cd_medicacao         INT8                 not null,
cd_ficha_acolhimento INT8                 not null,
medicacao            VARCHAR(50)          not null,
posologia            VARCHAR(50)          not null,
version              INT8                 not null
);

alter table uso_medicacao
   add constraint PK_USO_MEDICACAO primary key (cd_medicacao);

alter table uso_medicacao
   add constraint FK_MEDIC_REF_FICH_ACOLH foreign key (cd_ficha_acolhimento)
      references ficha_acolhimento (cd_ficha_acolhimento)
      on delete restrict on update restrict;

/*==============================================================*/
/* Table: atividade_terapeutica                                 */
/*==============================================================*/
create table atividade_terapeutica (
cd_atividade_terapeutica INT8                 not null,
cd_ficha_acolhimento INT8                 not null,
dia_da_semana        INT2                 not null,
ds_atividade_terapeutica VARCHAR(100)         not null,
version              INT8                 not null
);

alter table atividade_terapeutica
   add constraint PK_ATIVIDADE_TERAPEUTICA primary key (cd_atividade_terapeutica);

alter table atividade_terapeutica
   add constraint FK_ATIVIDADE_REF_ACOLHIM foreign key (cd_ficha_acolhimento)
      references ficha_acolhimento (cd_ficha_acolhimento)
      on delete restrict on update restrict;

/*==============================================================*/
/* Table: ecomapa_item                                          */
/*==============================================================*/
create table ecomapa_item (
cd_ecomapa_item      INT8                 not null,
cd_ficha_acolhimento INT8                 not null,
tipo_ecomapa_item    VARCHAR(50)          not null,
ds_ecomapa_item      VARCHAR(500)         not null,
version              INT8                 not null
);

alter table ecomapa_item
   add constraint PK_ECOMAPA_ITEM primary key (cd_ecomapa_item);

alter table ecomapa_item
   add constraint FK_ECOMAPA_REF_ACOLHIM foreign key (cd_ficha_acolhimento)
      references ficha_acolhimento (cd_ficha_acolhimento)
      on delete restrict on update restrict;

/*==============================================================*/
/* Table: relacao_familiar_acolhimento                          */
/*==============================================================*/
create table relacao_familiar_acolhimento (
cd_rel_familiar_acolhim INT8                 not null,
cd_ficha_acolhimento INT8                 not null,
cd_usu_cadsus        NUMERIC(8)           not null,
grau_parentesco      VARCHAR(50)          not null,
version              INT8                 not null
);

alter table relacao_familiar_acolhimento
   add constraint PK_RELACAO_FAMILIAR_ACOLHIMENT primary key (cd_rel_familiar_acolhim);

alter table relacao_familiar_acolhimento
   add constraint FK_REL_FAM_REF_FICH_ACOLH foreign key (cd_ficha_acolhimento)
      references ficha_acolhimento (cd_ficha_acolhimento)
      on delete restrict on update restrict;

alter table relacao_familiar_acolhimento
   add constraint FK_REL_FAM_REF_PACIENTE foreign key (cd_usu_cadsus)
      references usuario_cadsus (cd_usu_cadsus)
      on delete restrict on update restrict;

/*==============================================================*/
/* Table: droga_uso                                             */
/*==============================================================*/
create table droga_uso (
cd_droga_uso         INT8                 not null,
cd_ficha_acolhimento INT8                 not null,
tipo_droga           INT2                 not null,
outro_descricao      VARCHAR(50)          null,
ultimo_uso           VARCHAR(100)         null,
quantidade_uso       VARCHAR(100)         null,
tempo_uso            VARCHAR(100)         null,
overdose             VARCHAR(100)         null,
version              INT8                 not null
);

alter table droga_uso
   add constraint PK_DROGA_USO primary key (cd_droga_uso);

alter table droga_uso
   add constraint FK_DROGA_REF_FICHA_ACOLH foreign key (cd_ficha_acolhimento)
      references ficha_acolhimento (cd_ficha_acolhimento)
      on delete restrict on update restrict;

/*==============================================================*/
/* Table: tipo_sintoma                                          */
/*==============================================================*/
create table tipo_sintoma (
cd_tipo_sintoma      INT8                 not null,
ds_tipo_sintoma      VARCHAR(50)          not null,
dica_resposta        VARCHAR(100)         null,
armazena_sugestao    INT2                 not null,
version              INT8                 not null
);

alter table tipo_sintoma
   add constraint PK_TIPO_SINTOMA primary key (cd_tipo_sintoma);

/*==============================================================*/
/* Table: sugestao_sintoma                                      */
/*==============================================================*/
create table sugestao_sintoma (
cd_sugestao_sintoma  INT8                 not null,
cd_tipo_sintoma      INT8                 not null,
ds_sugestao_sintoma  VARCHAR(50)          not null,
version              INT8                 not null
);

alter table sugestao_sintoma
   add constraint PK_SUGESTAO_SINTOMA primary key (cd_sugestao_sintoma);

alter table sugestao_sintoma
   add constraint FK_SUGEST_SINT_REF_TP_SINT foreign key (cd_tipo_sintoma)
      references tipo_sintoma (cd_tipo_sintoma)
      on delete restrict on update restrict;

/*==============================================================*/
/* Table: sintoma                                               */
/*==============================================================*/
create table sintoma (
cd_sintoma           INT8                 not null,
cd_ficha_acolhimento INT8                 not null,
cd_tipo_sintoma      INT8                 not null,
sintoma              VARCHAR(50)          null,
version              INT8                 not null
);

alter table sintoma
   add constraint PK_SINTOMA primary key (cd_sintoma);

alter table sintoma
   add constraint FK_SINTOMA_REF_FICH_ACOLHIM foreign key (cd_ficha_acolhimento)
      references ficha_acolhimento (cd_ficha_acolhimento)
      on delete restrict on update restrict;

alter table sintoma
   add constraint FK_SINTOMA_REF_TP_SINTOMA foreign key (cd_tipo_sintoma)
      references tipo_sintoma (cd_tipo_sintoma)
      on delete restrict on update restrict;

/*
    Leandro - 05/04/2013
*/
alter table usuario_cadsus add religiao VARCHAR(50) null;
alter table usuario_cadsus add local_trabalho VARCHAR(50) null;
alter table usuario_cadsus add telefone_trabalho VARCHAR(15) null;
alter table usuario_cadsus add responsavel VARCHAR(70) null;
alter table usuario_cadsus add parentesco_responsavel VARCHAR(20) null;
alter table usuario_cadsus add urgencia_chamar VARCHAR(70) null;
alter table usuario_cadsus add telefone_urgencia VARCHAR(15) null;
alter table usuario_cadsus add grau_parentesco_urgencia VARCHAR(20) null;

alter table endereco_usuario_cadsus add ponto_referencia VARCHAR(100) null;
alter table endereco_usuario_cadsus add empresa INT4 null;

alter table endereco_usuario_cadsus
   add constraint FK_END_CADSUS_REF_EMPRESA foreign key (empresa)
      references empresa (empresa)
      on delete restrict on update restrict;

/*
    Felipe - 08/04/2013
*/
insert into programa_pagina values(419, 'br.com.celk.view.atendimento.prontuario.enfermagem.ConsultaAtendimentoEnfermagemPage', 'N');
insert into programa_pagina values(420, 'br.com.celk.view.atendimento.prontuario.enfermagem.ProntuarioEnfermagemPage', 'N');
insert into programa_pagina values(421, 'br.com.celk.view.atendimento.prontuario.DadosConsultaEnfermagemPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (253,'Atendimento de Enfermagem',419,'N');
insert into programa_web_pagina values (420, 253, 419);
insert into programa_web_pagina values (421, 253, 420);
insert into programa_web_pagina values (422, 253, 421);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (351,'Atendimento de Enfermagem','atendimentoEnfermagem',182,253,143);