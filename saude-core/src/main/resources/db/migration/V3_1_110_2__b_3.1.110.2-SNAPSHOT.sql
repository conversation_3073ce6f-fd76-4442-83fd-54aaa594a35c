SET application_name = 'flyway|*********';

SET statement_timeout TO 600000;

/*
    <PERSON> - AMB-1993 - 30/11/2021
*/
ALTER TABLE usuario_empresas ADD COLUMN cd_equipe_faturamento INT8;
ALTER TABLE auditschema.usuario_empresas ADD COLUMN cd_equipe_faturamento INT8;

WITH legado_equipe_fat AS
    (SELECT cd_usuario_empresa,
            ue.empresa_bpa,
            u.cd_profissional
     FROM usuarios u
     LEFT JOIN usuario_empresas ue ON u.cd_usuario = ue.cd_usuario
     WHERE ue.empresa_bpa IS NOT NULL
         AND u.cd_profissional IS NOT NULL)
UPDATE usuario_empresas uemp
SET cd_equipe_faturamento =
    ( SELECT e.cd_equipe
     FROM equipe_profissional ep
     JOIN profissional p ON p.cd_profissional = ep.cd_profissional
     JOIN equipe e ON e.cd_equipe = ep.cd_equipe
     WHERE p.cd_profissional=legado_equipe_fat.cd_profissional
         AND e.empresa = legado_equipe_fat.empresa_bpa
         AND ep.status = 0
         AND e.ativo = 'S'
     LIMIT 1 )
FROM legado_equipe_fat
WHERE uemp.cd_usuario_empresa = legado_equipe_fat.cd_usuario_empresa;