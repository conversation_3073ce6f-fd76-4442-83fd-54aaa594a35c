SET application_name = 'flyway|3.1.239.3';

SET statement_timeout TO 600000;


--HOTFIX/VSA-2421
--LUIS SEIDEL

alter table public.configuracao_vigilancia add column if not exists alvara_inicial_anos_validade int2 null;
alter table auditschema.configuracao_vigilancia add column if not exists alvara_inicial_anos_validade int2 null;

alter table public.configuracao_vigilancia add column if not exists revalidacao_alvara_anos_validade int2 null;
alter table auditschema.configuracao_vigilancia add column if not exists revalidacao_alvara_anos_validade int2 null;

alter table public.configuracao_vigilancia add column if not exists autorizacao_sanitaria_anos_validade int2 null;
alter table auditschema.configuracao_vigilancia add column if not exists autorizacao_sanitaria_anos_validade int2 null;

alter table public.configuracao_vigilancia add column if not exists credenciamento_treinamento_anos_validade int2 null;
alter table auditschema.configuracao_vigilancia add column if not exists credenciamento_treinamento_anos_validade int2 null;

alter table public.configuracao_vigilancia add column if not exists licenca_transporte_anos_validade int2 null;
alter table auditschema.configuracao_vigilancia add column if not exists licenca_transporte_anos_validade int2 null;

update public.configuracao_vigilancia
set alvara_inicial_anos_validade = '0'
,   revalidacao_alvara_anos_validade = '0'
,   autorizacao_sanitaria_anos_validade = '0'
,   credenciamento_treinamento_anos_validade = '0'
,   licenca_transporte_anos_validade = '0';

CREATE TABLE integracao_estoque_smar (
    cd_integracao_estoque_smar int8 primary key not null,
    empresa integer NOT NULL,
    num_lancto numeric(10,0) NOT NULL,
    resposta_json varchar(4000),
    resposta_status numeric(3,0),
    dt_integracao date NOT NULL,
    version bigint DEFAULT 0 NOT NULL
);

create sequence seq_integracao_estoque_smar start with 1;

CREATE TABLE AUDITSCHEMA.integracao_estoque_smar AS SELECT T2.*, T1.* FROM integracao_estoque_smar T1, AUDIT_TEMP T2 WHERE 1=2;CREATE SEQUENCE SEQ_AUDIT_ID_integracao_estoque_smar;ALTER TABLE AUDITSCHEMA.integracao_estoque_smar ADD PRIMARY KEY (AUDIT_ID);CREATE TRIGGER EMP_AUDIT AFTER INSERT OR UPDATE OR DELETE ON integracao_estoque_smar FOR EACH ROW EXECUTE PROCEDURE PROCESS_EMP_AUDIT();

CREATE TABLE  integracao_recebimento_brm_smar (
    cd_integracao_recebimento_brm_smar int8 primary key not null,
    resposta_json varchar(4000),
    resposta_status numeric(3,0),
    dt_integracao date NOT NULL,
    version bigint DEFAULT 0 NOT NULL
);

create sequence seq_integracao_recebimento_brm_smar start with 1;

CREATE TABLE AUDITSCHEMA.integracao_recebimento_brm_smar AS SELECT T2.*, T1.* FROM integracao_recebimento_brm_smar T1, AUDIT_TEMP T2 WHERE 1=2;CREATE SEQUENCE SEQ_AUDIT_ID_integracao_recebimento_brm_smar;ALTER TABLE AUDITSCHEMA.integracao_recebimento_brm_smar ADD PRIMARY KEY (AUDIT_ID);CREATE TRIGGER EMP_AUDIT AFTER INSERT OR UPDATE OR DELETE ON integracao_recebimento_brm_smar FOR EACH ROW EXECUTE PROCEDURE PROCESS_EMP_AUDIT();

COMMENT ON TABLE integracao_estoque_smar IS 'Tabela de Respostas - Integração API Smar';

INSERT INTO agendador_processo (cd_agendador_processo, cd_processo, nm_servico, ds_servico, status, version, tp_processo)
VALUES (87, 87, 'Enviar registro para Smar', 'Executa processo de envio da movimentação do estoque para a Integração API Smar. Deve ser configurado o parametroGEM dataInicioIntegraçãoSmar', 1, 0, 1);

ALTER TABLE empresa ADD COLUMN id_unidade_orcamentaria varchar(19);
ALTER TABLE auditschema.empresa ADD COLUMN id_unidade_orcamentaria varchar(19);
