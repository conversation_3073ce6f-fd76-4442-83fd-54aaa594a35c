SET application_name = 'flyway|3.1.60';

SET statement_timeout TO 600000;

/*
Marcus - 05/11/2020 AMB-1207
*/

INSERT INTO programa_pagina VALUES (1841, 'br.com.celk.view.unidadesaude.exames.resultadoexame.ResultadoExameComCidPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (1015, 'Resultado do exame com C.I.D. ', 1841, 'N');
INSERT INTO programa_web_pagina VALUES (1933, 1015, 1841);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1261,'Resultado do exame com CID','resultadoDoExameComCid',669,1015,143,0,0);
INSERT INTO programa_pagina_permissao VALUES(556, 3, 1841, 0, 'unidade');

CREATE TABLE resultado_cid_exame (
	cd_resultado_cid_exame int8 NOT NULL,
	dt_laudo timestamp NULL,
	cd_profissional_responsavel int8 NULL,
	cd_cid bpchar NULL,
	cd_anexo_paciente int8 NULL,
	cd_atendimento_exame int8 NULL,
	status int2 NOT NULL DEFAULT 1,
	"version" int8 NOT NULL,
	cd_usuario_cadsus numeric NULL,
	cd_exame int8 NULL,
	cd_conta_paciente int8 NULL,
	CONSTRAINT resultado_cid_exame_pk PRIMARY KEY (cd_resultado_cid_exame)
);

ALTER TABLE resultado_cid_exame ADD CONSTRAINT fk_res_cid_ex_ref_anexo_paci FOREIGN KEY (cd_anexo_paciente) REFERENCES anexo_paciente(cd_anexo_paciente);
ALTER TABLE resultado_cid_exame ADD CONSTRAINT fk_res_cid_ex_ref_atend_exame FOREIGN KEY (cd_atendimento_exame) REFERENCES atendimento_exame(cd_atendimento_exame);
ALTER TABLE resultado_cid_exame ADD CONSTRAINT fk_res_cid_ex_ref_cid FOREIGN KEY (cd_cid) REFERENCES cid(cd_cid);
ALTER TABLE resultado_cid_exame ADD CONSTRAINT fk_res_cid_ex_ref_conta_paci FOREIGN KEY (cd_conta_paciente) REFERENCES conta_paciente(cd_conta_paciente);
ALTER TABLE resultado_cid_exame ADD CONSTRAINT fk_res_cid_ex_ref_exame FOREIGN KEY (cd_exame) REFERENCES exame(cd_exame);
ALTER TABLE resultado_cid_exame ADD CONSTRAINT fk_res_cid_ex_ref_profis_respons FOREIGN KEY (cd_profissional_responsavel) REFERENCES profissional(cd_profissional);
ALTER TABLE resultado_cid_exame ADD CONSTRAINT fk_res_cid_ex_ref_usuario_cadsus FOREIGN KEY (cd_usuario_cadsus) REFERENCES usuario_cadsus(cd_usu_cadsus);

CREATE TABLE auditschema.resultado_cid_exame AS SELECT t2.*, t1.* FROM resultado_cid_exame t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_resultado_cid_exame;alter table auditschema.resultado_cid_exame add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON resultado_cid_exame FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

/*
    AMB-1208 - Cristian - 2020-11-05
*/
alter table exame_procedimento add column flag_informa_cid_resultado int8 default 0;
alter table auditschema.exame_procedimento add column flag_informa_cid_resultado int8 default 0;