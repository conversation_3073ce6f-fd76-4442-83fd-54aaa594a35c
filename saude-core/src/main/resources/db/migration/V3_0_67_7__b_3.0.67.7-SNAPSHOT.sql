SET application_name = 'flyway|3.0.67.7';

/*
    Roger - #15185 - 28/06/2017
*/

DROP VIEW IF EXISTS view_contas_tiss;
DROP VIEW IF EXISTS view_contas_tiss_profissional;

alter table item_conta_paciente alter cod_uni type numeric(12);
alter table item_kit_produto alter cod_uni type numeric(12);
alter table medicamento_descricao alter cod_uni type numeric(12);
alter table medicamento_paciente alter cod_uni type numeric(12);
alter table produtos alter cod_uni type numeric(12);
alter table produtos alter cod_uni_prescricao type numeric(12);
alter table produtos alter cod_uni_rec type numeric(12);
alter table receituario_item alter cod_uni type numeric(12);
alter table receituario_item_componente alter cod_uni type numeric(12);
alter table receituario_item_kit alter cod_uni type numeric(12);
alter table samu_checagem_item alter cod_uni type numeric(12);
alter table samu_inspecao_item alter cod_uni type numeric(12);

alter table auditschema.item_conta_paciente alter cod_uni type numeric(12);
alter table auditschema.item_kit_produto alter cod_uni type numeric(12);
alter table auditschema.medicamento_descricao alter cod_uni type numeric(12);
alter table auditschema.medicamento_paciente alter cod_uni type numeric(12);
alter table auditschema.produtos alter cod_uni type numeric(12);
alter table auditschema.produtos alter cod_uni_prescricao type numeric(12);
alter table auditschema.produtos alter cod_uni_rec type numeric(12);
alter table auditschema.receituario_item alter cod_uni type numeric(12);
alter table auditschema.receituario_item_componente alter cod_uni type numeric(12);
alter table auditschema.receituario_item_kit alter cod_uni type numeric(12);
alter table auditschema.samu_checagem_item alter cod_uni type numeric(12);
alter table auditschema.samu_inspecao_item alter cod_uni type numeric(12);
