SET application_name = 'flyway|3.1.8';

/*
    Mauricley - 17/04/2018 - #18734
*/

alter table atendimento add column dt_canc_lote timestamp;
alter table auditschema.atendimento add column dt_canc_lote timestamp;

INSERT INTO programa_pagina VALUES (1568, 'br.com.celk.view.atendimento.prontuario.cancelamentolistaatendimento.CancelamentoListaAtendimentosPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (875, 'Cancelamento Lista dos Atendimentos', 1568, 'N');
INSERT INTO programa_web_pagina VALUES (1657, 875, 1568);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1128,'Cancelamento Lista dos Atendimentos','cancelamentoListaAtendimentos',182,875,143,0,0);

INSERT INTO programa_pagina_permissao VALUES(407, 18, 1568, 0, 'unidade');
INSERT INTO programa_pagina_permissao VALUES(408, 12, 1568, 0, '');


/*
    Mauricley - 04/06/2018 - #19102
*/

ALTER TABLE pedido_transferencia DISABLE TRIGGER USER;

update pedido_transferencia
   set status = 4,
       dt_cancelamento = current_timestamp,
       cd_usuario_can = 0
 where status <> 4 and
       exists(select 1
                from pedido_transferencia_item pti
               where pti.pedido = pedido_transferencia.pedido) and
       not exists(select 1
                    from pedido_transferencia_item pti
                   where pti.pedido = pedido_transferencia.pedido and
                         pti.status not in (5, 6));

ALTER TABLE pedido_transferencia ENABLE TRIGGER USER;

/*
    Silvio - 06/06/2018 - #19141
*/

INSERT INTO programa_pagina VALUES (1573, 'br.com.celk.view.consorcio.consorcioguiaprocedimento.relatorio.RelatorioRelacaoGuiasPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (878, 'Relação das Guias (SISREG)', 1573, 'N');
INSERT INTO programa_web_pagina VALUES (1662, 878, 1573);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1131,'Relação das Guias (SISREG)','',183,878,111,0,0);

/*
 PostgreSQL
 Everton - 08/06/2018 - #19185
*/
alter table auditschema.receituario_item ALTER nm_produto type varchar(200);
alter table receituario_item ALTER nm_produto type varchar(200);

/*
    Elton - 05/06/2018 - #19107
*/

alter table pedido_transferencia add column motivo_cancelamento varchar(500);
alter table auditschema.pedido_transferencia add column motivo_cancelamento varchar(500);

INSERT INTO programa_pagina_permissao VALUES(421, 12, 221, 0, 'cancelarPedido');

/*
    Mauricley - 08/06/2018 - #19164
*/
alter table cidade_bairro alter column ds_bairro type varchar(40);
alter table auditschema.cidade_bairro alter column ds_bairro type varchar(40);

alter table raas_psi alter column descricao_bairro type varchar(40);
alter table auditschema.raas_psi alter column descricao_bairro type varchar(40);

alter table atendimento_samu_endereco alter column bairro type varchar(40);
alter table auditschema.atendimento_samu_endereco alter column bairro type varchar(40);

alter table endereco_usuario_cadsus alter column nm_bairro type varchar(40);
alter table auditschema.endereco_usuario_cadsus alter column nm_bairro type varchar(40);

alter table responsavel_tecnico alter column bairro type varchar(40);
alter table auditschema.responsavel_tecnico alter column bairro type varchar(40);

alter table termo_recusa_cad_domiciliar alter column nm_bairro type varchar(40);
alter table auditschema.termo_recusa_cad_domiciliar alter column nm_bairro type varchar(40);

alter table vigilancia_endereco alter column nm_bairro type varchar(40);
alter table auditschema.vigilancia_endereco alter column nm_bairro type varchar(40);

alter table profissional alter column bairro type varchar(40);
alter table auditschema.profissional alter column bairro type varchar(40);