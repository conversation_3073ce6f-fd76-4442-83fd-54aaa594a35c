SET application_name = 'flyway|3.1.125';

SET statement_timeout TO 600000;

/*
    <PERSON><PERSON> - REG-1046 - 30/03/2022
*/

insert into permissao_web values (167, 'Abrir Competências', 0);
insert into programa_pagina_permissao values (594, 167, 252, 0, 'abrirCompetencias');


/*
    Hudson / VSA-71 / 25/03/2022
*/

INSERT INTO public.ficha_investigacao_agravo(
    cd_ficha_investigacao_agravo, ordem, dt_cadastro, descricao, status, "version")
VALUES (24, 1, NOW(), 'ACIDENTE DE TRABALHO COM EXPOSIÇÃO À MATERIAL BIOLÓGICO', 1, 0);

CREATE TABLE public.investigacao_agr_acidente_trab_mat_biologico(
    cd_invest_agr_acidente_trab_mat_biologico int8 PRIMARY KEY NOT NULL,
    "version" int8 NOT NULL,
    flag_informacoes_complementares varchar(1) NOT NULL DEFAULT 'S'::character varying,
    cd_registro_agravo int8 NOT NULL REFERENCES public.registro_agravo(cd_registro_agravo),

    --investigacao
    dt_investigacao date NULL,
    ocupacao_cbo varchar(10) NULL REFERENCES public.tabela_cbo(cd_cbo),

    --observacao
    observacao text NULL,

    --encerramento
    dt_encerramento date NULL,
    cd_usuario_encerramento int8 NULL,

    --antecedentes epidemiologicos
    situacao_mercado_trabalho int2 null,
    tempo_trabalho_ocupacao varchar(2) null,
    tempo_trabalho_ocupacao_um int2 null,

    --Dados da empresa contratante
    cd_empresa int8 null references public.empresa(empresa),
    empresa_distrito varchar(100) null,
    empresa_ponto_referencia varchar(100) null,
    empresa_telefone varchar(16) null,
    empregador_empresa_terceirizada int2 null,

    --acidente com material biológico
    tipo_exposicao_percutanea int2 null,
    tipo_exposicao_pele_integra int2 null,
    tipo_exposicao_mucosa int2 null,
    tipo_exposicao_pele_nao_integra int2 null,
    tipo_exposicao_outros varchar(50) null,

    material_organico int2 null,
    material_organico_outros varchar(50) null,

    circunstancia_acidente int2 null,
    agente int2 null,

    uso_epi_luva int2 null,
    uso_epi_avental int2 null,
    uso_epi_oculos int2 null,
    uso_epi_mascara int2 null,
    uso_epi_protecao_facial int2 null,
    uso_epi_bota int2 null,

    vacina_hepatite_b int2 null,

    resultado_exames_anti_hiv int2 null,
    resultado_exames_hbs_ag int2 null,
    resultado_exames_anti_hbs int2 null,
    resultado_exames_anti_hcv int2 null,

    paciente_fonte_conhecida int2 null,

    resultado_sorologico_anti_hiv int2 null,
    resultado_sorologico_hbs_ag int2 null,
    resultado_sorologico_anti_hbc int2 null,
    resultado_sorologico_anti_hcv int2 null,

    conduta_sem_quimioprofilaxia int2 null,
    conduta_recusou_quimioprofilaxia int2 null,
    conduta_azt_3tc int2 null,
    conduta_azt_3tc_indinavir int2 null,
    conduta_azt_3tc_nelfinavir int2 null,
    conduta_hbig int2 null,
    conduta_vacina_hepatite_b int2 null,
    conduta_outros varchar(50) null,

    --conclusao
    evolucao_caso int2 NULL,
    evolucao_caso_virus varchar(50) null,
    data_obito DATE NULL,
    emitida_cat int2 NULL
);

CREATE SEQUENCE seq_investigacao_agr_acidente_trab_mat_biologico INCREMENT 1 START 1;

CREATE TABLE auditschema.investigacao_agr_acidente_trab_mat_biologico AS
    (SELECT t2.*, t1.* FROM investigacao_agr_acidente_trab_mat_biologico t1, audit_temp t2 WHERE 1=2);

CREATE sequence seq_audit_id_investigacao_agr_acidente_trab_mat_biologico;

ALTER TABLE auditschema.investigacao_agr_acidente_trab_mat_biologico ADD PRIMARY KEY (audit_id);

CREATE trigger emp_audit AFTER INSERT OR UPDATE OR DELETE
    ON public.investigacao_agr_acidente_trab_mat_biologico FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

/*
    pedro.celk  / INF-967 / 29/09/2022
*/

create index if not exists idx_conta_paciente_cd_visita
on conta_paciente (cd_visita);