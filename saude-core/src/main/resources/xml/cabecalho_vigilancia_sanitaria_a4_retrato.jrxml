<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="cabecalho_vigilancia_sanitaria_a4_retrato" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="6241c44e-6e7a-498a-8264-4d528a58fd1a">
	<property name="ireport.zoom" value="1.948717100000003"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="CAMINHO_IMAGEM_LOGO_VIGILANCIA_SANITARIA" class="java.lang.String"/>
	<parameter name="DESC_LINHA_1" class="java.lang.String"/>
	<parameter name="DESC_LINHA_2" class="java.lang.String"/>
	<parameter name="DESC_LINHA_3" class="java.lang.String"/>
	<parameter name="TITULO_REPORT" class="java.lang.String"/>
	<parameter name="RUA_UNIDADE" class="java.lang.String"/>
	<parameter name="NUMERO_UNIDADE" class="java.lang.String"/>
	<parameter name="BAIRRO_UNIDADE" class="java.lang.String"/>
	<parameter name="CEP_UNIDADE" class="java.lang.String"/>
	<parameter name="CIDADE_UNIDADE" class="java.lang.String"/>
	<parameter name="UF_UNIDADE" class="java.lang.String"/>
	<parameter name="FONE_UNIDADE" class="java.lang.String"/>
	<parameter name="EXIBIR_CABECALHO" class="java.lang.Boolean"/>
	<parameter name="EXIBIR_RODAPE" class="java.lang.Boolean"/>
	<parameter name="USUARIO_LOGADO" class="java.lang.String"/>
	<parameter name="EXIBIR_HORARIO" class="java.lang.Boolean"/>
	<parameter name="DESC_LINHA_4" class="java.lang.String"/>
	<parameter name="EXIBIR_LOGO_SUS" class="java.lang.Boolean"/>
	<parameter name="VERSAO_SISTEMA" class="java.lang.String"/>
	<parameter name="SISTEMA" class="java.lang.String"/>
	<parameter name="EXIBIR_TITULO_PRIMEIRA_PAGINA" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[false]]></defaultValueExpression>
	</parameter>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="81" splitType="Stretch">
			<image hAlign="Center" vAlign="Middle">
				<reportElement key="image-1" x="2" y="1" width="71" height="60" isRemoveLineWhenBlank="true" uuid="3d4066cb-bc5f-4763-a904-e7175f9499c2">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<imageExpression><![CDATA[$P{CAMINHO_IMAGEM_LOGO_VIGILANCIA_SANITARIA}]]></imageExpression>
			</image>
			<textField isBlankWhenNull="true">
				<reportElement x="78" y="13" width="350" height="12" isRemoveLineWhenBlank="true" uuid="eae90ae1-6ae2-4ccd-8bb1-e3f112e9bebf">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{DESC_LINHA_2}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" x="0" y="61" width="555" height="1" isRemoveLineWhenBlank="true" uuid="46ea9593-e203-4cca-a012-1115ffb39548">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement x="78" y="27" width="350" height="12" isRemoveLineWhenBlank="true" uuid="ee715499-772d-4227-9d05-06e8450ae569">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{DESC_LINHA_3}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="0" y="64" width="555" height="16" isRemoveLineWhenBlank="true" uuid="eeab5f6a-95a3-4cfd-ac23-b34d409d47ec">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO} && (!$P{EXIBIR_TITULO_PRIMEIRA_PAGINA} || $V{PAGE_NUMBER} == 1)]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="13" isBold="true" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{TITULO_REPORT}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="78" y="1" width="350" height="12" isRemoveLineWhenBlank="true" uuid="67c4aff1-f174-441e-b54b-c763b8bae0de">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{DESC_LINHA_1}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="78" y="39" width="350" height="12" isRemoveLineWhenBlank="true" uuid="9b133586-6336-4a0e-81f1-076285b3a420">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{DESC_LINHA_4}]]></textFieldExpression>
			</textField>
			<image>
				<reportElement x="428" y="1" width="125" height="50" isRemoveLineWhenBlank="true" uuid="48c7ece9-39a6-4d5b-ae06-618963b0432a">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO} && $P{EXIBIR_LOGO_SUS}]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA["br/com/ksisolucoes/gui/imagens/sus_logo.png"]]></imageExpression>
			</image>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="78" y="53" width="475" height="8" isRemoveLineWhenBlank="true" uuid="e19a9494-a832-4583-82df-d17a7033f589">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="5" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Emitido"
    + ($P{USUARIO_LOGADO} != null ? " por " + $P{USUARIO_LOGADO} : "")
    + " em " + Data.formatarComTimezone(Data.getDataAtual())
    + " | " + $P{SISTEMA} + " v" + $P{VERSAO_SISTEMA} + " - CELK SISTEMAS LTDA"]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band splitType="Stretch"/>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
