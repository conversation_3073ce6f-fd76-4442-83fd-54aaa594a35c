<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="report name" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="dfea44f4-24c7-4b38-bba4-57985c167b4c">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="DESC_CABECALHO_PADRAO" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="UNIDADE_ATENDIMENTO" class="java.lang.String"/>
	<parameter name="CAMINHO_IMAGEM_PADRAO" class="java.lang.String"/>
	<parameter name="RUA_UNIDADE" class="java.lang.String"/>
	<parameter name="BAIRRO_UNIDADE" class="java.lang.String"/>
	<parameter name="CIDADE_UNIDADE" class="java.lang.String"/>
	<parameter name="UF_UNIDADE" class="java.lang.String"/>
	<parameter name="CEP_UNIDADE" class="java.lang.String"/>
	<parameter name="FONE_UNIDADE" class="java.lang.String"/>
	<parameter name="EXIBIR_CABECALHO" class="java.lang.Boolean"/>
	<parameter name="EXIBIR_RODAPE" class="java.lang.Boolean"/>
	<parameter name="NUMERO_UNIDADE" class="java.lang.String"/>
	<parameter name="TITULO_REPORT" class="java.lang.String"/>
	<parameter name="CABECALHO_ADICIONAL_1" class="java.lang.String"/>
	<parameter name="CABECALHO_ADICIONAL_2" class="java.lang.String"/>
	<parameter name="CABECALHO_DIRETOR_TECNICO" class="java.lang.String"/>
	<parameter name="DESCRICAO_1_VIA" class="java.lang.String"/>
	<parameter name="DESCRICAO_2_VIA" class="java.lang.String"/>
	<parameter name="USUARIO_LOGADO" class="java.lang.String"/>
	<parameter name="EXIBIR_HORARIO" class="java.lang.Boolean"/>
	<parameter name="VERSAO_SISTEMA" class="java.lang.String"/>
	<parameter name="SISTEMA" class="java.lang.String"/>
	<parameter name="EXIBIR_DIRETOR_TECNICO" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[false]]></defaultValueExpression>
	</parameter>
	<parameter name="EXIBIR_TITULO_PRIMEIRA_PAGINA" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[false]]></defaultValueExpression>
	</parameter>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="82" splitType="Stretch">
			<line>
				<reportElement uuid="da5dc9e9-436b-4a8f-927f-3d56fe44a402" positionType="Float" x="0" y="52" width="381" height="1" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField>
				<reportElement uuid="dfd3ac66-30bf-4513-9c58-f209a3040a18" positionType="Float" x="1" y="54" width="379" height="15" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO} && (!$P{EXIBIR_TITULO_PRIMEIRA_PAGINA} || $V{PAGE_NUMBER} == 1)]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{TITULO_REPORT}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="9bf0a76b-0516-4e96-98e0-321755789c87" positionType="Float" x="422" y="54" width="379" height="15" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO} && (!$P{EXIBIR_TITULO_PRIMEIRA_PAGINA} || $V{PAGE_NUMBER} == 1)]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{TITULO_REPORT}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement uuid="e60bc669-f106-4686-8d87-2c2f40164780" positionType="Float" x="421" y="52" width="381" height="1" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement uuid="9b29125e-bb55-485c-92f3-ae208517e174" key="textField-4" positionType="Float" x="132" y="70" width="244" height="11" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="styled">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA["<style  isBold=\"true\" >"
+($P{DESCRICAO_1_VIA} != null ? $P{DESCRICAO_1_VIA} : Bundle.getStringApplication("rotulo_primeira_via"))+
"</style>"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="0649dc73-6d23-431a-be01-999d70778ddc" key="textField-4" positionType="Float" x="554" y="70" width="244" height="11" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="styled">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA["<style isBold=\"true\" >"
+($P{DESCRICAO_2_VIA} != null ? $P{DESCRICAO_2_VIA} : Bundle.getStringApplication("rotulo_segunda_via"))+
"</style>"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="ef16bcd0-ccbb-444f-8c67-0d8e9dedcd5f" x="62" y="44" width="317" height="8" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<box topPadding="1"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="5" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Emitido"
    + ($P{USUARIO_LOGADO} != null ? " por " + $P{USUARIO_LOGADO} : "")
    + " em " + Data.formatarComTimezone(Data.getDataAtual())
    + " | " + $P{SISTEMA} + " v" + $P{VERSAO_SISTEMA} + " - CELK SISTEMAS LTDA"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="7d0b5040-9684-4198-87e5-efdd269974b2" x="62" y="14" width="250" height="10" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{UNIDADE_ATENDIMENTO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="7956573f-f0e7-434c-aa12-921255b973f0" x="62" y="34" width="250" height="10" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{CABECALHO_ADICIONAL_2}]]></textFieldExpression>
			</textField>
			<image>
				<reportElement uuid="ccfff6c3-965b-4130-b0c7-42ae1cab19da" key="image-1" x="2" y="2" width="55" height="50" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<imageExpression><![CDATA[$P{CAMINHO_IMAGEM_PADRAO}]]></imageExpression>
			</image>
			<textField>
				<reportElement uuid="f424d2e5-6a67-4731-958e-ff721f4b1279" x="314" y="2" width="65" height="41" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{EXIBIR_DIRETOR_TECNICO} && $P{EXIBIR_CABECALHO} && (!$P{EXIBIR_TITULO_PRIMEIRA_PAGINA} || $V{PAGE_NUMBER} == 1)]]></printWhenExpression>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineStyle="Dashed"/>
					<topPen lineWidth="0.25" lineStyle="Dashed"/>
					<leftPen lineWidth="0.25" lineStyle="Dashed"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.25" lineStyle="Dashed"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="5" isBold="false"/>
					<paragraph lineSpacing="1_1_2"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{CABECALHO_DIRETOR_TECNICO} != null
? $P{CABECALHO_DIRETOR_TECNICO}.replaceAll("-", "\n")
: null]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="367d3c03-8a83-4d65-a899-91d2a0ec3a62" x="62" y="25" width="250" height="10" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{CABECALHO_ADICIONAL_1}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="d9159aea-9402-437b-82e0-5ac8bf3b3b00" x="62" y="2" width="250" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{DESC_CABECALHO_PADRAO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="b8277ce1-4c35-45cf-9371-e40b6b0ddbec" x="483" y="2" width="250" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{DESC_CABECALHO_PADRAO}]]></textFieldExpression>
			</textField>
			<image>
				<reportElement uuid="417ce862-39af-4481-b39a-191eca92c9eb" key="image-1" x="423" y="2" width="55" height="50" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<imageExpression><![CDATA[$P{CAMINHO_IMAGEM_PADRAO}]]></imageExpression>
			</image>
			<textField>
				<reportElement uuid="418e6bcb-8f63-469a-82ec-ad256f0c2a24" x="483" y="34" width="250" height="10" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{CABECALHO_ADICIONAL_2}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="aa0a7d53-2504-409a-8f2d-b3630d1ef176" x="735" y="2" width="65" height="41" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{EXIBIR_DIRETOR_TECNICO} && $P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineStyle="Dashed"/>
					<topPen lineWidth="0.25" lineStyle="Dashed"/>
					<leftPen lineWidth="0.25" lineStyle="Dashed"/>
					<bottomPen lineWidth="0.25" lineStyle="Dashed"/>
					<rightPen lineWidth="0.25" lineStyle="Dashed"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="5" isBold="false"/>
					<paragraph lineSpacing="1_1_2"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{CABECALHO_DIRETOR_TECNICO} != null
? $P{CABECALHO_DIRETOR_TECNICO}.replaceAll("-", "\n")
: null]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="7e38a20c-9745-4a33-abf2-b89b67e8b2df" x="483" y="14" width="250" height="10" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{UNIDADE_ATENDIMENTO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="3a9d30b7-04f0-4346-bcbd-730a81e9dc92" x="483" y="44" width="317" height="8" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<box topPadding="1"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="5" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Emitido"
    + ($P{USUARIO_LOGADO} != null ? " por " + $P{USUARIO_LOGADO} : "")
    + " em " + Data.formatarComTimezone(Data.getDataAtual())
    + " | " + $P{SISTEMA} + " v" + $P{VERSAO_SISTEMA} + " - CELK SISTEMAS LTDA"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="cde6687f-c663-4c02-8efa-a7e7c75edbce" x="483" y="25" width="250" height="10" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{EXIBIR_CABECALHO}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{CABECALHO_ADICIONAL_1}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band splitType="Stretch"/>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="25" splitType="Stretch">
			<line>
				<reportElement uuid="29f1a15d-9b10-42b1-8c9b-d83935bacc30" x="0" y="1" width="381" height="1"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField>
				<reportElement uuid="02e21f85-340b-4855-ae52-50678fe33967" x="422" y="14" width="379" height="11">
					<printWhenExpression><![CDATA[$P{EXIBIR_RODAPE}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{CIDADE_UNIDADE} + " - " + $P{UF_UNIDADE}
+ ($P{FONE_UNIDADE} == null
    ? ""
    : " | " + $P{FONE_UNIDADE})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="a157a5a9-3e1f-4356-934f-480b2810d474" x="422" y="3" width="379" height="11">
					<printWhenExpression><![CDATA[$P{EXIBIR_RODAPE}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{RUA_UNIDADE} + ($P{NUMERO_UNIDADE} != null ? ", " + $P{NUMERO_UNIDADE} : "")
    + " - " + $P{BAIRRO_UNIDADE} + ($P{CEP_UNIDADE} != null ? " - CEP " + $P{CEP_UNIDADE} : "")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement uuid="d694bd8c-f2f6-4bc9-a527-fa8c897508a2" x="421" y="1" width="381" height="1"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField>
				<reportElement uuid="5417ba74-f9f1-4158-8fba-fac2a9196b70" x="1" y="14" width="379" height="11">
					<printWhenExpression><![CDATA[$P{EXIBIR_RODAPE}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{CIDADE_UNIDADE} + " - " + $P{UF_UNIDADE}
+ ($P{FONE_UNIDADE} == null
    ? ""
    : " | " + $P{FONE_UNIDADE})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="1c9e4668-6205-4be4-a125-c196d2b4d710" x="1" y="3" width="379" height="11">
					<printWhenExpression><![CDATA[$P{EXIBIR_RODAPE}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{RUA_UNIDADE} + ($P{NUMERO_UNIDADE} != null ? ", " + $P{NUMERO_UNIDADE} : "")
    + " - " + $P{BAIRRO_UNIDADE} + ($P{CEP_UNIDADE} != null ? " - CEP " + $P{CEP_UNIDADE} : "")]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
