#Mensagem para erro na validação
2000=O campo {0} deve ser definido\!
#Mensagem quando ocorre erro durante a tentativa de validação
2001=Erro durante a tentativa de validação dos campos\!
#Mensagem para cpf/cnpj já cadastrados.
2002=Já existe uma pessoa cadastrada com este CPF/CNPJ
#Mensagem com problemas nas regras de negócios
2003=Problemas ao aplicar as regras de negócio no objeto
#Rótulo para A soma dos percentuais deve alcançar o valor de 100%
2004=A soma dos percentuais deve alcançar o valor de 100%
#Mensagem para Usuário sem autorização para modificar este PIC
2005=Usuário sem autorização para modificar este {0}
#Mensagem para Qtde Encomendada inválida.
2006=Quantidade encomendada deve ser maior ou igual a quantidade comprada.
#Mensagem quando PIC já efetivado. Não habilitado para alteração
2007=Pedido Interno de Compra já fechado. Disponível apenas para consulta.
#Mensagem quando entidade não pode ser excluída
2008=Não permitido a exclusão de(a) {0}.
#Mensagem de erro quando decremento resultar em valor menor que zero.
2009=A quantidade resultante não pode ser menor que zero.
#Mensagem para valores nulos.
2010=O(A) {0} não pode ser nulo\!
#Mensagem para valores nulos.
2011=Erro ao executar Transação.
#Mensagem para quando nenhum tipo de pessoa for selecionado.
2012=Deve ser selecionado pelo menos um tipo de pessoa (Representante, Cliente, Fornecedor e/ou Funcionário).
#Mensagem para que representante não possa ter representante.
2013=Representante não pode ter um representante.
2014=CPF/CNPJ inválido\!
2015=Índice já cadastrado para o período
2016=Impossível realizar operação. {0} mantém relacionamento com {1}.
2017=Não é possível alterar duplicatas que não estejam abertas ou com pagamento parcial.
2018=Duplicata já recebida.
2019=Moeda inválida.
2020=Os valores da decomposição não conferem com o valor inicial da baixa.
2021=O valor deve ser informado, e maior que 0.0
2022=Identificador inválido\!
2023=Deve ser selecionado pelo menos uma opção Interno/Externo
2024=Operacao não suportada.
2025=Saldo deve ser zerado, ou seja, o total de pagamentos e total de cheques devem ser iguais.
2026=Movimentação Financeira já fechada para esta data\!
2027=O status desse item é cancelado. Não podendo ser alterado.
2028=O status desse item é pago. Não podendo ser alterado.
2029=Não pode carregar/modificar entidade padrão do sistema.
#Mensagem de erro para estoque fisico mais estoque encomendado, menos estoque reservado menor que zero.
2030=Estoque disponível resultante menor que zero.
2031=A data de {0} não pode ser maior que a data de {1}.
2032=O numero da nota fiscal deve ser informado.
2033=A {0} deve possuir item(s).
2034=Não e possivel atualizar um item com status {0}.
2035=A quantidade comprada deve ser menor ou iqual a quantidade de saldo do(s) PIC(s).
2036=A quantidade deve ser menor ou iqual a quantidade de saldo do(s) item(s) de ordem compra.
2037=A quantidade recebida a estornar e maior que o total recebido.
2038=O valor informado para a {0} está incorreto.
2039=O Pedido Interno de compra {0} não possui saldo suficiente para cancelamento.
2040=Deve ser enviado ao menos um item de ordem de compra para ser aprovado.
2041=Deve ser enviado ao menos um item de ordem de compra para ser negado.
2042=A quantidade comprada não pode ser menor que a quantidade recebida
2043=Deve ser enviado ao menos um item de {0} para ser {1}.
2044=Gerado pelo pagamento de contas com cheque
2045=O valor total da  duplicata não confere com o valor total da nota fiscal.
2046=Movimento gerado apartir da {0}.
2047=Deve ser selecionado algum cheque para efetuar a transação
2048=Quantidade entregue inválida
2049=Quantidade inválida para entrada
2050=Não é possível estornar uma baixa de duplicatas cancelada.
2051=O Item de Pedido {0}  não possui saldo suficiente para cancelamento.
2052=Não é possível cancelar  ordem de compra que não esteja aberta
2053=O cancelamento do Pedido exige um motivo cancelamento.
2054=Não é possível cancelar um relacionamento já cancelado
2055=Tomada de preço é inválida. Esta deve conter {0} e {1}
2056=Não é possível estornar uma despesa não paga
2057=A data de {0} deve ser menor da data de {1}
2058=A data da baixa deve ser informada
2060=Não existem baixas para esta duplicata
2061=A data de compensação deve ser informada
2062=Não é possível alterar uma despesa já paga
2063=Não é possível {0} uma {1} cancelada.
2064=Este histórico é padrão do sistema, não podendo ser alterado.
2065=Esse tipo de documento exige inclusão da observação.
2066=O {0} referente a esse {1} exige um {2}
2067=O tipo de documento selecionado não gera atualização de estoque.
2068=O {0} deve possuir uma {1}
2069=Não é possivel alterar uma {0} com {1}
2070=A Nota Fiscal referente a este tipo de documento exige Duplicata a Pagar para o faturamento.
2071=Deve ser digitado, ou um Representante ou um Vendedor
2072=Não é possivel alterar este pedido. Status {0}
2073=Não é possivel alterar este {0}
2074=Não é possivel alterar uma Nota Fiscal impressa ou cancelada.
2076=A Nota Fiscal deve possuir um condição de pagamento.
2077=Não é possível estornar um cheque não compensado.
2078=Estorno de Suprimento
2079=Lançamento de Suprimento
2080=È necessario ter um produto cadastrado para este tipo de produto
2081=O item de nota fiscal deve obrigatoriamente possuir item(ns) de pedido.
2082=A entidade deve possuir {0}
2083=O Item deve ter um produto.
2084=Não é permitido item de orcamento com produto diferente do produto do item de pedido.
2085=A quantidade vendida {0} deve ser maior que a quantidade atendida {1}
2086=A Nota Fiscal referente a este tipo de documento exige Duplicata(s) a Receber para o faturamento.
2087=A CondicaoPagamento da NotaFiscal deve ser a mesma do pedido dos itens de pedido selecionados
2088=Não é possível alterar um item pedido com status Faturado ou Cancelado
2089=O cliente e o item de NotaFiscal deve ser necessáriamente igual ao do Pedido.
2090=O tipo de pedido dos itens de pedido deve ser igual ao da nota fiscal.
2091=A quantidade comprada deve ser menor ou igual a quantidade de saldo dos itens de pedido
2092=Não é possível alterar um pedido com status Faturado ou Cancelado.
2107=Obrigatoriamente deve ser definido no mínimo um representante ou um vendedor.
2108=O(a) {0} deve ser maior que o(a) {1}.
2109=Alteração na pessoa de codigo {0}.
2110=Selecione um plano de contas para o produto ou um grupo de produto ou sub-grupo que possua um plano de contas.
2111=A seleção de um plano de contas é obrigatória quando a Movimentação Financeira for de Débito.
2112=O tipo de duplicata não permite cadastro manual de duplicata.
2113=O Plano de Contas é Obrigatório.
2114=Problemas aplicando regras de consultas
2115=Revisão já aprovada.
2116=Ordem de Fabricação inválida! Esta ordem caracteriza uma OP.
2117=Movimentação de Estoque não permitida para este Produto.
2118=Não é permitida a exclusão de Roteiro de Fabricação já aprovado.
2119=Revisão ainda não aprovada.
2120=Somente a revisão atual pode ser desaprovada.
2121=Revisão já em produção.
2122=Revisão pendente já existente para este produto.
2123=O Orçamento deve possuir um Cliente.
2124=Baixa de Duplicata à Receber já vinculada ao recebimento por cheques.
2125=<html>O Tipo de Documento Selecionado exige a geração de duplicatas.</br> O valor total das duplicatas não confere com o valor total da Nota fiscal</html>
2126=Sem permissão para edição.
2127=O Relacionamento deve possuir uma Despesa.
2128=Não e possivel baixar a Ordem de Fabricacao. Existem Ordens de Producao abertas.
2129=Devem ser selecionados Item(s) de Pedido para Baixar.
2131=<html>\nProduto com Procedência Fabricada que possui estrutura não pode possuir <br>\no SubGrupo alterado para outro cuja Procedência não seja Fabricada.\n<html>
2130=A quantidade baixada resultante ( {0} ) é superior a quantidade reservada ( {1} ) .
2132=Não é possível alterar uma reserva que não seja aberta. Reserva\:  {0}
2133=O produto {0} não possui quantidade disponível em estoque para a reserva/venda. Quantidade disponível\: {1};
2134=Não é possivel alterar uma Nota Fiscal com item(s) recebido(s). Nota Fiscal\: {0)
2135=Deve ser enviado ao menos um Item de Ordem de Compra para ser negado.
2136=Não é possível baixar uma Duplicata com data de pagamento anterior à data de emissão.
2137=Não é possível salvar uma Duplicata com valor 0 (zero).
2138=Não é possível baixar uma Duplicata com data de vencimento anterior à data de emissão.
2139=Devido ao Tipo de Duplicata exigir a geração de comissão, é exigida a seleção de um Representante.
2140=Não é possível salvar uma Duplicata cancelada.
2141=Deve ser selecionado um Produto.
2142=Sem permissão para consulta.
2143=Nivel máximo({0}) de níveis hierarquicos atingido. Os produtos podem estar distribuidos de forma recursiva.
2144=Problemas com a substituicao. Produto\: {0}, Componente\: {1}, Item\: {2}.
2145=<html>A estrutura resultante da alteração já existe, com valores em sua identificação\: <br>\n\\t - Código do produto \= {0}\n\\t - Código do componente \= {1}\n\\t - Item \= {2} <br>\nEsta foi alterada para os seguintes valores em sua identificação\:<br>\n\\t - Código do produto \= {3}\n\\t - Código do componente \= {4}\n\\t - Item \= {5}</html>
2146=O Componete deve ser informado.
2147=O Produto de destino não pode ser igual ao Produto de origem.
2148=O Produto de destino não pode possuir estrutura.
2149=O Produto de origem deve possuir estrutura para ser copiada.
2150=Problemas com clone da Estrutura.
2151=O Produto da Ordem de Fabricacao deve ser de Procedência Fabricada.
2152=A Ordem de Fabricacao já possui uma copia de Estrutura Equipamento.
2153=Para um produto com Procedência Fabricado ser um item substituível, este, deve possuir uma Estrutura.
2154=Produto\: {0}
2155=O Produto de origem não possui Roteiro de Fabricacao aprovado.
2156=O produto {0} possui a revisão {1} pendente.
2157=O Produto ({0}) não possui a revisão {1}
2158=Roteiro de Fabricacao aprovado não pode ser alterado.
2159=O Produto deve ter a Procedencia Fabricado.
2160=Para Roteiro de Fabricacao com Grupo de Máquinas deve ser inserido o Servico Terceiro.
2161=Não é possivel criar uma Ordem de Fabricacao para um Produto com Prodecencia não Fabricado.
2162=<html>Tipo de Planejamento inválido para Ordem de Fabricação. <br>\nA flag planeja (S/N) deste Tipo de Planejamento não permite planejamento.</html>
2163=Não é possível alterar um Pedido salvo.
2164=<html>Tipo de Pedido <br> \nO Item de Pedido deve obrigatoriamente ser proveniente de Item(s) de Orcamento.<br>\nRegra definida pela flag seleciona Orcamento (S/N) no Tipo de Pedido</html>
2165=Nota Fiscal gerada pelo processo de Baixa de Item de Pedido. Pedido codigo {0}.
2166=Não é possível imprimir uma Nota Fiscal que não esteja aberta. Status da Nota Fiscal\: {0}.
2167=Não é possível excluir uma Nota Fiscal com Status diferente de Normal/Aberto. Status da Nota Fiscal\: {0}.
2168=Status inválido. Status da Nota Fiscal\: {0}.
2169=Para Nota fiscal com Produto não cadastrado, a seleção da Unidade é obrigatória.
2170=O código do Tipo de Planejamento deve ser definido.
2171=A Quantidade a Baixar ({0}) é maior que o Saldo da Requisicao ({1}).
2172=( Serie\: {0} - Item\: {1} - Tipo de Duplicata\: {2} )
2173=Não é possivel carregar uma duplicata com status diferente de ABERTO.
2174=Não existem dados para simulacao.
2175=A seleção de um produto é obrigatória.
2176=<html>Apenas produtos Fabricados podem ser selecionados.\n<br>{0} - {1}</html>
2177=A bitola deve ser preenchida.
2178=A quantidade informada é maior que 0 (zero).
2179=<html>Duplicata já recebida.<br>( Serie\: {0} - Item\: {1} - Tipo de Duplicata\: {2} - Valor\: {3} ) </html>
2180=Baixa de Duplicata à Pagar já vinculada ao pagamento por cheques.
2181=Não é possível excluir a revisão. Há Orden(s) de Produção contendo esta revisão.
2182=É obrigatório a definição de um tipo de documento nos parâmetros padrões do sistema para o processo de baixa de OP/OF.
2183=Cliente bloqueado por existir {0,number} duplicata(s) vencida(s) a mais de {1,number} dia(s), somando um valor total de {2,number,currency}.
2184=Cliente desbloqueado por não existirem mais dívidas pendentes para o período.
2185=<html>O Tipo de Frete selecionado exige a geração de duplicatas.</br> O valor total das duplicatas não confere com o valor total da Nota fiscal</html>
2186=Já exitem pagamentos para este lançamento.
2187=O processo {0} necessita da definição do Parametro de Sistema: {1}.
2188=O cancelamento do Orçamento exige um motivo cancelamento.
2189=Lote obrigatório para formulação {0}
2190=A data de {0} não deve ser menor da data de {1}
2191=Não é possível cancelar uma nota de serviço se já foi baixada a duplicata referente a mesma.
2193=Quantidade prescrita deve ser maior que zero.
2194=A data de {0} deve ser menor que a data {1}.
2195=Tipo de Encaminhamento informado não suportado.
2207=A data de entrada não pode ser maior que a data atual.