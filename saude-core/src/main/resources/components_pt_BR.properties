# Arquivo de resource bundle para utilizacao pelos LAF

#Rotulo para FileChooser.fileNameLabelText
FileChooser.lookInLabelText=Examinar

#Rotulo para FileChooser.fileNameLabelText
FileChooser.fileNameLabelText=Nome do arquivo\:

#Rotulo para FileChooser.filesOfTypeLabelText
FileChooser.filesOfTypeLabelText=Arquivos do tipo\:

#Rotulo para Um Nivel Acima
FileChooser.upFolderToolTipText=Um Nível Acima
#Rotulo para FileChooser.upFolderAccessibleName
FileChooser.upFolderAccessibleName=Um Nível Acima

#Rotulo para FileChooser.homeFolderToolTipText
FileChooser.homeFolderToolTipText=Início

#Rotulo para FileChooser.homeFolderAccessibleName
FileChooser.homeFolderAccessibleName=Início

#Rotulo para FileChooser.newFolderToolTipText
FileChooser.newFolderToolTipText=Criar nova pasta

#Rotulo para FileChooser.newFolderAccessibleName
FileChooser.newFolderAccessibleName=Nova pasta

#Rotulo para FileChooser.listViewButtonToolTipText
FileChooser.listViewButtonToolTipText=Lista

#Rotulo para FileChooser.listViewButtonAccessibleName
FileChooser.listViewButtonAccessibleName=Lista

#Rotulo para FileChooser.detailsViewButtonToolTipText
FileChooser.detailsViewButtonToolTipText=Detalhes

#Rotulo para FileChooser.detailsViewButtonAccessibleName
FileChooser.detailsViewButtonAccessibleName=Detalhes

#Rotulo para FileChooser.cancelButtonText
FileChooser.cancelButtonText=Cancelar

#Rotulo para FileChooser.cancelButtonToolTipText
FileChooser.cancelButtonToolTipText=Aborta seleção de arquivo corrente

#Rotulo para FileChooser.openButtonText
FileChooser.openButtonText=Abrir

#Rotulo para FileChooser.saveButtonText
FileChooser.saveButtonText=Salvar

#Rotulo para FileChooser.saveButtonToolTipText
FileChooser.saveButtonToolTipText=Salvar arquivo selecionado

#Rotulo para FileChooser.newFolderErrorText
FileChooser.newFolderErrorText=Erro criando nova pasta

#Rotulo para FileChooser.saveButtonToolTipText
FileChooser.openButtonToolTipText=Abrir arquivo selecionado

#Rotulo para FileChooser.fileDescriptionText
FileChooser.fileDescriptionText=Arquivo genérico

#Rotulo para FileChooser.directoryDescriptionText
FileChooser.directoryDescriptionText=Diretório

#Rotulo para FileChooser.saveDialogTitleText
FileChooser.saveDialogTitleText=Salvar

#Rotulo para FileChooser.saveDialogTitleText
FileChooser.openDialogTitleText=Abrir

#Rotulo para FileChooser.updateButtonText
FileChooser.updateButtonText=Atualizar

#Rotulo para FileChooser.helpButtonText
FileChooser.helpButtonText=Ajuda

#Rotulo para FileChooser.directoryOpenButtonText
FileChooser.directoryOpenButtonText=Abrir

#Rotulo para FileChooser.updateButtonToolTipText
FileChooser.updateButtonToolTipText=Atualizar listagem de diretórios

#Rotulo para FileChooser.helpButtonToolTipText
FileChooser.helpButtonToolTipText=Ajuda

#Rotulo para FileChooser.directoryOpenButtonToolTipText
FileChooser.directoryOpenButtonToolTipText=Abrir diretório selecionado

#Rotulo para FileChooser.acceptAllFileFilterText
FileChooser.acceptAllFileFilterText=Todos os arquivos

#Rotulo para FileChooser.saveInLabelText
FileChooser.saveInLabelText=Salvar em\:

#Rotulo para FileChooser.fileNameHeaderText
FileChooser.fileNameHeaderText=Nome

#Rotulo para FileChooser.fileSizeHeaderText
FileChooser.fileSizeHeaderText=Tamanho

#Rotulo para FileChooser.fileTypeHeaderText
FileChooser.fileTypeHeaderText=Tipo

#Rotulo para FileChooser.fileDateHeaderText
FileChooser.fileDateHeaderText=Data de modificação

#Rotulo para FileChooser.fileAttrHeaderText
FileChooser.fileAttrHeaderText=Atributos

#Rotulo para OptionPane.yesButtonText
OptionPane.yesButtonText=Sim

#Rotulo para OptionPane.noButtonText
OptionPane.noButtonText=Não

#Rotulo para OptionPane.cancelButtonText
OptionPane.cancelButtonText=Cancelar

#Rotulo para OptionPane.yesButtonMnemonic
OptionPane.yesButtonMnemonic=83

#Rotulo para OptionPane.cancelButtonMnemonic
OptionPane.cancelButtonMnemonic=67

#Rotulo para OptionPane.okButtonMnemonic
OptionPane.okButtonMnemonic=79

#Rotulo para OptionPane.messageDialogTitle
OptionPane.messageDialogTitle=Mensagem

#Rotulo para OptionPane.inputDialogTitle
OptionPane.inputDialogTitle=Entrada de dados

#Rotulo para "OptionPane.titleText
OptionPane.titleText=Selecione uma opção
