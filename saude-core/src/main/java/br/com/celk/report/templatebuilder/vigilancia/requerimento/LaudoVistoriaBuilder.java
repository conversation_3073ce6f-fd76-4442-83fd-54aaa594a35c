package br.com.celk.report.templatebuilder.vigilancia.requerimento;

import br.com.celk.report.HtmlFile;
import br.com.celk.report.template.HtmlTemplate;
import br.com.celk.report.templatebuilder.HtmlTemplateBuilderImpl;

public class LaudoVistoriaBuilder extends HtmlTemplateBuilderImpl {
    @Override
    public void buildTemplate() {

    }

    @Override
    public HtmlFile template() {
        return HtmlTemplate.LAUDO_VISTORIA_VIGILANCIA.htmlFile();
    }
}
