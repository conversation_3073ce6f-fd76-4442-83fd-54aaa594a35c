package br.com.celk.report;

import br.com.celk.report.template.HtmlTemplate;
import br.com.ksisolucoes.ResourceReferenceLocation;
import br.com.ksisolucoes.util.log.Loggable;

import java.io.InputStream;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class HtmlFile implements Serializable {

    private String content;

    private HtmlFile(String content) {
        this.content = content;
    }

    public static HtmlFile newInstance(HtmlTemplate htmlTemplate) {
        try {
            InputStream is = ResourceReferenceLocation.class.getResourceAsStream(htmlTemplate.fileName());

            byte[] b = new byte[is.available()];
            is.read(b);
            String content = new String(b);

            return new HtmlFile(content);
        } catch (Exception ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        return null;
    }

    public String content() {
        return content;
    }

}
