package br.com.celk.singleton;

import javax.ejb.Lock;
import javax.ejb.LockType;
import javax.ejb.Singleton;
import javax.naming.InitialContext;
import javax.naming.NamingException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Singleton
public class LocalCacheInovamfriSingleton {

    private Map<String, Object> sharedCache = new HashMap<String, Object>();

    public Object getCache(String id) {
        return sharedCache.get(id);
    }

    @Lock(LockType.WRITE)
    public void saveCache(String id, Object recurso) {
        if (sharedCache.get(id) == null) {
            sharedCache.put(id, recurso);
        }
    }

    public void clearCache(String id) {
        sharedCache.remove(id);
    }

    public static LocalCacheInovamfriSingleton lookup() throws NamingException {
        return (LocalCacheInovamfriSingleton) new InitialContext().lookup("java:app/saude-core/" + LocalCacheInovamfriSingleton.class.getSimpleName());
    }
}
