package br.com.celk.integracao.fcm.core;

import de.bytefish.fcmjava.model.enums.PriorityEnum;
import de.bytefish.fcmjava.model.options.FcmMessageOptions;

/**
 * <AUTHOR>
 */

public class CustomTopicUnicastMessage extends de.bytefish.fcmjava.requests.topic.TopicUnicastMessage {
    private static final FcmMessageOptions OPTIONS = FcmMessageOptions
            .builder()
            .setDelayWhileIdle(true)
            .setPriorityEnum(PriorityEnum.High)
            .build();
    public CustomTopicUnicastMessage(FCMMessage fcmMessage) {
        super(OPTIONS, fcmMessage.getTopic(), fcmMessage.getData());
    }


}
