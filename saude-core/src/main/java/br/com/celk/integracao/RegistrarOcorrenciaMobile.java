package br.com.celk.integracao;

import br.com.celk.integracao.dto.ResourceProcessDTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.server.interfaces.facade.HibernateSessionFactoryFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.AbstractSessaoAplicacao;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoContext;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.integracao.mobile.IntegracaoMobile;
import br.com.ksisolucoes.vo.integracao.mobile.IntegracaoMobileItem;
import br.com.ksisolucoes.vo.integracao.mobile.IntegracaoMobileOcorrencia;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.hibernate.Session;

import java.io.IOException;
import java.util.Date;
import java.util.List;

public class RegistrarOcorrenciaMobile extends AbstractCommandTransaction {

    private Exception exception;
    private IntegracaoMobileItem vo;
    private String acao;

    public RegistrarOcorrenciaMobile(IntegracaoMobileItem vo, String acao) {
        this.vo = vo;
        this.acao = acao;
    }

    public RegistrarOcorrenciaMobile(IntegracaoMobileItem vo, String acao, Exception exception){
        this.vo = vo;
        this.acao = acao;
        this.exception = exception;
    }

    private void registrarOcorrencia() {
        AbstractSessaoAplicacao sessaoAplicacao = (AbstractSessaoAplicacao) SessaoAplicacaoContext.getContext();
        Session session = BOFactory.getBO(HibernateSessionFactoryFacade.class).getSession();
        IntegracaoMobileOcorrencia ocorrencia = new IntegracaoMobileOcorrencia();
        ocorrencia.setIntegracaoMobileItem(vo);
        ocorrencia.setUsuario(sessaoAplicacao.getUsuario());
        ocorrencia.setDataOcorrencia(new Date());
        try {
            StringBuilder sb = new StringBuilder();

            vo = reloadVo(vo);
            setDescricao(sb, vo, ocorrencia, acao);
            getCodigoMobile(sb, vo, session);

            if (vo.getGerenciadorArquivo() != null) {
                //Mantem o histórico dos arquivos alterados
                ocorrencia.setGerenciadorArquivo(vo.getGerenciadorArquivo());
            }

            BOFactory.newTransactionSave(ocorrencia);
        } catch (IOException | DAOException e) {
            Loggable.log.error(e);

        } catch (ValidacaoException e) {
            Loggable.log.warn(e);

        }
    }

    private void getCodigoMobile(StringBuilder sb, IntegracaoMobileItem vo, Session session) throws IOException {
        //Se houver o Json da mensagem
        if (vo.getMsgRecurso() != null) {
            //Pega a lista de mensagens do recurso
            List<ResourceProcessDTO> listRecursoDTO = fromJsonString(vo.getMsgRecurso(), new TypeReference<List<ResourceProcessDTO>>() {
            });

            //E mostra seus dados
            for (ResourceProcessDTO recursoDTO : listRecursoDTO) {
                if (recursoDTO.getCodigoLote() != null) {
                    vo.setIntegracaoMobile((IntegracaoMobile) session.get(IntegracaoMobile.class, recursoDTO.getCodigoLote()));
                    sb.append("Código Mobile: ")
                            .append(recursoDTO.getCodigoMobile())
                            .append("\n\n");
                    break;
                }
            }
        }
    }

    public IntegracaoMobileItem reloadVo(IntegracaoMobileItem vo) throws ValidacaoException, DAOException {

        IntegracaoMobileItem retorno = null;
        if (vo.getCodigo() != null) {
            retorno = LoadManager.getInstance(IntegracaoMobileItem.class)
                    .addProperties(new HQLProperties(IntegracaoMobileItem.class).getProperties())
                    .addProperties(new HQLProperties(IntegracaoMobile.class, VOUtils.montarPath(IntegracaoMobileItem.PROP_INTEGRACAO_MOBILE)).getProperties())
                    .setId(vo.getCodigo())
                    .startNewTransaction()
                    .getVO();
        }
        return retorno;

    }

    private void setDescricao(StringBuilder sb, IntegracaoMobileItem vo, IntegracaoMobileOcorrencia ocorrencia, String acao) {
        sb.append("Ação: ")
                .append(acao)
                .append("\n\n")
                .append("Número do Lote: ")
                .append(vo.getIntegracaoMobile().getCodigo())
                .append("\n\n")
                .append("Status do Lote: ")
                .append(vo.getIntegracaoMobile().getDescricaoStatus())
                .append("\n\n")
                .append("Profissional: ")
                .append(vo.getIntegracaoMobile().getProfissional().getDescricaoFormatado())
                .append("\n\n")
                .append("Recurso: ")
                .append(vo.getNomeRecurso())
                .append("\n\n")
                .append("Status do Recurso: ")
                .append(vo.getDescricaoStatus())
                .append(" \n\n");

        if (vo.getDescricaoMsg() != null) {
            sb.append("Descrição: ")
                    .append(vo.getDescricaoMsg())
                    .append("\n\n");
        }else if(exception != null){
            sb.append("Mensagem: ")
                    .append(ExceptionUtils.getFullStackTrace(exception))
                    .append("\n\n");
        }


        ocorrencia.setDescricao(sb.toString());
    }

    private <T> T fromJsonString(String object, TypeReference<T> tr) throws IOException {
        if (object == null) {
            return null;
        }
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(object, tr);

    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        registrarOcorrencia();

    }
}
