package br.com.celk.integracao.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;

public class CredenciaisRndsDto implements Serializable {

    private String tenant;
    private String passwordCertificate;
    private String ativo;
    private String ufSolicitante;
    private String cnsSolicitante;
    private Long idSolicitante;
    private String certificado;

    @JsonIgnore
    private boolean isNew;

    public String getTenant() {
        return tenant;
    }

    public void setTenant(String tenant) {
        this.tenant = tenant;
    }

    public String getPasswordCertificate() {
        return passwordCertificate;
    }

    public void setPasswordCertificate(String passwordCertificate) {
        this.passwordCertificate = passwordCertificate;
    }

    public String getAtivo() {
        return ativo;
    }

    public void setAtivo(String ativo) {
        this.ativo = ativo;
    }

    public String getUfSolicitante() {
        return ufSolicitante;
    }

    public void setUfSolicitante(String ufSolicitante) {
        this.ufSolicitante = ufSolicitante;
    }

    public String getCnsSolicitante() {
        return cnsSolicitante;
    }

    public void setCnsSolicitante(String cnsSolicitante) {
        this.cnsSolicitante = cnsSolicitante;
    }

    public Long getIdSolicitante() {
        return idSolicitante;
    }

    public void setIdSolicitante(Long idSolicitante) {
        this.idSolicitante = idSolicitante;
    }

    public String getCertificado() {
        return certificado;
    }

    public void setCertificado(String certificado) {
        this.certificado = certificado;
    }

    public boolean isNew() {
        return isNew;
    }

    public void setNew(boolean aNew) {
        isNew = aNew;
    }
}
