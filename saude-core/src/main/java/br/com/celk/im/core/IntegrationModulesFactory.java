/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.celk.im.core;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.core.type.classreading.SimpleMetadataReaderFactory;

import br.com.ksisolucoes.dao.exception.DAOException;

/**
 *
 * <AUTHOR>
 */
public class IntegrationModulesFactory {
    
    public static void main(String[] args) throws DAOException {
        new IntegrationModulesFactory().getAbstractIntregratingModules(null);
    }
    
    public List<TriggerIntregratingModules> getAbstractIntregratingModules(Class classFrom) throws DAOException {
        List<TriggerIntregratingModules> triggerIntregratingModuleses = new ArrayList();
        try {
            PathMatchingResourcePatternResolver pathMatchingResourcePatternResolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = pathMatchingResourcePatternResolver.getResources("classpath*:br/com/celk/im/integracoes/**/*.class");
            SimpleMetadataReaderFactory simpleMetadataReaderFactory = new SimpleMetadataReaderFactory();
            for (Resource resource : resources) {
                MetadataReader metadataReader = simpleMetadataReaderFactory.getMetadataReader(resource);
                AnnotationMetadata annotationMetadata = metadataReader.getAnnotationMetadata();
                Set<String> annotationTypes = annotationMetadata.getAnnotationTypes();
                if (annotationTypes.contains(IMFromTo.class.getName())) {
                    Class<?> clazz = Class.forName(metadataReader.getClassMetadata().getClassName());
                    IMFromTo annotation = clazz.getAnnotation(IMFromTo.class);
                    if (annotation.from().equals(classFrom)) {
                        TriggerIntregratingModules aim = (TriggerIntregratingModules)clazz.newInstance();
                        aim.builder();
                        triggerIntregratingModuleses.add(aim);
                    }
                }
            }
        } catch (IllegalAccessException ex) {
            throw new DAOException(ex);
        } catch (InstantiationException ex) {
            throw new DAOException(ex);
        } catch (ClassNotFoundException ex) {
            throw new DAOException(ex);
        } catch (IOException ex) {
            throw new DAOException(ex);
        }
        return triggerIntregratingModuleses;
    }
}
