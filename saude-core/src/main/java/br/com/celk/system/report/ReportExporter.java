package br.com.celk.system.report;

/**
 *
 * <AUTHOR>
 */

import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.util.log.Loggable;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JRExporter;
import net.sf.jasperreports.engine.JRExporterParameter;
import net.sf.jasperreports.engine.export.JRCsvExporterParameter;
import net.sf.jasperreports.engine.export.JRHtmlExporterParameter;
import net.sf.jasperreports.engine.export.JRTextExporterParameter;
import net.sf.jasperreports.engine.export.JRXlsExporterParameter;

import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class ReportExporter implements Serializable {

    private final TipoRelatorio tipoRelatorio;
    private final DataReport dataReport;

    public ReportExporter(DataReport dataReport, TipoRelatorio tipoRelatorio) {
        this.dataReport = dataReport;
        this.tipoRelatorio = tipoRelatorio;
    }

    public File createFile() throws IOException {
        if(dataReport.getJasperPrint() != null){
            try {
                JRExporter exporter = null;
                File newFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), tipoRelatorio.descricao());

                if (tipoRelatorio.equals(TipoRelatorio.HTML)) {
                    Map imageMap = new HashMap();

                    exporter = new net.sf.jasperreports.engine.export.JRHtmlExporter();
                    exporter.setParameter(JRHtmlExporterParameter.IS_USING_IMAGES_TO_ALIGN, Boolean.FALSE);
                    exporter.setParameter(JRHtmlExporterParameter.IMAGES_MAP, imageMap);
                    exporter.setParameter(JRHtmlExporterParameter.IMAGES_URI, "image?image=");
                    exporter.setParameter(JRHtmlExporterParameter.IMAGES_DIR_NAME, "/tmp/images");
                    exporter.setParameter(JRHtmlExporterParameter.IS_OUTPUT_IMAGES_TO_DIR, Boolean.TRUE);

                }else if (tipoRelatorio.equals(TipoRelatorio.XLS) || tipoRelatorio.equals(TipoRelatorio.XLS2)) {
                    exporter = new net.sf.jasperreports.engine.export.JRXlsExporter();
                    exporter.setParameter(JRXlsExporterParameter.MAXIMUM_ROWS_PER_SHEET, 65536);

                }else if (tipoRelatorio.equals(TipoRelatorio.CSV)) {
                    exporter = new net.sf.jasperreports.engine.export.JRCsvExporter();
                    exporter.setParameter(JRCsvExporterParameter.CHARACTER_ENCODING, "UTF-8");
                    exporter.setParameter(JRCsvExporterParameter.FIELD_DELIMITER, ";");

                }else if (tipoRelatorio.equals(TipoRelatorio.TXT)) {
                    exporter = new net.sf.jasperreports.engine.export.JRTextExporter();
                    exporter.setParameter(JRTextExporterParameter.CHARACTER_WIDTH, 7f);
                    exporter.setParameter(JRTextExporterParameter.CHARACTER_HEIGHT, 13f);

                } else if (tipoRelatorio.equals(TipoRelatorio.RTF)) {
                    exporter = new net.sf.jasperreports.engine.export.JRRtfExporter();

                } else {
                    exporter = new net.sf.jasperreports.engine.export.JRPdfExporter();
                }

                exporter.setParameter(JRExporterParameter.OUTPUT_FILE, newFile);
                exporter.setParameter(JRExporterParameter.JASPER_PRINT, dataReport.getJasperPrint());
                exporter.exportReport();

                return newFile;
            } catch (JRException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        }else{
            return dataReport.getFilePrint();
        }
        
        return null;
    }
}
