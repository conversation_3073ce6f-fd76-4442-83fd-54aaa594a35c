package br.com.celk.lgpd;

import br.com.ksisolucoes.system.sessao.AbstractSessaoAplicacao;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.controle.Usuario;
import java.io.Serializable;


/**
 *
 * <AUTHOR>
 */
public abstract class AbstractLgpdFilter<T> implements Serializable{
    
    private Boolean userDenied;

    protected abstract T convert(T value);

    private boolean isUserDenied() {
        if (userDenied == null) {
            userDenied = false;
            if ("true".equals(RepositoryComponentDefault.SystemProperty.APLICAR_FILTRO_LGPD.value())) {
                AbstractSessaoAplicacao sessaoAplicacao = SessaoAplicacaoImp.getInstance();
                if(sessaoAplicacao != null && sessaoAplicacao.getUsuario() != null){
                    Usuario usuario = sessaoAplicacao.getUsuario();
                    if (Usuario.FuncionarioCelk.valueOf(usuario.getFuncionarioCelk()).value() > Usuario.FuncionarioCelk.NAO_FUNCIONARIO.value()) {
                        userDenied = true;
                    }
                }
            }
        }
        return userDenied;
    }
    
    public T value(T value){
        if(isUserDenied()){
            value = convert(value);
        }
        return (T) value;
    } 
}
