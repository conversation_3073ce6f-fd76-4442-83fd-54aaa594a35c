package br.com.celk.cluster.interceptor;

import br.com.celk.interceptor.hibernate.CelkObjectAccessDeniedException;
import br.com.ksisolucoes.dao.exception.ConcurrentDAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.hibernate.StaleObjectStateException;
import org.hibernate.exception.ConstraintViolationException;
import org.jboss.ejb.client.EJBClientInterceptor;
import org.jboss.ejb.client.EJBClientInvocationContext;

import javax.ejb.EJBTransactionRolledbackException;
import java.sql.BatchUpdateException;

/**
 *
 * <AUTHOR>
 */
public class ClientInterceptorError implements EJBClientInterceptor {

    @Override
    public void handleInvocation(EJBClientInvocationContext context) throws Exception {
        try {
            context.sendRequest();
        } catch (Exception exception) {
            String message = exception.getMessage();

            if (exception instanceof EJBTransactionRolledbackException) {
                if (exception.getCause() != null && exception.getCause().getCause() != null && exception.getCause().getCause().getCause() != null) {
                    if (exception.getCause().getCause().getCause() instanceof StaleObjectStateException) {
                        throw new ConcurrentDAOException(exception);
                    }
                    if (exception.getCause().getCause().getCause() instanceof CelkObjectAccessDeniedException) {
                        throw new ValidacaoException(exception.getCause().getCause().getCause().getMessage());
                    }

                    if (exception.getCause().getCause().getCause().getCause() != null) {

                        if (exception.getCause().getCause().getCause() instanceof ConstraintViolationException) {
                            message = exception.getCause().getCause().getCause().getCause().getMessage();
                        } else if (exception.getCause().getCause().getCause().getCause() instanceof BatchUpdateException) {
                            message = ((BatchUpdateException) exception.getCause().getCause().getCause().getCause()).getNextException().getMessage();
                        }

                        if (message != null && message.contains("idx_referencia")) {
                            throw new ValidacaoException(Bundle.getStringApplication("msg_ja_existe_um_registro_cadastrado_com_a_mesma_referencia"));
                        } else if (message != null && message.contains("violates foreign key constraint")) {
                            throw new ValidacaoException(Bundle.getStringApplication("msg_registro_nao_pode_ser_excluido"));
                        } else {
                            throw new ValidacaoException(message);
                        }
                    }
                }
            }

            throw exception;
        }
    }

    @Override
    public Object handleInvocationResult(EJBClientInvocationContext context) throws Exception {
        return context.getResult();
    }
}
