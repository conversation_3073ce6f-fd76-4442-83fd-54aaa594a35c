package br.com.ksisolucoes.reflection;

import java.util.HashMap;
import java.util.Map;

public final class ClassesCache {

   private static Map cache = new HashMap();

   static {
      cache.put(Byte.TYPE.getName(), Byte.TYPE);
      cache.put(Character.TYPE.getName(), Character.TYPE);
      cache.put(Short.TYPE.getName(), Short.TYPE);
      cache.put(Integer.TYPE.getName(), Integer.TYPE);
      cache.put(Long.TYPE.getName(), Long.TYPE);
      cache.put(Float.TYPE.getName(), Float.TYPE);
      cache.put(Double.TYPE.getName(), Double.TYPE);
      cache.put(Boolean.TYPE.getName(), Boolean.TYPE);
      cache.put(Void.TYPE.getName(), Void.TYPE);
   }

   public static Class getClass(String className) throws ClassNotFoundException {
      Class clazz = (Class) cache.get(className);

      if (clazz == null) {
         clazz = Class.forName(className);
         cache.put(className, clazz);
      }

      return clazz;
   }

}