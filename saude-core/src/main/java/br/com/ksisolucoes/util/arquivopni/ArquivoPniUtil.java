package br.com.ksisolucoes.util.arquivopni;

import br.com.ksisolucoes.io.FileUtils;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import org.apache.commons.lang.StringUtils;

import java.io.*;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import static br.com.ksisolucoes.util.Data.competenciaData;

/**
 *
 * <AUTHOR>
 */
public class ArquivoPniUtil {
    
    public static String baixar(String path) {
        if (StringUtils.trimToNull(path) != null) {
            try {
                File tempDir = File.createTempFile("DOWNLOAD_TEMP", Long.toString(System.nanoTime()));

                tempDir.delete();
                tempDir.mkdir();

                String nomeArquivo = path.substring(path.lastIndexOf("/"), path.length());
                
                FileUtils.buscarArquivoFtp(path, tempDir.getAbsolutePath()+ "/"+nomeArquivo);
                return tempDir.getAbsolutePath()+ "/"+nomeArquivo;
            } catch (Throwable ex) {
                Loggable.log.error(ex.getMessage());
            }
        }
        return null;
    }

    public static void remover(String path) {
        try {
            if (StringUtils.trimToNull(path) != null) {
                FileUtils.excluirArquivoFtpSeExiste(path);
            }
        } catch (Throwable ex) {
            Loggable.log.error(ex.getMessage());
        }
    }
    
    public static File criarArquivo(String tipoArquivo, String cnes, String texto) throws IOException, ValidacaoException {
        File file;
        if(cnes != null){
            file = new File(tipoArquivo + cnes + ".DAT");            
        } else {
            file = new File(tipoArquivo + ".DAT");
        }
        file.createNewFile();

        BufferedWriter bufferedWriter = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file), "ISO-8859-1"));
        bufferedWriter.write(texto);
        bufferedWriter.close();
        
        return file;
    }
    
    public static String salvaFtp(Date dataGeracao, List<File> fileList, Date competencia, String siglaSistemaOrigem, String senhaSistemaOrigem) throws ValidacaoException, IOException {
        String caminho = "pni";

        File tempZip = File.createTempFile("pni", ".zip");
        br.com.ksisolucoes.io.FileUtils.ziparAES(tempZip.getPath(), fileList, senhaSistemaOrigem, FileUtils.AES_STRENGTH_256);

        String codigoIbge = new DecimalFormat("000000").format(SessaoAplicacaoImp.getInstance().<Empresa>getEmpresa().getCidade().getCodigo());
        
        String pathArquivo = caminho + "/M-" + codigoIbge + "-" + new SimpleDateFormat("yyyyMM").format(competenciaData(1, competencia)) + "-"
                + new SimpleDateFormat("ddMMyyyyHHmmss").format(dataGeracao) + "-" + siglaSistemaOrigem + ".PNI";
        
        FileUtils.enviarArquivoFtp(tempZip.getAbsolutePath(), pathArquivo, null);
        
        for (File file : fileList) {
            file.delete();
        }
            
        tempZip.delete();
            
        return pathArquivo;
    }

    public static String salvaFtp(List<File> fileList,Date competencia) throws ValidacaoException, IOException {
        String caminho = "pni";

        File tempZip = File.createTempFile("SIPNI", ".zip");
        br.com.ksisolucoes.io.FileUtils.zipar(tempZip.getPath(), fileList);

        String pathArquivo = new StringBuilder(caminho).append("/SIPNI").append(new SimpleDateFormat("yyyyMM").format(competenciaData(1, competencia))).append(".zip").toString();
        //String pathArquivo = caminho + "/SIPNI.zip";

        FileUtils.enviarArquivoFtp(tempZip.getAbsolutePath(), pathArquivo, null);

        for (File file : fileList) {
            file.delete();
        }

        tempZip.delete();

        return pathArquivo;
    }
    
}
