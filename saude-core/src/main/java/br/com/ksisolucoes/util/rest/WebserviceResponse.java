package br.com.ksisolucoes.util.rest;

import br.com.ksisolucoes.util.Bundle;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;

public class WebserviceResponse {

    private int statusCode;
    private String responseMessage;

    public WebserviceResponse() {
    }

    public WebserviceResponse(int statusCode, String responseMessage) {
        this.statusCode = statusCode;
        this.responseMessage = responseMessage;
    }

    public WebserviceResponse(HttpURLConnection connection) throws IOException {
        this.statusCode = connection.getResponseCode();

        InputStream inputStream;
        if (connection.getResponseCode() >= 200 && connection.getResponseCode() < 400)
            inputStream = connection.getInputStream();
        else inputStream = connection.getErrorStream();

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
            StringBuffer response = new StringBuffer();
            String inputLine;
            while ((inputLine = reader.readLine()) != null) {
                response.append(inputLine);
            }
            this.responseMessage = response.toString();
        } catch (NullPointerException e) {
            this.responseMessage = Bundle.getStringApplication("Falha_ao_receber_resposta_de_mensagem");
        } finally {
            if (inputStream != null) inputStream.close();
        }
    }

    public int getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }

    public String getResponseMessage() {
        return responseMessage;
    }

    public void setResponseMessage(String responseMessage) {
        this.responseMessage = responseMessage;
    }

    public boolean isSuccess(int statusCode) {
        return (statusCode >= 200 && statusCode < 300);
    }

    public boolean isSuccess() {
        return (statusCode >= 200 && statusCode < 300);
    }
}
