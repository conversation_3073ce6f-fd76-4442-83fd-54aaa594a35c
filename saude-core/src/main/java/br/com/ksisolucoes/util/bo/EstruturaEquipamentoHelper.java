package br.com.ksisolucoes.util.bo;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.AbstractSessaoAplicacao;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import br.com.ksisolucoes.vo.entradas.estoque.Unidade;
import br.com.ksisolucoes.vo.geral.EstruturaEquipamento;
import br.com.ksisolucoes.vo.geral.EstruturaEquipamentoPK;
import br.com.ksisolucoes.vo.geral.interfaces.EstruturaEquipamentoInterface;

/**
 *
 * <AUTHOR> Graciano
 */
public class EstruturaEquipamentoHelper {

    public static final void antesSaveProcessor(EstruturaEquipamentoInterface estruturaEquipamentoInterface, Class clazz, AbstractSessaoAplicacao sessao) throws DAOException, ValidacaoException {

//        /*
//         * VALIDACAO
//         * ---------
//         * Caso o componente seja Fabricado este deve possuir estrutura.
//         * A ponta da estrutura nunca pode ser de procedencia Fabricada.
//         *---------------------------------------------------------------------*/
//        VerificarExistenciaEstruturaEquipamento v = new VerificarExistenciaEstruturaEquipamento(estruturaEquipamentoInterface.getId().getComponente());
//        v.start(sessao);
//
//        if( estruturaEquipamentoInterface.getId().getComponente().isFabricado() && !v.isExistenciaEstruturaEquipamento() ) {
//            // TODO Fabricio - Colocar Bundle
//            throw new ValidacaoException("Nao  possvel inserir um componente Fabricado sem estrutura definida.");
//        }
//        /*--------------------------------------------------------------------*/

        /*
         * VALIDACAO
         * ---------
         * Validao da quantidade da CopiaEstruturaEquipamento em relao a unidade
         *  do produto.
         *---------------------------------------------------------------------*/
        UnidadeHelper.validarValorRefUnidade(estruturaEquipamentoInterface.getQuantidade(), estruturaEquipamentoInterface.getId().getComponente());
        /*--------------------------------------------------------------------*/

        /*
         * VALIDACAO
         * ---------
         * A id deve ser preenchida.
         *---------------------------------------------------------------------*/
        if (estruturaEquipamentoInterface.getId() == null ||
                estruturaEquipamentoInterface.getId().getProduto() == null ||
                estruturaEquipamentoInterface.getId().getComponente() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_preenchimento_produto_componente_obrigatorio"));
        }
        /*--------------------------------------------------------------------*/

        /*
         * DATA DO LANAMENTO DA ESTRUTURA
         * -------------------------------
         * . Data do momento do lanamento
         *---------------------------------------------------------------------*/
        estruturaEquipamentoInterface.setDataLancamento(Data.getDataAtual());
        /*---------------------------------------------------------------------*/

        /*
         * PROCEDENCIA DO PRODUTO
         * ----------------------
         * 1. O comprimento / largura pode ser informado ou no apenas caso o produto seja
         *    uma materia prima
         *---------------------------------------------------------------------*/
//        Produto componente = estruturaEquipamentoInterface.getId().getComponente();
//        if (!getProcedenciaToProduto(componente).getSigla().equals(Procedencia.MATERIA_PRIMA)) {
//            Double comprimento = estruturaEquipamentoInterface.getComprimento();
//            Double largura = estruturaEquipamentoInterface.getLargura();
//            if ((comprimento != null && comprimento.doubleValue() > 0D) || (largura != null && largura.doubleValue() > 0D)) {
//                throw new ValidacaoException(Bundle.getStringApplication("msg_comprimento_largura_produto_definido_apenas_materia_prima", estruturaEquipamentoInterface.getId().getProduto().getDescricaoFormatado(), estruturaEquipamentoInterface.getId().getComponente().getDescricaoFormatado()));
//            }
//        }
        /*--------------------------------------------------------------------*/

        /*
         * DADOS AUDITORIA
         * ---------------
         *---------------------------------------------------------------------*/
        estruturaEquipamentoInterface.setDataUsuario(Data.getDataAtual());
        estruturaEquipamentoInterface.setUsuario(sessao.<Usuario>getUsuario());
    /*--------------------------------------------------------------------*/
    }

    /**
     * Retorna a properties que seram utilizados para tela de cadastro de estrutura equipamento.
     */
    public static final String[] getUsedPropertiesEstruturaEquipamentoToCadastroEstruturaSimples() {
        String[] props = {
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_CODIGO),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_REFERENCIA),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_DESCRICAO),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_PESO_ESPECIFICO),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_CODIGO),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_REFERENCIA),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_DESCRICAO),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_PESO_ESPECIFICO),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_ITEM),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_STATUS),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_TAMANHO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ESPECIFICACAO_PINTURA, EspecificacaoPintura.PROP_CODIGO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ESPECIFICACAO_PINTURA, EspecificacaoPintura.PROP_DESCRICAO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ESPECIFICACAO_PINTURA, EspecificacaoPintura.PROP_REFERENCIA),
            //            VOUtils.montarPath(EstruturaEquipamento.PROP_QUANTIDADE_MILIMETRO),
            //            VOUtils.montarPath(EstruturaEquipamento.PROP_QUANTIDADE_EXTRA),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ITEM_SUBSTITUIVEL),
            //            VOUtils.montarPath(EstruturaEquipamento.PROP_MAQUINA_PADRAO, Maquina.PROP_CODIGO),
            //            VOUtils.montarPath(EstruturaEquipamento.PROP_MAQUINA_PADRAO, Maquina.PROP_DESCRICAO),
            VOUtils.montarPath(EstruturaEquipamento.PROP_DATA_LANCAMENTO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_REVISAO),
            //            VOUtils.montarPath(EstruturaEquipamento.PROP_USUARIO, Usuario.PROP_CODIGO),
            //            VOUtils.montarPath(EstruturaEquipamento.PROP_USUARIO, Usuario.PROP_NOME),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_EMBARQUE),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_LARGURA),
            VOUtils.montarPath(EstruturaEquipamento.PROP_DATA_USUARIO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_FORMATO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_COMPRIMENTO),
            VOUtils.montarPath(EstruturaEquipamento.PROP_QUANTIDADE),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_PRIORIDADE_EMBARQUE),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_PENDENCIA),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_DIMENSAO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_QUANTIDADE_BRUTA),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_TIPO_ESTRUTURA)
        };

        return props;
    }

    public static final String[] getUsedPropertiesEstruturaEquipamentoToCadastroEstrutura() {

        String[] props = {
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_CODIGO),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_REFERENCIA),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_DESCRICAO),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_UNIDADE,Unidade.PROP_CODIGO),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_UNIDADE,Unidade.PROP_UNIDADE),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_PROCEDENCIA, Procedencia.PROP_CODIGO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_PROCEDENCIA, Procedencia.PROP_SIGLA),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_PROCEDENCIA, Procedencia.PROP_DESCRICAO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_PROCEDENCIA, Procedencia.PROP_MARGEM_VENDA),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO, GrupoProduto.PROP_CODIGO),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO, GrupoProduto.PROP_DESCRICAO),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_REVISAO_ESTRUTURA),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_REVISAO_ATUAL),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_STATUS_ESTRUTURA),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_USUARIO_ESTRUTURA, Usuario.PROP_CODIGO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_FLAG_KIT_MONTAGEM),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_PESO_ARTIGO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_PESO_ESPECIFICO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_PESO_EQUIPAMENTO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_PESO_LIQUIDO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_PESO_QUEIMADO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_PESO_SECO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_FLAG_EMBARQUE),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_CODIGO_ENGENHARIA),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_CODIGO_PRODUTO_FORMATADO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_PRODUTO, Produto.PROP_SITUACAO_ESTRUTURA),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_CODIGO_PRODUTO_GRUPO),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_CODIGO),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_REFERENCIA),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_DESCRICAO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_PROCEDENCIA, Procedencia.PROP_CODIGO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_PROCEDENCIA, Procedencia.PROP_SIGLA),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_PROCEDENCIA, Procedencia.PROP_DESCRICAO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_PROCEDENCIA, Procedencia.PROP_MARGEM_VENDA),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO, GrupoProduto.PROP_CODIGO),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO, GrupoProduto.PROP_DESCRICAO),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_UNIDADE, Unidade.PROP_CODIGO),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_UNIDADE, Unidade.PROP_FLAG_CASA_DECIMAL),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_LOCALIZACAO, Localizacao.PROP_CODIGO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_LOCALIZACAO, Localizacao.PROP_DESCRICAO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_LOCALIZACAO, Localizacao.PROP_FLAG_EMBARQUE),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_REVISAO_ESTRUTURA),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_STATUS_ESTRUTURA),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_FLAG_KIT_MONTAGEM),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_PESO_ARTIGO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_PESO_ESPECIFICO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_PESO_EQUIPAMENTO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_PESO_LIQUIDO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_PESO_QUEIMADO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_PESO_SECO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_FLAG_EMBARQUE),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_CODIGO_ENGENHARIA),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_PECAS_M2),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_SITUACAO_ESTRUTURA),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_REVISAO_ATUAL),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_COMPONENTE, Produto.PROP_REVISAO_ESTRUTURA_LIBERADA),
            VOUtils.montarPath(EstruturaEquipamento.PROP_ID, EstruturaEquipamentoPK.PROP_ITEM),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_STATUS),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_TAMANHO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ESPECIFICACAO_PINTURA, EspecificacaoPintura.PROP_CODIGO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ESPECIFICACAO_PINTURA, EspecificacaoPintura.PROP_DESCRICAO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ESPECIFICACAO_PINTURA, EspecificacaoPintura.PROP_REFERENCIA),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_QUANTIDADE_MILIMETRO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_QUANTIDADE_EXTRA),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_ITEM_SUBSTITUIVEL),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_MAQUINA_PADRAO, Maquina.PROP_CODIGO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_MAQUINA_PADRAO, Maquina.PROP_DESCRICAO),
            VOUtils.montarPath(EstruturaEquipamento.PROP_DATA_LANCAMENTO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_REVISAO),
            VOUtils.montarPath(EstruturaEquipamento.PROP_USUARIO, Usuario.PROP_CODIGO),
            VOUtils.montarPath(EstruturaEquipamento.PROP_USUARIO, Usuario.PROP_NOME),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_EMBARQUE),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_LARGURA),
            VOUtils.montarPath(EstruturaEquipamento.PROP_DATA_USUARIO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_FORMATO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_COMPRIMENTO),
            VOUtils.montarPath(EstruturaEquipamento.PROP_QUANTIDADE)
//            VOUtils.montarPath(EstruturaEquipamento.PROP_PRIORIDADE_EMBARQUE),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_PENDENCIA),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_FLAG_LOCALIZACAO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_DIMENSAO),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_QUANTIDADE_BRUTA),
//            VOUtils.montarPath(EstruturaEquipamento.PROP_TIPO_ESTRUTURA)
        };

        return props;
    }
}