/*
 * EstruturaEquipamentoHelper.java
 *
 * Created on 21 de Novembro de 2005, 08:55
 *
 * To change this template, choose Tools | Template Manager
 * and open the template in the editor.
 */

package br.com.ksisolucoes.util.bo;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.AbstractSessaoAplicacao;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.Unidade;
import br.com.ksisolucoes.vo.geral.EstruturaEquipamentoRevisao;
import br.com.ksisolucoes.vo.geral.EstruturaEquipamentoRevisaoPK;
import br.com.ksisolucoes.vo.geral.interfaces.EstruturaEquipamentoRevisaoInterface;

/**
 *
 * <AUTHOR> Graciano
 */
public class EstruturaEquipamentoRevisaoHelper {

    public static final void antesSaveProcessor( EstruturaEquipamentoRevisaoInterface estruturaEquipamentoInterface, Class clazz, AbstractSessaoAplicacao sessao ) throws DAOException, ValidacaoException{

        /*
         * VALIDACAO
         * ---------
         * Validao da quantidade da CopiaEstruturaEquipamento em relao a unidade
         *  do produto.
         *---------------------------------------------------------------------*/
        UnidadeHelper.validarValorRefUnidade(estruturaEquipamentoInterface.getQuantidade(), estruturaEquipamentoInterface.getId().getComponente());
        /*--------------------------------------------------------------------*/

        /*
         * VALIDACAO
         * ---------
         * A id deve ser preenchida.
         *---------------------------------------------------------------------*/
        if( estruturaEquipamentoInterface.getId() == null ||
                estruturaEquipamentoInterface.getId().getProduto() == null ||
                estruturaEquipamentoInterface.getId().getComponente() == null ) {
            throw new ValidacaoException( Bundle.getStringApplication( "msg_preenchimento_produto_componente_obrigatorio" ) );
        }
        /*--------------------------------------------------------------------*/

        /*
         * DATA DO LANAMENTO DA ESTRUTURA
         * -------------------------------
         * . Data do momento do lanamento
         *---------------------------------------------------------------------*/
        estruturaEquipamentoInterface.setDataLancamento( Data.getDataAtual() );
        /*---------------------------------------------------------------------*/

        /*
         * PROCEDENCIA DO PRODUTO
         * ----------------------
         * 1. O comprimento / largura pode ser informado ou no apenas caso o produto seja
         *    uma materia prima
         *---------------------------------------------------------------------*/
        Produto componente = estruturaEquipamentoInterface.getId().getComponente();
//        if( !getProcedenciaToProduto(componente).getSigla().equals(Procedencia.MATERIA_PRIMA) ) {
//            Double comprimento = estruturaEquipamentoInterface.getComprimento();
//            Double largura = estruturaEquipamentoInterface.getLargura();
//            if( ( comprimento != null && comprimento.doubleValue() > 0D ) || ( largura != null && largura.doubleValue() > 0D ) ) {
//                //TODO Fabricio - Colocar Bundle
//                throw new ValidacaoException("O comprimento ou largura so permitidos apenas caso o produto seja uma matria prima.");
//            }
//        }
        /*--------------------------------------------------------------------*/
        
        /*
         * QUANTIDADE
         * ----------------------
         * 1. Quantidade deve ser maior que 0
         *---------------------------------------------------------------------*/
        if(Coalesce.asDouble(estruturaEquipamentoInterface.getQuantidade()) == 0D){
            throw new ValidacaoException("Quantidade deve ser maior que 0.");
        }
        /*--------------------------------------------------------------------*/
        
        /*
         * DADOS AUDITORIA
         * ---------------
         *---------------------------------------------------------------------*/
        estruturaEquipamentoInterface.setDataUsuario(Data.getDataAtual());
        estruturaEquipamentoInterface.setUsuario((Usuario)sessao.getUsuario());
        /*--------------------------------------------------------------------*/
    }

    /**
     * Retorna a <code>Procedencia</code> do <code>Produto</code>.
     * Utilizado para simplificar a chamada da <code>Procedencia</code>.
     *
     * @param <code>Produto</code>
     * @return <code>Procedencia</code>
     */
//    public static final Procedencia getProcedenciaToProduto(Produto produto) {
//        return produto.getSubGrupo().getProcedencia();
//    }
    
    /**
     * Retorna a properties que seram utilizados para tela de cadastro de estrutura equipamento.
     */
    public static final String[] getUsedPropertiesEstruturaEquipamentoRevisaoToCadastroEstrutura(){
        
        String[] props = {
            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_PRODUTO, Produto.PROP_CODIGO),
            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_PRODUTO, Produto.PROP_REFERENCIA),
            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_PRODUTO, Produto.PROP_DESCRICAO),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_PROCEDENCIA, Procedencia.PROP_SIGLA),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_PROCEDENCIA, Procedencia.PROP_DESCRICAO),
            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_PRODUTO, Produto.PROP_STATUS_ESTRUTURA),
            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_PRODUTO, Produto.PROP_REVISAO_ESTRUTURA),
            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_PRODUTO, Produto.PROP_USUARIO_ESTRUTURA, Usuario.PROP_CODIGO ),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_PRODUTO, Produto.PROP_PESO_ESPECIFICO),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_PRODUTO, Produto.PROP_PESO_EQUIPAMENTO),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_PRODUTO, Produto.PROP_PESO_LIQUIDO),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_PRODUTO, Produto.PROP_PESO_QUEIMADO),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_PRODUTO, Produto.PROP_PESO_SECO),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_PRODUTO, Produto.PROP_FLAG_EMBARQUE),
            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_COMPONENTE, Produto.PROP_CODIGO ),
            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_COMPONENTE, Produto.PROP_REFERENCIA ),
            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_COMPONENTE, Produto.PROP_DESCRICAO ),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_COMPONENTE, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_PROCEDENCIA, Procedencia.PROP_SIGLA),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_COMPONENTE, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_PROCEDENCIA, Procedencia.PROP_DESCRICAO),
            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_COMPONENTE, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_COMPONENTE, Produto.PROP_LOCALIZACAO, Localizacao.PROP_CODIGO),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_COMPONENTE, Produto.PROP_LOCALIZACAO, Localizacao.PROP_DESCRICAO),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_COMPONENTE, Produto.PROP_LOCALIZACAO, Localizacao.PROP_FLAG_EMBARQUE),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_COMPONENTE, Produto.PROP_LOCALIZACAO, Localizacao.PROP_FLAG_ALMOX_EXPED),
            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_COMPONENTE, Produto.PROP_REVISAO_ESTRUTURA),
            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_COMPONENTE, Produto.PROP_STATUS_ESTRUTURA),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_COMPONENTE, Produto.PROP_PESO_ESPECIFICO),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_COMPONENTE, Produto.PROP_PESO_EQUIPAMENTO),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_COMPONENTE, Produto.PROP_PESO_LIQUIDO),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_COMPONENTE, Produto.PROP_PESO_QUEIMADO),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_COMPONENTE, Produto.PROP_PESO_SECO),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_COMPONENTE, Produto.PROP_FLAG_EMBARQUE),
            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_ITEM ),
            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ID, EstruturaEquipamentoRevisaoPK.PROP_REVISAO ),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_RO_REVISAO),
            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_FLAG_MOVIMENTACAO),
            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_STATUS),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_TAMANHO),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ESPECIFICACAO_PINTURA, EspecificacaoPintura.PROP_CODIGO),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ESPECIFICACAO_PINTURA, EspecificacaoPintura.PROP_DESCRICAO),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_QUANTIDADE_MILIMETRO),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_QUANTIDADE_EXTRA),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_ITEM_SUBSTITUIVEL),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_MAQUINA_PADRAO, Maquina.PROP_CODIGO),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_MAQUINA_PADRAO, Maquina.PROP_DESCRICAO),
            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_DATA_LANCAMENTO),
            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_USUARIO, Usuario.PROP_CODIGO),
            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_USUARIO, Usuario.PROP_NOME),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_EMBARQUE),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_LARGURA),
            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_DATA_USUARIO),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_FORMATO),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_COMPRIMENTO),
            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_QUANTIDADE)
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_PRIORIDADE_EMBARQUE),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_PENDENCIA),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_DIMENSAO),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_FLAG_LOCALIZACAO),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_QUANTIDADE_BRUTA),
//            VOUtils.montarPath(EstruturaEquipamentoRevisao.PROP_TIPO_ESTRUTURA)
        };
        
        return props;
    }
    
}
