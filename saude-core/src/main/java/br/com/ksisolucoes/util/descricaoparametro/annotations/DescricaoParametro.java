/*
 * descParam.java
 *
 * Created on 17 de Setembro de 2007, 09:02
 *
 * To change this template, choose Tools | Template Manager
 * and open the template in the editor.
 */

package br.com.ksisolucoes.util.descricaoparametro.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 *
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface DescricaoParametro {
    String value();
    boolean whenNull() default true;
    String[] customValue() default {};
    String[] customResult() default {};
    String elseResult() default "";
    String[] notShowWhen() default {};
}
