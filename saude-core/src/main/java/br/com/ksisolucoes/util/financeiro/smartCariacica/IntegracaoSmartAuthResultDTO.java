package br.com.ksisolucoes.util.financeiro.smartCariacica;

import java.io.Serializable;

public class IntegracaoSmartAuthResultDTO implements Serializable {

    private int statusCode;
    private IntegracaoSmartAuthOkDTO okDTO;
    private IntegracaoSmartAuthErroDTO erroDTO;

    public IntegracaoSmartAuthResultDTO() {
    }

    public IntegracaoSmartAuthResultDTO(int statusCode, IntegracaoSmartAuthOkDTO okDTO, IntegracaoSmartAuthErroDTO erroDTO) {
        this.statusCode = statusCode;
        this.okDTO = okDTO;
        this.erroDTO = erroDTO;
    }

    public int getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }

    public IntegracaoSmartAuthOkDTO getOkDTO() {
        return okDTO;
    }

    public void setOkDTO(IntegracaoSmartAuthOkDTO okDTO) {
        this.okDTO = okDTO;
    }

    public IntegracaoSmartAuthErroDTO getErroDTO() {
        return erroDTO;
    }

    public void setErroDTO(IntegracaoSmartAuthErroDTO erroDTO) {
        this.erroDTO = erroDTO;
    }
}
