package br.com.ksisolucoes.util.esus;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.StringUtil;
import br.com.celk.util.Util;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Pais;
import br.com.ksisolucoes.vo.cadsus.Raca;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.esus.CboFichaEsus;
import br.com.ksisolucoes.vo.esus.CboFichaEsusItem;
import br.com.ksisolucoes.vo.esus.dto.EsusValidacoesFichasDTOParam;
import ch.lambdaj.Lambda;
import org.apache.commons.lang.StringUtils;
import org.jrimum.utilix.Objects;

import java.text.Normalizer;
import java.util.Date;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.having;
import static ch.lambdaj.Lambda.on;
import static org.hamcrest.Matchers.equalTo;

/**
 * Created by laudecir on 31/08/17.
 */
public class EsusHelper {

    private EsusHelper() {}

    public static boolean isNameValid(String nome) {
        nome = StringUtils.trimToNull(nome);
        if (nome == null) {
            return false;
        }

        /*  6. A informação só pode conter letras do alfabeto romano (incluindo K, W, Y),
        *      além dos acentos gráficos (agudo, circunflexo, til e trema) e do caractere apóstrofo (‘),
        *      para possibilitar a inserção de nomes como JOÃO D’ÁVILA, sendo vedada a utilização de outros caracteres especiais; */
        if (StringUtil.findByRegex(nome, "[\\S&&\\W[\\d]&&[^áàãâäéèẽêëíìĩîïóòõôöúùũûüçÁÀÃÂÄÉÈẼÊËÍÌĨÎÏÓÒÕÖÔÚÙŨÛÜÇ’']]")) {
            return false;
        }

        // Normaliza a string substituindo todos os caracteres especiais e transformando a string para minúscula
        String nameNormalized = Normalizer.normalize(nome, Normalizer.Form.NFD).replaceAll("[^\\p{ASCII}]", "").toLowerCase();
        if (StringUtils.trimToNull(nameNormalized) == null) {
            return false;
        }

        String[] names = nameNormalized.split("\\s+");
        //  4. A informação que contiver um único termo, não entrará na base de dados do CADSUS (Ex: JOAQUIM);
        if (names.length < 2) {
            return false;
        }

        String firstName = names[0];
        if (firstName.length() == 1) {
            return false;

        }

        if (!nome.equals(nome.replaceAll("\\s+", " "))) {
            return false;
        }

        if (names.length == 2) {
            //  14. A informação que contiver apenas dois termos, ambos com apenas dois caracteres, não entrará na base de dados do CADSUS;
            if (names[1].length() == 1 || (firstName.length() == 2 && names[1].length() == 2)) {
                return false;
            }
        }

        String lastName = names[names.length - 1];
        if (lastName.length() == 1) {
            return false;
        }

        if (!(StringUtils.equals(firstName, "rn") || StringUtils.containsAny(firstName, "wy"))) {
            /*  15. A informação que contiver o 1° termo com apenas consoantes, não entrará na base de dados do CADSUS (Ex: PFTG SANTOS MARTINS),
            *       exceto nos casos de: 1 - esse 1º termo conter as letras W e Y; e 2 – esse termo for igual a “RN”;
            *   16. A informação que contiver o 1° termo com 5 ou mais consoantes seguidas de vogal não entrará na base de dados do CADSUS (Ex: PFTGAHQ MOREIRA),
            *       exceto no caso dessa informação conter as letras: W e Y. */
            if (StringUtil.findByRegex(firstName, "^[^aeiou]{5,}")) {
                return false;
            }
        }

        return true;
    }

    public static String getInconsistencies(EsusValidacoesFichasDTOParam validationParam, String message) throws ValidacaoException {
        String errorMsg = getReturnMessage(validationParam, message);
        return errorMsg != null ? errorMsg : StringUtils.EMPTY;
    }

    public static String getReturnMessage(EsusValidacoesFichasDTOParam param, String msgErro) throws ValidacaoException {
        msgErro = StringUtils.trimToNull(msgErro);

        if (msgErro != null && !msgErro.isEmpty() && EsusValidacoesFichasDTOParam.Retorno.EXCEPTION.value().equals(param.getRetorno().value())) {
            throw new ValidacaoException(msgErro);
        }

        return msgErro;
    }

    public static boolean isNomeSocialValido(String nomeSocial) {
        nomeSocial = StringUtils.trimToNull(nomeSocial);
        if (nomeSocial == null) {
            return false;
        }

        /*  6. A informação só pode conter letras do alfabeto romano (incluindo K, W, Y),
         *      além dos acentos gráficos (agudo, circunflexo, til e trema) e do caractere apóstrofo (‘),
         *      para possibilitar a inserção de nomes como JOÃO D’ÁVILA, sendo vedada a utilização de outros caracteres especiais; */
        return !StringUtil.findByRegex(nomeSocial, "[\\S&&\\W[\\d]&&[^áàãâäéèẽêëíìĩîïóòõôöúùũûüçÁÀÃÂÄÉÈẼÊËÍÌĨÎÏÓÒÕÖÔÚÙŨÛÜÇ’']]");
    }

    public static boolean isEqualsSimLong(Long conditionValue) {
        final Long SIM_LONG = RepositoryComponentDefault.SIM_LONG;
        return SIM_LONG.equals(conditionValue);
    }

    public static boolean isOnlyNumbersOrNull(String patientPhone) {
        return Util.isNull(patientPhone) || patientPhone.replaceAll("[0-9]", "").length() == 0;
    }

    public static String getValidPhone(String phone) {
        if (phone == null) return null;
        phone = Util.removeNotNumbers(phone);
        return phone.length() >= 10 && phone.length() <= 11 ? phone : null;
    }

    public static long getRaceColor(Raca raca) {
        long raceColor;
        //Os unicos codigos que diferem são a raca parda e amarela, que são invertidos
        if (raca.getCodigo().equals(Raca.TipoRaca.PARDA.value())) {
            raceColor = (long) Raca.TipoRaca.AMARELA.value();
        } else if (raca.getCodigo().equals(Raca.TipoRaca.AMARELA.value())) {
            raceColor = (long) Raca.TipoRaca.PARDA.value();
        } else {
            raceColor = raca.getCodigo();
        }
        return raceColor;
    }

    public static void appendInconsitency(StringBuilder stringBuilder, String inconsistency) {
        stringBuilder.append(inconsistency);
        stringBuilder.append("</br>");
    }

    public static void validateItem(boolean condition, StringBuilder stringBuilder, String inconsistency) {
        if (condition) {
            appendInconsitency(stringBuilder, inconsistency);
        }
    }

    public static String validateCareUnit(EsusValidacoesFichasDTOParam validationParam) throws ValidacaoException {
        StringBuilder stringBuilder = new StringBuilder();

        if (validationParam.getEmpresa() == null) {
            appendInconsitency(stringBuilder,"Empresa não informada");
        } else {
            validateItem(Objects.isNull(validationParam.getEmpresa().getCnpj()), stringBuilder, "CNPJ " + validationParam.getEmpresa().getDescricao() + " não definido.");
            validateItem(Objects.isNull(validationParam.getEmpresa().getCnes()), stringBuilder, "Unidade " + validationParam.getEmpresa().getDescricao() + " sem CNES definido.");
        }

        getReturnMessage(validationParam, stringBuilder.toString());

        return stringBuilder.toString();
    }

    public static String validateProfessionalCBO(EsusValidacoesFichasDTOParam validationParam) throws ValidacaoException {
        StringBuilder stringBuilder = new StringBuilder();

        if (validationParam.getTabelaCbo() == null) {
            appendInconsitency(stringBuilder, "CBO não definido.");
        } else if (validationParam.getProfissional() == null) {
            appendInconsitency(stringBuilder, "Profissional não definido.");
        } else if (CollectionUtils.isEmpty(validationParam.getCboFichaEsusItemList()) || !Lambda.exists(validationParam.getCboFichaEsusItemList(), having(on(CboFichaEsusItem.class).getTabelaCbo().getCbo(), equalTo(validationParam.getTabelaCbo().getCbo())))) {
            appendInconsitency(stringBuilder, "CBO " + validationParam.getTabelaCbo().getDescricaoFormatado() + " do profissional " + validationParam.getProfissional().getNome() + " não permitido.");
        }

        getReturnMessage(validationParam, stringBuilder.toString());

        return stringBuilder.toString();
    }

    public static Long getNacionalidadePaciente(Pais paisNaturalidade, String numeroPortaria, Date dataEntrada) {
        Long nacionalidade = UsuarioCadsus.Nacionalidade.BRASILEIRO.value();
        if (Objects.isNotNull(paisNaturalidade)) {
            if (Objects.isNotNull(paisNaturalidade.getCodigo()) && Objects.isNotNull(numeroPortaria) && Objects.isNotNull(dataEntrada)) {
                nacionalidade = UsuarioCadsus.Nacionalidade.NATURALIZADO.value();
            } else if (Objects.isNotNull(paisNaturalidade.getCodigo()) && Objects.isNotNull(dataEntrada)) {
                nacionalidade = UsuarioCadsus.Nacionalidade.ESTRANGEIRO.value();
            }
        }
        return nacionalidade;
    }

    public static boolean doesntExistsCboFichaEsusItem(EsusValidacoesFichasDTOParam validationParam, CboFichaEsus.TipoFicha tipoFicha) {
        CboFichaEsusItem cboFichaEsusItemProxy = on(CboFichaEsusItem.class);

        return !LoadManager.getInstance(CboFichaEsusItem.class)
                .addParameter(new QueryCustom.QueryCustomParameter(path(cboFichaEsusItemProxy.getTabelaCbo()), validationParam.getTabelaCbo()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(cboFichaEsusItemProxy.getCboFichaEsus().getFicha()), tipoFicha.value()))
                .start()
                .exists();
    }

    public static String validateProfessional(EsusValidacoesFichasDTOParam validationParam) {
        StringBuilder stringBuilder = new StringBuilder();

        validateItem(validationParam.getProfissional().getCodigoCns() == null, stringBuilder, "Profissional " + validationParam.getProfissional().getNome() + " sem CNS definido.");
        validateItem(validationParam.getProfissional().getCidade() == null, stringBuilder, "Profissional " + validationParam.getProfissional().getNome() + " sem Cidade definida.");

        return stringBuilder.toString();
    }
}
