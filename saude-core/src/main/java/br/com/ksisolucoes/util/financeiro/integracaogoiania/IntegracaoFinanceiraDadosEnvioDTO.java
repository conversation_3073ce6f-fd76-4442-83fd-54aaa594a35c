package br.com.ksisolucoes.util.financeiro.integracaogoiania;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
@XmlRootElement(name = "dados_envio")
@XmlType(propOrder = { "cpfCnpj", "nomePessoa", "tipoDuam", "inscricao", "rubrica", "ano",
        "parcela", "valor", "dataVencimento", "pessoaId", "categoriaDuam",
        "parametroDuam", "verificarPendencia", "endereco", "numr", "quadra",
        "lote", "complemento", "nomeBairro", "idSistemaSolicitante",
        "idDebito", "operacao", "criaDebito", "codSistema" })
public class IntegracaoFinanceiraDadosEnvioDTO implements Serializable {

    private String cpfCnpj;
    private String nomePessoa;
    private String tipoDuam;
    private String inscricao;
    private String rubrica;
    private Integer ano;
    private Integer parcela;
    private Double valor;
    private String dataVencimento;
    private Integer pessoaId;
    private String categoriaDuam;
    private Integer parametroDuam;
    private String verificarPendencia;
    private String endereco;
    private String numr;
    private Integer quadra;
    private Integer lote;
    private Integer complemento;
    private String nomeBairro;
    private int idSistemaSolicitante;
    private Integer idDebito;
    private String operacao;
    private Integer criaDebito;
    private String codSistema;

    @XmlElement(name = "cpfcnpj")
    public String getCpfCnpj() {
        return cpfCnpj;
    }

    public void setCpfCnpj(String cpfCnpj) {
        this.cpfCnpj = cpfCnpj;
    }

    @XmlElement(name = "nomepessoa")
    public String getNomePessoa() {
        return nomePessoa;
    }

    public void setNomePessoa(String nomePessoa) {
        this.nomePessoa = nomePessoa;
    }

    @XmlElement(name = "tipoduam")
    public String getTipoDuam() {
        return tipoDuam;
    }

    public void setTipoDuam(String tipoDuam) {
        this.tipoDuam = tipoDuam;
    }

    @XmlElement(name = "inscricao")
    public String getInscricao() {
        return inscricao;
    }

    public void setInscricao(String inscricao) {
        this.inscricao = inscricao;
    }

    @XmlElement(name = "rubrica")
    public String getRubrica() {
        return rubrica;
    }

    public void setRubrica(String rubrica) {
        this.rubrica = rubrica;
    }

    @XmlElement(name = "ano")
    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    @XmlElement(name = "parcela")
    public Integer getParcela() {
        return parcela;
    }

    public void setParcela(Integer parcela) {
        this.parcela = parcela;
    }

    @XmlElement(name = "valor")
    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    @XmlElement(name = "datavencimento")
    public String getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(String dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    @XmlElement(name = "pessoaid")
    public Integer getPessoaId() {
        return pessoaId;
    }

    public void setPessoaId(Integer pessoaId) {
        this.pessoaId = pessoaId;
    }

    @XmlElement(name = "categoriaduam")
    public String getCategoriaDuam() {
        return categoriaDuam;
    }

    public void setCategoriaDuam(String categoriaDuam) {
        this.categoriaDuam = categoriaDuam;
    }

    @XmlElement(name = "parametroduam")
    public Integer getParametroDuam() {
        return parametroDuam;
    }

    public void setParametroDuam(Integer parametroDuam) {
        this.parametroDuam = parametroDuam;
    }

    @XmlElement(name = "verificarpendencia")
    public String getVerificarPendencia() {
        return verificarPendencia;
    }

    public void setVerificarPendencia(String verificarPendencia) {
        this.verificarPendencia = verificarPendencia;
    }

    @XmlElement(name = "endereco")
    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    @XmlElement(name = "numr")
    public String getNumr() {
        return numr;
    }

    public void setNumr(String numr) {
        this.numr = numr;
    }

    @XmlElement(name = "quadra")
    public Integer getQuadra() {
        return quadra;
    }

    public void setQuadra(Integer quadra) {
        this.quadra = quadra;
    }

    @XmlElement(name = "lote")
    public Integer getLote() {
        return lote;
    }

    public void setLote(Integer lote) {
        this.lote = lote;
    }

    @XmlElement(name = "complemento")
    public Integer getComplemento() {
        return complemento;
    }

    public void setComplemento(Integer complemento) {
        this.complemento = complemento;
    }

    @XmlElement(name = "nomebairro")
    public String getNomeBairro() {
        return nomeBairro;
    }

    public void setNomeBairro(String nomeBairro) {
        this.nomeBairro = nomeBairro;
    }

    @XmlElement(name = "idsistemasolicitante")
    public int getIdSistemaSolicitante() {
        return idSistemaSolicitante;
    }

    public void setIdSistemaSolicitante(int idSistemaSolicitante) {
        this.idSistemaSolicitante = idSistemaSolicitante;
    }

    @XmlElement(name = "iddebito")
    public Integer getIdDebito() {
        return idDebito;
    }

    public void setIdDebito(Integer idDebito) {
        this.idDebito = idDebito;
    }

    @XmlElement(name = "operacao")
    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    @XmlElement(name = "criadebito")
    public Integer getCriaDebito() {
        return criaDebito;
    }

    public void setCriaDebito(Integer criaDebito) {
        this.criaDebito = criaDebito;
    }

    @XmlElement(name = "codsistema")
    public String getCodSistema() {
        return codSistema;
    }

    public void setCodSistema(String codSistema) {
        this.codSistema = codSistema;
    }
}
