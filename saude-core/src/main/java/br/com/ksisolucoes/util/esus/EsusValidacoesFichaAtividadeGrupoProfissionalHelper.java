package br.com.ksisolucoes.util.esus;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoProfissional;
import br.com.ksisolucoes.vo.basico.Equipe;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.esus.CboFichaEsus;
import br.com.ksisolucoes.vo.esus.CboFichaEsusItem;
import br.com.ksisolucoes.vo.esus.dto.EsusValidacoesFichasDTOParam;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import ch.lambdaj.Lambda;
import org.jrimum.utilix.Objects;

import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static br.com.ksisolucoes.util.esus.EsusHelper.*;
import static ch.lambdaj.Lambda.having;
import static ch.lambdaj.Lambda.on;
import static org.hamcrest.Matchers.equalTo;

/**
 * Created by laudecir on 06/09/17.
 */
public class EsusValidacoesFichaAtividadeGrupoProfissionalHelper {

    private static final String PROFISSIONAL = "Profissional ";

    private EsusValidacoesFichaAtividadeGrupoProfissionalHelper() { }

    public static String validate(EsusValidacoesFichasDTOParam validationParam) throws ValidacaoException {
        StringBuilder stringBuilder = new StringBuilder();

        stringBuilder.append(getInconsistencies(validationParam, validateProfessional(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateProfessionalCBO(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateProfessionalTeam(validationParam)));

        return stringBuilder.toString();
    }

    public static String validateProfessional(EsusValidacoesFichasDTOParam param) {
        AtividadeGrupoProfissional atividadeGrupoProfissional = param.getAtividadeGrupoDTO().getAtividadeGrupoProfissional();
        StringBuilder stringBuilder = new StringBuilder();

        validateItem(Objects.isNull(atividadeGrupoProfissional.getProfissional().getCodigoCns()), stringBuilder, PROFISSIONAL + atividadeGrupoProfissional.getProfissional().getNome() + " sem CNS definido.");
        validateItem(Objects.isNull(atividadeGrupoProfissional.getProfissional().getCidade()), stringBuilder, PROFISSIONAL + atividadeGrupoProfissional.getProfissional().getNome() + " sem Cidade definida.");

        return stringBuilder.toString();
    }

    public static String validateProfessionalTeam(EsusValidacoesFichasDTOParam param) {
        AtividadeGrupoProfissional atividadeGrupoProfissional = param.getAtividadeGrupoDTO().getAtividadeGrupoProfissional();
        if (atividadeGrupoProfissional.getEquipe() != null) {
            return new StringBuilder().toString();
        }
        List<EquipeProfissional> equipeProfissionalList = getProfessionalTeams(param, atividadeGrupoProfissional);

        StringBuilder stringBuilder = new StringBuilder();

        if (CollectionUtils.isNotNullEmpty(equipeProfissionalList)) {
            if (equipeProfissionalList.size() > 1) {
                appendInconsitency(stringBuilder, PROFISSIONAL + atividadeGrupoProfissional.getProfissional().getNome() + " está vinculado em mais de uma equipe. Estabelecimento: " + param.getEmpresa().getDescricao());
            } else {
                Equipe equipe = equipeProfissionalList.get(0).getEquipe();
                if (Objects.isNotNull(equipe.getEquipeCnes()) && equipe.getEquipeCnes().length() < 10) {
                    appendInconsitency(stringBuilder, "O Código INE da equipe " + equipe.getReferencia() + " da área de " + equipe.getEquipeArea().getDescricao() + " pertencente ao estabelecimento " + param.getEmpresa().getDescricao() + " é inválido, devendo este possuir 10 dígitos.");
                }
            }
        }

        return stringBuilder.toString();
    }

    public static String validateProfessionalEquipe(EsusValidacoesFichasDTOParam param) {
        AtividadeGrupoProfissional atividadeGrupoProfissional = param.getAtividadeGrupoDTO().getAtividadeGrupoProfissional();
        List<EquipeProfissional> equipeProfissionalList = getProfessionalTeams(param, atividadeGrupoProfissional);

        StringBuilder stringBuilder = new StringBuilder();

        if (CollectionUtils.isNotNullEmpty(equipeProfissionalList)) {
            if (equipeProfissionalList.size() > 1) {
                appendInconsitency(stringBuilder, PROFISSIONAL + atividadeGrupoProfissional.getProfissional().getNome() + " está vinculado em mais de uma equipe. Estabelecimento: " + param.getEmpresa().getDescricao());
            } else {
                Equipe equipe = equipeProfissionalList.get(0).getEquipe();
                if (Objects.isNotNull(equipe.getEquipeCnes()) && equipe.getEquipeCnes().length() < 10) {
                    appendInconsitency(stringBuilder, "O Código INE da equipe " + equipe.getReferencia() + " da área de " + equipe.getEquipeArea().getDescricao() + " pertencente ao estabelecimento " + param.getEmpresa().getDescricao() + " é inválido, devendo este possuir 10 dígitos.");
                }
            }
        }

        return stringBuilder.toString();
    }

    public static List<EquipeProfissional> getProfessionalTeams(EsusValidacoesFichasDTOParam param, AtividadeGrupoProfissional atividadeGrupoProfissional) {
        EquipeProfissional equipeProfissionalProxy = on(EquipeProfissional.class);

        return LoadManager.getInstance(EquipeProfissional.class)
                .addProperty(path(equipeProfissionalProxy.getCodigo()))
                .addProperty(path(equipeProfissionalProxy.getEquipe().getCodigo()))
                .addProperty(path(equipeProfissionalProxy.getEquipe().getEquipeArea().getCodigo()))
                .addProperty(path(equipeProfissionalProxy.getEquipe().getEquipeArea().getDescricao()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(equipeProfissionalProxy.getStatus()), EquipeProfissional.STATUS_ATIVO))
                .addParameter(new QueryCustom.QueryCustomParameter(path(equipeProfissionalProxy.getProfissional()), atividadeGrupoProfissional.getProfissional()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(equipeProfissionalProxy.getEquipe().getAtivo()), RepositoryComponentDefault.SIM))
                .addParameter(new QueryCustom.QueryCustomParameter(path(equipeProfissionalProxy.getEquipe().getEmpresa()), param.getEmpresa()))
                .start()
                .getList();
    }

    public static String validateProfessionalCBO(EsusValidacoesFichasDTOParam param) {
        AtividadeGrupoProfissional atividadeGrupoProfissional = param.getAtividadeGrupoDTO().getAtividadeGrupoProfissional();
        Profissional profissional = Objects.isNotNull(param.getProfissional()) ? param.getProfissional() : atividadeGrupoProfissional.getProfissional();
        TabelaCbo profissionalCBO = Objects.isNotNull(param.getTabelaCbo()) ? param.getTabelaCbo() : atividadeGrupoProfissional.getTabelaCbo();
        StringBuilder stringBuilder = new StringBuilder();

        if (Objects.isNull(profissionalCBO)) {
            appendInconsitency(stringBuilder, "CBO do profissional " + profissional.getNome() + " não informado.");
        } else if (CollectionUtils.isEmpty(param.getCboFichaEsusItemList())) {
            CboFichaEsusItem cboFichaEsus = getCboFichaEsusItem(profissionalCBO);

            if (Objects.isNull(cboFichaEsus)) {
                appendInconsitency(stringBuilder, "CBO " + profissionalCBO.getDescricaoFormatado() + " do profissional " + profissional.getNome() + " não permitido.");
            }
        } else if (!Lambda.exists(param.getCboFichaEsusItemList(), having(on(CboFichaEsusItem.class).getTabelaCbo().getCbo(), equalTo(profissionalCBO.getCbo())))) {
            appendInconsitency(stringBuilder, "CBO " + profissionalCBO.getDescricaoFormatado() + " do profissional " + profissional.getNome() + " não permitido.");
        }

        return stringBuilder.toString();
    }

    public static CboFichaEsusItem getCboFichaEsusItem(TabelaCbo cboProfissional) {
        CboFichaEsusItem cboFichaEsusItemProxy = on(CboFichaEsusItem.class);

        return LoadManager.getInstance(CboFichaEsusItem.class)
                .addProperty(path(cboFichaEsusItemProxy.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(cboFichaEsusItemProxy.getTabelaCbo()), cboProfissional))
                .addParameter(new QueryCustom.QueryCustomParameter(path(cboFichaEsusItemProxy.getCboFichaEsus().getFicha()), CboFichaEsus.TipoFicha.FICHA_ATIVIDADE_COLETIVA.value()))
                .setMaxResults(1)
                .start()
                .getVO();
    }
}
