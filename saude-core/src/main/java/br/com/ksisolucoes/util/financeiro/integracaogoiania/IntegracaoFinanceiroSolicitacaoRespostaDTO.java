package br.com.ksisolucoes.util.financeiro.integracaogoiania;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

@XmlRootElement(name = "solicitacao_resposta")
@XmlType(propOrder = { "servico", "dadosEnvio", "dadosResposta", "mensagem"})
public class IntegracaoFinanceiroSolicitacaoRespostaDTO implements Serializable {

    private IntegracaoFinanceiraServicoDTO servico;
    private IntegracaoFinanceiraDadosEnvioDTO dadosEnvio;
    private IntegracaoFinanceiraDadosRespostaDTO dadosResposta;
    private IntegracaoFinanceiraMensagemDTO mensagem;

    @XmlElement(name = "servico")
    public IntegracaoFinanceiraServicoDTO getServico() {
        return servico;
    }

    public void setServico(IntegracaoFinanceiraServicoDTO servico) {
        this.servico = servico;
    }

    @XmlElement(name = "dados_envio")
    public IntegracaoFinanceiraDadosEnvioDTO getDadosEnvio() {
        return dadosEnvio;
    }

    public void setDadosEnvio(IntegracaoFinanceiraDadosEnvioDTO dadosEnvio) {
        this.dadosEnvio = dadosEnvio;
    }

    @XmlElement(name = "dados_resposta")
    public IntegracaoFinanceiraDadosRespostaDTO getDadosResposta() {
        return dadosResposta;
    }

    public void setDadosResposta(IntegracaoFinanceiraDadosRespostaDTO dadosResposta) {
        this.dadosResposta = dadosResposta;
    }

    @XmlElement(name = "mensagem")
    public IntegracaoFinanceiraMensagemDTO getMensagem() {
        return mensagem;
    }

    public void setMensagem(IntegracaoFinanceiraMensagemDTO mensagem) {
        this.mensagem = mensagem;
    }
}
