package br.com.ksisolucoes.util;

import java.math.MathContext;

/**
 *
 * <AUTHOR>
 */
public class CalculosUtil {
    
    public static Double calculoImc(Double alturaM, Double pesoKG){
        Dinheiro imc = new Dinheiro(0D);
        if (Coalesce.asDouble(alturaM,0D) > 0D && Coalesce.asDouble(pesoKG,0D) > 0D) {
            Dinheiro altura = new Dinheiro(alturaM, MathContext.DECIMAL128).multiplicar(new Dinhe<PERSON>(alturaM, MathContext.DECIMAL128), MathContext.DECIMAL128);
            if (altura.doubleValue() > 0D) {
                imc = new Dinheiro(pesoKG, MathContext.DECIMAL128).dividir(altura);
            }
        }
        return imc.round().doubleValue();
    }
}
