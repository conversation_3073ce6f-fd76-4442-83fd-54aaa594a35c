package br.com.ksisolucoes.util.financeiro.smartCariacica;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

public class IntegracaoSmartGerarPrecoPublicoDTO implements Serializable {

    @JsonProperty("tipo")
    private String tipo;
    @JsonProperty("valor")
    private String valor;
    @JsonProperty("quantidade")
    private String quantidade;
    @JsonProperty("datavencimento")
    private String datavencimento;
    @JsonProperty("exerciciolancto")
    private String exerciciolancto;
    @JsonProperty("nroparcela")
    private String nroparcela;
    @JsonProperty("codigoprecopublico")
    private String codigoprecopublico;
    @JsonProperty("observacao")
    private String observacao;
    @JsonProperty("tipopessoa")
    private String tipopessoa;
    @JsonProperty("nome")
    private String nome;
    @JsonProperty("nomeFantasia")
    private String nomeFantasia;
    @JsonProperty("cnpjCpf")
    private String cnpjCpf;
    @JsonProperty("rginscricaoestadual")
    private String rginscricaoestadual;
    @JsonProperty("email")
    private String email;
    @JsonProperty("contato")
    private String contato;
    @JsonProperty("numero")
    private String numero;
    @JsonProperty("complemento")
    private String complemento;
    @JsonProperty("cep")
    private String cep;
    @JsonProperty("tipologradouro")
    private String tipologradouro;
    @JsonProperty("titulo")
    private String titulo;
    @JsonProperty("logradouro")
    private String logradouro;
    @JsonProperty("bairro")
    private String bairro;
    @JsonProperty("cidade")
    private String cidade;
    @JsonProperty("estado")
    private String estado;

    public IntegracaoSmartGerarPrecoPublicoDTO() {}

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }

    public String getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(String quantidade) {
        this.quantidade = quantidade;
    }

    public String getDatavencimento() {
        return datavencimento;
    }

    public void setDatavencimento(String datavencimento) {
        this.datavencimento = datavencimento;
    }

    public String getExerciciolancto() {
        return exerciciolancto;
    }

    public void setExerciciolancto(String exerciciolancto) {
        this.exerciciolancto = exerciciolancto;
    }

    public String getNroparcela() {
        return nroparcela;
    }

    public void setNroparcela(String nroparcela) {
        this.nroparcela = nroparcela;
    }

    public String getCodigoprecopublico() {
        return codigoprecopublico;
    }

    public void setCodigoprecopublico(String codigoprecopublico) {
        this.codigoprecopublico = codigoprecopublico;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getTipopessoa() {
        return tipopessoa;
    }

    public void setTipopessoa(String tipopessoa) {
        this.tipopessoa = tipopessoa;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getNomeFantasia() {
        return nomeFantasia;
    }

    public void setNomeFantasia(String nomeFantasia) {
        this.nomeFantasia = nomeFantasia;
    }

    public String getCnpjCpf() {
        return cnpjCpf;
    }

    public void setCnpjCpf(String cnpjCpf) {
        this.cnpjCpf = cnpjCpf;
    }

    public String getRginscricaoestadual() {
        return rginscricaoestadual;
    }

    public void setRginscricaoestadual(String rginscricaoestadual) {
        this.rginscricaoestadual = rginscricaoestadual;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getContato() {
        return contato;
    }

    public void setContato(String contato) {
        this.contato = contato;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getCep() {
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public String getTipologradouro() {
        return tipologradouro;
    }

    public void setTipologradouro(String tipologradouro) {
        this.tipologradouro = tipologradouro;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getLogradouro() {
        return logradouro;
    }

    public void setLogradouro(String logradouro) {
        this.logradouro = logradouro;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getEstado() {
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }
}
