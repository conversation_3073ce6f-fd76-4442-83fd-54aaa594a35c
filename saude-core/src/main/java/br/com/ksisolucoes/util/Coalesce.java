package br.com.ksisolucoes.util;

import java.util.Date;
import java.util.Locale;

import br.com.ksisolucoes.util.log.Loggable;

/**
 * Classe utilitria a qual simula o comportamento da funo coalesce da maioria
 * dos BD do mercado. Esta serve para que, caso sejam informados valores invlidos ou nulos,
 * os mtodos retornem seus correspondentes igualmente vzios. Por exemplo, para <code>String</code>s,
 * ser retornado o valor vzio (<code>""</code>), para um <code>Long</code>, ser retornado <code>0L</code>
 * e assim por diante.
 */
@Deprecated
public class Coalesce {
    
    /**
     * Retorna um valor <code>Double</code> como uma <CODE>String</CODE>, vazia, caso
     * este seja <CODE>null</CODE>. A <code>String</code> retornada vem com o valor
     * adequadamente formatado para o <code>Locale</code> corrente.
     * @param valor
     * @return
     */
    public static String asString( Double valor ){
        String retorno = "";
        if ( valor != null ){
            retorno = Valor.adicionarFormatacaoMonetaria( valor );
        }
        
        return retorno;
    }
    
    /**
     * Retorna um valor <code>Long</code> como uma <CODE>String</CODE>, caso
     * este seja <CODE>null</CODE>, o <code>retorno</code> informado ser retornado.
     * @param valor
     * @param retorno retorno caso seja um <CODE>Long</CODE> <CODE>null</CODE>.
     * @return
     */
    @Deprecated
    public static String asString( Long valor, String retorno ){
        return valor == null ? retorno : valor.toString();
    }
    
    /**
     * Retorna um valor <code>Long</code> como uma <CODE>String</CODE>, vazia, caso
     * este seja <CODE>null</CODE>.
     * @param valor
     * @return
     */
    @Deprecated
    public static String asString( Long valor ){
        return asString( valor, "" );
    }
    
    /**
     * Retorna um valor <code>String</code> como uma <CODE>String</CODE>, vazia, caso
     * este seja <CODE>null</CODE>.
     * @param valor
     * @param retorno
     * @return
     */
    @Deprecated
    public static String asString(String valor,String retorno){
        return valor == null ? retorno : valor.trim();
    }

    /**
     * Retorna um valor <code>String</code> como uma <CODE>String</CODE>, vazia, caso
     * este seja <CODE>null</CODE>.
     * @param valor
     * @param retorno
     * @return
     */
    @Deprecated
    public static String asString(Object valor, String retorno){
        return valor == null ? retorno : asString(valor.toString(), retorno);
    }
    
    /**
     * Retorna um valor <code>String</code> como uma <CODE>String</CODE>, vazia, caso
     * este seja <CODE>null</CODE>.
     * @param valor
     * @return
     */
    @Deprecated
    public static String asString(String valor){
        return asString(valor,"");
    }
    
    /**
     * Retorna um valor <code>Object</code> como uma <CODE>String</CODE>, vazia, caso
     * este seja <CODE>null</CODE>.
     * @param valor
     * @return
     */
    @Deprecated
    public static String asString(Object valor){
        String _valor = "";
        if ( valor != null ){
            _valor = valor.toString();
        }
        
        return asString( _valor, "" );
    }
    
    /**
     * Retorna um valor <code>String</code> como um <CODE>Double</CODE>, caso
     * este seja <CODE>null</CODE> ou invlido, o <code>retorno</code> informado ser retornado.
     * @param valor
     * @param retorno
     * @return
     */
    public static Double asDouble(String valor,Double retorno){
        try{
            retorno = Double.parseDouble( Valor.removerFormatacaoMonetaria( valor ) );
        } catch ( NumberFormatException e ){}
        
        return retorno;
    }
    
    /**
     * Retorna um valor <code>String</code> como um <CODE>Double</CODE>, zerado, caso
     * este seja <CODE>null</CODE> ou invlido.
     * @param valor
     * @return
     */
    public static Double asDouble(String valor){
        return asDouble(valor, 0D);
    }
    
    /**
     * Retorna um valor <code>Double</code> como um <CODE>Double</CODE>, caso
     * este seja <CODE>null</CODE> ou invlido, o <code>retorno</code> informado ser retornado.
     * @param valor
     * @param retorno
     * @return
     */
    @Deprecated
    public static Double asDouble(Double valor,Double retorno){
        return valor==null?retorno:valor;
    }
    
    /**
     * Retorna um valor <code>Double</code> como um <CODE>Double</CODE>, zerado, caso
     * este seja <CODE>null</CODE> ou invlido.
     * @param valor
     * @return
     */
    @Deprecated
    public static Double asDouble(Double valor){
        return asDouble(valor,0D);
    }
    
    /**
     * Retorna um valor <code>Long</code> como um <CODE>Long</CODE>, caso
     * este seja <CODE>null</CODE> ou invlido, o <code>retorno</code> informado ser retornado.
     * @param valor
     * @param retorno
     * @return
     */
    @Deprecated
    public static Long asLong(Long valor,Long retorno){
        return valor == null ? retorno : valor;
    }

    /**
     * Retorna um valor <code>Long</code> como um <CODE>Long</CODE>, caso
     * este seja <CODE>null</CODE> ou invlido, o <code>retorno</code> informado ser retornado.
     * @param valor
     * @param retorno
     * @return
     */
    @Deprecated
    public static Long asLong(Object valor, Long retorno){
        return valor == null ? retorno : asLong(valor.toString(), retorno);
    }
    
    /**
     * Retorna um valor <code>Long</code> como um <CODE>Long</CODE>, zerado, caso
     * este seja <CODE>null</CODE> ou invlido.
     * @param valor
     * @return
     */
    @Deprecated
    public static Long asLong(Long valor){
        return asLong( valor, 0L );
    }
    
    /**
     * Retorna um valor <code>String</code> como um <CODE>Long</CODE>, zerado, caso
     * este seja <CODE>null</CODE> ou invlido.
     * @param valor
     * @return
     */
    @Deprecated
    public static Long asLong(String valor) {
        return asLong( valor, 0L );
    }
    
    /**
     * Retorna um valor <code>String</code> como um <CODE>Long</CODE>, caso
     * este seja <CODE>null</CODE> ou invlido, o <code>retorno</code> informado ser retornado.
     * @param valor
     * @param retorno
     * @return
     */
    @Deprecated
    public static Long asLong(String valor, Long retorno) {
        if ( valor == null || valor.trim().isEmpty()){
            return retorno;
        }
        
        try {
            retorno = new Long( valor.trim() );
        } catch (NumberFormatException ex) {
            Loggable.log.debug(ex.getMessage(), ex);
        }
        
        return retorno;
    }
    
    /**
     * Retorna o primeiro long nao nulo.
     * @param numbers
     * @return
     */
    @Deprecated
    public static Long asLong(Long... numbers) {
        return asLong(false, numbers);
    }
    
    /**
     * Retorna o primeiro long nao nulo.
     * Caso zeroIsNull for true o number deve ser diferente de zero tambem.
     * @param numbers
     * @return
     */
    @Deprecated
    public static Long asLong(boolean zeroIsNull, Long... numbers) {
        for (Long number : numbers) {
            if ((zeroIsNull && number != null && !number.equals(0L)) || (!zeroIsNull && number != null)) {
                return number;
            }
        }
        
        return null;
    }
    
    /**
     * Retorna a primeira string nao nula.
     * @param strings
     * @return
     */
    public static String asString(String... strings) {
        return asString(false, strings);
    }
    
    /**
     * Retorna a primeira string nao nula.
     * Caso emptyStringIsNull for true o number deve ser diferente de zero tambem.
     * @param strings
     * @return
     */
    public static String asString(boolean emptyStringIsNull, String... strings) {
        for (String string : strings) {
            if ((emptyStringIsNull && string != null && !string.equals("")) || (!emptyStringIsNull && string != null)) {
                return string;
            }
        }
        
        return null;
    }
    
    /**
     * Retorna um valor <code>Date</code> como um <CODE>Date</CODE>, caso
     * este seja <CODE>null</CODE> ou invlido, o <code>retorno</code> informado ser retornado.
     * @param valor
     * @param retorno
     * @return
     */
    public static Date asDate(Date valor, Date retorno){
        return valor == null ? retorno : valor;
    }
    
    /**
     * Retorna um valor <code>Date</code> como um <CODE>Date</CODE>. Caso
     * este seja <CODE>null</CODE> ou invlido, retorna a data atual do sistema.
     * @param valor
     * @return
     */
    public static Date asDate(Date valor){
        return asDate( valor, Data.getDataAtual() );
    }
    
    /**
     * Retorna o Locale padro caso o Locale informado seja nulo.
     * @param locale
     * @return
     */
    public static Locale asLocale(Locale locale) {
        return asLocale( locale, Bundle.getLocale() );
    }
    
    /**
     * Retorna o Locale de retorno informado caso o Locale informado seja nulo.
     * @param locale
     * @param retorno
     * @return
     */
    public static Locale asLocale(Locale locale, Locale retorno) {
        if ( locale != null ){
            retorno = locale;
        }
        
        return retorno;
    }

    @Deprecated
    public static Integer asInteger(Integer valor) {
        return asInteger(valor, 0);
    }

    @Deprecated
    public static Integer asInteger(Integer valor, Integer retorno) {
        return valor == null ? retorno : valor;
    }
}
