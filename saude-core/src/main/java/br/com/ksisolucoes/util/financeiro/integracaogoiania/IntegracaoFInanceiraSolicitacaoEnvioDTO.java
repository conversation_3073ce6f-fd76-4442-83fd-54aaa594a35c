package br.com.ksisolucoes.util.financeiro.integracaogoiania;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

@XmlRootElement(name = "solicitacao_envio")
@XmlType(propOrder = { "servico", "dadosEnvio"})
public class IntegracaoFInanceiraSolicitacaoEnvioDTO implements Serializable {

    private IntegracaoFinanceiraServicoDTO servico;
    private IntegracaoFinanceiraDadosEnvioDTO dadosEnvio;

    @XmlElement(name = "servico")
    public IntegracaoFinanceiraServicoDTO getServico() {
        return servico;
    }

    public void setServico(IntegracaoFinanceiraServicoDTO servico) {
        this.servico = servico;
    }

    @XmlElement(name = "dados_envio")
    public IntegracaoFinanceiraDadosEnvioDTO getDadosEnvio() {
        return dadosEnvio;
    }

    public void setDadosEnvio(IntegracaoFinanceiraDadosEnvioDTO dadosEnvio) {
        this.dadosEnvio = dadosEnvio;
    }
}
