package br.com.ksisolucoes.util;

import br.com.ksisolucoes.util.log.Loggable;

import javax.swing.text.NumberFormatter;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * Classe para trabalhar com valores numricos. Esta possibilita a formatao em
 * nmeros monetrios e a sua volta ao formato decimal normal de armazenamento.
 * <AUTHOR>
 * <AUTHOR>
 */
public class Valor implements Loggable {
    
    private static final String PATTERN = "#,###,##0.00";
    
    /**
     * Verifica se o valor da String  Double
     * @return  Retorna <CODE>true</CODE> se valor for um Double vlido.
     * @param valor Valor a ser verificado
     */
    public static boolean isDouble(String valor){
        boolean resultado = false;
        double controle;
        try{
            controle = Double.parseDouble(valor);
            resultado = true;
        }catch(Exception ex){
            resultado = false;
        }
        return resultado;
    }
    
    
    /**
     * Verifica se o valor da String  Integer
     * @return Retorna <CODE>true</CODE> se valor for um Integer vlido.
     * @param valor Valor a ser verificado
     */
    public static boolean isInteger(String valor){
        boolean resultado = false;
        double controle;
        try{
            controle = Integer.parseInt(valor);
            resultado = true;
        }catch(Exception ex){
            resultado = false;
        }
        return resultado;
    }
    
    /**
     * Verifica se o valor  Integer
     * @return Retorna <CODE>true</CODE> se valor for um Integer vlido.
     * @param valor Valor a ser verificado
     */
    public static boolean isInteger(double valor){
        boolean resultado = false;
        double controle;
        try{
            controle = round(valor,0);
            if(valor == controle){
                resultado = true;
            }
        }catch(Exception ex){
            resultado = false;
        }
        return resultado;
    }
    
    public static DecimalFormat getDecimalFormat(){
        return getDecimalFormat(2);
    }
    
    /**
     * Retorna um DecimalFormat para a instancia correspondente ao Locale corrente.
     * @param locale 
     * @param fractionDigits 
     * @return DecimalFoirmat correspondente ao Locale atual
     */
    public static DecimalFormat getDecimalFormat(int fractionDigits){
        return getDecimalFormat( fractionDigits, Bundle.getLocale() );
    }
    
    /**
     * Retorna um DecimalFormat para a instancia correspondente ao Locale corrente.
     * @return DecimalFoirmat correspondente ao Locale atual
     */
    public static DecimalFormat getDecimalFormat( int fractionDigits, Locale locale ){
        DecimalFormat df = (DecimalFormat) NumberFormat.getInstance( locale );
        df.setDecimalSeparatorAlwaysShown( false );
        
        
        if ( fractionDigits == 0 ){
            String pattern = Valor.PATTERN.substring( 0, Valor.PATTERN.length() - 3 );
            
            df.applyPattern( pattern );
        } else if ( fractionDigits == 2 ){
            df.applyPattern( Valor.PATTERN );
        } else{
            
            String pattern = Valor.PATTERN.substring( 0, Valor.PATTERN.length() - 2 );
            
            //for ( int i = 0; i < fractionDigits - 2; i++ ){
            for ( int i = 0; i < fractionDigits; i++ ){
                pattern += "0";
            }
            
            df.applyPattern( pattern );
        }
        
        return df;
    }
    
    /**
     * Retorna um NumberFormatter para a representao apropriada de valores numricos
     * em jformattedtextfield.
     * @return MaskFormatter mscara de formatao de valor monetrio
     */
    public static NumberFormatter getDefaultNumberFormatter(){
        return getDefaultNumberFormatter( 50, 2 );
    }
    
    /**
     * Retorna um NumberFormatter para a representao apropriada de valores numricos
     * em jformattedtextfield. Este aceita o valor mximo aceito de caracteres, inclusive
     * o separador decimal.
     * @return MaskFormatter mscara de formatao de valor monetrio
     * @param maxLength tamanho mximo de caracteres aceitos, inclusive o separador decimal
     */
    public static NumberFormatter getDefaultNumberFormatter( int maxLength ){
        return getDefaultNumberFormatter( maxLength, 2 );
    }
    
    /**
     * Retorna um NumberFormatter para a representao apropriada de valores numricos
     * em jformattedtextfield. Este aceita o valor mximo aceito de caracteres, inclusive
     * o separador decimal.
     * @return MaskFormatter mscara de formatao de valor monetrio
     * @param maxLength tamanho mximo de caracteres aceitos, inclusive o separador decimal
     */
    public static NumberFormatter getDefaultNumberFormatter( int maxLength, int fractionDigits ){
        DecimalFormat decimal = Valor.getDecimalFormat( fractionDigits );
        decimal.setMaximumIntegerDigits( maxLength - (fractionDigits + 1) );
        NumberFormatter numberFormatter = new NumberFormatter( decimal );
        numberFormatter.setAllowsInvalid( false );
        numberFormatter.setValueClass( Double.class );
        
        return numberFormatter;
    }
    
    /**
     * Coloca a formatao monetria ao valor informado. Deve ser utilizada para
     * exibio dos valores ao usurio e no para armazenamento.
     * Se o valor informado for invlido, ser retornado um <code>String</code> vazio.
     * @return Valor com formatao monetria.
     * @param valor Valor a ser formatado.
     */
    public static String adicionarFormatacaoMonetaria(String valor){
        return adicionarFormatacaoMonetaria(valor, 2);
    }
    
    /**
     * Coloca a formatao monetria ao valor informado. Deve ser utilizada para
     * exibio dos valores ao usurio e no para armazenamento.
     * Se o valor informado for invlido, ser retornado um <code>String</code> vazio.
     * @return Valor com formatao monetria.
     * @param valor Valor a ser formatado.
     */
    public static String adicionarFormatacaoMonetaria(String valor, int fractionDigits){
        try{
            valor = Valor.getDecimalFormat(fractionDigits).format( Double.parseDouble( valor ) );
        }catch(Exception ex){
            return "";
        }
        
        return valor;
    }
    
    public static String adicionarFormatacaoMonetaria(Double valor){
        return adicionarFormatacaoMonetaria(valor, Bundle.getLocale());
    }
    
    /**
     * Coloca a formatao monetria ao valor informado. Deve ser utilizada para
     * exibio dos valores ao usurio e no para armazenamento.
     * Se o valor informado for invlido, ser retornado um <code>String</code> vazio.
     * @return Valor com formatao monetria.
     * @param valor Valor a ser formatado.
     */
    public static String adicionarFormatacaoMonetaria(Double valor, Locale locale){
        return adicionarFormatacaoMonetaria( valor, 2, locale );
    }
    
    public static String adicionarFormatacaoMonetaria(Double valor, int fractionDigits){
        return adicionarFormatacaoMonetaria(valor, fractionDigits, Bundle.getLocale());
    }
    
    public static String adicionarFormatacaoMonetaria(BigDecimal valor, int fractionDigits){
        return adicionarFormatacaoMonetaria(valor, fractionDigits, Bundle.getLocale());
    }
    
    /**
     * Coloca a formatao monetria ao valor informado. Deve ser utilizada para
     * exibio dos valores ao usurio e no para armazenamento.
     * Se o valor informado for invlido, ser retornado um <code>String</code> vazio.
     * @return Valor com formatao monetria.
     * @param valor Valor a ser formatado.
     */
    public static String adicionarFormatacaoMonetaria(Double valor, int fractionDigits, Locale locale){
        String literal="";
        try{
            literal = Valor.getDecimalFormat(fractionDigits, locale).format( valor );
        }catch(Exception ex){
            return "";
        }
        return literal;
    }
    
    /**
     * Coloca a formatao monetria ao valor informado. Deve ser utilizada para
     * exibio dos valores ao usurio e no para armazenamento.
     * Se o valor informado for invlido, ser retornado um <code>String</code> vazio.
     * @return Valor com formatao monetria.
     * @param valor Valor a ser formatado.
     */
    public static String adicionarFormatacaoMonetaria(BigDecimal valor, int fractionDigits, Locale locale){
        String literal="";
        try{
            literal = Valor.getDecimalFormat(fractionDigits, locale).format( valor );
        }catch(Exception ex){
            return "";
        }
        return literal;
    }
    
    /**
     * Coloca a formatao monetria ao valor informado. Deve ser utilizada para
     * exibio dos valores ao usurio e no para armazenamento.
     * Se o valor informado for invlido, ser retornado um <code>String</code> vazio.
     * @return Valor com formatao monetria.
     * @param valor Valor a ser formatado.
     */
    public static String adicionarFormatacaoMonetaria(double valor){
        return adicionarFormatacaoMonetaria( valor, 2 );
    }
    
    /**
     * Coloca a formatao monetria ao valor informado. Deve ser utilizada para
     * exibio dos valores ao usurio e no para armazenamento.
     * Se o valor informado for invlido, ser retornado um <code>String</code> vazio.
     * @return Valor com formatao monetria.
     * @param valor Valor a ser formatado.
     */
    public static String adicionarFormatacaoMonetaria(double valor, int fractionDigits){
        return adicionarFormatacaoMonetaria( new Double(valor), fractionDigits );
    }

    /**
     * Retorna o valor informado para uma formatao adequada para armazenamento persistente.
     * Deve ser utilizada para criao do valor a ser arquivado.
     * @return Valor sem a formatao monetria.
     * @param valor Valor a ter a formatao removida.
     */
    public static String removerFormatacaoMonetaria(String valor, int fractionDigits){
        char decimalSeparator = Valor.getDecimalFormat(fractionDigits).getDecimalFormatSymbols().getDecimalSeparator();
        String groupSeparator = String.valueOf( Valor.getDecimalFormat().getDecimalFormatSymbols().getGroupingSeparator() );
        
        valor = Coalesce.asString( valor );
        valor = valor.replaceAll( "[" + groupSeparator + "]", "" );
        valor = valor.replace( decimalSeparator, '.' );
        
        return valor;
    }
    
    /**
     * Retorna o valor informado para uma formatao adequada para armazenamento persistente.
     * Deve ser utilizada para criao do valor a ser arquivado.
     * @return Valor sem a formatao monetria.
     * @param valor Valor a ter a formatao removida.
     */
    public static String removerFormatacaoMonetaria(String valor){
        return removerFormatacaoMonetaria(valor, 2);
    }
    
    /**
     * Retorna o valor informado para uma formatao sem separadores.<br>
     * Segue exemplo:<br>
     *  valor original: 43.15
     *  valor retornado: 4315
     * @return Valor sem a formatao monetria.
     * @param valor Valor a ter a formatao removida.
     */
    public static String removerFormatadoresMonetarios(String valor, int fractionDigits){
        char decimalSeparator = Valor.getDecimalFormat(fractionDigits).getDecimalFormatSymbols().getDecimalSeparator();
        String groupSeparator = String.valueOf( Valor.getDecimalFormat().getDecimalFormatSymbols().getGroupingSeparator() );
        
        valor = valor.replaceAll( "[" + groupSeparator + "]", "" );
        valor = valor.replaceAll( "[" + decimalSeparator + "]", "" );
        
        return valor;
    }
    
    /**
     * Retorna o valor informado para uma formatao sem separadores.<br>
     * Segue exemplo:<br>
     *  valor original: 43.15
     *  valor retornado: 4315
     * @return Valor sem a formatao monetria.
     * @param valor Valor a ter a formatao removida.
     */
    public static String removerFormatadoresMonetarios(String valor){
        return removerFormatadoresMonetarios( valor, 2 );
    }
    
    /**
     * Retorna o valor informado para uma formatao sem separadores.<br>
     * Segue exemplo:<br>
     *  valor original: 43.15
     *  valor retornado: 4315
     * @return Valor sem a formatao monetria.
     * @param valor Valor a ter a formatao removida.
     */
    public static String removerFormatadoresMonetarios(Double valor){
        return removerFormatadoresMonetarios( adicionarFormatacaoMonetaria( valor ), 2 );
    }
    
    public static double round(double valor, int casas) {
        int multiplicador = 1;
        for(int i = 0; i < casas; i++) {
            multiplicador = multiplicador * 10;
        }
        
        double novoValor = Math.round(valor * multiplicador);
        novoValor =  novoValor / multiplicador;
        
        return novoValor;
    }
    
    public static double truncate(double valor, int casas) {
        int multiplicador = 1;
        for(int i = 0; i < casas; i++) {
            multiplicador = multiplicador * 10;
        }
        
        double novoValor = Math.floor(valor * multiplicador);
        novoValor =  novoValor / multiplicador;
        
        return novoValor;
    }
    
    /**
     * Converte um nmero no formato string para long.<br>
     * Caso ocorra erro na formatao ser retornado nulo.
     * @param number nmero no formato string
     * @return nmero convertido pra long
     */
    public static Long parseLong(String number) {
        try {
            return Long.parseLong( number );
        } catch(NumberFormatException e) {
            return null;
        }
    }

    public static List<Long> resolveSomatorio(Long soma) {
        List<Long> result = new ArrayList<Long>();

        while( soma > 0 )
        {
            Long count = 1L;
            while( soma > count )
            {
                count = count * 2;
            }
            if( count > soma )
            {
                count /= 2;
            }
            result.add(count);
            soma -= count;
        }

        return result;
    }

    public static boolean isValorSelecionado(Long valorCheckbox, Long somatorio) {
        List<Long> result = resolveSomatorio(somatorio);
        return result.contains(valorCheckbox);
    }

}
