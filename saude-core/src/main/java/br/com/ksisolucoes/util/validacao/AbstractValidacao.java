/*
 * AbstractValidacao.java
 *
 * Created on 15 de Maro de 2007, 17:52
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */

package br.com.ksisolucoes.util.validacao;

import br.com.ksisolucoes.util.Coalesce;

import java.io.Serializable;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;

import org.apache.commons.lang.NullArgumentException;
import org.apache.commons.lang.SystemUtils;

/**
 *
 * <AUTHOR>
 */
public abstract class AbstractValidacao implements Serializable {
    
    private static long moficador = 1;
    protected Map<Object, MensagemValidacao> mensagemValidacaoMap = new LinkedHashMap<Object, MensagemValidacao>();
    
    public AbstractValidacao() {}
    
    /**
     * Cria uma nova instncia de <CODE>ExceptionDetalhes</CODE>.
     * @param e causa da exceo.
     */
    public AbstractValidacao(Throwable e) {
    	this.add(e.getMessage(), e.getCause().toString());
    }
    
    /**
     * Adiciona uma nova mensagemValidacaoMap.
     * 
     * @param campo O campo ou identificador para a inconscistncia.
     *  Caso o campo no seja passado por parmetro, ser atribuida uma chave para a menssagem.
     * @param mensagemValidacaoMap A mensagemValidacaoMap para a referida validao.
     */
    public AbstractValidacao add(Object campo, String mensagem) throws NullArgumentException {
        return this.add( campo, mensagem, Coalesce.asString( campo )  );
    }
    
    
    /**
     * Adiciona uma nova mensagemValidacaoMap.
     * 
     * @param mensagemValidacaoMap A mensagemValidacaoMap para a referida validao.
     */
    public AbstractValidacao add(String mensagem) throws NullArgumentException {
        return this.add( null, mensagem, null );
    }
    
    
    /**
     * Adiciona uma nova mensagemValidacaoMap e seus detalhes.
     * 
     * @param campo O campo ou identificador para a inconscistncia.
     *  Caso o campo no seja passado por parmetro, ser atribuida uma chave para a menssagem.
     * @param mensagemValidacaoMap A mensagemValidacaoMap para a referida validao.
     * @param detalhes Detalhes da mensagemValidacaoMap para a referida validao, onde se null, ser vazia.
     */
    public AbstractValidacao add(String mensagem, String detalhes) throws NullArgumentException {
        return this.add(null, mensagem, detalhes);
    }
    
    /**
     * 
     * @param retornoValidacao 
     * @return 
     */
    public AbstractValidacao merge(AbstractValidacao retornoValidacao) {
        this.mensagemValidacaoMap.putAll( retornoValidacao.getMensagemValidacaoMap() );
        
        return this;
    }
    /**
     * Adiciona uma nova mensagemValidacaoMap e seus detalhes.
     * 
     * @param campo O campo ou identificador para a inconscistncia.
     *  Caso o campo no seja passado por parmetro, ser atribuida uma chave para a menssagem.
     * @param mensagemValidacaoMap A mensagemValidacaoMap para a referida validao.
     * @param detalhes Detalhes da mensagemValidacaoMap para a referida validao, onde se null, ser vazia.
     */
    public AbstractValidacao add(Object campo, String mensagem, String detalhes) throws NullArgumentException {
        
         if( campo == null ) {
            campo = this.generateKey();
        }
        
        try{
            if (mensagem == null){
                throw new NullArgumentException("A 'mensagem' no pode ser nulo!");
            }
            if ( detalhes == null ){
                detalhes = "";
            }
            
            this.mensagemValidacaoMap.put( campo, new MensagemValidacao(campo, mensagem, detalhes) );
        }  catch (NullPointerException e){
            throw new NullArgumentException(e.getMessage() );
        }
        
        return this;
    }
    
    /**
     * Obtm uma determinada mensagemValidacaoMap, com base no nome do campo.
     * 
     * @param campo Nome do campo o qual deseja-se a mensagemValidacaoMap.
     * @return A mensagemValidacaoMap correspondente ao campo indicado.
     */
    public String getMensagem(String campo) {
        try{
            if (campo == null){
                throw new NullArgumentException("O 'campo' no pode ser nulo!");
            }

            if(mensagemValidacaoMap.get( campo ) != null){
                return mensagemValidacaoMap.get( campo ).getMensagem();
            }
            return null;
        }  catch (NullPointerException e){
            throw new NullArgumentException(e.getMessage() );
        }
    }
    
    
    /**
     * Retorna valido se no houver nenhuma mensagemValidacaoMap de erro cadastrada, ou seja,
     * a operao de validao foi bem sucedida.
     * 
     * @return Se a validao foi bem sucedida.
     */
    public boolean isValido() {
        return isVazio();
    }
    
    
    /**
     * Avalia se existe alguma mensagemValidacaoMap cadastrada durante a validao.
     * 
     * @return <CODE>true</CODE> se no houver nenhuma entrada na instncia.
     */
    public boolean isVazio() {
        return mensagemValidacaoMap == null || mensagemValidacaoMap.isEmpty();
    }
    
    
    /**
     * Remove todas as entradas de erros da referida instncia.
     */
    public void limpar() {
        mensagemValidacaoMap = new LinkedHashMap<Object, MensagemValidacao>();
    }
    
    
    /**
     * Remove a entrada feita anteriormente, com base no nome do campo.
     * 
     * @param campo Campo a ser excludo da mensagemValidacaoMap de mensagens.
     * @return A mensagemValidacaoMap a qual foi removida.
     */
    public String remove(String campo) {
        try{
            if (campo == null){
                throw new NullArgumentException("O 'campo' no pode ser nulo!");
            }
            
            return mensagemValidacaoMap.remove( campo ).getMensagem();
            
        }  catch (NullPointerException e){
            return "";
        }
    }
    
    
    /**
     * Retorna a quantidade de campos os quais no foram validados com exito.
     * @return quantidade de campos inconscistentes.
     */
    public int tamanho() {
        if (mensagemValidacaoMap == null){
            return 0;
        }
        return mensagemValidacaoMap.size();
    }
    
    
    /**
     * Retorna as mensagens concatenadas.
     * @return Se a validao foi bem sucedida.
     */
    public String toString() {
        String retorno = "<html>";
        
        if (mensagemValidacaoMap != null){
            //retorno = mensagemValidacaoMap.values().toString();
            for (Iterator i = mensagemValidacaoMap.values().iterator(); i.hasNext(); ){
                retorno += i.next()  + "</br> ";
            }
        }
        
        retorno += "</html>";
        
        return retorno;
    }
    
    /**
     * Gera uma chave quanquer para utilizao no map.
     *
     * @return Long System.currentTimeMillis()
     */
    private synchronized Long generateKey() {
        return System.currentTimeMillis() + moficador++;
    }

    public Map<Object, MensagemValidacao> getMensagemValidacaoMap() {
        return mensagemValidacaoMap;
    }
}
