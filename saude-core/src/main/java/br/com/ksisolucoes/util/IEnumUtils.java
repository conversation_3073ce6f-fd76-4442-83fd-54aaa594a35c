package br.com.ksisolucoes.util;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.enums.IEnumSum;
import br.com.ksisolucoes.vo.prontuario.basico.resultado.saudemulher.ResultadoSaudeMulher;

import java.util.ListIterator;

/**
 * <AUTHOR>
 */
public final class IEnumUtils {
    public static String getDescricaoIEnumSum(IEnumSum<?>[] iEnumSum, Long index) {
        if (iEnumSum == null || index == null) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        ListIterator<Long> valorIterator = Valor.resolveSomatorio(index).listIterator();
        if (valorIterator == null) {
            return "";
        }
        while (valorIterator.hasNext()) {
            if (iEnumSum.length > 0) {
                Long valor = valorIterator.next();
                for (int i = 0; i < iEnumSum.length; i++) {
                    if (iEnumSum[i] != null && valor.equals(iEnumSum[i].sum())) {
                        sb.append(iEnumSum[i].descricao());
                        if (iEnumSum.length > i && valor.equals(iEnumSum[i++].sum()) && valorIterator.hasNext()) {
                            sb.append(", ");
                            break;
                        }
                    }
                }
            }
        }
        return sb.toString();
    }

    public static String getDescricaoIEnum(IEnum<?>[] iEnum, Long index) {
        if (iEnum == null || index == null) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < iEnum.length; i++) {
            if (iEnum[i] != null && index.equals(iEnum[i].value())) {
                sb.append(iEnum[i].descricao());
                if (iEnum.length > i) {
                    sb.append(", ");
                }
            }
        }
        return sb.toString();
    }

    public static String getUmaDescricao(IEnum<?>[] iEnum, Long value){
        for (IEnum<?> anEnum : iEnum) {
            if(anEnum.value().equals(value)){
                return anEnum.descricao();
            }
        }
        return null;
    }

    public static IEnum<?> getUmIEnum(IEnum<?>[] iEnum, Long value){
        for (IEnum<?> anEnum : iEnum) {
            if(anEnum.value().equals(value)){
                return anEnum;
            }
        }
        return null;
    }

    public String getDescricaoResultado(ResultadoSaudeMulher rsm) {
        StringBuilder descricao = new StringBuilder();
        descricao.append("dataResultado" + ": ");
        descricao.append(rsm.getDataResultado().toString());

        descricao.append(getDescricaoIEnumSum(ResultadoSaudeMulher.Microbiologia.values(), rsm.getMicrobiologia()));

        return descricao.toString();
    }
}
