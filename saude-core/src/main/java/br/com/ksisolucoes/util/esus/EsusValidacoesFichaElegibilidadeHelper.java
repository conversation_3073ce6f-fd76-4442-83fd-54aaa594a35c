package br.com.ksisolucoes.util.esus;

import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Pais;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAvaliacaoElegebilidadeAdmissao;
import br.com.ksisolucoes.vo.esus.CboFichaEsus;
import br.com.ksisolucoes.vo.esus.CboFichaEsusItem;
import br.com.ksisolucoes.vo.esus.dto.EsusValidacoesFichasDTOParam;
import ch.lambdaj.Lambda;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static br.com.ksisolucoes.util.esus.EsusHelper.*;
import static ch.lambdaj.Lambda.having;
import static ch.lambdaj.Lambda.on;
import static org.hamcrest.Matchers.equalTo;

/**
 * Created by maicon on 04/09/17.
 */
public class EsusValidacoesFichaElegibilidadeHelper {

    private EsusValidacoesFichaElegibilidadeHelper() {}

    public static String validate(EsusValidacoesFichasDTOParam validationParam) throws ValidacaoException {
        StringBuilder stringBuilder = new StringBuilder();

        stringBuilder.append(getInconsistencies(validationParam, validateCareUnit(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateProfessional(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateCpfCns(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateName(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateGender(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateBirthdate(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateColor(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateOrigin(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateModality(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateCID(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateAddress(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateShift(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateNationality(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateMotherName(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateFatherName(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateCountry(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateBirthCity(validationParam)));

        return stringBuilder.toString();
    }

    public static String validateGender(EsusValidacoesFichasDTOParam validationParam) {
        StringBuilder stringBuilder = new StringBuilder();

        validateItem(validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getSexo() == null, stringBuilder, "Sexo é obrigatório");

        return stringBuilder.toString();
    }

    public static String validateShift(EsusValidacoesFichasDTOParam validationParam) {
        StringBuilder stringBuilder = new StringBuilder();

        validateItem(validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getTurno() == null, stringBuilder, "Turno é obrigatório");

        return stringBuilder.toString();
    }

    public static String validateCID(EsusValidacoesFichasDTOParam validationParam) {
        StringBuilder stringBuilder = new StringBuilder();

        validateItem(validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getCidPrincipal() == null
                || validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getCidPrincipal().getCodigo() == null, stringBuilder, "Cid principal é obrigatório");

        validateItem(validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getCidPrincipal() != null
                        && validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getCidSecundario() != null
                        && validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getCidPrincipal().getCodigo().equals(validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getCidSecundario().getCodigo()),
                stringBuilder,
                "Cid secundário não pode ser igual ao primário"
        );

        validateItem(validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getCidPrincipal() != null
                        && validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getCidTerciario() != null
                        && validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getCidPrincipal().getCodigo().equals(validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getCidTerciario().getCodigo()),
                stringBuilder,
                "Cid terciário não pode ser igual ao primário"
        );

        validateItem(validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getCidSecundario() != null
                        && validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getCidTerciario() != null
                        && validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getCidSecundario().getCodigo().equals(validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getCidTerciario().getCodigo()),
                stringBuilder,
                "Cid secundário não pode ser igual ao terciário"
        );

        return stringBuilder.toString();
    }

    public static String validateOrigin(EsusValidacoesFichasDTOParam validationParam) {
        StringBuilder stringBuilder = new StringBuilder();

        validateItem(validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getAtencaoDomiciliarOrigem() == null, stringBuilder, "Procedência é obrigatória");

        return stringBuilder.toString();
    }

    public static String validateModality(EsusValidacoesFichasDTOParam validationParam) {
        StringBuilder stringBuilder = new StringBuilder();

        validateItem(validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getAtencaoDomiciliarModalidade() == null, stringBuilder, "Modalidade é obrigatória");

        return stringBuilder.toString();
    }

    public static String validateNationality(EsusValidacoesFichasDTOParam validationParam) {
        EsusFichaAvaliacaoElegebilidadeAdmissao fichaAvalElegAdmissao = validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao();
        StringBuilder stringBuilder = new StringBuilder();

        validateItem(hasCommonCondition(fichaAvalElegAdmissao) && fichaAvalElegAdmissao.getNacionalidade() == null, stringBuilder, "Nacionalidade é obrigatória");

        return stringBuilder.toString();
    }

    public static String validateAddress(EsusValidacoesFichasDTOParam validationParam) {
        EsusFichaAvaliacaoElegebilidadeAdmissao fichaAvalElegAdmissao = validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao();
        StringBuilder stringBuilder = new StringBuilder();

        if (hasCommonCondition(fichaAvalElegAdmissao)) {

            validateItem(fichaAvalElegAdmissao.getBairro() == null, stringBuilder, "Bairro é obrigatório");
            validateItem(fichaAvalElegAdmissao.getCep() == null, stringBuilder, "Cep é obrigatório");
            validateItem(fichaAvalElegAdmissao.getCidade() == null || fichaAvalElegAdmissao.getCidade().getCodigo() == null, stringBuilder, "Codigo IBGE é obrigatório");
            validateItem(fichaAvalElegAdmissao.getLogradouro() == null, stringBuilder, "Logradouro é obrigatório");
            validateItem(fichaAvalElegAdmissao.getCidade() == null || fichaAvalElegAdmissao.getCidade().getEstado() == null || fichaAvalElegAdmissao.getCidade().getEstado().getCodigoEsus() == null, stringBuilder, "Número DNE UF é obrigatório");
            validateItem(fichaAvalElegAdmissao.getTelefoneResidencial() == null && fichaAvalElegAdmissao.getTelefoneReferencial() == null, stringBuilder, "Ao menos um telefone deve ser informado");
            validateItem(fichaAvalElegAdmissao.getTipoLogradouro() == null || fichaAvalElegAdmissao.getTipoLogradouro().getCodigo() == null, stringBuilder, "Tipo de logradouro é obrigatório");
        }

        return stringBuilder.toString();
    }

    public static String validateColor(EsusValidacoesFichasDTOParam validationParam) {
        EsusFichaAvaliacaoElegebilidadeAdmissao fichaAvalElegAdmissao = validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao();
        StringBuilder stringBuilder = new StringBuilder();

        validateItem(hasCommonCondition(fichaAvalElegAdmissao) && fichaAvalElegAdmissao.getRacaCor() == null, stringBuilder, "Raça/Cor é obrigatório");

        return stringBuilder.toString();
    }

    public static String validateBirthdate(EsusValidacoesFichasDTOParam validationParam) {
        StringBuilder stringBuilder = new StringBuilder();
        EsusFichaAvaliacaoElegebilidadeAdmissao fichaAvalElegAdmissao = validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao();

        if (fichaAvalElegAdmissao.getDataNascimento() == null) {
            appendInconsitency(stringBuilder, "Sem data de nascimento informada, paciente: " + fichaAvalElegAdmissao.getNomeCidadao());
        } else {
            if (fichaAvalElegAdmissao.getDataNascimento().after(fichaAvalElegAdmissao.getDataAtendimento())) {
                appendInconsitency(stringBuilder, "A data de nascimento deve ser anterior a data do atendimento, paciente: " + validationParam.getUsuarioCadsus().getNome());
            }
            if (DataUtil.getAnosDiferenca(Data.adjustRangeHour(fichaAvalElegAdmissao.getDataNascimento()).getDataInicial(), Data.adjustRangeHour(fichaAvalElegAdmissao.getDataAtendimento()).getDataInicial()) > 130L) {
                appendInconsitency(stringBuilder, "A data de nascimento não deve compor uma idade superior a 130 anos, paciente: " + validationParam.getUsuarioCadsus().getNome());
            }
        }

        return stringBuilder.toString();
    }

    public static String validateCboProfessional(EsusValidacoesFichasDTOParam validationParam) throws ValidacaoException {
        StringBuilder stringBuilder = new StringBuilder();

        if (validationParam.getTabelaCbo() == null) {
            appendInconsitency(stringBuilder, "CBO não definido.");
        } else if (CollectionUtils.isEmpty(validationParam.getCboFichaEsusItemList())) {
            if (!existsCboFichaEsusItem(validationParam)) {
                appendInconsitency(stringBuilder, "CBO " + validationParam.getTabelaCbo().getDescricaoFormatado() + " do profissional " + validationParam.getProfissional().getNome() + " não permitido.");
            }
        } else if (!Lambda.exists(validationParam.getCboFichaEsusItemList(), having(on(CboFichaEsusItem.class).getTabelaCbo().getCbo(), equalTo(validationParam.getTabelaCbo().getCbo())))) {
            appendInconsitency(stringBuilder, "CBO " + validationParam.getTabelaCbo().getDescricaoFormatado() + " do profissional " + validationParam.getProfissional().getNome() + " não permitido.");
        }

        getReturnMessage(validationParam, stringBuilder.toString());

        return stringBuilder.toString();
    }

    public static String validateCountry(EsusValidacoesFichasDTOParam validationParam) {
        StringBuilder stringBuilder = new StringBuilder();
        Pais paisNascimento = validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getPaisNascimento();

        validateItem(
                paisNascimento != null && paisNascimento.getCodigoEsus() == null,
                stringBuilder,
                "PAÍS: " + (paisNascimento != null ? paisNascimento.getDescricao() : "") + ", Sem código e-Sus definido."
        );

        return stringBuilder.toString();
    }

    public static String validateBirthCity(EsusValidacoesFichasDTOParam validationParam) {
        EsusFichaAvaliacaoElegebilidadeAdmissao esusFichaAvaliacaoElegebilidadeAdmissao = validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao();
        StringBuilder stringBuilder = new StringBuilder();

        boolean isBrazilian = EsusFichaAvaliacaoElegebilidadeAdmissao.Nacionalidade.BRASILEIRA.value().equals(esusFichaAvaliacaoElegebilidadeAdmissao.getNacionalidade());

        validateItem(
                isBrazilian && esusFichaAvaliacaoElegebilidadeAdmissao.getCidadeNascimento() != null
                        && (esusFichaAvaliacaoElegebilidadeAdmissao.getCidadeNascimento().getCodigo() == null
                        || esusFichaAvaliacaoElegebilidadeAdmissao.getCidadeNascimento().getCodigo().toString().length() != 6),
                stringBuilder,
                Bundle.getStringApplication("msg_ficha_cidade_nascimento_sem_codigo_IBGE_valido")
        );

        return stringBuilder.toString();
    }

    public static String validateCpfCns(EsusValidacoesFichasDTOParam validationParam) {
        EsusFichaAvaliacaoElegebilidadeAdmissao fichaAvalElegAdmissao = validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao();
        StringBuilder stringBuilder = new StringBuilder();

        validateItem(
                (fichaAvalElegAdmissao.getUsuarioCadsus() == null || fichaAvalElegAdmissao.getUsuarioCadsus().getCpf() == null || fichaAvalElegAdmissao.getUsuarioCadsus().getCpf().isEmpty())
                        && fichaAvalElegAdmissao.getCns() == null,
                stringBuilder,
                Bundle.getStringApplication("msg_ficha_cpf_cns_nao_informados")
        );

        return stringBuilder.toString();
    }

    public static String validateMotherName(EsusValidacoesFichasDTOParam validationParam) {
        StringBuilder stringBuilder = new StringBuilder();

        if (hasCommonCondition(validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao())) {
            if (isNomeMaePreenchido(validationParam)) {
                appendInconsitency(stringBuilder, "O nome da mãe do Paciente: " + getNomePaciente(validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao()) + " é de preenchimento obrigatório.");
            } else if (!EsusHelper.isNameValid(validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getNomeMae())) {
                appendInconsitency(stringBuilder, "O nome da mãe do Paciente: " + getNomePaciente(validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao()) + " é inválido.");
            }
        }

        return stringBuilder.toString();
    }

    private static String getNomePaciente(EsusFichaAvaliacaoElegebilidadeAdmissao esusFichaAvaliacaoElegebilidadeAdmissao) {
        return (esusFichaAvaliacaoElegebilidadeAdmissao.getUsuarioCadsus() == null) ? esusFichaAvaliacaoElegebilidadeAdmissao.getNomeCidadao() : esusFichaAvaliacaoElegebilidadeAdmissao.getUsuarioCadsus().getNome();
    }

    private static boolean isNomeMaePreenchido(EsusValidacoesFichasDTOParam validationParam) {
        EsusFichaAvaliacaoElegebilidadeAdmissao fichaAvaliacaoElegibilidade = validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao();
        return fichaAvaliacaoElegibilidade.getNomeMae() == null && RepositoryComponentDefault.NAO_LONG.equals(fichaAvaliacaoElegibilidade.getDesconheceNomeMae());
    }

    public static String validateName(EsusValidacoesFichasDTOParam validationParam) {
        EsusFichaAvaliacaoElegebilidadeAdmissao fichaAvalElegAdmissao = validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao();
        StringBuilder stringBuilder = new StringBuilder();

        if (hasCommonCondition(fichaAvalElegAdmissao)) {
            validateItem( fichaAvalElegAdmissao.getNomeCidadao() == null, stringBuilder, Bundle.getStringApplication("msg_ficha_nome_nao_informado"));
            validateItem(fichaAvalElegAdmissao.getNomeCidadao() != null && !EsusHelper.isNameValid(fichaAvalElegAdmissao.getNomeCidadao()), stringBuilder, "O nome do Paciente é inválido.");
        }

        return stringBuilder.toString();
    }

    public static String validateFatherName(EsusValidacoesFichasDTOParam validationParam) {
        StringBuilder stringBuilder = new StringBuilder();

        if (hasCommonCondition(validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao()) &&
            RepositoryComponentDefault.NAO_LONG.equals(Coalesce.asLong(validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getDesconheceNomePai(), RepositoryComponentDefault.NAO_LONG)))
        {
            if (validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getNomePai() == null) {
                appendInconsitency(stringBuilder, "O nome do pai do Paciente: " + validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getNomeCidadao() + " é de preenchimento obrigatório.");
            } else if (!EsusHelper.isNameValid(validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getNomePai())) {
                appendInconsitency(stringBuilder, "O nome do pai do Paciente: " + validationParam.getEsusFichaAvaliacaoElegebilidadeAdmissao().getNomeCidadao() + " é inválido.");
            }
        }

        return stringBuilder.toString();
    }

    private static boolean existsCboFichaEsusItem(EsusValidacoesFichasDTOParam validationParam) {
        CboFichaEsusItem cboFichaEsusItemProxy = on(CboFichaEsusItem.class);

        return LoadManager.getInstance(CboFichaEsusItem.class)
                .addParameter(new QueryCustom.QueryCustomParameter(path(cboFichaEsusItemProxy.getTabelaCbo()), validationParam.getTabelaCbo()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(cboFichaEsusItemProxy.getCboFichaEsus().getFicha()), CboFichaEsus.TipoFicha.FICHA_AVALIACAO_ELEGEBILIDADE.value()))
                .start()
                .exists();
    }

    private static boolean hasCommonCondition(EsusFichaAvaliacaoElegebilidadeAdmissao fichaAvalElegAdmissao) {
        return EsusFichaAvaliacaoElegebilidadeAdmissao.ConclusaoDestinoElegivel.ADMISSAO_PROPRIA_EMAD.value().equals(fichaAvalElegAdmissao.getConclusaoDestinoElegivel())
                && (EsusFichaAvaliacaoElegebilidadeAdmissao.Modalidade.AD1.value().equals(fichaAvalElegAdmissao.getAtencaoDomiciliarModalidade())
                || EsusFichaAvaliacaoElegebilidadeAdmissao.Modalidade.AD2.value().equals(fichaAvalElegAdmissao.getAtencaoDomiciliarModalidade())
                || EsusFichaAvaliacaoElegebilidadeAdmissao.Modalidade.AD3.value().equals(fichaAvalElegAdmissao.getAtencaoDomiciliarModalidade()));
    }
}