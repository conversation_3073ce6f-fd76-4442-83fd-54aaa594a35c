/*
 * Created on 20/09/2004
 *
 */
package br.com.ksisolucoes.util;

import br.com.ksisolucoes.util.log.Loggable;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * <AUTHOR>
 *  
 */
public class PropertieUtils {
    
    private final Properties props;
    
    public PropertieUtils(InputStream stream) {
        this.props = new Properties();
        try {
            this.props.load(stream);
        } catch (IOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
    }
    
    /**
     * O caminho do arquivo  calculado relativamente ao classpath.
     * 
     * @param fileProperties
     */
    public PropertieUtils(String fileProperties) {
        fileProperties = "" + fileProperties;
        this.props = new Properties();
        try {
            this.props.load(this.getClass().getResourceAsStream(fileProperties));
        }
        catch (IOException ex) {
            Loggable.log.error(ex);
        }
    }
    
    /**
     * Retorna o valor da propriedade lida no arquivo.
     * 
     * @param propertie
     * @return valor da propriedade lida no arquivo
     */
    public String getPropertie(String propertie) {
        //Le e retorna a propriedade lida no arquivo
        return this.props.getProperty(propertie);
    }
    
    /**
     * Seta um valor para uma propriedade.
     * 
     * @param propertie
     * @param valor
     */
    public void setPropertie(String propertie, String valor) {
        this.props.setProperty(propertie, valor);
    }
    
}