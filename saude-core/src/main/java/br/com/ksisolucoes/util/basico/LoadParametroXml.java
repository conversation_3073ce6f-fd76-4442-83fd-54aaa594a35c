/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.util.basico;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.parametrogem.NivelParametroGem;
import br.com.ksisolucoes.vo.basico.ParametroGem;
import br.com.ksisolucoes.vo.basico.ParametroGemPK;
import br.com.ksisolucoes.vo.controle.Modulo;

/**
 *
 * <AUTHOR>
 */
public class LoadParametroXml {

    private List<ParametroGem> configuracoes;
    private List<Modulo> modulosList;

    private static LoadParametroXml instance;

    public static LoadParametroXml getInstance(){
//        if (instance == null) {
            instance = new LoadParametroXml();
//        }
        return instance;
    }

    private LoadParametroXml() {
        try {
            loadXmls();
        } catch (Exception ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    private Modulo getModulo(Long id){
        if(modulosList == null){
            modulosList = LoadManager.getInstance(Modulo.class).start().getList();
        }
        
        for (Modulo modulosList1 : modulosList) {
            if(modulosList1.getCodigo().equals(id)){
                return modulosList1;
            }
        }
        return null;
    }
    
    private void loadXmls() throws Exception {
          InputStream inputStream = getClass().getResourceAsStream( "/Parametros.xml" );

          SAXReader reader = new SAXReader();

          Document documento = reader.read(inputStream);

          this.configuracoes = parsearXmls(documento);

    }

    private List<ParametroGem> parsearXmls(Document documento) throws Exception {

        List<ParametroGem> gems = new ArrayList<ParametroGem>();

        
        
        //Modulos
        for (Object mod : documento.getRootElement().elements("modulo")) {
            Element moduloEle = (Element) mod;
            String modulo_ = moduloEle.attributeValue("nome");
            Modulo modulo = getModulo(Long.valueOf(modulo_));
            //Parametros
            for (Object ele : moduloEle.elements()) {
                Element parametro = (Element) ele;
                String parametro_ = parametro.attributeValue("nome");
                String observacao_ = parametro.attributeValue("observacao");
                String value_ = parametro.attributeValue("defaultValue");
                String tipo_ = parametro.attributeValue("tipo");
                String grupo_ = parametro.attributeValue("grupo");
                String obrigatorio_ = parametro.attributeValue("obrigatorio");
                String valorMinimo_ = parametro.attributeValue("valorMinimo");
                String valorMaximo_ = parametro.attributeValue("valorMaximo");

                ParametroGem parametroGem = new ParametroGem(new ParametroGemPK());
                parametroGem.getId().setModulo(modulo);
                parametroGem.getId().setNivel(NivelParametroGem.GERAL.getNivel());
                parametroGem.getId().setParametro(parametro_);
                parametroGem.getId().setIdentificador("0");

                parametroGem.setObservacao(observacao_);
                parametroGem.setValor(value_);
//                parametroGem.setValorDefault(value_);
                parametroGem.setValorMinimo(valorMinimo_);
                parametroGem.setValorMaximo(valorMaximo_);
                parametroGem.setObrigatorio(Boolean.valueOf(obrigatorio_));
                try {
                    parametroGem.setType(Class.forName(tipo_));
                } catch (Exception ex) {
                    Loggable.log.error("O tipo do parametro: " + parametro_ + " n�o est� definido corretamente!", ex);
                    throw ex;
                }
                parametroGem.setGrupo(grupo_);

                Map<String,String> checkValues = new LinkedHashMap<String, String>();
                for (Object cv : parametro.elements("checkValues")) {
                    Element check_ = (Element) cv;
                    checkValues.put(check_.attributeValue("rotulo"), check_.attributeValue("value"));
                    parametroGem.setCheckValues(checkValues);
                }

                gems.add(parametroGem);
            }
        }

        return gems;
    }

    public List<ParametroGem> getConfiguracoes() {
        return configuracoes;
    }

}
