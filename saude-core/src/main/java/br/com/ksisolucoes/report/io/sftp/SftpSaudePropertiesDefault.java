/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.io.sftp;

import br.com.ksisolucoes.io.sftp.SftpDefaultUserInfo;
import br.com.ksisolucoes.io.sftp.SftpProperties;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Parametro;

/**
 *
 * <AUTHOR>
 */
public class SftpSaudePropertiesDefault {

    public static SftpProperties getSftpProperties(){
        Parametro parametro = CargaBasicoPadrao.getInstance().getParametroPadrao();

        SftpProperties properties = new SftpProperties();
        properties.setCompress(RepositoryComponentDefault.SIM.equals(parametro.getSftpCompressao())?true:false);
        properties.setHost(parametro.getSftpServidor());
        properties.setUserName(parametro.getSftpUsuario());
        SftpDefaultUserInfo sftpDefaultUserInfo = new SftpDefaultUserInfo();
        sftpDefaultUserInfo.setPass(parametro.getSftpSenha());
        properties.setUserInfo(sftpDefaultUserInfo);

        return properties;
    }
}
