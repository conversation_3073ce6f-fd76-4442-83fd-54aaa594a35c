package br.com.ksisolucoes.report.export;

import org.apache.commons.io.FilenameUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Iterator;

public class ExportListToCsv {

    public static ExportListToCsv getInstance() {
        return new ExportListToCsv();
    }

    public File extrairParaArquivoCsv(File inputFile) {
        File outputFile = null;
        try {
            outputFile = File.createTempFile(inputFile.getName(), ".csv");
            StringBuffer data = new StringBuffer();

            try {
                FileOutputStream fos = new FileOutputStream(outputFile);
                FileInputStream fis = new FileInputStream(inputFile);
                Workbook workbook = null;
                String ext = FilenameUtils.getExtension(inputFile.toString());
                workbook = new HSSFWorkbook(fis);
                int numberOfSheets = workbook.getNumberOfSheets();
                Row row;
                Cell cell;

                for (int i = 0; i < numberOfSheets; i++) {
                    Sheet sheet = workbook.getSheetAt(0);
                    Iterator<Row> rowIterator = sheet.iterator();

                    while (rowIterator.hasNext()) {
                        row = rowIterator.next();
                        Iterator<Cell> cellIterator = row.cellIterator();
                        while (cellIterator.hasNext()) {
                            cell = cellIterator.next();
                            switch (cell.getCellType()) {
                                case Cell.CELL_TYPE_BOOLEAN:
                                    data.append(cell.getBooleanCellValue() + ",");
                                    break;
                                case Cell.CELL_TYPE_NUMERIC:
                                    data.append(cell.getNumericCellValue() + ",");
                                    break;
                                case Cell.CELL_TYPE_STRING:
                                    data.append(cell.getStringCellValue() + ",");
                                    break;
                                case Cell.CELL_TYPE_BLANK:
                                    data.append("" + ",");
                                    break;
                                default:
                                    data.append(cell + ",");
                            }
                        }
                        data.append('\n');
                    }
                }
                fos.write(data.toString().getBytes());
                fos.close();

            } catch (Exception ioe) {
                ioe.printStackTrace();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return outputFile;
    }
}