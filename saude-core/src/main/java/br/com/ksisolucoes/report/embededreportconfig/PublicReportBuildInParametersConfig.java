package br.com.ksisolucoes.report.embededreportconfig;

import br.com.ksisolucoes.report.datasource.ReportDataSource;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.jasperreport.JasperInstance;
import br.com.ksisolucoes.util.Bundle;

public class PublicReportBuildInParametersConfig extends AbstractReportBuildInParametersConfig {
    private String descricaoUsuario;
    private String descricaoEmpresa;
    private String descricaoCabecalho1;
    private String descricaoCabecalho2;
    private String tituloRelatorio;

    public PublicReportBuildInParametersConfig(String descricaoUsuario, String descricaoEmpresa, String descricaoCabecalho1, String descricaoCabecalho2, String tituloRelatorio) {
        this.descricaoUsuario = descricaoUsuario;
        this.descricaoEmpresa = descricaoEmpresa;
        this.descricaoCabecalho1 = descricaoCabecalho1;
        this.descricaoCabecalho2 = descricaoCabecalho2;
        this.tituloRelatorio = tituloRelatorio;
    }

    @Override
    public void postConfigure(JasperInstance jasperInstance, ReportDataSource dataSource) throws ReportException {
        dataSource.getMapParameters().put("TITULO_REPORT", tituloRelatorio);
        dataSource.getMapParameters().put("USUARIO_REPORT", descricaoUsuario);
        dataSource.getMapParameters().put("CLIENTE_REPORT", descricaoEmpresa);
        dataSource.getMapParameters().put("TITULO_JANELA_REPORT", descricaoCabecalho1);
        dataSource.getMapParameters().put("NOME_RELATORIO_REPORT", descricaoCabecalho2);

        dataSource.getMapParameters().put("DESC_CABECALHO_PADRAO", "");
        dataSource.getMapParameters().put("UNIDADE_ATENDIMENTO", "");
        dataSource.getMapParameters().put(RUA_UNIDADE, "");
        dataSource.getMapParameters().put(NUMERO_UNIDADE, "");
        dataSource.getMapParameters().put(BAIRRO_UNIDADE, "");
        dataSource.getMapParameters().put(CIDADE_UNIDADE, "");
        dataSource.getMapParameters().put(UF_UNIDADE, "");
        dataSource.getMapParameters().put(CEP_UNIDADE, "");
        dataSource.getMapParameters().put(FONE_UNIDADE, "");

        dataSource.getMapParameters().put("USUARIO_LOGADO", "");

        String versaoSistema = Bundle.getStringProject("version");
        dataSource.getMapParameters().put("VERSAO_SISTEMA", versaoSistema);
        dataSource.getMapParameters().put("SISTEMA", "CELK Saúde");
    }
}
