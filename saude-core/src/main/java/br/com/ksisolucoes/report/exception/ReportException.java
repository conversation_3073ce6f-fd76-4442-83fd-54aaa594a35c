/*
 * Created on 28/07/2004
 *
 * To change the template for this generated file go to
 * Window - Preferences - Java - Code Generation - Code and Comments
 */
package br.com.ksisolucoes.report.exception;

import br.com.ksisolucoes.util.ExceptionsDetalhes;
import br.com.ksisolucoes.util.StringUtilKsi;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Generation - Code and Comments
 */
public class ReportException extends Exception {
    
    /**
     * Constri uma instancia de <code>DAOException</code> especificando detalhes da menssagem.
     * @param msg detalhes da menssagem de erro.
     */
    public ReportException(String msg) {
        super(msg);
    }
    
    /**
     * Constri uma instancia de <code>DAOException</code> especificando detalhes e origem da menssagem.
     * @param msg detalhes da menssagem de erro.
     * @param causa origem da menssagem de erro.
     */
    public ReportException(String msg, Throwable causa) {
        super(msg, causa);
    }
    
    /**
     * Constri uma instancia de <code>DAOException</code> especificando origem da menssagem.
     * @param causa origem da menssagem de erro.
     */
    public ReportException(Throwable causa) {
        super(causa.getMessage(), causa);
    }
    
    private void setExceptionsDetalhes(){
        detalhes = new ExceptionsDetalhes();

        detalhes.add( getMessage(), StringUtilKsi.getStringStackTrace( this ) );
    }
    
    /**
     * Retorna uma instancia de <code>ExceptionsDetalhes</code> contendo detalhes da validao.
     * @return Detalhes da validao, contendo mensagens e detalhes dos problemas.
     */
    public ExceptionsDetalhes getExceptionsDetalhes(){
        setExceptionsDetalhes();
        return detalhes;
    }
    
    ExceptionsDetalhes detalhes;
    
    /**
     * Prints this throwable and its backtrace to the specified print stream.
     *
     * @param s <code>PrintStream</code> to use for output
     */
    public String getStackTraceException() {
        String s = "";
        s += this;
        StackTraceElement[] trace = getStackTrace();
        for (int i=0; i < trace.length; i++)
            s += "\tat " + trace[i];
                
        return s;
    }
    
}

