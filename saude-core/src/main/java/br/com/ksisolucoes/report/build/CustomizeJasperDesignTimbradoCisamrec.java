/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.build;

import br.com.ksisolucoes.report.exception.ReportException;
import net.sf.jasperreports.engine.JRBand;
import net.sf.jasperreports.engine.JRElement;
import net.sf.jasperreports.engine.JRParameter;
import net.sf.jasperreports.engine.design.JRDesignBand;
import net.sf.jasperreports.engine.design.JRDesignElement;
import net.sf.jasperreports.engine.design.JasperDesign;
import net.sf.jasperreports.engine.type.OrientationEnum;
import net.sf.jasperreports.engine.xml.JRXmlLoader;

import java.io.InputStream;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR> <PERSON>
 */
public class CustomizeJasperDesignTimbradoCisamrec implements ICustomizeJasperDesign{

    private boolean exibirRodape;

    public CustomizeJasperDesignTimbradoCisamrec() {
        exibirRodape = true;
    }

    public CustomizeJasperDesignTimbradoCisamrec(boolean exibirRodape) {
        this.exibirRodape = exibirRodape;
    }
    
    @Override
    public JasperDesign customizes(String xml) throws ReportException {
        try {
            JasperDesign relatorio = JRXmlLoader.load(xml);

            InputStream path = null;

            path = this.getClass().getResourceAsStream("/xml/cabecalho_timbrado_saude_cisamrec_a4_retrato.jrxml");

            JasperDesign cabecalhoRodape = JRXmlLoader.load(path);

            JRBand pageHeader = cabecalhoRodape.getPageHeader();
            JRBand pageFooter = cabecalhoRodape.getPageFooter();
            JRParameter[] parameters = cabecalhoRodape.getParameters();


            if (exibirRodape) {
                    if(relatorio.getPageFooter() != null && relatorio.getPageFooter().getHeight() > 0){
                        int tam = cabecalhoRodape.getPageFooter().getHeight();
                        int tamOriginal = relatorio.getPageFooter().getHeight();
                        ((JRDesignBand)relatorio.getPageFooter()).setHeight(relatorio.getPageFooter().getHeight() + tam);

                        for (JRElement jRElement : pageFooter.getElements()) {
                            ((JRDesignElement)jRElement).setY(jRElement.getY() + tamOriginal);
                            ((JRDesignBand)relatorio.getPageFooter()).addElement((JRDesignElement)jRElement);
                        }
                    }else{
                        relatorio.setPageFooter(pageFooter);
                    }
                }
            
            if(relatorio.getPageHeader() != null && relatorio.getPageHeader().getHeight() > 0){
                int tam = cabecalhoRodape.getPageHeader().getHeight();
                int tamOriginal = relatorio.getPageHeader().getHeight();
                ((JRDesignBand)cabecalhoRodape.getPageHeader()).setHeight(cabecalhoRodape.getPageHeader().getHeight() + tamOriginal);

                for (JRElement jRElement : ((JRDesignBand)relatorio.getPageHeader()).getElements()) {
                    ((JRDesignElement)jRElement).setY(jRElement.getY() + tam);
                    ((JRDesignBand)cabecalhoRodape.getPageHeader()).addElement((JRDesignElement)jRElement);
                }

                relatorio.setPageHeader(((JRDesignBand)cabecalhoRodape.getPageHeader()));
            }else{
                relatorio.setPageHeader(pageHeader);
            }

            List<JRParameter> parameterJasper = relatorio.getParametersList();
            for (JRParameter jRParameter : parameters) {
                boolean parametroNovo = true;
                for (JRParameter param : parameterJasper) {
                    if(param.getName().equals(jRParameter.getName())){
                        parametroNovo = false;
                        continue;
                    }
                }
                if(parametroNovo){
                    relatorio.addParameter(jRParameter);
                }
            }

            return relatorio;
        } catch (Exception ex) {
            throw new ReportException(ex);
        }
    }

    public static void main(String[] args){
        try {
            new CustomizeJasperDesignTimbradoCisamrec().customizes(null);
        } catch (ReportException ex) {
            Logger.getLogger(CustomizeJasperDesignTimbradoCisamrec.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
}
