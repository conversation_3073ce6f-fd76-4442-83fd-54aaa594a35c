/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.datatransfer;

import br.com.ksisolucoes.report.datasource.ReportDataSource;

/**
 *
 * <AUTHOR>
 */
public class JasperReportDataTransfer implements ReportDataTransfer{

    private ReportDataSource reportDataSource;

    public JasperReportDataTransfer(ReportDataSource reportDataSource) {
        this.reportDataSource = reportDataSource;
    }
    
    @Override
    public Object getData() {
        return this.reportDataSource;
    }
    
}
