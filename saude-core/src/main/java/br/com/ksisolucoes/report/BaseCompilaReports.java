/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import br.com.ksisolucoes.report.build.BuildReport;
import br.com.ksisolucoes.report.build.ICustomizeJasperDesign;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.log.Loggable;

/**
 *
 * <AUTHOR>
 */
public class BaseCompilaReports {
    
    public static final String DTD = "http://192.168.0.101:8081/report/jasperreport1_3.dtd";
    
    private static final long serialVersionUID = 1L;
    private String titulo;
    private BuildReport buildReport = new BuildReport();

    private Map<String, Exception> errors = new HashMap<String, Exception>();
    
    public void start(List<Relatorio> relatorios){
        System.out.println("--- Processo compilacao --- ");
        for (Relatorio relatorio : relatorios) {
            start(relatorio);
        }
        showErrors();
    }

    private void start(Relatorio relatorio) {
        try {
            String xml = relatorio.getJrxml();
            this.titulo = relatorio.getJrxml();

            this.alteraDTD(relatorio.getJrxml(), xml);
            buildReport.verificaAtualizacao(relatorio.getCustomizeJasperDesign(), xml);

        } catch (Exception e) {
            errors.put(relatorio.getJrxml(), e);
        }
    }

    private void showErrors() {
        if (!errors.isEmpty()) {
            System.out.println("--- Processo compilacao concluido com erros");

            for (Map.Entry<String, Exception> entry : errors.entrySet()) {
                Exception ex = entry.getValue();
                System.out.println("Jrxml: " + entry.getKey() + "\nErro: " + ex.getMessage());
                for (StackTraceElement stackTraceElement : ex.getStackTrace()) {
                    System.err.println("    - " + stackTraceElement.toString());
                }
                System.out.println("");
//              Aborta processo de build casso exista erro da compilação dos relatorios
                System.exit(1);
            }

        } else {
            System.out.println("--- Processo compilacao concluido");
        }
    }

    private void alteraDTD(String jrxml, String xml) throws ReportException {
        try {
            FileReader fileReader = new FileReader(this.getClass().getResource(jrxml).getPath());
            BufferedReader bufferedReader = new BufferedReader(fileReader);
            StringBuffer stringFile = new StringBuffer();
            String linha = null;

            while ((linha = bufferedReader.readLine()) != null) {

                if (linha.equals("<!DOCTYPE jasperReport PUBLIC \"//JasperReports//DTD Report Design//EN\" \"http://jasperreports.sourceforge.net/dtds/jasperreport.dtd\">")) {

                    linha = "<!DOCTYPE jasperReport PUBLIC \"//JasperReports//DTD Report Design//EN\" \"" + DTD + "\">";
                }

                stringFile.append(linha + "\n");
            }


            bufferedReader.close();
            fileReader.close();

            PrintWriter printWriter = new PrintWriter(new FileWriter(this.getClass().getResource(jrxml).getPath()));
            printWriter.print(stringFile);
            printWriter.flush();
            printWriter.close();

        } catch (NullPointerException e) {
            throw new ReportException("JRXML Inexistente - " + xml);
        } catch (IOException e) {
            throw new ReportException(e.getMessage(), e);
        }

    }

// </editor-fold>
    public static class Relatorio implements Serializable {

        private String jrxml;
        private ICustomizeJasperDesign customizeJasperDesign;

        public Relatorio(String jrxml, ICustomizeJasperDesign customizeJasperDesign) {
            this.setJrxml(jrxml);
            this.setCustomizeJasperDesign(customizeJasperDesign);
        }

        public Relatorio(String jrxml) {
            this.setJrxml(jrxml);
        }

        public String getJrxml() {
            return jrxml;
        }

        public void setJrxml(String jrxml) {
            this.jrxml = jrxml;
        }

        public ICustomizeJasperDesign getCustomizeJasperDesign() {
            return customizeJasperDesign;
        }

        public void setCustomizeJasperDesign(ICustomizeJasperDesign customizeJasperDesign) {
            this.customizeJasperDesign = customizeJasperDesign;
        }

    }

}
