package br.com.ksisolucoes.command;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.report.*;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

/**
 * <AUTHOR>
 *
 */
public class ExecutorRemote implements IExecutor {

    public ExecutorRemote() {
    }

    public void execute(InterfaceCommand command) throws DAOException, ValidacaoException {
        command.start();
    }

    public InterfaceCommand executeReturn(InterfaceCommand command) throws DAOException, ValidacaoException {
        command.start();
        return command;
    }

    public void execute(InterfaceCommand command, String nomeProcesso) throws DAOException, ValidacaoException {
        command.start();
    }

    public CommandQuery executeQuery(CommandQuery command) throws DAOException, ValidacaoException {
        command.start();
        return command;
    }

    public DataPagingResult executeQueryPager(DataPaging dataPaging, CommandQueryPager command) throws DAOException, ValidacaoException {
        command.setDataPaging(dataPaging);
        command.start();
        return command.getDataPagingResult();
    }

    public DataReport executeDataReport(Report report) throws ReportException {
        return new ReportExecutor(report).start().getDataReport();
    }

    @Override
    public FileReport executeFileReport(CommandFileReport command) throws ReportException {
        try {
            return ((CommandFileReport) command.start()).getFileReport();
        } catch (DAOException ex) {
            throw new ReportException(ex.getCause());
        } catch (ValidacaoException ex) {
            throw new ReportException(ex.getCause());
        }
    }
}
