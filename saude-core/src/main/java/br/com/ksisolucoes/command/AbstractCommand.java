/*
 * Created on 03/08/2004
 *
 */
package br.com.ksisolucoes.command;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.interfaces.FacadeDAO;
import br.com.ksisolucoes.system.factory.DAOFactory;
import br.com.ksisolucoes.system.factory.exception.FactoryException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.StringUtilKsi;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.postgresql.util.PSQLException;

/**
 * <AUTHOR> Giordani
 * 
 * Classe que implementa os patterns template e command e d suporte para os
 * objetos de negcio do sistema.
 * 
 * Quando for instanciado um objeto de negcio deve-se necessriamente chamar o
 * metodo start() passando ou no a transao corrente.
 *  
 */
public abstract class AbstractCommand<T> extends AbstractCommandExtends<T> implements InterfaceCommand {

    public static final int serialVersionUID = 1;

    protected transient FacadeDAO dao;

    public T start() throws DAOException, ValidacaoException {
        try {
            this.dao = DAOFactory.getDAO(getChave());
        } catch (FactoryException e) {
            Loggable.log.error(e.getMessage(), e);
            throw new DAOException(e);
        }

        this.dao.constructRootDAOServer();

        try {
            this.execute();
            this.clear();
        } catch (ValidacaoException exception) {
            throw exception;
        } catch (NullPointerException e) {
            Loggable.log.error(e.getMessage(), e);
            throw new DAOException(e.getMessage(), e);
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage(), e);
            throw e;
        } catch (HibernateException e) {
            Loggable.log.error(e.getMessage(), e);

//            String erroBanco = "Erro de Persistencia: ";
//            Throwable throwable = e.getCause();
//            if (throwable instanceof PSQLException) {
//                erroBanco += ((PSQLException) throwable).getMessage();
//            }

//            String err = e.getMessage() + "\n" + StringUtilKsi.montarMensagemErro(e);
//            err = erroBanco + "\n" + err;
            String msg = Bundle.getStringApplication("msg_falha_persistencia");
            throw new DAOException(msg, e);
        } catch (Exception e) {
            Loggable.log.error(e.getMessage(), e);
            throw new DAOException(e.getMessage(), e);
        }

        return (T) this;
    }

    /*
     * (non-Javadoc)
     * 
     * @see br.com.ksisolucoes.server.InterfaceCommand#execute()
     */
    public abstract void execute() throws DAOException, ValidacaoException;

    public Session getSession(){
        return this.dao.getSession();
    }

    /**
     * Chave para uso do factory.
     * @return chave da interface de DAO.
     */
    public String getChave(){
        return null;
    }

    protected void clear() {
    }
}
