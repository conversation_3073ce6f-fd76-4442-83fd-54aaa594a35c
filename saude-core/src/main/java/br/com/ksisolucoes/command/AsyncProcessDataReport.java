/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.command;

import net.sf.jasperreports.engine.JasperPrint;
import br.com.ksisolucoes.report.DataReport;
import java.io.File;

/**
 *
 * <AUTHOR>
 */
public class AsyncProcessDataReport implements DataReport, IAsyncProcess{

    private Object asyncObject;

    public AsyncProcessDataReport(Object asyncObject) {
        this.asyncObject = asyncObject;
    }

    public JasperPrint getJasperPrint() {
        throw new RuntimeException("Estado para o objeto invalido, processo assincrono");
    }

    @Override
    public Object getAsyncObject() {
        return this.asyncObject;
    }

    @Override
    public File getFilePrint() {
        throw new RuntimeException("Estado para o objeto invalido, processo assincrono");
    }

}
