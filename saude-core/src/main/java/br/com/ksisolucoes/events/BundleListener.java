/*
 * BundleListener.java
 *
 * Created on 21 de Agosto de 2004, 17:03
 */

package br.com.ksisolucoes.events;

import java.util.EventListener;

import br.com.ksisolucoes.util.log.Loggable;

/**
 * Uma classe abstrata de listener a qual recebe eventos do Bundle. Deve ser tomado o
 * cuidado de retirar-se este listener da classe informante assim que no for
 * mais necessrio a sua utilizao, apesar do mtodo <code>finalize</code>
 * ser implementado para este fim, porm este mtodo ser chamado a bom gosto do
 * Garbage Collector, podendo este listener ocupando recursos desnecessrios da
 * mquina.
 * 
 * @see BundleEvent
 * <AUTHOR>
 * @version 1.0 21/08/04
 */
public abstract class BundleListener implements EventListener, Loggable {

    /**
     * Invocado quando o Locale for atualizado.
     * 
     * <AUTHOR>
     * @param e <code>BundleEvent</code> quando da modificao do <code>Locale</code>
     */
    public abstract void localeChanged(BundleEvent e);

    /**
     * Utilizado pelo mtodo <code>equals</code>. Deve ser sobrecarregdo para
     * a identificao do <code>BundleListener</code>, quando da remoo por
     * exemplo.
     * 
     * @see java.lang.Object#hashCode()
     * @return <code>int</code> representando uma identificao nica do
     *         objeto.
     * <AUTHOR> Graciano
     */
    public abstract int hashCode();

    /**
     * Utilizado para comparaes entre objetos. Este se utiliza do mtodo
     * <code>hashCode</code> para ver se este <code>BundleListener</code> 
     * o mesmo informado por parmetro.
     * 
     * @see java.lang.Object#equals(java.lang.Object)
     * @return <code>true</code> se os mtodos <code>hashCode</code> dos
     *         referidos objetos retornarem o mesmo valor.
     * <AUTHOR> Graciano
     */
    public final boolean equals(Object obj) {
        return this.hashCode() == obj.hashCode();
    }

    /**
     * Este mtodo retira este <code>BundleListener</code> de sua(s) atual(is)
     * fontes de evento, como a classe <code>Bundle</code>. Isto  realizado
     * na tentativa de reduo do consumo de recursos dos listener, porm, 
     * aconselhado a retirada manual do evento quando este no for mais
     * necessrio.
     * 
     * @see java.lang.Object#finalize()
     * <AUTHOR> Graciano
     */
    protected void finalize() throws Throwable {
        //TODO Michel - Ver esse mtodo (Fabricio)
        //Bundle.removeBundleListener(this);
    }

}