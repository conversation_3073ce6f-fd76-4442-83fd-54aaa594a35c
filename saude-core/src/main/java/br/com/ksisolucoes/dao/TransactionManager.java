/*
 * Created on 09/08/2004
 *
 */
package br.com.ksisolucoes.dao;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.hibernate.HibernateException;
import org.hibernate.JDBCException;
import org.hibernate.ObjectDeletedException;
import org.hibernate.Session;
import org.hibernate.Transaction;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.AbstractSessaoAplicacao;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.bo.BusinessObjectConstants;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

/**
 * <AUTHOR>
 *  
 * Encapsula a session e a transaction corrente.
 */
public class TransactionManager implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Flag para setar a transaction manager para rollback.
     */
    private transient boolean rollback = false;
    
    /**
     * Session, Anliga a uma connection.
     */
    private Session session;

    /*
     * Sesso atual da aplicao.
     * Utilizado para fins de transporte.
     */
    private AbstractSessaoAplicacao sessaoAplicacao;
    
    /**
     * Transaction, representando um caso de uso. Uma transao  iniciada
     * apartir de uma session.
     *  
     */
    private Transaction transaction;

    private List<TransactionManagerListener> beforeCommitListeners;

    private List<TransactionManagerListener> afterCommitListeners;
    /**
     * Recebe uma session como parmetro. Esta  essencial para o
     *  inicio da transao.
     * 
     * @param session
     */
    public TransactionManager(Session session) throws DAOException {
        this.setSession(session);
    }
    
    /**
     * Retorna a session.
     * 
     * @return session
     */
    public Session getSession() {
        return session;
    }

    /**
     * Seta a session. Ao setar a session  criada uma nova transao e feito
     * rollback na transao corrente.
     * 
     * @param session
     */
    public void setSession(Session session) throws DAOException {
        this.session = session;

        /**
         * Cria uma nova transao.
         */
        if (this.transaction != null) {
            this.rollback();
            this.beginTransaction();
        } else {
            this.beginTransaction();
        }
    }

    /**
     * Retorna a transaction.
     * 
     * @return transaction Transao corrente.
     */
    public Transaction getTransaction() {
        return transaction;
    }

    /**
     * Inicia uma nova transaction beseada na session corrente.
     * 
     * @throws DAOException
     */
    public void beginTransaction() throws DAOException {
        try {
            
            this.transaction = this.session.beginTransaction();
        } catch (HibernateException e) {
            throw new DAOException(this.getMsgErrorBeginTransaction(), e);
        } 
    }

    /**
     * Desconecta a session 
     * 
     * @throws DAOException
     */
    public void disconectSession() throws DAOException {
        if( this.isSessionConected() ) {
            try {
                this.session.disconnect();
            } catch(HibernateException e) {
                throw new DAOException(this.getMsgErrorDisconectSession(), e);
            }
        }
    }

    /**
     * Desconecta a session 
     * 
     * @throws DAOException
     */
    public void closeSession() throws DAOException {
        if( this.isSessionOpen() ) {
            try {
                this.session.close();
            } catch(HibernateException e) {
                throw new DAOException(this.getMsgErrorCloseSession(), e);
            }
        }
    }
    
    /**
     * Informa se a session est conectada.
     * 
     * @return boolean
     */
    public boolean isSessionConected() {
        return this.session.isConnected();
    }

    /**
     * Informa de a session est aberta.
     * 
     * @return boolean
     */
    public boolean isSessionOpen() {
        return this.session.isOpen();
    }
    
    /**
     * Commita os processos executados na transao.
     * 
     * @throws DAOException
     */
    public void commit() throws DAOException {
        try {
            if( !this.rollback ) {
                if (this.beforeCommitListeners != null) {
                    for (TransactionManagerListener listenerBeforeCommit : this.beforeCommitListeners) {
                        try {
                            listenerBeforeCommit.execute(this);
                        } catch (ValidacaoException ex) {
                            throw new DAOException(ex);
                        }
                    }
                }

                this.transaction.commit();

                if (this.afterCommitListeners != null) {
                    for (TransactionManagerListener listenerAfterCommit : this.afterCommitListeners) {
                        try {
                            listenerAfterCommit.execute(this);
                        } catch (ValidacaoException ex) {
                            throw new DAOException(ex);
                        }
                    }
                }
            } else {
                this.rollback();
            }
        } catch( ObjectDeletedException e ) {
            throw new DAOException(e.getMessage(), e);
        } catch(JDBCException e) {
            //Pasear mensagens para melhor visualizao
            /*
             *Mensagem de ERRO para violao de foreig key
             *update or delete on "duplicata_receber" violates foreign key constraint "fk_hist_por_ope_ref_duplicat" on "historico_portador_operacao"
             */
            String msg = e.getSQLException().getNextException().getMessage();
            
            if ( msg.indexOf( "update or delete" ) > -1 && msg.indexOf( "violates foreign key constraint" ) > -1 ){
                String[] msgs = msg.split( " " );
                
                //O primeiro referece a onde ocorreu o erro, e o segundo, qual a tabela de relacionamento.
                String[] params = {msgs[5], msgs[12]};
                
                throw new DAOException(Bundle.getStringBO(BusinessObjectConstants.MENSAGEM_VIOLACAO_FOREIGN_KEY, params, this.sessaoAplicacao.getLocale()), e.getSQLException());
            }
            else{
                throw new DAOException(Bundle.getStringBO(BusinessObjectConstants.MENSAGEM_ERRO_EXECUCAO_TRANSACAO, this.sessaoAplicacao.getLocale()) + "\n" +
                    e.getSQLException().getNextException(), e.getSQLException());
            }
            
        } catch (HibernateException e) {
            throw new DAOException(this.getMsgErrorCommitt(), e);
        }
    }

    /**
     * Desfaz os processos executados na transao.
     * 
     * @throws DAOException
     */
    public void rollback() throws DAOException {
        Logger.getLogger( this.getClass() ).info("Rollback...");
        if( this.isSessionConected() ) {
            try {
                this.transaction.rollback();
            } catch (HibernateException e) {
                throw new DAOException(this.getMsrErrorRollBack(), e);
            }
        }
    }

    /**
     * Verifica se a transao j foi comitada.
     * 
     * @throws DAOException
     */
    public boolean wasCommitted() throws DAOException {
        try {
            return this.transaction.wasCommitted();
        } catch (HibernateException e) {
            throw new DAOException(this.getMsgErrorWasComitted(), e);
        }
    }

    /**
     * Verifica se a transao foi cancelada.
     * 
     * @return @throws
     *         DAOException
     */
    public boolean wasRolledBack() throws DAOException {
        try {
            return this.transaction.wasRolledBack();
        } catch (HibernateException e) {
            throw new DAOException(this.getMsgErrorWasRolledBack(), e);
        }
    }
    
    /**
     * @return Returns the sessaoAplicacao.
     */
    public AbstractSessaoAplicacao getSessaoAplicacao() {
        return sessaoAplicacao;
    }
    
    /**
     * @param sessaoAplicacao The sessaoAplicacao to set.
     */
    public void setSessaoAplicacao(AbstractSessaoAplicacao sessaoAplicacao) {
        this.sessaoAplicacao = sessaoAplicacao;
    }
    
    /*
     * TODO Menssagens de erro.
     * 
     * Estas foram disponibilizadas dessa forma para fins de testes e 
     *  devem ser internacionalizadas. 
     ****************************************************************/
    /**
     * 
     * @return Menssagem de erro ao fazer rollback.
     */
    private String getMsrErrorRollBack() {
        return "Erro ao fazer rolback";
    }
    
    /**
     * 
     * @return Menssagem de erro ao desconectar a session.
     */
    private String getMsgErrorDisconectSession() {
        return "Erro ao desconectar sesso";
    }
    
    /**
     * 
     * @return Menssagem de erro ao fechar a session.
     */
    private String getMsgErrorCloseSession() {
        return "Erro ao desconectar sesso";
    }
    
    /**
     * 
     * @return Menssagem de erro ao commitar.
     */
    private String getMsgErrorCommitt() {
        return "Erro executar transao";
    }
    
    /**
     * 
     * @return Menssagem de erro ao verificar se est ....
     */
    private String getMsgErrorWasComitted() {
        return "Erro ao ...";
    }

    /**
     * 
     * @return Menssagem de erro ao verificar se est ....
     */
    private String getMsgErrorWasRolledBack() {
        return "Erro ao ...";
    }
    
    /**
     * 
     * @return Menssagem de erro ao iniciar transao.
     */
    private String getMsgErrorBeginTransaction() {
        return "Erro ao iniciar a transao";
    }
    /*
     * ***************************************************************
     */
    
    /**
     * Retorna o status do da marcao de rollback.
     * 
     * @return Returns the rollback.
     */
    public boolean isRollback() {
        return rollback;
    }
    
    /**
     * Define o status do rollback.<br>
     * <li>true - TransactionManager Marcada para rollback.
     * <li>false - TransactionManager Marcada para commit.
     * <br>
     * @param rollback The rollback to set.
     */
    private void setRollback( boolean rollback ) {
        Logger.getLogger( this.getClass() ).info("Setando estado do Rollback para a transacao.: " + rollback);
        this.rollback = rollback;
    }
    
    /**
     * Marca uma TransactionManager para rollback.
     */
    public void setRollback() {
        Logger.getLogger( this.getClass() ).info("Setando estado true do Rollback para a transacao.");
        this.rollback = true;
    }

    public void addBeforeCommitListener(TransactionManagerListener beforeCommitListener){
        if (this.beforeCommitListeners == null) {
            this.beforeCommitListeners = new ArrayList<TransactionManagerListener>();
        }

        this.beforeCommitListeners.add(beforeCommitListener);
    }

    public List<TransactionManagerListener> getBeforeCommitListeners() {
        return beforeCommitListeners;
    }

    public void addAfterCommitListener(TransactionManagerListener afterCommitListener){
        if (this.afterCommitListeners == null) {
            this.afterCommitListeners = new ArrayList<TransactionManagerListener>();
        }

        this.afterCommitListeners.add(afterCommitListener);
    }

    public List<TransactionManagerListener> getAfterCommitListeners() {
        return afterCommitListeners;
    }

}
