/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.dao;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import br.com.ksisolucoes.bo.ConvertKeyToProperties;
import br.com.ksisolucoes.bo.RecoveryProperties;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.log.Loggable;

/**
 *
 * <AUTHOR>
 */
public class CacheEntityProperties implements Serializable {

    private CacheEntityProperties() {
    }
    
    private static CacheEntityProperties instance;
    
    private Map<Class, ConvertKeyToProperties> cacheKeyProperties;
    private Map<Class, RecoveryProperties> cacheProperties;

    public RecoveryProperties getRecoveryProperties(Class entity) {
        RecoveryProperties recoveryProperties = getCacheProperties().get(entity);
//        if (recoveryProperties != null) {
//            Loggable.log.debug("CacheEntityProperties: " + entity.getSimpleName());
//        }
        return recoveryProperties;
    }
    
    public void addRecoveryProperties(RecoveryProperties recoveryProperties){
        getCacheProperties().put(recoveryProperties.getEntity(), recoveryProperties);
    }
    
    private Map<Class, RecoveryProperties> getCacheProperties(){
        if (cacheProperties == null) {
            cacheProperties = new HashMap<Class, RecoveryProperties>();
        }
        
        return cacheProperties;
    }

    public ConvertKeyToProperties getConvertKeyToProperties(Class entity) {
        ConvertKeyToProperties convertKeyToProperties = getCacheKeyProperties().get(entity);
//        if (convertKeyToProperties != null) {
//            Loggable.log.debug("CacheEntityProperties: " + entity.getSimpleName());
//        }
        return convertKeyToProperties;
    }    
    
    public void addConvertKeyToProperties(ConvertKeyToProperties convertKeyToProperties){
        getCacheKeyProperties().put(convertKeyToProperties.getEntity(), convertKeyToProperties);
    }    
    
    private Map<Class, ConvertKeyToProperties> getCacheKeyProperties(){
        if (cacheKeyProperties == null) {
            cacheKeyProperties = new HashMap<Class, ConvertKeyToProperties>();
        }
        
        return cacheKeyProperties;
    }
    
    public static CacheEntityProperties getCurrentInstance(){
        return instance;
    }
    
    public static CacheEntityProperties getInstance(){
        if (instance == null) {
            try {
                instance = new LoadCacheEntityProperties().startCommandWithReturn().getCacheEntityProperties();
            } catch (SGKException ex) {
                Loggable.log.error(ex.getMessage());
            }
        }
        
        return instance;
    }
    
    public static CacheEntityProperties create(){
        return new CacheEntityProperties();
    }
}
