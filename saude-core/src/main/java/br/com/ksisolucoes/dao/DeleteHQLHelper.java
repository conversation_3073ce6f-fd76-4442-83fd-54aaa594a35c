package br.com.ksisolucoes.dao;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import org.hibernate.Query;

import br.com.ksisolucoes.vo.BaseRootVO;

public class DeleteHQLHelper {

    private List<String> values = new ArrayList();
    private StringBuffer where = new StringBuffer();
    private String typeDelete = null;
    private String alias = "";

    /*
     * CONTROLE
     * --------
     * Controles para primeira insero de , e AND ou OR
     *---------------------------------------------------------------------*/
    private boolean firstWhere = true;
    /*--------------------------------------------------------------------*/

    public DeleteHQLHelper addToWhereWhithAnd(String value) {
        if( !firstWhere ) {
            where.append(" AND ");
        }
        firstWhere = false;

        where.append(alias +"."+ value);

        return this;
    }

    public DeleteHQLHelper addToWhereWhithOr(String value) {
        if( !firstWhere ) {
            where.append(" OR ");
        }
        firstWhere = false;

        where.append(alias +"."+ value);

        return this;
    }

    public DeleteHQLHelper setAlias(String alias) {
        this.alias = alias;

        return this;
    }

    public DeleteHQLHelper setTypeDelete(String typeUpdate) {
        this.typeDelete = typeUpdate;

        return this;
    }

    public String getUpdate() {
        StringBuilder hql = new StringBuilder();

        /*
         * Update
         * ---------
         *---------------------------------------------------------------------*/
        hql.append("DELETE FROM ");
        hql.append(this.typeDelete + " " + alias + " ");

        for ( Iterator<String> it = values.iterator() ; it.hasNext() ; ){
            String property = it.next();
            hql.append( alias + "." + property + " =:" + property );
            //Verifica se ainda contem itens para adicionar a virgula.
            if ( it.hasNext() ){
                hql.append(",");
            }
        }

        /*
         * WHERE
         * -----
         *---------------------------------------------------------------------*/
        if (where != null && where.toString().trim().length() > 0) {
            hql.append(" WHERE ");
            hql.append(where.toString());
        }
        /*--------------------------------------------------------------------*/

        return hql.toString();
    }

    public static void setParameterValue(Query query, String key, Object value) {
        if (null == key || null == value) {
            return;
        } else if (value instanceof Boolean) {
            query.setBoolean(key, ((Boolean) value).booleanValue());
        } else if (value instanceof String) {
            query.setString(key, (String) value);
        } else if (value instanceof Integer) {
            query.setInteger(key, ((Integer) value).intValue());
        } else if (value instanceof Long) {
            query.setLong(key, ((Long) value).longValue());
        } else if (value instanceof Float) {
            query.setFloat(key, ((Float) value).floatValue());
        } else if (value instanceof Double) {
            query.setDouble(key, ((Double) value).doubleValue());
        } else if (value instanceof BigDecimal) {
            query.setBigDecimal(key, (BigDecimal) value);
        } else if (value instanceof Byte) {
            query.setByte(key, ((Byte) value).byteValue());
        } else if (value instanceof Calendar) {
            query.setCalendar(key, (Calendar) value);
        } else if (value instanceof Character) {
            query.setCharacter(key, ((Character) value).charValue());
        } else if (value instanceof Timestamp) {
            query.setTimestamp(key, (Timestamp) value);
        } else if (value instanceof Date) {
            query.setDate(key, (Date) value);
        } else if (value instanceof Short) {
            query.setShort(key, ((Short) value).shortValue());
        } else if (value instanceof Collection) {
            query.setParameterList(key, ((Collection) value));
        } else if (value instanceof BaseRootVO) {
            query.setEntity(key, value);
        }
    }

    public static void main(String[] args) {
        DeleteHQLHelper hql = new DeleteHQLHelper();
        hql.setTypeDelete("ItemFaturaProforma");
        hql.setAlias("i");

        hql.addToWhereWhithAnd("produto.subGrupo like 'a%'");
        hql.addToWhereWhithAnd("codigo = 1");

        System.out.println( hql.getUpdate() );
    }
}
