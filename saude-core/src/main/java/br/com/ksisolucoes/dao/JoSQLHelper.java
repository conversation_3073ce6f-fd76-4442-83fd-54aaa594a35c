package br.com.ksisolucoes.dao;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.josql.Query;
import org.josql.QueryResults;

import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.BaseRootVO;

public class JoSQLHelper<T> implements Serializable {
    
    private StringBuilder select = new StringBuilder();
    private StringBuilder from = new StringBuilder();
    private StringBuilder where = new StringBuilder();
    private StringBuilder group = new StringBuilder();
    private StringBuilder having = new StringBuilder();
    private StringBuilder order = new StringBuilder();
    private StringBuilder groupByOrder = new StringBuilder();
    private List<ExecuteOn> executeOnList = new ArrayList<ExecuteOn>();
    private String typeSelect = null;
    private String typeSelectGettersAndSetters = null;
    
    /*
     * CONTROLE
     * --------
     * Controles para primeira insero de , e AND ou OR
     *---------------------------------------------------------------------*/
    private boolean firstWhere = true;
    private boolean firstFrom = true;
    private boolean firstSelect = true;
    private boolean firstGroup = true;
    private boolean firstHaving = true;
    private boolean firstOrder = true;
    private boolean firstGroupByOrder = true;
    /*--------------------------------------------------------------------*/
    
    public static long CRESCENTE = 0;
    public static long DECRESCENTE = 1;

    private long orderType;
    
    public JoSQLHelper() {
        this.orderType = JoSQLHelper.CRESCENTE;
    }
    
    public JoSQLHelper addToSelectGetAndSet(String value) {
        return addToSelectGetAndSet(value, value);
    }
    public JoSQLHelper addToSelectGetAndSet(String value, String setter) {
        return addToSelect(value + "->" + setter);
    }
     
    public JoSQLHelper addToSelect(String value) {
        if( Coalesce.asString(value).trim().equals("") ) {
            return this;
        }
        
        if( !firstSelect ) {
            select.append(" , ");
        }
        firstSelect = false;
        
        select.append( value );
        
        return this;
    }
    
    public JoSQLHelper addToFrom(String value) {
        if( Coalesce.asString(value).trim().equals("") ) {
            return this;
        }
        
        if( !firstFrom ) {
            from.append(", ");
        }
        firstFrom = false;
        
        from.append( value );
        
        return this;
    }
    
    public JoSQLHelper addToFrom(String value, String alias) {
        if( Coalesce.asString(value).trim().equals("") ) {
            return this;
        }
        
        if( !firstFrom ) {
            from.append(", ");
        }
        firstFrom = false;
        
        from.append( value + " " + alias );
        
        return this;
    }
    
    public JoSQLHelper addToWhereWhithAnd(String value) {
        if( Coalesce.asString(value).trim().equals("") ) {
            return this;
        }
        
        if( !firstWhere ) {
            where.append(" and ");
        }
        firstWhere = false;
        
        where.append( value );
        
        return this;
    }
    
    public JoSQLHelper addToWhereWhithOr(String value) {
        if( Coalesce.asString(value).trim().equals("") ) {
            return this;
        }
        
        if( !firstWhere ) {
            where.append(" or ");
        }
        firstWhere = false;
        
        where.append( value );
        
        return this;
    }
    
    public JoSQLHelper addToGroupAndGroupByOrder(String value) {
        addToGroup(value);
        return addToGroupByOrder(value);
    }
        
    public JoSQLHelper addToGroup(String value) {
        if( Coalesce.asString(value).trim().equals("") ) {
            return this;
        }
        
        if( !firstGroup ) {
            group.append(", ");
        }
        firstGroup = false;
        
        group.append( value );
        
        return this;
    }
    
    public JoSQLHelper addToExecuteOn(String value) {
        if (Coalesce.asString(value).trim().equals("")) {
            return this;
        }
        
        executeOnList.add(new ExecuteOn(value));
        
        return this;
    }
    
    public JoSQLHelper addToOrder(String value) {
        if( Coalesce.asString(value).trim().equals("") ) {
            return this;
        }
        
        if( !firstOrder ) {
            order.append(", ");
        }
        firstOrder = false;
        
        order.append( value );
        
        return this;
    }
    
    public JoSQLHelper addToGroupByOrder(String value) {
        if( Coalesce.asString(value).trim().equals("") ) {
            return this;
        }
        
        if( !firstGroupByOrder ) {
            groupByOrder.append(", ");
        }
        firstGroupByOrder = false;
        
        groupByOrder.append( value );
        
        return this;
    }
    
    public JoSQLHelper addToHaving(String value) {
        if( Coalesce.asString(value).trim().equals("") ) {
            return this;
        }
        
        if( !firstHaving ) {
            having.append(", ");
        }
        firstHaving = false;
        
        having.append( value );
        
        return this;
    }
    
    public void setFrom(String from) {
        this.from = new StringBuilder(from);
    }
    
    public void setGroup(String group) {
        this.group = new StringBuilder(group);
    }
    
    public void setHaving(String having) {
        this.having = new StringBuilder(having);
    }
    
    public void setOrder(String order) {
        this.order = new StringBuilder(order);
    }
    
    public void setGroupByOrder(String groupByOrder) {
        this.groupByOrder = new StringBuilder(groupByOrder);
    }
    
    public void setSelect(String select) {
        this.select = new StringBuilder(select);
    }
    
    public void setWhere(String where) {
        this.where = new StringBuilder(where);
    }
    
    public StringBuilder getWhere() {
        return this.where;
    }
    
    public StringBuilder getFrom() {
        return this.from;
    }
    
    public StringBuilder getGroup() {
        return this.group;
    }
    
    public StringBuilder getHaving() {
        return this.having;
    }
    
    public StringBuilder getOrder() {
        return this.order;
    }
    
    public StringBuilder getGroupByOrder() {
        return this.groupByOrder;
    }
    
    public StringBuilder getSelect() {
        return this.select;
    }
    
    public long getOrderType() {
        return this.orderType;
    }
    
    public JoSQLHelper setTypeSelect(String typeSelect) {
        this.typeSelect = typeSelect;
        
        return this;
    }
    
    public String getQuery() {
        StringBuilder hql = new StringBuilder();
        
        /*
         * SELECT
         * ---------
         *---------------------------------------------------------------------*/
        if (select != null && select.toString().trim().length() > 0) {
            if ( this.select != null  ){
                hql.append("select ");
                if( this.typeSelect != null ) {
                    hql.append(" new " + this.typeSelect + "( ");
                } else if (typeSelectGettersAndSetters != null) {
                    hql.append(" new " + this.typeSelectGettersAndSetters + "() { ");
                }
            }
            
            hql.append(select.toString());
        }else {
            hql.append("select * ");
        }
        
        if (select != null && select.toString().trim().length() > 0) {
            if( this.typeSelect != null ) {
                hql.append(" ) ");
            }
            if( this.typeSelectGettersAndSetters != null ) {
                hql.append(" } ");
            }
        }
        /*--------------------------------------------------------------------*/
        
        /*
         * FROM
         * ----
         *---------------------------------------------------------------------*/
        hql.append(" from ");
        hql.append(from.toString());
        /*--------------------------------------------------------------------*/
        
        /*
         * WHERE
         * -----
         *---------------------------------------------------------------------*/
        if (where != null && where.toString().trim().length() > 0) {
            hql.append(" where ");
            hql.append(where.toString());
        }
        /*--------------------------------------------------------------------*/
        
        /*
         * GROUP
         * -----
         *---------------------------------------------------------------------*/
        if (group != null && group.toString().trim().length() > 0) {
            hql.append(" group by ");
            hql.append(group.toString());
        }
        /*--------------------------------------------------------------------*/
        
        /*
         * HAVING
         * ------
         *---------------------------------------------------------------------*/
        if (having != null && having.toString().trim().length() > 0) {
            hql.append(" having ");
            hql.append(having.toString());
        }
        /*--------------------------------------------------------------------*/
        
        /*
         * ORDER
         * -----
         *---------------------------------------------------------------------*/
        if (order != null && order.toString().trim().length() > 0) {
            hql.append(" order by ");
            hql.append(order.toString());
            if(this.orderType == JoSQLHelper.CRESCENTE) {
                hql.append(" asc ");
            } else {
                hql.append(" desc ");
            }
        }
        /*--------------------------------------------------------------------*/
        
        /*
         * GROUP BY ORDER
         * -----
         *---------------------------------------------------------------------*/
        if (groupByOrder != null && groupByOrder.toString().trim().length() > 0) {
            hql.append(" group by order ");
            hql.append(groupByOrder.toString());
            if(this.orderType == JoSQLHelper.CRESCENTE) {
                hql.append(" asc ");
            } else {
                hql.append(" desc ");
            }
        }
        /*--------------------------------------------------------------------*/
        
        /*
         * EXECUTE ON
         * -----
         *---------------------------------------------------------------------*/
        if (!executeOnList.isEmpty()) {
            for (ExecuteOn executeOn : executeOnList) {
                hql.append(executeOn.toJoSQL());
            }
        }
        /*--------------------------------------------------------------------*/
        
        return hql.toString();
    }
    
    public static void setParameterValue(Query query, String key, Object value) {
        if (null == key) {
            return;
        } else if (value instanceof Boolean) {
            query.setVariable(key, ((Boolean) value).booleanValue());
        } else if (value instanceof String) {
            query.setVariable(key, (String) value);
        } else if (value instanceof Integer) {
            query.setVariable(key, ((Integer) value).intValue());
        } else if (value instanceof Long) {
            query.setVariable(key, ((Long) value).longValue());
        } else if (value instanceof Float) {
            query.setVariable(key, ((Float) value).floatValue());
        } else if (value instanceof Double) {
            query.setVariable(key, ((Double) value).doubleValue());
        } else if (value instanceof BigDecimal) {
            query.setVariable(key, (BigDecimal) value);
        } else if (value instanceof Byte) {
            query.setVariable(key, ((Byte) value).byteValue());
        } else if (value instanceof Calendar) {
            query.setVariable(key, (Calendar) value);
        } else if (value instanceof Character) {
            query.setVariable(key, ((Character) value).charValue());
        } else if (value instanceof Timestamp) {
            query.setVariable(key, (Timestamp) value);
        } else if (value instanceof Date) {
            Date date = ( Date ) value;
            if( Data.isTimestamp( date ) ) {
                query.setVariable( key, (Date) value);
            } else {
                query.setVariable(key, (Date) value);
            }
        } else if (value instanceof Short) {
            query.setVariable(key, ((Short) value).shortValue());
        } else if (value instanceof Collection) {
            query.setVariable(key, ((Collection) value));
        } else if (value instanceof BaseRootVO) {
            query.setVariable(key, value);
        }
    }
    
    public static void main(String[] args) {
        JoSQLHelper hql = new JoSQLHelper();
        hql.setTypeSelect("List");
        
        /*
        hql.addToSelect("d.codigo");
        hql.addToSelect("d.descricao");
        hql.addToSelect("d.valor");
        */
        hql.addToFrom("Duplicata d");
        hql.addToFrom("Cliente c");
        
        hql.addToWhereWhithAnd("descricao like 'a%'");
        hql.addToWhereWhithAnd("codigo = 1");
        hql.addToWhereWhithOr("codigo = 3");
        hql.addToWhereWhithOr("dataVencimento = 01/02/1802");
        
        hql.addToGroup("codigo");
        hql.addToGroup("descricao");
        
        hql.addToHaving("d.codigo");
        hql.addToHaving("d.descricao");
        
        hql.addToOrder("d.codigo");
        hql.addToOrder("d.descricao");
        hql.addToOrder("d.dataVencimento");
        
        System.out.println( hql.getQuery() );
    }
    
    public void setOrderType(long orderType) {
        if(orderType != this.CRESCENTE && orderType != this.DECRESCENTE) {
            throw new IllegalArgumentException("Tipo de ordenacao invalida.");
        }
        this.orderType = orderType;
    }
    
    /**
     * Percorre o resultado do group by retornando uma lista com seus objetos
     * @param queryResults 
     * @return 
     */
    public List<T> getGroupByResults(QueryResults queryResults) {
        if ( queryResults != null ) {
            Map groupByResults = queryResults.getGroupByResults();
            Iterator iterator = groupByResults.values().iterator();

            List objectList = new ArrayList();
            while( iterator.hasNext() ) {
                T object = (T) ( (List) ( (List) iterator.next() ).get( 0 ) ).get( 0 );
                objectList.add( object );
            }
            
            return objectList;
        } else {
            return null;
        }
        
    }

    public void setTypeSelectGettersAndSetters(String typeSelectGettersAndSetters) {
        this.typeSelectGettersAndSetters = typeSelectGettersAndSetters;
    }
    
    public void merge(JoSQLHelper<T> joSQL) {
        if (joSQL.select.length() > 0) {
            addToSelect(joSQL.select.toString());
        }
        
        if (joSQL.where.length() > 0) {
            addToWhereWhithAnd(joSQL.where.toString());
        }
    }
}

class ExecuteOn implements Serializable {
    public static long EXECUTE_ON_RESULTS = 1;
    public static long EXECUTE_ON_ALL = 2;
    public static long EXECUTE_ON_GROUP_BY_RESULTS = 3;

    private Long type;
    private String expression;
    
    public ExecuteOn(String expression) {
        this(expression, EXECUTE_ON_RESULTS);
    }
    
    public ExecuteOn(String expression, Long type) {
        this.expression = expression;
        this.type = type;
    }
    
    public Long getType() {
        return type;
    }
    public void setType(Long type) {
        this.type = type;
    }
    public String getExpression() {
        return expression;
    }
    public void setExpression(String expression) {
        this.expression = expression;
    }
    public String toJoSQL() {
        String execute = null;
        if (EXECUTE_ON_RESULTS == type) {
            execute = " execute on results ";
        } else if (EXECUTE_ON_ALL == type) {
            execute = " execute on all ";
        } else if (EXECUTE_ON_GROUP_BY_RESULTS == type) {
            execute = " execute on group_by_results ";
        }
        return execute + expression; 
    }
}