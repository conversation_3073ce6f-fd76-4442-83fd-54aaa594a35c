package br.com.ksisolucoes.dao.paginacao.hibernate;

import java.util.ArrayList;
import java.util.List;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.dao.paginacao.Page;
import br.com.ksisolucoes.dao.paginacao.PageImpl;
import br.com.ksisolucoes.dao.paginacao.Pager;
import br.com.ksisolucoes.dao.paginacao.PagingException;


public class QueryPager implements Pager {

   private final CommandQueryPager query;

   public QueryPager(final CommandQueryPager query) {
      this.query = query;
   }

   public Page getPage(final int pageNumber) throws PagingException {
       return this.getPage(pageNumber, Pager.LENGTH_PAGE_DEFAULT);
   }
   
   public Page getPage(final int pageNumber, final int resultsPerPage) throws PagingException {
      return getPage(pageNumber, resultsPerPage, false);
   }

   private Page getPage(final int pageNumber, final int resultsPerPage,
         boolean last) throws PagingException {
      try {
         query.setFirstResult(pageNumber * resultsPerPage);
         query.setMaxResults(resultsPerPage);
         List results = query.list();
         if (pageNumber > 0 && results.isEmpty()) {
            return getPage(pageNumber - 1, resultsPerPage, true);
         }
         results = results == null?new ArrayList():results;//usar assim pois da um nullpointer quando processado em concorrencia varias vezes seguidas
         return new PageImpl(results, pageNumber, resultsPerPage, last || 
               results.size() < resultsPerPage);
      } catch (Exception e) {
         throw new PagingException(e);
      }
   }

   public IteratorPager iterator() {
       return new IteratorPager(this);
   }
   
   public IteratorPager iterator(int resultsPerPage) {
       return new IteratorPager(this, resultsPerPage);
   }
   
   public int getAmountResults() throws PagingException {
	   try {
		   return this.query.getAmountResults();
	   } catch (Exception e) {
		   throw new PagingException(e);
	   }
   }
}