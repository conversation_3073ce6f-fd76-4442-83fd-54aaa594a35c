/*
 * Created on 17/03/2005
 *
 */
package br.com.ksisolucoes.server;

import javax.naming.InitialContext;
import javax.naming.NamingException;

import org.apache.log4j.Logger;

/**
 * <AUTHOR>
 * 
 * Localizados para o servico de contexto.
 */
public class InitialContextLocator {

	private static InitialContextLocator instance;

	private static final Logger log = Logger.getLogger("server");
	
	private InitialContextLocator() {}

	public static InitialContextLocator getInstance() {
		if (instance == null) {
			instance = new InitialContextLocator();
		}
		return instance;
	}

	public InitialContext getContext() throws NamingException {

		// if( serverUrl == null ) {
		// serverUrl = System.getProperty("server.url", "127.0.0.1:6099");
		// serverUrl = SystemProperties.getInstance().getPropertieServerUrl();
		// log.debug("Recuperando a URL passa por parametro: " + serverUrl);
		// }

//		log.debug("InitialContext.PROVIDER_URL: "
//				+ System.getProperty("java.naming.provider.url"));

		// Hashtable props = new Hashtable();

		// props.put( InitialContext.INITIAL_CONTEXT_FACTORY,
		// "org.jnp.interfaces.NamingContextFactory" );
		// props.put( InitialContext.PROVIDER_URL, "jnp://" + serverUrl );
		// log.debug("props.put( InitialContext.PROVIDER_URL, \"jnp://\" +
		// serverUrl ): jnp://" + serverUrl);

		// This establishes the security for authorization/authentication
		// props.put(InitialContext.SECURITY_PRINCIPAL,"username");
		// props.put(InitialContext.SECURITY_CREDENTIALS,"password");

		// log.debug("this.initialContext = new InitialContext(props):
		// Instanciando initialContext.");
		// this.initialContext = new InitialContext(props);
		InitialContext initialContext = new InitialContext();

		return initialContext;
	}

}
