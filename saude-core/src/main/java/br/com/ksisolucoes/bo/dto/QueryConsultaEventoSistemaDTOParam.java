/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.dto;

import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.controle.Usuario;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class QueryConsultaEventoSistemaDTOParam implements Serializable {
    private DatePeriod periodo;
    private String fonteEvento;
    private String descricao;
    private Long nivelCriticidade;
    private Long tipoEvento;
    private String paciente;
    private String usuario;
    private String profissional;
    private String empresa;

    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    public String getFonteEvento() {
        return fonteEvento;
    }

    public void setFonteEvento(String fonteEvento) {
        this.fonteEvento = fonteEvento;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Long getNivelCriticidade() {
        return nivelCriticidade;
    }

    public void setNivelCriticidade(Long nivelCriticidade) {
        this.nivelCriticidade = nivelCriticidade;
    }

    public Long getTipoEvento() {
        return tipoEvento;
    }

    public void setTipoEvento(Long tipoEvento) {
        this.tipoEvento = tipoEvento;
    }

    public String getPaciente() {
        return paciente;
    }

    public void setPaciente(String paciente) {
        this.paciente = paciente;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public String getProfissional() {
        return profissional;
    }

    public void setProfissional(String profissional) {
        this.profissional = profissional;
    }

    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }
}