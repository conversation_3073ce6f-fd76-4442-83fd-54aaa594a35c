/*
 * Created on 13/08/2004
 *
 */
package br.com.ksisolucoes.bo.command;

import java.io.Serializable;
import java.util.List;

import br.com.ksisolucoes.command.AbstractCommand;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.DAOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

/**
 * <AUTHOR>
 *
 */
public abstract class FindAll extends AbstractCommand implements Serializable {

    private static final long serialVersionUID = 1L;
    
    //Parmetros
    /**
     * Propriedade para ordenao da lista a ser retornada.
     */
    private String property;
    
    //Retorno
    /**
     * Lista de Atividades a ser retornada
     */
    private List list;

    /**
     * Construtor para recebimento de uma propriedade, a qual, ser
     *  utilizada para a ordenao da list a ser retornada.
     * 
     * @param property Atributo para ordenar a list.
     */
    public FindAll(String property) {
        this.property = property;
    }
    
    /**
     * Construtor default, onde,  retornada apenas uma list sem controle 
     *  de ordenao.
     *
     */
    public FindAll() {
        this.property = null;
    }
    
    /* (non-Javadoc)
     * @see br.com.ksisolucoes.command.AbstractCommand#execute()
     */
    public void execute() throws DAOException, ValidacaoException {
        this.antesFindAll();
        if(this.property == null) {
            this.processingFind();
        } else if (this.property != null) {
            this.processingFindOrderByProperty();
        }
        this.depoisFindAll();
    }

    /**
     * 
     */
    protected void depoisFindAll() throws DAOException, ValidacaoException {
    }

    /**
     * 
     */
    protected void antesFindAll() throws DAOException, ValidacaoException {
    }

    /**
     * Processa query.
     * 
     * @throws DAOException
     */
    private void processingFind() throws DAOException, ValidacaoException {
        this.list = LoadManager.getInstance(DAOFactory.getDAO(getChave()).getReferenceClass()).setLazyMode(true).start().getList();
    }

    /**
     * Processa query aplicando order by.
     * 
     * @throws DAOException
     */
    private void processingFindOrderByProperty() throws DAOException, ValidacaoException {
        this.list = LoadManager.getInstance(DAOFactory.getDAO(getChave()).getReferenceClass()).setLazyMode(true).start().getList();
    }
    
    /**
     * Retorna a Listagem de Atividades.
     * 
     * @return atividades
     */
    public List getList() {
        return this.list;
    }

    /* (non-Javadoc)
     * @see br.com.ksisolucoes.command.AbstractCommand#getInterfaceChave()
     */
    public abstract String getChave();

    /**
     * @param list The list to set.
     */
    protected void setList(List list) {
        this.list = list;
    }
}
