package br.com.ksisolucoes.bo.command;

import java.io.Serializable;
import java.util.Set;

import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

public interface StrategyRecovery extends Serializable {

    boolean validate(Object object, String property) throws ValidacaoException;
    
    public Set<String> getExecutedProperties();

    Object getPropertyValue(Object instance, Object currentValue, String property);
        
}
