package br.com.ksisolucoes.bo.server.interfaces.facade;

import java.io.Serializable;

import org.hibernate.Session;

import br.com.celk.provider.ejb.EJBLocation;

/**
 *
 * <AUTHOR>
 */
@EJBLocation("br.com.ksisolucoes.bo.server.HibernateSessionFactoryBO")
public interface HibernateSessionFactoryFacade extends Serializable{

    public Session getSession();

    public Session getSessionLeitura();

}
