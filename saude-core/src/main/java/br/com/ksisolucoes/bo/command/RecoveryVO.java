/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.command;

import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.HQLConvertKeyToProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Reflection;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoRuntimeException;
import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

/**
 *
 * <AUTHOR>
 */
public class RecoveryVO {

    private Object instance;
    private Object currentValue;
    private String property;
    private CommomFacade bo;
    private StrategyRecovery strategyRecovery;

    public RecoveryVO(Object instance, Object currentValue, String property, StrategyRecovery strategyRecovery) {
        this.instance = instance;
        this.currentValue = currentValue;
        this.property = property;
        this.strategyRecovery = strategyRecovery;
    }

    public <T> T getPropertyValue() {
        try {
            if (strategyRecovery != null && strategyRecovery.validate(instance, property)) {

                Method method = Reflection.getMethodByName(instance.getClass(), Reflection.getGetMethodName(property));
                Object newInstance = null;
                ReloadVOData reloadVO = null;

                Loggable.log.debug("RecoveryVO: " + property);
                if (!CodigoManager.class.isAssignableFrom(method.getReturnType())) {
                    reloadVO = getBO().recoveryVO(instance, strategyRecovery, false, property);
                } else {
                    reloadVO = getBO().recoveryVO(instance, strategyRecovery, true, new HQLConvertKeyToProperties(method.getReturnType(), property, (Serializable)null));
                }

                newInstance = reloadVO.getNewInstance();

                if (newInstance != null) {
                    String[] propertiesEvaluated = reloadVO.getPropertiesMap().keySet().toArray(new String[reloadVO.getPropertiesMap().keySet().size()]);
                    ReloadVO.sincronizeInstance(instance, newInstance, propertiesEvaluated);

                    Object value = (T) Reflection.getValueByPattern(newInstance, property);
                    if (value != null && value instanceof BaseRootVO) {
                        StrategyRecovery strategy = ((BaseRootVO) instance).getDefaultStrategyRecovery();
                        setStrategyRecoveryToNextEscope(strategy, instance, propertiesEvaluated);
                        value = (T) Reflection.getPropertyValueByPattern(instance, property);
                    }
                    Loggable.log.debug(instance.getClass().getSimpleName() + " -- " + property);
                    return (T) value;
                }
            } else if ( strategyRecovery != null ) {
                if (currentValue != null && currentValue instanceof BaseRootVO ) {
                    if (((BaseRootVO) currentValue).getDefaultStrategyRecovery() == null) {
                        String[] propertiesByPattern = ReloadVO.extractPropertiesByPattern(strategyRecovery.getExecutedProperties(), property);

                        //Seta a estratgia para os objetos dentro da instancia
                        setStrategyRecoveryToNextEscope(ReloadVO.onlyIfNull(propertiesByPattern), currentValue, propertiesByPattern);

                        //Seta a estratgia para o objetos
                        ((BaseRootVO) currentValue).setDefaultStrategyRecovery(ReloadVO.onlyIfNull(propertiesByPattern));
                    }
                }
            }
        } catch (DAOException ex) {
            throw new ValidacaoRuntimeException(ex.getMessage(), ex);
        } catch (ValidacaoException ex) {
            throw new ValidacaoRuntimeException(ex.getMessage(), ex);
        } catch (NoSuchMethodException ex) {
            throw new ValidacaoRuntimeException(ex.getMessage(), ex);
        } catch (NoSuchFieldException ex) {
            throw new ValidacaoRuntimeException(ex.getMessage(), ex);
        } catch (IllegalAccessException ex) {
            throw new ValidacaoRuntimeException(ex.getMessage(), ex);
        } catch (InvocationTargetException ex) {
            throw new ValidacaoRuntimeException(ex.getMessage(), ex);
        }

        return (T) currentValue;
    }

    private void setStrategyRecoveryToNextEscope(StrategyRecovery strategyRecovery, Object instance, String... properties) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException, NoSuchFieldException {
        exit:
        for (String prop : properties) {
            String[] path = prop.split("\\.");
            String fragmentPath = "";
            for (String currentPath : path) {
                if (!fragmentPath.trim().equals("")) {
                    fragmentPath = VOUtils.montarPath(fragmentPath, currentPath);
                } else {
                    fragmentPath = currentPath;
                }

                Object value = Reflection.getPropertyValueByPattern(instance, fragmentPath);
                if (value == null) {
                    continue exit;
                }

                if (CodigoManager.class.isAssignableFrom(value.getClass()) && value instanceof BaseRootVO) {
                    if (((BaseRootVO) value).getDefaultStrategyRecovery() == null) {
                        ((BaseRootVO) value).setDefaultStrategyRecovery(ReloadVO.onlyIfNull(ReloadVO.extractPropertiesByPattern(strategyRecovery.getExecutedProperties(), fragmentPath)));
                    }
                }
            }
        }
    }

    private CommomFacade getBO() {
        if (this.bo == null) {
           bo = BOFactory.getBO(CommomFacade.class);
        }

        return this.bo;
    }
}
