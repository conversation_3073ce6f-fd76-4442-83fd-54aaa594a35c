/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.command;

import java.io.Serializable;
import java.util.List;

import br.com.ksisolucoes.util.CollectionUtils;

/**
 *
 * <AUTHOR>
 */
public class QueryLoadResults implements Serializable{

    private List result;

    public QueryLoadResults(List result) {
        this.result = result;
    }

    public <T> List<T> getResult() {
        return result;
    }

    public <T> T getVo() {
        if(CollectionUtils.isNotNullEmpty(result)){
            if(result.size() > 1){
                throw new RuntimeException("A consulta retornou mais de um registro onde esperava-se somente um. (Verifique se o ID do objeto está completo)");
            }
            return (T) result.get(0);
        }
        return null;
    }

}
