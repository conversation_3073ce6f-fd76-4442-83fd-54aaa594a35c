/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.server.interfaces.facade;

import br.com.celk.provider.ejb.EJBLocation;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.command.InterfaceCommand;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;

/**
 *
 * <AUTHOR>
 */
@EJBLocation("br.com.ksisolucoes.bo.server.CommandServerBO")
public interface CommandServerFacade {

    public void executeCommand(InterfaceCommand command) throws br.com.ksisolucoes.dao.exception.DAOException, br.com.ksisolucoes.util.validacao.exception.ValidacaoException, java.rmi.RemoteException;

    public InterfaceCommand executeCommandReturn(InterfaceCommand command) throws br.com.ksisolucoes.dao.exception.DAOException, br.com.ksisolucoes.util.validacao.exception.ValidacaoException, java.rmi.RemoteException;

    public CommandQuery executeQuery(CommandQuery command) throws br.com.ksisolucoes.dao.exception.DAOException, br.com.ksisolucoes.util.validacao.exception.ValidacaoException, java.rmi.RemoteException;

    public DataPagingResult executeQueryPager(DataPaging dataPaging, CommandQueryPager command) throws br.com.ksisolucoes.dao.exception.DAOException, br.com.ksisolucoes.util.validacao.exception.ValidacaoException, java.rmi.RemoteException;
}
