package br.com.ksisolucoes.bo.command;

import java.util.HashSet;
import java.util.Set;

import br.com.ksisolucoes.dao.HQLConvertToLeftJoin;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;

public class ProcessPropertiesQuery {
    
    private AbstractPropertiesQuery propertiesQuery;
    private HQLHelper hql;
    private String[] propertyList;
    
    private Set<String> propertySet = new HashSet<String>();
    
    public ProcessPropertiesQuery(AbstractPropertiesQuery propertiesQuery, HQLHelper hql) {
        this(propertiesQuery, hql, null);
    }
    
    public ProcessPropertiesQuery(AbstractPropertiesQuery propertiesQuery, HQLHelper hql, String[] propertyList) {
        this.propertiesQuery = propertiesQuery;
        this.hql = hql;
        this.propertyList = propertyList;
    }
    
    public void execute() {
        for (String property : propertyList) {
            propertySet.add(property);
        }
        
        process(propertiesQuery);
        
        for (String property : propertySet) {
            hql.addToSelect(property, true);
        }
        
        new HQLConvertToLeftJoin().resolve(hql);
        
        Loggable.log.debug("Query: " + hql.getQuery());
    }
    
    private void process(AbstractPropertiesQuery propertiesQueryInterface) {
        for (String string : propertiesQueryInterface.getProperties()) {
            String path = VOUtils.montarPath(propertiesQueryInterface.getPath(), string);
            propertySet.add(path);
        }
        
        if (propertiesQueryInterface.getPropertiesQueryList() != null) {
            for (AbstractPropertiesQuery propertiesQuery: propertiesQueryInterface.getPropertiesQueryList()) {
                process(propertiesQuery);
            }
        }
    }
}
