<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.agendamento"  >
    <class name="ConfiguracaoOrdenacaoAgendamentoListaEspera" table="conf_ord_age_lista_espera" >

        <id
                column="cd_conf_ord_age_lista_espera"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> <version column="version" name="version" type="long" />

        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Empresa"
                name="empresa"
                column="empresa"
        />

        <property
                name="codigoColuna"
                column="cod_coluna"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                name="ordem"
                column="ordem"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                name="tipo"
                column="tipo"
                type="java.lang.Long"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                name="usuarioCadastro"
        >
            <column name="cd_usuario_cadastro" not-null="true"/>
        </many-to-one>

        <property
                name="dataCadastro"
                column="dt_cadastro"
                type="timestamp"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                name="usuarioAlteracao"
        >
            <column name="cd_usuario_alteracao" not-null="true"/>
        </many-to-one>

        <property
                name="dataAlteracao"
                column="dt_alteracao"
                type="timestamp"
                not-null="true"
        />

    </class>
</hibernate-mapping>
