package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseRequisicaoPadraoExame;



public class RequisicaoPadraoExame extends BaseRequisicaoPadraoExame implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequisicaoPadraoExame () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequisicaoPadraoExame (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequisicaoPadraoExame (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.RequisicaoPadrao requisicaoPadrao,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimento,
		java.util.Date dataCadastro,
		java.util.Date dataUsuario,
		java.lang.Long quantidade) {

		super (
			codigo,
			requisicaoPadrao,
			usuario,
			exameProcedimento,
			dataCadastro,
			dataUsuario,
			quantidade);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}