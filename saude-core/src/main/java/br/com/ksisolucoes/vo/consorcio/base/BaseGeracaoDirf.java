package br.com.ksisolucoes.vo.consorcio.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the dirf_processo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="dirf_processo"
 */

public abstract class BaseGeracaoDirf extends BaseRootVO implements Serializable {

	public static String REF = "GeracaoDirf";
	public static final String PROP_STATUS = "status";
	public static final String PROP_PATH = "path";
	public static final String PROP_MENSAGEM_ERRO = "mensagemErro";
	public static final String PROP_ANO = "ano";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_ASYNC_PROCESS = "asyncProcess";
	public static final String PROP_GERENCIADOR_ARQUIVO = "gerenciadorArquivo";


	// constructors
	public BaseGeracaoDirf () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseGeracaoDirf (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseGeracaoDirf (
		java.lang.Long codigo,
		java.util.Date dataCadastro,
		java.lang.Long status) {

		this.setCodigo(codigo);
		this.setDataCadastro(dataCadastro);
		this.setStatus(status);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataCadastro;
	private java.lang.Long status;
	private java.lang.Long ano;
	private java.lang.String mensagemErro;
	private java.lang.String path;

	// many to one
	private br.com.ksisolucoes.vo.service.AsyncProcess asyncProcess;
	private br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo gerenciadorArquivo;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_dirf_processo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: ano
	 */
	public java.lang.Long getAno () {
		return getPropertyValue(this, ano, PROP_ANO); 
	}

	/**
	 * Set the value related to the column: ano
	 * @param ano the ano value
	 */
	public void setAno (java.lang.Long ano) {
//        java.lang.Long anoOld = this.ano;
		this.ano = ano;
//        this.getPropertyChangeSupport().firePropertyChange ("ano", anoOld, ano);
	}



	/**
	 * Return the value associated with the column: msg_erro
	 */
	public java.lang.String getMensagemErro () {
		return getPropertyValue(this, mensagemErro, PROP_MENSAGEM_ERRO); 
	}

	/**
	 * Set the value related to the column: msg_erro
	 * @param mensagemErro the msg_erro value
	 */
	public void setMensagemErro (java.lang.String mensagemErro) {
//        java.lang.String mensagemErroOld = this.mensagemErro;
		this.mensagemErro = mensagemErro;
//        this.getPropertyChangeSupport().firePropertyChange ("mensagemErro", mensagemErroOld, mensagemErro);
	}



	/**
	 * Return the value associated with the column: path
	 */
	public java.lang.String getPath () {
		return getPropertyValue(this, path, PROP_PATH); 
	}

	/**
	 * Set the value related to the column: path
	 * @param path the path value
	 */
	public void setPath (java.lang.String path) {
//        java.lang.String pathOld = this.path;
		this.path = path;
//        this.getPropertyChangeSupport().firePropertyChange ("path", pathOld, path);
	}



	/**
	 * Return the value associated with the column: cd_process
	 */
	public br.com.ksisolucoes.vo.service.AsyncProcess getAsyncProcess () {
		return getPropertyValue(this, asyncProcess, PROP_ASYNC_PROCESS); 
	}

	/**
	 * Set the value related to the column: cd_process
	 * @param asyncProcess the cd_process value
	 */
	public void setAsyncProcess (br.com.ksisolucoes.vo.service.AsyncProcess asyncProcess) {
//        br.com.ksisolucoes.vo.service.AsyncProcess asyncProcessOld = this.asyncProcess;
		this.asyncProcess = asyncProcess;
//        this.getPropertyChangeSupport().firePropertyChange ("asyncProcess", asyncProcessOld, asyncProcess);
	}



	/**
	 * Return the value associated with the column: cd_gerenciador_arquivo
	 */
	public br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo getGerenciadorArquivo () {
		return getPropertyValue(this, gerenciadorArquivo, PROP_GERENCIADOR_ARQUIVO); 
	}

	/**
	 * Set the value related to the column: cd_gerenciador_arquivo
	 * @param gerenciadorArquivo the cd_gerenciador_arquivo value
	 */
	public void setGerenciadorArquivo (br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo gerenciadorArquivo) {
//        br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo gerenciadorArquivoOld = this.gerenciadorArquivo;
		this.gerenciadorArquivo = gerenciadorArquivo;
//        this.getPropertyChangeSupport().firePropertyChange ("gerenciadorArquivo", gerenciadorArquivoOld, gerenciadorArquivo);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.consorcio.GeracaoDirf)) return false;
		else {
			br.com.ksisolucoes.vo.consorcio.GeracaoDirf geracaoDirf = (br.com.ksisolucoes.vo.consorcio.GeracaoDirf) obj;
			if (null == this.getCodigo() || null == geracaoDirf.getCodigo()) return false;
			else return (this.getCodigo().equals(geracaoDirf.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}