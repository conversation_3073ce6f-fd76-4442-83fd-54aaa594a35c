package br.com.ksisolucoes.vo.vigilancia.autointimacao.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the auto_intimacao_prorogacao table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="auto_intimacao_prorogacao"
 */

public abstract class BaseAutoIntimacaoProrogacao extends BaseRootVO implements Serializable {

	public static String REF = "AutoIntimacaoProrogacao";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_DATA_PROROGADA = "dataProrogada";
	public static final String PROP_AUTO_INTIMACAO = "autoIntimacao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_USUARIO = "dataUsuario";
	public static final String PROP_PRAZO = "prazo";
	public static final String PROP_OBSERVACAO_PRAZO = "observacaoPrazo";


	// constructors
	public BaseAutoIntimacaoProrogacao () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAutoIntimacaoProrogacao (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseAutoIntimacaoProrogacao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao autoIntimacao,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataProrogada,
		java.util.Date dataUsuario) {

		this.setCodigo(codigo);
		this.setAutoIntimacao(autoIntimacao);
		this.setUsuario(usuario);
		this.setDataProrogada(dataProrogada);
		this.setDataUsuario(dataUsuario);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataProrogada;
	private java.lang.Long prazo;
	private java.lang.String observacaoPrazo;
	private java.util.Date dataUsuario;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao autoIntimacao;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_auto_intimacao_prorog"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_prorogada
	 */
	public java.util.Date getDataProrogada () {
		return getPropertyValue(this, dataProrogada, PROP_DATA_PROROGADA); 
	}

	/**
	 * Set the value related to the column: dt_prorogada
	 * @param dataProrogada the dt_prorogada value
	 */
	public void setDataProrogada (java.util.Date dataProrogada) {
//        java.util.Date dataProrogadaOld = this.dataProrogada;
		this.dataProrogada = dataProrogada;
//        this.getPropertyChangeSupport().firePropertyChange ("dataProrogada", dataProrogadaOld, dataProrogada);
	}



	/**
	 * Return the value associated with the column: prazo
	 */
	public java.lang.Long getPrazo () {
		return getPropertyValue(this, prazo, PROP_PRAZO); 
	}

	/**
	 * Set the value related to the column: prazo
	 * @param prazo the prazo value
	 */
	public void setPrazo (java.lang.Long prazo) {
//        java.lang.Long prazoOld = this.prazo;
		this.prazo = prazo;
//        this.getPropertyChangeSupport().firePropertyChange ("prazo", prazoOld, prazo);
	}



	/**
	 * Return the value associated with the column: ds_observacao_prazo
	 */
	public java.lang.String getObservacaoPrazo () {
		return getPropertyValue(this, observacaoPrazo, PROP_OBSERVACAO_PRAZO); 
	}

	/**
	 * Set the value related to the column: ds_observacao_prazo
	 * @param observacaoPrazo the ds_observacao_prazo value
	 */
	public void setObservacaoPrazo (java.lang.String observacaoPrazo) {
//        java.lang.String observacaoPrazoOld = this.observacaoPrazo;
		this.observacaoPrazo = observacaoPrazo;
//        this.getPropertyChangeSupport().firePropertyChange ("observacaoPrazo", observacaoPrazoOld, observacaoPrazo);
	}



	/**
	 * Return the value associated with the column: dt_usuario
	 */
	public java.util.Date getDataUsuario () {
		return getPropertyValue(this, dataUsuario, PROP_DATA_USUARIO); 
	}

	/**
	 * Set the value related to the column: dt_usuario
	 * @param dataUsuario the dt_usuario value
	 */
	public void setDataUsuario (java.util.Date dataUsuario) {
//        java.util.Date dataUsuarioOld = this.dataUsuario;
		this.dataUsuario = dataUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsuario", dataUsuarioOld, dataUsuario);
	}



	/**
	 * Return the value associated with the column: cd_auto_intimacao
	 */
	public br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao getAutoIntimacao () {
		return getPropertyValue(this, autoIntimacao, PROP_AUTO_INTIMACAO); 
	}

	/**
	 * Set the value related to the column: cd_auto_intimacao
	 * @param autoIntimacao the cd_auto_intimacao value
	 */
	public void setAutoIntimacao (br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao autoIntimacao) {
//        br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao autoIntimacaoOld = this.autoIntimacao;
		this.autoIntimacao = autoIntimacao;
//        this.getPropertyChangeSupport().firePropertyChange ("autoIntimacao", autoIntimacaoOld, autoIntimacao);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacaoProrogacao)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacaoProrogacao autoIntimacaoProrogacao = (br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacaoProrogacao) obj;
			if (null == this.getCodigo() || null == autoIntimacaoProrogacao.getCodigo()) return false;
			else return (this.getCodigo().equals(autoIntimacaoProrogacao.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}