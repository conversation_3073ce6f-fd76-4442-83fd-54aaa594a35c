<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.entradas.recebimento"  >

    <class name="RegistroItemNotaFiscal" table="registro_itens_nota_fiscal">
        
        <id
            name="codigo"
            type="java.lang.Long" 
            column="cd_reg_it_nf"
            >
            <generator class="sequence">
                <param name="sequence">seq_registro_it_nf</param>
            </generator>
        </id> <version column="version" name="version" type="long" />
	
        <property
            column="item_nf"
            name="item"
            type="java.lang.Long"
            not-null="true"
         />

        <many-to-one 
            class="RegistroNotaFiscal" 
            name="registroNotaFiscal" 
            not-null="true"
        >
            <column name="cd_reg_nf" />
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.entradas.estoque.Produto"
                name="produto"
                not-null="true"
            >
            <column name="cod_pro" />
        </many-to-one>

        <property
            column="quantidade"
            name="quantidade"
            not-null="true"
            type="java.lang.Double"
         />

        <property
            column="quantidade"
            name="roQuantidadeAnterior"
            type="java.lang.Double"
            insert="false"
            update="false"
         />

         <property
            column="preco_unitario"
            name="precoUnitario"
            not-null="true"
            type="java.lang.Double"
         />

         <property
            column="perc_ipi"
            name="percentualIpi"
            not-null="false"
            type="java.lang.Double"
         />

         <property
            column="valor_ipi"
            name="valorIpi"
            not-null="false"
            type="java.lang.Double"
         />

         <property
            column="status"
            length="4"
            name="status"
            not-null="true"
            type="java.lang.Long"
         />

         <property
            column="qtdade_est"
            name="quantidadeEstoque"
            not-null="false"
            type="java.lang.Double"
         />

         <property
            column="qtdade_devolvida"
            name="quantidadeDevolvida"
            not-null="false"
            type="java.lang.Double"
         />

        <property
            column="desconto"
            name="desconto"
            not-null="false"
            type="java.lang.Double"
         />

        <property
            column="qtdade_extra"
            name="quantidadeExtra"
            not-null="false"
            type="java.lang.Double"
         />

        <property
            column="divergencia_entrada"
            length="70"
            name="divergenciaEntrada"
            not-null="false"
            type="string"
         />

        <property
            column="descricao"
            length="60"
            name="descricaoProduto"
            not-null="false"
            type="string"
         />

         <property
            column="valor_icms"
            name="valorIcms"
            not-null="false"
            type="java.lang.Double"
         />
         
         <property
            column="perc_icms"
            name="percentualIcms"
            not-null="false"
            type="java.lang.Double"
         />

        <property
            column="unidade"
            name="unidade"
            not-null="false"
            type="string"
        />

        <property
                column="valor_item"
                name="valorItem"
                not-null="false"
                type="java.lang.Double" 
             />

        <property
            column="atende_oc"
            name="flagAtenderOrdemCompra"
            type="java.lang.String"
            length="1"
        />

        <property
            column="valor_bc_ipi"
            name="valorBaseCaculoIpi" 
            not-null="false"
            type="java.lang.Double"
         />
         
         <property
            column="valor_bc_icms"
            name="valorBaseCaculoIcms" 
            not-null="false"
            type="java.lang.Double"
         />
         
         <property
            column="preco_divergente"
            name="precoDivergente"
            type="java.lang.Long"
         />
		
    </class>
</hibernate-mapping>
