package br.com.ksisolucoes.vo.vigilancia.investigacao.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the investigacao_agr_doenca_lyme table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agr_doenca_lyme"
 */

public abstract class BaseInvestigacaoAgravoDoencaLyme extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravoDoencaLyme";
	public static final String PROP_SINAIS_SINTOMAS_FADIGA = "sinaisSintomasFadiga";
	public static final String PROP_DATA_PRESENCA_CARRAPATO = "dataPresencaCarrapato";
	public static final String PROP_SOROLOGIA_ELISA_RESULTADO_IGM = "sorologiaElisaResultadoIgm";
	public static final String PROP_LOCAL_PROVAVEL_INFECCAO = "localProvavelInfeccao";
	public static final String PROP_SINAIS_SINTOMAS_RIGIDEZ_NUCA = "sinaisSintomasRigidezNuca";
	public static final String PROP_DATA_ANIMAIS_DOENTES = "dataAnimaisDoentes";
	public static final String PROP_MANIFESTACOES_CARDIACAS_CARDIOMEGALIA = "manifestacoesCardiacasCardiomegalia";
	public static final String PROP_BAIRRO_LOCAL_INFECCAO = "bairroLocalInfeccao";
	public static final String PROP_SOROLOGIA_ELISA_RESULTADO_IGG = "sorologiaElisaResultadoIgg";
	public static final String PROP_DATA_MORTE_ANIMAL = "dataMorteAnimal";
	public static final String PROP_DATA_ENCERRAMENTO = "dataEncerramento";
	public static final String PROP_DATA_CASOS_HUMANOS = "dataCasosHumanos";
	public static final String PROP_CASO_AUTOCTONE = "casoAutoctone";
	public static final String PROP_AMBIENTE_INFECCAO = "ambienteInfeccao";
	public static final String PROP_SITUACAO_RISCO_CASOS_HUMANOS = "situacaoRiscoCasosHumanos";
	public static final String PROP_CRITERIO_CONFIRMACAO_DESCARTE = "criterioConfirmacaoDescarte";
	public static final String PROP_DATA_OBITO = "dataObito";
	public static final String PROP_MANIFESTACOES_NAUROLOGICAS_NEURITE_CRANIANA = "manifestacoesNaurologicasNeuriteCraniana";
	public static final String PROP_HOSPITAL = "hospital";
	public static final String PROP_CIDADE_LOCAL_INFECCAO = "cidadeLocalInfeccao";
	public static final String PROP_SITUACAO_RISCO_PRESENCA_CARRAPATO = "situacaoRiscoPresencaCarrapato";
	public static final String PROP_SOROLOGIA_WESTERN_BLOT_DATA = "sorologiaWesternBlotData";
	public static final String PROP_SITUACAO_RISCO_AREAS_MATA = "situacaoRiscoAreasMata";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_MANIFESTACOES_CARDIACAS_ARRITMIA = "manifestacoesCardiacasArritmia";
	public static final String PROP_CLASSIFICACAO_FINAL = "classificacaoFinal";
	public static final String PROP_SITUACAO_RISCO_CONTATO_ANIMAIS = "situacaoRiscoContatoAnimais";
	public static final String PROP_MANIFESTACOES_NAUROLOGICAS_MENINGITE = "manifestacoesNaurologicasMeningite";
	public static final String PROP_MANIFESTACOES_NAUROLOGICAS_PERIFERICA = "manifestacoesNaurologicasPeriferica";
	public static final String PROP_SOROLOGIA_ELISA_DATA = "sorologiaElisaData";
	public static final String PROP_SOROLOGIA_WESTERN_BLOT = "sorologiaWesternBlot";
	public static final String PROP_SOROLOGIA_WESTERN_BLOT_RESULTADO_IGM_DESC = "sorologiaWesternBlotResultadoIgmDesc";
	public static final String PROP_SITUACAO_RISCO_MORTE_ANIMAL = "situacaoRiscoMorteAnimal";
	public static final String PROP_CASOS_ANTERIORES_LYME = "casosAnterioresLyme";
	public static final String PROP_ANIMAIS_DOMESTICOS_DESC = "animaisDomesticosDesc";
	public static final String PROP_SINAIS_SINTOMAS_MIALGIA = "sinaisSintomasMialgia";
	public static final String PROP_DATA_CONTATO_ANIMAIS = "dataContatoAnimais";
	public static final String PROP_SINAIS_SINTOMAS_PETEQUIAS = "sinaisSintomasPetequias";
	public static final String PROP_SINAIS_SINTOMAS_AUMENTO_GANGLIOS = "sinaisSintomasAumentoGanglios";
	public static final String PROP_SITUACAO_RISCO_ANIMAIS_DOENTES = "situacaoRiscoAnimaisDoentes";
	public static final String PROP_SOROLOGIA_WESTERN_BLOT_IGG = "sorologiaWesternBlotIgg";
	public static final String PROP_EVOLUCAO_CASO = "evolucaoCaso";
	public static final String PROP_SINAIS_SINTOMAS_MAL_ESTAR = "sinaisSintomasMalEstar";
	public static final String PROP_DATA_ALTA = "dataAlta";
	public static final String PROP_SINAIS_SINTOMAS_FEBRE = "sinaisSintomasFebre";
	public static final String PROP_DATA_INVESTIGACAO = "dataInvestigacao";
	public static final String PROP_USUARIO_ENCERRAMENTO = "usuarioEncerramento";
	public static final String PROP_SINAIS_SINTOMAS_ERITME_CRONICO = "sinaisSintomasEritmeCronico";
	public static final String PROP_FLAG_INFORMACOES_COMPLEMENTARES = "flagInformacoesComplementares";
	public static final String PROP_SOROLOGIA_WESTERN_BLOT_IGG_DESC = "sorologiaWesternBlotIggDesc";
	public static final String PROP_DISTRITO_LOCAL_INFECCAO = "distritoLocalInfeccao";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_DATA_PICADA_CARRAPATO = "dataPicadaCarrapato";
	public static final String PROP_SOROLOGIA_WESTERN_BLOT_RESULTADO_IGM = "sorologiaWesternBlotResultadoIgm";
	public static final String PROP_SOROLOGIA_ELISA_RESULTADO_IGM_DESC = "sorologiaElisaResultadoIgmDesc";
	public static final String PROP_SOROLOGIA_ELISA = "sorologiaElisa";
	public static final String PROP_CONTATO_ANIMAIS_SILVESTRES = "contatoAnimaisSilvestres";
	public static final String PROP_DOENCA_RELACIONADA_TRABALHO = "doencaRelacionadaTrabalho";
	public static final String PROP_SOROLOGIA_ELISA_RESULTADO_IGG_DESC = "sorologiaElisaResultadoIggDesc";
	public static final String PROP_HOSPITALIZACAO = "hospitalizacao";
	public static final String PROP_REGISTRO_AGRAVO = "registroAgravo";
	public static final String PROP_OCUPACAO_CBO = "ocupacaoCbo";
	public static final String PROP_SITUACAO_RISCO_PICADA_CARRAPATO = "situacaoRiscoPicadaCarrapato";
	public static final String PROP_DATA_AREAS_MATA = "dataAreasMata";
	public static final String PROP_SINAIS_SINTOMAS_ASTRALGIA = "sinaisSintomasAstralgia";
	public static final String PROP_SINAIS_SINTOMAS_CEFALEIA = "sinaisSintomasCefaleia";
	public static final String PROP_CONTATO_ANIMAIS_DOMESTICOS = "contatoAnimaisDomesticos";
	public static final String PROP_PAIS_LOCAL_INFECCAO = "paisLocalInfeccao";
	public static final String PROP_DATA_INTERNACAO = "dataInternacao";


	// constructors
	public BaseInvestigacaoAgravoDoencaLyme () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravoDoencaLyme (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseInvestigacaoAgravoDoencaLyme (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
		java.lang.String flagInformacoesComplementares) {

		this.setCodigo(codigo);
		this.setRegistroAgravo(registroAgravo);
		this.setFlagInformacoesComplementares(flagInformacoesComplementares);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String flagInformacoesComplementares;
	private java.util.Date dataInvestigacao;
	private java.lang.Long situacaoRiscoPicadaCarrapato;
	private java.util.Date dataPicadaCarrapato;
	private java.lang.Long situacaoRiscoPresencaCarrapato;
	private java.util.Date dataPresencaCarrapato;
	private java.lang.Long situacaoRiscoAreasMata;
	private java.util.Date dataAreasMata;
	private java.lang.Long situacaoRiscoContatoAnimais;
	private java.util.Date dataContatoAnimais;
	private java.lang.Long contatoAnimaisSilvestres;
	private java.lang.Long contatoAnimaisDomesticos;
	private java.lang.String animaisDomesticosDesc;
	private java.lang.Long situacaoRiscoAnimaisDoentes;
	private java.util.Date dataAnimaisDoentes;
	private java.lang.Long situacaoRiscoMorteAnimal;
	private java.util.Date dataMorteAnimal;
	private java.lang.Long situacaoRiscoCasosHumanos;
	private java.util.Date dataCasosHumanos;
	private java.lang.Long sinaisSintomasFebre;
	private java.lang.Long sinaisSintomasMialgia;
	private java.lang.Long sinaisSintomasAstralgia;
	private java.lang.Long sinaisSintomasCefaleia;
	private java.lang.Long sinaisSintomasMalEstar;
	private java.lang.Long sinaisSintomasPetequias;
	private java.lang.Long sinaisSintomasRigidezNuca;
	private java.lang.Long sinaisSintomasFadiga;
	private java.lang.Long sinaisSintomasEritmeCronico;
	private java.lang.Long sinaisSintomasAumentoGanglios;
	private java.lang.Long manifestacoesNaurologicasMeningite;
	private java.lang.Long manifestacoesNaurologicasNeuriteCraniana;
	private java.lang.Long manifestacoesNaurologicasPeriferica;
	private java.lang.Long manifestacoesCardiacasCardiomegalia;
	private java.lang.Long manifestacoesCardiacasArritmia;
	private java.lang.Long casosAnterioresLyme;
	private java.lang.Long hospitalizacao;
	private java.util.Date dataInternacao;
	private java.util.Date dataAlta;
	private java.lang.Long sorologiaElisa;
	private java.util.Date sorologiaElisaData;
	private java.lang.Long sorologiaElisaResultadoIgm;
	private java.lang.String sorologiaElisaResultadoIgmDesc;
	private java.lang.Long sorologiaElisaResultadoIgg;
	private java.lang.String sorologiaElisaResultadoIggDesc;
	private java.lang.Long sorologiaWesternBlot;
	private java.util.Date sorologiaWesternBlotData;
	private java.lang.Long sorologiaWesternBlotResultadoIgm;
	private java.lang.String sorologiaWesternBlotResultadoIgmDesc;
	private java.lang.Long sorologiaWesternBlotIgg;
	private java.lang.String sorologiaWesternBlotIggDesc;
	private java.lang.Long casoAutoctone;
	private java.lang.String distritoLocalInfeccao;
	private java.lang.String bairroLocalInfeccao;
	private java.lang.Long localProvavelInfeccao;
	private java.lang.Long ambienteInfeccao;
	private java.lang.Long doencaRelacionadaTrabalho;
	private java.lang.Long classificacaoFinal;
	private java.lang.Long criterioConfirmacaoDescarte;
	private java.lang.Long evolucaoCaso;
	private java.util.Date dataObito;
	private java.lang.String observacao;
	private java.util.Date dataEncerramento;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo;
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo;
	private br.com.ksisolucoes.vo.basico.Empresa hospital;
	private br.com.ksisolucoes.vo.basico.Cidade cidadeLocalInfeccao;
	private br.com.ksisolucoes.vo.basico.Pais paisLocalInfeccao;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_investigacao_agr_doenca_lyme"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: flag_informacoes_complementares
	 */
	public java.lang.String getFlagInformacoesComplementares () {
		return getPropertyValue(this, flagInformacoesComplementares, PROP_FLAG_INFORMACOES_COMPLEMENTARES); 
	}

	/**
	 * Set the value related to the column: flag_informacoes_complementares
	 * @param flagInformacoesComplementares the flag_informacoes_complementares value
	 */
	public void setFlagInformacoesComplementares (java.lang.String flagInformacoesComplementares) {
//        java.lang.String flagInformacoesComplementaresOld = this.flagInformacoesComplementares;
		this.flagInformacoesComplementares = flagInformacoesComplementares;
//        this.getPropertyChangeSupport().firePropertyChange ("flagInformacoesComplementares", flagInformacoesComplementaresOld, flagInformacoesComplementares);
	}



	/**
	 * Return the value associated with the column: dt_investigacao
	 */
	public java.util.Date getDataInvestigacao () {
		return getPropertyValue(this, dataInvestigacao, PROP_DATA_INVESTIGACAO); 
	}

	/**
	 * Set the value related to the column: dt_investigacao
	 * @param dataInvestigacao the dt_investigacao value
	 */
	public void setDataInvestigacao (java.util.Date dataInvestigacao) {
//        java.util.Date dataInvestigacaoOld = this.dataInvestigacao;
		this.dataInvestigacao = dataInvestigacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInvestigacao", dataInvestigacaoOld, dataInvestigacao);
	}



	/**
	 * Return the value associated with the column: situacao_risco_picada_carrapato
	 */
	public java.lang.Long getSituacaoRiscoPicadaCarrapato () {
		return getPropertyValue(this, situacaoRiscoPicadaCarrapato, PROP_SITUACAO_RISCO_PICADA_CARRAPATO); 
	}

	/**
	 * Set the value related to the column: situacao_risco_picada_carrapato
	 * @param situacaoRiscoPicadaCarrapato the situacao_risco_picada_carrapato value
	 */
	public void setSituacaoRiscoPicadaCarrapato (java.lang.Long situacaoRiscoPicadaCarrapato) {
//        java.lang.Long situacaoRiscoPicadaCarrapatoOld = this.situacaoRiscoPicadaCarrapato;
		this.situacaoRiscoPicadaCarrapato = situacaoRiscoPicadaCarrapato;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoPicadaCarrapato", situacaoRiscoPicadaCarrapatoOld, situacaoRiscoPicadaCarrapato);
	}



	/**
	 * Return the value associated with the column: dt_picada_carrapato
	 */
	public java.util.Date getDataPicadaCarrapato () {
		return getPropertyValue(this, dataPicadaCarrapato, PROP_DATA_PICADA_CARRAPATO); 
	}

	/**
	 * Set the value related to the column: dt_picada_carrapato
	 * @param dataPicadaCarrapato the dt_picada_carrapato value
	 */
	public void setDataPicadaCarrapato (java.util.Date dataPicadaCarrapato) {
//        java.util.Date dataPicadaCarrapatoOld = this.dataPicadaCarrapato;
		this.dataPicadaCarrapato = dataPicadaCarrapato;
//        this.getPropertyChangeSupport().firePropertyChange ("dataPicadaCarrapato", dataPicadaCarrapatoOld, dataPicadaCarrapato);
	}



	/**
	 * Return the value associated with the column: situacao_risco_presenca_carrapato
	 */
	public java.lang.Long getSituacaoRiscoPresencaCarrapato () {
		return getPropertyValue(this, situacaoRiscoPresencaCarrapato, PROP_SITUACAO_RISCO_PRESENCA_CARRAPATO); 
	}

	/**
	 * Set the value related to the column: situacao_risco_presenca_carrapato
	 * @param situacaoRiscoPresencaCarrapato the situacao_risco_presenca_carrapato value
	 */
	public void setSituacaoRiscoPresencaCarrapato (java.lang.Long situacaoRiscoPresencaCarrapato) {
//        java.lang.Long situacaoRiscoPresencaCarrapatoOld = this.situacaoRiscoPresencaCarrapato;
		this.situacaoRiscoPresencaCarrapato = situacaoRiscoPresencaCarrapato;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoPresencaCarrapato", situacaoRiscoPresencaCarrapatoOld, situacaoRiscoPresencaCarrapato);
	}



	/**
	 * Return the value associated with the column: dt_presenca_carrapato
	 */
	public java.util.Date getDataPresencaCarrapato () {
		return getPropertyValue(this, dataPresencaCarrapato, PROP_DATA_PRESENCA_CARRAPATO); 
	}

	/**
	 * Set the value related to the column: dt_presenca_carrapato
	 * @param dataPresencaCarrapato the dt_presenca_carrapato value
	 */
	public void setDataPresencaCarrapato (java.util.Date dataPresencaCarrapato) {
//        java.util.Date dataPresencaCarrapatoOld = this.dataPresencaCarrapato;
		this.dataPresencaCarrapato = dataPresencaCarrapato;
//        this.getPropertyChangeSupport().firePropertyChange ("dataPresencaCarrapato", dataPresencaCarrapatoOld, dataPresencaCarrapato);
	}



	/**
	 * Return the value associated with the column: situacao_risco_areas_mata
	 */
	public java.lang.Long getSituacaoRiscoAreasMata () {
		return getPropertyValue(this, situacaoRiscoAreasMata, PROP_SITUACAO_RISCO_AREAS_MATA); 
	}

	/**
	 * Set the value related to the column: situacao_risco_areas_mata
	 * @param situacaoRiscoAreasMata the situacao_risco_areas_mata value
	 */
	public void setSituacaoRiscoAreasMata (java.lang.Long situacaoRiscoAreasMata) {
//        java.lang.Long situacaoRiscoAreasMataOld = this.situacaoRiscoAreasMata;
		this.situacaoRiscoAreasMata = situacaoRiscoAreasMata;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoAreasMata", situacaoRiscoAreasMataOld, situacaoRiscoAreasMata);
	}



	/**
	 * Return the value associated with the column: dt_areas_mata
	 */
	public java.util.Date getDataAreasMata () {
		return getPropertyValue(this, dataAreasMata, PROP_DATA_AREAS_MATA); 
	}

	/**
	 * Set the value related to the column: dt_areas_mata
	 * @param dataAreasMata the dt_areas_mata value
	 */
	public void setDataAreasMata (java.util.Date dataAreasMata) {
//        java.util.Date dataAreasMataOld = this.dataAreasMata;
		this.dataAreasMata = dataAreasMata;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAreasMata", dataAreasMataOld, dataAreasMata);
	}



	/**
	 * Return the value associated with the column: situacao_risco_contato_animais
	 */
	public java.lang.Long getSituacaoRiscoContatoAnimais () {
		return getPropertyValue(this, situacaoRiscoContatoAnimais, PROP_SITUACAO_RISCO_CONTATO_ANIMAIS); 
	}

	/**
	 * Set the value related to the column: situacao_risco_contato_animais
	 * @param situacaoRiscoContatoAnimais the situacao_risco_contato_animais value
	 */
	public void setSituacaoRiscoContatoAnimais (java.lang.Long situacaoRiscoContatoAnimais) {
//        java.lang.Long situacaoRiscoContatoAnimaisOld = this.situacaoRiscoContatoAnimais;
		this.situacaoRiscoContatoAnimais = situacaoRiscoContatoAnimais;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoContatoAnimais", situacaoRiscoContatoAnimaisOld, situacaoRiscoContatoAnimais);
	}



	/**
	 * Return the value associated with the column: dt_contato_animais
	 */
	public java.util.Date getDataContatoAnimais () {
		return getPropertyValue(this, dataContatoAnimais, PROP_DATA_CONTATO_ANIMAIS); 
	}

	/**
	 * Set the value related to the column: dt_contato_animais
	 * @param dataContatoAnimais the dt_contato_animais value
	 */
	public void setDataContatoAnimais (java.util.Date dataContatoAnimais) {
//        java.util.Date dataContatoAnimaisOld = this.dataContatoAnimais;
		this.dataContatoAnimais = dataContatoAnimais;
//        this.getPropertyChangeSupport().firePropertyChange ("dataContatoAnimais", dataContatoAnimaisOld, dataContatoAnimais);
	}



	/**
	 * Return the value associated with the column: contato_animais_silvestres
	 */
	public java.lang.Long getContatoAnimaisSilvestres () {
		return getPropertyValue(this, contatoAnimaisSilvestres, PROP_CONTATO_ANIMAIS_SILVESTRES); 
	}

	/**
	 * Set the value related to the column: contato_animais_silvestres
	 * @param contatoAnimaisSilvestres the contato_animais_silvestres value
	 */
	public void setContatoAnimaisSilvestres (java.lang.Long contatoAnimaisSilvestres) {
//        java.lang.Long contatoAnimaisSilvestresOld = this.contatoAnimaisSilvestres;
		this.contatoAnimaisSilvestres = contatoAnimaisSilvestres;
//        this.getPropertyChangeSupport().firePropertyChange ("contatoAnimaisSilvestres", contatoAnimaisSilvestresOld, contatoAnimaisSilvestres);
	}



	/**
	 * Return the value associated with the column: contato_animais_domesticos
	 */
	public java.lang.Long getContatoAnimaisDomesticos () {
		return getPropertyValue(this, contatoAnimaisDomesticos, PROP_CONTATO_ANIMAIS_DOMESTICOS); 
	}

	/**
	 * Set the value related to the column: contato_animais_domesticos
	 * @param contatoAnimaisDomesticos the contato_animais_domesticos value
	 */
	public void setContatoAnimaisDomesticos (java.lang.Long contatoAnimaisDomesticos) {
//        java.lang.Long contatoAnimaisDomesticosOld = this.contatoAnimaisDomesticos;
		this.contatoAnimaisDomesticos = contatoAnimaisDomesticos;
//        this.getPropertyChangeSupport().firePropertyChange ("contatoAnimaisDomesticos", contatoAnimaisDomesticosOld, contatoAnimaisDomesticos);
	}



	/**
	 * Return the value associated with the column: animais_domesticos_desc
	 */
	public java.lang.String getAnimaisDomesticosDesc () {
		return getPropertyValue(this, animaisDomesticosDesc, PROP_ANIMAIS_DOMESTICOS_DESC); 
	}

	/**
	 * Set the value related to the column: animais_domesticos_desc
	 * @param animaisDomesticosDesc the animais_domesticos_desc value
	 */
	public void setAnimaisDomesticosDesc (java.lang.String animaisDomesticosDesc) {
//        java.lang.String animaisDomesticosDescOld = this.animaisDomesticosDesc;
		this.animaisDomesticosDesc = animaisDomesticosDesc;
//        this.getPropertyChangeSupport().firePropertyChange ("animaisDomesticosDesc", animaisDomesticosDescOld, animaisDomesticosDesc);
	}



	/**
	 * Return the value associated with the column: situacao_risco_animais_doentes
	 */
	public java.lang.Long getSituacaoRiscoAnimaisDoentes () {
		return getPropertyValue(this, situacaoRiscoAnimaisDoentes, PROP_SITUACAO_RISCO_ANIMAIS_DOENTES); 
	}

	/**
	 * Set the value related to the column: situacao_risco_animais_doentes
	 * @param situacaoRiscoAnimaisDoentes the situacao_risco_animais_doentes value
	 */
	public void setSituacaoRiscoAnimaisDoentes (java.lang.Long situacaoRiscoAnimaisDoentes) {
//        java.lang.Long situacaoRiscoAnimaisDoentesOld = this.situacaoRiscoAnimaisDoentes;
		this.situacaoRiscoAnimaisDoentes = situacaoRiscoAnimaisDoentes;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoAnimaisDoentes", situacaoRiscoAnimaisDoentesOld, situacaoRiscoAnimaisDoentes);
	}



	/**
	 * Return the value associated with the column: dt_animais_doentes
	 */
	public java.util.Date getDataAnimaisDoentes () {
		return getPropertyValue(this, dataAnimaisDoentes, PROP_DATA_ANIMAIS_DOENTES); 
	}

	/**
	 * Set the value related to the column: dt_animais_doentes
	 * @param dataAnimaisDoentes the dt_animais_doentes value
	 */
	public void setDataAnimaisDoentes (java.util.Date dataAnimaisDoentes) {
//        java.util.Date dataAnimaisDoentesOld = this.dataAnimaisDoentes;
		this.dataAnimaisDoentes = dataAnimaisDoentes;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAnimaisDoentes", dataAnimaisDoentesOld, dataAnimaisDoentes);
	}



	/**
	 * Return the value associated with the column: situacao_risco_morte_animal
	 */
	public java.lang.Long getSituacaoRiscoMorteAnimal () {
		return getPropertyValue(this, situacaoRiscoMorteAnimal, PROP_SITUACAO_RISCO_MORTE_ANIMAL); 
	}

	/**
	 * Set the value related to the column: situacao_risco_morte_animal
	 * @param situacaoRiscoMorteAnimal the situacao_risco_morte_animal value
	 */
	public void setSituacaoRiscoMorteAnimal (java.lang.Long situacaoRiscoMorteAnimal) {
//        java.lang.Long situacaoRiscoMorteAnimalOld = this.situacaoRiscoMorteAnimal;
		this.situacaoRiscoMorteAnimal = situacaoRiscoMorteAnimal;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoMorteAnimal", situacaoRiscoMorteAnimalOld, situacaoRiscoMorteAnimal);
	}



	/**
	 * Return the value associated with the column: dt_morte_animal
	 */
	public java.util.Date getDataMorteAnimal () {
		return getPropertyValue(this, dataMorteAnimal, PROP_DATA_MORTE_ANIMAL); 
	}

	/**
	 * Set the value related to the column: dt_morte_animal
	 * @param dataMorteAnimal the dt_morte_animal value
	 */
	public void setDataMorteAnimal (java.util.Date dataMorteAnimal) {
//        java.util.Date dataMorteAnimalOld = this.dataMorteAnimal;
		this.dataMorteAnimal = dataMorteAnimal;
//        this.getPropertyChangeSupport().firePropertyChange ("dataMorteAnimal", dataMorteAnimalOld, dataMorteAnimal);
	}



	/**
	 * Return the value associated with the column: situacao_risco_casos_humanos
	 */
	public java.lang.Long getSituacaoRiscoCasosHumanos () {
		return getPropertyValue(this, situacaoRiscoCasosHumanos, PROP_SITUACAO_RISCO_CASOS_HUMANOS); 
	}

	/**
	 * Set the value related to the column: situacao_risco_casos_humanos
	 * @param situacaoRiscoCasosHumanos the situacao_risco_casos_humanos value
	 */
	public void setSituacaoRiscoCasosHumanos (java.lang.Long situacaoRiscoCasosHumanos) {
//        java.lang.Long situacaoRiscoCasosHumanosOld = this.situacaoRiscoCasosHumanos;
		this.situacaoRiscoCasosHumanos = situacaoRiscoCasosHumanos;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoCasosHumanos", situacaoRiscoCasosHumanosOld, situacaoRiscoCasosHumanos);
	}



	/**
	 * Return the value associated with the column: dt_casos_humanos
	 */
	public java.util.Date getDataCasosHumanos () {
		return getPropertyValue(this, dataCasosHumanos, PROP_DATA_CASOS_HUMANOS); 
	}

	/**
	 * Set the value related to the column: dt_casos_humanos
	 * @param dataCasosHumanos the dt_casos_humanos value
	 */
	public void setDataCasosHumanos (java.util.Date dataCasosHumanos) {
//        java.util.Date dataCasosHumanosOld = this.dataCasosHumanos;
		this.dataCasosHumanos = dataCasosHumanos;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCasosHumanos", dataCasosHumanosOld, dataCasosHumanos);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_febre
	 */
	public java.lang.Long getSinaisSintomasFebre () {
		return getPropertyValue(this, sinaisSintomasFebre, PROP_SINAIS_SINTOMAS_FEBRE); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_febre
	 * @param sinaisSintomasFebre the sinais_sintomas_febre value
	 */
	public void setSinaisSintomasFebre (java.lang.Long sinaisSintomasFebre) {
//        java.lang.Long sinaisSintomasFebreOld = this.sinaisSintomasFebre;
		this.sinaisSintomasFebre = sinaisSintomasFebre;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasFebre", sinaisSintomasFebreOld, sinaisSintomasFebre);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_mialgia
	 */
	public java.lang.Long getSinaisSintomasMialgia () {
		return getPropertyValue(this, sinaisSintomasMialgia, PROP_SINAIS_SINTOMAS_MIALGIA); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_mialgia
	 * @param sinaisSintomasMialgia the sinais_sintomas_mialgia value
	 */
	public void setSinaisSintomasMialgia (java.lang.Long sinaisSintomasMialgia) {
//        java.lang.Long sinaisSintomasMialgiaOld = this.sinaisSintomasMialgia;
		this.sinaisSintomasMialgia = sinaisSintomasMialgia;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasMialgia", sinaisSintomasMialgiaOld, sinaisSintomasMialgia);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_astralgia
	 */
	public java.lang.Long getSinaisSintomasAstralgia () {
		return getPropertyValue(this, sinaisSintomasAstralgia, PROP_SINAIS_SINTOMAS_ASTRALGIA); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_astralgia
	 * @param sinaisSintomasAstralgia the sinais_sintomas_astralgia value
	 */
	public void setSinaisSintomasAstralgia (java.lang.Long sinaisSintomasAstralgia) {
//        java.lang.Long sinaisSintomasAstralgiaOld = this.sinaisSintomasAstralgia;
		this.sinaisSintomasAstralgia = sinaisSintomasAstralgia;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasAstralgia", sinaisSintomasAstralgiaOld, sinaisSintomasAstralgia);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_cefaleia
	 */
	public java.lang.Long getSinaisSintomasCefaleia () {
		return getPropertyValue(this, sinaisSintomasCefaleia, PROP_SINAIS_SINTOMAS_CEFALEIA); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_cefaleia
	 * @param sinaisSintomasCefaleia the sinais_sintomas_cefaleia value
	 */
	public void setSinaisSintomasCefaleia (java.lang.Long sinaisSintomasCefaleia) {
//        java.lang.Long sinaisSintomasCefaleiaOld = this.sinaisSintomasCefaleia;
		this.sinaisSintomasCefaleia = sinaisSintomasCefaleia;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasCefaleia", sinaisSintomasCefaleiaOld, sinaisSintomasCefaleia);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_mal_estar
	 */
	public java.lang.Long getSinaisSintomasMalEstar () {
		return getPropertyValue(this, sinaisSintomasMalEstar, PROP_SINAIS_SINTOMAS_MAL_ESTAR); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_mal_estar
	 * @param sinaisSintomasMalEstar the sinais_sintomas_mal_estar value
	 */
	public void setSinaisSintomasMalEstar (java.lang.Long sinaisSintomasMalEstar) {
//        java.lang.Long sinaisSintomasMalEstarOld = this.sinaisSintomasMalEstar;
		this.sinaisSintomasMalEstar = sinaisSintomasMalEstar;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasMalEstar", sinaisSintomasMalEstarOld, sinaisSintomasMalEstar);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_petequias
	 */
	public java.lang.Long getSinaisSintomasPetequias () {
		return getPropertyValue(this, sinaisSintomasPetequias, PROP_SINAIS_SINTOMAS_PETEQUIAS); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_petequias
	 * @param sinaisSintomasPetequias the sinais_sintomas_petequias value
	 */
	public void setSinaisSintomasPetequias (java.lang.Long sinaisSintomasPetequias) {
//        java.lang.Long sinaisSintomasPetequiasOld = this.sinaisSintomasPetequias;
		this.sinaisSintomasPetequias = sinaisSintomasPetequias;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasPetequias", sinaisSintomasPetequiasOld, sinaisSintomasPetequias);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_rigidez_nuca
	 */
	public java.lang.Long getSinaisSintomasRigidezNuca () {
		return getPropertyValue(this, sinaisSintomasRigidezNuca, PROP_SINAIS_SINTOMAS_RIGIDEZ_NUCA); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_rigidez_nuca
	 * @param sinaisSintomasRigidezNuca the sinais_sintomas_rigidez_nuca value
	 */
	public void setSinaisSintomasRigidezNuca (java.lang.Long sinaisSintomasRigidezNuca) {
//        java.lang.Long sinaisSintomasRigidezNucaOld = this.sinaisSintomasRigidezNuca;
		this.sinaisSintomasRigidezNuca = sinaisSintomasRigidezNuca;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasRigidezNuca", sinaisSintomasRigidezNucaOld, sinaisSintomasRigidezNuca);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_fadiga
	 */
	public java.lang.Long getSinaisSintomasFadiga () {
		return getPropertyValue(this, sinaisSintomasFadiga, PROP_SINAIS_SINTOMAS_FADIGA); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_fadiga
	 * @param sinaisSintomasFadiga the sinais_sintomas_fadiga value
	 */
	public void setSinaisSintomasFadiga (java.lang.Long sinaisSintomasFadiga) {
//        java.lang.Long sinaisSintomasFadigaOld = this.sinaisSintomasFadiga;
		this.sinaisSintomasFadiga = sinaisSintomasFadiga;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasFadiga", sinaisSintomasFadigaOld, sinaisSintomasFadiga);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_eritema_cronico
	 */
	public java.lang.Long getSinaisSintomasEritmeCronico () {
		return getPropertyValue(this, sinaisSintomasEritmeCronico, PROP_SINAIS_SINTOMAS_ERITME_CRONICO); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_eritema_cronico
	 * @param sinaisSintomasEritmeCronico the sinais_sintomas_eritema_cronico value
	 */
	public void setSinaisSintomasEritmeCronico (java.lang.Long sinaisSintomasEritmeCronico) {
//        java.lang.Long sinaisSintomasEritmeCronicoOld = this.sinaisSintomasEritmeCronico;
		this.sinaisSintomasEritmeCronico = sinaisSintomasEritmeCronico;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasEritmeCronico", sinaisSintomasEritmeCronicoOld, sinaisSintomasEritmeCronico);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_aumento_ganglios
	 */
	public java.lang.Long getSinaisSintomasAumentoGanglios () {
		return getPropertyValue(this, sinaisSintomasAumentoGanglios, PROP_SINAIS_SINTOMAS_AUMENTO_GANGLIOS); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_aumento_ganglios
	 * @param sinaisSintomasAumentoGanglios the sinais_sintomas_aumento_ganglios value
	 */
	public void setSinaisSintomasAumentoGanglios (java.lang.Long sinaisSintomasAumentoGanglios) {
//        java.lang.Long sinaisSintomasAumentoGangliosOld = this.sinaisSintomasAumentoGanglios;
		this.sinaisSintomasAumentoGanglios = sinaisSintomasAumentoGanglios;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasAumentoGanglios", sinaisSintomasAumentoGangliosOld, sinaisSintomasAumentoGanglios);
	}



	/**
	 * Return the value associated with the column: manifestacoes_naurologicas_meningite
	 */
	public java.lang.Long getManifestacoesNaurologicasMeningite () {
		return getPropertyValue(this, manifestacoesNaurologicasMeningite, PROP_MANIFESTACOES_NAUROLOGICAS_MENINGITE); 
	}

	/**
	 * Set the value related to the column: manifestacoes_naurologicas_meningite
	 * @param manifestacoesNaurologicasMeningite the manifestacoes_naurologicas_meningite value
	 */
	public void setManifestacoesNaurologicasMeningite (java.lang.Long manifestacoesNaurologicasMeningite) {
//        java.lang.Long manifestacoesNaurologicasMeningiteOld = this.manifestacoesNaurologicasMeningite;
		this.manifestacoesNaurologicasMeningite = manifestacoesNaurologicasMeningite;
//        this.getPropertyChangeSupport().firePropertyChange ("manifestacoesNaurologicasMeningite", manifestacoesNaurologicasMeningiteOld, manifestacoesNaurologicasMeningite);
	}



	/**
	 * Return the value associated with the column: manifestacoes_naurologicas_neurite_craniana
	 */
	public java.lang.Long getManifestacoesNaurologicasNeuriteCraniana () {
		return getPropertyValue(this, manifestacoesNaurologicasNeuriteCraniana, PROP_MANIFESTACOES_NAUROLOGICAS_NEURITE_CRANIANA); 
	}

	/**
	 * Set the value related to the column: manifestacoes_naurologicas_neurite_craniana
	 * @param manifestacoesNaurologicasNeuriteCraniana the manifestacoes_naurologicas_neurite_craniana value
	 */
	public void setManifestacoesNaurologicasNeuriteCraniana (java.lang.Long manifestacoesNaurologicasNeuriteCraniana) {
//        java.lang.Long manifestacoesNaurologicasNeuriteCranianaOld = this.manifestacoesNaurologicasNeuriteCraniana;
		this.manifestacoesNaurologicasNeuriteCraniana = manifestacoesNaurologicasNeuriteCraniana;
//        this.getPropertyChangeSupport().firePropertyChange ("manifestacoesNaurologicasNeuriteCraniana", manifestacoesNaurologicasNeuriteCranianaOld, manifestacoesNaurologicasNeuriteCraniana);
	}



	/**
	 * Return the value associated with the column: manifestacoes_naurologicas_periferica
	 */
	public java.lang.Long getManifestacoesNaurologicasPeriferica () {
		return getPropertyValue(this, manifestacoesNaurologicasPeriferica, PROP_MANIFESTACOES_NAUROLOGICAS_PERIFERICA); 
	}

	/**
	 * Set the value related to the column: manifestacoes_naurologicas_periferica
	 * @param manifestacoesNaurologicasPeriferica the manifestacoes_naurologicas_periferica value
	 */
	public void setManifestacoesNaurologicasPeriferica (java.lang.Long manifestacoesNaurologicasPeriferica) {
//        java.lang.Long manifestacoesNaurologicasPerifericaOld = this.manifestacoesNaurologicasPeriferica;
		this.manifestacoesNaurologicasPeriferica = manifestacoesNaurologicasPeriferica;
//        this.getPropertyChangeSupport().firePropertyChange ("manifestacoesNaurologicasPeriferica", manifestacoesNaurologicasPerifericaOld, manifestacoesNaurologicasPeriferica);
	}



	/**
	 * Return the value associated with the column: manifestacoes_cardiacas_cardiomegalia
	 */
	public java.lang.Long getManifestacoesCardiacasCardiomegalia () {
		return getPropertyValue(this, manifestacoesCardiacasCardiomegalia, PROP_MANIFESTACOES_CARDIACAS_CARDIOMEGALIA); 
	}

	/**
	 * Set the value related to the column: manifestacoes_cardiacas_cardiomegalia
	 * @param manifestacoesCardiacasCardiomegalia the manifestacoes_cardiacas_cardiomegalia value
	 */
	public void setManifestacoesCardiacasCardiomegalia (java.lang.Long manifestacoesCardiacasCardiomegalia) {
//        java.lang.Long manifestacoesCardiacasCardiomegaliaOld = this.manifestacoesCardiacasCardiomegalia;
		this.manifestacoesCardiacasCardiomegalia = manifestacoesCardiacasCardiomegalia;
//        this.getPropertyChangeSupport().firePropertyChange ("manifestacoesCardiacasCardiomegalia", manifestacoesCardiacasCardiomegaliaOld, manifestacoesCardiacasCardiomegalia);
	}



	/**
	 * Return the value associated with the column: manifestacoes_cardiacas_arritmia
	 */
	public java.lang.Long getManifestacoesCardiacasArritmia () {
		return getPropertyValue(this, manifestacoesCardiacasArritmia, PROP_MANIFESTACOES_CARDIACAS_ARRITMIA); 
	}

	/**
	 * Set the value related to the column: manifestacoes_cardiacas_arritmia
	 * @param manifestacoesCardiacasArritmia the manifestacoes_cardiacas_arritmia value
	 */
	public void setManifestacoesCardiacasArritmia (java.lang.Long manifestacoesCardiacasArritmia) {
//        java.lang.Long manifestacoesCardiacasArritmiaOld = this.manifestacoesCardiacasArritmia;
		this.manifestacoesCardiacasArritmia = manifestacoesCardiacasArritmia;
//        this.getPropertyChangeSupport().firePropertyChange ("manifestacoesCardiacasArritmia", manifestacoesCardiacasArritmiaOld, manifestacoesCardiacasArritmia);
	}



	/**
	 * Return the value associated with the column: casos_anteriores_lyme
	 */
	public java.lang.Long getCasosAnterioresLyme () {
		return getPropertyValue(this, casosAnterioresLyme, PROP_CASOS_ANTERIORES_LYME); 
	}

	/**
	 * Set the value related to the column: casos_anteriores_lyme
	 * @param casosAnterioresLyme the casos_anteriores_lyme value
	 */
	public void setCasosAnterioresLyme (java.lang.Long casosAnterioresLyme) {
//        java.lang.Long casosAnterioresLymeOld = this.casosAnterioresLyme;
		this.casosAnterioresLyme = casosAnterioresLyme;
//        this.getPropertyChangeSupport().firePropertyChange ("casosAnterioresLyme", casosAnterioresLymeOld, casosAnterioresLyme);
	}



	/**
	 * Return the value associated with the column: hospitalizacao
	 */
	public java.lang.Long getHospitalizacao () {
		return getPropertyValue(this, hospitalizacao, PROP_HOSPITALIZACAO); 
	}

	/**
	 * Set the value related to the column: hospitalizacao
	 * @param hospitalizacao the hospitalizacao value
	 */
	public void setHospitalizacao (java.lang.Long hospitalizacao) {
//        java.lang.Long hospitalizacaoOld = this.hospitalizacao;
		this.hospitalizacao = hospitalizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("hospitalizacao", hospitalizacaoOld, hospitalizacao);
	}



	/**
	 * Return the value associated with the column: dt_internacao
	 */
	public java.util.Date getDataInternacao () {
		return getPropertyValue(this, dataInternacao, PROP_DATA_INTERNACAO); 
	}

	/**
	 * Set the value related to the column: dt_internacao
	 * @param dataInternacao the dt_internacao value
	 */
	public void setDataInternacao (java.util.Date dataInternacao) {
//        java.util.Date dataInternacaoOld = this.dataInternacao;
		this.dataInternacao = dataInternacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInternacao", dataInternacaoOld, dataInternacao);
	}



	/**
	 * Return the value associated with the column: dt_alta
	 */
	public java.util.Date getDataAlta () {
		return getPropertyValue(this, dataAlta, PROP_DATA_ALTA); 
	}

	/**
	 * Set the value related to the column: dt_alta
	 * @param dataAlta the dt_alta value
	 */
	public void setDataAlta (java.util.Date dataAlta) {
//        java.util.Date dataAltaOld = this.dataAlta;
		this.dataAlta = dataAlta;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAlta", dataAltaOld, dataAlta);
	}



	/**
	 * Return the value associated with the column: sorologia_elisa
	 */
	public java.lang.Long getSorologiaElisa () {
		return getPropertyValue(this, sorologiaElisa, PROP_SOROLOGIA_ELISA); 
	}

	/**
	 * Set the value related to the column: sorologia_elisa
	 * @param sorologiaElisa the sorologia_elisa value
	 */
	public void setSorologiaElisa (java.lang.Long sorologiaElisa) {
//        java.lang.Long sorologiaElisaOld = this.sorologiaElisa;
		this.sorologiaElisa = sorologiaElisa;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaElisa", sorologiaElisaOld, sorologiaElisa);
	}



	/**
	 * Return the value associated with the column: sorologia_elisa_dt
	 */
	public java.util.Date getSorologiaElisaData () {
		return getPropertyValue(this, sorologiaElisaData, PROP_SOROLOGIA_ELISA_DATA); 
	}

	/**
	 * Set the value related to the column: sorologia_elisa_dt
	 * @param sorologiaElisaData the sorologia_elisa_dt value
	 */
	public void setSorologiaElisaData (java.util.Date sorologiaElisaData) {
//        java.util.Date sorologiaElisaDataOld = this.sorologiaElisaData;
		this.sorologiaElisaData = sorologiaElisaData;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaElisaData", sorologiaElisaDataOld, sorologiaElisaData);
	}



	/**
	 * Return the value associated with the column: sorologia_elisa_resultado_igm
	 */
	public java.lang.Long getSorologiaElisaResultadoIgm () {
		return getPropertyValue(this, sorologiaElisaResultadoIgm, PROP_SOROLOGIA_ELISA_RESULTADO_IGM); 
	}

	/**
	 * Set the value related to the column: sorologia_elisa_resultado_igm
	 * @param sorologiaElisaResultadoIgm the sorologia_elisa_resultado_igm value
	 */
	public void setSorologiaElisaResultadoIgm (java.lang.Long sorologiaElisaResultadoIgm) {
//        java.lang.Long sorologiaElisaResultadoIgmOld = this.sorologiaElisaResultadoIgm;
		this.sorologiaElisaResultadoIgm = sorologiaElisaResultadoIgm;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaElisaResultadoIgm", sorologiaElisaResultadoIgmOld, sorologiaElisaResultadoIgm);
	}



	/**
	 * Return the value associated with the column: sorologia_elisa_resultado_igm_desc
	 */
	public java.lang.String getSorologiaElisaResultadoIgmDesc () {
		return getPropertyValue(this, sorologiaElisaResultadoIgmDesc, PROP_SOROLOGIA_ELISA_RESULTADO_IGM_DESC); 
	}

	/**
	 * Set the value related to the column: sorologia_elisa_resultado_igm_desc
	 * @param sorologiaElisaResultadoIgmDesc the sorologia_elisa_resultado_igm_desc value
	 */
	public void setSorologiaElisaResultadoIgmDesc (java.lang.String sorologiaElisaResultadoIgmDesc) {
//        java.lang.String sorologiaElisaResultadoIgmDescOld = this.sorologiaElisaResultadoIgmDesc;
		this.sorologiaElisaResultadoIgmDesc = sorologiaElisaResultadoIgmDesc;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaElisaResultadoIgmDesc", sorologiaElisaResultadoIgmDescOld, sorologiaElisaResultadoIgmDesc);
	}



	/**
	 * Return the value associated with the column: sorologia_elisa_resultado_igg
	 */
	public java.lang.Long getSorologiaElisaResultadoIgg () {
		return getPropertyValue(this, sorologiaElisaResultadoIgg, PROP_SOROLOGIA_ELISA_RESULTADO_IGG); 
	}

	/**
	 * Set the value related to the column: sorologia_elisa_resultado_igg
	 * @param sorologiaElisaResultadoIgg the sorologia_elisa_resultado_igg value
	 */
	public void setSorologiaElisaResultadoIgg (java.lang.Long sorologiaElisaResultadoIgg) {
//        java.lang.Long sorologiaElisaResultadoIggOld = this.sorologiaElisaResultadoIgg;
		this.sorologiaElisaResultadoIgg = sorologiaElisaResultadoIgg;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaElisaResultadoIgg", sorologiaElisaResultadoIggOld, sorologiaElisaResultadoIgg);
	}



	/**
	 * Return the value associated with the column: sorologia_elisa_resultado_igg_desc
	 */
	public java.lang.String getSorologiaElisaResultadoIggDesc () {
		return getPropertyValue(this, sorologiaElisaResultadoIggDesc, PROP_SOROLOGIA_ELISA_RESULTADO_IGG_DESC); 
	}

	/**
	 * Set the value related to the column: sorologia_elisa_resultado_igg_desc
	 * @param sorologiaElisaResultadoIggDesc the sorologia_elisa_resultado_igg_desc value
	 */
	public void setSorologiaElisaResultadoIggDesc (java.lang.String sorologiaElisaResultadoIggDesc) {
//        java.lang.String sorologiaElisaResultadoIggDescOld = this.sorologiaElisaResultadoIggDesc;
		this.sorologiaElisaResultadoIggDesc = sorologiaElisaResultadoIggDesc;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaElisaResultadoIggDesc", sorologiaElisaResultadoIggDescOld, sorologiaElisaResultadoIggDesc);
	}



	/**
	 * Return the value associated with the column: sorologia_western_blot
	 */
	public java.lang.Long getSorologiaWesternBlot () {
		return getPropertyValue(this, sorologiaWesternBlot, PROP_SOROLOGIA_WESTERN_BLOT); 
	}

	/**
	 * Set the value related to the column: sorologia_western_blot
	 * @param sorologiaWesternBlot the sorologia_western_blot value
	 */
	public void setSorologiaWesternBlot (java.lang.Long sorologiaWesternBlot) {
//        java.lang.Long sorologiaWesternBlotOld = this.sorologiaWesternBlot;
		this.sorologiaWesternBlot = sorologiaWesternBlot;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaWesternBlot", sorologiaWesternBlotOld, sorologiaWesternBlot);
	}



	/**
	 * Return the value associated with the column: sorologia_western_blot_dt
	 */
	public java.util.Date getSorologiaWesternBlotData () {
		return getPropertyValue(this, sorologiaWesternBlotData, PROP_SOROLOGIA_WESTERN_BLOT_DATA); 
	}

	/**
	 * Set the value related to the column: sorologia_western_blot_dt
	 * @param sorologiaWesternBlotData the sorologia_western_blot_dt value
	 */
	public void setSorologiaWesternBlotData (java.util.Date sorologiaWesternBlotData) {
//        java.util.Date sorologiaWesternBlotDataOld = this.sorologiaWesternBlotData;
		this.sorologiaWesternBlotData = sorologiaWesternBlotData;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaWesternBlotData", sorologiaWesternBlotDataOld, sorologiaWesternBlotData);
	}



	/**
	 * Return the value associated with the column: sorologia_western_blot_resultado_igm
	 */
	public java.lang.Long getSorologiaWesternBlotResultadoIgm () {
		return getPropertyValue(this, sorologiaWesternBlotResultadoIgm, PROP_SOROLOGIA_WESTERN_BLOT_RESULTADO_IGM); 
	}

	/**
	 * Set the value related to the column: sorologia_western_blot_resultado_igm
	 * @param sorologiaWesternBlotResultadoIgm the sorologia_western_blot_resultado_igm value
	 */
	public void setSorologiaWesternBlotResultadoIgm (java.lang.Long sorologiaWesternBlotResultadoIgm) {
//        java.lang.Long sorologiaWesternBlotResultadoIgmOld = this.sorologiaWesternBlotResultadoIgm;
		this.sorologiaWesternBlotResultadoIgm = sorologiaWesternBlotResultadoIgm;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaWesternBlotResultadoIgm", sorologiaWesternBlotResultadoIgmOld, sorologiaWesternBlotResultadoIgm);
	}



	/**
	 * Return the value associated with the column: sorologia_western_blot_resultado_igm_desc
	 */
	public java.lang.String getSorologiaWesternBlotResultadoIgmDesc () {
		return getPropertyValue(this, sorologiaWesternBlotResultadoIgmDesc, PROP_SOROLOGIA_WESTERN_BLOT_RESULTADO_IGM_DESC); 
	}

	/**
	 * Set the value related to the column: sorologia_western_blot_resultado_igm_desc
	 * @param sorologiaWesternBlotResultadoIgmDesc the sorologia_western_blot_resultado_igm_desc value
	 */
	public void setSorologiaWesternBlotResultadoIgmDesc (java.lang.String sorologiaWesternBlotResultadoIgmDesc) {
//        java.lang.String sorologiaWesternBlotResultadoIgmDescOld = this.sorologiaWesternBlotResultadoIgmDesc;
		this.sorologiaWesternBlotResultadoIgmDesc = sorologiaWesternBlotResultadoIgmDesc;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaWesternBlotResultadoIgmDesc", sorologiaWesternBlotResultadoIgmDescOld, sorologiaWesternBlotResultadoIgmDesc);
	}



	/**
	 * Return the value associated with the column: sorologia_western_blot_resultado_igg
	 */
	public java.lang.Long getSorologiaWesternBlotIgg () {
		return getPropertyValue(this, sorologiaWesternBlotIgg, PROP_SOROLOGIA_WESTERN_BLOT_IGG); 
	}

	/**
	 * Set the value related to the column: sorologia_western_blot_resultado_igg
	 * @param sorologiaWesternBlotIgg the sorologia_western_blot_resultado_igg value
	 */
	public void setSorologiaWesternBlotIgg (java.lang.Long sorologiaWesternBlotIgg) {
//        java.lang.Long sorologiaWesternBlotIggOld = this.sorologiaWesternBlotIgg;
		this.sorologiaWesternBlotIgg = sorologiaWesternBlotIgg;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaWesternBlotIgg", sorologiaWesternBlotIggOld, sorologiaWesternBlotIgg);
	}



	/**
	 * Return the value associated with the column: sorologia_western_blot_resultado_igg_desc
	 */
	public java.lang.String getSorologiaWesternBlotIggDesc () {
		return getPropertyValue(this, sorologiaWesternBlotIggDesc, PROP_SOROLOGIA_WESTERN_BLOT_IGG_DESC); 
	}

	/**
	 * Set the value related to the column: sorologia_western_blot_resultado_igg_desc
	 * @param sorologiaWesternBlotIggDesc the sorologia_western_blot_resultado_igg_desc value
	 */
	public void setSorologiaWesternBlotIggDesc (java.lang.String sorologiaWesternBlotIggDesc) {
//        java.lang.String sorologiaWesternBlotIggDescOld = this.sorologiaWesternBlotIggDesc;
		this.sorologiaWesternBlotIggDesc = sorologiaWesternBlotIggDesc;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaWesternBlotIggDesc", sorologiaWesternBlotIggDescOld, sorologiaWesternBlotIggDesc);
	}



	/**
	 * Return the value associated with the column: caso_autoctone
	 */
	public java.lang.Long getCasoAutoctone () {
		return getPropertyValue(this, casoAutoctone, PROP_CASO_AUTOCTONE); 
	}

	/**
	 * Set the value related to the column: caso_autoctone
	 * @param casoAutoctone the caso_autoctone value
	 */
	public void setCasoAutoctone (java.lang.Long casoAutoctone) {
//        java.lang.Long casoAutoctoneOld = this.casoAutoctone;
		this.casoAutoctone = casoAutoctone;
//        this.getPropertyChangeSupport().firePropertyChange ("casoAutoctone", casoAutoctoneOld, casoAutoctone);
	}



	/**
	 * Return the value associated with the column: str_distrito_infeccao
	 */
	public java.lang.String getDistritoLocalInfeccao () {
		return getPropertyValue(this, distritoLocalInfeccao, PROP_DISTRITO_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: str_distrito_infeccao
	 * @param distritoLocalInfeccao the str_distrito_infeccao value
	 */
	public void setDistritoLocalInfeccao (java.lang.String distritoLocalInfeccao) {
//        java.lang.String distritoLocalInfeccaoOld = this.distritoLocalInfeccao;
		this.distritoLocalInfeccao = distritoLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("distritoLocalInfeccao", distritoLocalInfeccaoOld, distritoLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: str_bairro_infeccao
	 */
	public java.lang.String getBairroLocalInfeccao () {
		return getPropertyValue(this, bairroLocalInfeccao, PROP_BAIRRO_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: str_bairro_infeccao
	 * @param bairroLocalInfeccao the str_bairro_infeccao value
	 */
	public void setBairroLocalInfeccao (java.lang.String bairroLocalInfeccao) {
//        java.lang.String bairroLocalInfeccaoOld = this.bairroLocalInfeccao;
		this.bairroLocalInfeccao = bairroLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("bairroLocalInfeccao", bairroLocalInfeccaoOld, bairroLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: local_provavel_infeccao
	 */
	public java.lang.Long getLocalProvavelInfeccao () {
		return getPropertyValue(this, localProvavelInfeccao, PROP_LOCAL_PROVAVEL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: local_provavel_infeccao
	 * @param localProvavelInfeccao the local_provavel_infeccao value
	 */
	public void setLocalProvavelInfeccao (java.lang.Long localProvavelInfeccao) {
//        java.lang.Long localProvavelInfeccaoOld = this.localProvavelInfeccao;
		this.localProvavelInfeccao = localProvavelInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("localProvavelInfeccao", localProvavelInfeccaoOld, localProvavelInfeccao);
	}



	/**
	 * Return the value associated with the column: ambiente_infeccao
	 */
	public java.lang.Long getAmbienteInfeccao () {
		return getPropertyValue(this, ambienteInfeccao, PROP_AMBIENTE_INFECCAO); 
	}

	/**
	 * Set the value related to the column: ambiente_infeccao
	 * @param ambienteInfeccao the ambiente_infeccao value
	 */
	public void setAmbienteInfeccao (java.lang.Long ambienteInfeccao) {
//        java.lang.Long ambienteInfeccaoOld = this.ambienteInfeccao;
		this.ambienteInfeccao = ambienteInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("ambienteInfeccao", ambienteInfeccaoOld, ambienteInfeccao);
	}



	/**
	 * Return the value associated with the column: doenca_relacionada_trabalho
	 */
	public java.lang.Long getDoencaRelacionadaTrabalho () {
		return getPropertyValue(this, doencaRelacionadaTrabalho, PROP_DOENCA_RELACIONADA_TRABALHO); 
	}

	/**
	 * Set the value related to the column: doenca_relacionada_trabalho
	 * @param doencaRelacionadaTrabalho the doenca_relacionada_trabalho value
	 */
	public void setDoencaRelacionadaTrabalho (java.lang.Long doencaRelacionadaTrabalho) {
//        java.lang.Long doencaRelacionadaTrabalhoOld = this.doencaRelacionadaTrabalho;
		this.doencaRelacionadaTrabalho = doencaRelacionadaTrabalho;
//        this.getPropertyChangeSupport().firePropertyChange ("doencaRelacionadaTrabalho", doencaRelacionadaTrabalhoOld, doencaRelacionadaTrabalho);
	}



	/**
	 * Return the value associated with the column: classificacao_final
	 */
	public java.lang.Long getClassificacaoFinal () {
		return getPropertyValue(this, classificacaoFinal, PROP_CLASSIFICACAO_FINAL); 
	}

	/**
	 * Set the value related to the column: classificacao_final
	 * @param classificacaoFinal the classificacao_final value
	 */
	public void setClassificacaoFinal (java.lang.Long classificacaoFinal) {
//        java.lang.Long classificacaoFinalOld = this.classificacaoFinal;
		this.classificacaoFinal = classificacaoFinal;
//        this.getPropertyChangeSupport().firePropertyChange ("classificacaoFinal", classificacaoFinalOld, classificacaoFinal);
	}



	/**
	 * Return the value associated with the column: criterio_confirmacao_descarte
	 */
	public java.lang.Long getCriterioConfirmacaoDescarte () {
		return getPropertyValue(this, criterioConfirmacaoDescarte, PROP_CRITERIO_CONFIRMACAO_DESCARTE); 
	}

	/**
	 * Set the value related to the column: criterio_confirmacao_descarte
	 * @param criterioConfirmacaoDescarte the criterio_confirmacao_descarte value
	 */
	public void setCriterioConfirmacaoDescarte (java.lang.Long criterioConfirmacaoDescarte) {
//        java.lang.Long criterioConfirmacaoDescarteOld = this.criterioConfirmacaoDescarte;
		this.criterioConfirmacaoDescarte = criterioConfirmacaoDescarte;
//        this.getPropertyChangeSupport().firePropertyChange ("criterioConfirmacaoDescarte", criterioConfirmacaoDescarteOld, criterioConfirmacaoDescarte);
	}



	/**
	 * Return the value associated with the column: evolucao_caso
	 */
	public java.lang.Long getEvolucaoCaso () {
		return getPropertyValue(this, evolucaoCaso, PROP_EVOLUCAO_CASO); 
	}

	/**
	 * Set the value related to the column: evolucao_caso
	 * @param evolucaoCaso the evolucao_caso value
	 */
	public void setEvolucaoCaso (java.lang.Long evolucaoCaso) {
//        java.lang.Long evolucaoCasoOld = this.evolucaoCaso;
		this.evolucaoCaso = evolucaoCaso;
//        this.getPropertyChangeSupport().firePropertyChange ("evolucaoCaso", evolucaoCasoOld, evolucaoCaso);
	}



	/**
	 * Return the value associated with the column: dt_obito
	 */
	public java.util.Date getDataObito () {
		return getPropertyValue(this, dataObito, PROP_DATA_OBITO); 
	}

	/**
	 * Set the value related to the column: dt_obito
	 * @param dataObito the dt_obito value
	 */
	public void setDataObito (java.util.Date dataObito) {
//        java.util.Date dataObitoOld = this.dataObito;
		this.dataObito = dataObito;
//        this.getPropertyChangeSupport().firePropertyChange ("dataObito", dataObitoOld, dataObito);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: dt_encerramento
	 */
	public java.util.Date getDataEncerramento () {
		return getPropertyValue(this, dataEncerramento, PROP_DATA_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_encerramento
	 * @param dataEncerramento the dt_encerramento value
	 */
	public void setDataEncerramento (java.util.Date dataEncerramento) {
//        java.util.Date dataEncerramentoOld = this.dataEncerramento;
		this.dataEncerramento = dataEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEncerramento", dataEncerramentoOld, dataEncerramento);
	}



	/**
	 * Return the value associated with the column: cd_registro_agravo
	 */
	public br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo getRegistroAgravo () {
		return getPropertyValue(this, registroAgravo, PROP_REGISTRO_AGRAVO); 
	}

	/**
	 * Set the value related to the column: cd_registro_agravo
	 * @param registroAgravo the cd_registro_agravo value
	 */
	public void setRegistroAgravo (br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo) {
//        br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravoOld = this.registroAgravo;
		this.registroAgravo = registroAgravo;
//        this.getPropertyChangeSupport().firePropertyChange ("registroAgravo", registroAgravoOld, registroAgravo);
	}



	/**
	 * Return the value associated with the column: ocupacao_cbo
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getOcupacaoCbo () {
		return getPropertyValue(this, ocupacaoCbo, PROP_OCUPACAO_CBO); 
	}

	/**
	 * Set the value related to the column: ocupacao_cbo
	 * @param ocupacaoCbo the ocupacao_cbo value
	 */
	public void setOcupacaoCbo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCboOld = this.ocupacaoCbo;
		this.ocupacaoCbo = ocupacaoCbo;
//        this.getPropertyChangeSupport().firePropertyChange ("ocupacaoCbo", ocupacaoCboOld, ocupacaoCbo);
	}



	/**
	 * Return the value associated with the column: unidade_hospital
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getHospital () {
		return getPropertyValue(this, hospital, PROP_HOSPITAL); 
	}

	/**
	 * Set the value related to the column: unidade_hospital
	 * @param hospital the unidade_hospital value
	 */
	public void setHospital (br.com.ksisolucoes.vo.basico.Empresa hospital) {
//        br.com.ksisolucoes.vo.basico.Empresa hospitalOld = this.hospital;
		this.hospital = hospital;
//        this.getPropertyChangeSupport().firePropertyChange ("hospital", hospitalOld, hospital);
	}



	/**
	 * Return the value associated with the column: cd_cidade_infeccao
	 */
	public br.com.ksisolucoes.vo.basico.Cidade getCidadeLocalInfeccao () {
		return getPropertyValue(this, cidadeLocalInfeccao, PROP_CIDADE_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: cd_cidade_infeccao
	 * @param cidadeLocalInfeccao the cd_cidade_infeccao value
	 */
	public void setCidadeLocalInfeccao (br.com.ksisolucoes.vo.basico.Cidade cidadeLocalInfeccao) {
//        br.com.ksisolucoes.vo.basico.Cidade cidadeLocalInfeccaoOld = this.cidadeLocalInfeccao;
		this.cidadeLocalInfeccao = cidadeLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("cidadeLocalInfeccao", cidadeLocalInfeccaoOld, cidadeLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: cd_pais_infeccao
	 */
	public br.com.ksisolucoes.vo.basico.Pais getPaisLocalInfeccao () {
		return getPropertyValue(this, paisLocalInfeccao, PROP_PAIS_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: cd_pais_infeccao
	 * @param paisLocalInfeccao the cd_pais_infeccao value
	 */
	public void setPaisLocalInfeccao (br.com.ksisolucoes.vo.basico.Pais paisLocalInfeccao) {
//        br.com.ksisolucoes.vo.basico.Pais paisLocalInfeccaoOld = this.paisLocalInfeccao;
		this.paisLocalInfeccao = paisLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("paisLocalInfeccao", paisLocalInfeccaoOld, paisLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: cd_usuario_encerramento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioEncerramento () {
		return getPropertyValue(this, usuarioEncerramento, PROP_USUARIO_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_encerramento
	 * @param usuarioEncerramento the cd_usuario_encerramento value
	 */
	public void setUsuarioEncerramento (br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramentoOld = this.usuarioEncerramento;
		this.usuarioEncerramento = usuarioEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioEncerramento", usuarioEncerramentoOld, usuarioEncerramento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaLyme)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaLyme investigacaoAgravoDoencaLyme = (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaLyme) obj;
			if (null == this.getCodigo() || null == investigacaoAgravoDoencaLyme.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravoDoencaLyme.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}