<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.basico"  >
    <class name="Parametro" table="parametro">
        <id
                column="cod_parametro"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> <version column="version" name="version" type="long" />
        <property
                name="dataAtualizacao"
                not-null="false"
                length="4"
                type="java.util.Date"
        >
            <column name="dt_atualizacao" sql-type="timestamp"/>
        </property>
        <property
                column="cont_min"
                length="1"
                name="flagControlaMinimo"
                type="string"
        />
        <property
                column="aprova_pic_diretoria"
                length="1"
                name="flagAprovaPedidoInternoCompraDiretoria"
                type="string"
        />
        <property
                column="aprova_ordem_de_compra"
                length="1"
                name="flagAprovaOrdemCompra"
                type="string"
        />
        <property
                column="utiliza_tab_preco_pro_cli"
                length="1"
                name="flagUtilizaTabelaPrecoProdutoCliente"
                type="string"
        />
        <property
                column="ds_imagem_login"
                length="350"
                name="caminhoImagemLogin"
                type="java.lang.String"
        />

        <property
                column="intervalo_validade_continuo"
                name="intervaloValidadeContinuo"
                type="java.lang.Long"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                name="tipoDocumentoEntrada"
        >
            <column name="cod_tip_doc_entrada" />
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                name="tipoDocumentoSaida"
        >
            <column name="cod_tip_doc_saida" />
        </many-to-one>

        <!--        <many-to-one
                    class="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                    name="tipoDocumentoTransferenciaEntrada"
                    >
                    <column name="cod_tip_doc_trans_ent" />
                </many-to-one>TipoDocumentoTransferenciaEntrada-->

        <many-to-one
                class="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                name="tipoDocumentoDispensacaoMedicamento"
        >
            <column name="cod_tip_doc_dispensacao" />
        </many-to-one>

        <!--        <many-to-one
                    class="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                    name="tipoDocumentoTransferenciaSaida"
                    >
                    <column name="cod_tip_doc_trans_sai" />
                </many-to-one>-->

        <many-to-one
                class="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                name="tipoDocumentoOperacaoEntrada"
        >
            <column name="cod_tip_doc_enc_ent" />
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                name="tipoDocumentoOperacaoSaida"
        >
            <column name="cod_tip_doc_enc_sai" />
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                name="tipoDocumentoNFEntrada"
        >
            <column name="cod_tip_doc_nf_ent" />
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                name="tipoDocumentoNFSaida"
        >
            <column name="cod_tip_doc_nf_sai" />
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                name="tipoDocumentoPedidoTransferenciaSaida"
        >
            <column name="cod_tip_doc_sai_ped_transf" />
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                name="tipoDocumentoPedidoTransferenciaEntrada"
        >
            <column name="cod_tip_doc_ent_ped_transf" />
        </many-to-one>

        <property
                column="aprova_pic_setor"
                length="1"
                name="flagAprovaPedidoInternoCompraSetor"
                type="string"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Pessoa"
                name="pessoa"
        >
            <column name="cod_pessoa" />
        </many-to-one>

        <!-- ___________________________________________ -->
        <property
                column="aprova_oc_dentro_do_limite"
                length="1"
                name="flagAprovaOrdemCompraDentroDoLimite"
                type="string"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.entradas.estoque.Produto"
                name="produto"
        >
            <column name="cod_pro" />
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                name="tipoDocumento"
        >
            <column name="cod_tip_doc" />
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Empresa"
                name="unidadePrincipal"
        >
            <column name="empresa_principal" />
        </many-to-one>

        <!-- Parametro de sistema -->
        <property
                column="valida_cpf_cnpj"
                length="1"
                name="validaCpfCnpj"
                type="java.lang.String"
        />
        <!-- ___________________________________________ -->
        <property
                name="rangeQuantidadePadrao"
                column="range_qtde_padrao"
                type="java.lang.Double"
                not-null="false"
        />
        <property
                name="caminhoLogoPrefeitura"
                column="caminho_logo_prefeitura"
                type="java.lang.String"
                not-null="false"
                length="255"
        />
        <property
                name="pressaoArterialBaixa"
                column="presao_art_baixa"
                type="java.lang.String"
                not-null="false"
                length="5"
        />
        <property
                name="pressaoArterialNormal"
                column="presao_art_normal"
                type="java.lang.String"
                not-null="false"
                length="5"
        />
        <property
                name="pressaoArterialAlta"
                column="presao_art_alta"
                type="java.lang.String"
                not-null="false"
                length="5"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                name="tipoDocumentoProntuarioEmergencia"
        >
            <column name="cod_tip_doc_pront_eme" />
        </many-to-one>

        <property
                column="dt_comp_procedimento"
                name="dataCompetenciaProcedimento"
                type="java.util.Date"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                name="tipoDocumentoBaixaEnfermagem"
        >
            <column name="cod_tip_doc_bx_enf" />
        </many-to-one>

        <property
                column="path_file_manager_web"
                name="pathFileManagerWeb"
                type="java.lang.String"
        />

        <property
                column="path_cadsus"
                name="pathCadsus"
                length="200"
                type="java.lang.String"
        />

        <property
                column="path_tab_corporativas"
                name="pathTabelasCorporativas"
                length="200"
                type="java.lang.String"
        />

        <property
                column="driver_base_cnes"
                name="driverBaseCnes"
                length="100"
                type="java.lang.String"
        />

        <property
                column="url_base_cnes"
                name="urlBaseCnes"
                length="100"
                type="java.lang.String"
        />

        <property
                column="usuario_base_cnes"
                name="usuarioBaseCnes"
                length="100"
                type="java.lang.String"
        />

        <property
                column="senha_base_cnes"
                name="senhaBaseCnes"
                length="100"
                type="java.lang.String"
        />
        <property
                column="smtp_servidor"
                name="smtpServidor"
                length="50"
                type="java.lang.String"
        />
        <property
                column="smtp_usuario"
                name="smtpUsuario"
                length="60"
                type="java.lang.String"
        />
        <property
                column="smtp_senha"
                name="smtpSenha"
                length="15"
                type="java.lang.String"
        />

        <property
                name="nomePrefeitura"
                column="nm_prefeitura"
                type="java.lang.String"
                length="200"
        />

        <property
                column="dt_export_cadsus"
                name="dataExportacaoCadsus"
                type="timestamp"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento"
                name="tipoDocumentoExtornoEnfermagem"
        >
            <column name="cod_tip_doc_ext_enf" />
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.entradas.estoque.SubGrupo"
                name="subGrupoHiperdia"
        >
            <column name="cod_sub_hip" />
            <column name="cod_gru_hip" />
        </many-to-one>

        <property
                column="sftp_usuario"
                name="sftpUsuario"
                type="java.lang.String"
        />

        <property
                column="sftp_senha"
                name="sftpSenha"
                type="java.lang.String"
        />

        <property
                column="sftp_servidor"
                name="sftpServidor"
                type="java.lang.String"
        />

        <property
                column="sftp_compressao"
                name="sftpCompressao"
                type="java.lang.String"
        />

        <property
                column="sftp_diretorio_raiz"
                name="sftpDiretorioRaiz"
                type="java.lang.String"
        />

        <property
                column="tempo_sessao"
                name="tempoSessao"
                type="java.lang.Long"
        />

        <property
                column="login_bloqueado"
                name="loginBloqueado"
                type="java.lang.Long"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo"
                name="logoDocumento"
        >
            <column name="cd_ger_arq_logo_doc" />
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo"
                name="logoLogin"
        >
            <column name="cd_ger_arq_logo_login" />
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo"
                name="logoEtiquetaEmbarque"
        >
            <column name="cd_ger_arq_logo_etq" />
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo"
                name="logoSistema"
        >
            <column name="cd_ger_arq_logo_sistema" />
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo"
                name="logoSistemaTelaLogin"
        >
            <column name="cd_ger_arq_logo_sistema_tela_login"/>
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo"
                name="logoFavicon"
        >
            <column name="cd_ger_arq_logo_favicon"/>
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo"
                name="logoListaPublica"
        >
            <column name="cd_ger_arq_logo_lista_publica" />
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo"
                name="unidadeLacen"
        >
            <column name="cd_ger_arq_unidade_lacen" />
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo"
                name="logoLacen"
        >
            <column name="cd_ger_arq_logo_lacen" />
        </many-to-one>

        <property
                column="keycloack_client_id"
                name="keycloackClientId"
                type="java.lang.String"
        />

        <property
                column="keycloack_client_secret"
                name="keycloackClientSecret"
                type="java.lang.String"
        />

        <property
                column="keycloack_public_key"
                name="keycloackPublicKey"
                type="java.lang.String"
        />

        <property
                column="limite_resultados_relatorios"
                name="limiteResultadosRelatorios"
                type="java.lang.Long"
        />

        <property
                column="google_recaptcha_key"
                name="googleReCaptchaKey"
                type="java.lang.String"
                length="255"
                not-null="false"
        />

    </class>
</hibernate-mapping>
