package br.com.ksisolucoes.vo.vigilancia.investigacao;

import br.com.ksisolucoes.enums.IEnum;

public class InvestigacaoAgravoCisticercoseEnum {

    public enum SimNaoIgnoradoEnum implements IEnum {
        SIM(1L, "Sim"),
        NAO(2L, "Não"),
        IGNORADO(9L, "Ignorado");

        private Long value;
        private String descricao;

        SimNaoIgnoradoEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static SimNaoIgnoradoEnum valueOf(Long value) {
            for (SimNaoIgnoradoEnum v : SimNaoIgnoradoEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }

        public static IEnum[] getSimNao() {
            IEnum[] arr = {SimNaoIgnoradoEnum.SIM, SimNaoIgnoradoEnum.NAO};
            return arr;
        }
    }

    public enum FormaEnfermidadeEnum implements IEnum {
        AGUDO(1L, "Agudo"),
        CRONICO(2L, "Crônico"),
        NAO_SE_APLICA(9L, "Não se Aplica");

        private Long value;
        private String descricao;

        FormaEnfermidadeEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static FormaEnfermidadeEnum valueOf(Long value) {
            for (FormaEnfermidadeEnum v : FormaEnfermidadeEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum EvolucaoEnum implements IEnum {
        CURA(1L, "Cura"),
        OBITO(2L, "Óbito"),
        EM_TRATAMENTO(9L, "Em tratamento"),
        IGNORADO(9L, "Ignorado");
        private Long value;
        private String descricao;

        EvolucaoEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static EvolucaoEnum valueOf(Long value) {
            for (EvolucaoEnum v : EvolucaoEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

}
