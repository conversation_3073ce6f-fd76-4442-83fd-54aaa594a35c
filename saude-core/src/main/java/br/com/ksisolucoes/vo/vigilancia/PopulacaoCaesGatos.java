package br.com.ksisolucoes.vo.vigilancia;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.base.BasePopulacaoCaesGatos;

import java.io.Serializable;

public class PopulacaoCaesGatos extends BasePopulacaoCaesGatos implements CodigoManager {
    private static final long serialVersionUID = 1L;

    public static final String SIM = RepositoryComponentDefault.SIM;
    public static final String NAO = RepositoryComponentDefault.NAO;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public PopulacaoCaesGatos () {
        super();
    }

    /**
     * Constructor for primary key
     */
    public PopulacaoCaesGatos (java.lang.Long codigo) {
        super(codigo);
    }

    @Override
    public Serializable getCodigoManager() {
        return null;
    }

    @Override
    public void setCodigoManager(Serializable key) {

    }

    public String getCodigoFamiliaFormatado() {
        return getCodigoFamiliaFormatado(getCodigoFamilia());
    }

    public String getCodigoFamiliaFormatado(Long valor) {
        if (valor == null) {
            return "";
        } else {
            return String.valueOf(valor);
        }
    }

    public String getCodigoMicroareaFormatado() {
        return getCodigoMicroareaFormatado(getCodigoMicroarea());
    }

    public String getCodigoMicroareaFormatado(Long valor) {
        if (valor == null) {
            return "";
        } else {
            return String.valueOf(valor);
        }
    }

    public String getSimNaoCasaCercadaFormatado() {
        return getSimNaoFormatado(getCasaCercada());
    }

    public String getSimNaoFormatado(String valor){

        if (SIM.equals(valor)) {
            return Bundle.getStringApplication("rotulo_sim");
        } else if (NAO.equals(valor)) {
            return Bundle.getStringApplication("rotulo_nao");
        } else {
            return null;
        }
    }
}
