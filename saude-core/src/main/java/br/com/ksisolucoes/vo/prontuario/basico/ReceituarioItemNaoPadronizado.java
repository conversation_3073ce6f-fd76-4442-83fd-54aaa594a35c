package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.ksisolucoes.util.Bundle;
import java.io.Serializable;

import br.com.ksisolucoes.vo.prontuario.basico.base.BaseReceituarioItemNaoPadronizado;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class ReceituarioItemNaoPadronizado extends BaseReceituarioItemNaoPadronizado implements CodigoManager {
	private static final long serialVersionUID = 1L;
        
    public enum AlternativaTerapeutica{

        USO_ANTERIOR_SEM_RESPOSTA_TERAPEUTICA(1L),
        REACAO_ADVERSA_USO_ANTERIOR(2L),
        MENOR_SEGURANCA(4L),
        MENOR_EFETIVIDADE(8L),
        MENOR_COMODIDADE_USUARIO(16L),
        OUTROS_MOTIVOS(32L);

        private Long value;

        private AlternativaTerapeutica(Long value) {
            this.value = value;
        }

        public Long value(){
            return value;
        }

        @Override
        public String toString() {
            if (USO_ANTERIOR_SEM_RESPOSTA_TERAPEUTICA.equals(this)) {
                return Bundle.getStringApplication("rotulo_uso_anterior_sem_resposta_terapeutica");
            } else if (REACAO_ADVERSA_USO_ANTERIOR.equals(this)) {
                return Bundle.getStringApplication("rotulo_reacao_adversa_em_uso_anterior");
            } else if (MENOR_SEGURANCA.equals(this)) {
                return Bundle.getStringApplication("rotulo_menor_seguranca");
            } else if (MENOR_EFETIVIDADE.equals(this)) {
                return Bundle.getStringApplication("rotulo_menor_efetividade");
            } else if (MENOR_COMODIDADE_USUARIO.equals(this)) {
                return Bundle.getStringApplication("rotulo_menor_comodidade_para_usuario");
            } else if (OUTROS_MOTIVOS.equals(this)) {
                return Bundle.getStringApplication("rotulo_outros_motivos");
            }
            return Bundle.getStringApplication("rotulo_desconhecido");
        }

    }    

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ReceituarioItemNaoPadronizado () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ReceituarioItemNaoPadronizado (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ReceituarioItemNaoPadronizado (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem receituarioItem,
		br.com.ksisolucoes.vo.cadsus.Profissional profissional,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		br.com.ksisolucoes.vo.prontuario.basico.Cid cid,
		java.lang.String patologia,
		java.lang.String medicamentoDcb,
		java.lang.String posologia,
		java.lang.String periodicidade,
		java.lang.String justificativa,
		java.lang.String justificativaMedicamentoGenerico,
		java.lang.Long quantidade,
		java.lang.Long flagMedicamentoSimilar,
		java.lang.Long flagAlternativaTerapeutica,
		java.lang.String concentracao) {

		super (
			codigo,
			receituarioItem,
			profissional,
			empresa,
			cid,
			patologia,
			medicamentoDcb,
			posologia,
			periodicidade,
			justificativa,
			justificativaMedicamentoGenerico,
			quantidade,
			flagMedicamentoSimilar,
			flagAlternativaTerapeutica,
			concentracao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}