package br.com.ksisolucoes.vo.outraunidade.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the paciente_atendido_outra_unidade table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="paciente_atendido_outra_unidade"
 */

public abstract class BasePacienteAtendidoOutraUnidade extends BaseRootVO implements Serializable {

	public static String REF = "PacienteAtendidoOutraUnidade";
	public static final String PROP_DATA_ENCERRAMENTO = "dataEncerramento";
	public static final String PROP_EMPRESA_PACIENTE = "empresaPaciente";
	public static final String PROP_ATENDIMENTO = "atendimento";
	public static final String PROP_JUSTIFICATIVA = "justificativa";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_SITUACAO = "situacao";
	public static final String PROP_USUARIO_ENCERRAMENTO = "usuarioEncerramento";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";


	// constructors
	public BasePacienteAtendidoOutraUnidade () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BasePacienteAtendidoOutraUnidade (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BasePacienteAtendidoOutraUnidade (
		java.lang.Long codigo,
		java.lang.Long situacao,
		java.lang.String justificativa,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setSituacao(situacao);
		this.setJustificativa(justificativa);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long situacao;
	private java.lang.String justificativa;
	private java.util.Date dataCadastro;
	private java.util.Date dataEncerramento;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento;
	private br.com.ksisolucoes.vo.basico.Empresa empresaPaciente;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_atend_outra_unid"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: situacao
	 */
	public java.lang.Long getSituacao () {
		return getPropertyValue(this, situacao, PROP_SITUACAO); 
	}

	/**
	 * Set the value related to the column: situacao
	 * @param situacao the situacao value
	 */
	public void setSituacao (java.lang.Long situacao) {
//        java.lang.Long situacaoOld = this.situacao;
		this.situacao = situacao;
//        this.getPropertyChangeSupport().firePropertyChange ("situacao", situacaoOld, situacao);
	}



	/**
	 * Return the value associated with the column: justificativa
	 */
	public java.lang.String getJustificativa () {
		return getPropertyValue(this, justificativa, PROP_JUSTIFICATIVA); 
	}

	/**
	 * Set the value related to the column: justificativa
	 * @param justificativa the justificativa value
	 */
	public void setJustificativa (java.lang.String justificativa) {
//        java.lang.String justificativaOld = this.justificativa;
		this.justificativa = justificativa;
//        this.getPropertyChangeSupport().firePropertyChange ("justificativa", justificativaOld, justificativa);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_encerramento
	 */
	public java.util.Date getDataEncerramento () {
		return getPropertyValue(this, dataEncerramento, PROP_DATA_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_encerramento
	 * @param dataEncerramento the dt_encerramento value
	 */
	public void setDataEncerramento (java.util.Date dataEncerramento) {
//        java.util.Date dataEncerramentoOld = this.dataEncerramento;
		this.dataEncerramento = dataEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEncerramento", dataEncerramentoOld, dataEncerramento);
	}



	/**
	 * Return the value associated with the column: nr_atendimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Atendimento getAtendimento () {
		return getPropertyValue(this, atendimento, PROP_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: nr_atendimento
	 * @param atendimento the nr_atendimento value
	 */
	public void setAtendimento (br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoOld = this.atendimento;
		this.atendimento = atendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimento", atendimentoOld, atendimento);
	}



	/**
	 * Return the value associated with the column: empresa_paciente
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresaPaciente () {
		return getPropertyValue(this, empresaPaciente, PROP_EMPRESA_PACIENTE); 
	}

	/**
	 * Set the value related to the column: empresa_paciente
	 * @param empresaPaciente the empresa_paciente value
	 */
	public void setEmpresaPaciente (br.com.ksisolucoes.vo.basico.Empresa empresaPaciente) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaPacienteOld = this.empresaPaciente;
		this.empresaPaciente = empresaPaciente;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaPaciente", empresaPacienteOld, empresaPaciente);
	}



	/**
	 * Return the value associated with the column: cd_usuario_encerramento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioEncerramento () {
		return getPropertyValue(this, usuarioEncerramento, PROP_USUARIO_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_encerramento
	 * @param usuarioEncerramento the cd_usuario_encerramento value
	 */
	public void setUsuarioEncerramento (br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramentoOld = this.usuarioEncerramento;
		this.usuarioEncerramento = usuarioEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioEncerramento", usuarioEncerramentoOld, usuarioEncerramento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.outraunidade.PacienteAtendidoOutraUnidade)) return false;
		else {
			br.com.ksisolucoes.vo.outraunidade.PacienteAtendidoOutraUnidade pacienteAtendidoOutraUnidade = (br.com.ksisolucoes.vo.outraunidade.PacienteAtendidoOutraUnidade) obj;
			if (null == this.getCodigo() || null == pacienteAtendidoOutraUnidade.getCodigo()) return false;
			else return (this.getCodigo().equals(pacienteAtendidoOutraUnidade.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}