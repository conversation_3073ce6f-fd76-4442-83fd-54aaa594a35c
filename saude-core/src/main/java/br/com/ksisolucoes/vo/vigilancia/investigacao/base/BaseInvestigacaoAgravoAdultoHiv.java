package br.com.ksisolucoes.vo.vigilancia.investigacao.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.basico.Estado;

import java.io.Serializable;


/**
 * This is an object that contains data related to the investigacao_agr_adulto_hiv table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agr_adulto_hiv"
 */

public abstract class BaseInvestigacaoAgravoAdultoHiv extends BaseRootVO implements Serializable {

    public static final String PROP_DATA_COLETA123 = "dataColeta123";
	public static final String PROP_CANDIDOSE_TRAQUEIA = "candidoseTraqueia";
	public static final String PROP_LEUCOENCEFALOPATIA = "leucoencefalopatia";
	public static final String PROP_CRIPTOSPORIDIOSE = "criptosporidiose";
	public static final String PROP_DOENCA_CHAGAS = "doencaChagas";
	public static final String PROP_DIARREIA1_MES = "diarreia1Mes";
	public static final String PROP_TRANSMISSAO_VERTICAL = "transmissaoVertical";
    public static final String PROP_DECLARACAO_OBITO = "declaracaoObito";
	public static final String PROP_HERPES_ZOSTER = "herpesZoster";
    public static final String PROP_DATA_OBITO = "dataObito";
	public static final String PROP_APOS_INVESTIGACAO_PN_DST = "aposInvestigacaoPnDst";
	public static final String PROP_SARCOMA_KAPOSI = "sarcomaKaposi";
	public static final String PROP_HISTOPLASMOSE = "histoplasmose";
    public static final String PROP_SALMONELOSE = "salmonelose";
	public static final String PROP_TRANSFUSAO_SANGUINEA = "transfusaoSanguinea";
	public static final String PROP_LINFOCITOS_T_CD4 = "linfocitosTCd4";
	public static final String PROP_CANDIDOSE_ORAL = "candidoseOral";
	public static final String PROP_DATA_COLETA_TRIAGEM = "dataColetaTriagem";
	public static final String PROP_LINFOMA_PRIMARIO = "linfomaPrimario";
	public static final String PROP_TESTE_CONFIRMATORIO = "testeConfirmatorio";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_ASTENIA = "astenia";
	public static final String PROP_TOSSE_PERSISTENTE = "tossePersistente";
	public static final String PROP_CRIPTOCOCOSE = "criptococose";
	public static final String PROP_DISTRITO = "distrito";
	public static final String PROP_DERMATITE_PERSISTENTE = "dermatitePersistente";
	public static final String PROP_HERPES_SIMPLES = "herpesSimples";
	public static final String PROP_MICOBACTERIOSE = "micobacteriose";
    public static final String PROP_EVOLUCAO_CASO = "evolucaoCaso";
	public static final String PROP_CAQUEIXA = "caqueixa";
	public static final String PROP_CANDIDOSE_ESOFAGO = "candidoseEsofago";
	public static final String PROP_DISFUNCAO_SISTEMA_CENTRAL = "disfuncaoSistemaCentral";
	public static final String PROP_SEXUAL = "sexual";
    public static final String PROP_UNIDADE_TRATAMENTO = "unidadeTratamento";
	public static final String PROP_TUBERCULOSE_PULMONAR = "tuberculosePulmonar";
	public static final String PROP_ANEMIA_LINFOPENIA = "anemiaLinfopenia";
	public static final String PROP_PNEUMONIA = "pneumonia";
	public static final String PROP_UNIDADE_OCORREU_TRANSFUSAO_ACIDENTE = "unidadeOcorreuTransfusaoAcidente";
	public static final String PROP_CITOMEGALOVIROSE = "citomegalovirose";
    public static final String PROP_MUNICIPIO_TRATAMENTO = "municipioTratamento";
	public static final String PROP_TOXOPLASMOSE = "toxoplasmose";
	public static final String PROP_CANCER_CERVICAL = "cancerCervical";
	public static final String PROP_TRATAMENTO_HEMOTRANSFUSAO_PARA_HEMOFILIA = "tratamentoHemotransfusaoParaHemofilia";
	public static final String PROP_TESTE_RAPIDO2 = "testeRapido2";
	public static final String PROP_TESTE_RAPIDO3 = "testeRapido3";
	public static final String PROP_LINFADENOPATIA = "linfadenopatia";
	public static final String PROP_TESTE_RAPIDO1 = "testeRapido1";
	public static final String PROP_FEBRE_MAIOR38 = "febreMaior38";
	public static final String PROP_OCUPACAO = "ocupacao";
	public static final String PROP_ACIDENTE_MATERIAL_BIOLOGICO = "acidenteMaterialBiologico";
	public static final String PROP_TUBERCULOSE_DISSEMINADA = "tuberculoseDisseminada";
	public static final String PROP_USO_DROGAS_INJETAVEIS = "usoDrogasInjetaveis";
	public static final String PROP_ZONA = "zona";
	public static final String PROP_TESTE_TRIAGEM = "testeTriagem";
	public static final String PROP_LINFOMA_NAO_HODGKIN = "linfomaNaoHodgkin";
	public static final String PROP_REGISTRO_AGRAVO = "registroAgravo";
	public static final String PROP_DATA_TRANSFUSAO_ACIDENTE = "dataTransfusaoAcidente";
	public static final String PROP_GEO_CAMPO1 = "geoCampo1";
	public static final String PROP_GEO_CAMPO2 = "geoCampo2";
	public static final String PROP_ISOSPORIDIOSE = "isosporidiose";
	public static final String PROP_DATA_COLETA_CONFIRMATORIO = "dataColetaConfirmatorio";
    public static final String PROP_MUNICIPIO_OCORREU_TRANSFUSAO_ACIDENTE = "municipioOcorreuTransfusaoAcidente";
	public static final String PROP_UF_TRATAMENTO = "ufTratamento";
	public static final String PROP_UF_OCORREU_TRANSFUSAO_ACIDENTE = "ufOcorreuTransfusaoAcidente";
	public static final String PROP_FLAG_INFORMACOES_COMPLEMENTARES = "flagInformacoesComplementares";
    public static String REF = "InvestigacaoAgravoAdultoHiv";

	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String distrito;
	private java.math.BigDecimal geoCampo1;
	private java.math.BigDecimal geoCampo2;
	private java.lang.Long zona;
	private java.lang.Long transmissaoVertical;
	private java.lang.Long sexual;
	private java.lang.Long usoDrogasInjetaveis;
	private java.lang.Long transfusaoSanguinea;
	private java.lang.Long tratamentoHemotransfusaoParaHemofilia;
	private java.lang.Long acidenteMaterialBiologico;
	private java.util.Date dataTransfusaoAcidente;
	private java.lang.Long aposInvestigacaoPnDst;
	private java.lang.Long testeTriagem;
	private java.lang.Long testeConfirmatorio;
	private java.lang.Long testeRapido1;
	private java.lang.Long testeRapido2;
	private java.lang.Long testeRapido3;
	private java.util.Date dataColetaTriagem;
	private java.util.Date dataColetaConfirmatorio;
    private java.util.Date dataObito;
	private java.lang.Long sarcomaKaposi;
	private java.lang.Long tuberculoseDisseminada;
	private java.lang.Long candidoseOral;
	private java.lang.Long tuberculosePulmonar;
	private java.lang.Long herpesZoster;
	private java.lang.Long disfuncaoSistemaCentral;
	private java.lang.Long diarreia1Mes;
	private java.lang.Long febreMaior38;
	private java.lang.Long caqueixa;
	private java.lang.Long astenia;
	private java.lang.Long dermatitePersistente;
	private java.lang.Long anemiaLinfopenia;
	private java.lang.Long tossePersistente;
	private java.lang.Long linfadenopatia;
	private java.lang.Long cancerCervical;
	private java.lang.Long candidoseEsofago;
	private java.lang.Long candidoseTraqueia;
	private java.lang.Long citomegalovirose;
	private java.lang.Long criptococose;
	private java.lang.Long criptosporidiose;
	private java.lang.Long herpesSimples;
	private java.lang.Long histoplasmose;
	private java.lang.Long isosporidiose;
	private java.lang.Long leucoencefalopatia;
	private java.lang.Long linfomaNaoHodgkin;
	private java.lang.Long linfomaPrimario;
	private java.lang.Long micobacteriose;
	private java.lang.Long pneumonia;
	private java.lang.Long doencaChagas;
	private java.util.Date dataColeta123;
	private java.lang.Long salmonelose;
	private java.lang.Long declaracaoObito;
	private java.lang.Long evolucaoCaso;
	private java.lang.Long toxoplasmose;
	private java.lang.Long linfocitosTCd4;
	private java.lang.String flagInformacoesComplementares;

    private RetornoValidacao retornoValidacao;

    // constructors
    public BaseInvestigacaoAgravoAdultoHiv() {
        initialize();
    }

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo;
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacao;
	private br.com.ksisolucoes.vo.basico.Estado ufOcorreuTransfusaoAcidente;
	private br.com.ksisolucoes.vo.basico.Cidade municipioOcorreuTransfusaoAcidente;
	private br.com.ksisolucoes.vo.basico.Empresa unidadeOcorreuTransfusaoAcidente;
	private br.com.ksisolucoes.vo.basico.Estado ufTratamento;
	private br.com.ksisolucoes.vo.basico.Cidade municipioTratamento;
	private br.com.ksisolucoes.vo.basico.Empresa unidadeTratamento;

	/**
	 * Constructor for primary key
	 */
    public BaseInvestigacaoAgravoAdultoHiv(java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}
	/**
	 * Constructor for required fields
	 */
    public BaseInvestigacaoAgravoAdultoHiv(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo) {

		this.setCodigo(codigo);
		this.setRegistroAgravo(registroAgravo);
		initialize();
	}

    protected void initialize() {
    }

	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_invest_agr_adulto_hiv"
     */
    public java.lang.Long getCodigo() {
        return getPropertyValue(this, codigo, "codigo");
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
    public void setCodigo(java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}

	/**
	 * Return the value associated with the column: distrito
	 */
    public java.lang.String getDistrito() {
		return getPropertyValue(this, distrito, PROP_DISTRITO);
	}

	/**
	 * Set the value related to the column: distrito
	 * @param distrito the distrito value
	 */
    public void setDistrito(java.lang.String distrito) {
//        java.lang.String distritoOld = this.distrito;
		this.distrito = distrito;
//        this.getPropertyChangeSupport().firePropertyChange ("distrito", distritoOld, distrito);
	}

	/**
	 * Set the value related to the column: geo_campo_1
	 * @param geoCampo1 the geo_campo_1 value
	 */
	public void setGeoCampo1 (java.math.BigDecimal geoCampo1) {
//        java.math.BigDecimal geoCampo1Old = this.geoCampo1;
		this.geoCampo1 = geoCampo1;
//        this.getPropertyChangeSupport().firePropertyChange ("geoCampo1", geoCampo1Old, geoCampo1);
	}

	/**
     * Return the value associated with the column: geo_campo_1
	 */
    public java.math.BigDecimal getGeoCampo1() {
        return getPropertyValue(this, geoCampo1, PROP_GEO_CAMPO1);
	}

	/**
	 * Set the value related to the column: geo_campo_2
	 * @param geoCampo2 the geo_campo_2 value
	 */
	public void setGeoCampo2 (java.math.BigDecimal geoCampo2) {
//        java.math.BigDecimal geoCampo2Old = this.geoCampo2;
		this.geoCampo2 = geoCampo2;
//        this.getPropertyChangeSupport().firePropertyChange ("geoCampo2", geoCampo2Old, geoCampo2);
	}

    /**
     * Return the value associated with the column: geo_campo_2
     */
    public java.math.BigDecimal getGeoCampo2() {
        return getPropertyValue(this, geoCampo2, PROP_GEO_CAMPO2);
    }

	/**
	 * Return the value associated with the column: zona
     */
    public java.lang.Long getZona () {
		return getPropertyValue(this, zona, PROP_ZONA);
	}

	/**
	 * Set the value related to the column: zona
	 * @param zona the zona value
     */
    public void setZona(java.lang.Long zona) {
//        java.lang.Long zonaOld = this.zona;
        this.zona = zona;
//        this.getPropertyChangeSupport().firePropertyChange ("zona", zonaOld, zona);
    }

    /**
     * Return the value associated with the column: transmissao_vertical
     */
    public java.lang.Long getTransmissaoVertical() {
        return getPropertyValue(this, transmissaoVertical, PROP_TRANSMISSAO_VERTICAL);
    }

	/**
	 * Set the value related to the column: transmissao_vertical
	 * @param transmissaoVertical the transmissao_vertical value
     */
    public void setTransmissaoVertical(java.lang.Long transmissaoVertical) {
//        java.lang.Long transmissaoVerticalOld = this.transmissaoVertical;
        this.transmissaoVertical = transmissaoVertical;
//        this.getPropertyChangeSupport().firePropertyChange ("transmissaoVertical", transmissaoVerticalOld, transmissaoVertical);
	}

	/**
	 * Return the value associated with the column: sexual
     */
    public java.lang.Long getSexual () {
		return getPropertyValue(this, sexual, PROP_SEXUAL);
	}

	/**
	 * Set the value related to the column: sexual
	 * @param sexual the sexual value
     */
    public void setSexual(java.lang.Long sexual) {
//        java.lang.Long sexualOld = this.sexual;
        this.sexual = sexual;
//        this.getPropertyChangeSupport().firePropertyChange ("sexual", sexualOld, sexual);
    }

    /**
     * Return the value associated with the column: uso_drogas_injetaveis
     */
    public java.lang.Long getUsoDrogasInjetaveis() {
        return getPropertyValue(this, usoDrogasInjetaveis, PROP_USO_DROGAS_INJETAVEIS);
    }

	/**
	 * Set the value related to the column: uso_drogas_injetaveis
	 * @param usoDrogasInjetaveis the uso_drogas_injetaveis value
	 */
	public void setUsoDrogasInjetaveis (java.lang.Long usoDrogasInjetaveis) {
//        java.lang.Long usoDrogasInjetaveisOld = this.usoDrogasInjetaveis;
		this.usoDrogasInjetaveis = usoDrogasInjetaveis;
//        this.getPropertyChangeSupport().firePropertyChange ("usoDrogasInjetaveis", usoDrogasInjetaveisOld, usoDrogasInjetaveis);
    }

    /**
     * Return the value associated with the column: transfusao_sanguinea
     */
    public java.lang.Long getTransfusaoSanguinea() {
        return getPropertyValue(this, transfusaoSanguinea, PROP_TRANSFUSAO_SANGUINEA);
	}

	/**
	 * Set the value related to the column: transfusao_sanguinea
	 * @param transfusaoSanguinea the transfusao_sanguinea value
	 */
	public void setTransfusaoSanguinea (java.lang.Long transfusaoSanguinea) {
//        java.lang.Long transfusaoSanguineaOld = this.transfusaoSanguinea;
		this.transfusaoSanguinea = transfusaoSanguinea;
//        this.getPropertyChangeSupport().firePropertyChange ("transfusaoSanguinea", transfusaoSanguineaOld, transfusaoSanguinea);
    }

    /**
     * Return the value associated with the column: tratamento_hemotransfusao_para_hemofilia
     */
    public java.lang.Long getTratamentoHemotransfusaoParaHemofilia() {
        return getPropertyValue(this, tratamentoHemotransfusaoParaHemofilia, PROP_TRATAMENTO_HEMOTRANSFUSAO_PARA_HEMOFILIA);
	}

	/**
	 * Set the value related to the column: tratamento_hemotransfusao_para_hemofilia
	 * @param tratamentoHemotransfusaoParaHemofilia the tratamento_hemotransfusao_para_hemofilia value
     */
    public void setTratamentoHemotransfusaoParaHemofilia(java.lang.Long tratamentoHemotransfusaoParaHemofilia) {
//        java.lang.Long tratamentoHemotransfusaoParaHemofiliaOld = this.tratamentoHemotransfusaoParaHemofilia;
        this.tratamentoHemotransfusaoParaHemofilia = tratamentoHemotransfusaoParaHemofilia;
//        this.getPropertyChangeSupport().firePropertyChange ("tratamentoHemotransfusaoParaHemofilia", tratamentoHemotransfusaoParaHemofiliaOld, tratamentoHemotransfusaoParaHemofilia);
	}

	/**
	 * Return the value associated with the column: acidente_material_biologico
     */
    public java.lang.Long getAcidenteMaterialBiologico () {
		return getPropertyValue(this, acidenteMaterialBiologico, PROP_ACIDENTE_MATERIAL_BIOLOGICO);
	}

	/**
	 * Set the value related to the column: acidente_material_biologico
	 * @param acidenteMaterialBiologico the acidente_material_biologico value
     */
    public void setAcidenteMaterialBiologico(java.lang.Long acidenteMaterialBiologico) {
//        java.lang.Long acidenteMaterialBiologicoOld = this.acidenteMaterialBiologico;
		this.acidenteMaterialBiologico = acidenteMaterialBiologico;
//        this.getPropertyChangeSupport().firePropertyChange ("acidenteMaterialBiologico", acidenteMaterialBiologicoOld, acidenteMaterialBiologico);
	}

	/**
	 * Return the value associated with the column: data_transfusao_acidente
     */
    public java.util.Date getDataTransfusaoAcidente () {
		return getPropertyValue(this, dataTransfusaoAcidente, PROP_DATA_TRANSFUSAO_ACIDENTE);
	}

	/**
	 * Set the value related to the column: data_transfusao_acidente
	 * @param dataTransfusaoAcidente the data_transfusao_acidente value
     */
    public void setDataTransfusaoAcidente(java.util.Date dataTransfusaoAcidente) {
//        java.util.Date dataTransfusaoAcidenteOld = this.dataTransfusaoAcidente;
		this.dataTransfusaoAcidente = dataTransfusaoAcidente;
//        this.getPropertyChangeSupport().firePropertyChange ("dataTransfusaoAcidente", dataTransfusaoAcidenteOld, dataTransfusaoAcidente);
	}

	/**
	 * Return the value associated with the column: apos_investigacao_pn_dst
     */
    public java.lang.Long getAposInvestigacaoPnDst () {
		return getPropertyValue(this, aposInvestigacaoPnDst, PROP_APOS_INVESTIGACAO_PN_DST);
	}

	/**
	 * Set the value related to the column: apos_investigacao_pn_dst
	 * @param aposInvestigacaoPnDst the apos_investigacao_pn_dst value
     */
    public void setAposInvestigacaoPnDst(java.lang.Long aposInvestigacaoPnDst) {
//        java.lang.Long aposInvestigacaoPnDstOld = this.aposInvestigacaoPnDst;
        this.aposInvestigacaoPnDst = aposInvestigacaoPnDst;
//        this.getPropertyChangeSupport().firePropertyChange ("aposInvestigacaoPnDst", aposInvestigacaoPnDstOld, aposInvestigacaoPnDst);
    }

    /**
     * Return the value associated with the column: teste_triagem
     */
    public java.lang.Long getTesteTriagem() {
        return getPropertyValue(this, testeTriagem, PROP_TESTE_TRIAGEM);
    }

	/**
	 * Set the value related to the column: teste_triagem
	 * @param testeTriagem the teste_triagem value
	 */
	public void setTesteTriagem (java.lang.Long testeTriagem) {
//        java.lang.Long testeTriagemOld = this.testeTriagem;
		this.testeTriagem = testeTriagem;
//        this.getPropertyChangeSupport().firePropertyChange ("testeTriagem", testeTriagemOld, testeTriagem);
    }

    /**
     * Return the value associated with the column: teste_confirmatorio
     */
    public java.lang.Long getTesteConfirmatorio() {
        return getPropertyValue(this, testeConfirmatorio, PROP_TESTE_CONFIRMATORIO);
	}

	/**
	 * Set the value related to the column: teste_confirmatorio
	 * @param testeConfirmatorio the teste_confirmatorio value
	 */
	public void setTesteConfirmatorio (java.lang.Long testeConfirmatorio) {
//        java.lang.Long testeConfirmatorioOld = this.testeConfirmatorio;
		this.testeConfirmatorio = testeConfirmatorio;
//        this.getPropertyChangeSupport().firePropertyChange ("testeConfirmatorio", testeConfirmatorioOld, testeConfirmatorio);
    }

    /**
     * Return the value associated with the column: teste_rapido1
     */
    public java.lang.Long getTesteRapido1() {
        return getPropertyValue(this, testeRapido1, PROP_TESTE_RAPIDO1);
	}

	/**
	 * Set the value related to the column: teste_rapido1
	 * @param testeRapido1 the teste_rapido1 value
	 */
	public void setTesteRapido1 (java.lang.Long testeRapido1) {
//        java.lang.Long testeRapido1Old = this.testeRapido1;
		this.testeRapido1 = testeRapido1;
//        this.getPropertyChangeSupport().firePropertyChange ("testeRapido1", testeRapido1Old, testeRapido1);
    }

    /**
     * Return the value associated with the column: teste_rapido2
     */
    public java.lang.Long getTesteRapido2() {
        return getPropertyValue(this, testeRapido2, PROP_TESTE_RAPIDO2);
	}

	/**
	 * Set the value related to the column: teste_rapido2
	 * @param testeRapido2 the teste_rapido2 value
	 */
	public void setTesteRapido2 (java.lang.Long testeRapido2) {
//        java.lang.Long testeRapido2Old = this.testeRapido2;
		this.testeRapido2 = testeRapido2;
//        this.getPropertyChangeSupport().firePropertyChange ("testeRapido2", testeRapido2Old, testeRapido2);
    }

    /**
     * Return the value associated with the column: teste_rapido3
     */
    public java.lang.Long getTesteRapido3() {
        return getPropertyValue(this, testeRapido3, PROP_TESTE_RAPIDO3);
	}

	/**
	 * Set the value related to the column: teste_rapido3
	 * @param testeRapido3 the teste_rapido3 value
	 */
	public void setTesteRapido3 (java.lang.Long testeRapido3) {
//        java.lang.Long testeRapido3Old = this.testeRapido3;
		this.testeRapido3 = testeRapido3;
//        this.getPropertyChangeSupport().firePropertyChange ("testeRapido3", testeRapido3Old, testeRapido3);
    }

    /**
     * Return the value associated with the column: data_coleta_triagem
     */
    public java.util.Date getDataColetaTriagem() {
        return getPropertyValue(this, dataColetaTriagem, PROP_DATA_COLETA_TRIAGEM);
	}

	/**
	 * Set the value related to the column: data_coleta_triagem
	 * @param dataColetaTriagem the data_coleta_triagem value
	 */
	public void setDataColetaTriagem (java.util.Date dataColetaTriagem) {
//        java.util.Date dataColetaTriagemOld = this.dataColetaTriagem;
		this.dataColetaTriagem = dataColetaTriagem;
//        this.getPropertyChangeSupport().firePropertyChange ("dataColetaTriagem", dataColetaTriagemOld, dataColetaTriagem);
    }

    /**
     * Return the value associated with the column: data_coleta_confirmatorio
     */
    public java.util.Date getDataColetaConfirmatorio() {
        return getPropertyValue(this, dataColetaConfirmatorio, PROP_DATA_COLETA_CONFIRMATORIO);
	}

	/**
	 * Set the value related to the column: data_coleta_confirmatorio
	 * @param dataColetaConfirmatorio the data_coleta_confirmatorio value
     */
    public void setDataColetaConfirmatorio (java.util.Date dataColetaConfirmatorio) {
//        java.util.Date dataColetaConfirmatorioOld = this.dataColetaConfirmatorio;
		this.dataColetaConfirmatorio = dataColetaConfirmatorio;
//        this.getPropertyChangeSupport().firePropertyChange ("dataColetaConfirmatorio", dataColetaConfirmatorioOld, dataColetaConfirmatorio);
    }

    /**
     * Return the value associated with the column: data_coleta_123
     */
    public java.util.Date getDataColeta123() {
        return getPropertyValue(this, dataColeta123, PROP_DATA_COLETA123);
    }

    /**
     * Set the value related to the column: data_coleta_123
     *
     * @param dataColeta123 the data_coleta_123 value
     */
    public void setDataColeta123(java.util.Date dataColeta123) {
//        java.util.Date dataColeta123Old = this.dataColeta123;
        this.dataColeta123 = dataColeta123;
//        this.getPropertyChangeSupport().firePropertyChange ("dataColeta123", dataColeta123Old, dataColeta123);
    }

    /**
     * Return the value associated with the column: sarcoma_kaposi
	 */
	public java.lang.Long getSarcomaKaposi () {
		return getPropertyValue(this, sarcomaKaposi, PROP_SARCOMA_KAPOSI);
	}

	/**
	 * Set the value related to the column: sarcoma_kaposi
     * @param sarcomaKaposi the sarcoma_kaposi value
	 */
    public void setSarcomaKaposi (java.lang.Long sarcomaKaposi) {
//        java.lang.Long sarcomaKaposiOld = this.sarcomaKaposi;
        this.sarcomaKaposi = sarcomaKaposi;
//        this.getPropertyChangeSupport().firePropertyChange ("sarcomaKaposi", sarcomaKaposiOld, sarcomaKaposi);
    }

    /**
     * Return the value associated with the column: tuberculose_disseminada
     */
    public java.lang.Long getTuberculoseDisseminada() {
        return getPropertyValue(this, tuberculoseDisseminada, PROP_TUBERCULOSE_DISSEMINADA);
    }

	/**
	 * Set the value related to the column: tuberculose_disseminada
     * @param tuberculoseDisseminada the tuberculose_disseminada value
	 */
	public void setTuberculoseDisseminada (java.lang.Long tuberculoseDisseminada) {
//        java.lang.Long tuberculoseDisseminadaOld = this.tuberculoseDisseminada;
		this.tuberculoseDisseminada = tuberculoseDisseminada;
//        this.getPropertyChangeSupport().firePropertyChange ("tuberculoseDisseminada", tuberculoseDisseminadaOld, tuberculoseDisseminada);
	}

	/**
	 * Return the value associated with the column: candidose_oral
	 */
	public java.lang.Long getCandidoseOral () {
		return getPropertyValue(this, candidoseOral, PROP_CANDIDOSE_ORAL);
	}

	/**
	 * Set the value related to the column: candidose_oral
     * @param candidoseOral the candidose_oral value
	 */
    public void setCandidoseOral (java.lang.Long candidoseOral) {
//        java.lang.Long candidoseOralOld = this.candidoseOral;
        this.candidoseOral = candidoseOral;
//        this.getPropertyChangeSupport().firePropertyChange ("candidoseOral", candidoseOralOld, candidoseOral);
    }

    /**
     * Return the value associated with the column: tuberculose_pulmonar
     */
    public java.lang.Long getTuberculosePulmonar () {
        return getPropertyValue(this, tuberculosePulmonar, PROP_TUBERCULOSE_PULMONAR);
    }

	/**
	 * Set the value related to the column: tuberculose_pulmonar
     * @param tuberculosePulmonar the tuberculose_pulmonar value
	 */
	public void setTuberculosePulmonar (java.lang.Long tuberculosePulmonar) {
//        java.lang.Long tuberculosePulmonarOld = this.tuberculosePulmonar;
		this.tuberculosePulmonar = tuberculosePulmonar;
//        this.getPropertyChangeSupport().firePropertyChange ("tuberculosePulmonar", tuberculosePulmonarOld, tuberculosePulmonar);
	}

	/**
	 * Return the value associated with the column: herpes_zoster
	 */
	public java.lang.Long getHerpesZoster () {
		return getPropertyValue(this, herpesZoster, PROP_HERPES_ZOSTER);
	}

	/**
     * Set the value related to the column: herpes_zoster
     * @param herpesZoster the herpes_zoster value
	 */
    public void setHerpesZoster (java.lang.Long herpesZoster) {
//        java.lang.Long herpesZosterOld = this.herpesZoster;
        this.herpesZoster = herpesZoster;
//        this.getPropertyChangeSupport().firePropertyChange ("herpesZoster", herpesZosterOld, herpesZoster);
    }

    /**
     * Return the value associated with the column: disfuncao_sistema_central
     */
    public java.lang.Long getDisfuncaoSistemaCentral () {
        return getPropertyValue(this, disfuncaoSistemaCentral, PROP_DISFUNCAO_SISTEMA_CENTRAL);
    }

	/**
	 * Set the value related to the column: disfuncao_sistema_central
     * @param disfuncaoSistemaCentral the disfuncao_sistema_central value
	 */
	public void setDisfuncaoSistemaCentral (java.lang.Long disfuncaoSistemaCentral) {
//        java.lang.Long disfuncaoSistemaCentralOld = this.disfuncaoSistemaCentral;
		this.disfuncaoSistemaCentral = disfuncaoSistemaCentral;
//        this.getPropertyChangeSupport().firePropertyChange ("disfuncaoSistemaCentral", disfuncaoSistemaCentralOld, disfuncaoSistemaCentral);
	}

	/**
	 * Return the value associated with the column: diarreia_1_mes
	 */
	public java.lang.Long getDiarreia1Mes () {
		return getPropertyValue(this, diarreia1Mes, PROP_DIARREIA1_MES);
	}

    /**
     * Set the value related to the column: diarreia_1_mes
     * @param diarreia1Mes the diarreia_1_mes value
	 */
	public void setDiarreia1Mes (java.lang.Long diarreia1Mes) {
//        java.lang.Long diarreia1MesOld = this.diarreia1Mes;
		this.diarreia1Mes = diarreia1Mes;
//        this.getPropertyChangeSupport().firePropertyChange ("diarreia1Mes", diarreia1MesOld, diarreia1Mes);
	}

	/**
	 * Return the value associated with the column: febre_maior_38
	 */
	public java.lang.Long getFebreMaior38 () {
		return getPropertyValue(this, febreMaior38, PROP_FEBRE_MAIOR38);
	}

    /**
     * Set the value related to the column: febre_maior_38
     * @param febreMaior38 the febre_maior_38 value
	 */
	public void setFebreMaior38 (java.lang.Long febreMaior38) {
//        java.lang.Long febreMaior38Old = this.febreMaior38;
		this.febreMaior38 = febreMaior38;
//        this.getPropertyChangeSupport().firePropertyChange ("febreMaior38", febreMaior38Old, febreMaior38);
	}

	/**
	 * Return the value associated with the column: caqueixa
	 */
	public java.lang.Long getCaqueixa () {
		return getPropertyValue(this, caqueixa, PROP_CAQUEIXA);
    }

    /**
     * Set the value related to the column: caqueixa
	 * @param caqueixa the caqueixa value
	 */
	public void setCaqueixa (java.lang.Long caqueixa) {
//        java.lang.Long caqueixaOld = this.caqueixa;
		this.caqueixa = caqueixa;
//        this.getPropertyChangeSupport().firePropertyChange ("caqueixa", caqueixaOld, caqueixa);
	}

	/**
	 * Return the value associated with the column: astenia
	 */
	public java.lang.Long getAstenia () {
        return getPropertyValue(this, astenia, PROP_ASTENIA);
    }

    /**
     * Set the value related to the column: astenia
	 * @param astenia the astenia value
	 */
    public void setAstenia (java.lang.Long astenia) {
//        java.lang.Long asteniaOld = this.astenia;
        this.astenia = astenia;
//        this.getPropertyChangeSupport().firePropertyChange ("astenia", asteniaOld, astenia);
    }

    /**
     * Return the value associated with the column: dermatite_persistente
     */
    public java.lang.Long getDermatitePersistente () {
        return getPropertyValue(this, dermatitePersistente, PROP_DERMATITE_PERSISTENTE);
    }

	/**
	 * Set the value related to the column: dermatite_persistente
     * @param dermatitePersistente the dermatite_persistente value
	 */
	public void setDermatitePersistente(java.lang.Long dermatitePersistente) {
//        java.lang.Long dermatitePersistenteOld = this.dermatitePersistente;
		this.dermatitePersistente = dermatitePersistente;
//        this.getPropertyChangeSupport().firePropertyChange ("dermatitePersistente", dermatitePersistenteOld, dermatitePersistente);
	}

	/**
	 * Return the value associated with the column: anemia_linfopenia
	 */
	public java.lang.Long getAnemiaLinfopenia () {
		return getPropertyValue(this, anemiaLinfopenia, PROP_ANEMIA_LINFOPENIA);
	}

	/**
     * Set the value related to the column: anemia_linfopenia
     * @param anemiaLinfopenia the anemia_linfopenia value
	 */
	public void setAnemiaLinfopenia (java.lang.Long anemiaLinfopenia) {
//        java.lang.Long anemiaLinfopeniaOld = this.anemiaLinfopenia;
		this.anemiaLinfopenia = anemiaLinfopenia;
//        this.getPropertyChangeSupport().firePropertyChange ("anemiaLinfopenia", anemiaLinfopeniaOld, anemiaLinfopenia);
	}

	/**
	 * Return the value associated with the column: tosse_persistente
	 */
	public java.lang.Long getTossePersistente () {
		return getPropertyValue(this, tossePersistente, PROP_TOSSE_PERSISTENTE);
	}

	/**
     * Set the value related to the column: tosse_persistente
     * @param tossePersistente the tosse_persistente value
	 */
	public void setTossePersistente (java.lang.Long tossePersistente) {
//        java.lang.Long tossePersistenteOld = this.tossePersistente;
		this.tossePersistente = tossePersistente;
//        this.getPropertyChangeSupport().firePropertyChange ("tossePersistente", tossePersistenteOld, tossePersistente);
	}

	/**
	 * Return the value associated with the column: linfadenopatia
	 */
	public java.lang.Long getLinfadenopatia () {
		return getPropertyValue(this, linfadenopatia, PROP_LINFADENOPATIA);
    }

    /**
     * Set the value related to the column: linfadenopatia
     * @param linfadenopatia the linfadenopatia value
	 */
	public void setLinfadenopatia (java.lang.Long linfadenopatia) {
//        java.lang.Long linfadenopatiaOld = this.linfadenopatia;
		this.linfadenopatia = linfadenopatia;
//        this.getPropertyChangeSupport().firePropertyChange ("linfadenopatia", linfadenopatiaOld, linfadenopatia);
	}

	/**
	 * Return the value associated with the column: cancer_cervical
	 */
	public java.lang.Long getCancerCervical () {
		return getPropertyValue(this, cancerCervical, PROP_CANCER_CERVICAL);
    }

    /**
     * Set the value related to the column: cancer_cervical
     * @param cancerCervical the cancer_cervical value
	 */
	public void setCancerCervical (java.lang.Long cancerCervical) {
//        java.lang.Long cancerCervicalOld = this.cancerCervical;
		this.cancerCervical = cancerCervical;
//        this.getPropertyChangeSupport().firePropertyChange ("cancerCervical", cancerCervicalOld, cancerCervical);
	}

	/**
	 * Return the value associated with the column: candidose_esofago
	 */
	public java.lang.Long getCandidoseEsofago () {
		return getPropertyValue(this, candidoseEsofago, PROP_CANDIDOSE_ESOFAGO);
	}

	/**
     * Set the value related to the column: candidose_esofago
     * @param candidoseEsofago the candidose_esofago value
	 */
	public void setCandidoseEsofago (java.lang.Long candidoseEsofago) {
//        java.lang.Long candidoseEsofagoOld = this.candidoseEsofago;
		this.candidoseEsofago = candidoseEsofago;
//        this.getPropertyChangeSupport().firePropertyChange ("candidoseEsofago", candidoseEsofagoOld, candidoseEsofago);
	}

	/**
	 * Return the value associated with the column: candidose_traqueia
	 */
	public java.lang.Long getCandidoseTraqueia () {
		return getPropertyValue(this, candidoseTraqueia, PROP_CANDIDOSE_TRAQUEIA);
	}

	/**
     * Set the value related to the column: candidose_traqueia
     * @param candidoseTraqueia the candidose_traqueia value
	 */
	public void setCandidoseTraqueia (java.lang.Long candidoseTraqueia) {
//        java.lang.Long candidoseTraqueiaOld = this.candidoseTraqueia;
		this.candidoseTraqueia = candidoseTraqueia;
//        this.getPropertyChangeSupport().firePropertyChange ("candidoseTraqueia", candidoseTraqueiaOld, candidoseTraqueia);
	}

	/**
	 * Return the value associated with the column: citomegalovirose
	 */
	public java.lang.Long getCitomegalovirose () {
		return getPropertyValue(this, citomegalovirose, PROP_CITOMEGALOVIROSE);
	}

    /**
     * Set the value related to the column: citomegalovirose
     * @param citomegalovirose the citomegalovirose value
	 */
	public void setCitomegalovirose (java.lang.Long citomegalovirose) {
//        java.lang.Long citomegaloviroseOld = this.citomegalovirose;
		this.citomegalovirose = citomegalovirose;
//        this.getPropertyChangeSupport().firePropertyChange ("citomegalovirose", citomegaloviroseOld, citomegalovirose);
	}

	/**
	 * Return the value associated with the column: criptococose
	 */
	public java.lang.Long getCriptococose () {
		return getPropertyValue(this, criptococose, PROP_CRIPTOCOCOSE);
    }

    /**
     * Set the value related to the column: criptococose
	 * @param criptococose the criptococose value
	 */
	public void setCriptococose (java.lang.Long criptococose) {
//        java.lang.Long criptococoseOld = this.criptococose;
		this.criptococose = criptococose;
//        this.getPropertyChangeSupport().firePropertyChange ("criptococose", criptococoseOld, criptococose);
	}

	/**
	 * Return the value associated with the column: criptosporidiose
	 */
	public java.lang.Long getCriptosporidiose () {
		return getPropertyValue(this, criptosporidiose, PROP_CRIPTOSPORIDIOSE);
	}

    /**
     * Set the value related to the column: criptosporidiose
     * @param criptosporidiose the criptosporidiose value
	 */
	public void setCriptosporidiose (java.lang.Long criptosporidiose) {
//        java.lang.Long criptosporidioseOld = this.criptosporidiose;
		this.criptosporidiose = criptosporidiose;
//        this.getPropertyChangeSupport().firePropertyChange ("criptosporidiose", criptosporidioseOld, criptosporidiose);
	}

	/**
	 * Return the value associated with the column: herpes_simples
	 */
	public java.lang.Long getHerpesSimples () {
		return getPropertyValue(this, herpesSimples, PROP_HERPES_SIMPLES);
    }

    /**
     * Set the value related to the column: herpes_simples
	 * @param herpesSimples the herpes_simples value
	 */
	public void setHerpesSimples (java.lang.Long herpesSimples) {
//        java.lang.Long herpesSimplesOld = this.herpesSimples;
		this.herpesSimples = herpesSimples;
//        this.getPropertyChangeSupport().firePropertyChange ("herpesSimples", herpesSimplesOld, herpesSimples);
	}

	/**
	 * Return the value associated with the column: histoplasmose
	 */
	public java.lang.Long getHistoplasmose () {
		return getPropertyValue(this, histoplasmose, PROP_HISTOPLASMOSE);
    }

    /**
     * Set the value related to the column: histoplasmose
	 * @param histoplasmose the histoplasmose value
	 */
	public void setHistoplasmose (java.lang.Long histoplasmose) {
//        java.lang.Long histoplasmoseOld = this.histoplasmose;
		this.histoplasmose = histoplasmose;
//        this.getPropertyChangeSupport().firePropertyChange ("histoplasmose", histoplasmoseOld, histoplasmose);
	}

	/**
	 * Return the value associated with the column: isosporidiose
	 */
	public java.lang.Long getIsosporidiose () {
		return getPropertyValue(this, isosporidiose, PROP_ISOSPORIDIOSE);
    }

    /**
     * Set the value related to the column: isosporidiose
	 * @param isosporidiose the isosporidiose value
	 */
	public void setIsosporidiose (java.lang.Long isosporidiose) {
//        java.lang.Long isosporidioseOld = this.isosporidiose;
		this.isosporidiose = isosporidiose;
//        this.getPropertyChangeSupport().firePropertyChange ("isosporidiose", isosporidioseOld, isosporidiose);
	}

	/**
	 * Return the value associated with the column: leucoencefalopatia
	 */
	public java.lang.Long getLeucoencefalopatia () {
		return getPropertyValue(this, leucoencefalopatia, PROP_LEUCOENCEFALOPATIA);
	}

	/**
     * Set the value related to the column: leucoencefalopatia
     * @param leucoencefalopatia the leucoencefalopatia value
	 */
	public void setLeucoencefalopatia (java.lang.Long leucoencefalopatia) {
//        java.lang.Long leucoencefalopatiaOld = this.leucoencefalopatia;
		this.leucoencefalopatia = leucoencefalopatia;
//        this.getPropertyChangeSupport().firePropertyChange ("leucoencefalopatia", leucoencefalopatiaOld, leucoencefalopatia);
	}

	/**
	 * Return the value associated with the column: linfoma_nao_hodgkin
	 */
	public java.lang.Long getLinfomaNaoHodgkin () {
		return getPropertyValue(this, linfomaNaoHodgkin, PROP_LINFOMA_NAO_HODGKIN);
	}

	/**
     * Set the value related to the column: linfoma_nao_hodgkin
     * @param linfomaNaoHodgkin the linfoma_nao_hodgkin value
	 */
	public void setLinfomaNaoHodgkin (java.lang.Long linfomaNaoHodgkin) {
//        java.lang.Long linfomaNaoHodgkinOld = this.linfomaNaoHodgkin;
		this.linfomaNaoHodgkin = linfomaNaoHodgkin;
//        this.getPropertyChangeSupport().firePropertyChange ("linfomaNaoHodgkin", linfomaNaoHodgkinOld, linfomaNaoHodgkin);
	}

	/**
	 * Return the value associated with the column: linfoma_primario
	 */
	public java.lang.Long getLinfomaPrimario () {
		return getPropertyValue(this, linfomaPrimario, PROP_LINFOMA_PRIMARIO);
	}

    /**
     * Set the value related to the column: linfoma_primario
     * @param linfomaPrimario the linfoma_primario value
	 */
	public void setLinfomaPrimario (java.lang.Long linfomaPrimario) {
//        java.lang.Long linfomaPrimarioOld = this.linfomaPrimario;
		this.linfomaPrimario = linfomaPrimario;
//        this.getPropertyChangeSupport().firePropertyChange ("linfomaPrimario", linfomaPrimarioOld, linfomaPrimario);
	}

	/**
	 * Return the value associated with the column: micobacteriose
	 */
	public java.lang.Long getMicobacteriose () {
		return getPropertyValue(this, micobacteriose, PROP_MICOBACTERIOSE);
    }

    /**
     * Set the value related to the column: micobacteriose
     * @param micobacteriose the micobacteriose value
	 */
	public void setMicobacteriose (java.lang.Long micobacteriose) {
//        java.lang.Long micobacterioseOld = this.micobacteriose;
		this.micobacteriose = micobacteriose;
//        this.getPropertyChangeSupport().firePropertyChange ("micobacteriose", micobacterioseOld, micobacteriose);
	}

	/**
	 * Return the value associated with the column: pneumonia
	 */
	public java.lang.Long getPneumonia () {
        return getPropertyValue(this, pneumonia, PROP_PNEUMONIA);
    }

    /**
     * Set the value related to the column: pneumonia
	 * @param pneumonia the pneumonia value
	 */
	public void setPneumonia (java.lang.Long pneumonia) {
//        java.lang.Long pneumoniaOld = this.pneumonia;
		this.pneumonia = pneumonia;
//        this.getPropertyChangeSupport().firePropertyChange ("pneumonia", pneumoniaOld, pneumonia);
	}

	/**
	 * Return the value associated with the column: doenca_chagas
	 */
	public java.lang.Long getDoencaChagas () {
		return getPropertyValue(this, doencaChagas, PROP_DOENCA_CHAGAS);
    }

    /**
     * Set the value related to the column: doenca_chagas
	 * @param doencaChagas the doenca_chagas value
	 */
	public void setDoencaChagas(java.lang.Long doencaChagas) {
//        java.lang.Long doencaChagasOld = this.doencaChagas;
		this.doencaChagas = doencaChagas;
//        this.getPropertyChangeSupport().firePropertyChange ("doencaChagas", doencaChagasOld, doencaChagas);
    }

    /**
     * Return the value associated with the column: salmonelose
     */
    public java.lang.Long getSalmonelose() {
        return getPropertyValue(this, salmonelose, PROP_SALMONELOSE);
    }

    /**
     * Set the value related to the column: salmonelose
	 * @param salmonelose the salmonelose value
     */
    public void setSalmonelose(java.lang.Long salmonelose) {
//        java.lang.Long salmoneloseOld = this.salmonelose;
		this.salmonelose = salmonelose;
//        this.getPropertyChangeSupport().firePropertyChange ("salmonelose", salmoneloseOld, salmonelose);
	}

	/**
	 * Return the value associated with the column: toxoplasmose
	 */
	public java.lang.Long getToxoplasmose () {
		return getPropertyValue(this, toxoplasmose, PROP_TOXOPLASMOSE);
	}

	/**
	 * Set the value related to the column: toxoplasmose
	 * @param toxoplasmose the toxoplasmose value
	 */
	public void setToxoplasmose (java.lang.Long toxoplasmose) {
//        java.lang.Long toxoplasmoseOld = this.toxoplasmose;
		this.toxoplasmose = toxoplasmose;
//        this.getPropertyChangeSupport().firePropertyChange ("toxoplasmose", toxoplasmoseOld, toxoplasmose);
	}

	/**
	 * Return the value associated with the column: linfocitos_t_cd4
	 */
	public java.lang.Long getLinfocitosTCd4 () {
		return getPropertyValue(this, linfocitosTCd4, PROP_LINFOCITOS_T_CD4);
    }

    /**
     * Set the value related to the column: linfocitos_t_cd4
	 * @param linfocitosTCd4 the linfocitos_t_cd4 value
	 */
	public void setLinfocitosTCd4(java.lang.Long linfocitosTCd4) {
//        java.lang.Long linfocitosTCd4Old = this.linfocitosTCd4;
		this.linfocitosTCd4 = linfocitosTCd4;
//        this.getPropertyChangeSupport().firePropertyChange ("linfocitosTCd4", linfocitosTCd4Old, linfocitosTCd4);
    }

    /**
     * Return the value associated with the column: declaracao_obito
     */
    public java.lang.Long getDeclaracaoObito() {
        return getPropertyValue(this, declaracaoObito, PROP_DECLARACAO_OBITO);
    }

    /**
     * Set the value related to the column: declaracao_obito
     * @param declaracaoObito the declaracao_obito value
	 */
	public void setDeclaracaoObito(java.lang.Long declaracaoObito) {
//        java.lang.Long declaracaoObitoOld = this.declaracaoObito;
		this.declaracaoObito = declaracaoObito;
//        this.getPropertyChangeSupport().firePropertyChange ("declaracaoObito", declaracaoObitoOld, declaracaoObito);
    }

    /**
     * Return the value associated with the column: evolucao_caso
     */
    public java.lang.Long getEvolucaoCaso() {
        return getPropertyValue(this, evolucaoCaso, PROP_EVOLUCAO_CASO);
    }

    /**
     * Set the value related to the column: evolucao_caso
	 * @param evolucaoCaso the evolucao_caso value
     */
    public void setEvolucaoCaso (java.lang.Long evolucaoCaso) {
//        java.lang.Long evolucaoCasoOld = this.evolucaoCaso;
		this.evolucaoCaso = evolucaoCaso;
//        this.getPropertyChangeSupport().firePropertyChange ("evolucaoCaso", evolucaoCasoOld, evolucaoCaso);
    }

    /**
     * Return the value associated with the column: data_obito
     */
    public java.util.Date getDataObito() {
        return getPropertyValue(this, dataObito, PROP_DATA_OBITO);
    }

    /**
     * Set the value related to the column: data_obito
	 * @param dataObito the data_obito value
     */
	public void setDataObito (java.util.Date dataObito) {
//        java.util.Date dataObitoOld = this.dataObito;
		this.dataObito = dataObito;
//        this.getPropertyChangeSupport().firePropertyChange ("dataObito", dataObitoOld, dataObito);
	}

	/**
	 * Return the value associated with the column: cd_registro_agravo
	 */
	public br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo getRegistroAgravo() {
        return getPropertyValue(this, registroAgravo, PROP_REGISTRO_AGRAVO);
	}

	/**
	 * Set the value related to the column: cd_registro_agravo
	 * @param registroAgravo the cd_registro_agravo value
	 */
	public void setRegistroAgravo (br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo) {
//        br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravoOld = this.registroAgravo;
		this.registroAgravo = registroAgravo;
//        this.getPropertyChangeSupport().firePropertyChange ("registroAgravo", registroAgravoOld, registroAgravo);
	}

	/**
	 * Return the value associated with the column: ocupacao
     */
    public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getOcupacao() {
        return getPropertyValue(this, ocupacao, PROP_OCUPACAO);
	}

	/**
	 * Set the value related to the column: ocupacao
	 * @param ocupacao the ocupacao value
	 */
    public void setOcupacao (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacao) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoOld = this.ocupacao;
        this.ocupacao = ocupacao;
//        this.getPropertyChangeSupport().firePropertyChange ("ocupacao", ocupacaoOld, ocupacao);
    }

	/**
	 * Return the value associated with the column: cd_uf_ocorreu_transfusao_acidente
	 */
	public Estado getUfOcorreuTransfusaoAcidente() {
		return getPropertyValue(this, ufOcorreuTransfusaoAcidente, PROP_UF_OCORREU_TRANSFUSAO_ACIDENTE);
	}

	/**
	 * Set the value related to the column: cd_uf_ocorreu_transfusao_acidente
	 * @param ufOcorreuTransfusaoAcidente the cd_uf_ocorreu_transfusao_acidente value
	 */
	public void setUfOcorreuTransfusaoAcidente(Estado ufOcorreuTransfusaoAcidente) {
		this.ufOcorreuTransfusaoAcidente = ufOcorreuTransfusaoAcidente;
	}

    /**
     * Return the value associated with the column: cd_municipio_ocorreu_transfusao_acidente
     */
    public br.com.ksisolucoes.vo.basico.Cidade getMunicipioOcorreuTransfusaoAcidente () {
        return getPropertyValue(this, municipioOcorreuTransfusaoAcidente, PROP_MUNICIPIO_OCORREU_TRANSFUSAO_ACIDENTE);
    }

	/**
	 * Set the value related to the column: cd_municipio_ocorreu_transfusao_acidente
	 * @param municipioOcorreuTransfusaoAcidente the cd_municipio_ocorreu_transfusao_acidente value
	 */
	public void setMunicipioOcorreuTransfusaoAcidente (br.com.ksisolucoes.vo.basico.Cidade municipioOcorreuTransfusaoAcidente) {
//        br.com.ksisolucoes.vo.basico.Cidade municipioOcorreuTransfusaoAcidenteOld = this.municipioOcorreuTransfusaoAcidente;
		this.municipioOcorreuTransfusaoAcidente = municipioOcorreuTransfusaoAcidente;
//        this.getPropertyChangeSupport().firePropertyChange ("municipioOcorreuTransfusaoAcidente", municipioOcorreuTransfusaoAcidenteOld, municipioOcorreuTransfusaoAcidente);
    }

    /**
     * Return the value associated with the column: cd_unidade_ocorreu_transfusao_acidente
     */
    public br.com.ksisolucoes.vo.basico.Empresa getUnidadeOcorreuTransfusaoAcidente () {
		return getPropertyValue(this, unidadeOcorreuTransfusaoAcidente, PROP_UNIDADE_OCORREU_TRANSFUSAO_ACIDENTE);
	}

	/**
	 * Set the value related to the column: cd_unidade_ocorreu_transfusao_acidente
	 * @param unidadeOcorreuTransfusaoAcidente the cd_unidade_ocorreu_transfusao_acidente value
	 */
	public void setUnidadeOcorreuTransfusaoAcidente (br.com.ksisolucoes.vo.basico.Empresa unidadeOcorreuTransfusaoAcidente) {
//        br.com.ksisolucoes.vo.basico.Empresa unidadeOcorreuTransfusaoAcidenteOld = this.unidadeOcorreuTransfusaoAcidente;
		this.unidadeOcorreuTransfusaoAcidente = unidadeOcorreuTransfusaoAcidente;
//        this.getPropertyChangeSupport().firePropertyChange ("unidadeOcorreuTransfusaoAcidente", unidadeOcorreuTransfusaoAcidenteOld, unidadeOcorreuTransfusaoAcidente);
    }

	/**
	 * Return the value associated with the column: cd_uf_tratamento
	 */
	public Estado getUfTratamento() {
		return getPropertyValue(this, ufTratamento, PROP_UF_TRATAMENTO);
	}

	/**
	 * Set the value related to the column: cd_uf_tratamento
	 * @param ufTratamento the cd_uf_tratamento value
	 */
	public void setUfTratamento(Estado ufTratamento) {
		this.ufTratamento = ufTratamento;
	}

    /**
     * Return the value associated with the column: cd_municipio_tratamento
     */
    public br.com.ksisolucoes.vo.basico.Cidade getMunicipioTratamento() {
        return getPropertyValue(this, municipioTratamento, PROP_MUNICIPIO_TRATAMENTO);
    }

    /**
     * Set the value related to the column: cd_municipio_tratamento
     * @param municipioTratamento the cd_municipio_tratamento value
	 */
	public void setMunicipioTratamento(br.com.ksisolucoes.vo.basico.Cidade municipioTratamento) {
//        br.com.ksisolucoes.vo.basico.Cidade municipioTratamentoOld = this.municipioTratamento;
		this.municipioTratamento = municipioTratamento;
//        this.getPropertyChangeSupport().firePropertyChange ("municipioTratamento", municipioTratamentoOld, municipioTratamento);
    }

    /**
     * Return the value associated with the column: cd_unidade_tratamento
     */
    public br.com.ksisolucoes.vo.basico.Empresa getUnidadeTratamento() {
        return getPropertyValue(this, unidadeTratamento, PROP_UNIDADE_TRATAMENTO);
    }

    /**
     * Set the value related to the column: cd_unidade_tratamento
	 * @param unidadeTratamento the cd_unidade_tratamento value
	 */
	public void setUnidadeTratamento(br.com.ksisolucoes.vo.basico.Empresa unidadeTratamento) {
//        br.com.ksisolucoes.vo.basico.Empresa unidadeTratamentoOld = this.unidadeTratamento;
		this.unidadeTratamento = unidadeTratamento;
//        this.getPropertyChangeSupport().firePropertyChange ("unidadeTratamento", unidadeTratamentoOld, unidadeTratamento);
    }


	/**
	 * Return the value associated with the column: flag_informacoes_complementares
	 */
	public java.lang.String getFlagInformacoesComplementares () {
		return getPropertyValue(this, flagInformacoesComplementares, PROP_FLAG_INFORMACOES_COMPLEMENTARES);
	}

	/**
	 * Set the value related to the column: flag_informacoes_complementares
	 * @param flagInformacoesComplementares the flag_informacoes_complementares value
	 */
	public void setFlagInformacoesComplementares (java.lang.String flagInformacoesComplementares) {
//        java.lang.Long flagInformacoesComplementares = this.flagInformacoesComplementares;
		this.flagInformacoesComplementares = flagInformacoesComplementares;
//        this.getPropertyChangeSupport().firePropertyChange ("flagInformacoesComplementares", flagInformacoesComplementaresoOld, flagInformacoesComplementares);
	}



    public boolean equals(Object obj) {
        if (null == obj) return false;
        if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoAdultoHiv)) return false;
        else {
            br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoAdultoHiv investigacaoAgravoAdultoHiv = (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoAdultoHiv) obj;
            if (null == this.getCodigo() || null == investigacaoAgravoAdultoHiv.getCodigo()) return false;
            else return (this.getCodigo().equals(investigacaoAgravoAdultoHiv.getCodigo()));
        }
    }

    public int hashCode() {
        if (Integer.MIN_VALUE == this.hashCode) {
            if (null == this.getCodigo()) return super.hashCode();
            else {
                String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}

	public String toString () {
		return super.toString();
	}

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}