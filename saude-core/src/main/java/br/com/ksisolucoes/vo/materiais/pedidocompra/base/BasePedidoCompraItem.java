package br.com.ksisolucoes.vo.materiais.pedidocompra.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the pedido_compra_item table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="pedido_compra_item"
 */

public abstract class BasePedidoCompraItem extends BaseRootVO implements Serializable {

	public static String REF = "PedidoCompraItem";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_QUANTIDADE_RECEBIDA = "quantidadeRecebida";
	public static final String PROP_STATUS = "status";
	public static final String PROP_PEDIDO_COMPRA = "pedidoCompra";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_QUANTIDADE_ITEM = "quantidadeItem";
	public static final String PROP_DATA_USUARIO = "dataUsuario";
	public static final String PROP_PRODUTO = "produto";
	public static final String PROP_QUANTIDADE_CANCELADA = "quantidadeCancelada";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";


	// constructors
	public BasePedidoCompraItem () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BasePedidoCompraItem (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BasePedidoCompraItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.materiais.pedidocompra.PedidoCompra pedidoCompra,
		br.com.ksisolucoes.vo.entradas.estoque.Produto produto,
		java.lang.Long status,
		java.util.Date dataUsuario) {

		this.setCodigo(codigo);
		this.setPedidoCompra(pedidoCompra);
		this.setProduto(produto);
		this.setStatus(status);
		this.setDataUsuario(dataUsuario);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.math.BigDecimal quantidadeItem;
	private java.lang.String observacao;
	private java.lang.Long status;
	private java.math.BigDecimal quantidadeRecebida;
	private java.util.Date dataUsuario;
	private java.util.Date dataCancelamento;
	private java.math.BigDecimal quantidadeCancelada;

	// many to one
	private br.com.ksisolucoes.vo.materiais.pedidocompra.PedidoCompra pedidoCompra;
	private br.com.ksisolucoes.vo.entradas.estoque.Produto produto;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_pedido_compra_item"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: qtd_item
	 */
	public java.math.BigDecimal getQuantidadeItem () {
		return getPropertyValue(this, quantidadeItem, PROP_QUANTIDADE_ITEM); 
	}

	/**
	 * Set the value related to the column: qtd_item
	 * @param quantidadeItem the qtd_item value
	 */
	public void setQuantidadeItem (java.math.BigDecimal quantidadeItem) {
//        java.math.BigDecimal quantidadeItemOld = this.quantidadeItem;
		this.quantidadeItem = quantidadeItem;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeItem", quantidadeItemOld, quantidadeItem);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: qtd_recebida
	 */
	public java.math.BigDecimal getQuantidadeRecebida () {
		return getPropertyValue(this, quantidadeRecebida, PROP_QUANTIDADE_RECEBIDA); 
	}

	/**
	 * Set the value related to the column: qtd_recebida
	 * @param quantidadeRecebida the qtd_recebida value
	 */
	public void setQuantidadeRecebida (java.math.BigDecimal quantidadeRecebida) {
//        java.math.BigDecimal quantidadeRecebidaOld = this.quantidadeRecebida;
		this.quantidadeRecebida = quantidadeRecebida;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeRecebida", quantidadeRecebidaOld, quantidadeRecebida);
	}



	/**
	 * Return the value associated with the column: dt_usuario
	 */
	public java.util.Date getDataUsuario () {
		return getPropertyValue(this, dataUsuario, PROP_DATA_USUARIO); 
	}

	/**
	 * Set the value related to the column: dt_usuario
	 * @param dataUsuario the dt_usuario value
	 */
	public void setDataUsuario (java.util.Date dataUsuario) {
//        java.util.Date dataUsuarioOld = this.dataUsuario;
		this.dataUsuario = dataUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsuario", dataUsuarioOld, dataUsuario);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: qtd_cancelada
	 */
	public java.math.BigDecimal getQuantidadeCancelada () {
		return getPropertyValue(this, quantidadeCancelada, PROP_QUANTIDADE_CANCELADA); 
	}

	/**
	 * Set the value related to the column: qtd_cancelada
	 * @param quantidadeCancelada the qtd_cancelada value
	 */
	public void setQuantidadeCancelada (java.math.BigDecimal quantidadeCancelada) {
//        java.math.BigDecimal quantidadeCanceladaOld = this.quantidadeCancelada;
		this.quantidadeCancelada = quantidadeCancelada;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeCancelada", quantidadeCanceladaOld, quantidadeCancelada);
	}



	/**
	 * Return the value associated with the column: cd_pedido_compra
	 */
	public br.com.ksisolucoes.vo.materiais.pedidocompra.PedidoCompra getPedidoCompra () {
		return getPropertyValue(this, pedidoCompra, PROP_PEDIDO_COMPRA); 
	}

	/**
	 * Set the value related to the column: cd_pedido_compra
	 * @param pedidoCompra the cd_pedido_compra value
	 */
	public void setPedidoCompra (br.com.ksisolucoes.vo.materiais.pedidocompra.PedidoCompra pedidoCompra) {
//        br.com.ksisolucoes.vo.materiais.pedidocompra.PedidoCompra pedidoCompraOld = this.pedidoCompra;
		this.pedidoCompra = pedidoCompra;
//        this.getPropertyChangeSupport().firePropertyChange ("pedidoCompra", pedidoCompraOld, pedidoCompra);
	}



	/**
	 * Return the value associated with the column: cod_pro
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Produto getProduto () {
		return getPropertyValue(this, produto, PROP_PRODUTO); 
	}

	/**
	 * Set the value related to the column: cod_pro
	 * @param produto the cod_pro value
	 */
	public void setProduto (br.com.ksisolucoes.vo.entradas.estoque.Produto produto) {
//        br.com.ksisolucoes.vo.entradas.estoque.Produto produtoOld = this.produto;
		this.produto = produto;
//        this.getPropertyChangeSupport().firePropertyChange ("produto", produtoOld, produto);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_usu_can
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usu_can
	 * @param usuarioCancelamento the cd_usu_can value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.materiais.pedidocompra.PedidoCompraItem)) return false;
		else {
			br.com.ksisolucoes.vo.materiais.pedidocompra.PedidoCompraItem pedidoCompraItem = (br.com.ksisolucoes.vo.materiais.pedidocompra.PedidoCompraItem) obj;
			if (null == this.getCodigo() || null == pedidoCompraItem.getCodigo()) return false;
			else return (this.getCodigo().equals(pedidoCompraItem.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}