package br.com.ksisolucoes.vo.cadsus.cds.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the esus_ficha_odonto_item_procedimento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="esus_ficha_odonto_item_procedimento"
 */

public abstract class BaseEsusFichaOdontoItemProcedimento extends BaseRootVO implements Serializable {

	public static String REF = "EsusFichaOdontoItemProcedimento";
	public static final String PROP_PROCEDIMENTO = "procedimento";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_ESUS_FICHA_ODONTO_ITEM = "esusFichaOdontoItem";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_PROCEDIMENTO_ESUS = "procedimentoEsus";
	public static final String PROP_QUANTIDADE = "quantidade";


	// constructors
	public BaseEsusFichaOdontoItemProcedimento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEsusFichaOdontoItemProcedimento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseEsusFichaOdontoItemProcedimento (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.cds.EsusFichaOdontoItem esusFichaOdontoItem,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setEsusFichaOdontoItem(esusFichaOdontoItem);
		this.setUsuario(usuario);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long quantidade;
	private java.util.Date dataCadastro;

	// many to one
	private br.com.ksisolucoes.vo.cadsus.cds.EsusFichaOdontoItem esusFichaOdontoItem;
	private br.com.ksisolucoes.vo.esus.ProcedimentoEsus procedimentoEsus;
	private br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_ficha_odonto_item_proced"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: quantidade
	 */
	public java.lang.Long getQuantidade () {
		return getPropertyValue(this, quantidade, PROP_QUANTIDADE); 
	}

	/**
	 * Set the value related to the column: quantidade
	 * @param quantidade the quantidade value
	 */
	public void setQuantidade (java.lang.Long quantidade) {
//        java.lang.Long quantidadeOld = this.quantidade;
		this.quantidade = quantidade;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidade", quantidadeOld, quantidade);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: cd_esus_ficha_odonto_item
	 */
	public br.com.ksisolucoes.vo.cadsus.cds.EsusFichaOdontoItem getEsusFichaOdontoItem () {
		return getPropertyValue(this, esusFichaOdontoItem, PROP_ESUS_FICHA_ODONTO_ITEM); 
	}

	/**
	 * Set the value related to the column: cd_esus_ficha_odonto_item
	 * @param esusFichaOdontoItem the cd_esus_ficha_odonto_item value
	 */
	public void setEsusFichaOdontoItem (br.com.ksisolucoes.vo.cadsus.cds.EsusFichaOdontoItem esusFichaOdontoItem) {
//        br.com.ksisolucoes.vo.cadsus.cds.EsusFichaOdontoItem esusFichaOdontoItemOld = this.esusFichaOdontoItem;
		this.esusFichaOdontoItem = esusFichaOdontoItem;
//        this.getPropertyChangeSupport().firePropertyChange ("esusFichaOdontoItem", esusFichaOdontoItemOld, esusFichaOdontoItem);
	}



	/**
	 * Return the value associated with the column: cd_procedimento_esus
	 */
	public br.com.ksisolucoes.vo.esus.ProcedimentoEsus getProcedimentoEsus () {
		return getPropertyValue(this, procedimentoEsus, PROP_PROCEDIMENTO_ESUS); 
	}

	/**
	 * Set the value related to the column: cd_procedimento_esus
	 * @param procedimentoEsus the cd_procedimento_esus value
	 */
	public void setProcedimentoEsus (br.com.ksisolucoes.vo.esus.ProcedimentoEsus procedimentoEsus) {
//        br.com.ksisolucoes.vo.esus.ProcedimentoEsus procedimentoEsusOld = this.procedimentoEsus;
		this.procedimentoEsus = procedimentoEsus;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimentoEsus", procedimentoEsusOld, procedimentoEsus);
	}



	/**
	 * Return the value associated with the column: cd_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento getProcedimento () {
		return getPropertyValue(this, procedimento, PROP_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_procedimento
	 * @param procedimento the cd_procedimento value
	 */
	public void setProcedimento (br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoOld = this.procedimento;
		this.procedimento = procedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimento", procedimentoOld, procedimento);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.cadsus.cds.EsusFichaOdontoItemProcedimento)) return false;
		else {
			br.com.ksisolucoes.vo.cadsus.cds.EsusFichaOdontoItemProcedimento esusFichaOdontoItemProcedimento = (br.com.ksisolucoes.vo.cadsus.cds.EsusFichaOdontoItemProcedimento) obj;
			if (null == this.getCodigo() || null == esusFichaOdontoItemProcedimento.getCodigo()) return false;
			else return (this.getCodigo().equals(esusFichaOdontoItemProcedimento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}