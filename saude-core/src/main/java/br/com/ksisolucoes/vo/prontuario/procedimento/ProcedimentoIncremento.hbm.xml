<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.procedimento"  >
	<class
		name="ProcedimentoIncremento"
		table="procedimento_incremento"
	>
		<meta attribute="sync-DAO">false</meta>
		<composite-id name="id" class="ProcedimentoIncrementoPK">
			<key-many-to-one
				name="procedimentoHabilitacaoCadastro"
				class="ProcedimentoHabilitacaoCadastro"
				column="cd_habilitacao"
			/>
			<key-many-to-one class="ProcedimentoCompetencia" name="procedimentoCompetencia">
				<column name="cd_procedimento"/>
				<column name="dt_competencia"/>
			 </key-many-to-one>
		</composite-id> <version column="version" name="version" type="long" />

		<property
			name="valorPercentualServicoHospitalar"
			column="vl_percentual_sh"
			type="java.lang.Double"
			not-null="true"
			length="5"
		/>
		<property
			name="valorPercentualServicoAmbulatorial"
			column="vl_percentual_sa"
			type="java.lang.Double"
			not-null="true"
			length="5"
		/>
		<property
			name="valorPercentualServicoProfissional"
			column="vl_percentual_sp"
			type="java.lang.Double"
			not-null="true"
			length="5"
		/>

	</class>	
</hibernate-mapping>