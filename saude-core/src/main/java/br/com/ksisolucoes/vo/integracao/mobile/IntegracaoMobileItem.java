package br.com.ksisolucoes.vo.integracao.mobile;

import java.io.Serializable;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.IEnumUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.integracao.mobile.base.BaseIntegracaoMobileItem;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import org.apache.commons.lang.StringUtils;
import org.hornetq.utils.json.JSONArray;
import org.hornetq.utils.json.JSONException;
import org.hornetq.utils.json.JSONObject;


public class IntegracaoMobileItem extends BaseIntegracaoMobileItem implements CodigoManager {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public IntegracaoMobileItem() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public IntegracaoMobileItem(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public IntegracaoMobileItem(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.integracao.mobile.IntegracaoMobile integracaoMobile,
            br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo gerenciadorArquivo) {

        super(codigo, integracaoMobile, gerenciadorArquivo);
    }

    /*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getDescricaoMsg() {

        String msgRecurso = getMsgRecurso();

        if (StringUtils.isNotEmpty(msgRecurso)) {

            try {
                JSONArray jsonArray = new JSONArray(getMsgRecurso());

                StringBuilder sb = new StringBuilder();

                for (int i = 0; i < jsonArray.length(); i++) {
                    if (!"null".equals(jsonArray.getJSONObject(i).getString("msg")) || jsonArray.getJSONObject(i).getString("msg") != null) {

                        sb.append("Código Mobile: ")
                                .append(jsonArray.getJSONObject(i).getString("codigoMobile"))
                                .append("\n")
                                .append("Mensagem: ")
                                .append(jsonArray.getJSONObject(i).getString("msg"))
                                .append(" \n")
                                .append(" \n");
                    }

                }

                return sb.toString();
            } catch (JSONException e) {
                Loggable.log.error(e);
            }
        }

        return null;
    }

    public String getDescricaoMsgKey() {
        return "Visualizar Mensagem do Erro";
    }

    public String getDescricaoVisualizarMensagem() {
        String abbreviate = StringUtils.abbreviate(getDescricaoMsg(), 100);
        if (StringUtils.isNotEmpty(abbreviate) && abbreviate.endsWith("...")) {
            return getDescricaoMsgKey();
        }

        return abbreviate;
    }

    public enum Status implements IEnum {

        ATIVADO(Bundle.getStringApplication("rotulo_ativado"), 0L),
        DESATIVADO(Bundle.getStringApplication("rotulo_desativado"), 1L);

        private String descricao;
        private Long value;

        Status(String descricao, Long value) {

            this.descricao = descricao;
            this.value = value;
        }

        @Override
        public Long value() {

            return value;
        }

        @Override
        public String descricao() {

            return descricao;
        }
    }

    public String getDescricaoStatus() {

        return IEnumUtils.getUmaDescricao(IntegracaoMobileItem.Status.values(), getStatus());
    }
}