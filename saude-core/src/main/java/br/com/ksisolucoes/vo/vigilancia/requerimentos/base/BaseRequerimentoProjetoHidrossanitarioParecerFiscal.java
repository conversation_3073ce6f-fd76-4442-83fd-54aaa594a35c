package br.com.ksisolucoes.vo.vigilancia.requerimentos.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the req_pro_hidros_parecer_fiscal table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="req_pro_hidros_parecer_fiscal"
 */

public abstract class BaseRequerimentoProjetoHidrossanitarioParecerFiscal extends BaseRootVO implements Serializable {

	public static String REF = "RequerimentoProjetoHidrossanitarioParecerFiscal";
	public static final String PROP_REQUERIMENTO_VIGILANCIA_FISCAL = "requerimentoVigilanciaFiscal";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_REQUERIMENTO_PROJETO_HIDROSSANITARIO_PARECER = "requerimentoProjetoHidrossanitarioParecer";


	// constructors
	public BaseRequerimentoProjetoHidrossanitarioParecerFiscal () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseRequerimentoProjetoHidrossanitarioParecerFiscal (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseRequerimentoProjetoHidrossanitarioParecerFiscal (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecer requerimentoProjetoHidrossanitarioParecer,
		br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaFiscal requerimentoVigilanciaFiscal) {

		this.setCodigo(codigo);
		this.setRequerimentoProjetoHidrossanitarioParecer(requerimentoProjetoHidrossanitarioParecer);
		this.setRequerimentoVigilanciaFiscal(requerimentoVigilanciaFiscal);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecer requerimentoProjetoHidrossanitarioParecer;
	private br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaFiscal requerimentoVigilanciaFiscal;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_req_pro_hidros_parecer_fiscal"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_req_pro_hidros_parecer
	 */
	public br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecer getRequerimentoProjetoHidrossanitarioParecer () {
		return getPropertyValue(this, requerimentoProjetoHidrossanitarioParecer, PROP_REQUERIMENTO_PROJETO_HIDROSSANITARIO_PARECER); 
	}

	/**
	 * Set the value related to the column: cd_req_pro_hidros_parecer
	 * @param requerimentoProjetoHidrossanitarioParecer the cd_req_pro_hidros_parecer value
	 */
	public void setRequerimentoProjetoHidrossanitarioParecer (br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecer requerimentoProjetoHidrossanitarioParecer) {
//        br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecer requerimentoProjetoHidrossanitarioParecerOld = this.requerimentoProjetoHidrossanitarioParecer;
		this.requerimentoProjetoHidrossanitarioParecer = requerimentoProjetoHidrossanitarioParecer;
//        this.getPropertyChangeSupport().firePropertyChange ("requerimentoProjetoHidrossanitarioParecer", requerimentoProjetoHidrossanitarioParecerOld, requerimentoProjetoHidrossanitarioParecer);
	}



	/**
	 * Return the value associated with the column: cd_req_vig_fiscal
	 */
	public br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaFiscal getRequerimentoVigilanciaFiscal () {
		return getPropertyValue(this, requerimentoVigilanciaFiscal, PROP_REQUERIMENTO_VIGILANCIA_FISCAL); 
	}

	/**
	 * Set the value related to the column: cd_req_vig_fiscal
	 * @param requerimentoVigilanciaFiscal the cd_req_vig_fiscal value
	 */
	public void setRequerimentoVigilanciaFiscal (br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaFiscal requerimentoVigilanciaFiscal) {
//        br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaFiscal requerimentoVigilanciaFiscalOld = this.requerimentoVigilanciaFiscal;
		this.requerimentoVigilanciaFiscal = requerimentoVigilanciaFiscal;
//        this.getPropertyChangeSupport().firePropertyChange ("requerimentoVigilanciaFiscal", requerimentoVigilanciaFiscalOld, requerimentoVigilanciaFiscal);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecerFiscal)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecerFiscal requerimentoProjetoHidrossanitarioParecerFiscal = (br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecerFiscal) obj;
			if (null == this.getCodigo() || null == requerimentoProjetoHidrossanitarioParecerFiscal.getCodigo()) return false;
			else return (this.getCodigo().equals(requerimentoProjetoHidrossanitarioParecerFiscal.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}