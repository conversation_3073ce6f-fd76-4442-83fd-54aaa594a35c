package br.com.ksisolucoes.vo.basico;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.io.Serializable;

import br.com.ksisolucoes.vo.basico.base.BaseParametroProcedimento;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ParametroProcedimento extends BaseParametroProcedimento implements CodigoManager {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public ParametroProcedimento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ParametroProcedimento (java.lang.Long codigo) {
		super(codigo);
	}

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    @Override
    protected void validatePropertyValue(String property, Object value) throws ValidacaoException {
        String param = null;
        try {
            param = (String) BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("validaProcedimentoAtendimento");
        } catch (DAOException ex) {
            Logger.getLogger(ParametroAtendimento.class.getName()).log(Level.SEVERE, null, ex);
        }

        if (value == null && RepositoryComponentDefault.SIM.equals(param)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_parametro_X_nao_definido", property));
        }
    }
}
