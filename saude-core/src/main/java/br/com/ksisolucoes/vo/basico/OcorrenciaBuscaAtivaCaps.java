package br.com.ksisolucoes.vo.basico;

import java.io.Serializable;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.EnumUtil;
import br.com.ksisolucoes.vo.basico.base.BaseOcorrenciaBuscaAtivaCaps;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class OcorrenciaBuscaAtivaCaps extends BaseOcorrenciaBuscaAtivaCaps implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public OcorrenciaBuscaAtivaCaps () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public OcorrenciaBuscaAtivaCaps (java.lang.Long codigo) {
		super(codigo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

	public enum FormaBusca implements IEnum {
		TELEFONE(1L, Bundle.getStringApplication("rotulo_telefone")),
		WHATSAPP(2L, Bundle.getStringApplication("rotulo_whatsapp")),
		EMAIL(3L, Bundle.getStringApplication("rotulo_email")),
		VISITA(4L, Bundle.getStringApplication("rotulo_visita"))
		;

		private Long value;
		private String descricao;

		private FormaBusca(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static OcorrenciaBuscaAtivaCaps.FormaBusca valeuOf(Long value) {
			for (OcorrenciaBuscaAtivaCaps.FormaBusca status : OcorrenciaBuscaAtivaCaps.FormaBusca.values()) {
				if (status.value().equals(value)) {
					return status;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}

	}

	public String getDescricaoFormaBusca(){
		return new EnumUtil().resolveDescricao(OcorrenciaBuscaAtivaCaps.FormaBusca.values(), getFormaBusca());
	}
}