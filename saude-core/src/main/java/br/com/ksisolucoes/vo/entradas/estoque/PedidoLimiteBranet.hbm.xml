<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="br.com.ksisolucoes.vo.entradas.estoque" >
    <class name="PedidoLimiteBranet" table="pedido_limite_branet">

        <id name="codigo" type="java.lang.Long" column="cd_pedido_limite">
            <generator class="sequence">
                <param name="sequence">seq_pedido_limite_branet</param>
            </generator>
        </id>
        <version column="version" name="version" type="long" />

        <property column="dt_cadastro" name="dataCadastro" type="java.util.Date" not-null="true"/>
        <property column="qtd_maxima_anual" name="qtdMaximaAnual" type="java.lang.Double" not-null="false"/>
        <property column="qtd_solicitada" name="qtdSolicitada" type="java.lang.Double" not-null="true"/>

        <many-to-one class="br.com.ksisolucoes.vo.basico.Empresa" column="empresa" name="empresa" not-null="true"/>
        <many-to-one class="br.com.ksisolucoes.vo.controle.Usuario" column="cd_usuario" name="usuario" not-null="true"/>
        <many-to-one name="produto" class="Produto" not-null="true"><column name="cod_pro"/></many-to-one>

    </class>
</hibernate-mapping>