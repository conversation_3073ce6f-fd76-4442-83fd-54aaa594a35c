<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.requerimentos">
    <class name="RequerimentoAnaliseProjeto" table="requerimento_analise_projeto">
        <id
                column="cd_requerimento_analise_projeto"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="assigned"/>
        </id>
        <version column="version" name="version" type="long"/>

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia"
                column="cd_req_vigilancia"
                name="requerimentoVigilancia"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.TipoProjetoVigilancia"
                column="cd_tp_projeto_vigilancia"
                name="tipoProjetoVigilancia"
                not-null="true"
        />

        <!--<property-->
                <!--column="ds_assunto_parecer"-->
                <!--name="descricaoAssuntoParecer"-->
                <!--type="java.lang.String"-->
        <!--/>-->

        <!--<property-->
                <!--column="ds_dados_projeto"-->
                <!--name="descricaoDadosProjeto"-->
                <!--type="java.lang.String"-->
        <!--/>-->

        <!--<property-->
                <!--column="ds_documentacao_apresentada"-->
                <!--name="descricaoDocumentacaoApresentada"-->
                <!--type="java.lang.String"-->
        <!--/>-->

        <!--<property-->
                <!--column="ds_adequacao_projeto"-->
                <!--name="descricaoAdequacaoProjeto"-->
                <!--type="java.lang.String"-->
        <!--/>-->

        <!--<property-->
                <!--column="ds_especificacoes_basicas"-->
                <!--name="descricaoEspecificacoesBasicas"-->
                <!--type="java.lang.String"-->
        <!--/>-->

        <property
                name="numeracaoParecerTecnico"
                column="num_parecer_tecnico"
                type="java.lang.Long"
        />
        
        <property
                name="numeracaoParecerConformidadeTecnica"
                column="num_parecer_conformidade_tecnica"
                type="java.lang.Long"
        />
        
        <property
                column="area"
                name="area"
                type="java.lang.Double"
                not-null="true"
        />
        
        <property
            name="dataInspecao"
            column="dt_inspecao"
            type="java.util.Date"
        />
        
        <property
                column="ds_caracterizacao"
                name="descricaoCaracterizacao"
                type="java.lang.String"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco"
            column="cd_vigilancia_endereco"
            name="vigilanciaEndereco"
            not-null="false"
        />
        
        <property
            column="nr_obra"
            name="numeroObra"
            type="java.lang.String"
            length="10"
        />
        
        <property
            column="quadra_obra"
            name="quadraObra"
            type="java.lang.String"
            length="100"
        />
        
        <property
            column="nr_lado_obra"
            name="numeroObraAoLado"
            type="java.lang.String"
            length="10"
        />
        
        <property
            column="lote_obra"
            name="loteObra"
            type="java.lang.String"
            length="10"
        />
        <property
            column="complemento_obra"
            name="complementoObra"
            type="java.lang.String"
            length="100"
        />
        <property
            column="nr_loteamento_obra"
            name="numeroLoteamentoObra"
            type="java.lang.String"
            length="100"
        />
        
        <property
            column="status_parecer"
            name="statusParecer"
            type="java.lang.Long"
        />
        
        
    </class>
</hibernate-mapping>
