package br.com.ksisolucoes.vo.cadsus;

import java.io.Serializable;

import br.com.ksisolucoes.vo.cadsus.base.BaseFormularioTriagemMorbidadeCovid19;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class FormularioTriagemMorbidadeCovid19 extends BaseFormularioTriagemMorbidadeCovid19 implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public FormularioTriagemMorbidadeCovid19 () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public FormularioTriagemMorbidadeCovid19 (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public FormularioTriagemMorbidadeCovid19 (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.FormularioTriagemCovid19 formularioTriagemCovid19,
		br.com.ksisolucoes.vo.cadsus.MorbidadePreviaCovid19 morbidadeCovid19) {

		super (
			codigo,
			formularioTriagemCovid19,
			morbidadeCovid19);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}