package br.com.ksisolucoes.vo.controle;

import java.io.Serializable;

import br.com.ksisolucoes.vo.controle.base.BaseGrupo;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;

/**
 * Esta classe est relacionado com a tabela grupos. Classe dimensionada para customizaes.
 * 
 */
public class Grupo extends BaseGrupo implements CodigoManager, PesquisaObjectInterface {

    private static final long serialVersionUID = 1L;

    /* [CONSTRUCTOR MARKER BEGIN] */
	public Grupo () {
		super();
	}

	public static final long MEDICAMENTO = 1;

	/**
	 * Constructor for primary key
	 */
	public Grupo (java.lang.Long codigo) {
		super(codigo);
	}

    /* [CONSTRUCTOR MARKER END] */

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public void setCodigoManager( Serializable codigo ) {
        this.setCodigo( ( Long ) codigo );
    }

    @Override
    public String getDescricaoVO() {
        return getNome();
    }

    @Override
    public String getIdentificador() {
        return getCodigo().toString();
    }

}
