package br.com.ksisolucoes.vo.cadsus.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the exportacao_esus_processo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="exportacao_esus_processo"
 */

public abstract class BaseExportacaoEsusProcesso extends BaseRootVO implements Serializable {

	public static String REF = "ExportacaoEsusProcesso";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_INCONSISTENCIAS = "inconsistencias";
	public static final String PROP_STATUS = "status";
	public static final String PROP_DATA = "data";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_LIMITE = "dataLimite";
	public static final String PROP_ASYNC_PROCESS = "asyncProcess";
	public static final String PROP_PATH = "path";


	// constructors
	public BaseExportacaoEsusProcesso () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseExportacaoEsusProcesso (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String path;
	private java.util.Date data;
	private java.lang.String inconsistencias;
	private java.util.Date dataLimite;
	private java.lang.Long status;

	// many to one
	private br.com.ksisolucoes.vo.service.AsyncProcess asyncProcess;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_exp_proc"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: path
	 */
	public java.lang.String getPath () {
		return getPropertyValue(this, path, PROP_PATH); 
	}

	/**
	 * Set the value related to the column: path
	 * @param path the path value
	 */
	public void setPath (java.lang.String path) {
//        java.lang.String pathOld = this.path;
		this.path = path;
//        this.getPropertyChangeSupport().firePropertyChange ("path", pathOld, path);
	}



	/**
	 * Return the value associated with the column: data
	 */
	public java.util.Date getData () {
		return getPropertyValue(this, data, PROP_DATA); 
	}

	/**
	 * Set the value related to the column: data
	 * @param data the data value
	 */
	public void setData (java.util.Date data) {
//        java.util.Date dataOld = this.data;
		this.data = data;
//        this.getPropertyChangeSupport().firePropertyChange ("data", dataOld, data);
	}



	/**
	 * Return the value associated with the column: inconsistencias
	 */
	public java.lang.String getInconsistencias () {
		return getPropertyValue(this, inconsistencias, PROP_INCONSISTENCIAS); 
	}

	/**
	 * Set the value related to the column: inconsistencias
	 * @param inconsistencias the inconsistencias value
	 */
	public void setInconsistencias (java.lang.String inconsistencias) {
//        java.lang.String inconsistenciasOld = this.inconsistencias;
		this.inconsistencias = inconsistencias;
//        this.getPropertyChangeSupport().firePropertyChange ("inconsistencias", inconsistenciasOld, inconsistencias);
	}



	/**
	 * Return the value associated with the column: data_limite
	 */
	public java.util.Date getDataLimite () {
		return getPropertyValue(this, dataLimite, PROP_DATA_LIMITE); 
	}

	/**
	 * Set the value related to the column: data_limite
	 * @param dataLimite the data_limite value
	 */
	public void setDataLimite (java.util.Date dataLimite) {
//        java.util.Date dataLimiteOld = this.dataLimite;
		this.dataLimite = dataLimite;
//        this.getPropertyChangeSupport().firePropertyChange ("dataLimite", dataLimiteOld, dataLimite);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: cd_process
	 */
	public br.com.ksisolucoes.vo.service.AsyncProcess getAsyncProcess () {
		return getPropertyValue(this, asyncProcess, PROP_ASYNC_PROCESS); 
	}

	/**
	 * Set the value related to the column: cd_process
	 * @param asyncProcess the cd_process value
	 */
	public void setAsyncProcess (br.com.ksisolucoes.vo.service.AsyncProcess asyncProcess) {
//        br.com.ksisolucoes.vo.service.AsyncProcess asyncProcessOld = this.asyncProcess;
		this.asyncProcess = asyncProcess;
//        this.getPropertyChangeSupport().firePropertyChange ("asyncProcess", asyncProcessOld, asyncProcess);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.cadsus.ExportacaoEsusProcesso)) return false;
		else {
			br.com.ksisolucoes.vo.cadsus.ExportacaoEsusProcesso exportacaoEsusProcesso = (br.com.ksisolucoes.vo.cadsus.ExportacaoEsusProcesso) obj;
			if (null == this.getCodigo() || null == exportacaoEsusProcesso.getCodigo()) return false;
			else return (this.getCodigo().equals(exportacaoEsusProcesso.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}