package br.com.ksisolucoes.vo.frota;

import java.io.Serializable;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.vo.frota.base.BaseDiarioBordoVeiculo;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class DiarioBordoVeiculo extends BaseDiarioBordoVeiculo implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public DiarioBordoVeiculo () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public DiarioBordoVeiculo (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public DiarioBordoVeiculo (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.frota.Veiculo veiculo,
		br.com.ksisolucoes.vo.frota.Motorista motorista,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataCadastro,
		java.util.Date dataSaida,
		java.util.Date dataChegada,
		java.lang.Long kmInicial,
		java.lang.Long kmFinal,
		java.lang.String destino,
		java.util.Date dataUsuario) {

		super (
			codigo,
			veiculo,
			motorista,
			usuario,
			dataCadastro,
			dataSaida,
			dataChegada,
			kmInicial,
			kmFinal,
			destino,
			dataUsuario);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public Long getKmPercorrido() {
        return Coalesce.asLong(getKmFinal()) - Coalesce.asLong(getKmInicial());
    }
}