package br.com.ksisolucoes.vo.agendamento;

import java.io.Serializable;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.agendamento.base.BaseLoteSolicitacaoAgendamento;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class LoteSolicitacaoAgendamento extends BaseLoteSolicitacaoAgendamento implements CodigoManager {
	private static final long serialVersionUID = 1L;
        
        public static final String PROP_DESCRICAO_STATUS = "descricaoStatus";

        public enum StatusLoteSolicitacaoAgendamento implements IEnum<StatusLoteSolicitacaoAgendamento> {

            PENDENTE(0L, Bundle.getStringApplication("rotulo_pendente")),
            ENVIADO(1L, Bundle.getStringApplication("rotulo_enviado")),
            RECEBIDO(2L, Bundle.getStringApplication("rotulo_recebido"));

            private Long value;
            private String descricao;

            private StatusLoteSolicitacaoAgendamento(Long value, String descricao) {
                this.value = value;
                this.descricao = descricao;
            }

            @Override
            public Long value() {
                return value;
            }

            @Override
            public String descricao() {
                return descricao;
            }

            public static StatusLoteSolicitacaoAgendamento valueOf(Long value) {
                for (StatusLoteSolicitacaoAgendamento status : StatusLoteSolicitacaoAgendamento.values()) {
                    if (status.value().equals(value)) {
                        return status;
                    }
                }
                return null;
            }
        }
        
/*[CONSTRUCTOR MARKER BEGIN]*/
	public LoteSolicitacaoAgendamento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public LoteSolicitacaoAgendamento (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public LoteSolicitacaoAgendamento (
		java.lang.Long codigo,
		java.util.Date dataCadastro,
		java.lang.Long status) {

		super (
			codigo,
			dataCadastro,
			status);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public String getDescricaoStatus(){
        StatusLoteSolicitacaoAgendamento status = StatusLoteSolicitacaoAgendamento.valueOf(getStatus());
        if (status != null && status.descricao != null) {
            return status.descricao();
        }
        return "";
    }
}