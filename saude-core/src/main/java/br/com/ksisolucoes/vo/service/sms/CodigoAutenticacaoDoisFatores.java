package br.com.ksisolucoes.vo.service.sms;

import java.io.Serializable;

import br.com.ksisolucoes.vo.service.sms.base.BaseCodigoAutenticacaoDoisFatores;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class CodigoAutenticacaoDoisFatores extends BaseCodigoAutenticacaoDoisFatores implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public CodigoAutenticacaoDoisFatores () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public CodigoAutenticacaoDoisFatores (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public CodigoAutenticacaoDoisFatores (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataGeracao,
		java.lang.String codigoValidacao) {

		super (
			codigo,
			usuario,
			dataGeracao,
			codigoValidacao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}