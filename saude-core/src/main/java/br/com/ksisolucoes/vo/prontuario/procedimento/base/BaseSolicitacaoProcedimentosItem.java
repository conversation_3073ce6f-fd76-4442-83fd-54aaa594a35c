package br.com.ksisolucoes.vo.prontuario.procedimento.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the solicitacao_procedimentos_item table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="solicitacao_procedimentos_item"
 */

public abstract class BaseSolicitacaoProcedimentosItem extends BaseRootVO implements Serializable {

	public static String REF = "SolicitacaoProcedimentosItem";
	public static final String PROP_STATUS = "status";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_SOLICITACAO_PROCEDIMENTO = "solicitacaoProcedimento";
	public static final String PROP_PROCEDIMENTO_COMPETENCIA = "procedimentoCompetencia";
	public static final String PROP_QUANTIDADE = "quantidade";


	// constructors
	public BaseSolicitacaoProcedimentosItem () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseSolicitacaoProcedimentosItem (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseSolicitacaoProcedimentosItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetencia,
		br.com.ksisolucoes.vo.prontuario.procedimento.SolicitacaoProcedimentos solicitacaoProcedimento,
		java.lang.Long status) {

		this.setCodigo(codigo);
		this.setProcedimentoCompetencia(procedimentoCompetencia);
		this.setSolicitacaoProcedimento(solicitacaoProcedimento);
		this.setStatus(status);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long status;
	private java.lang.Long quantidade;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetencia;
	private br.com.ksisolucoes.vo.prontuario.procedimento.SolicitacaoProcedimentos solicitacaoProcedimento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_sol_proced_item"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: quantidade
	 */
	public java.lang.Long getQuantidade () {
		return getPropertyValue(this, quantidade, PROP_QUANTIDADE); 
	}

	/**
	 * Set the value related to the column: quantidade
	 * @param quantidade the quantidade value
	 */
	public void setQuantidade (java.lang.Long quantidade) {
//        java.lang.Long quantidadeOld = this.quantidade;
		this.quantidade = quantidade;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidade", quantidadeOld, quantidade);
	}



	/**
	 * Return the value associated with the column: dt_competencia
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia getProcedimentoCompetencia () {
		return getPropertyValue(this, procedimentoCompetencia, PROP_PROCEDIMENTO_COMPETENCIA); 
	}

	/**
	 * Set the value related to the column: dt_competencia
	 * @param procedimentoCompetencia the dt_competencia value
	 */
	public void setProcedimentoCompetencia (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetencia) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetenciaOld = this.procedimentoCompetencia;
		this.procedimentoCompetencia = procedimentoCompetencia;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimentoCompetencia", procedimentoCompetenciaOld, procedimentoCompetencia);
	}



	/**
	 * Return the value associated with the column: cd_sol_proced
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.SolicitacaoProcedimentos getSolicitacaoProcedimento () {
		return getPropertyValue(this, solicitacaoProcedimento, PROP_SOLICITACAO_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_sol_proced
	 * @param solicitacaoProcedimento the cd_sol_proced value
	 */
	public void setSolicitacaoProcedimento (br.com.ksisolucoes.vo.prontuario.procedimento.SolicitacaoProcedimentos solicitacaoProcedimento) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.SolicitacaoProcedimentos solicitacaoProcedimentoOld = this.solicitacaoProcedimento;
		this.solicitacaoProcedimento = solicitacaoProcedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("solicitacaoProcedimento", solicitacaoProcedimentoOld, solicitacaoProcedimento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.procedimento.SolicitacaoProcedimentosItem)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.procedimento.SolicitacaoProcedimentosItem solicitacaoProcedimentosItem = (br.com.ksisolucoes.vo.prontuario.procedimento.SolicitacaoProcedimentosItem) obj;
			if (null == this.getCodigo() || null == solicitacaoProcedimentosItem.getCodigo()) return false;
			else return (this.getCodigo().equals(solicitacaoProcedimentosItem.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}