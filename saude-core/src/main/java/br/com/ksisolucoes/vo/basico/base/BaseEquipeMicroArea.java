package br.com.ksisolucoes.vo.basico.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the equipe_micro_area table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="equipe_micro_area"
 */

public abstract class BaseEquipeMicroArea extends BaseRootVO implements Serializable {

	public static String REF = "EquipeMicroArea";
	public static final String PROP_EQUIPE_PROFISSIONAL = "equipeProfissional";
	public static final String PROP_STATUS = "status";
	public static final String PROP_MICRO_AREA = "microArea";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_EQUIPE_AREA = "equipeArea";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_VERSION_ALL = "versionAll";


	// constructors
	public BaseEquipeMicroArea () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEquipeMicroArea (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseEquipeMicroArea (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.EquipeProfissional equipeProfissional,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		java.lang.Long status) {

		this.setCodigo(codigo);
		this.setEquipeProfissional(equipeProfissional);
		this.setEmpresa(empresa);
		this.setStatus(status);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long microArea;
	private java.lang.Long versionAll;
	private java.lang.Long status;

	// many to one
	private br.com.ksisolucoes.vo.basico.EquipeArea equipeArea;
	private br.com.ksisolucoes.vo.basico.EquipeProfissional equipeProfissional;
	private br.com.ksisolucoes.vo.basico.Empresa empresa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_eqp_micro_area"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: micro_area
	 */
	public java.lang.Long getMicroArea () {
		return getPropertyValue(this, microArea, PROP_MICRO_AREA); 
	}

	/**
	 * Set the value related to the column: micro_area
	 * @param microArea the micro_area value
	 */
	public void setMicroArea (java.lang.Long microArea) {
//        java.lang.Long microAreaOld = this.microArea;
		this.microArea = microArea;
//        this.getPropertyChangeSupport().firePropertyChange ("microArea", microAreaOld, microArea);
	}



	/**
	 * Return the value associated with the column: version_all
	 */
	public java.lang.Long getVersionAll () {
		return getPropertyValue(this, versionAll, PROP_VERSION_ALL); 
	}

	/**
	 * Set the value related to the column: version_all
	 * @param versionAll the version_all value
	 */
	public void setVersionAll (java.lang.Long versionAll) {
//        java.lang.Long versionAllOld = this.versionAll;
		this.versionAll = versionAll;
//        this.getPropertyChangeSupport().firePropertyChange ("versionAll", versionAllOld, versionAll);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: cd_equipe_area
	 */
	public br.com.ksisolucoes.vo.basico.EquipeArea getEquipeArea () {
		return getPropertyValue(this, equipeArea, PROP_EQUIPE_AREA); 
	}

	/**
	 * Set the value related to the column: cd_equipe_area
	 * @param equipeArea the cd_equipe_area value
	 */
	public void setEquipeArea (br.com.ksisolucoes.vo.basico.EquipeArea equipeArea) {
//        br.com.ksisolucoes.vo.basico.EquipeArea equipeAreaOld = this.equipeArea;
		this.equipeArea = equipeArea;
//        this.getPropertyChangeSupport().firePropertyChange ("equipeArea", equipeAreaOld, equipeArea);
	}



	/**
	 * Return the value associated with the column: cd_equipe_profissional
	 */
	public br.com.ksisolucoes.vo.basico.EquipeProfissional getEquipeProfissional () {
		return getPropertyValue(this, equipeProfissional, PROP_EQUIPE_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_equipe_profissional
	 * @param equipeProfissional the cd_equipe_profissional value
	 */
	public void setEquipeProfissional (br.com.ksisolucoes.vo.basico.EquipeProfissional equipeProfissional) {
//        br.com.ksisolucoes.vo.basico.EquipeProfissional equipeProfissionalOld = this.equipeProfissional;
		this.equipeProfissional = equipeProfissional;
//        this.getPropertyChangeSupport().firePropertyChange ("equipeProfissional", equipeProfissionalOld, equipeProfissional);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.EquipeMicroArea)) return false;
		else {
			br.com.ksisolucoes.vo.basico.EquipeMicroArea equipeMicroArea = (br.com.ksisolucoes.vo.basico.EquipeMicroArea) obj;
			if (null == this.getCodigo() || null == equipeMicroArea.getCodigo()) return false;
			else return (this.getCodigo().equals(equipeMicroArea.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}