package br.com.ksisolucoes.vo.vigilancia;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import br.com.ksisolucoes.vo.vigilancia.base.BaseTipoProjetoVigilancia;

import java.io.Serializable;


public class TipoProjetoVigilancia extends BaseTipoProjetoVigilancia implements CodigoManager, PesquisaObjectInterface {
    private static final long serialVersionUID = 1L;

    public enum Tipo implements IEnum {
        PBA(1L, Bundle.getStringApplication("rotulo_analise_basico_arquitetura_pba")),
        VISTORIA_PBA(2L, Bundle.getStringApplication("rotulo_laudo_conformidade_pba")),
        PHS(4L, Bundle.getStringApplication("rotulo_analise_projeto_hidrossanitario_padrao")),
        VISTORIA_HABITE_SE(8L, Bundle.getStringApplication("rotulo_habitese_padrao")),
        VISTORIA_HABITE_SE_DECLARATORIO(16L, Bundle.getStringApplication("rotulo_habitese_declaratorio")),
        PHSDeclaratorio(32L, Bundle.getStringApplication("rotulo_analise_projeto_hidrossanitario_declaratorio")),
        PROJETO_ARQUITETONICO_SANITARIO(64L, Bundle.getStringApplication("rotulo_projeto_arquitetonico_sanitario"))
        ;

        private Long value;
        private String descricao;

        private Tipo(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Tipo valeuOf(Long value) {
            for (Tipo tipo : Tipo.values()) {
                if (tipo.value().equals(value)) {
                    return tipo;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }

    public enum TipoCobranca implements IEnum {
        UNICA(0L, Bundle.getStringApplication("rotulo_taxa_unica")),
        POR_M2(1L, Bundle.getStringApplication("rotulo_por_metro_quadrado_abv")),
        ;

        private Long value;
        private String descricao;

        private TipoCobranca(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Tipo valeuOf(Long value) {
            for (Tipo tipo : Tipo.values()) {
                if (tipo.value().equals(value)) {
                    return tipo;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public TipoProjetoVigilancia() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public TipoProjetoVigilancia(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public TipoProjetoVigilancia(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.vigilancia.taxa.Taxa taxa,
            br.com.ksisolucoes.vo.controle.Usuario usuario,
            java.lang.String descricao,
            java.lang.Double valor,
            java.lang.Double metragemMaxima,
            java.util.Date dataCadastro,
            java.util.Date dataUsuario) {

        super(
                codigo,
                taxa,
                usuario,
                descricao,
                valor,
                metragemMaxima,
                dataCadastro,
                dataUsuario);
    }

    /*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    @Override
    public String getDescricaoVO() {
        return this.getDescricao();
    }

    @Override
    public String getIdentificador() {
        return Coalesce.asString(this.getCodigo()).toString();
    }

    public String getDescricaoTipo() {
        Tipo tipo = Tipo.valeuOf(getTipo());
        if (tipo != null && tipo.descricao != null) {
            return tipo.descricao();
        }
        return "";
    }
}