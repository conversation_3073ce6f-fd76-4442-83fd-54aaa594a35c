package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseLembreteAtendimento;

import java.io.Serializable;



public class LembreteAtendimento extends BaseLembreteAtendimento implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public LembreteAtendimento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public LembreteAtendimento (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public LembreteAtendimento (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.String descricao,
		java.lang.Long flagPublico,
		java.lang.Long flagAtivo,
		java.util.Date dataCadastro) {

		super (
			codigo,
			usuarioCadsus,
			usuario,
			descricao,
			flagPublico,
			flagAtivo,
			dataCadastro);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getFlagPublicoFormatado(){
		if(RepositoryComponentDefault.SIM_LONG.equals(getFlagPublico())){
			return Bundle.getStringApplication("rotulo_sim");
		}
		return Bundle.getStringApplication("rotulo_nao");
	}

	public String getFlagAtivoFormatado(){
		if(RepositoryComponentDefault.SIM_LONG.equals(getFlagAtivo())){
			return Bundle.getStringApplication("rotulo_sim");
		}
		return Bundle.getStringApplication("rotulo_nao");
	}

	public String getDescricaoAbreviado(){
		if(getDescricao().length() > 50){
			return StringUtil.getStringMaxPrecision(getDescricao(), 50) + "...";
		}
		return getDescricao();
	}
}