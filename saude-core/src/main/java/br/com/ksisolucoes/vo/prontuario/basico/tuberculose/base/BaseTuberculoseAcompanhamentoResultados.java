package br.com.ksisolucoes.vo.prontuario.basico.tuberculose.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the tuberc_acomp_resultados table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="tuberc_acomp_resultados"
 */

public abstract class BaseTuberculoseAcompanhamentoResultados extends BaseRootVO implements Serializable {

	public static String REF = "TuberculoseAcompanhamentoResultados";
	public static final String PROP_DATA_RESULTADO = "dataResultado";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_SOLICITACAO = "dataSolicitacao";
	public static final String PROP_DATA_USUARIO = "dataUsuario";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_PROFISSIONAL = "profissional";
	public static final String PROP_USUARIO_CADASTRO = "usuarioCadastro";
	public static final String PROP_TUBERCULOSE_ACOMPANHAMENTO = "tuberculoseAcompanhamento";
	public static final String PROP_RESULTADO = "resultado";
	public static final String PROP_USUARIO_UPDATE = "usuarioUpdate";
	public static final String PROP_ATENDIMENTO = "atendimento";


	// constructors
	public BaseTuberculoseAcompanhamentoResultados () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseTuberculoseAcompanhamentoResultados (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseTuberculoseAcompanhamentoResultados (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseAcompanhamento tuberculoseAcompanhamento,
		br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento,
		br.com.ksisolucoes.vo.cadsus.Profissional profissional,
		br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
		br.com.ksisolucoes.vo.controle.Usuario usuarioUpdate,
		java.util.Date dataSolicitacao,
		java.util.Date dataCadastro,
		java.util.Date dataUsuario) {

		this.setCodigo(codigo);
		this.setTuberculoseAcompanhamento(tuberculoseAcompanhamento);
		this.setAtendimento(atendimento);
		this.setProfissional(profissional);
		this.setUsuarioCadastro(usuarioCadastro);
		this.setUsuarioUpdate(usuarioUpdate);
		this.setDataSolicitacao(dataSolicitacao);
		this.setDataCadastro(dataCadastro);
		this.setDataUsuario(dataUsuario);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataSolicitacao;
	private java.util.Date dataResultado;
	private java.lang.Long resultado;
	private java.util.Date dataCadastro;
	private java.util.Date dataUsuario;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseAcompanhamento tuberculoseAcompanhamento;
	private br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissional;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioUpdate;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_tuberc_acomp_resultados"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_solicitacao
	 */
	public java.util.Date getDataSolicitacao () {
		return getPropertyValue(this, dataSolicitacao, PROP_DATA_SOLICITACAO); 
	}

	/**
	 * Set the value related to the column: dt_solicitacao
	 * @param dataSolicitacao the dt_solicitacao value
	 */
	public void setDataSolicitacao (java.util.Date dataSolicitacao) {
//        java.util.Date dataSolicitacaoOld = this.dataSolicitacao;
		this.dataSolicitacao = dataSolicitacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataSolicitacao", dataSolicitacaoOld, dataSolicitacao);
	}



	/**
	 * Return the value associated with the column: dt_resultado
	 */
	public java.util.Date getDataResultado () {
		return getPropertyValue(this, dataResultado, PROP_DATA_RESULTADO); 
	}

	/**
	 * Set the value related to the column: dt_resultado
	 * @param dataResultado the dt_resultado value
	 */
	public void setDataResultado (java.util.Date dataResultado) {
//        java.util.Date dataResultadoOld = this.dataResultado;
		this.dataResultado = dataResultado;
//        this.getPropertyChangeSupport().firePropertyChange ("dataResultado", dataResultadoOld, dataResultado);
	}



	/**
	 * Return the value associated with the column: resultado
	 */
	public java.lang.Long getResultado () {
		return getPropertyValue(this, resultado, PROP_RESULTADO); 
	}

	/**
	 * Set the value related to the column: resultado
	 * @param resultado the resultado value
	 */
	public void setResultado (java.lang.Long resultado) {
//        java.lang.Long resultadoOld = this.resultado;
		this.resultado = resultado;
//        this.getPropertyChangeSupport().firePropertyChange ("resultado", resultadoOld, resultado);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_usuario
	 */
	public java.util.Date getDataUsuario () {
		return getPropertyValue(this, dataUsuario, PROP_DATA_USUARIO); 
	}

	/**
	 * Set the value related to the column: dt_usuario
	 * @param dataUsuario the dt_usuario value
	 */
	public void setDataUsuario (java.util.Date dataUsuario) {
//        java.util.Date dataUsuarioOld = this.dataUsuario;
		this.dataUsuario = dataUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsuario", dataUsuarioOld, dataUsuario);
	}



	/**
	 * Return the value associated with the column: cd_tuberc_acompanhamento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseAcompanhamento getTuberculoseAcompanhamento () {
		return getPropertyValue(this, tuberculoseAcompanhamento, PROP_TUBERCULOSE_ACOMPANHAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_tuberc_acompanhamento
	 * @param tuberculoseAcompanhamento the cd_tuberc_acompanhamento value
	 */
	public void setTuberculoseAcompanhamento (br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseAcompanhamento tuberculoseAcompanhamento) {
//        br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseAcompanhamento tuberculoseAcompanhamentoOld = this.tuberculoseAcompanhamento;
		this.tuberculoseAcompanhamento = tuberculoseAcompanhamento;
//        this.getPropertyChangeSupport().firePropertyChange ("tuberculoseAcompanhamento", tuberculoseAcompanhamentoOld, tuberculoseAcompanhamento);
	}



	/**
	 * Return the value associated with the column: nr_atendimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Atendimento getAtendimento () {
		return getPropertyValue(this, atendimento, PROP_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: nr_atendimento
	 * @param atendimento the nr_atendimento value
	 */
	public void setAtendimento (br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoOld = this.atendimento;
		this.atendimento = atendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimento", atendimentoOld, atendimento);
	}



	/**
	 * Return the value associated with the column: cd_profissional
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissional () {
		return getPropertyValue(this, profissional, PROP_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_profissional
	 * @param profissional the cd_profissional value
	 */
	public void setProfissional (br.com.ksisolucoes.vo.cadsus.Profissional profissional) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalOld = this.profissional;
		this.profissional = profissional;
//        this.getPropertyChangeSupport().firePropertyChange ("profissional", profissionalOld, profissional);
	}



	/**
	 * Return the value associated with the column: cd_usuario_cad
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCadastro () {
		return getPropertyValue(this, usuarioCadastro, PROP_USUARIO_CADASTRO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_cad
	 * @param usuarioCadastro the cd_usuario_cad value
	 */
	public void setUsuarioCadastro (br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCadastroOld = this.usuarioCadastro;
		this.usuarioCadastro = usuarioCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadastro", usuarioCadastroOld, usuarioCadastro);
	}



	/**
	 * Return the value associated with the column: cd_usuario_upd
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioUpdate () {
		return getPropertyValue(this, usuarioUpdate, PROP_USUARIO_UPDATE); 
	}

	/**
	 * Set the value related to the column: cd_usuario_upd
	 * @param usuarioUpdate the cd_usuario_upd value
	 */
	public void setUsuarioUpdate (br.com.ksisolucoes.vo.controle.Usuario usuarioUpdate) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioUpdateOld = this.usuarioUpdate;
		this.usuarioUpdate = usuarioUpdate;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioUpdate", usuarioUpdateOld, usuarioUpdate);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseAcompanhamentoResultados)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseAcompanhamentoResultados tuberculoseAcompanhamentoResultados = (br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseAcompanhamentoResultados) obj;
			if (null == this.getCodigo() || null == tuberculoseAcompanhamentoResultados.getCodigo()) return false;
			else return (this.getCodigo().equals(tuberculoseAcompanhamentoResultados.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}