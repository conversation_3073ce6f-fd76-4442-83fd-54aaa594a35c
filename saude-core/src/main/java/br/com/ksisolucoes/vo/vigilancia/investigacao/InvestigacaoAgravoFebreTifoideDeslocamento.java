package br.com.ksisolucoes.vo.vigilancia.investigacao;

import java.io.Serializable;

import br.com.ksisolucoes.vo.vigilancia.investigacao.base.BaseInvestigacaoAgravoFebreTifoideDeslocamento;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class InvestigacaoAgravoFebreTifoideDeslocamento extends BaseInvestigacaoAgravoFebreTifoideDeslocamento implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public InvestigacaoAgravoFebreTifoideDeslocamento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public InvestigacaoAgravoFebreTifoideDeslocamento (java.lang.Long codigo) {
		super(codigo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}