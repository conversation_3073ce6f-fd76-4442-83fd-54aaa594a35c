package br.com.ksisolucoes.vo.cadsus.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the dom_endereco_domicilio table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="dom_endereco_domicilio"
 */

public abstract class BaseDominioEnderecoDomicilio extends BaseRootVO implements Serializable {

	public static String REF = "DominioEnderecoDomicilio";
	public static final String PROP_USUARIO_CADSUS_RESPONSAVEL = "usuarioCadsusResponsavel";
	public static final String PROP_NUMERO = "numero";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_KEYWORD = "keyword";
	public static final String PROP_EQUIPE_AREA = "equipeArea";
	public static final String PROP_EQUIPE_MICRO_AREA = "equipeMicroArea";
	public static final String PROP_NOME_PACIENTE = "nomePaciente";
	public static final String PROP_RESPONSAVEL_FAMILIA = "responsavelFamilia";
	public static final String PROP_CEP = "cep";
	public static final String PROP_NUMERO_FAMILIA = "numeroFamilia";
	public static final String PROP_DATA_NASCIMENTO = "dataNascimento";
	public static final String PROP_UF = "uf";
	public static final String PROP_COMPLEMENTO = "complemento";
	public static final String PROP_MICRO_AREA = "microArea";
	public static final String PROP_DESCRICAO_AREA = "descricaoArea";
	public static final String PROP_CIDADE = "cidade";
	public static final String PROP_LOGRADOURO = "logradouro";
	public static final String PROP_DESCRICAO_CIDADE = "descricaoCidade";
	public static final String PROP_ENDERECO_DOMICILIO = "enderecoDomicilio";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_ENDERECO_USUARIO_CADSUS = "enderecoUsuarioCadsus";
	public static final String PROP_NOME_SOCIAL_PACIENTE = "nomeSocialPaciente";
	public static final String PROP_PONTO_REFERENCIA = "pontoReferencia";
	public static final String PROP_BAIRRO = "bairro";


	// constructors
	public BaseDominioEnderecoDomicilio () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseDominioEnderecoDomicilio (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseDominioEnderecoDomicilio (
		java.lang.Long codigo,
		java.lang.String logradouro) {

		this.setCodigo(codigo);
		this.setLogradouro(logradouro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String logradouro;
	private java.lang.String numero;
	private java.lang.String bairro;
	private java.lang.String cep;
	private java.lang.String complemento;
	private java.lang.String descricaoCidade;
	private java.lang.String uf;
	private java.lang.String pontoReferencia;
	private java.lang.Long numeroFamilia;
	private java.lang.String nomePaciente;
	private java.lang.String nomeSocialPaciente;
	private java.lang.Long responsavelFamilia;
	private java.util.Date dataNascimento;
	private java.lang.String descricaoArea;
	private java.lang.Long microArea;
	private java.lang.String keyword;

	// many to one
	private br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus enderecoUsuarioCadsus;
	private br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio enderecoDomicilio;
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusResponsavel;
	private br.com.ksisolucoes.vo.basico.Cidade cidade;
	private br.com.ksisolucoes.vo.basico.EquipeArea equipeArea;
	private br.com.ksisolucoes.vo.basico.EquipeMicroArea equipeMicroArea;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="id"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_logradouro
	 */
	public java.lang.String getLogradouro () {
		return getPropertyValue(this, logradouro, PROP_LOGRADOURO); 
	}

	/**
	 * Set the value related to the column: ds_logradouro
	 * @param logradouro the ds_logradouro value
	 */
	public void setLogradouro (java.lang.String logradouro) {
//        java.lang.String logradouroOld = this.logradouro;
		this.logradouro = logradouro;
//        this.getPropertyChangeSupport().firePropertyChange ("logradouro", logradouroOld, logradouro);
	}



	/**
	 * Return the value associated with the column: nr_logradouro
	 */
	public java.lang.String getNumero () {
		return getPropertyValue(this, numero, PROP_NUMERO); 
	}

	/**
	 * Set the value related to the column: nr_logradouro
	 * @param numero the nr_logradouro value
	 */
	public void setNumero (java.lang.String numero) {
//        java.lang.String numeroOld = this.numero;
		this.numero = numero;
//        this.getPropertyChangeSupport().firePropertyChange ("numero", numeroOld, numero);
	}



	/**
	 * Return the value associated with the column: ds_bairro
	 */
	public java.lang.String getBairro () {
		return getPropertyValue(this, bairro, PROP_BAIRRO); 
	}

	/**
	 * Set the value related to the column: ds_bairro
	 * @param bairro the ds_bairro value
	 */
	public void setBairro (java.lang.String bairro) {
//        java.lang.String bairroOld = this.bairro;
		this.bairro = bairro;
//        this.getPropertyChangeSupport().firePropertyChange ("bairro", bairroOld, bairro);
	}



	/**
	 * Return the value associated with the column: cep
	 */
	public java.lang.String getCep () {
		return getPropertyValue(this, cep, PROP_CEP); 
	}

	/**
	 * Set the value related to the column: cep
	 * @param cep the cep value
	 */
	public void setCep (java.lang.String cep) {
//        java.lang.String cepOld = this.cep;
		this.cep = cep;
//        this.getPropertyChangeSupport().firePropertyChange ("cep", cepOld, cep);
	}



	/**
	 * Return the value associated with the column: ds_comp_logradouro
	 */
	public java.lang.String getComplemento () {
		return getPropertyValue(this, complemento, PROP_COMPLEMENTO); 
	}

	/**
	 * Set the value related to the column: ds_comp_logradouro
	 * @param complemento the ds_comp_logradouro value
	 */
	public void setComplemento (java.lang.String complemento) {
//        java.lang.String complementoOld = this.complemento;
		this.complemento = complemento;
//        this.getPropertyChangeSupport().firePropertyChange ("complemento", complementoOld, complemento);
	}



	/**
	 * Return the value associated with the column: ds_cidade
	 */
	public java.lang.String getDescricaoCidade () {
		return getPropertyValue(this, descricaoCidade, PROP_DESCRICAO_CIDADE); 
	}

	/**
	 * Set the value related to the column: ds_cidade
	 * @param descricaoCidade the ds_cidade value
	 */
	public void setDescricaoCidade (java.lang.String descricaoCidade) {
//        java.lang.String descricaoCidadeOld = this.descricaoCidade;
		this.descricaoCidade = descricaoCidade;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoCidade", descricaoCidadeOld, descricaoCidade);
	}



	/**
	 * Return the value associated with the column: ds_uf
	 */
	public java.lang.String getUf () {
		return getPropertyValue(this, uf, PROP_UF); 
	}

	/**
	 * Set the value related to the column: ds_uf
	 * @param uf the ds_uf value
	 */
	public void setUf (java.lang.String uf) {
//        java.lang.String ufOld = this.uf;
		this.uf = uf;
//        this.getPropertyChangeSupport().firePropertyChange ("uf", ufOld, uf);
	}



	/**
	 * Return the value associated with the column: ponto_referencia
	 */
	public java.lang.String getPontoReferencia () {
		return getPropertyValue(this, pontoReferencia, PROP_PONTO_REFERENCIA); 
	}

	/**
	 * Set the value related to the column: ponto_referencia
	 * @param pontoReferencia the ponto_referencia value
	 */
	public void setPontoReferencia (java.lang.String pontoReferencia) {
//        java.lang.String pontoReferenciaOld = this.pontoReferencia;
		this.pontoReferencia = pontoReferencia;
//        this.getPropertyChangeSupport().firePropertyChange ("pontoReferencia", pontoReferenciaOld, pontoReferencia);
	}



	/**
	 * Return the value associated with the column: nr_familia
	 */
	public java.lang.Long getNumeroFamilia () {
		return getPropertyValue(this, numeroFamilia, PROP_NUMERO_FAMILIA); 
	}

	/**
	 * Set the value related to the column: nr_familia
	 * @param numeroFamilia the nr_familia value
	 */
	public void setNumeroFamilia (java.lang.Long numeroFamilia) {
//        java.lang.Long numeroFamiliaOld = this.numeroFamilia;
		this.numeroFamilia = numeroFamilia;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroFamilia", numeroFamiliaOld, numeroFamilia);
	}



	/**
	 * Return the value associated with the column: ds_pac_nome
	 */
	public java.lang.String getNomePaciente () {
		return getPropertyValue(this, nomePaciente, PROP_NOME_PACIENTE); 
	}

	/**
	 * Set the value related to the column: ds_pac_nome
	 * @param nomePaciente the ds_pac_nome value
	 */
	public void setNomePaciente (java.lang.String nomePaciente) {
//        java.lang.String nomePacienteOld = this.nomePaciente;
		this.nomePaciente = nomePaciente;
//        this.getPropertyChangeSupport().firePropertyChange ("nomePaciente", nomePacienteOld, nomePaciente);
	}



	/**
	 * Return the value associated with the column: ds_pac_nome_social
	 */
	public java.lang.String getNomeSocialPaciente () {
		return getPropertyValue(this, nomeSocialPaciente, PROP_NOME_SOCIAL_PACIENTE); 
	}

	/**
	 * Set the value related to the column: ds_pac_nome_social
	 * @param nomeSocialPaciente the ds_pac_nome_social value
	 */
	public void setNomeSocialPaciente (java.lang.String nomeSocialPaciente) {
//        java.lang.String nomeSocialPacienteOld = this.nomeSocialPaciente;
		this.nomeSocialPaciente = nomeSocialPaciente;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeSocialPaciente", nomeSocialPacienteOld, nomeSocialPaciente);
	}



	/**
	 * Return the value associated with the column: flag_responsavel
	 */
	public java.lang.Long getResponsavelFamilia () {
		return getPropertyValue(this, responsavelFamilia, PROP_RESPONSAVEL_FAMILIA); 
	}

	/**
	 * Set the value related to the column: flag_responsavel
	 * @param responsavelFamilia the flag_responsavel value
	 */
	public void setResponsavelFamilia (java.lang.Long responsavelFamilia) {
//        java.lang.Long responsavelFamiliaOld = this.responsavelFamilia;
		this.responsavelFamilia = responsavelFamilia;
//        this.getPropertyChangeSupport().firePropertyChange ("responsavelFamilia", responsavelFamiliaOld, responsavelFamilia);
	}



	/**
	 * Return the value associated with the column: dt_nascimento
	 */
	public java.util.Date getDataNascimento () {
		return getPropertyValue(this, dataNascimento, PROP_DATA_NASCIMENTO); 
	}

	/**
	 * Set the value related to the column: dt_nascimento
	 * @param dataNascimento the dt_nascimento value
	 */
	public void setDataNascimento (java.util.Date dataNascimento) {
//        java.util.Date dataNascimentoOld = this.dataNascimento;
		this.dataNascimento = dataNascimento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataNascimento", dataNascimentoOld, dataNascimento);
	}



	/**
	 * Return the value associated with the column: ds_area
	 */
	public java.lang.String getDescricaoArea () {
		return getPropertyValue(this, descricaoArea, PROP_DESCRICAO_AREA); 
	}

	/**
	 * Set the value related to the column: ds_area
	 * @param descricaoArea the ds_area value
	 */
	public void setDescricaoArea (java.lang.String descricaoArea) {
//        java.lang.String descricaoAreaOld = this.descricaoArea;
		this.descricaoArea = descricaoArea;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoArea", descricaoAreaOld, descricaoArea);
	}



	/**
	 * Return the value associated with the column: micro_area
	 */
	public java.lang.Long getMicroArea () {
		return getPropertyValue(this, microArea, PROP_MICRO_AREA); 
	}

	/**
	 * Set the value related to the column: micro_area
	 * @param microArea the micro_area value
	 */
	public void setMicroArea (java.lang.Long microArea) {
//        java.lang.Long microAreaOld = this.microArea;
		this.microArea = microArea;
//        this.getPropertyChangeSupport().firePropertyChange ("microArea", microAreaOld, microArea);
	}



	/**
	 * Return the value associated with the column: keyword
	 */
	public java.lang.String getKeyword () {
		return getPropertyValue(this, keyword, PROP_KEYWORD); 
	}

	/**
	 * Set the value related to the column: keyword
	 * @param keyword the keyword value
	 */
	public void setKeyword (java.lang.String keyword) {
//        java.lang.String keywordOld = this.keyword;
		this.keyword = keyword;
//        this.getPropertyChangeSupport().firePropertyChange ("keyword", keywordOld, keyword);
	}



	/**
	 * Return the value associated with the column: cd_endereco
	 */
	public br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus getEnderecoUsuarioCadsus () {
		return getPropertyValue(this, enderecoUsuarioCadsus, PROP_ENDERECO_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_endereco
	 * @param enderecoUsuarioCadsus the cd_endereco value
	 */
	public void setEnderecoUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus enderecoUsuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus enderecoUsuarioCadsusOld = this.enderecoUsuarioCadsus;
		this.enderecoUsuarioCadsus = enderecoUsuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("enderecoUsuarioCadsus", enderecoUsuarioCadsusOld, enderecoUsuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cd_domicilio
	 */
	public br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio getEnderecoDomicilio () {
		return getPropertyValue(this, enderecoDomicilio, PROP_ENDERECO_DOMICILIO); 
	}

	/**
	 * Set the value related to the column: cd_domicilio
	 * @param enderecoDomicilio the cd_domicilio value
	 */
	public void setEnderecoDomicilio (br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio enderecoDomicilio) {
//        br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio enderecoDomicilioOld = this.enderecoDomicilio;
		this.enderecoDomicilio = enderecoDomicilio;
//        this.getPropertyChangeSupport().firePropertyChange ("enderecoDomicilio", enderecoDomicilioOld, enderecoDomicilio);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus_responsavel
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsusResponsavel () {
		return getPropertyValue(this, usuarioCadsusResponsavel, PROP_USUARIO_CADSUS_RESPONSAVEL); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus_responsavel
	 * @param usuarioCadsusResponsavel the cd_usu_cadsus_responsavel value
	 */
	public void setUsuarioCadsusResponsavel (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusResponsavel) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusResponsavelOld = this.usuarioCadsusResponsavel;
		this.usuarioCadsusResponsavel = usuarioCadsusResponsavel;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsusResponsavel", usuarioCadsusResponsavelOld, usuarioCadsusResponsavel);
	}



	/**
	 * Return the value associated with the column: cod_cid
	 */
	public br.com.ksisolucoes.vo.basico.Cidade getCidade () {
		return getPropertyValue(this, cidade, PROP_CIDADE); 
	}

	/**
	 * Set the value related to the column: cod_cid
	 * @param cidade the cod_cid value
	 */
	public void setCidade (br.com.ksisolucoes.vo.basico.Cidade cidade) {
//        br.com.ksisolucoes.vo.basico.Cidade cidadeOld = this.cidade;
		this.cidade = cidade;
//        this.getPropertyChangeSupport().firePropertyChange ("cidade", cidadeOld, cidade);
	}



	/**
	 * Return the value associated with the column: cd_equipe_area
	 */
	public br.com.ksisolucoes.vo.basico.EquipeArea getEquipeArea () {
		return getPropertyValue(this, equipeArea, PROP_EQUIPE_AREA); 
	}

	/**
	 * Set the value related to the column: cd_equipe_area
	 * @param equipeArea the cd_equipe_area value
	 */
	public void setEquipeArea (br.com.ksisolucoes.vo.basico.EquipeArea equipeArea) {
//        br.com.ksisolucoes.vo.basico.EquipeArea equipeAreaOld = this.equipeArea;
		this.equipeArea = equipeArea;
//        this.getPropertyChangeSupport().firePropertyChange ("equipeArea", equipeAreaOld, equipeArea);
	}



	/**
	 * Return the value associated with the column: cd_eqp_micro_area
	 */
	public br.com.ksisolucoes.vo.basico.EquipeMicroArea getEquipeMicroArea () {
		return getPropertyValue(this, equipeMicroArea, PROP_EQUIPE_MICRO_AREA); 
	}

	/**
	 * Set the value related to the column: cd_eqp_micro_area
	 * @param equipeMicroArea the cd_eqp_micro_area value
	 */
	public void setEquipeMicroArea (br.com.ksisolucoes.vo.basico.EquipeMicroArea equipeMicroArea) {
//        br.com.ksisolucoes.vo.basico.EquipeMicroArea equipeMicroAreaOld = this.equipeMicroArea;
		this.equipeMicroArea = equipeMicroArea;
//        this.getPropertyChangeSupport().firePropertyChange ("equipeMicroArea", equipeMicroAreaOld, equipeMicroArea);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.cadsus.DominioEnderecoDomicilio)) return false;
		else {
			br.com.ksisolucoes.vo.cadsus.DominioEnderecoDomicilio dominioEnderecoDomicilio = (br.com.ksisolucoes.vo.cadsus.DominioEnderecoDomicilio) obj;
			if (null == this.getCodigo() || null == dominioEnderecoDomicilio.getCodigo()) return false;
			else return (this.getCodigo().equals(dominioEnderecoDomicilio.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}