package br.com.ksisolucoes.vo.prontuario.operacao;

import java.io.Serializable;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.operacao.base.BaseAtoOperatorioItem;



public class AtoOperatorioItem extends BaseAtoOperatorioItem implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public AtoOperatorioItem () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public AtoOperatorioItem (java.lang.Long codigo) {
		super(codigo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}