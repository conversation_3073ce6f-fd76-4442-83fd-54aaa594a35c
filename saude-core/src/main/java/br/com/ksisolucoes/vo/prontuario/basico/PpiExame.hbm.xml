<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico">
    <class name="PpiExame" table="ppi_exame">

        <id name="codigo" type="java.lang.Long" column="cd_ppi_exame">
            <generator class="sequence">
                <param name="sequence">ppi_exame_cd_ppi_exame_seq</param>
            </generator>
        </id>

        <version column="version" name="version" type="long"/>

        <many-to-one
                name="ppiTipoExame"
                class="br.com.ksisolucoes.vo.prontuario.basico.PpiTipoExame"
                column="cd_ppi_tipo_exame"
                not-null="true"
        />

        <many-to-one
                name="exameProcedimento"
                class="br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento"
                column="cd_exame_procedimento"
                not-null="true"
        />

        <property
                name="vlPpi"
                column="vl_ppi"
                type="java.lang.Double"
                not-null="true"
        />

        <property
                name="vlUsado"
                column="vl_usado"
                type="java.lang.Double"
                not-null="true"
        />

        <property
                name="stSituacao"
                column="st_situacao"
                type="java.lang.Long"
                not-null="true"
        />
    </class>
</hibernate-mapping>