/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.vo.entradas.estoque;

import java.util.Arrays;
import java.util.List;

import br.com.ksisolucoes.dao.HQLHelper;

/**
 *
 * <AUTHOR>
 */
public class ProdutoEstoqueEmpresaHQLBuilder {

    private HQLHelper hQLHelper;
    private String aliasProduto;
    private List<Long> codigosEmpresa;
    private String flagAtivo;
    private Long tipoEmpresa;

    public ProdutoEstoqueEmpresaHQLBuilder setFlagAtivo(String flagAtivo) {
        this.flagAtivo = flagAtivo;
        return this;
    }

    public ProdutoEstoqueEmpresaHQLBuilder setAliasProduto(String aliasProduto) {
        this.aliasProduto = aliasProduto;
        return this;
    }

    public ProdutoEstoqueEmpresaHQLBuilder setCodigoEmpresa(Long codigoEmpresa) {
        return setCodigosEmpresa(Arrays.<Long>asList(codigoEmpresa));
    }

    public ProdutoEstoqueEmpresaHQLBuilder setCodigosEmpresa(List<Long> codigosEmpresa) {
        this.codigosEmpresa = codigosEmpresa;
        return this;
    }

    public ProdutoEstoqueEmpresaHQLBuilder sethQLHelper(HQLHelper hQLHelper) {
        this.hQLHelper = hQLHelper;
        return this;
    }

    public ProdutoEstoqueEmpresaHQLBuilder setTipoEmpresa(Long tipoEmpresa) {
        this.tipoEmpresa = tipoEmpresa;
        return this;
    }

    public void build() {
        HQLHelper hqlSub = hQLHelper.getNewInstanceSubQuery();
        hqlSub.addToSelect("1");
        hqlSub.addToFrom("EstoqueEmpresa ee");
        hqlSub.addToWhereWhithAnd("ee.id.produto = " + aliasProduto);
        hqlSub.addToWhereWhithAnd("ee.id.empresa.codigo in ", this.codigosEmpresa);
        hqlSub.addToWhereWhithAnd("ee.id.empresa.tipoUnidade = ", this.tipoEmpresa);
        hqlSub.addToWhereWhithAnd("ee.flagAtivo = ", this.flagAtivo);

        hQLHelper.addToWhereWhithAnd("exists(" + hqlSub.getQuery() + ")");
    }
}
