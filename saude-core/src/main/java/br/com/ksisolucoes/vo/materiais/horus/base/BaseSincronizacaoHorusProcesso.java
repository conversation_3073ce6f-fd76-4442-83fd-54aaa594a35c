package br.com.ksisolucoes.vo.materiais.horus.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the horus_sincronizacao_processo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="horus_sincronizacao_processo"
 */

public abstract class BaseSincronizacaoHorusProcesso extends BaseRootVO implements Serializable {

	public static String REF = "SincronizacaoHorusProcesso";
	public static final String PROP_COMPETENCIA = "competencia";
	public static final String PROP_STATUS = "status";
	public static final String PROP_NUMERO_PROTOCOLO_RECEBIMENTO = "numeroProtocoloRecebimento";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_MENSAGEM_ERRO = "mensagemErro";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_DATA_GERACAO = "dataGeracao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";
	public static final String PROP_TIPO_SINCRONIZACAO = "tipoSincronizacao";
	public static final String PROP_NUMERO_PROTOCOLO = "numeroProtocolo";
	public static final String PROP_DATA_PROTOCOLO_RECEBIMENTO = "dataProtocoloRecebimento";


	// constructors
	public BaseSincronizacaoHorusProcesso () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseSincronizacaoHorusProcesso (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date competencia;
	private java.util.Date dataGeracao;
	private java.lang.Long status;
	private java.util.Date dataCancelamento;
	private java.lang.Long tipoSincronizacao;
	private java.lang.String mensagemErro;
	private java.lang.Long numeroProtocolo;
	private java.lang.String numeroProtocoloRecebimento;
	private java.lang.String dataProtocoloRecebimento;

	// many to one
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_horus_sincronizacao_processo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: competencia
	 */
	public java.util.Date getCompetencia () {
		return getPropertyValue(this, competencia, PROP_COMPETENCIA); 
	}

	/**
	 * Set the value related to the column: competencia
	 * @param competencia the competencia value
	 */
	public void setCompetencia (java.util.Date competencia) {
//        java.util.Date competenciaOld = this.competencia;
		this.competencia = competencia;
//        this.getPropertyChangeSupport().firePropertyChange ("competencia", competenciaOld, competencia);
	}



	/**
	 * Return the value associated with the column: dt_geracao
	 */
	public java.util.Date getDataGeracao () {
		return getPropertyValue(this, dataGeracao, PROP_DATA_GERACAO); 
	}

	/**
	 * Set the value related to the column: dt_geracao
	 * @param dataGeracao the dt_geracao value
	 */
	public void setDataGeracao (java.util.Date dataGeracao) {
//        java.util.Date dataGeracaoOld = this.dataGeracao;
		this.dataGeracao = dataGeracao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataGeracao", dataGeracaoOld, dataGeracao);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: tp_sincronizacao
	 */
	public java.lang.Long getTipoSincronizacao () {
		return getPropertyValue(this, tipoSincronizacao, PROP_TIPO_SINCRONIZACAO); 
	}

	/**
	 * Set the value related to the column: tp_sincronizacao
	 * @param tipoSincronizacao the tp_sincronizacao value
	 */
	public void setTipoSincronizacao (java.lang.Long tipoSincronizacao) {
//        java.lang.Long tipoSincronizacaoOld = this.tipoSincronizacao;
		this.tipoSincronizacao = tipoSincronizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoSincronizacao", tipoSincronizacaoOld, tipoSincronizacao);
	}



	/**
	 * Return the value associated with the column: msg_erro
	 */
	public java.lang.String getMensagemErro () {
		return getPropertyValue(this, mensagemErro, PROP_MENSAGEM_ERRO); 
	}

	/**
	 * Set the value related to the column: msg_erro
	 * @param mensagemErro the msg_erro value
	 */
	public void setMensagemErro (java.lang.String mensagemErro) {
//        java.lang.String mensagemErroOld = this.mensagemErro;
		this.mensagemErro = mensagemErro;
//        this.getPropertyChangeSupport().firePropertyChange ("mensagemErro", mensagemErroOld, mensagemErro);
	}



	/**
	 * Return the value associated with the column: num_protocolo
	 */
	public java.lang.Long getNumeroProtocolo () {
		return getPropertyValue(this, numeroProtocolo, PROP_NUMERO_PROTOCOLO); 
	}

	/**
	 * Set the value related to the column: num_protocolo
	 * @param numeroProtocolo the num_protocolo value
	 */
	public void setNumeroProtocolo (java.lang.Long numeroProtocolo) {
//        java.lang.Long numeroProtocoloOld = this.numeroProtocolo;
		this.numeroProtocolo = numeroProtocolo;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroProtocolo", numeroProtocoloOld, numeroProtocolo);
	}



	/**
	 * Return the value associated with the column: nr_protocolo_recebimento
	 */
	public java.lang.String getNumeroProtocoloRecebimento () {
		return getPropertyValue(this, numeroProtocoloRecebimento, PROP_NUMERO_PROTOCOLO_RECEBIMENTO); 
	}

	/**
	 * Set the value related to the column: nr_protocolo_recebimento
	 * @param numeroProtocoloRecebimento the nr_protocolo_recebimento value
	 */
	public void setNumeroProtocoloRecebimento (java.lang.String numeroProtocoloRecebimento) {
//        java.lang.String numeroProtocoloRecebimentoOld = this.numeroProtocoloRecebimento;
		this.numeroProtocoloRecebimento = numeroProtocoloRecebimento;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroProtocoloRecebimento", numeroProtocoloRecebimentoOld, numeroProtocoloRecebimento);
	}



	/**
	 * Return the value associated with the column: dt_protocolo_recebimento
	 */
	public java.lang.String getDataProtocoloRecebimento () {
		return getPropertyValue(this, dataProtocoloRecebimento, PROP_DATA_PROTOCOLO_RECEBIMENTO); 
	}

	/**
	 * Set the value related to the column: dt_protocolo_recebimento
	 * @param dataProtocoloRecebimento the dt_protocolo_recebimento value
	 */
	public void setDataProtocoloRecebimento (java.lang.String dataProtocoloRecebimento) {
//        java.lang.String dataProtocoloRecebimentoOld = this.dataProtocoloRecebimento;
		this.dataProtocoloRecebimento = dataProtocoloRecebimento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataProtocoloRecebimento", dataProtocoloRecebimentoOld, dataProtocoloRecebimento);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_usuario_can
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_can
	 * @param usuarioCancelamento the cd_usuario_can value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso)) return false;
		else {
			br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso sincronizacaoHorusProcesso = (br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso) obj;
			if (null == this.getCodigo() || null == sincronizacaoHorusProcesso.getCodigo()) return false;
			else return (this.getCodigo().equals(sincronizacaoHorusProcesso.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}