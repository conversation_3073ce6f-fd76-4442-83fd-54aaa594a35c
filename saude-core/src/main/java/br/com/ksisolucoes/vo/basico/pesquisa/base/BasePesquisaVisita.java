package br.com.ksisolucoes.vo.basico.pesquisa.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;

/**
 * This is an object that contains data related to the pesquisa_visita table. Do
 * not modify this class because it will be overwritten if the configuration
 * file related to this class is modified.
 *
 * @hibernate.class table="pesquisa_visita"
 */
public abstract class BasePesquisaVisita extends BaseRootVO implements Serializable {

    public static String REF = "PesquisaVisita";
    public static final String PROP_CODIGO = "codigo";
    public static final String PROP_IDADE = "idade";
    public static final String PROP_PERGUNTA_RESPOSTA = "perguntaResposta";
    public static final String PROP_PESQUISA_PERGUNTA = "pesquisaPergunta";
    public static final String PROP_EQUIPE_MICRO_AREA = "equipeMicroArea";
    public static final String PROP_VISITA_DOMICILIAR = "visitaDomiciliar";
    public static final String PROP_VERSION_ALL = "versionAll";
    public static final String PROP_SEXO = "sexo";

    // constructors
    public BasePesquisaVisita() {
        initialize();
    }

    /**
     * Constructor for primary key
     */
    public BasePesquisaVisita(java.lang.Long codigo) {
        this.setCodigo(codigo);
        initialize();
    }

    /**
     * Constructor for required fields
     */
    public BasePesquisaVisita(
            java.lang.Long codigo,
            java.lang.Long versionAll) {

        this.setCodigo(codigo);
        this.setVersionAll(versionAll);
        initialize();
    }

    protected void initialize() {
    }

    private int hashCode = Integer.MIN_VALUE;

    // primary key
    private java.lang.Long codigo;

    // fields
    private java.lang.Long versionAll;
    private java.lang.Long idade;
    private java.lang.String sexo;

    // many to one
    private br.com.ksisolucoes.vo.basico.pesquisa.PerguntaResposta perguntaResposta;
    private br.com.ksisolucoes.vo.cadsus.VisitaDomiciliar visitaDomiciliar;
    private br.com.ksisolucoes.vo.basico.pesquisa.PesquisaPergunta pesquisaPergunta;
    private br.com.ksisolucoes.vo.basico.EquipeMicroArea equipeMicroArea;

    /**
     * Return the unique identifier of this class
     *
     * @hibernate.id generator-class="assigned" column="cd_pesq_visita"
     */
    public java.lang.Long getCodigo() {
        return getPropertyValue(this, codigo, "codigo");
    }

    /**
     * Set the unique identifier of this class
     *
     * @param codigo the new ID
     */
    public void setCodigo(java.lang.Long codigo) {
        this.codigo = codigo;
        this.hashCode = Integer.MIN_VALUE;
    }

    /**
     * Return the value associated with the column: version_all
     */
    public java.lang.Long getVersionAll() {
        return getPropertyValue(this, versionAll, PROP_VERSION_ALL);
    }

    /**
     * Set the value related to the column: version_all
     *
     * @param versionAll the version_all value
     */
    public void setVersionAll(java.lang.Long versionAll) {
//        java.lang.Long versionAllOld = this.versionAll;
        this.versionAll = versionAll;
//        this.getPropertyChangeSupport().firePropertyChange ("versionAll", versionAllOld, versionAll);
    }

    /**
     * Return the value associated with the column: idade
     */
    public java.lang.Long getIdade() {
        return getPropertyValue(this, idade, PROP_IDADE);
    }

    /**
     * Set the value related to the column: idade
     *
     * @param idade the idade value
     */
    public void setIdade(java.lang.Long idade) {
//        java.lang.Long idadeOld = this.idade;
        this.idade = idade;
//        this.getPropertyChangeSupport().firePropertyChange ("idade", idadeOld, idade);
    }

    /**
     * Return the value associated with the column: sexo
     */
    public java.lang.String getSexo() {
        return getPropertyValue(this, sexo, PROP_SEXO);
    }

    /**
     * Set the value related to the column: sexo
     *
     * @param sexo the sexo value
     */
    public void setSexo(java.lang.String sexo) {
//        java.lang.String sexoOld = this.sexo;
        this.sexo = sexo;
//        this.getPropertyChangeSupport().firePropertyChange ("sexo", sexoOld, sexo);
    }

    /**
     * Return the value associated with the column: cd_resposta
     */
    public br.com.ksisolucoes.vo.basico.pesquisa.PerguntaResposta getPerguntaResposta() {
        return getPropertyValue(this, perguntaResposta, PROP_PERGUNTA_RESPOSTA);
    }

    /**
     * Set the value related to the column: cd_resposta
     *
     * @param perguntaResposta the cd_resposta value
     */
    public void setPerguntaResposta(br.com.ksisolucoes.vo.basico.pesquisa.PerguntaResposta perguntaResposta) {
//        br.com.ksisolucoes.vo.basico.pesquisa.PerguntaResposta perguntaRespostaOld = this.perguntaResposta;
        this.perguntaResposta = perguntaResposta;
//        this.getPropertyChangeSupport().firePropertyChange ("perguntaResposta", perguntaRespostaOld, perguntaResposta);
    }

    /**
     * Return the value associated with the column: cd_visita
     */
    public br.com.ksisolucoes.vo.cadsus.VisitaDomiciliar getVisitaDomiciliar() {
        return getPropertyValue(this, visitaDomiciliar, PROP_VISITA_DOMICILIAR);
    }

    /**
     * Set the value related to the column: cd_visita
     *
     * @param visitaDomiciliar the cd_visita value
     */
    public void setVisitaDomiciliar(br.com.ksisolucoes.vo.cadsus.VisitaDomiciliar visitaDomiciliar) {
//        br.com.ksisolucoes.vo.cadsus.VisitaDomiciliar visitaDomiciliarOld = this.visitaDomiciliar;
        this.visitaDomiciliar = visitaDomiciliar;
//        this.getPropertyChangeSupport().firePropertyChange ("visitaDomiciliar", visitaDomiciliarOld, visitaDomiciliar);
    }

    /**
     * Return the value associated with the column: cd_pesq_pergunta
     */
    public br.com.ksisolucoes.vo.basico.pesquisa.PesquisaPergunta getPesquisaPergunta() {
        return getPropertyValue(this, pesquisaPergunta, PROP_PESQUISA_PERGUNTA);
    }

    /**
     * Set the value related to the column: cd_pesq_pergunta
     *
     * @param pesquisaPergunta the cd_pesq_pergunta value
     */
    public void setPesquisaPergunta(br.com.ksisolucoes.vo.basico.pesquisa.PesquisaPergunta pesquisaPergunta) {
//        br.com.ksisolucoes.vo.basico.pesquisa.PesquisaPergunta pesquisaPerguntaOld = this.pesquisaPergunta;
        this.pesquisaPergunta = pesquisaPergunta;
//        this.getPropertyChangeSupport().firePropertyChange ("pesquisaPergunta", pesquisaPerguntaOld, pesquisaPergunta);
    }

    /**
     * Return the value associated with the column: cd_eqp_micro_area
     */
    public br.com.ksisolucoes.vo.basico.EquipeMicroArea getEquipeMicroArea() {
        return getPropertyValue(this, equipeMicroArea, PROP_EQUIPE_MICRO_AREA);
    }

    /**
     * Set the value related to the column: cd_eqp_micro_area
     *
     * @param equipeMicroArea the cd_eqp_micro_area value
     */
    public void setEquipeMicroArea(br.com.ksisolucoes.vo.basico.EquipeMicroArea equipeMicroArea) {
//        br.com.ksisolucoes.vo.basico.EquipeMicroArea equipeMicroAreaOld = this.equipeMicroArea;
        this.equipeMicroArea = equipeMicroArea;
//        this.getPropertyChangeSupport().firePropertyChange ("equipeMicroArea", equipeMicroAreaOld, equipeMicroArea);
    }

    public boolean equals(Object obj) {
        if (null == obj) {
            return false;
        }
        if (!(obj instanceof br.com.ksisolucoes.vo.basico.pesquisa.PesquisaVisita)) {
            return false;
        } else {
            br.com.ksisolucoes.vo.basico.pesquisa.PesquisaVisita pesquisaVisita = (br.com.ksisolucoes.vo.basico.pesquisa.PesquisaVisita) obj;
            if (null == this.getCodigo() || null == pesquisaVisita.getCodigo()) {
                return false;
            } else {
                return (this.getCodigo().equals(pesquisaVisita.getCodigo()));
            }
        }
    }

    public int hashCode() {
        if (Integer.MIN_VALUE == this.hashCode) {
            if (null == this.getCodigo()) {
                return super.hashCode();
            } else {
                String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
                this.hashCode = hashStr.hashCode();
            }
        }
        return this.hashCode;
    }

    public String toString() {
        return super.toString();
    }

    private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
        if (this.retornoValidacao == null) {
            this.retornoValidacao = new RetornoValidacao();
        }
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
        this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}
