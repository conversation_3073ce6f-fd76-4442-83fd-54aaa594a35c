<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.atendimento"  >

    <class name="BpaExameItem" table="bpa_exame_item">

        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_bpa_exame_item"
        >
            <generator class="sequence">
                <param name="sequence">seq_bpa</param>
            </generator>
        </id> 
        
        <version column="version" name="version" type="long" />

        <many-to-one class="Bpa" name="bpa">
            <column name="cd_bpa" />
        </many-to-one>

        <many-to-one class="br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExameItem" name="atendimentoExameItem">
            <column name="cd_ate_exa_item" />
        </many-to-one>
        
    </class>
</hibernate-mapping>
