<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.geral"  >

    <class name="PrestacaoContasEloIndicadorSimas" table="prestacao_contas_elo_indicador_simas">

        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_prestacao_contas_elo_indicador_simas"
        >
            <generator class="sequence">
                <param name="sequence">seq_prestacao_contas_elo_indicador_simas</param>
            </generator>
        </id> 
        
        <version column="version" name="version" type="long" />

        <many-to-one
                class="br.com.ksisolucoes.vo.geral.PrestacaoContasSimas"
                name="prestacaoContasSimas"
        >
            <column name="cd_prestacao_contas_simas"/>
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.geral.IndicadorSimas"
                name="indicadorSimas"
        >
            <column name="cd_indicador_simas"/>
        </many-to-one>

        <many-to-one class="br.com.ksisolucoes.vo.controle.Usuario" name="usuario">
            <column name="cd_usuario" />
        </many-to-one>

        <property
            column="dt_cadastro"
            name="dataCadastro"
            type="timestamp"
        />

        <property
                column="dt_alteracao"
                name="dataAlteracao"
                type="timestamp"
        />
		 
    </class>
</hibernate-mapping>
