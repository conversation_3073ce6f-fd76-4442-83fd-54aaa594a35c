package br.com.ksisolucoes.vo.vigilancia.requerimentos;

import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.StringUtilKsi;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.base.BaseRequerimentoProjetoHidrossanitarioDeclaratorioParecer;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.enums.RequerimentosProjetosEnums;
import org.apache.commons.lang3.StringEscapeUtils;

import java.io.Serializable;



public class RequerimentoProjetoHidrossanitarioDeclaratorioParecer extends BaseRequerimentoProjetoHidrossanitarioDeclaratorioParecer implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequerimentoProjetoHidrossanitarioDeclaratorioParecer () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequerimentoProjetoHidrossanitarioDeclaratorioParecer (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequerimentoProjetoHidrossanitarioDeclaratorioParecer (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioDeclaratorio requerimentoProjetoHidrossanitarioDeclaratorio,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataParecer,
		java.util.Date dataCadastro) {

		super (
			codigo,
			requerimentoProjetoHidrossanitarioDeclaratorio,
			usuario,
			dataParecer,
			dataCadastro);
	}

/*[CONSTRUCTOR MARKER END]*/

	public String getDescricaoTipoAprovacao() {
        RequerimentosProjetosEnums.TipoAprovacao tipoAprovacao = RequerimentosProjetosEnums.TipoAprovacao.valueOf(this.getTipoAprovacao());
		if (tipoAprovacao != null) {
			return tipoAprovacao.descricao();
		}
		return Bundle.getStringApplication("rotulo_desconhecido");
	}

	public String getDescricaoStatus() {
		if (getStatus() != null) {
			RequerimentoVigilancia.Situacao situacao = RequerimentoVigilancia.Situacao.valeuOf(getStatus());
			if (situacao != null) {
				return situacao.descricao();
			}
		}
		return "";
	}

	@Override
	public String getDescricaoParecer() {
		return StringUtil.limparHtml(super.getDescricaoParecer());
	}

	@Override
	public void setDescricaoParecer(String descricaoParecer) {
		super.setDescricaoParecer(StringUtil.limparHtml(descricaoParecer));
	}

	public String getDescricaoParecerSemHtml() {
		if(getDescricaoParecer() != null){
			return StringUtilKsi.removeHtmlString(StringEscapeUtils.unescapeHtml4(getDescricaoParecer()));
		}
		return "";
	}

	public String getRodape() {
		StringBuilder msg = new StringBuilder();

		if (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(getStatus())) {
			String realContext = TenantContext.getRealContext();
			if (realContext.equals("localhost")) {
				realContext = realContext.concat(":8080");
			}

			msg.append("IMPORTANTE")
					.append("\n\n1 - Toda e qualquer alteração no projeto, além das solicitadas nos laudos emitidos pelos analistas, deve ser informada por meio de documento assinado pelo responsável técnico pelo projeto, quando da reapresentação do processo;")
					.append("\n2 - O processo deve ser retornado para a VISA em até 90 dias a partir da data da sua análise*;")
					.append("\n3 - Cada processo tem direito a uma primeira análise e mais três reanálises*;")
					.append("\n4 - O não cumprimento dos itens 2 e 3 acima acarreta no indeferimento e arquivamento do processo*;")
					.append("\n5 - Para acompanhar o andamento do processo, consulte a página: ")
					.append(realContext).append("/vigilancia/")
					.append(", clique no botão consulta de requerimento e informe a senha ")
					.append(this.getRequerimentoProjetoHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getCodigo()).append(".")
					.append("\n\n* Decreto Municipal nº 14793/15");

		} else if (RequerimentoVigilancia.Situacao.INDEFERIDO.value().equals(getStatus())) {
			msg.append("1 - Em caso de arquivamento do processo por indeferimento, o requerente deverá dar entrada em novo Processo de Análide Declaratória de Projeto Hidrossanitário, não havendo a possibilidade de desarquivamento do processo anterior para a sua continuidade.")
					.append("\n2 - Para a solicitação de reconsideração de parecer de indeferimento o requerente deve protocolar processo de solicitação de parecer técnico e direcionar o pedido para a COMATS.");
		} else if (RequerimentoVigilancia.Situacao.DEFERIDO.value().equals(getStatus())) {
			msg.append("“A aprovação do Projeto Hidrossanitário não licencia, por si só, a construção ou a instalação das atividades, devendo o requerente providenciar as demais licenças e/ou autorizações junto aos órgãos competentes.”");
		}

		return msg.toString();
	}

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}