package br.com.ksisolucoes.vo.vigilancia.autodepenalidade;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.base.BaseAutoPenalidadeItem;

import java.io.Serializable;



public class AutoPenalidadeItem extends BaseAutoPenalidadeItem implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public AutoPenalidadeItem () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public AutoPenalidadeItem (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public AutoPenalidadeItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.autodepenalidade.TipoPenalidade tipoPenalidade,
		java.lang.Double valorMulta) {

		super (
			codigo,
			tipoPenalidade,
			valorMulta);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}