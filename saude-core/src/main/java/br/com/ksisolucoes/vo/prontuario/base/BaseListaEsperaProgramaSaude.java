package br.com.ksisolucoes.vo.prontuario.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the lista_espera_programa_saude table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="lista_espera_programa_saude"
 */

public abstract class BaseListaEsperaProgramaSaude extends BaseRootVO implements Serializable {

	public static String REF = "ListaEsperaProgramaSaude";
	public static final String PROP_PRONTUARIO_EVENTOS = "prontuarioEventos";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_MOTIVO_CANCELAMENTO = "motivoCancelamento";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";
	public static final String PROP_DATA_ATENDIMENTO = "dataAtendimento";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_STATUS = "status";
	public static final String PROP_NUMERO_ATENDIMENTO_PROGRAMA_SAUDE = "numeroAtendimentoProgramaSaude";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_DATA_CHEGADA = "dataChegada";
	public static final String PROP_ID = "id";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_OBSERVACAO_CANCELAMENTO = "observacaoCancelamento";
	public static final String PROP_TIPO_PROGRAMA_SAUDE = "tipoProgramaSaude";


	// constructors
	public BaseListaEsperaProgramaSaude () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseListaEsperaProgramaSaude (br.com.ksisolucoes.vo.prontuario.ListaEsperaProgramaSaudePK id) {
		this.setId(id);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseListaEsperaProgramaSaude (
		br.com.ksisolucoes.vo.prontuario.ListaEsperaProgramaSaudePK id,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.String tipoProgramaSaude,
		java.util.Date dataChegada,
		java.lang.Long status) {

		this.setId(id);
		this.setUsuarioCadsus(usuarioCadsus);
		this.setUsuario(usuario);
		this.setTipoProgramaSaude(tipoProgramaSaude);
		this.setDataChegada(dataChegada);
		this.setStatus(status);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private br.com.ksisolucoes.vo.prontuario.ListaEsperaProgramaSaudePK id;

	// fields
	private java.lang.String tipoProgramaSaude;
	private java.util.Date dataChegada;
	private java.util.Date dataAtendimento;
	private java.lang.Long status;
	private java.util.Date dataCancelamento;
	private java.lang.String observacaoCancelamento;
	private java.lang.Long numeroAtendimentoProgramaSaude;
	private java.lang.String observacao;

	// many to one
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.basico.MotivoCancelamento motivoCancelamento;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;
	private br.com.ksisolucoes.vo.prontuario.emergencia.ProntuarioEventos prontuarioEventos;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     */
	public br.com.ksisolucoes.vo.prontuario.ListaEsperaProgramaSaudePK getId () {
	    return getPropertyValue(this,  id, "id" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param id the new ID
	 */
	public void setId (br.com.ksisolucoes.vo.prontuario.ListaEsperaProgramaSaudePK id) {
		this.id = id;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: programa_saude
	 */
	public java.lang.String getTipoProgramaSaude () {
		return getPropertyValue(this, tipoProgramaSaude, PROP_TIPO_PROGRAMA_SAUDE); 
	}

	/**
	 * Set the value related to the column: programa_saude
	 * @param tipoProgramaSaude the programa_saude value
	 */
	public void setTipoProgramaSaude (java.lang.String tipoProgramaSaude) {
//        java.lang.String tipoProgramaSaudeOld = this.tipoProgramaSaude;
		this.tipoProgramaSaude = tipoProgramaSaude;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoProgramaSaude", tipoProgramaSaudeOld, tipoProgramaSaude);
	}



	/**
	 * Return the value associated with the column: dt_chegada
	 */
	public java.util.Date getDataChegada () {
		return getPropertyValue(this, dataChegada, PROP_DATA_CHEGADA); 
	}

	/**
	 * Set the value related to the column: dt_chegada
	 * @param dataChegada the dt_chegada value
	 */
	public void setDataChegada (java.util.Date dataChegada) {
//        java.util.Date dataChegadaOld = this.dataChegada;
		this.dataChegada = dataChegada;
//        this.getPropertyChangeSupport().firePropertyChange ("dataChegada", dataChegadaOld, dataChegada);
	}



	/**
	 * Return the value associated with the column: dt_atendimento
	 */
	public java.util.Date getDataAtendimento () {
		return getPropertyValue(this, dataAtendimento, PROP_DATA_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: dt_atendimento
	 * @param dataAtendimento the dt_atendimento value
	 */
	public void setDataAtendimento (java.util.Date dataAtendimento) {
//        java.util.Date dataAtendimentoOld = this.dataAtendimento;
		this.dataAtendimento = dataAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAtendimento", dataAtendimentoOld, dataAtendimento);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: ds_obs_cancelamento
	 */
	public java.lang.String getObservacaoCancelamento () {
		return getPropertyValue(this, observacaoCancelamento, PROP_OBSERVACAO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: ds_obs_cancelamento
	 * @param observacaoCancelamento the ds_obs_cancelamento value
	 */
	public void setObservacaoCancelamento (java.lang.String observacaoCancelamento) {
//        java.lang.String observacaoCancelamentoOld = this.observacaoCancelamento;
		this.observacaoCancelamento = observacaoCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("observacaoCancelamento", observacaoCancelamentoOld, observacaoCancelamento);
	}



	/**
	 * Return the value associated with the column: nr_atend_prg_saude
	 */
	public java.lang.Long getNumeroAtendimentoProgramaSaude () {
		return getPropertyValue(this, numeroAtendimentoProgramaSaude, PROP_NUMERO_ATENDIMENTO_PROGRAMA_SAUDE); 
	}

	/**
	 * Set the value related to the column: nr_atend_prg_saude
	 * @param numeroAtendimentoProgramaSaude the nr_atend_prg_saude value
	 */
	public void setNumeroAtendimentoProgramaSaude (java.lang.Long numeroAtendimentoProgramaSaude) {
//        java.lang.Long numeroAtendimentoProgramaSaudeOld = this.numeroAtendimentoProgramaSaude;
		this.numeroAtendimentoProgramaSaude = numeroAtendimentoProgramaSaude;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroAtendimentoProgramaSaude", numeroAtendimentoProgramaSaudeOld, numeroAtendimentoProgramaSaude);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cod_motivo
	 */
	public br.com.ksisolucoes.vo.basico.MotivoCancelamento getMotivoCancelamento () {
		return getPropertyValue(this, motivoCancelamento, PROP_MOTIVO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: cod_motivo
	 * @param motivoCancelamento the cod_motivo value
	 */
	public void setMotivoCancelamento (br.com.ksisolucoes.vo.basico.MotivoCancelamento motivoCancelamento) {
//        br.com.ksisolucoes.vo.basico.MotivoCancelamento motivoCancelamentoOld = this.motivoCancelamento;
		this.motivoCancelamento = motivoCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoCancelamento", motivoCancelamentoOld, motivoCancelamento);
	}



	/**
	 * Return the value associated with the column: cd_usuario_can
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_can
	 * @param usuarioCancelamento the cd_usuario_can value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}



	/**
	 * Return the value associated with the column: id_prontuario_evento
	 */
	public br.com.ksisolucoes.vo.prontuario.emergencia.ProntuarioEventos getProntuarioEventos () {
		return getPropertyValue(this, prontuarioEventos, PROP_PRONTUARIO_EVENTOS); 
	}

	/**
	 * Set the value related to the column: id_prontuario_evento
	 * @param prontuarioEventos the id_prontuario_evento value
	 */
	public void setProntuarioEventos (br.com.ksisolucoes.vo.prontuario.emergencia.ProntuarioEventos prontuarioEventos) {
//        br.com.ksisolucoes.vo.prontuario.emergencia.ProntuarioEventos prontuarioEventosOld = this.prontuarioEventos;
		this.prontuarioEventos = prontuarioEventos;
//        this.getPropertyChangeSupport().firePropertyChange ("prontuarioEventos", prontuarioEventosOld, prontuarioEventos);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.ListaEsperaProgramaSaude)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.ListaEsperaProgramaSaude listaEsperaProgramaSaude = (br.com.ksisolucoes.vo.prontuario.ListaEsperaProgramaSaude) obj;
			if (null == this.getId() || null == listaEsperaProgramaSaude.getId()) return false;
			else return (this.getId().equals(listaEsperaProgramaSaude.getId()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getId()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getId().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}