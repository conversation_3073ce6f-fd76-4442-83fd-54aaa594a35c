<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.materiais.horus"  >
    <class 
        name="SincronizacaoHorusProcessoEnvio"
        table="horus_sincronizacao_proc_envio"
    >
	
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_horus_sincronizacao_proc_envio"
        >
            <generator class="assigned"/>
        </id> 
        <version column="version" name="version" type="long" />

        <many-to-one class="br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso" name="sincronizacaoHorusProcesso">
            <column name="cd_horus_sincronizacao_processo" />
        </many-to-one>

        <property
            column="status"
            name="status"
            type="java.lang.Long"
        />

        <property
            column="msg_erro"
            name="mensagemErro"
            type="java.lang.String"
        />

        <property
            column="num_protocolo"
            name="numeroProtocolo"
            type="java.lang.Long"
        />

        <property
            column="nr_protocolo_recebimento"
            name="numeroProtocoloRecebimento"
            type="java.lang.String"
            length="20"
        />

        <property
            column="dt_protocolo_recebimento"
            name="dataProtocoloRecebimento"
            type="java.lang.String"
            length="20"
        />
    </class>
</hibernate-mapping>