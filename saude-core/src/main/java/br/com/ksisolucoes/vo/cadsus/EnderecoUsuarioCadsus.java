package br.com.ksisolucoes.vo.cadsus;

import br.com.celk.integracao.IntegracaoRest;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.parametrogem.ParametroPanelConsultaBean;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.cadsus.base.BaseEnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import org.apache.commons.lang.StringUtils;

import javax.swing.text.MaskFormatter;
import java.io.Serializable;
import java.text.ParseException;

@ParametroPanelConsultaBean("br.com.celk.view.cadsus.enderecousuariocadsus.AutoCompleteConsultaEnderecoUsuarioCadsus")
@IntegracaoRest
public class EnderecoUsuarioCadsus extends BaseEnderecoUsuarioCadsus implements CodigoManager, PesquisaObjectInterface {

    private static final long serialVersionUID = 1L;
    public static Long ST_EXCLUIDO_NAO = RepositoryComponentDefault.NAO_EXCLUIDO;
    public static Long ST_EXCLUIDO_SIM = RepositoryComponentDefault.EXCLUIDO;
    public static Long ST_ATIVO_SIM = RepositoryComponentDefault.ATIVO;
    public static Long ST_ATIVO_NAO = RepositoryComponentDefault.INATIVO;
    public static final String PROP_TELEFONE_FORMATADO = "telefoneFormatado";
    public static final String PROP_TELEFONE_REFERENCIA_FORMATADO = "telefoneReferenciaFormatado";
    public static final String PROP_CIDADE_FORMATADO = "cidadeFormatado";

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public EnderecoUsuarioCadsus () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public EnderecoUsuarioCadsus (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public EnderecoUsuarioCadsus (
		java.lang.Long codigo,
		java.lang.String logradouro) {

		super (
			codigo,
			logradouro);
	}

    /*[CONSTRUCTOR MARKER END]*/
    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public void setCodigoManager(Serializable srlzbl) {
        this.setCodigo((Long) srlzbl);
    }

    public String getCepFormatado() {
        try {
            if (StringUtils.trimToNull(getCep()) != null) {
                MaskFormatter m = new MaskFormatter("#####-###");
                m.setValueContainsLiteralCharacters(false);

                return m.valueToString(getCep().replaceAll("[^0-9]", ""));
            }
        } catch (ParseException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        return "";
    }

    public String getRuaFormatada() {
        StringBuilder sb = new StringBuilder();
        if (this.getTipoLogradouro() != null && this.getTipoLogradouro().getDescricao() != null) {
            sb.append(this.getTipoLogradouro().getDescricao());
            sb.append(" ");
        }
        if (this.getLogradouro() != null) {
            sb.append(this.getLogradouro());
        }
        if (this.getNumeroLogradouro() != null) {
            sb.append(", ");
            sb.append(this.getNumeroLogradouro());
        }
        return sb.toString();
    }

    public String getRuaFormatadaSemNumero() {
        StringBuilder sb = new StringBuilder();
        if (this.getTipoLogradouro() != null && this.getTipoLogradouro().getDescricao() != null) {
            sb.append(this.getTipoLogradouro().getDescricao());
            sb.append(" ");
        }
        if (this.getLogradouro() != null) {
            sb.append(this.getLogradouro());
        }
        return sb.toString();
    }

    public String getRuaFormatadaComComplemento() {
        StringBuilder sb = new StringBuilder();
        sb.append(getRuaFormatada());

        if (this.getComplementoLogradouro() != null) {
            sb.append(" ");
            sb.append(this.getComplementoLogradouro());
        }
        return sb.toString();
    }

    public String getEnderecoFormatado() {
        StringBuilder sb = new StringBuilder();
        sb.append(getRuaFormatada());

        if (this.getBairro() != null) {
            sb.append(", ");
            sb.append(this.getBairro());
        }
        return sb.toString();
    }
    
    public String getEnderecoComplementoFormatado() {
        StringBuilder sb = new StringBuilder();
        sb.append(getRuaFormatadaComComplemento());

        if (this.getBairro() != null) {
            sb.append(", ");
            sb.append(this.getBairro());
        }
        return sb.toString();
    }

    public String getEnderecoComplementoComCepFormatado() {
        StringBuilder sb = new StringBuilder();
        sb.append(getEnderecoComplementoFormatado());

        if (this.getCep() != null) {
            sb.append(", ");
            sb.append(this.getCepFormatado());
        }
        return sb.toString();
    }

    public String getEnderecoFormatadoSemNumero() {
        StringBuilder sb = new StringBuilder();
        sb.append(getRuaFormatadaSemNumero());

        if (this.getBairro() != null) {
            sb.append(", ");
            sb.append(this.getBairro());
        }
        return sb.toString();
    }

    public String getEnderecoFormatadoComCidade() {
        StringBuilder sb = new StringBuilder();
        sb.append(getEnderecoFormatado());

        if (getCidade() != null) {
            sb.append(", ");
            sb.append(Coalesce.asString(getCidade().getDescricao()));
            if (getCidade().getEstado() != null) {
                sb.append(" - ");
                sb.append(Coalesce.asString(getCidade().getEstado().getSigla()));
            }
        }

        return sb.toString();
    }

    public String getEnderecoComplementoFormatadoComCidade() {
        StringBuilder sb = new StringBuilder();
        sb.append(getEnderecoComplementoFormatado());

        if (getCidade() != null) {
            sb.append(", ");
            sb.append(Coalesce.asString(getCidade().getDescricao()));
            if (getCidade().getEstado() != null) {
                sb.append(" - ");
                sb.append(Coalesce.asString(getCidade().getEstado().getSigla()));
            }
        }
        return sb.toString();
    }

    public String getEnderecoFormatadoComLocalReferencia() {
        StringBuilder sb = new StringBuilder();
        sb.append(getEnderecoComplementoFormatadoComCidade());

        if (getPontoReferencia() != null) {
            sb.append(" / ");
            sb.append(getPontoReferencia());
        }
        return sb.toString();
    }

    public String getCidadeFormatado() {
        StringBuilder sb = new StringBuilder();
        
        if (getCidade() != null) {
            sb.append(Coalesce.asString(getCidade().getDescricao()));
            if (getCidade().getEstado() != null) {
                sb.append(" - ");
                sb.append(Coalesce.asString(getCidade().getEstado().getSigla()));
            }
        }

        return sb.toString();
    }

    public String getTelefoneFormatado() {
        return Util.getTelefoneFormatado(getTelefone());
    }

    public String getTelefoneReferenciaFormatado() {
        return Util.getTelefoneFormatado(getTelefoneReferencia());
    }

    @Override
    public String getDescricaoVO() {
        return getEnderecoFormatadoComCidade();
    }

    @Override
    public String getIdentificador() {
        return Coalesce.asString(getCodigo());
    }

    @Override
    public String getLogradouro() {
        return lgpdFilterText(super.getLogradouro());
    }

    @Override
    public String getNumeroLogradouro() {
        return lgpdFilterNumberAsText(super.getNumeroLogradouro());
    }

    @Override
    public String getTelefone() {
        return lgpdFilterNumberAsText(super.getTelefone());
    }

    @Override
    public String getTelefoneReferencia() {
        return lgpdFilterNumberAsText(super.getTelefoneReferencia());
    }

    @Override
    public String getLote() {
        return lgpdFilterNumberAsText(super.getLote());
    }

}
