package br.com.ksisolucoes.vo.agendamento;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import java.io.Serializable;

import br.com.ksisolucoes.vo.agendamento.base.BaseAgendaCota;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class AgendaCota extends BaseAgendaCota implements CodigoManager {
	private static final long serialVersionUID = 1L;
        
        public static final String PROP_DESCRICAO_TIPO_COTA = "descricaoTipoCota";

        public enum TipoCota implements IEnum {
            DIARIA(1L, Bundle.getStringApplication("rotulo_diaria")),
            SEMANAL(2L, Bundle.getStringApplication("rotulo_semanal")),
            MENSAL(3L, Bundle.getStringApplication("rotulo_mensal")),
            ;

            private Long value;
            private String descricao;

            private TipoCota(Long value, String descricao) {
                this.value = value;
                this.descricao = descricao;
            }

            public static TipoCota valeuOf(Long value) {
                for (TipoCota status : TipoCota.values()) {
                    if (status.value().equals(value)) {
                        return status;
                    }
                }
                return null;
            }

            @Override
            public Long value() {
                return value;
            }

            @Override
            public String descricao() {
                return descricao;
            }

        }
        
/*[CONSTRUCTOR MARKER BEGIN]*/
	public AgendaCota () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public AgendaCota (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public AgendaCota (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimento,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		java.lang.Long quantidadeCota,
		java.lang.Long tipoCota) {

		super (
			codigo,
			tipoProcedimento,
			empresa,
			quantidadeCota,
			tipoCota);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public String getDescricaoTipoCota(){
        TipoCota tipoCota = TipoCota.valeuOf(getTipoCota());
        if (tipoCota != null && tipoCota.descricao != null) {
            return tipoCota.descricao();
        }
        return "";
    }
}