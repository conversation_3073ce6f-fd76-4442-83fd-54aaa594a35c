<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.cadsus"  >
	<class
		name="TipoLogradouroCadsus"
		table="tipo_logradouro_cadsus"
	>
		<id
			name="codigo"
			type="java.lang.Long"
			column="cd_tipo_logradouro"
		>
			<generator class="assigned" />
		</id> <version column="version" name="version" type="long" />

		<property
			name="descricao"
			column="ds_tipo_logradouro"
			type="string"
			not-null="true"
			length="72"
		/>

		<property
			name="sigla"
			column="ds_sigla_logradouro"
			type="string"
			not-null="false"
			length="15"
		/>
		
		<property
			name="versionAll"
			column="version_all"
			type="java.lang.Long"
			not-null="true"
		/>
		
	</class>
</hibernate-mapping>