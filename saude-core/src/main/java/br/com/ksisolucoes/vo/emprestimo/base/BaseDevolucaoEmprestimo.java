package br.com.ksisolucoes.vo.emprestimo.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the devolucao_emprestimo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="devolucao_emprestimo"
 */

public abstract class BaseDevolucaoEmprestimo extends BaseRootVO implements Serializable {

	public static String REF = "DevolucaoEmprestimo";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_STATUS = "status";
	public static final String PROP_TIPO_DEVOLUCAO = "tipoDevolucao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_DATA_DEVOLUCAO = "dataDevolucao";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_EMPRESA_DEVOLUCAO = "empresaDevolucao";
	public static final String PROP_NOME_PACIENTE_ESTABELECIMENTO = "nomePacienteEstabelecimento";
	public static final String PROP_OBSERVACAO = "observacao";


	// constructors
	public BaseDevolucaoEmprestimo () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseDevolucaoEmprestimo (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseDevolucaoEmprestimo (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataDevolucao,
		java.util.Date dataCadastro,
		java.lang.Long status,
		java.lang.String nomePacienteEstabelecimento) {

		this.setCodigo(codigo);
		this.setEmpresa(empresa);
		this.setUsuario(usuario);
		this.setDataDevolucao(dataDevolucao);
		this.setDataCadastro(dataCadastro);
		this.setStatus(status);
		this.setNomePacienteEstabelecimento(nomePacienteEstabelecimento);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataDevolucao;
	private java.util.Date dataCadastro;
	private java.lang.String observacao;
	private java.lang.Long status;
	private java.lang.String nomePacienteEstabelecimento;

	// many to one
	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private br.com.ksisolucoes.vo.emprestimo.TipoDevolucao tipoDevolucao;
	private br.com.ksisolucoes.vo.basico.Empresa empresaDevolucao;
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_dev_emprestimo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_devolucao
	 */
	public java.util.Date getDataDevolucao () {
		return getPropertyValue(this, dataDevolucao, PROP_DATA_DEVOLUCAO); 
	}

	/**
	 * Set the value related to the column: dt_devolucao
	 * @param dataDevolucao the dt_devolucao value
	 */
	public void setDataDevolucao (java.util.Date dataDevolucao) {
//        java.util.Date dataDevolucaoOld = this.dataDevolucao;
		this.dataDevolucao = dataDevolucao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataDevolucao", dataDevolucaoOld, dataDevolucao);
	}



	/**
	 * Return the value associated with the column: dt_cadatro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadatro
	 * @param dataCadastro the dt_cadatro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: nm_paciente_estabelecimento
	 */
	public java.lang.String getNomePacienteEstabelecimento () {
		return getPropertyValue(this, nomePacienteEstabelecimento, PROP_NOME_PACIENTE_ESTABELECIMENTO); 
	}

	/**
	 * Set the value related to the column: nm_paciente_estabelecimento
	 * @param nomePacienteEstabelecimento the nm_paciente_estabelecimento value
	 */
	public void setNomePacienteEstabelecimento (java.lang.String nomePacienteEstabelecimento) {
//        java.lang.String nomePacienteEstabelecimentoOld = this.nomePacienteEstabelecimento;
		this.nomePacienteEstabelecimento = nomePacienteEstabelecimento;
//        this.getPropertyChangeSupport().firePropertyChange ("nomePacienteEstabelecimento", nomePacienteEstabelecimentoOld, nomePacienteEstabelecimento);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: cd_tp_devolucao
	 */
	public br.com.ksisolucoes.vo.emprestimo.TipoDevolucao getTipoDevolucao () {
		return getPropertyValue(this, tipoDevolucao, PROP_TIPO_DEVOLUCAO); 
	}

	/**
	 * Set the value related to the column: cd_tp_devolucao
	 * @param tipoDevolucao the cd_tp_devolucao value
	 */
	public void setTipoDevolucao (br.com.ksisolucoes.vo.emprestimo.TipoDevolucao tipoDevolucao) {
//        br.com.ksisolucoes.vo.emprestimo.TipoDevolucao tipoDevolucaoOld = this.tipoDevolucao;
		this.tipoDevolucao = tipoDevolucao;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDevolucao", tipoDevolucaoOld, tipoDevolucao);
	}



	/**
	 * Return the value associated with the column: empresa_devolucao
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresaDevolucao () {
		return getPropertyValue(this, empresaDevolucao, PROP_EMPRESA_DEVOLUCAO); 
	}

	/**
	 * Set the value related to the column: empresa_devolucao
	 * @param empresaDevolucao the empresa_devolucao value
	 */
	public void setEmpresaDevolucao (br.com.ksisolucoes.vo.basico.Empresa empresaDevolucao) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaDevolucaoOld = this.empresaDevolucao;
		this.empresaDevolucao = empresaDevolucao;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaDevolucao", empresaDevolucaoOld, empresaDevolucao);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimo)) return false;
		else {
			br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimo devolucaoEmprestimo = (br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimo) obj;
			if (null == this.getCodigo() || null == devolucaoEmprestimo.getCodigo()) return false;
			else return (this.getCodigo().equals(devolucaoEmprestimo.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}