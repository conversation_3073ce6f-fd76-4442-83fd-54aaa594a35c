package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.celk.integracao.IntegracaoRest;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import java.io.Serializable;

import br.com.ksisolucoes.vo.prontuario.basico.base.BaseAnexoPaciente;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

@IntegracaoRest
public class AnexoPaciente extends BaseAnexoPaciente implements CodigoManager {

    private static final long serialVersionUID = 1L;

    public static enum Status implements IEnum {

        NORMAL(0L, Bundle.getStringApplication("rotulo_normal")),
        CANCELADO(1L, Bundle.getStringApplication("rotulo_cancelado"));

        private Long value;
        private String descricao;

        private Status(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public Long value() {
            return this.value;
        }

        @Override
        public String toString() {
            return this.descricao;
        }
    }

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public AnexoPaciente () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public AnexoPaciente (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public AnexoPaciente (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.prontuario.basico.TipoAnexo tipoAnexo,
		java.lang.Long status,
		java.util.Date dataCadastro,
		java.util.Date dataUsuario,
		java.lang.Long versionAll) {

		super (
			codigo,
			usuarioCadsus,
			usuario,
			tipoAnexo,
			status,
			dataCadastro,
			dataUsuario,
			versionAll);
	}

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}
