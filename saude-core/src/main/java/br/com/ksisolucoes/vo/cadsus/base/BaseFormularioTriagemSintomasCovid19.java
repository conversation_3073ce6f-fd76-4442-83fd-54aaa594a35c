package br.com.ksisolucoes.vo.cadsus.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the formulario_triagem_sintomas_covid19 table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="formulario_triagem_sintomas_covid19"
 */

public abstract class BaseFormularioTriagemSintomasCovid19 extends BaseRootVO implements Serializable {

	public static String REF = "FormularioTriagemSintomasCovid19";
	public static final String PROP_SINTOMA_COVID19 = "sintomaCovid19";
	public static final String PROP_FORMULARIO_TRIAGEM_COVID19 = "formularioTriagemCovid19";
	public static final String PROP_CODIGO = "codigo";


	// constructors
	public BaseFormularioTriagemSintomasCovid19 () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseFormularioTriagemSintomasCovid19 (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseFormularioTriagemSintomasCovid19 (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.FormularioTriagemCovid19 formularioTriagemCovid19,
		br.com.ksisolucoes.vo.cadsus.SintomasCovid19 sintomaCovid19) {

		this.setCodigo(codigo);
		this.setFormularioTriagemCovid19(formularioTriagemCovid19);
		this.setSintomaCovid19(sintomaCovid19);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// many to one
	private br.com.ksisolucoes.vo.cadsus.FormularioTriagemCovid19 formularioTriagemCovid19;
	private br.com.ksisolucoes.vo.cadsus.SintomasCovid19 sintomaCovid19;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_formulario_triagem_sintomas_covid19"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_formulario_triagem_covid19
	 */
	public br.com.ksisolucoes.vo.cadsus.FormularioTriagemCovid19 getFormularioTriagemCovid19 () {
		return getPropertyValue(this, formularioTriagemCovid19, PROP_FORMULARIO_TRIAGEM_COVID19); 
	}

	/**
	 * Set the value related to the column: cd_formulario_triagem_covid19
	 * @param formularioTriagemCovid19 the cd_formulario_triagem_covid19 value
	 */
	public void setFormularioTriagemCovid19 (br.com.ksisolucoes.vo.cadsus.FormularioTriagemCovid19 formularioTriagemCovid19) {
//        br.com.ksisolucoes.vo.cadsus.FormularioTriagemCovid19 formularioTriagemCovid19Old = this.formularioTriagemCovid19;
		this.formularioTriagemCovid19 = formularioTriagemCovid19;
//        this.getPropertyChangeSupport().firePropertyChange ("formularioTriagemCovid19", formularioTriagemCovid19Old, formularioTriagemCovid19);
	}



	/**
	 * Return the value associated with the column: cd_sintomas_covid19
	 */
	public br.com.ksisolucoes.vo.cadsus.SintomasCovid19 getSintomaCovid19 () {
		return getPropertyValue(this, sintomaCovid19, PROP_SINTOMA_COVID19); 
	}

	/**
	 * Set the value related to the column: cd_sintomas_covid19
	 * @param sintomaCovid19 the cd_sintomas_covid19 value
	 */
	public void setSintomaCovid19 (br.com.ksisolucoes.vo.cadsus.SintomasCovid19 sintomaCovid19) {
//        br.com.ksisolucoes.vo.cadsus.SintomasCovid19 sintomaCovid19Old = this.sintomaCovid19;
		this.sintomaCovid19 = sintomaCovid19;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomaCovid19", sintomaCovid19Old, sintomaCovid19);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.cadsus.FormularioTriagemSintomasCovid19)) return false;
		else {
			br.com.ksisolucoes.vo.cadsus.FormularioTriagemSintomasCovid19 formularioTriagemSintomasCovid19 = (br.com.ksisolucoes.vo.cadsus.FormularioTriagemSintomasCovid19) obj;
			if (null == this.getCodigo() || null == formularioTriagemSintomasCovid19.getCodigo()) return false;
			else return (this.getCodigo().equals(formularioTriagemSintomasCovid19.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}