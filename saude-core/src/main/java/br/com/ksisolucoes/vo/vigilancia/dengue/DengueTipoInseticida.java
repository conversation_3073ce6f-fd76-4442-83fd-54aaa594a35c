package br.com.ksisolucoes.vo.vigilancia.dengue;

import java.io.Serializable;

import br.com.ksisolucoes.vo.vigilancia.dengue.base.BaseDengueTipoInseticida;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;

public class DengueTipoInseticida extends BaseDengueTipoInseticida implements CodigoManager, PesquisaObjectInterface {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public DengueTipoInseticida() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public DengueTipoInseticida(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public DengueTipoInseticida(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
            java.lang.String descricao,
            java.util.Date dataCadastro) {

        super(
                codigo,
                usuarioCadastro,
                descricao,
                dataCadastro);
    }

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    @Override
    public String getDescricaoVO() {
        return this.getDescricao();
    }

    @Override
    public String getIdentificador() {
        return this.getCodigo().toString();
    }
}
