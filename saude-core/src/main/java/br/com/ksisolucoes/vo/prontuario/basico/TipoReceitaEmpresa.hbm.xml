<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="TipoReceitaEmpresa" table="tipo_receita_empresa" >
         
        <id  
            name="codigo"
            type="java.lang.Long"
            column="cd_tp_receita_empresa"
        >
            <generator class="assigned"/>
        </id> 
        <version column="version" name="version" type="long" />
         
        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.TipoReceita" 
                     name="tipoReceita"
                     column="cd_receita"
        />
        
        <many-to-one class="br.com.ksisolucoes.vo.basico.Empresa" 
                     name="empresa"
                     column="empresa" 
        /> 

        
    </class>
</hibernate-mapping>
