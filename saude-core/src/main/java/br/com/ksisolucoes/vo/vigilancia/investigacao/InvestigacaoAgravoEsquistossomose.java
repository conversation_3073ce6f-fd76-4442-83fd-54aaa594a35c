package br.com.ksisolucoes.vo.vigilancia.investigacao;

import java.io.Serializable;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.base.BaseInvestigacaoAgravoEsquistossomose;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class InvestigacaoAgravoEsquistossomose extends BaseInvestigacaoAgravoEsquistossomose implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public InvestigacaoAgravoEsquistossomose () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public InvestigacaoAgravoEsquistossomose (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public InvestigacaoAgravoEsquistossomose (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
		java.lang.String flagInformacoesComplementares) {

		super (
			codigo,
			registroAgravo,
			flagInformacoesComplementares);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

	public static InvestigacaoAgravoEsquistossomose buscaPorRegistroAgravo(RegistroAgravo registroAgravo) {
		InvestigacaoAgravoEsquistossomose investigacao =
				LoadManager.getInstance(InvestigacaoAgravoEsquistossomose.class)
						.addProperties(new HQLProperties(InvestigacaoAgravoEsquistossomose.class).getProperties())
						.addProperties(new HQLProperties(
								RegistroAgravo.class,
								VOUtils.montarPath(InvestigacaoAgravoEsquistossomose.PROP_REGISTRO_AGRAVO)).getProperties())
						.addParameter(new QueryCustom.QueryCustomParameter(
								VOUtils.montarPath(InvestigacaoAgravoEsquistossomose.PROP_REGISTRO_AGRAVO, RegistroAgravo.PROP_CODIGO),
								registroAgravo.getCodigo()))
						.start().getVO();
		return investigacao;
	}

}
