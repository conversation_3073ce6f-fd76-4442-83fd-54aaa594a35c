package br.com.ksisolucoes.vo.vigilancia.investigacao.dto;

import br.com.ksisolucoes.vo.prontuario.basico.ExameExterno;
import br.com.ksisolucoes.vo.prontuario.basico.TesteRapidoRealizado;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoCovid19;

import java.util.Date;

public class ExameDto {

    private Long tipoTeste;
    private Date dataColeta;
    private InvestigacaoAgravoCovid19.ResultadoTeste resultadoTeste;

    public ExameDto() {}

    public ExameDto(ExameExterno exameExterno) {
        this.tipoTeste =
            exameExterno.getDescricao() != null && ExameExterno.DESCRICAO_EXAME_COVID_19_RT_PCR.contains(exameExterno.getDescricao().toUpperCase())
            ? InvestigacaoAgravoCovid19.TipoTeste.RT_PCR.getValue()
            : InvestigacaoAgravoCovid19.TipoTeste.TESTE_RAPIDO_ANTICORPO.getValue();
        this.dataColeta = exameExterno.getDataExame();
        if (exameExterno.getResultado() != null) {
            Long lResultado = Long.valueOf(exameExterno.getResultado());
            this.resultadoTeste = InvestigacaoAgravoCovid19.ResultadoTeste.valueOf(lResultado);
        }
    }

    public ExameDto(TesteRapidoRealizado testeRapidoRealizado) {
        this.tipoTeste = InvestigacaoAgravoCovid19.TipoTeste.TESTE_RAPIDO_ANTICORPO.getValue();
        this.dataColeta = testeRapidoRealizado.getAtendimentoResultado() == null ? null : testeRapidoRealizado.getAtendimentoResultado().getDataAtendimento();
        this.resultadoTeste = verificarResultadoTesteRapido(testeRapidoRealizado);
    }

    private InvestigacaoAgravoCovid19.ResultadoTeste verificarResultadoTesteRapido(TesteRapidoRealizado testeRapidoRealizado) {
        if (TesteRapidoRealizado.Resultado.REAGENTE.value().equals(testeRapidoRealizado.getResultado())) {
            return InvestigacaoAgravoCovid19.ResultadoTeste.POSITIVO;
        } else if (TesteRapidoRealizado.Resultado.NAO_REAGENTE.value().equals(testeRapidoRealizado.getResultado())) {
            return InvestigacaoAgravoCovid19.ResultadoTeste.NEGATIVO;
        }
        return null;
    }

    public Long getTipoTeste() {
        return tipoTeste;
    }

    public void setTipoTeste(Long tipoTeste) {
        this.tipoTeste = tipoTeste;
    }

    public Date getDataColeta() {
        return dataColeta;
    }

    public void setDataColeta(Date dataColeta) {
        this.dataColeta = dataColeta;
    }

    public InvestigacaoAgravoCovid19.ResultadoTeste getResultadoTeste() {
        return resultadoTeste;
    }

    public void setResultadoTeste(InvestigacaoAgravoCovid19.ResultadoTeste resultadoTeste) {
        this.resultadoTeste = resultadoTeste;
    }

    public boolean isValido() {
        return this.getTipoTeste() != null
            && this.getDataColeta() != null
            && this.getResultadoTeste() != null;
    }

    public int compareTo(ExameDto outro) {
        if (outro == null) return -1; //se o outro objeto estiver nulo, este vem antes
        if (!(this.isValido() && outro.isValido())) return 0; //Se ambos não forem validos, retorna 0
        if (!this.getResultadoTeste().equals(outro.getResultadoTeste())) {//Se os status forem diferentes entre os dois objetos
            if (InvestigacaoAgravoCovid19.ResultadoTeste.POSITIVO.equals(this.getResultadoTeste())) return -1;// o que tem resultado positivo tem precedencia
            return 1;
        }
        int resultado = this.getDataColeta().compareTo(outro.getDataColeta());//Caso o status seja igual, o fator determinante é a data
        return InvestigacaoAgravoCovid19.ResultadoTeste.POSITIVO.equals(this.getResultadoTeste()) ? resultado : resultado - 1;
    }

}
