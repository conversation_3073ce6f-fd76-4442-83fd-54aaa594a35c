<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico">
    <class name="AltaOcorrencia" table="alta_ocorrencia">

        <id
			name="codigo"
			column="cd_alta_ocorrencia"
			type="java.lang.Long" 
		>
            <generator class="assigned" />
        </id>

        <version column="version" name="version" type="long" />

        <many-to-one
			class="br.com.ksisolucoes.vo.prontuario.basico.AtendimentoAlta"
			name="atendimentoAlta"
			not-null="true"
		>
            <column name="cd_atendimento_alta" />
        </many-to-one>

        <property
			name="campoAlterado"
			type="java.lang.String"
			not-null="true"
		>
            <column name="campo_alterado" />
        </property>

		<property
			name="valorAntigo"
			type="java.lang.String"
			not-null="true"
		>
            <column name="valor_antigo" />
        </property>

		<property
			name="valorNovo"
			type="java.lang.String"
			not-null="true"
		>
            <column name="valor_novo" />
        </property>

		<property
			name="justificativa"
			type="java.lang.String"
			not-null="true"
		>
            <column name="justificativa" />
        </property>

		 <many-to-one
			class="br.com.ksisolucoes.vo.controle.Usuario"
			name="usuario"
			not-null="true"
		>
            <column name="cd_usuario" />
        </many-to-one>

		<property
			name="data"
			type="timestamp"
			not-null="true"
		>
            <column name="data_ocorrencia" />
        </property>
    </class>
</hibernate-mapping>