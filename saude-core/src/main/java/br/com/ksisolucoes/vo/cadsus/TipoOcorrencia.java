package br.com.ksisolucoes.vo.cadsus;

import br.com.ksisolucoes.vo.cadsus.base.BaseTipoOcorrencia;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;



public class TipoOcorrencia extends BaseTipoOcorrencia implements CodigoManager {
	private static final long serialVersionUID = 1L;

        public static final Long TIPO_AGENDAMENTO = 1L;
        public static final Long TIPO_TFD = 2L;
        public static final Long TIPO_EXAME = 3L;
        public static final Long TIPO_SOLICITACAO = 4L;
        public static final Long CADSUS = 5L;
        public static final Long SMS = 6L;
        public static final Long CONTATO = 7L;
        public static final Long PRONTUARIO = 8L;
        public static final Long DISPENSACAO_JUDICIAL = 9L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public TipoOcorrencia () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public TipoOcorrencia (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public TipoOcorrencia (
		java.lang.Long codigo,
		java.lang.String descricao) {

		super (
			codigo,
			descricao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}