package br.com.ksisolucoes.vo.prontuario.integracao;

import java.io.Serializable;

import br.com.ksisolucoes.vo.prontuario.integracao.base.BaseConfiguracaoEmpresaIntegracao;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class ConfiguracaoEmpresaIntegracao extends BaseConfiguracaoEmpresaIntegracao implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ConfiguracaoEmpresaIntegracao () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ConfiguracaoEmpresaIntegracao (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ConfiguracaoEmpresaIntegracao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		java.lang.String url,
		java.lang.String id,
		java.lang.String chave) {

		super (
			codigo,
			empresa,
			url,
			id,
			chave);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}