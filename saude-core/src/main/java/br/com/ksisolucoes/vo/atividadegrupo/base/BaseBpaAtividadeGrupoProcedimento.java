package br.com.ksisolucoes.vo.atividadegrupo.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the bpa_ativ_gr_proc table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="bpa_ativ_gr_proc"
 */

public abstract class BaseBpaAtividadeGrupoProcedimento extends BaseRootVO implements Serializable {

	public static String REF = "BpaAtividadeGrupoProcedimento";
	public static final String PROP_BPA = "bpa";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_ATIVIDADE_GRUPO_PROCEDIMENTO = "atividadeGrupoProcedimento";


	// constructors
	public BaseBpaAtividadeGrupoProcedimento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseBpaAtividadeGrupoProcedimento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// many to one
	private br.com.ksisolucoes.vo.atendimento.Bpa bpa;
	private br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoProcedimento atividadeGrupoProcedimento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_bpa_at_gr_proc"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_bpa
	 */
	public br.com.ksisolucoes.vo.atendimento.Bpa getBpa () {
		return getPropertyValue(this, bpa, PROP_BPA); 
	}

	/**
	 * Set the value related to the column: cd_bpa
	 * @param bpa the cd_bpa value
	 */
	public void setBpa (br.com.ksisolucoes.vo.atendimento.Bpa bpa) {
//        br.com.ksisolucoes.vo.atendimento.Bpa bpaOld = this.bpa;
		this.bpa = bpa;
//        this.getPropertyChangeSupport().firePropertyChange ("bpa", bpaOld, bpa);
	}



	/**
	 * Return the value associated with the column: cd_at_gr_proc
	 */
	public br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoProcedimento getAtividadeGrupoProcedimento () {
		return getPropertyValue(this, atividadeGrupoProcedimento, PROP_ATIVIDADE_GRUPO_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_at_gr_proc
	 * @param atividadeGrupoProcedimento the cd_at_gr_proc value
	 */
	public void setAtividadeGrupoProcedimento (br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoProcedimento atividadeGrupoProcedimento) {
//        br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoProcedimento atividadeGrupoProcedimentoOld = this.atividadeGrupoProcedimento;
		this.atividadeGrupoProcedimento = atividadeGrupoProcedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("atividadeGrupoProcedimento", atividadeGrupoProcedimentoOld, atividadeGrupoProcedimento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.atividadegrupo.BpaAtividadeGrupoProcedimento)) return false;
		else {
			br.com.ksisolucoes.vo.atividadegrupo.BpaAtividadeGrupoProcedimento bpaAtividadeGrupoProcedimento = (br.com.ksisolucoes.vo.atividadegrupo.BpaAtividadeGrupoProcedimento) obj;
			if (null == this.getCodigo() || null == bpaAtividadeGrupoProcedimento.getCodigo()) return false;
			else return (this.getCodigo().equals(bpaAtividadeGrupoProcedimento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}