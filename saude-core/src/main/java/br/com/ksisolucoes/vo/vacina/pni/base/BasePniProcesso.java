package br.com.ksisolucoes.vo.vacina.pni.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the pni_processo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="pni_processo"
 */

public abstract class BasePniProcesso extends BaseRootVO implements Serializable {

	public static String REF = "PniProcesso";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_STATUS = "status";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_CONTROLE_DADOS_ARQUIVO = "controleDadosArquivo";
	public static final String PROP_MES = "mes";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_ANO = "ano";
	public static final String PROP_ASYNC_PROCESS = "asyncProcess";
	public static final String PROP_MENSAGEM_ERRO = "mensagemErro";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";
	public static final String PROP_PATH = "path";
	public static final String PROP_DATA_GERACAO = "dataGeracao";


	// constructors
	public BasePniProcesso () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BasePniProcesso (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BasePniProcesso (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento,
		java.util.Date dataGeracao) {

		this.setCodigo(codigo);
		this.setUsuario(usuario);
		this.setUsuarioCancelamento(usuarioCancelamento);
		this.setDataGeracao(dataGeracao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long mes;
	private java.lang.Long ano;
	private java.util.Date dataGeracao;
	private java.lang.Long controleDadosArquivo;
	private java.lang.Long status;
	private java.util.Date dataCancelamento;
	private java.lang.String path;
	private java.lang.String mensagemErro;

	// many to one
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;
	private br.com.ksisolucoes.vo.service.AsyncProcess asyncProcess;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_pni_processo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: mes
	 */
	public java.lang.Long getMes () {
		return getPropertyValue(this, mes, PROP_MES); 
	}

	/**
	 * Set the value related to the column: mes
	 * @param mes the mes value
	 */
	public void setMes (java.lang.Long mes) {
//        java.lang.Long mesOld = this.mes;
		this.mes = mes;
//        this.getPropertyChangeSupport().firePropertyChange ("mes", mesOld, mes);
	}



	/**
	 * Return the value associated with the column: ano
	 */
	public java.lang.Long getAno () {
		return getPropertyValue(this, ano, PROP_ANO); 
	}

	/**
	 * Set the value related to the column: ano
	 * @param ano the ano value
	 */
	public void setAno (java.lang.Long ano) {
//        java.lang.Long anoOld = this.ano;
		this.ano = ano;
//        this.getPropertyChangeSupport().firePropertyChange ("ano", anoOld, ano);
	}



	/**
	 * Return the value associated with the column: dt_geracao
	 */
	public java.util.Date getDataGeracao () {
		return getPropertyValue(this, dataGeracao, PROP_DATA_GERACAO); 
	}

	/**
	 * Set the value related to the column: dt_geracao
	 * @param dataGeracao the dt_geracao value
	 */
	public void setDataGeracao (java.util.Date dataGeracao) {
//        java.util.Date dataGeracaoOld = this.dataGeracao;
		this.dataGeracao = dataGeracao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataGeracao", dataGeracaoOld, dataGeracao);
	}



	/**
	 * Return the value associated with the column: controle_dados_arquivo
	 */
	public java.lang.Long getControleDadosArquivo () {
		return getPropertyValue(this, controleDadosArquivo, PROP_CONTROLE_DADOS_ARQUIVO); 
	}

	/**
	 * Set the value related to the column: controle_dados_arquivo
	 * @param controleDadosArquivo the controle_dados_arquivo value
	 */
	public void setControleDadosArquivo (java.lang.Long controleDadosArquivo) {
//        java.lang.Long controleDadosArquivoOld = this.controleDadosArquivo;
		this.controleDadosArquivo = controleDadosArquivo;
//        this.getPropertyChangeSupport().firePropertyChange ("controleDadosArquivo", controleDadosArquivoOld, controleDadosArquivo);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: path
	 */
	public java.lang.String getPath () {
		return getPropertyValue(this, path, PROP_PATH); 
	}

	/**
	 * Set the value related to the column: path
	 * @param path the path value
	 */
	public void setPath (java.lang.String path) {
//        java.lang.String pathOld = this.path;
		this.path = path;
//        this.getPropertyChangeSupport().firePropertyChange ("path", pathOld, path);
	}



	/**
	 * Return the value associated with the column: msg_erro
	 */
	public java.lang.String getMensagemErro () {
		return getPropertyValue(this, mensagemErro, PROP_MENSAGEM_ERRO); 
	}

	/**
	 * Set the value related to the column: msg_erro
	 * @param mensagemErro the msg_erro value
	 */
	public void setMensagemErro (java.lang.String mensagemErro) {
//        java.lang.String mensagemErroOld = this.mensagemErro;
		this.mensagemErro = mensagemErro;
//        this.getPropertyChangeSupport().firePropertyChange ("mensagemErro", mensagemErroOld, mensagemErro);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_usuario_can
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_can
	 * @param usuarioCancelamento the cd_usuario_can value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}



	/**
	 * Return the value associated with the column: cd_process
	 */
	public br.com.ksisolucoes.vo.service.AsyncProcess getAsyncProcess () {
		return getPropertyValue(this, asyncProcess, PROP_ASYNC_PROCESS); 
	}

	/**
	 * Set the value related to the column: cd_process
	 * @param asyncProcess the cd_process value
	 */
	public void setAsyncProcess (br.com.ksisolucoes.vo.service.AsyncProcess asyncProcess) {
//        br.com.ksisolucoes.vo.service.AsyncProcess asyncProcessOld = this.asyncProcess;
		this.asyncProcess = asyncProcess;
//        this.getPropertyChangeSupport().firePropertyChange ("asyncProcess", asyncProcessOld, asyncProcess);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vacina.pni.PniProcesso)) return false;
		else {
			br.com.ksisolucoes.vo.vacina.pni.PniProcesso pniProcesso = (br.com.ksisolucoes.vo.vacina.pni.PniProcesso) obj;
			if (null == this.getCodigo() || null == pniProcesso.getCodigo()) return false;
			else return (this.getCodigo().equals(pniProcesso.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}