package br.com.ksisolucoes.vo.vigilancia.requerimentos;

import br.com.ksisolucoes.util.Valor;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.base.BaseRequerimentoAnaliseProjeto;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

import java.io.Serializable;


public class RequerimentoAnaliseProjeto extends BaseRequerimentoAnaliseProjeto implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequerimentoAnaliseProjeto () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequerimentoAnaliseProjeto (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequerimentoAnaliseProjeto (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia,
		br.com.ksisolucoes.vo.vigilancia.TipoProjetoVigilancia tipoProjetoVigilancia,
		java.lang.Double area) {

		super (
			codigo,
			requerimentoVigilancia,
			tipoProjetoVigilancia,
			area);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getNumeracaoParecerTecnicoFormatado(){
		return VigilanciaHelper.formatarProtocolo(getNumeracaoParecerTecnico());
	}

	public String getNumeracaoConformidadeTecnicaFormatado(){
		return VigilanciaHelper.formatarProtocolo(getNumeracaoParecerConformidadeTecnica());
	}

	public String getDescricaoAreaFormatado() {
		return Valor.adicionarFormatacaoMonetaria(getArea()).concat(" m²");
	}

	@Override
	public String getDescricaoVO() {
		if (this.getRequerimentoVigilancia() != null) {
			return this.getRequerimentoVigilancia().getProtocoloFormatado();
		}
    	return null;
	}

	@Override
	public String getIdentificador() {
		return this.getCodigo().toString();
	}
}