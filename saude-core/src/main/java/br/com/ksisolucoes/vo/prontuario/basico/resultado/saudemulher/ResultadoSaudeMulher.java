package br.com.ksisolucoes.vo.prontuario.basico.resultado.saudemulher;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.enums.IEnumSum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Valor;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.resultado.saudemulher.base.BaseResultadoSaudeMulher;
import org.jgroups.annotations.DeprecatedProperty;

import java.io.Serializable;
import java.util.List;

public class ResultadoSaudeMulher extends BaseResultadoSaudeMulher implements CodigoManager {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public ResultadoSaudeMulher () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ResultadoSaudeMulher (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ResultadoSaudeMulher (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicao) {

		super (
			codigo,
			exameRequisicao);
	}

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public static enum AvaliacaoPreAnalitica implements IEnum<AvaliacaoPreAnalitica> {

        INSATISFATORIA(0L, Bundle.getStringApplication("rotulo_insatisfatoria")),
        SATISFATORIA(1L, Bundle.getStringApplication("rotulo_satisfatoria")),
        REJEITADA(2L, Bundle.getStringApplication("rotulo_rejeitada"));

        private Long value;
        private String descricao;

        private AvaliacaoPreAnalitica(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static AvaliacaoPreAnalitica valeuOf(String value) {
            for (AvaliacaoPreAnalitica apa : AvaliacaoPreAnalitica.values()) {
                if (apa.value().equals(value)) {
                    return apa;
                }
            }
            return null;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    public static enum EpiteliosAmostra implements IEnumSum<EpiteliosAmostra> {

        ESCAMOSA(0L, 1L, Bundle.getStringApplication("rotulo_escamoso")),
        METAPLASICO(1L, 2L, Bundle.getStringApplication("rotulo_metaplásico")),
        GLANDULAR(2L, 4L, Bundle.getStringApplication("rotulo_glandular"));

        private Long value;
        private Long sum;
        private String descricao;

        private EpiteliosAmostra(Long value, Long sum, String descricao) {
            this.sum = sum;
            this.value = value;
            this.descricao = descricao;
        }

        public static EpiteliosAmostra valeuOf(String value) {
            for (EpiteliosAmostra ea : EpiteliosAmostra.values()) {
                if (ea.value().equals(value)) {
                    return ea;
                }
            }
            return null;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public Long sum() {
            return this.sum;
        }

    }

    public static enum AvaliacaoCelularBenignaReativaReparativa implements IEnumSum<AvaliacaoCelularBenignaReativaReparativa> {

        INFLAMACAO(0L, 1L, Bundle.getStringApplication("rotulo_inflamacao")),
        METAPLASIA(1L, 2L, Bundle.getStringApplication("rotulo_metaplasia_escamosa_imatura")),
        REPARACAO(2L, 4L, Bundle.getStringApplication("rotulo_reparacao")),
        ATROFIA_INFLAMACAO(3L, 8L, Bundle.getStringApplication("rotulo_atrofia_inflamacao")),
        RADIACAO(4L, 16L, Bundle.getStringApplication("rotulo_radiacao")),
        OUTROS(5L, 32L, Bundle.getStringApplication("rotulo_outros"));

        private Long value;
        private Long sum;
        private String descricao;

        private AvaliacaoCelularBenignaReativaReparativa(Long value, Long sum, String descricao) {
            this.sum = sum;
            this.value = value;
            this.descricao = descricao;
        }

        public static AvaliacaoCelularBenignaReativaReparativa valeuOf(String value) {
            for (AvaliacaoCelularBenignaReativaReparativa acbrr : AvaliacaoCelularBenignaReativaReparativa.values()) {
                if (acbrr.value().equals(value)) {
                    return acbrr;
                }
            }
            return null;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public Long sum() {
            return this.sum;
        }
    }

    public static enum Microbiologia implements IEnumSum<Microbiologia> {

        LACTOBACILUS(0L, 1L, Bundle.getStringApplication("rotulo_lactobacilus_sp")),
        COCOS(1L, 2L, Bundle.getStringApplication("rotulo_cocos")),
        SUGESTIVO(2L, 4L, Bundle.getStringApplication("rotulo_sugestivo_chlamydia_sp")),
        ACTINOMYCES(3L, 8L, Bundle.getStringApplication("rotulo_actinomyces_sp")),
        CANDIDA(4L, 16L, Bundle.getStringApplication("rotulo_candida_sp")),
        TRICHOMONAS(5L, 32L, Bundle.getStringApplication("rotulo_trichomonas_vaginalis")),
        BACILOS_SUPRACITOPLASMATICO(6L, 64L, Bundle.getStringApplication("rotulo_bacilos_supracitoplasmaticos")),
        OUTROS_BACILOS(7L, 128L, Bundle.getStringApplication("rotulo_outros_bacilos")),
        OUTROS(8L, 256L, Bundle.getStringApplication("rotulo_outros")),
        EFEITOS_CITOPATOLOGICOS(9L, 512L, Bundle.getStringApplication("rotulo_efeito_citopatologico_virus_herpes"));

        private Long value;
        private Long sum;
        private String descricao;

        private Microbiologia(Long value, Long sum, String descricao) {
            this.sum = sum;
            this.value = value;
            this.descricao = descricao;
        }

        public static Microbiologia valeuOf(String value) {
            for (Microbiologia m : Microbiologia.values()) {
                if (m.value().equals(value)) {
                    return m;
                }
            }
            return null;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public Long sum() {
            return this.sum;
        }
    }

    public static enum Escamosas implements IEnum<Escamosas> {

        POSSIVELMENTE_NAO_NEOPLASICA(0L, Bundle.getStringApplication("rotulo_possivelmente_nao_neoplastica_asc")),
        NAO_AFASTAR_LESAO(1L, Bundle.getStringApplication("rotulo_nao_afastar_lesao_alto_grau_asc"));

        private Long value;
        private String descricao;

        private Escamosas(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Escamosas valeuOf(String value) {
            for (Escamosas apa : Escamosas.values()) {
                if (apa.value().equals(value)) {
                    return apa;
                }
            }
            return null;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    public static enum Glandulares implements IEnum<Glandulares> {

        POSSIVELMENTE_NAO_NEOPLASICA(0L, Bundle.getStringApplication("rotulo_possivelmente_nao_neoplastica")),
        NAO_AFASTAR_LESAO(1L, Bundle.getStringApplication("rotulo_nao_afastar_lesao_alto_grau"));

        private Long value;
        private String descricao;

        private Glandulares(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Glandulares valeuOf(String value) {
            for (Glandulares apa : Glandulares.values()) {
                if (apa.value().equals(value)) {
                    return apa;
                }
            }
            return null;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    public static enum OrigemIndefinida implements IEnum<OrigemIndefinida> {

        POSSIVELMENTE_NAO_NEOPLASICA(0L, Bundle.getStringApplication("rotulo_possivelmente_nao_neoplastica")),
        NAO_AFASTAR_LESAO(1L, Bundle.getStringApplication("rotulo_nao_afastar_lesao_alto_grau"));

        private Long value;
        private String descricao;

        private OrigemIndefinida(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Glandulares valeuOf(String value) {
            for (Glandulares apa : Glandulares.values()) {
                if (apa.value().equals(value)) {
                    return apa;
                }
            }
            return null;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    public static enum Resultado implements IEnum<Resultado> {

        NORMAL(0L, Bundle.getStringApplication("rotulo_normal")),
        ALTERADO(1L, Bundle.getStringApplication("rotulo_alterado"));

        private Long value;
        private String descricao;

        private Resultado(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Resultado valeuOf(String value) {
            for (Resultado resultado : Resultado.values()) {
                if (resultado.value().equals(value)) {
                    return resultado;
                }
            }
            return null;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    public static enum AtipiasCelulaEscamosa implements IEnumSum<AtipiasCelulaEscamosa> {

        LESAO_INTRAEPITELIAL_BAIXO_GRAU(0L, 1L, Bundle.getStringApplication("rotulo_lesao_intraepitelial_baixo_grau")),
        LESAO_INTRAEPITELIAL_ALTO_GRAU_1(1L, 2L, Bundle.getStringApplication("rotulo_lesao_intraepitelial_alto_grau_1")),
        LESAO_INTRAEPITELIAL_ALTO_GRAU_2(2L, 4L, Bundle.getStringApplication("rotulo_lesao_intraepitelial_alto_grau_2")),
        CARCINOMA(4L, 8L, Bundle.getStringApplication("rotulo_carcinoma_epidermoide_invasor"));

        private Long value;
        private Long sum;
        private String descricao;

        private AtipiasCelulaEscamosa(Long value, Long sum, String descricao) {
            this.sum = sum;
            this.value = value;
            this.descricao = descricao;
        }

        public static AtipiasCelulaEscamosa valeuOf(String value) {
            for (AtipiasCelulaEscamosa acbrr : AtipiasCelulaEscamosa.values()) {
                if (acbrr.value().equals(value)) {
                    return acbrr;
                }
            }
            return null;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public Long sum() {
            return this.sum;
        }
    }

    public static enum AtipiasCelulaGlandulares implements IEnumSum<AtipiasCelulaGlandulares> {

        IN_SITU(0L, 1L, Bundle.getStringApplication("rotulo_adenocarcinoma_in_situ")),
        INVASOR_CERVICAL(1L, 2L, Bundle.getStringApplication("rotulo_adenocarcinoma_invasor_cervical")),
        INVASOR_ENDOMETRIAL(2L, 4L, Bundle.getStringApplication("rotulo_adenocarcinoma_invasor_endometrial")),
        INVASOR_OUTRAS(4L, 8L, Bundle.getStringApplication("rotulo_adenocarcinoma_invasor_sem_outras_especificacoes"));

        private Long value;
        private Long sum;
        private String descricao;

        private AtipiasCelulaGlandulares(Long value, Long sum, String descricao) {
            this.sum = sum;
            this.value = value;
            this.descricao = descricao;
        }

        public static AtipiasCelulaGlandulares valeuOf(String value) {
            for (AtipiasCelulaGlandulares acbrr : AtipiasCelulaGlandulares.values()) {
                if (acbrr.value().equals(value)) {
                    return acbrr;
                }
            }
            return null;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public Long sum() {
            return this.sum;
        }
    }
    /*getDescricaoIEnumSum se encontra em br.com.ksisolucoes.util.IEnumUtils*/
    @Deprecated
    public String getDescricaoIEnumSum(IEnumSum<?> iEnumSum, Long obj) {
        if (iEnumSum == null || obj == null) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        List<Long> valores = Valor.resolveSomatorio(obj);
        for (Long valor : valores) {
            if (valor.equals(iEnumSum.sum())) {
                sb.append(iEnumSum.descricao());
            }
        }
        return sb.toString();
    }
}
