package br.com.ksisolucoes.vo.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the estabelecimento_cerest table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="estabelecimento_cerest"
 */

public abstract class BaseEstabelecimentoCerest extends BaseRootVO implements Serializable {

	public static String REF = "EstabelecimentoCerest";
	public static final String PROP_CNPJ = "cnpj";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_USUARIO_CAN = "usuarioCan";
	public static final String PROP_MATRIZ = "matriz";
	public static final String PROP_SITUACAO = "situacao";
	public static final String PROP_FANTASIA = "fantasia";
	public static final String PROP_NUMERO_LOGRADOURO = "numeroLogradouro";
	public static final String PROP_RAZAO_SOCIAL = "razaoSocial";
	public static final String PROP_CEP = "cep";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_ESTADO = "estado";
	public static final String PROP_COMPLEMENTO = "complemento";
	public static final String PROP_CIDADE = "cidade";
	public static final String PROP_LOGRADOURO = "logradouro";
	public static final String PROP_EMAIL = "email";
	public static final String PROP_DATA_INICIO_FUNCIONAMENTO = "dataInicioFuncionamento";
	public static final String PROP_TELEFONE = "telefone";
	public static final String PROP_BAIRRO = "bairro";


	// constructors
	public BaseEstabelecimentoCerest () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEstabelecimentoCerest (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseEstabelecimentoCerest (
		java.lang.Long codigo,
		java.lang.String razaoSocial,
		java.lang.String cnpj,
		java.lang.Long matriz,
		java.lang.Long situacao,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setRazaoSocial(razaoSocial);
		this.setCnpj(cnpj);
		this.setMatriz(matriz);
		this.setSituacao(situacao);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String razaoSocial;
	private java.lang.String fantasia;
	private java.lang.String cnpj;
	private java.lang.Long matriz;
	private java.util.Date dataInicioFuncionamento;
	private java.lang.String email;
	private java.lang.String telefone;
	private java.lang.String logradouro;
	private java.lang.String numeroLogradouro;
	private java.lang.String bairro;
	private java.lang.String complemento;
	private java.lang.String cep;
	private java.lang.Long situacao;
	private java.util.Date dataCadastro;
	private java.util.Date dataCancelamento;

	// many to one
	private br.com.ksisolucoes.vo.basico.Cidade cidade;
	private br.com.ksisolucoes.vo.basico.Estado estado;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCan;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_estabelecimento_cerest"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: razao_social
	 */
	public java.lang.String getRazaoSocial () {
		return getPropertyValue(this, razaoSocial, PROP_RAZAO_SOCIAL); 
	}

	/**
	 * Set the value related to the column: razao_social
	 * @param razaoSocial the razao_social value
	 */
	public void setRazaoSocial (java.lang.String razaoSocial) {
//        java.lang.String razaoSocialOld = this.razaoSocial;
		this.razaoSocial = razaoSocial;
//        this.getPropertyChangeSupport().firePropertyChange ("razaoSocial", razaoSocialOld, razaoSocial);
	}



	/**
	 * Return the value associated with the column: fantasia
	 */
	public java.lang.String getFantasia () {
		return getPropertyValue(this, fantasia, PROP_FANTASIA); 
	}

	/**
	 * Set the value related to the column: fantasia
	 * @param fantasia the fantasia value
	 */
	public void setFantasia (java.lang.String fantasia) {
//        java.lang.String fantasiaOld = this.fantasia;
		this.fantasia = fantasia;
//        this.getPropertyChangeSupport().firePropertyChange ("fantasia", fantasiaOld, fantasia);
	}



	/**
	 * Return the value associated with the column: cnpj
	 */
	public java.lang.String getCnpj () {
		return getPropertyValue(this, cnpj, PROP_CNPJ); 
	}

	/**
	 * Set the value related to the column: cnpj
	 * @param cnpj the cnpj value
	 */
	public void setCnpj (java.lang.String cnpj) {
//        java.lang.String cnpjOld = this.cnpj;
		this.cnpj = cnpj;
//        this.getPropertyChangeSupport().firePropertyChange ("cnpj", cnpjOld, cnpj);
	}



	/**
	 * Return the value associated with the column: matriz
	 */
	public java.lang.Long getMatriz () {
		return getPropertyValue(this, matriz, PROP_MATRIZ); 
	}

	/**
	 * Set the value related to the column: matriz
	 * @param matriz the matriz value
	 */
	public void setMatriz (java.lang.Long matriz) {
//        java.lang.Long matrizOld = this.matriz;
		this.matriz = matriz;
//        this.getPropertyChangeSupport().firePropertyChange ("matriz", matrizOld, matriz);
	}



	/**
	 * Return the value associated with the column: dt_inicio_funcionamento
	 */
	public java.util.Date getDataInicioFuncionamento () {
		return getPropertyValue(this, dataInicioFuncionamento, PROP_DATA_INICIO_FUNCIONAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_inicio_funcionamento
	 * @param dataInicioFuncionamento the dt_inicio_funcionamento value
	 */
	public void setDataInicioFuncionamento (java.util.Date dataInicioFuncionamento) {
//        java.util.Date dataInicioFuncionamentoOld = this.dataInicioFuncionamento;
		this.dataInicioFuncionamento = dataInicioFuncionamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInicioFuncionamento", dataInicioFuncionamentoOld, dataInicioFuncionamento);
	}



	/**
	 * Return the value associated with the column: email
	 */
	public java.lang.String getEmail () {
		return getPropertyValue(this, email, PROP_EMAIL); 
	}

	/**
	 * Set the value related to the column: email
	 * @param email the email value
	 */
	public void setEmail (java.lang.String email) {
//        java.lang.String emailOld = this.email;
		this.email = email;
//        this.getPropertyChangeSupport().firePropertyChange ("email", emailOld, email);
	}



	/**
	 * Return the value associated with the column: telefone
	 */
	public java.lang.String getTelefone () {
		return getPropertyValue(this, telefone, PROP_TELEFONE); 
	}

	/**
	 * Set the value related to the column: telefone
	 * @param telefone the telefone value
	 */
	public void setTelefone (java.lang.String telefone) {
//        java.lang.String telefoneOld = this.telefone;
		this.telefone = telefone;
//        this.getPropertyChangeSupport().firePropertyChange ("telefone", telefoneOld, telefone);
	}



	/**
	 * Return the value associated with the column: logradouro
	 */
	public java.lang.String getLogradouro () {
		return getPropertyValue(this, logradouro, PROP_LOGRADOURO); 
	}

	/**
	 * Set the value related to the column: logradouro
	 * @param logradouro the logradouro value
	 */
	public void setLogradouro (java.lang.String logradouro) {
//        java.lang.String logradouroOld = this.logradouro;
		this.logradouro = logradouro;
//        this.getPropertyChangeSupport().firePropertyChange ("logradouro", logradouroOld, logradouro);
	}



	/**
	 * Return the value associated with the column: nr_logradouro
	 */
	public java.lang.String getNumeroLogradouro () {
		return getPropertyValue(this, numeroLogradouro, PROP_NUMERO_LOGRADOURO); 
	}

	/**
	 * Set the value related to the column: nr_logradouro
	 * @param numeroLogradouro the nr_logradouro value
	 */
	public void setNumeroLogradouro (java.lang.String numeroLogradouro) {
//        java.lang.String numeroLogradouroOld = this.numeroLogradouro;
		this.numeroLogradouro = numeroLogradouro;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroLogradouro", numeroLogradouroOld, numeroLogradouro);
	}



	/**
	 * Return the value associated with the column: bairro
	 */
	public java.lang.String getBairro () {
		return getPropertyValue(this, bairro, PROP_BAIRRO); 
	}

	/**
	 * Set the value related to the column: bairro
	 * @param bairro the bairro value
	 */
	public void setBairro (java.lang.String bairro) {
//        java.lang.String bairroOld = this.bairro;
		this.bairro = bairro;
//        this.getPropertyChangeSupport().firePropertyChange ("bairro", bairroOld, bairro);
	}



	/**
	 * Return the value associated with the column: complemento
	 */
	public java.lang.String getComplemento () {
		return getPropertyValue(this, complemento, PROP_COMPLEMENTO); 
	}

	/**
	 * Set the value related to the column: complemento
	 * @param complemento the complemento value
	 */
	public void setComplemento (java.lang.String complemento) {
//        java.lang.String complementoOld = this.complemento;
		this.complemento = complemento;
//        this.getPropertyChangeSupport().firePropertyChange ("complemento", complementoOld, complemento);
	}



	/**
	 * Return the value associated with the column: cep
	 */
	public java.lang.String getCep () {
		return getPropertyValue(this, cep, PROP_CEP); 
	}

	/**
	 * Set the value related to the column: cep
	 * @param cep the cep value
	 */
	public void setCep (java.lang.String cep) {
//        java.lang.String cepOld = this.cep;
		this.cep = cep;
//        this.getPropertyChangeSupport().firePropertyChange ("cep", cepOld, cep);
	}



	/**
	 * Return the value associated with the column: situacao
	 */
	public java.lang.Long getSituacao () {
		return getPropertyValue(this, situacao, PROP_SITUACAO); 
	}

	/**
	 * Set the value related to the column: situacao
	 * @param situacao the situacao value
	 */
	public void setSituacao (java.lang.Long situacao) {
//        java.lang.Long situacaoOld = this.situacao;
		this.situacao = situacao;
//        this.getPropertyChangeSupport().firePropertyChange ("situacao", situacaoOld, situacao);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: cod_cid
	 */
	public br.com.ksisolucoes.vo.basico.Cidade getCidade () {
		return getPropertyValue(this, cidade, PROP_CIDADE); 
	}

	/**
	 * Set the value related to the column: cod_cid
	 * @param cidade the cod_cid value
	 */
	public void setCidade (br.com.ksisolucoes.vo.basico.Cidade cidade) {
//        br.com.ksisolucoes.vo.basico.Cidade cidadeOld = this.cidade;
		this.cidade = cidade;
//        this.getPropertyChangeSupport().firePropertyChange ("cidade", cidadeOld, cidade);
	}



	/**
	 * Return the value associated with the column: cod_est
	 */
	public br.com.ksisolucoes.vo.basico.Estado getEstado () {
		return getPropertyValue(this, estado, PROP_ESTADO); 
	}

	/**
	 * Set the value related to the column: cod_est
	 * @param estado the cod_est value
	 */
	public void setEstado (br.com.ksisolucoes.vo.basico.Estado estado) {
//        br.com.ksisolucoes.vo.basico.Estado estadoOld = this.estado;
		this.estado = estado;
//        this.getPropertyChangeSupport().firePropertyChange ("estado", estadoOld, estado);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_usuario_can
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCan () {
		return getPropertyValue(this, usuarioCan, PROP_USUARIO_CAN); 
	}

	/**
	 * Set the value related to the column: cd_usuario_can
	 * @param usuarioCan the cd_usuario_can value
	 */
	public void setUsuarioCan (br.com.ksisolucoes.vo.controle.Usuario usuarioCan) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCanOld = this.usuarioCan;
		this.usuarioCan = usuarioCan;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCan", usuarioCanOld, usuarioCan);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.EstabelecimentoCerest)) return false;
		else {
			br.com.ksisolucoes.vo.basico.EstabelecimentoCerest estabelecimentoCerest = (br.com.ksisolucoes.vo.basico.EstabelecimentoCerest) obj;
			if (null == this.getCodigo() || null == estabelecimentoCerest.getCodigo()) return false;
			else return (this.getCodigo().equals(estabelecimentoCerest.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}