package br.com.ksisolucoes.vo.prontuario.procedimento;

import br.com.ksisolucoes.associacao.annotations.IdNameSIGTAP;
import br.com.ksisolucoes.vo.prontuario.procedimento.base.BaseProcedimentoCompativelPK;

public class ProcedimentoCompativelPK extends BaseProcedimentoCompativelPK {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ProcedimentoCompativelPK () {}
	
	public ProcedimentoCompativelPK (
		br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetenciaPrincipal,
		br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroCadastro registroPrincipal,
		br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetenciaCompativel,
		br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroCadastro registroCompativel) {

		super (
			procedimentoCompetenciaPrincipal,
			registroPrincipal,
			procedimentoCompetenciaCompativel,
			registroCompativel);
	}

/*[CONSTRUCTOR MARKER END]*/
    @IdNameSIGTAP
    @Override
    public ProcedimentoCompetencia getProcedimentoCompetenciaCompativel() {
        return super.getProcedimentoCompetenciaCompativel();
    }

    @IdNameSIGTAP
    @Override
    public ProcedimentoCompetencia getProcedimentoCompetenciaPrincipal() {
        return super.getProcedimentoCompetenciaPrincipal();
    }

    @IdNameSIGTAP
    @Override
    public ProcedimentoRegistroCadastro getRegistroCompativel() {
        return super.getRegistroCompativel();
    }

    @IdNameSIGTAP
    @Override
    public ProcedimentoRegistroCadastro getRegistroPrincipal() {
        return super.getRegistroPrincipal();
    }

    @Override
    public void setProcedimentoCompetenciaCompativel(ProcedimentoCompetencia procedimentoCompetenciaCompativel) {
        super.setProcedimentoCompetenciaCompativel(procedimentoCompetenciaCompativel);
    }

    @Override
    public void setProcedimentoCompetenciaPrincipal(ProcedimentoCompetencia procedimentoCompetenciaPrincipal) {
        super.setProcedimentoCompetenciaPrincipal(procedimentoCompetenciaPrincipal);
    }

    @Override
    public void setRegistroCompativel(ProcedimentoRegistroCadastro registroCompativel) {
        super.setRegistroCompativel(registroCompativel);
    }

    @Override
    public void setRegistroPrincipal(ProcedimentoRegistroCadastro registroPrincipal) {
        super.setRegistroPrincipal(registroPrincipal);
    }


}