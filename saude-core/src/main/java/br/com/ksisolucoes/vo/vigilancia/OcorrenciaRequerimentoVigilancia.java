package br.com.ksisolucoes.vo.vigilancia;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.base.BaseOcorrenciaRequerimentoVigilancia;

import java.io.Serializable;

public class OcorrenciaRequerimentoVigilancia extends BaseOcorrenciaRequerimentoVigilancia implements CodigoManager {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public OcorrenciaRequerimentoVigilancia () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public OcorrenciaRequerimentoVigilancia (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public OcorrenciaRequerimentoVigilancia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia,
		java.util.Date dataOcorrencia,
		java.lang.String descricao) {

		super (
			codigo,
			usuario,
			requerimentoVigilancia,
			dataOcorrencia,
			descricao);
	}

    /*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

	public static enum TipoOcorrencia implements IEnum<TipoOcorrencia> {

		PUBLICO(0L, Bundle.getStringApplication("rotulo_publico")),
		PRIVADO(1L, Bundle.getStringApplication("rotulo_privado"));

		private Long value;
		private String descricao;

		private TipoOcorrencia(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static TipoOcorrencia valeuOf(Long value) {
			for (TipoOcorrencia situacaoOcorrencia : TipoOcorrencia.values()) {
				if (situacaoOcorrencia.value().equals(value)) {
					return situacaoOcorrencia;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return this.value;
		}

		@Override
		public String descricao() {
			return this.descricao;
		}
	}
}