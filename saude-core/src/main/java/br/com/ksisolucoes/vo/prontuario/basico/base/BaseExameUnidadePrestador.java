package br.com.ksisolucoes.vo.prontuario.basico.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the exame_unidade_prestador table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="exame_unidade_prestador"
 */

public abstract class BaseExameUnidadePrestador extends BaseRootVO implements Serializable {

	public static String REF = "ExameUnidadePrestador";
	public static final String PROP_EXAME_PRESTADOR = "examePrestador";
	public static final String PROP_PORCENTAGEM_TETO_FINANCEIRO = "porcentagemTetoFinanceiro";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_TETO_FINANCEIRO = "tetoFinanceiro";


	// constructors
	public BaseExameUnidadePrestador () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseExameUnidadePrestador (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseExameUnidadePrestador (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.ExamePrestador examePrestador,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		java.lang.Double tetoFinanceiro) {

		this.setCodigo(codigo);
		this.setExamePrestador(examePrestador);
		this.setEmpresa(empresa);
		this.setTetoFinanceiro(tetoFinanceiro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Double tetoFinanceiro;
	private java.lang.Double porcentagemTetoFinanceiro;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.ExamePrestador examePrestador;
	private br.com.ksisolucoes.vo.basico.Empresa empresa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_unidade_prestador"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: teto_financeiro
	 */
	public java.lang.Double getTetoFinanceiro () {
		return getPropertyValue(this, tetoFinanceiro, PROP_TETO_FINANCEIRO); 
	}

	/**
	 * Set the value related to the column: teto_financeiro
	 * @param tetoFinanceiro the teto_financeiro value
	 */
	public void setTetoFinanceiro (java.lang.Double tetoFinanceiro) {
//        java.lang.Double tetoFinanceiroOld = this.tetoFinanceiro;
		this.tetoFinanceiro = tetoFinanceiro;
//        this.getPropertyChangeSupport().firePropertyChange ("tetoFinanceiro", tetoFinanceiroOld, tetoFinanceiro);
	}



	/**
	 * Return the value associated with the column: porcentagem_teto_financeiro
	 */
	public java.lang.Double getPorcentagemTetoFinanceiro () {
		return getPropertyValue(this, porcentagemTetoFinanceiro, PROP_PORCENTAGEM_TETO_FINANCEIRO); 
	}

	/**
	 * Set the value related to the column: porcentagem_teto_financeiro
	 * @param porcentagemTetoFinanceiro the porcentagem_teto_financeiro value
	 */
	public void setPorcentagemTetoFinanceiro (java.lang.Double porcentagemTetoFinanceiro) {
//        java.lang.Double porcentagemTetoFinanceiroOld = this.porcentagemTetoFinanceiro;
		this.porcentagemTetoFinanceiro = porcentagemTetoFinanceiro;
//        this.getPropertyChangeSupport().firePropertyChange ("porcentagemTetoFinanceiro", porcentagemTetoFinanceiroOld, porcentagemTetoFinanceiro);
	}



	/**
	 * Return the value associated with the column: cd_exame_prestador
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ExamePrestador getExamePrestador () {
		return getPropertyValue(this, examePrestador, PROP_EXAME_PRESTADOR); 
	}

	/**
	 * Set the value related to the column: cd_exame_prestador
	 * @param examePrestador the cd_exame_prestador value
	 */
	public void setExamePrestador (br.com.ksisolucoes.vo.prontuario.basico.ExamePrestador examePrestador) {
//        br.com.ksisolucoes.vo.prontuario.basico.ExamePrestador examePrestadorOld = this.examePrestador;
		this.examePrestador = examePrestador;
//        this.getPropertyChangeSupport().firePropertyChange ("examePrestador", examePrestadorOld, examePrestador);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.ExameUnidadePrestador)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.ExameUnidadePrestador exameUnidadePrestador = (br.com.ksisolucoes.vo.prontuario.basico.ExameUnidadePrestador) obj;
			if (null == this.getCodigo() || null == exameUnidadePrestador.getCodigo()) return false;
			else return (this.getCodigo().equals(exameUnidadePrestador.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}