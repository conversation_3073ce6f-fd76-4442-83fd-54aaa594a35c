package br.com.ksisolucoes.vo.agendamento.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the numeracao_aih_apac table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="numeracao_aih_apac"
 */

public abstract class BaseNumeracaoAihApac extends BaseRootVO implements Serializable {

	public static String REF = "NumeracaoAihApac";
	public static final String PROP_STATUS = "status";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_SEQ_FINAL = "seqFinal";
	public static final String PROP_NR_INICIAL = "nrInicial";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TIPO_LAUDO = "tipoLaudo";
	public static final String PROP_NR_FINAL = "nrFinal";
	public static final String PROP_DATA_FIM_CAMPANHA = "dataFimCampanha";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_USUARIO_CADASTRO = "usuarioCadastro";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";
	public static final String PROP_SEQ_INICIAL = "seqInicial";
	public static final String PROP_TIPO = "tipo";
	public static final String PROP_SEQ_USO = "seqUso";
	public static final String PROP_DATA_INICIO_CAMPANHA = "dataInicioCampanha";
	public static final String PROP_MOTIVO_CANCELAMENTO = "motivoCancelamento";
	public static final String PROP_TIPO_FINANCIAMENTO = "tipoFinanciamento";


	// constructors
	public BaseNumeracaoAihApac () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseNumeracaoAihApac (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseNumeracaoAihApac (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
		java.lang.Long tipoLaudo,
		java.lang.Long tipo,
		java.lang.Long nrInicial,
		java.lang.Long seqInicial,
		java.lang.Long nrFinal,
		java.lang.Long seqFinal,
		java.lang.Long seqUso,
		java.lang.Long status,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setUsuarioCadastro(usuarioCadastro);
		this.setTipoLaudo(tipoLaudo);
		this.setTipo(tipo);
		this.setNrInicial(nrInicial);
		this.setSeqInicial(seqInicial);
		this.setNrFinal(nrFinal);
		this.setSeqFinal(seqFinal);
		this.setSeqUso(seqUso);
		this.setStatus(status);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long tipoLaudo;
	private java.lang.Long tipo;
	private java.lang.Long nrInicial;
	private java.lang.Long seqInicial;
	private java.lang.Long nrFinal;
	private java.lang.Long seqFinal;
	private java.lang.Long seqUso;
	private java.lang.Long status;
	private java.util.Date dataCadastro;
	private java.util.Date dataCancelamento;
	private java.lang.String motivoCancelamento;
	private java.util.Date dataInicioCampanha;
	private java.util.Date dataFimCampanha;
	private java.lang.Long tipoFinanciamento;

	// many to one
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_numeracao_aih_apac"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: tipo_laudo
	 */
	public java.lang.Long getTipoLaudo () {
		return getPropertyValue(this, tipoLaudo, PROP_TIPO_LAUDO); 
	}

	/**
	 * Set the value related to the column: tipo_laudo
	 * @param tipoLaudo the tipo_laudo value
	 */
	public void setTipoLaudo (java.lang.Long tipoLaudo) {
//        java.lang.Long tipoLaudoOld = this.tipoLaudo;
		this.tipoLaudo = tipoLaudo;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoLaudo", tipoLaudoOld, tipoLaudo);
	}



	/**
	 * Return the value associated with the column: tipo
	 */
	public java.lang.Long getTipo () {
		return getPropertyValue(this, tipo, PROP_TIPO); 
	}

	/**
	 * Set the value related to the column: tipo
	 * @param tipo the tipo value
	 */
	public void setTipo (java.lang.Long tipo) {
//        java.lang.Long tipoOld = this.tipo;
		this.tipo = tipo;
//        this.getPropertyChangeSupport().firePropertyChange ("tipo", tipoOld, tipo);
	}



	/**
	 * Return the value associated with the column: nr_inicial
	 */
	public java.lang.Long getNrInicial () {
		return getPropertyValue(this, nrInicial, PROP_NR_INICIAL); 
	}

	/**
	 * Set the value related to the column: nr_inicial
	 * @param nrInicial the nr_inicial value
	 */
	public void setNrInicial (java.lang.Long nrInicial) {
//        java.lang.Long nrInicialOld = this.nrInicial;
		this.nrInicial = nrInicial;
//        this.getPropertyChangeSupport().firePropertyChange ("nrInicial", nrInicialOld, nrInicial);
	}



	/**
	 * Return the value associated with the column: seq_inicial
	 */
	public java.lang.Long getSeqInicial () {
		return getPropertyValue(this, seqInicial, PROP_SEQ_INICIAL); 
	}

	/**
	 * Set the value related to the column: seq_inicial
	 * @param seqInicial the seq_inicial value
	 */
	public void setSeqInicial (java.lang.Long seqInicial) {
//        java.lang.Long seqInicialOld = this.seqInicial;
		this.seqInicial = seqInicial;
//        this.getPropertyChangeSupport().firePropertyChange ("seqInicial", seqInicialOld, seqInicial);
	}



	/**
	 * Return the value associated with the column: nr_final
	 */
	public java.lang.Long getNrFinal () {
		return getPropertyValue(this, nrFinal, PROP_NR_FINAL); 
	}

	/**
	 * Set the value related to the column: nr_final
	 * @param nrFinal the nr_final value
	 */
	public void setNrFinal (java.lang.Long nrFinal) {
//        java.lang.Long nrFinalOld = this.nrFinal;
		this.nrFinal = nrFinal;
//        this.getPropertyChangeSupport().firePropertyChange ("nrFinal", nrFinalOld, nrFinal);
	}



	/**
	 * Return the value associated with the column: seq_final
	 */
	public java.lang.Long getSeqFinal () {
		return getPropertyValue(this, seqFinal, PROP_SEQ_FINAL); 
	}

	/**
	 * Set the value related to the column: seq_final
	 * @param seqFinal the seq_final value
	 */
	public void setSeqFinal (java.lang.Long seqFinal) {
//        java.lang.Long seqFinalOld = this.seqFinal;
		this.seqFinal = seqFinal;
//        this.getPropertyChangeSupport().firePropertyChange ("seqFinal", seqFinalOld, seqFinal);
	}



	/**
	 * Return the value associated with the column: sequencia_uso
	 */
	public java.lang.Long getSeqUso () {
		return getPropertyValue(this, seqUso, PROP_SEQ_USO); 
	}

	/**
	 * Set the value related to the column: sequencia_uso
	 * @param seqUso the sequencia_uso value
	 */
	public void setSeqUso (java.lang.Long seqUso) {
//        java.lang.Long seqUsoOld = this.seqUso;
		this.seqUso = seqUso;
//        this.getPropertyChangeSupport().firePropertyChange ("seqUso", seqUsoOld, seqUso);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: data_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: data_cadastro
	 * @param dataCadastro the data_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: mot_cancelamento
	 */
	public java.lang.String getMotivoCancelamento () {
		return getPropertyValue(this, motivoCancelamento, PROP_MOTIVO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: mot_cancelamento
	 * @param motivoCancelamento the mot_cancelamento value
	 */
	public void setMotivoCancelamento (java.lang.String motivoCancelamento) {
//        java.lang.String motivoCancelamentoOld = this.motivoCancelamento;
		this.motivoCancelamento = motivoCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoCancelamento", motivoCancelamentoOld, motivoCancelamento);
	}



	/**
	 * Return the value associated with the column: dt_inicio_campanha
	 */
	public java.util.Date getDataInicioCampanha () {
		return getPropertyValue(this, dataInicioCampanha, PROP_DATA_INICIO_CAMPANHA); 
	}

	/**
	 * Set the value related to the column: dt_inicio_campanha
	 * @param dataInicioCampanha the dt_inicio_campanha value
	 */
	public void setDataInicioCampanha (java.util.Date dataInicioCampanha) {
//        java.util.Date dataInicioCampanhaOld = this.dataInicioCampanha;
		this.dataInicioCampanha = dataInicioCampanha;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInicioCampanha", dataInicioCampanhaOld, dataInicioCampanha);
	}



	/**
	 * Return the value associated with the column: dt_fim_campanha
	 */
	public java.util.Date getDataFimCampanha () {
		return getPropertyValue(this, dataFimCampanha, PROP_DATA_FIM_CAMPANHA); 
	}

	/**
	 * Set the value related to the column: dt_fim_campanha
	 * @param dataFimCampanha the dt_fim_campanha value
	 */
	public void setDataFimCampanha (java.util.Date dataFimCampanha) {
//        java.util.Date dataFimCampanhaOld = this.dataFimCampanha;
		this.dataFimCampanha = dataFimCampanha;
//        this.getPropertyChangeSupport().firePropertyChange ("dataFimCampanha", dataFimCampanhaOld, dataFimCampanha);
	}



	/**
	 * Return the value associated with the column: tipo_financiamento
	 */
	public java.lang.Long getTipoFinanciamento () {
		return getPropertyValue(this, tipoFinanciamento, PROP_TIPO_FINANCIAMENTO); 
	}

	/**
	 * Set the value related to the column: tipo_financiamento
	 * @param tipoFinanciamento the tipo_financiamento value
	 */
	public void setTipoFinanciamento (java.lang.Long tipoFinanciamento) {
//        java.lang.Long tipoFinanciamentoOld = this.tipoFinanciamento;
		this.tipoFinanciamento = tipoFinanciamento;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoFinanciamento", tipoFinanciamentoOld, tipoFinanciamento);
	}



	/**
	 * Return the value associated with the column: cd_usuario_cadastro
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCadastro () {
		return getPropertyValue(this, usuarioCadastro, PROP_USUARIO_CADASTRO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_cadastro
	 * @param usuarioCadastro the cd_usuario_cadastro value
	 */
	public void setUsuarioCadastro (br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCadastroOld = this.usuarioCadastro;
		this.usuarioCadastro = usuarioCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadastro", usuarioCadastroOld, usuarioCadastro);
	}



	/**
	 * Return the value associated with the column: cd_usu_canc
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usu_canc
	 * @param usuarioCancelamento the cd_usu_canc value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.agendamento.NumeracaoAihApac)) return false;
		else {
			br.com.ksisolucoes.vo.agendamento.NumeracaoAihApac numeracaoAihApac = (br.com.ksisolucoes.vo.agendamento.NumeracaoAihApac) obj;
			if (null == this.getCodigo() || null == numeracaoAihApac.getCodigo()) return false;
			else return (this.getCodigo().equals(numeracaoAihApac.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}