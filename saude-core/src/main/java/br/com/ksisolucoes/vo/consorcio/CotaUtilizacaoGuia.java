package br.com.ksisolucoes.vo.consorcio;

import java.io.Serializable;

import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.vo.consorcio.base.BaseCotaUtilizacaoGuia;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class CotaUtilizacaoGuia extends BaseCotaUtilizacaoGuia implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public CotaUtilizacaoGuia () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public CotaUtilizacaoGuia (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public CotaUtilizacaoGuia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Empresa consorciado,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataHoraCadastro,
		java.util.Date mesAno,
		java.lang.Double valor,
		java.lang.Double valorUtilizado) {

		super (
			codigo,
			consorciado,
			usuario,
			dataHoraCadastro,
			mesAno,
			valor,
			valorUtilizado);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public Double getSaldo() {
		Dinheiro dinheiro = new Dinheiro(getValor());
		if (getValorUtilizado() != null){
			if (getValorUtilizado() > 0D) {
				dinheiro = dinheiro.subtrair(getValorUtilizado());
			}
		}
		return dinheiro.doubleValue();
	}
}