package br.com.ksisolucoes.vo.atendimento;

import java.io.Serializable;

import br.com.ksisolucoes.vo.atendimento.base.BaseBpaAtendimento;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class BpaAtendimento extends BaseBpaAtendimento implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public BpaAtendimento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public BpaAtendimento (java.lang.Long codigo) {
		super(codigo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (Long) key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}