package br.com.ksisolucoes.vo.geral.cnes;

import br.com.ksisolucoes.vo.geral.cnes.base.BaseCnesProcessoEmpresa;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;



public class CnesProcessoEmpresa extends BaseCnesProcessoEmpresa implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public CnesProcessoEmpresa () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public CnesProcessoEmpresa (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public CnesProcessoEmpresa (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.geral.cnes.CnesProcesso cnesProcesso,
		java.lang.String unidadeId,
		java.lang.String razaoSocial,
		java.lang.String nomeFantasia,
		java.lang.String logradouro,
		java.lang.String bairro,
		java.lang.String cep,
		java.lang.String statusMovimentacao,
		java.util.Date dataAtualizacao,
		java.lang.String usuarioAtualizacao) {

		super (
			codigo,
			cnesProcesso,
			unidadeId,
			razaoSocial,
			nomeFantasia,
			logradouro,
			bairro,
			cep,
			statusMovimentacao,
			dataAtualizacao,
			usuarioAtualizacao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getCpfCnpj(){
    	String cpfCnpj = StringUtils.isNotBlank(this.getCnpj()) ? this.getCnpj() : this.getCpf();
    	return StringUtils.isNotBlank(cpfCnpj) ? cpfCnpj : this.getCnpjMantenedora();
	}
}