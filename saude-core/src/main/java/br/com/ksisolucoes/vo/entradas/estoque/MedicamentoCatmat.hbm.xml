<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.entradas.estoque">
    <class name="MedicamentoCatmat"
           table="medicamento_catmat">
        <id
                column="cd_medicamento_catmat"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="assigned"/>
        </id>

        <version column="version" name="version" type="long"/>

        <property
                column="descricao"
                length="200"
                name="descricao"
                not-null="true"
                type="string"
        />

        <property
                column="catmat"
                length="15"
                name="catmat"
                not-null="true"
                type="string"
        />

        <property
                column="flag_tipo_produto"
                name="tipoCatmat"
                not-null="true"
                type="java.lang.Long"
        />

    </class>
</hibernate-mapping>
