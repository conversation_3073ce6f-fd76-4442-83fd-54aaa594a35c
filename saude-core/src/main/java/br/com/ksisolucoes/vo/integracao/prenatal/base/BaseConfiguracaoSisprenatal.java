package br.com.ksisolucoes.vo.integracao.prenatal.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the configuracao_sisprenatal table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="configuracao_sisprenatal"
 */

public abstract class BaseConfiguracaoSisprenatal extends BaseRootVO implements Serializable {

	public static String REF = "ConfiguracaoSisprenatal";
	public static final String PROP_USUARIO_SCPA = "usuarioScpa";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_USUARIO_ALTERACAO = "usuarioAlteracao";
	public static final String PROP_SENHA_SCPA = "senhaScpa";
	public static final String PROP_DATA_ALTERACAO = "dataAlteracao";


	// constructors
	public BaseConfiguracaoSisprenatal () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseConfiguracaoSisprenatal (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseConfiguracaoSisprenatal (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.String usuarioScpa,
		java.lang.String senhaScpa,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setUsuario(usuario);
		this.setUsuarioScpa(usuarioScpa);
		this.setSenhaScpa(senhaScpa);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String usuarioScpa;
	private java.lang.String senhaScpa;
	private java.util.Date dataCadastro;
	private java.util.Date dataAlteracao;

	// many to one
	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioAlteracao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_configuracao_sisprenatal"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: usuario_scpa
	 */
	public java.lang.String getUsuarioScpa () {
		return getPropertyValue(this, usuarioScpa, PROP_USUARIO_SCPA); 
	}

	/**
	 * Set the value related to the column: usuario_scpa
	 * @param usuarioScpa the usuario_scpa value
	 */
	public void setUsuarioScpa (java.lang.String usuarioScpa) {
//        java.lang.String usuarioScpaOld = this.usuarioScpa;
		this.usuarioScpa = usuarioScpa;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioScpa", usuarioScpaOld, usuarioScpa);
	}



	/**
	 * Return the value associated with the column: senha_scpa
	 */
	public java.lang.String getSenhaScpa () {
		return getPropertyValue(this, senhaScpa, PROP_SENHA_SCPA); 
	}

	/**
	 * Set the value related to the column: senha_scpa
	 * @param senhaScpa the senha_scpa value
	 */
	public void setSenhaScpa (java.lang.String senhaScpa) {
//        java.lang.String senhaScpaOld = this.senhaScpa;
		this.senhaScpa = senhaScpa;
//        this.getPropertyChangeSupport().firePropertyChange ("senhaScpa", senhaScpaOld, senhaScpa);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_alteracao
	 */
	public java.util.Date getDataAlteracao () {
		return getPropertyValue(this, dataAlteracao, PROP_DATA_ALTERACAO); 
	}

	/**
	 * Set the value related to the column: dt_alteracao
	 * @param dataAlteracao the dt_alteracao value
	 */
	public void setDataAlteracao (java.util.Date dataAlteracao) {
//        java.util.Date dataAlteracaoOld = this.dataAlteracao;
		this.dataAlteracao = dataAlteracao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAlteracao", dataAlteracaoOld, dataAlteracao);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_usuario_alteracao
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioAlteracao () {
		return getPropertyValue(this, usuarioAlteracao, PROP_USUARIO_ALTERACAO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_alteracao
	 * @param usuarioAlteracao the cd_usuario_alteracao value
	 */
	public void setUsuarioAlteracao (br.com.ksisolucoes.vo.controle.Usuario usuarioAlteracao) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioAlteracaoOld = this.usuarioAlteracao;
		this.usuarioAlteracao = usuarioAlteracao;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioAlteracao", usuarioAlteracaoOld, usuarioAlteracao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.integracao.prenatal.ConfiguracaoSisprenatal)) return false;
		else {
			br.com.ksisolucoes.vo.integracao.prenatal.ConfiguracaoSisprenatal configuracaoSisprenatal = (br.com.ksisolucoes.vo.integracao.prenatal.ConfiguracaoSisprenatal) obj;
			if (null == this.getCodigo() || null == configuracaoSisprenatal.getCodigo()) return false;
			else return (this.getCodigo().equals(configuracaoSisprenatal.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}