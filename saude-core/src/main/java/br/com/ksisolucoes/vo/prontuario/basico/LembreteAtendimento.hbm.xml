<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico">
    <class name="LembreteAtendimento" table="lembrete_atendimento">

        <id name="codigo" column="cd_lembrete_atendimento" type="java.lang.Long" >
            <generator class="sequence">
                <param name="sequence">seq_atendimento</param>
            </generator>
        </id>
        <version column="version" name="version" type="long" />

        <many-to-one
            name="atendimento"
            class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
            not-null="false"
        >
            <column name="nr_atendimento"/>
        </many-to-one>

        <many-to-one
            name="profissional"
            class="br.com.ksisolucoes.vo.cadsus.Profissional"
            not-null="false"
        >
            <column name="cd_profissional"/>
        </many-to-one>

        <many-to-one
            name="usuarioCadsus"
            class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"
            not-null="true"
        >
            <column name="cd_usuario_cadsus"/>
        </many-to-one>

        <property name="descricao" column="descricao" type="java.lang.String"
                  length="2048" not-null="true" />

        <property
            name="flagPublico"
            column="flag_publico"
            type="java.lang.Long"
            not-null="true"
        />

        <property
            name="flagAtivo"
            column="flag_ativo"
            type="java.lang.Long"
            not-null="true"
        />

        <many-to-one
            name="usuario"
            class="br.com.ksisolucoes.vo.controle.Usuario"
            not-null="true"
        >
            <column name="cd_usuario"/>
        </many-to-one>

        <property
            name="dataCadastro"
            type="java.util.Date"
            not-null="true"
        >
            <column name="dt_cadastro" sql-type="timestamp"/>
        </property>

        <property
                name="flagRecepcao"
                column="flag_recepcao"
                type="java.lang.Long"
                not-null="false"
        />

    </class>
</hibernate-mapping>