<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.esus"  >
    <class name="ExameEsus" table="exame_esus" >
         
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_exame_esus"
        >
            <generator class="assigned" />
        </id> 

        <version column="version" name="version" type="long" />

        <property
            name="descricaoExameEsus"
            column="ds_exame_esus"
            type="java.lang.String"
            not-null="true"
        />

		<property 
            name="codigoEsus"
            column="cd_esus"
            type="java.lang.String"
            not-null="true"
        />

    </class>
</hibernate-mapping>
