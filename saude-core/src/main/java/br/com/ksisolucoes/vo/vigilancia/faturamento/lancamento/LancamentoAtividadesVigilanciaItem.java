package br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento;

import java.io.Serializable;

import br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.base.BaseLancamentoAtividadesVigilanciaItem;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class LancamentoAtividadesVigilanciaItem extends BaseLancamentoAtividadesVigilanciaItem implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public LancamentoAtividadesVigilanciaItem () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public LancamentoAtividadesVigilanciaItem (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public LancamentoAtividadesVigilanciaItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilancia lancamentoAtividadesVigilancia,
		br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia atividadesVigilancia,
		br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
		java.lang.Long quantidade,
		java.util.Date dataCadastro,
		java.util.Date dataAlteracao) {

		super (
			codigo,
			lancamentoAtividadesVigilancia,
			atividadesVigilancia,
			usuarioCadastro,
			quantidade,
			dataCadastro,
			dataAlteracao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}