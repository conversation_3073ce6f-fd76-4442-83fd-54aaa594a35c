<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.consorcio">
    <class name="ModeloDocumentoConsorcio" table="modelo_documento_consorcio">
        <id
            name="codigo"
            column="cd_modelo_documento"
            type="java.lang.Long"
        />

        <version column="version" name="version" type="long" />

        <property 
            name="descricao"
            column="descricao"
            type="java.lang.String"
            length="30"
            not-null="true"
        />

        <property 
            name="modelo"
            column="modelo"
            type="java.lang.String"
            not-null="true"
        />
    </class>
</hibernate-mapping>
