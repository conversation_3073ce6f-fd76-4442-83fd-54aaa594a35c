package br.com.ksisolucoes.vo.vigilancia.investigacao;

import br.com.ksisolucoes.enums.IEnum;

public class InvestigacaoAgravoRotavirusEnum {

    public enum SimOuNao implements IEnum {
        SIM(1L, "Sim"),
        NAO(2L, "Não"),
        ;

        private Long value;
        private String descricao;

        SimOuNao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static ExclusivoOuMisto valueOf(Long value) {
            for (ExclusivoOuMisto v : ExclusivoOuMisto.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum ExclusivoOuMisto implements IEnum {
        EXCLUSIVO(1L, "Exclusivo"),
        MISTO(2L, "Misto"),
        ;

        private Long value;
        private String descricao;

        ExclusivoOuMisto(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static ExclusivoOuMisto valueOf(Long value) {
            for (ExclusivoOuMisto v : ExclusivoOuMisto.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum Criterio implements IEnum {
        LABORATORIAL(1L, "Laboratorial"),
        CLINICO_EPIDEMIOLOGICO(2L, "Clínico-epidemiológico"),
        CLINICO(3L, "Clínico"),
        ;

        private Long value;
        private String descricao;

        Criterio(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static ExclusivoOuMisto valueOf(Long value) {
            for (ExclusivoOuMisto v : ExclusivoOuMisto.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum EvolucaoEnum implements IEnum {
        CURA(1L, "Cura"),
        OBITO_ROTAVIRUS(2L, "Óbito por Rotavírus"),
        OBITO_OUTRAS_CAUSAS(3L, "Óbito por outras causas"),
        IGNORADO(9L, "Ignorado"),
        ;

        private Long value;
        private String descricao;

        EvolucaoEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static ExclusivoOuMisto valueOf(Long value) {
            for (ExclusivoOuMisto v : ExclusivoOuMisto.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }
}
