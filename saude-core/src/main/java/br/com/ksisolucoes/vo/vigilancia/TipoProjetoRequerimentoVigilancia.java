package br.com.ksisolucoes.vo.vigilancia;

import br.com.ksisolucoes.util.Valor;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.base.BaseTipoProjetoRequerimentoVigilancia;

import java.io.Serializable;



public class TipoProjetoRequerimentoVigilancia extends BaseTipoProjetoRequerimentoVigilancia implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public TipoProjetoRequerimentoVigilancia () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public TipoProjetoRequerimentoVigilancia (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public TipoProjetoRequerimentoVigilancia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia,
		br.com.ksisolucoes.vo.vigilancia.TipoProjetoVigilancia tipoProjetoVigilancia,
		java.lang.Double area) {

		super (
			codigo,
			requerimentoVigilancia,
			tipoProjetoVigilancia,
			area);
	}

/*[CONSTRUCTOR MARKER END]*/

	public String getDescricaoAreaFormatado() {
		if(getArea() != null) {
			return Valor.adicionarFormatacaoMonetaria(getArea()).concat(" m²");
		} else {
			return "";
		}
	}
    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}