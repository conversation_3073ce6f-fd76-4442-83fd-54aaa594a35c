<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia"  >
	<class name="TabelaCnae" table="tabela_cnae">
		<id
			column="cd_cnae"
			name="codigo"
			type="java.lang.Long"
		>
			<generator class="assigned" />
		</id> <version column="version" name="version" type="long" />

		<property
			column="cnae"
			name="cnae"
			not-null="true"
			type="java.lang.String"
		 />
		
		<property
			column="ds_atividade"
			name="descricaoAtividade"
			not-null="true"
			type="java.lang.String"
		 />
		<property
			column="grupo"
			name="grupo"
			not-null="true"
			type="java.lang.String"
		 />

		<property
			column="situacao"
			name="situacao"
			type="java.lang.Long"
			not-null="true"
		/>
	</class>
</hibernate-mapping>
