package br.com.ksisolucoes.vo.consorcio.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;


/**
 * This is an object that contains data related to the consorcio_prestador_servico_cota table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="consorcio_prestador_servico_cota"
 */

public abstract class BaseConsorcioPrestadorServicoCota extends BaseRootVO implements Serializable, Comparable<BaseConsorcioPrestadorServicoCota> {

	public static String REF = "ConsorcioPrestadorServicoCota";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_CONSORCIO_PRESTADOR_SERVICO = "consorcioPrestadorServico";
	public static final String PROP_COMPETENCIA = "competencia";
	public static final String PROP_COTA_CONSORCIO = "cotaConsorcio";
	public static final String PROP_CONTAGEM_COTA_CONSORCIO = "contagemCotaConsorcio";
	public static final String PROP_COTA_MUNICIPIO = "cotaMunicipio";
	public static final String PROP_CONTAGEM_COTA_MUNICIPIO = "contagemCotaMunicipio";


	// constructors
	public BaseConsorcioPrestadorServicoCota() {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseConsorcioPrestadorServicoCota(Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseConsorcioPrestadorServicoCota(
		Long codigo,
		br.com.ksisolucoes.vo.consorcio.ConsorcioPrestadorServico consorcioPrestadorServico) {

		this.setCodigo(codigo);
		this.setConsorcioPrestadorServico(consorcioPrestadorServico);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private Long codigo;

	// fields
	private java.util.Date competencia;
	private java.lang.Long cotaConsorcio;
	private java.lang.Long contagemCotaConsorcio;
	private java.lang.Long cotaMunicipio;
	private java.lang.Long contagemCotaMunicipio;

	// many to one
	private br.com.ksisolucoes.vo.consorcio.ConsorcioPrestadorServico consorcioPrestadorServico;

	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_consorcio_prestador_servico_cota"
     */
	public Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}

	/**
	 * Return the value associated with the column: cd_consorcio_prestador_servico
	 */
	public br.com.ksisolucoes.vo.consorcio.ConsorcioPrestadorServico getConsorcioPrestadorServico () {
		return getPropertyValue(this, consorcioPrestadorServico, PROP_CONSORCIO_PRESTADOR_SERVICO);
	}

	/**
	 * Set the value related to the column: cd_consorcio_prestador_servico
	 * @param consorcioPrestadorServico the cd_consorcio_prestador_servico value
	 */
	public void setConsorcioPrestadorServico (br.com.ksisolucoes.vo.consorcio.ConsorcioPrestadorServico consorcioPrestadorServico) {
		this.consorcioPrestadorServico = consorcioPrestadorServico;
	}



	/**
	 * Return the value associated with the column: competencia
	 */
	public java.util.Date getCompetencia () {
		return getPropertyValue(this, competencia, PROP_COMPETENCIA);
	}

	/**
	 * Set the value related to the column: competencia
	 * @param competencia the competencia value
	 */
	public void setCompetencia (java.util.Date competencia) {
		this.competencia = competencia;
	}

	/**
	 * Return the value associated with the column: cota_consorcio
	 */
	public java.lang.Long getCotaConsorcio() {
		return getPropertyValue(this, cotaConsorcio, PROP_COTA_CONSORCIO);
	}

	/**
	 * Set the value related to the column: cota_consorcio
	 * @param cotaConsorcio the cota_consorcio value
	 */
	public void setCotaConsorcio(java.lang.Long cotaConsorcio) {
		this.cotaConsorcio = cotaConsorcio;
	}

	/**
	 * Return the value associated with the column: contagem_cota_consorcio
	 */
	public java.lang.Long getContagemCotaConsorcio() {
		return getPropertyValue(this, contagemCotaConsorcio, PROP_CONTAGEM_COTA_CONSORCIO);
	}

	/**
	 * Set the value related to the column: contagem_cota_consorcio
	 * @param contagemCotaConsorcio the contagem_cota_consorcio value
	 */
	public void setContagemCotaConsorcio(java.lang.Long contagemCotaConsorcio) {
		this.contagemCotaConsorcio = contagemCotaConsorcio;
	}

	/**
	 * Return the value associated with the column: cota_municipio
	 */
	public java.lang.Long getCotaMunicipio() {
		return getPropertyValue(this, cotaMunicipio, PROP_COTA_MUNICIPIO);
	}

	/**
	 * Set the value related to the column: cota_municipio
	 * @param cotaMunicipio the cota_municipio value
	 */
	public void setCotaMunicipio(java.lang.Long cotaMunicipio) {
		this.cotaMunicipio = cotaMunicipio;
	}

	/**
	 * Return the value associated with the column: contagem_cota_municipio
	 */
	public java.lang.Long getContagemCotaMunicipio() {
		return getPropertyValue(this, contagemCotaMunicipio, PROP_CONTAGEM_COTA_MUNICIPIO);
	}

	/**
	 * Set the value related to the column: contagem_cota_municipio
	 * @param contagemCotaMunicipio the contagem_cota_municipio value
	 */
	public void setContagemCotaMunicipio(Long contagemCotaMunicipio) {
		this.contagemCotaMunicipio = contagemCotaMunicipio;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		BaseConsorcioPrestadorServicoCota that = (BaseConsorcioPrestadorServicoCota) o;
		return hashCode == that.hashCode &&
				Objects.equals(codigo, that.codigo) &&
				competencia.equals(that.competencia);
	}

	@Override
	public int hashCode() {
		return Objects.hash(hashCode, codigo, competencia);
	}

	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

	@Override
	public int compareTo(@NotNull BaseConsorcioPrestadorServicoCota o) {
		return this.getCompetencia().compareTo(o.getCompetencia());
	}

	public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }
}