<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.cadsus.cds"  >
    <class name="EsusFichaOdontoItemProcedimento" table="esus_ficha_odonto_item_procedimento">

        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_ficha_odonto_item_proced"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />
	
        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.cds.EsusFichaOdontoItem"
            column="cd_esus_ficha_odonto_item"
            name="esusFichaOdontoItem"
            not-null="true"
            />
	
        <many-to-one
            class="br.com.ksisolucoes.vo.esus.ProcedimentoEsus"
            column="cd_procedimento_esus"
            name="procedimentoEsus"
            not-null="false"
            />
	
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
            column="cd_procedimento"
            name="procedimento"
            not-null="false"
            />
        
        <property
            column="quantidade"
            name="quantidade"
            type="java.lang.Long"
            not-null="false"
        />
	
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuario"
            not-null="true"
            />
        
        
        <property
            column="dt_cadastro"
            name="dataCadastro"
            type="timestamp"
            not-null="true"
    	/>
    </class>
</hibernate-mapping>