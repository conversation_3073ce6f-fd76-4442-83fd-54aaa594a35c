<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="LaudoMedicamentosEspeciaisItem" table="laudo_medicamentos_especiais_item" >

        <id
                column="cd_laudo_medicamentos_especiais_item"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="assigned"/>
        </id>

        <version column="version" name="version" type="long" />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.basico.LaudoMedicamentosEspeciais"
                name="laudoMedicamentosEspeciais"
        >
            <column name="cd_laudo_medicamentos_especiais"/>
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.entradas.estoque.Produto"
                name="produto"
        >
            <column name="cod_pro" />
        </many-to-one>

        <property
                name="quantidadeSolicitacao1Mes"
                type="java.lang.Long"
                column="qtd_solic_1_mes"
                not-null="true"
        />

        <property
                name="quantidadeSolicitacao2Mes"
                type="java.lang.Long"
                column="qtd_solic_2_mes"
                not-null="false"
        />

        <property
                name="quantidadeSolicitacao3Mes"
                type="java.lang.Long"
                column="qtd_solic_3_mes"
                not-null="false"
        />

        <property
                name="quantidadeSolicitacao4Mes"
                type="java.lang.Long"
                column="qtd_solic_4_mes"
                not-null="false"
        />

        <property
                name="quantidadeSolicitacao5Mes"
                type="java.lang.Long"
                column="qtd_solic_5_mes"
                not-null="false"
        />

        <property
                name="quantidadeSolicitacao6Mes"
                type="java.lang.Long"
                column="qtd_solic_6_mes"
                not-null="false"
        />

    </class>
</hibernate-mapping>
