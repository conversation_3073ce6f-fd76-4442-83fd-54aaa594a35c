package br.com.ksisolucoes.vo.materiais.bnafar.dispensacao.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the bnafar_dispensacao table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="bnafar_dispensacao"
 */

public abstract class BaseBnafarDispensacao extends BaseRootVO implements Serializable {

	public static String REF = "BnafarDispensacao";
	public static final String PROP_DATA_VALIDADE = "dataValidade";
	public static final String PROP_PESO_USUARIO_CADSUS = "pesoUsuarioCadsus";
	public static final String PROP_DATA_ULTIMO_ENVIO = "dataUltimoEnvio";
	public static final String PROP_DISPENSACAO_MEDICAMENTO = "dispensacaoMedicamento";
	public static final String PROP_USUARIO_DISPENSADOR = "usuarioDispensador";
	public static final String PROP_DATA_DISPENSACAO = "dataDispensacao";
	public static final String PROP_CODIGO_CID = "codigoCid";
	public static final String PROP_EMPRESA = "Empresa";
	public static final String PROP_CNPJ_FABRICANTE = "cnpjFabricante";
	public static final String PROP_ALTURA_USUARIO_CADSUS = "alturaUsuarioCadsus";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_FABRICANTE = "fabricante";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_DISPENSACAO_MEDICAMENTO_ITEM = "dispensacaoMedicamentoItem";
	public static final String PROP_MEDICAMENTO_CATMAT = "MedicamentoCatmat";
	public static final String PROP_GRUPO_ESTOQUE = "grupoEstoque";
	public static final String PROP_DESCRICAO_FABRICANTE = "descricaoFabricante";
	public static final String PROP_QUANTIDADE = "quantidade";
	public static final String PROP_NUMERO_CNS = "numeroCns";
	public static final String PROP_USUARIO_CADSUS = "UsuarioCadsus";
	public static final String PROP_BNAFAR_DISPENSACAO_ELO = "BnafarDispensacaoElo";
	public static final String PROP_STATUS_REGISTRO = "statusRegistro";
	public static final String PROP_PROFISSIONAL = "Profissional";
	public static final String PROP_PRODUTO = "Produto";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";


	// constructors
	public BaseBnafarDispensacao () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseBnafarDispensacao (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseBnafarDispensacao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuarioDispensador,
		java.util.Date dataDispensacao,
		java.lang.Long statusRegistro,
		java.util.Date dataCadastro,
		java.util.Date dataUltimoEnvio) {

		this.setCodigo(codigo);
		this.setUsuarioDispensador(usuarioDispensador);
		this.setDataDispensacao(dataDispensacao);
		this.setStatusRegistro(statusRegistro);
		this.setDataCadastro(dataCadastro);
		this.setDataUltimoEnvio(dataUltimoEnvio);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String grupoEstoque;
	private java.lang.String cnpjFabricante;
	private java.lang.String descricaoFabricante;
	private java.util.Date dataValidade;
	private java.util.Date dataDispensacao;
	private java.lang.String numeroCns;
	private java.lang.Double pesoUsuarioCadsus;
	private java.lang.Double alturaUsuarioCadsus;
	private java.lang.Double quantidade;
	private java.lang.Long statusRegistro;
	private java.util.Date dataCadastro;
	private java.util.Date dataUltimoEnvio;
	private java.lang.String codigoCid;
	private java.util.Date dataCancelamento;

	// many to one
	private br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento dispensacaoMedicamento;
	private br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem dispensacaoMedicamentoItem;
	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private br.com.ksisolucoes.vo.entradas.estoque.Produto produto;
	private br.com.ksisolucoes.vo.entradas.estoque.MedicamentoCatmat medicamentoCatmat;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissional;
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioDispensador;
	private br.com.ksisolucoes.vo.materiais.bnafar.dispensacao.BnafarDispensacaoElo bnafarDispensacaoElo;
	private br.com.ksisolucoes.vo.entradas.estoque.Fabricante fabricante;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_bnafar_dispensacao"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: grupo_estoque
	 */
	public java.lang.String getGrupoEstoque () {
		return getPropertyValue(this, grupoEstoque, PROP_GRUPO_ESTOQUE); 
	}

	/**
	 * Set the value related to the column: grupo_estoque
	 * @param grupoEstoque the grupo_estoque value
	 */
	public void setGrupoEstoque (java.lang.String grupoEstoque) {
//        java.lang.String grupoEstoqueOld = this.grupoEstoque;
		this.grupoEstoque = grupoEstoque;
//        this.getPropertyChangeSupport().firePropertyChange ("grupoEstoque", grupoEstoqueOld, grupoEstoque);
	}



	/**
	 * Return the value associated with the column: cnpj_fabricante
	 */
	public java.lang.String getCnpjFabricante () {
		return getPropertyValue(this, cnpjFabricante, PROP_CNPJ_FABRICANTE); 
	}

	/**
	 * Set the value related to the column: cnpj_fabricante
	 * @param cnpjFabricante the cnpj_fabricante value
	 */
	public void setCnpjFabricante (java.lang.String cnpjFabricante) {
//        java.lang.String cnpjFabricanteOld = this.cnpjFabricante;
		this.cnpjFabricante = cnpjFabricante;
//        this.getPropertyChangeSupport().firePropertyChange ("cnpjFabricante", cnpjFabricanteOld, cnpjFabricante);
	}



	/**
	 * Return the value associated with the column: descricao_fabricante
	 */
	public java.lang.String getDescricaoFabricante () {
		return getPropertyValue(this, descricaoFabricante, PROP_DESCRICAO_FABRICANTE); 
	}

	/**
	 * Set the value related to the column: descricao_fabricante
	 * @param descricaoFabricante the descricao_fabricante value
	 */
	public void setDescricaoFabricante (java.lang.String descricaoFabricante) {
//        java.lang.String descricaoFabricanteOld = this.descricaoFabricante;
		this.descricaoFabricante = descricaoFabricante;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoFabricante", descricaoFabricanteOld, descricaoFabricante);
	}



	/**
	 * Return the value associated with the column: dt_validade
	 */
	public java.util.Date getDataValidade () {
		return getPropertyValue(this, dataValidade, PROP_DATA_VALIDADE); 
	}

	/**
	 * Set the value related to the column: dt_validade
	 * @param dataValidade the dt_validade value
	 */
	public void setDataValidade (java.util.Date dataValidade) {
//        java.util.Date dataValidadeOld = this.dataValidade;
		this.dataValidade = dataValidade;
//        this.getPropertyChangeSupport().firePropertyChange ("dataValidade", dataValidadeOld, dataValidade);
	}



	/**
	 * Return the value associated with the column: dt_dispensacao
	 */
	public java.util.Date getDataDispensacao () {
		return getPropertyValue(this, dataDispensacao, PROP_DATA_DISPENSACAO); 
	}

	/**
	 * Set the value related to the column: dt_dispensacao
	 * @param dataDispensacao the dt_dispensacao value
	 */
	public void setDataDispensacao (java.util.Date dataDispensacao) {
//        java.util.Date dataDispensacaoOld = this.dataDispensacao;
		this.dataDispensacao = dataDispensacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataDispensacao", dataDispensacaoOld, dataDispensacao);
	}



	/**
	 * Return the value associated with the column: nr_cns
	 */
	public java.lang.String getNumeroCns () {
		return getPropertyValue(this, numeroCns, PROP_NUMERO_CNS); 
	}

	/**
	 * Set the value related to the column: nr_cns
	 * @param numeroCns the nr_cns value
	 */
	public void setNumeroCns (java.lang.String numeroCns) {
//        java.lang.String numeroCnsOld = this.numeroCns;
		this.numeroCns = numeroCns;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroCns", numeroCnsOld, numeroCns);
	}



	/**
	 * Return the value associated with the column: peso_usuario_cadsus
	 */
	public java.lang.Double getPesoUsuarioCadsus () {
		return getPropertyValue(this, pesoUsuarioCadsus, PROP_PESO_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: peso_usuario_cadsus
	 * @param pesoUsuarioCadsus the peso_usuario_cadsus value
	 */
	public void setPesoUsuarioCadsus (java.lang.Double pesoUsuarioCadsus) {
//        java.lang.Double pesoUsuarioCadsusOld = this.pesoUsuarioCadsus;
		this.pesoUsuarioCadsus = pesoUsuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("pesoUsuarioCadsus", pesoUsuarioCadsusOld, pesoUsuarioCadsus);
	}



	/**
	 * Return the value associated with the column: altura_usuario_cadsus
	 */
	public java.lang.Double getAlturaUsuarioCadsus () {
		return getPropertyValue(this, alturaUsuarioCadsus, PROP_ALTURA_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: altura_usuario_cadsus
	 * @param alturaUsuarioCadsus the altura_usuario_cadsus value
	 */
	public void setAlturaUsuarioCadsus (java.lang.Double alturaUsuarioCadsus) {
//        java.lang.Double alturaUsuarioCadsusOld = this.alturaUsuarioCadsus;
		this.alturaUsuarioCadsus = alturaUsuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("alturaUsuarioCadsus", alturaUsuarioCadsusOld, alturaUsuarioCadsus);
	}



	/**
	 * Return the value associated with the column: quantidade
	 */
	public java.lang.Double getQuantidade () {
		return getPropertyValue(this, quantidade, PROP_QUANTIDADE); 
	}

	/**
	 * Set the value related to the column: quantidade
	 * @param quantidade the quantidade value
	 */
	public void setQuantidade (java.lang.Double quantidade) {
//        java.lang.Double quantidadeOld = this.quantidade;
		this.quantidade = quantidade;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidade", quantidadeOld, quantidade);
	}



	/**
	 * Return the value associated with the column: flag_status_registro
	 */
	public java.lang.Long getStatusRegistro () {
		return getPropertyValue(this, statusRegistro, PROP_STATUS_REGISTRO); 
	}

	/**
	 * Set the value related to the column: flag_status_registro
	 * @param statusRegistro the flag_status_registro value
	 */
	public void setStatusRegistro (java.lang.Long statusRegistro) {
//        java.lang.Long statusRegistroOld = this.statusRegistro;
		this.statusRegistro = statusRegistro;
//        this.getPropertyChangeSupport().firePropertyChange ("statusRegistro", statusRegistroOld, statusRegistro);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_ultimo_envio
	 */
	public java.util.Date getDataUltimoEnvio () {
		return getPropertyValue(this, dataUltimoEnvio, PROP_DATA_ULTIMO_ENVIO); 
	}

	/**
	 * Set the value related to the column: dt_ultimo_envio
	 * @param dataUltimoEnvio the dt_ultimo_envio value
	 */
	public void setDataUltimoEnvio (java.util.Date dataUltimoEnvio) {
//        java.util.Date dataUltimoEnvioOld = this.dataUltimoEnvio;
		this.dataUltimoEnvio = dataUltimoEnvio;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUltimoEnvio", dataUltimoEnvioOld, dataUltimoEnvio);
	}



	/**
	 * Return the value associated with the column: cd_cid
	 */
	public java.lang.String getCodigoCid () {
		return getPropertyValue(this, codigoCid, PROP_CODIGO_CID); 
	}

	/**
	 * Set the value related to the column: cd_cid
	 * @param codigoCid the cd_cid value
	 */
	public void setCodigoCid (java.lang.String codigoCid) {
//        java.lang.String codigoCidOld = this.codigoCid;
		this.codigoCid = codigoCid;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoCid", codigoCidOld, codigoCid);
	}



	/**
	 * Return the value associated with the column: cd_dispensacao_medicamento
	 */
	public br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento getDispensacaoMedicamento () {
		return getPropertyValue(this, dispensacaoMedicamento, PROP_DISPENSACAO_MEDICAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_dispensacao_medicamento
	 * @param dispensacaoMedicamento the cd_dispensacao_medicamento value
	 */
	public void setDispensacaoMedicamento (br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento dispensacaoMedicamento) {
//        br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento dispensacaoMedicamentoOld = this.dispensacaoMedicamento;
		this.dispensacaoMedicamento = dispensacaoMedicamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dispensacaoMedicamento", dispensacaoMedicamentoOld, dispensacaoMedicamento);
	}



	/**
	 * Return the value associated with the column: cd_dispensacao_medicamento_item
	 */
	public br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem getDispensacaoMedicamentoItem () {
		return getPropertyValue(this, dispensacaoMedicamentoItem, PROP_DISPENSACAO_MEDICAMENTO_ITEM); 
	}

	/**
	 * Set the value related to the column: cd_dispensacao_medicamento_item
	 * @param dispensacaoMedicamentoItem the cd_dispensacao_medicamento_item value
	 */
	public void setDispensacaoMedicamentoItem (br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem dispensacaoMedicamentoItem) {
//        br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem dispensacaoMedicamentoItemOld = this.dispensacaoMedicamentoItem;
		this.dispensacaoMedicamentoItem = dispensacaoMedicamentoItem;
//        this.getPropertyChangeSupport().firePropertyChange ("dispensacaoMedicamentoItem", dispensacaoMedicamentoItemOld, dispensacaoMedicamentoItem);
	}



	/**
	 * Return the value associated with the column: cd_empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: cd_empresa
	 * @param empresa the cd_empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: cd_produto
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Produto getProduto () {
		return getPropertyValue(this, produto, PROP_PRODUTO); 
	}

	/**
	 * Set the value related to the column: cd_produto
	 * @param produto the cd_produto value
	 */
	public void setProduto (br.com.ksisolucoes.vo.entradas.estoque.Produto produto) {
//        br.com.ksisolucoes.vo.entradas.estoque.Produto produtoOld = this.produto;
		this.produto = produto;
//        this.getPropertyChangeSupport().firePropertyChange ("produto", produtoOld, produto);
	}



	/**
	 * Return the value associated with the column: cd_medicamento_catmat
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.MedicamentoCatmat getMedicamentoCatmat () {
		return getPropertyValue(this, medicamentoCatmat, PROP_MEDICAMENTO_CATMAT); 
	}

	/**
	 * Set the value related to the column: cd_medicamento_catmat
	 * @param medicamentoCatmat the cd_medicamento_catmat value
	 */
	public void setMedicamentoCatmat (br.com.ksisolucoes.vo.entradas.estoque.MedicamentoCatmat medicamentoCatmat) {
//        br.com.ksisolucoes.vo.entradas.estoque.MedicamentoCatmat medicamentoCatmatOld = this.medicamentoCatmat;
		this.medicamentoCatmat = medicamentoCatmat;
//        this.getPropertyChangeSupport().firePropertyChange ("medicamentoCatmat", medicamentoCatmatOld, medicamentoCatmat);
	}



	/**
	 * Return the value associated with the column: cd_profissional
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissional () {
		return getPropertyValue(this, profissional, PROP_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_profissional
	 * @param profissional the cd_profissional value
	 */
	public void setProfissional (br.com.ksisolucoes.vo.cadsus.Profissional profissional) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalOld = this.profissional;
		this.profissional = profissional;
//        this.getPropertyChangeSupport().firePropertyChange ("profissional", profissionalOld, profissional);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cd_usuario_dispensador
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioDispensador () {
		return getPropertyValue(this, usuarioDispensador, PROP_USUARIO_DISPENSADOR); 
	}

	/**
	 * Set the value related to the column: cd_usuario_dispensador
	 * @param usuarioDispensador the cd_usuario_dispensador value
	 */
	public void setUsuarioDispensador (br.com.ksisolucoes.vo.controle.Usuario usuarioDispensador) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioDispensadorOld = this.usuarioDispensador;
		this.usuarioDispensador = usuarioDispensador;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioDispensador", usuarioDispensadorOld, usuarioDispensador);
	}



	/**
	 * Return the value associated with the column: cd_bnafar_dispensacao_elo
	 */
	public br.com.ksisolucoes.vo.materiais.bnafar.dispensacao.BnafarDispensacaoElo getBnafarDispensacaoElo () {
		return getPropertyValue(this, bnafarDispensacaoElo, PROP_BNAFAR_DISPENSACAO_ELO); 
	}

	/**
	 * Set the value related to the column: cd_bnafar_dispensacao_elo
	 * @param bnafarDispensacaoElo the cd_bnafar_dispensacao_elo value
	 */
	public void setBnafarDispensacaoElo (br.com.ksisolucoes.vo.materiais.bnafar.dispensacao.BnafarDispensacaoElo bnafarDispensacaoElo) {
//        br.com.ksisolucoes.vo.materiais.bnafar.dispensacao.BnafarDispensacaoElo bnafarDispensacaoEloOld = this.bnafarDispensacaoElo;
		this.bnafarDispensacaoElo = bnafarDispensacaoElo;
//        this.getPropertyChangeSupport().firePropertyChange ("bnafarDispensacaoElo", bnafarDispensacaoEloOld, bnafarDispensacaoElo);
	}



	/**
	 * Return the value associated with the column: cd_fabricante
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Fabricante getFabricante () {
		return getPropertyValue(this, fabricante, PROP_FABRICANTE); 
	}

	/**
	 * Set the value related to the column: cd_fabricante
	 * @param fabricante the cd_fabricante value
	 */
	public void setFabricante (br.com.ksisolucoes.vo.entradas.estoque.Fabricante fabricante) {
//        br.com.ksisolucoes.vo.entradas.estoque.Fabricante fabricanteOld = this.fabricante;
		this.fabricante = fabricante;
//        this.getPropertyChangeSupport().firePropertyChange ("fabricante", fabricanteOld, fabricante);
	}

	/**
	 * Return the value associated with the column: cd_usuario_cancelamento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO);
	}

	/**
	 * Set the value related to the column: cd_usuario_cancelamento
	 * @param usuarioCancelamento the cd_usuario_cancelamento value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}

	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_ULTIMO_ENVIO);
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}


	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.materiais.bnafar.dispensacao.BnafarDispensacao)) return false;
		else {
			br.com.ksisolucoes.vo.materiais.bnafar.dispensacao.BnafarDispensacao bnafarDispensacao = (br.com.ksisolucoes.vo.materiais.bnafar.dispensacao.BnafarDispensacao) obj;
			if (null == this.getCodigo() || null == bnafarDispensacao.getCodigo()) return false;
			else return (this.getCodigo().equals(bnafarDispensacao.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}