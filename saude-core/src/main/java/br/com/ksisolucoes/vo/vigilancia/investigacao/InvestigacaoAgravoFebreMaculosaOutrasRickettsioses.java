package br.com.ksisolucoes.vo.vigilancia.investigacao;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.base.BaseInvestigacaoAgravoFebreMaculosaOutrasRickettsioses;

import java.io.Serializable;

public class InvestigacaoAgravoFebreMaculosaOutrasRickettsioses extends BaseInvestigacaoAgravoFebreMaculosaOutrasRickettsioses implements CodigoManager {
    public InvestigacaoAgravoFebreMaculosaOutrasRickettsioses() { super(); }

    public InvestigacaoAgravoFebreMaculosaOutrasRickettsioses(Long codigo) {
        super(codigo);
    }

    public InvestigacaoAgravoFebreMaculosaOutrasRickettsioses(
            Long codigo,
            RegistroAgravo registroAgravo,
            TabelaCbo tabelaCbo,
            String flagInformacoesComplementares
    ) {
        super (
            codigo,
            registroAgravo,
            tabelaCbo,
            flagInformacoesComplementares
        );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public void setCodigoManager(Serializable key) {
        this.setCodigo((Long) key);
    }


    public static InvestigacaoAgravoFebreMaculosaOutrasRickettsioses buscaPorRegistroAgravo(RegistroAgravo registroAgravo) {
        InvestigacaoAgravoFebreMaculosaOutrasRickettsioses investigacao =
                LoadManager.getInstance(InvestigacaoAgravoFebreMaculosaOutrasRickettsioses.class)
                        .addProperties(new HQLProperties(InvestigacaoAgravoFebreMaculosaOutrasRickettsioses.class).getProperties())
                        .addProperties(new HQLProperties(
                                RegistroAgravo.class,
                                VOUtils.montarPath(InvestigacaoAgravoFebreMaculosaOutrasRickettsioses.PROP_REGISTRO_AGRAVO)).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(
                                VOUtils.montarPath(InvestigacaoAgravoFebreMaculosaOutrasRickettsioses.PROP_REGISTRO_AGRAVO, RegistroAgravo.PROP_CODIGO),
                                registroAgravo.getCodigo()))
                        .start().getVO();
        return investigacao;
    }
}
