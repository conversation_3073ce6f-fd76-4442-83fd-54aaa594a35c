package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseFormularioGestantes;

import java.io.Serializable;


public class FormularioGestantes extends BaseFormularioGestantes implements CodigoManager {
    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public FormularioGestantes() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public FormularioGestantes(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public FormularioGestantes(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.prontuario.basico.EstratificacaoRisco estratificacaoRisco) {

        super(
                codigo,
                estratificacaoRisco);
    }

    /*[CONSTRUCTOR MARKER END]*/

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }
}