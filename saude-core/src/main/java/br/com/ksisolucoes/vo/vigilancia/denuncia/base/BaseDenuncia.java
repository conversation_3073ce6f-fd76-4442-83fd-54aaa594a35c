package br.com.ksisolucoes.vo.vigilancia.denuncia.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the denuncia table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="denuncia"
 */

public abstract class BaseDenuncia extends BaseRootVO implements Serializable {

	public static String REF = "Denuncia";
	public static final String PROP_CODIGO_PROTOCOLO_EXTERNO = "codigoProtocoloExterno";
	public static final String PROP_VIGILANCIA_PESSOA_DENUNCIADA = "vigilanciaPessoaDenunciada";
	public static final String PROP_TELEFONE_DENUNCIANTE = "telefoneDenunciante";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_ENDERECO_DENUNCIANTE = "enderecoDenunciante";
	public static final String PROP_NUMERO_LOGRADOURO_DENUNCIANTE = "numeroLogradouroDenunciante";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_USUARIO_CADASTRO = "usuarioCadastro";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_DENUNCIADO = "denunciado";
	public static final String PROP_DENUNCIANTE = "denunciante";
	public static final String PROP_FLAG_ANONIMO = "flagAnonimo";
	public static final String PROP_MOTIVO_CANCELAMENTO = "motivoCancelamento";
	public static final String PROP_NUMERO_LOGRADOURO_DENUNCIADO = "numeroLogradouroDenunciado";
	public static final String PROP_TELEFONE_DENUNCIADO = "telefoneDenunciado";
	public static final String PROP_LOGRADOURO_DENUNCIANTE = "logradouroDenunciante";
	public static final String PROP_EMAIL_DENUNCIANTE = "emailDenunciante";
	public static final String PROP_STATUS = "status";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_TIPO_PESSOA_DENUNCIANTE = "tipoPessoaDenunciante";
	public static final String PROP_REQUERIMENTO_VIGILANCIA = "requerimentoVigilancia";
	public static final String PROP_ESTABELECIMENTO_DENUNCIADO = "estabelecimentoDenunciado";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TIPO_DENUNCIA = "tipoDenuncia";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";
	public static final String PROP_ENDERECO_DENUNCIADO = "enderecoDenunciado";
	public static final String PROP_CNPJ_CPF_DENUNCIANTE = "cnpjCpfDenunciante";
	public static final String PROP_DATA_CONCLUSAO = "dataConclusao";
	public static final String PROP_TIPO_DENUNCIADO = "tipoDenunciado";
	public static final String PROP_LOGRADOURO_DENUNCIADO = "logradouroDenunciado";
	public static final String PROP_PONTO_REFERENCIA = "pontoReferencia";
	public static final String PROP_JUSTIFICATIVA_ANONIMO = "justificativaAnonimo";
	public static final String PROP_COMPLEMENTO_LOGRADOURO_DENUNCIANTE = "complementoLogradouroDenunciante";
	public static final String PROP_COMPLEMENTO_LOGRADOURO_DENUNCIADO = "complementoLogradouroDenunciado";


	// constructors
	public BaseDenuncia () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseDenuncia (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseDenuncia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco enderecoDenunciado,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		br.com.ksisolucoes.vo.vigilancia.denuncia.TipoDenuncia tipoDenuncia,
		br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
		java.util.Date dataCadastro,
		java.lang.Long status) {

		this.setCodigo(codigo);
		this.setEnderecoDenunciado(enderecoDenunciado);
		this.setEmpresa(empresa);
		this.setTipoDenuncia(tipoDenuncia);
		this.setUsuarioCadastro(usuarioCadastro);
		this.setDataCadastro(dataCadastro);
		this.setStatus(status);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String denunciante;
	private java.lang.String denunciado;
	private java.util.Date dataCadastro;
	private java.util.Date dataConclusao;
	private java.lang.String observacao;
	private java.lang.Long status;
	private java.util.Date dataCancelamento;
	private java.lang.String motivoCancelamento;
	private java.lang.Long tipoDenunciado;
	private java.lang.String telefoneDenunciado;
	private java.lang.String complementoLogradouroDenunciado;
	private java.lang.String numeroLogradouroDenunciado;
	private java.lang.String telefoneDenunciante;
	private java.lang.String complementoLogradouroDenunciante;
	private java.lang.String numeroLogradouroDenunciante;
	private java.lang.String pontoReferencia;
	private java.lang.String codigoProtocoloExterno;
	private java.lang.Long flagAnonimo;
	private java.lang.String tipoPessoaDenunciante;
	private java.lang.String cnpjCpfDenunciante;
	private java.lang.String emailDenunciante;
	private java.lang.String logradouroDenunciante;
	private java.lang.String logradouroDenunciado;
	private java.lang.String justificativaAnonimo;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco enderecoDenunciado;
	private br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco enderecoDenunciante;
	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private br.com.ksisolucoes.vo.vigilancia.denuncia.TipoDenuncia tipoDenuncia;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;
	private br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimentoDenunciado;
	private br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia;
	private br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa vigilanciaPessoaDenunciada;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_denuncia "
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: denunciante
	 */
	public java.lang.String getDenunciante () {
		return getPropertyValue(this, denunciante, PROP_DENUNCIANTE); 
	}

	/**
	 * Set the value related to the column: denunciante
	 * @param denunciante the denunciante value
	 */
	public void setDenunciante (java.lang.String denunciante) {
//        java.lang.String denuncianteOld = this.denunciante;
		this.denunciante = denunciante;
//        this.getPropertyChangeSupport().firePropertyChange ("denunciante", denuncianteOld, denunciante);
	}



	/**
	 * Return the value associated with the column: denunciado
	 */
	public java.lang.String getDenunciado () {
		return getPropertyValue(this, denunciado, PROP_DENUNCIADO); 
	}

	/**
	 * Set the value related to the column: denunciado
	 * @param denunciado the denunciado value
	 */
	public void setDenunciado (java.lang.String denunciado) {
//        java.lang.String denunciadoOld = this.denunciado;
		this.denunciado = denunciado;
//        this.getPropertyChangeSupport().firePropertyChange ("denunciado", denunciadoOld, denunciado);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_conclusao
	 */
	public java.util.Date getDataConclusao () {
		return getPropertyValue(this, dataConclusao, PROP_DATA_CONCLUSAO); 
	}

	/**
	 * Set the value related to the column: dt_conclusao
	 * @param dataConclusao the dt_conclusao value
	 */
	public void setDataConclusao (java.util.Date dataConclusao) {
//        java.util.Date dataConclusaoOld = this.dataConclusao;
		this.dataConclusao = dataConclusao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataConclusao", dataConclusaoOld, dataConclusao);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: mot_cancelamento
	 */
	public java.lang.String getMotivoCancelamento () {
		return getPropertyValue(this, motivoCancelamento, PROP_MOTIVO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: mot_cancelamento
	 * @param motivoCancelamento the mot_cancelamento value
	 */
	public void setMotivoCancelamento (java.lang.String motivoCancelamento) {
//        java.lang.String motivoCancelamentoOld = this.motivoCancelamento;
		this.motivoCancelamento = motivoCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoCancelamento", motivoCancelamentoOld, motivoCancelamento);
	}



	/**
	 * Return the value associated with the column: tp_denunciado
	 */
	public java.lang.Long getTipoDenunciado () {
		return getPropertyValue(this, tipoDenunciado, PROP_TIPO_DENUNCIADO); 
	}

	/**
	 * Set the value related to the column: tp_denunciado
	 * @param tipoDenunciado the tp_denunciado value
	 */
	public void setTipoDenunciado (java.lang.Long tipoDenunciado) {
//        java.lang.Long tipoDenunciadoOld = this.tipoDenunciado;
		this.tipoDenunciado = tipoDenunciado;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDenunciado", tipoDenunciadoOld, tipoDenunciado);
	}



	/**
	 * Return the value associated with the column: nr_telefone_denunciado
	 */
	public java.lang.String getTelefoneDenunciado () {
		return getPropertyValue(this, telefoneDenunciado, PROP_TELEFONE_DENUNCIADO); 
	}

	/**
	 * Set the value related to the column: nr_telefone_denunciado
	 * @param telefoneDenunciado the nr_telefone_denunciado value
	 */
	public void setTelefoneDenunciado (java.lang.String telefoneDenunciado) {
//        java.lang.String telefoneDenunciadoOld = this.telefoneDenunciado;
		this.telefoneDenunciado = telefoneDenunciado;
//        this.getPropertyChangeSupport().firePropertyChange ("telefoneDenunciado", telefoneDenunciadoOld, telefoneDenunciado);
	}



	/**
	 * Return the value associated with the column: comp_logradouro_denunciado
	 */
	public java.lang.String getComplementoLogradouroDenunciado () {
		return getPropertyValue(this, complementoLogradouroDenunciado, PROP_COMPLEMENTO_LOGRADOURO_DENUNCIADO); 
	}

	/**
	 * Set the value related to the column: comp_logradouro_denunciado
	 * @param complementoLogradouroDenunciado the comp_logradouro_denunciado value
	 */
	public void setComplementoLogradouroDenunciado (java.lang.String complementoLogradouroDenunciado) {
//        java.lang.String complementoLogradouroDenunciadoOld = this.complementoLogradouroDenunciado;
		this.complementoLogradouroDenunciado = complementoLogradouroDenunciado;
//        this.getPropertyChangeSupport().firePropertyChange ("complementoLogradouroDenunciado", complementoLogradouroDenunciadoOld, complementoLogradouroDenunciado);
	}



	/**
	 * Return the value associated with the column: nr_logradouro_denunciado
	 */
	public java.lang.String getNumeroLogradouroDenunciado () {
		return getPropertyValue(this, numeroLogradouroDenunciado, PROP_NUMERO_LOGRADOURO_DENUNCIADO); 
	}

	/**
	 * Set the value related to the column: nr_logradouro_denunciado
	 * @param numeroLogradouroDenunciado the nr_logradouro_denunciado value
	 */
	public void setNumeroLogradouroDenunciado (java.lang.String numeroLogradouroDenunciado) {
//        java.lang.String numeroLogradouroDenunciadoOld = this.numeroLogradouroDenunciado;
		this.numeroLogradouroDenunciado = numeroLogradouroDenunciado;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroLogradouroDenunciado", numeroLogradouroDenunciadoOld, numeroLogradouroDenunciado);
	}



	/**
	 * Return the value associated with the column: nr_telefone_denunciante
	 */
	public java.lang.String getTelefoneDenunciante () {
		return getPropertyValue(this, telefoneDenunciante, PROP_TELEFONE_DENUNCIANTE); 
	}

	/**
	 * Set the value related to the column: nr_telefone_denunciante
	 * @param telefoneDenunciante the nr_telefone_denunciante value
	 */
	public void setTelefoneDenunciante (java.lang.String telefoneDenunciante) {
//        java.lang.String telefoneDenuncianteOld = this.telefoneDenunciante;
		this.telefoneDenunciante = telefoneDenunciante;
//        this.getPropertyChangeSupport().firePropertyChange ("telefoneDenunciante", telefoneDenuncianteOld, telefoneDenunciante);
	}



	/**
	 * Return the value associated with the column: comp_logradouro_denunciante
	 */
	public java.lang.String getComplementoLogradouroDenunciante () {
		return getPropertyValue(this, complementoLogradouroDenunciante, PROP_COMPLEMENTO_LOGRADOURO_DENUNCIANTE); 
	}

	/**
	 * Set the value related to the column: comp_logradouro_denunciante
	 * @param complementoLogradouroDenunciante the comp_logradouro_denunciante value
	 */
	public void setComplementoLogradouroDenunciante (java.lang.String complementoLogradouroDenunciante) {
//        java.lang.String complementoLogradouroDenuncianteOld = this.complementoLogradouroDenunciante;
		this.complementoLogradouroDenunciante = complementoLogradouroDenunciante;
//        this.getPropertyChangeSupport().firePropertyChange ("complementoLogradouroDenunciante", complementoLogradouroDenuncianteOld, complementoLogradouroDenunciante);
	}



	/**
	 * Return the value associated with the column: nr_logradouro_denunciante
	 */
	public java.lang.String getNumeroLogradouroDenunciante () {
		return getPropertyValue(this, numeroLogradouroDenunciante, PROP_NUMERO_LOGRADOURO_DENUNCIANTE); 
	}

	/**
	 * Set the value related to the column: nr_logradouro_denunciante
	 * @param numeroLogradouroDenunciante the nr_logradouro_denunciante value
	 */
	public void setNumeroLogradouroDenunciante (java.lang.String numeroLogradouroDenunciante) {
//        java.lang.String numeroLogradouroDenuncianteOld = this.numeroLogradouroDenunciante;
		this.numeroLogradouroDenunciante = numeroLogradouroDenunciante;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroLogradouroDenunciante", numeroLogradouroDenuncianteOld, numeroLogradouroDenunciante);
	}



	/**
	 * Return the value associated with the column: ponto_referencia
	 */
	public java.lang.String getPontoReferencia () {
		return getPropertyValue(this, pontoReferencia, PROP_PONTO_REFERENCIA); 
	}

	/**
	 * Set the value related to the column: ponto_referencia
	 * @param pontoReferencia the ponto_referencia value
	 */
	public void setPontoReferencia (java.lang.String pontoReferencia) {
//        java.lang.String pontoReferenciaOld = this.pontoReferencia;
		this.pontoReferencia = pontoReferencia;
//        this.getPropertyChangeSupport().firePropertyChange ("pontoReferencia", pontoReferenciaOld, pontoReferencia);
	}



	/**
	 * Return the value associated with the column: cd_protocolo_externo
	 */
	public java.lang.String getCodigoProtocoloExterno () {
		return getPropertyValue(this, codigoProtocoloExterno, PROP_CODIGO_PROTOCOLO_EXTERNO); 
	}

	/**
	 * Set the value related to the column: cd_protocolo_externo
	 * @param codigoProtocoloExterno the cd_protocolo_externo value
	 */
	public void setCodigoProtocoloExterno (java.lang.String codigoProtocoloExterno) {
//        java.lang.String codigoProtocoloExternoOld = this.codigoProtocoloExterno;
		this.codigoProtocoloExterno = codigoProtocoloExterno;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoProtocoloExterno", codigoProtocoloExternoOld, codigoProtocoloExterno);
	}



	/**
	 * Return the value associated with the column: flag_anonimo
	 */
	public java.lang.Long getFlagAnonimo () {
		return getPropertyValue(this, flagAnonimo, PROP_FLAG_ANONIMO); 
	}

	/**
	 * Set the value related to the column: flag_anonimo
	 * @param flagAnonimo the flag_anonimo value
	 */
	public void setFlagAnonimo (java.lang.Long flagAnonimo) {
//        java.lang.Long flagAnonimoOld = this.flagAnonimo;
		this.flagAnonimo = flagAnonimo;
//        this.getPropertyChangeSupport().firePropertyChange ("flagAnonimo", flagAnonimoOld, flagAnonimo);
	}



	/**
	 * Return the value associated with the column: tp_pessoa_denunciante
	 */
	public java.lang.String getTipoPessoaDenunciante () {
		return getPropertyValue(this, tipoPessoaDenunciante, PROP_TIPO_PESSOA_DENUNCIANTE); 
	}

	/**
	 * Set the value related to the column: tp_pessoa_denunciante
	 * @param tipoPessoaDenunciante the tp_pessoa_denunciante value
	 */
	public void setTipoPessoaDenunciante (java.lang.String tipoPessoaDenunciante) {
//        java.lang.String tipoPessoaDenuncianteOld = this.tipoPessoaDenunciante;
		this.tipoPessoaDenunciante = tipoPessoaDenunciante;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoPessoaDenunciante", tipoPessoaDenuncianteOld, tipoPessoaDenunciante);
	}



	/**
	 * Return the value associated with the column: cnpj_cpf_denunciante
	 */
	public java.lang.String getCnpjCpfDenunciante () {
		return getPropertyValue(this, cnpjCpfDenunciante, PROP_CNPJ_CPF_DENUNCIANTE); 
	}

	/**
	 * Set the value related to the column: cnpj_cpf_denunciante
	 * @param cnpjCpfDenunciante the cnpj_cpf_denunciante value
	 */
	public void setCnpjCpfDenunciante (java.lang.String cnpjCpfDenunciante) {
//        java.lang.String cnpjCpfDenuncianteOld = this.cnpjCpfDenunciante;
		this.cnpjCpfDenunciante = cnpjCpfDenunciante;
//        this.getPropertyChangeSupport().firePropertyChange ("cnpjCpfDenunciante", cnpjCpfDenuncianteOld, cnpjCpfDenunciante);
	}



	/**
	 * Return the value associated with the column: email_denunciante
	 */
	public java.lang.String getEmailDenunciante () {
		return getPropertyValue(this, emailDenunciante, PROP_EMAIL_DENUNCIANTE); 
	}

	/**
	 * Set the value related to the column: email_denunciante
	 * @param emailDenunciante the email_denunciante value
	 */
	public void setEmailDenunciante (java.lang.String emailDenunciante) {
//        java.lang.String emailDenuncianteOld = this.emailDenunciante;
		this.emailDenunciante = emailDenunciante;
//        this.getPropertyChangeSupport().firePropertyChange ("emailDenunciante", emailDenuncianteOld, emailDenunciante);
	}



	/**
	 * Return the value associated with the column: logradouro_denunciante
	 */
	public java.lang.String getLogradouroDenunciante () {
		return getPropertyValue(this, logradouroDenunciante, PROP_LOGRADOURO_DENUNCIANTE); 
	}

	/**
	 * Set the value related to the column: logradouro_denunciante
	 * @param logradouroDenunciante the logradouro_denunciante value
	 */
	public void setLogradouroDenunciante (java.lang.String logradouroDenunciante) {
//        java.lang.String logradouroDenuncianteOld = this.logradouroDenunciante;
		this.logradouroDenunciante = logradouroDenunciante;
//        this.getPropertyChangeSupport().firePropertyChange ("logradouroDenunciante", logradouroDenuncianteOld, logradouroDenunciante);
	}



	/**
	 * Return the value associated with the column: logradouro_denunciado
	 */
	public java.lang.String getLogradouroDenunciado () {
		return getPropertyValue(this, logradouroDenunciado, PROP_LOGRADOURO_DENUNCIADO); 
	}

	/**
	 * Set the value related to the column: logradouro_denunciado
	 * @param logradouroDenunciado the logradouro_denunciado value
	 */
	public void setLogradouroDenunciado (java.lang.String logradouroDenunciado) {
//        java.lang.String logradouroDenunciadoOld = this.logradouroDenunciado;
		this.logradouroDenunciado = logradouroDenunciado;
//        this.getPropertyChangeSupport().firePropertyChange ("logradouroDenunciado", logradouroDenunciadoOld, logradouroDenunciado);
	}



	/**
	 * Return the value associated with the column: justificativa_anonimo
	 */
	public java.lang.String getJustificativaAnonimo () {
		return getPropertyValue(this, justificativaAnonimo, PROP_JUSTIFICATIVA_ANONIMO); 
	}

	/**
	 * Set the value related to the column: justificativa_anonimo
	 * @param justificativaAnonimo the justificativa_anonimo value
	 */
	public void setJustificativaAnonimo (java.lang.String justificativaAnonimo) {
//        java.lang.String justificativaAnonimoOld = this.justificativaAnonimo;
		this.justificativaAnonimo = justificativaAnonimo;
//        this.getPropertyChangeSupport().firePropertyChange ("justificativaAnonimo", justificativaAnonimoOld, justificativaAnonimo);
	}



	/**
	 * Return the value associated with the column: cd_vigilancia_endereco
	 */
	public br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco getEnderecoDenunciado () {
		return getPropertyValue(this, enderecoDenunciado, PROP_ENDERECO_DENUNCIADO); 
	}

	/**
	 * Set the value related to the column: cd_vigilancia_endereco
	 * @param enderecoDenunciado the cd_vigilancia_endereco value
	 */
	public void setEnderecoDenunciado (br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco enderecoDenunciado) {
//        br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco enderecoDenunciadoOld = this.enderecoDenunciado;
		this.enderecoDenunciado = enderecoDenunciado;
//        this.getPropertyChangeSupport().firePropertyChange ("enderecoDenunciado", enderecoDenunciadoOld, enderecoDenunciado);
	}



	/**
	 * Return the value associated with the column: cd_endereco_denunciante
	 */
	public br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco getEnderecoDenunciante () {
		return getPropertyValue(this, enderecoDenunciante, PROP_ENDERECO_DENUNCIANTE); 
	}

	/**
	 * Set the value related to the column: cd_endereco_denunciante
	 * @param enderecoDenunciante the cd_endereco_denunciante value
	 */
	public void setEnderecoDenunciante (br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco enderecoDenunciante) {
//        br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco enderecoDenuncianteOld = this.enderecoDenunciante;
		this.enderecoDenunciante = enderecoDenunciante;
//        this.getPropertyChangeSupport().firePropertyChange ("enderecoDenunciante", enderecoDenuncianteOld, enderecoDenunciante);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: cd_tipo_denuncia
	 */
	public br.com.ksisolucoes.vo.vigilancia.denuncia.TipoDenuncia getTipoDenuncia () {
		return getPropertyValue(this, tipoDenuncia, PROP_TIPO_DENUNCIA); 
	}

	/**
	 * Set the value related to the column: cd_tipo_denuncia
	 * @param tipoDenuncia the cd_tipo_denuncia value
	 */
	public void setTipoDenuncia (br.com.ksisolucoes.vo.vigilancia.denuncia.TipoDenuncia tipoDenuncia) {
//        br.com.ksisolucoes.vo.vigilancia.denuncia.TipoDenuncia tipoDenunciaOld = this.tipoDenuncia;
		this.tipoDenuncia = tipoDenuncia;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDenuncia", tipoDenunciaOld, tipoDenuncia);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCadastro () {
		return getPropertyValue(this, usuarioCadastro, PROP_USUARIO_CADASTRO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuarioCadastro the cd_usuario value
	 */
	public void setUsuarioCadastro (br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCadastroOld = this.usuarioCadastro;
		this.usuarioCadastro = usuarioCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadastro", usuarioCadastroOld, usuarioCadastro);
	}



	/**
	 * Return the value associated with the column: cd_usuario_can
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_can
	 * @param usuarioCancelamento the cd_usuario_can value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}



	/**
	 * Return the value associated with the column: cd_estabelecimento_denunciado
	 */
	public br.com.ksisolucoes.vo.vigilancia.Estabelecimento getEstabelecimentoDenunciado () {
		return getPropertyValue(this, estabelecimentoDenunciado, PROP_ESTABELECIMENTO_DENUNCIADO); 
	}

	/**
	 * Set the value related to the column: cd_estabelecimento_denunciado
	 * @param estabelecimentoDenunciado the cd_estabelecimento_denunciado value
	 */
	public void setEstabelecimentoDenunciado (br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimentoDenunciado) {
//        br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimentoDenunciadoOld = this.estabelecimentoDenunciado;
		this.estabelecimentoDenunciado = estabelecimentoDenunciado;
//        this.getPropertyChangeSupport().firePropertyChange ("estabelecimentoDenunciado", estabelecimentoDenunciadoOld, estabelecimentoDenunciado);
	}



	/**
	 * Return the value associated with the column: cd_req_vigilancia
	 */
	public br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia getRequerimentoVigilancia () {
		return getPropertyValue(this, requerimentoVigilancia, PROP_REQUERIMENTO_VIGILANCIA); 
	}

	/**
	 * Set the value related to the column: cd_req_vigilancia
	 * @param requerimentoVigilancia the cd_req_vigilancia value
	 */
	public void setRequerimentoVigilancia (br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia) {
//        br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilanciaOld = this.requerimentoVigilancia;
		this.requerimentoVigilancia = requerimentoVigilancia;
//        this.getPropertyChangeSupport().firePropertyChange ("requerimentoVigilancia", requerimentoVigilanciaOld, requerimentoVigilancia);
	}



	/**
	 * Return the value associated with the column: cd_vigilancia_pessoa_denunciada
	 */
	public br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa getVigilanciaPessoaDenunciada () {
		return getPropertyValue(this, vigilanciaPessoaDenunciada, PROP_VIGILANCIA_PESSOA_DENUNCIADA); 
	}

	/**
	 * Set the value related to the column: cd_vigilancia_pessoa_denunciada
	 * @param vigilanciaPessoaDenunciada the cd_vigilancia_pessoa_denunciada value
	 */
	public void setVigilanciaPessoaDenunciada (br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa vigilanciaPessoaDenunciada) {
//        br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa vigilanciaPessoaDenunciadaOld = this.vigilanciaPessoaDenunciada;
		this.vigilanciaPessoaDenunciada = vigilanciaPessoaDenunciada;
//        this.getPropertyChangeSupport().firePropertyChange ("vigilanciaPessoaDenunciada", vigilanciaPessoaDenunciadaOld, vigilanciaPessoaDenunciada);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia denuncia = (br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia) obj;
			if (null == this.getCodigo() || null == denuncia.getCodigo()) return false;
			else return (this.getCodigo().equals(denuncia.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}