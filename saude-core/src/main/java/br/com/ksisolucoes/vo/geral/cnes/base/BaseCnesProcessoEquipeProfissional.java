package br.com.ksisolucoes.vo.geral.cnes.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the cnes_processo_equipe_prof table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="cnes_processo_equipe_prof"
 */

public abstract class BaseCnesProcessoEquipeProfissional extends BaseRootVO implements Serializable {

	public static String REF = "CnesProcessoEquipeProfissional";
	public static final String PROP_CNES_ATEND_COMPLEMENTAR1 = "cnesAtendComplementar1";
	public static final String PROP_CODIGO_CBO = "codigoCbo";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_PROFISSIONAL_ID = "profissionalId";
	public static final String PROP_DATA_ENTRADA = "dataEntrada";
	public static final String PROP_CODIGO_HORA_OUTRO = "codigoHoraOutro";
	public static final String PROP_CNES_ATEND_COMPLEMENTAR2 = "cnesAtendComplementar2";
	public static final String PROP_DATA_DESLIGAMENTO = "dataDesligamento";
	public static final String PROP_CNES_ATEND_COMPLEMENTAR3 = "cnesAtendComplementar3";
	public static final String PROP_CARGA_HORARIA_OUTROS_CARGA_HORARIA_DIFERENCIADA_RESIDENCIA_MEDICA = "cargaHorariaOutrosCargaHorariaDiferenciadaResidenciaMedica";
	public static final String PROP_CNES1_CARGA_HORARIA_DIFERENCIADA_SISTEMA_HPP = "cnes1CargaHorariaDiferenciadaSistemaHpp";
	public static final String PROP_FLAG_EQUIPE_MINIMA = "flagEquipeMinima";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_CODIGO_MUNICIPIO_OUTRA_EQUIPE = "codigoMunicipioOutraEquipe";
	public static final String PROP_INDICA_VINCULACAO = "indicaVinculacao";
	public static final String PROP_MICRO_AREA = "microArea";
	public static final String PROP_CODIGO_HORA_AMBULATORIAL = "codigoHoraAmbulatorial";
	public static final String PROP_CODIGO_HORA_HOSPITAL = "codigoHoraHospital";
	public static final String PROP_CNES1_CARGA_HORARIA_DIFERENCIADA_SISTEMA_PENITENCIARIO = "cnes1CargaHorariaDiferenciadaSistemaPenitenciario";
	public static final String PROP_CNES_PROCESSO_EQUIPE = "cnesProcessoEquipe";
	public static final String PROP_TIPO_SUS_NAO_SUS = "tipoSusNaoSus";
	public static final String PROP_CNES_OUTRA_EQUIPE = "cnesOutraEquipe";


	// constructors
	public BaseCnesProcessoEquipeProfissional () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseCnesProcessoEquipeProfissional (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseCnesProcessoEquipeProfissional (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.geral.cnes.CnesProcessoEquipe cnesProcessoEquipe,
		java.lang.String profissionalId,
		java.lang.String codigoCbo,
		java.lang.String indicaVinculacao,
		java.lang.String tipoSusNaoSus) {

		this.setCodigo(codigo);
		this.setCnesProcessoEquipe(cnesProcessoEquipe);
		this.setProfissionalId(profissionalId);
		this.setCodigoCbo(codigoCbo);
		this.setIndicaVinculacao(indicaVinculacao);
		this.setTipoSusNaoSus(tipoSusNaoSus);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String profissionalId;
	private java.lang.String codigoCbo;
	private java.lang.String indicaVinculacao;
	private java.lang.String tipoSusNaoSus;
	private java.lang.Long codigoHoraAmbulatorial;
	private java.lang.Long codigoHoraHospital;
	private java.lang.Long codigoHoraOutro;
	private java.lang.String flagEquipeMinima;
	private java.lang.String microArea;
	private java.lang.String cnesOutraEquipe;
	private java.lang.String codigoMunicipioOutraEquipe;
	private java.util.Date dataEntrada;
	private java.util.Date dataDesligamento;
	private java.lang.String cnesAtendComplementar1;
	private java.lang.String cnesAtendComplementar2;
	private java.lang.String cnesAtendComplementar3;
	private java.lang.String cnes1CargaHorariaDiferenciadaSistemaPenitenciario;
	private java.lang.String cnes1CargaHorariaDiferenciadaSistemaHpp;
	private java.lang.String cargaHorariaOutrosCargaHorariaDiferenciadaResidenciaMedica;
	private java.lang.String usuario;

	// many to one
	private br.com.ksisolucoes.vo.geral.cnes.CnesProcessoEquipe cnesProcessoEquipe;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_cnes_processo_equipe_prof"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: prof_id
	 */
	public java.lang.String getProfissionalId () {
		return getPropertyValue(this, profissionalId, PROP_PROFISSIONAL_ID); 
	}

	/**
	 * Set the value related to the column: prof_id
	 * @param profissionalId the prof_id value
	 */
	public void setProfissionalId (java.lang.String profissionalId) {
//        java.lang.String profissionalIdOld = this.profissionalId;
		this.profissionalId = profissionalId;
//        this.getPropertyChangeSupport().firePropertyChange ("profissionalId", profissionalIdOld, profissionalId);
	}



	/**
	 * Return the value associated with the column: cod_cbo
	 */
	public java.lang.String getCodigoCbo () {
		return getPropertyValue(this, codigoCbo, PROP_CODIGO_CBO); 
	}

	/**
	 * Set the value related to the column: cod_cbo
	 * @param codigoCbo the cod_cbo value
	 */
	public void setCodigoCbo (java.lang.String codigoCbo) {
//        java.lang.String codigoCboOld = this.codigoCbo;
		this.codigoCbo = codigoCbo;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoCbo", codigoCboOld, codigoCbo);
	}



	/**
	 * Return the value associated with the column: ind_vinc
	 */
	public java.lang.String getIndicaVinculacao () {
		return getPropertyValue(this, indicaVinculacao, PROP_INDICA_VINCULACAO); 
	}

	/**
	 * Set the value related to the column: ind_vinc
	 * @param indicaVinculacao the ind_vinc value
	 */
	public void setIndicaVinculacao (java.lang.String indicaVinculacao) {
//        java.lang.String indicaVinculacaoOld = this.indicaVinculacao;
		this.indicaVinculacao = indicaVinculacao;
//        this.getPropertyChangeSupport().firePropertyChange ("indicaVinculacao", indicaVinculacaoOld, indicaVinculacao);
	}



	/**
	 * Return the value associated with the column: tp_sus_nao_sus
	 */
	public java.lang.String getTipoSusNaoSus () {
		return getPropertyValue(this, tipoSusNaoSus, PROP_TIPO_SUS_NAO_SUS); 
	}

	/**
	 * Set the value related to the column: tp_sus_nao_sus
	 * @param tipoSusNaoSus the tp_sus_nao_sus value
	 */
	public void setTipoSusNaoSus (java.lang.String tipoSusNaoSus) {
//        java.lang.String tipoSusNaoSusOld = this.tipoSusNaoSus;
		this.tipoSusNaoSus = tipoSusNaoSus;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoSusNaoSus", tipoSusNaoSusOld, tipoSusNaoSus);
	}



	/**
	 * Return the value associated with the column: cod_hora_amb
	 */
	public java.lang.Long getCodigoHoraAmbulatorial () {
		return getPropertyValue(this, codigoHoraAmbulatorial, PROP_CODIGO_HORA_AMBULATORIAL); 
	}

	/**
	 * Set the value related to the column: cod_hora_amb
	 * @param codigoHoraAmbulatorial the cod_hora_amb value
	 */
	public void setCodigoHoraAmbulatorial (java.lang.Long codigoHoraAmbulatorial) {
//        java.lang.Long codigoHoraAmbulatorialOld = this.codigoHoraAmbulatorial;
		this.codigoHoraAmbulatorial = codigoHoraAmbulatorial;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoHoraAmbulatorial", codigoHoraAmbulatorialOld, codigoHoraAmbulatorial);
	}



	/**
	 * Return the value associated with the column: cod_hora_hosp
	 */
	public java.lang.Long getCodigoHoraHospital () {
		return getPropertyValue(this, codigoHoraHospital, PROP_CODIGO_HORA_HOSPITAL); 
	}

	/**
	 * Set the value related to the column: cod_hora_hosp
	 * @param codigoHoraHospital the cod_hora_hosp value
	 */
	public void setCodigoHoraHospital (java.lang.Long codigoHoraHospital) {
//        java.lang.Long codigoHoraHospitalOld = this.codigoHoraHospital;
		this.codigoHoraHospital = codigoHoraHospital;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoHoraHospital", codigoHoraHospitalOld, codigoHoraHospital);
	}



	/**
	 * Return the value associated with the column: cod_hora_outro
	 */
	public java.lang.Long getCodigoHoraOutro () {
		return getPropertyValue(this, codigoHoraOutro, PROP_CODIGO_HORA_OUTRO); 
	}

	/**
	 * Set the value related to the column: cod_hora_outro
	 * @param codigoHoraOutro the cod_hora_outro value
	 */
	public void setCodigoHoraOutro (java.lang.Long codigoHoraOutro) {
//        java.lang.Long codigoHoraOutroOld = this.codigoHoraOutro;
		this.codigoHoraOutro = codigoHoraOutro;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoHoraOutro", codigoHoraOutroOld, codigoHoraOutro);
	}



	/**
	 * Return the value associated with the column: fl_equipe_minima
	 */
	public java.lang.String getFlagEquipeMinima () {
		return getPropertyValue(this, flagEquipeMinima, PROP_FLAG_EQUIPE_MINIMA); 
	}

	/**
	 * Set the value related to the column: fl_equipe_minima
	 * @param flagEquipeMinima the fl_equipe_minima value
	 */
	public void setFlagEquipeMinima (java.lang.String flagEquipeMinima) {
//        java.lang.String flagEquipeMinimaOld = this.flagEquipeMinima;
		this.flagEquipeMinima = flagEquipeMinima;
//        this.getPropertyChangeSupport().firePropertyChange ("flagEquipeMinima", flagEquipeMinimaOld, flagEquipeMinima);
	}



	/**
	 * Return the value associated with the column: micro_area
	 */
	public java.lang.String getMicroArea () {
		return getPropertyValue(this, microArea, PROP_MICRO_AREA); 
	}

	/**
	 * Set the value related to the column: micro_area
	 * @param microArea the micro_area value
	 */
	public void setMicroArea (java.lang.String microArea) {
//        java.lang.String microAreaOld = this.microArea;
		this.microArea = microArea;
//        this.getPropertyChangeSupport().firePropertyChange ("microArea", microAreaOld, microArea);
	}



	/**
	 * Return the value associated with the column: cnes_outra_equipe
	 */
	public java.lang.String getCnesOutraEquipe () {
		return getPropertyValue(this, cnesOutraEquipe, PROP_CNES_OUTRA_EQUIPE); 
	}

	/**
	 * Set the value related to the column: cnes_outra_equipe
	 * @param cnesOutraEquipe the cnes_outra_equipe value
	 */
	public void setCnesOutraEquipe (java.lang.String cnesOutraEquipe) {
//        java.lang.String cnesOutraEquipeOld = this.cnesOutraEquipe;
		this.cnesOutraEquipe = cnesOutraEquipe;
//        this.getPropertyChangeSupport().firePropertyChange ("cnesOutraEquipe", cnesOutraEquipeOld, cnesOutraEquipe);
	}



	/**
	 * Return the value associated with the column: cod_munic_outra_equipe
	 */
	public java.lang.String getCodigoMunicipioOutraEquipe () {
		return getPropertyValue(this, codigoMunicipioOutraEquipe, PROP_CODIGO_MUNICIPIO_OUTRA_EQUIPE); 
	}

	/**
	 * Set the value related to the column: cod_munic_outra_equipe
	 * @param codigoMunicipioOutraEquipe the cod_munic_outra_equipe value
	 */
	public void setCodigoMunicipioOutraEquipe (java.lang.String codigoMunicipioOutraEquipe) {
//        java.lang.String codigoMunicipioOutraEquipeOld = this.codigoMunicipioOutraEquipe;
		this.codigoMunicipioOutraEquipe = codigoMunicipioOutraEquipe;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoMunicipioOutraEquipe", codigoMunicipioOutraEquipeOld, codigoMunicipioOutraEquipe);
	}



	/**
	 * Return the value associated with the column: dt_entrada
	 */
	public java.util.Date getDataEntrada () {
		return getPropertyValue(this, dataEntrada, PROP_DATA_ENTRADA); 
	}

	/**
	 * Set the value related to the column: dt_entrada
	 * @param dataEntrada the dt_entrada value
	 */
	public void setDataEntrada (java.util.Date dataEntrada) {
//        java.util.Date dataEntradaOld = this.dataEntrada;
		this.dataEntrada = dataEntrada;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEntrada", dataEntradaOld, dataEntrada);
	}



	/**
	 * Return the value associated with the column: dt_desligamento
	 */
	public java.util.Date getDataDesligamento () {
		return getPropertyValue(this, dataDesligamento, PROP_DATA_DESLIGAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_desligamento
	 * @param dataDesligamento the dt_desligamento value
	 */
	public void setDataDesligamento (java.util.Date dataDesligamento) {
//        java.util.Date dataDesligamentoOld = this.dataDesligamento;
		this.dataDesligamento = dataDesligamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataDesligamento", dataDesligamentoOld, dataDesligamento);
	}



	/**
	 * Return the value associated with the column: cnes_atend_comp_1
	 */
	public java.lang.String getCnesAtendComplementar1 () {
		return getPropertyValue(this, cnesAtendComplementar1, PROP_CNES_ATEND_COMPLEMENTAR1); 
	}

	/**
	 * Set the value related to the column: cnes_atend_comp_1
	 * @param cnesAtendComplementar1 the cnes_atend_comp_1 value
	 */
	public void setCnesAtendComplementar1 (java.lang.String cnesAtendComplementar1) {
//        java.lang.String cnesAtendComplementar1Old = this.cnesAtendComplementar1;
		this.cnesAtendComplementar1 = cnesAtendComplementar1;
//        this.getPropertyChangeSupport().firePropertyChange ("cnesAtendComplementar1", cnesAtendComplementar1Old, cnesAtendComplementar1);
	}



	/**
	 * Return the value associated with the column: cnes_atend_comp_2
	 */
	public java.lang.String getCnesAtendComplementar2 () {
		return getPropertyValue(this, cnesAtendComplementar2, PROP_CNES_ATEND_COMPLEMENTAR2); 
	}

	/**
	 * Set the value related to the column: cnes_atend_comp_2
	 * @param cnesAtendComplementar2 the cnes_atend_comp_2 value
	 */
	public void setCnesAtendComplementar2 (java.lang.String cnesAtendComplementar2) {
//        java.lang.String cnesAtendComplementar2Old = this.cnesAtendComplementar2;
		this.cnesAtendComplementar2 = cnesAtendComplementar2;
//        this.getPropertyChangeSupport().firePropertyChange ("cnesAtendComplementar2", cnesAtendComplementar2Old, cnesAtendComplementar2);
	}



	/**
	 * Return the value associated with the column: cnes_atend_comp_3
	 */
	public java.lang.String getCnesAtendComplementar3 () {
		return getPropertyValue(this, cnesAtendComplementar3, PROP_CNES_ATEND_COMPLEMENTAR3); 
	}

	/**
	 * Set the value related to the column: cnes_atend_comp_3
	 * @param cnesAtendComplementar3 the cnes_atend_comp_3 value
	 */
	public void setCnesAtendComplementar3 (java.lang.String cnesAtendComplementar3) {
//        java.lang.String cnesAtendComplementar3Old = this.cnesAtendComplementar3;
		this.cnesAtendComplementar3 = cnesAtendComplementar3;
//        this.getPropertyChangeSupport().firePropertyChange ("cnesAtendComplementar3", cnesAtendComplementar3Old, cnesAtendComplementar3);
	}



	/**
	 * Return the value associated with the column: cnes_1_ch_difer_sistpenit
	 */
	public java.lang.String getCnes1CargaHorariaDiferenciadaSistemaPenitenciario () {
		return getPropertyValue(this, cnes1CargaHorariaDiferenciadaSistemaPenitenciario, PROP_CNES1_CARGA_HORARIA_DIFERENCIADA_SISTEMA_PENITENCIARIO); 
	}

	/**
	 * Set the value related to the column: cnes_1_ch_difer_sistpenit
	 * @param cnes1CargaHorariaDiferenciadaSistemaPenitenciario the cnes_1_ch_difer_sistpenit value
	 */
	public void setCnes1CargaHorariaDiferenciadaSistemaPenitenciario (java.lang.String cnes1CargaHorariaDiferenciadaSistemaPenitenciario) {
//        java.lang.String cnes1CargaHorariaDiferenciadaSistemaPenitenciarioOld = this.cnes1CargaHorariaDiferenciadaSistemaPenitenciario;
		this.cnes1CargaHorariaDiferenciadaSistemaPenitenciario = cnes1CargaHorariaDiferenciadaSistemaPenitenciario;
//        this.getPropertyChangeSupport().firePropertyChange ("cnes1CargaHorariaDiferenciadaSistemaPenitenciario", cnes1CargaHorariaDiferenciadaSistemaPenitenciarioOld, cnes1CargaHorariaDiferenciadaSistemaPenitenciario);
	}



	/**
	 * Return the value associated with the column: cnes_1_ch_difer_hpp
	 */
	public java.lang.String getCnes1CargaHorariaDiferenciadaSistemaHpp () {
		return getPropertyValue(this, cnes1CargaHorariaDiferenciadaSistemaHpp, PROP_CNES1_CARGA_HORARIA_DIFERENCIADA_SISTEMA_HPP); 
	}

	/**
	 * Set the value related to the column: cnes_1_ch_difer_hpp
	 * @param cnes1CargaHorariaDiferenciadaSistemaHpp the cnes_1_ch_difer_hpp value
	 */
	public void setCnes1CargaHorariaDiferenciadaSistemaHpp (java.lang.String cnes1CargaHorariaDiferenciadaSistemaHpp) {
//        java.lang.String cnes1CargaHorariaDiferenciadaSistemaHppOld = this.cnes1CargaHorariaDiferenciadaSistemaHpp;
		this.cnes1CargaHorariaDiferenciadaSistemaHpp = cnes1CargaHorariaDiferenciadaSistemaHpp;
//        this.getPropertyChangeSupport().firePropertyChange ("cnes1CargaHorariaDiferenciadaSistemaHpp", cnes1CargaHorariaDiferenciadaSistemaHppOld, cnes1CargaHorariaDiferenciadaSistemaHpp);
	}



	/**
	 * Return the value associated with the column: ch_outros_ch_difer_resmed
	 */
	public java.lang.String getCargaHorariaOutrosCargaHorariaDiferenciadaResidenciaMedica () {
		return getPropertyValue(this, cargaHorariaOutrosCargaHorariaDiferenciadaResidenciaMedica, PROP_CARGA_HORARIA_OUTROS_CARGA_HORARIA_DIFERENCIADA_RESIDENCIA_MEDICA); 
	}

	/**
	 * Set the value related to the column: ch_outros_ch_difer_resmed
	 * @param cargaHorariaOutrosCargaHorariaDiferenciadaResidenciaMedica the ch_outros_ch_difer_resmed value
	 */
	public void setCargaHorariaOutrosCargaHorariaDiferenciadaResidenciaMedica (java.lang.String cargaHorariaOutrosCargaHorariaDiferenciadaResidenciaMedica) {
//        java.lang.String cargaHorariaOutrosCargaHorariaDiferenciadaResidenciaMedicaOld = this.cargaHorariaOutrosCargaHorariaDiferenciadaResidenciaMedica;
		this.cargaHorariaOutrosCargaHorariaDiferenciadaResidenciaMedica = cargaHorariaOutrosCargaHorariaDiferenciadaResidenciaMedica;
//        this.getPropertyChangeSupport().firePropertyChange ("cargaHorariaOutrosCargaHorariaDiferenciadaResidenciaMedica", cargaHorariaOutrosCargaHorariaDiferenciadaResidenciaMedicaOld, cargaHorariaOutrosCargaHorariaDiferenciadaResidenciaMedica);
	}



	/**
	 * Return the value associated with the column: usuario
	 */
	public java.lang.String getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: usuario
	 * @param usuario the usuario value
	 */
	public void setUsuario (java.lang.String usuario) {
//        java.lang.String usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_cnes_processo_equipe
	 */
	public br.com.ksisolucoes.vo.geral.cnes.CnesProcessoEquipe getCnesProcessoEquipe () {
		return getPropertyValue(this, cnesProcessoEquipe, PROP_CNES_PROCESSO_EQUIPE); 
	}

	/**
	 * Set the value related to the column: cd_cnes_processo_equipe
	 * @param cnesProcessoEquipe the cd_cnes_processo_equipe value
	 */
	public void setCnesProcessoEquipe (br.com.ksisolucoes.vo.geral.cnes.CnesProcessoEquipe cnesProcessoEquipe) {
//        br.com.ksisolucoes.vo.geral.cnes.CnesProcessoEquipe cnesProcessoEquipeOld = this.cnesProcessoEquipe;
		this.cnesProcessoEquipe = cnesProcessoEquipe;
//        this.getPropertyChangeSupport().firePropertyChange ("cnesProcessoEquipe", cnesProcessoEquipeOld, cnesProcessoEquipe);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.geral.cnes.CnesProcessoEquipeProfissional)) return false;
		else {
			br.com.ksisolucoes.vo.geral.cnes.CnesProcessoEquipeProfissional cnesProcessoEquipeProfissional = (br.com.ksisolucoes.vo.geral.cnes.CnesProcessoEquipeProfissional) obj;
			if (null == this.getCodigo() || null == cnesProcessoEquipeProfissional.getCodigo()) return false;
			else return (this.getCodigo().equals(cnesProcessoEquipeProfissional.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}