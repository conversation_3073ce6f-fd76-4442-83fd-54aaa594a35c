<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vacina">
    <class name="AplicacaoVacinaCampanha" table="aplicacao_vacina_campanha">

        <id
                column="cd_aplicacao_vacina_campanha"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="sequence">
                <param name="sequence">seq_aplicacao_vacina_campanha</param>
            </generator>
        </id>
        <version column="version" name="version" type="long"/>

        <many-to-one
                class="br.com.ksisolucoes.vo.vacina.TipoVacina"
                column="cd_vacina"
                name="tipoVacina"
                not-null="true"
        />

        <property
                column="status"
                name="status"
                not-null="true"
                type="java.lang.Long"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Empresa"
                name="empresa"
                column="empresa"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.cadsus.Profissional"
                name="profissionalAplicacao"
                not-null="false"
                column="cd_profissional"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vacina.ProdutoVacina"
                column="cd_produto_vacina"
                name="produtoVacina"
        />

        <property
                column="dt_aplicacao"
                name="dataAplicacao"
                not-null="false"
                type="timestamp"
        />
        <property
                column="dt_aplicacao_fim"
                name="dataAplicacaoFim"
                not-null="false"
                type="timestamp"
        />

        <property
                column="local_atendimento"
                name="localAtendimento"
                type="java.lang.Long"
        />

        <property
                column="turno"
                name="turno"
                type="java.lang.Long"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vacina.Calendario"
                column="cd_estrategia_calendario"
                name="estrategia"
                not-null="false"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vacina.VacinaCalendario"
                column="cd_vacina_calendario"
                name="vacinaCalendario"
        />

        <property
                column="lote"
                length="20"
                name="lote"
                not-null="false"
                type="java.lang.String"
        />

        <property
                column="dt_cadastro"
                name="dataCadastro"
                not-null="true"
                type="java.util.Date"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario"
                not-null="true"
                name="usuario"
        />

        <property
                column="dt_conclusao"
                name="dataConclusao"
                not-null="true"
                type="java.util.Date"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario_conclusao"
                not-null="true"
                name="usuarioConclusao"
        />
        <property
                column="dt_cancelamento"
                name="dataCancelamento"
                not-null="true"
                type="java.util.Date"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario_cancelamento"
                not-null="true"
                name="usuarioCancelamento"
        />
    </class>
</hibernate-mapping>
