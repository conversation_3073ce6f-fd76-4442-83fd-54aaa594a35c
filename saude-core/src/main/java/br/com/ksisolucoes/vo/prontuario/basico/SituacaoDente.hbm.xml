<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="SituacaoDente" table="situacao_dente" >
        <id
            name="codigo"
            column="cd_situacao"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id>
        
        <version column="version" name="version" type="long" />
		
        <property 
            name="descricao"
            column="ds_situacao"
            type="java.lang.String"
            length="30"
            not-null="true"
		/>      
		
        <property 
            name="referencia"
            column="referencia"
            type="java.lang.String"
            length="10"
            not-null="true"
		/>      
		
        <property 
            name="ordem"
            column="ordem"
            type="java.lang.Long"
            not-null="false"
		/>      
		
        <property 
            name="tipoSituacao"
            column="tp_situacao"
            type="java.lang.Long"
            not-null="true"
		/>

        <property
            name="cariado"
            column="cariado"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="perdido"
            column="perdido"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="obturadoRestaurado"
            column="obturado_restaurado"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            name="cor"
            column="cor"
            type="java.lang.String"
            length="6"
		/>
		
		<property 
            name="tipoAplicacao"
            column="tp_aplicacao"
            type="java.lang.Long"
		/>
		
		<property 
            name="tipoPreenchimento"
            column="tp_preenchimento"
            type="java.lang.Long"
		/>
		
		<property 
            name="flagInativarDente"
            column="flag_inativar_dente"
            type="java.lang.Long"
		/>
    </class>
</hibernate-mapping>
