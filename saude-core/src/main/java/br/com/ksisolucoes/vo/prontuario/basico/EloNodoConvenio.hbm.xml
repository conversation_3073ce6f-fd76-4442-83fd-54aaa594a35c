<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="EloNodoConvenio" table="elo_nodo_convenio" >
    
        <id
            column="cd_elo"
            name="codigo"
            type="java.lang.Long"
            >
            <generator class="assigned" />
        </id> <version column="version" name="version" type="long" />   
                 
         <many-to-one  
            class="br.com.ksisolucoes.vo.prontuario.basico.NodoAtendimentoWeb"
            name="nodoAtendimento"
            column="cd_nodo_atendimento"
        />

         <many-to-one  
            class="br.com.ksisolucoes.vo.prontuario.basico.Convenio"
            name="convenio"
            column="cd_convenio"
        />
    </class>
</hibernate-mapping>
