package br.com.ksisolucoes.vo.prontuario.basico.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the atendimento_painel table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="atendimento_painel"
 */

public abstract class BaseAtendimentoPainel extends BaseRootVO implements Serializable {

	public static String REF = "AtendimentoPainel";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TIPO_ATENDIMENTO = "tipoAtendimento";
	public static final String PROP_PROFISSIONAL = "profissional";
	public static final String PROP_DATA_CHAMADA = "dataChamada";
	public static final String PROP_ATENDIMENTO = "atendimento";


	// constructors
	public BaseAtendimentoPainel () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAtendimentoPainel (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseAtendimentoPainel (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento,
		br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento tipoAtendimento) {

		this.setCodigo(codigo);
		this.setAtendimento(atendimento);
		this.setTipoAtendimento(tipoAtendimento);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataChamada;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento;
	private br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento tipoAtendimento;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissional;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_atendimento_painel"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_chamada
	 */
	public java.util.Date getDataChamada () {
		return getPropertyValue(this, dataChamada, PROP_DATA_CHAMADA); 
	}

	/**
	 * Set the value related to the column: dt_chamada
	 * @param dataChamada the dt_chamada value
	 */
	public void setDataChamada (java.util.Date dataChamada) {
//        java.util.Date dataChamadaOld = this.dataChamada;
		this.dataChamada = dataChamada;
//        this.getPropertyChangeSupport().firePropertyChange ("dataChamada", dataChamadaOld, dataChamada);
	}



	/**
	 * Return the value associated with the column: nr_atendimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Atendimento getAtendimento () {
		return getPropertyValue(this, atendimento, PROP_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: nr_atendimento
	 * @param atendimento the nr_atendimento value
	 */
	public void setAtendimento (br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoOld = this.atendimento;
		this.atendimento = atendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimento", atendimentoOld, atendimento);
	}



	/**
	 * Return the value associated with the column: cd_tp_atendimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento getTipoAtendimento () {
		return getPropertyValue(this, tipoAtendimento, PROP_TIPO_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_tp_atendimento
	 * @param tipoAtendimento the cd_tp_atendimento value
	 */
	public void setTipoAtendimento (br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento tipoAtendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento tipoAtendimentoOld = this.tipoAtendimento;
		this.tipoAtendimento = tipoAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAtendimento", tipoAtendimentoOld, tipoAtendimento);
	}



	/**
	 * Return the value associated with the column: cd_profissional
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissional () {
		return getPropertyValue(this, profissional, PROP_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_profissional
	 * @param profissional the cd_profissional value
	 */
	public void setProfissional (br.com.ksisolucoes.vo.cadsus.Profissional profissional) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalOld = this.profissional;
		this.profissional = profissional;
//        this.getPropertyChangeSupport().firePropertyChange ("profissional", profissionalOld, profissional);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.AtendimentoPainel)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.AtendimentoPainel atendimentoPainel = (br.com.ksisolucoes.vo.prontuario.basico.AtendimentoPainel) obj;
			if (null == this.getCodigo() || null == atendimentoPainel.getCodigo()) return false;
			else return (this.getCodigo().equals(atendimentoPainel.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}