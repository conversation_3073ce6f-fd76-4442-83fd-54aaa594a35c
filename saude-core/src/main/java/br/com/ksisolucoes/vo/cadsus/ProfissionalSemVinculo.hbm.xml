<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.cadsus"  >
    <class name="ProfissionalSemVinculo" table="profissional_sem_vinculo">
        <id name="codigo" type="java.lang.Long" column="cd_profissional_sem_vinculo">
            <generator class="sequence"><param name="sequence">seq_profissional_sem_vinculo</param></generator>
        </id>

        <version column="version" name="version" type="long" />

        <property name="nomeProfissional" column="nm_profissional" type="java.lang.String" not-null="true" length="100"/>
        <property name="ufConselho" column="uf_conselho_classe" type="java.lang.String" not-null="true" length="2"/>
        <property name="numeroConselhoClasse" column="nr_conselho" type="java.lang.String" not-null="true" length="60"/>
        <property name="status" column="status" type="java.lang.Long" not-null="true" />
        <property name="datCadastro" column="dt_cadastro" type="timestamp" not-null="true"/>
        <many-to-one name="conselhoClasse" class="br.com.ksisolucoes.vo.basico.OrgaoEmissor" not-null="true" column="cd_conselho_classe"/>
        <many-to-one name="usuario" class="br.com.ksisolucoes.vo.controle.Usuario" column="cd_usuario"/>

    </class>
</hibernate-mapping>