package br.com.ksisolucoes.vo.entradas.recebimento;

import java.io.Serializable;

import br.com.ksisolucoes.vo.entradas.recebimento.base.BaseEloPedidoLoteRecebtoLote;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class EloPedidoLoteRecebtoLote extends BaseEloPedidoLoteRecebtoLote implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EloPedidoLoteRecebtoLote () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public EloPedidoLoteRecebtoLote (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public EloPedidoLoteRecebtoLote (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferenciaItemLote pedidoTransferenciaItemLote,
		br.com.ksisolucoes.vo.entradas.recebimento.RecebimentoGrupoEstoque recebimentoGrupoEstoque) {

		super (
			codigo,
			pedidoTransferenciaItemLote,
			recebimentoGrupoEstoque);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}