<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.requerimentos">
    <class name="RequerimentoAtividadeEconomicaAtividade" table="requerimento_atividade_economica_atividade">
        <id
                column="cd_requerimento_atividade_economica_atividade"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="assigned"/>
        </id>
        <version column="version" name="version" type="long"/>

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAtividadeEconomica"
                column="cd_requerimento_atividade_economica"
                name="requerimentoAtividadeEconomica"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento"
                column="cd_atividade_estabelecimento"
                name="atividadeEstabelecimento"
                not-null="true"
        />

        <property
                column="flag_principal"
                name="flagPrincipal"
                not-null="true"
                type="java.lang.Long"
        />

        <property
                column="isento_taxa"
                name="isentoTaxa"
                not-null="false"
                type="java.lang.Long"
        />

        <property
                column="qtd_taxa"
                name="quantidadeTaxa"
                not-null="false"
                type="java.lang.Double"
        />

        <property
                column="qtd_cobrada"
                name="quantidadeCobrada"
                not-null="false"
                type="java.lang.Long"
        />

        <property
                column="vl_indice"
                name="valorIndice"
                type="java.lang.Double"
                not-null="true"
        />

    </class>
</hibernate-mapping>
