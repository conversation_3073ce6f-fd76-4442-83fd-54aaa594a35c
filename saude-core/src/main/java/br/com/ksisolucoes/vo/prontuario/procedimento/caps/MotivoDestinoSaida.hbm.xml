<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.procedimento.caps"  >
    <class name="MotivoDestinoSaida" table="motivo_destino_saida">
        
        <id
            name="codigo"
            type="java.lang.Long" 
            column="cd_motivo_destino_saida"
        > 
            <generator class="assigned"/>
        </id> 
        
        <version column="version" name="version" type="long" />
        
        <property
            column="descricao"
            name="descricao"
            not-null="true"
            type="java.lang.String"
        />
        
        <property
            column="cd_raas"
            name="codigoRaas"
            type="java.lang.String"
        />
        
        <property
            column="concluir_ficha"
            name="concluirFicha"
            type="java.lang.Long"
        />
    </class>
</hibernate-mapping>
