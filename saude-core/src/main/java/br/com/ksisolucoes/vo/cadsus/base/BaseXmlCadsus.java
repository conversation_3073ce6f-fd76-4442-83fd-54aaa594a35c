package br.com.ksisolucoes.vo.cadsus.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the export_xml_cadsus table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="export_xml_cadsus"
 */

public abstract class BaseXmlCadsus extends BaseRootVO implements Serializable {

	public static String REF = "XmlCadsus";
	public static final String PROP_REGISTROS = "registros";
	public static final String PROP_USUARIOS = "usuarios";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_ENDERECOS = "enderecos";
	public static final String PROP_XML = "xml";
	public static final String PROP_DOMICILIOS = "domicilios";
	public static final String PROP_DATA_CRICAO = "dataCricao";
	public static final String PROP_NOME_ARQUIVO = "nomeArquivo";


	// constructors
	public BaseXmlCadsus () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseXmlCadsus (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseXmlCadsus (
		java.lang.Long codigo,
		java.util.Date dataCricao,
		java.lang.String xml) {

		this.setCodigo(codigo);
		this.setDataCricao(dataCricao);
		this.setXml(xml);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataCricao;
	private java.lang.String xml;
	private java.lang.Long registros;
	private java.lang.Long enderecos;
	private java.lang.Long domicilios;
	private java.lang.Long usuarios;
	private java.lang.String nomeArquivo;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="codigo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_criacao
	 */
	public java.util.Date getDataCricao () {
		return getPropertyValue(this, dataCricao, PROP_DATA_CRICAO); 
	}

	/**
	 * Set the value related to the column: dt_criacao
	 * @param dataCricao the dt_criacao value
	 */
	public void setDataCricao (java.util.Date dataCricao) {
//        java.util.Date dataCricaoOld = this.dataCricao;
		this.dataCricao = dataCricao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCricao", dataCricaoOld, dataCricao);
	}



	/**
	 * Return the value associated with the column: xml
	 */
	public java.lang.String getXml () {
		return getPropertyValue(this, xml, PROP_XML); 
	}

	/**
	 * Set the value related to the column: xml
	 * @param xml the xml value
	 */
	public void setXml (java.lang.String xml) {
//        java.lang.String xmlOld = this.xml;
		this.xml = xml;
//        this.getPropertyChangeSupport().firePropertyChange ("xml", xmlOld, xml);
	}



	/**
	 * Return the value associated with the column: registros
	 */
	public java.lang.Long getRegistros () {
		return getPropertyValue(this, registros, PROP_REGISTROS); 
	}

	/**
	 * Set the value related to the column: registros
	 * @param registros the registros value
	 */
	public void setRegistros (java.lang.Long registros) {
//        java.lang.Long registrosOld = this.registros;
		this.registros = registros;
//        this.getPropertyChangeSupport().firePropertyChange ("registros", registrosOld, registros);
	}



	/**
	 * Return the value associated with the column: enderecos
	 */
	public java.lang.Long getEnderecos () {
		return getPropertyValue(this, enderecos, PROP_ENDERECOS); 
	}

	/**
	 * Set the value related to the column: enderecos
	 * @param enderecos the enderecos value
	 */
	public void setEnderecos (java.lang.Long enderecos) {
//        java.lang.Long enderecosOld = this.enderecos;
		this.enderecos = enderecos;
//        this.getPropertyChangeSupport().firePropertyChange ("enderecos", enderecosOld, enderecos);
	}



	/**
	 * Return the value associated with the column: domicilios
	 */
	public java.lang.Long getDomicilios () {
		return getPropertyValue(this, domicilios, PROP_DOMICILIOS); 
	}

	/**
	 * Set the value related to the column: domicilios
	 * @param domicilios the domicilios value
	 */
	public void setDomicilios (java.lang.Long domicilios) {
//        java.lang.Long domiciliosOld = this.domicilios;
		this.domicilios = domicilios;
//        this.getPropertyChangeSupport().firePropertyChange ("domicilios", domiciliosOld, domicilios);
	}



	/**
	 * Return the value associated with the column: usuarios
	 */
	public java.lang.Long getUsuarios () {
		return getPropertyValue(this, usuarios, PROP_USUARIOS); 
	}

	/**
	 * Set the value related to the column: usuarios
	 * @param usuarios the usuarios value
	 */
	public void setUsuarios (java.lang.Long usuarios) {
//        java.lang.Long usuariosOld = this.usuarios;
		this.usuarios = usuarios;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarios", usuariosOld, usuarios);
	}



	/**
	 * Return the value associated with the column: nm_arquivo
	 */
	public java.lang.String getNomeArquivo () {
		return getPropertyValue(this, nomeArquivo, PROP_NOME_ARQUIVO); 
	}

	/**
	 * Set the value related to the column: nm_arquivo
	 * @param nomeArquivo the nm_arquivo value
	 */
	public void setNomeArquivo (java.lang.String nomeArquivo) {
//        java.lang.String nomeArquivoOld = this.nomeArquivo;
		this.nomeArquivo = nomeArquivo;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeArquivo", nomeArquivoOld, nomeArquivo);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.cadsus.XmlCadsus)) return false;
		else {
			br.com.ksisolucoes.vo.cadsus.XmlCadsus xmlCadsus = (br.com.ksisolucoes.vo.cadsus.XmlCadsus) obj;
			if (null == this.getCodigo() || null == xmlCadsus.getCodigo()) return false;
			else return (this.getCodigo().equals(xmlCadsus.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}