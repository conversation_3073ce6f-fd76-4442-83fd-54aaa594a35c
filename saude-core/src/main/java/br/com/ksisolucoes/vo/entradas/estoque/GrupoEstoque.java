package br.com.ksisolucoes.vo.entradas.estoque;

import java.io.Serializable;

import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.Valor;
import br.com.ksisolucoes.vo.entradas.estoque.base.BaseGrupoEstoque;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class GrupoEstoque extends BaseGrupoEstoque implements CodigoManager {
	private static final long serialVersionUID = 1L;
        public final static String GRUPO_ESTOQUE_PADRAO = "0";
/*[CONSTRUCTOR MARKER BEGIN]*/
	public GrupoEstoque () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public GrupoEstoque (br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoquePK id) {
		super(id);
	}

	/**
	 * Constructor for required fields
	 */
	public GrupoEstoque (
		br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoquePK id,
		java.lang.Double estoqueEncomendado,
		java.lang.Double estoqueFisico) {

		super (
			id,
			estoqueEncomendado,
			estoqueFisico);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setId( (br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoquePK)key );
    }

    public Serializable getCodigoManager() {
        return this.getId();
    }

    public Double getEstoqueDisponivel() {
        return getCalculoEstoqueDisponivel(getEstoqueFisico(), getEstoqueEncomendado(), getEstoqueReservado());
    }

    public static Double getCalculoEstoqueDisponivel(Double estoqueFisico, Double estoqueEncomendado, Double estoqueReservado) {
        if( estoqueFisico == null ) {
            estoqueFisico = 0D;
        }
        if( estoqueEncomendado == null ) {
            estoqueEncomendado = 0D;
        }
        if( estoqueReservado == null ) {
            estoqueReservado = 0D;
        }

        return Valor.round( estoqueFisico + estoqueEncomendado - estoqueReservado, 4 );
    }

    @Override
    public Double getEstoqueEncomendado() {
        return Coalesce.asDouble(super.getEstoqueEncomendado());
    }

    @Override
    public Double getEstoqueDevolucao() {
        return Coalesce.asDouble(super.getEstoqueDevolucao());
    }

    @Override
    public Double getEstoqueFisico() {
        return Coalesce.asDouble(super.getEstoqueFisico());
    }

    @Override
    public Double getEstoqueNaoConforme() {
        return Coalesce.asDouble(super.getEstoqueNaoConforme());
    }

    @Override
    public Double getEstoqueReservado() {
        return Coalesce.asDouble(super.getEstoqueReservado());
    }

    @Override
    public Double getEstoqueReservadoDevolucao() {
        return Coalesce.asDouble(super.getEstoqueReservadoDevolucao());
    }
}