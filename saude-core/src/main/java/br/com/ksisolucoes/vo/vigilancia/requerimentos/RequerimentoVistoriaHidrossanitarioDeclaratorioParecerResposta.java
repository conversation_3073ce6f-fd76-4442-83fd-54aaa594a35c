package br.com.ksisolucoes.vo.vigilancia.requerimentos;

import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.base.BaseRequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta;

import java.io.Serializable;



public class RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta extends BaseRequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaHidrossanitarioDeclaratorioParecer requerimentoVistoriaHidrossanitarioDeclaratorioParecer,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataResposta,
		java.util.Date dataUsuario) {

		super (
			codigo,
			requerimentoVistoriaHidrossanitarioDeclaratorioParecer,
			usuario,
			dataResposta,
			dataUsuario);
	}

/*[CONSTRUCTOR MARKER END]*/

	public enum Situacao implements IEnum {
		CADASTRADO(0L, Bundle.getStringApplication("rotulo_cadastrado")),
		ENVIADO(1L, Bundle.getStringApplication("rotulo_enviado")),
		;

		private Long value;
		private String descricao;

		private Situacao(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static Situacao valeuOf(Long value) {
			for (Situacao situacao : Situacao.values()) {
				if (situacao.value().equals(value)) {
					return situacao;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}

	}

	@Override
	public String getDescricaoResposta() {
		return StringUtil.limparHtml(super.getDescricaoResposta());
	}

	@Override
	public void setDescricaoResposta(String descricaoResposta) {
		super.setDescricaoResposta(StringUtil.limparHtml(descricaoResposta));
	}

	public String getDescricaoRespostaSemHtml() {
		return StringUtil.removeHtmlString(getDescricaoResposta());
	}

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}