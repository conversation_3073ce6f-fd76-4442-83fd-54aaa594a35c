package br.com.ksisolucoes.vo.controle.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the programas table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="programas"
 */

public abstract class BasePrograma extends BaseRootVO implements Serializable {

	public static String REF = "Programa";
	public static final String PROP_MENU = "menu";
	public static final String PROP_IMAGEM = "imagem";
	public static final String PROP_ROTULO = "rotulo";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_SEQUENCIA = "sequencia";
	public static final String PROP_CLASSE_PROGAMA = "classeProgama";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_ATIVO = "ativo";
	public static final String PROP_NOME = "nome";
	public static final String PROP_VISIVEL = "visivel";


	// constructors
	public BasePrograma () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BasePrograma (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BasePrograma (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Menu menu,
		java.lang.String nome,
		java.lang.String descricao,
		java.lang.String classeProgama,
		java.lang.String ativo) {

		this.setCodigo(codigo);
		this.setMenu(menu);
		this.setNome(nome);
		this.setDescricao(descricao);
		this.setClasseProgama(classeProgama);
		this.setAtivo(ativo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long visivel;
	private java.lang.String nome;
	private java.lang.String rotulo;
	private java.lang.Long sequencia;
	private java.lang.String descricao;
	private java.lang.String imagem;
	private java.lang.String classeProgama;
	private java.lang.String ativo;

	// many to one
	private br.com.ksisolucoes.vo.controle.Menu menu;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_programa"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: visivel
	 */
	public java.lang.Long getVisivel () {
		return getPropertyValue(this, visivel, PROP_VISIVEL); 
	}

	/**
	 * Set the value related to the column: visivel
	 * @param visivel the visivel value
	 */
	public void setVisivel (java.lang.Long visivel) {
//        java.lang.Long visivelOld = this.visivel;
		this.visivel = visivel;
//        this.getPropertyChangeSupport().firePropertyChange ("visivel", visivelOld, visivel);
	}



	/**
	 * Return the value associated with the column: nm_programa
	 */
	public java.lang.String getNome () {
		return getPropertyValue(this, nome, PROP_NOME); 
	}

	/**
	 * Set the value related to the column: nm_programa
	 * @param nome the nm_programa value
	 */
	public void setNome (java.lang.String nome) {
//        java.lang.String nomeOld = this.nome;
		this.nome = nome;
//        this.getPropertyChangeSupport().firePropertyChange ("nome", nomeOld, nome);
	}



	/**
	 * Return the value associated with the column: rotulo
	 */
	public java.lang.String getRotulo () {
		return getPropertyValue(this, rotulo, PROP_ROTULO); 
	}

	/**
	 * Set the value related to the column: rotulo
	 * @param rotulo the rotulo value
	 */
	public void setRotulo (java.lang.String rotulo) {
//        java.lang.String rotuloOld = this.rotulo;
		this.rotulo = rotulo;
//        this.getPropertyChangeSupport().firePropertyChange ("rotulo", rotuloOld, rotulo);
	}



	/**
	 * Return the value associated with the column: sequencia
	 */
	public java.lang.Long getSequencia () {
		return getPropertyValue(this, sequencia, PROP_SEQUENCIA); 
	}

	/**
	 * Set the value related to the column: sequencia
	 * @param sequencia the sequencia value
	 */
	public void setSequencia (java.lang.Long sequencia) {
//        java.lang.Long sequenciaOld = this.sequencia;
		this.sequencia = sequencia;
//        this.getPropertyChangeSupport().firePropertyChange ("sequencia", sequenciaOld, sequencia);
	}



	/**
	 * Return the value associated with the column: ds_programa
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_programa
	 * @param descricao the ds_programa value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: ds_imagem
	 */
	public java.lang.String getImagem () {
		return getPropertyValue(this, imagem, PROP_IMAGEM); 
	}

	/**
	 * Set the value related to the column: ds_imagem
	 * @param imagem the ds_imagem value
	 */
	public void setImagem (java.lang.String imagem) {
//        java.lang.String imagemOld = this.imagem;
		this.imagem = imagem;
//        this.getPropertyChangeSupport().firePropertyChange ("imagem", imagemOld, imagem);
	}



	/**
	 * Return the value associated with the column: ds_classe
	 */
	public java.lang.String getClasseProgama () {
		return getPropertyValue(this, classeProgama, PROP_CLASSE_PROGAMA); 
	}

	/**
	 * Set the value related to the column: ds_classe
	 * @param classeProgama the ds_classe value
	 */
	public void setClasseProgama (java.lang.String classeProgama) {
//        java.lang.String classeProgamaOld = this.classeProgama;
		this.classeProgama = classeProgama;
//        this.getPropertyChangeSupport().firePropertyChange ("classeProgama", classeProgamaOld, classeProgama);
	}



	/**
	 * Return the value associated with the column: ativo
	 */
	public java.lang.String getAtivo () {
		return getPropertyValue(this, ativo, PROP_ATIVO); 
	}

	/**
	 * Set the value related to the column: ativo
	 * @param ativo the ativo value
	 */
	public void setAtivo (java.lang.String ativo) {
//        java.lang.String ativoOld = this.ativo;
		this.ativo = ativo;
//        this.getPropertyChangeSupport().firePropertyChange ("ativo", ativoOld, ativo);
	}



	/**
	 * Return the value associated with the column: cd_menu
	 */
	public br.com.ksisolucoes.vo.controle.Menu getMenu () {
		return getPropertyValue(this, menu, PROP_MENU); 
	}

	/**
	 * Set the value related to the column: cd_menu
	 * @param menu the cd_menu value
	 */
	public void setMenu (br.com.ksisolucoes.vo.controle.Menu menu) {
//        br.com.ksisolucoes.vo.controle.Menu menuOld = this.menu;
		this.menu = menu;
//        this.getPropertyChangeSupport().firePropertyChange ("menu", menuOld, menu);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.controle.Programa)) return false;
		else {
			br.com.ksisolucoes.vo.controle.Programa programa = (br.com.ksisolucoes.vo.controle.Programa) obj;
			if (null == this.getCodigo() || null == programa.getCodigo()) return false;
			else return (this.getCodigo().equals(programa.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}