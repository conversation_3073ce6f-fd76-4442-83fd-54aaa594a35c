package br.com.ksisolucoes.vo.entradas.estoque.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the estoque_empresa table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="estoque_empresa"
 */

public abstract class BaseEstoqueEmpresa extends BaseRootVO implements Serializable {

	public static String REF = "EstoqueEmpresa";
	public static final String PROP_ESTOQUE_FISICO = "estoqueFisico";
	public static final String PROP_QUANTIDADE_PADRAO_DISPENSACAO = "quantidadePadraoDispensacao";
	public static final String PROP_RO_PRODUTO = "roProduto";
	public static final String PROP_PRECO_VENDA = "precoVenda";
	public static final String PROP_DATA_INTEGRACAO_INOVAMFRI = "dataIntegracaoInovamfri";
	public static final String PROP_PRECO_CUSTO = "precoCusto";
	public static final String PROP_ESTOQUE_NAO_CONFORME = "estoqueNaoConforme";
	public static final String PROP_ESTOQUE_RESERVADO = "estoqueReservado";
	public static final String PROP_ESTOQUE_REPOSICAO = "estoqueReposicao";
	public static final String PROP_ESTOQUE_ENCOMENDADO = "estoqueEncomendado";
	public static final String PROP_CONSUMO_MEDIO = "consumoMedio";
	public static final String PROP_RO_EMPRESA = "roEmpresa";
	public static final String PROP_ESTOQUE_MINIMO = "estoqueMinimo";
	public static final String PROP_FLAG_ATIVO = "flagAtivo";
	public static final String PROP_QUANTIDADE_IDEAL = "quantidadeIdeal";
	public static final String PROP_ESTOQUE_MAXIMO = "estoqueMaximo";
	public static final String PROP_PRECO_MEDIO = "precoMedio";
	public static final String PROP_PRAZO_MEDIO_O_C_RECEBIMENTO = "prazoMedioOCRecebimento";
	public static final String PROP_DATA_ULTIMA_RECEBIMENTO = "dataUltimaRecebimento";
	public static final String PROP_ULTIMO_FORNECEDOR = "ultimoFornecedor";
	public static final String PROP_ESTOQUE_ANALISE = "estoqueAnalise";
	public static final String PROP_DATA_ULTIMA_ENTRADA = "dataUltimaEntrada";
	public static final String PROP_QUANTIDADE_MULTIPLA = "quantidadeMultipla";
	public static final String PROP_LOCALIZACAO = "localizacao";
	public static final String PROP_ESTOQUE_RESERVADO_DEVOLUCAO = "estoqueReservadoDevolucao";
	public static final String PROP_ULTIMO_PRECO = "ultimoPreco";
	public static final String PROP_DATA_ULTIMA_NOTA_FISCAL = "dataUltimaNotaFiscal";
	public static final String PROP_DATA_ULTIMA_SAIDA = "dataUltimaSaida";
	public static final String PROP_ID = "id";
	public static final String PROP_ESTOQUE_PRODUCAO = "estoqueProducao";
	public static final String PROP_TEMPO_REPOSICAO = "tempoReposicao";
	public static final String PROP_PERIODO_CONSUMO_MEDIO = "periodoConsumoMedio";


	// constructors
	public BaseEstoqueEmpresa () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEstoqueEmpresa (br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresaPK id) {
		this.setId(id);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseEstoqueEmpresa (
		br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresaPK id,
		br.com.ksisolucoes.vo.basico.Pessoa ultimoFornecedor) {

		this.setId(id);
		this.setUltimoFornecedor(ultimoFornecedor);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresaPK id;

	// fields
	private java.lang.Double estoqueAnalise;
	private java.util.Date dataUltimaEntrada;
	private java.util.Date dataUltimaNotaFiscal;
	private java.util.Date dataUltimaRecebimento;
	private java.util.Date dataUltimaSaida;
	private java.lang.Double estoqueMaximo;
	private java.lang.Double estoqueEncomendado;
	private java.lang.Double estoqueReservado;
	private java.lang.Double estoqueReservadoDevolucao;
	private java.lang.Double estoqueProducao;
	private java.lang.Double estoqueMinimo;
	private java.lang.Double consumoMedio;
	private java.lang.Double estoqueFisico;
	private java.lang.Double precoVenda;
	private java.lang.Double estoqueNaoConforme;
	private java.lang.String estoqueReposicao;
	private java.lang.Double quantidadeIdeal;
	private java.lang.Double quantidadeMultipla;
	private java.lang.Double prazoMedioOCRecebimento;
	private java.lang.String flagAtivo;
	private java.lang.Double quantidadePadraoDispensacao;
	private java.lang.Double precoCusto;
	private java.lang.Double precoMedio;
	private java.lang.Double ultimoPreco;
	private java.lang.Long tempoReposicao;
	private java.lang.Long periodoConsumoMedio;
	private java.util.Date dataIntegracaoInovamfri;

	// many to one
	private br.com.ksisolucoes.vo.entradas.estoque.Produto roProduto;
	private br.com.ksisolucoes.vo.basico.Empresa roEmpresa;
	private br.com.ksisolucoes.vo.basico.Pessoa ultimoFornecedor;
	private br.com.ksisolucoes.vo.entradas.estoque.Localizacao localizacao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     */
	public br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresaPK getId () {
	    return getPropertyValue(this,  id, "id" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param id the new ID
	 */
	public void setId (br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresaPK id) {
		this.id = id;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: estoque_analise
	 */
	public java.lang.Double getEstoqueAnalise () {
		return getPropertyValue(this, estoqueAnalise, PROP_ESTOQUE_ANALISE); 
	}

	/**
	 * Set the value related to the column: estoque_analise
	 * @param estoqueAnalise the estoque_analise value
	 */
	public void setEstoqueAnalise (java.lang.Double estoqueAnalise) {
//        java.lang.Double estoqueAnaliseOld = this.estoqueAnalise;
		this.estoqueAnalise = estoqueAnalise;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueAnalise", estoqueAnaliseOld, estoqueAnalise);
	}



	/**
	 * Return the value associated with the column: dt_ult_ent
	 */
	public java.util.Date getDataUltimaEntrada () {
		return getPropertyValue(this, dataUltimaEntrada, PROP_DATA_ULTIMA_ENTRADA); 
	}

	/**
	 * Set the value related to the column: dt_ult_ent
	 * @param dataUltimaEntrada the dt_ult_ent value
	 */
	public void setDataUltimaEntrada (java.util.Date dataUltimaEntrada) {
//        java.util.Date dataUltimaEntradaOld = this.dataUltimaEntrada;
		this.dataUltimaEntrada = dataUltimaEntrada;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUltimaEntrada", dataUltimaEntradaOld, dataUltimaEntrada);
	}



	/**
	 * Return the value associated with the column: dt_ult_nf
	 */
	public java.util.Date getDataUltimaNotaFiscal () {
		return getPropertyValue(this, dataUltimaNotaFiscal, PROP_DATA_ULTIMA_NOTA_FISCAL); 
	}

	/**
	 * Set the value related to the column: dt_ult_nf
	 * @param dataUltimaNotaFiscal the dt_ult_nf value
	 */
	public void setDataUltimaNotaFiscal (java.util.Date dataUltimaNotaFiscal) {
//        java.util.Date dataUltimaNotaFiscalOld = this.dataUltimaNotaFiscal;
		this.dataUltimaNotaFiscal = dataUltimaNotaFiscal;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUltimaNotaFiscal", dataUltimaNotaFiscalOld, dataUltimaNotaFiscal);
	}



	/**
	 * Return the value associated with the column: dt_ult_recebto
	 */
	public java.util.Date getDataUltimaRecebimento () {
		return getPropertyValue(this, dataUltimaRecebimento, PROP_DATA_ULTIMA_RECEBIMENTO); 
	}

	/**
	 * Set the value related to the column: dt_ult_recebto
	 * @param dataUltimaRecebimento the dt_ult_recebto value
	 */
	public void setDataUltimaRecebimento (java.util.Date dataUltimaRecebimento) {
//        java.util.Date dataUltimaRecebimentoOld = this.dataUltimaRecebimento;
		this.dataUltimaRecebimento = dataUltimaRecebimento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUltimaRecebimento", dataUltimaRecebimentoOld, dataUltimaRecebimento);
	}



	/**
	 * Return the value associated with the column: dt_ult_sai
	 */
	public java.util.Date getDataUltimaSaida () {
		return getPropertyValue(this, dataUltimaSaida, PROP_DATA_ULTIMA_SAIDA); 
	}

	/**
	 * Set the value related to the column: dt_ult_sai
	 * @param dataUltimaSaida the dt_ult_sai value
	 */
	public void setDataUltimaSaida (java.util.Date dataUltimaSaida) {
//        java.util.Date dataUltimaSaidaOld = this.dataUltimaSaida;
		this.dataUltimaSaida = dataUltimaSaida;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUltimaSaida", dataUltimaSaidaOld, dataUltimaSaida);
	}

	/**
	 * Return the value associated with the column: tempo_reposicao
	 */
	public java.lang.Long getTempoReposicao() {
		return getPropertyValue(this, tempoReposicao, PROP_TEMPO_REPOSICAO);
	}

	/**
	 * Set the value related to the column: tempo_reposicao
	 * @param tempoReposicao the tempo_reposicao value
	 */
	public void setTempoReposicao (java.lang.Long tempoReposicao) {
		this.tempoReposicao = tempoReposicao;
	}

	/**
	 * Return the value associated with the column: periodo_consumo_medio
	 */
	public java.lang.Long getPeriodoConsumoMedio() {
		return getPropertyValue(this, periodoConsumoMedio, PROP_PERIODO_CONSUMO_MEDIO);
	}

	/**
	 * Set the value related to the column: periodo_consumo_medio
	 * @param periodoConsumoMedio the periodo_consumo_medio value
	 */
	public void setPeriodoConsumoMedio (java.lang.Long periodoConsumoMedio) {
		this.periodoConsumoMedio = periodoConsumoMedio;
	}


	/**
	 * Return the value associated with the column: estoque_maximo
	 */
	public java.lang.Double getEstoqueMaximo () {
		return getPropertyValue(this, estoqueMaximo, PROP_ESTOQUE_MAXIMO); 
	}

	/**
	 * Set the value related to the column: estoque_maximo
	 * @param estoqueMaximo the estoque_maximo value
	 */
	public void setEstoqueMaximo (java.lang.Double estoqueMaximo) {
//        java.lang.Double estoqueMaximoOld = this.estoqueMaximo;
		this.estoqueMaximo = estoqueMaximo;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueMaximo", estoqueMaximoOld, estoqueMaximo);
	}



	/**
	 * Return the value associated with the column: estoque_encomendado
	 */
	public java.lang.Double getEstoqueEncomendado () {
		return getPropertyValue(this, estoqueEncomendado, PROP_ESTOQUE_ENCOMENDADO); 
	}

	/**
	 * Set the value related to the column: estoque_encomendado
	 * @param estoqueEncomendado the estoque_encomendado value
	 */
	public void setEstoqueEncomendado (java.lang.Double estoqueEncomendado) {
//        java.lang.Double estoqueEncomendadoOld = this.estoqueEncomendado;
		this.estoqueEncomendado = estoqueEncomendado;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueEncomendado", estoqueEncomendadoOld, estoqueEncomendado);
	}



	/**
	 * Return the value associated with the column: estoque_reservado
	 */
	public java.lang.Double getEstoqueReservado () {
		return getPropertyValue(this, estoqueReservado, PROP_ESTOQUE_RESERVADO); 
	}

	/**
	 * Set the value related to the column: estoque_reservado
	 * @param estoqueReservado the estoque_reservado value
	 */
	public void setEstoqueReservado (java.lang.Double estoqueReservado) {
//        java.lang.Double estoqueReservadoOld = this.estoqueReservado;
		this.estoqueReservado = estoqueReservado;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueReservado", estoqueReservadoOld, estoqueReservado);
	}



	/**
	 * Return the value associated with the column: estoque_reservado_devolucao
	 */
	public java.lang.Double getEstoqueReservadoDevolucao () {
		return getPropertyValue(this, estoqueReservadoDevolucao, PROP_ESTOQUE_RESERVADO_DEVOLUCAO); 
	}

	/**
	 * Set the value related to the column: estoque_reservado_devolucao
	 * @param estoqueReservadoDevolucao the estoque_reservado_devolucao value
	 */
	public void setEstoqueReservadoDevolucao (java.lang.Double estoqueReservadoDevolucao) {
//        java.lang.Double estoqueReservadoDevolucaoOld = this.estoqueReservadoDevolucao;
		this.estoqueReservadoDevolucao = estoqueReservadoDevolucao;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueReservadoDevolucao", estoqueReservadoDevolucaoOld, estoqueReservadoDevolucao);
	}



	/**
	 * Return the value associated with the column: estoque_producao
	 */
	public java.lang.Double getEstoqueProducao () {
		return getPropertyValue(this, estoqueProducao, PROP_ESTOQUE_PRODUCAO); 
	}

	/**
	 * Set the value related to the column: estoque_producao
	 * @param estoqueProducao the estoque_producao value
	 */
	public void setEstoqueProducao (java.lang.Double estoqueProducao) {
//        java.lang.Double estoqueProducaoOld = this.estoqueProducao;
		this.estoqueProducao = estoqueProducao;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueProducao", estoqueProducaoOld, estoqueProducao);
	}



	/**
	 * Return the value associated with the column: estoque_minimo
	 */
	public java.lang.Double getEstoqueMinimo () {
		return getPropertyValue(this, estoqueMinimo, PROP_ESTOQUE_MINIMO); 
	}

	/**
	 * Set the value related to the column: estoque_minimo
	 * @param estoqueMinimo the estoque_minimo value
	 */
	public void setEstoqueMinimo (java.lang.Double estoqueMinimo) {
//        java.lang.Double estoqueMinimoOld = this.estoqueMinimo;
		this.estoqueMinimo = estoqueMinimo;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueMinimo", estoqueMinimoOld, estoqueMinimo);
	}



	/**
	 * Return the value associated with the column: consumo_medio
	 */
	public java.lang.Double getConsumoMedio () {
		return getPropertyValue(this, consumoMedio, PROP_CONSUMO_MEDIO); 
	}

	/**
	 * Set the value related to the column: consumo_medio
	 * @param consumoMedio the consumo_medio value
	 */
	public void setConsumoMedio (java.lang.Double consumoMedio) {
//        java.lang.Double consumoMedioOld = this.consumoMedio;
		this.consumoMedio = consumoMedio;
//        this.getPropertyChangeSupport().firePropertyChange ("consumoMedio", consumoMedioOld, consumoMedio);
	}



	/**
	 * Return the value associated with the column: estoque_fisico
	 */
	public java.lang.Double getEstoqueFisico () {
		return getPropertyValue(this, estoqueFisico, PROP_ESTOQUE_FISICO); 
	}

	/**
	 * Set the value related to the column: estoque_fisico
	 * @param estoqueFisico the estoque_fisico value
	 */
	public void setEstoqueFisico (java.lang.Double estoqueFisico) {
//        java.lang.Double estoqueFisicoOld = this.estoqueFisico;
		this.estoqueFisico = estoqueFisico;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueFisico", estoqueFisicoOld, estoqueFisico);
	}



	/**
	 * Return the value associated with the column: preco_venda
	 */
	public java.lang.Double getPrecoVenda () {
		return getPropertyValue(this, precoVenda, PROP_PRECO_VENDA); 
	}

	/**
	 * Set the value related to the column: preco_venda
	 * @param precoVenda the preco_venda value
	 */
	public void setPrecoVenda (java.lang.Double precoVenda) {
//        java.lang.Double precoVendaOld = this.precoVenda;
		this.precoVenda = precoVenda;
//        this.getPropertyChangeSupport().firePropertyChange ("precoVenda", precoVendaOld, precoVenda);
	}



	/**
	 * Return the value associated with the column: estoque_nao_conforme
	 */
	public java.lang.Double getEstoqueNaoConforme () {
		return getPropertyValue(this, estoqueNaoConforme, PROP_ESTOQUE_NAO_CONFORME); 
	}

	/**
	 * Set the value related to the column: estoque_nao_conforme
	 * @param estoqueNaoConforme the estoque_nao_conforme value
	 */
	public void setEstoqueNaoConforme (java.lang.Double estoqueNaoConforme) {
//        java.lang.Double estoqueNaoConformeOld = this.estoqueNaoConforme;
		this.estoqueNaoConforme = estoqueNaoConforme;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueNaoConforme", estoqueNaoConformeOld, estoqueNaoConforme);
	}



	/**
	 * Return the value associated with the column: estoque_reposicao
	 */
	public java.lang.String getEstoqueReposicao () {
		return getPropertyValue(this, estoqueReposicao, PROP_ESTOQUE_REPOSICAO); 
	}

	/**
	 * Set the value related to the column: estoque_reposicao
	 * @param estoqueReposicao the estoque_reposicao value
	 */
	public void setEstoqueReposicao (java.lang.String estoqueReposicao) {
//        java.lang.String estoqueReposicaoOld = this.estoqueReposicao;
		this.estoqueReposicao = estoqueReposicao;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueReposicao", estoqueReposicaoOld, estoqueReposicao);
	}



	/**
	 * Return the value associated with the column: qtdade_ideal
	 */
	public java.lang.Double getQuantidadeIdeal () {
		return getPropertyValue(this, quantidadeIdeal, PROP_QUANTIDADE_IDEAL); 
	}

	/**
	 * Set the value related to the column: qtdade_ideal
	 * @param quantidadeIdeal the qtdade_ideal value
	 */
	public void setQuantidadeIdeal (java.lang.Double quantidadeIdeal) {
//        java.lang.Double quantidadeIdealOld = this.quantidadeIdeal;
		this.quantidadeIdeal = quantidadeIdeal;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeIdeal", quantidadeIdealOld, quantidadeIdeal);
	}



	/**
	 * Return the value associated with the column: qtdade_multipla
	 */
	public java.lang.Double getQuantidadeMultipla () {
		return getPropertyValue(this, quantidadeMultipla, PROP_QUANTIDADE_MULTIPLA); 
	}

	/**
	 * Set the value related to the column: qtdade_multipla
	 * @param quantidadeMultipla the qtdade_multipla value
	 */
	public void setQuantidadeMultipla (java.lang.Double quantidadeMultipla) {
//        java.lang.Double quantidadeMultiplaOld = this.quantidadeMultipla;
		this.quantidadeMultipla = quantidadeMultipla;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeMultipla", quantidadeMultiplaOld, quantidadeMultipla);
	}



	/**
	 * Return the value associated with the column: prazo_medio_oc_recebto
	 */
	public java.lang.Double getPrazoMedioOCRecebimento () {
		return getPropertyValue(this, prazoMedioOCRecebimento, PROP_PRAZO_MEDIO_O_C_RECEBIMENTO); 
	}

	/**
	 * Set the value related to the column: prazo_medio_oc_recebto
	 * @param prazoMedioOCRecebimento the prazo_medio_oc_recebto value
	 */
	public void setPrazoMedioOCRecebimento (java.lang.Double prazoMedioOCRecebimento) {
//        java.lang.Double prazoMedioOCRecebimentoOld = this.prazoMedioOCRecebimento;
		this.prazoMedioOCRecebimento = prazoMedioOCRecebimento;
//        this.getPropertyChangeSupport().firePropertyChange ("prazoMedioOCRecebimento", prazoMedioOCRecebimentoOld, prazoMedioOCRecebimento);
	}



	/**
	 * Return the value associated with the column: flag_ativo
	 */
	public java.lang.String getFlagAtivo () {
		return getPropertyValue(this, flagAtivo, PROP_FLAG_ATIVO); 
	}

	/**
	 * Set the value related to the column: flag_ativo
	 * @param flagAtivo the flag_ativo value
	 */
	public void setFlagAtivo (java.lang.String flagAtivo) {
//        java.lang.String flagAtivoOld = this.flagAtivo;
		this.flagAtivo = flagAtivo;
//        this.getPropertyChangeSupport().firePropertyChange ("flagAtivo", flagAtivoOld, flagAtivo);
	}



	/**
	 * Return the value associated with the column: qt_padrao_dispensacao
	 */
	public java.lang.Double getQuantidadePadraoDispensacao () {
		return getPropertyValue(this, quantidadePadraoDispensacao, PROP_QUANTIDADE_PADRAO_DISPENSACAO); 
	}

	/**
	 * Set the value related to the column: qt_padrao_dispensacao
	 * @param quantidadePadraoDispensacao the qt_padrao_dispensacao value
	 */
	public void setQuantidadePadraoDispensacao (java.lang.Double quantidadePadraoDispensacao) {
//        java.lang.Double quantidadePadraoDispensacaoOld = this.quantidadePadraoDispensacao;
		this.quantidadePadraoDispensacao = quantidadePadraoDispensacao;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadePadraoDispensacao", quantidadePadraoDispensacaoOld, quantidadePadraoDispensacao);
	}



	/**
	 * Return the value associated with the column: preco_custo
	 */
	public java.lang.Double getPrecoCusto () {
		return getPropertyValue(this, precoCusto, PROP_PRECO_CUSTO); 
	}

	/**
	 * Set the value related to the column: preco_custo
	 * @param precoCusto the preco_custo value
	 */
	public void setPrecoCusto (java.lang.Double precoCusto) {
//        java.lang.Double precoCustoOld = this.precoCusto;
		this.precoCusto = precoCusto;
//        this.getPropertyChangeSupport().firePropertyChange ("precoCusto", precoCustoOld, precoCusto);
	}



	/**
	 * Return the value associated with the column: preco_medio
	 */
	public java.lang.Double getPrecoMedio () {
		return getPropertyValue(this, precoMedio, PROP_PRECO_MEDIO); 
	}

	/**
	 * Set the value related to the column: preco_medio
	 * @param precoMedio the preco_medio value
	 */
	public void setPrecoMedio (java.lang.Double precoMedio) {
//        java.lang.Double precoMedioOld = this.precoMedio;
		this.precoMedio = precoMedio;
//        this.getPropertyChangeSupport().firePropertyChange ("precoMedio", precoMedioOld, precoMedio);
	}



	/**
	 * Return the value associated with the column: ultimo_preco
	 */
	public java.lang.Double getUltimoPreco () {
		return getPropertyValue(this, ultimoPreco, PROP_ULTIMO_PRECO); 
	}

	/**
	 * Set the value related to the column: ultimo_preco
	 * @param ultimoPreco the ultimo_preco value
	 */
	public void setUltimoPreco (java.lang.Double ultimoPreco) {
//        java.lang.Double ultimoPrecoOld = this.ultimoPreco;
		this.ultimoPreco = ultimoPreco;
//        this.getPropertyChangeSupport().firePropertyChange ("ultimoPreco", ultimoPrecoOld, ultimoPreco);
	}



	/**
	 * Return the value associated with the column: dt_integracao_inovamfri
	 */
	public java.util.Date getDataIntegracaoInovamfri () {
		return getPropertyValue(this, dataIntegracaoInovamfri, PROP_DATA_INTEGRACAO_INOVAMFRI); 
	}

	/**
	 * Set the value related to the column: dt_integracao_inovamfri
	 * @param dataIntegracaoInovamfri the dt_integracao_inovamfri value
	 */
	public void setDataIntegracaoInovamfri (java.util.Date dataIntegracaoInovamfri) {
//        java.util.Date dataIntegracaoInovamfriOld = this.dataIntegracaoInovamfri;
		this.dataIntegracaoInovamfri = dataIntegracaoInovamfri;
//        this.getPropertyChangeSupport().firePropertyChange ("dataIntegracaoInovamfri", dataIntegracaoInovamfriOld, dataIntegracaoInovamfri);
	}



	/**
	 * Return the value associated with the column: cod_pro
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Produto getRoProduto () {
		return getPropertyValue(this, roProduto, PROP_RO_PRODUTO); 
	}

	/**
	 * Set the value related to the column: cod_pro
	 * @param roProduto the cod_pro value
	 */
	public void setRoProduto (br.com.ksisolucoes.vo.entradas.estoque.Produto roProduto) {
//        br.com.ksisolucoes.vo.entradas.estoque.Produto roProdutoOld = this.roProduto;
		this.roProduto = roProduto;
//        this.getPropertyChangeSupport().firePropertyChange ("roProduto", roProdutoOld, roProduto);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getRoEmpresa () {
		return getPropertyValue(this, roEmpresa, PROP_RO_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param roEmpresa the empresa value
	 */
	public void setRoEmpresa (br.com.ksisolucoes.vo.basico.Empresa roEmpresa) {
//        br.com.ksisolucoes.vo.basico.Empresa roEmpresaOld = this.roEmpresa;
		this.roEmpresa = roEmpresa;
//        this.getPropertyChangeSupport().firePropertyChange ("roEmpresa", roEmpresaOld, roEmpresa);
	}



	/**
	 * Return the value associated with the column: ultimo_fornecedor
	 */
	public br.com.ksisolucoes.vo.basico.Pessoa getUltimoFornecedor () {
		return getPropertyValue(this, ultimoFornecedor, PROP_ULTIMO_FORNECEDOR); 
	}

	/**
	 * Set the value related to the column: ultimo_fornecedor
	 * @param ultimoFornecedor the ultimo_fornecedor value
	 */
	public void setUltimoFornecedor (br.com.ksisolucoes.vo.basico.Pessoa ultimoFornecedor) {
//        br.com.ksisolucoes.vo.basico.Pessoa ultimoFornecedorOld = this.ultimoFornecedor;
		this.ultimoFornecedor = ultimoFornecedor;
//        this.getPropertyChangeSupport().firePropertyChange ("ultimoFornecedor", ultimoFornecedorOld, ultimoFornecedor);
	}



	/**
	 * Return the value associated with the column: cod_loc
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Localizacao getLocalizacao () {
		return getPropertyValue(this, localizacao, PROP_LOCALIZACAO); 
	}

	/**
	 * Set the value related to the column: cod_loc
	 * @param localizacao the cod_loc value
	 */
	public void setLocalizacao (br.com.ksisolucoes.vo.entradas.estoque.Localizacao localizacao) {
//        br.com.ksisolucoes.vo.entradas.estoque.Localizacao localizacaoOld = this.localizacao;
		this.localizacao = localizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("localizacao", localizacaoOld, localizacao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa)) return false;
		else {
			br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa estoqueEmpresa = (br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa) obj;
			if (null == this.getId() || null == estoqueEmpresa.getId()) return false;
			else return (this.getId().equals(estoqueEmpresa.getId()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getId()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getId().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}