package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import java.io.Serializable;

import br.com.ksisolucoes.vo.prontuario.basico.base.BasePlanejamentoFamiliar;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

public class PlanejamentoFamiliar extends BasePlanejamentoFamiliar implements CodigoManager {

    private static final long serialVersionUID = 1L;

    public static enum Parto implements IEnum {

        NORMAL(1L, Bundle.getStringApplication("rotulo_normal")),
        CESAREA(2L, Bundle.getStringApplication("rotulo_cesarea")),
        OUTRO(3L, Bundle.getStringApplication("rotulo_outro"));

        private Long codigo;
        private String descricao;

        private Parto(Long codigo, String descricao) {
            this.codigo = codigo;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return this.codigo;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    public static enum ExameCitopatologico implements IEnum {

        EM_DIA(1L, Bundle.getStringApplication("rotulo_em_dia")),
        ATRASADO(2L, Bundle.getStringApplication("rotulo_atrasado")),
        NUNCA_FEZ(3L, Bundle.getStringApplication("rotulo_nunca_fez"));

        private Long codigo;
        private String descricao;

        private ExameCitopatologico(Long codigo, String descricao) {
            this.codigo = codigo;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return this.codigo;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    /* [CONSTRUCTOR MARKER BEGIN] */
    public PlanejamentoFamiliar() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public PlanejamentoFamiliar(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public PlanejamentoFamiliar(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {

        super(
                codigo,
                atendimento);
    }

    /* [CONSTRUCTOR MARKER END] */
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}
