<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.requerimentos"  >
    <class name="RequerimentoProrrogacaoPrazoItem" table="requerimento_prorrogacao_prazo_item" >
        <id
            column="cd_requerimento_prorrogacao_prazo_item"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />    
        
        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProrrogacaoPrazo"
            column="cd_requerimento_prorrogacao_prazo"
            name="requerimentoProrrogacaoPrazo"
            not-null="true"
        />
        
        <property
            column="descricao"
            name="descricao"
            not-null="true"
            type="java.lang.String"
            length="80"
        />
        
        <property
            column="motivo"
            name="motivo"
            not-null="true"
            type="java.lang.String"
            length="500"
        />
        
        <property
            column="prazo"
            name="prazo"
            not-null="false"
            type="java.lang.Long"
        />

        <property
            column="status"
            name="status"
            not-null="true"
            type="java.lang.Long"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacaoExigencia"
            column="cd_auto_intimacao_exigencia"
            name="autoIntimacaoExigencia"
            not-null="false"
        />

        <property
            column="ds_observacao_fiscal"
            name="observacaoFiscal"
            not-null="false"
            type="java.lang.String"
            length="500"
        />

        <property
            column="nova_data_cumprimento"
            name="novaDataCumprimento"
            not-null="false"
            type="java.util.Date"
        />

        <property
            column="data_cumprimento_original"
            name="dataCumprimentoOriginal"
            not-null="false"
            type="java.util.Date"
        />

        <property
            column="prazo_original"
            name="prazoOriginal"
            not-null="false"
            type="java.lang.Long"
        />
    </class>
</hibernate-mapping>
