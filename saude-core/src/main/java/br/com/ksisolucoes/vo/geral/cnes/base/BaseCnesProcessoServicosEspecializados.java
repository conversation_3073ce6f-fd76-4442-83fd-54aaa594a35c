package br.com.ksisolucoes.vo.geral.cnes.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the cnes_processo_serv_especializados table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="cnes_processo_serv_especializados"
 */

public abstract class BaseCnesProcessoServicosEspecializados extends BaseRootVO implements Serializable {

	public static String REF = "CnesProcessoServicosEspecializados";
	public static final String PROP_CODIGO_SERVICO_HOSPITALAR_SUS = "codigoServicoHospitalarSus";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_CODIGO_SERVICO_HOSPITALAR = "codigoServicoHospitalar";
	public static final String PROP_CODIGO_CARACTERISTICA = "codigoCaracteristica";
	public static final String PROP_DESCRICAO_ENDERECO_COMPLEMENTAR = "descricaoEnderecoComplementar";
	public static final String PROP_CODIGO_CLASSIFICACAO = "codigoClassificacao";
	public static final String PROP_CODIGO_SERVICO = "codigoServico";
	public static final String PROP_CNES_PROCESSO_EMPRESA = "cnesProcessoEmpresa";
	public static final String PROP_DESCRICAO_SERVICO = "descricaoServico";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_DESCRICAO_CLASSIFICACAO = "descricaoClassificacao";
	public static final String PROP_CODIGO_SERVICO_AMBULATORIAL_SUS = "codigoServicoAmbulatorialSus";
	public static final String PROP_CNPJ_CPF = "cnpjCpf";
	public static final String PROP_CODIGO_ENDERECO_COMPLEMENTAR = "codigoEnderecoComplementar";
	public static final String PROP_CODIGO_SERVICO_AMBULATORIAL = "codigoServicoAmbulatorial";


	// constructors
	public BaseCnesProcessoServicosEspecializados () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseCnesProcessoServicosEspecializados (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseCnesProcessoServicosEspecializados (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.geral.cnes.CnesProcessoEmpresa cnesProcessoEmpresa,
		java.lang.String codigoServico,
		java.lang.String descricaoServico,
		java.lang.String codigoCaracteristica,
		java.lang.String codigoClassificacao,
		java.lang.String cnpjCpf) {

		this.setCodigo(codigo);
		this.setCnesProcessoEmpresa(cnesProcessoEmpresa);
		this.setCodigoServico(codigoServico);
		this.setDescricaoServico(descricaoServico);
		this.setCodigoCaracteristica(codigoCaracteristica);
		this.setCodigoClassificacao(codigoClassificacao);
		this.setCnpjCpf(cnpjCpf);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String codigoServico;
	private java.lang.String descricaoServico;
	private java.lang.String codigoCaracteristica;
	private java.lang.String codigoServicoAmbulatorial;
	private java.lang.String codigoServicoAmbulatorialSus;
	private java.lang.String codigoServicoHospitalar;
	private java.lang.String codigoServicoHospitalarSus;
	private java.lang.String codigoClassificacao;
	private java.lang.String cnpjCpf;
	private java.lang.String codigoEnderecoComplementar;
	private java.lang.String descricaoEnderecoComplementar;
	private java.lang.String descricaoClassificacao;
	private java.lang.String usuario;

	// many to one
	private br.com.ksisolucoes.vo.geral.cnes.CnesProcessoEmpresa cnesProcessoEmpresa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_cnes_processo_serv_especializados"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cod_servico
	 */
	public java.lang.String getCodigoServico () {
		return getPropertyValue(this, codigoServico, PROP_CODIGO_SERVICO); 
	}

	/**
	 * Set the value related to the column: cod_servico
	 * @param codigoServico the cod_servico value
	 */
	public void setCodigoServico (java.lang.String codigoServico) {
//        java.lang.String codigoServicoOld = this.codigoServico;
		this.codigoServico = codigoServico;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoServico", codigoServicoOld, codigoServico);
	}



	/**
	 * Return the value associated with the column: ds_servico
	 */
	public java.lang.String getDescricaoServico () {
		return getPropertyValue(this, descricaoServico, PROP_DESCRICAO_SERVICO); 
	}

	/**
	 * Set the value related to the column: ds_servico
	 * @param descricaoServico the ds_servico value
	 */
	public void setDescricaoServico (java.lang.String descricaoServico) {
//        java.lang.String descricaoServicoOld = this.descricaoServico;
		this.descricaoServico = descricaoServico;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoServico", descricaoServicoOld, descricaoServico);
	}



	/**
	 * Return the value associated with the column: cod_caracteristica
	 */
	public java.lang.String getCodigoCaracteristica () {
		return getPropertyValue(this, codigoCaracteristica, PROP_CODIGO_CARACTERISTICA); 
	}

	/**
	 * Set the value related to the column: cod_caracteristica
	 * @param codigoCaracteristica the cod_caracteristica value
	 */
	public void setCodigoCaracteristica (java.lang.String codigoCaracteristica) {
//        java.lang.String codigoCaracteristicaOld = this.codigoCaracteristica;
		this.codigoCaracteristica = codigoCaracteristica;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoCaracteristica", codigoCaracteristicaOld, codigoCaracteristica);
	}



	/**
	 * Return the value associated with the column: cod_serv_ambul
	 */
	public java.lang.String getCodigoServicoAmbulatorial () {
		return getPropertyValue(this, codigoServicoAmbulatorial, PROP_CODIGO_SERVICO_AMBULATORIAL); 
	}

	/**
	 * Set the value related to the column: cod_serv_ambul
	 * @param codigoServicoAmbulatorial the cod_serv_ambul value
	 */
	public void setCodigoServicoAmbulatorial (java.lang.String codigoServicoAmbulatorial) {
//        java.lang.String codigoServicoAmbulatorialOld = this.codigoServicoAmbulatorial;
		this.codigoServicoAmbulatorial = codigoServicoAmbulatorial;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoServicoAmbulatorial", codigoServicoAmbulatorialOld, codigoServicoAmbulatorial);
	}



	/**
	 * Return the value associated with the column: cod_serv_ambul_sus
	 */
	public java.lang.String getCodigoServicoAmbulatorialSus () {
		return getPropertyValue(this, codigoServicoAmbulatorialSus, PROP_CODIGO_SERVICO_AMBULATORIAL_SUS); 
	}

	/**
	 * Set the value related to the column: cod_serv_ambul_sus
	 * @param codigoServicoAmbulatorialSus the cod_serv_ambul_sus value
	 */
	public void setCodigoServicoAmbulatorialSus (java.lang.String codigoServicoAmbulatorialSus) {
//        java.lang.String codigoServicoAmbulatorialSusOld = this.codigoServicoAmbulatorialSus;
		this.codigoServicoAmbulatorialSus = codigoServicoAmbulatorialSus;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoServicoAmbulatorialSus", codigoServicoAmbulatorialSusOld, codigoServicoAmbulatorialSus);
	}



	/**
	 * Return the value associated with the column: cod_serv_hosp
	 */
	public java.lang.String getCodigoServicoHospitalar () {
		return getPropertyValue(this, codigoServicoHospitalar, PROP_CODIGO_SERVICO_HOSPITALAR); 
	}

	/**
	 * Set the value related to the column: cod_serv_hosp
	 * @param codigoServicoHospitalar the cod_serv_hosp value
	 */
	public void setCodigoServicoHospitalar (java.lang.String codigoServicoHospitalar) {
//        java.lang.String codigoServicoHospitalarOld = this.codigoServicoHospitalar;
		this.codigoServicoHospitalar = codigoServicoHospitalar;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoServicoHospitalar", codigoServicoHospitalarOld, codigoServicoHospitalar);
	}



	/**
	 * Return the value associated with the column: cod_serv_hosp_sus
	 */
	public java.lang.String getCodigoServicoHospitalarSus () {
		return getPropertyValue(this, codigoServicoHospitalarSus, PROP_CODIGO_SERVICO_HOSPITALAR_SUS); 
	}

	/**
	 * Set the value related to the column: cod_serv_hosp_sus
	 * @param codigoServicoHospitalarSus the cod_serv_hosp_sus value
	 */
	public void setCodigoServicoHospitalarSus (java.lang.String codigoServicoHospitalarSus) {
//        java.lang.String codigoServicoHospitalarSusOld = this.codigoServicoHospitalarSus;
		this.codigoServicoHospitalarSus = codigoServicoHospitalarSus;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoServicoHospitalarSus", codigoServicoHospitalarSusOld, codigoServicoHospitalarSus);
	}



	/**
	 * Return the value associated with the column: cod_class
	 */
	public java.lang.String getCodigoClassificacao () {
		return getPropertyValue(this, codigoClassificacao, PROP_CODIGO_CLASSIFICACAO); 
	}

	/**
	 * Set the value related to the column: cod_class
	 * @param codigoClassificacao the cod_class value
	 */
	public void setCodigoClassificacao (java.lang.String codigoClassificacao) {
//        java.lang.String codigoClassificacaoOld = this.codigoClassificacao;
		this.codigoClassificacao = codigoClassificacao;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoClassificacao", codigoClassificacaoOld, codigoClassificacao);
	}



	/**
	 * Return the value associated with the column: cnpj_cpf
	 */
	public java.lang.String getCnpjCpf () {
		return getPropertyValue(this, cnpjCpf, PROP_CNPJ_CPF); 
	}

	/**
	 * Set the value related to the column: cnpj_cpf
	 * @param cnpjCpf the cnpj_cpf value
	 */
	public void setCnpjCpf (java.lang.String cnpjCpf) {
//        java.lang.String cnpjCpfOld = this.cnpjCpf;
		this.cnpjCpf = cnpjCpf;
//        this.getPropertyChangeSupport().firePropertyChange ("cnpjCpf", cnpjCpfOld, cnpjCpf);
	}



	/**
	 * Return the value associated with the column: cod_end_compl
	 */
	public java.lang.String getCodigoEnderecoComplementar () {
		return getPropertyValue(this, codigoEnderecoComplementar, PROP_CODIGO_ENDERECO_COMPLEMENTAR); 
	}

	/**
	 * Set the value related to the column: cod_end_compl
	 * @param codigoEnderecoComplementar the cod_end_compl value
	 */
	public void setCodigoEnderecoComplementar (java.lang.String codigoEnderecoComplementar) {
//        java.lang.String codigoEnderecoComplementarOld = this.codigoEnderecoComplementar;
		this.codigoEnderecoComplementar = codigoEnderecoComplementar;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoEnderecoComplementar", codigoEnderecoComplementarOld, codigoEnderecoComplementar);
	}



	/**
	 * Return the value associated with the column: ds_end_compl
	 */
	public java.lang.String getDescricaoEnderecoComplementar () {
		return getPropertyValue(this, descricaoEnderecoComplementar, PROP_DESCRICAO_ENDERECO_COMPLEMENTAR); 
	}

	/**
	 * Set the value related to the column: ds_end_compl
	 * @param descricaoEnderecoComplementar the ds_end_compl value
	 */
	public void setDescricaoEnderecoComplementar (java.lang.String descricaoEnderecoComplementar) {
//        java.lang.String descricaoEnderecoComplementarOld = this.descricaoEnderecoComplementar;
		this.descricaoEnderecoComplementar = descricaoEnderecoComplementar;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoEnderecoComplementar", descricaoEnderecoComplementarOld, descricaoEnderecoComplementar);
	}



	/**
	 * Return the value associated with the column: ds_class
	 */
	public java.lang.String getDescricaoClassificacao () {
		return getPropertyValue(this, descricaoClassificacao, PROP_DESCRICAO_CLASSIFICACAO); 
	}

	/**
	 * Set the value related to the column: ds_class
	 * @param descricaoClassificacao the ds_class value
	 */
	public void setDescricaoClassificacao (java.lang.String descricaoClassificacao) {
//        java.lang.String descricaoClassificacaoOld = this.descricaoClassificacao;
		this.descricaoClassificacao = descricaoClassificacao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoClassificacao", descricaoClassificacaoOld, descricaoClassificacao);
	}



	/**
	 * Return the value associated with the column: usuario
	 */
	public java.lang.String getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: usuario
	 * @param usuario the usuario value
	 */
	public void setUsuario (java.lang.String usuario) {
//        java.lang.String usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_cnes_processo_empresa
	 */
	public br.com.ksisolucoes.vo.geral.cnes.CnesProcessoEmpresa getCnesProcessoEmpresa () {
		return getPropertyValue(this, cnesProcessoEmpresa, PROP_CNES_PROCESSO_EMPRESA); 
	}

	/**
	 * Set the value related to the column: cd_cnes_processo_empresa
	 * @param cnesProcessoEmpresa the cd_cnes_processo_empresa value
	 */
	public void setCnesProcessoEmpresa (br.com.ksisolucoes.vo.geral.cnes.CnesProcessoEmpresa cnesProcessoEmpresa) {
//        br.com.ksisolucoes.vo.geral.cnes.CnesProcessoEmpresa cnesProcessoEmpresaOld = this.cnesProcessoEmpresa;
		this.cnesProcessoEmpresa = cnesProcessoEmpresa;
//        this.getPropertyChangeSupport().firePropertyChange ("cnesProcessoEmpresa", cnesProcessoEmpresaOld, cnesProcessoEmpresa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.geral.cnes.CnesProcessoServicosEspecializados)) return false;
		else {
			br.com.ksisolucoes.vo.geral.cnes.CnesProcessoServicosEspecializados cnesProcessoServicosEspecializados = (br.com.ksisolucoes.vo.geral.cnes.CnesProcessoServicosEspecializados) obj;
			if (null == this.getCodigo() || null == cnesProcessoServicosEspecializados.getCodigo()) return false;
			else return (this.getCodigo().equals(cnesProcessoServicosEspecializados.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}