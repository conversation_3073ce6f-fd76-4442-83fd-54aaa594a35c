<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.service.sms"  >
    <class name="SmsCadastroDoenca" table="sms_cadastro_doenca">
        
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_sms_cadastro_doenca"
        >
            <generator class="assigned" />
        </id> <version column="version" name="version" type="long" />


        <many-to-one
            class="br.com.ksisolucoes.vo.service.sms.SmsCadastro"
            column="cd_sms_cadastro"
            name="SmsCadastro"
            not-null="true"
         />

        <many-to-one
            class="br.com.ksisolucoes.vo.basico.Doenca"
            column="cd_doenca"
            name="Doenca"
            not-null="true"
         />

    </class>
</hibernate-mapping>
