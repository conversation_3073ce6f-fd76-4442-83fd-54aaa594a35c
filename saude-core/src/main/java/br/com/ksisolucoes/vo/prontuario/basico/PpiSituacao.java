package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;

public enum PpiSituacao implements IEnum<PpiSituacao> {
    ATIVO(1L, "rotulo_ativo"),
    EXCLUIDO(2L, "rotulo_excluido");

    private final Long value;
    private final String descricao;

    PpiSituacao(Long value, String rotuloDescricao) {
        this.value = value;
        this.descricao = Bundle.getStringApplication(rotuloDescricao);
    }

    @Override
    public Long value() {
        return value;
    }

    @Override
    public String descricao() {
        return descricao;
    }

}
