package br.com.ksisolucoes.vo.atividadegrupo.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the atividade_grupo_procedimento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="atividade_grupo_procedimento"
 */

public abstract class BaseAtividadeGrupoProcedimento extends BaseRootVO implements Serializable {

	public static String REF = "AtividadeGrupoProcedimento";
	public static final String PROP_TABELA_CBO = "tabelaCbo";
	public static final String PROP_EMPRESA_BPA = "empresaBpa";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_ATIVIDADE_GRUPO = "atividadeGrupo";
	public static final String PROP_PROFISSIONAL = "profissional";
	public static final String PROP_PROCEDIMENTO = "procedimento";
	public static final String PROP_QUANTIDADE = "quantidade";


	// constructors
	public BaseAtividadeGrupoProcedimento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAtividadeGrupoProcedimento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long quantidade;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento;
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCbo;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissional;
	private br.com.ksisolucoes.vo.basico.Empresa empresaBpa;
	private br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo atividadeGrupo;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_at_gr_proc"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: quantidade
	 */
	public java.lang.Long getQuantidade () {
		return getPropertyValue(this, quantidade, PROP_QUANTIDADE); 
	}

	/**
	 * Set the value related to the column: quantidade
	 * @param quantidade the quantidade value
	 */
	public void setQuantidade (java.lang.Long quantidade) {
//        java.lang.Long quantidadeOld = this.quantidade;
		this.quantidade = quantidade;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidade", quantidadeOld, quantidade);
	}



	/**
	 * Return the value associated with the column: cd_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento getProcedimento () {
		return getPropertyValue(this, procedimento, PROP_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_procedimento
	 * @param procedimento the cd_procedimento value
	 */
	public void setProcedimento (br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoOld = this.procedimento;
		this.procedimento = procedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimento", procedimentoOld, procedimento);
	}



	/**
	 * Return the value associated with the column: cd_cbo
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getTabelaCbo () {
		return getPropertyValue(this, tabelaCbo, PROP_TABELA_CBO); 
	}

	/**
	 * Set the value related to the column: cd_cbo
	 * @param tabelaCbo the cd_cbo value
	 */
	public void setTabelaCbo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCbo) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCboOld = this.tabelaCbo;
		this.tabelaCbo = tabelaCbo;
//        this.getPropertyChangeSupport().firePropertyChange ("tabelaCbo", tabelaCboOld, tabelaCbo);
	}



	/**
	 * Return the value associated with the column: cd_profissional
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissional () {
		return getPropertyValue(this, profissional, PROP_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_profissional
	 * @param profissional the cd_profissional value
	 */
	public void setProfissional (br.com.ksisolucoes.vo.cadsus.Profissional profissional) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalOld = this.profissional;
		this.profissional = profissional;
//        this.getPropertyChangeSupport().firePropertyChange ("profissional", profissionalOld, profissional);
	}



	/**
	 * Return the value associated with the column: empresa_bpa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresaBpa () {
		return getPropertyValue(this, empresaBpa, PROP_EMPRESA_BPA); 
	}

	/**
	 * Set the value related to the column: empresa_bpa
	 * @param empresaBpa the empresa_bpa value
	 */
	public void setEmpresaBpa (br.com.ksisolucoes.vo.basico.Empresa empresaBpa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaBpaOld = this.empresaBpa;
		this.empresaBpa = empresaBpa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaBpa", empresaBpaOld, empresaBpa);
	}



	/**
	 * Return the value associated with the column: cd_atv_grupo
	 */
	public br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo getAtividadeGrupo () {
		return getPropertyValue(this, atividadeGrupo, PROP_ATIVIDADE_GRUPO); 
	}

	/**
	 * Set the value related to the column: cd_atv_grupo
	 * @param atividadeGrupo the cd_atv_grupo value
	 */
	public void setAtividadeGrupo (br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo atividadeGrupo) {
//        br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo atividadeGrupoOld = this.atividadeGrupo;
		this.atividadeGrupo = atividadeGrupo;
//        this.getPropertyChangeSupport().firePropertyChange ("atividadeGrupo", atividadeGrupoOld, atividadeGrupo);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoProcedimento)) return false;
		else {
			br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoProcedimento atividadeGrupoProcedimento = (br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoProcedimento) obj;
			if (null == this.getCodigo() || null == atividadeGrupoProcedimento.getCodigo()) return false;
			else return (this.getCodigo().equals(atividadeGrupoProcedimento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}