package br.com.ksisolucoes.vo.basico;

import br.com.ksisolucoes.util.Bundle;
import java.io.Serializable;
import br.com.ksisolucoes.vo.basico.base.BaseProdutoSolicitado;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

public class ProdutoSolicitado extends BaseProdutoSolicitado implements CodigoManager {

    private static final long serialVersionUID = 1L;

    public static final String PROP_DESCRICAO_STATUS = "descricaoStatus";
    public static final String PROP_DESCRICAO_ORIGEM = "descricaoOrigem";

    public static final Long STATUS_ATIVO = 0L;
    public static final Long STATUS_EXCLUIDO = 3L;

    public static final Long ORIGEM_ESTADUAL = ProdutoSolicitadoHelper.ORIGEM_ESTADUAL;
    public static final Long ORIGEM_JUDICIAL = ProdutoSolicitadoHelper.ORIGEM_JUDICIAL;

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public ProdutoSolicitado () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ProdutoSolicitado (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ProdutoSolicitado (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.Profissional profissional,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.basico.TipoSolicitacaoProduto tipoSolicitacaoProduto,
		java.lang.Long origem,
		java.lang.Long status,
		java.util.Date dataCadastro,
		java.util.Date dataUsuario) {

		super (
			codigo,
			profissional,
			empresa,
			usuario,
			tipoSolicitacaoProduto,
			origem,
			status,
			dataCadastro,
			dataUsuario);
	}

    /*[CONSTRUCTOR MARKER END]*/
    @Override
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long)key);
    }

    @Override
    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getDescricaoOrigem() {
        return ProdutoSolicitadoHelper.getDescricaoOrigem(getOrigem());
    }

    public String getDescricaoStatus() {
        if (STATUS_ATIVO.equals(getStatus())) {
            return Bundle.getStringApplication("rotulo_ativo");
        } else {
            return Bundle.getStringApplication("rotulo_excluido");
        }
    }

}
