<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.requerimentos">
    <class name="RequerimentoInspecaoSanitaria" table="requerimento_inspecao_sanitaria">
        <id
                column="cd_requerimento_inspecao_sanitaria"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="assigned"/>
        </id>
        <version column="version" name="version" type="long"/>

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia"
                column="cd_req_vigilancia"
                name="requerimentoVigilancia"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.Estabelecimento"
                column="cd_estabelecimento"
                name="estabelecimento"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario"
                not-null="true"
                name="usuario"
        />

        <property
                name="dataCadastro"
                column="dt_cadastro"
                type="timestamp"
                not-null="true"
        />

        <property
                name="dataInspecao"
                column="dt_inspecao"
                type="timestamp"
                not-null="false"
        />

        <property
                name="naturezaInspecao"
                column="natureza_inspecao"
                type="java.lang.Long"
        />

		<property
                name="motivo"
                column="motivo"
                type="java.lang.String"
        />
    </class>
</hibernate-mapping>
