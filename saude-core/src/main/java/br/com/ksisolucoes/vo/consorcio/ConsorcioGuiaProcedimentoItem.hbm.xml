<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.consorcio"  >
    <class name="ConsorcioGuiaProcedimentoItem"  table="consorcio_guia_proc_item" >
        
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_guia_item"
        >
            <generator class="assigned"/>
        </id> 
        
        <version column="version" name="version" type="long" />

        <many-to-one  
            class="br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento"
            name="consorcioGuiaProcedimento"
            column="cd_guia"
            not-null="true"
         		/>
        
        <many-to-one  
            class="br.com.ksisolucoes.vo.consorcio.ConsorcioProcedimento"
            name="consorcioProcedimento"
            column="cd_consorcio_procedimento"
            not-null="true"
         		/>
        
        <property
            name="status"
            column="status"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property
            name="valorProcedimento"
            column="vl_procedimento"
            type="java.lang.Double"
            not-null="true"
        />
        
        <property
            name="valorProcedimentoImposto"
            column="vl_procedimento_imposto"
            type="java.lang.Double"
            not-null="false"
        />
        
        <property
            name="quantidade"
            column="quantidade"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property
            name="quantidadeAplicacao"
            column="qtdade_aplicacao"
            type="java.lang.Long"
            not-null="false"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.Cid"
            name="cid"
        >
            <column name="cd_cid"/>
        </many-to-one>
        
        <property
            name="numeroSisreg"
            column="nr_sisreg"
            type="java.lang.String"
            not-null="false"
            length="5"
        />

        <property
            name="codigoSisreg"
            column="cd_sisreg"
            type="java.lang.String"
            not-null="false"
            length="9"
        />
        
        <property
            name="dataSisreg"
            column="dt_sisreg"
            type="java.util.Date"
            not-null="false"
        />

        <property
                name="situacaoSisreg"
                column="situacao_sisreg"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                name="dataSituacaoSisreg"
                column="dt_situacao_sisreg"
                type="java.util.Date"
                not-null="false"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                name="usuarioSituacaoSisreg"
                column="cd_usu_situacao_sisreg"
                not-null="true"
        />

    </class>
</hibernate-mapping>