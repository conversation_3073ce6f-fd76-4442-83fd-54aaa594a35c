<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.agendamento.tfd"  >
    <class name="RequisicaoEnvioOcorrencia" table="requisicao_envio_ocorrencia" >

        <id
			name="codigo" 
			type="java.lang.Long"   
			column="cd_req_env_oco"
		> 
            <generator class="assigned"/>
        </id> <version column="version" name="version" type="long" />
		
        <many-to-one
                class="br.com.ksisolucoes.vo.agendamento.tfd.RequisicaoEnvioExame"
                name="requisicaoEnvioExame"
                not-null="true"
        >
            <column name="cd_req_envio"/>
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                name="usuario"
        >
            <column name="cd_usuario"/>
        </many-to-one>

        <property
            name="dataOcorrencia"
            type="timestamp"
            column="dt_ocorrencia"
            not-null="true"
            >
        </property>

        <property
            name="descricao"
            type="java.lang.String"
            column="ds_ocorrencia"
            not-null="true"
            >
        </property>

    </class>
</hibernate-mapping>
