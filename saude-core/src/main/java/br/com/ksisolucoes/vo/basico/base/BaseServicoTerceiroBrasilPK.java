package br.com.ksisolucoes.vo.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;


public abstract class BaseServicoTerceiroBrasilPK extends BaseRootVO implements Serializable {

	protected int hashCode = Integer.MIN_VALUE;

	public static String PROP_CNES = "cnes";
	public static String PROP_CODIGO_SERVICO = "codigoServico";
	public static String PROP_CODIGO_CLASSIFICACAO = "codigoClassificacao";

	private java.lang.String cnes;
	private java.lang.Long codigoServico;
	private java.lang.Long codigoClassificacao;


	public BaseServicoTerceiroBrasilPK () {}
	
	public BaseServicoTerceiroBrasilPK (
		java.lang.String cnes,
		java.lang.Long codigoServico,
		java.lang.Long codigoClassificacao) {

		this.setCnes(cnes);
		this.setCodigoServico(codigoServico);
		this.setCodigoClassificacao(codigoClassificacao);
	}


	/**
	 * Return the value associated with the column: cnes
	 */
	public java.lang.String getCnes () {
		return getPropertyValue(this, cnes, PROP_CNES); 
	}

	/**
	 * Set the value related to the column: cnes
	 * @param cnes the cnes value
	 */
	public void setCnes (java.lang.String cnes) {
//        java.lang.String cnesOld = this.cnes;
		this.cnes = cnes;
//        this.getPropertyChangeSupport().firePropertyChange ("cnes", cnesOld, cnes);
	}



	/**
	 * Return the value associated with the column: cd_servico
	 */
	public java.lang.Long getCodigoServico () {
		return getPropertyValue(this, codigoServico, PROP_CODIGO_SERVICO); 
	}

	/**
	 * Set the value related to the column: cd_servico
	 * @param codigoServico the cd_servico value
	 */
	public void setCodigoServico (java.lang.Long codigoServico) {
//        java.lang.Long codigoServicoOld = this.codigoServico;
		this.codigoServico = codigoServico;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoServico", codigoServicoOld, codigoServico);
	}



	/**
	 * Return the value associated with the column: cd_classificacao
	 */
	public java.lang.Long getCodigoClassificacao () {
		return getPropertyValue(this, codigoClassificacao, PROP_CODIGO_CLASSIFICACAO); 
	}

	/**
	 * Set the value related to the column: cd_classificacao
	 * @param codigoClassificacao the cd_classificacao value
	 */
	public void setCodigoClassificacao (java.lang.Long codigoClassificacao) {
//        java.lang.Long codigoClassificacaoOld = this.codigoClassificacao;
		this.codigoClassificacao = codigoClassificacao;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoClassificacao", codigoClassificacaoOld, codigoClassificacao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.ServicoTerceiroBrasilPK)) return false;
		else {
			br.com.ksisolucoes.vo.basico.ServicoTerceiroBrasilPK mObj = (br.com.ksisolucoes.vo.basico.ServicoTerceiroBrasilPK) obj;
			if (null != this.getCnes() && null != mObj.getCnes()) {
				if (!this.getCnes().equals(mObj.getCnes())) {
					return false;
				}
			}
			else {
				return false;
			}
			if (null != this.getCodigoServico() && null != mObj.getCodigoServico()) {
				if (!this.getCodigoServico().equals(mObj.getCodigoServico())) {
					return false;
				}
			}
			else {
				return false;
			}
			if (null != this.getCodigoClassificacao() && null != mObj.getCodigoClassificacao()) {
				if (!this.getCodigoClassificacao().equals(mObj.getCodigoClassificacao())) {
					return false;
				}
			}
			else {
				return false;
			}
			return true;
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			StringBuilder sb = new StringBuilder();
			if (null != this.getCnes()) {
				sb.append(this.getCnes().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			if (null != this.getCodigoServico()) {
				sb.append(this.getCodigoServico().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			if (null != this.getCodigoClassificacao()) {
				sb.append(this.getCodigoClassificacao().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			this.hashCode = sb.toString().hashCode();
		}
		return this.hashCode;
	}

    private java.beans.PropertyChangeSupport propertyChangeSupport;

    protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
        if( this.propertyChangeSupport == null ) {
            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
        }
        return this.propertyChangeSupport;
    }

    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.addPropertyChangeListener(l);
    }

    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
		propertyChangeSupport.addPropertyChangeListener(propertyName, listener);
    }

    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.removePropertyChangeListener(l);
    }
}