package br.com.ksisolucoes.vo.atividadegrupo.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the tipo_atividade_grupo_cbo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="tipo_atividade_grupo_cbo"
 */

public abstract class BaseTipoAtividadeGrupoCbo extends BaseRootVO implements Serializable {

	public static String REF = "TipoAtividadeGrupoCbo";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TIPO_ATIVIDADE_GRUPO = "tipoAtividadeGrupo";


	// constructors
	public BaseTipoAtividadeGrupoCbo () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseTipoAtividadeGrupoCbo (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// many to one
	private br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupo tipoAtividadeGrupo;
	private br.com.ksisolucoes.vo.basico.Empresa empresa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_atv_grupo_cbo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_tp_atv_grupo
	 */
	public br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupo getTipoAtividadeGrupo () {
		return getPropertyValue(this, tipoAtividadeGrupo, PROP_TIPO_ATIVIDADE_GRUPO); 
	}

	/**
	 * Set the value related to the column: cd_tp_atv_grupo
	 * @param tipoAtividadeGrupo the cd_tp_atv_grupo value
	 */
	public void setTipoAtividadeGrupo (br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupo tipoAtividadeGrupo) {
//        br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupo tipoAtividadeGrupoOld = this.tipoAtividadeGrupo;
		this.tipoAtividadeGrupo = tipoAtividadeGrupo;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAtividadeGrupo", tipoAtividadeGrupoOld, tipoAtividadeGrupo);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupoCbo)) return false;
		else {
			br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupoCbo tipoAtividadeGrupoCbo = (br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupoCbo) obj;
			if (null == this.getCodigo() || null == tipoAtividadeGrupoCbo.getCodigo()) return false;
			else return (this.getCodigo().equals(tipoAtividadeGrupoCbo.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}
