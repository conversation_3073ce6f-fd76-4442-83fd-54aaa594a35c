package br.com.ksisolucoes.vo.vigilancia.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the eventos_vigilancia_anexo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="eventos_vigilancia_anexo"
 */

public abstract class BaseEventosVigilanciaAnexo extends BaseRootVO implements Serializable {

	public static String REF = "EventosVigilanciaAnexo";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_EVENTOS_VIGILANCIA = "eventosVigilancia";
	public static final String PROP_GERENCIADOR_ARQUIVO = "gerenciadorArquivo";


	// constructors
	public BaseEventosVigilanciaAnexo () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEventosVigilanciaAnexo (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseEventosVigilanciaAnexo (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.EventosVigilancia eventosVigilancia,
		br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo gerenciadorArquivo,
		java.lang.String descricao) {

		this.setCodigo(codigo);
		this.setEventosVigilancia(eventosVigilancia);
		this.setGerenciadorArquivo(gerenciadorArquivo);
		this.setDescricao(descricao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.EventosVigilancia eventosVigilancia;
	private br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo gerenciadorArquivo;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_eventos_vigilancia_anexo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: descricao
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: descricao
	 * @param descricao the descricao value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: cd_eventos_vigilancia
	 */
	public br.com.ksisolucoes.vo.vigilancia.EventosVigilancia getEventosVigilancia () {
		return getPropertyValue(this, eventosVigilancia, PROP_EVENTOS_VIGILANCIA); 
	}

	/**
	 * Set the value related to the column: cd_eventos_vigilancia
	 * @param eventosVigilancia the cd_eventos_vigilancia value
	 */
	public void setEventosVigilancia (br.com.ksisolucoes.vo.vigilancia.EventosVigilancia eventosVigilancia) {
//        br.com.ksisolucoes.vo.vigilancia.EventosVigilancia eventosVigilanciaOld = this.eventosVigilancia;
		this.eventosVigilancia = eventosVigilancia;
//        this.getPropertyChangeSupport().firePropertyChange ("eventosVigilancia", eventosVigilanciaOld, eventosVigilancia);
	}



	/**
	 * Return the value associated with the column: cd_gerenciador_arquivo
	 */
	public br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo getGerenciadorArquivo () {
		return getPropertyValue(this, gerenciadorArquivo, PROP_GERENCIADOR_ARQUIVO); 
	}

	/**
	 * Set the value related to the column: cd_gerenciador_arquivo
	 * @param gerenciadorArquivo the cd_gerenciador_arquivo value
	 */
	public void setGerenciadorArquivo (br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo gerenciadorArquivo) {
//        br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo gerenciadorArquivoOld = this.gerenciadorArquivo;
		this.gerenciadorArquivo = gerenciadorArquivo;
//        this.getPropertyChangeSupport().firePropertyChange ("gerenciadorArquivo", gerenciadorArquivoOld, gerenciadorArquivo);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.EventosVigilanciaAnexo)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.EventosVigilanciaAnexo eventosVigilanciaAnexo = (br.com.ksisolucoes.vo.vigilancia.EventosVigilanciaAnexo) obj;
			if (null == this.getCodigo() || null == eventosVigilanciaAnexo.getCodigo()) return false;
			else return (this.getCodigo().equals(eventosVigilanciaAnexo.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}