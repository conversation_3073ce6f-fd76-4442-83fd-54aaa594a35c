package br.com.ksisolucoes.vo.agendamento;

import java.io.Serializable;

import br.com.ksisolucoes.vo.agendamento.base.BaseUnidadeCotaPpi;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class UnidadeCotaPpi extends BaseUnidadeCotaPpi implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public UnidadeCotaPpi () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public UnidadeCotaPpi (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public UnidadeCotaPpi (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		br.com.ksisolucoes.vo.prontuario.basico.TipoExame tipoExame,
		java.lang.Double tetoFisico,
		java.lang.Double tetoFinanceiro) {

		super (
			codigo,
			empresa,
			tipoExame,
			tetoFisico,
			tetoFinanceiro);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}