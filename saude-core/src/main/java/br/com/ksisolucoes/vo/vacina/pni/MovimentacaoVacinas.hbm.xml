<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vacina.pni"  >

    <class name="MovimentacaoVacinas" table="movimentacao_vacinas">

        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_movimentacao_vac"
        >
            <generator class="assigned" />
        </id> 
        
        <version column="version" name="version" type="long" />

		<property
            column="dt_competencia"
            name="dataCompetencia"
            type="timestamp"
            not-null="true"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.basico.Empresa"
            column="empresa"
            name="empresa"
            not-null="true"
        />
        
        <property
            column="status"
            name="status"
            type="java.lang.Long"
            not-null="true"
        />

        <property
            column="dt_cadastro"
            name="dataCadastro"
            type="timestamp"
            not-null="true"
        />
        
        <property
            column="dt_usuario"
            name="dataUsuario"
            type="timestamp"
            not-null="true"
        />

		<many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuario"
        />
    </class>
</hibernate-mapping>
