package br.com.ksisolucoes.vo.cadsus.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the profissional_sem_vinculo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="profissional_sem_vinculo"
 */

public abstract class BaseProfissionalSemVinculo extends BaseRootVO implements Serializable {

	public static String REF = "ProfissionalSemVinculo";
	public static final String PROP_STATUS = "status";
	public static final String PROP_NOME_PROFISSIONAL = "nomeProfissional";
	public static final String PROP_UF_CONSELHO = "ufConselho";
	public static final String PROP_DAT_CADASTRO = "datCadastro";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_NUMERO_CONSELHO_CLASSE = "numeroConselhoClasse";
	public static final String PROP_CONSELHO_CLASSE = "conselhoClasse";


	// constructors
	public BaseProfissionalSemVinculo () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseProfissionalSemVinculo (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseProfissionalSemVinculo (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.OrgaoEmissor conselhoClasse,
		java.lang.String nomeProfissional,
		java.lang.String ufConselho,
		java.lang.String numeroConselhoClasse,
		java.lang.Long status,
		java.util.Date datCadastro) {

		this.setCodigo(codigo);
		this.setConselhoClasse(conselhoClasse);
		this.setNomeProfissional(nomeProfissional);
		this.setUfConselho(ufConselho);
		this.setNumeroConselhoClasse(numeroConselhoClasse);
		this.setStatus(status);
		this.setDatCadastro(datCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String nomeProfissional;
	private java.lang.String ufConselho;
	private java.lang.String numeroConselhoClasse;
	private java.lang.Long status;
	private java.util.Date datCadastro;

	// many to one
	private br.com.ksisolucoes.vo.basico.OrgaoEmissor conselhoClasse;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_profissional_sem_vinculo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: nm_profissional
	 */
	public java.lang.String getNomeProfissional () {
		return getPropertyValue(this, nomeProfissional, PROP_NOME_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: nm_profissional
	 * @param nomeProfissional the nm_profissional value
	 */
	public void setNomeProfissional (java.lang.String nomeProfissional) {
//        java.lang.String nomeProfissionalOld = this.nomeProfissional;
		this.nomeProfissional = nomeProfissional;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeProfissional", nomeProfissionalOld, nomeProfissional);
	}



	/**
	 * Return the value associated with the column: uf_conselho_classe
	 */
	public java.lang.String getUfConselho () {
		return getPropertyValue(this, ufConselho, PROP_UF_CONSELHO); 
	}

	/**
	 * Set the value related to the column: uf_conselho_classe
	 * @param ufConselho the uf_conselho_classe value
	 */
	public void setUfConselho (java.lang.String ufConselho) {
//        java.lang.String ufConselhoOld = this.ufConselho;
		this.ufConselho = ufConselho;
//        this.getPropertyChangeSupport().firePropertyChange ("ufConselho", ufConselhoOld, ufConselho);
	}



	/**
	 * Return the value associated with the column: nr_conselho
	 */
	public java.lang.String getNumeroConselhoClasse () {
		return getPropertyValue(this, numeroConselhoClasse, PROP_NUMERO_CONSELHO_CLASSE); 
	}

	/**
	 * Set the value related to the column: nr_conselho
	 * @param numeroConselhoClasse the nr_conselho value
	 */
	public void setNumeroConselhoClasse (java.lang.String numeroConselhoClasse) {
//        java.lang.String numeroConselhoClasseOld = this.numeroConselhoClasse;
		this.numeroConselhoClasse = numeroConselhoClasse;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroConselhoClasse", numeroConselhoClasseOld, numeroConselhoClasse);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDatCadastro () {
		return getPropertyValue(this, datCadastro, PROP_DAT_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param datCadastro the dt_cadastro value
	 */
	public void setDatCadastro (java.util.Date datCadastro) {
//        java.util.Date datCadastroOld = this.datCadastro;
		this.datCadastro = datCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("datCadastro", datCadastroOld, datCadastro);
	}



	/**
	 * Return the value associated with the column: cd_conselho_classe
	 */
	public br.com.ksisolucoes.vo.basico.OrgaoEmissor getConselhoClasse () {
		return getPropertyValue(this, conselhoClasse, PROP_CONSELHO_CLASSE); 
	}

	/**
	 * Set the value related to the column: cd_conselho_classe
	 * @param conselhoClasse the cd_conselho_classe value
	 */
	public void setConselhoClasse (br.com.ksisolucoes.vo.basico.OrgaoEmissor conselhoClasse) {
//        br.com.ksisolucoes.vo.basico.OrgaoEmissor conselhoClasseOld = this.conselhoClasse;
		this.conselhoClasse = conselhoClasse;
//        this.getPropertyChangeSupport().firePropertyChange ("conselhoClasse", conselhoClasseOld, conselhoClasse);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.cadsus.ProfissionalSemVinculo)) return false;
		else {
			br.com.ksisolucoes.vo.cadsus.ProfissionalSemVinculo profissionalSemVinculo = (br.com.ksisolucoes.vo.cadsus.ProfissionalSemVinculo) obj;
			if (null == this.getCodigo() || null == profissionalSemVinculo.getCodigo()) return false;
			else return (this.getCodigo().equals(profissionalSemVinculo.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}