<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vacina"  >
	<class name="TipoVacinaInsumo" table="tipo_vacina_insumo" >

        <id
            column="cd_tipo_vacina_insumo"
            name="codigo"
            type="java.lang.Long"
            >
            <generator class="assigned" />
        </id> <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.vacina.TipoVacina"
            column="cd_tipo_vacina"
            name="tipoVacina"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.entradas.estoque.Produto"
            column="cod_pro"
            name="produto"
            not-null="true"
        />
        
        <property
            column="quantidade"
            name="quantidade"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property
            column="idade_minima"
            name="idadeMinima"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property
            column="idade_maxima"
            name="idadeMaxima"
            type="java.lang.Long"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            name="usuario"
            not-null="true"
        >
            <column name="cd_usuario" />
        </many-to-one>
        
        <property
            column="dt_cadastro"
            name="dataCadastro"
            not-null="true"
            type="java.util.Date"
        />
        
        <property
            column="flag_tipo_idade"
            name="flagTipoIdade"
            type="java.lang.Long"
            not-null="false"
        />
        
	</class>
</hibernate-mapping>
