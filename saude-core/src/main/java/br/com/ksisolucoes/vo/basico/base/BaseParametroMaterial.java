package br.com.ksisolucoes.vo.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the parametro_material table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="parametro_material"
 */

public abstract class BaseParametroMaterial extends BaseRootVO implements Serializable {

	public static String REF = "ParametroMaterial";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TIPO_DOCUMENTO_EXTORNO_PEDIDO = "tipoDocumentoExtornoPedido";
	public static final String PROP_TIPO_DOCUMENTO_ENTRADA_ESTADUAL = "tipoDocumentoEntradaEstadual";
	public static final String PROP_TIPO_DOCUMENTO_SAIDA_JUDICIAL = "tipoDocumentoSaidaJudicial";
	public static final String PROP_TIPO_DOCUMENTO_ENTRADA_JUDICIAL = "tipoDocumentoEntradaJudicial";
	public static final String PROP_TIPO_DOCUMENTO_SAIDA_ESTADUAL = "tipoDocumentoSaidaEstadual";
	public static final String PROP_CENTRO_CUSTO_SAIDA_SOLICITADA = "centroCustoSaidaSolicitada";


	// constructors
	public BaseParametroMaterial () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseParametroMaterial (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// many to one
	private br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoExtornoPedido;
	private br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoSaidaJudicial;
	private br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoEntradaJudicial;
	private br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoEntradaEstadual;
	private br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoSaidaEstadual;
	private br.com.ksisolucoes.vo.entradas.estoque.CentroCusto centroCustoSaidaSolicitada;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cod_parametro"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cod_tip_doc_ext_ped
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento getTipoDocumentoExtornoPedido () {
		return getPropertyValue(this, tipoDocumentoExtornoPedido, PROP_TIPO_DOCUMENTO_EXTORNO_PEDIDO); 
	}

	/**
	 * Set the value related to the column: cod_tip_doc_ext_ped
	 * @param tipoDocumentoExtornoPedido the cod_tip_doc_ext_ped value
	 */
	public void setTipoDocumentoExtornoPedido (br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoExtornoPedido) {
//        br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoExtornoPedidoOld = this.tipoDocumentoExtornoPedido;
		this.tipoDocumentoExtornoPedido = tipoDocumentoExtornoPedido;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDocumentoExtornoPedido", tipoDocumentoExtornoPedidoOld, tipoDocumentoExtornoPedido);
	}



	/**
	 * Return the value associated with the column: cod_tip_doc_sai_jud
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento getTipoDocumentoSaidaJudicial () {
		return getPropertyValue(this, tipoDocumentoSaidaJudicial, PROP_TIPO_DOCUMENTO_SAIDA_JUDICIAL); 
	}

	/**
	 * Set the value related to the column: cod_tip_doc_sai_jud
	 * @param tipoDocumentoSaidaJudicial the cod_tip_doc_sai_jud value
	 */
	public void setTipoDocumentoSaidaJudicial (br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoSaidaJudicial) {
//        br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoSaidaJudicialOld = this.tipoDocumentoSaidaJudicial;
		this.tipoDocumentoSaidaJudicial = tipoDocumentoSaidaJudicial;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDocumentoSaidaJudicial", tipoDocumentoSaidaJudicialOld, tipoDocumentoSaidaJudicial);
	}



	/**
	 * Return the value associated with the column: cod_tip_doc_ent_jud
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento getTipoDocumentoEntradaJudicial () {
		return getPropertyValue(this, tipoDocumentoEntradaJudicial, PROP_TIPO_DOCUMENTO_ENTRADA_JUDICIAL); 
	}

	/**
	 * Set the value related to the column: cod_tip_doc_ent_jud
	 * @param tipoDocumentoEntradaJudicial the cod_tip_doc_ent_jud value
	 */
	public void setTipoDocumentoEntradaJudicial (br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoEntradaJudicial) {
//        br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoEntradaJudicialOld = this.tipoDocumentoEntradaJudicial;
		this.tipoDocumentoEntradaJudicial = tipoDocumentoEntradaJudicial;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDocumentoEntradaJudicial", tipoDocumentoEntradaJudicialOld, tipoDocumentoEntradaJudicial);
	}



	/**
	 * Return the value associated with the column: cod_tip_doc_ent_est
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento getTipoDocumentoEntradaEstadual () {
		return getPropertyValue(this, tipoDocumentoEntradaEstadual, PROP_TIPO_DOCUMENTO_ENTRADA_ESTADUAL); 
	}

	/**
	 * Set the value related to the column: cod_tip_doc_ent_est
	 * @param tipoDocumentoEntradaEstadual the cod_tip_doc_ent_est value
	 */
	public void setTipoDocumentoEntradaEstadual (br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoEntradaEstadual) {
//        br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoEntradaEstadualOld = this.tipoDocumentoEntradaEstadual;
		this.tipoDocumentoEntradaEstadual = tipoDocumentoEntradaEstadual;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDocumentoEntradaEstadual", tipoDocumentoEntradaEstadualOld, tipoDocumentoEntradaEstadual);
	}



	/**
	 * Return the value associated with the column: cod_tip_doc_sai_est
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento getTipoDocumentoSaidaEstadual () {
		return getPropertyValue(this, tipoDocumentoSaidaEstadual, PROP_TIPO_DOCUMENTO_SAIDA_ESTADUAL); 
	}

	/**
	 * Set the value related to the column: cod_tip_doc_sai_est
	 * @param tipoDocumentoSaidaEstadual the cod_tip_doc_sai_est value
	 */
	public void setTipoDocumentoSaidaEstadual (br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoSaidaEstadual) {
//        br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoSaidaEstadualOld = this.tipoDocumentoSaidaEstadual;
		this.tipoDocumentoSaidaEstadual = tipoDocumentoSaidaEstadual;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDocumentoSaidaEstadual", tipoDocumentoSaidaEstadualOld, tipoDocumentoSaidaEstadual);
	}



	/**
	 * Return the value associated with the column: cod_custo_sai_sol
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.CentroCusto getCentroCustoSaidaSolicitada () {
		return getPropertyValue(this, centroCustoSaidaSolicitada, PROP_CENTRO_CUSTO_SAIDA_SOLICITADA); 
	}

	/**
	 * Set the value related to the column: cod_custo_sai_sol
	 * @param centroCustoSaidaSolicitada the cod_custo_sai_sol value
	 */
	public void setCentroCustoSaidaSolicitada (br.com.ksisolucoes.vo.entradas.estoque.CentroCusto centroCustoSaidaSolicitada) {
//        br.com.ksisolucoes.vo.entradas.estoque.CentroCusto centroCustoSaidaSolicitadaOld = this.centroCustoSaidaSolicitada;
		this.centroCustoSaidaSolicitada = centroCustoSaidaSolicitada;
//        this.getPropertyChangeSupport().firePropertyChange ("centroCustoSaidaSolicitada", centroCustoSaidaSolicitadaOld, centroCustoSaidaSolicitada);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.ParametroMaterial)) return false;
		else {
			br.com.ksisolucoes.vo.basico.ParametroMaterial parametroMaterial = (br.com.ksisolucoes.vo.basico.ParametroMaterial) obj;
			if (null == this.getCodigo() || null == parametroMaterial.getCodigo()) return false;
			else return (this.getCodigo().equals(parametroMaterial.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}