package br.com.ksisolucoes.vo.agendamento;

import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.util.VOUtils;

/**
 *
 * <AUTHOR>
 */
public class LoadInterceptorExistsAgendamento implements LoadInterceptor {

    private Agenda agenda;

    public LoadInterceptorExistsAgendamento(Agenda agenda) {
        this.agenda = agenda;
    }

    @Override
    public void customHQL(HQLHelper hql, String alias) {
        hql.addToWhereWhithAnd("exists (select 1 from AgendaGradeAtendimento aga "
                + "  left join aga.agendaGrade ag"
                + "  left join ag.agenda a"
                + " where a.codigo = " + agenda.getCodigo()
                + "   and aga.codigo = " + alias + "." + VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO, AgendaGradeAtendimento.PROP_CODIGO)
                + ")");
    }
}
