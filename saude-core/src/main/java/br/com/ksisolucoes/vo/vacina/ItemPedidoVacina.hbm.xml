<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vacina"  >
	<class name="ItemPedidoVacina" table="item_pedido_vacina" >

        <id
            column="cd_it_ped_vacina"
            name="codigo"
            type="java.lang.Long"
            >
            <generator class="assigned" />
        </id> <version column="version" name="version" type="long" />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.vacina.PedidoVacinaInsumo"
            column="cd_ped_vacina"
            name="pedidoVacinaInsumo"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.vacina.TipoVacina"
            column="cd_vacina"
            name="tipoVacina"
            not-null="true"
        />
        
        <property
            column="doses_pedidas"
            name="dosesPedidas"
            type="java.lang.Long"
            not-null="true"
        />

        <property
            column="doses_recebidas"
            name="dosesRecebidas"
            type="java.lang.Long"
        />
        
        <property
            column="doses_canceladas"
            name="dosesCanceladas"
            type="java.lang.Long"
        />
        
        <property
            column="status"
            name="status"
            type="java.lang.Long"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario_can"
            name="usuarioCancelamento"
        />
        
        <property
            column="dt_cancelamento"
            name="dataCancelamento"
            type="java.util.Date"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuario"
            not-null="true"
        />
        
        <property
            column="dt_usuario"
            name="dataUsuario"
            type="java.util.Date"
            not-null="true"
        />
        
        <property
            column="dt_cadastro"
            name="dataCadastro"
            type="java.util.Date"
            not-null="true"
        />
        
        <property
            column="dt_ult_recebimento"
            name="dataUltimoRecebimento"
            type="java.util.Date"
        />
        
	</class>
</hibernate-mapping>
