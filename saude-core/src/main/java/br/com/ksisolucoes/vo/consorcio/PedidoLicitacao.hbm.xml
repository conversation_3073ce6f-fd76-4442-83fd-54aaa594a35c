<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.consorcio"  >
    <class name="PedidoLicitacao" table="pedido_licitacao">
        
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_ped_licitacao"
        >
            <generator class="sequence">
               <param name="sequence">seq_pedido_licitacao</param>
            </generator>
        </id> <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.basico.Empresa"
            column="empresa"
            name="empresa"
            not-null="true"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usu_cadastro"
            name="usuarioCadastro"
            not-null="true"
        />
        
        <property
            column="dt_cadastro"
            name="dataCadastro"
            not-null="true"
            type="java.util.Date"
        />
        
        <property
            column="observacao"
            name="observacao"
            not-null="false"
            type="java.lang.String"
            length="512"
        />

        <property
            column="status"
            name="status"
            type="java.lang.Long"
            not-null="true"
        />
                
    </class>
</hibernate-mapping>
