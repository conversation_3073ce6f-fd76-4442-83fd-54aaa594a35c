package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseDadosClinicos;



public class DadosClinicos extends BaseDadosClinicos implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public DadosClinicos () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public DadosClinicos (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public DadosClinicos (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento,
		br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimento,
		br.com.ksisolucoes.vo.prontuario.basico.Cid cidPrincipal,
		br.com.ksisolucoes.vo.prontuario.basico.ClassificacaoAtendimento classificacaoAtendimento) {

		super (
			codigo,
			atendimento,
			procedimento,
			cidPrincipal,
			classificacaoAtendimento);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}