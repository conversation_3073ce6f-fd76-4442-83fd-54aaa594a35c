package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseExamePrestadorCompetenciaOcorrencia;

import java.io.Serializable;


public class ExamePrestadorCompetenciaOcorrencia extends BaseExamePrestadorCompetenciaOcorrencia implements CodigoManager  {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ExamePrestadorCompetenciaOcorrencia () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ExamePrestadorCompetenciaOcorrencia (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ExamePrestadorCompetenciaOcorrencia (
		java.lang.Long codigo,
		java.lang.String descricao) {

		super (
			codigo,
			descricao);
	}

	/*[CONSTRUCTOR MARKER END]*/

	public void setCodigoManager(Serializable key) {
		this.setCodigo( (java.lang.Long)key );
	}

	public Serializable getCodigoManager() {
		return this.getCodigo();
	}
}