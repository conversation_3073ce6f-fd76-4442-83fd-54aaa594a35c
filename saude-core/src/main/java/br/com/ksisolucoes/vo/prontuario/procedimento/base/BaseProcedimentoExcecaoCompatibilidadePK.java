package br.com.ksisolucoes.vo.prontuario.procedimento.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;


public abstract class BaseProcedimentoExcecaoCompatibilidadePK extends BaseRootVO implements Serializable {

	protected int hashCode = Integer.MIN_VALUE;

	public static String PROP_PROCEDIMENTO_COMPETENCIA_RESTRICAO = "procedimentoCompetenciaRestricao";
	public static String PROP_PROCEDIMENTO_COMPETENCIA_PRINCIPAL = "procedimentoCompetenciaPrincipal";
	public static String PROP_REGISTRO_PRINCIPAL = "registroPrincipal";
	public static String PROP_PROCEDIMENTO_COMPETENCIA_COMPATIVEL = "procedimentoCompetenciaCompativel";
	public static String PROP_REGISTRO_COMPATIVEL = "registroCompativel";

	private br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetenciaRestricao;
	private br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetenciaPrincipal;
	private br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroCadastro registroPrincipal;
	private br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetenciaCompativel;
	private br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroCadastro registroCompativel;


	public BaseProcedimentoExcecaoCompatibilidadePK () {}
	
	public BaseProcedimentoExcecaoCompatibilidadePK (
		br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetenciaRestricao,
		br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetenciaPrincipal,
		br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroCadastro registroPrincipal,
		br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetenciaCompativel,
		br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroCadastro registroCompativel) {

		this.setProcedimentoCompetenciaRestricao(procedimentoCompetenciaRestricao);
		this.setProcedimentoCompetenciaPrincipal(procedimentoCompetenciaPrincipal);
		this.setRegistroPrincipal(registroPrincipal);
		this.setProcedimentoCompetenciaCompativel(procedimentoCompetenciaCompativel);
		this.setRegistroCompativel(registroCompativel);
	}


	/**
	 * Return the value associated with the column: dt_comp_restricao
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia getProcedimentoCompetenciaRestricao () {
		return getPropertyValue(this, procedimentoCompetenciaRestricao, PROP_PROCEDIMENTO_COMPETENCIA_RESTRICAO); 
	}

	/**
	 * Set the value related to the column: dt_comp_restricao
	 * @param procedimentoCompetenciaRestricao the dt_comp_restricao value
	 */
	public void setProcedimentoCompetenciaRestricao (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetenciaRestricao) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetenciaRestricaoOld = this.procedimentoCompetenciaRestricao;
		this.procedimentoCompetenciaRestricao = procedimentoCompetenciaRestricao;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimentoCompetenciaRestricao", procedimentoCompetenciaRestricaoOld, procedimentoCompetenciaRestricao);
	}



	/**
	 * Return the value associated with the column: dt_comp_principal
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia getProcedimentoCompetenciaPrincipal () {
		return getPropertyValue(this, procedimentoCompetenciaPrincipal, PROP_PROCEDIMENTO_COMPETENCIA_PRINCIPAL); 
	}

	/**
	 * Set the value related to the column: dt_comp_principal
	 * @param procedimentoCompetenciaPrincipal the dt_comp_principal value
	 */
	public void setProcedimentoCompetenciaPrincipal (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetenciaPrincipal) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetenciaPrincipalOld = this.procedimentoCompetenciaPrincipal;
		this.procedimentoCompetenciaPrincipal = procedimentoCompetenciaPrincipal;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimentoCompetenciaPrincipal", procedimentoCompetenciaPrincipalOld, procedimentoCompetenciaPrincipal);
	}



	/**
	 * Return the value associated with the column: cd_registro_principal
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroCadastro getRegistroPrincipal () {
		return getPropertyValue(this, registroPrincipal, PROP_REGISTRO_PRINCIPAL); 
	}

	/**
	 * Set the value related to the column: cd_registro_principal
	 * @param registroPrincipal the cd_registro_principal value
	 */
	public void setRegistroPrincipal (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroCadastro registroPrincipal) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroCadastro registroPrincipalOld = this.registroPrincipal;
		this.registroPrincipal = registroPrincipal;
//        this.getPropertyChangeSupport().firePropertyChange ("registroPrincipal", registroPrincipalOld, registroPrincipal);
	}



	/**
	 * Return the value associated with the column: dt_comp_compativel
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia getProcedimentoCompetenciaCompativel () {
		return getPropertyValue(this, procedimentoCompetenciaCompativel, PROP_PROCEDIMENTO_COMPETENCIA_COMPATIVEL); 
	}

	/**
	 * Set the value related to the column: dt_comp_compativel
	 * @param procedimentoCompetenciaCompativel the dt_comp_compativel value
	 */
	public void setProcedimentoCompetenciaCompativel (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetenciaCompativel) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetenciaCompativelOld = this.procedimentoCompetenciaCompativel;
		this.procedimentoCompetenciaCompativel = procedimentoCompetenciaCompativel;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimentoCompetenciaCompativel", procedimentoCompetenciaCompativelOld, procedimentoCompetenciaCompativel);
	}



	/**
	 * Return the value associated with the column: cd_registro_compativel
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroCadastro getRegistroCompativel () {
		return getPropertyValue(this, registroCompativel, PROP_REGISTRO_COMPATIVEL); 
	}

	/**
	 * Set the value related to the column: cd_registro_compativel
	 * @param registroCompativel the cd_registro_compativel value
	 */
	public void setRegistroCompativel (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroCadastro registroCompativel) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroCadastro registroCompativelOld = this.registroCompativel;
		this.registroCompativel = registroCompativel;
//        this.getPropertyChangeSupport().firePropertyChange ("registroCompativel", registroCompativelOld, registroCompativel);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoExcecaoCompatibilidadePK)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoExcecaoCompatibilidadePK mObj = (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoExcecaoCompatibilidadePK) obj;
			if (null != this.getProcedimentoCompetenciaRestricao() && null != mObj.getProcedimentoCompetenciaRestricao()) {
				if (!this.getProcedimentoCompetenciaRestricao().equals(mObj.getProcedimentoCompetenciaRestricao())) {
					return false;
				}
			}
			else {
				return false;
			}
			if (null != this.getProcedimentoCompetenciaPrincipal() && null != mObj.getProcedimentoCompetenciaPrincipal()) {
				if (!this.getProcedimentoCompetenciaPrincipal().equals(mObj.getProcedimentoCompetenciaPrincipal())) {
					return false;
				}
			}
			else {
				return false;
			}
			if (null != this.getRegistroPrincipal() && null != mObj.getRegistroPrincipal()) {
				if (!this.getRegistroPrincipal().equals(mObj.getRegistroPrincipal())) {
					return false;
				}
			}
			else {
				return false;
			}
			if (null != this.getProcedimentoCompetenciaCompativel() && null != mObj.getProcedimentoCompetenciaCompativel()) {
				if (!this.getProcedimentoCompetenciaCompativel().equals(mObj.getProcedimentoCompetenciaCompativel())) {
					return false;
				}
			}
			else {
				return false;
			}
			if (null != this.getRegistroCompativel() && null != mObj.getRegistroCompativel()) {
				if (!this.getRegistroCompativel().equals(mObj.getRegistroCompativel())) {
					return false;
				}
			}
			else {
				return false;
			}
			return true;
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			StringBuilder sb = new StringBuilder();
			if (null != this.getProcedimentoCompetenciaRestricao()) {
				sb.append(this.getProcedimentoCompetenciaRestricao().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			if (null != this.getProcedimentoCompetenciaPrincipal()) {
				sb.append(this.getProcedimentoCompetenciaPrincipal().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			if (null != this.getRegistroPrincipal()) {
				sb.append(this.getRegistroPrincipal().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			if (null != this.getProcedimentoCompetenciaCompativel()) {
				sb.append(this.getProcedimentoCompetenciaCompativel().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			if (null != this.getRegistroCompativel()) {
				sb.append(this.getRegistroCompativel().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			this.hashCode = sb.toString().hashCode();
		}
		return this.hashCode;
	}

    private java.beans.PropertyChangeSupport propertyChangeSupport;

    protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
        if( this.propertyChangeSupport == null ) {
            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
        }
        return this.propertyChangeSupport;
    }

    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.addPropertyChangeListener(l);
    }

    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
		propertyChangeSupport.addPropertyChangeListener(propertyName, listener);
    }

    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.removePropertyChangeListener(l);
    }
}