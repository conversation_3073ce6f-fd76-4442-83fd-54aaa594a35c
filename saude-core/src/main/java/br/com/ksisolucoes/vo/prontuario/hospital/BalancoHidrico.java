package br.com.ksisolucoes.vo.prontuario.hospital;

import java.io.Serializable;

import br.com.ksisolucoes.vo.prontuario.hospital.base.BaseBalancoHidrico;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

public class BalancoHidrico extends BaseBalancoHidrico implements CodigoManager {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public BalancoHidrico () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public BalancoHidrico (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public BalancoHidrico (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento,
		br.com.ksisolucoes.vo.prontuario.hospital.TipoGanhoPerda tipoGanhoPerda,
		br.com.ksisolucoes.vo.cadsus.Profissional profissionalLancamento,
		java.lang.Long tipoTurno,
		java.util.Date dataLancamento,
		java.util.Date dataBalanco,
		java.lang.Double volume,
		java.lang.Long ativo,
		java.util.Date dataCadastro) {

		super (
			codigo,
			atendimento,
			tipoGanhoPerda,
			profissionalLancamento,
			tipoTurno,
			dataLancamento,
			dataBalanco,
			volume,
			ativo,
			dataCadastro);
	}

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getDescricaoTipoTurno() {
        HorariosTurno.Tipo tipo = HorariosTurno.Tipo.valueOf(getTipoTurno());
        if (tipo != null) {
            return tipo.descricao();
        }
        return null;
    }
}
