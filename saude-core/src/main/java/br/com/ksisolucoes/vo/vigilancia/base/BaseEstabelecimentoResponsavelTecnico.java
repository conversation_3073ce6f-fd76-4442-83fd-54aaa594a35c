package br.com.ksisolucoes.vo.vigilancia.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the estabelecimento_responsavel_tecnico table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="estabelecimento_responsavel_tecnico"
 */

public abstract class BaseEstabelecimentoResponsavelTecnico extends BaseRootVO implements Serializable {

	public static String REF = "EstabelecimentoResponsavelTecnico";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_RESPONSAVEL_TECNICO = "responsavelTecnico";
	public static final String PROP_ESTABELECIMENTO = "estabelecimento";


	// constructors
	public BaseEstabelecimentoResponsavelTecnico () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEstabelecimentoResponsavelTecnico (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseEstabelecimentoResponsavelTecnico (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimento,
		br.com.ksisolucoes.vo.vigilancia.ResponsavelTecnico responsavelTecnico) {

		this.setCodigo(codigo);
		this.setEstabelecimento(estabelecimento);
		this.setResponsavelTecnico(responsavelTecnico);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimento;
	private br.com.ksisolucoes.vo.vigilancia.ResponsavelTecnico responsavelTecnico;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_estabelecimento_responsavel"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_estabelecimento
	 */
	public br.com.ksisolucoes.vo.vigilancia.Estabelecimento getEstabelecimento () {
		return getPropertyValue(this, estabelecimento, PROP_ESTABELECIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_estabelecimento
	 * @param estabelecimento the cd_estabelecimento value
	 */
	public void setEstabelecimento (br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimento) {
//        br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimentoOld = this.estabelecimento;
		this.estabelecimento = estabelecimento;
//        this.getPropertyChangeSupport().firePropertyChange ("estabelecimento", estabelecimentoOld, estabelecimento);
	}



	/**
	 * Return the value associated with the column: cd_resp_tecnico
	 */
	public br.com.ksisolucoes.vo.vigilancia.ResponsavelTecnico getResponsavelTecnico () {
		return getPropertyValue(this, responsavelTecnico, PROP_RESPONSAVEL_TECNICO); 
	}

	/**
	 * Set the value related to the column: cd_resp_tecnico
	 * @param responsavelTecnico the cd_resp_tecnico value
	 */
	public void setResponsavelTecnico (br.com.ksisolucoes.vo.vigilancia.ResponsavelTecnico responsavelTecnico) {
//        br.com.ksisolucoes.vo.vigilancia.ResponsavelTecnico responsavelTecnicoOld = this.responsavelTecnico;
		this.responsavelTecnico = responsavelTecnico;
//        this.getPropertyChangeSupport().firePropertyChange ("responsavelTecnico", responsavelTecnicoOld, responsavelTecnico);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.EstabelecimentoResponsavelTecnico)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.EstabelecimentoResponsavelTecnico estabelecimentoResponsavelTecnico = (br.com.ksisolucoes.vo.vigilancia.EstabelecimentoResponsavelTecnico) obj;
			if (null == this.getCodigo() || null == estabelecimentoResponsavelTecnico.getCodigo()) return false;
			else return (this.getCodigo().equals(estabelecimentoResponsavelTecnico.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}