package br.com.ksisolucoes.vo.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the empresa_bpa table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="empresa_bpa"
 */

public abstract class BaseEmpresaBpa extends BaseRootVO implements Serializable {

	public static String REF = "EmpresaBpa";
	public static final String PROP_ORIGEM_DADOS = "origemDados";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_SEQUENCE_LOTE_BPA = "sequenceLoteBpa";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_GERA_BPA = "geraBpa";


	// constructors
	public BaseEmpresaBpa () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEmpresaBpa (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String geraBpa;
	private java.lang.Long sequenceLoteBpa;
	private java.lang.Long origemDados;

	// one to one
	private br.com.ksisolucoes.vo.basico.Empresa empresa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="empresa"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: gera_bpa
	 */
	public java.lang.String getGeraBpa () {
		return getPropertyValue(this, geraBpa, PROP_GERA_BPA); 
	}

	/**
	 * Set the value related to the column: gera_bpa
	 * @param geraBpa the gera_bpa value
	 */
	public void setGeraBpa (java.lang.String geraBpa) {
//        java.lang.String geraBpaOld = this.geraBpa;
		this.geraBpa = geraBpa;
//        this.getPropertyChangeSupport().firePropertyChange ("geraBpa", geraBpaOld, geraBpa);
	}



	/**
	 * Return the value associated with the column: seq_lote_bpa
	 */
	public java.lang.Long getSequenceLoteBpa () {
		return getPropertyValue(this, sequenceLoteBpa, PROP_SEQUENCE_LOTE_BPA); 
	}

	/**
	 * Set the value related to the column: seq_lote_bpa
	 * @param sequenceLoteBpa the seq_lote_bpa value
	 */
	public void setSequenceLoteBpa (java.lang.Long sequenceLoteBpa) {
//        java.lang.Long sequenceLoteBpaOld = this.sequenceLoteBpa;
		this.sequenceLoteBpa = sequenceLoteBpa;
//        this.getPropertyChangeSupport().firePropertyChange ("sequenceLoteBpa", sequenceLoteBpaOld, sequenceLoteBpa);
	}



	/**
	 * Return the value associated with the column: origem_dados
	 */
	public java.lang.Long getOrigemDados () {
		return getPropertyValue(this, origemDados, PROP_ORIGEM_DADOS); 
	}

	/**
	 * Set the value related to the column: origem_dados
	 * @param origemDados the origem_dados value
	 */
	public void setOrigemDados (java.lang.Long origemDados) {
//        java.lang.Long origemDadosOld = this.origemDados;
		this.origemDados = origemDados;
//        this.getPropertyChangeSupport().firePropertyChange ("origemDados", origemDadosOld, origemDados);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.EmpresaBpa)) return false;
		else {
			br.com.ksisolucoes.vo.basico.EmpresaBpa empresaBpa = (br.com.ksisolucoes.vo.basico.EmpresaBpa) obj;
			if (null == this.getCodigo() || null == empresaBpa.getCodigo()) return false;
			else return (this.getCodigo().equals(empresaBpa.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}