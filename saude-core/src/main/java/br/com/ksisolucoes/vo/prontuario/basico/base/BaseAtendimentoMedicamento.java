package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the atendimento_medicamento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="atendimento_medicamento"
 */

public abstract class BaseAtendimentoMedicamento extends BaseRootVO implements Serializable {

	public static String REF = "AtendimentoMedicamento";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_CUIDADOS = "cuidados";
	public static final String PROP_RECEITUARIO_ITEM = "receituarioItem";
	public static final String PROP_ATENDIMENTO = "atendimento";
	public static final String PROP_DATA_HORA_APLICACAO = "dataHoraAplicacao";


	// constructors
	public BaseAtendimentoMedicamento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAtendimentoMedicamento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseAtendimentoMedicamento (
		java.lang.Long codigo,
		java.util.Date dataHoraAplicacao) {

		this.setCodigo(codigo);
		this.setDataHoraAplicacao(dataHoraAplicacao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataHoraAplicacao;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento;
	private br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem receituarioItem;
	private br.com.ksisolucoes.vo.prontuario.basico.Cuidados cuidados;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_atendimento_medicamento"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: data_hora_aplicacao
	 */
	public java.util.Date getDataHoraAplicacao () {
		return getPropertyValue(this, dataHoraAplicacao, PROP_DATA_HORA_APLICACAO); 
	}

	/**
	 * Set the value related to the column: data_hora_aplicacao
	 * @param dataHoraAplicacao the data_hora_aplicacao value
	 */
	public void setDataHoraAplicacao (java.util.Date dataHoraAplicacao) {
//        java.util.Date dataHoraAplicacaoOld = this.dataHoraAplicacao;
		this.dataHoraAplicacao = dataHoraAplicacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataHoraAplicacao", dataHoraAplicacaoOld, dataHoraAplicacao);
	}



	/**
	 * Return the value associated with the column: nr_atendimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Atendimento getAtendimento () {
		return getPropertyValue(this, atendimento, PROP_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: nr_atendimento
	 * @param atendimento the nr_atendimento value
	 */
	public void setAtendimento (br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoOld = this.atendimento;
		this.atendimento = atendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimento", atendimentoOld, atendimento);
	}



	/**
	 * Return the value associated with the column: cd_receiturario_item
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem getReceituarioItem () {
		return getPropertyValue(this, receituarioItem, PROP_RECEITUARIO_ITEM); 
	}

	/**
	 * Set the value related to the column: cd_receiturario_item
	 * @param receituarioItem the cd_receiturario_item value
	 */
	public void setReceituarioItem (br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem receituarioItem) {
//        br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem receituarioItemOld = this.receituarioItem;
		this.receituarioItem = receituarioItem;
//        this.getPropertyChangeSupport().firePropertyChange ("receituarioItem", receituarioItemOld, receituarioItem);
	}



	/**
	 * Return the value associated with the column: cd_cuidado
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Cuidados getCuidados () {
		return getPropertyValue(this, cuidados, PROP_CUIDADOS); 
	}

	/**
	 * Set the value related to the column: cd_cuidado
	 * @param cuidados the cd_cuidado value
	 */
	public void setCuidados (br.com.ksisolucoes.vo.prontuario.basico.Cuidados cuidados) {
//        br.com.ksisolucoes.vo.prontuario.basico.Cuidados cuidadosOld = this.cuidados;
		this.cuidados = cuidados;
//        this.getPropertyChangeSupport().firePropertyChange ("cuidados", cuidadosOld, cuidados);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.AtendimentoMedicamento)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.AtendimentoMedicamento atendimentoMedicamento = (br.com.ksisolucoes.vo.prontuario.basico.AtendimentoMedicamento) obj;
			if (null == this.getCodigo() || null == atendimentoMedicamento.getCodigo()) return false;
			else return (this.getCodigo().equals(atendimentoMedicamento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}