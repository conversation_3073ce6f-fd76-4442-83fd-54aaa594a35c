package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the exame_cbo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="exame_cbo"
 */

public abstract class BaseExameCbo extends BaseRootVO implements Serializable {

	public static String REF = "ExameCbo";
	public static final String PROP_TETO_FINANCEIRO_VALOR = "tetoFinanceiroValor";
	public static final String PROP_TABELA_CBO = "tabelaCbo";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TETO_FINANCEIRO_PERCENTUAL = "tetoFinanceiroPercentual";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_TIPO_EXAME = "tipoExame";


	// constructors
	public BaseExameCbo () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseExameCbo (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseExameCbo (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCbo) {

		this.setCodigo(codigo);
		this.setEmpresa(empresa);
		this.setTabelaCbo(tabelaCbo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Double tetoFinanceiroPercentual;
	private java.lang.Double tetoFinanceiroValor;

	// many to one
	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private br.com.ksisolucoes.vo.prontuario.basico.TipoExame tipoExame;
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCbo;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_exame_cbo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: teto_financeiro_perc
	 */
	public java.lang.Double getTetoFinanceiroPercentual () {
		return getPropertyValue(this, tetoFinanceiroPercentual, PROP_TETO_FINANCEIRO_PERCENTUAL); 
	}

	/**
	 * Set the value related to the column: teto_financeiro_perc
	 * @param tetoFinanceiroPercentual the teto_financeiro_perc value
	 */
	public void setTetoFinanceiroPercentual (java.lang.Double tetoFinanceiroPercentual) {
//        java.lang.Double tetoFinanceiroPercentualOld = this.tetoFinanceiroPercentual;
		this.tetoFinanceiroPercentual = tetoFinanceiroPercentual;
//        this.getPropertyChangeSupport().firePropertyChange ("tetoFinanceiroPercentual", tetoFinanceiroPercentualOld, tetoFinanceiroPercentual);
	}



	/**
	 * Return the value associated with the column: teto_financeiro_vlr
	 */
	public java.lang.Double getTetoFinanceiroValor () {
		return getPropertyValue(this, tetoFinanceiroValor, PROP_TETO_FINANCEIRO_VALOR); 
	}

	/**
	 * Set the value related to the column: teto_financeiro_vlr
	 * @param tetoFinanceiroValor the teto_financeiro_vlr value
	 */
	public void setTetoFinanceiroValor (java.lang.Double tetoFinanceiroValor) {
//        java.lang.Double tetoFinanceiroValorOld = this.tetoFinanceiroValor;
		this.tetoFinanceiroValor = tetoFinanceiroValor;
//        this.getPropertyChangeSupport().firePropertyChange ("tetoFinanceiroValor", tetoFinanceiroValorOld, tetoFinanceiroValor);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: cd_tp_exame
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.TipoExame getTipoExame () {
		return getPropertyValue(this, tipoExame, PROP_TIPO_EXAME); 
	}

	/**
	 * Set the value related to the column: cd_tp_exame
	 * @param tipoExame the cd_tp_exame value
	 */
	public void setTipoExame (br.com.ksisolucoes.vo.prontuario.basico.TipoExame tipoExame) {
//        br.com.ksisolucoes.vo.prontuario.basico.TipoExame tipoExameOld = this.tipoExame;
		this.tipoExame = tipoExame;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoExame", tipoExameOld, tipoExame);
	}



	/**
	 * Return the value associated with the column: cd_cbo
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getTabelaCbo () {
		return getPropertyValue(this, tabelaCbo, PROP_TABELA_CBO); 
	}

	/**
	 * Set the value related to the column: cd_cbo
	 * @param tabelaCbo the cd_cbo value
	 */
	public void setTabelaCbo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCbo) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCboOld = this.tabelaCbo;
		this.tabelaCbo = tabelaCbo;
//        this.getPropertyChangeSupport().firePropertyChange ("tabelaCbo", tabelaCboOld, tabelaCbo);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.ExameCbo)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.ExameCbo exameCbo = (br.com.ksisolucoes.vo.prontuario.basico.ExameCbo) obj;
			if (null == this.getCodigo() || null == exameCbo.getCodigo()) return false;
			else return (this.getCodigo().equals(exameCbo.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}