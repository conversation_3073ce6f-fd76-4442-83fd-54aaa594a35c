package br.com.ksisolucoes.vo.agendamento.tfd.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the pedido_tfd_passageiro table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="pedido_tfd_passageiro"
 */

public abstract class BasePedidoTfdPassageiro extends BaseRootVO implements Serializable {

	public static String REF = "PedidoTfdPassageiro";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TIPO_PASSAGEIRO = "tipoPassageiro";
	public static final String PROP_PEDIDO_TFD = "pedidoTfd";
	public static final String PROP_SOLICITACAO_VIAGEM = "solicitacaoViagem";
	public static final String PROP_LOCAL_EMBARQUE = "localEmbarque";
	public static final String PROP_TIPO_TRANSPORTE_VIAGEM = "tipoTransporteViagem";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_OBSERVACAO = "observacao";


	// constructors
	public BasePedidoTfdPassageiro () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BasePedidoTfdPassageiro (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long tipoPassageiro;
	private java.lang.String observacao;
	private java.lang.String localEmbarque;

	// many to one
	private br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd pedidoTfd;
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private br.com.ksisolucoes.vo.frota.viagem.TipoTransporteViagem tipoTransporteViagem;
	private br.com.ksisolucoes.vo.frota.viagem.solicitacao.SolicitacaoViagem solicitacaoViagem;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_pedido_passageiro"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: tp_passageiro
	 */
	public java.lang.Long getTipoPassageiro () {
		return getPropertyValue(this, tipoPassageiro, PROP_TIPO_PASSAGEIRO); 
	}

	/**
	 * Set the value related to the column: tp_passageiro
	 * @param tipoPassageiro the tp_passageiro value
	 */
	public void setTipoPassageiro (java.lang.Long tipoPassageiro) {
//        java.lang.Long tipoPassageiroOld = this.tipoPassageiro;
		this.tipoPassageiro = tipoPassageiro;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoPassageiro", tipoPassageiroOld, tipoPassageiro);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: local_embarque
	 */
	public java.lang.String getLocalEmbarque () {
		return getPropertyValue(this, localEmbarque, PROP_LOCAL_EMBARQUE); 
	}

	/**
	 * Set the value related to the column: local_embarque
	 * @param localEmbarque the local_embarque value
	 */
	public void setLocalEmbarque (java.lang.String localEmbarque) {
//        java.lang.String localEmbarqueOld = this.localEmbarque;
		this.localEmbarque = localEmbarque;
//        this.getPropertyChangeSupport().firePropertyChange ("localEmbarque", localEmbarqueOld, localEmbarque);
	}



	/**
	 * Return the value associated with the column: cd_pedido_tfd
	 */
	public br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd getPedidoTfd () {
		return getPropertyValue(this, pedidoTfd, PROP_PEDIDO_TFD); 
	}

	/**
	 * Set the value related to the column: cd_pedido_tfd
	 * @param pedidoTfd the cd_pedido_tfd value
	 */
	public void setPedidoTfd (br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd pedidoTfd) {
//        br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd pedidoTfdOld = this.pedidoTfd;
		this.pedidoTfd = pedidoTfd;
//        this.getPropertyChangeSupport().firePropertyChange ("pedidoTfd", pedidoTfdOld, pedidoTfd);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cd_tp_transporte
	 */
	public br.com.ksisolucoes.vo.frota.viagem.TipoTransporteViagem getTipoTransporteViagem () {
		return getPropertyValue(this, tipoTransporteViagem, PROP_TIPO_TRANSPORTE_VIAGEM); 
	}

	/**
	 * Set the value related to the column: cd_tp_transporte
	 * @param tipoTransporteViagem the cd_tp_transporte value
	 */
	public void setTipoTransporteViagem (br.com.ksisolucoes.vo.frota.viagem.TipoTransporteViagem tipoTransporteViagem) {
//        br.com.ksisolucoes.vo.frota.viagem.TipoTransporteViagem tipoTransporteViagemOld = this.tipoTransporteViagem;
		this.tipoTransporteViagem = tipoTransporteViagem;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoTransporteViagem", tipoTransporteViagemOld, tipoTransporteViagem);
	}



	/**
	 * Return the value associated with the column: cd_solicitacao_viagem
	 */
	public br.com.ksisolucoes.vo.frota.viagem.solicitacao.SolicitacaoViagem getSolicitacaoViagem () {
		return getPropertyValue(this, solicitacaoViagem, PROP_SOLICITACAO_VIAGEM); 
	}

	/**
	 * Set the value related to the column: cd_solicitacao_viagem
	 * @param solicitacaoViagem the cd_solicitacao_viagem value
	 */
	public void setSolicitacaoViagem (br.com.ksisolucoes.vo.frota.viagem.solicitacao.SolicitacaoViagem solicitacaoViagem) {
//        br.com.ksisolucoes.vo.frota.viagem.solicitacao.SolicitacaoViagem solicitacaoViagemOld = this.solicitacaoViagem;
		this.solicitacaoViagem = solicitacaoViagem;
//        this.getPropertyChangeSupport().firePropertyChange ("solicitacaoViagem", solicitacaoViagemOld, solicitacaoViagem);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfdPassageiro)) return false;
		else {
			br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfdPassageiro pedidoTfdPassageiro = (br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfdPassageiro) obj;
			if (null == this.getCodigo() || null == pedidoTfdPassageiro.getCodigo()) return false;
			else return (this.getCodigo().equals(pedidoTfdPassageiro.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}