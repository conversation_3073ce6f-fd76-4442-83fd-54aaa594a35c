package br.com.ksisolucoes.vo.inovamfri.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the inovamfri_oauth table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="inovamfri_oauth"
 */

public abstract class BaseInovamfriOAuth extends BaseRootVO implements Serializable {

	public static String REF = "InovamfriOAuth";
	public static final String PROP_TOKEN = "token";
	public static final String PROP_DATA_HORA_EXPIRACAO = "dataHoraExpiracao";


	// constructors
	public BaseInovamfriOAuth () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInovamfriOAuth (java.lang.String token) {
		this.setToken(token);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseInovamfriOAuth (
		java.lang.String token,
		java.util.Date dataHoraExpiracao) {

		this.setToken(token);
		this.setDataHoraExpiracao(dataHoraExpiracao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.String token;

	// fields
	private java.util.Date dataHoraExpiracao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="token"
     */
	public java.lang.String getToken () {
	    return getPropertyValue(this,  token, "token" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param token the new ID
	 */
	public void setToken (java.lang.String token) {
		this.token = token;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_expiracao
	 */
	public java.util.Date getDataHoraExpiracao () {
		return getPropertyValue(this, dataHoraExpiracao, PROP_DATA_HORA_EXPIRACAO); 
	}

	/**
	 * Set the value related to the column: dt_expiracao
	 * @param dataHoraExpiracao the dt_expiracao value
	 */
	public void setDataHoraExpiracao (java.util.Date dataHoraExpiracao) {
//        java.util.Date dataHoraExpiracaoOld = this.dataHoraExpiracao;
		this.dataHoraExpiracao = dataHoraExpiracao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataHoraExpiracao", dataHoraExpiracaoOld, dataHoraExpiracao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.inovamfri.InovamfriOAuth)) return false;
		else {
			br.com.ksisolucoes.vo.inovamfri.InovamfriOAuth inovamfriOAuth = (br.com.ksisolucoes.vo.inovamfri.InovamfriOAuth) obj;
			if (null == this.getToken() || null == inovamfriOAuth.getToken()) return false;
			else return (this.getToken().equals(inovamfriOAuth.getToken()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getToken()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getToken().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}