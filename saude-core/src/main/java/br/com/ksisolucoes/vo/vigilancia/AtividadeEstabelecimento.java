package br.com.ksisolucoes.vo.vigilancia;

import java.io.Serializable;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import br.com.ksisolucoes.vo.vigilancia.base.BaseAtividadeEstabelecimento;



public class AtividadeEstabelecimento extends BaseAtividadeEstabelecimento implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public AtividadeEstabelecimento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public AtividadeEstabelecimento (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public AtividadeEstabelecimento (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.grupoestabelecimento.GrupoEstabelecimento grupoEstabelecimento,
		java.lang.String descricao) {

		super (
			codigo,
			grupoEstabelecimento,
			descricao);
	}

/*[CONSTRUCTOR MARKER END]*/

    @Override
    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    @Override
    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    @Override
    public String getDescricaoVO() {
        return this.getDescricao();
    }

    @Override
    public String getIdentificador() {
        return this.getCodigo().toString();
    }
}