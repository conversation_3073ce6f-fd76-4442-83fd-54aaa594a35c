package br.com.ksisolucoes.vo.entradas.estoque.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the produtos table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="produtos"
 */

public abstract class BaseProduto extends BaseRootVO implements Serializable {

	public static String REF = "Produto";
	public static final String PROP_FLAG_DISPONIVEL_PEDIDO_LICITACAO = "flagDisponivelPedidoLicitacao";
	public static final String PROP_VELOCIDADE_INFUSAO_VIA_ENDOVENOSA = "velocidadeInfusaoViaEndovenosa";
	public static final String PROP_UNIDADE_PRESCRICAO = "unidadePrescricao";
	public static final String PROP_VIDA_UTIL = "vidaUtil";
	public static final String PROP_LIMITE_DISPENSACAO = "limiteDispensacao";
	public static final String PROP_DATA_ATIVO = "dataAtivo";
	public static final String PROP_FLAG_LISTA_MEDICAMENTO_PUBLICO = "flagListaMedicamentoPublico";
	public static final String PROP_TIPO_PRODUTO_CATMAT = "tipoProdutoCatmat";
	public static final String PROP_DURACAO_TRATAMENTO = "duracaoTratamento";
	public static final String PROP_MEDICAMENTO_CATMAT = "medicamentoCatmat";
	public static final String PROP_MEDICAMENTO_OBM_AMPP = "medicamentoObmAmpp";
	public static final String PROP_UNIDADE_RECEITUARIO = "unidadeReceituario";
	public static final String PROP_UNIDADE = "unidade";
	public static final String PROP_FLAG_PADRONIZADO = "flagPadronizado";
	public static final String PROP_FABRICANTE_ESUS = "fabricanteEsus";
	public static final String PROP_CLASSIFICACAO_CONTABIL = "classificacaoContabil";
	public static final String PROP_FLAG_HIPERTENSAO_ARTERIAL = "flagHipertensaoArterial";
	public static final String PROP_USUARIO_ESTRUTURA = "usuarioEstrutura";
	public static final String PROP_COEFICIENTE_VIDA_UTIL = "coeficienteVidaUtil";
	public static final String PROP_FLAG_FRACIONADO = "flagFracionado";
	public static final String PROP_STATUS_ESTRUTURA = "statusEstrutura";
	public static final String PROP_FLAG_DIABETE = "flagDiabete";
	public static final String PROP_ID_INTEGRACAO_TERCEIRO = "idIntegracaoTerceiro";
	public static final String PROP_FLAG_KIT_MONTAGEM = "flagKitMontagem";
	public static final String PROP_VOLUME_RESTITUICAO = "volumeRestituicao";
	public static final String PROP_NOME_INTEGRACAO_TERCEIRO = "nomeIntegracaoTerceiro";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_POSOLOGIA_MAXIMA_DIARIA = "posologiaMaximaDiaria";
	public static final String PROP_FABRICANTE = "fabricante";
	public static final String PROP_FLAG_CONTROLE_MINIMO = "flagControleMinimo";
	public static final String PROP_CURVA = "curva";
	public static final String PROP_VALIDADE_RECEITA_CONTINUA = "validadeReceitaContinua";
	public static final String PROP_NUMERO_LISTA = "numeroLista";
	public static final String PROP_CONCENTRACAO = "concentracao";
	public static final String PROP_QTDADE_MG_ML = "qtdadeMgMl";
	public static final String PROP_DATA_USUARIO_ALTEROU = "dataUsuarioAlterou";
	public static final String PROP_FLAG_PERMITE_DISPENSAR_MAIS = "flagPermiteDispensarMais";
	public static final String PROP_REVISAO_ESTRUTURA = "revisaoEstrutura";
	public static final String PROP_FLAG_DISPENSACAO_ESPECIAL = "flagDispensacaoEspecial";
	public static final String PROP_LOCAL_FORNECIMENTO = "localFornecimento";
	public static final String PROP_SUB_GRUPO = "subGrupo";
	public static final String PROP_FLAG_EMITE_LME = "flagEmiteLme";
	public static final String PROP_FLAG_TRATAMENTO_PROLONGADO_ANTIBIOTICO = "flagTratamentoProlongadoAntibiotico";
	public static final String PROP_TEMPO_INFUSAO = "tempoInfusao";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_FLAG_BAIXA_ESTOQUE_PROCESSO_ENFERMAGEM = "flagBaixaEstoqueProcessoEnfermagem";
	public static final String PROP_QUANTIDADE_UNIDADE = "quantidadeUnidade";
	public static final String PROP_FLAG_EMPRESTIMO = "flagEmprestimo";
	public static final String PROP_CODIGO_BARRAS = "codigoBarras";
	public static final String PROP_FLAG_EXPORTA_HORUS = "flagExportaHorus";
	public static final String PROP_VOLUME_DILUICAO = "volumeDiluicao";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_CODIGO_DCB = "codigoDcb";
	public static final String PROP_REFERENCIA = "referencia";
	public static final String PROP_QUANTIDADE_POR_EMBALAGEM = "quantidadePorEmbalagem";
	public static final String PROP_FORMA_FARMACEUTICA = "formaFarmaceutica";
	public static final String PROP_ELO_PRODUTO_BRASINDICE = "eloProdutoBrasindice";
	public static final String PROP_CODIGO_MINISTERIO_SAUDE = "codigoMinisterioSaude";
	public static final String PROP_TIPO_RECEITA = "tipoReceita";
	public static final String PROP_CONTEM_DILUENTE = "contemDiluente";
	public static final String PROP_CODIGO_TCE = "codigoTce";
	public static final String PROP_FLAG_ATIVO = "flagAtivo";
	public static final String PROP_DILUENTE = "diluente";
	public static final String PROP_USO_CONTINUO = "usoContinuo";
	public static final String PROP_COMPLEMENTO = "complemento";
	public static final String PROP_CRITICIDADE = "criticidade";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_NOME_DCB = "nomeDcb";
	public static final String PROP_MARCA = "marca";
	public static final String PROP_FLAG_RENAME = "flagRename";
	public static final String PROP_FLAG_HABILITAR_OBM = "flagHabilitarObm";


	// constructors
	public BaseProduto () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseProduto (java.lang.String codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseProduto (
		java.lang.String codigo,
		br.com.ksisolucoes.vo.entradas.estoque.Unidade unidadeReceituario,
		br.com.ksisolucoes.vo.entradas.estoque.Unidade unidade,
		br.com.ksisolucoes.vo.entradas.estoque.SubGrupo subGrupo,
		java.lang.String flagControleMinimo,
		java.lang.Long usuario,
		java.util.Date dataCadastro,
		java.lang.String descricao,
		java.util.Date dataUsuarioAlterou,
		java.lang.String flagPermiteDispensarMais,
		java.lang.Long flagEmprestimo,
		java.lang.Long flagTratamentoProlongadoAntibiotico,
		java.lang.Long flagAtivo,
		java.lang.Long flagDisponivelPedidoLicitacao,
		java.lang.Long flagEmiteLme,
		java.lang.String flagHipertensaoArterial,
		java.lang.String flagDiabete) {

		this.setCodigo(codigo);
		this.setUnidadeReceituario(unidadeReceituario);
		this.setUnidade(unidade);
		this.setSubGrupo(subGrupo);
		this.setFlagControleMinimo(flagControleMinimo);
		this.setUsuario(usuario);
		this.setDataCadastro(dataCadastro);
		this.setDescricao(descricao);
		this.setDataUsuarioAlterou(dataUsuarioAlterou);
		this.setFlagPermiteDispensarMais(flagPermiteDispensarMais);
		this.setFlagEmprestimo(flagEmprestimo);
		this.setFlagTratamentoProlongadoAntibiotico(flagTratamentoProlongadoAntibiotico);
		this.setFlagAtivo(flagAtivo);
		this.setFlagDisponivelPedidoLicitacao(flagDisponivelPedidoLicitacao);
		this.setFlagEmiteLme(flagEmiteLme);
		this.setFlagHipertensaoArterial(flagHipertensaoArterial);
		this.setFlagDiabete(flagDiabete);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.String codigo;

	// fields
	private java.lang.String flagControleMinimo;
	private java.lang.String codigoBarras;
	private java.lang.String curva;
	private java.lang.String criticidade;
	private java.util.Date dataAtivo;
	private java.lang.Long numeroLista;
	private java.lang.Long usuario;
	private java.util.Date dataCadastro;
	private java.lang.String descricao;
	private java.lang.String marca;
	private java.lang.String codigoDcb;
	private java.lang.String nomeDcb;
	private java.lang.String concentracao;
	private java.lang.Double quantidadePorEmbalagem;
	private java.util.Date dataUsuarioAlterou;
	private java.lang.String flagBaixaEstoqueProcessoEnfermagem;
	private java.lang.Long revisaoEstrutura;
	private java.lang.Long statusEstrutura;
	private java.lang.Long limiteDispensacao;
	private java.lang.Long duracaoTratamento;
	private java.lang.String codigoMinisterioSaude;
	private java.lang.Double qtdadeMgMl;
	private java.lang.String usoContinuo;
	private java.lang.String flagKitMontagem;
	private java.lang.String flagDispensacaoEspecial;
	private java.lang.Long validadeReceitaContinua;
	private java.lang.String flagRename;
	private java.lang.String referencia;
	private java.lang.String complemento;
	private java.lang.Long quantidadeUnidade;
	private java.lang.String flagPadronizado;
	private java.lang.Long flagFracionado;
	private java.lang.String flagPermiteDispensarMais;
	private java.lang.String tipoProdutoCatmat;
	private java.lang.Long flagEmprestimo;
	private java.lang.Long posologiaMaximaDiaria;
	private java.lang.Long flagExportaHorus;
	private java.lang.String codigoTce;
	private java.lang.Long flagTratamentoProlongadoAntibiotico;
	private java.lang.Long flagAtivo;
	private java.lang.Long formaFarmaceutica;
	private java.lang.Long flagDisponivelPedidoLicitacao;
	private java.lang.Long contemDiluente;
	private java.lang.Long tempoInfusao;
	private java.lang.Long volumeDiluicao;
	private java.lang.Long volumeRestituicao;
	private java.lang.Long flagEmiteLme;
	private java.lang.Long idIntegracaoTerceiro;
	private java.lang.String nomeIntegracaoTerceiro;
	private java.lang.Long vidaUtil;
	private java.lang.Long coeficienteVidaUtil;
	private java.lang.String fabricanteEsus;
	private java.lang.String flagHipertensaoArterial;
	private java.lang.String flagDiabete;
	private java.lang.Long flagListaMedicamentoPublico;
	private java.lang.Long flagHabilitarObm;

	// one to one
	private br.com.ksisolucoes.vo.entradas.estoque.EloProdutoBrasindice eloProdutoBrasindice;

	// many to one
	private br.com.ksisolucoes.vo.entradas.estoque.FabricanteMedicamento fabricante;
	private br.com.ksisolucoes.vo.entradas.estoque.MedicamentoCatmat medicamentoCatmat;
	private br.com.ksisolucoes.vo.entradas.estoque.MedicamentoObmAmpp medicamentoObmAmpp;
	private br.com.ksisolucoes.vo.prontuario.basico.TipoReceita tipoReceita;
	private br.com.ksisolucoes.vo.entradas.estoque.Unidade unidadeReceituario;
	private br.com.ksisolucoes.vo.entradas.estoque.ClassificacaoContabil classificacaoContabil;
	private br.com.ksisolucoes.vo.prontuario.basico.LocalFornecimento localFornecimento;
	private br.com.ksisolucoes.vo.entradas.estoque.Unidade unidadePrescricao;
	private br.com.ksisolucoes.vo.entradas.estoque.Unidade unidade;
	private br.com.ksisolucoes.vo.entradas.estoque.SubGrupo subGrupo;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioEstrutura;
	private br.com.ksisolucoes.vo.entradas.estoque.Produto diluente;
	private br.com.ksisolucoes.vo.basico.TipoInfusao velocidadeInfusaoViaEndovenosa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cod_pro"
     */
	public java.lang.String getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.String codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cont_min
	 */
	public java.lang.String getFlagControleMinimo () {
		return getPropertyValue(this, flagControleMinimo, PROP_FLAG_CONTROLE_MINIMO); 
	}

	/**
	 * Set the value related to the column: cont_min
	 * @param flagControleMinimo the cont_min value
	 */
	public void setFlagControleMinimo (java.lang.String flagControleMinimo) {
//        java.lang.String flagControleMinimoOld = this.flagControleMinimo;
		this.flagControleMinimo = flagControleMinimo;
//        this.getPropertyChangeSupport().firePropertyChange ("flagControleMinimo", flagControleMinimoOld, flagControleMinimo);
	}



	/**
	 * Return the value associated with the column: ds_cod_barras
	 */
	public java.lang.String getCodigoBarras () {
		return getPropertyValue(this, codigoBarras, PROP_CODIGO_BARRAS); 
	}

	/**
	 * Set the value related to the column: ds_cod_barras
	 * @param codigoBarras the ds_cod_barras value
	 */
	public void setCodigoBarras (java.lang.String codigoBarras) {
//        java.lang.String codigoBarrasOld = this.codigoBarras;
		this.codigoBarras = codigoBarras;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoBarras", codigoBarrasOld, codigoBarras);
	}



	/**
	 * Return the value associated with the column: curva
	 */
	public java.lang.String getCurva () {
		return getPropertyValue(this, curva, PROP_CURVA); 
	}

	/**
	 * Set the value related to the column: curva
	 * @param curva the curva value
	 */
	public void setCurva (java.lang.String curva) {
//        java.lang.String curvaOld = this.curva;
		this.curva = curva;
//        this.getPropertyChangeSupport().firePropertyChange ("curva", curvaOld, curva);
	}



	/**
	 * Return the value associated with the column: criticidade
	 */
	public java.lang.String getCriticidade () {
		return getPropertyValue(this, criticidade, PROP_CRITICIDADE); 
	}

	/**
	 * Set the value related to the column: criticidade
	 * @param criticidade the criticidade value
	 */
	public void setCriticidade (java.lang.String criticidade) {
//        java.lang.String criticidadeOld = this.criticidade;
		this.criticidade = criticidade;
//        this.getPropertyChangeSupport().firePropertyChange ("criticidade", criticidadeOld, criticidade);
	}



	/**
	 * Return the value associated with the column: dt_flag
	 */
	public java.util.Date getDataAtivo () {
		return getPropertyValue(this, dataAtivo, PROP_DATA_ATIVO); 
	}

	/**
	 * Set the value related to the column: dt_flag
	 * @param dataAtivo the dt_flag value
	 */
	public void setDataAtivo (java.util.Date dataAtivo) {
//        java.util.Date dataAtivoOld = this.dataAtivo;
		this.dataAtivo = dataAtivo;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAtivo", dataAtivoOld, dataAtivo);
	}



	/**
	 * Return the value associated with the column: numero_lista
	 */
	public java.lang.Long getNumeroLista () {
		return getPropertyValue(this, numeroLista, PROP_NUMERO_LISTA); 
	}

	/**
	 * Set the value related to the column: numero_lista
	 * @param numeroLista the numero_lista value
	 */
	public void setNumeroLista (java.lang.Long numeroLista) {
//        java.lang.Long numeroListaOld = this.numeroLista;
		this.numeroLista = numeroLista;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroLista", numeroListaOld, numeroLista);
	}



	/**
	 * Return the value associated with the column: usuario
	 */
	public java.lang.Long getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: usuario
	 * @param usuario the usuario value
	 */
	public void setUsuario (java.lang.Long usuario) {
//        java.lang.Long usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: descricao
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: descricao
	 * @param descricao the descricao value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: marca
	 */
	public java.lang.String getMarca () {
		return getPropertyValue(this, marca, PROP_MARCA); 
	}

	/**
	 * Set the value related to the column: marca
	 * @param marca the marca value
	 */
	public void setMarca (java.lang.String marca) {
//        java.lang.String marcaOld = this.marca;
		this.marca = marca;
//        this.getPropertyChangeSupport().firePropertyChange ("marca", marcaOld, marca);
	}



	/**
	 * Return the value associated with the column: codigo_dcb
	 */
	public java.lang.String getCodigoDcb () {
		return getPropertyValue(this, codigoDcb, PROP_CODIGO_DCB); 
	}

	/**
	 * Set the value related to the column: codigo_dcb
	 * @param codigoDcb the codigo_dcb value
	 */
	public void setCodigoDcb (java.lang.String codigoDcb) {
//        java.lang.String codigoDcbOld = this.codigoDcb;
		this.codigoDcb = codigoDcb;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoDcb", codigoDcbOld, codigoDcb);
	}



	/**
	 * Return the value associated with the column: nome_dcb
	 */
	public java.lang.String getNomeDcb () {
		return getPropertyValue(this, nomeDcb, PROP_NOME_DCB); 
	}

	/**
	 * Set the value related to the column: nome_dcb
	 * @param nomeDcb the nome_dcb value
	 */
	public void setNomeDcb (java.lang.String nomeDcb) {
//        java.lang.String nomeDcbOld = this.nomeDcb;
		this.nomeDcb = nomeDcb;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeDcb", nomeDcbOld, nomeDcb);
	}



	/**
	 * Return the value associated with the column: concentracao
	 */
	public java.lang.String getConcentracao () {
		return getPropertyValue(this, concentracao, PROP_CONCENTRACAO); 
	}

	/**
	 * Set the value related to the column: concentracao
	 * @param concentracao the concentracao value
	 */
	public void setConcentracao (java.lang.String concentracao) {
//        java.lang.String concentracaoOld = this.concentracao;
		this.concentracao = concentracao;
//        this.getPropertyChangeSupport().firePropertyChange ("concentracao", concentracaoOld, concentracao);
	}



	/**
	 * Return the value associated with the column: nr_quantidade_embalagem
	 */
	public java.lang.Double getQuantidadePorEmbalagem () {
		return getPropertyValue(this, quantidadePorEmbalagem, PROP_QUANTIDADE_POR_EMBALAGEM); 
	}

	/**
	 * Set the value related to the column: nr_quantidade_embalagem
	 * @param quantidadePorEmbalagem the nr_quantidade_embalagem value
	 */
	public void setQuantidadePorEmbalagem (java.lang.Double quantidadePorEmbalagem) {
//        java.lang.Double quantidadePorEmbalagemOld = this.quantidadePorEmbalagem;
		this.quantidadePorEmbalagem = quantidadePorEmbalagem;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadePorEmbalagem", quantidadePorEmbalagemOld, quantidadePorEmbalagem);
	}



	/**
	 * Return the value associated with the column: dt_usuario
	 */
	public java.util.Date getDataUsuarioAlterou () {
		return getPropertyValue(this, dataUsuarioAlterou, PROP_DATA_USUARIO_ALTEROU); 
	}

	/**
	 * Set the value related to the column: dt_usuario
	 * @param dataUsuarioAlterou the dt_usuario value
	 */
	public void setDataUsuarioAlterou (java.util.Date dataUsuarioAlterou) {
//        java.util.Date dataUsuarioAlterouOld = this.dataUsuarioAlterou;
		this.dataUsuarioAlterou = dataUsuarioAlterou;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsuarioAlterou", dataUsuarioAlterouOld, dataUsuarioAlterou);
	}



	/**
	 * Return the value associated with the column: bai_est_proc_enfermagem
	 */
	public java.lang.String getFlagBaixaEstoqueProcessoEnfermagem () {
		return getPropertyValue(this, flagBaixaEstoqueProcessoEnfermagem, PROP_FLAG_BAIXA_ESTOQUE_PROCESSO_ENFERMAGEM); 
	}

	/**
	 * Set the value related to the column: bai_est_proc_enfermagem
	 * @param flagBaixaEstoqueProcessoEnfermagem the bai_est_proc_enfermagem value
	 */
	public void setFlagBaixaEstoqueProcessoEnfermagem (java.lang.String flagBaixaEstoqueProcessoEnfermagem) {
//        java.lang.String flagBaixaEstoqueProcessoEnfermagemOld = this.flagBaixaEstoqueProcessoEnfermagem;
		this.flagBaixaEstoqueProcessoEnfermagem = flagBaixaEstoqueProcessoEnfermagem;
//        this.getPropertyChangeSupport().firePropertyChange ("flagBaixaEstoqueProcessoEnfermagem", flagBaixaEstoqueProcessoEnfermagemOld, flagBaixaEstoqueProcessoEnfermagem);
	}



	/**
	 * Return the value associated with the column: revisao_estrutura
	 */
	public java.lang.Long getRevisaoEstrutura () {
		return getPropertyValue(this, revisaoEstrutura, PROP_REVISAO_ESTRUTURA); 
	}

	/**
	 * Set the value related to the column: revisao_estrutura
	 * @param revisaoEstrutura the revisao_estrutura value
	 */
	public void setRevisaoEstrutura (java.lang.Long revisaoEstrutura) {
//        java.lang.Long revisaoEstruturaOld = this.revisaoEstrutura;
		this.revisaoEstrutura = revisaoEstrutura;
//        this.getPropertyChangeSupport().firePropertyChange ("revisaoEstrutura", revisaoEstruturaOld, revisaoEstrutura);
	}



	/**
	 * Return the value associated with the column: status_estrutura
	 */
	public java.lang.Long getStatusEstrutura () {
		return getPropertyValue(this, statusEstrutura, PROP_STATUS_ESTRUTURA); 
	}

	/**
	 * Set the value related to the column: status_estrutura
	 * @param statusEstrutura the status_estrutura value
	 */
	public void setStatusEstrutura (java.lang.Long statusEstrutura) {
//        java.lang.Long statusEstruturaOld = this.statusEstrutura;
		this.statusEstrutura = statusEstrutura;
//        this.getPropertyChangeSupport().firePropertyChange ("statusEstrutura", statusEstruturaOld, statusEstrutura);
	}



	/**
	 * Return the value associated with the column: limite_dispensacao
	 */
	public java.lang.Long getLimiteDispensacao () {
		return getPropertyValue(this, limiteDispensacao, PROP_LIMITE_DISPENSACAO); 
	}

	/**
	 * Set the value related to the column: limite_dispensacao
	 * @param limiteDispensacao the limite_dispensacao value
	 */
	public void setLimiteDispensacao (java.lang.Long limiteDispensacao) {
//        java.lang.Long limiteDispensacaoOld = this.limiteDispensacao;
		this.limiteDispensacao = limiteDispensacao;
//        this.getPropertyChangeSupport().firePropertyChange ("limiteDispensacao", limiteDispensacaoOld, limiteDispensacao);
	}



	/**
	 * Return the value associated with the column: duracao_tratamento
	 */
	public java.lang.Long getDuracaoTratamento () {
		return getPropertyValue(this, duracaoTratamento, PROP_DURACAO_TRATAMENTO); 
	}

	/**
	 * Set the value related to the column: duracao_tratamento
	 * @param duracaoTratamento the duracao_tratamento value
	 */
	public void setDuracaoTratamento (java.lang.Long duracaoTratamento) {
//        java.lang.Long duracaoTratamentoOld = this.duracaoTratamento;
		this.duracaoTratamento = duracaoTratamento;
//        this.getPropertyChangeSupport().firePropertyChange ("duracaoTratamento", duracaoTratamentoOld, duracaoTratamento);
	}



	/**
	 * Return the value associated with the column: cd_min_saude
	 */
	public java.lang.String getCodigoMinisterioSaude () {
		return getPropertyValue(this, codigoMinisterioSaude, PROP_CODIGO_MINISTERIO_SAUDE); 
	}

	/**
	 * Set the value related to the column: cd_min_saude
	 * @param codigoMinisterioSaude the cd_min_saude value
	 */
	public void setCodigoMinisterioSaude (java.lang.String codigoMinisterioSaude) {
//        java.lang.String codigoMinisterioSaudeOld = this.codigoMinisterioSaude;
		this.codigoMinisterioSaude = codigoMinisterioSaude;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoMinisterioSaude", codigoMinisterioSaudeOld, codigoMinisterioSaude);
	}



	/**
	 * Return the value associated with the column: qtdade_mg_ml
	 */
	public java.lang.Double getQtdadeMgMl () {
		return getPropertyValue(this, qtdadeMgMl, PROP_QTDADE_MG_ML); 
	}

	/**
	 * Set the value related to the column: qtdade_mg_ml
	 * @param qtdadeMgMl the qtdade_mg_ml value
	 */
	public void setQtdadeMgMl (java.lang.Double qtdadeMgMl) {
//        java.lang.Long qtdadeMgMlOld = this.qtdadeMgMl;
		this.qtdadeMgMl = qtdadeMgMl;
//        this.getPropertyChangeSupport().firePropertyChange ("qtdadeMgMl", qtdadeMgMlOld, qtdadeMgMl);
	}



	/**
	 * Return the value associated with the column: uso_continuo
	 */
	public java.lang.String getUsoContinuo () {
		return getPropertyValue(this, usoContinuo, PROP_USO_CONTINUO); 
	}

	/**
	 * Set the value related to the column: uso_continuo
	 * @param usoContinuo the uso_continuo value
	 */
	public void setUsoContinuo (java.lang.String usoContinuo) {
//        java.lang.String usoContinuoOld = this.usoContinuo;
		this.usoContinuo = usoContinuo;
//        this.getPropertyChangeSupport().firePropertyChange ("usoContinuo", usoContinuoOld, usoContinuo);
	}



	/**
	 * Return the value associated with the column: kit_montagem
	 */
	public java.lang.String getFlagKitMontagem () {
		return getPropertyValue(this, flagKitMontagem, PROP_FLAG_KIT_MONTAGEM); 
	}

	/**
	 * Set the value related to the column: kit_montagem
	 * @param flagKitMontagem the kit_montagem value
	 */
	public void setFlagKitMontagem (java.lang.String flagKitMontagem) {
//        java.lang.String flagKitMontagemOld = this.flagKitMontagem;
		this.flagKitMontagem = flagKitMontagem;
//        this.getPropertyChangeSupport().firePropertyChange ("flagKitMontagem", flagKitMontagemOld, flagKitMontagem);
	}



	/**
	 * Return the value associated with the column: flag_disp_esp
	 */
	public java.lang.String getFlagDispensacaoEspecial () {
		return getPropertyValue(this, flagDispensacaoEspecial, PROP_FLAG_DISPENSACAO_ESPECIAL); 
	}

	/**
	 * Set the value related to the column: flag_disp_esp
	 * @param flagDispensacaoEspecial the flag_disp_esp value
	 */
	public void setFlagDispensacaoEspecial (java.lang.String flagDispensacaoEspecial) {
//        java.lang.String flagDispensacaoEspecialOld = this.flagDispensacaoEspecial;
		this.flagDispensacaoEspecial = flagDispensacaoEspecial;
//        this.getPropertyChangeSupport().firePropertyChange ("flagDispensacaoEspecial", flagDispensacaoEspecialOld, flagDispensacaoEspecial);
	}



	/**
	 * Return the value associated with the column: val_receita_continua
	 */
	public java.lang.Long getValidadeReceitaContinua () {
		return getPropertyValue(this, validadeReceitaContinua, PROP_VALIDADE_RECEITA_CONTINUA); 
	}

	/**
	 * Set the value related to the column: val_receita_continua
	 * @param validadeReceitaContinua the val_receita_continua value
	 */
	public void setValidadeReceitaContinua (java.lang.Long validadeReceitaContinua) {
//        java.lang.Long validadeReceitaContinuaOld = this.validadeReceitaContinua;
		this.validadeReceitaContinua = validadeReceitaContinua;
//        this.getPropertyChangeSupport().firePropertyChange ("validadeReceitaContinua", validadeReceitaContinuaOld, validadeReceitaContinua);
	}



	/**
	 * Return the value associated with the column: rename
	 */
	public java.lang.String getFlagRename () {
		return getPropertyValue(this, flagRename, PROP_FLAG_RENAME); 
	}

	/**
	 * Set the value related to the column: rename
	 * @param flagRename the rename value
	 */
	public void setFlagRename (java.lang.String flagRename) {
//        java.lang.String flagRenameOld = this.flagRename;
		this.flagRename = flagRename;
//        this.getPropertyChangeSupport().firePropertyChange ("flagRename", flagRenameOld, flagRename);
	}



	/**
	 * Return the value associated with the column: referencia
	 */
	public java.lang.String getReferencia () {
		return getPropertyValue(this, referencia, PROP_REFERENCIA); 
	}

	/**
	 * Set the value related to the column: referencia
	 * @param referencia the referencia value
	 */
	public void setReferencia (java.lang.String referencia) {
//        java.lang.String referenciaOld = this.referencia;
		this.referencia = referencia;
//        this.getPropertyChangeSupport().firePropertyChange ("referencia", referenciaOld, referencia);
	}



	/**
	 * Return the value associated with the column: complemento
	 */
	public java.lang.String getComplemento () {
		return getPropertyValue(this, complemento, PROP_COMPLEMENTO); 
	}

	/**
	 * Set the value related to the column: complemento
	 * @param complemento the complemento value
	 */
	public void setComplemento (java.lang.String complemento) {
//        java.lang.String complementoOld = this.complemento;
		this.complemento = complemento;
//        this.getPropertyChangeSupport().firePropertyChange ("complemento", complementoOld, complemento);
	}



	/**
	 * Return the value associated with the column: qtd_unidade
	 */
	public java.lang.Long getQuantidadeUnidade () {
		return getPropertyValue(this, quantidadeUnidade, PROP_QUANTIDADE_UNIDADE); 
	}

	/**
	 * Set the value related to the column: qtd_unidade
	 * @param quantidadeUnidade the qtd_unidade value
	 */
	public void setQuantidadeUnidade (java.lang.Long quantidadeUnidade) {
//        java.lang.Long quantidadeUnidadeOld = this.quantidadeUnidade;
		this.quantidadeUnidade = quantidadeUnidade;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeUnidade", quantidadeUnidadeOld, quantidadeUnidade);
	}



	/**
	 * Return the value associated with the column: flag_padronizado
	 */
	public java.lang.String getFlagPadronizado () {
		return getPropertyValue(this, flagPadronizado, PROP_FLAG_PADRONIZADO); 
	}

	/**
	 * Set the value related to the column: flag_padronizado
	 * @param flagPadronizado the flag_padronizado value
	 */
	public void setFlagPadronizado (java.lang.String flagPadronizado) {
//        java.lang.String flagPadronizadoOld = this.flagPadronizado;
		this.flagPadronizado = flagPadronizado;
//        this.getPropertyChangeSupport().firePropertyChange ("flagPadronizado", flagPadronizadoOld, flagPadronizado);
	}



	/**
	 * Return the value associated with the column: flag_fracionado
	 */
	public java.lang.Long getFlagFracionado () {
		return getPropertyValue(this, flagFracionado, PROP_FLAG_FRACIONADO); 
	}

	/**
	 * Set the value related to the column: flag_fracionado
	 * @param flagFracionado the flag_fracionado value
	 */
	public void setFlagFracionado (java.lang.Long flagFracionado) {
//        java.lang.Long flagFracionadoOld = this.flagFracionado;
		this.flagFracionado = flagFracionado;
//        this.getPropertyChangeSupport().firePropertyChange ("flagFracionado", flagFracionadoOld, flagFracionado);
	}



	/**
	 * Return the value associated with the column: flag_permite_disp_mais
	 */
	public java.lang.String getFlagPermiteDispensarMais () {
		return getPropertyValue(this, flagPermiteDispensarMais, PROP_FLAG_PERMITE_DISPENSAR_MAIS); 
	}

	/**
	 * Set the value related to the column: flag_permite_disp_mais
	 * @param flagPermiteDispensarMais the flag_permite_disp_mais value
	 */
	public void setFlagPermiteDispensarMais (java.lang.String flagPermiteDispensarMais) {
//        java.lang.String flagPermiteDispensarMaisOld = this.flagPermiteDispensarMais;
		this.flagPermiteDispensarMais = flagPermiteDispensarMais;
//        this.getPropertyChangeSupport().firePropertyChange ("flagPermiteDispensarMais", flagPermiteDispensarMaisOld, flagPermiteDispensarMais);
	}



	/**
	 * Return the value associated with the column: cd_tp_produto_catmat
	 */
	public java.lang.String getTipoProdutoCatmat () {
		return getPropertyValue(this, tipoProdutoCatmat, PROP_TIPO_PRODUTO_CATMAT); 
	}

	/**
	 * Set the value related to the column: cd_tp_produto_catmat
	 * @param tipoProdutoCatmat the cd_tp_produto_catmat value
	 */
	public void setTipoProdutoCatmat (java.lang.String tipoProdutoCatmat) {
//        java.lang.String tipoProdutoCatmatOld = this.tipoProdutoCatmat;
		this.tipoProdutoCatmat = tipoProdutoCatmat;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoProdutoCatmat", tipoProdutoCatmatOld, tipoProdutoCatmat);
	}



	/**
	 * Return the value associated with the column: flag_emprestimo
	 */
	public java.lang.Long getFlagEmprestimo () {
		return getPropertyValue(this, flagEmprestimo, PROP_FLAG_EMPRESTIMO); 
	}

	/**
	 * Set the value related to the column: flag_emprestimo
	 * @param flagEmprestimo the flag_emprestimo value
	 */
	public void setFlagEmprestimo (java.lang.Long flagEmprestimo) {
//        java.lang.Long flagEmprestimoOld = this.flagEmprestimo;
		this.flagEmprestimo = flagEmprestimo;
//        this.getPropertyChangeSupport().firePropertyChange ("flagEmprestimo", flagEmprestimoOld, flagEmprestimo);
	}



	/**
	 * Return the value associated with the column: posologia_max_diaria
	 */
	public java.lang.Long getPosologiaMaximaDiaria () {
		return getPropertyValue(this, posologiaMaximaDiaria, PROP_POSOLOGIA_MAXIMA_DIARIA); 
	}

	/**
	 * Set the value related to the column: posologia_max_diaria
	 * @param posologiaMaximaDiaria the posologia_max_diaria value
	 */
	public void setPosologiaMaximaDiaria (java.lang.Long posologiaMaximaDiaria) {
//        java.lang.Long posologiaMaximaDiariaOld = this.posologiaMaximaDiaria;
		this.posologiaMaximaDiaria = posologiaMaximaDiaria;
//        this.getPropertyChangeSupport().firePropertyChange ("posologiaMaximaDiaria", posologiaMaximaDiariaOld, posologiaMaximaDiaria);
	}



	/**
	 * Return the value associated with the column: flag_exporta_horus
	 */
	public java.lang.Long getFlagExportaHorus () {
		return getPropertyValue(this, flagExportaHorus, PROP_FLAG_EXPORTA_HORUS); 
	}

	/**
	 * Set the value related to the column: flag_exporta_horus
	 * @param flagExportaHorus the flag_exporta_horus value
	 */
	public void setFlagExportaHorus (java.lang.Long flagExportaHorus) {
//        java.lang.Long flagExportaHorusOld = this.flagExportaHorus;
		this.flagExportaHorus = flagExportaHorus;
//        this.getPropertyChangeSupport().firePropertyChange ("flagExportaHorus", flagExportaHorusOld, flagExportaHorus);
	}



	/**
	 * Return the value associated with the column: cod_tce
	 */
	public java.lang.String getCodigoTce () {
		return getPropertyValue(this, codigoTce, PROP_CODIGO_TCE); 
	}

	/**
	 * Set the value related to the column: cod_tce
	 * @param codigoTce the cod_tce value
	 */
	public void setCodigoTce (java.lang.String codigoTce) {
//        java.lang.String codigoTceOld = this.codigoTce;
		this.codigoTce = codigoTce;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoTce", codigoTceOld, codigoTce);
	}



	/**
	 * Return the value associated with the column: flag_tratamento_prolongado_antibiotico
	 */
	public java.lang.Long getFlagTratamentoProlongadoAntibiotico () {
		return getPropertyValue(this, flagTratamentoProlongadoAntibiotico, PROP_FLAG_TRATAMENTO_PROLONGADO_ANTIBIOTICO); 
	}

	/**
	 * Set the value related to the column: flag_tratamento_prolongado_antibiotico
	 * @param flagTratamentoProlongadoAntibiotico the flag_tratamento_prolongado_antibiotico value
	 */
	public void setFlagTratamentoProlongadoAntibiotico (java.lang.Long flagTratamentoProlongadoAntibiotico) {
//        java.lang.Long flagTratamentoProlongadoAntibioticoOld = this.flagTratamentoProlongadoAntibiotico;
		this.flagTratamentoProlongadoAntibiotico = flagTratamentoProlongadoAntibiotico;
//        this.getPropertyChangeSupport().firePropertyChange ("flagTratamentoProlongadoAntibiotico", flagTratamentoProlongadoAntibioticoOld, flagTratamentoProlongadoAntibiotico);
	}



	/**
	 * Return the value associated with the column: flag_ativo
	 */
	public java.lang.Long getFlagAtivo () {
		return getPropertyValue(this, flagAtivo, PROP_FLAG_ATIVO); 
	}

	/**
	 * Set the value related to the column: flag_ativo
	 * @param flagAtivo the flag_ativo value
	 */
	public void setFlagAtivo (java.lang.Long flagAtivo) {
//        java.lang.Long flagAtivoOld = this.flagAtivo;
		this.flagAtivo = flagAtivo;
//        this.getPropertyChangeSupport().firePropertyChange ("flagAtivo", flagAtivoOld, flagAtivo);
	}



	/**
	 * Return the value associated with the column: forma_farmaceutica
	 */
	public java.lang.Long getFormaFarmaceutica () {
		return getPropertyValue(this, formaFarmaceutica, PROP_FORMA_FARMACEUTICA); 
	}

	/**
	 * Set the value related to the column: forma_farmaceutica
	 * @param formaFarmaceutica the forma_farmaceutica value
	 */
	public void setFormaFarmaceutica (java.lang.Long formaFarmaceutica) {
//        java.lang.Long formaFarmaceuticaOld = this.formaFarmaceutica;
		this.formaFarmaceutica = formaFarmaceutica;
//        this.getPropertyChangeSupport().firePropertyChange ("formaFarmaceutica", formaFarmaceuticaOld, formaFarmaceutica);
	}



	/**
	 * Return the value associated with the column: flag_disp_ped_lic
	 */
	public java.lang.Long getFlagDisponivelPedidoLicitacao () {
		return getPropertyValue(this, flagDisponivelPedidoLicitacao, PROP_FLAG_DISPONIVEL_PEDIDO_LICITACAO); 
	}

	/**
	 * Set the value related to the column: flag_disp_ped_lic
	 * @param flagDisponivelPedidoLicitacao the flag_disp_ped_lic value
	 */
	public void setFlagDisponivelPedidoLicitacao (java.lang.Long flagDisponivelPedidoLicitacao) {
//        java.lang.Long flagDisponivelPedidoLicitacaoOld = this.flagDisponivelPedidoLicitacao;
		this.flagDisponivelPedidoLicitacao = flagDisponivelPedidoLicitacao;
//        this.getPropertyChangeSupport().firePropertyChange ("flagDisponivelPedidoLicitacao", flagDisponivelPedidoLicitacaoOld, flagDisponivelPedidoLicitacao);
	}



	/**
	 * Return the value associated with the column: contem_diluente
	 */
	public java.lang.Long getContemDiluente () {
		return getPropertyValue(this, contemDiluente, PROP_CONTEM_DILUENTE); 
	}

	/**
	 * Set the value related to the column: contem_diluente
	 * @param contemDiluente the contem_diluente value
	 */
	public void setContemDiluente (java.lang.Long contemDiluente) {
//        java.lang.Long contemDiluenteOld = this.contemDiluente;
		this.contemDiluente = contemDiluente;
//        this.getPropertyChangeSupport().firePropertyChange ("contemDiluente", contemDiluenteOld, contemDiluente);
	}



	/**
	 * Return the value associated with the column: tempo_infusao
	 */
	public java.lang.Long getTempoInfusao () {
		return getPropertyValue(this, tempoInfusao, PROP_TEMPO_INFUSAO); 
	}

	/**
	 * Set the value related to the column: tempo_infusao
	 * @param tempoInfusao the tempo_infusao value
	 */
	public void setTempoInfusao (java.lang.Long tempoInfusao) {
//        java.lang.Long tempoInfusaoOld = this.tempoInfusao;
		this.tempoInfusao = tempoInfusao;
//        this.getPropertyChangeSupport().firePropertyChange ("tempoInfusao", tempoInfusaoOld, tempoInfusao);
	}



	/**
	 * Return the value associated with the column: volume_diluicao
	 */
	public java.lang.Long getVolumeDiluicao () {
		return getPropertyValue(this, volumeDiluicao, PROP_VOLUME_DILUICAO); 
	}

	/**
	 * Set the value related to the column: volume_diluicao
	 * @param volumeDiluicao the volume_diluicao value
	 */
	public void setVolumeDiluicao (java.lang.Long volumeDiluicao) {
//        java.lang.Long volumeDiluicaoOld = this.volumeDiluicao;
		this.volumeDiluicao = volumeDiluicao;
//        this.getPropertyChangeSupport().firePropertyChange ("volumeDiluicao", volumeDiluicaoOld, volumeDiluicao);
	}



	/**
	 * Return the value associated with the column: volume_restituicao
	 */
	public java.lang.Long getVolumeRestituicao () {
		return getPropertyValue(this, volumeRestituicao, PROP_VOLUME_RESTITUICAO); 
	}

	/**
	 * Set the value related to the column: volume_restituicao
	 * @param volumeRestituicao the volume_restituicao value
	 */
	public void setVolumeRestituicao (java.lang.Long volumeRestituicao) {
//        java.lang.Long volumeRestituicaoOld = this.volumeRestituicao;
		this.volumeRestituicao = volumeRestituicao;
//        this.getPropertyChangeSupport().firePropertyChange ("volumeRestituicao", volumeRestituicaoOld, volumeRestituicao);
	}



	/**
	 * Return the value associated with the column: flag_emite_lme
	 */
	public java.lang.Long getFlagEmiteLme () {
		return getPropertyValue(this, flagEmiteLme, PROP_FLAG_EMITE_LME); 
	}

	/**
	 * Set the value related to the column: flag_emite_lme
	 * @param flagEmiteLme the flag_emite_lme value
	 */
	public void setFlagEmiteLme (java.lang.Long flagEmiteLme) {
//        java.lang.Long flagEmiteLmeOld = this.flagEmiteLme;
		this.flagEmiteLme = flagEmiteLme;
//        this.getPropertyChangeSupport().firePropertyChange ("flagEmiteLme", flagEmiteLmeOld, flagEmiteLme);
	}



	/**
	 * Return the value associated with the column: id_integracao_terceiro
	 */
	public java.lang.Long getIdIntegracaoTerceiro () {
		return getPropertyValue(this, idIntegracaoTerceiro, PROP_ID_INTEGRACAO_TERCEIRO); 
	}

	/**
	 * Set the value related to the column: id_integracao_terceiro
	 * @param idIntegracaoTerceiro the id_integracao_terceiro value
	 */
	public void setIdIntegracaoTerceiro (java.lang.Long idIntegracaoTerceiro) {
//        java.lang.Long idIntegracaoTerceiroOld = this.idIntegracaoTerceiro;
		this.idIntegracaoTerceiro = idIntegracaoTerceiro;
//        this.getPropertyChangeSupport().firePropertyChange ("idIntegracaoTerceiro", idIntegracaoTerceiroOld, idIntegracaoTerceiro);
	}



	/**
	 * Return the value associated with the column: nm_integracao_terceiro
	 */
	public java.lang.String getNomeIntegracaoTerceiro () {
		return getPropertyValue(this, nomeIntegracaoTerceiro, PROP_NOME_INTEGRACAO_TERCEIRO); 
	}

	/**
	 * Set the value related to the column: nm_integracao_terceiro
	 * @param nomeIntegracaoTerceiro the nm_integracao_terceiro value
	 */
	public void setNomeIntegracaoTerceiro (java.lang.String nomeIntegracaoTerceiro) {
//        java.lang.String nomeIntegracaoTerceiroOld = this.nomeIntegracaoTerceiro;
		this.nomeIntegracaoTerceiro = nomeIntegracaoTerceiro;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeIntegracaoTerceiro", nomeIntegracaoTerceiroOld, nomeIntegracaoTerceiro);
	}



	/**
	 * Return the value associated with the column: vida_util
	 */
	public java.lang.Long getVidaUtil () {
		return getPropertyValue(this, vidaUtil, PROP_VIDA_UTIL); 
	}

	/**
	 * Set the value related to the column: vida_util
	 * @param vidaUtil the vida_util value
	 */
	public void setVidaUtil (java.lang.Long vidaUtil) {
//        java.lang.Long vidaUtilOld = this.vidaUtil;
		this.vidaUtil = vidaUtil;
//        this.getPropertyChangeSupport().firePropertyChange ("vidaUtil", vidaUtilOld, vidaUtil);
	}



	/**
	 * Return the value associated with the column: coeficiente_vida_util
	 */
	public java.lang.Long getCoeficienteVidaUtil () {
		return getPropertyValue(this, coeficienteVidaUtil, PROP_COEFICIENTE_VIDA_UTIL); 
	}

	/**
	 * Set the value related to the column: coeficiente_vida_util
	 * @param coeficienteVidaUtil the coeficiente_vida_util value
	 */
	public void setCoeficienteVidaUtil (java.lang.Long coeficienteVidaUtil) {
//        java.lang.Long coeficienteVidaUtilOld = this.coeficienteVidaUtil;
		this.coeficienteVidaUtil = coeficienteVidaUtil;
//        this.getPropertyChangeSupport().firePropertyChange ("coeficienteVidaUtil", coeficienteVidaUtilOld, coeficienteVidaUtil);
	}



	/**
	 * Return the value associated with the column: fabricante_esus
	 */
	public java.lang.String getFabricanteEsus () {
		return getPropertyValue(this, fabricanteEsus, PROP_FABRICANTE_ESUS); 
	}

	/**
	 * Set the value related to the column: fabricante_esus
	 * @param fabricanteEsus the fabricante_esus value
	 */
	public void setFabricanteEsus (java.lang.String fabricanteEsus) {
//        java.lang.String fabricanteEsusOld = this.fabricanteEsus;
		this.fabricanteEsus = fabricanteEsus;
//        this.getPropertyChangeSupport().firePropertyChange ("fabricanteEsus", fabricanteEsusOld, fabricanteEsus);
	}



	/**
	 * Return the value associated with the column: flag_hipertensao_arterial
	 */
	public java.lang.String getFlagHipertensaoArterial () {
		return getPropertyValue(this, flagHipertensaoArterial, PROP_FLAG_HIPERTENSAO_ARTERIAL); 
	}

	/**
	 * Set the value related to the column: flag_hipertensao_arterial
	 * @param flagHipertensaoArterial the flag_hipertensao_arterial value
	 */
	public void setFlagHipertensaoArterial (java.lang.String flagHipertensaoArterial) {
//        java.lang.String flagHipertensaoArterialOld = this.flagHipertensaoArterial;
		this.flagHipertensaoArterial = flagHipertensaoArterial;
//        this.getPropertyChangeSupport().firePropertyChange ("flagHipertensaoArterial", flagHipertensaoArterialOld, flagHipertensaoArterial);
	}



	/**
	 * Return the value associated with the column: flag_diabete
	 */
	public java.lang.String getFlagDiabete () {
		return getPropertyValue(this, flagDiabete, PROP_FLAG_DIABETE); 
	}

	/**
	 * Set the value related to the column: flag_diabete
	 * @param flagDiabete the flag_diabete value
	 */
	public void setFlagDiabete (java.lang.String flagDiabete) {
//        java.lang.String flagDiabeteOld = this.flagDiabete;
		this.flagDiabete = flagDiabete;
//        this.getPropertyChangeSupport().firePropertyChange ("flagDiabete", flagDiabeteOld, flagDiabete);
	}



	/**
	 * Return the value associated with the column: flag_lista_medicamento_publico
	 */
	public java.lang.Long getFlagListaMedicamentoPublico () {
		return getPropertyValue(this, flagListaMedicamentoPublico, PROP_FLAG_LISTA_MEDICAMENTO_PUBLICO); 
	}

	/**
	 * Set the value related to the column: flag_lista_medicamento_publico
	 * @param flagListaMedicamentoPublico the flag_lista_medicamento_publico value
	 */
	public void setFlagListaMedicamentoPublico (java.lang.Long flagListaMedicamentoPublico) {
//        java.lang.Long flagListaMedicamentoPublicoOld = this.flagListaMedicamentoPublico;
		this.flagListaMedicamentoPublico = flagListaMedicamentoPublico;
//        this.getPropertyChangeSupport().firePropertyChange ("flagListaMedicamentoPublico", flagListaMedicamentoPublicoOld, flagListaMedicamentoPublico);
	}



	/**
	 * Return the value associated with the column: eloProdutoBrasindice
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.EloProdutoBrasindice getEloProdutoBrasindice () {
		return getPropertyValue(this, eloProdutoBrasindice, PROP_ELO_PRODUTO_BRASINDICE); 
	}

	/**
	 * Set the value related to the column: eloProdutoBrasindice
	 * @param eloProdutoBrasindice the eloProdutoBrasindice value
	 */
	public void setEloProdutoBrasindice (br.com.ksisolucoes.vo.entradas.estoque.EloProdutoBrasindice eloProdutoBrasindice) {
//        br.com.ksisolucoes.vo.entradas.estoque.EloProdutoBrasindice eloProdutoBrasindiceOld = this.eloProdutoBrasindice;
		this.eloProdutoBrasindice = eloProdutoBrasindice;
//        this.getPropertyChangeSupport().firePropertyChange ("eloProdutoBrasindice", eloProdutoBrasindiceOld, eloProdutoBrasindice);
	}



	/**
	 * Return the value associated with the column: cd_fabricante
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.FabricanteMedicamento getFabricante () {
		return getPropertyValue(this, fabricante, PROP_FABRICANTE); 
	}

	/**
	 * Set the value related to the column: cd_fabricante
	 * @param fabricante the cd_fabricante value
	 */
	public void setFabricante (br.com.ksisolucoes.vo.entradas.estoque.FabricanteMedicamento fabricante) {
//        br.com.ksisolucoes.vo.entradas.estoque.FabricanteMedicamento fabricanteOld = this.fabricante;
		this.fabricante = fabricante;
//        this.getPropertyChangeSupport().firePropertyChange ("fabricante", fabricanteOld, fabricante);
	}



	/**
	 * Return the value associated with the column: cd_medicamento_catmat
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.MedicamentoCatmat getMedicamentoCatmat () {
		return getPropertyValue(this, medicamentoCatmat, PROP_MEDICAMENTO_CATMAT); 
	}

	/**
	 * Set the value related to the column: cd_medicamento_catmat
	 * @param medicamentoCatmat the cd_medicamento_catmat value
	 */
	public void setMedicamentoCatmat (br.com.ksisolucoes.vo.entradas.estoque.MedicamentoCatmat medicamentoCatmat) {
//        br.com.ksisolucoes.vo.entradas.estoque.MedicamentoCatmat medicamentoCatmatOld = this.medicamentoCatmat;
		this.medicamentoCatmat = medicamentoCatmat;
//        this.getPropertyChangeSupport().firePropertyChange ("medicamentoCatmat", medicamentoCatmatOld, medicamentoCatmat);
	}

	/**
	 * Return the value associated with the column: cd_medicamento_obm_ampp
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.MedicamentoObmAmpp getMedicamentoObmAmpp () {
		return getPropertyValue(this, medicamentoObmAmpp, PROP_MEDICAMENTO_OBM_AMPP);
	}

	/**
	 * Set the value related to the column: cd_medicamento_obm_ampp
	 * @param medicamentoObmAmpp the cd_medicamento_obm_ampp value
	 */
	public void setMedicamentoObmAmpp (br.com.ksisolucoes.vo.entradas.estoque.MedicamentoObmAmpp medicamentoObmAmpp) {
		this.medicamentoObmAmpp = medicamentoObmAmpp;
	}

	/**
	 * Return the value associated with the column: cd_receita
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.TipoReceita getTipoReceita () {
		return getPropertyValue(this, tipoReceita, PROP_TIPO_RECEITA); 
	}

	/**
	 * Set the value related to the column: cd_receita
	 * @param tipoReceita the cd_receita value
	 */
	public void setTipoReceita (br.com.ksisolucoes.vo.prontuario.basico.TipoReceita tipoReceita) {
//        br.com.ksisolucoes.vo.prontuario.basico.TipoReceita tipoReceitaOld = this.tipoReceita;
		this.tipoReceita = tipoReceita;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoReceita", tipoReceitaOld, tipoReceita);
	}



	/**
	 * Return the value associated with the column: cod_uni_rec
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Unidade getUnidadeReceituario () {
		return getPropertyValue(this, unidadeReceituario, PROP_UNIDADE_RECEITUARIO); 
	}

	/**
	 * Set the value related to the column: cod_uni_rec
	 * @param unidadeReceituario the cod_uni_rec value
	 */
	public void setUnidadeReceituario (br.com.ksisolucoes.vo.entradas.estoque.Unidade unidadeReceituario) {
//        br.com.ksisolucoes.vo.entradas.estoque.Unidade unidadeReceituarioOld = this.unidadeReceituario;
		this.unidadeReceituario = unidadeReceituario;
//        this.getPropertyChangeSupport().firePropertyChange ("unidadeReceituario", unidadeReceituarioOld, unidadeReceituario);
	}



	/**
	 * Return the value associated with the column: cd_classificacao
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.ClassificacaoContabil getClassificacaoContabil () {
		return getPropertyValue(this, classificacaoContabil, PROP_CLASSIFICACAO_CONTABIL); 
	}

	/**
	 * Set the value related to the column: cd_classificacao
	 * @param classificacaoContabil the cd_classificacao value
	 */
	public void setClassificacaoContabil (br.com.ksisolucoes.vo.entradas.estoque.ClassificacaoContabil classificacaoContabil) {
//        br.com.ksisolucoes.vo.entradas.estoque.ClassificacaoContabil classificacaoContabilOld = this.classificacaoContabil;
		this.classificacaoContabil = classificacaoContabil;
//        this.getPropertyChangeSupport().firePropertyChange ("classificacaoContabil", classificacaoContabilOld, classificacaoContabil);
	}



	/**
	 * Return the value associated with the column: cd_local_fornecimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.LocalFornecimento getLocalFornecimento () {
		return getPropertyValue(this, localFornecimento, PROP_LOCAL_FORNECIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_local_fornecimento
	 * @param localFornecimento the cd_local_fornecimento value
	 */
	public void setLocalFornecimento (br.com.ksisolucoes.vo.prontuario.basico.LocalFornecimento localFornecimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.LocalFornecimento localFornecimentoOld = this.localFornecimento;
		this.localFornecimento = localFornecimento;
//        this.getPropertyChangeSupport().firePropertyChange ("localFornecimento", localFornecimentoOld, localFornecimento);
	}



	/**
	 * Return the value associated with the column: cod_uni_prescricao
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Unidade getUnidadePrescricao () {
		return getPropertyValue(this, unidadePrescricao, PROP_UNIDADE_PRESCRICAO); 
	}

	/**
	 * Set the value related to the column: cod_uni_prescricao
	 * @param unidadePrescricao the cod_uni_prescricao value
	 */
	public void setUnidadePrescricao (br.com.ksisolucoes.vo.entradas.estoque.Unidade unidadePrescricao) {
//        br.com.ksisolucoes.vo.entradas.estoque.Unidade unidadePrescricaoOld = this.unidadePrescricao;
		this.unidadePrescricao = unidadePrescricao;
//        this.getPropertyChangeSupport().firePropertyChange ("unidadePrescricao", unidadePrescricaoOld, unidadePrescricao);
	}



	/**
	 * Return the value associated with the column: cod_uni
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Unidade getUnidade () {
		return getPropertyValue(this, unidade, PROP_UNIDADE); 
	}

	/**
	 * Set the value related to the column: cod_uni
	 * @param unidade the cod_uni value
	 */
	public void setUnidade (br.com.ksisolucoes.vo.entradas.estoque.Unidade unidade) {
//        br.com.ksisolucoes.vo.entradas.estoque.Unidade unidadeOld = this.unidade;
		this.unidade = unidade;
//        this.getPropertyChangeSupport().firePropertyChange ("unidade", unidadeOld, unidade);
	}



	/**
	 * Return the value associated with the column: cod_gru
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.SubGrupo getSubGrupo () {
		return getPropertyValue(this, subGrupo, PROP_SUB_GRUPO); 
	}

	/**
	 * Set the value related to the column: cod_gru
	 * @param subGrupo the cod_gru value
	 */
	public void setSubGrupo (br.com.ksisolucoes.vo.entradas.estoque.SubGrupo subGrupo) {
//        br.com.ksisolucoes.vo.entradas.estoque.SubGrupo subGrupoOld = this.subGrupo;
		this.subGrupo = subGrupo;
//        this.getPropertyChangeSupport().firePropertyChange ("subGrupo", subGrupoOld, subGrupo);
	}



	/**
	 * Return the value associated with the column: cd_usuario_est
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioEstrutura () {
		return getPropertyValue(this, usuarioEstrutura, PROP_USUARIO_ESTRUTURA); 
	}

	/**
	 * Set the value related to the column: cd_usuario_est
	 * @param usuarioEstrutura the cd_usuario_est value
	 */
	public void setUsuarioEstrutura (br.com.ksisolucoes.vo.controle.Usuario usuarioEstrutura) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioEstruturaOld = this.usuarioEstrutura;
		this.usuarioEstrutura = usuarioEstrutura;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioEstrutura", usuarioEstruturaOld, usuarioEstrutura);
	}



	/**
	 * Return the value associated with the column: diluente
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Produto getDiluente () {
		return getPropertyValue(this, diluente, PROP_DILUENTE); 
	}

	/**
	 * Set the value related to the column: diluente
	 * @param diluente the diluente value
	 */
	public void setDiluente (br.com.ksisolucoes.vo.entradas.estoque.Produto diluente) {
//        br.com.ksisolucoes.vo.entradas.estoque.Produto diluenteOld = this.diluente;
		this.diluente = diluente;
//        this.getPropertyChangeSupport().firePropertyChange ("diluente", diluenteOld, diluente);
	}



	/**
	 * Return the value associated with the column: vel_inf_via_endovenosa
	 */
	public br.com.ksisolucoes.vo.basico.TipoInfusao getVelocidadeInfusaoViaEndovenosa () {
		return getPropertyValue(this, velocidadeInfusaoViaEndovenosa, PROP_VELOCIDADE_INFUSAO_VIA_ENDOVENOSA); 
	}

	/**
	 * Set the value related to the column: vel_inf_via_endovenosa
	 * @param velocidadeInfusaoViaEndovenosa the vel_inf_via_endovenosa value
	 */
	public void setVelocidadeInfusaoViaEndovenosa (br.com.ksisolucoes.vo.basico.TipoInfusao velocidadeInfusaoViaEndovenosa) {
//        br.com.ksisolucoes.vo.basico.TipoInfusao velocidadeInfusaoViaEndovenosaOld = this.velocidadeInfusaoViaEndovenosa;
		this.velocidadeInfusaoViaEndovenosa = velocidadeInfusaoViaEndovenosa;
//        this.getPropertyChangeSupport().firePropertyChange ("velocidadeInfusaoViaEndovenosa", velocidadeInfusaoViaEndovenosaOld, velocidadeInfusaoViaEndovenosa);
	}

	/**
	 * Return the value associated with the column: flag_habilitar_obm
	 */
	public java.lang.Long getFlagHabilitarObm () {
		return getPropertyValue(this, flagHabilitarObm, PROP_FLAG_HABILITAR_OBM);
	}

	/**
	 * Set the value related to the column: flag_habilitar_obm
	 * @param flagHabilitarObm the flag_habilitar_obm value
	 */
	public void setFlagHabilitarObm (java.lang.Long flagHabilitarObm) {
		this.flagHabilitarObm = flagHabilitarObm;
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.entradas.estoque.Produto)) return false;
		else {
			br.com.ksisolucoes.vo.entradas.estoque.Produto produto = (br.com.ksisolucoes.vo.entradas.estoque.Produto) obj;
			if (null == this.getCodigo() || null == produto.getCodigo()) return false;
			else return (this.getCodigo().equals(produto.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}