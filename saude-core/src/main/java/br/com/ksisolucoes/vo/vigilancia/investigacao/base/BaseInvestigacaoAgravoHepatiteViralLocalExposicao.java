package br.com.ksisolucoes.vo.vigilancia.investigacao.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the investigacao_agr_hepatite_viral_local_exposicao table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agr_hepatite_viral_local_exposicao"
 */

public abstract class BaseInvestigacaoAgravoHepatiteViralLocalExposicao extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravoHepatiteViralLocalExposicao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_LOCAL_EXPOSICAO = "localExposicao";
	public static final String PROP_CIDADE_LOCAL_EXPOSICAO = "cidadeLocalExposicao";
	public static final String PROP_LOCAL_EXPOSICAO_TELEFONE = "localExposicaoTelefone";
	public static final String PROP_INVESTIGACAO_AGRAVO_HEPATITE_VIRAL = "investigacaoAgravoHepatiteViral";


	// constructors
	public BaseInvestigacaoAgravoHepatiteViralLocalExposicao () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravoHepatiteViralLocalExposicao (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseInvestigacaoAgravoHepatiteViralLocalExposicao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoHepatiteViral investigacaoAgravoHepatiteViral) {

		this.setCodigo(codigo);
		this.setInvestigacaoAgravoHepatiteViral(investigacaoAgravoHepatiteViral);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long localExposicao;
	private java.lang.String localExposicaoTelefone;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoHepatiteViral investigacaoAgravoHepatiteViral;
	private br.com.ksisolucoes.vo.basico.Cidade cidadeLocalExposicao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_hepatite_viral_local_exposicao"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: local_exposicao
	 */
	public java.lang.Long getLocalExposicao () {
		return getPropertyValue(this, localExposicao, PROP_LOCAL_EXPOSICAO); 
	}

	/**
	 * Set the value related to the column: local_exposicao
	 * @param localExposicao the local_exposicao value
	 */
	public void setLocalExposicao (java.lang.Long localExposicao) {
//        java.lang.Long localExposicaoOld = this.localExposicao;
		this.localExposicao = localExposicao;
//        this.getPropertyChangeSupport().firePropertyChange ("localExposicao", localExposicaoOld, localExposicao);
	}



	/**
	 * Return the value associated with the column: local_exposicao_telefone
	 */
	public java.lang.String getLocalExposicaoTelefone () {
		return getPropertyValue(this, localExposicaoTelefone, PROP_LOCAL_EXPOSICAO_TELEFONE); 
	}

	/**
	 * Set the value related to the column: local_exposicao_telefone
	 * @param localExposicaoTelefone the local_exposicao_telefone value
	 */
	public void setLocalExposicaoTelefone (java.lang.String localExposicaoTelefone) {
//        java.lang.String localExposicaoTelefoneOld = this.localExposicaoTelefone;
		this.localExposicaoTelefone = localExposicaoTelefone;
//        this.getPropertyChangeSupport().firePropertyChange ("localExposicaoTelefone", localExposicaoTelefoneOld, localExposicaoTelefone);
	}



	/**
	 * Return the value associated with the column: cd_invest_agr_hepatite_viral
	 */
	public br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoHepatiteViral getInvestigacaoAgravoHepatiteViral () {
		return getPropertyValue(this, investigacaoAgravoHepatiteViral, PROP_INVESTIGACAO_AGRAVO_HEPATITE_VIRAL); 
	}

	/**
	 * Set the value related to the column: cd_invest_agr_hepatite_viral
	 * @param investigacaoAgravoHepatiteViral the cd_invest_agr_hepatite_viral value
	 */
	public void setInvestigacaoAgravoHepatiteViral (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoHepatiteViral investigacaoAgravoHepatiteViral) {
//        br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoHepatiteViral investigacaoAgravoHepatiteViralOld = this.investigacaoAgravoHepatiteViral;
		this.investigacaoAgravoHepatiteViral = investigacaoAgravoHepatiteViral;
//        this.getPropertyChangeSupport().firePropertyChange ("investigacaoAgravoHepatiteViral", investigacaoAgravoHepatiteViralOld, investigacaoAgravoHepatiteViral);
	}



	/**
	 * Return the value associated with the column: cd_cidade_exposicao
	 */
	public br.com.ksisolucoes.vo.basico.Cidade getCidadeLocalExposicao () {
		return getPropertyValue(this, cidadeLocalExposicao, PROP_CIDADE_LOCAL_EXPOSICAO); 
	}

	/**
	 * Set the value related to the column: cd_cidade_exposicao
	 * @param cidadeLocalExposicao the cd_cidade_exposicao value
	 */
	public void setCidadeLocalExposicao (br.com.ksisolucoes.vo.basico.Cidade cidadeLocalExposicao) {
//        br.com.ksisolucoes.vo.basico.Cidade cidadeLocalExposicaoOld = this.cidadeLocalExposicao;
		this.cidadeLocalExposicao = cidadeLocalExposicao;
//        this.getPropertyChangeSupport().firePropertyChange ("cidadeLocalExposicao", cidadeLocalExposicaoOld, cidadeLocalExposicao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoHepatiteViralLocalExposicao)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoHepatiteViralLocalExposicao investigacaoAgravoHepatiteViralLocalExposicao = (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoHepatiteViralLocalExposicao) obj;
			if (null == this.getCodigo() || null == investigacaoAgravoHepatiteViralLocalExposicao.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravoHepatiteViralLocalExposicao.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}