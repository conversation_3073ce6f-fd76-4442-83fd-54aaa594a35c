package br.com.ksisolucoes.vo.vigilancia.requerimentos.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the req_pro_hidros_parecer_ativ table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="req_pro_hidros_parecer_ativ"
 */

public abstract class BaseRequerimentoProjetoHidrossanitarioParecerAtividade extends BaseRootVO implements Serializable {

	public static String REF = "RequerimentoProjetoHidrossanitarioParecerAtividade";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_REQUERIMENTO_PROJETO_HIDROSSANITARIO_PARECER = "requerimentoProjetoHidrossanitarioParecer";
	public static final String PROP_ATIVIDADES_VIGILANCIA = "atividadesVigilancia";


	// constructors
	public BaseRequerimentoProjetoHidrossanitarioParecerAtividade () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseRequerimentoProjetoHidrossanitarioParecerAtividade (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseRequerimentoProjetoHidrossanitarioParecerAtividade (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecer requerimentoProjetoHidrossanitarioParecer,
		br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia atividadesVigilancia) {

		this.setCodigo(codigo);
		this.setRequerimentoProjetoHidrossanitarioParecer(requerimentoProjetoHidrossanitarioParecer);
		this.setAtividadesVigilancia(atividadesVigilancia);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecer requerimentoProjetoHidrossanitarioParecer;
	private br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia atividadesVigilancia;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_req_pro_hidros_parecer_ativ"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_req_pro_hidros_parecer
	 */
	public br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecer getRequerimentoProjetoHidrossanitarioParecer () {
		return getPropertyValue(this, requerimentoProjetoHidrossanitarioParecer, PROP_REQUERIMENTO_PROJETO_HIDROSSANITARIO_PARECER); 
	}

	/**
	 * Set the value related to the column: cd_req_pro_hidros_parecer
	 * @param requerimentoProjetoHidrossanitarioParecer the cd_req_pro_hidros_parecer value
	 */
	public void setRequerimentoProjetoHidrossanitarioParecer (br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecer requerimentoProjetoHidrossanitarioParecer) {
//        br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecer requerimentoProjetoHidrossanitarioParecerOld = this.requerimentoProjetoHidrossanitarioParecer;
		this.requerimentoProjetoHidrossanitarioParecer = requerimentoProjetoHidrossanitarioParecer;
//        this.getPropertyChangeSupport().firePropertyChange ("requerimentoProjetoHidrossanitarioParecer", requerimentoProjetoHidrossanitarioParecerOld, requerimentoProjetoHidrossanitarioParecer);
	}



	/**
	 * Return the value associated with the column: cd_atividades_vigilancia
	 */
	public br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia getAtividadesVigilancia () {
		return getPropertyValue(this, atividadesVigilancia, PROP_ATIVIDADES_VIGILANCIA); 
	}

	/**
	 * Set the value related to the column: cd_atividades_vigilancia
	 * @param atividadesVigilancia the cd_atividades_vigilancia value
	 */
	public void setAtividadesVigilancia (br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia atividadesVigilancia) {
//        br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia atividadesVigilanciaOld = this.atividadesVigilancia;
		this.atividadesVigilancia = atividadesVigilancia;
//        this.getPropertyChangeSupport().firePropertyChange ("atividadesVigilancia", atividadesVigilanciaOld, atividadesVigilancia);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecerAtividade)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecerAtividade requerimentoProjetoHidrossanitarioParecerAtividade = (br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecerAtividade) obj;
			if (null == this.getCodigo() || null == requerimentoProjetoHidrossanitarioParecerAtividade.getCodigo()) return false;
			else return (this.getCodigo().equals(requerimentoProjetoHidrossanitarioParecerAtividade.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}