<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.outraunidade">
    <class name="VisualizacaoPacienteAtendidoOutraUnidadeUsuario" table="visualizacao_paciente_atendido_outra_unidade_usuario">
        <id column="cd_visualizacao_paciente_atendido_outra_unidade_usuario" name="codigo" type="java.lang.Long">
            <generator class="sequence">
                <param name="sequence">seq_audit_id_visualizacao_paciente_atendido_outra_unidade_usuario</param>
            </generator>
        </id>

        <version column="version" name="version" type="long"/>

        <many-to-one name="pacienteAtendidoOutraUnidade" class="br.com.ksisolucoes.vo.outraunidade.PacienteAtendidoOutraUnidade" not-null="true">
            <column name="cd_atend_outra_unid"/>
        </many-to-one>

        <many-to-one name="usuario" class="br.com.ksisolucoes.vo.controle.Usuario" not-null="true">
            <column name="cd_usuario"/>
        </many-to-one>

        <property
                column="dt_visalizacao"
                name="dataVisalizacao"
                type="timestamp"
                not-null="true"/>
    </class>
</hibernate-mapping>
