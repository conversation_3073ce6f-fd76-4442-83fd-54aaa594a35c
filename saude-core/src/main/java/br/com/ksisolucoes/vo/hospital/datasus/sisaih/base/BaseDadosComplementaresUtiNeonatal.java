package br.com.ksisolucoes.vo.hospital.datasus.sisaih.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;

/**
 * This is an object that contains data related to the
 * dados_complementares_uti_neonatal table. Do not modify this class because it
 * will be overwritten if the configuration file related to this class is
 * modified.
 *
 * @hibernate.class table="dados_complementares_uti_neonatal"
 */
public abstract class BaseDadosComplementaresUtiNeonatal extends BaseRootVO implements Serializable {

    public static String REF = "DadosComplementaresUtiNeonatal";
    public static final String PROP_DATA_COMPETENCIA = "dataCompetencia";
    public static final String PROP_CODIGO = "codigo";
    public static final String PROP_PESO_NASCIMENTO = "pesoNascimento";
    public static final String PROP_QUANTIDADE_DIAS = "quantidadeDias";
    public static final String PROP_MESES_GESTACAO = "mesesGestacao";
    public static final String PROP_MOTIVO_SAIDA = "motivoSaida";
    public static final String PROP_ITEM_CONTA_PACIENTE = "itemContaPaciente";

    // constructors
    public BaseDadosComplementaresUtiNeonatal() {
        initialize();
    }

    /**
     * Constructor for primary key
     */
    public BaseDadosComplementaresUtiNeonatal(java.lang.Long codigo) {
        this.setCodigo(codigo);
        initialize();
    }

    /**
     * Constructor for required fields
     */
    public BaseDadosComplementaresUtiNeonatal(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente itemContaPaciente) {

        this.setCodigo(codigo);
        this.setItemContaPaciente(itemContaPaciente);
        initialize();
    }

    protected void initialize() {
    }

    private int hashCode = Integer.MIN_VALUE;

    // primary key
    private java.lang.Long codigo;

    // fields
    private java.util.Date dataCompetencia;
    private java.lang.Long quantidadeDias;
    private java.lang.Long mesesGestacao;
    private java.lang.Long pesoNascimento;
    private java.lang.Long motivoSaida;

    // many to one
    private br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente itemContaPaciente;

    /**
     * Return the unique identifier of this class
     *
     * @hibernate.id generator-class="assigned" column="cd_dado_compl_uti_neo"
     */
    public java.lang.Long getCodigo() {
        return getPropertyValue(this, codigo, "codigo");
    }

    /**
     * Set the unique identifier of this class
     *
     * @param codigo the new ID
     */
    public void setCodigo(java.lang.Long codigo) {
        this.codigo = codigo;
        this.hashCode = Integer.MIN_VALUE;
    }

    /**
     * Return the value associated with the column: dt_competencia
     */
    public java.util.Date getDataCompetencia() {
        return getPropertyValue(this, dataCompetencia, PROP_DATA_COMPETENCIA);
    }

    /**
     * Set the value related to the column: dt_competencia
     *
     * @param dataCompetencia the dt_competencia value
     */
    public void setDataCompetencia(java.util.Date dataCompetencia) {
//        java.util.Date dataCompetenciaOld = this.dataCompetencia;
        this.dataCompetencia = dataCompetencia;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCompetencia", dataCompetenciaOld, dataCompetencia);
    }

    /**
     * Return the value associated with the column: qtd_dias
     */
    public java.lang.Long getQuantidadeDias() {
        return getPropertyValue(this, quantidadeDias, PROP_QUANTIDADE_DIAS);
    }

    /**
     * Set the value related to the column: qtd_dias
     *
     * @param quantidadeDias the qtd_dias value
     */
    public void setQuantidadeDias(java.lang.Long quantidadeDias) {
//        java.lang.Long quantidadeDiasOld = this.quantidadeDias;
        this.quantidadeDias = quantidadeDias;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeDias", quantidadeDiasOld, quantidadeDias);
    }

    /**
     * Return the value associated with the column: meses_gestacao
     */
    public java.lang.Long getMesesGestacao() {
        return getPropertyValue(this, mesesGestacao, PROP_MESES_GESTACAO);
    }

    /**
     * Set the value related to the column: meses_gestacao
     *
     * @param mesesGestacao the meses_gestacao value
     */
    public void setMesesGestacao(java.lang.Long mesesGestacao) {
//        java.lang.Long mesesGestacaoOld = this.mesesGestacao;
        this.mesesGestacao = mesesGestacao;
//        this.getPropertyChangeSupport().firePropertyChange ("mesesGestacao", mesesGestacaoOld, mesesGestacao);
    }

    /**
     * Return the value associated with the column: peso_nascimento
     */
    public java.lang.Long getPesoNascimento() {
        return getPropertyValue(this, pesoNascimento, PROP_PESO_NASCIMENTO);
    }

    /**
     * Set the value related to the column: peso_nascimento
     *
     * @param pesoNascimento the peso_nascimento value
     */
    public void setPesoNascimento(java.lang.Long pesoNascimento) {
//        java.lang.Long pesoNascimentoOld = this.pesoNascimento;
        this.pesoNascimento = pesoNascimento;
//        this.getPropertyChangeSupport().firePropertyChange ("pesoNascimento", pesoNascimentoOld, pesoNascimento);
    }

    /**
     * Return the value associated with the column: motivo_saida
     */
    public java.lang.Long getMotivoSaida() {
        return getPropertyValue(this, motivoSaida, PROP_MOTIVO_SAIDA);
    }

    /**
     * Set the value related to the column: motivo_saida
     *
     * @param motivoSaida the motivo_saida value
     */
    public void setMotivoSaida(java.lang.Long motivoSaida) {
//        java.lang.Long motivoSaidaOld = this.motivoSaida;
        this.motivoSaida = motivoSaida;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoSaida", motivoSaidaOld, motivoSaida);
    }

    /**
     * Return the value associated with the column: cd_it_cta_paciente
     */
    public br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente getItemContaPaciente() {
        return getPropertyValue(this, itemContaPaciente, PROP_ITEM_CONTA_PACIENTE);
    }

    /**
     * Set the value related to the column: cd_it_cta_paciente
     *
     * @param itemContaPaciente the cd_it_cta_paciente value
     */
    public void setItemContaPaciente(br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente itemContaPaciente) {
//        br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente itemContaPacienteOld = this.itemContaPaciente;
        this.itemContaPaciente = itemContaPaciente;
//        this.getPropertyChangeSupport().firePropertyChange ("itemContaPaciente", itemContaPacienteOld, itemContaPaciente);
    }

    public boolean equals(Object obj) {
        if (null == obj) {
            return false;
        }
        if (!(obj instanceof br.com.ksisolucoes.vo.hospital.datasus.sisaih.DadosComplementaresUtiNeonatal)) {
            return false;
        } else {
            br.com.ksisolucoes.vo.hospital.datasus.sisaih.DadosComplementaresUtiNeonatal dadosComplementaresUtiNeonatal = (br.com.ksisolucoes.vo.hospital.datasus.sisaih.DadosComplementaresUtiNeonatal) obj;
            if (null == this.getCodigo() || null == dadosComplementaresUtiNeonatal.getCodigo()) {
                return false;
            } else {
                return (this.getCodigo().equals(dadosComplementaresUtiNeonatal.getCodigo()));
            }
        }
    }

    public int hashCode() {
        if (Integer.MIN_VALUE == this.hashCode) {
            if (null == this.getCodigo()) {
                return super.hashCode();
            } else {
                String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
                this.hashCode = hashStr.hashCode();
            }
        }
        return this.hashCode;
    }

    public String toString() {
        return super.toString();
    }

    private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
        if (this.retornoValidacao == null) {
            this.retornoValidacao = new RetornoValidacao();
        }
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
        this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}
