package br.com.ksisolucoes.vo.integracao.boleto.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the integracao_boleto table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="integracao_boleto"
 */

public abstract class BaseIntegracaoBoleto extends BaseRootVO implements Serializable {

	public static String REF = "IntegracaoBoleto";
	public static final String PROP_DATA_REMESSA = "dataRemessa";
	public static final String PROP_DATA_GERACAO = "dataGeracao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_CODIGO_BANCO = "codigoBanco";
	public static final String PROP_TOKEN = "token";
	public static final String PROP_NUMERO_PROTOCOLO = "numeroProtocolo";


	// constructors
	public BaseIntegracaoBoleto () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseIntegracaoBoleto (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String token;
	private java.lang.Long codigoBanco;
	private java.lang.Long numeroProtocolo;
	private java.util.Date dataGeracao;
	private java.util.Date dataRemessa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_integracao"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: token
	 */
	public java.lang.String getToken () {
		return getPropertyValue(this, token, PROP_TOKEN); 
	}

	/**
	 * Set the value related to the column: token
	 * @param token the token value
	 */
	public void setToken (java.lang.String token) {
//        java.lang.String tokenOld = this.token;
		this.token = token;
//        this.getPropertyChangeSupport().firePropertyChange ("token", tokenOld, token);
	}



	/**
	 * Return the value associated with the column: cod_banco
	 */
	public java.lang.Long getCodigoBanco () {
		return getPropertyValue(this, codigoBanco, PROP_CODIGO_BANCO); 
	}

	/**
	 * Set the value related to the column: cod_banco
	 * @param codigoBanco the cod_banco value
	 */
	public void setCodigoBanco (java.lang.Long codigoBanco) {
//        java.lang.Long codigoBancoOld = this.codigoBanco;
		this.codigoBanco = codigoBanco;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoBanco", codigoBancoOld, codigoBanco);
	}



	/**
	 * Return the value associated with the column: nr_protocolo
	 */
	public java.lang.Long getNumeroProtocolo () {
		return getPropertyValue(this, numeroProtocolo, PROP_NUMERO_PROTOCOLO); 
	}

	/**
	 * Set the value related to the column: nr_protocolo
	 * @param numeroProtocolo the nr_protocolo value
	 */
	public void setNumeroProtocolo (java.lang.Long numeroProtocolo) {
//        java.lang.Long numeroProtocoloOld = this.numeroProtocolo;
		this.numeroProtocolo = numeroProtocolo;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroProtocolo", numeroProtocoloOld, numeroProtocolo);
	}



	/**
	 * Return the value associated with the column: dt_geracao
	 */
	public java.util.Date getDataGeracao () {
		return getPropertyValue(this, dataGeracao, PROP_DATA_GERACAO); 
	}

	/**
	 * Set the value related to the column: dt_geracao
	 * @param dataGeracao the dt_geracao value
	 */
	public void setDataGeracao (java.util.Date dataGeracao) {
//        java.util.Date dataGeracaoOld = this.dataGeracao;
		this.dataGeracao = dataGeracao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataGeracao", dataGeracaoOld, dataGeracao);
	}



	/**
	 * Return the value associated with the column: dt_remessa
	 */
	public java.util.Date getDataRemessa () {
		return getPropertyValue(this, dataRemessa, PROP_DATA_REMESSA); 
	}

	/**
	 * Set the value related to the column: dt_remessa
	 * @param dataRemessa the dt_remessa value
	 */
	public void setDataRemessa (java.util.Date dataRemessa) {
//        java.util.Date dataRemessaOld = this.dataRemessa;
		this.dataRemessa = dataRemessa;
//        this.getPropertyChangeSupport().firePropertyChange ("dataRemessa", dataRemessaOld, dataRemessa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.integracao.boleto.IntegracaoBoleto)) return false;
		else {
			br.com.ksisolucoes.vo.integracao.boleto.IntegracaoBoleto integracaoBoleto = (br.com.ksisolucoes.vo.integracao.boleto.IntegracaoBoleto) obj;
			if (null == this.getCodigo() || null == integracaoBoleto.getCodigo()) return false;
			else return (this.getCodigo().equals(integracaoBoleto.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}