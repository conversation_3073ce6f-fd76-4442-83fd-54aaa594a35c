package br.com.ksisolucoes.vo.cadsus;

import java.io.Serializable;
import java.text.SimpleDateFormat;

import br.com.ksisolucoes.vo.cadsus.base.BaseXmlCadsus;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class XmlCadsus extends BaseXmlCadsus implements CodigoManager {
	private static final long serialVersionUID = 1L;

	public static final String PROP_DESCRICAO_DATA_CRIACAO = "descricaoDataCriacao";

/*[CONSTRUCTOR MARKER BEGIN]*/
	public XmlCadsus () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public XmlCadsus (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public XmlCadsus (
		java.lang.Long codigo,
		java.util.Date dataCricao,
		java.lang.String xml) {

		super (
			codigo,
			dataCricao,
			xml);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getDescricaoDataCriacao(){
        if(getDataCricao() != null){
            return new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(getDataCricao());
        }
        return null;
    }
}