package br.com.ksisolucoes.vo.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the parametro_atendimento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="parametro_atendimento"
 */

public abstract class BaseParametroAtendimento extends BaseRootVO implements Serializable {

	public static String REF = "ParametroAtendimento";
	public static final String PROP_DATA_ATUALIZACAO = "dataAtualizacao";
	public static final String PROP_BPA_NOME_DESTINO = "bpaNomeDestino";
	public static final String PROP_BPA_FLAG_DESTINO = "bpaFlagDestino";
	public static final String PROP_BPA_CNPJ_CPF_ORIGEM = "bpaCnpjCpfOrigem";
	public static final String PROP_PROCEDIMENTO_ENFERMAGEM = "procedimentoEnfermagem";
	public static final String PROP_BPA_NOME_ORIGEM = "bpaNomeOrigem";
	public static final String PROP_FLAG_BPA_GERA_TFD = "FlagBpaGeraTfd";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_PROCEDIMENTO_MEDICO = "procedimentoMedico";
	public static final String PROP_TIPO_ATENDIMENTO_ENFERMAGEM = "tipoAtendimentoEnfermagem";
	public static final String PROP_FLAG_BPA_VACINA = "flagBpaVacina";
	public static final String PROP_PROCEDIMENTO_PRIMARIO = "procedimentoPrimario";
	public static final String PROP_TIPO_ENCAMINHAMENTO_PEQUENA_CIRURGIA = "tipoEncaminhamentoPequenaCirurgia";
	public static final String PROP_DATA_PROCESSO_CID_NOTIFICAVEL = "dataProcessoCidNotificavel";
	public static final String PROP_PROCEDIMENTO_EMERGENCIA = "procedimentoEmergencia";
	public static final String PROP_DIA_INICIO_COMPETENCIA = "diaInicioCompetencia";
	public static final String PROP_BPA_SIGLA_ORIGEM = "bpaSiglaOrigem";
	public static final String PROP_FLAG_BPA_GERA_VIGILANCIA_SANITARIA = "flagBpaGeraVigilanciaSanitaria";


	// constructors
	public BaseParametroAtendimento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseParametroAtendimento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataAtualizacao;
	private java.lang.Long diaInicioCompetencia;
	private java.lang.String bpaNomeOrigem;
	private java.lang.String bpaSiglaOrigem;
	private java.lang.String bpaCnpjCpfOrigem;
	private java.lang.String bpaNomeDestino;
	private java.lang.String bpaFlagDestino;
	private java.lang.String flagBpaGeraTfd;
	private java.lang.Long flagBpaGeraVigilanciaSanitaria;
	private java.lang.Long flagBpaVacina;
	private java.util.Date dataProcessoCidNotificavel;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoEnfermagem;
	private br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoPrimario;
	private br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoMedico;
	private br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoEmergencia;
	private br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento tipoAtendimentoEnfermagem;
	private br.com.ksisolucoes.vo.prontuario.basico.TipoEncaminhamento tipoEncaminhamentoPequenaCirurgia;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cod_parametro"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_atualizacao
	 */
	public java.util.Date getDataAtualizacao () {
		return getPropertyValue(this, dataAtualizacao, PROP_DATA_ATUALIZACAO); 
	}

	/**
	 * Set the value related to the column: dt_atualizacao
	 * @param dataAtualizacao the dt_atualizacao value
	 */
	public void setDataAtualizacao (java.util.Date dataAtualizacao) {
//        java.util.Date dataAtualizacaoOld = this.dataAtualizacao;
		this.dataAtualizacao = dataAtualizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAtualizacao", dataAtualizacaoOld, dataAtualizacao);
	}



	/**
	 * Return the value associated with the column: dia_ini_comp
	 */
	public java.lang.Long getDiaInicioCompetencia () {
		return getPropertyValue(this, diaInicioCompetencia, PROP_DIA_INICIO_COMPETENCIA); 
	}

	/**
	 * Set the value related to the column: dia_ini_comp
	 * @param diaInicioCompetencia the dia_ini_comp value
	 */
	public void setDiaInicioCompetencia (java.lang.Long diaInicioCompetencia) {
//        java.lang.Long diaInicioCompetenciaOld = this.diaInicioCompetencia;
		this.diaInicioCompetencia = diaInicioCompetencia;
//        this.getPropertyChangeSupport().firePropertyChange ("diaInicioCompetencia", diaInicioCompetenciaOld, diaInicioCompetencia);
	}



	/**
	 * Return the value associated with the column: bpa_nome_origem
	 */
	public java.lang.String getBpaNomeOrigem () {
		return getPropertyValue(this, bpaNomeOrigem, PROP_BPA_NOME_ORIGEM); 
	}

	/**
	 * Set the value related to the column: bpa_nome_origem
	 * @param bpaNomeOrigem the bpa_nome_origem value
	 */
	public void setBpaNomeOrigem (java.lang.String bpaNomeOrigem) {
//        java.lang.String bpaNomeOrigemOld = this.bpaNomeOrigem;
		this.bpaNomeOrigem = bpaNomeOrigem;
//        this.getPropertyChangeSupport().firePropertyChange ("bpaNomeOrigem", bpaNomeOrigemOld, bpaNomeOrigem);
	}



	/**
	 * Return the value associated with the column: bpa_sigla_origem
	 */
	public java.lang.String getBpaSiglaOrigem () {
		return getPropertyValue(this, bpaSiglaOrigem, PROP_BPA_SIGLA_ORIGEM); 
	}

	/**
	 * Set the value related to the column: bpa_sigla_origem
	 * @param bpaSiglaOrigem the bpa_sigla_origem value
	 */
	public void setBpaSiglaOrigem (java.lang.String bpaSiglaOrigem) {
//        java.lang.String bpaSiglaOrigemOld = this.bpaSiglaOrigem;
		this.bpaSiglaOrigem = bpaSiglaOrigem;
//        this.getPropertyChangeSupport().firePropertyChange ("bpaSiglaOrigem", bpaSiglaOrigemOld, bpaSiglaOrigem);
	}



	/**
	 * Return the value associated with the column: bpa_cnpj_cpf_origem
	 */
	public java.lang.String getBpaCnpjCpfOrigem () {
		return getPropertyValue(this, bpaCnpjCpfOrigem, PROP_BPA_CNPJ_CPF_ORIGEM); 
	}

	/**
	 * Set the value related to the column: bpa_cnpj_cpf_origem
	 * @param bpaCnpjCpfOrigem the bpa_cnpj_cpf_origem value
	 */
	public void setBpaCnpjCpfOrigem (java.lang.String bpaCnpjCpfOrigem) {
//        java.lang.String bpaCnpjCpfOrigemOld = this.bpaCnpjCpfOrigem;
		this.bpaCnpjCpfOrigem = bpaCnpjCpfOrigem;
//        this.getPropertyChangeSupport().firePropertyChange ("bpaCnpjCpfOrigem", bpaCnpjCpfOrigemOld, bpaCnpjCpfOrigem);
	}



	/**
	 * Return the value associated with the column: bpa_nome_destino
	 */
	public java.lang.String getBpaNomeDestino () {
		return getPropertyValue(this, bpaNomeDestino, PROP_BPA_NOME_DESTINO); 
	}

	/**
	 * Set the value related to the column: bpa_nome_destino
	 * @param bpaNomeDestino the bpa_nome_destino value
	 */
	public void setBpaNomeDestino (java.lang.String bpaNomeDestino) {
//        java.lang.String bpaNomeDestinoOld = this.bpaNomeDestino;
		this.bpaNomeDestino = bpaNomeDestino;
//        this.getPropertyChangeSupport().firePropertyChange ("bpaNomeDestino", bpaNomeDestinoOld, bpaNomeDestino);
	}



	/**
	 * Return the value associated with the column: bpa_flag_destino
	 */
	public java.lang.String getBpaFlagDestino () {
		return getPropertyValue(this, bpaFlagDestino, PROP_BPA_FLAG_DESTINO); 
	}

	/**
	 * Set the value related to the column: bpa_flag_destino
	 * @param bpaFlagDestino the bpa_flag_destino value
	 */
	public void setBpaFlagDestino (java.lang.String bpaFlagDestino) {
//        java.lang.String bpaFlagDestinoOld = this.bpaFlagDestino;
		this.bpaFlagDestino = bpaFlagDestino;
//        this.getPropertyChangeSupport().firePropertyChange ("bpaFlagDestino", bpaFlagDestinoOld, bpaFlagDestino);
	}



	/**
	 * Return the value associated with the column: flag_bpa_gera_tfd
	 */
	public java.lang.String getFlagBpaGeraTfd () {
		return getPropertyValue(this, flagBpaGeraTfd, PROP_FLAG_BPA_GERA_TFD); 
	}

	/**
	 * Set the value related to the column: flag_bpa_gera_tfd
	 * @param flagBpaGeraTfd the flag_bpa_gera_tfd value
	 */
	public void setFlagBpaGeraTfd (java.lang.String flagBpaGeraTfd) {
//        java.lang.String flagBpaGeraTfdOld = this.flagBpaGeraTfd;
		this.flagBpaGeraTfd = flagBpaGeraTfd;
//        this.getPropertyChangeSupport().firePropertyChange ("flagBpaGeraTfd", flagBpaGeraTfdOld, flagBpaGeraTfd);
	}



	/**
	 * Return the value associated with the column: flag_bpa_gera_vigilancia_sanitaria
	 */
	public java.lang.Long getFlagBpaGeraVigilanciaSanitaria () {
		return getPropertyValue(this, flagBpaGeraVigilanciaSanitaria, PROP_FLAG_BPA_GERA_VIGILANCIA_SANITARIA); 
	}

	/**
	 * Set the value related to the column: flag_bpa_gera_vigilancia_sanitaria
	 * @param flagBpaGeraVigilanciaSanitaria the flag_bpa_gera_vigilancia_sanitaria value
	 */
	public void setFlagBpaGeraVigilanciaSanitaria (java.lang.Long flagBpaGeraVigilanciaSanitaria) {
//        java.lang.Long flagBpaGeraVigilanciaSanitariaOld = this.flagBpaGeraVigilanciaSanitaria;
		this.flagBpaGeraVigilanciaSanitaria = flagBpaGeraVigilanciaSanitaria;
//        this.getPropertyChangeSupport().firePropertyChange ("flagBpaGeraVigilanciaSanitaria", flagBpaGeraVigilanciaSanitariaOld, flagBpaGeraVigilanciaSanitaria);
	}



	/**
	 * Return the value associated with the column: flag_bpa_vacina
	 */
	public java.lang.Long getFlagBpaVacina () {
		return getPropertyValue(this, flagBpaVacina, PROP_FLAG_BPA_VACINA); 
	}

	/**
	 * Set the value related to the column: flag_bpa_vacina
	 * @param flagBpaVacina the flag_bpa_vacina value
	 */
	public void setFlagBpaVacina (java.lang.Long flagBpaVacina) {
//        java.lang.Long flagBpaVacinaOld = this.flagBpaVacina;
		this.flagBpaVacina = flagBpaVacina;
//        this.getPropertyChangeSupport().firePropertyChange ("flagBpaVacina", flagBpaVacinaOld, flagBpaVacina);
	}



	/**
	 * Return the value associated with the column: dt_proc_cid_notificavel
	 */
	public java.util.Date getDataProcessoCidNotificavel () {
		return getPropertyValue(this, dataProcessoCidNotificavel, PROP_DATA_PROCESSO_CID_NOTIFICAVEL); 
	}

	/**
	 * Set the value related to the column: dt_proc_cid_notificavel
	 * @param dataProcessoCidNotificavel the dt_proc_cid_notificavel value
	 */
	public void setDataProcessoCidNotificavel (java.util.Date dataProcessoCidNotificavel) {
//        java.util.Date dataProcessoCidNotificavelOld = this.dataProcessoCidNotificavel;
		this.dataProcessoCidNotificavel = dataProcessoCidNotificavel;
//        this.getPropertyChangeSupport().firePropertyChange ("dataProcessoCidNotificavel", dataProcessoCidNotificavelOld, dataProcessoCidNotificavel);
	}



	/**
	 * Return the value associated with the column: cd_proced_enfermagem
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento getProcedimentoEnfermagem () {
		return getPropertyValue(this, procedimentoEnfermagem, PROP_PROCEDIMENTO_ENFERMAGEM); 
	}

	/**
	 * Set the value related to the column: cd_proced_enfermagem
	 * @param procedimentoEnfermagem the cd_proced_enfermagem value
	 */
	public void setProcedimentoEnfermagem (br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoEnfermagem) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoEnfermagemOld = this.procedimentoEnfermagem;
		this.procedimentoEnfermagem = procedimentoEnfermagem;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimentoEnfermagem", procedimentoEnfermagemOld, procedimentoEnfermagem);
	}



	/**
	 * Return the value associated with the column: cd_proced_primario
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento getProcedimentoPrimario () {
		return getPropertyValue(this, procedimentoPrimario, PROP_PROCEDIMENTO_PRIMARIO); 
	}

	/**
	 * Set the value related to the column: cd_proced_primario
	 * @param procedimentoPrimario the cd_proced_primario value
	 */
	public void setProcedimentoPrimario (br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoPrimario) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoPrimarioOld = this.procedimentoPrimario;
		this.procedimentoPrimario = procedimentoPrimario;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimentoPrimario", procedimentoPrimarioOld, procedimentoPrimario);
	}



	/**
	 * Return the value associated with the column: cd_proced_medico
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento getProcedimentoMedico () {
		return getPropertyValue(this, procedimentoMedico, PROP_PROCEDIMENTO_MEDICO); 
	}

	/**
	 * Set the value related to the column: cd_proced_medico
	 * @param procedimentoMedico the cd_proced_medico value
	 */
	public void setProcedimentoMedico (br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoMedico) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoMedicoOld = this.procedimentoMedico;
		this.procedimentoMedico = procedimentoMedico;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimentoMedico", procedimentoMedicoOld, procedimentoMedico);
	}



	/**
	 * Return the value associated with the column: cd_proced_emergencia
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento getProcedimentoEmergencia () {
		return getPropertyValue(this, procedimentoEmergencia, PROP_PROCEDIMENTO_EMERGENCIA); 
	}

	/**
	 * Set the value related to the column: cd_proced_emergencia
	 * @param procedimentoEmergencia the cd_proced_emergencia value
	 */
	public void setProcedimentoEmergencia (br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoEmergencia) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoEmergenciaOld = this.procedimentoEmergencia;
		this.procedimentoEmergencia = procedimentoEmergencia;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimentoEmergencia", procedimentoEmergenciaOld, procedimentoEmergencia);
	}



	/**
	 * Return the value associated with the column: cd_tp_atendimento_enfermagem
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento getTipoAtendimentoEnfermagem () {
		return getPropertyValue(this, tipoAtendimentoEnfermagem, PROP_TIPO_ATENDIMENTO_ENFERMAGEM); 
	}

	/**
	 * Set the value related to the column: cd_tp_atendimento_enfermagem
	 * @param tipoAtendimentoEnfermagem the cd_tp_atendimento_enfermagem value
	 */
	public void setTipoAtendimentoEnfermagem (br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento tipoAtendimentoEnfermagem) {
//        br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento tipoAtendimentoEnfermagemOld = this.tipoAtendimentoEnfermagem;
		this.tipoAtendimentoEnfermagem = tipoAtendimentoEnfermagem;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAtendimentoEnfermagem", tipoAtendimentoEnfermagemOld, tipoAtendimentoEnfermagem);
	}



	/**
	 * Return the value associated with the column: cd_tp_enc_peq_cir
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.TipoEncaminhamento getTipoEncaminhamentoPequenaCirurgia () {
		return getPropertyValue(this, tipoEncaminhamentoPequenaCirurgia, PROP_TIPO_ENCAMINHAMENTO_PEQUENA_CIRURGIA); 
	}

	/**
	 * Set the value related to the column: cd_tp_enc_peq_cir
	 * @param tipoEncaminhamentoPequenaCirurgia the cd_tp_enc_peq_cir value
	 */
	public void setTipoEncaminhamentoPequenaCirurgia (br.com.ksisolucoes.vo.prontuario.basico.TipoEncaminhamento tipoEncaminhamentoPequenaCirurgia) {
//        br.com.ksisolucoes.vo.prontuario.basico.TipoEncaminhamento tipoEncaminhamentoPequenaCirurgiaOld = this.tipoEncaminhamentoPequenaCirurgia;
		this.tipoEncaminhamentoPequenaCirurgia = tipoEncaminhamentoPequenaCirurgia;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoEncaminhamentoPequenaCirurgia", tipoEncaminhamentoPequenaCirurgiaOld, tipoEncaminhamentoPequenaCirurgia);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.ParametroAtendimento)) return false;
		else {
			br.com.ksisolucoes.vo.basico.ParametroAtendimento parametroAtendimento = (br.com.ksisolucoes.vo.basico.ParametroAtendimento) obj;
			if (null == this.getCodigo() || null == parametroAtendimento.getCodigo()) return false;
			else return (this.getCodigo().equals(parametroAtendimento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}