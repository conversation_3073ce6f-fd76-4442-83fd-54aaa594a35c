<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.basico"  >
    <class
        name="Equipamento"
        table="equipamento"
        >
        <id
            name="codigo"
            type="java.lang.String"
            column="cd_equipamento"
            length="2"
            >
        </id> <version column="version" name="version" type="long" />

		<property
            name="descricao"
            column="ds_equipamento"
            type="java.lang.String"
            length="60"
         />

       	<many-to-one
            name="tipoEquipamento"
            class="br.com.ksisolucoes.vo.basico.TipoEquipamento"
            not-null="false"
            >
            <column name="cd_tp_equipamento"/>
        </many-to-one>

        <property
            name="dataAtualizacao"
            column="dt_atualizacao"
            type="java.util.Date"
        />

		<property
            name="dataCadastro"
            column="dt_cadastro"
            type="java.util.Date"
        />

        <property
            name="status"
            column="status"
            type="java.lang.Long"
        />
    </class>
</hibernate-mapping>