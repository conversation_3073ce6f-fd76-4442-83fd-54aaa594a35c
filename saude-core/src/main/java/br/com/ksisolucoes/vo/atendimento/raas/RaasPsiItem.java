package br.com.ksisolucoes.vo.atendimento.raas;

import java.io.Serializable;

import br.com.ksisolucoes.vo.atendimento.raas.base.BaseRaasPsiItem;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class RaasPsiItem extends BaseRaasPsiItem implements CodigoManager {
    private static final long serialVersionUID = 1L;
        
    public static final String PROP_ORIGEM_INFORMACOES_RAS = "RAS";
    public static final String PROP_ORIGEM_INFORMACOES_EXT = "EXT";
        
    public static final String PROP_LOCAL_REALIZACAO_CAPS = "C";
    public static final String PROP_LOCAL_REALIZACAO_TERRITORIO = "T";

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RaasPsiItem () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RaasPsiItem (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RaasPsiItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.atendimento.raas.RaasPsi raasPsi,
		java.lang.Long linha,
		java.util.Date dataCadastro) {

		super (
			codigo,
			raasPsi,
			linha,
			dataCadastro);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}