<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="FormularioCriancas" table="formulario_criancas" >
        
        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_formulario_criancas"
        >
            <generator class="sequence">
                <param name="sequence">seq_formulario_criancas</param>
            </generator>
        </id>
		<version column="version" name="version" type="long" />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.basico.EstratificacaoRisco"
                name="estratificacaoRisco"
                column="cd_estratificacao_risco"
                not-null="true"
        />

        <property
                name="flagFilhoMaeNegra"
                type="java.lang.Long"
                column="flag_filho_mae_negra"
        />

        <property
                name="flagFilhoMaeMenos15Mais40Anos"
                type="java.lang.Long"
                column="flag_filho_mae_menos_15_mais_40_anos"
        />

        <property
                name="flagFilhoMaeAnalfabetaMenos3AnosEstudo"
                type="java.lang.Long"
                column="flag_filho_mae_analfabeta_menos_3_anos_estudo"
        />

        <property
                name="flagFilhoMaeHistoricoObitoGestacaoAnterior"
                type="java.lang.Long"
                column="flag_filho_mae_historico_obito_gestacao_anterior"
        />

        <property
                name="flagFilhoMaeMenos20AnosMais3Partos"
                type="java.lang.Long"
                column="flag_filho_mae_menos_20_anos_mais_3_partos"
        />

        <property
                name="flagFilhoMaeMorreuPartoPuerperio"
                type="java.lang.Long"
                column="flag_filho_mae_morreu_parto_puerperio"
        />

        <property
                name="flagAfeccoesPerinatais"
                type="java.lang.Long"
                column="flag_afeccoes_perinatais"
        />

        <property
                name="flagMasFormacoesCongenitas"
                type="java.lang.Long"
                column="flag_mas_formacoes_congenitas"
        />

        <property
                name="flagTriagemNeonatalPositiva"
                type="java.lang.Long"
                column="flag_triagem_neonatal_positiva"
        />

        <property
                name="flagDoencaTransmissaoVerticalConfirmada"
                type="java.lang.Long"
                column="flag_doenca_transmissao_vertical_confirmada"
        />

        <property
                name="flagDesnutricaoGrave"
                type="java.lang.Long"
                column="flag_desnutricao_grave"
        />

        <property
                name="flagObesidade"
                type="java.lang.Long"
                column="flag_obesidade"
        />

        <property
                name="flagAtrasoDesenvolvimentoNeuropsicomotor"
                type="java.lang.Long"
                column="flag_atraso_desenvolvimento_neuropsicomotor"
        />

        <property
                name="flagIntercorrenciasRepetidasRepercucaoClinica"
                type="java.lang.Long"
                column="flag_intercorrencias_repetidas_repercucao_clinica"
        />

        <property
                name="observacao"
                type="java.lang.String"
                column="observacao"
        />
        
        <property
                name="score"
                type="java.lang.Long"
                column="score"
        />
        
        <property
                name="flagRisco"
                type="java.lang.Long"
                column="flag_risco"
        />
    </class>
</hibernate-mapping>