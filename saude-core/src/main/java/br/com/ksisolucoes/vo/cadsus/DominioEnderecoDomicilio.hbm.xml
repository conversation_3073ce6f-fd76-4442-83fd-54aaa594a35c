<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.cadsus"  >
    <class name="DominioEnderecoDomicilio" table="dom_endereco_domicilio">

        <id name="codigo" type="java.lang.Long" column="id">
            <generator class="assigned" />
        </id>

        <property column="ds_logradouro" name="logradouro" type="java.lang.String" not-null="true" />
        <property column="nr_logradouro" name="numero" type="java.lang.String" />
        <property column="ds_bairro" name="bairro" type="java.lang.String" />
        <property column="cep" name="cep" type="java.lang.String" />
        <property column="ds_comp_logradouro" name="complemento" type="java.lang.String" />
        <property column="ds_cidade" name="descricaoCidade" type="java.lang.String" />
        <property column="ds_uf" name="uf" type="java.lang.String" />
        <property column="ponto_referencia" name="pontoReferencia" type="java.lang.String" />
        <property column="nr_familia" name="numeroFamilia" type="java.lang.Long" />
        <property column="ds_pac_nome" name="nomePaciente" type="java.lang.String" />
        <property column="ds_pac_nome_social" name="nomeSocialPaciente" type="java.lang.String" />
        <property column="flag_responsavel" name="responsavelFamilia" type="java.lang.Long" />
        <property column="dt_nascimento" name="dataNascimento" type="java.util.Date" />
        <property column="ds_area" name="descricaoArea" type="java.lang.String" />
        <property column="micro_area" name="microArea" type="java.lang.Long" />
        <property column="keyword" name="keyword" type="java.lang.String" />

        <many-to-one column="cd_endereco" name="enderecoUsuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus" />
        <many-to-one column="cd_domicilio" name="enderecoDomicilio" class="br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio" />
        <many-to-one column="cd_usu_cadsus" name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus" />
        <many-to-one column="cd_usu_cadsus_responsavel" name="usuarioCadsusResponsavel" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus" />
        <many-to-one column="cod_cid" name="cidade" class="br.com.ksisolucoes.vo.basico.Cidade" />
        <many-to-one column="cd_equipe_area" name="equipeArea" class="br.com.ksisolucoes.vo.basico.EquipeArea" />
        <many-to-one column="cd_eqp_micro_area" name="equipeMicroArea" class="br.com.ksisolucoes.vo.basico.EquipeMicroArea" />

    </class>
</hibernate-mapping>