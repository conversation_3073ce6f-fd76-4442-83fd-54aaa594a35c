package br.com.ksisolucoes.vo.prontuario.hospital;

import java.io.Serializable;

import br.com.ksisolucoes.vo.prontuario.hospital.base.BaseConfiguracaoBalancoHidrico;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

public class ConfiguracaoBalancoHidrico extends BaseConfiguracaoBalancoHidrico implements CodigoManager {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public ConfiguracaoBalancoHidrico() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public ConfiguracaoBalancoHidrico(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public ConfiguracaoBalancoHidrico(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCbo grupoAtendimentoCbo,
            java.lang.Long permissaoInativar) {

        super(
                codigo,
                grupoAtendimentoCbo,
                permissaoInativar);
    }

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}
