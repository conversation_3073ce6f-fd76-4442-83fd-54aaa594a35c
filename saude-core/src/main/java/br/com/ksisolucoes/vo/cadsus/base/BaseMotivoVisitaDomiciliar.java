package br.com.ksisolucoes.vo.cadsus.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the motivo_visita_domiciliar table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="motivo_visita_domiciliar"
 */

public abstract class BaseMotivoVisitaDomiciliar extends BaseRootVO implements Serializable {

	public static String REF = "MotivoVisitaDomiciliar";
	public static final String PROP_TIPO_MOTIVO = "tipoMotivo";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_ATIVO = "ativo";
	public static final String PROP_INTEGRACAO = "integracao";


	// constructors
	public BaseMotivoVisitaDomiciliar () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseMotivoVisitaDomiciliar (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseMotivoVisitaDomiciliar (
		java.lang.Long codigo,
		java.lang.String descricao,
		java.lang.Long ativo) {

		this.setCodigo(codigo);
		this.setDescricao(descricao);
		this.setAtivo(ativo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.lang.Long tipoMotivo;
	private java.lang.Long integracao;
	private java.lang.Long ativo;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_motivo_visita"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_motivo_visita
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_motivo_visita
	 * @param descricao the ds_motivo_visita value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: tp_motivo
	 */
	public java.lang.Long getTipoMotivo () {
		return getPropertyValue(this, tipoMotivo, PROP_TIPO_MOTIVO); 
	}

	/**
	 * Set the value related to the column: tp_motivo
	 * @param tipoMotivo the tp_motivo value
	 */
	public void setTipoMotivo (java.lang.Long tipoMotivo) {
//        java.lang.Long tipoMotivoOld = this.tipoMotivo;
		this.tipoMotivo = tipoMotivo;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoMotivo", tipoMotivoOld, tipoMotivo);
	}



	/**
	 * Return the value associated with the column: cd_integracao
	 */
	public java.lang.Long getIntegracao () {
		return getPropertyValue(this, integracao, PROP_INTEGRACAO); 
	}

	/**
	 * Set the value related to the column: cd_integracao
	 * @param integracao the cd_integracao value
	 */
	public void setIntegracao (java.lang.Long integracao) {
//        java.lang.Long integracaoOld = this.integracao;
		this.integracao = integracao;
//        this.getPropertyChangeSupport().firePropertyChange ("integracao", integracaoOld, integracao);
	}



	/**
	 * Return the value associated with the column: ativo
	 */
	public java.lang.Long getAtivo () {
		return getPropertyValue(this, ativo, PROP_ATIVO); 
	}

	/**
	 * Set the value related to the column: ativo
	 * @param ativo the ativo value
	 */
	public void setAtivo (java.lang.Long ativo) {
//        java.lang.Long ativoOld = this.ativo;
		this.ativo = ativo;
//        this.getPropertyChangeSupport().firePropertyChange ("ativo", ativoOld, ativo);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.cadsus.MotivoVisitaDomiciliar)) return false;
		else {
			br.com.ksisolucoes.vo.cadsus.MotivoVisitaDomiciliar motivoVisitaDomiciliar = (br.com.ksisolucoes.vo.cadsus.MotivoVisitaDomiciliar) obj;
			if (null == this.getCodigo() || null == motivoVisitaDomiciliar.getCodigo()) return false;
			else return (this.getCodigo().equals(motivoVisitaDomiciliar.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}