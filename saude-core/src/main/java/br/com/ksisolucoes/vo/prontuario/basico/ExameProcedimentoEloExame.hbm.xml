<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="ExameProcedimentoEloExame" table="exame_procedimento_elo_exame" >
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_exame_proc_elo_exame"
        >
            <generator class="assigned" />
        </id> 

        <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento"
            name="exameProcedimento"
            column="cd_exame_procedimento"
            not-null="true"
        />   

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento"
            name="exameProcedimentoRelacionado"
            column="cd_exame_procedimento_relacionado"
            not-null="true"
        />
    </class>
</hibernate-mapping>
