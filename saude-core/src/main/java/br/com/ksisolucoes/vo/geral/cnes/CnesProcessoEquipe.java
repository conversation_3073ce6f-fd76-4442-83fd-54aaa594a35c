package br.com.ksisolucoes.vo.geral.cnes;

import br.com.ksisolucoes.vo.geral.cnes.base.BaseCnesProcessoEquipe;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;



public class CnesProcessoEquipe extends BaseCnesProcessoEquipe implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public CnesProcessoEquipe () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public CnesProcessoEquipe (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public CnesProcessoEquipe (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.geral.cnes.CnesProcessoEmpresa cnesProcessoEmpresa,
		java.lang.String codigoMunicipio,
		java.lang.String codigoArea,
		java.lang.Long sequencialEquipe,
		java.lang.String descricaoArea,
		java.lang.String unidadeId,
		java.lang.String tipoEquipe) {

		super (
			codigo,
			cnesProcessoEmpresa,
			codigoMunicipio,
			codigoArea,
			sequencialEquipe,
			descricaoArea,
			unidadeId,
			tipoEquipe);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}