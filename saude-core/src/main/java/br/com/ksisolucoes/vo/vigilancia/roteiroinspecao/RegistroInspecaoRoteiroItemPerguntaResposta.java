package br.com.ksisolucoes.vo.vigilancia.roteiroinspecao;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import java.io.Serializable;

import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.base.BaseRegistroInspecaoRoteiroItemPerguntaResposta;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

public class RegistroInspecaoRoteiroItemPerguntaResposta extends BaseRegistroInspecaoRoteiroItemPerguntaResposta implements CodigoManager {

    private static final long serialVersionUID = 1L;

    public static enum Resposta implements IEnum {

        SIM(1L, Bundle.getStringApplication("rotulo_sim")),
        NAO(2L, Bundle.getStringApplication("rotulo_nao")),
        NAO_APLICA(3L, Bundle.getStringApplication("rotulo_nao_se_aplica")),
        CONFORMIDADE(4L, Bundle.getStringApplication("rotulo_conformidade"));

        private Long value;
        private String descricao;

        private Resposta(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        public static Resposta valueOf(Long resposta) {
            for (Resposta item : Resposta.values()) {
                if (item.value().equals(resposta)) {
                    return item;
                }
            }
            return null;
        }
    }

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public RegistroInspecaoRoteiroItemPerguntaResposta () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RegistroInspecaoRoteiroItemPerguntaResposta (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RegistroInspecaoRoteiroItemPerguntaResposta (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecaoRoteiroItem registroInspecaoRoteiroItem,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecaoRoteiroItemPerguntaResposta registroInspecaoRoteiroItemPerguntaRespostaOrigem,
		java.lang.String pergunta,
		java.lang.Long resposta,
		java.util.Date dataUsuario) {

		super (
			codigo,
			registroInspecaoRoteiroItem,
			usuario,
			registroInspecaoRoteiroItemPerguntaRespostaOrigem,
			pergunta,
			resposta,
			dataUsuario);
	}

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getDescricaoClassificacao() {
        return ItemInspecaoPergunta.getDescricaoClassificacao(getClassificacao());
    }

    public String getDescricaoResposta() {
        Resposta resp = Resposta.valueOf(getResposta());
        if (resp != null) {
            return resp.descricao();
        }
        return null;
    }
}
