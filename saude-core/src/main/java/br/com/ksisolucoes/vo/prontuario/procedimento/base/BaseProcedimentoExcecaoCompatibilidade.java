package br.com.ksisolucoes.vo.prontuario.procedimento.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the procedimento_excecao_comp table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="procedimento_excecao_comp"
 */

public abstract class BaseProcedimentoExcecaoCompatibilidade extends BaseRootVO implements Serializable {

	public static String REF = "ProcedimentoExcecaoCompatibilidade";
	public static final String PROP_ID = "id";
	public static final String PROP_TIPO_COMPATIBILIDADE = "tipoCompatibilidade";


	// constructors
	public BaseProcedimentoExcecaoCompatibilidade () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseProcedimentoExcecaoCompatibilidade (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoExcecaoCompatibilidadePK id) {
		this.setId(id);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseProcedimentoExcecaoCompatibilidade (
		br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoExcecaoCompatibilidadePK id,
		java.lang.Long tipoCompatibilidade) {

		this.setId(id);
		this.setTipoCompatibilidade(tipoCompatibilidade);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoExcecaoCompatibilidadePK id;

	// fields
	private java.lang.Long tipoCompatibilidade;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     */
	public br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoExcecaoCompatibilidadePK getId () {
	    return getPropertyValue(this,  id, "id" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param id the new ID
	 */
	public void setId (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoExcecaoCompatibilidadePK id) {
		this.id = id;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: tp_compatibilidade
	 */
	public java.lang.Long getTipoCompatibilidade () {
		return getPropertyValue(this, tipoCompatibilidade, PROP_TIPO_COMPATIBILIDADE); 
	}

	/**
	 * Set the value related to the column: tp_compatibilidade
	 * @param tipoCompatibilidade the tp_compatibilidade value
	 */
	public void setTipoCompatibilidade (java.lang.Long tipoCompatibilidade) {
//        java.lang.Long tipoCompatibilidadeOld = this.tipoCompatibilidade;
		this.tipoCompatibilidade = tipoCompatibilidade;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoCompatibilidade", tipoCompatibilidadeOld, tipoCompatibilidade);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoExcecaoCompatibilidade)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoExcecaoCompatibilidade procedimentoExcecaoCompatibilidade = (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoExcecaoCompatibilidade) obj;
			if (null == this.getId() || null == procedimentoExcecaoCompatibilidade.getId()) return false;
			else return (this.getId().equals(procedimentoExcecaoCompatibilidade.getId()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getId()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getId().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}