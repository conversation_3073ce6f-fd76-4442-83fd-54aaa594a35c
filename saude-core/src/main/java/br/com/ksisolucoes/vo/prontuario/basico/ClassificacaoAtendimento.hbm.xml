<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="ClassificacaoAtendimento" table="classificacao_atendimento" >
         
        <id
            name="codigo"
            column="cd_cla_atendimento"
            type="java.lang.Long"
        /> <version column="version" name="version" type="long" />
          
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
            column="cd_procedimento"
            name="procedimento"
        />
         
        <property 
	        name="descricao"
	        column="ds_cla_atendimento"
	        type="java.lang.String"
	        length="50"
	        not-null="true"
        />
        
        <property 
	        name="codigoSiab"
	        column="cd_siab"
	        type="java.lang.Long"
        />
        
        <property 
	        name="ordem"
	        column="ordem"
	        type="java.lang.Integer"
        />
        <property
                name="exibeEncaminhamentoAlta"
                column="flag_exibe_encaminhamento_alta"
                type="java.lang.Long"
        />

		<property 
            name="codigoEsus"
            column="cd_esus"
            type="java.lang.String"
            not-null="false"
        />

		<property 
            name="classificacaoEsus"
            column="classificacao_esus"
            type="java.lang.Long"
            not-null="false"
        />

        <property
                name="situacao"
                column="situacao"
                type="java.lang.Long"
                not-null="true"
        />

    </class>
</hibernate-mapping>
