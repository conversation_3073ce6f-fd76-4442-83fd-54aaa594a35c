<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vacina">
    <class name="ViaAdministracaoLocalAplicacao" table="via_adm_local_aplicacao">
        <id
                column="cd_via_adm_local_aplicacao"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="sequence">
                <param name="sequence">seq_via_adm_local_aplicacao</param>
            </generator>
        </id>
        <version column="version" name="version" type="long"/>

        <many-to-one
                class="br.com.ksisolucoes.vo.vacina.LocalAplicacao"
                column="cd_local_aplicacao"
                name="localAplicacao"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vacina.ViaAdministracao"
                column="cd_via_administracao"
                name="viaAdministracao"
                not-null="true"
        />

    </class>
</hibernate-mapping>
