package br.com.ksisolucoes.vo.vigilancia.requerimentos;

import java.io.Serializable;

import br.com.ksisolucoes.vo.vigilancia.requerimentos.base.BaseRequerimentoDeclaracaoVisaOutros;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class RequerimentoDeclaracaoVisaOutros extends BaseRequerimentoDeclaracaoVisaOutros implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequerimentoDeclaracaoVisaOutros () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequerimentoDeclaracaoVisaOutros (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequerimentoDeclaracaoVisaOutros (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia,
		java.lang.String titulo,
		java.lang.String observacao,
		java.util.Date dataCadastro) {

		super (
			codigo,
			requerimentoVigilancia,
			titulo,
			observacao,
			dataCadastro);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}