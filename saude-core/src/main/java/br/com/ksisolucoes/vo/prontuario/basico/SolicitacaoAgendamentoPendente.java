package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseSolicitacaoAgendamentoPendente;

import java.io.Serializable;



public class SolicitacaoAgendamentoPendente extends BaseSolicitacaoAgendamentoPendente implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public SolicitacaoAgendamentoPendente () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public SolicitacaoAgendamentoPendente (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public SolicitacaoAgendamentoPendente (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento solicitacaoAgendamento,
		br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimento) {

		super (
			codigo,
			solicitacaoAgendamento,
			tipoProcedimento);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}