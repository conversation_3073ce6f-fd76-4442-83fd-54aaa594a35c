package br.com.ksisolucoes.vo.atendimento.recepcao.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the nodo_recepcao_unidade table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="nodo_recepcao_unidade"
 */

public abstract class BaseNodoRecepcaoUnidade extends BaseRootVO implements Serializable {

	public static String REF = "NodoRecepcaoUnidade";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_ORDEM = "ordem";
	public static final String PROP_CLASSE_NODO = "classeNodo";


	// constructors
	public BaseNodoRecepcaoUnidade () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseNodoRecepcaoUnidade (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String classeNodo;
	private java.lang.Long ordem;

	// many to one
	private br.com.ksisolucoes.vo.basico.Empresa empresa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_nodo_rec_unidade"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: classe_nodo
	 */
	public java.lang.String getClasseNodo () {
		return getPropertyValue(this, classeNodo, PROP_CLASSE_NODO); 
	}

	/**
	 * Set the value related to the column: classe_nodo
	 * @param classeNodo the classe_nodo value
	 */
	public void setClasseNodo (java.lang.String classeNodo) {
//        java.lang.String classeNodoOld = this.classeNodo;
		this.classeNodo = classeNodo;
//        this.getPropertyChangeSupport().firePropertyChange ("classeNodo", classeNodoOld, classeNodo);
	}



	/**
	 * Return the value associated with the column: ordem
	 */
	public java.lang.Long getOrdem () {
		return getPropertyValue(this, ordem, PROP_ORDEM); 
	}

	/**
	 * Set the value related to the column: ordem
	 * @param ordem the ordem value
	 */
	public void setOrdem (java.lang.Long ordem) {
//        java.lang.Long ordemOld = this.ordem;
		this.ordem = ordem;
//        this.getPropertyChangeSupport().firePropertyChange ("ordem", ordemOld, ordem);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.atendimento.recepcao.NodoRecepcaoUnidade)) return false;
		else {
			br.com.ksisolucoes.vo.atendimento.recepcao.NodoRecepcaoUnidade nodoRecepcaoUnidade = (br.com.ksisolucoes.vo.atendimento.recepcao.NodoRecepcaoUnidade) obj;
			if (null == this.getCodigo() || null == nodoRecepcaoUnidade.getCodigo()) return false;
			else return (this.getCodigo().equals(nodoRecepcaoUnidade.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}