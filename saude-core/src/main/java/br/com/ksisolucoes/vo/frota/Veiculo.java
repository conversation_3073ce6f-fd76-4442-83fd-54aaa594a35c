package br.com.ksisolucoes.vo.frota;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.frota.base.BaseVeiculo;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;

import java.io.Serializable;


public class Veiculo extends BaseVei<PERSON>lo implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

	public static final String PROP_DESCRICAO_FORMATADO = "descricaoFormatado";

/*[CONSTRUCTOR MARKER BEGIN]*/
	public Veiculo () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public Veiculo (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public Veiculo (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.programasaude.ProgramaSaude programaSaude,
		java.util.Date dataCompra,
		java.lang.Long situacao) {

		super (
			codigo,
			programaSaude,
			dataCompra,
			situacao);
	}

	public enum Situacao implements IEnum {
		ATIVO(0L, Bundle.getStringApplication("rotulo_ativo")),
		INATIVO(1L, Bundle.getStringApplication("rotulo_inativo"));


		private Long value;
		private String descricao;

		private Situacao(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static Situacao valeuOf(Long value) {
			for (Situacao situacao : Situacao.values()) {
				if (situacao.value().equals(value)) {
					return situacao;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}

	}

	public String getDescricaoSituacaoFormatada() {
		Situacao situacao = Situacao.valeuOf(this.getSituacao());

		if (situacao != null) {
			return situacao.descricao();
		}
		return "";
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

	@Override
	public String getDescricaoVO() {
		return getDescricao();
	}

	@Override
	public String getIdentificador() {
		return Coalesce.asString(getCodigo());
	}

	public String getDescricaoFormatado(){
		if (getDescricao() == null && getReferencia() == null) {
			return null;
		} else if (getReferencia() == null) {
			return getDescricao();
		} else {
			return "(" + getReferencia() + ") " + getDescricao();
		}
	}

}