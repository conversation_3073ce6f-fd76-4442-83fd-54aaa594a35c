package br.com.ksisolucoes.vo.vigilancia.investigacao.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the investigacao_agr_tetano_acidental table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agr_tetano_acidental"
 */

public abstract class BaseInvestigacaoAgravoTetanoAcidental extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravoTetanoAcidental";
	public static final String PROP_DATA_INVESTIGACAO = "dataInvestigacao";
	public static final String PROP_MANIFESTACAO_CLINICA_RIGIDEZ_NUCA = "manifestacaoClinicaRigidezNuca";
	public static final String PROP_LOCAL_PROVAVEL_INFECCAO = "localProvavelInfeccao";
	public static final String PROP_USUARIO_ENCERRAMENTO = "usuarioEncerramento";
	public static final String PROP_LOCAL_LESAO = "localLesao";
	public static final String PROP_FLAG_INFORMACOES_COMPLEMENTARES = "flagInformacoesComplementares";
	public static final String PROP_BAIRRO_LOCAL_INFECCAO = "bairroLocalInfeccao";
	public static final String PROP_TRATAMENTO_ESPECIFICO = "tratamentoEspecifico";
	public static final String PROP_MANIFESTACAO_CLINICA_TRISMO = "manifestacaoClinicaTrismo";
	public static final String PROP_DISTRITO_LOCAL_INFECCAO = "distritoLocalInfeccao";
	public static final String PROP_DATA_ENCERRAMENTO = "dataEncerramento";
	public static final String PROP_MANIFESTACAO_CLINICA_RIGIDEZ_MEMBROS = "manifestacaoClinicaRigidezMembros";
	public static final String PROP_CASO_AUTOCTONE = "casoAutoctone";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_DATA_OBITO = "dataObito";
	public static final String PROP_HOSPITAL = "hospital";
	public static final String PROP_CIDADE_LOCAL_INFECCAO = "cidadeLocalInfeccao";
	public static final String PROP_POSSIVEL_CAUSA = "possivelCausa";
	public static final String PROP_DATA_ULTIMA_DOSE = "dataUltimaDose";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_MEDIDA_CONTROLE_ANALISE_COBERTURA_VACINAL = "medidaControleAnaliseCoberturaVacinal";
	public static final String PROP_MANIFESTACAO_CLINICA_OPISTOTONO = "manifestacaoClinicaOpistotono";
	public static final String PROP_HOSPITALIZACAO = "hospitalizacao";
	public static final String PROP_REGISTRO_AGRAVO = "registroAgravo";
	public static final String PROP_NUMERO_DOSES_APLICADAS = "numeroDosesAplicadas";
	public static final String PROP_OCUPACAO_CBO = "ocupacaoCbo";
	public static final String PROP_MANIFESTACAO_CLINICA_RISO_SARDONICO = "manifestacaoClinicaRisoSardonico";
	public static final String PROP_EVOLUCAO_CASO = "evolucaoCaso";
	public static final String PROP_MEDIDA_CONTROLE_IDENTIFICAR_POPULACAO = "medidaControleIdentificarPopulacao";
	public static final String PROP_CLASSIFICACAOFINAL = "classificacaofinal";
	public static final String PROP_PAIS_LOCAL_INFECCAO = "paisLocalInfeccao";
	public static final String PROP_MANIFESTACAO_CLINICA_CRISES_CONTRATURAS = "manifestacaoClinicaCrisesContraturas";
	public static final String PROP_MANIFESTACAO_CLINICA_OUTROS = "manifestacaoClinicaOutros";
	public static final String PROP_POSSIVEL_CAUSA_OUTROS = "possivelCausaOutros";
	public static final String PROP_ORIGEM_CASO = "origemCaso";
	public static final String PROP_MEDIDA_CONTROLE_VACINACAO_POPULACAO = "medidaControleVacinacaoPopulacao";
	public static final String PROP_MANIFESTACAO_CLINICA_RIGIDEZ_ABOMINAL = "manifestacaoClinicaRigidezAbominal";
	public static final String PROP_DATA_INTERNACAO = "dataInternacao";


	// constructors
	public BaseInvestigacaoAgravoTetanoAcidental () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravoTetanoAcidental (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseInvestigacaoAgravoTetanoAcidental (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
		br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo,
		java.lang.String flagInformacoesComplementares) {

		this.setCodigo(codigo);
		this.setRegistroAgravo(registroAgravo);
		this.setOcupacaoCbo(ocupacaoCbo);
		this.setFlagInformacoesComplementares(flagInformacoesComplementares);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String flagInformacoesComplementares;
	private java.util.Date dataInvestigacao;
	private java.lang.Long possivelCausa;
	private java.lang.String possivelCausaOutros;
	private java.lang.Long localLesao;
	private java.lang.Long numeroDosesAplicadas;
	private java.util.Date dataUltimaDose;
	private java.lang.Long tratamentoEspecifico;
	private java.lang.Long manifestacaoClinicaTrismo;
	private java.lang.Long manifestacaoClinicaRisoSardonico;
	private java.lang.Long manifestacaoClinicaOpistotono;
	private java.lang.Long manifestacaoClinicaRigidezNuca;
	private java.lang.Long manifestacaoClinicaRigidezAbominal;
	private java.lang.Long manifestacaoClinicaRigidezMembros;
	private java.lang.Long manifestacaoClinicaCrisesContraturas;
	private java.lang.String manifestacaoClinicaOutros;
	private java.lang.Long origemCaso;
	private java.lang.Long hospitalizacao;
	private java.util.Date dataInternacao;
	private java.lang.Long medidaControleIdentificarPopulacao;
	private java.lang.Long medidaControleVacinacaoPopulacao;
	private java.lang.Long medidaControleAnaliseCoberturaVacinal;
	private java.lang.Long casoAutoctone;
	private java.lang.String distritoLocalInfeccao;
	private java.lang.String bairroLocalInfeccao;
	private java.lang.Long localProvavelInfeccao;
	private java.lang.Long classificacaofinal;
	private java.lang.Long evolucaoCaso;
	private java.util.Date dataObito;
	private java.lang.String observacao;
	private java.util.Date dataEncerramento;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo;
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo;
	private br.com.ksisolucoes.vo.basico.Empresa hospital;
	private br.com.ksisolucoes.vo.basico.Cidade cidadeLocalInfeccao;
	private br.com.ksisolucoes.vo.basico.Pais paisLocalInfeccao;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="codigo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: flag_informacoes_complementares
	 */
	public java.lang.String getFlagInformacoesComplementares () {
		return getPropertyValue(this, flagInformacoesComplementares, PROP_FLAG_INFORMACOES_COMPLEMENTARES); 
	}

	/**
	 * Set the value related to the column: flag_informacoes_complementares
	 * @param flagInformacoesComplementares the flag_informacoes_complementares value
	 */
	public void setFlagInformacoesComplementares (java.lang.String flagInformacoesComplementares) {
//        java.lang.String flagInformacoesComplementaresOld = this.flagInformacoesComplementares;
		this.flagInformacoesComplementares = flagInformacoesComplementares;
//        this.getPropertyChangeSupport().firePropertyChange ("flagInformacoesComplementares", flagInformacoesComplementaresOld, flagInformacoesComplementares);
	}



	/**
	 * Return the value associated with the column: dt_investigacao
	 */
	public java.util.Date getDataInvestigacao () {
		return getPropertyValue(this, dataInvestigacao, PROP_DATA_INVESTIGACAO); 
	}

	/**
	 * Set the value related to the column: dt_investigacao
	 * @param dataInvestigacao the dt_investigacao value
	 */
	public void setDataInvestigacao (java.util.Date dataInvestigacao) {
//        java.util.Date dataInvestigacaoOld = this.dataInvestigacao;
		this.dataInvestigacao = dataInvestigacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInvestigacao", dataInvestigacaoOld, dataInvestigacao);
	}



	/**
	 * Return the value associated with the column: possivel_causa
	 */
	public java.lang.Long getPossivelCausa () {
		return getPropertyValue(this, possivelCausa, PROP_POSSIVEL_CAUSA); 
	}

	/**
	 * Set the value related to the column: possivel_causa
	 * @param possivelCausa the possivel_causa value
	 */
	public void setPossivelCausa (java.lang.Long possivelCausa) {
//        java.lang.Long possivelCausaOld = this.possivelCausa;
		this.possivelCausa = possivelCausa;
//        this.getPropertyChangeSupport().firePropertyChange ("possivelCausa", possivelCausaOld, possivelCausa);
	}



	/**
	 * Return the value associated with the column: possivel_causa_outros
	 */
	public java.lang.String getPossivelCausaOutros () {
		return getPropertyValue(this, possivelCausaOutros, PROP_POSSIVEL_CAUSA_OUTROS); 
	}

	/**
	 * Set the value related to the column: possivel_causa_outros
	 * @param possivelCausaOutros the possivel_causa_outros value
	 */
	public void setPossivelCausaOutros (java.lang.String possivelCausaOutros) {
//        java.lang.String possivelCausaOutrosOld = this.possivelCausaOutros;
		this.possivelCausaOutros = possivelCausaOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("possivelCausaOutros", possivelCausaOutrosOld, possivelCausaOutros);
	}



	/**
	 * Return the value associated with the column: local_lesao
	 */
	public java.lang.Long getLocalLesao () {
		return getPropertyValue(this, localLesao, PROP_LOCAL_LESAO); 
	}

	/**
	 * Set the value related to the column: local_lesao
	 * @param localLesao the local_lesao value
	 */
	public void setLocalLesao (java.lang.Long localLesao) {
//        java.lang.Long localLesaoOld = this.localLesao;
		this.localLesao = localLesao;
//        this.getPropertyChangeSupport().firePropertyChange ("localLesao", localLesaoOld, localLesao);
	}



	/**
	 * Return the value associated with the column: numero_doses_aplicadas
	 */
	public java.lang.Long getNumeroDosesAplicadas () {
		return getPropertyValue(this, numeroDosesAplicadas, PROP_NUMERO_DOSES_APLICADAS); 
	}

	/**
	 * Set the value related to the column: numero_doses_aplicadas
	 * @param numeroDosesAplicadas the numero_doses_aplicadas value
	 */
	public void setNumeroDosesAplicadas (java.lang.Long numeroDosesAplicadas) {
//        java.lang.Long numeroDosesAplicadasOld = this.numeroDosesAplicadas;
		this.numeroDosesAplicadas = numeroDosesAplicadas;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDosesAplicadas", numeroDosesAplicadasOld, numeroDosesAplicadas);
	}



	/**
	 * Return the value associated with the column: dt_ultima_dose
	 */
	public java.util.Date getDataUltimaDose () {
		return getPropertyValue(this, dataUltimaDose, PROP_DATA_ULTIMA_DOSE); 
	}

	/**
	 * Set the value related to the column: dt_ultima_dose
	 * @param dataUltimaDose the dt_ultima_dose value
	 */
	public void setDataUltimaDose (java.util.Date dataUltimaDose) {
//        java.util.Date dataUltimaDoseOld = this.dataUltimaDose;
		this.dataUltimaDose = dataUltimaDose;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUltimaDose", dataUltimaDoseOld, dataUltimaDose);
	}



	/**
	 * Return the value associated with the column: tratamento_especifico
	 */
	public java.lang.Long getTratamentoEspecifico () {
		return getPropertyValue(this, tratamentoEspecifico, PROP_TRATAMENTO_ESPECIFICO); 
	}

	/**
	 * Set the value related to the column: tratamento_especifico
	 * @param tratamentoEspecifico the tratamento_especifico value
	 */
	public void setTratamentoEspecifico (java.lang.Long tratamentoEspecifico) {
//        java.lang.Long tratamentoEspecificoOld = this.tratamentoEspecifico;
		this.tratamentoEspecifico = tratamentoEspecifico;
//        this.getPropertyChangeSupport().firePropertyChange ("tratamentoEspecifico", tratamentoEspecificoOld, tratamentoEspecifico);
	}



	/**
	 * Return the value associated with the column: manifestacao_clinica_trismo
	 */
	public java.lang.Long getManifestacaoClinicaTrismo () {
		return getPropertyValue(this, manifestacaoClinicaTrismo, PROP_MANIFESTACAO_CLINICA_TRISMO); 
	}

	/**
	 * Set the value related to the column: manifestacao_clinica_trismo
	 * @param manifestacaoClinicaTrismo the manifestacao_clinica_trismo value
	 */
	public void setManifestacaoClinicaTrismo (java.lang.Long manifestacaoClinicaTrismo) {
//        java.lang.Long manifestacaoClinicaTrismoOld = this.manifestacaoClinicaTrismo;
		this.manifestacaoClinicaTrismo = manifestacaoClinicaTrismo;
//        this.getPropertyChangeSupport().firePropertyChange ("manifestacaoClinicaTrismo", manifestacaoClinicaTrismoOld, manifestacaoClinicaTrismo);
	}



	/**
	 * Return the value associated with the column: manifestacao_clinica_riso_sardonico
	 */
	public java.lang.Long getManifestacaoClinicaRisoSardonico () {
		return getPropertyValue(this, manifestacaoClinicaRisoSardonico, PROP_MANIFESTACAO_CLINICA_RISO_SARDONICO); 
	}

	/**
	 * Set the value related to the column: manifestacao_clinica_riso_sardonico
	 * @param manifestacaoClinicaRisoSardonico the manifestacao_clinica_riso_sardonico value
	 */
	public void setManifestacaoClinicaRisoSardonico (java.lang.Long manifestacaoClinicaRisoSardonico) {
//        java.lang.Long manifestacaoClinicaRisoSardonicoOld = this.manifestacaoClinicaRisoSardonico;
		this.manifestacaoClinicaRisoSardonico = manifestacaoClinicaRisoSardonico;
//        this.getPropertyChangeSupport().firePropertyChange ("manifestacaoClinicaRisoSardonico", manifestacaoClinicaRisoSardonicoOld, manifestacaoClinicaRisoSardonico);
	}



	/**
	 * Return the value associated with the column: manifestacao_clinica_opistotono
	 */
	public java.lang.Long getManifestacaoClinicaOpistotono () {
		return getPropertyValue(this, manifestacaoClinicaOpistotono, PROP_MANIFESTACAO_CLINICA_OPISTOTONO); 
	}

	/**
	 * Set the value related to the column: manifestacao_clinica_opistotono
	 * @param manifestacaoClinicaOpistotono the manifestacao_clinica_opistotono value
	 */
	public void setManifestacaoClinicaOpistotono (java.lang.Long manifestacaoClinicaOpistotono) {
//        java.lang.Long manifestacaoClinicaOpistotonoOld = this.manifestacaoClinicaOpistotono;
		this.manifestacaoClinicaOpistotono = manifestacaoClinicaOpistotono;
//        this.getPropertyChangeSupport().firePropertyChange ("manifestacaoClinicaOpistotono", manifestacaoClinicaOpistotonoOld, manifestacaoClinicaOpistotono);
	}



	/**
	 * Return the value associated with the column: manifestacao_clinica_rigidez_nuca
	 */
	public java.lang.Long getManifestacaoClinicaRigidezNuca () {
		return getPropertyValue(this, manifestacaoClinicaRigidezNuca, PROP_MANIFESTACAO_CLINICA_RIGIDEZ_NUCA); 
	}

	/**
	 * Set the value related to the column: manifestacao_clinica_rigidez_nuca
	 * @param manifestacaoClinicaRigidezNuca the manifestacao_clinica_rigidez_nuca value
	 */
	public void setManifestacaoClinicaRigidezNuca (java.lang.Long manifestacaoClinicaRigidezNuca) {
//        java.lang.Long manifestacaoClinicaRigidezNucaOld = this.manifestacaoClinicaRigidezNuca;
		this.manifestacaoClinicaRigidezNuca = manifestacaoClinicaRigidezNuca;
//        this.getPropertyChangeSupport().firePropertyChange ("manifestacaoClinicaRigidezNuca", manifestacaoClinicaRigidezNucaOld, manifestacaoClinicaRigidezNuca);
	}



	/**
	 * Return the value associated with the column: manifestacao_clinica_rigidez_abdominal
	 */
	public java.lang.Long getManifestacaoClinicaRigidezAbominal () {
		return getPropertyValue(this, manifestacaoClinicaRigidezAbominal, PROP_MANIFESTACAO_CLINICA_RIGIDEZ_ABOMINAL); 
	}

	/**
	 * Set the value related to the column: manifestacao_clinica_rigidez_abdominal
	 * @param manifestacaoClinicaRigidezAbominal the manifestacao_clinica_rigidez_abdominal value
	 */
	public void setManifestacaoClinicaRigidezAbominal (java.lang.Long manifestacaoClinicaRigidezAbominal) {
//        java.lang.Long manifestacaoClinicaRigidezAbominalOld = this.manifestacaoClinicaRigidezAbominal;
		this.manifestacaoClinicaRigidezAbominal = manifestacaoClinicaRigidezAbominal;
//        this.getPropertyChangeSupport().firePropertyChange ("manifestacaoClinicaRigidezAbominal", manifestacaoClinicaRigidezAbominalOld, manifestacaoClinicaRigidezAbominal);
	}



	/**
	 * Return the value associated with the column: manifestacao_clinica_rigidez_membros
	 */
	public java.lang.Long getManifestacaoClinicaRigidezMembros () {
		return getPropertyValue(this, manifestacaoClinicaRigidezMembros, PROP_MANIFESTACAO_CLINICA_RIGIDEZ_MEMBROS); 
	}

	/**
	 * Set the value related to the column: manifestacao_clinica_rigidez_membros
	 * @param manifestacaoClinicaRigidezMembros the manifestacao_clinica_rigidez_membros value
	 */
	public void setManifestacaoClinicaRigidezMembros (java.lang.Long manifestacaoClinicaRigidezMembros) {
//        java.lang.Long manifestacaoClinicaRigidezMembrosOld = this.manifestacaoClinicaRigidezMembros;
		this.manifestacaoClinicaRigidezMembros = manifestacaoClinicaRigidezMembros;
//        this.getPropertyChangeSupport().firePropertyChange ("manifestacaoClinicaRigidezMembros", manifestacaoClinicaRigidezMembrosOld, manifestacaoClinicaRigidezMembros);
	}



	/**
	 * Return the value associated with the column: manifestacao_clinica_crises_contraturas
	 */
	public java.lang.Long getManifestacaoClinicaCrisesContraturas () {
		return getPropertyValue(this, manifestacaoClinicaCrisesContraturas, PROP_MANIFESTACAO_CLINICA_CRISES_CONTRATURAS); 
	}

	/**
	 * Set the value related to the column: manifestacao_clinica_crises_contraturas
	 * @param manifestacaoClinicaCrisesContraturas the manifestacao_clinica_crises_contraturas value
	 */
	public void setManifestacaoClinicaCrisesContraturas (java.lang.Long manifestacaoClinicaCrisesContraturas) {
//        java.lang.Long manifestacaoClinicaCrisesContraturasOld = this.manifestacaoClinicaCrisesContraturas;
		this.manifestacaoClinicaCrisesContraturas = manifestacaoClinicaCrisesContraturas;
//        this.getPropertyChangeSupport().firePropertyChange ("manifestacaoClinicaCrisesContraturas", manifestacaoClinicaCrisesContraturasOld, manifestacaoClinicaCrisesContraturas);
	}



	/**
	 * Return the value associated with the column: manifestacao_clinica_outros
	 */
	public java.lang.String getManifestacaoClinicaOutros () {
		return getPropertyValue(this, manifestacaoClinicaOutros, PROP_MANIFESTACAO_CLINICA_OUTROS); 
	}

	/**
	 * Set the value related to the column: manifestacao_clinica_outros
	 * @param manifestacaoClinicaOutros the manifestacao_clinica_outros value
	 */
	public void setManifestacaoClinicaOutros (java.lang.String manifestacaoClinicaOutros) {
//        java.lang.String manifestacaoClinicaOutrosOld = this.manifestacaoClinicaOutros;
		this.manifestacaoClinicaOutros = manifestacaoClinicaOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("manifestacaoClinicaOutros", manifestacaoClinicaOutrosOld, manifestacaoClinicaOutros);
	}



	/**
	 * Return the value associated with the column: origem_caso
	 */
	public java.lang.Long getOrigemCaso () {
		return getPropertyValue(this, origemCaso, PROP_ORIGEM_CASO); 
	}

	/**
	 * Set the value related to the column: origem_caso
	 * @param origemCaso the origem_caso value
	 */
	public void setOrigemCaso (java.lang.Long origemCaso) {
//        java.lang.Long origemCasoOld = this.origemCaso;
		this.origemCaso = origemCaso;
//        this.getPropertyChangeSupport().firePropertyChange ("origemCaso", origemCasoOld, origemCaso);
	}



	/**
	 * Return the value associated with the column: hospitalizacao
	 */
	public java.lang.Long getHospitalizacao () {
		return getPropertyValue(this, hospitalizacao, PROP_HOSPITALIZACAO); 
	}

	/**
	 * Set the value related to the column: hospitalizacao
	 * @param hospitalizacao the hospitalizacao value
	 */
	public void setHospitalizacao (java.lang.Long hospitalizacao) {
//        java.lang.Long hospitalizacaoOld = this.hospitalizacao;
		this.hospitalizacao = hospitalizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("hospitalizacao", hospitalizacaoOld, hospitalizacao);
	}



	/**
	 * Return the value associated with the column: dt_internacao
	 */
	public java.util.Date getDataInternacao () {
		return getPropertyValue(this, dataInternacao, PROP_DATA_INTERNACAO); 
	}

	/**
	 * Set the value related to the column: dt_internacao
	 * @param dataInternacao the dt_internacao value
	 */
	public void setDataInternacao (java.util.Date dataInternacao) {
//        java.util.Date dataInternacaoOld = this.dataInternacao;
		this.dataInternacao = dataInternacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInternacao", dataInternacaoOld, dataInternacao);
	}



	/**
	 * Return the value associated with the column: medida_controle_identificar_populacao
	 */
	public java.lang.Long getMedidaControleIdentificarPopulacao () {
		return getPropertyValue(this, medidaControleIdentificarPopulacao, PROP_MEDIDA_CONTROLE_IDENTIFICAR_POPULACAO); 
	}

	/**
	 * Set the value related to the column: medida_controle_identificar_populacao
	 * @param medidaControleIdentificarPopulacao the medida_controle_identificar_populacao value
	 */
	public void setMedidaControleIdentificarPopulacao (java.lang.Long medidaControleIdentificarPopulacao) {
//        java.lang.Long medidaControleIdentificarPopulacaoOld = this.medidaControleIdentificarPopulacao;
		this.medidaControleIdentificarPopulacao = medidaControleIdentificarPopulacao;
//        this.getPropertyChangeSupport().firePropertyChange ("medidaControleIdentificarPopulacao", medidaControleIdentificarPopulacaoOld, medidaControleIdentificarPopulacao);
	}



	/**
	 * Return the value associated with the column: medida_controle_vacinacao_populacao
	 */
	public java.lang.Long getMedidaControleVacinacaoPopulacao () {
		return getPropertyValue(this, medidaControleVacinacaoPopulacao, PROP_MEDIDA_CONTROLE_VACINACAO_POPULACAO); 
	}

	/**
	 * Set the value related to the column: medida_controle_vacinacao_populacao
	 * @param medidaControleVacinacaoPopulacao the medida_controle_vacinacao_populacao value
	 */
	public void setMedidaControleVacinacaoPopulacao (java.lang.Long medidaControleVacinacaoPopulacao) {
//        java.lang.Long medidaControleVacinacaoPopulacaoOld = this.medidaControleVacinacaoPopulacao;
		this.medidaControleVacinacaoPopulacao = medidaControleVacinacaoPopulacao;
//        this.getPropertyChangeSupport().firePropertyChange ("medidaControleVacinacaoPopulacao", medidaControleVacinacaoPopulacaoOld, medidaControleVacinacaoPopulacao);
	}



	/**
	 * Return the value associated with the column: medida_controle_analise_cobertura_vacinal
	 */
	public java.lang.Long getMedidaControleAnaliseCoberturaVacinal () {
		return getPropertyValue(this, medidaControleAnaliseCoberturaVacinal, PROP_MEDIDA_CONTROLE_ANALISE_COBERTURA_VACINAL); 
	}

	/**
	 * Set the value related to the column: medida_controle_analise_cobertura_vacinal
	 * @param medidaControleAnaliseCoberturaVacinal the medida_controle_analise_cobertura_vacinal value
	 */
	public void setMedidaControleAnaliseCoberturaVacinal (java.lang.Long medidaControleAnaliseCoberturaVacinal) {
//        java.lang.Long medidaControleAnaliseCoberturaVacinalOld = this.medidaControleAnaliseCoberturaVacinal;
		this.medidaControleAnaliseCoberturaVacinal = medidaControleAnaliseCoberturaVacinal;
//        this.getPropertyChangeSupport().firePropertyChange ("medidaControleAnaliseCoberturaVacinal", medidaControleAnaliseCoberturaVacinalOld, medidaControleAnaliseCoberturaVacinal);
	}



	/**
	 * Return the value associated with the column: caso_autoctone
	 */
	public java.lang.Long getCasoAutoctone () {
		return getPropertyValue(this, casoAutoctone, PROP_CASO_AUTOCTONE); 
	}

	/**
	 * Set the value related to the column: caso_autoctone
	 * @param casoAutoctone the caso_autoctone value
	 */
	public void setCasoAutoctone (java.lang.Long casoAutoctone) {
//        java.lang.Long casoAutoctoneOld = this.casoAutoctone;
		this.casoAutoctone = casoAutoctone;
//        this.getPropertyChangeSupport().firePropertyChange ("casoAutoctone", casoAutoctoneOld, casoAutoctone);
	}



	/**
	 * Return the value associated with the column: str_distrito_infeccao
	 */
	public java.lang.String getDistritoLocalInfeccao () {
		return getPropertyValue(this, distritoLocalInfeccao, PROP_DISTRITO_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: str_distrito_infeccao
	 * @param distritoLocalInfeccao the str_distrito_infeccao value
	 */
	public void setDistritoLocalInfeccao (java.lang.String distritoLocalInfeccao) {
//        java.lang.String distritoLocalInfeccaoOld = this.distritoLocalInfeccao;
		this.distritoLocalInfeccao = distritoLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("distritoLocalInfeccao", distritoLocalInfeccaoOld, distritoLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: str_bairro_infeccao
	 */
	public java.lang.String getBairroLocalInfeccao () {
		return getPropertyValue(this, bairroLocalInfeccao, PROP_BAIRRO_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: str_bairro_infeccao
	 * @param bairroLocalInfeccao the str_bairro_infeccao value
	 */
	public void setBairroLocalInfeccao (java.lang.String bairroLocalInfeccao) {
//        java.lang.String bairroLocalInfeccaoOld = this.bairroLocalInfeccao;
		this.bairroLocalInfeccao = bairroLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("bairroLocalInfeccao", bairroLocalInfeccaoOld, bairroLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: local_provavel_infeccao
	 */
	public java.lang.Long getLocalProvavelInfeccao () {
		return getPropertyValue(this, localProvavelInfeccao, PROP_LOCAL_PROVAVEL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: local_provavel_infeccao
	 * @param localProvavelInfeccao the local_provavel_infeccao value
	 */
	public void setLocalProvavelInfeccao (java.lang.Long localProvavelInfeccao) {
//        java.lang.Long localProvavelInfeccaoOld = this.localProvavelInfeccao;
		this.localProvavelInfeccao = localProvavelInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("localProvavelInfeccao", localProvavelInfeccaoOld, localProvavelInfeccao);
	}



	/**
	 * Return the value associated with the column: classificacao_final
	 */
	public java.lang.Long getClassificacaofinal () {
		return getPropertyValue(this, classificacaofinal, PROP_CLASSIFICACAOFINAL); 
	}

	/**
	 * Set the value related to the column: classificacao_final
	 * @param classificacaofinal the classificacao_final value
	 */
	public void setClassificacaofinal (java.lang.Long classificacaofinal) {
//        java.lang.Long classificacaofinalOld = this.classificacaofinal;
		this.classificacaofinal = classificacaofinal;
//        this.getPropertyChangeSupport().firePropertyChange ("classificacaofinal", classificacaofinalOld, classificacaofinal);
	}



	/**
	 * Return the value associated with the column: evolucao_caso
	 */
	public java.lang.Long getEvolucaoCaso () {
		return getPropertyValue(this, evolucaoCaso, PROP_EVOLUCAO_CASO); 
	}

	/**
	 * Set the value related to the column: evolucao_caso
	 * @param evolucaoCaso the evolucao_caso value
	 */
	public void setEvolucaoCaso (java.lang.Long evolucaoCaso) {
//        java.lang.Long evolucaoCasoOld = this.evolucaoCaso;
		this.evolucaoCaso = evolucaoCaso;
//        this.getPropertyChangeSupport().firePropertyChange ("evolucaoCaso", evolucaoCasoOld, evolucaoCaso);
	}



	/**
	 * Return the value associated with the column: dt_obito
	 */
	public java.util.Date getDataObito () {
		return getPropertyValue(this, dataObito, PROP_DATA_OBITO); 
	}

	/**
	 * Set the value related to the column: dt_obito
	 * @param dataObito the dt_obito value
	 */
	public void setDataObito (java.util.Date dataObito) {
//        java.util.Date dataObitoOld = this.dataObito;
		this.dataObito = dataObito;
//        this.getPropertyChangeSupport().firePropertyChange ("dataObito", dataObitoOld, dataObito);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: dt_encerramento
	 */
	public java.util.Date getDataEncerramento () {
		return getPropertyValue(this, dataEncerramento, PROP_DATA_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_encerramento
	 * @param dataEncerramento the dt_encerramento value
	 */
	public void setDataEncerramento (java.util.Date dataEncerramento) {
//        java.util.Date dataEncerramentoOld = this.dataEncerramento;
		this.dataEncerramento = dataEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEncerramento", dataEncerramentoOld, dataEncerramento);
	}



	/**
	 * Return the value associated with the column: cd_registro_agravo
	 */
	public br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo getRegistroAgravo () {
		return getPropertyValue(this, registroAgravo, PROP_REGISTRO_AGRAVO); 
	}

	/**
	 * Set the value related to the column: cd_registro_agravo
	 * @param registroAgravo the cd_registro_agravo value
	 */
	public void setRegistroAgravo (br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo) {
//        br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravoOld = this.registroAgravo;
		this.registroAgravo = registroAgravo;
//        this.getPropertyChangeSupport().firePropertyChange ("registroAgravo", registroAgravoOld, registroAgravo);
	}



	/**
	 * Return the value associated with the column: ocupacao_cbo
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getOcupacaoCbo () {
		return getPropertyValue(this, ocupacaoCbo, PROP_OCUPACAO_CBO); 
	}

	/**
	 * Set the value related to the column: ocupacao_cbo
	 * @param ocupacaoCbo the ocupacao_cbo value
	 */
	public void setOcupacaoCbo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCboOld = this.ocupacaoCbo;
		this.ocupacaoCbo = ocupacaoCbo;
//        this.getPropertyChangeSupport().firePropertyChange ("ocupacaoCbo", ocupacaoCboOld, ocupacaoCbo);
	}



	/**
	 * Return the value associated with the column: unidade_hospital
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getHospital () {
		return getPropertyValue(this, hospital, PROP_HOSPITAL); 
	}

	/**
	 * Set the value related to the column: unidade_hospital
	 * @param hospital the unidade_hospital value
	 */
	public void setHospital (br.com.ksisolucoes.vo.basico.Empresa hospital) {
//        br.com.ksisolucoes.vo.basico.Empresa hospitalOld = this.hospital;
		this.hospital = hospital;
//        this.getPropertyChangeSupport().firePropertyChange ("hospital", hospitalOld, hospital);
	}



	/**
	 * Return the value associated with the column: cd_cidade_infeccao
	 */
	public br.com.ksisolucoes.vo.basico.Cidade getCidadeLocalInfeccao () {
		return getPropertyValue(this, cidadeLocalInfeccao, PROP_CIDADE_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: cd_cidade_infeccao
	 * @param cidadeLocalInfeccao the cd_cidade_infeccao value
	 */
	public void setCidadeLocalInfeccao (br.com.ksisolucoes.vo.basico.Cidade cidadeLocalInfeccao) {
//        br.com.ksisolucoes.vo.basico.Cidade cidadeLocalInfeccaoOld = this.cidadeLocalInfeccao;
		this.cidadeLocalInfeccao = cidadeLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("cidadeLocalInfeccao", cidadeLocalInfeccaoOld, cidadeLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: cd_pais_infeccao
	 */
	public br.com.ksisolucoes.vo.basico.Pais getPaisLocalInfeccao () {
		return getPropertyValue(this, paisLocalInfeccao, PROP_PAIS_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: cd_pais_infeccao
	 * @param paisLocalInfeccao the cd_pais_infeccao value
	 */
	public void setPaisLocalInfeccao (br.com.ksisolucoes.vo.basico.Pais paisLocalInfeccao) {
//        br.com.ksisolucoes.vo.basico.Pais paisLocalInfeccaoOld = this.paisLocalInfeccao;
		this.paisLocalInfeccao = paisLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("paisLocalInfeccao", paisLocalInfeccaoOld, paisLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: cd_usuario_encerramento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioEncerramento () {
		return getPropertyValue(this, usuarioEncerramento, PROP_USUARIO_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_encerramento
	 * @param usuarioEncerramento the cd_usuario_encerramento value
	 */
	public void setUsuarioEncerramento (br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramentoOld = this.usuarioEncerramento;
		this.usuarioEncerramento = usuarioEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioEncerramento", usuarioEncerramentoOld, usuarioEncerramento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoTetanoAcidental)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoTetanoAcidental investigacaoAgravoTetanoAcidental = (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoTetanoAcidental) obj;
			if (null == this.getCodigo() || null == investigacaoAgravoTetanoAcidental.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravoTetanoAcidental.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}