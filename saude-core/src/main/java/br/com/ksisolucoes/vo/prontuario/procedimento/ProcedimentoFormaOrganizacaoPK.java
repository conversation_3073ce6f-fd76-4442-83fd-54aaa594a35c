package br.com.ksisolucoes.vo.prontuario.procedimento;

import br.com.ksisolucoes.associacao.annotations.IdNameSIGTAP;
import br.com.ksisolucoes.vo.prontuario.procedimento.base.BaseProcedimentoFormaOrganizacaoPK;

public class ProcedimentoFormaOrganizacaoPK extends BaseProcedimentoFormaOrganizacaoPK {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ProcedimentoFormaOrganizacaoPK () {}
	
	public ProcedimentoFormaOrganizacaoPK (
		java.lang.Long codigoProcedimentoGrupo,
		java.lang.Long codigoProcedimentoSubGrupo,
		java.lang.Long codigo) {

		super (
			codigoProcedimentoGrupo,
			codigoProcedimentoSubGrupo,
			codigo);
	}
/*[CONSTRUCTOR MARKER END]*/


    @IdNameSIGTAP("CO_FORMA_ORGANIZACAO")
    @Override
    public Long getCodigo() {
        return super.getCodigo();
    }

    @IdNameSIGTAP("CO_GRUPO")
    @Override
    public Long getCodigoProcedimentoGrupo() {
        return super.getCodigoProcedimentoGrupo();
    }

    @IdNameSIGTAP("CO_SUB_GRUPO")
    @Override
    public Long getCodigoProcedimentoSubGrupo() {
        return super.getCodigoProcedimentoSubGrupo();
    }

    @Override
    public void setCodigoProcedimentoGrupo(Long codigoProcedimentoGrupo) {
        super.setCodigoProcedimentoGrupo(codigoProcedimentoGrupo);
    }

    @Override
    public void setCodigoProcedimentoSubGrupo(Long codigoProcedimentoSubGrupo) {
        super.setCodigoProcedimentoSubGrupo(codigoProcedimentoSubGrupo);
    }

    @Override
    public void setCodigo(Long codigo) {
        super.setCodigo(codigo);
    }

}