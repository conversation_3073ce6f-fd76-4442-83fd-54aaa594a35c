package br.com.ksisolucoes.vo.geral;

import java.io.Serializable;

import br.com.ksisolucoes.vo.geral.base.BaseLancamentoDespesa;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class LancamentoDespesa extends BaseLancamentoDespesa implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public LancamentoDespesa () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public LancamentoDespesa (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public LancamentoDespesa (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.programasaude.ProgramaSaude programaSaude,
		br.com.ksisolucoes.vo.basico.Pessoa pessoa,
		br.com.ksisolucoes.vo.consorcio.TipoMovimentacao tipoMovimentacao,
		br.com.ksisolucoes.vo.entradas.estoque.CentroCusto centroCusto,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		java.lang.String descricao,
		java.util.Date dataDespesa,
		java.lang.Double valorDespesa) {

		super (
			codigo,
			programaSaude,
			pessoa,
			tipoMovimentacao,
			centroCusto,
			empresa,
			descricao,
			dataDespesa,
			valorDespesa);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}