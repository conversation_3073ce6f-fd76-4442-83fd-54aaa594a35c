<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.comunicacao"  >
	<class name="GrupoMensagem" table="grupo_mensagem">
		<id
			column="cd_grupo_mensagem"
			name="codigo"
			type="java.lang.Long"
		>
			<generator class="assigned" />
		</id> <version column="version" name="version" type="long" />

                <property
			column="descricao"
			name="descricao"
			not-null="true"
			type="java.lang.String"
		 />
                
	</class>
</hibernate-mapping>
