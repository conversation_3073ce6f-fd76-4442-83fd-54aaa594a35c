<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.atendimento" >

    <class name="Bpa" table="bpa">

        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_bpa"
        >
            <generator class="sequence">
                <param name="sequence">seq_bpa</param>
            </generator>
        </id> 
        <version column="version" name="version" type="long" />

        <many-to-one class="br.com.ksisolucoes.vo.atendimento.BpaProcesso" 
                     name="bpaProcesso">
            <column name="cd_bpa_processo" />
        </many-to-one>
        
        <many-to-one class="br.com.ksisolucoes.vo.basico.Empresa" 
                     name="empresa">
            <column name="empresa" />
        </many-to-one>

        <property
            column="folha"
            name="folha"
            type="java.lang.Long"
        />

        <property
            column="sequencia"
            name="sequencia"
            type="java.lang.Long"
        />
        
        <many-to-one class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo" 
                     name="tabelaCbo">
            <column name="cd_cbo" />
        </many-to-one>
        
        <property
            column="idade"
            name="idade"
            type="java.lang.Long"
        />
        
        <property
            column="competencia_procedimento"
            name="competenciaProcedimento"
            type="java.util.Date"
        />

        <many-to-one class="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
                     name="procedimento">
            <column name="cd_procedimento" />
        </many-to-one>

        <property
            column="qtdade"
            name="quantidade"
            type="java.lang.Long"
        />
        
        <many-to-one class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"
                     name="usuarioCadsus">
            <column name="cd_usu_cadsus" />
        </many-to-one>
        
        <many-to-one class="br.com.ksisolucoes.vo.cadsus.Profissional"
                     name="profissional">
            <column name="cd_profissional" />
        </many-to-one>
        
        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.Cid"
                     name="cid">
            <column name="cd_cid" />
        </many-to-one>
        
        <property
            column="cns"
            name="cns"
            type="java.lang.String"
        />
        
        <property
            column="carater_atendimento"
            name="caraterAtendimento"
            type="java.lang.Long"
        />
        
        <property
            column="nr_autorizacao"
            name="numeroAutorizacao"
            type="java.lang.String"
        />
        
        <property
            column="dt_atendimento"
            name="dataAtendimento"
            type="java.util.Date"
        />
        
        <property
            column="tipo_bpa"
            name="tipoBpa"
            type="java.lang.String"
        />
        
        <many-to-one class="br.com.ksisolucoes.vo.basico.Cidade"
                     name="cidade">
            <column name="cod_cid" />
        </many-to-one>
        
        <property
            column="cd_servico"
            name="servico"
            type="java.lang.Long"
        />
        
        <property
            column="cd_classificacao"
            name="classificacao"
            type="java.lang.Long"
        />
        
        <many-to-one class="br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus"
                     name="enderecoUsuarioCadsus">
            <column name="cd_endereco" />
        </many-to-one>

        <property
            column="cns_profissional"
            name="cnsProfissional"
            type="java.lang.String"
        />
        
        <property
            column="cnes_equipe"
            name="cnesEquipe"
            type="java.lang.String"
        />

    </class>
</hibernate-mapping>
