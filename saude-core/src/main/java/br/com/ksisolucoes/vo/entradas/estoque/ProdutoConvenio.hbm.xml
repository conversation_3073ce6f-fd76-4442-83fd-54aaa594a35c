<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.entradas.estoque"  >
    <class name="ProdutoConvenio"
           table="produto_convenio">

        <id
            column="cd_produto_convenio"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />

        <many-to-one class="br.com.ksisolucoes.vo.entradas.estoque.Produto"
                     name="produto" not-null="true">
            <column name="cod_pro" />
        </many-to-one>

        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.Convenio"
                     name="convenio" not-null="true">
            <column name="cd_convenio" />
        </many-to-one>

        <property
            column="cd_item_convenio"
            length="10"
            name="itemConvenio"
            not-null="true"
            type="string"
        />
    </class>
</hibernate-mapping>
