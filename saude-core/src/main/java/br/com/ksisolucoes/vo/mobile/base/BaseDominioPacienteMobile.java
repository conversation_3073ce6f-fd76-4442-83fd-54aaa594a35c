package br.com.ksisolucoes.vo.mobile.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the dom_paciente_mobile table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="dom_paciente_mobile"
 */

public abstract class BaseDominioPacienteMobile extends BaseRootVO implements Serializable {

	public static String REF = "DominioPacienteMobile";
	public static final String PROP_CODIGO_ORGAO_EMISSOR = "codigoOrgaoEmissor";
	public static final String PROP_SITUACAO = "situacao";
	public static final String PROP_SIGLA_U_F = "SiglaUF";
	public static final String PROP_DATA_EMISSAO_R_G = "DataEmissaoRG";
	public static final String PROP_CNS = "cns";
	public static final String PROP_USUARIO_CADASTRO = "usuarioCadastro";
	public static final String PROP_OBSERVACAO_ULTIMA_VISITA = "observacaoUltimaVisita";
	public static final String PROP_DESCRICAO_FAMILIA = "descricaoFamilia";
	public static final String PROP_SEXO = "sexo";
	public static final String PROP_VERSION_ALL = "versionAll";
	public static final String PROP_FLAG_UNIFICADO = "flagUnificado";
	public static final String PROP_NUMERO_FOLHA = "numeroFolha";
	public static final String PROP_NUMERO_DOCUMENTO_R_G = "numeroDocumentoRG";
	public static final String PROP_DATA_OBITO = "dataObito";
	public static final String PROP_CODIGO_CIDADE_NASCIMENTO = "codigoCidadeNascimento";
	public static final String PROP_NOME = "nome";
	public static final String PROP_NUMERO_TERMO = "numeroTermo";
	public static final String PROP_NOME_PAI = "nomePai";
	public static final String PROP_CODIGO_AREA = "codigoArea";
	public static final String PROP_NUMERO_MATRICULA = "numeroMatricula";
	public static final String PROP_MOT_EXCLUSAO = "motExclusao";
	public static final String PROP_KEYWORD = "keyword";
	public static final String PROP_DATA_NASCIMENTO = "dataNascimento";
	public static final String PROP_CODIGO_ETNIA = "codigoEtnia";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_NOME_MAE = "nomeMae";
	public static final String PROP_CODIGO_RACA = "codigoRaca";
	public static final String PROP_NUMERO_TELEFONE = "numeroTelefone";
	public static final String PROP_CELULAR = "celular";
	public static final String PROP_DATA_EMISSAO_CERTIDAO = "dataEmissaoCertidao";
	public static final String PROP_NACIONALIDADE = "nacionalidade";
	public static final String PROP_NUMERO_LIVRO = "numeroLivro";
	public static final String PROP_CPF = "cpf";
	public static final String PROP_NUMERO_CARTORIO = "numeroCartorio";
	public static final String PROP_MICROAREA = "microarea";
	public static final String PROP_NUMERO_DO = "numeroDo";
	public static final String PROP_BENEFICIARIO_BOLSA_FAMILIA = "beneficiarioBolsaFamilia";
	public static final String PROP_FLAG_UTILIZA_NOME_SOCIAL = "flagUtilizaNomeSocial";
	public static final String PROP_APELIDO = "apelido";


	// constructors
	public BaseDominioPacienteMobile () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseDominioPacienteMobile (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String nome;
	private java.util.Date dataNascimento;
	private java.lang.String nomeMae;
	private java.lang.String cns;
	private java.lang.String cpf;
	private java.lang.String descricaoFamilia;
	private java.lang.String sexo;
	private java.lang.Long situacao;
	private java.lang.Long codigoArea;
	private java.lang.Long microarea;
	private java.lang.Long codigoRaca;
	private java.lang.Long codigoEtnia;
	private java.lang.String nomePai;
	private java.lang.Long nacionalidade;
	private java.lang.Long codigoCidadeNascimento;
	private java.lang.String celular;
	private java.lang.String numeroTelefone;
	private java.lang.Long codigoOrgaoEmissor;
	private java.util.Date dataEmissaoRG;
	private java.lang.String numeroDocumentoRG;
	private java.lang.String siglaUF;
	private java.lang.String numeroCartorio;
	private java.lang.String numeroLivro;
	private java.lang.String numeroFolha;
	private java.lang.String numeroTermo;
	private java.util.Date dataEmissaoCertidao;
	private java.lang.String numeroMatricula;
	private java.lang.String observacaoUltimaVisita;
	private java.lang.Long flagUnificado;
	private java.util.Date dataObito;
	private java.lang.String numeroDo;
	private java.lang.String keyword;
	private java.lang.Long versionAll;
	private java.lang.Long motExclusao;
	private java.lang.Long beneficiarioBolsaFamilia;
	private java.lang.Long flagUtilizaNomeSocial;
	private java.lang.String apelido;

	// many to one
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_dominio"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: nome
	 */
	public java.lang.String getNome () {
		return getPropertyValue(this, nome, PROP_NOME); 
	}

	/**
	 * Set the value related to the column: nome
	 * @param nome the nome value
	 */
	public void setNome (java.lang.String nome) {
//        java.lang.String nomeOld = this.nome;
		this.nome = nome;
//        this.getPropertyChangeSupport().firePropertyChange ("nome", nomeOld, nome);
	}



	/**
	 * Return the value associated with the column: datanascimento
	 */
	public java.util.Date getDataNascimento () {
		return getPropertyValue(this, dataNascimento, PROP_DATA_NASCIMENTO); 
	}

	/**
	 * Set the value related to the column: datanascimento
	 * @param dataNascimento the datanascimento value
	 */
	public void setDataNascimento (java.util.Date dataNascimento) {
//        java.util.Date dataNascimentoOld = this.dataNascimento;
		this.dataNascimento = dataNascimento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataNascimento", dataNascimentoOld, dataNascimento);
	}



	/**
	 * Return the value associated with the column: nomemae
	 */
	public java.lang.String getNomeMae () {
		return getPropertyValue(this, nomeMae, PROP_NOME_MAE); 
	}

	/**
	 * Set the value related to the column: nomemae
	 * @param nomeMae the nomemae value
	 */
	public void setNomeMae (java.lang.String nomeMae) {
//        java.lang.String nomeMaeOld = this.nomeMae;
		this.nomeMae = nomeMae;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeMae", nomeMaeOld, nomeMae);
	}



	/**
	 * Return the value associated with the column: cns
	 */
	public java.lang.String getCns () {
		return getPropertyValue(this, cns, PROP_CNS); 
	}

	/**
	 * Set the value related to the column: cns
	 * @param cns the cns value
	 */
	public void setCns (java.lang.String cns) {
//        java.lang.String cnsOld = this.cns;
		this.cns = cns;
//        this.getPropertyChangeSupport().firePropertyChange ("cns", cnsOld, cns);
	}



	/**
	 * Return the value associated with the column: cpf
	 */
	public java.lang.String getCpf () {
		return getPropertyValue(this, cpf, PROP_CPF); 
	}

	/**
	 * Set the value related to the column: cpf
	 * @param cpf the cpf value
	 */
	public void setCpf (java.lang.String cpf) {
//        java.lang.String cpfOld = this.cpf;
		this.cpf = cpf;
//        this.getPropertyChangeSupport().firePropertyChange ("cpf", cpfOld, cpf);
	}



	/**
	 * Return the value associated with the column: descricaofamilia
	 */
	public java.lang.String getDescricaoFamilia () {
		return getPropertyValue(this, descricaoFamilia, PROP_DESCRICAO_FAMILIA); 
	}

	/**
	 * Set the value related to the column: descricaofamilia
	 * @param descricaoFamilia the descricaofamilia value
	 */
	public void setDescricaoFamilia (java.lang.String descricaoFamilia) {
//        java.lang.String descricaoFamiliaOld = this.descricaoFamilia;
		this.descricaoFamilia = descricaoFamilia;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoFamilia", descricaoFamiliaOld, descricaoFamilia);
	}



	/**
	 * Return the value associated with the column: sexo
	 */
	public java.lang.String getSexo () {
		return getPropertyValue(this, sexo, PROP_SEXO); 
	}

	/**
	 * Set the value related to the column: sexo
	 * @param sexo the sexo value
	 */
	public void setSexo (java.lang.String sexo) {
//        java.lang.String sexoOld = this.sexo;
		this.sexo = sexo;
//        this.getPropertyChangeSupport().firePropertyChange ("sexo", sexoOld, sexo);
	}



	/**
	 * Return the value associated with the column: situacao
	 */
	public java.lang.Long getSituacao () {
		return getPropertyValue(this, situacao, PROP_SITUACAO); 
	}

	/**
	 * Set the value related to the column: situacao
	 * @param situacao the situacao value
	 */
	public void setSituacao (java.lang.Long situacao) {
//        java.lang.Long situacaoOld = this.situacao;
		this.situacao = situacao;
//        this.getPropertyChangeSupport().firePropertyChange ("situacao", situacaoOld, situacao);
	}



	/**
	 * Return the value associated with the column: cd_area
	 */
	public java.lang.Long getCodigoArea () {
		return getPropertyValue(this, codigoArea, PROP_CODIGO_AREA); 
	}

	/**
	 * Set the value related to the column: cd_area
	 * @param codigoArea the cd_area value
	 */
	public void setCodigoArea (java.lang.Long codigoArea) {
//        java.lang.Long codigoAreaOld = this.codigoArea;
		this.codigoArea = codigoArea;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoArea", codigoAreaOld, codigoArea);
	}



	/**
	 * Return the value associated with the column: microarea
	 */
	public java.lang.Long getMicroarea () {
		return getPropertyValue(this, microarea, PROP_MICROAREA); 
	}

	/**
	 * Set the value related to the column: microarea
	 * @param microarea the microarea value
	 */
	public void setMicroarea (java.lang.Long microarea) {
//        java.lang.Long microareaOld = this.microarea;
		this.microarea = microarea;
//        this.getPropertyChangeSupport().firePropertyChange ("microarea", microareaOld, microarea);
	}



	/**
	 * Return the value associated with the column: cd_raca
	 */
	public java.lang.Long getCodigoRaca () {
		return getPropertyValue(this, codigoRaca, PROP_CODIGO_RACA); 
	}

	/**
	 * Set the value related to the column: cd_raca
	 * @param codigoRaca the cd_raca value
	 */
	public void setCodigoRaca (java.lang.Long codigoRaca) {
//        java.lang.Long codigoRacaOld = this.codigoRaca;
		this.codigoRaca = codigoRaca;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoRaca", codigoRacaOld, codigoRaca);
	}



	/**
	 * Return the value associated with the column: cd_etnia
	 */
	public java.lang.Long getCodigoEtnia () {
		return getPropertyValue(this, codigoEtnia, PROP_CODIGO_ETNIA); 
	}

	/**
	 * Set the value related to the column: cd_etnia
	 * @param codigoEtnia the cd_etnia value
	 */
	public void setCodigoEtnia (java.lang.Long codigoEtnia) {
//        java.lang.Long codigoEtniaOld = this.codigoEtnia;
		this.codigoEtnia = codigoEtnia;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoEtnia", codigoEtniaOld, codigoEtnia);
	}



	/**
	 * Return the value associated with the column: nm_pai
	 */
	public java.lang.String getNomePai () {
		return getPropertyValue(this, nomePai, PROP_NOME_PAI); 
	}

	/**
	 * Set the value related to the column: nm_pai
	 * @param nomePai the nm_pai value
	 */
	public void setNomePai (java.lang.String nomePai) {
//        java.lang.String nomePaiOld = this.nomePai;
		this.nomePai = nomePai;
//        this.getPropertyChangeSupport().firePropertyChange ("nomePai", nomePaiOld, nomePai);
	}



	/**
	 * Return the value associated with the column: nacionalidade
	 */
	public java.lang.Long getNacionalidade () {
		return getPropertyValue(this, nacionalidade, PROP_NACIONALIDADE); 
	}

	/**
	 * Set the value related to the column: nacionalidade
	 * @param nacionalidade the nacionalidade value
	 */
	public void setNacionalidade (java.lang.Long nacionalidade) {
//        java.lang.Long nacionalidadeOld = this.nacionalidade;
		this.nacionalidade = nacionalidade;
//        this.getPropertyChangeSupport().firePropertyChange ("nacionalidade", nacionalidadeOld, nacionalidade);
	}



	/**
	 * Return the value associated with the column: cod_cid_nascimento
	 */
	public java.lang.Long getCodigoCidadeNascimento () {
		return getPropertyValue(this, codigoCidadeNascimento, PROP_CODIGO_CIDADE_NASCIMENTO); 
	}

	/**
	 * Set the value related to the column: cod_cid_nascimento
	 * @param codigoCidadeNascimento the cod_cid_nascimento value
	 */
	public void setCodigoCidadeNascimento (java.lang.Long codigoCidadeNascimento) {
//        java.lang.Long codigoCidadeNascimentoOld = this.codigoCidadeNascimento;
		this.codigoCidadeNascimento = codigoCidadeNascimento;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoCidadeNascimento", codigoCidadeNascimentoOld, codigoCidadeNascimento);
	}



	/**
	 * Return the value associated with the column: celular
	 */
	public java.lang.String getCelular () {
		return getPropertyValue(this, celular, PROP_CELULAR); 
	}

	/**
	 * Set the value related to the column: celular
	 * @param celular the celular value
	 */
	public void setCelular (java.lang.String celular) {
//        java.lang.String celularOld = this.celular;
		this.celular = celular;
//        this.getPropertyChangeSupport().firePropertyChange ("celular", celularOld, celular);
	}



	/**
	 * Return the value associated with the column: nr_telefone
	 */
	public java.lang.String getNumeroTelefone () {
		return getPropertyValue(this, numeroTelefone, PROP_NUMERO_TELEFONE); 
	}

	/**
	 * Set the value related to the column: nr_telefone
	 * @param numeroTelefone the nr_telefone value
	 */
	public void setNumeroTelefone (java.lang.String numeroTelefone) {
//        java.lang.String numeroTelefoneOld = this.numeroTelefone;
		this.numeroTelefone = numeroTelefone;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroTelefone", numeroTelefoneOld, numeroTelefone);
	}



	/**
	 * Return the value associated with the column: cd_orgao_emissor
	 */
	public java.lang.Long getCodigoOrgaoEmissor () {
		return getPropertyValue(this, codigoOrgaoEmissor, PROP_CODIGO_ORGAO_EMISSOR); 
	}

	/**
	 * Set the value related to the column: cd_orgao_emissor
	 * @param codigoOrgaoEmissor the cd_orgao_emissor value
	 */
	public void setCodigoOrgaoEmissor (java.lang.Long codigoOrgaoEmissor) {
//        java.lang.Long codigoOrgaoEmissorOld = this.codigoOrgaoEmissor;
		this.codigoOrgaoEmissor = codigoOrgaoEmissor;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoOrgaoEmissor", codigoOrgaoEmissorOld, codigoOrgaoEmissor);
	}



	/**
	 * Return the value associated with the column: dt_emissao_rg
	 */
	public java.util.Date getDataEmissaoRG () {
		return getPropertyValue(this, dataEmissaoRG, PROP_DATA_EMISSAO_R_G); 
	}

	/**
	 * Set the value related to the column: dt_emissao_rg
	 * @param dataEmissaoRG the dt_emissao_rg value
	 */
	public void setDataEmissaoRG (java.util.Date dataEmissaoRG) {
//        java.util.Date dataEmissaoRGOld = this.dataEmissaoRG;
		this.dataEmissaoRG = dataEmissaoRG;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEmissaoRG", dataEmissaoRGOld, dataEmissaoRG);
	}



	/**
	 * Return the value associated with the column: nr_documento_rg
	 */
	public java.lang.String getNumeroDocumentoRG () {
		return getPropertyValue(this, numeroDocumentoRG, PROP_NUMERO_DOCUMENTO_R_G); 
	}

	/**
	 * Set the value related to the column: nr_documento_rg
	 * @param numeroDocumentoRG the nr_documento_rg value
	 */
	public void setNumeroDocumentoRG (java.lang.String numeroDocumentoRG) {
//        java.lang.String numeroDocumentoRGOld = this.numeroDocumentoRG;
		this.numeroDocumentoRG = numeroDocumentoRG;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDocumentoRG", numeroDocumentoRGOld, numeroDocumentoRG);
	}



	/**
	 * Return the value associated with the column: sg_uf
	 */
	public java.lang.String getSiglaUF () {
		return getPropertyValue(this, siglaUF, PROP_SIGLA_U_F); 
	}

	/**
	 * Set the value related to the column: sg_uf
	 * @param siglaUF the sg_uf value
	 */
	public void setSiglaUF (java.lang.String siglaUF) {
//        java.lang.String siglaUFOld = this.siglaUF;
		this.siglaUF = siglaUF;
//        this.getPropertyChangeSupport().firePropertyChange ("siglaUF", siglaUFOld, siglaUF);
	}



	/**
	 * Return the value associated with the column: nr_cartorio
	 */
	public java.lang.String getNumeroCartorio () {
		return getPropertyValue(this, numeroCartorio, PROP_NUMERO_CARTORIO); 
	}

	/**
	 * Set the value related to the column: nr_cartorio
	 * @param numeroCartorio the nr_cartorio value
	 */
	public void setNumeroCartorio (java.lang.String numeroCartorio) {
//        java.lang.String numeroCartorioOld = this.numeroCartorio;
		this.numeroCartorio = numeroCartorio;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroCartorio", numeroCartorioOld, numeroCartorio);
	}



	/**
	 * Return the value associated with the column: nr_livro
	 */
	public java.lang.String getNumeroLivro () {
		return getPropertyValue(this, numeroLivro, PROP_NUMERO_LIVRO); 
	}

	/**
	 * Set the value related to the column: nr_livro
	 * @param numeroLivro the nr_livro value
	 */
	public void setNumeroLivro (java.lang.String numeroLivro) {
//        java.lang.String numeroLivroOld = this.numeroLivro;
		this.numeroLivro = numeroLivro;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroLivro", numeroLivroOld, numeroLivro);
	}



	/**
	 * Return the value associated with the column: nr_folha
	 */
	public java.lang.String getNumeroFolha () {
		return getPropertyValue(this, numeroFolha, PROP_NUMERO_FOLHA); 
	}

	/**
	 * Set the value related to the column: nr_folha
	 * @param numeroFolha the nr_folha value
	 */
	public void setNumeroFolha (java.lang.String numeroFolha) {
//        java.lang.String numeroFolhaOld = this.numeroFolha;
		this.numeroFolha = numeroFolha;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroFolha", numeroFolhaOld, numeroFolha);
	}



	/**
	 * Return the value associated with the column: nr_termo
	 */
	public java.lang.String getNumeroTermo () {
		return getPropertyValue(this, numeroTermo, PROP_NUMERO_TERMO); 
	}

	/**
	 * Set the value related to the column: nr_termo
	 * @param numeroTermo the nr_termo value
	 */
	public void setNumeroTermo (java.lang.String numeroTermo) {
//        java.lang.String numeroTermoOld = this.numeroTermo;
		this.numeroTermo = numeroTermo;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroTermo", numeroTermoOld, numeroTermo);
	}



	/**
	 * Return the value associated with the column: dt_emissao_certidao
	 */
	public java.util.Date getDataEmissaoCertidao () {
		return getPropertyValue(this, dataEmissaoCertidao, PROP_DATA_EMISSAO_CERTIDAO); 
	}

	/**
	 * Set the value related to the column: dt_emissao_certidao
	 * @param dataEmissaoCertidao the dt_emissao_certidao value
	 */
	public void setDataEmissaoCertidao (java.util.Date dataEmissaoCertidao) {
//        java.util.Date dataEmissaoCertidaoOld = this.dataEmissaoCertidao;
		this.dataEmissaoCertidao = dataEmissaoCertidao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEmissaoCertidao", dataEmissaoCertidaoOld, dataEmissaoCertidao);
	}



	/**
	 * Return the value associated with the column: nr_matricula
	 */
	public java.lang.String getNumeroMatricula () {
		return getPropertyValue(this, numeroMatricula, PROP_NUMERO_MATRICULA); 
	}

	/**
	 * Set the value related to the column: nr_matricula
	 * @param numeroMatricula the nr_matricula value
	 */
	public void setNumeroMatricula (java.lang.String numeroMatricula) {
//        java.lang.String numeroMatriculaOld = this.numeroMatricula;
		this.numeroMatricula = numeroMatricula;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroMatricula", numeroMatriculaOld, numeroMatricula);
	}



	/**
	 * Return the value associated with the column: observacao_ultima_visita
	 */
	public java.lang.String getObservacaoUltimaVisita () {
		return getPropertyValue(this, observacaoUltimaVisita, PROP_OBSERVACAO_ULTIMA_VISITA); 
	}

	/**
	 * Set the value related to the column: observacao_ultima_visita
	 * @param observacaoUltimaVisita the observacao_ultima_visita value
	 */
	public void setObservacaoUltimaVisita (java.lang.String observacaoUltimaVisita) {
//        java.lang.String observacaoUltimaVisitaOld = this.observacaoUltimaVisita;
		this.observacaoUltimaVisita = observacaoUltimaVisita;
//        this.getPropertyChangeSupport().firePropertyChange ("observacaoUltimaVisita", observacaoUltimaVisitaOld, observacaoUltimaVisita);
	}



	/**
	 * Return the value associated with the column: flag_unificado
	 */
	public java.lang.Long getFlagUnificado () {
		return getPropertyValue(this, flagUnificado, PROP_FLAG_UNIFICADO); 
	}

	/**
	 * Set the value related to the column: flag_unificado
	 * @param flagUnificado the flag_unificado value
	 */
	public void setFlagUnificado (java.lang.Long flagUnificado) {
//        java.lang.Long flagUnificadoOld = this.flagUnificado;
		this.flagUnificado = flagUnificado;
//        this.getPropertyChangeSupport().firePropertyChange ("flagUnificado", flagUnificadoOld, flagUnificado);
	}

	/**
	 * Return the value associated with the column: flag_utiliza_nome_social
	 */
	public java.lang.Long getFlagUtilizaNomeSocial () {
		return getPropertyValue(this, flagUtilizaNomeSocial, PROP_FLAG_UTILIZA_NOME_SOCIAL);
	}

	/**
	 * Set the value related to the column: flag_utiliza_nome_social
	 * @param flagUtilizaNomeSocial the flag_utiliza_nome_social value
	 */
	public void setFlagUtilizaNomeSocial (java.lang.Long flagUtilizaNomeSocial) {
		this.flagUtilizaNomeSocial = flagUtilizaNomeSocial;
	}

	/**
	 * Return the value associated with the column: apelido
	 */
	public java.lang.String getApelido () {
		return getPropertyValue(this, apelido, PROP_APELIDO);
	}

	/**
	 * Set the value related to the column: apelido
	 * @param apelido the apelido value
	 */
	public void setApelido (java.lang.String apelido) {
		this.apelido = apelido;
	}

	/**
	 * Return the value associated with the column: dt_obito
	 */
	public java.util.Date getDataObito () {
		return getPropertyValue(this, dataObito, PROP_DATA_OBITO); 
	}

	/**
	 * Set the value related to the column: dt_obito
	 * @param dataObito the dt_obito value
	 */
	public void setDataObito (java.util.Date dataObito) {
//        java.util.Date dataObitoOld = this.dataObito;
		this.dataObito = dataObito;
//        this.getPropertyChangeSupport().firePropertyChange ("dataObito", dataObitoOld, dataObito);
	}



	/**
	 * Return the value associated with the column: numero_do
	 */
	public java.lang.String getNumeroDo () {
		return getPropertyValue(this, numeroDo, PROP_NUMERO_DO); 
	}

	/**
	 * Set the value related to the column: numero_do
	 * @param numeroDo the numero_do value
	 */
	public void setNumeroDo (java.lang.String numeroDo) {
//        java.lang.String numeroDoOld = this.numeroDo;
		this.numeroDo = numeroDo;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDo", numeroDoOld, numeroDo);
	}



	/**
	 * Return the value associated with the column: keyword
	 */
	public java.lang.String getKeyword () {
		return getPropertyValue(this, keyword, PROP_KEYWORD); 
	}

	/**
	 * Set the value related to the column: keyword
	 * @param keyword the keyword value
	 */
	public void setKeyword (java.lang.String keyword) {
//        java.lang.String keywordOld = this.keyword;
		this.keyword = keyword;
//        this.getPropertyChangeSupport().firePropertyChange ("keyword", keywordOld, keyword);
	}



	/**
	 * Return the value associated with the column: version_all
	 */
	public java.lang.Long getVersionAll () {
		return getPropertyValue(this, versionAll, PROP_VERSION_ALL); 
	}

	/**
	 * Set the value related to the column: version_all
	 * @param versionAll the version_all value
	 */
	public void setVersionAll (java.lang.Long versionAll) {
//        java.lang.Long versionAllOld = this.versionAll;
		this.versionAll = versionAll;
//        this.getPropertyChangeSupport().firePropertyChange ("versionAll", versionAllOld, versionAll);
	}



	/**
	 * Return the value associated with the column: mot_exclusao
	 */
	public java.lang.Long getMotExclusao () {
		return getPropertyValue(this, motExclusao, PROP_MOT_EXCLUSAO); 
	}

	/**
	 * Set the value related to the column: mot_exclusao
	 * @param motExclusao the mot_exclusao value
	 */
	public void setMotExclusao (java.lang.Long motExclusao) {
//        java.lang.Long motExclusaoOld = this.motExclusao;
		this.motExclusao = motExclusao;
//        this.getPropertyChangeSupport().firePropertyChange ("motExclusao", motExclusaoOld, motExclusao);
	}

	/**
	 * Return the value associated with the column: beneficiario_bolsa_familia
	 */
	public java.lang.Long getBeneficiarioBolsaFamilia () {
		return getPropertyValue(this, beneficiarioBolsaFamilia, PROP_BENEFICIARIO_BOLSA_FAMILIA);
	}

	/**
	 * Set the value related to the column: beneficiario_bolsa_familia
	 * @param beneficiarioBolsaFamilia the beneficiario_bolsa_familia value
	 */
	public void setBeneficiarioBolsaFamilia (java.lang.Long beneficiarioBolsaFamilia) {
		this.beneficiarioBolsaFamilia = beneficiarioBolsaFamilia;
	}


	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCadastro () {
		return getPropertyValue(this, usuarioCadastro, PROP_USUARIO_CADASTRO); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadastro the cd_usu_cadsus value
	 */
	public void setUsuarioCadastro (br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCadastroOld = this.usuarioCadastro;
		this.usuarioCadastro = usuarioCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadastro", usuarioCadastroOld, usuarioCadastro);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.mobile.DominioPacienteMobile)) return false;
		else {
			br.com.ksisolucoes.vo.mobile.DominioPacienteMobile dominioPacienteMobile = (br.com.ksisolucoes.vo.mobile.DominioPacienteMobile) obj;
			if (null == this.getCodigo() || null == dominioPacienteMobile.getCodigo()) return false;
			else return (this.getCodigo().equals(dominioPacienteMobile.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}