<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.atendimento.recepcao"  >
    <class name="NodoRecepcaoUnidade" table="nodo_recepcao_unidade" >
    
        <id
            column="cd_nodo_rec_unidade"
            name="codigo"
            type="java.lang.Long"
            >
            <generator class="assigned" />
        </id> <version column="version" name="version" type="long" />   
                 
         <many-to-one  
            class="br.com.ksisolucoes.vo.basico.Empresa"
            name="empresa"
            column="empresa"
        />

        <property
            name="classeNodo"
            column="classe_nodo"
            type="java.lang.String"
         /> 
  
        <property
            name="ordem"
            column="ordem"
            type="java.lang.Long"
         />

    </class>
</hibernate-mapping>
