package br.com.ksisolucoes.vo.samu;

import java.io.Serializable;

import br.com.ksisolucoes.vo.samu.base.BaseSamuChecagemItem;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class SamuChecagemItem extends BaseSamuChecagemItem implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public SamuChecagemItem () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public SamuChecagemItem (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public SamuChecagemItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.samu.SamuChecagem samuChecagem,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.String descricao,
		java.util.Date dataCadastro,
		java.util.Date dataUsuario) {

		super (
			codigo,
			samuChecagem,
			usuario,
			descricao,
			dataCadastro,
			dataUsuario);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}