package br.com.ksisolucoes.vo.prontuario.enfermagem;

import java.io.Serializable;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.enfermagem.base.BaseAtendimentoHistorico;



public class AtendimentoHistorico extends BaseAtendimentoHistorico implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public AtendimentoHistorico () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public AtendimentoHistorico (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public AtendimentoHistorico (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {

		super (
			codigo,
			atendimento);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (Long) key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}