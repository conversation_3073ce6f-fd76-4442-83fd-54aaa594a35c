package br.com.ksisolucoes.vo.enderecoestruturado;

import br.com.ksisolucoes.vo.enderecoestruturado.base.BaseEnderecoEstruturadoDistrito;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;

import java.io.Serializable;


public class EnderecoEstruturadoDistrito extends BaseEnderecoEstruturadoDistrito implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EnderecoEstruturadoDistrito () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public EnderecoEstruturadoDistrito (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public EnderecoEstruturadoDistrito (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuarioAlteracao) {

		super (
			codigo,
			usuarioAlteracao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    @Override
	public String getDescricaoVO() {
		return this.getDescricao();
	}
	@Override
	public String getIdentificador() {
		return this.getCodigo().toString();
	}
}