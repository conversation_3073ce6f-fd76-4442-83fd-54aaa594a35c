<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.requerimentos"  >
    <class name="RequerimentoVistoriaHidrossanitarioDeclaratorioParecer" table="req_vistoria_hidro_declaratorio_parecer">
        <id
            column="cd_req_vistoria_hidro_declaratorio_parecer"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="sequence">
                <param name="sequence">seq_req_vistoria_hidro_declaratorio_parecer</param>
            </generator>
        </id> 
        <version column="version" name="version" type="long" />

 		<many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaHidrossanitarioDeclaratorio"
            column="cd_requerimento_vistoria_hidro_declaratorio"
            name="requerimentoVistoriaHidrossanitarioDeclaratorio"
            not-null="true"
        />

        <property
                column="ds_parecer"
                name="descricaoParecer"
                type="java.lang.String"
        />

        <property
                name="dataParecer"
                column="dt_parecer"
                type="timestamp"
                not-null="true"
        />
        
        <property
            column="status"
            name="status"
            type="java.lang.Long"
        />

        <property
                column="dt_cadastro"
                name="dataCadastro"
                type="java.util.Date"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario"
                name="usuario"
                not-null="true"
        />


        <property
                column="tipo_concessao"
                name="tipoConcessao"
                type="java.lang.Long"
        />


        <property
                name="numeroHabitese"
                column="numero_habitese"
                type="java.lang.Long"
        />

        <property
                name="dataSaida"
                column="dt_saida"
                type="date"
                not-null="false"
        />

        <property
                name="dataRetorno"
                column="dt_retorno"
                type="date"
                not-null="false"
        />

    </class>
</hibernate-mapping>
