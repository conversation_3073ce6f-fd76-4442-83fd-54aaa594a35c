package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the exame_unidade_cota table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="exame_unidade_cota"
 */

public abstract class BaseExameUnidadeCota extends BaseRootVO implements Serializable {

	public static String REF = "ExameUnidadeCota";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_TIPO_EXAME = "tipoExame";
	public static final String PROP_VALOR_COTA = "valorCota";
	public static final String PROP_PERCENTUAL_COTA = "percentualCota";


	// constructors
	public BaseExameUnidadeCota () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseExameUnidadeCota (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseExameUnidadeCota (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		br.com.ksisolucoes.vo.prontuario.basico.TipoExame tipoExame,
		java.lang.Double percentualCota) {

		this.setCodigo(codigo);
		this.setEmpresa(empresa);
		this.setTipoExame(tipoExame);
		this.setPercentualCota(percentualCota);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Double percentualCota;
	private java.lang.Double valorCota;

	// many to one
	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private br.com.ksisolucoes.vo.prontuario.basico.TipoExame tipoExame;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_exame_un_cota"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: perc_cota
	 */
	public java.lang.Double getPercentualCota () {
		return getPropertyValue(this, percentualCota, PROP_PERCENTUAL_COTA); 
	}

	/**
	 * Set the value related to the column: perc_cota
	 * @param percentualCota the perc_cota value
	 */
	public void setPercentualCota (java.lang.Double percentualCota) {
//        java.lang.Double percentualCotaOld = this.percentualCota;
		this.percentualCota = percentualCota;
//        this.getPropertyChangeSupport().firePropertyChange ("percentualCota", percentualCotaOld, percentualCota);
	}



	/**
	 * Return the value associated with the column: vl_cota
	 */
	public java.lang.Double getValorCota () {
		return getPropertyValue(this, valorCota, PROP_VALOR_COTA); 
	}

	/**
	 * Set the value related to the column: vl_cota
	 * @param valorCota the vl_cota value
	 */
	public void setValorCota (java.lang.Double valorCota) {
//        java.lang.Double valorCotaOld = this.valorCota;
		this.valorCota = valorCota;
//        this.getPropertyChangeSupport().firePropertyChange ("valorCota", valorCotaOld, valorCota);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: cd_tp_exame
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.TipoExame getTipoExame () {
		return getPropertyValue(this, tipoExame, PROP_TIPO_EXAME); 
	}

	/**
	 * Set the value related to the column: cd_tp_exame
	 * @param tipoExame the cd_tp_exame value
	 */
	public void setTipoExame (br.com.ksisolucoes.vo.prontuario.basico.TipoExame tipoExame) {
//        br.com.ksisolucoes.vo.prontuario.basico.TipoExame tipoExameOld = this.tipoExame;
		this.tipoExame = tipoExame;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoExame", tipoExameOld, tipoExame);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.ExameUnidadeCota)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.ExameUnidadeCota exameUnidadeCota = (br.com.ksisolucoes.vo.prontuario.basico.ExameUnidadeCota) obj;
			if (null == this.getCodigo() || null == exameUnidadeCota.getCodigo()) return false;
			else return (this.getCodigo().equals(exameUnidadeCota.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}