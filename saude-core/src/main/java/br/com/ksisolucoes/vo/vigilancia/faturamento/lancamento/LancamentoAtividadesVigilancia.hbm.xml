<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento"  >
    <class name="LancamentoAtividadesVigilancia" table="lancamento_atividades_vigilancia">

        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_lancamento_atividades_vigilancia"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.Profissional"
            column="cd_profissional"
            name="profissional"
            not-null="true"
        />

        <property
            name="dataAtividade"
            column="dt_atividade"
            type="java.util.Date"
            not-null="true"
        />

        <property
            name="observacao"
            column="observacao"
            type="java.lang.String"
            not-null="true"
        />

        <property
            name="dataCadastro"
            column="dt_cadastro"
            type="timestamp"
            not-null="true"
        />

        <property
            name="dataAlteracao"
            column="dt_alteracao"
            type="timestamp"
            not-null="true"
        />

        <property
                name="tipoAtividade"
                column="ds_tipo"
                type="java.lang.String"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuarioCadastro"
            not-null="true"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia"
            column="cd_req_vigilancia"
            name="requerimentoVigilancia"
            not-null="false"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.RegistroVisita"
                column="cd_registro_visita"
                name="registroVisita"
                not-null="false"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente"
                column="cd_conta_paciente"
                name="contaPaciente"
                not-null="false"
        />

        <property
                name="flagTipo"
                column="flag_tipo"
                type="java.lang.Long"
        />

        <many-to-one
                column="cd_estabelecimento"
                name="estabelecimento"
                class="br.com.ksisolucoes.vo.vigilancia.Estabelecimento"
        />

        <many-to-one
                column="cd_vigilancia_pessoa"
                name="vigilanciaPessoa"
                class="br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa"
        />

        <property
                name="nomePessoa"
                column="nm_pessoa"
                type="java.lang.String"
        />

        <property
                column="cnpj_cpf_pessoa"
                name="cnpjCpfPessoa"
                not-null="false"
                type="java.lang.String"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento"
                column="cd_atividade_estabelecimento"
                not-null="false"
                name="atividadeEstabelecimento"
        />

        <property
                column="ds_outros"
                name="descricaoOutros"
                type="java.lang.String"
        />

        <property
                column="num_auto_multa"
                name="numeroAutoMulta"
                not-null="false"
                type="java.lang.Long"
        />
        <property
                column="num_auto_intimacao"
                name="numeroAutoIntimacao"
                not-null="false"
                type="java.lang.Long"
        />
        <property
                column="num_auto_infracao"
                name="numeroAutoInfracao"
                not-null="false"
                type="java.lang.Long"
        />
    </class>
</hibernate-mapping>
