<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vacina.rnds">
    <class name="RndsVacinaOcorrencias" table="rnds_vacina_ocorrencias">
        <id
                column="cd_rnds_vacina_ocorrencias"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="sequence">
                <param name="sequence">seq_rnds_vacina_ocorrencias</param>
            </generator>
        </id>
        <version column="version" name="version" type="long"/>
        <many-to-one
                class="br.com.ksisolucoes.vo.vacina.rnds.RndsIntegracaoVacina"
                column="cd_rnds_integracao_vacina"
                name="rndsIntegracaoVacina"
                not-null="true"
        />
        <property
                column="descricao"
                name="descricao"
                not-null="false"
                type="java.lang.String"
        />

        <property
                column="documento_enviado"
                name="documentoEnviado"
                not-null="false"
                type="java.lang.String"
        />

        <property
                column="dt_ocorrencia"
                name="dataOcorrencia"
                type="timestamp"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario"
                name="usuario"
        />

        <property
                column="json_retorno"
                name="jsonRetorno"
                not-null="false"
                type="java.lang.String"
        />

    </class>
</hibernate-mapping>
