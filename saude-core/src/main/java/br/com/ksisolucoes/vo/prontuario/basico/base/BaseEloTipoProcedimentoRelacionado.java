package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the elo_tipo_procedimento_relacionado table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="elo_tipo_procedimento_relacionado"
 */

public abstract class BaseEloTipoProcedimentoRelacionado extends BaseRootVO implements Serializable {

	public static String REF = "EloTipoProcedimentoRelacionado";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TIPO_PROCEDIMENTO_RELACIONADO = "tipoProcedimentoRelacionado";
	public static final String PROP_TIPO_PROCEDIMENTO_PRINCIPAL = "tipoProcedimentoPrincipal";


	// constructors
	public BaseEloTipoProcedimentoRelacionado () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEloTipoProcedimentoRelacionado (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimentoPrincipal;
	private br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimentoRelacionado;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_elo_tp_proc_rel"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_tp_procedimento_princ
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento getTipoProcedimentoPrincipal () {
		return getPropertyValue(this, tipoProcedimentoPrincipal, PROP_TIPO_PROCEDIMENTO_PRINCIPAL); 
	}

	/**
	 * Set the value related to the column: cd_tp_procedimento_princ
	 * @param tipoProcedimentoPrincipal the cd_tp_procedimento_princ value
	 */
	public void setTipoProcedimentoPrincipal (br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimentoPrincipal) {
//        br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimentoPrincipalOld = this.tipoProcedimentoPrincipal;
		this.tipoProcedimentoPrincipal = tipoProcedimentoPrincipal;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoProcedimentoPrincipal", tipoProcedimentoPrincipalOld, tipoProcedimentoPrincipal);
	}



	/**
	 * Return the value associated with the column: cd_tp_procedimento_rel
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento getTipoProcedimentoRelacionado () {
		return getPropertyValue(this, tipoProcedimentoRelacionado, PROP_TIPO_PROCEDIMENTO_RELACIONADO); 
	}

	/**
	 * Set the value related to the column: cd_tp_procedimento_rel
	 * @param tipoProcedimentoRelacionado the cd_tp_procedimento_rel value
	 */
	public void setTipoProcedimentoRelacionado (br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimentoRelacionado) {
//        br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimentoRelacionadoOld = this.tipoProcedimentoRelacionado;
		this.tipoProcedimentoRelacionado = tipoProcedimentoRelacionado;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoProcedimentoRelacionado", tipoProcedimentoRelacionadoOld, tipoProcedimentoRelacionado);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.EloTipoProcedimentoRelacionado)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.EloTipoProcedimentoRelacionado eloTipoProcedimentoRelacionado = (br.com.ksisolucoes.vo.prontuario.basico.EloTipoProcedimentoRelacionado) obj;
			if (null == this.getCodigo() || null == eloTipoProcedimentoRelacionado.getCodigo()) return false;
			else return (this.getCodigo().equals(eloTipoProcedimentoRelacionado.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}