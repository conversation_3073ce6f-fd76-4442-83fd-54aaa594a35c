<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico">
    <class name="PlanejamentoFamiliar" table="planejamento_familiar">
		
        <id
            column="cd_planej_familiar"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />
        
        <many-to-one 
            column="nr_atendimento"
            name="atendimento"
            class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
            not-null="true"
        />
        
        <property 
            column="cardiopatias" 
            name="cardiopatias" 
            type="java.lang.Long"
            not-null="false"
        />

        <property 
            column="diabetes" 
            name="diabetes" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="ictericia_hepatite" 
            name="ictericiaHepatite" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="enxaquecas" 
            name="enxaquecas" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="anemia" 
            name="anemia" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="cirurgias" 
            name="cirurgias" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="alergia" 
            name="alergia" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="drogas" 
            name="drogas" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="fumo" 
            name="fumo" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="hipertensao_arterial" 
            name="hipertensaoArterial" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="doencas_endemicas" 
            name="doencasEndemicas" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="doencas_tromboembolicas" 
            name="doencasTromboembolicas" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="outros" 
            name="outros" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="compl_outros" 
            name="complementoOutros" 
            type="java.lang.String"
            not-null="false"
            length="50"
        />
        
        <property 
            column="menarca" 
            name="menarca" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="ciclo_menstrual" 
            name="cicloMenstrual" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="dt_ultima_menstruacao" 
            name="dataUltimaMenstruacao" 
            type="date"
            not-null="false"
        />
        
        <property 
            column="nro_gestacao" 
            name="numeroGestacao" 
            type="java.lang.Long"
            not-null="false"
        />

        <property 
            column="nro_partos" 
            name="numeroPartos" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="abortos_espontaneos" 
            name="abortosEspontaneos" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="abortos_provocados" 
            name="abortosProvocados" 
            type="java.lang.Long"
            not-null="false"
        />

        <property 
            column="gravidez_ectopica" 
            name="gravidezEctopica" 
            type="java.lang.Long"
            not-null="false"
        />

        <property 
            column="cesareas" 
            name="cesareas" 
            type="java.lang.Long"
            not-null="false"
        />
        <property 
            column="rn_baixo_peso" 
            name="rnBaixoPeso" 
            type="java.lang.Long"
            not-null="false"
        />
        <property 
            column="natimortos" 
            name="natimortos" 
            type="java.lang.Long"
            not-null="false"
        />
        <property 
            column="filhos_vivos" 
            name="filhosVivos" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="outros_dados_gineco_obst" 
            name="outrosDadosGinecoObstetricos" 
            type="java.lang.String"
            not-null="false"
            length="100"
        />
        
        <property 
            column="dt_ultima_gestacao" 
            name="dataUltimaGestacao" 
            type="date"
            not-null="false"
        />
        
        <property 
            column="parto" 
            name="parto" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="compl_parto" 
            name="complementoParto" 
            type="java.lang.String"
            not-null="false"
            length="50"
        />
        
        <property 
            column="dt_ultimo_aborto" 
            name="dataUltimoAborto" 
            type="date"
            not-null="false"
        />
        
        <property 
            column="amamentando" 
            name="amamentando" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="inicio_vida_sexual" 
            name="inicioVidaSexual" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="freq_relacao_sexual" 
            name="frequenciaRelacaoSexual" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="dispareunia" 
            name="dispareunia" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="dst" 
            name="dst" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="tratamento_dst" 
            name="tratamentoDst" 
            type="java.lang.String"
            not-null="false"
            length="100"
        />
        
        <property 
            column="exame_colpocitologico" 
            name="exameColpocitologico" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="diabetes_ant_familiares" 
            name="diabetesAntecedentesFamiliares" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="enfarte_ant_familiares" 
            name="enfarteAntecedentesFamiliares" 
            type="java.lang.Long"
            not-null="false"
        />

        <property 
            column="cancer_mama_ant_familiares" 
            name="cancerMamaAntecedentesFamiliares" 
            type="java.lang.Long"
            not-null="false"
        />

        <property 
            column="outros_antecedentes" 
            name="outrosAntecedentes" 
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="compl_outros_antecedentes" 
            name="complementoOutrosAntecedentes" 
            type="java.lang.String"
            not-null="false"
            length="100"
        />
        
        <property 
            column="obs_antecedentes" 
            name="observacaoAntecedentes" 
            type="java.lang.String"
            not-null="false"
            length="200"
        />

    </class>
</hibernate-mapping>