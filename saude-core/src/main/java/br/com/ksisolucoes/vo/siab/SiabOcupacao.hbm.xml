<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.siab"  >
    <class name="SiabOcupacao" table="siab_ocupacao" >

        <id
            name="codigo" 
            type="java.lang.Long"   
            column="cd_ocupacao"  
        > 
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />

        <property
            name="descricao"
            not-null="true"
            length="100"
            type="java.lang.String"
            column="ds_ocupacao"
        >
        </property>
        
        <property
            name="codigoSiab"
            type="java.lang.Long"
            column="cd_siab"
        />
        
    </class>
</hibernate-mapping>
