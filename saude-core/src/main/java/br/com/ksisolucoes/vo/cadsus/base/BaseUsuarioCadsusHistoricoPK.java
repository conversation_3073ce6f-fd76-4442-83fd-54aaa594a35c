package br.com.ksisolucoes.vo.cadsus.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;


public abstract class BaseUsuarioCadsusHistoricoPK extends BaseRootVO implements Serializable {

	protected int hashCode = Integer.MIN_VALUE;

	public static String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static String PROP_DATA_ALTERACAO = "dataAlteracao";

	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private java.util.Date dataAlteracao;


	public BaseUsuarioCadsusHistoricoPK () {}
	
	public BaseUsuarioCadsusHistoricoPK (
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		java.util.Date dataAlteracao) {

		this.setUsuarioCadsus(usuarioCadsus);
		this.setDataAlteracao(dataAlteracao);
	}


	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: dt_alteracao
	 */
	public java.util.Date getDataAlteracao () {
		return getPropertyValue(this, dataAlteracao, PROP_DATA_ALTERACAO); 
	}

	/**
	 * Set the value related to the column: dt_alteracao
	 * @param dataAlteracao the dt_alteracao value
	 */
	public void setDataAlteracao (java.util.Date dataAlteracao) {
//        java.util.Date dataAlteracaoOld = this.dataAlteracao;
		this.dataAlteracao = dataAlteracao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAlteracao", dataAlteracaoOld, dataAlteracao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHistoricoPK)) return false;
		else {
			br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHistoricoPK mObj = (br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHistoricoPK) obj;
			if (null != this.getUsuarioCadsus() && null != mObj.getUsuarioCadsus()) {
				if (!this.getUsuarioCadsus().equals(mObj.getUsuarioCadsus())) {
					return false;
				}
			}
			else {
				return false;
			}
			if (null != this.getDataAlteracao() && null != mObj.getDataAlteracao()) {
				if (!this.getDataAlteracao().equals(mObj.getDataAlteracao())) {
					return false;
				}
			}
			else {
				return false;
			}
			return true;
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			StringBuilder sb = new StringBuilder();
			if (null != this.getUsuarioCadsus()) {
				sb.append(this.getUsuarioCadsus().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			if (null != this.getDataAlteracao()) {
				sb.append(this.getDataAlteracao().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			this.hashCode = sb.toString().hashCode();
		}
		return this.hashCode;
	}

    private java.beans.PropertyChangeSupport propertyChangeSupport;

    protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
        if( this.propertyChangeSupport == null ) {
            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
        }
        return this.propertyChangeSupport;
    }

    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.addPropertyChangeListener(l);
    }

    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
		propertyChangeSupport.addPropertyChangeListener(propertyName, listener);
    }

    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.removePropertyChangeListener(l);
    }
}