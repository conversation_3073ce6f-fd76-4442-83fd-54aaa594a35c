package br.com.ksisolucoes.vo.agendamento.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the agendamento_procedimento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="agendamento_procedimento"
 */

public abstract class BaseAgendamentoProcedimento extends BaseRootVO implements Serializable {

	public static String REF = "AgendamentoProcedimento";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO = "agendaGradeAtendimentoHorario";
	public static final String PROP_EXAME_PROCEDIMENTO = "exameProcedimento";


	// constructors
	public BaseAgendamentoProcedimento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAgendamentoProcedimento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseAgendamentoProcedimento (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario,
		br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimento) {

		this.setCodigo(codigo);
		this.setAgendaGradeAtendimentoHorario(agendaGradeAtendimentoHorario);
		this.setExameProcedimento(exameProcedimento);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// many to one
	private br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario;
	private br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_ag_procedimento"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_ag_gra_ate_hor
	 */
	public br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario getAgendaGradeAtendimentoHorario () {
		return getPropertyValue(this, agendaGradeAtendimentoHorario, PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO); 
	}

	/**
	 * Set the value related to the column: cd_ag_gra_ate_hor
	 * @param agendaGradeAtendimentoHorario the cd_ag_gra_ate_hor value
	 */
	public void setAgendaGradeAtendimentoHorario (br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario) {
//        br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorarioOld = this.agendaGradeAtendimentoHorario;
		this.agendaGradeAtendimentoHorario = agendaGradeAtendimentoHorario;
//        this.getPropertyChangeSupport().firePropertyChange ("agendaGradeAtendimentoHorario", agendaGradeAtendimentoHorarioOld, agendaGradeAtendimentoHorario);
	}



	/**
	 * Return the value associated with the column: cd_exame_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento getExameProcedimento () {
		return getPropertyValue(this, exameProcedimento, PROP_EXAME_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_exame_procedimento
	 * @param exameProcedimento the cd_exame_procedimento value
	 */
	public void setExameProcedimento (br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimentoOld = this.exameProcedimento;
		this.exameProcedimento = exameProcedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("exameProcedimento", exameProcedimentoOld, exameProcedimento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.agendamento.AgendamentoProcedimento)) return false;
		else {
			br.com.ksisolucoes.vo.agendamento.AgendamentoProcedimento agendamentoProcedimento = (br.com.ksisolucoes.vo.agendamento.AgendamentoProcedimento) obj;
			if (null == this.getCodigo() || null == agendamentoProcedimento.getCodigo()) return false;
			else return (this.getCodigo().equals(agendamentoProcedimento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}