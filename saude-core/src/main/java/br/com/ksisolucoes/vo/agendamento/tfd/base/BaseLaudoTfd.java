package br.com.ksisolucoes.vo.agendamento.tfd.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the laudo_tfd table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="laudo_tfd"
 */

public abstract class BaseLaudoTfd extends BaseRootVO implements Serializable {

	public static String REF = "LaudoTfd";
	public static final String PROP_JUSTIFICATIVA_TFD = "justificativaTfd";
	public static final String PROP_DATA_RECEBIMENTO_PACIENTE = "dataRecebimentoPaciente";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_EXAME_BPAI = "exameBpai";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_TRATAMENTO_REALIZADO = "tratamentoRealizado";
	public static final String PROP_PROCEDIMENTO = "procedimento";
	public static final String PROP_CARACTERIZACAO_URGENCIA = "caracterizacaoUrgencia";
	public static final String PROP_OBSERVACAO_URGENTE = "observacaoUrgente";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";
	public static final String PROP_ATENDIMENTO = "atendimento";
	public static final String PROP_CARATER_ATENDIMENTO = "caraterAtendimento";
	public static final String PROP_JUSTIFICATIVA_LIMITE_DIAS = "justificativaLimiteDias";
	public static final String PROP_COMPLEMENTO = "complemento";
	public static final String PROP_DATA_INTEGRACAO_INOVAMFRI = "dataIntegracaoInovamfri";
	public static final String PROP_NOME_PROFISSIONAL = "nomeProfissional";
	public static final String PROP_ESTADO_NUTRICIONAL = "estadoNutricional";
	public static final String PROP_CID = "cid";
	public static final String PROP_PROFISSIONAL = "profissional";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_DATA_LAUDO = "dataLaudo";
	public static final String PROP_TIPO_PROCEDIMENTO = "tipoProcedimento";
	public static final String PROP_PRIORIDADE = "prioridade";
	public static final String PROP_JUSTIFICATIVA_INTERNACAO = "justificativaInternacao";
	public static final String PROP_HISTORIA_PREGRESSA = "historiaPregressa";
	public static final String PROP_EXAME_APAC = "exameApac";
	public static final String PROP_TRANSPORTE_RECOMENDAVEL = "transporteRecomendavel";
	public static final String PROP_EXAME_COMPLEMENTAR_REALIZADO = "exameComplementarRealizado";
	public static final String PROP_FLAG_URGENTE = "flagUrgente";
	public static final String PROP_AVALIACAO_CLINICA_GERAL = "avaliacaoClinicaGeral";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_FLAG_TIPO = "flagTipo";
	public static final String PROP_MEDICAMENTOS_USO = "medicamentosUso";
	public static final String PROP_JUSTIFICATIVA_ACOMPANHANTE = "justificativaAcompanhante";
	public static final String PROP_HISTORICO_DOENCA = "historicoDoenca";
	public static final String PROP_NUMERO_CARTAO = "numeroCartao";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_EXAME_FISICO = "exameFisico";
	public static final String PROP_STATUS = "status";
	public static final String PROP_PEDIDO_TFD = "pedidoTfd";
	public static final String PROP_DIAGNOSTICO_PROVAVEL = "diagnosticoProvavel";
	public static final String PROP_DESCRICAO_CANCELAMENTO = "descricaoCancelamento";
	public static final String PROP_JUSTIFICATIVA_TRANSPORTE = "justificativaTransporte";
	public static final String PROP_JUSTIFICATIVA_IMPEDIMENTO_TRANSPORTE_ONIBUS = "justificativaImpedimentoTransporteOnibus";

	// constructors
	public BaseLaudoTfd () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseLaudoTfd (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseLaudoTfd (
			java.lang.Long codigo,
			br.com.ksisolucoes.vo.basico.Empresa empresa,
			br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimento,
			br.com.ksisolucoes.vo.controle.Usuario usuario,
			br.com.ksisolucoes.vo.prontuario.basico.Cid cid,
			br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento,
			java.util.Date dataLaudo,
			java.util.Date dataCadastro,
			java.lang.Long caraterAtendimento,
			java.lang.String historicoDoenca,
			java.lang.Long status,
			java.lang.Long flagTipo) {

		this.setCodigo(codigo);
		this.setEmpresa(empresa);
		this.setTipoProcedimento(tipoProcedimento);
		this.setUsuario(usuario);
		this.setCid(cid);
		this.setProcedimento(procedimento);
		this.setDataLaudo(dataLaudo);
		this.setDataCadastro(dataCadastro);
		this.setCaraterAtendimento(caraterAtendimento);
		this.setHistoricoDoenca(historicoDoenca);
		this.setStatus(status);
		this.setFlagTipo(flagTipo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataLaudo;
	private java.util.Date dataCadastro;
	private java.lang.Long caraterAtendimento;
	private java.lang.String historicoDoenca;
	private java.lang.String exameFisico;
	private java.lang.String diagnosticoProvavel;
	private java.lang.String exameComplementarRealizado;
	private java.lang.String tratamentoRealizado;
	private java.lang.String justificativaTfd;
	private java.lang.String justificativaAcompanhante;
	private java.lang.Long transporteRecomendavel;
	private java.lang.String justificativaTransporte;
	private java.lang.Long status;
	private java.util.Date dataRecebimentoPaciente;
	private java.lang.Long flagUrgente;
	private java.lang.String observacaoUrgente;
	private java.util.Date dataCancelamento;
	private java.lang.String descricaoCancelamento;
	private java.lang.Long numeroCartao;
	private java.lang.String justificativaLimiteDias;
	private java.lang.String complemento;
	private java.util.Date dataIntegracaoInovamfri;
	private java.lang.String nomeProfissional;
	private java.lang.Long flagTipo;
	private java.lang.Long prioridade;
	private java.lang.String avaliacaoClinicaGeral;
	private java.lang.String medicamentosUso;
	private java.lang.String caracterizacaoUrgencia;
	private java.lang.String historiaPregressa;
	private java.lang.String justificativaInternacao;
	private java.lang.String estadoNutricional;
	private java.lang.String justificativaImpedimentoTransporteOnibus;

	// many to one
	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimento;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissional;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.prontuario.basico.Cid cid;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;
	private br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento;
	private br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd pedidoTfd;
	private br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento;
	private br.com.ksisolucoes.vo.prontuario.basico.ExameApac exameApac;
	private br.com.ksisolucoes.vo.prontuario.basico.ExameBpai exameBpai;



	/**
	 * Return the unique identifier of this class
	 * @hibernate.id
	 *  generator-class="sequence"
	 *  column="cd_laudo_tfd"
	 */
	public java.lang.Long getCodigo () {
		return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_laudo
	 */
	public java.util.Date getDataLaudo () {
		return getPropertyValue(this, dataLaudo, PROP_DATA_LAUDO);
	}

	/**
	 * Set the value related to the column: dt_laudo
	 * @param dataLaudo the dt_laudo value
	 */
	public void setDataLaudo (java.util.Date dataLaudo) {
//        java.util.Date dataLaudoOld = this.dataLaudo;
		this.dataLaudo = dataLaudo;
//        this.getPropertyChangeSupport().firePropertyChange ("dataLaudo", dataLaudoOld, dataLaudo);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO);
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: carater_atendimento
	 */
	public java.lang.Long getCaraterAtendimento () {
		return getPropertyValue(this, caraterAtendimento, PROP_CARATER_ATENDIMENTO);
	}

	/**
	 * Set the value related to the column: carater_atendimento
	 * @param caraterAtendimento the carater_atendimento value
	 */
	public void setCaraterAtendimento (java.lang.Long caraterAtendimento) {
//        java.lang.Long caraterAtendimentoOld = this.caraterAtendimento;
		this.caraterAtendimento = caraterAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("caraterAtendimento", caraterAtendimentoOld, caraterAtendimento);
	}



	/**
	 * Return the value associated with the column: historico_doenca
	 */
	public java.lang.String getHistoricoDoenca () {
		return getPropertyValue(this, historicoDoenca, PROP_HISTORICO_DOENCA);
	}

	/**
	 * Set the value related to the column: historico_doenca
	 * @param historicoDoenca the historico_doenca value
	 */
	public void setHistoricoDoenca (java.lang.String historicoDoenca) {
//        java.lang.String historicoDoencaOld = this.historicoDoenca;
		this.historicoDoenca = historicoDoenca;
//        this.getPropertyChangeSupport().firePropertyChange ("historicoDoenca", historicoDoencaOld, historicoDoenca);
	}



	/**
	 * Return the value associated with the column: exame_fisico
	 */
	public java.lang.String getExameFisico () {
		return getPropertyValue(this, exameFisico, PROP_EXAME_FISICO);
	}

	/**
	 * Set the value related to the column: exame_fisico
	 * @param exameFisico the exame_fisico value
	 */
	public void setExameFisico (java.lang.String exameFisico) {
//        java.lang.String exameFisicoOld = this.exameFisico;
		this.exameFisico = exameFisico;
//        this.getPropertyChangeSupport().firePropertyChange ("exameFisico", exameFisicoOld, exameFisico);
	}



	/**
	 * Return the value associated with the column: diagnostico_provavel
	 */
	public java.lang.String getDiagnosticoProvavel () {
		return getPropertyValue(this, diagnosticoProvavel, PROP_DIAGNOSTICO_PROVAVEL);
	}

	/**
	 * Set the value related to the column: diagnostico_provavel
	 * @param diagnosticoProvavel the diagnostico_provavel value
	 */
	public void setDiagnosticoProvavel (java.lang.String diagnosticoProvavel) {
//        java.lang.String diagnosticoProvavelOld = this.diagnosticoProvavel;
		this.diagnosticoProvavel = diagnosticoProvavel;
//        this.getPropertyChangeSupport().firePropertyChange ("diagnosticoProvavel", diagnosticoProvavelOld, diagnosticoProvavel);
	}



	/**
	 * Return the value associated with the column: exame_complementar_realizado
	 */
	public java.lang.String getExameComplementarRealizado () {
		return getPropertyValue(this, exameComplementarRealizado, PROP_EXAME_COMPLEMENTAR_REALIZADO);
	}

	/**
	 * Set the value related to the column: exame_complementar_realizado
	 * @param exameComplementarRealizado the exame_complementar_realizado value
	 */
	public void setExameComplementarRealizado (java.lang.String exameComplementarRealizado) {
//        java.lang.String exameComplementarRealizadoOld = this.exameComplementarRealizado;
		this.exameComplementarRealizado = exameComplementarRealizado;
//        this.getPropertyChangeSupport().firePropertyChange ("exameComplementarRealizado", exameComplementarRealizadoOld, exameComplementarRealizado);
	}



	/**
	 * Return the value associated with the column: tratamento_realizado
	 */
	public java.lang.String getTratamentoRealizado () {
		return getPropertyValue(this, tratamentoRealizado, PROP_TRATAMENTO_REALIZADO);
	}

	/**
	 * Set the value related to the column: tratamento_realizado
	 * @param tratamentoRealizado the tratamento_realizado value
	 */
	public void setTratamentoRealizado (java.lang.String tratamentoRealizado) {
//        java.lang.String tratamentoRealizadoOld = this.tratamentoRealizado;
		this.tratamentoRealizado = tratamentoRealizado;
//        this.getPropertyChangeSupport().firePropertyChange ("tratamentoRealizado", tratamentoRealizadoOld, tratamentoRealizado);
	}



	/**
	 * Return the value associated with the column: justificativa_tfd
	 */
	public java.lang.String getJustificativaTfd () {
		return getPropertyValue(this, justificativaTfd, PROP_JUSTIFICATIVA_TFD);
	}

	/**
	 * Set the value related to the column: justificativa_tfd
	 * @param justificativaTfd the justificativa_tfd value
	 */
	public void setJustificativaTfd (java.lang.String justificativaTfd) {
//        java.lang.String justificativaTfdOld = this.justificativaTfd;
		this.justificativaTfd = justificativaTfd;
//        this.getPropertyChangeSupport().firePropertyChange ("justificativaTfd", justificativaTfdOld, justificativaTfd);
	}



	/**
	 * Return the value associated with the column: justificativa_acompanhante
	 */
	public java.lang.String getJustificativaAcompanhante () {
		return getPropertyValue(this, justificativaAcompanhante, PROP_JUSTIFICATIVA_ACOMPANHANTE);
	}

	/**
	 * Set the value related to the column: justificativa_acompanhante
	 * @param justificativaAcompanhante the justificativa_acompanhante value
	 */
	public void setJustificativaAcompanhante (java.lang.String justificativaAcompanhante) {
//        java.lang.String justificativaAcompanhanteOld = this.justificativaAcompanhante;
		this.justificativaAcompanhante = justificativaAcompanhante;
//        this.getPropertyChangeSupport().firePropertyChange ("justificativaAcompanhante", justificativaAcompanhanteOld, justificativaAcompanhante);
	}



	/**
	 * Return the value associated with the column: transporte_recomendavel
	 */
	public java.lang.Long getTransporteRecomendavel () {
		return getPropertyValue(this, transporteRecomendavel, PROP_TRANSPORTE_RECOMENDAVEL);
	}

	/**
	 * Set the value related to the column: transporte_recomendavel
	 * @param transporteRecomendavel the transporte_recomendavel value
	 */
	public void setTransporteRecomendavel (java.lang.Long transporteRecomendavel) {
//        java.lang.Long transporteRecomendavelOld = this.transporteRecomendavel;
		this.transporteRecomendavel = transporteRecomendavel;
//        this.getPropertyChangeSupport().firePropertyChange ("transporteRecomendavel", transporteRecomendavelOld, transporteRecomendavel);
	}



	/**
	 * Return the value associated with the column: justificativa_transporte
	 */
	public java.lang.String getJustificativaTransporte () {
		return getPropertyValue(this, justificativaTransporte, PROP_JUSTIFICATIVA_TRANSPORTE);
	}

	/**
	 * Set the value related to the column: justificativa_transporte
	 * @param justificativaTransporte the justificativa_transporte value
	 */
	public void setJustificativaTransporte (java.lang.String justificativaTransporte) {
//        java.lang.String justificativaTransporteOld = this.justificativaTransporte;
		this.justificativaTransporte = justificativaTransporte;
//        this.getPropertyChangeSupport().firePropertyChange ("justificativaTransporte", justificativaTransporteOld, justificativaTransporte);
	}

	/**
	 * Return the value associated with the column: justificativa_impedimento_transporte_onibus
	 */
	public java.lang.String getJustificativaImpedimentoTransporteOnibus () {
		return getPropertyValue(this, justificativaImpedimentoTransporteOnibus, PROP_JUSTIFICATIVA_IMPEDIMENTO_TRANSPORTE_ONIBUS);
	}

	/**
	 * Set the value related to the column: justificativa_impedimento_transporte_onibus
	 * @param justificativaImpedimentoTransporteOnibus the justificativa_impedimento_transporte_onibus value
	 */
	public void setJustificativaImpedimentoTransporteOnibus (java.lang.String justificativaImpedimentoTransporteOnibus) {
//        java.lang.String justificativaImpedimentoTransporteOnibusOld = this.justificativaImpedimentoTransporteOnibus;
		this.justificativaImpedimentoTransporteOnibus = justificativaImpedimentoTransporteOnibus;
//        this.getPropertyChangeSupport().firePropertyChange ("justificativaImpedimentoTransporteOnibus", justificativaImpedimentoTransporteOnibusOld, justificativaImpedimentoTransporteOnibus);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS);
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: dt_rec_paciente
	 */
	public java.util.Date getDataRecebimentoPaciente () {
		return getPropertyValue(this, dataRecebimentoPaciente, PROP_DATA_RECEBIMENTO_PACIENTE);
	}

	/**
	 * Set the value related to the column: dt_rec_paciente
	 * @param dataRecebimentoPaciente the dt_rec_paciente value
	 */
	public void setDataRecebimentoPaciente (java.util.Date dataRecebimentoPaciente) {
//        java.util.Date dataRecebimentoPacienteOld = this.dataRecebimentoPaciente;
		this.dataRecebimentoPaciente = dataRecebimentoPaciente;
//        this.getPropertyChangeSupport().firePropertyChange ("dataRecebimentoPaciente", dataRecebimentoPacienteOld, dataRecebimentoPaciente);
	}



	/**
	 * Return the value associated with the column: flag_urgente
	 */
	public java.lang.Long getFlagUrgente () {
		return getPropertyValue(this, flagUrgente, PROP_FLAG_URGENTE);
	}

	/**
	 * Set the value related to the column: flag_urgente
	 * @param flagUrgente the flag_urgente value
	 */
	public void setFlagUrgente (java.lang.Long flagUrgente) {
//        java.lang.Long flagUrgenteOld = this.flagUrgente;
		this.flagUrgente = flagUrgente;
//        this.getPropertyChangeSupport().firePropertyChange ("flagUrgente", flagUrgenteOld, flagUrgente);
	}



	/**
	 * Return the value associated with the column: obs_urgente
	 */
	public java.lang.String getObservacaoUrgente () {
		return getPropertyValue(this, observacaoUrgente, PROP_OBSERVACAO_URGENTE);
	}

	/**
	 * Set the value related to the column: obs_urgente
	 * @param observacaoUrgente the obs_urgente value
	 */
	public void setObservacaoUrgente (java.lang.String observacaoUrgente) {
//        java.lang.String observacaoUrgenteOld = this.observacaoUrgente;
		this.observacaoUrgente = observacaoUrgente;
//        this.getPropertyChangeSupport().firePropertyChange ("observacaoUrgente", observacaoUrgenteOld, observacaoUrgente);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO);
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: ds_cancelamento
	 */
	public java.lang.String getDescricaoCancelamento () {
		return getPropertyValue(this, descricaoCancelamento, PROP_DESCRICAO_CANCELAMENTO);
	}

	/**
	 * Set the value related to the column: ds_cancelamento
	 * @param descricaoCancelamento the ds_cancelamento value
	 */
	public void setDescricaoCancelamento (java.lang.String descricaoCancelamento) {
//        java.lang.String descricaoCancelamentoOld = this.descricaoCancelamento;
		this.descricaoCancelamento = descricaoCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoCancelamento", descricaoCancelamentoOld, descricaoCancelamento);
	}



	/**
	 * Return the value associated with the column: cd_numero_cartao
	 */
	public java.lang.Long getNumeroCartao () {
		return getPropertyValue(this, numeroCartao, PROP_NUMERO_CARTAO);
	}

	/**
	 * Set the value related to the column: cd_numero_cartao
	 * @param numeroCartao the cd_numero_cartao value
	 */
	public void setNumeroCartao (java.lang.Long numeroCartao) {
//        java.lang.Long numeroCartaoOld = this.numeroCartao;
		this.numeroCartao = numeroCartao;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroCartao", numeroCartaoOld, numeroCartao);
	}



	/**
	 * Return the value associated with the column: just_lim_dias
	 */
	public java.lang.String getJustificativaLimiteDias () {
		return getPropertyValue(this, justificativaLimiteDias, PROP_JUSTIFICATIVA_LIMITE_DIAS);
	}

	/**
	 * Set the value related to the column: just_lim_dias
	 * @param justificativaLimiteDias the just_lim_dias value
	 */
	public void setJustificativaLimiteDias (java.lang.String justificativaLimiteDias) {
//        java.lang.String justificativaLimiteDiasOld = this.justificativaLimiteDias;
		this.justificativaLimiteDias = justificativaLimiteDias;
//        this.getPropertyChangeSupport().firePropertyChange ("justificativaLimiteDias", justificativaLimiteDiasOld, justificativaLimiteDias);
	}



	/**
	 * Return the value associated with the column: complemento
	 */
	public java.lang.String getComplemento () {
		return getPropertyValue(this, complemento, PROP_COMPLEMENTO);
	}

	/**
	 * Set the value related to the column: complemento
	 * @param complemento the complemento value
	 */
	public void setComplemento (java.lang.String complemento) {
//        java.lang.String complementoOld = this.complemento;
		this.complemento = complemento;
//        this.getPropertyChangeSupport().firePropertyChange ("complemento", complementoOld, complemento);
	}



	/**
	 * Return the value associated with the column: dt_integracao_inovamfri
	 */
	public java.util.Date getDataIntegracaoInovamfri () {
		return getPropertyValue(this, dataIntegracaoInovamfri, PROP_DATA_INTEGRACAO_INOVAMFRI);
	}

	/**
	 * Set the value related to the column: dt_integracao_inovamfri
	 * @param dataIntegracaoInovamfri the dt_integracao_inovamfri value
	 */
	public void setDataIntegracaoInovamfri (java.util.Date dataIntegracaoInovamfri) {
//        java.util.Date dataIntegracaoInovamfriOld = this.dataIntegracaoInovamfri;
		this.dataIntegracaoInovamfri = dataIntegracaoInovamfri;
//        this.getPropertyChangeSupport().firePropertyChange ("dataIntegracaoInovamfri", dataIntegracaoInovamfriOld, dataIntegracaoInovamfri);
	}



	/**
	 * Return the value associated with the column: nome_profissional
	 */
	public java.lang.String getNomeProfissional () {
		return getPropertyValue(this, nomeProfissional, PROP_NOME_PROFISSIONAL);
	}

	/**
	 * Set the value related to the column: nome_profissional
	 * @param nomeProfissional the nome_profissional value
	 */
	public void setNomeProfissional (java.lang.String nomeProfissional) {
//        java.lang.String nomeProfissionalOld = this.nomeProfissional;
		this.nomeProfissional = nomeProfissional;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeProfissional", nomeProfissionalOld, nomeProfissional);
	}



	/**
	 * Return the value associated with the column: flag_tipo
	 */
	public java.lang.Long getFlagTipo () {
		return getPropertyValue(this, flagTipo, PROP_FLAG_TIPO);
	}

	/**
	 * Set the value related to the column: flag_tipo
	 * @param flagTipo the flag_tipo value
	 */
	public void setFlagTipo (java.lang.Long flagTipo) {
//        java.lang.Long flagTipoOld = this.flagTipo;
		this.flagTipo = flagTipo;
//        this.getPropertyChangeSupport().firePropertyChange ("flagTipo", flagTipoOld, flagTipo);
	}



	/**
	 * Return the value associated with the column: prioridade
	 */
	public java.lang.Long getPrioridade () {
		return getPropertyValue(this, prioridade, PROP_PRIORIDADE);
	}

	/**
	 * Set the value related to the column: prioridade
	 * @param prioridade the prioridade value
	 */
	public void setPrioridade (java.lang.Long prioridade) {
//        java.lang.Long prioridadeOld = this.prioridade;
		this.prioridade = prioridade;
//        this.getPropertyChangeSupport().firePropertyChange ("prioridade", prioridadeOld, prioridade);
	}



	/**
	 * Return the value associated with the column: avaliacao_clinica_geral
	 */
	public java.lang.String getAvaliacaoClinicaGeral () {
		return getPropertyValue(this, avaliacaoClinicaGeral, PROP_AVALIACAO_CLINICA_GERAL);
	}

	/**
	 * Set the value related to the column: avaliacao_clinica_geral
	 * @param avaliacaoClinicaGeral the avaliacao_clinica_geral value
	 */
	public void setAvaliacaoClinicaGeral (java.lang.String avaliacaoClinicaGeral) {
//        java.lang.String avaliacaoClinicaGeralOld = this.avaliacaoClinicaGeral;
		this.avaliacaoClinicaGeral = avaliacaoClinicaGeral;
//        this.getPropertyChangeSupport().firePropertyChange ("avaliacaoClinicaGeral", avaliacaoClinicaGeralOld, avaliacaoClinicaGeral);
	}



	/**
	 * Return the value associated with the column: medicamentos_uso
	 */
	public java.lang.String getMedicamentosUso () {
		return getPropertyValue(this, medicamentosUso, PROP_MEDICAMENTOS_USO);
	}

	/**
	 * Set the value related to the column: medicamentos_uso
	 * @param medicamentosUso the medicamentos_uso value
	 */
	public void setMedicamentosUso (java.lang.String medicamentosUso) {
//        java.lang.String medicamentosUsoOld = this.medicamentosUso;
		this.medicamentosUso = medicamentosUso;
//        this.getPropertyChangeSupport().firePropertyChange ("medicamentosUso", medicamentosUsoOld, medicamentosUso);
	}



	/**
	 * Return the value associated with the column: caracterizacao_urgencia
	 */
	public java.lang.String getCaracterizacaoUrgencia () {
		return getPropertyValue(this, caracterizacaoUrgencia, PROP_CARACTERIZACAO_URGENCIA);
	}

	/**
	 * Set the value related to the column: caracterizacao_urgencia
	 * @param caracterizacaoUrgencia the caracterizacao_urgencia value
	 */
	public void setCaracterizacaoUrgencia (java.lang.String caracterizacaoUrgencia) {
//        java.lang.String caracterizacaoUrgenciaOld = this.caracterizacaoUrgencia;
		this.caracterizacaoUrgencia = caracterizacaoUrgencia;
//        this.getPropertyChangeSupport().firePropertyChange ("caracterizacaoUrgencia", caracterizacaoUrgenciaOld, caracterizacaoUrgencia);
	}



	/**
	 * Return the value associated with the column: historia_pregressa
	 */
	public java.lang.String getHistoriaPregressa () {
		return getPropertyValue(this, historiaPregressa, PROP_HISTORIA_PREGRESSA);
	}

	/**
	 * Set the value related to the column: historia_pregressa
	 * @param historiaPregressa the historia_pregressa value
	 */
	public void setHistoriaPregressa (java.lang.String historiaPregressa) {
//        java.lang.String historiaPregressaOld = this.historiaPregressa;
		this.historiaPregressa = historiaPregressa;
//        this.getPropertyChangeSupport().firePropertyChange ("historiaPregressa", historiaPregressaOld, historiaPregressa);
	}



	/**
	 * Return the value associated with the column: justificativa_internacao
	 */
	public java.lang.String getJustificativaInternacao () {
		return getPropertyValue(this, justificativaInternacao, PROP_JUSTIFICATIVA_INTERNACAO);
	}

	/**
	 * Set the value related to the column: justificativa_internacao
	 * @param justificativaInternacao the justificativa_internacao value
	 */
	public void setJustificativaInternacao (java.lang.String justificativaInternacao) {
//        java.lang.String justificativaInternacaoOld = this.justificativaInternacao;
		this.justificativaInternacao = justificativaInternacao;
//        this.getPropertyChangeSupport().firePropertyChange ("justificativaInternacao", justificativaInternacaoOld, justificativaInternacao);
	}



	/**
	 * Return the value associated with the column: estado_nutricional
	 */
	public java.lang.String getEstadoNutricional () {
		return getPropertyValue(this, estadoNutricional, PROP_ESTADO_NUTRICIONAL);
	}

	/**
	 * Set the value related to the column: estado_nutricional
	 * @param estadoNutricional the estado_nutricional value
	 */
	public void setEstadoNutricional (java.lang.String estadoNutricional) {
//        java.lang.String estadoNutricionalOld = this.estadoNutricional;
		this.estadoNutricional = estadoNutricional;
//        this.getPropertyChangeSupport().firePropertyChange ("estadoNutricional", estadoNutricionalOld, estadoNutricional);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA);
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS);
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cd_tp_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento getTipoProcedimento () {
		return getPropertyValue(this, tipoProcedimento, PROP_TIPO_PROCEDIMENTO);
	}

	/**
	 * Set the value related to the column: cd_tp_procedimento
	 * @param tipoProcedimento the cd_tp_procedimento value
	 */
	public void setTipoProcedimento (br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimentoOld = this.tipoProcedimento;
		this.tipoProcedimento = tipoProcedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoProcedimento", tipoProcedimentoOld, tipoProcedimento);
	}



	/**
	 * Return the value associated with the column: cd_profissional
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissional () {
		return getPropertyValue(this, profissional, PROP_PROFISSIONAL);
	}

	/**
	 * Set the value related to the column: cd_profissional
	 * @param profissional the cd_profissional value
	 */
	public void setProfissional (br.com.ksisolucoes.vo.cadsus.Profissional profissional) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalOld = this.profissional;
		this.profissional = profissional;
//        this.getPropertyChangeSupport().firePropertyChange ("profissional", profissionalOld, profissional);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO);
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_cid
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Cid getCid () {
		return getPropertyValue(this, cid, PROP_CID);
	}

	/**
	 * Set the value related to the column: cd_cid
	 * @param cid the cd_cid value
	 */
	public void setCid (br.com.ksisolucoes.vo.prontuario.basico.Cid cid) {
//        br.com.ksisolucoes.vo.prontuario.basico.Cid cidOld = this.cid;
		this.cid = cid;
//        this.getPropertyChangeSupport().firePropertyChange ("cid", cidOld, cid);
	}



	/**
	 * Return the value associated with the column: cd_usu_can
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO);
	}

	/**
	 * Set the value related to the column: cd_usu_can
	 * @param usuarioCancelamento the cd_usu_can value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}



	/**
	 * Return the value associated with the column: nr_atendimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Atendimento getAtendimento () {
		return getPropertyValue(this, atendimento, PROP_ATENDIMENTO);
	}

	/**
	 * Set the value related to the column: nr_atendimento
	 * @param atendimento the nr_atendimento value
	 */
	public void setAtendimento (br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoOld = this.atendimento;
		this.atendimento = atendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimento", atendimentoOld, atendimento);
	}



	/**
	 * Return the value associated with the column: cd_pedido_tfd
	 */
	public br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd getPedidoTfd () {
		return getPropertyValue(this, pedidoTfd, PROP_PEDIDO_TFD);
	}

	/**
	 * Set the value related to the column: cd_pedido_tfd
	 * @param pedidoTfd the cd_pedido_tfd value
	 */
	public void setPedidoTfd (br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd pedidoTfd) {
//        br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd pedidoTfdOld = this.pedidoTfd;
		this.pedidoTfd = pedidoTfd;
//        this.getPropertyChangeSupport().firePropertyChange ("pedidoTfd", pedidoTfdOld, pedidoTfd);
	}



	/**
	 * Return the value associated with the column: cd_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento getProcedimento () {
		return getPropertyValue(this, procedimento, PROP_PROCEDIMENTO);
	}

	/**
	 * Set the value related to the column: cd_procedimento
	 * @param procedimento the cd_procedimento value
	 */
	public void setProcedimento (br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoOld = this.procedimento;
		this.procedimento = procedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimento", procedimentoOld, procedimento);
	}



	/**
	 * Return the value associated with the column: cd_exame_apac
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ExameApac getExameApac () {
		return getPropertyValue(this, exameApac, PROP_EXAME_APAC);
	}

	/**
	 * Set the value related to the column: cd_exame_apac
	 * @param exameApac the cd_exame_apac value
	 */
	public void setExameApac (br.com.ksisolucoes.vo.prontuario.basico.ExameApac exameApac) {
//        br.com.ksisolucoes.vo.prontuario.basico.ExameApac exameApacOld = this.exameApac;
		this.exameApac = exameApac;
//        this.getPropertyChangeSupport().firePropertyChange ("exameApac", exameApacOld, exameApac);
	}



	/**
	 * Return the value associated with the column: cd_exame_bpai
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ExameBpai getExameBpai () {
		return getPropertyValue(this, exameBpai, PROP_EXAME_BPAI);
	}

	/**
	 * Set the value related to the column: cd_exame_bpai
	 * @param exameBpai the cd_exame_bpai value
	 */
	public void setExameBpai (br.com.ksisolucoes.vo.prontuario.basico.ExameBpai exameBpai) {
//        br.com.ksisolucoes.vo.prontuario.basico.ExameBpai exameBpaiOld = this.exameBpai;
		this.exameBpai = exameBpai;
//        this.getPropertyChangeSupport().firePropertyChange ("exameBpai", exameBpaiOld, exameBpai);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd)) return false;
		else {
			br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd laudoTfd = (br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd) obj;
			if (null == this.getCodigo() || null == laudoTfd.getCodigo()) return false;
			else return (this.getCodigo().equals(laudoTfd.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

	public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
		return this.retornoValidacao;
	}

	public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
	}

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}