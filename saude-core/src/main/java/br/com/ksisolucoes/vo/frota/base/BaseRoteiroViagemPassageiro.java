package br.com.ksisolucoes.vo.frota.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the roteiro_viagem_passageiro table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="roteiro_viagem_passageiro"
 */

public abstract class BaseRoteiroViagemPassageiro extends BaseRootVO implements Serializable {

	public static String REF = "RoteiroViagemPassageiro";
	public static final String PROP_STATUS = "status";
	public static final String PROP_FLAG_ENVIO_MENSAGEM_CANCELAMENTO = "flagEnvioMensagemCancelamento";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_FLAG_IMPRESSO = "flagImpresso";
	public static final String PROP_ROTEIRO = "roteiro";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_DESTINO = "destino";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_REGISTRO_FALTA = "dataRegistroFalta";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";
	public static final String PROP_SOLICITACAO_VIAGEM = "solicitacaoViagem";
	public static final String PROP_TIPO_VIAGEM = "tipoViagem";
	public static final String PROP_USUARIO_FALTA = "usuarioFalta";
	public static final String PROP_DATA_USUARIO = "dataUsuario";
	public static final String PROP_LOCAL_EMBARQUE = "localEmbarque";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_TIPO_PASSAGEIRO = "tipoPassageiro";
	public static final String PROP_HORARIO = "horario";
	public static final String PROP_USUARIO_CADSUS_ACOMPANHADO = "usuarioCadsusAcompanhado";


	// constructors
	public BaseRoteiroViagemPassageiro () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseRoteiroViagemPassageiro (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseRoteiroViagemPassageiro (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.frota.RoteiroViagem roteiro,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.frota.viagem.solicitacao.SolicitacaoViagem solicitacaoViagem,
		java.lang.String destino,
		java.util.Date dataCadastro,
		java.util.Date dataUsuario,
		java.lang.Long status,
		java.lang.Long tipoViagem) {

		this.setCodigo(codigo);
		this.setRoteiro(roteiro);
		this.setUsuario(usuario);
		this.setSolicitacaoViagem(solicitacaoViagem);
		this.setDestino(destino);
		this.setDataCadastro(dataCadastro);
		this.setDataUsuario(dataUsuario);
		this.setStatus(status);
		this.setTipoViagem(tipoViagem);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String destino;
	private java.lang.String observacao;
	private java.util.Date dataCadastro;
	private java.util.Date horario;
	private java.util.Date dataUsuario;
	private java.lang.Long status;
	private java.util.Date dataRegistroFalta;
	private java.lang.String localEmbarque;
	private java.lang.Long tipoViagem;
	private java.lang.Long tipoPassageiro;
	private java.util.Date dataCancelamento;
	private java.lang.Long flagEnvioMensagemCancelamento;
	private java.lang.Long flagImpresso;

	// many to one
	private br.com.ksisolucoes.vo.frota.RoteiroViagem roteiro;
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.frota.viagem.solicitacao.SolicitacaoViagem solicitacaoViagem;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioFalta;
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusAcompanhado;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_roteiro_paciente"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: destino
	 */
	public java.lang.String getDestino () {
		return getPropertyValue(this, destino, PROP_DESTINO); 
	}

	/**
	 * Set the value related to the column: destino
	 * @param destino the destino value
	 */
	public void setDestino (java.lang.String destino) {
//        java.lang.String destinoOld = this.destino;
		this.destino = destino;
//        this.getPropertyChangeSupport().firePropertyChange ("destino", destinoOld, destino);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: horario
	 */
	public java.util.Date getHorario () {
		return getPropertyValue(this, horario, PROP_HORARIO); 
	}

	/**
	 * Set the value related to the column: horario
	 * @param horario the horario value
	 */
	public void setHorario (java.util.Date horario) {
//        java.util.Date horarioOld = this.horario;
		this.horario = horario;
//        this.getPropertyChangeSupport().firePropertyChange ("horario", horarioOld, horario);
	}



	/**
	 * Return the value associated with the column: dt_usuario
	 */
	public java.util.Date getDataUsuario () {
		return getPropertyValue(this, dataUsuario, PROP_DATA_USUARIO); 
	}

	/**
	 * Set the value related to the column: dt_usuario
	 * @param dataUsuario the dt_usuario value
	 */
	public void setDataUsuario (java.util.Date dataUsuario) {
//        java.util.Date dataUsuarioOld = this.dataUsuario;
		this.dataUsuario = dataUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsuario", dataUsuarioOld, dataUsuario);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: dt_registro_falta
	 */
	public java.util.Date getDataRegistroFalta () {
		return getPropertyValue(this, dataRegistroFalta, PROP_DATA_REGISTRO_FALTA); 
	}

	/**
	 * Set the value related to the column: dt_registro_falta
	 * @param dataRegistroFalta the dt_registro_falta value
	 */
	public void setDataRegistroFalta (java.util.Date dataRegistroFalta) {
//        java.util.Date dataRegistroFaltaOld = this.dataRegistroFalta;
		this.dataRegistroFalta = dataRegistroFalta;
//        this.getPropertyChangeSupport().firePropertyChange ("dataRegistroFalta", dataRegistroFaltaOld, dataRegistroFalta);
	}



	/**
	 * Return the value associated with the column: local_embarque
	 */
	public java.lang.String getLocalEmbarque () {
		return getPropertyValue(this, localEmbarque, PROP_LOCAL_EMBARQUE); 
	}

	/**
	 * Set the value related to the column: local_embarque
	 * @param localEmbarque the local_embarque value
	 */
	public void setLocalEmbarque (java.lang.String localEmbarque) {
//        java.lang.String localEmbarqueOld = this.localEmbarque;
		this.localEmbarque = localEmbarque;
//        this.getPropertyChangeSupport().firePropertyChange ("localEmbarque", localEmbarqueOld, localEmbarque);
	}



	/**
	 * Return the value associated with the column: tp_viagem
	 */
	public java.lang.Long getTipoViagem () {
		return getPropertyValue(this, tipoViagem, PROP_TIPO_VIAGEM); 
	}

	/**
	 * Set the value related to the column: tp_viagem
	 * @param tipoViagem the tp_viagem value
	 */
	public void setTipoViagem (java.lang.Long tipoViagem) {
//        java.lang.Long tipoViagemOld = this.tipoViagem;
		this.tipoViagem = tipoViagem;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoViagem", tipoViagemOld, tipoViagem);
	}



	/**
	 * Return the value associated with the column: flag_tipo_passageiro
	 */
	public java.lang.Long getTipoPassageiro () {
		return getPropertyValue(this, tipoPassageiro, PROP_TIPO_PASSAGEIRO); 
	}

	/**
	 * Set the value related to the column: flag_tipo_passageiro
	 * @param tipoPassageiro the flag_tipo_passageiro value
	 */
	public void setTipoPassageiro (java.lang.Long tipoPassageiro) {
//        java.lang.Long tipoPassageiroOld = this.tipoPassageiro;
		this.tipoPassageiro = tipoPassageiro;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoPassageiro", tipoPassageiroOld, tipoPassageiro);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: flag_envio_mensagem_cancelamento
	 */
	public java.lang.Long getFlagEnvioMensagemCancelamento () {
		return getPropertyValue(this, flagEnvioMensagemCancelamento, PROP_FLAG_ENVIO_MENSAGEM_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: flag_envio_mensagem_cancelamento
	 * @param flagEnvioMensagemCancelamento the flag_envio_mensagem_cancelamento value
	 */
	public void setFlagEnvioMensagemCancelamento (java.lang.Long flagEnvioMensagemCancelamento) {
//        java.lang.Long flagEnvioMensagemCancelamentoOld = this.flagEnvioMensagemCancelamento;
		this.flagEnvioMensagemCancelamento = flagEnvioMensagemCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("flagEnvioMensagemCancelamento", flagEnvioMensagemCancelamentoOld, flagEnvioMensagemCancelamento);
	}



	/**
	 * Return the value associated with the column: flag_impresso
	 */
	public java.lang.Long getFlagImpresso () {
		return getPropertyValue(this, flagImpresso, PROP_FLAG_IMPRESSO); 
	}

	/**
	 * Set the value related to the column: flag_impresso
	 * @param flagImpresso the flag_impresso value
	 */
	public void setFlagImpresso (java.lang.Long flagImpresso) {
//        java.lang.Long flagImpressoOld = this.flagImpresso;
		this.flagImpresso = flagImpresso;
//        this.getPropertyChangeSupport().firePropertyChange ("flagImpresso", flagImpressoOld, flagImpresso);
	}



	/**
	 * Return the value associated with the column: cd_roteiro
	 */
	public br.com.ksisolucoes.vo.frota.RoteiroViagem getRoteiro () {
		return getPropertyValue(this, roteiro, PROP_ROTEIRO); 
	}

	/**
	 * Set the value related to the column: cd_roteiro
	 * @param roteiro the cd_roteiro value
	 */
	public void setRoteiro (br.com.ksisolucoes.vo.frota.RoteiroViagem roteiro) {
//        br.com.ksisolucoes.vo.frota.RoteiroViagem roteiroOld = this.roteiro;
		this.roteiro = roteiro;
//        this.getPropertyChangeSupport().firePropertyChange ("roteiro", roteiroOld, roteiro);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_solicitacao_viagem
	 */
	public br.com.ksisolucoes.vo.frota.viagem.solicitacao.SolicitacaoViagem getSolicitacaoViagem () {
		return getPropertyValue(this, solicitacaoViagem, PROP_SOLICITACAO_VIAGEM); 
	}

	/**
	 * Set the value related to the column: cd_solicitacao_viagem
	 * @param solicitacaoViagem the cd_solicitacao_viagem value
	 */
	public void setSolicitacaoViagem (br.com.ksisolucoes.vo.frota.viagem.solicitacao.SolicitacaoViagem solicitacaoViagem) {
//        br.com.ksisolucoes.vo.frota.viagem.solicitacao.SolicitacaoViagem solicitacaoViagemOld = this.solicitacaoViagem;
		this.solicitacaoViagem = solicitacaoViagem;
//        this.getPropertyChangeSupport().firePropertyChange ("solicitacaoViagem", solicitacaoViagemOld, solicitacaoViagem);
	}



	/**
	 * Return the value associated with the column: cd_usu_falta
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioFalta () {
		return getPropertyValue(this, usuarioFalta, PROP_USUARIO_FALTA); 
	}

	/**
	 * Set the value related to the column: cd_usu_falta
	 * @param usuarioFalta the cd_usu_falta value
	 */
	public void setUsuarioFalta (br.com.ksisolucoes.vo.controle.Usuario usuarioFalta) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioFaltaOld = this.usuarioFalta;
		this.usuarioFalta = usuarioFalta;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioFalta", usuarioFaltaOld, usuarioFalta);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus_acompanhado
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsusAcompanhado () {
		return getPropertyValue(this, usuarioCadsusAcompanhado, PROP_USUARIO_CADSUS_ACOMPANHADO); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus_acompanhado
	 * @param usuarioCadsusAcompanhado the cd_usu_cadsus_acompanhado value
	 */
	public void setUsuarioCadsusAcompanhado (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusAcompanhado) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusAcompanhadoOld = this.usuarioCadsusAcompanhado;
		this.usuarioCadsusAcompanhado = usuarioCadsusAcompanhado;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsusAcompanhado", usuarioCadsusAcompanhadoOld, usuarioCadsusAcompanhado);
	}



	/**
	 * Return the value associated with the column: cd_usu_cancelamento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usu_cancelamento
	 * @param usuarioCancelamento the cd_usu_cancelamento value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.frota.RoteiroViagemPassageiro)) return false;
		else {
			br.com.ksisolucoes.vo.frota.RoteiroViagemPassageiro roteiroViagemPassageiro = (br.com.ksisolucoes.vo.frota.RoteiroViagemPassageiro) obj;
			if (null == this.getCodigo() || null == roteiroViagemPassageiro.getCodigo()) return false;
			else return (this.getCodigo().equals(roteiroViagemPassageiro.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}