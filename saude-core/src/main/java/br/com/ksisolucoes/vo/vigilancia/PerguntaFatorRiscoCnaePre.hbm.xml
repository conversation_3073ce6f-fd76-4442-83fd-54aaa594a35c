<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia">
    <class name="PerguntaFatorRiscoCnaePre" table="pergunta_fator_risco_cnae_pre">
        <id
                column="cd_pergunta_fator_risco_cnae_pre"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="sequence">
                <param name="sequence">seq_pergunta_fator_risco_cnae_pre</param>
            </generator>
        </id>
        <version column="version" name="version" type="long"/>


        <many-to-one
                name="fatorRiscoCnae"
                class="br.com.ksisolucoes.vo.vigilancia.FatorRiscoCnae"
        >
            <column name="cd_fator_risco_cnae"/>
        </many-to-one>

        <property
                column="condicao_pre"
                name="condicaoPre"
                length="255"
                type="java.lang.String"
        />

        <property name="dataCriacao" type="java.util.Date">
            <column name="dt_criacao"/>
        </property>

        <property name="dataAutualizacao" type="java.util.Date">
            <column name="dt_autualizacao"/>
        </property>

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                name="usuarioCriacao"
        >
            <column name="cd_usuario_criacao" />
        </many-to-one>



    </class>
</hibernate-mapping>
