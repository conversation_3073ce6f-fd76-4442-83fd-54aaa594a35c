package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the atendimento_alta table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="atendimento_alta"
 */

public abstract class BaseAtendimentoAlta extends BaseRootVO implements Serializable {

	public static String REF = "AtendimentoAlta";
	public static final String PROP_USUARIO_IMPRESSAO_NOTA_ALTA = "usuarioImpressaoNotaAlta";
	public static final String PROP_HISTORICO_RESUMIDO = "historicoResumido";
	public static final String PROP_ORIENTACOES_ALTA = "orientacoesAlta";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_ATENDIMENTO = "atendimento";
	public static final String PROP_DATA_ALTA = "dataAlta";
	public static final String PROP_DATA_USUARIO_IMPRESSAO_NOTA_ALTA = "dataUsuarioImpressaoNotaAlta";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_MOTIVO_ALTA = "motivoAlta";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_CLASSIFICACAO_ATENDIMENTO = "classificacaoAtendimento";
	public static final String PROP_CID = "cid";


	// constructors
	public BaseAtendimentoAlta () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAtendimentoAlta (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseAtendimentoAlta (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {

		this.setCodigo(codigo);
		this.setAtendimento(atendimento);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataAlta;
	private java.lang.Long motivoAlta;
	private java.util.Date dataCadastro;
	private java.lang.String orientacoesAlta;
	private java.util.Date dataUsuarioImpressaoNotaAlta;
	private java.lang.String historicoResumido;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento;
	private br.com.ksisolucoes.vo.prontuario.basico.Cid cid;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioImpressaoNotaAlta;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.prontuario.basico.ClassificacaoAtendimento classificacaoAtendimento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_atendimento_alta"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_alta
	 */
	public java.util.Date getDataAlta () {
		return getPropertyValue(this, dataAlta, PROP_DATA_ALTA); 
	}

	/**
	 * Set the value related to the column: dt_alta
	 * @param dataAlta the dt_alta value
	 */
	public void setDataAlta (java.util.Date dataAlta) {
//        java.util.Date dataAltaOld = this.dataAlta;
		this.dataAlta = dataAlta;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAlta", dataAltaOld, dataAlta);
	}



	/**
	 * Return the value associated with the column: motivo_alta
	 */
	public java.lang.Long getMotivoAlta () {
		return getPropertyValue(this, motivoAlta, PROP_MOTIVO_ALTA); 
	}

	/**
	 * Set the value related to the column: motivo_alta
	 * @param motivoAlta the motivo_alta value
	 */
	public void setMotivoAlta (java.lang.Long motivoAlta) {
//        java.lang.Long motivoAltaOld = this.motivoAlta;
		this.motivoAlta = motivoAlta;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoAlta", motivoAltaOld, motivoAlta);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: orientacoes_alta
	 */
	public java.lang.String getOrientacoesAlta () {
		return getPropertyValue(this, orientacoesAlta, PROP_ORIENTACOES_ALTA); 
	}

	/**
	 * Set the value related to the column: orientacoes_alta
	 * @param orientacoesAlta the orientacoes_alta value
	 */
	public void setOrientacoesAlta (java.lang.String orientacoesAlta) {
//        java.lang.String orientacoesAltaOld = this.orientacoesAlta;
		this.orientacoesAlta = orientacoesAlta;
//        this.getPropertyChangeSupport().firePropertyChange ("orientacoesAlta", orientacoesAltaOld, orientacoesAlta);
	}



	/**
	 * Return the value associated with the column: dt_usuario_imp_nota_alta
	 */
	public java.util.Date getDataUsuarioImpressaoNotaAlta () {
		return getPropertyValue(this, dataUsuarioImpressaoNotaAlta, PROP_DATA_USUARIO_IMPRESSAO_NOTA_ALTA); 
	}

	/**
	 * Set the value related to the column: dt_usuario_imp_nota_alta
	 * @param dataUsuarioImpressaoNotaAlta the dt_usuario_imp_nota_alta value
	 */
	public void setDataUsuarioImpressaoNotaAlta (java.util.Date dataUsuarioImpressaoNotaAlta) {
//        java.util.Date dataUsuarioImpressaoNotaAltaOld = this.dataUsuarioImpressaoNotaAlta;
		this.dataUsuarioImpressaoNotaAlta = dataUsuarioImpressaoNotaAlta;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsuarioImpressaoNotaAlta", dataUsuarioImpressaoNotaAltaOld, dataUsuarioImpressaoNotaAlta);
	}



	/**
	 * Return the value associated with the column: historico_resumido
	 */
	public java.lang.String getHistoricoResumido () {
		return getPropertyValue(this, historicoResumido, PROP_HISTORICO_RESUMIDO); 
	}

	/**
	 * Set the value related to the column: historico_resumido
	 * @param historicoResumido the historico_resumido value
	 */
	public void setHistoricoResumido (java.lang.String historicoResumido) {
//        java.lang.String historicoResumidoOld = this.historicoResumido;
		this.historicoResumido = historicoResumido;
//        this.getPropertyChangeSupport().firePropertyChange ("historicoResumido", historicoResumidoOld, historicoResumido);
	}



	/**
	 * Return the value associated with the column: nr_atendimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Atendimento getAtendimento () {
		return getPropertyValue(this, atendimento, PROP_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: nr_atendimento
	 * @param atendimento the nr_atendimento value
	 */
	public void setAtendimento (br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoOld = this.atendimento;
		this.atendimento = atendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimento", atendimentoOld, atendimento);
	}



	/**
	 * Return the value associated with the column: cd_cid
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Cid getCid () {
		return getPropertyValue(this, cid, PROP_CID); 
	}

	/**
	 * Set the value related to the column: cd_cid
	 * @param cid the cd_cid value
	 */
	public void setCid (br.com.ksisolucoes.vo.prontuario.basico.Cid cid) {
//        br.com.ksisolucoes.vo.prontuario.basico.Cid cidOld = this.cid;
		this.cid = cid;
//        this.getPropertyChangeSupport().firePropertyChange ("cid", cidOld, cid);
	}



	/**
	 * Return the value associated with the column: cd_usuario_imp_nota_alta
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioImpressaoNotaAlta () {
		return getPropertyValue(this, usuarioImpressaoNotaAlta, PROP_USUARIO_IMPRESSAO_NOTA_ALTA); 
	}

	/**
	 * Set the value related to the column: cd_usuario_imp_nota_alta
	 * @param usuarioImpressaoNotaAlta the cd_usuario_imp_nota_alta value
	 */
	public void setUsuarioImpressaoNotaAlta (br.com.ksisolucoes.vo.controle.Usuario usuarioImpressaoNotaAlta) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioImpressaoNotaAltaOld = this.usuarioImpressaoNotaAlta;
		this.usuarioImpressaoNotaAlta = usuarioImpressaoNotaAlta;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioImpressaoNotaAlta", usuarioImpressaoNotaAltaOld, usuarioImpressaoNotaAlta);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_cla_atend
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ClassificacaoAtendimento getClassificacaoAtendimento () {
		return getPropertyValue(this, classificacaoAtendimento, PROP_CLASSIFICACAO_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_cla_atend
	 * @param classificacaoAtendimento the cd_cla_atend value
	 */
	public void setClassificacaoAtendimento (br.com.ksisolucoes.vo.prontuario.basico.ClassificacaoAtendimento classificacaoAtendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.ClassificacaoAtendimento classificacaoAtendimentoOld = this.classificacaoAtendimento;
		this.classificacaoAtendimento = classificacaoAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("classificacaoAtendimento", classificacaoAtendimentoOld, classificacaoAtendimento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.AtendimentoAlta)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.AtendimentoAlta atendimentoAlta = (br.com.ksisolucoes.vo.prontuario.basico.AtendimentoAlta) obj;
			if (null == this.getCodigo() || null == atendimentoAlta.getCodigo()) return false;
			else return (this.getCodigo().equals(atendimentoAlta.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}