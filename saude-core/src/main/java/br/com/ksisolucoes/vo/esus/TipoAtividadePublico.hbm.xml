<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.esus"  >
    <class name="TipoAtividadePublico" table="tipo_atividade_publico" >
         
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_publico_alvo"
        >
            <generator class="assigned" />
        </id> 

        <version column="version" name="version" type="long" />

        <property
            name="descricaoPublicoAlvo"
            column="ds_publico_alvo"
            type="java.lang.String"
            not-null="true"
        />

		<property 
            name="codigoEsus"
            column="cd_esus"
            type="java.lang.Long"
            not-null="true"
        />

        <property
                name="versionAll"
                column="version_all"
                type="java.lang.Long"
        />

    </class>
</hibernate-mapping>
