<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.cadsus.cds"  >
    <class name="EsusFichaAtendIndividualItemProcedimento" table="esus_ficha_atend_individual_item_procedimento">

        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_ficha_atend_individual_item_proced"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendIndividualItem"
            column="cd_esus_ficha_atend_individual_item"
            name="esusFichaAtendIndividualItem"
            not-null="true"
        />
        
        
        <many-to-one
            class="br.com.ksisolucoes.vo.esus.ExameEsus"
            column="cd_exame_esus"
            name="exameEsus"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
            column="cd_procedimento"
            name="procedimento"
        />
        
        <property
            name="solicitadoAvaliado"
            column="solicitado_avaliado"
            type="java.lang.Long"
            not-null="false"
            length="1"
        >
        </property>
        
        <property
            column="dt_cadastro"
            name="dataCadastro"
            type="timestamp"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuario"
            not-null="true"
        />
    </class>
</hibernate-mapping>