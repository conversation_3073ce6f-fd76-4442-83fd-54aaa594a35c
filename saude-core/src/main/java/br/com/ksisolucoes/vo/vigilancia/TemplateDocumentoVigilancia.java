package br.com.ksisolucoes.vo.vigilancia;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import br.com.ksisolucoes.vo.vigilancia.base.BaseTemplateDocumentoVigilancia;

import java.io.Serializable;

public class TemplateDocumentoVigilancia extends BaseTemplateDocumentoVigilancia implements CodigoManager, PesquisaObjectInterface, Comparable<TemplateDocumentoVigilancia>, Serializable {

/*[CONSTRUCTOR MARKER BEGIN]*/
	public TemplateDocumentoVigilancia () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public TemplateDocumentoVigilancia (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public TemplateDocumentoVigilancia (
		java.lang.Long codigo,
		java.lang.String nome,
		java.lang.String texto) {

		super (
			codigo,
			nome,
			texto);
	}

    /*[CONSTRUCTOR MARKER END]*/

	public static enum TipoDocumento implements IEnum {

		CERTIDAO_DEFESA_PREVIA(1L, Bundle.getStringApplication("rotulo_certidao_defesa_previa")),
		CERTIDAO_DOCUMENTO_PROCESSUAL(2L, Bundle.getStringApplication("rotulo_certidao_documento_processual")),
		CERTIDAO_RECURSO(3L, Bundle.getStringApplication("rotulo_certidao_recurso")),
        CERTIDAO_PROCESSO_ADMINISTRATIVO(4L, Bundle.getStringApplication("rotulo_certidao_processo_administrativo")),
        LANCAR_PARECER_REQUERIMENTO(5L, Bundle.getStringApplication("rotulo_lancar_parecer"));

		private Long value;
		private String descricao;

		private TipoDocumento(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static TipoDocumento valueOf(Long value) {
			for (TipoDocumento tipoDocumento : TipoDocumento.values()) {
				if (tipoDocumento.value().equals(value)) {
					return tipoDocumento;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return this.value;
		}

		@Override
		public String descricao() {
			return this.descricao;
		}
	}

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    @Override
    public String getDescricaoVO() {
        return getNome();
    }

    @Override
    public String getIdentificador() {
        return Coalesce.asString((Long) getCodigoManager());
    }

    @Override
    public int compareTo(TemplateDocumentoVigilancia tipoProcedimento) {
        if (this == tipoProcedimento || this.equals(tipoProcedimento)) {
            return 0;
        }
        return -1;
    }

}