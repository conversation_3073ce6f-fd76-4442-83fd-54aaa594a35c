package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseTipoTesteRapido;

public class TipoTesteRapido extends BaseTipoTesteRapido implements CodigoManager {

    private static final long serialVersionUID = 1L;
    public static final Long TR_UM = 1L;
    public static final Long TR_DOIS = 2L;
    public static final Long TR_TRES = 3L;
    public static final Long TR_QUATRO = 4L;

    public enum TipoTeste implements IEnum {

        HIV(0L, Bundle.getStringApplication("rotulo_hiv")),
        HEPATITE_B(1L, Bundle.getStringApplication("rotulo_hepatite_b")),
        SIFILIS(2L, Bundle.getStringApplication("rotulo_sifilis")),
        HEPATITE_C(3L, Bundle.getStringApplication("rotulo_hepatite_c")),
        GRAVIDEZ(4L, Bundle.getStringApplication("rotulo_gravidez")),
        COVID_19(5L, Bundle.getStringApplication("covid19")),
        TB_LAM(6L, Bundle.getStringApplication("tblam")),
        DENGUE(7L, Bundle.getStringApplication("rotulo_dengue")),
        INFLUENZA(8L, Bundle.getStringApplication("rotulo_influenza")),
        HANSENIASE(9L, Bundle.getStringApplication("rotulo_hanseniase")),
        HIV_SIFILIS(10L, Bundle.getStringApplication("rotulo_hiv/sifilis")),
        AIDS_AVANCADO(11L, Bundle.getStringApplication("rotulo_aids_avancado")),
        COVID_MAIS_INFLUENZA_AB(12L, Bundle.getStringApplication("rotulo_covid_influenza_ab"));

        public static TipoTeste valueOf(Long value) {
            for (TipoTeste tipoTeste : TipoTeste.values()) {
                if (tipoTeste.value().equals(value)) {
                    return tipoTeste;
                }
            }
            return null;
        }

        private TipoTeste(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;

        }

        private String descricao;
        private Long value;

        @Override
        public Object value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    private Long utilizaFormIst;

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public TipoTesteRapido () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public TipoTesteRapido (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public TipoTesteRapido (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento,
		java.lang.String descricao,
		java.lang.Long tipoTeste) {

		super (
			codigo,
			procedimento,
			descricao,
			tipoTeste);
	}

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getDescricaoTipoTeste() {
        TipoTeste tipoTeste = TipoTeste.valueOf(getTipoTeste());

        if (tipoTeste != null) {
            return tipoTeste.descricao();
        }

        return null;
    }

    public Long getUtilizaFormIst() {
        return utilizaFormIst;
    }

    public void setUtilizaFormIst(Long utilizaFormIst) {
        this.utilizaFormIst = utilizaFormIst;
    }
}
