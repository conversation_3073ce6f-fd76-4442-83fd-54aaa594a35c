package br.com.ksisolucoes.vo.prontuario.basico.tuberculose;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.tuberculose.base.BaseTuberculoseSintomatico;

import java.io.Serializable;



public class TuberculoseSintomatico extends BaseTuberculoseSintomatico implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public TuberculoseSintomatico () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public TuberculoseSintomatico (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public TuberculoseSintomatico (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento,
		br.com.ksisolucoes.vo.cadsus.Profissional profissional,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataRegistro,
		java.lang.Long contactante,
		java.lang.Long situacao,
		java.util.Date dataCadastro) {

		super (
			codigo,
			atendimento,
			profissional,
			usuarioCadsus,
			usuario,
			dataRegistro,
			contactante,
			situacao,
			dataCadastro);
	}

/*[CONSTRUCTOR MARKER END]*/

    public enum Situacao implements IEnum {
        PENDENTE(0L, Bundle.getStringApplication("rotulo_pendente")),
        CONFIRMADO(1L, Bundle.getStringApplication("rotulo_confirmado")),
        NEGADO(2L, Bundle.getStringApplication("rotulo_negado")),
        CURA(TuberculoseAcompanhamento.MotivoEncerramento.CURA.value(), Bundle.getStringApplication("rotulo_cura")),
        COMPLETOU_TRATAMENTO(TuberculoseAcompanhamento.MotivoEncerramento.COMPLETOU_TRATAMENTO.value(), Bundle.getStringApplication("rotulo_completou_tratamento")),
        EXITO_TRATAMENTO(TuberculoseAcompanhamento.MotivoEncerramento.EXITO_TRATAMENTO.value(), Bundle.getStringApplication("rotulo_exito_tratamento")),
        ABANDOO(TuberculoseAcompanhamento.MotivoEncerramento.ABANDOO.value(), Bundle.getStringApplication("rotulo_abandono")),
        TRANSFERENCIA(TuberculoseAcompanhamento.MotivoEncerramento.TRANSFERENCIA.value(), Bundle.getStringApplication("rotulo_transferencia")),
        OBITO(TuberculoseAcompanhamento.MotivoEncerramento.OBITO.value(), Bundle.getStringApplication("rotulo_obito")),
        FALENCIA(TuberculoseAcompanhamento.MotivoEncerramento.FALENCIA.value(), Bundle.getStringApplication("rotulo_falencia")),
        MUDANCA_DIAGNOSTICO(TuberculoseAcompanhamento.MotivoEncerramento.MUDANCA_DIAGNOSTICO.value(), Bundle.getStringApplication("rotulo_mudanca_diagnostico")),
        ;

        private Long value;
        private String descricao;

        private Situacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Situacao valueOf(Long value) {
            for (Situacao situacao : Situacao.values()) {
                if (situacao.value().equals(value)) {
                    return situacao;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }

    public enum SituacaoConfirmacao implements IEnum {
        CONFIRMADO(Situacao.CONFIRMADO.value(), Bundle.getStringApplication("rotulo_confirmado")),
        NEGADO(Situacao.NEGADO.value(), Bundle.getStringApplication("rotulo_negado")),
        ;

        private Long value;
        private String descricao;

        private SituacaoConfirmacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static SituacaoConfirmacao valueOf(Long value) {
            for (SituacaoConfirmacao situacao : SituacaoConfirmacao.values()) {
                if (situacao.value().equals(value)) {
                    return situacao;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }

    public String getDescricaoSituacao() {
        Situacao situacao = Situacao.valueOf(getSituacao());
        if (situacao != null) {
            return situacao.descricao();
        }
        return null;
    }

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}