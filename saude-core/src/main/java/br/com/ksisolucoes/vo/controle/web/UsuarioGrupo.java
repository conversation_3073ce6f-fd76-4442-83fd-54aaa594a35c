package br.com.ksisolucoes.vo.controle.web;

import java.io.Serializable;

import br.com.ksisolucoes.vo.controle.web.base.BaseUsuarioGrupo;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class UsuarioGrupo extends BaseUsuarioGrupo implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public UsuarioGrupo () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public UsuarioGrupo (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public UsuarioGrupo (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Grupo grupo,
		br.com.ksisolucoes.vo.controle.Usuario usuario) {

		super (
			codigo,
			grupo,
			usuario);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}