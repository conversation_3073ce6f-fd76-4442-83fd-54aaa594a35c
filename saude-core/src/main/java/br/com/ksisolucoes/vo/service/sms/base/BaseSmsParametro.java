package br.com.ksisolucoes.vo.service.sms.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the  sms_parametro table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table=" sms_parametro"
 */

public abstract class BaseSmsParametro extends BaseRootVO implements Serializable {

	public static String REF = "SmsParametro";
	public static final String PROP_LIMITE_DIARIO_S_M_S = "limiteDiarioSMS";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_LIMITE_ATINGIDO = "dataLimiteAtingido";
	public static final String PROP_FLAG_LIMITE_DIARIO_ATINGIDO = "FlagLimiteDiarioAtingido";


	// constructors
	public BaseSmsParametro () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseSmsParametro (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseSmsParametro (
		java.lang.Long codigo,
		java.lang.Long limiteDiarioSMS) {

		this.setCodigo(codigo);
		this.setLimiteDiarioSMS(limiteDiarioSMS);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long limiteDiarioSMS;
	private java.util.Date dataLimiteAtingido;
	private java.lang.Long flagLimiteDiarioAtingido;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_sms_parametro"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: limite_diario_sms
	 */
	public java.lang.Long getLimiteDiarioSMS () {
		return getPropertyValue(this, limiteDiarioSMS, PROP_LIMITE_DIARIO_S_M_S); 
	}

	/**
	 * Set the value related to the column: limite_diario_sms
	 * @param limiteDiarioSMS the limite_diario_sms value
	 */
	public void setLimiteDiarioSMS (java.lang.Long limiteDiarioSMS) {
//        java.lang.Long limiteDiarioSMSOld = this.limiteDiarioSMS;
		this.limiteDiarioSMS = limiteDiarioSMS;
//        this.getPropertyChangeSupport().firePropertyChange ("limiteDiarioSMS", limiteDiarioSMSOld, limiteDiarioSMS);
	}



	/**
	 * Return the value associated with the column: dt_limite_atingido
	 */
	public java.util.Date getDataLimiteAtingido () {
		return getPropertyValue(this, dataLimiteAtingido, PROP_DATA_LIMITE_ATINGIDO); 
	}

	/**
	 * Set the value related to the column: dt_limite_atingido
	 * @param dataLimiteAtingido the dt_limite_atingido value
	 */
	public void setDataLimiteAtingido (java.util.Date dataLimiteAtingido) {
//        java.util.Date dataLimiteAtingidoOld = this.dataLimiteAtingido;
		this.dataLimiteAtingido = dataLimiteAtingido;
//        this.getPropertyChangeSupport().firePropertyChange ("dataLimiteAtingido", dataLimiteAtingidoOld, dataLimiteAtingido);
	}



	/**
	 * Return the value associated with the column: flag_limite_diario_atingido
	 */
	public java.lang.Long getFlagLimiteDiarioAtingido () {
		return getPropertyValue(this, flagLimiteDiarioAtingido, PROP_FLAG_LIMITE_DIARIO_ATINGIDO);
	}

	/**
	 * Set the value related to the column: flag_limite_diario_atingido
	 * @param flagLimiteDiarioAtingido the flag_limite_diario_atingido value
	 */
	public void setFlagLimiteDiarioAtingido (java.lang.Long flagLimiteDiarioAtingido) {
//        java.lang.Long flagLimiteDiarioAtingidoOld = this.flagLimiteDiarioAtingido;
		this.flagLimiteDiarioAtingido = flagLimiteDiarioAtingido;
//        this.getPropertyChangeSupport().firePropertyChange ("flagLimiteDiarioAtingido", flagLimiteDiarioAtingidoOld, flagLimiteDiarioAtingido);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.service.sms.SmsParametro)) return false;
		else {
			br.com.ksisolucoes.vo.service.sms.SmsParametro smsParametro = (br.com.ksisolucoes.vo.service.sms.SmsParametro) obj;
			if (null == this.getCodigo() || null == smsParametro.getCodigo()) return false;
			else return (this.getCodigo().equals(smsParametro.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}