package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseAtendimentoAnamnese;



public class AtendimentoAnamnese extends BaseAtendimentoAnamnese implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public AtendimentoAnamnese () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public AtendimentoAnamnese (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public AtendimentoAnamnese (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {

		super (
			codigo,
			atendimento);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}