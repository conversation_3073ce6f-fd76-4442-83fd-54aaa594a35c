<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.cadsus.cds"  >
    <class name="EsusFichaAtendIndividualItem" table="esus_ficha_atend_individual_item">

        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_esus_ficha_atend_individual_item"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendIndividual"
            column="cd_esus_ficha_atend_individual"
            name="esusFichaAtendIndividual"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"
            column="cd_usu_cadsus"
            name="usuarioCadsus"
            not-null="false"
        />
        
        <property
            name="numeroCartao"
            column="cd_numero_cartao"
            type="java.lang.Long"
            not-null="false"
        >
        </property>
        
        <property
            name="turno"
            column="turno"
            type="java.lang.Long"
            not-null="false"
        >
        </property>
        
        <property
            name="numeroProntuario"
            column="nr_prontuario"
            type="java.lang.String"
            length="30"
        >
        </property>
        
        <property
            name="dataNascimento"
            column="dt_nascimento"
            not-null="true"
            type="date"
        />

        <property
            name="sexo"
            column="sexo"
            not-null="true"
            type="java.lang.Long"
            length="1"
        />
        
        <property
            name="localAtendimento"
            column="local_atendimento"
            type="java.lang.Long"
            not-null="true"
        >
        </property>
       
        <property
            name="tipoAtendimento"
            column="tipo_atendimento"
            type="java.lang.Long"
            not-null="true"
        >
        </property>
        
        <property
            name="peso"
            column="peso"
            not-null="false"
            type="java.lang.Double"
        />
        
        <property
            name="altura"
            column="altura"
            not-null="false"
            type="java.lang.Double"
        />

        <property
                name="perimetroCefalico"
                column="perimetro_cefalico"
                not-null="false"
                type="java.lang.Double"
        />
        
        <property
            name="vacinaEmDia"
            column="vacina_em_dia"
            not-null="false"
            type="java.lang.Long"
        />
        
        <property
            name="aleitamentoMaterno"
            column="aleitamento_materno"
            not-null="false"
            type="java.lang.Long"
            length="1"
        />
        
        <property
            name="dumGestante"
            column="dum_gestante"
            not-null="false"
            type="date"
        />
        
        <property
            name="gravidezPlanejada"
            column="gravidez_planejada"
            not-null="false"
            type="java.lang.Long"
            length="1"
        />
        
        <property
            name="idadeGestacional"
            column="idade_gestacional"
            not-null="false"
            type="java.lang.Long"
            length="2"
        />
        
        <property
            name="numeroGestasPrevias"
            column="nr_gestas_previas"
            not-null="false"
            type="java.lang.Long"
            length="2"
        />
        
        <property
            name="numeroPartos"
            column="nr_partos"
            not-null="false"
            type="java.lang.Long"
            length="2"
        />
        
        <property
            name="atencaoDomiciliar"
            column="atencao_domiciliar"
            not-null="false"
            type="java.lang.Long"
        />
        
        <property
            name="problemaCondicaoAvaliada"
            column="problema_condicao_avaliada"
            type="java.lang.Long"
            not-null="true"
            length="10"
        >
        </property>
        
        <property
            name="outroCiap1"
            column="outro_ciap_1"
            type="java.lang.String"
            not-null="false"
            length="10"
        >
        </property>
        
        <property
            name="outroCiap2"
            column="outro_ciap_2"
            type="java.lang.String"
            not-null="false"
            length="10"
        >
        </property>
        
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.Cid"
            column="cd_cid"
            name="cid"
            not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.basico.Cid"
                column="cd_cid2"
                name="cid2"
                not-null="false"
        />
        
        <property
            name="racionalidadeSaude"
            column="racionalidade_saude"
            not-null="false"
            type="java.lang.Long"
        />
        
        <property
            name="ficouEmObservacao"
            column="ficou_em_observacao"
            not-null="false"
            type="java.lang.Long"
            length="1"
        />
        
        <property
            name="nasfs"
            column="nasfs"
            not-null="false"
            type="java.lang.Long"
        />
        
        <property
            name="condutas"
            column="condutas"
            not-null="false"
            type="java.lang.Long"
        />

        <property
            column="dt_cadastro"
            name="dataCadastro"
            type="timestamp"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuario"
            not-null="true"
        />

        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.Ciap"
             name="ciap"
             not-null="false">
            <column name="cd_ciap" />
        </many-to-one>

        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.Ciap"
             name="ciap2"
             not-null="false">
            <column name="cd_ciap_2" />
        </many-to-one>

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
            column="nr_atendimento"
            name="atendimento"
            not-null="false"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.hospital.ItemContaEsus"
            column="cd_it_cont_esus"
            name="itemContaEsus"
            not-null="false"
        />

    </class>
</hibernate-mapping>