package br.com.ksisolucoes.vo.vigilancia.requerimentos.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the controle_num_receita_b table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="controle_num_receita_b"
 */

public abstract class BaseControleNumeracaoReceitaB extends BaseRootVO implements Serializable {

	public static String REF = "ControleNumeracaoReceitaB";
	public static final String PROP_NUMERACAO_ATUAL = "numeracaoAtual";
	public static final String PROP_SUBTIPO = "subtipo";
	public static final String PROP_CODIGO = "codigo";


	// constructors
	public BaseControleNumeracaoReceitaB () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseControleNumeracaoReceitaB (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseControleNumeracaoReceitaB (
		java.lang.Long codigo,
		java.lang.Long subtipo,
		java.lang.Long numeracaoAtual) {

		this.setCodigo(codigo);
		this.setSubtipo(subtipo);
		this.setNumeracaoAtual(numeracaoAtual);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long subtipo;
	private java.lang.Long numeracaoAtual;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_controle_num_receita_b"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: subtipo
	 */
	public java.lang.Long getSubtipo () {
		return getPropertyValue(this, subtipo, PROP_SUBTIPO); 
	}

	/**
	 * Set the value related to the column: subtipo
	 * @param subtipo the subtipo value
	 */
	public void setSubtipo (java.lang.Long subtipo) {
//        java.lang.Long subtipoOld = this.subtipo;
		this.subtipo = subtipo;
//        this.getPropertyChangeSupport().firePropertyChange ("subtipo", subtipoOld, subtipo);
	}



	/**
	 * Return the value associated with the column: num_atual
	 */
	public java.lang.Long getNumeracaoAtual () {
		return getPropertyValue(this, numeracaoAtual, PROP_NUMERACAO_ATUAL); 
	}

	/**
	 * Set the value related to the column: num_atual
	 * @param numeracaoAtual the num_atual value
	 */
	public void setNumeracaoAtual (java.lang.Long numeracaoAtual) {
//        java.lang.Long numeracaoAtualOld = this.numeracaoAtual;
		this.numeracaoAtual = numeracaoAtual;
//        this.getPropertyChangeSupport().firePropertyChange ("numeracaoAtual", numeracaoAtualOld, numeracaoAtual);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.requerimentos.ControleNumeracaoReceitaB)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.requerimentos.ControleNumeracaoReceitaB controleNumeracaoReceitaB = (br.com.ksisolucoes.vo.vigilancia.requerimentos.ControleNumeracaoReceitaB) obj;
			if (null == this.getCodigo() || null == controleNumeracaoReceitaB.getCodigo()) return false;
			else return (this.getCodigo().equals(controleNumeracaoReceitaB.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}