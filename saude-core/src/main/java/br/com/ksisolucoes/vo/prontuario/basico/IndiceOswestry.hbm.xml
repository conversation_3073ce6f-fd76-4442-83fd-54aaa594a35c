<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico">
    <class name="IndiceOswestry" table="indice_oswestry">

        <id
                column="cd_indice_oswestry"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="assigned"/>
        </id>

        <version column="version" name="version" type="long"/>

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
                name="atendimento"
        >
            <column name="nr_atendimento"/>
        </many-to-one>

        <property
                name="intencidadeDor"
                type="java.lang.Long"
                column="intencidade_dor"
                not-null="false"
        />

        <property
                name="cuidadosPessoais"
                type="java.lang.Long"
                column="cuidados_pessoais"
                not-null="false"
        />

        <property
                name="pesos"
                type="java.lang.Long"
                column="pesos"
                not-null="false"
        />

        <property
                name="andar"
                type="java.lang.Long"
                column="andar"
                not-null="false"
        />

        <property
                name="sentar"
                type="java.lang.Long"
                column="sentar"
                not-null="false"
        />

        <property
                name="dePe"
                type="java.lang.Long"
                column="de_pe"
                not-null="false"
        />

        <property
                name="sono"
                type="java.lang.Long"
                column="sono"
                not-null="false"
        />

        <property
                name="vidaSexual"
                type="java.lang.Long"
                column="vida_sexual"
                not-null="false"
        />

        <property
                name="vidaSocial"
                type="java.lang.Long"
                column="vida_social"
                not-null="false"
        />

        <property
                name="viagens"
                type="java.lang.Long"
                column="viagens"
                not-null="false"
        />

        <property
                name="dataCadastro"
                type="timestamp"
                column="dt_cadastro"
                not-null="true"
        />

        <property
                name="dataAlteracao"
                type="timestamp"
                column="dt_alteracao"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                name="usuario"
        >
            <column name="cd_usuario" not-null="true"/>
        </many-to-one>
        
        <property
                name="resultado"
                type="java.lang.String"
                column="resultado"
                not-null="false"
        />

    </class>
</hibernate-mapping>
