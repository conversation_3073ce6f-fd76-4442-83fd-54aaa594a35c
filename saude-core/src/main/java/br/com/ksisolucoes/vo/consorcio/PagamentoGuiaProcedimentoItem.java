package br.com.ksisolucoes.vo.consorcio;

import java.io.Serializable;
import java.math.MathContext;

import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.vo.consorcio.base.BasePagamentoGuiaProcedimentoItem;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class PagamentoGuiaProcedimentoItem extends BasePagamentoGuiaProcedimentoItem implements CodigoManager {
	private static final long serialVersionUID = 1L;

        public static final String PROP_VALOR_TOTAL_BRUTO = "valorTotalBruto";
        public static final String PROP_VALOR_TOTAL_IMPOSTOS = "valorTotalImpostos";
        
/*[CONSTRUCTOR MARKER BEGIN]*/
	public PagamentoGuiaProcedimentoItem () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public PagamentoGuiaProcedimentoItem (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public PagamentoGuiaProcedimentoItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.consorcio.PagamentoGuiaProcedimento pagamentoGuiaProcedimento,
		br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento consorcioGuiaProcedimento,
		br.com.ksisolucoes.vo.consorcio.MovimentacaoFinanceira movimentacaoFinanceira,
		java.lang.Double valorPagamento) {

		super (
			codigo,
			pagamentoGuiaProcedimento,
			consorcioGuiaProcedimento,
			movimentacaoFinanceira,
			valorPagamento);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public Double getValorTotalBruto(){
        return new Dinheiro(Coalesce.asDouble(getValorPagamento()), MathContext.DECIMAL128)
                    .somar(Coalesce.asDouble(getValorImpostoRenda()))
                    .somar(Coalesce.asDouble(getValorImpostoInss()))
                    .somar(Coalesce.asDouble(getValorImpostoIss()))
                    .round().doubleValue();
    }
    
    public Double getValorTotalImpostos(){
        return new Dinheiro(Coalesce.asDouble(getValorImpostoRenda()), MathContext.DECIMAL128)
                    .somar(Coalesce.asDouble(getValorImpostoInss()))
                    .somar(Coalesce.asDouble(getValorImpostoIss()))
                    .round().doubleValue();
    }
}