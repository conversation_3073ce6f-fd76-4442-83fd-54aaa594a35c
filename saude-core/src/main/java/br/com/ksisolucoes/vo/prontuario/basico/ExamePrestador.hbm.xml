<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="ExamePrestador" table="exame_prestador" >
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_exame_prestador"
        >
            <generator class="assigned"/>

        </id> 
        <version column="version" name="version" type="long" />
         
        <many-to-one
            class="br.com.ksisolucoes.vo.basico.Empresa"
            name="prestador"
            column="empresa"
            not-null="true"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.TipoExame"
            name="tipoExame"
            column="cd_tp_exame"
            not-null="false"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.TipoExame"
            name="tipoExameSecundario"
            column="cd_tp_exame_sec"
            not-null="false"
        />
          
        <property
            name="tetoFinanceiro"
            type="java.lang.Double"
            column="teto_financeiro"
            not-null="true"
        />
        
        <property
            name="tipoCota"
            type="java.lang.Long"
            column="tp_cota"
            not-null="true"
        />         
        
        <property
            name="numeroPacienteAtendidos"
            type="java.lang.Long"
            column="num_paciente_atendidos"
            not-null="false"
        />         
        
        <property
            name="horaInicial"
            column="hora_inicial"
            type="timestamp"
            not-null="false"
        />
        
        <property
            name="recursoProprio"
            column="recurso_proprio"
            type="java.lang.Double"
            not-null="false"
        />

        <property
            name="agendaEspecialista"
            column="flag_agenda_especialista"
            type="java.lang.Integer"
            not-null="true"
        />
        
    </class>
</hibernate-mapping>
