<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.coordenadas">
    <class name="EnderecoCoordenadas" table="endereco_coordenadas">

        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_endereco_coordenadas"
        >
            <generator class="sequence">
                <param name="sequence">SEQ_AUDIT_ID_endereco_coordenadas</param>
            </generator>
        </id>

        <version column="version" name="version" type="long"/>

        <many-to-one
                name="enderecoUsuarioCadsus"
                class="br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus"
                column="cd_endereco_usuario_cadsus"
        />

        <many-to-one
                name="cidade"
                class="br.com.ksisolucoes.vo.basico.Cidade"
                column="cod_cid"
        />

        <many-to-one
                name="denguePontoEstrategico"
                class="br.com.ksisolucoes.vo.vigilancia.dengue.DenguePontoEstrategico"
                column="cd_dengue_ponto_estrategico"
        />

        <many-to-one
                name="dengueArmadilha"
                class="br.com.ksisolucoes.vo.vigilancia.dengue.DengueArmadilha"
                column="cd_dengue_armadilha"
        />

        <property
                name="latitude"
                type="java.lang.Double"
                column="latitude"
                not-null="true"
        />

        <property
                name="longitude"
                type="java.lang.Double"
                column="longitude"
                not-null="true"
        />

        <property
            name="dataRegistro"
            type="timestamp"
            column="dt_registro"
            not-null="true"
        />
    </class>
</hibernate-mapping>
