package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the requisicao_padrao_exame table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="requisicao_padrao_exame"
 */

public abstract class BaseRequisicaoPadraoExame extends BaseRootVO implements Serializable {

	public static String REF = "RequisicaoPadraoExame";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_COMPLEMENTO = "complemento";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_USUARIO = "dataUsuario";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_REQUISICAO_PADRAO = "requisicaoPadrao";
	public static final String PROP_EXAME_PROCEDIMENTO = "exameProcedimento";
	public static final String PROP_QUANTIDADE = "quantidade";


	// constructors
	public BaseRequisicaoPadraoExame () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseRequisicaoPadraoExame (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseRequisicaoPadraoExame (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.RequisicaoPadrao requisicaoPadrao,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimento,
		java.util.Date dataCadastro,
		java.util.Date dataUsuario,
		java.lang.Long quantidade) {

		this.setCodigo(codigo);
		this.setRequisicaoPadrao(requisicaoPadrao);
		this.setUsuario(usuario);
		this.setExameProcedimento(exameProcedimento);
		this.setDataCadastro(dataCadastro);
		this.setDataUsuario(dataUsuario);
		this.setQuantidade(quantidade);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataCadastro;
	private java.util.Date dataUsuario;
	private java.lang.String complemento;
	private java.lang.Long quantidade;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.RequisicaoPadrao requisicaoPadrao;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_req_padrao_item"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_usuario
	 */
	public java.util.Date getDataUsuario () {
		return getPropertyValue(this, dataUsuario, PROP_DATA_USUARIO); 
	}

	/**
	 * Set the value related to the column: dt_usuario
	 * @param dataUsuario the dt_usuario value
	 */
	public void setDataUsuario (java.util.Date dataUsuario) {
//        java.util.Date dataUsuarioOld = this.dataUsuario;
		this.dataUsuario = dataUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsuario", dataUsuarioOld, dataUsuario);
	}



	/**
	 * Return the value associated with the column: complemento
	 */
	public java.lang.String getComplemento () {
		return getPropertyValue(this, complemento, PROP_COMPLEMENTO); 
	}

	/**
	 * Set the value related to the column: complemento
	 * @param complemento the complemento value
	 */
	public void setComplemento (java.lang.String complemento) {
//        java.lang.String complementoOld = this.complemento;
		this.complemento = complemento;
//        this.getPropertyChangeSupport().firePropertyChange ("complemento", complementoOld, complemento);
	}



	/**
	 * Return the value associated with the column: qtdade
	 */
	public java.lang.Long getQuantidade () {
		return getPropertyValue(this, quantidade, PROP_QUANTIDADE); 
	}

	/**
	 * Set the value related to the column: qtdade
	 * @param quantidade the qtdade value
	 */
	public void setQuantidade (java.lang.Long quantidade) {
//        java.lang.Long quantidadeOld = this.quantidade;
		this.quantidade = quantidade;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidade", quantidadeOld, quantidade);
	}



	/**
	 * Return the value associated with the column: cd_requisicao
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.RequisicaoPadrao getRequisicaoPadrao () {
		return getPropertyValue(this, requisicaoPadrao, PROP_REQUISICAO_PADRAO); 
	}

	/**
	 * Set the value related to the column: cd_requisicao
	 * @param requisicaoPadrao the cd_requisicao value
	 */
	public void setRequisicaoPadrao (br.com.ksisolucoes.vo.prontuario.basico.RequisicaoPadrao requisicaoPadrao) {
//        br.com.ksisolucoes.vo.prontuario.basico.RequisicaoPadrao requisicaoPadraoOld = this.requisicaoPadrao;
		this.requisicaoPadrao = requisicaoPadrao;
//        this.getPropertyChangeSupport().firePropertyChange ("requisicaoPadrao", requisicaoPadraoOld, requisicaoPadrao);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_exame_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento getExameProcedimento () {
		return getPropertyValue(this, exameProcedimento, PROP_EXAME_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_exame_procedimento
	 * @param exameProcedimento the cd_exame_procedimento value
	 */
	public void setExameProcedimento (br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimentoOld = this.exameProcedimento;
		this.exameProcedimento = exameProcedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("exameProcedimento", exameProcedimentoOld, exameProcedimento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.RequisicaoPadraoExame)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.RequisicaoPadraoExame requisicaoPadraoExame = (br.com.ksisolucoes.vo.prontuario.basico.RequisicaoPadraoExame) obj;
			if (null == this.getCodigo() || null == requisicaoPadraoExame.getCodigo()) return false;
			else return (this.getCodigo().equals(requisicaoPadraoExame.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}