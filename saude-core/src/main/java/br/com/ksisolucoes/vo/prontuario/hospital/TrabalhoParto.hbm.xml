<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.hospital"  >
    <class name="TrabalhoParto" table="trabalho_parto" >
        <id
            column="cd_trabalho_parto"
            name="codigo"
            type="java.lang.Long"
            >
            <generator class="assigned" />
        </id> <version column="version" name="version" type="long" />    
            
        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
                     name="atendimento" not-null="true">
            <column name="nr_atendimento" />
        </many-to-one>
        
        <many-to-one class="br.com.ksisolucoes.vo.cadsus.Profissional"
                     name="profissional" not-null="true">
            <column name="cd_profissional" />
        </many-to-one>
        
        <property
            column="hora"
            name="hora"
            not-null="true"
            type="java.util.Date"
        />
        
        <property
            column="pressao_arterial"
            name="pressaoArterial"
            not-null="false"
            type="java.lang.String"
            length="10"
        />
        
        <property
            column="temperatura_axiliar"
            name="temperaturaAxiliar"
            not-null="false"
            type="java.lang.String"
            length="10"
        />
        
        <property
            column="torus_sistole"
            name="torusSistole"
            not-null="false"
            type="java.lang.String"
            length="10"
        />
        
        <property
            column="contracoes_em"
            name="contracoesEm"
            not-null="false"
            type="java.lang.String"
            length="10"
        />
        
        <property
            column="dilatacao_colo"
            name="dilatacaoColo"
            not-null="false"
            type="java.lang.Long"
        />
        
        <property
            column="altura_apresentacao"
            name="alturaApresentacao"
            not-null="false"
            type="java.lang.Long"
        />
        
        <property
            column="bolsas_dagua"
            name="bolsasDagua"
            not-null="false"
            type="java.lang.Long"
        />
        
        <property
            column="batimento_cardiaco_feto"
            name="batimentoCardiacoFeto"
            not-null="false"
            type="java.lang.Long"
        />
        
    </class>
</hibernate-mapping>
