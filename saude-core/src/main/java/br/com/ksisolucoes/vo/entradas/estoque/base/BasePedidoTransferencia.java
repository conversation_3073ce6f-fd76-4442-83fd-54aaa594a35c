package br.com.ksisolucoes.vo.entradas.estoque.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the pedido_transferencia table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="pedido_transferencia"
 */

public abstract class BasePedidoTransferencia extends BaseRootVO implements Serializable {

	public static String REF = "PedidoTransferencia";
	public static final String PROP_MOTORISTA = "motorista";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_USUARIO_INTEGRACAO = "usuarioIntegracao";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_VEICULO = "veiculo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_DATA_RECEBIMENTO = "dataRecebimento";
	public static final String PROP_USUARIO_SEPARACAO = "usuarioSeparacao";
	public static final String PROP_JUSTIFICATIVA_REPROVACAO = "justificativaReprovacao";
	public static final String PROP_EMPRESA_ORIGEM = "empresaOrigem";
	public static final String PROP_STATUS_IMPRESSAO_EMBARQUE = "statusImpressaoEmbarque";
	public static final String PROP_DATA_PEDIDO = "dataPedido";
	public static final String PROP_MOTIVO_CANCELAMENTO = "motivoCancelamento";
	public static final String PROP_USUARIO_RECEBIMENTO = "usuarioRecebimento";
	public static final String PROP_RESPONSAVEL_ENTREGA = "responsavelEntrega";
	public static final String PROP_FLAG_VACINA = "flagVacina";
	public static final String PROP_STATUS = "status";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_STATUS_APROVACAO = "statusAprovacao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_CODIGO_ORDEM_ENTREGA = "codigoOrdemEntrega";
	public static final String PROP_DEPOSITO = "deposito";
	public static final String PROP_PEDIDO_TRANSFERENCIA_PAI = "pedidoTransferenciaPai";
	public static final String PROP_STATUS_SEPARACAO = "statusSeparacao";
	public static final String PROP_EMPRESA_DESTINO = "empresaDestino";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";
	public static final String PROP_DATA_EMBARQUE = "dataEmbarque";
	public static final String PROP_STATUS_IMPRESSAO = "statusImpressao";
	public static final String PROP_DATA_ALTERACAO = "dataAlteracao";
	public static final String PROP_TIPO = "tipo";
	public static final String PROP_DATA_SEPARACAO = "dataSeparacao";
	public static final String PROP_ID_PEDIDO_INTEGRACAO_TERCEIRO = "idPedidoIntegracaoTerceiro";


	// constructors
	public BasePedidoTransferencia () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BasePedidoTransferencia (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BasePedidoTransferencia (
			java.lang.Long codigo,
			br.com.ksisolucoes.vo.basico.Empresa empresa,
			br.com.ksisolucoes.vo.basico.Empresa empresaOrigem,
			br.com.ksisolucoes.vo.basico.Empresa empresaDestino,
			br.com.ksisolucoes.vo.controle.Usuario usuario,
			java.util.Date dataPedido,
			java.lang.Long flagVacina,
			java.util.Date dataCadastro,
			java.lang.Long statusSeparacao,
			java.lang.Long tipo) {

		this.setCodigo(codigo);
		this.setEmpresa(empresa);
		this.setEmpresaOrigem(empresaOrigem);
		this.setEmpresaDestino(empresaDestino);
		this.setUsuario(usuario);
		this.setDataPedido(dataPedido);
		this.setFlagVacina(flagVacina);
		this.setDataCadastro(dataCadastro);
		this.setStatusSeparacao(statusSeparacao);
		this.setTipo(tipo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataPedido;
	private java.lang.Long status;
	private java.lang.Long flagVacina;
	private java.lang.Long statusImpressao;
	private java.util.Date dataCadastro;
	private java.util.Date dataEmbarque;
	private java.util.Date dataRecebimento;
	private java.util.Date dataCancelamento;
	private java.lang.Long statusImpressaoEmbarque;
	private java.util.Date dataAlteracao;
	private java.lang.String responsavelEntrega;
	private java.util.Date dataSeparacao;
	private java.lang.Long statusSeparacao;
	private java.lang.Long tipo;
	private java.lang.String motivoCancelamento;
	private java.lang.Long statusAprovacao;
	private java.lang.Long codigoOrdemEntrega;
	private java.lang.Long idPedidoIntegracaoTerceiro;
	private java.lang.String justificativaReprovacao;

	// many to one
	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private br.com.ksisolucoes.vo.basico.Empresa empresaOrigem;
	private br.com.ksisolucoes.vo.basico.Empresa empresaDestino;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioSeparacao;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private java.lang.String usuarioIntegracao;
	private br.com.ksisolucoes.vo.frota.Motorista motorista;
	private br.com.ksisolucoes.vo.frota.Veiculo veiculo;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioRecebimento;
	private br.com.ksisolucoes.vo.entradas.estoque.Deposito deposito;
	private br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferencia pedidoTransferenciaPai;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;



	/**
	 * Return the unique identifier of this class
	 * @hibernate.id
	 *  generator-class="sequence"
	 *  column="pedido"
	 */
	public java.lang.Long getCodigo () {
		return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: data_pedido
	 */
	public java.util.Date getDataPedido () {
		return getPropertyValue(this, dataPedido, PROP_DATA_PEDIDO);
	}

	/**
	 * Set the value related to the column: data_pedido
	 * @param dataPedido the data_pedido value
	 */
	public void setDataPedido (java.util.Date dataPedido) {
//        java.util.Date dataPedidoOld = this.dataPedido;
		this.dataPedido = dataPedido;
//        this.getPropertyChangeSupport().firePropertyChange ("dataPedido", dataPedidoOld, dataPedido);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS);
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: flag_vacina
	 */
	public java.lang.Long getFlagVacina () {
		return getPropertyValue(this, flagVacina, PROP_FLAG_VACINA);
	}

	/**
	 * Set the value related to the column: flag_vacina
	 * @param flagVacina the flag_vacina value
	 */
	public void setFlagVacina (java.lang.Long flagVacina) {
//        java.lang.Long flagVacinaOld = this.flagVacina;
		this.flagVacina = flagVacina;
//        this.getPropertyChangeSupport().firePropertyChange ("flagVacina", flagVacinaOld, flagVacina);
	}



	/**
	 * Return the value associated with the column: status_imp
	 */
	public java.lang.Long getStatusImpressao () {
		return getPropertyValue(this, statusImpressao, PROP_STATUS_IMPRESSAO);
	}

	/**
	 * Set the value related to the column: status_imp
	 * @param statusImpressao the status_imp value
	 */
	public void setStatusImpressao (java.lang.Long statusImpressao) {
//        java.lang.Long statusImpressaoOld = this.statusImpressao;
		this.statusImpressao = statusImpressao;
//        this.getPropertyChangeSupport().firePropertyChange ("statusImpressao", statusImpressaoOld, statusImpressao);
	}



	/**
	 * Return the value associated with the column: data_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO);
	}

	/**
	 * Set the value related to the column: data_cadastro
	 * @param dataCadastro the data_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: data_embarque
	 */
	public java.util.Date getDataEmbarque () {
		return getPropertyValue(this, dataEmbarque, PROP_DATA_EMBARQUE);
	}

	/**
	 * Set the value related to the column: data_embarque
	 * @param dataEmbarque the data_embarque value
	 */
	public void setDataEmbarque (java.util.Date dataEmbarque) {
//        java.util.Date dataEmbarqueOld = this.dataEmbarque;
		this.dataEmbarque = dataEmbarque;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEmbarque", dataEmbarqueOld, dataEmbarque);
	}



	/**
	 * Return the value associated with the column: data_recebto
	 */
	public java.util.Date getDataRecebimento () {
		return getPropertyValue(this, dataRecebimento, PROP_DATA_RECEBIMENTO);
	}

	/**
	 * Set the value related to the column: data_recebto
	 * @param dataRecebimento the data_recebto value
	 */
	public void setDataRecebimento (java.util.Date dataRecebimento) {
//        java.util.Date dataRecebimentoOld = this.dataRecebimento;
		this.dataRecebimento = dataRecebimento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataRecebimento", dataRecebimentoOld, dataRecebimento);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO);
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: status_imp_emb
	 */
	public java.lang.Long getStatusImpressaoEmbarque () {
		return getPropertyValue(this, statusImpressaoEmbarque, PROP_STATUS_IMPRESSAO_EMBARQUE);
	}

	/**
	 * Set the value related to the column: status_imp_emb
	 * @param statusImpressaoEmbarque the status_imp_emb value
	 */
	public void setStatusImpressaoEmbarque (java.lang.Long statusImpressaoEmbarque) {
//        java.lang.Long statusImpressaoEmbarqueOld = this.statusImpressaoEmbarque;
		this.statusImpressaoEmbarque = statusImpressaoEmbarque;
//        this.getPropertyChangeSupport().firePropertyChange ("statusImpressaoEmbarque", statusImpressaoEmbarqueOld, statusImpressaoEmbarque);
	}



	/**
	 * Return the value associated with the column: dt_alteracao
	 */
	public java.util.Date getDataAlteracao () {
		return getPropertyValue(this, dataAlteracao, PROP_DATA_ALTERACAO);
	}

	/**
	 * Set the value related to the column: dt_alteracao
	 * @param dataAlteracao the dt_alteracao value
	 */
	public void setDataAlteracao (java.util.Date dataAlteracao) {
//        java.util.Date dataAlteracaoOld = this.dataAlteracao;
		this.dataAlteracao = dataAlteracao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAlteracao", dataAlteracaoOld, dataAlteracao);
	}



	/**
	 * Return the value associated with the column: responsavel_entrega
	 */
	public java.lang.String getResponsavelEntrega () {
		return getPropertyValue(this, responsavelEntrega, PROP_RESPONSAVEL_ENTREGA);
	}

	/**
	 * Set the value related to the column: responsavel_entrega
	 * @param responsavelEntrega the responsavel_entrega value
	 */
	public void setResponsavelEntrega (java.lang.String responsavelEntrega) {
//        java.lang.String responsavelEntregaOld = this.responsavelEntrega;
		this.responsavelEntrega = responsavelEntrega;
//        this.getPropertyChangeSupport().firePropertyChange ("responsavelEntrega", responsavelEntregaOld, responsavelEntrega);
	}



	/**
	 * Return the value associated with the column: dt_separacao
	 */
	public java.util.Date getDataSeparacao () {
		return getPropertyValue(this, dataSeparacao, PROP_DATA_SEPARACAO);
	}

	/**
	 * Set the value related to the column: dt_separacao
	 * @param dataSeparacao the dt_separacao value
	 */
	public void setDataSeparacao (java.util.Date dataSeparacao) {
//        java.util.Date dataSeparacaoOld = this.dataSeparacao;
		this.dataSeparacao = dataSeparacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataSeparacao", dataSeparacaoOld, dataSeparacao);
	}



	/**
	 * Return the value associated with the column: status_separacao
	 */
	public java.lang.Long getStatusSeparacao () {
		return getPropertyValue(this, statusSeparacao, PROP_STATUS_SEPARACAO);
	}

	/**
	 * Set the value related to the column: status_separacao
	 * @param statusSeparacao the status_separacao value
	 */
	public void setStatusSeparacao (java.lang.Long statusSeparacao) {
//        java.lang.Long statusSeparacaoOld = this.statusSeparacao;
		this.statusSeparacao = statusSeparacao;
//        this.getPropertyChangeSupport().firePropertyChange ("statusSeparacao", statusSeparacaoOld, statusSeparacao);
	}



	/**
	 * Return the value associated with the column: tipo
	 */
	public java.lang.Long getTipo () {
		return getPropertyValue(this, tipo, PROP_TIPO);
	}

	/**
	 * Set the value related to the column: tipo
	 * @param tipo the tipo value
	 */
	public void setTipo (java.lang.Long tipo) {
//        java.lang.Long tipoOld = this.tipo;
		this.tipo = tipo;
//        this.getPropertyChangeSupport().firePropertyChange ("tipo", tipoOld, tipo);
	}



	/**
	 * Return the value associated with the column: motivo_cancelamento
	 */
	public java.lang.String getMotivoCancelamento () {
		return getPropertyValue(this, motivoCancelamento, PROP_MOTIVO_CANCELAMENTO);
	}

	/**
	 * Set the value related to the column: motivo_cancelamento
	 * @param motivoCancelamento the motivo_cancelamento value
	 */
	public void setMotivoCancelamento (java.lang.String motivoCancelamento) {
//        java.lang.String motivoCancelamentoOld = this.motivoCancelamento;
		this.motivoCancelamento = motivoCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoCancelamento", motivoCancelamentoOld, motivoCancelamento);
	}



	/**
	 * Return the value associated with the column: status_aprovacao
	 */
	public java.lang.Long getStatusAprovacao () {
		return getPropertyValue(this, statusAprovacao, PROP_STATUS_APROVACAO);
	}

	/**
	 * Set the value related to the column: status_aprovacao
	 * @param statusAprovacao the status_aprovacao value
	 */
	public void setStatusAprovacao (java.lang.Long statusAprovacao) {
//        java.lang.Long statusAprovacaoOld = this.statusAprovacao;
		this.statusAprovacao = statusAprovacao;
//        this.getPropertyChangeSupport().firePropertyChange ("statusAprovacao", statusAprovacaoOld, statusAprovacao);
	}



	/**
	 * Return the value associated with the column: cd_ordem_entrega
	 */
	public java.lang.Long getCodigoOrdemEntrega () {
		return getPropertyValue(this, codigoOrdemEntrega, PROP_CODIGO_ORDEM_ENTREGA);
	}

	/**
	 * Set the value related to the column: cd_ordem_entrega
	 * @param codigoOrdemEntrega the cd_ordem_entrega value
	 */
	public void setCodigoOrdemEntrega (java.lang.Long codigoOrdemEntrega) {
//        java.lang.Long codigoOrdemEntregaOld = this.codigoOrdemEntrega;
		this.codigoOrdemEntrega = codigoOrdemEntrega;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoOrdemEntrega", codigoOrdemEntregaOld, codigoOrdemEntrega);
	}

	/**
	 * Return the value associated with the column: id_pedido_integracao_terceiro
	 */
	public java.lang.Long getIdPedidoIntegracaoTerceiro () {
		return getPropertyValue(this, idPedidoIntegracaoTerceiro, PROP_ID_PEDIDO_INTEGRACAO_TERCEIRO);
	}

	/**
	 * Set the value related to the column: id_pedido_integracao_terceiro
	 * @param idPedidoIntegracaoTerceiro the id_pedido_integracao_terceiro value
	 */
	public void setIdPedidoIntegracaoTerceiro (java.lang.Long idPedidoIntegracaoTerceiro) {
//        java.lang.Long idPedidoIntegracaoTerceiroOld = this.idPedidoIntegracaoTerceiro;
		this.idPedidoIntegracaoTerceiro = idPedidoIntegracaoTerceiro;
//        this.getPropertyChangeSupport().firePropertyChange ("idPedidoIntegracaoTerceiro", idPedidoIntegracaoTerceiroOld, idPedidoIntegracaoTerceiro);
	}

	/**
	 * Return the value associated with the column: justificativa_reprovacao
	 */
	public java.lang.String getJustificativaReprovacao () {
		return getPropertyValue(this, justificativaReprovacao, PROP_JUSTIFICATIVA_REPROVACAO);
	}

	/**
	 * Set the value related to the column: justificativa_reprovacao
	 * @param justificativaReprovacao the justificativa_reprovacao value
	 */
	public void setJustificativaReprovacao (java.lang.String justificativaReprovacao) {
//        java.lang.String justificativaReprovacaoOld = this.justificativaReprovacao;
		this.justificativaReprovacao = justificativaReprovacao;
//        this.getPropertyChangeSupport().firePropertyChange ("justificativaReprovacao", justificativaReprovacaoOld, justificativaReprovacao);
	}

	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA);
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: empresa_origem
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresaOrigem () {
		return getPropertyValue(this, empresaOrigem, PROP_EMPRESA_ORIGEM);
	}

	/**
	 * Set the value related to the column: empresa_origem
	 * @param empresaOrigem the empresa_origem value
	 */
	public void setEmpresaOrigem (br.com.ksisolucoes.vo.basico.Empresa empresaOrigem) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOrigemOld = this.empresaOrigem;
		this.empresaOrigem = empresaOrigem;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaOrigem", empresaOrigemOld, empresaOrigem);
	}



	/**
	 * Return the value associated with the column: empresa_destino
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresaDestino () {
		return getPropertyValue(this, empresaDestino, PROP_EMPRESA_DESTINO);
	}

	/**
	 * Set the value related to the column: empresa_destino
	 * @param empresaDestino the empresa_destino value
	 */
	public void setEmpresaDestino (br.com.ksisolucoes.vo.basico.Empresa empresaDestino) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaDestinoOld = this.empresaDestino;
		this.empresaDestino = empresaDestino;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaDestino", empresaDestinoOld, empresaDestino);
	}



	/**
	 * Return the value associated with the column: cd_usuario_separacao
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioSeparacao () {
		return getPropertyValue(this, usuarioSeparacao, PROP_USUARIO_SEPARACAO);
	}

	/**
	 * Set the value related to the column: cd_usuario_separacao
	 * @param usuarioSeparacao the cd_usuario_separacao value
	 */
	public void setUsuarioSeparacao (br.com.ksisolucoes.vo.controle.Usuario usuarioSeparacao) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioSeparacaoOld = this.usuarioSeparacao;
		this.usuarioSeparacao = usuarioSeparacao;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioSeparacao", usuarioSeparacaoOld, usuarioSeparacao);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO);
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}

	/**
	 * Return the value associated with the column: usuario_integracao
	 */
	public java.lang.String getUsuarioIntegracao () {
		return getPropertyValue(this, usuarioIntegracao, PROP_USUARIO_INTEGRACAO);
	}

	/**
	 * Set the value related to the column: usuario_integracao
	 * @param usuarioIntegracao the usuario_integracao value
	 */
	public void setUsuarioIntegracao (java.lang.String usuarioIntegracao) {
//        java.lang.String usuarioIntegracaoOld = this.usuarioIntegracao;
		this.usuarioIntegracao = usuarioIntegracao;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioIntegracao", usuarioIntegracaoOld, usuarioIntegracao);
	}

	/**
	 * Return the value associated with the column: cd_motorista
	 */
	public br.com.ksisolucoes.vo.frota.Motorista getMotorista () {
		return getPropertyValue(this, motorista, PROP_MOTORISTA);
	}

	/**
	 * Set the value related to the column: cd_motorista
	 * @param motorista the cd_motorista value
	 */
	public void setMotorista (br.com.ksisolucoes.vo.frota.Motorista motorista) {
//        br.com.ksisolucoes.vo.frota.Motorista motoristaOld = this.motorista;
		this.motorista = motorista;
//        this.getPropertyChangeSupport().firePropertyChange ("motorista", motoristaOld, motorista);
	}



	/**
	 * Return the value associated with the column: cd_veiculo
	 */
	public br.com.ksisolucoes.vo.frota.Veiculo getVeiculo () {
		return getPropertyValue(this, veiculo, PROP_VEICULO);
	}

	/**
	 * Set the value related to the column: cd_veiculo
	 * @param veiculo the cd_veiculo value
	 */
	public void setVeiculo (br.com.ksisolucoes.vo.frota.Veiculo veiculo) {
//        br.com.ksisolucoes.vo.frota.Veiculo veiculoOld = this.veiculo;
		this.veiculo = veiculo;
//        this.getPropertyChangeSupport().firePropertyChange ("veiculo", veiculoOld, veiculo);
	}



	/**
	 * Return the value associated with the column: cd_usuario_recebto
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioRecebimento () {
		return getPropertyValue(this, usuarioRecebimento, PROP_USUARIO_RECEBIMENTO);
	}

	/**
	 * Set the value related to the column: cd_usuario_recebto
	 * @param usuarioRecebimento the cd_usuario_recebto value
	 */
	public void setUsuarioRecebimento (br.com.ksisolucoes.vo.controle.Usuario usuarioRecebimento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioRecebimentoOld = this.usuarioRecebimento;
		this.usuarioRecebimento = usuarioRecebimento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioRecebimento", usuarioRecebimentoOld, usuarioRecebimento);
	}



	/**
	 * Return the value associated with the column: cod_deposito
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Deposito getDeposito () {
		return getPropertyValue(this, deposito, PROP_DEPOSITO);
	}

	/**
	 * Set the value related to the column: cod_deposito
	 * @param deposito the cod_deposito value
	 */
	public void setDeposito (br.com.ksisolucoes.vo.entradas.estoque.Deposito deposito) {
//        br.com.ksisolucoes.vo.entradas.estoque.Deposito depositoOld = this.deposito;
		this.deposito = deposito;
//        this.getPropertyChangeSupport().firePropertyChange ("deposito", depositoOld, deposito);
	}



	/**
	 * Return the value associated with the column: pedido_pai
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferencia getPedidoTransferenciaPai () {
		return getPropertyValue(this, pedidoTransferenciaPai, PROP_PEDIDO_TRANSFERENCIA_PAI);
	}

	/**
	 * Set the value related to the column: pedido_pai
	 * @param pedidoTransferenciaPai the pedido_pai value
	 */
	public void setPedidoTransferenciaPai (br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferencia pedidoTransferenciaPai) {
//        br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferencia pedidoTransferenciaPaiOld = this.pedidoTransferenciaPai;
		this.pedidoTransferenciaPai = pedidoTransferenciaPai;
//        this.getPropertyChangeSupport().firePropertyChange ("pedidoTransferenciaPai", pedidoTransferenciaPaiOld, pedidoTransferenciaPai);
	}



	/**
	 * Return the value associated with the column: cd_usuario_can
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO);
	}

	/**
	 * Set the value related to the column: cd_usuario_can
	 * @param usuarioCancelamento the cd_usuario_can value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferencia)) return false;
		else {
			br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferencia pedidoTransferencia = (br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferencia) obj;
			if (null == this.getCodigo() || null == pedidoTransferencia.getCodigo()) return false;
			else return (this.getCodigo().equals(pedidoTransferencia.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

	public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
		return this.retornoValidacao;
	}

	public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
	}

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}