package br.com.ksisolucoes.vo.vigilancia;

import java.io.Serializable;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.base.BaseEstabelecimentoCnae;



public class EstabelecimentoCnae extends BaseEstabelecimentoCnae implements CodigoManager {
	private static final long serialVersionUID = 1L;
        
        public static final String PROP_DESCRICAO_TERCEIRIZADA = "descricaoTerceirizada";

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EstabelecimentoCnae () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public EstabelecimentoCnae (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public EstabelecimentoCnae (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimento,
		br.com.ksisolucoes.vo.vigilancia.TabelaCnae cnae,
		java.lang.Long terceirizada) {

		super (
			codigo,
			estabelecimento,
			cnae,
			terceirizada);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public String getDescricaoTerceirizada () {
        if (getTerceirizada() != null) {
            if (getTerceirizada().equals(RepositoryComponentDefault.SIM_LONG)){
               return Bundle.getStringApplication("rotulo_sim");
            } else if (getTerceirizada().equals(RepositoryComponentDefault.NAO_LONG)){
               return Bundle.getStringApplication("rotulo_nao"); 
            }
        }
       return "";
    }
}