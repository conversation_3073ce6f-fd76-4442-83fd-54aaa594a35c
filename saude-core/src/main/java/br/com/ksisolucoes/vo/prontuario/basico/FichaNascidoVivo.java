package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.celk.integracao.IntegracaoRest;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseFichaNascidoVivo;

import java.io.Serializable;

@IntegracaoRest
public class FichaNascidoVivo extends BaseFichaNascidoVivo implements CodigoManager {
    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public FichaNascidoVivo() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public FichaNascidoVivo(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public FichaNascidoVivo(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.controle.Usuario usuarioIntegracao,
            br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusMae,
            br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusNascido,
            java.util.Date dataCadastro,
            java.lang.Long localParto,
            java.lang.Long tipoParto,
            java.lang.Long duracaoGestacao) {

        super(
                codigo,
                usuarioIntegracao,
                usuarioCadsusMae,
                usuarioCadsusNascido,
                dataCadastro,
                localParto,
                tipoParto,
                duracaoGestacao);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public enum TipoParto implements IEnum<TipoParto> {

        NORMAL(0L, Bundle.getStringApplication("rotulo_normal")),
        CESARIO(1L, Bundle.getStringApplication("rotulo_cesario")),
        FORCEPS(2L, Bundle.getStringApplication("rotulo_forceps")),
        OUTRO(3L, Bundle.getStringApplication("rotulo_outro"));

        private final Long value;
        private final String descricao;

        TipoParto(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static FichaNascidoVivo.TipoParto valueOf(Long value) {
            for (FichaNascidoVivo.TipoParto tipoParto : FichaNascidoVivo.TipoParto.values()) {
                if (tipoParto.value().equals(value)) {
                    return tipoParto;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    public enum LocalParto implements IEnum<LocalParto> {
        HOSPITAL(0L, Bundle.getStringApplication("rotulo_hospital")),
        DOMICILIO(1L, Bundle.getStringApplication("rotulo_domicilio")),
        OUTROS_ESTABELECIMENTOS(2L, Bundle.getStringApplication("rotulo_outro_estabelecimento_saude")),
        OUTRO(3L, Bundle.getStringApplication("rotulo_outro"));

        private final Long value;
        private final String descricao;

        LocalParto(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static FichaNascidoVivo.LocalParto valueOf(Long value) {
            for (FichaNascidoVivo.LocalParto localParto : FichaNascidoVivo.LocalParto.values()) {
                if (localParto.value().equals(value)) {
                    return localParto;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    public enum QtdConsultasPrenatal implements IEnum<QtdConsultasPrenatal> {
        NENHUMA(0L, Bundle.getStringApplication("rotulo_nunhuma")),
        ATE_6(1L, Bundle.getStringApplication("rotulo_ate_6_consultas")),
        MAIS_DE_6(2L, Bundle.getStringApplication("rotulo_mais_de_6_consultas")),
        IGNORADO(3L, Bundle.getStringApplication("rotulo_ignorado"));

        private final Long value;
        private final String descricao;

        QtdConsultasPrenatal(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static FichaNascidoVivo.QtdConsultasPrenatal valueOf(Long value) {
            for (FichaNascidoVivo.QtdConsultasPrenatal qtdConsultasPrenatal : FichaNascidoVivo.QtdConsultasPrenatal.values()) {
                if (qtdConsultasPrenatal.value().equals(value)) {
                    return qtdConsultasPrenatal;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    /*[CONSTRUCTOR MARKER END]*/

    public enum DuracaoGestacao implements IEnum<DuracaoGestacao> {
        DURACAO_0_A_21_SEMANAS(0L, Bundle.getStringApplication("rotulo_0_a_21_semanas")),
        DURACAO_22_A_27_SEMANAS(1L, Bundle.getStringApplication("rotulo_22_a_27_semanas")),
        DURACAO_28_A_36_SEMANAS(2L, Bundle.getStringApplication("rotulo_28_a_36_semanas")),
        DURACAO_37_A_41_SEMANAS(3L, Bundle.getStringApplication("rotulo_37_a_41_semanas")),
        DURACAO_42_A_47_SEMANAS(4L, Bundle.getStringApplication("rotulo_42_a_47_semanas")),
        IGNORADO(5L, Bundle.getStringApplication("rotulo_ignorado"));

        private final Long value;
        private final String descricao;

        DuracaoGestacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static FichaNascidoVivo.DuracaoGestacao valueOf(Long value) {
            for (FichaNascidoVivo.DuracaoGestacao duracaoGestacao : FichaNascidoVivo.DuracaoGestacao.values()) {
                if (duracaoGestacao.value().equals(value)) {
                    return duracaoGestacao;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    public enum TipoGravidez implements IEnum<TipoGravidez> {
        UNICA(0L, Bundle.getStringApplication("rotulo_unica")),
        DUPLA(1L, Bundle.getStringApplication("rotulo_dupla")),
        TRIPLA(2L, Bundle.getStringApplication("rotulo_tripla")),
        TRIPLA_MAIS(3L, Bundle.getStringApplication("rotulo_tripla_mais")),
        IGNORADO(4L, Bundle.getStringApplication("rotulo_ignorado"));

        private final Long value;
        private final String descricao;

        TipoGravidez(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static FichaNascidoVivo.TipoGravidez valueOf(Long value) {
            for (FichaNascidoVivo.TipoGravidez tipoGravidez : FichaNascidoVivo.TipoGravidez.values()) {
                if (tipoGravidez.value().equals(value)) {
                    return tipoGravidez;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    public String getDataCadastroFormatada() {
        return Data.formatar(getDataCadastro());
    }
}