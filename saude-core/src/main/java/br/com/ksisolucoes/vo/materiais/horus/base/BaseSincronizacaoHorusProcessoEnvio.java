package br.com.ksisolucoes.vo.materiais.horus.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the horus_sincronizacao_proc_envio table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="horus_sincronizacao_proc_envio"
 */

public abstract class BaseSincronizacaoHorusProcessoEnvio extends BaseRootVO implements Serializable {

	public static String REF = "SincronizacaoHorusProcessoEnvio";
	public static final String PROP_STATUS = "status";
	public static final String PROP_NUMERO_PROTOCOLO_RECEBIMENTO = "numeroProtocoloRecebimento";
	public static final String PROP_MENSAGEM_ERRO = "mensagemErro";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_NUMERO_PROTOCOLO = "numeroProtocolo";
	public static final String PROP_SINCRONIZACAO_HORUS_PROCESSO = "sincronizacaoHorusProcesso";
	public static final String PROP_DATA_PROTOCOLO_RECEBIMENTO = "dataProtocoloRecebimento";


	// constructors
	public BaseSincronizacaoHorusProcessoEnvio () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseSincronizacaoHorusProcessoEnvio (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long status;
	private java.lang.String mensagemErro;
	private java.lang.Long numeroProtocolo;
	private java.lang.String numeroProtocoloRecebimento;
	private java.lang.String dataProtocoloRecebimento;

	// many to one
	private br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso sincronizacaoHorusProcesso;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_horus_sincronizacao_proc_envio"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: msg_erro
	 */
	public java.lang.String getMensagemErro () {
		return getPropertyValue(this, mensagemErro, PROP_MENSAGEM_ERRO); 
	}

	/**
	 * Set the value related to the column: msg_erro
	 * @param mensagemErro the msg_erro value
	 */
	public void setMensagemErro (java.lang.String mensagemErro) {
//        java.lang.String mensagemErroOld = this.mensagemErro;
		this.mensagemErro = mensagemErro;
//        this.getPropertyChangeSupport().firePropertyChange ("mensagemErro", mensagemErroOld, mensagemErro);
	}



	/**
	 * Return the value associated with the column: num_protocolo
	 */
	public java.lang.Long getNumeroProtocolo () {
		return getPropertyValue(this, numeroProtocolo, PROP_NUMERO_PROTOCOLO); 
	}

	/**
	 * Set the value related to the column: num_protocolo
	 * @param numeroProtocolo the num_protocolo value
	 */
	public void setNumeroProtocolo (java.lang.Long numeroProtocolo) {
//        java.lang.Long numeroProtocoloOld = this.numeroProtocolo;
		this.numeroProtocolo = numeroProtocolo;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroProtocolo", numeroProtocoloOld, numeroProtocolo);
	}



	/**
	 * Return the value associated with the column: nr_protocolo_recebimento
	 */
	public java.lang.String getNumeroProtocoloRecebimento () {
		return getPropertyValue(this, numeroProtocoloRecebimento, PROP_NUMERO_PROTOCOLO_RECEBIMENTO); 
	}

	/**
	 * Set the value related to the column: nr_protocolo_recebimento
	 * @param numeroProtocoloRecebimento the nr_protocolo_recebimento value
	 */
	public void setNumeroProtocoloRecebimento (java.lang.String numeroProtocoloRecebimento) {
//        java.lang.String numeroProtocoloRecebimentoOld = this.numeroProtocoloRecebimento;
		this.numeroProtocoloRecebimento = numeroProtocoloRecebimento;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroProtocoloRecebimento", numeroProtocoloRecebimentoOld, numeroProtocoloRecebimento);
	}



	/**
	 * Return the value associated with the column: dt_protocolo_recebimento
	 */
	public java.lang.String getDataProtocoloRecebimento () {
		return getPropertyValue(this, dataProtocoloRecebimento, PROP_DATA_PROTOCOLO_RECEBIMENTO); 
	}

	/**
	 * Set the value related to the column: dt_protocolo_recebimento
	 * @param dataProtocoloRecebimento the dt_protocolo_recebimento value
	 */
	public void setDataProtocoloRecebimento (java.lang.String dataProtocoloRecebimento) {
//        java.lang.String dataProtocoloRecebimentoOld = this.dataProtocoloRecebimento;
		this.dataProtocoloRecebimento = dataProtocoloRecebimento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataProtocoloRecebimento", dataProtocoloRecebimentoOld, dataProtocoloRecebimento);
	}



	/**
	 * Return the value associated with the column: cd_horus_sincronizacao_processo
	 */
	public br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso getSincronizacaoHorusProcesso () {
		return getPropertyValue(this, sincronizacaoHorusProcesso, PROP_SINCRONIZACAO_HORUS_PROCESSO); 
	}

	/**
	 * Set the value related to the column: cd_horus_sincronizacao_processo
	 * @param sincronizacaoHorusProcesso the cd_horus_sincronizacao_processo value
	 */
	public void setSincronizacaoHorusProcesso (br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso sincronizacaoHorusProcesso) {
//        br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso sincronizacaoHorusProcessoOld = this.sincronizacaoHorusProcesso;
		this.sincronizacaoHorusProcesso = sincronizacaoHorusProcesso;
//        this.getPropertyChangeSupport().firePropertyChange ("sincronizacaoHorusProcesso", sincronizacaoHorusProcessoOld, sincronizacaoHorusProcesso);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcessoEnvio)) return false;
		else {
			br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcessoEnvio sincronizacaoHorusProcessoEnvio = (br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcessoEnvio) obj;
			if (null == this.getCodigo() || null == sincronizacaoHorusProcessoEnvio.getCodigo()) return false;
			else return (this.getCodigo().equals(sincronizacaoHorusProcessoEnvio.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}