package br.com.ksisolucoes.vo.emprestimo;

import java.io.Serializable;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.vo.emprestimo.base.BaseLancamentoEmprestimoItem;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class LancamentoEmprestimoItem extends BaseLancamentoEmprestimoItem implements CodigoManager {
	private static final long serialVersionUID = 1L;
        
        public static final String PROP_DESCRICAO_STATUS = "descricaoStatus";
        
        public enum Status implements IEnum {
            PENDENTE(0L, Bundle.getStringApplication("rotulo_pendente")),
            DEVOLVIDO(1L, Bundle.getStringApplication("rotulo_devolvido")),
            CANCELADO(2L, Bundle.getStringApplication("rotulo_cancelado")),
            ;

            private Long value;
            private String descricao;

            private Status(Long value, String descricao) {
                this.value = value;
                this.descricao = descricao;
            }

            public static Status valeuOf(Long value) {
                for (Status status : Status.values()) {
                    if (status.value().equals(value)) {
                        return status;
                    }
                }
                return null;
            }

            @Override
            public Long value() {
                return value;
            }

            @Override
            public String descricao() {
                return descricao;
            }

        }

/*[CONSTRUCTOR MARKER BEGIN]*/
	public LancamentoEmprestimoItem () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public LancamentoEmprestimoItem (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public LancamentoEmprestimoItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.emprestimo.LancamentoEmprestimo lancamentoEmprestimo,
		br.com.ksisolucoes.vo.entradas.estoque.Produto produto,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.String grupoEstoque,
		java.lang.Double quantidade,
		java.lang.Long status,
		java.util.Date dataCadastro) {

		super (
			codigo,
			lancamentoEmprestimo,
			produto,
			usuario,
			grupoEstoque,
			quantidade,
			status,
			dataCadastro);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public String getDescricaoStatus(){
        Status status = Status.valeuOf(getStatus());
        if (status != null && status.descricao != null) {
            return status.descricao();
        }
        return "";
    }
    
    public String getDescricaoGrupoEstoque(){
        if(GrupoEstoque.GRUPO_ESTOQUE_PADRAO.equals(getGrupoEstoque())){
            return "";
        } else {
            return getGrupoEstoque();
        }
    }
    
    public Double getSaldo(){
        return new Dinheiro(Coalesce.asDouble(getQuantidade())).subtrair(Coalesce.asDouble(getQuantidadeDevolvida())).doubleValue();
    }
    
}