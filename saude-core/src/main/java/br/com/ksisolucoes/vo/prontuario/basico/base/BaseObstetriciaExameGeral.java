package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the obstetricia_exame_geral table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="obstetricia_exame_geral"
 */

public abstract class BaseObstetriciaExameGeral extends BaseRootVO implements Serializable {

	public static String REF = "ObstetriciaExameGeral";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_PARTO_ANTERIOR_CESARIOS = "partoAnteriorCesarios";
	public static final String PROP_FETO_SITUACAO = "fetoSituacao";
	public static final String PROP_VARIZES = "varizes";
	public static final String PROP_DATA_ULTIMA_MENSTRUACAO = "dataUltimaMenstruacao";
	public static final String PROP_EDEMA = "edema";
	public static final String PROP_PARA = "para";
	public static final String PROP_ATENDIMENTO = "atendimento";
	public static final String PROP_DATA = "data";
	public static final String PROP_NUTRICAO = "nutricao";
	public static final String PROP_TIPO_GESTACAO = "tipoGestacao";
	public static final String PROP_FETO_APRESENTACAO = "fetoApresentacao";
	public static final String PROP_OBSTETRA = "obstetra";
	public static final String PROP_PAREDE = "parede";
	public static final String PROP_ABORTO_TIPO = "abortoTipo";
	public static final String PROP_ALTURA_UTERO = "alturaUtero";
	public static final String PROP_BCF_NUMERO_BATIMENTOS = "bcfNumeroBatimentos";
	public static final String PROP_BACIA_BR = "baciaBr";
	public static final String PROP_PARTO_ANTERIOR_FORCEPS = "partoAnteriorForceps";
	public static final String PROP_BACIA_BT = "baciaBt";
	public static final String PROP_PRE_NATAL = "preNatal";
	public static final String PROP_DATA_PROVAVEL_PARTO = "dataProvavelParto";
	public static final String PROP_GESTA = "gesta";
	public static final String PROP_BACIA_SPE = "baciaSpe";
	public static final String PROP_DATA_IMPRESSAO = "dataImpressao";
	public static final String PROP_ABORTO_INFECTADO = "abortoInfectado";
	public static final String PROP_PARTO_ANTERIOR_NATURAL = "partoAnteriorNatural";
	public static final String PROP_VOLUME = "volume";
	public static final String PROP_USO_CORTICOIDES = "usoCorticoides";
	public static final String PROP_BACIA_BC = "baciaBc";
	public static final String PROP_FATOR_RH = "fatorRh";
	public static final String PROP_VIROSES = "viroses";
	public static final String PROP_ABDOME = "abdome";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_DENTES = "dentes";
	public static final String PROP_OUTROS = "outros";
	public static final String PROP_ESTADO_GERAL = "estadoGeral";
	public static final String PROP_DATA_REGISTRO = "dataRegistro";
	public static final String PROP_TIPO_SANGUINEO = "tipoSanguineo";
	public static final String PROP_ABORTO_NATUREZA = "abortoNatureza";
	public static final String PROP_BCF_LOCALIZACAO = "bcfLocalizacao";
	public static final String PROP_CONCLUSAO = "conclusao";


	// constructors
	public BaseObstetriciaExameGeral () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseObstetriciaExameGeral (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseObstetriciaExameGeral (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento,
		java.util.Date dataRegistro) {

		this.setCodigo(codigo);
		this.setAtendimento(atendimento);
		this.setDataRegistro(dataRegistro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataUltimaMenstruacao;
	private java.util.Date dataProvavelParto;
	private java.lang.String preNatal;
	private java.lang.String viroses;
	private java.lang.String tipoSanguineo;
	private java.lang.Long fatorRh;
	private java.lang.Long partoAnteriorNatural;
	private java.lang.Long partoAnteriorForceps;
	private java.lang.Long partoAnteriorCesarios;
	private java.lang.String abortoNatureza;
	private java.lang.String abortoInfectado;
	private java.lang.Long abortoTipo;
	private java.lang.String estadoGeral;
	private java.lang.String nutricao;
	private java.lang.String dentes;
	private java.lang.String varizes;
	private java.lang.String edema;
	private java.lang.String usoCorticoides;
	private java.lang.String outros;
	private java.lang.String abdome;
	private java.lang.String parede;
	private java.lang.String volume;
	private java.lang.String alturaUtero;
	private java.lang.String fetoSituacao;
	private java.lang.String fetoApresentacao;
	private java.lang.String bcfLocalizacao;
	private java.lang.String bcfNumeroBatimentos;
	private java.lang.String baciaBc;
	private java.lang.String baciaBr;
	private java.lang.String baciaBt;
	private java.lang.String baciaSpe;
	private java.lang.Long gesta;
	private java.lang.Long para;
	private java.util.Date dataRegistro;
	private java.lang.String conclusao;
	private java.util.Date data;
	private java.util.Date dataImpressao;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.cadsus.Profissional obstetra;
	private br.com.ksisolucoes.vo.prontuario.hospital.TipoGestacao tipoGestacao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_obstetricia_geral"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_ult_menstruacao
	 */
	public java.util.Date getDataUltimaMenstruacao () {
		return getPropertyValue(this, dataUltimaMenstruacao, PROP_DATA_ULTIMA_MENSTRUACAO); 
	}

	/**
	 * Set the value related to the column: dt_ult_menstruacao
	 * @param dataUltimaMenstruacao the dt_ult_menstruacao value
	 */
	public void setDataUltimaMenstruacao (java.util.Date dataUltimaMenstruacao) {
//        java.util.Date dataUltimaMenstruacaoOld = this.dataUltimaMenstruacao;
		this.dataUltimaMenstruacao = dataUltimaMenstruacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUltimaMenstruacao", dataUltimaMenstruacaoOld, dataUltimaMenstruacao);
	}



	/**
	 * Return the value associated with the column: dt_provavel_parto
	 */
	public java.util.Date getDataProvavelParto () {
		return getPropertyValue(this, dataProvavelParto, PROP_DATA_PROVAVEL_PARTO); 
	}

	/**
	 * Set the value related to the column: dt_provavel_parto
	 * @param dataProvavelParto the dt_provavel_parto value
	 */
	public void setDataProvavelParto (java.util.Date dataProvavelParto) {
//        java.util.Date dataProvavelPartoOld = this.dataProvavelParto;
		this.dataProvavelParto = dataProvavelParto;
//        this.getPropertyChangeSupport().firePropertyChange ("dataProvavelParto", dataProvavelPartoOld, dataProvavelParto);
	}



	/**
	 * Return the value associated with the column: pre_natal
	 */
	public java.lang.String getPreNatal () {
		return getPropertyValue(this, preNatal, PROP_PRE_NATAL); 
	}

	/**
	 * Set the value related to the column: pre_natal
	 * @param preNatal the pre_natal value
	 */
	public void setPreNatal (java.lang.String preNatal) {
//        java.lang.String preNatalOld = this.preNatal;
		this.preNatal = preNatal;
//        this.getPropertyChangeSupport().firePropertyChange ("preNatal", preNatalOld, preNatal);
	}



	/**
	 * Return the value associated with the column: viroses
	 */
	public java.lang.String getViroses () {
		return getPropertyValue(this, viroses, PROP_VIROSES); 
	}

	/**
	 * Set the value related to the column: viroses
	 * @param viroses the viroses value
	 */
	public void setViroses (java.lang.String viroses) {
//        java.lang.String virosesOld = this.viroses;
		this.viroses = viroses;
//        this.getPropertyChangeSupport().firePropertyChange ("viroses", virosesOld, viroses);
	}



	/**
	 * Return the value associated with the column: tipo_sanguineo
	 */
	public java.lang.String getTipoSanguineo () {
		return getPropertyValue(this, tipoSanguineo, PROP_TIPO_SANGUINEO); 
	}

	/**
	 * Set the value related to the column: tipo_sanguineo
	 * @param tipoSanguineo the tipo_sanguineo value
	 */
	public void setTipoSanguineo (java.lang.String tipoSanguineo) {
//        java.lang.String tipoSanguineoOld = this.tipoSanguineo;
		this.tipoSanguineo = tipoSanguineo;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoSanguineo", tipoSanguineoOld, tipoSanguineo);
	}



	/**
	 * Return the value associated with the column: fator_rh
	 */
	public java.lang.Long getFatorRh () {
		return getPropertyValue(this, fatorRh, PROP_FATOR_RH); 
	}

	/**
	 * Set the value related to the column: fator_rh
	 * @param fatorRh the fator_rh value
	 */
	public void setFatorRh (java.lang.Long fatorRh) {
//        java.lang.Long fatorRhOld = this.fatorRh;
		this.fatorRh = fatorRh;
//        this.getPropertyChangeSupport().firePropertyChange ("fatorRh", fatorRhOld, fatorRh);
	}



	/**
	 * Return the value associated with the column: parto_anterior_natural
	 */
	public java.lang.Long getPartoAnteriorNatural () {
		return getPropertyValue(this, partoAnteriorNatural, PROP_PARTO_ANTERIOR_NATURAL); 
	}

	/**
	 * Set the value related to the column: parto_anterior_natural
	 * @param partoAnteriorNatural the parto_anterior_natural value
	 */
	public void setPartoAnteriorNatural (java.lang.Long partoAnteriorNatural) {
//        java.lang.Long partoAnteriorNaturalOld = this.partoAnteriorNatural;
		this.partoAnteriorNatural = partoAnteriorNatural;
//        this.getPropertyChangeSupport().firePropertyChange ("partoAnteriorNatural", partoAnteriorNaturalOld, partoAnteriorNatural);
	}



	/**
	 * Return the value associated with the column: parto_anterior_forceps
	 */
	public java.lang.Long getPartoAnteriorForceps () {
		return getPropertyValue(this, partoAnteriorForceps, PROP_PARTO_ANTERIOR_FORCEPS); 
	}

	/**
	 * Set the value related to the column: parto_anterior_forceps
	 * @param partoAnteriorForceps the parto_anterior_forceps value
	 */
	public void setPartoAnteriorForceps (java.lang.Long partoAnteriorForceps) {
//        java.lang.Long partoAnteriorForcepsOld = this.partoAnteriorForceps;
		this.partoAnteriorForceps = partoAnteriorForceps;
//        this.getPropertyChangeSupport().firePropertyChange ("partoAnteriorForceps", partoAnteriorForcepsOld, partoAnteriorForceps);
	}



	/**
	 * Return the value associated with the column: parto_anterior_cesarios
	 */
	public java.lang.Long getPartoAnteriorCesarios () {
		return getPropertyValue(this, partoAnteriorCesarios, PROP_PARTO_ANTERIOR_CESARIOS); 
	}

	/**
	 * Set the value related to the column: parto_anterior_cesarios
	 * @param partoAnteriorCesarios the parto_anterior_cesarios value
	 */
	public void setPartoAnteriorCesarios (java.lang.Long partoAnteriorCesarios) {
//        java.lang.Long partoAnteriorCesariosOld = this.partoAnteriorCesarios;
		this.partoAnteriorCesarios = partoAnteriorCesarios;
//        this.getPropertyChangeSupport().firePropertyChange ("partoAnteriorCesarios", partoAnteriorCesariosOld, partoAnteriorCesarios);
	}



	/**
	 * Return the value associated with the column: aborto_natureza
	 */
	public java.lang.String getAbortoNatureza () {
		return getPropertyValue(this, abortoNatureza, PROP_ABORTO_NATUREZA); 
	}

	/**
	 * Set the value related to the column: aborto_natureza
	 * @param abortoNatureza the aborto_natureza value
	 */
	public void setAbortoNatureza (java.lang.String abortoNatureza) {
//        java.lang.String abortoNaturezaOld = this.abortoNatureza;
		this.abortoNatureza = abortoNatureza;
//        this.getPropertyChangeSupport().firePropertyChange ("abortoNatureza", abortoNaturezaOld, abortoNatureza);
	}



	/**
	 * Return the value associated with the column: aborto_infectado
	 */
	public java.lang.String getAbortoInfectado () {
		return getPropertyValue(this, abortoInfectado, PROP_ABORTO_INFECTADO); 
	}

	/**
	 * Set the value related to the column: aborto_infectado
	 * @param abortoInfectado the aborto_infectado value
	 */
	public void setAbortoInfectado (java.lang.String abortoInfectado) {
//        java.lang.String abortoInfectadoOld = this.abortoInfectado;
		this.abortoInfectado = abortoInfectado;
//        this.getPropertyChangeSupport().firePropertyChange ("abortoInfectado", abortoInfectadoOld, abortoInfectado);
	}



	/**
	 * Return the value associated with the column: aborto_tipo
	 */
	public java.lang.Long getAbortoTipo () {
		return getPropertyValue(this, abortoTipo, PROP_ABORTO_TIPO); 
	}

	/**
	 * Set the value related to the column: aborto_tipo
	 * @param abortoTipo the aborto_tipo value
	 */
	public void setAbortoTipo (java.lang.Long abortoTipo) {
//        java.lang.Long abortoTipoOld = this.abortoTipo;
		this.abortoTipo = abortoTipo;
//        this.getPropertyChangeSupport().firePropertyChange ("abortoTipo", abortoTipoOld, abortoTipo);
	}



	/**
	 * Return the value associated with the column: estado_geral
	 */
	public java.lang.String getEstadoGeral () {
		return getPropertyValue(this, estadoGeral, PROP_ESTADO_GERAL); 
	}

	/**
	 * Set the value related to the column: estado_geral
	 * @param estadoGeral the estado_geral value
	 */
	public void setEstadoGeral (java.lang.String estadoGeral) {
//        java.lang.String estadoGeralOld = this.estadoGeral;
		this.estadoGeral = estadoGeral;
//        this.getPropertyChangeSupport().firePropertyChange ("estadoGeral", estadoGeralOld, estadoGeral);
	}



	/**
	 * Return the value associated with the column: nutricao
	 */
	public java.lang.String getNutricao () {
		return getPropertyValue(this, nutricao, PROP_NUTRICAO); 
	}

	/**
	 * Set the value related to the column: nutricao
	 * @param nutricao the nutricao value
	 */
	public void setNutricao (java.lang.String nutricao) {
//        java.lang.String nutricaoOld = this.nutricao;
		this.nutricao = nutricao;
//        this.getPropertyChangeSupport().firePropertyChange ("nutricao", nutricaoOld, nutricao);
	}



	/**
	 * Return the value associated with the column: dentes
	 */
	public java.lang.String getDentes () {
		return getPropertyValue(this, dentes, PROP_DENTES); 
	}

	/**
	 * Set the value related to the column: dentes
	 * @param dentes the dentes value
	 */
	public void setDentes (java.lang.String dentes) {
//        java.lang.String dentesOld = this.dentes;
		this.dentes = dentes;
//        this.getPropertyChangeSupport().firePropertyChange ("dentes", dentesOld, dentes);
	}



	/**
	 * Return the value associated with the column: varizes
	 */
	public java.lang.String getVarizes () {
		return getPropertyValue(this, varizes, PROP_VARIZES); 
	}

	/**
	 * Set the value related to the column: varizes
	 * @param varizes the varizes value
	 */
	public void setVarizes (java.lang.String varizes) {
//        java.lang.String varizesOld = this.varizes;
		this.varizes = varizes;
//        this.getPropertyChangeSupport().firePropertyChange ("varizes", varizesOld, varizes);
	}



	/**
	 * Return the value associated with the column: edema
	 */
	public java.lang.String getEdema () {
		return getPropertyValue(this, edema, PROP_EDEMA); 
	}

	/**
	 * Set the value related to the column: edema
	 * @param edema the edema value
	 */
	public void setEdema (java.lang.String edema) {
//        java.lang.String edemaOld = this.edema;
		this.edema = edema;
//        this.getPropertyChangeSupport().firePropertyChange ("edema", edemaOld, edema);
	}



	/**
	 * Return the value associated with the column: uso_corticoides
	 */
	public java.lang.String getUsoCorticoides () {
		return getPropertyValue(this, usoCorticoides, PROP_USO_CORTICOIDES); 
	}

	/**
	 * Set the value related to the column: uso_corticoides
	 * @param usoCorticoides the uso_corticoides value
	 */
	public void setUsoCorticoides (java.lang.String usoCorticoides) {
//        java.lang.String usoCorticoidesOld = this.usoCorticoides;
		this.usoCorticoides = usoCorticoides;
//        this.getPropertyChangeSupport().firePropertyChange ("usoCorticoides", usoCorticoidesOld, usoCorticoides);
	}



	/**
	 * Return the value associated with the column: outros
	 */
	public java.lang.String getOutros () {
		return getPropertyValue(this, outros, PROP_OUTROS); 
	}

	/**
	 * Set the value related to the column: outros
	 * @param outros the outros value
	 */
	public void setOutros (java.lang.String outros) {
//        java.lang.String outrosOld = this.outros;
		this.outros = outros;
//        this.getPropertyChangeSupport().firePropertyChange ("outros", outrosOld, outros);
	}



	/**
	 * Return the value associated with the column: abdome
	 */
	public java.lang.String getAbdome () {
		return getPropertyValue(this, abdome, PROP_ABDOME); 
	}

	/**
	 * Set the value related to the column: abdome
	 * @param abdome the abdome value
	 */
	public void setAbdome (java.lang.String abdome) {
//        java.lang.String abdomeOld = this.abdome;
		this.abdome = abdome;
//        this.getPropertyChangeSupport().firePropertyChange ("abdome", abdomeOld, abdome);
	}



	/**
	 * Return the value associated with the column: parede
	 */
	public java.lang.String getParede () {
		return getPropertyValue(this, parede, PROP_PAREDE); 
	}

	/**
	 * Set the value related to the column: parede
	 * @param parede the parede value
	 */
	public void setParede (java.lang.String parede) {
//        java.lang.String paredeOld = this.parede;
		this.parede = parede;
//        this.getPropertyChangeSupport().firePropertyChange ("parede", paredeOld, parede);
	}



	/**
	 * Return the value associated with the column: volume
	 */
	public java.lang.String getVolume () {
		return getPropertyValue(this, volume, PROP_VOLUME); 
	}

	/**
	 * Set the value related to the column: volume
	 * @param volume the volume value
	 */
	public void setVolume (java.lang.String volume) {
//        java.lang.String volumeOld = this.volume;
		this.volume = volume;
//        this.getPropertyChangeSupport().firePropertyChange ("volume", volumeOld, volume);
	}



	/**
	 * Return the value associated with the column: altura_utero
	 */
	public java.lang.String getAlturaUtero () {
		return getPropertyValue(this, alturaUtero, PROP_ALTURA_UTERO); 
	}

	/**
	 * Set the value related to the column: altura_utero
	 * @param alturaUtero the altura_utero value
	 */
	public void setAlturaUtero (java.lang.String alturaUtero) {
//        java.lang.String alturaUteroOld = this.alturaUtero;
		this.alturaUtero = alturaUtero;
//        this.getPropertyChangeSupport().firePropertyChange ("alturaUtero", alturaUteroOld, alturaUtero);
	}



	/**
	 * Return the value associated with the column: feto_situacao
	 */
	public java.lang.String getFetoSituacao () {
		return getPropertyValue(this, fetoSituacao, PROP_FETO_SITUACAO); 
	}

	/**
	 * Set the value related to the column: feto_situacao
	 * @param fetoSituacao the feto_situacao value
	 */
	public void setFetoSituacao (java.lang.String fetoSituacao) {
//        java.lang.String fetoSituacaoOld = this.fetoSituacao;
		this.fetoSituacao = fetoSituacao;
//        this.getPropertyChangeSupport().firePropertyChange ("fetoSituacao", fetoSituacaoOld, fetoSituacao);
	}



	/**
	 * Return the value associated with the column: feto_apresentacao
	 */
	public java.lang.String getFetoApresentacao () {
		return getPropertyValue(this, fetoApresentacao, PROP_FETO_APRESENTACAO); 
	}

	/**
	 * Set the value related to the column: feto_apresentacao
	 * @param fetoApresentacao the feto_apresentacao value
	 */
	public void setFetoApresentacao (java.lang.String fetoApresentacao) {
//        java.lang.String fetoApresentacaoOld = this.fetoApresentacao;
		this.fetoApresentacao = fetoApresentacao;
//        this.getPropertyChangeSupport().firePropertyChange ("fetoApresentacao", fetoApresentacaoOld, fetoApresentacao);
	}



	/**
	 * Return the value associated with the column: bcf_localizacao
	 */
	public java.lang.String getBcfLocalizacao () {
		return getPropertyValue(this, bcfLocalizacao, PROP_BCF_LOCALIZACAO); 
	}

	/**
	 * Set the value related to the column: bcf_localizacao
	 * @param bcfLocalizacao the bcf_localizacao value
	 */
	public void setBcfLocalizacao (java.lang.String bcfLocalizacao) {
//        java.lang.String bcfLocalizacaoOld = this.bcfLocalizacao;
		this.bcfLocalizacao = bcfLocalizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("bcfLocalizacao", bcfLocalizacaoOld, bcfLocalizacao);
	}



	/**
	 * Return the value associated with the column: bcf_numero_batimentos
	 */
	public java.lang.String getBcfNumeroBatimentos () {
		return getPropertyValue(this, bcfNumeroBatimentos, PROP_BCF_NUMERO_BATIMENTOS); 
	}

	/**
	 * Set the value related to the column: bcf_numero_batimentos
	 * @param bcfNumeroBatimentos the bcf_numero_batimentos value
	 */
	public void setBcfNumeroBatimentos (java.lang.String bcfNumeroBatimentos) {
//        java.lang.String bcfNumeroBatimentosOld = this.bcfNumeroBatimentos;
		this.bcfNumeroBatimentos = bcfNumeroBatimentos;
//        this.getPropertyChangeSupport().firePropertyChange ("bcfNumeroBatimentos", bcfNumeroBatimentosOld, bcfNumeroBatimentos);
	}



	/**
	 * Return the value associated with the column: bacia_bc
	 */
	public java.lang.String getBaciaBc () {
		return getPropertyValue(this, baciaBc, PROP_BACIA_BC); 
	}

	/**
	 * Set the value related to the column: bacia_bc
	 * @param baciaBc the bacia_bc value
	 */
	public void setBaciaBc (java.lang.String baciaBc) {
//        java.lang.String baciaBcOld = this.baciaBc;
		this.baciaBc = baciaBc;
//        this.getPropertyChangeSupport().firePropertyChange ("baciaBc", baciaBcOld, baciaBc);
	}



	/**
	 * Return the value associated with the column: bacia_br
	 */
	public java.lang.String getBaciaBr () {
		return getPropertyValue(this, baciaBr, PROP_BACIA_BR); 
	}

	/**
	 * Set the value related to the column: bacia_br
	 * @param baciaBr the bacia_br value
	 */
	public void setBaciaBr (java.lang.String baciaBr) {
//        java.lang.String baciaBrOld = this.baciaBr;
		this.baciaBr = baciaBr;
//        this.getPropertyChangeSupport().firePropertyChange ("baciaBr", baciaBrOld, baciaBr);
	}



	/**
	 * Return the value associated with the column: bacia_bt
	 */
	public java.lang.String getBaciaBt () {
		return getPropertyValue(this, baciaBt, PROP_BACIA_BT); 
	}

	/**
	 * Set the value related to the column: bacia_bt
	 * @param baciaBt the bacia_bt value
	 */
	public void setBaciaBt (java.lang.String baciaBt) {
//        java.lang.String baciaBtOld = this.baciaBt;
		this.baciaBt = baciaBt;
//        this.getPropertyChangeSupport().firePropertyChange ("baciaBt", baciaBtOld, baciaBt);
	}



	/**
	 * Return the value associated with the column: bacia_spe
	 */
	public java.lang.String getBaciaSpe () {
		return getPropertyValue(this, baciaSpe, PROP_BACIA_SPE); 
	}

	/**
	 * Set the value related to the column: bacia_spe
	 * @param baciaSpe the bacia_spe value
	 */
	public void setBaciaSpe (java.lang.String baciaSpe) {
//        java.lang.String baciaSpeOld = this.baciaSpe;
		this.baciaSpe = baciaSpe;
//        this.getPropertyChangeSupport().firePropertyChange ("baciaSpe", baciaSpeOld, baciaSpe);
	}



	/**
	 * Return the value associated with the column: gesta
	 */
	public java.lang.Long getGesta () {
		return getPropertyValue(this, gesta, PROP_GESTA); 
	}

	/**
	 * Set the value related to the column: gesta
	 * @param gesta the gesta value
	 */
	public void setGesta (java.lang.Long gesta) {
//        java.lang.Long gestaOld = this.gesta;
		this.gesta = gesta;
//        this.getPropertyChangeSupport().firePropertyChange ("gesta", gestaOld, gesta);
	}



	/**
	 * Return the value associated with the column: para
	 */
	public java.lang.Long getPara () {
		return getPropertyValue(this, para, PROP_PARA); 
	}

	/**
	 * Set the value related to the column: para
	 * @param para the para value
	 */
	public void setPara (java.lang.Long para) {
//        java.lang.Long paraOld = this.para;
		this.para = para;
//        this.getPropertyChangeSupport().firePropertyChange ("para", paraOld, para);
	}



	/**
	 * Return the value associated with the column: dt_registro
	 */
	public java.util.Date getDataRegistro () {
		return getPropertyValue(this, dataRegistro, PROP_DATA_REGISTRO); 
	}

	/**
	 * Set the value related to the column: dt_registro
	 * @param dataRegistro the dt_registro value
	 */
	public void setDataRegistro (java.util.Date dataRegistro) {
//        java.util.Date dataRegistroOld = this.dataRegistro;
		this.dataRegistro = dataRegistro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataRegistro", dataRegistroOld, dataRegistro);
	}



	/**
	 * Return the value associated with the column: conclusao
	 */
	public java.lang.String getConclusao () {
		return getPropertyValue(this, conclusao, PROP_CONCLUSAO); 
	}

	/**
	 * Set the value related to the column: conclusao
	 * @param conclusao the conclusao value
	 */
	public void setConclusao (java.lang.String conclusao) {
//        java.lang.String conclusaoOld = this.conclusao;
		this.conclusao = conclusao;
//        this.getPropertyChangeSupport().firePropertyChange ("conclusao", conclusaoOld, conclusao);
	}



	/**
	 * Return the value associated with the column: data
	 */
	public java.util.Date getData () {
		return getPropertyValue(this, data, PROP_DATA); 
	}

	/**
	 * Set the value related to the column: data
	 * @param data the data value
	 */
	public void setData (java.util.Date data) {
//        java.util.Date dataOld = this.data;
		this.data = data;
//        this.getPropertyChangeSupport().firePropertyChange ("data", dataOld, data);
	}



	/**
	 * Return the value associated with the column: data_impressao
	 */
	public java.util.Date getDataImpressao () {
		return getPropertyValue(this, dataImpressao, PROP_DATA_IMPRESSAO); 
	}

	/**
	 * Set the value related to the column: data_impressao
	 * @param dataImpressao the data_impressao value
	 */
	public void setDataImpressao (java.util.Date dataImpressao) {
//        java.util.Date dataImpressaoOld = this.dataImpressao;
		this.dataImpressao = dataImpressao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataImpressao", dataImpressaoOld, dataImpressao);
	}



	/**
	 * Return the value associated with the column: nr_atendimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Atendimento getAtendimento () {
		return getPropertyValue(this, atendimento, PROP_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: nr_atendimento
	 * @param atendimento the nr_atendimento value
	 */
	public void setAtendimento (br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoOld = this.atendimento;
		this.atendimento = atendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimento", atendimentoOld, atendimento);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_prof_obstetra
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getObstetra () {
		return getPropertyValue(this, obstetra, PROP_OBSTETRA); 
	}

	/**
	 * Set the value related to the column: cd_prof_obstetra
	 * @param obstetra the cd_prof_obstetra value
	 */
	public void setObstetra (br.com.ksisolucoes.vo.cadsus.Profissional obstetra) {
//        br.com.ksisolucoes.vo.cadsus.Profissional obstetraOld = this.obstetra;
		this.obstetra = obstetra;
//        this.getPropertyChangeSupport().firePropertyChange ("obstetra", obstetraOld, obstetra);
	}



	/**
	 * Return the value associated with the column: cd_tipo_gestacao
	 */
	public br.com.ksisolucoes.vo.prontuario.hospital.TipoGestacao getTipoGestacao () {
		return getPropertyValue(this, tipoGestacao, PROP_TIPO_GESTACAO); 
	}

	/**
	 * Set the value related to the column: cd_tipo_gestacao
	 * @param tipoGestacao the cd_tipo_gestacao value
	 */
	public void setTipoGestacao (br.com.ksisolucoes.vo.prontuario.hospital.TipoGestacao tipoGestacao) {
//        br.com.ksisolucoes.vo.prontuario.hospital.TipoGestacao tipoGestacaoOld = this.tipoGestacao;
		this.tipoGestacao = tipoGestacao;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoGestacao", tipoGestacaoOld, tipoGestacao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.ObstetriciaExameGeral)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.ObstetriciaExameGeral obstetriciaExameGeral = (br.com.ksisolucoes.vo.prontuario.basico.ObstetriciaExameGeral) obj;
			if (null == this.getCodigo() || null == obstetriciaExameGeral.getCodigo()) return false;
			else return (this.getCodigo().equals(obstetriciaExameGeral.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}