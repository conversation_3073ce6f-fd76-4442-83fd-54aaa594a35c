<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class
            name="IMTableSolicitacaoAgendamentoToExamePadrao"
            table="im_solic_agend_to_exame_padrao"
    >
        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_im"
        >
            <generator class="sequence">
                <param name="sequence">seq_im_solic_agend_to_exame_padrao</param>
            </generator>
        </id>
        <version column="version" name="version" type="long" />

        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento"
                     name="solicitacaoAgendamento"
                     not-null="true">
            <column name="cd_solicitacao" />
        </many-to-one>

        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.Exame"
                     name="exame"
                     not-null="true">
            <column name="cd_exame" />
        </many-to-one>

    </class>
</hibernate-mapping>