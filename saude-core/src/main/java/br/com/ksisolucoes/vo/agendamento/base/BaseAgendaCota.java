package br.com.ksisolucoes.vo.agendamento.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the agenda_cota table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="agenda_cota"
 */

public abstract class BaseAgendaCota extends BaseRootVO implements Serializable {

	public static String REF = "AgendaCota";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_TIPO_COTA = "tipoCota";
	public static final String PROP_QUANTIDADE_COTA = "quantidadeCota";
	public static final String PROP_TIPO_PROCEDIMENTO = "tipoProcedimento";


	// constructors
	public BaseAgendaCota () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAgendaCota (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseAgendaCota (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimento,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		java.lang.Long quantidadeCota,
		java.lang.Long tipoCota) {

		this.setCodigo(codigo);
		this.setTipoProcedimento(tipoProcedimento);
		this.setEmpresa(empresa);
		this.setQuantidadeCota(quantidadeCota);
		this.setTipoCota(tipoCota);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long quantidadeCota;
	private java.lang.Long tipoCota;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimento;
	private br.com.ksisolucoes.vo.basico.Empresa empresa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_agenda_cota"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: qt_cota
	 */
	public java.lang.Long getQuantidadeCota () {
		return getPropertyValue(this, quantidadeCota, PROP_QUANTIDADE_COTA); 
	}

	/**
	 * Set the value related to the column: qt_cota
	 * @param quantidadeCota the qt_cota value
	 */
	public void setQuantidadeCota (java.lang.Long quantidadeCota) {
//        java.lang.Long quantidadeCotaOld = this.quantidadeCota;
		this.quantidadeCota = quantidadeCota;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeCota", quantidadeCotaOld, quantidadeCota);
	}



	/**
	 * Return the value associated with the column: tp_cota
	 */
	public java.lang.Long getTipoCota () {
		return getPropertyValue(this, tipoCota, PROP_TIPO_COTA); 
	}

	/**
	 * Set the value related to the column: tp_cota
	 * @param tipoCota the tp_cota value
	 */
	public void setTipoCota (java.lang.Long tipoCota) {
//        java.lang.Long tipoCotaOld = this.tipoCota;
		this.tipoCota = tipoCota;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoCota", tipoCotaOld, tipoCota);
	}



	/**
	 * Return the value associated with the column: cd_tp_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento getTipoProcedimento () {
		return getPropertyValue(this, tipoProcedimento, PROP_TIPO_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_tp_procedimento
	 * @param tipoProcedimento the cd_tp_procedimento value
	 */
	public void setTipoProcedimento (br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimentoOld = this.tipoProcedimento;
		this.tipoProcedimento = tipoProcedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoProcedimento", tipoProcedimentoOld, tipoProcedimento);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.agendamento.AgendaCota)) return false;
		else {
			br.com.ksisolucoes.vo.agendamento.AgendaCota agendaCota = (br.com.ksisolucoes.vo.agendamento.AgendaCota) obj;
			if (null == this.getCodigo() || null == agendaCota.getCodigo()) return false;
			else return (this.getCodigo().equals(agendaCota.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}