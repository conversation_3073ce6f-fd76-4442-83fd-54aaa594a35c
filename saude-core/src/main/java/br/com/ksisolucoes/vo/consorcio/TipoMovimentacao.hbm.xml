<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.consorcio"  >
    <class name="TipoMovimentacao"  table="tipo_movimentacao" >
        
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_tp_movimentacao"
        >
            <generator class="assigned"/>
        </id> 
        
        <version column="version" name="version" type="long" />

        <property
            name="referencia"
            column="referencia"
            type="java.lang.String"
            length="10"
            not-null="false"
        />

        <property
            name="descricao"
            column="ds_tp_movimentacao"
            type="java.lang.String"
            length="30"
            not-null="true"
        />

        <property
            name="tipoMovimento"
            column="tp_movimento"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property
            name="flagInterno"
            column="flag_interno"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property
            name="movimentaOrcamento"
            column="movimenta_orcamento"
            type="java.lang.Long"
            not-null="true"
        />

    </class>
</hibernate-mapping>