package br.com.ksisolucoes.vo.entradas.estoque.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;

/**
 * This is an object that contains data related to the tipo_via_medicamento
 * table. Do not modify this class because it will be overwritten if the
 * configuration file related to this class is modified.
 *
 * @hibernate.class table="tipo_via_medicamento"
 */
public abstract class BaseTipoViaMedicamento extends BaseRootVO implements Serializable {

    public static String REF = "TipoViaMedicamento";
    public static final String PROP_CODIGO = "codigo";
    public static final String PROP_REFERENCIA = "referencia";
    public static final String PROP_SIGLA = "sigla";
    public static final String PROP_DESCRICAO = "descricao";
    public static final String PROP_ORDEM = "ordem";

    // constructors
    public BaseTipoViaMedicamento() {
        initialize();
    }

    /**
     * Constructor for primary key
     */
    public BaseTipoViaMedicamento(java.lang.Long codigo) {
        this.setCodigo(codigo);
        initialize();
    }

    /**
     * Constructor for required fields
     */
    public BaseTipoViaMedicamento(
            java.lang.Long codigo,
            java.lang.String descricao) {

        this.setCodigo(codigo);
        this.setDescricao(descricao);
        initialize();
    }

    protected void initialize() {
    }

    private int hashCode = Integer.MIN_VALUE;

    // primary key
    private java.lang.Long codigo;

    // fields
    private java.lang.String referencia;
    private java.lang.String descricao;
    private java.lang.String sigla;
    private java.lang.Long ordem;

    /**
     * Return the unique identifier of this class
     *
     * @hibernate.id generator-class="assigned" column="cd_tp_via_medicamento"
     */
    public java.lang.Long getCodigo() {
        return getPropertyValue(this, codigo, "codigo");
    }

    /**
     * Set the unique identifier of this class
     *
     * @param codigo the new ID
     */
    public void setCodigo(java.lang.Long codigo) {
        this.codigo = codigo;
        this.hashCode = Integer.MIN_VALUE;
    }

    /**
     * Return the value associated with the column: referencia
     */
    public java.lang.String getReferencia() {
        return getPropertyValue(this, referencia, PROP_REFERENCIA);
    }

    /**
     * Set the value related to the column: referencia
     *
     * @param referencia the referencia value
     */
    public void setReferencia(java.lang.String referencia) {
//        java.lang.String referenciaOld = this.referencia;
        this.referencia = referencia;
//        this.getPropertyChangeSupport().firePropertyChange ("referencia", referenciaOld, referencia);
    }

    /**
     * Return the value associated with the column: ds_via
     */
    public java.lang.String getDescricao() {
        return getPropertyValue(this, descricao, PROP_DESCRICAO);
    }

    /**
     * Set the value related to the column: ds_via
     *
     * @param descricao the ds_via value
     */
    public void setDescricao(java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
        this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
    }

    /**
     * Return the value associated with the column: sigla
     */
    public java.lang.String getSigla() {
        return getPropertyValue(this, sigla, PROP_SIGLA);
    }

    /**
     * Set the value related to the column: sigla
     *
     * @param sigla the sigla value
     */
    public void setSigla(java.lang.String sigla) {
//        java.lang.String siglaOld = this.sigla;
        this.sigla = sigla;
//        this.getPropertyChangeSupport().firePropertyChange ("sigla", siglaOld, sigla);
    }

    /**
     * Return the value associated with the column: ordem
     */
    public java.lang.Long getOrdem() {
        return getPropertyValue(this, ordem, PROP_ORDEM);
    }

    /**
     * Set the value related to the column: ordem
     *
     * @param ordem the ordem value
     */
    public void setOrdem(java.lang.Long ordem) {
//        java.lang.Long ordemOld = this.ordem;
        this.ordem = ordem;
//        this.getPropertyChangeSupport().firePropertyChange ("ordem", ordemOld, ordem);
    }

    public boolean equals(Object obj) {
        if (null == obj) {
            return false;
        }
        if (!(obj instanceof br.com.ksisolucoes.vo.entradas.estoque.TipoViaMedicamento)) {
            return false;
        } else {
            br.com.ksisolucoes.vo.entradas.estoque.TipoViaMedicamento tipoViaMedicamento = (br.com.ksisolucoes.vo.entradas.estoque.TipoViaMedicamento) obj;
            if (null == this.getCodigo() || null == tipoViaMedicamento.getCodigo()) {
                return false;
            } else {
                return (this.getCodigo().equals(tipoViaMedicamento.getCodigo()));
            }
        }
    }

    public int hashCode() {
        if (Integer.MIN_VALUE == this.hashCode) {
            if (null == this.getCodigo()) {
                return super.hashCode();
            } else {
                String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
                this.hashCode = hashStr.hashCode();
            }
        }
        return this.hashCode;
    }

    public String toString() {
        return super.toString();
    }

    private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
        if (this.retornoValidacao == null) {
            this.retornoValidacao = new RetornoValidacao();
        }
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
        this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}
