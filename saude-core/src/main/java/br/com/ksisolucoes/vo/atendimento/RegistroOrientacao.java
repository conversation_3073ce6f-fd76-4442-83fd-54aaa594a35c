package br.com.ksisolucoes.vo.atendimento;

import java.io.Serializable;

import br.com.ksisolucoes.vo.atendimento.base.BaseRegistroOrientacao;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class RegistroOrientacao extends BaseRegistroOrientacao implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RegistroOrientacao () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RegistroOrientacao (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RegistroOrientacao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.Profissional profissional,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.String descricaoOrientado,
		java.util.Date dataOrientacao,
		java.lang.String orientacao,
		java.util.Date dataCadastro) {

		super (
			codigo,
			profissional,
			usuario,
			descricaoOrientado,
			dataOrientacao,
			orientacao,
			dataCadastro);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}