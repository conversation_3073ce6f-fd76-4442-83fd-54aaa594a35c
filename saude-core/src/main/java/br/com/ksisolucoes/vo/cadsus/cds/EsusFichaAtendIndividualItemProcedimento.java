package br.com.ksisolucoes.vo.cadsus.cds;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.cadsus.cds.base.BaseEsusFichaAtendIndividualItemProcedimento;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import java.io.Serializable;

public class EsusFichaAtendIndividualItemProcedimento extends BaseEsusFichaAtendIndividualItemProcedimento implements CodigoManager {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public EsusFichaAtendIndividualItemProcedimento() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public EsusFichaAtendIndividualItemProcedimento(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public EsusFichaAtendIndividualItemProcedimento(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendIndividualItem esusFichaAtendIndividualItem,
            br.com.ksisolucoes.vo.controle.Usuario usuario,
            java.util.Date dataCadastro) {

        super(
                codigo,
                esusFichaAtendIndividualItem,
                usuario,
                dataCadastro);
    }

    /*[CONSTRUCTOR MARKER END]*/
    public static enum AvaliadoSolicitado implements IEnum<AvaliadoSolicitado> {

        AVALIADO(1L, Bundle.getStringApplication("rotulo_avaliado")),
        SOLICITADO(2L, Bundle.getStringApplication("rotulo_solicitado"));

        private Long value;
        private String descricao;

        private AvaliadoSolicitado(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static AvaliadoSolicitado valeuOf(Long value) {
            for (AvaliadoSolicitado avaliadoSolicitado : AvaliadoSolicitado.values()) {
                if (avaliadoSolicitado.value().equals(value)) {
                    return avaliadoSolicitado;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }

    public String getAvaliadoSolicitadoFormatado() {
        if (AvaliadoSolicitado.AVALIADO.value().equals(this.getSolicitadoAvaliado())) {
            return Bundle.getStringApplication("rotulo_avaliado");
        } else if (AvaliadoSolicitado.SOLICITADO.value().equals(this.getSolicitadoAvaliado())) {
            return Bundle.getStringApplication("rotulo_solicitado");
        } else {
            return "";
        }
    }

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}
