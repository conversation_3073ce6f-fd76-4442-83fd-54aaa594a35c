<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.esus">
    <class name="TermoRecusaCadastroIndividual" table="termo_recusa_cad_individual">

        <id name="codigo" type="java.lang.Long" column="cd_termo_rec_cad_ind">
            <generator class="assigned"/>
        </id>

        <version column="version" name="version" type="long"/>

        <property column="nome" length="70" name="nome" type="java.lang.String"/>

        <property column="nm_social" length="50" name="nomeSocial" type="java.lang.String"/>

        <property column="sexo" length="1" name="sexo" type="java.lang.String"/>

        <property column="dt_nascimento" name="dataNascimento" type="date"/>

        <property column="nm_mae" length="70" name="nomeMae" type="java.lang.String"/>

        <property column="mae_desconhecido" name="maeDesconhecido" type="java.lang.Long"/>

        <property column="nm_pai" length="70" name="nomePai" type="java.lang.String"/>

        <property column="pai_desconhecido" name="paiDesconhecido" type="java.lang.Long"/>

        <property column="nacionalidade" name="nacionalidade" type="java.lang.Long"/>

        <many-to-one class="br.com.ksisolucoes.vo.basico.Cidade" column="cod_cid_nascimento" name="cidadeNascimento"/>

        <many-to-one class="br.com.ksisolucoes.vo.cadsus.Raca" column="cd_raca" name="raca"/>

        <many-to-one class="br.com.ksisolucoes.vo.cadsus.EtniaIndigena" column="cd_etnia" name="etniaIndigena"/>

        <many-to-one class="br.com.ksisolucoes.vo.basico.Pais" column="cd_pais_nascimento" name="paisNascimento"/>

        <property column="dt_naturalizado" name="dataNaturalizado" type="java.util.Date"/>

        <property column="dt_entrada_brasil" name="dataEntradaBrasil" type="java.util.Date"/>

        <property column="portaria_naturalizacao" name="portariaNaturalizacao" type="java.lang.String"/>

        <property column="flag_fora_area" name="flagForaArea" type="java.lang.Long"/>

        <property column="celular" name="celular" length="15" type="java.lang.String"/>

        <property column="email" length="100" name="email" type="java.lang.String"/>

        <property column="nis" name="nis" length="20" type="java.lang.String"/>

        <property name="cns" type="java.lang.Long" column="cns"/>

        <property name="cnsResponsavel" type="java.lang.Long" column="cns_responsavel"/>

        <property column="responsavel" name="responsavel" type="java.lang.Long"/>

        <many-to-one class="br.com.ksisolucoes.vo.basico.Empresa" column="empresa" name="empresa" />

        <many-to-one class="br.com.ksisolucoes.vo.cadsus.Profissional" column="cd_profissional" name="profissional" />

        <many-to-one class="br.com.ksisolucoes.vo.basico.EquipeMicroArea" column="cd_eqp_micro_area" name="equipeMicroArea"/>
        
        <many-to-one class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo" column="cd_cbo" name="tabelaCbo"/>
        
        <many-to-one class="br.com.ksisolucoes.vo.basico.EquipeProfissional" column="cd_equipe_profissional" name="equipeProfissional"/>

        <many-to-one class="br.com.ksisolucoes.vo.controle.Usuario" column="cd_usuario" name="usuario"/>

        <property column="dt_preenchimento" name="dataPreenchimento" not-null="true" type="java.util.Date"/>

    </class>
</hibernate-mapping>
