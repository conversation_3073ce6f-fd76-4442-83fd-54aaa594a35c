<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.requerimentos"  >
    <class name="RequerimentoDefesaPrevia" table="requerimento_defesa_previa" >
        <id
            column="cd_requerimento_defesa_previa"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />    
        
        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia"
            column="cd_req_vigilancia"
            name="requerimentoVigilancia"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.Estabelecimento"
            column="cd_estabelecimento"
            name="estabelecimento"
            not-null="false"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco"
            column="cd_vigilancia_endereco"
            name="vigilanciaEndereco"
            not-null="false"
        />
        
        <property
            column="nome"
            name="nome"
            not-null="false"
            type="java.lang.String"
            length="80"
        />
        
        <property
            column="cnpj_cpf"
            name="cnpjCpf"
            not-null="false"
            type="java.lang.String"
            length="15"
        />
        
        <property
            column="telefone"
            name="telefone"
            not-null="false"
            type="java.lang.String"
            length="15"
        />
        
        <property
            column="num_auto_infracao"
            name="numeroAutoInfracao"
            not-null="true"
            type="java.lang.String"
            length="30"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuarioCadastro"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario_can"
            name="usuarioCancelamento"
            not-null="false"
        />
        
        <property 
            name="dataCadastro"
            column="dt_cadastro"
            type="timestamp"
            not-null="true"
        />
        
        <property 
            name="dataCancelamento"
            column="dt_cancelamento"
            type="timestamp"
            not-null="false"
        />
        
<!--        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.SetorVigilancia"
            column="cd_setor_vigilancia"
            not-null="false"
            name="setorVigilancia"
        />-->

        <property
            name="dataAuto"
            column="data_auto"
            type="timestamp"
            not-null="true"
        />

        <property
            name="razoes"
            column="razoes"
            type="java.lang.String"
            not-null="false"
        />
        
    </class>
</hibernate-mapping>
