package br.com.ksisolucoes.vo.basico.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the microrregiao table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="microrregiao"
 */

public abstract class BaseMicrorregiao extends BaseRootVO implements Serializable {

	public static String REF = "Microrregiao";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_REGIONAL_SAUDE = "regionalSaude";


	// constructors
	public BaseMicrorregiao () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseMicrorregiao (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseMicrorregiao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.RegionalSaude regionalSaude,
		java.lang.String descricao) {

		this.setCodigo(codigo);
		this.setRegionalSaude(regionalSaude);
		this.setDescricao(descricao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;

	// many to one
	private br.com.ksisolucoes.vo.basico.RegionalSaude regionalSaude;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_microrregiao"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: descricao
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: descricao
	 * @param descricao the descricao value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: cd_regional
	 */
	public br.com.ksisolucoes.vo.basico.RegionalSaude getRegionalSaude () {
		return getPropertyValue(this, regionalSaude, PROP_REGIONAL_SAUDE); 
	}

	/**
	 * Set the value related to the column: cd_regional
	 * @param regionalSaude the cd_regional value
	 */
	public void setRegionalSaude (br.com.ksisolucoes.vo.basico.RegionalSaude regionalSaude) {
//        br.com.ksisolucoes.vo.basico.RegionalSaude regionalSaudeOld = this.regionalSaude;
		this.regionalSaude = regionalSaude;
//        this.getPropertyChangeSupport().firePropertyChange ("regionalSaude", regionalSaudeOld, regionalSaude);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.Microrregiao)) return false;
		else {
			br.com.ksisolucoes.vo.basico.Microrregiao microrregiao = (br.com.ksisolucoes.vo.basico.Microrregiao) obj;
			if (null == this.getCodigo() || null == microrregiao.getCodigo()) return false;
			else return (this.getCodigo().equals(microrregiao.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}