package br.com.ksisolucoes.vo.prontuario.procedimento;

import br.com.ksisolucoes.associacao.annotations.IdNameSIGTAP;
import br.com.ksisolucoes.vo.prontuario.procedimento.base.BaseProcedimentoRegraCondicionadaPK;

public class ProcedimentoRegraCondicionadaPK extends BaseProcedimentoRegraCondicionadaPK {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ProcedimentoRegraCondicionadaPK () {}
	
	public ProcedimentoRegraCondicionadaPK (
		br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegraCondicionadaCadastro procedimentoRegraCondicionadaCadastro,
		br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento) {

		super (
			procedimentoRegraCondicionadaCadastro,
			procedimento);
	}
/*[CONSTRUCTOR MARKER END]*/

	@IdNameSIGTAP
	@Override
	public Procedimento getProcedimento() {
		return super.getProcedimento();
	}

	@IdNameSIGTAP
	@Override
	public ProcedimentoRegraCondicionadaCadastro getProcedimentoRegraCondicionadaCadastro() {
		return super.getProcedimentoRegraCondicionadaCadastro();
	}

	@Override
	public void setProcedimento(Procedimento procedimento) {
		super.setProcedimento(procedimento);
	}

	@Override
	public void setProcedimentoRegraCondicionadaCadastro(ProcedimentoRegraCondicionadaCadastro procedimentoRegraCondicionadaCadastro) {
		super.setProcedimentoRegraCondicionadaCadastro(procedimentoRegraCondicionadaCadastro);
	}

}