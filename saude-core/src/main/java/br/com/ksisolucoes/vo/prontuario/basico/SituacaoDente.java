package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.util.parametrogem.ParametroPanelConsultaBean;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseSituacaoDente;

import java.io.Serializable;


@ParametroPanelConsultaBean("br.com.celk.view.prontuario.situacaodente.autocomplete.AutoCompleteConsultaSituacaoDente")
public class SituacaoDente extends BaseSituacaoDente implements PesquisaObjectInterface, CodigoManager {
    private static final long serialVersionUID = 1L;

    public static final String PROP_DESCRICAO_FORMATADO = "descricaoFormatado";
    public static final String PROP_DESCRICAO_FACE_FORMATADO = "descricaoFaceFormatado";
    public static final String PROP_DESCRICAO_TIPO_SITUACAO = "descricaoTipoSituacao";

    public enum TipoSituacao implements IEnum {
        HISTORICO(0L, Bundle.getStringApplication("rotulo_historico")),
        PROCEDIMENTO(1L, Bundle.getStringApplication("rotulo_procedimento"));

        private Long value;
        private String descricao;

        private TipoSituacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static TipoSituacao valueOf(Long value) {
            for (TipoSituacao tipoSituacao : TipoSituacao.values()) {
                if (tipoSituacao.value().equals(value)) {
                    return tipoSituacao;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }

    public enum TipoAplicacao implements IEnum {
        COROA(1L, Bundle.getStringApplication("rotulo_coroa")),
        RAIZ(2L, Bundle.getStringApplication("rotulo_raiz"));

        private Long value;
        private String descricao;

        private TipoAplicacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static TipoAplicacao valueOf(Long value) {
            for (TipoAplicacao tipoAplicacao : TipoAplicacao.values()) {
                if (tipoAplicacao.value().equals(value)) {
                    return tipoAplicacao;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }

    public enum TipoPreenchimento implements IEnum {
        COMPLETO(1L, Bundle.getStringApplication("rotulo_completo")),
        COLO(2L, Bundle.getStringApplication("rotulo_colo")),
        RAIZ(3L, Bundle.getStringApplication("rotulo_raiz"));

        private Long value;
        private String descricao;

        private TipoPreenchimento(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static TipoPreenchimento valueOf(Long value) {
            for (TipoPreenchimento tipoPreenchimento : TipoPreenchimento.values()) {
                if (tipoPreenchimento.value().equals(value)) {
                    return tipoPreenchimento;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }

    public static enum IndiceCPOD implements IEnum<IndiceCPOD> {

        CARIADO(1L, Bundle.getStringApplication("rotulo_cariado")),
        PERDIDO(2L, Bundle.getStringApplication("rotulo_perdido")),
        OBTURADO_RESTAURADO(4L, Bundle.getStringApplication("rotulo_obturado_restaurado"));

        private Long value;
        private String descricao;

        private IndiceCPOD(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static IndiceCPOD valeuOf(Long value) {
            for (IndiceCPOD indiceCPOD : IndiceCPOD.values()) {
                if (indiceCPOD.value().equals(value)) {
                    return indiceCPOD;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public SituacaoDente() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public SituacaoDente(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public SituacaoDente(
            java.lang.Long codigo,
            java.lang.String descricao,
            java.lang.String referencia,
            java.lang.Long tipoSituacao) {

        super(
                codigo,
                descricao,
                referencia,
                tipoSituacao);
    }

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    @Override
    public String getDescricaoVO() {
        return this.getDescricao();
    }

    @Override
    public String getIdentificador() {
        return this.getCodigo().toString();
    }

    public String getDescricaoFormatado() {
        return Util.getDescricaoFormatado(getReferencia(), getDescricao());
    }

    public String getDescricaoTipoSituacao() {
        TipoSituacao tipoSituacao = TipoSituacao.valueOf(getTipoSituacao());
        if (tipoSituacao != null && tipoSituacao.descricao != null) {
            return tipoSituacao.descricao();
        }
        return "";
    }
}
