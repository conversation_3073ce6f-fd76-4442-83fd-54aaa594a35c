package br.com.ksisolucoes.vo.vigilancia.taxa.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the taxa_indice table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="taxa_indice"
 */

public abstract class BaseTaxaIndice extends BaseRootVO implements Serializable {

	public static String REF = "TaxaIndice";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_VALOR_INDICE = "valorIndice";
	public static final String PROP_TAXA = "taxa";
	public static final String PROP_DATA_INICIO_VIGENCIA = "dataInicioVigencia";


	// constructors
	public BaseTaxaIndice () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseTaxaIndice (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseTaxaIndice (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.taxa.Taxa taxa,
		java.util.Date dataInicioVigencia,
		java.lang.Double valorIndice) {

		this.setCodigo(codigo);
		this.setTaxa(taxa);
		this.setDataInicioVigencia(dataInicioVigencia);
		this.setValorIndice(valorIndice);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataInicioVigencia;
	private java.lang.Double valorIndice;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.taxa.Taxa taxa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_taxa_indice"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_inicio_vigencia
	 */
	public java.util.Date getDataInicioVigencia () {
		return getPropertyValue(this, dataInicioVigencia, PROP_DATA_INICIO_VIGENCIA); 
	}

	/**
	 * Set the value related to the column: dt_inicio_vigencia
	 * @param dataInicioVigencia the dt_inicio_vigencia value
	 */
	public void setDataInicioVigencia (java.util.Date dataInicioVigencia) {
//        java.util.Date dataInicioVigenciaOld = this.dataInicioVigencia;
		this.dataInicioVigencia = dataInicioVigencia;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInicioVigencia", dataInicioVigenciaOld, dataInicioVigencia);
	}



	/**
	 * Return the value associated with the column: vl_indice
	 */
	public java.lang.Double getValorIndice () {
		return getPropertyValue(this, valorIndice, PROP_VALOR_INDICE); 
	}

	/**
	 * Set the value related to the column: vl_indice
	 * @param valorIndice the vl_indice value
	 */
	public void setValorIndice (java.lang.Double valorIndice) {
//        java.lang.Double valorIndiceOld = this.valorIndice;
		this.valorIndice = valorIndice;
//        this.getPropertyChangeSupport().firePropertyChange ("valorIndice", valorIndiceOld, valorIndice);
	}



	/**
	 * Return the value associated with the column: cd_taxa
	 */
	public br.com.ksisolucoes.vo.vigilancia.taxa.Taxa getTaxa () {
		return getPropertyValue(this, taxa, PROP_TAXA); 
	}

	/**
	 * Set the value related to the column: cd_taxa
	 * @param taxa the cd_taxa value
	 */
	public void setTaxa (br.com.ksisolucoes.vo.vigilancia.taxa.Taxa taxa) {
//        br.com.ksisolucoes.vo.vigilancia.taxa.Taxa taxaOld = this.taxa;
		this.taxa = taxa;
//        this.getPropertyChangeSupport().firePropertyChange ("taxa", taxaOld, taxa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.taxa.TaxaIndice)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.taxa.TaxaIndice taxaIndice = (br.com.ksisolucoes.vo.vigilancia.taxa.TaxaIndice) obj;
			if (null == this.getCodigo() || null == taxaIndice.getCodigo()) return false;
			else return (this.getCodigo().equals(taxaIndice.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}