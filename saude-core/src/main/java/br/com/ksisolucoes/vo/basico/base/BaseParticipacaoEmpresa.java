package br.com.ksisolucoes.vo.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the participacao_empresa table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="participacao_empresa"
 */

public abstract class BaseParticipacaoEmpresa extends BaseRootVO implements Serializable {

	public static String REF = "ParticipacaoEmpresa";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_PERCENTUAL = "percentual";
	public static final String PROP_PESSOA = "pessoa";
	public static final String PROP_NOME_EMPRESA = "nomeEmpresa";


	// constructors
	public BaseParticipacaoEmpresa () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseParticipacaoEmpresa (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseParticipacaoEmpresa (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Pessoa pessoa,
		java.lang.Double percentual,
		java.lang.String nomeEmpresa) {

		this.setCodigo(codigo);
		this.setPessoa(pessoa);
		this.setPercentual(percentual);
		this.setNomeEmpresa(nomeEmpresa);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Double percentual;
	private java.lang.String nomeEmpresa;

	// many to one
	private br.com.ksisolucoes.vo.basico.Pessoa pessoa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="sequencial"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: perc_participacao
	 */
	public java.lang.Double getPercentual () {
		return getPropertyValue(this, percentual, PROP_PERCENTUAL); 
	}

	/**
	 * Set the value related to the column: perc_participacao
	 * @param percentual the perc_participacao value
	 */
	public void setPercentual (java.lang.Double percentual) {
//        java.lang.Double percentualOld = this.percentual;
		this.percentual = percentual;
//        this.getPropertyChangeSupport().firePropertyChange ("percentual", percentualOld, percentual);
	}



	/**
	 * Return the value associated with the column: ds_empresa
	 */
	public java.lang.String getNomeEmpresa () {
		return getPropertyValue(this, nomeEmpresa, PROP_NOME_EMPRESA); 
	}

	/**
	 * Set the value related to the column: ds_empresa
	 * @param nomeEmpresa the ds_empresa value
	 */
	public void setNomeEmpresa (java.lang.String nomeEmpresa) {
//        java.lang.String nomeEmpresaOld = this.nomeEmpresa;
		this.nomeEmpresa = nomeEmpresa;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeEmpresa", nomeEmpresaOld, nomeEmpresa);
	}



	/**
	 * Return the value associated with the column: cod_pessoa
	 */
	public br.com.ksisolucoes.vo.basico.Pessoa getPessoa () {
		return getPropertyValue(this, pessoa, PROP_PESSOA); 
	}

	/**
	 * Set the value related to the column: cod_pessoa
	 * @param pessoa the cod_pessoa value
	 */
	public void setPessoa (br.com.ksisolucoes.vo.basico.Pessoa pessoa) {
//        br.com.ksisolucoes.vo.basico.Pessoa pessoaOld = this.pessoa;
		this.pessoa = pessoa;
//        this.getPropertyChangeSupport().firePropertyChange ("pessoa", pessoaOld, pessoa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.ParticipacaoEmpresa)) return false;
		else {
			br.com.ksisolucoes.vo.basico.ParticipacaoEmpresa participacaoEmpresa = (br.com.ksisolucoes.vo.basico.ParticipacaoEmpresa) obj;
			if (null == this.getCodigo() || null == participacaoEmpresa.getCodigo()) return false;
			else return (this.getCodigo().equals(participacaoEmpresa.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}