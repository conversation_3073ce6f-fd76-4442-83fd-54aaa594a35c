package br.com.ksisolucoes.vo.cadsus;

import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.cadsus.base.BaseUsuarioCadsusOcorrencia;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;



public class UsuarioCadsusOcorrencia extends BaseUsuarioCadsusOcorrencia implements CodigoManager {
	private static final long serialVersionUID = 1L;

        public static final String PROP_DATA_HORA = "dataHora";

/*[CONSTRUCTOR MARKER BEGIN]*/
	public UsuarioCadsusOcorrencia () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public UsuarioCadsusOcorrencia (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public UsuarioCadsusOcorrencia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		br.com.ksisolucoes.vo.cadsus.TipoOcorrencia tipoOcorrencia,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.basico.ProdutoSolicitado produtoSolicitado,
		java.util.Date dataOcorrencia,
		java.lang.String descricao) {

		super (
			codigo,
			usuarioCadsus,
			tipoOcorrencia,
			usuario,
			produtoSolicitado,
			dataOcorrencia,
			descricao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getDataHora(){
        return Data.formatarDataHora(getDataOcorrencia());
    }
}