package br.com.ksisolucoes.vo.materiais.smar.service;

import br.com.celk.util.StringUtil;
import br.com.celk.util.Util;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.rest.WebserviceResponse;
import br.com.ksisolucoes.util.rest.WebserviceUtil;
import br.com.ksisolucoes.util.validacao.CpfCnpJValidator;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.materiais.smar.auth.AutenticacaoSmar;
import br.com.ksisolucoes.vo.materiais.smar.brm.IntegracaoRecebimentoBrmSmar;
import br.com.ksisolucoes.vo.materiais.smar.rme.RequisicaoMateriaisEstoque;
import br.com.ksisolucoes.vo.materiais.smar.brm.BoletimRecebimentoMaterial;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.text.MessageFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class SmarService {

    //region Endpoints
    private static final String AUTENTICACAO = "/api/autenticacao";
    private static final String LISTAR_ID_NOTAS_ENTRADA = "/api/brm";
    private static final String BUSCAR_RECEBIMENTO_MATERIAL_POR_ID = "/api/brm/{0}";
    private static final String GERAR_REQUISICAO_MATERIAIS = "/api/rme";
    //endregion

    //region Requisições API
    public BoletimRecebimentoMaterial carregarNota(Long id) throws ValidacaoException {
        // Carrega url
        String url = getUrl() + MessageFormat.format(BUSCAR_RECEBIMENTO_MATERIAL_POR_ID, id.toString());
        Map<String, String> header = generateAuthorizationHeader();

        try {
            WebserviceResponse webserviceResponse = new WebserviceUtil().sendGetRequest(url, header);
            JSONObject response = new JSONObject(webserviceResponse.getResponseMessage());

            Gson gson = new Gson();

            BoletimRecebimentoMaterial brm =  gson.fromJson(response.toString(), BoletimRecebimentoMaterial.class);

            validacaoBrm(brm);

            IntegracaoRecebimentoBrmSmar integracaoRecebimentoBrmSmar = new IntegracaoRecebimentoBrmSmar();
            integracaoRecebimentoBrmSmar.setCodigo(brm.getIdBrm());
            integracaoRecebimentoBrmSmar.setDataIntegracao(new Date());
            integracaoRecebimentoBrmSmar.setRespostaJson(webserviceResponse.getResponseMessage());
            integracaoRecebimentoBrmSmar.setRespostaStatus((long) webserviceResponse.getStatusCode());
            BOFactory.save(integracaoRecebimentoBrmSmar);

            return brm;

        } catch (JSONException | DAOException e) {
            throw new ValidacaoException("Ocorreu um erro ao executar a integração com o sistema da Smar.");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private void validacaoBrm(BoletimRecebimentoMaterial brm) throws ValidacaoException{
        if (brm.getItens() == null || brm.getItens().isEmpty()) throw new ValidacaoException("Não foi possível carregar os itens do boletim de recebimento de material");
        if (brm.getFornecedor() == null) throw new ValidacaoException("Não foi possível carregar o fornecedor do boletim de recebimento de material");
        if (brm.getFornecedor().getDocumento() == null || brm.getFornecedor().getDocumento().isEmpty()) throw new ValidacaoException("Não foi possível carregar o documento do fornecedor do boletim de recebimento de material");

        String cpfCnpjDigits = StringUtils.trimToNull(StringUtil.getDigits(brm.getFornecedor().getDocumento()));
        if (Util.isCpf(cpfCnpjDigits) && !CpfCnpJValidator.CPFIsValid(cpfCnpjDigits)) {
            throw new ValidacaoException("CPF Inválido! " + cpfCnpjDigits);
        } else if (Util.isCnpj(cpfCnpjDigits) && !CpfCnpJValidator.CNPJIsValid(cpfCnpjDigits)) {
            throw new ValidacaoException("CNPJ Inválido! " + cpfCnpjDigits);
        }
    }

    public WebserviceResponse enviarRequisicaoMateriais(RequisicaoMateriaisEstoque rme) throws ValidacaoException {
        // Carrega url
        String url = getUrl() + GERAR_REQUISICAO_MATERIAIS;
        Map<String, String> header = generateAuthorizationHeader();

        try {
            return new WebserviceUtil(false).sendPostRequest(url, rme, header);
        } catch (IOException e) {
            throw new ValidacaoException(e);
        }
    }
    //endregion

    //region Autenticação e Url
    private Map generateAuthorizationHeader() throws ValidacaoException {
        // Carrega url
        String url = getUrl() + AUTENTICACAO;

        // Carrega informações
        String usuario = getUsuario();
        String apiKey = getApiKey();
        String chave = getChave();

        // Cria o body
        JSONObject body = new JSONObject();
        try {
            body.put("Usuario", usuario);
            body.put("Chave", chave);
        } catch (JSONException e) {
            throw new ValidacaoException("Não foi possível gerar o corpo de envio da requisição");
        }

        AutenticacaoSmar autenticacaoSmar = new AutenticacaoSmar();
        autenticacaoSmar.setChave(chave);
        autenticacaoSmar.setUsuario(usuario);

        // Cria o header
        Map<String, String> headers = new HashMap<>();
        headers.put("apiKey", apiKey);

        // Envia a requisição
        try {
            WebserviceResponse webserviceResponse = new WebserviceUtil(false, true, false, false).sendPostRequest(url, autenticacaoSmar, headers);
            JSONObject response = new JSONObject(webserviceResponse.getResponseMessage());

            Map<String, String> map = new HashMap<>();
            map.put("Authorization", "Bearer " + response.getString("token"));
            return map;
        } catch (IOException e) {
            throw new ValidacaoException("Não foi possível gerar o token de autenticação");
        } catch (JSONException e) {
            throw new ValidacaoException("Não foi possível ler a resposta da requisição");
        }
    }

    private String getUrl() throws ValidacaoException {
        try {
            String ambiente = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("urlSmartUtilizada");
            String url;

            if (ambiente.equalsIgnoreCase("h")) {
                url = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("urlSmarHomologacao");
            } else {
                url = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("urlSmarProducao");
            }

            if (url.endsWith("/")) {
                url = url.substring(0, url.length() - 1);
            }

            return url;
        } catch (Exception e) {
            throw new ValidacaoException("Não foi possível carregar a url. Verifique o parâmetro GEM");
        }
    }

    private String getUsuario() throws ValidacaoException {
        try {
            return BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("usuarioSmar");
        } catch (Exception e) {
            throw new ValidacaoException("Não foi possível carregar o usuário. Verifique o parâmetro GEM");
        }
    }

    private String getChave() throws ValidacaoException {
        try {
            return BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("chaveSmar");
        } catch (Exception e) {
            throw new ValidacaoException("Não foi possível carregar a chave. Verifique o parâmetro GEM");
        }
    }

    private String getApiKey() throws ValidacaoException {
        try {
            return BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("apiKeySmar");
        } catch (Exception e) {
            throw new ValidacaoException("Não foi possível carregar a Api Key. Verifique o parâmetro GEM");
        }
    }
    //endregion
}
