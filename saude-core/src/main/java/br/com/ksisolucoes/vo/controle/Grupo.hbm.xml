<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.controle"  >
	<class 
		name="Grupo"
		table="grupos"
	>
		<id
			name="codigo"
			type="java.lang.Long"
			column="cd_grupo"
		>
                    <generator class="sequence">
                        <param name="sequence">seq_grupos</param>
                    </generator>
		</id> <version column="version" name="version" type="long" />

		<property
			name="nome"
			column="nm_grupo"
			type="string"
			not-null="false"
		/>

		<property
			name="utilidade"
			column="utilidade"
			type="java.lang.String"
			not-null="false"
		/>
	
	</class>
</hibernate-mapping>