<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.taxa">
    <class name="Taxa" table="taxa">
        <id
                column="cd_taxa"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="assigned"/>
        </id>
        <version column="version" name="version" type="long"/>

        <property
                column="descricao"
                name="descricao"
                not-null="true"
                type="java.lang.String"
        />

        <property
                column="dt_cadastro"
                name="dataCadastro"
                not-null="true"
                type="java.util.Date"
        />

        <property
                column="dt_usuario"
                name="dataUsuario"
                not-null="true"
                type="java.util.Date"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario"
                not-null="true"
                name="usuario"
        />

    </class>
</hibernate-mapping>
