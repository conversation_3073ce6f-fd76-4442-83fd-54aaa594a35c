package br.com.ksisolucoes.vo.vigilancia.requerimentos.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the requerimento_baixa_responsabilidade table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="requerimento_baixa_responsabilidade"
 */

public abstract class BaseRequerimentoBaixaResponsabilidade extends BaseRootVO implements Serializable {

	public static String REF = "RequerimentoBaixaResponsabilidade";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_NUMERACAO = "numeracao";
	public static final String PROP_ESTABELECIMENTO = "estabelecimento";
	public static final String PROP_RESPONSAVEL_TECNICO = "responsavelTecnico";
	public static final String PROP_ESTABELECIMENTO_SETORES = "estabelecimentoSetores";
	public static final String PROP_REQUERIMENTO_VIGILANCIA = "requerimentoVigilancia";


	// constructors
	public BaseRequerimentoBaixaResponsabilidade () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseRequerimentoBaixaResponsabilidade (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseRequerimentoBaixaResponsabilidade (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia,
		br.com.ksisolucoes.vo.vigilancia.ResponsavelTecnico responsavelTecnico,
		br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimento) {

		this.setCodigo(codigo);
		this.setRequerimentoVigilancia(requerimentoVigilancia);
		this.setResponsavelTecnico(responsavelTecnico);
		this.setEstabelecimento(estabelecimento);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long numeracao;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia;
	private br.com.ksisolucoes.vo.vigilancia.ResponsavelTecnico responsavelTecnico;
	private br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimento;
	private br.com.ksisolucoes.vo.vigilancia.EstabelecimentoSetores estabelecimentoSetores;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_requerimento_baixa"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: numeracao
	 */
	public java.lang.Long getNumeracao () {
		return getPropertyValue(this, numeracao, PROP_NUMERACAO); 
	}

	/**
	 * Set the value related to the column: numeracao
	 * @param numeracao the numeracao value
	 */
	public void setNumeracao (java.lang.Long numeracao) {
//        java.lang.Long numeracaoOld = this.numeracao;
		this.numeracao = numeracao;
//        this.getPropertyChangeSupport().firePropertyChange ("numeracao", numeracaoOld, numeracao);
	}



	/**
	 * Return the value associated with the column: cd_req_vigilancia
	 */
	public br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia getRequerimentoVigilancia () {
		return getPropertyValue(this, requerimentoVigilancia, PROP_REQUERIMENTO_VIGILANCIA); 
	}

	/**
	 * Set the value related to the column: cd_req_vigilancia
	 * @param requerimentoVigilancia the cd_req_vigilancia value
	 */
	public void setRequerimentoVigilancia (br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia) {
//        br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilanciaOld = this.requerimentoVigilancia;
		this.requerimentoVigilancia = requerimentoVigilancia;
//        this.getPropertyChangeSupport().firePropertyChange ("requerimentoVigilancia", requerimentoVigilanciaOld, requerimentoVigilancia);
	}



	/**
	 * Return the value associated with the column: cd_resp_tecnico
	 */
	public br.com.ksisolucoes.vo.vigilancia.ResponsavelTecnico getResponsavelTecnico () {
		return getPropertyValue(this, responsavelTecnico, PROP_RESPONSAVEL_TECNICO); 
	}

	/**
	 * Set the value related to the column: cd_resp_tecnico
	 * @param responsavelTecnico the cd_resp_tecnico value
	 */
	public void setResponsavelTecnico (br.com.ksisolucoes.vo.vigilancia.ResponsavelTecnico responsavelTecnico) {
//        br.com.ksisolucoes.vo.vigilancia.ResponsavelTecnico responsavelTecnicoOld = this.responsavelTecnico;
		this.responsavelTecnico = responsavelTecnico;
//        this.getPropertyChangeSupport().firePropertyChange ("responsavelTecnico", responsavelTecnicoOld, responsavelTecnico);
	}



	/**
	 * Return the value associated with the column: cd_estabelecimento
	 */
	public br.com.ksisolucoes.vo.vigilancia.Estabelecimento getEstabelecimento () {
		return getPropertyValue(this, estabelecimento, PROP_ESTABELECIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_estabelecimento
	 * @param estabelecimento the cd_estabelecimento value
	 */
	public void setEstabelecimento (br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimento) {
//        br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimentoOld = this.estabelecimento;
		this.estabelecimento = estabelecimento;
//        this.getPropertyChangeSupport().firePropertyChange ("estabelecimento", estabelecimentoOld, estabelecimento);
	}



	/**
	 * Return the value associated with the column: cd_estabelecimento_setores
	 */
	public br.com.ksisolucoes.vo.vigilancia.EstabelecimentoSetores getEstabelecimentoSetores () {
		return getPropertyValue(this, estabelecimentoSetores, PROP_ESTABELECIMENTO_SETORES); 
	}

	/**
	 * Set the value related to the column: cd_estabelecimento_setores
	 * @param estabelecimentoSetores the cd_estabelecimento_setores value
	 */
	public void setEstabelecimentoSetores (br.com.ksisolucoes.vo.vigilancia.EstabelecimentoSetores estabelecimentoSetores) {
//        br.com.ksisolucoes.vo.vigilancia.EstabelecimentoSetores estabelecimentoSetoresOld = this.estabelecimentoSetores;
		this.estabelecimentoSetores = estabelecimentoSetores;
//        this.getPropertyChangeSupport().firePropertyChange ("estabelecimentoSetores", estabelecimentoSetoresOld, estabelecimentoSetores);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoBaixaResponsabilidade)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoBaixaResponsabilidade requerimentoBaixaResponsabilidade = (br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoBaixaResponsabilidade) obj;
			if (null == this.getCodigo() || null == requerimentoBaixaResponsabilidade.getCodigo()) return false;
			else return (this.getCodigo().equals(requerimentoBaixaResponsabilidade.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}