package br.com.ksisolucoes.vo.frota;

import java.io.Serializable;

import br.com.ksisolucoes.vo.frota.base.BaseModeloDocumento;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.DictionaryData;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;


@DictionaryData(referenceField="referencia")
public class ModeloDocumento extends BaseModeloDocumento implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ModeloDocumento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ModeloDocumento (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ModeloDocumento (
		java.lang.Long codigo,
		java.lang.String descricao) {

		super (
			codigo,
			descricao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    @Override
    public String getDescricaoVO() {
        return getDescricao();
    }

    @Override
    public String getIdentificador() {
        return this.getCodigo().toString();
    }
}