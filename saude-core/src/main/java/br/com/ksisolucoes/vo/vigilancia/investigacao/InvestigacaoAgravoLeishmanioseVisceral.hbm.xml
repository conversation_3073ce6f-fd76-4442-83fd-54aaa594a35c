<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.investigacao">

    <class name="InvestigacaoAgravoLeishmanioseVisceral" table="investigacao_agr_leishmaniose_visceral">

        <id name="codigo"
            type="java.lang.Long"
            column="cd_invest_agr_leishmaniose_visceral" >
            <generator class="sequence">
                <param name="sequence">seq_investigacao_agr_leishmaniose_visceral</param>
            </generator>
        </id>

        <version column="version" name="version" type="long"/>

        <property
            name="flagInformacoesComplementares"
            column="flag_informacoes_complementares"
            type="java.lang.String"
            not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo"
                name="registroAgravo"
                column="cd_registro_agravo"
                not-null="true"
        />

        <property
                name="dataInvestigacao"
                column="dt_investigacao"
                type="java.util.Date"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"
                name="ocupacaoCbo"
                column="ocupacao_cbo"
                not-null="false"
        />


        <!-- Dados Clínicos -->
        <property
                name="manifestacoesClinicasFebre"
                column="manifestacoes_clinicas_febre"
                type="java.lang.Long"
        />
        <property
                name="manifestacoesClinicasEmagrecimento"
                column="manifestacoes_clinicas_emagrecimento"
                type="java.lang.Long"
        />
        <property
                name="manifestacoesClinicasAumentoBaco"
                column="manifestacoes_clinicas_aumento_baco"
                type="java.lang.Long"
        />
        <property
                name="manifestacoesClinicasAumentoFigado"
                column="manifestacoes_clinicas_aumento_figado"
                type="java.lang.Long"
        />
        <property
                name="manifestacoesClinicasFraqueza"
                column="manifestacoes_clinicas_fraqueza"
                type="java.lang.Long"
        />
        <property
                name="manifestacoesClinicasTosseDiarreia"
                column="manifestacoes_clinicas_tosse_Diarreia"
                type="java.lang.Long"
        />
        <property
                name="manifestacoesClinicasQuadroInfeccioso"
                column="manifestacoes_clinicas_quadro_infeccioso"
                type="java.lang.Long"
        />
        <property
                name="manifestacoesClinicasIctericia"
                column="manifestacoes_clinicas_ictericia"
                type="java.lang.Long"
        />
        <property
                name="manifestacoesClinicasEdema"
                column="manifestacoes_clinicas_edema"
                type="java.lang.Long"
        />
        <property
                name="manifestacoesClinicasPalidez"
                column="manifestacoes_clinicas_palidez"
                type="java.lang.Long"
        />
        <property
                name="manifestacoesClinicasHemorragicos"
                column="manifestacoes_clinicas_fenomenos_hemorragicos"
                type="java.lang.Long"
        />
        <property
                name="manifestacoesClinicasOutros"
                column="manifestacoes_clinicas_outros"
                type="java.lang.String"
        />

        <property
                name="coInfeccaoHiv"
                column="co_infeccao_hiv"
                type="java.lang.Long"
        />

        <!-- Dados Laboratoriais -->
        <property
                name="diagnosticoParasitologico"
                column="diagnostico_parasitologico"
                type="java.lang.Long"
        />
        <property
                name="diagnosticoImunologicoIfi"
                column="diagnostico_imunologico_ifi"
                type="java.lang.Long"
        />
        <property
                name="diagnosticoImunologicoOutro"
                column="diagnostico_imunologico_outro"
                type="java.lang.Long"
        />
        <property
                name="tipoEntrada"
                column="tipo_entrada"
                type="java.lang.Long"
        />

        <!-- Tratamento -->
        <property
                name="dataInicioTratamento"
                column="data_inicio_tratamento"
                type="java.util.Date"
        />
        <property
                name="drogaInicialAdministrada"
                column="droga_inicial_administrada"
                type="java.lang.Long"
        />
        <property
                name="peso"
                column="peso"
                type="java.lang.String"
        />
        <property
                name="dosePrescrita"
                column="dose_prescrita"
                type="java.lang.Long"
        />
        <property
                name="numeroAmpolasPrescritas"
                column="numero_ampolas_prescritas"
                type="java.lang.String"
        />
        <property
                name="outraDrogaPrescrita"
                column="outra_droga_utilizada"
                type="java.lang.Long"
        />

        <!-- Conclusão -->
        <property
                name="classificacaoFinal"
                column="classificacao_final"
                type="java.lang.Long"
        />
        <property
                name="criterioConfirmacao"
                column="criterio_confirmacao"
                type="java.lang.Long"
        />
        <property
                name="doencaRelacionadaTrabalho"
                column="doenca_relacionada_trabalho"
                type="java.lang.Long"
        />
        <property
                name="evolucaoCaso"
                column="evolucao_caso"
                type="java.lang.Long"
        />
        <property
                name="dataObito"
                column="data_obito"
                type="java.util.Date"
        />

        <!-- CASO Autoctone -->
        <property
                name="casoAutoctone"
                column="caso_autoctone"
                type="java.lang.Long"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Cidade"
                name="cidadeLocalInfeccao"
                column="cd_cidade_infeccao"
                not-null="false"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Pais"
                name="paisLocalInfeccao"
                column="cd_pais_infeccao"
                not-null="false"
        />
        <property
                name="distritoLocalInfeccao"
                column="str_distrito_infeccao"
                type="java.lang.String"
        />
        <property
                name="bairroLocalInfeccao"
                column="str_bairro_infeccao"
                type="java.lang.String"
        />

        <!-- Observacoes -->
        <property
            name="observacao"
            column="observacao"
            type="java.lang.String"
        />

        <!-- Encerramento -->
        <property
            name="dataEncerramento"
            column="dt_encerramento"
            type="java.util.Date"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                name="usuarioEncerramento"
                column="cd_usuario_encerramento"
                not-null="false"
        />

    </class>
</hibernate-mapping>