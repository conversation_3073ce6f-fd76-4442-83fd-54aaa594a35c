package br.com.ksisolucoes.vo.prontuario.enfermagem.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the atendimento_enfermagem_interv_prescricao table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class table="atendimento_enfermagem_interv_prescricao"
 */

public abstract class BaseAtendimentoEnfermagemIntervencaoPrescricao extends BaseRootVO implements Serializable {

    public static String REF = "AtendimentoEnfermagemIntervencaoPrescricao";
    public static final String PROP_PRESCRICAO = "prescricao";
    public static final String PROP_CODIGO = "codigo";
    public static final String PROP_PUBLICO = "publico";
    public static final String PROP_ATENDIMENTO_ENFERMAGEM = "atendimentoEnfermagem";
    public static final String PROP_RECEITUARIO = "receituario";
    public static final String PROP_ATENDIMENTO_ENFERMAGEM_DIAGNOSTICO_PLANEJAMENTO = "atendimentoEnfermagemDiagnosticoPlanejamento";


    // constructors
    public BaseAtendimentoEnfermagemIntervencaoPrescricao() {
        initialize();
    }

    /**
     * Constructor for primary key
     */
    public BaseAtendimentoEnfermagemIntervencaoPrescricao(java.lang.Long codigo) {
        this.setCodigo(codigo);
        initialize();
    }

    /**
     * Constructor for required fields
     */
    public BaseAtendimentoEnfermagemIntervencaoPrescricao(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoEnfermagem atendimentoEnfermagem,
            br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoEnfermagemDiagnosticoPlanejamento atendimentoEnfermagemDiagnosticoPlanejamento,
            br.com.ksisolucoes.vo.prontuario.basico.Receituario receituario,
            java.lang.Long publico) {

        this.setCodigo(codigo);
        this.setAtendimentoEnfermagem(atendimentoEnfermagem);
        this.setAtendimentoEnfermagemDiagnosticoPlanejamento(atendimentoEnfermagemDiagnosticoPlanejamento);
        this.setReceituario(receituario);
        this.setPublico(publico);
        initialize();
    }

    protected void initialize() {
    }


    private int hashCode = Integer.MIN_VALUE;

    // primary key
    private java.lang.Long codigo;

    // fields
    private java.lang.String prescricao;
    private java.lang.Long publico;

    // many to one
    private br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoEnfermagem atendimentoEnfermagem;
    private br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoEnfermagemDiagnosticoPlanejamento atendimentoEnfermagemDiagnosticoPlanejamento;
    private br.com.ksisolucoes.vo.prontuario.basico.Receituario receituario;


    /**
     * Return the unique identifier of this class
     *
     * @hibernate.id generator-class="sequence"
     * column="cd_atendimento_enfermagem_interv_prescricao"
     */
    public java.lang.Long getCodigo() {
        return getPropertyValue(this, codigo, "codigo");
    }

    /**
     * Set the unique identifier of this class
     *
     * @param codigo the new ID
     */
    public void setCodigo(java.lang.Long codigo) {
        this.codigo = codigo;
        this.hashCode = Integer.MIN_VALUE;
    }


    /**
     * Return the value associated with the column: prescricao
     */
    public java.lang.String getPrescricao() {
        return getPropertyValue(this, prescricao, PROP_PRESCRICAO);
    }

    /**
     * Set the value related to the column: prescricao
     *
     * @param prescricao the prescricao value
     */
    public void setPrescricao(java.lang.String prescricao) {
//        java.lang.String prescricaoOld = this.prescricao;
        this.prescricao = prescricao;
//        this.getPropertyChangeSupport().firePropertyChange ("prescricao", prescricaoOld, prescricao);
    }


    /**
     * Return the value associated with the column: publico
     */
    public java.lang.Long getPublico() {
        return getPropertyValue(this, publico, PROP_PUBLICO);
    }

    /**
     * Set the value related to the column: publico
     *
     * @param publico the publico value
     */
    public void setPublico(java.lang.Long publico) {
//        java.lang.Long publicoOld = this.publico;
        this.publico = publico;
//        this.getPropertyChangeSupport().firePropertyChange ("publico", publicoOld, publico);
    }


    /**
     * Return the value associated with the column: cd_atendimento_enfermagem
     */
    public br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoEnfermagem getAtendimentoEnfermagem() {
        return getPropertyValue(this, atendimentoEnfermagem, PROP_ATENDIMENTO_ENFERMAGEM);
    }

    /**
     * Set the value related to the column: cd_atendimento_enfermagem
     *
     * @param atendimentoEnfermagem the cd_atendimento_enfermagem value
     */
    public void setAtendimentoEnfermagem(br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoEnfermagem atendimentoEnfermagem) {
//        br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoEnfermagem atendimentoEnfermagemOld = this.atendimentoEnfermagem;
        this.atendimentoEnfermagem = atendimentoEnfermagem;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimentoEnfermagem", atendimentoEnfermagemOld, atendimentoEnfermagem);
    }


    /**
     * Return the value associated with the column: cd_atendimento_enfermagem_diag_planejamento
     */
    public br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoEnfermagemDiagnosticoPlanejamento getAtendimentoEnfermagemDiagnosticoPlanejamento() {
        return getPropertyValue(this, atendimentoEnfermagemDiagnosticoPlanejamento, PROP_ATENDIMENTO_ENFERMAGEM_DIAGNOSTICO_PLANEJAMENTO);
    }

    /**
     * Set the value related to the column: cd_atendimento_enfermagem_diag_planejamento
     *
     * @param atendimentoEnfermagemDiagnosticoPlanejamento the cd_atendimento_enfermagem_diag_planejamento value
     */
    public void setAtendimentoEnfermagemDiagnosticoPlanejamento(br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoEnfermagemDiagnosticoPlanejamento atendimentoEnfermagemDiagnosticoPlanejamento) {
//        br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoEnfermagemDiagnosticoPlanejamento atendimentoEnfermagemDiagnosticoPlanejamentoOld = this.atendimentoEnfermagemDiagnosticoPlanejamento;
        this.atendimentoEnfermagemDiagnosticoPlanejamento = atendimentoEnfermagemDiagnosticoPlanejamento;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimentoEnfermagemDiagnosticoPlanejamento", atendimentoEnfermagemDiagnosticoPlanejamentoOld, atendimentoEnfermagemDiagnosticoPlanejamento);
    }


    /**
     * Return the value associated with the column: cd_receituario
     */
    public br.com.ksisolucoes.vo.prontuario.basico.Receituario getReceituario() {
        return getPropertyValue(this, receituario, PROP_RECEITUARIO);
    }

    /**
     * Set the value related to the column: cd_receituario
     *
     * @param receituario the cd_receituario value
     */
    public void setReceituario(br.com.ksisolucoes.vo.prontuario.basico.Receituario receituario) {
//        br.com.ksisolucoes.vo.prontuario.basico.Receituario receituarioOld = this.receituario;
        this.receituario = receituario;
//        this.getPropertyChangeSupport().firePropertyChange ("receituario", receituarioOld, receituario);
    }


    public boolean equals(Object obj) {
        if (null == obj) return false;
        if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoEnfermagemIntervencaoPrescricao))
            return false;
        else {
            br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoEnfermagemIntervencaoPrescricao atendimentoEnfermagemIntervencaoPrescricao = (br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoEnfermagemIntervencaoPrescricao) obj;
            if (null == this.getCodigo() || null == atendimentoEnfermagemIntervencaoPrescricao.getCodigo())
                return false;
            else return (this.getCodigo().equals(atendimentoEnfermagemIntervencaoPrescricao.getCodigo()));
        }
    }

    public int hashCode() {
        if (Integer.MIN_VALUE == this.hashCode) {
            if (null == this.getCodigo()) return super.hashCode();
            else {
                String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
                this.hashCode = hashStr.hashCode();
            }
        }
        return this.hashCode;
    }


    public String toString() {
        return super.toString();
    }

    private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
        if (this.retornoValidacao == null) {
            this.retornoValidacao = new RetornoValidacao();
        }
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
        this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}