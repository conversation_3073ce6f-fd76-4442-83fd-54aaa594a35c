package br.com.ksisolucoes.vo.atividadegrupo.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the atividade_grupo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="atividade_grupo"
 */

public abstract class BaseAtividadeGrupo extends BaseRootVO implements Serializable {

	public static String REF = "AtividadeGrupo";
	public static final String PROP_PSE_SAUDE = "pseSaude";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_FIM = "dataFim";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_DESCRICAO_ATA = "descricaoAta";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_PARTICIPANTES = "participantes";
	public static final String PROP_LOCAL_ATIVIDADE_GRUPO = "localAtividadeGrupo";
	public static final String PROP_SITUACAO = "situacao";
	public static final String PROP_NUMERO_INEP = "numeroInep";
	public static final String PROP_DATA_INICIO = "dataInicio";
	public static final String PROP_OUTRO_PROCEDIMENTO_ESUS = "outroProcedimentoEsus";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";
	public static final String PROP_USUARIO_BAIXA = "usuarioBaixa";
	public static final String PROP_DATA_BAIXA = "dataBaixa";
	public static final String PROP_EMPRESA_BPA = "empresaBpa";
	public static final String PROP_OUTRO_PROCEDIMENTO_COLETIVO = "outroProcedimentoColetivo";
	public static final String PROP_TURNO = "turno";
	public static final String PROP_OBSERVACAO_FECHAMENTO = "observacaoFechamento";
	public static final String PROP_TIPO_ATIVIDADE_GRUPO = "tipoAtividadeGrupo";
	public static final String PROP_PSE_EDUCACAO = "pseEducacao";
	public static final String PROP_MOTIVO = "motivo";
	public static final String PROP_ASSUNTO = "assunto";
	public static final String PROP_ORIGEM = "origem";


	// constructors
	public BaseAtividadeGrupo () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAtividadeGrupo (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String assunto;
	private java.util.Date dataInicio;
	private java.util.Date dataFim;
	private java.util.Date dataBaixa;
	private java.util.Date dataCadastro;
	private java.util.Date dataCancelamento;
	private java.lang.String motivo;
	private java.lang.Long situacao;
	private java.lang.String observacaoFechamento;
	private java.lang.Long participantes;
	private java.lang.Long numeroInep;
	private java.lang.Long origem;
	private java.lang.Long turno;
	private java.lang.String descricaoAta;
	private java.lang.Long pseEducacao;
	private java.lang.Long pseSaude;

	// many to one
	private br.com.ksisolucoes.vo.atividadegrupo.LocalAtividadeGrupo localAtividadeGrupo;
	private br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupo tipoAtividadeGrupo;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioBaixa;
	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private br.com.ksisolucoes.vo.esus.ProcedimentoEsus outroProcedimentoEsus;
	private br.com.ksisolucoes.vo.esus.ProcedimentoEloEsus outroProcedimentoColetivo;
	private br.com.ksisolucoes.vo.basico.Empresa empresaBpa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_atv_grupo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: assunto
	 */
	public java.lang.String getAssunto () {
		return getPropertyValue(this, assunto, PROP_ASSUNTO); 
	}

	/**
	 * Set the value related to the column: assunto
	 * @param assunto the assunto value
	 */
	public void setAssunto (java.lang.String assunto) {
//        java.lang.String assuntoOld = this.assunto;
		this.assunto = assunto;
//        this.getPropertyChangeSupport().firePropertyChange ("assunto", assuntoOld, assunto);
	}



	/**
	 * Return the value associated with the column: data_hora_inicio
	 */
	public java.util.Date getDataInicio () {
		return getPropertyValue(this, dataInicio, PROP_DATA_INICIO); 
	}

	/**
	 * Set the value related to the column: data_hora_inicio
	 * @param dataInicio the data_hora_inicio value
	 */
	public void setDataInicio (java.util.Date dataInicio) {
//        java.util.Date dataInicioOld = this.dataInicio;
		this.dataInicio = dataInicio;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInicio", dataInicioOld, dataInicio);
	}



	/**
	 * Return the value associated with the column: data_hora_fim
	 */
	public java.util.Date getDataFim () {
		return getPropertyValue(this, dataFim, PROP_DATA_FIM); 
	}

	/**
	 * Set the value related to the column: data_hora_fim
	 * @param dataFim the data_hora_fim value
	 */
	public void setDataFim (java.util.Date dataFim) {
//        java.util.Date dataFimOld = this.dataFim;
		this.dataFim = dataFim;
//        this.getPropertyChangeSupport().firePropertyChange ("dataFim", dataFimOld, dataFim);
	}



	/**
	 * Return the value associated with the column: dt_baixa
	 */
	public java.util.Date getDataBaixa () {
		return getPropertyValue(this, dataBaixa, PROP_DATA_BAIXA); 
	}

	/**
	 * Set the value related to the column: dt_baixa
	 * @param dataBaixa the dt_baixa value
	 */
	public void setDataBaixa (java.util.Date dataBaixa) {
//        java.util.Date dataBaixaOld = this.dataBaixa;
		this.dataBaixa = dataBaixa;
//        this.getPropertyChangeSupport().firePropertyChange ("dataBaixa", dataBaixaOld, dataBaixa);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: motivo
	 */
	public java.lang.String getMotivo () {
		return getPropertyValue(this, motivo, PROP_MOTIVO); 
	}

	/**
	 * Set the value related to the column: motivo
	 * @param motivo the motivo value
	 */
	public void setMotivo (java.lang.String motivo) {
//        java.lang.String motivoOld = this.motivo;
		this.motivo = motivo;
//        this.getPropertyChangeSupport().firePropertyChange ("motivo", motivoOld, motivo);
	}



	/**
	 * Return the value associated with the column: situacao
	 */
	public java.lang.Long getSituacao () {
		return getPropertyValue(this, situacao, PROP_SITUACAO); 
	}

	/**
	 * Set the value related to the column: situacao
	 * @param situacao the situacao value
	 */
	public void setSituacao (java.lang.Long situacao) {
//        java.lang.Long situacaoOld = this.situacao;
		this.situacao = situacao;
//        this.getPropertyChangeSupport().firePropertyChange ("situacao", situacaoOld, situacao);
	}



	/**
	 * Return the value associated with the column: observacao_fechamento
	 */
	public java.lang.String getObservacaoFechamento () {
		return getPropertyValue(this, observacaoFechamento, PROP_OBSERVACAO_FECHAMENTO); 
	}

	/**
	 * Set the value related to the column: observacao_fechamento
	 * @param observacaoFechamento the observacao_fechamento value
	 */
	public void setObservacaoFechamento (java.lang.String observacaoFechamento) {
//        java.lang.String observacaoFechamentoOld = this.observacaoFechamento;
		this.observacaoFechamento = observacaoFechamento;
//        this.getPropertyChangeSupport().firePropertyChange ("observacaoFechamento", observacaoFechamentoOld, observacaoFechamento);
	}



	/**
	 * Return the value associated with the column: qtd_participantes
	 */
	public java.lang.Long getParticipantes () {
		return getPropertyValue(this, participantes, PROP_PARTICIPANTES); 
	}

	/**
	 * Set the value related to the column: qtd_participantes
	 * @param participantes the qtd_participantes value
	 */
	public void setParticipantes (java.lang.Long participantes) {
//        java.lang.Long participantesOld = this.participantes;
		this.participantes = participantes;
//        this.getPropertyChangeSupport().firePropertyChange ("participantes", participantesOld, participantes);
	}



	/**
	 * Return the value associated with the column: nr_inep
	 */
	public java.lang.Long getNumeroInep () {
		return getPropertyValue(this, numeroInep, PROP_NUMERO_INEP); 
	}

	/**
	 * Set the value related to the column: nr_inep
	 * @param numeroInep the nr_inep value
	 */
	public void setNumeroInep (java.lang.Long numeroInep) {
//        java.lang.Long numeroInepOld = this.numeroInep;
		this.numeroInep = numeroInep;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroInep", numeroInepOld, numeroInep);
	}



	/**
	 * Return the value associated with the column: flag_origem
	 */
	public java.lang.Long getOrigem () {
		return getPropertyValue(this, origem, PROP_ORIGEM); 
	}

	/**
	 * Set the value related to the column: flag_origem
	 * @param origem the flag_origem value
	 */
	public void setOrigem (java.lang.Long origem) {
//        java.lang.Long origemOld = this.origem;
		this.origem = origem;
//        this.getPropertyChangeSupport().firePropertyChange ("origem", origemOld, origem);
	}



	/**
	 * Return the value associated with the column: turno
	 */
	public java.lang.Long getTurno () {
		return getPropertyValue(this, turno, PROP_TURNO); 
	}

	/**
	 * Set the value related to the column: turno
	 * @param turno the turno value
	 */
	public void setTurno (java.lang.Long turno) {
//        java.lang.Long turnoOld = this.turno;
		this.turno = turno;
//        this.getPropertyChangeSupport().firePropertyChange ("turno", turnoOld, turno);
	}



	/**
	 * Return the value associated with the column: ds_ata
	 */
	public java.lang.String getDescricaoAta () {
		return getPropertyValue(this, descricaoAta, PROP_DESCRICAO_ATA); 
	}

	/**
	 * Set the value related to the column: ds_ata
	 * @param descricaoAta the ds_ata value
	 */
	public void setDescricaoAta (java.lang.String descricaoAta) {
//        java.lang.String descricaoAtaOld = this.descricaoAta;
		this.descricaoAta = descricaoAta;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoAta", descricaoAtaOld, descricaoAta);
	}



	/**
	 * Return the value associated with the column: pse_educacao
	 */
	public java.lang.Long getPseEducacao () {
		return getPropertyValue(this, pseEducacao, PROP_PSE_EDUCACAO); 
	}

	/**
	 * Set the value related to the column: pse_educacao
	 * @param pseEducacao the pse_educacao value
	 */
	public void setPseEducacao (java.lang.Long pseEducacao) {
//        java.lang.Long pseEducacaoOld = this.pseEducacao;
		this.pseEducacao = pseEducacao;
//        this.getPropertyChangeSupport().firePropertyChange ("pseEducacao", pseEducacaoOld, pseEducacao);
	}



	/**
	 * Return the value associated with the column: pse_saude
	 */
	public java.lang.Long getPseSaude () {
		return getPropertyValue(this, pseSaude, PROP_PSE_SAUDE); 
	}

	/**
	 * Set the value related to the column: pse_saude
	 * @param pseSaude the pse_saude value
	 */
	public void setPseSaude (java.lang.Long pseSaude) {
//        java.lang.Long pseSaudeOld = this.pseSaude;
		this.pseSaude = pseSaude;
//        this.getPropertyChangeSupport().firePropertyChange ("pseSaude", pseSaudeOld, pseSaude);
	}



	/**
	 * Return the value associated with the column: cd_local_acao
	 */
	public br.com.ksisolucoes.vo.atividadegrupo.LocalAtividadeGrupo getLocalAtividadeGrupo () {
		return getPropertyValue(this, localAtividadeGrupo, PROP_LOCAL_ATIVIDADE_GRUPO); 
	}

	/**
	 * Set the value related to the column: cd_local_acao
	 * @param localAtividadeGrupo the cd_local_acao value
	 */
	public void setLocalAtividadeGrupo (br.com.ksisolucoes.vo.atividadegrupo.LocalAtividadeGrupo localAtividadeGrupo) {
//        br.com.ksisolucoes.vo.atividadegrupo.LocalAtividadeGrupo localAtividadeGrupoOld = this.localAtividadeGrupo;
		this.localAtividadeGrupo = localAtividadeGrupo;
//        this.getPropertyChangeSupport().firePropertyChange ("localAtividadeGrupo", localAtividadeGrupoOld, localAtividadeGrupo);
	}



	/**
	 * Return the value associated with the column: cd_tp_atv_grupo
	 */
	public br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupo getTipoAtividadeGrupo () {
		return getPropertyValue(this, tipoAtividadeGrupo, PROP_TIPO_ATIVIDADE_GRUPO); 
	}

	/**
	 * Set the value related to the column: cd_tp_atv_grupo
	 * @param tipoAtividadeGrupo the cd_tp_atv_grupo value
	 */
	public void setTipoAtividadeGrupo (br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupo tipoAtividadeGrupo) {
//        br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupo tipoAtividadeGrupoOld = this.tipoAtividadeGrupo;
		this.tipoAtividadeGrupo = tipoAtividadeGrupo;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAtividadeGrupo", tipoAtividadeGrupoOld, tipoAtividadeGrupo);
	}



	/**
	 * Return the value associated with the column: cd_usu_baixa
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioBaixa () {
		return getPropertyValue(this, usuarioBaixa, PROP_USUARIO_BAIXA); 
	}

	/**
	 * Set the value related to the column: cd_usu_baixa
	 * @param usuarioBaixa the cd_usu_baixa value
	 */
	public void setUsuarioBaixa (br.com.ksisolucoes.vo.controle.Usuario usuarioBaixa) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioBaixaOld = this.usuarioBaixa;
		this.usuarioBaixa = usuarioBaixa;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioBaixa", usuarioBaixaOld, usuarioBaixa);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: cd_outro_procedimento_esus
	 */
	public br.com.ksisolucoes.vo.esus.ProcedimentoEsus getOutroProcedimentoEsus () {
		return getPropertyValue(this, outroProcedimentoEsus, PROP_OUTRO_PROCEDIMENTO_ESUS); 
	}

	/**
	 * Set the value related to the column: cd_outro_procedimento_esus
	 * @param outroProcedimentoEsus the cd_outro_procedimento_esus value
	 */
	public void setOutroProcedimentoEsus (br.com.ksisolucoes.vo.esus.ProcedimentoEsus outroProcedimentoEsus) {
//        br.com.ksisolucoes.vo.esus.ProcedimentoEsus outroProcedimentoEsusOld = this.outroProcedimentoEsus;
		this.outroProcedimentoEsus = outroProcedimentoEsus;
//        this.getPropertyChangeSupport().firePropertyChange ("outroProcedimentoEsus", outroProcedimentoEsusOld, outroProcedimentoEsus);
	}



	/**
	 * Return the value associated with the column: cd_outro_proc_coletivo
	 */
	public br.com.ksisolucoes.vo.esus.ProcedimentoEloEsus getOutroProcedimentoColetivo () {
		return getPropertyValue(this, outroProcedimentoColetivo, PROP_OUTRO_PROCEDIMENTO_COLETIVO); 
	}

	/**
	 * Set the value related to the column: cd_outro_proc_coletivo
	 * @param outroProcedimentoColetivo the cd_outro_proc_coletivo value
	 */
	public void setOutroProcedimentoColetivo (br.com.ksisolucoes.vo.esus.ProcedimentoEloEsus outroProcedimentoColetivo) {
//        br.com.ksisolucoes.vo.esus.ProcedimentoEloEsus outroProcedimentoColetivoOld = this.outroProcedimentoColetivo;
		this.outroProcedimentoColetivo = outroProcedimentoColetivo;
//        this.getPropertyChangeSupport().firePropertyChange ("outroProcedimentoColetivo", outroProcedimentoColetivoOld, outroProcedimentoColetivo);
	}



	/**
	 * Return the value associated with the column: cd_empresa_bpa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresaBpa () {
		return getPropertyValue(this, empresaBpa, PROP_EMPRESA_BPA); 
	}

	/**
	 * Set the value related to the column: cd_empresa_bpa
	 * @param empresaBpa the cd_empresa_bpa value
	 */
	public void setEmpresaBpa (br.com.ksisolucoes.vo.basico.Empresa empresaBpa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaBpaOld = this.empresaBpa;
		this.empresaBpa = empresaBpa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaBpa", empresaBpaOld, empresaBpa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo)) return false;
		else {
			br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo atividadeGrupo = (br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo) obj;
			if (null == this.getCodigo() || null == atividadeGrupo.getCodigo()) return false;
			else return (this.getCodigo().equals(atividadeGrupo.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}