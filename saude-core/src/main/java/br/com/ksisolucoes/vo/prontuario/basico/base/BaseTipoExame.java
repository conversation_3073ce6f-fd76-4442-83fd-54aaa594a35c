package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the tipo_exame table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="tipo_exame"
 */

public abstract class BaseTipoExame extends BaseRootVO implements Serializable {

	public static String REF = "TipoExame";
	public static final String PROP_TIPO_MOVIMENTO_CONTA_FINANCEIRA = "tipoMovimentoContaFinanceira";
	public static final String PROP_PPI_GRUPO = "ppiGrupo";
	public static final String PROP_FLAG_ENVIA_MENSAGEM = "flagEnviaMensagem";
	public static final String PROP_CONVENIO = "convenio";
	public static final String PROP_FLAG_EXIBIR_NO_REQUISICAO_EXAMES = "flagExibirNoRequisicaoExames";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TIPO_REGRA = "tipoRegra";
	public static final String PROP_DIAS_VALIDACAO_EXAME_PENDENTE = "diasValidacaoExamePendente";
	public static final String PROP_QUANTIDADE_EXAME_REQUISICAO = "quantidadeExameRequisicao";
	public static final String PROP_TIPO_PROCEDIMENTO = "tipoProcedimento";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_AGENDADO = "agendado";
	public static final String PROP_TIPO = "tipo";
	public static final String PROP_DIAS_VALIDADE_AUTORIZACAO = "diasValidadeAutorizacao";
	public static final String PROP_QUANTIDADE_DIAS = "quantidadeDias";
	public static final String PROP_FLAG_Q_R_CODE = "flagQRCode";
	public static final String PROP_FLAG_EXIBIR_HISTORICO = "flagExibirHistorico";
	public static final String PROP_CLASSIFICACAO = "classificacao";
	public static final String PROP_EXAME_PROCEDIMENTO_PADRAO = "exameProcedimentoPadrao";
	public static final String PROP_FLAG_IMPRIMIR_SEM_AUTORIZACAO = "flagImprimirSemAutorizacao";


	// constructors
	public BaseTipoExame () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseTipoExame (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseTipoExame (
		java.lang.Long codigo,
		java.lang.String descricao,
		java.lang.Long classificacao,
		java.lang.Long flagImprimirSemAutorizacao,
		java.lang.Long flagExibirHistorico,
		java.lang.Long flagEnviaMensagem) {

		this.setCodigo(codigo);
		this.setDescricao(descricao);
		this.setClassificacao(classificacao);
		this.setFlagImprimirSemAutorizacao(flagImprimirSemAutorizacao);
		this.setFlagExibirHistorico(flagExibirHistorico);
		this.setFlagEnviaMensagem(flagEnviaMensagem);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.lang.Long quantidadeExameRequisicao;
	private java.lang.Long convenio;
	private java.lang.String agendado;
	private java.lang.Long classificacao;
	private java.lang.Long tipo;
	private java.lang.Long flagImprimirSemAutorizacao;
	private java.lang.Long quantidadeDias;
	private java.lang.Long tipoRegra;
	private java.lang.Long diasValidacaoExamePendente;
	private java.lang.Long diasValidadeAutorizacao;
	private java.lang.Long flagExibirHistorico;
	private java.lang.Long flagEnviaMensagem;
	private java.lang.Long flagQRCode;
	private java.lang.Long flagExibirNoRequisicaoExames;

	// many to one
	private br.com.ksisolucoes.vo.geral.PpiGrupo ppiGrupo;
	private br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimento;
	private br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimentoPadrao;
	private br.com.ksisolucoes.vo.hospital.financeiro.TipoMovimentoContaFinanceira tipoMovimentoContaFinanceira;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_tp_exame"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_tp_exame
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_tp_exame
	 * @param descricao the ds_tp_exame value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: qt_exame_requisicao
	 */
	public java.lang.Long getQuantidadeExameRequisicao () {
		return getPropertyValue(this, quantidadeExameRequisicao, PROP_QUANTIDADE_EXAME_REQUISICAO); 
	}

	/**
	 * Set the value related to the column: qt_exame_requisicao
	 * @param quantidadeExameRequisicao the qt_exame_requisicao value
	 */
	public void setQuantidadeExameRequisicao (java.lang.Long quantidadeExameRequisicao) {
//        java.lang.Long quantidadeExameRequisicaoOld = this.quantidadeExameRequisicao;
		this.quantidadeExameRequisicao = quantidadeExameRequisicao;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeExameRequisicao", quantidadeExameRequisicaoOld, quantidadeExameRequisicao);
	}



	/**
	 * Return the value associated with the column: convenio
	 */
	public java.lang.Long getConvenio () {
		return getPropertyValue(this, convenio, PROP_CONVENIO); 
	}

	/**
	 * Set the value related to the column: convenio
	 * @param convenio the convenio value
	 */
	public void setConvenio (java.lang.Long convenio) {
//        java.lang.Long convenioOld = this.convenio;
		this.convenio = convenio;
//        this.getPropertyChangeSupport().firePropertyChange ("convenio", convenioOld, convenio);
	}



	/**
	 * Return the value associated with the column: agendado
	 */
	public java.lang.String getAgendado () {
		return getPropertyValue(this, agendado, PROP_AGENDADO); 
	}

	/**
	 * Set the value related to the column: agendado
	 * @param agendado the agendado value
	 */
	public void setAgendado (java.lang.String agendado) {
//        java.lang.String agendadoOld = this.agendado;
		this.agendado = agendado;
//        this.getPropertyChangeSupport().firePropertyChange ("agendado", agendadoOld, agendado);
	}



	/**
	 * Return the value associated with the column: classificacao
	 */
	public java.lang.Long getClassificacao () {
		return getPropertyValue(this, classificacao, PROP_CLASSIFICACAO); 
	}

	/**
	 * Set the value related to the column: classificacao
	 * @param classificacao the classificacao value
	 */
	public void setClassificacao (java.lang.Long classificacao) {
//        java.lang.Long classificacaoOld = this.classificacao;
		this.classificacao = classificacao;
//        this.getPropertyChangeSupport().firePropertyChange ("classificacao", classificacaoOld, classificacao);
	}



	/**
	 * Return the value associated with the column: tp_exame
	 */
	public java.lang.Long getTipo () {
		return getPropertyValue(this, tipo, PROP_TIPO); 
	}

	/**
	 * Set the value related to the column: tp_exame
	 * @param tipo the tp_exame value
	 */
	public void setTipo (java.lang.Long tipo) {
//        java.lang.Long tipoOld = this.tipo;
		this.tipo = tipo;
//        this.getPropertyChangeSupport().firePropertyChange ("tipo", tipoOld, tipo);
	}



	/**
	 * Return the value associated with the column: flag_imp_sem_autorizacao
	 */
	public java.lang.Long getFlagImprimirSemAutorizacao () {
		return getPropertyValue(this, flagImprimirSemAutorizacao, PROP_FLAG_IMPRIMIR_SEM_AUTORIZACAO); 
	}

	/**
	 * Set the value related to the column: flag_imp_sem_autorizacao
	 * @param flagImprimirSemAutorizacao the flag_imp_sem_autorizacao value
	 */
	public void setFlagImprimirSemAutorizacao (java.lang.Long flagImprimirSemAutorizacao) {
//        java.lang.Long flagImprimirSemAutorizacaoOld = this.flagImprimirSemAutorizacao;
		this.flagImprimirSemAutorizacao = flagImprimirSemAutorizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("flagImprimirSemAutorizacao", flagImprimirSemAutorizacaoOld, flagImprimirSemAutorizacao);
	}



	/**
	 * Return the value associated with the column: qt_dias
	 */
	public java.lang.Long getQuantidadeDias () {
		return getPropertyValue(this, quantidadeDias, PROP_QUANTIDADE_DIAS); 
	}

	/**
	 * Set the value related to the column: qt_dias
	 * @param quantidadeDias the qt_dias value
	 */
	public void setQuantidadeDias (java.lang.Long quantidadeDias) {
//        java.lang.Long quantidadeDiasOld = this.quantidadeDias;
		this.quantidadeDias = quantidadeDias;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeDias", quantidadeDiasOld, quantidadeDias);
	}



	/**
	 * Return the value associated with the column: tp_regra_dias
	 */
	public java.lang.Long getTipoRegra () {
		return getPropertyValue(this, tipoRegra, PROP_TIPO_REGRA); 
	}

	/**
	 * Set the value related to the column: tp_regra_dias
	 * @param tipoRegra the tp_regra_dias value
	 */
	public void setTipoRegra (java.lang.Long tipoRegra) {
//        java.lang.Long tipoRegraOld = this.tipoRegra;
		this.tipoRegra = tipoRegra;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoRegra", tipoRegraOld, tipoRegra);
	}



	/**
	 * Return the value associated with the column: dias_validacao_exame_pendente
	 */
	public java.lang.Long getDiasValidacaoExamePendente () {
		return getPropertyValue(this, diasValidacaoExamePendente, PROP_DIAS_VALIDACAO_EXAME_PENDENTE); 
	}

	/**
	 * Set the value related to the column: dias_validacao_exame_pendente
	 * @param diasValidacaoExamePendente the dias_validacao_exame_pendente value
	 */
	public void setDiasValidacaoExamePendente (java.lang.Long diasValidacaoExamePendente) {
//        java.lang.Long diasValidacaoExamePendenteOld = this.diasValidacaoExamePendente;
		this.diasValidacaoExamePendente = diasValidacaoExamePendente;
//        this.getPropertyChangeSupport().firePropertyChange ("diasValidacaoExamePendente", diasValidacaoExamePendenteOld, diasValidacaoExamePendente);
	}



	/**
	 * Return the value associated with the column: dias_validade_autorizacao
	 */
	public java.lang.Long getDiasValidadeAutorizacao () {
		return getPropertyValue(this, diasValidadeAutorizacao, PROP_DIAS_VALIDADE_AUTORIZACAO); 
	}

	/**
	 * Set the value related to the column: dias_validade_autorizacao
	 * @param diasValidadeAutorizacao the dias_validade_autorizacao value
	 */
	public void setDiasValidadeAutorizacao (java.lang.Long diasValidadeAutorizacao) {
//        java.lang.Long diasValidadeAutorizacaoOld = this.diasValidadeAutorizacao;
		this.diasValidadeAutorizacao = diasValidadeAutorizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("diasValidadeAutorizacao", diasValidadeAutorizacaoOld, diasValidadeAutorizacao);
	}



	/**
	 * Return the value associated with the column: flag_exibir_historico
	 */
	public java.lang.Long getFlagExibirHistorico () {
		return getPropertyValue(this, flagExibirHistorico, PROP_FLAG_EXIBIR_HISTORICO); 
	}

	/**
	 * Set the value related to the column: flag_exibir_historico
	 * @param flagExibirHistorico the flag_exibir_historico value
	 */
	public void setFlagExibirHistorico (java.lang.Long flagExibirHistorico) {
//        java.lang.Long flagExibirHistoricoOld = this.flagExibirHistorico;
		this.flagExibirHistorico = flagExibirHistorico;
//        this.getPropertyChangeSupport().firePropertyChange ("flagExibirHistorico", flagExibirHistoricoOld, flagExibirHistorico);
	}



	/**
	 * Return the value associated with the column: flag_enviar_mensagem
	 */
	public java.lang.Long getFlagEnviaMensagem () {
		return getPropertyValue(this, flagEnviaMensagem, PROP_FLAG_ENVIA_MENSAGEM); 
	}

	/**
	 * Set the value related to the column: flag_enviar_mensagem
	 * @param flagEnviaMensagem the flag_enviar_mensagem value
	 */
	public void setFlagEnviaMensagem (java.lang.Long flagEnviaMensagem) {
//        java.lang.Long flagEnviaMensagemOld = this.flagEnviaMensagem;
		this.flagEnviaMensagem = flagEnviaMensagem;
//        this.getPropertyChangeSupport().firePropertyChange ("flagEnviaMensagem", flagEnviaMensagemOld, flagEnviaMensagem);
	}



	/**
	 * Return the value associated with the column: flag_qrcode
	 */
	public java.lang.Long getFlagQRCode () {
		return getPropertyValue(this, flagQRCode, PROP_FLAG_Q_R_CODE); 
	}

	/**
	 * Set the value related to the column: flag_qrcode
	 * @param flagQRCode the flag_qrcode value
	 */
	public void setFlagQRCode (java.lang.Long flagQRCode) {
//        java.lang.Long flagQRCodeOld = this.flagQRCode;
		this.flagQRCode = flagQRCode;
//        this.getPropertyChangeSupport().firePropertyChange ("flagQRCode", flagQRCodeOld, flagQRCode);
	}



	/**
	 * Return the value associated with the column: flag_exibir_no_requis_exam
	 */
	public java.lang.Long getFlagExibirNoRequisicaoExames () {
		return getPropertyValue(this, flagExibirNoRequisicaoExames, PROP_FLAG_EXIBIR_NO_REQUISICAO_EXAMES); 
	}

	/**
	 * Set the value related to the column: flag_exibir_no_requis_exam
	 * @param flagExibirNoRequisicaoExames the flag_exibir_no_requis_exam value
	 */
	public void setFlagExibirNoRequisicaoExames (java.lang.Long flagExibirNoRequisicaoExames) {
//        java.lang.Long flagExibirNoRequisicaoExamesOld = this.flagExibirNoRequisicaoExames;
		this.flagExibirNoRequisicaoExames = flagExibirNoRequisicaoExames;
//        this.getPropertyChangeSupport().firePropertyChange ("flagExibirNoRequisicaoExames", flagExibirNoRequisicaoExamesOld, flagExibirNoRequisicaoExames);
	}



	/**
	 * Return the value associated with the column: cd_grupo_ppi
	 */
	public br.com.ksisolucoes.vo.geral.PpiGrupo getPpiGrupo () {
		return getPropertyValue(this, ppiGrupo, PROP_PPI_GRUPO); 
	}

	/**
	 * Set the value related to the column: cd_grupo_ppi
	 * @param ppiGrupo the cd_grupo_ppi value
	 */
	public void setPpiGrupo (br.com.ksisolucoes.vo.geral.PpiGrupo ppiGrupo) {
//        br.com.ksisolucoes.vo.geral.PpiGrupo ppiGrupoOld = this.ppiGrupo;
		this.ppiGrupo = ppiGrupo;
//        this.getPropertyChangeSupport().firePropertyChange ("ppiGrupo", ppiGrupoOld, ppiGrupo);
	}



	/**
	 * Return the value associated with the column: cd_tp_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento getTipoProcedimento () {
		return getPropertyValue(this, tipoProcedimento, PROP_TIPO_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_tp_procedimento
	 * @param tipoProcedimento the cd_tp_procedimento value
	 */
	public void setTipoProcedimento (br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimentoOld = this.tipoProcedimento;
		this.tipoProcedimento = tipoProcedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoProcedimento", tipoProcedimentoOld, tipoProcedimento);
	}



	/**
	 * Return the value associated with the column: cd_exame_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento getExameProcedimentoPadrao () {
		return getPropertyValue(this, exameProcedimentoPadrao, PROP_EXAME_PROCEDIMENTO_PADRAO); 
	}

	/**
	 * Set the value related to the column: cd_exame_procedimento
	 * @param exameProcedimentoPadrao the cd_exame_procedimento value
	 */
	public void setExameProcedimentoPadrao (br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimentoPadrao) {
//        br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimentoPadraoOld = this.exameProcedimentoPadrao;
		this.exameProcedimentoPadrao = exameProcedimentoPadrao;
//        this.getPropertyChangeSupport().firePropertyChange ("exameProcedimentoPadrao", exameProcedimentoPadraoOld, exameProcedimentoPadrao);
	}



	/**
	 * Return the value associated with the column: cd_tp_mov_cta_financeira
	 */
	public br.com.ksisolucoes.vo.hospital.financeiro.TipoMovimentoContaFinanceira getTipoMovimentoContaFinanceira () {
		return getPropertyValue(this, tipoMovimentoContaFinanceira, PROP_TIPO_MOVIMENTO_CONTA_FINANCEIRA); 
	}

	/**
	 * Set the value related to the column: cd_tp_mov_cta_financeira
	 * @param tipoMovimentoContaFinanceira the cd_tp_mov_cta_financeira value
	 */
	public void setTipoMovimentoContaFinanceira (br.com.ksisolucoes.vo.hospital.financeiro.TipoMovimentoContaFinanceira tipoMovimentoContaFinanceira) {
//        br.com.ksisolucoes.vo.hospital.financeiro.TipoMovimentoContaFinanceira tipoMovimentoContaFinanceiraOld = this.tipoMovimentoContaFinanceira;
		this.tipoMovimentoContaFinanceira = tipoMovimentoContaFinanceira;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoMovimentoContaFinanceira", tipoMovimentoContaFinanceiraOld, tipoMovimentoContaFinanceira);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.TipoExame)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.TipoExame tipoExame = (br.com.ksisolucoes.vo.prontuario.basico.TipoExame) obj;
			if (null == this.getCodigo() || null == tipoExame.getCodigo()) return false;
			else return (this.getCodigo().equals(tipoExame.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}