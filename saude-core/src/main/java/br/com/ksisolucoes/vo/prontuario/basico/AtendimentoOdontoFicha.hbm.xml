<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="AtendimentoOdontoFicha" table="atendimento_odonto_ficha" >
        <id
            name="codigo"
            column="cd_ficha_odonto"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id>
        
        <version column="version" name="version" type="long" />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
            column="nr_atendimento"
            name="atendimento"
            not-null="true"
        />
        
        <property 
            column="dt_inicio_tratamento"
            name="dataInicioTratamento"
            type="java.util.Date"
            not-null="true"
        /> 
		
        <property 
            column="observacao"
            name="observacao"
            type="java.lang.String"
            length="800"
            not-null="false"
        />
        
        <property 
            column="status"
            name="status"
            type="java.lang.Long"
            not-null="true"
        />        
        
        <property 
            column="tratamento_medico"
            name="tratamentoMedico"
            type="java.lang.Long"
            not-null="true"
        />        
        
        <property 
            column="doente_hospitalizado"
            name="doenteHospitalizado"
            type="java.lang.Long"
            not-null="true"
        /> 
        
        <property 
            column="doente_hospitalizado_motivo"
            name="doenteHospitalizadoMotivo"
            type="java.lang.String"
            length="50"
            not-null="false"
        />       
        
        <property 
            column="usa_medicamento"
            name="usaMedicamento"
            type="java.lang.Long"
            not-null="true"
        /> 
        
        <property 
            column="usa_medicamento_qual"
            name="usaMedicamentoQual"
            type="java.lang.String"
            length="300"
            not-null="false"
        />       
        
        <property 
            column="prb_cardiacos"
            name="problemasCardiacos"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property 
            column="prb_figado"
            name="problemasFigado"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property 
            column="prb_rins"
            name="problemasRins"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property 
            column="prb_pulmonar"
            name="problemasPulmonar"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property 
            column="diabete"
            name="diabete"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property 
            column="anemia"
            name="anemia"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property 
            column="tontura"
            name="tontura"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property 
            column="hipertensao"
            name="hipertensao"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property 
            column="febre_reumatica"
            name="febreReumatica"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property 
            column="hepatite"
            name="hepatite"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property 
            column="sifilis"
            name="sifilis"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property 
            column="tuberculose"
            name="tuberculose"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property 
            column="enfermidade_nao_mencionada"
            name="enfermidadeNaoMencionada"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property 
            column="enfermidade_nao_mensionada_qual"
            name="enfermidadeNaoMensionadaQual"
            type="java.lang.String"
            length="150"
            not-null="false"
        />
        
        <property 
            column="sangrou_extracao_ferimento"
            name="sangrouExtracaoFerimento"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property 
            column="alergico_medicamento"
            name="alergicoMedicamento"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property 
            column="alergico_anestesia"
            name="alergicoAnestesia"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property 
            column="gravida"
            name="gravida"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property 
            column="gravida_mes_gestacao"
            name="gravidaMesGestacao"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property 
            column="dt_conclusao"
            name="dataConclusao"
            type="java.util.Date"
            not-null="false"
        />
        
        <property 
            column="obs_conclusao"
            name="observacaoConclusao"
            type="java.lang.String"
            length="200"
            not-null="false"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
            column="nr_atendimento_conclusao"
            name="atendimentoConclusao"
            not-null="false"
        />
        
        <property 
            column="motivo_cancelamento"
            name="motivoCancelamento"
            type="java.lang.String"
            length="200"
            not-null="false"
        />
        
        <property 
            column="dt_cancelamento"
            name="dataCancelamento"
            type="timestamp"
            not-null="false"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usu_can"
            name="usuarioCancelamento"
            not-null="false"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
            column="nr_atendimento_can"
            name="atendimentoCancelamento"
            not-null="false"
        />
        
        <property 
            column="tratamento_medico_qual"
            name="tratamentoMedicoQual"
            type="java.lang.String"
            length="50"
            not-null="false"
        />
        
        <property 
            column="alergico_medicamento_qual"
            name="alergicoMedicamentoQual"
            type="java.lang.String"
            length="150"
            not-null="false"
        />
        
        <property 
            column="alergico_anestesia_qual"
            name="alergicoAnestesiaQual"
            type="java.lang.String"
            length="50"
            not-null="false"
        />
        
        <property 
            column="flag_necess_especiais"
            name="flagPossuiNecessidadesEspeciais"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="semana_gestacao"
            name="semanaGestacao"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property 
            column="flag_inicio_tratamento"
            name="flagInicioTratamento"
            type="java.lang.Long"
            not-null="false"
        />

        <property 
            column="odontograma_json"
            name="odontogramaJson"
            type="java.lang.String"
            not-null="false"
        />
    </class>
</hibernate-mapping>
