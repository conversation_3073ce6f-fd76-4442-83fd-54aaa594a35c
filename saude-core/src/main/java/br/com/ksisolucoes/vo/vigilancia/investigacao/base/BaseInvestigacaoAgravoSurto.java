package br.com.ksisolucoes.vo.vigilancia.investigacao.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the investigacao_agr_surto table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agr_surto"
 */

public abstract class BaseInvestigacaoAgravoSurto extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravoSurto";
	public static final String PROP_LOCAL_INICIAL_OCORRENCIA_SURTO = "localInicialOcorrenciaSurto";
	public static final String PROP_FATOR_CAUSAL_MANIPULACAO_INADEQUADA = "fatorCausalManipulacaoInadequada";
	public static final String PROP_NUMERO_DOENTES_FAIXA_ETARIA4_IGNORADO = "numeroDoentesFaixaEtaria4Ignorado";
	public static final String PROP_NUMERO_POSITIVAS_RESULTADO_AMOSTRA_ALIMENTO3 = "numeroPositivasResultadoAmostraAlimento3";
	public static final String PROP_DATA_ENCERRAMENTO = "dataEncerramento";
	public static final String PROP_NUMERO_DOENTES_FAIXA_ETARIA3_MASCULINO = "numeroDoentesFaixaEtaria3Masculino";
	public static final String PROP_SINAL_SINTOMA_DIARREIA = "sinalSintomaDiarreia";
	public static final String PROP_NUMERO_DOENTES_FAIXA_ETARIA6_MASCULINO = "numeroDoentesFaixaEtaria6Masculino";
	public static final String PROP_SINAL_SINTOMA_OUTRO = "sinalSintomaOutro";
	public static final String PROP_NUMERO_POSITIVAS_RESULTADO_AMOSTRA_ALIMENTO1 = "numeroPositivasResultadoAmostraAlimento1";
	public static final String PROP_NUMERO_AMOSTRAS_CLINICAS = "numeroAmostrasClinicas";
	public static final String PROP_NUMERO_POSITIVAS_RESULTADO_AMOSTRA_ALIMENTO2 = "numeroPositivasResultadoAmostraAlimento2";
	public static final String PROP_NUMERO_OBITOS = "numeroObitos";
	public static final String PROP_NUMERO_DOENTES_FAIXA_ETARIA2_FEMININO = "numeroDoentesFaixaEtaria2Feminino";
	public static final String PROP_NUMERO_DOENTES_FAIXA_ETARIA2_IGNORADO = "numeroDoentesFaixaEtaria2Ignorado";
	public static final String PROP_CRITERIO_CONFIRMACAO_DESCARTE = "criterioConfirmacaoDescarte";
	public static final String PROP_COLETADAS_AMOSTRAS_CLINICAS = "coletadasAmostrasClinicas";
	public static final String PROP_PERIODO_INCUBACAO_MINIMO_UNIDADE_MEDIDA = "periodoIncubacaoMinimoUnidadeMedida";
	public static final String PROP_PERIODO_INCUBACAO_MEDIANA = "periodoIncubacaoMediana";
	public static final String PROP_NUMERO_DOENTES_FAIXA_ETARIA7_FEMININO = "numeroDoentesFaixaEtaria7Feminino";
	public static final String PROP_NUMERO_CASOS_SUSPEITOS_ATE_DATA_NOTIFICACAO = "numeroCasosSuspeitosAteDataNotificacao";
	public static final String PROP_SINAL_SINTOMA_VOMITO = "sinalSintomaVomito";
	public static final String PROP_NUMERO_AMOSTRAS_ALIMENTOS = "numeroAmostrasAlimentos";
	public static final String PROP_PERIODO_INCUBACAO_MAXIMO_UNIDADE_MEDIDA = "periodoIncubacaoMaximoUnidadeMedida";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_COLETADAS_AMOSTRAS_ALIMENTOS = "coletadasAmostrasAlimentos";
	public static final String PROP_NUMERO_DOENTES_FAIXA_ETARIA4_MASCULINO = "numeroDoentesFaixaEtaria4Masculino";
	public static final String PROP_FATOR_CAUSAL_CONSERVACAO_INADEQUADA = "fatorCausalConservacaoInadequada";
	public static final String PROP_NUMERO_DOENTES_FAIXA_ETARIA3_FEMININO = "numeroDoentesFaixaEtaria3Feminino";
	public static final String PROP_SINAL_SINTOMA_NEUROLOGICOS = "sinalSintomaNeurologicos";
	public static final String PROP_LOCAL_INGESTAO_OUTRO = "localIngestaoOutro";
	public static final String PROP_NUMERO_DOENTES_FAIXA_ETARIA5_IGNORADO = "numeroDoentesFaixaEtaria5Ignorado";
	public static final String PROP_LOCAL_PRODUCAO_PREPARACAO = "localProducaoPreparacao";
	public static final String PROP_NUMERO_POSITIVAS_RESULTADO_AMOSTRA_CLINICA3 = "numeroPositivasResultadoAmostraClinica3";
	public static final String PROP_SINAL_SINTOMA_FEBRE = "sinalSintomaFebre";
	public static final String PROP_OBSERVACOES = "observacoes";
	public static final String PROP_NUMERO_DOENTES_FAIXA_ETARIA7_IGNORADO = "numeroDoentesFaixaEtaria7Ignorado";
	public static final String PROP_AGENTE_ETIOLOGICO_SURTO = "agenteEtiologicoSurto";
	public static final String PROP_DATA_INVESTIGACAO = "dataInvestigacao";
	public static final String PROP_FATOR_CAUSAL_MATERIA_PRIMA_IMPROPRIA = "fatorCausalMateriaPrimaImpropria";
	public static final String PROP_LOCAL_INGESTAO = "localIngestao";
	public static final String PROP_SINAL_SINTOMA_CEFALEIA = "sinalSintomaCefaleia";
	public static final String PROP_NUMERO_POSITIVAS_RESULTADO_AMOSTRA_CLINICA1 = "numeroPositivasResultadoAmostraClinica1";
	public static final String PROP_NUMERO_DOENTES_FAIXA_ETARIA2_MASCULINO = "numeroDoentesFaixaEtaria2Masculino";
	public static final String PROP_NUMERO_POSITIVAS_RESULTADO_AMOSTRA_CLINICA2 = "numeroPositivasResultadoAmostraClinica2";
	public static final String PROP_MODO_PROVAVEL_TRANSMISSAO = "modoProvavelTransmissao";
	public static final String PROP_USUARIO_ENCERRAMENTO = "usuarioEncerramento";
	public static final String PROP_NUMERO_ENTREVISTADOS = "numeroEntrevistados";
	public static final String PROP_FLAG_INFORMACOES_COMPLEMENTARES = "flagInformacoesComplementares";
	public static final String PROP_NUMERO_DOENTES_FAIXA_ETARIA5_MASCULINO = "numeroDoentesFaixaEtaria5Masculino";
	public static final String PROP_FATOR_CAUSAL_OUTRO = "fatorCausalOutro";
	public static final String PROP_PERIODO_INCUBACAO_MINIMO = "periodoIncubacaoMinimo";
	public static final String PROP_NUMERO_DOENTES_FAIXA_ETARIA3_IGNORADO = "numeroDoentesFaixaEtaria3Ignorado";
	public static final String PROP_RESULTADO_AMOSTRA_CLINICA1 = "resultadoAmostraClinica1";
	public static final String PROP_RESULTADO_AMOSTRA_CLINICA2 = "resultadoAmostraClinica2";
	public static final String PROP_RESULTADO_AMOSTRA_CLINICA3 = "resultadoAmostraClinica3";
	public static final String PROP_LOCAL_PRODUCAO_PREPARACAO_OUTRO = "localProducaoPreparacaoOutro";
	public static final String PROP_NUMERO_DOENTES_FAIXA_ETARIA1_FEMININO = "numeroDoentesFaixaEtaria1Feminino";
	public static final String PROP_ALIMENTO_CAUSADOR_SURTO = "alimentoCausadorSurto";
	public static final String PROP_NUMERO_DOENTES_ENTREVISTADOS = "numeroDoentesEntrevistados";
	public static final String PROP_NUMERO_DOENTES_FAIXA_ETARIA1_IGNORADO = "numeroDoentesFaixaEtaria1Ignorado";
	public static final String PROP_VEICULO_TRANSMISSAO_INDIRETO_OUTRO = "veiculoTransmissaoIndiretoOutro";
	public static final String PROP_PERIODO_INCUBACAO_MEDIANA_UNIDADE_MEDIDA = "periodoIncubacaoMedianaUnidadeMedida";
	public static final String PROP_PERIODO_INCUBACAO_MAXIMO = "periodoIncubacaoMaximo";
	public static final String PROP_MEDIDAS_ADOTADAS_RECOMENDADAS = "medidasAdotadasRecomendadas";
	public static final String PROP_REGISTRO_AGRAVO = "registroAgravo";
	public static final String PROP_NUMERO_DOENTES_FAIXA_ETARIA4_FEMININO = "numeroDoentesFaixaEtaria4Feminino";
	public static final String PROP_NUMERO_DOENTES_FAIXA_ETARIA1_MASCULINO = "numeroDoentesFaixaEtaria1Masculino";
	public static final String PROP_VEICULO_TRANSMISSAO_INDIRETO = "veiculoTransmissaoIndireto";
	public static final String PROP_NUMERO_TOTAL_DOENTES = "numeroTotalDoentes";
	public static final String PROP_RESULTADO_AMOSTRA_ALIMENTO1 = "resultadoAmostraAlimento1";
	public static final String PROP_SINAL_SINTOMA_DOR_ABDOMINAL = "sinalSintomaDorAbdominal";
	public static final String PROP_RESULTADO_AMOSTRA_ALIMENTO2 = "resultadoAmostraAlimento2";
	public static final String PROP_NUMERO_DOENTES_FAIXA_ETARIA6_IGNORADO = "numeroDoentesFaixaEtaria6Ignorado";
	public static final String PROP_NUMERO_TOTAL_HOSPITALIZADOS = "numeroTotalHospitalizados";
	public static final String PROP_NUMERO_DOENTES_FAIXA_ETARIA5_FEMININO = "numeroDoentesFaixaEtaria5Feminino";
	public static final String PROP_NUMERO_DOENTES_FAIXA_ETARIA7_MASCULINO = "numeroDoentesFaixaEtaria7Masculino";
	public static final String PROP_NUMERO_DOENTES_FAIXA_ETARIA6_FEMININO = "numeroDoentesFaixaEtaria6Feminino";
	public static final String PROP_RESULTADO_AMOSTRA_ALIMENTO3 = "resultadoAmostraAlimento3";
	public static final String PROP_SINAL_SINTOMA_NAUSEA = "sinalSintomaNausea";


	// constructors
	public BaseInvestigacaoAgravoSurto () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravoSurto (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseInvestigacaoAgravoSurto (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
		java.lang.String flagInformacoesComplementares) {

		this.setCodigo(codigo);
		this.setRegistroAgravo(registroAgravo);
		this.setFlagInformacoesComplementares(flagInformacoesComplementares);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String flagInformacoesComplementares;
	private java.lang.String numeroCasosSuspeitosAteDataNotificacao;
	private java.lang.Long localInicialOcorrenciaSurto;
	private java.util.Date dataInvestigacao;
	private java.lang.Long modoProvavelTransmissao;
	private java.lang.Long veiculoTransmissaoIndireto;
	private java.lang.String veiculoTransmissaoIndiretoOutro;
	private java.lang.String numeroEntrevistados;
	private java.lang.String numeroDoentesEntrevistados;
	private java.lang.String numeroTotalDoentes;
	private java.lang.String numeroTotalHospitalizados;
	private java.lang.String numeroObitos;
	private java.lang.String numeroDoentesFaixaEtaria1Masculino;
	private java.lang.String numeroDoentesFaixaEtaria1Feminino;
	private java.lang.String numeroDoentesFaixaEtaria1Ignorado;
	private java.lang.String numeroDoentesFaixaEtaria2Masculino;
	private java.lang.String numeroDoentesFaixaEtaria2Feminino;
	private java.lang.String numeroDoentesFaixaEtaria2Ignorado;
	private java.lang.String numeroDoentesFaixaEtaria3Masculino;
	private java.lang.String numeroDoentesFaixaEtaria3Feminino;
	private java.lang.String numeroDoentesFaixaEtaria3Ignorado;
	private java.lang.String numeroDoentesFaixaEtaria4Masculino;
	private java.lang.String numeroDoentesFaixaEtaria4Feminino;
	private java.lang.String numeroDoentesFaixaEtaria4Ignorado;
	private java.lang.String numeroDoentesFaixaEtaria5Masculino;
	private java.lang.String numeroDoentesFaixaEtaria5Feminino;
	private java.lang.String numeroDoentesFaixaEtaria5Ignorado;
	private java.lang.String numeroDoentesFaixaEtaria6Masculino;
	private java.lang.String numeroDoentesFaixaEtaria6Feminino;
	private java.lang.String numeroDoentesFaixaEtaria6Ignorado;
	private java.lang.String numeroDoentesFaixaEtaria7Masculino;
	private java.lang.String numeroDoentesFaixaEtaria7Feminino;
	private java.lang.String numeroDoentesFaixaEtaria7Ignorado;
	private java.lang.String sinalSintomaNausea;
	private java.lang.String sinalSintomaVomito;
	private java.lang.String sinalSintomaDiarreia;
	private java.lang.String sinalSintomaCefaleia;
	private java.lang.String sinalSintomaDorAbdominal;
	private java.lang.String sinalSintomaNeurologicos;
	private java.lang.String sinalSintomaOutro;
	private java.lang.String sinalSintomaFebre;
	private java.lang.String periodoIncubacaoMinimo;
	private java.lang.Long periodoIncubacaoMinimoUnidadeMedida;
	private java.lang.String periodoIncubacaoMaximo;
	private java.lang.Long periodoIncubacaoMaximoUnidadeMedida;
	private java.lang.String periodoIncubacaoMediana;
	private java.lang.Long periodoIncubacaoMedianaUnidadeMedida;
	private java.lang.Long localProducaoPreparacao;
	private java.lang.String localProducaoPreparacaoOutro;
	private java.lang.Long localIngestao;
	private java.lang.String localIngestaoOutro;
	private java.lang.Long fatorCausalMateriaPrimaImpropria;
	private java.lang.Long fatorCausalManipulacaoInadequada;
	private java.lang.Long fatorCausalConservacaoInadequada;
	private java.lang.String fatorCausalOutro;
	private java.lang.Long coletadasAmostrasClinicas;
	private java.lang.String numeroAmostrasClinicas;
	private java.lang.String resultadoAmostraClinica1;
	private java.lang.String numeroPositivasResultadoAmostraClinica1;
	private java.lang.String resultadoAmostraClinica2;
	private java.lang.String numeroPositivasResultadoAmostraClinica2;
	private java.lang.String resultadoAmostraClinica3;
	private java.lang.String numeroPositivasResultadoAmostraClinica3;
	private java.lang.Long coletadasAmostrasAlimentos;
	private java.lang.String numeroAmostrasAlimentos;
	private java.lang.String resultadoAmostraAlimento1;
	private java.lang.String numeroPositivasResultadoAmostraAlimento1;
	private java.lang.String resultadoAmostraAlimento2;
	private java.lang.String numeroPositivasResultadoAmostraAlimento2;
	private java.lang.String resultadoAmostraAlimento3;
	private java.lang.String numeroPositivasResultadoAmostraAlimento3;
	private java.lang.Long criterioConfirmacaoDescarte;
	private java.lang.String agenteEtiologicoSurto;
	private java.lang.String alimentoCausadorSurto;
	private java.lang.String medidasAdotadasRecomendadas;
	private java.lang.String observacoes;
	private java.util.Date dataEncerramento;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="codigo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: flag_informacoes_complementares
	 */
	public java.lang.String getFlagInformacoesComplementares () {
		return getPropertyValue(this, flagInformacoesComplementares, PROP_FLAG_INFORMACOES_COMPLEMENTARES); 
	}

	/**
	 * Set the value related to the column: flag_informacoes_complementares
	 * @param flagInformacoesComplementares the flag_informacoes_complementares value
	 */
	public void setFlagInformacoesComplementares (java.lang.String flagInformacoesComplementares) {
//        java.lang.String flagInformacoesComplementaresOld = this.flagInformacoesComplementares;
		this.flagInformacoesComplementares = flagInformacoesComplementares;
//        this.getPropertyChangeSupport().firePropertyChange ("flagInformacoesComplementares", flagInformacoesComplementaresOld, flagInformacoesComplementares);
	}



	/**
	 * Return the value associated with the column: nr_casos_suspeitos_ate_data_notificacao
	 */
	public java.lang.String getNumeroCasosSuspeitosAteDataNotificacao () {
		return getPropertyValue(this, numeroCasosSuspeitosAteDataNotificacao, PROP_NUMERO_CASOS_SUSPEITOS_ATE_DATA_NOTIFICACAO); 
	}

	/**
	 * Set the value related to the column: nr_casos_suspeitos_ate_data_notificacao
	 * @param numeroCasosSuspeitosAteDataNotificacao the nr_casos_suspeitos_ate_data_notificacao value
	 */
	public void setNumeroCasosSuspeitosAteDataNotificacao (java.lang.String numeroCasosSuspeitosAteDataNotificacao) {
//        java.lang.String numeroCasosSuspeitosAteDataNotificacaoOld = this.numeroCasosSuspeitosAteDataNotificacao;
		this.numeroCasosSuspeitosAteDataNotificacao = numeroCasosSuspeitosAteDataNotificacao;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroCasosSuspeitosAteDataNotificacao", numeroCasosSuspeitosAteDataNotificacaoOld, numeroCasosSuspeitosAteDataNotificacao);
	}



	/**
	 * Return the value associated with the column: local_inicial_ocorrencia_surto
	 */
	public java.lang.Long getLocalInicialOcorrenciaSurto () {
		return getPropertyValue(this, localInicialOcorrenciaSurto, PROP_LOCAL_INICIAL_OCORRENCIA_SURTO); 
	}

	/**
	 * Set the value related to the column: local_inicial_ocorrencia_surto
	 * @param localInicialOcorrenciaSurto the local_inicial_ocorrencia_surto value
	 */
	public void setLocalInicialOcorrenciaSurto (java.lang.Long localInicialOcorrenciaSurto) {
//        java.lang.Long localInicialOcorrenciaSurtoOld = this.localInicialOcorrenciaSurto;
		this.localInicialOcorrenciaSurto = localInicialOcorrenciaSurto;
//        this.getPropertyChangeSupport().firePropertyChange ("localInicialOcorrenciaSurto", localInicialOcorrenciaSurtoOld, localInicialOcorrenciaSurto);
	}



	/**
	 * Return the value associated with the column: dt_investigacao
	 */
	public java.util.Date getDataInvestigacao () {
		return getPropertyValue(this, dataInvestigacao, PROP_DATA_INVESTIGACAO); 
	}

	/**
	 * Set the value related to the column: dt_investigacao
	 * @param dataInvestigacao the dt_investigacao value
	 */
	public void setDataInvestigacao (java.util.Date dataInvestigacao) {
//        java.util.Date dataInvestigacaoOld = this.dataInvestigacao;
		this.dataInvestigacao = dataInvestigacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInvestigacao", dataInvestigacaoOld, dataInvestigacao);
	}



	/**
	 * Return the value associated with the column: modo_provavel_transmissao
	 */
	public java.lang.Long getModoProvavelTransmissao () {
		return getPropertyValue(this, modoProvavelTransmissao, PROP_MODO_PROVAVEL_TRANSMISSAO); 
	}

	/**
	 * Set the value related to the column: modo_provavel_transmissao
	 * @param modoProvavelTransmissao the modo_provavel_transmissao value
	 */
	public void setModoProvavelTransmissao (java.lang.Long modoProvavelTransmissao) {
//        java.lang.Long modoProvavelTransmissaoOld = this.modoProvavelTransmissao;
		this.modoProvavelTransmissao = modoProvavelTransmissao;
//        this.getPropertyChangeSupport().firePropertyChange ("modoProvavelTransmissao", modoProvavelTransmissaoOld, modoProvavelTransmissao);
	}



	/**
	 * Return the value associated with the column: veiculo_transmissao_indireto
	 */
	public java.lang.Long getVeiculoTransmissaoIndireto () {
		return getPropertyValue(this, veiculoTransmissaoIndireto, PROP_VEICULO_TRANSMISSAO_INDIRETO); 
	}

	/**
	 * Set the value related to the column: veiculo_transmissao_indireto
	 * @param veiculoTransmissaoIndireto the veiculo_transmissao_indireto value
	 */
	public void setVeiculoTransmissaoIndireto (java.lang.Long veiculoTransmissaoIndireto) {
//        java.lang.Long veiculoTransmissaoIndiretoOld = this.veiculoTransmissaoIndireto;
		this.veiculoTransmissaoIndireto = veiculoTransmissaoIndireto;
//        this.getPropertyChangeSupport().firePropertyChange ("veiculoTransmissaoIndireto", veiculoTransmissaoIndiretoOld, veiculoTransmissaoIndireto);
	}



	/**
	 * Return the value associated with the column: veiculo_transmissao_indireto_outro
	 */
	public java.lang.String getVeiculoTransmissaoIndiretoOutro () {
		return getPropertyValue(this, veiculoTransmissaoIndiretoOutro, PROP_VEICULO_TRANSMISSAO_INDIRETO_OUTRO); 
	}

	/**
	 * Set the value related to the column: veiculo_transmissao_indireto_outro
	 * @param veiculoTransmissaoIndiretoOutro the veiculo_transmissao_indireto_outro value
	 */
	public void setVeiculoTransmissaoIndiretoOutro (java.lang.String veiculoTransmissaoIndiretoOutro) {
//        java.lang.String veiculoTransmissaoIndiretoOutroOld = this.veiculoTransmissaoIndiretoOutro;
		this.veiculoTransmissaoIndiretoOutro = veiculoTransmissaoIndiretoOutro;
//        this.getPropertyChangeSupport().firePropertyChange ("veiculoTransmissaoIndiretoOutro", veiculoTransmissaoIndiretoOutroOld, veiculoTransmissaoIndiretoOutro);
	}



	/**
	 * Return the value associated with the column: nr_entrevistados
	 */
	public java.lang.String getNumeroEntrevistados () {
		return getPropertyValue(this, numeroEntrevistados, PROP_NUMERO_ENTREVISTADOS); 
	}

	/**
	 * Set the value related to the column: nr_entrevistados
	 * @param numeroEntrevistados the nr_entrevistados value
	 */
	public void setNumeroEntrevistados (java.lang.String numeroEntrevistados) {
//        java.lang.String numeroEntrevistadosOld = this.numeroEntrevistados;
		this.numeroEntrevistados = numeroEntrevistados;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroEntrevistados", numeroEntrevistadosOld, numeroEntrevistados);
	}



	/**
	 * Return the value associated with the column: nr_doentes_entrevistados
	 */
	public java.lang.String getNumeroDoentesEntrevistados () {
		return getPropertyValue(this, numeroDoentesEntrevistados, PROP_NUMERO_DOENTES_ENTREVISTADOS); 
	}

	/**
	 * Set the value related to the column: nr_doentes_entrevistados
	 * @param numeroDoentesEntrevistados the nr_doentes_entrevistados value
	 */
	public void setNumeroDoentesEntrevistados (java.lang.String numeroDoentesEntrevistados) {
//        java.lang.String numeroDoentesEntrevistadosOld = this.numeroDoentesEntrevistados;
		this.numeroDoentesEntrevistados = numeroDoentesEntrevistados;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDoentesEntrevistados", numeroDoentesEntrevistadosOld, numeroDoentesEntrevistados);
	}



	/**
	 * Return the value associated with the column: nr_total_doentes
	 */
	public java.lang.String getNumeroTotalDoentes () {
		return getPropertyValue(this, numeroTotalDoentes, PROP_NUMERO_TOTAL_DOENTES); 
	}

	/**
	 * Set the value related to the column: nr_total_doentes
	 * @param numeroTotalDoentes the nr_total_doentes value
	 */
	public void setNumeroTotalDoentes (java.lang.String numeroTotalDoentes) {
//        java.lang.String numeroTotalDoentesOld = this.numeroTotalDoentes;
		this.numeroTotalDoentes = numeroTotalDoentes;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroTotalDoentes", numeroTotalDoentesOld, numeroTotalDoentes);
	}



	/**
	 * Return the value associated with the column: nr_total_hospitalizados
	 */
	public java.lang.String getNumeroTotalHospitalizados () {
		return getPropertyValue(this, numeroTotalHospitalizados, PROP_NUMERO_TOTAL_HOSPITALIZADOS); 
	}

	/**
	 * Set the value related to the column: nr_total_hospitalizados
	 * @param numeroTotalHospitalizados the nr_total_hospitalizados value
	 */
	public void setNumeroTotalHospitalizados (java.lang.String numeroTotalHospitalizados) {
//        java.lang.String numeroTotalHospitalizadosOld = this.numeroTotalHospitalizados;
		this.numeroTotalHospitalizados = numeroTotalHospitalizados;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroTotalHospitalizados", numeroTotalHospitalizadosOld, numeroTotalHospitalizados);
	}



	/**
	 * Return the value associated with the column: nr_obitos
	 */
	public java.lang.String getNumeroObitos () {
		return getPropertyValue(this, numeroObitos, PROP_NUMERO_OBITOS); 
	}

	/**
	 * Set the value related to the column: nr_obitos
	 * @param numeroObitos the nr_obitos value
	 */
	public void setNumeroObitos (java.lang.String numeroObitos) {
//        java.lang.String numeroObitosOld = this.numeroObitos;
		this.numeroObitos = numeroObitos;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroObitos", numeroObitosOld, numeroObitos);
	}



	/**
	 * Return the value associated with the column: nr_doentes_faixa_etaria_1_masc
	 */
	public java.lang.String getNumeroDoentesFaixaEtaria1Masculino () {
		return getPropertyValue(this, numeroDoentesFaixaEtaria1Masculino, PROP_NUMERO_DOENTES_FAIXA_ETARIA1_MASCULINO); 
	}

	/**
	 * Set the value related to the column: nr_doentes_faixa_etaria_1_masc
	 * @param numeroDoentesFaixaEtaria1Masculino the nr_doentes_faixa_etaria_1_masc value
	 */
	public void setNumeroDoentesFaixaEtaria1Masculino (java.lang.String numeroDoentesFaixaEtaria1Masculino) {
//        java.lang.String numeroDoentesFaixaEtaria1MasculinoOld = this.numeroDoentesFaixaEtaria1Masculino;
		this.numeroDoentesFaixaEtaria1Masculino = numeroDoentesFaixaEtaria1Masculino;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDoentesFaixaEtaria1Masculino", numeroDoentesFaixaEtaria1MasculinoOld, numeroDoentesFaixaEtaria1Masculino);
	}



	/**
	 * Return the value associated with the column: nr_doentes_faixa_etaria_1_fem
	 */
	public java.lang.String getNumeroDoentesFaixaEtaria1Feminino () {
		return getPropertyValue(this, numeroDoentesFaixaEtaria1Feminino, PROP_NUMERO_DOENTES_FAIXA_ETARIA1_FEMININO); 
	}

	/**
	 * Set the value related to the column: nr_doentes_faixa_etaria_1_fem
	 * @param numeroDoentesFaixaEtaria1Feminino the nr_doentes_faixa_etaria_1_fem value
	 */
	public void setNumeroDoentesFaixaEtaria1Feminino (java.lang.String numeroDoentesFaixaEtaria1Feminino) {
//        java.lang.String numeroDoentesFaixaEtaria1FemininoOld = this.numeroDoentesFaixaEtaria1Feminino;
		this.numeroDoentesFaixaEtaria1Feminino = numeroDoentesFaixaEtaria1Feminino;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDoentesFaixaEtaria1Feminino", numeroDoentesFaixaEtaria1FemininoOld, numeroDoentesFaixaEtaria1Feminino);
	}



	/**
	 * Return the value associated with the column: nr_doentes_faixa_etaria_1_ign
	 */
	public java.lang.String getNumeroDoentesFaixaEtaria1Ignorado () {
		return getPropertyValue(this, numeroDoentesFaixaEtaria1Ignorado, PROP_NUMERO_DOENTES_FAIXA_ETARIA1_IGNORADO); 
	}

	/**
	 * Set the value related to the column: nr_doentes_faixa_etaria_1_ign
	 * @param numeroDoentesFaixaEtaria1Ignorado the nr_doentes_faixa_etaria_1_ign value
	 */
	public void setNumeroDoentesFaixaEtaria1Ignorado (java.lang.String numeroDoentesFaixaEtaria1Ignorado) {
//        java.lang.String numeroDoentesFaixaEtaria1IgnoradoOld = this.numeroDoentesFaixaEtaria1Ignorado;
		this.numeroDoentesFaixaEtaria1Ignorado = numeroDoentesFaixaEtaria1Ignorado;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDoentesFaixaEtaria1Ignorado", numeroDoentesFaixaEtaria1IgnoradoOld, numeroDoentesFaixaEtaria1Ignorado);
	}



	/**
	 * Return the value associated with the column: nr_doentes_faixa_etaria_2_masc
	 */
	public java.lang.String getNumeroDoentesFaixaEtaria2Masculino () {
		return getPropertyValue(this, numeroDoentesFaixaEtaria2Masculino, PROP_NUMERO_DOENTES_FAIXA_ETARIA2_MASCULINO); 
	}

	/**
	 * Set the value related to the column: nr_doentes_faixa_etaria_2_masc
	 * @param numeroDoentesFaixaEtaria2Masculino the nr_doentes_faixa_etaria_2_masc value
	 */
	public void setNumeroDoentesFaixaEtaria2Masculino (java.lang.String numeroDoentesFaixaEtaria2Masculino) {
//        java.lang.String numeroDoentesFaixaEtaria2MasculinoOld = this.numeroDoentesFaixaEtaria2Masculino;
		this.numeroDoentesFaixaEtaria2Masculino = numeroDoentesFaixaEtaria2Masculino;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDoentesFaixaEtaria2Masculino", numeroDoentesFaixaEtaria2MasculinoOld, numeroDoentesFaixaEtaria2Masculino);
	}



	/**
	 * Return the value associated with the column: nr_doentes_faixa_etaria_2_fem
	 */
	public java.lang.String getNumeroDoentesFaixaEtaria2Feminino () {
		return getPropertyValue(this, numeroDoentesFaixaEtaria2Feminino, PROP_NUMERO_DOENTES_FAIXA_ETARIA2_FEMININO); 
	}

	/**
	 * Set the value related to the column: nr_doentes_faixa_etaria_2_fem
	 * @param numeroDoentesFaixaEtaria2Feminino the nr_doentes_faixa_etaria_2_fem value
	 */
	public void setNumeroDoentesFaixaEtaria2Feminino (java.lang.String numeroDoentesFaixaEtaria2Feminino) {
//        java.lang.String numeroDoentesFaixaEtaria2FemininoOld = this.numeroDoentesFaixaEtaria2Feminino;
		this.numeroDoentesFaixaEtaria2Feminino = numeroDoentesFaixaEtaria2Feminino;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDoentesFaixaEtaria2Feminino", numeroDoentesFaixaEtaria2FemininoOld, numeroDoentesFaixaEtaria2Feminino);
	}



	/**
	 * Return the value associated with the column: nr_doentes_faixa_etaria_2_ign
	 */
	public java.lang.String getNumeroDoentesFaixaEtaria2Ignorado () {
		return getPropertyValue(this, numeroDoentesFaixaEtaria2Ignorado, PROP_NUMERO_DOENTES_FAIXA_ETARIA2_IGNORADO); 
	}

	/**
	 * Set the value related to the column: nr_doentes_faixa_etaria_2_ign
	 * @param numeroDoentesFaixaEtaria2Ignorado the nr_doentes_faixa_etaria_2_ign value
	 */
	public void setNumeroDoentesFaixaEtaria2Ignorado (java.lang.String numeroDoentesFaixaEtaria2Ignorado) {
//        java.lang.String numeroDoentesFaixaEtaria2IgnoradoOld = this.numeroDoentesFaixaEtaria2Ignorado;
		this.numeroDoentesFaixaEtaria2Ignorado = numeroDoentesFaixaEtaria2Ignorado;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDoentesFaixaEtaria2Ignorado", numeroDoentesFaixaEtaria2IgnoradoOld, numeroDoentesFaixaEtaria2Ignorado);
	}



	/**
	 * Return the value associated with the column: nr_doentes_faixa_etaria_3_masc
	 */
	public java.lang.String getNumeroDoentesFaixaEtaria3Masculino () {
		return getPropertyValue(this, numeroDoentesFaixaEtaria3Masculino, PROP_NUMERO_DOENTES_FAIXA_ETARIA3_MASCULINO); 
	}

	/**
	 * Set the value related to the column: nr_doentes_faixa_etaria_3_masc
	 * @param numeroDoentesFaixaEtaria3Masculino the nr_doentes_faixa_etaria_3_masc value
	 */
	public void setNumeroDoentesFaixaEtaria3Masculino (java.lang.String numeroDoentesFaixaEtaria3Masculino) {
//        java.lang.String numeroDoentesFaixaEtaria3MasculinoOld = this.numeroDoentesFaixaEtaria3Masculino;
		this.numeroDoentesFaixaEtaria3Masculino = numeroDoentesFaixaEtaria3Masculino;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDoentesFaixaEtaria3Masculino", numeroDoentesFaixaEtaria3MasculinoOld, numeroDoentesFaixaEtaria3Masculino);
	}



	/**
	 * Return the value associated with the column: nr_doentes_faixa_etaria_3_fem
	 */
	public java.lang.String getNumeroDoentesFaixaEtaria3Feminino () {
		return getPropertyValue(this, numeroDoentesFaixaEtaria3Feminino, PROP_NUMERO_DOENTES_FAIXA_ETARIA3_FEMININO); 
	}

	/**
	 * Set the value related to the column: nr_doentes_faixa_etaria_3_fem
	 * @param numeroDoentesFaixaEtaria3Feminino the nr_doentes_faixa_etaria_3_fem value
	 */
	public void setNumeroDoentesFaixaEtaria3Feminino (java.lang.String numeroDoentesFaixaEtaria3Feminino) {
//        java.lang.String numeroDoentesFaixaEtaria3FemininoOld = this.numeroDoentesFaixaEtaria3Feminino;
		this.numeroDoentesFaixaEtaria3Feminino = numeroDoentesFaixaEtaria3Feminino;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDoentesFaixaEtaria3Feminino", numeroDoentesFaixaEtaria3FemininoOld, numeroDoentesFaixaEtaria3Feminino);
	}



	/**
	 * Return the value associated with the column: nr_doentes_faixa_etaria_3_ign
	 */
	public java.lang.String getNumeroDoentesFaixaEtaria3Ignorado () {
		return getPropertyValue(this, numeroDoentesFaixaEtaria3Ignorado, PROP_NUMERO_DOENTES_FAIXA_ETARIA3_IGNORADO); 
	}

	/**
	 * Set the value related to the column: nr_doentes_faixa_etaria_3_ign
	 * @param numeroDoentesFaixaEtaria3Ignorado the nr_doentes_faixa_etaria_3_ign value
	 */
	public void setNumeroDoentesFaixaEtaria3Ignorado (java.lang.String numeroDoentesFaixaEtaria3Ignorado) {
//        java.lang.String numeroDoentesFaixaEtaria3IgnoradoOld = this.numeroDoentesFaixaEtaria3Ignorado;
		this.numeroDoentesFaixaEtaria3Ignorado = numeroDoentesFaixaEtaria3Ignorado;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDoentesFaixaEtaria3Ignorado", numeroDoentesFaixaEtaria3IgnoradoOld, numeroDoentesFaixaEtaria3Ignorado);
	}



	/**
	 * Return the value associated with the column: nr_doentes_faixa_etaria_4_masc
	 */
	public java.lang.String getNumeroDoentesFaixaEtaria4Masculino () {
		return getPropertyValue(this, numeroDoentesFaixaEtaria4Masculino, PROP_NUMERO_DOENTES_FAIXA_ETARIA4_MASCULINO); 
	}

	/**
	 * Set the value related to the column: nr_doentes_faixa_etaria_4_masc
	 * @param numeroDoentesFaixaEtaria4Masculino the nr_doentes_faixa_etaria_4_masc value
	 */
	public void setNumeroDoentesFaixaEtaria4Masculino (java.lang.String numeroDoentesFaixaEtaria4Masculino) {
//        java.lang.String numeroDoentesFaixaEtaria4MasculinoOld = this.numeroDoentesFaixaEtaria4Masculino;
		this.numeroDoentesFaixaEtaria4Masculino = numeroDoentesFaixaEtaria4Masculino;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDoentesFaixaEtaria4Masculino", numeroDoentesFaixaEtaria4MasculinoOld, numeroDoentesFaixaEtaria4Masculino);
	}



	/**
	 * Return the value associated with the column: nr_doentes_faixa_etaria_4_fem
	 */
	public java.lang.String getNumeroDoentesFaixaEtaria4Feminino () {
		return getPropertyValue(this, numeroDoentesFaixaEtaria4Feminino, PROP_NUMERO_DOENTES_FAIXA_ETARIA4_FEMININO); 
	}

	/**
	 * Set the value related to the column: nr_doentes_faixa_etaria_4_fem
	 * @param numeroDoentesFaixaEtaria4Feminino the nr_doentes_faixa_etaria_4_fem value
	 */
	public void setNumeroDoentesFaixaEtaria4Feminino (java.lang.String numeroDoentesFaixaEtaria4Feminino) {
//        java.lang.String numeroDoentesFaixaEtaria4FemininoOld = this.numeroDoentesFaixaEtaria4Feminino;
		this.numeroDoentesFaixaEtaria4Feminino = numeroDoentesFaixaEtaria4Feminino;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDoentesFaixaEtaria4Feminino", numeroDoentesFaixaEtaria4FemininoOld, numeroDoentesFaixaEtaria4Feminino);
	}



	/**
	 * Return the value associated with the column: nr_doentes_faixa_etaria_4_ign
	 */
	public java.lang.String getNumeroDoentesFaixaEtaria4Ignorado () {
		return getPropertyValue(this, numeroDoentesFaixaEtaria4Ignorado, PROP_NUMERO_DOENTES_FAIXA_ETARIA4_IGNORADO); 
	}

	/**
	 * Set the value related to the column: nr_doentes_faixa_etaria_4_ign
	 * @param numeroDoentesFaixaEtaria4Ignorado the nr_doentes_faixa_etaria_4_ign value
	 */
	public void setNumeroDoentesFaixaEtaria4Ignorado (java.lang.String numeroDoentesFaixaEtaria4Ignorado) {
//        java.lang.String numeroDoentesFaixaEtaria4IgnoradoOld = this.numeroDoentesFaixaEtaria4Ignorado;
		this.numeroDoentesFaixaEtaria4Ignorado = numeroDoentesFaixaEtaria4Ignorado;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDoentesFaixaEtaria4Ignorado", numeroDoentesFaixaEtaria4IgnoradoOld, numeroDoentesFaixaEtaria4Ignorado);
	}



	/**
	 * Return the value associated with the column: nr_doentes_faixa_etaria_5_masc
	 */
	public java.lang.String getNumeroDoentesFaixaEtaria5Masculino () {
		return getPropertyValue(this, numeroDoentesFaixaEtaria5Masculino, PROP_NUMERO_DOENTES_FAIXA_ETARIA5_MASCULINO); 
	}

	/**
	 * Set the value related to the column: nr_doentes_faixa_etaria_5_masc
	 * @param numeroDoentesFaixaEtaria5Masculino the nr_doentes_faixa_etaria_5_masc value
	 */
	public void setNumeroDoentesFaixaEtaria5Masculino (java.lang.String numeroDoentesFaixaEtaria5Masculino) {
//        java.lang.String numeroDoentesFaixaEtaria5MasculinoOld = this.numeroDoentesFaixaEtaria5Masculino;
		this.numeroDoentesFaixaEtaria5Masculino = numeroDoentesFaixaEtaria5Masculino;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDoentesFaixaEtaria5Masculino", numeroDoentesFaixaEtaria5MasculinoOld, numeroDoentesFaixaEtaria5Masculino);
	}



	/**
	 * Return the value associated with the column: nr_doentes_faixa_etaria_5_fem
	 */
	public java.lang.String getNumeroDoentesFaixaEtaria5Feminino() {
		return getPropertyValue(this, numeroDoentesFaixaEtaria5Feminino, PROP_NUMERO_DOENTES_FAIXA_ETARIA5_FEMININO);
	}

	/**
	 * Set the value related to the column: nr_doentes_faixa_etaria_5_fem
	 * @param numeroDoentesFaixaEtaria5Feminino the nr_doentes_faixa_etaria_5_fem value
	 */
	public void setNumeroDoentesFaixaEtaria5Feminino(java.lang.String numeroDoentesFaixaEtaria5Feminino) {
//        java.lang.String numeroDoentesFaixaEtaria5FemininoininoOld = this.numeroDoentesFaixaEtaria5Femininoinino;
		this.numeroDoentesFaixaEtaria5Feminino = numeroDoentesFaixaEtaria5Feminino;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDoentesFaixaEtaria5Femininoinino", numeroDoentesFaixaEtaria5FemininoininoOld, numeroDoentesFaixaEtaria5Femininoinino);
	}



	/**
	 * Return the value associated with the column: nr_doentes_faixa_etaria_5_ign
	 */
	public java.lang.String getNumeroDoentesFaixaEtaria5Ignorado () {
		return getPropertyValue(this, numeroDoentesFaixaEtaria5Ignorado, PROP_NUMERO_DOENTES_FAIXA_ETARIA5_IGNORADO); 
	}

	/**
	 * Set the value related to the column: nr_doentes_faixa_etaria_5_ign
	 * @param numeroDoentesFaixaEtaria5Ignorado the nr_doentes_faixa_etaria_5_ign value
	 */
	public void setNumeroDoentesFaixaEtaria5Ignorado (java.lang.String numeroDoentesFaixaEtaria5Ignorado) {
//        java.lang.String numeroDoentesFaixaEtaria5IgnoradoOld = this.numeroDoentesFaixaEtaria5Ignorado;
		this.numeroDoentesFaixaEtaria5Ignorado = numeroDoentesFaixaEtaria5Ignorado;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDoentesFaixaEtaria5Ignorado", numeroDoentesFaixaEtaria5IgnoradoOld, numeroDoentesFaixaEtaria5Ignorado);
	}



	/**
	 * Return the value associated with the column: nr_doentes_faixa_etaria_6_masc
	 */
	public java.lang.String getNumeroDoentesFaixaEtaria6Masculino () {
		return getPropertyValue(this, numeroDoentesFaixaEtaria6Masculino, PROP_NUMERO_DOENTES_FAIXA_ETARIA6_MASCULINO); 
	}

	/**
	 * Set the value related to the column: nr_doentes_faixa_etaria_6_masc
	 * @param numeroDoentesFaixaEtaria6Masculino the nr_doentes_faixa_etaria_6_masc value
	 */
	public void setNumeroDoentesFaixaEtaria6Masculino (java.lang.String numeroDoentesFaixaEtaria6Masculino) {
//        java.lang.String numeroDoentesFaixaEtaria6MasculinoOld = this.numeroDoentesFaixaEtaria6Masculino;
		this.numeroDoentesFaixaEtaria6Masculino = numeroDoentesFaixaEtaria6Masculino;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDoentesFaixaEtaria6Masculino", numeroDoentesFaixaEtaria6MasculinoOld, numeroDoentesFaixaEtaria6Masculino);
	}



	/**
	 * Return the value associated with the column: nr_doentes_faixa_etaria_6_fem
	 */
	public java.lang.String getNumeroDoentesFaixaEtaria6Feminino () {
		return getPropertyValue(this, numeroDoentesFaixaEtaria6Feminino, PROP_NUMERO_DOENTES_FAIXA_ETARIA6_FEMININO); 
	}

	/**
	 * Set the value related to the column: nr_doentes_faixa_etaria_6_fem
	 * @param numeroDoentesFaixaEtaria6Feminino the nr_doentes_faixa_etaria_6_fem value
	 */
	public void setNumeroDoentesFaixaEtaria6Feminino (java.lang.String numeroDoentesFaixaEtaria6Feminino) {
//        java.lang.String numeroDoentesFaixaEtaria6FemininoOld = this.numeroDoentesFaixaEtaria6Feminino;
		this.numeroDoentesFaixaEtaria6Feminino = numeroDoentesFaixaEtaria6Feminino;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDoentesFaixaEtaria6Feminino", numeroDoentesFaixaEtaria6FemininoOld, numeroDoentesFaixaEtaria6Feminino);
	}



	/**
	 * Return the value associated with the column: nr_doentes_faixa_etaria_6_ign
	 */
	public java.lang.String getNumeroDoentesFaixaEtaria6Ignorado () {
		return getPropertyValue(this, numeroDoentesFaixaEtaria6Ignorado, PROP_NUMERO_DOENTES_FAIXA_ETARIA6_IGNORADO); 
	}

	/**
	 * Set the value related to the column: nr_doentes_faixa_etaria_6_ign
	 * @param numeroDoentesFaixaEtaria6Ignorado the nr_doentes_faixa_etaria_6_ign value
	 */
	public void setNumeroDoentesFaixaEtaria6Ignorado (java.lang.String numeroDoentesFaixaEtaria6Ignorado) {
//        java.lang.String numeroDoentesFaixaEtaria6IgnoradoOld = this.numeroDoentesFaixaEtaria6Ignorado;
		this.numeroDoentesFaixaEtaria6Ignorado = numeroDoentesFaixaEtaria6Ignorado;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDoentesFaixaEtaria6Ignorado", numeroDoentesFaixaEtaria6IgnoradoOld, numeroDoentesFaixaEtaria6Ignorado);
	}



	/**
	 * Return the value associated with the column: nr_doentes_faixa_etaria_7_masc
	 */
	public java.lang.String getNumeroDoentesFaixaEtaria7Masculino () {
		return getPropertyValue(this, numeroDoentesFaixaEtaria7Masculino, PROP_NUMERO_DOENTES_FAIXA_ETARIA7_MASCULINO); 
	}

	/**
	 * Set the value related to the column: nr_doentes_faixa_etaria_7_masc
	 * @param numeroDoentesFaixaEtaria7Masculino the nr_doentes_faixa_etaria_7_masc value
	 */
	public void setNumeroDoentesFaixaEtaria7Masculino (java.lang.String numeroDoentesFaixaEtaria7Masculino) {
//        java.lang.String numeroDoentesFaixaEtaria7MasculinoOld = this.numeroDoentesFaixaEtaria7Masculino;
		this.numeroDoentesFaixaEtaria7Masculino = numeroDoentesFaixaEtaria7Masculino;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDoentesFaixaEtaria7Masculino", numeroDoentesFaixaEtaria7MasculinoOld, numeroDoentesFaixaEtaria7Masculino);
	}



	/**
	 * Return the value associated with the column: nr_doentes_faixa_etaria_7_fem
	 */
	public java.lang.String getNumeroDoentesFaixaEtaria7Feminino () {
		return getPropertyValue(this, numeroDoentesFaixaEtaria7Feminino, PROP_NUMERO_DOENTES_FAIXA_ETARIA7_FEMININO); 
	}

	/**
	 * Set the value related to the column: nr_doentes_faixa_etaria_7_fem
	 * @param numeroDoentesFaixaEtaria7Feminino the nr_doentes_faixa_etaria_7_fem value
	 */
	public void setNumeroDoentesFaixaEtaria7Feminino (java.lang.String numeroDoentesFaixaEtaria7Feminino) {
//        java.lang.String numeroDoentesFaixaEtaria7FemininoOld = this.numeroDoentesFaixaEtaria7Feminino;
		this.numeroDoentesFaixaEtaria7Feminino = numeroDoentesFaixaEtaria7Feminino;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDoentesFaixaEtaria7Feminino", numeroDoentesFaixaEtaria7FemininoOld, numeroDoentesFaixaEtaria7Feminino);
	}



	/**
	 * Return the value associated with the column: nr_doentes_faixa_etaria_7_ign
	 */
	public java.lang.String getNumeroDoentesFaixaEtaria7Ignorado () {
		return getPropertyValue(this, numeroDoentesFaixaEtaria7Ignorado, PROP_NUMERO_DOENTES_FAIXA_ETARIA7_IGNORADO); 
	}

	/**
	 * Set the value related to the column: nr_doentes_faixa_etaria_7_ign
	 * @param numeroDoentesFaixaEtaria7Ignorado the nr_doentes_faixa_etaria_7_ign value
	 */
	public void setNumeroDoentesFaixaEtaria7Ignorado (java.lang.String numeroDoentesFaixaEtaria7Ignorado) {
//        java.lang.String numeroDoentesFaixaEtaria7IgnoradoOld = this.numeroDoentesFaixaEtaria7Ignorado;
		this.numeroDoentesFaixaEtaria7Ignorado = numeroDoentesFaixaEtaria7Ignorado;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDoentesFaixaEtaria7Ignorado", numeroDoentesFaixaEtaria7IgnoradoOld, numeroDoentesFaixaEtaria7Ignorado);
	}



	/**
	 * Return the value associated with the column: sinal_sintoma_nausea
	 */
	public java.lang.String getSinalSintomaNausea () {
		return getPropertyValue(this, sinalSintomaNausea, PROP_SINAL_SINTOMA_NAUSEA); 
	}

	/**
	 * Set the value related to the column: sinal_sintoma_nausea
	 * @param sinalSintomaNausea the sinal_sintoma_nausea value
	 */
	public void setSinalSintomaNausea (java.lang.String sinalSintomaNausea) {
//        java.lang.String sinalSintomaNauseaOld = this.sinalSintomaNausea;
		this.sinalSintomaNausea = sinalSintomaNausea;
//        this.getPropertyChangeSupport().firePropertyChange ("sinalSintomaNausea", sinalSintomaNauseaOld, sinalSintomaNausea);
	}



	/**
	 * Return the value associated with the column: sinal_sintoma_vomito
	 */
	public java.lang.String getSinalSintomaVomito () {
		return getPropertyValue(this, sinalSintomaVomito, PROP_SINAL_SINTOMA_VOMITO); 
	}

	/**
	 * Set the value related to the column: sinal_sintoma_vomito
	 * @param sinalSintomaVomito the sinal_sintoma_vomito value
	 */
	public void setSinalSintomaVomito (java.lang.String sinalSintomaVomito) {
//        java.lang.String sinalSintomaVomitoOld = this.sinalSintomaVomito;
		this.sinalSintomaVomito = sinalSintomaVomito;
//        this.getPropertyChangeSupport().firePropertyChange ("sinalSintomaVomito", sinalSintomaVomitoOld, sinalSintomaVomito);
	}



	/**
	 * Return the value associated with the column: sinal_sintoma_diarreia
	 */
	public java.lang.String getSinalSintomaDiarreia () {
		return getPropertyValue(this, sinalSintomaDiarreia, PROP_SINAL_SINTOMA_DIARREIA); 
	}

	/**
	 * Set the value related to the column: sinal_sintoma_diarreia
	 * @param sinalSintomaDiarreia the sinal_sintoma_diarreia value
	 */
	public void setSinalSintomaDiarreia (java.lang.String sinalSintomaDiarreia) {
//        java.lang.String sinalSintomaDiarreiaOld = this.sinalSintomaDiarreia;
		this.sinalSintomaDiarreia = sinalSintomaDiarreia;
//        this.getPropertyChangeSupport().firePropertyChange ("sinalSintomaDiarreia", sinalSintomaDiarreiaOld, sinalSintomaDiarreia);
	}



	/**
	 * Return the value associated with the column: sinal_sintoma_cefaleia
	 */
	public java.lang.String getSinalSintomaCefaleia () {
		return getPropertyValue(this, sinalSintomaCefaleia, PROP_SINAL_SINTOMA_CEFALEIA); 
	}

	/**
	 * Set the value related to the column: sinal_sintoma_cefaleia
	 * @param sinalSintomaCefaleia the sinal_sintoma_cefaleia value
	 */
	public void setSinalSintomaCefaleia (java.lang.String sinalSintomaCefaleia) {
//        java.lang.String sinalSintomaCefaleiaOld = this.sinalSintomaCefaleia;
		this.sinalSintomaCefaleia = sinalSintomaCefaleia;
//        this.getPropertyChangeSupport().firePropertyChange ("sinalSintomaCefaleia", sinalSintomaCefaleiaOld, sinalSintomaCefaleia);
	}



	/**
	 * Return the value associated with the column: sinal_sintoma_dor_abdominal
	 */
	public java.lang.String getSinalSintomaDorAbdominal () {
		return getPropertyValue(this, sinalSintomaDorAbdominal, PROP_SINAL_SINTOMA_DOR_ABDOMINAL); 
	}

	/**
	 * Set the value related to the column: sinal_sintoma_dor_abdominal
	 * @param sinalSintomaDorAbdominal the sinal_sintoma_dor_abdominal value
	 */
	public void setSinalSintomaDorAbdominal (java.lang.String sinalSintomaDorAbdominal) {
//        java.lang.String sinalSintomaDorAbdominalOld = this.sinalSintomaDorAbdominal;
		this.sinalSintomaDorAbdominal = sinalSintomaDorAbdominal;
//        this.getPropertyChangeSupport().firePropertyChange ("sinalSintomaDorAbdominal", sinalSintomaDorAbdominalOld, sinalSintomaDorAbdominal);
	}



	/**
	 * Return the value associated with the column: sinal_sintoma_neurologicos
	 */
	public java.lang.String getSinalSintomaNeurologicos () {
		return getPropertyValue(this, sinalSintomaNeurologicos, PROP_SINAL_SINTOMA_NEUROLOGICOS); 
	}

	/**
	 * Set the value related to the column: sinal_sintoma_neurologicos
	 * @param sinalSintomaNeurologicos the sinal_sintoma_neurologicos value
	 */
	public void setSinalSintomaNeurologicos (java.lang.String sinalSintomaNeurologicos) {
//        java.lang.String sinalSintomaNeurologicosOld = this.sinalSintomaNeurologicos;
		this.sinalSintomaNeurologicos = sinalSintomaNeurologicos;
//        this.getPropertyChangeSupport().firePropertyChange ("sinalSintomaNeurologicos", sinalSintomaNeurologicosOld, sinalSintomaNeurologicos);
	}



	/**
	 * Return the value associated with the column: sinal_sintoma_outro
	 */
	public java.lang.String getSinalSintomaOutro () {
		return getPropertyValue(this, sinalSintomaOutro, PROP_SINAL_SINTOMA_OUTRO); 
	}

	/**
	 * Set the value related to the column: sinal_sintoma_outro
	 * @param sinalSintomaOutro the sinal_sintoma_outro value
	 */
	public void setSinalSintomaOutro (java.lang.String sinalSintomaOutro) {
//        java.lang.String sinalSintomaOutroOld = this.sinalSintomaOutro;
		this.sinalSintomaOutro = sinalSintomaOutro;
//        this.getPropertyChangeSupport().firePropertyChange ("sinalSintomaOutro", sinalSintomaOutroOld, sinalSintomaOutro);
	}



	/**
	 * Return the value associated with the column: sinal_sintoma_febre
	 */
	public java.lang.String getSinalSintomaFebre () {
		return getPropertyValue(this, sinalSintomaFebre, PROP_SINAL_SINTOMA_FEBRE); 
	}

	/**
	 * Set the value related to the column: sinal_sintoma_febre
	 * @param sinalSintomaFebre the sinal_sintoma_febre value
	 */
	public void setSinalSintomaFebre (java.lang.String sinalSintomaFebre) {
//        java.lang.String sinalSintomaFebreOld = this.sinalSintomaFebre;
		this.sinalSintomaFebre = sinalSintomaFebre;
//        this.getPropertyChangeSupport().firePropertyChange ("sinalSintomaFebre", sinalSintomaFebreOld, sinalSintomaFebre);
	}



	/**
	 * Return the value associated with the column: periodo_incubacao_minimo
	 */
	public java.lang.String getPeriodoIncubacaoMinimo () {
		return getPropertyValue(this, periodoIncubacaoMinimo, PROP_PERIODO_INCUBACAO_MINIMO); 
	}

	/**
	 * Set the value related to the column: periodo_incubacao_minimo
	 * @param periodoIncubacaoMinimo the periodo_incubacao_minimo value
	 */
	public void setPeriodoIncubacaoMinimo (java.lang.String periodoIncubacaoMinimo) {
//        java.lang.String periodoIncubacaoMinimoOld = this.periodoIncubacaoMinimo;
		this.periodoIncubacaoMinimo = periodoIncubacaoMinimo;
//        this.getPropertyChangeSupport().firePropertyChange ("periodoIncubacaoMinimo", periodoIncubacaoMinimoOld, periodoIncubacaoMinimo);
	}



	/**
	 * Return the value associated with the column: periodo_incubacao_minimo_um
	 */
	public java.lang.Long getPeriodoIncubacaoMinimoUnidadeMedida () {
		return getPropertyValue(this, periodoIncubacaoMinimoUnidadeMedida, PROP_PERIODO_INCUBACAO_MINIMO_UNIDADE_MEDIDA); 
	}

	/**
	 * Set the value related to the column: periodo_incubacao_minimo_um
	 * @param periodoIncubacaoMinimoUnidadeMedida the periodo_incubacao_minimo_um value
	 */
	public void setPeriodoIncubacaoMinimoUnidadeMedida (java.lang.Long periodoIncubacaoMinimoUnidadeMedida) {
//        java.lang.Long periodoIncubacaoMinimoUnidadeMedidaOld = this.periodoIncubacaoMinimoUnidadeMedida;
		this.periodoIncubacaoMinimoUnidadeMedida = periodoIncubacaoMinimoUnidadeMedida;
//        this.getPropertyChangeSupport().firePropertyChange ("periodoIncubacaoMinimoUnidadeMedida", periodoIncubacaoMinimoUnidadeMedidaOld, periodoIncubacaoMinimoUnidadeMedida);
	}



	/**
	 * Return the value associated with the column: periodo_incubacao_maximo
	 */
	public java.lang.String getPeriodoIncubacaoMaximo () {
		return getPropertyValue(this, periodoIncubacaoMaximo, PROP_PERIODO_INCUBACAO_MAXIMO); 
	}

	/**
	 * Set the value related to the column: periodo_incubacao_maximo
	 * @param periodoIncubacaoMaximo the periodo_incubacao_maximo value
	 */
	public void setPeriodoIncubacaoMaximo (java.lang.String periodoIncubacaoMaximo) {
//        java.lang.String periodoIncubacaoMaximoOld = this.periodoIncubacaoMaximo;
		this.periodoIncubacaoMaximo = periodoIncubacaoMaximo;
//        this.getPropertyChangeSupport().firePropertyChange ("periodoIncubacaoMaximo", periodoIncubacaoMaximoOld, periodoIncubacaoMaximo);
	}



	/**
	 * Return the value associated with the column: periodo_incubacao_maximo_um
	 */
	public java.lang.Long getPeriodoIncubacaoMaximoUnidadeMedida () {
		return getPropertyValue(this, periodoIncubacaoMaximoUnidadeMedida, PROP_PERIODO_INCUBACAO_MAXIMO_UNIDADE_MEDIDA); 
	}

	/**
	 * Set the value related to the column: periodo_incubacao_maximo_um
	 * @param periodoIncubacaoMaximoUnidadeMedida the periodo_incubacao_maximo_um value
	 */
	public void setPeriodoIncubacaoMaximoUnidadeMedida (java.lang.Long periodoIncubacaoMaximoUnidadeMedida) {
//        java.lang.Long periodoIncubacaoMaximoUnidadeMedidaOld = this.periodoIncubacaoMaximoUnidadeMedida;
		this.periodoIncubacaoMaximoUnidadeMedida = periodoIncubacaoMaximoUnidadeMedida;
//        this.getPropertyChangeSupport().firePropertyChange ("periodoIncubacaoMaximoUnidadeMedida", periodoIncubacaoMaximoUnidadeMedidaOld, periodoIncubacaoMaximoUnidadeMedida);
	}



	/**
	 * Return the value associated with the column: periodo_incubacao_mediana
	 */
	public java.lang.String getPeriodoIncubacaoMediana () {
		return getPropertyValue(this, periodoIncubacaoMediana, PROP_PERIODO_INCUBACAO_MEDIANA); 
	}

	/**
	 * Set the value related to the column: periodo_incubacao_mediana
	 * @param periodoIncubacaoMediana the periodo_incubacao_mediana value
	 */
	public void setPeriodoIncubacaoMediana (java.lang.String periodoIncubacaoMediana) {
//        java.lang.String periodoIncubacaoMedianaOld = this.periodoIncubacaoMediana;
		this.periodoIncubacaoMediana = periodoIncubacaoMediana;
//        this.getPropertyChangeSupport().firePropertyChange ("periodoIncubacaoMediana", periodoIncubacaoMedianaOld, periodoIncubacaoMediana);
	}



	/**
	 * Return the value associated with the column: periodo_incubacao_mediana_um
	 */
	public java.lang.Long getPeriodoIncubacaoMedianaUnidadeMedida () {
		return getPropertyValue(this, periodoIncubacaoMedianaUnidadeMedida, PROP_PERIODO_INCUBACAO_MEDIANA_UNIDADE_MEDIDA); 
	}

	/**
	 * Set the value related to the column: periodo_incubacao_mediana_um
	 * @param periodoIncubacaoMedianaUnidadeMedida the periodo_incubacao_mediana_um value
	 */
	public void setPeriodoIncubacaoMedianaUnidadeMedida (java.lang.Long periodoIncubacaoMedianaUnidadeMedida) {
//        java.lang.Long periodoIncubacaoMedianaUnidadeMedidaOld = this.periodoIncubacaoMedianaUnidadeMedida;
		this.periodoIncubacaoMedianaUnidadeMedida = periodoIncubacaoMedianaUnidadeMedida;
//        this.getPropertyChangeSupport().firePropertyChange ("periodoIncubacaoMedianaUnidadeMedida", periodoIncubacaoMedianaUnidadeMedidaOld, periodoIncubacaoMedianaUnidadeMedida);
	}



	/**
	 * Return the value associated with the column: local_producao_preparacao
	 */
	public java.lang.Long getLocalProducaoPreparacao () {
		return getPropertyValue(this, localProducaoPreparacao, PROP_LOCAL_PRODUCAO_PREPARACAO); 
	}

	/**
	 * Set the value related to the column: local_producao_preparacao
	 * @param localProducaoPreparacao the local_producao_preparacao value
	 */
	public void setLocalProducaoPreparacao (java.lang.Long localProducaoPreparacao) {
//        java.lang.Long localProducaoPreparacaoOld = this.localProducaoPreparacao;
		this.localProducaoPreparacao = localProducaoPreparacao;
//        this.getPropertyChangeSupport().firePropertyChange ("localProducaoPreparacao", localProducaoPreparacaoOld, localProducaoPreparacao);
	}



	/**
	 * Return the value associated with the column: local_producao_preparacao_outro
	 */
	public java.lang.String getLocalProducaoPreparacaoOutro () {
		return getPropertyValue(this, localProducaoPreparacaoOutro, PROP_LOCAL_PRODUCAO_PREPARACAO_OUTRO); 
	}

	/**
	 * Set the value related to the column: local_producao_preparacao_outro
	 * @param localProducaoPreparacaoOutro the local_producao_preparacao_outro value
	 */
	public void setLocalProducaoPreparacaoOutro (java.lang.String localProducaoPreparacaoOutro) {
//        java.lang.String localProducaoPreparacaoOutroOld = this.localProducaoPreparacaoOutro;
		this.localProducaoPreparacaoOutro = localProducaoPreparacaoOutro;
//        this.getPropertyChangeSupport().firePropertyChange ("localProducaoPreparacaoOutro", localProducaoPreparacaoOutroOld, localProducaoPreparacaoOutro);
	}



	/**
	 * Return the value associated with the column: local_ingestao
	 */
	public java.lang.Long getLocalIngestao () {
		return getPropertyValue(this, localIngestao, PROP_LOCAL_INGESTAO); 
	}

	/**
	 * Set the value related to the column: local_ingestao
	 * @param localIngestao the local_ingestao value
	 */
	public void setLocalIngestao (java.lang.Long localIngestao) {
//        java.lang.Long localIngestaoOld = this.localIngestao;
		this.localIngestao = localIngestao;
//        this.getPropertyChangeSupport().firePropertyChange ("localIngestao", localIngestaoOld, localIngestao);
	}



	/**
	 * Return the value associated with the column: local_ingestao_outro
	 */
	public java.lang.String getLocalIngestaoOutro () {
		return getPropertyValue(this, localIngestaoOutro, PROP_LOCAL_INGESTAO_OUTRO); 
	}

	/**
	 * Set the value related to the column: local_ingestao_outro
	 * @param localIngestaoOutro the local_ingestao_outro value
	 */
	public void setLocalIngestaoOutro (java.lang.String localIngestaoOutro) {
//        java.lang.String localIngestaoOutroOld = this.localIngestaoOutro;
		this.localIngestaoOutro = localIngestaoOutro;
//        this.getPropertyChangeSupport().firePropertyChange ("localIngestaoOutro", localIngestaoOutroOld, localIngestaoOutro);
	}



	/**
	 * Return the value associated with the column: fator_causal_materia_prima_impropria
	 */
	public java.lang.Long getFatorCausalMateriaPrimaImpropria () {
		return getPropertyValue(this, fatorCausalMateriaPrimaImpropria, PROP_FATOR_CAUSAL_MATERIA_PRIMA_IMPROPRIA); 
	}

	/**
	 * Set the value related to the column: fator_causal_materia_prima_impropria
	 * @param fatorCausalMateriaPrimaImpropria the fator_causal_materia_prima_impropria value
	 */
	public void setFatorCausalMateriaPrimaImpropria (java.lang.Long fatorCausalMateriaPrimaImpropria) {
//        java.lang.Long fatorCausalMateriaPrimaImpropriaOld = this.fatorCausalMateriaPrimaImpropria;
		this.fatorCausalMateriaPrimaImpropria = fatorCausalMateriaPrimaImpropria;
//        this.getPropertyChangeSupport().firePropertyChange ("fatorCausalMateriaPrimaImpropria", fatorCausalMateriaPrimaImpropriaOld, fatorCausalMateriaPrimaImpropria);
	}



	/**
	 * Return the value associated with the column: fator_causal_manipulacao_inadequada
	 */
	public java.lang.Long getFatorCausalManipulacaoInadequada () {
		return getPropertyValue(this, fatorCausalManipulacaoInadequada, PROP_FATOR_CAUSAL_MANIPULACAO_INADEQUADA); 
	}

	/**
	 * Set the value related to the column: fator_causal_manipulacao_inadequada
	 * @param fatorCausalManipulacaoInadequada the fator_causal_manipulacao_inadequada value
	 */
	public void setFatorCausalManipulacaoInadequada (java.lang.Long fatorCausalManipulacaoInadequada) {
//        java.lang.Long fatorCausalManipulacaoInadequadaOld = this.fatorCausalManipulacaoInadequada;
		this.fatorCausalManipulacaoInadequada = fatorCausalManipulacaoInadequada;
//        this.getPropertyChangeSupport().firePropertyChange ("fatorCausalManipulacaoInadequada", fatorCausalManipulacaoInadequadaOld, fatorCausalManipulacaoInadequada);
	}



	/**
	 * Return the value associated with the column: fator_causal_conservacao_inadequada
	 */
	public java.lang.Long getFatorCausalConservacaoInadequada () {
		return getPropertyValue(this, fatorCausalConservacaoInadequada, PROP_FATOR_CAUSAL_CONSERVACAO_INADEQUADA); 
	}

	/**
	 * Set the value related to the column: fator_causal_conservacao_inadequada
	 * @param fatorCausalConservacaoInadequada the fator_causal_conservacao_inadequada value
	 */
	public void setFatorCausalConservacaoInadequada (java.lang.Long fatorCausalConservacaoInadequada) {
//        java.lang.Long fatorCausalConservacaoInadequadaOld = this.fatorCausalConservacaoInadequada;
		this.fatorCausalConservacaoInadequada = fatorCausalConservacaoInadequada;
//        this.getPropertyChangeSupport().firePropertyChange ("fatorCausalConservacaoInadequada", fatorCausalConservacaoInadequadaOld, fatorCausalConservacaoInadequada);
	}



	/**
	 * Return the value associated with the column: fator_causal_outro
	 */
	public java.lang.String getFatorCausalOutro () {
		return getPropertyValue(this, fatorCausalOutro, PROP_FATOR_CAUSAL_OUTRO); 
	}

	/**
	 * Set the value related to the column: fator_causal_outro
	 * @param fatorCausalOutro the fator_causal_outro value
	 */
	public void setFatorCausalOutro (java.lang.String fatorCausalOutro) {
//        java.lang.String fatorCausalOutroOld = this.fatorCausalOutro;
		this.fatorCausalOutro = fatorCausalOutro;
//        this.getPropertyChangeSupport().firePropertyChange ("fatorCausalOutro", fatorCausalOutroOld, fatorCausalOutro);
	}



	/**
	 * Return the value associated with the column: coletadas_amostras_clinicas
	 */
	public java.lang.Long getColetadasAmostrasClinicas () {
		return getPropertyValue(this, coletadasAmostrasClinicas, PROP_COLETADAS_AMOSTRAS_CLINICAS); 
	}

	/**
	 * Set the value related to the column: coletadas_amostras_clinicas
	 * @param coletadasAmostrasClinicas the coletadas_amostras_clinicas value
	 */
	public void setColetadasAmostrasClinicas (java.lang.Long coletadasAmostrasClinicas) {
//        java.lang.Long coletadasAmostrasClinicasOld = this.coletadasAmostrasClinicas;
		this.coletadasAmostrasClinicas = coletadasAmostrasClinicas;
//        this.getPropertyChangeSupport().firePropertyChange ("coletadasAmostrasClinicas", coletadasAmostrasClinicasOld, coletadasAmostrasClinicas);
	}



	/**
	 * Return the value associated with the column: nr_amostras_clinicas
	 */
	public java.lang.String getNumeroAmostrasClinicas () {
		return getPropertyValue(this, numeroAmostrasClinicas, PROP_NUMERO_AMOSTRAS_CLINICAS); 
	}

	/**
	 * Set the value related to the column: nr_amostras_clinicas
	 * @param numeroAmostrasClinicas the nr_amostras_clinicas value
	 */
	public void setNumeroAmostrasClinicas (java.lang.String numeroAmostrasClinicas) {
//        java.lang.String numeroAmostrasClinicasOld = this.numeroAmostrasClinicas;
		this.numeroAmostrasClinicas = numeroAmostrasClinicas;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroAmostrasClinicas", numeroAmostrasClinicasOld, numeroAmostrasClinicas);
	}



	/**
	 * Return the value associated with the column: resultado_amostra_clinica_1
	 */
	public java.lang.String getResultadoAmostraClinica1 () {
		return getPropertyValue(this, resultadoAmostraClinica1, PROP_RESULTADO_AMOSTRA_CLINICA1); 
	}

	/**
	 * Set the value related to the column: resultado_amostra_clinica_1
	 * @param resultadoAmostraClinica1 the resultado_amostra_clinica_1 value
	 */
	public void setResultadoAmostraClinica1 (java.lang.String resultadoAmostraClinica1) {
//        java.lang.String resultadoAmostraClinica1Old = this.resultadoAmostraClinica1;
		this.resultadoAmostraClinica1 = resultadoAmostraClinica1;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoAmostraClinica1", resultadoAmostraClinica1Old, resultadoAmostraClinica1);
	}



	/**
	 * Return the value associated with the column: nr_postivas_resultado_amostra_clinica_1
	 */
	public java.lang.String getNumeroPositivasResultadoAmostraClinica1 () {
		return getPropertyValue(this, numeroPositivasResultadoAmostraClinica1, PROP_NUMERO_POSITIVAS_RESULTADO_AMOSTRA_CLINICA1); 
	}

	/**
	 * Set the value related to the column: nr_postivas_resultado_amostra_clinica_1
	 * @param numeroPositivasResultadoAmostraClinica1 the nr_postivas_resultado_amostra_clinica_1 value
	 */
	public void setNumeroPositivasResultadoAmostraClinica1 (java.lang.String numeroPositivasResultadoAmostraClinica1) {
//        java.lang.String numeroPositivasResultadoAmostraClinica1Old = this.numeroPositivasResultadoAmostraClinica1;
		this.numeroPositivasResultadoAmostraClinica1 = numeroPositivasResultadoAmostraClinica1;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroPositivasResultadoAmostraClinica1", numeroPositivasResultadoAmostraClinica1Old, numeroPositivasResultadoAmostraClinica1);
	}



	/**
	 * Return the value associated with the column: resultado_amostra_clinica_2
	 */
	public java.lang.String getResultadoAmostraClinica2 () {
		return getPropertyValue(this, resultadoAmostraClinica2, PROP_RESULTADO_AMOSTRA_CLINICA2); 
	}

	/**
	 * Set the value related to the column: resultado_amostra_clinica_2
	 * @param resultadoAmostraClinica2 the resultado_amostra_clinica_2 value
	 */
	public void setResultadoAmostraClinica2 (java.lang.String resultadoAmostraClinica2) {
//        java.lang.String resultadoAmostraClinica2Old = this.resultadoAmostraClinica2;
		this.resultadoAmostraClinica2 = resultadoAmostraClinica2;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoAmostraClinica2", resultadoAmostraClinica2Old, resultadoAmostraClinica2);
	}



	/**
	 * Return the value associated with the column: nr_postivas_resultado_amostra_clinica_2
	 */
	public java.lang.String getNumeroPositivasResultadoAmostraClinica2 () {
		return getPropertyValue(this, numeroPositivasResultadoAmostraClinica2, PROP_NUMERO_POSITIVAS_RESULTADO_AMOSTRA_CLINICA2); 
	}

	/**
	 * Set the value related to the column: nr_postivas_resultado_amostra_clinica_2
	 * @param numeroPositivasResultadoAmostraClinica2 the nr_postivas_resultado_amostra_clinica_2 value
	 */
	public void setNumeroPositivasResultadoAmostraClinica2 (java.lang.String numeroPositivasResultadoAmostraClinica2) {
//        java.lang.String numeroPositivasResultadoAmostraClinica2Old = this.numeroPositivasResultadoAmostraClinica2;
		this.numeroPositivasResultadoAmostraClinica2 = numeroPositivasResultadoAmostraClinica2;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroPositivasResultadoAmostraClinica2", numeroPositivasResultadoAmostraClinica2Old, numeroPositivasResultadoAmostraClinica2);
	}



	/**
	 * Return the value associated with the column: resultado_amostra_clinica_3
	 */
	public java.lang.String getResultadoAmostraClinica3 () {
		return getPropertyValue(this, resultadoAmostraClinica3, PROP_RESULTADO_AMOSTRA_CLINICA3); 
	}

	/**
	 * Set the value related to the column: resultado_amostra_clinica_3
	 * @param resultadoAmostraClinica3 the resultado_amostra_clinica_3 value
	 */
	public void setResultadoAmostraClinica3 (java.lang.String resultadoAmostraClinica3) {
//        java.lang.String resultadoAmostraClinica3Old = this.resultadoAmostraClinica3;
		this.resultadoAmostraClinica3 = resultadoAmostraClinica3;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoAmostraClinica3", resultadoAmostraClinica3Old, resultadoAmostraClinica3);
	}



	/**
	 * Return the value associated with the column: nr_postivas_resultado_amostra_clinica_3
	 */
	public java.lang.String getNumeroPositivasResultadoAmostraClinica3 () {
		return getPropertyValue(this, numeroPositivasResultadoAmostraClinica3, PROP_NUMERO_POSITIVAS_RESULTADO_AMOSTRA_CLINICA3); 
	}

	/**
	 * Set the value related to the column: nr_postivas_resultado_amostra_clinica_3
	 * @param numeroPositivasResultadoAmostraClinica3 the nr_postivas_resultado_amostra_clinica_3 value
	 */
	public void setNumeroPositivasResultadoAmostraClinica3 (java.lang.String numeroPositivasResultadoAmostraClinica3) {
//        java.lang.String numeroPositivasResultadoAmostraClinica3Old = this.numeroPositivasResultadoAmostraClinica3;
		this.numeroPositivasResultadoAmostraClinica3 = numeroPositivasResultadoAmostraClinica3;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroPositivasResultadoAmostraClinica3", numeroPositivasResultadoAmostraClinica3Old, numeroPositivasResultadoAmostraClinica3);
	}



	/**
	 * Return the value associated with the column: coletadas_amostras_alimentos
	 */
	public java.lang.Long getColetadasAmostrasAlimentos () {
		return getPropertyValue(this, coletadasAmostrasAlimentos, PROP_COLETADAS_AMOSTRAS_ALIMENTOS); 
	}

	/**
	 * Set the value related to the column: coletadas_amostras_alimentos
	 * @param coletadasAmostrasAlimentos the coletadas_amostras_alimentos value
	 */
	public void setColetadasAmostrasAlimentos (java.lang.Long coletadasAmostrasAlimentos) {
//        java.lang.Long coletadasAmostrasAlimentosOld = this.coletadasAmostrasAlimentos;
		this.coletadasAmostrasAlimentos = coletadasAmostrasAlimentos;
//        this.getPropertyChangeSupport().firePropertyChange ("coletadasAmostrasAlimentos", coletadasAmostrasAlimentosOld, coletadasAmostrasAlimentos);
	}



	/**
	 * Return the value associated with the column: nr_amostras_alimentos
	 */
	public java.lang.String getNumeroAmostrasAlimentos () {
		return getPropertyValue(this, numeroAmostrasAlimentos, PROP_NUMERO_AMOSTRAS_ALIMENTOS); 
	}

	/**
	 * Set the value related to the column: nr_amostras_alimentos
	 * @param numeroAmostrasAlimentos the nr_amostras_alimentos value
	 */
	public void setNumeroAmostrasAlimentos (java.lang.String numeroAmostrasAlimentos) {
//        java.lang.String numeroAmostrasAlimentosOld = this.numeroAmostrasAlimentos;
		this.numeroAmostrasAlimentos = numeroAmostrasAlimentos;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroAmostrasAlimentos", numeroAmostrasAlimentosOld, numeroAmostrasAlimentos);
	}



	/**
	 * Return the value associated with the column: resultado_amostra_alimento_1
	 */
	public java.lang.String getResultadoAmostraAlimento1 () {
		return getPropertyValue(this, resultadoAmostraAlimento1, PROP_RESULTADO_AMOSTRA_ALIMENTO1); 
	}

	/**
	 * Set the value related to the column: resultado_amostra_alimento_1
	 * @param resultadoAmostraAlimento1 the resultado_amostra_alimento_1 value
	 */
	public void setResultadoAmostraAlimento1 (java.lang.String resultadoAmostraAlimento1) {
//        java.lang.String resultadoAmostraAlimento1Old = this.resultadoAmostraAlimento1;
		this.resultadoAmostraAlimento1 = resultadoAmostraAlimento1;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoAmostraAlimento1", resultadoAmostraAlimento1Old, resultadoAmostraAlimento1);
	}



	/**
	 * Return the value associated with the column: nr_postivas_resultado_amostra_alimento_1
	 */
	public java.lang.String getNumeroPositivasResultadoAmostraAlimento1 () {
		return getPropertyValue(this, numeroPositivasResultadoAmostraAlimento1, PROP_NUMERO_POSITIVAS_RESULTADO_AMOSTRA_ALIMENTO1); 
	}

	/**
	 * Set the value related to the column: nr_postivas_resultado_amostra_alimento_1
	 * @param numeroPositivasResultadoAmostraAlimento1 the nr_postivas_resultado_amostra_alimento_1 value
	 */
	public void setNumeroPositivasResultadoAmostraAlimento1 (java.lang.String numeroPositivasResultadoAmostraAlimento1) {
//        java.lang.String numeroPositivasResultadoAmostraAlimento1Old = this.numeroPositivasResultadoAmostraAlimento1;
		this.numeroPositivasResultadoAmostraAlimento1 = numeroPositivasResultadoAmostraAlimento1;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroPositivasResultadoAmostraAlimento1", numeroPositivasResultadoAmostraAlimento1Old, numeroPositivasResultadoAmostraAlimento1);
	}



	/**
	 * Return the value associated with the column: resultado_amostra_alimento_2
	 */
	public java.lang.String getResultadoAmostraAlimento2 () {
		return getPropertyValue(this, resultadoAmostraAlimento2, PROP_RESULTADO_AMOSTRA_ALIMENTO2); 
	}

	/**
	 * Set the value related to the column: resultado_amostra_alimento_2
	 * @param resultadoAmostraAlimento2 the resultado_amostra_alimento_2 value
	 */
	public void setResultadoAmostraAlimento2 (java.lang.String resultadoAmostraAlimento2) {
//        java.lang.String resultadoAmostraAlimento2Old = this.resultadoAmostraAlimento2;
		this.resultadoAmostraAlimento2 = resultadoAmostraAlimento2;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoAmostraAlimento2", resultadoAmostraAlimento2Old, resultadoAmostraAlimento2);
	}



	/**
	 * Return the value associated with the column: nr_postivas_resultado_amostra_alimento_2
	 */
	public java.lang.String getNumeroPositivasResultadoAmostraAlimento2 () {
		return getPropertyValue(this, numeroPositivasResultadoAmostraAlimento2, PROP_NUMERO_POSITIVAS_RESULTADO_AMOSTRA_ALIMENTO2); 
	}

	/**
	 * Set the value related to the column: nr_postivas_resultado_amostra_alimento_2
	 * @param numeroPositivasResultadoAmostraAlimento2 the nr_postivas_resultado_amostra_alimento_2 value
	 */
	public void setNumeroPositivasResultadoAmostraAlimento2 (java.lang.String numeroPositivasResultadoAmostraAlimento2) {
//        java.lang.String numeroPositivasResultadoAmostraAlimento2Old = this.numeroPositivasResultadoAmostraAlimento2;
		this.numeroPositivasResultadoAmostraAlimento2 = numeroPositivasResultadoAmostraAlimento2;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroPositivasResultadoAmostraAlimento2", numeroPositivasResultadoAmostraAlimento2Old, numeroPositivasResultadoAmostraAlimento2);
	}



	/**
	 * Return the value associated with the column: resultado_amostra_alimento_3
	 */
	public java.lang.String getResultadoAmostraAlimento3 () {
		return getPropertyValue(this, resultadoAmostraAlimento3, PROP_RESULTADO_AMOSTRA_ALIMENTO3); 
	}

	/**
	 * Set the value related to the column: resultado_amostra_alimento_3
	 * @param resultadoAmostraAlimento3 the resultado_amostra_alimento_3 value
	 */
	public void setResultadoAmostraAlimento3 (java.lang.String resultadoAmostraAlimento3) {
//        java.lang.String resultadoAmostraAlimento3Old = this.resultadoAmostraAlimento3;
		this.resultadoAmostraAlimento3 = resultadoAmostraAlimento3;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoAmostraAlimento3", resultadoAmostraAlimento3Old, resultadoAmostraAlimento3);
	}



	/**
	 * Return the value associated with the column: nr_postivas_resultado_amostra_alimento_3
	 */
	public java.lang.String getNumeroPositivasResultadoAmostraAlimento3 () {
		return getPropertyValue(this, numeroPositivasResultadoAmostraAlimento3, PROP_NUMERO_POSITIVAS_RESULTADO_AMOSTRA_ALIMENTO3); 
	}

	/**
	 * Set the value related to the column: nr_postivas_resultado_amostra_alimento_3
	 * @param numeroPositivasResultadoAmostraAlimento3 the nr_postivas_resultado_amostra_alimento_3 value
	 */
	public void setNumeroPositivasResultadoAmostraAlimento3 (java.lang.String numeroPositivasResultadoAmostraAlimento3) {
//        java.lang.String numeroPositivasResultadoAmostraAlimento3Old = this.numeroPositivasResultadoAmostraAlimento3;
		this.numeroPositivasResultadoAmostraAlimento3 = numeroPositivasResultadoAmostraAlimento3;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroPositivasResultadoAmostraAlimento3", numeroPositivasResultadoAmostraAlimento3Old, numeroPositivasResultadoAmostraAlimento3);
	}



	/**
	 * Return the value associated with the column: criterio_confirmacao_descarte
	 */
	public java.lang.Long getCriterioConfirmacaoDescarte () {
		return getPropertyValue(this, criterioConfirmacaoDescarte, PROP_CRITERIO_CONFIRMACAO_DESCARTE); 
	}

	/**
	 * Set the value related to the column: criterio_confirmacao_descarte
	 * @param criterioConfirmacaoDescarte the criterio_confirmacao_descarte value
	 */
	public void setCriterioConfirmacaoDescarte (java.lang.Long criterioConfirmacaoDescarte) {
//        java.lang.Long criterioConfirmacaoDescarteOld = this.criterioConfirmacaoDescarte;
		this.criterioConfirmacaoDescarte = criterioConfirmacaoDescarte;
//        this.getPropertyChangeSupport().firePropertyChange ("criterioConfirmacaoDescarte", criterioConfirmacaoDescarteOld, criterioConfirmacaoDescarte);
	}



	/**
	 * Return the value associated with the column: agente_etiologico_surto
	 */
	public java.lang.String getAgenteEtiologicoSurto () {
		return getPropertyValue(this, agenteEtiologicoSurto, PROP_AGENTE_ETIOLOGICO_SURTO); 
	}

	/**
	 * Set the value related to the column: agente_etiologico_surto
	 * @param agenteEtiologicoSurto the agente_etiologico_surto value
	 */
	public void setAgenteEtiologicoSurto (java.lang.String agenteEtiologicoSurto) {
//        java.lang.String agenteEtiologicoSurtoOld = this.agenteEtiologicoSurto;
		this.agenteEtiologicoSurto = agenteEtiologicoSurto;
//        this.getPropertyChangeSupport().firePropertyChange ("agenteEtiologicoSurto", agenteEtiologicoSurtoOld, agenteEtiologicoSurto);
	}



	/**
	 * Return the value associated with the column: alimento_causador_surto
	 */
	public java.lang.String getAlimentoCausadorSurto () {
		return getPropertyValue(this, alimentoCausadorSurto, PROP_ALIMENTO_CAUSADOR_SURTO); 
	}

	/**
	 * Set the value related to the column: alimento_causador_surto
	 * @param alimentoCausadorSurto the alimento_causador_surto value
	 */
	public void setAlimentoCausadorSurto (java.lang.String alimentoCausadorSurto) {
//        java.lang.String alimentoCausadorSurtoOld = this.alimentoCausadorSurto;
		this.alimentoCausadorSurto = alimentoCausadorSurto;
//        this.getPropertyChangeSupport().firePropertyChange ("alimentoCausadorSurto", alimentoCausadorSurtoOld, alimentoCausadorSurto);
	}



	/**
	 * Return the value associated with the column: medidas_adotadas_recomendadas
	 */
	public java.lang.String getMedidasAdotadasRecomendadas () {
		return getPropertyValue(this, medidasAdotadasRecomendadas, PROP_MEDIDAS_ADOTADAS_RECOMENDADAS); 
	}

	/**
	 * Set the value related to the column: medidas_adotadas_recomendadas
	 * @param medidasAdotadasRecomendadas the medidas_adotadas_recomendadas value
	 */
	public void setMedidasAdotadasRecomendadas (java.lang.String medidasAdotadasRecomendadas) {
//        java.lang.String medidasAdotadasRecomendadasOld = this.medidasAdotadasRecomendadas;
		this.medidasAdotadasRecomendadas = medidasAdotadasRecomendadas;
//        this.getPropertyChangeSupport().firePropertyChange ("medidasAdotadasRecomendadas", medidasAdotadasRecomendadasOld, medidasAdotadasRecomendadas);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacoes () {
		return getPropertyValue(this, observacoes, PROP_OBSERVACOES); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacoes the observacao value
	 */
	public void setObservacoes (java.lang.String observacoes) {
//        java.lang.String observacoesOld = this.observacoes;
		this.observacoes = observacoes;
//        this.getPropertyChangeSupport().firePropertyChange ("observacoes", observacoesOld, observacoes);
	}



	/**
	 * Return the value associated with the column: dt_encerramento
	 */
	public java.util.Date getDataEncerramento () {
		return getPropertyValue(this, dataEncerramento, PROP_DATA_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_encerramento
	 * @param dataEncerramento the dt_encerramento value
	 */
	public void setDataEncerramento (java.util.Date dataEncerramento) {
//        java.util.Date dataEncerramentoOld = this.dataEncerramento;
		this.dataEncerramento = dataEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEncerramento", dataEncerramentoOld, dataEncerramento);
	}



	/**
	 * Return the value associated with the column: cd_registro_agravo
	 */
	public br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo getRegistroAgravo () {
		return getPropertyValue(this, registroAgravo, PROP_REGISTRO_AGRAVO); 
	}

	/**
	 * Set the value related to the column: cd_registro_agravo
	 * @param registroAgravo the cd_registro_agravo value
	 */
	public void setRegistroAgravo (br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo) {
//        br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravoOld = this.registroAgravo;
		this.registroAgravo = registroAgravo;
//        this.getPropertyChangeSupport().firePropertyChange ("registroAgravo", registroAgravoOld, registroAgravo);
	}



	/**
	 * Return the value associated with the column: cd_usuario_encerramento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioEncerramento () {
		return getPropertyValue(this, usuarioEncerramento, PROP_USUARIO_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_encerramento
	 * @param usuarioEncerramento the cd_usuario_encerramento value
	 */
	public void setUsuarioEncerramento (br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramentoOld = this.usuarioEncerramento;
		this.usuarioEncerramento = usuarioEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioEncerramento", usuarioEncerramentoOld, usuarioEncerramento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoSurto)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoSurto investigacaoAgravoSurto = (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoSurto) obj;
			if (null == this.getCodigo() || null == investigacaoAgravoSurto.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravoSurto.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}