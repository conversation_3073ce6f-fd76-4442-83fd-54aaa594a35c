<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
	
<hibernate-mapping package="br.com.ksisolucoes.vo.entradas.estoque"  >
    <class 
        name="ProdutoImportacaoNfe"
        table="produto_importacao_nfe"
    >

        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_prod_imp"
        >
            <generator class="assigned"/>
        </id> 
        <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.entradas.estoque.Produto"
            name="produto"
        >
            <column name="cod_pro" />
        </many-to-one>
        
        <property
            name="codigoProdutoExterno"
            column="cd_pro_externo"
            type="java.lang.String"
            not-null="true"
        />
        
        <property
            name="descricaoProdutoExterno"
            column="ds_pro_externo"
            type="java.lang.String"
        />
        
        <property
            name="codigoCnpjFornecedorExterno"
            column="cd_cnpj_fornec_ext"
            type="java.lang.String"
            not-null="true"
        />
        
        <property
            name="descricaoFornecedorExterno"
            column="ds_fornec_ext"
            type="java.lang.String"
        />
        
        <property
            name="fatorConversao"
            column="fator_conversao"
            type="java.lang.Double"
        />
        
        <property
            name="tipoConversao"
            column="tipo_conversao"
            type="java.lang.Long"
        />
        
    </class>
</hibernate-mapping>