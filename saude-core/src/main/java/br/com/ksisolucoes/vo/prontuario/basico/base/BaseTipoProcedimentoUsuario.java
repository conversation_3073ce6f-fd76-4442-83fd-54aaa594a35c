package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the tipo_procedimento_usuario table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="tipo_procedimento_usuario"
 */

public abstract class BaseTipoProcedimentoUsuario extends BaseRootVO implements Serializable {

	public static String REF = "TipoProcedimentoUsuario";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TIPO_PROCEDIMENTO = "tipoProcedimento";


	// constructors
	public BaseTipoProcedimentoUsuario () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseTipoProcedimentoUsuario (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseTipoProcedimentoUsuario (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimento,
		br.com.ksisolucoes.vo.controle.Usuario usuario) {

		this.setCodigo(codigo);
		this.setTipoProcedimento(tipoProcedimento);
		this.setUsuario(usuario);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimento;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  column="cd_tp_proc_empresa"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_tp_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento getTipoProcedimento () {
		return getPropertyValue(this, tipoProcedimento, PROP_TIPO_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_tp_procedimento
	 * @param tipoProcedimento the cd_tp_procedimento value
	 */
	public void setTipoProcedimento (br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimentoOld = this.tipoProcedimento;
		this.tipoProcedimento = tipoProcedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoProcedimento", tipoProcedimentoOld, tipoProcedimento);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimentoUsuario)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimentoUsuario tipoProcedimentoUsuario = (br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimentoUsuario) obj;
			if (null == this.getCodigo() || null == tipoProcedimentoUsuario.getCodigo()) return false;
			else return (this.getCodigo().equals(tipoProcedimentoUsuario.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}