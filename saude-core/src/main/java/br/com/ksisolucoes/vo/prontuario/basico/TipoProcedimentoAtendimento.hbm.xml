<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="TipoProcedimentoAtendimento" table="tipo_procedimento_atendimento" >
         
        <id
            name="codigo"
            column="cd_procedimento_atendimento"
            type="java.lang.Long"
        /> 
        <version column="version" name="version" type="long" />
         
        <property 
            name="descricao"
            column="ds_procedimento_atendimento"
            type="java.lang.String"
            length="50"
            not-null="true"
        />

        <property
            name="flagReserva"
            column="flag_reserva"
            type="java.lang.Long"
            not-null="true"
        />

        <property
            name="flagExibirPagina"
            column="flag_exibir_pagina"
            type="java.lang.Long"
            not-null="true"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento"
            name="naturezaProcuraTipoAtendimentoAgenda"
            column="cd_nat_proc_tp_atendimento_ag"
            not-null="false"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento"
            name="naturezaProcuraTipoAtendimentoAtendimento"
            column="cd_nat_proc_tp_atendimento_at"
            not-null="false"
        />

        <property
                name="flagFilaProcedimentos"
                column="flag_fila_procedimentos"
                type="java.lang.Long"
        />

        <property
                name="flagDisponivelConfirmacaoPresenca"
                column="flag_dis_confirmacao_presenca"
                type="java.lang.Long"
                not-null="false"
        />
        
    </class>
</hibernate-mapping>
