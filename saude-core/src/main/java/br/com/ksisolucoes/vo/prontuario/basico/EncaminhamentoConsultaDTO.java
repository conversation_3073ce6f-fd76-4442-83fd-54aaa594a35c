package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;
import java.util.List;

public class EncaminhamentoConsultaDTO  implements Serializable {


    private boolean origemPainelSoap;
    private EncaminhamentoConsulta encaminhamentoConsulta;
    private String tipoControleRegulacao;
    private String obrigatorioCidEncaminhamentoEspecialista;
    private String descricaoMotivo;
    private Long flagPreencheuNotificacao;
    private Encaminhamento encaminhamento;
    private Atendimento atendimentoContraReferencia;
    private ClassificacaoRisco classificacaoRisco;
    private Cid cid;
    private String motivoConsulta;
    private String exameResultado;

    private List<EncaminhamentoConsultaCriterio> criterios;

    public boolean isOrigemPainelSoap() {
        return origemPainelSoap;
    }

    public void setOrigemPainelSoap(boolean origemPainelSoap) {
        this.origemPainelSoap = origemPainelSoap;
    }

    public EncaminhamentoConsulta getEncaminhamentoConsulta() {
        return encaminhamentoConsulta;
    }

    public void setEncaminhamentoConsulta(EncaminhamentoConsulta encaminhamentoConsulta) {
        this.encaminhamentoConsulta = encaminhamentoConsulta;
    }

    public String getTipoControleRegulacao() {
        return tipoControleRegulacao;
    }

    public void setTipoControleRegulacao(String tipoControleRegulacao) {
        this.tipoControleRegulacao = tipoControleRegulacao;
    }

    public String getObrigatorioCidEncaminhamentoEspecialista() {
        return obrigatorioCidEncaminhamentoEspecialista;
    }

    public void setObrigatorioCidEncaminhamentoEspecialista(String obrigatorioCidEncaminhamentoEspecialista) {
        this.obrigatorioCidEncaminhamentoEspecialista = obrigatorioCidEncaminhamentoEspecialista;
    }

    public String getDescricaoMotivo() {
        return descricaoMotivo;
    }

    public void setDescricaoMotivo(String descricaoMotivo) {
        this.descricaoMotivo = descricaoMotivo;
    }

    public Long getFlagPreencheuNotificacao() {
        return flagPreencheuNotificacao;
    }

    public void setFlagPreencheuNotificacao(Long flagPreencheuNotificacao) {
        this.flagPreencheuNotificacao = flagPreencheuNotificacao;
    }

    public Encaminhamento getEncaminhamento() {
        return encaminhamento;
    }

    public void setEncaminhamento(Encaminhamento encaminhamento) {
        this.encaminhamento = encaminhamento;
    }

    public Atendimento getAtendimentoContraReferencia() {
        return atendimentoContraReferencia;
    }

    public void setAtendimentoContraReferencia(Atendimento atendimentoContraReferencia) {
        this.atendimentoContraReferencia = atendimentoContraReferencia;
    }

    public Cid getCid() {
        return cid;
    }

    public void setCid(Cid cid) {
        this.cid = cid;
    }

    public String getMotivoConsulta() {
        return motivoConsulta;
    }

    public void setMotivoConsulta(String motivoConsulta) {
        this.motivoConsulta = motivoConsulta;
    }

    public String getExameResultado() {
        return exameResultado;
    }

    public void setExameResultado(String exameResultado) {
        this.exameResultado = exameResultado;
    }

    public List<EncaminhamentoConsultaCriterio> getCriterios() {
        return criterios;
    }

    public void setCriterios(List<EncaminhamentoConsultaCriterio> criterios) {
        this.criterios = criterios;
    }

    public ClassificacaoRisco getClassificacaoRisco() {
        return classificacaoRisco;
    }

    public void setClassificacaoRisco(ClassificacaoRisco classificacaoRisco) {
        this.classificacaoRisco = classificacaoRisco;
    }
}
