package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the formulario_populacao_exposta_agrotoxico_rastreio table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="formulario_populacao_exposta_agrotoxico_rastreio"
 */

public abstract class BaseFormularioPopulacaoExpostaAgrotoxicoRastreio extends BaseRootVO implements Serializable {

	public static String REF = "FormularioPopulacaoExpostaAgrotoxicoRastreio";
	public static final String PROP_OUTRAS_ATIVIDADES_ATUAL = "outrasAtividadesAtual";
	public static final String PROP_ATIVIDADE_ATUAL_AGRICULTURA = "atividadeAtualAgricultura";
	public static final String PROP_FORMA_CONTATO_ATUAL_APLICACAO = "formaContatoAtualAplicacao";
	public static final String PROP_NRO_INTOXICACOES = "nroIntoxicacoes";
	public static final String PROP_OUTRA_ATIVIDADE = "outraAtividade";
	public static final String PROP_ATIVIDADE_AVICULTURA = "atividadeAvicultura";
	public static final String PROP_FORMA_CONTATO_ATUAL_PRODUCAO = "formaContatoAtualProducao";
	public static final String PROP_FORMA_CONTATO_ATUAL_OUTRAS = "formaContatoAtualOutras";
	public static final String PROP_ATIVIDADE_PECUARIA = "atividadePecuaria";
	public static final String PROP_UNIDADE_TEMPO_EXPOSICAO = "unidadeTempoExposicao";
	public static final String PROP_NOME_AGROTOXICO = "nomeAgrotoxico";
	public static final String PROP_ATIVIDADE_ATUAL_AGENTE_ENDEMIAS = "atividadeAtualAgenteEndemias";
	public static final String PROP_FORMA_CONTATO_ATUAL_TRATAMENTO_SEMENTES = "formaContatoAtualTratamentoSementes";
	public static final String PROP_FORMA_CONTATO_ATUAL_LAVAGEM_ROUPA = "formaContatoAtualLavagemRoupa";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_FORMA_CONTATO_ATUAL_CARGA = "formaContatoAtualCarga";
	public static final String PROP_GESTANTE = "gestante";
	public static final String PROP_TEM_CONTATO_AGROTOXICO = "temContatoAgrotoxico";
	public static final String PROP_CONTATO_AGROTOXICO = "contatoAgrotoxico";
	public static final String PROP_SINTOMAS_RESPIRATORIA = "sintomasRespiratoria";
	public static final String PROP_FORMA_CONTATO_ATUAL_ARMAZENAMENTO = "formaContatoAtualArmazenamento";
	public static final String PROP_OUTRAS_FORMAS_CONTATO_ATUAL = "outrasFormasContatoAtual";
	public static final String PROP_SINTOMAS_PELE = "sintomasPele";
	public static final String PROP_FORMA_CONTATO_ATUAL_CONTAMINACAO_AMBIENTAL = "formaContatoAtualContaminacaoAmbiental";
	public static final String PROP_ATIVIDADE_ATUAL_DESINSETIZACAO = "atividadeAtualDesinsetizacao";
	public static final String PROP_ATIVIDADE_ATUAL_OUTROS = "atividadeAtualOutros";
	public static final String PROP_FORMA_CONTATO_ATUAL_SUPERVISAO = "formaContatoAtualSupervisao";
	public static final String PROP_TEM_AGROTOXICO_UNIDADE = "temAgrotoxicoUnidade";
	public static final String PROP_ZONA_RURAL = "zonaRural";
	public static final String PROP_SINTOMAS_SENSORIO = "sintomasSensorio";
	public static final String PROP_OUTROS_SINTOMAS = "outrosSintomas";
	public static final String PROP_ATIVIDADE_PISCICULTURA = "atividadePiscicultura";
	public static final String PROP_FORMA_CONTATO_ATUAL_DILUICAO = "formaContatoAtualDiluicao";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_ATIVIDADE_AGRICULTURA = "atividadeAgricultura";
	public static final String PROP_TIPO_TEMPO_EXPOSICAO_ATUAL = "tipoTempoExposicaoAtual";
	public static final String PROP_FORMA_CONTATO_ATUAL_PREPARO = "formaContatoAtualPreparo";
	public static final String PROP_OCUPACAO = "ocupacao";
	public static final String PROP_ATIVIDADE_OUTRAS = "atividadeOutras";
	public static final String PROP_FORMA_CONTATO_ATUAL_LIMPEZA_EQUIPAMENTO = "formaContatoAtualLimpezaEquipamento";
	public static final String PROP_ATIVIDADE_ATUAL_PECUARIA = "atividadeAtualPecuaria";
	public static final String PROP_FORMA_CONTATO_ATUAL_TRANSPORTE = "formaContatoAtualTransporte";
	public static final String PROP_CONTATO_POR_ATUAL = "contatoPorAtual";
	public static final String PROP_TEMPO_EXPOSICAO = "tempoExposicao";
	public static final String PROP_FORMA_CONTATO_ATUAL_DESCARTE_EMBALAGEM = "formaContatoAtualDescarteEmbalagem";
	public static final String PROP_SINTOMAS_GASTROINTESTINAIS = "sintomasGastrointestinais";
	public static final String PROP_DATA_ULTIMO_CONTATO = "dataUltimoContato";
	public static final String PROP_ATIVIDADE_ATUAL_USO_DOMESTICO = "atividadeAtualUsoDomestico";
	public static final String PROP_AGROTOXICOS_TEM_CONTATO = "agrotoxicosTemContato";
	public static final String PROP_SINTOMAS_NAO_LEMBRA = "sintomasNaoLembra";
	public static final String PROP_FORMA_CONTATO_ATUAL_COLHEITA = "formaContatoAtualColheita";
	public static final String PROP_SINTOMAS_CARDIOVASCULAR = "sintomasCardiovascular";
	public static final String PROP_SINTOMAS_OUTROS = "sintomasOutros";
	public static final String PROP_TEMPO_EXPOSICAO_ATUAL = "tempoExposicaoAtual";
	public static final String PROP_ATIVIDADE_ATUAL_INDUSTRIA = "atividadeAtualIndustria";
	public static final String PROP_FORMA_CONTATO_ATUAL_CONTROLE_EXPEDICAO = "formaContatoAtualControleExpedicao";
	public static final String PROP_CONTATO_POR = "contatoPor";
	public static final String PROP_ESTRATIFICACAO_RISCO = "estratificacaoRisco";


	// constructors
	public BaseFormularioPopulacaoExpostaAgrotoxicoRastreio () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseFormularioPopulacaoExpostaAgrotoxicoRastreio (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseFormularioPopulacaoExpostaAgrotoxicoRastreio (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.EstratificacaoRisco estratificacaoRisco) {

		this.setCodigo(codigo);
		this.setEstratificacaoRisco(estratificacaoRisco);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long zonaRural;
	private java.lang.Long gestante;
	private java.lang.String ocupacao;
	private java.lang.Long contatoAgrotoxico;
	private java.lang.Long contatoPor;
	private java.lang.Long atividadeAgricultura;
	private java.lang.Long atividadePecuaria;
	private java.lang.Long atividadeAvicultura;
	private java.lang.Long atividadePiscicultura;
	private java.lang.Long atividadeOutras;
	private java.lang.String outraAtividade;
	private java.lang.String nomeAgrotoxico;
	private java.lang.Long tempoExposicao;
	private java.lang.Long unidadeTempoExposicao;
	private java.lang.Long temContatoAgrotoxico;
	private java.lang.String contatoPorAtual;
	private java.lang.Long tempoExposicaoAtual;
	private java.lang.Long tipoTempoExposicaoAtual;
	private java.util.Date dataUltimoContato;
	private java.lang.String agrotoxicosTemContato;
	private java.lang.Long atividadeAtualAgricultura;
	private java.lang.Long atividadeAtualPecuaria;
	private java.lang.Long atividadeAtualIndustria;
	private java.lang.Long atividadeAtualDesinsetizacao;
	private java.lang.Long atividadeAtualAgenteEndemias;
	private java.lang.Long atividadeAtualUsoDomestico;
	private java.lang.Long atividadeAtualOutros;
	private java.lang.String outrasAtividadesAtual;
	private java.lang.Long formaContatoAtualPreparo;
	private java.lang.Long formaContatoAtualDiluicao;
	private java.lang.Long formaContatoAtualTratamentoSementes;
	private java.lang.Long formaContatoAtualAplicacao;
	private java.lang.Long formaContatoAtualColheita;
	private java.lang.Long formaContatoAtualSupervisao;
	private java.lang.Long formaContatoAtualArmazenamento;
	private java.lang.Long formaContatoAtualDescarteEmbalagem;
	private java.lang.Long formaContatoAtualLimpezaEquipamento;
	private java.lang.Long formaContatoAtualLavagemRoupa;
	private java.lang.Long formaContatoAtualCarga;
	private java.lang.Long formaContatoAtualTransporte;
	private java.lang.Long formaContatoAtualControleExpedicao;
	private java.lang.Long formaContatoAtualProducao;
	private java.lang.Long formaContatoAtualContaminacaoAmbiental;
	private java.lang.Long formaContatoAtualOutras;
	private java.lang.String outrasFormasContatoAtual;
	private java.lang.Long nroIntoxicacoes;
	private java.lang.Long sintomasGastrointestinais;
	private java.lang.Long sintomasSensorio;
	private java.lang.Long sintomasPele;
	private java.lang.Long sintomasCardiovascular;
	private java.lang.Long sintomasRespiratoria;
	private java.lang.Long sintomasNaoLembra;
	private java.lang.Long sintomasOutros;
	private java.lang.String outrosSintomas;
	private java.lang.Long temAgrotoxicoUnidade;
	private java.lang.String observacao;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.EstratificacaoRisco estratificacaoRisco;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_formulario_populacao_exposta_agrotoxico_rastreio"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: zona_rural
	 */
	public java.lang.Long getZonaRural () {
		return getPropertyValue(this, zonaRural, PROP_ZONA_RURAL); 
	}

	/**
	 * Set the value related to the column: zona_rural
	 * @param zonaRural the zona_rural value
	 */
	public void setZonaRural (java.lang.Long zonaRural) {
//        java.lang.Long zonaRuralOld = this.zonaRural;
		this.zonaRural = zonaRural;
//        this.getPropertyChangeSupport().firePropertyChange ("zonaRural", zonaRuralOld, zonaRural);
	}



	/**
	 * Return the value associated with the column: gestante
	 */
	public java.lang.Long getGestante () {
		return getPropertyValue(this, gestante, PROP_GESTANTE); 
	}

	/**
	 * Set the value related to the column: gestante
	 * @param gestante the gestante value
	 */
	public void setGestante (java.lang.Long gestante) {
//        java.lang.Long gestanteOld = this.gestante;
		this.gestante = gestante;
//        this.getPropertyChangeSupport().firePropertyChange ("gestante", gestanteOld, gestante);
	}



	/**
	 * Return the value associated with the column: ocupacao
	 */
	public java.lang.String getOcupacao () {
		return getPropertyValue(this, ocupacao, PROP_OCUPACAO); 
	}

	/**
	 * Set the value related to the column: ocupacao
	 * @param ocupacao the ocupacao value
	 */
	public void setOcupacao (java.lang.String ocupacao) {
//        java.lang.String ocupacaoOld = this.ocupacao;
		this.ocupacao = ocupacao;
//        this.getPropertyChangeSupport().firePropertyChange ("ocupacao", ocupacaoOld, ocupacao);
	}



	/**
	 * Return the value associated with the column: contato_agrotoxico
	 */
	public java.lang.Long getContatoAgrotoxico () {
		return getPropertyValue(this, contatoAgrotoxico, PROP_CONTATO_AGROTOXICO); 
	}

	/**
	 * Set the value related to the column: contato_agrotoxico
	 * @param contatoAgrotoxico the contato_agrotoxico value
	 */
	public void setContatoAgrotoxico (java.lang.Long contatoAgrotoxico) {
//        java.lang.Long contatoAgrotoxicoOld = this.contatoAgrotoxico;
		this.contatoAgrotoxico = contatoAgrotoxico;
//        this.getPropertyChangeSupport().firePropertyChange ("contatoAgrotoxico", contatoAgrotoxicoOld, contatoAgrotoxico);
	}



	/**
	 * Return the value associated with the column: contato_por
	 */
	public java.lang.Long getContatoPor () {
		return getPropertyValue(this, contatoPor, PROP_CONTATO_POR); 
	}

	/**
	 * Set the value related to the column: contato_por
	 * @param contatoPor the contato_por value
	 */
	public void setContatoPor (java.lang.Long contatoPor) {
//        java.lang.Long contatoPorOld = this.contatoPor;
		this.contatoPor = contatoPor;
//        this.getPropertyChangeSupport().firePropertyChange ("contatoPor", contatoPorOld, contatoPor);
	}



	/**
	 * Return the value associated with the column: atividade_agricultura
	 */
	public java.lang.Long getAtividadeAgricultura () {
		return getPropertyValue(this, atividadeAgricultura, PROP_ATIVIDADE_AGRICULTURA); 
	}

	/**
	 * Set the value related to the column: atividade_agricultura
	 * @param atividadeAgricultura the atividade_agricultura value
	 */
	public void setAtividadeAgricultura (java.lang.Long atividadeAgricultura) {
//        java.lang.Long atividadeAgriculturaOld = this.atividadeAgricultura;
		this.atividadeAgricultura = atividadeAgricultura;
//        this.getPropertyChangeSupport().firePropertyChange ("atividadeAgricultura", atividadeAgriculturaOld, atividadeAgricultura);
	}



	/**
	 * Return the value associated with the column: atividade_pecuaria
	 */
	public java.lang.Long getAtividadePecuaria () {
		return getPropertyValue(this, atividadePecuaria, PROP_ATIVIDADE_PECUARIA); 
	}

	/**
	 * Set the value related to the column: atividade_pecuaria
	 * @param atividadePecuaria the atividade_pecuaria value
	 */
	public void setAtividadePecuaria (java.lang.Long atividadePecuaria) {
//        java.lang.Long atividadePecuariaOld = this.atividadePecuaria;
		this.atividadePecuaria = atividadePecuaria;
//        this.getPropertyChangeSupport().firePropertyChange ("atividadePecuaria", atividadePecuariaOld, atividadePecuaria);
	}



	/**
	 * Return the value associated with the column: atividade_avicultura
	 */
	public java.lang.Long getAtividadeAvicultura () {
		return getPropertyValue(this, atividadeAvicultura, PROP_ATIVIDADE_AVICULTURA); 
	}

	/**
	 * Set the value related to the column: atividade_avicultura
	 * @param atividadeAvicultura the atividade_avicultura value
	 */
	public void setAtividadeAvicultura (java.lang.Long atividadeAvicultura) {
//        java.lang.Long atividadeAviculturaOld = this.atividadeAvicultura;
		this.atividadeAvicultura = atividadeAvicultura;
//        this.getPropertyChangeSupport().firePropertyChange ("atividadeAvicultura", atividadeAviculturaOld, atividadeAvicultura);
	}



	/**
	 * Return the value associated with the column: atividade_piscicultura
	 */
	public java.lang.Long getAtividadePiscicultura () {
		return getPropertyValue(this, atividadePiscicultura, PROP_ATIVIDADE_PISCICULTURA); 
	}

	/**
	 * Set the value related to the column: atividade_piscicultura
	 * @param atividadePiscicultura the atividade_piscicultura value
	 */
	public void setAtividadePiscicultura (java.lang.Long atividadePiscicultura) {
//        java.lang.Long atividadePisciculturaOld = this.atividadePiscicultura;
		this.atividadePiscicultura = atividadePiscicultura;
//        this.getPropertyChangeSupport().firePropertyChange ("atividadePiscicultura", atividadePisciculturaOld, atividadePiscicultura);
	}



	/**
	 * Return the value associated with the column: atividade_outras
	 */
	public java.lang.Long getAtividadeOutras () {
		return getPropertyValue(this, atividadeOutras, PROP_ATIVIDADE_OUTRAS); 
	}

	/**
	 * Set the value related to the column: atividade_outras
	 * @param atividadeOutras the atividade_outras value
	 */
	public void setAtividadeOutras (java.lang.Long atividadeOutras) {
//        java.lang.Long atividadeOutrasOld = this.atividadeOutras;
		this.atividadeOutras = atividadeOutras;
//        this.getPropertyChangeSupport().firePropertyChange ("atividadeOutras", atividadeOutrasOld, atividadeOutras);
	}



	/**
	 * Return the value associated with the column: outra_atividade
	 */
	public java.lang.String getOutraAtividade () {
		return getPropertyValue(this, outraAtividade, PROP_OUTRA_ATIVIDADE); 
	}

	/**
	 * Set the value related to the column: outra_atividade
	 * @param outraAtividade the outra_atividade value
	 */
	public void setOutraAtividade (java.lang.String outraAtividade) {
//        java.lang.String outraAtividadeOld = this.outraAtividade;
		this.outraAtividade = outraAtividade;
//        this.getPropertyChangeSupport().firePropertyChange ("outraAtividade", outraAtividadeOld, outraAtividade);
	}



	/**
	 * Return the value associated with the column: nome_agrotoxico
	 */
	public java.lang.String getNomeAgrotoxico () {
		return getPropertyValue(this, nomeAgrotoxico, PROP_NOME_AGROTOXICO); 
	}

	/**
	 * Set the value related to the column: nome_agrotoxico
	 * @param nomeAgrotoxico the nome_agrotoxico value
	 */
	public void setNomeAgrotoxico (java.lang.String nomeAgrotoxico) {
//        java.lang.String nomeAgrotoxicoOld = this.nomeAgrotoxico;
		this.nomeAgrotoxico = nomeAgrotoxico;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeAgrotoxico", nomeAgrotoxicoOld, nomeAgrotoxico);
	}



	/**
	 * Return the value associated with the column: tempo_exposicao
	 */
	public java.lang.Long getTempoExposicao () {
		return getPropertyValue(this, tempoExposicao, PROP_TEMPO_EXPOSICAO); 
	}

	/**
	 * Set the value related to the column: tempo_exposicao
	 * @param tempoExposicao the tempo_exposicao value
	 */
	public void setTempoExposicao (java.lang.Long tempoExposicao) {
//        java.lang.Long tempoExposicaoOld = this.tempoExposicao;
		this.tempoExposicao = tempoExposicao;
//        this.getPropertyChangeSupport().firePropertyChange ("tempoExposicao", tempoExposicaoOld, tempoExposicao);
	}



	/**
	 * Return the value associated with the column: unidade_tempo_exposicao
	 */
	public java.lang.Long getUnidadeTempoExposicao () {
		return getPropertyValue(this, unidadeTempoExposicao, PROP_UNIDADE_TEMPO_EXPOSICAO); 
	}

	/**
	 * Set the value related to the column: unidade_tempo_exposicao
	 * @param unidadeTempoExposicao the unidade_tempo_exposicao value
	 */
	public void setUnidadeTempoExposicao (java.lang.Long unidadeTempoExposicao) {
//        java.lang.Long unidadeTempoExposicaoOld = this.unidadeTempoExposicao;
		this.unidadeTempoExposicao = unidadeTempoExposicao;
//        this.getPropertyChangeSupport().firePropertyChange ("unidadeTempoExposicao", unidadeTempoExposicaoOld, unidadeTempoExposicao);
	}



	/**
	 * Return the value associated with the column: tem_contato_agrotoxico
	 */
	public java.lang.Long getTemContatoAgrotoxico () {
		return getPropertyValue(this, temContatoAgrotoxico, PROP_TEM_CONTATO_AGROTOXICO); 
	}

	/**
	 * Set the value related to the column: tem_contato_agrotoxico
	 * @param temContatoAgrotoxico the tem_contato_agrotoxico value
	 */
	public void setTemContatoAgrotoxico (java.lang.Long temContatoAgrotoxico) {
//        java.lang.Long temContatoAgrotoxicoOld = this.temContatoAgrotoxico;
		this.temContatoAgrotoxico = temContatoAgrotoxico;
//        this.getPropertyChangeSupport().firePropertyChange ("temContatoAgrotoxico", temContatoAgrotoxicoOld, temContatoAgrotoxico);
	}



	/**
	 * Return the value associated with the column: contato_por_atual
	 */
	public java.lang.String getContatoPorAtual () {
		return getPropertyValue(this, contatoPorAtual, PROP_CONTATO_POR_ATUAL); 
	}

	/**
	 * Set the value related to the column: contato_por_atual
	 * @param contatoPorAtual the contato_por_atual value
	 */
	public void setContatoPorAtual (java.lang.String contatoPorAtual) {
//        java.lang.String contatoPorAtualOld = this.contatoPorAtual;
		this.contatoPorAtual = contatoPorAtual;
//        this.getPropertyChangeSupport().firePropertyChange ("contatoPorAtual", contatoPorAtualOld, contatoPorAtual);
	}



	/**
	 * Return the value associated with the column: tempo_exposicao_atual
	 */
	public java.lang.Long getTempoExposicaoAtual () {
		return getPropertyValue(this, tempoExposicaoAtual, PROP_TEMPO_EXPOSICAO_ATUAL); 
	}

	/**
	 * Set the value related to the column: tempo_exposicao_atual
	 * @param tempoExposicaoAtual the tempo_exposicao_atual value
	 */
	public void setTempoExposicaoAtual (java.lang.Long tempoExposicaoAtual) {
//        java.lang.Long tempoExposicaoAtualOld = this.tempoExposicaoAtual;
		this.tempoExposicaoAtual = tempoExposicaoAtual;
//        this.getPropertyChangeSupport().firePropertyChange ("tempoExposicaoAtual", tempoExposicaoAtualOld, tempoExposicaoAtual);
	}



	/**
	 * Return the value associated with the column: tipo_tempo_exposicao_atual
	 */
	public java.lang.Long getTipoTempoExposicaoAtual () {
		return getPropertyValue(this, tipoTempoExposicaoAtual, PROP_TIPO_TEMPO_EXPOSICAO_ATUAL); 
	}

	/**
	 * Set the value related to the column: tipo_tempo_exposicao_atual
	 * @param tipoTempoExposicaoAtual the tipo_tempo_exposicao_atual value
	 */
	public void setTipoTempoExposicaoAtual (java.lang.Long tipoTempoExposicaoAtual) {
//        java.lang.Long tipoTempoExposicaoAtualOld = this.tipoTempoExposicaoAtual;
		this.tipoTempoExposicaoAtual = tipoTempoExposicaoAtual;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoTempoExposicaoAtual", tipoTempoExposicaoAtualOld, tipoTempoExposicaoAtual);
	}



	/**
	 * Return the value associated with the column: data_ultimo_contato
	 */
	public java.util.Date getDataUltimoContato () {
		return getPropertyValue(this, dataUltimoContato, PROP_DATA_ULTIMO_CONTATO); 
	}

	/**
	 * Set the value related to the column: data_ultimo_contato
	 * @param dataUltimoContato the data_ultimo_contato value
	 */
	public void setDataUltimoContato (java.util.Date dataUltimoContato) {
//        java.util.Date dataUltimoContatoOld = this.dataUltimoContato;
		this.dataUltimoContato = dataUltimoContato;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUltimoContato", dataUltimoContatoOld, dataUltimoContato);
	}



	/**
	 * Return the value associated with the column: agrotoxicos_tem_contato
	 */
	public java.lang.String getAgrotoxicosTemContato () {
		return getPropertyValue(this, agrotoxicosTemContato, PROP_AGROTOXICOS_TEM_CONTATO); 
	}

	/**
	 * Set the value related to the column: agrotoxicos_tem_contato
	 * @param agrotoxicosTemContato the agrotoxicos_tem_contato value
	 */
	public void setAgrotoxicosTemContato (java.lang.String agrotoxicosTemContato) {
//        java.lang.String agrotoxicosTemContatoOld = this.agrotoxicosTemContato;
		this.agrotoxicosTemContato = agrotoxicosTemContato;
//        this.getPropertyChangeSupport().firePropertyChange ("agrotoxicosTemContato", agrotoxicosTemContatoOld, agrotoxicosTemContato);
	}



	/**
	 * Return the value associated with the column: atividade_atual_agricultura
	 */
	public java.lang.Long getAtividadeAtualAgricultura () {
		return getPropertyValue(this, atividadeAtualAgricultura, PROP_ATIVIDADE_ATUAL_AGRICULTURA); 
	}

	/**
	 * Set the value related to the column: atividade_atual_agricultura
	 * @param atividadeAtualAgricultura the atividade_atual_agricultura value
	 */
	public void setAtividadeAtualAgricultura (java.lang.Long atividadeAtualAgricultura) {
//        java.lang.Long atividadeAtualAgriculturaOld = this.atividadeAtualAgricultura;
		this.atividadeAtualAgricultura = atividadeAtualAgricultura;
//        this.getPropertyChangeSupport().firePropertyChange ("atividadeAtualAgricultura", atividadeAtualAgriculturaOld, atividadeAtualAgricultura);
	}



	/**
	 * Return the value associated with the column: atividade_atual_pecuaria
	 */
	public java.lang.Long getAtividadeAtualPecuaria () {
		return getPropertyValue(this, atividadeAtualPecuaria, PROP_ATIVIDADE_ATUAL_PECUARIA); 
	}

	/**
	 * Set the value related to the column: atividade_atual_pecuaria
	 * @param atividadeAtualPecuaria the atividade_atual_pecuaria value
	 */
	public void setAtividadeAtualPecuaria (java.lang.Long atividadeAtualPecuaria) {
//        java.lang.Long atividadeAtualPecuariaOld = this.atividadeAtualPecuaria;
		this.atividadeAtualPecuaria = atividadeAtualPecuaria;
//        this.getPropertyChangeSupport().firePropertyChange ("atividadeAtualPecuaria", atividadeAtualPecuariaOld, atividadeAtualPecuaria);
	}



	/**
	 * Return the value associated with the column: atividade_atual_industria
	 */
	public java.lang.Long getAtividadeAtualIndustria () {
		return getPropertyValue(this, atividadeAtualIndustria, PROP_ATIVIDADE_ATUAL_INDUSTRIA); 
	}

	/**
	 * Set the value related to the column: atividade_atual_industria
	 * @param atividadeAtualIndustria the atividade_atual_industria value
	 */
	public void setAtividadeAtualIndustria (java.lang.Long atividadeAtualIndustria) {
//        java.lang.Long atividadeAtualIndustriaOld = this.atividadeAtualIndustria;
		this.atividadeAtualIndustria = atividadeAtualIndustria;
//        this.getPropertyChangeSupport().firePropertyChange ("atividadeAtualIndustria", atividadeAtualIndustriaOld, atividadeAtualIndustria);
	}



	/**
	 * Return the value associated with the column: atividade_atual_desinsetizacao
	 */
	public java.lang.Long getAtividadeAtualDesinsetizacao () {
		return getPropertyValue(this, atividadeAtualDesinsetizacao, PROP_ATIVIDADE_ATUAL_DESINSETIZACAO); 
	}

	/**
	 * Set the value related to the column: atividade_atual_desinsetizacao
	 * @param atividadeAtualDesinsetizacao the atividade_atual_desinsetizacao value
	 */
	public void setAtividadeAtualDesinsetizacao (java.lang.Long atividadeAtualDesinsetizacao) {
//        java.lang.Long atividadeAtualDesinsetizacaoOld = this.atividadeAtualDesinsetizacao;
		this.atividadeAtualDesinsetizacao = atividadeAtualDesinsetizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("atividadeAtualDesinsetizacao", atividadeAtualDesinsetizacaoOld, atividadeAtualDesinsetizacao);
	}



	/**
	 * Return the value associated with the column: atividade_atual_agente_endemias
	 */
	public java.lang.Long getAtividadeAtualAgenteEndemias () {
		return getPropertyValue(this, atividadeAtualAgenteEndemias, PROP_ATIVIDADE_ATUAL_AGENTE_ENDEMIAS); 
	}

	/**
	 * Set the value related to the column: atividade_atual_agente_endemias
	 * @param atividadeAtualAgenteEndemias the atividade_atual_agente_endemias value
	 */
	public void setAtividadeAtualAgenteEndemias (java.lang.Long atividadeAtualAgenteEndemias) {
//        java.lang.Long atividadeAtualAgenteEndemiasOld = this.atividadeAtualAgenteEndemias;
		this.atividadeAtualAgenteEndemias = atividadeAtualAgenteEndemias;
//        this.getPropertyChangeSupport().firePropertyChange ("atividadeAtualAgenteEndemias", atividadeAtualAgenteEndemiasOld, atividadeAtualAgenteEndemias);
	}



	/**
	 * Return the value associated with the column: atividade_atual_domestico
	 */
	public java.lang.Long getAtividadeAtualUsoDomestico () {
		return getPropertyValue(this, atividadeAtualUsoDomestico, PROP_ATIVIDADE_ATUAL_USO_DOMESTICO); 
	}

	/**
	 * Set the value related to the column: atividade_atual_domestico
	 * @param atividadeAtualUsoDomestico the atividade_atual_domestico value
	 */
	public void setAtividadeAtualUsoDomestico (java.lang.Long atividadeAtualUsoDomestico) {
//        java.lang.Long atividadeAtualUsoDomesticoOld = this.atividadeAtualUsoDomestico;
		this.atividadeAtualUsoDomestico = atividadeAtualUsoDomestico;
//        this.getPropertyChangeSupport().firePropertyChange ("atividadeAtualUsoDomestico", atividadeAtualUsoDomesticoOld, atividadeAtualUsoDomestico);
	}



	/**
	 * Return the value associated with the column: atividade_atual_outros
	 */
	public java.lang.Long getAtividadeAtualOutros () {
		return getPropertyValue(this, atividadeAtualOutros, PROP_ATIVIDADE_ATUAL_OUTROS); 
	}

	/**
	 * Set the value related to the column: atividade_atual_outros
	 * @param atividadeAtualOutros the atividade_atual_outros value
	 */
	public void setAtividadeAtualOutros (java.lang.Long atividadeAtualOutros) {
//        java.lang.Long atividadeAtualOutrosOld = this.atividadeAtualOutros;
		this.atividadeAtualOutros = atividadeAtualOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("atividadeAtualOutros", atividadeAtualOutrosOld, atividadeAtualOutros);
	}



	/**
	 * Return the value associated with the column: outras_atividades_atual
	 */
	public java.lang.String getOutrasAtividadesAtual () {
		return getPropertyValue(this, outrasAtividadesAtual, PROP_OUTRAS_ATIVIDADES_ATUAL); 
	}

	/**
	 * Set the value related to the column: outras_atividades_atual
	 * @param outrasAtividadesAtual the outras_atividades_atual value
	 */
	public void setOutrasAtividadesAtual (java.lang.String outrasAtividadesAtual) {
//        java.lang.String outrasAtividadesAtualOld = this.outrasAtividadesAtual;
		this.outrasAtividadesAtual = outrasAtividadesAtual;
//        this.getPropertyChangeSupport().firePropertyChange ("outrasAtividadesAtual", outrasAtividadesAtualOld, outrasAtividadesAtual);
	}



	/**
	 * Return the value associated with the column: forma_contato_atual_preparo
	 */
	public java.lang.Long getFormaContatoAtualPreparo () {
		return getPropertyValue(this, formaContatoAtualPreparo, PROP_FORMA_CONTATO_ATUAL_PREPARO); 
	}

	/**
	 * Set the value related to the column: forma_contato_atual_preparo
	 * @param formaContatoAtualPreparo the forma_contato_atual_preparo value
	 */
	public void setFormaContatoAtualPreparo (java.lang.Long formaContatoAtualPreparo) {
//        java.lang.Long formaContatoAtualPreparoOld = this.formaContatoAtualPreparo;
		this.formaContatoAtualPreparo = formaContatoAtualPreparo;
//        this.getPropertyChangeSupport().firePropertyChange ("formaContatoAtualPreparo", formaContatoAtualPreparoOld, formaContatoAtualPreparo);
	}



	/**
	 * Return the value associated with the column: forma_contato_atual_diluicao
	 */
	public java.lang.Long getFormaContatoAtualDiluicao () {
		return getPropertyValue(this, formaContatoAtualDiluicao, PROP_FORMA_CONTATO_ATUAL_DILUICAO); 
	}

	/**
	 * Set the value related to the column: forma_contato_atual_diluicao
	 * @param formaContatoAtualDiluicao the forma_contato_atual_diluicao value
	 */
	public void setFormaContatoAtualDiluicao (java.lang.Long formaContatoAtualDiluicao) {
//        java.lang.Long formaContatoAtualDiluicaoOld = this.formaContatoAtualDiluicao;
		this.formaContatoAtualDiluicao = formaContatoAtualDiluicao;
//        this.getPropertyChangeSupport().firePropertyChange ("formaContatoAtualDiluicao", formaContatoAtualDiluicaoOld, formaContatoAtualDiluicao);
	}



	/**
	 * Return the value associated with the column: forma_contato_atual_tratamento_sementes
	 */
	public java.lang.Long getFormaContatoAtualTratamentoSementes () {
		return getPropertyValue(this, formaContatoAtualTratamentoSementes, PROP_FORMA_CONTATO_ATUAL_TRATAMENTO_SEMENTES); 
	}

	/**
	 * Set the value related to the column: forma_contato_atual_tratamento_sementes
	 * @param formaContatoAtualTratamentoSementes the forma_contato_atual_tratamento_sementes value
	 */
	public void setFormaContatoAtualTratamentoSementes (java.lang.Long formaContatoAtualTratamentoSementes) {
//        java.lang.Long formaContatoAtualTratamentoSementesOld = this.formaContatoAtualTratamentoSementes;
		this.formaContatoAtualTratamentoSementes = formaContatoAtualTratamentoSementes;
//        this.getPropertyChangeSupport().firePropertyChange ("formaContatoAtualTratamentoSementes", formaContatoAtualTratamentoSementesOld, formaContatoAtualTratamentoSementes);
	}



	/**
	 * Return the value associated with the column: forma_contato_atual_aplicacao
	 */
	public java.lang.Long getFormaContatoAtualAplicacao () {
		return getPropertyValue(this, formaContatoAtualAplicacao, PROP_FORMA_CONTATO_ATUAL_APLICACAO); 
	}

	/**
	 * Set the value related to the column: forma_contato_atual_aplicacao
	 * @param formaContatoAtualAplicacao the forma_contato_atual_aplicacao value
	 */
	public void setFormaContatoAtualAplicacao (java.lang.Long formaContatoAtualAplicacao) {
//        java.lang.Long formaContatoAtualAplicacaoOld = this.formaContatoAtualAplicacao;
		this.formaContatoAtualAplicacao = formaContatoAtualAplicacao;
//        this.getPropertyChangeSupport().firePropertyChange ("formaContatoAtualAplicacao", formaContatoAtualAplicacaoOld, formaContatoAtualAplicacao);
	}



	/**
	 * Return the value associated with the column: forma_contato_atual_colheita
	 */
	public java.lang.Long getFormaContatoAtualColheita () {
		return getPropertyValue(this, formaContatoAtualColheita, PROP_FORMA_CONTATO_ATUAL_COLHEITA); 
	}

	/**
	 * Set the value related to the column: forma_contato_atual_colheita
	 * @param formaContatoAtualColheita the forma_contato_atual_colheita value
	 */
	public void setFormaContatoAtualColheita (java.lang.Long formaContatoAtualColheita) {
//        java.lang.Long formaContatoAtualColheitaOld = this.formaContatoAtualColheita;
		this.formaContatoAtualColheita = formaContatoAtualColheita;
//        this.getPropertyChangeSupport().firePropertyChange ("formaContatoAtualColheita", formaContatoAtualColheitaOld, formaContatoAtualColheita);
	}



	/**
	 * Return the value associated with the column: forma_contato_atual_supervisao
	 */
	public java.lang.Long getFormaContatoAtualSupervisao () {
		return getPropertyValue(this, formaContatoAtualSupervisao, PROP_FORMA_CONTATO_ATUAL_SUPERVISAO); 
	}

	/**
	 * Set the value related to the column: forma_contato_atual_supervisao
	 * @param formaContatoAtualSupervisao the forma_contato_atual_supervisao value
	 */
	public void setFormaContatoAtualSupervisao (java.lang.Long formaContatoAtualSupervisao) {
//        java.lang.Long formaContatoAtualSupervisaoOld = this.formaContatoAtualSupervisao;
		this.formaContatoAtualSupervisao = formaContatoAtualSupervisao;
//        this.getPropertyChangeSupport().firePropertyChange ("formaContatoAtualSupervisao", formaContatoAtualSupervisaoOld, formaContatoAtualSupervisao);
	}



	/**
	 * Return the value associated with the column: forma_contato_atual_armazenamento
	 */
	public java.lang.Long getFormaContatoAtualArmazenamento () {
		return getPropertyValue(this, formaContatoAtualArmazenamento, PROP_FORMA_CONTATO_ATUAL_ARMAZENAMENTO); 
	}

	/**
	 * Set the value related to the column: forma_contato_atual_armazenamento
	 * @param formaContatoAtualArmazenamento the forma_contato_atual_armazenamento value
	 */
	public void setFormaContatoAtualArmazenamento (java.lang.Long formaContatoAtualArmazenamento) {
//        java.lang.Long formaContatoAtualArmazenamentoOld = this.formaContatoAtualArmazenamento;
		this.formaContatoAtualArmazenamento = formaContatoAtualArmazenamento;
//        this.getPropertyChangeSupport().firePropertyChange ("formaContatoAtualArmazenamento", formaContatoAtualArmazenamentoOld, formaContatoAtualArmazenamento);
	}



	/**
	 * Return the value associated with the column: forma_contato_atual_descarte_embalagem
	 */
	public java.lang.Long getFormaContatoAtualDescarteEmbalagem () {
		return getPropertyValue(this, formaContatoAtualDescarteEmbalagem, PROP_FORMA_CONTATO_ATUAL_DESCARTE_EMBALAGEM); 
	}

	/**
	 * Set the value related to the column: forma_contato_atual_descarte_embalagem
	 * @param formaContatoAtualDescarteEmbalagem the forma_contato_atual_descarte_embalagem value
	 */
	public void setFormaContatoAtualDescarteEmbalagem (java.lang.Long formaContatoAtualDescarteEmbalagem) {
//        java.lang.Long formaContatoAtualDescarteEmbalagemOld = this.formaContatoAtualDescarteEmbalagem;
		this.formaContatoAtualDescarteEmbalagem = formaContatoAtualDescarteEmbalagem;
//        this.getPropertyChangeSupport().firePropertyChange ("formaContatoAtualDescarteEmbalagem", formaContatoAtualDescarteEmbalagemOld, formaContatoAtualDescarteEmbalagem);
	}



	/**
	 * Return the value associated with the column: forma_contato_atual_limpeza_equipamento
	 */
	public java.lang.Long getFormaContatoAtualLimpezaEquipamento () {
		return getPropertyValue(this, formaContatoAtualLimpezaEquipamento, PROP_FORMA_CONTATO_ATUAL_LIMPEZA_EQUIPAMENTO); 
	}

	/**
	 * Set the value related to the column: forma_contato_atual_limpeza_equipamento
	 * @param formaContatoAtualLimpezaEquipamento the forma_contato_atual_limpeza_equipamento value
	 */
	public void setFormaContatoAtualLimpezaEquipamento (java.lang.Long formaContatoAtualLimpezaEquipamento) {
//        java.lang.Long formaContatoAtualLimpezaEquipamentoOld = this.formaContatoAtualLimpezaEquipamento;
		this.formaContatoAtualLimpezaEquipamento = formaContatoAtualLimpezaEquipamento;
//        this.getPropertyChangeSupport().firePropertyChange ("formaContatoAtualLimpezaEquipamento", formaContatoAtualLimpezaEquipamentoOld, formaContatoAtualLimpezaEquipamento);
	}



	/**
	 * Return the value associated with the column: forma_contato_atual_lavagem_roupa
	 */
	public java.lang.Long getFormaContatoAtualLavagemRoupa () {
		return getPropertyValue(this, formaContatoAtualLavagemRoupa, PROP_FORMA_CONTATO_ATUAL_LAVAGEM_ROUPA); 
	}

	/**
	 * Set the value related to the column: forma_contato_atual_lavagem_roupa
	 * @param formaContatoAtualLavagemRoupa the forma_contato_atual_lavagem_roupa value
	 */
	public void setFormaContatoAtualLavagemRoupa (java.lang.Long formaContatoAtualLavagemRoupa) {
//        java.lang.Long formaContatoAtualLavagemRoupaOld = this.formaContatoAtualLavagemRoupa;
		this.formaContatoAtualLavagemRoupa = formaContatoAtualLavagemRoupa;
//        this.getPropertyChangeSupport().firePropertyChange ("formaContatoAtualLavagemRoupa", formaContatoAtualLavagemRoupaOld, formaContatoAtualLavagemRoupa);
	}



	/**
	 * Return the value associated with the column: forma_contato_atual_carga
	 */
	public java.lang.Long getFormaContatoAtualCarga () {
		return getPropertyValue(this, formaContatoAtualCarga, PROP_FORMA_CONTATO_ATUAL_CARGA); 
	}

	/**
	 * Set the value related to the column: forma_contato_atual_carga
	 * @param formaContatoAtualCarga the forma_contato_atual_carga value
	 */
	public void setFormaContatoAtualCarga (java.lang.Long formaContatoAtualCarga) {
//        java.lang.Long formaContatoAtualCargaOld = this.formaContatoAtualCarga;
		this.formaContatoAtualCarga = formaContatoAtualCarga;
//        this.getPropertyChangeSupport().firePropertyChange ("formaContatoAtualCarga", formaContatoAtualCargaOld, formaContatoAtualCarga);
	}



	/**
	 * Return the value associated with the column: forma_contato_atual_transporte
	 */
	public java.lang.Long getFormaContatoAtualTransporte () {
		return getPropertyValue(this, formaContatoAtualTransporte, PROP_FORMA_CONTATO_ATUAL_TRANSPORTE); 
	}

	/**
	 * Set the value related to the column: forma_contato_atual_transporte
	 * @param formaContatoAtualTransporte the forma_contato_atual_transporte value
	 */
	public void setFormaContatoAtualTransporte (java.lang.Long formaContatoAtualTransporte) {
//        java.lang.Long formaContatoAtualTransporteOld = this.formaContatoAtualTransporte;
		this.formaContatoAtualTransporte = formaContatoAtualTransporte;
//        this.getPropertyChangeSupport().firePropertyChange ("formaContatoAtualTransporte", formaContatoAtualTransporteOld, formaContatoAtualTransporte);
	}



	/**
	 * Return the value associated with the column: forma_contato_atual_controle_expedicao
	 */
	public java.lang.Long getFormaContatoAtualControleExpedicao () {
		return getPropertyValue(this, formaContatoAtualControleExpedicao, PROP_FORMA_CONTATO_ATUAL_CONTROLE_EXPEDICAO); 
	}

	/**
	 * Set the value related to the column: forma_contato_atual_controle_expedicao
	 * @param formaContatoAtualControleExpedicao the forma_contato_atual_controle_expedicao value
	 */
	public void setFormaContatoAtualControleExpedicao (java.lang.Long formaContatoAtualControleExpedicao) {
//        java.lang.Long formaContatoAtualControleExpedicaoOld = this.formaContatoAtualControleExpedicao;
		this.formaContatoAtualControleExpedicao = formaContatoAtualControleExpedicao;
//        this.getPropertyChangeSupport().firePropertyChange ("formaContatoAtualControleExpedicao", formaContatoAtualControleExpedicaoOld, formaContatoAtualControleExpedicao);
	}



	/**
	 * Return the value associated with the column: forma_contato_atual_producao
	 */
	public java.lang.Long getFormaContatoAtualProducao () {
		return getPropertyValue(this, formaContatoAtualProducao, PROP_FORMA_CONTATO_ATUAL_PRODUCAO); 
	}

	/**
	 * Set the value related to the column: forma_contato_atual_producao
	 * @param formaContatoAtualProducao the forma_contato_atual_producao value
	 */
	public void setFormaContatoAtualProducao (java.lang.Long formaContatoAtualProducao) {
//        java.lang.Long formaContatoAtualProducaoOld = this.formaContatoAtualProducao;
		this.formaContatoAtualProducao = formaContatoAtualProducao;
//        this.getPropertyChangeSupport().firePropertyChange ("formaContatoAtualProducao", formaContatoAtualProducaoOld, formaContatoAtualProducao);
	}



	/**
	 * Return the value associated with the column: forma_contato_atual_contaminacao_ambiental
	 */
	public java.lang.Long getFormaContatoAtualContaminacaoAmbiental () {
		return getPropertyValue(this, formaContatoAtualContaminacaoAmbiental, PROP_FORMA_CONTATO_ATUAL_CONTAMINACAO_AMBIENTAL); 
	}

	/**
	 * Set the value related to the column: forma_contato_atual_contaminacao_ambiental
	 * @param formaContatoAtualContaminacaoAmbiental the forma_contato_atual_contaminacao_ambiental value
	 */
	public void setFormaContatoAtualContaminacaoAmbiental (java.lang.Long formaContatoAtualContaminacaoAmbiental) {
//        java.lang.Long formaContatoAtualContaminacaoAmbientalOld = this.formaContatoAtualContaminacaoAmbiental;
		this.formaContatoAtualContaminacaoAmbiental = formaContatoAtualContaminacaoAmbiental;
//        this.getPropertyChangeSupport().firePropertyChange ("formaContatoAtualContaminacaoAmbiental", formaContatoAtualContaminacaoAmbientalOld, formaContatoAtualContaminacaoAmbiental);
	}



	/**
	 * Return the value associated with the column: forma_contato_atual_outras
	 */
	public java.lang.Long getFormaContatoAtualOutras () {
		return getPropertyValue(this, formaContatoAtualOutras, PROP_FORMA_CONTATO_ATUAL_OUTRAS); 
	}

	/**
	 * Set the value related to the column: forma_contato_atual_outras
	 * @param formaContatoAtualOutras the forma_contato_atual_outras value
	 */
	public void setFormaContatoAtualOutras (java.lang.Long formaContatoAtualOutras) {
//        java.lang.Long formaContatoAtualOutrasOld = this.formaContatoAtualOutras;
		this.formaContatoAtualOutras = formaContatoAtualOutras;
//        this.getPropertyChangeSupport().firePropertyChange ("formaContatoAtualOutras", formaContatoAtualOutrasOld, formaContatoAtualOutras);
	}



	/**
	 * Return the value associated with the column: outras_formas_contato_atual
	 */
	public java.lang.String getOutrasFormasContatoAtual () {
		return getPropertyValue(this, outrasFormasContatoAtual, PROP_OUTRAS_FORMAS_CONTATO_ATUAL); 
	}

	/**
	 * Set the value related to the column: outras_formas_contato_atual
	 * @param outrasFormasContatoAtual the outras_formas_contato_atual value
	 */
	public void setOutrasFormasContatoAtual (java.lang.String outrasFormasContatoAtual) {
//        java.lang.String outrasFormasContatoAtualOld = this.outrasFormasContatoAtual;
		this.outrasFormasContatoAtual = outrasFormasContatoAtual;
//        this.getPropertyChangeSupport().firePropertyChange ("outrasFormasContatoAtual", outrasFormasContatoAtualOld, outrasFormasContatoAtual);
	}



	/**
	 * Return the value associated with the column: nro_intoxicacoes
	 */
	public java.lang.Long getNroIntoxicacoes () {
		return getPropertyValue(this, nroIntoxicacoes, PROP_NRO_INTOXICACOES); 
	}

	/**
	 * Set the value related to the column: nro_intoxicacoes
	 * @param nroIntoxicacoes the nro_intoxicacoes value
	 */
	public void setNroIntoxicacoes (java.lang.Long nroIntoxicacoes) {
//        java.lang.Long nroIntoxicacoesOld = this.nroIntoxicacoes;
		this.nroIntoxicacoes = nroIntoxicacoes;
//        this.getPropertyChangeSupport().firePropertyChange ("nroIntoxicacoes", nroIntoxicacoesOld, nroIntoxicacoes);
	}



	/**
	 * Return the value associated with the column: sintomas_gastrointestinais
	 */
	public java.lang.Long getSintomasGastrointestinais () {
		return getPropertyValue(this, sintomasGastrointestinais, PROP_SINTOMAS_GASTROINTESTINAIS); 
	}

	/**
	 * Set the value related to the column: sintomas_gastrointestinais
	 * @param sintomasGastrointestinais the sintomas_gastrointestinais value
	 */
	public void setSintomasGastrointestinais (java.lang.Long sintomasGastrointestinais) {
//        java.lang.Long sintomasGastrointestinaisOld = this.sintomasGastrointestinais;
		this.sintomasGastrointestinais = sintomasGastrointestinais;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomasGastrointestinais", sintomasGastrointestinaisOld, sintomasGastrointestinais);
	}



	/**
	 * Return the value associated with the column: sintomas_sensorio
	 */
	public java.lang.Long getSintomasSensorio () {
		return getPropertyValue(this, sintomasSensorio, PROP_SINTOMAS_SENSORIO); 
	}

	/**
	 * Set the value related to the column: sintomas_sensorio
	 * @param sintomasSensorio the sintomas_sensorio value
	 */
	public void setSintomasSensorio (java.lang.Long sintomasSensorio) {
//        java.lang.Long sintomasSensorioOld = this.sintomasSensorio;
		this.sintomasSensorio = sintomasSensorio;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomasSensorio", sintomasSensorioOld, sintomasSensorio);
	}



	/**
	 * Return the value associated with the column: sintomas_pele
	 */
	public java.lang.Long getSintomasPele () {
		return getPropertyValue(this, sintomasPele, PROP_SINTOMAS_PELE); 
	}

	/**
	 * Set the value related to the column: sintomas_pele
	 * @param sintomasPele the sintomas_pele value
	 */
	public void setSintomasPele (java.lang.Long sintomasPele) {
//        java.lang.Long sintomasPeleOld = this.sintomasPele;
		this.sintomasPele = sintomasPele;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomasPele", sintomasPeleOld, sintomasPele);
	}



	/**
	 * Return the value associated with the column: sintomas_cardiovascular
	 */
	public java.lang.Long getSintomasCardiovascular () {
		return getPropertyValue(this, sintomasCardiovascular, PROP_SINTOMAS_CARDIOVASCULAR); 
	}

	/**
	 * Set the value related to the column: sintomas_cardiovascular
	 * @param sintomasCardiovascular the sintomas_cardiovascular value
	 */
	public void setSintomasCardiovascular (java.lang.Long sintomasCardiovascular) {
//        java.lang.Long sintomasCardiovascularOld = this.sintomasCardiovascular;
		this.sintomasCardiovascular = sintomasCardiovascular;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomasCardiovascular", sintomasCardiovascularOld, sintomasCardiovascular);
	}



	/**
	 * Return the value associated with the column: sintomas_resíratoria
	 */
	public java.lang.Long getSintomasRespiratoria () {
		return getPropertyValue(this, sintomasRespiratoria, PROP_SINTOMAS_RESPIRATORIA); 
	}

	/**
	 * Set the value related to the column: sintomas_resíratoria
	 * @param sintomasRespiratoria the sintomas_resíratoria value
	 */
	public void setSintomasRespiratoria (java.lang.Long sintomasRespiratoria) {
//        java.lang.Long sintomasRespiratoriaOld = this.sintomasRespiratoria;
		this.sintomasRespiratoria = sintomasRespiratoria;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomasRespiratoria", sintomasRespiratoriaOld, sintomasRespiratoria);
	}



	/**
	 * Return the value associated with the column: sintomas_nao_lembra
	 */
	public java.lang.Long getSintomasNaoLembra () {
		return getPropertyValue(this, sintomasNaoLembra, PROP_SINTOMAS_NAO_LEMBRA); 
	}

	/**
	 * Set the value related to the column: sintomas_nao_lembra
	 * @param sintomasNaoLembra the sintomas_nao_lembra value
	 */
	public void setSintomasNaoLembra (java.lang.Long sintomasNaoLembra) {
//        java.lang.Long sintomasNaoLembraOld = this.sintomasNaoLembra;
		this.sintomasNaoLembra = sintomasNaoLembra;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomasNaoLembra", sintomasNaoLembraOld, sintomasNaoLembra);
	}



	/**
	 * Return the value associated with the column: sintomas_outros
	 */
	public java.lang.Long getSintomasOutros () {
		return getPropertyValue(this, sintomasOutros, PROP_SINTOMAS_OUTROS); 
	}

	/**
	 * Set the value related to the column: sintomas_outros
	 * @param sintomasOutros the sintomas_outros value
	 */
	public void setSintomasOutros (java.lang.Long sintomasOutros) {
//        java.lang.Long sintomasOutrosOld = this.sintomasOutros;
		this.sintomasOutros = sintomasOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomasOutros", sintomasOutrosOld, sintomasOutros);
	}



	/**
	 * Return the value associated with the column: outros_sintomas
	 */
	public java.lang.String getOutrosSintomas () {
		return getPropertyValue(this, outrosSintomas, PROP_OUTROS_SINTOMAS); 
	}

	/**
	 * Set the value related to the column: outros_sintomas
	 * @param outrosSintomas the outros_sintomas value
	 */
	public void setOutrosSintomas (java.lang.String outrosSintomas) {
//        java.lang.String outrosSintomasOld = this.outrosSintomas;
		this.outrosSintomas = outrosSintomas;
//        this.getPropertyChangeSupport().firePropertyChange ("outrosSintomas", outrosSintomasOld, outrosSintomas);
	}



	/**
	 * Return the value associated with the column: tem_agrotoxico_unidade
	 */
	public java.lang.Long getTemAgrotoxicoUnidade () {
		return getPropertyValue(this, temAgrotoxicoUnidade, PROP_TEM_AGROTOXICO_UNIDADE); 
	}

	/**
	 * Set the value related to the column: tem_agrotoxico_unidade
	 * @param temAgrotoxicoUnidade the tem_agrotoxico_unidade value
	 */
	public void setTemAgrotoxicoUnidade (java.lang.Long temAgrotoxicoUnidade) {
//        java.lang.Long temAgrotoxicoUnidadeOld = this.temAgrotoxicoUnidade;
		this.temAgrotoxicoUnidade = temAgrotoxicoUnidade;
//        this.getPropertyChangeSupport().firePropertyChange ("temAgrotoxicoUnidade", temAgrotoxicoUnidadeOld, temAgrotoxicoUnidade);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: cd_estratificacao_risco
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.EstratificacaoRisco getEstratificacaoRisco () {
		return getPropertyValue(this, estratificacaoRisco, PROP_ESTRATIFICACAO_RISCO); 
	}

	/**
	 * Set the value related to the column: cd_estratificacao_risco
	 * @param estratificacaoRisco the cd_estratificacao_risco value
	 */
	public void setEstratificacaoRisco (br.com.ksisolucoes.vo.prontuario.basico.EstratificacaoRisco estratificacaoRisco) {
//        br.com.ksisolucoes.vo.prontuario.basico.EstratificacaoRisco estratificacaoRiscoOld = this.estratificacaoRisco;
		this.estratificacaoRisco = estratificacaoRisco;
//        this.getPropertyChangeSupport().firePropertyChange ("estratificacaoRisco", estratificacaoRiscoOld, estratificacaoRisco);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.FormularioPopulacaoExpostaAgrotoxicoRastreio)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.FormularioPopulacaoExpostaAgrotoxicoRastreio formularioPopulacaoExpostaAgrotoxicoRastreio = (br.com.ksisolucoes.vo.prontuario.basico.FormularioPopulacaoExpostaAgrotoxicoRastreio) obj;
			if (null == this.getCodigo() || null == formularioPopulacaoExpostaAgrotoxicoRastreio.getCodigo()) return false;
			else return (this.getCodigo().equals(formularioPopulacaoExpostaAgrotoxicoRastreio.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}