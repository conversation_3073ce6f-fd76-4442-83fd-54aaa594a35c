package br.com.ksisolucoes.vo.entradas.estoque;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.entradas.estoque.base.BasePedidoTransferencia;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;

import java.io.Serializable;
import java.util.List;

/**
 * Esta classe est relacionado com a tabela pedido_transferencia.
 * Classe dimensionada para customizaes.
 */
public class PedidoTransferencia extends BasePedidoTransferencia implements PesquisaObjectInterface, CodigoManager {

    public static final Long STATUS_SEPARACAO_NAO = new Long(0);
    public static final Long STATUS_SEPARACAO_SIM = new Long(1);
    public static final Long STATUS_SEPARACAO_AGUARDANDO_ENVIO = new Long(2);

    private List<PedidoTransferenciaItem> pedidoTransferenciaItemList;
    private List<PedidoTransferenciaItemLote> pedidoTransferenciaItemLoteList;

    public static final String PROP_DESCRICAO_STATUS = "descricaoStatus";


    public static final String PROP_FLAG_VACINA_FORMATADA = "flagVacinaFormatada";

    public static final String PROP_DESCRICAO_STATUS_SEPARACAO = "descricaoStatusSeparacao";

    public static final String PROP_DESCRICAO_TIPO = "descricaoTipo";

    /**
     * Constante de status aberto.<br>
     * Valor: 0
     */
    public static final Long STATUS_ABERTO = new Long(0);

    public static final Long STATUS_SEPARANDO = new Long(1);

    /**
     * Constante de status processado.<br>
     * Valor: 2
     */
    public static final Long STATUS_PROCESSADO = new Long(2);
    /**
     * Constante de status recebido.<br>
     * Valor: 3
     */
    public static final Long STATUS_RECEBIDO = new Long(3);
    /**
     * Constante de status cancelado.<br>
     * Valor: 4
     */
    public static final Long STATUS_CANCELADO = new Long(4);

    public static final Long STATUS_NAO_APROVADO = new Long(6);
    
    public static final Long STATUS_PENDENTE_APROVACAO = new Long(7);
    /**
     * Constante de status para pedido no impresso.<br>
     * Valor: 0
     */
    public static final Long STATUS_IMPRESSAO_NAO_IMPRESSO = new Long(0);
    /**
     * Constante de status para pedido j impresso.<br>
     * Valor: 2
     */
    public static final Long STATUS_IMPRESSAO_IMPRESSO = new Long(2);

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public PedidoTransferencia() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public PedidoTransferencia(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public PedidoTransferencia(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.basico.Empresa empresa,
            br.com.ksisolucoes.vo.basico.Empresa empresaOrigem,
            br.com.ksisolucoes.vo.basico.Empresa empresaDestino,
            br.com.ksisolucoes.vo.controle.Usuario usuario,
            java.util.Date dataPedido,
            java.lang.Long flagVacina,
            java.util.Date dataCadastro,
            java.lang.Long statusSeparacao,
            java.lang.Long tipo) {

        super(
                codigo,
                empresa,
                empresaOrigem,
                empresaDestino,
                usuario,
                dataPedido,
                flagVacina,
                dataCadastro,
                statusSeparacao,
                tipo);
    }

    /*[CONSTRUCTOR MARKER END]*/
    public List<PedidoTransferenciaItemLote> getPedidoTransferenciaItemLoteList() {
        return pedidoTransferenciaItemLoteList;
    }

    public void setPedidoTransferenciaItemLoteList(List<PedidoTransferenciaItemLote> pedidoTransferenciaItemLoteList) {
        this.pedidoTransferenciaItemLoteList = pedidoTransferenciaItemLoteList;
    }

    public String getFlagVacinaFormatada() {
        if (RepositoryComponentDefault.SIM_LONG.equals(getFlagVacina())) {
            return Bundle.getStringApplication("rotulo_sim");
        }
        return Bundle.getStringApplication("rotulo_nao");
    }

    public String getNomeUsuarioIntegracao() {
        return getUsuarioIntegracao() != null ? getUsuarioIntegracao() : getUsuario().getNome();
    }


    public enum Tipo implements IEnum<Tipo> {

        MENSAL(0L, Bundle.getStringApplication("rotulo_tipo_mensal")),
        COMPLEMENTAR(1L, Bundle.getStringApplication("rotulo_tipo_complementar"));

        private Long value;
        private String descricao;

        private Tipo(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Tipo valueOf(Long value) {
            for (Tipo tipo : Tipo.values()) {
                if (tipo.value().equals(value)) {
                    return tipo;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    public enum StatusAprovacao implements IEnum<StatusAprovacao> {

        APROVADO(0L, Bundle.getStringApplication("rotulo_aprovado")),
        AGUARDANDO(1L, Bundle.getStringApplication("rotulo_aguardando")),
        REPROVADO(2L, Bundle.getStringApplication("rotulo_reprovado"));

        private Long value;
        private String descricao;

        private StatusAprovacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static StatusAprovacao valueOf(Long value) {
            for (StatusAprovacao statusAprovacao : StatusAprovacao.values()) {
                if (statusAprovacao.value().equals(value)) {
                    return statusAprovacao;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public void setCodigoManager(Serializable key) {
        this.setCodigo((Long) key);
    }

    public String getDescricaoVO() {
        return this.getEmpresaDestino().getDescricao() + " - " + Data.formatar(this.getDataPedido());
    }

    public String getIdentificador() {
        return this.getCodigo().toString();
    }

    public String getDescricaoStatus() {
        if (getStatus().equals(STATUS_ABERTO)) {
            return Bundle.getStringApplication("rotulo_aberto");
        } else if (getStatus().equals(STATUS_SEPARANDO)) {
            return Bundle.getStringApplication("rotulo_separando");
        } else if (getStatus().equals(STATUS_CANCELADO)) {
            return Bundle.getStringApplication("rotulo_cancelado");
        } else if (getStatus().equals(STATUS_PROCESSADO)) {
            return Bundle.getStringApplication("rotulo_processado");
        } else if (getStatus().equals(STATUS_RECEBIDO)) {
            return Bundle.getStringApplication("rotulo_recebido");
        } else if (getStatus().equals(STATUS_NAO_APROVADO)) {
            return Bundle.getStringApplication("rotulo_nao_aprovado");
        } else if (getStatus().equals(STATUS_PENDENTE_APROVACAO)) {
            return Bundle.getStringApplication("rotulo_pendente_aprovacao");
        }
        return Bundle.getStringApplication("rotulo_desconhecido");
    }

    public String getDescricaoStatusSeparacao() {
        if (getStatusSeparacao().equals(STATUS_SEPARACAO_NAO)) {
            return Bundle.getStringApplication("rotulo_nao_separado");
        } else if (getStatusSeparacao().equals(STATUS_SEPARACAO_SIM)) {
            return Bundle.getStringApplication("rotulo_separando");
        } else if (getStatusSeparacao().equals(STATUS_SEPARACAO_AGUARDANDO_ENVIO)) {
            return Bundle.getStringApplication("rotulo_pendente_envio");
        }
        return Bundle.getStringApplication("rotulo_desconhecido");
    }

    public String getDescricaoStatus(Long value) {
        if (value.equals(STATUS_ABERTO)) {
            return Bundle.getStringApplication("rotulo_aberto");
        } else if (value.equals(STATUS_SEPARANDO)) {
            return Bundle.getStringApplication("rotulo_separando");
        } else if (value.equals(STATUS_CANCELADO)) {
            return Bundle.getStringApplication("rotulo_cancelado");
        } else if (value.equals(STATUS_PROCESSADO)) {
            return Bundle.getStringApplication("rotulo_processado");
        } else if (value.equals(STATUS_RECEBIDO)) {
            return Bundle.getStringApplication("rotulo_recebido");
        } else if (value.equals(STATUS_NAO_APROVADO)) {
            return Bundle.getStringApplication("rotulo_nao_aprovado");
        } else if (value.equals(STATUS_PENDENTE_APROVACAO)) {
            return Bundle.getStringApplication("rotulo_pendente_aprovacao");
        }
        return Bundle.getStringApplication("rotulo_desconhecido");
    }

    public String getDataCadastroFormatado() {
        return Data.formatarDataHora(getDataCadastro());
    }

    public String getDescricaoTipo() {
        Tipo tipo = Tipo.valueOf(getTipo());
        if (tipo != null) {
            return tipo.descricao();
        }
        return null;
    }

    public String getStatusAprovacaoFormatado() {
        if (StatusAprovacao.APROVADO.value().equals(getStatusAprovacao())) {
            return StatusAprovacao.APROVADO.descricao();
        } else if (StatusAprovacao.AGUARDANDO.value().equals(getStatusAprovacao())) {
            return StatusAprovacao.AGUARDANDO.descricao();
        } else if (StatusAprovacao.REPROVADO.value().equals(getStatusAprovacao())) {
            return StatusAprovacao.REPROVADO.descricao();
        }
        return "";
    }
}