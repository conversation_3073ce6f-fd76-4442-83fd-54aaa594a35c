package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the config_ord_atendimento_basica table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="config_ord_atendimento_basica"
 */

public abstract class BaseConfiguracaoOrdenacaoAtendimentoBasica extends BaseRootVO implements Serializable {

	public static String REF = "ConfiguracaoOrdenacaoAtendimentoBasica";
	public static final String PROP_TIPO = "tipo";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_ORDEM = "ordem";
	public static final String PROP_CODIGO_COLUNA = "codigoColuna";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_USUARIO_CADASTRO = "usuarioCadastro";
	public static final String PROP_USUARIO_ALTERACAO = "usuarioAlteracao";
	public static final String PROP_DATA_ALTERACAO = "dataAlteracao";


	// constructors
	public BaseConfiguracaoOrdenacaoAtendimentoBasica () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseConfiguracaoOrdenacaoAtendimentoBasica (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseConfiguracaoOrdenacaoAtendimentoBasica (
		java.lang.Long codigo,
		java.lang.Long codigoColuna,
		java.lang.Long ordem,
		java.lang.Long tipo,
		java.util.Date dataCadastro,
		java.util.Date dataAlteracao) {

		this.setCodigo(codigo);
		this.setCodigoColuna(codigoColuna);
		this.setOrdem(ordem);
		this.setTipo(tipo);
		this.setDataCadastro(dataCadastro);
		this.setDataAlteracao(dataAlteracao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long codigoColuna;
	private java.lang.Long ordem;
	private java.lang.Long tipo;
	private java.util.Date dataCadastro;
	private java.util.Date dataAlteracao;

	// many to one
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioAlteracao;
	private br.com.ksisolucoes.vo.basico.Empresa empresa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_config_ord_atendimento_basica"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cod_coluna
	 */
	public java.lang.Long getCodigoColuna () {
		return getPropertyValue(this, codigoColuna, PROP_CODIGO_COLUNA); 
	}

	/**
	 * Set the value related to the column: cod_coluna
	 * @param codigoColuna the cod_coluna value
	 */
	public void setCodigoColuna (java.lang.Long codigoColuna) {
//        java.lang.Long codigoColunaOld = this.codigoColuna;
		this.codigoColuna = codigoColuna;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoColuna", codigoColunaOld, codigoColuna);
	}



	/**
	 * Return the value associated with the column: ordem
	 */
	public java.lang.Long getOrdem () {
		return getPropertyValue(this, ordem, PROP_ORDEM); 
	}

	/**
	 * Set the value related to the column: ordem
	 * @param ordem the ordem value
	 */
	public void setOrdem (java.lang.Long ordem) {
//        java.lang.Long ordemOld = this.ordem;
		this.ordem = ordem;
//        this.getPropertyChangeSupport().firePropertyChange ("ordem", ordemOld, ordem);
	}



	/**
	 * Return the value associated with the column: tipo
	 */
	public java.lang.Long getTipo () {
		return getPropertyValue(this, tipo, PROP_TIPO); 
	}

	/**
	 * Set the value related to the column: tipo
	 * @param tipo the tipo value
	 */
	public void setTipo (java.lang.Long tipo) {
//        java.lang.Long tipoOld = this.tipo;
		this.tipo = tipo;
//        this.getPropertyChangeSupport().firePropertyChange ("tipo", tipoOld, tipo);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_alteracao
	 */
	public java.util.Date getDataAlteracao () {
		return getPropertyValue(this, dataAlteracao, PROP_DATA_ALTERACAO); 
	}

	/**
	 * Set the value related to the column: dt_alteracao
	 * @param dataAlteracao the dt_alteracao value
	 */
	public void setDataAlteracao (java.util.Date dataAlteracao) {
//        java.util.Date dataAlteracaoOld = this.dataAlteracao;
		this.dataAlteracao = dataAlteracao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAlteracao", dataAlteracaoOld, dataAlteracao);
	}



	/**
	 * Return the value associated with the column: cd_usuario_cadastro
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCadastro () {
		return getPropertyValue(this, usuarioCadastro, PROP_USUARIO_CADASTRO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_cadastro
	 * @param usuarioCadastro the cd_usuario_cadastro value
	 */
	public void setUsuarioCadastro (br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCadastroOld = this.usuarioCadastro;
		this.usuarioCadastro = usuarioCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadastro", usuarioCadastroOld, usuarioCadastro);
	}



	/**
	 * Return the value associated with the column: cd_usuario_alteracao
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioAlteracao () {
		return getPropertyValue(this, usuarioAlteracao, PROP_USUARIO_ALTERACAO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_alteracao
	 * @param usuarioAlteracao the cd_usuario_alteracao value
	 */
	public void setUsuarioAlteracao (br.com.ksisolucoes.vo.controle.Usuario usuarioAlteracao) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioAlteracaoOld = this.usuarioAlteracao;
		this.usuarioAlteracao = usuarioAlteracao;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioAlteracao", usuarioAlteracaoOld, usuarioAlteracao);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.ConfiguracaoOrdenacaoAtendimentoBasica)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.ConfiguracaoOrdenacaoAtendimentoBasica configuracaoOrdenacaoAtendimentoBasica = (br.com.ksisolucoes.vo.prontuario.basico.ConfiguracaoOrdenacaoAtendimentoBasica) obj;
			if (null == this.getCodigo() || null == configuracaoOrdenacaoAtendimentoBasica.getCodigo()) return false;
			else return (this.getCodigo().equals(configuracaoOrdenacaoAtendimentoBasica.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}