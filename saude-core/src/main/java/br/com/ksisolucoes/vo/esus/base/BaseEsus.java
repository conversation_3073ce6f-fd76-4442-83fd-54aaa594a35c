package br.com.ksisolucoes.vo.esus.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the esus table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="esus"
 */

public abstract class BaseEsus extends BaseRootVO implements Serializable {

	public static String REF = "Esus";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_STATUS = "status";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_INICIAL = "dataInicial";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_DATA_FINAL = "dataFinal";
	public static final String PROP_EXPORTACAO_ESUS_PROCESSO = "exportacaoEsusProcesso";
	public static final String PROP_DATA_GERACAO = "dataGeracao";


	// constructors
	public BaseEsus () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEsus (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseEsus (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataGeracao,
		java.util.Date dataInicial,
		java.lang.Long status,
		java.util.Date dataFinal) {

		this.setCodigo(codigo);
		this.setUsuario(usuario);
		this.setDataGeracao(dataGeracao);
		this.setDataInicial(dataInicial);
		this.setStatus(status);
		this.setDataFinal(dataFinal);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataGeracao;
	private java.util.Date dataInicial;
	private java.lang.Long status;
	private java.util.Date dataFinal;

	// many to one
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private br.com.ksisolucoes.vo.cadsus.ExportacaoEsusProcesso exportacaoEsusProcesso;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_esus"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_geracao
	 */
	public java.util.Date getDataGeracao () {
		return getPropertyValue(this, dataGeracao, PROP_DATA_GERACAO); 
	}

	/**
	 * Set the value related to the column: dt_geracao
	 * @param dataGeracao the dt_geracao value
	 */
	public void setDataGeracao (java.util.Date dataGeracao) {
//        java.util.Date dataGeracaoOld = this.dataGeracao;
		this.dataGeracao = dataGeracao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataGeracao", dataGeracaoOld, dataGeracao);
	}



	/**
	 * Return the value associated with the column: dt_inicial
	 */
	public java.util.Date getDataInicial () {
		return getPropertyValue(this, dataInicial, PROP_DATA_INICIAL); 
	}

	/**
	 * Set the value related to the column: dt_inicial
	 * @param dataInicial the dt_inicial value
	 */
	public void setDataInicial (java.util.Date dataInicial) {
//        java.util.Date dataInicialOld = this.dataInicial;
		this.dataInicial = dataInicial;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInicial", dataInicialOld, dataInicial);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: dt_final
	 */
	public java.util.Date getDataFinal () {
		return getPropertyValue(this, dataFinal, PROP_DATA_FINAL); 
	}

	/**
	 * Set the value related to the column: dt_final
	 * @param dataFinal the dt_final value
	 */
	public void setDataFinal (java.util.Date dataFinal) {
//        java.util.Date dataFinalOld = this.dataFinal;
		this.dataFinal = dataFinal;
//        this.getPropertyChangeSupport().firePropertyChange ("dataFinal", dataFinalOld, dataFinal);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: cd_exp_proc
	 */
	public br.com.ksisolucoes.vo.cadsus.ExportacaoEsusProcesso getExportacaoEsusProcesso () {
		return getPropertyValue(this, exportacaoEsusProcesso, PROP_EXPORTACAO_ESUS_PROCESSO); 
	}

	/**
	 * Set the value related to the column: cd_exp_proc
	 * @param exportacaoEsusProcesso the cd_exp_proc value
	 */
	public void setExportacaoEsusProcesso (br.com.ksisolucoes.vo.cadsus.ExportacaoEsusProcesso exportacaoEsusProcesso) {
//        br.com.ksisolucoes.vo.cadsus.ExportacaoEsusProcesso exportacaoEsusProcessoOld = this.exportacaoEsusProcesso;
		this.exportacaoEsusProcesso = exportacaoEsusProcesso;
//        this.getPropertyChangeSupport().firePropertyChange ("exportacaoEsusProcesso", exportacaoEsusProcessoOld, exportacaoEsusProcesso);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.esus.Esus)) return false;
		else {
			br.com.ksisolucoes.vo.esus.Esus esus = (br.com.ksisolucoes.vo.esus.Esus) obj;
			if (null == this.getCodigo() || null == esus.getCodigo()) return false;
			else return (this.getCodigo().equals(esus.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}