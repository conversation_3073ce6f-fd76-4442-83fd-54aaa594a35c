<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vacina"  >
	<class name="TipoVacina" table="tipo_vacina" >

        <id
            column="cd_vacina"
            name="codigo"
            type="java.lang.Long"
            >
            <generator class="assigned" />
        </id> <version column="version" name="version" type="long" />
        
        <property
            column="ds_vacina"
            length="50"
            name="descricao"
            not-null="true"
            type="java.lang.String"
            />

        <many-to-one
            class="br.com.ksisolucoes.vo.entradas.estoque.SubGrupo"
            name="subGrupo"
            not-null="true"
            >
            <column name="cod_sub" />
            <column name="cod_gru" />
        </many-to-one>
        
        <property
            column="validade_aberta"
            name="validadeAberta"
            type="java.lang.Long"
            />
        
        <property
            column="version_all"
            name="versionAll"
            type="java.lang.Long"
            />
        
        <property
            column="tempo_aberta"
            name="tempoAberta"
            type="java.lang.Long"
            not-null="true"
            />
        
        <property
            column="mensagem_alerta"
            length="1024"
            name="mensagemAlerta"
            not-null="false"
            type="java.lang.String"
            />

        <property
                column="tipo_esus"
                name="tipoEsus"
                not-null="false"
                type="java.lang.Long"
        />

        <property
            column="vacina_nao_existe_calendario"
            length="1"
            name="flagVacinaNaoExisteCalendario"
            not-null="true"
            type="string"
        />

        <property
                column="flag_inserido_celk"
                name="flagInseridoCelk"
                type="java.lang.Long"
        />


        <many-to-one
                class="br.com.ksisolucoes.vo.vacina.ViaAdministracao"
                name="viaAdministracao"
        >
            <column name="cd_via_administracao" />
        </many-to-one>
        
	</class>
</hibernate-mapping>
