package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the exame_apac_medicamentos table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="exame_apac_medicamentos"
 */

public abstract class BaseExameApacMedicamentos extends BaseRootVO implements Serializable {

	public static String REF = "ExameApacMedicamentos";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_SITUACAO = "situacao";
	public static final String PROP_REFERENCIA = "referencia";
	public static final String PROP_MEDICAMENTO = "medicamento";


	// constructors
	public BaseExameApacMedicamentos () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseExameApacMedicamentos (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String referencia;
	private java.lang.String medicamento;
	private java.lang.Long situacao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_medicamento"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_referencia
	 */
	public java.lang.String getReferencia () {
		return getPropertyValue(this, referencia, PROP_REFERENCIA); 
	}

	/**
	 * Set the value related to the column: cd_referencia
	 * @param referencia the cd_referencia value
	 */
	public void setReferencia (java.lang.String referencia) {
//        java.lang.String referenciaOld = this.referencia;
		this.referencia = referencia;
//        this.getPropertyChangeSupport().firePropertyChange ("referencia", referenciaOld, referencia);
	}



	/**
	 * Return the value associated with the column: ds_medicamento
	 */
	public java.lang.String getMedicamento () {
		return getPropertyValue(this, medicamento, PROP_MEDICAMENTO); 
	}

	/**
	 * Set the value related to the column: ds_medicamento
	 * @param medicamento the ds_medicamento value
	 */
	public void setMedicamento (java.lang.String medicamento) {
//        java.lang.String medicamentoOld = this.medicamento;
		this.medicamento = medicamento;
//        this.getPropertyChangeSupport().firePropertyChange ("medicamento", medicamentoOld, medicamento);
	}



	/**
	 * Return the value associated with the column: situacao
	 */
	public java.lang.Long getSituacao () {
		return getPropertyValue(this, situacao, PROP_SITUACAO); 
	}

	/**
	 * Set the value related to the column: situacao
	 * @param situacao the situacao value
	 */
	public void setSituacao (java.lang.Long situacao) {
//        java.lang.Long situacaoOld = this.situacao;
		this.situacao = situacao;
//        this.getPropertyChangeSupport().firePropertyChange ("situacao", situacaoOld, situacao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.ExameApacMedicamentos)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.ExameApacMedicamentos exameApacMedicamentos = (br.com.ksisolucoes.vo.prontuario.basico.ExameApacMedicamentos) obj;
			if (null == this.getCodigo() || null == exameApacMedicamentos.getCodigo()) return false;
			else return (this.getCodigo().equals(exameApacMedicamentos.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}