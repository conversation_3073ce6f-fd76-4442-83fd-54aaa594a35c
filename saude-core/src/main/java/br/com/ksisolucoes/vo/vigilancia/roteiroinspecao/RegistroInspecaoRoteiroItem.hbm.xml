<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.roteiroinspecao">
    <class name="RegistroInspecaoRoteiroItem" table="registro_inspecao_roteiro_item">
        <id
                column="cd_reg_insp_rot_it"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="assigned"/>
        </id>

        <version column="version" name="version" type="long"/>

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecaoRoteiro"
                column="cd_reg_insp_rot"
                name="registroInspecaoRoteiro"
                not-null="true"
        />

        <property
                column="ds_subtitulo"
                name="subtitulo"
                type="java.lang.String"
                not-null="true"
        />

        <property
                column="ds_enquad_legal"
                name="enquadramentoLegal"
                type="java.lang.String"
                not-null="false"
        />

        <property
                column="cd_item_insp_copia"
                name="idItemInspecaoCopia"
                type="java.lang.Long"
                not-null="true"
        />
    </class>
</hibernate-mapping>