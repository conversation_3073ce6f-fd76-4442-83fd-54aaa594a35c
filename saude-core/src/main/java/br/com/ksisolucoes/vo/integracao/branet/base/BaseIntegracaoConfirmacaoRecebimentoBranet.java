package br.com.ksisolucoes.vo.integracao.branet.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the integra_confirma_receb_branet table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="integra_confirma_receb_branet"
 */

public abstract class BaseIntegracaoConfirmacaoRecebimentoBranet extends BaseRootVO implements Serializable {

	public static String REF = "IntegracaoConfirmacaoRecebimentoBranet";
	public static final String PROP_STATUS = "status";
	public static final String PROP_RESPOSTA = "resposta";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_TIPO_LOG = "tipoLog";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA = "data";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_JSON = "json";
	public static final String PROP_MENSAGEM = "mensagem";
	public static final String PROP_ID_CNPJ_INTEGRACAO = "idCnpjIntegracao";


	// constructors
	public BaseIntegracaoConfirmacaoRecebimentoBranet () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseIntegracaoConfirmacaoRecebimentoBranet (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseIntegracaoConfirmacaoRecebimentoBranet (
		java.lang.Long codigo,
		java.util.Date data,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setData(data);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long idCnpjIntegracao;
	private java.lang.String json;
	private java.lang.String mensagem;
	private java.util.Date data;
	private java.lang.String status;
	private java.lang.String tipoLog;
	private java.util.Date dataCadastro;
	private java.lang.Long resposta;

	// many to one
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_integra_confirma_receb_branet"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: id_cnpj_integracao
	 */
	public java.lang.Long getIdCnpjIntegracao () {
		return getPropertyValue(this, idCnpjIntegracao, PROP_ID_CNPJ_INTEGRACAO); 
	}

	/**
	 * Set the value related to the column: id_cnpj_integracao
	 * @param idCnpjIntegracao the id_cnpj_integracao value
	 */
	public void setIdCnpjIntegracao (java.lang.Long idCnpjIntegracao) {
//        java.lang.Long idCnpjIntegracaoOld = this.idCnpjIntegracao;
		this.idCnpjIntegracao = idCnpjIntegracao;
//        this.getPropertyChangeSupport().firePropertyChange ("idCnpjIntegracao", idCnpjIntegracaoOld, idCnpjIntegracao);
	}



	/**
	 * Return the value associated with the column: json
	 */
	public java.lang.String getJson () {
		return getPropertyValue(this, json, PROP_JSON); 
	}

	/**
	 * Set the value related to the column: json
	 * @param json the json value
	 */
	public void setJson (java.lang.String json) {
//        java.lang.String jsonOld = this.json;
		this.json = json;
//        this.getPropertyChangeSupport().firePropertyChange ("json", jsonOld, json);
	}



	/**
	 * Return the value associated with the column: mensagem
	 */
	public java.lang.String getMensagem () {
		return getPropertyValue(this, mensagem, PROP_MENSAGEM); 
	}

	/**
	 * Set the value related to the column: mensagem
	 * @param mensagem the mensagem value
	 */
	public void setMensagem (java.lang.String mensagem) {
//        java.lang.String mensagemOld = this.mensagem;
		this.mensagem = mensagem;
//        this.getPropertyChangeSupport().firePropertyChange ("mensagem", mensagemOld, mensagem);
	}



	/**
	 * Return the value associated with the column: data
	 */
	public java.util.Date getData () {
		return getPropertyValue(this, data, PROP_DATA); 
	}

	/**
	 * Set the value related to the column: data
	 * @param data the data value
	 */
	public void setData (java.util.Date data) {
//        java.util.Date dataOld = this.data;
		this.data = data;
//        this.getPropertyChangeSupport().firePropertyChange ("data", dataOld, data);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.String getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.String status) {
//        java.lang.String statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: tipo_log
	 */
	public java.lang.String getTipoLog () {
		return getPropertyValue(this, tipoLog, PROP_TIPO_LOG); 
	}

	/**
	 * Set the value related to the column: tipo_log
	 * @param tipoLog the tipo_log value
	 */
	public void setTipoLog (java.lang.String tipoLog) {
//        java.lang.String tipoLogOld = this.tipoLog;
		this.tipoLog = tipoLog;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoLog", tipoLogOld, tipoLog);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: resposta
	 */
	public java.lang.Long getResposta () {
		return getPropertyValue(this, resposta, PROP_RESPOSTA); 
	}

	/**
	 * Set the value related to the column: resposta
	 * @param resposta the resposta value
	 */
	public void setResposta (java.lang.Long resposta) {
//        java.lang.Long respostaOld = this.resposta;
		this.resposta = resposta;
//        this.getPropertyChangeSupport().firePropertyChange ("resposta", respostaOld, resposta);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.integracao.branet.IntegracaoConfirmacaoRecebimentoBranet)) return false;
		else {
			br.com.ksisolucoes.vo.integracao.branet.IntegracaoConfirmacaoRecebimentoBranet integracaoConfirmacaoRecebimentoBranet = (br.com.ksisolucoes.vo.integracao.branet.IntegracaoConfirmacaoRecebimentoBranet) obj;
			if (null == this.getCodigo() || null == integracaoConfirmacaoRecebimentoBranet.getCodigo()) return false;
			else return (this.getCodigo().equals(integracaoConfirmacaoRecebimentoBranet.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}