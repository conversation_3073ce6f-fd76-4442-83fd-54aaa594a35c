package br.com.ksisolucoes.vo.entradas.estoque;

import java.io.Serializable;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.vo.entradas.estoque.base.BaseClassificacaoContabil;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;



public class ClassificacaoContabil extends BaseClassificacaoContabil implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ClassificacaoContabil () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ClassificacaoContabil (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ClassificacaoContabil (
		java.lang.Long codigo,
		java.lang.String descricao) {

		super (
			codigo,
			descricao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    @Override
    public String getDescricaoVO() {
        return getDescricao();
    }

    @Override
    public String getIdentificador() {
        return Coalesce.asString(getCodigo());
    }
}