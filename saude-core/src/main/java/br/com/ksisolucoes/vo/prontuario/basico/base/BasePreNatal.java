package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the prenatal table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="prenatal"
 */

public abstract class BasePreNatal extends BaseRootVO implements Serializable {

	public static String REF = "PreNatal";
	public static final String PROP_DATA_PARTO = "dataParto";
	public static final String PROP_DATA_ATENDIMENTO_ODONTOLOGICO = "dataAtendimentoOdontologico";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_ABORTOS = "abortos";
	public static final String PROP_PESSOAIS_DIABETES = "pessoaisDiabetes";
	public static final String PROP_NASCIDOS_MORTOS = "nascidosMortos";
	public static final String PROP_DATA_ANTITETANICA_REFORCO = "dataAntitetanicaReforco";
	public static final String PROP_EXAME_CLINICO_NORMAL = "exameClinicoNormal";
	public static final String PROP_FAMILIARES_DIABETES = "familiaresDiabetes";
	public static final String PROP_PARTOS = "partos";
	public static final String PROP_HIST_TOXOPLASMOSE = "histToxoplasmose";
	public static final String PROP_HIST_HEMORRAGIA2_SEMESTRE = "histHemorragia2Semestre";
	public static final String PROP_FAMILIARES_HIPERTENSAO = "familiaresHipertensao";
	public static final String PROP_PESSOAIS_HIPERTENSAO = "pessoaisHipertensao";
	public static final String PROP_HIST_CIUR = "histCiur";
	public static final String PROP_NASCIDO_MAIOR_PESO = "nascidoMaiorPeso";
	public static final String PROP_ABORTO_OUTROS = "abortoOutros";
	public static final String PROP_PESSOAIS_CIRURGIA_PELVICA_UTERINA = "pessoaisCirurgiaPelvicaUterina";
	public static final String PROP_EXAME_CLINICO_CERVIX = "exameClinicoCervix";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_FAMILIARES_GEMELARES = "familiaresGemelares";
	public static final String PROP_DIAS_HOSPITALIZADA = "diasHospitalizada";
	public static final String PROP_ANTITETANICA_PREVIA = "antitetanicaPrevia";
	public static final String PROP_ANTITETANICA_PRIMEIRO_MES = "antitetanicaPrimeiroMes";
	public static final String PROP_PAPANICOLAU_NORMAL = "papanicolauNormal";
	public static final String PROP_HIST_ANEMIA = "histAnemia";
	public static final String PROP_DATA_OCORRENCIA = "dataOcorrencia";
	public static final String PROP_RECEM_NASCIDO_ABAIXO_PESO = "recemNascidoAbaixoPeso";
	public static final String PROP_DATA_HEPATITE_B_TERCEIRA_DOSE = "dataHepatiteBTerceiraDose";
	public static final String PROP_PESSOAIS_CARDIOPATIA = "pessoaisCardiopatia";
	public static final String PROP_QUAL_DROGA = "qualDroga";
	public static final String PROP_DATA_TERMINO_ULTIMA_GESTACAO = "dataTerminoUltimaGestacao";
	public static final String PROP_DATA_ANTITETANICA_PRIMEIRA_DOSE = "dataAntitetanicaPrimeiraDose";
	public static final String PROP_RACA = "raca";
	public static final String PROP_HIST_ISOIMUNIZACAO = "histIsoimunizacao";
	public static final String PROP_HIST_INCOMPET_ISTMO_CERVICAL = "histIncompetIstmoCervical";
	public static final String PROP_DESFECHO = "desfecho";
	public static final String PROP_PESSOAIS_DOENCA_MENTAL = "pessoaisDoencaMental";
	public static final String PROP_DATA_HEPATITE_B_PRIMEIRA_DOSE = "dataHepatiteBPrimeiraDose";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_PESSOAIS_INFECCAO_URINARIA = "pessoaisInfeccaoUrinaria";
	public static final String PROP_ANTITETANICA_SEGUNDO_MES = "antitetanicaSegundoMes";
	public static final String PROP_DATA_ULTIMA_MENSTRUACAO = "dataUltimaMenstruacao";
	public static final String PROP_DATA_ANTITETANICA_SEGUNDA_DOSE = "dataAntitetanicaSegundaDose";
	public static final String PROP_ABORTO_ECTOPICO = "abortoEctopico";
	public static final String PROP_HOSPITALIZACAO_GRAVIDEZ = "hospitalizacaoGravidez";
	public static final String PROP_IDADE_GESTACIONAL = "idadeGestacional";
	public static final String PROP_GESTACAO_ANTERIOR = "gestacaoAnterior";
	public static final String PROP_DATA_VDRL = "dataVdrl";
	public static final String PROP_TIPO_GRAVIDEZ = "tipoGravidez";
	public static final String PROP_PROBLEMA_MAMAS = "problemaMamas";
	public static final String PROP_HIST_OLIGO_POLIDRAMNIO = "histOligoPolidramnio";
	public static final String PROP_LOCAL_OCORRENCIA = "localOcorrencia";
	public static final String PROP_DATA_FECHAMENTO = "dataFechamento";
	public static final String PROP_LOCAL_TRANSFERENCIA = "localTransferencia";
	public static final String PROP_HIST_HEMORRAGIA1_SEMESTRE = "histHemorragia1Semestre";
	public static final String PROP_HIST_INFECCAO_URINARIA = "histInfeccaoUrinaria";
	public static final String PROP_FEBRE = "febre";
	public static final String PROP_PESSOAIS_INFERTILIDADE = "pessoaisInfertilidade";
	public static final String PROP_PARTOS_FORCEPS = "partosForceps";
	public static final String PROP_AMAMENTACAO = "amamentacao";
	public static final String PROP_INFLUENZA_IMUNIZADA = "influenzaImunizada";
	public static final String PROP_DATA_HEPATITE_B_SEGUNDA_DOSE = "dataHepatiteBSegundaDose";
	public static final String PROP_PARTICIPOU_ATIVIDADE_EDUCATIVA = "participouAtividadeEducativa";
	public static final String PROP_DATA_ATIVIDADE3 = "dataAtividade3";
	public static final String PROP_FINAL_GESTACAO_ANTERIOR1_ANO = "finalGestacaoAnterior1Ano";
	public static final String PROP_DATA_ATIVIDADE2 = "dataAtividade2";
	public static final String PROP_DATA_ATIVIDADE1 = "dataAtividade1";
	public static final String PROP_MORRERAM_APOS_PRIMEIRA_SEMANA = "morreramAposPrimeiraSemana";
	public static final String PROP_PARTOS_VAGINAIS = "partosVaginais";
	public static final String PROP_ALFABETIZADA = "alfabetizada";
	public static final String PROP_HIST_USO_INSULINA = "histUsoInsulina";
	public static final String PROP_DATA_ANTITETANICA_TERCEIRA_DOSE = "dataAntitetanicaTerceiraDose";
	public static final String PROP_HEPATITE_B_IMUNIZADA = "hepatiteBImunizada";
	public static final String PROP_HIST_ROTURA_PREMATURA_MEMBRANA = "histRoturaPrematuraMembrana";
	public static final String PROP_ESTADO_CIVIL = "estadoCivil";
	public static final String PROP_HIST_HIPERTENSAO_ARTERIAL = "histHipertensaoArterial";
	public static final String PROP_TIPO_PARTO = "tipoParto";
	public static final String PROP_HIST_SIFILIS = "histSifilis";
	public static final String PROP_NUMERO_SISPRENATAL = "numeroSisprenatal";
	public static final String PROP_COLPOSCOPIA_NORMAL = "colposcopiaNormal";
	public static final String PROP_EXAME_ODONTOLOGICO_NORMAL = "exameOdontologicoNormal";
	public static final String PROP_GRUPO = "grupo";
	public static final String PROP_ESCOLARIDADE = "escolaridade";
	public static final String PROP_PESSOAIS_OUTROS_DESCRICAO = "pessoaisOutrosDescricao";
	public static final String PROP_ABORTO_MOLAR = "abortoMolar";
	public static final String PROP_HIST_POS_DATISMO = "histPosDatismo";
	public static final String PROP_PESSOAIS_MA_FORMACAO = "pessoaisMaFormacao";
	public static final String PROP_NASCIDOS_VIVOS = "nascidosVivos";
	public static final String PROP_HIST_AIDS = "histAids";
	public static final String PROP_PESSOAIS_TROMBOEMBOLISMO = "pessoaisTromboembolismo";
	public static final String PROP_VIOLENCIA_DOMESTICA = "violenciaDomestica";
	public static final String PROP_NUMERO_CONSULTA = "numeroConsulta";
	public static final String PROP_DATA_ULTIMA_CONSULTA = "dataUltimaConsulta";
	public static final String PROP_REALIZOU_VISITA_MATERNIDADE = "realizouVisitaMaternidade";
	public static final String PROP_ESTATURA = "estatura";
	public static final String PROP_GESTACOES = "gestacoes";
	public static final String PROP_ANTITETANICA_IMUNIZADA = "antitetanicaImunizada";
	public static final String PROP_ANTITETANICA_TERCEIRO_MES = "antitetanicaTerceiroMes";
	public static final String PROP_PESSOAIS_OUTROS = "pessoaisOutros";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_TRANSFERENCIA = "transferencia";
	public static final String PROP_HEMORRAGIA = "hemorragia";
	public static final String PROP_VIVEM = "vivem";
	public static final String PROP_HIST_HEMORRAGIA3_SEMESTRE = "histHemorragia3Semestre";
	public static final String PROP_HIST_ECLAMPSIA = "histEclampsia";
	public static final String PROP_NUMERO_CIGARROS_POR_DIA = "numeroCigarrosPorDia";
	public static final String PROP_ALCOOL = "alcool";
	public static final String PROP_FUMA = "fuma";
	public static final String PROP_EXAME_MAMAS_NORMAL = "exameMamasNormal";
	public static final String PROP_HIST_DIABETES_GESTACIONAL = "histDiabetesGestacional";
	public static final String PROP_FAMILIARES_OUTROS = "familiaresOutros";
	public static final String PROP_DUVIDAS = "duvidas";
	public static final String PROP_REALIZOU_ATEND_ODONTO = "realizouAtendOdonto";
	public static final String PROP_HIST_AMEACA_PARTO_PREMATURO = "histAmeacaPartoPrematuro";
	public static final String PROP_GRAVIDEZ_RISCO = "gravidezRisco";
	public static final String PROP_VDRL = "vdrl";
	public static final String PROP_ABORTO_ESPONTANEO = "abortoEspontaneo";
	public static final String PROP_SENSIBILIZADA = "sensibilizada";
	public static final String PROP_PRESENCA_ACOMPANHANTE = "presencaAcompanhante";
	public static final String PROP_HIST_TRANSFUSAO = "histTransfusao";
	public static final String PROP_PARTOS_CESARIAS = "partosCesarias";
	public static final String PROP_PELVIS_NORMAL = "pelvisNormal";
	public static final String PROP_DATA_TRANSFERENCIA = "dataTransferencia";
	public static final String PROP_PESO_ANTERIOR = "pesoAnterior";
	public static final String PROP_DATA_APLICACAO_INFLUENZA = "dataAplicacaoInfluenza";
	public static final String PROP_DATA_PROVAVEL_PARTO = "dataProvavelParto";
	public static final String PROP_ESTABELECIMENTO = "estabelecimento";
	public static final String PROP_RH = "rh";
	public static final String PROP_FAMILIARES_OUTROS_DESCRICAO = "familiaresOutrosDescricao";
	public static final String PROP_OUTRAS_DROGAS = "outrasDrogas";
	public static final String PROP_FAMILIARES_MA_FORMACAO = "familiaresMaFormacao";
	public static final String PROP_GRAVIDEZ_PLANEJADA = "gravidezPlanejada";
	public static final String PROP_MORRERAM_NA_PRIMEIRA_SEMANA = "morreramNaPrimeiraSemana";
	public static final String PROP_PESO = "peso";
	public static final String PROP_STATUS = "status";
	public static final String PROP_DATA_VISITA_MATERNIDADE = "dataVisitaMaternidade";
	public static final String PROP_INFECCAO = "infeccao";
	public static final String PROP_USUARIO_FECHAMENTO = "usuarioFechamento";


	// constructors
	public BasePreNatal () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BasePreNatal (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BasePreNatal (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		br.com.ksisolucoes.vo.cadsus.Escolaridade escolaridade,
		br.com.ksisolucoes.vo.cadsus.EstadoCivil estadoCivil,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.controle.Usuario usuarioFechamento,
		java.lang.Long status,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setUsuarioCadsus(usuarioCadsus);
		this.setEscolaridade(escolaridade);
		this.setEstadoCivil(estadoCivil);
		this.setUsuario(usuario);
		this.setUsuarioFechamento(usuarioFechamento);
		this.setStatus(status);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long status;
	private java.lang.Long alfabetizada;
	private java.lang.Long familiaresGemelares;
	private java.lang.Long familiaresDiabetes;
	private java.lang.Long familiaresHipertensao;
	private java.lang.Long familiaresMaFormacao;
	private java.lang.Long familiaresOutros;
	private java.lang.String familiaresOutrosDescricao;
	private java.lang.Long pessoaisInfeccaoUrinaria;
	private java.lang.Long pessoaisInfertilidade;
	private java.lang.Long pessoaisCardiopatia;
	private java.lang.Long pessoaisDiabetes;
	private java.lang.Long pessoaisHipertensao;
	private java.lang.Long pessoaisCirurgiaPelvicaUterina;
	private java.lang.Long pessoaisMaFormacao;
	private java.lang.Long pessoaisOutros;
	private java.lang.String pessoaisOutrosDescricao;
	private java.lang.Long gestacoes;
	private java.lang.Long abortos;
	private java.lang.Long partos;
	private java.lang.Long partosVaginais;
	private java.lang.Long partosCesarias;
	private java.lang.Long nascidosVivos;
	private java.lang.Long nascidosMortos;
	private java.lang.Long vivem;
	private java.lang.Long morreramNaPrimeiraSemana;
	private java.lang.Long morreramAposPrimeiraSemana;
	private java.util.Date dataTerminoUltimaGestacao;
	private java.lang.Long amamentacao;
	private java.lang.Long recemNascidoAbaixoPeso;
	private java.lang.Long nascidoMaiorPeso;
	private java.lang.Double pesoAnterior;
	private java.lang.Double estatura;
	private java.util.Date dataProvavelParto;
	private java.util.Date dataUltimaMenstruacao;
	private java.lang.Long duvidas;
	private java.lang.Long antitetanicaPrevia;
	private java.lang.Long antitetanicaPrimeiroMes;
	private java.lang.Long antitetanicaSegundoMes;
	private java.lang.Long antitetanicaTerceiroMes;
	private java.lang.Long hospitalizacaoGravidez;
	private java.lang.Long diasHospitalizada;
	private java.lang.String grupo;
	private java.lang.Long rh;
	private java.lang.Long sensibilizada;
	private java.lang.Long transferencia;
	private java.util.Date dataTransferencia;
	private java.lang.String localTransferencia;
	private java.lang.Long exameClinicoNormal;
	private java.lang.Long exameMamasNormal;
	private java.lang.Long exameOdontologicoNormal;
	private java.lang.Long pelvisNormal;
	private java.lang.Long papanicolauNormal;
	private java.lang.Long colposcopiaNormal;
	private java.lang.Long exameClinicoCervix;
	private java.lang.Long vdrl;
	private java.util.Date dataVdrl;
	private java.lang.Long fuma;
	private java.lang.Long numeroCigarrosPorDia;
	private java.lang.Long gravidezRisco;
	private java.lang.Long numeroConsulta;
	private java.util.Date dataCadastro;
	private java.util.Date dataUltimaConsulta;
	private java.util.Date dataFechamento;
	private java.lang.String numeroSisprenatal;
	private java.util.Date dataParto;
	private java.util.Date dataAntitetanicaPrimeiraDose;
	private java.util.Date dataAntitetanicaSegundaDose;
	private java.util.Date dataAntitetanicaTerceiraDose;
	private java.lang.Long desfecho;
	private java.lang.Long gravidezPlanejada;
	private java.lang.Long tipoGravidez;
	private java.lang.Long alcool;
	private java.lang.Long violenciaDomestica;
	private java.lang.Long outrasDrogas;
	private java.lang.String qualDroga;
	private java.lang.Long pessoaisDoencaMental;
	private java.lang.Long pessoaisTromboembolismo;
	private java.lang.Long antitetanicaImunizada;
	private java.lang.Long hepatiteBImunizada;
	private java.lang.Long influenzaImunizada;
	private java.util.Date dataAntitetanicaReforco;
	private java.util.Date dataHepatiteBPrimeiraDose;
	private java.util.Date dataHepatiteBSegundaDose;
	private java.util.Date dataHepatiteBTerceiraDose;
	private java.util.Date dataAplicacaoInfluenza;
	private java.lang.Long participouAtividadeEducativa;
	private java.util.Date dataAtividade1;
	private java.util.Date dataAtividade2;
	private java.util.Date dataAtividade3;
	private java.lang.Long realizouVisitaMaternidade;
	private java.util.Date dataVisitaMaternidade;
	private java.lang.Long realizouAtendOdonto;
	private java.util.Date dataAtendimentoOdontologico;
	private java.lang.Long abortoEctopico;
	private java.lang.Long abortoMolar;
	private java.lang.Long partosForceps;
	private java.lang.Long gestacaoAnterior;
	private java.lang.Long finalGestacaoAnterior1Ano;
	private java.lang.Long abortoEspontaneo;
	private java.lang.Long abortoOutros;
	private java.lang.Long histUsoInsulina;
	private java.lang.Long histEclampsia;
	private java.lang.Long histPosDatismo;
	private java.lang.Long histRoturaPrematuraMembrana;
	private java.lang.Long histAmeacaPartoPrematuro;
	private java.lang.Long histIncompetIstmoCervical;
	private java.lang.Long histInfeccaoUrinaria;
	private java.lang.Long histSifilis;
	private java.lang.Long histTransfusao;
	private java.lang.Long histHemorragia1Semestre;
	private java.lang.Long histHemorragia2Semestre;
	private java.lang.Long histHemorragia3Semestre;
	private java.lang.Long histDiabetesGestacional;
	private java.lang.Long histHipertensaoArterial;
	private java.lang.Long histCiur;
	private java.lang.Long histOligoPolidramnio;
	private java.lang.Long histIsoimunizacao;
	private java.lang.Long histAnemia;
	private java.lang.Long histToxoplasmose;
	private java.lang.Long histAids;
	private java.lang.Long tipoParto;
	private java.lang.Long idadeGestacional;
	private java.lang.Double peso;
	private java.lang.Long localOcorrencia;
	private java.lang.String estabelecimento;
	private java.lang.Long presencaAcompanhante;
	private java.lang.Long febre;
	private java.lang.Long infeccao;
	private java.lang.Long hemorragia;
	private java.lang.Long problemaMamas;
	private java.util.Date dataOcorrencia;

	// many to one
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private br.com.ksisolucoes.vo.cadsus.Escolaridade escolaridade;
	private br.com.ksisolucoes.vo.cadsus.EstadoCivil estadoCivil;
	private br.com.ksisolucoes.vo.cadsus.Raca raca;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioFechamento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_prenatal"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: alfabetizada
	 */
	public java.lang.Long getAlfabetizada () {
		return getPropertyValue(this, alfabetizada, PROP_ALFABETIZADA); 
	}

	/**
	 * Set the value related to the column: alfabetizada
	 * @param alfabetizada the alfabetizada value
	 */
	public void setAlfabetizada (java.lang.Long alfabetizada) {
//        java.lang.Long alfabetizadaOld = this.alfabetizada;
		this.alfabetizada = alfabetizada;
//        this.getPropertyChangeSupport().firePropertyChange ("alfabetizada", alfabetizadaOld, alfabetizada);
	}



	/**
	 * Return the value associated with the column: fam_gemelares
	 */
	public java.lang.Long getFamiliaresGemelares () {
		return getPropertyValue(this, familiaresGemelares, PROP_FAMILIARES_GEMELARES); 
	}

	/**
	 * Set the value related to the column: fam_gemelares
	 * @param familiaresGemelares the fam_gemelares value
	 */
	public void setFamiliaresGemelares (java.lang.Long familiaresGemelares) {
//        java.lang.Long familiaresGemelaresOld = this.familiaresGemelares;
		this.familiaresGemelares = familiaresGemelares;
//        this.getPropertyChangeSupport().firePropertyChange ("familiaresGemelares", familiaresGemelaresOld, familiaresGemelares);
	}



	/**
	 * Return the value associated with the column: fam_diabetes
	 */
	public java.lang.Long getFamiliaresDiabetes () {
		return getPropertyValue(this, familiaresDiabetes, PROP_FAMILIARES_DIABETES); 
	}

	/**
	 * Set the value related to the column: fam_diabetes
	 * @param familiaresDiabetes the fam_diabetes value
	 */
	public void setFamiliaresDiabetes (java.lang.Long familiaresDiabetes) {
//        java.lang.Long familiaresDiabetesOld = this.familiaresDiabetes;
		this.familiaresDiabetes = familiaresDiabetes;
//        this.getPropertyChangeSupport().firePropertyChange ("familiaresDiabetes", familiaresDiabetesOld, familiaresDiabetes);
	}



	/**
	 * Return the value associated with the column: fam_hipertensao
	 */
	public java.lang.Long getFamiliaresHipertensao () {
		return getPropertyValue(this, familiaresHipertensao, PROP_FAMILIARES_HIPERTENSAO); 
	}

	/**
	 * Set the value related to the column: fam_hipertensao
	 * @param familiaresHipertensao the fam_hipertensao value
	 */
	public void setFamiliaresHipertensao (java.lang.Long familiaresHipertensao) {
//        java.lang.Long familiaresHipertensaoOld = this.familiaresHipertensao;
		this.familiaresHipertensao = familiaresHipertensao;
//        this.getPropertyChangeSupport().firePropertyChange ("familiaresHipertensao", familiaresHipertensaoOld, familiaresHipertensao);
	}



	/**
	 * Return the value associated with the column: fam_ma_formacao
	 */
	public java.lang.Long getFamiliaresMaFormacao () {
		return getPropertyValue(this, familiaresMaFormacao, PROP_FAMILIARES_MA_FORMACAO); 
	}

	/**
	 * Set the value related to the column: fam_ma_formacao
	 * @param familiaresMaFormacao the fam_ma_formacao value
	 */
	public void setFamiliaresMaFormacao (java.lang.Long familiaresMaFormacao) {
//        java.lang.Long familiaresMaFormacaoOld = this.familiaresMaFormacao;
		this.familiaresMaFormacao = familiaresMaFormacao;
//        this.getPropertyChangeSupport().firePropertyChange ("familiaresMaFormacao", familiaresMaFormacaoOld, familiaresMaFormacao);
	}



	/**
	 * Return the value associated with the column: fam_outros
	 */
	public java.lang.Long getFamiliaresOutros () {
		return getPropertyValue(this, familiaresOutros, PROP_FAMILIARES_OUTROS); 
	}

	/**
	 * Set the value related to the column: fam_outros
	 * @param familiaresOutros the fam_outros value
	 */
	public void setFamiliaresOutros (java.lang.Long familiaresOutros) {
//        java.lang.Long familiaresOutrosOld = this.familiaresOutros;
		this.familiaresOutros = familiaresOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("familiaresOutros", familiaresOutrosOld, familiaresOutros);
	}



	/**
	 * Return the value associated with the column: fam_outros_desc
	 */
	public java.lang.String getFamiliaresOutrosDescricao () {
		return getPropertyValue(this, familiaresOutrosDescricao, PROP_FAMILIARES_OUTROS_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: fam_outros_desc
	 * @param familiaresOutrosDescricao the fam_outros_desc value
	 */
	public void setFamiliaresOutrosDescricao (java.lang.String familiaresOutrosDescricao) {
//        java.lang.String familiaresOutrosDescricaoOld = this.familiaresOutrosDescricao;
		this.familiaresOutrosDescricao = familiaresOutrosDescricao;
//        this.getPropertyChangeSupport().firePropertyChange ("familiaresOutrosDescricao", familiaresOutrosDescricaoOld, familiaresOutrosDescricao);
	}



	/**
	 * Return the value associated with the column: pes_infec_urinaria
	 */
	public java.lang.Long getPessoaisInfeccaoUrinaria () {
		return getPropertyValue(this, pessoaisInfeccaoUrinaria, PROP_PESSOAIS_INFECCAO_URINARIA); 
	}

	/**
	 * Set the value related to the column: pes_infec_urinaria
	 * @param pessoaisInfeccaoUrinaria the pes_infec_urinaria value
	 */
	public void setPessoaisInfeccaoUrinaria (java.lang.Long pessoaisInfeccaoUrinaria) {
//        java.lang.Long pessoaisInfeccaoUrinariaOld = this.pessoaisInfeccaoUrinaria;
		this.pessoaisInfeccaoUrinaria = pessoaisInfeccaoUrinaria;
//        this.getPropertyChangeSupport().firePropertyChange ("pessoaisInfeccaoUrinaria", pessoaisInfeccaoUrinariaOld, pessoaisInfeccaoUrinaria);
	}



	/**
	 * Return the value associated with the column: pes_infertilidade
	 */
	public java.lang.Long getPessoaisInfertilidade () {
		return getPropertyValue(this, pessoaisInfertilidade, PROP_PESSOAIS_INFERTILIDADE); 
	}

	/**
	 * Set the value related to the column: pes_infertilidade
	 * @param pessoaisInfertilidade the pes_infertilidade value
	 */
	public void setPessoaisInfertilidade (java.lang.Long pessoaisInfertilidade) {
//        java.lang.Long pessoaisInfertilidadeOld = this.pessoaisInfertilidade;
		this.pessoaisInfertilidade = pessoaisInfertilidade;
//        this.getPropertyChangeSupport().firePropertyChange ("pessoaisInfertilidade", pessoaisInfertilidadeOld, pessoaisInfertilidade);
	}



	/**
	 * Return the value associated with the column: pes_cardiopatia
	 */
	public java.lang.Long getPessoaisCardiopatia () {
		return getPropertyValue(this, pessoaisCardiopatia, PROP_PESSOAIS_CARDIOPATIA); 
	}

	/**
	 * Set the value related to the column: pes_cardiopatia
	 * @param pessoaisCardiopatia the pes_cardiopatia value
	 */
	public void setPessoaisCardiopatia (java.lang.Long pessoaisCardiopatia) {
//        java.lang.Long pessoaisCardiopatiaOld = this.pessoaisCardiopatia;
		this.pessoaisCardiopatia = pessoaisCardiopatia;
//        this.getPropertyChangeSupport().firePropertyChange ("pessoaisCardiopatia", pessoaisCardiopatiaOld, pessoaisCardiopatia);
	}



	/**
	 * Return the value associated with the column: pes_diabetes
	 */
	public java.lang.Long getPessoaisDiabetes () {
		return getPropertyValue(this, pessoaisDiabetes, PROP_PESSOAIS_DIABETES); 
	}

	/**
	 * Set the value related to the column: pes_diabetes
	 * @param pessoaisDiabetes the pes_diabetes value
	 */
	public void setPessoaisDiabetes (java.lang.Long pessoaisDiabetes) {
//        java.lang.Long pessoaisDiabetesOld = this.pessoaisDiabetes;
		this.pessoaisDiabetes = pessoaisDiabetes;
//        this.getPropertyChangeSupport().firePropertyChange ("pessoaisDiabetes", pessoaisDiabetesOld, pessoaisDiabetes);
	}



	/**
	 * Return the value associated with the column: pes_hipertensao
	 */
	public java.lang.Long getPessoaisHipertensao () {
		return getPropertyValue(this, pessoaisHipertensao, PROP_PESSOAIS_HIPERTENSAO); 
	}

	/**
	 * Set the value related to the column: pes_hipertensao
	 * @param pessoaisHipertensao the pes_hipertensao value
	 */
	public void setPessoaisHipertensao (java.lang.Long pessoaisHipertensao) {
//        java.lang.Long pessoaisHipertensaoOld = this.pessoaisHipertensao;
		this.pessoaisHipertensao = pessoaisHipertensao;
//        this.getPropertyChangeSupport().firePropertyChange ("pessoaisHipertensao", pessoaisHipertensaoOld, pessoaisHipertensao);
	}



	/**
	 * Return the value associated with the column: pes_cir_pelv_uterina
	 */
	public java.lang.Long getPessoaisCirurgiaPelvicaUterina () {
		return getPropertyValue(this, pessoaisCirurgiaPelvicaUterina, PROP_PESSOAIS_CIRURGIA_PELVICA_UTERINA); 
	}

	/**
	 * Set the value related to the column: pes_cir_pelv_uterina
	 * @param pessoaisCirurgiaPelvicaUterina the pes_cir_pelv_uterina value
	 */
	public void setPessoaisCirurgiaPelvicaUterina (java.lang.Long pessoaisCirurgiaPelvicaUterina) {
//        java.lang.Long pessoaisCirurgiaPelvicaUterinaOld = this.pessoaisCirurgiaPelvicaUterina;
		this.pessoaisCirurgiaPelvicaUterina = pessoaisCirurgiaPelvicaUterina;
//        this.getPropertyChangeSupport().firePropertyChange ("pessoaisCirurgiaPelvicaUterina", pessoaisCirurgiaPelvicaUterinaOld, pessoaisCirurgiaPelvicaUterina);
	}



	/**
	 * Return the value associated with the column: pes_ma_formacao
	 */
	public java.lang.Long getPessoaisMaFormacao () {
		return getPropertyValue(this, pessoaisMaFormacao, PROP_PESSOAIS_MA_FORMACAO); 
	}

	/**
	 * Set the value related to the column: pes_ma_formacao
	 * @param pessoaisMaFormacao the pes_ma_formacao value
	 */
	public void setPessoaisMaFormacao (java.lang.Long pessoaisMaFormacao) {
//        java.lang.Long pessoaisMaFormacaoOld = this.pessoaisMaFormacao;
		this.pessoaisMaFormacao = pessoaisMaFormacao;
//        this.getPropertyChangeSupport().firePropertyChange ("pessoaisMaFormacao", pessoaisMaFormacaoOld, pessoaisMaFormacao);
	}



	/**
	 * Return the value associated with the column: pes_outros
	 */
	public java.lang.Long getPessoaisOutros () {
		return getPropertyValue(this, pessoaisOutros, PROP_PESSOAIS_OUTROS); 
	}

	/**
	 * Set the value related to the column: pes_outros
	 * @param pessoaisOutros the pes_outros value
	 */
	public void setPessoaisOutros (java.lang.Long pessoaisOutros) {
//        java.lang.Long pessoaisOutrosOld = this.pessoaisOutros;
		this.pessoaisOutros = pessoaisOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("pessoaisOutros", pessoaisOutrosOld, pessoaisOutros);
	}



	/**
	 * Return the value associated with the column: pes_outros_desc
	 */
	public java.lang.String getPessoaisOutrosDescricao () {
		return getPropertyValue(this, pessoaisOutrosDescricao, PROP_PESSOAIS_OUTROS_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: pes_outros_desc
	 * @param pessoaisOutrosDescricao the pes_outros_desc value
	 */
	public void setPessoaisOutrosDescricao (java.lang.String pessoaisOutrosDescricao) {
//        java.lang.String pessoaisOutrosDescricaoOld = this.pessoaisOutrosDescricao;
		this.pessoaisOutrosDescricao = pessoaisOutrosDescricao;
//        this.getPropertyChangeSupport().firePropertyChange ("pessoaisOutrosDescricao", pessoaisOutrosDescricaoOld, pessoaisOutrosDescricao);
	}



	/**
	 * Return the value associated with the column: gestacoes
	 */
	public java.lang.Long getGestacoes () {
		return getPropertyValue(this, gestacoes, PROP_GESTACOES); 
	}

	/**
	 * Set the value related to the column: gestacoes
	 * @param gestacoes the gestacoes value
	 */
	public void setGestacoes (java.lang.Long gestacoes) {
//        java.lang.Long gestacoesOld = this.gestacoes;
		this.gestacoes = gestacoes;
//        this.getPropertyChangeSupport().firePropertyChange ("gestacoes", gestacoesOld, gestacoes);
	}



	/**
	 * Return the value associated with the column: abortos
	 */
	public java.lang.Long getAbortos () {
		return getPropertyValue(this, abortos, PROP_ABORTOS); 
	}

	/**
	 * Set the value related to the column: abortos
	 * @param abortos the abortos value
	 */
	public void setAbortos (java.lang.Long abortos) {
//        java.lang.Long abortosOld = this.abortos;
		this.abortos = abortos;
//        this.getPropertyChangeSupport().firePropertyChange ("abortos", abortosOld, abortos);
	}



	/**
	 * Return the value associated with the column: partos
	 */
	public java.lang.Long getPartos () {
		return getPropertyValue(this, partos, PROP_PARTOS); 
	}

	/**
	 * Set the value related to the column: partos
	 * @param partos the partos value
	 */
	public void setPartos (java.lang.Long partos) {
//        java.lang.Long partosOld = this.partos;
		this.partos = partos;
//        this.getPropertyChangeSupport().firePropertyChange ("partos", partosOld, partos);
	}



	/**
	 * Return the value associated with the column: partos_vaginais
	 */
	public java.lang.Long getPartosVaginais () {
		return getPropertyValue(this, partosVaginais, PROP_PARTOS_VAGINAIS); 
	}

	/**
	 * Set the value related to the column: partos_vaginais
	 * @param partosVaginais the partos_vaginais value
	 */
	public void setPartosVaginais (java.lang.Long partosVaginais) {
//        java.lang.Long partosVaginaisOld = this.partosVaginais;
		this.partosVaginais = partosVaginais;
//        this.getPropertyChangeSupport().firePropertyChange ("partosVaginais", partosVaginaisOld, partosVaginais);
	}



	/**
	 * Return the value associated with the column: partos_cesarias
	 */
	public java.lang.Long getPartosCesarias () {
		return getPropertyValue(this, partosCesarias, PROP_PARTOS_CESARIAS); 
	}

	/**
	 * Set the value related to the column: partos_cesarias
	 * @param partosCesarias the partos_cesarias value
	 */
	public void setPartosCesarias (java.lang.Long partosCesarias) {
//        java.lang.Long partosCesariasOld = this.partosCesarias;
		this.partosCesarias = partosCesarias;
//        this.getPropertyChangeSupport().firePropertyChange ("partosCesarias", partosCesariasOld, partosCesarias);
	}



	/**
	 * Return the value associated with the column: nascidos_vivos
	 */
	public java.lang.Long getNascidosVivos () {
		return getPropertyValue(this, nascidosVivos, PROP_NASCIDOS_VIVOS); 
	}

	/**
	 * Set the value related to the column: nascidos_vivos
	 * @param nascidosVivos the nascidos_vivos value
	 */
	public void setNascidosVivos (java.lang.Long nascidosVivos) {
//        java.lang.Long nascidosVivosOld = this.nascidosVivos;
		this.nascidosVivos = nascidosVivos;
//        this.getPropertyChangeSupport().firePropertyChange ("nascidosVivos", nascidosVivosOld, nascidosVivos);
	}



	/**
	 * Return the value associated with the column: nascidos_mortos
	 */
	public java.lang.Long getNascidosMortos () {
		return getPropertyValue(this, nascidosMortos, PROP_NASCIDOS_MORTOS); 
	}

	/**
	 * Set the value related to the column: nascidos_mortos
	 * @param nascidosMortos the nascidos_mortos value
	 */
	public void setNascidosMortos (java.lang.Long nascidosMortos) {
//        java.lang.Long nascidosMortosOld = this.nascidosMortos;
		this.nascidosMortos = nascidosMortos;
//        this.getPropertyChangeSupport().firePropertyChange ("nascidosMortos", nascidosMortosOld, nascidosMortos);
	}



	/**
	 * Return the value associated with the column: vivem
	 */
	public java.lang.Long getVivem () {
		return getPropertyValue(this, vivem, PROP_VIVEM); 
	}

	/**
	 * Set the value related to the column: vivem
	 * @param vivem the vivem value
	 */
	public void setVivem (java.lang.Long vivem) {
//        java.lang.Long vivemOld = this.vivem;
		this.vivem = vivem;
//        this.getPropertyChangeSupport().firePropertyChange ("vivem", vivemOld, vivem);
	}



	/**
	 * Return the value associated with the column: morr_prim_semana
	 */
	public java.lang.Long getMorreramNaPrimeiraSemana () {
		return getPropertyValue(this, morreramNaPrimeiraSemana, PROP_MORRERAM_NA_PRIMEIRA_SEMANA); 
	}

	/**
	 * Set the value related to the column: morr_prim_semana
	 * @param morreramNaPrimeiraSemana the morr_prim_semana value
	 */
	public void setMorreramNaPrimeiraSemana (java.lang.Long morreramNaPrimeiraSemana) {
//        java.lang.Long morreramNaPrimeiraSemanaOld = this.morreramNaPrimeiraSemana;
		this.morreramNaPrimeiraSemana = morreramNaPrimeiraSemana;
//        this.getPropertyChangeSupport().firePropertyChange ("morreramNaPrimeiraSemana", morreramNaPrimeiraSemanaOld, morreramNaPrimeiraSemana);
	}



	/**
	 * Return the value associated with the column: morr_pos_prim_semana
	 */
	public java.lang.Long getMorreramAposPrimeiraSemana () {
		return getPropertyValue(this, morreramAposPrimeiraSemana, PROP_MORRERAM_APOS_PRIMEIRA_SEMANA); 
	}

	/**
	 * Set the value related to the column: morr_pos_prim_semana
	 * @param morreramAposPrimeiraSemana the morr_pos_prim_semana value
	 */
	public void setMorreramAposPrimeiraSemana (java.lang.Long morreramAposPrimeiraSemana) {
//        java.lang.Long morreramAposPrimeiraSemanaOld = this.morreramAposPrimeiraSemana;
		this.morreramAposPrimeiraSemana = morreramAposPrimeiraSemana;
//        this.getPropertyChangeSupport().firePropertyChange ("morreramAposPrimeiraSemana", morreramAposPrimeiraSemanaOld, morreramAposPrimeiraSemana);
	}



	/**
	 * Return the value associated with the column: dt_term_ult_gestacao
	 */
	public java.util.Date getDataTerminoUltimaGestacao () {
		return getPropertyValue(this, dataTerminoUltimaGestacao, PROP_DATA_TERMINO_ULTIMA_GESTACAO); 
	}

	/**
	 * Set the value related to the column: dt_term_ult_gestacao
	 * @param dataTerminoUltimaGestacao the dt_term_ult_gestacao value
	 */
	public void setDataTerminoUltimaGestacao (java.util.Date dataTerminoUltimaGestacao) {
//        java.util.Date dataTerminoUltimaGestacaoOld = this.dataTerminoUltimaGestacao;
		this.dataTerminoUltimaGestacao = dataTerminoUltimaGestacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataTerminoUltimaGestacao", dataTerminoUltimaGestacaoOld, dataTerminoUltimaGestacao);
	}



	/**
	 * Return the value associated with the column: amamentacao
	 */
	public java.lang.Long getAmamentacao () {
		return getPropertyValue(this, amamentacao, PROP_AMAMENTACAO); 
	}

	/**
	 * Set the value related to the column: amamentacao
	 * @param amamentacao the amamentacao value
	 */
	public void setAmamentacao (java.lang.Long amamentacao) {
//        java.lang.Long amamentacaoOld = this.amamentacao;
		this.amamentacao = amamentacao;
//        this.getPropertyChangeSupport().firePropertyChange ("amamentacao", amamentacaoOld, amamentacao);
	}



	/**
	 * Return the value associated with the column: rn_abaixo_peso
	 */
	public java.lang.Long getRecemNascidoAbaixoPeso () {
		return getPropertyValue(this, recemNascidoAbaixoPeso, PROP_RECEM_NASCIDO_ABAIXO_PESO); 
	}

	/**
	 * Set the value related to the column: rn_abaixo_peso
	 * @param recemNascidoAbaixoPeso the rn_abaixo_peso value
	 */
	public void setRecemNascidoAbaixoPeso (java.lang.Long recemNascidoAbaixoPeso) {
//        java.lang.Long recemNascidoAbaixoPesoOld = this.recemNascidoAbaixoPeso;
		this.recemNascidoAbaixoPeso = recemNascidoAbaixoPeso;
//        this.getPropertyChangeSupport().firePropertyChange ("recemNascidoAbaixoPeso", recemNascidoAbaixoPesoOld, recemNascidoAbaixoPeso);
	}



	/**
	 * Return the value associated with the column: nasc_maior_peso
	 */
	public java.lang.Long getNascidoMaiorPeso () {
		return getPropertyValue(this, nascidoMaiorPeso, PROP_NASCIDO_MAIOR_PESO); 
	}

	/**
	 * Set the value related to the column: nasc_maior_peso
	 * @param nascidoMaiorPeso the nasc_maior_peso value
	 */
	public void setNascidoMaiorPeso (java.lang.Long nascidoMaiorPeso) {
//        java.lang.Long nascidoMaiorPesoOld = this.nascidoMaiorPeso;
		this.nascidoMaiorPeso = nascidoMaiorPeso;
//        this.getPropertyChangeSupport().firePropertyChange ("nascidoMaiorPeso", nascidoMaiorPesoOld, nascidoMaiorPeso);
	}



	/**
	 * Return the value associated with the column: peso_anterior
	 */
	public java.lang.Double getPesoAnterior () {
		return getPropertyValue(this, pesoAnterior, PROP_PESO_ANTERIOR); 
	}

	/**
	 * Set the value related to the column: peso_anterior
	 * @param pesoAnterior the peso_anterior value
	 */
	public void setPesoAnterior (java.lang.Double pesoAnterior) {
//        java.lang.Double pesoAnteriorOld = this.pesoAnterior;
		this.pesoAnterior = pesoAnterior;
//        this.getPropertyChangeSupport().firePropertyChange ("pesoAnterior", pesoAnteriorOld, pesoAnterior);
	}



	/**
	 * Return the value associated with the column: estatura
	 */
	public java.lang.Double getEstatura () {
		return getPropertyValue(this, estatura, PROP_ESTATURA); 
	}

	/**
	 * Set the value related to the column: estatura
	 * @param estatura the estatura value
	 */
	public void setEstatura (java.lang.Double estatura) {
		this.estatura = estatura;
	}



	/**
	 * Return the value associated with the column: dt_prov_parto
	 */
	public java.util.Date getDataProvavelParto () {
		return getPropertyValue(this, dataProvavelParto, PROP_DATA_PROVAVEL_PARTO); 
	}

	/**
	 * Set the value related to the column: dt_prov_parto
	 * @param dataProvavelParto the dt_prov_parto value
	 */
	public void setDataProvavelParto (java.util.Date dataProvavelParto) {
//        java.util.Date dataProvavelPartoOld = this.dataProvavelParto;
		this.dataProvavelParto = dataProvavelParto;
//        this.getPropertyChangeSupport().firePropertyChange ("dataProvavelParto", dataProvavelPartoOld, dataProvavelParto);
	}



	/**
	 * Return the value associated with the column: dt_ult_menst
	 */
	public java.util.Date getDataUltimaMenstruacao () {
		return getPropertyValue(this, dataUltimaMenstruacao, PROP_DATA_ULTIMA_MENSTRUACAO); 
	}

	/**
	 * Set the value related to the column: dt_ult_menst
	 * @param dataUltimaMenstruacao the dt_ult_menst value
	 */
	public void setDataUltimaMenstruacao (java.util.Date dataUltimaMenstruacao) {
//        java.util.Date dataUltimaMenstruacaoOld = this.dataUltimaMenstruacao;
		this.dataUltimaMenstruacao = dataUltimaMenstruacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUltimaMenstruacao", dataUltimaMenstruacaoOld, dataUltimaMenstruacao);
	}



	/**
	 * Return the value associated with the column: duvidas
	 */
	public java.lang.Long getDuvidas () {
		return getPropertyValue(this, duvidas, PROP_DUVIDAS); 
	}

	/**
	 * Set the value related to the column: duvidas
	 * @param duvidas the duvidas value
	 */
	public void setDuvidas (java.lang.Long duvidas) {
//        java.lang.Long duvidasOld = this.duvidas;
		this.duvidas = duvidas;
//        this.getPropertyChangeSupport().firePropertyChange ("duvidas", duvidasOld, duvidas);
	}



	/**
	 * Return the value associated with the column: antitetanica_previa
	 */
	public java.lang.Long getAntitetanicaPrevia () {
		return getPropertyValue(this, antitetanicaPrevia, PROP_ANTITETANICA_PREVIA); 
	}

	/**
	 * Set the value related to the column: antitetanica_previa
	 * @param antitetanicaPrevia the antitetanica_previa value
	 */
	public void setAntitetanicaPrevia (java.lang.Long antitetanicaPrevia) {
//        java.lang.Long antitetanicaPreviaOld = this.antitetanicaPrevia;
		this.antitetanicaPrevia = antitetanicaPrevia;
//        this.getPropertyChangeSupport().firePropertyChange ("antitetanicaPrevia", antitetanicaPreviaOld, antitetanicaPrevia);
	}



	/**
	 * Return the value associated with the column: antitetanica_pri_mes
	 */
	public java.lang.Long getAntitetanicaPrimeiroMes () {
		return getPropertyValue(this, antitetanicaPrimeiroMes, PROP_ANTITETANICA_PRIMEIRO_MES); 
	}

	/**
	 * Set the value related to the column: antitetanica_pri_mes
	 * @param antitetanicaPrimeiroMes the antitetanica_pri_mes value
	 */
	public void setAntitetanicaPrimeiroMes (java.lang.Long antitetanicaPrimeiroMes) {
//        java.lang.Long antitetanicaPrimeiroMesOld = this.antitetanicaPrimeiroMes;
		this.antitetanicaPrimeiroMes = antitetanicaPrimeiroMes;
//        this.getPropertyChangeSupport().firePropertyChange ("antitetanicaPrimeiroMes", antitetanicaPrimeiroMesOld, antitetanicaPrimeiroMes);
	}



	/**
	 * Return the value associated with the column: antitetanica_seg_mes
	 */
	public java.lang.Long getAntitetanicaSegundoMes () {
		return getPropertyValue(this, antitetanicaSegundoMes, PROP_ANTITETANICA_SEGUNDO_MES); 
	}

	/**
	 * Set the value related to the column: antitetanica_seg_mes
	 * @param antitetanicaSegundoMes the antitetanica_seg_mes value
	 */
	public void setAntitetanicaSegundoMes (java.lang.Long antitetanicaSegundoMes) {
//        java.lang.Long antitetanicaSegundoMesOld = this.antitetanicaSegundoMes;
		this.antitetanicaSegundoMes = antitetanicaSegundoMes;
//        this.getPropertyChangeSupport().firePropertyChange ("antitetanicaSegundoMes", antitetanicaSegundoMesOld, antitetanicaSegundoMes);
	}



	/**
	 * Return the value associated with the column: antitetanica_tec_mes
	 */
	public java.lang.Long getAntitetanicaTerceiroMes () {
		return getPropertyValue(this, antitetanicaTerceiroMes, PROP_ANTITETANICA_TERCEIRO_MES); 
	}

	/**
	 * Set the value related to the column: antitetanica_tec_mes
	 * @param antitetanicaTerceiroMes the antitetanica_tec_mes value
	 */
	public void setAntitetanicaTerceiroMes (java.lang.Long antitetanicaTerceiroMes) {
//        java.lang.Long antitetanicaTerceiroMesOld = this.antitetanicaTerceiroMes;
		this.antitetanicaTerceiroMes = antitetanicaTerceiroMes;
//        this.getPropertyChangeSupport().firePropertyChange ("antitetanicaTerceiroMes", antitetanicaTerceiroMesOld, antitetanicaTerceiroMes);
	}



	/**
	 * Return the value associated with the column: hosp_gravidez
	 */
	public java.lang.Long getHospitalizacaoGravidez () {
		return getPropertyValue(this, hospitalizacaoGravidez, PROP_HOSPITALIZACAO_GRAVIDEZ); 
	}

	/**
	 * Set the value related to the column: hosp_gravidez
	 * @param hospitalizacaoGravidez the hosp_gravidez value
	 */
	public void setHospitalizacaoGravidez (java.lang.Long hospitalizacaoGravidez) {
//        java.lang.Long hospitalizacaoGravidezOld = this.hospitalizacaoGravidez;
		this.hospitalizacaoGravidez = hospitalizacaoGravidez;
//        this.getPropertyChangeSupport().firePropertyChange ("hospitalizacaoGravidez", hospitalizacaoGravidezOld, hospitalizacaoGravidez);
	}



	/**
	 * Return the value associated with the column: dias_hosp
	 */
	public java.lang.Long getDiasHospitalizada () {
		return getPropertyValue(this, diasHospitalizada, PROP_DIAS_HOSPITALIZADA); 
	}

	/**
	 * Set the value related to the column: dias_hosp
	 * @param diasHospitalizada the dias_hosp value
	 */
	public void setDiasHospitalizada (java.lang.Long diasHospitalizada) {
//        java.lang.Long diasHospitalizadaOld = this.diasHospitalizada;
		this.diasHospitalizada = diasHospitalizada;
//        this.getPropertyChangeSupport().firePropertyChange ("diasHospitalizada", diasHospitalizadaOld, diasHospitalizada);
	}



	/**
	 * Return the value associated with the column: grupo
	 */
	public java.lang.String getGrupo () {
		return getPropertyValue(this, grupo, PROP_GRUPO); 
	}

	/**
	 * Set the value related to the column: grupo
	 * @param grupo the grupo value
	 */
	public void setGrupo (java.lang.String grupo) {
//        java.lang.String grupoOld = this.grupo;
		this.grupo = grupo;
//        this.getPropertyChangeSupport().firePropertyChange ("grupo", grupoOld, grupo);
	}



	/**
	 * Return the value associated with the column: rh
	 */
	public java.lang.Long getRh () {
		return getPropertyValue(this, rh, PROP_RH); 
	}

	/**
	 * Set the value related to the column: rh
	 * @param rh the rh value
	 */
	public void setRh (java.lang.Long rh) {
//        java.lang.Long rhOld = this.rh;
		this.rh = rh;
//        this.getPropertyChangeSupport().firePropertyChange ("rh", rhOld, rh);
	}



	/**
	 * Return the value associated with the column: sensib
	 */
	public java.lang.Long getSensibilizada () {
		return getPropertyValue(this, sensibilizada, PROP_SENSIBILIZADA); 
	}

	/**
	 * Set the value related to the column: sensib
	 * @param sensibilizada the sensib value
	 */
	public void setSensibilizada (java.lang.Long sensibilizada) {
//        java.lang.Long sensibilizadaOld = this.sensibilizada;
		this.sensibilizada = sensibilizada;
//        this.getPropertyChangeSupport().firePropertyChange ("sensibilizada", sensibilizadaOld, sensibilizada);
	}



	/**
	 * Return the value associated with the column: transf
	 */
	public java.lang.Long getTransferencia () {
		return getPropertyValue(this, transferencia, PROP_TRANSFERENCIA); 
	}

	/**
	 * Set the value related to the column: transf
	 * @param transferencia the transf value
	 */
	public void setTransferencia (java.lang.Long transferencia) {
//        java.lang.Long transferenciaOld = this.transferencia;
		this.transferencia = transferencia;
//        this.getPropertyChangeSupport().firePropertyChange ("transferencia", transferenciaOld, transferencia);
	}



	/**
	 * Return the value associated with the column: dt_transferencia
	 */
	public java.util.Date getDataTransferencia () {
		return getPropertyValue(this, dataTransferencia, PROP_DATA_TRANSFERENCIA); 
	}

	/**
	 * Set the value related to the column: dt_transferencia
	 * @param dataTransferencia the dt_transferencia value
	 */
	public void setDataTransferencia (java.util.Date dataTransferencia) {
//        java.util.Date dataTransferenciaOld = this.dataTransferencia;
		this.dataTransferencia = dataTransferencia;
//        this.getPropertyChangeSupport().firePropertyChange ("dataTransferencia", dataTransferenciaOld, dataTransferencia);
	}



	/**
	 * Return the value associated with the column: local_transferencia
	 */
	public java.lang.String getLocalTransferencia () {
		return getPropertyValue(this, localTransferencia, PROP_LOCAL_TRANSFERENCIA); 
	}

	/**
	 * Set the value related to the column: local_transferencia
	 * @param localTransferencia the local_transferencia value
	 */
	public void setLocalTransferencia (java.lang.String localTransferencia) {
//        java.lang.String localTransferenciaOld = this.localTransferencia;
		this.localTransferencia = localTransferencia;
//        this.getPropertyChangeSupport().firePropertyChange ("localTransferencia", localTransferenciaOld, localTransferencia);
	}



	/**
	 * Return the value associated with the column: exame_clin_normal
	 */
	public java.lang.Long getExameClinicoNormal () {
		return getPropertyValue(this, exameClinicoNormal, PROP_EXAME_CLINICO_NORMAL); 
	}

	/**
	 * Set the value related to the column: exame_clin_normal
	 * @param exameClinicoNormal the exame_clin_normal value
	 */
	public void setExameClinicoNormal (java.lang.Long exameClinicoNormal) {
//        java.lang.Long exameClinicoNormalOld = this.exameClinicoNormal;
		this.exameClinicoNormal = exameClinicoNormal;
//        this.getPropertyChangeSupport().firePropertyChange ("exameClinicoNormal", exameClinicoNormalOld, exameClinicoNormal);
	}



	/**
	 * Return the value associated with the column: exame_mamas_normal
	 */
	public java.lang.Long getExameMamasNormal () {
		return getPropertyValue(this, exameMamasNormal, PROP_EXAME_MAMAS_NORMAL); 
	}

	/**
	 * Set the value related to the column: exame_mamas_normal
	 * @param exameMamasNormal the exame_mamas_normal value
	 */
	public void setExameMamasNormal (java.lang.Long exameMamasNormal) {
//        java.lang.Long exameMamasNormalOld = this.exameMamasNormal;
		this.exameMamasNormal = exameMamasNormal;
//        this.getPropertyChangeSupport().firePropertyChange ("exameMamasNormal", exameMamasNormalOld, exameMamasNormal);
	}



	/**
	 * Return the value associated with the column: exame_odonto_normal
	 */
	public java.lang.Long getExameOdontologicoNormal () {
		return getPropertyValue(this, exameOdontologicoNormal, PROP_EXAME_ODONTOLOGICO_NORMAL); 
	}

	/**
	 * Set the value related to the column: exame_odonto_normal
	 * @param exameOdontologicoNormal the exame_odonto_normal value
	 */
	public void setExameOdontologicoNormal (java.lang.Long exameOdontologicoNormal) {
//        java.lang.Long exameOdontologicoNormalOld = this.exameOdontologicoNormal;
		this.exameOdontologicoNormal = exameOdontologicoNormal;
//        this.getPropertyChangeSupport().firePropertyChange ("exameOdontologicoNormal", exameOdontologicoNormalOld, exameOdontologicoNormal);
	}



	/**
	 * Return the value associated with the column: pelvis_normal
	 */
	public java.lang.Long getPelvisNormal () {
		return getPropertyValue(this, pelvisNormal, PROP_PELVIS_NORMAL); 
	}

	/**
	 * Set the value related to the column: pelvis_normal
	 * @param pelvisNormal the pelvis_normal value
	 */
	public void setPelvisNormal (java.lang.Long pelvisNormal) {
//        java.lang.Long pelvisNormalOld = this.pelvisNormal;
		this.pelvisNormal = pelvisNormal;
//        this.getPropertyChangeSupport().firePropertyChange ("pelvisNormal", pelvisNormalOld, pelvisNormal);
	}



	/**
	 * Return the value associated with the column: papanicolau_normal
	 */
	public java.lang.Long getPapanicolauNormal () {
		return getPropertyValue(this, papanicolauNormal, PROP_PAPANICOLAU_NORMAL); 
	}

	/**
	 * Set the value related to the column: papanicolau_normal
	 * @param papanicolauNormal the papanicolau_normal value
	 */
	public void setPapanicolauNormal (java.lang.Long papanicolauNormal) {
//        java.lang.Long papanicolauNormalOld = this.papanicolauNormal;
		this.papanicolauNormal = papanicolauNormal;
//        this.getPropertyChangeSupport().firePropertyChange ("papanicolauNormal", papanicolauNormalOld, papanicolauNormal);
	}



	/**
	 * Return the value associated with the column: colposcopia_normal
	 */
	public java.lang.Long getColposcopiaNormal () {
		return getPropertyValue(this, colposcopiaNormal, PROP_COLPOSCOPIA_NORMAL); 
	}

	/**
	 * Set the value related to the column: colposcopia_normal
	 * @param colposcopiaNormal the colposcopia_normal value
	 */
	public void setColposcopiaNormal (java.lang.Long colposcopiaNormal) {
//        java.lang.Long colposcopiaNormalOld = this.colposcopiaNormal;
		this.colposcopiaNormal = colposcopiaNormal;
//        this.getPropertyChangeSupport().firePropertyChange ("colposcopiaNormal", colposcopiaNormalOld, colposcopiaNormal);
	}



	/**
	 * Return the value associated with the column: exame_clinico_cervix
	 */
	public java.lang.Long getExameClinicoCervix () {
		return getPropertyValue(this, exameClinicoCervix, PROP_EXAME_CLINICO_CERVIX); 
	}

	/**
	 * Set the value related to the column: exame_clinico_cervix
	 * @param exameClinicoCervix the exame_clinico_cervix value
	 */
	public void setExameClinicoCervix (java.lang.Long exameClinicoCervix) {
//        java.lang.Long exameClinicoCervixOld = this.exameClinicoCervix;
		this.exameClinicoCervix = exameClinicoCervix;
//        this.getPropertyChangeSupport().firePropertyChange ("exameClinicoCervix", exameClinicoCervixOld, exameClinicoCervix);
	}



	/**
	 * Return the value associated with the column: vdrl
	 */
	public java.lang.Long getVdrl () {
		return getPropertyValue(this, vdrl, PROP_VDRL); 
	}

	/**
	 * Set the value related to the column: vdrl
	 * @param vdrl the vdrl value
	 */
	public void setVdrl (java.lang.Long vdrl) {
//        java.lang.Long vdrlOld = this.vdrl;
		this.vdrl = vdrl;
//        this.getPropertyChangeSupport().firePropertyChange ("vdrl", vdrlOld, vdrl);
	}



	/**
	 * Return the value associated with the column: dt_vdrl
	 */
	public java.util.Date getDataVdrl () {
		return getPropertyValue(this, dataVdrl, PROP_DATA_VDRL); 
	}

	/**
	 * Set the value related to the column: dt_vdrl
	 * @param dataVdrl the dt_vdrl value
	 */
	public void setDataVdrl (java.util.Date dataVdrl) {
//        java.util.Date dataVdrlOld = this.dataVdrl;
		this.dataVdrl = dataVdrl;
//        this.getPropertyChangeSupport().firePropertyChange ("dataVdrl", dataVdrlOld, dataVdrl);
	}



	/**
	 * Return the value associated with the column: fuma
	 */
	public java.lang.Long getFuma () {
		return getPropertyValue(this, fuma, PROP_FUMA); 
	}

	/**
	 * Set the value related to the column: fuma
	 * @param fuma the fuma value
	 */
	public void setFuma (java.lang.Long fuma) {
//        java.lang.Long fumaOld = this.fuma;
		this.fuma = fuma;
//        this.getPropertyChangeSupport().firePropertyChange ("fuma", fumaOld, fuma);
	}



	/**
	 * Return the value associated with the column: num_cigarros_dia
	 */
	public java.lang.Long getNumeroCigarrosPorDia () {
		return getPropertyValue(this, numeroCigarrosPorDia, PROP_NUMERO_CIGARROS_POR_DIA); 
	}

	/**
	 * Set the value related to the column: num_cigarros_dia
	 * @param numeroCigarrosPorDia the num_cigarros_dia value
	 */
	public void setNumeroCigarrosPorDia (java.lang.Long numeroCigarrosPorDia) {
//        java.lang.Long numeroCigarrosPorDiaOld = this.numeroCigarrosPorDia;
		this.numeroCigarrosPorDia = numeroCigarrosPorDia;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroCigarrosPorDia", numeroCigarrosPorDiaOld, numeroCigarrosPorDia);
	}



	/**
	 * Return the value associated with the column: gravidez_risco
	 */
	public java.lang.Long getGravidezRisco () {
		return getPropertyValue(this, gravidezRisco, PROP_GRAVIDEZ_RISCO); 
	}

	/**
	 * Set the value related to the column: gravidez_risco
	 * @param gravidezRisco the gravidez_risco value
	 */
	public void setGravidezRisco (java.lang.Long gravidezRisco) {
//        java.lang.Long gravidezRiscoOld = this.gravidezRisco;
		this.gravidezRisco = gravidezRisco;
//        this.getPropertyChangeSupport().firePropertyChange ("gravidezRisco", gravidezRiscoOld, gravidezRisco);
	}



	/**
	 * Return the value associated with the column: num_consulta
	 */
	public java.lang.Long getNumeroConsulta () {
		return getPropertyValue(this, numeroConsulta, PROP_NUMERO_CONSULTA); 
	}

	/**
	 * Set the value related to the column: num_consulta
	 * @param numeroConsulta the num_consulta value
	 */
	public void setNumeroConsulta (java.lang.Long numeroConsulta) {
//        java.lang.Long numeroConsultaOld = this.numeroConsulta;
		this.numeroConsulta = numeroConsulta;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroConsulta", numeroConsultaOld, numeroConsulta);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_ult_consulta
	 */
	public java.util.Date getDataUltimaConsulta () {
		return getPropertyValue(this, dataUltimaConsulta, PROP_DATA_ULTIMA_CONSULTA); 
	}

	/**
	 * Set the value related to the column: dt_ult_consulta
	 * @param dataUltimaConsulta the dt_ult_consulta value
	 */
	public void setDataUltimaConsulta (java.util.Date dataUltimaConsulta) {
//        java.util.Date dataUltimaConsultaOld = this.dataUltimaConsulta;
		this.dataUltimaConsulta = dataUltimaConsulta;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUltimaConsulta", dataUltimaConsultaOld, dataUltimaConsulta);
	}



	/**
	 * Return the value associated with the column: dt_fechamento
	 */
	public java.util.Date getDataFechamento () {
		return getPropertyValue(this, dataFechamento, PROP_DATA_FECHAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_fechamento
	 * @param dataFechamento the dt_fechamento value
	 */
	public void setDataFechamento (java.util.Date dataFechamento) {
//        java.util.Date dataFechamentoOld = this.dataFechamento;
		this.dataFechamento = dataFechamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataFechamento", dataFechamentoOld, dataFechamento);
	}



	/**
	 * Return the value associated with the column: nr_sisprenatal
	 */
	public java.lang.String getNumeroSisprenatal () {
		return getPropertyValue(this, numeroSisprenatal, PROP_NUMERO_SISPRENATAL); 
	}

	/**
	 * Set the value related to the column: nr_sisprenatal
	 * @param numeroSisprenatal the nr_sisprenatal value
	 */
	public void setNumeroSisprenatal (java.lang.String numeroSisprenatal) {
//        java.lang.String numeroSisprenatalOld = this.numeroSisprenatal;
		this.numeroSisprenatal = numeroSisprenatal;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroSisprenatal", numeroSisprenatalOld, numeroSisprenatal);
	}



	/**
	 * Return the value associated with the column: dt_parto
	 */
	public java.util.Date getDataParto () {
		return getPropertyValue(this, dataParto, PROP_DATA_PARTO); 
	}

	/**
	 * Set the value related to the column: dt_parto
	 * @param dataParto the dt_parto value
	 */
	public void setDataParto (java.util.Date dataParto) {
//        java.util.Date dataPartoOld = this.dataParto;
		this.dataParto = dataParto;
//        this.getPropertyChangeSupport().firePropertyChange ("dataParto", dataPartoOld, dataParto);
	}



	/**
	 * Return the value associated with the column: dt_antite_pri_dose
	 */
	public java.util.Date getDataAntitetanicaPrimeiraDose () {
		return getPropertyValue(this, dataAntitetanicaPrimeiraDose, PROP_DATA_ANTITETANICA_PRIMEIRA_DOSE); 
	}

	/**
	 * Set the value related to the column: dt_antite_pri_dose
	 * @param dataAntitetanicaPrimeiraDose the dt_antite_pri_dose value
	 */
	public void setDataAntitetanicaPrimeiraDose (java.util.Date dataAntitetanicaPrimeiraDose) {
//        java.util.Date dataAntitetanicaPrimeiraDoseOld = this.dataAntitetanicaPrimeiraDose;
		this.dataAntitetanicaPrimeiraDose = dataAntitetanicaPrimeiraDose;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAntitetanicaPrimeiraDose", dataAntitetanicaPrimeiraDoseOld, dataAntitetanicaPrimeiraDose);
	}



	/**
	 * Return the value associated with the column: dt_antite_seg_dose
	 */
	public java.util.Date getDataAntitetanicaSegundaDose () {
		return getPropertyValue(this, dataAntitetanicaSegundaDose, PROP_DATA_ANTITETANICA_SEGUNDA_DOSE); 
	}

	/**
	 * Set the value related to the column: dt_antite_seg_dose
	 * @param dataAntitetanicaSegundaDose the dt_antite_seg_dose value
	 */
	public void setDataAntitetanicaSegundaDose (java.util.Date dataAntitetanicaSegundaDose) {
//        java.util.Date dataAntitetanicaSegundaDoseOld = this.dataAntitetanicaSegundaDose;
		this.dataAntitetanicaSegundaDose = dataAntitetanicaSegundaDose;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAntitetanicaSegundaDose", dataAntitetanicaSegundaDoseOld, dataAntitetanicaSegundaDose);
	}



	/**
	 * Return the value associated with the column: dt_antite_ter_dose
	 */
	public java.util.Date getDataAntitetanicaTerceiraDose () {
		return getPropertyValue(this, dataAntitetanicaTerceiraDose, PROP_DATA_ANTITETANICA_TERCEIRA_DOSE); 
	}

	/**
	 * Set the value related to the column: dt_antite_ter_dose
	 * @param dataAntitetanicaTerceiraDose the dt_antite_ter_dose value
	 */
	public void setDataAntitetanicaTerceiraDose (java.util.Date dataAntitetanicaTerceiraDose) {
//        java.util.Date dataAntitetanicaTerceiraDoseOld = this.dataAntitetanicaTerceiraDose;
		this.dataAntitetanicaTerceiraDose = dataAntitetanicaTerceiraDose;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAntitetanicaTerceiraDose", dataAntitetanicaTerceiraDoseOld, dataAntitetanicaTerceiraDose);
	}



	/**
	 * Return the value associated with the column: desfecho
	 */
	public java.lang.Long getDesfecho () {
		return getPropertyValue(this, desfecho, PROP_DESFECHO); 
	}

	/**
	 * Set the value related to the column: desfecho
	 * @param desfecho the desfecho value
	 */
	public void setDesfecho (java.lang.Long desfecho) {
//        java.lang.Long desfechoOld = this.desfecho;
		this.desfecho = desfecho;
//        this.getPropertyChangeSupport().firePropertyChange ("desfecho", desfechoOld, desfecho);
	}



	/**
	 * Return the value associated with the column: gravidez_planejada
	 */
	public java.lang.Long getGravidezPlanejada () {
		return getPropertyValue(this, gravidezPlanejada, PROP_GRAVIDEZ_PLANEJADA); 
	}

	/**
	 * Set the value related to the column: gravidez_planejada
	 * @param gravidezPlanejada the gravidez_planejada value
	 */
	public void setGravidezPlanejada (java.lang.Long gravidezPlanejada) {
//        java.lang.Long gravidezPlanejadaOld = this.gravidezPlanejada;
		this.gravidezPlanejada = gravidezPlanejada;
//        this.getPropertyChangeSupport().firePropertyChange ("gravidezPlanejada", gravidezPlanejadaOld, gravidezPlanejada);
	}



	/**
	 * Return the value associated with the column: tp_gravidez
	 */
	public java.lang.Long getTipoGravidez () {
		return getPropertyValue(this, tipoGravidez, PROP_TIPO_GRAVIDEZ); 
	}

	/**
	 * Set the value related to the column: tp_gravidez
	 * @param tipoGravidez the tp_gravidez value
	 */
	public void setTipoGravidez (java.lang.Long tipoGravidez) {
//        java.lang.Long tipoGravidezOld = this.tipoGravidez;
		this.tipoGravidez = tipoGravidez;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoGravidez", tipoGravidezOld, tipoGravidez);
	}



	/**
	 * Return the value associated with the column: alcool
	 */
	public java.lang.Long getAlcool () {
		return getPropertyValue(this, alcool, PROP_ALCOOL); 
	}

	/**
	 * Set the value related to the column: alcool
	 * @param alcool the alcool value
	 */
	public void setAlcool (java.lang.Long alcool) {
//        java.lang.Long alcoolOld = this.alcool;
		this.alcool = alcool;
//        this.getPropertyChangeSupport().firePropertyChange ("alcool", alcoolOld, alcool);
	}



	/**
	 * Return the value associated with the column: violencia_domestica
	 */
	public java.lang.Long getViolenciaDomestica () {
		return getPropertyValue(this, violenciaDomestica, PROP_VIOLENCIA_DOMESTICA); 
	}

	/**
	 * Set the value related to the column: violencia_domestica
	 * @param violenciaDomestica the violencia_domestica value
	 */
	public void setViolenciaDomestica (java.lang.Long violenciaDomestica) {
//        java.lang.Long violenciaDomesticaOld = this.violenciaDomestica;
		this.violenciaDomestica = violenciaDomestica;
//        this.getPropertyChangeSupport().firePropertyChange ("violenciaDomestica", violenciaDomesticaOld, violenciaDomestica);
	}



	/**
	 * Return the value associated with the column: outras_drogas
	 */
	public java.lang.Long getOutrasDrogas () {
		return getPropertyValue(this, outrasDrogas, PROP_OUTRAS_DROGAS); 
	}

	/**
	 * Set the value related to the column: outras_drogas
	 * @param outrasDrogas the outras_drogas value
	 */
	public void setOutrasDrogas (java.lang.Long outrasDrogas) {
//        java.lang.Long outrasDrogasOld = this.outrasDrogas;
		this.outrasDrogas = outrasDrogas;
//        this.getPropertyChangeSupport().firePropertyChange ("outrasDrogas", outrasDrogasOld, outrasDrogas);
	}



	/**
	 * Return the value associated with the column: qual_droga
	 */
	public java.lang.String getQualDroga () {
		return getPropertyValue(this, qualDroga, PROP_QUAL_DROGA); 
	}

	/**
	 * Set the value related to the column: qual_droga
	 * @param qualDroga the qual_droga value
	 */
	public void setQualDroga (java.lang.String qualDroga) {
//        java.lang.String qualDrogaOld = this.qualDroga;
		this.qualDroga = qualDroga;
//        this.getPropertyChangeSupport().firePropertyChange ("qualDroga", qualDrogaOld, qualDroga);
	}



	/**
	 * Return the value associated with the column: pes_doenca_mental
	 */
	public java.lang.Long getPessoaisDoencaMental () {
		return getPropertyValue(this, pessoaisDoencaMental, PROP_PESSOAIS_DOENCA_MENTAL); 
	}

	/**
	 * Set the value related to the column: pes_doenca_mental
	 * @param pessoaisDoencaMental the pes_doenca_mental value
	 */
	public void setPessoaisDoencaMental (java.lang.Long pessoaisDoencaMental) {
//        java.lang.Long pessoaisDoencaMentalOld = this.pessoaisDoencaMental;
		this.pessoaisDoencaMental = pessoaisDoencaMental;
//        this.getPropertyChangeSupport().firePropertyChange ("pessoaisDoencaMental", pessoaisDoencaMentalOld, pessoaisDoencaMental);
	}



	/**
	 * Return the value associated with the column: pes_tromboembolismo
	 */
	public java.lang.Long getPessoaisTromboembolismo () {
		return getPropertyValue(this, pessoaisTromboembolismo, PROP_PESSOAIS_TROMBOEMBOLISMO); 
	}

	/**
	 * Set the value related to the column: pes_tromboembolismo
	 * @param pessoaisTromboembolismo the pes_tromboembolismo value
	 */
	public void setPessoaisTromboembolismo (java.lang.Long pessoaisTromboembolismo) {
//        java.lang.Long pessoaisTromboembolismoOld = this.pessoaisTromboembolismo;
		this.pessoaisTromboembolismo = pessoaisTromboembolismo;
//        this.getPropertyChangeSupport().firePropertyChange ("pessoaisTromboembolismo", pessoaisTromboembolismoOld, pessoaisTromboembolismo);
	}



	/**
	 * Return the value associated with the column: antitetanica_imuni
	 */
	public java.lang.Long getAntitetanicaImunizada () {
		return getPropertyValue(this, antitetanicaImunizada, PROP_ANTITETANICA_IMUNIZADA); 
	}

	/**
	 * Set the value related to the column: antitetanica_imuni
	 * @param antitetanicaImunizada the antitetanica_imuni value
	 */
	public void setAntitetanicaImunizada (java.lang.Long antitetanicaImunizada) {
//        java.lang.Long antitetanicaImunizadaOld = this.antitetanicaImunizada;
		this.antitetanicaImunizada = antitetanicaImunizada;
//        this.getPropertyChangeSupport().firePropertyChange ("antitetanicaImunizada", antitetanicaImunizadaOld, antitetanicaImunizada);
	}



	/**
	 * Return the value associated with the column: hepatite_b_imuni
	 */
	public java.lang.Long getHepatiteBImunizada () {
		return getPropertyValue(this, hepatiteBImunizada, PROP_HEPATITE_B_IMUNIZADA); 
	}

	/**
	 * Set the value related to the column: hepatite_b_imuni
	 * @param hepatiteBImunizada the hepatite_b_imuni value
	 */
	public void setHepatiteBImunizada (java.lang.Long hepatiteBImunizada) {
//        java.lang.Long hepatiteBImunizadaOld = this.hepatiteBImunizada;
		this.hepatiteBImunizada = hepatiteBImunizada;
//        this.getPropertyChangeSupport().firePropertyChange ("hepatiteBImunizada", hepatiteBImunizadaOld, hepatiteBImunizada);
	}



	/**
	 * Return the value associated with the column: influenza_imuni
	 */
	public java.lang.Long getInfluenzaImunizada () {
		return getPropertyValue(this, influenzaImunizada, PROP_INFLUENZA_IMUNIZADA); 
	}

	/**
	 * Set the value related to the column: influenza_imuni
	 * @param influenzaImunizada the influenza_imuni value
	 */
	public void setInfluenzaImunizada (java.lang.Long influenzaImunizada) {
//        java.lang.Long influenzaImunizadaOld = this.influenzaImunizada;
		this.influenzaImunizada = influenzaImunizada;
//        this.getPropertyChangeSupport().firePropertyChange ("influenzaImunizada", influenzaImunizadaOld, influenzaImunizada);
	}



	/**
	 * Return the value associated with the column: dt_antite_reforco
	 */
	public java.util.Date getDataAntitetanicaReforco () {
		return getPropertyValue(this, dataAntitetanicaReforco, PROP_DATA_ANTITETANICA_REFORCO); 
	}

	/**
	 * Set the value related to the column: dt_antite_reforco
	 * @param dataAntitetanicaReforco the dt_antite_reforco value
	 */
	public void setDataAntitetanicaReforco (java.util.Date dataAntitetanicaReforco) {
//        java.util.Date dataAntitetanicaReforcoOld = this.dataAntitetanicaReforco;
		this.dataAntitetanicaReforco = dataAntitetanicaReforco;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAntitetanicaReforco", dataAntitetanicaReforcoOld, dataAntitetanicaReforco);
	}



	/**
	 * Return the value associated with the column: dt_hepatite_b_pri_dose
	 */
	public java.util.Date getDataHepatiteBPrimeiraDose () {
		return getPropertyValue(this, dataHepatiteBPrimeiraDose, PROP_DATA_HEPATITE_B_PRIMEIRA_DOSE); 
	}

	/**
	 * Set the value related to the column: dt_hepatite_b_pri_dose
	 * @param dataHepatiteBPrimeiraDose the dt_hepatite_b_pri_dose value
	 */
	public void setDataHepatiteBPrimeiraDose (java.util.Date dataHepatiteBPrimeiraDose) {
//        java.util.Date dataHepatiteBPrimeiraDoseOld = this.dataHepatiteBPrimeiraDose;
		this.dataHepatiteBPrimeiraDose = dataHepatiteBPrimeiraDose;
//        this.getPropertyChangeSupport().firePropertyChange ("dataHepatiteBPrimeiraDose", dataHepatiteBPrimeiraDoseOld, dataHepatiteBPrimeiraDose);
	}



	/**
	 * Return the value associated with the column: dt_hepatite_b_seg_dose
	 */
	public java.util.Date getDataHepatiteBSegundaDose () {
		return getPropertyValue(this, dataHepatiteBSegundaDose, PROP_DATA_HEPATITE_B_SEGUNDA_DOSE); 
	}

	/**
	 * Set the value related to the column: dt_hepatite_b_seg_dose
	 * @param dataHepatiteBSegundaDose the dt_hepatite_b_seg_dose value
	 */
	public void setDataHepatiteBSegundaDose (java.util.Date dataHepatiteBSegundaDose) {
//        java.util.Date dataHepatiteBSegundaDoseOld = this.dataHepatiteBSegundaDose;
		this.dataHepatiteBSegundaDose = dataHepatiteBSegundaDose;
//        this.getPropertyChangeSupport().firePropertyChange ("dataHepatiteBSegundaDose", dataHepatiteBSegundaDoseOld, dataHepatiteBSegundaDose);
	}



	/**
	 * Return the value associated with the column: dt_hepatite_b_ter_dose
	 */
	public java.util.Date getDataHepatiteBTerceiraDose () {
		return getPropertyValue(this, dataHepatiteBTerceiraDose, PROP_DATA_HEPATITE_B_TERCEIRA_DOSE); 
	}

	/**
	 * Set the value related to the column: dt_hepatite_b_ter_dose
	 * @param dataHepatiteBTerceiraDose the dt_hepatite_b_ter_dose value
	 */
	public void setDataHepatiteBTerceiraDose (java.util.Date dataHepatiteBTerceiraDose) {
//        java.util.Date dataHepatiteBTerceiraDoseOld = this.dataHepatiteBTerceiraDose;
		this.dataHepatiteBTerceiraDose = dataHepatiteBTerceiraDose;
//        this.getPropertyChangeSupport().firePropertyChange ("dataHepatiteBTerceiraDose", dataHepatiteBTerceiraDoseOld, dataHepatiteBTerceiraDose);
	}



	/**
	 * Return the value associated with the column: dt_aplicacao_influenza
	 */
	public java.util.Date getDataAplicacaoInfluenza () {
		return getPropertyValue(this, dataAplicacaoInfluenza, PROP_DATA_APLICACAO_INFLUENZA); 
	}

	/**
	 * Set the value related to the column: dt_aplicacao_influenza
	 * @param dataAplicacaoInfluenza the dt_aplicacao_influenza value
	 */
	public void setDataAplicacaoInfluenza (java.util.Date dataAplicacaoInfluenza) {
//        java.util.Date dataAplicacaoInfluenzaOld = this.dataAplicacaoInfluenza;
		this.dataAplicacaoInfluenza = dataAplicacaoInfluenza;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAplicacaoInfluenza", dataAplicacaoInfluenzaOld, dataAplicacaoInfluenza);
	}



	/**
	 * Return the value associated with the column: participou_atvd_educativa
	 */
	public java.lang.Long getParticipouAtividadeEducativa () {
		return getPropertyValue(this, participouAtividadeEducativa, PROP_PARTICIPOU_ATIVIDADE_EDUCATIVA); 
	}

	/**
	 * Set the value related to the column: participou_atvd_educativa
	 * @param participouAtividadeEducativa the participou_atvd_educativa value
	 */
	public void setParticipouAtividadeEducativa (java.lang.Long participouAtividadeEducativa) {
//        java.lang.Long participouAtividadeEducativaOld = this.participouAtividadeEducativa;
		this.participouAtividadeEducativa = participouAtividadeEducativa;
//        this.getPropertyChangeSupport().firePropertyChange ("participouAtividadeEducativa", participouAtividadeEducativaOld, participouAtividadeEducativa);
	}



	/**
	 * Return the value associated with the column: dt_atvd_1
	 */
	public java.util.Date getDataAtividade1 () {
		return getPropertyValue(this, dataAtividade1, PROP_DATA_ATIVIDADE1); 
	}

	/**
	 * Set the value related to the column: dt_atvd_1
	 * @param dataAtividade1 the dt_atvd_1 value
	 */
	public void setDataAtividade1 (java.util.Date dataAtividade1) {
//        java.util.Date dataAtividade1Old = this.dataAtividade1;
		this.dataAtividade1 = dataAtividade1;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAtividade1", dataAtividade1Old, dataAtividade1);
	}



	/**
	 * Return the value associated with the column: dt_atvd_2
	 */
	public java.util.Date getDataAtividade2 () {
		return getPropertyValue(this, dataAtividade2, PROP_DATA_ATIVIDADE2); 
	}

	/**
	 * Set the value related to the column: dt_atvd_2
	 * @param dataAtividade2 the dt_atvd_2 value
	 */
	public void setDataAtividade2 (java.util.Date dataAtividade2) {
//        java.util.Date dataAtividade2Old = this.dataAtividade2;
		this.dataAtividade2 = dataAtividade2;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAtividade2", dataAtividade2Old, dataAtividade2);
	}



	/**
	 * Return the value associated with the column: dt_atvd_3
	 */
	public java.util.Date getDataAtividade3 () {
		return getPropertyValue(this, dataAtividade3, PROP_DATA_ATIVIDADE3); 
	}

	/**
	 * Set the value related to the column: dt_atvd_3
	 * @param dataAtividade3 the dt_atvd_3 value
	 */
	public void setDataAtividade3 (java.util.Date dataAtividade3) {
//        java.util.Date dataAtividade3Old = this.dataAtividade3;
		this.dataAtividade3 = dataAtividade3;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAtividade3", dataAtividade3Old, dataAtividade3);
	}



	/**
	 * Return the value associated with the column: realizou_visita_maternidade
	 */
	public java.lang.Long getRealizouVisitaMaternidade () {
		return getPropertyValue(this, realizouVisitaMaternidade, PROP_REALIZOU_VISITA_MATERNIDADE); 
	}

	/**
	 * Set the value related to the column: realizou_visita_maternidade
	 * @param realizouVisitaMaternidade the realizou_visita_maternidade value
	 */
	public void setRealizouVisitaMaternidade (java.lang.Long realizouVisitaMaternidade) {
//        java.lang.Long realizouVisitaMaternidadeOld = this.realizouVisitaMaternidade;
		this.realizouVisitaMaternidade = realizouVisitaMaternidade;
//        this.getPropertyChangeSupport().firePropertyChange ("realizouVisitaMaternidade", realizouVisitaMaternidadeOld, realizouVisitaMaternidade);
	}



	/**
	 * Return the value associated with the column: dt_visita_maternidade
	 */
	public java.util.Date getDataVisitaMaternidade () {
		return getPropertyValue(this, dataVisitaMaternidade, PROP_DATA_VISITA_MATERNIDADE); 
	}

	/**
	 * Set the value related to the column: dt_visita_maternidade
	 * @param dataVisitaMaternidade the dt_visita_maternidade value
	 */
	public void setDataVisitaMaternidade (java.util.Date dataVisitaMaternidade) {
//        java.util.Date dataVisitaMaternidadeOld = this.dataVisitaMaternidade;
		this.dataVisitaMaternidade = dataVisitaMaternidade;
//        this.getPropertyChangeSupport().firePropertyChange ("dataVisitaMaternidade", dataVisitaMaternidadeOld, dataVisitaMaternidade);
	}



	/**
	 * Return the value associated with the column: realizou_atend_odonto
	 */
	public java.lang.Long getRealizouAtendOdonto () {
		return getPropertyValue(this, realizouAtendOdonto, PROP_REALIZOU_ATEND_ODONTO); 
	}

	/**
	 * Set the value related to the column: realizou_atend_odonto
	 * @param realizouAtendOdonto the realizou_atend_odonto value
	 */
	public void setRealizouAtendOdonto (java.lang.Long realizouAtendOdonto) {
//        java.lang.Long realizouAtendOdontoOld = this.realizouAtendOdonto;
		this.realizouAtendOdonto = realizouAtendOdonto;
//        this.getPropertyChangeSupport().firePropertyChange ("realizouAtendOdonto", realizouAtendOdontoOld, realizouAtendOdonto);
	}



	/**
	 * Return the value associated with the column: dt_atend_odonto
	 */
	public java.util.Date getDataAtendimentoOdontologico () {
		return getPropertyValue(this, dataAtendimentoOdontologico, PROP_DATA_ATENDIMENTO_ODONTOLOGICO); 
	}

	/**
	 * Set the value related to the column: dt_atend_odonto
	 * @param dataAtendimentoOdontologico the dt_atend_odonto value
	 */
	public void setDataAtendimentoOdontologico (java.util.Date dataAtendimentoOdontologico) {
//        java.util.Date dataAtendimentoOdontologicoOld = this.dataAtendimentoOdontologico;
		this.dataAtendimentoOdontologico = dataAtendimentoOdontologico;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAtendimentoOdontologico", dataAtendimentoOdontologicoOld, dataAtendimentoOdontologico);
	}



	/**
	 * Return the value associated with the column: aborto_ectopico
	 */
	public java.lang.Long getAbortoEctopico () {
		return getPropertyValue(this, abortoEctopico, PROP_ABORTO_ECTOPICO); 
	}

	/**
	 * Set the value related to the column: aborto_ectopico
	 * @param abortoEctopico the aborto_ectopico value
	 */
	public void setAbortoEctopico (java.lang.Long abortoEctopico) {
//        java.lang.Long abortoEctopicoOld = this.abortoEctopico;
		this.abortoEctopico = abortoEctopico;
//        this.getPropertyChangeSupport().firePropertyChange ("abortoEctopico", abortoEctopicoOld, abortoEctopico);
	}



	/**
	 * Return the value associated with the column: aborto_molar
	 */
	public java.lang.Long getAbortoMolar () {
		return getPropertyValue(this, abortoMolar, PROP_ABORTO_MOLAR); 
	}

	/**
	 * Set the value related to the column: aborto_molar
	 * @param abortoMolar the aborto_molar value
	 */
	public void setAbortoMolar (java.lang.Long abortoMolar) {
//        java.lang.Long abortoMolarOld = this.abortoMolar;
		this.abortoMolar = abortoMolar;
//        this.getPropertyChangeSupport().firePropertyChange ("abortoMolar", abortoMolarOld, abortoMolar);
	}



	/**
	 * Return the value associated with the column: partos_forceps
	 */
	public java.lang.Long getPartosForceps () {
		return getPropertyValue(this, partosForceps, PROP_PARTOS_FORCEPS); 
	}

	/**
	 * Set the value related to the column: partos_forceps
	 * @param partosForceps the partos_forceps value
	 */
	public void setPartosForceps (java.lang.Long partosForceps) {
//        java.lang.Long partosForcepsOld = this.partosForceps;
		this.partosForceps = partosForceps;
//        this.getPropertyChangeSupport().firePropertyChange ("partosForceps", partosForcepsOld, partosForceps);
	}



	/**
	 * Return the value associated with the column: gestacao_anterior
	 */
	public java.lang.Long getGestacaoAnterior () {
		return getPropertyValue(this, gestacaoAnterior, PROP_GESTACAO_ANTERIOR); 
	}

	/**
	 * Set the value related to the column: gestacao_anterior
	 * @param gestacaoAnterior the gestacao_anterior value
	 */
	public void setGestacaoAnterior (java.lang.Long gestacaoAnterior) {
//        java.lang.Long gestacaoAnteriorOld = this.gestacaoAnterior;
		this.gestacaoAnterior = gestacaoAnterior;
//        this.getPropertyChangeSupport().firePropertyChange ("gestacaoAnterior", gestacaoAnteriorOld, gestacaoAnterior);
	}



	/**
	 * Return the value associated with the column: final_gestacao_ant_1_ano
	 */
	public java.lang.Long getFinalGestacaoAnterior1Ano () {
		return getPropertyValue(this, finalGestacaoAnterior1Ano, PROP_FINAL_GESTACAO_ANTERIOR1_ANO); 
	}

	/**
	 * Set the value related to the column: final_gestacao_ant_1_ano
	 * @param finalGestacaoAnterior1Ano the final_gestacao_ant_1_ano value
	 */
	public void setFinalGestacaoAnterior1Ano (java.lang.Long finalGestacaoAnterior1Ano) {
//        java.lang.Long finalGestacaoAnterior1AnoOld = this.finalGestacaoAnterior1Ano;
		this.finalGestacaoAnterior1Ano = finalGestacaoAnterior1Ano;
//        this.getPropertyChangeSupport().firePropertyChange ("finalGestacaoAnterior1Ano", finalGestacaoAnterior1AnoOld, finalGestacaoAnterior1Ano);
	}



	/**
	 * Return the value associated with the column: aborto_espontaneo
	 */
	public java.lang.Long getAbortoEspontaneo () {
		return getPropertyValue(this, abortoEspontaneo, PROP_ABORTO_ESPONTANEO); 
	}

	/**
	 * Set the value related to the column: aborto_espontaneo
	 * @param abortoEspontaneo the aborto_espontaneo value
	 */
	public void setAbortoEspontaneo (java.lang.Long abortoEspontaneo) {
//        java.lang.Long abortoEspontaneoOld = this.abortoEspontaneo;
		this.abortoEspontaneo = abortoEspontaneo;
//        this.getPropertyChangeSupport().firePropertyChange ("abortoEspontaneo", abortoEspontaneoOld, abortoEspontaneo);
	}



	/**
	 * Return the value associated with the column: aborto_outros
	 */
	public java.lang.Long getAbortoOutros () {
		return getPropertyValue(this, abortoOutros, PROP_ABORTO_OUTROS); 
	}

	/**
	 * Set the value related to the column: aborto_outros
	 * @param abortoOutros the aborto_outros value
	 */
	public void setAbortoOutros (java.lang.Long abortoOutros) {
//        java.lang.Long abortoOutrosOld = this.abortoOutros;
		this.abortoOutros = abortoOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("abortoOutros", abortoOutrosOld, abortoOutros);
	}



	/**
	 * Return the value associated with the column: hist_uso_insulina
	 */
	public java.lang.Long getHistUsoInsulina () {
		return getPropertyValue(this, histUsoInsulina, PROP_HIST_USO_INSULINA); 
	}

	/**
	 * Set the value related to the column: hist_uso_insulina
	 * @param histUsoInsulina the hist_uso_insulina value
	 */
	public void setHistUsoInsulina (java.lang.Long histUsoInsulina) {
//        java.lang.Long histUsoInsulinaOld = this.histUsoInsulina;
		this.histUsoInsulina = histUsoInsulina;
//        this.getPropertyChangeSupport().firePropertyChange ("histUsoInsulina", histUsoInsulinaOld, histUsoInsulina);
	}



	/**
	 * Return the value associated with the column: hist_eclampsia
	 */
	public java.lang.Long getHistEclampsia () {
		return getPropertyValue(this, histEclampsia, PROP_HIST_ECLAMPSIA); 
	}

	/**
	 * Set the value related to the column: hist_eclampsia
	 * @param histEclampsia the hist_eclampsia value
	 */
	public void setHistEclampsia (java.lang.Long histEclampsia) {
//        java.lang.Long histEclampsiaOld = this.histEclampsia;
		this.histEclampsia = histEclampsia;
//        this.getPropertyChangeSupport().firePropertyChange ("histEclampsia", histEclampsiaOld, histEclampsia);
	}



	/**
	 * Return the value associated with the column: hist_pos_datismo
	 */
	public java.lang.Long getHistPosDatismo () {
		return getPropertyValue(this, histPosDatismo, PROP_HIST_POS_DATISMO); 
	}

	/**
	 * Set the value related to the column: hist_pos_datismo
	 * @param histPosDatismo the hist_pos_datismo value
	 */
	public void setHistPosDatismo (java.lang.Long histPosDatismo) {
//        java.lang.Long histPosDatismoOld = this.histPosDatismo;
		this.histPosDatismo = histPosDatismo;
//        this.getPropertyChangeSupport().firePropertyChange ("histPosDatismo", histPosDatismoOld, histPosDatismo);
	}



	/**
	 * Return the value associated with the column: hist_rotura_prematura_membrana
	 */
	public java.lang.Long getHistRoturaPrematuraMembrana () {
		return getPropertyValue(this, histRoturaPrematuraMembrana, PROP_HIST_ROTURA_PREMATURA_MEMBRANA); 
	}

	/**
	 * Set the value related to the column: hist_rotura_prematura_membrana
	 * @param histRoturaPrematuraMembrana the hist_rotura_prematura_membrana value
	 */
	public void setHistRoturaPrematuraMembrana (java.lang.Long histRoturaPrematuraMembrana) {
//        java.lang.Long histRoturaPrematuraMembranaOld = this.histRoturaPrematuraMembrana;
		this.histRoturaPrematuraMembrana = histRoturaPrematuraMembrana;
//        this.getPropertyChangeSupport().firePropertyChange ("histRoturaPrematuraMembrana", histRoturaPrematuraMembranaOld, histRoturaPrematuraMembrana);
	}



	/**
	 * Return the value associated with the column: hist_ameaca_parto_prematuro
	 */
	public java.lang.Long getHistAmeacaPartoPrematuro () {
		return getPropertyValue(this, histAmeacaPartoPrematuro, PROP_HIST_AMEACA_PARTO_PREMATURO); 
	}

	/**
	 * Set the value related to the column: hist_ameaca_parto_prematuro
	 * @param histAmeacaPartoPrematuro the hist_ameaca_parto_prematuro value
	 */
	public void setHistAmeacaPartoPrematuro (java.lang.Long histAmeacaPartoPrematuro) {
//        java.lang.Long histAmeacaPartoPrematuroOld = this.histAmeacaPartoPrematuro;
		this.histAmeacaPartoPrematuro = histAmeacaPartoPrematuro;
//        this.getPropertyChangeSupport().firePropertyChange ("histAmeacaPartoPrematuro", histAmeacaPartoPrematuroOld, histAmeacaPartoPrematuro);
	}



	/**
	 * Return the value associated with the column: hist_incompet_istmo_cervical
	 */
	public java.lang.Long getHistIncompetIstmoCervical () {
		return getPropertyValue(this, histIncompetIstmoCervical, PROP_HIST_INCOMPET_ISTMO_CERVICAL); 
	}

	/**
	 * Set the value related to the column: hist_incompet_istmo_cervical
	 * @param histIncompetIstmoCervical the hist_incompet_istmo_cervical value
	 */
	public void setHistIncompetIstmoCervical (java.lang.Long histIncompetIstmoCervical) {
//        java.lang.Long histIncompetIstmoCervicalOld = this.histIncompetIstmoCervical;
		this.histIncompetIstmoCervical = histIncompetIstmoCervical;
//        this.getPropertyChangeSupport().firePropertyChange ("histIncompetIstmoCervical", histIncompetIstmoCervicalOld, histIncompetIstmoCervical);
	}



	/**
	 * Return the value associated with the column: hist_infeccao_urinaria
	 */
	public java.lang.Long getHistInfeccaoUrinaria () {
		return getPropertyValue(this, histInfeccaoUrinaria, PROP_HIST_INFECCAO_URINARIA); 
	}

	/**
	 * Set the value related to the column: hist_infeccao_urinaria
	 * @param histInfeccaoUrinaria the hist_infeccao_urinaria value
	 */
	public void setHistInfeccaoUrinaria (java.lang.Long histInfeccaoUrinaria) {
//        java.lang.Long histInfeccaoUrinariaOld = this.histInfeccaoUrinaria;
		this.histInfeccaoUrinaria = histInfeccaoUrinaria;
//        this.getPropertyChangeSupport().firePropertyChange ("histInfeccaoUrinaria", histInfeccaoUrinariaOld, histInfeccaoUrinaria);
	}



	/**
	 * Return the value associated with the column: hist_sifilis
	 */
	public java.lang.Long getHistSifilis () {
		return getPropertyValue(this, histSifilis, PROP_HIST_SIFILIS); 
	}

	/**
	 * Set the value related to the column: hist_sifilis
	 * @param histSifilis the hist_sifilis value
	 */
	public void setHistSifilis (java.lang.Long histSifilis) {
//        java.lang.Long histSifilisOld = this.histSifilis;
		this.histSifilis = histSifilis;
//        this.getPropertyChangeSupport().firePropertyChange ("histSifilis", histSifilisOld, histSifilis);
	}



	/**
	 * Return the value associated with the column: hist_transfusao
	 */
	public java.lang.Long getHistTransfusao () {
		return getPropertyValue(this, histTransfusao, PROP_HIST_TRANSFUSAO); 
	}

	/**
	 * Set the value related to the column: hist_transfusao
	 * @param histTransfusao the hist_transfusao value
	 */
	public void setHistTransfusao (java.lang.Long histTransfusao) {
//        java.lang.Long histTransfusaoOld = this.histTransfusao;
		this.histTransfusao = histTransfusao;
//        this.getPropertyChangeSupport().firePropertyChange ("histTransfusao", histTransfusaoOld, histTransfusao);
	}



	/**
	 * Return the value associated with the column: hist_hemorragia_1_semestre
	 */
	public java.lang.Long getHistHemorragia1Semestre () {
		return getPropertyValue(this, histHemorragia1Semestre, PROP_HIST_HEMORRAGIA1_SEMESTRE); 
	}

	/**
	 * Set the value related to the column: hist_hemorragia_1_semestre
	 * @param histHemorragia1Semestre the hist_hemorragia_1_semestre value
	 */
	public void setHistHemorragia1Semestre (java.lang.Long histHemorragia1Semestre) {
//        java.lang.Long histHemorragia1SemestreOld = this.histHemorragia1Semestre;
		this.histHemorragia1Semestre = histHemorragia1Semestre;
//        this.getPropertyChangeSupport().firePropertyChange ("histHemorragia1Semestre", histHemorragia1SemestreOld, histHemorragia1Semestre);
	}



	/**
	 * Return the value associated with the column: hist_hemorragia_2_semestre
	 */
	public java.lang.Long getHistHemorragia2Semestre () {
		return getPropertyValue(this, histHemorragia2Semestre, PROP_HIST_HEMORRAGIA2_SEMESTRE); 
	}

	/**
	 * Set the value related to the column: hist_hemorragia_2_semestre
	 * @param histHemorragia2Semestre the hist_hemorragia_2_semestre value
	 */
	public void setHistHemorragia2Semestre (java.lang.Long histHemorragia2Semestre) {
//        java.lang.Long histHemorragia2SemestreOld = this.histHemorragia2Semestre;
		this.histHemorragia2Semestre = histHemorragia2Semestre;
//        this.getPropertyChangeSupport().firePropertyChange ("histHemorragia2Semestre", histHemorragia2SemestreOld, histHemorragia2Semestre);
	}



	/**
	 * Return the value associated with the column: hist_hemorragia_3_semestre
	 */
	public java.lang.Long getHistHemorragia3Semestre () {
		return getPropertyValue(this, histHemorragia3Semestre, PROP_HIST_HEMORRAGIA3_SEMESTRE); 
	}

	/**
	 * Set the value related to the column: hist_hemorragia_3_semestre
	 * @param histHemorragia3Semestre the hist_hemorragia_3_semestre value
	 */
	public void setHistHemorragia3Semestre (java.lang.Long histHemorragia3Semestre) {
//        java.lang.Long histHemorragia3SemestreOld = this.histHemorragia3Semestre;
		this.histHemorragia3Semestre = histHemorragia3Semestre;
//        this.getPropertyChangeSupport().firePropertyChange ("histHemorragia3Semestre", histHemorragia3SemestreOld, histHemorragia3Semestre);
	}



	/**
	 * Return the value associated with the column: hist_diabetes_gestacional
	 */
	public java.lang.Long getHistDiabetesGestacional () {
		return getPropertyValue(this, histDiabetesGestacional, PROP_HIST_DIABETES_GESTACIONAL); 
	}

	/**
	 * Set the value related to the column: hist_diabetes_gestacional
	 * @param histDiabetesGestacional the hist_diabetes_gestacional value
	 */
	public void setHistDiabetesGestacional (java.lang.Long histDiabetesGestacional) {
//        java.lang.Long histDiabetesGestacionalOld = this.histDiabetesGestacional;
		this.histDiabetesGestacional = histDiabetesGestacional;
//        this.getPropertyChangeSupport().firePropertyChange ("histDiabetesGestacional", histDiabetesGestacionalOld, histDiabetesGestacional);
	}



	/**
	 * Return the value associated with the column: hist_hipertensao_arterial
	 */
	public java.lang.Long getHistHipertensaoArterial () {
		return getPropertyValue(this, histHipertensaoArterial, PROP_HIST_HIPERTENSAO_ARTERIAL); 
	}

	/**
	 * Set the value related to the column: hist_hipertensao_arterial
	 * @param histHipertensaoArterial the hist_hipertensao_arterial value
	 */
	public void setHistHipertensaoArterial (java.lang.Long histHipertensaoArterial) {
//        java.lang.Long histHipertensaoArterialOld = this.histHipertensaoArterial;
		this.histHipertensaoArterial = histHipertensaoArterial;
//        this.getPropertyChangeSupport().firePropertyChange ("histHipertensaoArterial", histHipertensaoArterialOld, histHipertensaoArterial);
	}



	/**
	 * Return the value associated with the column: hist_ciur
	 */
	public java.lang.Long getHistCiur () {
		return getPropertyValue(this, histCiur, PROP_HIST_CIUR); 
	}

	/**
	 * Set the value related to the column: hist_ciur
	 * @param histCiur the hist_ciur value
	 */
	public void setHistCiur (java.lang.Long histCiur) {
//        java.lang.Long histCiurOld = this.histCiur;
		this.histCiur = histCiur;
//        this.getPropertyChangeSupport().firePropertyChange ("histCiur", histCiurOld, histCiur);
	}



	/**
	 * Return the value associated with the column: hist_oligo_polidramnio
	 */
	public java.lang.Long getHistOligoPolidramnio () {
		return getPropertyValue(this, histOligoPolidramnio, PROP_HIST_OLIGO_POLIDRAMNIO); 
	}

	/**
	 * Set the value related to the column: hist_oligo_polidramnio
	 * @param histOligoPolidramnio the hist_oligo_polidramnio value
	 */
	public void setHistOligoPolidramnio (java.lang.Long histOligoPolidramnio) {
//        java.lang.Long histOligoPolidramnioOld = this.histOligoPolidramnio;
		this.histOligoPolidramnio = histOligoPolidramnio;
//        this.getPropertyChangeSupport().firePropertyChange ("histOligoPolidramnio", histOligoPolidramnioOld, histOligoPolidramnio);
	}



	/**
	 * Return the value associated with the column: hist_isoimunizacao
	 */
	public java.lang.Long getHistIsoimunizacao () {
		return getPropertyValue(this, histIsoimunizacao, PROP_HIST_ISOIMUNIZACAO); 
	}

	/**
	 * Set the value related to the column: hist_isoimunizacao
	 * @param histIsoimunizacao the hist_isoimunizacao value
	 */
	public void setHistIsoimunizacao (java.lang.Long histIsoimunizacao) {
//        java.lang.Long histIsoimunizacaoOld = this.histIsoimunizacao;
		this.histIsoimunizacao = histIsoimunizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("histIsoimunizacao", histIsoimunizacaoOld, histIsoimunizacao);
	}



	/**
	 * Return the value associated with the column: hist_anemia
	 */
	public java.lang.Long getHistAnemia () {
		return getPropertyValue(this, histAnemia, PROP_HIST_ANEMIA); 
	}

	/**
	 * Set the value related to the column: hist_anemia
	 * @param histAnemia the hist_anemia value
	 */
	public void setHistAnemia (java.lang.Long histAnemia) {
//        java.lang.Long histAnemiaOld = this.histAnemia;
		this.histAnemia = histAnemia;
//        this.getPropertyChangeSupport().firePropertyChange ("histAnemia", histAnemiaOld, histAnemia);
	}



	/**
	 * Return the value associated with the column: hist_toxoplasmose
	 */
	public java.lang.Long getHistToxoplasmose () {
		return getPropertyValue(this, histToxoplasmose, PROP_HIST_TOXOPLASMOSE); 
	}

	/**
	 * Set the value related to the column: hist_toxoplasmose
	 * @param histToxoplasmose the hist_toxoplasmose value
	 */
	public void setHistToxoplasmose (java.lang.Long histToxoplasmose) {
//        java.lang.Long histToxoplasmoseOld = this.histToxoplasmose;
		this.histToxoplasmose = histToxoplasmose;
//        this.getPropertyChangeSupport().firePropertyChange ("histToxoplasmose", histToxoplasmoseOld, histToxoplasmose);
	}



	/**
	 * Return the value associated with the column: hist_aids
	 */
	public java.lang.Long getHistAids () {
		return getPropertyValue(this, histAids, PROP_HIST_AIDS); 
	}

	/**
	 * Set the value related to the column: hist_aids
	 * @param histAids the hist_aids value
	 */
	public void setHistAids (java.lang.Long histAids) {
//        java.lang.Long histAidsOld = this.histAids;
		this.histAids = histAids;
//        this.getPropertyChangeSupport().firePropertyChange ("histAids", histAidsOld, histAids);
	}



	/**
	 * Return the value associated with the column: tp_parto
	 */
	public java.lang.Long getTipoParto () {
		return getPropertyValue(this, tipoParto, PROP_TIPO_PARTO); 
	}

	/**
	 * Set the value related to the column: tp_parto
	 * @param tipoParto the tp_parto value
	 */
	public void setTipoParto (java.lang.Long tipoParto) {
//        java.lang.Long tipoPartoOld = this.tipoParto;
		this.tipoParto = tipoParto;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoParto", tipoPartoOld, tipoParto);
	}



	/**
	 * Return the value associated with the column: idade_gestacional
	 */
	public java.lang.Long getIdadeGestacional () {
		return getPropertyValue(this, idadeGestacional, PROP_IDADE_GESTACIONAL); 
	}

	/**
	 * Set the value related to the column: idade_gestacional
	 * @param idadeGestacional the idade_gestacional value
	 */
	public void setIdadeGestacional (java.lang.Long idadeGestacional) {
//        java.lang.Long idadeGestacionalOld = this.idadeGestacional;
		this.idadeGestacional = idadeGestacional;
//        this.getPropertyChangeSupport().firePropertyChange ("idadeGestacional", idadeGestacionalOld, idadeGestacional);
	}



	/**
	 * Return the value associated with the column: peso
	 */
	public java.lang.Double getPeso () {
		return getPropertyValue(this, peso, PROP_PESO); 
	}

	/**
	 * Set the value related to the column: peso
	 * @param peso the peso value
	 */
	public void setPeso (java.lang.Double peso) {
//        java.lang.Double pesoOld = this.peso;
		this.peso = peso;
//        this.getPropertyChangeSupport().firePropertyChange ("peso", pesoOld, peso);
	}



	/**
	 * Return the value associated with the column: local_ocorrencia
	 */
	public java.lang.Long getLocalOcorrencia () {
		return getPropertyValue(this, localOcorrencia, PROP_LOCAL_OCORRENCIA); 
	}

	/**
	 * Set the value related to the column: local_ocorrencia
	 * @param localOcorrencia the local_ocorrencia value
	 */
	public void setLocalOcorrencia (java.lang.Long localOcorrencia) {
//        java.lang.Long localOcorrenciaOld = this.localOcorrencia;
		this.localOcorrencia = localOcorrencia;
//        this.getPropertyChangeSupport().firePropertyChange ("localOcorrencia", localOcorrenciaOld, localOcorrencia);
	}



	/**
	 * Return the value associated with the column: estabelecimento
	 */
	public java.lang.String getEstabelecimento () {
		return getPropertyValue(this, estabelecimento, PROP_ESTABELECIMENTO); 
	}

	/**
	 * Set the value related to the column: estabelecimento
	 * @param estabelecimento the estabelecimento value
	 */
	public void setEstabelecimento (java.lang.String estabelecimento) {
//        java.lang.String estabelecimentoOld = this.estabelecimento;
		this.estabelecimento = estabelecimento;
//        this.getPropertyChangeSupport().firePropertyChange ("estabelecimento", estabelecimentoOld, estabelecimento);
	}



	/**
	 * Return the value associated with the column: presenca_acompanhante
	 */
	public java.lang.Long getPresencaAcompanhante () {
		return getPropertyValue(this, presencaAcompanhante, PROP_PRESENCA_ACOMPANHANTE); 
	}

	/**
	 * Set the value related to the column: presenca_acompanhante
	 * @param presencaAcompanhante the presenca_acompanhante value
	 */
	public void setPresencaAcompanhante (java.lang.Long presencaAcompanhante) {
//        java.lang.Long presencaAcompanhanteOld = this.presencaAcompanhante;
		this.presencaAcompanhante = presencaAcompanhante;
//        this.getPropertyChangeSupport().firePropertyChange ("presencaAcompanhante", presencaAcompanhanteOld, presencaAcompanhante);
	}



	/**
	 * Return the value associated with the column: febre
	 */
	public java.lang.Long getFebre () {
		return getPropertyValue(this, febre, PROP_FEBRE); 
	}

	/**
	 * Set the value related to the column: febre
	 * @param febre the febre value
	 */
	public void setFebre (java.lang.Long febre) {
//        java.lang.Long febreOld = this.febre;
		this.febre = febre;
//        this.getPropertyChangeSupport().firePropertyChange ("febre", febreOld, febre);
	}



	/**
	 * Return the value associated with the column: infeccao
	 */
	public java.lang.Long getInfeccao () {
		return getPropertyValue(this, infeccao, PROP_INFECCAO); 
	}

	/**
	 * Set the value related to the column: infeccao
	 * @param infeccao the infeccao value
	 */
	public void setInfeccao (java.lang.Long infeccao) {
//        java.lang.Long infeccaoOld = this.infeccao;
		this.infeccao = infeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("infeccao", infeccaoOld, infeccao);
	}



	/**
	 * Return the value associated with the column: hemorragia
	 */
	public java.lang.Long getHemorragia () {
		return getPropertyValue(this, hemorragia, PROP_HEMORRAGIA); 
	}

	/**
	 * Set the value related to the column: hemorragia
	 * @param hemorragia the hemorragia value
	 */
	public void setHemorragia (java.lang.Long hemorragia) {
//        java.lang.Long hemorragiaOld = this.hemorragia;
		this.hemorragia = hemorragia;
//        this.getPropertyChangeSupport().firePropertyChange ("hemorragia", hemorragiaOld, hemorragia);
	}



	/**
	 * Return the value associated with the column: problema_mamas
	 */
	public java.lang.Long getProblemaMamas () {
		return getPropertyValue(this, problemaMamas, PROP_PROBLEMA_MAMAS); 
	}

	/**
	 * Set the value related to the column: problema_mamas
	 * @param problemaMamas the problema_mamas value
	 */
	public void setProblemaMamas (java.lang.Long problemaMamas) {
//        java.lang.Long problemaMamasOld = this.problemaMamas;
		this.problemaMamas = problemaMamas;
//        this.getPropertyChangeSupport().firePropertyChange ("problemaMamas", problemaMamasOld, problemaMamas);
	}



	/**
	 * Return the value associated with the column: dt_ocorrencia
	 */
	public java.util.Date getDataOcorrencia () {
		return getPropertyValue(this, dataOcorrencia, PROP_DATA_OCORRENCIA); 
	}

	/**
	 * Set the value related to the column: dt_ocorrencia
	 * @param dataOcorrencia the dt_ocorrencia value
	 */
	public void setDataOcorrencia (java.util.Date dataOcorrencia) {
//        java.util.Date dataOcorrenciaOld = this.dataOcorrencia;
		this.dataOcorrencia = dataOcorrencia;
//        this.getPropertyChangeSupport().firePropertyChange ("dataOcorrencia", dataOcorrenciaOld, dataOcorrencia);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cd_escolaridade
	 */
	public br.com.ksisolucoes.vo.cadsus.Escolaridade getEscolaridade () {
		return getPropertyValue(this, escolaridade, PROP_ESCOLARIDADE); 
	}

	/**
	 * Set the value related to the column: cd_escolaridade
	 * @param escolaridade the cd_escolaridade value
	 */
	public void setEscolaridade (br.com.ksisolucoes.vo.cadsus.Escolaridade escolaridade) {
//        br.com.ksisolucoes.vo.cadsus.Escolaridade escolaridadeOld = this.escolaridade;
		this.escolaridade = escolaridade;
//        this.getPropertyChangeSupport().firePropertyChange ("escolaridade", escolaridadeOld, escolaridade);
	}



	/**
	 * Return the value associated with the column: cd_estado_civil
	 */
	public br.com.ksisolucoes.vo.cadsus.EstadoCivil getEstadoCivil () {
		return getPropertyValue(this, estadoCivil, PROP_ESTADO_CIVIL); 
	}

	/**
	 * Set the value related to the column: cd_estado_civil
	 * @param estadoCivil the cd_estado_civil value
	 */
	public void setEstadoCivil (br.com.ksisolucoes.vo.cadsus.EstadoCivil estadoCivil) {
//        br.com.ksisolucoes.vo.cadsus.EstadoCivil estadoCivilOld = this.estadoCivil;
		this.estadoCivil = estadoCivil;
//        this.getPropertyChangeSupport().firePropertyChange ("estadoCivil", estadoCivilOld, estadoCivil);
	}



	/**
	 * Return the value associated with the column: cd_raca
	 */
	public br.com.ksisolucoes.vo.cadsus.Raca getRaca () {
		return getPropertyValue(this, raca, PROP_RACA); 
	}

	/**
	 * Set the value related to the column: cd_raca
	 * @param raca the cd_raca value
	 */
	public void setRaca (br.com.ksisolucoes.vo.cadsus.Raca raca) {
//        br.com.ksisolucoes.vo.cadsus.Raca racaOld = this.raca;
		this.raca = raca;
//        this.getPropertyChangeSupport().firePropertyChange ("raca", racaOld, raca);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_usu_fechamento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioFechamento () {
		return getPropertyValue(this, usuarioFechamento, PROP_USUARIO_FECHAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usu_fechamento
	 * @param usuarioFechamento the cd_usu_fechamento value
	 */
	public void setUsuarioFechamento (br.com.ksisolucoes.vo.controle.Usuario usuarioFechamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioFechamentoOld = this.usuarioFechamento;
		this.usuarioFechamento = usuarioFechamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioFechamento", usuarioFechamentoOld, usuarioFechamento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.PreNatal)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.PreNatal preNatal = (br.com.ksisolucoes.vo.prontuario.basico.PreNatal) obj;
			if (null == this.getCodigo() || null == preNatal.getCodigo()) return false;
			else return (this.getCodigo().equals(preNatal.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}