package br.com.ksisolucoes.vo.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the producao_ipe_processo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="producao_ipe_processo"
 */

public abstract class BaseProducaoIpeProcesso extends BaseRootVO implements Serializable {

	public static String REF = "ProducaoIpeProcesso";
	public static final String PROP_STATUS = "status";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_MENSAGEM_VALIDACAO = "mensagemValidacao";
	public static final String PROP_TEXTO = "texto";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_USUARIO_GERACAO = "usuarioGeracao";
	public static final String PROP_CAMINHO_ARQUIVO = "caminhoArquivo";
	public static final String PROP_ASYNC_PROCESS = "asyncProcess";
	public static final String PROP_DATA_GERACAO = "dataGeracao";


	// constructors
	public BaseProducaoIpeProcesso () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseProducaoIpeProcesso (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseProducaoIpeProcesso (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuarioGeracao,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		java.util.Date dataGeracao,
		java.lang.Long status) {

		this.setCodigo(codigo);
		this.setUsuarioGeracao(usuarioGeracao);
		this.setUsuarioCadsus(usuarioCadsus);
		this.setDataGeracao(dataGeracao);
		this.setStatus(status);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataGeracao;
	private java.lang.Long status;
	private java.lang.String texto;
	private java.lang.String mensagemValidacao;
	private java.lang.String caminhoArquivo;

	// many to one
	private br.com.ksisolucoes.vo.service.AsyncProcess asyncProcess;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioGeracao;
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_producao_ipe_processo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: data_geracao
	 */
	public java.util.Date getDataGeracao () {
		return getPropertyValue(this, dataGeracao, PROP_DATA_GERACAO); 
	}

	/**
	 * Set the value related to the column: data_geracao
	 * @param dataGeracao the data_geracao value
	 */
	public void setDataGeracao (java.util.Date dataGeracao) {
//        java.util.Date dataGeracaoOld = this.dataGeracao;
		this.dataGeracao = dataGeracao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataGeracao", dataGeracaoOld, dataGeracao);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: texto
	 */
	public java.lang.String getTexto () {
		return getPropertyValue(this, texto, PROP_TEXTO); 
	}

	/**
	 * Set the value related to the column: texto
	 * @param texto the texto value
	 */
	public void setTexto (java.lang.String texto) {
//        java.lang.String textoOld = this.texto;
		this.texto = texto;
//        this.getPropertyChangeSupport().firePropertyChange ("texto", textoOld, texto);
	}



	/**
	 * Return the value associated with the column: msg_validacao
	 */
	public java.lang.String getMensagemValidacao () {
		return getPropertyValue(this, mensagemValidacao, PROP_MENSAGEM_VALIDACAO); 
	}

	/**
	 * Set the value related to the column: msg_validacao
	 * @param mensagemValidacao the msg_validacao value
	 */
	public void setMensagemValidacao (java.lang.String mensagemValidacao) {
//        java.lang.String mensagemValidacaoOld = this.mensagemValidacao;
		this.mensagemValidacao = mensagemValidacao;
//        this.getPropertyChangeSupport().firePropertyChange ("mensagemValidacao", mensagemValidacaoOld, mensagemValidacao);
	}



	/**
	 * Return the value associated with the column: caminho_arquivo
	 */
	public java.lang.String getCaminhoArquivo () {
		return getPropertyValue(this, caminhoArquivo, PROP_CAMINHO_ARQUIVO); 
	}

	/**
	 * Set the value related to the column: caminho_arquivo
	 * @param caminhoArquivo the caminho_arquivo value
	 */
	public void setCaminhoArquivo (java.lang.String caminhoArquivo) {
//        java.lang.String caminhoArquivoOld = this.caminhoArquivo;
		this.caminhoArquivo = caminhoArquivo;
//        this.getPropertyChangeSupport().firePropertyChange ("caminhoArquivo", caminhoArquivoOld, caminhoArquivo);
	}



	/**
	 * Return the value associated with the column: cd_process
	 */
	public br.com.ksisolucoes.vo.service.AsyncProcess getAsyncProcess () {
		return getPropertyValue(this, asyncProcess, PROP_ASYNC_PROCESS); 
	}

	/**
	 * Set the value related to the column: cd_process
	 * @param asyncProcess the cd_process value
	 */
	public void setAsyncProcess (br.com.ksisolucoes.vo.service.AsyncProcess asyncProcess) {
//        br.com.ksisolucoes.vo.service.AsyncProcess asyncProcessOld = this.asyncProcess;
		this.asyncProcess = asyncProcess;
//        this.getPropertyChangeSupport().firePropertyChange ("asyncProcess", asyncProcessOld, asyncProcess);
	}



	/**
	 * Return the value associated with the column: cd_usuario_geracao
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioGeracao () {
		return getPropertyValue(this, usuarioGeracao, PROP_USUARIO_GERACAO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_geracao
	 * @param usuarioGeracao the cd_usuario_geracao value
	 */
	public void setUsuarioGeracao (br.com.ksisolucoes.vo.controle.Usuario usuarioGeracao) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioGeracaoOld = this.usuarioGeracao;
		this.usuarioGeracao = usuarioGeracao;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioGeracao", usuarioGeracaoOld, usuarioGeracao);
	}



	/**
	 * Return the value associated with the column: cd_usuario_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usuario_cadsus
	 * @param usuarioCadsus the cd_usuario_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.ProducaoIpeProcesso)) return false;
		else {
			br.com.ksisolucoes.vo.basico.ProducaoIpeProcesso producaoIpeProcesso = (br.com.ksisolucoes.vo.basico.ProducaoIpeProcesso) obj;
			if (null == this.getCodigo() || null == producaoIpeProcesso.getCodigo()) return false;
			else return (this.getCodigo().equals(producaoIpeProcesso.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}