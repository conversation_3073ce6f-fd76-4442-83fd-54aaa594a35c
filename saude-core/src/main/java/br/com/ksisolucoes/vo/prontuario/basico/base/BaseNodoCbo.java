package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the nodo_cbo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="nodo_cbo"
 */

public abstract class BaseNodoCbo extends BaseRootVO implements Serializable {

	public static String REF = "NodoCbo";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_CBO = "cbo";
	public static final String PROP_CLASSE_NODO = "classeNodo";


	// constructors
	public BaseNodoCbo () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseNodoCbo (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String classeNodo;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo cbo;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="codigo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: classe_nodo
	 */
	public java.lang.String getClasseNodo () {
		return getPropertyValue(this, classeNodo, PROP_CLASSE_NODO); 
	}

	/**
	 * Set the value related to the column: classe_nodo
	 * @param classeNodo the classe_nodo value
	 */
	public void setClasseNodo (java.lang.String classeNodo) {
//        java.lang.String classeNodoOld = this.classeNodo;
		this.classeNodo = classeNodo;
//        this.getPropertyChangeSupport().firePropertyChange ("classeNodo", classeNodoOld, classeNodo);
	}



	/**
	 * Return the value associated with the column: cd_cbo
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getCbo () {
		return getPropertyValue(this, cbo, PROP_CBO); 
	}

	/**
	 * Set the value related to the column: cd_cbo
	 * @param cbo the cd_cbo value
	 */
	public void setCbo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo cbo) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo cboOld = this.cbo;
		this.cbo = cbo;
//        this.getPropertyChangeSupport().firePropertyChange ("cbo", cboOld, cbo);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.NodoCbo)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.NodoCbo nodoCbo = (br.com.ksisolucoes.vo.prontuario.basico.NodoCbo) obj;
			if (null == this.getCodigo() || null == nodoCbo.getCodigo()) return false;
			else return (this.getCodigo().equals(nodoCbo.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}