<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="Cid" table="cid" >
        <id
            column="cd_cid"
            name="codigo"
            type="string"
            >
            <meta attribute="property-type">java.lang.String</meta>		
            <generator class="assigned" />
        </id> <version column="version" name="version" type="long" />    
        
        <property
            column="nm_cid"
            length="60"
            name="descricao"
            not-null="true"
            type="string"
        />
        <property
            column="tp_agravo"
            name="tipoAgravo"
            not-null="true"
            type="java.lang.Long"
        />
        <property
            column="tp_sexo"
            name="tipoSexo"
            not-null="true"
            type="java.lang.String"
        />

        <many-to-one
            name="cidClassificacao"
            class="br.com.ksisolucoes.vo.basico.ClassificacaoCids"
            not-null="false"
        >
            <column name="cd_classificacao"/>
        </many-to-one>
        
		<property
            column="flag_cid_categoria"
            name="flagCidCategoria"
            not-null="true"
            type="java.lang.String"
        />

        <property
                name="ativo"
                column="ativo"
                type="java.lang.Long"
                not-null="false"
        />

        <property
                name="flagRegistroDiarreia"
                column="flag_registro_diarreia"
                type="java.lang.Long"
                not-null="false"
        />


    </class>
</hibernate-mapping>
