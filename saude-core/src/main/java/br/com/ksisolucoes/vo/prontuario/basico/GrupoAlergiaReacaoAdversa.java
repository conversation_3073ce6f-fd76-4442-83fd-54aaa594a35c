package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseGrupoAlergiaReacaoAdversa;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class GrupoAlergiaReacaoAdversa extends BaseGrupoAlergiaReacaoAdversa implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public GrupoAlergiaReacaoAdversa () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public GrupoAlergiaReacaoAdversa (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public GrupoAlergiaReacaoAdversa (
		java.lang.Long codigo,
		java.lang.Long criticidade) {

		super (
			codigo,
			criticidade);
	}

	public enum Criticidade implements IEnum {
		ALTA(1L, Bundle.getStringApplication("rotulo_alta")),
		BAIXA(2L, Bundle.getStringApplication("rotulo_baixa"));

		private Long value;
		private String descricao;

		private Criticidade(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static GrupoAlergiaReacaoAdversa.Criticidade valeuOf(Long value) {
			for (GrupoAlergiaReacaoAdversa.Criticidade criticidade : GrupoAlergiaReacaoAdversa.Criticidade.values()) {
				if (criticidade.value().equals(value)) {
					return criticidade;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}

	}

	public String getCriticidadeFormatada() {
		if (Criticidade.ALTA.value().equals(getCriticidade())) {
			return Bundle.getStringApplication("rotulo_alta");
		} else if (Criticidade.BAIXA.value().equals(getCriticidade())) {
			return Bundle.getStringApplication("rotulo_baixa");
		}
		return "";
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}