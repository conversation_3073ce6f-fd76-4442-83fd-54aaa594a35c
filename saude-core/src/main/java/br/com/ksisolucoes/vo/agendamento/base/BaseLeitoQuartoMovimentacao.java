package br.com.ksisolucoes.vo.agendamento.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the leito_quarto_movimentacao table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="leito_quarto_movimentacao"
 */

public abstract class BaseLeitoQuartoMovimentacao extends BaseRootVO implements Serializable {

	public static String REF = "LeitoQuartoMovimentacao";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_DATA_MOVIMENTACAO = "dataMovimentacao";
	public static final String PROP_LEITO_QUARTO = "leitoQuarto";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_SITUACAO = "situacao";


	public BaseLeitoQuartoMovimentacao () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseLeitoQuartoMovimentacao (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseLeitoQuartoMovimentacao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto leitoQuarto,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataMovimentacao,
		java.lang.Long situacao) {

		this.setCodigo(codigo);
		this.setLeitoQuarto(leitoQuarto);
		this.setUsuario(usuario);
		this.setDataMovimentacao(dataMovimentacao);
		this.setSituacao(situacao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	private java.lang.Long codigo;

	private java.util.Date dataMovimentacao;
	private java.lang.Long situacao;

	private br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto leitoQuarto;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_leito_movimentacao"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_movimentacao
	 */
	public java.util.Date getDataMovimentacao () {
		return getPropertyValue(this, dataMovimentacao, PROP_DATA_MOVIMENTACAO); 
	}

	/**
	 * Set the value related to the column: dt_movimentacao
	 * @param dataMovimentacao the dt_movimentacao value
	 */
	public void setDataMovimentacao (java.util.Date dataMovimentacao) {
		this.dataMovimentacao = dataMovimentacao;
	}



	/**
	 * Return the value associated with the column: situacao
	 */
	public java.lang.Long getSituacao () {
		return getPropertyValue(this, situacao, PROP_SITUACAO); 
	}

	/**
	 * Set the value related to the column: situacao
	 * @param situacao the situacao value
	 */
	public void setSituacao (java.lang.Long situacao) {
		this.situacao = situacao;
	}



	/**
	 * Return the value associated with the column: cd_leito
	 */
	public br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto getLeitoQuarto () {
		return getPropertyValue(this, leitoQuarto, PROP_LEITO_QUARTO); 
	}

	/**
	 * Set the value related to the column: cd_leito
	 * @param leitoQuarto the cd_leito value
	 */
	public void setLeitoQuarto (br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto leitoQuarto) {
		this.leitoQuarto = leitoQuarto;
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
		this.usuario = usuario;
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.agendamento.LeitoQuartoMovimentacao)) return false;
		else {
			br.com.ksisolucoes.vo.agendamento.LeitoQuartoMovimentacao leitoQuartoMovimentacao = (br.com.ksisolucoes.vo.agendamento.LeitoQuartoMovimentacao) obj;
			if (null == this.getCodigo() || null == leitoQuartoMovimentacao.getCodigo()) return false;
			else return (this.getCodigo().equals(leitoQuartoMovimentacao.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

}