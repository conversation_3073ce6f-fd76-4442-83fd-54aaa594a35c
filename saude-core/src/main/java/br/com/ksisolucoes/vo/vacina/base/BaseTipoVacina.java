package br.com.ksisolucoes.vo.vacina.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the tipo_vacina table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="tipo_vacina"
 */

public abstract class BaseTipoVacina extends BaseRootVO implements Serializable {

	public static String REF = "TipoVacina";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_VERSION_ALL = "versionAll";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TEMPO_ABERTA = "tempoAberta";
	public static final String PROP_SUB_GRUPO = "subGrupo";
	public static final String PROP_FLAG_VACINA_NAO_EXISTE_CALENDARIO = "flagVacinaNaoExisteCalendario";
	public static final String PROP_TIPO_ESUS = "tipoEsus";
	public static final String PROP_VALIDADE_ABERTA = "validadeAberta";
	public static final String PROP_VIA_ADMINISTRACAO = "viaAdministracao";
	public static final String PROP_MENSAGEM_ALERTA = "mensagemAlerta";
	public static final String PROP_FLAG_INSERIDO_CELK  = "flagInseridoCelk";

	// constructors
	public BaseTipoVacina () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseTipoVacina (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseTipoVacina (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.entradas.estoque.SubGrupo subGrupo,
		java.lang.String descricao,
		java.lang.Long tempoAberta,
		java.lang.String flagVacinaNaoExisteCalendario) {

		this.setCodigo(codigo);
		this.setSubGrupo(subGrupo);
		this.setDescricao(descricao);
		this.setTempoAberta(tempoAberta);
		this.setFlagVacinaNaoExisteCalendario(flagVacinaNaoExisteCalendario);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.lang.Long validadeAberta;
	private java.lang.Long versionAll;
	private java.lang.Long tempoAberta;
	private java.lang.String mensagemAlerta;
	private java.lang.Long tipoEsus;
	private java.lang.String flagVacinaNaoExisteCalendario;
	private java.lang.Long flagInseridoCelk;

	// many to one
	private br.com.ksisolucoes.vo.entradas.estoque.SubGrupo subGrupo;
	private br.com.ksisolucoes.vo.vacina.ViaAdministracao viaAdministracao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_vacina"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_vacina
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_vacina
	 * @param descricao the ds_vacina value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: validade_aberta
	 */
	public java.lang.Long getValidadeAberta () {
		return getPropertyValue(this, validadeAberta, PROP_VALIDADE_ABERTA); 
	}

	/**
	 * Set the value related to the column: validade_aberta
	 * @param validadeAberta the validade_aberta value
	 */
	public void setValidadeAberta (java.lang.Long validadeAberta) {
//        java.lang.Long validadeAbertaOld = this.validadeAberta;
		this.validadeAberta = validadeAberta;
//        this.getPropertyChangeSupport().firePropertyChange ("validadeAberta", validadeAbertaOld, validadeAberta);
	}



	/**
	 * Return the value associated with the column: version_all
	 */
	public java.lang.Long getVersionAll () {
		return getPropertyValue(this, versionAll, PROP_VERSION_ALL); 
	}

	/**
	 * Set the value related to the column: version_all
	 * @param versionAll the version_all value
	 */
	public void setVersionAll (java.lang.Long versionAll) {
//        java.lang.Long versionAllOld = this.versionAll;
		this.versionAll = versionAll;
//        this.getPropertyChangeSupport().firePropertyChange ("versionAll", versionAllOld, versionAll);
	}



	/**
	 * Return the value associated with the column: tempo_aberta
	 */
	public java.lang.Long getTempoAberta () {
		return getPropertyValue(this, tempoAberta, PROP_TEMPO_ABERTA); 
	}

	/**
	 * Set the value related to the column: tempo_aberta
	 * @param tempoAberta the tempo_aberta value
	 */
	public void setTempoAberta (java.lang.Long tempoAberta) {
//        java.lang.Long tempoAbertaOld = this.tempoAberta;
		this.tempoAberta = tempoAberta;
//        this.getPropertyChangeSupport().firePropertyChange ("tempoAberta", tempoAbertaOld, tempoAberta);
	}



	/**
	 * Return the value associated with the column: mensagem_alerta
	 */
	public java.lang.String getMensagemAlerta () {
		return getPropertyValue(this, mensagemAlerta, PROP_MENSAGEM_ALERTA); 
	}

	/**
	 * Set the value related to the column: mensagem_alerta
	 * @param mensagemAlerta the mensagem_alerta value
	 */
	public void setMensagemAlerta (java.lang.String mensagemAlerta) {
//        java.lang.String mensagemAlertaOld = this.mensagemAlerta;
		this.mensagemAlerta = mensagemAlerta;
//        this.getPropertyChangeSupport().firePropertyChange ("mensagemAlerta", mensagemAlertaOld, mensagemAlerta);
	}

	public  java.lang.Long getFlagInseridoCelk() {
		return getPropertyValue(this, flagInseridoCelk, PROP_FLAG_INSERIDO_CELK);
	}

	public void setFlagInseridoCelk(java.lang.Long flagInseridoCelk) {
		this.flagInseridoCelk = flagInseridoCelk;
	}

	/**
	 * Return the value associated with the column: tipo_esus
	 */
	public java.lang.Long getTipoEsus () {
		return getPropertyValue(this, tipoEsus, PROP_TIPO_ESUS); 
	}

	/**
	 * Set the value related to the column: tipo_esus
	 * @param tipoEsus the tipo_esus value
	 */
	public void setTipoEsus (java.lang.Long tipoEsus) {
//        java.lang.Long tipoEsusOld = this.tipoEsus;
		this.tipoEsus = tipoEsus;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoEsus", tipoEsusOld, tipoEsus);
	}



	/**
	 * Return the value associated with the column: vacina_nao_existe_calendario
	 */
	public java.lang.String getFlagVacinaNaoExisteCalendario () {
		return getPropertyValue(this, flagVacinaNaoExisteCalendario, PROP_FLAG_VACINA_NAO_EXISTE_CALENDARIO); 
	}

	/**
	 * Set the value related to the column: vacina_nao_existe_calendario
	 * @param flagVacinaNaoExisteCalendario the vacina_nao_existe_calendario value
	 */
	public void setFlagVacinaNaoExisteCalendario (java.lang.String flagVacinaNaoExisteCalendario) {
//        java.lang.String flagVacinaNaoExisteCalendarioOld = this.flagVacinaNaoExisteCalendario;
		this.flagVacinaNaoExisteCalendario = flagVacinaNaoExisteCalendario;
//        this.getPropertyChangeSupport().firePropertyChange ("flagVacinaNaoExisteCalendario", flagVacinaNaoExisteCalendarioOld, flagVacinaNaoExisteCalendario);
	}



	/**
	 * Return the value associated with the column: cod_gru
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.SubGrupo getSubGrupo () {
		return getPropertyValue(this, subGrupo, PROP_SUB_GRUPO); 
	}

	/**
	 * Set the value related to the column: cod_gru
	 * @param subGrupo the cod_gru value
	 */
	public void setSubGrupo (br.com.ksisolucoes.vo.entradas.estoque.SubGrupo subGrupo) {
//        br.com.ksisolucoes.vo.entradas.estoque.SubGrupo subGrupoOld = this.subGrupo;
		this.subGrupo = subGrupo;
//        this.getPropertyChangeSupport().firePropertyChange ("subGrupo", subGrupoOld, subGrupo);
	}



	/**
	 * Return the value associated with the column: cd_via_administracao
	 */
	public br.com.ksisolucoes.vo.vacina.ViaAdministracao getViaAdministracao () {
		return getPropertyValue(this, viaAdministracao, PROP_VIA_ADMINISTRACAO); 
	}

	/**
	 * Set the value related to the column: cd_via_administracao
	 * @param viaAdministracao the cd_via_administracao value
	 */
	public void setViaAdministracao (br.com.ksisolucoes.vo.vacina.ViaAdministracao viaAdministracao) {
//        br.com.ksisolucoes.vo.vacina.ViaAdministracao viaAdministracaoOld = this.viaAdministracao;
		this.viaAdministracao = viaAdministracao;
//        this.getPropertyChangeSupport().firePropertyChange ("viaAdministracao", viaAdministracaoOld, viaAdministracao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vacina.TipoVacina)) return false;
		else {
			br.com.ksisolucoes.vo.vacina.TipoVacina tipoVacina = (br.com.ksisolucoes.vo.vacina.TipoVacina) obj;
			if (null == this.getCodigo() || null == tipoVacina.getCodigo()) return false;
			else return (this.getCodigo().equals(tipoVacina.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}