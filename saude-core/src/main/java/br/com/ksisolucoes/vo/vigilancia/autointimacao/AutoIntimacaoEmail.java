package br.com.ksisolucoes.vo.vigilancia.autointimacao;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.base.BaseAutoIntimacaoEmail;

import java.io.Serializable;


public class AutoIntimacaoEmail extends BaseAutoIntimacaoEmail implements CodigoManager {
    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public AutoIntimacaoEmail() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public AutoIntimacaoEmail(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public AutoIntimacaoEmail(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao autoIntimacao,
            java.lang.String email) {

        super(
                codigo,
                autoIntimacao,
                email);
    }

    /*[CONSTRUCTOR MARKER END]*/

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }
}