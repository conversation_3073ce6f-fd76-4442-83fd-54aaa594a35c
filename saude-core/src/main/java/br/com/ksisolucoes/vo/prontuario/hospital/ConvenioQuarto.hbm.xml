<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.hospital"  >
    <class name="ConvenioQuarto" table="convenio_quarto" >
        <id
            column="cd_convenio_quarto"
            name="codigo"
            type="java.lang.Long" 
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />    
        
        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.Convenio"
                     name="convenio" not-null="true">
            <column name="cd_convenio" />
        </many-to-one>
        
        <many-to-one class="br.com.ksisolucoes.vo.prontuario.hospital.QuartoInternacao"
                     name="quartoInternacao" not-null="true">
            <column name="cd_quarto_internacao" />
        </many-to-one>
        
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
            name="procedimento"
            not-null="false"
        >
            <column name="cd_procedimento"/>
        </many-to-one>
    </class>
</hibernate-mapping>
