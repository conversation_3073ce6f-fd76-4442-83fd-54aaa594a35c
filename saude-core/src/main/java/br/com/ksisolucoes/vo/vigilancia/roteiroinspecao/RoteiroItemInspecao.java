package br.com.ksisolucoes.vo.vigilancia.roteiroinspecao;

import java.io.Serializable;

import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.base.BaseRoteiroItemInspecao;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

public class RoteiroItemInspecao extends BaseRoteiroItemInspecao implements CodigoManager {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public RoteiroItemInspecao() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public RoteiroItemInspecao(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public RoteiroItemInspecao(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RoteiroInspecao roteiroInspecao,
            br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.ItemInspecao itemInspecao) {

        super(
                codigo,
                roteiroInspecao,
                itemInspecao);
    }

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}
