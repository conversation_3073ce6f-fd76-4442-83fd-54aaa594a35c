package br.com.ksisolucoes.vo.esus.dto;

import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo;
import br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio;
import br.com.ksisolucoes.vo.cadsus.EnderecoDomicilioEsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.VisitaDomiciliar;
import br.com.ksisolucoes.vo.cadsus.cds.*;
import br.com.ksisolucoes.vo.esus.EsusTransporte;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoItem;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaEsus;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class EsusFichaDTOParam implements Serializable {

    private EsusTransporte esusTransporte;
    private Atendimento atendimento;
    private AtendimentoItem atendimentoItem;
    private UsuarioCadsus usuarioCadsus;
    private EnderecoDomicilioEsus enderecoDomicilioEsus;
    private EnderecoDomicilio enderecoDomicilio;
    private VisitaDomiciliar visitaDomiciliar;
    private String uuid;
    private EsusFichaAtendIndividualItem esusFichaAtendIndividualItem;
    private ItemContaEsus itemContaEsus;
    private AtividadeGrupo atividadeGrupo;
    private EsusFichaOdontoItem esusFichaOdontoItem;
    private EsusFichaProcedimentoItem esusFichaProcedimentoItem;
    private EsusFichaAtendDomiciliar esusFichaAtendDomiciliar;
    private EsusFichaMarcadoresConsumoAlimentar esusFichaMarcadoresConsumoAlimentar;
    private EsusFichaAvaliacaoElegebilidadeAdmissao esusFichaAvaliacaoElegebilidadeAdmissao;

    public EsusFichaAvaliacaoElegebilidadeAdmissao getEsusFichaAvaliacaoElegebilidadeAdmissao() {
        return esusFichaAvaliacaoElegebilidadeAdmissao;
    }

    public void setEsusFichaAvaliacaoElegebilidadeAdmissao(EsusFichaAvaliacaoElegebilidadeAdmissao esusFichaAvaliacaoElegebilidadeAdmissao) {
        this.esusFichaAvaliacaoElegebilidadeAdmissao = esusFichaAvaliacaoElegebilidadeAdmissao;
    }

    public EsusFichaMarcadoresConsumoAlimentar getEsusFichaMarcadoresConsumoAlimentar() {
        return esusFichaMarcadoresConsumoAlimentar;
    }

    public void setEsusFichaMarcadoresConsumoAlimentar(EsusFichaMarcadoresConsumoAlimentar esusFichaMarcadoresConsumoAlimentar) {
        this.esusFichaMarcadoresConsumoAlimentar = esusFichaMarcadoresConsumoAlimentar;
    }

    public EsusFichaAtendDomiciliar getEsusFichaAtendDomiciliar() {
        return esusFichaAtendDomiciliar;
    }

    public void setEsusFichaAtendDomiciliar(EsusFichaAtendDomiciliar esusFichaAtendDomiciliar) {
        this.esusFichaAtendDomiciliar = esusFichaAtendDomiciliar;
    }

    public EsusFichaProcedimentoItem getEsusFichaProcedimentoItem() {
        return esusFichaProcedimentoItem;
    }

    public void setEsusFichaProcedimentoItem(EsusFichaProcedimentoItem esusFichaProcedimentoItem) {
        this.esusFichaProcedimentoItem = esusFichaProcedimentoItem;
    }

    public EsusFichaOdontoItem getEsusFichaOdontoItem() {
        return esusFichaOdontoItem;
    }

    public void setEsusFichaOdontoItem(EsusFichaOdontoItem esusFichaOdontoItem) {
        this.esusFichaOdontoItem = esusFichaOdontoItem;
    }

    public AtividadeGrupo getAtividadeGrupo() {
        return atividadeGrupo;
    }

    public void setAtividadeGrupo(AtividadeGrupo atividadeGrupo) {
        this.atividadeGrupo = atividadeGrupo;
    }

    public ItemContaEsus getItemContaEsus() {
        return itemContaEsus;
    }

    public void setItemContaEsus(ItemContaEsus itemContaEsus) {
        this.itemContaEsus = itemContaEsus;
    }

    public EsusFichaAtendIndividualItem getEsusFichaAtendIndividualItem() {
        return esusFichaAtendIndividualItem;
    }

    public void setEsusFichaAtendIndividualItem(EsusFichaAtendIndividualItem esusFichaAtendIndividualItem) {
        this.esusFichaAtendIndividualItem = esusFichaAtendIndividualItem;
    }

    public VisitaDomiciliar getVisitaDomiciliar() {
        return visitaDomiciliar;
    }

    public void setVisitaDomiciliar(VisitaDomiciliar visitaDomiciliar) {
        this.visitaDomiciliar = visitaDomiciliar;
    }

    public EnderecoDomicilioEsus getEnderecoDomicilioEsus() {
        return enderecoDomicilioEsus;
    }

    public void setEnderecoDomicilioEsus(EnderecoDomicilioEsus enderecoDomicilioEsus) {
        this.enderecoDomicilioEsus = enderecoDomicilioEsus;
    }

    public EsusTransporte getEsusTransporte() {
        return esusTransporte;
    }

    public void setEsusTransporte(EsusTransporte esusTransporte) {
        this.esusTransporte = esusTransporte;
    }

    public Atendimento getAtendimento() {
        return atendimento;
    }

    public void setAtendimento(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    public AtendimentoItem getAtendimentoItem() {
        return atendimentoItem;
    }

    public void setAtendimentoItem(AtendimentoItem atendimentoItem) {
        this.atendimentoItem = atendimentoItem;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public EnderecoDomicilio getEnderecoDomicilio() {
        return enderecoDomicilio;
    }

    public void setEnderecoDomicilio(EnderecoDomicilio enderecoDomicilio) {
        this.enderecoDomicilio = enderecoDomicilio;
    }
}
