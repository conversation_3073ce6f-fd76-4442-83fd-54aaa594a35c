package br.com.ksisolucoes.vo.cadsus.cds;

import br.com.ksisolucoes.vo.cadsus.cds.base.BaseEsusFichaEnderecoDomicilioEsus;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;



public class EsusFichaEnderecoDomicilioEsus extends BaseEsusFichaEnderecoDomicilioEsus implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EsusFichaEnderecoDomicilioEsus () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public EsusFichaEnderecoDomicilioEsus (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public EsusFichaEnderecoDomicilioEsus (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.EnderecoDomicilioEsus enderecoDomicilioEsus,
		java.util.Date dataCadastro) {

		super (
			codigo,
			enderecoDomicilioEsus,
			dataCadastro);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}