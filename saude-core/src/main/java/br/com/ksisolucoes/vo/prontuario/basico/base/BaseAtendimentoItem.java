package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the atendimento_item table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="atendimento_item"
 */

public abstract class BaseAtendimentoItem extends BaseRootVO implements Serializable {

	public static String REF = "AtendimentoItem";
	public static final String PROP_POSOLOGIA = "posologia";
	public static final String PROP_ANOTACAO_ATENDIMENTO = "anotacaoAtendimento";
	public static final String PROP_TABELA_CBO = "tabelaCbo";
	public static final String PROP_COMPETENCIA_ATENDIMENTO = "competenciaAtendimento";
	public static final String PROP_TESTE_RAPIDO_REALIZADO = "testeRapidoRealizado";
	public static final String PROP_PROCEDIMENTO_COMPETENCIA = "procedimentoCompetencia";
	public static final String PROP_QUANTIDADE = "quantidade";
	public static final String PROP_ORIGEM = "origem";
	public static final String PROP_ITEM = "item";
	public static final String PROP_TIPO_ORIGEM = "tipoOrigem";
	public static final String PROP_ATENDIMENTO = "atendimento";
	public static final String PROP_JUSTIFICATIVA = "justificativa";
	public static final String PROP_DATA_HORA = "dataHora";
	public static final String PROP_MOTIVO_CANCELAMENTO = "motivoCancelamento";
	public static final String PROP_ATENDIMENTO_ODONTO_EXECUCAO = "atendimentoOdontoExecucao";
	public static final String PROP_CID = "cid";
	public static final String PROP_STATUS = "status";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_PROCEDIMENTO_BPA = "procedimentoBpa";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DESCRICAO_OBSERVACAO_CANCELAMENTO = "descricaoObservacaoCancelamento";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";
	public static final String PROP_DATA_HORA_APLICACAO = "dataHoraAplicacao";
	public static final String PROP_PROFISSIONAL_APLICACAO = "profissionalAplicacao";
	public static final String PROP_PROFISSIONAL_B_P_A = "profissionalBPA";
	public static final String PROP_EMPRESA_B_P_A = "empresaBPA";
	public static final String PROP_PROFISSIONAL = "profissional";
	public static final String PROP_PROCEDIMENTO_SERVICO_CLASSIFICACAO = "procedimentoServicoClassificacao";


	// constructors
	public BaseAtendimentoItem () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAtendimentoItem (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseAtendimentoItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoBpa,
		br.com.ksisolucoes.vo.basico.Empresa empresaBPA,
		br.com.ksisolucoes.vo.cadsus.Profissional profissionalBPA) {

		this.setCodigo(codigo);
		this.setProcedimentoBpa(procedimentoBpa);
		this.setEmpresaBPA(empresaBPA);
		this.setProfissionalBPA(profissionalBPA);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long item;
	private java.util.Date dataHora;
	private java.lang.String anotacaoAtendimento;
	private java.lang.Long status;
	private java.lang.String posologia;
	private java.lang.String justificativa;
	private java.util.Date dataHoraAplicacao;
	private java.util.Date dataCancelamento;
	private java.lang.String descricaoObservacaoCancelamento;
	private java.lang.Double quantidade;
	private java.lang.String origem;
	private java.lang.Long tipoOrigem;
	private java.util.Date competenciaAtendimento;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento;
	private br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetencia;
	private br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoServicoClassificacao procedimentoServicoClassificacao;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissional;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;
	private br.com.ksisolucoes.vo.basico.MotivoCancelamento motivoCancelamento;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissionalAplicacao;
	private br.com.ksisolucoes.vo.prontuario.basico.AtendimentoOdontoExecucao atendimentoOdontoExecucao;
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCbo;
	private br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoBpa;
	private br.com.ksisolucoes.vo.basico.Empresa empresaBPA;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissionalBPA;
	private br.com.ksisolucoes.vo.prontuario.basico.Cid cid;
	private br.com.ksisolucoes.vo.prontuario.basico.TesteRapidoRealizado testeRapidoRealizado;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_atendimento_item"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: item
	 */
	public java.lang.Long getItem () {
		return getPropertyValue(this, item, PROP_ITEM); 
	}

	/**
	 * Set the value related to the column: item
	 * @param item the item value
	 */
	public void setItem (java.lang.Long item) {
//        java.lang.Long itemOld = this.item;
		this.item = item;
//        this.getPropertyChangeSupport().firePropertyChange ("item", itemOld, item);
	}



	/**
	 * Return the value associated with the column: data_hora
	 */
	public java.util.Date getDataHora () {
		return getPropertyValue(this, dataHora, PROP_DATA_HORA); 
	}

	/**
	 * Set the value related to the column: data_hora
	 * @param dataHora the data_hora value
	 */
	public void setDataHora (java.util.Date dataHora) {
//        java.util.Date dataHoraOld = this.dataHora;
		this.dataHora = dataHora;
//        this.getPropertyChangeSupport().firePropertyChange ("dataHora", dataHoraOld, dataHora);
	}



	/**
	 * Return the value associated with the column: anotacao_atendimento
	 */
	public java.lang.String getAnotacaoAtendimento () {
		return getPropertyValue(this, anotacaoAtendimento, PROP_ANOTACAO_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: anotacao_atendimento
	 * @param anotacaoAtendimento the anotacao_atendimento value
	 */
	public void setAnotacaoAtendimento (java.lang.String anotacaoAtendimento) {
//        java.lang.String anotacaoAtendimentoOld = this.anotacaoAtendimento;
		this.anotacaoAtendimento = anotacaoAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("anotacaoAtendimento", anotacaoAtendimentoOld, anotacaoAtendimento);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: posologia
	 */
	public java.lang.String getPosologia () {
		return getPropertyValue(this, posologia, PROP_POSOLOGIA); 
	}

	/**
	 * Set the value related to the column: posologia
	 * @param posologia the posologia value
	 */
	public void setPosologia (java.lang.String posologia) {
//        java.lang.String posologiaOld = this.posologia;
		this.posologia = posologia;
//        this.getPropertyChangeSupport().firePropertyChange ("posologia", posologiaOld, posologia);
	}



	/**
	 * Return the value associated with the column: ds_justificativa
	 */
	public java.lang.String getJustificativa () {
		return getPropertyValue(this, justificativa, PROP_JUSTIFICATIVA); 
	}

	/**
	 * Set the value related to the column: ds_justificativa
	 * @param justificativa the ds_justificativa value
	 */
	public void setJustificativa (java.lang.String justificativa) {
//        java.lang.String justificativaOld = this.justificativa;
		this.justificativa = justificativa;
//        this.getPropertyChangeSupport().firePropertyChange ("justificativa", justificativaOld, justificativa);
	}



	/**
	 * Return the value associated with the column: data_hora_aplicacao
	 */
	public java.util.Date getDataHoraAplicacao () {
		return getPropertyValue(this, dataHoraAplicacao, PROP_DATA_HORA_APLICACAO); 
	}

	/**
	 * Set the value related to the column: data_hora_aplicacao
	 * @param dataHoraAplicacao the data_hora_aplicacao value
	 */
	public void setDataHoraAplicacao (java.util.Date dataHoraAplicacao) {
//        java.util.Date dataHoraAplicacaoOld = this.dataHoraAplicacao;
		this.dataHoraAplicacao = dataHoraAplicacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataHoraAplicacao", dataHoraAplicacaoOld, dataHoraAplicacao);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: ds_obs_cancelamento
	 */
	public java.lang.String getDescricaoObservacaoCancelamento () {
		return getPropertyValue(this, descricaoObservacaoCancelamento, PROP_DESCRICAO_OBSERVACAO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: ds_obs_cancelamento
	 * @param descricaoObservacaoCancelamento the ds_obs_cancelamento value
	 */
	public void setDescricaoObservacaoCancelamento (java.lang.String descricaoObservacaoCancelamento) {
//        java.lang.String descricaoObservacaoCancelamentoOld = this.descricaoObservacaoCancelamento;
		this.descricaoObservacaoCancelamento = descricaoObservacaoCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoObservacaoCancelamento", descricaoObservacaoCancelamentoOld, descricaoObservacaoCancelamento);
	}



	/**
	 * Return the value associated with the column: qtdade
	 */
	public java.lang.Double getQuantidade () {
		return getPropertyValue(this, quantidade, PROP_QUANTIDADE); 
	}

	/**
	 * Set the value related to the column: qtdade
	 * @param quantidade the qtdade value
	 */
	public void setQuantidade (java.lang.Double quantidade) {
//        java.lang.Double quantidadeOld = this.quantidade;
		this.quantidade = quantidade;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidade", quantidadeOld, quantidade);
	}



	/**
	 * Return the value associated with the column: origem
	 */
	public java.lang.String getOrigem () {
		return getPropertyValue(this, origem, PROP_ORIGEM); 
	}

	/**
	 * Set the value related to the column: origem
	 * @param origem the origem value
	 */
	public void setOrigem (java.lang.String origem) {
//        java.lang.String origemOld = this.origem;
		this.origem = origem;
//        this.getPropertyChangeSupport().firePropertyChange ("origem", origemOld, origem);
	}



	/**
	 * Return the value associated with the column: tipo_origem
	 */
	public java.lang.Long getTipoOrigem () {
		return getPropertyValue(this, tipoOrigem, PROP_TIPO_ORIGEM); 
	}

	/**
	 * Set the value related to the column: tipo_origem
	 * @param tipoOrigem the tipo_origem value
	 */
	public void setTipoOrigem (java.lang.Long tipoOrigem) {
//        java.lang.Long tipoOrigemOld = this.tipoOrigem;
		this.tipoOrigem = tipoOrigem;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoOrigem", tipoOrigemOld, tipoOrigem);
	}



	/**
	 * Return the value associated with the column: competencia_atendimento
	 */
	public java.util.Date getCompetenciaAtendimento () {
		return getPropertyValue(this, competenciaAtendimento, PROP_COMPETENCIA_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: competencia_atendimento
	 * @param competenciaAtendimento the competencia_atendimento value
	 */
	public void setCompetenciaAtendimento (java.util.Date competenciaAtendimento) {
//        java.util.Date competenciaAtendimentoOld = this.competenciaAtendimento;
		this.competenciaAtendimento = competenciaAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("competenciaAtendimento", competenciaAtendimentoOld, competenciaAtendimento);
	}



	/**
	 * Return the value associated with the column: nr_atendimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Atendimento getAtendimento () {
		return getPropertyValue(this, atendimento, PROP_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: nr_atendimento
	 * @param atendimento the nr_atendimento value
	 */
	public void setAtendimento (br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoOld = this.atendimento;
		this.atendimento = atendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimento", atendimentoOld, atendimento);
	}



	/**
	 * Return the value associated with the column: dt_competencia
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia getProcedimentoCompetencia () {
		return getPropertyValue(this, procedimentoCompetencia, PROP_PROCEDIMENTO_COMPETENCIA); 
	}

	/**
	 * Set the value related to the column: dt_competencia
	 * @param procedimentoCompetencia the dt_competencia value
	 */
	public void setProcedimentoCompetencia (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetencia) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetenciaOld = this.procedimentoCompetencia;
		this.procedimentoCompetencia = procedimentoCompetencia;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimentoCompetencia", procedimentoCompetenciaOld, procedimentoCompetencia);
	}

	/**
	 * Return the values associated with the columns: cd_servico and cd_classificacao
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoServicoClassificacao getProcedimentoServicoClassificacao () {
		return getPropertyValue(this, procedimentoServicoClassificacao, PROP_PROCEDIMENTO_SERVICO_CLASSIFICACAO);
	}

	/**
	 * Set the values related to the columns: cd_servico and cd_classificacao
	 * @param procedimentoServicoClassificacao the cd_servico value
	 * @param procedimentoServicoClassificacao the cd_classificacao value
	 */
	public void setProcedimentoServicoClassificacao (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoServicoClassificacao procedimentoServicoClassificacao) {
		this.procedimentoServicoClassificacao = procedimentoServicoClassificacao;
	}



	/**
	 * Return the value associated with the column: cd_profissional
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissional () {
		return getPropertyValue(this, profissional, PROP_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_profissional
	 * @param profissional the cd_profissional value
	 */
	public void setProfissional (br.com.ksisolucoes.vo.cadsus.Profissional profissional) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalOld = this.profissional;
		this.profissional = profissional;
//        this.getPropertyChangeSupport().firePropertyChange ("profissional", profissionalOld, profissional);
	}



	/**
	 * Return the value associated with the column: cd_usuario_can
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_can
	 * @param usuarioCancelamento the cd_usuario_can value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}



	/**
	 * Return the value associated with the column: cod_motivo_can
	 */
	public br.com.ksisolucoes.vo.basico.MotivoCancelamento getMotivoCancelamento () {
		return getPropertyValue(this, motivoCancelamento, PROP_MOTIVO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: cod_motivo_can
	 * @param motivoCancelamento the cod_motivo_can value
	 */
	public void setMotivoCancelamento (br.com.ksisolucoes.vo.basico.MotivoCancelamento motivoCancelamento) {
//        br.com.ksisolucoes.vo.basico.MotivoCancelamento motivoCancelamentoOld = this.motivoCancelamento;
		this.motivoCancelamento = motivoCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoCancelamento", motivoCancelamentoOld, motivoCancelamento);
	}



	/**
	 * Return the value associated with the column: cd_profissional_aplicacao
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissionalAplicacao () {
		return getPropertyValue(this, profissionalAplicacao, PROP_PROFISSIONAL_APLICACAO); 
	}

	/**
	 * Set the value related to the column: cd_profissional_aplicacao
	 * @param profissionalAplicacao the cd_profissional_aplicacao value
	 */
	public void setProfissionalAplicacao (br.com.ksisolucoes.vo.cadsus.Profissional profissionalAplicacao) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalAplicacaoOld = this.profissionalAplicacao;
		this.profissionalAplicacao = profissionalAplicacao;
//        this.getPropertyChangeSupport().firePropertyChange ("profissionalAplicacao", profissionalAplicacaoOld, profissionalAplicacao);
	}



	/**
	 * Return the value associated with the column: cd_execucao
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.AtendimentoOdontoExecucao getAtendimentoOdontoExecucao () {
		return getPropertyValue(this, atendimentoOdontoExecucao, PROP_ATENDIMENTO_ODONTO_EXECUCAO); 
	}

	/**
	 * Set the value related to the column: cd_execucao
	 * @param atendimentoOdontoExecucao the cd_execucao value
	 */
	public void setAtendimentoOdontoExecucao (br.com.ksisolucoes.vo.prontuario.basico.AtendimentoOdontoExecucao atendimentoOdontoExecucao) {
//        br.com.ksisolucoes.vo.prontuario.basico.AtendimentoOdontoExecucao atendimentoOdontoExecucaoOld = this.atendimentoOdontoExecucao;
		this.atendimentoOdontoExecucao = atendimentoOdontoExecucao;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimentoOdontoExecucao", atendimentoOdontoExecucaoOld, atendimentoOdontoExecucao);
	}



	/**
	 * Return the value associated with the column: cd_cbo
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getTabelaCbo () {
		return getPropertyValue(this, tabelaCbo, PROP_TABELA_CBO); 
	}

	/**
	 * Set the value related to the column: cd_cbo
	 * @param tabelaCbo the cd_cbo value
	 */
	public void setTabelaCbo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCbo) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCboOld = this.tabelaCbo;
		this.tabelaCbo = tabelaCbo;
//        this.getPropertyChangeSupport().firePropertyChange ("tabelaCbo", tabelaCboOld, tabelaCbo);
	}



	/**
	 * Return the value associated with the column: cd_procedimento_bpa
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento getProcedimentoBpa () {
		return getPropertyValue(this, procedimentoBpa, PROP_PROCEDIMENTO_BPA); 
	}

	/**
	 * Set the value related to the column: cd_procedimento_bpa
	 * @param procedimentoBpa the cd_procedimento_bpa value
	 */
	public void setProcedimentoBpa (br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoBpa) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoBpaOld = this.procedimentoBpa;
		this.procedimentoBpa = procedimentoBpa;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimentoBpa", procedimentoBpaOld, procedimentoBpa);
	}



	/**
	 * Return the value associated with the column: empresa_bpa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresaBPA () {
		return getPropertyValue(this, empresaBPA, PROP_EMPRESA_B_P_A); 
	}

	/**
	 * Set the value related to the column: empresa_bpa
	 * @param empresaBPA the empresa_bpa value
	 */
	public void setEmpresaBPA (br.com.ksisolucoes.vo.basico.Empresa empresaBPA) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaBPAOld = this.empresaBPA;
		this.empresaBPA = empresaBPA;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaBPA", empresaBPAOld, empresaBPA);
	}



	/**
	 * Return the value associated with the column: cd_profissional_bpa
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissionalBPA () {
		return getPropertyValue(this, profissionalBPA, PROP_PROFISSIONAL_B_P_A); 
	}

	/**
	 * Set the value related to the column: cd_profissional_bpa
	 * @param profissionalBPA the cd_profissional_bpa value
	 */
	public void setProfissionalBPA (br.com.ksisolucoes.vo.cadsus.Profissional profissionalBPA) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalBPAOld = this.profissionalBPA;
		this.profissionalBPA = profissionalBPA;
//        this.getPropertyChangeSupport().firePropertyChange ("profissionalBPA", profissionalBPAOld, profissionalBPA);
	}



	/**
	 * Return the value associated with the column: cd_cid
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Cid getCid () {
		return getPropertyValue(this, cid, PROP_CID); 
	}

	/**
	 * Set the value related to the column: cd_cid
	 * @param cid the cd_cid value
	 */
	public void setCid (br.com.ksisolucoes.vo.prontuario.basico.Cid cid) {
//        br.com.ksisolucoes.vo.prontuario.basico.Cid cidOld = this.cid;
		this.cid = cid;
//        this.getPropertyChangeSupport().firePropertyChange ("cid", cidOld, cid);
	}



	/**
	 * Return the value associated with the column: cd_teste_rapido_realizado
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.TesteRapidoRealizado getTesteRapidoRealizado () {
		return getPropertyValue(this, testeRapidoRealizado, PROP_TESTE_RAPIDO_REALIZADO); 
	}

	/**
	 * Set the value related to the column: cd_teste_rapido_realizado
	 * @param testeRapidoRealizado the cd_teste_rapido_realizado value
	 */
	public void setTesteRapidoRealizado (br.com.ksisolucoes.vo.prontuario.basico.TesteRapidoRealizado testeRapidoRealizado) {
//        br.com.ksisolucoes.vo.prontuario.basico.TesteRapidoRealizado testeRapidoRealizadoOld = this.testeRapidoRealizado;
		this.testeRapidoRealizado = testeRapidoRealizado;
//        this.getPropertyChangeSupport().firePropertyChange ("testeRapidoRealizado", testeRapidoRealizadoOld, testeRapidoRealizado);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.AtendimentoItem)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.AtendimentoItem atendimentoItem = (br.com.ksisolucoes.vo.prontuario.basico.AtendimentoItem) obj;
			if (null == this.getCodigo() || null == atendimentoItem.getCodigo()) return false;
			else return (this.getCodigo().equals(atendimentoItem.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}