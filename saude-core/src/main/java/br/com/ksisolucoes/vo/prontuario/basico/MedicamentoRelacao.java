package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItemHelper;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseMedicamentoRelacao;

import java.io.Serializable;
import java.util.Arrays;


public class MedicamentoRelacao extends BaseMedicamentoRelacao implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public MedicamentoRelacao () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public MedicamentoRelacao (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public MedicamentoRelacao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.entradas.estoque.Produto produto,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataCadastro,
		java.lang.Long quantidade,
		java.lang.Long frequencia,
		java.lang.Long periodoTratamento) {

		super (
			codigo,
			produto,
			usuario,
			dataCadastro,
			quantidade,
			frequencia,
			periodoTratamento);
	}
	public static enum IntervaloLista implements IEnum<IntervaloLista> {
		HORA_12(12L,Bundle.getStringApplication("rotulo_12_em_12")),
		HORA_8(8L,Bundle.getStringApplication("rotulo_8_em_8")),
		HORA_6(6L,Bundle.getStringApplication("rotulo_6_em_6")),
		HORA_4(4L,Bundle.getStringApplication("rotulo_4_em_4"));
		private Long value;
		private String descricao;

		private IntervaloLista(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static IntervaloLista valeuOf(Long value) {
			for (IntervaloLista intervalo : IntervaloLista.values()) {
				if (intervalo.value().equals(value)) {
					return intervalo;
				}
			}
			return null;
		}

		@Override
		public Object value() {
			return this.value;
		}

		@Override
		public String descricao() {
			return this.descricao;
		}
	}

	public String getDescricaoIntervalo() {
		IntervaloLista intervaloLista = IntervaloLista.valeuOf(this.getIntervalo());
		if (intervaloLista != null) {
			return intervaloLista.descricao();
		}
		return "";
	}
	public static enum Frequencia implements IEnum<Frequencia> {

		FREQUENCIA_DIA(1L, Bundle.getStringApplication("rotulo_dia")),
		FREQUENCIA_HORA(2L, Bundle.getStringApplication("rotulo_hora")),
		FREQUENCIA_SEMANA(3L, Bundle.getStringApplication("rotulo_semana")),
		FREQUENCIA_MES(4L, Bundle.getStringApplication("rotulo_mes"));
		private Long value;
		private String descricao;

		private Frequencia(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static Frequencia valeuOf(Long value) {
			for (Frequencia frequencia : Frequencia.values()) {
				if (frequencia.value().equals(value)) {
					return frequencia;
				}
			}
			return null;
		}

		@Override
		public Object value() {
			return this.value;
		}

		@Override
		public String descricao() {
			return this.descricao;
		}
	}

	public String getDescricaoFrequencia() {
		Frequencia frequencia = Frequencia.valeuOf(this.getFrequencia());
		if (frequencia != null) {
			return frequencia.descricao();
		}
		return "";
	}

	public static final String[] TIPOS_DISPENSACAO_COM_DESCRICAO = new String[]{
			DispensacaoMedicamentoItem.DISPENSACAO_GOTAS,
			DispensacaoMedicamentoItem.DISPENSACAO_ML,
			DispensacaoMedicamentoItem.DISPENSACAO_MCG,
			DispensacaoMedicamentoItem.DISPENSACAO_UI
	};

	public String getDescricaoPosologia() {
		String descricaoUnidade = getProduto().getUnidade().getDescricao();
		if (Arrays.asList(TIPOS_DISPENSACAO_COM_DESCRICAO).contains(getProduto().getFlagDispensacaoEspecial())) {
			descricaoUnidade = DispensacaoMedicamentoItemHelper.getDescricaoUnidadePosologia(getProduto());
		}

		if (!DispensacaoMedicamentoItem.DISPENSACAO_SEM_CONTROLE.equals(getProduto().getFlagDispensacaoEspecial())) {
			if (ReceituarioItem.FREQUENCIA_DIA.equals(getFrequencia())) {
				if (getProduto().getTipoReceita()==null || getProduto().getUnidadeReceituario()!=null) {
					return Bundle.getStringApplication("rotulo_descricao_posologia_dias_X",
							getQuantidade(), descricaoUnidade);
				} else {
					return Bundle.getStringApplication("rotulo_descricao_posologia_dias_X_por_Y_dias",
							getQuantidade(), descricaoUnidade, getPeriodoTratamento());
				}
			} else if (ReceituarioItem.FREQUENCIA_SEMANA.equals(getFrequencia())) {
				if (getProduto().getTipoReceita()==null || getProduto().getUnidadeReceituario()!=null) {
					return Bundle.getStringApplication("rotulo_descricao_posologia_semana_X",
							getQuantidade(), descricaoUnidade);
				} else {
					return Bundle.getStringApplication("rotulo_descricao_posologia_semana_X_por_Y_semanas",
							getQuantidade(), descricaoUnidade, getPeriodoTratamento());
				}
			} else if (ReceituarioItem.FREQUENCIA_MES.equals(getFrequencia())) {
				if (getProduto().getTipoReceita()==null || getProduto().getUnidadeReceituario()!=null) {
					return Bundle.getStringApplication("rotulo_descricao_posologia_mes_X",
							getQuantidade(), descricaoUnidade);
				} else {
					return Bundle.getStringApplication("rotulo_descricao_posologia_mes_X_por_Y_meses",
							getQuantidade(), descricaoUnidade, getPeriodoTratamento());
				}
			} else if (ReceituarioItem.FREQUENCIA_HORA.equals(getFrequencia())) {
				if (getIntervalo() == null) {
					return null;
				}
				if (getProduto().getTipoReceita()==null || getProduto().getUnidadeReceituario()!=null) {
					return Bundle.getStringApplication("rotulo_descricao_posologia_horas_X",
							getQuantidade(), descricaoUnidade,
							getIntervalo());
				} else {
					return Bundle.getStringApplication("rotulo_descricao_posologia_horas_X_por_Y_dias",
							getQuantidade(), descricaoUnidade,
							getIntervalo(), getPeriodoTratamento());
				}
			}
		}
		return null;
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}