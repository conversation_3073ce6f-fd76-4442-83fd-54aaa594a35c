package br.com.ksisolucoes.vo.basico;

import java.io.Serializable;

import br.com.ksisolucoes.vo.basico.base.BaseComorbidadePaciente;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class ComorbidadePaciente extends BaseComorbidadePaciente implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ComorbidadePaciente () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ComorbidadePaciente (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ComorbidadePaciente (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		br.com.ksisolucoes.vo.basico.Comorbidade comorbidade) {

		super (
			codigo,
			usuarioCadsus,
			comorbidade);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}