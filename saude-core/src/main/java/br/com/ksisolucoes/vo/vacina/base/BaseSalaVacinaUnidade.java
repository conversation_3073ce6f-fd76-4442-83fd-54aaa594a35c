package br.com.ksisolucoes.vo.vacina.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the sala_vacina_unidade table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="sala_vacina_unidade"
 */

public abstract class BaseSalaVacinaUnidade extends BaseRootVO implements Serializable {

	public static String REF = "SalaVacinaUnidade";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TARDE = "tarde";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_NOITE = "noite";
	public static final String PROP_MANHA = "manha";


	// constructors
	public BaseSalaVacinaUnidade () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseSalaVacinaUnidade (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseSalaVacinaUnidade (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Empresa empresa) {

		this.setCodigo(codigo);
		this.setEmpresa(empresa);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long manha;
	private java.lang.Long tarde;
	private java.lang.Long noite;

	// many to one
	private br.com.ksisolucoes.vo.basico.Empresa empresa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_sala_vac_unidade"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: manha
	 */
	public java.lang.Long getManha () {
		return getPropertyValue(this, manha, PROP_MANHA); 
	}

	/**
	 * Set the value related to the column: manha
	 * @param manha the manha value
	 */
	public void setManha (java.lang.Long manha) {
//        java.lang.Long manhaOld = this.manha;
		this.manha = manha;
//        this.getPropertyChangeSupport().firePropertyChange ("manha", manhaOld, manha);
	}



	/**
	 * Return the value associated with the column: tarde
	 */
	public java.lang.Long getTarde () {
		return getPropertyValue(this, tarde, PROP_TARDE); 
	}

	/**
	 * Set the value related to the column: tarde
	 * @param tarde the tarde value
	 */
	public void setTarde (java.lang.Long tarde) {
//        java.lang.Long tardeOld = this.tarde;
		this.tarde = tarde;
//        this.getPropertyChangeSupport().firePropertyChange ("tarde", tardeOld, tarde);
	}



	/**
	 * Return the value associated with the column: noite
	 */
	public java.lang.Long getNoite () {
		return getPropertyValue(this, noite, PROP_NOITE); 
	}

	/**
	 * Set the value related to the column: noite
	 * @param noite the noite value
	 */
	public void setNoite (java.lang.Long noite) {
//        java.lang.Long noiteOld = this.noite;
		this.noite = noite;
//        this.getPropertyChangeSupport().firePropertyChange ("noite", noiteOld, noite);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vacina.SalaVacinaUnidade)) return false;
		else {
			br.com.ksisolucoes.vo.vacina.SalaVacinaUnidade salaVacinaUnidade = (br.com.ksisolucoes.vo.vacina.SalaVacinaUnidade) obj;
			if (null == this.getCodigo() || null == salaVacinaUnidade.getCodigo()) return false;
			else return (this.getCodigo().equals(salaVacinaUnidade.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}