package br.com.ksisolucoes.vo.prontuario.integracao.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the configuraco_empresa_integracao table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="configuraco_empresa_integracao"
 */

public abstract class BaseConfiguracaoEmpresaIntegracao extends BaseRootVO implements Serializable {

	public static String REF = "ConfiguracaoEmpresaIntegracao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_URL = "url";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_CHAVE = "chave";
	public static final String PROP_ID = "id";


	// constructors
	public BaseConfiguracaoEmpresaIntegracao () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseConfiguracaoEmpresaIntegracao (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseConfiguracaoEmpresaIntegracao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		java.lang.String url,
		java.lang.String id,
		java.lang.String chave) {

		this.setCodigo(codigo);
		this.setEmpresa(empresa);
		this.setUrl(url);
		this.setId(id);
		this.setChave(chave);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String url;
	private java.lang.String id;
	private java.lang.String chave;

	// many to one
	private br.com.ksisolucoes.vo.basico.Empresa empresa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_configuraco_empresa_integracao"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: url
	 */
	public java.lang.String getUrl () {
		return getPropertyValue(this, url, PROP_URL); 
	}

	/**
	 * Set the value related to the column: url
	 * @param url the url value
	 */
	public void setUrl (java.lang.String url) {
//        java.lang.String urlOld = this.url;
		this.url = url;
//        this.getPropertyChangeSupport().firePropertyChange ("url", urlOld, url);
	}



	/**
	 * Return the value associated with the column: id
	 */
	public java.lang.String getId () {
		return getPropertyValue(this, id, PROP_ID); 
	}

	/**
	 * Set the value related to the column: id
	 * @param id the id value
	 */
	public void setId (java.lang.String id) {
//        java.lang.String idOld = this.id;
		this.id = id;
//        this.getPropertyChangeSupport().firePropertyChange ("id", idOld, id);
	}



	/**
	 * Return the value associated with the column: chave
	 */
	public java.lang.String getChave () {
		return getPropertyValue(this, chave, PROP_CHAVE); 
	}

	/**
	 * Set the value related to the column: chave
	 * @param chave the chave value
	 */
	public void setChave (java.lang.String chave) {
//        java.lang.String chaveOld = this.chave;
		this.chave = chave;
//        this.getPropertyChangeSupport().firePropertyChange ("chave", chaveOld, chave);
	}



	/**
	 * Return the value associated with the column: cd_empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: cd_empresa
	 * @param empresa the cd_empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.integracao.ConfiguracaoEmpresaIntegracao)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.integracao.ConfiguracaoEmpresaIntegracao configuracaoEmpresaIntegracao = (br.com.ksisolucoes.vo.prontuario.integracao.ConfiguracaoEmpresaIntegracao) obj;
			if (null == this.getCodigo() || null == configuracaoEmpresaIntegracao.getCodigo()) return false;
			else return (this.getCodigo().equals(configuracaoEmpresaIntegracao.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}