package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;
import java.text.ParseException;

import javax.swing.text.MaskFormatter;

import org.apache.commons.lang.StringUtils;

import br.com.ksisolucoes.util.EnumUtil;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseAtendimentoRecemNascido;



public class AtendimentoRecemNascido extends BaseAtendimentoRecemNascido implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public AtendimentoRecemNascido () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public AtendimentoRecemNascido (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public AtendimentoRecemNascido (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento,
		java.util.Date dataRegistro,
		java.util.Date dataNascimento,
		java.lang.String sexo) {

		super (
			codigo,
			atendimento,
			dataRegistro,
			dataNascimento,
			sexo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public String getColirioFormatado(){
        return formatarHora(getColirio());
    }
    
    public String getVitaminaKFormatado(){
        return formatarHora(getVitaminaK());
    }
    
    public String getOxigenioInicioFormatado(){
        return formatarHora(getOxigenioInicio());
    }
    
    public String getOxigenioTerminoFormatado(){
        return formatarHora(getOxigenioTermino());
    }
    
    public String getSexoFormatado(){
        return new EnumUtil().resolveDescricao(RepositoryComponentDefault.Sexo.values(), getSexo());
    }
    
    private String formatarHora(String hora){
        if (StringUtils.trimToNull(hora)!=null) {
            try {
                MaskFormatter m = new MaskFormatter("##:##");
                m.setValueContainsLiteralCharacters(false);

                return m.valueToString(hora);
            } catch (ParseException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        }

        return "";
    }
    
}