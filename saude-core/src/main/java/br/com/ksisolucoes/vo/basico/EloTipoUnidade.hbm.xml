<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
	
<hibernate-mapping package="br.com.ksisolucoes.vo.basico"  >
    <class 
        name="EloTipoUnidade"
        table="elo_tipo_unidade"
    >
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_elo_tp_unidade"
        >
            <generator class="assigned"/>
        </id> 
        <version column="version" name="version" type="long" />

        <property
            name="tipoUnidade"
            column="tp_unidade"
            type="java.lang.Long"
            not-null="true"
            
		/>

        <many-to-one
            name="empresa"
            class="br.com.ksisolucoes.vo.basico.Empresa"
            not-null="true"
        >
            <column name="empresa"/>
        </many-to-one>
                
    </class>
</hibernate-mapping>