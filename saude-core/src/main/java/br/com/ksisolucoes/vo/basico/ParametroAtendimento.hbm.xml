<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.basico"  >
    <class name="ParametroAtendimento" table="parametro_atendimento">
        <id
            column="cod_parametro"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />
 
        <many-to-one    
            class="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
            name="procedimentoEnfermagem" 
        >
            <column name="cd_proced_enfermagem" />
        </many-to-one>

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
            name="procedimentoPrimario"
        >
            <column name="cd_proced_primario" />
        </many-to-one>

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
            name="procedimentoMedico"
        >
            <column name="cd_proced_medico" />
        </many-to-one>

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
            name="procedimentoEmergencia"
        >
            <column name="cd_proced_emergencia" />
        </many-to-one>

        <property
            name="dataAtualizacao"
            not-null="false" 
            length="4"
            type="java.util.Date"
        >
            <column name="dt_atualizacao" sql-type="timestamp"/>
        </property>

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"
            name="tipoAtendimentoEnfermagem"
        >
            <column name="cd_tp_atendimento_enfermagem" />
        </many-to-one>
 
        <property
            name="diaInicioCompetencia"
            type="java.lang.Long"
            column="dia_ini_comp"
        />
 
        <property
            name="bpaNomeOrigem"
            type="java.lang.String"
            column="bpa_nome_origem"
        />
 
        <property
            name="bpaSiglaOrigem"
            type="java.lang.String"
            column="bpa_sigla_origem"
        />
 
        <property
            name="bpaCnpjCpfOrigem"
            type="java.lang.String"
            column="bpa_cnpj_cpf_origem"
        />
 
        <property
            name="bpaNomeDestino"
            type="java.lang.String"
            column="bpa_nome_destino"
        />
 
        <property
            name="bpaFlagDestino"
            type="java.lang.String"
            column="bpa_flag_destino"
            length="1"
        />

        <property
            name="FlagBpaGeraTfd"
            type="java.lang.String"
            column="flag_bpa_gera_tfd"
            length="1"
        />
        
        <property
            name="flagBpaGeraVigilanciaSanitaria"
            type="java.lang.Long"
            column="flag_bpa_gera_vigilancia_sanitaria"
            length="1"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.TipoEncaminhamento"
            name="tipoEncaminhamentoPequenaCirurgia"
        >
            <column name="cd_tp_enc_peq_cir"/>
        </many-to-one>
        
        <property
            name="flagBpaVacina"
            type="java.lang.Long"
            column="flag_bpa_vacina"
        />

        <property
            name="dataProcessoCidNotificavel"
            not-null="false" 
            type="java.util.Date"
        >
            <column name="dt_proc_cid_notificavel"/>
        </property>
         
    </class>
</hibernate-mapping>
