<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.frota">
    <class
        name="RegistroManutencao"
        table="registro_manutencao"
    >
        <id
            name="codigo"
            type="java.lang.Long" 
            column="cd_manutencao"
        >
        </id> 
        <version column="version" name="version" type="long" />
 
         <many-to-one
            class="br.com.ksisolucoes.vo.basico.Pessoa"
            name="fornecedor"
            not-null="true"
        >
            <column name="cod_pessoa"/>
        </many-to-one>
 
        <property
            name="documento"
            column="cod_documento"
            type="java.lang.String"
            not-null="true"
            length="10"
            />
            
         <many-to-one
            class="br.com.ksisolucoes.vo.financeiro.Serie"
            name="serie"
            not-null="true"
        >
            <column name="serie"/>
        </many-to-one>
            
         <many-to-one
            class="br.com.ksisolucoes.vo.frota.ModeloDocumento"
            name="modeloDocumento"
            not-null="true"
        >
            <column name="cd_modelo_documento"/>
        </many-to-one>
 
         <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            name="usuario"
            not-null="true"
        >
            <column name="cd_usuario"/>
        </many-to-one>
 
         <property
            name="dataUsuario"
            column="dt_usuario"
            type="java.util.Date"
            not-null="true"
            /> 
 
         <property
            name="dataCadastro"
            column="dt_cadastro"
            type="java.util.Date"
            not-null="true"
            /> 
 
         <property
            name="valorTotal"
            column="vl_total"
            type="java.lang.Double"
            not-null="true"
            /> 
 
         <property
            name="observacao"
            column="observacao"
            type="java.lang.String"
            not-null="false"
            length="1024"
            /> 
 
  
         <property
            name="dataManutencao"
            column="dt_manutencao"
            type="java.util.Date"
            not-null="true"
            /> 
 
         <many-to-one
            class="br.com.ksisolucoes.vo.basico.Empresa"
            name="empresa"
            not-null="true"
        >
            <column name="empresa"/>
        </many-to-one>
 
    </class>
</hibernate-mapping>
