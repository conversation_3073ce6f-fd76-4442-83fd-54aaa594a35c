package br.com.ksisolucoes.vo.cadsus;

import java.io.Serializable;

import br.com.ksisolucoes.vo.cadsus.base.BaseFormularioTriagemSinaisClinicosCovid19;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class FormularioTriagemSinaisClinicosCovid19 extends BaseFormularioTriagemSinaisClinicosCovid19 implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public FormularioTriagemSinaisClinicosCovid19 () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public FormularioTriagemSinaisClinicosCovid19 (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public FormularioTriagemSinaisClinicosCovid19 (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.FormularioTriagemCovid19 formularioTriagemCovid19,
		br.com.ksisolucoes.vo.cadsus.SinaisClinicosCovid19 sinalClinicoCovid19) {

		super (
			codigo,
			formularioTriagemCovid19,
			sinalClinicoCovid19);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}