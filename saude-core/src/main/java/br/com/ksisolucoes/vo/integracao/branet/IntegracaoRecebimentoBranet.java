package br.com.ksisolucoes.vo.integracao.branet;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.integracao.branet.base.BaseIntegracaoRecebimentoBranet;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;


public class IntegracaoRecebimentoBranet extends BaseIntegracaoRecebimentoBranet implements CodigoManager {
    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public IntegracaoRecebimentoBranet () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public IntegracaoRecebimentoBranet (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public IntegracaoRecebimentoBranet (
		java.lang.Long codigo,
		java.util.Date data,
		java.util.Date dataCadastro) {

		super (
			codigo,
			data,
			dataCadastro);
	}

    /*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}