<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="ReceituarioItemKit" table="receituario_item_kit" >
         
        <id
            column="cd_receituario_item_kit"
            name="codigo"
            type="java.lang.Long"
        > 
            <generator class="assigned" />
        </id>
        
        <version column="version" name="version" type="long" /> 
        
        <many-to-one 
            class="br.com.ksisolucoes.vo.entradas.estoque.Produto"
            name="produtoKit"
            column="cod_pro"
            not-null="true"
        />
        
        <many-to-one 
            class="br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem"
            name="receituarioItem"
            column="cd_receiturario_item"
            not-null="true"
        />

        <property
            name="quantidade"
            column="qtdade"
            not-null="true"
            type="java.lang.Double"
        />
        
        <many-to-one 
            class="br.com.ksisolucoes.vo.entradas.estoque.Produto"
            name="produtoDispensado"
            column="cod_pro_dispensado"
            not-null="true"
        />
        
        <property
            name="quantidadeDispensada"
            column="qtd_dispensada"
            not-null="false"
            type="java.lang.Double"
        />
        
        <property
            name="status"
            column="status"
            not-null="true"
            type="java.lang.Long"
        />
        
        <property
            name="dataCancelamento"
            column="dt_cancelamento"
            type="timestamp"
            not-null="false"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            name="usuarioCancelamento"
            column="cd_usu_can"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.entradas.estoque.Unidade"
            name="unidade"
            column="cod_uni"
        />
        
    </class>
</hibernate-mapping>
