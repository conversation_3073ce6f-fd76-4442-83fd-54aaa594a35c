package br.com.ksisolucoes.vo.vigilancia.forcatarefa.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the ficha_forca_tarefa_covid19 table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="ficha_forca_tarefa_covid19"
 */

public abstract class BaseFichaForcaTarefaCovid19 extends BaseRootVO implements Serializable {

	public static String REF = "FichaForcaTarefaCovid19";
	public static final String PROP_BAIRRO_DESTINO = "bairroDestino";
	public static final String PROP_FALTA_DE_AR = "faltaDeAr";
	public static final String PROP_EMAIL = "email";
	public static final String PROP_DOCUMENTO_IDENTIFICACAO_RESPONSAVEL = "documentoIdentificacaoResponsavel";
	public static final String PROP_TELEFONE = "telefone";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_CIDADE_DESTINO = "cidadeDestino";
	public static final String PROP_NARIZ_ESCORRENDO = "narizEscorrendo";
	public static final String PROP_TOSSE = "tosse";
	public static final String PROP_CIDADE_DOMICILIO = "cidadeDomicilio";
	public static final String PROP_NOME = "nome";
	public static final String PROP_PAIS_DESTINO = "paisDestino";
	public static final String PROP_PAIS_DOMICILIO = "paisDomicilio";
	public static final String PROP_FEBRE = "febre";
	public static final String PROP_DOCUMENTO_IDENTIFICACAO = "documentoIdentificacao";
	public static final String PROP_ACEITE_TERMO = "aceiteTermo";
	public static final String PROP_DATA_INICIO_SINTOMAS = "dataInicioSintomas";
	public static final String PROP_MENOR_ACOMPANHADO = "menorAcompanhado";
	public static final String PROP_NUMERO_VOO = "numeroVoo";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_NOME_RESPONSAVEL = "nomeResponsavel";
	public static final String PROP_BAIRRO_DOMICILIO = "bairroDomicilio";
	public static final String PROP_DOR_DE_GARGANTA = "dorDeGarganta";
	public static final String PROP_CONGESTAO_NASAL = "congestaoNasal";
	public static final String PROP_ENDERECO_DOMICILIO = "enderecoDomicilio";
	public static final String PROP_PAIS_ORIGEM = "paisOrigem";
	public static final String PROP_ENDERECO_DESTINO = "enderecoDestino";
	public static final String PROP_CIDADE_ORIGEM = "cidadeOrigem";
	public static final String PROP_UUID_QR_CODE = "uuidQrCode";


	// constructors
	public BaseFichaForcaTarefaCovid19 () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseFichaForcaTarefaCovid19 (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseFichaForcaTarefaCovid19 (
		java.lang.Long codigo,
		java.lang.String nome,
		java.lang.String documentoIdentificacao,
		java.lang.Integer menorAcompanhado,
		java.lang.String nomeResponsavel,
		java.lang.String email,
		java.lang.String telefone,
		java.lang.String paisDomicilio,
		java.lang.String enderecoDomicilio,
		java.lang.String cidadeDomicilio,
		java.lang.String bairroDomicilio,
		java.lang.String paisOrigem,
		java.lang.String cidadeOrigem,
		java.lang.String paisDestino,
		java.lang.String enderecoDestino,
		java.lang.String bairroDestino,
		java.lang.String cidadeDestino,
		java.lang.String numeroVoo,
		java.lang.Integer febre,
		java.lang.Integer tosse,
		java.lang.Integer faltaDeAr,
		java.lang.Integer congestaoNasal,
		java.lang.Integer narizEscorrendo,
		java.lang.Integer dorDeGarganta,
		java.lang.Integer aceiteTermo,
		java.util.Date dataCadastro,
		java.lang.String uuidQrCode) {

		this.setCodigo(codigo);
		this.setNome(nome);
		this.setDocumentoIdentificacao(documentoIdentificacao);
		this.setMenorAcompanhado(menorAcompanhado);
		this.setNomeResponsavel(nomeResponsavel);
		this.setEmail(email);
		this.setTelefone(telefone);
		this.setPaisDomicilio(paisDomicilio);
		this.setEnderecoDomicilio(enderecoDomicilio);
		this.setCidadeDomicilio(cidadeDomicilio);
		this.setBairroDomicilio(bairroDomicilio);
		this.setPaisOrigem(paisOrigem);
		this.setCidadeOrigem(cidadeOrigem);
		this.setPaisDestino(paisDestino);
		this.setEnderecoDestino(enderecoDestino);
		this.setBairroDestino(bairroDestino);
		this.setCidadeDestino(cidadeDestino);
		this.setNumeroVoo(numeroVoo);
		this.setFebre(febre);
		this.setTosse(tosse);
		this.setFaltaDeAr(faltaDeAr);
		this.setCongestaoNasal(congestaoNasal);
		this.setNarizEscorrendo(narizEscorrendo);
		this.setDorDeGarganta(dorDeGarganta);
		this.setAceiteTermo(aceiteTermo);
		this.setDataCadastro(dataCadastro);
		this.setUuidQrCode(uuidQrCode);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String nome;
	private java.lang.String documentoIdentificacao;
	private java.lang.Integer menorAcompanhado;
	private java.lang.String nomeResponsavel;
	private java.lang.String documentoIdentificacaoResponsavel;
	private java.lang.String email;
	private java.lang.String telefone;
	private java.lang.String paisDomicilio;
	private java.lang.String enderecoDomicilio;
	private java.lang.String cidadeDomicilio;
	private java.lang.String bairroDomicilio;
	private java.lang.String paisOrigem;
	private java.lang.String cidadeOrigem;
	private java.lang.String paisDestino;
	private java.lang.String enderecoDestino;
	private java.lang.String bairroDestino;
	private java.lang.String cidadeDestino;
	private java.lang.String numeroVoo;
	private java.lang.Integer febre;
	private java.lang.Integer tosse;
	private java.lang.Integer faltaDeAr;
	private java.lang.Integer congestaoNasal;
	private java.lang.Integer narizEscorrendo;
	private java.lang.Integer dorDeGarganta;
	private java.lang.Integer aceiteTermo;
	private java.util.Date dataInicioSintomas;
	private java.util.Date dataCadastro;
	private java.lang.String uuidQrCode;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_ficha_forca_tarefa_covid19"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: nome
	 */
	public java.lang.String getNome () {
		return getPropertyValue(this, nome, PROP_NOME); 
	}

	/**
	 * Set the value related to the column: nome
	 * @param nome the nome value
	 */
	public void setNome (java.lang.String nome) {
//        java.lang.String nomeOld = this.nome;
		this.nome = nome;
//        this.getPropertyChangeSupport().firePropertyChange ("nome", nomeOld, nome);
	}



	/**
	 * Return the value associated with the column: documento_identificacao
	 */
	public java.lang.String getDocumentoIdentificacao () {
		return getPropertyValue(this, documentoIdentificacao, PROP_DOCUMENTO_IDENTIFICACAO); 
	}

	/**
	 * Set the value related to the column: documento_identificacao
	 * @param documentoIdentificacao the documento_identificacao value
	 */
	public void setDocumentoIdentificacao (java.lang.String documentoIdentificacao) {
//        java.lang.String documentoIdentificacaoOld = this.documentoIdentificacao;
		this.documentoIdentificacao = documentoIdentificacao;
//        this.getPropertyChangeSupport().firePropertyChange ("documentoIdentificacao", documentoIdentificacaoOld, documentoIdentificacao);
	}



	/**
	 * Return the value associated with the column: menor_acompanhado
	 */
	public java.lang.Integer getMenorAcompanhado () {
		return getPropertyValue(this, menorAcompanhado, PROP_MENOR_ACOMPANHADO); 
	}

	/**
	 * Set the value related to the column: menor_acompanhado
	 * @param menorAcompanhado the menor_acompanhado value
	 */
	public void setMenorAcompanhado (java.lang.Integer menorAcompanhado) {
//        java.lang.Integer menorAcompanhadoOld = this.menorAcompanhado;
		this.menorAcompanhado = menorAcompanhado;
//        this.getPropertyChangeSupport().firePropertyChange ("menorAcompanhado", menorAcompanhadoOld, menorAcompanhado);
	}



	/**
	 * Return the value associated with the column: nome_responsavel
	 */
	public java.lang.String getNomeResponsavel () {
		return getPropertyValue(this, nomeResponsavel, PROP_NOME_RESPONSAVEL); 
	}

	/**
	 * Set the value related to the column: nome_responsavel
	 * @param nomeResponsavel the nome_responsavel value
	 */
	public void setNomeResponsavel (java.lang.String nomeResponsavel) {
//        java.lang.String nomeResponsavelOld = this.nomeResponsavel;
		this.nomeResponsavel = nomeResponsavel;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeResponsavel", nomeResponsavelOld, nomeResponsavel);
	}



	/**
	 * Return the value associated with the column: documento_identificacao_responsavel
	 */
	public java.lang.String getDocumentoIdentificacaoResponsavel () {
		return getPropertyValue(this, documentoIdentificacaoResponsavel, PROP_DOCUMENTO_IDENTIFICACAO_RESPONSAVEL); 
	}

	/**
	 * Set the value related to the column: documento_identificacao_responsavel
	 * @param documentoIdentificacaoResponsavel the documento_identificacao_responsavel value
	 */
	public void setDocumentoIdentificacaoResponsavel (java.lang.String documentoIdentificacaoResponsavel) {
//        java.lang.String documentoIdentificacaoResponsavelOld = this.documentoIdentificacaoResponsavel;
		this.documentoIdentificacaoResponsavel = documentoIdentificacaoResponsavel;
//        this.getPropertyChangeSupport().firePropertyChange ("documentoIdentificacaoResponsavel", documentoIdentificacaoResponsavelOld, documentoIdentificacaoResponsavel);
	}



	/**
	 * Return the value associated with the column: email
	 */
	public java.lang.String getEmail () {
		return getPropertyValue(this, email, PROP_EMAIL); 
	}

	/**
	 * Set the value related to the column: email
	 * @param email the email value
	 */
	public void setEmail (java.lang.String email) {
//        java.lang.String emailOld = this.email;
		this.email = email;
//        this.getPropertyChangeSupport().firePropertyChange ("email", emailOld, email);
	}



	/**
	 * Return the value associated with the column: telefone
	 */
	public java.lang.String getTelefone () {
		return getPropertyValue(this, telefone, PROP_TELEFONE); 
	}

	/**
	 * Set the value related to the column: telefone
	 * @param telefone the telefone value
	 */
	public void setTelefone (java.lang.String telefone) {
//        java.lang.String telefoneOld = this.telefone;
		this.telefone = telefone;
//        this.getPropertyChangeSupport().firePropertyChange ("telefone", telefoneOld, telefone);
	}



	/**
	 * Return the value associated with the column: pais_domicilio
	 */
	public java.lang.String getPaisDomicilio () {
		return getPropertyValue(this, paisDomicilio, PROP_PAIS_DOMICILIO); 
	}

	/**
	 * Set the value related to the column: pais_domicilio
	 * @param paisDomicilio the pais_domicilio value
	 */
	public void setPaisDomicilio (java.lang.String paisDomicilio) {
//        java.lang.String paisDomicilioOld = this.paisDomicilio;
		this.paisDomicilio = paisDomicilio;
//        this.getPropertyChangeSupport().firePropertyChange ("paisDomicilio", paisDomicilioOld, paisDomicilio);
	}



	/**
	 * Return the value associated with the column: endereco_domicilio
	 */
	public java.lang.String getEnderecoDomicilio () {
		return getPropertyValue(this, enderecoDomicilio, PROP_ENDERECO_DOMICILIO); 
	}

	/**
	 * Set the value related to the column: endereco_domicilio
	 * @param enderecoDomicilio the endereco_domicilio value
	 */
	public void setEnderecoDomicilio (java.lang.String enderecoDomicilio) {
//        java.lang.String enderecoDomicilioOld = this.enderecoDomicilio;
		this.enderecoDomicilio = enderecoDomicilio;
//        this.getPropertyChangeSupport().firePropertyChange ("enderecoDomicilio", enderecoDomicilioOld, enderecoDomicilio);
	}



	/**
	 * Return the value associated with the column: cidade_domicilio
	 */
	public java.lang.String getCidadeDomicilio () {
		return getPropertyValue(this, cidadeDomicilio, PROP_CIDADE_DOMICILIO); 
	}

	/**
	 * Set the value related to the column: cidade_domicilio
	 * @param cidadeDomicilio the cidade_domicilio value
	 */
	public void setCidadeDomicilio (java.lang.String cidadeDomicilio) {
//        java.lang.String cidadeDomicilioOld = this.cidadeDomicilio;
		this.cidadeDomicilio = cidadeDomicilio;
//        this.getPropertyChangeSupport().firePropertyChange ("cidadeDomicilio", cidadeDomicilioOld, cidadeDomicilio);
	}



	/**
	 * Return the value associated with the column: bairro_domicilio
	 */
	public java.lang.String getBairroDomicilio () {
		return getPropertyValue(this, bairroDomicilio, PROP_BAIRRO_DOMICILIO); 
	}

	/**
	 * Set the value related to the column: bairro_domicilio
	 * @param bairroDomicilio the bairro_domicilio value
	 */
	public void setBairroDomicilio (java.lang.String bairroDomicilio) {
//        java.lang.String bairroDomicilioOld = this.bairroDomicilio;
		this.bairroDomicilio = bairroDomicilio;
//        this.getPropertyChangeSupport().firePropertyChange ("bairroDomicilio", bairroDomicilioOld, bairroDomicilio);
	}



	/**
	 * Return the value associated with the column: pais_origem
	 */
	public java.lang.String getPaisOrigem () {
		return getPropertyValue(this, paisOrigem, PROP_PAIS_ORIGEM); 
	}

	/**
	 * Set the value related to the column: pais_origem
	 * @param paisOrigem the pais_origem value
	 */
	public void setPaisOrigem (java.lang.String paisOrigem) {
//        java.lang.String paisOrigemOld = this.paisOrigem;
		this.paisOrigem = paisOrigem;
//        this.getPropertyChangeSupport().firePropertyChange ("paisOrigem", paisOrigemOld, paisOrigem);
	}



	/**
	 * Return the value associated with the column: cidade_origem
	 */
	public java.lang.String getCidadeOrigem () {
		return getPropertyValue(this, cidadeOrigem, PROP_CIDADE_ORIGEM); 
	}

	/**
	 * Set the value related to the column: cidade_origem
	 * @param cidadeOrigem the cidade_origem value
	 */
	public void setCidadeOrigem (java.lang.String cidadeOrigem) {
//        java.lang.String cidadeOrigemOld = this.cidadeOrigem;
		this.cidadeOrigem = cidadeOrigem;
//        this.getPropertyChangeSupport().firePropertyChange ("cidadeOrigem", cidadeOrigemOld, cidadeOrigem);
	}



	/**
	 * Return the value associated with the column: pais_destino
	 */
	public java.lang.String getPaisDestino () {
		return getPropertyValue(this, paisDestino, PROP_PAIS_DESTINO); 
	}

	/**
	 * Set the value related to the column: pais_destino
	 * @param paisDestino the pais_destino value
	 */
	public void setPaisDestino (java.lang.String paisDestino) {
//        java.lang.String paisDestinoOld = this.paisDestino;
		this.paisDestino = paisDestino;
//        this.getPropertyChangeSupport().firePropertyChange ("paisDestino", paisDestinoOld, paisDestino);
	}



	/**
	 * Return the value associated with the column: endereco_destino
	 */
	public java.lang.String getEnderecoDestino () {
		return getPropertyValue(this, enderecoDestino, PROP_ENDERECO_DESTINO); 
	}

	/**
	 * Set the value related to the column: endereco_destino
	 * @param enderecoDestino the endereco_destino value
	 */
	public void setEnderecoDestino (java.lang.String enderecoDestino) {
//        java.lang.String enderecoDestinoOld = this.enderecoDestino;
		this.enderecoDestino = enderecoDestino;
//        this.getPropertyChangeSupport().firePropertyChange ("enderecoDestino", enderecoDestinoOld, enderecoDestino);
	}



	/**
	 * Return the value associated with the column: bairro_destino
	 */
	public java.lang.String getBairroDestino () {
		return getPropertyValue(this, bairroDestino, PROP_BAIRRO_DESTINO); 
	}

	/**
	 * Set the value related to the column: bairro_destino
	 * @param bairroDestino the bairro_destino value
	 */
	public void setBairroDestino (java.lang.String bairroDestino) {
//        java.lang.String bairroDestinoOld = this.bairroDestino;
		this.bairroDestino = bairroDestino;
//        this.getPropertyChangeSupport().firePropertyChange ("bairroDestino", bairroDestinoOld, bairroDestino);
	}



	/**
	 * Return the value associated with the column: cidade_destino
	 */
	public java.lang.String getCidadeDestino () {
		return getPropertyValue(this, cidadeDestino, PROP_CIDADE_DESTINO); 
	}

	/**
	 * Set the value related to the column: cidade_destino
	 * @param cidadeDestino the cidade_destino value
	 */
	public void setCidadeDestino (java.lang.String cidadeDestino) {
//        java.lang.String cidadeDestinoOld = this.cidadeDestino;
		this.cidadeDestino = cidadeDestino;
//        this.getPropertyChangeSupport().firePropertyChange ("cidadeDestino", cidadeDestinoOld, cidadeDestino);
	}



	/**
	 * Return the value associated with the column: numero_voo
	 */
	public java.lang.String getNumeroVoo () {
		return getPropertyValue(this, numeroVoo, PROP_NUMERO_VOO); 
	}

	/**
	 * Set the value related to the column: numero_voo
	 * @param numeroVoo the numero_voo value
	 */
	public void setNumeroVoo (java.lang.String numeroVoo) {
//        java.lang.String numeroVooOld = this.numeroVoo;
		this.numeroVoo = numeroVoo;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroVoo", numeroVooOld, numeroVoo);
	}



	/**
	 * Return the value associated with the column: febre
	 */
	public java.lang.Integer getFebre () {
		return getPropertyValue(this, febre, PROP_FEBRE); 
	}

	/**
	 * Set the value related to the column: febre
	 * @param febre the febre value
	 */
	public void setFebre (java.lang.Integer febre) {
//        java.lang.Integer febreOld = this.febre;
		this.febre = febre;
//        this.getPropertyChangeSupport().firePropertyChange ("febre", febreOld, febre);
	}



	/**
	 * Return the value associated with the column: tosse
	 */
	public java.lang.Integer getTosse () {
		return getPropertyValue(this, tosse, PROP_TOSSE); 
	}

	/**
	 * Set the value related to the column: tosse
	 * @param tosse the tosse value
	 */
	public void setTosse (java.lang.Integer tosse) {
//        java.lang.Integer tosseOld = this.tosse;
		this.tosse = tosse;
//        this.getPropertyChangeSupport().firePropertyChange ("tosse", tosseOld, tosse);
	}



	/**
	 * Return the value associated with the column: falta_de_ar
	 */
	public java.lang.Integer getFaltaDeAr () {
		return getPropertyValue(this, faltaDeAr, PROP_FALTA_DE_AR); 
	}

	/**
	 * Set the value related to the column: falta_de_ar
	 * @param faltaDeAr the falta_de_ar value
	 */
	public void setFaltaDeAr (java.lang.Integer faltaDeAr) {
//        java.lang.Integer faltaDeArOld = this.faltaDeAr;
		this.faltaDeAr = faltaDeAr;
//        this.getPropertyChangeSupport().firePropertyChange ("faltaDeAr", faltaDeArOld, faltaDeAr);
	}



	/**
	 * Return the value associated with the column: congestao_nasal
	 */
	public java.lang.Integer getCongestaoNasal () {
		return getPropertyValue(this, congestaoNasal, PROP_CONGESTAO_NASAL); 
	}

	/**
	 * Set the value related to the column: congestao_nasal
	 * @param congestaoNasal the congestao_nasal value
	 */
	public void setCongestaoNasal (java.lang.Integer congestaoNasal) {
//        java.lang.Integer congestaoNasalOld = this.congestaoNasal;
		this.congestaoNasal = congestaoNasal;
//        this.getPropertyChangeSupport().firePropertyChange ("congestaoNasal", congestaoNasalOld, congestaoNasal);
	}



	/**
	 * Return the value associated with the column: nariz_escorrendo
	 */
	public java.lang.Integer getNarizEscorrendo () {
		return getPropertyValue(this, narizEscorrendo, PROP_NARIZ_ESCORRENDO); 
	}

	/**
	 * Set the value related to the column: nariz_escorrendo
	 * @param narizEscorrendo the nariz_escorrendo value
	 */
	public void setNarizEscorrendo (java.lang.Integer narizEscorrendo) {
//        java.lang.Integer narizEscorrendoOld = this.narizEscorrendo;
		this.narizEscorrendo = narizEscorrendo;
//        this.getPropertyChangeSupport().firePropertyChange ("narizEscorrendo", narizEscorrendoOld, narizEscorrendo);
	}



	/**
	 * Return the value associated with the column: dor_de_garganta
	 */
	public java.lang.Integer getDorDeGarganta () {
		return getPropertyValue(this, dorDeGarganta, PROP_DOR_DE_GARGANTA); 
	}

	/**
	 * Set the value related to the column: dor_de_garganta
	 * @param dorDeGarganta the dor_de_garganta value
	 */
	public void setDorDeGarganta (java.lang.Integer dorDeGarganta) {
//        java.lang.Integer dorDeGargantaOld = this.dorDeGarganta;
		this.dorDeGarganta = dorDeGarganta;
//        this.getPropertyChangeSupport().firePropertyChange ("dorDeGarganta", dorDeGargantaOld, dorDeGarganta);
	}



	/**
	 * Return the value associated with the column: aceite_termo
	 */
	public java.lang.Integer getAceiteTermo () {
		return getPropertyValue(this, aceiteTermo, PROP_ACEITE_TERMO); 
	}

	/**
	 * Set the value related to the column: aceite_termo
	 * @param aceiteTermo the aceite_termo value
	 */
	public void setAceiteTermo (java.lang.Integer aceiteTermo) {
//        java.lang.Integer aceiteTermoOld = this.aceiteTermo;
		this.aceiteTermo = aceiteTermo;
//        this.getPropertyChangeSupport().firePropertyChange ("aceiteTermo", aceiteTermoOld, aceiteTermo);
	}



	/**
	 * Return the value associated with the column: dt_inicio_sintomas
	 */
	public java.util.Date getDataInicioSintomas () {
		return getPropertyValue(this, dataInicioSintomas, PROP_DATA_INICIO_SINTOMAS); 
	}

	/**
	 * Set the value related to the column: dt_inicio_sintomas
	 * @param dataInicioSintomas the dt_inicio_sintomas value
	 */
	public void setDataInicioSintomas (java.util.Date dataInicioSintomas) {
//        java.util.Date dataInicioSintomasOld = this.dataInicioSintomas;
		this.dataInicioSintomas = dataInicioSintomas;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInicioSintomas", dataInicioSintomasOld, dataInicioSintomas);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: uuid_qr_code
	 */
	public java.lang.String getUuidQrCode () {
		return getPropertyValue(this, uuidQrCode, PROP_UUID_QR_CODE); 
	}

	/**
	 * Set the value related to the column: uuid_qr_code
	 * @param uuidQrCode the uuid_qr_code value
	 */
	public void setUuidQrCode (java.lang.String uuidQrCode) {
//        java.lang.String uuidQrCodeOld = this.uuidQrCode;
		this.uuidQrCode = uuidQrCode;
//        this.getPropertyChangeSupport().firePropertyChange ("uuidQrCode", uuidQrCodeOld, uuidQrCode);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.forcatarefa.FichaForcaTarefaCovid19)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.forcatarefa.FichaForcaTarefaCovid19 fichaForcaTarefaCovid19 = (br.com.ksisolucoes.vo.vigilancia.forcatarefa.FichaForcaTarefaCovid19) obj;
			if (null == this.getCodigo() || null == fichaForcaTarefaCovid19.getCodigo()) return false;
			else return (this.getCodigo().equals(fichaForcaTarefaCovid19.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}