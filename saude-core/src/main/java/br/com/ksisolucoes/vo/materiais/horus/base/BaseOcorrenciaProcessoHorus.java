package br.com.ksisolucoes.vo.materiais.horus.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the ocorrencia_processo_horus table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="ocorrencia_processo_horus"
 */

public abstract class BaseOcorrenciaProcessoHorus extends BaseRootVO implements Serializable {

	public static String REF = "OcorrenciaProcessoHorus";
	public static final String PROP_TIPO = "tipo";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_REGISTRO_ORIGEM = "registroOrigem";
	public static final String PROP_PRODUTO = "produto";
	public static final String PROP_DATA_OCORRENCIA = "dataOcorrencia";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_SITUACAO = "situacao";
	public static final String PROP_SINCRONIZACAO_HORUS_PROCESSO = "sincronizacaoHorusProcesso";


	// constructors
	public BaseOcorrenciaProcessoHorus () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseOcorrenciaProcessoHorus (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseOcorrenciaProcessoHorus (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso sincronizacaoHorusProcesso,
		br.com.ksisolucoes.vo.entradas.estoque.Produto produto,
		java.lang.Long registroOrigem,
		java.lang.Long tipo,
		java.lang.Long situacao) {

		this.setCodigo(codigo);
		this.setSincronizacaoHorusProcesso(sincronizacaoHorusProcesso);
		this.setProduto(produto);
		this.setRegistroOrigem(registroOrigem);
		this.setTipo(tipo);
		this.setSituacao(situacao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long registroOrigem;
	private java.lang.Long tipo;
	private java.lang.Long situacao;
	private java.lang.String descricao;
	private java.util.Date dataOcorrencia;

	// many to one
	private br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso sincronizacaoHorusProcesso;
	private br.com.ksisolucoes.vo.entradas.estoque.Produto produto;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_ocorrencia_processo_horus"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_registro_origem
	 */
	public java.lang.Long getRegistroOrigem () {
		return getPropertyValue(this, registroOrigem, PROP_REGISTRO_ORIGEM); 
	}

	/**
	 * Set the value related to the column: cd_registro_origem
	 * @param registroOrigem the cd_registro_origem value
	 */
	public void setRegistroOrigem (java.lang.Long registroOrigem) {
//        java.lang.Long registroOrigemOld = this.registroOrigem;
		this.registroOrigem = registroOrigem;
//        this.getPropertyChangeSupport().firePropertyChange ("registroOrigem", registroOrigemOld, registroOrigem);
	}



	/**
	 * Return the value associated with the column: tipo
	 */
	public java.lang.Long getTipo () {
		return getPropertyValue(this, tipo, PROP_TIPO); 
	}

	/**
	 * Set the value related to the column: tipo
	 * @param tipo the tipo value
	 */
	public void setTipo (java.lang.Long tipo) {
//        java.lang.Long tipoOld = this.tipo;
		this.tipo = tipo;
//        this.getPropertyChangeSupport().firePropertyChange ("tipo", tipoOld, tipo);
	}



	/**
	 * Return the value associated with the column: situacao
	 */
	public java.lang.Long getSituacao () {
		return getPropertyValue(this, situacao, PROP_SITUACAO); 
	}

	/**
	 * Set the value related to the column: situacao
	 * @param situacao the situacao value
	 */
	public void setSituacao (java.lang.Long situacao) {
//        java.lang.Long situacaoOld = this.situacao;
		this.situacao = situacao;
//        this.getPropertyChangeSupport().firePropertyChange ("situacao", situacaoOld, situacao);
	}



	/**
	 * Return the value associated with the column: ds_ocorrencia
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_ocorrencia
	 * @param descricao the ds_ocorrencia value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: dt_ocorrencia
	 */
	public java.util.Date getDataOcorrencia () {
		return getPropertyValue(this, dataOcorrencia, PROP_DATA_OCORRENCIA); 
	}

	/**
	 * Set the value related to the column: dt_ocorrencia
	 * @param dataOcorrencia the dt_ocorrencia value
	 */
	public void setDataOcorrencia (java.util.Date dataOcorrencia) {
//        java.util.Date dataOcorrenciaOld = this.dataOcorrencia;
		this.dataOcorrencia = dataOcorrencia;
//        this.getPropertyChangeSupport().firePropertyChange ("dataOcorrencia", dataOcorrenciaOld, dataOcorrencia);
	}



	/**
	 * Return the value associated with the column: cd_horus_sincronizacao_proc
	 */
	public br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso getSincronizacaoHorusProcesso () {
		return getPropertyValue(this, sincronizacaoHorusProcesso, PROP_SINCRONIZACAO_HORUS_PROCESSO); 
	}

	/**
	 * Set the value related to the column: cd_horus_sincronizacao_proc
	 * @param sincronizacaoHorusProcesso the cd_horus_sincronizacao_proc value
	 */
	public void setSincronizacaoHorusProcesso (br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso sincronizacaoHorusProcesso) {
//        br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso sincronizacaoHorusProcessoOld = this.sincronizacaoHorusProcesso;
		this.sincronizacaoHorusProcesso = sincronizacaoHorusProcesso;
//        this.getPropertyChangeSupport().firePropertyChange ("sincronizacaoHorusProcesso", sincronizacaoHorusProcessoOld, sincronizacaoHorusProcesso);
	}



	/**
	 * Return the value associated with the column: cod_pro
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Produto getProduto () {
		return getPropertyValue(this, produto, PROP_PRODUTO); 
	}

	/**
	 * Set the value related to the column: cod_pro
	 * @param produto the cod_pro value
	 */
	public void setProduto (br.com.ksisolucoes.vo.entradas.estoque.Produto produto) {
//        br.com.ksisolucoes.vo.entradas.estoque.Produto produtoOld = this.produto;
		this.produto = produto;
//        this.getPropertyChangeSupport().firePropertyChange ("produto", produtoOld, produto);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.materiais.horus.OcorrenciaProcessoHorus)) return false;
		else {
			br.com.ksisolucoes.vo.materiais.horus.OcorrenciaProcessoHorus ocorrenciaProcessoHorus = (br.com.ksisolucoes.vo.materiais.horus.OcorrenciaProcessoHorus) obj;
			if (null == this.getCodigo() || null == ocorrenciaProcessoHorus.getCodigo()) return false;
			else return (this.getCodigo().equals(ocorrenciaProcessoHorus.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}