package br.com.ksisolucoes.vo.vigilancia.investigacao.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the investigacao_agr_doenca_creutzfeldt_jacob table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agr_doenca_creutzfeldt_jacob"
 */

public abstract class BaseInvestigacaoAgravoDoencaCreutzfeldtJacob extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravoDoencaCreutzfeldtJacob";
	public static final String PROP_DATA_INVESTIGACAO = "dataInvestigacao";
	public static final String PROP_SINAL_SINTOMA_ALTERACOES_SONO = "sinalSintomaAlteracoesSono";
	public static final String PROP_EXPOSICAO_IANTROGENICA_DURA_MATER = "exposicaoIantrogenicaDuraMater";
	public static final String PROP_RESULTADO_IMUNOHISTOQUIMICA_PROTEINA_PRIONICA = "resultadoImunohistoquimicaProteinaPrionica";
	public static final String PROP_EXPOSICAO_IANTROGENICA_HORMONIO_CRESCIMENTO = "exposicaoIantrogenicaHormonioCrescimento";
	public static final String PROP_USUARIO_ENCERRAMENTO = "usuarioEncerramento";
	public static final String PROP_RESULTADO_BIOPSIA_CEREBRAL = "resultadoBiopsiaCerebral";
	public static final String PROP_RESULTADO_EEG = "resultadoEeg";
	public static final String PROP_FLAG_INFORMACOES_COMPLEMENTARES = "flagInformacoesComplementares";
	public static final String PROP_CRITERIOS_SUSPEITA_CLINICA = "criteriosSuspeitaClinica";
	public static final String PROP_FAMILIAR_APRESENTOU_QUADRO_SEMELHANTE = "familiarApresentouQuadroSemelhante";
	public static final String PROP_EXPOSICAO_IANTROGENICA_TRANSPLANTE_CORNEAS = "exposicaoIantrogenicaTransplanteCorneas";
	public static final String PROP_DATA_ENCERRAMENTO = "dataEncerramento";
	public static final String PROP_SINAL_SINTOMA_SINAIS_EXTRAPIRAMIDAIS = "sinalSintomaSinaisExtrapiramidais";
	public static final String PROP_CONCLUSAO_EVOLUCAO_CASO = "conclusaoEvolucaoCaso";
	public static final String PROP_CONCLUSAO_FORMA_CLINICA = "conclusaoFormaClinica";
	public static final String PROP_RESULTADO_ANALISE_GENETICA = "resultadoAnaliseGenetica";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_DATA_VIAGEM_EXTERIOR = "dataViagemExterior";
	public static final String PROP_VIAGEM_EXTERIOR_PAIS = "viagemExteriorPais";
	public static final String PROP_SINAL_SINTOMA_DEMENCIA_PROGRESSIVA = "sinalSintomaDemenciaProgressiva";
	public static final String PROP_EXPOSICAO_IANTROGENICA_NEUROCIRURGIAS = "exposicaoIantrogenicaNeurocirurgias";
	public static final String PROP_PACIENTE_VEGETARIANO = "pacienteVegetariano";
	public static final String PROP_CONCLUSAO_DIAGNOSTICO_FINAL_CID = "conclusaoDiagnosticoFinalCid";
	public static final String PROP_PACIENTE_COME_CARNE_BOVINA = "pacienteComeCarneBovina";
	public static final String PROP_EXPOSICAO_IANTROGENICA_TRANSFUSAO_SANGUE = "exposicaoIantrogenicaTransfusaoSangue";
	public static final String PROP_SINAL_SINTOMA_DISESTESIAS_DOLOROSAS_PERSISTENTES = "sinalSintomaDisestesiasDolorosasPersistentes";
	public static final String PROP_RESULTADO_PROTEINA_LCR = "resultadoProteinaLcr";
	public static final String PROP_SINAL_SINTOMA_ATAXIA = "sinalSintomaAtaxia";
	public static final String PROP_SINAL_SINTOMA_SINAIS_PIRAMIDAIS = "sinalSintomaSinaisPiramidais";
	public static final String PROP_RESULTADO_PROTEINA_TAU_LCR = "resultadoProteinaTauLcr";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_RESULTADO_RESSONANCIA_MAGNETICA = "resultadoRessonanciaMagnetica";
	public static final String PROP_RESULTADO_NECROPSIA_CEREBRAL = "resultadoNecropsiaCerebral";
	public static final String PROP_REGISTRO_AGRAVO = "registroAgravo";
	public static final String PROP_SINAL_SINTOMA_TRANSTORNOS_PSIQUIATRICOS = "sinalSintomaTranstornosPsiquiatricos";
	public static final String PROP_OCUPACAO_CBO = "ocupacaoCbo";
	public static final String PROP_CONCLUSAO_DATA_OBITO = "conclusaoDataObito";
	public static final String PROP_SINAL_SINTOMA_MIOCLONIAS = "sinalSintomaMioclonias";
	public static final String PROP_SINAL_SINTOMA_MUTISMO_ACINETICO = "sinalSintomaMutismoAcinetico";
	public static final String PROP_SINAL_SINTOMA_DISTURBIOS_CEREBELARES = "sinalSintomaDisturbiosCerebelares";
	public static final String PROP_SINAL_SINTOMA_DISTURBIOS_VISUAIS = "sinalSintomaDisturbiosVisuais";
	public static final String PROP_VIAGEM_EXTERIOR = "viagemExterior";


	// constructors
	public BaseInvestigacaoAgravoDoencaCreutzfeldtJacob () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravoDoencaCreutzfeldtJacob (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseInvestigacaoAgravoDoencaCreutzfeldtJacob (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
		java.lang.String flagInformacoesComplementares) {

		this.setCodigo(codigo);
		this.setRegistroAgravo(registroAgravo);
		this.setFlagInformacoesComplementares(flagInformacoesComplementares);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String flagInformacoesComplementares;
	private java.util.Date dataInvestigacao;
	private java.lang.Long criteriosSuspeitaClinica;
	private java.lang.Long sinalSintomaDemenciaProgressiva;
	private java.lang.Long sinalSintomaMioclonias;
	private java.lang.Long sinalSintomaDisturbiosVisuais;
	private java.lang.Long sinalSintomaDisturbiosCerebelares;
	private java.lang.Long sinalSintomaDisestesiasDolorosasPersistentes;
	private java.lang.Long sinalSintomaAtaxia;
	private java.lang.Long sinalSintomaSinaisPiramidais;
	private java.lang.Long sinalSintomaSinaisExtrapiramidais;
	private java.lang.Long sinalSintomaMutismoAcinetico;
	private java.lang.Long sinalSintomaTranstornosPsiquiatricos;
	private java.lang.Long sinalSintomaAlteracoesSono;
	private java.lang.Long viagemExterior;
	private java.util.Date dataViagemExterior;
	private java.lang.String viagemExteriorPais;
	private java.lang.Long familiarApresentouQuadroSemelhante;
	private java.lang.Long pacienteComeCarneBovina;
	private java.lang.Long pacienteVegetariano;
	private java.lang.Long exposicaoIantrogenicaDuraMater;
	private java.lang.Long exposicaoIantrogenicaHormonioCrescimento;
	private java.lang.Long exposicaoIantrogenicaTransplanteCorneas;
	private java.lang.Long exposicaoIantrogenicaNeurocirurgias;
	private java.lang.Long exposicaoIantrogenicaTransfusaoSangue;
	private java.lang.Long resultadoEeg;
	private java.lang.Long resultadoRessonanciaMagnetica;
	private java.lang.Long resultadoProteinaLcr;
	private java.lang.Long resultadoProteinaTauLcr;
	private java.lang.Long resultadoBiopsiaCerebral;
	private java.lang.Long resultadoNecropsiaCerebral;
	private java.lang.Long resultadoImunohistoquimicaProteinaPrionica;
	private java.lang.Long resultadoAnaliseGenetica;
	private java.lang.Long conclusaoFormaClinica;
	private java.lang.Long conclusaoEvolucaoCaso;
	private java.util.Date conclusaoDataObito;
	private java.lang.String observacao;
	private java.util.Date dataEncerramento;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo;
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo;
	private br.com.ksisolucoes.vo.prontuario.basico.Cid conclusaoDiagnosticoFinalCid;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_doenca_creutzfeldt_jacob"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: flag_informacoes_complementares
	 */
	public java.lang.String getFlagInformacoesComplementares () {
		return getPropertyValue(this, flagInformacoesComplementares, PROP_FLAG_INFORMACOES_COMPLEMENTARES); 
	}

	/**
	 * Set the value related to the column: flag_informacoes_complementares
	 * @param flagInformacoesComplementares the flag_informacoes_complementares value
	 */
	public void setFlagInformacoesComplementares (java.lang.String flagInformacoesComplementares) {
//        java.lang.String flagInformacoesComplementaresOld = this.flagInformacoesComplementares;
		this.flagInformacoesComplementares = flagInformacoesComplementares;
//        this.getPropertyChangeSupport().firePropertyChange ("flagInformacoesComplementares", flagInformacoesComplementaresOld, flagInformacoesComplementares);
	}



	/**
	 * Return the value associated with the column: dt_investigacao
	 */
	public java.util.Date getDataInvestigacao () {
		return getPropertyValue(this, dataInvestigacao, PROP_DATA_INVESTIGACAO); 
	}

	/**
	 * Set the value related to the column: dt_investigacao
	 * @param dataInvestigacao the dt_investigacao value
	 */
	public void setDataInvestigacao (java.util.Date dataInvestigacao) {
//        java.util.Date dataInvestigacaoOld = this.dataInvestigacao;
		this.dataInvestigacao = dataInvestigacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInvestigacao", dataInvestigacaoOld, dataInvestigacao);
	}



	/**
	 * Return the value associated with the column: criterios_suspeita_clinica
	 */
	public java.lang.Long getCriteriosSuspeitaClinica () {
		return getPropertyValue(this, criteriosSuspeitaClinica, PROP_CRITERIOS_SUSPEITA_CLINICA); 
	}

	/**
	 * Set the value related to the column: criterios_suspeita_clinica
	 * @param criteriosSuspeitaClinica the criterios_suspeita_clinica value
	 */
	public void setCriteriosSuspeitaClinica (java.lang.Long criteriosSuspeitaClinica) {
//        java.lang.Long criteriosSuspeitaClinicaOld = this.criteriosSuspeitaClinica;
		this.criteriosSuspeitaClinica = criteriosSuspeitaClinica;
//        this.getPropertyChangeSupport().firePropertyChange ("criteriosSuspeitaClinica", criteriosSuspeitaClinicaOld, criteriosSuspeitaClinica);
	}



	/**
	 * Return the value associated with the column: sinal_sintoma_demencia_progressiva
	 */
	public java.lang.Long getSinalSintomaDemenciaProgressiva () {
		return getPropertyValue(this, sinalSintomaDemenciaProgressiva, PROP_SINAL_SINTOMA_DEMENCIA_PROGRESSIVA); 
	}

	/**
	 * Set the value related to the column: sinal_sintoma_demencia_progressiva
	 * @param sinalSintomaDemenciaProgressiva the sinal_sintoma_demencia_progressiva value
	 */
	public void setSinalSintomaDemenciaProgressiva (java.lang.Long sinalSintomaDemenciaProgressiva) {
//        java.lang.Long sinalSintomaDemenciaProgressivaOld = this.sinalSintomaDemenciaProgressiva;
		this.sinalSintomaDemenciaProgressiva = sinalSintomaDemenciaProgressiva;
//        this.getPropertyChangeSupport().firePropertyChange ("sinalSintomaDemenciaProgressiva", sinalSintomaDemenciaProgressivaOld, sinalSintomaDemenciaProgressiva);
	}



	/**
	 * Return the value associated with the column: sinal_sintoma_mioclonias
	 */
	public java.lang.Long getSinalSintomaMioclonias () {
		return getPropertyValue(this, sinalSintomaMioclonias, PROP_SINAL_SINTOMA_MIOCLONIAS); 
	}

	/**
	 * Set the value related to the column: sinal_sintoma_mioclonias
	 * @param sinalSintomaMioclonias the sinal_sintoma_mioclonias value
	 */
	public void setSinalSintomaMioclonias (java.lang.Long sinalSintomaMioclonias) {
//        java.lang.Long sinalSintomaMiocloniasOld = this.sinalSintomaMioclonias;
		this.sinalSintomaMioclonias = sinalSintomaMioclonias;
//        this.getPropertyChangeSupport().firePropertyChange ("sinalSintomaMioclonias", sinalSintomaMiocloniasOld, sinalSintomaMioclonias);
	}



	/**
	 * Return the value associated with the column: sinal_sintoma_disturbios_visuais
	 */
	public java.lang.Long getSinalSintomaDisturbiosVisuais () {
		return getPropertyValue(this, sinalSintomaDisturbiosVisuais, PROP_SINAL_SINTOMA_DISTURBIOS_VISUAIS); 
	}

	/**
	 * Set the value related to the column: sinal_sintoma_disturbios_visuais
	 * @param sinalSintomaDisturbiosVisuais the sinal_sintoma_disturbios_visuais value
	 */
	public void setSinalSintomaDisturbiosVisuais (java.lang.Long sinalSintomaDisturbiosVisuais) {
//        java.lang.Long sinalSintomaDisturbiosVisuaisOld = this.sinalSintomaDisturbiosVisuais;
		this.sinalSintomaDisturbiosVisuais = sinalSintomaDisturbiosVisuais;
//        this.getPropertyChangeSupport().firePropertyChange ("sinalSintomaDisturbiosVisuais", sinalSintomaDisturbiosVisuaisOld, sinalSintomaDisturbiosVisuais);
	}



	/**
	 * Return the value associated with the column: sinal_sintoma_disturbios_cerebelares
	 */
	public java.lang.Long getSinalSintomaDisturbiosCerebelares () {
		return getPropertyValue(this, sinalSintomaDisturbiosCerebelares, PROP_SINAL_SINTOMA_DISTURBIOS_CEREBELARES); 
	}

	/**
	 * Set the value related to the column: sinal_sintoma_disturbios_cerebelares
	 * @param sinalSintomaDisturbiosCerebelares the sinal_sintoma_disturbios_cerebelares value
	 */
	public void setSinalSintomaDisturbiosCerebelares (java.lang.Long sinalSintomaDisturbiosCerebelares) {
//        java.lang.Long sinalSintomaDisturbiosCerebelaresOld = this.sinalSintomaDisturbiosCerebelares;
		this.sinalSintomaDisturbiosCerebelares = sinalSintomaDisturbiosCerebelares;
//        this.getPropertyChangeSupport().firePropertyChange ("sinalSintomaDisturbiosCerebelares", sinalSintomaDisturbiosCerebelaresOld, sinalSintomaDisturbiosCerebelares);
	}



	/**
	 * Return the value associated with the column: sinal_sintoma_disestesias_dolorosas_persistentes
	 */
	public java.lang.Long getSinalSintomaDisestesiasDolorosasPersistentes () {
		return getPropertyValue(this, sinalSintomaDisestesiasDolorosasPersistentes, PROP_SINAL_SINTOMA_DISESTESIAS_DOLOROSAS_PERSISTENTES); 
	}

	/**
	 * Set the value related to the column: sinal_sintoma_disestesias_dolorosas_persistentes
	 * @param sinalSintomaDisestesiasDolorosasPersistentes the sinal_sintoma_disestesias_dolorosas_persistentes value
	 */
	public void setSinalSintomaDisestesiasDolorosasPersistentes (java.lang.Long sinalSintomaDisestesiasDolorosasPersistentes) {
//        java.lang.Long sinalSintomaDisestesiasDolorosasPersistentesOld = this.sinalSintomaDisestesiasDolorosasPersistentes;
		this.sinalSintomaDisestesiasDolorosasPersistentes = sinalSintomaDisestesiasDolorosasPersistentes;
//        this.getPropertyChangeSupport().firePropertyChange ("sinalSintomaDisestesiasDolorosasPersistentes", sinalSintomaDisestesiasDolorosasPersistentesOld, sinalSintomaDisestesiasDolorosasPersistentes);
	}



	/**
	 * Return the value associated with the column: sinal_sintoma_ataxia
	 */
	public java.lang.Long getSinalSintomaAtaxia () {
		return getPropertyValue(this, sinalSintomaAtaxia, PROP_SINAL_SINTOMA_ATAXIA); 
	}

	/**
	 * Set the value related to the column: sinal_sintoma_ataxia
	 * @param sinalSintomaAtaxia the sinal_sintoma_ataxia value
	 */
	public void setSinalSintomaAtaxia (java.lang.Long sinalSintomaAtaxia) {
//        java.lang.Long sinalSintomaAtaxiaOld = this.sinalSintomaAtaxia;
		this.sinalSintomaAtaxia = sinalSintomaAtaxia;
//        this.getPropertyChangeSupport().firePropertyChange ("sinalSintomaAtaxia", sinalSintomaAtaxiaOld, sinalSintomaAtaxia);
	}



	/**
	 * Return the value associated with the column: sinal_sintoma_sinais_piramidais
	 */
	public java.lang.Long getSinalSintomaSinaisPiramidais () {
		return getPropertyValue(this, sinalSintomaSinaisPiramidais, PROP_SINAL_SINTOMA_SINAIS_PIRAMIDAIS); 
	}

	/**
	 * Set the value related to the column: sinal_sintoma_sinais_piramidais
	 * @param sinalSintomaSinaisPiramidais the sinal_sintoma_sinais_piramidais value
	 */
	public void setSinalSintomaSinaisPiramidais (java.lang.Long sinalSintomaSinaisPiramidais) {
//        java.lang.Long sinalSintomaSinaisPiramidaisOld = this.sinalSintomaSinaisPiramidais;
		this.sinalSintomaSinaisPiramidais = sinalSintomaSinaisPiramidais;
//        this.getPropertyChangeSupport().firePropertyChange ("sinalSintomaSinaisPiramidais", sinalSintomaSinaisPiramidaisOld, sinalSintomaSinaisPiramidais);
	}



	/**
	 * Return the value associated with the column: sinal_sintoma_sinais_extrapiramidais
	 */
	public java.lang.Long getSinalSintomaSinaisExtrapiramidais () {
		return getPropertyValue(this, sinalSintomaSinaisExtrapiramidais, PROP_SINAL_SINTOMA_SINAIS_EXTRAPIRAMIDAIS); 
	}

	/**
	 * Set the value related to the column: sinal_sintoma_sinais_extrapiramidais
	 * @param sinalSintomaSinaisExtrapiramidais the sinal_sintoma_sinais_extrapiramidais value
	 */
	public void setSinalSintomaSinaisExtrapiramidais (java.lang.Long sinalSintomaSinaisExtrapiramidais) {
//        java.lang.Long sinalSintomaSinaisExtrapiramidaisOld = this.sinalSintomaSinaisExtrapiramidais;
		this.sinalSintomaSinaisExtrapiramidais = sinalSintomaSinaisExtrapiramidais;
//        this.getPropertyChangeSupport().firePropertyChange ("sinalSintomaSinaisExtrapiramidais", sinalSintomaSinaisExtrapiramidaisOld, sinalSintomaSinaisExtrapiramidais);
	}



	/**
	 * Return the value associated with the column: sinal_sintoma_mutismo_acinetico
	 */
	public java.lang.Long getSinalSintomaMutismoAcinetico () {
		return getPropertyValue(this, sinalSintomaMutismoAcinetico, PROP_SINAL_SINTOMA_MUTISMO_ACINETICO); 
	}

	/**
	 * Set the value related to the column: sinal_sintoma_mutismo_acinetico
	 * @param sinalSintomaMutismoAcinetico the sinal_sintoma_mutismo_acinetico value
	 */
	public void setSinalSintomaMutismoAcinetico (java.lang.Long sinalSintomaMutismoAcinetico) {
//        java.lang.Long sinalSintomaMutismoAcineticoOld = this.sinalSintomaMutismoAcinetico;
		this.sinalSintomaMutismoAcinetico = sinalSintomaMutismoAcinetico;
//        this.getPropertyChangeSupport().firePropertyChange ("sinalSintomaMutismoAcinetico", sinalSintomaMutismoAcineticoOld, sinalSintomaMutismoAcinetico);
	}



	/**
	 * Return the value associated with the column: sinal_sintoma_transtornos_psiquiatricos
	 */
	public java.lang.Long getSinalSintomaTranstornosPsiquiatricos () {
		return getPropertyValue(this, sinalSintomaTranstornosPsiquiatricos, PROP_SINAL_SINTOMA_TRANSTORNOS_PSIQUIATRICOS); 
	}

	/**
	 * Set the value related to the column: sinal_sintoma_transtornos_psiquiatricos
	 * @param sinalSintomaTranstornosPsiquiatricos the sinal_sintoma_transtornos_psiquiatricos value
	 */
	public void setSinalSintomaTranstornosPsiquiatricos (java.lang.Long sinalSintomaTranstornosPsiquiatricos) {
//        java.lang.Long sinalSintomaTranstornosPsiquiatricosOld = this.sinalSintomaTranstornosPsiquiatricos;
		this.sinalSintomaTranstornosPsiquiatricos = sinalSintomaTranstornosPsiquiatricos;
//        this.getPropertyChangeSupport().firePropertyChange ("sinalSintomaTranstornosPsiquiatricos", sinalSintomaTranstornosPsiquiatricosOld, sinalSintomaTranstornosPsiquiatricos);
	}



	/**
	 * Return the value associated with the column: sinal_sintoma_alteracoes_sono
	 */
	public java.lang.Long getSinalSintomaAlteracoesSono () {
		return getPropertyValue(this, sinalSintomaAlteracoesSono, PROP_SINAL_SINTOMA_ALTERACOES_SONO); 
	}

	/**
	 * Set the value related to the column: sinal_sintoma_alteracoes_sono
	 * @param sinalSintomaAlteracoesSono the sinal_sintoma_alteracoes_sono value
	 */
	public void setSinalSintomaAlteracoesSono (java.lang.Long sinalSintomaAlteracoesSono) {
//        java.lang.Long sinalSintomaAlteracoesSonoOld = this.sinalSintomaAlteracoesSono;
		this.sinalSintomaAlteracoesSono = sinalSintomaAlteracoesSono;
//        this.getPropertyChangeSupport().firePropertyChange ("sinalSintomaAlteracoesSono", sinalSintomaAlteracoesSonoOld, sinalSintomaAlteracoesSono);
	}



	/**
	 * Return the value associated with the column: viagem_exterior
	 */
	public java.lang.Long getViagemExterior () {
		return getPropertyValue(this, viagemExterior, PROP_VIAGEM_EXTERIOR); 
	}

	/**
	 * Set the value related to the column: viagem_exterior
	 * @param viagemExterior the viagem_exterior value
	 */
	public void setViagemExterior (java.lang.Long viagemExterior) {
//        java.lang.Long viagemExteriorOld = this.viagemExterior;
		this.viagemExterior = viagemExterior;
//        this.getPropertyChangeSupport().firePropertyChange ("viagemExterior", viagemExteriorOld, viagemExterior);
	}



	/**
	 * Return the value associated with the column: dt_viagem_exterior
	 */
	public java.util.Date getDataViagemExterior () {
		return getPropertyValue(this, dataViagemExterior, PROP_DATA_VIAGEM_EXTERIOR); 
	}

	/**
	 * Set the value related to the column: dt_viagem_exterior
	 * @param dataViagemExterior the dt_viagem_exterior value
	 */
	public void setDataViagemExterior (java.util.Date dataViagemExterior) {
//        java.util.Date dataViagemExteriorOld = this.dataViagemExterior;
		this.dataViagemExterior = dataViagemExterior;
//        this.getPropertyChangeSupport().firePropertyChange ("dataViagemExterior", dataViagemExteriorOld, dataViagemExterior);
	}



	/**
	 * Return the value associated with the column: viagem_exterior_pais
	 */
	public java.lang.String getViagemExteriorPais () {
		return getPropertyValue(this, viagemExteriorPais, PROP_VIAGEM_EXTERIOR_PAIS); 
	}

	/**
	 * Set the value related to the column: viagem_exterior_pais
	 * @param viagemExteriorPais the viagem_exterior_pais value
	 */
	public void setViagemExteriorPais (java.lang.String viagemExteriorPais) {
//        java.lang.String viagemExteriorPaisOld = this.viagemExteriorPais;
		this.viagemExteriorPais = viagemExteriorPais;
//        this.getPropertyChangeSupport().firePropertyChange ("viagemExteriorPais", viagemExteriorPaisOld, viagemExteriorPais);
	}



	/**
	 * Return the value associated with the column: familiar_apresentou_quadro_semelhante
	 */
	public java.lang.Long getFamiliarApresentouQuadroSemelhante () {
		return getPropertyValue(this, familiarApresentouQuadroSemelhante, PROP_FAMILIAR_APRESENTOU_QUADRO_SEMELHANTE); 
	}

	/**
	 * Set the value related to the column: familiar_apresentou_quadro_semelhante
	 * @param familiarApresentouQuadroSemelhante the familiar_apresentou_quadro_semelhante value
	 */
	public void setFamiliarApresentouQuadroSemelhante (java.lang.Long familiarApresentouQuadroSemelhante) {
//        java.lang.Long familiarApresentouQuadroSemelhanteOld = this.familiarApresentouQuadroSemelhante;
		this.familiarApresentouQuadroSemelhante = familiarApresentouQuadroSemelhante;
//        this.getPropertyChangeSupport().firePropertyChange ("familiarApresentouQuadroSemelhante", familiarApresentouQuadroSemelhanteOld, familiarApresentouQuadroSemelhante);
	}



	/**
	 * Return the value associated with the column: paciente_come_carne_bovina
	 */
	public java.lang.Long getPacienteComeCarneBovina () {
		return getPropertyValue(this, pacienteComeCarneBovina, PROP_PACIENTE_COME_CARNE_BOVINA); 
	}

	/**
	 * Set the value related to the column: paciente_come_carne_bovina
	 * @param pacienteComeCarneBovina the paciente_come_carne_bovina value
	 */
	public void setPacienteComeCarneBovina (java.lang.Long pacienteComeCarneBovina) {
//        java.lang.Long pacienteComeCarneBovinaOld = this.pacienteComeCarneBovina;
		this.pacienteComeCarneBovina = pacienteComeCarneBovina;
//        this.getPropertyChangeSupport().firePropertyChange ("pacienteComeCarneBovina", pacienteComeCarneBovinaOld, pacienteComeCarneBovina);
	}



	/**
	 * Return the value associated with the column: paciente_vegetariano
	 */
	public java.lang.Long getPacienteVegetariano () {
		return getPropertyValue(this, pacienteVegetariano, PROP_PACIENTE_VEGETARIANO); 
	}

	/**
	 * Set the value related to the column: paciente_vegetariano
	 * @param pacienteVegetariano the paciente_vegetariano value
	 */
	public void setPacienteVegetariano (java.lang.Long pacienteVegetariano) {
//        java.lang.Long pacienteVegetarianoOld = this.pacienteVegetariano;
		this.pacienteVegetariano = pacienteVegetariano;
//        this.getPropertyChangeSupport().firePropertyChange ("pacienteVegetariano", pacienteVegetarianoOld, pacienteVegetariano);
	}



	/**
	 * Return the value associated with the column: exposicao_iatrogenica_dura_mater
	 */
	public java.lang.Long getExposicaoIantrogenicaDuraMater () {
		return getPropertyValue(this, exposicaoIantrogenicaDuraMater, PROP_EXPOSICAO_IANTROGENICA_DURA_MATER); 
	}

	/**
	 * Set the value related to the column: exposicao_iatrogenica_dura_mater
	 * @param exposicaoIantrogenicaDuraMater the exposicao_iatrogenica_dura_mater value
	 */
	public void setExposicaoIantrogenicaDuraMater (java.lang.Long exposicaoIantrogenicaDuraMater) {
//        java.lang.Long exposicaoIantrogenicaDuraMaterOld = this.exposicaoIantrogenicaDuraMater;
		this.exposicaoIantrogenicaDuraMater = exposicaoIantrogenicaDuraMater;
//        this.getPropertyChangeSupport().firePropertyChange ("exposicaoIantrogenicaDuraMater", exposicaoIantrogenicaDuraMaterOld, exposicaoIantrogenicaDuraMater);
	}



	/**
	 * Return the value associated with the column: exposicao_iatrogenica_hormonio_crescimento
	 */
	public java.lang.Long getExposicaoIantrogenicaHormonioCrescimento () {
		return getPropertyValue(this, exposicaoIantrogenicaHormonioCrescimento, PROP_EXPOSICAO_IANTROGENICA_HORMONIO_CRESCIMENTO); 
	}

	/**
	 * Set the value related to the column: exposicao_iatrogenica_hormonio_crescimento
	 * @param exposicaoIantrogenicaHormonioCrescimento the exposicao_iatrogenica_hormonio_crescimento value
	 */
	public void setExposicaoIantrogenicaHormonioCrescimento (java.lang.Long exposicaoIantrogenicaHormonioCrescimento) {
//        java.lang.Long exposicaoIantrogenicaHormonioCrescimentoOld = this.exposicaoIantrogenicaHormonioCrescimento;
		this.exposicaoIantrogenicaHormonioCrescimento = exposicaoIantrogenicaHormonioCrescimento;
//        this.getPropertyChangeSupport().firePropertyChange ("exposicaoIantrogenicaHormonioCrescimento", exposicaoIantrogenicaHormonioCrescimentoOld, exposicaoIantrogenicaHormonioCrescimento);
	}



	/**
	 * Return the value associated with the column: exposicao_iatrogenica_transplante_corneas
	 */
	public java.lang.Long getExposicaoIantrogenicaTransplanteCorneas () {
		return getPropertyValue(this, exposicaoIantrogenicaTransplanteCorneas, PROP_EXPOSICAO_IANTROGENICA_TRANSPLANTE_CORNEAS); 
	}

	/**
	 * Set the value related to the column: exposicao_iatrogenica_transplante_corneas
	 * @param exposicaoIantrogenicaTransplanteCorneas the exposicao_iatrogenica_transplante_corneas value
	 */
	public void setExposicaoIantrogenicaTransplanteCorneas (java.lang.Long exposicaoIantrogenicaTransplanteCorneas) {
//        java.lang.Long exposicaoIantrogenicaTransplanteCorneasOld = this.exposicaoIantrogenicaTransplanteCorneas;
		this.exposicaoIantrogenicaTransplanteCorneas = exposicaoIantrogenicaTransplanteCorneas;
//        this.getPropertyChangeSupport().firePropertyChange ("exposicaoIantrogenicaTransplanteCorneas", exposicaoIantrogenicaTransplanteCorneasOld, exposicaoIantrogenicaTransplanteCorneas);
	}



	/**
	 * Return the value associated with the column: exposicao_iatrogenica_neurocirurgias
	 */
	public java.lang.Long getExposicaoIantrogenicaNeurocirurgias () {
		return getPropertyValue(this, exposicaoIantrogenicaNeurocirurgias, PROP_EXPOSICAO_IANTROGENICA_NEUROCIRURGIAS); 
	}

	/**
	 * Set the value related to the column: exposicao_iatrogenica_neurocirurgias
	 * @param exposicaoIantrogenicaNeurocirurgias the exposicao_iatrogenica_neurocirurgias value
	 */
	public void setExposicaoIantrogenicaNeurocirurgias (java.lang.Long exposicaoIantrogenicaNeurocirurgias) {
//        java.lang.Long exposicaoIantrogenicaNeurocirurgiasOld = this.exposicaoIantrogenicaNeurocirurgias;
		this.exposicaoIantrogenicaNeurocirurgias = exposicaoIantrogenicaNeurocirurgias;
//        this.getPropertyChangeSupport().firePropertyChange ("exposicaoIantrogenicaNeurocirurgias", exposicaoIantrogenicaNeurocirurgiasOld, exposicaoIantrogenicaNeurocirurgias);
	}



	/**
	 * Return the value associated with the column: exposicao_iatrogenica_transfusao_sangue
	 */
	public java.lang.Long getExposicaoIantrogenicaTransfusaoSangue () {
		return getPropertyValue(this, exposicaoIantrogenicaTransfusaoSangue, PROP_EXPOSICAO_IANTROGENICA_TRANSFUSAO_SANGUE); 
	}

	/**
	 * Set the value related to the column: exposicao_iatrogenica_transfusao_sangue
	 * @param exposicaoIantrogenicaTransfusaoSangue the exposicao_iatrogenica_transfusao_sangue value
	 */
	public void setExposicaoIantrogenicaTransfusaoSangue (java.lang.Long exposicaoIantrogenicaTransfusaoSangue) {
//        java.lang.Long exposicaoIantrogenicaTransfusaoSangueOld = this.exposicaoIantrogenicaTransfusaoSangue;
		this.exposicaoIantrogenicaTransfusaoSangue = exposicaoIantrogenicaTransfusaoSangue;
//        this.getPropertyChangeSupport().firePropertyChange ("exposicaoIantrogenicaTransfusaoSangue", exposicaoIantrogenicaTransfusaoSangueOld, exposicaoIantrogenicaTransfusaoSangue);
	}



	/**
	 * Return the value associated with the column: resultato_eeg
	 */
	public java.lang.Long getResultadoEeg () {
		return getPropertyValue(this, resultadoEeg, PROP_RESULTADO_EEG); 
	}

	/**
	 * Set the value related to the column: resultato_eeg
	 * @param resultadoEeg the resultato_eeg value
	 */
	public void setResultadoEeg (java.lang.Long resultadoEeg) {
//        java.lang.Long resultadoEegOld = this.resultadoEeg;
		this.resultadoEeg = resultadoEeg;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoEeg", resultadoEegOld, resultadoEeg);
	}



	/**
	 * Return the value associated with the column: resultado_ressonancia_magnetica
	 */
	public java.lang.Long getResultadoRessonanciaMagnetica () {
		return getPropertyValue(this, resultadoRessonanciaMagnetica, PROP_RESULTADO_RESSONANCIA_MAGNETICA); 
	}

	/**
	 * Set the value related to the column: resultado_ressonancia_magnetica
	 * @param resultadoRessonanciaMagnetica the resultado_ressonancia_magnetica value
	 */
	public void setResultadoRessonanciaMagnetica (java.lang.Long resultadoRessonanciaMagnetica) {
//        java.lang.Long resultadoRessonanciaMagneticaOld = this.resultadoRessonanciaMagnetica;
		this.resultadoRessonanciaMagnetica = resultadoRessonanciaMagnetica;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoRessonanciaMagnetica", resultadoRessonanciaMagneticaOld, resultadoRessonanciaMagnetica);
	}



	/**
	 * Return the value associated with the column: resultado_proteina_lcr
	 */
	public java.lang.Long getResultadoProteinaLcr () {
		return getPropertyValue(this, resultadoProteinaLcr, PROP_RESULTADO_PROTEINA_LCR); 
	}

	/**
	 * Set the value related to the column: resultado_proteina_lcr
	 * @param resultadoProteinaLcr the resultado_proteina_lcr value
	 */
	public void setResultadoProteinaLcr (java.lang.Long resultadoProteinaLcr) {
//        java.lang.Long resultadoProteinaLcrOld = this.resultadoProteinaLcr;
		this.resultadoProteinaLcr = resultadoProteinaLcr;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoProteinaLcr", resultadoProteinaLcrOld, resultadoProteinaLcr);
	}



	/**
	 * Return the value associated with the column: resultado_proteina_tau_lcr
	 */
	public java.lang.Long getResultadoProteinaTauLcr () {
		return getPropertyValue(this, resultadoProteinaTauLcr, PROP_RESULTADO_PROTEINA_TAU_LCR); 
	}

	/**
	 * Set the value related to the column: resultado_proteina_tau_lcr
	 * @param resultadoProteinaTauLcr the resultado_proteina_tau_lcr value
	 */
	public void setResultadoProteinaTauLcr (java.lang.Long resultadoProteinaTauLcr) {
//        java.lang.Long resultadoProteinaTauLcrOld = this.resultadoProteinaTauLcr;
		this.resultadoProteinaTauLcr = resultadoProteinaTauLcr;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoProteinaTauLcr", resultadoProteinaTauLcrOld, resultadoProteinaTauLcr);
	}



	/**
	 * Return the value associated with the column: resultado_biopsia_cerebral
	 */
	public java.lang.Long getResultadoBiopsiaCerebral () {
		return getPropertyValue(this, resultadoBiopsiaCerebral, PROP_RESULTADO_BIOPSIA_CEREBRAL); 
	}

	/**
	 * Set the value related to the column: resultado_biopsia_cerebral
	 * @param resultadoBiopsiaCerebral the resultado_biopsia_cerebral value
	 */
	public void setResultadoBiopsiaCerebral (java.lang.Long resultadoBiopsiaCerebral) {
//        java.lang.Long resultadoBiopsiaCerebralOld = this.resultadoBiopsiaCerebral;
		this.resultadoBiopsiaCerebral = resultadoBiopsiaCerebral;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoBiopsiaCerebral", resultadoBiopsiaCerebralOld, resultadoBiopsiaCerebral);
	}



	/**
	 * Return the value associated with the column: resultado_necropsia_cerebral
	 */
	public java.lang.Long getResultadoNecropsiaCerebral () {
		return getPropertyValue(this, resultadoNecropsiaCerebral, PROP_RESULTADO_NECROPSIA_CEREBRAL); 
	}

	/**
	 * Set the value related to the column: resultado_necropsia_cerebral
	 * @param resultadoNecropsiaCerebral the resultado_necropsia_cerebral value
	 */
	public void setResultadoNecropsiaCerebral (java.lang.Long resultadoNecropsiaCerebral) {
//        java.lang.Long resultadoNecropsiaCerebralOld = this.resultadoNecropsiaCerebral;
		this.resultadoNecropsiaCerebral = resultadoNecropsiaCerebral;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoNecropsiaCerebral", resultadoNecropsiaCerebralOld, resultadoNecropsiaCerebral);
	}



	/**
	 * Return the value associated with the column: resultado_imunohistoquimica_proteina_prionica
	 */
	public java.lang.Long getResultadoImunohistoquimicaProteinaPrionica () {
		return getPropertyValue(this, resultadoImunohistoquimicaProteinaPrionica, PROP_RESULTADO_IMUNOHISTOQUIMICA_PROTEINA_PRIONICA); 
	}

	/**
	 * Set the value related to the column: resultado_imunohistoquimica_proteina_prionica
	 * @param resultadoImunohistoquimicaProteinaPrionica the resultado_imunohistoquimica_proteina_prionica value
	 */
	public void setResultadoImunohistoquimicaProteinaPrionica (java.lang.Long resultadoImunohistoquimicaProteinaPrionica) {
//        java.lang.Long resultadoImunohistoquimicaProteinaPrionicaOld = this.resultadoImunohistoquimicaProteinaPrionica;
		this.resultadoImunohistoquimicaProteinaPrionica = resultadoImunohistoquimicaProteinaPrionica;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoImunohistoquimicaProteinaPrionica", resultadoImunohistoquimicaProteinaPrionicaOld, resultadoImunohistoquimicaProteinaPrionica);
	}



	/**
	 * Return the value associated with the column: resultado_analise_genetica
	 */
	public java.lang.Long getResultadoAnaliseGenetica () {
		return getPropertyValue(this, resultadoAnaliseGenetica, PROP_RESULTADO_ANALISE_GENETICA); 
	}

	/**
	 * Set the value related to the column: resultado_analise_genetica
	 * @param resultadoAnaliseGenetica the resultado_analise_genetica value
	 */
	public void setResultadoAnaliseGenetica (java.lang.Long resultadoAnaliseGenetica) {
//        java.lang.Long resultadoAnaliseGeneticaOld = this.resultadoAnaliseGenetica;
		this.resultadoAnaliseGenetica = resultadoAnaliseGenetica;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoAnaliseGenetica", resultadoAnaliseGeneticaOld, resultadoAnaliseGenetica);
	}



	/**
	 * Return the value associated with the column: conclusao_forma_clinica
	 */
	public java.lang.Long getConclusaoFormaClinica () {
		return getPropertyValue(this, conclusaoFormaClinica, PROP_CONCLUSAO_FORMA_CLINICA); 
	}

	/**
	 * Set the value related to the column: conclusao_forma_clinica
	 * @param conclusaoFormaClinica the conclusao_forma_clinica value
	 */
	public void setConclusaoFormaClinica (java.lang.Long conclusaoFormaClinica) {
//        java.lang.Long conclusaoFormaClinicaOld = this.conclusaoFormaClinica;
		this.conclusaoFormaClinica = conclusaoFormaClinica;
//        this.getPropertyChangeSupport().firePropertyChange ("conclusaoFormaClinica", conclusaoFormaClinicaOld, conclusaoFormaClinica);
	}



	/**
	 * Return the value associated with the column: conclusao_evolucao_caso
	 */
	public java.lang.Long getConclusaoEvolucaoCaso () {
		return getPropertyValue(this, conclusaoEvolucaoCaso, PROP_CONCLUSAO_EVOLUCAO_CASO); 
	}

	/**
	 * Set the value related to the column: conclusao_evolucao_caso
	 * @param conclusaoEvolucaoCaso the conclusao_evolucao_caso value
	 */
	public void setConclusaoEvolucaoCaso (java.lang.Long conclusaoEvolucaoCaso) {
//        java.lang.Long conclusaoEvolucaoCasoOld = this.conclusaoEvolucaoCaso;
		this.conclusaoEvolucaoCaso = conclusaoEvolucaoCaso;
//        this.getPropertyChangeSupport().firePropertyChange ("conclusaoEvolucaoCaso", conclusaoEvolucaoCasoOld, conclusaoEvolucaoCaso);
	}



	/**
	 * Return the value associated with the column: conclusao_dt_obito
	 */
	public java.util.Date getConclusaoDataObito () {
		return getPropertyValue(this, conclusaoDataObito, PROP_CONCLUSAO_DATA_OBITO); 
	}

	/**
	 * Set the value related to the column: conclusao_dt_obito
	 * @param conclusaoDataObito the conclusao_dt_obito value
	 */
	public void setConclusaoDataObito (java.util.Date conclusaoDataObito) {
//        java.util.Date conclusaoDataObitoOld = this.conclusaoDataObito;
		this.conclusaoDataObito = conclusaoDataObito;
//        this.getPropertyChangeSupport().firePropertyChange ("conclusaoDataObito", conclusaoDataObitoOld, conclusaoDataObito);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: dt_encerramento
	 */
	public java.util.Date getDataEncerramento () {
		return getPropertyValue(this, dataEncerramento, PROP_DATA_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_encerramento
	 * @param dataEncerramento the dt_encerramento value
	 */
	public void setDataEncerramento (java.util.Date dataEncerramento) {
//        java.util.Date dataEncerramentoOld = this.dataEncerramento;
		this.dataEncerramento = dataEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEncerramento", dataEncerramentoOld, dataEncerramento);
	}



	/**
	 * Return the value associated with the column: cd_registro_agravo
	 */
	public br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo getRegistroAgravo () {
		return getPropertyValue(this, registroAgravo, PROP_REGISTRO_AGRAVO); 
	}

	/**
	 * Set the value related to the column: cd_registro_agravo
	 * @param registroAgravo the cd_registro_agravo value
	 */
	public void setRegistroAgravo (br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo) {
//        br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravoOld = this.registroAgravo;
		this.registroAgravo = registroAgravo;
//        this.getPropertyChangeSupport().firePropertyChange ("registroAgravo", registroAgravoOld, registroAgravo);
	}



	/**
	 * Return the value associated with the column: ocupacao_cbo
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getOcupacaoCbo () {
		return getPropertyValue(this, ocupacaoCbo, PROP_OCUPACAO_CBO); 
	}

	/**
	 * Set the value related to the column: ocupacao_cbo
	 * @param ocupacaoCbo the ocupacao_cbo value
	 */
	public void setOcupacaoCbo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCboOld = this.ocupacaoCbo;
		this.ocupacaoCbo = ocupacaoCbo;
//        this.getPropertyChangeSupport().firePropertyChange ("ocupacaoCbo", ocupacaoCboOld, ocupacaoCbo);
	}



	/**
	 * Return the value associated with the column: conclusao_diagnostico_final_cid
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Cid getConclusaoDiagnosticoFinalCid () {
		return getPropertyValue(this, conclusaoDiagnosticoFinalCid, PROP_CONCLUSAO_DIAGNOSTICO_FINAL_CID); 
	}

	/**
	 * Set the value related to the column: conclusao_diagnostico_final_cid
	 * @param conclusaoDiagnosticoFinalCid the conclusao_diagnostico_final_cid value
	 */
	public void setConclusaoDiagnosticoFinalCid (br.com.ksisolucoes.vo.prontuario.basico.Cid conclusaoDiagnosticoFinalCid) {
//        br.com.ksisolucoes.vo.prontuario.basico.Cid conclusaoDiagnosticoFinalCidOld = this.conclusaoDiagnosticoFinalCid;
		this.conclusaoDiagnosticoFinalCid = conclusaoDiagnosticoFinalCid;
//        this.getPropertyChangeSupport().firePropertyChange ("conclusaoDiagnosticoFinalCid", conclusaoDiagnosticoFinalCidOld, conclusaoDiagnosticoFinalCid);
	}



	/**
	 * Return the value associated with the column: cd_usuario_encerramento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioEncerramento () {
		return getPropertyValue(this, usuarioEncerramento, PROP_USUARIO_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_encerramento
	 * @param usuarioEncerramento the cd_usuario_encerramento value
	 */
	public void setUsuarioEncerramento (br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramentoOld = this.usuarioEncerramento;
		this.usuarioEncerramento = usuarioEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioEncerramento", usuarioEncerramentoOld, usuarioEncerramento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaCreutzfeldtJacob)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaCreutzfeldtJacob investigacaoAgravoDoencaCreutzfeldtJacob = (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaCreutzfeldtJacob) obj;
			if (null == this.getCodigo() || null == investigacaoAgravoDoencaCreutzfeldtJacob.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravoDoencaCreutzfeldtJacob.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}