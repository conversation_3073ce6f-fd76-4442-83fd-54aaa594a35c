package br.com.ksisolucoes.vo.basico.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the cid_classificacao table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="cid_classificacao"
 */

public abstract class BaseClassificacaoCids extends BaseRootVO implements Serializable {

	public static String REF = "ClassificacaoCids";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_PRAZO_ENCERRAMENTO = "prazoEncerramento";
	public static final String PROP_CID_AGRUPADOR = "cidAgrupador";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_FICHA_INVESTIGACAO_AGRAVO = "fichaInvestigacaoAgravo";
	public static final String PROP_PERMITE_NOTIFICACAO_CONCOMITANTE = "permiteNotificacaoConcomitante";


	// constructors
	public BaseClassificacaoCids () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseClassificacaoCids (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseClassificacaoCids (
		java.lang.Long codigo,
		java.lang.String descricao,
		java.lang.Long permiteNotificacaoConcomitante) {

		this.setCodigo(codigo);
		this.setDescricao(descricao);
		this.setPermiteNotificacaoConcomitante(permiteNotificacaoConcomitante);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.lang.Long permiteNotificacaoConcomitante;
	private java.lang.Long prazoEncerramento;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.Cid cidAgrupador;
	private br.com.ksisolucoes.vo.basico.FichaInvestigacaoAgravo fichaInvestigacaoAgravo;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_classificacao"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: descricao
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: descricao
	 * @param descricao the descricao value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: flag_notificacao_concomitante
	 */
	public java.lang.Long getPermiteNotificacaoConcomitante () {
		return getPropertyValue(this, permiteNotificacaoConcomitante, PROP_PERMITE_NOTIFICACAO_CONCOMITANTE); 
	}

	/**
	 * Set the value related to the column: flag_notificacao_concomitante
	 * @param permiteNotificacaoConcomitante the flag_notificacao_concomitante value
	 */
	public void setPermiteNotificacaoConcomitante (java.lang.Long permiteNotificacaoConcomitante) {
//        java.lang.Long permiteNotificacaoConcomitanteOld = this.permiteNotificacaoConcomitante;
		this.permiteNotificacaoConcomitante = permiteNotificacaoConcomitante;
//        this.getPropertyChangeSupport().firePropertyChange ("permiteNotificacaoConcomitante", permiteNotificacaoConcomitanteOld, permiteNotificacaoConcomitante);
	}



	/**
	 * Return the value associated with the column: prazo_encerramento
	 */
	public java.lang.Long getPrazoEncerramento () {
		return getPropertyValue(this, prazoEncerramento, PROP_PRAZO_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: prazo_encerramento
	 * @param prazoEncerramento the prazo_encerramento value
	 */
	public void setPrazoEncerramento (java.lang.Long prazoEncerramento) {
//        java.lang.Long prazoEncerramentoOld = this.prazoEncerramento;
		this.prazoEncerramento = prazoEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("prazoEncerramento", prazoEncerramentoOld, prazoEncerramento);
	}



	/**
	 * Return the value associated with the column: cd_cid_agrupador
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Cid getCidAgrupador () {
		return getPropertyValue(this, cidAgrupador, PROP_CID_AGRUPADOR); 
	}

	/**
	 * Set the value related to the column: cd_cid_agrupador
	 * @param cidAgrupador the cd_cid_agrupador value
	 */
	public void setCidAgrupador (br.com.ksisolucoes.vo.prontuario.basico.Cid cidAgrupador) {
//        br.com.ksisolucoes.vo.prontuario.basico.Cid cidAgrupadorOld = this.cidAgrupador;
		this.cidAgrupador = cidAgrupador;
//        this.getPropertyChangeSupport().firePropertyChange ("cidAgrupador", cidAgrupadorOld, cidAgrupador);
	}



	/**
	 * Return the value associated with the column: cd_ficha_investigacao_agravo
	 */
	public br.com.ksisolucoes.vo.basico.FichaInvestigacaoAgravo getFichaInvestigacaoAgravo () {
		return getPropertyValue(this, fichaInvestigacaoAgravo, PROP_FICHA_INVESTIGACAO_AGRAVO); 
	}

	/**
	 * Set the value related to the column: cd_ficha_investigacao_agravo
	 * @param fichaInvestigacaoAgravo the cd_ficha_investigacao_agravo value
	 */
	public void setFichaInvestigacaoAgravo (br.com.ksisolucoes.vo.basico.FichaInvestigacaoAgravo fichaInvestigacaoAgravo) {
//        br.com.ksisolucoes.vo.basico.FichaInvestigacaoAgravo fichaInvestigacaoAgravoOld = this.fichaInvestigacaoAgravo;
		this.fichaInvestigacaoAgravo = fichaInvestigacaoAgravo;
//        this.getPropertyChangeSupport().firePropertyChange ("fichaInvestigacaoAgravo", fichaInvestigacaoAgravoOld, fichaInvestigacaoAgravo);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.ClassificacaoCids)) return false;
		else {
			br.com.ksisolucoes.vo.basico.ClassificacaoCids classificacaoCids = (br.com.ksisolucoes.vo.basico.ClassificacaoCids) obj;
			if (null == this.getCodigo() || null == classificacaoCids.getCodigo()) return false;
			else return (this.getCodigo().equals(classificacaoCids.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}