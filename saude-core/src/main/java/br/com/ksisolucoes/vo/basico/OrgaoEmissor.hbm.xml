<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.basico"  >
    <class
        name="OrgaoEmissor"
        table="orgao_emissor"
        >
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_orgao_emissor"
            >
            <generator class="assigned"/>
        </id> <version column="version" name="version" type="long" />

        <property
            name="descricao"
            column="ds_orgao_emissor"
            type="java.lang.String"
            not-null="true"
            length="60"
            />

        <property
            name="sigla"
            column="sg_orgao_emissor"
            type="java.lang.String"
            not-null="false"
            length="6"
            />

        <property
            name="flagSaude"
            column="fl_saude"
            type="java.lang.String"
            not-null="false"
            length="1"
            />

        <property
            name="versionAll"
            column="version_all"
            type="java.lang.Long"
            not-null="true"
            />

		<property
            name="codigoConselhoTiss"
            column="cod_conselho_tiss"
            type="java.lang.Long"
            not-null="false"
            length="6"
		/>

        <property
            name="oid"
            column="oid"
            type="java.lang.String"
            not-null="false"
        />

    </class>
</hibernate-mapping>