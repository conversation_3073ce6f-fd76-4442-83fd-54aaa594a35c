package br.com.ksisolucoes.vo.cadsus;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.cadsus.base.BaseExportacaoEsusProcesso;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;



public class ExportacaoEsusProcesso extends BaseExportacaoEsusProcesso implements CodigoManager {
	private static final long serialVersionUID = 1L;

	public enum Status implements IEnum {
		CONCLUIDO(0L, Bundle.getStringApplication("rotulo_concluido")),
		PROCESSANDO(1L, Bundle.getStringApplication("rotulo_processando")),
		ERRO(2L, Bundle.getStringApplication("rotulo_erro")),
		SEM_REGISTROS(3L, Bundle.getStringApplication("rotulo_sem_registros")),
		;

		private Long value;
		private String descricao;

		private Status(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static Status valeuOf(Long value) {
			for (Status status : Status.values()) {
				if (status.value().equals(value)) {
					return status;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}

	}

	/*[CONSTRUCTOR MARKER BEGIN]*/
	public ExportacaoEsusProcesso () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ExportacaoEsusProcesso (java.lang.Long codigo) {
		super(codigo);
	}

	/*[CONSTRUCTOR MARKER END]*/

	public void setCodigoManager(Serializable key) {
		this.setCodigo( (java.lang.Long)key );
	}

	public Serializable getCodigoManager() {
		return this.getCodigo();
	}

	public String getDescricaoStatus(){
		Status status = Status.valeuOf(getStatus());
		if (status != null && status.descricao != null) {
			return status.descricao();
		}
		return "";
	}
}