package br.com.ksisolucoes.vo.saidas.devolucao.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the devolucao_medicamento_item_lote table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="devolucao_medicamento_item_lote"
 */

public abstract class BaseDevolucaoMedicamentoItemLote extends BaseRootVO implements Serializable {

	public static String REF = "DevolucaoMedicamentoItemLote";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_GRUPO_ESTOQUE = "grupoEstoque";
	public static final String PROP_DEVOLUCAO_MEDICAMENTO_ITEM = "devolucaoMedicamentoItem";
	public static final String PROP_QUANTIDADE = "quantidade";


	// constructors
	public BaseDevolucaoMedicamentoItemLote () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseDevolucaoMedicamentoItemLote (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseDevolucaoMedicamentoItemLote (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.saidas.devolucao.DevolucaoMedicamentoItem devolucaoMedicamentoItem,
		br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque grupoEstoque,
		java.lang.Double quantidade) {

		this.setCodigo(codigo);
		this.setDevolucaoMedicamentoItem(devolucaoMedicamentoItem);
		this.setGrupoEstoque(grupoEstoque);
		this.setQuantidade(quantidade);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Double quantidade;

	// many to one
	private br.com.ksisolucoes.vo.saidas.devolucao.DevolucaoMedicamentoItem devolucaoMedicamentoItem;
	private br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque grupoEstoque;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_dev_med_item_lote"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: quantidade
	 */
	public java.lang.Double getQuantidade () {
		return getPropertyValue(this, quantidade, PROP_QUANTIDADE); 
	}

	/**
	 * Set the value related to the column: quantidade
	 * @param quantidade the quantidade value
	 */
	public void setQuantidade (java.lang.Double quantidade) {
//        java.lang.Double quantidadeOld = this.quantidade;
		this.quantidade = quantidade;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidade", quantidadeOld, quantidade);
	}



	/**
	 * Return the value associated with the column: cd_dev_med_item
	 */
	public br.com.ksisolucoes.vo.saidas.devolucao.DevolucaoMedicamentoItem getDevolucaoMedicamentoItem () {
		return getPropertyValue(this, devolucaoMedicamentoItem, PROP_DEVOLUCAO_MEDICAMENTO_ITEM); 
	}

	/**
	 * Set the value related to the column: cd_dev_med_item
	 * @param devolucaoMedicamentoItem the cd_dev_med_item value
	 */
	public void setDevolucaoMedicamentoItem (br.com.ksisolucoes.vo.saidas.devolucao.DevolucaoMedicamentoItem devolucaoMedicamentoItem) {
//        br.com.ksisolucoes.vo.saidas.devolucao.DevolucaoMedicamentoItem devolucaoMedicamentoItemOld = this.devolucaoMedicamentoItem;
		this.devolucaoMedicamentoItem = devolucaoMedicamentoItem;
//        this.getPropertyChangeSupport().firePropertyChange ("devolucaoMedicamentoItem", devolucaoMedicamentoItemOld, devolucaoMedicamentoItem);
	}



	/**
	 * Return the value associated with the column: cd_localizacao_estrutura
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque getGrupoEstoque () {
		return getPropertyValue(this, grupoEstoque, PROP_GRUPO_ESTOQUE); 
	}

	/**
	 * Set the value related to the column: cd_localizacao_estrutura
	 * @param grupoEstoque the cd_localizacao_estrutura value
	 */
	public void setGrupoEstoque (br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque grupoEstoque) {
//        br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque grupoEstoqueOld = this.grupoEstoque;
		this.grupoEstoque = grupoEstoque;
//        this.getPropertyChangeSupport().firePropertyChange ("grupoEstoque", grupoEstoqueOld, grupoEstoque);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.saidas.devolucao.DevolucaoMedicamentoItemLote)) return false;
		else {
			br.com.ksisolucoes.vo.saidas.devolucao.DevolucaoMedicamentoItemLote devolucaoMedicamentoItemLote = (br.com.ksisolucoes.vo.saidas.devolucao.DevolucaoMedicamentoItemLote) obj;
			if (null == this.getCodigo() || null == devolucaoMedicamentoItemLote.getCodigo()) return false;
			else return (this.getCodigo().equals(devolucaoMedicamentoItemLote.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}