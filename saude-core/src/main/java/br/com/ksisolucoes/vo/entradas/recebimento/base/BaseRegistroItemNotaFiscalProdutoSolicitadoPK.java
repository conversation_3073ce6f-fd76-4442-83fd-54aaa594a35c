package br.com.ksisolucoes.vo.entradas.recebimento.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;


public abstract class BaseRegistroItemNotaFiscalProdutoSolicitadoPK extends BaseRootVO implements Serializable {

	protected int hashCode = Integer.MIN_VALUE;

	public static String PROP_REGISTRO_ITEM_NOTA_FISCAL = "registroItemNotaFiscal";
	public static String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static String PROP_LOTE = "lote";

	private br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal registroItemNotaFiscal;
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private java.lang.String lote;


	public BaseRegistroItemNotaFiscalProdutoSolicitadoPK () {}
	
	public BaseRegistroItemNotaFiscalProdutoSolicitadoPK (
		br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal registroItemNotaFiscal,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		java.lang.String lote) {

		this.setRegistroItemNotaFiscal(registroItemNotaFiscal);
		this.setUsuarioCadsus(usuarioCadsus);
		this.setLote(lote);
	}


	/**
	 * Return the value associated with the column: cod_pessoa
	 */
	public br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal getRegistroItemNotaFiscal () {
		return getPropertyValue(this, registroItemNotaFiscal, PROP_REGISTRO_ITEM_NOTA_FISCAL); 
	}

	/**
	 * Set the value related to the column: cod_pessoa
	 * @param registroItemNotaFiscal the cod_pessoa value
	 */
	public void setRegistroItemNotaFiscal (br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal registroItemNotaFiscal) {
//        br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal registroItemNotaFiscalOld = this.registroItemNotaFiscal;
		this.registroItemNotaFiscal = registroItemNotaFiscal;
//        this.getPropertyChangeSupport().firePropertyChange ("registroItemNotaFiscal", registroItemNotaFiscalOld, registroItemNotaFiscal);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: lote
	 */
	public java.lang.String getLote () {
		return getPropertyValue(this, lote, PROP_LOTE); 
	}

	/**
	 * Set the value related to the column: lote
	 * @param lote the lote value
	 */
	public void setLote (java.lang.String lote) {
//        java.lang.String loteOld = this.lote;
		this.lote = lote;
//        this.getPropertyChangeSupport().firePropertyChange ("lote", loteOld, lote);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscalProdutoSolicitadoPK)) return false;
		else {
			br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscalProdutoSolicitadoPK mObj = (br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscalProdutoSolicitadoPK) obj;
			if (null != this.getRegistroItemNotaFiscal() && null != mObj.getRegistroItemNotaFiscal()) {
				if (!this.getRegistroItemNotaFiscal().equals(mObj.getRegistroItemNotaFiscal())) {
					return false;
				}
			}
			else {
				return false;
			}
			if (null != this.getUsuarioCadsus() && null != mObj.getUsuarioCadsus()) {
				if (!this.getUsuarioCadsus().equals(mObj.getUsuarioCadsus())) {
					return false;
				}
			}
			else {
				return false;
			}
			if (null != this.getLote() && null != mObj.getLote()) {
				if (!this.getLote().equals(mObj.getLote())) {
					return false;
				}
			}
			else {
				return false;
			}
			return true;
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			StringBuilder sb = new StringBuilder();
			if (null != this.getRegistroItemNotaFiscal()) {
				sb.append(this.getRegistroItemNotaFiscal().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			if (null != this.getUsuarioCadsus()) {
				sb.append(this.getUsuarioCadsus().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			if (null != this.getLote()) {
				sb.append(this.getLote().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			this.hashCode = sb.toString().hashCode();
		}
		return this.hashCode;
	}

    private java.beans.PropertyChangeSupport propertyChangeSupport;

    protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
        if( this.propertyChangeSupport == null ) {
            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
        }
        return this.propertyChangeSupport;
    }

    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.addPropertyChangeListener(l);
    }

    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
		propertyChangeSupport.addPropertyChangeListener(propertyName, listener);
    }

    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.removePropertyChangeListener(l);
    }
}