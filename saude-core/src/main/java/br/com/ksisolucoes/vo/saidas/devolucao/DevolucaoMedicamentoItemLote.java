package br.com.ksisolucoes.vo.saidas.devolucao;

import java.io.Serializable;

import br.com.ksisolucoes.vo.saidas.devolucao.base.BaseDevolucaoMedicamentoItemLote;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class DevolucaoMedicamentoItemLote extends BaseDevolucaoMedicamentoItemLote implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public DevolucaoMedicamentoItemLote () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public DevolucaoMedicamentoItemLote (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public DevolucaoMedicamentoItemLote (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.saidas.devolucao.DevolucaoMedicamentoItem devolucaoMedicamentoItem,
		br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque grupoEstoque,
		java.lang.Double quantidade) {

		super (
			codigo,
			devolucaoMedicamentoItem,
			grupoEstoque,
			quantidade);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}