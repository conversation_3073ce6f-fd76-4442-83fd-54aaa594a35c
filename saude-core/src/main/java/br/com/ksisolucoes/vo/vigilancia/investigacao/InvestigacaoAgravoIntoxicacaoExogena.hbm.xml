<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.investigacao">

    <class name="InvestigacaoAgravoIntoxicacaoExogena" table="investigacao_agr_intoxicacao_exogena">

        <id name="codigo"
            type="java.lang.Long"
            column="cd_invest_agr_intoxicacao_exogena" >
            <generator class="sequence">
                <param name="sequence">seq_investigacao_agr_intoxicacao_exogena</param>
            </generator>
        </id>

        <version column="version" name="version" type="long"/>

        <property
            name="flagInformacoesComplementares"
            column="flag_informacoes_complementares"
            type="java.lang.String"
            not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo"
                column="cd_registro_agravo"
                name="registroAgravo"
                not-null="true"
        />

        <property
                name="dataInvestigacao"
                column="dt_investigacao"
                type="java.util.Date"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"
                column="ocupacao_cbo"
                name="ocupacaoCbo"
                not-null="false"
        />

        <!-- OBS -->
        <property
                name="observacao"
                column="observacao"
                type="java.lang.String"
        />

        <!-- Encerramento -->
        <property
                name="dataEncerramento"
                column="dt_encerramento"
                type="java.util.Date"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario_encerramento"
                name="usuarioEncerramento"
                not-null="false"
        />



        <!-- Antecedentes e epidemiologicos -->
        <property
                name="situacaoMercadoTrabalho"
                column="situacao_mercado_trabalho"
                type="java.lang.Long"
        />
        <property
                name="situacaoMercadoTrabalhoOutros"
                column="situacao_mercado_trabalho_outros"
                type="java.lang.String"
        />

        <property
                name="localOcorrenciaExposicao"
                column="local_ocorrencia_exposicao"
                type="java.lang.Long"
        />
        <property
                name="localOcorrenciaExposicaoOutros"
                column="local_ocorrencia_exposicao_outros"
                type="java.lang.String"
        />

        <!-- Dados da exposição -->
        <property
                name="localEstabelecimentoOcorrenciaNome"
                column="local_estabelecimento_ocorrencia_nome"
                type="java.lang.String"
        />
        <property
                name="localEstabelecimentoOcorrenciaCnae"
                column="local_estabelecimento_ocorrencia_cnae"
                type="java.lang.String"
        />


        <property
                name="localEstabelecimentoCep"
                column="local_estab_cep"
                type="java.lang.String"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Pais"
                column="local_estab_pais"
                name="localEstabelecimentoPais"
                not-null="false"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Cidade"
                column="local_estab_cidade"
                name="localEstabelecimentoCidade"
                not-null="false"
        />

        <property
                name="localEstabelecimentoDistrito"
                column="local_estab_distrito"
                type="java.lang.String"
        />
        <property
                name="localEstabelecimentoBairro"
                column="local_estab_bairro"
                type="java.lang.String"
        />
        <property
                name="localEstabelecimentoLogradouro"
                column="local_estab_logradouro"
                type="java.lang.String"
        />
        <property
                name="localEstabelecimentoNumero"
                column="local_estab_numero"
                type="java.lang.String"
        />
        <property
                name="localEstabelecimentoComplemento"
                column="local_estab_complemento"
                type="java.lang.String"
        />
        <property
                name="localEstabelecimentoPontoReferencia"
                column="local_estab_ponto_referencia"
                type="java.lang.String"
        />
        <property
                name="localEstabelecimentoTelefone"
                column="local_estab_telefone"
                type="java.lang.String"
        />


        <property
                name="zonaExposicao"
                column="zona_exposicao"
                type="java.lang.Long"
        />
        <property
                name="grupoAgenteToxico"
                column="grupo_agente_toxico"
                type="java.lang.Long"
        />
        <property
                name="grupoAgenteToxicoOutro"
                column="grupo_agente_toxico_outro"
                type="java.lang.String"
        />


        <property
                name="agenteToxico1"
                column="agente_toxico_1"
                type="java.lang.String"
        />
        <property
                name="principioAtivo1"
                column="principio_ativo_1"
                type="java.lang.String"
        />

        <property
                name="agenteToxico2"
                column="agente_toxico_2"
                type="java.lang.String"
        />
        <property
                name="principioAtivo2"
                column="principio_ativo_2"
                type="java.lang.String"
        />

        <property
                name="agenteToxico3"
                column="agente_toxico_3"
                type="java.lang.String"
        />
        <property
                name="principioAtivo3"
                column="principio_ativo_3"
                type="java.lang.String"
        />


        <property
                name="agrotoxicoFinalidadeUtilizacao"
                column="agrotoxico_finalidade_utilizacao"
                type="java.lang.Long"
        />
        <property
                name="agrotoxicoFinalidadeUtilizacaoOutros"
                column="agrotoxico_finalidade_utilizacao_outros"
                type="java.lang.String"
        />
        <property
                name="agrotoxicoAtividadeExercida1"
                column="agrotoxico_atividades_exercidas_1"
                type="java.lang.Long"
        />
        <property
                name="agrotoxicoAtividadeExercida2"
                column="agrotoxico_atividades_exercidas_2"
                type="java.lang.Long"
        />
        <property
                name="agrotoxicoAtividadeExercida3"
                column="agrotoxico_atividades_exercidas_3"
                type="java.lang.Long"
        />
        <property
                name="agrotoxicoAgricolaCulturaLavoura"
                column="agrotoxico_agricola_cultura_lavoura"
                type="java.lang.String"
        />


        <property
                name="viaContaminacao1"
                column="via_contaminacao_1"
                type="java.lang.Long"
        />
        <property
                name="viaContaminacao2"
                column="via_contaminacao_2"
                type="java.lang.Long"
        />
        <property
                name="viaContaminacao3"
                column="via_contaminacao_3"
                type="java.lang.Long"
        />


        <property
                name="circunstanciaContaminacao"
                column="circunstancia_contaminacao"
                type="java.lang.Long"
        />
        <property
                name="circunstanciaContaminacaoOutros"
                column="circunstancia_contaminacao_outros"
                type="java.lang.String"
        />

        <property
                name="contaminacaoTrabalhoOcupacao"
                column="contaminacao_trabalho_ocupacao"
                type="java.lang.Long"
        />
        <property
                name="tipoContaminacao"
                column="tipo_exposicao"
                type="java.lang.Long"
        />

        <!-- Dados do Atendimento -->
        <property
                name="tempoExposicaoAtendimento"
                column="tempo_exposicao_atendimento"
                type="java.lang.String"
        />
        <property
                name="tempoExposicaoAtendimentoUnidadeMedida"
                column="tempo_exposicao_atendimento_um"
                type="java.lang.Long"
        />
        <property
                name="tipoAtendimento"
                column="tipo_atendimento"
                type="java.lang.Long"
        />

            <!-- Hospitalizacao -->
        <property
                name="hospitalizacao"
                column="hospitalizacao"
                type="java.lang.Long"
        />
        <property
                name="dataInternacao"
                column="dt_internacao"
                type="java.util.Date"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Empresa"
                column="unidade_hospital"
                name="hospital"
                not-null="false"
        />


        <!-- Conclusao -->
        <property
                name="classificacaoFinal"
                column="classificacao_final"
                type="java.lang.Long"
        />
        <property
                name="intoxicacaoConfirmadaDiagnostico"
                column="intoxicacao_confirmada_diagnostico"
                type="java.lang.String"
        />
        <property
                name="intoxicacaoConfirmadaDiagnosticoCid10"
                column="intoxicacao_confirmada_diagnostico_cid10"
                type="java.lang.String"
        />
        <property
                name="criterioConfirmacao"
                column="criterio_confirmacao"
                type="java.lang.Long"
        />
        <property
                name="evolucaoCaso"
                column="evolucao_caso"
                type="java.lang.Long"
        />

        <property
                name="dataObito"
                column="data_obito"
                type="java.util.Date"
        />

        <property
                name="comunicacaoAcidenteTrabalho"
                column="comunicacao_acidente_trabalho"
                type="java.lang.Long"
        />

    </class>
</hibernate-mapping>