<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.consorcio"  >
    <class name="PedidoLicitacaoItem" table="pedido_licitacao_item">
        
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_ped_lic_it"
        >
            <generator class="assigned" />
        </id> <version column="version" name="version" type="long" />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.consorcio.PedidoLicitacao"
            name="pedidoLicitacao"
            not-null="true"
            column="cd_ped_licitacao"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.entradas.estoque.Produto"
            name="produto"
            not-null="true"
            column="cod_pro"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuario"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usu_cancelamento"
            name="usuarioCancelamento"
        />        
        
        <property
            column="dt_usuario"
            name="dataUsuario"
            type="java.util.Date"
            not-null="true"
        />
        
        <property
            column="dt_cancelamento"
            name="dataCancelamento"
            type="java.util.Date"
        />
        
        <property
            column="qtd_pedido"
            name="quantidade"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property
            column="qtd_original"
            name="quantidadeOriginal"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property
            column="qtd_recebido"
            name="quantidadeRecebida"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            column="ult_preco"
            name="ultimoPreco"
            type="java.lang.Double"
            not-null="true"
        />
        
        <property
            column="preco_real"
            name="precoReal"
            type="java.lang.Double"
            not-null="false"
        />
        
        <property
            column="status"
            name="status"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property
            column="dt_validade"
            name="dataValidade"
            type="java.util.Date"
            not-null="false"
        />
        
        <property
            column="valor_total_licitado"
            name="valorTotalLicitado"
            type="java.lang.Double"
            not-null="false"
        />
        
        <property
            column="valor_recebido"
            name="valorRecebido"
            type="java.lang.Double"
            not-null="false"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario_desm"
            name="usuarioDesmembramento"
        />  
        
        <property
            column="dt_desmembramento"
            name="dataDesmembramento"
            type="java.util.Date"
        />
        
        <property
            column="status_desmembramento"
            name="statusDesmembramento"
            type="java.lang.Long"
        />
                
        <many-to-one
            class="br.com.ksisolucoes.vo.consorcio.PedidoLicitacaoItem"
            column="cd_pedido_lic_it_origem"
            name="pedidoLicitacaoItemOrigem"
        />
        
    </class>
</hibernate-mapping>
