package br.com.ksisolucoes.vo.basico.pesquisa;

import br.com.celk.integracao.IntegracaoRest;
import java.io.Serializable;

import br.com.ksisolucoes.vo.basico.pesquisa.base.BasePerguntaPesquisa;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;


@IntegracaoRest
public class PerguntaPesquisa extends BasePerguntaPesquisa implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public PerguntaPesquisa () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public PerguntaPesquisa (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public PerguntaPesquisa (
		java.lang.Long codigo,
		java.lang.Long versionAll) {

		super (
			codigo,
			versionAll);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    @Override
    public String getDescricaoVO() {
        return getDescricao();
    }

    @Override
    public String getIdentificador() {
        return getCodigo().toString();
    }
}