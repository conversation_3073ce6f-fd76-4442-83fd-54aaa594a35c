package br.com.ksisolucoes.vo.basico;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import java.io.Serializable;

import br.com.ksisolucoes.vo.basico.base.BaseLiberacaoAgendamento;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class LiberacaoAgendamento extends BaseLiberacaoAgendamento implements CodigoManager {
	private static final long serialVersionUID = 1L;
        
    public enum Status implements IEnum {
        ABERTO(0L, Bundle.getStringApplication("rotulo_aberto")),
        CANCELADO(1L, Bundle.getStringApplication("rotulo_cancelado")),
        LIBERADO(2L, Bundle.getStringApplication("rotulo_liberado")),
        ;

        private Long value;
        private String descricao;

        private Status(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Status valeuOf(Long value) {
            for (Status status : Status.values()) {
                if (status.value().equals(value)) {
                    return status;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }    

/*[CONSTRUCTOR MARKER BEGIN]*/
	public LiberacaoAgendamento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public LiberacaoAgendamento (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public LiberacaoAgendamento (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimento,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.String motivo,
		java.lang.Long status,
		java.util.Date dataCadastro) {

		super (
			codigo,
			usuarioCadsus,
			tipoProcedimento,
			usuario,
			motivo,
			status,
			dataCadastro);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public String getDescricaoStatus(){
        Status status = Status.valeuOf(getStatus());
        if (status != null && status.descricao != null) {
            return status.descricao();
        }
        return "";
    }
}