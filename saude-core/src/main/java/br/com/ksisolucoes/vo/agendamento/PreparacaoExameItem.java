package br.com.ksisolucoes.vo.agendamento;

import java.io.Serializable;

import br.com.ksisolucoes.vo.agendamento.base.BasePreparacaoExameItem;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class PreparacaoExameItem extends BasePreparacaoExameItem implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public PreparacaoExameItem () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public PreparacaoExameItem (java.lang.Long codigo) {
		super(codigo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}