package br.com.ksisolucoes.vo.consorcio.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the documento_consorcio table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="documento_consorcio"
 */

public abstract class BaseDocumentoConsorcio extends BaseRootVO implements Serializable {

	public static String REF = "DocumentoConsorcio";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_STATUS = "status";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_CONSORCIO_GUIA_PROCEDIMENTO = "consorcioGuiaProcedimento";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_MODELO_DOCUMENTO_CONSORCIO = "modeloDocumentoConsorcio";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_CONSORCIO_PRESTADOR = "consorcioPrestador";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";


	// constructors
	public BaseDocumentoConsorcio () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseDocumentoConsorcio (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseDocumentoConsorcio (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		br.com.ksisolucoes.vo.consorcio.ConsorcioPrestador consorcioPrestador,
		br.com.ksisolucoes.vo.consorcio.ModeloDocumentoConsorcio modeloDocumentoConsorcio,
		java.lang.String descricao) {

		this.setCodigo(codigo);
		this.setUsuarioCadsus(usuarioCadsus);
		this.setConsorcioPrestador(consorcioPrestador);
		this.setModeloDocumentoConsorcio(modeloDocumentoConsorcio);
		this.setDescricao(descricao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.util.Date dataCadastro;
	private java.util.Date dataCancelamento;
	private java.lang.Long status;

	// many to one
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private br.com.ksisolucoes.vo.consorcio.ConsorcioPrestador consorcioPrestador;
	private br.com.ksisolucoes.vo.consorcio.ModeloDocumentoConsorcio modeloDocumentoConsorcio;
	private br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento consorcioGuiaProcedimento;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_documento"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: descricao
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: descricao
	 * @param descricao the descricao value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: cd_usuario_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usuario_cadsus
	 * @param usuarioCadsus the cd_usuario_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cd_prestador
	 */
	public br.com.ksisolucoes.vo.consorcio.ConsorcioPrestador getConsorcioPrestador () {
		return getPropertyValue(this, consorcioPrestador, PROP_CONSORCIO_PRESTADOR); 
	}

	/**
	 * Set the value related to the column: cd_prestador
	 * @param consorcioPrestador the cd_prestador value
	 */
	public void setConsorcioPrestador (br.com.ksisolucoes.vo.consorcio.ConsorcioPrestador consorcioPrestador) {
//        br.com.ksisolucoes.vo.consorcio.ConsorcioPrestador consorcioPrestadorOld = this.consorcioPrestador;
		this.consorcioPrestador = consorcioPrestador;
//        this.getPropertyChangeSupport().firePropertyChange ("consorcioPrestador", consorcioPrestadorOld, consorcioPrestador);
	}



	/**
	 * Return the value associated with the column: cd_modelo_documento
	 */
	public br.com.ksisolucoes.vo.consorcio.ModeloDocumentoConsorcio getModeloDocumentoConsorcio () {
		return getPropertyValue(this, modeloDocumentoConsorcio, PROP_MODELO_DOCUMENTO_CONSORCIO); 
	}

	/**
	 * Set the value related to the column: cd_modelo_documento
	 * @param modeloDocumentoConsorcio the cd_modelo_documento value
	 */
	public void setModeloDocumentoConsorcio (br.com.ksisolucoes.vo.consorcio.ModeloDocumentoConsorcio modeloDocumentoConsorcio) {
//        br.com.ksisolucoes.vo.consorcio.ModeloDocumentoConsorcio modeloDocumentoConsorcioOld = this.modeloDocumentoConsorcio;
		this.modeloDocumentoConsorcio = modeloDocumentoConsorcio;
//        this.getPropertyChangeSupport().firePropertyChange ("modeloDocumentoConsorcio", modeloDocumentoConsorcioOld, modeloDocumentoConsorcio);
	}



	/**
	 * Return the value associated with the column: cd_guia
	 */
	public br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento getConsorcioGuiaProcedimento () {
		return getPropertyValue(this, consorcioGuiaProcedimento, PROP_CONSORCIO_GUIA_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_guia
	 * @param consorcioGuiaProcedimento the cd_guia value
	 */
	public void setConsorcioGuiaProcedimento (br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento consorcioGuiaProcedimento) {
//        br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento consorcioGuiaProcedimentoOld = this.consorcioGuiaProcedimento;
		this.consorcioGuiaProcedimento = consorcioGuiaProcedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("consorcioGuiaProcedimento", consorcioGuiaProcedimentoOld, consorcioGuiaProcedimento);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_usuario_cancelamento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_cancelamento
	 * @param usuarioCancelamento the cd_usuario_cancelamento value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.consorcio.DocumentoConsorcio)) return false;
		else {
			br.com.ksisolucoes.vo.consorcio.DocumentoConsorcio documentoConsorcio = (br.com.ksisolucoes.vo.consorcio.DocumentoConsorcio) obj;
			if (null == this.getCodigo() || null == documentoConsorcio.getCodigo()) return false;
			else return (this.getCodigo().equals(documentoConsorcio.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}