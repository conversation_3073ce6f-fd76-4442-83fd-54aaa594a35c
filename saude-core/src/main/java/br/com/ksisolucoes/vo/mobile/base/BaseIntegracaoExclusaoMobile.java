package br.com.ksisolucoes.vo.mobile.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the integracao_exclusao_mobile table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="integracao_exclusao_mobile"
 */

public abstract class BaseIntegracaoExclusaoMobile extends BaseRootVO implements Serializable {

	public static String REF = "IntegracaoExclusaoMobile";
	public static final String PROP_TIPO = "tipo";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_VERSION_ALL = "versionAll";
	public static final String PROP_ID_REGISTRO = "idRegistro";


	// constructors
	public BaseIntegracaoExclusaoMobile () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseIntegracaoExclusaoMobile (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseIntegracaoExclusaoMobile (
		java.lang.Long codigo,
		java.lang.String idRegistro,
		java.lang.String tipo,
		java.lang.Long versionAll) {

		this.setCodigo(codigo);
		this.setIdRegistro(idRegistro);
		this.setTipo(tipo);
		this.setVersionAll(versionAll);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String idRegistro;
	private java.lang.String tipo;
	private java.lang.Long versionAll;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_int_exclusao"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: id_registro
	 */
	public java.lang.String getIdRegistro () {
		return getPropertyValue(this, idRegistro, PROP_ID_REGISTRO); 
	}

	/**
	 * Set the value related to the column: id_registro
	 * @param idRegistro the id_registro value
	 */
	public void setIdRegistro (java.lang.String idRegistro) {
//        java.lang.String idRegistroOld = this.idRegistro;
		this.idRegistro = idRegistro;
//        this.getPropertyChangeSupport().firePropertyChange ("idRegistro", idRegistroOld, idRegistro);
	}



	/**
	 * Return the value associated with the column: tipo_registro
	 */
	public java.lang.String getTipo () {
		return getPropertyValue(this, tipo, PROP_TIPO); 
	}

	/**
	 * Set the value related to the column: tipo_registro
	 * @param tipo the tipo_registro value
	 */
	public void setTipo (java.lang.String tipo) {
//        java.lang.String tipoOld = this.tipo;
		this.tipo = tipo;
//        this.getPropertyChangeSupport().firePropertyChange ("tipo", tipoOld, tipo);
	}



	/**
	 * Return the value associated with the column: version_all
	 */
	public java.lang.Long getVersionAll () {
		return getPropertyValue(this, versionAll, PROP_VERSION_ALL); 
	}

	/**
	 * Set the value related to the column: version_all
	 * @param versionAll the version_all value
	 */
	public void setVersionAll (java.lang.Long versionAll) {
//        java.lang.Long versionAllOld = this.versionAll;
		this.versionAll = versionAll;
//        this.getPropertyChangeSupport().firePropertyChange ("versionAll", versionAllOld, versionAll);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.mobile.IntegracaoExclusaoMobile)) return false;
		else {
			br.com.ksisolucoes.vo.mobile.IntegracaoExclusaoMobile integracaoExclusaoMobile = (br.com.ksisolucoes.vo.mobile.IntegracaoExclusaoMobile) obj;
			if (null == this.getCodigo() || null == integracaoExclusaoMobile.getCodigo()) return false;
			else return (this.getCodigo().equals(integracaoExclusaoMobile.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}