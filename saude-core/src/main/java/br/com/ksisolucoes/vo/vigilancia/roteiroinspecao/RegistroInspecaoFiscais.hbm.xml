<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.roteiroinspecao"  >
    <class name="RegistroInspecaoFiscais" table="registro_inspecao_fiscais" >
        <id
            column="cd_reg_insp_fisc"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id>

        <version column="version" name="version" type="long" />    

        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.Profissional"
            column="cd_profissional"
            name="profissional"
            not-null="true"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecao"
            column="cd_reg_insp"
            name="registroInspecao"
            not-null="true"
        />
    </class>
</hibernate-mapping>