<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.basico"  >
	<class  
		name="TipoPessoa"
		table="tipo_pessoa"
	>
		<id
			name="codigo"
			type="java.lang.Long"
			column="cod_tip_pessoa"
		>
			<generator class="assigned"/>
		</id> <version column="version" name="version" type="long" />

		<property
			name="sigla"
			column="sigla"
			type="java.lang.String"
			not-null="true"
			length="10"
		/>
		<!-- please tell <PERSON> that the type 'descricao' could not be resolved.. defaulting to java.lang.String -->
		<property
			name="descricao"
			column="descricao"
			type="java.lang.String"
			not-null="true"
			length="40"
		/>
	
		<!--set name="pessoaSet" lazy="true">
			<key column="cod_tip_pessoa"/>
			<one-to-many class="br.com.ksisolucoes.vo.basico.Pessoa"/>
		</set-->

	</class>
</hibernate-mapping>
