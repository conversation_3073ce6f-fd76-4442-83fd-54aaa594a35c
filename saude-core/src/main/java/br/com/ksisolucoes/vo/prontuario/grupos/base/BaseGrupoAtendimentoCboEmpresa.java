package br.com.ksisolucoes.vo.prontuario.grupos.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the grupo_atendimento_cbo_empresa table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="grupo_atendimento_cbo_empresa"
 */

public abstract class BaseGrupoAtendimentoCboEmpresa extends BaseRootVO implements Serializable {

	public static String REF = "GrupoAtendimentoCboEmpresa";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_GRUPO_ATENDIMENTO_CBO = "grupoAtendimentoCbo";
	public static final String PROP_TIPO_FILTRO_ATENDIMENTO = "tipoFiltroAtendimento";
	public static final String PROP_USUARIO_CADASTRO = "usuarioCadastro";


	// constructors
	public BaseGrupoAtendimentoCboEmpresa () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseGrupoAtendimentoCboEmpresa (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseGrupoAtendimentoCboEmpresa (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCbo grupoAtendimentoCbo,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
		java.lang.Long tipoFiltroAtendimento,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setGrupoAtendimentoCbo(grupoAtendimentoCbo);
		this.setEmpresa(empresa);
		this.setUsuarioCadastro(usuarioCadastro);
		this.setTipoFiltroAtendimento(tipoFiltroAtendimento);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long tipoFiltroAtendimento;
	private java.util.Date dataCadastro;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCbo grupoAtendimentoCbo;
	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_grupo_atend_cbo_empresa"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: tp_filtro_atendimento
	 */
	public java.lang.Long getTipoFiltroAtendimento () {
		return getPropertyValue(this, tipoFiltroAtendimento, PROP_TIPO_FILTRO_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: tp_filtro_atendimento
	 * @param tipoFiltroAtendimento the tp_filtro_atendimento value
	 */
	public void setTipoFiltroAtendimento (java.lang.Long tipoFiltroAtendimento) {
//        java.lang.Long tipoFiltroAtendimentoOld = this.tipoFiltroAtendimento;
		this.tipoFiltroAtendimento = tipoFiltroAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoFiltroAtendimento", tipoFiltroAtendimentoOld, tipoFiltroAtendimento);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: cd_grupo_atend_cbo
	 */
	public br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCbo getGrupoAtendimentoCbo () {
		return getPropertyValue(this, grupoAtendimentoCbo, PROP_GRUPO_ATENDIMENTO_CBO); 
	}

	/**
	 * Set the value related to the column: cd_grupo_atend_cbo
	 * @param grupoAtendimentoCbo the cd_grupo_atend_cbo value
	 */
	public void setGrupoAtendimentoCbo (br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCbo grupoAtendimentoCbo) {
//        br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCbo grupoAtendimentoCboOld = this.grupoAtendimentoCbo;
		this.grupoAtendimentoCbo = grupoAtendimentoCbo;
//        this.getPropertyChangeSupport().firePropertyChange ("grupoAtendimentoCbo", grupoAtendimentoCboOld, grupoAtendimentoCbo);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: cd_usuario_cad
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCadastro () {
		return getPropertyValue(this, usuarioCadastro, PROP_USUARIO_CADASTRO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_cad
	 * @param usuarioCadastro the cd_usuario_cad value
	 */
	public void setUsuarioCadastro (br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCadastroOld = this.usuarioCadastro;
		this.usuarioCadastro = usuarioCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadastro", usuarioCadastroOld, usuarioCadastro);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCboEmpresa)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCboEmpresa grupoAtendimentoCboEmpresa = (br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCboEmpresa) obj;
			if (null == this.getCodigo() || null == grupoAtendimentoCboEmpresa.getCodigo()) return false;
			else return (this.getCodigo().equals(grupoAtendimentoCboEmpresa.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}