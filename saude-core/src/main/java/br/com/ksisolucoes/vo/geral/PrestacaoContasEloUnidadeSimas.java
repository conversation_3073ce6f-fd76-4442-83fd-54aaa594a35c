package br.com.ksisolucoes.vo.geral;

import java.io.Serializable;

import br.com.ksisolucoes.vo.geral.base.BasePrestacaoContasEloUnidadeSimas;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class PrestacaoContasEloUnidadeSimas extends BasePrestacaoContasEloUnidadeSimas implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public PrestacaoContasEloUnidadeSimas () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public PrestacaoContasEloUnidadeSimas (java.lang.Long codigo) {
		super(codigo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}