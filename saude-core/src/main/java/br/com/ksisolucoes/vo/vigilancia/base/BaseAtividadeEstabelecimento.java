package br.com.ksisolucoes.vo.vigilancia.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the atividade_estabelecimento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="atividade_estabelecimento"
 */

public abstract class BaseAtividadeEstabelecimento extends BaseRootVO implements Serializable {

	public static String REF = "AtividadeEstabelecimento";
	public static final String PROP_QUANTIDADE_TAXA_ALVARA_REVALIDACAO = "quantidadeTaxaAlvaraRevalidacao";
	public static final String PROP_OBSERVACAO_ALVARA = "observacaoAlvara";
	public static final String PROP_OBSERVACAO_DESTAQUE_ALVARA = "observacaoDestaqueAlvara";
	public static final String PROP_VALIDADE_ALVARA_PERIODO = "validadeAlvaraPeriodo";
	public static final String PROP_TABELA_CNAE = "tabelaCnae";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_SETOR_VIGILANCIA = "setorVigilancia";
	public static final String PROP_VALIDADE_ALVARA_DATA_FIXA = "validadeAlvaraDataFixa";
	public static final String PROP_FLAG_VALIDADE_ALVARA = "flagValidadeAlvara";
	public static final String PROP_DESCRICAO_COMPLEMENTAR = "descricaoComplementar";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_FLAG_VALIDADE_LICENCA_TRANSPORTE = "flagValidadeLicencaTransporte";
	public static final String PROP_TAXA = "taxa";
	public static final String PROP_QUANTIDADE_TAXA = "quantidadeTaxa";
	public static final String PROP_EXIGE_RESPONSAVEL_TECNICO = "exigeResponsavelTecnico";
	public static final String PROP_GRUPO_ESTABELECIMENTO = "grupoEstabelecimento";
	public static final String PROP_QUANTIDADE_TAXA_ALVARA_INICIAL = "quantidadeTaxaAlvaraInicial";
	public static final String PROP_VALIDADE_ALVARA_DATA_VENCIMENTO = "validadeAlvaraDataVencimento";
	public static final String PROP_LICENCA_TRANSPORTE_DATA_VALIDADE = "licencaTransporteDataValidade";
	public static final String PROP_DT_VALIDADE_TRANSPORTE = "dtValidadeTransporte";


	// constructors
	public BaseAtividadeEstabelecimento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAtividadeEstabelecimento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseAtividadeEstabelecimento (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.grupoestabelecimento.GrupoEstabelecimento grupoEstabelecimento,
		java.lang.String descricao) {

		this.setCodigo(codigo);
		this.setGrupoEstabelecimento(grupoEstabelecimento);
		this.setDescricao(descricao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.lang.String observacaoAlvara;
	private java.lang.Long exigeResponsavelTecnico;
	private java.lang.String observacaoDestaqueAlvara;
	private java.lang.Long flagValidadeAlvara;
	private java.lang.Long validadeAlvaraDataFixa;
	private java.lang.Long validadeAlvaraPeriodo;
	private java.util.Date validadeAlvaraDataVencimento;
	private java.lang.Double quantidadeTaxa;
	private java.lang.Double quantidadeTaxaAlvaraInicial;
	private java.lang.Double quantidadeTaxaAlvaraRevalidacao;
	private java.lang.String descricaoComplementar;
	private java.lang.Long licencaTransporteDataValidade;
	private java.util.Date dtValidadeTransporte;
	private java.lang.Long flagValidadeLicencaTransporte;

	// many to one
	private br.com.ksisolucoes.vo.grupoestabelecimento.GrupoEstabelecimento grupoEstabelecimento;
	private br.com.ksisolucoes.vo.vigilancia.SetorVigilancia setorVigilancia;
	private br.com.ksisolucoes.vo.vigilancia.taxa.Taxa taxa;
	private br.com.ksisolucoes.vo.vigilancia.TabelaCnae tabelaCnae;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_atividade_estabelecimento"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_atividade_estabelecimento
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_atividade_estabelecimento
	 * @param descricao the ds_atividade_estabelecimento value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: observacao_alvara
	 */
	public java.lang.String getObservacaoAlvara () {
		return getPropertyValue(this, observacaoAlvara, PROP_OBSERVACAO_ALVARA); 
	}

	/**
	 * Set the value related to the column: observacao_alvara
	 * @param observacaoAlvara the observacao_alvara value
	 */
	public void setObservacaoAlvara (java.lang.String observacaoAlvara) {
//        java.lang.String observacaoAlvaraOld = this.observacaoAlvara;
		this.observacaoAlvara = observacaoAlvara;
//        this.getPropertyChangeSupport().firePropertyChange ("observacaoAlvara", observacaoAlvaraOld, observacaoAlvara);
	}



	/**
	 * Return the value associated with the column: flag_exige_responsavel
	 */
	public java.lang.Long getExigeResponsavelTecnico () {
		return getPropertyValue(this, exigeResponsavelTecnico, PROP_EXIGE_RESPONSAVEL_TECNICO); 
	}

	/**
	 * Set the value related to the column: flag_exige_responsavel
	 * @param exigeResponsavelTecnico the flag_exige_responsavel value
	 */
	public void setExigeResponsavelTecnico (java.lang.Long exigeResponsavelTecnico) {
//        java.lang.Long exigeResponsavelTecnicoOld = this.exigeResponsavelTecnico;
		this.exigeResponsavelTecnico = exigeResponsavelTecnico;
//        this.getPropertyChangeSupport().firePropertyChange ("exigeResponsavelTecnico", exigeResponsavelTecnicoOld, exigeResponsavelTecnico);
	}



	/**
	 * Return the value associated with the column: obs_destaque_alvara
	 */
	public java.lang.String getObservacaoDestaqueAlvara () {
		return getPropertyValue(this, observacaoDestaqueAlvara, PROP_OBSERVACAO_DESTAQUE_ALVARA); 
	}

	/**
	 * Set the value related to the column: obs_destaque_alvara
	 * @param observacaoDestaqueAlvara the obs_destaque_alvara value
	 */
	public void setObservacaoDestaqueAlvara (java.lang.String observacaoDestaqueAlvara) {
//        java.lang.String observacaoDestaqueAlvaraOld = this.observacaoDestaqueAlvara;
		this.observacaoDestaqueAlvara = observacaoDestaqueAlvara;
//        this.getPropertyChangeSupport().firePropertyChange ("observacaoDestaqueAlvara", observacaoDestaqueAlvaraOld, observacaoDestaqueAlvara);
	}



	/**
	 * Return the value associated with the column: flag_validade_alvara
	 */
	public java.lang.Long getFlagValidadeAlvara () {
		return getPropertyValue(this, flagValidadeAlvara, PROP_FLAG_VALIDADE_ALVARA); 
	}

	/**
	 * Set the value related to the column: flag_validade_alvara
	 * @param flagValidadeAlvara the flag_validade_alvara value
	 */
	public void setFlagValidadeAlvara (java.lang.Long flagValidadeAlvara) {
//        java.lang.Long flagValidadeAlvaraOld = this.flagValidadeAlvara;
		this.flagValidadeAlvara = flagValidadeAlvara;
//        this.getPropertyChangeSupport().firePropertyChange ("flagValidadeAlvara", flagValidadeAlvaraOld, flagValidadeAlvara);
	}



	/**
	 * Return the value associated with the column: validade_alvara_data_fixa
	 */
	public java.lang.Long getValidadeAlvaraDataFixa () {
		return getPropertyValue(this, validadeAlvaraDataFixa, PROP_VALIDADE_ALVARA_DATA_FIXA); 
	}

	/**
	 * Set the value related to the column: validade_alvara_data_fixa
	 * @param validadeAlvaraDataFixa the validade_alvara_data_fixa value
	 */
	public void setValidadeAlvaraDataFixa (java.lang.Long validadeAlvaraDataFixa) {
//        java.lang.Long validadeAlvaraDataFixaOld = this.validadeAlvaraDataFixa;
		this.validadeAlvaraDataFixa = validadeAlvaraDataFixa;
//        this.getPropertyChangeSupport().firePropertyChange ("validadeAlvaraDataFixa", validadeAlvaraDataFixaOld, validadeAlvaraDataFixa);
	}



	/**
	 * Return the value associated with the column: validade_alvara_periodo
	 */
	public java.lang.Long getValidadeAlvaraPeriodo () {
		return getPropertyValue(this, validadeAlvaraPeriodo, PROP_VALIDADE_ALVARA_PERIODO); 
	}

	/**
	 * Set the value related to the column: validade_alvara_periodo
	 * @param validadeAlvaraPeriodo the validade_alvara_periodo value
	 */
	public void setValidadeAlvaraPeriodo (java.lang.Long validadeAlvaraPeriodo) {
//        java.lang.Long validadeAlvaraPeriodoOld = this.validadeAlvaraPeriodo;
		this.validadeAlvaraPeriodo = validadeAlvaraPeriodo;
//        this.getPropertyChangeSupport().firePropertyChange ("validadeAlvaraPeriodo", validadeAlvaraPeriodoOld, validadeAlvaraPeriodo);
	}



	/**
	 * Return the value associated with the column: validade_alvara_data_vencimento
	 */
	public java.util.Date getValidadeAlvaraDataVencimento () {
		return getPropertyValue(this, validadeAlvaraDataVencimento, PROP_VALIDADE_ALVARA_DATA_VENCIMENTO); 
	}

	/**
	 * Set the value related to the column: validade_alvara_data_vencimento
	 * @param validadeAlvaraDataVencimento the validade_alvara_data_vencimento value
	 */
	public void setValidadeAlvaraDataVencimento (java.util.Date validadeAlvaraDataVencimento) {
//        java.util.Date validadeAlvaraDataVencimentoOld = this.validadeAlvaraDataVencimento;
		this.validadeAlvaraDataVencimento = validadeAlvaraDataVencimento;
//        this.getPropertyChangeSupport().firePropertyChange ("validadeAlvaraDataVencimento", validadeAlvaraDataVencimentoOld, validadeAlvaraDataVencimento);
	}



	/**
	 * Return the value associated with the column: qtd_taxa
	 */
	public java.lang.Double getQuantidadeTaxa () {
		return getPropertyValue(this, quantidadeTaxa, PROP_QUANTIDADE_TAXA); 
	}

	/**
	 * Set the value related to the column: qtd_taxa
	 * @param quantidadeTaxa the qtd_taxa value
	 */
	public void setQuantidadeTaxa (java.lang.Double quantidadeTaxa) {
//        java.lang.Double quantidadeTaxaOld = this.quantidadeTaxa;
		this.quantidadeTaxa = quantidadeTaxa;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeTaxa", quantidadeTaxaOld, quantidadeTaxa);
	}



	/**
	 * Return the value associated with the column: qtd_taxa_alvara_inicial
	 */
	public java.lang.Double getQuantidadeTaxaAlvaraInicial () {
		return getPropertyValue(this, quantidadeTaxaAlvaraInicial, PROP_QUANTIDADE_TAXA_ALVARA_INICIAL); 
	}

	/**
	 * Set the value related to the column: qtd_taxa_alvara_inicial
	 * @param quantidadeTaxaAlvaraInicial the qtd_taxa_alvara_inicial value
	 */
	public void setQuantidadeTaxaAlvaraInicial (java.lang.Double quantidadeTaxaAlvaraInicial) {
//        java.lang.Double quantidadeTaxaAlvaraInicialOld = this.quantidadeTaxaAlvaraInicial;
		this.quantidadeTaxaAlvaraInicial = quantidadeTaxaAlvaraInicial;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeTaxaAlvaraInicial", quantidadeTaxaAlvaraInicialOld, quantidadeTaxaAlvaraInicial);
	}



	/**
	 * Return the value associated with the column: qtd_taxa_alvara_revalidacao
	 */
	public java.lang.Double getQuantidadeTaxaAlvaraRevalidacao () {
		return getPropertyValue(this, quantidadeTaxaAlvaraRevalidacao, PROP_QUANTIDADE_TAXA_ALVARA_REVALIDACAO); 
	}

	/**
	 * Set the value related to the column: qtd_taxa_alvara_revalidacao
	 * @param quantidadeTaxaAlvaraRevalidacao the qtd_taxa_alvara_revalidacao value
	 */
	public void setQuantidadeTaxaAlvaraRevalidacao (java.lang.Double quantidadeTaxaAlvaraRevalidacao) {
//        java.lang.Double quantidadeTaxaAlvaraRevalidacaoOld = this.quantidadeTaxaAlvaraRevalidacao;
		this.quantidadeTaxaAlvaraRevalidacao = quantidadeTaxaAlvaraRevalidacao;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeTaxaAlvaraRevalidacao", quantidadeTaxaAlvaraRevalidacaoOld, quantidadeTaxaAlvaraRevalidacao);
	}



	/**
	 * Return the value associated with the column: ds_complementar
	 */
	public java.lang.String getDescricaoComplementar () {
		return getPropertyValue(this, descricaoComplementar, PROP_DESCRICAO_COMPLEMENTAR); 
	}

	/**
	 * Set the value related to the column: ds_complementar
	 * @param descricaoComplementar the ds_complementar value
	 */
	public void setDescricaoComplementar (java.lang.String descricaoComplementar) {
//        java.lang.String descricaoComplementarOld = this.descricaoComplementar;
		this.descricaoComplementar = descricaoComplementar;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoComplementar", descricaoComplementarOld, descricaoComplementar);
	}



	/**
	 * Return the value associated with the column: licenca_transporte_data_validade
	 */
	public java.lang.Long getLicencaTransporteDataValidade () {
		return getPropertyValue(this, licencaTransporteDataValidade, PROP_LICENCA_TRANSPORTE_DATA_VALIDADE); 
	}

	/**
	 * Set the value related to the column: licenca_transporte_data_validade
	 * @param licencaTransporteDataValidade the licenca_transporte_data_validade value
	 */
	public void setLicencaTransporteDataValidade (java.lang.Long licencaTransporteDataValidade) {
//        java.lang.Long licencaTransporteDataValidadeOld = this.licencaTransporteDataValidade;
		this.licencaTransporteDataValidade = licencaTransporteDataValidade;
//        this.getPropertyChangeSupport().firePropertyChange ("licencaTransporteDataValidade", licencaTransporteDataValidadeOld, licencaTransporteDataValidade);
	}



	/**
	 * Return the value associated with the column: dt_validade_transporte
	 */
	public java.util.Date getDtValidadeTransporte () {
		return getPropertyValue(this, dtValidadeTransporte, PROP_DT_VALIDADE_TRANSPORTE); 
	}

	/**
	 * Set the value related to the column: dt_validade_transporte
	 * @param dtValidadeTransporte the dt_validade_transporte value
	 */
	public void setDtValidadeTransporte (java.util.Date dtValidadeTransporte) {
//        java.util.Date dtValidadeTransporteOld = this.dtValidadeTransporte;
		this.dtValidadeTransporte = dtValidadeTransporte;
//        this.getPropertyChangeSupport().firePropertyChange ("dtValidadeTransporte", dtValidadeTransporteOld, dtValidadeTransporte);
	}



	/**
	 * Return the value associated with the column: flag_validade_licenca_transporte
	 */
	public java.lang.Long getFlagValidadeLicencaTransporte () {
		return getPropertyValue(this, flagValidadeLicencaTransporte, PROP_FLAG_VALIDADE_LICENCA_TRANSPORTE); 
	}

	/**
	 * Set the value related to the column: flag_validade_licenca_transporte
	 * @param flagValidadeLicencaTransporte the flag_validade_licenca_transporte value
	 */
	public void setFlagValidadeLicencaTransporte (java.lang.Long flagValidadeLicencaTransporte) {
//        java.lang.Long flagValidadeLicencaTransporteOld = this.flagValidadeLicencaTransporte;
		this.flagValidadeLicencaTransporte = flagValidadeLicencaTransporte;
//        this.getPropertyChangeSupport().firePropertyChange ("flagValidadeLicencaTransporte", flagValidadeLicencaTransporteOld, flagValidadeLicencaTransporte);
	}



	/**
	 * Return the value associated with the column: cd_grupo_estabelecimento
	 */
	public br.com.ksisolucoes.vo.grupoestabelecimento.GrupoEstabelecimento getGrupoEstabelecimento () {
		return getPropertyValue(this, grupoEstabelecimento, PROP_GRUPO_ESTABELECIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_grupo_estabelecimento
	 * @param grupoEstabelecimento the cd_grupo_estabelecimento value
	 */
	public void setGrupoEstabelecimento (br.com.ksisolucoes.vo.grupoestabelecimento.GrupoEstabelecimento grupoEstabelecimento) {
//        br.com.ksisolucoes.vo.grupoestabelecimento.GrupoEstabelecimento grupoEstabelecimentoOld = this.grupoEstabelecimento;
		this.grupoEstabelecimento = grupoEstabelecimento;
//        this.getPropertyChangeSupport().firePropertyChange ("grupoEstabelecimento", grupoEstabelecimentoOld, grupoEstabelecimento);
	}



	/**
	 * Return the value associated with the column: cd_setor_vigilancia
	 */
	public br.com.ksisolucoes.vo.vigilancia.SetorVigilancia getSetorVigilancia () {
		return getPropertyValue(this, setorVigilancia, PROP_SETOR_VIGILANCIA); 
	}

	/**
	 * Set the value related to the column: cd_setor_vigilancia
	 * @param setorVigilancia the cd_setor_vigilancia value
	 */
	public void setSetorVigilancia (br.com.ksisolucoes.vo.vigilancia.SetorVigilancia setorVigilancia) {
//        br.com.ksisolucoes.vo.vigilancia.SetorVigilancia setorVigilanciaOld = this.setorVigilancia;
		this.setorVigilancia = setorVigilancia;
//        this.getPropertyChangeSupport().firePropertyChange ("setorVigilancia", setorVigilanciaOld, setorVigilancia);
	}



	/**
	 * Return the value associated with the column: cd_taxa
	 */
	public br.com.ksisolucoes.vo.vigilancia.taxa.Taxa getTaxa () {
		return getPropertyValue(this, taxa, PROP_TAXA); 
	}

	/**
	 * Set the value related to the column: cd_taxa
	 * @param taxa the cd_taxa value
	 */
	public void setTaxa (br.com.ksisolucoes.vo.vigilancia.taxa.Taxa taxa) {
//        br.com.ksisolucoes.vo.vigilancia.taxa.Taxa taxaOld = this.taxa;
		this.taxa = taxa;
//        this.getPropertyChangeSupport().firePropertyChange ("taxa", taxaOld, taxa);
	}



	/**
	 * Return the value associated with the column: cd_cnae
	 */
	public br.com.ksisolucoes.vo.vigilancia.TabelaCnae getTabelaCnae () {
		return getPropertyValue(this, tabelaCnae, PROP_TABELA_CNAE); 
	}

	/**
	 * Set the value related to the column: cd_cnae
	 * @param tabelaCnae the cd_cnae value
	 */
	public void setTabelaCnae (br.com.ksisolucoes.vo.vigilancia.TabelaCnae tabelaCnae) {
//        br.com.ksisolucoes.vo.vigilancia.TabelaCnae tabelaCnaeOld = this.tabelaCnae;
		this.tabelaCnae = tabelaCnae;
//        this.getPropertyChangeSupport().firePropertyChange ("tabelaCnae", tabelaCnaeOld, tabelaCnae);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento atividadeEstabelecimento = (br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento) obj;
			if (null == this.getCodigo() || null == atividadeEstabelecimento.getCodigo()) return false;
			else return (this.getCodigo().equals(atividadeEstabelecimento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}