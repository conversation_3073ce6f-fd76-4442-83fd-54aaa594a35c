package br.com.ksisolucoes.vo.vigilancia.requerimentos.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the requerimento_defesa_previa table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="requerimento_defesa_previa"
 */

public abstract class BaseRequerimentoDefesaPrevia extends BaseRootVO implements Serializable {

	public static String REF = "RequerimentoDefesaPrevia";
	public static final String PROP_ESTABELECIMENTO = "estabelecimento";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_NUMERO_AUTO_INFRACAO = "numeroAutoInfracao";
	public static final String PROP_REQUERIMENTO_VIGILANCIA = "requerimentoVigilancia";
	public static final String PROP_CNPJ_CPF = "cnpjCpf";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TELEFONE = "telefone";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_USUARIO_CADASTRO = "usuarioCadastro";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";
	public static final String PROP_DATA_AUTO = "dataAuto";
	public static final String PROP_VIGILANCIA_ENDERECO = "vigilanciaEndereco";
	public static final String PROP_RAZOES = "razoes";
	public static final String PROP_NOME = "nome";


	// constructors
	public BaseRequerimentoDefesaPrevia () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseRequerimentoDefesaPrevia (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseRequerimentoDefesaPrevia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia,
		br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
		java.lang.String numeroAutoInfracao,
		java.util.Date dataCadastro,
		java.util.Date dataAuto) {

		this.setCodigo(codigo);
		this.setRequerimentoVigilancia(requerimentoVigilancia);
		this.setUsuarioCadastro(usuarioCadastro);
		this.setNumeroAutoInfracao(numeroAutoInfracao);
		this.setDataCadastro(dataCadastro);
		this.setDataAuto(dataAuto);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String nome;
	private java.lang.String cnpjCpf;
	private java.lang.String telefone;
	private java.lang.String numeroAutoInfracao;
	private java.util.Date dataCadastro;
	private java.util.Date dataCancelamento;
	private java.util.Date dataAuto;
	private java.lang.String razoes;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia;
	private br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimento;
	private br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco vigilanciaEndereco;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_requerimento_defesa_previa"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: nome
	 */
	public java.lang.String getNome () {
		return getPropertyValue(this, nome, PROP_NOME); 
	}

	/**
	 * Set the value related to the column: nome
	 * @param nome the nome value
	 */
	public void setNome (java.lang.String nome) {
//        java.lang.String nomeOld = this.nome;
		this.nome = nome;
//        this.getPropertyChangeSupport().firePropertyChange ("nome", nomeOld, nome);
	}



	/**
	 * Return the value associated with the column: cnpj_cpf
	 */
	public java.lang.String getCnpjCpf () {
		return getPropertyValue(this, cnpjCpf, PROP_CNPJ_CPF); 
	}

	/**
	 * Set the value related to the column: cnpj_cpf
	 * @param cnpjCpf the cnpj_cpf value
	 */
	public void setCnpjCpf (java.lang.String cnpjCpf) {
//        java.lang.String cnpjCpfOld = this.cnpjCpf;
		this.cnpjCpf = cnpjCpf;
//        this.getPropertyChangeSupport().firePropertyChange ("cnpjCpf", cnpjCpfOld, cnpjCpf);
	}



	/**
	 * Return the value associated with the column: telefone
	 */
	public java.lang.String getTelefone () {
		return getPropertyValue(this, telefone, PROP_TELEFONE); 
	}

	/**
	 * Set the value related to the column: telefone
	 * @param telefone the telefone value
	 */
	public void setTelefone (java.lang.String telefone) {
//        java.lang.String telefoneOld = this.telefone;
		this.telefone = telefone;
//        this.getPropertyChangeSupport().firePropertyChange ("telefone", telefoneOld, telefone);
	}



	/**
	 * Return the value associated with the column: num_auto_infracao
	 */
	public java.lang.String getNumeroAutoInfracao () {
		return getPropertyValue(this, numeroAutoInfracao, PROP_NUMERO_AUTO_INFRACAO); 
	}

	/**
	 * Set the value related to the column: num_auto_infracao
	 * @param numeroAutoInfracao the num_auto_infracao value
	 */
	public void setNumeroAutoInfracao (java.lang.String numeroAutoInfracao) {
//        java.lang.String numeroAutoInfracaoOld = this.numeroAutoInfracao;
		this.numeroAutoInfracao = numeroAutoInfracao;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroAutoInfracao", numeroAutoInfracaoOld, numeroAutoInfracao);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: data_auto
	 */
	public java.util.Date getDataAuto () {
		return getPropertyValue(this, dataAuto, PROP_DATA_AUTO); 
	}

	/**
	 * Set the value related to the column: data_auto
	 * @param dataAuto the data_auto value
	 */
	public void setDataAuto (java.util.Date dataAuto) {
//        java.util.Date dataAutoOld = this.dataAuto;
		this.dataAuto = dataAuto;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAuto", dataAutoOld, dataAuto);
	}



	/**
	 * Return the value associated with the column: razoes
	 */
	public java.lang.String getRazoes () {
		return getPropertyValue(this, razoes, PROP_RAZOES); 
	}

	/**
	 * Set the value related to the column: razoes
	 * @param razoes the razoes value
	 */
	public void setRazoes (java.lang.String razoes) {
//        java.lang.String razoesOld = this.razoes;
		this.razoes = razoes;
//        this.getPropertyChangeSupport().firePropertyChange ("razoes", razoesOld, razoes);
	}



	/**
	 * Return the value associated with the column: cd_req_vigilancia
	 */
	public br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia getRequerimentoVigilancia () {
		return getPropertyValue(this, requerimentoVigilancia, PROP_REQUERIMENTO_VIGILANCIA); 
	}

	/**
	 * Set the value related to the column: cd_req_vigilancia
	 * @param requerimentoVigilancia the cd_req_vigilancia value
	 */
	public void setRequerimentoVigilancia (br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia) {
//        br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilanciaOld = this.requerimentoVigilancia;
		this.requerimentoVigilancia = requerimentoVigilancia;
//        this.getPropertyChangeSupport().firePropertyChange ("requerimentoVigilancia", requerimentoVigilanciaOld, requerimentoVigilancia);
	}



	/**
	 * Return the value associated with the column: cd_estabelecimento
	 */
	public br.com.ksisolucoes.vo.vigilancia.Estabelecimento getEstabelecimento () {
		return getPropertyValue(this, estabelecimento, PROP_ESTABELECIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_estabelecimento
	 * @param estabelecimento the cd_estabelecimento value
	 */
	public void setEstabelecimento (br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimento) {
//        br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimentoOld = this.estabelecimento;
		this.estabelecimento = estabelecimento;
//        this.getPropertyChangeSupport().firePropertyChange ("estabelecimento", estabelecimentoOld, estabelecimento);
	}



	/**
	 * Return the value associated with the column: cd_vigilancia_endereco
	 */
	public br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco getVigilanciaEndereco () {
		return getPropertyValue(this, vigilanciaEndereco, PROP_VIGILANCIA_ENDERECO); 
	}

	/**
	 * Set the value related to the column: cd_vigilancia_endereco
	 * @param vigilanciaEndereco the cd_vigilancia_endereco value
	 */
	public void setVigilanciaEndereco (br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco vigilanciaEndereco) {
//        br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco vigilanciaEnderecoOld = this.vigilanciaEndereco;
		this.vigilanciaEndereco = vigilanciaEndereco;
//        this.getPropertyChangeSupport().firePropertyChange ("vigilanciaEndereco", vigilanciaEnderecoOld, vigilanciaEndereco);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCadastro () {
		return getPropertyValue(this, usuarioCadastro, PROP_USUARIO_CADASTRO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuarioCadastro the cd_usuario value
	 */
	public void setUsuarioCadastro (br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCadastroOld = this.usuarioCadastro;
		this.usuarioCadastro = usuarioCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadastro", usuarioCadastroOld, usuarioCadastro);
	}



	/**
	 * Return the value associated with the column: cd_usuario_can
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_can
	 * @param usuarioCancelamento the cd_usuario_can value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoDefesaPrevia)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoDefesaPrevia requerimentoDefesaPrevia = (br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoDefesaPrevia) obj;
			if (null == this.getCodigo() || null == requerimentoDefesaPrevia.getCodigo()) return false;
			else return (this.getCodigo().equals(requerimentoDefesaPrevia.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}