package br.com.ksisolucoes.vo.basico.pesquisa.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the pesquisa_pergunta table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="pesquisa_pergunta"
 */

public abstract class BasePesquisaPergunta extends BaseRootVO implements Serializable {

	public static String REF = "PesquisaPergunta";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_PERGUNTA_PESQUISA = "perguntaPesquisa";
	public static final String PROP_VERSION_ALL = "versionAll";
	public static final String PROP_PESQUISA = "pesquisa";


	// constructors
	public BasePesquisaPergunta () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BasePesquisaPergunta (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BasePesquisaPergunta (
		java.lang.Long codigo,
		java.lang.Long versionAll) {

		this.setCodigo(codigo);
		this.setVersionAll(versionAll);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long versionAll;

	// many to one
	private br.com.ksisolucoes.vo.basico.pesquisa.Pesquisa pesquisa;
	private br.com.ksisolucoes.vo.basico.pesquisa.PerguntaPesquisa perguntaPesquisa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_pesq_pergunta"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: version_all
	 */
	public java.lang.Long getVersionAll () {
		return getPropertyValue(this, versionAll, PROP_VERSION_ALL); 
	}

	/**
	 * Set the value related to the column: version_all
	 * @param versionAll the version_all value
	 */
	public void setVersionAll (java.lang.Long versionAll) {
//        java.lang.Long versionAllOld = this.versionAll;
		this.versionAll = versionAll;
//        this.getPropertyChangeSupport().firePropertyChange ("versionAll", versionAllOld, versionAll);
	}



	/**
	 * Return the value associated with the column: cd_pesquisa
	 */
	public br.com.ksisolucoes.vo.basico.pesquisa.Pesquisa getPesquisa () {
		return getPropertyValue(this, pesquisa, PROP_PESQUISA); 
	}

	/**
	 * Set the value related to the column: cd_pesquisa
	 * @param pesquisa the cd_pesquisa value
	 */
	public void setPesquisa (br.com.ksisolucoes.vo.basico.pesquisa.Pesquisa pesquisa) {
//        br.com.ksisolucoes.vo.basico.pesquisa.Pesquisa pesquisaOld = this.pesquisa;
		this.pesquisa = pesquisa;
//        this.getPropertyChangeSupport().firePropertyChange ("pesquisa", pesquisaOld, pesquisa);
	}



	/**
	 * Return the value associated with the column: cd_pergunta
	 */
	public br.com.ksisolucoes.vo.basico.pesquisa.PerguntaPesquisa getPerguntaPesquisa () {
		return getPropertyValue(this, perguntaPesquisa, PROP_PERGUNTA_PESQUISA); 
	}

	/**
	 * Set the value related to the column: cd_pergunta
	 * @param perguntaPesquisa the cd_pergunta value
	 */
	public void setPerguntaPesquisa (br.com.ksisolucoes.vo.basico.pesquisa.PerguntaPesquisa perguntaPesquisa) {
//        br.com.ksisolucoes.vo.basico.pesquisa.PerguntaPesquisa perguntaPesquisaOld = this.perguntaPesquisa;
		this.perguntaPesquisa = perguntaPesquisa;
//        this.getPropertyChangeSupport().firePropertyChange ("perguntaPesquisa", perguntaPesquisaOld, perguntaPesquisa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.pesquisa.PesquisaPergunta)) return false;
		else {
			br.com.ksisolucoes.vo.basico.pesquisa.PesquisaPergunta pesquisaPergunta = (br.com.ksisolucoes.vo.basico.pesquisa.PesquisaPergunta) obj;
			if (null == this.getCodigo() || null == pesquisaPergunta.getCodigo()) return false;
			else return (this.getCodigo().equals(pesquisaPergunta.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}