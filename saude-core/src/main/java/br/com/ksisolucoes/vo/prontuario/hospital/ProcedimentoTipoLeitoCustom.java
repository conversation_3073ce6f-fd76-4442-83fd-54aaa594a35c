package br.com.ksisolucoes.vo.prontuario.hospital;

import java.io.Serializable;

import br.com.ksisolucoes.vo.prontuario.hospital.base.BaseProcedimentoTipoLeitoCustom;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class ProcedimentoTipoLeitoCustom extends BaseProcedimentoTipoLeitoCustom implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ProcedimentoTipoLeitoCustom () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ProcedimentoTipoLeitoCustom (java.lang.Long codigo) {
		super(codigo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}