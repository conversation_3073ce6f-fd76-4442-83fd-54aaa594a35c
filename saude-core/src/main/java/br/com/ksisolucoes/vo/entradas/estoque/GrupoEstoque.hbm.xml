<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="br.com.ksisolucoes.vo.entradas.estoque" >
    <class name="GrupoEstoque" table="grupo_estoque">
        <composite-id class="GrupoEstoquePK" name="Id">
            <key-many-to-one class="EstoqueEmpresa" name="estoqueEmpresa">
                <column name="cod_pro" />
                <column name="empresa" />
            </key-many-to-one>
            
            <key-property
	            column="grupo_estoque"
	            name="grupo"
	            type="string"
            >
                <meta attribute="property-type">java.lang.String</meta>
            </key-property>
     		
     		<key-property 
     			name="codigoDeposito"
     			column="cod_deposito"
     			type="java.lang.Long"
   			/>

            <key-many-to-one
                name="localizacaoEstrutura"
                class="br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura"
                column="cd_localizacao_estrutura"
            />

        </composite-id> <version column="version" name="version" type="long" />
	
	<many-to-one 
		class="Deposito" 
		name="roDeposito"
		insert="false"
		update="false"
	>
       <column name="cod_deposito" />
    </many-to-one>

	<property
            column="laboratorio_fabricante"
            name="laboratorioFabricante"
            type="string"
        />
    <property
        column="estoque_encomendado"
        length="12"
        name="estoqueEncomendado"
        not-null="true"     		
        type="java.lang.Double" 
        />
    <property
        column="estoque_reservado"
        name="estoqueReservado"
        not-null="false"
        type="java.lang.Double" 
        />
    <property
        column="estoque_devolucao"
        name="estoqueDevolucao"
        not-null="false"
        type="java.lang.Double" 
        />
    <property
        column="estoque_reversado_devolucao"
        name="estoqueReservadoDevolucao"
        not-null="false"
        type="java.lang.Double" 
        />
    <property
        column="estoque_fisico"
        length="12"
        name="estoqueFisico"
        not-null="true"
        type="java.lang.Double" 
        />
    <property
        column="estoque_nao_conforme"
	name="estoqueNaoConforme"
        length="12"
	not-null="false"
	type="java.lang.Double"
	/>
    <property
            name="dataUltimaEntrada"
            column="dt_ult_ent"
            type="date"
            not-null="false"
        />
    <property
            name="dataValidade"
            column="dt_validade"
            type="date"
            not-null="false"
        />
    <property
        column="num_ultimo_lancto"
        name="numeroUltimoMovimento"
        not-null="false"
        type="java.lang.Long" 
        />

    <many-to-one
        class="br.com.ksisolucoes.vo.entradas.estoque.Fabricante"
        name="fabricante"
        column="cd_fabricante"
        not-null="false"
    />
        
</class>
</hibernate-mapping>
