package br.com.ksisolucoes.vo.geral;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import java.io.Serializable;

import br.com.ksisolucoes.vo.geral.base.BaseProcessoAtualizacao;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

public class ProcessoAtualizacao extends BaseProcessoAtualizacao implements CodigoManager {

    private static final long serialVersionUID = 1L;

    public enum Status implements IEnum {

        PROCESSAMENTO(1L, Bundle.getStringApplication("rotulo_processamento")),
        PROCESSADO(2L, Bundle.getStringApplication("rotulo_processado")),
        ERRO(3L, Bundle.getStringApplication("rotulo_erro"));

        private Long value;
        private String descricao;

        private Status(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Status valueOf(Long value) {
            for (Status status : Status.values()) {
                if (status.value().equals(value)) {
                    return status;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public ProcessoAtualizacao() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public ProcessoAtualizacao(java.lang.Long codigo) {
        super(codigo);
    }

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getDescricaoStatus() {
        Status status = Status.valueOf(getStatus());
        if (status != null) {
            return status.descricao();
        }
        return null;
    }
}
