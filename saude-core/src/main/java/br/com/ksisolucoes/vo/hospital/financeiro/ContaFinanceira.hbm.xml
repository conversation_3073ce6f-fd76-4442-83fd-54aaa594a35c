<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.hospital.financeiro"  >
    <class name="ContaFinanceira" table="conta_financeira" >
        <id
            column="cd_conta_financeira"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />
        
        <many-to-one class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"
                     name="usuarioCadsus" not-null="true">
            <column name="cd_usuario_cadsus" />
        </many-to-one>
        
        <property 
            name="dataEmissao"
            column="dt_emissao"
            not-null="false"
            type="java.util.Date"
        />
        
        <property 
            name="dataVencimento"
            column="dt_vencimento"
            not-null="false"
            type="java.util.Date"
        />
        
        <property 
            name="dataCriacao"
            column="dt_criacao"
            not-null="true"
            type="timestamp"
        />
        
        <property 
            name="status"
            column="status"
            type="java.lang.Long"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.hospital.financeiro.FormaPagamento"
            column="cd_forma_pagamento"
            name="formaPagamento"
            not-null="false"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente"
            column="cd_conta_paciente"
            name="contaPaciente"
            not-null="false"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuario"
            not-null="true"
        />        
        
        <many-to-one 
            class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
            name="atendimentoPrincipal"
            column="nr_atendimento_principal"
            not-null="true"
         />
        
        <property
            name="valor"
            column="valor"
            not-null="false"
            type="java.lang.Double"
        />
        
        <property
            name="valorPago"
            column="valor_pago"
            not-null="false"
            type="java.lang.Double"
        />
        
        <property
            name="valorDesconto"
            column="valor_desconto"
            not-null="false"
            type="java.lang.Double"
        />
        
    </class>
</hibernate-mapping>
