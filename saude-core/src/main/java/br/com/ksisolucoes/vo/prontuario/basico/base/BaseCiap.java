package br.com.ksisolucoes.vo.prontuario.basico.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the ciap table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="ciap"
 */

public abstract class BaseCiap extends BaseRootVO implements Serializable {

	public static String REF = "Ciap";
	public static final String PROP_CRITERIOS_INCLUSAO = "criteriosInclusao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DEFINICAO = "definicao";
	public static final String PROP_REFERENCIA = "referencia";
	public static final String PROP_COMPONENTE = "componente";
	public static final String PROP_NOTA = "nota";
	public static final String PROP_TITULO_ORIGINAL = "tituloOriginal";
	public static final String PROP_SITUACAO = "situacao";
	public static final String PROP_CONSIDERAR = "considerar";
	public static final String PROP_TITULO_LEIGO = "tituloLeigo";
	public static final String PROP_CAPITULO = "capitulo";
	public static final String PROP_CODIGO_ESUS = "codigoEsus";
	public static final String PROP_CRITERIOS_EXCLUSAO = "criteriosExclusao";
	public static final String PROP_CID_MAIS_FREQUENTE = "cidMaisFrequente";


	// constructors
	public BaseCiap () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseCiap (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseCiap (
		java.lang.Long codigo,
		java.lang.String referencia,
		java.lang.Long componente,
		java.lang.String capitulo,
		java.lang.String tituloOriginal,
		java.lang.String tituloLeigo,
		java.lang.Long situacao) {

		this.setCodigo(codigo);
		this.setReferencia(referencia);
		this.setComponente(componente);
		this.setCapitulo(capitulo);
		this.setTituloOriginal(tituloOriginal);
		this.setTituloLeigo(tituloLeigo);
		this.setSituacao(situacao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String referencia;
	private java.lang.Long componente;
	private java.lang.String capitulo;
	private java.lang.String tituloOriginal;
	private java.lang.String tituloLeigo;
	private java.lang.String definicao;
	private java.lang.String criteriosInclusao;
	private java.lang.String criteriosExclusao;
	private java.lang.String considerar;
	private java.lang.String nota;
	private java.lang.Long situacao;
	private java.lang.String codigoEsus;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.Cid cidMaisFrequente;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_ciap"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: referencia
	 */
	public java.lang.String getReferencia () {
		return getPropertyValue(this, referencia, PROP_REFERENCIA); 
	}

	/**
	 * Set the value related to the column: referencia
	 * @param referencia the referencia value
	 */
	public void setReferencia (java.lang.String referencia) {
//        java.lang.String referenciaOld = this.referencia;
		this.referencia = referencia;
//        this.getPropertyChangeSupport().firePropertyChange ("referencia", referenciaOld, referencia);
	}



	/**
	 * Return the value associated with the column: componente
	 */
	public java.lang.Long getComponente () {
		return getPropertyValue(this, componente, PROP_COMPONENTE); 
	}

	/**
	 * Set the value related to the column: componente
	 * @param componente the componente value
	 */
	public void setComponente (java.lang.Long componente) {
//        java.lang.Long componenteOld = this.componente;
		this.componente = componente;
//        this.getPropertyChangeSupport().firePropertyChange ("componente", componenteOld, componente);
	}



	/**
	 * Return the value associated with the column: capitulo
	 */
	public java.lang.String getCapitulo () {
		return getPropertyValue(this, capitulo, PROP_CAPITULO); 
	}

	/**
	 * Set the value related to the column: capitulo
	 * @param capitulo the capitulo value
	 */
	public void setCapitulo (java.lang.String capitulo) {
//        java.lang.String capituloOld = this.capitulo;
		this.capitulo = capitulo;
//        this.getPropertyChangeSupport().firePropertyChange ("capitulo", capituloOld, capitulo);
	}



	/**
	 * Return the value associated with the column: titulo_original
	 */
	public java.lang.String getTituloOriginal () {
		return getPropertyValue(this, tituloOriginal, PROP_TITULO_ORIGINAL); 
	}

	/**
	 * Set the value related to the column: titulo_original
	 * @param tituloOriginal the titulo_original value
	 */
	public void setTituloOriginal (java.lang.String tituloOriginal) {
//        java.lang.String tituloOriginalOld = this.tituloOriginal;
		this.tituloOriginal = tituloOriginal;
//        this.getPropertyChangeSupport().firePropertyChange ("tituloOriginal", tituloOriginalOld, tituloOriginal);
	}



	/**
	 * Return the value associated with the column: titulo_leigo
	 */
	public java.lang.String getTituloLeigo () {
		return getPropertyValue(this, tituloLeigo, PROP_TITULO_LEIGO); 
	}

	/**
	 * Set the value related to the column: titulo_leigo
	 * @param tituloLeigo the titulo_leigo value
	 */
	public void setTituloLeigo (java.lang.String tituloLeigo) {
//        java.lang.String tituloLeigoOld = this.tituloLeigo;
		this.tituloLeigo = tituloLeigo;
//        this.getPropertyChangeSupport().firePropertyChange ("tituloLeigo", tituloLeigoOld, tituloLeigo);
	}



	/**
	 * Return the value associated with the column: definicao
	 */
	public java.lang.String getDefinicao () {
		return getPropertyValue(this, definicao, PROP_DEFINICAO); 
	}

	/**
	 * Set the value related to the column: definicao
	 * @param definicao the definicao value
	 */
	public void setDefinicao (java.lang.String definicao) {
//        java.lang.String definicaoOld = this.definicao;
		this.definicao = definicao;
//        this.getPropertyChangeSupport().firePropertyChange ("definicao", definicaoOld, definicao);
	}



	/**
	 * Return the value associated with the column: criterios_inclusao
	 */
	public java.lang.String getCriteriosInclusao () {
		return getPropertyValue(this, criteriosInclusao, PROP_CRITERIOS_INCLUSAO); 
	}

	/**
	 * Set the value related to the column: criterios_inclusao
	 * @param criteriosInclusao the criterios_inclusao value
	 */
	public void setCriteriosInclusao (java.lang.String criteriosInclusao) {
//        java.lang.String criteriosInclusaoOld = this.criteriosInclusao;
		this.criteriosInclusao = criteriosInclusao;
//        this.getPropertyChangeSupport().firePropertyChange ("criteriosInclusao", criteriosInclusaoOld, criteriosInclusao);
	}



	/**
	 * Return the value associated with the column: criterios_exclusao
	 */
	public java.lang.String getCriteriosExclusao () {
		return getPropertyValue(this, criteriosExclusao, PROP_CRITERIOS_EXCLUSAO); 
	}

	/**
	 * Set the value related to the column: criterios_exclusao
	 * @param criteriosExclusao the criterios_exclusao value
	 */
	public void setCriteriosExclusao (java.lang.String criteriosExclusao) {
//        java.lang.String criteriosExclusaoOld = this.criteriosExclusao;
		this.criteriosExclusao = criteriosExclusao;
//        this.getPropertyChangeSupport().firePropertyChange ("criteriosExclusao", criteriosExclusaoOld, criteriosExclusao);
	}



	/**
	 * Return the value associated with the column: considerar
	 */
	public java.lang.String getConsiderar () {
		return getPropertyValue(this, considerar, PROP_CONSIDERAR); 
	}

	/**
	 * Set the value related to the column: considerar
	 * @param considerar the considerar value
	 */
	public void setConsiderar (java.lang.String considerar) {
//        java.lang.String considerarOld = this.considerar;
		this.considerar = considerar;
//        this.getPropertyChangeSupport().firePropertyChange ("considerar", considerarOld, considerar);
	}



	/**
	 * Return the value associated with the column: nota
	 */
	public java.lang.String getNota () {
		return getPropertyValue(this, nota, PROP_NOTA); 
	}

	/**
	 * Set the value related to the column: nota
	 * @param nota the nota value
	 */
	public void setNota (java.lang.String nota) {
//        java.lang.String notaOld = this.nota;
		this.nota = nota;
//        this.getPropertyChangeSupport().firePropertyChange ("nota", notaOld, nota);
	}



	/**
	 * Return the value associated with the column: situacao
	 */
	public java.lang.Long getSituacao () {
		return getPropertyValue(this, situacao, PROP_SITUACAO); 
	}

	/**
	 * Set the value related to the column: situacao
	 * @param situacao the situacao value
	 */
	public void setSituacao (java.lang.Long situacao) {
//        java.lang.Long situacaoOld = this.situacao;
		this.situacao = situacao;
//        this.getPropertyChangeSupport().firePropertyChange ("situacao", situacaoOld, situacao);
	}



	/**
	 * Return the value associated with the column: cd_esus
	 */
	public java.lang.String getCodigoEsus () {
		return getPropertyValue(this, codigoEsus, PROP_CODIGO_ESUS); 
	}

	/**
	 * Set the value related to the column: cd_esus
	 * @param codigoEsus the cd_esus value
	 */
	public void setCodigoEsus (java.lang.String codigoEsus) {
//        java.lang.String codigoEsusOld = this.codigoEsus;
		this.codigoEsus = codigoEsus;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoEsus", codigoEsusOld, codigoEsus);
	}



	/**
	 * Return the value associated with the column: cd_cid_mais_frequente
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Cid getCidMaisFrequente () {
		return getPropertyValue(this, cidMaisFrequente, PROP_CID_MAIS_FREQUENTE); 
	}

	/**
	 * Set the value related to the column: cd_cid_mais_frequente
	 * @param cidMaisFrequente the cd_cid_mais_frequente value
	 */
	public void setCidMaisFrequente (br.com.ksisolucoes.vo.prontuario.basico.Cid cidMaisFrequente) {
//        br.com.ksisolucoes.vo.prontuario.basico.Cid cidMaisFrequenteOld = this.cidMaisFrequente;
		this.cidMaisFrequente = cidMaisFrequente;
//        this.getPropertyChangeSupport().firePropertyChange ("cidMaisFrequente", cidMaisFrequenteOld, cidMaisFrequente);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.Ciap)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.Ciap ciap = (br.com.ksisolucoes.vo.prontuario.basico.Ciap) obj;
			if (null == this.getCodigo() || null == ciap.getCodigo()) return false;
			else return (this.getCodigo().equals(ciap.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}