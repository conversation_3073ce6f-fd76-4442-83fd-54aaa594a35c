package br.com.ksisolucoes.vo.basico.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the investigacao_manifestacoes table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_manifestacoes"
 */

public abstract class BaseInvestigacaoManifestacoes extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoManifestacoes";
	public static final String PROP_INVESTIGACAO_AGRAVO = "investigacaoAgravo";
	public static final String PROP_TIPO_EVENTO_ADVERSO = "tipoEventoAdverso";
	public static final String PROP_FLAG_EM_ACOMPANHAMENTO = "flagEmAcompanhamento";
	public static final String PROP_DATA_INICIO_MANIFESTACAO = "dataInicioManifestacao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TIPO_MANIFESTACAO = "tipoManifestacao";
	public static final String PROP_DESCRICAO_OUTROS_EVENTO_ADVERSO = "descricaoOutrosEventoAdverso";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_DATA_TERMINO_MANIFESTACAO = "dataTerminoManifestacao";
	public static final String PROP_TEMPO_INICIO = "tempoInicio";


	// constructors
	public BaseInvestigacaoManifestacoes () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoManifestacoes (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseInvestigacaoManifestacoes (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.InvestigacaoAgravo investigacaoAgravo,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setInvestigacaoAgravo(investigacaoAgravo);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long tipoManifestacao;
	private java.lang.Long tipoEventoAdverso;
	private java.lang.String descricaoOutrosEventoAdverso;
	private java.util.Date dataInicioManifestacao;
	private java.util.Date dataTerminoManifestacao;
	private java.lang.String tempoInicio;
	private java.lang.Long flagEmAcompanhamento;
	private java.util.Date dataCadastro;

	// many to one
	private br.com.ksisolucoes.vo.basico.InvestigacaoAgravo investigacaoAgravo;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_investigacao_manifestacoes"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: tp_manifestacao
	 */
	public java.lang.Long getTipoManifestacao () {
		return getPropertyValue(this, tipoManifestacao, PROP_TIPO_MANIFESTACAO); 
	}

	/**
	 * Set the value related to the column: tp_manifestacao
	 * @param tipoManifestacao the tp_manifestacao value
	 */
	public void setTipoManifestacao (java.lang.Long tipoManifestacao) {
//        java.lang.Long tipoManifestacaoOld = this.tipoManifestacao;
		this.tipoManifestacao = tipoManifestacao;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoManifestacao", tipoManifestacaoOld, tipoManifestacao);
	}



	/**
	 * Return the value associated with the column: tp_evento_adverso
	 */
	public java.lang.Long getTipoEventoAdverso () {
		return getPropertyValue(this, tipoEventoAdverso, PROP_TIPO_EVENTO_ADVERSO); 
	}

	/**
	 * Set the value related to the column: tp_evento_adverso
	 * @param tipoEventoAdverso the tp_evento_adverso value
	 */
	public void setTipoEventoAdverso (java.lang.Long tipoEventoAdverso) {
//        java.lang.Long tipoEventoAdversoOld = this.tipoEventoAdverso;
		this.tipoEventoAdverso = tipoEventoAdverso;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoEventoAdverso", tipoEventoAdversoOld, tipoEventoAdverso);
	}



	/**
	 * Return the value associated with the column: ds_outros_evento_adverso
	 */
	public java.lang.String getDescricaoOutrosEventoAdverso () {
		return getPropertyValue(this, descricaoOutrosEventoAdverso, PROP_DESCRICAO_OUTROS_EVENTO_ADVERSO); 
	}

	/**
	 * Set the value related to the column: ds_outros_evento_adverso
	 * @param descricaoOutrosEventoAdverso the ds_outros_evento_adverso value
	 */
	public void setDescricaoOutrosEventoAdverso (java.lang.String descricaoOutrosEventoAdverso) {
//        java.lang.String descricaoOutrosEventoAdversoOld = this.descricaoOutrosEventoAdverso;
		this.descricaoOutrosEventoAdverso = descricaoOutrosEventoAdverso;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoOutrosEventoAdverso", descricaoOutrosEventoAdversoOld, descricaoOutrosEventoAdverso);
	}



	/**
	 * Return the value associated with the column: dt_inicio
	 */
	public java.util.Date getDataInicioManifestacao () {
		return getPropertyValue(this, dataInicioManifestacao, PROP_DATA_INICIO_MANIFESTACAO); 
	}

	/**
	 * Set the value related to the column: dt_inicio
	 * @param dataInicioManifestacao the dt_inicio value
	 */
	public void setDataInicioManifestacao (java.util.Date dataInicioManifestacao) {
//        java.util.Date dataInicioManifestacaoOld = this.dataInicioManifestacao;
		this.dataInicioManifestacao = dataInicioManifestacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInicioManifestacao", dataInicioManifestacaoOld, dataInicioManifestacao);
	}



	/**
	 * Return the value associated with the column: dt_termino
	 */
	public java.util.Date getDataTerminoManifestacao () {
		return getPropertyValue(this, dataTerminoManifestacao, PROP_DATA_TERMINO_MANIFESTACAO); 
	}

	/**
	 * Set the value related to the column: dt_termino
	 * @param dataTerminoManifestacao the dt_termino value
	 */
	public void setDataTerminoManifestacao (java.util.Date dataTerminoManifestacao) {
//        java.util.Date dataTerminoManifestacaoOld = this.dataTerminoManifestacao;
		this.dataTerminoManifestacao = dataTerminoManifestacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataTerminoManifestacao", dataTerminoManifestacaoOld, dataTerminoManifestacao);
	}



	/**
	 * Return the value associated with the column: tempo_inicio
	 */
	public java.lang.String getTempoInicio () {
		return getPropertyValue(this, tempoInicio, PROP_TEMPO_INICIO); 
	}

	/**
	 * Set the value related to the column: tempo_inicio
	 * @param tempoInicio the tempo_inicio value
	 */
	public void setTempoInicio (java.lang.String tempoInicio) {
//        java.lang.String tempoInicioOld = this.tempoInicio;
		this.tempoInicio = tempoInicio;
//        this.getPropertyChangeSupport().firePropertyChange ("tempoInicio", tempoInicioOld, tempoInicio);
	}



	/**
	 * Return the value associated with the column: flag_em_acompanhamento
	 */
	public java.lang.Long getFlagEmAcompanhamento () {
		return getPropertyValue(this, flagEmAcompanhamento, PROP_FLAG_EM_ACOMPANHAMENTO); 
	}

	/**
	 * Set the value related to the column: flag_em_acompanhamento
	 * @param flagEmAcompanhamento the flag_em_acompanhamento value
	 */
	public void setFlagEmAcompanhamento (java.lang.Long flagEmAcompanhamento) {
//        java.lang.Long flagEmAcompanhamentoOld = this.flagEmAcompanhamento;
		this.flagEmAcompanhamento = flagEmAcompanhamento;
//        this.getPropertyChangeSupport().firePropertyChange ("flagEmAcompanhamento", flagEmAcompanhamentoOld, flagEmAcompanhamento);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: cd_investigacao_agravo
	 */
	public br.com.ksisolucoes.vo.basico.InvestigacaoAgravo getInvestigacaoAgravo () {
		return getPropertyValue(this, investigacaoAgravo, PROP_INVESTIGACAO_AGRAVO); 
	}

	/**
	 * Set the value related to the column: cd_investigacao_agravo
	 * @param investigacaoAgravo the cd_investigacao_agravo value
	 */
	public void setInvestigacaoAgravo (br.com.ksisolucoes.vo.basico.InvestigacaoAgravo investigacaoAgravo) {
//        br.com.ksisolucoes.vo.basico.InvestigacaoAgravo investigacaoAgravoOld = this.investigacaoAgravo;
		this.investigacaoAgravo = investigacaoAgravo;
//        this.getPropertyChangeSupport().firePropertyChange ("investigacaoAgravo", investigacaoAgravoOld, investigacaoAgravo);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.InvestigacaoManifestacoes)) return false;
		else {
			br.com.ksisolucoes.vo.basico.InvestigacaoManifestacoes investigacaoManifestacoes = (br.com.ksisolucoes.vo.basico.InvestigacaoManifestacoes) obj;
			if (null == this.getCodigo() || null == investigacaoManifestacoes.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoManifestacoes.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}