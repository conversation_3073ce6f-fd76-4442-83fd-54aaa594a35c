package br.com.ksisolucoes.vo.vigilancia.investigacao;

import br.com.ksisolucoes.enums.IEnum;

public class InvestigacaoAgravoHantaviroseEnum {

    public enum SimNaoIgnoradoEnum implements IEnum{
        SIM(1L, "Sim"),
        NAO(2L, "Não"),
        NAO_REALIZADO(3L, "Não realizado"),
        IGNORADO(9L, "Ignorado");

        private Long value;
        private String descricao;

        SimNaoIgnoradoEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static SimNaoIgnoradoEnum valueOf(Long value) {
            for (SimNaoIgnoradoEnum v : SimNaoIgnoradoEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }

        public static IEnum[] getSimNaoIgonorado() {
            IEnum[] arr = {SimNaoIgnoradoEnum.SIM, SimNaoIgnoradoEnum.NAO, SimNaoIgnoradoEnum.IGNORADO};
            return arr;
        }
    }

    public enum SimNaoIndeterminadoEnum implements IEnum{
        SIM(1L, "Sim"),
        NAO(2L, "Não"),
        INDETERMINADO(3L, "Indeterminado");

        private Long value;
        private String descricao;

        SimNaoIndeterminadoEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static SimNaoIndeterminadoEnum valueOf(Long value) {
            for (SimNaoIndeterminadoEnum v : SimNaoIndeterminadoEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum ResultadoBEnum implements IEnum{
        NORMAIS(1L, "Normais"),
        AUMENTADOS_COM_DESVIO(2L, " Aumentados COM desvio à esquerda"),
        DIMINUIDOS(3L, "Diminuídos (Leucopenia)"),
        AUMENTADOS_SEM_DESVIO(4L, "Aumentados SEM desvio à esquerda"),
        NAO_REALIZADO(5L, "Não Realizado"),
        IGNORADO(9L, "Ignorado");

        private Long value;
        private String descricao;

        ResultadoBEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static ResultadoBEnum valueOf(Long value) {
            for (ResultadoBEnum v : ResultadoBEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum ResultadoReagenteEnum implements IEnum{
        REAGENTE(1L, "Reagente"),
        NAO_REAGENTE(2L, " Não Reagente"),
        INCONCLUSIVO(3L, "Inconclusivo"),
        NAO_REALIZADO(4L, "Não Realizado");

        private Long value;
        private String descricao;

        ResultadoReagenteEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static ResultadoReagenteEnum valueOf(Long value) {
            for (ResultadoReagenteEnum v : ResultadoReagenteEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum ResultadoPositivoEnum implements IEnum{
        POSITIVO(1L, "Positivo"),
        NEGATIVO(2L, " Negativo"),
        INCONCLUSIVO(3L, "Inconclusivo"),
        NAO_REALIZADO(4L, "Não realizado");

        private Long value;
        private String descricao;

        ResultadoPositivoEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static ResultadoPositivoEnum valueOf(Long value) {
            for (ResultadoPositivoEnum v : ResultadoPositivoEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum ClassificacaoFinalEnum implements IEnum{
        CONFIRMADO(1L, "Confirmado"),
        DESCARTADO(2L, " Descartado");

        private Long value;
        private String descricao;

        ClassificacaoFinalEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static ClassificacaoFinalEnum valueOf(Long value) {
            for (ClassificacaoFinalEnum v : ClassificacaoFinalEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum FormaClinicaEnum implements IEnum{
        PODROMICA(1L, "Prodrômica ou inespecífica"),
        SINDROME(2L, " Sindrome Cardiopulmonar por Hantavírus");

        private Long value;
        private String descricao;

        FormaClinicaEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static FormaClinicaEnum valueOf(Long value) {
            for (FormaClinicaEnum v : FormaClinicaEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum CriterioDiagnosticoEnum implements IEnum{
        LABORATORIAL(1L, "Laboratorial"),
        CLINICO_EPIDEMIOLOGICO(2L, " Clínico Epidemiológico");

        private Long value;
        private String descricao;

        CriterioDiagnosticoEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static CriterioDiagnosticoEnum valueOf(Long value) {
            for (CriterioDiagnosticoEnum v : CriterioDiagnosticoEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum ZonaEnum implements IEnum{
        URBANA(1L, "Urbana"),
        RURAL(2L, "Rural"),
        PERIURBANA(3L, "Periurbana"),
        IGNORADO(4L, " Ignorado");

        private Long value;
        private String descricao;

        ZonaEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static ZonaEnum valueOf(Long value) {
            for (ZonaEnum v : ZonaEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum TipoAmbienteEnum implements IEnum{
        DOMICILIAR(1L, "Domiciliar"),
        TRABALHO(2L, "Trabalho"),
        LAZER(3L, "Lazer"),
        OUTRO(4L, "Outro"),
        IGNORADO(9L, " Ignorado");

        private Long value;
        private String descricao;

        TipoAmbienteEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static TipoAmbienteEnum valueOf(Long value) {
            for (TipoAmbienteEnum v : TipoAmbienteEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum LocalizacaoEnum implements IEnum{
        SUL(1L, "Sul"),
        NORTE(2L, "Norte"),
        LESTE(3L, "Leste"),
        OESTE(4L, "Oeste");

        private Long value;
        private String descricao;

        LocalizacaoEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static LocalizacaoEnum valueOf(Long value) {
            for (LocalizacaoEnum v : LocalizacaoEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum EvolucaoCasoEnum implements IEnum{
        CURA(1L, "Cura"),
        OBITO_HANTAVIROSE(2L, "Óbito por hantavirose"),
        OBITO_OUTRA_CAUSA(3L, "Óbito por outra causa"),
        IGNORADO(4L, "Ignorado");

        private Long value;
        private String descricao;

        EvolucaoCasoEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static EvolucaoCasoEnum valueOf(Long value) {
            for (EvolucaoCasoEnum v : EvolucaoCasoEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

}
