<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.emergencia"  >
    <class name="LeitoUnidade" table="leito_unidade" >
        <composite-id class="LeitoUnidadePK" name="id">
            <key-many-to-one
            class="br.com.ksisolucoes.vo.basico.Empresa"
            column="empresa"
            name="empresa"
            />
            <key-property
            name="codigo"
            column="cd_leito"
            type="java.lang.Long"
            />
        </composite-id> <version column="version" name="version" type="long" />
        
        <!-- Propriedade somente leitura, utilizada pelo filtro/Criteria -->
        <many-to-one
        class="br.com.ksisolucoes.vo.basico.Empresa"
        column="empresa"
        name="roEmpresa"
        update="false"
        insert="false"
        />
        <!-- Propriedade somente leitura, utilizada pelo filtro/Criteria -->
        <!-- Propriedade somente leitura, utilizada pelo filtro/Criteria -->
        <property
        column="cd_leito"
        name="roCodigo"
        not-null="true"
        type="java.lang.Long"
        update="false"
        insert="false"
        />
        <!-- Propriedade somente leitura, utilizada pelo filtro/Criteria -->
        
        <property
            column="ds_leito"
            length="50"
            name="descricao"
            not-null="true"
            type="string"
        />
        
    </class>
</hibernate-mapping>
