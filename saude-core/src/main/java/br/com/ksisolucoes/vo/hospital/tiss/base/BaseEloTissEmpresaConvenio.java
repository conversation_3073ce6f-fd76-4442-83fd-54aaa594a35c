package br.com.ksisolucoes.vo.hospital.tiss.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;

/**
 * This is an object that contains data related to the elo_tiss_empresa_convenio
 * table. Do not modify this class because it will be overwritten if the
 * configuration file related to this class is modified.
 *
 * @hibernate.class table="elo_tiss_empresa_convenio"
 */
public abstract class BaseEloTissEmpresaConvenio extends BaseRootVO implements Serializable {

    public static String REF = "EloTissEmpresaConvenio";
    public static final String PROP_SENHA = "senha";
    public static final String PROP_LOGIN = "login";
    public static final String PROP_CODIGO = "codigo";
    public static final String PROP_CODIGO_CONTRATADO_EXECUTANTE_ORIGEM = "codigoContratadoExecutanteOrigem";
    public static final String PROP_EMPRESA = "empresa";
    public static final String PROP_CONVENIO = "convenio";
    public static final String PROP_CODIGO_CONTRATADO_EXECUTANTE_DESTINO = "codigoContratadoExecutanteDestino";

    // constructors
    public BaseEloTissEmpresaConvenio() {
        initialize();
    }

    /**
     * Constructor for primary key
     */
    public BaseEloTissEmpresaConvenio(java.lang.Long codigo) {
        this.setCodigo(codigo);
        initialize();
    }

    /**
     * Constructor for required fields
     */
    public BaseEloTissEmpresaConvenio(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.basico.Empresa empresa,
            br.com.ksisolucoes.vo.prontuario.basico.Convenio convenio,
            java.lang.String codigoContratadoExecutanteOrigem,
            java.lang.String codigoContratadoExecutanteDestino) {

        this.setCodigo(codigo);
        this.setEmpresa(empresa);
        this.setConvenio(convenio);
        this.setCodigoContratadoExecutanteOrigem(codigoContratadoExecutanteOrigem);
        this.setCodigoContratadoExecutanteDestino(codigoContratadoExecutanteDestino);
        initialize();
    }

    protected void initialize() {
    }

    private int hashCode = Integer.MIN_VALUE;

    // primary key
    private java.lang.Long codigo;

    // fields
    private java.lang.String codigoContratadoExecutanteOrigem;
    private java.lang.String codigoContratadoExecutanteDestino;
    private java.lang.String login;
    private java.lang.String senha;

    // many to one
    private br.com.ksisolucoes.vo.basico.Empresa empresa;
    private br.com.ksisolucoes.vo.prontuario.basico.Convenio convenio;

    /**
     * Return the unique identifier of this class
     *
     * @hibernate.id generator-class="assigned" column="cd_elo_tiss_emp_conv"
     */
    public java.lang.Long getCodigo() {
        return getPropertyValue(this, codigo, "codigo");
    }

    /**
     * Set the unique identifier of this class
     *
     * @param codigo the new ID
     */
    public void setCodigo(java.lang.Long codigo) {
        this.codigo = codigo;
        this.hashCode = Integer.MIN_VALUE;
    }

    /**
     * Return the value associated with the column: cod_contratado_exec_orig
     */
    public java.lang.String getCodigoContratadoExecutanteOrigem() {
        return getPropertyValue(this, codigoContratadoExecutanteOrigem, PROP_CODIGO_CONTRATADO_EXECUTANTE_ORIGEM);
    }

    /**
     * Set the value related to the column: cod_contratado_exec_orig
     *
     * @param codigoContratadoExecutanteOrigem the cod_contratado_exec_orig
     * value
     */
    public void setCodigoContratadoExecutanteOrigem(java.lang.String codigoContratadoExecutanteOrigem) {
//        java.lang.String codigoContratadoExecutanteOrigemOld = this.codigoContratadoExecutanteOrigem;
        this.codigoContratadoExecutanteOrigem = codigoContratadoExecutanteOrigem;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoContratadoExecutanteOrigem", codigoContratadoExecutanteOrigemOld, codigoContratadoExecutanteOrigem);
    }

    /**
     * Return the value associated with the column: cod_contratado_exec_dest
     */
    public java.lang.String getCodigoContratadoExecutanteDestino() {
        return getPropertyValue(this, codigoContratadoExecutanteDestino, PROP_CODIGO_CONTRATADO_EXECUTANTE_DESTINO);
    }

    /**
     * Set the value related to the column: cod_contratado_exec_dest
     *
     * @param codigoContratadoExecutanteDestino the cod_contratado_exec_dest
     * value
     */
    public void setCodigoContratadoExecutanteDestino(java.lang.String codigoContratadoExecutanteDestino) {
//        java.lang.String codigoContratadoExecutanteDestinoOld = this.codigoContratadoExecutanteDestino;
        this.codigoContratadoExecutanteDestino = codigoContratadoExecutanteDestino;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoContratadoExecutanteDestino", codigoContratadoExecutanteDestinoOld, codigoContratadoExecutanteDestino);
    }

    /**
     * Return the value associated with the column: login
     */
    public java.lang.String getLogin() {
        return getPropertyValue(this, login, PROP_LOGIN);
    }

    /**
     * Set the value related to the column: login
     *
     * @param login the login value
     */
    public void setLogin(java.lang.String login) {
//        java.lang.String loginOld = this.login;
        this.login = login;
//        this.getPropertyChangeSupport().firePropertyChange ("login", loginOld, login);
    }

    /**
     * Return the value associated with the column: senha
     */
    public java.lang.String getSenha() {
        return getPropertyValue(this, senha, PROP_SENHA);
    }

    /**
     * Set the value related to the column: senha
     *
     * @param senha the senha value
     */
    public void setSenha(java.lang.String senha) {
//        java.lang.String senhaOld = this.senha;
        this.senha = senha;
//        this.getPropertyChangeSupport().firePropertyChange ("senha", senhaOld, senha);
    }

    /**
     * Return the value associated with the column: empresa
     */
    public br.com.ksisolucoes.vo.basico.Empresa getEmpresa() {
        return getPropertyValue(this, empresa, PROP_EMPRESA);
    }

    /**
     * Set the value related to the column: empresa
     *
     * @param empresa the empresa value
     */
    public void setEmpresa(br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
        this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
    }

    /**
     * Return the value associated with the column: cd_convenio
     */
    public br.com.ksisolucoes.vo.prontuario.basico.Convenio getConvenio() {
        return getPropertyValue(this, convenio, PROP_CONVENIO);
    }

    /**
     * Set the value related to the column: cd_convenio
     *
     * @param convenio the cd_convenio value
     */
    public void setConvenio(br.com.ksisolucoes.vo.prontuario.basico.Convenio convenio) {
//        br.com.ksisolucoes.vo.prontuario.basico.Convenio convenioOld = this.convenio;
        this.convenio = convenio;
//        this.getPropertyChangeSupport().firePropertyChange ("convenio", convenioOld, convenio);
    }

    public boolean equals(Object obj) {
        if (null == obj) {
            return false;
        }
        if (!(obj instanceof br.com.ksisolucoes.vo.hospital.tiss.EloTissEmpresaConvenio)) {
            return false;
        } else {
            br.com.ksisolucoes.vo.hospital.tiss.EloTissEmpresaConvenio eloTissEmpresaConvenio = (br.com.ksisolucoes.vo.hospital.tiss.EloTissEmpresaConvenio) obj;
            if (null == this.getCodigo() || null == eloTissEmpresaConvenio.getCodigo()) {
                return false;
            } else {
                return (this.getCodigo().equals(eloTissEmpresaConvenio.getCodigo()));
            }
        }
    }

    public int hashCode() {
        if (Integer.MIN_VALUE == this.hashCode) {
            if (null == this.getCodigo()) {
                return super.hashCode();
            } else {
                String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
                this.hashCode = hashStr.hashCode();
            }
        }
        return this.hashCode;
    }

    public String toString() {
        return super.toString();
    }

    private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
        if (this.retornoValidacao == null) {
            this.retornoValidacao = new RetornoValidacao();
        }
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
        this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}
