package br.com.ksisolucoes.vo.basico;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import java.io.Serializable;

import br.com.ksisolucoes.vo.basico.base.BaseEmpresaBpa;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

public class EmpresaBpa extends BaseEmpresaBpa implements CodigoManager {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public EmpresaBpa() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public EmpresaBpa(java.lang.Long codigo) {
        super(codigo);
    }

    /*[CONSTRUCTOR MARKER END]*/
    public enum OrigemDados implements IEnum {

        ATENDIMENTO(0L, Bundle.getStringApplication("rotulo_atendimento")),
        CONTA_PACIENTE(1L, Bundle.getStringApplication("rotulo_conta_paciente"));

        private OrigemDados(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        private final Long value;
        private final String descricao;

        @Override
        public String toString() {
            return descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static OrigemDados valueOf(Long value) {
            for (OrigemDados origemDados : OrigemDados.values()) {
                if (origemDados.value().equals(value)) {
                    return origemDados;
                }
            }
            return null;
        }
    }

    public String getDescricaoOrigemDados() {
        OrigemDados origemDados = OrigemDados.valueOf(getOrigemDados());
        if (origemDados != null) {
            return origemDados.descricao();
        }
        return Bundle.getStringApplication("rotulo_ambos");
    }

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}
