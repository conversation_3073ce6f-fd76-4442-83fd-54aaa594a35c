<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.cadsus"  >
    <class name="UsuarioCadsusPatologia" table="usuario_cadsus_patologia" >
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_cadsus_patologia"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"
            column="cd_usu_cadsus"
            name="usuarioCadsus"
            not-null="true"
            />

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.Cid"
            column="cd_cid"
            name="cid"
            not-null="true"
            />

        <property
            name="dataAtendimento"
            type="java.util.Date"
            not-null="true"
            column="dt_atendimento"
            />

        <property
            name="observacao"
            column="observacao"
            not-null="false"
            type="java.lang.String"
            />

        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.Profissional"
            column="cd_profissional"
            name="profissional"
            not-null="false"
            />

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
            name="atendimento"
            not-null="false"
        >
            <column name="nr_atendimento"/>
        </many-to-one>

        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuario"
            not-null="true"
            />

        <property
            name="dataUsuario"
            column="dt_usuario"
            not-null="true"
            type="java.util.Date"
            />

    </class>
</hibernate-mapping>
