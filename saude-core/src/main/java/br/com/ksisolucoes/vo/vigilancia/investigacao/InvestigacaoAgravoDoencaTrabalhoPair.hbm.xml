<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.investigacao">

    <class name="InvestigacaoAgravoDoencaTrabalhoPair" table="investigacao_agr_pair">

        <id name="codigo"
            type="java.lang.Long"
            column="cd_investigacao_agr_pair" >
            <generator class="sequence">
                <param name="sequence">seq_investigacao_agr_pair</param>
            </generator>
        </id>

        <version column="version" name="version" type="long"/>

        <property
                name="flagInformacoesComplementares"
                column="flag_informacoes_complementares"
                type="java.lang.String"
                not-null="true"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo"
                name="registroAgravo"
                not-null="true"
                column="cd_registro_agravo"
        />

        <!-- INVESTIGAÇÃO -->
        <property
                name="dataInvestigacao"
                column="dt_investigacao"
                type="java.util.Date"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"
                name="ocupacaoCbo"
                not-null="false"
                column="ocupacao_cbo"
        />

        <!-- ANTECEDENTES EPIDEMIOLOGICOS -->
        <property
                name="situacaoMercadoTrabalho"
                column="tp_situacao_mercado_trabalho"
                type="java.lang.Long"
        />
        <property
                name="tempoTrabalhoOcupacao"
                column="tempo_trabalho_ocupacao"
                type="java.lang.String"
                length="2"
        />
        <property
                name="tempoTrabalhoOcupacaoUnidadeMedida"
                column="tempo_trabalho_ocupacao_um"
                type="java.lang.Long"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Empresa"
                column="cd_empresa_contratante"
                name="empresaContratante"
                not-null="false"
        />
        <property
                name="empresaDistrito"
                column="empresa_distrito"
                type="java.lang.String"
        />
        <property
                name="empresaPontoReferencia"
                column="empresa_ponto_referencia"
                type="java.lang.String"
        />
        <property
                name="empresaTelefone"
                column="empresa_telefone"
                type="java.lang.String"
        />
        <property
                name="empresaTerceirizada"
                column="sn_empresa_terceirizada"
                type="java.lang.Long"
        />

        <property
                name="agravosAssociadosHipertensao"
                column="agravos_associados_hipertensao"
                type="java.lang.Long"
        />
        <property
                name="agravosAssociadosTuberculose"
                column="agravos_associados_tuberculose"
                type="java.lang.Long"
        />
        <property
                name="agravosAssociadosDiabetes"
                column="agravos_associados_diabetes"
                type="java.lang.Long"
        />
        <property
                name="agravosAssociadosAsma"
                column="agravos_associados_asma"
                type="java.lang.Long"
        />
        <property
                name="agravosAssociadosHanseniase"
                column="agravos_associados_hanseniase"
                type="java.lang.Long"
        />
        <property
                name="agravosAssociadosTranstornoMental"
                column="agravos_associados_transtorno_mental"
                type="java.lang.Long"
        />
        <property
                name="agravosAssociadosOutros"
                column="agravos_associados_outros"
                type="java.lang.String"
                length="100"
        />

        <property
                name="tempoExposicaoAgenteRisco"
                column="tempo_exposicao_agente_risco"
                type="java.lang.String"
        />
        <property
                name="tempoExposicaoAgenteRiscoUnidadeMedida"
                column="tempo_exposicao_agente_risco_um"
                type="java.lang.Long"
        />

        <property
                name="regimeTratamento"
                column="tp_regime_tratamento"
                type="java.lang.Long"
        />


        <!-- PAIR -->
        <property
                name="tipoRuidoPredominante"
                column="tp_ruido_predominante"
                type="java.lang.Long"
        />
        <property
                name="ruidoSolventeTolueno"
                column="ruido_solvente_tolueno"
                type="java.lang.Long"
        />
        <property
                name="ruidoGasesToxicos"
                column="ruido_gases_toxicos"
                type="java.lang.Long"
        />
        <property
                name="ruidoMetaisPesados"
                column="ruido_metais_pesados"
                type="java.lang.Long"
        />
        <property
                name="ruidoMedicamentosOtotoxicos"
                column="ruido_medicamentos_ototoxicos"
                type="java.lang.Long"
        />
        <property
                name="ruidosOutros"
                column="ruido_outros"
                type="java.lang.String"
                length="100"
        />

        <property
                name="sintomasZumbido"
                column="sintomas_zumbido"
                type="java.lang.Long"
        />
        <property
                name="sintomasCefaleia"
                column="sintomas_cefaleia"
                type="java.lang.Long"
        />
        <property
                name="sintomasTontura"
                column="sintomas_tontura"
                type="java.lang.Long"
        />
        <property
                name="sintomasDificuldadeFala"
                column="sintomas_dificuldade_fala"
                type="java.lang.Long"
        />
        <property
                name="sintomasOutros"
                column="sintomas_outros"
                type="java.lang.String"
                length="100"
        />

        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.Cid"
                     name="diagnosticoEspecifico">
            <column name="diagnostico_especifico" />
        </many-to-one>


        <!-- CONCLUSÃO -->
        <property
                name="afastamentoParaTratamento"
                column="afastamento_para_tratamento"
                type="java.lang.Long"
        />
        <property
                name="tempoAfastamento"
                column="tempo_afastamento"
                type="java.lang.String"
                length="2"
        />
        <property
                name="tempoAfastamentoUnidadeMedida"
                column="tempo_afastamento_um"
                type="java.lang.Long"
        />
        <property
                name="conclusaoAfastamento"
                column="conclusao_afastamento"
                type="java.lang.Long"
        />
        <property
                name="outrosTrabalhadoresMesmaDoenca"
                column="outros_trabalhadores_mesma_doenca"
                type="java.lang.Long"
        />

        <property
                name="condutaGeralAfastamentoAgenteRisco"
                column="conduta_geral_afastamento_agente_risco"
                type="java.lang.Long"
        />
        <property
                name="condutaGeralMudancaOrganizacaoTrabalho"
                column="conduta_geral_mudanca_organizacao_trabalho"
                type="java.lang.Long"
        />
        <property
                name="condutaGeralProtecaoColetiva"
                column="conduta_geral_protecao_coletiva"
                type="java.lang.Long"
        />
        <property
                name="condutaGeralProtecaoIndividual"
                column="conduta_geral_protecao_individual"
                type="java.lang.Long"
        />
        <property
                name="condutaGeralNenhum"
                column="conduta_geral_nenhum"
                type="java.lang.Long"
        />
        <property
                name="condutaGeralAfastamentoLocalTrabalho"
                column="conduta_geral_afastamento_local_trabalho"
                type="java.lang.Long"
        />
        <property
                name="condutaGeralOutros"
                column="conduta_geral_outros"
                type="java.lang.String"
                length="100"
        />

        <property
                name="evolucaoCaso"
                column="evolucao_caso"
                type="java.lang.Long"
        />
        <property
                name="dataObito"
                column="dt_obito"
                type="java.util.Date"
        />
        <property
                name="emitidaCat"
                column="emitida_cat"
                type="java.lang.Long"
        />

        <!-- OBS -->
        <property
                name="observacao"
                column="observacao"
                type="java.lang.String"
                length="5000"
        />

        <!-- ENCERRAMENTO -->
        <property
                name="dataEncerramento"
                column="dt_encerramento"
                type="java.util.Date"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                name="usuarioEncerramento"
                not-null="false"
                column="cd_usuario_encerramento"
        />

    </class>
</hibernate-mapping>