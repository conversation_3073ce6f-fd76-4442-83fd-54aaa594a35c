<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD//EN" "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico">
    <class name="SolicitacaoAgendamentoPosicaoFila" table="solicitacao_agendamento_posicao_fila">

        <id name="codigo" type="java.lang.Long">
            <column name="cd_solicitacao" />
            <generator class="assigned">
                <param name="property">solicitacao_agendamento</param>
            </generator>
        </id>

        <one-to-one
                class="br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento"
                name="solicitacaoAgendamento"
        />

        <property name="posicaoFilaEspera" column="posicao_fila_espera" type="java.lang.Long"/>

        <property name="posicaoFilaEsperaManual" column="posicao_fila_espera_manual" type="java.lang.Long"/>

    </class>
</hibernate-mapping>
