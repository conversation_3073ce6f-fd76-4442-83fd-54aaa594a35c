package br.com.ksisolucoes.vo.prontuario.hospital.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the leito_desativacao table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="leito_desativacao"
 */

public abstract class BaseLeitoDesativacao extends BaseRootVO implements Serializable {

	public static String REF = "LeitoDesativacao";
	public static final String PROP_USUARIO_REATIVACAO = "usuarioReativacao";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_LEITO_QUARTO = "leitoQuarto";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_DESATIVACAO = "dataDesativacao";
	public static final String PROP_MOTIVO = "motivo";
	public static final String PROP_DATA_REATIVACAO = "dataReativacao";


	// constructors
	public BaseLeitoDesativacao () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseLeitoDesativacao (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseLeitoDesativacao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto leitoQuarto,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataDesativacao) {

		this.setCodigo(codigo);
		this.setLeitoQuarto(leitoQuarto);
		this.setUsuario(usuario);
		this.setDataDesativacao(dataDesativacao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataDesativacao;
	private java.util.Date dataReativacao;
	private java.lang.String motivo;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto leitoQuarto;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioReativacao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_leito_desativacao"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_desativacao
	 */
	public java.util.Date getDataDesativacao () {
		return getPropertyValue(this, dataDesativacao, PROP_DATA_DESATIVACAO); 
	}

	/**
	 * Set the value related to the column: dt_desativacao
	 * @param dataDesativacao the dt_desativacao value
	 */
	public void setDataDesativacao (java.util.Date dataDesativacao) {
//        java.util.Date dataDesativacaoOld = this.dataDesativacao;
		this.dataDesativacao = dataDesativacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataDesativacao", dataDesativacaoOld, dataDesativacao);
	}



	/**
	 * Return the value associated with the column: dt_reativacao
	 */
	public java.util.Date getDataReativacao () {
		return getPropertyValue(this, dataReativacao, PROP_DATA_REATIVACAO); 
	}

	/**
	 * Set the value related to the column: dt_reativacao
	 * @param dataReativacao the dt_reativacao value
	 */
	public void setDataReativacao (java.util.Date dataReativacao) {
//        java.util.Date dataReativacaoOld = this.dataReativacao;
		this.dataReativacao = dataReativacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataReativacao", dataReativacaoOld, dataReativacao);
	}



	/**
	 * Return the value associated with the column: motivo
	 */
	public java.lang.String getMotivo () {
		return getPropertyValue(this, motivo, PROP_MOTIVO); 
	}

	/**
	 * Set the value related to the column: motivo
	 * @param motivo the motivo value
	 */
	public void setMotivo (java.lang.String motivo) {
//        java.lang.String motivoOld = this.motivo;
		this.motivo = motivo;
//        this.getPropertyChangeSupport().firePropertyChange ("motivo", motivoOld, motivo);
	}



	/**
	 * Return the value associated with the column: cd_leito
	 */
	public br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto getLeitoQuarto () {
		return getPropertyValue(this, leitoQuarto, PROP_LEITO_QUARTO); 
	}

	/**
	 * Set the value related to the column: cd_leito
	 * @param leitoQuarto the cd_leito value
	 */
	public void setLeitoQuarto (br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto leitoQuarto) {
//        br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto leitoQuartoOld = this.leitoQuarto;
		this.leitoQuarto = leitoQuarto;
//        this.getPropertyChangeSupport().firePropertyChange ("leitoQuarto", leitoQuartoOld, leitoQuarto);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_usuario_reativacao
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioReativacao () {
		return getPropertyValue(this, usuarioReativacao, PROP_USUARIO_REATIVACAO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_reativacao
	 * @param usuarioReativacao the cd_usuario_reativacao value
	 */
	public void setUsuarioReativacao (br.com.ksisolucoes.vo.controle.Usuario usuarioReativacao) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioReativacaoOld = this.usuarioReativacao;
		this.usuarioReativacao = usuarioReativacao;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioReativacao", usuarioReativacaoOld, usuarioReativacao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.hospital.LeitoDesativacao)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.hospital.LeitoDesativacao leitoDesativacao = (br.com.ksisolucoes.vo.prontuario.hospital.LeitoDesativacao) obj;
			if (null == this.getCodigo() || null == leitoDesativacao.getCodigo()) return false;
			else return (this.getCodigo().equals(leitoDesativacao.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}