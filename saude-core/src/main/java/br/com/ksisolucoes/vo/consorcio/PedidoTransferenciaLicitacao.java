package br.com.ksisolucoes.vo.consorcio;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.EnumUtil;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.consorcio.base.BasePedidoTransferenciaLicitacao;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;



public class PedidoTransferenciaLicitacao extends BasePedidoTransferenciaLicitacao implements CodigoManager {
	private static final long serialVersionUID = 1L;

    public static final String PROP_DESCRICAO_STATUS = "descricaoStatus";
    public static final String PROP_DESCRICAO_FLAG_OC = "descricaoFlagOc";
    public static final String PROP_DESCRICAO_SITUACAO_CONTROLE_FINANCEIRO = "descricaoSituacaoControleFinanceiro";

    public enum StatusPedidoTransferenciaLicitacao implements IEnum<StatusPedidoTransferenciaLicitacao> {

        ABERTO(0L, Bundle.getStringApplication("rotulo_aberto")),
        CANCELADO(1L, Bundle.getStringApplication("rotulo_cancelado")),
        ENCAMINHADO(2L, Bundle.getStringApplication("rotulo_encaminhado")),
        SEPARADO(3L, Bundle.getStringApplication("rotulo_separado")),
        FECHADO(4L, Bundle.getStringApplication("rotulo_fechado")),
        ;
        private Long value;
        private String descricao;

        private StatusPedidoTransferenciaLicitacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        @Override
        public Long value() {
            return value;
        }
    }

    public enum SituacaoControleFinanceiro implements IEnum {
        PENDENTE(0L, Bundle.getStringApplication("rotulo_pendente")),
        APROVADO(1L, Bundle.getStringApplication("rotulo_aprovado")),
        NAO_APROVADO(2L, Bundle.getStringApplication("rotulo_nao_aprovado")),
        ;

        private Long value;
        private String descricao;

        private SituacaoControleFinanceiro(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static SituacaoControleFinanceiro valeuOf(Long value) {
            for (SituacaoControleFinanceiro situacaoControleFinanceiro : SituacaoControleFinanceiro.values()) {
                if (situacaoControleFinanceiro.value().equals(value)) {
                    return situacaoControleFinanceiro;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }
        
/*[CONSTRUCTOR MARKER BEGIN]*/
	public PedidoTransferenciaLicitacao () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public PedidoTransferenciaLicitacao (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public PedidoTransferenciaLicitacao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		br.com.ksisolucoes.vo.basico.Empresa empresaAlmoxarifado,
		br.com.ksisolucoes.vo.basico.Empresa empresaConsorciado,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
		java.util.Date dataCadastro,
		java.util.Date dataUsuario,
		java.lang.Long status,
		java.lang.Long situacaoControleFinanceiro) {

		super (
			codigo,
			empresa,
			empresaAlmoxarifado,
			empresaConsorciado,
			usuario,
			usuarioCadastro,
			dataCadastro,
			dataUsuario,
			status,
			situacaoControleFinanceiro);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public String getDescricaoStatus() {
        return getDescricaoStatus(getStatus());
    }
    
    public static String getDescricaoStatus(Long status) {
        return new EnumUtil().resolveDescricao(StatusPedidoTransferenciaLicitacao.values(), status);
    }

    public String getDescricaoSituacaoControleFinanceiro(){
        SituacaoControleFinanceiro situacaoControleFinanceiro = SituacaoControleFinanceiro.valeuOf(getSituacaoControleFinanceiro());
        if (situacaoControleFinanceiro != null && situacaoControleFinanceiro.descricao != null) {
            return situacaoControleFinanceiro.descricao();
        }
        return "";
    }

    public Long getAnoCadastro() {
        if (getDataCadastro() == null)
            return null;
        return (long) DataUtil.getAno(getDataCadastro());
    }

    public String getDescricaoFlagOc() {
        if (RepositoryComponentDefault.SIM_LONG.equals(getFlagOrdemCompra())) {
            return Bundle.getStringApplication("rotulo_sim");
        } else
            return Bundle.getStringApplication("rotulo_nao");
    }
}