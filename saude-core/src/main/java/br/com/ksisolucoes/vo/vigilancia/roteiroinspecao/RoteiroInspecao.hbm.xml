<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.roteiroinspecao"  >
    <class name="RoteiroInspecao" table="roteiro_inspecao" >
        <id
            column="cd_roteiro_insp"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id>

        <version column="version" name="version" type="long" />    

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento"
            column="cd_atividade_estabelecimento"
            name="atividadeEstabelecimento"
            not-null="true"
        />

        <property
            column="nm_roteiro"
            name="nomeRoteiro"
            type="java.lang.String"
            not-null="true"
        />

        <property
            column="ds_enquad_legal"
            name="enquadramentoLegal"
            type="java.lang.String"
            not-null="false"
        />

        <property
            column="obs_inicial"
            name="observacaoInicial"
            type="java.lang.String"
            not-null="false"
        />

        <property
            column="obs_final"
            name="observacaoFinal"
            type="java.lang.String"
            not-null="false"
        />

        <property
            column="dt_cadastro"
            name="dataCadastro"
            type="timestamp"
            not-null="true"
        />

        <property
            column="dt_usuario"
            name="dataUsuario"
            type="timestamp"
            not-null="true"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuario"
            not-null="true"
        />
    </class>
</hibernate-mapping>