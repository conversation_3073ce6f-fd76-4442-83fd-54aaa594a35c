package br.com.ksisolucoes.vo.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the investigacao_agravo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agravo"
 */

public abstract class BaseInvestigacaoAgravo extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravo";
	public static final String PROP_INFORMACOES_COMPLEMENTARES = "informacoesComplementares";
	public static final String PROP_DIAGNOSTICO = "diagnostico";
	public static final String PROP_EMPRESA_INVESTIGACAO = "empresaInvestigacao";
	public static final String PROP_PAIS_VIAGEM = "paisViagem";
	public static final String PROP_TIPO_ATENDIMENTO_MEDICO = "tipoAtendimentoMedico";
	public static final String PROP_MUNICIPIO_VIAGEM = "municipioViagem";
	public static final String PROP_DESCRICAO_OUTROS_CLASSIFICACAO_CASUAL = "descricaoOutrosClassificacaoCasual";
	public static final String PROP_ERROS_IMUNIZACAO = "errosImunizacao";
	public static final String PROP_EVOLUCAO = "evolucao";
	public static final String PROP_DESCRICAO_OUTROS_ERROS_IMUNIZACAO = "descricaoOutrosErrosImunizacao";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_PROFISSIONAL_RESPONSAVEL_INVESTIGACAO = "profissionalResponsavelInvestigacao";
	public static final String PROP_HISTORIA_PREVIA_CONVULSAO = "historiaPreviaConvulsao";
	public static final String PROP_DESCRICAO_OUTROS_DOENCAS_EXISTENTES = "descricaoOutrosDoencasExistentes";
	public static final String PROP_DOENCA_PRE_EXISTENTES = "doencaPreExistentes";
	public static final String PROP_MUNICIPIO_ATENDIMENTO_MEDICO = "municipioAtendimentoMedico";
	public static final String PROP_DATA_TERMINO_VIAGEM = "dataTerminoViagem";
	public static final String PROP_EMPRESA_ENCERRAMENTO = "empresaEncerramento";
	public static final String PROP_CNES_HOSPITAL_ATENDIMENTO_MEDICO = "cnesHospitalAtendimentoMedico";
	public static final String PROP_DATA_TRANSFUSAO_ULTIMO_MES = "dataTransfusaoUltimoMes";
	public static final String PROP_CONDUTA_ERROS_IMUNIZACAO_ITEM = "condutaErrosImunizacaoItem";
	public static final String PROP_VIAJOU_ULTIMOS_DIAS = "viajouUltimosDias";
	public static final String PROP_FLAG_MULHER_AMAMENTANDO = "flagMulherAmamentando";
	public static final String PROP_CLASSIFICACAO = "classificacao";
	public static final String PROP_EAPV_PRESENTE = "eapvPresente";
	public static final String PROP_DATA_ALTA_ATENDIMENTO_MEDICO = "dataAltaAtendimentoMedico";
	public static final String PROP_DATA_NOTIFICACAO = "dataNotificacao";
	public static final String PROP_SOMATORIO_DOENCAS_EXISTENTES = "somatorioDoencasExistentes";
	public static final String PROP_UF_ATENDIMENTO_MEDICO = "ufAtendimentoMedico";
	public static final String PROP_DATA_INICIO_VIAGEM = "dataInicioViagem";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_FLAG_CRIANCA_ALEITAMENTO = "flagCriancaAleitamento";
	public static final String PROP_UF_VIAGEM = "ufViagem";
	public static final String PROP_REGISTRO_AGRAVO = "registroAgravo";
	public static final String PROP_CLASSIFICACAO_FINAL = "classificacaoFinal";
	public static final String PROP_DESCRICAO_OUTROS_CONDUTA_ERROS_IMUNIZACAO_ITEM = "descricaoOutrosCondutaErrosImunizacaoItem";
	public static final String PROP_PROFISSIONAL_RESPONSAVEL_ENCERRAMENTO = "profissionalResponsavelEncerramento";
	public static final String PROP_MEDICACAO_USO = "medicacaoUso";
	public static final String PROP_EVENTO_ADVERSO = "eventoAdverso";
	public static final String PROP_TRANSFUSAO_ULTIMO_MES = "transfusaoUltimoMes";
	public static final String PROP_LOCAL_VIAGEM = "localViagem";
	public static final String PROP_NOME_HOSPITAL_ATENDIMENTO_MEDICO = "nomeHospitalAtendimentoMedico";
	public static final String PROP_CONDUTA_ERROS_IMUNIZACAO = "condutaErrosImunizacao";
	public static final String PROP_EMPRESA_NOTIFICACAO = "empresaNotificacao";
	public static final String PROP_DATA_INTERNACAO_ATENDIMENTO_MEDICO = "dataInternacaoAtendimentoMedico";
	public static final String PROP_ATENDIMENTO_MEDICO = "atendimentoMedico";
	public static final String PROP_PROFISSIONAL_RESPONSAVEL_NOTIFICACAO = "profissionalResponsavelNotificacao";
	public static final String PROP_DESCRICAO_OUTROS_CONDUTA_FRENTE_ESQUEMA_VACINACAO = "descricaoOutrosCondutaFrenteEsquemaVacinacao";


	// constructors
	public BaseInvestigacaoAgravo () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravo (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseInvestigacaoAgravo (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
		br.com.ksisolucoes.vo.cadsus.Profissional profissionalResponsavelNotificacao,
		java.util.Date dataCadastro,
		java.util.Date dataNotificacao) {

		this.setCodigo(codigo);
		this.setRegistroAgravo(registroAgravo);
		this.setProfissionalResponsavelNotificacao(profissionalResponsavelNotificacao);
		this.setDataCadastro(dataCadastro);
		this.setDataNotificacao(dataNotificacao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataCadastro;
	private java.util.Date dataNotificacao;
	private java.lang.Long flagCriancaAleitamento;
	private java.lang.Long flagMulherAmamentando;
	private java.lang.String eventoAdverso;
	private java.lang.Long classificacao;
	private java.lang.Long eapvPresente;
	private java.lang.Long doencaPreExistentes;
	private java.lang.Long somatorioDoencasExistentes;
	private java.lang.String descricaoOutrosDoencasExistentes;
	private java.lang.Long medicacaoUso;
	private java.lang.Long transfusaoUltimoMes;
	private java.util.Date dataTransfusaoUltimoMes;
	private java.lang.Long historiaPreviaConvulsao;
	private java.lang.Long viajouUltimosDias;
	private java.util.Date dataInicioViagem;
	private java.util.Date dataTerminoViagem;
	private java.lang.String paisViagem;
	private java.lang.String localViagem;
	private java.lang.String ufViagem;
	private java.lang.String municipioViagem;
	private java.lang.Long atendimentoMedico;
	private java.lang.Long tipoAtendimentoMedico;
	private java.lang.String cnesHospitalAtendimentoMedico;
	private java.lang.String nomeHospitalAtendimentoMedico;
	private java.util.Date dataInternacaoAtendimentoMedico;
	private java.util.Date dataAltaAtendimentoMedico;
	private java.lang.String municipioAtendimentoMedico;
	private java.lang.String ufAtendimentoMedico;
	private java.lang.String informacoesComplementares;
	private java.lang.String diagnostico;
	private java.lang.Long evolucao;
	private java.lang.Long classificacaoFinal;
	private java.lang.Long errosImunizacao;
	private java.lang.String descricaoOutrosErrosImunizacao;
	private java.lang.Long condutaErrosImunizacao;
	private java.lang.Long condutaErrosImunizacaoItem;
	private java.lang.String descricaoOutrosCondutaErrosImunizacaoItem;
	private java.lang.String descricaoOutrosCondutaFrenteEsquemaVacinacao;
	private java.lang.String descricaoOutrosClassificacaoCasual;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissionalResponsavelNotificacao;
	private br.com.ksisolucoes.vo.basico.Empresa empresaNotificacao;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissionalResponsavelInvestigacao;
	private br.com.ksisolucoes.vo.basico.Empresa empresaInvestigacao;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissionalResponsavelEncerramento;
	private br.com.ksisolucoes.vo.basico.Empresa empresaEncerramento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_investigacao_agravo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_notificacao
	 */
	public java.util.Date getDataNotificacao () {
		return getPropertyValue(this, dataNotificacao, PROP_DATA_NOTIFICACAO); 
	}

	/**
	 * Set the value related to the column: dt_notificacao
	 * @param dataNotificacao the dt_notificacao value
	 */
	public void setDataNotificacao (java.util.Date dataNotificacao) {
//        java.util.Date dataNotificacaoOld = this.dataNotificacao;
		this.dataNotificacao = dataNotificacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataNotificacao", dataNotificacaoOld, dataNotificacao);
	}



	/**
	 * Return the value associated with the column: flag_crianca_aleitamento
	 */
	public java.lang.Long getFlagCriancaAleitamento () {
		return getPropertyValue(this, flagCriancaAleitamento, PROP_FLAG_CRIANCA_ALEITAMENTO); 
	}

	/**
	 * Set the value related to the column: flag_crianca_aleitamento
	 * @param flagCriancaAleitamento the flag_crianca_aleitamento value
	 */
	public void setFlagCriancaAleitamento (java.lang.Long flagCriancaAleitamento) {
//        java.lang.Long flagCriancaAleitamentoOld = this.flagCriancaAleitamento;
		this.flagCriancaAleitamento = flagCriancaAleitamento;
//        this.getPropertyChangeSupport().firePropertyChange ("flagCriancaAleitamento", flagCriancaAleitamentoOld, flagCriancaAleitamento);
	}



	/**
	 * Return the value associated with the column: flag_mulher_amamentando
	 */
	public java.lang.Long getFlagMulherAmamentando () {
		return getPropertyValue(this, flagMulherAmamentando, PROP_FLAG_MULHER_AMAMENTANDO); 
	}

	/**
	 * Set the value related to the column: flag_mulher_amamentando
	 * @param flagMulherAmamentando the flag_mulher_amamentando value
	 */
	public void setFlagMulherAmamentando (java.lang.Long flagMulherAmamentando) {
//        java.lang.Long flagMulherAmamentandoOld = this.flagMulherAmamentando;
		this.flagMulherAmamentando = flagMulherAmamentando;
//        this.getPropertyChangeSupport().firePropertyChange ("flagMulherAmamentando", flagMulherAmamentandoOld, flagMulherAmamentando);
	}



	/**
	 * Return the value associated with the column: evento_adverso
	 */
	public java.lang.String getEventoAdverso () {
		return getPropertyValue(this, eventoAdverso, PROP_EVENTO_ADVERSO); 
	}

	/**
	 * Set the value related to the column: evento_adverso
	 * @param eventoAdverso the evento_adverso value
	 */
	public void setEventoAdverso (java.lang.String eventoAdverso) {
//        java.lang.String eventoAdversoOld = this.eventoAdverso;
		this.eventoAdverso = eventoAdverso;
//        this.getPropertyChangeSupport().firePropertyChange ("eventoAdverso", eventoAdversoOld, eventoAdverso);
	}



	/**
	 * Return the value associated with the column: classificacao
	 */
	public java.lang.Long getClassificacao () {
		return getPropertyValue(this, classificacao, PROP_CLASSIFICACAO); 
	}

	/**
	 * Set the value related to the column: classificacao
	 * @param classificacao the classificacao value
	 */
	public void setClassificacao (java.lang.Long classificacao) {
//        java.lang.Long classificacaoOld = this.classificacao;
		this.classificacao = classificacao;
//        this.getPropertyChangeSupport().firePropertyChange ("classificacao", classificacaoOld, classificacao);
	}



	/**
	 * Return the value associated with the column: eapv_presente
	 */
	public java.lang.Long getEapvPresente () {
		return getPropertyValue(this, eapvPresente, PROP_EAPV_PRESENTE); 
	}

	/**
	 * Set the value related to the column: eapv_presente
	 * @param eapvPresente the eapv_presente value
	 */
	public void setEapvPresente (java.lang.Long eapvPresente) {
//        java.lang.Long eapvPresenteOld = this.eapvPresente;
		this.eapvPresente = eapvPresente;
//        this.getPropertyChangeSupport().firePropertyChange ("eapvPresente", eapvPresenteOld, eapvPresente);
	}



	/**
	 * Return the value associated with the column: doenca_pre_existentes
	 */
	public java.lang.Long getDoencaPreExistentes () {
		return getPropertyValue(this, doencaPreExistentes, PROP_DOENCA_PRE_EXISTENTES); 
	}

	/**
	 * Set the value related to the column: doenca_pre_existentes
	 * @param doencaPreExistentes the doenca_pre_existentes value
	 */
	public void setDoencaPreExistentes (java.lang.Long doencaPreExistentes) {
//        java.lang.Long doencaPreExistentesOld = this.doencaPreExistentes;
		this.doencaPreExistentes = doencaPreExistentes;
//        this.getPropertyChangeSupport().firePropertyChange ("doencaPreExistentes", doencaPreExistentesOld, doencaPreExistentes);
	}



	/**
	 * Return the value associated with the column: somatorio_doencas_existentes
	 */
	public java.lang.Long getSomatorioDoencasExistentes () {
		return getPropertyValue(this, somatorioDoencasExistentes, PROP_SOMATORIO_DOENCAS_EXISTENTES); 
	}

	/**
	 * Set the value related to the column: somatorio_doencas_existentes
	 * @param somatorioDoencasExistentes the somatorio_doencas_existentes value
	 */
	public void setSomatorioDoencasExistentes (java.lang.Long somatorioDoencasExistentes) {
//        java.lang.Long somatorioDoencasExistentesOld = this.somatorioDoencasExistentes;
		this.somatorioDoencasExistentes = somatorioDoencasExistentes;
//        this.getPropertyChangeSupport().firePropertyChange ("somatorioDoencasExistentes", somatorioDoencasExistentesOld, somatorioDoencasExistentes);
	}



	/**
	 * Return the value associated with the column: ds_outros_doencas_existentes
	 */
	public java.lang.String getDescricaoOutrosDoencasExistentes () {
		return getPropertyValue(this, descricaoOutrosDoencasExistentes, PROP_DESCRICAO_OUTROS_DOENCAS_EXISTENTES); 
	}

	/**
	 * Set the value related to the column: ds_outros_doencas_existentes
	 * @param descricaoOutrosDoencasExistentes the ds_outros_doencas_existentes value
	 */
	public void setDescricaoOutrosDoencasExistentes (java.lang.String descricaoOutrosDoencasExistentes) {
//        java.lang.String descricaoOutrosDoencasExistentesOld = this.descricaoOutrosDoencasExistentes;
		this.descricaoOutrosDoencasExistentes = descricaoOutrosDoencasExistentes;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoOutrosDoencasExistentes", descricaoOutrosDoencasExistentesOld, descricaoOutrosDoencasExistentes);
	}



	/**
	 * Return the value associated with the column: medicacao_uso
	 */
	public java.lang.Long getMedicacaoUso () {
		return getPropertyValue(this, medicacaoUso, PROP_MEDICACAO_USO); 
	}

	/**
	 * Set the value related to the column: medicacao_uso
	 * @param medicacaoUso the medicacao_uso value
	 */
	public void setMedicacaoUso (java.lang.Long medicacaoUso) {
//        java.lang.Long medicacaoUsoOld = this.medicacaoUso;
		this.medicacaoUso = medicacaoUso;
//        this.getPropertyChangeSupport().firePropertyChange ("medicacaoUso", medicacaoUsoOld, medicacaoUso);
	}



	/**
	 * Return the value associated with the column: transfusao_ultimo_mes
	 */
	public java.lang.Long getTransfusaoUltimoMes () {
		return getPropertyValue(this, transfusaoUltimoMes, PROP_TRANSFUSAO_ULTIMO_MES); 
	}

	/**
	 * Set the value related to the column: transfusao_ultimo_mes
	 * @param transfusaoUltimoMes the transfusao_ultimo_mes value
	 */
	public void setTransfusaoUltimoMes (java.lang.Long transfusaoUltimoMes) {
//        java.lang.Long transfusaoUltimoMesOld = this.transfusaoUltimoMes;
		this.transfusaoUltimoMes = transfusaoUltimoMes;
//        this.getPropertyChangeSupport().firePropertyChange ("transfusaoUltimoMes", transfusaoUltimoMesOld, transfusaoUltimoMes);
	}



	/**
	 * Return the value associated with the column: data_transfusao_ultimo_mes
	 */
	public java.util.Date getDataTransfusaoUltimoMes () {
		return getPropertyValue(this, dataTransfusaoUltimoMes, PROP_DATA_TRANSFUSAO_ULTIMO_MES); 
	}

	/**
	 * Set the value related to the column: data_transfusao_ultimo_mes
	 * @param dataTransfusaoUltimoMes the data_transfusao_ultimo_mes value
	 */
	public void setDataTransfusaoUltimoMes (java.util.Date dataTransfusaoUltimoMes) {
//        java.util.Date dataTransfusaoUltimoMesOld = this.dataTransfusaoUltimoMes;
		this.dataTransfusaoUltimoMes = dataTransfusaoUltimoMes;
//        this.getPropertyChangeSupport().firePropertyChange ("dataTransfusaoUltimoMes", dataTransfusaoUltimoMesOld, dataTransfusaoUltimoMes);
	}



	/**
	 * Return the value associated with the column: historia_previa_convulsao
	 */
	public java.lang.Long getHistoriaPreviaConvulsao () {
		return getPropertyValue(this, historiaPreviaConvulsao, PROP_HISTORIA_PREVIA_CONVULSAO); 
	}

	/**
	 * Set the value related to the column: historia_previa_convulsao
	 * @param historiaPreviaConvulsao the historia_previa_convulsao value
	 */
	public void setHistoriaPreviaConvulsao (java.lang.Long historiaPreviaConvulsao) {
//        java.lang.Long historiaPreviaConvulsaoOld = this.historiaPreviaConvulsao;
		this.historiaPreviaConvulsao = historiaPreviaConvulsao;
//        this.getPropertyChangeSupport().firePropertyChange ("historiaPreviaConvulsao", historiaPreviaConvulsaoOld, historiaPreviaConvulsao);
	}



	/**
	 * Return the value associated with the column: viajou_ultimos_dias
	 */
	public java.lang.Long getViajouUltimosDias () {
		return getPropertyValue(this, viajouUltimosDias, PROP_VIAJOU_ULTIMOS_DIAS); 
	}

	/**
	 * Set the value related to the column: viajou_ultimos_dias
	 * @param viajouUltimosDias the viajou_ultimos_dias value
	 */
	public void setViajouUltimosDias (java.lang.Long viajouUltimosDias) {
//        java.lang.Long viajouUltimosDiasOld = this.viajouUltimosDias;
		this.viajouUltimosDias = viajouUltimosDias;
//        this.getPropertyChangeSupport().firePropertyChange ("viajouUltimosDias", viajouUltimosDiasOld, viajouUltimosDias);
	}



	/**
	 * Return the value associated with the column: data_inicio_viagem
	 */
	public java.util.Date getDataInicioViagem () {
		return getPropertyValue(this, dataInicioViagem, PROP_DATA_INICIO_VIAGEM); 
	}

	/**
	 * Set the value related to the column: data_inicio_viagem
	 * @param dataInicioViagem the data_inicio_viagem value
	 */
	public void setDataInicioViagem (java.util.Date dataInicioViagem) {
//        java.util.Date dataInicioViagemOld = this.dataInicioViagem;
		this.dataInicioViagem = dataInicioViagem;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInicioViagem", dataInicioViagemOld, dataInicioViagem);
	}



	/**
	 * Return the value associated with the column: data_termino_viagem
	 */
	public java.util.Date getDataTerminoViagem () {
		return getPropertyValue(this, dataTerminoViagem, PROP_DATA_TERMINO_VIAGEM); 
	}

	/**
	 * Set the value related to the column: data_termino_viagem
	 * @param dataTerminoViagem the data_termino_viagem value
	 */
	public void setDataTerminoViagem (java.util.Date dataTerminoViagem) {
//        java.util.Date dataTerminoViagemOld = this.dataTerminoViagem;
		this.dataTerminoViagem = dataTerminoViagem;
//        this.getPropertyChangeSupport().firePropertyChange ("dataTerminoViagem", dataTerminoViagemOld, dataTerminoViagem);
	}



	/**
	 * Return the value associated with the column: pais_viagem
	 */
	public java.lang.String getPaisViagem () {
		return getPropertyValue(this, paisViagem, PROP_PAIS_VIAGEM); 
	}

	/**
	 * Set the value related to the column: pais_viagem
	 * @param paisViagem the pais_viagem value
	 */
	public void setPaisViagem (java.lang.String paisViagem) {
//        java.lang.String paisViagemOld = this.paisViagem;
		this.paisViagem = paisViagem;
//        this.getPropertyChangeSupport().firePropertyChange ("paisViagem", paisViagemOld, paisViagem);
	}



	/**
	 * Return the value associated with the column: local_viagem
	 */
	public java.lang.String getLocalViagem () {
		return getPropertyValue(this, localViagem, PROP_LOCAL_VIAGEM); 
	}

	/**
	 * Set the value related to the column: local_viagem
	 * @param localViagem the local_viagem value
	 */
	public void setLocalViagem (java.lang.String localViagem) {
//        java.lang.String localViagemOld = this.localViagem;
		this.localViagem = localViagem;
//        this.getPropertyChangeSupport().firePropertyChange ("localViagem", localViagemOld, localViagem);
	}



	/**
	 * Return the value associated with the column: uf_viagem
	 */
	public java.lang.String getUfViagem () {
		return getPropertyValue(this, ufViagem, PROP_UF_VIAGEM); 
	}

	/**
	 * Set the value related to the column: uf_viagem
	 * @param ufViagem the uf_viagem value
	 */
	public void setUfViagem (java.lang.String ufViagem) {
//        java.lang.String ufViagemOld = this.ufViagem;
		this.ufViagem = ufViagem;
//        this.getPropertyChangeSupport().firePropertyChange ("ufViagem", ufViagemOld, ufViagem);
	}



	/**
	 * Return the value associated with the column: municipio_viagem
	 */
	public java.lang.String getMunicipioViagem () {
		return getPropertyValue(this, municipioViagem, PROP_MUNICIPIO_VIAGEM); 
	}

	/**
	 * Set the value related to the column: municipio_viagem
	 * @param municipioViagem the municipio_viagem value
	 */
	public void setMunicipioViagem (java.lang.String municipioViagem) {
//        java.lang.String municipioViagemOld = this.municipioViagem;
		this.municipioViagem = municipioViagem;
//        this.getPropertyChangeSupport().firePropertyChange ("municipioViagem", municipioViagemOld, municipioViagem);
	}



	/**
	 * Return the value associated with the column: atendimento_medico
	 */
	public java.lang.Long getAtendimentoMedico () {
		return getPropertyValue(this, atendimentoMedico, PROP_ATENDIMENTO_MEDICO); 
	}

	/**
	 * Set the value related to the column: atendimento_medico
	 * @param atendimentoMedico the atendimento_medico value
	 */
	public void setAtendimentoMedico (java.lang.Long atendimentoMedico) {
//        java.lang.Long atendimentoMedicoOld = this.atendimentoMedico;
		this.atendimentoMedico = atendimentoMedico;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimentoMedico", atendimentoMedicoOld, atendimentoMedico);
	}



	/**
	 * Return the value associated with the column: tipo_at_medico
	 */
	public java.lang.Long getTipoAtendimentoMedico () {
		return getPropertyValue(this, tipoAtendimentoMedico, PROP_TIPO_ATENDIMENTO_MEDICO); 
	}

	/**
	 * Set the value related to the column: tipo_at_medico
	 * @param tipoAtendimentoMedico the tipo_at_medico value
	 */
	public void setTipoAtendimentoMedico (java.lang.Long tipoAtendimentoMedico) {
//        java.lang.Long tipoAtendimentoMedicoOld = this.tipoAtendimentoMedico;
		this.tipoAtendimentoMedico = tipoAtendimentoMedico;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAtendimentoMedico", tipoAtendimentoMedicoOld, tipoAtendimentoMedico);
	}



	/**
	 * Return the value associated with the column: cnes_hosp_at_medico
	 */
	public java.lang.String getCnesHospitalAtendimentoMedico () {
		return getPropertyValue(this, cnesHospitalAtendimentoMedico, PROP_CNES_HOSPITAL_ATENDIMENTO_MEDICO); 
	}

	/**
	 * Set the value related to the column: cnes_hosp_at_medico
	 * @param cnesHospitalAtendimentoMedico the cnes_hosp_at_medico value
	 */
	public void setCnesHospitalAtendimentoMedico (java.lang.String cnesHospitalAtendimentoMedico) {
//        java.lang.String cnesHospitalAtendimentoMedicoOld = this.cnesHospitalAtendimentoMedico;
		this.cnesHospitalAtendimentoMedico = cnesHospitalAtendimentoMedico;
//        this.getPropertyChangeSupport().firePropertyChange ("cnesHospitalAtendimentoMedico", cnesHospitalAtendimentoMedicoOld, cnesHospitalAtendimentoMedico);
	}



	/**
	 * Return the value associated with the column: nome_hospital_at_medico
	 */
	public java.lang.String getNomeHospitalAtendimentoMedico () {
		return getPropertyValue(this, nomeHospitalAtendimentoMedico, PROP_NOME_HOSPITAL_ATENDIMENTO_MEDICO); 
	}

	/**
	 * Set the value related to the column: nome_hospital_at_medico
	 * @param nomeHospitalAtendimentoMedico the nome_hospital_at_medico value
	 */
	public void setNomeHospitalAtendimentoMedico (java.lang.String nomeHospitalAtendimentoMedico) {
//        java.lang.String nomeHospitalAtendimentoMedicoOld = this.nomeHospitalAtendimentoMedico;
		this.nomeHospitalAtendimentoMedico = nomeHospitalAtendimentoMedico;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeHospitalAtendimentoMedico", nomeHospitalAtendimentoMedicoOld, nomeHospitalAtendimentoMedico);
	}



	/**
	 * Return the value associated with the column: data_internacao_at_medico
	 */
	public java.util.Date getDataInternacaoAtendimentoMedico () {
		return getPropertyValue(this, dataInternacaoAtendimentoMedico, PROP_DATA_INTERNACAO_ATENDIMENTO_MEDICO); 
	}

	/**
	 * Set the value related to the column: data_internacao_at_medico
	 * @param dataInternacaoAtendimentoMedico the data_internacao_at_medico value
	 */
	public void setDataInternacaoAtendimentoMedico (java.util.Date dataInternacaoAtendimentoMedico) {
//        java.util.Date dataInternacaoAtendimentoMedicoOld = this.dataInternacaoAtendimentoMedico;
		this.dataInternacaoAtendimentoMedico = dataInternacaoAtendimentoMedico;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInternacaoAtendimentoMedico", dataInternacaoAtendimentoMedicoOld, dataInternacaoAtendimentoMedico);
	}



	/**
	 * Return the value associated with the column: data_alta_at_medico
	 */
	public java.util.Date getDataAltaAtendimentoMedico () {
		return getPropertyValue(this, dataAltaAtendimentoMedico, PROP_DATA_ALTA_ATENDIMENTO_MEDICO); 
	}

	/**
	 * Set the value related to the column: data_alta_at_medico
	 * @param dataAltaAtendimentoMedico the data_alta_at_medico value
	 */
	public void setDataAltaAtendimentoMedico (java.util.Date dataAltaAtendimentoMedico) {
//        java.util.Date dataAltaAtendimentoMedicoOld = this.dataAltaAtendimentoMedico;
		this.dataAltaAtendimentoMedico = dataAltaAtendimentoMedico;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAltaAtendimentoMedico", dataAltaAtendimentoMedicoOld, dataAltaAtendimentoMedico);
	}



	/**
	 * Return the value associated with the column: municipio_at_medico
	 */
	public java.lang.String getMunicipioAtendimentoMedico () {
		return getPropertyValue(this, municipioAtendimentoMedico, PROP_MUNICIPIO_ATENDIMENTO_MEDICO); 
	}

	/**
	 * Set the value related to the column: municipio_at_medico
	 * @param municipioAtendimentoMedico the municipio_at_medico value
	 */
	public void setMunicipioAtendimentoMedico (java.lang.String municipioAtendimentoMedico) {
//        java.lang.String municipioAtendimentoMedicoOld = this.municipioAtendimentoMedico;
		this.municipioAtendimentoMedico = municipioAtendimentoMedico;
//        this.getPropertyChangeSupport().firePropertyChange ("municipioAtendimentoMedico", municipioAtendimentoMedicoOld, municipioAtendimentoMedico);
	}



	/**
	 * Return the value associated with the column: uf_at_medico
	 */
	public java.lang.String getUfAtendimentoMedico () {
		return getPropertyValue(this, ufAtendimentoMedico, PROP_UF_ATENDIMENTO_MEDICO); 
	}

	/**
	 * Set the value related to the column: uf_at_medico
	 * @param ufAtendimentoMedico the uf_at_medico value
	 */
	public void setUfAtendimentoMedico (java.lang.String ufAtendimentoMedico) {
//        java.lang.String ufAtendimentoMedicoOld = this.ufAtendimentoMedico;
		this.ufAtendimentoMedico = ufAtendimentoMedico;
//        this.getPropertyChangeSupport().firePropertyChange ("ufAtendimentoMedico", ufAtendimentoMedicoOld, ufAtendimentoMedico);
	}



	/**
	 * Return the value associated with the column: informacoes_complementares
	 */
	public java.lang.String getInformacoesComplementares () {
		return getPropertyValue(this, informacoesComplementares, PROP_INFORMACOES_COMPLEMENTARES); 
	}

	/**
	 * Set the value related to the column: informacoes_complementares
	 * @param informacoesComplementares the informacoes_complementares value
	 */
	public void setInformacoesComplementares (java.lang.String informacoesComplementares) {
//        java.lang.String informacoesComplementaresOld = this.informacoesComplementares;
		this.informacoesComplementares = informacoesComplementares;
//        this.getPropertyChangeSupport().firePropertyChange ("informacoesComplementares", informacoesComplementaresOld, informacoesComplementares);
	}



	/**
	 * Return the value associated with the column: diagnostico
	 */
	public java.lang.String getDiagnostico () {
		return getPropertyValue(this, diagnostico, PROP_DIAGNOSTICO); 
	}

	/**
	 * Set the value related to the column: diagnostico
	 * @param diagnostico the diagnostico value
	 */
	public void setDiagnostico (java.lang.String diagnostico) {
//        java.lang.String diagnosticoOld = this.diagnostico;
		this.diagnostico = diagnostico;
//        this.getPropertyChangeSupport().firePropertyChange ("diagnostico", diagnosticoOld, diagnostico);
	}



	/**
	 * Return the value associated with the column: evolucao
	 */
	public java.lang.Long getEvolucao () {
		return getPropertyValue(this, evolucao, PROP_EVOLUCAO); 
	}

	/**
	 * Set the value related to the column: evolucao
	 * @param evolucao the evolucao value
	 */
	public void setEvolucao (java.lang.Long evolucao) {
//        java.lang.Long evolucaoOld = this.evolucao;
		this.evolucao = evolucao;
//        this.getPropertyChangeSupport().firePropertyChange ("evolucao", evolucaoOld, evolucao);
	}



	/**
	 * Return the value associated with the column: classificacao_final
	 */
	public java.lang.Long getClassificacaoFinal () {
		return getPropertyValue(this, classificacaoFinal, PROP_CLASSIFICACAO_FINAL); 
	}

	/**
	 * Set the value related to the column: classificacao_final
	 * @param classificacaoFinal the classificacao_final value
	 */
	public void setClassificacaoFinal (java.lang.Long classificacaoFinal) {
//        java.lang.Long classificacaoFinalOld = this.classificacaoFinal;
		this.classificacaoFinal = classificacaoFinal;
//        this.getPropertyChangeSupport().firePropertyChange ("classificacaoFinal", classificacaoFinalOld, classificacaoFinal);
	}



	/**
	 * Return the value associated with the column: erros_imunizacao
	 */
	public java.lang.Long getErrosImunizacao () {
		return getPropertyValue(this, errosImunizacao, PROP_ERROS_IMUNIZACAO); 
	}

	/**
	 * Set the value related to the column: erros_imunizacao
	 * @param errosImunizacao the erros_imunizacao value
	 */
	public void setErrosImunizacao (java.lang.Long errosImunizacao) {
//        java.lang.Long errosImunizacaoOld = this.errosImunizacao;
		this.errosImunizacao = errosImunizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("errosImunizacao", errosImunizacaoOld, errosImunizacao);
	}



	/**
	 * Return the value associated with the column: ds_outros_erros_imunizacao
	 */
	public java.lang.String getDescricaoOutrosErrosImunizacao () {
		return getPropertyValue(this, descricaoOutrosErrosImunizacao, PROP_DESCRICAO_OUTROS_ERROS_IMUNIZACAO); 
	}

	/**
	 * Set the value related to the column: ds_outros_erros_imunizacao
	 * @param descricaoOutrosErrosImunizacao the ds_outros_erros_imunizacao value
	 */
	public void setDescricaoOutrosErrosImunizacao (java.lang.String descricaoOutrosErrosImunizacao) {
//        java.lang.String descricaoOutrosErrosImunizacaoOld = this.descricaoOutrosErrosImunizacao;
		this.descricaoOutrosErrosImunizacao = descricaoOutrosErrosImunizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoOutrosErrosImunizacao", descricaoOutrosErrosImunizacaoOld, descricaoOutrosErrosImunizacao);
	}



	/**
	 * Return the value associated with the column: conduta_erros_imunizacao
	 */
	public java.lang.Long getCondutaErrosImunizacao () {
		return getPropertyValue(this, condutaErrosImunizacao, PROP_CONDUTA_ERROS_IMUNIZACAO); 
	}

	/**
	 * Set the value related to the column: conduta_erros_imunizacao
	 * @param condutaErrosImunizacao the conduta_erros_imunizacao value
	 */
	public void setCondutaErrosImunizacao (java.lang.Long condutaErrosImunizacao) {
//        java.lang.Long condutaErrosImunizacaoOld = this.condutaErrosImunizacao;
		this.condutaErrosImunizacao = condutaErrosImunizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("condutaErrosImunizacao", condutaErrosImunizacaoOld, condutaErrosImunizacao);
	}



	/**
	 * Return the value associated with the column: conduta_erros_imu_item
	 */
	public java.lang.Long getCondutaErrosImunizacaoItem () {
		return getPropertyValue(this, condutaErrosImunizacaoItem, PROP_CONDUTA_ERROS_IMUNIZACAO_ITEM); 
	}

	/**
	 * Set the value related to the column: conduta_erros_imu_item
	 * @param condutaErrosImunizacaoItem the conduta_erros_imu_item value
	 */
	public void setCondutaErrosImunizacaoItem (java.lang.Long condutaErrosImunizacaoItem) {
//        java.lang.Long condutaErrosImunizacaoItemOld = this.condutaErrosImunizacaoItem;
		this.condutaErrosImunizacaoItem = condutaErrosImunizacaoItem;
//        this.getPropertyChangeSupport().firePropertyChange ("condutaErrosImunizacaoItem", condutaErrosImunizacaoItemOld, condutaErrosImunizacaoItem);
	}



	/**
	 * Return the value associated with the column: ds_outros_con_err_imu_item
	 */
	public java.lang.String getDescricaoOutrosCondutaErrosImunizacaoItem () {
		return getPropertyValue(this, descricaoOutrosCondutaErrosImunizacaoItem, PROP_DESCRICAO_OUTROS_CONDUTA_ERROS_IMUNIZACAO_ITEM); 
	}

	/**
	 * Set the value related to the column: ds_outros_con_err_imu_item
	 * @param descricaoOutrosCondutaErrosImunizacaoItem the ds_outros_con_err_imu_item value
	 */
	public void setDescricaoOutrosCondutaErrosImunizacaoItem (java.lang.String descricaoOutrosCondutaErrosImunizacaoItem) {
//        java.lang.String descricaoOutrosCondutaErrosImunizacaoItemOld = this.descricaoOutrosCondutaErrosImunizacaoItem;
		this.descricaoOutrosCondutaErrosImunizacaoItem = descricaoOutrosCondutaErrosImunizacaoItem;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoOutrosCondutaErrosImunizacaoItem", descricaoOutrosCondutaErrosImunizacaoItemOld, descricaoOutrosCondutaErrosImunizacaoItem);
	}



	/**
	 * Return the value associated with the column: ds_outros_cond_fr_es_vac
	 */
	public java.lang.String getDescricaoOutrosCondutaFrenteEsquemaVacinacao () {
		return getPropertyValue(this, descricaoOutrosCondutaFrenteEsquemaVacinacao, PROP_DESCRICAO_OUTROS_CONDUTA_FRENTE_ESQUEMA_VACINACAO); 
	}

	/**
	 * Set the value related to the column: ds_outros_cond_fr_es_vac
	 * @param descricaoOutrosCondutaFrenteEsquemaVacinacao the ds_outros_cond_fr_es_vac value
	 */
	public void setDescricaoOutrosCondutaFrenteEsquemaVacinacao (java.lang.String descricaoOutrosCondutaFrenteEsquemaVacinacao) {
//        java.lang.String descricaoOutrosCondutaFrenteEsquemaVacinacaoOld = this.descricaoOutrosCondutaFrenteEsquemaVacinacao;
		this.descricaoOutrosCondutaFrenteEsquemaVacinacao = descricaoOutrosCondutaFrenteEsquemaVacinacao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoOutrosCondutaFrenteEsquemaVacinacao", descricaoOutrosCondutaFrenteEsquemaVacinacaoOld, descricaoOutrosCondutaFrenteEsquemaVacinacao);
	}



	/**
	 * Return the value associated with the column: ds_outros_class_casual
	 */
	public java.lang.String getDescricaoOutrosClassificacaoCasual () {
		return getPropertyValue(this, descricaoOutrosClassificacaoCasual, PROP_DESCRICAO_OUTROS_CLASSIFICACAO_CASUAL); 
	}

	/**
	 * Set the value related to the column: ds_outros_class_casual
	 * @param descricaoOutrosClassificacaoCasual the ds_outros_class_casual value
	 */
	public void setDescricaoOutrosClassificacaoCasual (java.lang.String descricaoOutrosClassificacaoCasual) {
//        java.lang.String descricaoOutrosClassificacaoCasualOld = this.descricaoOutrosClassificacaoCasual;
		this.descricaoOutrosClassificacaoCasual = descricaoOutrosClassificacaoCasual;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoOutrosClassificacaoCasual", descricaoOutrosClassificacaoCasualOld, descricaoOutrosClassificacaoCasual);
	}



	/**
	 * Return the value associated with the column: cd_registro_agravo
	 */
	public br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo getRegistroAgravo () {
		return getPropertyValue(this, registroAgravo, PROP_REGISTRO_AGRAVO); 
	}

	/**
	 * Set the value related to the column: cd_registro_agravo
	 * @param registroAgravo the cd_registro_agravo value
	 */
	public void setRegistroAgravo (br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo) {
//        br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravoOld = this.registroAgravo;
		this.registroAgravo = registroAgravo;
//        this.getPropertyChangeSupport().firePropertyChange ("registroAgravo", registroAgravoOld, registroAgravo);
	}



	/**
	 * Return the value associated with the column: cd_responsavel_notificacao
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissionalResponsavelNotificacao () {
		return getPropertyValue(this, profissionalResponsavelNotificacao, PROP_PROFISSIONAL_RESPONSAVEL_NOTIFICACAO); 
	}

	/**
	 * Set the value related to the column: cd_responsavel_notificacao
	 * @param profissionalResponsavelNotificacao the cd_responsavel_notificacao value
	 */
	public void setProfissionalResponsavelNotificacao (br.com.ksisolucoes.vo.cadsus.Profissional profissionalResponsavelNotificacao) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalResponsavelNotificacaoOld = this.profissionalResponsavelNotificacao;
		this.profissionalResponsavelNotificacao = profissionalResponsavelNotificacao;
//        this.getPropertyChangeSupport().firePropertyChange ("profissionalResponsavelNotificacao", profissionalResponsavelNotificacaoOld, profissionalResponsavelNotificacao);
	}



	/**
	 * Return the value associated with the column: empresa_notificacao
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresaNotificacao () {
		return getPropertyValue(this, empresaNotificacao, PROP_EMPRESA_NOTIFICACAO); 
	}

	/**
	 * Set the value related to the column: empresa_notificacao
	 * @param empresaNotificacao the empresa_notificacao value
	 */
	public void setEmpresaNotificacao (br.com.ksisolucoes.vo.basico.Empresa empresaNotificacao) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaNotificacaoOld = this.empresaNotificacao;
		this.empresaNotificacao = empresaNotificacao;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaNotificacao", empresaNotificacaoOld, empresaNotificacao);
	}



	/**
	 * Return the value associated with the column: cd_responsavel_investigacao
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissionalResponsavelInvestigacao () {
		return getPropertyValue(this, profissionalResponsavelInvestigacao, PROP_PROFISSIONAL_RESPONSAVEL_INVESTIGACAO); 
	}

	/**
	 * Set the value related to the column: cd_responsavel_investigacao
	 * @param profissionalResponsavelInvestigacao the cd_responsavel_investigacao value
	 */
	public void setProfissionalResponsavelInvestigacao (br.com.ksisolucoes.vo.cadsus.Profissional profissionalResponsavelInvestigacao) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalResponsavelInvestigacaoOld = this.profissionalResponsavelInvestigacao;
		this.profissionalResponsavelInvestigacao = profissionalResponsavelInvestigacao;
//        this.getPropertyChangeSupport().firePropertyChange ("profissionalResponsavelInvestigacao", profissionalResponsavelInvestigacaoOld, profissionalResponsavelInvestigacao);
	}



	/**
	 * Return the value associated with the column: empresa_investigacao
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresaInvestigacao () {
		return getPropertyValue(this, empresaInvestigacao, PROP_EMPRESA_INVESTIGACAO); 
	}

	/**
	 * Set the value related to the column: empresa_investigacao
	 * @param empresaInvestigacao the empresa_investigacao value
	 */
	public void setEmpresaInvestigacao (br.com.ksisolucoes.vo.basico.Empresa empresaInvestigacao) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaInvestigacaoOld = this.empresaInvestigacao;
		this.empresaInvestigacao = empresaInvestigacao;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaInvestigacao", empresaInvestigacaoOld, empresaInvestigacao);
	}



	/**
	 * Return the value associated with the column: cd_responsavel_encerramento
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissionalResponsavelEncerramento () {
		return getPropertyValue(this, profissionalResponsavelEncerramento, PROP_PROFISSIONAL_RESPONSAVEL_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_responsavel_encerramento
	 * @param profissionalResponsavelEncerramento the cd_responsavel_encerramento value
	 */
	public void setProfissionalResponsavelEncerramento (br.com.ksisolucoes.vo.cadsus.Profissional profissionalResponsavelEncerramento) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalResponsavelEncerramentoOld = this.profissionalResponsavelEncerramento;
		this.profissionalResponsavelEncerramento = profissionalResponsavelEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("profissionalResponsavelEncerramento", profissionalResponsavelEncerramentoOld, profissionalResponsavelEncerramento);
	}



	/**
	 * Return the value associated with the column: empresa_encerramento
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresaEncerramento () {
		return getPropertyValue(this, empresaEncerramento, PROP_EMPRESA_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: empresa_encerramento
	 * @param empresaEncerramento the empresa_encerramento value
	 */
	public void setEmpresaEncerramento (br.com.ksisolucoes.vo.basico.Empresa empresaEncerramento) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaEncerramentoOld = this.empresaEncerramento;
		this.empresaEncerramento = empresaEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaEncerramento", empresaEncerramentoOld, empresaEncerramento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.InvestigacaoAgravo)) return false;
		else {
			br.com.ksisolucoes.vo.basico.InvestigacaoAgravo investigacaoAgravo = (br.com.ksisolucoes.vo.basico.InvestigacaoAgravo) obj;
			if (null == this.getCodigo() || null == investigacaoAgravo.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravo.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}