package br.com.ksisolucoes.vo.prontuario.procedimento;

import java.io.Serializable;

import br.com.ksisolucoes.associacao.annotations.ColumnNameSIGTAP;
import br.com.ksisolucoes.associacao.annotations.IdNameSIGTAP;
import br.com.ksisolucoes.associacao.annotations.IdOverrideAtributeSIGTAP;
import br.com.ksisolucoes.associacao.annotations.IdOverrideSIGTAP;
import br.com.ksisolucoes.associacao.annotations.TableNameSIGTAP;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.procedimento.base.BaseProcedimentoExcecaoCompatibilidade;


@TableNameSIGTAP(value="rl_excecao_compatibilidade",dependencias={ProcedimentoCompetencia.class,ProcedimentoRegistroCadastro.class})
public class ProcedimentoExcecaoCompatibilidade extends BaseProcedimentoExcecaoCompatibilidade implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ProcedimentoExcecaoCompatibilidade () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ProcedimentoExcecaoCompatibilidade (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoExcecaoCompatibilidadePK id) {
		super(id);
	}

	/**
	 * Constructor for required fields
	 */
	public ProcedimentoExcecaoCompatibilidade (
		br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoExcecaoCompatibilidadePK id,
		java.lang.Long tipoCompatibilidade) {

		super (
			id,
			tipoCompatibilidade);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setId( (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoExcecaoCompatibilidadePK)key );
    }

    public Serializable getCodigoManager() {
        return this.getId();
    }

    @IdNameSIGTAP
    @IdOverrideSIGTAP(atribute={
        @IdOverrideAtributeSIGTAP(prop="id.procedimentoCompetenciaCompativel.id.dataCompetencia",value="DT_COMPETENCIA"),
        @IdOverrideAtributeSIGTAP(prop="id.procedimentoCompetenciaCompativel.id.procedimento.codigo",value="CO_PROCEDIMENTO_COMPATIVEL"),
        @IdOverrideAtributeSIGTAP(prop="id.procedimentoCompetenciaPrincipal.id.dataCompetencia",value="DT_COMPETENCIA"),
        @IdOverrideAtributeSIGTAP(prop="id.procedimentoCompetenciaPrincipal.id.procedimento.codigo",value="CO_PROCEDIMENTO_PRINCIPAL"),
        @IdOverrideAtributeSIGTAP(prop="id.procedimentoCompetenciaRestricao.id.dataCompetencia",value="DT_COMPETENCIA"),
        @IdOverrideAtributeSIGTAP(prop="id.procedimentoCompetenciaRestricao.id.procedimento.codigo",value="CO_PROCEDIMENTO_RESTRICAO"),
        @IdOverrideAtributeSIGTAP(prop="id.registroCompativel.codigo",value="CO_REGISTRO_COMPATIVEL"),
        @IdOverrideAtributeSIGTAP(prop="id.registroPrincipal.codigo",value="CO_REGISTRO_PRINCIPAL")
    })
    @Override
    public ProcedimentoExcecaoCompatibilidadePK getId() {
        return super.getId();
    }

    @ColumnNameSIGTAP("TP_COMPATIBILIDADE")
    @Override
    public Long getTipoCompatibilidade() {
        return super.getTipoCompatibilidade();
    }

    @Override
    public void setId(ProcedimentoExcecaoCompatibilidadePK id) {
        super.setId(id);
    }

    @Override
    public void setTipoCompatibilidade(Long tipoCompatibilidade) {
        super.setTipoCompatibilidade(tipoCompatibilidade);
    }


}