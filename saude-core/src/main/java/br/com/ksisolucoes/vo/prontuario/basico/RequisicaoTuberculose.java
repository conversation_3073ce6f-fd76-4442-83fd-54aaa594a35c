package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseRequisicaoTuberculose;

public class RequisicaoTuberculose extends BaseRequisicaoTuberculose implements CodigoManager {

    private static final long serialVersionUID = 1L;

    public static enum ControleTratamento implements IEnum<ControleTratamento> {

        PRIMEIRO_MES(1L, Bundle.getStringApplication("rotulo_primeiro_mes")),
        SEGUNDO_MES(2L, Bundle.getStringApplication("rotulo_segundo_mes")),
        TERCEIRO_MES(3L, Bundle.getStringApplication("rotulo_terceiro_mes")),
        QUARTO_MES(4L, Bundle.getStringApplication("rotulo_quarto_mes")),
        QUINTO_MES(5L, Bundle.getStringApplication("rotulo_quinto_mes")),
        SEXTO_MES(6L, Bundle.getStringApplication("rotulo_sexto_mes"));

        private final Long value;
        private final String descricao;

        private ControleTratamento(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }
    
    public enum AmostraBiologica{

            ESCARRO(1L),
            LIQUOR(2L),
            LAVADO_BRONQUICO(4L),
            LAVADO_BRONCO_ALVEOLAR(8L),
            LIQUIDO_ASCITICO(16L),
            LIQUIDO_PLEURAL(32L),
            CEPA_P_IDENTIFICACAO(64L);

            private Long value;

            private AmostraBiologica(Long value) {
                this.value = value;
            }

            public Long value(){
                return value;
            }

            @Override
            public String toString() {
                if (ESCARRO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_escarro");
                } else if (LIQUOR.equals(this)) {
                    return Bundle.getStringApplication("rotulo_liquor");
                } else if (LAVADO_BRONQUICO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_lavado_bronquico");
                } else if (LAVADO_BRONCO_ALVEOLAR.equals(this)) {
                    return Bundle.getStringApplication("rotulo_lavado_bronco_alveolar");
                } else if (LIQUIDO_ASCITICO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_liquido_ascitico");
                } else if (LIQUIDO_PLEURAL.equals(this)) {
                    return Bundle.getStringApplication("rotulo_liquido_pleural");
                } else if (CEPA_P_IDENTIFICACAO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_cepa_p_identificacao");
                }
                return Bundle.getStringApplication("rotulo_desconhecido");
            }

        }

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public RequisicaoTuberculose () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequisicaoTuberculose (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequisicaoTuberculose (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicao) {

		super (
			codigo,
			exameRequisicao);
	}

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}
