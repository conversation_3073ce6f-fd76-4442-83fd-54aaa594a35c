package br.com.ksisolucoes.vo.agendamento.tfd;

import java.io.Serializable;

import br.com.ksisolucoes.vo.agendamento.tfd.base.BaseCidadeTrajeto;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class CidadeTrajeto extends BaseCidadeTrajeto implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public CidadeTrajeto () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public CidadeTrajeto (java.lang.Long codigo) {
		super(codigo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}