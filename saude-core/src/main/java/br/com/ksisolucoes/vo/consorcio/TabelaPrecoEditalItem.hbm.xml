<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.consorcio"  >
    <class name="TabelaPrecoEditalItem"  table="tabela_preco_edital_item" >

        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_tabela_preco_edital_item"
        >
            <generator class="assigned"/>
        </id>

        <version column="version" name="version" type="long" />

        <many-to-one
                name="tabelaPrecoEdital"
                class="br.com.ksisolucoes.vo.consorcio.TabelaPrecoEdital"
                column="cd_tabela_preco_edital"
                not-null="true"
        />

        <many-to-one
                name="consorcioProcedimento"
                class="br.com.ksisolucoes.vo.consorcio.ConsorcioProcedimento"
                column="cd_consorcio_procedimento"
                not-null="true"
        />

        <property
                name="valorProcedimento"
                column="vl_procedimento"
                type="java.lang.Double"
                not-null="true"
        />

    </class>
</hibernate-mapping>