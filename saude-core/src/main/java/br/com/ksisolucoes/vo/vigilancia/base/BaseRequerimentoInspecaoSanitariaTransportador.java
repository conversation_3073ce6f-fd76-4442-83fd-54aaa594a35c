package br.com.ksisolucoes.vo.vigilancia.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoInspecaoSanitariaTransportador;

import java.io.Serializable;


/**
 * This is an object that contains data related to the requerimento_inspecao_sanitaria_transportador table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="requerimento_inspecao_sanitaria_transportador"
 */

public abstract class BaseRequerimentoInspecaoSanitariaTransportador extends BaseRootVO implements Serializable {

	public static String REF = "RequerimentoVigilanciaTransportador";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_REQUERIMENTO_INSPECAO_SANITARIA = "requerimentoInspecaoSanitaria";
	public static final String PROP_NUMERO_A_F_E = "numeroAFE";
	public static final String PROP_DESCRICAO_EMPRESA = "descricaoEmpresa";
	public static final String PROP_ENDERECO = "endereco";


	// constructors
	public BaseRequerimentoInspecaoSanitariaTransportador() {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseRequerimentoInspecaoSanitariaTransportador(Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseRequerimentoInspecaoSanitariaTransportador(
		Long codigo,
		br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoInspecaoSanitaria requerimentoInspecaoSanitaria,
		String descricaoEmpresa,
		String endereco,
		String numeroAFE) {

		this.setCodigo(codigo);
		this.setRequerimentoInspecaoSanitaria(requerimentoInspecaoSanitaria);
		this.setDescricaoEmpresa(descricaoEmpresa);
		this.setEndereco(endereco);
		this.setNumeroAFE(numeroAFE);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private Long codigo;

	// fields
	private String descricaoEmpresa;
	private String endereco;
	private String numeroAFE;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoInspecaoSanitaria requerimentoInspecaoSanitaria;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_requerimento_inspecao_sanitaria_transportador"
     */
	public Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_empresa
	 */
	public String getDescricaoEmpresa () {
		return getPropertyValue(this, descricaoEmpresa, PROP_DESCRICAO_EMPRESA); 
	}

	/**
	 * Set the value related to the column: ds_empresa
	 * @param descricaoEmpresa the ds_empresa value
	 */
	public void setDescricaoEmpresa (String descricaoEmpresa) {
//        java.lang.String descricaoEmpresaOld = this.descricaoEmpresa;
		this.descricaoEmpresa = descricaoEmpresa;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoEmpresa", descricaoEmpresaOld, descricaoEmpresa);
	}



	/**
	 * Return the value associated with the column: endereco
	 */
	public String getEndereco () {
		return getPropertyValue(this, endereco, PROP_ENDERECO); 
	}

	/**
	 * Set the value related to the column: endereco
	 * @param endereco the endereco value
	 */
	public void setEndereco (String endereco) {
//        java.lang.String enderecoOld = this.endereco;
		this.endereco = endereco;
//        this.getPropertyChangeSupport().firePropertyChange ("endereco", enderecoOld, endereco);
	}



	/**
	 * Return the value associated with the column: numero_afe
	 */
	public String getNumeroAFE () {
		return getPropertyValue(this, numeroAFE, PROP_NUMERO_A_F_E); 
	}

	/**
	 * Set the value related to the column: numero_afe
	 * @param numeroAFE the numero_afe value
	 */
	public void setNumeroAFE (String numeroAFE) {
//        java.lang.String numeroAFEOld = this.numeroAFE;
		this.numeroAFE = numeroAFE;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroAFE", numeroAFEOld, numeroAFE);
	}



	/**
	 * Return the value associated with the column: cd_requerimento_inspecao_sanitaria
	 */
	public br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoInspecaoSanitaria getRequerimentoInspecaoSanitaria () {
		return getPropertyValue(this, requerimentoInspecaoSanitaria, PROP_REQUERIMENTO_INSPECAO_SANITARIA); 
	}

	/**
	 * Set the value related to the column: cd_requerimento_inspecao_sanitaria
	 * @param requerimentoInspecaoSanitaria the cd_requerimento_inspecao_sanitaria value
	 */
	public void setRequerimentoInspecaoSanitaria (br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoInspecaoSanitaria requerimentoInspecaoSanitaria) {
//        br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoInspecaoSanitaria requerimentoInspecaoSanitariaOld = this.requerimentoInspecaoSanitaria;
		this.requerimentoInspecaoSanitaria = requerimentoInspecaoSanitaria;
//        this.getPropertyChangeSupport().firePropertyChange ("requerimentoInspecaoSanitaria", requerimentoInspecaoSanitariaOld, requerimentoInspecaoSanitaria);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof RequerimentoInspecaoSanitariaTransportador)) return false;
		else {
			RequerimentoInspecaoSanitariaTransportador requerimentoInspecaoSanitariaTransportador = (RequerimentoInspecaoSanitariaTransportador) obj;
			if (null == this.getCodigo() || null == requerimentoInspecaoSanitariaTransportador.getCodigo()) return false;
			else return (this.getCodigo().equals(requerimentoInspecaoSanitariaTransportador.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}