package br.com.ksisolucoes.vo.atendimento.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the bpa_atendimento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="bpa_atendimento"
 */

public abstract class BaseBpaAtendimento extends BaseRootVO implements Serializable {

	public static String REF = "BpaAtendimento";
	public static final String PROP_BPA = "bpa";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_ATENDIMENTO = "atendimento";


	// constructors
	public BaseBpaAtendimento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseBpaAtendimento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// many to one
	private br.com.ksisolucoes.vo.atendimento.Bpa bpa;
	private br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_bpa_atendimento"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_bpa
	 */
	public br.com.ksisolucoes.vo.atendimento.Bpa getBpa () {
		return getPropertyValue(this, bpa, PROP_BPA); 
	}

	/**
	 * Set the value related to the column: cd_bpa
	 * @param bpa the cd_bpa value
	 */
	public void setBpa (br.com.ksisolucoes.vo.atendimento.Bpa bpa) {
//        br.com.ksisolucoes.vo.atendimento.Bpa bpaOld = this.bpa;
		this.bpa = bpa;
//        this.getPropertyChangeSupport().firePropertyChange ("bpa", bpaOld, bpa);
	}



	/**
	 * Return the value associated with the column: nr_atendimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Atendimento getAtendimento () {
		return getPropertyValue(this, atendimento, PROP_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: nr_atendimento
	 * @param atendimento the nr_atendimento value
	 */
	public void setAtendimento (br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoOld = this.atendimento;
		this.atendimento = atendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimento", atendimentoOld, atendimento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.atendimento.BpaAtendimento)) return false;
		else {
			br.com.ksisolucoes.vo.atendimento.BpaAtendimento bpaAtendimento = (br.com.ksisolucoes.vo.atendimento.BpaAtendimento) obj;
			if (null == this.getCodigo() || null == bpaAtendimento.getCodigo()) return false;
			else return (this.getCodigo().equals(bpaAtendimento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}