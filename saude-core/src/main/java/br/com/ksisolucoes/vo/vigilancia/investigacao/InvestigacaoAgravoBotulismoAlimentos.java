package br.com.ksisolucoes.vo.vigilancia.investigacao;

import java.io.Serializable;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.vigilancia.investigacao.base.BaseInvestigacaoAgravoBotulismoAlimentos;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

public class InvestigacaoAgravoBotulismoAlimentos extends BaseInvestigacaoAgravoBotulismoAlimentos implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public InvestigacaoAgravoBotulismoAlimentos () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public InvestigacaoAgravoBotulismoAlimentos (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public InvestigacaoAgravoBotulismoAlimentos (
		java.lang.Long codigo,
		InvestigacaoAgravoBotulismo investigacaoAgravoBotulismo) {

		super (
			codigo,
			investigacaoAgravoBotulismo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

	public static InvestigacaoAgravoBotulismoAlimentos buscaPorBotulismo(InvestigacaoAgravoBotulismo botulismo) {
		InvestigacaoAgravoBotulismoAlimentos investigacao =
				LoadManager.getInstance(InvestigacaoAgravoBotulismoAlimentos.class)
						.addProperties(new HQLProperties(InvestigacaoAgravoBotulismoAlimentos.class).getProperties())
						.addProperties(new HQLProperties(
								InvestigacaoAgravoBotulismo.class,
								VOUtils.montarPath(InvestigacaoAgravoBotulismoAlimentos.PROP_INVESTIGACAO_AGRAVO_BOTULISMO)).getProperties())
						.addParameter(new QueryCustom.QueryCustomParameter(
								VOUtils.montarPath(InvestigacaoAgravoBotulismoAlimentos.PROP_INVESTIGACAO_AGRAVO_BOTULISMO, InvestigacaoAgravoBotulismo.PROP_CODIGO),
								botulismo.getCodigo()))
						.start().getVO();
		return investigacao;
	}
}