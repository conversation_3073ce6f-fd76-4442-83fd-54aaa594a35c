package br.com.ksisolucoes.vo.materiais.bnafar.dispensacao;

import br.com.celk.service.async.BnafarErroDto;
import br.com.celk.service.async.BnafarItemErroDto;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.materiais.bnafar.dispensacao.base.BaseBnafarDispensacaoElo;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.io.Serializable;
import java.util.List;


public class BnafarDispensacaoElo extends BaseBnafarDispensacaoElo implements CodigoManager {
    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public BnafarDispensacaoElo() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public BnafarDispensacaoElo(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public BnafarDispensacaoElo(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.materiais.bnafar.dispensacao.BnafarDispensacao bnafarDispensacao,
            br.com.ksisolucoes.vo.materiais.bnafar.dispensacao.BnafarDispensacaoIntegracao bnafarDispensacaoIntegracao) {

        super(
                codigo,
                bnafarDispensacao,
                bnafarDispensacaoIntegracao);
    }

    /*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getMensagemRetornoFormatada() {

        try {
            if (this.getStatusRetorno() != null && !isSucesso(Integer.parseInt(this.getStatusRetorno()))) {
                String body = this.getMensagemRetorno();
                ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                BnafarErroDto bnafarErroDto = objectMapper.readValue(body, BnafarErroDto.class);

                List<BnafarItemErroDto> bnafarErroDtoDetalhes = bnafarErroDto.getDetalhes();

                int contador = 1;
                StringBuilder sb = new StringBuilder();
                if (CollectionUtils.isNotNullEmpty(bnafarErroDtoDetalhes)) {
                    for (BnafarItemErroDto bnafarItemErroDto : bnafarErroDtoDetalhes) {
                        sb.append("Mensagem ").append(contador).append("\n");
                        sb.append("- Código: ").append(bnafarItemErroDto.getCodigo()).append("\n");
                        sb.append("- Mensagem: ").append(bnafarItemErroDto.getMensagem()).append("\n");
                        sb.append("- Caminho: ").append(Coalesce.asString(bnafarItemErroDto.getCaminho())).append("\n");
                        sb.append("- Valor rejeitado: ").append(Coalesce.asString(bnafarItemErroDto.getValorRejeitado())).append("\n");
                        sb.append("--------------------------" + "\n");
                        sb.append("\n");
                        contador++;
                    }
                }

                return sb.toString();
            } else {
                if (this.getMensagemRetorno() != null) {
                    String codigoRetorno = this.getMensagemRetorno().split(":")[1];
                    codigoRetorno = codigoRetorno.replaceAll("\\}", "");
                    return Bundle.getStringApplication("codigoRetornoBnafar", codigoRetorno);
                } else {
                    return "";
                }
            }

        } catch (IOException e) {
            return "Erro ao tentar recuparar JSON de erro retornado pelo BNAFAR: " + e.getMessage();
        }
    }

    public int getQuantidadeErrosResponse() {
        String body = this.getMensagemRetorno();
        if (body ==null || body.isEmpty()) return 0;

        ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            BnafarErroDto bnafarErroDto = objectMapper.readValue(body, BnafarErroDto.class);
            List<BnafarItemErroDto> bnafarErroDtoDetalhes = bnafarErroDto.getDetalhes();

            if (CollectionUtils.isNotNullEmpty(bnafarErroDtoDetalhes)) {
                return bnafarErroDtoDetalhes.size();
            }
        } catch (IOException e) {
            Loggable.log.error(e);
        }
        return 0;
    }

    public boolean isSucesso(Integer codigo) {
        return Integer.valueOf(200).equals(codigo) || Integer.valueOf(201).equals(codigo);
    }
}