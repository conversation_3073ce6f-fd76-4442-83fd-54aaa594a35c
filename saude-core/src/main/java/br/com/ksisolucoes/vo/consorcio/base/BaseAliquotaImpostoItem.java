package br.com.ksisolucoes.vo.consorcio.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the aliquota_imposto_item table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="aliquota_imposto_item"
 */

public abstract class BaseAliquotaImpostoItem extends BaseRootVO implements Serializable {

	public static String REF = "AliquotaImpostoItem";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_ALIQUOTA_IMPOSTO = "aliquotaImposto";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_VALOR_MAXIMO = "valorMaximo";
	public static final String PROP_VALOR_DEDUCAO = "valorDeducao";
	public static final String PROP_ALIQUOTA = "aliquota";
	public static final String PROP_VALOR_MINIMO = "valorMinimo";


	// constructors
	public BaseAliquotaImpostoItem () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAliquotaImpostoItem (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseAliquotaImpostoItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.consorcio.AliquotaImposto aliquotaImposto,
		java.lang.Double valorMinimo,
		java.lang.Double valorMaximo,
		java.lang.Double aliquota,
		java.lang.String descricao,
		java.lang.Double valorDeducao) {

		this.setCodigo(codigo);
		this.setAliquotaImposto(aliquotaImposto);
		this.setValorMinimo(valorMinimo);
		this.setValorMaximo(valorMaximo);
		this.setAliquota(aliquota);
		this.setDescricao(descricao);
		this.setValorDeducao(valorDeducao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Double valorMinimo;
	private java.lang.Double valorMaximo;
	private java.lang.Double aliquota;
	private java.lang.String descricao;
	private java.lang.Double valorDeducao;

	// many to one
	private br.com.ksisolucoes.vo.consorcio.AliquotaImposto aliquotaImposto;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_aliquota_imposto_item"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: valor_minimo
	 */
	public java.lang.Double getValorMinimo () {
		return getPropertyValue(this, valorMinimo, PROP_VALOR_MINIMO); 
	}

	/**
	 * Set the value related to the column: valor_minimo
	 * @param valorMinimo the valor_minimo value
	 */
	public void setValorMinimo (java.lang.Double valorMinimo) {
//        java.lang.Double valorMinimoOld = this.valorMinimo;
		this.valorMinimo = valorMinimo;
//        this.getPropertyChangeSupport().firePropertyChange ("valorMinimo", valorMinimoOld, valorMinimo);
	}



	/**
	 * Return the value associated with the column: valor_maximo
	 */
	public java.lang.Double getValorMaximo () {
		return getPropertyValue(this, valorMaximo, PROP_VALOR_MAXIMO); 
	}

	/**
	 * Set the value related to the column: valor_maximo
	 * @param valorMaximo the valor_maximo value
	 */
	public void setValorMaximo (java.lang.Double valorMaximo) {
//        java.lang.Double valorMaximoOld = this.valorMaximo;
		this.valorMaximo = valorMaximo;
//        this.getPropertyChangeSupport().firePropertyChange ("valorMaximo", valorMaximoOld, valorMaximo);
	}



	/**
	 * Return the value associated with the column: aliquota
	 */
	public java.lang.Double getAliquota () {
		return getPropertyValue(this, aliquota, PROP_ALIQUOTA); 
	}

	/**
	 * Set the value related to the column: aliquota
	 * @param aliquota the aliquota value
	 */
	public void setAliquota (java.lang.Double aliquota) {
//        java.lang.Double aliquotaOld = this.aliquota;
		this.aliquota = aliquota;
//        this.getPropertyChangeSupport().firePropertyChange ("aliquota", aliquotaOld, aliquota);
	}



	/**
	 * Return the value associated with the column: ds_aliquota_imposto_item
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_aliquota_imposto_item
	 * @param descricao the ds_aliquota_imposto_item value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: valor_deducao
	 */
	public java.lang.Double getValorDeducao () {
		return getPropertyValue(this, valorDeducao, PROP_VALOR_DEDUCAO); 
	}

	/**
	 * Set the value related to the column: valor_deducao
	 * @param valorDeducao the valor_deducao value
	 */
	public void setValorDeducao (java.lang.Double valorDeducao) {
//        java.lang.Double valorDeducaoOld = this.valorDeducao;
		this.valorDeducao = valorDeducao;
//        this.getPropertyChangeSupport().firePropertyChange ("valorDeducao", valorDeducaoOld, valorDeducao);
	}



	/**
	 * Return the value associated with the column: cd_aliquota_imposto
	 */
	public br.com.ksisolucoes.vo.consorcio.AliquotaImposto getAliquotaImposto () {
		return getPropertyValue(this, aliquotaImposto, PROP_ALIQUOTA_IMPOSTO); 
	}

	/**
	 * Set the value related to the column: cd_aliquota_imposto
	 * @param aliquotaImposto the cd_aliquota_imposto value
	 */
	public void setAliquotaImposto (br.com.ksisolucoes.vo.consorcio.AliquotaImposto aliquotaImposto) {
//        br.com.ksisolucoes.vo.consorcio.AliquotaImposto aliquotaImpostoOld = this.aliquotaImposto;
		this.aliquotaImposto = aliquotaImposto;
//        this.getPropertyChangeSupport().firePropertyChange ("aliquotaImposto", aliquotaImpostoOld, aliquotaImposto);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.consorcio.AliquotaImpostoItem)) return false;
		else {
			br.com.ksisolucoes.vo.consorcio.AliquotaImpostoItem aliquotaImpostoItem = (br.com.ksisolucoes.vo.consorcio.AliquotaImpostoItem) obj;
			if (null == this.getCodigo() || null == aliquotaImpostoItem.getCodigo()) return false;
			else return (this.getCodigo().equals(aliquotaImpostoItem.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}