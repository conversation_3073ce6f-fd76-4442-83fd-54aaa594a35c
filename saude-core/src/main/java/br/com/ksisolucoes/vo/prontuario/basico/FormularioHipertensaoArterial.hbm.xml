<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico">
        <class name="FormularioHipertensaoArterial" table="formulario_hipertensao_arterial">

        <id column="cd_form_hip_art"
            name="codigo"
            type="java.lang.Long"
        >
                <generator class="sequence">
                        <param name="sequence">seq_formulario_hipertensao_arterial</param>
                </generator>
        </id>

        <version column="version" name="version" type="long" />

        <many-to-one 
            column="cd_estratificacao_risco"
            name="estratificacaoRisco"
            class="br.com.ksisolucoes.vo.prontuario.basico.EstratificacaoRisco"
            not-null="false"
         />

        <property 
            column="sexo_masc"
            name="sexoMasculino"
            type="java.lang.Long"
            not-null="true"
        />

        <property
                column="idade"
                name="idade"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                column="tabagismo"
                name="tabagismo"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                column="doenca_cardio_prematura"
                name="doencaCardioPrematura"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                column="dislipidemia"
                name="dislipidemia"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                column="resistencia_insulina"
                name="resistenciaInsulina"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                column="obesidade"
                name="obesidade"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                column="hipertrofia_ventricular_esq"
                name="hipertrofiaVentricularEsquerda"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                column="espessura_mediointimal_carotida_placa_carotidea"
                name="espessuraMediointimalCarotidaPlacaCarotidea"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                column="velocidade_onda_pulso_carotido_femoral"
                name="velocidadeOndaPulsoCarotidoFemoral"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                column="indice_tornozelo_branquial"
                name="indiceTornozeloBranquial"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                column="doenca_renal_cronica"
                name="doencaRenalCronica"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                column="albuminuria"
                name="albuminuria"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                column="doenca_cerebrovascular"
                name="doencaCerebrovascular"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                column="doenca_arteria_coronaria"
                name="doencaArteriaCoronaria"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                column="pas"
                name="pas"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                column="pad"
                name="pad"
                type="java.lang.Long"
                not-null="true"
        />
        
        <property
                column="score"
                name="score"
                type="java.lang.Long"
                not-null="true"
        />
                
        <property
                column="flag_risco"
                name="flagRisco"
                type="java.lang.Long"
                not-null="true"
        />
                
        <property
                column="observacao"
                name="observacao"
                type="java.lang.String"
                not-null="false"
        />

        <property
                column="diabetes"
                name="diabete"
                type="java.lang.Long"
                not-null="false"
        />

    </class>
</hibernate-mapping>