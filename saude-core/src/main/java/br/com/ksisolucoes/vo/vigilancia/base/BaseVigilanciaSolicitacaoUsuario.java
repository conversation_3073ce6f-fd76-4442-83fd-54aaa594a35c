package br.com.ksisolucoes.vo.vigilancia.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the vigilancia_solicitacao_usuario table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="vigilancia_solicitacao_usuario"
 */

public abstract class BaseVigilanciaSolicitacaoUsuario extends BaseRootVO implements Serializable {

	public static String REF = "VigilanciaSolicitacaoUsuario";
	public static final String PROP_TIPO_DOCUMENTO = "tipoDocumento";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_CODIGO = "codigo";


	// constructors
	public BaseVigilanciaSolicitacaoUsuario () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseVigilanciaSolicitacaoUsuario (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseVigilanciaSolicitacaoUsuario (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.Long tipoDocumento) {

		this.setCodigo(codigo);
		this.setUsuario(usuario);
		this.setTipoDocumento(tipoDocumento);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long tipoDocumento;

	// many to one
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_vigilancia_solicitacao_usuario"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: tipo_documento
	 */
	public java.lang.Long getTipoDocumento () {
		return getPropertyValue(this, tipoDocumento, PROP_TIPO_DOCUMENTO); 
	}

	/**
	 * Set the value related to the column: tipo_documento
	 * @param tipoDocumento the tipo_documento value
	 */
	public void setTipoDocumento (java.lang.Long tipoDocumento) {
//        java.lang.Long tipoDocumentoOld = this.tipoDocumento;
		this.tipoDocumento = tipoDocumento;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDocumento", tipoDocumentoOld, tipoDocumento);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.VigilanciaSolicitacaoUsuario)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.VigilanciaSolicitacaoUsuario vigilanciaSolicitacaoUsuario = (br.com.ksisolucoes.vo.vigilancia.VigilanciaSolicitacaoUsuario) obj;
			if (null == this.getCodigo() || null == vigilanciaSolicitacaoUsuario.getCodigo()) return false;
			else return (this.getCodigo().equals(vigilanciaSolicitacaoUsuario.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}