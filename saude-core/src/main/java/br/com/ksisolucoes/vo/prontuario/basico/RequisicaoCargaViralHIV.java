package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseRequisicaoCargaViralHIV;

import java.io.Serializable;



public class RequisicaoCargaViralHIV extends BaseRequisicaoCargaViralHIV implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequisicaoCargaViralHIV () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequisicaoCargaViralHIV (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequisicaoCargaViralHIV (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicao,
		br.com.ksisolucoes.vo.prontuario.basico.Cid cid) {

		super (
			codigo,
			exameRequisicao,
			cid);
	}

/*[CONSTRUCTOR MARKER END]*/

	public static enum MotivoSolicitacao implements IEnum<RequisicaoDeteccaoDNAProViralHIV1.MotivoSolicitacao> {

		DIAGNOSTICO(1L, Bundle.getStringApplication("rotulo_diagnostico")),
		MONITORAMENTO_COM_TRATAMENTO_ANTIRRETROVIRAL(2L, Bundle.getStringApplication("rotulo_monitoramento_pessoas_com_tratamento_antirretroviral")),
		MONITORAMENTO_SEM_TRATAMENTO_ANTIRRETROVIRAL(3L, Bundle.getStringApplication("rotulo_monitoramento_pessoas_sem_tratamento_antirretroviral"));

		private final Long value;
		private final String descricao;

		private MotivoSolicitacao(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		@Override
		public Object value() {
			return this.value;
		}

		@Override
		public String descricao() {
			return this.descricao;
		}

		@Override
		public String toString() {
			return descricao;
		}
	}

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}