<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.financeiro">
    <class name="VigilanciaGerarFinanceiro" table="vigilancia_gerar_financeiro">

        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_vigilancia_gerar_financeiro"
        >
            <generator class="assigned"/>
        </id>
        <version column="version" name="version" type="long"/>

        <property
                column="tipo_documento"
                name="tipoDocumento"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                column="dt_cadastro"
                name="dataCadastro"
                not-null="true"
                type="timestamp"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                name="usuario"
                not-null="true"
                column="cd_usuario"
        />

    </class>
</hibernate-mapping>
