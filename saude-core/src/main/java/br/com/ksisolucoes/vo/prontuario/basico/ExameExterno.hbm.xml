<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="ExameExterno" table="exame_externo" >

        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_exame_externo"
        >
            <generator class="assigned"/>
        </id>
        <version column="version" name="version" type="long" />

        <many-to-one
                class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"
                column="cd_usu_cadsus"
                name="usuarioCadsus"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
                column="nr_atendimento"
                name="atendimento"
                not-null="true"
        />

        <property
                name="dataExame"
                column="dt_exame"
                type="java.util.Date"
        />

        <property
                name="descricao"
                column="ds_exame"
                type="java.lang.String"
                not-null="true"
        />

        <property
                name="resultado"
                column="ds_resultado"
                type="java.lang.String"
                not-null="true"
        />

        <property
                name="dataCadastro"
                column="dt_cadastro"
                type="timestamp"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario"
                name="usuario"
                not-null="true"
        />

    </class>
</hibernate-mapping>
