package br.com.ksisolucoes.vo.vendas.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the cond_pagamento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="cond_pagamento"
 */

public abstract class BaseCondicaoPagamento extends BaseRootVO implements Serializable {

	public static String REF = "CondicaoPagamento";
	public static final String PROP_QUANTIDADE_PARCELAS_ANTECIPADAS = "quantidadeParcelasAntecipadas";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_QUANTIDADE_PARCELAS = "quantidadeParcelas";
	public static final String PROP_DESCRICAO = "descricao";


	// constructors
	public BaseCondicaoPagamento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseCondicaoPagamento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseCondicaoPagamento (
		java.lang.Long codigo,
		java.lang.String descricao,
		java.lang.Long quantidadeParcelas) {

		this.setCodigo(codigo);
		this.setDescricao(descricao);
		this.setQuantidadeParcelas(quantidadeParcelas);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.lang.Long quantidadeParcelasAntecipadas;
	private java.lang.Long quantidadeParcelas;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cod_pto"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: descricao
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: descricao
	 * @param descricao the descricao value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: qtdade_parcela_ant
	 */
	public java.lang.Long getQuantidadeParcelasAntecipadas () {
		return getPropertyValue(this, quantidadeParcelasAntecipadas, PROP_QUANTIDADE_PARCELAS_ANTECIPADAS); 
	}

	/**
	 * Set the value related to the column: qtdade_parcela_ant
	 * @param quantidadeParcelasAntecipadas the qtdade_parcela_ant value
	 */
	public void setQuantidadeParcelasAntecipadas (java.lang.Long quantidadeParcelasAntecipadas) {
//        java.lang.Long quantidadeParcelasAntecipadasOld = this.quantidadeParcelasAntecipadas;
		this.quantidadeParcelasAntecipadas = quantidadeParcelasAntecipadas;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeParcelasAntecipadas", quantidadeParcelasAntecipadasOld, quantidadeParcelasAntecipadas);
	}



	/**
	 * Return the value associated with the column: qtdade_parcela
	 */
	public java.lang.Long getQuantidadeParcelas () {
		return getPropertyValue(this, quantidadeParcelas, PROP_QUANTIDADE_PARCELAS); 
	}

	/**
	 * Set the value related to the column: qtdade_parcela
	 * @param quantidadeParcelas the qtdade_parcela value
	 */
	public void setQuantidadeParcelas (java.lang.Long quantidadeParcelas) {
//        java.lang.Long quantidadeParcelasOld = this.quantidadeParcelas;
		this.quantidadeParcelas = quantidadeParcelas;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeParcelas", quantidadeParcelasOld, quantidadeParcelas);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vendas.CondicaoPagamento)) return false;
		else {
			br.com.ksisolucoes.vo.vendas.CondicaoPagamento condicaoPagamento = (br.com.ksisolucoes.vo.vendas.CondicaoPagamento) obj;
			if (null == this.getCodigo() || null == condicaoPagamento.getCodigo()) return false;
			else return (this.getCodigo().equals(condicaoPagamento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}