package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import java.io.Serializable;

import br.com.ksisolucoes.vo.prontuario.basico.base.BaseMedicamentoPaciente;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class MedicamentoPaciente extends BaseMedicamentoPaciente implements CodigoManager {
	private static final long serialVersionUID = 1L;
        public static final String PROP_USO_CONTINUO_DESCRICAO = "usoContinuoDescricao";
        

/*[CONSTRUCTOR MARKER BEGIN]*/
	public MedicamentoPaciente () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public MedicamentoPaciente (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public MedicamentoPaciente (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		br.com.ksisolucoes.vo.entradas.estoque.TipoViaMedicamento tipoViaMedicamento,
		br.com.ksisolucoes.vo.prontuario.basico.TipoReceita tipoReceita,
		java.lang.String nomeProduto,
		java.util.Date dataCadastro,
		java.util.Date dataUsuario,
		java.lang.String posologia,
		java.lang.Long quantidadePrescrita,
		java.lang.String usoContinuo,
		java.lang.Long status,
		java.lang.Long receita) {

		super (
			codigo,
			usuario,
			usuarioCadsus,
			tipoViaMedicamento,
			tipoReceita,
			nomeProduto,
			dataCadastro,
			dataUsuario,
			posologia,
			quantidadePrescrita,
			usoContinuo,
			status,
			receita);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public String getUsoContinuoDescricao(){
        if (RepositoryComponentDefault.SIM.equals(this.getUsoContinuo())) {
            return Bundle.getStringApplication("rotulo_sim");
        } else {
            return Bundle.getStringApplication("rotulo_nao");
        }
    }
    
    public static enum Status implements IEnum<Status> {

        ATIVO(0L, Bundle.getStringApplication("rotulo_ativo")),
        CANCELADO(1L, Bundle.getStringApplication("rotulo_cancelado"));
        private Long value;
        private String descricao;

        private Status(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Status valeuOf(Long value) {
            for (Status status : Status.values()) {
                if (status.value().equals(value)) {
                    return status;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }
}