<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.basico"  >
    <class name="SalaUnidade" table="sala_unidade">

        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_sala"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.basico.Empresa"
            name="empresa"
            not-null="true"
        >
            <column name="empresa" />
        </many-to-one>
        
        <property
            name="descricao"
            column="ds_sala"
            type="java.lang.String"
            length="50" 
        />
        
    </class>
</hibernate-mapping>
