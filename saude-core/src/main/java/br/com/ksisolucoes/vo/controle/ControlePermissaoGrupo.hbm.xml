<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.controle"  >
	<class 
		name="ControlePermissaoGrupo"
		table="controle_permissoes_grupo"
	>
	
		<id
			name="codigo"
			type="java.lang.Long"
			column="cd_ctr_permissao_grupo"
		>
			<generator class="assigned"/>
		</id> <version column="version" name="version" type="long" />
		
		<many-to-one
			name="controleProgramaGrupo"
			class="br.com.ksisolucoes.vo.controle.ControleProgramaGrupo"
			column="cd_prg_grupo"
		/>
		
		<many-to-one
			name="permissao"
			class="br.com.ksisolucoes.vo.controle.Permissao"
			column="cd_permissao"
		/>

	</class>
</hibernate-mapping>