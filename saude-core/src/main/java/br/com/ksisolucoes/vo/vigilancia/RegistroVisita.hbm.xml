<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia"  >
    <class name="RegistroVisita" table="registro_visita">
        <id
            column="cd_registro_visita"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />
               
        <many-to-one class="br.com.ksisolucoes.vo.vigilancia.Estabelecimento"
                     name="estabelecimento" not-null="true">
            <column name="cd_estabelecimento" />
        </many-to-one>
        
        <many-to-one class="br.com.ksisolucoes.vo.vigilancia.MotivoVisita"
                     name="motivoVisita" not-null="true">
            <column name="cd_mot_visita" />
        </many-to-one>
        
        <many-to-one class="br.com.ksisolucoes.vo.controle.Usuario"
                     name="usuario" not-null="true">
            <column name="cd_usuario" />
        </many-to-one>
        
        <many-to-one class="br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia"
                     name="requerimentoVigilancia" not-null="true">
            <column name="cd_req_vigilancia" />
        </many-to-one>
        
        <property
            column="dt_usuario"
            name="dataUsuario"
            not-null="true"
            type="java.util.Date"
        />
        
        <property
            column="dt_cadastro"
            name="dataCadastro"
            not-null="true"
            type="java.util.Date"
        />
        
        <property
            column="dt_visita"
            name="dataVisita"
            not-null="true"
            type="java.util.Date"
        />
        
        <property
            column="ds_visita"
            name="descricao"
            not-null="true"
            type="java.lang.String"
        />
        
        <property
            column="resp_estabelecimento"
            name="responsavelEstabelecimento"
            not-null="true"
            type="java.lang.String"
        />
        
        <property
            column="resultado_inspecao"
            name="resultadoInspecao"
            type="java.lang.Long"
        />
        
        <property
            column="ds_estabelecimento"
            name="descricaoEstabelecimento"
            not-null="true"
            type="java.lang.String"
        />
        
    </class>
</hibernate-mapping>
