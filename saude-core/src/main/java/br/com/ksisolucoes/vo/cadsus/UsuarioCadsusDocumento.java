package br.com.ksisolucoes.vo.cadsus;


import br.com.celk.integracao.IntegracaoRest;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.cadsus.base.BaseUsuarioCadsusDocumento;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import java.io.Serializable;
import java.text.ParseException;
import javax.swing.text.MaskFormatter;


@IntegracaoRest
public class UsuarioCadsusDocumento extends BaseUsuarioCadsusDocumento implements CodigoManager{
	private static final long serialVersionUID = 1L;

        public static final Long STATUS_ATIVO = 0L;
        public static final Long STATUS_EXCLUIDO = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public UsuarioCadsusDocumento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public UsuarioCadsusDocumento (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public UsuarioCadsusDocumento (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {

		super (
			codigo,
			usuarioCadsus);
	}

/*[CONSTRUCTOR MARKER END]*/

        public String getNumeroMatriculaFormatado(){
            String matricula = getNumeroMatricula();
        if (matricula != null && !matricula.trim().equals("")){
            try {
                MaskFormatter m = new MaskFormatter("######.##.##.####.#.#####.###.#######.##");
                m.setValueContainsLiteralCharacters(false);

                return m.valueToString(matricula);
            } catch (ParseException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        }
        return "";
    }
        
    public void setCodigoManager(Serializable key) {
        this.setCodigo((Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    @Override
    public String getNumeroDocumento() {
        return lgpdFilterNumberAsText(super.getNumeroDocumento());
    }

    @Override
    public String getNumeroMatricula() {
        return lgpdFilterNumberAsText(super.getNumeroMatricula());
    }

}