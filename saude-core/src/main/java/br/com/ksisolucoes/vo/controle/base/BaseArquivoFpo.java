package br.com.ksisolucoes.vo.controle.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the arquivo_fpo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="arquivo_fpo"
 */

public abstract class BaseArquivoFpo extends BaseRootVO implements Serializable {

	public static String REF = "ArquivoFpo";
	public static final String PROP_COMPETENCIA = "competencia";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_DATA_GERACAO = "dataGeracao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_SITUACAO = "situacao";
	public static final String PROP_NOME_ARQUIVO = "nomeArquivo";
	public static final String PROP_LOGS = "logs";
	public static final String PROP_ASYNC_PROCESS = "asyncProcess";
	public static final String PROP_GERENCIADOR_ARQUIVO = "gerenciadorArquivo";


	// constructors
	public BaseArquivoFpo () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseArquivoFpo (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String nomeArquivo;
	private java.util.Date competencia;
	private java.util.Date dataGeracao;
	private java.lang.Long situacao;
	private java.lang.String logs;

	// many to one
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.service.AsyncProcess asyncProcess;
	private br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo gerenciadorArquivo;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_arquivo_fpo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: nome_arquivo
	 */
	public java.lang.String getNomeArquivo () {
		return getPropertyValue(this, nomeArquivo, PROP_NOME_ARQUIVO); 
	}

	/**
	 * Set the value related to the column: nome_arquivo
	 * @param nomeArquivo the nome_arquivo value
	 */
	public void setNomeArquivo (java.lang.String nomeArquivo) {
//        java.lang.String nomeArquivoOld = this.nomeArquivo;
		this.nomeArquivo = nomeArquivo;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeArquivo", nomeArquivoOld, nomeArquivo);
	}



	/**
	 * Return the value associated with the column: competencia
	 */
	public java.util.Date getCompetencia () {
		return getPropertyValue(this, competencia, PROP_COMPETENCIA); 
	}

	/**
	 * Set the value related to the column: competencia
	 * @param competencia the competencia value
	 */
	public void setCompetencia (java.util.Date competencia) {
//        java.util.Date competenciaOld = this.competencia;
		this.competencia = competencia;
//        this.getPropertyChangeSupport().firePropertyChange ("competencia", competenciaOld, competencia);
	}



	/**
	 * Return the value associated with the column: dt_geracao
	 */
	public java.util.Date getDataGeracao () {
		return getPropertyValue(this, dataGeracao, PROP_DATA_GERACAO); 
	}

	/**
	 * Set the value related to the column: dt_geracao
	 * @param dataGeracao the dt_geracao value
	 */
	public void setDataGeracao (java.util.Date dataGeracao) {
//        java.util.Date dataGeracaoOld = this.dataGeracao;
		this.dataGeracao = dataGeracao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataGeracao", dataGeracaoOld, dataGeracao);
	}



	/**
	 * Return the value associated with the column: situacao
	 */
	public java.lang.Long getSituacao () {
		return getPropertyValue(this, situacao, PROP_SITUACAO); 
	}

	/**
	 * Set the value related to the column: situacao
	 * @param situacao the situacao value
	 */
	public void setSituacao (java.lang.Long situacao) {
//        java.lang.Long situacaoOld = this.situacao;
		this.situacao = situacao;
//        this.getPropertyChangeSupport().firePropertyChange ("situacao", situacaoOld, situacao);
	}



	/**
	 * Return the value associated with the column: logs
	 */
	public java.lang.String getLogs () {
		return getPropertyValue(this, logs, PROP_LOGS); 
	}

	/**
	 * Set the value related to the column: logs
	 * @param logs the logs value
	 */
	public void setLogs (java.lang.String logs) {
//        java.lang.String logsOld = this.logs;
		this.logs = logs;
//        this.getPropertyChangeSupport().firePropertyChange ("logs", logsOld, logs);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_process
	 */
	public br.com.ksisolucoes.vo.service.AsyncProcess getAsyncProcess () {
		return getPropertyValue(this, asyncProcess, PROP_ASYNC_PROCESS); 
	}

	/**
	 * Set the value related to the column: cd_process
	 * @param asyncProcess the cd_process value
	 */
	public void setAsyncProcess (br.com.ksisolucoes.vo.service.AsyncProcess asyncProcess) {
//        br.com.ksisolucoes.vo.service.AsyncProcess asyncProcessOld = this.asyncProcess;
		this.asyncProcess = asyncProcess;
//        this.getPropertyChangeSupport().firePropertyChange ("asyncProcess", asyncProcessOld, asyncProcess);
	}



	/**
	 * Return the value associated with the column: cd_gerenciador_arquivo
	 */
	public br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo getGerenciadorArquivo () {
		return getPropertyValue(this, gerenciadorArquivo, PROP_GERENCIADOR_ARQUIVO); 
	}

	/**
	 * Set the value related to the column: cd_gerenciador_arquivo
	 * @param gerenciadorArquivo the cd_gerenciador_arquivo value
	 */
	public void setGerenciadorArquivo (br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo gerenciadorArquivo) {
//        br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo gerenciadorArquivoOld = this.gerenciadorArquivo;
		this.gerenciadorArquivo = gerenciadorArquivo;
//        this.getPropertyChangeSupport().firePropertyChange ("gerenciadorArquivo", gerenciadorArquivoOld, gerenciadorArquivo);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.controle.ArquivoFpo)) return false;
		else {
			br.com.ksisolucoes.vo.controle.ArquivoFpo arquivoFpo = (br.com.ksisolucoes.vo.controle.ArquivoFpo) obj;
			if (null == this.getCodigo() || null == arquivoFpo.getCodigo()) return false;
			else return (this.getCodigo().equals(arquivoFpo.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}