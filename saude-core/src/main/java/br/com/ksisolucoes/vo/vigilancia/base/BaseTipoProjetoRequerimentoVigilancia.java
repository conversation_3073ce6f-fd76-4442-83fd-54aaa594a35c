package br.com.ksisolucoes.vo.vigilancia.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the tipo_projeto_req_vig table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="tipo_projeto_req_vig"
 */

public abstract class BaseTipoProjetoRequerimentoVigilancia extends BaseRootVO implements Serializable {

	public static String REF = "TipoProjetoRequerimentoVigilancia";
	public static final String PROP_AREA = "area";
	public static final String PROP_TIPO_PROJETO_VIGILANCIA = "tipoProjetoVigilancia";
	public static final String PROP_REQUERIMENTO_VIGILANCIA = "requerimentoVigilancia";
	public static final String PROP_CODIGO = "codigo";


	// constructors
	public BaseTipoProjetoRequerimentoVigilancia () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseTipoProjetoRequerimentoVigilancia (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseTipoProjetoRequerimentoVigilancia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia,
		br.com.ksisolucoes.vo.vigilancia.TipoProjetoVigilancia tipoProjetoVigilancia,
		java.lang.Double area) {

		this.setCodigo(codigo);
		this.setRequerimentoVigilancia(requerimentoVigilancia);
		this.setTipoProjetoVigilancia(tipoProjetoVigilancia);
		this.setArea(area);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Double area;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia;
	private br.com.ksisolucoes.vo.vigilancia.TipoProjetoVigilancia tipoProjetoVigilancia;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_tipo_projeto_req_vig"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: area
	 */
	public java.lang.Double getArea () {
		return getPropertyValue(this, area, PROP_AREA); 
	}

	/**
	 * Set the value related to the column: area
	 * @param area the area value
	 */
	public void setArea (java.lang.Double area) {
//        java.lang.Double areaOld = this.area;
		this.area = area;
//        this.getPropertyChangeSupport().firePropertyChange ("area", areaOld, area);
	}



	/**
	 * Return the value associated with the column: cd_req_vigilancia
	 */
	public br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia getRequerimentoVigilancia () {
		return getPropertyValue(this, requerimentoVigilancia, PROP_REQUERIMENTO_VIGILANCIA); 
	}

	/**
	 * Set the value related to the column: cd_req_vigilancia
	 * @param requerimentoVigilancia the cd_req_vigilancia value
	 */
	public void setRequerimentoVigilancia (br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia) {
//        br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilanciaOld = this.requerimentoVigilancia;
		this.requerimentoVigilancia = requerimentoVigilancia;
//        this.getPropertyChangeSupport().firePropertyChange ("requerimentoVigilancia", requerimentoVigilanciaOld, requerimentoVigilancia);
	}



	/**
	 * Return the value associated with the column: cd_tp_projeto_vigilancia
	 */
	public br.com.ksisolucoes.vo.vigilancia.TipoProjetoVigilancia getTipoProjetoVigilancia () {
		return getPropertyValue(this, tipoProjetoVigilancia, PROP_TIPO_PROJETO_VIGILANCIA); 
	}

	/**
	 * Set the value related to the column: cd_tp_projeto_vigilancia
	 * @param tipoProjetoVigilancia the cd_tp_projeto_vigilancia value
	 */
	public void setTipoProjetoVigilancia (br.com.ksisolucoes.vo.vigilancia.TipoProjetoVigilancia tipoProjetoVigilancia) {
//        br.com.ksisolucoes.vo.vigilancia.TipoProjetoVigilancia tipoProjetoVigilanciaOld = this.tipoProjetoVigilancia;
		this.tipoProjetoVigilancia = tipoProjetoVigilancia;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoProjetoVigilancia", tipoProjetoVigilanciaOld, tipoProjetoVigilancia);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.TipoProjetoRequerimentoVigilancia)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.TipoProjetoRequerimentoVigilancia tipoProjetoRequerimentoVigilancia = (br.com.ksisolucoes.vo.vigilancia.TipoProjetoRequerimentoVigilancia) obj;
			if (null == this.getCodigo() || null == tipoProjetoRequerimentoVigilancia.getCodigo()) return false;
			else return (this.getCodigo().equals(tipoProjetoRequerimentoVigilancia.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}