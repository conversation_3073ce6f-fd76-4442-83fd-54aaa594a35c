package br.com.ksisolucoes.vo.esus;

import java.io.Serializable;

import br.com.celk.integracao.IntegracaoRest;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.esus.base.BaseTipoAtividadePublico;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

@IntegracaoRest
public class TipoAtividadePublico extends BaseTipoAtividadePublico implements CodigoManager {
    private static final long serialVersionUID = 1L;

    public static enum CodigoESUS implements IEnum {

        COMUNIDADE_GERAL(1L, Bundle.getStringApplication("rotulo_comunidade_geral")),
        CRIANCA_0_3_ANOS(2L, Bundle.getStringApplication("rotulo_crianca_zero_tres_anos")),
        CRIANCA_4_5_ANOS(3L, Bundle.getStringApplication("rotulo_crianca_quatro_cinco_anos")),
        CRIANCA_6_11_ANOS(4L, Bundle.getStringApplication("rotulo_crianca_seis_onze_anos")),
        ADOLESCENTE(5L, Bundle.getStringApplication("rotulo_adolescente")),
        MULHER(6L, Bundle.getStringApplication("rotulo_mulher")),
        GESTANTE(7L, Bundle.getStringApplication("rotulo_gestante")),
        HOMEM(8L, Bundle.getStringApplication("rotulo_homem")),
        FAMILIARES(9L, Bundle.getStringApplication("rotulo_familiares")),
        IDOSO(10L, Bundle.getStringApplication("rotulo_idoso")),
        PESSOAS_DOENCAS_CRONICAS(12L, Bundle.getStringApplication("rotulo_pessoas_doencas_cronicas")),
        USUARIO_TABACO(13L, Bundle.getStringApplication("rotulo_usuario_tabaco")),
        USUARIO_ALCOOL(14L, Bundle.getStringApplication("rotulo_usuario_alcool")),
        USUARIO_OUTRAS_DROGAS(15L, Bundle.getStringApplication("rotulo_usuario_outras_drogas")),
        PORTADOR_SOFRIMENTO_TRANSTORNO_MENTAL(16L, Bundle.getStringApplication("rotulo_portador_sofrimento_transtorno_mental")),
        PROFISSIONAL_EDUCACAO(17L, Bundle.getStringApplication("rotulo_profissional_educacao")),
        OUTROS(18L, Bundle.getStringApplication("rotulo_outros")),
        ;

        private CodigoESUS(Long codigo, String descricao) {
            this.codigo = codigo;
            this.descricao = descricao;
        }

        private Long codigo;
        private String descricao;

        @Override
        public Long value() {
            return this.codigo;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return this.descricao;
        }
    }

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public TipoAtividadePublico() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public TipoAtividadePublico(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public TipoAtividadePublico(
            java.lang.Long codigo,
            java.lang.String descricaoPublicoAlvo,
            java.lang.Long codigoEsus) {

        super(
                codigo,
                descricaoPublicoAlvo,
                codigoEsus);
    }

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}