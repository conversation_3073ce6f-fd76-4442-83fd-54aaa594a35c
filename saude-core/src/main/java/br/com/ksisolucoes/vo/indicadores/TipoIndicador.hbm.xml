<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.indicadores"  >
    <class name="TipoIndicador" table="tipo_indicador" >

        <id
            name="codigo"
            column="cd_tp_indicador"
            type="java.lang.Long"
        />

		<version column="version" name="version" type="long" />

        <property 
	        name="descricao"
	        column="ds_tp_indicador"
	        type="java.lang.String"
	        length="50"
	        not-null="true"
        />

        <property 
	        name="sql"
	        column="sql"
	        type="java.lang.String"
	        not-null="false"
        />
        
        <property 
	        name="labelResultado"
	        column="label_resultado"
	        type="java.lang.String"
	        length="25"
	        not-null="false"
        />

        <property
                name="labelDescricao"
                column="label_descricao"
                type="java.lang.String"
                length="50"
                not-null="false"
        />
                
        <property
                name="flagFiltros"
                column="flag_filtros"
                type="java.lang.Long"
                not-null="false"
        />        
    </class>
</hibernate-mapping>
