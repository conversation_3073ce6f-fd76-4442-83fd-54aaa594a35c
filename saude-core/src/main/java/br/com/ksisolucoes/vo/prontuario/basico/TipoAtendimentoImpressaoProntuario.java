package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;

import br.com.ksisolucoes.vo.prontuario.basico.base.BaseTipoAtendimentoImpressaoProntuario;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class TipoAtendimentoImpressaoProntuario extends BaseTipoAtendimentoImpressaoProntuario implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public TipoAtendimentoImpressaoProntuario () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public TipoAtendimentoImpressaoProntuario (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public TipoAtendimentoImpressaoProntuario (
		java.lang.Long codigo,
		java.lang.Long tipoRegistro) {

		super (
			codigo,
			tipoRegistro);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public String getDescricaoTipoRegistro() {
        AtendimentoProntuario.TipoRegistro tipoRegistro = AtendimentoProntuario.TipoRegistro.valeuOf(getTipoRegistro());
        if (tipoRegistro != null && tipoRegistro.descricao() != null) {
            return tipoRegistro.descricao();
        }
        return "";
    }
}