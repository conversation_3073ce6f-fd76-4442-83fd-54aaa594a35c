package br.com.ksisolucoes.vo.consorcio;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.consorcio.base.BaseConsorcioPrestador;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.DictionaryData;

import java.io.Serializable;


@DictionaryData(referenceField = "empresaPrestador.referencia")
public class ConsorcioPrestador extends BaseConsorcioPrestador implements CodigoManager {
	private static final long serialVersionUID = 1L;

        public static final String PROP_DESCRICAO_STATUS = "descricaoStatus";
        public static final String PROP_DESCRICAO_CONSORCIO_PRESTADOR_ATIVO = "descricaoConsorcioPrestadorAtivo";

        public enum StatusConsorcioPrestador implements IEnum<StatusConsorcioPrestador>{
            ATIVO(0L, Bundle.getStringApplication("rotulo_ativo")),
            INATIVO(1L, Bundle.getStringApplication("rotulo_inativo")),
        ;
            
            private Long value;
            private String descricao;

        private StatusConsorcioPrestador(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
            
        }
        
        public enum TipoCobrancaImposto implements IEnum<TipoCobrancaImposto>{
            ISENTO(0L, Bundle.getStringApplication("rotulo_isento")),
            ALIQUOTA_FIXA(1L, Bundle.getStringApplication("rotulo_aliquota_fixa")),
            TABELA_REFERENCIA(2L, Bundle.getStringApplication("rotulo_tabela_referencia")),
        ;
            
            private Long value;
            private String descricao;

        private TipoCobrancaImposto(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
            
        }
        
/*[CONSTRUCTOR MARKER BEGIN]*/
	public ConsorcioPrestador () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ConsorcioPrestador (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ConsorcioPrestador (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Empresa empresaConsorcio,
		br.com.ksisolucoes.vo.basico.Empresa empresaPrestador,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.Long status,
		java.util.Date dataUsuario,
		java.lang.Long flagManterPrecoSus) {

		super (
			codigo,
			empresaConsorcio,
			empresaPrestador,
			usuario,
			status,
			dataUsuario,
			flagManterPrecoSus);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public String getDescricaoStatus(){
        if (StatusConsorcioPrestador.ATIVO.value().equals(getStatus())) {
            return StatusConsorcioPrestador.ATIVO.descricao();
        } else if (StatusConsorcioPrestador.INATIVO.value().equals(getStatus())) {
            return StatusConsorcioPrestador.INATIVO.descricao();
        }
        return Bundle.getStringApplication("rotulo_desconhecido");
    }

    public String getDescricaoConsorcioPrestadorAtivo() {
        if (RepositoryComponentDefault.SIM.equals(getEmpresaPrestador().getAtivo())) {
            return Bundle.getStringApplication("rotulo_ativo");
        } else if (RepositoryComponentDefault.NAO.equals(getEmpresaPrestador().getAtivo())) {
            return Bundle.getStringApplication("rotulo_inativo");
        }
        return "";
    }

}