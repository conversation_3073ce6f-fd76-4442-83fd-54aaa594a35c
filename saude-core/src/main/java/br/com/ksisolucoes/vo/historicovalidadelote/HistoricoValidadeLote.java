package br.com.ksisolucoes.vo.historicovalidadelote;

import br.com.ksisolucoes.vo.historicovalidadelote.base.BaseHistoricoValidadeLote;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;


public class HistoricoValidadeLote extends BaseHistoricoValidadeLote implements CodigoManager {
	private static final long serialVersionUID = 1L;

    public static final String PROP_FABRICANTE_FORMATADO = "fabricanteFormatado";

/*[CONSTRUCTOR MARKER BEGIN]*/
	public HistoricoValidadeLote () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public HistoricoValidadeLote (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public HistoricoValidadeLote (
		java.lang.Long codigo,
		java.util.Date dataValidadeAntiga) {

		super (
			codigo,
			dataValidadeAntiga);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }


    public String getFabricanteFormatado() {
        if (getFabricante() != null && getFabricante().getCodigo() != null && StringUtils.isNotEmpty(getFabricante().getDescricao())) {
            return getFabricante().getCodigo() + " - " + getFabricante().getDescricao();

        } else if (getProduto() != null && getProduto().getFabricante() != null && getProduto().getFabricante().getCodigo() != null && StringUtils.isNotEmpty(getProduto().getFabricante().getDescricao())) {
            return getProduto().getFabricante().getCodigo() + " - " + getProduto().getFabricante().getDescricao();
        }
        return "";
    }
}
