package br.com.ksisolucoes.vo.frota.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the motorista table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="motorista"
 */

public abstract class BaseMotorista extends BaseRootVO implements Serializable {

	public static String REF = "Motorista";
	public static final String PROP_NUMERO = "numero";
	public static final String PROP_DATA_VALIDADE_HABILITACAO = "dataValidadeHabilitacao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_ATIVO = "ativo";
	public static final String PROP_NUMERO_HABILITACAO = "numeroHabilitacao";
	public static final String PROP_CEP = "cep";
	public static final String PROP_CELULAR = "celular";
	public static final String PROP_DATA_NASCIMENTO = "dataNascimento";
	public static final String PROP_COMPLEMENTO = "complemento";
	public static final String PROP_CIDADE = "cidade";
	public static final String PROP_LOGRADOURO = "logradouro";
	public static final String PROP_REFERENCIA = "referencia";
	public static final String PROP_PROFISSIONAL = "profissional";
	public static final String PROP_TELEFONE = "telefone";
	public static final String PROP_NOME = "nome";
	public static final String PROP_BAIRRO = "bairro";


	// constructors
	public BaseMotorista () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseMotorista (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseMotorista (
		java.lang.Long codigo,
		java.lang.String nome,
		java.util.Date dataNascimento,
		java.lang.String numeroHabilitacao,
		java.util.Date dataValidadeHabilitacao) {

		this.setCodigo(codigo);
		this.setNome(nome);
		this.setDataNascimento(dataNascimento);
		this.setNumeroHabilitacao(numeroHabilitacao);
		this.setDataValidadeHabilitacao(dataValidadeHabilitacao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String referencia;
	private java.lang.String nome;
	private java.util.Date dataNascimento;
	private java.lang.String numeroHabilitacao;
	private java.util.Date dataValidadeHabilitacao;
	private java.lang.String logradouro;
	private java.lang.String numero;
	private java.lang.String complemento;
	private java.lang.String bairro;
	private java.lang.String cep;
	private java.lang.String telefone;
	private java.lang.String celular;
	private java.lang.String ativo;

	// many to one
	private br.com.ksisolucoes.vo.basico.Cidade cidade;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissional;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  column="cd_motorista"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: referencia
	 */
	public java.lang.String getReferencia () {
		return getPropertyValue(this, referencia, PROP_REFERENCIA); 
	}

	/**
	 * Set the value related to the column: referencia
	 * @param referencia the referencia value
	 */
	public void setReferencia (java.lang.String referencia) {
//        java.lang.String referenciaOld = this.referencia;
		this.referencia = referencia;
//        this.getPropertyChangeSupport().firePropertyChange ("referencia", referenciaOld, referencia);
	}



	/**
	 * Return the value associated with the column: nm_motorista
	 */
	public java.lang.String getNome () {
		return getPropertyValue(this, nome, PROP_NOME); 
	}

	/**
	 * Set the value related to the column: nm_motorista
	 * @param nome the nm_motorista value
	 */
	public void setNome (java.lang.String nome) {
//        java.lang.String nomeOld = this.nome;
		this.nome = nome;
//        this.getPropertyChangeSupport().firePropertyChange ("nome", nomeOld, nome);
	}



	/**
	 * Return the value associated with the column: dt_nascimento
	 */
	public java.util.Date getDataNascimento () {
		return getPropertyValue(this, dataNascimento, PROP_DATA_NASCIMENTO); 
	}

	/**
	 * Set the value related to the column: dt_nascimento
	 * @param dataNascimento the dt_nascimento value
	 */
	public void setDataNascimento (java.util.Date dataNascimento) {
//        java.util.Date dataNascimentoOld = this.dataNascimento;
		this.dataNascimento = dataNascimento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataNascimento", dataNascimentoOld, dataNascimento);
	}



	/**
	 * Return the value associated with the column: nr_habilitacao
	 */
	public java.lang.String getNumeroHabilitacao () {
		return getPropertyValue(this, numeroHabilitacao, PROP_NUMERO_HABILITACAO); 
	}

	/**
	 * Set the value related to the column: nr_habilitacao
	 * @param numeroHabilitacao the nr_habilitacao value
	 */
	public void setNumeroHabilitacao (java.lang.String numeroHabilitacao) {
//        java.lang.String numeroHabilitacaoOld = this.numeroHabilitacao;
		this.numeroHabilitacao = numeroHabilitacao;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroHabilitacao", numeroHabilitacaoOld, numeroHabilitacao);
	}



	/**
	 * Return the value associated with the column: dt_val_habilitacao
	 */
	public java.util.Date getDataValidadeHabilitacao () {
		return getPropertyValue(this, dataValidadeHabilitacao, PROP_DATA_VALIDADE_HABILITACAO); 
	}

	/**
	 * Set the value related to the column: dt_val_habilitacao
	 * @param dataValidadeHabilitacao the dt_val_habilitacao value
	 */
	public void setDataValidadeHabilitacao (java.util.Date dataValidadeHabilitacao) {
//        java.util.Date dataValidadeHabilitacaoOld = this.dataValidadeHabilitacao;
		this.dataValidadeHabilitacao = dataValidadeHabilitacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataValidadeHabilitacao", dataValidadeHabilitacaoOld, dataValidadeHabilitacao);
	}



	/**
	 * Return the value associated with the column: nm_logradouro
	 */
	public java.lang.String getLogradouro () {
		return getPropertyValue(this, logradouro, PROP_LOGRADOURO); 
	}

	/**
	 * Set the value related to the column: nm_logradouro
	 * @param logradouro the nm_logradouro value
	 */
	public void setLogradouro (java.lang.String logradouro) {
//        java.lang.String logradouroOld = this.logradouro;
		this.logradouro = logradouro;
//        this.getPropertyChangeSupport().firePropertyChange ("logradouro", logradouroOld, logradouro);
	}



	/**
	 * Return the value associated with the column: numero
	 */
	public java.lang.String getNumero () {
		return getPropertyValue(this, numero, PROP_NUMERO); 
	}

	/**
	 * Set the value related to the column: numero
	 * @param numero the numero value
	 */
	public void setNumero (java.lang.String numero) {
//        java.lang.String numeroOld = this.numero;
		this.numero = numero;
//        this.getPropertyChangeSupport().firePropertyChange ("numero", numeroOld, numero);
	}



	/**
	 * Return the value associated with the column: complemento
	 */
	public java.lang.String getComplemento () {
		return getPropertyValue(this, complemento, PROP_COMPLEMENTO); 
	}

	/**
	 * Set the value related to the column: complemento
	 * @param complemento the complemento value
	 */
	public void setComplemento (java.lang.String complemento) {
//        java.lang.String complementoOld = this.complemento;
		this.complemento = complemento;
//        this.getPropertyChangeSupport().firePropertyChange ("complemento", complementoOld, complemento);
	}



	/**
	 * Return the value associated with the column: bairro
	 */
	public java.lang.String getBairro () {
		return getPropertyValue(this, bairro, PROP_BAIRRO); 
	}

	/**
	 * Set the value related to the column: bairro
	 * @param bairro the bairro value
	 */
	public void setBairro (java.lang.String bairro) {
//        java.lang.String bairroOld = this.bairro;
		this.bairro = bairro;
//        this.getPropertyChangeSupport().firePropertyChange ("bairro", bairroOld, bairro);
	}



	/**
	 * Return the value associated with the column: cep
	 */
	public java.lang.String getCep () {
		return getPropertyValue(this, cep, PROP_CEP); 
	}

	/**
	 * Set the value related to the column: cep
	 * @param cep the cep value
	 */
	public void setCep (java.lang.String cep) {
//        java.lang.String cepOld = this.cep;
		this.cep = cep;
//        this.getPropertyChangeSupport().firePropertyChange ("cep", cepOld, cep);
	}



	/**
	 * Return the value associated with the column: telefone
	 */
	public java.lang.String getTelefone () {
		return getPropertyValue(this, telefone, PROP_TELEFONE); 
	}

	/**
	 * Set the value related to the column: telefone
	 * @param telefone the telefone value
	 */
	public void setTelefone (java.lang.String telefone) {
//        java.lang.String telefoneOld = this.telefone;
		this.telefone = telefone;
//        this.getPropertyChangeSupport().firePropertyChange ("telefone", telefoneOld, telefone);
	}



	/**
	 * Return the value associated with the column: celular
	 */
	public java.lang.String getCelular () {
		return getPropertyValue(this, celular, PROP_CELULAR); 
	}

	/**
	 * Set the value related to the column: celular
	 * @param celular the celular value
	 */
	public void setCelular (java.lang.String celular) {
//        java.lang.String celularOld = this.celular;
		this.celular = celular;
//        this.getPropertyChangeSupport().firePropertyChange ("celular", celularOld, celular);
	}



	/**
	 * Return the value associated with the column: ativo
	 */
	public java.lang.String getAtivo () {
		return getPropertyValue(this, ativo, PROP_ATIVO); 
	}

	/**
	 * Set the value related to the column: ativo
	 * @param ativo the ativo value
	 */
	public void setAtivo (java.lang.String ativo) {
//        java.lang.String ativoOld = this.ativo;
		this.ativo = ativo;
//        this.getPropertyChangeSupport().firePropertyChange ("ativo", ativoOld, ativo);
	}



	/**
	 * Return the value associated with the column: cod_cid
	 */
	public br.com.ksisolucoes.vo.basico.Cidade getCidade () {
		return getPropertyValue(this, cidade, PROP_CIDADE); 
	}

	/**
	 * Set the value related to the column: cod_cid
	 * @param cidade the cod_cid value
	 */
	public void setCidade (br.com.ksisolucoes.vo.basico.Cidade cidade) {
//        br.com.ksisolucoes.vo.basico.Cidade cidadeOld = this.cidade;
		this.cidade = cidade;
//        this.getPropertyChangeSupport().firePropertyChange ("cidade", cidadeOld, cidade);
	}



	/**
	 * Return the value associated with the column: cd_profissional
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissional () {
		return getPropertyValue(this, profissional, PROP_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_profissional
	 * @param profissional the cd_profissional value
	 */
	public void setProfissional (br.com.ksisolucoes.vo.cadsus.Profissional profissional) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalOld = this.profissional;
		this.profissional = profissional;
//        this.getPropertyChangeSupport().firePropertyChange ("profissional", profissionalOld, profissional);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.frota.Motorista)) return false;
		else {
			br.com.ksisolucoes.vo.frota.Motorista motorista = (br.com.ksisolucoes.vo.frota.Motorista) obj;
			if (null == this.getCodigo() || null == motorista.getCodigo()) return false;
			else return (this.getCodigo().equals(motorista.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}