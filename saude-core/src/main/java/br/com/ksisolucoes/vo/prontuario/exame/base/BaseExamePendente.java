package br.com.ksisolucoes.vo.prontuario.exame.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the exame_pendente table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="exame_pendente"
 */

public abstract class BaseExamePendente extends BaseRootVO implements Serializable {

	public static String REF = "ExamePendente";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_USUARIO = "dataUsuario";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_USUARIO_CADASTRO = "usuarioCadastro";
	public static final String PROP_EXAME_PROCEDIMENTO_DEPENDENTE = "exameProcedimentoDependente";


	// constructors
	public BaseExamePendente () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseExamePendente (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseExamePendente (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
		java.util.Date dataCadastro,
		java.util.Date dataUsuario) {

		this.setCodigo(codigo);
		this.setUsuarioCadastro(usuarioCadastro);
		this.setDataCadastro(dataCadastro);
		this.setDataUsuario(dataUsuario);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataCadastro;
	private java.util.Date dataUsuario;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimentoDependente;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_exame_pendente"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_usuario
	 */
	public java.util.Date getDataUsuario () {
		return getPropertyValue(this, dataUsuario, PROP_DATA_USUARIO); 
	}

	/**
	 * Set the value related to the column: dt_usuario
	 * @param dataUsuario the dt_usuario value
	 */
	public void setDataUsuario (java.util.Date dataUsuario) {
//        java.util.Date dataUsuarioOld = this.dataUsuario;
		this.dataUsuario = dataUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsuario", dataUsuarioOld, dataUsuario);
	}



	/**
	 * Return the value associated with the column: cd_exame_proc_dependente
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento getExameProcedimentoDependente () {
		return getPropertyValue(this, exameProcedimentoDependente, PROP_EXAME_PROCEDIMENTO_DEPENDENTE); 
	}

	/**
	 * Set the value related to the column: cd_exame_proc_dependente
	 * @param exameProcedimentoDependente the cd_exame_proc_dependente value
	 */
	public void setExameProcedimentoDependente (br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimentoDependente) {
//        br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimentoDependenteOld = this.exameProcedimentoDependente;
		this.exameProcedimentoDependente = exameProcedimentoDependente;
//        this.getPropertyChangeSupport().firePropertyChange ("exameProcedimentoDependente", exameProcedimentoDependenteOld, exameProcedimentoDependente);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCadastro () {
		return getPropertyValue(this, usuarioCadastro, PROP_USUARIO_CADASTRO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuarioCadastro the cd_usuario value
	 */
	public void setUsuarioCadastro (br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCadastroOld = this.usuarioCadastro;
		this.usuarioCadastro = usuarioCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadastro", usuarioCadastroOld, usuarioCadastro);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.exame.ExamePendente)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.exame.ExamePendente examePendente = (br.com.ksisolucoes.vo.prontuario.exame.ExamePendente) obj;
			if (null == this.getCodigo() || null == examePendente.getCodigo()) return false;
			else return (this.getCodigo().equals(examePendente.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}