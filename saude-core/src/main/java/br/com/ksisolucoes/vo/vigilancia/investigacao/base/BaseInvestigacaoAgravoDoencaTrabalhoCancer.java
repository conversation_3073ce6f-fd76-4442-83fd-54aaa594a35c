package br.com.ksisolucoes.vo.vigilancia.investigacao.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the investigacao_agr_cancer table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agr_cancer"
 */

public abstract class BaseInvestigacaoAgravoDoencaTrabalhoCancer extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravoDoencaTrabalhoCancer";
	public static final String PROP_DATA_INVESTIGACAO = "dataInvestigacao";
	public static final String PROP_EXPOSICAO_CADMIO_COMPOSTOS = "exposicaoCadmioCompostos";
	public static final String PROP_EXPOSICAO_OLEOS_MINERAIS = "exposicaoOleosMinerais";
	public static final String PROP_EXPOSICAO_COMPOSTOS_NIQUEL = "exposicaoCompostosNiquel";
	public static final String PROP_USUARIO_ENCERRAMENTO = "usuarioEncerramento";
	public static final String PROP_TEMPO_TRABALHO_OCUPACAO_UNIDADE_MEDIDA = "tempoTrabalhoOcupacaoUnidadeMedida";
	public static final String PROP_FLAG_INFORMACOES_COMPLEMENTARES = "flagInformacoesComplementares";
	public static final String PROP_TEMPO_EXPOSICAO_TABACO_UNIDADE_MEDIDA = "tempoExposicaoTabacoUnidadeMedida";
	public static final String PROP_TEMPO_EXPOSICAO_TABACO = "tempoExposicaoTabaco";
	public static final String PROP_EMPRESA_TERCEIRIZADA = "empresaTerceirizada";
	public static final String PROP_DATA_ENCERRAMENTO = "dataEncerramento";
	public static final String PROP_EXPOSICAO_HORMONIOS = "exposicaoHormonios";
	public static final String PROP_DIAGNOSTICO_ESPECIFICO = "diagnosticoEspecifico";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_HABITO_FUMAR = "habitoFumar";
	public static final String PROP_TEMPO_EXPOSICAO_AGENTE_RISCO_UNIDADE_MEDIDA = "tempoExposicaoAgenteRiscoUnidadeMedida";
	public static final String PROP_DATA_OBITO = "dataObito";
	public static final String PROP_EMPRESA_TELEFONE = "empresaTelefone";
	public static final String PROP_EMPRESA_DISTRITO = "empresaDistrito";
	public static final String PROP_EXPOSICAO_ANTINEOPLASTICOS = "exposicaoAntineoplasticos";
	public static final String PROP_EXPOSICAO_BENZENO = "exposicaoBenzeno";
	public static final String PROP_REGIME_TRATAMENTO = "regimeTratamento";
	public static final String PROP_EXPOSICAO_RADIACOES_IONIZANTES = "exposicaoRadiacoesIonizantes";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_EXPOSICAO_RADIACOES_NAO_IONIZANTES = "exposicaoRadiacoesNaoIonizantes";
	public static final String PROP_EXPOSICAO_CROMO_COMPOSTOS = "exposicaoCromoCompostos";
	public static final String PROP_REGISTRO_AGRAVO = "registroAgravo";
	public static final String PROP_EXPOSICAO_ASBESTO = "exposicaoAsbesto";
	public static final String PROP_OCUPACAO_CBO = "ocupacaoCbo";
	public static final String PROP_TEMPO_EXPOSICAO_AGENTE_RISCO = "tempoExposicaoAgenteRisco";
	public static final String PROP_TEMPO_TRABALHO_OCUPACAO = "tempoTrabalhoOcupacao";
	public static final String PROP_EXPOSICAO_SILICA_LIVRE = "exposicaoSilicaLivre";
	public static final String PROP_EXPOSICAO_BERILIO = "exposicaoBerilio";
	public static final String PROP_OUTROS_TRABALHADORES_MESMA_DOENCA = "outrosTrabalhadoresMesmaDoenca";
	public static final String PROP_EXPOSICAO_OUTROS = "exposicaoOutros";
	public static final String PROP_EVOLUCAO_CASO = "evolucaoCaso";
	public static final String PROP_EXPOSICAO_HIDROCARBONETOS = "exposicaoHidrocarbonetos";
	public static final String PROP_SITUACAO_MERCADO_TRABALHO = "situacaoMercadoTrabalho";
	public static final String PROP_EXPOSICAO_ALCATRAO = "exposicaoAlcatrao";
	public static final String PROP_EMPRESA_PONTO_REFERENCIA = "empresaPontoReferencia";
	public static final String PROP_EXPOSICAO_AMINAS_AROMATICAS = "exposicaoAminasAromaticas";
	public static final String PROP_EMITIDA_CAT = "emitidaCat";
	public static final String PROP_EMPRESA_CONTRATANTE = "empresaContratante";


	// constructors
	public BaseInvestigacaoAgravoDoencaTrabalhoCancer () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravoDoencaTrabalhoCancer (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseInvestigacaoAgravoDoencaTrabalhoCancer (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
		java.lang.String flagInformacoesComplementares) {

		this.setCodigo(codigo);
		this.setRegistroAgravo(registroAgravo);
		this.setFlagInformacoesComplementares(flagInformacoesComplementares);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String flagInformacoesComplementares;
	private java.util.Date dataInvestigacao;
	private java.lang.Long situacaoMercadoTrabalho;
	private java.lang.String tempoTrabalhoOcupacao;
	private java.lang.Long tempoTrabalhoOcupacaoUnidadeMedida;
	private java.lang.String empresaDistrito;
	private java.lang.String empresaPontoReferencia;
	private java.lang.String empresaTelefone;
	private java.lang.Long empresaTerceirizada;
	private java.lang.String tempoExposicaoAgenteRisco;
	private java.lang.Long tempoExposicaoAgenteRiscoUnidadeMedida;
	private java.lang.Long regimeTratamento;
	private java.lang.Long exposicaoAsbesto;
	private java.lang.Long exposicaoSilicaLivre;
	private java.lang.Long exposicaoAminasAromaticas;
	private java.lang.Long exposicaoBenzeno;
	private java.lang.Long exposicaoAlcatrao;
	private java.lang.Long exposicaoHidrocarbonetos;
	private java.lang.Long exposicaoOleosMinerais;
	private java.lang.Long exposicaoBerilio;
	private java.lang.Long exposicaoCadmioCompostos;
	private java.lang.Long exposicaoCromoCompostos;
	private java.lang.Long exposicaoCompostosNiquel;
	private java.lang.Long exposicaoRadiacoesIonizantes;
	private java.lang.Long exposicaoRadiacoesNaoIonizantes;
	private java.lang.Long exposicaoHormonios;
	private java.lang.Long exposicaoAntineoplasticos;
	private java.lang.String exposicaoOutros;
	private java.lang.Long habitoFumar;
	private java.lang.String tempoExposicaoTabaco;
	private java.lang.Long tempoExposicaoTabacoUnidadeMedida;
	private java.lang.Long outrosTrabalhadoresMesmaDoenca;
	private java.lang.Long evolucaoCaso;
	private java.util.Date dataObito;
	private java.lang.Long emitidaCat;
	private java.lang.String observacao;
	private java.util.Date dataEncerramento;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo;
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo;
	private br.com.ksisolucoes.vo.basico.Empresa empresaContratante;
	private br.com.ksisolucoes.vo.prontuario.basico.Cid diagnosticoEspecifico;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_invest_agr_cancer"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: flag_informacoes_complementares
	 */
	public java.lang.String getFlagInformacoesComplementares () {
		return getPropertyValue(this, flagInformacoesComplementares, PROP_FLAG_INFORMACOES_COMPLEMENTARES); 
	}

	/**
	 * Set the value related to the column: flag_informacoes_complementares
	 * @param flagInformacoesComplementares the flag_informacoes_complementares value
	 */
	public void setFlagInformacoesComplementares (java.lang.String flagInformacoesComplementares) {
//        java.lang.String flagInformacoesComplementaresOld = this.flagInformacoesComplementares;
		this.flagInformacoesComplementares = flagInformacoesComplementares;
//        this.getPropertyChangeSupport().firePropertyChange ("flagInformacoesComplementares", flagInformacoesComplementaresOld, flagInformacoesComplementares);
	}



	/**
	 * Return the value associated with the column: dt_investigacao
	 */
	public java.util.Date getDataInvestigacao () {
		return getPropertyValue(this, dataInvestigacao, PROP_DATA_INVESTIGACAO); 
	}

	/**
	 * Set the value related to the column: dt_investigacao
	 * @param dataInvestigacao the dt_investigacao value
	 */
	public void setDataInvestigacao (java.util.Date dataInvestigacao) {
//        java.util.Date dataInvestigacaoOld = this.dataInvestigacao;
		this.dataInvestigacao = dataInvestigacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInvestigacao", dataInvestigacaoOld, dataInvestigacao);
	}



	/**
	 * Return the value associated with the column: tp_situacao_mercado_trabalho
	 */
	public java.lang.Long getSituacaoMercadoTrabalho () {
		return getPropertyValue(this, situacaoMercadoTrabalho, PROP_SITUACAO_MERCADO_TRABALHO); 
	}

	/**
	 * Set the value related to the column: tp_situacao_mercado_trabalho
	 * @param situacaoMercadoTrabalho the tp_situacao_mercado_trabalho value
	 */
	public void setSituacaoMercadoTrabalho (java.lang.Long situacaoMercadoTrabalho) {
//        java.lang.Long situacaoMercadoTrabalhoOld = this.situacaoMercadoTrabalho;
		this.situacaoMercadoTrabalho = situacaoMercadoTrabalho;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoMercadoTrabalho", situacaoMercadoTrabalhoOld, situacaoMercadoTrabalho);
	}



	/**
	 * Return the value associated with the column: tempo_trabalho_ocupacao
	 */
	public java.lang.String getTempoTrabalhoOcupacao () {
		return getPropertyValue(this, tempoTrabalhoOcupacao, PROP_TEMPO_TRABALHO_OCUPACAO); 
	}

	/**
	 * Set the value related to the column: tempo_trabalho_ocupacao
	 * @param tempoTrabalhoOcupacao the tempo_trabalho_ocupacao value
	 */
	public void setTempoTrabalhoOcupacao (java.lang.String tempoTrabalhoOcupacao) {
//        java.lang.String tempoTrabalhoOcupacaoOld = this.tempoTrabalhoOcupacao;
		this.tempoTrabalhoOcupacao = tempoTrabalhoOcupacao;
//        this.getPropertyChangeSupport().firePropertyChange ("tempoTrabalhoOcupacao", tempoTrabalhoOcupacaoOld, tempoTrabalhoOcupacao);
	}



	/**
	 * Return the value associated with the column: tempo_trabalho_ocupacao_um
	 */
	public java.lang.Long getTempoTrabalhoOcupacaoUnidadeMedida () {
		return getPropertyValue(this, tempoTrabalhoOcupacaoUnidadeMedida, PROP_TEMPO_TRABALHO_OCUPACAO_UNIDADE_MEDIDA); 
	}

	/**
	 * Set the value related to the column: tempo_trabalho_ocupacao_um
	 * @param tempoTrabalhoOcupacaoUnidadeMedida the tempo_trabalho_ocupacao_um value
	 */
	public void setTempoTrabalhoOcupacaoUnidadeMedida (java.lang.Long tempoTrabalhoOcupacaoUnidadeMedida) {
//        java.lang.Long tempoTrabalhoOcupacaoUnidadeMedidaOld = this.tempoTrabalhoOcupacaoUnidadeMedida;
		this.tempoTrabalhoOcupacaoUnidadeMedida = tempoTrabalhoOcupacaoUnidadeMedida;
//        this.getPropertyChangeSupport().firePropertyChange ("tempoTrabalhoOcupacaoUnidadeMedida", tempoTrabalhoOcupacaoUnidadeMedidaOld, tempoTrabalhoOcupacaoUnidadeMedida);
	}



	/**
	 * Return the value associated with the column: empresa_distrito
	 */
	public java.lang.String getEmpresaDistrito () {
		return getPropertyValue(this, empresaDistrito, PROP_EMPRESA_DISTRITO); 
	}

	/**
	 * Set the value related to the column: empresa_distrito
	 * @param empresaDistrito the empresa_distrito value
	 */
	public void setEmpresaDistrito (java.lang.String empresaDistrito) {
//        java.lang.String empresaDistritoOld = this.empresaDistrito;
		this.empresaDistrito = empresaDistrito;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaDistrito", empresaDistritoOld, empresaDistrito);
	}



	/**
	 * Return the value associated with the column: empresa_ponto_referencia
	 */
	public java.lang.String getEmpresaPontoReferencia () {
		return getPropertyValue(this, empresaPontoReferencia, PROP_EMPRESA_PONTO_REFERENCIA); 
	}

	/**
	 * Set the value related to the column: empresa_ponto_referencia
	 * @param empresaPontoReferencia the empresa_ponto_referencia value
	 */
	public void setEmpresaPontoReferencia (java.lang.String empresaPontoReferencia) {
//        java.lang.String empresaPontoReferenciaOld = this.empresaPontoReferencia;
		this.empresaPontoReferencia = empresaPontoReferencia;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaPontoReferencia", empresaPontoReferenciaOld, empresaPontoReferencia);
	}



	/**
	 * Return the value associated with the column: empresa_telefone
	 */
	public java.lang.String getEmpresaTelefone () {
		return getPropertyValue(this, empresaTelefone, PROP_EMPRESA_TELEFONE); 
	}

	/**
	 * Set the value related to the column: empresa_telefone
	 * @param empresaTelefone the empresa_telefone value
	 */
	public void setEmpresaTelefone (java.lang.String empresaTelefone) {
//        java.lang.String empresaTelefoneOld = this.empresaTelefone;
		this.empresaTelefone = empresaTelefone;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaTelefone", empresaTelefoneOld, empresaTelefone);
	}



	/**
	 * Return the value associated with the column: sn_empresa_terceirizada
	 */
	public java.lang.Long getEmpresaTerceirizada () {
		return getPropertyValue(this, empresaTerceirizada, PROP_EMPRESA_TERCEIRIZADA); 
	}

	/**
	 * Set the value related to the column: sn_empresa_terceirizada
	 * @param empresaTerceirizada the sn_empresa_terceirizada value
	 */
	public void setEmpresaTerceirizada (java.lang.Long empresaTerceirizada) {
//        java.lang.Long empresaTerceirizadaOld = this.empresaTerceirizada;
		this.empresaTerceirizada = empresaTerceirizada;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaTerceirizada", empresaTerceirizadaOld, empresaTerceirizada);
	}



	/**
	 * Return the value associated with the column: tempo_exposicao_agente_risco
	 */
	public java.lang.String getTempoExposicaoAgenteRisco () {
		return getPropertyValue(this, tempoExposicaoAgenteRisco, PROP_TEMPO_EXPOSICAO_AGENTE_RISCO); 
	}

	/**
	 * Set the value related to the column: tempo_exposicao_agente_risco
	 * @param tempoExposicaoAgenteRisco the tempo_exposicao_agente_risco value
	 */
	public void setTempoExposicaoAgenteRisco (java.lang.String tempoExposicaoAgenteRisco) {
//        java.lang.String tempoExposicaoAgenteRiscoOld = this.tempoExposicaoAgenteRisco;
		this.tempoExposicaoAgenteRisco = tempoExposicaoAgenteRisco;
//        this.getPropertyChangeSupport().firePropertyChange ("tempoExposicaoAgenteRisco", tempoExposicaoAgenteRiscoOld, tempoExposicaoAgenteRisco);
	}



	/**
	 * Return the value associated with the column: tempo_exposicao_agente_risco_um
	 */
	public java.lang.Long getTempoExposicaoAgenteRiscoUnidadeMedida () {
		return getPropertyValue(this, tempoExposicaoAgenteRiscoUnidadeMedida, PROP_TEMPO_EXPOSICAO_AGENTE_RISCO_UNIDADE_MEDIDA); 
	}

	/**
	 * Set the value related to the column: tempo_exposicao_agente_risco_um
	 * @param tempoExposicaoAgenteRiscoUnidadeMedida the tempo_exposicao_agente_risco_um value
	 */
	public void setTempoExposicaoAgenteRiscoUnidadeMedida (java.lang.Long tempoExposicaoAgenteRiscoUnidadeMedida) {
//        java.lang.Long tempoExposicaoAgenteRiscoUnidadeMedidaOld = this.tempoExposicaoAgenteRiscoUnidadeMedida;
		this.tempoExposicaoAgenteRiscoUnidadeMedida = tempoExposicaoAgenteRiscoUnidadeMedida;
//        this.getPropertyChangeSupport().firePropertyChange ("tempoExposicaoAgenteRiscoUnidadeMedida", tempoExposicaoAgenteRiscoUnidadeMedidaOld, tempoExposicaoAgenteRiscoUnidadeMedida);
	}



	/**
	 * Return the value associated with the column: tp_regime_tratamento
	 */
	public java.lang.Long getRegimeTratamento () {
		return getPropertyValue(this, regimeTratamento, PROP_REGIME_TRATAMENTO); 
	}

	/**
	 * Set the value related to the column: tp_regime_tratamento
	 * @param regimeTratamento the tp_regime_tratamento value
	 */
	public void setRegimeTratamento (java.lang.Long regimeTratamento) {
//        java.lang.Long regimeTratamentoOld = this.regimeTratamento;
		this.regimeTratamento = regimeTratamento;
//        this.getPropertyChangeSupport().firePropertyChange ("regimeTratamento", regimeTratamentoOld, regimeTratamento);
	}



	/**
	 * Return the value associated with the column: exposicao_asbesto
	 */
	public java.lang.Long getExposicaoAsbesto () {
		return getPropertyValue(this, exposicaoAsbesto, PROP_EXPOSICAO_ASBESTO); 
	}

	/**
	 * Set the value related to the column: exposicao_asbesto
	 * @param exposicaoAsbesto the exposicao_asbesto value
	 */
	public void setExposicaoAsbesto (java.lang.Long exposicaoAsbesto) {
//        java.lang.Long exposicaoAsbestoOld = this.exposicaoAsbesto;
		this.exposicaoAsbesto = exposicaoAsbesto;
//        this.getPropertyChangeSupport().firePropertyChange ("exposicaoAsbesto", exposicaoAsbestoOld, exposicaoAsbesto);
	}



	/**
	 * Return the value associated with the column: exposicao_silica_livre
	 */
	public java.lang.Long getExposicaoSilicaLivre () {
		return getPropertyValue(this, exposicaoSilicaLivre, PROP_EXPOSICAO_SILICA_LIVRE); 
	}

	/**
	 * Set the value related to the column: exposicao_silica_livre
	 * @param exposicaoSilicaLivre the exposicao_silica_livre value
	 */
	public void setExposicaoSilicaLivre (java.lang.Long exposicaoSilicaLivre) {
//        java.lang.Long exposicaoSilicaLivreOld = this.exposicaoSilicaLivre;
		this.exposicaoSilicaLivre = exposicaoSilicaLivre;
//        this.getPropertyChangeSupport().firePropertyChange ("exposicaoSilicaLivre", exposicaoSilicaLivreOld, exposicaoSilicaLivre);
	}



	/**
	 * Return the value associated with the column: exposicao_aminas_aromaticas
	 */
	public java.lang.Long getExposicaoAminasAromaticas () {
		return getPropertyValue(this, exposicaoAminasAromaticas, PROP_EXPOSICAO_AMINAS_AROMATICAS); 
	}

	/**
	 * Set the value related to the column: exposicao_aminas_aromaticas
	 * @param exposicaoAminasAromaticas the exposicao_aminas_aromaticas value
	 */
	public void setExposicaoAminasAromaticas (java.lang.Long exposicaoAminasAromaticas) {
//        java.lang.Long exposicaoAminasAromaticasOld = this.exposicaoAminasAromaticas;
		this.exposicaoAminasAromaticas = exposicaoAminasAromaticas;
//        this.getPropertyChangeSupport().firePropertyChange ("exposicaoAminasAromaticas", exposicaoAminasAromaticasOld, exposicaoAminasAromaticas);
	}



	/**
	 * Return the value associated with the column: exposicao_benzeno
	 */
	public java.lang.Long getExposicaoBenzeno () {
		return getPropertyValue(this, exposicaoBenzeno, PROP_EXPOSICAO_BENZENO); 
	}

	/**
	 * Set the value related to the column: exposicao_benzeno
	 * @param exposicaoBenzeno the exposicao_benzeno value
	 */
	public void setExposicaoBenzeno (java.lang.Long exposicaoBenzeno) {
//        java.lang.Long exposicaoBenzenoOld = this.exposicaoBenzeno;
		this.exposicaoBenzeno = exposicaoBenzeno;
//        this.getPropertyChangeSupport().firePropertyChange ("exposicaoBenzeno", exposicaoBenzenoOld, exposicaoBenzeno);
	}



	/**
	 * Return the value associated with the column: exposicao_alcatrao
	 */
	public java.lang.Long getExposicaoAlcatrao () {
		return getPropertyValue(this, exposicaoAlcatrao, PROP_EXPOSICAO_ALCATRAO); 
	}

	/**
	 * Set the value related to the column: exposicao_alcatrao
	 * @param exposicaoAlcatrao the exposicao_alcatrao value
	 */
	public void setExposicaoAlcatrao (java.lang.Long exposicaoAlcatrao) {
//        java.lang.Long exposicaoAlcatraoOld = this.exposicaoAlcatrao;
		this.exposicaoAlcatrao = exposicaoAlcatrao;
//        this.getPropertyChangeSupport().firePropertyChange ("exposicaoAlcatrao", exposicaoAlcatraoOld, exposicaoAlcatrao);
	}



	/**
	 * Return the value associated with the column: exposicao_hidrocarbonetos
	 */
	public java.lang.Long getExposicaoHidrocarbonetos () {
		return getPropertyValue(this, exposicaoHidrocarbonetos, PROP_EXPOSICAO_HIDROCARBONETOS); 
	}

	/**
	 * Set the value related to the column: exposicao_hidrocarbonetos
	 * @param exposicaoHidrocarbonetos the exposicao_hidrocarbonetos value
	 */
	public void setExposicaoHidrocarbonetos (java.lang.Long exposicaoHidrocarbonetos) {
//        java.lang.Long exposicaoHidrocarbonetosOld = this.exposicaoHidrocarbonetos;
		this.exposicaoHidrocarbonetos = exposicaoHidrocarbonetos;
//        this.getPropertyChangeSupport().firePropertyChange ("exposicaoHidrocarbonetos", exposicaoHidrocarbonetosOld, exposicaoHidrocarbonetos);
	}



	/**
	 * Return the value associated with the column: exposicao_oleos_minerais
	 */
	public java.lang.Long getExposicaoOleosMinerais () {
		return getPropertyValue(this, exposicaoOleosMinerais, PROP_EXPOSICAO_OLEOS_MINERAIS); 
	}

	/**
	 * Set the value related to the column: exposicao_oleos_minerais
	 * @param exposicaoOleosMinerais the exposicao_oleos_minerais value
	 */
	public void setExposicaoOleosMinerais (java.lang.Long exposicaoOleosMinerais) {
//        java.lang.Long exposicaoOleosMineraisOld = this.exposicaoOleosMinerais;
		this.exposicaoOleosMinerais = exposicaoOleosMinerais;
//        this.getPropertyChangeSupport().firePropertyChange ("exposicaoOleosMinerais", exposicaoOleosMineraisOld, exposicaoOleosMinerais);
	}



	/**
	 * Return the value associated with the column: exposicao_berilio
	 */
	public java.lang.Long getExposicaoBerilio () {
		return getPropertyValue(this, exposicaoBerilio, PROP_EXPOSICAO_BERILIO); 
	}

	/**
	 * Set the value related to the column: exposicao_berilio
	 * @param exposicaoBerilio the exposicao_berilio value
	 */
	public void setExposicaoBerilio (java.lang.Long exposicaoBerilio) {
//        java.lang.Long exposicaoBerilioOld = this.exposicaoBerilio;
		this.exposicaoBerilio = exposicaoBerilio;
//        this.getPropertyChangeSupport().firePropertyChange ("exposicaoBerilio", exposicaoBerilioOld, exposicaoBerilio);
	}



	/**
	 * Return the value associated with the column: exposicao_cadmio_compostos
	 */
	public java.lang.Long getExposicaoCadmioCompostos () {
		return getPropertyValue(this, exposicaoCadmioCompostos, PROP_EXPOSICAO_CADMIO_COMPOSTOS); 
	}

	/**
	 * Set the value related to the column: exposicao_cadmio_compostos
	 * @param exposicaoCadmioCompostos the exposicao_cadmio_compostos value
	 */
	public void setExposicaoCadmioCompostos (java.lang.Long exposicaoCadmioCompostos) {
//        java.lang.Long exposicaoCadmioCompostosOld = this.exposicaoCadmioCompostos;
		this.exposicaoCadmioCompostos = exposicaoCadmioCompostos;
//        this.getPropertyChangeSupport().firePropertyChange ("exposicaoCadmioCompostos", exposicaoCadmioCompostosOld, exposicaoCadmioCompostos);
	}



	/**
	 * Return the value associated with the column: exposicao_cromo_compostos
	 */
	public java.lang.Long getExposicaoCromoCompostos () {
		return getPropertyValue(this, exposicaoCromoCompostos, PROP_EXPOSICAO_CROMO_COMPOSTOS); 
	}

	/**
	 * Set the value related to the column: exposicao_cromo_compostos
	 * @param exposicaoCromoCompostos the exposicao_cromo_compostos value
	 */
	public void setExposicaoCromoCompostos (java.lang.Long exposicaoCromoCompostos) {
//        java.lang.Long exposicaoCromoCompostosOld = this.exposicaoCromoCompostos;
		this.exposicaoCromoCompostos = exposicaoCromoCompostos;
//        this.getPropertyChangeSupport().firePropertyChange ("exposicaoCromoCompostos", exposicaoCromoCompostosOld, exposicaoCromoCompostos);
	}



	/**
	 * Return the value associated with the column: exposicao_compostos_niquel
	 */
	public java.lang.Long getExposicaoCompostosNiquel () {
		return getPropertyValue(this, exposicaoCompostosNiquel, PROP_EXPOSICAO_COMPOSTOS_NIQUEL); 
	}

	/**
	 * Set the value related to the column: exposicao_compostos_niquel
	 * @param exposicaoCompostosNiquel the exposicao_compostos_niquel value
	 */
	public void setExposicaoCompostosNiquel (java.lang.Long exposicaoCompostosNiquel) {
//        java.lang.Long exposicaoCompostosNiquelOld = this.exposicaoCompostosNiquel;
		this.exposicaoCompostosNiquel = exposicaoCompostosNiquel;
//        this.getPropertyChangeSupport().firePropertyChange ("exposicaoCompostosNiquel", exposicaoCompostosNiquelOld, exposicaoCompostosNiquel);
	}



	/**
	 * Return the value associated with the column: exposicao_radiacoes_ionizantes
	 */
	public java.lang.Long getExposicaoRadiacoesIonizantes () {
		return getPropertyValue(this, exposicaoRadiacoesIonizantes, PROP_EXPOSICAO_RADIACOES_IONIZANTES); 
	}

	/**
	 * Set the value related to the column: exposicao_radiacoes_ionizantes
	 * @param exposicaoRadiacoesIonizantes the exposicao_radiacoes_ionizantes value
	 */
	public void setExposicaoRadiacoesIonizantes (java.lang.Long exposicaoRadiacoesIonizantes) {
//        java.lang.Long exposicaoRadiacoesIonizantesOld = this.exposicaoRadiacoesIonizantes;
		this.exposicaoRadiacoesIonizantes = exposicaoRadiacoesIonizantes;
//        this.getPropertyChangeSupport().firePropertyChange ("exposicaoRadiacoesIonizantes", exposicaoRadiacoesIonizantesOld, exposicaoRadiacoesIonizantes);
	}



	/**
	 * Return the value associated with the column: exposicao_radiacoes_nao_ionizantes
	 */
	public java.lang.Long getExposicaoRadiacoesNaoIonizantes () {
		return getPropertyValue(this, exposicaoRadiacoesNaoIonizantes, PROP_EXPOSICAO_RADIACOES_NAO_IONIZANTES); 
	}

	/**
	 * Set the value related to the column: exposicao_radiacoes_nao_ionizantes
	 * @param exposicaoRadiacoesNaoIonizantes the exposicao_radiacoes_nao_ionizantes value
	 */
	public void setExposicaoRadiacoesNaoIonizantes (java.lang.Long exposicaoRadiacoesNaoIonizantes) {
//        java.lang.Long exposicaoRadiacoesNaoIonizantesOld = this.exposicaoRadiacoesNaoIonizantes;
		this.exposicaoRadiacoesNaoIonizantes = exposicaoRadiacoesNaoIonizantes;
//        this.getPropertyChangeSupport().firePropertyChange ("exposicaoRadiacoesNaoIonizantes", exposicaoRadiacoesNaoIonizantesOld, exposicaoRadiacoesNaoIonizantes);
	}



	/**
	 * Return the value associated with the column: exposicao_hormonios
	 */
	public java.lang.Long getExposicaoHormonios () {
		return getPropertyValue(this, exposicaoHormonios, PROP_EXPOSICAO_HORMONIOS); 
	}

	/**
	 * Set the value related to the column: exposicao_hormonios
	 * @param exposicaoHormonios the exposicao_hormonios value
	 */
	public void setExposicaoHormonios (java.lang.Long exposicaoHormonios) {
//        java.lang.Long exposicaoHormoniosOld = this.exposicaoHormonios;
		this.exposicaoHormonios = exposicaoHormonios;
//        this.getPropertyChangeSupport().firePropertyChange ("exposicaoHormonios", exposicaoHormoniosOld, exposicaoHormonios);
	}



	/**
	 * Return the value associated with the column: exposicao_antineoplasticos
	 */
	public java.lang.Long getExposicaoAntineoplasticos () {
		return getPropertyValue(this, exposicaoAntineoplasticos, PROP_EXPOSICAO_ANTINEOPLASTICOS); 
	}

	/**
	 * Set the value related to the column: exposicao_antineoplasticos
	 * @param exposicaoAntineoplasticos the exposicao_antineoplasticos value
	 */
	public void setExposicaoAntineoplasticos (java.lang.Long exposicaoAntineoplasticos) {
//        java.lang.Long exposicaoAntineoplasticosOld = this.exposicaoAntineoplasticos;
		this.exposicaoAntineoplasticos = exposicaoAntineoplasticos;
//        this.getPropertyChangeSupport().firePropertyChange ("exposicaoAntineoplasticos", exposicaoAntineoplasticosOld, exposicaoAntineoplasticos);
	}



	/**
	 * Return the value associated with the column: exposicao_outros
	 */
	public java.lang.String getExposicaoOutros () {
		return getPropertyValue(this, exposicaoOutros, PROP_EXPOSICAO_OUTROS); 
	}

	/**
	 * Set the value related to the column: exposicao_outros
	 * @param exposicaoOutros the exposicao_outros value
	 */
	public void setExposicaoOutros (java.lang.String exposicaoOutros) {
//        java.lang.String exposicaoOutrosOld = this.exposicaoOutros;
		this.exposicaoOutros = exposicaoOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("exposicaoOutros", exposicaoOutrosOld, exposicaoOutros);
	}



	/**
	 * Return the value associated with the column: habito_fumar
	 */
	public java.lang.Long getHabitoFumar () {
		return getPropertyValue(this, habitoFumar, PROP_HABITO_FUMAR); 
	}

	/**
	 * Set the value related to the column: habito_fumar
	 * @param habitoFumar the habito_fumar value
	 */
	public void setHabitoFumar (java.lang.Long habitoFumar) {
//        java.lang.Long habitoFumarOld = this.habitoFumar;
		this.habitoFumar = habitoFumar;
//        this.getPropertyChangeSupport().firePropertyChange ("habitoFumar", habitoFumarOld, habitoFumar);
	}



	/**
	 * Return the value associated with the column: tempo_exposicao_tabaco
	 */
	public java.lang.String getTempoExposicaoTabaco () {
		return getPropertyValue(this, tempoExposicaoTabaco, PROP_TEMPO_EXPOSICAO_TABACO); 
	}

	/**
	 * Set the value related to the column: tempo_exposicao_tabaco
	 * @param tempoExposicaoTabaco the tempo_exposicao_tabaco value
	 */
	public void setTempoExposicaoTabaco (java.lang.String tempoExposicaoTabaco) {
//        java.lang.String tempoExposicaoTabacoOld = this.tempoExposicaoTabaco;
		this.tempoExposicaoTabaco = tempoExposicaoTabaco;
//        this.getPropertyChangeSupport().firePropertyChange ("tempoExposicaoTabaco", tempoExposicaoTabacoOld, tempoExposicaoTabaco);
	}



	/**
	 * Return the value associated with the column: tempo_exposicao_tabaco_um
	 */
	public java.lang.Long getTempoExposicaoTabacoUnidadeMedida () {
		return getPropertyValue(this, tempoExposicaoTabacoUnidadeMedida, PROP_TEMPO_EXPOSICAO_TABACO_UNIDADE_MEDIDA); 
	}

	/**
	 * Set the value related to the column: tempo_exposicao_tabaco_um
	 * @param tempoExposicaoTabacoUnidadeMedida the tempo_exposicao_tabaco_um value
	 */
	public void setTempoExposicaoTabacoUnidadeMedida (java.lang.Long tempoExposicaoTabacoUnidadeMedida) {
//        java.lang.Long tempoExposicaoTabacoUnidadeMedidaOld = this.tempoExposicaoTabacoUnidadeMedida;
		this.tempoExposicaoTabacoUnidadeMedida = tempoExposicaoTabacoUnidadeMedida;
//        this.getPropertyChangeSupport().firePropertyChange ("tempoExposicaoTabacoUnidadeMedida", tempoExposicaoTabacoUnidadeMedidaOld, tempoExposicaoTabacoUnidadeMedida);
	}



	/**
	 * Return the value associated with the column: outros_trabalhadores_mesma_doenca
	 */
	public java.lang.Long getOutrosTrabalhadoresMesmaDoenca () {
		return getPropertyValue(this, outrosTrabalhadoresMesmaDoenca, PROP_OUTROS_TRABALHADORES_MESMA_DOENCA); 
	}

	/**
	 * Set the value related to the column: outros_trabalhadores_mesma_doenca
	 * @param outrosTrabalhadoresMesmaDoenca the outros_trabalhadores_mesma_doenca value
	 */
	public void setOutrosTrabalhadoresMesmaDoenca (java.lang.Long outrosTrabalhadoresMesmaDoenca) {
//        java.lang.Long outrosTrabalhadoresMesmaDoencaOld = this.outrosTrabalhadoresMesmaDoenca;
		this.outrosTrabalhadoresMesmaDoenca = outrosTrabalhadoresMesmaDoenca;
//        this.getPropertyChangeSupport().firePropertyChange ("outrosTrabalhadoresMesmaDoenca", outrosTrabalhadoresMesmaDoencaOld, outrosTrabalhadoresMesmaDoenca);
	}



	/**
	 * Return the value associated with the column: evolucao_caso
	 */
	public java.lang.Long getEvolucaoCaso () {
		return getPropertyValue(this, evolucaoCaso, PROP_EVOLUCAO_CASO); 
	}

	/**
	 * Set the value related to the column: evolucao_caso
	 * @param evolucaoCaso the evolucao_caso value
	 */
	public void setEvolucaoCaso (java.lang.Long evolucaoCaso) {
//        java.lang.Long evolucaoCasoOld = this.evolucaoCaso;
		this.evolucaoCaso = evolucaoCaso;
//        this.getPropertyChangeSupport().firePropertyChange ("evolucaoCaso", evolucaoCasoOld, evolucaoCaso);
	}



	/**
	 * Return the value associated with the column: dt_obito
	 */
	public java.util.Date getDataObito () {
		return getPropertyValue(this, dataObito, PROP_DATA_OBITO); 
	}

	/**
	 * Set the value related to the column: dt_obito
	 * @param dataObito the dt_obito value
	 */
	public void setDataObito (java.util.Date dataObito) {
//        java.util.Date dataObitoOld = this.dataObito;
		this.dataObito = dataObito;
//        this.getPropertyChangeSupport().firePropertyChange ("dataObito", dataObitoOld, dataObito);
	}



	/**
	 * Return the value associated with the column: emitida_cat
	 */
	public java.lang.Long getEmitidaCat () {
		return getPropertyValue(this, emitidaCat, PROP_EMITIDA_CAT); 
	}

	/**
	 * Set the value related to the column: emitida_cat
	 * @param emitidaCat the emitida_cat value
	 */
	public void setEmitidaCat (java.lang.Long emitidaCat) {
//        java.lang.Long emitidaCatOld = this.emitidaCat;
		this.emitidaCat = emitidaCat;
//        this.getPropertyChangeSupport().firePropertyChange ("emitidaCat", emitidaCatOld, emitidaCat);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: dt_encerramento
	 */
	public java.util.Date getDataEncerramento () {
		return getPropertyValue(this, dataEncerramento, PROP_DATA_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_encerramento
	 * @param dataEncerramento the dt_encerramento value
	 */
	public void setDataEncerramento (java.util.Date dataEncerramento) {
//        java.util.Date dataEncerramentoOld = this.dataEncerramento;
		this.dataEncerramento = dataEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEncerramento", dataEncerramentoOld, dataEncerramento);
	}



	/**
	 * Return the value associated with the column: cd_registro_agravo
	 */
	public br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo getRegistroAgravo () {
		return getPropertyValue(this, registroAgravo, PROP_REGISTRO_AGRAVO); 
	}

	/**
	 * Set the value related to the column: cd_registro_agravo
	 * @param registroAgravo the cd_registro_agravo value
	 */
	public void setRegistroAgravo (br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo) {
//        br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravoOld = this.registroAgravo;
		this.registroAgravo = registroAgravo;
//        this.getPropertyChangeSupport().firePropertyChange ("registroAgravo", registroAgravoOld, registroAgravo);
	}



	/**
	 * Return the value associated with the column: ocupacao_cbo
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getOcupacaoCbo () {
		return getPropertyValue(this, ocupacaoCbo, PROP_OCUPACAO_CBO); 
	}

	/**
	 * Set the value related to the column: ocupacao_cbo
	 * @param ocupacaoCbo the ocupacao_cbo value
	 */
	public void setOcupacaoCbo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCboOld = this.ocupacaoCbo;
		this.ocupacaoCbo = ocupacaoCbo;
//        this.getPropertyChangeSupport().firePropertyChange ("ocupacaoCbo", ocupacaoCboOld, ocupacaoCbo);
	}



	/**
	 * Return the value associated with the column: cd_empresa_contratante
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresaContratante () {
		return getPropertyValue(this, empresaContratante, PROP_EMPRESA_CONTRATANTE); 
	}

	/**
	 * Set the value related to the column: cd_empresa_contratante
	 * @param empresaContratante the cd_empresa_contratante value
	 */
	public void setEmpresaContratante (br.com.ksisolucoes.vo.basico.Empresa empresaContratante) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaContratanteOld = this.empresaContratante;
		this.empresaContratante = empresaContratante;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaContratante", empresaContratanteOld, empresaContratante);
	}



	/**
	 * Return the value associated with the column: diagnostico_especifico
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Cid getDiagnosticoEspecifico () {
		return getPropertyValue(this, diagnosticoEspecifico, PROP_DIAGNOSTICO_ESPECIFICO); 
	}

	/**
	 * Set the value related to the column: diagnostico_especifico
	 * @param diagnosticoEspecifico the diagnostico_especifico value
	 */
	public void setDiagnosticoEspecifico (br.com.ksisolucoes.vo.prontuario.basico.Cid diagnosticoEspecifico) {
//        br.com.ksisolucoes.vo.prontuario.basico.Cid diagnosticoEspecificoOld = this.diagnosticoEspecifico;
		this.diagnosticoEspecifico = diagnosticoEspecifico;
//        this.getPropertyChangeSupport().firePropertyChange ("diagnosticoEspecifico", diagnosticoEspecificoOld, diagnosticoEspecifico);
	}



	/**
	 * Return the value associated with the column: cd_usuario_encerramento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioEncerramento () {
		return getPropertyValue(this, usuarioEncerramento, PROP_USUARIO_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_encerramento
	 * @param usuarioEncerramento the cd_usuario_encerramento value
	 */
	public void setUsuarioEncerramento (br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramentoOld = this.usuarioEncerramento;
		this.usuarioEncerramento = usuarioEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioEncerramento", usuarioEncerramentoOld, usuarioEncerramento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaTrabalhoCancer)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaTrabalhoCancer investigacaoAgravoDoencaTrabalhoCancer = (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaTrabalhoCancer) obj;
			if (null == this.getCodigo() || null == investigacaoAgravoDoencaTrabalhoCancer.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravoDoencaTrabalhoCancer.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}