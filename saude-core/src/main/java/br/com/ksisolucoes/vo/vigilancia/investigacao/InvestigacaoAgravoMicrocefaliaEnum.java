package br.com.ksisolucoes.vo.vigilancia.investigacao;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;


public class InvestigacaoAgravoMicrocefaliaEnum {

	public static InvestigacaoAgravoMicrocefalia buscaPorRegistroAgravo(RegistroAgravo ra) {
		return
				LoadManager.getInstance(InvestigacaoAgravoMicrocefalia.class)
						.addProperties(new HQLProperties(InvestigacaoAgravoMicrocefalia.class).getProperties())
						.addProperties(new HQLProperties(
								RegistroAgravo.class,
								VOUtils.montarPath(InvestigacaoAgravoMicrocefalia.PROP_REGISTRO_AGRAVO)).getProperties())
						.addParameter(new QueryCustom.QueryCustomParameter(
								VOUtils.montarPath(InvestigacaoAgravoMicrocefalia.PROP_REGISTRO_AGRAVO, RegistroAgravo.PROP_CODIGO),
								ra.getCodigo()))
						.start().getVO();
	}

	public enum TipoNotificacao implements IEnum {
		CRIANCA(1L, Bundle.getStringApplication("crianca")),
		RECEM_NASCIDO(2L, Bundle.getStringApplication("recem_nascido")),
		OBITO_FETAL_NATIMORTO(3L, Bundle.getStringApplication("obito_fetal_natimorto")),
		ABORTO_ESPONTANEO(5L, Bundle.getStringApplication("aborto_espontaneo")),
		FETO_SUSPEITO(4L, Bundle.getStringApplication("feto_suspeito")),
		FETO_RISCO(6L, Bundle.getStringApplication("feto_risco"))
		;

		private Long value;
		private String descricao;

		TipoNotificacao(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static TipoNotificacao valueOf(Long value) {
			for (TipoNotificacao val : TipoNotificacao.values()) {
				if (val.value().equals(value)) {
					return val;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}
	}
	
	public enum Sexo implements IEnum {
		FEMININO(1L, Bundle.getStringApplication("rotulo_feminino")),
		MASCULINO(2L, Bundle.getStringApplication("rotulo_masculino")),
		INDETERMINADO(3L, Bundle.getStringApplication("rotulo_indeterminado")),
		IGNORADO(4L, Bundle.getStringApplication("ignorado"))
		;

		private Long value;
		private String descricao;

		Sexo(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static Sexo valueOf(Long value) {
			for (Sexo val : Sexo.values()) {
				if (val.value().equals(value)) {
					return val;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}
	}

	public enum TipoAlteracaoCongenita implements IEnum {
		MICROCEFALIA_APENAS(1L, Bundle.getStringApplication("microcefaliaApenas")),
		MICROCEFALIA_ALTERACAO_SNC(2L, Bundle.getStringApplication("microcefaliaAlteracaoSNC")),
		MICROCEFALIA_OUTRAS_ALTERACOES_CONGENITAS(3L, Bundle.getStringApplication("microcefaliaAlteracoesCongenitas")),
		ALTERACAO_CONGENITA_SEM_MICROCEFALIA(4L, Bundle.getStringApplication("alteracoesCongenitasSemMicrocefalia")),
		DEFICIENCIA_NEUROLOGICA(5L, Bundle.getStringApplication("deficienciaNeurologica")),
		DEFICIENCIA_AUDITIVA(6L, Bundle.getStringApplication("deficienciaAuditiva")),
		DEFICIENCIA_VISUAL(7L, Bundle.getStringApplication("deficienciaVisual")),
		IGNORADO(8L, Bundle.getStringApplication("ignorado")),
		;

		private Long value;
		private String descricao;

		TipoAlteracaoCongenita(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static TipoAlteracaoCongenita valueOf(Long value) {
			for (TipoAlteracaoCongenita val : TipoAlteracaoCongenita.values()) {
				if (val.value().equals(value)) {
					return val;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}
	}

	public enum DeteccaoAlteracaoCongenita implements IEnum {
		INTRAUTERINO(1L, Bundle.getStringApplication("intrauterino")),
		POS_PARTO(2L, Bundle.getStringApplication("posParto")),
		NAO_DETECTADA_MICROCEFALIA(3L, Bundle.getStringApplication("naoDetectadaMicrocefalia")),
		IGNORADO(4L, Bundle.getStringApplication("ignorado"))
		;

		private Long value;
		private String descricao;

		DeteccaoAlteracaoCongenita(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static DeteccaoAlteracaoCongenita valueOf(Long value) {
			for (DeteccaoAlteracaoCongenita val : DeteccaoAlteracaoCongenita.values()) {
				if (val.value().equals(value)) {
					return val;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}
	}

	public enum TipoGravidez implements IEnum {
		UNICA(1L, Bundle.getStringApplication("unica")),
		DUPLA(2L, Bundle.getStringApplication("dupla")),
		TRIPLA_MAIS(3L, Bundle.getStringApplication("triplaMais")),
		IGNORADO(4L, Bundle.getStringApplication("ignorado"))
		;

		private Long value;
		private String descricao;

		TipoGravidez(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static TipoGravidez valueOf(Long value) {
			for (TipoGravidez val : TipoGravidez.values()) {
				if (val.value().equals(value)) {
					return val;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}
	}

	public enum ClassificacaoNascidoVivoNatimorto implements IEnum {
		PRE_TERMO(1L, Bundle.getStringApplication("preTermo")),
		A_TERMO(2L, Bundle.getStringApplication("aTermo")),
		POS_TERMO(3L, Bundle.getStringApplication("posTermo")),
		NAO_SE_APLICA(4L, Bundle.getStringApplication("naoSeAplicaGestante"))
		;

		private Long value;
		private String descricao;

		ClassificacaoNascidoVivoNatimorto(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static ClassificacaoNascidoVivoNatimorto valueOf(Long value) {
			for (ClassificacaoNascidoVivoNatimorto val : ClassificacaoNascidoVivoNatimorto.values()) {
				if (val.value().equals(value)) {
					return val;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}
	}

	public enum SimNao implements IEnum {
		SIM(1L, Bundle.getStringApplication("rotulo_sim")),
		NAO(2L, Bundle.getStringApplication("rotulo_nao")),
		NAO_SABE(3L, Bundle.getStringApplication("naoSabe")),
		IGNORADO(4L, Bundle.getStringApplication("ignorado"))
		;

		private Long value;
		private String descricao;

		SimNao(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static SimNao valueOf(Long value) {
			for (SimNao val : SimNao.values()) {
				if (val.value().equals(value)) {
					return val;
				}
			}
			return null;
		}

		public static SimNao[] getSimNao() {
			return new SimNao[]{SIM, NAO};
		}

		public static SimNao[] getSimNaoSabe() {
			return new SimNao[]{SIM, NAO, NAO_SABE};
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}
	}

	public enum PeriodoExantemaGestacao implements IEnum {
		SIM_1_TRI(1L ,Bundle.getStringApplication("simPriTri")),
		SIM_2_TRI(2L , Bundle.getStringApplication("simSegTri")),
		SIM_3_TRI(3L , Bundle.getStringApplication("simTerTri")),
		SIM_NAO_LEMBRA(4L , Bundle.getStringApplication("simNaoLembraDataGestacional")),
		NAO_APRESENTOU(5L , Bundle.getStringApplication("naoApresentouExantema")),
		NAO_SABE(6L , Bundle.getStringApplication("naoSabe"))
		;

		private Long value;
		private String descricao;

		PeriodoExantemaGestacao(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static PeriodoExantemaGestacao valueOf(Long value) {
			for (PeriodoExantemaGestacao val : PeriodoExantemaGestacao.values()) {
				if (val.value().equals(value)) {
					return val;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}
	}

	public enum ReagenteNaoReagente implements IEnum {
		REAGENTE_POSITIVO(1L, Bundle.getStringApplication("reagentePositivo")),
		NAO_REAGENTE_NEGATIVO(2L, Bundle.getStringApplication("reagenteNegativo")),
		INDETECTAVEL_INDETERMINADO(3L, Bundle.getStringApplication("indetectavelIndeterminado")),
		NAO_REALIZADO(4L, Bundle.getStringApplication("naoRealizado")),
		IGNORADO(5L, Bundle.getStringApplication("ignorado")),
		;

		private Long value;
		private String descricao;

		ReagenteNaoReagente(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static ReagenteNaoReagente valueOf(Long value) {
			for (ReagenteNaoReagente val : ReagenteNaoReagente.values()) {
				if (val.value().equals(value)) {
					return val;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}
	}

	public enum HistoricoInfeccaoArbovirus implements IEnum {
		DENGUE(1L , Bundle.getStringApplication("dengue")),
		CHIKUNGUNYA(2L , Bundle.getStringApplication("chikungunya")),
		DENGUE_CHIKUNGUNYA(3L , Bundle.getStringApplication("dengueChikungunya")),
		SEM_HISTORICO(4L , Bundle.getStringApplication("semHistoricoDoenca")),
		NAO_SABE(5L , Bundle.getStringApplication("naoSabe")),
		IGNORADO(6L , Bundle.getStringApplication("ignorado"))
		;

		private Long value;
		private String descricao;

		HistoricoInfeccaoArbovirus(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static HistoricoInfeccaoArbovirus valueOf(Long value) {
			for (HistoricoInfeccaoArbovirus val : HistoricoInfeccaoArbovirus.values()) {
				if (val.value().equals(value)) {
					return val;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}
	}

	public enum ExamesImagensOpcoes implements IEnum {
		NAO_REALIZADO(1L, Bundle.getStringApplication("rotulo_naorealizado")),
		REALIZADO_NORMAL(2L, Bundle.getStringApplication("realizadoResultadoNormal")),
		REALIZADO_ALTERADO_INFECCAO(3L, Bundle.getStringApplication("realizadoResultadoAlteradoInfeccaoCongenita")),
		REALIZADO_ALTERADO_OUTRAS(4L, Bundle.getStringApplication("realizadoResultadoOutrasAlteracoes")),
		REALIZADO_INTEDERMINADO(5L, Bundle.getStringApplication("realizadoResultado")),
		IGNORADO(6L, Bundle.getStringApplication("ignorado"))
		;

		private Long value;
		private String descricao;

		ExamesImagensOpcoes(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static ExamesImagensOpcoes valueOf(Long value) {
			for (ExamesImagensOpcoes val : ExamesImagensOpcoes.values()) {
				if (val.value().equals(value)) {
					return val;
				}
			}
			return null;
		}

		public static boolean isIgnoradoOuNaoRealizado(Long value) {
			return NAO_REALIZADO.value().equals(value) || IGNORADO.value().equals(value);
		}

		public static boolean isRealizado(Long value) {
			return !isIgnoradoOuNaoRealizado(value);
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}
	}
}