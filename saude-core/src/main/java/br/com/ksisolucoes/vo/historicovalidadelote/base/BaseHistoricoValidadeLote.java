package br.com.ksisolucoes.vo.historicovalidadelote.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the historico_validade_lote table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="historico_validade_lote"
 */

public abstract class BaseHistoricoValidadeLote extends BaseRootVO implements Serializable {

	public static String REF = "HistoricoValidadeLote";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_DATA_ALTERACAO = "dataAlteracao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_PRODUTO = "produto";
	public static final String PROP_FABRICANTE = "fabricante";
	public static final String PROP_GRUPO_ESTOQUE = "grupoEstoque";
	public static final String PROP_DATA_VALIDADE_ANTIGA = "dataValidadeAntiga";


	// constructors
	public BaseHistoricoValidadeLote () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseHistoricoValidadeLote (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseHistoricoValidadeLote (
		java.lang.Long codigo,
		java.util.Date dataValidadeAntiga) {

		this.setCodigo(codigo);
		this.setDataValidadeAntiga(dataValidadeAntiga);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataAlteracao;
	private java.util.Date dataValidadeAntiga;
	private java.lang.String grupoEstoque;

	// many to one
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.entradas.estoque.Produto produto;
	private br.com.ksisolucoes.vo.entradas.estoque.Fabricante fabricante;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  column="cd_hist_val_lote"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_alteracao
	 */
	public java.util.Date getDataAlteracao () {
		return getPropertyValue(this, dataAlteracao, PROP_DATA_ALTERACAO); 
	}

	/**
	 * Set the value related to the column: dt_alteracao
	 * @param dataAlteracao the dt_alteracao value
	 */
	public void setDataAlteracao (java.util.Date dataAlteracao) {
//        java.util.Date dataAlteracaoOld = this.dataAlteracao;
		this.dataAlteracao = dataAlteracao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAlteracao", dataAlteracaoOld, dataAlteracao);
	}



	/**
	 * Return the value associated with the column: dt_val_antiga
	 */
	public java.util.Date getDataValidadeAntiga () {
		return getPropertyValue(this, dataValidadeAntiga, PROP_DATA_VALIDADE_ANTIGA); 
	}

	/**
	 * Set the value related to the column: dt_val_antiga
	 * @param dataValidadeAntiga the dt_val_antiga value
	 */
	public void setDataValidadeAntiga (java.util.Date dataValidadeAntiga) {
//        java.util.Date dataValidadeAntigaOld = this.dataValidadeAntiga;
		this.dataValidadeAntiga = dataValidadeAntiga;
//        this.getPropertyChangeSupport().firePropertyChange ("dataValidadeAntiga", dataValidadeAntigaOld, dataValidadeAntiga);
	}



	/**
	 * Return the value associated with the column: grupo_estoque
	 */
	public java.lang.String getGrupoEstoque () {
		return getPropertyValue(this, grupoEstoque, PROP_GRUPO_ESTOQUE); 
	}

	/**
	 * Set the value related to the column: grupo_estoque
	 * @param grupoEstoque the grupo_estoque value
	 */
	public void setGrupoEstoque (java.lang.String grupoEstoque) {
//        java.lang.String grupoEstoqueOld = this.grupoEstoque;
		this.grupoEstoque = grupoEstoque;
//        this.getPropertyChangeSupport().firePropertyChange ("grupoEstoque", grupoEstoqueOld, grupoEstoque);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cod_pro
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Produto getProduto () {
		return getPropertyValue(this, produto, PROP_PRODUTO); 
	}

	/**
	 * Set the value related to the column: cod_pro
	 * @param produto the cod_pro value
	 */
	public void setProduto (br.com.ksisolucoes.vo.entradas.estoque.Produto produto) {
//        br.com.ksisolucoes.vo.entradas.estoque.Produto produtoOld = this.produto;
		this.produto = produto;
//        this.getPropertyChangeSupport().firePropertyChange ("produto", produtoOld, produto);
	}



	/**
	 * Return the value associated with the column: cd_fabricante
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Fabricante getFabricante () {
		return getPropertyValue(this, fabricante, PROP_FABRICANTE); 
	}

	/**
	 * Set the value related to the column: cd_fabricante
	 * @param fabricante the cd_fabricante value
	 */
	public void setFabricante (br.com.ksisolucoes.vo.entradas.estoque.Fabricante fabricante) {
//        br.com.ksisolucoes.vo.entradas.estoque.Fabricante fabricanteOld = this.fabricante;
		this.fabricante = fabricante;
//        this.getPropertyChangeSupport().firePropertyChange ("fabricante", fabricanteOld, fabricante);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.historicovalidadelote.HistoricoValidadeLote)) return false;
		else {
			br.com.ksisolucoes.vo.historicovalidadelote.HistoricoValidadeLote historicoValidadeLote = (br.com.ksisolucoes.vo.historicovalidadelote.HistoricoValidadeLote) obj;
			if (null == this.getCodigo() || null == historicoValidadeLote.getCodigo()) return false;
			else return (this.getCodigo().equals(historicoValidadeLote.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}