package br.com.ksisolucoes.vo.basico;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.basico.base.BaseInvestigacaoAgravoImunologia;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;



public class InvestigacaoAgravoImunologia extends BaseInvestigacaoAgravoImunologia implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public InvestigacaoAgravoImunologia () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public InvestigacaoAgravoImunologia (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public InvestigacaoAgravoImunologia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.InvestigacaoAgravo investigacaoAgravo) {

		super (
			codigo,
			investigacaoAgravo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public enum Agravo implements IEnum {
		DENGUE(1L, Bundle.getStringApplication("rotulo_dengue")),
		FEBRE_AMARELA(2L, Bundle.getStringApplication("rotulo_febre_amarela")),
		RUBEOLA(3L, Bundle.getStringApplication("rotulo_rubeola")),
		HEPATITE_A(4L, Bundle.getStringApplication("rotulo_hepatite_a")),
		HEPATITE_B(5L, Bundle.getStringApplication("rotulo_hepatite_b")),
		HIV(6L, Bundle.getStringApplication("rotulo_hiv")),
		OUTROS(7L, Bundle.getStringApplication("rotulo_outros_especificar")),
		;

		private final Long value;
		private final String descricao;

		private Agravo(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static Agravo valueOf(Long value) {
			for (Agravo agravo : Agravo.values()) {
				if (agravo.value().equals(value)) {
					return agravo;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}
	}

	public String getDescricaoAgravo() {
		Agravo agravo = Agravo.valueOf(getAgravo());
		if (agravo != null) {
			if (agravo.OUTROS.value.equals(agravo.value) && getDescricaoOutrosAgravo() != null) {
				return agravo.descricao() + ": " + getDescricaoOutrosAgravo();
			}
			return agravo.descricao();
		}
		return null;
	}
}