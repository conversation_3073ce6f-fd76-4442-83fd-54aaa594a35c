<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="NodoConsultaProntuario" table="nodo_consulta_prontuario" >
    
        <id
            column="cd_nodo_consulta"
            name="codigo"
            type="java.lang.Long"
            >
            <generator class="assigned" />
        </id> <version column="version" name="version" type="long" />   
                 
        <many-to-one  
            class="br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCbo"
            name="grupoAtendimentoCbo"
            column="cd_grupo_atend_cbo"
        />

        <property
         	name="classeNodo"
         	column="classe_nodo"
         	type="java.lang.String"
                length="512"
         />
  
        <property
         	name="ordem"
         	column="ordem"
         	type="java.lang.Long"
         />

    </class>
</hibernate-mapping>
