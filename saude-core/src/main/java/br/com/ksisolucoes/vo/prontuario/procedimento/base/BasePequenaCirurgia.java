package br.com.ksisolucoes.vo.prontuario.procedimento.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the pequena_cirurgia table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="pequena_cirurgia"
 */

public abstract class BasePequenaCirurgia extends BaseRootVO implements Serializable {

	public static String REF = "PequenaCirurgia";
	public static final String PROP_STATUS = "status";
	public static final String PROP_QUANTIDADE_PROCEDIMENTO = "quantidadeProcedimento";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_ANOTACAO = "anotacao";
	public static final String PROP_PROCEDIMENTO = "procedimento";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_ENCAMINHAMENTO = "encaminhamento";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";
	public static final String PROP_ATENDIMENTO = "atendimento";


	// constructors
	public BasePequenaCirurgia () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BasePequenaCirurgia (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long quantidadeProcedimento;
	private java.lang.String anotacao;
	private java.lang.Long status;
	private java.util.Date dataCancelamento;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento encaminhamento;
	private br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento;
	private br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_pequena_cirurgia"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: qt_procedimento
	 */
	public java.lang.Long getQuantidadeProcedimento () {
		return getPropertyValue(this, quantidadeProcedimento, PROP_QUANTIDADE_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: qt_procedimento
	 * @param quantidadeProcedimento the qt_procedimento value
	 */
	public void setQuantidadeProcedimento (java.lang.Long quantidadeProcedimento) {
//        java.lang.Long quantidadeProcedimentoOld = this.quantidadeProcedimento;
		this.quantidadeProcedimento = quantidadeProcedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeProcedimento", quantidadeProcedimentoOld, quantidadeProcedimento);
	}



	/**
	 * Return the value associated with the column: anotacao
	 */
	public java.lang.String getAnotacao () {
		return getPropertyValue(this, anotacao, PROP_ANOTACAO); 
	}

	/**
	 * Set the value related to the column: anotacao
	 * @param anotacao the anotacao value
	 */
	public void setAnotacao (java.lang.String anotacao) {
//        java.lang.String anotacaoOld = this.anotacao;
		this.anotacao = anotacao;
//        this.getPropertyChangeSupport().firePropertyChange ("anotacao", anotacaoOld, anotacao);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: cd_encaminhamento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento getEncaminhamento () {
		return getPropertyValue(this, encaminhamento, PROP_ENCAMINHAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_encaminhamento
	 * @param encaminhamento the cd_encaminhamento value
	 */
	public void setEncaminhamento (br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento encaminhamento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento encaminhamentoOld = this.encaminhamento;
		this.encaminhamento = encaminhamento;
//        this.getPropertyChangeSupport().firePropertyChange ("encaminhamento", encaminhamentoOld, encaminhamento);
	}



	/**
	 * Return the value associated with the column: cd_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento getProcedimento () {
		return getPropertyValue(this, procedimento, PROP_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_procedimento
	 * @param procedimento the cd_procedimento value
	 */
	public void setProcedimento (br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoOld = this.procedimento;
		this.procedimento = procedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimento", procedimentoOld, procedimento);
	}



	/**
	 * Return the value associated with the column: nr_atendimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Atendimento getAtendimento () {
		return getPropertyValue(this, atendimento, PROP_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: nr_atendimento
	 * @param atendimento the nr_atendimento value
	 */
	public void setAtendimento (br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoOld = this.atendimento;
		this.atendimento = atendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimento", atendimentoOld, atendimento);
	}



	/**
	 * Return the value associated with the column: cd_usuario_can
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_can
	 * @param usuarioCancelamento the cd_usuario_can value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.procedimento.PequenaCirurgia)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.procedimento.PequenaCirurgia pequenaCirurgia = (br.com.ksisolucoes.vo.prontuario.procedimento.PequenaCirurgia) obj;
			if (null == this.getCodigo() || null == pequenaCirurgia.getCodigo()) return false;
			else return (this.getCodigo().equals(pequenaCirurgia.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}