package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the receituario_modelo_item table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="receituario_modelo_item"
 */

public abstract class BaseReceituarioModeloItem extends BaseRootVO implements Serializable {

	public static String REF = "ReceituarioModeloItem";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TIPO_VIA_MEDICAMENTO = "tipoViaMedicamento";
	public static final String PROP_RECEITUARIO_MODELO = "receituarioModelo";
	public static final String PROP_POSOLOGIA = "posologia";
	public static final String PROP_NOME_PRODUTO = "nomeProduto";


	// constructors
	public BaseReceituarioModeloItem () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseReceituarioModeloItem (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseReceituarioModeloItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.ReceituarioModelo receituarioModelo,
		java.lang.String nomeProduto,
		java.lang.String posologia) {

		this.setCodigo(codigo);
		this.setReceituarioModelo(receituarioModelo);
		this.setNomeProduto(nomeProduto);
		this.setPosologia(posologia);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String nomeProduto;
	private java.lang.String posologia;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.ReceituarioModelo receituarioModelo;
	private br.com.ksisolucoes.vo.entradas.estoque.TipoViaMedicamento tipoViaMedicamento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_receituario_modelo_item"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: nm_produto
	 */
	public java.lang.String getNomeProduto () {
		return getPropertyValue(this, nomeProduto, PROP_NOME_PRODUTO); 
	}

	/**
	 * Set the value related to the column: nm_produto
	 * @param nomeProduto the nm_produto value
	 */
	public void setNomeProduto (java.lang.String nomeProduto) {
//        java.lang.String nomeProdutoOld = this.nomeProduto;
		this.nomeProduto = nomeProduto;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeProduto", nomeProdutoOld, nomeProduto);
	}



	/**
	 * Return the value associated with the column: posologia
	 */
	public java.lang.String getPosologia () {
		return getPropertyValue(this, posologia, PROP_POSOLOGIA); 
	}

	/**
	 * Set the value related to the column: posologia
	 * @param posologia the posologia value
	 */
	public void setPosologia (java.lang.String posologia) {
//        java.lang.String posologiaOld = this.posologia;
		this.posologia = posologia;
//        this.getPropertyChangeSupport().firePropertyChange ("posologia", posologiaOld, posologia);
	}



	/**
	 * Return the value associated with the column: cd_receituario_modelo
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ReceituarioModelo getReceituarioModelo () {
		return getPropertyValue(this, receituarioModelo, PROP_RECEITUARIO_MODELO); 
	}

	/**
	 * Set the value related to the column: cd_receituario_modelo
	 * @param receituarioModelo the cd_receituario_modelo value
	 */
	public void setReceituarioModelo (br.com.ksisolucoes.vo.prontuario.basico.ReceituarioModelo receituarioModelo) {
//        br.com.ksisolucoes.vo.prontuario.basico.ReceituarioModelo receituarioModeloOld = this.receituarioModelo;
		this.receituarioModelo = receituarioModelo;
//        this.getPropertyChangeSupport().firePropertyChange ("receituarioModelo", receituarioModeloOld, receituarioModelo);
	}



	/**
	 * Return the value associated with the column: cd_tp_via_medicamento
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.TipoViaMedicamento getTipoViaMedicamento () {
		return getPropertyValue(this, tipoViaMedicamento, PROP_TIPO_VIA_MEDICAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_tp_via_medicamento
	 * @param tipoViaMedicamento the cd_tp_via_medicamento value
	 */
	public void setTipoViaMedicamento (br.com.ksisolucoes.vo.entradas.estoque.TipoViaMedicamento tipoViaMedicamento) {
//        br.com.ksisolucoes.vo.entradas.estoque.TipoViaMedicamento tipoViaMedicamentoOld = this.tipoViaMedicamento;
		this.tipoViaMedicamento = tipoViaMedicamento;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoViaMedicamento", tipoViaMedicamentoOld, tipoViaMedicamento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.ReceituarioModeloItem)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.ReceituarioModeloItem receituarioModeloItem = (br.com.ksisolucoes.vo.prontuario.basico.ReceituarioModeloItem) obj;
			if (null == this.getCodigo() || null == receituarioModeloItem.getCodigo()) return false;
			else return (this.getCodigo().equals(receituarioModeloItem.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}