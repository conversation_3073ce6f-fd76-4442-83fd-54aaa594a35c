package br.com.ksisolucoes.vo.service.sms;

import java.io.Serializable;

import br.com.ksisolucoes.vo.service.sms.base.BaseSmsCadastroDoenca;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class SmsCadastroDoenca extends BaseSmsCadastroDoenca implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public SmsCadastroDoenca () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public SmsCadastroDoenca (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public SmsCadastroDoenca (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.service.sms.SmsCadastro smsCadastro,
		br.com.ksisolucoes.vo.basico.Doenca doenca) {

		super (
			codigo,
			smsCadastro,
			doenca);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}