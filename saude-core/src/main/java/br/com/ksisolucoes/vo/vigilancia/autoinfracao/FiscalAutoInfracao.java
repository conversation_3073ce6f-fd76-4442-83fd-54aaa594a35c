package br.com.ksisolucoes.vo.vigilancia.autoinfracao;

import java.io.Serializable;

import br.com.ksisolucoes.vo.vigilancia.autoinfracao.base.BaseFiscalAutoInfracao;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class FiscalAutoInfracao extends BaseFiscalAutoInfracao implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public FiscalAutoInfracao () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public FiscalAutoInfracao (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public FiscalAutoInfracao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.Profissional profissional,
		br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao autoInfracao) {

		super (
			codigo,
			profissional,
			autoInfracao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}