<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.avaliacao"  >
    <class name="SisvanIntercorrencia" table="sisvan_intercorrencia">

        <id 
            name="codigo"
            type="java.lang.Long"
            column="cd_intercorrencia"
        >
            <generator class="assigned" />
        </id>
        <version column="version" name="version" type="long" />

        <property
            name="descricaoIntercorrencia"
            column="ds_intercorrencia"
            type="java.lang.String"
        />

        <property  
            name="codigoSisvan"
            column="cd_sisvan"
            type="java.lang.Long"
        />
    </class>
</hibernate-mapping>
