<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.agendamento.tfd"  >
    <class name="TipoRequisicaoExame" table="tipo_requisicao_exame" >

        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_tp_req_exame"
		> 
            <generator class="assigned"/>
        </id> <version column="version" name="version" type="long" />
		
        <property
            name="descricao"
            type="java.lang.String"
            column="ds_tp_req_exame"
            not-null="true"
            >
        </property>

    </class>
</hibernate-mapping>
