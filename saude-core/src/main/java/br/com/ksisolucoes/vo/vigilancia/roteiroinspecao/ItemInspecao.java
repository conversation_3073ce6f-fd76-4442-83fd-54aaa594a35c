package br.com.ksisolucoes.vo.vigilancia.roteiroinspecao;

import java.io.Serializable;

import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.base.BaseItemInspecao;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;

public class ItemInspecao extends BaseItemInspecao implements CodigoManager, PesquisaObjectInterface {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public ItemInspecao() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public ItemInspecao(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public ItemInspecao(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.controle.Usuario usuario,
            java.lang.String subtitulo,
            java.util.Date dataCadastro,
            java.util.Date dataUsuario) {

        super(
                codigo,
                usuario,
                subtitulo,
                dataCadastro,
                dataUsuario);
    }

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    @Override
    public String getDescricaoVO() {
        return this.getSubtitulo();
    }

    @Override
    public String getIdentificador() {
        return this.getCodigo().toString();
    }
}
