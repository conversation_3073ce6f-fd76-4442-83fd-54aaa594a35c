package br.com.ksisolucoes.vo.esus;

import java.io.Serializable;

import br.com.ksisolucoes.vo.esus.base.BaseExameEsus;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;



public class ExameEsus extends BaseExameEsus implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ExameEsus () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ExameEsus (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ExameEsus (
		java.lang.Long codigo,
		java.lang.String descricaoExameEsus,
		java.lang.String codigoEsus) {

		super (
			codigo,
			descricaoExameEsus,
			codigoEsus);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    @Override
    public String getDescricaoVO() {
        return getDescricaoExameEsus();
    }

    @Override
    public String getIdentificador() {
        return getCodigo().toString();
    }
}