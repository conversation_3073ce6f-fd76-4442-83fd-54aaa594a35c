package br.com.ksisolucoes.vo.cadsus.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the usuario_cadsus_ocorrencia table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="usuario_cadsus_ocorrencia"
 */

public abstract class BaseUsuarioCadsusOcorrencia extends BaseRootVO implements Serializable {

	public static String REF = "UsuarioCadsusOcorrencia";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_PRODUTO_SOLICITADO = "produtoSolicitado";
	public static final String PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO = "agendaGradeAtendimentoHorario";
	public static final String PROP_TIPO_OCORRENCIA = "tipoOcorrencia";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_DATA_OCORRENCIA = "dataOcorrencia";
	public static final String PROP_LAUDO_TFD = "laudoTfd";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_EXAME = "exame";
	public static final String PROP_ENCAMINHAMENTO = "encaminhamento";


	// constructors
	public BaseUsuarioCadsusOcorrencia () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseUsuarioCadsusOcorrencia (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseUsuarioCadsusOcorrencia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		br.com.ksisolucoes.vo.cadsus.TipoOcorrencia tipoOcorrencia,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.basico.ProdutoSolicitado produtoSolicitado,
		java.util.Date dataOcorrencia,
		java.lang.String descricao) {

		this.setCodigo(codigo);
		this.setUsuarioCadsus(usuarioCadsus);
		this.setTipoOcorrencia(tipoOcorrencia);
		this.setUsuario(usuario);
		this.setProdutoSolicitado(produtoSolicitado);
		this.setDataOcorrencia(dataOcorrencia);
		this.setDescricao(descricao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataOcorrencia;
	private java.lang.String descricao;

	// many to one
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private br.com.ksisolucoes.vo.cadsus.TipoOcorrencia tipoOcorrencia;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento encaminhamento;
	private br.com.ksisolucoes.vo.prontuario.basico.Exame exame;
	private br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd laudoTfd;
	private br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario;
	private br.com.ksisolucoes.vo.basico.ProdutoSolicitado produtoSolicitado;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_ocorrencia_cadsus"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_ocorrencia
	 */
	public java.util.Date getDataOcorrencia () {
		return getPropertyValue(this, dataOcorrencia, PROP_DATA_OCORRENCIA); 
	}

	/**
	 * Set the value related to the column: dt_ocorrencia
	 * @param dataOcorrencia the dt_ocorrencia value
	 */
	public void setDataOcorrencia (java.util.Date dataOcorrencia) {
//        java.util.Date dataOcorrenciaOld = this.dataOcorrencia;
		this.dataOcorrencia = dataOcorrencia;
//        this.getPropertyChangeSupport().firePropertyChange ("dataOcorrencia", dataOcorrenciaOld, dataOcorrencia);
	}



	/**
	 * Return the value associated with the column: ds_ocorrencia
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_ocorrencia
	 * @param descricao the ds_ocorrencia value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cd_tp_ocorrencia
	 */
	public br.com.ksisolucoes.vo.cadsus.TipoOcorrencia getTipoOcorrencia () {
		return getPropertyValue(this, tipoOcorrencia, PROP_TIPO_OCORRENCIA); 
	}

	/**
	 * Set the value related to the column: cd_tp_ocorrencia
	 * @param tipoOcorrencia the cd_tp_ocorrencia value
	 */
	public void setTipoOcorrencia (br.com.ksisolucoes.vo.cadsus.TipoOcorrencia tipoOcorrencia) {
//        br.com.ksisolucoes.vo.cadsus.TipoOcorrencia tipoOcorrenciaOld = this.tipoOcorrencia;
		this.tipoOcorrencia = tipoOcorrencia;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoOcorrencia", tipoOcorrenciaOld, tipoOcorrencia);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_encaminhamento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento getEncaminhamento () {
		return getPropertyValue(this, encaminhamento, PROP_ENCAMINHAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_encaminhamento
	 * @param encaminhamento the cd_encaminhamento value
	 */
	public void setEncaminhamento (br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento encaminhamento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento encaminhamentoOld = this.encaminhamento;
		this.encaminhamento = encaminhamento;
//        this.getPropertyChangeSupport().firePropertyChange ("encaminhamento", encaminhamentoOld, encaminhamento);
	}



	/**
	 * Return the value associated with the column: cd_exame
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Exame getExame () {
		return getPropertyValue(this, exame, PROP_EXAME); 
	}

	/**
	 * Set the value related to the column: cd_exame
	 * @param exame the cd_exame value
	 */
	public void setExame (br.com.ksisolucoes.vo.prontuario.basico.Exame exame) {
//        br.com.ksisolucoes.vo.prontuario.basico.Exame exameOld = this.exame;
		this.exame = exame;
//        this.getPropertyChangeSupport().firePropertyChange ("exame", exameOld, exame);
	}



	/**
	 * Return the value associated with the column: cd_laudo_tfd
	 */
	public br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd getLaudoTfd () {
		return getPropertyValue(this, laudoTfd, PROP_LAUDO_TFD); 
	}

	/**
	 * Set the value related to the column: cd_laudo_tfd
	 * @param laudoTfd the cd_laudo_tfd value
	 */
	public void setLaudoTfd (br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd laudoTfd) {
//        br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd laudoTfdOld = this.laudoTfd;
		this.laudoTfd = laudoTfd;
//        this.getPropertyChangeSupport().firePropertyChange ("laudoTfd", laudoTfdOld, laudoTfd);
	}



	/**
	 * Return the value associated with the column: cd_ag_gra_ate_hor
	 */
	public br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario getAgendaGradeAtendimentoHorario () {
		return getPropertyValue(this, agendaGradeAtendimentoHorario, PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO); 
	}

	/**
	 * Set the value related to the column: cd_ag_gra_ate_hor
	 * @param agendaGradeAtendimentoHorario the cd_ag_gra_ate_hor value
	 */
	public void setAgendaGradeAtendimentoHorario (br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario) {
//        br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorarioOld = this.agendaGradeAtendimentoHorario;
		this.agendaGradeAtendimentoHorario = agendaGradeAtendimentoHorario;
//        this.getPropertyChangeSupport().firePropertyChange ("agendaGradeAtendimentoHorario", agendaGradeAtendimentoHorarioOld, agendaGradeAtendimentoHorario);
	}



	/**
	 * Return the value associated with the column: cd_prod_solic
	 */
	public br.com.ksisolucoes.vo.basico.ProdutoSolicitado getProdutoSolicitado () {
		return getPropertyValue(this, produtoSolicitado, PROP_PRODUTO_SOLICITADO); 
	}

	/**
	 * Set the value related to the column: cd_prod_solic
	 * @param produtoSolicitado the cd_prod_solic value
	 */
	public void setProdutoSolicitado (br.com.ksisolucoes.vo.basico.ProdutoSolicitado produtoSolicitado) {
//        br.com.ksisolucoes.vo.basico.ProdutoSolicitado produtoSolicitadoOld = this.produtoSolicitado;
		this.produtoSolicitado = produtoSolicitado;
//        this.getPropertyChangeSupport().firePropertyChange ("produtoSolicitado", produtoSolicitadoOld, produtoSolicitado);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.cadsus.UsuarioCadsusOcorrencia)) return false;
		else {
			br.com.ksisolucoes.vo.cadsus.UsuarioCadsusOcorrencia usuarioCadsusOcorrencia = (br.com.ksisolucoes.vo.cadsus.UsuarioCadsusOcorrencia) obj;
			if (null == this.getCodigo() || null == usuarioCadsusOcorrencia.getCodigo()) return false;
			else return (this.getCodigo().equals(usuarioCadsusOcorrencia.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}