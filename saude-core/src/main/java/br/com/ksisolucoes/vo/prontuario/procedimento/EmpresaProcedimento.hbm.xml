<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.procedimento"  >
	<class name="EmpresaProcedimento" table="empresa_procedimento">
		<composite-id class="EmpresaProcedimentoPK" name="id">
			<key-many-to-one
				class="br.com.ksisolucoes.vo.basico.Empresa"
				column="empresa"
				name="empresa"
			 />
			<key-many-to-one
				class="Procedimento"
				column="cd_procedimento"
				name="procedimento"
			 />
		</composite-id> <version column="version" name="version" type="long" />
	
		<property
			column="nr_agendamento"
			name="numeroAgendamento"
			type="java.lang.Long"
		 />
		 
		 <property
			column="nr_agendamento_max"
			name="numeroAgendamentoMaximo"
			type="java.lang.Long"
		 />
		 
	</class>
</hibernate-mapping>
