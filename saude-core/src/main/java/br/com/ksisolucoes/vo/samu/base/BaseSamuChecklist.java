package br.com.ksisolucoes.vo.samu.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the samu_checklist table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="samu_checklist"
 */

public abstract class BaseSamuChecklist extends BaseRootVO implements Serializable {

	public static String REF = "SamuChecklist";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_TABELA_CBO = "tabelaCbo";


	// constructors
	public BaseSamuChecklist () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseSamuChecklist (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseSamuChecklist (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCbo,
		java.lang.String descricao,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setTabelaCbo(tabelaCbo);
		this.setDescricao(descricao);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.util.Date dataCadastro;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCbo;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_samu_checklist"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_samu_checklist
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_samu_checklist
	 * @param descricao the ds_samu_checklist value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: cd_cbo
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getTabelaCbo () {
		return getPropertyValue(this, tabelaCbo, PROP_TABELA_CBO); 
	}

	/**
	 * Set the value related to the column: cd_cbo
	 * @param tabelaCbo the cd_cbo value
	 */
	public void setTabelaCbo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCbo) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCboOld = this.tabelaCbo;
		this.tabelaCbo = tabelaCbo;
//        this.getPropertyChangeSupport().firePropertyChange ("tabelaCbo", tabelaCboOld, tabelaCbo);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.samu.SamuChecklist)) return false;
		else {
			br.com.ksisolucoes.vo.samu.SamuChecklist samuChecklist = (br.com.ksisolucoes.vo.samu.SamuChecklist) obj;
			if (null == this.getCodigo() || null == samuChecklist.getCodigo()) return false;
			else return (this.getCodigo().equals(samuChecklist.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}