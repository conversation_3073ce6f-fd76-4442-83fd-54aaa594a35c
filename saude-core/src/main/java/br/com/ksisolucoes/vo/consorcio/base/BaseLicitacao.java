package br.com.ksisolucoes.vo.consorcio.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the licitacao table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="licitacao"
 */

public abstract class BaseLicitacao extends BaseRootVO implements Serializable {

	public static String REF = "Licitacao";
	public static final String PROP_USUARIO_APROVACAO = "usuarioAprovacao";
	public static final String PROP_DATA_APROVACAO = "dataAprovacao";
	public static final String PROP_STATUS = "status";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_TIPO_CONTA = "tipoConta";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_USUARIO_CADASTRO = "usuarioCadastro";
	public static final String PROP_DATA_VALIDADE = "dataValidade";
	public static final String PROP_NUMERO_PREGAO = "numeroPregao";
	public static final String PROP_DATA_FECHAMENTO = "dataFechamento";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";
	public static final String PROP_USUARIO_FECHAMENTO = "usuarioFechamento";


	// constructors
	public BaseLicitacao () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseLicitacao (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseLicitacao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
		br.com.ksisolucoes.vo.consorcio.TipoConta tipoConta,
		java.util.Date dataCadastro,
		java.lang.Long status) {

		this.setCodigo(codigo);
		this.setEmpresa(empresa);
		this.setUsuarioCadastro(usuarioCadastro);
		this.setTipoConta(tipoConta);
		this.setDataCadastro(dataCadastro);
		this.setStatus(status);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataCadastro;
	private java.lang.String observacao;
	private java.lang.String numeroPregao;
	private java.util.Date dataValidade;
	private java.lang.Long status;
	private java.util.Date dataAprovacao;
	private java.util.Date dataCancelamento;
	private java.util.Date dataFechamento;

	// many to one
	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioAprovacao;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioFechamento;
	private br.com.ksisolucoes.vo.consorcio.TipoConta tipoConta;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_licitacao"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: num_pregao
	 */
	public java.lang.String getNumeroPregao () {
		return getPropertyValue(this, numeroPregao, PROP_NUMERO_PREGAO); 
	}

	/**
	 * Set the value related to the column: num_pregao
	 * @param numeroPregao the num_pregao value
	 */
	public void setNumeroPregao (java.lang.String numeroPregao) {
//        java.lang.String numeroPregaoOld = this.numeroPregao;
		this.numeroPregao = numeroPregao;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroPregao", numeroPregaoOld, numeroPregao);
	}



	/**
	 * Return the value associated with the column: dt_validade
	 */
	public java.util.Date getDataValidade () {
		return getPropertyValue(this, dataValidade, PROP_DATA_VALIDADE); 
	}

	/**
	 * Set the value related to the column: dt_validade
	 * @param dataValidade the dt_validade value
	 */
	public void setDataValidade (java.util.Date dataValidade) {
//        java.util.Date dataValidadeOld = this.dataValidade;
		this.dataValidade = dataValidade;
//        this.getPropertyChangeSupport().firePropertyChange ("dataValidade", dataValidadeOld, dataValidade);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: dt_aprovacao
	 */
	public java.util.Date getDataAprovacao () {
		return getPropertyValue(this, dataAprovacao, PROP_DATA_APROVACAO); 
	}

	/**
	 * Set the value related to the column: dt_aprovacao
	 * @param dataAprovacao the dt_aprovacao value
	 */
	public void setDataAprovacao (java.util.Date dataAprovacao) {
//        java.util.Date dataAprovacaoOld = this.dataAprovacao;
		this.dataAprovacao = dataAprovacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAprovacao", dataAprovacaoOld, dataAprovacao);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: dt_fechamento
	 */
	public java.util.Date getDataFechamento () {
		return getPropertyValue(this, dataFechamento, PROP_DATA_FECHAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_fechamento
	 * @param dataFechamento the dt_fechamento value
	 */
	public void setDataFechamento (java.util.Date dataFechamento) {
//        java.util.Date dataFechamentoOld = this.dataFechamento;
		this.dataFechamento = dataFechamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataFechamento", dataFechamentoOld, dataFechamento);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCadastro () {
		return getPropertyValue(this, usuarioCadastro, PROP_USUARIO_CADASTRO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuarioCadastro the cd_usuario value
	 */
	public void setUsuarioCadastro (br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCadastroOld = this.usuarioCadastro;
		this.usuarioCadastro = usuarioCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadastro", usuarioCadastroOld, usuarioCadastro);
	}



	/**
	 * Return the value associated with the column: cd_usu_aprovacao
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioAprovacao () {
		return getPropertyValue(this, usuarioAprovacao, PROP_USUARIO_APROVACAO); 
	}

	/**
	 * Set the value related to the column: cd_usu_aprovacao
	 * @param usuarioAprovacao the cd_usu_aprovacao value
	 */
	public void setUsuarioAprovacao (br.com.ksisolucoes.vo.controle.Usuario usuarioAprovacao) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioAprovacaoOld = this.usuarioAprovacao;
		this.usuarioAprovacao = usuarioAprovacao;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioAprovacao", usuarioAprovacaoOld, usuarioAprovacao);
	}



	/**
	 * Return the value associated with the column: cd_usu_cancelamento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usu_cancelamento
	 * @param usuarioCancelamento the cd_usu_cancelamento value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}



	/**
	 * Return the value associated with the column: cd_usu_fechamento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioFechamento () {
		return getPropertyValue(this, usuarioFechamento, PROP_USUARIO_FECHAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usu_fechamento
	 * @param usuarioFechamento the cd_usu_fechamento value
	 */
	public void setUsuarioFechamento (br.com.ksisolucoes.vo.controle.Usuario usuarioFechamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioFechamentoOld = this.usuarioFechamento;
		this.usuarioFechamento = usuarioFechamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioFechamento", usuarioFechamentoOld, usuarioFechamento);
	}



	/**
	 * Return the value associated with the column: cd_tp_conta
	 */
	public br.com.ksisolucoes.vo.consorcio.TipoConta getTipoConta () {
		return getPropertyValue(this, tipoConta, PROP_TIPO_CONTA); 
	}

	/**
	 * Set the value related to the column: cd_tp_conta
	 * @param tipoConta the cd_tp_conta value
	 */
	public void setTipoConta (br.com.ksisolucoes.vo.consorcio.TipoConta tipoConta) {
//        br.com.ksisolucoes.vo.consorcio.TipoConta tipoContaOld = this.tipoConta;
		this.tipoConta = tipoConta;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoConta", tipoContaOld, tipoConta);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.consorcio.Licitacao)) return false;
		else {
			br.com.ksisolucoes.vo.consorcio.Licitacao licitacao = (br.com.ksisolucoes.vo.consorcio.Licitacao) obj;
			if (null == this.getCodigo() || null == licitacao.getCodigo()) return false;
			else return (this.getCodigo().equals(licitacao.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}