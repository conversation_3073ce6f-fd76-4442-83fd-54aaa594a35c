package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseIMTableSolicitacaoAgendamentoToExameBpai;



public class IMTableSolicitacaoAgendamentoToExameBpai extends BaseIMTableSolicitacaoAgendamentoToExameBpai implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public IMTableSolicitacaoAgendamentoToExameBpai () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public IMTableSolicitacaoAgendamentoToExameBpai (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public IMTableSolicitacaoAgendamentoToExameBpai (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento solicitacaoAgendamento,
		br.com.ksisolucoes.vo.prontuario.basico.ExameBpai exameBpai) {

		super (
			codigo,
			solicitacaoAgendamento,
			exameBpai);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}