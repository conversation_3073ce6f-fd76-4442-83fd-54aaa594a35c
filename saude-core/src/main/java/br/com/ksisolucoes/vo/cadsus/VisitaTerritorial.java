package br.com.ksisolucoes.vo.cadsus;

import java.io.Serializable;

import br.com.ksisolucoes.vo.cadsus.base.BaseVisitaTerritorial;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class VisitaTerritorial extends BaseVisitaTerritorial implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public VisitaTerritorial () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public VisitaTerritorial (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public VisitaTerritorial (
		java.lang.Long codigo,
		java.lang.String pontoReferencia) {

		super (
			codigo,
			pontoReferencia);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }


	public String getDescricaoTipoImovel(){
		EnderecoDomicilioEsus.TipoImovel tipoImovel = EnderecoDomicilioEsus.TipoImovel.valueOf(getTipoImovel());
		if (tipoImovel != null && tipoImovel.value() != null) {
			return EnderecoDomicilioEsus.TipoImovel.valueOf(tipoImovel.value()).descricao();
		}
		return "";
	}
}