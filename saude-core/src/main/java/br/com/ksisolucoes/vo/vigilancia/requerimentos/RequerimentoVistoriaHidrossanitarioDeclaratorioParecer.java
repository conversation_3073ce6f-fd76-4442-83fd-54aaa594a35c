package br.com.ksisolucoes.vo.vigilancia.requerimentos;

import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.base.BaseRequerimentoVistoriaHidrossanitarioDeclaratorioParecer;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

import java.io.Serializable;



public class RequerimentoVistoriaHidrossanitarioDeclaratorioParecer extends BaseRequerimentoVistoriaHidrossanitarioDeclaratorioParecer implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequerimentoVistoriaHidrossanitarioDeclaratorioParecer () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequerimentoVistoriaHidrossanitarioDeclaratorioParecer (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequerimentoVistoriaHidrossanitarioDeclaratorioParecer (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaHidrossanitarioDeclaratorio requerimentoVistoriaHidrossanitarioDeclaratorio,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataParecer,
		java.util.Date dataCadastro) {

		super (
			codigo,
			requerimentoVistoriaHidrossanitarioDeclaratorio,
			usuario,
			dataParecer,
			dataCadastro);
	}

/*[CONSTRUCTOR MARKER END]*/

	public enum TipoConcessao implements IEnum {
		DECLARATORIA(0L, Bundle.getStringApplication("rotulo_declaratoria")),
		PADRAO(1L, Bundle.getStringApplication("rotulo_padrao")),
		DECLARATORIA_AUDITADA(2L, Bundle.getStringApplication("rotulo_declaratoria_auditada"));

		private Long value;
		private String descricao;

		private TipoConcessao(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static TipoConcessao valeuOf(Long value) {
			for (TipoConcessao tipoConcessao : TipoConcessao.values()) {
				if (tipoConcessao.value().equals(value)) {
					return tipoConcessao;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}

	}

	public String getTipoConcessaoFormatado() {
		if (TipoConcessao.DECLARATORIA.value().equals(getTipoConcessao())) {
			return Bundle.getStringApplication("rotulo_declaratoria");
		} else if (TipoConcessao.PADRAO.value().equals(getTipoConcessao())) {
			return Bundle.getStringApplication("rotulo_padrao");
		} else if (TipoConcessao.DECLARATORIA_AUDITADA.value().equals(getTipoConcessao())) {
			return Bundle.getStringApplication("rotulo_declaratoria_auditada");
		} else {
			return null;
		}
	}

	public String getNumeroHabiteseFormatado() {
		return VigilanciaHelper.formatarProtocolo(getNumeroHabitese());
	}

	public String getDescricaoParecerSemHtml() {
		return StringUtil.removeHtmlString(getDescricaoParecer());
	}

	@Override
	public String getDescricaoParecer() {
		return StringUtil.limparHtml(super.getDescricaoParecer());
	}

	@Override
	public void setDescricaoParecer(String descricaoParecer) {
		super.setDescricaoParecer(StringUtil.limparHtml(descricaoParecer));
	}

	public String getRodape() {
		StringBuilder msg = new StringBuilder();

		if (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(getStatus())) {
			String realContext = TenantContext.getRealContext();
			if (realContext.equals("localhost")) {
				realContext = realContext.concat(":8080");
			}

			msg.append("IMPORTANTE")
					.append("\n\n1 - As vistorias de retorno devem ser reagendadas no prazo máximo de 90 dias da data de emissão do parecer de vistoria*;")
					.append("\n2 - Cada processo tem direito a uma primeira vistoria e mais três retornos*;")
					.append("\n3 - O não cumprimento dos itens 1 e 2 acima acarreta no indeferimento e arquivamento do processo*;")
					.append("\n4 - Para acompanhar o andamento do processo, consulte a página:  ")
					.append(realContext).append("/vigilancia/")
					.append(", clique no botão consulta de requerimento e informe a senha ")
					.append(this.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getCodigo()).append(".")
					.append("\n\n* Decreto Municipal nº 14793/15");

		} else if (RequerimentoVigilancia.Situacao.INDEFERIDO.value().equals(getStatus())) {
			msg.append("1 - Em caso de arquivamento do processo por indeferimento, o requerente deverá dar entrada em novo Processo de Vistoria de Habite-se Sanitário, não havendo a possibilidade de desarquivamento do processo anterior para a sua continuidade.")
					.append("\n2 - Para a solicitação de reconsideração de parecer de indeferimento o requerente deve protocolar processo de solicitação de parecer técnico e direcionar o pedido para a COMATS.");
		}

		return msg.toString();
	}

	public String getDescricaoStatus() {
		if (getStatus() != null) {
			RequerimentoVigilancia.Situacao situacao = RequerimentoVigilancia.Situacao.valeuOf(getStatus());
			if (situacao != null) {
				return situacao.descricao();
			}
		}
		return "";
	}

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}