<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="AntecedentesObstetricos" table="antecedentes_obstetricos" >

        <id
            name="codigo"
            column="cd_antecedentes_obstetricos"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id>
         
        <version column="version" name="version" type="long" />

        <many-to-one
                class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"
                name="usuarioCadSus"
                column="cd_usu_cadsus"
        />

        <many-to-one
                name="ciap"
                class="br.com.ksisolucoes.vo.prontuario.basico.Ciap"
        		column="cd_ciap"
        />
    </class>
</hibernate-mapping>
