package br.com.ksisolucoes.vo.cadsus.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the usuario_cadsus_cns table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="usuario_cadsus_cns"
 */

public abstract class BaseUsuarioCadsusCns extends BaseRootVO implements Serializable {

	public static String REF = "UsuarioCadsusCns";
	public static final String PROP_DATA_ALTERACAO = "dataAlteracao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_EXCLUIDO = "excluido";
	public static final String PROP_DATA_ATRIBUICAO = "dataAtribuicao";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_VERSION_ALL = "versionAll";
	public static final String PROP_TIPO_CARTAO = "tipoCartao";
	public static final String PROP_NUMERO_CARTAO = "numeroCartao";


	// constructors
	public BaseUsuarioCadsusCns () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseUsuarioCadsusCns (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseUsuarioCadsusCns (
		java.lang.Long codigo,
		java.lang.String tipoCartao,
		java.util.Date dataAtribuicao) {

		this.setCodigo(codigo);
		this.setTipoCartao(tipoCartao);
		this.setDataAtribuicao(dataAtribuicao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long numeroCartao;
	private java.lang.String tipoCartao;
	private java.util.Date dataAtribuicao;
	private java.lang.Long excluido;
	private java.util.Date dataAlteracao;
	private java.lang.Long versionAll;

	// many to one
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_usu_cadsus_cns"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_numero_cartao
	 */
	public java.lang.Long getNumeroCartao () {
		return getPropertyValue(this, numeroCartao, PROP_NUMERO_CARTAO); 
	}

	/**
	 * Set the value related to the column: cd_numero_cartao
	 * @param numeroCartao the cd_numero_cartao value
	 */
	public void setNumeroCartao (java.lang.Long numeroCartao) {
//        java.lang.Long numeroCartaoOld = this.numeroCartao;
		this.numeroCartao = numeroCartao;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroCartao", numeroCartaoOld, numeroCartao);
	}



	/**
	 * Return the value associated with the column: tp_cartao
	 */
	public java.lang.String getTipoCartao () {
		return getPropertyValue(this, tipoCartao, PROP_TIPO_CARTAO); 
	}

	/**
	 * Set the value related to the column: tp_cartao
	 * @param tipoCartao the tp_cartao value
	 */
	public void setTipoCartao (java.lang.String tipoCartao) {
//        java.lang.String tipoCartaoOld = this.tipoCartao;
		this.tipoCartao = tipoCartao;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoCartao", tipoCartaoOld, tipoCartao);
	}



	/**
	 * Return the value associated with the column: dt_atribuicao
	 */
	public java.util.Date getDataAtribuicao () {
		return getPropertyValue(this, dataAtribuicao, PROP_DATA_ATRIBUICAO); 
	}

	/**
	 * Set the value related to the column: dt_atribuicao
	 * @param dataAtribuicao the dt_atribuicao value
	 */
	public void setDataAtribuicao (java.util.Date dataAtribuicao) {
//        java.util.Date dataAtribuicaoOld = this.dataAtribuicao;
		this.dataAtribuicao = dataAtribuicao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAtribuicao", dataAtribuicaoOld, dataAtribuicao);
	}



	/**
	 * Return the value associated with the column: st_excluido
	 */
	public java.lang.Long getExcluido () {
		return getPropertyValue(this, excluido, PROP_EXCLUIDO); 
	}

	/**
	 * Set the value related to the column: st_excluido
	 * @param excluido the st_excluido value
	 */
	public void setExcluido (java.lang.Long excluido) {
//        java.lang.Long excluidoOld = this.excluido;
		this.excluido = excluido;
//        this.getPropertyChangeSupport().firePropertyChange ("excluido", excluidoOld, excluido);
	}



	/**
	 * Return the value associated with the column: dt_alteracao
	 */
	public java.util.Date getDataAlteracao () {
		return getPropertyValue(this, dataAlteracao, PROP_DATA_ALTERACAO); 
	}

	/**
	 * Set the value related to the column: dt_alteracao
	 * @param dataAlteracao the dt_alteracao value
	 */
	public void setDataAlteracao (java.util.Date dataAlteracao) {
//        java.util.Date dataAlteracaoOld = this.dataAlteracao;
		this.dataAlteracao = dataAlteracao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAlteracao", dataAlteracaoOld, dataAlteracao);
	}



	/**
	 * Return the value associated with the column: version_all
	 */
	public java.lang.Long getVersionAll () {
		return getPropertyValue(this, versionAll, PROP_VERSION_ALL); 
	}

	/**
	 * Set the value related to the column: version_all
	 * @param versionAll the version_all value
	 */
	public void setVersionAll (java.lang.Long versionAll) {
//        java.lang.Long versionAllOld = this.versionAll;
		this.versionAll = versionAll;
//        this.getPropertyChangeSupport().firePropertyChange ("versionAll", versionAllOld, versionAll);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns)) return false;
		else {
			br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns usuarioCadsusCns = (br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns) obj;
			if (null == this.getCodigo() || null == usuarioCadsusCns.getCodigo()) return false;
			else return (this.getCodigo().equals(usuarioCadsusCns.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}