package br.com.ksisolucoes.vo.vigilancia.investigacao.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the investigacao_agr_febre_maculosa_rickettsioses table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agr_febre_maculosa_rickettsioses"
 */

public abstract class BaseInvestigacaoAgravoFebreMaculosaOutrasRickettsioses extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravoFebreMaculosaOutrasRickettsioses";
	public static final String PROP_SITUACAO_RISCO_BOVINOS = "situacaoRiscoBovinos";
	public static final String PROP_SINTOMA_HIPEREMIA_CONJUTIVAL = "sintomaHiperemiaConjutival";
	public static final String PROP_SOROLOGIA_IGM_S2_TITULO = "sorologiaIgmS2Titulo";
	public static final String PROP_SINTOMA_CEFALEIA = "sintomaCefaleia";
	public static final String PROP_BAIRRO_LOCAL_INFECCAO = "bairroLocalInfeccao";
	public static final String PROP_SINTOMA_OUTROS = "sintomaOutros";
	public static final String PROP_DIAGNOSTICO_ESPECIFICADO = "diagnosticoEspecificado";
	public static final String PROP_SINTOMA_PETEQUIAS = "sintomaPetequias";
	public static final String PROP_SITUACAO_RISCO_EQUINOS = "situacaoRiscoEquinos";
	public static final String PROP_SINTOMA_SUFUSAO_HEMORRAGICA = "sintomaSufusaoHemorragica";
	public static final String PROP_DATA_ENCERRAMENTO = "dataEncerramento";
	public static final String PROP_CASO_AUTOCTONE = "casoAutoctone";
	public static final String PROP_SOROLOGIA_IGG_S1_S_N = "sorologiaIggS1SN";
	public static final String PROP_CRITERIO_CONFIRMACAO_DESCARTE = "criterioConfirmacaoDescarte";
	public static final String PROP_DATA_OBITO = "dataObito";
	public static final String PROP_SOROLOGIA_IGG_S2_TITULO = "sorologiaIggS2Titulo";
	public static final String PROP_ISOLAMENTO_AGENTE = "isolamentoAgente";
	public static final String PROP_SINTOMA_CHOQUE_HIPOTENSAO = "sintomaChoqueHipotensao";
	public static final String PROP_HOSPITAL = "hospital";
	public static final String PROP_CIDADE_LOCAL_INFECCAO = "cidadeLocalInfeccao";
	public static final String PROP_AMBIENTE_FLORESTA = "ambienteFloresta";
	public static final String PROP_SINTOMA_FEBRE = "sintomaFebre";
	public static final String PROP_LOCAL_PROVAVEL_INFECCAO_ZONA = "localProvavelInfeccaoZona";
	public static final String PROP_SINTOMA_EXANTEMA = "sintomaExantema";
	public static final String PROP_LOCAL_PROVAVEL_INFECCAO_AMBIENTE = "localProvavelInfeccaoAmbiente";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_CLASSIFICACAO_FINAL = "classificacaoFinal";
	public static final String PROP_SINTOMA_DIARREIA = "sintomaDiarreia";
	public static final String PROP_SINTOMA_ESTUPOR_COMA = "sintomaEstuporComa";
	public static final String PROP_SINTOMA_PROSTRACAO = "sintomaProstracao";
	public static final String PROP_EVOLUCAO_CASO = "evolucaoCaso";
	public static final String PROP_DATA_ALTA = "dataAlta";
	public static final String PROP_SOROLOGIA_IGM_S2_S_N = "sorologiaIgmS2SN";
	public static final String PROP_SINTOMA_DOR_ABDOMINAL = "sintomaDorAbdominal";
	public static final String PROP_DATA_INVESTIGACAO = "dataInvestigacao";
	public static final String PROP_SINTOMA_OLIGURIA_ANURIA = "sintomaOliguriaAnuria";
	public static final String PROP_SINTOMA_NECROSE_EXTREMIDADES = "sintomaNecroseExtremidades";
	public static final String PROP_SINTOMA_CONVULSAO = "sintomaConvulsao";
	public static final String PROP_SINTOMA_MANIFESTACOES_HEMORRAGICAS = "sintomaManifestacoesHemorragicas";
	public static final String PROP_ISOLAMENTO_DATA = "IsolamentoData";
	public static final String PROP_USUARIO_ENCERRAMENTO = "usuarioEncerramento";
	public static final String PROP_DIAGNOSTICO_LABORATORIAL = "diagnosticoLaboratorial";
	public static final String PROP_SITUACAO_RISCO_CAPIVARA = "situacaoRiscoCapivara";
	public static final String PROP_FLAG_INFORMACOES_COMPLEMENTARES = "flagInformacoesComplementares";
	public static final String PROP_SINTOMA_MIALGIA = "sintomaMialgia";
	public static final String PROP_DISTRITO_LOCAL_INFECCAO = "distritoLocalInfeccao";
	public static final String PROP_HISTOPATOLOGIA_RESULTADO = "histopatologiaResultado";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_SOROLOGIA_IGM_S1_TITULO = "sorologiaIgmS1Titulo";
	public static final String PROP_SINTOMA_ICTERICIA = "sintomaIctericia";
	public static final String PROP_SINTOMA_LINFADENOPATIA = "sintomaLinfadenopatia";
	public static final String PROP_SINTOMA_NAUSEA_VOMITO = "sintomaNauseaVomito";
	public static final String PROP_SITUACAO_RISCO_CAO_GATO = "situacaoRiscoCaoGato";
	public static final String PROP_ISOLAMENTO_RESULTADO = "isolamentoResultado";
	public static final String PROP_SINTOMA_ALTERACOES_RESPIRATORIAS = "sintomaAlteracoesRespiratorias";
	public static final String PROP_SOROLOGIA_IGG_S2_S_N = "sorologiaIggS2SN";
	public static final String PROP_HOSPITALIZACAO = "hospitalizacao";
	public static final String PROP_REGISTRO_AGRAVO = "registroAgravo";
	public static final String PROP_OCUPACAO_CBO = "ocupacaoCbo";
	public static final String PROP_SOROLOGIA_DATA_COLETA_S2 = "sorologiaDataColetaS2";
	public static final String PROP_IMUNOHISTOQUIMICA_RESULTADO = "imunohistoquimicaResultado";
	public static final String PROP_SITUACAO_RISCO_OUTROS = "situacaoRiscoOutros";
	public static final String PROP_SOROLOGIA_DATA_COLETA_S1 = "sorologiaDataColetaS1";
	public static final String PROP_SITUACAO_RISCO_CARRAPATO = "situacaoRiscoCarrapato";
	public static final String PROP_SINTOMA_HEPATOMAGALIA_ESPLENOMEGALIA = "sintomaHepatomagaliaEsplenomegalia";
	public static final String PROP_SOROLOGIA_IGM_S1_S_N = "sorologiaIgmS1SN";
	public static final String PROP_PAIS_LOCAL_INFECCAO = "paisLocalInfeccao";
	public static final String PROP_DATA_INTERNACAO = "dataInternacao";
	public static final String PROP_SOROLOGIA_IGG_S1_TITULO = "sorologiaIggS1Titulo";
	public static final String PROP_RELACIONADO_TRABALHO = "relacionadoTrabalho";


	// constructors
	public BaseInvestigacaoAgravoFebreMaculosaOutrasRickettsioses () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravoFebreMaculosaOutrasRickettsioses (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseInvestigacaoAgravoFebreMaculosaOutrasRickettsioses (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
		br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo,
		java.lang.String flagInformacoesComplementares) {

		this.setCodigo(codigo);
		this.setRegistroAgravo(registroAgravo);
		this.setOcupacaoCbo(ocupacaoCbo);
		this.setFlagInformacoesComplementares(flagInformacoesComplementares);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String flagInformacoesComplementares;
	private java.util.Date dataInvestigacao;
	private java.lang.Long sintomaFebre;
	private java.lang.Long sintomaNauseaVomito;
	private java.lang.Long sintomaHiperemiaConjutival;
	private java.lang.Long sintomaLinfadenopatia;
	private java.lang.Long sintomaChoqueHipotensao;
	private java.lang.Long sintomaOliguriaAnuria;
	private java.lang.Long sintomaCefaleia;
	private java.lang.Long sintomaExantema;
	private java.lang.Long sintomaHepatomagaliaEsplenomegalia;
	private java.lang.Long sintomaConvulsao;
	private java.lang.Long sintomaEstuporComa;
	private java.lang.Long sintomaDorAbdominal;
	private java.lang.Long sintomaDiarreia;
	private java.lang.Long sintomaPetequias;
	private java.lang.Long sintomaNecroseExtremidades;
	private java.lang.Long sintomaSufusaoHemorragica;
	private java.lang.Long sintomaMialgia;
	private java.lang.Long sintomaIctericia;
	private java.lang.Long sintomaManifestacoesHemorragicas;
	private java.lang.Long sintomaProstracao;
	private java.lang.Long sintomaAlteracoesRespiratorias;
	private java.lang.String sintomaOutros;
	private java.lang.Long situacaoRiscoCarrapato;
	private java.lang.Long situacaoRiscoCapivara;
	private java.lang.Long situacaoRiscoCaoGato;
	private java.lang.Long situacaoRiscoBovinos;
	private java.lang.Long situacaoRiscoEquinos;
	private java.lang.String situacaoRiscoOutros;
	private java.lang.Long ambienteFloresta;
	private java.lang.Long hospitalizacao;
	private java.util.Date dataInternacao;
	private java.util.Date dataAlta;
	private java.lang.Long diagnosticoLaboratorial;
	private java.util.Date sorologiaDataColetaS1;
	private java.lang.Long sorologiaIgmS1SN;
	private java.lang.String sorologiaIgmS1Titulo;
	private java.lang.Long sorologiaIggS1SN;
	private java.lang.String sorologiaIggS1Titulo;
	private java.util.Date sorologiaDataColetaS2;
	private java.lang.Long sorologiaIgmS2SN;
	private java.lang.String sorologiaIgmS2Titulo;
	private java.lang.Long sorologiaIggS2SN;
	private java.lang.String sorologiaIggS2Titulo;
	private java.util.Date isolamentoData;
	private java.lang.Long isolamentoResultado;
	private java.lang.String isolamentoAgente;
	private java.lang.Long histopatologiaResultado;
	private java.lang.Long imunohistoquimicaResultado;
	private java.lang.Long casoAutoctone;
	private java.lang.String distritoLocalInfeccao;
	private java.lang.String bairroLocalInfeccao;
	private java.lang.Long localProvavelInfeccaoZona;
	private java.lang.Long localProvavelInfeccaoAmbiente;
	private java.lang.Long classificacaoFinal;
	private java.lang.Long criterioConfirmacaoDescarte;
	private java.lang.String diagnosticoEspecificado;
	private java.lang.Long relacionadoTrabalho;
	private java.lang.Long evolucaoCaso;
	private java.util.Date dataObito;
	private java.lang.String observacao;
	private java.util.Date dataEncerramento;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo;
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo;
	private br.com.ksisolucoes.vo.basico.Empresa hospital;
	private br.com.ksisolucoes.vo.basico.Cidade cidadeLocalInfeccao;
	private br.com.ksisolucoes.vo.basico.Pais paisLocalInfeccao;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_invest_agr_febre_maculosa_rickettsioses"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: flag_informacoes_complementares
	 */
	public java.lang.String getFlagInformacoesComplementares () {
		return getPropertyValue(this, flagInformacoesComplementares, PROP_FLAG_INFORMACOES_COMPLEMENTARES); 
	}

	/**
	 * Set the value related to the column: flag_informacoes_complementares
	 * @param flagInformacoesComplementares the flag_informacoes_complementares value
	 */
	public void setFlagInformacoesComplementares (java.lang.String flagInformacoesComplementares) {
//        java.lang.String flagInformacoesComplementaresOld = this.flagInformacoesComplementares;
		this.flagInformacoesComplementares = flagInformacoesComplementares;
//        this.getPropertyChangeSupport().firePropertyChange ("flagInformacoesComplementares", flagInformacoesComplementaresOld, flagInformacoesComplementares);
	}



	/**
	 * Return the value associated with the column: dt_investigacao
	 */
	public java.util.Date getDataInvestigacao () {
		return getPropertyValue(this, dataInvestigacao, PROP_DATA_INVESTIGACAO); 
	}

	/**
	 * Set the value related to the column: dt_investigacao
	 * @param dataInvestigacao the dt_investigacao value
	 */
	public void setDataInvestigacao (java.util.Date dataInvestigacao) {
//        java.util.Date dataInvestigacaoOld = this.dataInvestigacao;
		this.dataInvestigacao = dataInvestigacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInvestigacao", dataInvestigacaoOld, dataInvestigacao);
	}



	/**
	 * Return the value associated with the column: sintoma_febre
	 */
	public java.lang.Long getSintomaFebre () {
		return getPropertyValue(this, sintomaFebre, PROP_SINTOMA_FEBRE); 
	}

	/**
	 * Set the value related to the column: sintoma_febre
	 * @param sintomaFebre the sintoma_febre value
	 */
	public void setSintomaFebre (java.lang.Long sintomaFebre) {
//        java.lang.Long sintomaFebreOld = this.sintomaFebre;
		this.sintomaFebre = sintomaFebre;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomaFebre", sintomaFebreOld, sintomaFebre);
	}



	/**
	 * Return the value associated with the column: sintoma_nausea_vomito
	 */
	public java.lang.Long getSintomaNauseaVomito () {
		return getPropertyValue(this, sintomaNauseaVomito, PROP_SINTOMA_NAUSEA_VOMITO); 
	}

	/**
	 * Set the value related to the column: sintoma_nausea_vomito
	 * @param sintomaNauseaVomito the sintoma_nausea_vomito value
	 */
	public void setSintomaNauseaVomito (java.lang.Long sintomaNauseaVomito) {
//        java.lang.Long sintomaNauseaVomitoOld = this.sintomaNauseaVomito;
		this.sintomaNauseaVomito = sintomaNauseaVomito;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomaNauseaVomito", sintomaNauseaVomitoOld, sintomaNauseaVomito);
	}



	/**
	 * Return the value associated with the column: sintoma_hiperemia_conjuntival
	 */
	public java.lang.Long getSintomaHiperemiaConjutival () {
		return getPropertyValue(this, sintomaHiperemiaConjutival, PROP_SINTOMA_HIPEREMIA_CONJUTIVAL); 
	}

	/**
	 * Set the value related to the column: sintoma_hiperemia_conjuntival
	 * @param sintomaHiperemiaConjutival the sintoma_hiperemia_conjuntival value
	 */
	public void setSintomaHiperemiaConjutival (java.lang.Long sintomaHiperemiaConjutival) {
//        java.lang.Long sintomaHiperemiaConjutivalOld = this.sintomaHiperemiaConjutival;
		this.sintomaHiperemiaConjutival = sintomaHiperemiaConjutival;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomaHiperemiaConjutival", sintomaHiperemiaConjutivalOld, sintomaHiperemiaConjutival);
	}



	/**
	 * Return the value associated with the column: sintoma_linfadenopatia
	 */
	public java.lang.Long getSintomaLinfadenopatia () {
		return getPropertyValue(this, sintomaLinfadenopatia, PROP_SINTOMA_LINFADENOPATIA); 
	}

	/**
	 * Set the value related to the column: sintoma_linfadenopatia
	 * @param sintomaLinfadenopatia the sintoma_linfadenopatia value
	 */
	public void setSintomaLinfadenopatia (java.lang.Long sintomaLinfadenopatia) {
//        java.lang.Long sintomaLinfadenopatiaOld = this.sintomaLinfadenopatia;
		this.sintomaLinfadenopatia = sintomaLinfadenopatia;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomaLinfadenopatia", sintomaLinfadenopatiaOld, sintomaLinfadenopatia);
	}



	/**
	 * Return the value associated with the column: sintoma_choque_hipotensao
	 */
	public java.lang.Long getSintomaChoqueHipotensao () {
		return getPropertyValue(this, sintomaChoqueHipotensao, PROP_SINTOMA_CHOQUE_HIPOTENSAO); 
	}

	/**
	 * Set the value related to the column: sintoma_choque_hipotensao
	 * @param sintomaChoqueHipotensao the sintoma_choque_hipotensao value
	 */
	public void setSintomaChoqueHipotensao (java.lang.Long sintomaChoqueHipotensao) {
//        java.lang.Long sintomaChoqueHipotensaoOld = this.sintomaChoqueHipotensao;
		this.sintomaChoqueHipotensao = sintomaChoqueHipotensao;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomaChoqueHipotensao", sintomaChoqueHipotensaoOld, sintomaChoqueHipotensao);
	}



	/**
	 * Return the value associated with the column: sintoma_oliguria_anuria
	 */
	public java.lang.Long getSintomaOliguriaAnuria () {
		return getPropertyValue(this, sintomaOliguriaAnuria, PROP_SINTOMA_OLIGURIA_ANURIA); 
	}

	/**
	 * Set the value related to the column: sintoma_oliguria_anuria
	 * @param sintomaOliguriaAnuria the sintoma_oliguria_anuria value
	 */
	public void setSintomaOliguriaAnuria (java.lang.Long sintomaOliguriaAnuria) {
//        java.lang.Long sintomaOliguriaAnuriaOld = this.sintomaOliguriaAnuria;
		this.sintomaOliguriaAnuria = sintomaOliguriaAnuria;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomaOliguriaAnuria", sintomaOliguriaAnuriaOld, sintomaOliguriaAnuria);
	}



	/**
	 * Return the value associated with the column: sintoma_cefaleia
	 */
	public java.lang.Long getSintomaCefaleia () {
		return getPropertyValue(this, sintomaCefaleia, PROP_SINTOMA_CEFALEIA); 
	}

	/**
	 * Set the value related to the column: sintoma_cefaleia
	 * @param sintomaCefaleia the sintoma_cefaleia value
	 */
	public void setSintomaCefaleia (java.lang.Long sintomaCefaleia) {
//        java.lang.Long sintomaCefaleiaOld = this.sintomaCefaleia;
		this.sintomaCefaleia = sintomaCefaleia;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomaCefaleia", sintomaCefaleiaOld, sintomaCefaleia);
	}



	/**
	 * Return the value associated with the column: sintoma_exantema
	 */
	public java.lang.Long getSintomaExantema () {
		return getPropertyValue(this, sintomaExantema, PROP_SINTOMA_EXANTEMA); 
	}

	/**
	 * Set the value related to the column: sintoma_exantema
	 * @param sintomaExantema the sintoma_exantema value
	 */
	public void setSintomaExantema (java.lang.Long sintomaExantema) {
//        java.lang.Long sintomaExantemaOld = this.sintomaExantema;
		this.sintomaExantema = sintomaExantema;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomaExantema", sintomaExantemaOld, sintomaExantema);
	}



	/**
	 * Return the value associated with the column: sintoma_hepatomegalia_esplenomegalia
	 */
	public java.lang.Long getSintomaHepatomagaliaEsplenomegalia () {
		return getPropertyValue(this, sintomaHepatomagaliaEsplenomegalia, PROP_SINTOMA_HEPATOMAGALIA_ESPLENOMEGALIA); 
	}

	/**
	 * Set the value related to the column: sintoma_hepatomegalia_esplenomegalia
	 * @param sintomaHepatomagaliaEsplenomegalia the sintoma_hepatomegalia_esplenomegalia value
	 */
	public void setSintomaHepatomagaliaEsplenomegalia (java.lang.Long sintomaHepatomagaliaEsplenomegalia) {
//        java.lang.Long sintomaHepatomagaliaEsplenomegaliaOld = this.sintomaHepatomagaliaEsplenomegalia;
		this.sintomaHepatomagaliaEsplenomegalia = sintomaHepatomagaliaEsplenomegalia;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomaHepatomagaliaEsplenomegalia", sintomaHepatomagaliaEsplenomegaliaOld, sintomaHepatomagaliaEsplenomegalia);
	}



	/**
	 * Return the value associated with the column: sintoma_convulsao
	 */
	public java.lang.Long getSintomaConvulsao () {
		return getPropertyValue(this, sintomaConvulsao, PROP_SINTOMA_CONVULSAO); 
	}

	/**
	 * Set the value related to the column: sintoma_convulsao
	 * @param sintomaConvulsao the sintoma_convulsao value
	 */
	public void setSintomaConvulsao (java.lang.Long sintomaConvulsao) {
//        java.lang.Long sintomaConvulsaoOld = this.sintomaConvulsao;
		this.sintomaConvulsao = sintomaConvulsao;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomaConvulsao", sintomaConvulsaoOld, sintomaConvulsao);
	}



	/**
	 * Return the value associated with the column: sintoma_estupor_coma
	 */
	public java.lang.Long getSintomaEstuporComa () {
		return getPropertyValue(this, sintomaEstuporComa, PROP_SINTOMA_ESTUPOR_COMA); 
	}

	/**
	 * Set the value related to the column: sintoma_estupor_coma
	 * @param sintomaEstuporComa the sintoma_estupor_coma value
	 */
	public void setSintomaEstuporComa (java.lang.Long sintomaEstuporComa) {
//        java.lang.Long sintomaEstuporComaOld = this.sintomaEstuporComa;
		this.sintomaEstuporComa = sintomaEstuporComa;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomaEstuporComa", sintomaEstuporComaOld, sintomaEstuporComa);
	}



	/**
	 * Return the value associated with the column: sintoma_dor_abdominal
	 */
	public java.lang.Long getSintomaDorAbdominal () {
		return getPropertyValue(this, sintomaDorAbdominal, PROP_SINTOMA_DOR_ABDOMINAL); 
	}

	/**
	 * Set the value related to the column: sintoma_dor_abdominal
	 * @param sintomaDorAbdominal the sintoma_dor_abdominal value
	 */
	public void setSintomaDorAbdominal (java.lang.Long sintomaDorAbdominal) {
//        java.lang.Long sintomaDorAbdominalOld = this.sintomaDorAbdominal;
		this.sintomaDorAbdominal = sintomaDorAbdominal;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomaDorAbdominal", sintomaDorAbdominalOld, sintomaDorAbdominal);
	}



	/**
	 * Return the value associated with the column: sintoma_diarreia
	 */
	public java.lang.Long getSintomaDiarreia () {
		return getPropertyValue(this, sintomaDiarreia, PROP_SINTOMA_DIARREIA); 
	}

	/**
	 * Set the value related to the column: sintoma_diarreia
	 * @param sintomaDiarreia the sintoma_diarreia value
	 */
	public void setSintomaDiarreia (java.lang.Long sintomaDiarreia) {
//        java.lang.Long sintomaDiarreiaOld = this.sintomaDiarreia;
		this.sintomaDiarreia = sintomaDiarreia;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomaDiarreia", sintomaDiarreiaOld, sintomaDiarreia);
	}



	/**
	 * Return the value associated with the column: sintoma_petequias
	 */
	public java.lang.Long getSintomaPetequias () {
		return getPropertyValue(this, sintomaPetequias, PROP_SINTOMA_PETEQUIAS); 
	}

	/**
	 * Set the value related to the column: sintoma_petequias
	 * @param sintomaPetequias the sintoma_petequias value
	 */
	public void setSintomaPetequias (java.lang.Long sintomaPetequias) {
//        java.lang.Long sintomaPetequiasOld = this.sintomaPetequias;
		this.sintomaPetequias = sintomaPetequias;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomaPetequias", sintomaPetequiasOld, sintomaPetequias);
	}



	/**
	 * Return the value associated with the column: sintoma_necrose_extremidades
	 */
	public java.lang.Long getSintomaNecroseExtremidades () {
		return getPropertyValue(this, sintomaNecroseExtremidades, PROP_SINTOMA_NECROSE_EXTREMIDADES); 
	}

	/**
	 * Set the value related to the column: sintoma_necrose_extremidades
	 * @param sintomaNecroseExtremidades the sintoma_necrose_extremidades value
	 */
	public void setSintomaNecroseExtremidades (java.lang.Long sintomaNecroseExtremidades) {
//        java.lang.Long sintomaNecroseExtremidadesOld = this.sintomaNecroseExtremidades;
		this.sintomaNecroseExtremidades = sintomaNecroseExtremidades;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomaNecroseExtremidades", sintomaNecroseExtremidadesOld, sintomaNecroseExtremidades);
	}



	/**
	 * Return the value associated with the column: sintoma_sufusao_hemorragica
	 */
	public java.lang.Long getSintomaSufusaoHemorragica () {
		return getPropertyValue(this, sintomaSufusaoHemorragica, PROP_SINTOMA_SUFUSAO_HEMORRAGICA); 
	}

	/**
	 * Set the value related to the column: sintoma_sufusao_hemorragica
	 * @param sintomaSufusaoHemorragica the sintoma_sufusao_hemorragica value
	 */
	public void setSintomaSufusaoHemorragica (java.lang.Long sintomaSufusaoHemorragica) {
//        java.lang.Long sintomaSufusaoHemorragicaOld = this.sintomaSufusaoHemorragica;
		this.sintomaSufusaoHemorragica = sintomaSufusaoHemorragica;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomaSufusaoHemorragica", sintomaSufusaoHemorragicaOld, sintomaSufusaoHemorragica);
	}



	/**
	 * Return the value associated with the column: sintoma_mialgia
	 */
	public java.lang.Long getSintomaMialgia () {
		return getPropertyValue(this, sintomaMialgia, PROP_SINTOMA_MIALGIA); 
	}

	/**
	 * Set the value related to the column: sintoma_mialgia
	 * @param sintomaMialgia the sintoma_mialgia value
	 */
	public void setSintomaMialgia (java.lang.Long sintomaMialgia) {
//        java.lang.Long sintomaMialgiaOld = this.sintomaMialgia;
		this.sintomaMialgia = sintomaMialgia;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomaMialgia", sintomaMialgiaOld, sintomaMialgia);
	}



	/**
	 * Return the value associated with the column: sintoma_ictericia
	 */
	public java.lang.Long getSintomaIctericia () {
		return getPropertyValue(this, sintomaIctericia, PROP_SINTOMA_ICTERICIA); 
	}

	/**
	 * Set the value related to the column: sintoma_ictericia
	 * @param sintomaIctericia the sintoma_ictericia value
	 */
	public void setSintomaIctericia (java.lang.Long sintomaIctericia) {
//        java.lang.Long sintomaIctericiaOld = this.sintomaIctericia;
		this.sintomaIctericia = sintomaIctericia;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomaIctericia", sintomaIctericiaOld, sintomaIctericia);
	}



	/**
	 * Return the value associated with the column: sintoma_manifestacoes_hemorragicas
	 */
	public java.lang.Long getSintomaManifestacoesHemorragicas () {
		return getPropertyValue(this, sintomaManifestacoesHemorragicas, PROP_SINTOMA_MANIFESTACOES_HEMORRAGICAS); 
	}

	/**
	 * Set the value related to the column: sintoma_manifestacoes_hemorragicas
	 * @param sintomaManifestacoesHemorragicas the sintoma_manifestacoes_hemorragicas value
	 */
	public void setSintomaManifestacoesHemorragicas (java.lang.Long sintomaManifestacoesHemorragicas) {
//        java.lang.Long sintomaManifestacoesHemorragicasOld = this.sintomaManifestacoesHemorragicas;
		this.sintomaManifestacoesHemorragicas = sintomaManifestacoesHemorragicas;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomaManifestacoesHemorragicas", sintomaManifestacoesHemorragicasOld, sintomaManifestacoesHemorragicas);
	}



	/**
	 * Return the value associated with the column: sintoma_prostracao
	 */
	public java.lang.Long getSintomaProstracao () {
		return getPropertyValue(this, sintomaProstracao, PROP_SINTOMA_PROSTRACAO); 
	}

	/**
	 * Set the value related to the column: sintoma_prostracao
	 * @param sintomaProstracao the sintoma_prostracao value
	 */
	public void setSintomaProstracao (java.lang.Long sintomaProstracao) {
//        java.lang.Long sintomaProstracaoOld = this.sintomaProstracao;
		this.sintomaProstracao = sintomaProstracao;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomaProstracao", sintomaProstracaoOld, sintomaProstracao);
	}



	/**
	 * Return the value associated with the column: sintoma_alteracoes_respiratorias
	 */
	public java.lang.Long getSintomaAlteracoesRespiratorias () {
		return getPropertyValue(this, sintomaAlteracoesRespiratorias, PROP_SINTOMA_ALTERACOES_RESPIRATORIAS); 
	}

	/**
	 * Set the value related to the column: sintoma_alteracoes_respiratorias
	 * @param sintomaAlteracoesRespiratorias the sintoma_alteracoes_respiratorias value
	 */
	public void setSintomaAlteracoesRespiratorias (java.lang.Long sintomaAlteracoesRespiratorias) {
//        java.lang.Long sintomaAlteracoesRespiratoriasOld = this.sintomaAlteracoesRespiratorias;
		this.sintomaAlteracoesRespiratorias = sintomaAlteracoesRespiratorias;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomaAlteracoesRespiratorias", sintomaAlteracoesRespiratoriasOld, sintomaAlteracoesRespiratorias);
	}



	/**
	 * Return the value associated with the column: sintoma_outros
	 */
	public java.lang.String getSintomaOutros () {
		return getPropertyValue(this, sintomaOutros, PROP_SINTOMA_OUTROS); 
	}

	/**
	 * Set the value related to the column: sintoma_outros
	 * @param sintomaOutros the sintoma_outros value
	 */
	public void setSintomaOutros (java.lang.String sintomaOutros) {
//        java.lang.String sintomaOutrosOld = this.sintomaOutros;
		this.sintomaOutros = sintomaOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomaOutros", sintomaOutrosOld, sintomaOutros);
	}



	/**
	 * Return the value associated with the column: situacao_risco_carrapato
	 */
	public java.lang.Long getSituacaoRiscoCarrapato () {
		return getPropertyValue(this, situacaoRiscoCarrapato, PROP_SITUACAO_RISCO_CARRAPATO); 
	}

	/**
	 * Set the value related to the column: situacao_risco_carrapato
	 * @param situacaoRiscoCarrapato the situacao_risco_carrapato value
	 */
	public void setSituacaoRiscoCarrapato (java.lang.Long situacaoRiscoCarrapato) {
//        java.lang.Long situacaoRiscoCarrapatoOld = this.situacaoRiscoCarrapato;
		this.situacaoRiscoCarrapato = situacaoRiscoCarrapato;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoCarrapato", situacaoRiscoCarrapatoOld, situacaoRiscoCarrapato);
	}



	/**
	 * Return the value associated with the column: situacao_risco_capivara
	 */
	public java.lang.Long getSituacaoRiscoCapivara () {
		return getPropertyValue(this, situacaoRiscoCapivara, PROP_SITUACAO_RISCO_CAPIVARA); 
	}

	/**
	 * Set the value related to the column: situacao_risco_capivara
	 * @param situacaoRiscoCapivara the situacao_risco_capivara value
	 */
	public void setSituacaoRiscoCapivara (java.lang.Long situacaoRiscoCapivara) {
//        java.lang.Long situacaoRiscoCapivaraOld = this.situacaoRiscoCapivara;
		this.situacaoRiscoCapivara = situacaoRiscoCapivara;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoCapivara", situacaoRiscoCapivaraOld, situacaoRiscoCapivara);
	}



	/**
	 * Return the value associated with the column: situacao_risco_cao_gato
	 */
	public java.lang.Long getSituacaoRiscoCaoGato () {
		return getPropertyValue(this, situacaoRiscoCaoGato, PROP_SITUACAO_RISCO_CAO_GATO); 
	}

	/**
	 * Set the value related to the column: situacao_risco_cao_gato
	 * @param situacaoRiscoCaoGato the situacao_risco_cao_gato value
	 */
	public void setSituacaoRiscoCaoGato (java.lang.Long situacaoRiscoCaoGato) {
//        java.lang.Long situacaoRiscoCaoGatoOld = this.situacaoRiscoCaoGato;
		this.situacaoRiscoCaoGato = situacaoRiscoCaoGato;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoCaoGato", situacaoRiscoCaoGatoOld, situacaoRiscoCaoGato);
	}



	/**
	 * Return the value associated with the column: situacao_risco_bovinos
	 */
	public java.lang.Long getSituacaoRiscoBovinos () {
		return getPropertyValue(this, situacaoRiscoBovinos, PROP_SITUACAO_RISCO_BOVINOS); 
	}

	/**
	 * Set the value related to the column: situacao_risco_bovinos
	 * @param situacaoRiscoBovinos the situacao_risco_bovinos value
	 */
	public void setSituacaoRiscoBovinos (java.lang.Long situacaoRiscoBovinos) {
//        java.lang.Long situacaoRiscoBovinosOld = this.situacaoRiscoBovinos;
		this.situacaoRiscoBovinos = situacaoRiscoBovinos;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoBovinos", situacaoRiscoBovinosOld, situacaoRiscoBovinos);
	}



	/**
	 * Return the value associated with the column: situacao_risco_equinos
	 */
	public java.lang.Long getSituacaoRiscoEquinos () {
		return getPropertyValue(this, situacaoRiscoEquinos, PROP_SITUACAO_RISCO_EQUINOS); 
	}

	/**
	 * Set the value related to the column: situacao_risco_equinos
	 * @param situacaoRiscoEquinos the situacao_risco_equinos value
	 */
	public void setSituacaoRiscoEquinos (java.lang.Long situacaoRiscoEquinos) {
//        java.lang.Long situacaoRiscoEquinosOld = this.situacaoRiscoEquinos;
		this.situacaoRiscoEquinos = situacaoRiscoEquinos;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoEquinos", situacaoRiscoEquinosOld, situacaoRiscoEquinos);
	}



	/**
	 * Return the value associated with the column: situacao_risco_outros
	 */
	public java.lang.String getSituacaoRiscoOutros () {
		return getPropertyValue(this, situacaoRiscoOutros, PROP_SITUACAO_RISCO_OUTROS); 
	}

	/**
	 * Set the value related to the column: situacao_risco_outros
	 * @param situacaoRiscoOutros the situacao_risco_outros value
	 */
	public void setSituacaoRiscoOutros (java.lang.String situacaoRiscoOutros) {
//        java.lang.String situacaoRiscoOutrosOld = this.situacaoRiscoOutros;
		this.situacaoRiscoOutros = situacaoRiscoOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoOutros", situacaoRiscoOutrosOld, situacaoRiscoOutros);
	}



	/**
	 * Return the value associated with the column: ambientes_floresta
	 */
	public java.lang.Long getAmbienteFloresta () {
		return getPropertyValue(this, ambienteFloresta, PROP_AMBIENTE_FLORESTA); 
	}

	/**
	 * Set the value related to the column: ambientes_floresta
	 * @param ambienteFloresta the ambientes_floresta value
	 */
	public void setAmbienteFloresta (java.lang.Long ambienteFloresta) {
//        java.lang.Long ambienteFlorestaOld = this.ambienteFloresta;
		this.ambienteFloresta = ambienteFloresta;
//        this.getPropertyChangeSupport().firePropertyChange ("ambienteFloresta", ambienteFlorestaOld, ambienteFloresta);
	}



	/**
	 * Return the value associated with the column: hospitalizacao
	 */
	public java.lang.Long getHospitalizacao () {
		return getPropertyValue(this, hospitalizacao, PROP_HOSPITALIZACAO); 
	}

	/**
	 * Set the value related to the column: hospitalizacao
	 * @param hospitalizacao the hospitalizacao value
	 */
	public void setHospitalizacao (java.lang.Long hospitalizacao) {
//        java.lang.Long hospitalizacaoOld = this.hospitalizacao;
		this.hospitalizacao = hospitalizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("hospitalizacao", hospitalizacaoOld, hospitalizacao);
	}



	/**
	 * Return the value associated with the column: dt_internacao
	 */
	public java.util.Date getDataInternacao () {
		return getPropertyValue(this, dataInternacao, PROP_DATA_INTERNACAO); 
	}

	/**
	 * Set the value related to the column: dt_internacao
	 * @param dataInternacao the dt_internacao value
	 */
	public void setDataInternacao (java.util.Date dataInternacao) {
//        java.util.Date dataInternacaoOld = this.dataInternacao;
		this.dataInternacao = dataInternacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInternacao", dataInternacaoOld, dataInternacao);
	}



	/**
	 * Return the value associated with the column: dt_alta
	 */
	public java.util.Date getDataAlta () {
		return getPropertyValue(this, dataAlta, PROP_DATA_ALTA); 
	}

	/**
	 * Set the value related to the column: dt_alta
	 * @param dataAlta the dt_alta value
	 */
	public void setDataAlta (java.util.Date dataAlta) {
//        java.util.Date dataAltaOld = this.dataAlta;
		this.dataAlta = dataAlta;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAlta", dataAltaOld, dataAlta);
	}



	/**
	 * Return the value associated with the column: diagnostico_laboratorial_sn
	 */
	public java.lang.Long getDiagnosticoLaboratorial () {
		return getPropertyValue(this, diagnosticoLaboratorial, PROP_DIAGNOSTICO_LABORATORIAL); 
	}

	/**
	 * Set the value related to the column: diagnostico_laboratorial_sn
	 * @param diagnosticoLaboratorial the diagnostico_laboratorial_sn value
	 */
	public void setDiagnosticoLaboratorial (java.lang.Long diagnosticoLaboratorial) {
//        java.lang.Long diagnosticoLaboratorialOld = this.diagnosticoLaboratorial;
		this.diagnosticoLaboratorial = diagnosticoLaboratorial;
//        this.getPropertyChangeSupport().firePropertyChange ("diagnosticoLaboratorial", diagnosticoLaboratorialOld, diagnosticoLaboratorial);
	}



	/**
	 * Return the value associated with the column: sorologia_dt_coleta_s1
	 */
	public java.util.Date getSorologiaDataColetaS1 () {
		return getPropertyValue(this, sorologiaDataColetaS1, PROP_SOROLOGIA_DATA_COLETA_S1); 
	}

	/**
	 * Set the value related to the column: sorologia_dt_coleta_s1
	 * @param sorologiaDataColetaS1 the sorologia_dt_coleta_s1 value
	 */
	public void setSorologiaDataColetaS1 (java.util.Date sorologiaDataColetaS1) {
//        java.util.Date sorologiaDataColetaS1Old = this.sorologiaDataColetaS1;
		this.sorologiaDataColetaS1 = sorologiaDataColetaS1;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaDataColetaS1", sorologiaDataColetaS1Old, sorologiaDataColetaS1);
	}



	/**
	 * Return the value associated with the column: sorologia_igm_s1_sn
	 */
	public java.lang.Long getSorologiaIgmS1SN () {
		return getPropertyValue(this, sorologiaIgmS1SN, PROP_SOROLOGIA_IGM_S1_S_N); 
	}

	/**
	 * Set the value related to the column: sorologia_igm_s1_sn
	 * @param sorologiaIgmS1SN the sorologia_igm_s1_sn value
	 */
	public void setSorologiaIgmS1SN (java.lang.Long sorologiaIgmS1SN) {
//        java.lang.Long sorologiaIgmS1SNOld = this.sorologiaIgmS1SN;
		this.sorologiaIgmS1SN = sorologiaIgmS1SN;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaIgmS1SN", sorologiaIgmS1SNOld, sorologiaIgmS1SN);
	}



	/**
	 * Return the value associated with the column: sorologia_igm_s1_titulo
	 */
	public java.lang.String getSorologiaIgmS1Titulo () {
		return getPropertyValue(this, sorologiaIgmS1Titulo, PROP_SOROLOGIA_IGM_S1_TITULO); 
	}

	/**
	 * Set the value related to the column: sorologia_igm_s1_titulo
	 * @param sorologiaIgmS1Titulo the sorologia_igm_s1_titulo value
	 */
	public void setSorologiaIgmS1Titulo (java.lang.String sorologiaIgmS1Titulo) {
//        java.lang.String sorologiaIgmS1TituloOld = this.sorologiaIgmS1Titulo;
		this.sorologiaIgmS1Titulo = sorologiaIgmS1Titulo;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaIgmS1Titulo", sorologiaIgmS1TituloOld, sorologiaIgmS1Titulo);
	}



	/**
	 * Return the value associated with the column: sorologia_igg_s1_sn
	 */
	public java.lang.Long getSorologiaIggS1SN () {
		return getPropertyValue(this, sorologiaIggS1SN, PROP_SOROLOGIA_IGG_S1_S_N); 
	}

	/**
	 * Set the value related to the column: sorologia_igg_s1_sn
	 * @param sorologiaIggS1SN the sorologia_igg_s1_sn value
	 */
	public void setSorologiaIggS1SN (java.lang.Long sorologiaIggS1SN) {
//        java.lang.Long sorologiaIggS1SNOld = this.sorologiaIggS1SN;
		this.sorologiaIggS1SN = sorologiaIggS1SN;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaIggS1SN", sorologiaIggS1SNOld, sorologiaIggS1SN);
	}



	/**
	 * Return the value associated with the column: sorologia_igg_s1_titulo
	 */
	public java.lang.String getSorologiaIggS1Titulo () {
		return getPropertyValue(this, sorologiaIggS1Titulo, PROP_SOROLOGIA_IGG_S1_TITULO); 
	}

	/**
	 * Set the value related to the column: sorologia_igg_s1_titulo
	 * @param sorologiaIggS1Titulo the sorologia_igg_s1_titulo value
	 */
	public void setSorologiaIggS1Titulo (java.lang.String sorologiaIggS1Titulo) {
//        java.lang.String sorologiaIggS1TituloOld = this.sorologiaIggS1Titulo;
		this.sorologiaIggS1Titulo = sorologiaIggS1Titulo;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaIggS1Titulo", sorologiaIggS1TituloOld, sorologiaIggS1Titulo);
	}



	/**
	 * Return the value associated with the column: sorologia_dt_coleta_s2
	 */
	public java.util.Date getSorologiaDataColetaS2 () {
		return getPropertyValue(this, sorologiaDataColetaS2, PROP_SOROLOGIA_DATA_COLETA_S2); 
	}

	/**
	 * Set the value related to the column: sorologia_dt_coleta_s2
	 * @param sorologiaDataColetaS2 the sorologia_dt_coleta_s2 value
	 */
	public void setSorologiaDataColetaS2 (java.util.Date sorologiaDataColetaS2) {
//        java.util.Date sorologiaDataColetaS2Old = this.sorologiaDataColetaS2;
		this.sorologiaDataColetaS2 = sorologiaDataColetaS2;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaDataColetaS2", sorologiaDataColetaS2Old, sorologiaDataColetaS2);
	}



	/**
	 * Return the value associated with the column: sorologia_igm_s2_sn
	 */
	public java.lang.Long getSorologiaIgmS2SN () {
		return getPropertyValue(this, sorologiaIgmS2SN, PROP_SOROLOGIA_IGM_S2_S_N); 
	}

	/**
	 * Set the value related to the column: sorologia_igm_s2_sn
	 * @param sorologiaIgmS2SN the sorologia_igm_s2_sn value
	 */
	public void setSorologiaIgmS2SN (java.lang.Long sorologiaIgmS2SN) {
//        java.lang.Long sorologiaIgmS2SNOld = this.sorologiaIgmS2SN;
		this.sorologiaIgmS2SN = sorologiaIgmS2SN;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaIgmS2SN", sorologiaIgmS2SNOld, sorologiaIgmS2SN);
	}



	/**
	 * Return the value associated with the column: sorologia_igm_s2_titulo
	 */
	public java.lang.String getSorologiaIgmS2Titulo () {
		return getPropertyValue(this, sorologiaIgmS2Titulo, PROP_SOROLOGIA_IGM_S2_TITULO); 
	}

	/**
	 * Set the value related to the column: sorologia_igm_s2_titulo
	 * @param sorologiaIgmS2Titulo the sorologia_igm_s2_titulo value
	 */
	public void setSorologiaIgmS2Titulo (java.lang.String sorologiaIgmS2Titulo) {
//        java.lang.String sorologiaIgmS2TituloOld = this.sorologiaIgmS2Titulo;
		this.sorologiaIgmS2Titulo = sorologiaIgmS2Titulo;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaIgmS2Titulo", sorologiaIgmS2TituloOld, sorologiaIgmS2Titulo);
	}



	/**
	 * Return the value associated with the column: sorologia_igg_s2_sn
	 */
	public java.lang.Long getSorologiaIggS2SN () {
		return getPropertyValue(this, sorologiaIggS2SN, PROP_SOROLOGIA_IGG_S2_S_N); 
	}

	/**
	 * Set the value related to the column: sorologia_igg_s2_sn
	 * @param sorologiaIggS2SN the sorologia_igg_s2_sn value
	 */
	public void setSorologiaIggS2SN (java.lang.Long sorologiaIggS2SN) {
//        java.lang.Long sorologiaIggS2SNOld = this.sorologiaIggS2SN;
		this.sorologiaIggS2SN = sorologiaIggS2SN;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaIggS2SN", sorologiaIggS2SNOld, sorologiaIggS2SN);
	}



	/**
	 * Return the value associated with the column: sorologia_igg_s2_titulo
	 */
	public java.lang.String getSorologiaIggS2Titulo () {
		return getPropertyValue(this, sorologiaIggS2Titulo, PROP_SOROLOGIA_IGG_S2_TITULO); 
	}

	/**
	 * Set the value related to the column: sorologia_igg_s2_titulo
	 * @param sorologiaIggS2Titulo the sorologia_igg_s2_titulo value
	 */
	public void setSorologiaIggS2Titulo (java.lang.String sorologiaIggS2Titulo) {
//        java.lang.String sorologiaIggS2TituloOld = this.sorologiaIggS2Titulo;
		this.sorologiaIggS2Titulo = sorologiaIggS2Titulo;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaIggS2Titulo", sorologiaIggS2TituloOld, sorologiaIggS2Titulo);
	}



	/**
	 * Return the value associated with the column: isolamento_dt
	 */
	public java.util.Date getIsolamentoData () {
		return getPropertyValue(this, isolamentoData, PROP_ISOLAMENTO_DATA); 
	}

	/**
	 * Set the value related to the column: isolamento_dt
	 * @param isolamentoData the isolamento_dt value
	 */
	public void setIsolamentoData (java.util.Date isolamentoData) {
//        java.util.Date isolamentoDataOld = this.isolamentoData;
		this.isolamentoData = isolamentoData;
//        this.getPropertyChangeSupport().firePropertyChange ("isolamentoData", isolamentoDataOld, isolamentoData);
	}



	/**
	 * Return the value associated with the column: isolamento_resultado
	 */
	public java.lang.Long getIsolamentoResultado () {
		return getPropertyValue(this, isolamentoResultado, PROP_ISOLAMENTO_RESULTADO); 
	}

	/**
	 * Set the value related to the column: isolamento_resultado
	 * @param isolamentoResultado the isolamento_resultado value
	 */
	public void setIsolamentoResultado (java.lang.Long isolamentoResultado) {
//        java.lang.Long isolamentoResultadoOld = this.isolamentoResultado;
		this.isolamentoResultado = isolamentoResultado;
//        this.getPropertyChangeSupport().firePropertyChange ("isolamentoResultado", isolamentoResultadoOld, isolamentoResultado);
	}



	/**
	 * Return the value associated with the column: isolamento_agente
	 */
	public java.lang.String getIsolamentoAgente () {
		return getPropertyValue(this, isolamentoAgente, PROP_ISOLAMENTO_AGENTE); 
	}

	/**
	 * Set the value related to the column: isolamento_agente
	 * @param isolamentoAgente the isolamento_agente value
	 */
	public void setIsolamentoAgente (java.lang.String isolamentoAgente) {
//        java.lang.String isolamentoAgenteOld = this.isolamentoAgente;
		this.isolamentoAgente = isolamentoAgente;
//        this.getPropertyChangeSupport().firePropertyChange ("isolamentoAgente", isolamentoAgenteOld, isolamentoAgente);
	}



	/**
	 * Return the value associated with the column: histopatologia_resultado
	 */
	public java.lang.Long getHistopatologiaResultado () {
		return getPropertyValue(this, histopatologiaResultado, PROP_HISTOPATOLOGIA_RESULTADO); 
	}

	/**
	 * Set the value related to the column: histopatologia_resultado
	 * @param histopatologiaResultado the histopatologia_resultado value
	 */
	public void setHistopatologiaResultado (java.lang.Long histopatologiaResultado) {
//        java.lang.Long histopatologiaResultadoOld = this.histopatologiaResultado;
		this.histopatologiaResultado = histopatologiaResultado;
//        this.getPropertyChangeSupport().firePropertyChange ("histopatologiaResultado", histopatologiaResultadoOld, histopatologiaResultado);
	}



	/**
	 * Return the value associated with the column: imunohistoquimica_resultado
	 */
	public java.lang.Long getImunohistoquimicaResultado () {
		return getPropertyValue(this, imunohistoquimicaResultado, PROP_IMUNOHISTOQUIMICA_RESULTADO); 
	}

	/**
	 * Set the value related to the column: imunohistoquimica_resultado
	 * @param imunohistoquimicaResultado the imunohistoquimica_resultado value
	 */
	public void setImunohistoquimicaResultado (java.lang.Long imunohistoquimicaResultado) {
//        java.lang.Long imunohistoquimicaResultadoOld = this.imunohistoquimicaResultado;
		this.imunohistoquimicaResultado = imunohistoquimicaResultado;
//        this.getPropertyChangeSupport().firePropertyChange ("imunohistoquimicaResultado", imunohistoquimicaResultadoOld, imunohistoquimicaResultado);
	}



	/**
	 * Return the value associated with the column: caso_autoctone
	 */
	public java.lang.Long getCasoAutoctone () {
		return getPropertyValue(this, casoAutoctone, PROP_CASO_AUTOCTONE); 
	}

	/**
	 * Set the value related to the column: caso_autoctone
	 * @param casoAutoctone the caso_autoctone value
	 */
	public void setCasoAutoctone (java.lang.Long casoAutoctone) {
//        java.lang.Long casoAutoctoneOld = this.casoAutoctone;
		this.casoAutoctone = casoAutoctone;
//        this.getPropertyChangeSupport().firePropertyChange ("casoAutoctone", casoAutoctoneOld, casoAutoctone);
	}



	/**
	 * Return the value associated with the column: str_distrito_infeccao
	 */
	public java.lang.String getDistritoLocalInfeccao () {
		return getPropertyValue(this, distritoLocalInfeccao, PROP_DISTRITO_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: str_distrito_infeccao
	 * @param distritoLocalInfeccao the str_distrito_infeccao value
	 */
	public void setDistritoLocalInfeccao (java.lang.String distritoLocalInfeccao) {
//        java.lang.String distritoLocalInfeccaoOld = this.distritoLocalInfeccao;
		this.distritoLocalInfeccao = distritoLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("distritoLocalInfeccao", distritoLocalInfeccaoOld, distritoLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: str_bairro_infeccao
	 */
	public java.lang.String getBairroLocalInfeccao () {
		return getPropertyValue(this, bairroLocalInfeccao, PROP_BAIRRO_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: str_bairro_infeccao
	 * @param bairroLocalInfeccao the str_bairro_infeccao value
	 */
	public void setBairroLocalInfeccao (java.lang.String bairroLocalInfeccao) {
//        java.lang.String bairroLocalInfeccaoOld = this.bairroLocalInfeccao;
		this.bairroLocalInfeccao = bairroLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("bairroLocalInfeccao", bairroLocalInfeccaoOld, bairroLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: local_provavel_infeccao_zona
	 */
	public java.lang.Long getLocalProvavelInfeccaoZona () {
		return getPropertyValue(this, localProvavelInfeccaoZona, PROP_LOCAL_PROVAVEL_INFECCAO_ZONA); 
	}

	/**
	 * Set the value related to the column: local_provavel_infeccao_zona
	 * @param localProvavelInfeccaoZona the local_provavel_infeccao_zona value
	 */
	public void setLocalProvavelInfeccaoZona (java.lang.Long localProvavelInfeccaoZona) {
//        java.lang.Long localProvavelInfeccaoZonaOld = this.localProvavelInfeccaoZona;
		this.localProvavelInfeccaoZona = localProvavelInfeccaoZona;
//        this.getPropertyChangeSupport().firePropertyChange ("localProvavelInfeccaoZona", localProvavelInfeccaoZonaOld, localProvavelInfeccaoZona);
	}



	/**
	 * Return the value associated with the column: local_provavel_infeccao_ambiente
	 */
	public java.lang.Long getLocalProvavelInfeccaoAmbiente () {
		return getPropertyValue(this, localProvavelInfeccaoAmbiente, PROP_LOCAL_PROVAVEL_INFECCAO_AMBIENTE); 
	}

	/**
	 * Set the value related to the column: local_provavel_infeccao_ambiente
	 * @param localProvavelInfeccaoAmbiente the local_provavel_infeccao_ambiente value
	 */
	public void setLocalProvavelInfeccaoAmbiente (java.lang.Long localProvavelInfeccaoAmbiente) {
//        java.lang.Long localProvavelInfeccaoAmbienteOld = this.localProvavelInfeccaoAmbiente;
		this.localProvavelInfeccaoAmbiente = localProvavelInfeccaoAmbiente;
//        this.getPropertyChangeSupport().firePropertyChange ("localProvavelInfeccaoAmbiente", localProvavelInfeccaoAmbienteOld, localProvavelInfeccaoAmbiente);
	}



	/**
	 * Return the value associated with the column: classificacao_final
	 */
	public java.lang.Long getClassificacaoFinal () {
		return getPropertyValue(this, classificacaoFinal, PROP_CLASSIFICACAO_FINAL); 
	}

	/**
	 * Set the value related to the column: classificacao_final
	 * @param classificacaoFinal the classificacao_final value
	 */
	public void setClassificacaoFinal (java.lang.Long classificacaoFinal) {
//        java.lang.Long classificacaoFinalOld = this.classificacaoFinal;
		this.classificacaoFinal = classificacaoFinal;
//        this.getPropertyChangeSupport().firePropertyChange ("classificacaoFinal", classificacaoFinalOld, classificacaoFinal);
	}



	/**
	 * Return the value associated with the column: criterio_confirmacao_descarte
	 */
	public java.lang.Long getCriterioConfirmacaoDescarte () {
		return getPropertyValue(this, criterioConfirmacaoDescarte, PROP_CRITERIO_CONFIRMACAO_DESCARTE); 
	}

	/**
	 * Set the value related to the column: criterio_confirmacao_descarte
	 * @param criterioConfirmacaoDescarte the criterio_confirmacao_descarte value
	 */
	public void setCriterioConfirmacaoDescarte (java.lang.Long criterioConfirmacaoDescarte) {
//        java.lang.Long criterioConfirmacaoDescarteOld = this.criterioConfirmacaoDescarte;
		this.criterioConfirmacaoDescarte = criterioConfirmacaoDescarte;
//        this.getPropertyChangeSupport().firePropertyChange ("criterioConfirmacaoDescarte", criterioConfirmacaoDescarteOld, criterioConfirmacaoDescarte);
	}



	/**
	 * Return the value associated with the column: diagnostico_especificado
	 */
	public java.lang.String getDiagnosticoEspecificado () {
		return getPropertyValue(this, diagnosticoEspecificado, PROP_DIAGNOSTICO_ESPECIFICADO); 
	}

	/**
	 * Set the value related to the column: diagnostico_especificado
	 * @param diagnosticoEspecificado the diagnostico_especificado value
	 */
	public void setDiagnosticoEspecificado (java.lang.String diagnosticoEspecificado) {
//        java.lang.String diagnosticoEspecificadoOld = this.diagnosticoEspecificado;
		this.diagnosticoEspecificado = diagnosticoEspecificado;
//        this.getPropertyChangeSupport().firePropertyChange ("diagnosticoEspecificado", diagnosticoEspecificadoOld, diagnosticoEspecificado);
	}



	/**
	 * Return the value associated with the column: relacionado_trabalho
	 */
	public java.lang.Long getRelacionadoTrabalho () {
		return getPropertyValue(this, relacionadoTrabalho, PROP_RELACIONADO_TRABALHO); 
	}

	/**
	 * Set the value related to the column: relacionado_trabalho
	 * @param relacionadoTrabalho the relacionado_trabalho value
	 */
	public void setRelacionadoTrabalho (java.lang.Long relacionadoTrabalho) {
//        java.lang.Long relacionadoTrabalhoOld = this.relacionadoTrabalho;
		this.relacionadoTrabalho = relacionadoTrabalho;
//        this.getPropertyChangeSupport().firePropertyChange ("relacionadoTrabalho", relacionadoTrabalhoOld, relacionadoTrabalho);
	}



	/**
	 * Return the value associated with the column: evolucao_caso
	 */
	public java.lang.Long getEvolucaoCaso () {
		return getPropertyValue(this, evolucaoCaso, PROP_EVOLUCAO_CASO); 
	}

	/**
	 * Set the value related to the column: evolucao_caso
	 * @param evolucaoCaso the evolucao_caso value
	 */
	public void setEvolucaoCaso (java.lang.Long evolucaoCaso) {
//        java.lang.Long evolucaoCasoOld = this.evolucaoCaso;
		this.evolucaoCaso = evolucaoCaso;
//        this.getPropertyChangeSupport().firePropertyChange ("evolucaoCaso", evolucaoCasoOld, evolucaoCaso);
	}



	/**
	 * Return the value associated with the column: dt_obito
	 */
	public java.util.Date getDataObito () {
		return getPropertyValue(this, dataObito, PROP_DATA_OBITO); 
	}

	/**
	 * Set the value related to the column: dt_obito
	 * @param dataObito the dt_obito value
	 */
	public void setDataObito (java.util.Date dataObito) {
//        java.util.Date dataObitoOld = this.dataObito;
		this.dataObito = dataObito;
//        this.getPropertyChangeSupport().firePropertyChange ("dataObito", dataObitoOld, dataObito);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: dt_encerramento
	 */
	public java.util.Date getDataEncerramento () {
		return getPropertyValue(this, dataEncerramento, PROP_DATA_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_encerramento
	 * @param dataEncerramento the dt_encerramento value
	 */
	public void setDataEncerramento (java.util.Date dataEncerramento) {
//        java.util.Date dataEncerramentoOld = this.dataEncerramento;
		this.dataEncerramento = dataEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEncerramento", dataEncerramentoOld, dataEncerramento);
	}



	/**
	 * Return the value associated with the column: cd_registro_agravo
	 */
	public br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo getRegistroAgravo () {
		return getPropertyValue(this, registroAgravo, PROP_REGISTRO_AGRAVO); 
	}

	/**
	 * Set the value related to the column: cd_registro_agravo
	 * @param registroAgravo the cd_registro_agravo value
	 */
	public void setRegistroAgravo (br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo) {
//        br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravoOld = this.registroAgravo;
		this.registroAgravo = registroAgravo;
//        this.getPropertyChangeSupport().firePropertyChange ("registroAgravo", registroAgravoOld, registroAgravo);
	}



	/**
	 * Return the value associated with the column: ocupacao_cbo
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getOcupacaoCbo () {
		return getPropertyValue(this, ocupacaoCbo, PROP_OCUPACAO_CBO); 
	}

	/**
	 * Set the value related to the column: ocupacao_cbo
	 * @param ocupacaoCbo the ocupacao_cbo value
	 */
	public void setOcupacaoCbo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCboOld = this.ocupacaoCbo;
		this.ocupacaoCbo = ocupacaoCbo;
//        this.getPropertyChangeSupport().firePropertyChange ("ocupacaoCbo", ocupacaoCboOld, ocupacaoCbo);
	}



	/**
	 * Return the value associated with the column: unidade_hospital
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getHospital () {
		return getPropertyValue(this, hospital, PROP_HOSPITAL); 
	}

	/**
	 * Set the value related to the column: unidade_hospital
	 * @param hospital the unidade_hospital value
	 */
	public void setHospital (br.com.ksisolucoes.vo.basico.Empresa hospital) {
//        br.com.ksisolucoes.vo.basico.Empresa hospitalOld = this.hospital;
		this.hospital = hospital;
//        this.getPropertyChangeSupport().firePropertyChange ("hospital", hospitalOld, hospital);
	}



	/**
	 * Return the value associated with the column: cd_cidade_infeccao
	 */
	public br.com.ksisolucoes.vo.basico.Cidade getCidadeLocalInfeccao () {
		return getPropertyValue(this, cidadeLocalInfeccao, PROP_CIDADE_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: cd_cidade_infeccao
	 * @param cidadeLocalInfeccao the cd_cidade_infeccao value
	 */
	public void setCidadeLocalInfeccao (br.com.ksisolucoes.vo.basico.Cidade cidadeLocalInfeccao) {
//        br.com.ksisolucoes.vo.basico.Cidade cidadeLocalInfeccaoOld = this.cidadeLocalInfeccao;
		this.cidadeLocalInfeccao = cidadeLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("cidadeLocalInfeccao", cidadeLocalInfeccaoOld, cidadeLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: cd_pais_infeccao
	 */
	public br.com.ksisolucoes.vo.basico.Pais getPaisLocalInfeccao () {
		return getPropertyValue(this, paisLocalInfeccao, PROP_PAIS_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: cd_pais_infeccao
	 * @param paisLocalInfeccao the cd_pais_infeccao value
	 */
	public void setPaisLocalInfeccao (br.com.ksisolucoes.vo.basico.Pais paisLocalInfeccao) {
//        br.com.ksisolucoes.vo.basico.Pais paisLocalInfeccaoOld = this.paisLocalInfeccao;
		this.paisLocalInfeccao = paisLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("paisLocalInfeccao", paisLocalInfeccaoOld, paisLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: cd_usuario_encerramento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioEncerramento () {
		return getPropertyValue(this, usuarioEncerramento, PROP_USUARIO_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_encerramento
	 * @param usuarioEncerramento the cd_usuario_encerramento value
	 */
	public void setUsuarioEncerramento (br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramentoOld = this.usuarioEncerramento;
		this.usuarioEncerramento = usuarioEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioEncerramento", usuarioEncerramentoOld, usuarioEncerramento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoFebreMaculosaOutrasRickettsioses)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoFebreMaculosaOutrasRickettsioses investigacaoAgravoFebreMaculosaOutrasRickettsioses = (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoFebreMaculosaOutrasRickettsioses) obj;
			if (null == this.getCodigo() || null == investigacaoAgravoFebreMaculosaOutrasRickettsioses.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravoFebreMaculosaOutrasRickettsioses.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}