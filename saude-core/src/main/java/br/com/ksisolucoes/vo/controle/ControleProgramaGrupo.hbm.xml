<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.controle"  >
	<class
		name="ControleProgramaGrupo"
		table="controle_programas_grupo"
	>
		<id
			name="codigo"
			type="java.lang.Long"
			column="cd_prg_grupo"
		>
			<generator class="assigned"/>
		</id> <version column="version" name="version" type="long" />

		<many-to-one
			name="programa"
			class="br.com.ksisolucoes.vo.controle.Programa"
			column="cd_programa"
		/>

		<many-to-one
			name="grupo"
			class="br.com.ksisolucoes.vo.controle.Grupo"
			column="cd_grupo"
		/>

		<property
			column="dt_criacao"
			name="dataCriacao"
			type="java.util.Date"
			not-null="true"
		/>
	</class>
</hibernate-mapping>