<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.dengue"  >
    <class name="RegistroDiarioAntivetorialPesquisa" table="registro_diario_antivetorial_pesq">

        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_reg_diario_vet_peq"
        >
            <generator class="assigned" />
        </id> 

        <version column="version" name="version" type="long" />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.dengue.RegistroDiarioAntivetorial"
            column="cd_reg_diario_vet"
            name="registroDiarioAntivetorial"
            not-null="true"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco"
            column="cd_endereco"
            name="endereco"
            not-null="true"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.dengue.DengueTipoImovel"
            column="cd_tipo_imovel"
            name="tipoImovel"
            not-null="true"
        />

        <property
            name="numeroLogradouro"
            column="nr_logradouro"
            type="java.lang.String"
            not-null="true"
        />

        <property
            name="sequenciaLogradouro"
            column="seq_logradouro"
            type="java.lang.String"
            not-null="false"
        />

        <property
            name="numeroQuarteirao"
            column="nr_quarteirao"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="sequenciaQuarteirao"
            column="seq_quarteirao"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="ladoQuarteirao"
            column="lado_quarteirao"
            type="java.lang.String"
            not-null="false"
        />

        <property
            name="horaEntrada"
            column="hr_entrada"
            type="timestamp"
            not-null="false"
        />

        <property
            name="pendencia"
            column="pendencia"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="depositoInspecionadoA1"
            column="dep_insp_a1"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="depositoInspecionadoA2"
            column="dep_insp_a2"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="depositoInspecionadoB"
            column="dep_insp_b"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="depositoInspecionadoC"
            column="dep_insp_c"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="depositoInspecionadoD1"
            column="dep_insp_d1"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="depositoInspecionadoD2"
            column="dep_insp_d2"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="depositoInspecionadoE"
            column="dep_insp_e"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="coletaAmostra"
            column="coleta_amostra"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="numeroAmostraInicial"
            column="nr_amostra_inicial"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="numeroAmostraFinal"
            column="nr_amostra_final"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="quantidadeTubitos"
            column="qtde_tubitos"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="depositosEliminados"
            column="dep_eliminados"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="imovelTratado"
            column="imovel_tratado"
            type="java.lang.Long"
            not-null="false"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.dengue.DengueTipoInseticida"
            column="cd_tp_inseticida_amostra"
            name="tipoInseticidaAmostra"
            not-null="true"
        />

        <property
            name="quantidadeAmostra"
            column="qtde_amostra"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="quantidadeDepTratamentoAmostra"
            column="qtde_dep_trat_amostra"
            type="java.lang.Long"
            not-null="false"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.dengue.DengueTipoInseticida"
            column="cd_tp_inseticida_larv"
            name="tipoInseticidaLarvicida"
            not-null="true"
        />

        <property
            name="quantidadeLarvicida"
            column="qtde_larv"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="quantidadeDepTratamentoLarvicida"
            column="qtde_dep_trat_larv"
            type="java.lang.Long"
            not-null="false"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.dengue.DengueTipoInseticida"
            column="cd_tp_inseticida_adulticida"
            name="tipoInseticidaAdulticida"
            not-null="true"
        />

        <property
            name="quantidadeCargasAdulticida"
            column="qtde_cargas_adulticida"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="status"
            column="status"
            type="java.lang.Long"
            not-null="true"
        />

        <property
            name="dataCadastro"
            column="dt_cadastro"
            type="timestamp"
            not-null="true"
        />

        <property
            name="dataUsuario"
            column="dt_usuario"
            type="timestamp"
            not-null="true"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuario"
            not-null="true"
        />
    </class>
</hibernate-mapping>
