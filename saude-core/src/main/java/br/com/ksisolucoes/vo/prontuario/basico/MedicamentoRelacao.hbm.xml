<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="MedicamentoRelacao" table="medicamento_relacao" >
        <id
            name="codigo"
            column="cd_medicamento_relacao"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id>
        
        <version column="version" name="version" type="long" />
		
        <many-to-one
            class="br.com.ksisolucoes.vo.entradas.estoque.Produto"
            name="produto"
            not-null="true"
            column="cod_pro"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            name="usuario"
            column="cd_usuario"
            not-null="true"
        />
        
        <property
            name="dataCadastro"
            column="dt_cadastro"
            type="timestamp"
            not-null="true"
        />
        
        <property
            type="java.lang.Long"
            name="quantidade"
            not-null="true"
            column="quantidade"
        />
        
        <property
            type="java.lang.Long"
            name="frequencia"
            not-null="true"
            column="frequencia"
        />
        
        <property
            type="java.lang.Long"
            name="intervalo"
            column="intervalo"
        />
        
        <property
            type="java.lang.Long"
            name="periodoTratamento"
            not-null="true"
            column="periodo_tratamento"
        />
        
        <property
            type="java.lang.Long"
            name="tratamentoContinuo"
            column="tratamento_continuo"
        />
    </class>
</hibernate-mapping>
