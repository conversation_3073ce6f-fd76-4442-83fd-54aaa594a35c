package br.com.ksisolucoes.vo.materiais.bnafar.saida;

import java.io.Serializable;

import br.com.ksisolucoes.vo.materiais.bnafar.saida.base.BaseBnafarSaidaIntegracao;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class BnafarSaidaIntegracao extends BaseBnafarSaidaIntegracao implements CodigoManager {
	private static final long serialVersionUID = 1L;


/*[CONSTRUCTOR MARKER BEGIN]*/
	public BnafarSaidaIntegracao () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public BnafarSaidaIntegracao (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public BnafarSaidaIntegracao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.String jsonEnvio,
		java.util.Date dataEnvio,
		java.lang.String mensagemRetornoOriginal) {

		super (
			codigo,
			usuario,
			jsonEnvio,
			dataEnvio,
			mensagemRetornoOriginal);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}