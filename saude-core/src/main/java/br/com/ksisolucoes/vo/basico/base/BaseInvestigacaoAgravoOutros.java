package br.com.ksisolucoes.vo.basico.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the investigacao_agr_outros table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agr_outros"
 */

public abstract class BaseInvestigacaoAgravoOutros extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravoOutros";
	public static final String PROP_INVESTIGACAO_AGRAVO = "investigacaoAgravo";
	public static final String PROP_MICROSCOPIA = "microscopia";
	public static final String PROP_DATA_COLETA = "dataColeta";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_MACROSCOPIA = "macroscopia";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_ANATOMOPATOLOGICO = "anatomopatologico";


	// constructors
	public BaseInvestigacaoAgravoOutros () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravoOutros (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseInvestigacaoAgravoOutros (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.InvestigacaoAgravo investigacaoAgravo) {

		this.setCodigo(codigo);
		this.setInvestigacaoAgravo(investigacaoAgravo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataColeta;
	private java.lang.String macroscopia;
	private java.lang.String microscopia;
	private java.lang.String anatomopatologico;
	private java.util.Date dataCadastro;

	// many to one
	private br.com.ksisolucoes.vo.basico.InvestigacaoAgravo investigacaoAgravo;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_investigacao_agr_outros"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_coleta
	 */
	public java.util.Date getDataColeta () {
		return getPropertyValue(this, dataColeta, PROP_DATA_COLETA); 
	}

	/**
	 * Set the value related to the column: dt_coleta
	 * @param dataColeta the dt_coleta value
	 */
	public void setDataColeta (java.util.Date dataColeta) {
//        java.util.Date dataColetaOld = this.dataColeta;
		this.dataColeta = dataColeta;
//        this.getPropertyChangeSupport().firePropertyChange ("dataColeta", dataColetaOld, dataColeta);
	}



	/**
	 * Return the value associated with the column: macroscopia
	 */
	public java.lang.String getMacroscopia () {
		return getPropertyValue(this, macroscopia, PROP_MACROSCOPIA); 
	}

	/**
	 * Set the value related to the column: macroscopia
	 * @param macroscopia the macroscopia value
	 */
	public void setMacroscopia (java.lang.String macroscopia) {
//        java.lang.String macroscopiaOld = this.macroscopia;
		this.macroscopia = macroscopia;
//        this.getPropertyChangeSupport().firePropertyChange ("macroscopia", macroscopiaOld, macroscopia);
	}



	/**
	 * Return the value associated with the column: microscopia
	 */
	public java.lang.String getMicroscopia () {
		return getPropertyValue(this, microscopia, PROP_MICROSCOPIA); 
	}

	/**
	 * Set the value related to the column: microscopia
	 * @param microscopia the microscopia value
	 */
	public void setMicroscopia (java.lang.String microscopia) {
//        java.lang.String microscopiaOld = this.microscopia;
		this.microscopia = microscopia;
//        this.getPropertyChangeSupport().firePropertyChange ("microscopia", microscopiaOld, microscopia);
	}



	/**
	 * Return the value associated with the column: anatomopatologico
	 */
	public java.lang.String getAnatomopatologico () {
		return getPropertyValue(this, anatomopatologico, PROP_ANATOMOPATOLOGICO); 
	}

	/**
	 * Set the value related to the column: anatomopatologico
	 * @param anatomopatologico the anatomopatologico value
	 */
	public void setAnatomopatologico (java.lang.String anatomopatologico) {
//        java.lang.String anatomopatologicoOld = this.anatomopatologico;
		this.anatomopatologico = anatomopatologico;
//        this.getPropertyChangeSupport().firePropertyChange ("anatomopatologico", anatomopatologicoOld, anatomopatologico);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: cd_investigacao_agravo
	 */
	public br.com.ksisolucoes.vo.basico.InvestigacaoAgravo getInvestigacaoAgravo () {
		return getPropertyValue(this, investigacaoAgravo, PROP_INVESTIGACAO_AGRAVO); 
	}

	/**
	 * Set the value related to the column: cd_investigacao_agravo
	 * @param investigacaoAgravo the cd_investigacao_agravo value
	 */
	public void setInvestigacaoAgravo (br.com.ksisolucoes.vo.basico.InvestigacaoAgravo investigacaoAgravo) {
//        br.com.ksisolucoes.vo.basico.InvestigacaoAgravo investigacaoAgravoOld = this.investigacaoAgravo;
		this.investigacaoAgravo = investigacaoAgravo;
//        this.getPropertyChangeSupport().firePropertyChange ("investigacaoAgravo", investigacaoAgravoOld, investigacaoAgravo);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.InvestigacaoAgravoOutros)) return false;
		else {
			br.com.ksisolucoes.vo.basico.InvestigacaoAgravoOutros investigacaoAgravoOutros = (br.com.ksisolucoes.vo.basico.InvestigacaoAgravoOutros) obj;
			if (null == this.getCodigo() || null == investigacaoAgravoOutros.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravoOutros.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}