package br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the atividades_vigilancia table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="atividades_vigilancia"
 */

public abstract class BaseAtividadesVigilancia extends BaseRootVO implements Serializable {

	public static String REF = "AtividadesVigilancia";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_PROCEDIMENTO = "procedimento";
	public static final String PROP_TAXA = "taxa";
	public static final String PROP_SOLICITACOES_DIVERSAS = "solicitacoesDiversas";
	public static final String PROP_PONTUACAO = "pontuacao";
	public static final String PROP_QUANTIDADE_TAXA = "quantidadeTaxa";
	public static final String PROP_INSPECAO = "inspecao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_SOLICITACAO_JURIDICA = "solicitacaoJuridica";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_USUARIO_CADASTRO = "usuarioCadastro";
	public static final String PROP_DATA_ALTERACAO = "dataAlteracao";


	// constructors
	public BaseAtividadesVigilancia () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAtividadesVigilancia (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseAtividadesVigilancia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
		java.lang.String descricao,
		java.util.Date dataCadastro,
		java.util.Date dataAlteracao,
		java.lang.Long inspecao,
		java.lang.Long solicitacoesDiversas,
		java.lang.Long solicitacaoJuridica) {

		this.setCodigo(codigo);
		this.setUsuarioCadastro(usuarioCadastro);
		this.setDescricao(descricao);
		this.setDataCadastro(dataCadastro);
		this.setDataAlteracao(dataAlteracao);
		this.setInspecao(inspecao);
		this.setSolicitacoesDiversas(solicitacoesDiversas);
		this.setSolicitacaoJuridica(solicitacaoJuridica);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.util.Date dataCadastro;
	private java.util.Date dataAlteracao;
	private java.lang.Long inspecao;
	private java.lang.Double pontuacao;
	private java.lang.Long solicitacoesDiversas;
	private java.lang.Double quantidadeTaxa;
	private java.lang.Long solicitacaoJuridica;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro;
	private br.com.ksisolucoes.vo.vigilancia.taxa.Taxa taxa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_atividades_vigilancia"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: descricao
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: descricao
	 * @param descricao the descricao value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_alteracao
	 */
	public java.util.Date getDataAlteracao () {
		return getPropertyValue(this, dataAlteracao, PROP_DATA_ALTERACAO); 
	}

	/**
	 * Set the value related to the column: dt_alteracao
	 * @param dataAlteracao the dt_alteracao value
	 */
	public void setDataAlteracao (java.util.Date dataAlteracao) {
//        java.util.Date dataAlteracaoOld = this.dataAlteracao;
		this.dataAlteracao = dataAlteracao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAlteracao", dataAlteracaoOld, dataAlteracao);
	}



	/**
	 * Return the value associated with the column: flag_inspecao
	 */
	public java.lang.Long getInspecao () {
		return getPropertyValue(this, inspecao, PROP_INSPECAO); 
	}

	/**
	 * Set the value related to the column: flag_inspecao
	 * @param inspecao the flag_inspecao value
	 */
	public void setInspecao (java.lang.Long inspecao) {
//        java.lang.Long inspecaoOld = this.inspecao;
		this.inspecao = inspecao;
//        this.getPropertyChangeSupport().firePropertyChange ("inspecao", inspecaoOld, inspecao);
	}



	/**
	 * Return the value associated with the column: pontuacao
	 */
	public java.lang.Double getPontuacao () {
		return getPropertyValue(this, pontuacao, PROP_PONTUACAO); 
	}

	/**
	 * Set the value related to the column: pontuacao
	 * @param pontuacao the pontuacao value
	 */
	public void setPontuacao (java.lang.Double pontuacao) {
//        java.lang.Double pontuacaoOld = this.pontuacao;
		this.pontuacao = pontuacao;
//        this.getPropertyChangeSupport().firePropertyChange ("pontuacao", pontuacaoOld, pontuacao);
	}



	/**
	 * Return the value associated with the column: flag_solic_diversas
	 */
	public java.lang.Long getSolicitacoesDiversas () {
		return getPropertyValue(this, solicitacoesDiversas, PROP_SOLICITACOES_DIVERSAS); 
	}

	/**
	 * Set the value related to the column: flag_solic_diversas
	 * @param solicitacoesDiversas the flag_solic_diversas value
	 */
	public void setSolicitacoesDiversas (java.lang.Long solicitacoesDiversas) {
//        java.lang.Long solicitacoesDiversasOld = this.solicitacoesDiversas;
		this.solicitacoesDiversas = solicitacoesDiversas;
//        this.getPropertyChangeSupport().firePropertyChange ("solicitacoesDiversas", solicitacoesDiversasOld, solicitacoesDiversas);
	}



	/**
	 * Return the value associated with the column: qtd_taxa
	 */
	public java.lang.Double getQuantidadeTaxa () {
		return getPropertyValue(this, quantidadeTaxa, PROP_QUANTIDADE_TAXA); 
	}

	/**
	 * Set the value related to the column: qtd_taxa
	 * @param quantidadeTaxa the qtd_taxa value
	 */
	public void setQuantidadeTaxa (java.lang.Double quantidadeTaxa) {
//        java.lang.Double quantidadeTaxaOld = this.quantidadeTaxa;
		this.quantidadeTaxa = quantidadeTaxa;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeTaxa", quantidadeTaxaOld, quantidadeTaxa);
	}



	/**
	 * Return the value associated with the column: flag_solic_juridica
	 */
	public java.lang.Long getSolicitacaoJuridica () {
		return getPropertyValue(this, solicitacaoJuridica, PROP_SOLICITACAO_JURIDICA); 
	}

	/**
	 * Set the value related to the column: flag_solic_juridica
	 * @param solicitacaoJuridica the flag_solic_juridica value
	 */
	public void setSolicitacaoJuridica (java.lang.Long solicitacaoJuridica) {
//        java.lang.Long solicitacaoJuridicaOld = this.solicitacaoJuridica;
		this.solicitacaoJuridica = solicitacaoJuridica;
//        this.getPropertyChangeSupport().firePropertyChange ("solicitacaoJuridica", solicitacaoJuridicaOld, solicitacaoJuridica);
	}



	/**
	 * Return the value associated with the column: cd_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento getProcedimento () {
		return getPropertyValue(this, procedimento, PROP_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_procedimento
	 * @param procedimento the cd_procedimento value
	 */
	public void setProcedimento (br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoOld = this.procedimento;
		this.procedimento = procedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimento", procedimentoOld, procedimento);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCadastro () {
		return getPropertyValue(this, usuarioCadastro, PROP_USUARIO_CADASTRO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuarioCadastro the cd_usuario value
	 */
	public void setUsuarioCadastro (br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCadastroOld = this.usuarioCadastro;
		this.usuarioCadastro = usuarioCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadastro", usuarioCadastroOld, usuarioCadastro);
	}



	/**
	 * Return the value associated with the column: cd_taxa
	 */
	public br.com.ksisolucoes.vo.vigilancia.taxa.Taxa getTaxa () {
		return getPropertyValue(this, taxa, PROP_TAXA); 
	}

	/**
	 * Set the value related to the column: cd_taxa
	 * @param taxa the cd_taxa value
	 */
	public void setTaxa (br.com.ksisolucoes.vo.vigilancia.taxa.Taxa taxa) {
//        br.com.ksisolucoes.vo.vigilancia.taxa.Taxa taxaOld = this.taxa;
		this.taxa = taxa;
//        this.getPropertyChangeSupport().firePropertyChange ("taxa", taxaOld, taxa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia atividadesVigilancia = (br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia) obj;
			if (null == this.getCodigo() || null == atividadesVigilancia.getCodigo()) return false;
			else return (this.getCodigo().equals(atividadesVigilancia.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}