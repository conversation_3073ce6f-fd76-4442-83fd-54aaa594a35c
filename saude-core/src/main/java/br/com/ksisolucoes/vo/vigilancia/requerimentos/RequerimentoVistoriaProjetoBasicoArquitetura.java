package br.com.ksisolucoes.vo.vigilancia.requerimentos;

import br.com.ksisolucoes.util.Valor;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.base.BaseRequerimentoVistoriaProjetoBasicoArquitetura;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

import java.io.Serializable;



public class RequerimentoVistoriaProjetoBasicoArquitetura extends BaseRequerimentoVistoriaProjetoBasicoArquitetura implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequerimentoVistoriaProjetoBasicoArquitetura () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequerimentoVistoriaProjetoBasicoArquitetura (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequerimentoVistoriaProjetoBasicoArquitetura (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia,
		br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAnaliseProjeto requerimentoAnaliseProjeto,
		br.com.ksisolucoes.vo.vigilancia.TipoProjetoVigilancia tipoProjetoVigilancia) {

		super (
			codigo,
			requerimentoVigilancia,
			requerimentoAnaliseProjeto,
			tipoProjetoVigilancia);
	}

/*[CONSTRUCTOR MARKER END]*/

	public String getNumeracaoConformidadeTecnicaFormatado() {
		return VigilanciaHelper.formatarProtocolo(getNumeracaoParecerConformidadeTecnica());
	}

	public String getDescricaoAreaFormatado() {
		if(getArea() != null) {
			return Valor.adicionarFormatacaoMonetaria(getArea()).concat(" m²");
		} else {
			return "";
		}
	}

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}