package br.com.ksisolucoes.vo.prontuario.integracao.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the integracao_atendimento_prontuario table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="integracao_atendimento_prontuario"
 */

public abstract class BaseIntegracaoAtendimentoProntuario extends BaseRootVO implements Serializable {

	public static String REF = "IntegracaoAtendimentoProntuario";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_CODIGO_PRONTUARIO = "codigoProntuario";
	public static final String PROP_CNS = "cns";


	// constructors
	public BaseIntegracaoAtendimentoProntuario () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseIntegracaoAtendimentoProntuario (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseIntegracaoAtendimentoProntuario (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		java.lang.Long cns,
		java.lang.Long codigoProntuario) {

		this.setCodigo(codigo);
		this.setEmpresa(empresa);
		this.setCns(cns);
		this.setCodigoProntuario(codigoProntuario);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long cns;
	private java.lang.Long codigoProntuario;

	// many to one
	private br.com.ksisolucoes.vo.basico.Empresa empresa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_integracao_atendimento_prontuario"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cns
	 */
	public java.lang.Long getCns () {
		return getPropertyValue(this, cns, PROP_CNS); 
	}

	/**
	 * Set the value related to the column: cns
	 * @param cns the cns value
	 */
	public void setCns (java.lang.Long cns) {
//        java.lang.Long cnsOld = this.cns;
		this.cns = cns;
//        this.getPropertyChangeSupport().firePropertyChange ("cns", cnsOld, cns);
	}



	/**
	 * Return the value associated with the column: cd_atendimento_prontuario
	 */
	public java.lang.Long getCodigoProntuario () {
		return getPropertyValue(this, codigoProntuario, PROP_CODIGO_PRONTUARIO); 
	}

	/**
	 * Set the value related to the column: cd_atendimento_prontuario
	 * @param codigoProntuario the cd_atendimento_prontuario value
	 */
	public void setCodigoProntuario (java.lang.Long codigoProntuario) {
//        java.lang.Long codigoProntuarioOld = this.codigoProntuario;
		this.codigoProntuario = codigoProntuario;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoProntuario", codigoProntuarioOld, codigoProntuario);
	}



	/**
	 * Return the value associated with the column: cd_empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: cd_empresa
	 * @param empresa the cd_empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.integracao.IntegracaoAtendimentoProntuario)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.integracao.IntegracaoAtendimentoProntuario integracaoAtendimentoProntuario = (br.com.ksisolucoes.vo.prontuario.integracao.IntegracaoAtendimentoProntuario) obj;
			if (null == this.getCodigo() || null == integracaoAtendimentoProntuario.getCodigo()) return false;
			else return (this.getCodigo().equals(integracaoAtendimentoProntuario.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}