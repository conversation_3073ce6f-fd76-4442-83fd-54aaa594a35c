package br.com.ksisolucoes.vo.basico.pesquisa;

import br.com.celk.integracao.IntegracaoRest;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import java.io.Serializable;

import br.com.ksisolucoes.vo.basico.pesquisa.base.BasePesquisaVisita;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;


@IntegracaoRest
public class PesquisaVisita extends BasePesquisaVisita implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public PesquisaVisita () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public PesquisaVisita (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public PesquisaVisita (
		java.lang.Long codigo,
		java.lang.Long versionAll) {

		super (
			codigo,
			versionAll);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getSexoFormatado() {
        if (RepositoryComponentDefault.SEXO_MASCULINO.equals(getSexo())) {
            return Bundle.getStringApplication("rotulo_masculino");
        } else if (RepositoryComponentDefault.SEXO_FEMININO.equals(getSexo())) {
            return Bundle.getStringApplication("rotulo_feminino");
        }
        return null;
    }
}