package br.com.ksisolucoes.vo.vigilancia.investigacao;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.base.BaseInvestigacaoAgravoMenigococicaMeningites;

import java.io.Serializable;



public class InvestigacaoAgravoMenigococicaMeningites extends BaseInvestigacaoAgravoMenigococicaMeningites implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public InvestigacaoAgravoMenigococicaMeningites () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public InvestigacaoAgravoMenigococicaMeningites (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public InvestigacaoAgravoMenigococicaMeningites (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
		java.lang.String flagInformacoesComplementares) {

		super (
			codigo,
			registroAgravo,
			flagInformacoesComplementares);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

	public static InvestigacaoAgravoMenigococicaMeningites buscaPorRegistroAgravo(RegistroAgravo registroAgravo) {
		InvestigacaoAgravoMenigococicaMeningites investigacao =
				LoadManager.getInstance(InvestigacaoAgravoMenigococicaMeningites.class)
						.addProperties(new HQLProperties(InvestigacaoAgravoMenigococicaMeningites.class).getProperties())
						.addProperties(new HQLProperties(RegistroAgravo.class, VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_REGISTRO_AGRAVO)).getProperties())
						.addProperty(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_MUNICIPIO_NOTIFICACAO, Cidade.PROP_CODIGO))
						.addProperty(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_MUNICIPIO_NOTIFICACAO, Cidade.PROP_DESCRICAO))
						.addProperty(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_MUNICIPIO_NOTIFICACAO, Cidade.PROP_ESTADO, Estado.PROP_CODIGO))
						.addProperty(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_MUNICIPIO_NOTIFICACAO, Cidade.PROP_ESTADO, Estado.PROP_SIGLA))
						.addProperty(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_MUNICIPIO_NOTIFICACAO, Cidade.PROP_ESTADO, Estado.PROP_DESCRICAO))
						.addProperty(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_UNIDADE_NOTIFICADORA, Empresa.PROP_CODIGO))
						.addProperty(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_UNIDADE_NOTIFICADORA, Empresa.PROP_DESCRICAO))
						.addProperty(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_UNIDADE_NOTIFICADORA, Empresa.PROP_CNES))
						.addProperty(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_PACIENTE, UsuarioCadsus.PROP_CODIGO))
						.addProperty(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_PACIENTE, UsuarioCadsus.PROP_DATA_NASCIMENTO))
						.addProperty(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_PACIENTE, UsuarioCadsus.PROP_NOME_MAE))
						.addProperty(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_MUNICIPIO_RESIDENCIA, Cidade.PROP_CODIGO))
						.addProperty(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_MUNICIPIO_RESIDENCIA, Cidade.PROP_DESCRICAO))
						.addProperty(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_MUNICIPIO_RESIDENCIA, Cidade.PROP_ESTADO, Estado.PROP_CODIGO))
						.addProperty(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_MUNICIPIO_RESIDENCIA, Cidade.PROP_ESTADO, Estado.PROP_SIGLA))
						.addProperty(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_MUNICIPIO_RESIDENCIA, Cidade.PROP_ESTADO, Estado.PROP_DESCRICAO))
						.addProperty(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_HOSPITAL, Empresa.PROP_CODIGO))
						.addProperty(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_HOSPITAL, Empresa.PROP_DESCRICAO))
						.addProperty(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_HOSPITAL, Empresa.PROP_CNES))
						.addProperty(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_HOSPITAL, Empresa.PROP_CIDADE, Cidade.PROP_CODIGO))
						.addProperty(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_HOSPITAL, Empresa.PROP_CIDADE, Cidade.PROP_DESCRICAO))
						.addProperty(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_HOSPITAL, Empresa.PROP_CIDADE, Cidade.PROP_ESTADO, Estado.PROP_CODIGO))
						.addProperty(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_HOSPITAL, Empresa.PROP_CIDADE, Cidade.PROP_ESTADO, Estado.PROP_SIGLA))
						.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(InvestigacaoAgravoMenigococicaMeningites.PROP_REGISTRO_AGRAVO, RegistroAgravo.PROP_CODIGO), registroAgravo.getCodigo()))
						.start().getVO();
		return investigacao;
	}
}