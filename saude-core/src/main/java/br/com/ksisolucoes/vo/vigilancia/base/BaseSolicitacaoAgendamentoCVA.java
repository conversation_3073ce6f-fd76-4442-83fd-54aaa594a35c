package br.com.ksisolucoes.vo.vigilancia.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;

/**
 * This is an object that contains data related to the
 * solicitacao_agendamento_cva table. Do not modify this class because it will
 * be overwritten if the configuration file related to this class is modified.
 *
 * @hibernate.class table="solicitacao_agendamento_cva"
 */
public abstract class BaseSolicitacaoAgendamentoCVA extends BaseRootVO implements Serializable {

    public static String REF = "SolicitacaoAgendamentoCVA";
    public static final String PROP_ATIVIDADE_VETERINARIA = "atividadeVeterinaria";
    public static final String PROP_CODIGO = "codigo";
    public static final String PROP_DATA_CADASTRO = "dataCadastro";
    public static final String PROP_EMPRESA = "empresa";
    public static final String PROP_FLAG_URGENTE = "flagUrgente";
    public static final String PROP_EMPRESA_AGENDAMENTO = "empresaAgendamento";
    public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
    public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";
    public static final String PROP_USUARIO = "usuario";
    public static final String PROP_STATUS = "status";
    public static final String PROP_RESPONSAVEL = "responsavel";
    public static final String PROP_DATA_SOLICITACAO = "dataSolicitacao";
    public static final String PROP_DATA_USUARIO = "dataUsuario";
    public static final String PROP_TELEFONE2 = "telefone2";
    public static final String PROP_DATA_AGENDAMENTO = "dataAgendamento";
    public static final String PROP_TELEFONE3 = "telefone3";
    public static final String PROP_TELEFONE1 = "telefone1";
    public static final String PROP_MOTIVO = "motivo";
    public static final String PROP_PROFISSIONAL_AGENDAMENTO = "profissionalAgendamento";
    public static final String PROP_OBSERVACAO = "observacao";

    // constructors
    public BaseSolicitacaoAgendamentoCVA() {
        initialize();
    }

    /**
     * Constructor for primary key
     */
    public BaseSolicitacaoAgendamentoCVA(java.lang.Long codigo) {
        this.setCodigo(codigo);
        initialize();
    }

    /**
     * Constructor for required fields
     */
    public BaseSolicitacaoAgendamentoCVA(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.basico.Empresa empresa,
            br.com.ksisolucoes.vo.controle.Usuario usuario,
            br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento,
            java.lang.String responsavel,
            java.util.Date dataSolicitacao,
            java.util.Date dataCadastro,
            java.util.Date dataUsuario,
            java.lang.Long flagUrgente,
            java.lang.Long status,
            java.util.Date dataAgendamento) {

        this.setCodigo(codigo);
        this.setEmpresa(empresa);
        this.setUsuario(usuario);
        this.setUsuarioCancelamento(usuarioCancelamento);
        this.setResponsavel(responsavel);
        this.setDataSolicitacao(dataSolicitacao);
        this.setDataCadastro(dataCadastro);
        this.setDataUsuario(dataUsuario);
        this.setFlagUrgente(flagUrgente);
        this.setStatus(status);
        this.setDataAgendamento(dataAgendamento);
        initialize();
    }

    protected void initialize() {
    }

    private int hashCode = Integer.MIN_VALUE;

    // primary key
    private java.lang.Long codigo;

    // fields
    private java.lang.String responsavel;
    private java.util.Date dataSolicitacao;
    private java.util.Date dataCadastro;
    private java.util.Date dataUsuario;
    private java.lang.Long flagUrgente;
    private java.lang.String telefone1;
    private java.lang.String telefone2;
    private java.lang.String telefone3;
    private java.lang.Long status;
    private java.util.Date dataAgendamento;
    private java.lang.String motivo;
    private java.util.Date dataCancelamento;
    private java.lang.String observacao;

    // many to one
    private br.com.ksisolucoes.vo.basico.Empresa empresa;
    private br.com.ksisolucoes.vo.controle.Usuario usuario;
    private br.com.ksisolucoes.vo.vigilancia.AtividadeVeterinaria atividadeVeterinaria;
    private br.com.ksisolucoes.vo.basico.Empresa empresaAgendamento;
    private br.com.ksisolucoes.vo.cadsus.Profissional profissionalAgendamento;
    private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;

    /**
     * Return the unique identifier of this class
     *
     * @hibernate.id generator-class="assigned" column="cd_sol_ag_cva"
     */
    public java.lang.Long getCodigo() {
        return getPropertyValue(this, codigo, "codigo");
    }

    /**
     * Set the unique identifier of this class
     *
     * @param codigo the new ID
     */
    public void setCodigo(java.lang.Long codigo) {
        this.codigo = codigo;
        this.hashCode = Integer.MIN_VALUE;
    }

    /**
     * Return the value associated with the column: responsavel
     */
    public java.lang.String getResponsavel() {
        return getPropertyValue(this, responsavel, PROP_RESPONSAVEL);
    }

    /**
     * Set the value related to the column: responsavel
     *
     * @param responsavel the responsavel value
     */
    public void setResponsavel(java.lang.String responsavel) {
//        java.lang.String responsavelOld = this.responsavel;
        this.responsavel = responsavel;
//        this.getPropertyChangeSupport().firePropertyChange ("responsavel", responsavelOld, responsavel);
    }

    /**
     * Return the value associated with the column: dt_solicitacao
     */
    public java.util.Date getDataSolicitacao() {
        return getPropertyValue(this, dataSolicitacao, PROP_DATA_SOLICITACAO);
    }

    /**
     * Set the value related to the column: dt_solicitacao
     *
     * @param dataSolicitacao the dt_solicitacao value
     */
    public void setDataSolicitacao(java.util.Date dataSolicitacao) {
//        java.util.Date dataSolicitacaoOld = this.dataSolicitacao;
        this.dataSolicitacao = dataSolicitacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataSolicitacao", dataSolicitacaoOld, dataSolicitacao);
    }

    /**
     * Return the value associated with the column: dt_cadastro
     */
    public java.util.Date getDataCadastro() {
        return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO);
    }

    /**
     * Set the value related to the column: dt_cadastro
     *
     * @param dataCadastro the dt_cadastro value
     */
    public void setDataCadastro(java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
        this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
    }

    /**
     * Return the value associated with the column: dt_usuario
     */
    public java.util.Date getDataUsuario() {
        return getPropertyValue(this, dataUsuario, PROP_DATA_USUARIO);
    }

    /**
     * Set the value related to the column: dt_usuario
     *
     * @param dataUsuario the dt_usuario value
     */
    public void setDataUsuario(java.util.Date dataUsuario) {
//        java.util.Date dataUsuarioOld = this.dataUsuario;
        this.dataUsuario = dataUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsuario", dataUsuarioOld, dataUsuario);
    }

    /**
     * Return the value associated with the column: flag_urgente
     */
    public java.lang.Long getFlagUrgente() {
        return getPropertyValue(this, flagUrgente, PROP_FLAG_URGENTE);
    }

    /**
     * Set the value related to the column: flag_urgente
     *
     * @param flagUrgente the flag_urgente value
     */
    public void setFlagUrgente(java.lang.Long flagUrgente) {
//        java.lang.Long flagUrgenteOld = this.flagUrgente;
        this.flagUrgente = flagUrgente;
//        this.getPropertyChangeSupport().firePropertyChange ("flagUrgente", flagUrgenteOld, flagUrgente);
    }

    /**
     * Return the value associated with the column: telefone1
     */
    public java.lang.String getTelefone1() {
        return getPropertyValue(this, telefone1, PROP_TELEFONE1);
    }

    /**
     * Set the value related to the column: telefone1
     *
     * @param telefone1 the telefone1 value
     */
    public void setTelefone1(java.lang.String telefone1) {
//        java.lang.String telefone1Old = this.telefone1;
        this.telefone1 = telefone1;
//        this.getPropertyChangeSupport().firePropertyChange ("telefone1", telefone1Old, telefone1);
    }

    /**
     * Return the value associated with the column: telefone2
     */
    public java.lang.String getTelefone2() {
        return getPropertyValue(this, telefone2, PROP_TELEFONE2);
    }

    /**
     * Set the value related to the column: telefone2
     *
     * @param telefone2 the telefone2 value
     */
    public void setTelefone2(java.lang.String telefone2) {
//        java.lang.String telefone2Old = this.telefone2;
        this.telefone2 = telefone2;
//        this.getPropertyChangeSupport().firePropertyChange ("telefone2", telefone2Old, telefone2);
    }

    /**
     * Return the value associated with the column: telefone3
     */
    public java.lang.String getTelefone3() {
        return getPropertyValue(this, telefone3, PROP_TELEFONE3);
    }

    /**
     * Set the value related to the column: telefone3
     *
     * @param telefone3 the telefone3 value
     */
    public void setTelefone3(java.lang.String telefone3) {
//        java.lang.String telefone3Old = this.telefone3;
        this.telefone3 = telefone3;
//        this.getPropertyChangeSupport().firePropertyChange ("telefone3", telefone3Old, telefone3);
    }

    /**
     * Return the value associated with the column: status
     */
    public java.lang.Long getStatus() {
        return getPropertyValue(this, status, PROP_STATUS);
    }

    /**
     * Set the value related to the column: status
     *
     * @param status the status value
     */
    public void setStatus(java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
        this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
    }

    /**
     * Return the value associated with the column: dt_agendamento
     */
    public java.util.Date getDataAgendamento() {
        return getPropertyValue(this, dataAgendamento, PROP_DATA_AGENDAMENTO);
    }

    /**
     * Set the value related to the column: dt_agendamento
     *
     * @param dataAgendamento the dt_agendamento value
     */
    public void setDataAgendamento(java.util.Date dataAgendamento) {
//        java.util.Date dataAgendamentoOld = this.dataAgendamento;
        this.dataAgendamento = dataAgendamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAgendamento", dataAgendamentoOld, dataAgendamento);
    }

    /**
     * Return the value associated with the column: ds_motivo_can
     */
    public java.lang.String getMotivo() {
        return getPropertyValue(this, motivo, PROP_MOTIVO);
    }

    /**
     * Set the value related to the column: ds_motivo_can
     *
     * @param motivo the ds_motivo_can value
     */
    public void setMotivo(java.lang.String motivo) {
//        java.lang.String motivoOld = this.motivo;
        this.motivo = motivo;
//        this.getPropertyChangeSupport().firePropertyChange ("motivo", motivoOld, motivo);
    }

    /**
     * Return the value associated with the column: dt_cancelamento
     */
    public java.util.Date getDataCancelamento() {
        return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO);
    }

    /**
     * Set the value related to the column: dt_cancelamento
     *
     * @param dataCancelamento the dt_cancelamento value
     */
    public void setDataCancelamento(java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
        this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
    }

    /**
     * Return the value associated with the column: ds_observacao
     */
    public java.lang.String getObservacao() {
        return getPropertyValue(this, observacao, PROP_OBSERVACAO);
    }

    /**
     * Set the value related to the column: ds_observacao
     *
     * @param observacao the ds_observacao value
     */
    public void setObservacao(java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
        this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
    }

    /**
     * Return the value associated with the column: empresa
     */
    public br.com.ksisolucoes.vo.basico.Empresa getEmpresa() {
        return getPropertyValue(this, empresa, PROP_EMPRESA);
    }

    /**
     * Set the value related to the column: empresa
     *
     * @param empresa the empresa value
     */
    public void setEmpresa(br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
        this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
    }

    /**
     * Return the value associated with the column: cd_usuario
     */
    public br.com.ksisolucoes.vo.controle.Usuario getUsuario() {
        return getPropertyValue(this, usuario, PROP_USUARIO);
    }

    /**
     * Set the value related to the column: cd_usuario
     *
     * @param usuario the cd_usuario value
     */
    public void setUsuario(br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
        this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
    }

    /**
     * Return the value associated with the column: cd_atividade_veterinaria
     */
    public br.com.ksisolucoes.vo.vigilancia.AtividadeVeterinaria getAtividadeVeterinaria() {
        return getPropertyValue(this, atividadeVeterinaria, PROP_ATIVIDADE_VETERINARIA);
    }

    /**
     * Set the value related to the column: cd_atividade_veterinaria
     *
     * @param atividadeVeterinaria the cd_atividade_veterinaria value
     */
    public void setAtividadeVeterinaria(br.com.ksisolucoes.vo.vigilancia.AtividadeVeterinaria atividadeVeterinaria) {
//        br.com.ksisolucoes.vo.vigilancia.AtividadeVeterinaria atividadeVeterinariaOld = this.atividadeVeterinaria;
        this.atividadeVeterinaria = atividadeVeterinaria;
//        this.getPropertyChangeSupport().firePropertyChange ("atividadeVeterinaria", atividadeVeterinariaOld, atividadeVeterinaria);
    }

    /**
     * Return the value associated with the column: empresa_agendamento
     */
    public br.com.ksisolucoes.vo.basico.Empresa getEmpresaAgendamento() {
        return getPropertyValue(this, empresaAgendamento, PROP_EMPRESA_AGENDAMENTO);
    }

    /**
     * Set the value related to the column: empresa_agendamento
     *
     * @param empresaAgendamento the empresa_agendamento value
     */
    public void setEmpresaAgendamento(br.com.ksisolucoes.vo.basico.Empresa empresaAgendamento) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaAgendamentoOld = this.empresaAgendamento;
        this.empresaAgendamento = empresaAgendamento;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaAgendamento", empresaAgendamentoOld, empresaAgendamento);
    }

    /**
     * Return the value associated with the column: cd_prof_agendamento
     */
    public br.com.ksisolucoes.vo.cadsus.Profissional getProfissionalAgendamento() {
        return getPropertyValue(this, profissionalAgendamento, PROP_PROFISSIONAL_AGENDAMENTO);
    }

    /**
     * Set the value related to the column: cd_prof_agendamento
     *
     * @param profissionalAgendamento the cd_prof_agendamento value
     */
    public void setProfissionalAgendamento(br.com.ksisolucoes.vo.cadsus.Profissional profissionalAgendamento) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalAgendamentoOld = this.profissionalAgendamento;
        this.profissionalAgendamento = profissionalAgendamento;
//        this.getPropertyChangeSupport().firePropertyChange ("profissionalAgendamento", profissionalAgendamentoOld, profissionalAgendamento);
    }

    /**
     * Return the value associated with the column: cd_usu_can
     */
    public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento() {
        return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO);
    }

    /**
     * Set the value related to the column: cd_usu_can
     *
     * @param usuarioCancelamento the cd_usu_can value
     */
    public void setUsuarioCancelamento(br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
        this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
    }

    public boolean equals(Object obj) {
        if (null == obj) {
            return false;
        }
        if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.SolicitacaoAgendamentoCVA)) {
            return false;
        } else {
            br.com.ksisolucoes.vo.vigilancia.SolicitacaoAgendamentoCVA solicitacaoAgendamentoCVA = (br.com.ksisolucoes.vo.vigilancia.SolicitacaoAgendamentoCVA) obj;
            if (null == this.getCodigo() || null == solicitacaoAgendamentoCVA.getCodigo()) {
                return false;
            } else {
                return (this.getCodigo().equals(solicitacaoAgendamentoCVA.getCodigo()));
            }
        }
    }

    public int hashCode() {
        if (Integer.MIN_VALUE == this.hashCode) {
            if (null == this.getCodigo()) {
                return super.hashCode();
            } else {
                String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
                this.hashCode = hashStr.hashCode();
            }
        }
        return this.hashCode;
    }

    public String toString() {
        return super.toString();
    }

    private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
        if (this.retornoValidacao == null) {
            this.retornoValidacao = new RetornoValidacao();
        }
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
        this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}
