package br.com.ksisolucoes.vo.vigilancia.requerimentos;

import br.com.ksisolucoes.util.EnumUtil;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.base.BaseRequerimentoProjetoHidrossanitarioAnexo;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.enums.RequerimentosProjetosEnums;

import java.io.Serializable;


public class RequerimentoProjetoHidrossanitarioAnexo extends BaseRequerimentoProjetoHidrossanitarioAnexo implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequerimentoProjetoHidrossanitarioAnexo () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequerimentoProjetoHidrossanitarioAnexo (Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequerimentoProjetoHidrossanitarioAnexo (
		Long codigo,
		br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo gerenciadorArquivo,
		RequerimentoProjetoHidrossanitario requerimentoProjetoHidrossanitario,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.controle.Usuario usuarioAlteracao,
		Long situacao,
		String descricao,
		java.util.Date dataCadastro,
		java.util.Date dataAlteracao) {

		super (
			codigo,
			gerenciadorArquivo,
			requerimentoProjetoHidrossanitario,
			usuario,
			usuarioAlteracao,
			situacao,
			descricao,
			dataCadastro,
			dataAlteracao);
	}

/*[CONSTRUCTOR MARKER END]*/

	public String getDescricaoStatus() {
        return new EnumUtil().resolveDescricao(RequerimentosProjetosEnums.Status.values(), getSituacao());
	}

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}