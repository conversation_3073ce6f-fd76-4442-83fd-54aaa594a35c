package br.com.ksisolucoes.vo.programasaude.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the hiperdia table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="hiperdia"
 */

public abstract class BaseHiperdia extends BaseRootVO implements Serializable {

	public static String REF = "Hiperdia";
	public static final String PROP_SOBREPESO_OBESIDADE = "sobrepesoObesidade";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_INFARTO_AGUDO_MIOCARDIO = "infartoAgudoMiocardio";
	public static final String PROP_PE_DIABETICO = "peDiabetico";
	public static final String PROP_OUTRAS_CORONARIOPATIAS = "outrasCoronariopatias";
	public static final String PROP_GLICEMIA = "glicemia";
	public static final String PROP_PRESSAO_ARTERIAL_DIASTOLICA = "pressaoArterialDiastolica";
	public static final String PROP_PRESSAO_ARTERIAL_SISTOLICA = "pressaoArterialSistolica";
	public static final String PROP_DIABETE_TIPO1 = "diabeteTipo1";
	public static final String PROP_DIABETE_TIPO2 = "diabeteTipo2";
	public static final String PROP_ATENDIMENTO = "atendimento";
	public static final String PROP_AVC = "avc";
	public static final String PROP_CINTURA = "cintura";
	public static final String PROP_PESO = "peso";
	public static final String PROP_HIPERTENSAO_ARTERIAL = "hipertensaoArterial";
	public static final String PROP_ALTURA = "altura";
	public static final String PROP_DOENCA_RENAL = "doencaRenal";
	public static final String PROP_PROGRAMA_SAUDE_USUARIO = "ProgramaSaudeUsuario";
	public static final String PROP_AMPUTACAO_DIABETE = "amputacaoDiabete";
	public static final String PROP_TABAGISMO = "tabagismo";
	public static final String PROP_SEDENTARISMO = "sedentarismo";
	public static final String PROP_GLICEMIA_TIPO = "glicemiaTipo";
	public static final String PROP_TRATAMENTO_NAO_MEDICAMENTOSO = "tratamentoNaoMedicamentoso";
	public static final String PROP_ANTECEDENTES_FAMILIARES_CARDIOVASCULARES = "antecedentesFamiliaresCardiovasculares";


	// constructors
	public BaseHiperdia () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseHiperdia (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseHiperdia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.programasaude.ProgramaSaudeUsuario programaSaudeUsuario,
		br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {

		this.setCodigo(codigo);
		this.setProgramaSaudeUsuario(programaSaudeUsuario);
		this.setAtendimento(atendimento);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long pressaoArterialSistolica;
	private java.lang.Long pressaoArterialDiastolica;
	private java.lang.Long cintura;
	private java.lang.Double peso;
	private java.lang.Double altura;
	private java.lang.Long glicemia;
	private java.lang.Long glicemiaTipo;
	private java.lang.String antecedentesFamiliaresCardiovasculares;
	private java.lang.String diabeteTipo1;
	private java.lang.String diabeteTipo2;
	private java.lang.String tabagismo;
	private java.lang.String sedentarismo;
	private java.lang.String sobrepesoObesidade;
	private java.lang.String hipertensaoArterial;
	private java.lang.String infartoAgudoMiocardio;
	private java.lang.String outrasCoronariopatias;
	private java.lang.String avc;
	private java.lang.String peDiabetico;
	private java.lang.String amputacaoDiabete;
	private java.lang.String doencaRenal;
	private java.lang.String tratamentoNaoMedicamentoso;

	// many to one
	private br.com.ksisolucoes.vo.programasaude.ProgramaSaudeUsuario programaSaudeUsuario;
	private br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="seq_hiperdia"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: pas
	 */
	public java.lang.Long getPressaoArterialSistolica () {
		return getPropertyValue(this, pressaoArterialSistolica, PROP_PRESSAO_ARTERIAL_SISTOLICA); 
	}

	/**
	 * Set the value related to the column: pas
	 * @param pressaoArterialSistolica the pas value
	 */
	public void setPressaoArterialSistolica (java.lang.Long pressaoArterialSistolica) {
//        java.lang.Long pressaoArterialSistolicaOld = this.pressaoArterialSistolica;
		this.pressaoArterialSistolica = pressaoArterialSistolica;
//        this.getPropertyChangeSupport().firePropertyChange ("pressaoArterialSistolica", pressaoArterialSistolicaOld, pressaoArterialSistolica);
	}



	/**
	 * Return the value associated with the column: pad
	 */
	public java.lang.Long getPressaoArterialDiastolica () {
		return getPropertyValue(this, pressaoArterialDiastolica, PROP_PRESSAO_ARTERIAL_DIASTOLICA); 
	}

	/**
	 * Set the value related to the column: pad
	 * @param pressaoArterialDiastolica the pad value
	 */
	public void setPressaoArterialDiastolica (java.lang.Long pressaoArterialDiastolica) {
//        java.lang.Long pressaoArterialDiastolicaOld = this.pressaoArterialDiastolica;
		this.pressaoArterialDiastolica = pressaoArterialDiastolica;
//        this.getPropertyChangeSupport().firePropertyChange ("pressaoArterialDiastolica", pressaoArterialDiastolicaOld, pressaoArterialDiastolica);
	}



	/**
	 * Return the value associated with the column: cintura
	 */
	public java.lang.Long getCintura () {
		return getPropertyValue(this, cintura, PROP_CINTURA); 
	}

	/**
	 * Set the value related to the column: cintura
	 * @param cintura the cintura value
	 */
	public void setCintura (java.lang.Long cintura) {
//        java.lang.Long cinturaOld = this.cintura;
		this.cintura = cintura;
//        this.getPropertyChangeSupport().firePropertyChange ("cintura", cinturaOld, cintura);
	}



	/**
	 * Return the value associated with the column: peso
	 */
	public java.lang.Double getPeso () {
		return getPropertyValue(this, peso, PROP_PESO); 
	}

	/**
	 * Set the value related to the column: peso
	 * @param peso the peso value
	 */
	public void setPeso (java.lang.Double peso) {
//        java.lang.Long pesoOld = this.peso;
		this.peso = peso;
//        this.getPropertyChangeSupport().firePropertyChange ("peso", pesoOld, peso);
	}



	/**
	 * Return the value associated with the column: altura
	 */
	public java.lang.Double getAltura () {
		return getPropertyValue(this, altura, PROP_ALTURA); 
	}

	/**
	 * Set the value related to the column: altura
	 * @param altura the altura value
	 */
	public void setAltura (java.lang.Double altura) {
		this.altura = altura;
	}



	/**
	 * Return the value associated with the column: glicemia
	 */
	public java.lang.Long getGlicemia () {
		return getPropertyValue(this, glicemia, PROP_GLICEMIA); 
	}

	/**
	 * Set the value related to the column: glicemia
	 * @param glicemia the glicemia value
	 */
	public void setGlicemia (java.lang.Long glicemia) {
//        java.lang.Long glicemiaOld = this.glicemia;
		this.glicemia = glicemia;
//        this.getPropertyChangeSupport().firePropertyChange ("glicemia", glicemiaOld, glicemia);
	}



	/**
	 * Return the value associated with the column: glicemia_tipo
	 */
	public java.lang.Long getGlicemiaTipo () {
		return getPropertyValue(this, glicemiaTipo, PROP_GLICEMIA_TIPO); 
	}

	/**
	 * Set the value related to the column: glicemia_tipo
	 * @param glicemiaTipo the glicemia_tipo value
	 */
	public void setGlicemiaTipo (java.lang.Long glicemiaTipo) {
//        java.lang.Long glicemiaTipoOld = this.glicemiaTipo;
		this.glicemiaTipo = glicemiaTipo;
//        this.getPropertyChangeSupport().firePropertyChange ("glicemiaTipo", glicemiaTipoOld, glicemiaTipo);
	}



	/**
	 * Return the value associated with the column: ant_fam_cardiovascular
	 */
	public java.lang.String getAntecedentesFamiliaresCardiovasculares () {
		return getPropertyValue(this, antecedentesFamiliaresCardiovasculares, PROP_ANTECEDENTES_FAMILIARES_CARDIOVASCULARES); 
	}

	/**
	 * Set the value related to the column: ant_fam_cardiovascular
	 * @param antecedentesFamiliaresCardiovasculares the ant_fam_cardiovascular value
	 */
	public void setAntecedentesFamiliaresCardiovasculares (java.lang.String antecedentesFamiliaresCardiovasculares) {
//        java.lang.String antecedentesFamiliaresCardiovascularesOld = this.antecedentesFamiliaresCardiovasculares;
		this.antecedentesFamiliaresCardiovasculares = antecedentesFamiliaresCardiovasculares;
//        this.getPropertyChangeSupport().firePropertyChange ("antecedentesFamiliaresCardiovasculares", antecedentesFamiliaresCardiovascularesOld, antecedentesFamiliaresCardiovasculares);
	}



	/**
	 * Return the value associated with the column: diabete_tipo1
	 */
	public java.lang.String getDiabeteTipo1 () {
		return getPropertyValue(this, diabeteTipo1, PROP_DIABETE_TIPO1); 
	}

	/**
	 * Set the value related to the column: diabete_tipo1
	 * @param diabeteTipo1 the diabete_tipo1 value
	 */
	public void setDiabeteTipo1 (java.lang.String diabeteTipo1) {
//        java.lang.String diabeteTipo1Old = this.diabeteTipo1;
		this.diabeteTipo1 = diabeteTipo1;
//        this.getPropertyChangeSupport().firePropertyChange ("diabeteTipo1", diabeteTipo1Old, diabeteTipo1);
	}



	/**
	 * Return the value associated with the column: diabete_tipo2
	 */
	public java.lang.String getDiabeteTipo2 () {
		return getPropertyValue(this, diabeteTipo2, PROP_DIABETE_TIPO2); 
	}

	/**
	 * Set the value related to the column: diabete_tipo2
	 * @param diabeteTipo2 the diabete_tipo2 value
	 */
	public void setDiabeteTipo2 (java.lang.String diabeteTipo2) {
//        java.lang.String diabeteTipo2Old = this.diabeteTipo2;
		this.diabeteTipo2 = diabeteTipo2;
//        this.getPropertyChangeSupport().firePropertyChange ("diabeteTipo2", diabeteTipo2Old, diabeteTipo2);
	}



	/**
	 * Return the value associated with the column: tabagismo
	 */
	public java.lang.String getTabagismo () {
		return getPropertyValue(this, tabagismo, PROP_TABAGISMO); 
	}

	/**
	 * Set the value related to the column: tabagismo
	 * @param tabagismo the tabagismo value
	 */
	public void setTabagismo (java.lang.String tabagismo) {
//        java.lang.String tabagismoOld = this.tabagismo;
		this.tabagismo = tabagismo;
//        this.getPropertyChangeSupport().firePropertyChange ("tabagismo", tabagismoOld, tabagismo);
	}



	/**
	 * Return the value associated with the column: sedentarismo
	 */
	public java.lang.String getSedentarismo () {
		return getPropertyValue(this, sedentarismo, PROP_SEDENTARISMO); 
	}

	/**
	 * Set the value related to the column: sedentarismo
	 * @param sedentarismo the sedentarismo value
	 */
	public void setSedentarismo (java.lang.String sedentarismo) {
//        java.lang.String sedentarismoOld = this.sedentarismo;
		this.sedentarismo = sedentarismo;
//        this.getPropertyChangeSupport().firePropertyChange ("sedentarismo", sedentarismoOld, sedentarismo);
	}



	/**
	 * Return the value associated with the column: sobrepeso_obesidade
	 */
	public java.lang.String getSobrepesoObesidade () {
		return getPropertyValue(this, sobrepesoObesidade, PROP_SOBREPESO_OBESIDADE); 
	}

	/**
	 * Set the value related to the column: sobrepeso_obesidade
	 * @param sobrepesoObesidade the sobrepeso_obesidade value
	 */
	public void setSobrepesoObesidade (java.lang.String sobrepesoObesidade) {
//        java.lang.String sobrepesoObesidadeOld = this.sobrepesoObesidade;
		this.sobrepesoObesidade = sobrepesoObesidade;
//        this.getPropertyChangeSupport().firePropertyChange ("sobrepesoObesidade", sobrepesoObesidadeOld, sobrepesoObesidade);
	}



	/**
	 * Return the value associated with the column: hipertensao_arterial
	 */
	public java.lang.String getHipertensaoArterial () {
		return getPropertyValue(this, hipertensaoArterial, PROP_HIPERTENSAO_ARTERIAL); 
	}

	/**
	 * Set the value related to the column: hipertensao_arterial
	 * @param hipertensaoArterial the hipertensao_arterial value
	 */
	public void setHipertensaoArterial (java.lang.String hipertensaoArterial) {
//        java.lang.String hipertensaoArterialOld = this.hipertensaoArterial;
		this.hipertensaoArterial = hipertensaoArterial;
//        this.getPropertyChangeSupport().firePropertyChange ("hipertensaoArterial", hipertensaoArterialOld, hipertensaoArterial);
	}



	/**
	 * Return the value associated with the column: infarto_agudo_miocardio
	 */
	public java.lang.String getInfartoAgudoMiocardio () {
		return getPropertyValue(this, infartoAgudoMiocardio, PROP_INFARTO_AGUDO_MIOCARDIO); 
	}

	/**
	 * Set the value related to the column: infarto_agudo_miocardio
	 * @param infartoAgudoMiocardio the infarto_agudo_miocardio value
	 */
	public void setInfartoAgudoMiocardio (java.lang.String infartoAgudoMiocardio) {
//        java.lang.String infartoAgudoMiocardioOld = this.infartoAgudoMiocardio;
		this.infartoAgudoMiocardio = infartoAgudoMiocardio;
//        this.getPropertyChangeSupport().firePropertyChange ("infartoAgudoMiocardio", infartoAgudoMiocardioOld, infartoAgudoMiocardio);
	}



	/**
	 * Return the value associated with the column: outras_coronariopatias
	 */
	public java.lang.String getOutrasCoronariopatias () {
		return getPropertyValue(this, outrasCoronariopatias, PROP_OUTRAS_CORONARIOPATIAS); 
	}

	/**
	 * Set the value related to the column: outras_coronariopatias
	 * @param outrasCoronariopatias the outras_coronariopatias value
	 */
	public void setOutrasCoronariopatias (java.lang.String outrasCoronariopatias) {
//        java.lang.String outrasCoronariopatiasOld = this.outrasCoronariopatias;
		this.outrasCoronariopatias = outrasCoronariopatias;
//        this.getPropertyChangeSupport().firePropertyChange ("outrasCoronariopatias", outrasCoronariopatiasOld, outrasCoronariopatias);
	}



	/**
	 * Return the value associated with the column: avc
	 */
	public java.lang.String getAvc () {
		return getPropertyValue(this, avc, PROP_AVC); 
	}

	/**
	 * Set the value related to the column: avc
	 * @param avc the avc value
	 */
	public void setAvc (java.lang.String avc) {
//        java.lang.String avcOld = this.avc;
		this.avc = avc;
//        this.getPropertyChangeSupport().firePropertyChange ("avc", avcOld, avc);
	}



	/**
	 * Return the value associated with the column: pe_diabetico
	 */
	public java.lang.String getPeDiabetico () {
		return getPropertyValue(this, peDiabetico, PROP_PE_DIABETICO); 
	}

	/**
	 * Set the value related to the column: pe_diabetico
	 * @param peDiabetico the pe_diabetico value
	 */
	public void setPeDiabetico (java.lang.String peDiabetico) {
//        java.lang.String peDiabeticoOld = this.peDiabetico;
		this.peDiabetico = peDiabetico;
//        this.getPropertyChangeSupport().firePropertyChange ("peDiabetico", peDiabeticoOld, peDiabetico);
	}



	/**
	 * Return the value associated with the column: amputacao_diabete
	 */
	public java.lang.String getAmputacaoDiabete () {
		return getPropertyValue(this, amputacaoDiabete, PROP_AMPUTACAO_DIABETE); 
	}

	/**
	 * Set the value related to the column: amputacao_diabete
	 * @param amputacaoDiabete the amputacao_diabete value
	 */
	public void setAmputacaoDiabete (java.lang.String amputacaoDiabete) {
//        java.lang.String amputacaoDiabeteOld = this.amputacaoDiabete;
		this.amputacaoDiabete = amputacaoDiabete;
//        this.getPropertyChangeSupport().firePropertyChange ("amputacaoDiabete", amputacaoDiabeteOld, amputacaoDiabete);
	}



	/**
	 * Return the value associated with the column: doenca_renal
	 */
	public java.lang.String getDoencaRenal () {
		return getPropertyValue(this, doencaRenal, PROP_DOENCA_RENAL); 
	}

	/**
	 * Set the value related to the column: doenca_renal
	 * @param doencaRenal the doenca_renal value
	 */
	public void setDoencaRenal (java.lang.String doencaRenal) {
//        java.lang.String doencaRenalOld = this.doencaRenal;
		this.doencaRenal = doencaRenal;
//        this.getPropertyChangeSupport().firePropertyChange ("doencaRenal", doencaRenalOld, doencaRenal);
	}



	/**
	 * Return the value associated with the column: trat_medicamentoso
	 */
	public java.lang.String getTratamentoNaoMedicamentoso () {
		return getPropertyValue(this, tratamentoNaoMedicamentoso, PROP_TRATAMENTO_NAO_MEDICAMENTOSO); 
	}

	/**
	 * Set the value related to the column: trat_medicamentoso
	 * @param tratamentoNaoMedicamentoso the trat_medicamentoso value
	 */
	public void setTratamentoNaoMedicamentoso (java.lang.String tratamentoNaoMedicamentoso) {
//        java.lang.String tratamentoNaoMedicamentosoOld = this.tratamentoNaoMedicamentoso;
		this.tratamentoNaoMedicamentoso = tratamentoNaoMedicamentoso;
//        this.getPropertyChangeSupport().firePropertyChange ("tratamentoNaoMedicamentoso", tratamentoNaoMedicamentosoOld, tratamentoNaoMedicamentoso);
	}



	/**
	 * Return the value associated with the column: CD_PROGRAMA_SAUDE
	 */
	public br.com.ksisolucoes.vo.programasaude.ProgramaSaudeUsuario getProgramaSaudeUsuario () {
		return getPropertyValue(this, programaSaudeUsuario, PROP_PROGRAMA_SAUDE_USUARIO); 
	}

	/**
	 * Set the value related to the column: CD_PROGRAMA_SAUDE
	 * @param programaSaudeUsuario the CD_PROGRAMA_SAUDE value
	 */
	public void setProgramaSaudeUsuario (br.com.ksisolucoes.vo.programasaude.ProgramaSaudeUsuario programaSaudeUsuario) {
//        br.com.ksisolucoes.vo.programasaude.ProgramaSaudeUsuario programaSaudeUsuarioOld = this.programaSaudeUsuario;
		this.programaSaudeUsuario = programaSaudeUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("programaSaudeUsuario", programaSaudeUsuarioOld, programaSaudeUsuario);
	}



	/**
	 * Return the value associated with the column: nr_atendimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Atendimento getAtendimento () {
		return getPropertyValue(this, atendimento, PROP_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: nr_atendimento
	 * @param atendimento the nr_atendimento value
	 */
	public void setAtendimento (br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoOld = this.atendimento;
		this.atendimento = atendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimento", atendimentoOld, atendimento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.programasaude.Hiperdia)) return false;
		else {
			br.com.ksisolucoes.vo.programasaude.Hiperdia hiperdia = (br.com.ksisolucoes.vo.programasaude.Hiperdia) obj;
			if (null == this.getCodigo() || null == hiperdia.getCodigo()) return false;
			else return (this.getCodigo().equals(hiperdia.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}