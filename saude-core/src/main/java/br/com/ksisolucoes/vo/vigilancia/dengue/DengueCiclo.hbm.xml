<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.dengue"  >
    <class name="DengueCiclo" table="dengue_ciclo">

        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_dengue_ciclo"
        >
            <generator class="sequence">
                <param name="sequence">seq_dengue_ciclo</param>
            </generator>
        </id> 
        <version column="version" name="version" type="long" />
        
        <property 
            column="dt_semana_inicial"
            name="dataSemanaInicial"
            type="date"
            not-null="true"
        />

        <property 
            column="dt_semana_final"
            name="dataSemanaFinal"
            type="date"
            not-null="true"
        />

        <property 
            name="dataCadastro"
            column="dt_cadastro"
            type="timestamp"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuarioCadastro"
            not-null="true"
        />
    </class>
</hibernate-mapping>
