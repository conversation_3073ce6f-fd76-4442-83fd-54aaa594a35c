package br.com.ksisolucoes.vo.controle.web;

import java.io.Serializable;

import br.com.ksisolucoes.vo.controle.web.base.BaseControleProgramaWebGrupo;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class ControleProgramaWebGrupo extends BaseControleProgramaWebGrupo implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ControleProgramaWebGrupo () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ControleProgramaWebGrupo (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ControleProgramaWebGrupo (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Grupo grupo,
		br.com.ksisolucoes.vo.controle.web.ProgramaWeb programaWeb) {

		super (
			codigo,
			grupo,
			programaWeb);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}