package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the atendimento_informacao table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="atendimento_informacao"
 */

public abstract class BaseAtendimentoInformacao extends BaseRootVO implements Serializable {

	public static String REF = "AtendimentoInformacao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_PROFISSIONAL_ALTA = "profissionalAlta";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_TIPO_ATENDIMENTO_FATURAMENTO = "tipoAtendimentoFaturamento";
	public static final String PROP_NOME_PACIENTE = "nomePaciente";
	public static final String PROP_ATENDIMENTO_ALTA = "atendimentoAlta";
	public static final String PROP_DATA_SAIDA = "dataSaida";
	public static final String PROP_SEQUENCIA_CICLO = "sequenciaCiclo";
	public static final String PROP_ATENDIMENTO_PRINCIPAL = "atendimentoPrincipal";
	public static final String PROP_ELO_TIPO_DIETA_RECEITUARIO = "eloTipoDietaReceituario";
	public static final String PROP_MOTIVO_ALTA = "motivoAlta";
	public static final String PROP_LEITO_QUARTO = "leitoQuarto";
	public static final String PROP_CONVENIO = "convenio";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_DATA_CHEGADA = "dataChegada";
	public static final String PROP_USUARIO_CADSUS_ACOMPANHANTE = "usuarioCadsusAcompanhante";
	public static final String PROP_STATUS_ATENDIMENTO = "statusAtendimento";


	// constructors
	public BaseAtendimentoInformacao () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAtendimentoInformacao (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseAtendimentoInformacao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Empresa empresa) {

		this.setCodigo(codigo);
		this.setEmpresa(empresa);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String nomePaciente;
	private java.util.Date dataChegada;
	private java.util.Date dataSaida;
	private java.lang.Long statusAtendimento;
	private java.lang.Long motivoAlta;
	private java.lang.Long sequenciaCiclo;
	private java.lang.Long atendimentoRN;
	private java.lang.Long indicadorAcidente;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.AtendimentoAlta atendimentoAlta;
	private br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoPrincipal;
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private br.com.ksisolucoes.vo.prontuario.basico.Convenio convenio;
	private br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto leitoQuarto;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissionalAlta;
	private br.com.ksisolucoes.vo.prontuario.basico.EloTipoDietaReceituario eloTipoDietaReceituario;
	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento tipoAtendimentoFaturamento;
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsusAcompanhante usuarioCadsusAcompanhante;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_atend_inf"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: nm_paciente
	 */
	public java.lang.String getNomePaciente () {
		return getPropertyValue(this, nomePaciente, PROP_NOME_PACIENTE); 
	}

	/**
	 * Set the value related to the column: nm_paciente
	 * @param nomePaciente the nm_paciente value
	 */
	public void setNomePaciente (java.lang.String nomePaciente) {
//        java.lang.String nomePacienteOld = this.nomePaciente;
		this.nomePaciente = nomePaciente;
//        this.getPropertyChangeSupport().firePropertyChange ("nomePaciente", nomePacienteOld, nomePaciente);
	}



	/**
	 * Return the value associated with the column: data_chegada
	 */
	public java.util.Date getDataChegada () {
		return getPropertyValue(this, dataChegada, PROP_DATA_CHEGADA); 
	}

	/**
	 * Set the value related to the column: data_chegada
	 * @param dataChegada the data_chegada value
	 */
	public void setDataChegada (java.util.Date dataChegada) {
//        java.util.Date dataChegadaOld = this.dataChegada;
		this.dataChegada = dataChegada;
//        this.getPropertyChangeSupport().firePropertyChange ("dataChegada", dataChegadaOld, dataChegada);
	}



	/**
	 * Return the value associated with the column: data_saida
	 */
	public java.util.Date getDataSaida () {
		return getPropertyValue(this, dataSaida, PROP_DATA_SAIDA); 
	}

	/**
	 * Set the value related to the column: data_saida
	 * @param dataSaida the data_saida value
	 */
	public void setDataSaida (java.util.Date dataSaida) {
//        java.util.Date dataSaidaOld = this.dataSaida;
		this.dataSaida = dataSaida;
//        this.getPropertyChangeSupport().firePropertyChange ("dataSaida", dataSaidaOld, dataSaida);
	}



	/**
	 * Return the value associated with the column: status_atendimento
	 */
	public java.lang.Long getStatusAtendimento () {
		return getPropertyValue(this, statusAtendimento, PROP_STATUS_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: status_atendimento
	 * @param statusAtendimento the status_atendimento value
	 */
	public void setStatusAtendimento (java.lang.Long statusAtendimento) {
//        java.lang.Long statusAtendimentoOld = this.statusAtendimento;
		this.statusAtendimento = statusAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("statusAtendimento", statusAtendimentoOld, statusAtendimento);
	}



	/**
	 * Return the value associated with the column: motivo_alta
	 */
	public java.lang.Long getMotivoAlta () {
		return getPropertyValue(this, motivoAlta, PROP_MOTIVO_ALTA); 
	}

	/**
	 * Set the value related to the column: motivo_alta
	 * @param motivoAlta the motivo_alta value
	 */
	public void setMotivoAlta (java.lang.Long motivoAlta) {
//        java.lang.Long motivoAltaOld = this.motivoAlta;
		this.motivoAlta = motivoAlta;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoAlta", motivoAltaOld, motivoAlta);
	}



	/**
	 * Return the value associated with the column: seq_ciclo
	 */
	public java.lang.Long getSequenciaCiclo () {
		return getPropertyValue(this, sequenciaCiclo, PROP_SEQUENCIA_CICLO); 
	}

	/**
	 * Set the value related to the column: seq_ciclo
	 * @param sequenciaCiclo the seq_ciclo value
	 */
	public void setSequenciaCiclo (java.lang.Long sequenciaCiclo) {
//        java.lang.Long sequenciaCicloOld = this.sequenciaCiclo;
		this.sequenciaCiclo = sequenciaCiclo;
//        this.getPropertyChangeSupport().firePropertyChange ("sequenciaCiclo", sequenciaCicloOld, sequenciaCiclo);
	}



	/**
	 * Return the value associated with the column: cd_atendimento_alta
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.AtendimentoAlta getAtendimentoAlta () {
		return getPropertyValue(this, atendimentoAlta, PROP_ATENDIMENTO_ALTA); 
	}

	/**
	 * Set the value related to the column: cd_atendimento_alta
	 * @param atendimentoAlta the cd_atendimento_alta value
	 */
	public void setAtendimentoAlta (br.com.ksisolucoes.vo.prontuario.basico.AtendimentoAlta atendimentoAlta) {
//        br.com.ksisolucoes.vo.prontuario.basico.AtendimentoAlta atendimentoAltaOld = this.atendimentoAlta;
		this.atendimentoAlta = atendimentoAlta;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimentoAlta", atendimentoAltaOld, atendimentoAlta);
	}



	/**
	 * Return the value associated with the column: nr_atendimento_principal
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Atendimento getAtendimentoPrincipal () {
		return getPropertyValue(this, atendimentoPrincipal, PROP_ATENDIMENTO_PRINCIPAL); 
	}

	/**
	 * Set the value related to the column: nr_atendimento_principal
	 * @param atendimentoPrincipal the nr_atendimento_principal value
	 */
	public void setAtendimentoPrincipal (br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoPrincipal) {
//        br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoPrincipalOld = this.atendimentoPrincipal;
		this.atendimentoPrincipal = atendimentoPrincipal;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimentoPrincipal", atendimentoPrincipalOld, atendimentoPrincipal);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cd_convenio
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Convenio getConvenio () {
		return getPropertyValue(this, convenio, PROP_CONVENIO); 
	}

	/**
	 * Set the value related to the column: cd_convenio
	 * @param convenio the cd_convenio value
	 */
	public void setConvenio (br.com.ksisolucoes.vo.prontuario.basico.Convenio convenio) {
//        br.com.ksisolucoes.vo.prontuario.basico.Convenio convenioOld = this.convenio;
		this.convenio = convenio;
//        this.getPropertyChangeSupport().firePropertyChange ("convenio", convenioOld, convenio);
	}



	/**
	 * Return the value associated with the column: cd_leito
	 */
	public br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto getLeitoQuarto () {
		return getPropertyValue(this, leitoQuarto, PROP_LEITO_QUARTO); 
	}

	/**
	 * Set the value related to the column: cd_leito
	 * @param leitoQuarto the cd_leito value
	 */
	public void setLeitoQuarto (br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto leitoQuarto) {
//        br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto leitoQuartoOld = this.leitoQuarto;
		this.leitoQuarto = leitoQuarto;
//        this.getPropertyChangeSupport().firePropertyChange ("leitoQuarto", leitoQuartoOld, leitoQuarto);
	}



	/**
	 * Return the value associated with the column: cd_profissional_alta
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissionalAlta () {
		return getPropertyValue(this, profissionalAlta, PROP_PROFISSIONAL_ALTA); 
	}

	/**
	 * Set the value related to the column: cd_profissional_alta
	 * @param profissionalAlta the cd_profissional_alta value
	 */
	public void setProfissionalAlta (br.com.ksisolucoes.vo.cadsus.Profissional profissionalAlta) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalAltaOld = this.profissionalAlta;
		this.profissionalAlta = profissionalAlta;
//        this.getPropertyChangeSupport().firePropertyChange ("profissionalAlta", profissionalAltaOld, profissionalAlta);
	}



	/**
	 * Return the value associated with the column: cd_elo_ult_dieta
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.EloTipoDietaReceituario getEloTipoDietaReceituario () {
		return getPropertyValue(this, eloTipoDietaReceituario, PROP_ELO_TIPO_DIETA_RECEITUARIO); 
	}

	/**
	 * Set the value related to the column: cd_elo_ult_dieta
	 * @param eloTipoDietaReceituario the cd_elo_ult_dieta value
	 */
	public void setEloTipoDietaReceituario (br.com.ksisolucoes.vo.prontuario.basico.EloTipoDietaReceituario eloTipoDietaReceituario) {
//        br.com.ksisolucoes.vo.prontuario.basico.EloTipoDietaReceituario eloTipoDietaReceituarioOld = this.eloTipoDietaReceituario;
		this.eloTipoDietaReceituario = eloTipoDietaReceituario;
//        this.getPropertyChangeSupport().firePropertyChange ("eloTipoDietaReceituario", eloTipoDietaReceituarioOld, eloTipoDietaReceituario);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: tp_atend_faturamento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento getTipoAtendimentoFaturamento () {
		return getPropertyValue(this, tipoAtendimentoFaturamento, PROP_TIPO_ATENDIMENTO_FATURAMENTO); 
	}

	/**
	 * Set the value related to the column: tp_atend_faturamento
	 * @param tipoAtendimentoFaturamento the tp_atend_faturamento value
	 */
	public void setTipoAtendimentoFaturamento (br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento tipoAtendimentoFaturamento) {
//        br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento tipoAtendimentoFaturamentoOld = this.tipoAtendimentoFaturamento;
		this.tipoAtendimentoFaturamento = tipoAtendimentoFaturamento;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAtendimentoFaturamento", tipoAtendimentoFaturamentoOld, tipoAtendimentoFaturamento);
	}



	/**
	 * Return the value associated with the column: cd_usuario_cadsus_acompanhante
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsusAcompanhante getUsuarioCadsusAcompanhante () {
		return getPropertyValue(this, usuarioCadsusAcompanhante, PROP_USUARIO_CADSUS_ACOMPANHANTE); 
	}

	/**
	 * Set the value related to the column: cd_usuario_cadsus_acompanhante
	 * @param usuarioCadsusAcompanhante the cd_usuario_cadsus_acompanhante value
	 */
	public void setUsuarioCadsusAcompanhante (br.com.ksisolucoes.vo.cadsus.UsuarioCadsusAcompanhante usuarioCadsusAcompanhante) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsusAcompanhante usuarioCadsusAcompanhanteOld = this.usuarioCadsusAcompanhante;
		this.usuarioCadsusAcompanhante = usuarioCadsusAcompanhante;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsusAcompanhante", usuarioCadsusAcompanhanteOld, usuarioCadsusAcompanhante);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao atendimentoInformacao = (br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao) obj;
			if (null == this.getCodigo() || null == atendimentoInformacao.getCodigo()) return false;
			else return (this.getCodigo().equals(atendimentoInformacao.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }

    public java.lang.Long getAtendimentoRN() {
        return atendimentoRN;
    }

    public void setAtendimentoRN(java.lang.Long atendimentoRN) {
        this.atendimentoRN = atendimentoRN;
    }

    public java.lang.Long getIndicadorAcidente() {
        return indicadorAcidente;
    }

    public void setIndicadorAcidente(java.lang.Long indicadorAcidente) {
        this.indicadorAcidente = indicadorAcidente;
    }
}