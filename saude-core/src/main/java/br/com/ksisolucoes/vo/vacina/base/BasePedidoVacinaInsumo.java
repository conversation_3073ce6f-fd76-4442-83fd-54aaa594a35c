package br.com.ksisolucoes.vo.vacina.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the pedido_vacina_insumo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="pedido_vacina_insumo"
 */

public abstract class BasePedidoVacinaInsumo extends BaseRootVO implements Serializable {

	public static String REF = "PedidoVacinaInsumo";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_DATA_ENCAMINHAMENTO = "dataEncaminhamento";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_TIPO_PEDIDO = "tipoPedido";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_TIPO_PEDIDO_VACINA = "tipoPedidoVacina";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_STATUS = "status";
	public static final String PROP_DATA_USUARIO = "dataUsuario";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_USUARIO_ENCAMINHAMENTO = "usuarioEncaminhamento";
	public static final String PROP_NUMERO_PEDIDO = "numeroPedido";


	// constructors
	public BasePedidoVacinaInsumo () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BasePedidoVacinaInsumo (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BasePedidoVacinaInsumo (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.Long numeroPedido,
		java.util.Date dataCadastro,
		java.lang.Long status,
		java.lang.Long tipoPedido,
		java.util.Date dataUsuario) {

		this.setCodigo(codigo);
		this.setEmpresa(empresa);
		this.setUsuario(usuario);
		this.setNumeroPedido(numeroPedido);
		this.setDataCadastro(dataCadastro);
		this.setStatus(status);
		this.setTipoPedido(tipoPedido);
		this.setDataUsuario(dataUsuario);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long numeroPedido;
	private java.util.Date dataCadastro;
	private java.lang.Long status;
	private java.lang.Long tipoPedido;
	private java.util.Date dataUsuario;
	private java.util.Date dataCancelamento;
	private java.util.Date dataEncaminhamento;
	private java.lang.String observacao;
	private java.lang.Long tipoPedidoVacina;

	// many to one
	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioEncaminhamento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_ped_vacina"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: num_pedido
	 */
	public java.lang.Long getNumeroPedido () {
		return getPropertyValue(this, numeroPedido, PROP_NUMERO_PEDIDO); 
	}

	/**
	 * Set the value related to the column: num_pedido
	 * @param numeroPedido the num_pedido value
	 */
	public void setNumeroPedido (java.lang.Long numeroPedido) {
//        java.lang.Long numeroPedidoOld = this.numeroPedido;
		this.numeroPedido = numeroPedido;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroPedido", numeroPedidoOld, numeroPedido);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: tp_pedido
	 */
	public java.lang.Long getTipoPedido () {
		return getPropertyValue(this, tipoPedido, PROP_TIPO_PEDIDO); 
	}

	/**
	 * Set the value related to the column: tp_pedido
	 * @param tipoPedido the tp_pedido value
	 */
	public void setTipoPedido (java.lang.Long tipoPedido) {
//        java.lang.Long tipoPedidoOld = this.tipoPedido;
		this.tipoPedido = tipoPedido;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoPedido", tipoPedidoOld, tipoPedido);
	}



	/**
	 * Return the value associated with the column: dt_usuario
	 */
	public java.util.Date getDataUsuario () {
		return getPropertyValue(this, dataUsuario, PROP_DATA_USUARIO); 
	}

	/**
	 * Set the value related to the column: dt_usuario
	 * @param dataUsuario the dt_usuario value
	 */
	public void setDataUsuario (java.util.Date dataUsuario) {
//        java.util.Date dataUsuarioOld = this.dataUsuario;
		this.dataUsuario = dataUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsuario", dataUsuarioOld, dataUsuario);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: dt_encaminhamento
	 */
	public java.util.Date getDataEncaminhamento () {
		return getPropertyValue(this, dataEncaminhamento, PROP_DATA_ENCAMINHAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_encaminhamento
	 * @param dataEncaminhamento the dt_encaminhamento value
	 */
	public void setDataEncaminhamento (java.util.Date dataEncaminhamento) {
//        java.util.Date dataEncaminhamentoOld = this.dataEncaminhamento;
		this.dataEncaminhamento = dataEncaminhamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEncaminhamento", dataEncaminhamentoOld, dataEncaminhamento);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: tipo_pedido_vacina
	 */
	public java.lang.Long getTipoPedidoVacina () {
		return getPropertyValue(this, tipoPedidoVacina, PROP_TIPO_PEDIDO_VACINA); 
	}

	/**
	 * Set the value related to the column: tipo_pedido_vacina
	 * @param tipoPedidoVacina the tipo_pedido_vacina value
	 */
	public void setTipoPedidoVacina (java.lang.Long tipoPedidoVacina) {
//        java.lang.Long tipoPedidoVacinaOld = this.tipoPedidoVacina;
		this.tipoPedidoVacina = tipoPedidoVacina;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoPedidoVacina", tipoPedidoVacinaOld, tipoPedidoVacina);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_usuario_can
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_can
	 * @param usuarioCancelamento the cd_usuario_can value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}



	/**
	 * Return the value associated with the column: cd_usu_encaminhamento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioEncaminhamento () {
		return getPropertyValue(this, usuarioEncaminhamento, PROP_USUARIO_ENCAMINHAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usu_encaminhamento
	 * @param usuarioEncaminhamento the cd_usu_encaminhamento value
	 */
	public void setUsuarioEncaminhamento (br.com.ksisolucoes.vo.controle.Usuario usuarioEncaminhamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioEncaminhamentoOld = this.usuarioEncaminhamento;
		this.usuarioEncaminhamento = usuarioEncaminhamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioEncaminhamento", usuarioEncaminhamentoOld, usuarioEncaminhamento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vacina.PedidoVacinaInsumo)) return false;
		else {
			br.com.ksisolucoes.vo.vacina.PedidoVacinaInsumo pedidoVacinaInsumo = (br.com.ksisolucoes.vo.vacina.PedidoVacinaInsumo) obj;
			if (null == this.getCodigo() || null == pedidoVacinaInsumo.getCodigo()) return false;
			else return (this.getCodigo().equals(pedidoVacinaInsumo.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}