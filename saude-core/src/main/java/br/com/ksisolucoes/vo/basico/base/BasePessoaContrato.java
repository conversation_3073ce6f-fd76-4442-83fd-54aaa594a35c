package br.com.ksisolucoes.vo.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the pessoa_contrato table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="pessoa_contrato"
 */

public abstract class BasePessoaContrato extends BaseRootVO implements Serializable {

	public static String REF = "PessoaContrato";
	public static final String PROP_DATA_VALIDADE = "dataValidade";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_SITUACAO = "situacao";
	public static final String PROP_DATA_CONTRATO = "dataContrato";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_NUMERO_CONTRATO = "numeroContrato";
	public static final String PROP_PESSOA = "pessoa";


	// constructors
	public BasePessoaContrato () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BasePessoaContrato (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BasePessoaContrato (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.Long situacao) {

		this.setCodigo(codigo);
		this.setUsuario(usuario);
		this.setSituacao(situacao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String numeroContrato;
	private java.util.Date dataContrato;
	private java.util.Date dataValidade;
	private java.lang.Long situacao;
	private java.util.Date dataCadastro;

	// many to one
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.basico.Pessoa pessoa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_pessoa_contrato"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: nr_contrato
	 */
	public java.lang.String getNumeroContrato () {
		return getPropertyValue(this, numeroContrato, PROP_NUMERO_CONTRATO); 
	}

	/**
	 * Set the value related to the column: nr_contrato
	 * @param numeroContrato the nr_contrato value
	 */
	public void setNumeroContrato (java.lang.String numeroContrato) {
//        java.lang.String numeroContratoOld = this.numeroContrato;
		this.numeroContrato = numeroContrato;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroContrato", numeroContratoOld, numeroContrato);
	}



	/**
	 * Return the value associated with the column: dt_contrato
	 */
	public java.util.Date getDataContrato () {
		return getPropertyValue(this, dataContrato, PROP_DATA_CONTRATO); 
	}

	/**
	 * Set the value related to the column: dt_contrato
	 * @param dataContrato the dt_contrato value
	 */
	public void setDataContrato (java.util.Date dataContrato) {
//        java.util.Date dataContratoOld = this.dataContrato;
		this.dataContrato = dataContrato;
//        this.getPropertyChangeSupport().firePropertyChange ("dataContrato", dataContratoOld, dataContrato);
	}



	/**
	 * Return the value associated with the column: dt_validade
	 */
	public java.util.Date getDataValidade () {
		return getPropertyValue(this, dataValidade, PROP_DATA_VALIDADE); 
	}

	/**
	 * Set the value related to the column: dt_validade
	 * @param dataValidade the dt_validade value
	 */
	public void setDataValidade (java.util.Date dataValidade) {
//        java.util.Date dataValidadeOld = this.dataValidade;
		this.dataValidade = dataValidade;
//        this.getPropertyChangeSupport().firePropertyChange ("dataValidade", dataValidadeOld, dataValidade);
	}



	/**
	 * Return the value associated with the column: situacao
	 */
	public java.lang.Long getSituacao () {
		return getPropertyValue(this, situacao, PROP_SITUACAO); 
	}

	/**
	 * Set the value related to the column: situacao
	 * @param situacao the situacao value
	 */
	public void setSituacao (java.lang.Long situacao) {
//        java.lang.Long situacaoOld = this.situacao;
		this.situacao = situacao;
//        this.getPropertyChangeSupport().firePropertyChange ("situacao", situacaoOld, situacao);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cod_pessoa
	 */
	public br.com.ksisolucoes.vo.basico.Pessoa getPessoa () {
		return getPropertyValue(this, pessoa, PROP_PESSOA); 
	}

	/**
	 * Set the value related to the column: cod_pessoa
	 * @param pessoa the cod_pessoa value
	 */
	public void setPessoa (br.com.ksisolucoes.vo.basico.Pessoa pessoa) {
//        br.com.ksisolucoes.vo.basico.Pessoa pessoaOld = this.pessoa;
		this.pessoa = pessoa;
//        this.getPropertyChangeSupport().firePropertyChange ("pessoa", pessoaOld, pessoa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.PessoaContrato)) return false;
		else {
			br.com.ksisolucoes.vo.basico.PessoaContrato pessoaContrato = (br.com.ksisolucoes.vo.basico.PessoaContrato) obj;
			if (null == this.getCodigo() || null == pessoaContrato.getCodigo()) return false;
			else return (this.getCodigo().equals(pessoaContrato.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}