package br.com.ksisolucoes.vo.vigilancia.externo;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import br.com.ksisolucoes.vo.vigilancia.externo.base.BasePerfilUsuarioExternoVigilancia;

import java.io.Serializable;


public class PerfilUsuarioExternoVigilancia extends BasePerfilUsuarioExternoVigilancia implements CodigoManager, PesquisaObjectInterface {
    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public PerfilUsuarioExternoVigilancia() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public PerfilUsuarioExternoVigilancia(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public PerfilUsuarioExternoVigilancia(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
            java.util.Date dataCadastro) {

        super(
                codigo,
                usuarioCadastro,
                dataCadastro);
    }

    @Override
    public String getDescricaoVO() {
        return this.getDescricao();
    }

    @Override
    public String getIdentificador() {
        return this.getCodigo().toString();
    }

    public static enum TipoPessoa implements IEnum<TipoPessoa> {

        APESSOA_FISICA(0L, Images.PESSOA_FISICA, Bundle.getStringApplication("rotulo_fisica")),
        PESSOA_JURIDICA(1L, Images.PESSOA_JURIDICA, Bundle.getStringApplication("rotulo_juridica"));

        private Long value;
        private String descricao;
        private Images images;

        private TipoPessoa(Long value, Images images, String descricao) {
            this.value = value;
            this.images = images;
            this.descricao = descricao;
        }

        public static TipoPessoa valueOf(Long value) {
            for (TipoPessoa tipoPessoa : TipoPessoa.values()) {
                if (tipoPessoa.value().equals(value)) {
                    return tipoPessoa;
                }
            }
            return null;
        }

        public Images getImages() {
            return images;
        }

        @Override
        public Long value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    public TipoPessoa getEnumTipoPessoa() {
        TipoPessoa tipoPessoa = TipoPessoa.valueOf(getTipoPessoa());
        if (tipoPessoa != null) {
            return tipoPessoa;
        }
        return null;
    }

    public enum Images {

        PESSOA_FISICA("images/css/icons48/people.png"),
        PESSOA_JURIDICA("images/css/icons48/building.png");

        private String path;

        private Images(String path) {
            this.path = path;
        }

        public String getPath() {
            return path;
        }

    }

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}