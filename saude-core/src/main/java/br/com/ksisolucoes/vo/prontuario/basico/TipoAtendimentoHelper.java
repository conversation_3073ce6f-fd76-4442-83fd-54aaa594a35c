/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento.TiposAtendimento;

/**
 *
 * <AUTHOR>
 */
public class TipoAtendimentoHelper {

    public static enum TipoAtendimentoGrupo implements Serializable{
        GRUPO_ENFERMAGEM(0L),
        GRUPO_ODONTOLOGIA(1L),
        GRUPO_MEDICO(2L),
        GRUPO_EXAME(3L),
        GRUPO_INTERNACAO(4L),
        GRUPO_ACOMPANHAMENTO_MEDICO(5L);

        private Long value;
        private TipoAtendimentoGrupo(Long value) {
            this.value = value;
        }
//        public Long value() {
//            return value;
//        }
    }

    public static ArrayList<TiposAtendimento> getTipoAtendimentoGrupo(TipoAtendimentoGrupo grupo){
        if(TipoAtendimentoGrupo.GRUPO_ENFERMAGEM.equals(grupo)){
            return getTipoAtendimentoGrupoEnfermagem();
        }else if(TipoAtendimentoGrupo.GRUPO_ODONTOLOGIA.equals(grupo)){
            return getTipoAtendimentoGrupoOdontologia();
        }else if(TipoAtendimentoGrupo.GRUPO_MEDICO.equals(grupo)){
            return getTipoAtendimentoGrupoMedico();
        }else if(TipoAtendimentoGrupo.GRUPO_ACOMPANHAMENTO_MEDICO.equals(grupo)){
            return getTipoAtendimentoGrupoAcompanhamentoMedico();
        }else if(TipoAtendimentoGrupo.GRUPO_EXAME.equals(grupo)){
            return getTipoAtendimentoGrupoExame();
        }else if(TipoAtendimentoGrupo.GRUPO_INTERNACAO.equals(grupo)){
            return getTipoAtendimentoGrupoInternacao();
        }
        return null;
    }

    public static ArrayList<Long> getTipoAtendimentoGrupoValues(TipoAtendimentoGrupo grupo){
        List<TiposAtendimento> list = null;
        ArrayList<Long> values = null;
        if(TipoAtendimentoGrupo.GRUPO_ENFERMAGEM.equals(grupo)){
            list = getTipoAtendimentoGrupoEnfermagem();
        }else if(TipoAtendimentoGrupo.GRUPO_ODONTOLOGIA.equals(grupo)){
            list = getTipoAtendimentoGrupoOdontologia();
        }else if(TipoAtendimentoGrupo.GRUPO_MEDICO.equals(grupo)){
            list = getTipoAtendimentoGrupoMedico();
        }else if(TipoAtendimentoGrupo.GRUPO_ACOMPANHAMENTO_MEDICO.equals(grupo)){
            list = getTipoAtendimentoGrupoAcompanhamentoMedico();
        }else if(TipoAtendimentoGrupo.GRUPO_EXAME.equals(grupo)){
            list = getTipoAtendimentoGrupoExame();
        }else if(TipoAtendimentoGrupo.GRUPO_INTERNACAO.equals(grupo)){
            list = getTipoAtendimentoGrupoInternacao();
        }

        if(CollectionUtils.isNotNullEmpty(list)){
            values = new ArrayList<Long>();
            for (TiposAtendimento object : list) {
                values.add(object.value());
            }
        }

        return values;
    }

    public static boolean containsTipoAtendimentoGrupo(TipoAtendimentoGrupo tipoAtendimentoGrupo, Long tipoAtendimento){
        for (Long l : getTipoAtendimentoGrupoValues(tipoAtendimentoGrupo)) {
            if(l.equals(tipoAtendimento)){
                return true;
            }
        }

        return false;
    }

    private static ArrayList<TiposAtendimento> getTipoAtendimentoGrupoEnfermagem(){
        ArrayList<TiposAtendimento> list = new ArrayList();
        list.add(TiposAtendimento.ENFERMAGEM);
        list.add(TiposAtendimento.PRIMARIO);
        list.add(TiposAtendimento.FECHAMENTO_PEQUENA_CIRURGIA);
        list.add(TiposAtendimento.AVALIACAO_PEQUENA_CIRURGIA);
        list.add(TiposAtendimento.OBSERVACAO);
        list.add(TiposAtendimento.ALTA_EMERGENCIA);
        list.add(TiposAtendimento.TRANSFERENCIA_INTERNACAO);
        list.add(TiposAtendimento.EMERGENCIA);

        return list;
    }

    private static ArrayList<TiposAtendimento> getTipoAtendimentoGrupoMedico(){
        ArrayList<TiposAtendimento> list = new ArrayList();
        list.add(TiposAtendimento.CLINICA_GERAL);
        list.add(TiposAtendimento.RETORNO);
        list.add(TiposAtendimento.ATENDIMENTO_PEQUENA_CIRURGIA);
        list.add(TiposAtendimento.EMERGENCIA);
        list.add(TiposAtendimento.OBSERVACAO);

        return list;
    }

    private static ArrayList<TiposAtendimento> getTipoAtendimentoGrupoAcompanhamentoMedico(){
        ArrayList<TiposAtendimento> list = new ArrayList();
        list.add(TiposAtendimento.CLINICA_GERAL);
        list.add(TiposAtendimento.RETORNO);
        list.add(TiposAtendimento.INTERNACAO);
        list.add(TiposAtendimento.EMERGENCIA);
        list.add(TiposAtendimento.OBSERVACAO);

        return list;
    }

    private static ArrayList<TiposAtendimento> getTipoAtendimentoGrupoExame() {
        ArrayList<TiposAtendimento> list = new ArrayList();
        list.add(TiposAtendimento.EXAME);

        return list;
    }

    private static ArrayList<TiposAtendimento> getTipoAtendimentoGrupoInternacao() {
        ArrayList<TiposAtendimento> list = new ArrayList();
        list.add(TiposAtendimento.OBSTETRICIA);
        list.add(TiposAtendimento.INTERNACAO);
        list.add(TiposAtendimento.ALTA_INTERNACAO);

        return list;
    }

    private static ArrayList<TiposAtendimento> getTipoAtendimentoGrupoOdontologia(){
        ArrayList<TiposAtendimento> list = new ArrayList();
        list.add(TiposAtendimento.ODONTOLOGIA);
        list.add(TiposAtendimento.ACOMPANHAMENTO_ODONTOLOGICO);

        return list;
    }

}