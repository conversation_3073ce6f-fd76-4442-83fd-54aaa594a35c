<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.cadsus"  >
    <class name="UsuarioCadsusAcompanhanteDocumento" table="usuario_cadsus_acompanhante_documento" >
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_usuario_cadsus_acompanhante_documento"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />
        
        <many-to-one 
            name="usuarioCadsusAcompanhante"
            class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsusAcompanhante"
            column="cd_usuario_cadsus_acompanhante"
            not-null="true"
	/>
        
        <many-to-one 
            name="orgaoEmissor"
            class="br.com.ksisolucoes.vo.basico.OrgaoEmissor"
            column="cd_orgao_emissor"
            not-null="false"
	/>
        
        <property 
            name="numeroDocumento"
            column="nr_documento"
            type="java.lang.String"
            length="13"
	/>
	
	<property 
            name="numeroDocumentoComplementar"
            column="nr_documento_complementar"
            type="java.lang.String"
            length="4"
	/>
        
        <property 
            name="siglaUf"
            column="sg_uf"
            type="java.lang.String"
            length="2"
	/>
        
        <property 
            name="dataEmissao"
            column="dt_emissao"
            type="java.util.Date"
	/>

    </class>
</hibernate-mapping>
