package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the start_back_screening_tool table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="start_back_screening_tool"
 */

public abstract class BaseStartBackScreeningTool extends BaseRootVO implements Serializable {

	public static String REF = "StartBackScreeningTool";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_ITEM8 = "item8";
	public static final String PROP_ITEM7 = "item7";
	public static final String PROP_ITEM9 = "item9";
	public static final String PROP_ITEM4 = "item4";
	public static final String PROP_ITEM3 = "item3";
	public static final String PROP_ITEM6 = "item6";
	public static final String PROP_ATENDIMENTO = "atendimento";
	public static final String PROP_ITEM5 = "item5";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_ITEM1 = "item1";
	public static final String PROP_DATA_ALTERACAO = "dataAlteracao";
	public static final String PROP_ITEM2 = "item2";
	public static final String PROP_RESULTADO = "resultado";


	// constructors
	public BaseStartBackScreeningTool () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseStartBackScreeningTool (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseStartBackScreeningTool (
		java.lang.Long codigo,
		java.util.Date dataCadastro,
		java.util.Date dataAlteracao) {

		this.setCodigo(codigo);
		this.setDataCadastro(dataCadastro);
		this.setDataAlteracao(dataAlteracao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long item1;
	private java.lang.Long item2;
	private java.lang.Long item3;
	private java.lang.Long item4;
	private java.lang.Long item5;
	private java.lang.Long item6;
	private java.lang.Long item7;
	private java.lang.Long item8;
	private java.lang.Long item9;
	private java.util.Date dataCadastro;
	private java.util.Date dataAlteracao;
	private java.lang.String resultado;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_start_back_screening_tool"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: item_1
	 */
	public java.lang.Long getItem1 () {
		return getPropertyValue(this, item1, PROP_ITEM1); 
	}

	/**
	 * Set the value related to the column: item_1
	 * @param item1 the item_1 value
	 */
	public void setItem1 (java.lang.Long item1) {
//        java.lang.Long item1Old = this.item1;
		this.item1 = item1;
//        this.getPropertyChangeSupport().firePropertyChange ("item1", item1Old, item1);
	}



	/**
	 * Return the value associated with the column: item_2
	 */
	public java.lang.Long getItem2 () {
		return getPropertyValue(this, item2, PROP_ITEM2); 
	}

	/**
	 * Set the value related to the column: item_2
	 * @param item2 the item_2 value
	 */
	public void setItem2 (java.lang.Long item2) {
//        java.lang.Long item2Old = this.item2;
		this.item2 = item2;
//        this.getPropertyChangeSupport().firePropertyChange ("item2", item2Old, item2);
	}



	/**
	 * Return the value associated with the column: item_3
	 */
	public java.lang.Long getItem3 () {
		return getPropertyValue(this, item3, PROP_ITEM3); 
	}

	/**
	 * Set the value related to the column: item_3
	 * @param item3 the item_3 value
	 */
	public void setItem3 (java.lang.Long item3) {
//        java.lang.Long item3Old = this.item3;
		this.item3 = item3;
//        this.getPropertyChangeSupport().firePropertyChange ("item3", item3Old, item3);
	}



	/**
	 * Return the value associated with the column: item_4
	 */
	public java.lang.Long getItem4 () {
		return getPropertyValue(this, item4, PROP_ITEM4); 
	}

	/**
	 * Set the value related to the column: item_4
	 * @param item4 the item_4 value
	 */
	public void setItem4 (java.lang.Long item4) {
//        java.lang.Long item4Old = this.item4;
		this.item4 = item4;
//        this.getPropertyChangeSupport().firePropertyChange ("item4", item4Old, item4);
	}



	/**
	 * Return the value associated with the column: item_5
	 */
	public java.lang.Long getItem5 () {
		return getPropertyValue(this, item5, PROP_ITEM5); 
	}

	/**
	 * Set the value related to the column: item_5
	 * @param item5 the item_5 value
	 */
	public void setItem5 (java.lang.Long item5) {
//        java.lang.Long item5Old = this.item5;
		this.item5 = item5;
//        this.getPropertyChangeSupport().firePropertyChange ("item5", item5Old, item5);
	}



	/**
	 * Return the value associated with the column: item_6
	 */
	public java.lang.Long getItem6 () {
		return getPropertyValue(this, item6, PROP_ITEM6); 
	}

	/**
	 * Set the value related to the column: item_6
	 * @param item6 the item_6 value
	 */
	public void setItem6 (java.lang.Long item6) {
//        java.lang.Long item6Old = this.item6;
		this.item6 = item6;
//        this.getPropertyChangeSupport().firePropertyChange ("item6", item6Old, item6);
	}



	/**
	 * Return the value associated with the column: item_7
	 */
	public java.lang.Long getItem7 () {
		return getPropertyValue(this, item7, PROP_ITEM7); 
	}

	/**
	 * Set the value related to the column: item_7
	 * @param item7 the item_7 value
	 */
	public void setItem7 (java.lang.Long item7) {
//        java.lang.Long item7Old = this.item7;
		this.item7 = item7;
//        this.getPropertyChangeSupport().firePropertyChange ("item7", item7Old, item7);
	}



	/**
	 * Return the value associated with the column: item_8
	 */
	public java.lang.Long getItem8 () {
		return getPropertyValue(this, item8, PROP_ITEM8); 
	}

	/**
	 * Set the value related to the column: item_8
	 * @param item8 the item_8 value
	 */
	public void setItem8 (java.lang.Long item8) {
//        java.lang.Long item8Old = this.item8;
		this.item8 = item8;
//        this.getPropertyChangeSupport().firePropertyChange ("item8", item8Old, item8);
	}



	/**
	 * Return the value associated with the column: item_9
	 */
	public java.lang.Long getItem9 () {
		return getPropertyValue(this, item9, PROP_ITEM9); 
	}

	/**
	 * Set the value related to the column: item_9
	 * @param item9 the item_9 value
	 */
	public void setItem9 (java.lang.Long item9) {
//        java.lang.Long item9Old = this.item9;
		this.item9 = item9;
//        this.getPropertyChangeSupport().firePropertyChange ("item9", item9Old, item9);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_alteracao
	 */
	public java.util.Date getDataAlteracao () {
		return getPropertyValue(this, dataAlteracao, PROP_DATA_ALTERACAO); 
	}

	/**
	 * Set the value related to the column: dt_alteracao
	 * @param dataAlteracao the dt_alteracao value
	 */
	public void setDataAlteracao (java.util.Date dataAlteracao) {
//        java.util.Date dataAlteracaoOld = this.dataAlteracao;
		this.dataAlteracao = dataAlteracao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAlteracao", dataAlteracaoOld, dataAlteracao);
	}



	/**
	 * Return the value associated with the column: resultado
	 */
	public java.lang.String getResultado () {
		return getPropertyValue(this, resultado, PROP_RESULTADO); 
	}

	/**
	 * Set the value related to the column: resultado
	 * @param resultado the resultado value
	 */
	public void setResultado (java.lang.String resultado) {
//        java.lang.String resultadoOld = this.resultado;
		this.resultado = resultado;
//        this.getPropertyChangeSupport().firePropertyChange ("resultado", resultadoOld, resultado);
	}



	/**
	 * Return the value associated with the column: nr_atendimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Atendimento getAtendimento () {
		return getPropertyValue(this, atendimento, PROP_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: nr_atendimento
	 * @param atendimento the nr_atendimento value
	 */
	public void setAtendimento (br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoOld = this.atendimento;
		this.atendimento = atendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimento", atendimentoOld, atendimento);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.StartBackScreeningTool)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.StartBackScreeningTool startBackScreeningTool = (br.com.ksisolucoes.vo.prontuario.basico.StartBackScreeningTool) obj;
			if (null == this.getCodigo() || null == startBackScreeningTool.getCodigo()) return false;
			else return (this.getCodigo().equals(startBackScreeningTool.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}