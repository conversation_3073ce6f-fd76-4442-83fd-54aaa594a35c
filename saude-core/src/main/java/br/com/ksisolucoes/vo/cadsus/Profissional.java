package br.com.ksisolucoes.vo.cadsus;

import br.com.celk.integracao.IntegracaoRest;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.parametrogem.DefaultPanelConsultaBean;
import br.com.ksisolucoes.util.parametrogem.ParametroPanelConsultaBean;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.base.BaseProfissional;
import br.com.ksisolucoes.vo.esus.CboFichaEsus;
import br.com.ksisolucoes.vo.esus.CboFichaEsusItem;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.DictionaryData;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import ch.lambdaj.Lambda;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.commons.lang3.StringUtils;

import javax.swing.text.MaskFormatter;
import java.io.Serializable;
import java.text.ParseException;
import java.util.Date;
import java.util.List;

@ParametroPanelConsultaBean("br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional")
@DefaultPanelConsultaBean("br.com.ksisolucoes.gui.cadsus.consulta.panel.PnlConsultaProfissional")
@DictionaryData(referenceField = "referencia")
@IntegracaoRest
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class Profissional extends BaseProfissional implements CodigoManager, PesquisaObjectInterface, Comparable<Profissional> {

    private static final long serialVersionUID = 1L;
    public static final String TIPO_EXTERNO = "E";
    public static String PROP_DESCRICAO_FORMATADA = "descricaoFormatado";
    public static String PROP_PROFESSIONAL_EMPRESA = "professionalEmpresa";
    public static String PROP_CNS_FORMATADO = "cnsFormatado";
    public static String PROP_TELEFONE_FORMATADO = "telefoneFormatado";

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public Profissional () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public Profissional (Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public Profissional (
		Long codigo,
		String nome) {

		super (
			codigo,
			nome);
	}

    /*[CONSTRUCTOR MARKER END]*/


    public enum TipoEscolaridade implements IEnum<TipoEscolaridade> {

        FUNDAMENTAL_INCONPLETO(1L, Bundle.getStringApplication("escolaridade_fundamental_incompleto")),
        FUNDAMENTAL(2L, Bundle.getStringApplication("escolaridade_fundamental")),
        MEDIO_INCOMPLETO(3L, Bundle.getStringApplication("escolaridade_medio_incompleto")),
        MEDIO(4L, Bundle.getStringApplication("escolaridade_medio")),
        SUPERIOR_INCOMPLETO(5L, Bundle.getStringApplication("escolaridade_superior_incompleto")),
        GRADUACAO(6L, Bundle.getStringApplication("escolaridade_graduacao")),
        MESTRADO(8L, Bundle.getStringApplication("escolaridade_mestrado")),
        DOUTORADO(9L, Bundle.getStringApplication("escolaridade_doutorado")),
        POS_DOUTORADO(10L, Bundle.getStringApplication("escolaridade_pos_doutorado")),
        ESPECIALIZACAO(11L, Bundle.getStringApplication("escolaridade_especializacao")),
        MEDIO_PROFISSIONALIZANTE(12L, Bundle.getStringApplication("escolaridade_medio_profissionalizante")),
        POS_GRADUACAO(13L, Bundle.getStringApplication("escolaridade_pos_graduacao"));

        private final Long value;
        private final String descricao;

        TipoEscolaridade(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    public enum EstadoCivil implements IEnum<EstadoCivil> {

        SOLTEIRO(1L, Bundle.getStringApplication("estado_civil_solteiro")),
        CASADO(2L, Bundle.getStringApplication("estado_civil_casado")),
        DIVORCIADO(3L, Bundle.getStringApplication("estado_civil_divorciado")),
        VIUVO(5L, Bundle.getStringApplication("estado_civil_viuvo")),
        SEPARADO(6L, Bundle.getStringApplication("estado_civil_separado")),
        UNIAO_ESTAVEL(7L, Bundle.getStringApplication("estado_civil_uniao_estavel")),
        ;

        private final Long value;
        private final String descricao;

        EstadoCivil(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    public enum VinculoProfissional implements IEnum<VinculoProfissional> {
        COMISSIONADO(1L, Bundle.getStringApplication("vinculo_comissionado")),
        EFETIVO_TRANSITORIO(2L, Bundle.getStringApplication("vinculo_efetivo_transitorio")),
        EFETIVO_PERMANENTE(3L, Bundle.getStringApplication("vinculo_efetivo_permanente")),
        CONTRATADO(4L, Bundle.getStringApplication("vinculo_contratado")),
        TERCEIRIZADO(5L, Bundle.getStringApplication("vinculo_terceirizado")),
        CELETISTA(6L, Bundle.getStringApplication("vinculo_celetista")),
        EFETIVO_COMISSIONADO(7L, Bundle.getStringApplication("vinculo_efetivo_comissionado")),
        CEDIDO(8L, Bundle.getStringApplication("vinculo_cedido")),
        ESTAGIARIO(9L, Bundle.getStringApplication("vinculo_estagiario")),
        A_DISPOSICAO(10L, Bundle.getStringApplication("vinculo_a_disposicao")),
        NAO_INFORMADO(12L, Bundle.getStringApplication("vinculo_nao_informado"))
        ;

        VinculoProfissional(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        private final Long value;
        private final String descricao;

        @Override
        public Object value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }


    public void setCodigoManager(Serializable key) {
        this.setCodigo((Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getDescricaoVO() {
        return this.getNome();
    }

    public String getIdentificador() {
        return Coalesce.asString(this.getCodigo()).toString();
    }

    public String getDescricaoFormatado() {
        return getDescricaoFormatado(Coalesce.asString(this.getReferencia(), Coalesce.asString(getCodigo())), getNome());
    }

    public static String getDescricaoFormatado(String codigo, String descricao) {
        return Util.getDescricaoFormatado(
                Coalesce.asString(codigo),
                Coalesce.asString(descricao));
    }

    public String getNomeCnsFormatado() {
        return getNome() + " - " + getCnsFormatado();
    }

    public String getCnsFormatado() {
        if (getCodigoCns() != null && !getCodigoCns().trim().isEmpty()) {
            try {
                MaskFormatter m = new MaskFormatter("###.###.###.###.###");
                m.setValueContainsLiteralCharacters(false);
                return m.valueToString(getCodigoCns());
            } catch (ParseException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        }
        return "";
    }

    public String getTelefoneFormatado() {
        return Util.getTelefoneFormatado(this.getTelefone());
    }

    public String getRegistroFormatado() {
        StringBuilder sb = new StringBuilder();
        if (this.getConselhoClasse() != null) {
            if (this.getConselhoClasse().getSigla() != null && this.getNumeroRegistro() != null) {
                sb.append(this.getConselhoClasse().getSigla());
                if (this.getUnidadeFederacaoConselhoRegistro() != null) {
                    sb.append("/");
                    sb.append(this.getUnidadeFederacaoConselhoRegistro());
                }
                sb.append(": ");
                sb.append(this.getNumeroRegistro());
            }
        }
        return sb.toString();
    }

    public String getReferenciaRegistroFormatado() {
        StringBuilder sb = new StringBuilder();
        if (StringUtils.trimToNull(Coalesce.asString(getReferencia())) != null) {
            sb.append("Mat.: ");
            sb.append(getReferencia());
        }
        String registroFormatado = getRegistroFormatado();
        if (StringUtils.trimToNull(Coalesce.asString(registroFormatado)) != null) {
            sb.append("    ");
            sb.append(registroFormatado);
        }
        return sb.toString();
    }

    public String getCargoFormatado() {
        StringBuilder sb = new StringBuilder();
        if (StringUtils.trimToNull(Coalesce.asString(getCargo())) != null) {
            sb.append("Cargo: ");
            sb.append(getCargo());
        }
        return sb.toString();
    }

    public String getCpfFormatado() {
        if (getCpf() != null) {
            return Util.getCpfFormatado(getCpf());
        }
        return "";
    }

    public String getDescricaoProfissionalComNumeroRegistroFormatado() {
        StringBuilder sb = new StringBuilder();
        sb.append(getNome());

        String registroFormatado = getRegistroFormatado();
        if (registroFormatado != null && !registroFormatado.isEmpty()) {
            sb.append(" - ");
            sb.append(registroFormatado);
        }

        return sb.toString();
    }

    @Override
    public int compareTo(Profissional profissional) {
        if (this == profissional || this.equals(profissional)) {
            return 0;
        }
        return -1;
    }

    public String getDescricaoProfissionalComNumeroRegistroFormatadoCbo() {
        if (getCboProfissional() != null) {
            return getDescricaoProfissionalComNumeroRegistroFormatado() + " CBO: (" + getCboProfissional().getCbo() + ") " + getCboProfissional().getDescricao();
        } else {
            return getDescricaoProfissionalComNumeroRegistroFormatado();
        }
    }

    public TabelaCbo getCboProfissional() {
        return this.getCboProfissional(SessaoAplicacaoImp.getInstance().getEmpresa());
    }

    public List<TabelaCbo> getListCboProfissional(Empresa empresa) {
        if (this != null && empresa != null) {
            Date dataAtual = DataUtil.getDataAtual();

            List<ProfissionalCargaHoraria> cargaHorariaList = LoadManager.getInstance(ProfissionalCargaHoraria.class)
                    .addProperty(ProfissionalCargaHoraria.PROP_CODIGO)
                    .addProperty(VOUtils.montarPath(ProfissionalCargaHoraria.PROP_TABELA_CBO, TabelaCbo.PROP_CBO))
                    .addProperty(VOUtils.montarPath(ProfissionalCargaHoraria.PROP_TABELA_CBO, TabelaCbo.PROP_DESCRICAO))
                    .addProperty(VOUtils.montarPath(ProfissionalCargaHoraria.PROP_TABELA_CBO, TabelaCbo.PROP_NIVEL_ENSINO))
                    .addParameter(new QueryCustom.QueryCustomParameter(ProfissionalCargaHoraria.PROP_DATA_ATIVACAO, BuilderQueryCustom.QueryParameter.MENOR_IGUAL, dataAtual, HQLHelper.NOT_RESOLVE_TYPE, Data.addDias(dataAtual, 1)))
                    .addParameter(new QueryCustom.QueryCustomParameter(ProfissionalCargaHoraria.PROP_DATA_DESATIVACAO, BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, dataAtual, HQLHelper.NOT_RESOLVE_TYPE, dataAtual))
                    .addParameter(new QueryCustom.QueryCustomParameter(ProfissionalCargaHoraria.PROP_TIPO_REGISTRO, BuilderQueryCustom.QueryParameter.DIFERENTE, ProfissionalCargaHoraria.TIPO_REGISTRO_ERRO, HQLHelper.NOT_RESOLVE_TYPE, ProfissionalCargaHoraria.TIPO_REGISTRO_INCLUSAO))
                    .addParameter(new QueryCustom.QueryCustomParameter(ProfissionalCargaHoraria.PROP_EMPRESA, empresa))
                    .addParameter(new QueryCustom.QueryCustomParameter(ProfissionalCargaHoraria.PROP_PROFISSIONAL, this))
                    .start().getList();
            return Lambda.extract(cargaHorariaList, Lambda.on(ProfissionalCargaHoraria.class).getTabelaCbo());
        }
        return null;
    }

    public TabelaCbo getCboProfissional(Empresa empresa) {
        if (this != null && empresa != null) {
            Date dataAtual = DataUtil.getDataAtual();
            List<ProfissionalCargaHoraria> list = LoadManager.getInstance(ProfissionalCargaHoraria.class)
                .addProperty(ProfissionalCargaHoraria.PROP_CODIGO)
                .addProperty(VOUtils.montarPath(ProfissionalCargaHoraria.PROP_TABELA_CBO, TabelaCbo.PROP_CBO))
                .addProperty(VOUtils.montarPath(ProfissionalCargaHoraria.PROP_TABELA_CBO, TabelaCbo.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(ProfissionalCargaHoraria.PROP_TABELA_CBO, TabelaCbo.PROP_NIVEL_ENSINO))
                .addParameter(new QueryCustom.QueryCustomParameter(ProfissionalCargaHoraria.PROP_DATA_ATIVACAO, BuilderQueryCustom.QueryParameter.MENOR_IGUAL, dataAtual, HQLHelper.NOT_RESOLVE_TYPE, Data.addDias(dataAtual, 1)))
                .addParameter(new QueryCustom.QueryCustomParameter(ProfissionalCargaHoraria.PROP_DATA_DESATIVACAO, BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, dataAtual, HQLHelper.NOT_RESOLVE_TYPE, dataAtual))
                .addParameter(new QueryCustom.QueryCustomParameter(ProfissionalCargaHoraria.PROP_TIPO_REGISTRO, BuilderQueryCustom.QueryParameter.DIFERENTE, ProfissionalCargaHoraria.TIPO_REGISTRO_ERRO, HQLHelper.NOT_RESOLVE_TYPE, ProfissionalCargaHoraria.TIPO_REGISTRO_INCLUSAO))
                .addParameter(new QueryCustom.QueryCustomParameter(ProfissionalCargaHoraria.PROP_EMPRESA, empresa))
                .addParameter(new QueryCustom.QueryCustomParameter(ProfissionalCargaHoraria.PROP_PROFISSIONAL, this))
                .start().getList();
            if (CollectionUtils.isNotNullEmpty(list)) {
                return list.get(0).getTabelaCbo();
            }
        }
        return null;
    }

    public TabelaCbo getCboProfissionalVacina(Empresa empresa) {
        if (this != null && empresa != null) {
            List<TabelaCbo> listCboProfissional = this.getListCboProfissional(empresa);
            if (CollectionUtils.isNotNullEmpty(listCboProfissional)) {
                TabelaCbo cbo = getCboValidoEsusVacina(listCboProfissional);
                if (cbo != null) {
                    return  cbo;
                } else {
                    return listCboProfissional.get(0);
                }
            }
        }
        return null;
    }
    public TabelaCbo getCboValidoEsusVacina(List<TabelaCbo> listTabelaCbo) {
        if (CollectionUtils.isNotNullEmpty(listTabelaCbo)) {
            List<CboFichaEsusItem> cboFichaEsusItemList = LoadManager.getInstance(CboFichaEsusItem.class)
                    .addProperties(CboFichaEsusItem.PROP_CODIGO)
                    .addProperties(VOUtils.montarPath(CboFichaEsusItem.PROP_TABELA_CBO, TabelaCbo.PROP_CBO))
                    .addProperties(VOUtils.montarPath(CboFichaEsusItem.PROP_TABELA_CBO, TabelaCbo.PROP_DESCRICAO))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(CboFichaEsusItem.PROP_CBO_FICHA_ESUS, CboFichaEsus.PROP_FICHA), CboFichaEsus.TipoFicha.FICHA_VACINA.value()))
                    .addParameter(new QueryCustom.QueryCustomParameter(CboFichaEsusItem.PROP_TABELA_CBO, BuilderQueryCustom.QueryParameter.IN, listTabelaCbo))
                    .start().getList();
            if (CollectionUtils.isNotNullEmpty(cboFichaEsusItemList)) {
                return cboFichaEsusItemList.get(0).getTabelaCbo();
            }
        }
        return null;
    }

    public String getEnderecoComplementoFormatadoComCidade() {
        StringBuilder sb = new StringBuilder();
        sb.append(getEnderecoComplementoFormatado());

        if (getCidade() != null) {
            sb.append(", ");
            sb.append(br.com.celk.util.Coalesce.asString(getCidade().getDescricao()));
            if (getCidade().getEstado() != null) {
                sb.append(" - ");
                sb.append(br.com.celk.util.Coalesce.asString(getCidade().getEstado().getSigla()));
            }
        }

        return sb.toString();
    }

    public String getEnderecoComplementoFormatado() {
        StringBuilder sb = new StringBuilder();
        sb.append(getRuaFormatadaComComplemento());

        if (this.getBairro() != null) {
            sb.append(", ");
            sb.append(this.getBairro());
        }
        return sb.toString();
    }

    public String getRuaFormatadaComComplemento() {
        StringBuilder sb = new StringBuilder();
        sb.append(getRuaFormatada());

        if (this.getComplemento() != null) {
            sb.append(" ");
            sb.append(this.getComplemento());
        }
        return sb.toString();
    }

    public String getRuaFormatada() {
        StringBuilder sb = new StringBuilder();
        if (this.getTipoLogradouro() != null && this.getTipoLogradouro().getDescricao() != null) {
            sb.append(this.getTipoLogradouro().getDescricao());
            sb.append(" ");
        }
        if (this.getRua() != null) {
            sb.append(this.getRua());
        }
        if (this.getRuaNumero() != null) {
            sb.append(", ");
            sb.append(this.getRuaNumero());
        }
        return sb.toString();
    }

    @Override
    public String getCpf() {
        return lgpdFilterNumberAsText(super.getCpf());
    }

    @Override
    public String getNumeroRegistro() {
        return lgpdFilterNumberAsText(super.getNumeroRegistro());
    }

    @Override
    public String getCodigoCns() {
        return lgpdFilterNumberAsText(super.getCodigoCns());
    }

    @Override
    public String getEmail() {
        return lgpdFilterText(super.getEmail());
    }

    @Override
    public String getNumeroRegistroGeral() {
        return lgpdFilterNumberAsText(super.getNumeroRegistroGeral());
    }

    @Override
    public String getTelefone() {
        return lgpdFilterNumberAsText(super.getTelefone());
    }
}
