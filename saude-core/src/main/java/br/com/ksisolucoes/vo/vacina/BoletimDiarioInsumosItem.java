package br.com.ksisolucoes.vo.vacina;

import java.io.Serializable;

import br.com.ksisolucoes.vo.vacina.base.BaseBoletimDiarioInsumosItem;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class BoletimDiarioInsumosItem extends BaseBoletimDiarioInsumosItem implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public BoletimDiarioInsumosItem () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public BoletimDiarioInsumosItem (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public BoletimDiarioInsumosItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vacina.BoletimDiarioInsumos boletimDiarioInsumos,
		br.com.ksisolucoes.vo.vacina.VacinaAplicacaoInsumo vacinaAplicacaoInsumo,
		java.util.Date dataCadastro) {

		super (
			codigo,
			boletimDiarioInsumos,
			vacinaAplicacaoInsumo,
			dataCadastro);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}