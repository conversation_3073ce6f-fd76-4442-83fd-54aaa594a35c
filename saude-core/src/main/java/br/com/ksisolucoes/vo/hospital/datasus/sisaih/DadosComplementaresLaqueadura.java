package br.com.ksisolucoes.vo.hospital.datasus.sisaih;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import java.io.Serializable;

import br.com.ksisolucoes.vo.hospital.datasus.sisaih.base.BaseDadosComplementaresLaqueadura;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

public class DadosComplementaresLaqueadura extends BaseDadosComplementaresLaqueadura implements CodigoManager {

    private static final long serialVersionUID = 1L;

    public static enum GrauInstrucao implements IEnum {

        ANALFABETO(1L, Bundle.getStringApplication("rotulo_analfabeto")),
        PRIMEIRO_GRAU(2L, Bundle.getStringApplication("rotulo_primeiro_grau")),
        SEGUNDO_GRAU(3L, Bundle.getStringApplication("rotulo_segundo_grau")),
        TERCEIRO_GRAU(4L, Bundle.getStringApplication("rotulo_terceiro_grau"));

        private Long value;
        private String descricao;

        private GrauInstrucao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    public static enum TipoContraceptivo implements IEnum {

        LAM(1L, Bundle.getStringApplication("rotulo_lam")),
        OGINO_KNAUS(2L, Bundle.getStringApplication("rotulo_ogino_knaus")),
        TEMP_BASAL(3L, Bundle.getStringApplication("rotulo_temp_basal")),
        BILLINGS(4L, Bundle.getStringApplication("rotulo_billings")),
        CINTO_TERMICO(5L, Bundle.getStringApplication("rotulo_cinto_termico")),
        DIU(6L, Bundle.getStringApplication("rotulo_diu")),
        DIAFRAGMA(7L, Bundle.getStringApplication("rotulo_diafragma")),
        PRESERVATIVO(8L, Bundle.getStringApplication("rotulo_preservativo")),
        ESPERMICIDA(9L, Bundle.getStringApplication("rotulo_espermicida")),
        HORMONIO_ORAL(10L, Bundle.getStringApplication("rotulo_hormonio_oral")),
        HORMONIO_INJETAVEL(11L, Bundle.getStringApplication("rotulo_hormonio_injetavel")),
        COITO_INTERROMPIDO(12L, Bundle.getStringApplication("rotulo_coito_interrompido"));

        private Long value;
        private String descricao;

        private TipoContraceptivo(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    public static enum GestacaoRisco implements IEnum {

        SIM(0L, Bundle.getStringApplication("rotulo_sim")),
        NAO(1L, Bundle.getStringApplication("rotulo_nao"));

        private Long value;
        private String descricao;

        private GestacaoRisco(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public DadosComplementaresLaqueadura() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public DadosComplementaresLaqueadura(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public DadosComplementaresLaqueadura(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente itemContaPaciente) {

        super(
                codigo,
                itemContaPaciente);
    }

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}
