package br.com.ksisolucoes.vo.consorcio.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the empresa_terceiro_responsavel table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="empresa_terceiro_responsavel"
 */

public abstract class BaseEmpresaTerceiroResponsavel extends BaseRootVO implements Serializable {

	public static String REF = "EmpresaTerceiroResponsavel";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_CBO = "cbo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_CNS = "cns";


	// constructors
	public BaseEmpresaTerceiroResponsavel () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEmpresaTerceiroResponsavel (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseEmpresaTerceiroResponsavel (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo cbo,
		java.util.Date dataCadastro,
		java.lang.Long cns) {

		this.setCodigo(codigo);
		this.setEmpresa(empresa);
		this.setUsuario(usuario);
		this.setCbo(cbo);
		this.setDataCadastro(dataCadastro);
		this.setCns(cns);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataCadastro;
	private java.lang.Long cns;

	// many to one
	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo cbo;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_empresa_terceiro_responsavel"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: cns
	 */
	public java.lang.Long getCns () {
		return getPropertyValue(this, cns, PROP_CNS); 
	}

	/**
	 * Set the value related to the column: cns
	 * @param cns the cns value
	 */
	public void setCns (java.lang.Long cns) {
//        java.lang.Long cnsOld = this.cns;
		this.cns = cns;
//        this.getPropertyChangeSupport().firePropertyChange ("cns", cnsOld, cns);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_cbo
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getCbo () {
		return getPropertyValue(this, cbo, PROP_CBO); 
	}

	/**
	 * Set the value related to the column: cd_cbo
	 * @param cbo the cd_cbo value
	 */
	public void setCbo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo cbo) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo cboOld = this.cbo;
		this.cbo = cbo;
//        this.getPropertyChangeSupport().firePropertyChange ("cbo", cboOld, cbo);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.consorcio.EmpresaTerceiroResponsavel)) return false;
		else {
			br.com.ksisolucoes.vo.consorcio.EmpresaTerceiroResponsavel empresaTerceiroResponsavel = (br.com.ksisolucoes.vo.consorcio.EmpresaTerceiroResponsavel) obj;
			if (null == this.getCodigo() || null == empresaTerceiroResponsavel.getCodigo()) return false;
			else return (this.getCodigo().equals(empresaTerceiroResponsavel.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}