<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.enfermagem">
    <class name="AtendimentoHistorico" table="atendimento_historico">
		
        <id name="codigo" column="cd_atendimento_historico" type="java.lang.Long" >
            <generator class="sequence">
                <param name="sequence">seq_atendimento</param>
            </generator>
        </id>
		
        <version column="version" name="version" type="long" />
		
        <property name="dataHistorico" type="java.util.Date" not-null="true">
            <column name="dt_historico" sql-type="timestamp"/>
        </property>
		
        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
                     name="atendimento" not-null="true">
            <column name="nr_atendimento" />
        </many-to-one>
		
        <many-to-one class="br.com.ksisolucoes.vo.cadsus.Profissional"
                     name="profissional">
            <column name="cd_profissional" not-null="false" />
        </many-to-one>
		
        <many-to-one class="br.com.ksisolucoes.vo.controle.Usuario"
                     name="usuario">
            <column name="cd_usuario" not-null="true" />
        </many-to-one>
		
        <property column="ds_historico" name="descricaoHistorico" not-null="true"
                  type="java.lang.String" length="512" />
			
    </class>
</hibernate-mapping>