package br.com.ksisolucoes.vo.vigilancia.endereco.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the profissional_endereco table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="profissional_endereco"
 */

public abstract class BaseProfissionalEndereco extends BaseRootVO implements Serializable {

	public static String REF = "ProfissionalEndereco";
	public static final String PROP_COMPLEMENTO = "complemento";
	public static final String PROP_NUMERO = "numero";
	public static final String PROP_FLAG_PRINCIPAL = "flagPrincipal";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_VIGILANCIA_PROFISSIONAL = "vigilanciaProfissional";
	public static final String PROP_TIPO_ENDERECO_VIGILANCIA = "tipoEnderecoVigilancia";
	public static final String PROP_VIGILANCIA_ENDERECO = "vigilanciaEndereco";


	// constructors
	public BaseProfissionalEndereco () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseProfissionalEndereco (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String numero;
	private java.lang.String complemento;
	private java.lang.Long flagPrincipal;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco vigilanciaEndereco;
	private br.com.ksisolucoes.vo.vigilancia.endereco.TipoEnderecoVigilancia tipoEnderecoVigilancia;
	private br.com.ksisolucoes.vo.vigilancia.VigilanciaProfissional vigilanciaProfissional;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  column="cd_profissional_endereco"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: numero
	 */
	public java.lang.String getNumero () {
		return getPropertyValue(this, numero, PROP_NUMERO); 
	}

	/**
	 * Set the value related to the column: numero
	 * @param numero the numero value
	 */
	public void setNumero (java.lang.String numero) {
//        java.lang.String numeroOld = this.numero;
		this.numero = numero;
//        this.getPropertyChangeSupport().firePropertyChange ("numero", numeroOld, numero);
	}



	/**
	 * Return the value associated with the column: complemento
	 */
	public java.lang.String getComplemento () {
		return getPropertyValue(this, complemento, PROP_COMPLEMENTO); 
	}

	/**
	 * Set the value related to the column: complemento
	 * @param complemento the complemento value
	 */
	public void setComplemento (java.lang.String complemento) {
//        java.lang.String complementoOld = this.complemento;
		this.complemento = complemento;
//        this.getPropertyChangeSupport().firePropertyChange ("complemento", complementoOld, complemento);
	}



	/**
	 * Return the value associated with the column: flag_principal
	 */
	public java.lang.Long getFlagPrincipal () {
		return getPropertyValue(this, flagPrincipal, PROP_FLAG_PRINCIPAL); 
	}

	/**
	 * Set the value related to the column: flag_principal
	 * @param flagPrincipal the flag_principal value
	 */
	public void setFlagPrincipal (java.lang.Long flagPrincipal) {
//        java.lang.Long flagPrincipalOld = this.flagPrincipal;
		this.flagPrincipal = flagPrincipal;
//        this.getPropertyChangeSupport().firePropertyChange ("flagPrincipal", flagPrincipalOld, flagPrincipal);
	}



	/**
	 * Return the value associated with the column: cd_vigilancia_endereco
	 */
	public br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco getVigilanciaEndereco () {
		return getPropertyValue(this, vigilanciaEndereco, PROP_VIGILANCIA_ENDERECO); 
	}

	/**
	 * Set the value related to the column: cd_vigilancia_endereco
	 * @param vigilanciaEndereco the cd_vigilancia_endereco value
	 */
	public void setVigilanciaEndereco (br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco vigilanciaEndereco) {
//        br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco vigilanciaEnderecoOld = this.vigilanciaEndereco;
		this.vigilanciaEndereco = vigilanciaEndereco;
//        this.getPropertyChangeSupport().firePropertyChange ("vigilanciaEndereco", vigilanciaEnderecoOld, vigilanciaEndereco);
	}



	/**
	 * Return the value associated with the column: cd_tipo_endereco_vigilancia
	 */
	public br.com.ksisolucoes.vo.vigilancia.endereco.TipoEnderecoVigilancia getTipoEnderecoVigilancia () {
		return getPropertyValue(this, tipoEnderecoVigilancia, PROP_TIPO_ENDERECO_VIGILANCIA); 
	}

	/**
	 * Set the value related to the column: cd_tipo_endereco_vigilancia
	 * @param tipoEnderecoVigilancia the cd_tipo_endereco_vigilancia value
	 */
	public void setTipoEnderecoVigilancia (br.com.ksisolucoes.vo.vigilancia.endereco.TipoEnderecoVigilancia tipoEnderecoVigilancia) {
//        br.com.ksisolucoes.vo.vigilancia.endereco.TipoEnderecoVigilancia tipoEnderecoVigilanciaOld = this.tipoEnderecoVigilancia;
		this.tipoEnderecoVigilancia = tipoEnderecoVigilancia;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoEnderecoVigilancia", tipoEnderecoVigilanciaOld, tipoEnderecoVigilancia);
	}



	/**
	 * Return the value associated with the column: cd_vigilancia_profissional
	 */
	public br.com.ksisolucoes.vo.vigilancia.VigilanciaProfissional getVigilanciaProfissional () {
		return getPropertyValue(this, vigilanciaProfissional, PROP_VIGILANCIA_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_vigilancia_profissional
	 * @param vigilanciaProfissional the cd_vigilancia_profissional value
	 */
	public void setVigilanciaProfissional (br.com.ksisolucoes.vo.vigilancia.VigilanciaProfissional vigilanciaProfissional) {
//        br.com.ksisolucoes.vo.vigilancia.VigilanciaProfissional vigilanciaProfissionalOld = this.vigilanciaProfissional;
		this.vigilanciaProfissional = vigilanciaProfissional;
//        this.getPropertyChangeSupport().firePropertyChange ("vigilanciaProfissional", vigilanciaProfissionalOld, vigilanciaProfissional);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.endereco.ProfissionalEndereco)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.endereco.ProfissionalEndereco profissionalEndereco = (br.com.ksisolucoes.vo.vigilancia.endereco.ProfissionalEndereco) obj;
			if (null == this.getCodigo() || null == profissionalEndereco.getCodigo()) return false;
			else return (this.getCodigo().equals(profissionalEndereco.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}