package br.com.ksisolucoes.vo.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the empresa_servico_class table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="empresa_servico_class"
 */

public abstract class BaseEmpresaServicoClassificacao extends BaseRootVO implements Serializable {

	public static String REF = "EmpresaServicoClassificacao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_AMBULATORIAL = "ambulatorial";
	public static final String PROP_CODIGO_CARACTERIZACAO = "codigoCaracterizacao";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_AMBULATORIAL_SUS = "ambulatorialSus";
	public static final String PROP_CNS_RESPONSAVEL = "cnsResponsavel";
	public static final String PROP_CNES_PROCESSO = "cnesProcesso";
	public static final String PROP_EMPRESA_TERCEIRO = "empresaTerceiro";
	public static final String PROP_STATUS = "status";
	public static final String PROP_PROCEDIMENTO_SERVICO_CLASSIFICACAO = "procedimentoServicoClassificacao";
	public static final String PROP_HOSPITALAR_SUS = "hospitalarSus";
	public static final String PROP_CBO_RESPONSAVEL = "cboResponsavel";
	public static final String PROP_NOME_RESPONSAVEL = "nomeResponsavel";
	public static final String PROP_HOSPITALAR = "hospitalar";
	public static final String PROP_DATA_ATUALIZACAO = "dataAtualizacao";


	// constructors
	public BaseEmpresaServicoClassificacao () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEmpresaServicoClassificacao (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String codigoCaracterizacao;
	private java.lang.String ambulatorial;
	private java.lang.String ambulatorialSus;
	private java.lang.String hospitalar;
	private java.lang.String hospitalarSus;
	private java.util.Date dataAtualizacao;
	private java.lang.Long status;
	private java.lang.String nomeResponsavel;
	private java.lang.String cnsResponsavel;

	// many to one
	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoServicoClassificacao procedimentoServicoClassificacao;
	private br.com.ksisolucoes.vo.basico.Empresa empresaTerceiro;
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo cboResponsavel;
	private br.com.ksisolucoes.vo.geral.cnes.CnesProcesso cnesProcesso;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_emp_serv_cla"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_caracterizacao
	 */
	public java.lang.String getCodigoCaracterizacao () {
		return getPropertyValue(this, codigoCaracterizacao, PROP_CODIGO_CARACTERIZACAO); 
	}

	/**
	 * Set the value related to the column: cd_caracterizacao
	 * @param codigoCaracterizacao the cd_caracterizacao value
	 */
	public void setCodigoCaracterizacao (java.lang.String codigoCaracterizacao) {
//        java.lang.String codigoCaracterizacaoOld = this.codigoCaracterizacao;
		this.codigoCaracterizacao = codigoCaracterizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoCaracterizacao", codigoCaracterizacaoOld, codigoCaracterizacao);
	}



	/**
	 * Return the value associated with the column: ambulatorial
	 */
	public java.lang.String getAmbulatorial () {
		return getPropertyValue(this, ambulatorial, PROP_AMBULATORIAL); 
	}

	/**
	 * Set the value related to the column: ambulatorial
	 * @param ambulatorial the ambulatorial value
	 */
	public void setAmbulatorial (java.lang.String ambulatorial) {
//        java.lang.String ambulatorialOld = this.ambulatorial;
		this.ambulatorial = ambulatorial;
//        this.getPropertyChangeSupport().firePropertyChange ("ambulatorial", ambulatorialOld, ambulatorial);
	}



	/**
	 * Return the value associated with the column: ambulatorial_sus
	 */
	public java.lang.String getAmbulatorialSus () {
		return getPropertyValue(this, ambulatorialSus, PROP_AMBULATORIAL_SUS); 
	}

	/**
	 * Set the value related to the column: ambulatorial_sus
	 * @param ambulatorialSus the ambulatorial_sus value
	 */
	public void setAmbulatorialSus (java.lang.String ambulatorialSus) {
//        java.lang.String ambulatorialSusOld = this.ambulatorialSus;
		this.ambulatorialSus = ambulatorialSus;
//        this.getPropertyChangeSupport().firePropertyChange ("ambulatorialSus", ambulatorialSusOld, ambulatorialSus);
	}



	/**
	 * Return the value associated with the column: hospitalar
	 */
	public java.lang.String getHospitalar () {
		return getPropertyValue(this, hospitalar, PROP_HOSPITALAR); 
	}

	/**
	 * Set the value related to the column: hospitalar
	 * @param hospitalar the hospitalar value
	 */
	public void setHospitalar (java.lang.String hospitalar) {
//        java.lang.String hospitalarOld = this.hospitalar;
		this.hospitalar = hospitalar;
//        this.getPropertyChangeSupport().firePropertyChange ("hospitalar", hospitalarOld, hospitalar);
	}



	/**
	 * Return the value associated with the column: hospitalar_sus
	 */
	public java.lang.String getHospitalarSus () {
		return getPropertyValue(this, hospitalarSus, PROP_HOSPITALAR_SUS); 
	}

	/**
	 * Set the value related to the column: hospitalar_sus
	 * @param hospitalarSus the hospitalar_sus value
	 */
	public void setHospitalarSus (java.lang.String hospitalarSus) {
//        java.lang.String hospitalarSusOld = this.hospitalarSus;
		this.hospitalarSus = hospitalarSus;
//        this.getPropertyChangeSupport().firePropertyChange ("hospitalarSus", hospitalarSusOld, hospitalarSus);
	}



	/**
	 * Return the value associated with the column: dt_atualizacao
	 */
	public java.util.Date getDataAtualizacao () {
		return getPropertyValue(this, dataAtualizacao, PROP_DATA_ATUALIZACAO); 
	}

	/**
	 * Set the value related to the column: dt_atualizacao
	 * @param dataAtualizacao the dt_atualizacao value
	 */
	public void setDataAtualizacao (java.util.Date dataAtualizacao) {
//        java.util.Date dataAtualizacaoOld = this.dataAtualizacao;
		this.dataAtualizacao = dataAtualizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAtualizacao", dataAtualizacaoOld, dataAtualizacao);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: nm_responsavel
	 */
	public java.lang.String getNomeResponsavel () {
		return getPropertyValue(this, nomeResponsavel, PROP_NOME_RESPONSAVEL); 
	}

	/**
	 * Set the value related to the column: nm_responsavel
	 * @param nomeResponsavel the nm_responsavel value
	 */
	public void setNomeResponsavel (java.lang.String nomeResponsavel) {
//        java.lang.String nomeResponsavelOld = this.nomeResponsavel;
		this.nomeResponsavel = nomeResponsavel;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeResponsavel", nomeResponsavelOld, nomeResponsavel);
	}



	/**
	 * Return the value associated with the column: cns_responsavel
	 */
	public java.lang.String getCnsResponsavel () {
		return getPropertyValue(this, cnsResponsavel, PROP_CNS_RESPONSAVEL); 
	}

	/**
	 * Set the value related to the column: cns_responsavel
	 * @param cnsResponsavel the cns_responsavel value
	 */
	public void setCnsResponsavel (java.lang.String cnsResponsavel) {
//        java.lang.String cnsResponsavelOld = this.cnsResponsavel;
		this.cnsResponsavel = cnsResponsavel;
//        this.getPropertyChangeSupport().firePropertyChange ("cnsResponsavel", cnsResponsavelOld, cnsResponsavel);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: cd_classificacao
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoServicoClassificacao getProcedimentoServicoClassificacao () {
		return getPropertyValue(this, procedimentoServicoClassificacao, PROP_PROCEDIMENTO_SERVICO_CLASSIFICACAO); 
	}

	/**
	 * Set the value related to the column: cd_classificacao
	 * @param procedimentoServicoClassificacao the cd_classificacao value
	 */
	public void setProcedimentoServicoClassificacao (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoServicoClassificacao procedimentoServicoClassificacao) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoServicoClassificacao procedimentoServicoClassificacaoOld = this.procedimentoServicoClassificacao;
		this.procedimentoServicoClassificacao = procedimentoServicoClassificacao;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimentoServicoClassificacao", procedimentoServicoClassificacaoOld, procedimentoServicoClassificacao);
	}



	/**
	 * Return the value associated with the column: empresa_terceiro
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresaTerceiro () {
		return getPropertyValue(this, empresaTerceiro, PROP_EMPRESA_TERCEIRO); 
	}

	/**
	 * Set the value related to the column: empresa_terceiro
	 * @param empresaTerceiro the empresa_terceiro value
	 */
	public void setEmpresaTerceiro (br.com.ksisolucoes.vo.basico.Empresa empresaTerceiro) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaTerceiroOld = this.empresaTerceiro;
		this.empresaTerceiro = empresaTerceiro;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaTerceiro", empresaTerceiroOld, empresaTerceiro);
	}



	/**
	 * Return the value associated with the column: cbo_responsavel
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getCboResponsavel () {
		return getPropertyValue(this, cboResponsavel, PROP_CBO_RESPONSAVEL); 
	}

	/**
	 * Set the value related to the column: cbo_responsavel
	 * @param cboResponsavel the cbo_responsavel value
	 */
	public void setCboResponsavel (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo cboResponsavel) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo cboResponsavelOld = this.cboResponsavel;
		this.cboResponsavel = cboResponsavel;
//        this.getPropertyChangeSupport().firePropertyChange ("cboResponsavel", cboResponsavelOld, cboResponsavel);
	}



	/**
	 * Return the value associated with the column: cd_cnes_processo
	 */
	public br.com.ksisolucoes.vo.geral.cnes.CnesProcesso getCnesProcesso () {
		return getPropertyValue(this, cnesProcesso, PROP_CNES_PROCESSO); 
	}

	/**
	 * Set the value related to the column: cd_cnes_processo
	 * @param cnesProcesso the cd_cnes_processo value
	 */
	public void setCnesProcesso (br.com.ksisolucoes.vo.geral.cnes.CnesProcesso cnesProcesso) {
//        br.com.ksisolucoes.vo.geral.cnes.CnesProcesso cnesProcessoOld = this.cnesProcesso;
		this.cnesProcesso = cnesProcesso;
//        this.getPropertyChangeSupport().firePropertyChange ("cnesProcesso", cnesProcessoOld, cnesProcesso);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.EmpresaServicoClassificacao)) return false;
		else {
			br.com.ksisolucoes.vo.basico.EmpresaServicoClassificacao empresaServicoClassificacao = (br.com.ksisolucoes.vo.basico.EmpresaServicoClassificacao) obj;
			if (null == this.getCodigo() || null == empresaServicoClassificacao.getCodigo()) return false;
			else return (this.getCodigo().equals(empresaServicoClassificacao.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}