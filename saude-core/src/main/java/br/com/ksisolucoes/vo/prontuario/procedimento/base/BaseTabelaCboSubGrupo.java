package br.com.ksisolucoes.vo.prontuario.procedimento.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the tabela_subgrupo_cbo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="tabela_subgrupo_cbo"
 */

public abstract class BaseTabelaCboSubGrupo extends BaseRootVO implements Serializable {

	public static String REF = "TabelaCboSubGrupo";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_ID = "id";


	// constructors
	public BaseTabelaCboSubGrupo () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseTabelaCboSubGrupo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCboSubGrupoPK id) {
		this.setId(id);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCboSubGrupoPK id;

	// fields
	private java.lang.String descricao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCboSubGrupoPK getId () {
	    return getPropertyValue(this,  id, "id" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param id the new ID
	 */
	public void setId (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCboSubGrupoPK id) {
		this.id = id;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_subgrupo_cbo
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_subgrupo_cbo
	 * @param descricao the ds_subgrupo_cbo value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCboSubGrupo)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCboSubGrupo tabelaCboSubGrupo = (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCboSubGrupo) obj;
			if (null == this.getId() || null == tabelaCboSubGrupo.getId()) return false;
			else return (this.getId().equals(tabelaCboSubGrupo.getId()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getId()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getId().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}