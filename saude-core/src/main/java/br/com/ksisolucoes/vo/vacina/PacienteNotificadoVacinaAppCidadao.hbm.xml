<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vacina"  >
    <class name="PacienteNotificadoVacinaAppCidadao" table="paciente_notificado_vacina_app_cidadao" >

        <id
            column="cd_pac_notificacao_app"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"
            name="usuarioCadsus"
            not-null="true"
        >
            <column name="cd_usu_cadsus" />
        </many-to-one>
        
        <many-to-one
            class="br.com.ksisolucoes.vo.vacina.TipoVacina"
            name="tipoVacina"
            not-null="true"
        >
            <column name="cd_vacina" />
        </many-to-one>

        <property
            column="dt_notificacao"
            name="dataNotificacao"
            not-null="true"
            type="timestamp"
        />

        <property
                column="cd_dose_vac"
                name="dose"
                not-null="true"
                type="long"
        />

        <property
                column="dt_validade_notificacao"
                name="dataValidadeNotificacao"
                not-null="true"
                type="java.util.Date"
        />

    </class>
</hibernate-mapping>
