package br.com.ksisolucoes.vo.vigilancia;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.base.BaseRequerimentoInspecaoSanitariaRelacaoProduto;

import java.io.Serializable;



public class RequerimentoInspecaoSanitariaRelacaoProduto extends BaseRequerimentoInspecaoSanitariaRelacaoProduto implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequerimentoInspecaoSanitariaRelacaoProduto() {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequerimentoInspecaoSanitariaRelacaoProduto(Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequerimentoInspecaoSanitariaRelacaoProduto(
		Long codigo,
		br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoInspecaoSanitaria requerimentoInspecaoSanitaria,
		Long tipoProduto,
		String descricaoProduto,
		String nomeTecnico,
		String registro,
		Long classificacaoRisco,
		java.util.Date dataRegistro) {

		super (
			codigo,
			requerimentoInspecaoSanitaria,
			tipoProduto,
			descricaoProduto,
			nomeTecnico,
			registro,
			classificacaoRisco,
			dataRegistro);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}