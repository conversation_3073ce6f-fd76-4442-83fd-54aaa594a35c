package br.com.ksisolucoes.vo.vigilancia.investigacao.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the investigacao_agr_malaria table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agr_malaria"
 */

public abstract class BaseInvestigacaoAgravoMalaria extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravoMalaria";
	public static final String PROP_DATA_INVESTIGACAO = "dataInvestigacao";
	public static final String PROP_LOGRADOURO = "logradouro";
	public static final String PROP_NUMERO = "numero";
	public static final String PROP_TRATAMENTO_ESQUEMA = "tratamentoEsquema";
	public static final String PROP_CODIGO_RUA = "codigoRua";
	public static final String PROP_TIPO_LAMINA = "tipoLamina";
	public static final String PROP_USUARIO_ENCERRAMENTO = "usuarioEncerramento";
	public static final String PROP_TELEFONE = "telefone";
	public static final String PROP_FLAG_INFORMACOES_COMPLEMENTARES = "flagInformacoesComplementares";
	public static final String PROP_BAIRRO_LOCAL_INFECCAO = "bairroLocalInfeccao";
	public static final String PROP_EXAME_RESULTADO = "exameResultado";
	public static final String PROP_DISTRITO_LOCAL_INFECCAO = "distritoLocalInfeccao";
	public static final String PROP_LOCALICADE_PROVAVEL_INFECCAO = "localicadeProvavelInfeccao";
	public static final String PROP_DATA_ENCERRAMENTO = "dataEncerramento";
	public static final String PROP_CASO_AUTOCTONE = "casoAutoctone";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_PRINCIPAL_ATIVIDADE_ULTIMOS_DIAS = "principalAtividadeUltimosDias";
	public static final String PROP_CIDADE_LOCAL_INFECCAO = "cidadeLocalInfeccao";
	public static final String PROP_EXAME_PARASITEMIA_CRUZES = "exameParasitemiaCruzes";
	public static final String PROP_DATA_EXAME = "dataExame";
	public static final String PROP_ZONA = "zona";
	public static final String PROP_TRATAMENTO_ESQUEMA_OUTRO = "tratamentoEsquemaOutro";
	public static final String PROP_EXAME_PARASITOS_MM3 = "exameParasitosMm3";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_REGISTRO_AGRAVO = "registroAgravo";
	public static final String PROP_TRATAMENTO_ESQUEMA_DATA_INICIO = "tratamentoEsquemaDataInicio";
	public static final String PROP_COMPLEMENTO = "complemento";
	public static final String PROP_CLASSIFICACAO_FINAL = "classificacaoFinal";
	public static final String PROP_OCUPACAO_CBO = "ocupacaoCbo";
	public static final String PROP_CEP = "cep";
	public static final String PROP_GEO_CAMPO1 = "geoCampo1";
	public static final String PROP_GEO_CAMPO2 = "geoCampo2";
	public static final String PROP_PONTO_REFERENCIA = "pontoReferencia";
	public static final String PROP_SINTOMAS = "sintomas";
	public static final String PROP_PAIS_LOCAL_INFECCAO = "paisLocalInfeccao";


	// constructors
	public BaseInvestigacaoAgravoMalaria () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravoMalaria (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseInvestigacaoAgravoMalaria (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
		java.lang.String flagInformacoesComplementares) {

		this.setCodigo(codigo);
		this.setRegistroAgravo(registroAgravo);
		this.setFlagInformacoesComplementares(flagInformacoesComplementares);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String flagInformacoesComplementares;
	private java.util.Date dataInvestigacao;
	private java.lang.Long principalAtividadeUltimosDias;
	private java.lang.Long tipoLamina;
	private java.lang.Long sintomas;
	private java.util.Date dataExame;
	private java.lang.Long exameResultado;
	private java.lang.String exameParasitosMm3;
	private java.lang.Long exameParasitemiaCruzes;
	private java.lang.Long tratamentoEsquema;
	private java.lang.String tratamentoEsquemaOutro;
	private java.util.Date tratamentoEsquemaDataInicio;
	private java.lang.Long casoAutoctone;
	private java.lang.String distritoLocalInfeccao;
	private java.lang.String bairroLocalInfeccao;
	private java.lang.String localicadeProvavelInfeccao;
	private java.lang.String geoCampo1;
	private java.lang.String geoCampo2;
	private java.lang.String pontoReferencia;
	private java.lang.String cep;
	private java.lang.String telefone;
	private java.lang.Long zona;
	private java.lang.String codigoRua;
	private java.lang.String logradouro;
	private java.lang.String numero;
	private java.lang.String complemento;
	private java.lang.Long classificacaoFinal;
	private java.lang.String observacao;
	private java.util.Date dataEncerramento;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo;
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo;
	private br.com.ksisolucoes.vo.basico.Cidade cidadeLocalInfeccao;
	private br.com.ksisolucoes.vo.basico.Pais paisLocalInfeccao;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_invest_agr_malaria"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: flag_informacoes_complementares
	 */
	public java.lang.String getFlagInformacoesComplementares () {
		return getPropertyValue(this, flagInformacoesComplementares, PROP_FLAG_INFORMACOES_COMPLEMENTARES); 
	}

	/**
	 * Set the value related to the column: flag_informacoes_complementares
	 * @param flagInformacoesComplementares the flag_informacoes_complementares value
	 */
	public void setFlagInformacoesComplementares (java.lang.String flagInformacoesComplementares) {
//        java.lang.String flagInformacoesComplementaresOld = this.flagInformacoesComplementares;
		this.flagInformacoesComplementares = flagInformacoesComplementares;
//        this.getPropertyChangeSupport().firePropertyChange ("flagInformacoesComplementares", flagInformacoesComplementaresOld, flagInformacoesComplementares);
	}



	/**
	 * Return the value associated with the column: dt_investigacao
	 */
	public java.util.Date getDataInvestigacao () {
		return getPropertyValue(this, dataInvestigacao, PROP_DATA_INVESTIGACAO); 
	}

	/**
	 * Set the value related to the column: dt_investigacao
	 * @param dataInvestigacao the dt_investigacao value
	 */
	public void setDataInvestigacao (java.util.Date dataInvestigacao) {
//        java.util.Date dataInvestigacaoOld = this.dataInvestigacao;
		this.dataInvestigacao = dataInvestigacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInvestigacao", dataInvestigacaoOld, dataInvestigacao);
	}



	/**
	 * Return the value associated with the column: principal_atividade_ultimos_dias
	 */
	public java.lang.Long getPrincipalAtividadeUltimosDias () {
		return getPropertyValue(this, principalAtividadeUltimosDias, PROP_PRINCIPAL_ATIVIDADE_ULTIMOS_DIAS); 
	}

	/**
	 * Set the value related to the column: principal_atividade_ultimos_dias
	 * @param principalAtividadeUltimosDias the principal_atividade_ultimos_dias value
	 */
	public void setPrincipalAtividadeUltimosDias (java.lang.Long principalAtividadeUltimosDias) {
//        java.lang.Long principalAtividadeUltimosDiasOld = this.principalAtividadeUltimosDias;
		this.principalAtividadeUltimosDias = principalAtividadeUltimosDias;
//        this.getPropertyChangeSupport().firePropertyChange ("principalAtividadeUltimosDias", principalAtividadeUltimosDiasOld, principalAtividadeUltimosDias);
	}



	/**
	 * Return the value associated with the column: tipo_lamina
	 */
	public java.lang.Long getTipoLamina () {
		return getPropertyValue(this, tipoLamina, PROP_TIPO_LAMINA); 
	}

	/**
	 * Set the value related to the column: tipo_lamina
	 * @param tipoLamina the tipo_lamina value
	 */
	public void setTipoLamina (java.lang.Long tipoLamina) {
//        java.lang.Long tipoLaminaOld = this.tipoLamina;
		this.tipoLamina = tipoLamina;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoLamina", tipoLaminaOld, tipoLamina);
	}



	/**
	 * Return the value associated with the column: sintomas
	 */
	public java.lang.Long getSintomas () {
		return getPropertyValue(this, sintomas, PROP_SINTOMAS); 
	}

	/**
	 * Set the value related to the column: sintomas
	 * @param sintomas the sintomas value
	 */
	public void setSintomas (java.lang.Long sintomas) {
//        java.lang.Long sintomasOld = this.sintomas;
		this.sintomas = sintomas;
//        this.getPropertyChangeSupport().firePropertyChange ("sintomas", sintomasOld, sintomas);
	}



	/**
	 * Return the value associated with the column: exame_data
	 */
	public java.util.Date getDataExame () {
		return getPropertyValue(this, dataExame, PROP_DATA_EXAME); 
	}

	/**
	 * Set the value related to the column: exame_data
	 * @param dataExame the exame_data value
	 */
	public void setDataExame (java.util.Date dataExame) {
//        java.util.Date dataExameOld = this.dataExame;
		this.dataExame = dataExame;
//        this.getPropertyChangeSupport().firePropertyChange ("dataExame", dataExameOld, dataExame);
	}



	/**
	 * Return the value associated with the column: exame_resultado
	 */
	public java.lang.Long getExameResultado () {
		return getPropertyValue(this, exameResultado, PROP_EXAME_RESULTADO); 
	}

	/**
	 * Set the value related to the column: exame_resultado
	 * @param exameResultado the exame_resultado value
	 */
	public void setExameResultado (java.lang.Long exameResultado) {
//        java.lang.Long exameResultadoOld = this.exameResultado;
		this.exameResultado = exameResultado;
//        this.getPropertyChangeSupport().firePropertyChange ("exameResultado", exameResultadoOld, exameResultado);
	}



	/**
	 * Return the value associated with the column: exame_parasitos
	 */
	public java.lang.String getExameParasitosMm3 () {
		return getPropertyValue(this, exameParasitosMm3, PROP_EXAME_PARASITOS_MM3); 
	}

	/**
	 * Set the value related to the column: exame_parasitos
	 * @param exameParasitosMm3 the exame_parasitos value
	 */
	public void setExameParasitosMm3 (java.lang.String exameParasitosMm3) {
//        java.lang.String exameParasitosMm3Old = this.exameParasitosMm3;
		this.exameParasitosMm3 = exameParasitosMm3;
//        this.getPropertyChangeSupport().firePropertyChange ("exameParasitosMm3", exameParasitosMm3Old, exameParasitosMm3);
	}



	/**
	 * Return the value associated with the column: exame_parasitemia_cruzes
	 */
	public java.lang.Long getExameParasitemiaCruzes () {
		return getPropertyValue(this, exameParasitemiaCruzes, PROP_EXAME_PARASITEMIA_CRUZES); 
	}

	/**
	 * Set the value related to the column: exame_parasitemia_cruzes
	 * @param exameParasitemiaCruzes the exame_parasitemia_cruzes value
	 */
	public void setExameParasitemiaCruzes (java.lang.Long exameParasitemiaCruzes) {
//        java.lang.Long exameParasitemiaCruzesOld = this.exameParasitemiaCruzes;
		this.exameParasitemiaCruzes = exameParasitemiaCruzes;
//        this.getPropertyChangeSupport().firePropertyChange ("exameParasitemiaCruzes", exameParasitemiaCruzesOld, exameParasitemiaCruzes);
	}



	/**
	 * Return the value associated with the column: tratamento_esquema
	 */
	public java.lang.Long getTratamentoEsquema () {
		return getPropertyValue(this, tratamentoEsquema, PROP_TRATAMENTO_ESQUEMA); 
	}

	/**
	 * Set the value related to the column: tratamento_esquema
	 * @param tratamentoEsquema the tratamento_esquema value
	 */
	public void setTratamentoEsquema (java.lang.Long tratamentoEsquema) {
//        java.lang.Long tratamentoEsquemaOld = this.tratamentoEsquema;
		this.tratamentoEsquema = tratamentoEsquema;
//        this.getPropertyChangeSupport().firePropertyChange ("tratamentoEsquema", tratamentoEsquemaOld, tratamentoEsquema);
	}



	/**
	 * Return the value associated with the column: tratamento_esquema_outro_str
	 */
	public java.lang.String getTratamentoEsquemaOutro () {
		return getPropertyValue(this, tratamentoEsquemaOutro, PROP_TRATAMENTO_ESQUEMA_OUTRO); 
	}

	/**
	 * Set the value related to the column: tratamento_esquema_outro_str
	 * @param tratamentoEsquemaOutro the tratamento_esquema_outro_str value
	 */
	public void setTratamentoEsquemaOutro (java.lang.String tratamentoEsquemaOutro) {
//        java.lang.String tratamentoEsquemaOutroOld = this.tratamentoEsquemaOutro;
		this.tratamentoEsquemaOutro = tratamentoEsquemaOutro;
//        this.getPropertyChangeSupport().firePropertyChange ("tratamentoEsquemaOutro", tratamentoEsquemaOutroOld, tratamentoEsquemaOutro);
	}



	/**
	 * Return the value associated with the column: tratamento_esquema_data_inicio
	 */
	public java.util.Date getTratamentoEsquemaDataInicio () {
		return getPropertyValue(this, tratamentoEsquemaDataInicio, PROP_TRATAMENTO_ESQUEMA_DATA_INICIO); 
	}

	/**
	 * Set the value related to the column: tratamento_esquema_data_inicio
	 * @param tratamentoEsquemaDataInicio the tratamento_esquema_data_inicio value
	 */
	public void setTratamentoEsquemaDataInicio (java.util.Date tratamentoEsquemaDataInicio) {
//        java.util.Date tratamentoEsquemaDataInicioOld = this.tratamentoEsquemaDataInicio;
		this.tratamentoEsquemaDataInicio = tratamentoEsquemaDataInicio;
//        this.getPropertyChangeSupport().firePropertyChange ("tratamentoEsquemaDataInicio", tratamentoEsquemaDataInicioOld, tratamentoEsquemaDataInicio);
	}



	/**
	 * Return the value associated with the column: caso_autoctone
	 */
	public java.lang.Long getCasoAutoctone () {
		return getPropertyValue(this, casoAutoctone, PROP_CASO_AUTOCTONE); 
	}

	/**
	 * Set the value related to the column: caso_autoctone
	 * @param casoAutoctone the caso_autoctone value
	 */
	public void setCasoAutoctone (java.lang.Long casoAutoctone) {
//        java.lang.Long casoAutoctoneOld = this.casoAutoctone;
		this.casoAutoctone = casoAutoctone;
//        this.getPropertyChangeSupport().firePropertyChange ("casoAutoctone", casoAutoctoneOld, casoAutoctone);
	}



	/**
	 * Return the value associated with the column: str_distrito_infeccao
	 */
	public java.lang.String getDistritoLocalInfeccao () {
		return getPropertyValue(this, distritoLocalInfeccao, PROP_DISTRITO_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: str_distrito_infeccao
	 * @param distritoLocalInfeccao the str_distrito_infeccao value
	 */
	public void setDistritoLocalInfeccao (java.lang.String distritoLocalInfeccao) {
//        java.lang.String distritoLocalInfeccaoOld = this.distritoLocalInfeccao;
		this.distritoLocalInfeccao = distritoLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("distritoLocalInfeccao", distritoLocalInfeccaoOld, distritoLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: str_bairro_infeccao
	 */
	public java.lang.String getBairroLocalInfeccao () {
		return getPropertyValue(this, bairroLocalInfeccao, PROP_BAIRRO_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: str_bairro_infeccao
	 * @param bairroLocalInfeccao the str_bairro_infeccao value
	 */
	public void setBairroLocalInfeccao (java.lang.String bairroLocalInfeccao) {
//        java.lang.String bairroLocalInfeccaoOld = this.bairroLocalInfeccao;
		this.bairroLocalInfeccao = bairroLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("bairroLocalInfeccao", bairroLocalInfeccaoOld, bairroLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: str_localidade_provavel_infeccao
	 */
	public java.lang.String getLocalicadeProvavelInfeccao () {
		return getPropertyValue(this, localicadeProvavelInfeccao, PROP_LOCALICADE_PROVAVEL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: str_localidade_provavel_infeccao
	 * @param localicadeProvavelInfeccao the str_localidade_provavel_infeccao value
	 */
	public void setLocalicadeProvavelInfeccao (java.lang.String localicadeProvavelInfeccao) {
//        java.lang.String localicadeProvavelInfeccaoOld = this.localicadeProvavelInfeccao;
		this.localicadeProvavelInfeccao = localicadeProvavelInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("localicadeProvavelInfeccao", localicadeProvavelInfeccaoOld, localicadeProvavelInfeccao);
	}



	/**
	 * Return the value associated with the column: txt_ge_campo_1
	 */
	public java.lang.String getGeoCampo1 () {
		return getPropertyValue(this, geoCampo1, PROP_GEO_CAMPO1); 
	}

	/**
	 * Set the value related to the column: txt_ge_campo_1
	 * @param geoCampo1 the txt_ge_campo_1 value
	 */
	public void setGeoCampo1 (java.lang.String geoCampo1) {
//        java.lang.String geoCampo1Old = this.geoCampo1;
		this.geoCampo1 = geoCampo1;
//        this.getPropertyChangeSupport().firePropertyChange ("geoCampo1", geoCampo1Old, geoCampo1);
	}



	/**
	 * Return the value associated with the column: txt_ge_campo_2
	 */
	public java.lang.String getGeoCampo2 () {
		return getPropertyValue(this, geoCampo2, PROP_GEO_CAMPO2); 
	}

	/**
	 * Set the value related to the column: txt_ge_campo_2
	 * @param geoCampo2 the txt_ge_campo_2 value
	 */
	public void setGeoCampo2 (java.lang.String geoCampo2) {
//        java.lang.String geoCampo2Old = this.geoCampo2;
		this.geoCampo2 = geoCampo2;
//        this.getPropertyChangeSupport().firePropertyChange ("geoCampo2", geoCampo2Old, geoCampo2);
	}



	/**
	 * Return the value associated with the column: txt_ponto_referencia
	 */
	public java.lang.String getPontoReferencia () {
		return getPropertyValue(this, pontoReferencia, PROP_PONTO_REFERENCIA); 
	}

	/**
	 * Set the value related to the column: txt_ponto_referencia
	 * @param pontoReferencia the txt_ponto_referencia value
	 */
	public void setPontoReferencia (java.lang.String pontoReferencia) {
//        java.lang.String pontoReferenciaOld = this.pontoReferencia;
		this.pontoReferencia = pontoReferencia;
//        this.getPropertyChangeSupport().firePropertyChange ("pontoReferencia", pontoReferenciaOld, pontoReferencia);
	}



	/**
	 * Return the value associated with the column: txt_cep
	 */
	public java.lang.String getCep () {
		return getPropertyValue(this, cep, PROP_CEP); 
	}

	/**
	 * Set the value related to the column: txt_cep
	 * @param cep the txt_cep value
	 */
	public void setCep (java.lang.String cep) {
//        java.lang.String cepOld = this.cep;
		this.cep = cep;
//        this.getPropertyChangeSupport().firePropertyChange ("cep", cepOld, cep);
	}



	/**
	 * Return the value associated with the column: txt_telefone
	 */
	public java.lang.String getTelefone () {
		return getPropertyValue(this, telefone, PROP_TELEFONE); 
	}

	/**
	 * Set the value related to the column: txt_telefone
	 * @param telefone the txt_telefone value
	 */
	public void setTelefone (java.lang.String telefone) {
//        java.lang.String telefoneOld = this.telefone;
		this.telefone = telefone;
//        this.getPropertyChangeSupport().firePropertyChange ("telefone", telefoneOld, telefone);
	}



	/**
	 * Return the value associated with the column: flag_zona
	 */
	public java.lang.Long getZona () {
		return getPropertyValue(this, zona, PROP_ZONA); 
	}

	/**
	 * Set the value related to the column: flag_zona
	 * @param zona the flag_zona value
	 */
	public void setZona (java.lang.Long zona) {
//        java.lang.Long zonaOld = this.zona;
		this.zona = zona;
//        this.getPropertyChangeSupport().firePropertyChange ("zona", zonaOld, zona);
	}



	/**
	 * Return the value associated with the column: codigo_rua
	 */
	public java.lang.String getCodigoRua () {
		return getPropertyValue(this, codigoRua, PROP_CODIGO_RUA); 
	}

	/**
	 * Set the value related to the column: codigo_rua
	 * @param codigoRua the codigo_rua value
	 */
	public void setCodigoRua (java.lang.String codigoRua) {
//        java.lang.String codigoRuaOld = this.codigoRua;
		this.codigoRua = codigoRua;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoRua", codigoRuaOld, codigoRua);
	}



	/**
	 * Return the value associated with the column: logradouro
	 */
	public java.lang.String getLogradouro () {
		return getPropertyValue(this, logradouro, PROP_LOGRADOURO); 
	}

	/**
	 * Set the value related to the column: logradouro
	 * @param logradouro the logradouro value
	 */
	public void setLogradouro (java.lang.String logradouro) {
//        java.lang.String logradouroOld = this.logradouro;
		this.logradouro = logradouro;
//        this.getPropertyChangeSupport().firePropertyChange ("logradouro", logradouroOld, logradouro);
	}



	/**
	 * Return the value associated with the column: numero
	 */
	public java.lang.String getNumero () {
		return getPropertyValue(this, numero, PROP_NUMERO); 
	}

	/**
	 * Set the value related to the column: numero
	 * @param numero the numero value
	 */
	public void setNumero (java.lang.String numero) {
//        java.lang.String numeroOld = this.numero;
		this.numero = numero;
//        this.getPropertyChangeSupport().firePropertyChange ("numero", numeroOld, numero);
	}



	/**
	 * Return the value associated with the column: complemento
	 */
	public java.lang.String getComplemento () {
		return getPropertyValue(this, complemento, PROP_COMPLEMENTO); 
	}

	/**
	 * Set the value related to the column: complemento
	 * @param complemento the complemento value
	 */
	public void setComplemento (java.lang.String complemento) {
//        java.lang.String complementoOld = this.complemento;
		this.complemento = complemento;
//        this.getPropertyChangeSupport().firePropertyChange ("complemento", complementoOld, complemento);
	}



	/**
	 * Return the value associated with the column: classificacao_final
	 */
	public java.lang.Long getClassificacaoFinal () {
		return getPropertyValue(this, classificacaoFinal, PROP_CLASSIFICACAO_FINAL); 
	}

	/**
	 * Set the value related to the column: classificacao_final
	 * @param classificacaoFinal the classificacao_final value
	 */
	public void setClassificacaoFinal (java.lang.Long classificacaoFinal) {
//        java.lang.Long classificacaoFinalOld = this.classificacaoFinal;
		this.classificacaoFinal = classificacaoFinal;
//        this.getPropertyChangeSupport().firePropertyChange ("classificacaoFinal", classificacaoFinalOld, classificacaoFinal);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: dt_encerramento
	 */
	public java.util.Date getDataEncerramento () {
		return getPropertyValue(this, dataEncerramento, PROP_DATA_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_encerramento
	 * @param dataEncerramento the dt_encerramento value
	 */
	public void setDataEncerramento (java.util.Date dataEncerramento) {
//        java.util.Date dataEncerramentoOld = this.dataEncerramento;
		this.dataEncerramento = dataEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEncerramento", dataEncerramentoOld, dataEncerramento);
	}



	/**
	 * Return the value associated with the column: cd_registro_agravo
	 */
	public br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo getRegistroAgravo () {
		return getPropertyValue(this, registroAgravo, PROP_REGISTRO_AGRAVO); 
	}

	/**
	 * Set the value related to the column: cd_registro_agravo
	 * @param registroAgravo the cd_registro_agravo value
	 */
	public void setRegistroAgravo (br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo) {
//        br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravoOld = this.registroAgravo;
		this.registroAgravo = registroAgravo;
//        this.getPropertyChangeSupport().firePropertyChange ("registroAgravo", registroAgravoOld, registroAgravo);
	}



	/**
	 * Return the value associated with the column: ocupacao_cbo
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getOcupacaoCbo () {
		return getPropertyValue(this, ocupacaoCbo, PROP_OCUPACAO_CBO); 
	}

	/**
	 * Set the value related to the column: ocupacao_cbo
	 * @param ocupacaoCbo the ocupacao_cbo value
	 */
	public void setOcupacaoCbo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCboOld = this.ocupacaoCbo;
		this.ocupacaoCbo = ocupacaoCbo;
//        this.getPropertyChangeSupport().firePropertyChange ("ocupacaoCbo", ocupacaoCboOld, ocupacaoCbo);
	}



	/**
	 * Return the value associated with the column: cd_cidade_infeccao
	 */
	public br.com.ksisolucoes.vo.basico.Cidade getCidadeLocalInfeccao () {
		return getPropertyValue(this, cidadeLocalInfeccao, PROP_CIDADE_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: cd_cidade_infeccao
	 * @param cidadeLocalInfeccao the cd_cidade_infeccao value
	 */
	public void setCidadeLocalInfeccao (br.com.ksisolucoes.vo.basico.Cidade cidadeLocalInfeccao) {
//        br.com.ksisolucoes.vo.basico.Cidade cidadeLocalInfeccaoOld = this.cidadeLocalInfeccao;
		this.cidadeLocalInfeccao = cidadeLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("cidadeLocalInfeccao", cidadeLocalInfeccaoOld, cidadeLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: cd_pais_infeccao
	 */
	public br.com.ksisolucoes.vo.basico.Pais getPaisLocalInfeccao () {
		return getPropertyValue(this, paisLocalInfeccao, PROP_PAIS_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: cd_pais_infeccao
	 * @param paisLocalInfeccao the cd_pais_infeccao value
	 */
	public void setPaisLocalInfeccao (br.com.ksisolucoes.vo.basico.Pais paisLocalInfeccao) {
//        br.com.ksisolucoes.vo.basico.Pais paisLocalInfeccaoOld = this.paisLocalInfeccao;
		this.paisLocalInfeccao = paisLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("paisLocalInfeccao", paisLocalInfeccaoOld, paisLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: cd_usuario_encerramento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioEncerramento () {
		return getPropertyValue(this, usuarioEncerramento, PROP_USUARIO_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_encerramento
	 * @param usuarioEncerramento the cd_usuario_encerramento value
	 */
	public void setUsuarioEncerramento (br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramentoOld = this.usuarioEncerramento;
		this.usuarioEncerramento = usuarioEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioEncerramento", usuarioEncerramentoOld, usuarioEncerramento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoMalaria)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoMalaria investigacaoAgravoMalaria = (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoMalaria) obj;
			if (null == this.getCodigo() || null == investigacaoAgravoMalaria.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravoMalaria.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}