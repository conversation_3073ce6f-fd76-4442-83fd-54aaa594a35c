package br.com.ksisolucoes.vo.entradas.estoque.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the produto_tipo_via table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="produto_tipo_via"
 */

public abstract class BaseProdutoTipoVia extends BaseRootVO implements Serializable {

	public static String REF = "ProdutoTipoVia";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TIPO_VIA_MEDICAMENTO = "tipoViaMedicamento";
	public static final String PROP_PRODUTO = "produto";


	// constructors
	public BaseProdutoTipoVia () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseProdutoTipoVia (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseProdutoTipoVia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.entradas.estoque.Produto produto,
		br.com.ksisolucoes.vo.entradas.estoque.TipoViaMedicamento tipoViaMedicamento) {

		this.setCodigo(codigo);
		this.setProduto(produto);
		this.setTipoViaMedicamento(tipoViaMedicamento);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// many to one
	private br.com.ksisolucoes.vo.entradas.estoque.Produto produto;
	private br.com.ksisolucoes.vo.entradas.estoque.TipoViaMedicamento tipoViaMedicamento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_produto_tp_via"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cod_pro
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Produto getProduto () {
		return getPropertyValue(this, produto, PROP_PRODUTO); 
	}

	/**
	 * Set the value related to the column: cod_pro
	 * @param produto the cod_pro value
	 */
	public void setProduto (br.com.ksisolucoes.vo.entradas.estoque.Produto produto) {
//        br.com.ksisolucoes.vo.entradas.estoque.Produto produtoOld = this.produto;
		this.produto = produto;
//        this.getPropertyChangeSupport().firePropertyChange ("produto", produtoOld, produto);
	}



	/**
	 * Return the value associated with the column: cd_tp_via_medicamento
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.TipoViaMedicamento getTipoViaMedicamento () {
		return getPropertyValue(this, tipoViaMedicamento, PROP_TIPO_VIA_MEDICAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_tp_via_medicamento
	 * @param tipoViaMedicamento the cd_tp_via_medicamento value
	 */
	public void setTipoViaMedicamento (br.com.ksisolucoes.vo.entradas.estoque.TipoViaMedicamento tipoViaMedicamento) {
//        br.com.ksisolucoes.vo.entradas.estoque.TipoViaMedicamento tipoViaMedicamentoOld = this.tipoViaMedicamento;
		this.tipoViaMedicamento = tipoViaMedicamento;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoViaMedicamento", tipoViaMedicamentoOld, tipoViaMedicamento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.entradas.estoque.ProdutoTipoVia)) return false;
		else {
			br.com.ksisolucoes.vo.entradas.estoque.ProdutoTipoVia produtoTipoVia = (br.com.ksisolucoes.vo.entradas.estoque.ProdutoTipoVia) obj;
			if (null == this.getCodigo() || null == produtoTipoVia.getCodigo()) return false;
			else return (this.getCodigo().equals(produtoTipoVia.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}