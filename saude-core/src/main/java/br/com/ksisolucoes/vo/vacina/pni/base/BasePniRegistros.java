package br.com.ksisolucoes.vo.vacina.pni.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the pni_registros table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="pni_registros"
 */

public abstract class BasePniRegistros extends BaseRootVO implements Serializable {

	public static String REF = "PniRegistros";
	public static final String PROP_CODIGO_PRODUTOR = "codigoProdutor";
	public static final String PROP_FLAG_GESTANTE = "flagGestante";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_EMPRESA_LANCAMENTO = "empresaLancamento";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_CODIGO_CIDADE = "codigoCidade";
	public static final String PROP_DATA_IMUNIZACAO = "dataImunizacao";
	public static final String PROP_DATA_APRAZAMENTO = "dataAprazamento";
	public static final String PROP_FLAG_PUERPERA = "flagPuerpera";
	public static final String PROP_DESCRICAO_BAIRRO = "descricaoBairro";
	public static final String PROP_CODIGO_ESTRATEGIA = "codigoEstrategia";
	public static final String PROP_NUMERO_DOSE_REPETIDA = "numeroDoseRepetida";
	public static final String PROP_PNI = "pni";
	public static final String PROP_FLAG_ATUALIZACAO_CADERNETA = "flagAtualizacaoCaderneta";
	public static final String PROP_CODIGO_PRODUTO = "codigoProduto";
	public static final String PROP_CNS_PROFISSIONAL = "cnsProfissional";
	public static final String PROP_EMPRESA_CADASTRO = "empresaCadastro";
	public static final String PROP_CODIGO_MOTIVO_INDICACAO = "codigoMotivoIndicacao";
	public static final String PROP_LOTE = "lote";
	public static final String PROP_FLAG_COMUNICANTE_HANSENIASE = "flagComunicanteHanseniase";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_CBO_INDICADOR = "cboIndicador";
	public static final String PROP_CODIGO_DOSE = "codigoDose";
	public static final String PROP_SIGLA_ZONA_DOMICILIAR = "siglaZonaDomiciliar";
	public static final String PROP_FLAG_INADVERTIDA = "flagInadvertida";
	public static final String PROP_CODIGO_GRUPO_ATENDIMENTO = "codigoGrupoAtendimento";


	// constructors
	public BasePniRegistros () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BasePniRegistros (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BasePniRegistros (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vacina.pni.Pni pni,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		br.com.ksisolucoes.vo.basico.Empresa empresaLancamento,
		br.com.ksisolucoes.vo.basico.Empresa empresaCadastro,
		java.lang.Long codigoProduto,
		java.lang.Long codigoDose,
		java.lang.Long numeroDoseRepetida,
		java.lang.String flagInadvertida,
		java.util.Date dataImunizacao,
		java.lang.Long codigoGrupoAtendimento,
		java.lang.String flagComunicanteHanseniase,
		java.lang.String flagGestante,
		java.lang.Long codigoProdutor,
		java.lang.Long codigoEstrategia,
		java.lang.String flagAtualizacaoCaderneta,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setPni(pni);
		this.setUsuarioCadsus(usuarioCadsus);
		this.setEmpresaLancamento(empresaLancamento);
		this.setEmpresaCadastro(empresaCadastro);
		this.setCodigoProduto(codigoProduto);
		this.setCodigoDose(codigoDose);
		this.setNumeroDoseRepetida(numeroDoseRepetida);
		this.setFlagInadvertida(flagInadvertida);
		this.setDataImunizacao(dataImunizacao);
		this.setCodigoGrupoAtendimento(codigoGrupoAtendimento);
		this.setFlagComunicanteHanseniase(flagComunicanteHanseniase);
		this.setFlagGestante(flagGestante);
		this.setCodigoProdutor(codigoProdutor);
		this.setCodigoEstrategia(codigoEstrategia);
		this.setFlagAtualizacaoCaderneta(flagAtualizacaoCaderneta);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long codigoCidade;
	private java.lang.Long codigoProduto;
	private java.lang.Long codigoDose;
	private java.lang.Long numeroDoseRepetida;
	private java.lang.String flagInadvertida;
	private java.util.Date dataImunizacao;
	private java.lang.String lote;
	private java.lang.String descricaoBairro;
	private java.lang.String siglaZonaDomiciliar;
	private java.lang.Long codigoGrupoAtendimento;
	private java.lang.String flagComunicanteHanseniase;
	private java.lang.String flagGestante;
	private java.lang.Long codigoProdutor;
	private java.lang.Long codigoEstrategia;
	private java.lang.Long codigoMotivoIndicacao;
	private java.lang.Long cnsProfissional;
	private java.lang.String cboIndicador;
	private java.lang.String flagAtualizacaoCaderneta;
	private java.util.Date dataAprazamento;
	private java.util.Date dataCadastro;
	private java.lang.String flagPuerpera;

	// many to one
	private br.com.ksisolucoes.vo.vacina.pni.Pni pni;
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private br.com.ksisolucoes.vo.basico.Empresa empresaLancamento;
	private br.com.ksisolucoes.vo.basico.Empresa empresaCadastro;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_pni_registros"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cod_cidade
	 */
	public java.lang.Long getCodigoCidade () {
		return getPropertyValue(this, codigoCidade, PROP_CODIGO_CIDADE); 
	}

	/**
	 * Set the value related to the column: cod_cidade
	 * @param codigoCidade the cod_cidade value
	 */
	public void setCodigoCidade (java.lang.Long codigoCidade) {
//        java.lang.Long codigoCidadeOld = this.codigoCidade;
		this.codigoCidade = codigoCidade;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoCidade", codigoCidadeOld, codigoCidade);
	}



	/**
	 * Return the value associated with the column: cod_produto
	 */
	public java.lang.Long getCodigoProduto () {
		return getPropertyValue(this, codigoProduto, PROP_CODIGO_PRODUTO); 
	}

	/**
	 * Set the value related to the column: cod_produto
	 * @param codigoProduto the cod_produto value
	 */
	public void setCodigoProduto (java.lang.Long codigoProduto) {
//        java.lang.Long codigoProdutoOld = this.codigoProduto;
		this.codigoProduto = codigoProduto;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoProduto", codigoProdutoOld, codigoProduto);
	}



	/**
	 * Return the value associated with the column: cod_dose
	 */
	public java.lang.Long getCodigoDose () {
		return getPropertyValue(this, codigoDose, PROP_CODIGO_DOSE); 
	}

	/**
	 * Set the value related to the column: cod_dose
	 * @param codigoDose the cod_dose value
	 */
	public void setCodigoDose (java.lang.Long codigoDose) {
//        java.lang.Long codigoDoseOld = this.codigoDose;
		this.codigoDose = codigoDose;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoDose", codigoDoseOld, codigoDose);
	}



	/**
	 * Return the value associated with the column: num_dose_repetida
	 */
	public java.lang.Long getNumeroDoseRepetida () {
		return getPropertyValue(this, numeroDoseRepetida, PROP_NUMERO_DOSE_REPETIDA); 
	}

	/**
	 * Set the value related to the column: num_dose_repetida
	 * @param numeroDoseRepetida the num_dose_repetida value
	 */
	public void setNumeroDoseRepetida (java.lang.Long numeroDoseRepetida) {
//        java.lang.Long numeroDoseRepetidaOld = this.numeroDoseRepetida;
		this.numeroDoseRepetida = numeroDoseRepetida;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDoseRepetida", numeroDoseRepetidaOld, numeroDoseRepetida);
	}



	/**
	 * Return the value associated with the column: flag_inadvertida
	 */
	public java.lang.String getFlagInadvertida () {
		return getPropertyValue(this, flagInadvertida, PROP_FLAG_INADVERTIDA); 
	}

	/**
	 * Set the value related to the column: flag_inadvertida
	 * @param flagInadvertida the flag_inadvertida value
	 */
	public void setFlagInadvertida (java.lang.String flagInadvertida) {
//        java.lang.String flagInadvertidaOld = this.flagInadvertida;
		this.flagInadvertida = flagInadvertida;
//        this.getPropertyChangeSupport().firePropertyChange ("flagInadvertida", flagInadvertidaOld, flagInadvertida);
	}



	/**
	 * Return the value associated with the column: dt_imunizacao
	 */
	public java.util.Date getDataImunizacao () {
		return getPropertyValue(this, dataImunizacao, PROP_DATA_IMUNIZACAO); 
	}

	/**
	 * Set the value related to the column: dt_imunizacao
	 * @param dataImunizacao the dt_imunizacao value
	 */
	public void setDataImunizacao (java.util.Date dataImunizacao) {
//        java.util.Date dataImunizacaoOld = this.dataImunizacao;
		this.dataImunizacao = dataImunizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataImunizacao", dataImunizacaoOld, dataImunizacao);
	}



	/**
	 * Return the value associated with the column: lote
	 */
	public java.lang.String getLote () {
		return getPropertyValue(this, lote, PROP_LOTE); 
	}

	/**
	 * Set the value related to the column: lote
	 * @param lote the lote value
	 */
	public void setLote (java.lang.String lote) {
//        java.lang.String loteOld = this.lote;
		this.lote = lote;
//        this.getPropertyChangeSupport().firePropertyChange ("lote", loteOld, lote);
	}



	/**
	 * Return the value associated with the column: ds_bairro
	 */
	public java.lang.String getDescricaoBairro () {
		return getPropertyValue(this, descricaoBairro, PROP_DESCRICAO_BAIRRO); 
	}

	/**
	 * Set the value related to the column: ds_bairro
	 * @param descricaoBairro the ds_bairro value
	 */
	public void setDescricaoBairro (java.lang.String descricaoBairro) {
//        java.lang.String descricaoBairroOld = this.descricaoBairro;
		this.descricaoBairro = descricaoBairro;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoBairro", descricaoBairroOld, descricaoBairro);
	}



	/**
	 * Return the value associated with the column: sigla_zona_domiciliar
	 */
	public java.lang.String getSiglaZonaDomiciliar () {
		return getPropertyValue(this, siglaZonaDomiciliar, PROP_SIGLA_ZONA_DOMICILIAR); 
	}

	/**
	 * Set the value related to the column: sigla_zona_domiciliar
	 * @param siglaZonaDomiciliar the sigla_zona_domiciliar value
	 */
	public void setSiglaZonaDomiciliar (java.lang.String siglaZonaDomiciliar) {
//        java.lang.String siglaZonaDomiciliarOld = this.siglaZonaDomiciliar;
		this.siglaZonaDomiciliar = siglaZonaDomiciliar;
//        this.getPropertyChangeSupport().firePropertyChange ("siglaZonaDomiciliar", siglaZonaDomiciliarOld, siglaZonaDomiciliar);
	}



	/**
	 * Return the value associated with the column: cod_grupo_atendimento
	 */
	public java.lang.Long getCodigoGrupoAtendimento () {
		return getPropertyValue(this, codigoGrupoAtendimento, PROP_CODIGO_GRUPO_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: cod_grupo_atendimento
	 * @param codigoGrupoAtendimento the cod_grupo_atendimento value
	 */
	public void setCodigoGrupoAtendimento (java.lang.Long codigoGrupoAtendimento) {
//        java.lang.Long codigoGrupoAtendimentoOld = this.codigoGrupoAtendimento;
		this.codigoGrupoAtendimento = codigoGrupoAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoGrupoAtendimento", codigoGrupoAtendimentoOld, codigoGrupoAtendimento);
	}



	/**
	 * Return the value associated with the column: flag_comunicante_hanseniase
	 */
	public java.lang.String getFlagComunicanteHanseniase () {
		return getPropertyValue(this, flagComunicanteHanseniase, PROP_FLAG_COMUNICANTE_HANSENIASE); 
	}

	/**
	 * Set the value related to the column: flag_comunicante_hanseniase
	 * @param flagComunicanteHanseniase the flag_comunicante_hanseniase value
	 */
	public void setFlagComunicanteHanseniase (java.lang.String flagComunicanteHanseniase) {
//        java.lang.String flagComunicanteHanseniaseOld = this.flagComunicanteHanseniase;
		this.flagComunicanteHanseniase = flagComunicanteHanseniase;
//        this.getPropertyChangeSupport().firePropertyChange ("flagComunicanteHanseniase", flagComunicanteHanseniaseOld, flagComunicanteHanseniase);
	}



	/**
	 * Return the value associated with the column: flag_gestante
	 */
	public java.lang.String getFlagGestante () {
		return getPropertyValue(this, flagGestante, PROP_FLAG_GESTANTE); 
	}

	/**
	 * Set the value related to the column: flag_gestante
	 * @param flagGestante the flag_gestante value
	 */
	public void setFlagGestante (java.lang.String flagGestante) {
//        java.lang.String flagGestanteOld = this.flagGestante;
		this.flagGestante = flagGestante;
//        this.getPropertyChangeSupport().firePropertyChange ("flagGestante", flagGestanteOld, flagGestante);
	}



	/**
	 * Return the value associated with the column: cod_produtor
	 */
	public java.lang.Long getCodigoProdutor () {
		return getPropertyValue(this, codigoProdutor, PROP_CODIGO_PRODUTOR); 
	}

	/**
	 * Set the value related to the column: cod_produtor
	 * @param codigoProdutor the cod_produtor value
	 */
	public void setCodigoProdutor (java.lang.Long codigoProdutor) {
//        java.lang.Long codigoProdutorOld = this.codigoProdutor;
		this.codigoProdutor = codigoProdutor;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoProdutor", codigoProdutorOld, codigoProdutor);
	}



	/**
	 * Return the value associated with the column: cod_estrategia
	 */
	public java.lang.Long getCodigoEstrategia () {
		return getPropertyValue(this, codigoEstrategia, PROP_CODIGO_ESTRATEGIA); 
	}

	/**
	 * Set the value related to the column: cod_estrategia
	 * @param codigoEstrategia the cod_estrategia value
	 */
	public void setCodigoEstrategia (java.lang.Long codigoEstrategia) {
//        java.lang.Long codigoEstrategiaOld = this.codigoEstrategia;
		this.codigoEstrategia = codigoEstrategia;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoEstrategia", codigoEstrategiaOld, codigoEstrategia);
	}



	/**
	 * Return the value associated with the column: cod_motivo_indicacao
	 */
	public java.lang.Long getCodigoMotivoIndicacao () {
		return getPropertyValue(this, codigoMotivoIndicacao, PROP_CODIGO_MOTIVO_INDICACAO); 
	}

	/**
	 * Set the value related to the column: cod_motivo_indicacao
	 * @param codigoMotivoIndicacao the cod_motivo_indicacao value
	 */
	public void setCodigoMotivoIndicacao (java.lang.Long codigoMotivoIndicacao) {
//        java.lang.Long codigoMotivoIndicacaoOld = this.codigoMotivoIndicacao;
		this.codigoMotivoIndicacao = codigoMotivoIndicacao;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoMotivoIndicacao", codigoMotivoIndicacaoOld, codigoMotivoIndicacao);
	}



	/**
	 * Return the value associated with the column: cns_profissional
	 */
	public java.lang.Long getCnsProfissional () {
		return getPropertyValue(this, cnsProfissional, PROP_CNS_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cns_profissional
	 * @param cnsProfissional the cns_profissional value
	 */
	public void setCnsProfissional (java.lang.Long cnsProfissional) {
//        java.lang.Long cnsProfissionalOld = this.cnsProfissional;
		this.cnsProfissional = cnsProfissional;
//        this.getPropertyChangeSupport().firePropertyChange ("cnsProfissional", cnsProfissionalOld, cnsProfissional);
	}



	/**
	 * Return the value associated with the column: cbo_indicador
	 */
	public java.lang.String getCboIndicador () {
		return getPropertyValue(this, cboIndicador, PROP_CBO_INDICADOR); 
	}

	/**
	 * Set the value related to the column: cbo_indicador
	 * @param cboIndicador the cbo_indicador value
	 */
	public void setCboIndicador (java.lang.String cboIndicador) {
//        java.lang.String cboIndicadorOld = this.cboIndicador;
		this.cboIndicador = cboIndicador;
//        this.getPropertyChangeSupport().firePropertyChange ("cboIndicador", cboIndicadorOld, cboIndicador);
	}



	/**
	 * Return the value associated with the column: flag_atualizacao_caderneta
	 */
	public java.lang.String getFlagAtualizacaoCaderneta () {
		return getPropertyValue(this, flagAtualizacaoCaderneta, PROP_FLAG_ATUALIZACAO_CADERNETA); 
	}

	/**
	 * Set the value related to the column: flag_atualizacao_caderneta
	 * @param flagAtualizacaoCaderneta the flag_atualizacao_caderneta value
	 */
	public void setFlagAtualizacaoCaderneta (java.lang.String flagAtualizacaoCaderneta) {
//        java.lang.String flagAtualizacaoCadernetaOld = this.flagAtualizacaoCaderneta;
		this.flagAtualizacaoCaderneta = flagAtualizacaoCaderneta;
//        this.getPropertyChangeSupport().firePropertyChange ("flagAtualizacaoCaderneta", flagAtualizacaoCadernetaOld, flagAtualizacaoCaderneta);
	}



	/**
	 * Return the value associated with the column: dt_aprazamento
	 */
	public java.util.Date getDataAprazamento () {
		return getPropertyValue(this, dataAprazamento, PROP_DATA_APRAZAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_aprazamento
	 * @param dataAprazamento the dt_aprazamento value
	 */
	public void setDataAprazamento (java.util.Date dataAprazamento) {
//        java.util.Date dataAprazamentoOld = this.dataAprazamento;
		this.dataAprazamento = dataAprazamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAprazamento", dataAprazamentoOld, dataAprazamento);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: flag_puerpera
	 */
	public java.lang.String getFlagPuerpera () {
		return getPropertyValue(this, flagPuerpera, PROP_FLAG_PUERPERA); 
	}

	/**
	 * Set the value related to the column: flag_puerpera
	 * @param flagPuerpera the flag_puerpera value
	 */
	public void setFlagPuerpera (java.lang.String flagPuerpera) {
//        java.lang.String flagPuerperaOld = this.flagPuerpera;
		this.flagPuerpera = flagPuerpera;
//        this.getPropertyChangeSupport().firePropertyChange ("flagPuerpera", flagPuerperaOld, flagPuerpera);
	}



	/**
	 * Return the value associated with the column: cd_pni
	 */
	public br.com.ksisolucoes.vo.vacina.pni.Pni getPni () {
		return getPropertyValue(this, pni, PROP_PNI); 
	}

	/**
	 * Set the value related to the column: cd_pni
	 * @param pni the cd_pni value
	 */
	public void setPni (br.com.ksisolucoes.vo.vacina.pni.Pni pni) {
//        br.com.ksisolucoes.vo.vacina.pni.Pni pniOld = this.pni;
		this.pni = pni;
//        this.getPropertyChangeSupport().firePropertyChange ("pni", pniOld, pni);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cd_empresa_lanc
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresaLancamento () {
		return getPropertyValue(this, empresaLancamento, PROP_EMPRESA_LANCAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_empresa_lanc
	 * @param empresaLancamento the cd_empresa_lanc value
	 */
	public void setEmpresaLancamento (br.com.ksisolucoes.vo.basico.Empresa empresaLancamento) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaLancamentoOld = this.empresaLancamento;
		this.empresaLancamento = empresaLancamento;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaLancamento", empresaLancamentoOld, empresaLancamento);
	}



	/**
	 * Return the value associated with the column: cd_empresa_cad
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresaCadastro () {
		return getPropertyValue(this, empresaCadastro, PROP_EMPRESA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: cd_empresa_cad
	 * @param empresaCadastro the cd_empresa_cad value
	 */
	public void setEmpresaCadastro (br.com.ksisolucoes.vo.basico.Empresa empresaCadastro) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaCadastroOld = this.empresaCadastro;
		this.empresaCadastro = empresaCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaCadastro", empresaCadastroOld, empresaCadastro);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vacina.pni.PniRegistros)) return false;
		else {
			br.com.ksisolucoes.vo.vacina.pni.PniRegistros pniRegistros = (br.com.ksisolucoes.vo.vacina.pni.PniRegistros) obj;
			if (null == this.getCodigo() || null == pniRegistros.getCodigo()) return false;
			else return (this.getCodigo().equals(pniRegistros.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}