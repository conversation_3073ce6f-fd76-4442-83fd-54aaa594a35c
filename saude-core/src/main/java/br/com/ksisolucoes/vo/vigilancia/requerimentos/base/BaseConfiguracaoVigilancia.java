package br.com.ksisolucoes.vo.vigilancia.requerimentos.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the configuracao_vigilancia table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="configuracao_vigilancia"
 */

public abstract class BaseConfiguracaoVigilancia extends BaseRootVO implements Serializable {

	public static String REF = "ConfiguracaoVigilancia";
	public static final String PROP_ANO_BASE_REVALIDACAO_ALVARA = "anoBaseRevalidacaoAlvara";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_TIPO_ORDENACAO_REQUERIMENTOS = "tipoOrdenacaoRequerimentos";
	public static final String PROP_DATA_INICIAL_LICENCA_TRANSPORTE = "dataInicialLicencaTransporte";
	public static final String PROP_NUMERACAO_FINAL_RECEITA_B = "numeracaoFinalReceitaB";
	public static final String PROP_VALIDADE_EMAIL_CONFIRMACAO = "validadeEmailConfirmacao";
	public static final String PROP_DATA_VENCIMENTO_REVALIDACAO_ALVARA = "dataVencimentoRevalidacaoAlvara";
	public static final String PROP_NUM_PROCESSO_ADMINISTRATIVO = "numProcessoAdministrativo";
	public static final String PROP_VALIDADE_TREINAMENTO_DATA_FIXA = "validadeTreinamentoDataFixa";
	public static final String PROP_INICIAR_SEQUENCIAL_ALVARA_ANO_ANTERIOR = "iniciarSequencialAlvaraAnoAnterior";
	public static final String PROP_VALIDADE_LICENCA_DATA_FIXA = "validadeLicencaDataFixa";
	public static final String PROP_MENSAGEM_DENUNCIA_ANONIMA = "mensagemDenunciaAnonima";
	public static final String PROP_VALIDADE_ALVARA_DATA_VENCIMENTO = "validadeAlvaraDataVencimento";
	public static final String PROP_PRAZO_RECURSO_AUTO_PENALIDADE = "prazoRecursoAutoPenalidade";
	public static final String PROP_FLAG_TIPO_GESTAO_REQUERIMENTO = "flagTipoGestaoRequerimento";
	public static final String PROP_INICIAR_SEQUENCIAL = "iniciarSequencial";
	public static final String PROP_ALVARA_INICIAL_ANOS_VALIDADE = "alvaraInicialAnosValidade";
	public static final String PROP_NUM_RELATORIO_INSPECAO = "numRelatorioInspecao";
	public static final String PROP_TEXTO_REQ_EXUMACAO1 = "textoReqExumacao1";
	public static final String PROP_LICENCA_TRANSPORTE_USA_DATA_VENCIMENTO_DA_ATIVIDADE_ESTABELECIMENTO = "licencaTransporteUsaDataVencimentoDaAtividadeEstabelecimento";
	public static final String PROP_ALVARA_INICIAL_USA_DATA_VENCIMENTO_DA_ATIVIDADE_ESTABELECIMENTO = "alvaraInicialUsaDataVencimentoDaAtividadeEstabelecimento";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TEXTO_REQ_EXUMACAO2 = "textoReqExumacao2";
	public static final String PROP_MARCA_DAGUA_ALVARA = "marcaDaguaAlvara";
	public static final String PROP_INICIAR_SEQUENCIAL_ALVARA = "iniciarSequencialAlvara";
	public static final String PROP_VALIDADE_LICENCA_PERIODO = "validadeLicencaPeriodo";
	public static final String PROP_ANO_BASE_LICENCA_TRANSPORTE = "anoBaseLicencaTransporte";
	public static final String PROP_DATA_FINAL_REVALIDACAO_ALVARA = "dataFinalRevalidacaoAlvara";
	public static final String PROP_ESTOQUE_MINIMO_RECEITA_TALIDOMIDA = "estoqueMinimoReceitaTalidomida";
	public static final String PROP_DATA_ALTERACAO = "dataAlteracao";
	public static final String PROP_TIPO_DATABASE_CALCULO_PRAZO_RECURSO = "tipoDatabaseCalculoPrazoRecurso";
	public static final String PROP_CONCEDIDO = "concedido";
	public static final String PROP_ENQUADRAMENTO_LEGAL_AUTO_MULTA = "enquadramentoLegalAutoMulta";
	public static final String PROP_DATA_FINAL_LICENCA_TRANSPORTE = "dataFinalLicencaTransporte";
	public static final String PROP_ORIENTACAO_TREINAMENTO = "orientacaoTreinamento";
	public static final String PROP_USUARIO_ALTERACAO = "usuarioAlteracao";
	public static final String PROP_TEXTO_DESCRICAO_SELO_PRANCHAS_HIDROSSANITARIO_DECLARATORIO = "textoDescricaoSeloPranchasHidrossanitarioDeclaratorio";
	public static final String PROP_VALIDADE_TREINAMENTO_PERIODO = "validadeTreinamentoPeriodo";
	public static final String PROP_EXIBIR_GRUPO_ALVARA = "exibirGrupoAlvara";
	public static final String PROP_NUMERACAO_PARECER_TECNICO = "numeracaoParecerTecnico";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_INICIAR_PROTOCOLO_RECEITA_B_C2 = "iniciarProtocoloReceitaBC2";
	public static final String PROP_CIENCIA_AUTO_INFRACAO = "cienciaAutoInfracao";
	public static final String PROP_TIPO_DATA_CALC_ALVARA = "tipoDataCalcAlvara";
	public static final String PROP_ALVARA_REVALIDACAO_USA_DATA_FIXA = "alvaraRevalidacaoUsaDataFixa";
	public static final String PROP_FLAG_TERCEIRA_INSTANCIA_PROCESSO_ADMINISTRATIVO = "flagTerceiraInstanciaProcessoAdministrativo";
	public static final String PROP_INICIAR_PROTOCOLO_RECEITA_B = "iniciarProtocoloReceitaB";
	public static final String PROP_INICIO_PROXIMO_DIA_UTIL_AUTO_MULTA = "inicioProximoDiaUtilAutoMulta";
	public static final String PROP_INICIAR_PROTOCOLO_RECEITA_A = "iniciarProtocoloReceitaA";
	public static final String PROP_TIPO_CALCULO_PRAZO_AUTO_MULTA = "tipoCalculoPrazoAutoMulta";
	public static final String PROP_INICIAR_SEQUENCIAL_TREINAMENTO = "iniciarSequencialTreinamento";
	public static final String PROP_ESTOQUE_MINIMO_RECEITA_A = "estoqueMinimoReceitaA";
	public static final String PROP_TIPO_CALCULO_PRAZO_DEFESA = "tipoCalculoPrazoDefesa";
	public static final String PROP_ENQUADRAMENTO_LEGAL_INTIMACAO = "enquadramentoLegalIntimacao";
	public static final String PROP_BRASAO_MARCA_DAGUA = "brasaoMarcaDagua";
	public static final String PROP_TIPO_DATABASE_CALCULO_PRAZO_EXIGENCIA = "tipoDatabaseCalculoPrazoExigencia";
	public static final String PROP_CREDENCIAMENTO_PARA = "credenciamentoPara";
	public static final String PROP_FLAG_EMITIR_TERMO_SOLICITACAO_SERVICO = "flagEmitirTermoSolicitacaoServico";
	public static final String PROP_FLAG_OBRIGA_INFORMAR_ISENTO_TAXA = "flagObrigaInformarIsentoTaxa";
	public static final String PROP_PRAZO_AUTO_MULTA = "prazoAutoMulta";
	public static final String PROP_OBSERVACAO_DESTAQUE_TREINAMENTO = "observacaoDestaqueTreinamento";
	public static final String PROP_NUM_AUTO_INFRACAO = "numAutoInfracao";
	public static final String PROP_INICIAR_PROTOCOLO_RECEITA_B_B2 = "iniciarProtocoloReceitaBB2";
	public static final String PROP_FLAG_PERMITE_ALVARA_PROVISORIO = "flagPermiteAlvaraProvisorio";
	public static final String PROP_INICIAR_NUMERACAO_BAIXA = "iniciarNumeracaoBaixa";
	public static final String PROP_LICENCA_TRANSPORTE_ANOS_VALIDADE = "licencaTransporteAnosValidade";
	public static final String PROP_HABITESE_IMPORTANTE = "habiteseImportante";
	public static final String PROP_CIENCIA_AUTO_INTIMACAO = "cienciaAutoIntimacao";
	public static final String PROP_TEXTO_DIMENSIONAMENTO = "textoDimensionamento";
	public static final String PROP_NUMERO_PARECER_INICIAL = "numeroParecerInicial";
	public static final String PROP_CREDENCIAMENTO_TREINAMENTO_ANOS_VALIDADE = "credenciamentoTreinamentoAnosValidade";
	public static final String PROP_SETOR_VIGILANCIA_RECEITA_SECUNDARIO = "setorVigilanciaReceitaSecundario";
	public static final String PROP_FLAG_FORMATO_SEQUENCIAL = "flagFormatoSequencial";
	public static final String PROP_QTD_ANOS_VENCIMENTO_ALVARA_INICIAL = "qtdAnosVencimentoAlvaraInicial";
	public static final String PROP_ANO_BASE_GERAL = "anoBaseGeral";
	public static final String PROP_SETOR_VIGILANCIA_RECEITA = "setorVigilanciaReceita";
	public static final String PROP_SETOR_VIGILANCIA_ANALISE_PROJETOS = "setorVigilanciaAnaliseProjetos";
	public static final String PROP_TIPO_CALCULO_PRAZO_RECURSO = "tipoCalculoPrazoRecurso";
	public static final String PROP_NUMERO_INICIAL_APROVACAO_LAUDO_HIDROSSANITARIO = "numeroInicialAprovacaoLaudoHidrossanitario";
	public static final String PROP_LINHA2_CABECALHO = "linha2Cabecalho";
	public static final String PROP_TIPO_DATA_CALCULO_AUTORIZACAO_SANITARIA = "tipoDataCalculoAutorizacaoSanitaria";
	public static final String PROP_INICIO_PROXIMO_DIA_UTIL_AUTO_PENALIDADE = "inicioProximoDiaUtilAutoPenalidade";
	public static final String PROP_NUMERO_INICIAL_APROVACAO_LAUDO_ARQUITETONICO = "numeroInicialAprovacaoLaudoArquitetonico";
	public static final String PROP_PERIODO_VALIDADE_REVALIDACAO_ALVARA = "periodoValidadeRevalidacaoAlvara";
	public static final String PROP_ANO_BASE_AUTORIZACAO_SANITARIA = "anoBaseAutorizacaoSanitaria";
	public static final String PROP_TEXTO_RECOMENDACOES = "textoRecomendacoes";
	public static final String PROP_FLAG_OBRIGA_DADOS_VEICULO = "flagObrigaDadosVeiculo";
	public static final String PROP_DATA_VENCIMENTO_AUTORIZACAO_SANITARIA = "dataVencimentoAutorizacaoSanitaria";
	public static final String PROP_NUM_AUTO_MULTA = "numAutoMulta";
	public static final String PROP_DIAS_SEM_RESPONSAVEL_TECNICO = "diasSemResponsavelTecnico";
	public static final String PROP_FLAG_OBRIGA_DADOS_ESTABELECIMENTO = "flagObrigaDadosEstabelecimento";
	public static final String PROP_INICIAR_NUMERACAO_NADA_CONSTA = "iniciarNumeracaoNadaConsta";
	public static final String PROP_FLAG_SELECIONA_TALAO_MANUAL = "flagSelecionaTalaoManual";
	public static final String PROP_TIPO_CALCULO_PRAZO_EXIGENCIA = "tipoCalculoPrazoExigencia";
	public static final String PROP_NUM_TERMO_AJUSTAMENTO_CONDUTA = "numTermoAjustamentoConduta";
	public static final String PROP_LOGO_CABECALHO_RELATORIO = "logoCabecalhoRelatorio";
	public static final String PROP_FINALIZAR_REQUERIMENTO_RECEITA = "finalizarRequerimentoReceita";
	public static final String PROP_SETOR_VIGILANCIA_GERAL_DENUNCIA = "setorVigilanciaGeralDenuncia";
	public static final String PROP_TIPO_DATABASE_CALCULO_PRAZO_AUTO_MULTA = "tipoDatabaseCalculoPrazoAutoMulta";
	public static final String PROP_PRAZO_DEFESA_AUTO_INFRACAO = "prazoDefesaAutoInfracao";
	public static final String PROP_TEXTO_DESCRICAO_SELO_PRANCHAS_HIDROSSANITARIO_PADRAO = "textoDescricaoSeloPranchasHidrossanitarioPadrao";
	public static final String PROP_VALIDADE_ALVARA_DATA_FIXA = "validadeAlvaraDataFixa";
	public static final String PROP_AUTORIZACAO_SANITARIA_USA_DATA_FIXA = "autorizacaoSanitariaUsaDataFixa";
	public static final String PROP_SIGLA_ORGAO_MUNICIPAL_PROJETO_ARQUITETONICO = "siglaOrgaoMunicipalProjetoArquitetonico";
	public static final String PROP_FLAG_PERMITE_IMPRESSAO_EXTERNA = "flagPermiteImpressaoExterna";
	public static final String PROP_TIPO_DATABASE_CALCULO_PRAZO_DEFESA = "tipoDatabaseCalculoPrazoDefesa";
	public static final String PROP_CIENCIA_AUTO_MULTA = "cienciaAutoMulta";
	public static final String PROP_OBRIGATORIDADE_REFRIGERADO = "obrigatoridadeRefrigerado";
	public static final String PROP_FLAG_SELECIONA_TALAO_MANUAL_TALIDOMIDA = "flagSelecionaTalaoManualTalidomida";
	public static final String PROP_TEXTO_INSTALACOES_ORDINARIAS = "textoInstalacoesOrdinarias";
	public static final String PROP_VALIDADE_LICENCA_DATA_VENCIMENTO = "validadeLicencaDataVencimento";
	public static final String PROP_TIPO_DATA_CALC_LICENCA_TRANSPORTE = "tipoDataCalcLicencaTransporte";
	public static final String PROP_INICIAR_SEQUENCIAL_HABITESE = "iniciarSequencialHabitese";
	public static final String PROP_FLAG_OBRIGATORIO_INFORMAR_TELEFONE = "flagObrigatorioInformarTelefone";
	public static final String PROP_HABITESE_PARA = "habitesePara";
	public static final String PROP_LINHA3_CABECALHO = "linha3Cabecalho";
	public static final String PROP_INICIAR_PROTOCOLO_RECEITA_TALIDOMIDA = "iniciarProtocoloReceitaTalidomida";
	public static final String PROP_OBSERVACAO_DESTAQUE_ALVARA = "observacaoDestaqueAlvara";
	public static final String PROP_QUANTIDADE_DIAS_INSPECAO_RETROATIVA = "quantidadeDiasInspecaoRetroativa";
	public static final String PROP_OBSERVACAO_DESTAQUE_CADASTRO_EVENTO = "observacaoDestaqueCadastroEvento";
	public static final String PROP_QUANTIDADE_PERIODO_VALIDADE_TREINAMENTO = "quantidadePeriodoValidadeTreinamento";
	public static final String PROP_FLAG_CADASTRA_REQUERIMENTO_EXTERNO = "flagCadastraRequerimentoExterno";
	public static final String PROP_DESCRICAO_AUTO_MULTA = "descricaoAutoMulta";
	public static final String PROP_INICIAR_PROTOCOLO_LICENCA_TRANSPORTE_ANO_ANTERIOR = "iniciarProtocoloLicencaTransporteAnoAnterior";
	public static final String PROP_USUARIO_CADASTRO = "usuarioCadastro";
	public static final String PROP_FLAG_HABILITA_CAMPO_POSSUI_ESTAB_PRINCIPAL = "flagHabilitaCampoPossuiEstabPrincipal";
	public static final String PROP_FLAG_LINHA_ASSINATURA = "flagLinhaAssinatura";
	public static final String PROP_INICIO_PROXIMO_DIA_UTIL_AUTO_INTIMACAO = "inicioProximoDiaUtilAutoIntimacao";
	public static final String PROP_TEXTO_DESCRICAO_SELO_PRANCHAS_PROJETO_ARQUITETONICO_SANITARIO = "textoDescricaoSeloPranchasProjetoArquitetonicoSanitario";
	public static final String PROP_TIPO_FLUXO_EXTERNO = "tipoFluxoExterno";
	public static final String PROP_ANO_BASE_RECEITA = "anoBaseReceita";
	public static final String PROP_TIPO_ATIVIDADE_ALVARA = "tipoAtividadeAlvara";
	public static final String PROP_LINHA4_CABECALHO = "linha4Cabecalho";
	public static final String PROP_PERIODO_VALIDADE_AUTORIZACAO_SANITARIA = "periodoValidadeAutorizacaoSanitaria";
	public static final String PROP_TIPO_DATA_CALCULO_REVALIDACAO_ALVARA = "tipoDataCalculoRevalidacaoAlvara";
	public static final String PROP_SETOR_VIGILANCIA_DEFESA_PREVIA = "setorVigilanciaDefesaPrevia";
	public static final String PROP_CLAUSULAS_GERAIS_TERMO_AJUSTAMENTO_CONDUTA = "clausulasGeraisTermoAjustamentoConduta";
	public static final String PROP_NUM_AUTO_PENALIDADE = "numAutoPenalidade";
	public static final String PROP_AUTORIZACAO_SANITARIA_ANOS_VALIDADE = "autorizacaoSanitariaAnosValidade";
	public static final String PROP_ATIVIDADE_PADRAO_USUARIO_EXTERNO = "atividadePadraoUsuarioExterno";
	public static final String PROP_TEXTO_REPROVADO = "textoReprovado";
	public static final String PROP_FLAG_TIPO_GESTAO_ATIVIDADE = "flagTipoGestaoAtividade";
	public static final String PROP_INICIAR_PROTOCOLO_LICENCA_TRANSPORTE = "iniciarProtocoloLicencaTransporte";
	public static final String PROP_REVALIDACAO_ALVARA_USA_DATA_VENCIMENTO_DA_ATIVIDADE_ESTABELECIMENTO = "revalidacaoAlvaraUsaDataVencimentoDaAtividadeEstabelecimento";
	public static final String PROP_VALOR_EXCEDIDO_ANALISE_PROJETO = "valorExcedidoAnaliseProjeto";
	public static final String PROP_NUM_AUTO_INTIMACAO = "numAutoIntimacao";
	public static final String PROP_VALIDADE_ALVARA_PERIODO = "validadeAlvaraPeriodo";
	public static final String PROP_TIPO_DATA_CALC_CREDENCIAMENTO = "tipoDataCalcCredenciamento";
	public static final String PROP_ANO_BASE_ALVARA = "anoBaseAlvara";
	public static final String PROP_OBSERVACAO_VACINACAO_EXTRAMURO2 = "observacaoVacinacaoExtramuro2";
	public static final String PROP_MARCA_DAGUA_TREINAMENTO = "marcaDaguaTreinamento";
	public static final String PROP_OBSERVACAO_VACINACAO_EXTRAMURO1 = "observacaoVacinacaoExtramuro1";
	public static final String PROP_FLAG_HABILITA_DENUNCIA_RECLAMACAO_EXTERNO = "flagHabilitaDenunciaReclamacaoExterno";
	public static final String PROP_LINHA1_CABECALHO = "linha1Cabecalho";
	public static final String PROP_NUMERACAO_PARECER_CONFORMIDADE_TECNICA = "numeracaoParecerConformidadeTecnica";
	public static final String PROP_LOGO_REQUERIMENTO_DECLARATORIO = "logoRequerimentoDeclaratorio";
	public static final String PROP_FUNDAMENTO_LEGAL_REGULAMENTACAO = "fundamentoLegalRegulamentacao";
	public static final String PROP_ENQUADRAMENTO_LEGAL_INFRACAO = "enquadramentoLegalInfracao";
	public static final String PROP_REVALIDACAO_ALVARA_ANOS_VALIDADE = "revalidacaoAlvaraAnosValidade";
	public static final String PROP_DATA_INICIAL_REVALIDACAO_ALVARA = "dataInicialRevalidacaoAlvara";
	public static final String PROP_TEXTO_FUNCIONALIDADE_EDIFICACAO = "textoFuncionalidadeEdificacao";
	public static final String PROP_OBRIGA_RG_DT_EMISSAO_RESPONSAVEL_TECNICO = "obrigaRgDtEmissaoResponsavelTecnico";
	public static final String PROP_EMPRESA_PADRAO_USUARIO_EXTERNO = "empresaPadraoUsuarioExterno";
	public static final String PROP_CIENCIA_AUTO_PENALIDADE = "cienciaAutoPenalidade";
	public static final String PROP_TEXTO_APROVADO = "textoAprovado";
	public static final String PROP_URL_ROTEIRO_INSPECAO = "urlRoteiroInspecao";
	public static final String PROP_NUMERACAO_INICIAL_RECEITA_B = "numeracaoInicialReceitaB";
	public static final String PROP_INICIO_PROXIMO_DIA_UTIL_AUTO_INFRACAO = "inicioProximoDiaUtilAutoInfracao";
	public static final String PROP_VALIDADE_TREINAMENTO_DATA_VENCIMENTO = "validadeTreinamentoDataVencimento";
	public static final String PROP_FLAG_VISUALIZAR_DADOS_OBRA = "flagVisualizarDadosObra";
	public static final String PROP_OBSERVACAO_DESTAQUE_AUTORIZACAO_SANITARIA = "observacaoDestaqueAutorizacaoSanitaria";


	// constructors
	public BaseConfiguracaoVigilancia () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseConfiguracaoVigilancia (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseConfiguracaoVigilancia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
		java.util.Date dataCadastro,
		java.lang.String textoAprovado,
		java.lang.String textoReprovado,
		java.lang.Long obrigaRgDtEmissaoResponsavelTecnico,
		java.lang.Long flagSelecionaTalaoManual,
		java.lang.Long flagTipoGestaoRequerimento,
		java.lang.Long flagTipoGestaoAtividade,
		java.lang.Long flagSelecionaTalaoManualTalidomida,
		java.lang.Long flagObrigaDadosVeiculo,
		java.lang.Long flagVisualizarDadosObra,
		java.lang.Long qtdAnosVencimentoAlvaraInicial) {

		this.setCodigo(codigo);
		this.setUsuarioCadastro(usuarioCadastro);
		this.setDataCadastro(dataCadastro);
		this.setTextoAprovado(textoAprovado);
		this.setTextoReprovado(textoReprovado);
		this.setObrigaRgDtEmissaoResponsavelTecnico(obrigaRgDtEmissaoResponsavelTecnico);
		this.setFlagSelecionaTalaoManual(flagSelecionaTalaoManual);
		this.setFlagTipoGestaoRequerimento(flagTipoGestaoRequerimento);
		this.setFlagTipoGestaoAtividade(flagTipoGestaoAtividade);
		this.setFlagSelecionaTalaoManualTalidomida(flagSelecionaTalaoManualTalidomida);
		this.setFlagObrigaDadosVeiculo(flagObrigaDadosVeiculo);
		this.setFlagVisualizarDadosObra(flagVisualizarDadosObra);
		this.setQtdAnosVencimentoAlvaraInicial(qtdAnosVencimentoAlvaraInicial);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long iniciarSequencial;
	private java.lang.String textoReqExumacao1;
	private java.lang.String textoReqExumacao2;
	private java.util.Date dataCadastro;
	private java.util.Date dataAlteracao;
	private java.lang.Long iniciarSequencialHabitese;
	private java.lang.Long estoqueMinimoReceitaA;
	private java.lang.Long numeracaoInicialReceitaB;
	private java.lang.Long numeracaoFinalReceitaB;
	private java.lang.Long iniciarProtocoloReceitaB;
	private java.lang.Long iniciarProtocoloReceitaA;
	private java.lang.Long iniciarProtocoloReceitaBB2;
	private java.lang.Long iniciarProtocoloReceitaBC2;
	private java.lang.String linha1Cabecalho;
	private java.lang.String linha2Cabecalho;
	private java.lang.String linha3Cabecalho;
	private java.lang.Long validadeEmailConfirmacao;
	private java.lang.String cienciaAutoInfracao;
	private java.lang.Long prazoDefesaAutoInfracao;
	private java.lang.Long prazoRecursoAutoPenalidade;
	private java.lang.String cienciaAutoIntimacao;
	private java.lang.String cienciaAutoPenalidade;
	private java.lang.String fundamentoLegalRegulamentacao;
	private java.lang.String concedido;
	private java.lang.String linha4Cabecalho;
	private java.lang.Long anoBaseGeral;
	private java.lang.Long anoBaseReceita;
	private java.lang.Long iniciarNumeracaoBaixa;
	private java.lang.Long iniciarNumeracaoNadaConsta;
	private java.lang.Double valorExcedidoAnaliseProjeto;
	private java.lang.Long numeracaoParecerTecnico;
	private java.lang.Long numeracaoParecerConformidadeTecnica;
	private java.lang.Long diasSemResponsavelTecnico;
	private java.lang.String observacaoVacinacaoExtramuro1;
	private java.lang.String observacaoVacinacaoExtramuro2;
	private java.lang.String textoDimensionamento;
	private java.lang.String textoFuncionalidadeEdificacao;
	private java.lang.String textoInstalacoesOrdinarias;
	private java.lang.String textoRecomendacoes;
	private java.lang.Long flagLinhaAssinatura;
	private java.lang.Long flagEmitirTermoSolicitacaoServico;
	private java.lang.Long flagObrigaDadosEstabelecimento;
	private java.lang.Long tipoCalculoPrazoExigencia;
	private java.lang.Long tipoDatabaseCalculoPrazoExigencia;
	private java.lang.Long tipoCalculoPrazoDefesa;
	private java.lang.Long tipoDatabaseCalculoPrazoDefesa;
	private java.lang.Long tipoCalculoPrazoRecurso;
	private java.lang.Long tipoDatabaseCalculoPrazoRecurso;
	private java.lang.String textoAprovado;
	private java.lang.String textoReprovado;
	private java.lang.Long obrigatoridadeRefrigerado;
	private java.lang.Long numRelatorioInspecao;
	private java.lang.Long numAutoIntimacao;
	private java.lang.Long numAutoPenalidade;
	private java.lang.Long numAutoInfracao;
	private java.lang.String enquadramentoLegalIntimacao;
	private java.lang.String enquadramentoLegalInfracao;
	private java.lang.Long numProcessoAdministrativo;
	private java.lang.String enquadramentoLegalAutoMulta;
	private java.lang.String cienciaAutoMulta;
	private java.lang.String descricaoAutoMulta;
	private java.lang.Long numAutoMulta;
	private java.lang.Long prazoAutoMulta;
	private java.lang.Long tipoCalculoPrazoAutoMulta;
	private java.lang.Long tipoDatabaseCalculoPrazoAutoMulta;
	private java.lang.Long inicioProximoDiaUtilAutoMulta;
	private java.lang.Long inicioProximoDiaUtilAutoPenalidade;
	private java.lang.Long inicioProximoDiaUtilAutoInfracao;
	private java.lang.Long inicioProximoDiaUtilAutoIntimacao;
	private java.lang.Long obrigaRgDtEmissaoResponsavelTecnico;
	private java.lang.Long flagObrigaInformarIsentoTaxa;
	private java.lang.Long flagSelecionaTalaoManual;
	private java.lang.Long flagTipoGestaoRequerimento;
	private java.lang.Long flagTipoGestaoAtividade;
	private java.lang.Long iniciarProtocoloReceitaTalidomida;
	private java.lang.Long estoqueMinimoReceitaTalidomida;
	private java.lang.Long flagSelecionaTalaoManualTalidomida;
	private java.lang.Long flagObrigaDadosVeiculo;
	private java.lang.Long flagVisualizarDadosObra;
	private java.lang.Long flagTerceiraInstanciaProcessoAdministrativo;
	private java.lang.String mensagemDenunciaAnonima;
	private java.lang.Long numeroInicialAprovacaoLaudoHidrossanitario;
	private java.lang.String habitesePara;
	private java.lang.String habiteseImportante;
	private java.lang.Long tipoOrdenacaoRequerimentos;
	private java.lang.Long quantidadeDiasInspecaoRetroativa;
	private java.lang.Long finalizarRequerimentoReceita;
	private java.lang.Long flagFormatoSequencial;
	private java.lang.Long flagCadastraRequerimentoExterno;
	private java.lang.Boolean flagHabilitaDenunciaReclamacaoExterno;
	private java.lang.Long tipoFluxoExterno;
	private java.lang.String urlRoteiroInspecao;
	private java.lang.String observacaoDestaqueCadastroEvento;
	private java.lang.Long flagHabilitaCampoPossuiEstabPrincipal;
	private java.lang.Long flagObrigatorioInformarTelefone;
	private java.lang.String siglaOrgaoMunicipalProjetoArquitetonico;
	private java.lang.String textoDescricaoSeloPranchasHidrossanitarioPadrao;
	private java.lang.String textoDescricaoSeloPranchasHidrossanitarioDeclaratorio;
	private java.lang.String textoDescricaoSeloPranchasProjetoArquitetonicoSanitario;
	private java.lang.Long numeroInicialAprovacaoLaudoArquitetonico;
	private java.lang.Long iniciarProtocoloLicencaTransporte;
	private java.lang.Long validadeLicencaDataFixa;
	private java.lang.Long validadeLicencaPeriodo;
	private java.util.Date validadeLicencaDataVencimento;
	private java.lang.Long licencaTransporteUsaDataVencimentoDaAtividadeEstabelecimento;
	private java.util.Date dataInicialLicencaTransporte;
	private java.util.Date dataFinalLicencaTransporte;
	private java.lang.Long anoBaseLicencaTransporte;
	private java.lang.Long iniciarProtocoloLicencaTransporteAnoAnterior;
	private java.lang.Long tipoDataCalcLicencaTransporte;
	private java.lang.Long alvaraInicialUsaDataVencimentoDaAtividadeEstabelecimento;
	private java.lang.Long anoBaseAlvara;
	private java.lang.Long numeroParecerInicial;
	private java.lang.Long validadeAlvaraDataFixa;
	private java.lang.Long validadeAlvaraPeriodo;
	private java.util.Date validadeAlvaraDataVencimento;
	private java.lang.Long iniciarSequencialAlvara;
	private java.lang.String observacaoDestaqueAlvara;
	private java.lang.Long iniciarSequencialAlvaraAnoAnterior;
	private java.lang.Long flagPermiteAlvaraProvisorio;
	private java.lang.Long flagPermiteImpressaoExterna;
	private java.lang.Long tipoAtividadeAlvara;
	private java.lang.Long marcaDaguaAlvara;
	private java.lang.Long exibirGrupoAlvara;
	private java.lang.Long tipoDataCalcAlvara;
	private java.lang.Long revalidacaoAlvaraUsaDataVencimentoDaAtividadeEstabelecimento;
	private java.lang.Long alvaraRevalidacaoUsaDataFixa;
	private java.lang.Long tipoDataCalculoRevalidacaoAlvara;
	private java.lang.Long periodoValidadeRevalidacaoAlvara;
	private java.util.Date dataVencimentoRevalidacaoAlvara;
	private java.util.Date dataInicialRevalidacaoAlvara;
	private java.util.Date dataFinalRevalidacaoAlvara;
	private java.lang.Long anoBaseRevalidacaoAlvara;
	private java.lang.Long qtdAnosVencimentoAlvaraInicial;
	private java.lang.Long autorizacaoSanitariaUsaDataFixa;
	private java.lang.Long tipoDataCalculoAutorizacaoSanitaria;
	private java.lang.Long periodoValidadeAutorizacaoSanitaria;
	private java.util.Date dataVencimentoAutorizacaoSanitaria;
	private java.lang.String observacaoDestaqueAutorizacaoSanitaria;
	private java.lang.Long anoBaseAutorizacaoSanitaria;
	private java.lang.Long validadeTreinamentoDataFixa;
	private java.util.Date validadeTreinamentoDataVencimento;
	private java.lang.Long iniciarSequencialTreinamento;
	private java.lang.Long marcaDaguaTreinamento;
	private java.lang.String credenciamentoPara;
	private java.lang.Long validadeTreinamentoPeriodo;
	private java.lang.Long quantidadePeriodoValidadeTreinamento;
	private java.lang.String orientacaoTreinamento;
	private java.lang.String observacaoDestaqueTreinamento;
	private java.lang.Long tipoDataCalcCredenciamento;
	private java.lang.Long alvaraInicialAnosValidade;
	private java.lang.Long revalidacaoAlvaraAnosValidade;
	private java.lang.Long autorizacaoSanitariaAnosValidade;
	private java.lang.Long credenciamentoTreinamentoAnosValidade;
	private java.lang.Long licencaTransporteAnosValidade;
	private java.lang.Long numTermoAjustamentoConduta;
	private java.lang.String clausulasGeraisTermoAjustamentoConduta;

	// many to one
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioAlteracao;
	private br.com.ksisolucoes.vo.basico.Atividade atividadePadraoUsuarioExterno;
	private br.com.ksisolucoes.vo.basico.Empresa empresaPadraoUsuarioExterno;
	private br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoCabecalhoRelatorio;
	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private br.com.ksisolucoes.vo.vigilancia.SetorVigilancia setorVigilanciaReceita;
	private br.com.ksisolucoes.vo.vigilancia.SetorVigilancia setorVigilanciaDefesaPrevia;
	private br.com.ksisolucoes.vo.vigilancia.SetorVigilancia setorVigilanciaReceitaSecundario;
	private br.com.ksisolucoes.vo.vigilancia.SetorVigilancia setorVigilanciaAnaliseProjetos;
	private br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo brasaoMarcaDagua;
	private br.com.ksisolucoes.vo.vigilancia.SetorVigilancia setorVigilanciaGeralDenuncia;
	private br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoRequerimentoDeclaratorio;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_configuracao_vigilancia"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: iniciar_seq
	 */
	public java.lang.Long getIniciarSequencial () {
		return getPropertyValue(this, iniciarSequencial, PROP_INICIAR_SEQUENCIAL); 
	}

	/**
	 * Set the value related to the column: iniciar_seq
	 * @param iniciarSequencial the iniciar_seq value
	 */
	public void setIniciarSequencial (java.lang.Long iniciarSequencial) {
//        java.lang.Long iniciarSequencialOld = this.iniciarSequencial;
		this.iniciarSequencial = iniciarSequencial;
//        this.getPropertyChangeSupport().firePropertyChange ("iniciarSequencial", iniciarSequencialOld, iniciarSequencial);
	}



	/**
	 * Return the value associated with the column: txt_exumacao_1
	 */
	public java.lang.String getTextoReqExumacao1 () {
		return getPropertyValue(this, textoReqExumacao1, PROP_TEXTO_REQ_EXUMACAO1); 
	}

	/**
	 * Set the value related to the column: txt_exumacao_1
	 * @param textoReqExumacao1 the txt_exumacao_1 value
	 */
	public void setTextoReqExumacao1 (java.lang.String textoReqExumacao1) {
//        java.lang.String textoReqExumacao1Old = this.textoReqExumacao1;
		this.textoReqExumacao1 = textoReqExumacao1;
//        this.getPropertyChangeSupport().firePropertyChange ("textoReqExumacao1", textoReqExumacao1Old, textoReqExumacao1);
	}



	/**
	 * Return the value associated with the column: txt_exumacao_2
	 */
	public java.lang.String getTextoReqExumacao2 () {
		return getPropertyValue(this, textoReqExumacao2, PROP_TEXTO_REQ_EXUMACAO2); 
	}

	/**
	 * Set the value related to the column: txt_exumacao_2
	 * @param textoReqExumacao2 the txt_exumacao_2 value
	 */
	public void setTextoReqExumacao2 (java.lang.String textoReqExumacao2) {
//        java.lang.String textoReqExumacao2Old = this.textoReqExumacao2;
		this.textoReqExumacao2 = textoReqExumacao2;
//        this.getPropertyChangeSupport().firePropertyChange ("textoReqExumacao2", textoReqExumacao2Old, textoReqExumacao2);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_alteracao
	 */
	public java.util.Date getDataAlteracao () {
		return getPropertyValue(this, dataAlteracao, PROP_DATA_ALTERACAO); 
	}

	/**
	 * Set the value related to the column: dt_alteracao
	 * @param dataAlteracao the dt_alteracao value
	 */
	public void setDataAlteracao (java.util.Date dataAlteracao) {
//        java.util.Date dataAlteracaoOld = this.dataAlteracao;
		this.dataAlteracao = dataAlteracao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAlteracao", dataAlteracaoOld, dataAlteracao);
	}



	/**
	 * Return the value associated with the column: iniciar_seq_habitese
	 */
	public java.lang.Long getIniciarSequencialHabitese () {
		return getPropertyValue(this, iniciarSequencialHabitese, PROP_INICIAR_SEQUENCIAL_HABITESE); 
	}

	/**
	 * Set the value related to the column: iniciar_seq_habitese
	 * @param iniciarSequencialHabitese the iniciar_seq_habitese value
	 */
	public void setIniciarSequencialHabitese (java.lang.Long iniciarSequencialHabitese) {
//        java.lang.Long iniciarSequencialHabiteseOld = this.iniciarSequencialHabitese;
		this.iniciarSequencialHabitese = iniciarSequencialHabitese;
//        this.getPropertyChangeSupport().firePropertyChange ("iniciarSequencialHabitese", iniciarSequencialHabiteseOld, iniciarSequencialHabitese);
	}



	/**
	 * Return the value associated with the column: estoque_minimo_receita_a
	 */
	public java.lang.Long getEstoqueMinimoReceitaA () {
		return getPropertyValue(this, estoqueMinimoReceitaA, PROP_ESTOQUE_MINIMO_RECEITA_A); 
	}

	/**
	 * Set the value related to the column: estoque_minimo_receita_a
	 * @param estoqueMinimoReceitaA the estoque_minimo_receita_a value
	 */
	public void setEstoqueMinimoReceitaA (java.lang.Long estoqueMinimoReceitaA) {
//        java.lang.Long estoqueMinimoReceitaAOld = this.estoqueMinimoReceitaA;
		this.estoqueMinimoReceitaA = estoqueMinimoReceitaA;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueMinimoReceitaA", estoqueMinimoReceitaAOld, estoqueMinimoReceitaA);
	}



	/**
	 * Return the value associated with the column: num_ini_receita_b
	 */
	public java.lang.Long getNumeracaoInicialReceitaB () {
		return getPropertyValue(this, numeracaoInicialReceitaB, PROP_NUMERACAO_INICIAL_RECEITA_B); 
	}

	/**
	 * Set the value related to the column: num_ini_receita_b
	 * @param numeracaoInicialReceitaB the num_ini_receita_b value
	 */
	public void setNumeracaoInicialReceitaB (java.lang.Long numeracaoInicialReceitaB) {
//        java.lang.Long numeracaoInicialReceitaBOld = this.numeracaoInicialReceitaB;
		this.numeracaoInicialReceitaB = numeracaoInicialReceitaB;
//        this.getPropertyChangeSupport().firePropertyChange ("numeracaoInicialReceitaB", numeracaoInicialReceitaBOld, numeracaoInicialReceitaB);
	}



	/**
	 * Return the value associated with the column: num_fim_receita_b
	 */
	public java.lang.Long getNumeracaoFinalReceitaB () {
		return getPropertyValue(this, numeracaoFinalReceitaB, PROP_NUMERACAO_FINAL_RECEITA_B); 
	}

	/**
	 * Set the value related to the column: num_fim_receita_b
	 * @param numeracaoFinalReceitaB the num_fim_receita_b value
	 */
	public void setNumeracaoFinalReceitaB (java.lang.Long numeracaoFinalReceitaB) {
//        java.lang.Long numeracaoFinalReceitaBOld = this.numeracaoFinalReceitaB;
		this.numeracaoFinalReceitaB = numeracaoFinalReceitaB;
//        this.getPropertyChangeSupport().firePropertyChange ("numeracaoFinalReceitaB", numeracaoFinalReceitaBOld, numeracaoFinalReceitaB);
	}



	/**
	 * Return the value associated with the column: iniciar_protocolo_receita_b
	 */
	public java.lang.Long getIniciarProtocoloReceitaB () {
		return getPropertyValue(this, iniciarProtocoloReceitaB, PROP_INICIAR_PROTOCOLO_RECEITA_B); 
	}

	/**
	 * Set the value related to the column: iniciar_protocolo_receita_b
	 * @param iniciarProtocoloReceitaB the iniciar_protocolo_receita_b value
	 */
	public void setIniciarProtocoloReceitaB (java.lang.Long iniciarProtocoloReceitaB) {
//        java.lang.Long iniciarProtocoloReceitaBOld = this.iniciarProtocoloReceitaB;
		this.iniciarProtocoloReceitaB = iniciarProtocoloReceitaB;
//        this.getPropertyChangeSupport().firePropertyChange ("iniciarProtocoloReceitaB", iniciarProtocoloReceitaBOld, iniciarProtocoloReceitaB);
	}



	/**
	 * Return the value associated with the column: iniciar_protocolo_receita_a
	 */
	public java.lang.Long getIniciarProtocoloReceitaA () {
		return getPropertyValue(this, iniciarProtocoloReceitaA, PROP_INICIAR_PROTOCOLO_RECEITA_A); 
	}

	/**
	 * Set the value related to the column: iniciar_protocolo_receita_a
	 * @param iniciarProtocoloReceitaA the iniciar_protocolo_receita_a value
	 */
	public void setIniciarProtocoloReceitaA (java.lang.Long iniciarProtocoloReceitaA) {
//        java.lang.Long iniciarProtocoloReceitaAOld = this.iniciarProtocoloReceitaA;
		this.iniciarProtocoloReceitaA = iniciarProtocoloReceitaA;
//        this.getPropertyChangeSupport().firePropertyChange ("iniciarProtocoloReceitaA", iniciarProtocoloReceitaAOld, iniciarProtocoloReceitaA);
	}



	/**
	 * Return the value associated with the column: iniciar_protocolo_receita_b_b2
	 */
	public java.lang.Long getIniciarProtocoloReceitaBB2 () {
		return getPropertyValue(this, iniciarProtocoloReceitaBB2, PROP_INICIAR_PROTOCOLO_RECEITA_B_B2); 
	}

	/**
	 * Set the value related to the column: iniciar_protocolo_receita_b_b2
	 * @param iniciarProtocoloReceitaBB2 the iniciar_protocolo_receita_b_b2 value
	 */
	public void setIniciarProtocoloReceitaBB2 (java.lang.Long iniciarProtocoloReceitaBB2) {
//        java.lang.Long iniciarProtocoloReceitaBB2Old = this.iniciarProtocoloReceitaBB2;
		this.iniciarProtocoloReceitaBB2 = iniciarProtocoloReceitaBB2;
//        this.getPropertyChangeSupport().firePropertyChange ("iniciarProtocoloReceitaBB2", iniciarProtocoloReceitaBB2Old, iniciarProtocoloReceitaBB2);
	}



	/**
	 * Return the value associated with the column: iniciar_protocolo_receita_b_c2
	 */
	public java.lang.Long getIniciarProtocoloReceitaBC2 () {
		return getPropertyValue(this, iniciarProtocoloReceitaBC2, PROP_INICIAR_PROTOCOLO_RECEITA_B_C2); 
	}

	/**
	 * Set the value related to the column: iniciar_protocolo_receita_b_c2
	 * @param iniciarProtocoloReceitaBC2 the iniciar_protocolo_receita_b_c2 value
	 */
	public void setIniciarProtocoloReceitaBC2 (java.lang.Long iniciarProtocoloReceitaBC2) {
//        java.lang.Long iniciarProtocoloReceitaBC2Old = this.iniciarProtocoloReceitaBC2;
		this.iniciarProtocoloReceitaBC2 = iniciarProtocoloReceitaBC2;
//        this.getPropertyChangeSupport().firePropertyChange ("iniciarProtocoloReceitaBC2", iniciarProtocoloReceitaBC2Old, iniciarProtocoloReceitaBC2);
	}



	/**
	 * Return the value associated with the column: linha_1_cabecalho
	 */
	public java.lang.String getLinha1Cabecalho () {
		return getPropertyValue(this, linha1Cabecalho, PROP_LINHA1_CABECALHO); 
	}

	/**
	 * Set the value related to the column: linha_1_cabecalho
	 * @param linha1Cabecalho the linha_1_cabecalho value
	 */
	public void setLinha1Cabecalho (java.lang.String linha1Cabecalho) {
//        java.lang.String linha1CabecalhoOld = this.linha1Cabecalho;
		this.linha1Cabecalho = linha1Cabecalho;
//        this.getPropertyChangeSupport().firePropertyChange ("linha1Cabecalho", linha1CabecalhoOld, linha1Cabecalho);
	}



	/**
	 * Return the value associated with the column: linha_2_cabecalho
	 */
	public java.lang.String getLinha2Cabecalho () {
		return getPropertyValue(this, linha2Cabecalho, PROP_LINHA2_CABECALHO); 
	}

	/**
	 * Set the value related to the column: linha_2_cabecalho
	 * @param linha2Cabecalho the linha_2_cabecalho value
	 */
	public void setLinha2Cabecalho (java.lang.String linha2Cabecalho) {
//        java.lang.String linha2CabecalhoOld = this.linha2Cabecalho;
		this.linha2Cabecalho = linha2Cabecalho;
//        this.getPropertyChangeSupport().firePropertyChange ("linha2Cabecalho", linha2CabecalhoOld, linha2Cabecalho);
	}



	/**
	 * Return the value associated with the column: linha_3_cabecalho
	 */
	public java.lang.String getLinha3Cabecalho () {
		return getPropertyValue(this, linha3Cabecalho, PROP_LINHA3_CABECALHO); 
	}

	/**
	 * Set the value related to the column: linha_3_cabecalho
	 * @param linha3Cabecalho the linha_3_cabecalho value
	 */
	public void setLinha3Cabecalho (java.lang.String linha3Cabecalho) {
//        java.lang.String linha3CabecalhoOld = this.linha3Cabecalho;
		this.linha3Cabecalho = linha3Cabecalho;
//        this.getPropertyChangeSupport().firePropertyChange ("linha3Cabecalho", linha3CabecalhoOld, linha3Cabecalho);
	}



	/**
	 * Return the value associated with the column: validade_email_confirmacao
	 */
	public java.lang.Long getValidadeEmailConfirmacao () {
		return getPropertyValue(this, validadeEmailConfirmacao, PROP_VALIDADE_EMAIL_CONFIRMACAO); 
	}

	/**
	 * Set the value related to the column: validade_email_confirmacao
	 * @param validadeEmailConfirmacao the validade_email_confirmacao value
	 */
	public void setValidadeEmailConfirmacao (java.lang.Long validadeEmailConfirmacao) {
//        java.lang.Long validadeEmailConfirmacaoOld = this.validadeEmailConfirmacao;
		this.validadeEmailConfirmacao = validadeEmailConfirmacao;
//        this.getPropertyChangeSupport().firePropertyChange ("validadeEmailConfirmacao", validadeEmailConfirmacaoOld, validadeEmailConfirmacao);
	}



	/**
	 * Return the value associated with the column: ciencia_auto_infracao
	 */
	public java.lang.String getCienciaAutoInfracao () {
		return getPropertyValue(this, cienciaAutoInfracao, PROP_CIENCIA_AUTO_INFRACAO); 
	}

	/**
	 * Set the value related to the column: ciencia_auto_infracao
	 * @param cienciaAutoInfracao the ciencia_auto_infracao value
	 */
	public void setCienciaAutoInfracao (java.lang.String cienciaAutoInfracao) {
//        java.lang.String cienciaAutoInfracaoOld = this.cienciaAutoInfracao;
		this.cienciaAutoInfracao = cienciaAutoInfracao;
//        this.getPropertyChangeSupport().firePropertyChange ("cienciaAutoInfracao", cienciaAutoInfracaoOld, cienciaAutoInfracao);
	}



	/**
	 * Return the value associated with the column: prazo_defesa_auto_infracao
	 */
	public java.lang.Long getPrazoDefesaAutoInfracao () {
		return getPropertyValue(this, prazoDefesaAutoInfracao, PROP_PRAZO_DEFESA_AUTO_INFRACAO); 
	}

	/**
	 * Set the value related to the column: prazo_defesa_auto_infracao
	 * @param prazoDefesaAutoInfracao the prazo_defesa_auto_infracao value
	 */
	public void setPrazoDefesaAutoInfracao (java.lang.Long prazoDefesaAutoInfracao) {
//        java.lang.Long prazoDefesaAutoInfracaoOld = this.prazoDefesaAutoInfracao;
		this.prazoDefesaAutoInfracao = prazoDefesaAutoInfracao;
//        this.getPropertyChangeSupport().firePropertyChange ("prazoDefesaAutoInfracao", prazoDefesaAutoInfracaoOld, prazoDefesaAutoInfracao);
	}



	/**
	 * Return the value associated with the column: prazo_recurso_auto_penalidade
	 */
	public java.lang.Long getPrazoRecursoAutoPenalidade () {
		return getPropertyValue(this, prazoRecursoAutoPenalidade, PROP_PRAZO_RECURSO_AUTO_PENALIDADE); 
	}

	/**
	 * Set the value related to the column: prazo_recurso_auto_penalidade
	 * @param prazoRecursoAutoPenalidade the prazo_recurso_auto_penalidade value
	 */
	public void setPrazoRecursoAutoPenalidade (java.lang.Long prazoRecursoAutoPenalidade) {
//        java.lang.Long prazoRecursoAutoPenalidadeOld = this.prazoRecursoAutoPenalidade;
		this.prazoRecursoAutoPenalidade = prazoRecursoAutoPenalidade;
//        this.getPropertyChangeSupport().firePropertyChange ("prazoRecursoAutoPenalidade", prazoRecursoAutoPenalidadeOld, prazoRecursoAutoPenalidade);
	}



	/**
	 * Return the value associated with the column: ciencia_auto_intimacao
	 */
	public java.lang.String getCienciaAutoIntimacao () {
		return getPropertyValue(this, cienciaAutoIntimacao, PROP_CIENCIA_AUTO_INTIMACAO); 
	}

	/**
	 * Set the value related to the column: ciencia_auto_intimacao
	 * @param cienciaAutoIntimacao the ciencia_auto_intimacao value
	 */
	public void setCienciaAutoIntimacao (java.lang.String cienciaAutoIntimacao) {
//        java.lang.String cienciaAutoIntimacaoOld = this.cienciaAutoIntimacao;
		this.cienciaAutoIntimacao = cienciaAutoIntimacao;
//        this.getPropertyChangeSupport().firePropertyChange ("cienciaAutoIntimacao", cienciaAutoIntimacaoOld, cienciaAutoIntimacao);
	}



	/**
	 * Return the value associated with the column: ciencia_auto_penalidade
	 */
	public java.lang.String getCienciaAutoPenalidade () {
		return getPropertyValue(this, cienciaAutoPenalidade, PROP_CIENCIA_AUTO_PENALIDADE); 
	}

	/**
	 * Set the value related to the column: ciencia_auto_penalidade
	 * @param cienciaAutoPenalidade the ciencia_auto_penalidade value
	 */
	public void setCienciaAutoPenalidade (java.lang.String cienciaAutoPenalidade) {
//        java.lang.String cienciaAutoPenalidadeOld = this.cienciaAutoPenalidade;
		this.cienciaAutoPenalidade = cienciaAutoPenalidade;
//        this.getPropertyChangeSupport().firePropertyChange ("cienciaAutoPenalidade", cienciaAutoPenalidadeOld, cienciaAutoPenalidade);
	}



	/**
	 * Return the value associated with the column: fundamento_legal_regulamentacao
	 */
	public java.lang.String getFundamentoLegalRegulamentacao () {
		return getPropertyValue(this, fundamentoLegalRegulamentacao, PROP_FUNDAMENTO_LEGAL_REGULAMENTACAO); 
	}

	/**
	 * Set the value related to the column: fundamento_legal_regulamentacao
	 * @param fundamentoLegalRegulamentacao the fundamento_legal_regulamentacao value
	 */
	public void setFundamentoLegalRegulamentacao (java.lang.String fundamentoLegalRegulamentacao) {
//        java.lang.String fundamentoLegalRegulamentacaoOld = this.fundamentoLegalRegulamentacao;
		this.fundamentoLegalRegulamentacao = fundamentoLegalRegulamentacao;
//        this.getPropertyChangeSupport().firePropertyChange ("fundamentoLegalRegulamentacao", fundamentoLegalRegulamentacaoOld, fundamentoLegalRegulamentacao);
	}



	/**
	 * Return the value associated with the column: concedido
	 */
	public java.lang.String getConcedido () {
		return getPropertyValue(this, concedido, PROP_CONCEDIDO); 
	}

	/**
	 * Set the value related to the column: concedido
	 * @param concedido the concedido value
	 */
	public void setConcedido (java.lang.String concedido) {
//        java.lang.String concedidoOld = this.concedido;
		this.concedido = concedido;
//        this.getPropertyChangeSupport().firePropertyChange ("concedido", concedidoOld, concedido);
	}



	/**
	 * Return the value associated with the column: linha_4_cabecalho
	 */
	public java.lang.String getLinha4Cabecalho () {
		return getPropertyValue(this, linha4Cabecalho, PROP_LINHA4_CABECALHO); 
	}

	/**
	 * Set the value related to the column: linha_4_cabecalho
	 * @param linha4Cabecalho the linha_4_cabecalho value
	 */
	public void setLinha4Cabecalho (java.lang.String linha4Cabecalho) {
//        java.lang.String linha4CabecalhoOld = this.linha4Cabecalho;
		this.linha4Cabecalho = linha4Cabecalho;
//        this.getPropertyChangeSupport().firePropertyChange ("linha4Cabecalho", linha4CabecalhoOld, linha4Cabecalho);
	}



	/**
	 * Return the value associated with the column: ano_base_geral
	 */
	public java.lang.Long getAnoBaseGeral () {
		return getPropertyValue(this, anoBaseGeral, PROP_ANO_BASE_GERAL); 
	}

	/**
	 * Set the value related to the column: ano_base_geral
	 * @param anoBaseGeral the ano_base_geral value
	 */
	public void setAnoBaseGeral (java.lang.Long anoBaseGeral) {
//        java.lang.Long anoBaseGeralOld = this.anoBaseGeral;
		this.anoBaseGeral = anoBaseGeral;
//        this.getPropertyChangeSupport().firePropertyChange ("anoBaseGeral", anoBaseGeralOld, anoBaseGeral);
	}



	/**
	 * Return the value associated with the column: ano_base_receita
	 */
	public java.lang.Long getAnoBaseReceita () {
		return getPropertyValue(this, anoBaseReceita, PROP_ANO_BASE_RECEITA); 
	}

	/**
	 * Set the value related to the column: ano_base_receita
	 * @param anoBaseReceita the ano_base_receita value
	 */
	public void setAnoBaseReceita (java.lang.Long anoBaseReceita) {
//        java.lang.Long anoBaseReceitaOld = this.anoBaseReceita;
		this.anoBaseReceita = anoBaseReceita;
//        this.getPropertyChangeSupport().firePropertyChange ("anoBaseReceita", anoBaseReceitaOld, anoBaseReceita);
	}



	/**
	 * Return the value associated with the column: ini_numeracao_baixa
	 */
	public java.lang.Long getIniciarNumeracaoBaixa () {
		return getPropertyValue(this, iniciarNumeracaoBaixa, PROP_INICIAR_NUMERACAO_BAIXA); 
	}

	/**
	 * Set the value related to the column: ini_numeracao_baixa
	 * @param iniciarNumeracaoBaixa the ini_numeracao_baixa value
	 */
	public void setIniciarNumeracaoBaixa (java.lang.Long iniciarNumeracaoBaixa) {
//        java.lang.Long iniciarNumeracaoBaixaOld = this.iniciarNumeracaoBaixa;
		this.iniciarNumeracaoBaixa = iniciarNumeracaoBaixa;
//        this.getPropertyChangeSupport().firePropertyChange ("iniciarNumeracaoBaixa", iniciarNumeracaoBaixaOld, iniciarNumeracaoBaixa);
	}



	/**
	 * Return the value associated with the column: ini_numeracao_nada_consta
	 */
	public java.lang.Long getIniciarNumeracaoNadaConsta () {
		return getPropertyValue(this, iniciarNumeracaoNadaConsta, PROP_INICIAR_NUMERACAO_NADA_CONSTA); 
	}

	/**
	 * Set the value related to the column: ini_numeracao_nada_consta
	 * @param iniciarNumeracaoNadaConsta the ini_numeracao_nada_consta value
	 */
	public void setIniciarNumeracaoNadaConsta (java.lang.Long iniciarNumeracaoNadaConsta) {
//        java.lang.Long iniciarNumeracaoNadaConstaOld = this.iniciarNumeracaoNadaConsta;
		this.iniciarNumeracaoNadaConsta = iniciarNumeracaoNadaConsta;
//        this.getPropertyChangeSupport().firePropertyChange ("iniciarNumeracaoNadaConsta", iniciarNumeracaoNadaConstaOld, iniciarNumeracaoNadaConsta);
	}



	/**
	 * Return the value associated with the column: valor_excedido_analise_proj
	 */
	public java.lang.Double getValorExcedidoAnaliseProjeto () {
		return getPropertyValue(this, valorExcedidoAnaliseProjeto, PROP_VALOR_EXCEDIDO_ANALISE_PROJETO); 
	}

	/**
	 * Set the value related to the column: valor_excedido_analise_proj
	 * @param valorExcedidoAnaliseProjeto the valor_excedido_analise_proj value
	 */
	public void setValorExcedidoAnaliseProjeto (java.lang.Double valorExcedidoAnaliseProjeto) {
//        java.lang.Double valorExcedidoAnaliseProjetoOld = this.valorExcedidoAnaliseProjeto;
		this.valorExcedidoAnaliseProjeto = valorExcedidoAnaliseProjeto;
//        this.getPropertyChangeSupport().firePropertyChange ("valorExcedidoAnaliseProjeto", valorExcedidoAnaliseProjetoOld, valorExcedidoAnaliseProjeto);
	}



	/**
	 * Return the value associated with the column: num_parecer_tecnico
	 */
	public java.lang.Long getNumeracaoParecerTecnico () {
		return getPropertyValue(this, numeracaoParecerTecnico, PROP_NUMERACAO_PARECER_TECNICO); 
	}

	/**
	 * Set the value related to the column: num_parecer_tecnico
	 * @param numeracaoParecerTecnico the num_parecer_tecnico value
	 */
	public void setNumeracaoParecerTecnico (java.lang.Long numeracaoParecerTecnico) {
//        java.lang.Long numeracaoParecerTecnicoOld = this.numeracaoParecerTecnico;
		this.numeracaoParecerTecnico = numeracaoParecerTecnico;
//        this.getPropertyChangeSupport().firePropertyChange ("numeracaoParecerTecnico", numeracaoParecerTecnicoOld, numeracaoParecerTecnico);
	}



	/**
	 * Return the value associated with the column: num_parecer_conformidade_tecnica
	 */
	public java.lang.Long getNumeracaoParecerConformidadeTecnica () {
		return getPropertyValue(this, numeracaoParecerConformidadeTecnica, PROP_NUMERACAO_PARECER_CONFORMIDADE_TECNICA); 
	}

	/**
	 * Set the value related to the column: num_parecer_conformidade_tecnica
	 * @param numeracaoParecerConformidadeTecnica the num_parecer_conformidade_tecnica value
	 */
	public void setNumeracaoParecerConformidadeTecnica (java.lang.Long numeracaoParecerConformidadeTecnica) {
//        java.lang.Long numeracaoParecerConformidadeTecnicaOld = this.numeracaoParecerConformidadeTecnica;
		this.numeracaoParecerConformidadeTecnica = numeracaoParecerConformidadeTecnica;
//        this.getPropertyChangeSupport().firePropertyChange ("numeracaoParecerConformidadeTecnica", numeracaoParecerConformidadeTecnicaOld, numeracaoParecerConformidadeTecnica);
	}



	/**
	 * Return the value associated with the column: dias_sem_rt
	 */
	public java.lang.Long getDiasSemResponsavelTecnico () {
		return getPropertyValue(this, diasSemResponsavelTecnico, PROP_DIAS_SEM_RESPONSAVEL_TECNICO); 
	}

	/**
	 * Set the value related to the column: dias_sem_rt
	 * @param diasSemResponsavelTecnico the dias_sem_rt value
	 */
	public void setDiasSemResponsavelTecnico (java.lang.Long diasSemResponsavelTecnico) {
//        java.lang.Long diasSemResponsavelTecnicoOld = this.diasSemResponsavelTecnico;
		this.diasSemResponsavelTecnico = diasSemResponsavelTecnico;
//        this.getPropertyChangeSupport().firePropertyChange ("diasSemResponsavelTecnico", diasSemResponsavelTecnicoOld, diasSemResponsavelTecnico);
	}



	/**
	 * Return the value associated with the column: obs_vacinacao_extramuro_1
	 */
	public java.lang.String getObservacaoVacinacaoExtramuro1 () {
		return getPropertyValue(this, observacaoVacinacaoExtramuro1, PROP_OBSERVACAO_VACINACAO_EXTRAMURO1); 
	}

	/**
	 * Set the value related to the column: obs_vacinacao_extramuro_1
	 * @param observacaoVacinacaoExtramuro1 the obs_vacinacao_extramuro_1 value
	 */
	public void setObservacaoVacinacaoExtramuro1 (java.lang.String observacaoVacinacaoExtramuro1) {
//        java.lang.String observacaoVacinacaoExtramuro1Old = this.observacaoVacinacaoExtramuro1;
		this.observacaoVacinacaoExtramuro1 = observacaoVacinacaoExtramuro1;
//        this.getPropertyChangeSupport().firePropertyChange ("observacaoVacinacaoExtramuro1", observacaoVacinacaoExtramuro1Old, observacaoVacinacaoExtramuro1);
	}



	/**
	 * Return the value associated with the column: obs_vacinacao_extramuro_2
	 */
	public java.lang.String getObservacaoVacinacaoExtramuro2 () {
		return getPropertyValue(this, observacaoVacinacaoExtramuro2, PROP_OBSERVACAO_VACINACAO_EXTRAMURO2); 
	}

	/**
	 * Set the value related to the column: obs_vacinacao_extramuro_2
	 * @param observacaoVacinacaoExtramuro2 the obs_vacinacao_extramuro_2 value
	 */
	public void setObservacaoVacinacaoExtramuro2 (java.lang.String observacaoVacinacaoExtramuro2) {
//        java.lang.String observacaoVacinacaoExtramuro2Old = this.observacaoVacinacaoExtramuro2;
		this.observacaoVacinacaoExtramuro2 = observacaoVacinacaoExtramuro2;
//        this.getPropertyChangeSupport().firePropertyChange ("observacaoVacinacaoExtramuro2", observacaoVacinacaoExtramuro2Old, observacaoVacinacaoExtramuro2);
	}



	/**
	 * Return the value associated with the column: txt_dimensionamento
	 */
	public java.lang.String getTextoDimensionamento () {
		return getPropertyValue(this, textoDimensionamento, PROP_TEXTO_DIMENSIONAMENTO); 
	}

	/**
	 * Set the value related to the column: txt_dimensionamento
	 * @param textoDimensionamento the txt_dimensionamento value
	 */
	public void setTextoDimensionamento (java.lang.String textoDimensionamento) {
//        java.lang.String textoDimensionamentoOld = this.textoDimensionamento;
		this.textoDimensionamento = textoDimensionamento;
//        this.getPropertyChangeSupport().firePropertyChange ("textoDimensionamento", textoDimensionamentoOld, textoDimensionamento);
	}



	/**
	 * Return the value associated with the column: txt_funcionalidade_edificacao
	 */
	public java.lang.String getTextoFuncionalidadeEdificacao () {
		return getPropertyValue(this, textoFuncionalidadeEdificacao, PROP_TEXTO_FUNCIONALIDADE_EDIFICACAO); 
	}

	/**
	 * Set the value related to the column: txt_funcionalidade_edificacao
	 * @param textoFuncionalidadeEdificacao the txt_funcionalidade_edificacao value
	 */
	public void setTextoFuncionalidadeEdificacao (java.lang.String textoFuncionalidadeEdificacao) {
//        java.lang.String textoFuncionalidadeEdificacaoOld = this.textoFuncionalidadeEdificacao;
		this.textoFuncionalidadeEdificacao = textoFuncionalidadeEdificacao;
//        this.getPropertyChangeSupport().firePropertyChange ("textoFuncionalidadeEdificacao", textoFuncionalidadeEdificacaoOld, textoFuncionalidadeEdificacao);
	}



	/**
	 * Return the value associated with the column: txt_instalacoes_ordinarias
	 */
	public java.lang.String getTextoInstalacoesOrdinarias () {
		return getPropertyValue(this, textoInstalacoesOrdinarias, PROP_TEXTO_INSTALACOES_ORDINARIAS); 
	}

	/**
	 * Set the value related to the column: txt_instalacoes_ordinarias
	 * @param textoInstalacoesOrdinarias the txt_instalacoes_ordinarias value
	 */
	public void setTextoInstalacoesOrdinarias (java.lang.String textoInstalacoesOrdinarias) {
//        java.lang.String textoInstalacoesOrdinariasOld = this.textoInstalacoesOrdinarias;
		this.textoInstalacoesOrdinarias = textoInstalacoesOrdinarias;
//        this.getPropertyChangeSupport().firePropertyChange ("textoInstalacoesOrdinarias", textoInstalacoesOrdinariasOld, textoInstalacoesOrdinarias);
	}



	/**
	 * Return the value associated with the column: txt_recomendacoes
	 */
	public java.lang.String getTextoRecomendacoes () {
		return getPropertyValue(this, textoRecomendacoes, PROP_TEXTO_RECOMENDACOES); 
	}

	/**
	 * Set the value related to the column: txt_recomendacoes
	 * @param textoRecomendacoes the txt_recomendacoes value
	 */
	public void setTextoRecomendacoes (java.lang.String textoRecomendacoes) {
//        java.lang.String textoRecomendacoesOld = this.textoRecomendacoes;
		this.textoRecomendacoes = textoRecomendacoes;
//        this.getPropertyChangeSupport().firePropertyChange ("textoRecomendacoes", textoRecomendacoesOld, textoRecomendacoes);
	}



	/**
	 * Return the value associated with the column: flag_linha_assinatura
	 */
	public java.lang.Long getFlagLinhaAssinatura () {
		return getPropertyValue(this, flagLinhaAssinatura, PROP_FLAG_LINHA_ASSINATURA); 
	}

	/**
	 * Set the value related to the column: flag_linha_assinatura
	 * @param flagLinhaAssinatura the flag_linha_assinatura value
	 */
	public void setFlagLinhaAssinatura (java.lang.Long flagLinhaAssinatura) {
//        java.lang.Long flagLinhaAssinaturaOld = this.flagLinhaAssinatura;
		this.flagLinhaAssinatura = flagLinhaAssinatura;
//        this.getPropertyChangeSupport().firePropertyChange ("flagLinhaAssinatura", flagLinhaAssinaturaOld, flagLinhaAssinatura);
	}



	/**
	 * Return the value associated with the column: flag_emitir_termo_solic_servico
	 */
	public java.lang.Long getFlagEmitirTermoSolicitacaoServico () {
		return getPropertyValue(this, flagEmitirTermoSolicitacaoServico, PROP_FLAG_EMITIR_TERMO_SOLICITACAO_SERVICO); 
	}

	/**
	 * Set the value related to the column: flag_emitir_termo_solic_servico
	 * @param flagEmitirTermoSolicitacaoServico the flag_emitir_termo_solic_servico value
	 */
	public void setFlagEmitirTermoSolicitacaoServico (java.lang.Long flagEmitirTermoSolicitacaoServico) {
//        java.lang.Long flagEmitirTermoSolicitacaoServicoOld = this.flagEmitirTermoSolicitacaoServico;
		this.flagEmitirTermoSolicitacaoServico = flagEmitirTermoSolicitacaoServico;
//        this.getPropertyChangeSupport().firePropertyChange ("flagEmitirTermoSolicitacaoServico", flagEmitirTermoSolicitacaoServicoOld, flagEmitirTermoSolicitacaoServico);
	}



	/**
	 * Return the value associated with the column: flag_obriga_dados_estab_pess
	 */
	public java.lang.Long getFlagObrigaDadosEstabelecimento () {
		return getPropertyValue(this, flagObrigaDadosEstabelecimento, PROP_FLAG_OBRIGA_DADOS_ESTABELECIMENTO); 
	}

	/**
	 * Set the value related to the column: flag_obriga_dados_estab_pess
	 * @param flagObrigaDadosEstabelecimento the flag_obriga_dados_estab_pess value
	 */
	public void setFlagObrigaDadosEstabelecimento (java.lang.Long flagObrigaDadosEstabelecimento) {
//        java.lang.Long flagObrigaDadosEstabelecimentoOld = this.flagObrigaDadosEstabelecimento;
		this.flagObrigaDadosEstabelecimento = flagObrigaDadosEstabelecimento;
//        this.getPropertyChangeSupport().firePropertyChange ("flagObrigaDadosEstabelecimento", flagObrigaDadosEstabelecimentoOld, flagObrigaDadosEstabelecimento);
	}



	/**
	 * Return the value associated with the column: tp_calc_prazo_exigencia
	 */
	public java.lang.Long getTipoCalculoPrazoExigencia () {
		return getPropertyValue(this, tipoCalculoPrazoExigencia, PROP_TIPO_CALCULO_PRAZO_EXIGENCIA); 
	}

	/**
	 * Set the value related to the column: tp_calc_prazo_exigencia
	 * @param tipoCalculoPrazoExigencia the tp_calc_prazo_exigencia value
	 */
	public void setTipoCalculoPrazoExigencia (java.lang.Long tipoCalculoPrazoExigencia) {
//        java.lang.Long tipoCalculoPrazoExigenciaOld = this.tipoCalculoPrazoExigencia;
		this.tipoCalculoPrazoExigencia = tipoCalculoPrazoExigencia;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoCalculoPrazoExigencia", tipoCalculoPrazoExigenciaOld, tipoCalculoPrazoExigencia);
	}



	/**
	 * Return the value associated with the column: tp_database_calc_prazo_exigencia
	 */
	public java.lang.Long getTipoDatabaseCalculoPrazoExigencia () {
		return getPropertyValue(this, tipoDatabaseCalculoPrazoExigencia, PROP_TIPO_DATABASE_CALCULO_PRAZO_EXIGENCIA); 
	}

	/**
	 * Set the value related to the column: tp_database_calc_prazo_exigencia
	 * @param tipoDatabaseCalculoPrazoExigencia the tp_database_calc_prazo_exigencia value
	 */
	public void setTipoDatabaseCalculoPrazoExigencia (java.lang.Long tipoDatabaseCalculoPrazoExigencia) {
//        java.lang.Long tipoDatabaseCalculoPrazoExigenciaOld = this.tipoDatabaseCalculoPrazoExigencia;
		this.tipoDatabaseCalculoPrazoExigencia = tipoDatabaseCalculoPrazoExigencia;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDatabaseCalculoPrazoExigencia", tipoDatabaseCalculoPrazoExigenciaOld, tipoDatabaseCalculoPrazoExigencia);
	}



	/**
	 * Return the value associated with the column: tp_calc_prazo_defesa
	 */
	public java.lang.Long getTipoCalculoPrazoDefesa () {
		return getPropertyValue(this, tipoCalculoPrazoDefesa, PROP_TIPO_CALCULO_PRAZO_DEFESA); 
	}

	/**
	 * Set the value related to the column: tp_calc_prazo_defesa
	 * @param tipoCalculoPrazoDefesa the tp_calc_prazo_defesa value
	 */
	public void setTipoCalculoPrazoDefesa (java.lang.Long tipoCalculoPrazoDefesa) {
//        java.lang.Long tipoCalculoPrazoDefesaOld = this.tipoCalculoPrazoDefesa;
		this.tipoCalculoPrazoDefesa = tipoCalculoPrazoDefesa;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoCalculoPrazoDefesa", tipoCalculoPrazoDefesaOld, tipoCalculoPrazoDefesa);
	}



	/**
	 * Return the value associated with the column: tp_database_calc_prazo_defesa
	 */
	public java.lang.Long getTipoDatabaseCalculoPrazoDefesa () {
		return getPropertyValue(this, tipoDatabaseCalculoPrazoDefesa, PROP_TIPO_DATABASE_CALCULO_PRAZO_DEFESA); 
	}

	/**
	 * Set the value related to the column: tp_database_calc_prazo_defesa
	 * @param tipoDatabaseCalculoPrazoDefesa the tp_database_calc_prazo_defesa value
	 */
	public void setTipoDatabaseCalculoPrazoDefesa (java.lang.Long tipoDatabaseCalculoPrazoDefesa) {
//        java.lang.Long tipoDatabaseCalculoPrazoDefesaOld = this.tipoDatabaseCalculoPrazoDefesa;
		this.tipoDatabaseCalculoPrazoDefesa = tipoDatabaseCalculoPrazoDefesa;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDatabaseCalculoPrazoDefesa", tipoDatabaseCalculoPrazoDefesaOld, tipoDatabaseCalculoPrazoDefesa);
	}



	/**
	 * Return the value associated with the column: tp_calc_prazo_recurso
	 */
	public java.lang.Long getTipoCalculoPrazoRecurso () {
		return getPropertyValue(this, tipoCalculoPrazoRecurso, PROP_TIPO_CALCULO_PRAZO_RECURSO); 
	}

	/**
	 * Set the value related to the column: tp_calc_prazo_recurso
	 * @param tipoCalculoPrazoRecurso the tp_calc_prazo_recurso value
	 */
	public void setTipoCalculoPrazoRecurso (java.lang.Long tipoCalculoPrazoRecurso) {
//        java.lang.Long tipoCalculoPrazoRecursoOld = this.tipoCalculoPrazoRecurso;
		this.tipoCalculoPrazoRecurso = tipoCalculoPrazoRecurso;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoCalculoPrazoRecurso", tipoCalculoPrazoRecursoOld, tipoCalculoPrazoRecurso);
	}



	/**
	 * Return the value associated with the column: tp_database_calc_prazo_recurso
	 */
	public java.lang.Long getTipoDatabaseCalculoPrazoRecurso () {
		return getPropertyValue(this, tipoDatabaseCalculoPrazoRecurso, PROP_TIPO_DATABASE_CALCULO_PRAZO_RECURSO); 
	}

	/**
	 * Set the value related to the column: tp_database_calc_prazo_recurso
	 * @param tipoDatabaseCalculoPrazoRecurso the tp_database_calc_prazo_recurso value
	 */
	public void setTipoDatabaseCalculoPrazoRecurso (java.lang.Long tipoDatabaseCalculoPrazoRecurso) {
//        java.lang.Long tipoDatabaseCalculoPrazoRecursoOld = this.tipoDatabaseCalculoPrazoRecurso;
		this.tipoDatabaseCalculoPrazoRecurso = tipoDatabaseCalculoPrazoRecurso;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDatabaseCalculoPrazoRecurso", tipoDatabaseCalculoPrazoRecursoOld, tipoDatabaseCalculoPrazoRecurso);
	}



	/**
	 * Return the value associated with the column: txt_aprovado
	 */
	public java.lang.String getTextoAprovado () {
		return getPropertyValue(this, textoAprovado, PROP_TEXTO_APROVADO); 
	}

	/**
	 * Set the value related to the column: txt_aprovado
	 * @param textoAprovado the txt_aprovado value
	 */
	public void setTextoAprovado (java.lang.String textoAprovado) {
//        java.lang.String textoAprovadoOld = this.textoAprovado;
		this.textoAprovado = textoAprovado;
//        this.getPropertyChangeSupport().firePropertyChange ("textoAprovado", textoAprovadoOld, textoAprovado);
	}



	/**
	 * Return the value associated with the column: txt_reprovado
	 */
	public java.lang.String getTextoReprovado () {
		return getPropertyValue(this, textoReprovado, PROP_TEXTO_REPROVADO); 
	}

	/**
	 * Set the value related to the column: txt_reprovado
	 * @param textoReprovado the txt_reprovado value
	 */
	public void setTextoReprovado (java.lang.String textoReprovado) {
//        java.lang.String textoReprovadoOld = this.textoReprovado;
		this.textoReprovado = textoReprovado;
//        this.getPropertyChangeSupport().firePropertyChange ("textoReprovado", textoReprovadoOld, textoReprovado);
	}



	/**
	 * Return the value associated with the column: obrigatoriedade_refrigerado
	 */
	public java.lang.Long getObrigatoridadeRefrigerado () {
		return getPropertyValue(this, obrigatoridadeRefrigerado, PROP_OBRIGATORIDADE_REFRIGERADO); 
	}

	/**
	 * Set the value related to the column: obrigatoriedade_refrigerado
	 * @param obrigatoridadeRefrigerado the obrigatoriedade_refrigerado value
	 */
	public void setObrigatoridadeRefrigerado (java.lang.Long obrigatoridadeRefrigerado) {
//        java.lang.Long obrigatoridadeRefrigeradoOld = this.obrigatoridadeRefrigerado;
		this.obrigatoridadeRefrigerado = obrigatoridadeRefrigerado;
//        this.getPropertyChangeSupport().firePropertyChange ("obrigatoridadeRefrigerado", obrigatoridadeRefrigeradoOld, obrigatoridadeRefrigerado);
	}



	/**
	 * Return the value associated with the column: num_relatorio_inspecao
	 */
	public java.lang.Long getNumRelatorioInspecao () {
		return getPropertyValue(this, numRelatorioInspecao, PROP_NUM_RELATORIO_INSPECAO); 
	}

	/**
	 * Set the value related to the column: num_relatorio_inspecao
	 * @param numRelatorioInspecao the num_relatorio_inspecao value
	 */
	public void setNumRelatorioInspecao (java.lang.Long numRelatorioInspecao) {
//        java.lang.Long numRelatorioInspecaoOld = this.numRelatorioInspecao;
		this.numRelatorioInspecao = numRelatorioInspecao;
//        this.getPropertyChangeSupport().firePropertyChange ("numRelatorioInspecao", numRelatorioInspecaoOld, numRelatorioInspecao);
	}



	/**
	 * Return the value associated with the column: num_auto_intimacao
	 */
	public java.lang.Long getNumAutoIntimacao () {
		return getPropertyValue(this, numAutoIntimacao, PROP_NUM_AUTO_INTIMACAO); 
	}

	/**
	 * Set the value related to the column: num_auto_intimacao
	 * @param numAutoIntimacao the num_auto_intimacao value
	 */
	public void setNumAutoIntimacao (java.lang.Long numAutoIntimacao) {
//        java.lang.Long numAutoIntimacaoOld = this.numAutoIntimacao;
		this.numAutoIntimacao = numAutoIntimacao;
//        this.getPropertyChangeSupport().firePropertyChange ("numAutoIntimacao", numAutoIntimacaoOld, numAutoIntimacao);
	}



	/**
	 * Return the value associated with the column: num_auto_penalidade
	 */
	public java.lang.Long getNumAutoPenalidade () {
		return getPropertyValue(this, numAutoPenalidade, PROP_NUM_AUTO_PENALIDADE); 
	}

	/**
	 * Set the value related to the column: num_auto_penalidade
	 * @param numAutoPenalidade the num_auto_penalidade value
	 */
	public void setNumAutoPenalidade (java.lang.Long numAutoPenalidade) {
//        java.lang.Long numAutoPenalidadeOld = this.numAutoPenalidade;
		this.numAutoPenalidade = numAutoPenalidade;
//        this.getPropertyChangeSupport().firePropertyChange ("numAutoPenalidade", numAutoPenalidadeOld, numAutoPenalidade);
	}



	/**
	 * Return the value associated with the column: num_auto_infracao
	 */
	public java.lang.Long getNumAutoInfracao () {
		return getPropertyValue(this, numAutoInfracao, PROP_NUM_AUTO_INFRACAO); 
	}

	/**
	 * Set the value related to the column: num_auto_infracao
	 * @param numAutoInfracao the num_auto_infracao value
	 */
	public void setNumAutoInfracao (java.lang.Long numAutoInfracao) {
//        java.lang.Long numAutoInfracaoOld = this.numAutoInfracao;
		this.numAutoInfracao = numAutoInfracao;
//        this.getPropertyChangeSupport().firePropertyChange ("numAutoInfracao", numAutoInfracaoOld, numAutoInfracao);
	}



	/**
	 * Return the value associated with the column: ds_enquad_legal_intimacao
	 */
	public java.lang.String getEnquadramentoLegalIntimacao () {
		return getPropertyValue(this, enquadramentoLegalIntimacao, PROP_ENQUADRAMENTO_LEGAL_INTIMACAO); 
	}

	/**
	 * Set the value related to the column: ds_enquad_legal_intimacao
	 * @param enquadramentoLegalIntimacao the ds_enquad_legal_intimacao value
	 */
	public void setEnquadramentoLegalIntimacao (java.lang.String enquadramentoLegalIntimacao) {
//        java.lang.String enquadramentoLegalIntimacaoOld = this.enquadramentoLegalIntimacao;
		this.enquadramentoLegalIntimacao = enquadramentoLegalIntimacao;
//        this.getPropertyChangeSupport().firePropertyChange ("enquadramentoLegalIntimacao", enquadramentoLegalIntimacaoOld, enquadramentoLegalIntimacao);
	}



	/**
	 * Return the value associated with the column: ds_enquad_legal_infracao
	 */
	public java.lang.String getEnquadramentoLegalInfracao () {
		return getPropertyValue(this, enquadramentoLegalInfracao, PROP_ENQUADRAMENTO_LEGAL_INFRACAO); 
	}

	/**
	 * Set the value related to the column: ds_enquad_legal_infracao
	 * @param enquadramentoLegalInfracao the ds_enquad_legal_infracao value
	 */
	public void setEnquadramentoLegalInfracao (java.lang.String enquadramentoLegalInfracao) {
//        java.lang.String enquadramentoLegalInfracaoOld = this.enquadramentoLegalInfracao;
		this.enquadramentoLegalInfracao = enquadramentoLegalInfracao;
//        this.getPropertyChangeSupport().firePropertyChange ("enquadramentoLegalInfracao", enquadramentoLegalInfracaoOld, enquadramentoLegalInfracao);
	}



	/**
	 * Return the value associated with the column: num_processo_administrativo
	 */
	public java.lang.Long getNumProcessoAdministrativo () {
		return getPropertyValue(this, numProcessoAdministrativo, PROP_NUM_PROCESSO_ADMINISTRATIVO); 
	}

	/**
	 * Set the value related to the column: num_processo_administrativo
	 * @param numProcessoAdministrativo the num_processo_administrativo value
	 */
	public void setNumProcessoAdministrativo (java.lang.Long numProcessoAdministrativo) {
//        java.lang.Long numProcessoAdministrativoOld = this.numProcessoAdministrativo;
		this.numProcessoAdministrativo = numProcessoAdministrativo;
//        this.getPropertyChangeSupport().firePropertyChange ("numProcessoAdministrativo", numProcessoAdministrativoOld, numProcessoAdministrativo);
	}



	/**
	 * Return the value associated with the column: ds_enquad_legal_multa
	 */
	public java.lang.String getEnquadramentoLegalAutoMulta () {
		return getPropertyValue(this, enquadramentoLegalAutoMulta, PROP_ENQUADRAMENTO_LEGAL_AUTO_MULTA); 
	}

	/**
	 * Set the value related to the column: ds_enquad_legal_multa
	 * @param enquadramentoLegalAutoMulta the ds_enquad_legal_multa value
	 */
	public void setEnquadramentoLegalAutoMulta (java.lang.String enquadramentoLegalAutoMulta) {
//        java.lang.String enquadramentoLegalAutoMultaOld = this.enquadramentoLegalAutoMulta;
		this.enquadramentoLegalAutoMulta = enquadramentoLegalAutoMulta;
//        this.getPropertyChangeSupport().firePropertyChange ("enquadramentoLegalAutoMulta", enquadramentoLegalAutoMultaOld, enquadramentoLegalAutoMulta);
	}



	/**
	 * Return the value associated with the column: ciencia_auto_multa
	 */
	public java.lang.String getCienciaAutoMulta () {
		return getPropertyValue(this, cienciaAutoMulta, PROP_CIENCIA_AUTO_MULTA); 
	}

	/**
	 * Set the value related to the column: ciencia_auto_multa
	 * @param cienciaAutoMulta the ciencia_auto_multa value
	 */
	public void setCienciaAutoMulta (java.lang.String cienciaAutoMulta) {
//        java.lang.String cienciaAutoMultaOld = this.cienciaAutoMulta;
		this.cienciaAutoMulta = cienciaAutoMulta;
//        this.getPropertyChangeSupport().firePropertyChange ("cienciaAutoMulta", cienciaAutoMultaOld, cienciaAutoMulta);
	}



	/**
	 * Return the value associated with the column: ds_auto_multa
	 */
	public java.lang.String getDescricaoAutoMulta () {
		return getPropertyValue(this, descricaoAutoMulta, PROP_DESCRICAO_AUTO_MULTA); 
	}

	/**
	 * Set the value related to the column: ds_auto_multa
	 * @param descricaoAutoMulta the ds_auto_multa value
	 */
	public void setDescricaoAutoMulta (java.lang.String descricaoAutoMulta) {
//        java.lang.String descricaoAutoMultaOld = this.descricaoAutoMulta;
		this.descricaoAutoMulta = descricaoAutoMulta;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoAutoMulta", descricaoAutoMultaOld, descricaoAutoMulta);
	}



	/**
	 * Return the value associated with the column: num_auto_multa
	 */
	public java.lang.Long getNumAutoMulta () {
		return getPropertyValue(this, numAutoMulta, PROP_NUM_AUTO_MULTA); 
	}

	/**
	 * Set the value related to the column: num_auto_multa
	 * @param numAutoMulta the num_auto_multa value
	 */
	public void setNumAutoMulta (java.lang.Long numAutoMulta) {
//        java.lang.Long numAutoMultaOld = this.numAutoMulta;
		this.numAutoMulta = numAutoMulta;
//        this.getPropertyChangeSupport().firePropertyChange ("numAutoMulta", numAutoMultaOld, numAutoMulta);
	}



	/**
	 * Return the value associated with the column: prazo_auto_multa
	 */
	public java.lang.Long getPrazoAutoMulta () {
		return getPropertyValue(this, prazoAutoMulta, PROP_PRAZO_AUTO_MULTA); 
	}

	/**
	 * Set the value related to the column: prazo_auto_multa
	 * @param prazoAutoMulta the prazo_auto_multa value
	 */
	public void setPrazoAutoMulta (java.lang.Long prazoAutoMulta) {
//        java.lang.Long prazoAutoMultaOld = this.prazoAutoMulta;
		this.prazoAutoMulta = prazoAutoMulta;
//        this.getPropertyChangeSupport().firePropertyChange ("prazoAutoMulta", prazoAutoMultaOld, prazoAutoMulta);
	}



	/**
	 * Return the value associated with the column: tp_calc_prazo_multa
	 */
	public java.lang.Long getTipoCalculoPrazoAutoMulta () {
		return getPropertyValue(this, tipoCalculoPrazoAutoMulta, PROP_TIPO_CALCULO_PRAZO_AUTO_MULTA); 
	}

	/**
	 * Set the value related to the column: tp_calc_prazo_multa
	 * @param tipoCalculoPrazoAutoMulta the tp_calc_prazo_multa value
	 */
	public void setTipoCalculoPrazoAutoMulta (java.lang.Long tipoCalculoPrazoAutoMulta) {
//        java.lang.Long tipoCalculoPrazoAutoMultaOld = this.tipoCalculoPrazoAutoMulta;
		this.tipoCalculoPrazoAutoMulta = tipoCalculoPrazoAutoMulta;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoCalculoPrazoAutoMulta", tipoCalculoPrazoAutoMultaOld, tipoCalculoPrazoAutoMulta);
	}



	/**
	 * Return the value associated with the column: tp_database_calc_prazo_multa
	 */
	public java.lang.Long getTipoDatabaseCalculoPrazoAutoMulta () {
		return getPropertyValue(this, tipoDatabaseCalculoPrazoAutoMulta, PROP_TIPO_DATABASE_CALCULO_PRAZO_AUTO_MULTA); 
	}

	/**
	 * Set the value related to the column: tp_database_calc_prazo_multa
	 * @param tipoDatabaseCalculoPrazoAutoMulta the tp_database_calc_prazo_multa value
	 */
	public void setTipoDatabaseCalculoPrazoAutoMulta (java.lang.Long tipoDatabaseCalculoPrazoAutoMulta) {
//        java.lang.Long tipoDatabaseCalculoPrazoAutoMultaOld = this.tipoDatabaseCalculoPrazoAutoMulta;
		this.tipoDatabaseCalculoPrazoAutoMulta = tipoDatabaseCalculoPrazoAutoMulta;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDatabaseCalculoPrazoAutoMulta", tipoDatabaseCalculoPrazoAutoMultaOld, tipoDatabaseCalculoPrazoAutoMulta);
	}



	/**
	 * Return the value associated with the column: inicio_prox_dia_util_multa
	 */
	public java.lang.Long getInicioProximoDiaUtilAutoMulta () {
		return getPropertyValue(this, inicioProximoDiaUtilAutoMulta, PROP_INICIO_PROXIMO_DIA_UTIL_AUTO_MULTA); 
	}

	/**
	 * Set the value related to the column: inicio_prox_dia_util_multa
	 * @param inicioProximoDiaUtilAutoMulta the inicio_prox_dia_util_multa value
	 */
	public void setInicioProximoDiaUtilAutoMulta (java.lang.Long inicioProximoDiaUtilAutoMulta) {
//        java.lang.Long inicioProximoDiaUtilAutoMultaOld = this.inicioProximoDiaUtilAutoMulta;
		this.inicioProximoDiaUtilAutoMulta = inicioProximoDiaUtilAutoMulta;
//        this.getPropertyChangeSupport().firePropertyChange ("inicioProximoDiaUtilAutoMulta", inicioProximoDiaUtilAutoMultaOld, inicioProximoDiaUtilAutoMulta);
	}



	/**
	 * Return the value associated with the column: inicio_prox_dia_util_penalidade
	 */
	public java.lang.Long getInicioProximoDiaUtilAutoPenalidade () {
		return getPropertyValue(this, inicioProximoDiaUtilAutoPenalidade, PROP_INICIO_PROXIMO_DIA_UTIL_AUTO_PENALIDADE); 
	}

	/**
	 * Set the value related to the column: inicio_prox_dia_util_penalidade
	 * @param inicioProximoDiaUtilAutoPenalidade the inicio_prox_dia_util_penalidade value
	 */
	public void setInicioProximoDiaUtilAutoPenalidade (java.lang.Long inicioProximoDiaUtilAutoPenalidade) {
//        java.lang.Long inicioProximoDiaUtilAutoPenalidadeOld = this.inicioProximoDiaUtilAutoPenalidade;
		this.inicioProximoDiaUtilAutoPenalidade = inicioProximoDiaUtilAutoPenalidade;
//        this.getPropertyChangeSupport().firePropertyChange ("inicioProximoDiaUtilAutoPenalidade", inicioProximoDiaUtilAutoPenalidadeOld, inicioProximoDiaUtilAutoPenalidade);
	}



	/**
	 * Return the value associated with the column: inicio_prox_dia_util_infracao
	 */
	public java.lang.Long getInicioProximoDiaUtilAutoInfracao () {
		return getPropertyValue(this, inicioProximoDiaUtilAutoInfracao, PROP_INICIO_PROXIMO_DIA_UTIL_AUTO_INFRACAO); 
	}

	/**
	 * Set the value related to the column: inicio_prox_dia_util_infracao
	 * @param inicioProximoDiaUtilAutoInfracao the inicio_prox_dia_util_infracao value
	 */
	public void setInicioProximoDiaUtilAutoInfracao (java.lang.Long inicioProximoDiaUtilAutoInfracao) {
//        java.lang.Long inicioProximoDiaUtilAutoInfracaoOld = this.inicioProximoDiaUtilAutoInfracao;
		this.inicioProximoDiaUtilAutoInfracao = inicioProximoDiaUtilAutoInfracao;
//        this.getPropertyChangeSupport().firePropertyChange ("inicioProximoDiaUtilAutoInfracao", inicioProximoDiaUtilAutoInfracaoOld, inicioProximoDiaUtilAutoInfracao);
	}



	/**
	 * Return the value associated with the column: inicio_prox_dia_util_intimacao
	 */
	public java.lang.Long getInicioProximoDiaUtilAutoIntimacao () {
		return getPropertyValue(this, inicioProximoDiaUtilAutoIntimacao, PROP_INICIO_PROXIMO_DIA_UTIL_AUTO_INTIMACAO); 
	}

	/**
	 * Set the value related to the column: inicio_prox_dia_util_intimacao
	 * @param inicioProximoDiaUtilAutoIntimacao the inicio_prox_dia_util_intimacao value
	 */
	public void setInicioProximoDiaUtilAutoIntimacao (java.lang.Long inicioProximoDiaUtilAutoIntimacao) {
//        java.lang.Long inicioProximoDiaUtilAutoIntimacaoOld = this.inicioProximoDiaUtilAutoIntimacao;
		this.inicioProximoDiaUtilAutoIntimacao = inicioProximoDiaUtilAutoIntimacao;
//        this.getPropertyChangeSupport().firePropertyChange ("inicioProximoDiaUtilAutoIntimacao", inicioProximoDiaUtilAutoIntimacaoOld, inicioProximoDiaUtilAutoIntimacao);
	}



	/**
	 * Return the value associated with the column: obriga_rg_dt_emissao_rt
	 */
	public java.lang.Long getObrigaRgDtEmissaoResponsavelTecnico () {
		return getPropertyValue(this, obrigaRgDtEmissaoResponsavelTecnico, PROP_OBRIGA_RG_DT_EMISSAO_RESPONSAVEL_TECNICO); 
	}

	/**
	 * Set the value related to the column: obriga_rg_dt_emissao_rt
	 * @param obrigaRgDtEmissaoResponsavelTecnico the obriga_rg_dt_emissao_rt value
	 */
	public void setObrigaRgDtEmissaoResponsavelTecnico (java.lang.Long obrigaRgDtEmissaoResponsavelTecnico) {
//        java.lang.Long obrigaRgDtEmissaoResponsavelTecnicoOld = this.obrigaRgDtEmissaoResponsavelTecnico;
		this.obrigaRgDtEmissaoResponsavelTecnico = obrigaRgDtEmissaoResponsavelTecnico;
//        this.getPropertyChangeSupport().firePropertyChange ("obrigaRgDtEmissaoResponsavelTecnico", obrigaRgDtEmissaoResponsavelTecnicoOld, obrigaRgDtEmissaoResponsavelTecnico);
	}



	/**
	 * Return the value associated with the column: flag_obriga_info_isento_taxa
	 */
	public java.lang.Long getFlagObrigaInformarIsentoTaxa () {
		return getPropertyValue(this, flagObrigaInformarIsentoTaxa, PROP_FLAG_OBRIGA_INFORMAR_ISENTO_TAXA); 
	}

	/**
	 * Set the value related to the column: flag_obriga_info_isento_taxa
	 * @param flagObrigaInformarIsentoTaxa the flag_obriga_info_isento_taxa value
	 */
	public void setFlagObrigaInformarIsentoTaxa (java.lang.Long flagObrigaInformarIsentoTaxa) {
//        java.lang.Long flagObrigaInformarIsentoTaxaOld = this.flagObrigaInformarIsentoTaxa;
		this.flagObrigaInformarIsentoTaxa = flagObrigaInformarIsentoTaxa;
//        this.getPropertyChangeSupport().firePropertyChange ("flagObrigaInformarIsentoTaxa", flagObrigaInformarIsentoTaxaOld, flagObrigaInformarIsentoTaxa);
	}



	/**
	 * Return the value associated with the column: flag_seleciona_talao_manual
	 */
	public java.lang.Long getFlagSelecionaTalaoManual () {
		return getPropertyValue(this, flagSelecionaTalaoManual, PROP_FLAG_SELECIONA_TALAO_MANUAL); 
	}

	/**
	 * Set the value related to the column: flag_seleciona_talao_manual
	 * @param flagSelecionaTalaoManual the flag_seleciona_talao_manual value
	 */
	public void setFlagSelecionaTalaoManual (java.lang.Long flagSelecionaTalaoManual) {
//        java.lang.Long flagSelecionaTalaoManualOld = this.flagSelecionaTalaoManual;
		this.flagSelecionaTalaoManual = flagSelecionaTalaoManual;
//        this.getPropertyChangeSupport().firePropertyChange ("flagSelecionaTalaoManual", flagSelecionaTalaoManualOld, flagSelecionaTalaoManual);
	}



	/**
	 * Return the value associated with the column: flag_tipo_gestao_req
	 */
	public java.lang.Long getFlagTipoGestaoRequerimento () {
		return getPropertyValue(this, flagTipoGestaoRequerimento, PROP_FLAG_TIPO_GESTAO_REQUERIMENTO); 
	}

	/**
	 * Set the value related to the column: flag_tipo_gestao_req
	 * @param flagTipoGestaoRequerimento the flag_tipo_gestao_req value
	 */
	public void setFlagTipoGestaoRequerimento (java.lang.Long flagTipoGestaoRequerimento) {
//        java.lang.Long flagTipoGestaoRequerimentoOld = this.flagTipoGestaoRequerimento;
		this.flagTipoGestaoRequerimento = flagTipoGestaoRequerimento;
//        this.getPropertyChangeSupport().firePropertyChange ("flagTipoGestaoRequerimento", flagTipoGestaoRequerimentoOld, flagTipoGestaoRequerimento);
	}



	/**
	 * Return the value associated with the column: flag_tipo_gestao_atividade
	 */
	public java.lang.Long getFlagTipoGestaoAtividade () {
		return getPropertyValue(this, flagTipoGestaoAtividade, PROP_FLAG_TIPO_GESTAO_ATIVIDADE); 
	}

	/**
	 * Set the value related to the column: flag_tipo_gestao_atividade
	 * @param flagTipoGestaoAtividade the flag_tipo_gestao_atividade value
	 */
	public void setFlagTipoGestaoAtividade (java.lang.Long flagTipoGestaoAtividade) {
//        java.lang.Long flagTipoGestaoAtividadeOld = this.flagTipoGestaoAtividade;
		this.flagTipoGestaoAtividade = flagTipoGestaoAtividade;
//        this.getPropertyChangeSupport().firePropertyChange ("flagTipoGestaoAtividade", flagTipoGestaoAtividadeOld, flagTipoGestaoAtividade);
	}



	/**
	 * Return the value associated with the column: iniciar_protocolo_receita_talidomida
	 */
	public java.lang.Long getIniciarProtocoloReceitaTalidomida () {
		return getPropertyValue(this, iniciarProtocoloReceitaTalidomida, PROP_INICIAR_PROTOCOLO_RECEITA_TALIDOMIDA); 
	}

	/**
	 * Set the value related to the column: iniciar_protocolo_receita_talidomida
	 * @param iniciarProtocoloReceitaTalidomida the iniciar_protocolo_receita_talidomida value
	 */
	public void setIniciarProtocoloReceitaTalidomida (java.lang.Long iniciarProtocoloReceitaTalidomida) {
//        java.lang.Long iniciarProtocoloReceitaTalidomidaOld = this.iniciarProtocoloReceitaTalidomida;
		this.iniciarProtocoloReceitaTalidomida = iniciarProtocoloReceitaTalidomida;
//        this.getPropertyChangeSupport().firePropertyChange ("iniciarProtocoloReceitaTalidomida", iniciarProtocoloReceitaTalidomidaOld, iniciarProtocoloReceitaTalidomida);
	}



	/**
	 * Return the value associated with the column: estoque_minimo_receita_talidomida
	 */
	public java.lang.Long getEstoqueMinimoReceitaTalidomida () {
		return getPropertyValue(this, estoqueMinimoReceitaTalidomida, PROP_ESTOQUE_MINIMO_RECEITA_TALIDOMIDA); 
	}

	/**
	 * Set the value related to the column: estoque_minimo_receita_talidomida
	 * @param estoqueMinimoReceitaTalidomida the estoque_minimo_receita_talidomida value
	 */
	public void setEstoqueMinimoReceitaTalidomida (java.lang.Long estoqueMinimoReceitaTalidomida) {
//        java.lang.Long estoqueMinimoReceitaTalidomidaOld = this.estoqueMinimoReceitaTalidomida;
		this.estoqueMinimoReceitaTalidomida = estoqueMinimoReceitaTalidomida;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueMinimoReceitaTalidomida", estoqueMinimoReceitaTalidomidaOld, estoqueMinimoReceitaTalidomida);
	}



	/**
	 * Return the value associated with the column: flag_selec_talao_man_talidomida
	 */
	public java.lang.Long getFlagSelecionaTalaoManualTalidomida () {
		return getPropertyValue(this, flagSelecionaTalaoManualTalidomida, PROP_FLAG_SELECIONA_TALAO_MANUAL_TALIDOMIDA); 
	}

	/**
	 * Set the value related to the column: flag_selec_talao_man_talidomida
	 * @param flagSelecionaTalaoManualTalidomida the flag_selec_talao_man_talidomida value
	 */
	public void setFlagSelecionaTalaoManualTalidomida (java.lang.Long flagSelecionaTalaoManualTalidomida) {
//        java.lang.Long flagSelecionaTalaoManualTalidomidaOld = this.flagSelecionaTalaoManualTalidomida;
		this.flagSelecionaTalaoManualTalidomida = flagSelecionaTalaoManualTalidomida;
//        this.getPropertyChangeSupport().firePropertyChange ("flagSelecionaTalaoManualTalidomida", flagSelecionaTalaoManualTalidomidaOld, flagSelecionaTalaoManualTalidomida);
	}



	/**
	 * Return the value associated with the column: flag_obriga_dados_veiculo
	 */
	public java.lang.Long getFlagObrigaDadosVeiculo () {
		return getPropertyValue(this, flagObrigaDadosVeiculo, PROP_FLAG_OBRIGA_DADOS_VEICULO); 
	}

	/**
	 * Set the value related to the column: flag_obriga_dados_veiculo
	 * @param flagObrigaDadosVeiculo the flag_obriga_dados_veiculo value
	 */
	public void setFlagObrigaDadosVeiculo (java.lang.Long flagObrigaDadosVeiculo) {
//        java.lang.Long flagObrigaDadosVeiculoOld = this.flagObrigaDadosVeiculo;
		this.flagObrigaDadosVeiculo = flagObrigaDadosVeiculo;
//        this.getPropertyChangeSupport().firePropertyChange ("flagObrigaDadosVeiculo", flagObrigaDadosVeiculoOld, flagObrigaDadosVeiculo);
	}



	/**
	 * Return the value associated with the column: flag_visualizar_dados_obra
	 */
	public java.lang.Long getFlagVisualizarDadosObra () {
		return getPropertyValue(this, flagVisualizarDadosObra, PROP_FLAG_VISUALIZAR_DADOS_OBRA); 
	}

	/**
	 * Set the value related to the column: flag_visualizar_dados_obra
	 * @param flagVisualizarDadosObra the flag_visualizar_dados_obra value
	 */
	public void setFlagVisualizarDadosObra (java.lang.Long flagVisualizarDadosObra) {
//        java.lang.Long flagVisualizarDadosObraOld = this.flagVisualizarDadosObra;
		this.flagVisualizarDadosObra = flagVisualizarDadosObra;
//        this.getPropertyChangeSupport().firePropertyChange ("flagVisualizarDadosObra", flagVisualizarDadosObraOld, flagVisualizarDadosObra);
	}



	/**
	 * Return the value associated with the column: flag_terceira_inst_proc_adm
	 */
	public java.lang.Long getFlagTerceiraInstanciaProcessoAdministrativo () {
		return getPropertyValue(this, flagTerceiraInstanciaProcessoAdministrativo, PROP_FLAG_TERCEIRA_INSTANCIA_PROCESSO_ADMINISTRATIVO); 
	}

	/**
	 * Set the value related to the column: flag_terceira_inst_proc_adm
	 * @param flagTerceiraInstanciaProcessoAdministrativo the flag_terceira_inst_proc_adm value
	 */
	public void setFlagTerceiraInstanciaProcessoAdministrativo (java.lang.Long flagTerceiraInstanciaProcessoAdministrativo) {
//        java.lang.Long flagTerceiraInstanciaProcessoAdministrativoOld = this.flagTerceiraInstanciaProcessoAdministrativo;
		this.flagTerceiraInstanciaProcessoAdministrativo = flagTerceiraInstanciaProcessoAdministrativo;
//        this.getPropertyChangeSupport().firePropertyChange ("flagTerceiraInstanciaProcessoAdministrativo", flagTerceiraInstanciaProcessoAdministrativoOld, flagTerceiraInstanciaProcessoAdministrativo);
	}



	/**
	 * Return the value associated with the column: msg_denuncia_anonima
	 */
	public java.lang.String getMensagemDenunciaAnonima () {
		return getPropertyValue(this, mensagemDenunciaAnonima, PROP_MENSAGEM_DENUNCIA_ANONIMA); 
	}

	/**
	 * Set the value related to the column: msg_denuncia_anonima
	 * @param mensagemDenunciaAnonima the msg_denuncia_anonima value
	 */
	public void setMensagemDenunciaAnonima (java.lang.String mensagemDenunciaAnonima) {
//        java.lang.String mensagemDenunciaAnonimaOld = this.mensagemDenunciaAnonima;
		this.mensagemDenunciaAnonima = mensagemDenunciaAnonima;
//        this.getPropertyChangeSupport().firePropertyChange ("mensagemDenunciaAnonima", mensagemDenunciaAnonimaOld, mensagemDenunciaAnonima);
	}



	/**
	 * Return the value associated with the column: nr_aprovacao_laudo_hidrosanit_inical
	 */
	public java.lang.Long getNumeroInicialAprovacaoLaudoHidrossanitario () {
		return getPropertyValue(this, numeroInicialAprovacaoLaudoHidrossanitario, PROP_NUMERO_INICIAL_APROVACAO_LAUDO_HIDROSSANITARIO); 
	}

	/**
	 * Set the value related to the column: nr_aprovacao_laudo_hidrosanit_inical
	 * @param numeroInicialAprovacaoLaudoHidrossanitario the nr_aprovacao_laudo_hidrosanit_inical value
	 */
	public void setNumeroInicialAprovacaoLaudoHidrossanitario (java.lang.Long numeroInicialAprovacaoLaudoHidrossanitario) {
//        java.lang.Long numeroInicialAprovacaoLaudoHidrossanitarioOld = this.numeroInicialAprovacaoLaudoHidrossanitario;
		this.numeroInicialAprovacaoLaudoHidrossanitario = numeroInicialAprovacaoLaudoHidrossanitario;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroInicialAprovacaoLaudoHidrossanitario", numeroInicialAprovacaoLaudoHidrossanitarioOld, numeroInicialAprovacaoLaudoHidrossanitario);
	}



	/**
	 * Return the value associated with the column: habitese_para
	 */
	public java.lang.String getHabitesePara () {
		return getPropertyValue(this, habitesePara, PROP_HABITESE_PARA); 
	}

	/**
	 * Set the value related to the column: habitese_para
	 * @param habitesePara the habitese_para value
	 */
	public void setHabitesePara (java.lang.String habitesePara) {
//        java.lang.String habiteseParaOld = this.habitesePara;
		this.habitesePara = habitesePara;
//        this.getPropertyChangeSupport().firePropertyChange ("habitesePara", habiteseParaOld, habitesePara);
	}



	/**
	 * Return the value associated with the column: habitese_importante
	 */
	public java.lang.String getHabiteseImportante () {
		return getPropertyValue(this, habiteseImportante, PROP_HABITESE_IMPORTANTE); 
	}

	/**
	 * Set the value related to the column: habitese_importante
	 * @param habiteseImportante the habitese_importante value
	 */
	public void setHabiteseImportante (java.lang.String habiteseImportante) {
//        java.lang.String habiteseImportanteOld = this.habiteseImportante;
		this.habiteseImportante = habiteseImportante;
//        this.getPropertyChangeSupport().firePropertyChange ("habiteseImportante", habiteseImportanteOld, habiteseImportante);
	}



	/**
	 * Return the value associated with the column: tp_ordenacao_requerimentos
	 */
	public java.lang.Long getTipoOrdenacaoRequerimentos () {
		return getPropertyValue(this, tipoOrdenacaoRequerimentos, PROP_TIPO_ORDENACAO_REQUERIMENTOS); 
	}

	/**
	 * Set the value related to the column: tp_ordenacao_requerimentos
	 * @param tipoOrdenacaoRequerimentos the tp_ordenacao_requerimentos value
	 */
	public void setTipoOrdenacaoRequerimentos (java.lang.Long tipoOrdenacaoRequerimentos) {
//        java.lang.Long tipoOrdenacaoRequerimentosOld = this.tipoOrdenacaoRequerimentos;
		this.tipoOrdenacaoRequerimentos = tipoOrdenacaoRequerimentos;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoOrdenacaoRequerimentos", tipoOrdenacaoRequerimentosOld, tipoOrdenacaoRequerimentos);
	}



	/**
	 * Return the value associated with the column: qtd_dias_inspecao_retroativa
	 */
	public java.lang.Long getQuantidadeDiasInspecaoRetroativa () {
		return getPropertyValue(this, quantidadeDiasInspecaoRetroativa, PROP_QUANTIDADE_DIAS_INSPECAO_RETROATIVA); 
	}

	/**
	 * Set the value related to the column: qtd_dias_inspecao_retroativa
	 * @param quantidadeDiasInspecaoRetroativa the qtd_dias_inspecao_retroativa value
	 */
	public void setQuantidadeDiasInspecaoRetroativa (java.lang.Long quantidadeDiasInspecaoRetroativa) {
//        java.lang.Long quantidadeDiasInspecaoRetroativaOld = this.quantidadeDiasInspecaoRetroativa;
		this.quantidadeDiasInspecaoRetroativa = quantidadeDiasInspecaoRetroativa;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeDiasInspecaoRetroativa", quantidadeDiasInspecaoRetroativaOld, quantidadeDiasInspecaoRetroativa);
	}



	/**
	 * Return the value associated with the column: finalizar_requerimento_receita
	 */
	public java.lang.Long getFinalizarRequerimentoReceita () {
		return getPropertyValue(this, finalizarRequerimentoReceita, PROP_FINALIZAR_REQUERIMENTO_RECEITA); 
	}

	/**
	 * Set the value related to the column: finalizar_requerimento_receita
	 * @param finalizarRequerimentoReceita the finalizar_requerimento_receita value
	 */
	public void setFinalizarRequerimentoReceita (java.lang.Long finalizarRequerimentoReceita) {
//        java.lang.Long finalizarRequerimentoReceitaOld = this.finalizarRequerimentoReceita;
		this.finalizarRequerimentoReceita = finalizarRequerimentoReceita;
//        this.getPropertyChangeSupport().firePropertyChange ("finalizarRequerimentoReceita", finalizarRequerimentoReceitaOld, finalizarRequerimentoReceita);
	}



	/**
	 * Return the value associated with the column: flag_formato_sequencial
	 */
	public java.lang.Long getFlagFormatoSequencial () {
		return getPropertyValue(this, flagFormatoSequencial, PROP_FLAG_FORMATO_SEQUENCIAL); 
	}

	/**
	 * Set the value related to the column: flag_formato_sequencial
	 * @param flagFormatoSequencial the flag_formato_sequencial value
	 */
	public void setFlagFormatoSequencial (java.lang.Long flagFormatoSequencial) {
//        java.lang.Long flagFormatoSequencialOld = this.flagFormatoSequencial;
		this.flagFormatoSequencial = flagFormatoSequencial;
//        this.getPropertyChangeSupport().firePropertyChange ("flagFormatoSequencial", flagFormatoSequencialOld, flagFormatoSequencial);
	}



	/**
	 * Return the value associated with the column: flag_cadastra_req_externo
	 */
	public java.lang.Long getFlagCadastraRequerimentoExterno () {
		return getPropertyValue(this, flagCadastraRequerimentoExterno, PROP_FLAG_CADASTRA_REQUERIMENTO_EXTERNO); 
	}

	/**
	 * Set the value related to the column: flag_cadastra_req_externo
	 * @param flagCadastraRequerimentoExterno the flag_cadastra_req_externo value
	 */
	public void setFlagCadastraRequerimentoExterno (java.lang.Long flagCadastraRequerimentoExterno) {
//        java.lang.Long flagCadastraRequerimentoExternoOld = this.flagCadastraRequerimentoExterno;
		this.flagCadastraRequerimentoExterno = flagCadastraRequerimentoExterno;
//        this.getPropertyChangeSupport().firePropertyChange ("flagCadastraRequerimentoExterno", flagCadastraRequerimentoExternoOld, flagCadastraRequerimentoExterno);
	}



	/**
	 * Return the value associated with the column: flag_habilita_denuncia_reclamacao_externo
	 */
	public java.lang.Boolean isFlagHabilitaDenunciaReclamacaoExterno () {
		return getPropertyValue(this, flagHabilitaDenunciaReclamacaoExterno, PROP_FLAG_HABILITA_DENUNCIA_RECLAMACAO_EXTERNO); 
	}

	/**
	 * Set the value related to the column: flag_habilita_denuncia_reclamacao_externo
	 * @param flagHabilitaDenunciaReclamacaoExterno the flag_habilita_denuncia_reclamacao_externo value
	 */
	public void setFlagHabilitaDenunciaReclamacaoExterno (java.lang.Boolean flagHabilitaDenunciaReclamacaoExterno) {
//        java.lang.Boolean flagHabilitaDenunciaReclamacaoExternoOld = this.flagHabilitaDenunciaReclamacaoExterno;
		this.flagHabilitaDenunciaReclamacaoExterno = flagHabilitaDenunciaReclamacaoExterno;
//        this.getPropertyChangeSupport().firePropertyChange ("flagHabilitaDenunciaReclamacaoExterno", flagHabilitaDenunciaReclamacaoExternoOld, flagHabilitaDenunciaReclamacaoExterno);
	}



	/**
	 * Return the value associated with the column: tipo_fluxo_externo
	 */
	public java.lang.Long getTipoFluxoExterno () {
		return getPropertyValue(this, tipoFluxoExterno, PROP_TIPO_FLUXO_EXTERNO); 
	}

	/**
	 * Set the value related to the column: tipo_fluxo_externo
	 * @param tipoFluxoExterno the tipo_fluxo_externo value
	 */
	public void setTipoFluxoExterno (java.lang.Long tipoFluxoExterno) {
//        java.lang.Long tipoFluxoExternoOld = this.tipoFluxoExterno;
		this.tipoFluxoExterno = tipoFluxoExterno;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoFluxoExterno", tipoFluxoExternoOld, tipoFluxoExterno);
	}



	/**
	 * Return the value associated with the column: url_roteiro_inspecao
	 */
	public java.lang.String getUrlRoteiroInspecao () {
		return getPropertyValue(this, urlRoteiroInspecao, PROP_URL_ROTEIRO_INSPECAO); 
	}

	/**
	 * Set the value related to the column: url_roteiro_inspecao
	 * @param urlRoteiroInspecao the url_roteiro_inspecao value
	 */
	public void setUrlRoteiroInspecao (java.lang.String urlRoteiroInspecao) {
//        java.lang.String urlRoteiroInspecaoOld = this.urlRoteiroInspecao;
		this.urlRoteiroInspecao = urlRoteiroInspecao;
//        this.getPropertyChangeSupport().firePropertyChange ("urlRoteiroInspecao", urlRoteiroInspecaoOld, urlRoteiroInspecao);
	}



	/**
	 * Return the value associated with the column: obs_destaque_cad_evento
	 */
	public java.lang.String getObservacaoDestaqueCadastroEvento () {
		return getPropertyValue(this, observacaoDestaqueCadastroEvento, PROP_OBSERVACAO_DESTAQUE_CADASTRO_EVENTO); 
	}

	/**
	 * Set the value related to the column: obs_destaque_cad_evento
	 * @param observacaoDestaqueCadastroEvento the obs_destaque_cad_evento value
	 */
	public void setObservacaoDestaqueCadastroEvento (java.lang.String observacaoDestaqueCadastroEvento) {
//        java.lang.String observacaoDestaqueCadastroEventoOld = this.observacaoDestaqueCadastroEvento;
		this.observacaoDestaqueCadastroEvento = observacaoDestaqueCadastroEvento;
//        this.getPropertyChangeSupport().firePropertyChange ("observacaoDestaqueCadastroEvento", observacaoDestaqueCadastroEventoOld, observacaoDestaqueCadastroEvento);
	}



	/**
	 * Return the value associated with the column: flag_habilita_campo_possui_estab_principal
	 */
	public java.lang.Long getFlagHabilitaCampoPossuiEstabPrincipal () {
		return getPropertyValue(this, flagHabilitaCampoPossuiEstabPrincipal, PROP_FLAG_HABILITA_CAMPO_POSSUI_ESTAB_PRINCIPAL); 
	}

	/**
	 * Set the value related to the column: flag_habilita_campo_possui_estab_principal
	 * @param flagHabilitaCampoPossuiEstabPrincipal the flag_habilita_campo_possui_estab_principal value
	 */
	public void setFlagHabilitaCampoPossuiEstabPrincipal (java.lang.Long flagHabilitaCampoPossuiEstabPrincipal) {
//        java.lang.Long flagHabilitaCampoPossuiEstabPrincipalOld = this.flagHabilitaCampoPossuiEstabPrincipal;
		this.flagHabilitaCampoPossuiEstabPrincipal = flagHabilitaCampoPossuiEstabPrincipal;
//        this.getPropertyChangeSupport().firePropertyChange ("flagHabilitaCampoPossuiEstabPrincipal", flagHabilitaCampoPossuiEstabPrincipalOld, flagHabilitaCampoPossuiEstabPrincipal);
	}



	/**
	 * Return the value associated with the column: flag_obrigatorio_informar_telefone
	 */
	public java.lang.Long getFlagObrigatorioInformarTelefone () {
		return getPropertyValue(this, flagObrigatorioInformarTelefone, PROP_FLAG_OBRIGATORIO_INFORMAR_TELEFONE); 
	}

	/**
	 * Set the value related to the column: flag_obrigatorio_informar_telefone
	 * @param flagObrigatorioInformarTelefone the flag_obrigatorio_informar_telefone value
	 */
	public void setFlagObrigatorioInformarTelefone (java.lang.Long flagObrigatorioInformarTelefone) {
//        java.lang.Long flagObrigatorioInformarTelefoneOld = this.flagObrigatorioInformarTelefone;
		this.flagObrigatorioInformarTelefone = flagObrigatorioInformarTelefone;
//        this.getPropertyChangeSupport().firePropertyChange ("flagObrigatorioInformarTelefone", flagObrigatorioInformarTelefoneOld, flagObrigatorioInformarTelefone);
	}



	/**
	 * Return the value associated with the column: sgl_org_mun_proj_arquitetonico
	 */
	public java.lang.String getSiglaOrgaoMunicipalProjetoArquitetonico () {
		return getPropertyValue(this, siglaOrgaoMunicipalProjetoArquitetonico, PROP_SIGLA_ORGAO_MUNICIPAL_PROJETO_ARQUITETONICO); 
	}

	/**
	 * Set the value related to the column: sgl_org_mun_proj_arquitetonico
	 * @param siglaOrgaoMunicipalProjetoArquitetonico the sgl_org_mun_proj_arquitetonico value
	 */
	public void setSiglaOrgaoMunicipalProjetoArquitetonico (java.lang.String siglaOrgaoMunicipalProjetoArquitetonico) {
//        java.lang.String siglaOrgaoMunicipalProjetoArquitetonicoOld = this.siglaOrgaoMunicipalProjetoArquitetonico;
		this.siglaOrgaoMunicipalProjetoArquitetonico = siglaOrgaoMunicipalProjetoArquitetonico;
//        this.getPropertyChangeSupport().firePropertyChange ("siglaOrgaoMunicipalProjetoArquitetonico", siglaOrgaoMunicipalProjetoArquitetonicoOld, siglaOrgaoMunicipalProjetoArquitetonico);
	}



	/**
	 * Return the value associated with the column: txt_descricao_selo_pranchas_hidrossanitario_padrao
	 */
	public java.lang.String getTextoDescricaoSeloPranchasHidrossanitarioPadrao () {
		return getPropertyValue(this, textoDescricaoSeloPranchasHidrossanitarioPadrao, PROP_TEXTO_DESCRICAO_SELO_PRANCHAS_HIDROSSANITARIO_PADRAO); 
	}

	/**
	 * Set the value related to the column: txt_descricao_selo_pranchas_hidrossanitario_padrao
	 * @param textoDescricaoSeloPranchasHidrossanitarioPadrao the txt_descricao_selo_pranchas_hidrossanitario_padrao value
	 */
	public void setTextoDescricaoSeloPranchasHidrossanitarioPadrao (java.lang.String textoDescricaoSeloPranchasHidrossanitarioPadrao) {
//        java.lang.String textoDescricaoSeloPranchasHidrossanitarioPadraoOld = this.textoDescricaoSeloPranchasHidrossanitarioPadrao;
		this.textoDescricaoSeloPranchasHidrossanitarioPadrao = textoDescricaoSeloPranchasHidrossanitarioPadrao;
//        this.getPropertyChangeSupport().firePropertyChange ("textoDescricaoSeloPranchasHidrossanitarioPadrao", textoDescricaoSeloPranchasHidrossanitarioPadraoOld, textoDescricaoSeloPranchasHidrossanitarioPadrao);
	}



	/**
	 * Return the value associated with the column: txt_descricao_selo_pranchas_hidrossanitario_declaratorio
	 */
	public java.lang.String getTextoDescricaoSeloPranchasHidrossanitarioDeclaratorio () {
		return getPropertyValue(this, textoDescricaoSeloPranchasHidrossanitarioDeclaratorio, PROP_TEXTO_DESCRICAO_SELO_PRANCHAS_HIDROSSANITARIO_DECLARATORIO); 
	}

	/**
	 * Set the value related to the column: txt_descricao_selo_pranchas_hidrossanitario_declaratorio
	 * @param textoDescricaoSeloPranchasHidrossanitarioDeclaratorio the txt_descricao_selo_pranchas_hidrossanitario_declaratorio value
	 */
	public void setTextoDescricaoSeloPranchasHidrossanitarioDeclaratorio (java.lang.String textoDescricaoSeloPranchasHidrossanitarioDeclaratorio) {
//        java.lang.String textoDescricaoSeloPranchasHidrossanitarioDeclaratorioOld = this.textoDescricaoSeloPranchasHidrossanitarioDeclaratorio;
		this.textoDescricaoSeloPranchasHidrossanitarioDeclaratorio = textoDescricaoSeloPranchasHidrossanitarioDeclaratorio;
//        this.getPropertyChangeSupport().firePropertyChange ("textoDescricaoSeloPranchasHidrossanitarioDeclaratorio", textoDescricaoSeloPranchasHidrossanitarioDeclaratorioOld, textoDescricaoSeloPranchasHidrossanitarioDeclaratorio);
	}



	/**
	 * Return the value associated with the column: txt_descricao_selo_pranchas_projeto_arquitetonico_sanitario
	 */
	public java.lang.String getTextoDescricaoSeloPranchasProjetoArquitetonicoSanitario () {
		return getPropertyValue(this, textoDescricaoSeloPranchasProjetoArquitetonicoSanitario, PROP_TEXTO_DESCRICAO_SELO_PRANCHAS_PROJETO_ARQUITETONICO_SANITARIO); 
	}

	/**
	 * Set the value related to the column: txt_descricao_selo_pranchas_projeto_arquitetonico_sanitario
	 * @param textoDescricaoSeloPranchasProjetoArquitetonicoSanitario the txt_descricao_selo_pranchas_projeto_arquitetonico_sanitario value
	 */
	public void setTextoDescricaoSeloPranchasProjetoArquitetonicoSanitario (java.lang.String textoDescricaoSeloPranchasProjetoArquitetonicoSanitario) {
//        java.lang.String textoDescricaoSeloPranchasProjetoArquitetonicoSanitarioOld = this.textoDescricaoSeloPranchasProjetoArquitetonicoSanitario;
		this.textoDescricaoSeloPranchasProjetoArquitetonicoSanitario = textoDescricaoSeloPranchasProjetoArquitetonicoSanitario;
//        this.getPropertyChangeSupport().firePropertyChange ("textoDescricaoSeloPranchasProjetoArquitetonicoSanitario", textoDescricaoSeloPranchasProjetoArquitetonicoSanitarioOld, textoDescricaoSeloPranchasProjetoArquitetonicoSanitario);
	}



	/**
	 * Return the value associated with the column: nr_aprovacao_laudo_arquitetonico_inicial
	 */
	public java.lang.Long getNumeroInicialAprovacaoLaudoArquitetonico () {
		return getPropertyValue(this, numeroInicialAprovacaoLaudoArquitetonico, PROP_NUMERO_INICIAL_APROVACAO_LAUDO_ARQUITETONICO); 
	}

	/**
	 * Set the value related to the column: nr_aprovacao_laudo_arquitetonico_inicial
	 * @param numeroInicialAprovacaoLaudoArquitetonico the nr_aprovacao_laudo_arquitetonico_inicial value
	 */
	public void setNumeroInicialAprovacaoLaudoArquitetonico (java.lang.Long numeroInicialAprovacaoLaudoArquitetonico) {
//        java.lang.Long numeroInicialAprovacaoLaudoArquitetonicoOld = this.numeroInicialAprovacaoLaudoArquitetonico;
		this.numeroInicialAprovacaoLaudoArquitetonico = numeroInicialAprovacaoLaudoArquitetonico;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroInicialAprovacaoLaudoArquitetonico", numeroInicialAprovacaoLaudoArquitetonicoOld, numeroInicialAprovacaoLaudoArquitetonico);
	}



	/**
	 * Return the value associated with the column: iniciar_protocolo_licenca_transporte
	 */
	public java.lang.Long getIniciarProtocoloLicencaTransporte () {
		return getPropertyValue(this, iniciarProtocoloLicencaTransporte, PROP_INICIAR_PROTOCOLO_LICENCA_TRANSPORTE); 
	}

	/**
	 * Set the value related to the column: iniciar_protocolo_licenca_transporte
	 * @param iniciarProtocoloLicencaTransporte the iniciar_protocolo_licenca_transporte value
	 */
	public void setIniciarProtocoloLicencaTransporte (java.lang.Long iniciarProtocoloLicencaTransporte) {
//        java.lang.Long iniciarProtocoloLicencaTransporteOld = this.iniciarProtocoloLicencaTransporte;
		this.iniciarProtocoloLicencaTransporte = iniciarProtocoloLicencaTransporte;
//        this.getPropertyChangeSupport().firePropertyChange ("iniciarProtocoloLicencaTransporte", iniciarProtocoloLicencaTransporteOld, iniciarProtocoloLicencaTransporte);
	}



	/**
	 * Return the value associated with the column: validade_licenca_transporte_data_fixa
	 */
	public java.lang.Long getValidadeLicencaDataFixa () {
		return getPropertyValue(this, validadeLicencaDataFixa, PROP_VALIDADE_LICENCA_DATA_FIXA); 
	}

	/**
	 * Set the value related to the column: validade_licenca_transporte_data_fixa
	 * @param validadeLicencaDataFixa the validade_licenca_transporte_data_fixa value
	 */
	public void setValidadeLicencaDataFixa (java.lang.Long validadeLicencaDataFixa) {
//        java.lang.Long validadeLicencaDataFixaOld = this.validadeLicencaDataFixa;
		this.validadeLicencaDataFixa = validadeLicencaDataFixa;
//        this.getPropertyChangeSupport().firePropertyChange ("validadeLicencaDataFixa", validadeLicencaDataFixaOld, validadeLicencaDataFixa);
	}



	/**
	 * Return the value associated with the column: validade_licenca_transporte_periodo
	 */
	public java.lang.Long getValidadeLicencaPeriodo () {
		return getPropertyValue(this, validadeLicencaPeriodo, PROP_VALIDADE_LICENCA_PERIODO); 
	}

	/**
	 * Set the value related to the column: validade_licenca_transporte_periodo
	 * @param validadeLicencaPeriodo the validade_licenca_transporte_periodo value
	 */
	public void setValidadeLicencaPeriodo (java.lang.Long validadeLicencaPeriodo) {
//        java.lang.Long validadeLicencaPeriodoOld = this.validadeLicencaPeriodo;
		this.validadeLicencaPeriodo = validadeLicencaPeriodo;
//        this.getPropertyChangeSupport().firePropertyChange ("validadeLicencaPeriodo", validadeLicencaPeriodoOld, validadeLicencaPeriodo);
	}



	/**
	 * Return the value associated with the column: validade_licenca_transporte_data_vencimento
	 */
	public java.util.Date getValidadeLicencaDataVencimento () {
		return getPropertyValue(this, validadeLicencaDataVencimento, PROP_VALIDADE_LICENCA_DATA_VENCIMENTO); 
	}

	/**
	 * Set the value related to the column: validade_licenca_transporte_data_vencimento
	 * @param validadeLicencaDataVencimento the validade_licenca_transporte_data_vencimento value
	 */
	public void setValidadeLicencaDataVencimento (java.util.Date validadeLicencaDataVencimento) {
//        java.util.Date validadeLicencaDataVencimentoOld = this.validadeLicencaDataVencimento;
		this.validadeLicencaDataVencimento = validadeLicencaDataVencimento;
//        this.getPropertyChangeSupport().firePropertyChange ("validadeLicencaDataVencimento", validadeLicencaDataVencimentoOld, validadeLicencaDataVencimento);
	}



	/**
	 * Return the value associated with the column: flag_licenca_transporte_usa_dt_vencimento_atividade_estab
	 */
	public java.lang.Long getLicencaTransporteUsaDataVencimentoDaAtividadeEstabelecimento () {
		return getPropertyValue(this, licencaTransporteUsaDataVencimentoDaAtividadeEstabelecimento, PROP_LICENCA_TRANSPORTE_USA_DATA_VENCIMENTO_DA_ATIVIDADE_ESTABELECIMENTO); 
	}

	/**
	 * Set the value related to the column: flag_licenca_transporte_usa_dt_vencimento_atividade_estab
	 * @param licencaTransporteUsaDataVencimentoDaAtividadeEstabelecimento the flag_licenca_transporte_usa_dt_vencimento_atividade_estab value
	 */
	public void setLicencaTransporteUsaDataVencimentoDaAtividadeEstabelecimento (java.lang.Long licencaTransporteUsaDataVencimentoDaAtividadeEstabelecimento) {
//        java.lang.Long licencaTransporteUsaDataVencimentoDaAtividadeEstabelecimentoOld = this.licencaTransporteUsaDataVencimentoDaAtividadeEstabelecimento;
		this.licencaTransporteUsaDataVencimentoDaAtividadeEstabelecimento = licencaTransporteUsaDataVencimentoDaAtividadeEstabelecimento;
//        this.getPropertyChangeSupport().firePropertyChange ("licencaTransporteUsaDataVencimentoDaAtividadeEstabelecimento", licencaTransporteUsaDataVencimentoDaAtividadeEstabelecimentoOld, licencaTransporteUsaDataVencimentoDaAtividadeEstabelecimento);
	}



	/**
	 * Return the value associated with the column: dt_inicial_licenca_transporte
	 */
	public java.util.Date getDataInicialLicencaTransporte () {
		return getPropertyValue(this, dataInicialLicencaTransporte, PROP_DATA_INICIAL_LICENCA_TRANSPORTE); 
	}

	/**
	 * Set the value related to the column: dt_inicial_licenca_transporte
	 * @param dataInicialLicencaTransporte the dt_inicial_licenca_transporte value
	 */
	public void setDataInicialLicencaTransporte (java.util.Date dataInicialLicencaTransporte) {
//        java.util.Date dataInicialLicencaTransporteOld = this.dataInicialLicencaTransporte;
		this.dataInicialLicencaTransporte = dataInicialLicencaTransporte;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInicialLicencaTransporte", dataInicialLicencaTransporteOld, dataInicialLicencaTransporte);
	}



	/**
	 * Return the value associated with the column: dt_final_licenca_transporte
	 */
	public java.util.Date getDataFinalLicencaTransporte () {
		return getPropertyValue(this, dataFinalLicencaTransporte, PROP_DATA_FINAL_LICENCA_TRANSPORTE); 
	}

	/**
	 * Set the value related to the column: dt_final_licenca_transporte
	 * @param dataFinalLicencaTransporte the dt_final_licenca_transporte value
	 */
	public void setDataFinalLicencaTransporte (java.util.Date dataFinalLicencaTransporte) {
//        java.util.Date dataFinalLicencaTransporteOld = this.dataFinalLicencaTransporte;
		this.dataFinalLicencaTransporte = dataFinalLicencaTransporte;
//        this.getPropertyChangeSupport().firePropertyChange ("dataFinalLicencaTransporte", dataFinalLicencaTransporteOld, dataFinalLicencaTransporte);
	}



	/**
	 * Return the value associated with the column: ano_base_licen_transp
	 */
	public java.lang.Long getAnoBaseLicencaTransporte () {
		return getPropertyValue(this, anoBaseLicencaTransporte, PROP_ANO_BASE_LICENCA_TRANSPORTE); 
	}

	/**
	 * Set the value related to the column: ano_base_licen_transp
	 * @param anoBaseLicencaTransporte the ano_base_licen_transp value
	 */
	public void setAnoBaseLicencaTransporte (java.lang.Long anoBaseLicencaTransporte) {
//        java.lang.Long anoBaseLicencaTransporteOld = this.anoBaseLicencaTransporte;
		this.anoBaseLicencaTransporte = anoBaseLicencaTransporte;
//        this.getPropertyChangeSupport().firePropertyChange ("anoBaseLicencaTransporte", anoBaseLicencaTransporteOld, anoBaseLicencaTransporte);
	}



	/**
	 * Return the value associated with the column: ini_proto_lic_trans_ano_anterior
	 */
	public java.lang.Long getIniciarProtocoloLicencaTransporteAnoAnterior () {
		return getPropertyValue(this, iniciarProtocoloLicencaTransporteAnoAnterior, PROP_INICIAR_PROTOCOLO_LICENCA_TRANSPORTE_ANO_ANTERIOR); 
	}

	/**
	 * Set the value related to the column: ini_proto_lic_trans_ano_anterior
	 * @param iniciarProtocoloLicencaTransporteAnoAnterior the ini_proto_lic_trans_ano_anterior value
	 */
	public void setIniciarProtocoloLicencaTransporteAnoAnterior (java.lang.Long iniciarProtocoloLicencaTransporteAnoAnterior) {
//        java.lang.Long iniciarProtocoloLicencaTransporteAnoAnteriorOld = this.iniciarProtocoloLicencaTransporteAnoAnterior;
		this.iniciarProtocoloLicencaTransporteAnoAnterior = iniciarProtocoloLicencaTransporteAnoAnterior;
//        this.getPropertyChangeSupport().firePropertyChange ("iniciarProtocoloLicencaTransporteAnoAnterior", iniciarProtocoloLicencaTransporteAnoAnteriorOld, iniciarProtocoloLicencaTransporteAnoAnterior);
	}



	/**
	 * Return the value associated with the column: tp_database_calc_lic_transp
	 */
	public java.lang.Long getTipoDataCalcLicencaTransporte () {
		return getPropertyValue(this, tipoDataCalcLicencaTransporte, PROP_TIPO_DATA_CALC_LICENCA_TRANSPORTE); 
	}

	/**
	 * Set the value related to the column: tp_database_calc_lic_transp
	 * @param tipoDataCalcLicencaTransporte the tp_database_calc_lic_transp value
	 */
	public void setTipoDataCalcLicencaTransporte (java.lang.Long tipoDataCalcLicencaTransporte) {
//        java.lang.Long tipoDataCalcLicencaTransporteOld = this.tipoDataCalcLicencaTransporte;
		this.tipoDataCalcLicencaTransporte = tipoDataCalcLicencaTransporte;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDataCalcLicencaTransporte", tipoDataCalcLicencaTransporteOld, tipoDataCalcLicencaTransporte);
	}



	/**
	 * Return the value associated with the column: flag_alvara_inicial_usa_dt_vencimento_atividade_estab
	 */
	public java.lang.Long getAlvaraInicialUsaDataVencimentoDaAtividadeEstabelecimento () {
		return getPropertyValue(this, alvaraInicialUsaDataVencimentoDaAtividadeEstabelecimento, PROP_ALVARA_INICIAL_USA_DATA_VENCIMENTO_DA_ATIVIDADE_ESTABELECIMENTO); 
	}

	/**
	 * Set the value related to the column: flag_alvara_inicial_usa_dt_vencimento_atividade_estab
	 * @param alvaraInicialUsaDataVencimentoDaAtividadeEstabelecimento the flag_alvara_inicial_usa_dt_vencimento_atividade_estab value
	 */
	public void setAlvaraInicialUsaDataVencimentoDaAtividadeEstabelecimento (java.lang.Long alvaraInicialUsaDataVencimentoDaAtividadeEstabelecimento) {
//        java.lang.Long alvaraInicialUsaDataVencimentoDaAtividadeEstabelecimentoOld = this.alvaraInicialUsaDataVencimentoDaAtividadeEstabelecimento;
		this.alvaraInicialUsaDataVencimentoDaAtividadeEstabelecimento = alvaraInicialUsaDataVencimentoDaAtividadeEstabelecimento;
//        this.getPropertyChangeSupport().firePropertyChange ("alvaraInicialUsaDataVencimentoDaAtividadeEstabelecimento", alvaraInicialUsaDataVencimentoDaAtividadeEstabelecimentoOld, alvaraInicialUsaDataVencimentoDaAtividadeEstabelecimento);
	}



	/**
	 * Return the value associated with the column: ano_base_alvara
	 */
	public java.lang.Long getAnoBaseAlvara () {
		return getPropertyValue(this, anoBaseAlvara, PROP_ANO_BASE_ALVARA); 
	}

	/**
	 * Set the value related to the column: ano_base_alvara
	 * @param anoBaseAlvara the ano_base_alvara value
	 */
	public void setAnoBaseAlvara (java.lang.Long anoBaseAlvara) {
//        java.lang.Long anoBaseAlvaraOld = this.anoBaseAlvara;
		this.anoBaseAlvara = anoBaseAlvara;
//        this.getPropertyChangeSupport().firePropertyChange ("anoBaseAlvara", anoBaseAlvaraOld, anoBaseAlvara);
	}



	/**
	 * Return the value associated with the column: nr_parecer_inical
	 */
	public java.lang.Long getNumeroParecerInicial () {
		return getPropertyValue(this, numeroParecerInicial, PROP_NUMERO_PARECER_INICIAL); 
	}

	/**
	 * Set the value related to the column: nr_parecer_inical
	 * @param numeroParecerInicial the nr_parecer_inical value
	 */
	public void setNumeroParecerInicial (java.lang.Long numeroParecerInicial) {
//        java.lang.Long numeroParecerInicialOld = this.numeroParecerInicial;
		this.numeroParecerInicial = numeroParecerInicial;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroParecerInicial", numeroParecerInicialOld, numeroParecerInicial);
	}



	/**
	 * Return the value associated with the column: validade_alvara_data_fixa
	 */
	public java.lang.Long getValidadeAlvaraDataFixa () {
		return getPropertyValue(this, validadeAlvaraDataFixa, PROP_VALIDADE_ALVARA_DATA_FIXA); 
	}

	/**
	 * Set the value related to the column: validade_alvara_data_fixa
	 * @param validadeAlvaraDataFixa the validade_alvara_data_fixa value
	 */
	public void setValidadeAlvaraDataFixa (java.lang.Long validadeAlvaraDataFixa) {
//        java.lang.Long validadeAlvaraDataFixaOld = this.validadeAlvaraDataFixa;
		this.validadeAlvaraDataFixa = validadeAlvaraDataFixa;
//        this.getPropertyChangeSupport().firePropertyChange ("validadeAlvaraDataFixa", validadeAlvaraDataFixaOld, validadeAlvaraDataFixa);
	}



	/**
	 * Return the value associated with the column: validade_alvara_periodo
	 */
	public java.lang.Long getValidadeAlvaraPeriodo () {
		return getPropertyValue(this, validadeAlvaraPeriodo, PROP_VALIDADE_ALVARA_PERIODO); 
	}

	/**
	 * Set the value related to the column: validade_alvara_periodo
	 * @param validadeAlvaraPeriodo the validade_alvara_periodo value
	 */
	public void setValidadeAlvaraPeriodo (java.lang.Long validadeAlvaraPeriodo) {
//        java.lang.Long validadeAlvaraPeriodoOld = this.validadeAlvaraPeriodo;
		this.validadeAlvaraPeriodo = validadeAlvaraPeriodo;
//        this.getPropertyChangeSupport().firePropertyChange ("validadeAlvaraPeriodo", validadeAlvaraPeriodoOld, validadeAlvaraPeriodo);
	}



	/**
	 * Return the value associated with the column: validade_alvara_data_vencimento
	 */
	public java.util.Date getValidadeAlvaraDataVencimento () {
		return getPropertyValue(this, validadeAlvaraDataVencimento, PROP_VALIDADE_ALVARA_DATA_VENCIMENTO); 
	}

	/**
	 * Set the value related to the column: validade_alvara_data_vencimento
	 * @param validadeAlvaraDataVencimento the validade_alvara_data_vencimento value
	 */
	public void setValidadeAlvaraDataVencimento (java.util.Date validadeAlvaraDataVencimento) {
//        java.util.Date validadeAlvaraDataVencimentoOld = this.validadeAlvaraDataVencimento;
		this.validadeAlvaraDataVencimento = validadeAlvaraDataVencimento;
//        this.getPropertyChangeSupport().firePropertyChange ("validadeAlvaraDataVencimento", validadeAlvaraDataVencimentoOld, validadeAlvaraDataVencimento);
	}



	/**
	 * Return the value associated with the column: iniciar_seq_alvara
	 */
	public java.lang.Long getIniciarSequencialAlvara () {
		return getPropertyValue(this, iniciarSequencialAlvara, PROP_INICIAR_SEQUENCIAL_ALVARA); 
	}

	/**
	 * Set the value related to the column: iniciar_seq_alvara
	 * @param iniciarSequencialAlvara the iniciar_seq_alvara value
	 */
	public void setIniciarSequencialAlvara (java.lang.Long iniciarSequencialAlvara) {
//        java.lang.Long iniciarSequencialAlvaraOld = this.iniciarSequencialAlvara;
		this.iniciarSequencialAlvara = iniciarSequencialAlvara;
//        this.getPropertyChangeSupport().firePropertyChange ("iniciarSequencialAlvara", iniciarSequencialAlvaraOld, iniciarSequencialAlvara);
	}



	/**
	 * Return the value associated with the column: obs_destaque_alvara
	 */
	public java.lang.String getObservacaoDestaqueAlvara () {
		return getPropertyValue(this, observacaoDestaqueAlvara, PROP_OBSERVACAO_DESTAQUE_ALVARA); 
	}

	/**
	 * Set the value related to the column: obs_destaque_alvara
	 * @param observacaoDestaqueAlvara the obs_destaque_alvara value
	 */
	public void setObservacaoDestaqueAlvara (java.lang.String observacaoDestaqueAlvara) {
//        java.lang.String observacaoDestaqueAlvaraOld = this.observacaoDestaqueAlvara;
		this.observacaoDestaqueAlvara = observacaoDestaqueAlvara;
//        this.getPropertyChangeSupport().firePropertyChange ("observacaoDestaqueAlvara", observacaoDestaqueAlvaraOld, observacaoDestaqueAlvara);
	}



	/**
	 * Return the value associated with the column: ini_seq_alvara_ano_anterior
	 */
	public java.lang.Long getIniciarSequencialAlvaraAnoAnterior () {
		return getPropertyValue(this, iniciarSequencialAlvaraAnoAnterior, PROP_INICIAR_SEQUENCIAL_ALVARA_ANO_ANTERIOR); 
	}

	/**
	 * Set the value related to the column: ini_seq_alvara_ano_anterior
	 * @param iniciarSequencialAlvaraAnoAnterior the ini_seq_alvara_ano_anterior value
	 */
	public void setIniciarSequencialAlvaraAnoAnterior (java.lang.Long iniciarSequencialAlvaraAnoAnterior) {
//        java.lang.Long iniciarSequencialAlvaraAnoAnteriorOld = this.iniciarSequencialAlvaraAnoAnterior;
		this.iniciarSequencialAlvaraAnoAnterior = iniciarSequencialAlvaraAnoAnterior;
//        this.getPropertyChangeSupport().firePropertyChange ("iniciarSequencialAlvaraAnoAnterior", iniciarSequencialAlvaraAnoAnteriorOld, iniciarSequencialAlvaraAnoAnterior);
	}



	/**
	 * Return the value associated with the column: flag_permite_alvara_provisorio
	 */
	public java.lang.Long getFlagPermiteAlvaraProvisorio () {
		return getPropertyValue(this, flagPermiteAlvaraProvisorio, PROP_FLAG_PERMITE_ALVARA_PROVISORIO); 
	}

	/**
	 * Set the value related to the column: flag_permite_alvara_provisorio
	 * @param flagPermiteAlvaraProvisorio the flag_permite_alvara_provisorio value
	 */
	public void setFlagPermiteAlvaraProvisorio (java.lang.Long flagPermiteAlvaraProvisorio) {
//        java.lang.Long flagPermiteAlvaraProvisorioOld = this.flagPermiteAlvaraProvisorio;
		this.flagPermiteAlvaraProvisorio = flagPermiteAlvaraProvisorio;
//        this.getPropertyChangeSupport().firePropertyChange ("flagPermiteAlvaraProvisorio", flagPermiteAlvaraProvisorioOld, flagPermiteAlvaraProvisorio);
	}



	/**
	 * Return the value associated with the column: flag_permite_impressao_externa
	 */
	public java.lang.Long getFlagPermiteImpressaoExterna () {
		return getPropertyValue(this, flagPermiteImpressaoExterna, PROP_FLAG_PERMITE_IMPRESSAO_EXTERNA); 
	}

	/**
	 * Set the value related to the column: flag_permite_impressao_externa
	 * @param flagPermiteImpressaoExterna the flag_permite_impressao_externa value
	 */
	public void setFlagPermiteImpressaoExterna (java.lang.Long flagPermiteImpressaoExterna) {
//        java.lang.Long flagPermiteImpressaoExternaOld = this.flagPermiteImpressaoExterna;
		this.flagPermiteImpressaoExterna = flagPermiteImpressaoExterna;
//        this.getPropertyChangeSupport().firePropertyChange ("flagPermiteImpressaoExterna", flagPermiteImpressaoExternaOld, flagPermiteImpressaoExterna);
	}



	/**
	 * Return the value associated with the column: tipo_atividade_alvara
	 */
	public java.lang.Long getTipoAtividadeAlvara () {
		return getPropertyValue(this, tipoAtividadeAlvara, PROP_TIPO_ATIVIDADE_ALVARA); 
	}

	/**
	 * Set the value related to the column: tipo_atividade_alvara
	 * @param tipoAtividadeAlvara the tipo_atividade_alvara value
	 */
	public void setTipoAtividadeAlvara (java.lang.Long tipoAtividadeAlvara) {
//        java.lang.Long tipoAtividadeAlvaraOld = this.tipoAtividadeAlvara;
		this.tipoAtividadeAlvara = tipoAtividadeAlvara;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAtividadeAlvara", tipoAtividadeAlvaraOld, tipoAtividadeAlvara);
	}



	/**
	 * Return the value associated with the column: marca_dagua_alvara
	 */
	public java.lang.Long getMarcaDaguaAlvara () {
		return getPropertyValue(this, marcaDaguaAlvara, PROP_MARCA_DAGUA_ALVARA); 
	}

	/**
	 * Set the value related to the column: marca_dagua_alvara
	 * @param marcaDaguaAlvara the marca_dagua_alvara value
	 */
	public void setMarcaDaguaAlvara (java.lang.Long marcaDaguaAlvara) {
//        java.lang.Long marcaDaguaAlvaraOld = this.marcaDaguaAlvara;
		this.marcaDaguaAlvara = marcaDaguaAlvara;
//        this.getPropertyChangeSupport().firePropertyChange ("marcaDaguaAlvara", marcaDaguaAlvaraOld, marcaDaguaAlvara);
	}



	/**
	 * Return the value associated with the column: exibir_grupo_alvara
	 */
	public java.lang.Long getExibirGrupoAlvara () {
		return getPropertyValue(this, exibirGrupoAlvara, PROP_EXIBIR_GRUPO_ALVARA); 
	}

	/**
	 * Set the value related to the column: exibir_grupo_alvara
	 * @param exibirGrupoAlvara the exibir_grupo_alvara value
	 */
	public void setExibirGrupoAlvara (java.lang.Long exibirGrupoAlvara) {
//        java.lang.Long exibirGrupoAlvaraOld = this.exibirGrupoAlvara;
		this.exibirGrupoAlvara = exibirGrupoAlvara;
//        this.getPropertyChangeSupport().firePropertyChange ("exibirGrupoAlvara", exibirGrupoAlvaraOld, exibirGrupoAlvara);
	}



	/**
	 * Return the value associated with the column: tp_database_calc_alvara
	 */
	public java.lang.Long getTipoDataCalcAlvara () {
		return getPropertyValue(this, tipoDataCalcAlvara, PROP_TIPO_DATA_CALC_ALVARA); 
	}

	/**
	 * Set the value related to the column: tp_database_calc_alvara
	 * @param tipoDataCalcAlvara the tp_database_calc_alvara value
	 */
	public void setTipoDataCalcAlvara (java.lang.Long tipoDataCalcAlvara) {
//        java.lang.Long tipoDataCalcAlvaraOld = this.tipoDataCalcAlvara;
		this.tipoDataCalcAlvara = tipoDataCalcAlvara;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDataCalcAlvara", tipoDataCalcAlvaraOld, tipoDataCalcAlvara);
	}



	/**
	 * Return the value associated with the column: flag_revalidacao_alvara_usa_dt_vencimento_atividade_estab
	 */
	public java.lang.Long getRevalidacaoAlvaraUsaDataVencimentoDaAtividadeEstabelecimento () {
		return getPropertyValue(this, revalidacaoAlvaraUsaDataVencimentoDaAtividadeEstabelecimento, PROP_REVALIDACAO_ALVARA_USA_DATA_VENCIMENTO_DA_ATIVIDADE_ESTABELECIMENTO); 
	}

	/**
	 * Set the value related to the column: flag_revalidacao_alvara_usa_dt_vencimento_atividade_estab
	 * @param revalidacaoAlvaraUsaDataVencimentoDaAtividadeEstabelecimento the flag_revalidacao_alvara_usa_dt_vencimento_atividade_estab value
	 */
	public void setRevalidacaoAlvaraUsaDataVencimentoDaAtividadeEstabelecimento (java.lang.Long revalidacaoAlvaraUsaDataVencimentoDaAtividadeEstabelecimento) {
//        java.lang.Long revalidacaoAlvaraUsaDataVencimentoDaAtividadeEstabelecimentoOld = this.revalidacaoAlvaraUsaDataVencimentoDaAtividadeEstabelecimento;
		this.revalidacaoAlvaraUsaDataVencimentoDaAtividadeEstabelecimento = revalidacaoAlvaraUsaDataVencimentoDaAtividadeEstabelecimento;
//        this.getPropertyChangeSupport().firePropertyChange ("revalidacaoAlvaraUsaDataVencimentoDaAtividadeEstabelecimento", revalidacaoAlvaraUsaDataVencimentoDaAtividadeEstabelecimentoOld, revalidacaoAlvaraUsaDataVencimentoDaAtividadeEstabelecimento);
	}



	/**
	 * Return the value associated with the column: flag_alvara_revalidacao_usa_data_fixa
	 */
	public java.lang.Long getAlvaraRevalidacaoUsaDataFixa () {
		return getPropertyValue(this, alvaraRevalidacaoUsaDataFixa, PROP_ALVARA_REVALIDACAO_USA_DATA_FIXA); 
	}

	/**
	 * Set the value related to the column: flag_alvara_revalidacao_usa_data_fixa
	 * @param alvaraRevalidacaoUsaDataFixa the flag_alvara_revalidacao_usa_data_fixa value
	 */
	public void setAlvaraRevalidacaoUsaDataFixa (java.lang.Long alvaraRevalidacaoUsaDataFixa) {
//        java.lang.Long alvaraRevalidacaoUsaDataFixaOld = this.alvaraRevalidacaoUsaDataFixa;
		this.alvaraRevalidacaoUsaDataFixa = alvaraRevalidacaoUsaDataFixa;
//        this.getPropertyChangeSupport().firePropertyChange ("alvaraRevalidacaoUsaDataFixa", alvaraRevalidacaoUsaDataFixaOld, alvaraRevalidacaoUsaDataFixa);
	}



	/**
	 * Return the value associated with the column: tipo_data_calculo_revalidacao_alvara
	 */
	public java.lang.Long getTipoDataCalculoRevalidacaoAlvara () {
		return getPropertyValue(this, tipoDataCalculoRevalidacaoAlvara, PROP_TIPO_DATA_CALCULO_REVALIDACAO_ALVARA); 
	}

	/**
	 * Set the value related to the column: tipo_data_calculo_revalidacao_alvara
	 * @param tipoDataCalculoRevalidacaoAlvara the tipo_data_calculo_revalidacao_alvara value
	 */
	public void setTipoDataCalculoRevalidacaoAlvara (java.lang.Long tipoDataCalculoRevalidacaoAlvara) {
//        java.lang.Long tipoDataCalculoRevalidacaoAlvaraOld = this.tipoDataCalculoRevalidacaoAlvara;
		this.tipoDataCalculoRevalidacaoAlvara = tipoDataCalculoRevalidacaoAlvara;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDataCalculoRevalidacaoAlvara", tipoDataCalculoRevalidacaoAlvaraOld, tipoDataCalculoRevalidacaoAlvara);
	}



	/**
	 * Return the value associated with the column: periodo_validade_revalidacao_alvara
	 */
	public java.lang.Long getPeriodoValidadeRevalidacaoAlvara () {
		return getPropertyValue(this, periodoValidadeRevalidacaoAlvara, PROP_PERIODO_VALIDADE_REVALIDACAO_ALVARA); 
	}

	/**
	 * Set the value related to the column: periodo_validade_revalidacao_alvara
	 * @param periodoValidadeRevalidacaoAlvara the periodo_validade_revalidacao_alvara value
	 */
	public void setPeriodoValidadeRevalidacaoAlvara (java.lang.Long periodoValidadeRevalidacaoAlvara) {
//        java.lang.Long periodoValidadeRevalidacaoAlvaraOld = this.periodoValidadeRevalidacaoAlvara;
		this.periodoValidadeRevalidacaoAlvara = periodoValidadeRevalidacaoAlvara;
//        this.getPropertyChangeSupport().firePropertyChange ("periodoValidadeRevalidacaoAlvara", periodoValidadeRevalidacaoAlvaraOld, periodoValidadeRevalidacaoAlvara);
	}



	/**
	 * Return the value associated with the column: data_vencimento_revalidacao_alvara
	 */
	public java.util.Date getDataVencimentoRevalidacaoAlvara () {
		return getPropertyValue(this, dataVencimentoRevalidacaoAlvara, PROP_DATA_VENCIMENTO_REVALIDACAO_ALVARA); 
	}

	/**
	 * Set the value related to the column: data_vencimento_revalidacao_alvara
	 * @param dataVencimentoRevalidacaoAlvara the data_vencimento_revalidacao_alvara value
	 */
	public void setDataVencimentoRevalidacaoAlvara (java.util.Date dataVencimentoRevalidacaoAlvara) {
//        java.util.Date dataVencimentoRevalidacaoAlvaraOld = this.dataVencimentoRevalidacaoAlvara;
		this.dataVencimentoRevalidacaoAlvara = dataVencimentoRevalidacaoAlvara;
//        this.getPropertyChangeSupport().firePropertyChange ("dataVencimentoRevalidacaoAlvara", dataVencimentoRevalidacaoAlvaraOld, dataVencimentoRevalidacaoAlvara);
	}



	/**
	 * Return the value associated with the column: dt_inicial_revalidacao_alvara
	 */
	public java.util.Date getDataInicialRevalidacaoAlvara () {
		return getPropertyValue(this, dataInicialRevalidacaoAlvara, PROP_DATA_INICIAL_REVALIDACAO_ALVARA); 
	}

	/**
	 * Set the value related to the column: dt_inicial_revalidacao_alvara
	 * @param dataInicialRevalidacaoAlvara the dt_inicial_revalidacao_alvara value
	 */
	public void setDataInicialRevalidacaoAlvara (java.util.Date dataInicialRevalidacaoAlvara) {
//        java.util.Date dataInicialRevalidacaoAlvaraOld = this.dataInicialRevalidacaoAlvara;
		this.dataInicialRevalidacaoAlvara = dataInicialRevalidacaoAlvara;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInicialRevalidacaoAlvara", dataInicialRevalidacaoAlvaraOld, dataInicialRevalidacaoAlvara);
	}



	/**
	 * Return the value associated with the column: dt_final_revalidacao_alvara
	 */
	public java.util.Date getDataFinalRevalidacaoAlvara () {
		return getPropertyValue(this, dataFinalRevalidacaoAlvara, PROP_DATA_FINAL_REVALIDACAO_ALVARA); 
	}

	/**
	 * Set the value related to the column: dt_final_revalidacao_alvara
	 * @param dataFinalRevalidacaoAlvara the dt_final_revalidacao_alvara value
	 */
	public void setDataFinalRevalidacaoAlvara (java.util.Date dataFinalRevalidacaoAlvara) {
//        java.util.Date dataFinalRevalidacaoAlvaraOld = this.dataFinalRevalidacaoAlvara;
		this.dataFinalRevalidacaoAlvara = dataFinalRevalidacaoAlvara;
//        this.getPropertyChangeSupport().firePropertyChange ("dataFinalRevalidacaoAlvara", dataFinalRevalidacaoAlvaraOld, dataFinalRevalidacaoAlvara);
	}



	/**
	 * Return the value associated with the column: ano_base_revalidacao_alvara
	 */
	public java.lang.Long getAnoBaseRevalidacaoAlvara () {
		return getPropertyValue(this, anoBaseRevalidacaoAlvara, PROP_ANO_BASE_REVALIDACAO_ALVARA); 
	}

	/**
	 * Set the value related to the column: ano_base_revalidacao_alvara
	 * @param anoBaseRevalidacaoAlvara the ano_base_revalidacao_alvara value
	 */
	public void setAnoBaseRevalidacaoAlvara (java.lang.Long anoBaseRevalidacaoAlvara) {
//        java.lang.Long anoBaseRevalidacaoAlvaraOld = this.anoBaseRevalidacaoAlvara;
		this.anoBaseRevalidacaoAlvara = anoBaseRevalidacaoAlvara;
//        this.getPropertyChangeSupport().firePropertyChange ("anoBaseRevalidacaoAlvara", anoBaseRevalidacaoAlvaraOld, anoBaseRevalidacaoAlvara);
	}



	/**
	 * Return the value associated with the column: qtd_anos_vencimento_alvara_inicial
	 */
	public java.lang.Long getQtdAnosVencimentoAlvaraInicial () {
		return getPropertyValue(this, qtdAnosVencimentoAlvaraInicial, PROP_QTD_ANOS_VENCIMENTO_ALVARA_INICIAL); 
	}

	/**
	 * Set the value related to the column: qtd_anos_vencimento_alvara_inicial
	 * @param qtdAnosVencimentoAlvaraInicial the qtd_anos_vencimento_alvara_inicial value
	 */
	public void setQtdAnosVencimentoAlvaraInicial (java.lang.Long qtdAnosVencimentoAlvaraInicial) {
//        java.lang.Long qtdAnosVencimentoAlvaraInicialOld = this.qtdAnosVencimentoAlvaraInicial;
		this.qtdAnosVencimentoAlvaraInicial = qtdAnosVencimentoAlvaraInicial;
//        this.getPropertyChangeSupport().firePropertyChange ("qtdAnosVencimentoAlvaraInicial", qtdAnosVencimentoAlvaraInicialOld, qtdAnosVencimentoAlvaraInicial);
	}



	/**
	 * Return the value associated with the column: flag_autorizacao_sanitaria_usa_data_fixa
	 */
	public java.lang.Long getAutorizacaoSanitariaUsaDataFixa () {
		return getPropertyValue(this, autorizacaoSanitariaUsaDataFixa, PROP_AUTORIZACAO_SANITARIA_USA_DATA_FIXA); 
	}

	/**
	 * Set the value related to the column: flag_autorizacao_sanitaria_usa_data_fixa
	 * @param autorizacaoSanitariaUsaDataFixa the flag_autorizacao_sanitaria_usa_data_fixa value
	 */
	public void setAutorizacaoSanitariaUsaDataFixa (java.lang.Long autorizacaoSanitariaUsaDataFixa) {
//        java.lang.Long autorizacaoSanitariaUsaDataFixaOld = this.autorizacaoSanitariaUsaDataFixa;
		this.autorizacaoSanitariaUsaDataFixa = autorizacaoSanitariaUsaDataFixa;
//        this.getPropertyChangeSupport().firePropertyChange ("autorizacaoSanitariaUsaDataFixa", autorizacaoSanitariaUsaDataFixaOld, autorizacaoSanitariaUsaDataFixa);
	}



	/**
	 * Return the value associated with the column: tipo_data_calculo_autorizacao_sanitaria
	 */
	public java.lang.Long getTipoDataCalculoAutorizacaoSanitaria () {
		return getPropertyValue(this, tipoDataCalculoAutorizacaoSanitaria, PROP_TIPO_DATA_CALCULO_AUTORIZACAO_SANITARIA); 
	}

	/**
	 * Set the value related to the column: tipo_data_calculo_autorizacao_sanitaria
	 * @param tipoDataCalculoAutorizacaoSanitaria the tipo_data_calculo_autorizacao_sanitaria value
	 */
	public void setTipoDataCalculoAutorizacaoSanitaria (java.lang.Long tipoDataCalculoAutorizacaoSanitaria) {
//        java.lang.Long tipoDataCalculoAutorizacaoSanitariaOld = this.tipoDataCalculoAutorizacaoSanitaria;
		this.tipoDataCalculoAutorizacaoSanitaria = tipoDataCalculoAutorizacaoSanitaria;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDataCalculoAutorizacaoSanitaria", tipoDataCalculoAutorizacaoSanitariaOld, tipoDataCalculoAutorizacaoSanitaria);
	}



	/**
	 * Return the value associated with the column: periodo_validade_autorizacao_sanitaria
	 */
	public java.lang.Long getPeriodoValidadeAutorizacaoSanitaria () {
		return getPropertyValue(this, periodoValidadeAutorizacaoSanitaria, PROP_PERIODO_VALIDADE_AUTORIZACAO_SANITARIA); 
	}

	/**
	 * Set the value related to the column: periodo_validade_autorizacao_sanitaria
	 * @param periodoValidadeAutorizacaoSanitaria the periodo_validade_autorizacao_sanitaria value
	 */
	public void setPeriodoValidadeAutorizacaoSanitaria (java.lang.Long periodoValidadeAutorizacaoSanitaria) {
//        java.lang.Long periodoValidadeAutorizacaoSanitariaOld = this.periodoValidadeAutorizacaoSanitaria;
		this.periodoValidadeAutorizacaoSanitaria = periodoValidadeAutorizacaoSanitaria;
//        this.getPropertyChangeSupport().firePropertyChange ("periodoValidadeAutorizacaoSanitaria", periodoValidadeAutorizacaoSanitariaOld, periodoValidadeAutorizacaoSanitaria);
	}



	/**
	 * Return the value associated with the column: data_vencimento_autorizacao_sanitaria
	 */
	public java.util.Date getDataVencimentoAutorizacaoSanitaria () {
		return getPropertyValue(this, dataVencimentoAutorizacaoSanitaria, PROP_DATA_VENCIMENTO_AUTORIZACAO_SANITARIA); 
	}

	/**
	 * Set the value related to the column: data_vencimento_autorizacao_sanitaria
	 * @param dataVencimentoAutorizacaoSanitaria the data_vencimento_autorizacao_sanitaria value
	 */
	public void setDataVencimentoAutorizacaoSanitaria (java.util.Date dataVencimentoAutorizacaoSanitaria) {
//        java.util.Date dataVencimentoAutorizacaoSanitariaOld = this.dataVencimentoAutorizacaoSanitaria;
		this.dataVencimentoAutorizacaoSanitaria = dataVencimentoAutorizacaoSanitaria;
//        this.getPropertyChangeSupport().firePropertyChange ("dataVencimentoAutorizacaoSanitaria", dataVencimentoAutorizacaoSanitariaOld, dataVencimentoAutorizacaoSanitaria);
	}



	/**
	 * Return the value associated with the column: obs_destaque_autorizacao_san
	 */
	public java.lang.String getObservacaoDestaqueAutorizacaoSanitaria () {
		return getPropertyValue(this, observacaoDestaqueAutorizacaoSanitaria, PROP_OBSERVACAO_DESTAQUE_AUTORIZACAO_SANITARIA); 
	}

	/**
	 * Set the value related to the column: obs_destaque_autorizacao_san
	 * @param observacaoDestaqueAutorizacaoSanitaria the obs_destaque_autorizacao_san value
	 */
	public void setObservacaoDestaqueAutorizacaoSanitaria (java.lang.String observacaoDestaqueAutorizacaoSanitaria) {
//        java.lang.String observacaoDestaqueAutorizacaoSanitariaOld = this.observacaoDestaqueAutorizacaoSanitaria;
		this.observacaoDestaqueAutorizacaoSanitaria = observacaoDestaqueAutorizacaoSanitaria;
//        this.getPropertyChangeSupport().firePropertyChange ("observacaoDestaqueAutorizacaoSanitaria", observacaoDestaqueAutorizacaoSanitariaOld, observacaoDestaqueAutorizacaoSanitaria);
	}



	/**
	 * Return the value associated with the column: ano_base_autorizacao_sanitaria
	 */
	public java.lang.Long getAnoBaseAutorizacaoSanitaria () {
		return getPropertyValue(this, anoBaseAutorizacaoSanitaria, PROP_ANO_BASE_AUTORIZACAO_SANITARIA); 
	}

	/**
	 * Set the value related to the column: ano_base_autorizacao_sanitaria
	 * @param anoBaseAutorizacaoSanitaria the ano_base_autorizacao_sanitaria value
	 */
	public void setAnoBaseAutorizacaoSanitaria (java.lang.Long anoBaseAutorizacaoSanitaria) {
//        java.lang.Long anoBaseAutorizacaoSanitariaOld = this.anoBaseAutorizacaoSanitaria;
		this.anoBaseAutorizacaoSanitaria = anoBaseAutorizacaoSanitaria;
//        this.getPropertyChangeSupport().firePropertyChange ("anoBaseAutorizacaoSanitaria", anoBaseAutorizacaoSanitariaOld, anoBaseAutorizacaoSanitaria);
	}



	/**
	 * Return the value associated with the column: validade_treinamento_data_fixa
	 */
	public java.lang.Long getValidadeTreinamentoDataFixa () {
		return getPropertyValue(this, validadeTreinamentoDataFixa, PROP_VALIDADE_TREINAMENTO_DATA_FIXA); 
	}

	/**
	 * Set the value related to the column: validade_treinamento_data_fixa
	 * @param validadeTreinamentoDataFixa the validade_treinamento_data_fixa value
	 */
	public void setValidadeTreinamentoDataFixa (java.lang.Long validadeTreinamentoDataFixa) {
//        java.lang.Long validadeTreinamentoDataFixaOld = this.validadeTreinamentoDataFixa;
		this.validadeTreinamentoDataFixa = validadeTreinamentoDataFixa;
//        this.getPropertyChangeSupport().firePropertyChange ("validadeTreinamentoDataFixa", validadeTreinamentoDataFixaOld, validadeTreinamentoDataFixa);
	}



	/**
	 * Return the value associated with the column: validade_treinamento_data_vencimento
	 */
	public java.util.Date getValidadeTreinamentoDataVencimento () {
		return getPropertyValue(this, validadeTreinamentoDataVencimento, PROP_VALIDADE_TREINAMENTO_DATA_VENCIMENTO); 
	}

	/**
	 * Set the value related to the column: validade_treinamento_data_vencimento
	 * @param validadeTreinamentoDataVencimento the validade_treinamento_data_vencimento value
	 */
	public void setValidadeTreinamentoDataVencimento (java.util.Date validadeTreinamentoDataVencimento) {
//        java.util.Date validadeTreinamentoDataVencimentoOld = this.validadeTreinamentoDataVencimento;
		this.validadeTreinamentoDataVencimento = validadeTreinamentoDataVencimento;
//        this.getPropertyChangeSupport().firePropertyChange ("validadeTreinamentoDataVencimento", validadeTreinamentoDataVencimentoOld, validadeTreinamentoDataVencimento);
	}



	/**
	 * Return the value associated with the column: iniciar_seq_treinamento
	 */
	public java.lang.Long getIniciarSequencialTreinamento () {
		return getPropertyValue(this, iniciarSequencialTreinamento, PROP_INICIAR_SEQUENCIAL_TREINAMENTO); 
	}

	/**
	 * Set the value related to the column: iniciar_seq_treinamento
	 * @param iniciarSequencialTreinamento the iniciar_seq_treinamento value
	 */
	public void setIniciarSequencialTreinamento (java.lang.Long iniciarSequencialTreinamento) {
//        java.lang.Long iniciarSequencialTreinamentoOld = this.iniciarSequencialTreinamento;
		this.iniciarSequencialTreinamento = iniciarSequencialTreinamento;
//        this.getPropertyChangeSupport().firePropertyChange ("iniciarSequencialTreinamento", iniciarSequencialTreinamentoOld, iniciarSequencialTreinamento);
	}



	/**
	 * Return the value associated with the column: marca_dagua_treinamento
	 */
	public java.lang.Long getMarcaDaguaTreinamento () {
		return getPropertyValue(this, marcaDaguaTreinamento, PROP_MARCA_DAGUA_TREINAMENTO); 
	}

	/**
	 * Set the value related to the column: marca_dagua_treinamento
	 * @param marcaDaguaTreinamento the marca_dagua_treinamento value
	 */
	public void setMarcaDaguaTreinamento (java.lang.Long marcaDaguaTreinamento) {
//        java.lang.Long marcaDaguaTreinamentoOld = this.marcaDaguaTreinamento;
		this.marcaDaguaTreinamento = marcaDaguaTreinamento;
//        this.getPropertyChangeSupport().firePropertyChange ("marcaDaguaTreinamento", marcaDaguaTreinamentoOld, marcaDaguaTreinamento);
	}



	/**
	 * Return the value associated with the column: credenciamento_para
	 */
	public java.lang.String getCredenciamentoPara () {
		return getPropertyValue(this, credenciamentoPara, PROP_CREDENCIAMENTO_PARA); 
	}

	/**
	 * Set the value related to the column: credenciamento_para
	 * @param credenciamentoPara the credenciamento_para value
	 */
	public void setCredenciamentoPara (java.lang.String credenciamentoPara) {
//        java.lang.String credenciamentoParaOld = this.credenciamentoPara;
		this.credenciamentoPara = credenciamentoPara;
//        this.getPropertyChangeSupport().firePropertyChange ("credenciamentoPara", credenciamentoParaOld, credenciamentoPara);
	}



	/**
	 * Return the value associated with the column: validade_treinamento_periodo
	 */
	public java.lang.Long getValidadeTreinamentoPeriodo () {
		return getPropertyValue(this, validadeTreinamentoPeriodo, PROP_VALIDADE_TREINAMENTO_PERIODO); 
	}

	/**
	 * Set the value related to the column: validade_treinamento_periodo
	 * @param validadeTreinamentoPeriodo the validade_treinamento_periodo value
	 */
	public void setValidadeTreinamentoPeriodo (java.lang.Long validadeTreinamentoPeriodo) {
//        java.lang.Long validadeTreinamentoPeriodoOld = this.validadeTreinamentoPeriodo;
		this.validadeTreinamentoPeriodo = validadeTreinamentoPeriodo;
//        this.getPropertyChangeSupport().firePropertyChange ("validadeTreinamentoPeriodo", validadeTreinamentoPeriodoOld, validadeTreinamentoPeriodo);
	}



	/**
	 * Return the value associated with the column: qtd_periodo_validade_treinamento
	 */
	public java.lang.Long getQuantidadePeriodoValidadeTreinamento () {
		return getPropertyValue(this, quantidadePeriodoValidadeTreinamento, PROP_QUANTIDADE_PERIODO_VALIDADE_TREINAMENTO); 
	}

	/**
	 * Set the value related to the column: qtd_periodo_validade_treinamento
	 * @param quantidadePeriodoValidadeTreinamento the qtd_periodo_validade_treinamento value
	 */
	public void setQuantidadePeriodoValidadeTreinamento (java.lang.Long quantidadePeriodoValidadeTreinamento) {
//        java.lang.Long quantidadePeriodoValidadeTreinamentoOld = this.quantidadePeriodoValidadeTreinamento;
		this.quantidadePeriodoValidadeTreinamento = quantidadePeriodoValidadeTreinamento;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadePeriodoValidadeTreinamento", quantidadePeriodoValidadeTreinamentoOld, quantidadePeriodoValidadeTreinamento);
	}



	/**
	 * Return the value associated with the column: orientacao_treinamento
	 */
	public java.lang.String getOrientacaoTreinamento () {
		return getPropertyValue(this, orientacaoTreinamento, PROP_ORIENTACAO_TREINAMENTO); 
	}

	/**
	 * Set the value related to the column: orientacao_treinamento
	 * @param orientacaoTreinamento the orientacao_treinamento value
	 */
	public void setOrientacaoTreinamento (java.lang.String orientacaoTreinamento) {
//        java.lang.String orientacaoTreinamentoOld = this.orientacaoTreinamento;
		this.orientacaoTreinamento = orientacaoTreinamento;
//        this.getPropertyChangeSupport().firePropertyChange ("orientacaoTreinamento", orientacaoTreinamentoOld, orientacaoTreinamento);
	}



	/**
	 * Return the value associated with the column: obs_destaque_treinamento
	 */
	public java.lang.String getObservacaoDestaqueTreinamento () {
		return getPropertyValue(this, observacaoDestaqueTreinamento, PROP_OBSERVACAO_DESTAQUE_TREINAMENTO); 
	}

	/**
	 * Set the value related to the column: obs_destaque_treinamento
	 * @param observacaoDestaqueTreinamento the obs_destaque_treinamento value
	 */
	public void setObservacaoDestaqueTreinamento (java.lang.String observacaoDestaqueTreinamento) {
//        java.lang.String observacaoDestaqueTreinamentoOld = this.observacaoDestaqueTreinamento;
		this.observacaoDestaqueTreinamento = observacaoDestaqueTreinamento;
//        this.getPropertyChangeSupport().firePropertyChange ("observacaoDestaqueTreinamento", observacaoDestaqueTreinamentoOld, observacaoDestaqueTreinamento);
	}



	/**
	 * Return the value associated with the column: tp_database_calc_credenciamento
	 */
	public java.lang.Long getTipoDataCalcCredenciamento () {
		return getPropertyValue(this, tipoDataCalcCredenciamento, PROP_TIPO_DATA_CALC_CREDENCIAMENTO); 
	}

	/**
	 * Set the value related to the column: tp_database_calc_credenciamento
	 * @param tipoDataCalcCredenciamento the tp_database_calc_credenciamento value
	 */
	public void setTipoDataCalcCredenciamento (java.lang.Long tipoDataCalcCredenciamento) {
//        java.lang.Long tipoDataCalcCredenciamentoOld = this.tipoDataCalcCredenciamento;
		this.tipoDataCalcCredenciamento = tipoDataCalcCredenciamento;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDataCalcCredenciamento", tipoDataCalcCredenciamentoOld, tipoDataCalcCredenciamento);
	}



	/**
	 * Return the value associated with the column: alvara_inicial_anos_validade
	 */
	public java.lang.Long getAlvaraInicialAnosValidade () {
		return getPropertyValue(this, alvaraInicialAnosValidade, PROP_ALVARA_INICIAL_ANOS_VALIDADE); 
	}

	/**
	 * Set the value related to the column: alvara_inicial_anos_validade
	 * @param alvaraInicialAnosValidade the alvara_inicial_anos_validade value
	 */
	public void setAlvaraInicialAnosValidade (java.lang.Long alvaraInicialAnosValidade) {
//        java.lang.Long alvaraInicialAnosValidadeOld = this.alvaraInicialAnosValidade;
		this.alvaraInicialAnosValidade = alvaraInicialAnosValidade;
//        this.getPropertyChangeSupport().firePropertyChange ("alvaraInicialAnosValidade", alvaraInicialAnosValidadeOld, alvaraInicialAnosValidade);
	}



	/**
	 * Return the value associated with the column: revalidacao_alvara_anos_validade
	 */
	public java.lang.Long getRevalidacaoAlvaraAnosValidade () {
		return getPropertyValue(this, revalidacaoAlvaraAnosValidade, PROP_REVALIDACAO_ALVARA_ANOS_VALIDADE); 
	}

	/**
	 * Set the value related to the column: revalidacao_alvara_anos_validade
	 * @param revalidacaoAlvaraAnosValidade the revalidacao_alvara_anos_validade value
	 */
	public void setRevalidacaoAlvaraAnosValidade (java.lang.Long revalidacaoAlvaraAnosValidade) {
//        java.lang.Long revalidacaoAlvaraAnosValidadeOld = this.revalidacaoAlvaraAnosValidade;
		this.revalidacaoAlvaraAnosValidade = revalidacaoAlvaraAnosValidade;
//        this.getPropertyChangeSupport().firePropertyChange ("revalidacaoAlvaraAnosValidade", revalidacaoAlvaraAnosValidadeOld, revalidacaoAlvaraAnosValidade);
	}



	/**
	 * Return the value associated with the column: autorizacao_sanitaria_anos_validade
	 */
	public java.lang.Long getAutorizacaoSanitariaAnosValidade () {
		return getPropertyValue(this, autorizacaoSanitariaAnosValidade, PROP_AUTORIZACAO_SANITARIA_ANOS_VALIDADE); 
	}

	/**
	 * Set the value related to the column: autorizacao_sanitaria_anos_validade
	 * @param autorizacaoSanitariaAnosValidade the autorizacao_sanitaria_anos_validade value
	 */
	public void setAutorizacaoSanitariaAnosValidade (java.lang.Long autorizacaoSanitariaAnosValidade) {
//        java.lang.Long autorizacaoSanitariaAnosValidadeOld = this.autorizacaoSanitariaAnosValidade;
		this.autorizacaoSanitariaAnosValidade = autorizacaoSanitariaAnosValidade;
//        this.getPropertyChangeSupport().firePropertyChange ("autorizacaoSanitariaAnosValidade", autorizacaoSanitariaAnosValidadeOld, autorizacaoSanitariaAnosValidade);
	}



	/**
	 * Return the value associated with the column: credenciamento_treinamento_anos_validade
	 */
	public java.lang.Long getCredenciamentoTreinamentoAnosValidade () {
		return getPropertyValue(this, credenciamentoTreinamentoAnosValidade, PROP_CREDENCIAMENTO_TREINAMENTO_ANOS_VALIDADE); 
	}

	/**
	 * Set the value related to the column: credenciamento_treinamento_anos_validade
	 * @param credenciamentoTreinamentoAnosValidade the credenciamento_treinamento_anos_validade value
	 */
	public void setCredenciamentoTreinamentoAnosValidade (java.lang.Long credenciamentoTreinamentoAnosValidade) {
//        java.lang.Long credenciamentoTreinamentoAnosValidadeOld = this.credenciamentoTreinamentoAnosValidade;
		this.credenciamentoTreinamentoAnosValidade = credenciamentoTreinamentoAnosValidade;
//        this.getPropertyChangeSupport().firePropertyChange ("credenciamentoTreinamentoAnosValidade", credenciamentoTreinamentoAnosValidadeOld, credenciamentoTreinamentoAnosValidade);
	}



	/**
	 * Return the value associated with the column: licenca_transporte_anos_validade
	 */
	public java.lang.Long getLicencaTransporteAnosValidade () {
		return getPropertyValue(this, licencaTransporteAnosValidade, PROP_LICENCA_TRANSPORTE_ANOS_VALIDADE); 
	}

	/**
	 * Set the value related to the column: licenca_transporte_anos_validade
	 * @param licencaTransporteAnosValidade the licenca_transporte_anos_validade value
	 */
	public void setLicencaTransporteAnosValidade (java.lang.Long licencaTransporteAnosValidade) {
//        java.lang.Long licencaTransporteAnosValidadeOld = this.licencaTransporteAnosValidade;
		this.licencaTransporteAnosValidade = licencaTransporteAnosValidade;
//        this.getPropertyChangeSupport().firePropertyChange ("licencaTransporteAnosValidade", licencaTransporteAnosValidadeOld, licencaTransporteAnosValidade);
	}



	/**
	 * Return the value associated with the column: num_termo_ajustamento_conduta
	 */
	public java.lang.Long getNumTermoAjustamentoConduta () {
		return getPropertyValue(this, numTermoAjustamentoConduta, PROP_NUM_TERMO_AJUSTAMENTO_CONDUTA); 
	}

	/**
	 * Set the value related to the column: num_termo_ajustamento_conduta
	 * @param numTermoAjustamentoConduta the num_termo_ajustamento_conduta value
	 */
	public void setNumTermoAjustamentoConduta (java.lang.Long numTermoAjustamentoConduta) {
//        java.lang.Long numTermoAjustamentoCondutaOld = this.numTermoAjustamentoConduta;
		this.numTermoAjustamentoConduta = numTermoAjustamentoConduta;
//        this.getPropertyChangeSupport().firePropertyChange ("numTermoAjustamentoConduta", numTermoAjustamentoCondutaOld, numTermoAjustamentoConduta);
	}



	/**
	 * Return the value associated with the column: clausulas_gerais_termo_ajustamento_conduta
	 */
	public java.lang.String getClausulasGeraisTermoAjustamentoConduta () {
		return getPropertyValue(this, clausulasGeraisTermoAjustamentoConduta, PROP_CLAUSULAS_GERAIS_TERMO_AJUSTAMENTO_CONDUTA); 
	}

	/**
	 * Set the value related to the column: clausulas_gerais_termo_ajustamento_conduta
	 * @param clausulasGeraisTermoAjustamentoConduta the clausulas_gerais_termo_ajustamento_conduta value
	 */
	public void setClausulasGeraisTermoAjustamentoConduta (java.lang.String clausulasGeraisTermoAjustamentoConduta) {
//        java.lang.String clausulasGeraisTermoAjustamentoCondutaOld = this.clausulasGeraisTermoAjustamentoConduta;
		this.clausulasGeraisTermoAjustamentoConduta = clausulasGeraisTermoAjustamentoConduta;
//        this.getPropertyChangeSupport().firePropertyChange ("clausulasGeraisTermoAjustamentoConduta", clausulasGeraisTermoAjustamentoCondutaOld, clausulasGeraisTermoAjustamentoConduta);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCadastro () {
		return getPropertyValue(this, usuarioCadastro, PROP_USUARIO_CADASTRO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuarioCadastro the cd_usuario value
	 */
	public void setUsuarioCadastro (br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCadastroOld = this.usuarioCadastro;
		this.usuarioCadastro = usuarioCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadastro", usuarioCadastroOld, usuarioCadastro);
	}



	/**
	 * Return the value associated with the column: cd_usuario_alteracao
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioAlteracao () {
		return getPropertyValue(this, usuarioAlteracao, PROP_USUARIO_ALTERACAO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_alteracao
	 * @param usuarioAlteracao the cd_usuario_alteracao value
	 */
	public void setUsuarioAlteracao (br.com.ksisolucoes.vo.controle.Usuario usuarioAlteracao) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioAlteracaoOld = this.usuarioAlteracao;
		this.usuarioAlteracao = usuarioAlteracao;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioAlteracao", usuarioAlteracaoOld, usuarioAlteracao);
	}



	/**
	 * Return the value associated with the column: atividade_padrao_usuario_externo
	 */
	public br.com.ksisolucoes.vo.basico.Atividade getAtividadePadraoUsuarioExterno () {
		return getPropertyValue(this, atividadePadraoUsuarioExterno, PROP_ATIVIDADE_PADRAO_USUARIO_EXTERNO); 
	}

	/**
	 * Set the value related to the column: atividade_padrao_usuario_externo
	 * @param atividadePadraoUsuarioExterno the atividade_padrao_usuario_externo value
	 */
	public void setAtividadePadraoUsuarioExterno (br.com.ksisolucoes.vo.basico.Atividade atividadePadraoUsuarioExterno) {
//        br.com.ksisolucoes.vo.basico.Atividade atividadePadraoUsuarioExternoOld = this.atividadePadraoUsuarioExterno;
		this.atividadePadraoUsuarioExterno = atividadePadraoUsuarioExterno;
//        this.getPropertyChangeSupport().firePropertyChange ("atividadePadraoUsuarioExterno", atividadePadraoUsuarioExternoOld, atividadePadraoUsuarioExterno);
	}



	/**
	 * Return the value associated with the column: empresa_padrao_usuario_externo
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresaPadraoUsuarioExterno () {
		return getPropertyValue(this, empresaPadraoUsuarioExterno, PROP_EMPRESA_PADRAO_USUARIO_EXTERNO); 
	}

	/**
	 * Set the value related to the column: empresa_padrao_usuario_externo
	 * @param empresaPadraoUsuarioExterno the empresa_padrao_usuario_externo value
	 */
	public void setEmpresaPadraoUsuarioExterno (br.com.ksisolucoes.vo.basico.Empresa empresaPadraoUsuarioExterno) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaPadraoUsuarioExternoOld = this.empresaPadraoUsuarioExterno;
		this.empresaPadraoUsuarioExterno = empresaPadraoUsuarioExterno;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaPadraoUsuarioExterno", empresaPadraoUsuarioExternoOld, empresaPadraoUsuarioExterno);
	}



	/**
	 * Return the value associated with the column: cd_ger_arq_logo_cabecalho
	 */
	public br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo getLogoCabecalhoRelatorio () {
		return getPropertyValue(this, logoCabecalhoRelatorio, PROP_LOGO_CABECALHO_RELATORIO); 
	}

	/**
	 * Set the value related to the column: cd_ger_arq_logo_cabecalho
	 * @param logoCabecalhoRelatorio the cd_ger_arq_logo_cabecalho value
	 */
	public void setLogoCabecalhoRelatorio (br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoCabecalhoRelatorio) {
//        br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoCabecalhoRelatorioOld = this.logoCabecalhoRelatorio;
		this.logoCabecalhoRelatorio = logoCabecalhoRelatorio;
//        this.getPropertyChangeSupport().firePropertyChange ("logoCabecalhoRelatorio", logoCabecalhoRelatorioOld, logoCabecalhoRelatorio);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: cd_setor_vigilancia_receita
	 */
	public br.com.ksisolucoes.vo.vigilancia.SetorVigilancia getSetorVigilanciaReceita () {
		return getPropertyValue(this, setorVigilanciaReceita, PROP_SETOR_VIGILANCIA_RECEITA); 
	}

	/**
	 * Set the value related to the column: cd_setor_vigilancia_receita
	 * @param setorVigilanciaReceita the cd_setor_vigilancia_receita value
	 */
	public void setSetorVigilanciaReceita (br.com.ksisolucoes.vo.vigilancia.SetorVigilancia setorVigilanciaReceita) {
//        br.com.ksisolucoes.vo.vigilancia.SetorVigilancia setorVigilanciaReceitaOld = this.setorVigilanciaReceita;
		this.setorVigilanciaReceita = setorVigilanciaReceita;
//        this.getPropertyChangeSupport().firePropertyChange ("setorVigilanciaReceita", setorVigilanciaReceitaOld, setorVigilanciaReceita);
	}



	/**
	 * Return the value associated with the column: cd_setor_vigilancia_defesa_previa
	 */
	public br.com.ksisolucoes.vo.vigilancia.SetorVigilancia getSetorVigilanciaDefesaPrevia () {
		return getPropertyValue(this, setorVigilanciaDefesaPrevia, PROP_SETOR_VIGILANCIA_DEFESA_PREVIA); 
	}

	/**
	 * Set the value related to the column: cd_setor_vigilancia_defesa_previa
	 * @param setorVigilanciaDefesaPrevia the cd_setor_vigilancia_defesa_previa value
	 */
	public void setSetorVigilanciaDefesaPrevia (br.com.ksisolucoes.vo.vigilancia.SetorVigilancia setorVigilanciaDefesaPrevia) {
//        br.com.ksisolucoes.vo.vigilancia.SetorVigilancia setorVigilanciaDefesaPreviaOld = this.setorVigilanciaDefesaPrevia;
		this.setorVigilanciaDefesaPrevia = setorVigilanciaDefesaPrevia;
//        this.getPropertyChangeSupport().firePropertyChange ("setorVigilanciaDefesaPrevia", setorVigilanciaDefesaPreviaOld, setorVigilanciaDefesaPrevia);
	}



	/**
	 * Return the value associated with the column: cd_setor_vigilancia_receita_sec
	 */
	public br.com.ksisolucoes.vo.vigilancia.SetorVigilancia getSetorVigilanciaReceitaSecundario () {
		return getPropertyValue(this, setorVigilanciaReceitaSecundario, PROP_SETOR_VIGILANCIA_RECEITA_SECUNDARIO); 
	}

	/**
	 * Set the value related to the column: cd_setor_vigilancia_receita_sec
	 * @param setorVigilanciaReceitaSecundario the cd_setor_vigilancia_receita_sec value
	 */
	public void setSetorVigilanciaReceitaSecundario (br.com.ksisolucoes.vo.vigilancia.SetorVigilancia setorVigilanciaReceitaSecundario) {
//        br.com.ksisolucoes.vo.vigilancia.SetorVigilancia setorVigilanciaReceitaSecundarioOld = this.setorVigilanciaReceitaSecundario;
		this.setorVigilanciaReceitaSecundario = setorVigilanciaReceitaSecundario;
//        this.getPropertyChangeSupport().firePropertyChange ("setorVigilanciaReceitaSecundario", setorVigilanciaReceitaSecundarioOld, setorVigilanciaReceitaSecundario);
	}



	/**
	 * Return the value associated with the column: cd_setor_vigilancia_analise_projetos
	 */
	public br.com.ksisolucoes.vo.vigilancia.SetorVigilancia getSetorVigilanciaAnaliseProjetos () {
		return getPropertyValue(this, setorVigilanciaAnaliseProjetos, PROP_SETOR_VIGILANCIA_ANALISE_PROJETOS); 
	}

	/**
	 * Set the value related to the column: cd_setor_vigilancia_analise_projetos
	 * @param setorVigilanciaAnaliseProjetos the cd_setor_vigilancia_analise_projetos value
	 */
	public void setSetorVigilanciaAnaliseProjetos (br.com.ksisolucoes.vo.vigilancia.SetorVigilancia setorVigilanciaAnaliseProjetos) {
//        br.com.ksisolucoes.vo.vigilancia.SetorVigilancia setorVigilanciaAnaliseProjetosOld = this.setorVigilanciaAnaliseProjetos;
		this.setorVigilanciaAnaliseProjetos = setorVigilanciaAnaliseProjetos;
//        this.getPropertyChangeSupport().firePropertyChange ("setorVigilanciaAnaliseProjetos", setorVigilanciaAnaliseProjetosOld, setorVigilanciaAnaliseProjetos);
	}



	/**
	 * Return the value associated with the column: cd_ger_arq_brasao
	 */
	public br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo getBrasaoMarcaDagua () {
		return getPropertyValue(this, brasaoMarcaDagua, PROP_BRASAO_MARCA_DAGUA); 
	}

	/**
	 * Set the value related to the column: cd_ger_arq_brasao
	 * @param brasaoMarcaDagua the cd_ger_arq_brasao value
	 */
	public void setBrasaoMarcaDagua (br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo brasaoMarcaDagua) {
//        br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo brasaoMarcaDaguaOld = this.brasaoMarcaDagua;
		this.brasaoMarcaDagua = brasaoMarcaDagua;
//        this.getPropertyChangeSupport().firePropertyChange ("brasaoMarcaDagua", brasaoMarcaDaguaOld, brasaoMarcaDagua);
	}



	/**
	 * Return the value associated with the column: cd_setor_vigi_geral_denuncia
	 */
	public br.com.ksisolucoes.vo.vigilancia.SetorVigilancia getSetorVigilanciaGeralDenuncia () {
		return getPropertyValue(this, setorVigilanciaGeralDenuncia, PROP_SETOR_VIGILANCIA_GERAL_DENUNCIA); 
	}

	/**
	 * Set the value related to the column: cd_setor_vigi_geral_denuncia
	 * @param setorVigilanciaGeralDenuncia the cd_setor_vigi_geral_denuncia value
	 */
	public void setSetorVigilanciaGeralDenuncia (br.com.ksisolucoes.vo.vigilancia.SetorVigilancia setorVigilanciaGeralDenuncia) {
//        br.com.ksisolucoes.vo.vigilancia.SetorVigilancia setorVigilanciaGeralDenunciaOld = this.setorVigilanciaGeralDenuncia;
		this.setorVigilanciaGeralDenuncia = setorVigilanciaGeralDenuncia;
//        this.getPropertyChangeSupport().firePropertyChange ("setorVigilanciaGeralDenuncia", setorVigilanciaGeralDenunciaOld, setorVigilanciaGeralDenuncia);
	}



	/**
	 * Return the value associated with the column: cd_ger_arq_logo_declaratorio
	 */
	public br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo getLogoRequerimentoDeclaratorio () {
		return getPropertyValue(this, logoRequerimentoDeclaratorio, PROP_LOGO_REQUERIMENTO_DECLARATORIO); 
	}

	/**
	 * Set the value related to the column: cd_ger_arq_logo_declaratorio
	 * @param logoRequerimentoDeclaratorio the cd_ger_arq_logo_declaratorio value
	 */
	public void setLogoRequerimentoDeclaratorio (br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoRequerimentoDeclaratorio) {
//        br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoRequerimentoDeclaratorioOld = this.logoRequerimentoDeclaratorio;
		this.logoRequerimentoDeclaratorio = logoRequerimentoDeclaratorio;
//        this.getPropertyChangeSupport().firePropertyChange ("logoRequerimentoDeclaratorio", logoRequerimentoDeclaratorioOld, logoRequerimentoDeclaratorio);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia configuracaoVigilancia = (br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia) obj;
			if (null == this.getCodigo() || null == configuracaoVigilancia.getCodigo()) return false;
			else return (this.getCodigo().equals(configuracaoVigilancia.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}