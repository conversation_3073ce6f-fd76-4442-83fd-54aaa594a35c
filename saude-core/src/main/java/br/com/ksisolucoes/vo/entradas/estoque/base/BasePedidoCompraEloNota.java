package br.com.ksisolucoes.vo.entradas.estoque.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the ordem_compra_elo_nota table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="ordem_compra_elo_nota"
 */

public abstract class BasePedidoCompraEloNota extends BaseRootVO implements Serializable {

	public static String REF = "PedidoCompraEloNota";
	public static final String PROP_PEDIDO_COMPRA_ITEM = "pedidoCompraItem";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_REGISTRO_ITEM_NOTA_FISCAL = "registroItemNotaFiscal";
	public static final String PROP_QUANTIDADE = "quantidade";


	// constructors
	public BasePedidoCompraEloNota () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BasePedidoCompraEloNota (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BasePedidoCompraEloNota (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal registroItemNotaFiscal,
		br.com.ksisolucoes.vo.materiais.pedidocompra.PedidoCompraItem pedidoCompraItem,
		java.math.BigDecimal quantidade) {

		this.setCodigo(codigo);
		this.setRegistroItemNotaFiscal(registroItemNotaFiscal);
		this.setPedidoCompraItem(pedidoCompraItem);
		this.setQuantidade(quantidade);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.math.BigDecimal quantidade;

	// many to one
	private br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal registroItemNotaFiscal;
	private br.com.ksisolucoes.vo.materiais.pedidocompra.PedidoCompraItem pedidoCompraItem;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_elo_nota_pedido_compra"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: quantidade
	 */
	public java.math.BigDecimal getQuantidade () {
		return getPropertyValue(this, quantidade, PROP_QUANTIDADE); 
	}

	/**
	 * Set the value related to the column: quantidade
	 * @param quantidade the quantidade value
	 */
	public void setQuantidade (java.math.BigDecimal quantidade) {
//        java.math.BigDecimal quantidadeOld = this.quantidade;
		this.quantidade = quantidade;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidade", quantidadeOld, quantidade);
	}



	/**
	 * Return the value associated with the column: cd_reg_it_nf
	 */
	public br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal getRegistroItemNotaFiscal () {
		return getPropertyValue(this, registroItemNotaFiscal, PROP_REGISTRO_ITEM_NOTA_FISCAL); 
	}

	/**
	 * Set the value related to the column: cd_reg_it_nf
	 * @param registroItemNotaFiscal the cd_reg_it_nf value
	 */
	public void setRegistroItemNotaFiscal (br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal registroItemNotaFiscal) {
//        br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal registroItemNotaFiscalOld = this.registroItemNotaFiscal;
		this.registroItemNotaFiscal = registroItemNotaFiscal;
//        this.getPropertyChangeSupport().firePropertyChange ("registroItemNotaFiscal", registroItemNotaFiscalOld, registroItemNotaFiscal);
	}



	/**
	 * Return the value associated with the column: cd_pedido_compra_item
	 */
	public br.com.ksisolucoes.vo.materiais.pedidocompra.PedidoCompraItem getPedidoCompraItem () {
		return getPropertyValue(this, pedidoCompraItem, PROP_PEDIDO_COMPRA_ITEM); 
	}

	/**
	 * Set the value related to the column: cd_pedido_compra_item
	 * @param pedidoCompraItem the cd_pedido_compra_item value
	 */
	public void setPedidoCompraItem (br.com.ksisolucoes.vo.materiais.pedidocompra.PedidoCompraItem pedidoCompraItem) {
//        br.com.ksisolucoes.vo.materiais.pedidocompra.PedidoCompraItem pedidoCompraItemOld = this.pedidoCompraItem;
		this.pedidoCompraItem = pedidoCompraItem;
//        this.getPropertyChangeSupport().firePropertyChange ("pedidoCompraItem", pedidoCompraItemOld, pedidoCompraItem);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.entradas.estoque.PedidoCompraEloNota)) return false;
		else {
			br.com.ksisolucoes.vo.entradas.estoque.PedidoCompraEloNota pedidoCompraEloNota = (br.com.ksisolucoes.vo.entradas.estoque.PedidoCompraEloNota) obj;
			if (null == this.getCodigo() || null == pedidoCompraEloNota.getCodigo()) return false;
			else return (this.getCodigo().equals(pedidoCompraEloNota.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}