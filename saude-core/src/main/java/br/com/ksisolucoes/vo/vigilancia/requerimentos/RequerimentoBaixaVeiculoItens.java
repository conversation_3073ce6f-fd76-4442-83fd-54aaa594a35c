package br.com.ksisolucoes.vo.vigilancia.requerimentos;

import java.io.Serializable;

import br.com.ksisolucoes.vo.vigilancia.requerimentos.base.BaseRequerimentoBaixaVeiculoItens;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class RequerimentoBaixaVeiculoItens extends BaseRequerimentoBaixaVeiculoItens implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequerimentoBaixaVeiculoItens () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequerimentoBaixaVeiculoItens (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequerimentoBaixaVeiculoItens (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoBaixaVeiculo requerimentoBaixaVeiculo,
		br.com.ksisolucoes.vo.vigilancia.VeiculoEstabelecimento veiculoEstabelecimento) {

		super (
			codigo,
			requerimentoBaixaVeiculo,
			veiculoEstabelecimento);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}