package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;

import br.com.ksisolucoes.vo.prontuario.basico.base.BaseAtendimentoMedicamento;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class AtendimentoMedicamento extends BaseAtendimentoMedicamento implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public AtendimentoMedicamento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public AtendimentoMedicamento (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public AtendimentoMedicamento (
		java.lang.Long codigo,
		java.util.Date dataHoraAplicacao) {

		super (
			codigo,
			dataHoraAplicacao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}