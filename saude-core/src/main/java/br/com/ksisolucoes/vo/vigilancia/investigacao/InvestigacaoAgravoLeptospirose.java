package br.com.ksisolucoes.vo.vigilancia.investigacao;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.base.BaseInvestigacaoAgravoLeptospirose;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


public class InvestigacaoAgravoLeptospirose extends BaseInvestigacaoAgravoLeptospirose implements CodigoManager {
    public InvestigacaoAgravoLeptospirose () { super(); }

    public InvestigacaoAgravoLeptospirose (Long codigo) {
        super(codigo);
    }

    public InvestigacaoAgravoLeptospirose (
            Long codigo,
            RegistroAgravo registroAgravo,
            TabelaCbo tabelaCbo,
            String flagInformacoesComplementares
    ) {
        super (
            codigo,
            registroAgravo,
            tabelaCbo,
            flagInformacoesComplementares
        );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public void setCodigoManager(Serializable key) {
        this.setCodigo((Long) key);
    }

    public static InvestigacaoAgravoLeptospirose buscaPorRegistroAgravo(RegistroAgravo registroAgravo) {
        InvestigacaoAgravoLeptospirose investigacao =
                LoadManager.getInstance(InvestigacaoAgravoLeptospirose.class)
                        .addProperties(new HQLProperties(InvestigacaoAgravoLeptospirose.class).getProperties())
                        .addProperties(new HQLProperties(
                                RegistroAgravo.class,
                                VOUtils.montarPath(InvestigacaoAgravoLeptospirose.PROP_REGISTRO_AGRAVO)).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(
                                VOUtils.montarPath(InvestigacaoAgravoLeptospirose.PROP_REGISTRO_AGRAVO, RegistroAgravo.PROP_CODIGO),
                                registroAgravo.getCodigo()))
                        .start().getVO();
        return investigacao;
    }

    public List<InvestigacaoAgravoLeptospiroseLocalSituacaoRisco> getLocaisSituacaoRiscoByInvestigacaoLeptospirose() {
        if (getCodigo() != null) {
            List<InvestigacaoAgravoLeptospiroseLocalSituacaoRisco> list =
                    LoadManager.getInstance(InvestigacaoAgravoLeptospiroseLocalSituacaoRisco.class)
                            .addProperties(new HQLProperties(InvestigacaoAgravoLeptospiroseLocalSituacaoRisco.class).getProperties())
                            .addProperty(VOUtils.montarPath(InvestigacaoAgravoLeptospiroseLocalSituacaoRisco.PROP_CIDADE_LOCAL_SITUACAO_RISCO, Cidade.PROP_ESTADO, Estado.PROP_SIGLA))
                            .addProperties(new HQLProperties(
                                    InvestigacaoAgravoLeptospirose.class,
                                    VOUtils.montarPath(InvestigacaoAgravoLeptospiroseLocalSituacaoRisco.PROP_INVESTIGACAO_AGRAVO_LEPTOSPIROSE)).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(
                                    VOUtils.montarPath(InvestigacaoAgravoLeptospiroseLocalSituacaoRisco.PROP_INVESTIGACAO_AGRAVO_LEPTOSPIROSE, InvestigacaoAgravoLeptospirose.PROP_CODIGO),
                                    getCodigo()))
                            .start().getList();
            return list;
        }
        return new ArrayList<InvestigacaoAgravoLeptospiroseLocalSituacaoRisco>();
    }

}
