package br.com.ksisolucoes.vo.programasaude.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the hiperdia_medicamento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="hiperdia_medicamento"
 */

public abstract class BaseHiperdiaMedicamento extends BaseRootVO implements Serializable {

	public static String REF = "HiperdiaMedicamento";
	public static final String PROP_HIPERDIA = "hiperdia";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_PRODUTO = "produto";
	public static final String PROP_QUANTIDADE = "quantidade";


	// constructors
	public BaseHiperdiaMedicamento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseHiperdiaMedicamento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long quantidade;

	// many to one
	private br.com.ksisolucoes.vo.programasaude.Hiperdia hiperdia;
	private br.com.ksisolucoes.vo.entradas.estoque.Produto produto;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_hiperdia_medicamento"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: qtdade
	 */
	public java.lang.Long getQuantidade () {
		return getPropertyValue(this, quantidade, PROP_QUANTIDADE); 
	}

	/**
	 * Set the value related to the column: qtdade
	 * @param quantidade the qtdade value
	 */
	public void setQuantidade (java.lang.Long quantidade) {
//        java.lang.Long quantidadeOld = this.quantidade;
		this.quantidade = quantidade;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidade", quantidadeOld, quantidade);
	}



	/**
	 * Return the value associated with the column: nr_hiperdia
	 */
	public br.com.ksisolucoes.vo.programasaude.Hiperdia getHiperdia () {
		return getPropertyValue(this, hiperdia, PROP_HIPERDIA); 
	}

	/**
	 * Set the value related to the column: nr_hiperdia
	 * @param hiperdia the nr_hiperdia value
	 */
	public void setHiperdia (br.com.ksisolucoes.vo.programasaude.Hiperdia hiperdia) {
//        br.com.ksisolucoes.vo.programasaude.Hiperdia hiperdiaOld = this.hiperdia;
		this.hiperdia = hiperdia;
//        this.getPropertyChangeSupport().firePropertyChange ("hiperdia", hiperdiaOld, hiperdia);
	}



	/**
	 * Return the value associated with the column: cod_pro
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Produto getProduto () {
		return getPropertyValue(this, produto, PROP_PRODUTO); 
	}

	/**
	 * Set the value related to the column: cod_pro
	 * @param produto the cod_pro value
	 */
	public void setProduto (br.com.ksisolucoes.vo.entradas.estoque.Produto produto) {
//        br.com.ksisolucoes.vo.entradas.estoque.Produto produtoOld = this.produto;
		this.produto = produto;
//        this.getPropertyChangeSupport().firePropertyChange ("produto", produtoOld, produto);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.programasaude.HiperdiaMedicamento)) return false;
		else {
			br.com.ksisolucoes.vo.programasaude.HiperdiaMedicamento hiperdiaMedicamento = (br.com.ksisolucoes.vo.programasaude.HiperdiaMedicamento) obj;
			if (null == this.getCodigo() || null == hiperdiaMedicamento.getCodigo()) return false;
			else return (this.getCodigo().equals(hiperdiaMedicamento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}