package br.com.ksisolucoes.vo.integracao.cadsus.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the importacao_xml_cadsus table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="importacao_xml_cadsus"
 */

public abstract class BaseImportacaoXmlCadsus extends BaseRootVO implements Serializable {

	public static String REF = "ImportacaoXmlCadsus";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_XML = "xml";
	public static final String PROP_DATA_IMPORTACAO = "dataImportacao";
	public static final String PROP_TOTAL_ENTIDADES = "totalEntidades";
	public static final String PROP_TOTAL_USUARIOS = "totalUsuarios";
	public static final String PROP_USUARIOS_IMPORTADOS = "usuariosImportados";
	public static final String PROP_ENTIDADES_SALVAS = "entidadesSalvas";


	// constructors
	public BaseImportacaoXmlCadsus () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseImportacaoXmlCadsus (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseImportacaoXmlCadsus (
		java.lang.Long codigo,
		java.util.Date dataImportacao) {

		this.setCodigo(codigo);
		this.setDataImportacao(dataImportacao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataImportacao;
	private java.lang.Long totalUsuarios;
	private java.lang.Long usuariosImportados;
	private java.lang.Long totalEntidades;
	private java.lang.Long entidadesSalvas;
	private java.lang.String xml;

	// many to one
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="codigo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_importacao
	 */
	public java.util.Date getDataImportacao () {
		return getPropertyValue(this, dataImportacao, PROP_DATA_IMPORTACAO); 
	}

	/**
	 * Set the value related to the column: dt_importacao
	 * @param dataImportacao the dt_importacao value
	 */
	public void setDataImportacao (java.util.Date dataImportacao) {
//        java.util.Date dataImportacaoOld = this.dataImportacao;
		this.dataImportacao = dataImportacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataImportacao", dataImportacaoOld, dataImportacao);
	}



	/**
	 * Return the value associated with the column: total_usuarios
	 */
	public java.lang.Long getTotalUsuarios () {
		return getPropertyValue(this, totalUsuarios, PROP_TOTAL_USUARIOS); 
	}

	/**
	 * Set the value related to the column: total_usuarios
	 * @param totalUsuarios the total_usuarios value
	 */
	public void setTotalUsuarios (java.lang.Long totalUsuarios) {
//        java.lang.Long totalUsuariosOld = this.totalUsuarios;
		this.totalUsuarios = totalUsuarios;
//        this.getPropertyChangeSupport().firePropertyChange ("totalUsuarios", totalUsuariosOld, totalUsuarios);
	}



	/**
	 * Return the value associated with the column: usuarios_importados
	 */
	public java.lang.Long getUsuariosImportados () {
		return getPropertyValue(this, usuariosImportados, PROP_USUARIOS_IMPORTADOS); 
	}

	/**
	 * Set the value related to the column: usuarios_importados
	 * @param usuariosImportados the usuarios_importados value
	 */
	public void setUsuariosImportados (java.lang.Long usuariosImportados) {
//        java.lang.Long usuariosImportadosOld = this.usuariosImportados;
		this.usuariosImportados = usuariosImportados;
//        this.getPropertyChangeSupport().firePropertyChange ("usuariosImportados", usuariosImportadosOld, usuariosImportados);
	}



	/**
	 * Return the value associated with the column: total_entidades
	 */
	public java.lang.Long getTotalEntidades () {
		return getPropertyValue(this, totalEntidades, PROP_TOTAL_ENTIDADES); 
	}

	/**
	 * Set the value related to the column: total_entidades
	 * @param totalEntidades the total_entidades value
	 */
	public void setTotalEntidades (java.lang.Long totalEntidades) {
//        java.lang.Long totalEntidadesOld = this.totalEntidades;
		this.totalEntidades = totalEntidades;
//        this.getPropertyChangeSupport().firePropertyChange ("totalEntidades", totalEntidadesOld, totalEntidades);
	}



	/**
	 * Return the value associated with the column: entidades_salvas
	 */
	public java.lang.Long getEntidadesSalvas () {
		return getPropertyValue(this, entidadesSalvas, PROP_ENTIDADES_SALVAS); 
	}

	/**
	 * Set the value related to the column: entidades_salvas
	 * @param entidadesSalvas the entidades_salvas value
	 */
	public void setEntidadesSalvas (java.lang.Long entidadesSalvas) {
//        java.lang.Long entidadesSalvasOld = this.entidadesSalvas;
		this.entidadesSalvas = entidadesSalvas;
//        this.getPropertyChangeSupport().firePropertyChange ("entidadesSalvas", entidadesSalvasOld, entidadesSalvas);
	}



	/**
	 * Return the value associated with the column: xml
	 */
	public java.lang.String getXml () {
		return getPropertyValue(this, xml, PROP_XML); 
	}

	/**
	 * Set the value related to the column: xml
	 * @param xml the xml value
	 */
	public void setXml (java.lang.String xml) {
//        java.lang.String xmlOld = this.xml;
		this.xml = xml;
//        this.getPropertyChangeSupport().firePropertyChange ("xml", xmlOld, xml);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.integracao.cadsus.ImportacaoXmlCadsus)) return false;
		else {
			br.com.ksisolucoes.vo.integracao.cadsus.ImportacaoXmlCadsus importacaoXmlCadsus = (br.com.ksisolucoes.vo.integracao.cadsus.ImportacaoXmlCadsus) obj;
			if (null == this.getCodigo() || null == importacaoXmlCadsus.getCodigo()) return false;
			else return (this.getCodigo().equals(importacaoXmlCadsus.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}