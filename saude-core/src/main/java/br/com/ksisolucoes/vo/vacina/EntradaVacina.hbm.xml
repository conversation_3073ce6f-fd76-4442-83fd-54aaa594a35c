<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vacina"  >
	<class name="EntradaVacina" table="entrada_vacina" >

        <id
            column="cd_ent_vacina"
            name="codigo"
            type="java.lang.Long"
            >
            <generator class="assigned" />
        </id> <version column="version" name="version" type="long" />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.basico.Empresa"
            column="empresa"
            name="empresa"
            not-null="true"
        />
        
        <property
            column="id_nota"
            name="nota"
            type="java.lang.String"
            not-null="true"
        />
        
        <property
            column="dt_cadastro"
            name="dataCadastro"
            type="java.util.Date"
            not-null="true"
        />
        
        <property
            column="dt_portaria"
            name="dataPortaria"
            type="java.util.Date"
            not-null="true"
        />
        
        <property
            column="dt_emissao"
            name="dataEmissao"
            type="java.util.Date"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuario"
            not-null="true"
        />
        
        <property
            column="dt_usuario"
            name="dataUsuario"
            type="java.util.Date"
            not-null="true"
        />

        <property
            column="status"
            name="status"
            type="java.lang.Long"
            not-null="true"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usu_confirmacao"
            name="usuarioConfirmacao"
            not-null="false"
        />

        <property
            column="dt_confirmacao"
            name="dataConfirmacao"
            type="java.util.Date"
            not-null="false"
        />

        <property
            column="vl_total"
            name="valorTotal"
            type="java.lang.Double"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.basico.Pessoa"
            column="cod_fornecedor"
            name="fornecedor"
            not-null="true"
        />
        
        <property
            column="observacao"
            name="observacao"
            type="java.lang.String"
            length="200"
            not-null="false"
        />
        
	</class>
</hibernate-mapping>
