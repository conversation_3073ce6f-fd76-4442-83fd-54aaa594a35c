<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.investigacao">

    <class name="InvestigacaoAgravoDoencaTrabalhoCancer" table="investigacao_agr_cancer">

        <id name="codigo"
            type="java.lang.Long"
            column="cd_invest_agr_cancer" >
            <generator class="sequence">
                <param name="sequence">seq_investigacao_agr_cancer</param>
            </generator>
        </id>

        <version column="version" name="version" type="long"/>

        <property
                name="flagInformacoesComplementares"
                column="flag_informacoes_complementares"
                type="java.lang.String"
                not-null="true"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo"
                name="registroAgravo"
                not-null="true"
                column="cd_registro_agravo"
        />

        <!-- INVESTIGAÇÃO -->
        <property
                name="dataInvestigacao"
                column="dt_investigacao"
                type="java.util.Date"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"
                name="ocupacaoCbo"
                not-null="false"
                column="ocupacao_cbo"
        />

        <!-- ANTECEDENTES EPIDEMIOLOGICOS -->
        <property
            name="situacaoMercadoTrabalho"
            column="tp_situacao_mercado_trabalho"
            type="java.lang.Long"
        />
        <property
                name="tempoTrabalhoOcupacao"
                column="tempo_trabalho_ocupacao"
                type="java.lang.String"
                length="2"
        />
        <property
                name="tempoTrabalhoOcupacaoUnidadeMedida"
                column="tempo_trabalho_ocupacao_um"
                type="java.lang.Long"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Empresa"
                column="cd_empresa_contratante"
                name="empresaContratante"
                not-null="false"
        />

        <property
                name="empresaDistrito"
                column="empresa_distrito"
                type="java.lang.String"
        />
        <property
                name="empresaPontoReferencia"
                column="empresa_ponto_referencia"
                type="java.lang.String"
        />
        <property
                name="empresaTelefone"
                column="empresa_telefone"
                type="java.lang.String"
        />
        <property
                name="empresaTerceirizada"
                column="sn_empresa_terceirizada"
                type="java.lang.Long"
        />

        <!-- CANCER -->

        <property
                name="tempoExposicaoAgenteRisco"
                column="tempo_exposicao_agente_risco"
                type="java.lang.String"
        />
        <property
                name="tempoExposicaoAgenteRiscoUnidadeMedida"
                column="tempo_exposicao_agente_risco_um"
                type="java.lang.Long"
        />
        <property
                name="regimeTratamento"
                column="tp_regime_tratamento"
                type="java.lang.Long"
        />

        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.Cid"
                     name="diagnosticoEspecifico">
            <column name="diagnostico_especifico" />
        </many-to-one>

        <property
                name="exposicaoAsbesto"
                column="exposicao_asbesto"
                type="java.lang.Long"
        />
        <property
                name="exposicaoSilicaLivre"
                column="exposicao_silica_livre"
                type="java.lang.Long"
        />
        <property
                name="exposicaoAminasAromaticas"
                column="exposicao_aminas_aromaticas"
                type="java.lang.Long"
        />
        <property
                name="exposicaoBenzeno"
                column="exposicao_benzeno"
                type="java.lang.Long"
        />
        <property
                name="exposicaoAlcatrao"
                column="exposicao_alcatrao"
                type="java.lang.Long"
        />
        <property
                name="exposicaoHidrocarbonetos"
                column="exposicao_hidrocarbonetos"
                type="java.lang.Long"
        />
        <property
                name="exposicaoOleosMinerais"
                column="exposicao_oleos_minerais"
                type="java.lang.Long"
        />
        <property
                name="exposicaoBerilio"
                column="exposicao_berilio"
                type="java.lang.Long"
        />
        <property
                name="exposicaoCadmioCompostos"
                column="exposicao_cadmio_compostos"
                type="java.lang.Long"
        />
        <property
                name="exposicaoCromoCompostos"
                column="exposicao_cromo_compostos"
                type="java.lang.Long"
        />
        <property
                name="exposicaoCompostosNiquel"
                column="exposicao_compostos_niquel"
                type="java.lang.Long"
        />
        <property
                name="exposicaoRadiacoesIonizantes"
                column="exposicao_radiacoes_ionizantes"
                type="java.lang.Long"
        />
        <property
                name="exposicaoRadiacoesNaoIonizantes"
                column="exposicao_radiacoes_nao_ionizantes"
                type="java.lang.Long"
        />
        <property
                name="exposicaoHormonios"
                column="exposicao_hormonios"
                type="java.lang.Long"
        />
        <property
                name="exposicaoAntineoplasticos"
                column="exposicao_antineoplasticos"
                type="java.lang.Long"
        />
        <property
                name="exposicaoOutros"
                column="exposicao_outros"
                type="java.lang.String"
                length="100"
        />

        <property
                name="habitoFumar"
                column="habito_fumar"
                type="java.lang.Long"
        />
        <property
                name="tempoExposicaoTabaco"
                column="tempo_exposicao_tabaco"
                type="java.lang.String"
        />
        <property
                name="tempoExposicaoTabacoUnidadeMedida"
                column="tempo_exposicao_tabaco_um"
                type="java.lang.Long"
        />

        <!-- CONCLUSÃO -->
        <property
                name="outrosTrabalhadoresMesmaDoenca"
                column="outros_trabalhadores_mesma_doenca"
                type="java.lang.Long"
        />
        <property
                name="evolucaoCaso"
                column="evolucao_caso"
                type="java.lang.Long"
        />
        <property
                name="dataObito"
                column="dt_obito"
                type="java.util.Date"
        />
        <property
                name="emitidaCat"
                column="emitida_cat"
                type="java.lang.Long"
        />

        <!-- OBS -->
        <property
                name="observacao"
                column="observacao"
                type="java.lang.String"
                length="5000"
        />

        <!-- ENCERRAMENTO -->
        <property
                name="dataEncerramento"
                column="dt_encerramento"
                type="java.util.Date"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                name="usuarioEncerramento"
                not-null="false"
                column="cd_usuario_encerramento"
        />
    </class>
</hibernate-mapping>