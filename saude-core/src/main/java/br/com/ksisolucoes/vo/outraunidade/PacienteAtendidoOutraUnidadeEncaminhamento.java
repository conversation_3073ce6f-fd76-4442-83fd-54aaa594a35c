package br.com.ksisolucoes.vo.outraunidade;

import java.io.Serializable;

import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.outraunidade.base.BasePacienteAtendidoOutraUnidadeEncaminhamento;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class PacienteAtendidoOutraUnidadeEncaminhamento extends BasePacienteAtendidoOutraUnidadeEncaminhamento implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public PacienteAtendidoOutraUnidadeEncaminhamento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public PacienteAtendidoOutraUnidadeEncaminhamento (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public PacienteAtendidoOutraUnidadeEncaminhamento (
		java.lang.Long codigo,
		java.lang.String descricao,
		java.util.Date dataCadastro) {

		super (
			codigo,
			descricao,
			dataCadastro);
	}

/*[CONSTRUCTOR MARKER END]*/

	public String getDataCadastroFormatada() {
		return Data.formatar(getDataCadastro());
	}

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}