package br.com.ksisolucoes.vo.prontuario.procedimento.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;


public abstract class BaseProcedimentoCboPK extends BaseRootVO implements Serializable {

	protected int hashCode = Integer.MIN_VALUE;

	public static String PROP_PROCEDIMENTO_COMPETENCIA = "procedimentoCompetencia";
	public static String PROP_TABELA_CBO = "tabelaCbo";

	private br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetencia;
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCbo;


	public BaseProcedimentoCboPK () {}
	
	public BaseProcedimentoCboPK (
		br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetencia,
		br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCbo) {

		this.setProcedimentoCompetencia(procedimentoCompetencia);
		this.setTabelaCbo(tabelaCbo);
	}


	/**
	 * Return the value associated with the column: dt_competencia
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia getProcedimentoCompetencia () {
		return getPropertyValue(this, procedimentoCompetencia, PROP_PROCEDIMENTO_COMPETENCIA); 
	}

	/**
	 * Set the value related to the column: dt_competencia
	 * @param procedimentoCompetencia the dt_competencia value
	 */
	public void setProcedimentoCompetencia (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetencia) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetenciaOld = this.procedimentoCompetencia;
		this.procedimentoCompetencia = procedimentoCompetencia;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimentoCompetencia", procedimentoCompetenciaOld, procedimentoCompetencia);
	}



	/**
	 * Return the value associated with the column: cd_cbo
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getTabelaCbo () {
		return getPropertyValue(this, tabelaCbo, PROP_TABELA_CBO); 
	}

	/**
	 * Set the value related to the column: cd_cbo
	 * @param tabelaCbo the cd_cbo value
	 */
	public void setTabelaCbo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCbo) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCboOld = this.tabelaCbo;
		this.tabelaCbo = tabelaCbo;
//        this.getPropertyChangeSupport().firePropertyChange ("tabelaCbo", tabelaCboOld, tabelaCbo);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCboPK)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCboPK mObj = (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCboPK) obj;
			if (null != this.getProcedimentoCompetencia() && null != mObj.getProcedimentoCompetencia()) {
				if (!this.getProcedimentoCompetencia().equals(mObj.getProcedimentoCompetencia())) {
					return false;
				}
			}
			else {
				return false;
			}
			if (null != this.getTabelaCbo() && null != mObj.getTabelaCbo()) {
				if (!this.getTabelaCbo().equals(mObj.getTabelaCbo())) {
					return false;
				}
			}
			else {
				return false;
			}
			return true;
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			StringBuilder sb = new StringBuilder();
			if (null != this.getProcedimentoCompetencia()) {
				sb.append(this.getProcedimentoCompetencia().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			if (null != this.getTabelaCbo()) {
				sb.append(this.getTabelaCbo().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			this.hashCode = sb.toString().hashCode();
		}
		return this.hashCode;
	}

    private java.beans.PropertyChangeSupport propertyChangeSupport;

    protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
        if( this.propertyChangeSupport == null ) {
            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
        }
        return this.propertyChangeSupport;
    }

    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.addPropertyChangeListener(l);
    }

    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
		propertyChangeSupport.addPropertyChangeListener(propertyName, listener);
    }

    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.removePropertyChangeListener(l);
    }
}