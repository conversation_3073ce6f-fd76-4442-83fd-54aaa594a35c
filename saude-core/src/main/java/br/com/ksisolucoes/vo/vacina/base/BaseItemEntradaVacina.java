package br.com.ksisolucoes.vo.vacina.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the item_entrada_vacina table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="item_entrada_vacina"
 */

public abstract class BaseItemEntradaVacina extends BaseRootVO implements Serializable {

	public static String REF = "ItemEntradaVacina";
	public static final String PROP_VALOR_UNITARIO = "valorUnitario";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_LOTE = "lote";
	public static final String PROP_PRODUTO = "produto";
	public static final String PROP_QUANTIDADE_ENTRADA = "quantidadeEntrada";
	public static final String PROP_ENTRADA_VACINA = "entradaVacina";
	public static final String PROP_DATA_VALIDADE = "dataValidade";
	public static final String PROP_VALOR_TOTAL = "valorTotal";


	// constructors
	public BaseItemEntradaVacina () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseItemEntradaVacina (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseItemEntradaVacina (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vacina.EntradaVacina entradaVacina,
		br.com.ksisolucoes.vo.entradas.estoque.Produto produto,
		java.util.Date dataCadastro,
		java.lang.Long quantidadeEntrada,
		java.lang.Double valorTotal,
		java.lang.Double valorUnitario) {

		this.setCodigo(codigo);
		this.setEntradaVacina(entradaVacina);
		this.setProduto(produto);
		this.setDataCadastro(dataCadastro);
		this.setQuantidadeEntrada(quantidadeEntrada);
		this.setValorTotal(valorTotal);
		this.setValorUnitario(valorUnitario);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataCadastro;
	private java.lang.Long quantidadeEntrada;
	private java.lang.Double valorTotal;
	private java.lang.Double valorUnitario;
	private java.util.Date dataValidade;
	private java.lang.String lote;

	// many to one
	private br.com.ksisolucoes.vo.vacina.EntradaVacina entradaVacina;
	private br.com.ksisolucoes.vo.entradas.estoque.Produto produto;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_it_ent_vacina"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: qt_entrada
	 */
	public java.lang.Long getQuantidadeEntrada () {
		return getPropertyValue(this, quantidadeEntrada, PROP_QUANTIDADE_ENTRADA); 
	}

	/**
	 * Set the value related to the column: qt_entrada
	 * @param quantidadeEntrada the qt_entrada value
	 */
	public void setQuantidadeEntrada (java.lang.Long quantidadeEntrada) {
//        java.lang.Long quantidadeEntradaOld = this.quantidadeEntrada;
		this.quantidadeEntrada = quantidadeEntrada;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeEntrada", quantidadeEntradaOld, quantidadeEntrada);
	}



	/**
	 * Return the value associated with the column: vl_total
	 */
	public java.lang.Double getValorTotal () {
		return getPropertyValue(this, valorTotal, PROP_VALOR_TOTAL); 
	}

	/**
	 * Set the value related to the column: vl_total
	 * @param valorTotal the vl_total value
	 */
	public void setValorTotal (java.lang.Double valorTotal) {
//        java.lang.Double valorTotalOld = this.valorTotal;
		this.valorTotal = valorTotal;
//        this.getPropertyChangeSupport().firePropertyChange ("valorTotal", valorTotalOld, valorTotal);
	}



	/**
	 * Return the value associated with the column: vl_unitario
	 */
	public java.lang.Double getValorUnitario () {
		return getPropertyValue(this, valorUnitario, PROP_VALOR_UNITARIO); 
	}

	/**
	 * Set the value related to the column: vl_unitario
	 * @param valorUnitario the vl_unitario value
	 */
	public void setValorUnitario (java.lang.Double valorUnitario) {
//        java.lang.Double valorUnitarioOld = this.valorUnitario;
		this.valorUnitario = valorUnitario;
//        this.getPropertyChangeSupport().firePropertyChange ("valorUnitario", valorUnitarioOld, valorUnitario);
	}



	/**
	 * Return the value associated with the column: dt_validade
	 */
	public java.util.Date getDataValidade () {
		return getPropertyValue(this, dataValidade, PROP_DATA_VALIDADE); 
	}

	/**
	 * Set the value related to the column: dt_validade
	 * @param dataValidade the dt_validade value
	 */
	public void setDataValidade (java.util.Date dataValidade) {
//        java.util.Date dataValidadeOld = this.dataValidade;
		this.dataValidade = dataValidade;
//        this.getPropertyChangeSupport().firePropertyChange ("dataValidade", dataValidadeOld, dataValidade);
	}



	/**
	 * Return the value associated with the column: lote
	 */
	public java.lang.String getLote () {
		return getPropertyValue(this, lote, PROP_LOTE); 
	}

	/**
	 * Set the value related to the column: lote
	 * @param lote the lote value
	 */
	public void setLote (java.lang.String lote) {
//        java.lang.String loteOld = this.lote;
		this.lote = lote;
//        this.getPropertyChangeSupport().firePropertyChange ("lote", loteOld, lote);
	}



	/**
	 * Return the value associated with the column: cd_ent_vacina
	 */
	public br.com.ksisolucoes.vo.vacina.EntradaVacina getEntradaVacina () {
		return getPropertyValue(this, entradaVacina, PROP_ENTRADA_VACINA); 
	}

	/**
	 * Set the value related to the column: cd_ent_vacina
	 * @param entradaVacina the cd_ent_vacina value
	 */
	public void setEntradaVacina (br.com.ksisolucoes.vo.vacina.EntradaVacina entradaVacina) {
//        br.com.ksisolucoes.vo.vacina.EntradaVacina entradaVacinaOld = this.entradaVacina;
		this.entradaVacina = entradaVacina;
//        this.getPropertyChangeSupport().firePropertyChange ("entradaVacina", entradaVacinaOld, entradaVacina);
	}



	/**
	 * Return the value associated with the column: cod_pro
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Produto getProduto () {
		return getPropertyValue(this, produto, PROP_PRODUTO); 
	}

	/**
	 * Set the value related to the column: cod_pro
	 * @param produto the cod_pro value
	 */
	public void setProduto (br.com.ksisolucoes.vo.entradas.estoque.Produto produto) {
//        br.com.ksisolucoes.vo.entradas.estoque.Produto produtoOld = this.produto;
		this.produto = produto;
//        this.getPropertyChangeSupport().firePropertyChange ("produto", produtoOld, produto);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vacina.ItemEntradaVacina)) return false;
		else {
			br.com.ksisolucoes.vo.vacina.ItemEntradaVacina itemEntradaVacina = (br.com.ksisolucoes.vo.vacina.ItemEntradaVacina) obj;
			if (null == this.getCodigo() || null == itemEntradaVacina.getCodigo()) return false;
			else return (this.getCodigo().equals(itemEntradaVacina.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}