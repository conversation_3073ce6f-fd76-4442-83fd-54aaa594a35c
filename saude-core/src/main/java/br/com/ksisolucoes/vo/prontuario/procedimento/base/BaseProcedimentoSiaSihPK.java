package br.com.ksisolucoes.vo.prontuario.procedimento.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;


public abstract class BaseProcedimentoSiaSihPK extends BaseRootVO implements Serializable {

	protected int hashCode = Integer.MIN_VALUE;

	public static String PROP_PROCEDIMENTO_COMPETENCIA = "procedimentoCompetencia";
	public static String PROP_PROCEDIMENTO_SIA_SIH_CADASTRO = "procedimentoSiaSihCadastro";

	private br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetencia;
	private br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoSiaSihCadastro procedimentoSiaSihCadastro;


	public BaseProcedimentoSiaSihPK () {}
	
	public BaseProcedimentoSiaSihPK (
		br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetencia,
		br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoSiaSihCadastro procedimentoSiaSihCadastro) {

		this.setProcedimentoCompetencia(procedimentoCompetencia);
		this.setProcedimentoSiaSihCadastro(procedimentoSiaSihCadastro);
	}


	/**
	 * Return the value associated with the column: dt_competencia
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia getProcedimentoCompetencia () {
		return getPropertyValue(this, procedimentoCompetencia, PROP_PROCEDIMENTO_COMPETENCIA); 
	}

	/**
	 * Set the value related to the column: dt_competencia
	 * @param procedimentoCompetencia the dt_competencia value
	 */
	public void setProcedimentoCompetencia (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetencia) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetenciaOld = this.procedimentoCompetencia;
		this.procedimentoCompetencia = procedimentoCompetencia;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimentoCompetencia", procedimentoCompetenciaOld, procedimentoCompetencia);
	}



	/**
	 * Return the value associated with the column: tp_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoSiaSihCadastro getProcedimentoSiaSihCadastro () {
		return getPropertyValue(this, procedimentoSiaSihCadastro, PROP_PROCEDIMENTO_SIA_SIH_CADASTRO); 
	}

	/**
	 * Set the value related to the column: tp_procedimento
	 * @param procedimentoSiaSihCadastro the tp_procedimento value
	 */
	public void setProcedimentoSiaSihCadastro (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoSiaSihCadastro procedimentoSiaSihCadastro) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoSiaSihCadastro procedimentoSiaSihCadastroOld = this.procedimentoSiaSihCadastro;
		this.procedimentoSiaSihCadastro = procedimentoSiaSihCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimentoSiaSihCadastro", procedimentoSiaSihCadastroOld, procedimentoSiaSihCadastro);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoSiaSihPK)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoSiaSihPK mObj = (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoSiaSihPK) obj;
			if (null != this.getProcedimentoCompetencia() && null != mObj.getProcedimentoCompetencia()) {
				if (!this.getProcedimentoCompetencia().equals(mObj.getProcedimentoCompetencia())) {
					return false;
				}
			}
			else {
				return false;
			}
			if (null != this.getProcedimentoSiaSihCadastro() && null != mObj.getProcedimentoSiaSihCadastro()) {
				if (!this.getProcedimentoSiaSihCadastro().equals(mObj.getProcedimentoSiaSihCadastro())) {
					return false;
				}
			}
			else {
				return false;
			}
			return true;
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			StringBuilder sb = new StringBuilder();
			if (null != this.getProcedimentoCompetencia()) {
				sb.append(this.getProcedimentoCompetencia().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			if (null != this.getProcedimentoSiaSihCadastro()) {
				sb.append(this.getProcedimentoSiaSihCadastro().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			this.hashCode = sb.toString().hashCode();
		}
		return this.hashCode;
	}

    private java.beans.PropertyChangeSupport propertyChangeSupport;

    protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
        if( this.propertyChangeSupport == null ) {
            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
        }
        return this.propertyChangeSupport;
    }

    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.addPropertyChangeListener(l);
    }

    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
		propertyChangeSupport.addPropertyChangeListener(propertyName, listener);
    }

    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.removePropertyChangeListener(l);
    }
}