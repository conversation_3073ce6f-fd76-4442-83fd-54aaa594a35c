package br.com.ksisolucoes.vo.prontuario.procedimento.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the tabela_preco_procedimento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="tabela_preco_procedimento"
 */

public abstract class BaseTabelaPrecoProcedimento extends BaseRootVO implements Serializable {

	public static String REF = "TabelaPrecoProcedimento";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_PRECO = "preco";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_USUARIO = "dataUsuario";
	public static final String PROP_CONVENIO = "convenio";
	public static final String PROP_PROCEDIMENTO = "procedimento";


	// constructors
	public BaseTabelaPrecoProcedimento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseTabelaPrecoProcedimento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseTabelaPrecoProcedimento (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento,
		br.com.ksisolucoes.vo.prontuario.basico.Convenio convenio,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.Double preco,
		java.util.Date dataUsuario) {

		this.setCodigo(codigo);
		this.setProcedimento(procedimento);
		this.setConvenio(convenio);
		this.setUsuario(usuario);
		this.setPreco(preco);
		this.setDataUsuario(dataUsuario);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Double preco;
	private java.util.Date dataUsuario;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento;
	private br.com.ksisolucoes.vo.prontuario.basico.Convenio convenio;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_tab_preco_proced"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: preco
	 */
	public java.lang.Double getPreco () {
		return getPropertyValue(this, preco, PROP_PRECO); 
	}

	/**
	 * Set the value related to the column: preco
	 * @param preco the preco value
	 */
	public void setPreco (java.lang.Double preco) {
//        java.lang.Double precoOld = this.preco;
		this.preco = preco;
//        this.getPropertyChangeSupport().firePropertyChange ("preco", precoOld, preco);
	}



	/**
	 * Return the value associated with the column: dt_usuario
	 */
	public java.util.Date getDataUsuario () {
		return getPropertyValue(this, dataUsuario, PROP_DATA_USUARIO); 
	}

	/**
	 * Set the value related to the column: dt_usuario
	 * @param dataUsuario the dt_usuario value
	 */
	public void setDataUsuario (java.util.Date dataUsuario) {
//        java.util.Date dataUsuarioOld = this.dataUsuario;
		this.dataUsuario = dataUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsuario", dataUsuarioOld, dataUsuario);
	}



	/**
	 * Return the value associated with the column: cd_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento getProcedimento () {
		return getPropertyValue(this, procedimento, PROP_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_procedimento
	 * @param procedimento the cd_procedimento value
	 */
	public void setProcedimento (br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoOld = this.procedimento;
		this.procedimento = procedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimento", procedimentoOld, procedimento);
	}



	/**
	 * Return the value associated with the column: cd_convenio
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Convenio getConvenio () {
		return getPropertyValue(this, convenio, PROP_CONVENIO); 
	}

	/**
	 * Set the value related to the column: cd_convenio
	 * @param convenio the cd_convenio value
	 */
	public void setConvenio (br.com.ksisolucoes.vo.prontuario.basico.Convenio convenio) {
//        br.com.ksisolucoes.vo.prontuario.basico.Convenio convenioOld = this.convenio;
		this.convenio = convenio;
//        this.getPropertyChangeSupport().firePropertyChange ("convenio", convenioOld, convenio);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.procedimento.TabelaPrecoProcedimento)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.procedimento.TabelaPrecoProcedimento tabelaPrecoProcedimento = (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaPrecoProcedimento) obj;
			if (null == this.getCodigo() || null == tabelaPrecoProcedimento.getCodigo()) return false;
			else return (this.getCodigo().equals(tabelaPrecoProcedimento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}