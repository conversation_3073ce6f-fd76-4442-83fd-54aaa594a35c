<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.atendimento">
    <class name="AtendimentoNotificacao" table="atendimento_notificacao">
        <id name="codigo"
            column="cd_atendimento_notificacao"
            type="java.lang.Long">
            <generator class="assigned"/>
        </id>

        <version column="version" name="version" type="long"/>

        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.Cid"
                     name="cid" not-null="true">
            <column name="cd_cid"/>
        </many-to-one>

        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
                     name="atendimento" not-null="true">
            <column name="cd_atendimento"/>
        </many-to-one>

        <many-to-one name="usuario"
                class="br.com.ksisolucoes.vo.controle.Usuario"
                not-null="true">
            <column name="cd_usuario"/>
        </many-to-one>

        <many-to-one class="br.com.ksisolucoes.vo.basico.Empresa"
                     name="empresa" not-null="true">
            <column name="empresa"/>
        </many-to-one>

        <many-to-one class="br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo"
                     name="registroAgravo" not-null="false">
            <column name="cd_registro_agravo"/>
        </many-to-one>

        <property name="dataRegistro"
                  column="dt_registro"
                  type="java.util.Date"
                  not-null="true"
        />

        <property
                column="flag_registro_agravo_criado"
                name="flagRegistroAgravoCriado"
                type="java.lang.Long"
                not-null="true"
        />

    </class>
</hibernate-mapping>
