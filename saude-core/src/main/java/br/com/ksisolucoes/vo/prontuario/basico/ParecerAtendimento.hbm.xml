<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico">
    <class name="ParecerAtendimento" table="parecer_atendimento">
		
        <id name="codigo" 
            column="cd_parecer" 
            type="java.lang.Long"
         >
            <generator class="assigned"/>
        </id>
        
        <version column="version" name="version" type="long" />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
            name="atendimento"
            not-null="true"
        >
            <column name="nr_atendimento"/>
        </many-to-one>
        
        <many-to-one 
            class="br.com.ksisolucoes.vo.cadsus.Profissional"
            name="profissional"
        >
            <column name="cd_profissional" not-null="true" />
        </many-to-one>
        
        <many-to-one 
            class="br.com.ksisolucoes.vo.controle.Usuario"
            name="usuario"
        >
            <column name="cd_usuario" not-null="true" />
        </many-to-one>
        
        <property
            name="parecer"
            column="parecer"
            type="java.lang.String"
            not-null="true"
        />
        
        <property
            name="dataHistorico"
            column="dt_historico"
            type="java.util.Date"
            not-null="true"
        />
        
        <property
            name="dataRegistro"
            column="dt_registro"
            type="java.util.Date"
            not-null="true"
        />
        		
    </class>
</hibernate-mapping>