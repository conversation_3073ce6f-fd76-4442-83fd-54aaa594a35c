package br.com.ksisolucoes.vo.prontuario.procedimento.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;


public abstract class BaseProcedimentoPK extends BaseRootVO implements Serializable {

	protected int hashCode = Integer.MIN_VALUE;

	public static String PROP_CODIGO = "codigo";
	public static String PROP_DATA_COMPETENCIA = "dataCompetencia";

	private java.lang.Long codigo;
	private java.util.Date dataCompetencia;


	public BaseProcedimentoPK () {}
	
	public BaseProcedimentoPK (
		java.lang.Long codigo,
		java.util.Date dataCompetencia) {

		this.setCodigo(codigo);
		this.setDataCompetencia(dataCompetencia);
	}


	/**
	 * Return the value associated with the column: cd_procedimento
	 */
	public java.lang.Long getCodigo () {
		return getPropertyValue(this, codigo, PROP_CODIGO); 
	}

	/**
	 * Set the value related to the column: cd_procedimento
	 * @param codigo the cd_procedimento value
	 */
	public void setCodigo (java.lang.Long codigo) {
        java.lang.Long codigoOld = this.codigo;
		this.codigo = codigo;
        this.getPropertyChangeSupport().firePropertyChange ("codigo", codigoOld, codigo);
	}



	/**
	 * Return the value associated with the column: dt_competencia
	 */
	public java.util.Date getDataCompetencia () {
		return getPropertyValue(this, dataCompetencia, PROP_DATA_COMPETENCIA); 
	}

	/**
	 * Set the value related to the column: dt_competencia
	 * @param dataCompetencia the dt_competencia value
	 */
	public void setDataCompetencia (java.util.Date dataCompetencia) {
        java.util.Date dataCompetenciaOld = this.dataCompetencia;
		this.dataCompetencia = dataCompetencia;
        this.getPropertyChangeSupport().firePropertyChange ("dataCompetencia", dataCompetenciaOld, dataCompetencia);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoPK)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoPK mObj = (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoPK) obj;
			if (null != this.getCodigo() && null != mObj.getCodigo()) {
				if (!this.getCodigo().equals(mObj.getCodigo())) {
					return false;
				}
			}
			else {
				return false;
			}
			if (null != this.getDataCompetencia() && null != mObj.getDataCompetencia()) {
				if (!this.getDataCompetencia().equals(mObj.getDataCompetencia())) {
					return false;
				}
			}
			else {
				return false;
			}
			return true;
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			StringBuilder sb = new StringBuilder();
			if (null != this.getCodigo()) {
				sb.append(this.getCodigo().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			if (null != this.getDataCompetencia()) {
				sb.append(this.getDataCompetencia().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			this.hashCode = sb.toString().hashCode();
		}
		return this.hashCode;
	}

    private java.beans.PropertyChangeSupport propertyChangeSupport;

    protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
        if( this.propertyChangeSupport == null ) {
            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
        }
        return this.propertyChangeSupport;
    }

    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.addPropertyChangeListener(l);
    }

    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
		propertyChangeSupport.addPropertyChangeListener(propertyName, listener);
    }

    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.removePropertyChangeListener(l);
    }
}