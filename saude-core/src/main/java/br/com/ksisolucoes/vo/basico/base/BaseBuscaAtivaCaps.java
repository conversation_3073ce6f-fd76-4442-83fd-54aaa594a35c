package br.com.ksisolucoes.vo.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the busca_ativa_caps table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="busca_ativa_caps"
 */

public abstract class BaseBuscaAtivaCaps extends BaseRootVO implements Serializable {

	public static String REF = "BuscaAtivaCaps";
	public static final String PROP_STATUS = "status";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_DATA_ANDAMENTO = "dataAndamento";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_USUARIO_CADASTRO = "usuarioCadastro";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";
	public static final String PROP_DATA_OCORRENCIA = "dataOcorrencia";
	public static final String PROP_DATA_CONCLUSAO = "dataConclusao";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_PROFISSIONAL = "profissional";
	public static final String PROP_USUARIO_CONCLUSAO = "usuarioConclusao";


	// constructors
	public BaseBuscaAtivaCaps () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseBuscaAtivaCaps (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String observacao;
	private java.lang.Long status;
	private java.util.Date dataCadastro;
	private java.util.Date dataCancelamento;
	private java.util.Date dataConclusao;
	private java.util.Date dataAndamento;
	private java.util.Date dataOcorrencia;

	// many to one
	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissional;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioConclusao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_busca_ativa_caps"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: dt_conclusao
	 */
	public java.util.Date getDataConclusao () {
		return getPropertyValue(this, dataConclusao, PROP_DATA_CONCLUSAO); 
	}

	/**
	 * Set the value related to the column: dt_conclusao
	 * @param dataConclusao the dt_conclusao value
	 */
	public void setDataConclusao (java.util.Date dataConclusao) {
//        java.util.Date dataConclusaoOld = this.dataConclusao;
		this.dataConclusao = dataConclusao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataConclusao", dataConclusaoOld, dataConclusao);
	}



	/**
	 * Return the value associated with the column: dt_andamento
	 */
	public java.util.Date getDataAndamento () {
		return getPropertyValue(this, dataAndamento, PROP_DATA_ANDAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_andamento
	 * @param dataAndamento the dt_andamento value
	 */
	public void setDataAndamento (java.util.Date dataAndamento) {
//        java.util.Date dataAndamentoOld = this.dataAndamento;
		this.dataAndamento = dataAndamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAndamento", dataAndamentoOld, dataAndamento);
	}



	/**
	 * Return the value associated with the column: dt_ocorrencia
	 */
	public java.util.Date getDataOcorrencia () {
		return getPropertyValue(this, dataOcorrencia, PROP_DATA_OCORRENCIA); 
	}

	/**
	 * Set the value related to the column: dt_ocorrencia
	 * @param dataOcorrencia the dt_ocorrencia value
	 */
	public void setDataOcorrencia (java.util.Date dataOcorrencia) {
//        java.util.Date dataOcorrenciaOld = this.dataOcorrencia;
		this.dataOcorrencia = dataOcorrencia;
//        this.getPropertyChangeSupport().firePropertyChange ("dataOcorrencia", dataOcorrenciaOld, dataOcorrencia);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cd_profissional
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissional () {
		return getPropertyValue(this, profissional, PROP_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_profissional
	 * @param profissional the cd_profissional value
	 */
	public void setProfissional (br.com.ksisolucoes.vo.cadsus.Profissional profissional) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalOld = this.profissional;
		this.profissional = profissional;
//        this.getPropertyChangeSupport().firePropertyChange ("profissional", profissionalOld, profissional);
	}



	/**
	 * Return the value associated with the column: cd_usuario_cadastro
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCadastro () {
		return getPropertyValue(this, usuarioCadastro, PROP_USUARIO_CADASTRO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_cadastro
	 * @param usuarioCadastro the cd_usuario_cadastro value
	 */
	public void setUsuarioCadastro (br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCadastroOld = this.usuarioCadastro;
		this.usuarioCadastro = usuarioCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadastro", usuarioCadastroOld, usuarioCadastro);
	}



	/**
	 * Return the value associated with the column: cd_usuario_cancelamento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_cancelamento
	 * @param usuarioCancelamento the cd_usuario_cancelamento value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}



	/**
	 * Return the value associated with the column: cd_usuario_conclusao
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioConclusao () {
		return getPropertyValue(this, usuarioConclusao, PROP_USUARIO_CONCLUSAO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_conclusao
	 * @param usuarioConclusao the cd_usuario_conclusao value
	 */
	public void setUsuarioConclusao (br.com.ksisolucoes.vo.controle.Usuario usuarioConclusao) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioConclusaoOld = this.usuarioConclusao;
		this.usuarioConclusao = usuarioConclusao;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioConclusao", usuarioConclusaoOld, usuarioConclusao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.BuscaAtivaCaps)) return false;
		else {
			br.com.ksisolucoes.vo.basico.BuscaAtivaCaps buscaAtivaCaps = (br.com.ksisolucoes.vo.basico.BuscaAtivaCaps) obj;
			if (null == this.getCodigo() || null == buscaAtivaCaps.getCodigo()) return false;
			else return (this.getCodigo().equals(buscaAtivaCaps.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}