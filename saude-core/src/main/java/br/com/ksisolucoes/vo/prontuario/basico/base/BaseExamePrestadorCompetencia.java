package br.com.ksisolucoes.vo.prontuario.basico.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the exame_prestador_competencia table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="exame_prestador_competencia"
 */

public abstract class BaseExamePrestadorCompetencia extends BaseRootVO implements Serializable {

	public static String REF = "ExamePrestadorCompetencia";
	public static final String PROP_TETO_FINANCEIRO = "tetoFinanceiro";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TETO_RECURSO_PROPRIO_REALIZADO = "tetoRecursoProprioRealizado";
	public static final String PROP_TIPO_EXAME = "tipoExame";
	public static final String PROP_TETO_RECURSO_PROPRIO = "tetoRecursoProprio";
	public static final String PROP_TETO_FINANCEIRO_REALIZADO = "tetoFinanceiroRealizado";
	public static final String PROP_VALOR_EXTRA_COTA = "ValorExtraCota";
	public static final String PROP_DATA_COMPETENCIA = "dataCompetencia";


	// constructors
	public BaseExamePrestadorCompetencia () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseExamePrestadorCompetencia (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseExamePrestadorCompetencia (
		java.lang.Long codigo,
		java.util.Date dataCompetencia,
		java.lang.Double tetoFinanceiro) {

		this.setCodigo(codigo);
		this.setDataCompetencia(dataCompetencia);
		this.setTetoFinanceiro(tetoFinanceiro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataCompetencia;
	private java.lang.Double tetoFinanceiro;
	private java.lang.Double tetoFinanceiroRealizado;
	private java.lang.Double valorExtraCota;
	private java.lang.Double tetoRecursoProprio;
	private java.lang.Double tetoRecursoProprioRealizado;

	// many to one
	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private br.com.ksisolucoes.vo.prontuario.basico.TipoExame tipoExame;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_prestador_competencia"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_competencia
	 */
	public java.util.Date getDataCompetencia () {
		return getPropertyValue(this, dataCompetencia, PROP_DATA_COMPETENCIA); 
	}

	/**
	 * Set the value related to the column: dt_competencia
	 * @param dataCompetencia the dt_competencia value
	 */
	public void setDataCompetencia (java.util.Date dataCompetencia) {
//        java.util.Date dataCompetenciaOld = this.dataCompetencia;
		this.dataCompetencia = dataCompetencia;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCompetencia", dataCompetenciaOld, dataCompetencia);
	}



	/**
	 * Return the value associated with the column: teto_financeiro
	 */
	public java.lang.Double getTetoFinanceiro () {
		return getPropertyValue(this, tetoFinanceiro, PROP_TETO_FINANCEIRO); 
	}

	/**
	 * Set the value related to the column: teto_financeiro
	 * @param tetoFinanceiro the teto_financeiro value
	 */
	public void setTetoFinanceiro (java.lang.Double tetoFinanceiro) {
//        java.lang.Double tetoFinanceiroOld = this.tetoFinanceiro;
		this.tetoFinanceiro = tetoFinanceiro;
//        this.getPropertyChangeSupport().firePropertyChange ("tetoFinanceiro", tetoFinanceiroOld, tetoFinanceiro);
	}



	/**
	 * Return the value associated with the column: teto_financeiro_realizado
	 */
	public java.lang.Double getTetoFinanceiroRealizado () {
		return getPropertyValue(this, tetoFinanceiroRealizado, PROP_TETO_FINANCEIRO_REALIZADO); 
	}

	/**
	 * Set the value related to the column: teto_financeiro_realizado
	 * @param tetoFinanceiroRealizado the teto_financeiro_realizado value
	 */
	public void setTetoFinanceiroRealizado (java.lang.Double tetoFinanceiroRealizado) {
//        java.lang.Double tetoFinanceiroRealizadoOld = this.tetoFinanceiroRealizado;
		this.tetoFinanceiroRealizado = tetoFinanceiroRealizado;
//        this.getPropertyChangeSupport().firePropertyChange ("tetoFinanceiroRealizado", tetoFinanceiroRealizadoOld, tetoFinanceiroRealizado);
	}



	/**
	 * Return the value associated with the column: vl_extra_cota
	 */
	public java.lang.Double getValorExtraCota () {
		return getPropertyValue(this, valorExtraCota, PROP_VALOR_EXTRA_COTA); 
	}

	/**
	 * Set the value related to the column: vl_extra_cota
	 * @param valorExtraCota the vl_extra_cota value
	 */
	public void setValorExtraCota (java.lang.Double valorExtraCota) {
//        java.lang.Double valorExtraCotaOld = this.valorExtraCota;
		this.valorExtraCota = valorExtraCota;
//        this.getPropertyChangeSupport().firePropertyChange ("valorExtraCota", valorExtraCotaOld, valorExtraCota);
	}



	/**
	 * Return the value associated with the column: teto_recurso_proprio
	 */
	public java.lang.Double getTetoRecursoProprio () {
		return getPropertyValue(this, tetoRecursoProprio, PROP_TETO_RECURSO_PROPRIO); 
	}

	/**
	 * Set the value related to the column: teto_recurso_proprio
	 * @param tetoRecursoProprio the teto_recurso_proprio value
	 */
	public void setTetoRecursoProprio (java.lang.Double tetoRecursoProprio) {
//        java.lang.Double tetoRecursoProprioOld = this.tetoRecursoProprio;
		this.tetoRecursoProprio = tetoRecursoProprio;
//        this.getPropertyChangeSupport().firePropertyChange ("tetoRecursoProprio", tetoRecursoProprioOld, tetoRecursoProprio);
	}



	/**
	 * Return the value associated with the column: teto_recurso_proprio_realizado
	 */
	public java.lang.Double getTetoRecursoProprioRealizado () {
		return getPropertyValue(this, tetoRecursoProprioRealizado, PROP_TETO_RECURSO_PROPRIO_REALIZADO); 
	}

	/**
	 * Set the value related to the column: teto_recurso_proprio_realizado
	 * @param tetoRecursoProprioRealizado the teto_recurso_proprio_realizado value
	 */
	public void setTetoRecursoProprioRealizado (java.lang.Double tetoRecursoProprioRealizado) {
//        java.lang.Double tetoRecursoProprioRealizadoOld = this.tetoRecursoProprioRealizado;
		this.tetoRecursoProprioRealizado = tetoRecursoProprioRealizado;
//        this.getPropertyChangeSupport().firePropertyChange ("tetoRecursoProprioRealizado", tetoRecursoProprioRealizadoOld, tetoRecursoProprioRealizado);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: cd_tp_exame
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.TipoExame getTipoExame () {
		return getPropertyValue(this, tipoExame, PROP_TIPO_EXAME); 
	}

	/**
	 * Set the value related to the column: cd_tp_exame
	 * @param tipoExame the cd_tp_exame value
	 */
	public void setTipoExame (br.com.ksisolucoes.vo.prontuario.basico.TipoExame tipoExame) {
//        br.com.ksisolucoes.vo.prontuario.basico.TipoExame tipoExameOld = this.tipoExame;
		this.tipoExame = tipoExame;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoExame", tipoExameOld, tipoExame);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorCompetencia)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorCompetencia examePrestadorCompetencia = (br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorCompetencia) obj;
			if (null == this.getCodigo() || null == examePrestadorCompetencia.getCodigo()) return false;
			else return (this.getCodigo().equals(examePrestadorCompetencia.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}