<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.entradas.estoque">
    <class name="MedicamentoObmAmpp"
           table="medicamento_obm_ampp">
        <id
                column="cd_medicamento_obm_ampp"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="sequence">
                <param name="sequence">seq_medicamento_obm_ampp</param>
            </generator>
        </id>

        <version column="version" name="version" type="long"/>

        <property
                column="codigo_obm_ampp"
                name="obmAmpp"
                not-null="true"
                type="string"
        />

        <property
                column="descricao"
                name="descricao"
                type="string"
        />

        <property
                column="codigo_registro_sanitario_ampp"
                name="registroSanitarioAmpp"
                type="java.lang.Long"
        />

        <property
                column="flag_uso_exlusivo_hpt"
                name="usoExclusivoHpt"
                type="java.lang.Long"
        />

    </class>
</hibernate-mapping>
