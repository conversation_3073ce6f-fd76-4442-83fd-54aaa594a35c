package br.com.ksisolucoes.vo.saidas.devolucao;

import java.io.Serializable;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.saidas.devolucao.base.BaseEloDispensacaoDevolucao;



public class EloDispensacaoDevolucao extends BaseEloDispensacaoDevolucao implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EloDispensacaoDevolucao () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public EloDispensacaoDevolucao (java.lang.Long codigo) {
		super(codigo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}