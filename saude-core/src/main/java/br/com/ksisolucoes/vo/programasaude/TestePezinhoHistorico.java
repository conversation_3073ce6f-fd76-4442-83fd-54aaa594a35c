package br.com.ksisolucoes.vo.programasaude;

import java.io.Serializable;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.programasaude.base.BaseTestePezinhoHistorico;



public class TestePezinhoHistorico extends BaseTestePezinhoHistorico implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public TestePezinhoHistorico () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public TestePezinhoHistorico (br.com.ksisolucoes.vo.programasaude.TestePezinhoHistoricoPK id) {
		super(id);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setId( (br.com.ksisolucoes.vo.programasaude.TestePezinhoHistoricoPK)key );
    }

    public Serializable getCodigoManager() {
        return this.getId();
    }
}