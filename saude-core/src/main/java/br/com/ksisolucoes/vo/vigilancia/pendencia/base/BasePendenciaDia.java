package br.com.ksisolucoes.vo.vigilancia.pendencia.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the pendencia_dia table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="pendencia_dia"
 */

public abstract class BasePendenciaDia extends BaseRootVO implements Serializable {

	public static String REF = "PendenciaDia";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_CODIGO_ROTINA = "codigoRotina";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_SITUACAO = "situacao";
	public static final String PROP_DATA_PENDENCIA = "dataPendencia";
	public static final String PROP_DATA_FINALIZACAO = "dataFinalizacao";
	public static final String PROP_TIPO_ROTINA = "tipoRotina";


	// constructors
	public BasePendenciaDia () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BasePendenciaDia (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BasePendenciaDia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario) {

		this.setCodigo(codigo);
		this.setUsuario(usuario);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.lang.Long codigoRotina;
	private java.lang.Long tipoRotina;
	private java.util.Date dataPendencia;
	private java.lang.Long situacao;
	private java.util.Date dataFinalizacao;
	private java.util.Date dataCadastro;

	// many to one
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_pendencia_dia"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_pendencia
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_pendencia
	 * @param descricao the ds_pendencia value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: cd_rotina
	 */
	public java.lang.Long getCodigoRotina () {
		return getPropertyValue(this, codigoRotina, PROP_CODIGO_ROTINA); 
	}

	/**
	 * Set the value related to the column: cd_rotina
	 * @param codigoRotina the cd_rotina value
	 */
	public void setCodigoRotina (java.lang.Long codigoRotina) {
//        java.lang.Long codigoRotinaOld = this.codigoRotina;
		this.codigoRotina = codigoRotina;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoRotina", codigoRotinaOld, codigoRotina);
	}



	/**
	 * Return the value associated with the column: tp_rotina
	 */
	public java.lang.Long getTipoRotina () {
		return getPropertyValue(this, tipoRotina, PROP_TIPO_ROTINA); 
	}

	/**
	 * Set the value related to the column: tp_rotina
	 * @param tipoRotina the tp_rotina value
	 */
	public void setTipoRotina (java.lang.Long tipoRotina) {
//        java.lang.Long tipoRotinaOld = this.tipoRotina;
		this.tipoRotina = tipoRotina;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoRotina", tipoRotinaOld, tipoRotina);
	}



	/**
	 * Return the value associated with the column: dt_pendencia
	 */
	public java.util.Date getDataPendencia () {
		return getPropertyValue(this, dataPendencia, PROP_DATA_PENDENCIA); 
	}

	/**
	 * Set the value related to the column: dt_pendencia
	 * @param dataPendencia the dt_pendencia value
	 */
	public void setDataPendencia (java.util.Date dataPendencia) {
//        java.util.Date dataPendenciaOld = this.dataPendencia;
		this.dataPendencia = dataPendencia;
//        this.getPropertyChangeSupport().firePropertyChange ("dataPendencia", dataPendenciaOld, dataPendencia);
	}



	/**
	 * Return the value associated with the column: situacao
	 */
	public java.lang.Long getSituacao () {
		return getPropertyValue(this, situacao, PROP_SITUACAO); 
	}

	/**
	 * Set the value related to the column: situacao
	 * @param situacao the situacao value
	 */
	public void setSituacao (java.lang.Long situacao) {
//        java.lang.Long situacaoOld = this.situacao;
		this.situacao = situacao;
//        this.getPropertyChangeSupport().firePropertyChange ("situacao", situacaoOld, situacao);
	}



	/**
	 * Return the value associated with the column: dt_finalizacao
	 */
	public java.util.Date getDataFinalizacao () {
		return getPropertyValue(this, dataFinalizacao, PROP_DATA_FINALIZACAO); 
	}

	/**
	 * Set the value related to the column: dt_finalizacao
	 * @param dataFinalizacao the dt_finalizacao value
	 */
	public void setDataFinalizacao (java.util.Date dataFinalizacao) {
//        java.util.Date dataFinalizacaoOld = this.dataFinalizacao;
		this.dataFinalizacao = dataFinalizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataFinalizacao", dataFinalizacaoOld, dataFinalizacao);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.pendencia.PendenciaDia)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.pendencia.PendenciaDia pendenciaDia = (br.com.ksisolucoes.vo.vigilancia.pendencia.PendenciaDia) obj;
			if (null == this.getCodigo() || null == pendenciaDia.getCodigo()) return false;
			else return (this.getCodigo().equals(pendenciaDia.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}