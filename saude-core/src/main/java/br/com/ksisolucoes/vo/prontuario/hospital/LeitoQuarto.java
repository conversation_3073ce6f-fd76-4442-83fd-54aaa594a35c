package br.com.ksisolucoes.vo.prontuario.hospital;

import java.io.Serializable;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.EnumUtil;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import br.com.ksisolucoes.vo.prontuario.hospital.base.BaseLeitoQuarto;

public class LeitoQuarto extends BaseLeitoQuarto implements CodigoManager, PesquisaObjectInterface {

    private static final long serialVersionUID = 1L;
    public static final String PROP_SITUACAO_DESCRICAO = "situacaoDescricao";
    public static final String PROP_DESCRICAO_QUARTO = "descricaoQuarto";
    private String motivo;
    private Long situacaoAnterior;

    /**
     * Constructor for primary key
     */
    public LeitoQuarto(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public LeitoQuarto() {
        super();
    }

    public LeitoQuarto(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.prontuario.hospital.QuartoInternacao quartoInternacao,
            br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
            java.lang.String descricao,
            java.lang.Long situacao,
            java.util.Date dataCadastro,
            br.com.ksisolucoes.vo.hospital.datasus.sisaih.EspecialidadeLeito especialidadeLeito,
            br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {

        super(
                codigo,
                quartoInternacao,
                usuarioCadastro,
                especialidadeLeito,
                descricao,
                situacao,
                dataCadastro,
                usuarioCadsus);
    }

    /*[CONSTRUCTOR MARKER END]*/
    @Override
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    @Override
    public String getDescricaoVO() {
        return getDescricao();
    }

    @Override
    public String getIdentificador() {
        return getCodigo().toString();
    }

    public enum Sexo implements IEnum {
        AMBOS(0L, Bundle.getStringApplication("rotulo_ambos")),
        FEMININO(1L, Bundle.getStringApplication("f")),
        MASCULINO(2L, Bundle.getStringApplication("m"));

        private final Long value;
        private final String descricao;

        Sexo(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Sexo valueOf(Long value) {

            for (Sexo sexo : Sexo.values()) {
                if (sexo.value().equals(value)) {
                    return sexo;
                }
            }
            return null;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }
    public static enum TipoLeito implements IEnum<TipoLeito> {
        SUS(1L, Bundle.getStringApplication("sus")),
        CONVENIO(2L, Bundle.getStringApplication("convenio")),
        PARTICULAR(3L, Bundle.getStringApplication("particular"));

        private Long value;
        private String descricao;


        TipoLeito(Long value, String descricao){
            this.value = value;
            this.descricao = descricao;
        }

        public static TipoLeito valueOf(Long value) {
            for (TipoLeito tipoLeito : TipoLeito.values()) {
                if (tipoLeito.value().equals(value)) {
                    return tipoLeito;
                }
            }
            return null;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    public static enum Situacao implements IEnum<Situacao> {

        LIBERADO(0L, Bundle.getStringApplication("rotulo_liberado")),
        AGUARDANDO_LIMPEZA(1L, Bundle.getStringApplication("rotulo_aguardando_limpeza")),
        OCUPADO(2L, Bundle.getStringApplication("rotulo_ocupado")),
        DESATIVADO(3L, Bundle.getStringApplication("rotulo_desativado")),
        EXCLUIDO(4L, Bundle.getStringApplication("rotulo_excluido")),
        ISOLADO(5L, Bundle.getStringApplication("rotulo_isolado"));
        private Long value;
        private String descricao;

        private Situacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Situacao valeuOf(Long value) {
            for (Situacao situacao : Situacao.values()) {
                if (situacao.value().equals(value)) {
                    return situacao;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    public String getSituacaoDescricao() {
        return new EnumUtil().resolveDescricao(Situacao.values(), getSituacao());
    }

    @Override
    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getMotivo() {
        return motivo;
    }

    public void setMotivo(String motivo) {
        this.motivo = motivo;
    }

    public Long getSituacaoAnterior() {
        return situacaoAnterior;
    }
    @Override
    public void setSituacao(Long situacao) {
        if (situacaoAnterior == null) {
            situacaoAnterior = this.getSituacao();
        }
        super.setSituacao(situacao);
    }

    public String getDescricaoQuarto() {
        StringBuilder descricao = new StringBuilder();

        if (getQuartoInternacao() != null && getQuartoInternacao().getReferencia() != null) {
            descricao.append(getQuartoInternacao().getReferencia());
            if (getDescricao() != null) {
                descricao.append("-");
            }
        }

        if (getDescricao() != null) {
            descricao.append(getDescricao());
        }

        return descricao.toString();
    }

    public String getDescricaoTipoLeito() {
        TipoLeito tipoLeito = TipoLeito.valueOf(getTipoLeito());
        if(tipoLeito != null && tipoLeito.descricao != null) {
            return tipoLeito.descricao();
        }
        return "";
    }

    public String getDescricaoSexo() {
        Sexo sexo = Sexo.valueOf(getSexo());

        if(sexo != null && sexo.descricao != null) {
            return sexo.descricao();
        }
        return "";
    }
}
