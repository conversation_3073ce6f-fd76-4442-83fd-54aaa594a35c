package br.com.ksisolucoes.vo.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the cep_brasil table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="cep_brasil"
 */

public abstract class BaseCepBrasil extends BaseRootVO implements Serializable {

	public static String REF = "CepBrasil";
	public static final String PROP_ESTADO = "estado";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_CIDADE = "cidade";
	public static final String PROP_CEP = "cep";
	public static final String PROP_VERSION_ALL = "versionAll";


	// constructors
	public BaseCepBrasil () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseCepBrasil (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseCepBrasil (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Cidade cidade,
		br.com.ksisolucoes.vo.basico.Estado estado,
		java.lang.String cep) {

		this.setCodigo(codigo);
		this.setCidade(cidade);
		this.setEstado(estado);
		this.setCep(cep);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String cep;
	private java.lang.Long versionAll;

	// many to one
	private br.com.ksisolucoes.vo.basico.Cidade cidade;
	private br.com.ksisolucoes.vo.basico.Estado estado;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_cep"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cep
	 */
	public java.lang.String getCep () {
		return getPropertyValue(this, cep, PROP_CEP); 
	}

	/**
	 * Set the value related to the column: cep
	 * @param cep the cep value
	 */
	public void setCep (java.lang.String cep) {
//        java.lang.String cepOld = this.cep;
		this.cep = cep;
//        this.getPropertyChangeSupport().firePropertyChange ("cep", cepOld, cep);
	}



	/**
	 * Return the value associated with the column: cod_cid
	 */
	public br.com.ksisolucoes.vo.basico.Cidade getCidade () {
		return getPropertyValue(this, cidade, PROP_CIDADE); 
	}

	/**
	 * Set the value related to the column: cod_cid
	 * @param cidade the cod_cid value
	 */
	public void setCidade (br.com.ksisolucoes.vo.basico.Cidade cidade) {
//        br.com.ksisolucoes.vo.basico.Cidade cidadeOld = this.cidade;
		this.cidade = cidade;
//        this.getPropertyChangeSupport().firePropertyChange ("cidade", cidadeOld, cidade);
	}



	/**
	 * Return the value associated with the column: cod_est
	 */
	public br.com.ksisolucoes.vo.basico.Estado getEstado () {
		return getPropertyValue(this, estado, PROP_ESTADO); 
	}

	/**
	 * Set the value related to the column: cod_est
	 * @param estado the cod_est value
	 */
	public void setEstado (br.com.ksisolucoes.vo.basico.Estado estado) {
//        br.com.ksisolucoes.vo.basico.Estado estadoOld = this.estado;
		this.estado = estado;
//        this.getPropertyChangeSupport().firePropertyChange ("estado", estadoOld, estado);
	}



    /**
     * Return the value associated with the column: versionAll
     */
    public java.lang.Long getVersionAll () {
        return getPropertyValue(this, versionAll, PROP_VERSION_ALL);
    }

    /**
     * Set the value related to the column: versionAll
     * @param versionAll the versionAll value
     */
    public void setVersionAll (java.lang.Long versionAll) {
//        java.lang.String cepOld = this.versionAll;
        this.versionAll = versionAll;
//        this.getPropertyChangeSupport().firePropertyChange ("versionAll", versionAllOld, versionAll);
    }



	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.CepBrasil)) return false;
		else {
			br.com.ksisolucoes.vo.basico.CepBrasil cepBrasil = (br.com.ksisolucoes.vo.basico.CepBrasil) obj;
			if (null == this.getCodigo() || null == cepBrasil.getCodigo()) return false;
			else return (this.getCodigo().equals(cepBrasil.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}