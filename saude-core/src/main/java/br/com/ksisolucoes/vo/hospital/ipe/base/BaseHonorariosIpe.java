package br.com.ksisolucoes.vo.hospital.ipe.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the honorarios_ipe table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="honorarios_ipe"
 */

public abstract class BaseHonorariosIpe extends BaseRootVO implements Serializable {

	public static String REF = "HonorariosIpe";
	public static final String PROP_NUMERO_PORTE_ANESTESICO = "numeroPorteAnestesico";
	public static final String PROP_PERICIA = "pericia";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_CREDENCIAMENTO = "credenciamento";
	public static final String PROP_PROCEDIMENTO = "procedimento";
	public static final String PROP_QUANTIDADE_CH = "quantidadeCh";
	public static final String PROP_SITUACAO = "situacao";
	public static final String PROP_QUANTIDADE_AUXILIARES = "quantidadeAuxiliares";
	public static final String PROP_CONSULTORIO = "consultorio";
	public static final String PROP_VALOR = "valor";
	public static final String PROP_FRANQUIA = "franquia";
	public static final String PROP_MATERIAL_MEDICAMENTO = "materialMedicamento";
	public static final String PROP_TIPO_MOEDA = "tipoMoeda";
	public static final String PROP_QUANTIDADE_METROS_FILME = "quantidadeMetrosFilme";
	public static final String PROP_TIPO_HONORARIO = "tipoHonorario";
	public static final String PROP_DATA_INICIO_VALIDADE = "dataInicioValidade";
	public static final String PROP_ADICIONAL = "adicional";
	public static final String PROP_QUANTIDADE_INCIDENCIAS = "quantidadeIncidencias";
	public static final String PROP_PAMES = "pames";


	// constructors
	public BaseHonorariosIpe () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseHonorariosIpe (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseHonorariosIpe (
		java.lang.Long codigo,
		java.util.Date dataInicioValidade,
		java.lang.Double valor,
		java.lang.Long situacao,
		java.lang.Long tipoHonorario) {

		this.setCodigo(codigo);
		this.setDataInicioValidade(dataInicioValidade);
		this.setValor(valor);
		this.setSituacao(situacao);
		this.setTipoHonorario(tipoHonorario);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Double quantidadeCh;
	private java.lang.Long quantidadeAuxiliares;
	private java.lang.Long numeroPorteAnestesico;
	private java.lang.Long quantidadeIncidencias;
	private java.lang.Double quantidadeMetrosFilme;
	private java.util.Date dataInicioValidade;
	private java.lang.Double valor;
	private java.lang.Long situacao;
	private java.lang.String franquia;
	private java.lang.String pames;
	private java.lang.String adicional;
	private java.lang.String materialMedicamento;
	private java.lang.Long tipoHonorario;
	private java.lang.String pericia;
	private java.lang.String consultorio;
	private java.lang.String credenciamento;
	private java.lang.Long tipoMoeda;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_honorario"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: quantidade_ch
	 */
	public java.lang.Double getQuantidadeCh () {
		return getPropertyValue(this, quantidadeCh, PROP_QUANTIDADE_CH); 
	}

	/**
	 * Set the value related to the column: quantidade_ch
	 * @param quantidadeCh the quantidade_ch value
	 */
	public void setQuantidadeCh (java.lang.Double quantidadeCh) {
//        java.lang.Double quantidadeChOld = this.quantidadeCh;
		this.quantidadeCh = quantidadeCh;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeCh", quantidadeChOld, quantidadeCh);
	}



	/**
	 * Return the value associated with the column: quantidade_auxiliares
	 */
	public java.lang.Long getQuantidadeAuxiliares () {
		return getPropertyValue(this, quantidadeAuxiliares, PROP_QUANTIDADE_AUXILIARES); 
	}

	/**
	 * Set the value related to the column: quantidade_auxiliares
	 * @param quantidadeAuxiliares the quantidade_auxiliares value
	 */
	public void setQuantidadeAuxiliares (java.lang.Long quantidadeAuxiliares) {
//        java.lang.Long quantidadeAuxiliaresOld = this.quantidadeAuxiliares;
		this.quantidadeAuxiliares = quantidadeAuxiliares;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeAuxiliares", quantidadeAuxiliaresOld, quantidadeAuxiliares);
	}



	/**
	 * Return the value associated with the column: numero_porte_anestesico
	 */
	public java.lang.Long getNumeroPorteAnestesico () {
		return getPropertyValue(this, numeroPorteAnestesico, PROP_NUMERO_PORTE_ANESTESICO); 
	}

	/**
	 * Set the value related to the column: numero_porte_anestesico
	 * @param numeroPorteAnestesico the numero_porte_anestesico value
	 */
	public void setNumeroPorteAnestesico (java.lang.Long numeroPorteAnestesico) {
//        java.lang.Long numeroPorteAnestesicoOld = this.numeroPorteAnestesico;
		this.numeroPorteAnestesico = numeroPorteAnestesico;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroPorteAnestesico", numeroPorteAnestesicoOld, numeroPorteAnestesico);
	}



	/**
	 * Return the value associated with the column: quantidade_incidencias
	 */
	public java.lang.Long getQuantidadeIncidencias () {
		return getPropertyValue(this, quantidadeIncidencias, PROP_QUANTIDADE_INCIDENCIAS); 
	}

	/**
	 * Set the value related to the column: quantidade_incidencias
	 * @param quantidadeIncidencias the quantidade_incidencias value
	 */
	public void setQuantidadeIncidencias (java.lang.Long quantidadeIncidencias) {
//        java.lang.Long quantidadeIncidenciasOld = this.quantidadeIncidencias;
		this.quantidadeIncidencias = quantidadeIncidencias;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeIncidencias", quantidadeIncidenciasOld, quantidadeIncidencias);
	}



	/**
	 * Return the value associated with the column: quantidade_metros_filme
	 */
	public java.lang.Double getQuantidadeMetrosFilme () {
		return getPropertyValue(this, quantidadeMetrosFilme, PROP_QUANTIDADE_METROS_FILME); 
	}

	/**
	 * Set the value related to the column: quantidade_metros_filme
	 * @param quantidadeMetrosFilme the quantidade_metros_filme value
	 */
	public void setQuantidadeMetrosFilme (java.lang.Double quantidadeMetrosFilme) {
//        java.lang.Double quantidadeMetrosFilmeOld = this.quantidadeMetrosFilme;
		this.quantidadeMetrosFilme = quantidadeMetrosFilme;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeMetrosFilme", quantidadeMetrosFilmeOld, quantidadeMetrosFilme);
	}



	/**
	 * Return the value associated with the column: data_inicio_validade
	 */
	public java.util.Date getDataInicioValidade () {
		return getPropertyValue(this, dataInicioValidade, PROP_DATA_INICIO_VALIDADE); 
	}

	/**
	 * Set the value related to the column: data_inicio_validade
	 * @param dataInicioValidade the data_inicio_validade value
	 */
	public void setDataInicioValidade (java.util.Date dataInicioValidade) {
//        java.util.Date dataInicioValidadeOld = this.dataInicioValidade;
		this.dataInicioValidade = dataInicioValidade;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInicioValidade", dataInicioValidadeOld, dataInicioValidade);
	}



	/**
	 * Return the value associated with the column: valor
	 */
	public java.lang.Double getValor () {
		return getPropertyValue(this, valor, PROP_VALOR); 
	}

	/**
	 * Set the value related to the column: valor
	 * @param valor the valor value
	 */
	public void setValor (java.lang.Double valor) {
//        java.lang.Double valorOld = this.valor;
		this.valor = valor;
//        this.getPropertyChangeSupport().firePropertyChange ("valor", valorOld, valor);
	}



	/**
	 * Return the value associated with the column: situacao
	 */
	public java.lang.Long getSituacao () {
		return getPropertyValue(this, situacao, PROP_SITUACAO); 
	}

	/**
	 * Set the value related to the column: situacao
	 * @param situacao the situacao value
	 */
	public void setSituacao (java.lang.Long situacao) {
//        java.lang.Long situacaoOld = this.situacao;
		this.situacao = situacao;
//        this.getPropertyChangeSupport().firePropertyChange ("situacao", situacaoOld, situacao);
	}



	/**
	 * Return the value associated with the column: franquia
	 */
	public java.lang.String getFranquia () {
		return getPropertyValue(this, franquia, PROP_FRANQUIA); 
	}

	/**
	 * Set the value related to the column: franquia
	 * @param franquia the franquia value
	 */
	public void setFranquia (java.lang.String franquia) {
//        java.lang.String franquiaOld = this.franquia;
		this.franquia = franquia;
//        this.getPropertyChangeSupport().firePropertyChange ("franquia", franquiaOld, franquia);
	}



	/**
	 * Return the value associated with the column: pames
	 */
	public java.lang.String getPames () {
		return getPropertyValue(this, pames, PROP_PAMES); 
	}

	/**
	 * Set the value related to the column: pames
	 * @param pames the pames value
	 */
	public void setPames (java.lang.String pames) {
//        java.lang.String pamesOld = this.pames;
		this.pames = pames;
//        this.getPropertyChangeSupport().firePropertyChange ("pames", pamesOld, pames);
	}



	/**
	 * Return the value associated with the column: adicional
	 */
	public java.lang.String getAdicional () {
		return getPropertyValue(this, adicional, PROP_ADICIONAL); 
	}

	/**
	 * Set the value related to the column: adicional
	 * @param adicional the adicional value
	 */
	public void setAdicional (java.lang.String adicional) {
//        java.lang.String adicionalOld = this.adicional;
		this.adicional = adicional;
//        this.getPropertyChangeSupport().firePropertyChange ("adicional", adicionalOld, adicional);
	}



	/**
	 * Return the value associated with the column: material_medicamento
	 */
	public java.lang.String getMaterialMedicamento () {
		return getPropertyValue(this, materialMedicamento, PROP_MATERIAL_MEDICAMENTO); 
	}

	/**
	 * Set the value related to the column: material_medicamento
	 * @param materialMedicamento the material_medicamento value
	 */
	public void setMaterialMedicamento (java.lang.String materialMedicamento) {
//        java.lang.String materialMedicamentoOld = this.materialMedicamento;
		this.materialMedicamento = materialMedicamento;
//        this.getPropertyChangeSupport().firePropertyChange ("materialMedicamento", materialMedicamentoOld, materialMedicamento);
	}



	/**
	 * Return the value associated with the column: tipo_honorario
	 */
	public java.lang.Long getTipoHonorario () {
		return getPropertyValue(this, tipoHonorario, PROP_TIPO_HONORARIO); 
	}

	/**
	 * Set the value related to the column: tipo_honorario
	 * @param tipoHonorario the tipo_honorario value
	 */
	public void setTipoHonorario (java.lang.Long tipoHonorario) {
//        java.lang.Long tipoHonorarioOld = this.tipoHonorario;
		this.tipoHonorario = tipoHonorario;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoHonorario", tipoHonorarioOld, tipoHonorario);
	}



	/**
	 * Return the value associated with the column: pericia
	 */
	public java.lang.String getPericia () {
		return getPropertyValue(this, pericia, PROP_PERICIA); 
	}

	/**
	 * Set the value related to the column: pericia
	 * @param pericia the pericia value
	 */
	public void setPericia (java.lang.String pericia) {
//        java.lang.String periciaOld = this.pericia;
		this.pericia = pericia;
//        this.getPropertyChangeSupport().firePropertyChange ("pericia", periciaOld, pericia);
	}



	/**
	 * Return the value associated with the column: consultorio
	 */
	public java.lang.String getConsultorio () {
		return getPropertyValue(this, consultorio, PROP_CONSULTORIO); 
	}

	/**
	 * Set the value related to the column: consultorio
	 * @param consultorio the consultorio value
	 */
	public void setConsultorio (java.lang.String consultorio) {
//        java.lang.String consultorioOld = this.consultorio;
		this.consultorio = consultorio;
//        this.getPropertyChangeSupport().firePropertyChange ("consultorio", consultorioOld, consultorio);
	}



	/**
	 * Return the value associated with the column: credenciamento
	 */
	public java.lang.String getCredenciamento () {
		return getPropertyValue(this, credenciamento, PROP_CREDENCIAMENTO); 
	}

	/**
	 * Set the value related to the column: credenciamento
	 * @param credenciamento the credenciamento value
	 */
	public void setCredenciamento (java.lang.String credenciamento) {
//        java.lang.String credenciamentoOld = this.credenciamento;
		this.credenciamento = credenciamento;
//        this.getPropertyChangeSupport().firePropertyChange ("credenciamento", credenciamentoOld, credenciamento);
	}



	/**
	 * Return the value associated with the column: tp_moeda
	 */
	public java.lang.Long getTipoMoeda () {
		return getPropertyValue(this, tipoMoeda, PROP_TIPO_MOEDA); 
	}

	/**
	 * Set the value related to the column: tp_moeda
	 * @param tipoMoeda the tp_moeda value
	 */
	public void setTipoMoeda (java.lang.Long tipoMoeda) {
//        java.lang.Long tipoMoedaOld = this.tipoMoeda;
		this.tipoMoeda = tipoMoeda;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoMoeda", tipoMoedaOld, tipoMoeda);
	}



	/**
	 * Return the value associated with the column: cd_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento getProcedimento () {
		return getPropertyValue(this, procedimento, PROP_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_procedimento
	 * @param procedimento the cd_procedimento value
	 */
	public void setProcedimento (br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoOld = this.procedimento;
		this.procedimento = procedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimento", procedimentoOld, procedimento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.hospital.ipe.HonorariosIpe)) return false;
		else {
			br.com.ksisolucoes.vo.hospital.ipe.HonorariosIpe honorariosIpe = (br.com.ksisolucoes.vo.hospital.ipe.HonorariosIpe) obj;
			if (null == this.getCodigo() || null == honorariosIpe.getCodigo()) return false;
			else return (this.getCodigo().equals(honorariosIpe.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}