package br.com.ksisolucoes.vo.vigilancia.agravo.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the registro_agravo_ocorrencia table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="registro_agravo_ocorrencia"
 */

public abstract class BaseRegistroAgravoOcorrencia extends BaseRootVO implements Serializable {

	public static String REF = "RegistroAgravoOcorrencia";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_DATA_OCORRENCIA = "dataOcorrencia";
	public static final String PROP_PROFISSIONAL = "profissional";
	public static final String PROP_REGISTRO_AGRAVO = "registroAgravo";
	public static final String PROP_TIPO_OCORRENCIA_AGRAVO = "tipoOcorrenciaAgravo";
	public static final String PROP_USUARIO_CADASTRO = "usuarioCadastro";
	public static final String PROP_DESCRICAO_OCORRENCIA = "descricaoOcorrencia";


	// constructors
	public BaseRegistroAgravoOcorrencia () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseRegistroAgravoOcorrencia (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseRegistroAgravoOcorrencia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
		br.com.ksisolucoes.vo.cadsus.Profissional profissional,
		br.com.ksisolucoes.vo.vigilancia.agravo.TipoOcorrenciaAgravo tipoOcorrenciaAgravo,
		br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
		java.util.Date dataOcorrencia,
		java.lang.String descricaoOcorrencia,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setRegistroAgravo(registroAgravo);
		this.setProfissional(profissional);
		this.setTipoOcorrenciaAgravo(tipoOcorrenciaAgravo);
		this.setUsuarioCadastro(usuarioCadastro);
		this.setDataOcorrencia(dataOcorrencia);
		this.setDescricaoOcorrencia(descricaoOcorrencia);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataOcorrencia;
	private java.lang.String descricaoOcorrencia;
	private java.util.Date dataCadastro;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissional;
	private br.com.ksisolucoes.vo.vigilancia.agravo.TipoOcorrenciaAgravo tipoOcorrenciaAgravo;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_reg_agr_ocorrencia"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_ocorrencia
	 */
	public java.util.Date getDataOcorrencia () {
		return getPropertyValue(this, dataOcorrencia, PROP_DATA_OCORRENCIA); 
	}

	/**
	 * Set the value related to the column: dt_ocorrencia
	 * @param dataOcorrencia the dt_ocorrencia value
	 */
	public void setDataOcorrencia (java.util.Date dataOcorrencia) {
//        java.util.Date dataOcorrenciaOld = this.dataOcorrencia;
		this.dataOcorrencia = dataOcorrencia;
//        this.getPropertyChangeSupport().firePropertyChange ("dataOcorrencia", dataOcorrenciaOld, dataOcorrencia);
	}



	/**
	 * Return the value associated with the column: ds_ocorrencia
	 */
	public java.lang.String getDescricaoOcorrencia () {
		return getPropertyValue(this, descricaoOcorrencia, PROP_DESCRICAO_OCORRENCIA); 
	}

	/**
	 * Set the value related to the column: ds_ocorrencia
	 * @param descricaoOcorrencia the ds_ocorrencia value
	 */
	public void setDescricaoOcorrencia (java.lang.String descricaoOcorrencia) {
//        java.lang.String descricaoOcorrenciaOld = this.descricaoOcorrencia;
		this.descricaoOcorrencia = descricaoOcorrencia;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoOcorrencia", descricaoOcorrenciaOld, descricaoOcorrencia);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: cd_registro_agravo
	 */
	public br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo getRegistroAgravo () {
		return getPropertyValue(this, registroAgravo, PROP_REGISTRO_AGRAVO); 
	}

	/**
	 * Set the value related to the column: cd_registro_agravo
	 * @param registroAgravo the cd_registro_agravo value
	 */
	public void setRegistroAgravo (br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo) {
//        br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravoOld = this.registroAgravo;
		this.registroAgravo = registroAgravo;
//        this.getPropertyChangeSupport().firePropertyChange ("registroAgravo", registroAgravoOld, registroAgravo);
	}



	/**
	 * Return the value associated with the column: cd_profissional
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissional () {
		return getPropertyValue(this, profissional, PROP_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_profissional
	 * @param profissional the cd_profissional value
	 */
	public void setProfissional (br.com.ksisolucoes.vo.cadsus.Profissional profissional) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalOld = this.profissional;
		this.profissional = profissional;
//        this.getPropertyChangeSupport().firePropertyChange ("profissional", profissionalOld, profissional);
	}



	/**
	 * Return the value associated with the column: cd_tp_oco_agravo
	 */
	public br.com.ksisolucoes.vo.vigilancia.agravo.TipoOcorrenciaAgravo getTipoOcorrenciaAgravo () {
		return getPropertyValue(this, tipoOcorrenciaAgravo, PROP_TIPO_OCORRENCIA_AGRAVO); 
	}

	/**
	 * Set the value related to the column: cd_tp_oco_agravo
	 * @param tipoOcorrenciaAgravo the cd_tp_oco_agravo value
	 */
	public void setTipoOcorrenciaAgravo (br.com.ksisolucoes.vo.vigilancia.agravo.TipoOcorrenciaAgravo tipoOcorrenciaAgravo) {
//        br.com.ksisolucoes.vo.vigilancia.agravo.TipoOcorrenciaAgravo tipoOcorrenciaAgravoOld = this.tipoOcorrenciaAgravo;
		this.tipoOcorrenciaAgravo = tipoOcorrenciaAgravo;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoOcorrenciaAgravo", tipoOcorrenciaAgravoOld, tipoOcorrenciaAgravo);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCadastro () {
		return getPropertyValue(this, usuarioCadastro, PROP_USUARIO_CADASTRO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuarioCadastro the cd_usuario value
	 */
	public void setUsuarioCadastro (br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCadastroOld = this.usuarioCadastro;
		this.usuarioCadastro = usuarioCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadastro", usuarioCadastroOld, usuarioCadastro);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravoOcorrencia)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravoOcorrencia registroAgravoOcorrencia = (br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravoOcorrencia) obj;
			if (null == this.getCodigo() || null == registroAgravoOcorrencia.getCodigo()) return false;
			else return (this.getCodigo().equals(registroAgravoOcorrencia.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}