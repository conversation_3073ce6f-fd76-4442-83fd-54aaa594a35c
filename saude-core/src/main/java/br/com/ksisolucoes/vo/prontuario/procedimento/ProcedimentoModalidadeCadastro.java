package br.com.ksisolucoes.vo.prontuario.procedimento;

import java.io.Serializable;
import java.util.Date;

import br.com.ksisolucoes.associacao.annotations.ColumnNameSIGTAP;
import br.com.ksisolucoes.associacao.annotations.IdNameSIGTAP;
import br.com.ksisolucoes.associacao.annotations.TableNameSIGTAP;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import br.com.ksisolucoes.vo.prontuario.procedimento.base.BaseProcedimentoModalidadeCadastro;


@TableNameSIGTAP("tb_modalidade")
public class ProcedimentoModalidadeCadastro extends BaseProcedimentoModalidadeCadastro implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

	public static final Long AMBULATORIAL = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ProcedimentoModalidadeCadastro () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ProcedimentoModalidadeCadastro (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ProcedimentoModalidadeCadastro (
		java.lang.Long codigo,
		java.lang.String descricao,
		java.util.Date dataCompetencia) {

		super (
			codigo,
			descricao,
			dataCompetencia);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getDescricaoFormatado(){
        return Util.getDescricaoFormatado(this.getCodigo(), this.getDescricao());
    }

    public String getDescricaoVO() {
        return this.getDescricao();
    }

    public String getIdentificador() {
        return this.getCodigo().toString();
    }

    @IdNameSIGTAP("CO_MODALIDADE")
    @Override
    public Long getCodigo() {
        return super.getCodigo();
    }

    @ColumnNameSIGTAP("DT_COMPETENCIA")
    @Override
    public Date getDataCompetencia() {
        return super.getDataCompetencia();
    }

    @ColumnNameSIGTAP("NO_MODALIDADE")
    @Override
    public String getDescricao() {
        return super.getDescricao();
    }

    @Override
    public void setCodigo(Long codigo) {
        super.setCodigo(codigo);
    }

    @Override
    public void setDataCompetencia(Date dataCompetencia) {
        super.setDataCompetencia(dataCompetencia);
    }

    @Override
    public void setDescricao(String descricao) {
        super.setDescricao(descricao);
    }


}