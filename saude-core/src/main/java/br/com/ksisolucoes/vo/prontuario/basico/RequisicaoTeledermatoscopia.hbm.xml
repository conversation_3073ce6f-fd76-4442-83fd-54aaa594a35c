<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="RequisicaoTeledermatoscopia" table="requisicao_teledermatoscopia" >
         
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_teledermatoscopia"
        >
            <generator class="assigned"/>
        </id> <version column="version" name="version" type="long" />
          
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao"
            name="exameRequisicao"
            column="cd_exame_requisicao"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.Cid"
            name="cid"
            column="cd_cid"
            not-null="false"
        />
        
        <property
            name="descricaoRequisicao"
            column="ds_requisicao"
            type="java.lang.String"
            length="50"
            not-null="false"
        />
        
        <property
            name="descricaoProfissional"
            column="ds_profissional"
            type="java.lang.String"
            length="60"
            not-null="false"
        />
        
        <property
            name="descricaoProcedencia"
            column="ds_procedencia"
            type="java.lang.String"
            length="100"
            not-null="false"
        />
        
        <property
            name="tipo"
            column="tipo"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="tabagismo"
            column="tabagismo"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="cigarrosDia"
            column="cigarros_dia"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="anosFuma"
            column="anos_fuma"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="etilismo"
            column="etilismo"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="fototipo"
            column="fototipo"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="infeccoes"
            column="infeccoes"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="descricaoInfeccoes"
            column="ds_infeccoes"
            type="java.lang.String"
            length="25"
            not-null="false"
        />
        
        <property
            name="comorbidades"
            column="comorbidades"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="descricaoComorbidades"
            column="ds_comorbidades"
            type="java.lang.String"
            length="25"
            not-null="false"
        />
        
        <property
            name="tempoDoenca"
            column="tempo_doenca"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="quantidadeTempoDoenca"
            column="qtd_tempo_doenca"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="prurido"
            column="prurido"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="historiaMorbidaFamilia"
            column="hist_morbida_familia"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="historiaMorbidaPregressa"
            column="hist_morbida_pregressa"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="lesaoEritematosas"
            column="lesao_eritematosas"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="lesaoJoelho"
            column="lesao_joelho"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="orvalhoSangrante"
            column="orvalho_sangrante"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="tipoPsoriase"
            column="tipo_psoriase"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="padraoMorfologico"
            column="padrao_morfologico"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="descricaoPadraoMorfologico"
            column="ds_padrao_morfologico"
            type="java.lang.String"
            length="25"
            not-null="false"
        />
        
        <property
            name="motivoEncaminhamento"
            column="motivo_encaminhamento"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="historiaMorbidaCancerPele"
            column="hist_morbida_cancer_pele"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="historiaMorbidaCancerOutros"
            column="hist_morbida_cancer_outros"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="fotoprotetor"
            column="fotoprotetor"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="exposicaoSolHoraDia"
            column="exposicao_sol_hora_dia"
            type="java.lang.String"
            length="25"
            not-null="false"
        />
        
        <property
            name="exposicaoSolMesesAno"
            column="exposicao_sol_meses_ano"
            type="java.lang.String"
            length="25"
            not-null="false"
        />
        
        <property
            name="lesaoHipocromicas"
            column="lesao_hipocromicas"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="espessamentoNeural"
            column="espessamento_neural"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            name="sinalNeurite"
            column="sinal_neurite"
            type="java.lang.Long"
            not-null="false"
        />
        
        <property
            column="peso"
            name="peso"
            type="java.lang.Double"
            not-null="false"
        />
        
        <property
            column="altura"
            name="altura"
            type="java.lang.Double"
            not-null="false"
        />
        
    </class>
</hibernate-mapping>
