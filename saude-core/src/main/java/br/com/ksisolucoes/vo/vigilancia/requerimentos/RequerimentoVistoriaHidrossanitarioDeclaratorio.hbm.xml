<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.requerimentos">
    <class name="RequerimentoVistoriaHidrossanitarioDeclaratorio" table="requerimento_vistoria_hidro_declaratorio">
        <id
                column="cd_requerimento_vistoria_hidro_declaratorio"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="sequence">
                <param name="sequence">seq_requer_vistoria_hidro_declaratorio</param>
            </generator>
        </id>
        <version column="version" name="version" type="long"/>

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco"
                column="cd_vigilancia_endereco"
                name="vigilanciaEndereco"
                not-null="false"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.requerimentos.VigilanciaAltoBaixoRisco"
                column="cd_vigilancia_alto_baixo_risco"
                name="vigilanciaAltoBaixoRisco"
                not-null="false"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitario"
                column="cd_requerimento_projeto_hidro"
                name="requerimentoProjetoHidrossanitarioAprovado"
                not-null="false"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia"
                column="cd_req_vigilancia"
                name="requerimentoVigilancia"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.requerimentos.TipoEnquadramentoProjeto"
                column="cd_tipo_enquadramento_projeto"
                name="tipoEnquadramentoProjeto"
                not-null="true"
        />

        <property
                column="nr_projeto_aprovado"
                name="numeroProjetoAprovado"
                type="java.lang.String"
        />

        <property
                column="obra_nr_endereco"
                name="obraNumeroEndereco"
                type="java.lang.String"
        />

        <property
                column="obra_quadra"
                name="obraQuadra"
                type="java.lang.String"
        />

        <property
                column="obra_nr_lado"
                name="obraNumeroLado"
                type="java.lang.String"
        />

        <property
                column="obra_lote"
                name="obraLote"
                type="java.lang.String"
        />

        <property
                column="obra_complemento"
                name="obraComplemento"
                type="java.lang.String"
        />

        <property
                column="obra_nr_loteamento"
                name="obraNumeroLoteamento"
                type="java.lang.String"
        />

        <property
                name="regiaoAbastecidaAgua"
                column="regiao_abastecida_agua"
                type="java.lang.Long"
        />

        <property
                name="sistemaAguaPluvial"
                column="sistema_agua_pluvial"
                type="java.lang.Long"
        />

        <property
                name="naturezaEdificacao"
                column="natureza_edificacao"
                type="java.lang.Long"
        />

        <property
                name="usoEdificacao"
                column="uso_edificacao"
                type="java.lang.Long"
        />

        <property
                column="obs_uso_edificacao"
                name="observacaoUsoEdificacao"
                type="java.lang.String"
        />

        <property
                name="parcelamentoSolo"
                column="parcelamento_solo"
                type="java.lang.Long"
        />

        <property
                name="parcelamentoSoloNumeroLotes"
                column="parcelamento_solo_nr_lotes"
                type="java.lang.Long"
        />

        <property
                column="area_comercial"
                name="areaComercial"
                type="java.lang.Double"
                not-null="false"
        />

        <property
                column="area_residencial"
                name="areaResidencial"
                type="java.lang.Double"
                not-null="false"
        />

        <property
                column="area_total_construcao"
                name="areaTotalConstrucao"
                type="java.lang.Double"
                not-null="false"
        />

        <property
                column="nr_parecer_tecnico"
                name="numeroParecerTecnico"
                type="java.lang.Long"
                not-null="false"
        />

        <property
                column="num_projeto_aprovado"
                name="numeroProjetoHSAprovado"
                type="java.lang.String"
        />

        <property
                column="num_proj_urbanistico"
                name="numeroProjetoUrbanistico"
                type="java.lang.String"
        />

        <property
                column="num_lic_ambiental"
                name="numeroLicencaAmbiental"
                type="java.lang.String"
        />

        <property
                column="num_proj_esgoto"
                name="numeroProjetoEsgoto"
                type="java.lang.String"
        />

        <property
                column="num_proj_agua"
                name="numeroProjetoAgua"
                type="java.lang.String"
        />

        <property
                column="termo_aceite"
                name="termoAceite"
                type="java.lang.Long"
                not-null="false"
        />

        <property
                column="natureza_edificacao_nr_lai"
                name="naturezaEdificacaoLai"
                type="java.lang.String"
        />
    </class>
</hibernate-mapping>
