package br.com.ksisolucoes.vo.cadsus.cds.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the esus_ficha_atendimento_domiciliar_item_procedimento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="esus_ficha_atendimento_domiciliar_item_procedimento"
 */

public abstract class BaseEsusFichaAtendDomiciliarItemProcedimento extends BaseRootVO implements Serializable {

	public static String REF = "EsusFichaAtendDomiciliarItemProcedimento";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_PROCEDIMENTO_ESUS = "procedimentoEsus";
	public static final String PROP_PROCEDIMENTO = "procedimento";
	public static final String PROP_ESUS_FICHA_ATEND_DOMICILIAR_ITEM = "esusFichaAtendDomiciliarItem";


	// constructors
	public BaseEsusFichaAtendDomiciliarItemProcedimento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEsusFichaAtendDomiciliarItemProcedimento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseEsusFichaAtendDomiciliarItemProcedimento (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendDomiciliarItem esusFichaAtendDomiciliarItem) {

		this.setCodigo(codigo);
		this.setEsusFichaAtendDomiciliarItem(esusFichaAtendDomiciliarItem);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// many to one
	private br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendDomiciliarItem esusFichaAtendDomiciliarItem;
	private br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento;
	private br.com.ksisolucoes.vo.esus.ProcedimentoEsus procedimentoEsus;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_esus_ficha_atendimento_domiciliar_item_procedimento"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_esus_ficha_atendimento_domiciliar_item
	 */
	public br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendDomiciliarItem getEsusFichaAtendDomiciliarItem () {
		return getPropertyValue(this, esusFichaAtendDomiciliarItem, PROP_ESUS_FICHA_ATEND_DOMICILIAR_ITEM); 
	}

	/**
	 * Set the value related to the column: cd_esus_ficha_atendimento_domiciliar_item
	 * @param esusFichaAtendDomiciliarItem the cd_esus_ficha_atendimento_domiciliar_item value
	 */
	public void setEsusFichaAtendDomiciliarItem (br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendDomiciliarItem esusFichaAtendDomiciliarItem) {
//        br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendDomiciliarItem esusFichaAtendDomiciliarItemOld = this.esusFichaAtendDomiciliarItem;
		this.esusFichaAtendDomiciliarItem = esusFichaAtendDomiciliarItem;
//        this.getPropertyChangeSupport().firePropertyChange ("esusFichaAtendDomiciliarItem", esusFichaAtendDomiciliarItemOld, esusFichaAtendDomiciliarItem);
	}



	/**
	 * Return the value associated with the column: cd_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento getProcedimento () {
		return getPropertyValue(this, procedimento, PROP_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_procedimento
	 * @param procedimento the cd_procedimento value
	 */
	public void setProcedimento (br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoOld = this.procedimento;
		this.procedimento = procedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimento", procedimentoOld, procedimento);
	}



	/**
	 * Return the value associated with the column: cd_procedimento_esus
	 */
	public br.com.ksisolucoes.vo.esus.ProcedimentoEsus getProcedimentoEsus () {
		return getPropertyValue(this, procedimentoEsus, PROP_PROCEDIMENTO_ESUS); 
	}

	/**
	 * Set the value related to the column: cd_procedimento_esus
	 * @param procedimentoEsus the cd_procedimento_esus value
	 */
	public void setProcedimentoEsus (br.com.ksisolucoes.vo.esus.ProcedimentoEsus procedimentoEsus) {
//        br.com.ksisolucoes.vo.esus.ProcedimentoEsus procedimentoEsusOld = this.procedimentoEsus;
		this.procedimentoEsus = procedimentoEsus;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimentoEsus", procedimentoEsusOld, procedimentoEsus);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendDomiciliarItemProcedimento)) return false;
		else {
			br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendDomiciliarItemProcedimento esusFichaAtendDomiciliarItemProcedimento = (br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendDomiciliarItemProcedimento) obj;
			if (null == this.getCodigo() || null == esusFichaAtendDomiciliarItemProcedimento.getCodigo()) return false;
			else return (this.getCodigo().equals(esusFichaAtendDomiciliarItemProcedimento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}