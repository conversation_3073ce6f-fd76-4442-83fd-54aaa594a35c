package br.com.ksisolucoes.vo.vigilancia.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the requerimento_vigilancia table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="requerimento_vigilancia"
 */

public abstract class BaseRequerimentoVigilancia extends BaseRootVO implements Serializable {

	public static String REF = "RequerimentoVigilancia";
	public static final String PROP_ESTABELECIMENTO = "estabelecimento";
	public static final String PROP_NUMERO = "numero";
	public static final String PROP_OBSERVACAO_REQUERIMENTO = "observacaoRequerimento";
	public static final String PROP_DATA_NASCIMENTO_SOLICITANTE = "dataNascimentoSolicitante";
	public static final String PROP_DESCRICAO_PERIODO_VALIDADE = "descricaoPeriodoValidade";
	public static final String PROP_TIPO_REQUERENTE = "tipoRequerente";
	public static final String PROP_PROTOCOLO = "protocolo";
	public static final String PROP_ORIGEM = "origem";
	public static final String PROP_PROFISSIONAL_PALESTRA = "profissionalPalestra";
	public static final String PROP_SITUACAO_FINANCEIRO_COMPLEMENTAR = "situacaoFinanceiroComplementar";
	public static final String PROP_SITUACAO_APROVACAO = "situacaoAprovacao";
	public static final String PROP_MOTIVO_FINALIZACAO = "motivoFinalizacao";
	public static final String PROP_NOME = "nome";
	public static final String PROP_TIPO_INSPECAO = "tipoInspecao";
	public static final String PROP_USUARIO_FINALIZACAO = "usuarioFinalizacao";
	public static final String PROP_NATURALIDADE_SOLICITANTE = "naturalidadeSolicitante";
	public static final String PROP_DATA_ANALISE = "dataAnalise";
	public static final String PROP_DESCRICAO_ISENTO_OUTRO = "descricaoIsentoOutro";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_CARGO_SOLICITANTE = "cargoSolicitante";
	public static final String PROP_RG_CPF_SOLICITANTE = "rgCpfSolicitante";
	public static final String PROP_PARENTESCO_SOLICITANTE = "parentescoSolicitante";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";
	public static final String PROP_TIPO_DOCUMENTO = "tipoDocumento";
	public static final String PROP_DATA_USUARIO = "dataUsuario";
	public static final String PROP_DATA_INICIO_VALIDADE = "dataInicioValidade";
	public static final String PROP_DATA_FINAL_VALIDADE = "dataFinalValidade";
	public static final String PROP_CARTEIRA_PROFISSIONAL_SOLICITANTE = "carteiraProfissionalSolicitante";
	public static final String PROP_CNPJ_SOLICITANTE = "cnpjSolicitante";
	public static final String PROP_ESTABELECIMENTO_SETORES = "estabelecimentoSetores";
	public static final String PROP_USUARIO_ALTERACAO = "usuarioAlteracao";
	public static final String PROP_DATA_INSPECAO = "dataInspecao";
	public static final String PROP_SITUACAO_OCORRENCIA = "situacaoOcorrencia";
	public static final String PROP_CNPJ_CPF = "cnpjCpf";
	public static final String PROP_EMPRESA_CADASTRO = "empresaCadastro";
	public static final String PROP_VIGILANCIA_PESSOA = "vigilanciaPessoa";
	public static final String PROP_SITUACAO = "situacao";
	public static final String PROP_PROTOCOLO_SOLICITANTE = "protocoloSolicitante";
	public static final String PROP_SITUACAO_ANTERIOR_CONCLUSAO = "situacaoAnteriorConclusao";
	public static final String PROP_TELEFONE = "telefone";
	public static final String PROP_VEICULO = "veiculo";
	public static final String PROP_USUARIO_CADASTRO = "usuarioCadastro";
	public static final String PROP_DATA_ENTREGA = "dataEntrega";
	public static final String PROP_DATA_EMISSAO_RG_SOLICITANTE = "dataEmissaoRgSolicitante";
	public static final String PROP_VIGILANCIA_ENDERECO = "vigilanciaEndereco";
	public static final String PROP_CHAVE_Q_RCODE = "chaveQRcode";
	public static final String PROP_MOTIVO_REVERSAO_FINALIZACAO = "motivoReversaoFinalizacao";
	public static final String PROP_TIPO_PESSOA = "tipoPessoa";
	public static final String PROP_TIPO_LOGRADOURO = "tipoLogradouro";
	public static final String PROP_FLAG_INTEGRADO = "flagIntegrado";
	public static final String PROP_VIGILANCIA_PROFISSIONAL = "vigilanciaProfissional";
	public static final String PROP_DATA_INTEGRACAO = "dataIntegracao";
	public static final String PROP_NOME_SOLICITANTE = "nomeSolicitante";
	public static final String PROP_MOTIVO_CANCELAMENTO = "motivoCancelamento";
	public static final String PROP_DATA_FINALIZACAO = "dataFinalizacao";
	public static final String PROP_VINCULO_ENTREGA = "vinculoEntrega";
	public static final String PROP_DATA_PALESTRA = "dataPalestra";
	public static final String PROP_CPF_SOLICITANTE = "cpfSolicitante";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_DATA_APROVACAO = "dataAprovacao";
	public static final String PROP_MOTIVO_APROVACAO = "motivoAprovacao";
	public static final String PROP_ENDERECO_SOLICITANTE = "enderecoSolicitante";
	public static final String PROP_EMAIL_SOLICITANTE = "emailSolicitante";
	public static final String PROP_NOME_ENTREGA = "nomeEntrega";
	public static final String PROP_DATA_REQUERIMENTO = "dataRequerimento";
    public static final String PROP_ENVIADO_APP_FRU = "enviadoAppFru";
    public static final String PROP_COMPLEMENTO = "complemento";
	public static final String PROP_TELEFONE_SOLICITANTE = "telefoneSolicitante";
	public static final String PROP_RG_SOLICITANTE = "rgSolicitante";
	public static final String PROP_SITUACAO_ANALISE_PROJETOS = "situacaoAnaliseProjetos";
	public static final String PROP_FLAG_ISENTO_MEI = "flagIsentoMei";
	public static final String PROP_CELULAR_SOLICITANTE = "celularSolicitante";
	public static final String PROP_TIPO_SOLICITACAO = "tipoSolicitacao";
	public static final String PROP_FLAG_DESOBRIGACAO_ALVARA = "flagDesobrigacaoAlvara";


	// constructors
	public BaseRequerimentoVigilancia () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseRequerimentoVigilancia (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseRequerimentoVigilancia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao tipoSolicitacao,
		br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimento,
		br.com.ksisolucoes.vo.controle.Usuario usuarioAlteracao,
		br.com.ksisolucoes.vo.vigilancia.EstabelecimentoSetores estabelecimentoSetores,
		java.lang.String nomeSolicitante,
		java.lang.String rgCpfSolicitante,
		java.lang.String parentescoSolicitante,
		java.util.Date dataUsuario,
		java.lang.Long flagIntegrado) {

		this.setCodigo(codigo);
		this.setTipoSolicitacao(tipoSolicitacao);
		this.setEstabelecimento(estabelecimento);
		this.setUsuarioAlteracao(usuarioAlteracao);
		this.setEstabelecimentoSetores(estabelecimentoSetores);
		this.setNomeSolicitante(nomeSolicitante);
		this.setRgCpfSolicitante(rgCpfSolicitante);
		this.setParentescoSolicitante(parentescoSolicitante);
		this.setDataUsuario(dataUsuario);
		this.setFlagIntegrado(flagIntegrado);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String nomeSolicitante;
	private java.lang.String rgCpfSolicitante;
	private java.lang.String cargoSolicitante;
	private java.lang.String telefoneSolicitante;
	private java.lang.Long situacao;
	private java.lang.Long protocolo;
	private java.util.Date dataRequerimento;
	private java.util.Date dataFinalValidade;
	private java.util.Date dataInicioValidade;
	private java.lang.String descricaoPeriodoValidade;
	private java.util.Date dataNascimentoSolicitante;
	private java.lang.String naturalidadeSolicitante;
	private java.lang.String enderecoSolicitante;
	private java.lang.String carteiraProfissionalSolicitante;
	private java.lang.String protocoloSolicitante;
	private java.util.Date dataPalestra;
	private java.lang.Long tipoDocumento;
	private java.lang.String motivoCancelamento;
	private java.lang.String motivoFinalizacao;
	private java.util.Date dataFinalizacao;
	private java.util.Date dataCancelamento;
	private java.util.Date dataAnalise;
	private java.lang.String nome;
	private java.lang.Long origem;
	private java.lang.String chaveQRcode;
	private java.lang.String telefone;
	private java.lang.String cnpjCpf;
	private java.lang.String parentescoSolicitante;
	private java.lang.String emailSolicitante;
	private java.lang.String cpfSolicitante;
	private java.lang.String rgSolicitante;
	private java.lang.String cnpjSolicitante;
	private java.lang.String celularSolicitante;
	private java.util.Date dataUsuario;
	private java.util.Date dataIntegracao;
	private java.lang.Long flagIntegrado;
	private java.lang.String numero;
	private java.lang.String complemento;
	private java.util.Date dataEntrega;
	private java.lang.String nomeEntrega;
	private java.lang.String vinculoEntrega;
	private java.lang.String observacaoRequerimento;
	private java.util.Date dataEmissaoRgSolicitante;
	private java.lang.Long situacaoOcorrencia;
	private java.lang.Long tipoPessoa;
	private java.lang.Long situacaoAprovacao;
	private java.util.Date dataAprovacao;
	private java.lang.String motivoAprovacao;
	private java.lang.Long situacaoAnaliseProjetos;
	private java.util.Date dataInspecao;
	private java.lang.Long flagIsentoMei;
	private java.lang.String descricaoIsentoOutro;
	private java.lang.Long tipoRequerente;
	private java.lang.Long situacaoAnteriorConclusao;
	private java.lang.String motivoReversaoFinalizacao;
	private java.lang.Long situacaoFinanceiroComplementar;
    private java.lang.Long tipoInspecao;
    private java.lang.Long enviadoAppFru;
	private java.lang.Long flagDesobrigacaoAlvara;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao tipoSolicitacao;
	private br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimento;
	private br.com.ksisolucoes.vo.vigilancia.VeiculoEstabelecimento veiculo;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissionalPalestra;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioFinalizacao;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;
	private br.com.ksisolucoes.vo.vigilancia.VigilanciaProfissional vigilanciaProfissional;
	private br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco vigilanciaEndereco;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioAlteracao;
	private br.com.ksisolucoes.vo.cadsus.TipoLogradouroCadsus tipoLogradouro;
	private br.com.ksisolucoes.vo.vigilancia.EstabelecimentoSetores estabelecimentoSetores;
	private br.com.ksisolucoes.vo.basico.Empresa empresaCadastro;
	private br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa vigilanciaPessoa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_req_vigilancia"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: nm_solicitante
	 */
	public java.lang.String getNomeSolicitante () {
		return getPropertyValue(this, nomeSolicitante, PROP_NOME_SOLICITANTE); 
	}

	/**
	 * Set the value related to the column: nm_solicitante
	 * @param nomeSolicitante the nm_solicitante value
	 */
	public void setNomeSolicitante (java.lang.String nomeSolicitante) {
//        java.lang.String nomeSolicitanteOld = this.nomeSolicitante;
		this.nomeSolicitante = nomeSolicitante;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeSolicitante", nomeSolicitanteOld, nomeSolicitante);
	}



	/**
	 * Return the value associated with the column: rg_cpf_solicitante
	 */
	public java.lang.String getRgCpfSolicitante () {
		return getPropertyValue(this, rgCpfSolicitante, PROP_RG_CPF_SOLICITANTE); 
	}

	/**
	 * Set the value related to the column: rg_cpf_solicitante
	 * @param rgCpfSolicitante the rg_cpf_solicitante value
	 */
	public void setRgCpfSolicitante (java.lang.String rgCpfSolicitante) {
//        java.lang.String rgCpfSolicitanteOld = this.rgCpfSolicitante;
		this.rgCpfSolicitante = rgCpfSolicitante;
//        this.getPropertyChangeSupport().firePropertyChange ("rgCpfSolicitante", rgCpfSolicitanteOld, rgCpfSolicitante);
	}



	/**
	 * Return the value associated with the column: cargo_solicitante
	 */
	public java.lang.String getCargoSolicitante () {
		return getPropertyValue(this, cargoSolicitante, PROP_CARGO_SOLICITANTE); 
	}

	/**
	 * Set the value related to the column: cargo_solicitante
	 * @param cargoSolicitante the cargo_solicitante value
	 */
	public void setCargoSolicitante (java.lang.String cargoSolicitante) {
//        java.lang.String cargoSolicitanteOld = this.cargoSolicitante;
		this.cargoSolicitante = cargoSolicitante;
//        this.getPropertyChangeSupport().firePropertyChange ("cargoSolicitante", cargoSolicitanteOld, cargoSolicitante);
	}



	/**
	 * Return the value associated with the column: telefone_solicitante
	 */
	public java.lang.String getTelefoneSolicitante () {
		return getPropertyValue(this, telefoneSolicitante, PROP_TELEFONE_SOLICITANTE); 
	}

	/**
	 * Set the value related to the column: telefone_solicitante
	 * @param telefoneSolicitante the telefone_solicitante value
	 */
	public void setTelefoneSolicitante (java.lang.String telefoneSolicitante) {
//        java.lang.String telefoneSolicitanteOld = this.telefoneSolicitante;
		this.telefoneSolicitante = telefoneSolicitante;
//        this.getPropertyChangeSupport().firePropertyChange ("telefoneSolicitante", telefoneSolicitanteOld, telefoneSolicitante);
	}



	/**
	 * Return the value associated with the column: situacao
	 */
	public java.lang.Long getSituacao () {
		return getPropertyValue(this, situacao, PROP_SITUACAO); 
	}

	/**
	 * Set the value related to the column: situacao
	 * @param situacao the situacao value
	 */
	public void setSituacao (java.lang.Long situacao) {
//        java.lang.Long situacaoOld = this.situacao;
		this.situacao = situacao;
//        this.getPropertyChangeSupport().firePropertyChange ("situacao", situacaoOld, situacao);
	}



	/**
	 * Return the value associated with the column: protocolo
	 */
	public java.lang.Long getProtocolo () {
		return getPropertyValue(this, protocolo, PROP_PROTOCOLO); 
	}

	/**
	 * Set the value related to the column: protocolo
	 * @param protocolo the protocolo value
	 */
	public void setProtocolo (java.lang.Long protocolo) {
//        java.lang.Long protocoloOld = this.protocolo;
		this.protocolo = protocolo;
//        this.getPropertyChangeSupport().firePropertyChange ("protocolo", protocoloOld, protocolo);
	}



	/**
	 * Return the value associated with the column: data_requerimento
	 */
	public java.util.Date getDataRequerimento () {
		return getPropertyValue(this, dataRequerimento, PROP_DATA_REQUERIMENTO); 
	}

	/**
	 * Set the value related to the column: data_requerimento
	 * @param dataRequerimento the data_requerimento value
	 */
	public void setDataRequerimento (java.util.Date dataRequerimento) {
//        java.util.Date dataRequerimentoOld = this.dataRequerimento;
		this.dataRequerimento = dataRequerimento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataRequerimento", dataRequerimentoOld, dataRequerimento);
	}



	/**
	 * Return the value associated with the column: data_final_validade
	 */
	public java.util.Date getDataFinalValidade () {
		return getPropertyValue(this, dataFinalValidade, PROP_DATA_FINAL_VALIDADE); 
	}

	/**
	 * Set the value related to the column: data_final_validade
	 * @param dataFinalValidade the data_final_validade value
	 */
	public void setDataFinalValidade (java.util.Date dataFinalValidade) {
//        java.util.Date dataFinalValidadeOld = this.dataFinalValidade;
		this.dataFinalValidade = dataFinalValidade;
//        this.getPropertyChangeSupport().firePropertyChange ("dataFinalValidade", dataFinalValidadeOld, dataFinalValidade);
	}



	/**
	 * Return the value associated with the column: data_inicio_validade
	 */
	public java.util.Date getDataInicioValidade () {
		return getPropertyValue(this, dataInicioValidade, PROP_DATA_INICIO_VALIDADE); 
	}

	/**
	 * Set the value related to the column: data_inicio_validade
	 * @param dataInicioValidade the data_inicio_validade value
	 */
	public void setDataInicioValidade (java.util.Date dataInicioValidade) {
//        java.util.Date dataInicioValidadeOld = this.dataInicioValidade;
		this.dataInicioValidade = dataInicioValidade;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInicioValidade", dataInicioValidadeOld, dataInicioValidade);
	}



	/**
	 * Return the value associated with the column: descricao_periodo_validade
	 */
	public java.lang.String getDescricaoPeriodoValidade () {
		return getPropertyValue(this, descricaoPeriodoValidade, PROP_DESCRICAO_PERIODO_VALIDADE); 
	}

	/**
	 * Set the value related to the column: descricao_periodo_validade
	 * @param descricaoPeriodoValidade the descricao_periodo_validade value
	 */
	public void setDescricaoPeriodoValidade (java.lang.String descricaoPeriodoValidade) {
//        java.lang.String descricaoPeriodoValidadeOld = this.descricaoPeriodoValidade;
		this.descricaoPeriodoValidade = descricaoPeriodoValidade;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoPeriodoValidade", descricaoPeriodoValidadeOld, descricaoPeriodoValidade);
	}



	/**
	 * Return the value associated with the column: dt_nascimento_solicitante
	 */
	public java.util.Date getDataNascimentoSolicitante () {
		return getPropertyValue(this, dataNascimentoSolicitante, PROP_DATA_NASCIMENTO_SOLICITANTE); 
	}

	/**
	 * Set the value related to the column: dt_nascimento_solicitante
	 * @param dataNascimentoSolicitante the dt_nascimento_solicitante value
	 */
	public void setDataNascimentoSolicitante (java.util.Date dataNascimentoSolicitante) {
//        java.util.Date dataNascimentoSolicitanteOld = this.dataNascimentoSolicitante;
		this.dataNascimentoSolicitante = dataNascimentoSolicitante;
//        this.getPropertyChangeSupport().firePropertyChange ("dataNascimentoSolicitante", dataNascimentoSolicitanteOld, dataNascimentoSolicitante);
	}



	/**
	 * Return the value associated with the column: naturalidade_solicitante
	 */
	public java.lang.String getNaturalidadeSolicitante () {
		return getPropertyValue(this, naturalidadeSolicitante, PROP_NATURALIDADE_SOLICITANTE); 
	}

	/**
	 * Set the value related to the column: naturalidade_solicitante
	 * @param naturalidadeSolicitante the naturalidade_solicitante value
	 */
	public void setNaturalidadeSolicitante (java.lang.String naturalidadeSolicitante) {
//        java.lang.String naturalidadeSolicitanteOld = this.naturalidadeSolicitante;
		this.naturalidadeSolicitante = naturalidadeSolicitante;
//        this.getPropertyChangeSupport().firePropertyChange ("naturalidadeSolicitante", naturalidadeSolicitanteOld, naturalidadeSolicitante);
	}



	/**
	 * Return the value associated with the column: endereco_solicitante
	 */
	public java.lang.String getEnderecoSolicitante () {
		return getPropertyValue(this, enderecoSolicitante, PROP_ENDERECO_SOLICITANTE); 
	}

	/**
	 * Set the value related to the column: endereco_solicitante
	 * @param enderecoSolicitante the endereco_solicitante value
	 */
	public void setEnderecoSolicitante (java.lang.String enderecoSolicitante) {
//        java.lang.String enderecoSolicitanteOld = this.enderecoSolicitante;
		this.enderecoSolicitante = enderecoSolicitante;
//        this.getPropertyChangeSupport().firePropertyChange ("enderecoSolicitante", enderecoSolicitanteOld, enderecoSolicitante);
	}



	/**
	 * Return the value associated with the column: carteira_pro_solicitante
	 */
	public java.lang.String getCarteiraProfissionalSolicitante () {
		return getPropertyValue(this, carteiraProfissionalSolicitante, PROP_CARTEIRA_PROFISSIONAL_SOLICITANTE); 
	}

	/**
	 * Set the value related to the column: carteira_pro_solicitante
	 * @param carteiraProfissionalSolicitante the carteira_pro_solicitante value
	 */
	public void setCarteiraProfissionalSolicitante (java.lang.String carteiraProfissionalSolicitante) {
//        java.lang.String carteiraProfissionalSolicitanteOld = this.carteiraProfissionalSolicitante;
		this.carteiraProfissionalSolicitante = carteiraProfissionalSolicitante;
//        this.getPropertyChangeSupport().firePropertyChange ("carteiraProfissionalSolicitante", carteiraProfissionalSolicitanteOld, carteiraProfissionalSolicitante);
	}



	/**
	 * Return the value associated with the column: protocolo_solicitante
	 */
	public java.lang.String getProtocoloSolicitante () {
		return getPropertyValue(this, protocoloSolicitante, PROP_PROTOCOLO_SOLICITANTE); 
	}

	/**
	 * Set the value related to the column: protocolo_solicitante
	 * @param protocoloSolicitante the protocolo_solicitante value
	 */
	public void setProtocoloSolicitante (java.lang.String protocoloSolicitante) {
//        java.lang.String protocoloSolicitanteOld = this.protocoloSolicitante;
		this.protocoloSolicitante = protocoloSolicitante;
//        this.getPropertyChangeSupport().firePropertyChange ("protocoloSolicitante", protocoloSolicitanteOld, protocoloSolicitante);
	}



	/**
	 * Return the value associated with the column: dt_palestra
	 */
	public java.util.Date getDataPalestra () {
		return getPropertyValue(this, dataPalestra, PROP_DATA_PALESTRA); 
	}

	/**
	 * Set the value related to the column: dt_palestra
	 * @param dataPalestra the dt_palestra value
	 */
	public void setDataPalestra (java.util.Date dataPalestra) {
//        java.util.Date dataPalestraOld = this.dataPalestra;
		this.dataPalestra = dataPalestra;
//        this.getPropertyChangeSupport().firePropertyChange ("dataPalestra", dataPalestraOld, dataPalestra);
	}



	/**
	 * Return the value associated with the column: tipo_documento
	 */
	public java.lang.Long getTipoDocumento () {
		return getPropertyValue(this, tipoDocumento, PROP_TIPO_DOCUMENTO); 
	}

	/**
	 * Set the value related to the column: tipo_documento
	 * @param tipoDocumento the tipo_documento value
	 */
	public void setTipoDocumento (java.lang.Long tipoDocumento) {
//        java.lang.Long tipoDocumentoOld = this.tipoDocumento;
		this.tipoDocumento = tipoDocumento;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDocumento", tipoDocumentoOld, tipoDocumento);
	}



	/**
	 * Return the value associated with the column: motivo_cancelamento
	 */
	public java.lang.String getMotivoCancelamento () {
		return getPropertyValue(this, motivoCancelamento, PROP_MOTIVO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: motivo_cancelamento
	 * @param motivoCancelamento the motivo_cancelamento value
	 */
	public void setMotivoCancelamento (java.lang.String motivoCancelamento) {
//        java.lang.String motivoCancelamentoOld = this.motivoCancelamento;
		this.motivoCancelamento = motivoCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoCancelamento", motivoCancelamentoOld, motivoCancelamento);
	}



	/**
	 * Return the value associated with the column: motivo_finalizacao
	 */
	public java.lang.String getMotivoFinalizacao () {
		return getPropertyValue(this, motivoFinalizacao, PROP_MOTIVO_FINALIZACAO); 
	}

	/**
	 * Set the value related to the column: motivo_finalizacao
	 * @param motivoFinalizacao the motivo_finalizacao value
	 */
	public void setMotivoFinalizacao (java.lang.String motivoFinalizacao) {
//        java.lang.String motivoFinalizacaoOld = this.motivoFinalizacao;
		this.motivoFinalizacao = motivoFinalizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoFinalizacao", motivoFinalizacaoOld, motivoFinalizacao);
	}



	/**
	 * Return the value associated with the column: dt_finalizacao
	 */
	public java.util.Date getDataFinalizacao () {
		return getPropertyValue(this, dataFinalizacao, PROP_DATA_FINALIZACAO); 
	}

	/**
	 * Set the value related to the column: dt_finalizacao
	 * @param dataFinalizacao the dt_finalizacao value
	 */
	public void setDataFinalizacao (java.util.Date dataFinalizacao) {
//        java.util.Date dataFinalizacaoOld = this.dataFinalizacao;
		this.dataFinalizacao = dataFinalizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataFinalizacao", dataFinalizacaoOld, dataFinalizacao);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: dt_analise
	 */
	public java.util.Date getDataAnalise () {
		return getPropertyValue(this, dataAnalise, PROP_DATA_ANALISE); 
	}

	/**
	 * Set the value related to the column: dt_analise
	 * @param dataAnalise the dt_analise value
	 */
	public void setDataAnalise (java.util.Date dataAnalise) {
//        java.util.Date dataAnaliseOld = this.dataAnalise;
		this.dataAnalise = dataAnalise;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAnalise", dataAnaliseOld, dataAnalise);
	}



	/**
	 * Return the value associated with the column: nome
	 */
	public java.lang.String getNome () {
		return getPropertyValue(this, nome, PROP_NOME); 
	}

	/**
	 * Set the value related to the column: nome
	 * @param nome the nome value
	 */
	public void setNome (java.lang.String nome) {
//        java.lang.String nomeOld = this.nome;
		this.nome = nome;
//        this.getPropertyChangeSupport().firePropertyChange ("nome", nomeOld, nome);
	}



	/**
	 * Return the value associated with the column: origem
	 */
	public java.lang.Long getOrigem () {
		return getPropertyValue(this, origem, PROP_ORIGEM); 
	}

	/**
	 * Set the value related to the column: origem
	 * @param origem the origem value
	 */
	public void setOrigem (java.lang.Long origem) {
//        java.lang.Long origemOld = this.origem;
		this.origem = origem;
//        this.getPropertyChangeSupport().firePropertyChange ("origem", origemOld, origem);
	}



	/**
	 * Return the value associated with the column: chave_qrcode
	 */
	public java.lang.String getChaveQRcode () {
		return getPropertyValue(this, chaveQRcode, PROP_CHAVE_Q_RCODE); 
	}

	/**
	 * Set the value related to the column: chave_qrcode
	 * @param chaveQRcode the chave_qrcode value
	 */
	public void setChaveQRcode (java.lang.String chaveQRcode) {
//        java.lang.String chaveQRcodeOld = this.chaveQRcode;
		this.chaveQRcode = chaveQRcode;
//        this.getPropertyChangeSupport().firePropertyChange ("chaveQRcode", chaveQRcodeOld, chaveQRcode);
	}



	/**
	 * Return the value associated with the column: telefone
	 */
	public java.lang.String getTelefone () {
		return getPropertyValue(this, telefone, PROP_TELEFONE); 
	}

	/**
	 * Set the value related to the column: telefone
	 * @param telefone the telefone value
	 */
	public void setTelefone (java.lang.String telefone) {
//        java.lang.String telefoneOld = this.telefone;
		this.telefone = telefone;
//        this.getPropertyChangeSupport().firePropertyChange ("telefone", telefoneOld, telefone);
	}



	/**
	 * Return the value associated with the column: cnpj_cpf
	 */
	public java.lang.String getCnpjCpf () {
		return getPropertyValue(this, cnpjCpf, PROP_CNPJ_CPF); 
	}

	/**
	 * Set the value related to the column: cnpj_cpf
	 * @param cnpjCpf the cnpj_cpf value
	 */
	public void setCnpjCpf (java.lang.String cnpjCpf) {
//        java.lang.String cnpjCpfOld = this.cnpjCpf;
		this.cnpjCpf = cnpjCpf;
//        this.getPropertyChangeSupport().firePropertyChange ("cnpjCpf", cnpjCpfOld, cnpjCpf);
	}



	/**
	 * Return the value associated with the column: parentesco_solicitante
	 */
	public java.lang.String getParentescoSolicitante () {
		return getPropertyValue(this, parentescoSolicitante, PROP_PARENTESCO_SOLICITANTE); 
	}

	/**
	 * Set the value related to the column: parentesco_solicitante
	 * @param parentescoSolicitante the parentesco_solicitante value
	 */
	public void setParentescoSolicitante (java.lang.String parentescoSolicitante) {
//        java.lang.String parentescoSolicitanteOld = this.parentescoSolicitante;
		this.parentescoSolicitante = parentescoSolicitante;
//        this.getPropertyChangeSupport().firePropertyChange ("parentescoSolicitante", parentescoSolicitanteOld, parentescoSolicitante);
	}



	/**
	 * Return the value associated with the column: email_solicitante
	 */
	public java.lang.String getEmailSolicitante () {
		return getPropertyValue(this, emailSolicitante, PROP_EMAIL_SOLICITANTE); 
	}

	/**
	 * Set the value related to the column: email_solicitante
	 * @param emailSolicitante the email_solicitante value
	 */
	public void setEmailSolicitante (java.lang.String emailSolicitante) {
//        java.lang.String emailSolicitanteOld = this.emailSolicitante;
		this.emailSolicitante = emailSolicitante;
//        this.getPropertyChangeSupport().firePropertyChange ("emailSolicitante", emailSolicitanteOld, emailSolicitante);
	}



	/**
	 * Return the value associated with the column: cpf_solicitante
	 */
	public java.lang.String getCpfSolicitante () {
		return getPropertyValue(this, cpfSolicitante, PROP_CPF_SOLICITANTE); 
	}

	/**
	 * Set the value related to the column: cpf_solicitante
	 * @param cpfSolicitante the cpf_solicitante value
	 */
	public void setCpfSolicitante (java.lang.String cpfSolicitante) {
//        java.lang.String cpfSolicitanteOld = this.cpfSolicitante;
		this.cpfSolicitante = cpfSolicitante;
//        this.getPropertyChangeSupport().firePropertyChange ("cpfSolicitante", cpfSolicitanteOld, cpfSolicitante);
	}



	/**
	 * Return the value associated with the column: rg_solicitante
	 */
	public java.lang.String getRgSolicitante () {
		return getPropertyValue(this, rgSolicitante, PROP_RG_SOLICITANTE); 
	}

	/**
	 * Set the value related to the column: rg_solicitante
	 * @param rgSolicitante the rg_solicitante value
	 */
	public void setRgSolicitante (java.lang.String rgSolicitante) {
//        java.lang.String rgSolicitanteOld = this.rgSolicitante;
		this.rgSolicitante = rgSolicitante;
//        this.getPropertyChangeSupport().firePropertyChange ("rgSolicitante", rgSolicitanteOld, rgSolicitante);
	}



	/**
	 * Return the value associated with the column: cnpj_solicitante
	 */
	public java.lang.String getCnpjSolicitante () {
		return getPropertyValue(this, cnpjSolicitante, PROP_CNPJ_SOLICITANTE); 
	}

	/**
	 * Set the value related to the column: cnpj_solicitante
	 * @param cnpjSolicitante the cnpj_solicitante value
	 */
	public void setCnpjSolicitante (java.lang.String cnpjSolicitante) {
//        java.lang.String cnpjSolicitanteOld = this.cnpjSolicitante;
		this.cnpjSolicitante = cnpjSolicitante;
//        this.getPropertyChangeSupport().firePropertyChange ("cnpjSolicitante", cnpjSolicitanteOld, cnpjSolicitante);
	}



	/**
	 * Return the value associated with the column: celular_solicitante
	 */
	public java.lang.String getCelularSolicitante () {
		return getPropertyValue(this, celularSolicitante, PROP_CELULAR_SOLICITANTE); 
	}

	/**
	 * Set the value related to the column: celular_solicitante
	 * @param celularSolicitante the celular_solicitante value
	 */
	public void setCelularSolicitante (java.lang.String celularSolicitante) {
//        java.lang.String celularSolicitanteOld = this.celularSolicitante;
		this.celularSolicitante = celularSolicitante;
//        this.getPropertyChangeSupport().firePropertyChange ("celularSolicitante", celularSolicitanteOld, celularSolicitante);
	}



	/**
	 * Return the value associated with the column: dt_usuario
	 */
	public java.util.Date getDataUsuario () {
		return getPropertyValue(this, dataUsuario, PROP_DATA_USUARIO); 
	}

	/**
	 * Set the value related to the column: dt_usuario
	 * @param dataUsuario the dt_usuario value
	 */
	public void setDataUsuario (java.util.Date dataUsuario) {
//        java.util.Date dataUsuarioOld = this.dataUsuario;
		this.dataUsuario = dataUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsuario", dataUsuarioOld, dataUsuario);
	}



	/**
	 * Return the value associated with the column: dt_integracao
	 */
	public java.util.Date getDataIntegracao () {
		return getPropertyValue(this, dataIntegracao, PROP_DATA_INTEGRACAO); 
	}

	/**
	 * Set the value related to the column: dt_integracao
	 * @param dataIntegracao the dt_integracao value
	 */
	public void setDataIntegracao (java.util.Date dataIntegracao) {
//        java.util.Date dataIntegracaoOld = this.dataIntegracao;
		this.dataIntegracao = dataIntegracao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataIntegracao", dataIntegracaoOld, dataIntegracao);
	}



	/**
	 * Return the value associated with the column: flag_integrado
	 */
	public java.lang.Long getFlagIntegrado () {
		return getPropertyValue(this, flagIntegrado, PROP_FLAG_INTEGRADO); 
	}

	/**
	 * Set the value related to the column: flag_integrado
	 * @param flagIntegrado the flag_integrado value
	 */
	public void setFlagIntegrado (java.lang.Long flagIntegrado) {
//        java.lang.Long flagIntegradoOld = this.flagIntegrado;
		this.flagIntegrado = flagIntegrado;
//        this.getPropertyChangeSupport().firePropertyChange ("flagIntegrado", flagIntegradoOld, flagIntegrado);
	}

	/**
	 * Return the value associated with the column: flag_desobrigacao_alvara
	 */
	public java.lang.Long getFlagDesobrigacaoAlvara() {
		return getPropertyValue(this, flagDesobrigacaoAlvara, PROP_FLAG_DESOBRIGACAO_ALVARA);
	}

	/**
	 * Set the value related to flag_desobrigacao_alvara
	 * @param flagDesobrigacaoAlvara the flag_desobrigacao_alvara value
	 */
	public void setFlagDesobrigacaoAlvara(java.lang.Long flagDesobrigacaoAlvara) {
		this.flagDesobrigacaoAlvara = flagDesobrigacaoAlvara;
	}

	/**
	 * Return the value associated with the column: numero
	 */
	public java.lang.String getNumero () {
		return getPropertyValue(this, numero, PROP_NUMERO); 
	}

	/**
	 * Set the value related to the column: numero
	 * @param numero the numero value
	 */
	public void setNumero (java.lang.String numero) {
//        java.lang.String numeroOld = this.numero;
		this.numero = numero;
//        this.getPropertyChangeSupport().firePropertyChange ("numero", numeroOld, numero);
	}



	/**
	 * Return the value associated with the column: complemento
	 */
	public java.lang.String getComplemento () {
		return getPropertyValue(this, complemento, PROP_COMPLEMENTO); 
	}

	/**
	 * Set the value related to the column: complemento
	 * @param complemento the complemento value
	 */
	public void setComplemento (java.lang.String complemento) {
//        java.lang.String complementoOld = this.complemento;
		this.complemento = complemento;
//        this.getPropertyChangeSupport().firePropertyChange ("complemento", complementoOld, complemento);
	}



	/**
	 * Return the value associated with the column: dt_entrega
	 */
	public java.util.Date getDataEntrega () {
		return getPropertyValue(this, dataEntrega, PROP_DATA_ENTREGA); 
	}

	/**
	 * Set the value related to the column: dt_entrega
	 * @param dataEntrega the dt_entrega value
	 */
	public void setDataEntrega (java.util.Date dataEntrega) {
//        java.util.Date dataEntregaOld = this.dataEntrega;
		this.dataEntrega = dataEntrega;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEntrega", dataEntregaOld, dataEntrega);
	}



	/**
	 * Return the value associated with the column: nome_entrega
	 */
	public java.lang.String getNomeEntrega () {
		return getPropertyValue(this, nomeEntrega, PROP_NOME_ENTREGA); 
	}

	/**
	 * Set the value related to the column: nome_entrega
	 * @param nomeEntrega the nome_entrega value
	 */
	public void setNomeEntrega (java.lang.String nomeEntrega) {
//        java.lang.String nomeEntregaOld = this.nomeEntrega;
		this.nomeEntrega = nomeEntrega;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeEntrega", nomeEntregaOld, nomeEntrega);
	}



	/**
	 * Return the value associated with the column: vinculo_entrega
	 */
	public java.lang.String getVinculoEntrega () {
		return getPropertyValue(this, vinculoEntrega, PROP_VINCULO_ENTREGA); 
	}

	/**
	 * Set the value related to the column: vinculo_entrega
	 * @param vinculoEntrega the vinculo_entrega value
	 */
	public void setVinculoEntrega (java.lang.String vinculoEntrega) {
//        java.lang.String vinculoEntregaOld = this.vinculoEntrega;
		this.vinculoEntrega = vinculoEntrega;
//        this.getPropertyChangeSupport().firePropertyChange ("vinculoEntrega", vinculoEntregaOld, vinculoEntrega);
	}



	/**
	 * Return the value associated with the column: obs_requerimento
	 */
	public java.lang.String getObservacaoRequerimento () {
		return getPropertyValue(this, observacaoRequerimento, PROP_OBSERVACAO_REQUERIMENTO); 
	}

	/**
	 * Set the value related to the column: obs_requerimento
	 * @param observacaoRequerimento the obs_requerimento value
	 */
	public void setObservacaoRequerimento (java.lang.String observacaoRequerimento) {
//        java.lang.String observacaoRequerimentoOld = this.observacaoRequerimento;
		this.observacaoRequerimento = observacaoRequerimento;
//        this.getPropertyChangeSupport().firePropertyChange ("observacaoRequerimento", observacaoRequerimentoOld, observacaoRequerimento);
	}



	/**
	 * Return the value associated with the column: dt_emissao_rg_solic
	 */
	public java.util.Date getDataEmissaoRgSolicitante () {
		return getPropertyValue(this, dataEmissaoRgSolicitante, PROP_DATA_EMISSAO_RG_SOLICITANTE); 
	}

	/**
	 * Set the value related to the column: dt_emissao_rg_solic
	 * @param dataEmissaoRgSolicitante the dt_emissao_rg_solic value
	 */
	public void setDataEmissaoRgSolicitante (java.util.Date dataEmissaoRgSolicitante) {
//        java.util.Date dataEmissaoRgSolicitanteOld = this.dataEmissaoRgSolicitante;
		this.dataEmissaoRgSolicitante = dataEmissaoRgSolicitante;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEmissaoRgSolicitante", dataEmissaoRgSolicitanteOld, dataEmissaoRgSolicitante);
	}



	/**
	 * Return the value associated with the column: situacao_ocorrencia
	 */
	public java.lang.Long getSituacaoOcorrencia () {
		return getPropertyValue(this, situacaoOcorrencia, PROP_SITUACAO_OCORRENCIA); 
	}

	/**
	 * Set the value related to the column: situacao_ocorrencia
	 * @param situacaoOcorrencia the situacao_ocorrencia value
	 */
	public void setSituacaoOcorrencia (java.lang.Long situacaoOcorrencia) {
//        java.lang.Long situacaoOcorrenciaOld = this.situacaoOcorrencia;
		this.situacaoOcorrencia = situacaoOcorrencia;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoOcorrencia", situacaoOcorrenciaOld, situacaoOcorrencia);
	}



	/**
	 * Return the value associated with the column: tp_pessoa
	 */
	public java.lang.Long getTipoPessoa () {
		return getPropertyValue(this, tipoPessoa, PROP_TIPO_PESSOA); 
	}

	/**
	 * Set the value related to the column: tp_pessoa
	 * @param tipoPessoa the tp_pessoa value
	 */
	public void setTipoPessoa (java.lang.Long tipoPessoa) {
//        java.lang.Long tipoPessoaOld = this.tipoPessoa;
		this.tipoPessoa = tipoPessoa;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoPessoa", tipoPessoaOld, tipoPessoa);
	}



	/**
	 * Return the value associated with the column: situacao_aprovacao
	 */
	public java.lang.Long getSituacaoAprovacao () {
		return getPropertyValue(this, situacaoAprovacao, PROP_SITUACAO_APROVACAO); 
	}

	/**
	 * Set the value related to the column: situacao_aprovacao
	 * @param situacaoAprovacao the situacao_aprovacao value
	 */
	public void setSituacaoAprovacao (java.lang.Long situacaoAprovacao) {
//        java.lang.Long situacaoAprovacaoOld = this.situacaoAprovacao;
		this.situacaoAprovacao = situacaoAprovacao;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoAprovacao", situacaoAprovacaoOld, situacaoAprovacao);
	}



	/**
	 * Return the value associated with the column: dt_aprovacao
	 */
	public java.util.Date getDataAprovacao () {
		return getPropertyValue(this, dataAprovacao, PROP_DATA_APROVACAO); 
	}

	/**
	 * Set the value related to the column: dt_aprovacao
	 * @param dataAprovacao the dt_aprovacao value
	 */
	public void setDataAprovacao (java.util.Date dataAprovacao) {
//        java.util.Date dataAprovacaoOld = this.dataAprovacao;
		this.dataAprovacao = dataAprovacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAprovacao", dataAprovacaoOld, dataAprovacao);
	}



	/**
	 * Return the value associated with the column: motivo_aprovacao
	 */
	public java.lang.String getMotivoAprovacao () {
		return getPropertyValue(this, motivoAprovacao, PROP_MOTIVO_APROVACAO); 
	}

	/**
	 * Set the value related to the column: motivo_aprovacao
	 * @param motivoAprovacao the motivo_aprovacao value
	 */
	public void setMotivoAprovacao (java.lang.String motivoAprovacao) {
//        java.lang.String motivoAprovacaoOld = this.motivoAprovacao;
		this.motivoAprovacao = motivoAprovacao;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoAprovacao", motivoAprovacaoOld, motivoAprovacao);
	}



	/**
	 * Return the value associated with the column: situacao_analise_projetos
	 */
	public java.lang.Long getSituacaoAnaliseProjetos () {
		return getPropertyValue(this, situacaoAnaliseProjetos, PROP_SITUACAO_ANALISE_PROJETOS); 
	}

	/**
	 * Set the value related to the column: situacao_analise_projetos
	 * @param situacaoAnaliseProjetos the situacao_analise_projetos value
	 */
	public void setSituacaoAnaliseProjetos (java.lang.Long situacaoAnaliseProjetos) {
//        java.lang.Long situacaoAnaliseProjetosOld = this.situacaoAnaliseProjetos;
		this.situacaoAnaliseProjetos = situacaoAnaliseProjetos;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoAnaliseProjetos", situacaoAnaliseProjetosOld, situacaoAnaliseProjetos);
	}



	/**
	 * Return the value associated with the column: dt_inspecao
	 */
	public java.util.Date getDataInspecao () {
		return getPropertyValue(this, dataInspecao, PROP_DATA_INSPECAO); 
	}

	/**
	 * Set the value related to the column: dt_inspecao
	 * @param dataInspecao the dt_inspecao value
	 */
	public void setDataInspecao (java.util.Date dataInspecao) {
//        java.util.Date dataInspecaoOld = this.dataInspecao;
		this.dataInspecao = dataInspecao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInspecao", dataInspecaoOld, dataInspecao);
	}



	/**
	 * Return the value associated with the column: flag_isento_mei
	 */
	public java.lang.Long getFlagIsentoMei () {
		return getPropertyValue(this, flagIsentoMei, PROP_FLAG_ISENTO_MEI); 
	}

	/**
	 * Set the value related to the column: flag_isento_mei
	 * @param flagIsentoMei the flag_isento_mei value
	 */
	public void setFlagIsentoMei (java.lang.Long flagIsentoMei) {
//        java.lang.Long flagIsentoMeiOld = this.flagIsentoMei;
		this.flagIsentoMei = flagIsentoMei;
//        this.getPropertyChangeSupport().firePropertyChange ("flagIsentoMei", flagIsentoMeiOld, flagIsentoMei);
	}



	/**
	 * Return the value associated with the column: descricao_isento_outro
	 */
	public java.lang.String getDescricaoIsentoOutro () {
		return getPropertyValue(this, descricaoIsentoOutro, PROP_DESCRICAO_ISENTO_OUTRO); 
	}

	/**
	 * Set the value related to the column: descricao_isento_outro
	 * @param descricaoIsentoOutro the descricao_isento_outro value
	 */
	public void setDescricaoIsentoOutro (java.lang.String descricaoIsentoOutro) {
//        java.lang.String descricaoIsentoOutroOld = this.descricaoIsentoOutro;
		this.descricaoIsentoOutro = descricaoIsentoOutro;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoIsentoOutro", descricaoIsentoOutroOld, descricaoIsentoOutro);
	}



	/**
	 * Return the value associated with the column: tp_requerente
	 */
	public java.lang.Long getTipoRequerente () {
		return getPropertyValue(this, tipoRequerente, PROP_TIPO_REQUERENTE); 
	}

	/**
	 * Set the value related to the column: tp_requerente
	 * @param tipoRequerente the tp_requerente value
	 */
	public void setTipoRequerente (java.lang.Long tipoRequerente) {
//        java.lang.Long tipoRequerenteOld = this.tipoRequerente;
		this.tipoRequerente = tipoRequerente;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoRequerente", tipoRequerenteOld, tipoRequerente);
	}



	/**
	 * Return the value associated with the column: situacao_anterior_conclusao
	 */
	public java.lang.Long getSituacaoAnteriorConclusao () {
		return getPropertyValue(this, situacaoAnteriorConclusao, PROP_SITUACAO_ANTERIOR_CONCLUSAO); 
	}

	/**
	 * Set the value related to the column: situacao_anterior_conclusao
	 * @param situacaoAnteriorConclusao the situacao_anterior_conclusao value
	 */
	public void setSituacaoAnteriorConclusao (java.lang.Long situacaoAnteriorConclusao) {
//        java.lang.Long situacaoAnteriorConclusaoOld = this.situacaoAnteriorConclusao;
		this.situacaoAnteriorConclusao = situacaoAnteriorConclusao;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoAnteriorConclusao", situacaoAnteriorConclusaoOld, situacaoAnteriorConclusao);
	}



	/**
	 * Return the value associated with the column: motivo_reversao_finalizacao
	 */
	public java.lang.String getMotivoReversaoFinalizacao () {
		return getPropertyValue(this, motivoReversaoFinalizacao, PROP_MOTIVO_REVERSAO_FINALIZACAO); 
	}

	/**
	 * Set the value related to the column: motivo_reversao_finalizacao
	 * @param motivoReversaoFinalizacao the motivo_reversao_finalizacao value
	 */
	public void setMotivoReversaoFinalizacao (java.lang.String motivoReversaoFinalizacao) {
//        java.lang.String motivoReversaoFinalizacaoOld = this.motivoReversaoFinalizacao;
		this.motivoReversaoFinalizacao = motivoReversaoFinalizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoReversaoFinalizacao", motivoReversaoFinalizacaoOld, motivoReversaoFinalizacao);
	}



	/**
	 * Return the value associated with the column: situacao_fin_complementar
	 */
	public java.lang.Long getSituacaoFinanceiroComplementar () {
		return getPropertyValue(this, situacaoFinanceiroComplementar, PROP_SITUACAO_FINANCEIRO_COMPLEMENTAR); 
	}

	/**
	 * Set the value related to the column: situacao_fin_complementar
	 * @param situacaoFinanceiroComplementar the situacao_fin_complementar value
	 */
	public void setSituacaoFinanceiroComplementar (java.lang.Long situacaoFinanceiroComplementar) {
//        java.lang.Long situacaoFinanceiroComplementarOld = this.situacaoFinanceiroComplementar;
		this.situacaoFinanceiroComplementar = situacaoFinanceiroComplementar;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoFinanceiroComplementar", situacaoFinanceiroComplementarOld, situacaoFinanceiroComplementar);
	}



	/**
	 * Return the value associated with the column: tp_inspecao
	 */
	public java.lang.Long getTipoInspecao () {
		return getPropertyValue(this, tipoInspecao, PROP_TIPO_INSPECAO); 
	}

	/**
	 * Set the value related to the column: tp_inspecao
	 * @param tipoInspecao the tp_inspecao value
     */
    public void setTipoInspecao(java.lang.Long tipoInspecao) {
//        java.lang.Long tipoInspecaoOld = this.tipoInspecao;
        this.tipoInspecao = tipoInspecao;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoInspecao", tipoInspecaoOld, tipoInspecao);
    }


    /**
     * Return the value associated with the column: enviado_app_fru
     */
    public java.lang.Long getEnviadoAppFru() {
        return getPropertyValue(this, enviadoAppFru, PROP_ENVIADO_APP_FRU);
    }

    /**
     * Set the value related to the column: enviado_app_fru
     *
     * @param enviadoAppFru the enviado_app_fru value
     */
    public void setEnviadoAppFru(java.lang.Long enviadoAppFru) {
//        java.lang.Long enviadoAppFruOld = this.enviadoAppFru;
        this.enviadoAppFru = enviadoAppFru;
//        this.getPropertyChangeSupport().firePropertyChange ("enviadoAppFru", enviadoAppFruOld, enviadoAppFru);
    }


    /**
     * Return the value associated with the column: cd_tipo_solicitacao
     */
    public br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao getTipoSolicitacao() {
        return getPropertyValue(this, tipoSolicitacao, PROP_TIPO_SOLICITACAO);
    }

    /**
     * Set the value related to the column: cd_tipo_solicitacao
     *
     * @param tipoSolicitacao the cd_tipo_solicitacao value
     */
	public void setTipoSolicitacao (br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao tipoSolicitacao) {
//        br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao tipoSolicitacaoOld = this.tipoSolicitacao;
		this.tipoSolicitacao = tipoSolicitacao;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoSolicitacao", tipoSolicitacaoOld, tipoSolicitacao);
	}



	/**
	 * Return the value associated with the column: cd_estabelecimento
	 */
	public br.com.ksisolucoes.vo.vigilancia.Estabelecimento getEstabelecimento () {
		return getPropertyValue(this, estabelecimento, PROP_ESTABELECIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_estabelecimento
	 * @param estabelecimento the cd_estabelecimento value
	 */
	public void setEstabelecimento (br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimento) {
//        br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimentoOld = this.estabelecimento;
		this.estabelecimento = estabelecimento;
//        this.getPropertyChangeSupport().firePropertyChange ("estabelecimento", estabelecimentoOld, estabelecimento);
	}



	/**
	 * Return the value associated with the column: cd_veiculo
	 */
	public br.com.ksisolucoes.vo.vigilancia.VeiculoEstabelecimento getVeiculo () {
		return getPropertyValue(this, veiculo, PROP_VEICULO); 
	}

	/**
	 * Set the value related to the column: cd_veiculo
	 * @param veiculo the cd_veiculo value
	 */
	public void setVeiculo (br.com.ksisolucoes.vo.vigilancia.VeiculoEstabelecimento veiculo) {
//        br.com.ksisolucoes.vo.vigilancia.VeiculoEstabelecimento veiculoOld = this.veiculo;
		this.veiculo = veiculo;
//        this.getPropertyChangeSupport().firePropertyChange ("veiculo", veiculoOld, veiculo);
	}



	/**
	 * Return the value associated with the column: cd_profissional_palestra
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissionalPalestra () {
		return getPropertyValue(this, profissionalPalestra, PROP_PROFISSIONAL_PALESTRA); 
	}

	/**
	 * Set the value related to the column: cd_profissional_palestra
	 * @param profissionalPalestra the cd_profissional_palestra value
	 */
	public void setProfissionalPalestra (br.com.ksisolucoes.vo.cadsus.Profissional profissionalPalestra) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalPalestraOld = this.profissionalPalestra;
		this.profissionalPalestra = profissionalPalestra;
//        this.getPropertyChangeSupport().firePropertyChange ("profissionalPalestra", profissionalPalestraOld, profissionalPalestra);
	}



	/**
	 * Return the value associated with the column: cd_usuario_fin
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioFinalizacao () {
		return getPropertyValue(this, usuarioFinalizacao, PROP_USUARIO_FINALIZACAO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_fin
	 * @param usuarioFinalizacao the cd_usuario_fin value
	 */
	public void setUsuarioFinalizacao (br.com.ksisolucoes.vo.controle.Usuario usuarioFinalizacao) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioFinalizacaoOld = this.usuarioFinalizacao;
		this.usuarioFinalizacao = usuarioFinalizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioFinalizacao", usuarioFinalizacaoOld, usuarioFinalizacao);
	}



	/**
	 * Return the value associated with the column: cd_usuario_can
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_can
	 * @param usuarioCancelamento the cd_usuario_can value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}



	/**
	 * Return the value associated with the column: cd_vigilancia_profissional
	 */
	public br.com.ksisolucoes.vo.vigilancia.VigilanciaProfissional getVigilanciaProfissional () {
		return getPropertyValue(this, vigilanciaProfissional, PROP_VIGILANCIA_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_vigilancia_profissional
	 * @param vigilanciaProfissional the cd_vigilancia_profissional value
	 */
	public void setVigilanciaProfissional (br.com.ksisolucoes.vo.vigilancia.VigilanciaProfissional vigilanciaProfissional) {
//        br.com.ksisolucoes.vo.vigilancia.VigilanciaProfissional vigilanciaProfissionalOld = this.vigilanciaProfissional;
		this.vigilanciaProfissional = vigilanciaProfissional;
//        this.getPropertyChangeSupport().firePropertyChange ("vigilanciaProfissional", vigilanciaProfissionalOld, vigilanciaProfissional);
	}



	/**
	 * Return the value associated with the column: cd_vigilancia_endereco
	 */
	public br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco getVigilanciaEndereco () {
		return getPropertyValue(this, vigilanciaEndereco, PROP_VIGILANCIA_ENDERECO); 
	}

	/**
	 * Set the value related to the column: cd_vigilancia_endereco
	 * @param vigilanciaEndereco the cd_vigilancia_endereco value
	 */
	public void setVigilanciaEndereco (br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco vigilanciaEndereco) {
//        br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco vigilanciaEnderecoOld = this.vigilanciaEndereco;
		this.vigilanciaEndereco = vigilanciaEndereco;
//        this.getPropertyChangeSupport().firePropertyChange ("vigilanciaEndereco", vigilanciaEnderecoOld, vigilanciaEndereco);
	}



	/**
	 * Return the value associated with the column: cd_usuario_cad
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCadastro () {
		return getPropertyValue(this, usuarioCadastro, PROP_USUARIO_CADASTRO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_cad
	 * @param usuarioCadastro the cd_usuario_cad value
	 */
	public void setUsuarioCadastro (br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCadastroOld = this.usuarioCadastro;
		this.usuarioCadastro = usuarioCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadastro", usuarioCadastroOld, usuarioCadastro);
	}



	/**
	 * Return the value associated with the column: cd_usuario_alteracao
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioAlteracao () {
		return getPropertyValue(this, usuarioAlteracao, PROP_USUARIO_ALTERACAO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_alteracao
	 * @param usuarioAlteracao the cd_usuario_alteracao value
	 */
	public void setUsuarioAlteracao (br.com.ksisolucoes.vo.controle.Usuario usuarioAlteracao) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioAlteracaoOld = this.usuarioAlteracao;
		this.usuarioAlteracao = usuarioAlteracao;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioAlteracao", usuarioAlteracaoOld, usuarioAlteracao);
	}



	/**
	 * Return the value associated with the column: cd_tipo_logradouro
	 */
	public br.com.ksisolucoes.vo.cadsus.TipoLogradouroCadsus getTipoLogradouro () {
		return getPropertyValue(this, tipoLogradouro, PROP_TIPO_LOGRADOURO); 
	}

	/**
	 * Set the value related to the column: cd_tipo_logradouro
	 * @param tipoLogradouro the cd_tipo_logradouro value
	 */
	public void setTipoLogradouro (br.com.ksisolucoes.vo.cadsus.TipoLogradouroCadsus tipoLogradouro) {
//        br.com.ksisolucoes.vo.cadsus.TipoLogradouroCadsus tipoLogradouroOld = this.tipoLogradouro;
		this.tipoLogradouro = tipoLogradouro;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoLogradouro", tipoLogradouroOld, tipoLogradouro);
	}



	/**
	 * Return the value associated with the column: cd_estabelecimento_setores
	 */
	public br.com.ksisolucoes.vo.vigilancia.EstabelecimentoSetores getEstabelecimentoSetores () {
		return getPropertyValue(this, estabelecimentoSetores, PROP_ESTABELECIMENTO_SETORES); 
	}

	/**
	 * Set the value related to the column: cd_estabelecimento_setores
	 * @param estabelecimentoSetores the cd_estabelecimento_setores value
	 */
	public void setEstabelecimentoSetores (br.com.ksisolucoes.vo.vigilancia.EstabelecimentoSetores estabelecimentoSetores) {
//        br.com.ksisolucoes.vo.vigilancia.EstabelecimentoSetores estabelecimentoSetoresOld = this.estabelecimentoSetores;
		this.estabelecimentoSetores = estabelecimentoSetores;
//        this.getPropertyChangeSupport().firePropertyChange ("estabelecimentoSetores", estabelecimentoSetoresOld, estabelecimentoSetores);
	}



	/**
	 * Return the value associated with the column: empresa_cadastro
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresaCadastro () {
		return getPropertyValue(this, empresaCadastro, PROP_EMPRESA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: empresa_cadastro
	 * @param empresaCadastro the empresa_cadastro value
	 */
	public void setEmpresaCadastro (br.com.ksisolucoes.vo.basico.Empresa empresaCadastro) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaCadastroOld = this.empresaCadastro;
		this.empresaCadastro = empresaCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaCadastro", empresaCadastroOld, empresaCadastro);
	}



	/**
	 * Return the value associated with the column: cd_vigilancia_pessoa
	 */
	public br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa getVigilanciaPessoa () {
		return getPropertyValue(this, vigilanciaPessoa, PROP_VIGILANCIA_PESSOA); 
	}

	/**
	 * Set the value related to the column: cd_vigilancia_pessoa
	 * @param vigilanciaPessoa the cd_vigilancia_pessoa value
	 */
	public void setVigilanciaPessoa (br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa vigilanciaPessoa) {
//        br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa vigilanciaPessoaOld = this.vigilanciaPessoa;
		this.vigilanciaPessoa = vigilanciaPessoa;
//        this.getPropertyChangeSupport().firePropertyChange ("vigilanciaPessoa", vigilanciaPessoaOld, vigilanciaPessoa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia = (br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia) obj;
			if (null == this.getCodigo() || null == requerimentoVigilancia.getCodigo()) return false;
			else return (this.getCodigo().equals(requerimentoVigilancia.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}