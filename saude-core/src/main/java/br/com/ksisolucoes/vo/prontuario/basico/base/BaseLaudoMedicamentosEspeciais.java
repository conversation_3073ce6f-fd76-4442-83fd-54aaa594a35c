package br.com.ksisolucoes.vo.prontuario.basico.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;

import java.io.Serializable;


/**
 * This is an object that contains data related to the laudo_medicamentos_especiais table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="laudo_medicamentos_especiais"
 */

public abstract class BaseLaudoMedicamentosEspeciais extends BaseRootVO implements Serializable {

	public static String REF = "LaudoMedicamentosEspeciais";
	public static final String PROP_STATUS = "status";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_ALTURA = "altura";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_NOME_RESPONSAVEL = "nomeResponsavel";
	public static final String PROP_PACIENTE_CONSIDERADO_INCAPAZ = "pacienteConsideradoIncapaz";
	public static final String PROP_DATA_ALTERACAO = "dataAlteracao";
	public static final String PROP_OBS_PACIENTE_REALIZOU_TRATAMENTO_PREVENTIVO = "obsPacienteRealizouTratamentoPreventivo";
	public static final String PROP_ANAMNESE = "anamnese";
	public static final String PROP_PESO = "peso";
	public static final String PROP_ATENDIMENTO = "atendimento";
	public static final String PROP_PACIENTE_REALIZOU_TRATAMENTO_PREVENTIVO = "pacienteRealizouTratamentoPreventivo";
	public static final String PROP_CID = "cid";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_PROFISSIONAL = "profissional";
        public static final String PROP_USUARIO_CADASTRO = "usuarioCadastro";


	// constructors
	public BaseLaudoMedicamentosEspeciais () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseLaudoMedicamentosEspeciais (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseLaudoMedicamentosEspeciais (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.Cid cid,
		java.lang.String anamnese,
		java.lang.Long pacienteRealizouTratamentoPreventivo,
		java.lang.Long pacienteConsideradoIncapaz,
		java.lang.Long status,
		java.util.Date dataCadastro,
		java.util.Date dataAlteracao) {

		this.setCodigo(codigo);
		this.setCid(cid);
		this.setAnamnese(anamnese);
		this.setPacienteRealizouTratamentoPreventivo(pacienteRealizouTratamentoPreventivo);
		this.setPacienteConsideradoIncapaz(pacienteConsideradoIncapaz);
		this.setStatus(status);
		this.setDataCadastro(dataCadastro);
		this.setDataAlteracao(dataAlteracao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String anamnese;
	private java.lang.Long pacienteRealizouTratamentoPreventivo;
	private java.lang.String obsPacienteRealizouTratamentoPreventivo;
	private java.lang.Long pacienteConsideradoIncapaz;
	private java.lang.String nomeResponsavel;
	private java.lang.Long status;
	private java.util.Date dataCadastro;
	private java.util.Date dataAlteracao;
	private java.lang.Double peso;
	private java.lang.Double altura;

	// many to one
	private Empresa empresa;
	private Profissional profissional;
	private Usuario usuarioCadastro;
	private br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento;
	private br.com.ksisolucoes.vo.prontuario.basico.Cid cid;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_laudo_medicamentos_especiais"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: anamnese
	 */
	public java.lang.String getAnamnese () {
		return getPropertyValue(this, anamnese, PROP_ANAMNESE); 
	}

	/**
	 * Set the value related to the column: anamnese
	 * @param anamnese the anamnese value
	 */
	public void setAnamnese (java.lang.String anamnese) {
//        java.lang.String anamneseOld = this.anamnese;
		this.anamnese = anamnese;
//        this.getPropertyChangeSupport().firePropertyChange ("anamnese", anamneseOld, anamnese);
	}



	/**
	 * Return the value associated with the column: pac_realizou_trat_prev
	 */
	public java.lang.Long getPacienteRealizouTratamentoPreventivo () {
		return getPropertyValue(this, pacienteRealizouTratamentoPreventivo, PROP_PACIENTE_REALIZOU_TRATAMENTO_PREVENTIVO); 
	}

	/**
	 * Set the value related to the column: pac_realizou_trat_prev
	 * @param pacienteRealizouTratamentoPreventivo the pac_realizou_trat_prev value
	 */
	public void setPacienteRealizouTratamentoPreventivo (java.lang.Long pacienteRealizouTratamentoPreventivo) {
//        java.lang.Long pacienteRealizouTratamentoPreventivoOld = this.pacienteRealizouTratamentoPreventivo;
		this.pacienteRealizouTratamentoPreventivo = pacienteRealizouTratamentoPreventivo;
//        this.getPropertyChangeSupport().firePropertyChange ("pacienteRealizouTratamentoPreventivo", pacienteRealizouTratamentoPreventivoOld, pacienteRealizouTratamentoPreventivo);
	}



	/**
	 * Return the value associated with the column: obs_pac_realizou_trat_prev
	 */
	public java.lang.String getObsPacienteRealizouTratamentoPreventivo () {
		return getPropertyValue(this, obsPacienteRealizouTratamentoPreventivo, PROP_OBS_PACIENTE_REALIZOU_TRATAMENTO_PREVENTIVO); 
	}

	/**
	 * Set the value related to the column: obs_pac_realizou_trat_prev
	 * @param obsPacienteRealizouTratamentoPreventivo the obs_pac_realizou_trat_prev value
	 */
	public void setObsPacienteRealizouTratamentoPreventivo (java.lang.String obsPacienteRealizouTratamentoPreventivo) {
//        java.lang.String obsPacienteRealizouTratamentoPreventivoOld = this.obsPacienteRealizouTratamentoPreventivo;
		this.obsPacienteRealizouTratamentoPreventivo = obsPacienteRealizouTratamentoPreventivo;
//        this.getPropertyChangeSupport().firePropertyChange ("obsPacienteRealizouTratamentoPreventivo", obsPacienteRealizouTratamentoPreventivoOld, obsPacienteRealizouTratamentoPreventivo);
	}



	/**
	 * Return the value associated with the column: pac_considerado_incapaz
	 */
	public java.lang.Long getPacienteConsideradoIncapaz () {
		return getPropertyValue(this, pacienteConsideradoIncapaz, PROP_PACIENTE_CONSIDERADO_INCAPAZ); 
	}

	/**
	 * Set the value related to the column: pac_considerado_incapaz
	 * @param pacienteConsideradoIncapaz the pac_considerado_incapaz value
	 */
	public void setPacienteConsideradoIncapaz (java.lang.Long pacienteConsideradoIncapaz) {
//        java.lang.Long pacienteConsideradoIncapazOld = this.pacienteConsideradoIncapaz;
		this.pacienteConsideradoIncapaz = pacienteConsideradoIncapaz;
//        this.getPropertyChangeSupport().firePropertyChange ("pacienteConsideradoIncapaz", pacienteConsideradoIncapazOld, pacienteConsideradoIncapaz);
	}



	/**
	 * Return the value associated with the column: nm_responsavel
	 */
	public java.lang.String getNomeResponsavel () {
		return getPropertyValue(this, nomeResponsavel, PROP_NOME_RESPONSAVEL); 
	}

	/**
	 * Set the value related to the column: nm_responsavel
	 * @param nomeResponsavel the nm_responsavel value
	 */
	public void setNomeResponsavel (java.lang.String nomeResponsavel) {
//        java.lang.String nomeResponsavelOld = this.nomeResponsavel;
		this.nomeResponsavel = nomeResponsavel;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeResponsavel", nomeResponsavelOld, nomeResponsavel);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_alteracao
	 */
	public java.util.Date getDataAlteracao () {
		return getPropertyValue(this, dataAlteracao, PROP_DATA_ALTERACAO); 
	}

	/**
	 * Set the value related to the column: dt_alteracao
	 * @param dataAlteracao the dt_alteracao value
	 */
	public void setDataAlteracao (java.util.Date dataAlteracao) {
//        java.util.Date dataAlteracaoOld = this.dataAlteracao;
		this.dataAlteracao = dataAlteracao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAlteracao", dataAlteracaoOld, dataAlteracao);
	}



	/**
	 * Return the value associated with the column: peso
	 */
	public java.lang.Double getPeso () {
		return getPropertyValue(this, peso, PROP_PESO); 
	}

	/**
	 * Set the value related to the column: peso
	 * @param peso the peso value
	 */
	public void setPeso (java.lang.Double peso) {
//        java.lang.Double pesoOld = this.peso;
		this.peso = peso;
//        this.getPropertyChangeSupport().firePropertyChange ("peso", pesoOld, peso);
	}



	/**
	 * Return the value associated with the column: altura
	 */
	public java.lang.Double getAltura () {
		return getPropertyValue(this, altura, PROP_ALTURA); 
	}

	/**
	 * Set the value related to the column: altura
	 * @param altura the altura value
	 */
	public void setAltura (java.lang.Double altura) {
		this.altura = altura;
	}



	/**
	 * Return the value associated with the column: nr_atendimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Atendimento getAtendimento () {
		return getPropertyValue(this, atendimento, PROP_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: nr_atendimento
	 * @param atendimento the nr_atendimento value
	 */
	public void setAtendimento (br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoOld = this.atendimento;
		this.atendimento = atendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimento", atendimentoOld, atendimento);
	}


	public Profissional getProfissional() {
		return getPropertyValue(this, profissional, PROP_PROFISSIONAL); 
	}

	public void setProfissional(Profissional profissional) {
		this.profissional = profissional;
	}

	public Empresa getEmpresa() {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	public void setEmpresa(Empresa empresa) {
		this.empresa = empresa;
	}

	public Usuario getUsuarioCadastro() {
		return getPropertyValue(this, usuarioCadastro, PROP_USUARIO_CADASTRO); 
	}

	public void setUsuarioCadastro(Usuario usuarioCadastro) {
		this.usuarioCadastro = usuarioCadastro;
	}



	/**
	 * Return the value associated with the column: cd_cid
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Cid getCid () {
		return getPropertyValue(this, cid, PROP_CID); 
	}

	/**
	 * Set the value related to the column: cd_cid
	 * @param cid the cd_cid value
	 */
	public void setCid (br.com.ksisolucoes.vo.prontuario.basico.Cid cid) {
//        br.com.ksisolucoes.vo.prontuario.basico.Cid cidOld = this.cid;
		this.cid = cid;
//        this.getPropertyChangeSupport().firePropertyChange ("cid", cidOld, cid);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.LaudoMedicamentosEspeciais)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.LaudoMedicamentosEspeciais laudoMedicamentosEspeciais = (br.com.ksisolucoes.vo.prontuario.basico.LaudoMedicamentosEspeciais) obj;
			if (null == this.getCodigo() || null == laudoMedicamentosEspeciais.getCodigo()) return false;
			else return (this.getCodigo().equals(laudoMedicamentosEspeciais.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}