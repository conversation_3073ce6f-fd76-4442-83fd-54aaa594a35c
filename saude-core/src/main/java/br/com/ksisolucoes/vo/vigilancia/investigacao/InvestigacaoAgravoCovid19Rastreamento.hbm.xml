<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.investigacao">
    <class name="InvestigacaoAgravoCovid19Rastreamento" table="invest_covid_19_rastreamento">

        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_invest_covid_19_rastreamento"
        >
            <generator class="sequence">
                <param name="sequence">seq_invest_covid_19_rastreamento</param>
            </generator>
        </id>
        <version column="version" name="version" type="long"/>

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoCovid19"
                name="investigacaoAgravoCovid19"
                column="cd_invest_agr_covid_19"
                not-null="true"
        />

        <property
                name="nomeContato"
                column="nome_contato"
                type="java.lang.String"
        />

        <property
                name="relacaoContato"
                column="relacao_contato"
                type="java.lang.Long"
        />

        <property
                name="telefone1"
                column="telefone_1"
                type="java.lang.String"
                length="15"
        />

        <property
                name="telefone2"
                column="telefone_2"
                type="java.lang.String"
                length="15"
        />

        <property
                name="outrosMeios"
                column="outros_meios"
                type="java.lang.String"
        />

        <property
                name="rastreamento"
                column="rastreamento"
                type="java.lang.Long"
        />

        <property
                name="monitoramento"
                column="monitoramento"
                type="java.lang.Long"
        />

        <property
                name="dataCadastro"
                column="dt_cadastro"
                type="timestamp"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                name="usuario"
                column="cd_usuario"
        />
    </class>
</hibernate-mapping>
