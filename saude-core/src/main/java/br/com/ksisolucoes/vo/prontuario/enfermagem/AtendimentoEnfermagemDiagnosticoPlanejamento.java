package br.com.ksisolucoes.vo.prontuario.enfermagem;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.enfermagem.base.BaseAtendimentoEnfermagemDiagnosticoPlanejamento;

import java.io.Serializable;
import java.util.UUID;


public class AtendimentoEnfermagemDiagnosticoPlanejamento extends BaseAtendimentoEnfermagemDiagnosticoPlanejamento implements CodigoManager {
    private static final long serialVersionUID = 1L;

    public final String hashCompare = UUID.randomUUID().toString();

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public AtendimentoEnfermagemDiagnosticoPlanejamento() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public AtendimentoEnfermagemDiagnosticoPlanejamento(java.lang.Long codigo) {
        super(codigo);
    }

    /*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getHashCompare() {
        return hashCompare;
    }

    public String getResultadoEsperadoDescricaoFormatado() {
        if (getResultadoEsperadoNaoPadronizado() != null && !getResultadoEsperadoNaoPadronizado().isEmpty()) {
            return getResultadoEsperadoNaoPadronizado();
        }
        return getResultadoEsperado() == null ? "" : getResultadoEsperado().getDescricao();
    }

    public String getIntervencaoEnfermagemDescricaoFormatado() {
        if (getIntervencaoEnfermagemNaoPadronizado() != null && !getIntervencaoEnfermagemNaoPadronizado().isEmpty()) {
            return getIntervencaoEnfermagemNaoPadronizado();
        }
        return getIntervencaoEnfermagem() == null ? "" : getIntervencaoEnfermagem().getDescricao();
    }
}