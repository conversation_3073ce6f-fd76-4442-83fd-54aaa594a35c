package br.com.ksisolucoes.vo.entradas.recebimento;

import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.vo.entradas.recebimento.base.BaseRecebimentoGrupoEstoque;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;



public class RecebimentoGrupoEstoque extends BaseRecebimentoGrupoEstoque implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RecebimentoGrupoEstoque () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RecebimentoGrupoEstoque (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RecebimentoGrupoEstoque (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal registroItemNotaFiscal,
		br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura localizacaoEstrutura,
		java.lang.String grupoEstoque,
		java.util.Date dataCadastro,
		java.lang.Double quantidadeLote,
		java.lang.Double quantidadeTransferencia) {

		super (
			codigo,
			registroItemNotaFiscal,
			localizacaoEstrutura,
			grupoEstoque,
			dataCadastro,
			quantidadeLote,
			quantidadeTransferencia);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public Double getSaldoQuantidade(){
        return new Dinheiro(getQuantidadeLote()).subtrair(getQuantidadeTransferencia()).doubleValue();
    }
}