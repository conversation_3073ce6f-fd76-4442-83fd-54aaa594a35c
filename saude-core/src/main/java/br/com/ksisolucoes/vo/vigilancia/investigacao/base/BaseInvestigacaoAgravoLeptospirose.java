package br.com.ksisolucoes.vo.vigilancia.investigacao.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the investigacao_agr_leptospirose table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agr_leptospirose"
 */

public abstract class BaseInvestigacaoAgravoLeptospirose extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravoLeptospirose";
	public static final String PROP_ISOLAMENTO_S_N = "isolamentoSN";
	public static final String PROP_SITUACAO_RISCO_CRIACAO_ANIMAIS = "situacaoRiscoCriacaoAnimais";
	public static final String PROP_SINAIS_SINTOMAS_DIARREIA = "sinaisSintomasDiarreia";
	public static final String PROP_SINAIS_SINTOMAS_ALTERACOES_CARDIACAS = "sinaisSintomasAlteracoesCardiacas";
	public static final String PROP_RTPCR_DATA_COLETA = "rtpcrDataColeta";
	public static final String PROP_SOROLOGIA_IGM_RESULTADO2 = "sorologiaIgmResultado2";
	public static final String PROP_BAIRRO_LOCAL_INFECCAO = "bairroLocalInfeccao";
	public static final String PROP_SOROLOGIA_IGM_RESULTADO1 = "sorologiaIgmResultado1";
	public static final String PROP_SINAIS_SINTOMAS_OUTROS = "sinaisSintomasOutros";
	public static final String PROP_DATA_ENCERRAMENTO = "dataEncerramento";
	public static final String PROP_CASO_AUTOCTONE = "casoAutoctone";
	public static final String PROP_CASOS_ANTERIORES_LOCAL_HUMANOS = "casosAnterioresLocalHumanos";
	public static final String PROP_SITUACAO_RISCO_LIXO_ENTULHO = "situacaoRiscoLixoEntulho";
	public static final String PROP_AMBIENTE_INFECCAO = "ambienteInfeccao";
	public static final String PROP_DATA_OBITO = "dataObito";
	public static final String PROP_AREA_PROVAVEL_INFECCAO = "areaProvavelInfeccao";
	public static final String PROP_HOSPITAL = "hospital";
	public static final String PROP_CIDADE_LOCAL_INFECCAO = "cidadeLocalInfeccao";
	public static final String PROP_SINAIS_SINTOMAS_INSUFICIENCIA_RENAL = "sinaisSintomasInsuficienciaRenal";
	public static final String PROP_SITUACAO_RISCO_RIO_CORREGO_LAGOA_REPRESA = "situacaoRiscoRioCorregoLagoaRepresa";
	public static final String PROP_MICROAGLUTINACAO_AMOSTRA1_SOROVAR2 = "microaglutinacaoAmostra1Sorovar2";
	public static final String PROP_CRITERIO_CONFIRMACAO = "criterioConfirmacao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_ISOLAMENTO_DATA_COLETA = "isolamentoDataColeta";
	public static final String PROP_SITUACAO_RISCO_CAIXA_AGUA = "situacaoRiscoCaixaAgua";
	public static final String PROP_CLASSIFICACAO_FINAL = "classificacaoFinal";
	public static final String PROP_CASOS_ANTERIORES_LOCAL_ANIMAIS = "casosAnterioresLocalAnimais";
	public static final String PROP_SITUACAO_RISCO_PLANTIO_COLHEITA = "situacaoRiscoPlantioColheita";
	public static final String PROP_MICROAGLUTINACAO_AMOSTRA1_SOROVAR1 = "microaglutinacaoAmostra1Sorovar1";
	public static final String PROP_SINAIS_SINTOMAS_MIALGIA = "sinaisSintomasMialgia";
	public static final String PROP_EVOLUCAO_CASO = "evolucaoCaso";
	public static final String PROP_SITUACAO_RISCO_FOSSA_CAIXA_GORDURA_ESGOTO = "situacaoRiscoFossaCaixaGorduraEsgoto";
	public static final String PROP_DATA_ALTA = "dataAlta";
	public static final String PROP_SITUACAO_RISCO_AGUA_LAMA_ENCHENTE = "situacaoRiscoAguaLamaEnchente";
	public static final String PROP_SINAIS_SINTOMAS_ICTERICIA = "sinaisSintomasIctericia";
	public static final String PROP_SINAIS_SINTOMAS_FEBRE = "sinaisSintomasFebre";
	public static final String PROP_SINAIS_SINTOMAS_VOMITO = "sinaisSintomasVomito";
	public static final String PROP_MICROAGLUTINACAO_DATA_COLETA2 = "microaglutinacaoDataColeta2";
	public static final String PROP_DATA_INVESTIGACAO = "dataInvestigacao";
	public static final String PROP_MICROAGLUTINACAO_DATA_COLETA1 = "microaglutinacaoDataColeta1";
	public static final String PROP_SINAIS_SINTOMAS_ALTERACOES_RESPIRATORIAS = "sinaisSintomasAlteracoesRespiratorias";
	public static final String PROP_DATA_ATENDIMENTO = "dataAtendimento";
	public static final String PROP_USUARIO_ENCERRAMENTO = "usuarioEncerramento";
	public static final String PROP_FLAG_INFORMACOES_COMPLEMENTARES = "flagInformacoesComplementares";
	public static final String PROP_SINAIS_SINTOMAS_MENINGISMO = "sinaisSintomasMeningismo";
	public static final String PROP_SINAIS_SINTOMAS_PROSTRACAO = "sinaisSintomasProstracao";
	public static final String PROP_DISTRITO_LOCAL_INFECCAO = "distritoLocalInfeccao";
	public static final String PROP_RTPCR_S_N = "rtpcrSN";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_SOROLOGIA_IGM_ELISA_S_N = "sorologiaIgmElisaSN";
	public static final String PROP_SITUACAO_RISCO_LOCAL_ROEDORES = "situacaoRiscoLocalRoedores";
	public static final String PROP_SINAIS_SINTOMAS_DOR_PANTURRILHA = "sinaisSintomasDorPanturrilha";
	public static final String PROP_IMUNOHISTOQUIMICA_S_N = "imunohistoquimicaSN";
	public static final String PROP_DOENCA_RELACIONADA_TRABALHO = "doencaRelacionadaTrabalho";
	public static final String PROP_IMUNOHISTOQUIMICA_DATA_COLETA = "imunohistoquimicaDataColeta";
	public static final String PROP_MICROAGLUTINACAO_S_N = "microaglutinacaoSN";
	public static final String PROP_MICROAGLUTINACAO_AMOSTRA2_SOROVAR2 = "microaglutinacaoAmostra2Sorovar2";
	public static final String PROP_MICROAGLUTINACAO_AMOSTRA2_SOROVAR1 = "microaglutinacaoAmostra2Sorovar1";
	public static final String PROP_ISOLAMENTO_RESULTADO = "isolamentoResultado";
	public static final String PROP_HOSPITALIZACAO = "hospitalizacao";
	public static final String PROP_REGISTRO_AGRAVO = "registroAgravo";
	public static final String PROP_SITUACAO_RISCO_TERRENO_BALDIO = "situacaoRiscoTerrenoBaldio";
	public static final String PROP_OCUPACAO_CBO = "ocupacaoCbo";
	public static final String PROP_SINAIS_SINTOMAS_HEMORRAGIA_PULMONAR = "sinaisSintomasHemorragiaPulmonar";
	public static final String PROP_IMUNOHISTOQUIMICA_RESULTADO = "imunohistoquimicaResultado";
	public static final String PROP_SITUACAO_RISCO_OUTROS = "situacaoRiscoOutros";
	public static final String PROP_SOROLOGIA_IGM_DATA_COLETA1 = "sorologiaIgmDataColeta1";
	public static final String PROP_SITUACAO_RISCO_ROEDORES_DIRETAMENTE = "situacaoRiscoRoedoresDiretamente";
	public static final String PROP_SINAIS_SINTOMAS_CONGESTAO_CONJUNTIVAL = "sinaisSintomasCongestaoConjuntival";
	public static final String PROP_SINAIS_SINTOMAS_CEFALEIA = "sinaisSintomasCefaleia";
	public static final String PROP_SOROLOGIA_IGM_DATA_COLETA2 = "sorologiaIgmDataColeta2";
	public static final String PROP_MICROAGLUTINACAO_RESULTADO_AMOSTRA1 = "microaglutinacaoResultadoAmostra1";
	public static final String PROP_RTPCR_RESULTADO = "rtpcrResultado";
	public static final String PROP_PAIS_LOCAL_INFECCAO = "paisLocalInfeccao";
	public static final String PROP_SITUACAO_RISCO_ARMAZENAMENTO_GRAOS_ALIMENTOS = "situacaoRiscoArmazenamentoGraosAlimentos";
	public static final String PROP_SINAIS_SINTOMAS_OUTRAS_HEMORRAGIAS = "sinaisSintomasOutrasHemorragias";
	public static final String PROP_MICROAGLUTINACAO_RESULTADO_AMOSTRA2 = "microaglutinacaoResultadoAmostra2";
	public static final String PROP_DATA_INTERNACAO = "dataInternacao";


	// constructors
	public BaseInvestigacaoAgravoLeptospirose () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravoLeptospirose (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseInvestigacaoAgravoLeptospirose (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
		br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo,
		java.lang.String flagInformacoesComplementares) {

		this.setCodigo(codigo);
		this.setRegistroAgravo(registroAgravo);
		this.setOcupacaoCbo(ocupacaoCbo);
		this.setFlagInformacoesComplementares(flagInformacoesComplementares);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String flagInformacoesComplementares;
	private java.util.Date dataInvestigacao;
	private java.lang.Long situacaoRiscoAguaLamaEnchente;
	private java.lang.Long situacaoRiscoCriacaoAnimais;
	private java.lang.Long situacaoRiscoCaixaAgua;
	private java.lang.Long situacaoRiscoFossaCaixaGorduraEsgoto;
	private java.lang.Long situacaoRiscoLocalRoedores;
	private java.lang.Long situacaoRiscoPlantioColheita;
	private java.lang.Long situacaoRiscoRioCorregoLagoaRepresa;
	private java.lang.Long situacaoRiscoRoedoresDiretamente;
	private java.lang.Long situacaoRiscoArmazenamentoGraosAlimentos;
	private java.lang.Long situacaoRiscoTerrenoBaldio;
	private java.lang.Long situacaoRiscoLixoEntulho;
	private java.lang.String situacaoRiscoOutros;
	private java.lang.Long casosAnterioresLocalHumanos;
	private java.lang.Long casosAnterioresLocalAnimais;
	private java.util.Date dataAtendimento;
	private java.lang.Long sinaisSintomasFebre;
	private java.lang.Long sinaisSintomasMialgia;
	private java.lang.Long sinaisSintomasCefaleia;
	private java.lang.Long sinaisSintomasProstracao;
	private java.lang.Long sinaisSintomasCongestaoConjuntival;
	private java.lang.Long sinaisSintomasDorPanturrilha;
	private java.lang.Long sinaisSintomasVomito;
	private java.lang.Long sinaisSintomasDiarreia;
	private java.lang.Long sinaisSintomasIctericia;
	private java.lang.Long sinaisSintomasInsuficienciaRenal;
	private java.lang.Long sinaisSintomasAlteracoesRespiratorias;
	private java.lang.Long sinaisSintomasAlteracoesCardiacas;
	private java.lang.Long sinaisSintomasHemorragiaPulmonar;
	private java.lang.Long sinaisSintomasOutrasHemorragias;
	private java.lang.Long sinaisSintomasMeningismo;
	private java.lang.String sinaisSintomasOutros;
	private java.lang.Long hospitalizacao;
	private java.util.Date dataInternacao;
	private java.util.Date dataAlta;
	private java.lang.Long sorologiaIgmElisaSN;
	private java.util.Date sorologiaIgmDataColeta1;
	private java.lang.Long sorologiaIgmResultado1;
	private java.util.Date sorologiaIgmDataColeta2;
	private java.lang.Long sorologiaIgmResultado2;
	private java.lang.Long microaglutinacaoSN;
	private java.util.Date microaglutinacaoDataColeta1;
	private java.lang.String microaglutinacaoAmostra1Sorovar1;
	private java.lang.String microaglutinacaoAmostra1Sorovar2;
	private java.lang.Long microaglutinacaoResultadoAmostra1;
	private java.util.Date microaglutinacaoDataColeta2;
	private java.lang.String microaglutinacaoAmostra2Sorovar1;
	private java.lang.String microaglutinacaoAmostra2Sorovar2;
	private java.lang.Long microaglutinacaoResultadoAmostra2;
	private java.lang.Long isolamentoSN;
	private java.util.Date isolamentoDataColeta;
	private java.lang.Long isolamentoResultado;
	private java.lang.Long imunohistoquimicaSN;
	private java.util.Date imunohistoquimicaDataColeta;
	private java.lang.Long imunohistoquimicaResultado;
	private java.lang.Long rtpcrSN;
	private java.util.Date rtpcrDataColeta;
	private java.lang.Long rtpcrResultado;
	private java.lang.Long casoAutoctone;
	private java.lang.String distritoLocalInfeccao;
	private java.lang.String bairroLocalInfeccao;
	private java.lang.Long classificacaoFinal;
	private java.lang.Long criterioConfirmacao;
	private java.lang.Long areaProvavelInfeccao;
	private java.lang.Long ambienteInfeccao;
	private java.lang.Long doencaRelacionadaTrabalho;
	private java.lang.Long evolucaoCaso;
	private java.util.Date dataObito;
	private java.lang.String observacao;
	private java.util.Date dataEncerramento;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo;
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo;
	private br.com.ksisolucoes.vo.basico.Empresa hospital;
	private br.com.ksisolucoes.vo.basico.Pais paisLocalInfeccao;
	private br.com.ksisolucoes.vo.basico.Cidade cidadeLocalInfeccao;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_invest_agr_leptospirose"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: flag_informacoes_complementares
	 */
	public java.lang.String getFlagInformacoesComplementares () {
		return getPropertyValue(this, flagInformacoesComplementares, PROP_FLAG_INFORMACOES_COMPLEMENTARES); 
	}

	/**
	 * Set the value related to the column: flag_informacoes_complementares
	 * @param flagInformacoesComplementares the flag_informacoes_complementares value
	 */
	public void setFlagInformacoesComplementares (java.lang.String flagInformacoesComplementares) {
//        java.lang.String flagInformacoesComplementaresOld = this.flagInformacoesComplementares;
		this.flagInformacoesComplementares = flagInformacoesComplementares;
//        this.getPropertyChangeSupport().firePropertyChange ("flagInformacoesComplementares", flagInformacoesComplementaresOld, flagInformacoesComplementares);
	}



	/**
	 * Return the value associated with the column: dt_investigacao
	 */
	public java.util.Date getDataInvestigacao () {
		return getPropertyValue(this, dataInvestigacao, PROP_DATA_INVESTIGACAO); 
	}

	/**
	 * Set the value related to the column: dt_investigacao
	 * @param dataInvestigacao the dt_investigacao value
	 */
	public void setDataInvestigacao (java.util.Date dataInvestigacao) {
//        java.util.Date dataInvestigacaoOld = this.dataInvestigacao;
		this.dataInvestigacao = dataInvestigacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInvestigacao", dataInvestigacaoOld, dataInvestigacao);
	}



	/**
	 * Return the value associated with the column: situacao_risco_agua_lama_enchente
	 */
	public java.lang.Long getSituacaoRiscoAguaLamaEnchente () {
		return getPropertyValue(this, situacaoRiscoAguaLamaEnchente, PROP_SITUACAO_RISCO_AGUA_LAMA_ENCHENTE); 
	}

	/**
	 * Set the value related to the column: situacao_risco_agua_lama_enchente
	 * @param situacaoRiscoAguaLamaEnchente the situacao_risco_agua_lama_enchente value
	 */
	public void setSituacaoRiscoAguaLamaEnchente (java.lang.Long situacaoRiscoAguaLamaEnchente) {
//        java.lang.Long situacaoRiscoAguaLamaEnchenteOld = this.situacaoRiscoAguaLamaEnchente;
		this.situacaoRiscoAguaLamaEnchente = situacaoRiscoAguaLamaEnchente;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoAguaLamaEnchente", situacaoRiscoAguaLamaEnchenteOld, situacaoRiscoAguaLamaEnchente);
	}



	/**
	 * Return the value associated with the column: situacao_risco_criacao_animais
	 */
	public java.lang.Long getSituacaoRiscoCriacaoAnimais () {
		return getPropertyValue(this, situacaoRiscoCriacaoAnimais, PROP_SITUACAO_RISCO_CRIACAO_ANIMAIS); 
	}

	/**
	 * Set the value related to the column: situacao_risco_criacao_animais
	 * @param situacaoRiscoCriacaoAnimais the situacao_risco_criacao_animais value
	 */
	public void setSituacaoRiscoCriacaoAnimais (java.lang.Long situacaoRiscoCriacaoAnimais) {
//        java.lang.Long situacaoRiscoCriacaoAnimaisOld = this.situacaoRiscoCriacaoAnimais;
		this.situacaoRiscoCriacaoAnimais = situacaoRiscoCriacaoAnimais;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoCriacaoAnimais", situacaoRiscoCriacaoAnimaisOld, situacaoRiscoCriacaoAnimais);
	}



	/**
	 * Return the value associated with the column: situacao_risco_caixa_agua
	 */
	public java.lang.Long getSituacaoRiscoCaixaAgua () {
		return getPropertyValue(this, situacaoRiscoCaixaAgua, PROP_SITUACAO_RISCO_CAIXA_AGUA); 
	}

	/**
	 * Set the value related to the column: situacao_risco_caixa_agua
	 * @param situacaoRiscoCaixaAgua the situacao_risco_caixa_agua value
	 */
	public void setSituacaoRiscoCaixaAgua (java.lang.Long situacaoRiscoCaixaAgua) {
//        java.lang.Long situacaoRiscoCaixaAguaOld = this.situacaoRiscoCaixaAgua;
		this.situacaoRiscoCaixaAgua = situacaoRiscoCaixaAgua;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoCaixaAgua", situacaoRiscoCaixaAguaOld, situacaoRiscoCaixaAgua);
	}



	/**
	 * Return the value associated with the column: situacao_risco_fossa_gordura_esgoto
	 */
	public java.lang.Long getSituacaoRiscoFossaCaixaGorduraEsgoto () {
		return getPropertyValue(this, situacaoRiscoFossaCaixaGorduraEsgoto, PROP_SITUACAO_RISCO_FOSSA_CAIXA_GORDURA_ESGOTO); 
	}

	/**
	 * Set the value related to the column: situacao_risco_fossa_gordura_esgoto
	 * @param situacaoRiscoFossaCaixaGorduraEsgoto the situacao_risco_fossa_gordura_esgoto value
	 */
	public void setSituacaoRiscoFossaCaixaGorduraEsgoto (java.lang.Long situacaoRiscoFossaCaixaGorduraEsgoto) {
//        java.lang.Long situacaoRiscoFossaCaixaGorduraEsgotoOld = this.situacaoRiscoFossaCaixaGorduraEsgoto;
		this.situacaoRiscoFossaCaixaGorduraEsgoto = situacaoRiscoFossaCaixaGorduraEsgoto;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoFossaCaixaGorduraEsgoto", situacaoRiscoFossaCaixaGorduraEsgotoOld, situacaoRiscoFossaCaixaGorduraEsgoto);
	}



	/**
	 * Return the value associated with the column: situacao_risco_local_roedores
	 */
	public java.lang.Long getSituacaoRiscoLocalRoedores () {
		return getPropertyValue(this, situacaoRiscoLocalRoedores, PROP_SITUACAO_RISCO_LOCAL_ROEDORES); 
	}

	/**
	 * Set the value related to the column: situacao_risco_local_roedores
	 * @param situacaoRiscoLocalRoedores the situacao_risco_local_roedores value
	 */
	public void setSituacaoRiscoLocalRoedores (java.lang.Long situacaoRiscoLocalRoedores) {
//        java.lang.Long situacaoRiscoLocalRoedoresOld = this.situacaoRiscoLocalRoedores;
		this.situacaoRiscoLocalRoedores = situacaoRiscoLocalRoedores;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoLocalRoedores", situacaoRiscoLocalRoedoresOld, situacaoRiscoLocalRoedores);
	}



	/**
	 * Return the value associated with the column: situacao_risco_plantio_colheita
	 */
	public java.lang.Long getSituacaoRiscoPlantioColheita () {
		return getPropertyValue(this, situacaoRiscoPlantioColheita, PROP_SITUACAO_RISCO_PLANTIO_COLHEITA); 
	}

	/**
	 * Set the value related to the column: situacao_risco_plantio_colheita
	 * @param situacaoRiscoPlantioColheita the situacao_risco_plantio_colheita value
	 */
	public void setSituacaoRiscoPlantioColheita (java.lang.Long situacaoRiscoPlantioColheita) {
//        java.lang.Long situacaoRiscoPlantioColheitaOld = this.situacaoRiscoPlantioColheita;
		this.situacaoRiscoPlantioColheita = situacaoRiscoPlantioColheita;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoPlantioColheita", situacaoRiscoPlantioColheitaOld, situacaoRiscoPlantioColheita);
	}



	/**
	 * Return the value associated with the column: situacao_risco_rio_corrego_lagoa_represa
	 */
	public java.lang.Long getSituacaoRiscoRioCorregoLagoaRepresa () {
		return getPropertyValue(this, situacaoRiscoRioCorregoLagoaRepresa, PROP_SITUACAO_RISCO_RIO_CORREGO_LAGOA_REPRESA); 
	}

	/**
	 * Set the value related to the column: situacao_risco_rio_corrego_lagoa_represa
	 * @param situacaoRiscoRioCorregoLagoaRepresa the situacao_risco_rio_corrego_lagoa_represa value
	 */
	public void setSituacaoRiscoRioCorregoLagoaRepresa (java.lang.Long situacaoRiscoRioCorregoLagoaRepresa) {
//        java.lang.Long situacaoRiscoRioCorregoLagoaRepresaOld = this.situacaoRiscoRioCorregoLagoaRepresa;
		this.situacaoRiscoRioCorregoLagoaRepresa = situacaoRiscoRioCorregoLagoaRepresa;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoRioCorregoLagoaRepresa", situacaoRiscoRioCorregoLagoaRepresaOld, situacaoRiscoRioCorregoLagoaRepresa);
	}



	/**
	 * Return the value associated with the column: situacao_risco_roedores_diretamente
	 */
	public java.lang.Long getSituacaoRiscoRoedoresDiretamente () {
		return getPropertyValue(this, situacaoRiscoRoedoresDiretamente, PROP_SITUACAO_RISCO_ROEDORES_DIRETAMENTE); 
	}

	/**
	 * Set the value related to the column: situacao_risco_roedores_diretamente
	 * @param situacaoRiscoRoedoresDiretamente the situacao_risco_roedores_diretamente value
	 */
	public void setSituacaoRiscoRoedoresDiretamente (java.lang.Long situacaoRiscoRoedoresDiretamente) {
//        java.lang.Long situacaoRiscoRoedoresDiretamenteOld = this.situacaoRiscoRoedoresDiretamente;
		this.situacaoRiscoRoedoresDiretamente = situacaoRiscoRoedoresDiretamente;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoRoedoresDiretamente", situacaoRiscoRoedoresDiretamenteOld, situacaoRiscoRoedoresDiretamente);
	}



	/**
	 * Return the value associated with the column: situacao_risco_armazenamento_graos_alimentos
	 */
	public java.lang.Long getSituacaoRiscoArmazenamentoGraosAlimentos () {
		return getPropertyValue(this, situacaoRiscoArmazenamentoGraosAlimentos, PROP_SITUACAO_RISCO_ARMAZENAMENTO_GRAOS_ALIMENTOS); 
	}

	/**
	 * Set the value related to the column: situacao_risco_armazenamento_graos_alimentos
	 * @param situacaoRiscoArmazenamentoGraosAlimentos the situacao_risco_armazenamento_graos_alimentos value
	 */
	public void setSituacaoRiscoArmazenamentoGraosAlimentos (java.lang.Long situacaoRiscoArmazenamentoGraosAlimentos) {
//        java.lang.Long situacaoRiscoArmazenamentoGraosAlimentosOld = this.situacaoRiscoArmazenamentoGraosAlimentos;
		this.situacaoRiscoArmazenamentoGraosAlimentos = situacaoRiscoArmazenamentoGraosAlimentos;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoArmazenamentoGraosAlimentos", situacaoRiscoArmazenamentoGraosAlimentosOld, situacaoRiscoArmazenamentoGraosAlimentos);
	}



	/**
	 * Return the value associated with the column: situacao_risco_terreno_baldio
	 */
	public java.lang.Long getSituacaoRiscoTerrenoBaldio () {
		return getPropertyValue(this, situacaoRiscoTerrenoBaldio, PROP_SITUACAO_RISCO_TERRENO_BALDIO); 
	}

	/**
	 * Set the value related to the column: situacao_risco_terreno_baldio
	 * @param situacaoRiscoTerrenoBaldio the situacao_risco_terreno_baldio value
	 */
	public void setSituacaoRiscoTerrenoBaldio (java.lang.Long situacaoRiscoTerrenoBaldio) {
//        java.lang.Long situacaoRiscoTerrenoBaldioOld = this.situacaoRiscoTerrenoBaldio;
		this.situacaoRiscoTerrenoBaldio = situacaoRiscoTerrenoBaldio;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoTerrenoBaldio", situacaoRiscoTerrenoBaldioOld, situacaoRiscoTerrenoBaldio);
	}



	/**
	 * Return the value associated with the column: situacao_risco_lixo_entulho
	 */
	public java.lang.Long getSituacaoRiscoLixoEntulho () {
		return getPropertyValue(this, situacaoRiscoLixoEntulho, PROP_SITUACAO_RISCO_LIXO_ENTULHO); 
	}

	/**
	 * Set the value related to the column: situacao_risco_lixo_entulho
	 * @param situacaoRiscoLixoEntulho the situacao_risco_lixo_entulho value
	 */
	public void setSituacaoRiscoLixoEntulho (java.lang.Long situacaoRiscoLixoEntulho) {
//        java.lang.Long situacaoRiscoLixoEntulhoOld = this.situacaoRiscoLixoEntulho;
		this.situacaoRiscoLixoEntulho = situacaoRiscoLixoEntulho;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoLixoEntulho", situacaoRiscoLixoEntulhoOld, situacaoRiscoLixoEntulho);
	}



	/**
	 * Return the value associated with the column: situacao_risco_outtos
	 */
	public java.lang.String getSituacaoRiscoOutros () {
		return getPropertyValue(this, situacaoRiscoOutros, PROP_SITUACAO_RISCO_OUTROS); 
	}

	/**
	 * Set the value related to the column: situacao_risco_outtos
	 * @param situacaoRiscoOutros the situacao_risco_outtos value
	 */
	public void setSituacaoRiscoOutros (java.lang.String situacaoRiscoOutros) {
//        java.lang.String situacaoRiscoOutrosOld = this.situacaoRiscoOutros;
		this.situacaoRiscoOutros = situacaoRiscoOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoRiscoOutros", situacaoRiscoOutrosOld, situacaoRiscoOutros);
	}



	/**
	 * Return the value associated with the column: casos_anteriores_local_humanos
	 */
	public java.lang.Long getCasosAnterioresLocalHumanos () {
		return getPropertyValue(this, casosAnterioresLocalHumanos, PROP_CASOS_ANTERIORES_LOCAL_HUMANOS); 
	}

	/**
	 * Set the value related to the column: casos_anteriores_local_humanos
	 * @param casosAnterioresLocalHumanos the casos_anteriores_local_humanos value
	 */
	public void setCasosAnterioresLocalHumanos (java.lang.Long casosAnterioresLocalHumanos) {
//        java.lang.Long casosAnterioresLocalHumanosOld = this.casosAnterioresLocalHumanos;
		this.casosAnterioresLocalHumanos = casosAnterioresLocalHumanos;
//        this.getPropertyChangeSupport().firePropertyChange ("casosAnterioresLocalHumanos", casosAnterioresLocalHumanosOld, casosAnterioresLocalHumanos);
	}



	/**
	 * Return the value associated with the column: casos_anteriores_local_animais
	 */
	public java.lang.Long getCasosAnterioresLocalAnimais () {
		return getPropertyValue(this, casosAnterioresLocalAnimais, PROP_CASOS_ANTERIORES_LOCAL_ANIMAIS); 
	}

	/**
	 * Set the value related to the column: casos_anteriores_local_animais
	 * @param casosAnterioresLocalAnimais the casos_anteriores_local_animais value
	 */
	public void setCasosAnterioresLocalAnimais (java.lang.Long casosAnterioresLocalAnimais) {
//        java.lang.Long casosAnterioresLocalAnimaisOld = this.casosAnterioresLocalAnimais;
		this.casosAnterioresLocalAnimais = casosAnterioresLocalAnimais;
//        this.getPropertyChangeSupport().firePropertyChange ("casosAnterioresLocalAnimais", casosAnterioresLocalAnimaisOld, casosAnterioresLocalAnimais);
	}



	/**
	 * Return the value associated with the column: dt_atendimento
	 */
	public java.util.Date getDataAtendimento () {
		return getPropertyValue(this, dataAtendimento, PROP_DATA_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: dt_atendimento
	 * @param dataAtendimento the dt_atendimento value
	 */
	public void setDataAtendimento (java.util.Date dataAtendimento) {
//        java.util.Date dataAtendimentoOld = this.dataAtendimento;
		this.dataAtendimento = dataAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAtendimento", dataAtendimentoOld, dataAtendimento);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_febre
	 */
	public java.lang.Long getSinaisSintomasFebre () {
		return getPropertyValue(this, sinaisSintomasFebre, PROP_SINAIS_SINTOMAS_FEBRE); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_febre
	 * @param sinaisSintomasFebre the sinais_sintomas_febre value
	 */
	public void setSinaisSintomasFebre (java.lang.Long sinaisSintomasFebre) {
//        java.lang.Long sinaisSintomasFebreOld = this.sinaisSintomasFebre;
		this.sinaisSintomasFebre = sinaisSintomasFebre;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasFebre", sinaisSintomasFebreOld, sinaisSintomasFebre);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_mialgia
	 */
	public java.lang.Long getSinaisSintomasMialgia () {
		return getPropertyValue(this, sinaisSintomasMialgia, PROP_SINAIS_SINTOMAS_MIALGIA); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_mialgia
	 * @param sinaisSintomasMialgia the sinais_sintomas_mialgia value
	 */
	public void setSinaisSintomasMialgia (java.lang.Long sinaisSintomasMialgia) {
//        java.lang.Long sinaisSintomasMialgiaOld = this.sinaisSintomasMialgia;
		this.sinaisSintomasMialgia = sinaisSintomasMialgia;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasMialgia", sinaisSintomasMialgiaOld, sinaisSintomasMialgia);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_cefaleia
	 */
	public java.lang.Long getSinaisSintomasCefaleia () {
		return getPropertyValue(this, sinaisSintomasCefaleia, PROP_SINAIS_SINTOMAS_CEFALEIA); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_cefaleia
	 * @param sinaisSintomasCefaleia the sinais_sintomas_cefaleia value
	 */
	public void setSinaisSintomasCefaleia (java.lang.Long sinaisSintomasCefaleia) {
//        java.lang.Long sinaisSintomasCefaleiaOld = this.sinaisSintomasCefaleia;
		this.sinaisSintomasCefaleia = sinaisSintomasCefaleia;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasCefaleia", sinaisSintomasCefaleiaOld, sinaisSintomasCefaleia);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_prostracao
	 */
	public java.lang.Long getSinaisSintomasProstracao () {
		return getPropertyValue(this, sinaisSintomasProstracao, PROP_SINAIS_SINTOMAS_PROSTRACAO); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_prostracao
	 * @param sinaisSintomasProstracao the sinais_sintomas_prostracao value
	 */
	public void setSinaisSintomasProstracao (java.lang.Long sinaisSintomasProstracao) {
//        java.lang.Long sinaisSintomasProstracaoOld = this.sinaisSintomasProstracao;
		this.sinaisSintomasProstracao = sinaisSintomasProstracao;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasProstracao", sinaisSintomasProstracaoOld, sinaisSintomasProstracao);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_congestao_conjuntival
	 */
	public java.lang.Long getSinaisSintomasCongestaoConjuntival () {
		return getPropertyValue(this, sinaisSintomasCongestaoConjuntival, PROP_SINAIS_SINTOMAS_CONGESTAO_CONJUNTIVAL); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_congestao_conjuntival
	 * @param sinaisSintomasCongestaoConjuntival the sinais_sintomas_congestao_conjuntival value
	 */
	public void setSinaisSintomasCongestaoConjuntival (java.lang.Long sinaisSintomasCongestaoConjuntival) {
//        java.lang.Long sinaisSintomasCongestaoConjuntivalOld = this.sinaisSintomasCongestaoConjuntival;
		this.sinaisSintomasCongestaoConjuntival = sinaisSintomasCongestaoConjuntival;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasCongestaoConjuntival", sinaisSintomasCongestaoConjuntivalOld, sinaisSintomasCongestaoConjuntival);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_dor_panturrilha
	 */
	public java.lang.Long getSinaisSintomasDorPanturrilha () {
		return getPropertyValue(this, sinaisSintomasDorPanturrilha, PROP_SINAIS_SINTOMAS_DOR_PANTURRILHA); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_dor_panturrilha
	 * @param sinaisSintomasDorPanturrilha the sinais_sintomas_dor_panturrilha value
	 */
	public void setSinaisSintomasDorPanturrilha (java.lang.Long sinaisSintomasDorPanturrilha) {
//        java.lang.Long sinaisSintomasDorPanturrilhaOld = this.sinaisSintomasDorPanturrilha;
		this.sinaisSintomasDorPanturrilha = sinaisSintomasDorPanturrilha;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasDorPanturrilha", sinaisSintomasDorPanturrilhaOld, sinaisSintomasDorPanturrilha);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_vomito
	 */
	public java.lang.Long getSinaisSintomasVomito () {
		return getPropertyValue(this, sinaisSintomasVomito, PROP_SINAIS_SINTOMAS_VOMITO); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_vomito
	 * @param sinaisSintomasVomito the sinais_sintomas_vomito value
	 */
	public void setSinaisSintomasVomito (java.lang.Long sinaisSintomasVomito) {
//        java.lang.Long sinaisSintomasVomitoOld = this.sinaisSintomasVomito;
		this.sinaisSintomasVomito = sinaisSintomasVomito;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasVomito", sinaisSintomasVomitoOld, sinaisSintomasVomito);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_diarreia
	 */
	public java.lang.Long getSinaisSintomasDiarreia () {
		return getPropertyValue(this, sinaisSintomasDiarreia, PROP_SINAIS_SINTOMAS_DIARREIA); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_diarreia
	 * @param sinaisSintomasDiarreia the sinais_sintomas_diarreia value
	 */
	public void setSinaisSintomasDiarreia (java.lang.Long sinaisSintomasDiarreia) {
//        java.lang.Long sinaisSintomasDiarreiaOld = this.sinaisSintomasDiarreia;
		this.sinaisSintomasDiarreia = sinaisSintomasDiarreia;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasDiarreia", sinaisSintomasDiarreiaOld, sinaisSintomasDiarreia);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_ictericia
	 */
	public java.lang.Long getSinaisSintomasIctericia () {
		return getPropertyValue(this, sinaisSintomasIctericia, PROP_SINAIS_SINTOMAS_ICTERICIA); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_ictericia
	 * @param sinaisSintomasIctericia the sinais_sintomas_ictericia value
	 */
	public void setSinaisSintomasIctericia (java.lang.Long sinaisSintomasIctericia) {
//        java.lang.Long sinaisSintomasIctericiaOld = this.sinaisSintomasIctericia;
		this.sinaisSintomasIctericia = sinaisSintomasIctericia;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasIctericia", sinaisSintomasIctericiaOld, sinaisSintomasIctericia);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_insuficiencia_renal
	 */
	public java.lang.Long getSinaisSintomasInsuficienciaRenal () {
		return getPropertyValue(this, sinaisSintomasInsuficienciaRenal, PROP_SINAIS_SINTOMAS_INSUFICIENCIA_RENAL); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_insuficiencia_renal
	 * @param sinaisSintomasInsuficienciaRenal the sinais_sintomas_insuficiencia_renal value
	 */
	public void setSinaisSintomasInsuficienciaRenal (java.lang.Long sinaisSintomasInsuficienciaRenal) {
//        java.lang.Long sinaisSintomasInsuficienciaRenalOld = this.sinaisSintomasInsuficienciaRenal;
		this.sinaisSintomasInsuficienciaRenal = sinaisSintomasInsuficienciaRenal;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasInsuficienciaRenal", sinaisSintomasInsuficienciaRenalOld, sinaisSintomasInsuficienciaRenal);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_alteracoes_respiratorias
	 */
	public java.lang.Long getSinaisSintomasAlteracoesRespiratorias () {
		return getPropertyValue(this, sinaisSintomasAlteracoesRespiratorias, PROP_SINAIS_SINTOMAS_ALTERACOES_RESPIRATORIAS); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_alteracoes_respiratorias
	 * @param sinaisSintomasAlteracoesRespiratorias the sinais_sintomas_alteracoes_respiratorias value
	 */
	public void setSinaisSintomasAlteracoesRespiratorias (java.lang.Long sinaisSintomasAlteracoesRespiratorias) {
//        java.lang.Long sinaisSintomasAlteracoesRespiratoriasOld = this.sinaisSintomasAlteracoesRespiratorias;
		this.sinaisSintomasAlteracoesRespiratorias = sinaisSintomasAlteracoesRespiratorias;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasAlteracoesRespiratorias", sinaisSintomasAlteracoesRespiratoriasOld, sinaisSintomasAlteracoesRespiratorias);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_alteracoes_cardiacas
	 */
	public java.lang.Long getSinaisSintomasAlteracoesCardiacas () {
		return getPropertyValue(this, sinaisSintomasAlteracoesCardiacas, PROP_SINAIS_SINTOMAS_ALTERACOES_CARDIACAS); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_alteracoes_cardiacas
	 * @param sinaisSintomasAlteracoesCardiacas the sinais_sintomas_alteracoes_cardiacas value
	 */
	public void setSinaisSintomasAlteracoesCardiacas (java.lang.Long sinaisSintomasAlteracoesCardiacas) {
//        java.lang.Long sinaisSintomasAlteracoesCardiacasOld = this.sinaisSintomasAlteracoesCardiacas;
		this.sinaisSintomasAlteracoesCardiacas = sinaisSintomasAlteracoesCardiacas;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasAlteracoesCardiacas", sinaisSintomasAlteracoesCardiacasOld, sinaisSintomasAlteracoesCardiacas);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_hemorragia_pulmonar
	 */
	public java.lang.Long getSinaisSintomasHemorragiaPulmonar () {
		return getPropertyValue(this, sinaisSintomasHemorragiaPulmonar, PROP_SINAIS_SINTOMAS_HEMORRAGIA_PULMONAR); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_hemorragia_pulmonar
	 * @param sinaisSintomasHemorragiaPulmonar the sinais_sintomas_hemorragia_pulmonar value
	 */
	public void setSinaisSintomasHemorragiaPulmonar (java.lang.Long sinaisSintomasHemorragiaPulmonar) {
//        java.lang.Long sinaisSintomasHemorragiaPulmonarOld = this.sinaisSintomasHemorragiaPulmonar;
		this.sinaisSintomasHemorragiaPulmonar = sinaisSintomasHemorragiaPulmonar;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasHemorragiaPulmonar", sinaisSintomasHemorragiaPulmonarOld, sinaisSintomasHemorragiaPulmonar);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_outras_hemorragias
	 */
	public java.lang.Long getSinaisSintomasOutrasHemorragias () {
		return getPropertyValue(this, sinaisSintomasOutrasHemorragias, PROP_SINAIS_SINTOMAS_OUTRAS_HEMORRAGIAS); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_outras_hemorragias
	 * @param sinaisSintomasOutrasHemorragias the sinais_sintomas_outras_hemorragias value
	 */
	public void setSinaisSintomasOutrasHemorragias (java.lang.Long sinaisSintomasOutrasHemorragias) {
//        java.lang.Long sinaisSintomasOutrasHemorragiasOld = this.sinaisSintomasOutrasHemorragias;
		this.sinaisSintomasOutrasHemorragias = sinaisSintomasOutrasHemorragias;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasOutrasHemorragias", sinaisSintomasOutrasHemorragiasOld, sinaisSintomasOutrasHemorragias);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_meningismo
	 */
	public java.lang.Long getSinaisSintomasMeningismo () {
		return getPropertyValue(this, sinaisSintomasMeningismo, PROP_SINAIS_SINTOMAS_MENINGISMO); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_meningismo
	 * @param sinaisSintomasMeningismo the sinais_sintomas_meningismo value
	 */
	public void setSinaisSintomasMeningismo (java.lang.Long sinaisSintomasMeningismo) {
//        java.lang.Long sinaisSintomasMeningismoOld = this.sinaisSintomasMeningismo;
		this.sinaisSintomasMeningismo = sinaisSintomasMeningismo;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasMeningismo", sinaisSintomasMeningismoOld, sinaisSintomasMeningismo);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_outros
	 */
	public java.lang.String getSinaisSintomasOutros () {
		return getPropertyValue(this, sinaisSintomasOutros, PROP_SINAIS_SINTOMAS_OUTROS); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_outros
	 * @param sinaisSintomasOutros the sinais_sintomas_outros value
	 */
	public void setSinaisSintomasOutros (java.lang.String sinaisSintomasOutros) {
//        java.lang.String sinaisSintomasOutrosOld = this.sinaisSintomasOutros;
		this.sinaisSintomasOutros = sinaisSintomasOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasOutros", sinaisSintomasOutrosOld, sinaisSintomasOutros);
	}



	/**
	 * Return the value associated with the column: hospitalizacao
	 */
	public java.lang.Long getHospitalizacao () {
		return getPropertyValue(this, hospitalizacao, PROP_HOSPITALIZACAO); 
	}

	/**
	 * Set the value related to the column: hospitalizacao
	 * @param hospitalizacao the hospitalizacao value
	 */
	public void setHospitalizacao (java.lang.Long hospitalizacao) {
//        java.lang.Long hospitalizacaoOld = this.hospitalizacao;
		this.hospitalizacao = hospitalizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("hospitalizacao", hospitalizacaoOld, hospitalizacao);
	}



	/**
	 * Return the value associated with the column: dt_internacao
	 */
	public java.util.Date getDataInternacao () {
		return getPropertyValue(this, dataInternacao, PROP_DATA_INTERNACAO); 
	}

	/**
	 * Set the value related to the column: dt_internacao
	 * @param dataInternacao the dt_internacao value
	 */
	public void setDataInternacao (java.util.Date dataInternacao) {
//        java.util.Date dataInternacaoOld = this.dataInternacao;
		this.dataInternacao = dataInternacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInternacao", dataInternacaoOld, dataInternacao);
	}



	/**
	 * Return the value associated with the column: dt_alta
	 */
	public java.util.Date getDataAlta () {
		return getPropertyValue(this, dataAlta, PROP_DATA_ALTA); 
	}

	/**
	 * Set the value related to the column: dt_alta
	 * @param dataAlta the dt_alta value
	 */
	public void setDataAlta (java.util.Date dataAlta) {
//        java.util.Date dataAltaOld = this.dataAlta;
		this.dataAlta = dataAlta;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAlta", dataAltaOld, dataAlta);
	}



	/**
	 * Return the value associated with the column: sorologia_igm_elisa_sn
	 */
	public java.lang.Long getSorologiaIgmElisaSN () {
		return getPropertyValue(this, sorologiaIgmElisaSN, PROP_SOROLOGIA_IGM_ELISA_S_N); 
	}

	/**
	 * Set the value related to the column: sorologia_igm_elisa_sn
	 * @param sorologiaIgmElisaSN the sorologia_igm_elisa_sn value
	 */
	public void setSorologiaIgmElisaSN (java.lang.Long sorologiaIgmElisaSN) {
//        java.lang.Long sorologiaIgmElisaSNOld = this.sorologiaIgmElisaSN;
		this.sorologiaIgmElisaSN = sorologiaIgmElisaSN;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaIgmElisaSN", sorologiaIgmElisaSNOld, sorologiaIgmElisaSN);
	}



	/**
	 * Return the value associated with the column: sorologia_igm_dt_coleta_1
	 */
	public java.util.Date getSorologiaIgmDataColeta1 () {
		return getPropertyValue(this, sorologiaIgmDataColeta1, PROP_SOROLOGIA_IGM_DATA_COLETA1); 
	}

	/**
	 * Set the value related to the column: sorologia_igm_dt_coleta_1
	 * @param sorologiaIgmDataColeta1 the sorologia_igm_dt_coleta_1 value
	 */
	public void setSorologiaIgmDataColeta1 (java.util.Date sorologiaIgmDataColeta1) {
//        java.util.Date sorologiaIgmDataColeta1Old = this.sorologiaIgmDataColeta1;
		this.sorologiaIgmDataColeta1 = sorologiaIgmDataColeta1;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaIgmDataColeta1", sorologiaIgmDataColeta1Old, sorologiaIgmDataColeta1);
	}



	/**
	 * Return the value associated with the column: sorologia_igm_resultado_1
	 */
	public java.lang.Long getSorologiaIgmResultado1 () {
		return getPropertyValue(this, sorologiaIgmResultado1, PROP_SOROLOGIA_IGM_RESULTADO1); 
	}

	/**
	 * Set the value related to the column: sorologia_igm_resultado_1
	 * @param sorologiaIgmResultado1 the sorologia_igm_resultado_1 value
	 */
	public void setSorologiaIgmResultado1 (java.lang.Long sorologiaIgmResultado1) {
//        java.lang.Long sorologiaIgmResultado1Old = this.sorologiaIgmResultado1;
		this.sorologiaIgmResultado1 = sorologiaIgmResultado1;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaIgmResultado1", sorologiaIgmResultado1Old, sorologiaIgmResultado1);
	}



	/**
	 * Return the value associated with the column: sorologia_igm_dt_coleta_2
	 */
	public java.util.Date getSorologiaIgmDataColeta2 () {
		return getPropertyValue(this, sorologiaIgmDataColeta2, PROP_SOROLOGIA_IGM_DATA_COLETA2); 
	}

	/**
	 * Set the value related to the column: sorologia_igm_dt_coleta_2
	 * @param sorologiaIgmDataColeta2 the sorologia_igm_dt_coleta_2 value
	 */
	public void setSorologiaIgmDataColeta2 (java.util.Date sorologiaIgmDataColeta2) {
//        java.util.Date sorologiaIgmDataColeta2Old = this.sorologiaIgmDataColeta2;
		this.sorologiaIgmDataColeta2 = sorologiaIgmDataColeta2;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaIgmDataColeta2", sorologiaIgmDataColeta2Old, sorologiaIgmDataColeta2);
	}



	/**
	 * Return the value associated with the column: sorologia_igm_resultado_2
	 */
	public java.lang.Long getSorologiaIgmResultado2 () {
		return getPropertyValue(this, sorologiaIgmResultado2, PROP_SOROLOGIA_IGM_RESULTADO2); 
	}

	/**
	 * Set the value related to the column: sorologia_igm_resultado_2
	 * @param sorologiaIgmResultado2 the sorologia_igm_resultado_2 value
	 */
	public void setSorologiaIgmResultado2 (java.lang.Long sorologiaIgmResultado2) {
//        java.lang.Long sorologiaIgmResultado2Old = this.sorologiaIgmResultado2;
		this.sorologiaIgmResultado2 = sorologiaIgmResultado2;
//        this.getPropertyChangeSupport().firePropertyChange ("sorologiaIgmResultado2", sorologiaIgmResultado2Old, sorologiaIgmResultado2);
	}



	/**
	 * Return the value associated with the column: microaglutinacao_sn
	 */
	public java.lang.Long getMicroaglutinacaoSN () {
		return getPropertyValue(this, microaglutinacaoSN, PROP_MICROAGLUTINACAO_S_N); 
	}

	/**
	 * Set the value related to the column: microaglutinacao_sn
	 * @param microaglutinacaoSN the microaglutinacao_sn value
	 */
	public void setMicroaglutinacaoSN (java.lang.Long microaglutinacaoSN) {
//        java.lang.Long microaglutinacaoSNOld = this.microaglutinacaoSN;
		this.microaglutinacaoSN = microaglutinacaoSN;
//        this.getPropertyChangeSupport().firePropertyChange ("microaglutinacaoSN", microaglutinacaoSNOld, microaglutinacaoSN);
	}



	/**
	 * Return the value associated with the column: microaglutinacao_dt_coleta_1
	 */
	public java.util.Date getMicroaglutinacaoDataColeta1 () {
		return getPropertyValue(this, microaglutinacaoDataColeta1, PROP_MICROAGLUTINACAO_DATA_COLETA1); 
	}

	/**
	 * Set the value related to the column: microaglutinacao_dt_coleta_1
	 * @param microaglutinacaoDataColeta1 the microaglutinacao_dt_coleta_1 value
	 */
	public void setMicroaglutinacaoDataColeta1 (java.util.Date microaglutinacaoDataColeta1) {
//        java.util.Date microaglutinacaoDataColeta1Old = this.microaglutinacaoDataColeta1;
		this.microaglutinacaoDataColeta1 = microaglutinacaoDataColeta1;
//        this.getPropertyChangeSupport().firePropertyChange ("microaglutinacaoDataColeta1", microaglutinacaoDataColeta1Old, microaglutinacaoDataColeta1);
	}



	/**
	 * Return the value associated with the column: microaglutinacao_amostra_1_sorovar_1
	 */
	public java.lang.String getMicroaglutinacaoAmostra1Sorovar1 () {
		return getPropertyValue(this, microaglutinacaoAmostra1Sorovar1, PROP_MICROAGLUTINACAO_AMOSTRA1_SOROVAR1); 
	}

	/**
	 * Set the value related to the column: microaglutinacao_amostra_1_sorovar_1
	 * @param microaglutinacaoAmostra1Sorovar1 the microaglutinacao_amostra_1_sorovar_1 value
	 */
	public void setMicroaglutinacaoAmostra1Sorovar1 (java.lang.String microaglutinacaoAmostra1Sorovar1) {
//        java.lang.String microaglutinacaoAmostra1Sorovar1Old = this.microaglutinacaoAmostra1Sorovar1;
		this.microaglutinacaoAmostra1Sorovar1 = microaglutinacaoAmostra1Sorovar1;
//        this.getPropertyChangeSupport().firePropertyChange ("microaglutinacaoAmostra1Sorovar1", microaglutinacaoAmostra1Sorovar1Old, microaglutinacaoAmostra1Sorovar1);
	}



	/**
	 * Return the value associated with the column: microaglutinacao_amostra_1_sorovar_2
	 */
	public java.lang.String getMicroaglutinacaoAmostra1Sorovar2 () {
		return getPropertyValue(this, microaglutinacaoAmostra1Sorovar2, PROP_MICROAGLUTINACAO_AMOSTRA1_SOROVAR2); 
	}

	/**
	 * Set the value related to the column: microaglutinacao_amostra_1_sorovar_2
	 * @param microaglutinacaoAmostra1Sorovar2 the microaglutinacao_amostra_1_sorovar_2 value
	 */
	public void setMicroaglutinacaoAmostra1Sorovar2 (java.lang.String microaglutinacaoAmostra1Sorovar2) {
//        java.lang.String microaglutinacaoAmostra1Sorovar2Old = this.microaglutinacaoAmostra1Sorovar2;
		this.microaglutinacaoAmostra1Sorovar2 = microaglutinacaoAmostra1Sorovar2;
//        this.getPropertyChangeSupport().firePropertyChange ("microaglutinacaoAmostra1Sorovar2", microaglutinacaoAmostra1Sorovar2Old, microaglutinacaoAmostra1Sorovar2);
	}



	/**
	 * Return the value associated with the column: microaglutinacao_resultado_amostra_1
	 */
	public java.lang.Long getMicroaglutinacaoResultadoAmostra1 () {
		return getPropertyValue(this, microaglutinacaoResultadoAmostra1, PROP_MICROAGLUTINACAO_RESULTADO_AMOSTRA1); 
	}

	/**
	 * Set the value related to the column: microaglutinacao_resultado_amostra_1
	 * @param microaglutinacaoResultadoAmostra1 the microaglutinacao_resultado_amostra_1 value
	 */
	public void setMicroaglutinacaoResultadoAmostra1 (java.lang.Long microaglutinacaoResultadoAmostra1) {
//        java.lang.Long microaglutinacaoResultadoAmostra1Old = this.microaglutinacaoResultadoAmostra1;
		this.microaglutinacaoResultadoAmostra1 = microaglutinacaoResultadoAmostra1;
//        this.getPropertyChangeSupport().firePropertyChange ("microaglutinacaoResultadoAmostra1", microaglutinacaoResultadoAmostra1Old, microaglutinacaoResultadoAmostra1);
	}



	/**
	 * Return the value associated with the column: microaglutinacao_dt_coleta_2
	 */
	public java.util.Date getMicroaglutinacaoDataColeta2 () {
		return getPropertyValue(this, microaglutinacaoDataColeta2, PROP_MICROAGLUTINACAO_DATA_COLETA2); 
	}

	/**
	 * Set the value related to the column: microaglutinacao_dt_coleta_2
	 * @param microaglutinacaoDataColeta2 the microaglutinacao_dt_coleta_2 value
	 */
	public void setMicroaglutinacaoDataColeta2 (java.util.Date microaglutinacaoDataColeta2) {
//        java.util.Date microaglutinacaoDataColeta2Old = this.microaglutinacaoDataColeta2;
		this.microaglutinacaoDataColeta2 = microaglutinacaoDataColeta2;
//        this.getPropertyChangeSupport().firePropertyChange ("microaglutinacaoDataColeta2", microaglutinacaoDataColeta2Old, microaglutinacaoDataColeta2);
	}



	/**
	 * Return the value associated with the column: microaglutinacao_amostra_2_sorovar_1
	 */
	public java.lang.String getMicroaglutinacaoAmostra2Sorovar1 () {
		return getPropertyValue(this, microaglutinacaoAmostra2Sorovar1, PROP_MICROAGLUTINACAO_AMOSTRA2_SOROVAR1); 
	}

	/**
	 * Set the value related to the column: microaglutinacao_amostra_2_sorovar_1
	 * @param microaglutinacaoAmostra2Sorovar1 the microaglutinacao_amostra_2_sorovar_1 value
	 */
	public void setMicroaglutinacaoAmostra2Sorovar1 (java.lang.String microaglutinacaoAmostra2Sorovar1) {
//        java.lang.String microaglutinacaoAmostra2Sorovar1Old = this.microaglutinacaoAmostra2Sorovar1;
		this.microaglutinacaoAmostra2Sorovar1 = microaglutinacaoAmostra2Sorovar1;
//        this.getPropertyChangeSupport().firePropertyChange ("microaglutinacaoAmostra2Sorovar1", microaglutinacaoAmostra2Sorovar1Old, microaglutinacaoAmostra2Sorovar1);
	}



	/**
	 * Return the value associated with the column: microaglutinacao_amostra_2_sorovar_2
	 */
	public java.lang.String getMicroaglutinacaoAmostra2Sorovar2 () {
		return getPropertyValue(this, microaglutinacaoAmostra2Sorovar2, PROP_MICROAGLUTINACAO_AMOSTRA2_SOROVAR2); 
	}

	/**
	 * Set the value related to the column: microaglutinacao_amostra_2_sorovar_2
	 * @param microaglutinacaoAmostra2Sorovar2 the microaglutinacao_amostra_2_sorovar_2 value
	 */
	public void setMicroaglutinacaoAmostra2Sorovar2 (java.lang.String microaglutinacaoAmostra2Sorovar2) {
//        java.lang.String microaglutinacaoAmostra2Sorovar2Old = this.microaglutinacaoAmostra2Sorovar2;
		this.microaglutinacaoAmostra2Sorovar2 = microaglutinacaoAmostra2Sorovar2;
//        this.getPropertyChangeSupport().firePropertyChange ("microaglutinacaoAmostra2Sorovar2", microaglutinacaoAmostra2Sorovar2Old, microaglutinacaoAmostra2Sorovar2);
	}



	/**
	 * Return the value associated with the column: microaglutinacao_resultado_amostra_2
	 */
	public java.lang.Long getMicroaglutinacaoResultadoAmostra2 () {
		return getPropertyValue(this, microaglutinacaoResultadoAmostra2, PROP_MICROAGLUTINACAO_RESULTADO_AMOSTRA2); 
	}

	/**
	 * Set the value related to the column: microaglutinacao_resultado_amostra_2
	 * @param microaglutinacaoResultadoAmostra2 the microaglutinacao_resultado_amostra_2 value
	 */
	public void setMicroaglutinacaoResultadoAmostra2 (java.lang.Long microaglutinacaoResultadoAmostra2) {
//        java.lang.Long microaglutinacaoResultadoAmostra2Old = this.microaglutinacaoResultadoAmostra2;
		this.microaglutinacaoResultadoAmostra2 = microaglutinacaoResultadoAmostra2;
//        this.getPropertyChangeSupport().firePropertyChange ("microaglutinacaoResultadoAmostra2", microaglutinacaoResultadoAmostra2Old, microaglutinacaoResultadoAmostra2);
	}



	/**
	 * Return the value associated with the column: isolamento_sn
	 */
	public java.lang.Long getIsolamentoSN () {
		return getPropertyValue(this, isolamentoSN, PROP_ISOLAMENTO_S_N); 
	}

	/**
	 * Set the value related to the column: isolamento_sn
	 * @param isolamentoSN the isolamento_sn value
	 */
	public void setIsolamentoSN (java.lang.Long isolamentoSN) {
//        java.lang.Long isolamentoSNOld = this.isolamentoSN;
		this.isolamentoSN = isolamentoSN;
//        this.getPropertyChangeSupport().firePropertyChange ("isolamentoSN", isolamentoSNOld, isolamentoSN);
	}



	/**
	 * Return the value associated with the column: isolamento_dt_coleta
	 */
	public java.util.Date getIsolamentoDataColeta () {
		return getPropertyValue(this, isolamentoDataColeta, PROP_ISOLAMENTO_DATA_COLETA); 
	}

	/**
	 * Set the value related to the column: isolamento_dt_coleta
	 * @param isolamentoDataColeta the isolamento_dt_coleta value
	 */
	public void setIsolamentoDataColeta (java.util.Date isolamentoDataColeta) {
//        java.util.Date isolamentoDataColetaOld = this.isolamentoDataColeta;
		this.isolamentoDataColeta = isolamentoDataColeta;
//        this.getPropertyChangeSupport().firePropertyChange ("isolamentoDataColeta", isolamentoDataColetaOld, isolamentoDataColeta);
	}



	/**
	 * Return the value associated with the column: isolamento_resultado
	 */
	public java.lang.Long getIsolamentoResultado () {
		return getPropertyValue(this, isolamentoResultado, PROP_ISOLAMENTO_RESULTADO); 
	}

	/**
	 * Set the value related to the column: isolamento_resultado
	 * @param isolamentoResultado the isolamento_resultado value
	 */
	public void setIsolamentoResultado (java.lang.Long isolamentoResultado) {
//        java.lang.Long isolamentoResultadoOld = this.isolamentoResultado;
		this.isolamentoResultado = isolamentoResultado;
//        this.getPropertyChangeSupport().firePropertyChange ("isolamentoResultado", isolamentoResultadoOld, isolamentoResultado);
	}



	/**
	 * Return the value associated with the column: imunohistoquimica_sn
	 */
	public java.lang.Long getImunohistoquimicaSN () {
		return getPropertyValue(this, imunohistoquimicaSN, PROP_IMUNOHISTOQUIMICA_S_N); 
	}

	/**
	 * Set the value related to the column: imunohistoquimica_sn
	 * @param imunohistoquimicaSN the imunohistoquimica_sn value
	 */
	public void setImunohistoquimicaSN (java.lang.Long imunohistoquimicaSN) {
//        java.lang.Long imunohistoquimicaSNOld = this.imunohistoquimicaSN;
		this.imunohistoquimicaSN = imunohistoquimicaSN;
//        this.getPropertyChangeSupport().firePropertyChange ("imunohistoquimicaSN", imunohistoquimicaSNOld, imunohistoquimicaSN);
	}



	/**
	 * Return the value associated with the column: imunohistoquimica_dt_coleta
	 */
	public java.util.Date getImunohistoquimicaDataColeta () {
		return getPropertyValue(this, imunohistoquimicaDataColeta, PROP_IMUNOHISTOQUIMICA_DATA_COLETA); 
	}

	/**
	 * Set the value related to the column: imunohistoquimica_dt_coleta
	 * @param imunohistoquimicaDataColeta the imunohistoquimica_dt_coleta value
	 */
	public void setImunohistoquimicaDataColeta (java.util.Date imunohistoquimicaDataColeta) {
//        java.util.Date imunohistoquimicaDataColetaOld = this.imunohistoquimicaDataColeta;
		this.imunohistoquimicaDataColeta = imunohistoquimicaDataColeta;
//        this.getPropertyChangeSupport().firePropertyChange ("imunohistoquimicaDataColeta", imunohistoquimicaDataColetaOld, imunohistoquimicaDataColeta);
	}



	/**
	 * Return the value associated with the column: imunohistoquimica_resultado
	 */
	public java.lang.Long getImunohistoquimicaResultado () {
		return getPropertyValue(this, imunohistoquimicaResultado, PROP_IMUNOHISTOQUIMICA_RESULTADO); 
	}

	/**
	 * Set the value related to the column: imunohistoquimica_resultado
	 * @param imunohistoquimicaResultado the imunohistoquimica_resultado value
	 */
	public void setImunohistoquimicaResultado (java.lang.Long imunohistoquimicaResultado) {
//        java.lang.Long imunohistoquimicaResultadoOld = this.imunohistoquimicaResultado;
		this.imunohistoquimicaResultado = imunohistoquimicaResultado;
//        this.getPropertyChangeSupport().firePropertyChange ("imunohistoquimicaResultado", imunohistoquimicaResultadoOld, imunohistoquimicaResultado);
	}



	/**
	 * Return the value associated with the column: rtpcr_sn
	 */
	public java.lang.Long getRtpcrSN () {
		return getPropertyValue(this, rtpcrSN, PROP_RTPCR_S_N); 
	}

	/**
	 * Set the value related to the column: rtpcr_sn
	 * @param rtpcrSN the rtpcr_sn value
	 */
	public void setRtpcrSN (java.lang.Long rtpcrSN) {
//        java.lang.Long rtpcrSNOld = this.rtpcrSN;
		this.rtpcrSN = rtpcrSN;
//        this.getPropertyChangeSupport().firePropertyChange ("rtpcrSN", rtpcrSNOld, rtpcrSN);
	}



	/**
	 * Return the value associated with the column: rtpcr_dt_coleta
	 */
	public java.util.Date getRtpcrDataColeta () {
		return getPropertyValue(this, rtpcrDataColeta, PROP_RTPCR_DATA_COLETA); 
	}

	/**
	 * Set the value related to the column: rtpcr_dt_coleta
	 * @param rtpcrDataColeta the rtpcr_dt_coleta value
	 */
	public void setRtpcrDataColeta (java.util.Date rtpcrDataColeta) {
//        java.util.Date rtpcrDataColetaOld = this.rtpcrDataColeta;
		this.rtpcrDataColeta = rtpcrDataColeta;
//        this.getPropertyChangeSupport().firePropertyChange ("rtpcrDataColeta", rtpcrDataColetaOld, rtpcrDataColeta);
	}



	/**
	 * Return the value associated with the column: rtpcr_resultado
	 */
	public java.lang.Long getRtpcrResultado () {
		return getPropertyValue(this, rtpcrResultado, PROP_RTPCR_RESULTADO); 
	}

	/**
	 * Set the value related to the column: rtpcr_resultado
	 * @param rtpcrResultado the rtpcr_resultado value
	 */
	public void setRtpcrResultado (java.lang.Long rtpcrResultado) {
//        java.lang.Long rtpcrResultadoOld = this.rtpcrResultado;
		this.rtpcrResultado = rtpcrResultado;
//        this.getPropertyChangeSupport().firePropertyChange ("rtpcrResultado", rtpcrResultadoOld, rtpcrResultado);
	}



	/**
	 * Return the value associated with the column: caso_autoctone
	 */
	public java.lang.Long getCasoAutoctone () {
		return getPropertyValue(this, casoAutoctone, PROP_CASO_AUTOCTONE); 
	}

	/**
	 * Set the value related to the column: caso_autoctone
	 * @param casoAutoctone the caso_autoctone value
	 */
	public void setCasoAutoctone (java.lang.Long casoAutoctone) {
//        java.lang.Long casoAutoctoneOld = this.casoAutoctone;
		this.casoAutoctone = casoAutoctone;
//        this.getPropertyChangeSupport().firePropertyChange ("casoAutoctone", casoAutoctoneOld, casoAutoctone);
	}



	/**
	 * Return the value associated with the column: str_distrito_infeccao
	 */
	public java.lang.String getDistritoLocalInfeccao () {
		return getPropertyValue(this, distritoLocalInfeccao, PROP_DISTRITO_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: str_distrito_infeccao
	 * @param distritoLocalInfeccao the str_distrito_infeccao value
	 */
	public void setDistritoLocalInfeccao (java.lang.String distritoLocalInfeccao) {
//        java.lang.String distritoLocalInfeccaoOld = this.distritoLocalInfeccao;
		this.distritoLocalInfeccao = distritoLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("distritoLocalInfeccao", distritoLocalInfeccaoOld, distritoLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: str_bairro_infeccao
	 */
	public java.lang.String getBairroLocalInfeccao () {
		return getPropertyValue(this, bairroLocalInfeccao, PROP_BAIRRO_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: str_bairro_infeccao
	 * @param bairroLocalInfeccao the str_bairro_infeccao value
	 */
	public void setBairroLocalInfeccao (java.lang.String bairroLocalInfeccao) {
//        java.lang.String bairroLocalInfeccaoOld = this.bairroLocalInfeccao;
		this.bairroLocalInfeccao = bairroLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("bairroLocalInfeccao", bairroLocalInfeccaoOld, bairroLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: classificacao_final
	 */
	public java.lang.Long getClassificacaoFinal () {
		return getPropertyValue(this, classificacaoFinal, PROP_CLASSIFICACAO_FINAL); 
	}

	/**
	 * Set the value related to the column: classificacao_final
	 * @param classificacaoFinal the classificacao_final value
	 */
	public void setClassificacaoFinal (java.lang.Long classificacaoFinal) {
//        java.lang.Long classificacaoFinalOld = this.classificacaoFinal;
		this.classificacaoFinal = classificacaoFinal;
//        this.getPropertyChangeSupport().firePropertyChange ("classificacaoFinal", classificacaoFinalOld, classificacaoFinal);
	}



	/**
	 * Return the value associated with the column: criterio_confirmacao
	 */
	public java.lang.Long getCriterioConfirmacao () {
		return getPropertyValue(this, criterioConfirmacao, PROP_CRITERIO_CONFIRMACAO); 
	}

	/**
	 * Set the value related to the column: criterio_confirmacao
	 * @param criterioConfirmacao the criterio_confirmacao value
	 */
	public void setCriterioConfirmacao (java.lang.Long criterioConfirmacao) {
//        java.lang.Long criterioConfirmacaoOld = this.criterioConfirmacao;
		this.criterioConfirmacao = criterioConfirmacao;
//        this.getPropertyChangeSupport().firePropertyChange ("criterioConfirmacao", criterioConfirmacaoOld, criterioConfirmacao);
	}



	/**
	 * Return the value associated with the column: area_provavel_infeccao
	 */
	public java.lang.Long getAreaProvavelInfeccao () {
		return getPropertyValue(this, areaProvavelInfeccao, PROP_AREA_PROVAVEL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: area_provavel_infeccao
	 * @param areaProvavelInfeccao the area_provavel_infeccao value
	 */
	public void setAreaProvavelInfeccao (java.lang.Long areaProvavelInfeccao) {
//        java.lang.Long areaProvavelInfeccaoOld = this.areaProvavelInfeccao;
		this.areaProvavelInfeccao = areaProvavelInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("areaProvavelInfeccao", areaProvavelInfeccaoOld, areaProvavelInfeccao);
	}



	/**
	 * Return the value associated with the column: ambiente_infeccao
	 */
	public java.lang.Long getAmbienteInfeccao () {
		return getPropertyValue(this, ambienteInfeccao, PROP_AMBIENTE_INFECCAO); 
	}

	/**
	 * Set the value related to the column: ambiente_infeccao
	 * @param ambienteInfeccao the ambiente_infeccao value
	 */
	public void setAmbienteInfeccao (java.lang.Long ambienteInfeccao) {
//        java.lang.Long ambienteInfeccaoOld = this.ambienteInfeccao;
		this.ambienteInfeccao = ambienteInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("ambienteInfeccao", ambienteInfeccaoOld, ambienteInfeccao);
	}



	/**
	 * Return the value associated with the column: doenca_relacionada_trabalho
	 */
	public java.lang.Long getDoencaRelacionadaTrabalho () {
		return getPropertyValue(this, doencaRelacionadaTrabalho, PROP_DOENCA_RELACIONADA_TRABALHO); 
	}

	/**
	 * Set the value related to the column: doenca_relacionada_trabalho
	 * @param doencaRelacionadaTrabalho the doenca_relacionada_trabalho value
	 */
	public void setDoencaRelacionadaTrabalho (java.lang.Long doencaRelacionadaTrabalho) {
//        java.lang.Long doencaRelacionadaTrabalhoOld = this.doencaRelacionadaTrabalho;
		this.doencaRelacionadaTrabalho = doencaRelacionadaTrabalho;
//        this.getPropertyChangeSupport().firePropertyChange ("doencaRelacionadaTrabalho", doencaRelacionadaTrabalhoOld, doencaRelacionadaTrabalho);
	}



	/**
	 * Return the value associated with the column: evolucao_caso
	 */
	public java.lang.Long getEvolucaoCaso () {
		return getPropertyValue(this, evolucaoCaso, PROP_EVOLUCAO_CASO); 
	}

	/**
	 * Set the value related to the column: evolucao_caso
	 * @param evolucaoCaso the evolucao_caso value
	 */
	public void setEvolucaoCaso (java.lang.Long evolucaoCaso) {
//        java.lang.Long evolucaoCasoOld = this.evolucaoCaso;
		this.evolucaoCaso = evolucaoCaso;
//        this.getPropertyChangeSupport().firePropertyChange ("evolucaoCaso", evolucaoCasoOld, evolucaoCaso);
	}



	/**
	 * Return the value associated with the column: data_obito
	 */
	public java.util.Date getDataObito () {
		return getPropertyValue(this, dataObito, PROP_DATA_OBITO); 
	}

	/**
	 * Set the value related to the column: data_obito
	 * @param dataObito the data_obito value
	 */
	public void setDataObito (java.util.Date dataObito) {
//        java.util.Date dataObitoOld = this.dataObito;
		this.dataObito = dataObito;
//        this.getPropertyChangeSupport().firePropertyChange ("dataObito", dataObitoOld, dataObito);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: dt_encerramento
	 */
	public java.util.Date getDataEncerramento () {
		return getPropertyValue(this, dataEncerramento, PROP_DATA_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_encerramento
	 * @param dataEncerramento the dt_encerramento value
	 */
	public void setDataEncerramento (java.util.Date dataEncerramento) {
//        java.util.Date dataEncerramentoOld = this.dataEncerramento;
		this.dataEncerramento = dataEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEncerramento", dataEncerramentoOld, dataEncerramento);
	}



	/**
	 * Return the value associated with the column: cd_registro_agravo
	 */
	public br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo getRegistroAgravo () {
		return getPropertyValue(this, registroAgravo, PROP_REGISTRO_AGRAVO); 
	}

	/**
	 * Set the value related to the column: cd_registro_agravo
	 * @param registroAgravo the cd_registro_agravo value
	 */
	public void setRegistroAgravo (br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo) {
//        br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravoOld = this.registroAgravo;
		this.registroAgravo = registroAgravo;
//        this.getPropertyChangeSupport().firePropertyChange ("registroAgravo", registroAgravoOld, registroAgravo);
	}



	/**
	 * Return the value associated with the column: ocupacao_cbo
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getOcupacaoCbo () {
		return getPropertyValue(this, ocupacaoCbo, PROP_OCUPACAO_CBO); 
	}

	/**
	 * Set the value related to the column: ocupacao_cbo
	 * @param ocupacaoCbo the ocupacao_cbo value
	 */
	public void setOcupacaoCbo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCboOld = this.ocupacaoCbo;
		this.ocupacaoCbo = ocupacaoCbo;
//        this.getPropertyChangeSupport().firePropertyChange ("ocupacaoCbo", ocupacaoCboOld, ocupacaoCbo);
	}



	/**
	 * Return the value associated with the column: unidade_hospital
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getHospital () {
		return getPropertyValue(this, hospital, PROP_HOSPITAL); 
	}

	/**
	 * Set the value related to the column: unidade_hospital
	 * @param hospital the unidade_hospital value
	 */
	public void setHospital (br.com.ksisolucoes.vo.basico.Empresa hospital) {
//        br.com.ksisolucoes.vo.basico.Empresa hospitalOld = this.hospital;
		this.hospital = hospital;
//        this.getPropertyChangeSupport().firePropertyChange ("hospital", hospitalOld, hospital);
	}



	/**
	 * Return the value associated with the column: cd_pais_infeccao
	 */
	public br.com.ksisolucoes.vo.basico.Pais getPaisLocalInfeccao () {
		return getPropertyValue(this, paisLocalInfeccao, PROP_PAIS_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: cd_pais_infeccao
	 * @param paisLocalInfeccao the cd_pais_infeccao value
	 */
	public void setPaisLocalInfeccao (br.com.ksisolucoes.vo.basico.Pais paisLocalInfeccao) {
//        br.com.ksisolucoes.vo.basico.Pais paisLocalInfeccaoOld = this.paisLocalInfeccao;
		this.paisLocalInfeccao = paisLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("paisLocalInfeccao", paisLocalInfeccaoOld, paisLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: cd_cidade_infeccao
	 */
	public br.com.ksisolucoes.vo.basico.Cidade getCidadeLocalInfeccao () {
		return getPropertyValue(this, cidadeLocalInfeccao, PROP_CIDADE_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: cd_cidade_infeccao
	 * @param cidadeLocalInfeccao the cd_cidade_infeccao value
	 */
	public void setCidadeLocalInfeccao (br.com.ksisolucoes.vo.basico.Cidade cidadeLocalInfeccao) {
//        br.com.ksisolucoes.vo.basico.Cidade cidadeLocalInfeccaoOld = this.cidadeLocalInfeccao;
		this.cidadeLocalInfeccao = cidadeLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("cidadeLocalInfeccao", cidadeLocalInfeccaoOld, cidadeLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: cd_usuario_encerramento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioEncerramento () {
		return getPropertyValue(this, usuarioEncerramento, PROP_USUARIO_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_encerramento
	 * @param usuarioEncerramento the cd_usuario_encerramento value
	 */
	public void setUsuarioEncerramento (br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramentoOld = this.usuarioEncerramento;
		this.usuarioEncerramento = usuarioEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioEncerramento", usuarioEncerramentoOld, usuarioEncerramento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoLeptospirose)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoLeptospirose investigacaoAgravoLeptospirose = (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoLeptospirose) obj;
			if (null == this.getCodigo() || null == investigacaoAgravoLeptospirose.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravoLeptospirose.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}