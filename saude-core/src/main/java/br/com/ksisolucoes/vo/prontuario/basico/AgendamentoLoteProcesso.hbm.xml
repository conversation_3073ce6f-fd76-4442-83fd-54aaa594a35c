<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class 
        name="AgendamentoLoteProcesso"
        table="agendamento_lote_processo"
    >
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_agendamento_lote_processo"
        >
            <generator class="sequence">
                <param name="sequence">seq_agendamento_lote_processo</param>
            </generator>
        </id> 
        <version column="version" name="version" type="long" />
		          
        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento"
                     name="tipoProcedimento"
                     not-null="true">
            <column name="cd_tp_procedimento" />
        </many-to-one>

        <many-to-one class="br.com.ksisolucoes.vo.controle.Usuario"
                     name="usuario"
                     not-null="true">
            <column name="cd_usuario" />
        </many-to-one>

        <property
                name="dataGeracao"
                column="dt_geracao"
                type="timestamp"
                not-null="true"
        />

        <property
                name="dataFinalizacao"
                column="dt_finalizacao"
                type="timestamp"
                not-null="false"
        />

        <property
                name="quantidade"
                column="quantidade"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                name="status"
                column="status"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                name="quantidadeAgendada"
                column="qtd_agendada"
                type="java.lang.Long"
                not-null="false"
        />

        <property
                name="quantidadeErro"
                column="qtd_erro"
                type="java.lang.Long"
                not-null="false"
        />

        <property
                name="mensagemErro"
                column="msg_erro"
                type="java.lang.String"
        />

        <property
                name="solicitacoesAgendadas"
                column="solicitacoes_agendadas"
                type="java.lang.String"
        />

        <property
                name="quebraSolicitacao"
                column="quebra_solicitacao"
                type="java.lang.String"
        />
        
        <property
                name="resetFila"
                column="reset_fila"
                type="java.lang.Long"
        />

        <many-to-one class="br.com.ksisolucoes.vo.basico.Empresa"
                     name="origemSolicitacao">
            <column name="cd_origem_solicitacao" />
        </many-to-one>
    </class>
</hibernate-mapping>
