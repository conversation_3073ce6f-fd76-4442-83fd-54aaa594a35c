package br.com.ksisolucoes.vo.prontuario.procedimento;

import java.io.Serializable;

import br.com.ksisolucoes.associacao.annotations.IdNameSIGTAP;
import br.com.ksisolucoes.associacao.annotations.IdOverrideAtributeSIGTAP;
import br.com.ksisolucoes.associacao.annotations.IdOverrideSIGTAP;
import br.com.ksisolucoes.associacao.annotations.TableNameSIGTAP;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.procedimento.base.BaseProcedimentoOrigem;


@TableNameSIGTAP(value="rl_procedimento_origem",dependencias={ProcedimentoCompetencia.class})
public class ProcedimentoOrigem extends BaseProcedimentoOrigem implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ProcedimentoOrigem () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ProcedimentoOrigem (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoOrigemPK id) {
		super(id);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setId( (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoOrigemPK)key );
    }

    public Serializable getCodigoManager() {
        return this.getId();
    }

    @IdNameSIGTAP
    @IdOverrideSIGTAP(atribute={
        @IdOverrideAtributeSIGTAP(prop="id.procedimentoCompetencia.id.dataCompetencia",value="DT_COMPETENCIA"),
        @IdOverrideAtributeSIGTAP(prop="id.procedimentoCompetencia.id.procedimento.codigo",value="CO_PROCEDIMENTO"),
        @IdOverrideAtributeSIGTAP(prop="id.procedimentoCompetenciaOrigem.id.dataCompetencia",value="DT_COMPETENCIA"),
        @IdOverrideAtributeSIGTAP(prop="id.procedimentoCompetenciaOrigem.id.procedimento.codigo",value="CO_PROCEDIMENTO_ORIGEM")
    })
    @Override
    public ProcedimentoOrigemPK getId() {
        return super.getId();
    }

    @Override
    public void setId(ProcedimentoOrigemPK id) {
        super.setId(id);
    }
}