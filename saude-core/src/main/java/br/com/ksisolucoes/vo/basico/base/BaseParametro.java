package br.com.ksisolucoes.vo.basico.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the parametro table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="parametro"
 */

public abstract class BaseParametro extends BaseRootVO implements Serializable {

	public static String REF = "Parametro";
	public static final String PROP_VALIDA_CPF_CNPJ = "validaCpfCnpj";
	public static final String PROP_URL_BASE_CNES = "urlBaseCnes";
	public static final String PROP_DRIVER_BASE_CNES = "driverBaseCnes";
	public static final String PROP_PRESSAO_ARTERIAL_ALTA = "pressaoArterialAlta";
	public static final String PROP_NOME_PREFEITURA = "nomePrefeitura";
	public static final String PROP_TIPO_DOCUMENTO_OPERACAO_ENTRADA = "tipoDocumentoOperacaoEntrada";
	public static final String PROP_LOGO_LISTA_PUBLICA = "logoListaPublica";
	public static final String PROP_FLAG_APROVA_PEDIDO_INTERNO_COMPRA_DIRETORIA = "flagAprovaPedidoInternoCompraDiretoria";
	public static final String PROP_TIPO_DOCUMENTO_BAIXA_ENFERMAGEM = "tipoDocumentoBaixaEnfermagem";
	public static final String PROP_RANGE_QUANTIDADE_PADRAO = "rangeQuantidadePadrao";
	public static final String PROP_PESSOA = "pessoa";
	public static final String PROP_FLAG_APROVA_PEDIDO_INTERNO_COMPRA_SETOR = "flagAprovaPedidoInternoCompraSetor";
	public static final String PROP_LOGO_SISTEMA = "logoSistema";
	public static final String PROP_CAMINHO_IMAGEM_LOGIN = "caminhoImagemLogin";
	public static final String PROP_TIPO_DOCUMENTO_ENTRADA = "tipoDocumentoEntrada";
	public static final String PROP_INTERVALO_VALIDADE_CONTINUO = "intervaloValidadeContinuo";
	public static final String PROP_FLAG_APROVA_ORDEM_COMPRA_DENTRO_DO_LIMITE = "flagAprovaOrdemCompraDentroDoLimite";
	public static final String PROP_PRODUTO = "produto";
	public static final String PROP_PRESSAO_ARTERIAL_NORMAL = "pressaoArterialNormal";
	public static final String PROP_KEYCLOACK_CLIENT_SECRET = "keycloackClientSecret";
	public static final String PROP_KEYCLOACK_PUBLIC_KEY = "keycloackPublicKey";
	public static final String PROP_TIPO_DOCUMENTO_N_F_ENTRADA = "tipoDocumentoNFEntrada";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_SMTP_SERVIDOR = "smtpServidor";
	public static final String PROP_SFTP_DIRETORIO_RAIZ = "sftpDiretorioRaiz";
	public static final String PROP_UNIDADE_PRINCIPAL = "unidadePrincipal";
	public static final String PROP_SUB_GRUPO_HIPERDIA = "subGrupoHiperdia";
	public static final String PROP_TIPO_DOCUMENTO = "tipoDocumento";
	public static final String PROP_PATH_CADSUS = "pathCadsus";
	public static final String PROP_LOGO_LOGIN = "logoLogin";
	public static final String PROP_SMTP_SENHA = "smtpSenha";
	public static final String PROP_DATA_ATUALIZACAO = "dataAtualizacao";
	public static final String PROP_FLAG_CONTROLA_MINIMO = "flagControlaMinimo";
	public static final String PROP_FLAG_APROVA_ORDEM_COMPRA = "flagAprovaOrdemCompra";
	public static final String PROP_CAMINHO_LOGO_PREFEITURA = "caminhoLogoPrefeitura";
	public static final String PROP_SFTP_USUARIO = "sftpUsuario";
	public static final String PROP_TIPO_DOCUMENTO_OPERACAO_SAIDA = "tipoDocumentoOperacaoSaida";
	public static final String PROP_KEYCLOACK_CLIENT_ID = "keycloackClientId";
	public static final String PROP_TIPO_DOCUMENTO_SAIDA = "tipoDocumentoSaida";
	public static final String PROP_SENHA_BASE_CNES = "senhaBaseCnes";
	public static final String PROP_LOGIN_BLOQUEADO = "loginBloqueado";
	public static final String PROP_TIPO_DOCUMENTO_DISPENSACAO_MEDICAMENTO = "tipoDocumentoDispensacaoMedicamento";
	public static final String PROP_LOGO_DOCUMENTO = "logoDocumento";
	public static final String PROP_FLAG_UTILIZA_TABELA_PRECO_PRODUTO_CLIENTE = "flagUtilizaTabelaPrecoProdutoCliente";
	public static final String PROP_DATA_EXPORTACAO_CADSUS = "dataExportacaoCadsus";
	public static final String PROP_LOGO_FAVICON = "logoFavicon";
	public static final String PROP_SFTP_SERVIDOR = "sftpServidor";
	public static final String PROP_TEMPO_SESSAO = "tempoSessao";
	public static final String PROP_PATH_FILE_MANAGER_WEB = "pathFileManagerWeb";
	public static final String PROP_LOGO_SISTEMA_TELA_LOGIN = "logoSistemaTelaLogin";
	public static final String PROP_SMTP_USUARIO = "smtpUsuario";
	public static final String PROP_TIPO_DOCUMENTO_PEDIDO_TRANSFERENCIA_ENTRADA = "tipoDocumentoPedidoTransferenciaEntrada";
	public static final String PROP_SFTP_COMPRESSAO = "sftpCompressao";
	public static final String PROP_TIPO_DOCUMENTO_N_F_SAIDA = "tipoDocumentoNFSaida";
	public static final String PROP_PATH_TABELAS_CORPORATIVAS = "pathTabelasCorporativas";
	public static final String PROP_TIPO_DOCUMENTO_PEDIDO_TRANSFERENCIA_SAIDA = "tipoDocumentoPedidoTransferenciaSaida";
	public static final String PROP_DATA_COMPETENCIA_PROCEDIMENTO = "dataCompetenciaProcedimento";
	public static final String PROP_PRESSAO_ARTERIAL_BAIXA = "pressaoArterialBaixa";
	public static final String PROP_UNIDADE_LACEN = "unidadeLacen";
	public static final String PROP_LOGO_LACEN = "logoLacen";
	public static final String PROP_LOGO_ETIQUETA_EMBARQUE = "logoEtiquetaEmbarque";
	public static final String PROP_LIMITE_RESULTADOS_RELATORIOS = "limiteResultadosRelatorios";
	public static final String PROP_TIPO_DOCUMENTO_EXTORNO_ENFERMAGEM = "tipoDocumentoExtornoEnfermagem";
	public static final String PROP_SFTP_SENHA = "sftpSenha";
	public static final String PROP_TIPO_DOCUMENTO_PRONTUARIO_EMERGENCIA = "tipoDocumentoProntuarioEmergencia";
	public static final String PROP_USUARIO_BASE_CNES = "usuarioBaseCnes";
	public static final String PROP_GOOGLE_RECAPTCHA_KEY = "googleReCaptchaKey";


	// constructors
	public BaseParametro () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseParametro (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataAtualizacao;
	private java.lang.String flagControlaMinimo;
	private java.lang.String flagAprovaPedidoInternoCompraDiretoria;
	private java.lang.String flagAprovaOrdemCompra;
	private java.lang.String flagUtilizaTabelaPrecoProdutoCliente;
	private java.lang.String caminhoImagemLogin;
	private java.lang.Long intervaloValidadeContinuo;
	private java.lang.String flagAprovaPedidoInternoCompraSetor;
	private java.lang.String flagAprovaOrdemCompraDentroDoLimite;
	private java.lang.String validaCpfCnpj;
	private java.lang.Double rangeQuantidadePadrao;
	private java.lang.String caminhoLogoPrefeitura;
	private java.lang.String pressaoArterialBaixa;
	private java.lang.String pressaoArterialNormal;
	private java.lang.String pressaoArterialAlta;
	private java.util.Date dataCompetenciaProcedimento;
	private java.lang.String pathFileManagerWeb;
	private java.lang.String pathCadsus;
	private java.lang.String pathTabelasCorporativas;
	private java.lang.String driverBaseCnes;
	private java.lang.String urlBaseCnes;
	private java.lang.String usuarioBaseCnes;
	private java.lang.String senhaBaseCnes;
	private java.lang.String smtpServidor;
	private java.lang.String smtpUsuario;
	private java.lang.String smtpSenha;
	private java.lang.String nomePrefeitura;
	private java.util.Date dataExportacaoCadsus;
	private java.lang.String sftpUsuario;
	private java.lang.String sftpSenha;
	private java.lang.String sftpServidor;
	private java.lang.String sftpCompressao;
	private java.lang.String sftpDiretorioRaiz;
	private java.lang.Long tempoSessao;
	private java.lang.Long loginBloqueado;
	private java.lang.String keycloackClientId;
	private java.lang.String keycloackClientSecret;
	private java.lang.String keycloackPublicKey;
	private java.lang.Long limiteResultadosRelatorios;
	private java.lang.String googleReCaptchaKey;

	// many to one
	private br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoEntrada;
	private br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoSaida;
	private br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoDispensacaoMedicamento;
	private br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoOperacaoEntrada;
	private br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoOperacaoSaida;
	private br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoNFEntrada;
	private br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoNFSaida;
	private br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoPedidoTransferenciaSaida;
	private br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoPedidoTransferenciaEntrada;
	private br.com.ksisolucoes.vo.basico.Pessoa pessoa;
	private br.com.ksisolucoes.vo.entradas.estoque.Produto produto;
	private br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumento;
	private br.com.ksisolucoes.vo.basico.Empresa unidadePrincipal;
	private br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoProntuarioEmergencia;
	private br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoBaixaEnfermagem;
	private br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoExtornoEnfermagem;
	private br.com.ksisolucoes.vo.entradas.estoque.SubGrupo subGrupoHiperdia;
	private br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoDocumento;
	private br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoLogin;
	private br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoEtiquetaEmbarque;
	private br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoSistema;
	private br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoSistemaTelaLogin;
	private br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoFavicon;
	private br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoListaPublica;
	private br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo unidadeLacen;
	private br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoLacen;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cod_parametro"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_atualizacao
	 */
	public java.util.Date getDataAtualizacao () {
		return getPropertyValue(this, dataAtualizacao, PROP_DATA_ATUALIZACAO); 
	}

	/**
	 * Set the value related to the column: dt_atualizacao
	 * @param dataAtualizacao the dt_atualizacao value
	 */
	public void setDataAtualizacao (java.util.Date dataAtualizacao) {
//        java.util.Date dataAtualizacaoOld = this.dataAtualizacao;
		this.dataAtualizacao = dataAtualizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAtualizacao", dataAtualizacaoOld, dataAtualizacao);
	}



	/**
	 * Return the value associated with the column: cont_min
	 */
	public java.lang.String getFlagControlaMinimo () {
		return getPropertyValue(this, flagControlaMinimo, PROP_FLAG_CONTROLA_MINIMO); 
	}

	/**
	 * Set the value related to the column: cont_min
	 * @param flagControlaMinimo the cont_min value
	 */
	public void setFlagControlaMinimo (java.lang.String flagControlaMinimo) {
//        java.lang.String flagControlaMinimoOld = this.flagControlaMinimo;
		this.flagControlaMinimo = flagControlaMinimo;
//        this.getPropertyChangeSupport().firePropertyChange ("flagControlaMinimo", flagControlaMinimoOld, flagControlaMinimo);
	}



	/**
	 * Return the value associated with the column: aprova_pic_diretoria
	 */
	public java.lang.String getFlagAprovaPedidoInternoCompraDiretoria () {
		return getPropertyValue(this, flagAprovaPedidoInternoCompraDiretoria, PROP_FLAG_APROVA_PEDIDO_INTERNO_COMPRA_DIRETORIA); 
	}

	/**
	 * Set the value related to the column: aprova_pic_diretoria
	 * @param flagAprovaPedidoInternoCompraDiretoria the aprova_pic_diretoria value
	 */
	public void setFlagAprovaPedidoInternoCompraDiretoria (java.lang.String flagAprovaPedidoInternoCompraDiretoria) {
//        java.lang.String flagAprovaPedidoInternoCompraDiretoriaOld = this.flagAprovaPedidoInternoCompraDiretoria;
		this.flagAprovaPedidoInternoCompraDiretoria = flagAprovaPedidoInternoCompraDiretoria;
//        this.getPropertyChangeSupport().firePropertyChange ("flagAprovaPedidoInternoCompraDiretoria", flagAprovaPedidoInternoCompraDiretoriaOld, flagAprovaPedidoInternoCompraDiretoria);
	}



	/**
	 * Return the value associated with the column: aprova_ordem_de_compra
	 */
	public java.lang.String getFlagAprovaOrdemCompra () {
		return getPropertyValue(this, flagAprovaOrdemCompra, PROP_FLAG_APROVA_ORDEM_COMPRA); 
	}

	/**
	 * Set the value related to the column: aprova_ordem_de_compra
	 * @param flagAprovaOrdemCompra the aprova_ordem_de_compra value
	 */
	public void setFlagAprovaOrdemCompra (java.lang.String flagAprovaOrdemCompra) {
//        java.lang.String flagAprovaOrdemCompraOld = this.flagAprovaOrdemCompra;
		this.flagAprovaOrdemCompra = flagAprovaOrdemCompra;
//        this.getPropertyChangeSupport().firePropertyChange ("flagAprovaOrdemCompra", flagAprovaOrdemCompraOld, flagAprovaOrdemCompra);
	}



	/**
	 * Return the value associated with the column: utiliza_tab_preco_pro_cli
	 */
	public java.lang.String getFlagUtilizaTabelaPrecoProdutoCliente () {
		return getPropertyValue(this, flagUtilizaTabelaPrecoProdutoCliente, PROP_FLAG_UTILIZA_TABELA_PRECO_PRODUTO_CLIENTE); 
	}

	/**
	 * Set the value related to the column: utiliza_tab_preco_pro_cli
	 * @param flagUtilizaTabelaPrecoProdutoCliente the utiliza_tab_preco_pro_cli value
	 */
	public void setFlagUtilizaTabelaPrecoProdutoCliente (java.lang.String flagUtilizaTabelaPrecoProdutoCliente) {
//        java.lang.String flagUtilizaTabelaPrecoProdutoClienteOld = this.flagUtilizaTabelaPrecoProdutoCliente;
		this.flagUtilizaTabelaPrecoProdutoCliente = flagUtilizaTabelaPrecoProdutoCliente;
//        this.getPropertyChangeSupport().firePropertyChange ("flagUtilizaTabelaPrecoProdutoCliente", flagUtilizaTabelaPrecoProdutoClienteOld, flagUtilizaTabelaPrecoProdutoCliente);
	}



	/**
	 * Return the value associated with the column: ds_imagem_login
	 */
	public java.lang.String getCaminhoImagemLogin () {
		return getPropertyValue(this, caminhoImagemLogin, PROP_CAMINHO_IMAGEM_LOGIN); 
	}

	/**
	 * Set the value related to the column: ds_imagem_login
	 * @param caminhoImagemLogin the ds_imagem_login value
	 */
	public void setCaminhoImagemLogin (java.lang.String caminhoImagemLogin) {
//        java.lang.String caminhoImagemLoginOld = this.caminhoImagemLogin;
		this.caminhoImagemLogin = caminhoImagemLogin;
//        this.getPropertyChangeSupport().firePropertyChange ("caminhoImagemLogin", caminhoImagemLoginOld, caminhoImagemLogin);
	}



	/**
	 * Return the value associated with the column: intervalo_validade_continuo
	 */
	public java.lang.Long getIntervaloValidadeContinuo () {
		return getPropertyValue(this, intervaloValidadeContinuo, PROP_INTERVALO_VALIDADE_CONTINUO); 
	}

	/**
	 * Set the value related to the column: intervalo_validade_continuo
	 * @param intervaloValidadeContinuo the intervalo_validade_continuo value
	 */
	public void setIntervaloValidadeContinuo (java.lang.Long intervaloValidadeContinuo) {
//        java.lang.Long intervaloValidadeContinuoOld = this.intervaloValidadeContinuo;
		this.intervaloValidadeContinuo = intervaloValidadeContinuo;
//        this.getPropertyChangeSupport().firePropertyChange ("intervaloValidadeContinuo", intervaloValidadeContinuoOld, intervaloValidadeContinuo);
	}



	/**
	 * Return the value associated with the column: aprova_pic_setor
	 */
	public java.lang.String getFlagAprovaPedidoInternoCompraSetor () {
		return getPropertyValue(this, flagAprovaPedidoInternoCompraSetor, PROP_FLAG_APROVA_PEDIDO_INTERNO_COMPRA_SETOR); 
	}

	/**
	 * Set the value related to the column: aprova_pic_setor
	 * @param flagAprovaPedidoInternoCompraSetor the aprova_pic_setor value
	 */
	public void setFlagAprovaPedidoInternoCompraSetor (java.lang.String flagAprovaPedidoInternoCompraSetor) {
//        java.lang.String flagAprovaPedidoInternoCompraSetorOld = this.flagAprovaPedidoInternoCompraSetor;
		this.flagAprovaPedidoInternoCompraSetor = flagAprovaPedidoInternoCompraSetor;
//        this.getPropertyChangeSupport().firePropertyChange ("flagAprovaPedidoInternoCompraSetor", flagAprovaPedidoInternoCompraSetorOld, flagAprovaPedidoInternoCompraSetor);
	}



	/**
	 * Return the value associated with the column: aprova_oc_dentro_do_limite
	 */
	public java.lang.String getFlagAprovaOrdemCompraDentroDoLimite () {
		return getPropertyValue(this, flagAprovaOrdemCompraDentroDoLimite, PROP_FLAG_APROVA_ORDEM_COMPRA_DENTRO_DO_LIMITE); 
	}

	/**
	 * Set the value related to the column: aprova_oc_dentro_do_limite
	 * @param flagAprovaOrdemCompraDentroDoLimite the aprova_oc_dentro_do_limite value
	 */
	public void setFlagAprovaOrdemCompraDentroDoLimite (java.lang.String flagAprovaOrdemCompraDentroDoLimite) {
//        java.lang.String flagAprovaOrdemCompraDentroDoLimiteOld = this.flagAprovaOrdemCompraDentroDoLimite;
		this.flagAprovaOrdemCompraDentroDoLimite = flagAprovaOrdemCompraDentroDoLimite;
//        this.getPropertyChangeSupport().firePropertyChange ("flagAprovaOrdemCompraDentroDoLimite", flagAprovaOrdemCompraDentroDoLimiteOld, flagAprovaOrdemCompraDentroDoLimite);
	}



	/**
	 * Return the value associated with the column: valida_cpf_cnpj
	 */
	public java.lang.String getValidaCpfCnpj () {
		return getPropertyValue(this, validaCpfCnpj, PROP_VALIDA_CPF_CNPJ); 
	}

	/**
	 * Set the value related to the column: valida_cpf_cnpj
	 * @param validaCpfCnpj the valida_cpf_cnpj value
	 */
	public void setValidaCpfCnpj (java.lang.String validaCpfCnpj) {
//        java.lang.String validaCpfCnpjOld = this.validaCpfCnpj;
		this.validaCpfCnpj = validaCpfCnpj;
//        this.getPropertyChangeSupport().firePropertyChange ("validaCpfCnpj", validaCpfCnpjOld, validaCpfCnpj);
	}



	/**
	 * Return the value associated with the column: range_qtde_padrao
	 */
	public java.lang.Double getRangeQuantidadePadrao () {
		return getPropertyValue(this, rangeQuantidadePadrao, PROP_RANGE_QUANTIDADE_PADRAO); 
	}

	/**
	 * Set the value related to the column: range_qtde_padrao
	 * @param rangeQuantidadePadrao the range_qtde_padrao value
	 */
	public void setRangeQuantidadePadrao (java.lang.Double rangeQuantidadePadrao) {
//        java.lang.Double rangeQuantidadePadraoOld = this.rangeQuantidadePadrao;
		this.rangeQuantidadePadrao = rangeQuantidadePadrao;
//        this.getPropertyChangeSupport().firePropertyChange ("rangeQuantidadePadrao", rangeQuantidadePadraoOld, rangeQuantidadePadrao);
	}



	/**
	 * Return the value associated with the column: caminho_logo_prefeitura
	 */
	public java.lang.String getCaminhoLogoPrefeitura () {
		return getPropertyValue(this, caminhoLogoPrefeitura, PROP_CAMINHO_LOGO_PREFEITURA); 
	}

	/**
	 * Set the value related to the column: caminho_logo_prefeitura
	 * @param caminhoLogoPrefeitura the caminho_logo_prefeitura value
	 */
	public void setCaminhoLogoPrefeitura (java.lang.String caminhoLogoPrefeitura) {
//        java.lang.String caminhoLogoPrefeituraOld = this.caminhoLogoPrefeitura;
		this.caminhoLogoPrefeitura = caminhoLogoPrefeitura;
//        this.getPropertyChangeSupport().firePropertyChange ("caminhoLogoPrefeitura", caminhoLogoPrefeituraOld, caminhoLogoPrefeitura);
	}



	/**
	 * Return the value associated with the column: presao_art_baixa
	 */
	public java.lang.String getPressaoArterialBaixa () {
		return getPropertyValue(this, pressaoArterialBaixa, PROP_PRESSAO_ARTERIAL_BAIXA); 
	}

	/**
	 * Set the value related to the column: presao_art_baixa
	 * @param pressaoArterialBaixa the presao_art_baixa value
	 */
	public void setPressaoArterialBaixa (java.lang.String pressaoArterialBaixa) {
//        java.lang.String pressaoArterialBaixaOld = this.pressaoArterialBaixa;
		this.pressaoArterialBaixa = pressaoArterialBaixa;
//        this.getPropertyChangeSupport().firePropertyChange ("pressaoArterialBaixa", pressaoArterialBaixaOld, pressaoArterialBaixa);
	}



	/**
	 * Return the value associated with the column: presao_art_normal
	 */
	public java.lang.String getPressaoArterialNormal () {
		return getPropertyValue(this, pressaoArterialNormal, PROP_PRESSAO_ARTERIAL_NORMAL); 
	}

	/**
	 * Set the value related to the column: presao_art_normal
	 * @param pressaoArterialNormal the presao_art_normal value
	 */
	public void setPressaoArterialNormal (java.lang.String pressaoArterialNormal) {
//        java.lang.String pressaoArterialNormalOld = this.pressaoArterialNormal;
		this.pressaoArterialNormal = pressaoArterialNormal;
//        this.getPropertyChangeSupport().firePropertyChange ("pressaoArterialNormal", pressaoArterialNormalOld, pressaoArterialNormal);
	}



	/**
	 * Return the value associated with the column: presao_art_alta
	 */
	public java.lang.String getPressaoArterialAlta () {
		return getPropertyValue(this, pressaoArterialAlta, PROP_PRESSAO_ARTERIAL_ALTA); 
	}

	/**
	 * Set the value related to the column: presao_art_alta
	 * @param pressaoArterialAlta the presao_art_alta value
	 */
	public void setPressaoArterialAlta (java.lang.String pressaoArterialAlta) {
//        java.lang.String pressaoArterialAltaOld = this.pressaoArterialAlta;
		this.pressaoArterialAlta = pressaoArterialAlta;
//        this.getPropertyChangeSupport().firePropertyChange ("pressaoArterialAlta", pressaoArterialAltaOld, pressaoArterialAlta);
	}



	/**
	 * Return the value associated with the column: dt_comp_procedimento
	 */
	public java.util.Date getDataCompetenciaProcedimento () {
		return getPropertyValue(this, dataCompetenciaProcedimento, PROP_DATA_COMPETENCIA_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: dt_comp_procedimento
	 * @param dataCompetenciaProcedimento the dt_comp_procedimento value
	 */
	public void setDataCompetenciaProcedimento (java.util.Date dataCompetenciaProcedimento) {
//        java.util.Date dataCompetenciaProcedimentoOld = this.dataCompetenciaProcedimento;
		this.dataCompetenciaProcedimento = dataCompetenciaProcedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCompetenciaProcedimento", dataCompetenciaProcedimentoOld, dataCompetenciaProcedimento);
	}



	/**
	 * Return the value associated with the column: path_file_manager_web
	 */
	public java.lang.String getPathFileManagerWeb () {
		return getPropertyValue(this, pathFileManagerWeb, PROP_PATH_FILE_MANAGER_WEB); 
	}

	/**
	 * Set the value related to the column: path_file_manager_web
	 * @param pathFileManagerWeb the path_file_manager_web value
	 */
	public void setPathFileManagerWeb (java.lang.String pathFileManagerWeb) {
//        java.lang.String pathFileManagerWebOld = this.pathFileManagerWeb;
		this.pathFileManagerWeb = pathFileManagerWeb;
//        this.getPropertyChangeSupport().firePropertyChange ("pathFileManagerWeb", pathFileManagerWebOld, pathFileManagerWeb);
	}



	/**
	 * Return the value associated with the column: path_cadsus
	 */
	public java.lang.String getPathCadsus () {
		return getPropertyValue(this, pathCadsus, PROP_PATH_CADSUS); 
	}

	/**
	 * Set the value related to the column: path_cadsus
	 * @param pathCadsus the path_cadsus value
	 */
	public void setPathCadsus (java.lang.String pathCadsus) {
//        java.lang.String pathCadsusOld = this.pathCadsus;
		this.pathCadsus = pathCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("pathCadsus", pathCadsusOld, pathCadsus);
	}



	/**
	 * Return the value associated with the column: path_tab_corporativas
	 */
	public java.lang.String getPathTabelasCorporativas () {
		return getPropertyValue(this, pathTabelasCorporativas, PROP_PATH_TABELAS_CORPORATIVAS); 
	}

	/**
	 * Set the value related to the column: path_tab_corporativas
	 * @param pathTabelasCorporativas the path_tab_corporativas value
	 */
	public void setPathTabelasCorporativas (java.lang.String pathTabelasCorporativas) {
//        java.lang.String pathTabelasCorporativasOld = this.pathTabelasCorporativas;
		this.pathTabelasCorporativas = pathTabelasCorporativas;
//        this.getPropertyChangeSupport().firePropertyChange ("pathTabelasCorporativas", pathTabelasCorporativasOld, pathTabelasCorporativas);
	}



	/**
	 * Return the value associated with the column: driver_base_cnes
	 */
	public java.lang.String getDriverBaseCnes () {
		return getPropertyValue(this, driverBaseCnes, PROP_DRIVER_BASE_CNES); 
	}

	/**
	 * Set the value related to the column: driver_base_cnes
	 * @param driverBaseCnes the driver_base_cnes value
	 */
	public void setDriverBaseCnes (java.lang.String driverBaseCnes) {
//        java.lang.String driverBaseCnesOld = this.driverBaseCnes;
		this.driverBaseCnes = driverBaseCnes;
//        this.getPropertyChangeSupport().firePropertyChange ("driverBaseCnes", driverBaseCnesOld, driverBaseCnes);
	}



	/**
	 * Return the value associated with the column: url_base_cnes
	 */
	public java.lang.String getUrlBaseCnes () {
		return getPropertyValue(this, urlBaseCnes, PROP_URL_BASE_CNES); 
	}

	/**
	 * Set the value related to the column: url_base_cnes
	 * @param urlBaseCnes the url_base_cnes value
	 */
	public void setUrlBaseCnes (java.lang.String urlBaseCnes) {
//        java.lang.String urlBaseCnesOld = this.urlBaseCnes;
		this.urlBaseCnes = urlBaseCnes;
//        this.getPropertyChangeSupport().firePropertyChange ("urlBaseCnes", urlBaseCnesOld, urlBaseCnes);
	}



	/**
	 * Return the value associated with the column: usuario_base_cnes
	 */
	public java.lang.String getUsuarioBaseCnes () {
		return getPropertyValue(this, usuarioBaseCnes, PROP_USUARIO_BASE_CNES); 
	}

	/**
	 * Set the value related to the column: usuario_base_cnes
	 * @param usuarioBaseCnes the usuario_base_cnes value
	 */
	public void setUsuarioBaseCnes (java.lang.String usuarioBaseCnes) {
//        java.lang.String usuarioBaseCnesOld = this.usuarioBaseCnes;
		this.usuarioBaseCnes = usuarioBaseCnes;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioBaseCnes", usuarioBaseCnesOld, usuarioBaseCnes);
	}



	/**
	 * Return the value associated with the column: senha_base_cnes
	 */
	public java.lang.String getSenhaBaseCnes () {
		return getPropertyValue(this, senhaBaseCnes, PROP_SENHA_BASE_CNES); 
	}

	/**
	 * Set the value related to the column: senha_base_cnes
	 * @param senhaBaseCnes the senha_base_cnes value
	 */
	public void setSenhaBaseCnes (java.lang.String senhaBaseCnes) {
//        java.lang.String senhaBaseCnesOld = this.senhaBaseCnes;
		this.senhaBaseCnes = senhaBaseCnes;
//        this.getPropertyChangeSupport().firePropertyChange ("senhaBaseCnes", senhaBaseCnesOld, senhaBaseCnes);
	}



	/**
	 * Return the value associated with the column: smtp_servidor
	 */
	public java.lang.String getSmtpServidor () {
		return getPropertyValue(this, smtpServidor, PROP_SMTP_SERVIDOR); 
	}

	/**
	 * Set the value related to the column: smtp_servidor
	 * @param smtpServidor the smtp_servidor value
	 */
	public void setSmtpServidor (java.lang.String smtpServidor) {
//        java.lang.String smtpServidorOld = this.smtpServidor;
		this.smtpServidor = smtpServidor;
//        this.getPropertyChangeSupport().firePropertyChange ("smtpServidor", smtpServidorOld, smtpServidor);
	}



	/**
	 * Return the value associated with the column: smtp_usuario
	 */
	public java.lang.String getSmtpUsuario () {
		return getPropertyValue(this, smtpUsuario, PROP_SMTP_USUARIO); 
	}

	/**
	 * Set the value related to the column: smtp_usuario
	 * @param smtpUsuario the smtp_usuario value
	 */
	public void setSmtpUsuario (java.lang.String smtpUsuario) {
//        java.lang.String smtpUsuarioOld = this.smtpUsuario;
		this.smtpUsuario = smtpUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("smtpUsuario", smtpUsuarioOld, smtpUsuario);
	}



	/**
	 * Return the value associated with the column: smtp_senha
	 */
	public java.lang.String getSmtpSenha () {
		return getPropertyValue(this, smtpSenha, PROP_SMTP_SENHA); 
	}

	/**
	 * Set the value related to the column: smtp_senha
	 * @param smtpSenha the smtp_senha value
	 */
	public void setSmtpSenha (java.lang.String smtpSenha) {
//        java.lang.String smtpSenhaOld = this.smtpSenha;
		this.smtpSenha = smtpSenha;
//        this.getPropertyChangeSupport().firePropertyChange ("smtpSenha", smtpSenhaOld, smtpSenha);
	}



	/**
	 * Return the value associated with the column: nm_prefeitura
	 */
	public java.lang.String getNomePrefeitura () {
		return getPropertyValue(this, nomePrefeitura, PROP_NOME_PREFEITURA); 
	}

	/**
	 * Set the value related to the column: nm_prefeitura
	 * @param nomePrefeitura the nm_prefeitura value
	 */
	public void setNomePrefeitura (java.lang.String nomePrefeitura) {
//        java.lang.String nomePrefeituraOld = this.nomePrefeitura;
		this.nomePrefeitura = nomePrefeitura;
//        this.getPropertyChangeSupport().firePropertyChange ("nomePrefeitura", nomePrefeituraOld, nomePrefeitura);
	}



	/**
	 * Return the value associated with the column: dt_export_cadsus
	 */
	public java.util.Date getDataExportacaoCadsus () {
		return getPropertyValue(this, dataExportacaoCadsus, PROP_DATA_EXPORTACAO_CADSUS); 
	}

	/**
	 * Set the value related to the column: dt_export_cadsus
	 * @param dataExportacaoCadsus the dt_export_cadsus value
	 */
	public void setDataExportacaoCadsus (java.util.Date dataExportacaoCadsus) {
//        java.util.Date dataExportacaoCadsusOld = this.dataExportacaoCadsus;
		this.dataExportacaoCadsus = dataExportacaoCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("dataExportacaoCadsus", dataExportacaoCadsusOld, dataExportacaoCadsus);
	}



	/**
	 * Return the value associated with the column: sftp_usuario
	 */
	public java.lang.String getSftpUsuario () {
		return getPropertyValue(this, sftpUsuario, PROP_SFTP_USUARIO); 
	}

	/**
	 * Set the value related to the column: sftp_usuario
	 * @param sftpUsuario the sftp_usuario value
	 */
	public void setSftpUsuario (java.lang.String sftpUsuario) {
//        java.lang.String sftpUsuarioOld = this.sftpUsuario;
		this.sftpUsuario = sftpUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("sftpUsuario", sftpUsuarioOld, sftpUsuario);
	}



	/**
	 * Return the value associated with the column: sftp_senha
	 */
	public java.lang.String getSftpSenha () {
		return getPropertyValue(this, sftpSenha, PROP_SFTP_SENHA); 
	}

	/**
	 * Set the value related to the column: sftp_senha
	 * @param sftpSenha the sftp_senha value
	 */
	public void setSftpSenha (java.lang.String sftpSenha) {
//        java.lang.String sftpSenhaOld = this.sftpSenha;
		this.sftpSenha = sftpSenha;
//        this.getPropertyChangeSupport().firePropertyChange ("sftpSenha", sftpSenhaOld, sftpSenha);
	}



	/**
	 * Return the value associated with the column: sftp_servidor
	 */
	public java.lang.String getSftpServidor () {
		return getPropertyValue(this, sftpServidor, PROP_SFTP_SERVIDOR); 
	}

	/**
	 * Set the value related to the column: sftp_servidor
	 * @param sftpServidor the sftp_servidor value
	 */
	public void setSftpServidor (java.lang.String sftpServidor) {
//        java.lang.String sftpServidorOld = this.sftpServidor;
		this.sftpServidor = sftpServidor;
//        this.getPropertyChangeSupport().firePropertyChange ("sftpServidor", sftpServidorOld, sftpServidor);
	}



	/**
	 * Return the value associated with the column: sftp_compressao
	 */
	public java.lang.String getSftpCompressao () {
		return getPropertyValue(this, sftpCompressao, PROP_SFTP_COMPRESSAO); 
	}

	/**
	 * Set the value related to the column: sftp_compressao
	 * @param sftpCompressao the sftp_compressao value
	 */
	public void setSftpCompressao (java.lang.String sftpCompressao) {
//        java.lang.String sftpCompressaoOld = this.sftpCompressao;
		this.sftpCompressao = sftpCompressao;
//        this.getPropertyChangeSupport().firePropertyChange ("sftpCompressao", sftpCompressaoOld, sftpCompressao);
	}



	/**
	 * Return the value associated with the column: sftp_diretorio_raiz
	 */
	public java.lang.String getSftpDiretorioRaiz () {
		return getPropertyValue(this, sftpDiretorioRaiz, PROP_SFTP_DIRETORIO_RAIZ); 
	}

	/**
	 * Set the value related to the column: sftp_diretorio_raiz
	 * @param sftpDiretorioRaiz the sftp_diretorio_raiz value
	 */
	public void setSftpDiretorioRaiz (java.lang.String sftpDiretorioRaiz) {
//        java.lang.String sftpDiretorioRaizOld = this.sftpDiretorioRaiz;
		this.sftpDiretorioRaiz = sftpDiretorioRaiz;
//        this.getPropertyChangeSupport().firePropertyChange ("sftpDiretorioRaiz", sftpDiretorioRaizOld, sftpDiretorioRaiz);
	}



	/**
	 * Return the value associated with the column: tempo_sessao
	 */
	public java.lang.Long getTempoSessao () {
		return getPropertyValue(this, tempoSessao, PROP_TEMPO_SESSAO); 
	}

	/**
	 * Set the value related to the column: tempo_sessao
	 * @param tempoSessao the tempo_sessao value
	 */
	public void setTempoSessao (java.lang.Long tempoSessao) {
//        java.lang.Long tempoSessaoOld = this.tempoSessao;
		this.tempoSessao = tempoSessao;
//        this.getPropertyChangeSupport().firePropertyChange ("tempoSessao", tempoSessaoOld, tempoSessao);
	}



	/**
	 * Return the value associated with the column: login_bloqueado
	 */
	public java.lang.Long getLoginBloqueado () {
		return getPropertyValue(this, loginBloqueado, PROP_LOGIN_BLOQUEADO); 
	}

	/**
	 * Set the value related to the column: login_bloqueado
	 * @param loginBloqueado the login_bloqueado value
	 */
	public void setLoginBloqueado (java.lang.Long loginBloqueado) {
//        java.lang.Long loginBloqueadoOld = this.loginBloqueado;
		this.loginBloqueado = loginBloqueado;
//        this.getPropertyChangeSupport().firePropertyChange ("loginBloqueado", loginBloqueadoOld, loginBloqueado);
	}



	/**
	 * Return the value associated with the column: keycloack_client_id
	 */
	public java.lang.String getKeycloackClientId () {
		return getPropertyValue(this, keycloackClientId, PROP_KEYCLOACK_CLIENT_ID); 
	}

	/**
	 * Set the value related to the column: keycloack_client_id
	 * @param keycloackClientId the keycloack_client_id value
	 */
	public void setKeycloackClientId (java.lang.String keycloackClientId) {
//        java.lang.String keycloackClientIdOld = this.keycloackClientId;
		this.keycloackClientId = keycloackClientId;
//        this.getPropertyChangeSupport().firePropertyChange ("keycloackClientId", keycloackClientIdOld, keycloackClientId);
	}



	/**
	 * Return the value associated with the column: keycloack_client_secret
	 */
	public java.lang.String getKeycloackClientSecret () {
		return getPropertyValue(this, keycloackClientSecret, PROP_KEYCLOACK_CLIENT_SECRET); 
	}

	/**
	 * Set the value related to the column: keycloack_client_secret
	 * @param keycloackClientSecret the keycloack_client_secret value
	 */
	public void setKeycloackClientSecret (java.lang.String keycloackClientSecret) {
//        java.lang.String keycloackClientSecretOld = this.keycloackClientSecret;
		this.keycloackClientSecret = keycloackClientSecret;
//        this.getPropertyChangeSupport().firePropertyChange ("keycloackClientSecret", keycloackClientSecretOld, keycloackClientSecret);
	}



	/**
	 * Return the value associated with the column: keycloack_public_key
	 */
	public java.lang.String getKeycloackPublicKey () {
		return getPropertyValue(this, keycloackPublicKey, PROP_KEYCLOACK_PUBLIC_KEY); 
	}

	/**
	 * Set the value related to the column: keycloack_public_key
	 * @param keycloackPublicKey the keycloack_public_key value
	 */
	public void setKeycloackPublicKey (java.lang.String keycloackPublicKey) {
//        java.lang.String keycloackPublicKeyOld = this.keycloackPublicKey;
		this.keycloackPublicKey = keycloackPublicKey;
//        this.getPropertyChangeSupport().firePropertyChange ("keycloackPublicKey", keycloackPublicKeyOld, keycloackPublicKey);
	}



	/**
	 * Return the value associated with the column: limite_resultados_relatorios
	 */
	public java.lang.Long getLimiteResultadosRelatorios () {
		return getPropertyValue(this, limiteResultadosRelatorios, PROP_LIMITE_RESULTADOS_RELATORIOS); 
	}

	/**
	 * Set the value related to the column: limite_resultados_relatorios
	 * @param limiteResultadosRelatorios the limite_resultados_relatorios value
	 */
	public void setLimiteResultadosRelatorios (java.lang.Long limiteResultadosRelatorios) {
//        java.lang.Long limiteResultadosRelatoriosOld = this.limiteResultadosRelatorios;
		this.limiteResultadosRelatorios = limiteResultadosRelatorios;
//        this.getPropertyChangeSupport().firePropertyChange ("limiteResultadosRelatorios", limiteResultadosRelatoriosOld, limiteResultadosRelatorios);
	}



	/**
	 * Return the value associated with the column: google_recaptcha_key
	 */
	public java.lang.String getGoogleReCaptchaKey () {
		return getPropertyValue(this, googleReCaptchaKey, PROP_GOOGLE_RECAPTCHA_KEY);
	}

	/**
	 * Set the value related to the column: google_recaptcha_key
	 * @param googleReCaptchaKey the google_recaptcha_key value
	 */
	public void setGoogleReCaptchaKey (java.lang.String googleReCaptchaKey) {
//        java.lang.String googleReCaptchaKeyOld = this.googleReCaptchaKey;
		this.googleReCaptchaKey = googleReCaptchaKey;
//        this.getPropertyChangeSupport().firePropertyChange ("googleReCaptchaKey", googleReCaptchaKeyOld, googleReCaptchaKey);
	}



	/**
	 * Return the value associated with the column: cod_tip_doc_entrada
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento getTipoDocumentoEntrada () {
		return getPropertyValue(this, tipoDocumentoEntrada, PROP_TIPO_DOCUMENTO_ENTRADA); 
	}

	/**
	 * Set the value related to the column: cod_tip_doc_entrada
	 * @param tipoDocumentoEntrada the cod_tip_doc_entrada value
	 */
	public void setTipoDocumentoEntrada (br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoEntrada) {
//        br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoEntradaOld = this.tipoDocumentoEntrada;
		this.tipoDocumentoEntrada = tipoDocumentoEntrada;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDocumentoEntrada", tipoDocumentoEntradaOld, tipoDocumentoEntrada);
	}



	/**
	 * Return the value associated with the column: cod_tip_doc_saida
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento getTipoDocumentoSaida () {
		return getPropertyValue(this, tipoDocumentoSaida, PROP_TIPO_DOCUMENTO_SAIDA); 
	}

	/**
	 * Set the value related to the column: cod_tip_doc_saida
	 * @param tipoDocumentoSaida the cod_tip_doc_saida value
	 */
	public void setTipoDocumentoSaida (br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoSaida) {
//        br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoSaidaOld = this.tipoDocumentoSaida;
		this.tipoDocumentoSaida = tipoDocumentoSaida;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDocumentoSaida", tipoDocumentoSaidaOld, tipoDocumentoSaida);
	}



	/**
	 * Return the value associated with the column: cod_tip_doc_dispensacao
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento getTipoDocumentoDispensacaoMedicamento () {
		return getPropertyValue(this, tipoDocumentoDispensacaoMedicamento, PROP_TIPO_DOCUMENTO_DISPENSACAO_MEDICAMENTO); 
	}

	/**
	 * Set the value related to the column: cod_tip_doc_dispensacao
	 * @param tipoDocumentoDispensacaoMedicamento the cod_tip_doc_dispensacao value
	 */
	public void setTipoDocumentoDispensacaoMedicamento (br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoDispensacaoMedicamento) {
//        br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoDispensacaoMedicamentoOld = this.tipoDocumentoDispensacaoMedicamento;
		this.tipoDocumentoDispensacaoMedicamento = tipoDocumentoDispensacaoMedicamento;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDocumentoDispensacaoMedicamento", tipoDocumentoDispensacaoMedicamentoOld, tipoDocumentoDispensacaoMedicamento);
	}



	/**
	 * Return the value associated with the column: cod_tip_doc_enc_ent
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento getTipoDocumentoOperacaoEntrada () {
		return getPropertyValue(this, tipoDocumentoOperacaoEntrada, PROP_TIPO_DOCUMENTO_OPERACAO_ENTRADA); 
	}

	/**
	 * Set the value related to the column: cod_tip_doc_enc_ent
	 * @param tipoDocumentoOperacaoEntrada the cod_tip_doc_enc_ent value
	 */
	public void setTipoDocumentoOperacaoEntrada (br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoOperacaoEntrada) {
//        br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoOperacaoEntradaOld = this.tipoDocumentoOperacaoEntrada;
		this.tipoDocumentoOperacaoEntrada = tipoDocumentoOperacaoEntrada;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDocumentoOperacaoEntrada", tipoDocumentoOperacaoEntradaOld, tipoDocumentoOperacaoEntrada);
	}



	/**
	 * Return the value associated with the column: cod_tip_doc_enc_sai
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento getTipoDocumentoOperacaoSaida () {
		return getPropertyValue(this, tipoDocumentoOperacaoSaida, PROP_TIPO_DOCUMENTO_OPERACAO_SAIDA); 
	}

	/**
	 * Set the value related to the column: cod_tip_doc_enc_sai
	 * @param tipoDocumentoOperacaoSaida the cod_tip_doc_enc_sai value
	 */
	public void setTipoDocumentoOperacaoSaida (br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoOperacaoSaida) {
//        br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoOperacaoSaidaOld = this.tipoDocumentoOperacaoSaida;
		this.tipoDocumentoOperacaoSaida = tipoDocumentoOperacaoSaida;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDocumentoOperacaoSaida", tipoDocumentoOperacaoSaidaOld, tipoDocumentoOperacaoSaida);
	}



	/**
	 * Return the value associated with the column: cod_tip_doc_nf_ent
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento getTipoDocumentoNFEntrada () {
		return getPropertyValue(this, tipoDocumentoNFEntrada, PROP_TIPO_DOCUMENTO_N_F_ENTRADA); 
	}

	/**
	 * Set the value related to the column: cod_tip_doc_nf_ent
	 * @param tipoDocumentoNFEntrada the cod_tip_doc_nf_ent value
	 */
	public void setTipoDocumentoNFEntrada (br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoNFEntrada) {
//        br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoNFEntradaOld = this.tipoDocumentoNFEntrada;
		this.tipoDocumentoNFEntrada = tipoDocumentoNFEntrada;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDocumentoNFEntrada", tipoDocumentoNFEntradaOld, tipoDocumentoNFEntrada);
	}



	/**
	 * Return the value associated with the column: cod_tip_doc_nf_sai
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento getTipoDocumentoNFSaida () {
		return getPropertyValue(this, tipoDocumentoNFSaida, PROP_TIPO_DOCUMENTO_N_F_SAIDA); 
	}

	/**
	 * Set the value related to the column: cod_tip_doc_nf_sai
	 * @param tipoDocumentoNFSaida the cod_tip_doc_nf_sai value
	 */
	public void setTipoDocumentoNFSaida (br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoNFSaida) {
//        br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoNFSaidaOld = this.tipoDocumentoNFSaida;
		this.tipoDocumentoNFSaida = tipoDocumentoNFSaida;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDocumentoNFSaida", tipoDocumentoNFSaidaOld, tipoDocumentoNFSaida);
	}



	/**
	 * Return the value associated with the column: cod_tip_doc_sai_ped_transf
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento getTipoDocumentoPedidoTransferenciaSaida () {
		return getPropertyValue(this, tipoDocumentoPedidoTransferenciaSaida, PROP_TIPO_DOCUMENTO_PEDIDO_TRANSFERENCIA_SAIDA); 
	}

	/**
	 * Set the value related to the column: cod_tip_doc_sai_ped_transf
	 * @param tipoDocumentoPedidoTransferenciaSaida the cod_tip_doc_sai_ped_transf value
	 */
	public void setTipoDocumentoPedidoTransferenciaSaida (br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoPedidoTransferenciaSaida) {
//        br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoPedidoTransferenciaSaidaOld = this.tipoDocumentoPedidoTransferenciaSaida;
		this.tipoDocumentoPedidoTransferenciaSaida = tipoDocumentoPedidoTransferenciaSaida;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDocumentoPedidoTransferenciaSaida", tipoDocumentoPedidoTransferenciaSaidaOld, tipoDocumentoPedidoTransferenciaSaida);
	}



	/**
	 * Return the value associated with the column: cod_tip_doc_ent_ped_transf
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento getTipoDocumentoPedidoTransferenciaEntrada () {
		return getPropertyValue(this, tipoDocumentoPedidoTransferenciaEntrada, PROP_TIPO_DOCUMENTO_PEDIDO_TRANSFERENCIA_ENTRADA); 
	}

	/**
	 * Set the value related to the column: cod_tip_doc_ent_ped_transf
	 * @param tipoDocumentoPedidoTransferenciaEntrada the cod_tip_doc_ent_ped_transf value
	 */
	public void setTipoDocumentoPedidoTransferenciaEntrada (br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoPedidoTransferenciaEntrada) {
//        br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoPedidoTransferenciaEntradaOld = this.tipoDocumentoPedidoTransferenciaEntrada;
		this.tipoDocumentoPedidoTransferenciaEntrada = tipoDocumentoPedidoTransferenciaEntrada;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDocumentoPedidoTransferenciaEntrada", tipoDocumentoPedidoTransferenciaEntradaOld, tipoDocumentoPedidoTransferenciaEntrada);
	}



	/**
	 * Return the value associated with the column: cod_pessoa
	 */
	public br.com.ksisolucoes.vo.basico.Pessoa getPessoa () {
		return getPropertyValue(this, pessoa, PROP_PESSOA); 
	}

	/**
	 * Set the value related to the column: cod_pessoa
	 * @param pessoa the cod_pessoa value
	 */
	public void setPessoa (br.com.ksisolucoes.vo.basico.Pessoa pessoa) {
//        br.com.ksisolucoes.vo.basico.Pessoa pessoaOld = this.pessoa;
		this.pessoa = pessoa;
//        this.getPropertyChangeSupport().firePropertyChange ("pessoa", pessoaOld, pessoa);
	}



	/**
	 * Return the value associated with the column: cod_pro
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Produto getProduto () {
		return getPropertyValue(this, produto, PROP_PRODUTO); 
	}

	/**
	 * Set the value related to the column: cod_pro
	 * @param produto the cod_pro value
	 */
	public void setProduto (br.com.ksisolucoes.vo.entradas.estoque.Produto produto) {
//        br.com.ksisolucoes.vo.entradas.estoque.Produto produtoOld = this.produto;
		this.produto = produto;
//        this.getPropertyChangeSupport().firePropertyChange ("produto", produtoOld, produto);
	}



	/**
	 * Return the value associated with the column: cod_tip_doc
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento getTipoDocumento () {
		return getPropertyValue(this, tipoDocumento, PROP_TIPO_DOCUMENTO); 
	}

	/**
	 * Set the value related to the column: cod_tip_doc
	 * @param tipoDocumento the cod_tip_doc value
	 */
	public void setTipoDocumento (br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumento) {
//        br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoOld = this.tipoDocumento;
		this.tipoDocumento = tipoDocumento;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDocumento", tipoDocumentoOld, tipoDocumento);
	}



	/**
	 * Return the value associated with the column: empresa_principal
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getUnidadePrincipal () {
		return getPropertyValue(this, unidadePrincipal, PROP_UNIDADE_PRINCIPAL); 
	}

	/**
	 * Set the value related to the column: empresa_principal
	 * @param unidadePrincipal the empresa_principal value
	 */
	public void setUnidadePrincipal (br.com.ksisolucoes.vo.basico.Empresa unidadePrincipal) {
//        br.com.ksisolucoes.vo.basico.Empresa unidadePrincipalOld = this.unidadePrincipal;
		this.unidadePrincipal = unidadePrincipal;
//        this.getPropertyChangeSupport().firePropertyChange ("unidadePrincipal", unidadePrincipalOld, unidadePrincipal);
	}



	/**
	 * Return the value associated with the column: cod_tip_doc_pront_eme
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento getTipoDocumentoProntuarioEmergencia () {
		return getPropertyValue(this, tipoDocumentoProntuarioEmergencia, PROP_TIPO_DOCUMENTO_PRONTUARIO_EMERGENCIA); 
	}

	/**
	 * Set the value related to the column: cod_tip_doc_pront_eme
	 * @param tipoDocumentoProntuarioEmergencia the cod_tip_doc_pront_eme value
	 */
	public void setTipoDocumentoProntuarioEmergencia (br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoProntuarioEmergencia) {
//        br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoProntuarioEmergenciaOld = this.tipoDocumentoProntuarioEmergencia;
		this.tipoDocumentoProntuarioEmergencia = tipoDocumentoProntuarioEmergencia;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDocumentoProntuarioEmergencia", tipoDocumentoProntuarioEmergenciaOld, tipoDocumentoProntuarioEmergencia);
	}



	/**
	 * Return the value associated with the column: cod_tip_doc_bx_enf
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento getTipoDocumentoBaixaEnfermagem () {
		return getPropertyValue(this, tipoDocumentoBaixaEnfermagem, PROP_TIPO_DOCUMENTO_BAIXA_ENFERMAGEM); 
	}

	/**
	 * Set the value related to the column: cod_tip_doc_bx_enf
	 * @param tipoDocumentoBaixaEnfermagem the cod_tip_doc_bx_enf value
	 */
	public void setTipoDocumentoBaixaEnfermagem (br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoBaixaEnfermagem) {
//        br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoBaixaEnfermagemOld = this.tipoDocumentoBaixaEnfermagem;
		this.tipoDocumentoBaixaEnfermagem = tipoDocumentoBaixaEnfermagem;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDocumentoBaixaEnfermagem", tipoDocumentoBaixaEnfermagemOld, tipoDocumentoBaixaEnfermagem);
	}



	/**
	 * Return the value associated with the column: cod_tip_doc_ext_enf
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento getTipoDocumentoExtornoEnfermagem () {
		return getPropertyValue(this, tipoDocumentoExtornoEnfermagem, PROP_TIPO_DOCUMENTO_EXTORNO_ENFERMAGEM); 
	}

	/**
	 * Set the value related to the column: cod_tip_doc_ext_enf
	 * @param tipoDocumentoExtornoEnfermagem the cod_tip_doc_ext_enf value
	 */
	public void setTipoDocumentoExtornoEnfermagem (br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoExtornoEnfermagem) {
//        br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoExtornoEnfermagemOld = this.tipoDocumentoExtornoEnfermagem;
		this.tipoDocumentoExtornoEnfermagem = tipoDocumentoExtornoEnfermagem;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDocumentoExtornoEnfermagem", tipoDocumentoExtornoEnfermagemOld, tipoDocumentoExtornoEnfermagem);
	}



	/**
	 * Return the value associated with the column: cod_gru_hip
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.SubGrupo getSubGrupoHiperdia () {
		return getPropertyValue(this, subGrupoHiperdia, PROP_SUB_GRUPO_HIPERDIA); 
	}

	/**
	 * Set the value related to the column: cod_gru_hip
	 * @param subGrupoHiperdia the cod_gru_hip value
	 */
	public void setSubGrupoHiperdia (br.com.ksisolucoes.vo.entradas.estoque.SubGrupo subGrupoHiperdia) {
//        br.com.ksisolucoes.vo.entradas.estoque.SubGrupo subGrupoHiperdiaOld = this.subGrupoHiperdia;
		this.subGrupoHiperdia = subGrupoHiperdia;
//        this.getPropertyChangeSupport().firePropertyChange ("subGrupoHiperdia", subGrupoHiperdiaOld, subGrupoHiperdia);
	}



	/**
	 * Return the value associated with the column: cd_ger_arq_logo_doc
	 */
	public br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo getLogoDocumento () {
		return getPropertyValue(this, logoDocumento, PROP_LOGO_DOCUMENTO); 
	}

	/**
	 * Set the value related to the column: cd_ger_arq_logo_doc
	 * @param logoDocumento the cd_ger_arq_logo_doc value
	 */
	public void setLogoDocumento (br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoDocumento) {
//        br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoDocumentoOld = this.logoDocumento;
		this.logoDocumento = logoDocumento;
//        this.getPropertyChangeSupport().firePropertyChange ("logoDocumento", logoDocumentoOld, logoDocumento);
	}



	/**
	 * Return the value associated with the column: cd_ger_arq_logo_login
	 */
	public br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo getLogoLogin () {
		return getPropertyValue(this, logoLogin, PROP_LOGO_LOGIN); 
	}

	/**
	 * Set the value related to the column: cd_ger_arq_logo_login
	 * @param logoLogin the cd_ger_arq_logo_login value
	 */
	public void setLogoLogin (br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoLogin) {
//        br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoLoginOld = this.logoLogin;
		this.logoLogin = logoLogin;
//        this.getPropertyChangeSupport().firePropertyChange ("logoLogin", logoLoginOld, logoLogin);
	}



	/**
	 * Return the value associated with the column: cd_ger_arq_logo_etq
	 */
	public br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo getLogoEtiquetaEmbarque () {
		return getPropertyValue(this, logoEtiquetaEmbarque, PROP_LOGO_ETIQUETA_EMBARQUE); 
	}

	/**
	 * Set the value related to the column: cd_ger_arq_logo_etq
	 * @param logoEtiquetaEmbarque the cd_ger_arq_logo_etq value
	 */
	public void setLogoEtiquetaEmbarque (br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoEtiquetaEmbarque) {
//        br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoEtiquetaEmbarqueOld = this.logoEtiquetaEmbarque;
		this.logoEtiquetaEmbarque = logoEtiquetaEmbarque;
//        this.getPropertyChangeSupport().firePropertyChange ("logoEtiquetaEmbarque", logoEtiquetaEmbarqueOld, logoEtiquetaEmbarque);
	}



	/**
	 * Return the value associated with the column: cd_ger_arq_logo_sistema
	 */
	public br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo getLogoSistema () {
		return getPropertyValue(this, logoSistema, PROP_LOGO_SISTEMA); 
	}

	/**
	 * Set the value related to the column: cd_ger_arq_logo_sistema
	 * @param logoSistema the cd_ger_arq_logo_sistema value
	 */
	public void setLogoSistema (br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoSistema) {
//        br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoSistemaOld = this.logoSistema;
		this.logoSistema = logoSistema;
//        this.getPropertyChangeSupport().firePropertyChange ("logoSistema", logoSistemaOld, logoSistema);
	}



	/**
	 * Return the value associated with the column: cd_ger_arq_logo_sistema_tela_login
	 */
	public br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo getLogoSistemaTelaLogin () {
		return getPropertyValue(this, logoSistemaTelaLogin, PROP_LOGO_SISTEMA_TELA_LOGIN); 
	}

	/**
	 * Set the value related to the column: cd_ger_arq_logo_sistema_tela_login
	 * @param logoSistemaTelaLogin the cd_ger_arq_logo_sistema_tela_login value
	 */
	public void setLogoSistemaTelaLogin (br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoSistemaTelaLogin) {
//        br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoSistemaTelaLoginOld = this.logoSistemaTelaLogin;
		this.logoSistemaTelaLogin = logoSistemaTelaLogin;
//        this.getPropertyChangeSupport().firePropertyChange ("logoSistemaTelaLogin", logoSistemaTelaLoginOld, logoSistemaTelaLogin);
	}



	/**
	 * Return the value associated with the column: cd_ger_arq_logo_favicon
	 */
	public br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo getLogoFavicon () {
		return getPropertyValue(this, logoFavicon, PROP_LOGO_FAVICON); 
	}

	/**
	 * Set the value related to the column: cd_ger_arq_logo_favicon
	 * @param logoFavicon the cd_ger_arq_logo_favicon value
	 */
	public void setLogoFavicon (br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoFavicon) {
//        br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoFaviconOld = this.logoFavicon;
		this.logoFavicon = logoFavicon;
//        this.getPropertyChangeSupport().firePropertyChange ("logoFavicon", logoFaviconOld, logoFavicon);
	}



	/**
	 * Return the value associated with the column: cd_ger_arq_logo_lista_publica
	 */
	public br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo getLogoListaPublica () {
		return getPropertyValue(this, logoListaPublica, PROP_LOGO_LISTA_PUBLICA); 
	}

	/**
	 * Set the value related to the column: cd_ger_arq_logo_lista_publica
	 * @param logoListaPublica the cd_ger_arq_logo_lista_publica value
	 */
	public void setLogoListaPublica (br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoListaPublica) {
//        br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoListaPublicaOld = this.logoListaPublica;
		this.logoListaPublica = logoListaPublica;
//        this.getPropertyChangeSupport().firePropertyChange ("logoListaPublica", logoListaPublicaOld, logoListaPublica);
	}


	/**
	 * Return the value associated with the column: cd_ger_arq_unidade_lacen
	 */
	public br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo getUnidadeLacen () {
		return getPropertyValue(this, unidadeLacen, PROP_UNIDADE_LACEN);
	}

	/**
	 * Set the value related to the column: cd_ger_arq_unidade_lacen
	 * @param unidadeLacen the cd_ger_arq_unidade_lacen value
	 */
	public void setUnidadeLacen (br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo unidadeLacen) {
//        br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo unidadeLacenOld = this.unidadeLacen;
		this.unidadeLacen = unidadeLacen;
//        this.getPropertyChangeSupport().firePropertyChange ("unidadeLacen", unidadeLacenOld, unidadeLacen);
	}

	/**
	 * Return the value associated with the column: cd_ger_arq_logo_lacen
	 */
	public br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo getLogoLacen () {
		return getPropertyValue(this, logoLacen, PROP_LOGO_LACEN); 
	}

	/**
	 * Set the value related to the column: cd_ger_arq_logo_lacen
	 * @param logoLacen the cd_ger_arq_logo_lacen value
	 */
	public void setLogoLacen (br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoLacen) {
//        br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo logoLacenOld = this.logoLacen;
		this.logoLacen = logoLacen;
//        this.getPropertyChangeSupport().firePropertyChange ("logoLacen", logoLacenOld, logoLacen);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.Parametro)) return false;
		else {
			br.com.ksisolucoes.vo.basico.Parametro parametro = (br.com.ksisolucoes.vo.basico.Parametro) obj;
			if (null == this.getCodigo() || null == parametro.getCodigo()) return false;
			else return (this.getCodigo().equals(parametro.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}