package br.com.ksisolucoes.vo.exportacao;

import java.io.Serializable;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.exportacao.base.BaseExportacaoConsultaCsv;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class ExportacaoConsultaCsv extends BaseExportacaoConsultaCsv implements CodigoManager {
	private static final long serialVersionUID = 1L;
	public enum Filtros{

		PERIODO(1L);

		private Long value;

		private Filtros(Long value) {
			this.value = value;
		}

		public Long value(){
			return value;
		}

		@Override
		public String toString() {
			if (PERIODO.equals(this)) {
				return Bundle.getStringApplication("rotulo_periodo");
			}

			return Bundle.getStringApplication("rotulo_desconhecido");
		}

	}


/*[CONSTRUCTOR MARKER BEGIN]*/
	public ExportacaoConsultaCsv () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ExportacaoConsultaCsv (java.lang.Long codigo) {
		super(codigo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}