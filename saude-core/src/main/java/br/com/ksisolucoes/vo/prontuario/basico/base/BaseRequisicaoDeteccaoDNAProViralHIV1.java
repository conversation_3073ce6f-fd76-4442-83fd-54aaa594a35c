package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the requisicao_deteccao_dna table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="requisicao_deteccao_dna"
 */

public abstract class BaseRequisicaoDeteccaoDNAProViralHIV1 extends BaseRootVO implements Serializable {

	public static String REF = "RequisicaoDeteccaoDNAProViralHIV1";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_MOTIVO_SOLICITACAO = "motivoSolicitacao";
	public static final String PROP_JUSTIFICATIVA = "justificativa";
	public static final String PROP_EXAME_REQUISICAO = "exameRequisicao";


	// constructors
	public BaseRequisicaoDeteccaoDNAProViralHIV1 () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseRequisicaoDeteccaoDNAProViralHIV1 (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseRequisicaoDeteccaoDNAProViralHIV1 (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicao) {

		this.setCodigo(codigo);
		this.setExameRequisicao(exameRequisicao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long motivoSolicitacao;
	private java.lang.String justificativa;
	private java.util.Date dataCadastro;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_requisicao_deteccao_dna"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: motivo_solicitacao
	 */
	public java.lang.Long getMotivoSolicitacao () {
		return getPropertyValue(this, motivoSolicitacao, PROP_MOTIVO_SOLICITACAO); 
	}

	/**
	 * Set the value related to the column: motivo_solicitacao
	 * @param motivoSolicitacao the motivo_solicitacao value
	 */
	public void setMotivoSolicitacao (java.lang.Long motivoSolicitacao) {
//        java.lang.Long motivoSolicitacaoOld = this.motivoSolicitacao;
		this.motivoSolicitacao = motivoSolicitacao;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoSolicitacao", motivoSolicitacaoOld, motivoSolicitacao);
	}



	/**
	 * Return the value associated with the column: ds_justificativa
	 */
	public java.lang.String getJustificativa () {
		return getPropertyValue(this, justificativa, PROP_JUSTIFICATIVA); 
	}

	/**
	 * Set the value related to the column: ds_justificativa
	 * @param justificativa the ds_justificativa value
	 */
	public void setJustificativa (java.lang.String justificativa) {
//        java.lang.String justificativaOld = this.justificativa;
		this.justificativa = justificativa;
//        this.getPropertyChangeSupport().firePropertyChange ("justificativa", justificativaOld, justificativa);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: cd_exame_requisicao
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao getExameRequisicao () {
		return getPropertyValue(this, exameRequisicao, PROP_EXAME_REQUISICAO); 
	}

	/**
	 * Set the value related to the column: cd_exame_requisicao
	 * @param exameRequisicao the cd_exame_requisicao value
	 */
	public void setExameRequisicao (br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicao) {
//        br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicaoOld = this.exameRequisicao;
		this.exameRequisicao = exameRequisicao;
//        this.getPropertyChangeSupport().firePropertyChange ("exameRequisicao", exameRequisicaoOld, exameRequisicao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.RequisicaoDeteccaoDNAProViralHIV1)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.RequisicaoDeteccaoDNAProViralHIV1 requisicaoDeteccaoDNAProViralHIV1 = (br.com.ksisolucoes.vo.prontuario.basico.RequisicaoDeteccaoDNAProViralHIV1) obj;
			if (null == this.getCodigo() || null == requisicaoDeteccaoDNAProViralHIV1.getCodigo()) return false;
			else return (this.getCodigo().equals(requisicaoDeteccaoDNAProViralHIV1.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}