<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="ConvenioPaciente" table="convenio_paciente" >
        <id
            column="cd_convenio_paciente"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />    
        
        <property
            column="numero_registro"
            length="50" 
            name="numeroRegistro"
            type="string"
        />
        
        <many-to-one class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus" 
                     column="cd_usu_cadsus"
                     name="usuarioCadsus"
                     not-null="true"
        /> 
        
        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.Convenio" 
                     column="cd_convenio"
                     name="convenio"
                     not-null="true"
        /> 
    </class>
</hibernate-mapping>
