package br.com.ksisolucoes.vo.entradas.recebimento;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QuerySorter;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomSorter;
import br.com.ksisolucoes.dao.HQLConvertKeyToProperties;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.Unidade;
import br.com.ksisolucoes.vo.entradas.recebimento.base.BaseRegistroNotaFiscal;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;
import java.util.*;

/**
 * Esta classe est� relacionado com a tabela registro_notas_fiscais.
 * Classe dimensionada para customiza��es.
 *
 */
public class RegistroNotaFiscal extends BaseRegistroNotaFiscal implements CodigoManager {

    private static final long serialVersionUID = 1L;
    
    public static Long STATUS_ABERTO = 0L;
    public static Long STATUS_ENCERRADO = 2L;
    public static Long STATUS_PENDENTE = 3L;
    public static Long STATUS_ENTREGUE = 4L;
    
    public static Long ORIGEM_NORMAL = 0L;
    public static Long ORIGEM_CONSORCIO = 1L;
    public static Long ORIGEM_XML_NFE = 2L;
    public static Long ORIGEM_BRM = 3L;
    
    public static String PROP_ITEM_NOTA_FISCAL_SET = "itemNotaFiscalSet";
    public static String PROP_STATUS_DESCRICAO_FORMATADO = "statusDescricaoFormatado";

    private Set<RegistroItemNotaFiscal> itemNotaFiscalSet; 
    private Double totalCalculado = 0D;
    
/*[CONSTRUCTOR MARKER BEGIN]*/
	public RegistroNotaFiscal () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RegistroNotaFiscal (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RegistroNotaFiscal (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.financeiro.Serie serie,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		br.com.ksisolucoes.vo.basico.Pessoa fornecedor,
		br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumento,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.Long numeroNotaFiscal,
		java.util.Date dataEmissao,
		java.util.Date dataPortaria,
		java.util.Date dataLancamento,
		java.lang.Double valorIpi,
		java.lang.Double valorMercadoria,
		java.util.Date dataUsuario,
		java.lang.Double valorTotal,
		java.util.Date dataCadastro) {

		super (
			codigo,
			serie,
			empresa,
			fornecedor,
			tipoDocumento,
			usuario,
			numeroNotaFiscal,
			dataEmissao,
			dataPortaria,
			dataLancamento,
			valorIpi,
			valorMercadoria,
			dataUsuario,
			valorTotal,
			dataCadastro);
	}

/*[CONSTRUCTOR MARKER END]*/

    
    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public void setCodigoManager(Serializable key) {
        this.setCodigo((Long) key);
    }

    
    public Set<RegistroItemNotaFiscal> getItemNotaFiscalSet() {
        if( this.itemNotaFiscalSet == null ) {
            try {
                List parameters = new ArrayList<QueryParameter>();
                
                List<Long> statusList = new ArrayList<Long>(2);
                statusList.add(RegistroItemNotaFiscal.STATUS_NORMAL);
                statusList.add(RegistroItemNotaFiscal.STATUS_RECEBIDO);                

                
                List<QuerySorter> sorters = new ArrayList<QuerySorter>();
                sorters.add(new QueryCustomSorter(RegistroItemNotaFiscal.PROP_ITEM));
                
                String[] properties = VOUtils.mergeProperties(
                        new HQLProperties(RegistroItemNotaFiscal.class).getProperties(),
                        new String[]{VOUtils.montarPath(RegistroItemNotaFiscal.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_CODIGO),
                        VOUtils.montarPath(RegistroItemNotaFiscal.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE),
                });

                this.itemNotaFiscalSet = new LinkedHashSet<RegistroItemNotaFiscal>(LoadManager.getInstance(RegistroItemNotaFiscal.class)
                        .setLazyMode(true)
                        .addParameter(new QueryCustomParameter(new HQLConvertKeyToProperties(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_REGISTRO_NOTA_FISCAL), this)))
                        .addParameter(new QueryCustomParameter(RegistroItemNotaFiscal.PROP_STATUS, QueryParameter.IN, statusList))
                        .start().<RegistroItemNotaFiscal>getList()
                        );
                for (RegistroItemNotaFiscal itemNotaFiscal : this.itemNotaFiscalSet) {
                    List<RecebimentoGrupoEstoque> gruposEstoque = LoadManager.getInstance(RecebimentoGrupoEstoque.class)
                                .addProperties(new HQLProperties(RecebimentoGrupoEstoque.class).getProperties())
                                .addProperties(new HQLProperties(LocalizacaoEstrutura.class, RecebimentoGrupoEstoque.PROP_LOCALIZACAO_ESTRUTURA).getProperties())
                                .addParameter(new QueryCustomParameter(VOUtils.montarPath(RecebimentoGrupoEstoque.PROP_REGISTRO_ITEM_NOTA_FISCAL), itemNotaFiscal))
                                .start().getList();

                    itemNotaFiscal.setRegistroNotaFiscal(this);
                    itemNotaFiscal.setRecebimentoGruposEstoque(gruposEstoque);

                    List<RegistroItemNotaFiscalProdutoSolicitado> produtoSolicitado = LoadManager.getInstance(RegistroItemNotaFiscalProdutoSolicitado.class)
                                .addProperties(new HQLProperties(RegistroItemNotaFiscalProdutoSolicitado.class).getProperties())
                                .addProperty(VOUtils.montarPath(RegistroItemNotaFiscalProdutoSolicitado.PROP_ID, RegistroItemNotaFiscalProdutoSolicitadoPK.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME))
                                .addParameter(new QueryCustomParameter(VOUtils.montarPath(RegistroItemNotaFiscalProdutoSolicitado.PROP_ID, RegistroItemNotaFiscalProdutoSolicitadoPK.PROP_REGISTRO_ITEM_NOTA_FISCAL), itemNotaFiscal))
                                .addSorter(new QueryCustomSorter(VOUtils.montarPath(RegistroItemNotaFiscalProdutoSolicitado.PROP_ID, RegistroItemNotaFiscalProdutoSolicitadoPK.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO)))
                                .start().getList();

                    itemNotaFiscal.setPacienteLoteProdutoSolicitado(produtoSolicitado);
                }
                
            }catch ( Exception e ){
                /*
                 * CONTROLE
                 * --------
                 * Controle de carga do VO utilizado para otimiza��o
                 *---------------------------------------------------------------------*/
                this.controle.removeLoaded(PROP_ITEM_NOTA_FISCAL_SET);
                /*--------------------------------------------------------------------*/

                this.getLogger().error( e.getMessage(), e );
                this.itemNotaFiscalSet = new HashSet<RegistroItemNotaFiscal>();
            }
        }else {
            List<RegistroItemNotaFiscal> itemNotaFiscalList = new LinkedList(this.itemNotaFiscalSet);
            
            this.itemNotaFiscalSet = new LinkedHashSet(itemNotaFiscalList);
        }
        
        return itemNotaFiscalSet;
    }
    
    public void setItemNotaFiscalSet(Set<RegistroItemNotaFiscal> itemNotaFiscalSet) {
        this.itemNotaFiscalSet = itemNotaFiscalSet;
    }
    
    public Double getTotalCalculado() {
        return totalCalculado;
    }

    public void setTotalCalculado(Double totalCalculado) {
        this.totalCalculado = totalCalculado;
    }
    
     /**
      * Indica se a nota pode ou nao ser alterada.<br>
      * Verifica o status dos itens. Caso algum item tenha status recebido ou recebido parcial<br>
      *  retorna false, caso contrario retorna true.<br>
      * <br>
      * @return true/false
      */
     public boolean isUpdated() {
         for (RegistroItemNotaFiscal itemNotaFiscal : this.getItemNotaFiscalSet()) {
             if( itemNotaFiscal.getStatus() != null && ( itemNotaFiscal.getStatus().equals( RegistroItemNotaFiscal.STATUS_RECEBIDO_PARCIAL) || itemNotaFiscal.getStatus().equals( RegistroItemNotaFiscal.STATUS_RECEBIDO) ) ) {
                 return false;
             }
         }
         return true;
     }

    /**
     * Calcula o valor total dos produtos:<br>
     * <code>valorTotal - outrasDespesas - valorFrete</code>
     * @return
     */
    public Double getValorTotalProdutos() {
        return Coalesce.asDouble(getValorTotal()) - Coalesce.asDouble(getValorFrete());
//        return Coalesce.asDouble(getValorTotal()) - Coalesce.asDouble(getOutrasDespesas()) - Coalesce.asDouble(getValorFrete());
    }
    
    public String getStatusDescricaoFormatado(){
        String descricao = null;
        if (STATUS_ABERTO.equals(getStatus())) {
            descricao = Bundle.getStringApplication("rotulo_pendente");
        } else if (STATUS_ENCERRADO.equals(getStatus())){
            descricao = Bundle.getStringApplication("rotulo_confirmada");
        }
        return descricao;
    }
}