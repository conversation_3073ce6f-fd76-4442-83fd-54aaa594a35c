<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.integracao"  >
    <class name="IntegracaoAtendimentoProntuario" table="integracao_atendimento_prontuario" >
        <id
            column="cd_integracao_atendimento_prontuario"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />    
        
        <property
            column="cns"
            name="cns"
            not-null="true"
            type="java.lang.Long"
        />
        
        <property
            column="cd_atendimento_prontuario"
            name="codigoProntuario"
            not-null="true"
            type="java.lang.Long"
        />
        
        <many-to-one class="br.com.ksisolucoes.vo.basico.Empresa"
                     name="empresa" not-null="true">
            <column name="cd_empresa" />
        </many-to-one>
    </class>
</hibernate-mapping>
