package br.com.ksisolucoes.vo.portal;

import br.com.ksisolucoes.util.Bundle;
import java.io.Serializable;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.portal.base.BaseUsuarioPortal;



public class UsuarioPortal extends BaseUsuarioPortal implements CodigoManager {
	private static final long serialVersionUID = 1L;

        public enum NivelAcesso{
            NORMAL(0L, Bundle.getStringApplication("rotulo_normal")),
            LIBERADO(1L, Bundle.getStringApplication("rotulo_liberado"));
            
            private Long nivel;
            private String descricao;

            private NivelAcesso(Long nivel, String descricao) {
                this.nivel = nivel;
                this.descricao = descricao;
            }
            
            public Long value(){
                return nivel;
            }
            
            @Override
            public String toString() {
                return descricao;
            }
        }
        
/*[CONSTRUCTOR MARKER BEGIN]*/
	public UsuarioPortal () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public UsuarioPortal (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public UsuarioPortal (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataSolicitacao,
		java.lang.Long nivelAcesso) {

		super (
			codigo,
			usuarioCadsus,
			usuario,
			dataSolicitacao,
			nivelAcesso);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}