package br.com.ksisolucoes.vo.prontuario.basico.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the situacao_dente table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="situacao_dente"
 */

public abstract class BaseSituacaoDente extends BaseRootVO implements Serializable {

	public static String REF = "SituacaoDente";
	public static final String PROP_TIPO_SITUACAO = "tipoSituacao";
	public static final String PROP_FLAG_INATIVAR_DENTE = "flagInativarDente";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_COR = "cor";
	public static final String PROP_OBTURADO_RESTAURADO = "obturadoRestaurado";
	public static final String PROP_REFERENCIA = "referencia";
	public static final String PROP_TIPO_APLICACAO = "tipoAplicacao";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_PERDIDO = "perdido";
	public static final String PROP_CARIADO = "cariado";
	public static final String PROP_TIPO_PREENCHIMENTO = "tipoPreenchimento";
	public static final String PROP_ORDEM = "ordem";


	// constructors
	public BaseSituacaoDente () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseSituacaoDente (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseSituacaoDente (
		java.lang.Long codigo,
		java.lang.String descricao,
		java.lang.String referencia,
		java.lang.Long tipoSituacao) {

		this.setCodigo(codigo);
		this.setDescricao(descricao);
		this.setReferencia(referencia);
		this.setTipoSituacao(tipoSituacao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.lang.String referencia;
	private java.lang.Long ordem;
	private java.lang.Long tipoSituacao;
	private java.lang.Long cariado;
	private java.lang.Long perdido;
	private java.lang.Long obturadoRestaurado;
	private java.lang.String cor;
	private java.lang.Long tipoAplicacao;
	private java.lang.Long tipoPreenchimento;
	private java.lang.Long flagInativarDente;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_situacao"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_situacao
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_situacao
	 * @param descricao the ds_situacao value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: referencia
	 */
	public java.lang.String getReferencia () {
		return getPropertyValue(this, referencia, PROP_REFERENCIA); 
	}

	/**
	 * Set the value related to the column: referencia
	 * @param referencia the referencia value
	 */
	public void setReferencia (java.lang.String referencia) {
//        java.lang.String referenciaOld = this.referencia;
		this.referencia = referencia;
//        this.getPropertyChangeSupport().firePropertyChange ("referencia", referenciaOld, referencia);
	}



	/**
	 * Return the value associated with the column: ordem
	 */
	public java.lang.Long getOrdem () {
		return getPropertyValue(this, ordem, PROP_ORDEM); 
	}

	/**
	 * Set the value related to the column: ordem
	 * @param ordem the ordem value
	 */
	public void setOrdem (java.lang.Long ordem) {
//        java.lang.Long ordemOld = this.ordem;
		this.ordem = ordem;
//        this.getPropertyChangeSupport().firePropertyChange ("ordem", ordemOld, ordem);
	}



	/**
	 * Return the value associated with the column: tp_situacao
	 */
	public java.lang.Long getTipoSituacao () {
		return getPropertyValue(this, tipoSituacao, PROP_TIPO_SITUACAO); 
	}

	/**
	 * Set the value related to the column: tp_situacao
	 * @param tipoSituacao the tp_situacao value
	 */
	public void setTipoSituacao (java.lang.Long tipoSituacao) {
//        java.lang.Long tipoSituacaoOld = this.tipoSituacao;
		this.tipoSituacao = tipoSituacao;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoSituacao", tipoSituacaoOld, tipoSituacao);
	}



	/**
	 * Return the value associated with the column: cariado
	 */
	public java.lang.Long getCariado () {
		return getPropertyValue(this, cariado, PROP_CARIADO); 
	}

	/**
	 * Set the value related to the column: cariado
	 * @param cariado the cariado value
	 */
	public void setCariado (java.lang.Long cariado) {
//        java.lang.Long cariadoOld = this.cariado;
		this.cariado = cariado;
//        this.getPropertyChangeSupport().firePropertyChange ("cariado", cariadoOld, cariado);
	}



	/**
	 * Return the value associated with the column: perdido
	 */
	public java.lang.Long getPerdido () {
		return getPropertyValue(this, perdido, PROP_PERDIDO); 
	}

	/**
	 * Set the value related to the column: perdido
	 * @param perdido the perdido value
	 */
	public void setPerdido (java.lang.Long perdido) {
//        java.lang.Long perdidoOld = this.perdido;
		this.perdido = perdido;
//        this.getPropertyChangeSupport().firePropertyChange ("perdido", perdidoOld, perdido);
	}



	/**
	 * Return the value associated with the column: obturado_restaurado
	 */
	public java.lang.Long getObturadoRestaurado () {
		return getPropertyValue(this, obturadoRestaurado, PROP_OBTURADO_RESTAURADO); 
	}

	/**
	 * Set the value related to the column: obturado_restaurado
	 * @param obturadoRestaurado the obturado_restaurado value
	 */
	public void setObturadoRestaurado (java.lang.Long obturadoRestaurado) {
//        java.lang.Long obturadoRestauradoOld = this.obturadoRestaurado;
		this.obturadoRestaurado = obturadoRestaurado;
//        this.getPropertyChangeSupport().firePropertyChange ("obturadoRestaurado", obturadoRestauradoOld, obturadoRestaurado);
	}



	/**
	 * Return the value associated with the column: cor
	 */
	public java.lang.String getCor () {
		return getPropertyValue(this, cor, PROP_COR); 
	}

	/**
	 * Set the value related to the column: cor
	 * @param cor the cor value
	 */
	public void setCor (java.lang.String cor) {
//        java.lang.String corOld = this.cor;
		this.cor = cor;
//        this.getPropertyChangeSupport().firePropertyChange ("cor", corOld, cor);
	}



	/**
	 * Return the value associated with the column: tp_aplicacao
	 */
	public java.lang.Long getTipoAplicacao () {
		return getPropertyValue(this, tipoAplicacao, PROP_TIPO_APLICACAO); 
	}

	/**
	 * Set the value related to the column: tp_aplicacao
	 * @param tipoAplicacao the tp_aplicacao value
	 */
	public void setTipoAplicacao (java.lang.Long tipoAplicacao) {
//        java.lang.Long tipoAplicacaoOld = this.tipoAplicacao;
		this.tipoAplicacao = tipoAplicacao;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAplicacao", tipoAplicacaoOld, tipoAplicacao);
	}



	/**
	 * Return the value associated with the column: tp_preenchimento
	 */
	public java.lang.Long getTipoPreenchimento () {
		return getPropertyValue(this, tipoPreenchimento, PROP_TIPO_PREENCHIMENTO); 
	}

	/**
	 * Set the value related to the column: tp_preenchimento
	 * @param tipoPreenchimento the tp_preenchimento value
	 */
	public void setTipoPreenchimento (java.lang.Long tipoPreenchimento) {
//        java.lang.Long tipoPreenchimentoOld = this.tipoPreenchimento;
		this.tipoPreenchimento = tipoPreenchimento;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoPreenchimento", tipoPreenchimentoOld, tipoPreenchimento);
	}



	/**
	 * Return the value associated with the column: flag_inativar_dente
	 */
	public java.lang.Long getFlagInativarDente () {
		return getPropertyValue(this, flagInativarDente, PROP_FLAG_INATIVAR_DENTE); 
	}

	/**
	 * Set the value related to the column: flag_inativar_dente
	 * @param flagInativarDente the flag_inativar_dente value
	 */
	public void setFlagInativarDente (java.lang.Long flagInativarDente) {
//        java.lang.Long flagInativarDenteOld = this.flagInativarDente;
		this.flagInativarDente = flagInativarDente;
//        this.getPropertyChangeSupport().firePropertyChange ("flagInativarDente", flagInativarDenteOld, flagInativarDente);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.SituacaoDente)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.SituacaoDente situacaoDente = (br.com.ksisolucoes.vo.prontuario.basico.SituacaoDente) obj;
			if (null == this.getCodigo() || null == situacaoDente.getCodigo()) return false;
			else return (this.getCodigo().equals(situacaoDente.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}