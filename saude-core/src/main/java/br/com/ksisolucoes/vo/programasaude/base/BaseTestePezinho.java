package br.com.ksisolucoes.vo.programasaude.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the teste_pezinho table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="teste_pezinho"
 */

public abstract class BaseTestePezinho extends BaseRootVO implements Serializable {

	public static String REF = "TestePezinho";
	public static final String PROP_NUMERO_REGISTRO_LABORATORIO = "numeroRegistroLaboratorio";
	public static final String PROP_PREMATURO = "prematuro";
	public static final String PROP_RESULTADO_TSH = "resultadoTsh";
	public static final String PROP_RESULTADO_IRT = "resultadoIrt";
	public static final String PROP_RESULTADO_FAL = "resultadoFal";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_PROFISSIONAL = "profissional";
	public static final String PROP_REGISTRO_LOCAL = "registroLocal";
	public static final String PROP_DATA_FECHAMENTO = "dataFechamento";
	public static final String PROP_DESCRICAO_OBSERVACAO = "descricaoObservacao";
	public static final String PROP_GEMELAR = "gemelar";
	public static final String PROP_NUMERO_COLETA = "numeroColeta";
	public static final String PROP_USUARIO_CADSUS_MAE = "usuarioCadsusMae";
	public static final String PROP_DATA_ATENDIMENTO = "dataAtendimento";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_STATUS = "status";
	public static final String PROP_PESO_NASCIMENTO = "pesoNascimento";
	public static final String PROP_LOTE = "lote";
	public static final String PROP_TRANSFUSAO = "transfusao";
	public static final String PROP_GEMELAR_QUANTIDADE = "gemelarQuantidade";
	public static final String PROP_ID = "id";
	public static final String PROP_MAMOU = "mamou";
	public static final String PROP_DATA_TRANSFUSAO = "dataTransfusao";
	public static final String PROP_RESULTADO17OHP = "resultado17ohp";
	public static final String PROP_USUARIO_FECHAMENTO = "usuarioFechamento";


	// constructors
	public BaseTestePezinho () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseTestePezinho (br.com.ksisolucoes.vo.programasaude.TestePezinhoPK id) {
		this.setId(id);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseTestePezinho (
		br.com.ksisolucoes.vo.programasaude.TestePezinhoPK id,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusMae,
		java.util.Date dataAtendimento,
		java.util.Date dataFechamento) {

		this.setId(id);
		this.setUsuarioCadsus(usuarioCadsus);
		this.setUsuarioCadsusMae(usuarioCadsusMae);
		this.setDataAtendimento(dataAtendimento);
		this.setDataFechamento(dataFechamento);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private br.com.ksisolucoes.vo.programasaude.TestePezinhoPK id;

	// fields
	private java.lang.String registroLocal;
	private java.lang.String lote;
	private java.lang.Double pesoNascimento;
	private java.lang.String mamou;
	private java.lang.String transfusao;
	private java.util.Date dataTransfusao;
	private java.lang.String prematuro;
	private java.lang.String gemelar;
	private java.lang.Long gemelarQuantidade;
	private java.lang.Long status;
	private java.util.Date dataAtendimento;
	private java.lang.String descricaoObservacao;
	private java.util.Date dataFechamento;
	private java.lang.Long numeroColeta;
	private java.lang.String resultadoFal;
	private java.lang.String resultadoTsh;
	private java.lang.String resultadoIrt;
	private java.lang.String resultado17ohp;
	private java.lang.String numeroRegistroLaboratorio;

	// many to one
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusMae;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissional;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioFechamento;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     */
	public br.com.ksisolucoes.vo.programasaude.TestePezinhoPK getId () {
	    return getPropertyValue(this,  id, "id" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param id the new ID
	 */
	public void setId (br.com.ksisolucoes.vo.programasaude.TestePezinhoPK id) {
		this.id = id;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: registro_local
	 */
	public java.lang.String getRegistroLocal () {
		return getPropertyValue(this, registroLocal, PROP_REGISTRO_LOCAL); 
	}

	/**
	 * Set the value related to the column: registro_local
	 * @param registroLocal the registro_local value
	 */
	public void setRegistroLocal (java.lang.String registroLocal) {
//        java.lang.String registroLocalOld = this.registroLocal;
		this.registroLocal = registroLocal;
//        this.getPropertyChangeSupport().firePropertyChange ("registroLocal", registroLocalOld, registroLocal);
	}



	/**
	 * Return the value associated with the column: lote
	 */
	public java.lang.String getLote () {
		return getPropertyValue(this, lote, PROP_LOTE); 
	}

	/**
	 * Set the value related to the column: lote
	 * @param lote the lote value
	 */
	public void setLote (java.lang.String lote) {
//        java.lang.String loteOld = this.lote;
		this.lote = lote;
//        this.getPropertyChangeSupport().firePropertyChange ("lote", loteOld, lote);
	}



	/**
	 * Return the value associated with the column: peso_nascer
	 */
	public java.lang.Double getPesoNascimento () {
		return getPropertyValue(this, pesoNascimento, PROP_PESO_NASCIMENTO); 
	}

	/**
	 * Set the value related to the column: peso_nascer
	 * @param pesoNascimento the peso_nascer value
	 */
	public void setPesoNascimento (java.lang.Double pesoNascimento) {
//        java.lang.Double pesoNascimentoOld = this.pesoNascimento;
		this.pesoNascimento = pesoNascimento;
//        this.getPropertyChangeSupport().firePropertyChange ("pesoNascimento", pesoNascimentoOld, pesoNascimento);
	}



	/**
	 * Return the value associated with the column: mamou
	 */
	public java.lang.String getMamou () {
		return getPropertyValue(this, mamou, PROP_MAMOU); 
	}

	/**
	 * Set the value related to the column: mamou
	 * @param mamou the mamou value
	 */
	public void setMamou (java.lang.String mamou) {
//        java.lang.String mamouOld = this.mamou;
		this.mamou = mamou;
//        this.getPropertyChangeSupport().firePropertyChange ("mamou", mamouOld, mamou);
	}



	/**
	 * Return the value associated with the column: transfusao
	 */
	public java.lang.String getTransfusao () {
		return getPropertyValue(this, transfusao, PROP_TRANSFUSAO); 
	}

	/**
	 * Set the value related to the column: transfusao
	 * @param transfusao the transfusao value
	 */
	public void setTransfusao (java.lang.String transfusao) {
//        java.lang.String transfusaoOld = this.transfusao;
		this.transfusao = transfusao;
//        this.getPropertyChangeSupport().firePropertyChange ("transfusao", transfusaoOld, transfusao);
	}



	/**
	 * Return the value associated with the column: dt_transfusao
	 */
	public java.util.Date getDataTransfusao () {
		return getPropertyValue(this, dataTransfusao, PROP_DATA_TRANSFUSAO); 
	}

	/**
	 * Set the value related to the column: dt_transfusao
	 * @param dataTransfusao the dt_transfusao value
	 */
	public void setDataTransfusao (java.util.Date dataTransfusao) {
//        java.util.Date dataTransfusaoOld = this.dataTransfusao;
		this.dataTransfusao = dataTransfusao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataTransfusao", dataTransfusaoOld, dataTransfusao);
	}



	/**
	 * Return the value associated with the column: prematuro
	 */
	public java.lang.String getPrematuro () {
		return getPropertyValue(this, prematuro, PROP_PREMATURO); 
	}

	/**
	 * Set the value related to the column: prematuro
	 * @param prematuro the prematuro value
	 */
	public void setPrematuro (java.lang.String prematuro) {
//        java.lang.String prematuroOld = this.prematuro;
		this.prematuro = prematuro;
//        this.getPropertyChangeSupport().firePropertyChange ("prematuro", prematuroOld, prematuro);
	}



	/**
	 * Return the value associated with the column: gemelar
	 */
	public java.lang.String getGemelar () {
		return getPropertyValue(this, gemelar, PROP_GEMELAR); 
	}

	/**
	 * Set the value related to the column: gemelar
	 * @param gemelar the gemelar value
	 */
	public void setGemelar (java.lang.String gemelar) {
//        java.lang.String gemelarOld = this.gemelar;
		this.gemelar = gemelar;
//        this.getPropertyChangeSupport().firePropertyChange ("gemelar", gemelarOld, gemelar);
	}



	/**
	 * Return the value associated with the column: gemelar_qtdade
	 */
	public java.lang.Long getGemelarQuantidade () {
		return getPropertyValue(this, gemelarQuantidade, PROP_GEMELAR_QUANTIDADE); 
	}

	/**
	 * Set the value related to the column: gemelar_qtdade
	 * @param gemelarQuantidade the gemelar_qtdade value
	 */
	public void setGemelarQuantidade (java.lang.Long gemelarQuantidade) {
//        java.lang.Long gemelarQuantidadeOld = this.gemelarQuantidade;
		this.gemelarQuantidade = gemelarQuantidade;
//        this.getPropertyChangeSupport().firePropertyChange ("gemelarQuantidade", gemelarQuantidadeOld, gemelarQuantidade);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: dt_atendimento
	 */
	public java.util.Date getDataAtendimento () {
		return getPropertyValue(this, dataAtendimento, PROP_DATA_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: dt_atendimento
	 * @param dataAtendimento the dt_atendimento value
	 */
	public void setDataAtendimento (java.util.Date dataAtendimento) {
//        java.util.Date dataAtendimentoOld = this.dataAtendimento;
		this.dataAtendimento = dataAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAtendimento", dataAtendimentoOld, dataAtendimento);
	}



	/**
	 * Return the value associated with the column: ds_observacao
	 */
	public java.lang.String getDescricaoObservacao () {
		return getPropertyValue(this, descricaoObservacao, PROP_DESCRICAO_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: ds_observacao
	 * @param descricaoObservacao the ds_observacao value
	 */
	public void setDescricaoObservacao (java.lang.String descricaoObservacao) {
//        java.lang.String descricaoObservacaoOld = this.descricaoObservacao;
		this.descricaoObservacao = descricaoObservacao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoObservacao", descricaoObservacaoOld, descricaoObservacao);
	}



	/**
	 * Return the value associated with the column: dt_fechamento
	 */
	public java.util.Date getDataFechamento () {
		return getPropertyValue(this, dataFechamento, PROP_DATA_FECHAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_fechamento
	 * @param dataFechamento the dt_fechamento value
	 */
	public void setDataFechamento (java.util.Date dataFechamento) {
//        java.util.Date dataFechamentoOld = this.dataFechamento;
		this.dataFechamento = dataFechamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataFechamento", dataFechamentoOld, dataFechamento);
	}



	/**
	 * Return the value associated with the column: nr_coleta
	 */
	public java.lang.Long getNumeroColeta () {
		return getPropertyValue(this, numeroColeta, PROP_NUMERO_COLETA); 
	}

	/**
	 * Set the value related to the column: nr_coleta
	 * @param numeroColeta the nr_coleta value
	 */
	public void setNumeroColeta (java.lang.Long numeroColeta) {
//        java.lang.Long numeroColetaOld = this.numeroColeta;
		this.numeroColeta = numeroColeta;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroColeta", numeroColetaOld, numeroColeta);
	}



	/**
	 * Return the value associated with the column: resultado_fal
	 */
	public java.lang.String getResultadoFal () {
		return getPropertyValue(this, resultadoFal, PROP_RESULTADO_FAL); 
	}

	/**
	 * Set the value related to the column: resultado_fal
	 * @param resultadoFal the resultado_fal value
	 */
	public void setResultadoFal (java.lang.String resultadoFal) {
//        java.lang.String resultadoFalOld = this.resultadoFal;
		this.resultadoFal = resultadoFal;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoFal", resultadoFalOld, resultadoFal);
	}



	/**
	 * Return the value associated with the column: resultado_tsh
	 */
	public java.lang.String getResultadoTsh () {
		return getPropertyValue(this, resultadoTsh, PROP_RESULTADO_TSH); 
	}

	/**
	 * Set the value related to the column: resultado_tsh
	 * @param resultadoTsh the resultado_tsh value
	 */
	public void setResultadoTsh (java.lang.String resultadoTsh) {
//        java.lang.String resultadoTshOld = this.resultadoTsh;
		this.resultadoTsh = resultadoTsh;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoTsh", resultadoTshOld, resultadoTsh);
	}



	/**
	 * Return the value associated with the column: resultado_irt
	 */
	public java.lang.String getResultadoIrt () {
		return getPropertyValue(this, resultadoIrt, PROP_RESULTADO_IRT); 
	}

	/**
	 * Set the value related to the column: resultado_irt
	 * @param resultadoIrt the resultado_irt value
	 */
	public void setResultadoIrt (java.lang.String resultadoIrt) {
//        java.lang.String resultadoIrtOld = this.resultadoIrt;
		this.resultadoIrt = resultadoIrt;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoIrt", resultadoIrtOld, resultadoIrt);
	}



	/**
	 * Return the value associated with the column: resultado_17ohp
	 */
	public java.lang.String getResultado17ohp () {
		return getPropertyValue(this, resultado17ohp, PROP_RESULTADO17OHP); 
	}

	/**
	 * Set the value related to the column: resultado_17ohp
	 * @param resultado17ohp the resultado_17ohp value
	 */
	public void setResultado17ohp (java.lang.String resultado17ohp) {
//        java.lang.String resultado17ohpOld = this.resultado17ohp;
		this.resultado17ohp = resultado17ohp;
//        this.getPropertyChangeSupport().firePropertyChange ("resultado17ohp", resultado17ohpOld, resultado17ohp);
	}



	/**
	 * Return the value associated with the column: nr_registro_lab
	 */
	public java.lang.String getNumeroRegistroLaboratorio () {
		return getPropertyValue(this, numeroRegistroLaboratorio, PROP_NUMERO_REGISTRO_LABORATORIO); 
	}

	/**
	 * Set the value related to the column: nr_registro_lab
	 * @param numeroRegistroLaboratorio the nr_registro_lab value
	 */
	public void setNumeroRegistroLaboratorio (java.lang.String numeroRegistroLaboratorio) {
//        java.lang.String numeroRegistroLaboratorioOld = this.numeroRegistroLaboratorio;
		this.numeroRegistroLaboratorio = numeroRegistroLaboratorio;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroRegistroLaboratorio", numeroRegistroLaboratorioOld, numeroRegistroLaboratorio);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus_mae
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsusMae () {
		return getPropertyValue(this, usuarioCadsusMae, PROP_USUARIO_CADSUS_MAE); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus_mae
	 * @param usuarioCadsusMae the cd_usu_cadsus_mae value
	 */
	public void setUsuarioCadsusMae (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusMae) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusMaeOld = this.usuarioCadsusMae;
		this.usuarioCadsusMae = usuarioCadsusMae;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsusMae", usuarioCadsusMaeOld, usuarioCadsusMae);
	}



	/**
	 * Return the value associated with the column: cd_profissional
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissional () {
		return getPropertyValue(this, profissional, PROP_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_profissional
	 * @param profissional the cd_profissional value
	 */
	public void setProfissional (br.com.ksisolucoes.vo.cadsus.Profissional profissional) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalOld = this.profissional;
		this.profissional = profissional;
//        this.getPropertyChangeSupport().firePropertyChange ("profissional", profissionalOld, profissional);
	}



	/**
	 * Return the value associated with the column: cd_usu_fechamento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioFechamento () {
		return getPropertyValue(this, usuarioFechamento, PROP_USUARIO_FECHAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usu_fechamento
	 * @param usuarioFechamento the cd_usu_fechamento value
	 */
	public void setUsuarioFechamento (br.com.ksisolucoes.vo.controle.Usuario usuarioFechamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioFechamentoOld = this.usuarioFechamento;
		this.usuarioFechamento = usuarioFechamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioFechamento", usuarioFechamentoOld, usuarioFechamento);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.programasaude.TestePezinho)) return false;
		else {
			br.com.ksisolucoes.vo.programasaude.TestePezinho testePezinho = (br.com.ksisolucoes.vo.programasaude.TestePezinho) obj;
			if (null == this.getId() || null == testePezinho.getId()) return false;
			else return (this.getId().equals(testePezinho.getId()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getId()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getId().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}