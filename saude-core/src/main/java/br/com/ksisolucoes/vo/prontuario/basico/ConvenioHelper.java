package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

/**
 *
 * <AUTHOR>
 */
public class ConvenioHelper implements Serializable {

    public static void validaNumeroConvenioIpe(String numeroConvenio) throws ValidacaoException {

        if (numeroConvenio == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_numero_convenio"));
        }

        numeroConvenio = numeroConvenio.trim();

        if (!numeroConvenio.matches("[0-9]*")) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_numero_convenio_invalido_padrao_ipe"));
        }

        if (numeroConvenio.length() != 13) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_numero_convenio_tamanho_nao_permitido"));
        }

        String num = numeroConvenio.substring(0, 2);

        if (num.equals("63")) {
            if (!isOkDigitoVerificadorConvenioModulo10(numeroConvenio)) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_numero_convenio_invalido_padrao_ipe"));
            }
        } else if (num.equals("19") || num.equals("20") || num.equals("41") || num.equals("73")) {
            if (!isOkDigitoVerificadorConvenioModulo11(numeroConvenio)) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_numero_convenio_invalido_padrao_ipe"));
            }
        }

        if (!isOkDigitoVerificadorConvenioModulo10Geral(numeroConvenio)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_numero_convenio_invalido_padrao_ipe"));
        }
    }

    private static boolean isOkDigitoVerificadorConvenioModulo10Geral(String numero) {
        String digito = numero.substring(12, 13);
        String num = numero.substring(0, 12);

        if (digito.equals(String.valueOf(getDigitoVerificadorModulo10(num)))) {
            return true;
        }

        return false;
    }

    private static boolean isOkDigitoVerificadorConvenioModulo10(String numero) {
        String digito = numero.substring(9, 10);
        String num = numero.substring(2, 9);

        if (digito.equals(String.valueOf(getDigitoVerificadorModulo10(num)))) {
            return true;
        }

        return false;
    }

    private static boolean isOkDigitoVerificadorConvenioModulo11(String numero) {

        String digito = numero.substring(9, 10);
        String num = numero.substring(2, 9);

        if (digito.equals(String.valueOf(getDigitoVerificadorModulo11(num)))) {
            return true;
        }

        return false;
    }

    public static int getDigitoVerificadorModulo10(String num) {

        int soma = 0;
        int resto = 0;
        int dv = 0;
        String[] numeros = new String[num.length() + 1];
        int multiplicador = 2;
        String aux;
        String aux2;
        String aux3;

        for (int i = num.length(); i > 0; i--) {
            //Multiplica da direita pra esquerda, alternando os algarismos 2 e 1  
            if (multiplicador % 2 == 0) {
                // pega cada numero isoladamente    
                numeros[i] = String.valueOf(Integer.valueOf(num.substring(i - 1, i)) * 2);
                multiplicador = 1;
            } else {
                numeros[i] = String.valueOf(Integer.valueOf(num.substring(i - 1, i)) * 1);
                multiplicador = 2;
            }
        }

        // Realiza a soma dos campos de acordo com a regra  
        for (int i = (numeros.length - 1); i > 0; i--) {
            aux = String.valueOf(Integer.valueOf(numeros[i]));

            if (aux.length() > 1) {
                aux2 = aux.substring(0, aux.length() - 1);
                aux3 = aux.substring(aux.length() - 1, aux.length());
                numeros[i] = String.valueOf(Integer.valueOf(aux2) + Integer.valueOf(aux3));
            } else {
                numeros[i] = aux;
            }
        }

        //Realiza a soma de todos os elementos do array e calcula o digito verificador  
        //na base 10 de acordo com a regra.       
        for (int i = numeros.length; i > 0; i--) {
            if (numeros[i - 1] != null) {
                soma += Integer.valueOf(numeros[i - 1]);
            }
        }
        resto = soma % 10;
        dv = 10 - resto;

        //retorna o digito verificador  
        return dv;
    }

    public static int getDigitoVerificadorModulo11(String num) {

        //variáveis de instancia  
        int soma = 0;
        int resto = 0;
        int dv = 0;
        String[] numeros = new String[num.length() + 1];
        int multiplicador = 2;

        for (int i = num.length(); i > 0; i--) {
            //Multiplica da direita pra esquerda, incrementando o multiplicador de 2 a 9  
            //Caso o multiplicador seja maior que 9 o mesmo recomeça em 2  
            if (multiplicador > 9) {
                // pega cada numero isoladamente    
                multiplicador = 2;
                numeros[i] = String.valueOf(Integer.valueOf(num.substring(i - 1, i)) * multiplicador);
                multiplicador++;
            } else {
                numeros[i] = String.valueOf(Integer.valueOf(num.substring(i - 1, i)) * multiplicador);
                multiplicador++;
            }
        }

        //Realiza a soma de todos os elementos do array e calcula o digito verificador  
        //na base 11 de acordo com a regra.       
        for (int i = numeros.length; i > 0; i--) {
            if (numeros[i - 1] != null) {
                soma += Integer.valueOf(numeros[i - 1]);
            }
        }
        resto = soma % 11;
        dv = 11 - resto;

        //retorna o digito verificador  
        return dv;
    }
}
