<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.cadsus"  >
    <class name="NotificacaoAgendas" table="notificacao_agendas" >
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_not_ag"
        >
            <generator class="assigned"/>
        </id> 
        <version column="version" name="version" type="long" />
 
        <many-to-one
            class="br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario"
            column="cd_ag_gra_ate_hor"
            name="agendaGradeAtendimentoHorario"
            not-null="true"
        />

        <property
            column="status"
            name="status"
            not-null="true"
            type="java.lang.Integer"
        />

        <property
            column="version_all"
            name="versionAll"
            type="java.lang.Long"
        />

    </class>
</hibernate-mapping>
