<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="GrupoProblemasCondicoes" table="grupo_problemas_condicoes" >

        <id
            column="cd_grupo_condicoes"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id>

        <version column="version" name="version" type="long" />

        <many-to-one
                name="ciap"
                class="br.com.ksisolucoes.vo.prontuario.basico.Ciap"
        >
            <column name="cd_ciap"/>
        </many-to-one>

        <many-to-one
                name="cid"
                class="br.com.ksisolucoes.vo.prontuario.basico.Cid"
        >
            <column name="cd_cid"/>
        </many-to-one>

        <many-to-one
                name="usuarioCadSus"
                class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"
        >
            <column name="cd_usu_cadsus"/>
        </many-to-one>

        <many-to-one
                name="atendimento"
                class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
        >
            <column name="nr_atendimento"/>
        </many-to-one>

        <many-to-one
                name="cipe"
                class="br.com.ksisolucoes.vo.sae.diagnosticoenfermagemsae.DiagnosticoEnfermagemSae"
        >
            <column name="cd_diagnostico"/>
        </many-to-one>

        <property
            column="dt_inicial"
            name="dataInicial"
            type="date"
        />
        <property
            column="dt_final"
            name="dataFinal"
            type="date"
        />
        <property
            column="situacao"
            name="situacao"
            not-null="true"
            type="java.lang.Long"
        />
        
        <property 
         	name="descricao"
         	column="descricao"
         	type="java.lang.String"
         	not-null="false"
         />
    </class>
</hibernate-mapping>
