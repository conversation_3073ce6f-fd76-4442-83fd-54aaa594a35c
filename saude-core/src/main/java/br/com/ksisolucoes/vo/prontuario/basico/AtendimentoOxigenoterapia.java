package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import java.io.Serializable;

import br.com.ksisolucoes.vo.prontuario.basico.base.BaseAtendimentoOxigenoterapia;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

public class AtendimentoOxigenoterapia extends BaseAtendimentoOxigenoterapia implements CodigoManager {

    private static final long serialVersionUID = 1L;

    public static enum TipoOxigenoterapia implements IEnum {

        CAMPANULA(1L, Bundle.getStringApplication("rotulo_campanula")),
        CATETER(2L, Bundle.getStringApplication("rotulo_cateter")),
        CPAP(3L, Bundle.getStringApplication("rotulo_cpap")),
        MASCARA(4L, Bundle.getStringApplication("rotulo_mascara")),
        RESPIRADOR(5L, Bundle.getStringApplication("rotulo_respirador")),
        VAPOR_JET(6L, Bundle.getStringApplication("rotulo_vapor_jet")),
        ;

        private Long value;
        private String descricao;

        private TipoOxigenoterapia(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return this.descricao;
        }

        public static TipoOxigenoterapia valueOf(Long value) {
            if (value != null) {
                for (TipoOxigenoterapia tipoOxigenoterapia : TipoOxigenoterapia.values()) {
                    if (tipoOxigenoterapia.value().equals(value)) {
                        return tipoOxigenoterapia;
                    }
                }
            }
            return null;
        }
    }

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public AtendimentoOxigenoterapia() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public AtendimentoOxigenoterapia(java.lang.Long codigo) {
        super(codigo);
    }

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getDescricaoTipoOxigenoterapia() {
        TipoOxigenoterapia tipoOxigenoterapia = TipoOxigenoterapia.valueOf(getTipoOxigenoterapia());

        if (tipoOxigenoterapia != null) {
            return tipoOxigenoterapia.descricao();
        }

        return null;
    }
}
