<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.procedimento"  >
	<class name="ProcedimentoRegistroCadastro" table="procedimento_registro_cad" >
		<id
			name="codigo"
			type="java.lang.Long"
			column="cd_registro"
		>
			<generator class="assigned" />
		</id> <version column="version" name="version" type="long" />

		<property
			column="nm_registro"
			length="50"
			name="descricao"
			not-null="true"
			type="string"
		 />

		<property
			column="dt_competencia"
			name="dataCompetencia"
			not-null="true"
			type="java.util.Date"
		 />

	</class>
</hibernate-mapping>
