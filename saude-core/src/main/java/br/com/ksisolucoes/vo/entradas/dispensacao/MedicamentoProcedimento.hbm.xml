<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.entradas.dispensacao"  >
    <class name="MedicamentoProcedimento" table="medicamento_procedimento">
        
        <id
            name="codigo"
            type="java.lang.Long" 
            column="cd_medicamento_procedimento"
        > 
            <generator class="assigned"/>
        </id> 
        
        <version column="version" name="version" type="long" />
        
        <many-to-one
            name="produto"
            class="br.com.ksisolucoes.vo.entradas.estoque.Produto"
            column="cod_pro"
        />
        
        <many-to-one
            name="procedimento"
            class="br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento"
            column="cd_procedimento"
        />
        
    </class>
</hibernate-mapping>
