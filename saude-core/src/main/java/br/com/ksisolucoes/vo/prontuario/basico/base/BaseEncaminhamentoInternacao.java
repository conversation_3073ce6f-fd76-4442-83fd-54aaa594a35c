package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the encaminhamento_internacao table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="encaminhamento_internacao"
 */

public abstract class BaseEncaminhamentoInternacao extends BaseRootVO implements Serializable {

	public static String REF = "EncaminhamentoInternacao";
	public static final String PROP_STATUS = "status";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_ATENDIMENTO_INFORMACAO = "atendimentoInformacao";
	public static final String PROP_DATA_ENCAMINHAMENTO = "dataEncaminhamento";
	public static final String PROP_ATENDIMENTO = "atendimento";


	// constructors
	public BaseEncaminhamentoInternacao () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEncaminhamentoInternacao (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseEncaminhamentoInternacao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento,
		br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao atendimentoInformacao,
		java.util.Date dataEncaminhamento,
		java.lang.Long status) {

		this.setCodigo(codigo);
		this.setAtendimento(atendimento);
		this.setAtendimentoInformacao(atendimentoInformacao);
		this.setDataEncaminhamento(dataEncaminhamento);
		this.setStatus(status);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataEncaminhamento;
	private java.lang.Long status;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento;
	private br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao atendimentoInformacao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_enc_internacao"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_encaminhamento
	 */
	public java.util.Date getDataEncaminhamento () {
		return getPropertyValue(this, dataEncaminhamento, PROP_DATA_ENCAMINHAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_encaminhamento
	 * @param dataEncaminhamento the dt_encaminhamento value
	 */
	public void setDataEncaminhamento (java.util.Date dataEncaminhamento) {
//        java.util.Date dataEncaminhamentoOld = this.dataEncaminhamento;
		this.dataEncaminhamento = dataEncaminhamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEncaminhamento", dataEncaminhamentoOld, dataEncaminhamento);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: nr_atendimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Atendimento getAtendimento () {
		return getPropertyValue(this, atendimento, PROP_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: nr_atendimento
	 * @param atendimento the nr_atendimento value
	 */
	public void setAtendimento (br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoOld = this.atendimento;
		this.atendimento = atendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimento", atendimentoOld, atendimento);
	}



	/**
	 * Return the value associated with the column: cd_atend_inf
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao getAtendimentoInformacao () {
		return getPropertyValue(this, atendimentoInformacao, PROP_ATENDIMENTO_INFORMACAO); 
	}

	/**
	 * Set the value related to the column: cd_atend_inf
	 * @param atendimentoInformacao the cd_atend_inf value
	 */
	public void setAtendimentoInformacao (br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao atendimentoInformacao) {
//        br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao atendimentoInformacaoOld = this.atendimentoInformacao;
		this.atendimentoInformacao = atendimentoInformacao;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimentoInformacao", atendimentoInformacaoOld, atendimentoInformacao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.EncaminhamentoInternacao)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.EncaminhamentoInternacao encaminhamentoInternacao = (br.com.ksisolucoes.vo.prontuario.basico.EncaminhamentoInternacao) obj;
			if (null == this.getCodigo() || null == encaminhamentoInternacao.getCodigo()) return false;
			else return (this.getCodigo().equals(encaminhamentoInternacao.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}