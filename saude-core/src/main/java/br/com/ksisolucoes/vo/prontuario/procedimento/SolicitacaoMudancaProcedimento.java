package br.com.ksisolucoes.vo.prontuario.procedimento;

import java.io.Serializable;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import br.com.ksisolucoes.vo.prontuario.procedimento.base.BaseSolicitacaoMudancaProcedimento;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class SolicitacaoMudancaProcedimento extends BaseSolicitacaoMudancaProcedimento implements CodigoManager {
	private static final long serialVersionUID = 1L;

	public static final String PROP_STATUS_FORMATADO = "statusFormatado";
	public static final String PROP_DATA_HORA_ANALISE = "dataHoraAnalise";

	/*[CONSTRUCTOR MARKER BEGIN]*/
	public SolicitacaoMudancaProcedimento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public SolicitacaoMudancaProcedimento (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public SolicitacaoMudancaProcedimento (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento novoProcedimento,
		br.com.ksisolucoes.vo.cadsus.Profissional profissionalSolicitante,
		br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoOrigem,
		br.com.ksisolucoes.vo.prontuario.hospital.Aih autorizacaoInternacaoHospitalar,
		br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoAnterior,
		java.lang.String justificativa,
		java.util.Date dataSolicitacao) {

		super (
			codigo,
			novoProcedimento,
			profissionalSolicitante,
			atendimentoOrigem,
			autorizacaoInternacaoHospitalar,
			procedimentoAnterior,
			justificativa,
			dataSolicitacao);
	}

/*[CONSTRUCTOR MARKER END]*/

	public enum Status implements IEnum {
		AGUARDANDO_ANALISE(0L, Bundle.getStringApplication("rotulo_aguardando_analise")),
		AUTORIZADA(1L, Bundle.getStringApplication("rotulo_autorizada")),
		NEGADA(2L, Bundle.getStringApplication("rotulo_negada")),
		;

		private Long value;
		private String descricao;

		private Status(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static SolicitacaoMudancaProcedimento.Status valueOf(Long value) {
			for (SolicitacaoMudancaProcedimento.Status status : SolicitacaoMudancaProcedimento.Status.values()) {
				if (status.value().equals(value)) {
					return status;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}

	}

	public String getStatusFormatado() {
		SolicitacaoMudancaProcedimento.Status status = SolicitacaoMudancaProcedimento.Status.valueOf(getStatus());
		if (status != null && status.descricao() != null) {
			return status.descricao();
		}
		return "";
	}

	public String getDataHoraAnalise() {
		if (getDataAnalise() != null) {
			return Bundle.getStringApplication("format_day_hour_minute_sem_z", getDataAnalise());
		}
		return "";
	}

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}