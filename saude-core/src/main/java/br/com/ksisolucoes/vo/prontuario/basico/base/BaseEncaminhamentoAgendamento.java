package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the encaminhamento_agendamento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="encaminhamento_agendamento"
 */

public abstract class BaseEncaminhamentoAgendamento extends BaseRootVO implements Serializable {

	public static String REF = "EncaminhamentoAgendamento";
	public static final String PROP_STATUS = "status";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_AGENDAMENTO = "dataAgendamento";
	public static final String PROP_PROFISSIONAL = "profissional";
	public static final String PROP_LOCAL_AGENDAMENTO = "localAgendamento";
	public static final String PROP_ENCAMINHAMENTO = "encaminhamento";


	// constructors
	public BaseEncaminhamentoAgendamento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEncaminhamentoAgendamento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseEncaminhamentoAgendamento (
		java.lang.Long codigo,
		java.util.Date dataAgendamento,
		java.lang.Long status) {

		this.setCodigo(codigo);
		this.setDataAgendamento(dataAgendamento);
		this.setStatus(status);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataAgendamento;
	private java.lang.Long status;

	// many to one
	private br.com.ksisolucoes.vo.basico.Empresa localAgendamento;
	private br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento encaminhamento;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissional;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_enc_agendamento"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_agendamento
	 */
	public java.util.Date getDataAgendamento () {
		return getPropertyValue(this, dataAgendamento, PROP_DATA_AGENDAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_agendamento
	 * @param dataAgendamento the dt_agendamento value
	 */
	public void setDataAgendamento (java.util.Date dataAgendamento) {
//        java.util.Date dataAgendamentoOld = this.dataAgendamento;
		this.dataAgendamento = dataAgendamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAgendamento", dataAgendamentoOld, dataAgendamento);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}




	/**
	 * Return the value associated with the column: local_agendamento
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getLocalAgendamento () {
		return getPropertyValue(this, localAgendamento, PROP_LOCAL_AGENDAMENTO); 
	}

	/**
	 * Set the value related to the column: local_agendamento
	 * @param localAgendamento the local_agendamento value
	 */
	public void setLocalAgendamento (br.com.ksisolucoes.vo.basico.Empresa localAgendamento) {
//        br.com.ksisolucoes.vo.basico.Empresa localAgendamentoOld = this.localAgendamento;
		this.localAgendamento = localAgendamento;
//        this.getPropertyChangeSupport().firePropertyChange ("localAgendamento", localAgendamentoOld, localAgendamento);
	}



	/**
	 * Return the value associated with the column: cd_encaminhamento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento getEncaminhamento () {
		return getPropertyValue(this, encaminhamento, PROP_ENCAMINHAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_encaminhamento
	 * @param encaminhamento the cd_encaminhamento value
	 */
	public void setEncaminhamento (br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento encaminhamento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento encaminhamentoOld = this.encaminhamento;
		this.encaminhamento = encaminhamento;
//        this.getPropertyChangeSupport().firePropertyChange ("encaminhamento", encaminhamentoOld, encaminhamento);
	}



	/**
	 * Return the value associated with the column: cd_profissional
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissional () {
		return getPropertyValue(this, profissional, PROP_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_profissional
	 * @param profissional the cd_profissional value
	 */
	public void setProfissional (br.com.ksisolucoes.vo.cadsus.Profissional profissional) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalOld = this.profissional;
		this.profissional = profissional;
//        this.getPropertyChangeSupport().firePropertyChange ("profissional", profissionalOld, profissional);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.EncaminhamentoAgendamento)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.EncaminhamentoAgendamento encaminhamentoAgendamento = (br.com.ksisolucoes.vo.prontuario.basico.EncaminhamentoAgendamento) obj;
			if (null == this.getCodigo() || null == encaminhamentoAgendamento.getCodigo()) return false;
			else return (this.getCodigo().equals(encaminhamentoAgendamento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}