package br.com.ksisolucoes.vo.samu;

import java.io.Serializable;

import br.com.ksisolucoes.vo.samu.base.BaseSamuChecklistItem;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class SamuChecklistItem extends BaseSamuChecklistItem implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public SamuChecklistItem () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public SamuChecklistItem (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public SamuChecklistItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.samu.SamuChecklist samuChecklist,
		br.com.ksisolucoes.vo.samu.SamuChecagem samuChecagem,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataCadastro,
		java.util.Date dataUsuario) {

		super (
			codigo,
			samuChecklist,
			samuChecagem,
			usuario,
			dataCadastro,
			dataUsuario);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}