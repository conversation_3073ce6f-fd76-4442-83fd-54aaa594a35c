<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico">
    <class name="TesteRapidoConjunto" table="teste_rapido_conjunto">
		
        <id
            column="cd_tr_conjunto"
            name="codigo"
            type="java.lang.Long"
        >
        <generator class="assigned" />
        </id> <version column="version" name="version" type="long" />
        
        <many-to-one 
            column="cd_tp_teste"
            name="testeRapidoTipo"
            class="br.com.ksisolucoes.vo.prontuario.basico.TipoTesteRapido"
            not-null="true"
         />
        
        <property 
            column="nm_conjunto"
            name="nomeConjunto"
            type="java.lang.String"
            not-null="true"
            length="100"
         />
        
        <property 
            column="fabricante"
            name="fabricante"
            type="java.lang.String"
            not-null="true"
            length="100"
         />
        
        <property 
            column="metodo"
            name="metodo"
            type="java.lang.String"
            not-null="true"
            length="50"
         />

        <many-to-one
                name="usuario"
                class="br.com.ksisolucoes.vo.controle.Usuario"
                not-null="false"
        >
            <column name="cd_usuario"/>
        </many-to-one>

        <property
                column="dt_cadastro"
                name="dataCadastro"
                type="timestamp"
                not-null="false"
        />

        <property
                column="dt_usuario"
                name="dataUsuario"
                type="java.util.Date"
                not-null="false"
        />

        <property
                name="status"
                column="status"
                type="java.lang.Long"
                not-null="false"
        />

    </class>
</hibernate-mapping>