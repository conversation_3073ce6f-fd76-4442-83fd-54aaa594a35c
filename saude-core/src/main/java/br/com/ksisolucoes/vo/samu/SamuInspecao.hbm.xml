<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.samu"  >
    <class name="SamuInspecao" table="samu_inspecao" >
        <id
            column="cd_samu_inspecao"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />    
        
        <many-to-one class="br.com.ksisolucoes.vo.basico.Empresa" 
                     not-null="true"
                     name="empresa">
            <column name="empresa" />
        </many-to-one>
        
        <many-to-one class="br.com.ksisolucoes.vo.cadsus.Profissional" 
                     not-null="true"
                     name="profissional">
            <column name="cd_profissional" />
        </many-to-one>

        <property
            name="dataChecagem"
            column="dt_checagem"
            type="timestamp"
            not-null="true"
        />

        <many-to-one class="br.com.ksisolucoes.vo.samu.SamuChecklist" 
                     not-null="true"
                     name="samuChecklist">
            <column name="cd_samu_checklist" />
        </many-to-one>
       
        <property
            name="dataCadastro"
            column="dt_cadastro"
            type="timestamp"
            not-null="true"
        />
    </class>
</hibernate-mapping>