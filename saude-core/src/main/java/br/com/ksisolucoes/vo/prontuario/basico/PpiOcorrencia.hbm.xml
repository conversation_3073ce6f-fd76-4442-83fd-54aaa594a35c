<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico">
    <class name="PpiOcorrencia" table="ppi_ocorrencia">

        <id name="codigo" type="java.lang.Long" column="cd_ppi_ocorrencia">
            <generator class="sequence">
                <param name="sequence">seq_audit_id_ppi_ocorrencia</param>
            </generator>
        </id>

        <version column="version" name="version" type="long"/>

        <property
                name="cdPpiSecretaria"
                column="cd_ppi_secretaria"
                type="java.lang.Long"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.basico.PpiTipoExame"
                column="cd_ppi_tipo_exame"
                name="ppiTipoExame"
                not-null="false"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.basico.PpiExame"
                column="cd_ppi_exame"
                name="ppiExame"
                not-null="false"
        />

        <property
                name="cdSolicitacaoAgendamento"
                column="cd_solicitacao_agendamento"
                type="java.lang.Long"
                not-null="false"
        />

        <property
                name="tpOcorrencia"
                column="tp_ocorrencia"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                name="dtOcorrencia"
                column="dt_ocorrencia"
                type="java.util.Date"
                not-null="true"
        />

        <property
                name="dsOcorrencia"
                column="ds_ocorrencia"
                type="java.lang.String"
                not-null="true"
                length="200"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario"
                name="usuario"
                not-null="true"
        />
    </class>
</hibernate-mapping>