package br.com.ksisolucoes.vo.vigilancia.investigacao.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoLeptospirose;


/**
 * This is an object that contains data related to the investigacao_agr_leptospirose_local_situacao_risco table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agr_leptospirose_local_situacao_risco"
 */

public abstract class BaseInvestigacaoAgravoLeptospiroseLocalSituacaoRisco extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravoLeptospiroseLocalSituacaoRisco";
	public static final String PROP_LOCALIDADE = "localidade";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_CIDADE_LOCAL_SITUACAO_RISCO = "cidadeLocalSituacaoRisco";
	public static final String PROP_INVESTIGACAO_AGRAVO_LEPTOSPIROSE = "investigacaoAgravoLeptospirose";
	public static final String PROP_DATA_LOCAL_SITUACAO_RISCO = "dataLocalSituacaoRisco";
	public static final String PROP_ENDERECO = "endereco";

	// constructors
	public BaseInvestigacaoAgravoLeptospiroseLocalSituacaoRisco () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravoLeptospiroseLocalSituacaoRisco (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseInvestigacaoAgravoLeptospiroseLocalSituacaoRisco (
			java.lang.Long codigo,
			br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoLeptospirose investigacaoAgravoLeptospiroseleptospirose) {

		this.setCodigo(codigo);
		this.setInvestigacaoAgravoLeptospirose(investigacaoAgravoLeptospiroseleptospirose);
		initialize();
	}

	protected void initialize () {}

	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataLocalSituacaoRisco;
	private java.lang.String endereco;
	private java.lang.String localidade;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoLeptospirose investigacaoAgravoLeptospirose;
	private br.com.ksisolucoes.vo.basico.Cidade cidadeLocalSituacaoRisco;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_invest_agr_leptospirose_local_situacao_risco"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_local_situacao
	 */
	public java.util.Date getDataLocalSituacaoRisco () {
		return getPropertyValue(this, dataLocalSituacaoRisco, PROP_DATA_LOCAL_SITUACAO_RISCO); 
	}

	/**
	 * Set the value related to the column: dt_local_situacao
	 * @param dataLocalSituacaoRisco the dt_local_situacao value
	 */
	public void setDataLocalSituacaoRisco (java.util.Date dataLocalSituacaoRisco) {
//        java.util.Date dataLocalSituacaoRiscoOld = this.dataLocalSituacaoRisco;
		this.dataLocalSituacaoRisco = dataLocalSituacaoRisco;
//        this.getPropertyChangeSupport().firePropertyChange ("dataLocalSituacaoRisco", dataLocalSituacaoRiscoOld, dataLocalSituacaoRisco);
	}



	/**
	 * Return the value associated with the column: str_endereco
	 */
	public java.lang.String getEndereco () {
		return getPropertyValue(this, endereco, PROP_ENDERECO); 
	}

	/**
	 * Set the value related to the column: str_endereco
	 * @param endereco the str_endereco value
	 */
	public void setEndereco (java.lang.String endereco) {
//        java.lang.String enderecoOld = this.endereco;
		this.endereco = endereco;
//        this.getPropertyChangeSupport().firePropertyChange ("endereco", enderecoOld, endereco);
	}



	/**
	 * Return the value associated with the column: str_localidade
	 */
	public java.lang.String getLocalidade () {
		return getPropertyValue(this, localidade, PROP_LOCALIDADE); 
	}

	/**
	 * Set the value related to the column: str_localidade
	 * @param localidade the str_localidade value
	 */
	public void setLocalidade (java.lang.String localidade) {
//        java.lang.String localidadeOld = this.localidade;
		this.localidade = localidade;
//        this.getPropertyChangeSupport().firePropertyChange ("localidade", localidadeOld, localidade);
	}



	/**
	 * Return the value associated with the column: cd_invest_agr_leptospirose
	 */
	public br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoLeptospirose getInvestigacaoAgravoLeptospirose () {
		return getPropertyValue(this, investigacaoAgravoLeptospirose, PROP_INVESTIGACAO_AGRAVO_LEPTOSPIROSE); 
	}

	/**
	 * Set the value related to the column: cd_invest_agr_leptospirose
	 * @param investigacaoAgravoLeptospirose the cd_invest_agr_leptospirose value
	 */
	public void setInvestigacaoAgravoLeptospirose (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoLeptospirose investigacaoAgravoLeptospirose) {
//        br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoLeptospirose investigacaoAgravoLeptospiroseOld = this.investigacaoAgravoLeptospirose;
		this.investigacaoAgravoLeptospirose = investigacaoAgravoLeptospirose;
//        this.getPropertyChangeSupport().firePropertyChange ("investigacaoAgravoLeptospirose", investigacaoAgravoLeptospiroseOld, investigacaoAgravoLeptospirose);
	}



	/**
	 * Return the value associated with the column: cd_cidade
	 */
	public br.com.ksisolucoes.vo.basico.Cidade getCidadeLocalSituacaoRisco () {
		return getPropertyValue(this, cidadeLocalSituacaoRisco, PROP_CIDADE_LOCAL_SITUACAO_RISCO); 
	}

	/**
	 * Set the value related to the column: cd_cidade
	 * @param cidadeLocalSituacaoRisco the cd_cidade value
	 */
	public void setCidadeLocalSituacaoRisco (br.com.ksisolucoes.vo.basico.Cidade cidadeLocalSituacaoRisco) {
//        br.com.ksisolucoes.vo.basico.Cidade cidadeLocalSituacaoRiscoOld = this.cidadeLocalSituacaoRisco;
		this.cidadeLocalSituacaoRisco = cidadeLocalSituacaoRisco;
//        this.getPropertyChangeSupport().firePropertyChange ("cidadeLocalSituacaoRisco", cidadeLocalSituacaoRiscoOld, cidadeLocalSituacaoRisco);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoLeptospiroseLocalSituacaoRisco)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoLeptospiroseLocalSituacaoRisco investigacaoAgravoLeptospiroseLocalSituacaoRisco = (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoLeptospiroseLocalSituacaoRisco) obj;
			if (null == this.getCodigo() || null == investigacaoAgravoLeptospiroseLocalSituacaoRisco.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravoLeptospiroseLocalSituacaoRisco.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}