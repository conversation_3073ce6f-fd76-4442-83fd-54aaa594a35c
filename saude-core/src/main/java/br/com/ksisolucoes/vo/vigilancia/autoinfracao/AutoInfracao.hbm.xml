<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.autoinfracao"  >
    <class name="AutoInfracao" table="auto_infracao">
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_auto_infracao"
        >
            <generator class="sequence">
                <param name="sequence">seq_auto_infracao</param>
            </generator>
        </id> 
        <version column="version" name="version" type="long" />
        
        <property
            column="numero_formulario"
            name="numeroFormulario"
            not-null="false"
            type="java.lang.Long"
        />
        
        <property
            name="serie"
            column="serie"
            type="java.lang.String"
            not-null="false"
            length="3"
        />
        
        <many-to-one class="br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia"
                     name="denuncia" not-null="false">
            <column name="cd_denuncia"/>
        </many-to-one>
        
        <many-to-one class="br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao"
                     name="autoIntimacao" not-null="false">
            <column name="cd_auto_intimacao"/>
        </many-to-one>
        
        <property
            column="dt_infracao"
            name="dataInfracao"
            not-null="true"
            type="timestamp"
        />
        
        <property
            column="tp_denunciado"
            name="tipoDenunciado"
            not-null="true"
            type="java.lang.Long"
        />
        
        <property
            column="denunciado"
            name="denunciado"
            type="java.lang.String"
            not-null="true"
        />
        
        <many-to-one class="br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco"
                     name="vigilanciaEndereco" not-null="false">
            <column name="cd_vigilancia_endereco"/>
        </many-to-one>
        
        <property
            column="enquadramento_legal"
            name="enquadramentoLegal"
            type="java.lang.String"
            not-null="false"
        />
        <!--<property
            column="irregularidade"
            name="irregularidade"
            type="java.lang.String"
            not-null="false"
        />-->
		<!--<property-->
            <!--column="observacao"-->
            <!--name="observacao"-->
            <!--type="java.lang.String"-->
            <!--not-null="false"-->
        <!--/>-->
        <property
            column="nm_responsavel"
            name="nomeResponsavel"
            type="java.lang.String"
            not-null="false"
        />
        
        <property
            column="dt_recebimento" 
            name="dataRecebimento"
            not-null="false"
            type="timestamp"
        />
        
        <property
            column="testemunha"
            name="testemunha"
            type="java.lang.String"
            not-null="false"
        />
        
        <property
            column="flag_recusou_assinar"
            name="recusouAssinar"
            not-null="false"
            type="java.lang.Long"
        />
        
        <property
            column="status"
            name="situacao"
            not-null="true"
            type="java.lang.Long"
        />
        
        <property
            column="nm_responsavel_defesa"
            name="nomeResponsavelDefesa"
            type="java.lang.String"
            not-null="false"
        />
        
        <property
            column="dt_entrega" 
            name="dataEntrega"
            not-null="false"
            type="timestamp"
        />

        <!--<property-->
            <!--column="status_conclusao"-->
            <!--name="situacaoConclusao"-->
            <!--not-null="true"-->
            <!--type="java.lang.Long"-->
        <!--/>-->

        <!--<property-->
            <!--&lt;!&ndash;column="dt_analise" &ndash;&gt;-->
            <!--&lt;!&ndash;name="dataAnalise"&ndash;&gt;-->
            <!--not-null="false"-->
            <!--type="timestamp"-->
        <!--/>-->
        <!---->
        <!--<property-->
            <!--column="nm_processo"-->
            <!--name="numeroProcesso"-->
            <!--not-null="false"-->
            <!--type="java.lang.Long"-->
        <!--/>-->
        <!---->
        <!--<property-->
            <!--column="nm_responsavel_analise"-->
            <!--name="nomeResponsavelAnalise"-->
            <!--type="java.lang.String"-->
            <!--not-null="false"-->
        <!--/>-->
        
        <property
            column="dt_conclusao" 
            name="dataConclusao"
            not-null="false"
            type="timestamp"
        />
        
        <property
            column="dt_cadastro" 
            name="dataCadastro"
            not-null="true"
            type="timestamp"
        />
        
        <property
            column="dt_usuario" 
            name="dataUsuario"
            not-null="true"
            type="timestamp"
        />
                
        <many-to-one class="br.com.ksisolucoes.vo.controle.Usuario"
                     name="usuario" not-null="true">
            <column name="cd_usuario"/>
        </many-to-one>
        
        <many-to-one class="br.com.ksisolucoes.vo.controle.Usuario"
                     name="usuarioEdicao" not-null="true">
            <column name="cd_usuario_edicao"/>
        </many-to-one>
        
        <property
            column="prazo_defesa"
            name="prazoDefesa"
            not-null="false"
            type="timestamp"
        />
        
        <many-to-one class="br.com.ksisolucoes.vo.vigilancia.Estabelecimento"
                     name="estabelecimento" not-null="false">
            <column name="cd_estabelecimento"/>
        </many-to-one>

        <many-to-one class="br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa"
                     name="vigilanciaPessoa" not-null="false">
            <column name="cd_vigilancia_pessoa"/>
        </many-to-one>

        <many-to-one class="br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecao"
                      name="registroInspecao" not-null="false">
            <column name="cd_roteiro_inspecao"/>
        </many-to-one>
        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.autointimacao.MotivoRetorno"
                name="motivoRetorno"
                column="cd_motivo_retorno"
                not-null="false"
        />
        <property
                column="enviado"
                name="enviado"
                not-null="false"
                type="java.lang.Long"
        />

        <property
                column="num_auto_infracao"
                name="numero"
                not-null="false"
                type="java.lang.Long"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia"
                name="requerimentoVigilancia"
                column="cd_req_vigilancia"
                not-null="false"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.RelatorioInspecao"
                name="relatorioInspecao"
                column="cd_relatorio_inspecao"
                not-null="false"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoAutenticacao"
                name="processoAdministrativoAutenticacao"
                column="cd_processo_adm_autenticacao"
                not-null="false"
        />

        <many-to-one class="br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao"
                     name="autoIntimacaoSubsistente" not-null="false">
            <column name="cd_auto_intimacao_subsistente"/>
        </many-to-one>

        <property
                column="autuado_recusou_auto"
                name="autuadoRecusouAuto"
                not-null="false"
                type="java.lang.Long"
        />

        <property
                column="flag_criado_app_fru"
                name="flagCriadoPeloAppFru"
                not-null="true"
                type="java.lang.Long"
        />

        <property
                column="flag_email_enviado_fru"
                name="flagEmailEnviadoFru"
                not-null="true"
                type="java.lang.Long"
        />

        <property
                column="uuid_app_fru"
                name="uuidAppFru"
                not-null="false"
                type="java.lang.String"
        />

    </class>
</hibernate-mapping>
