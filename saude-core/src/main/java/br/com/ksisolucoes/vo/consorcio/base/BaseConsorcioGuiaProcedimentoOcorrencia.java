package br.com.ksisolucoes.vo.consorcio.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the consorcio_guia_proce_ocorrencia table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="consorcio_guia_proce_ocorrencia"
 */

public abstract class BaseConsorcioGuiaProcedimentoOcorrencia extends BaseRootVO implements Serializable {

	public static String REF = "ConsorcioGuiaProcedimentoOcorrencia";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_CONSORCIO_GUIA_PROCEDIMENTO = "consorcioGuiaProcedimento";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_DATA_OCORRENCIA = "dataOcorrencia";
	public static final String PROP_TIPO_OCORRENCIA = "tipoOcorrencia";


	// constructors
	public BaseConsorcioGuiaProcedimentoOcorrencia () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseConsorcioGuiaProcedimentoOcorrencia (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseConsorcioGuiaProcedimentoOcorrencia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento consorcioGuiaProcedimento,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.Long tipoOcorrencia,
		java.util.Date dataOcorrencia,
		java.lang.String descricao) {

		this.setCodigo(codigo);
		this.setConsorcioGuiaProcedimento(consorcioGuiaProcedimento);
		this.setUsuario(usuario);
		this.setTipoOcorrencia(tipoOcorrencia);
		this.setDataOcorrencia(dataOcorrencia);
		this.setDescricao(descricao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long tipoOcorrencia;
	private java.util.Date dataOcorrencia;
	private java.lang.String descricao;

	// many to one
	private br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento consorcioGuiaProcedimento;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_consorcio_guia_proce_ocorrencia"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_tp_ocorrencia
	 */
	public java.lang.Long getTipoOcorrencia () {
		return getPropertyValue(this, tipoOcorrencia, PROP_TIPO_OCORRENCIA); 
	}

	/**
	 * Set the value related to the column: cd_tp_ocorrencia
	 * @param tipoOcorrencia the cd_tp_ocorrencia value
	 */
	public void setTipoOcorrencia (java.lang.Long tipoOcorrencia) {
//        java.lang.Long tipoOcorrenciaOld = this.tipoOcorrencia;
		this.tipoOcorrencia = tipoOcorrencia;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoOcorrencia", tipoOcorrenciaOld, tipoOcorrencia);
	}



	/**
	 * Return the value associated with the column: dt_ocorrencia
	 */
	public java.util.Date getDataOcorrencia () {
		return getPropertyValue(this, dataOcorrencia, PROP_DATA_OCORRENCIA); 
	}

	/**
	 * Set the value related to the column: dt_ocorrencia
	 * @param dataOcorrencia the dt_ocorrencia value
	 */
	public void setDataOcorrencia (java.util.Date dataOcorrencia) {
//        java.util.Date dataOcorrenciaOld = this.dataOcorrencia;
		this.dataOcorrencia = dataOcorrencia;
//        this.getPropertyChangeSupport().firePropertyChange ("dataOcorrencia", dataOcorrenciaOld, dataOcorrencia);
	}



	/**
	 * Return the value associated with the column: ds_ocorrencia
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_ocorrencia
	 * @param descricao the ds_ocorrencia value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: cd_guia
	 */
	public br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento getConsorcioGuiaProcedimento () {
		return getPropertyValue(this, consorcioGuiaProcedimento, PROP_CONSORCIO_GUIA_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_guia
	 * @param consorcioGuiaProcedimento the cd_guia value
	 */
	public void setConsorcioGuiaProcedimento (br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento consorcioGuiaProcedimento) {
//        br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento consorcioGuiaProcedimentoOld = this.consorcioGuiaProcedimento;
		this.consorcioGuiaProcedimento = consorcioGuiaProcedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("consorcioGuiaProcedimento", consorcioGuiaProcedimentoOld, consorcioGuiaProcedimento);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimentoOcorrencia)) return false;
		else {
			br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimentoOcorrencia consorcioGuiaProcedimentoOcorrencia = (br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimentoOcorrencia) obj;
			if (null == this.getCodigo() || null == consorcioGuiaProcedimentoOcorrencia.getCodigo()) return false;
			else return (this.getCodigo().equals(consorcioGuiaProcedimentoOcorrencia.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}