package br.com.ksisolucoes.vo.basico;

import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import br.com.ksisolucoes.vo.basico.base.BaseMotivoCancelamento;

/**
 * Esta classe est relacionado com a tabela motivos_cancelamento.
 * Classe dimensionada para customizaes.
 *
 */
public class MotivoCancelamento extends BaseMotivoCancelamento implements CodigoManager, PesquisaObjectInterface {

/*[CONSTRUCTOR MARKER BEGIN]*/
	public MotivoCancelamento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public MotivoCancelamento (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public MotivoCancelamento (
		java.lang.Long codigo,
		java.lang.String descricao) {

		super (
			codigo,
			descricao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public String getDescricaoVO() {
        return this.getDescricao();
    }

    public String getIdentificador() {
        return this.getCodigo().toString();
    }

    public java.io.Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public void setCodigoManager(java.io.Serializable key) {
        this.setCodigo( (Long)key );
    }

    public String toString() {
        return Coalesce.asString( this.getDescricao() );
    }

}
