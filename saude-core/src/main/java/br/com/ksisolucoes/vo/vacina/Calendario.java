package br.com.ksisolucoes.vo.vacina;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import br.com.ksisolucoes.vo.vacina.base.BaseCalendario;

import java.io.Serializable;

public class Calendario extends BaseCalendario implements CodigoManager, PesquisaObjectInterface {

    private static final long serialVersionUID = 1L;

    public static final String PROP_PADRAO_FORMATADO = "padraoFormatado";

    public enum Aprazamento implements IEnum {
        AUTOMATICO(0L, Bundle.getStringApplication("rotulo_automatico")),
        MANUAL(1L, Bundle.getStringApplication("rotulo_manual")),
        ;

        private Long value;
        private String descricao;

        private Aprazamento(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Aprazamento valeuOf(Long value) {
            for (Aprazamento aprazamento : Aprazamento.values()) {
                if (aprazamento.value().equals(value)) {
                    return aprazamento;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }

    public enum EstrategiaEsusPniRnds implements IEnum {
        ROTINA(1L, 1L, "Rotina"),
        ESPECIAL(2L, 2L, "Especial"),
        BLOQUEIO(3L, 3L, "Bloqueio"),
        INTENSIFICACAO(4L, 4L, "Intensificação"),
        CAMPANHA_INDISCRIMINADA(5L, 5L, "Campanha Indiscriminada"),
        CAMPANHA_SELETIVA(6L, 6L, "Campanha Seletiva"),
        SOROTERAPIA(7L, 7L, "Soroterapia"),
        SERVICO_PRIVADO(8L, 8L, "Serviço Privado"),
        MONITORAMENTO_RAPIDO_COBERTURA_VACINAL(9L, 9L, "Monitoramento Rápido de Cobertura Vacinal"),
        MULTIVACINACAO(10L, null, "Multivacinação"),
        PESQUISA(11L, 10L, "Pesquisa"),
        PRE_EXPOSICAO(12L, 11L, "Pré-Exposição"),
        POS_EXPOSICAO(13L, 12L, "Pós-Exposição"),
        REEXPOSICAO(14L, 13L, "Reexposição"),
        VACINACAO_ESCOLAR(15L, 14L, "Vacinação Escolar"),
        ;

        private Long codigoEsusPni;
        private Long codigoRNDS;
        private String descricao;

        EstrategiaEsusPniRnds(Long codigoEsusPni, Long codigoRNDS, String descricao) {
            this.codigoEsusPni = codigoEsusPni;
            this.codigoRNDS = codigoRNDS;
            this.descricao = descricao;
        }

        public static EstrategiaEsusPniRnds valueOfEsus(Long codigoEsus) {
            for (EstrategiaEsusPniRnds estrategiaEsusPniRnds : EstrategiaEsusPniRnds.values()) {
                if (estrategiaEsusPniRnds.value() != null ){
                    if (estrategiaEsusPniRnds.value().equals(codigoEsus)) {
                        return estrategiaEsusPniRnds;
                    }
                }
            }
            return null;
        }

        public static EstrategiaEsusPniRnds valueOfRnds(Long codigoRnds) {
            for (EstrategiaEsusPniRnds estrategiaEsusPniRnds : EstrategiaEsusPniRnds.values()) {
                if (estrategiaEsusPniRnds.getCodigoRNDS() != null){
                    if (estrategiaEsusPniRnds.getCodigoRNDS().equals(codigoRnds)) {
                        return estrategiaEsusPniRnds;
                    }
                }

            }
            return null;
        }

        public Long getCodigoEsusPni() {
            return codigoEsusPni;
        }

        public void setCodigoEsusPni(Long codigoEsusPni) {
            this.codigoEsusPni = codigoEsusPni;
        }

        public Long getCodigoRNDS() {
            return codigoRNDS;
        }

        public void setCodigoRNDS(Long codigoRNDS) {
            this.codigoRNDS = codigoRNDS;
        }

        public String getDescricao() {
            return descricao;
        }

        public void setDescricao(String descricao) {
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return codigoEsusPni;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }
        
/*[CONSTRUCTOR MARKER BEGIN]*/
	public Calendario () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public Calendario (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public Calendario (
		java.lang.Long codigo,
		java.lang.String descricao,
		java.lang.String padrao,
		java.lang.Long flagAtualizacao,
		java.lang.Long flagAprazamento,
		java.lang.Long codigoEstrategiaPni) {

		super (
			codigo,
			descricao,
			padrao,
			flagAtualizacao,
			flagAprazamento,
			codigoEstrategiaPni);
	}

    /*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getPadraoFormatado(){
        if (RepositoryComponentDefault.SIM.equals(getPadrao())) {
            return Bundle.getStringApplication("rotulo_sim");
        } else if (RepositoryComponentDefault.NAO.equals(getPadrao())) {
            return Bundle.getStringApplication("rotulo_nao");
        }
        return Bundle.getStringApplication("rotulo_desconhecido");
    }
    
    public String getFlagAtualizacaoFormatado(){
        if (RepositoryComponentDefault.SIM_LONG.equals(getFlagAtualizacao())) {
            return Bundle.getStringApplication("rotulo_sim");
        } else if (RepositoryComponentDefault.NAO_LONG.equals(getFlagAtualizacao())) {
            return Bundle.getStringApplication("rotulo_nao");
        }
        return Bundle.getStringApplication("rotulo_desconhecido");
    }
    
    public String getDescricaoFlagAprazamento(){
        Aprazamento aprazamento = Aprazamento.valeuOf(getFlagAprazamento());
        if (aprazamento != null && aprazamento.descricao != null) {
            return aprazamento.descricao();
        }
        return "";
    }
    
    public String getDescricaoEstrategiaEsus(){
        EstrategiaEsusPniRnds estrategiaEsusPniRnds = EstrategiaEsusPniRnds.valueOfEsus(getEstrategiaEsus());
        if (estrategiaEsusPniRnds != null && estrategiaEsusPniRnds.descricao != null) {
            return estrategiaEsusPniRnds.descricao();
        }
        return "";
    }

    public String getDescricaoEstrategiaRndsComCodigo() {
        EstrategiaEsusPniRnds estrategiaRnds = EstrategiaEsusPniRnds.valueOfEsus(getEstrategiaEsus());
        if (estrategiaRnds != null && estrategiaRnds.descricao != null) {
            return estrategiaRnds.getCodigoRNDS() + " - " + estrategiaRnds.descricao();
        }
        return getDescricao();
    }


    @Override
    public String getDescricaoVO() {
        return this.getDescricao();
    }

    @Override
    public String getIdentificador() {
        return this.getCodigo().toString();
    }
}
