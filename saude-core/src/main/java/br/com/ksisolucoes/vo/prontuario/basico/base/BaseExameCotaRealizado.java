package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the exame_cota_realizado table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="exame_cota_realizado"
 */

public abstract class BaseExameCotaRealizado extends BaseRootVO implements Serializable {

	public static String REF = "ExameCotaRealizado";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_EXAME_PRESTADOR_COMPETENCIA = "examePrestadorCompetencia";
	public static final String PROP_EXAME_UNIDADE_COMPETENCIA = "exameUnidadeCompetencia";
	public static final String PROP_VALOR_PROCEDIMENTO = "valorProcedimento";
	public static final String PROP_EXAME_REQUISICAO = "exameRequisicao";


	// constructors
	public BaseExameCotaRealizado () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseExameCotaRealizado (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseExameCotaRealizado (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorCompetencia examePrestadorCompetencia,
		br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicao,
		java.util.Date dataCadastro,
		java.lang.Double valorProcedimento) {

		this.setCodigo(codigo);
		this.setExamePrestadorCompetencia(examePrestadorCompetencia);
		this.setExameRequisicao(exameRequisicao);
		this.setDataCadastro(dataCadastro);
		this.setValorProcedimento(valorProcedimento);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataCadastro;
	private java.lang.Double valorProcedimento;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorCompetencia examePrestadorCompetencia;
	private br.com.ksisolucoes.vo.agendamento.ExameUnidadeCompetencia exameUnidadeCompetencia;
	private br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_exame_cota_realizado"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: vlr_procedimento
	 */
	public java.lang.Double getValorProcedimento () {
		return getPropertyValue(this, valorProcedimento, PROP_VALOR_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: vlr_procedimento
	 * @param valorProcedimento the vlr_procedimento value
	 */
	public void setValorProcedimento (java.lang.Double valorProcedimento) {
//        java.lang.Double valorProcedimentoOld = this.valorProcedimento;
		this.valorProcedimento = valorProcedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("valorProcedimento", valorProcedimentoOld, valorProcedimento);
	}



	/**
	 * Return the value associated with the column: cd_prestador_competencia
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorCompetencia getExamePrestadorCompetencia () {
		return getPropertyValue(this, examePrestadorCompetencia, PROP_EXAME_PRESTADOR_COMPETENCIA); 
	}

	/**
	 * Set the value related to the column: cd_prestador_competencia
	 * @param examePrestadorCompetencia the cd_prestador_competencia value
	 */
	public void setExamePrestadorCompetencia (br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorCompetencia examePrestadorCompetencia) {
//        br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorCompetencia examePrestadorCompetenciaOld = this.examePrestadorCompetencia;
		this.examePrestadorCompetencia = examePrestadorCompetencia;
//        this.getPropertyChangeSupport().firePropertyChange ("examePrestadorCompetencia", examePrestadorCompetenciaOld, examePrestadorCompetencia);
	}



	/**
	 * Return the value associated with the column: cd_uni_exa_competencia
	 */
	public br.com.ksisolucoes.vo.agendamento.ExameUnidadeCompetencia getExameUnidadeCompetencia () {
		return getPropertyValue(this, exameUnidadeCompetencia, PROP_EXAME_UNIDADE_COMPETENCIA); 
	}

	/**
	 * Set the value related to the column: cd_uni_exa_competencia
	 * @param exameUnidadeCompetencia the cd_uni_exa_competencia value
	 */
	public void setExameUnidadeCompetencia (br.com.ksisolucoes.vo.agendamento.ExameUnidadeCompetencia exameUnidadeCompetencia) {
//        br.com.ksisolucoes.vo.agendamento.ExameUnidadeCompetencia exameUnidadeCompetenciaOld = this.exameUnidadeCompetencia;
		this.exameUnidadeCompetencia = exameUnidadeCompetencia;
//        this.getPropertyChangeSupport().firePropertyChange ("exameUnidadeCompetencia", exameUnidadeCompetenciaOld, exameUnidadeCompetencia);
	}



	/**
	 * Return the value associated with the column: cd_exame_requisicao
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao getExameRequisicao () {
		return getPropertyValue(this, exameRequisicao, PROP_EXAME_REQUISICAO); 
	}

	/**
	 * Set the value related to the column: cd_exame_requisicao
	 * @param exameRequisicao the cd_exame_requisicao value
	 */
	public void setExameRequisicao (br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicao) {
//        br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicaoOld = this.exameRequisicao;
		this.exameRequisicao = exameRequisicao;
//        this.getPropertyChangeSupport().firePropertyChange ("exameRequisicao", exameRequisicaoOld, exameRequisicao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.ExameCotaRealizado)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.ExameCotaRealizado exameCotaRealizado = (br.com.ksisolucoes.vo.prontuario.basico.ExameCotaRealizado) obj;
			if (null == this.getCodigo() || null == exameCotaRealizado.getCodigo()) return false;
			else return (this.getCodigo().equals(exameCotaRealizado.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}