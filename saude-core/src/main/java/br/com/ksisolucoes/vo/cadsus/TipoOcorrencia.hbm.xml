<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.cadsus"  >
	<class name="TipoOcorrencia" table="tipo_ocorrencia" >
            <id
                name="codigo"
                type="java.lang.Long"
                column="cd_tp_ocorrencia"
            >
                <generator class="assigned" />
            </id> <version column="version" name="version" type="long" />
 
            <property
                column="ds_ocorrencia"
                length="50"
                name="descricao"
                not-null="true"
                type="java.lang.String"
            />

	</class>
</hibernate-mapping>
