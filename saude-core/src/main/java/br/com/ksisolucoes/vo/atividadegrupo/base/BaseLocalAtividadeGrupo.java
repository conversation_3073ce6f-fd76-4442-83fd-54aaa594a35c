package br.com.ksisolucoes.vo.atividadegrupo.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the local_atividade_grupo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="local_atividade_grupo"
 */

public abstract class BaseLocalAtividadeGrupo extends BaseRootVO implements Serializable {

	public static String REF = "LocalAtividadeGrupo";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_TIPO_LOCAL = "tipoLocal";
	public static final String PROP_VERSION_ALL = "versionAll";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_NUMERO_INEP = "numeroInep";


	// constructors
	public BaseLocalAtividadeGrupo () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseLocalAtividadeGrupo (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseLocalAtividadeGrupo (
		java.lang.Long codigo,
		java.lang.String descricao,
		java.lang.Long tipoLocal) {

		this.setCodigo(codigo);
		this.setDescricao(descricao);
		this.setTipoLocal(tipoLocal);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.lang.Long tipoLocal;
	private java.lang.Long numeroInep;
	private java.lang.Long versionAll;

	// many to one
	private br.com.ksisolucoes.vo.basico.Empresa empresa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_local_acao"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_local_acao
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_local_acao
	 * @param descricao the ds_local_acao value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: tipo_local
	 */
	public java.lang.Long getTipoLocal () {
		return getPropertyValue(this, tipoLocal, PROP_TIPO_LOCAL); 
	}

	/**
	 * Set the value related to the column: tipo_local
	 * @param tipoLocal the tipo_local value
	 */
	public void setTipoLocal (java.lang.Long tipoLocal) {
//        java.lang.Long tipoLocalOld = this.tipoLocal;
		this.tipoLocal = tipoLocal;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoLocal", tipoLocalOld, tipoLocal);
	}



	/**
	 * Return the value associated with the column: cod_inep
	 */
	public java.lang.Long getNumeroInep () {
		return getPropertyValue(this, numeroInep, PROP_NUMERO_INEP); 
	}

	/**
	 * Set the value related to the column: cod_inep
	 * @param numeroInep the cod_inep value
	 */
	public void setNumeroInep (java.lang.Long numeroInep) {
//        java.lang.Long numeroInepOld = this.numeroInep;
		this.numeroInep = numeroInep;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroInep", numeroInepOld, numeroInep);
	}



	/**
	 * Return the value associated with the column: version_all
	 */
	public java.lang.Long getVersionAll () {
		return getPropertyValue(this, versionAll, PROP_VERSION_ALL); 
	}

	/**
	 * Set the value related to the column: version_all
	 * @param versionAll the version_all value
	 */
	public void setVersionAll (java.lang.Long versionAll) {
//        java.lang.Long versionAllOld = this.versionAll;
		this.versionAll = versionAll;
//        this.getPropertyChangeSupport().firePropertyChange ("versionAll", versionAllOld, versionAll);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.atividadegrupo.LocalAtividadeGrupo)) return false;
		else {
			br.com.ksisolucoes.vo.atividadegrupo.LocalAtividadeGrupo localAtividadeGrupo = (br.com.ksisolucoes.vo.atividadegrupo.LocalAtividadeGrupo) obj;
			if (null == this.getCodigo() || null == localAtividadeGrupo.getCodigo()) return false;
			else return (this.getCodigo().equals(localAtividadeGrupo.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}