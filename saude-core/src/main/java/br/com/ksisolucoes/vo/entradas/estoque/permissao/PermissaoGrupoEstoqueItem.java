package br.com.ksisolucoes.vo.entradas.estoque.permissao;

import java.io.Serializable;

import br.com.ksisolucoes.vo.entradas.estoque.permissao.base.BasePermissaoGrupoEstoqueItem;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class PermissaoGrupoEstoqueItem extends BasePermissaoGrupoEstoqueItem implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public PermissaoGrupoEstoqueItem () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public PermissaoGrupoEstoqueItem (java.lang.Long codigo) {
		super(codigo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}