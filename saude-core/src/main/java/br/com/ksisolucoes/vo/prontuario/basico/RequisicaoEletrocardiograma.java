package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseRequisicaoEletrocardiograma;



public class RequisicaoEletrocardiograma extends BaseRequisicaoEletrocardiograma implements CodigoManager {
	private static final long serialVersionUID = 1L;
        
        public enum MedicamentosUso{

            DIURETICOS(1L),
            DIGITAL(2L),
            BETABLOQUEADORES(4L),
            INIBIDORES_ENZIMA_CONVERSORA(8L),
            AMIODARONA(16L),
            BLOQUEADORES_CALCIO(32L),
            NENHUM(64L);

            private Long value;

            private MedicamentosUso(Long value) {
                this.value = value;
            }

            public Long value(){
                return value;
            }

            @Override
            public String toString() {
                if (DIURETICOS.equals(this)) {
                    return Bundle.getStringApplication("rotulo_diureticos");
                } else if (DIGITAL.equals(this)) {
                    return Bundle.getStringApplication("rotulo_digital");
                } else if (BETABLOQUEADORES.equals(this)) {
                    return Bundle.getStringApplication("rotulo_betabloqueadores");
                } else if (INIBIDORES_ENZIMA_CONVERSORA.equals(this)) {
                    return Bundle.getStringApplication("rotulo_inibidores_enzima_conversora");
                } else if (AMIODARONA.equals(this)) {
                    return Bundle.getStringApplication("rotulo_amiodarona");
                } else if (BLOQUEADORES_CALCIO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_bloqueadores_calcio");
                } else if (NENHUM.equals(this)) {
                    return Bundle.getStringApplication("rotulo_nenhum");
                }
                return Bundle.getStringApplication("rotulo_desconhecido");
            }

        }
        
        public enum CaracterizacaoDorToracica{

            PROVOCADA_ESFORCO_EMOCAO(1L),
            DESCONFORTO_RETROESTERNAL_QUALIDADE_DURACAO_CARACTERISTICAS(2L),
            ALIVIADA_REPOUSO_NITRATO(4L),
            NENHUM(8L);

            private Long value;

            private CaracterizacaoDorToracica(Long value) {
                this.value = value;
            }

            public Long value(){
                return value;
            }

            @Override
            public String toString() {
                if (PROVOCADA_ESFORCO_EMOCAO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_provocada_esforco_emocao");
                } else if (DESCONFORTO_RETROESTERNAL_QUALIDADE_DURACAO_CARACTERISTICAS.equals(this)) {
                    return Bundle.getStringApplication("rotulo_desconforto_retroesternal_qualidade_duracao_caracteristicas");
                } else if (ALIVIADA_REPOUSO_NITRATO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_aliviada_repouso_nitrato");
                } else if (NENHUM.equals(this)) {
                    return Bundle.getStringApplication("rotulo_nenhum");
                }
                return Bundle.getStringApplication("rotulo_desconhecido");
            }

        }
        
        public enum CoMorbidadesFatoresRisco{

            HIPERTENSAO_ARTERIAL(1L),
            DIABETES_MELLITUS(2L),
            INFARTO_MIOCARDIO_PREVIO(4L),
            DOENCA_RENAL_CRONICA(8L),
            REVASCULARIZACAO_MIOCARDIA_PREVIA(16L),
            DISLIPIDEMIA(32L),
            OBESIDADE(64L),
            TABAGISMO(128L),
            HISTORICO_FAMILIAR_DOENCA_CORONARIANA(256L),
            DOENCA_PULMONAR_CRONICA(512L),
            DOENCA_CHAGAS(1024L),
            NENHUM(2048L);

            private Long value;

            private CoMorbidadesFatoresRisco(Long value) {
                this.value = value;
            }

            public Long value(){
                return value;
            }

            @Override
            public String toString() {
                if (HIPERTENSAO_ARTERIAL.equals(this)) {
                    return Bundle.getStringApplication("rotulo_hipertensao_arterial");
                } else if (DIABETES_MELLITUS.equals(this)) {
                    return Bundle.getStringApplication("rotulo_diabetes_mellitus");
                } else if (INFARTO_MIOCARDIO_PREVIO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_infarto_miocardio_previo");
                } else if (DOENCA_RENAL_CRONICA.equals(this)) {
                    return Bundle.getStringApplication("rotulo_doenca_renal_cronica");
                } else if (REVASCULARIZACAO_MIOCARDIA_PREVIA.equals(this)) {
                    return Bundle.getStringApplication("rotulo_revascularizacao_miocardica_previa");
                } else if (DISLIPIDEMIA.equals(this)) {
                    return Bundle.getStringApplication("rotulo_dislipidemia");
                } else if (OBESIDADE.equals(this)) {
                    return Bundle.getStringApplication("rotulo_obesidade");
                } else if (TABAGISMO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_tabagismo");
                } else if (HISTORICO_FAMILIAR_DOENCA_CORONARIANA.equals(this)) {
                    return Bundle.getStringApplication("rotulo_historico_familiar_doenca_coronariana");
                } else if (DOENCA_PULMONAR_CRONICA.equals(this)) {
                    return Bundle.getStringApplication("rotulo_doenca_pulmonar_cronica");
                } else if (DOENCA_CHAGAS.equals(this)) {
                    return Bundle.getStringApplication("rotulo_doenca_chagas");
                } else if (NENHUM.equals(this)) {
                    return Bundle.getStringApplication("rotulo_nenhum");
                }
                return Bundle.getStringApplication("rotulo_desconhecido");
            }

        }
        
        public enum SintomasAssociados{

            DISPNEIA(1L),
            TONTURA(2L),
            NENHUM(4L),
            SUDORESE(8L),
            SINCOPE(16L),
            NAUSEAS_VOMITOS(32L),
            PALPITACAO(64L);

            private Long value;

            private SintomasAssociados(Long value) {
                this.value = value;
            }

            public Long value(){
                return value;
            }

            @Override
            public String toString() {
                if (DISPNEIA.equals(this)) {
                    return Bundle.getStringApplication("rotulo_dispneia");
                } else if (TONTURA.equals(this)) {
                    return Bundle.getStringApplication("rotulo_tontura");
                } else if (NENHUM.equals(this)) {
                    return Bundle.getStringApplication("rotulo_nenhum");
                } else if (SUDORESE.equals(this)) {
                    return Bundle.getStringApplication("rotulo_sudorese");
                } else if (SINCOPE.equals(this)) {
                    return Bundle.getStringApplication("rotulo_sincope");
                } else if (NAUSEAS_VOMITOS.equals(this)) {
                    return Bundle.getStringApplication("rotulo_nauseas_vomitos");
                } else if (PALPITACAO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_palpitacao");
                }
                return Bundle.getStringApplication("rotulo_desconhecido");
            }

        }
        
        public enum ClassificacaoDor{

            ATIVIDADE_FISICA_NAO_CAUSA_ANGINA(1L),
            LEVE_LIMITACAO_ATIVIDADE_HABITUAL(2L),
            LIMITACAO_ATIVIDADE_HABITUAL(4L),
            INCAPACIDADE_REALIZAR_QUALQUER_ATIVIDADE(8L);

            private Long value;

            private ClassificacaoDor(Long value) {
                this.value = value;
            }

            public Long value(){
                return value;
            }

            @Override
            public String toString() {
                if (ATIVIDADE_FISICA_NAO_CAUSA_ANGINA.equals(this)) {
                    return Bundle.getStringApplication("rotulo_atividade_fisica_nao_causa_angina");
                } else if (LEVE_LIMITACAO_ATIVIDADE_HABITUAL.equals(this)) {
                    return Bundle.getStringApplication("rotulo_leve_limitacao_atividade_habitual");
                } else if (LIMITACAO_ATIVIDADE_HABITUAL.equals(this)) {
                    return Bundle.getStringApplication("rotulo_limitacao_atividade_habitual");
                } else if (INCAPACIDADE_REALIZAR_QUALQUER_ATIVIDADE.equals(this)) {
                    return Bundle.getStringApplication("rotulo_incapacidade_realizar_qualquer_atividade");
                }
                return Bundle.getStringApplication("rotulo_desconhecido");
            }

        }
        
        public enum LocalizacaoIrradiacaoDor{

            MEMBRO_SUPERIOR_DIREITO(1L),
            PESCOCO(2L),
            REGIAO_PRECORDIAL(4L),
            REGIAO_EPIGASTRICA(8L),
            MEMBRO_SUPERIOR_ESQUERDO(16L),
            DORSO(32L),
            REGIAO_TORACICA_DIREITA(64L);

            private Long value;

            private LocalizacaoIrradiacaoDor(Long value) {
                this.value = value;
            }

            public Long value(){
                return value;
            }

            @Override
            public String toString() {
                if (MEMBRO_SUPERIOR_DIREITO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_membro_superior_direito");
                } else if (PESCOCO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_pescoco");
                } else if (REGIAO_PRECORDIAL.equals(this)) {
                    return Bundle.getStringApplication("rotulo_regiao_precordial");
                } else if (REGIAO_EPIGASTRICA.equals(this)) {
                    return Bundle.getStringApplication("rotulo_regiao_epigastrica");
                } else if (MEMBRO_SUPERIOR_ESQUERDO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_membro_superior_esquerdo");
                } else if (DORSO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_dorso");
                } else if (REGIAO_TORACICA_DIREITA.equals(this)) {
                    return Bundle.getStringApplication("rotulo_regiao_toracica_direita");
                }
                return Bundle.getStringApplication("rotulo_desconhecido");
            }

        }
        
        public enum DorNaoCardiaca{

            AORTA(1L),
            PULMONAR(2L),
            MUSCULO_ESQUELETICO(4L),
            GASTROINTESTINAL(8L),
            SOMATIZACAO(16L),
            MEDIASTINO(32L),
            CUTANEA(64L);

            private Long value;

            private DorNaoCardiaca(Long value) {
                this.value = value;
            }

            public Long value(){
                return value;
            }

            @Override
            public String toString() {
                if (AORTA.equals(this)) {
                    return Bundle.getStringApplication("rotulo_aorta");
                } else if (PULMONAR.equals(this)) {
                    return Bundle.getStringApplication("rotulo_pulmonar");
                } else if (MUSCULO_ESQUELETICO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_musculo_esqueletico");
                } else if (GASTROINTESTINAL.equals(this)) {
                    return Bundle.getStringApplication("rotulo_gastrointestinal");
                } else if (SOMATIZACAO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_somatizacao");
                } else if (MEDIASTINO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_mediastino");
                } else if (CUTANEA.equals(this)) {
                    return Bundle.getStringApplication("rotulo_cutanea");
                } 
                return Bundle.getStringApplication("rotulo_desconhecido");
            }

        }
        
        public enum Valvulopatia{

            MITRAL(1L),
            AORTICA(2L),
            MITRO_TRICUSPIDEA(4L),
            PULMONAR(8L),
            MITRO_AORTICA(16L);

            private Long value;

            private Valvulopatia(Long value) {
                this.value = value;
            }

            public Long value(){
                return value;
            }

            @Override
            public String toString() {
                if (MITRAL.equals(this)) {
                    return Bundle.getStringApplication("rotulo_mitral");
                } else if (AORTICA.equals(this)) {
                    return Bundle.getStringApplication("rotulo_aortica");
                } else if (MITRO_TRICUSPIDEA.equals(this)) {
                    return Bundle.getStringApplication("rotulo_mitro_tricuspidea");
                } else if (PULMONAR.equals(this)) {
                    return Bundle.getStringApplication("rotulo_pulmonar");
                } else if (MITRO_AORTICA.equals(this)) {
                    return Bundle.getStringApplication("rotulo_mitro_aortica");
                }
                return Bundle.getStringApplication("rotulo_desconhecido");
            }

        }
        
        public enum DorCardNaoIsquemica{

            MIOCARDITE(1L),
            DOENCA_VALVULAR(2L),
            PERICARDITE(4L),
            OUTROS(8L);

            private Long value;

            private DorCardNaoIsquemica(Long value) {
                this.value = value;
            }

            public Long value(){
                return value;
            }

            @Override
            public String toString() {
                if (MIOCARDITE.equals(this)) {
                    return Bundle.getStringApplication("rotulo_miocardite");
                } else if (DOENCA_VALVULAR.equals(this)) {
                    return Bundle.getStringApplication("rotulo_doenca_valvular");
                } else if (PERICARDITE.equals(this)) {
                    return Bundle.getStringApplication("rotulo_pericardite");
                } else if (OUTROS.equals(this)) {
                    return Bundle.getStringApplication("rotulo_outros");
                }
                return Bundle.getStringApplication("rotulo_desconhecido");
            }

        }
        
        public enum DorCardIsquemica{

            INFARTO_MIOCARDIO(1L),
            ANGINA_ESTAVEL(2L),
            ANGINA_INSTAVEL(4L);

            private Long value;

            private DorCardIsquemica(Long value) {
                this.value = value;
            }

            public Long value(){
                return value;
            }

            @Override
            public String toString() {
                if (INFARTO_MIOCARDIO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_infarto_miocardio");
                } else if (ANGINA_ESTAVEL.equals(this)) {
                    return Bundle.getStringApplication("rotulo_angina_estavel");
                } else if (ANGINA_INSTAVEL.equals(this)) {
                    return Bundle.getStringApplication("rotulo_angina_instavel");
                }
                return Bundle.getStringApplication("rotulo_desconhecido");
            }

        }

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequisicaoEletrocardiograma () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequisicaoEletrocardiograma (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequisicaoEletrocardiograma (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicao) {

		super (
			codigo,
			exameRequisicao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
 
    public String getMedicamentosUsoFormatado(Long medicamento){
        for (MedicamentosUso medicamentosUso : MedicamentosUso.values()) {
            if (medicamentosUso.value().equals(medicamento)) {
                return medicamentosUso.toString();
            }
        }
        return Bundle.getStringApplication("rotulo_desconhecido");
    }
}