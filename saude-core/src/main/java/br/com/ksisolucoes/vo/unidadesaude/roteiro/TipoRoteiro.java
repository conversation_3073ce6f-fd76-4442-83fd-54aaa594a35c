package br.com.ksisolucoes.vo.unidadesaude.roteiro;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.unidadesaude.roteiro.base.BaseTipoRoteiro;

import java.io.Serializable;

public class TipoRoteiro extends BaseTipoRoteiro implements CodigoManager {

    private static final long serialVersionUID = -1946410785966828604L;

    public static final int ORDEM_MIN = 0;
    public static final int ORDEM_MAX = 99;

    public String getDescricaoStatus() {
        Status status = Status.valueOf(getStatus());
        if (status != null && status.descricao != null) {
            return status.descricao();
        }
        return "";
    }

    public String getDescricaoCategoria() {
        return Categoria.valueOf(getCategoria()).getValue();
    }

    public enum Categoria {
        ENTREVISTA(Bundle.getStringApplication("rotulo_entrevista")),
        EXAME_FISICO(Bundle.getStringApplication("rotulo_exame_fisico")),
        ROTEIRO_ENFERMAGEM(Bundle.getStringApplication("rotulo_roteiro_enfermagem"))
        ;

        private final String value;

        Categoria(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public enum Status implements IEnum<Status> {

        INATIVO(0L, Bundle.getStringApplication("rotulo_inativo")),
        ATIVO(1L, Bundle.getStringApplication("rotulo_ativo")),
        ;

        private final Long value;
        private final String descricao;

        Status(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static Status valueOf(Long value) {
            for (Status status : Status.values()) {
                if (status.value().equals(value)) {
                    return status;
                }
            }
            return null;
        }
    }

    public void setCodigoManager(Serializable key) {
        this.setCodigo((Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}
