package br.com.ksisolucoes.vo.vacina;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vacina.base.BaseEloTipoVacinaDose;

import java.io.Serializable;



public class EloTipoVacinaDose extends BaseEloTipoVacinaDose implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EloTipoVacinaDose () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public EloTipoVacinaDose (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public EloTipoVacinaDose (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vacina.TipoVacina tipoVacina,
		java.lang.Long dose,
		java.util.Date dataCadastro) {

		super (
			codigo,
			tipoVacina,
			dose,
			dataCadastro);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public String getDescricaoDose(){
        return VacinaCalendario.Doses.valueOf(getDose()).descricao();
    }
}