package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;

import br.com.ksisolucoes.vo.prontuario.basico.base.BaseResultadoExameRequisicaoRtf;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class ResultadoExameRequisicaoRtf extends BaseResultadoExameRequisicaoRtf implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ResultadoExameRequisicaoRtf () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ResultadoExameRequisicaoRtf (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ResultadoExameRequisicaoRtf (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicao,
		java.lang.String rtf) {

		super (
			codigo,
			exameRequisicao,
			rtf);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}