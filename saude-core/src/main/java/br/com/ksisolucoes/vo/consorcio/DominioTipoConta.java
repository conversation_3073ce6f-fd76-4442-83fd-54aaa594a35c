package br.com.ksisolucoes.vo.consorcio;

import java.io.Serializable;

import br.com.ksisolucoes.vo.consorcio.base.BaseDominioTipoConta;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;



public class DominioTipoConta extends BaseDominioTipoConta implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public DominioTipoConta () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public DominioTipoConta (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public DominioTipoConta (
		java.lang.Long codigo,
		java.lang.String descricaoTipoConta,
		java.lang.String referencia,
		java.lang.String keyword) {

		super (
			codigo,
			descricaoTipoConta,
			referencia,
			keyword);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    @Override
    public String getDescricaoVO() {
        return getDescricaoTipoConta();
    }

    @Override
    public String getIdentificador() {
        return getTipoConta().getCodigo().toString();
    }
}