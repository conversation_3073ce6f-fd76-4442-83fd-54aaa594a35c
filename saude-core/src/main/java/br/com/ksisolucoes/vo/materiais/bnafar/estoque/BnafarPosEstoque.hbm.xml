<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.materiais.bnafar.estoque">
    <class
            name="BnafarPosEstoque"
            table="bnafar_pos_estoque"
    >

        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_bnafar_pos_estoque"
        >
            <generator class="sequence">
                <param name="sequence">seq_bnafar_pos_estoque</param>
            </generator>
        </id>
        <version column="version" name="version" type="long"/>

        <property
                column="cnes"
                name="cnes"
                type="java.lang.String"
                not-null="true"
        />

        <property
                column="tp_estabelecimento"
                name="tipoEstabelecimento"
                type="java.lang.String"
                not-null="true"
        />

        <property
                column="cd_origem"
                name="origem"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                column="dt_pos_estoque"
                name="dataPosEstoque"
                type="java.util.Date"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.entradas.estoque.Produto"
                column="cd_produto_origem"
                name="produtoOrigem"
                not-null="true"
        />

        <property
                column="grupo_estoque"
                name="grupoEstoque"
                type="java.lang.String"
                not-null="true"
        />

        <property
                column="dt_validade"
                name="dataValidade"
                type="java.util.Date"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.entradas.estoque.Fabricante"
                column="cd_fabricante"
                name="fabricante"
        />

        <property
                column="quantidade"
                name="quantidade"
                type="java.lang.Double"
                not-null="true"
        />

        <property
                column="flag_status_registro"
                name="statusRegistro"
                type="java.lang.Long"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.materiais.bnafar.estoque.BnafarPosEstoqueElo"
                column="cd_bnafar_pos_estoque_elo"
                name="bnafarPosEstoqueElo"

        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario"
                name="usuario"
                not-null="true"
        />

        <property
                column="dt_cadastro"
                name="dataCadastro"
                type="timestamp"
                not-null="true"
        />

        <property
                column="dt_ultimo_envio"
                name="dataUltimoEnvio"
                type="java.util.Date"
                not-null="true"
        />

    </class>
</hibernate-mapping>
