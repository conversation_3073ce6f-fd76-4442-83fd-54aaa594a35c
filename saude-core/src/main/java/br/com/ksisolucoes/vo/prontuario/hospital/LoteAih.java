package br.com.ksisolucoes.vo.prontuario.hospital;

import java.io.Serializable;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.hospital.base.BaseLoteAih;



public class LoteAih extends BaseLoteAih implements CodigoManager {
	private static final long serialVersionUID = 1L;
        public static final String PROP_DESCRICAO_STATUS = "descricaoStatus";
        
    public enum Status implements IEnum {
        LOTE_CRIADO(0L, Bundle.getStringApplication("rotulo_lote_criado")),
        ENVIADO_PARA_AUTORIZACAO(1L, Bundle.getStringApplication("rotulo_enviado_para_autorizacao")),
        AUTORIZADO(2L, Bundle.getStringApplication("rotulo_autorizado")),
        CANCELADO(3L, Bundle.getStringApplication("rotulo_cancelado")),
        ;

        private Long value;
        private String descricao;

        private Status(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Status valeuOf(Long value) {
            for (Status status : Status.values()) {
                if (status.value().equals(value)) {
                    return status;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }

/*[CONSTRUCTOR MARKER BEGIN]*/
	public LoteAih () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public LoteAih (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public LoteAih (
		java.lang.Long codigo,
		java.lang.Long status,
		java.util.Date dataApresentacao) {

		super (
			codigo,
			status,
			dataApresentacao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public String getDescricaoStatus(){
        Status status = Status.valeuOf(getStatus());
        if (status != null && status.descricao != null) {
            return status.descricao();
        }
        return "";
    }
}