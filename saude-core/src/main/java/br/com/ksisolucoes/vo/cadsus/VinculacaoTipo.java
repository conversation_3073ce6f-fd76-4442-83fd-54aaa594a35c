package br.com.ksisolucoes.vo.cadsus;

import java.io.Serializable;

import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.vo.cadsus.base.BaseVinculacaoTipo;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;



public class VinculacaoTipo extends BaseVinculacaoTipo implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public VinculacaoTipo () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public VinculacaoTipo (br.com.ksisolucoes.vo.cadsus.VinculacaoTipoPK id) {
		super(id);
	}

	/**
	 * Constructor for required fields
	 */
	public VinculacaoTipo (
		br.com.ksisolucoes.vo.cadsus.VinculacaoTipoPK id,
		java.lang.String descricao,
		java.lang.String tipoEsferaAdministracao) {

		super (
			id,
			descricao,
			tipoEsferaAdministracao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setId( (VinculacaoTipoPK)key );
    }

    public Serializable getCodigoManager() {
        return this.getId();
    }

    public String getDescricaoVO() {
        return this.getDescricao();
    }

    public String getIdentificador() {
        return this.getId().getCodigo();
    }

    public String getDescricaoFormatado() {
        return Util.getDescricaoFormatado( getId().getCodigo(), Coalesce.asString(this.getDescricao() ) );
    }
}