<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia">
    <class name="EscalaPlantao" table="escala_plantao">
        <id column="cd_escala_plantao" name="codigo" type="java.lang.Long" >
            <generator class="sequence">
                <param name="sequence">seq_escala_plantao</param>
            </generator>
        </id>

        <version column="version" name="version" type="long"/>

        <many-to-one column="cd_profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional" not-null="false" name="profissional" />

        <property column="dt_inicial" name="dataInicial" not-null="true" type="java.util.Date" />

        <property column="dt_final" name="dataFinal" not-null="true" type="java.util.Date" />

        <property column="ds_dia_inicio" name="descricaoDiaInicio" type="java.lang.String" />

        <property column="ds_dia_fim" name="descricaoDiaFim" type="java.lang.String" />

        <property column="hora_inicial" name="horaInicial" not-null="true" type="java.util.Date" />

        <property column="hora_final" name="horaFinal" not-null="true" type="java.util.Date" />

        <property column="dt_cadastro" name="dataCadastro" not-null="true" type="java.util.Date" />

        <many-to-one  column="cd_usuario" class="br.com.ksisolucoes.vo.controle.Usuario" name="usuario" not-null="true"/>

    </class>
</hibernate-mapping>
