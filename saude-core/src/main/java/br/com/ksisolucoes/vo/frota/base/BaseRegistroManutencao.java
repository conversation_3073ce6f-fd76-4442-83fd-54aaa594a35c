package br.com.ksisolucoes.vo.frota.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the registro_manutencao table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="registro_manutencao"
 */

public abstract class BaseRegistroManutencao extends BaseRootVO implements Serializable {

	public static String REF = "RegistroManutencao";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_DATA_MANUTENCAO = "dataManutencao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_DATA_USUARIO = "dataUsuario";
	public static final String PROP_DOCUMENTO = "documento";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_MODELO_DOCUMENTO = "modeloDocumento";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_FORNECEDOR = "fornecedor";
	public static final String PROP_SERIE = "serie";
	public static final String PROP_VALOR_TOTAL = "valorTotal";


	// constructors
	public BaseRegistroManutencao () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseRegistroManutencao (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseRegistroManutencao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Pessoa fornecedor,
		br.com.ksisolucoes.vo.financeiro.Serie serie,
		br.com.ksisolucoes.vo.frota.ModeloDocumento modeloDocumento,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		java.lang.String documento,
		java.util.Date dataUsuario,
		java.util.Date dataCadastro,
		java.lang.Double valorTotal,
		java.util.Date dataManutencao) {

		this.setCodigo(codigo);
		this.setFornecedor(fornecedor);
		this.setSerie(serie);
		this.setModeloDocumento(modeloDocumento);
		this.setUsuario(usuario);
		this.setEmpresa(empresa);
		this.setDocumento(documento);
		this.setDataUsuario(dataUsuario);
		this.setDataCadastro(dataCadastro);
		this.setValorTotal(valorTotal);
		this.setDataManutencao(dataManutencao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String documento;
	private java.util.Date dataUsuario;
	private java.util.Date dataCadastro;
	private java.lang.Double valorTotal;
	private java.lang.String observacao;
	private java.util.Date dataManutencao;

	// many to one
	private br.com.ksisolucoes.vo.basico.Pessoa fornecedor;
	private br.com.ksisolucoes.vo.financeiro.Serie serie;
	private br.com.ksisolucoes.vo.frota.ModeloDocumento modeloDocumento;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.basico.Empresa empresa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  column="cd_manutencao"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cod_documento
	 */
	public java.lang.String getDocumento () {
		return getPropertyValue(this, documento, PROP_DOCUMENTO); 
	}

	/**
	 * Set the value related to the column: cod_documento
	 * @param documento the cod_documento value
	 */
	public void setDocumento (java.lang.String documento) {
//        java.lang.String documentoOld = this.documento;
		this.documento = documento;
//        this.getPropertyChangeSupport().firePropertyChange ("documento", documentoOld, documento);
	}



	/**
	 * Return the value associated with the column: dt_usuario
	 */
	public java.util.Date getDataUsuario () {
		return getPropertyValue(this, dataUsuario, PROP_DATA_USUARIO); 
	}

	/**
	 * Set the value related to the column: dt_usuario
	 * @param dataUsuario the dt_usuario value
	 */
	public void setDataUsuario (java.util.Date dataUsuario) {
//        java.util.Date dataUsuarioOld = this.dataUsuario;
		this.dataUsuario = dataUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsuario", dataUsuarioOld, dataUsuario);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: vl_total
	 */
	public java.lang.Double getValorTotal () {
		return getPropertyValue(this, valorTotal, PROP_VALOR_TOTAL); 
	}

	/**
	 * Set the value related to the column: vl_total
	 * @param valorTotal the vl_total value
	 */
	public void setValorTotal (java.lang.Double valorTotal) {
//        java.lang.Double valorTotalOld = this.valorTotal;
		this.valorTotal = valorTotal;
//        this.getPropertyChangeSupport().firePropertyChange ("valorTotal", valorTotalOld, valorTotal);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: dt_manutencao
	 */
	public java.util.Date getDataManutencao () {
		return getPropertyValue(this, dataManutencao, PROP_DATA_MANUTENCAO); 
	}

	/**
	 * Set the value related to the column: dt_manutencao
	 * @param dataManutencao the dt_manutencao value
	 */
	public void setDataManutencao (java.util.Date dataManutencao) {
//        java.util.Date dataManutencaoOld = this.dataManutencao;
		this.dataManutencao = dataManutencao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataManutencao", dataManutencaoOld, dataManutencao);
	}



	/**
	 * Return the value associated with the column: cod_pessoa
	 */
	public br.com.ksisolucoes.vo.basico.Pessoa getFornecedor () {
		return getPropertyValue(this, fornecedor, PROP_FORNECEDOR); 
	}

	/**
	 * Set the value related to the column: cod_pessoa
	 * @param fornecedor the cod_pessoa value
	 */
	public void setFornecedor (br.com.ksisolucoes.vo.basico.Pessoa fornecedor) {
//        br.com.ksisolucoes.vo.basico.Pessoa fornecedorOld = this.fornecedor;
		this.fornecedor = fornecedor;
//        this.getPropertyChangeSupport().firePropertyChange ("fornecedor", fornecedorOld, fornecedor);
	}



	/**
	 * Return the value associated with the column: serie
	 */
	public br.com.ksisolucoes.vo.financeiro.Serie getSerie () {
		return getPropertyValue(this, serie, PROP_SERIE); 
	}

	/**
	 * Set the value related to the column: serie
	 * @param serie the serie value
	 */
	public void setSerie (br.com.ksisolucoes.vo.financeiro.Serie serie) {
//        br.com.ksisolucoes.vo.financeiro.Serie serieOld = this.serie;
		this.serie = serie;
//        this.getPropertyChangeSupport().firePropertyChange ("serie", serieOld, serie);
	}



	/**
	 * Return the value associated with the column: cd_modelo_documento
	 */
	public br.com.ksisolucoes.vo.frota.ModeloDocumento getModeloDocumento () {
		return getPropertyValue(this, modeloDocumento, PROP_MODELO_DOCUMENTO); 
	}

	/**
	 * Set the value related to the column: cd_modelo_documento
	 * @param modeloDocumento the cd_modelo_documento value
	 */
	public void setModeloDocumento (br.com.ksisolucoes.vo.frota.ModeloDocumento modeloDocumento) {
//        br.com.ksisolucoes.vo.frota.ModeloDocumento modeloDocumentoOld = this.modeloDocumento;
		this.modeloDocumento = modeloDocumento;
//        this.getPropertyChangeSupport().firePropertyChange ("modeloDocumento", modeloDocumentoOld, modeloDocumento);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.frota.RegistroManutencao)) return false;
		else {
			br.com.ksisolucoes.vo.frota.RegistroManutencao registroManutencao = (br.com.ksisolucoes.vo.frota.RegistroManutencao) obj;
			if (null == this.getCodigo() || null == registroManutencao.getCodigo()) return false;
			else return (this.getCodigo().equals(registroManutencao.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}