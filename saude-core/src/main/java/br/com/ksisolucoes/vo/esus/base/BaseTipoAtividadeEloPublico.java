package br.com.ksisolucoes.vo.esus.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the tipo_atividade_elo_publico table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="tipo_atividade_elo_publico"
 */

public abstract class BaseTipoAtividadeEloPublico extends BaseRootVO implements Serializable {

	public static String REF = "TipoAtividadeEloPublico";
	public static final String PROP_TIPO_ATIVIDADE_PUBLICO = "tipoAtividadePublico";
	public static final String PROP_VERSION_ALL = "versionAll";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TIPO_ATIVIDADE_GRUPO = "tipoAtividadeGrupo";


	// constructors
	public BaseTipoAtividadeEloPublico () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseTipoAtividadeEloPublico (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long versionAll;

	// many to one
	private br.com.ksisolucoes.vo.esus.TipoAtividadePublico tipoAtividadePublico;
	private br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupo tipoAtividadeGrupo;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_tipo_elo_publico"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: version_all
	 */
	public java.lang.Long getVersionAll () {
		return getPropertyValue(this, versionAll, PROP_VERSION_ALL); 
	}

	/**
	 * Set the value related to the column: version_all
	 * @param versionAll the version_all value
	 */
	public void setVersionAll (java.lang.Long versionAll) {
//        java.lang.Long versionAllOld = this.versionAll;
		this.versionAll = versionAll;
//        this.getPropertyChangeSupport().firePropertyChange ("versionAll", versionAllOld, versionAll);
	}



	/**
	 * Return the value associated with the column: cd_publico_alvo
	 */
	public br.com.ksisolucoes.vo.esus.TipoAtividadePublico getTipoAtividadePublico () {
		return getPropertyValue(this, tipoAtividadePublico, PROP_TIPO_ATIVIDADE_PUBLICO); 
	}

	/**
	 * Set the value related to the column: cd_publico_alvo
	 * @param tipoAtividadePublico the cd_publico_alvo value
	 */
	public void setTipoAtividadePublico (br.com.ksisolucoes.vo.esus.TipoAtividadePublico tipoAtividadePublico) {
//        br.com.ksisolucoes.vo.esus.TipoAtividadePublico tipoAtividadePublicoOld = this.tipoAtividadePublico;
		this.tipoAtividadePublico = tipoAtividadePublico;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAtividadePublico", tipoAtividadePublicoOld, tipoAtividadePublico);
	}



	/**
	 * Return the value associated with the column: cd_tp_atv_grupo
	 */
	public br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupo getTipoAtividadeGrupo () {
		return getPropertyValue(this, tipoAtividadeGrupo, PROP_TIPO_ATIVIDADE_GRUPO); 
	}

	/**
	 * Set the value related to the column: cd_tp_atv_grupo
	 * @param tipoAtividadeGrupo the cd_tp_atv_grupo value
	 */
	public void setTipoAtividadeGrupo (br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupo tipoAtividadeGrupo) {
//        br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupo tipoAtividadeGrupoOld = this.tipoAtividadeGrupo;
		this.tipoAtividadeGrupo = tipoAtividadeGrupo;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAtividadeGrupo", tipoAtividadeGrupoOld, tipoAtividadeGrupo);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.esus.TipoAtividadeEloPublico)) return false;
		else {
			br.com.ksisolucoes.vo.esus.TipoAtividadeEloPublico tipoAtividadeEloPublico = (br.com.ksisolucoes.vo.esus.TipoAtividadeEloPublico) obj;
			if (null == this.getCodigo() || null == tipoAtividadeEloPublico.getCodigo()) return false;
			else return (this.getCodigo().equals(tipoAtividadeEloPublico.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}