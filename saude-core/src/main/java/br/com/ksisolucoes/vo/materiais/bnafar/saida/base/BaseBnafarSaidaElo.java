package br.com.ksisolucoes.vo.materiais.bnafar.saida.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the bnafar_saida_elo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="bnafar_saida_elo"
 */

public abstract class BaseBnafarSaidaElo extends BaseRootVO implements Serializable {

	public static String REF = "BnafarSaidaElo";
	public static final String PROP_STATUS_RETORNO = "statusRetorno";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_MENSAGEM_RETORNO = "mensagemRetorno";
	public static final String PROP_BNAFAR_SAIDA = "bnafarSaida";
	public static final String PROP_BNAFAR_SAIDA_INTEGRACAO = "bnafarSaidaIntegracao";


	// constructors
	public BaseBnafarSaidaElo () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseBnafarSaidaElo (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseBnafarSaidaElo (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.materiais.bnafar.saida.BnafarSaida bnafarSaida,
		br.com.ksisolucoes.vo.materiais.bnafar.saida.BnafarSaidaIntegracao bnafarSaidaIntegracao) {

		this.setCodigo(codigo);
		this.setBnafarSaida(bnafarSaida);
		this.setBnafarSaidaIntegracao(bnafarSaidaIntegracao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String statusRetorno;
	private java.lang.String mensagemRetorno;

	// many to one
	private br.com.ksisolucoes.vo.materiais.bnafar.saida.BnafarSaida bnafarSaida;
	private br.com.ksisolucoes.vo.materiais.bnafar.saida.BnafarSaidaIntegracao bnafarSaidaIntegracao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_bnafar_saida_elo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: status_retorno
	 */
	public java.lang.String getStatusRetorno () {
		return getPropertyValue(this, statusRetorno, PROP_STATUS_RETORNO); 
	}

	/**
	 * Set the value related to the column: status_retorno
	 * @param statusRetorno the status_retorno value
	 */
	public void setStatusRetorno (java.lang.String statusRetorno) {
//        java.lang.String statusRetornoOld = this.statusRetorno;
		this.statusRetorno = statusRetorno;
//        this.getPropertyChangeSupport().firePropertyChange ("statusRetorno", statusRetornoOld, statusRetorno);
	}



	/**
	 * Return the value associated with the column: mensagem_retorno
	 */
	public java.lang.String getMensagemRetorno () {
		return getPropertyValue(this, mensagemRetorno, PROP_MENSAGEM_RETORNO); 
	}

	/**
	 * Set the value related to the column: mensagem_retorno
	 * @param mensagemRetorno the mensagem_retorno value
	 */
	public void setMensagemRetorno (java.lang.String mensagemRetorno) {
//        java.lang.String mensagemRetornoOld = this.mensagemRetorno;
		this.mensagemRetorno = mensagemRetorno;
//        this.getPropertyChangeSupport().firePropertyChange ("mensagemRetorno", mensagemRetornoOld, mensagemRetorno);
	}



	/**
	 * Return the value associated with the column: cd_bnafar_saida
	 */
	public br.com.ksisolucoes.vo.materiais.bnafar.saida.BnafarSaida getBnafarSaida () {
		return getPropertyValue(this, bnafarSaida, PROP_BNAFAR_SAIDA); 
	}

	/**
	 * Set the value related to the column: cd_bnafar_saida
	 * @param bnafarSaida the cd_bnafar_saida value
	 */
	public void setBnafarSaida (br.com.ksisolucoes.vo.materiais.bnafar.saida.BnafarSaida bnafarSaida) {
//        br.com.ksisolucoes.vo.materiais.bnafar.saida.BnafarSaida bnafarSaidaOld = this.bnafarSaida;
		this.bnafarSaida = bnafarSaida;
//        this.getPropertyChangeSupport().firePropertyChange ("bnafarSaida", bnafarSaidaOld, bnafarSaida);
	}



	/**
	 * Return the value associated with the column: cd_bnafar_saida_integracao
	 */
	public br.com.ksisolucoes.vo.materiais.bnafar.saida.BnafarSaidaIntegracao getBnafarSaidaIntegracao () {
		return getPropertyValue(this, bnafarSaidaIntegracao, PROP_BNAFAR_SAIDA_INTEGRACAO); 
	}

	/**
	 * Set the value related to the column: cd_bnafar_saida_integracao
	 * @param bnafarSaidaIntegracao the cd_bnafar_saida_integracao value
	 */
	public void setBnafarSaidaIntegracao (br.com.ksisolucoes.vo.materiais.bnafar.saida.BnafarSaidaIntegracao bnafarSaidaIntegracao) {
//        br.com.ksisolucoes.vo.materiais.bnafar.saida.BnafarSaidaIntegracao bnafarSaidaIntegracaoOld = this.bnafarSaidaIntegracao;
		this.bnafarSaidaIntegracao = bnafarSaidaIntegracao;
//        this.getPropertyChangeSupport().firePropertyChange ("bnafarSaidaIntegracao", bnafarSaidaIntegracaoOld, bnafarSaidaIntegracao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.materiais.bnafar.saida.BnafarSaidaElo)) return false;
		else {
			br.com.ksisolucoes.vo.materiais.bnafar.saida.BnafarSaidaElo bnafarSaidaElo = (br.com.ksisolucoes.vo.materiais.bnafar.saida.BnafarSaidaElo) obj;
			if (null == this.getCodigo() || null == bnafarSaidaElo.getCodigo()) return false;
			else return (this.getCodigo().equals(bnafarSaidaElo.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}