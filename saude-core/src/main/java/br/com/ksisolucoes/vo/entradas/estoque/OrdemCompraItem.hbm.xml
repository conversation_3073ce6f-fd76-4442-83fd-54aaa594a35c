<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.entradas.estoque"  >
	<class name="OrdemCompraItem" table="ordem_compra_item">
		<id
			name="codigo"
			type="java.lang.Long"
			column="cd_oc_item"
		>
                <generator class="assigned" />
		</id> <version column="version" name="version" type="long" />
                
                <many-to-one
                        name="produto"
                        class="br.com.ksisolucoes.vo.entradas.estoque.Produto"
                        column="cod_pro"   
                        not-null="true"
                />
                
                <property
			name="quantidadeCompra"
			column="qtd_compra" 
			type="java.math.BigDecimal"
                        not-null="false"
		/>
		
		           <property
			name="quantidadeCompraOriginal"
			column="qtd_compra_original" 
			type="java.math.BigDecimal"
                        not-null="false"
		/>
		
		<property
			name="quantidadeCompraPedidoTransferencia"
			column="qtd_compra_ped_transf" 
			type="java.math.BigDecimal"
                        not-null="false"
		/>
                
                <property
			name="precoUnitario"
			column="preco_unitario" 
			type="java.math.BigDecimal"
                        not-null="true"
		/>
                
                <property
			name="status"
			column="status"
			type="java.lang.Long"
			not-null="true"
				/>

		<property
				name="modelo"
				column="ds_modelo"
				type="java.lang.String"
				not-null="false"
		/>
                
                <property
			name="quantidadeRecebida"
			column="qtd_recebida" 
			type="java.math.BigDecimal"
                        not-null="false"
		/>
                
                <property
			name="numeroItemPregao"
			column="nr_item_pregao"
			type="java.lang.String"
			not-null="false"
                        length="10"
		/>
		
		 <property
			name="descricaoFabricante"
			column="ds_fabricante"
			type="java.lang.String"
			not-null="false"
		/>

		<property
				name="quantidadePregao"
				column="qtd_pregao"
				type="java.math.BigDecimal"
				not-null="false"
		/>
                
                <many-to-one
                        name="usuario"
                        class="br.com.ksisolucoes.vo.controle.Usuario"
                        column="cd_usuario"
                />
                
                <property
			name="dataUsuario"
			column="dt_usuario" 
			type="timestamp"
                        not-null="true"
		/>
                
                <property
			name="dataCancelamento"
			column="dt_cancelamento" 
			type="timestamp"
                        not-null="false"
		/>
                
                <many-to-one
                        name="usuarioCancelamento"
                        class="br.com.ksisolucoes.vo.controle.Usuario"
                        column="cd_usu_can"
                />
                
                <property
			name="quantidadeCancelada"
			column="qtd_cancelada" 
			type="java.math.BigDecimal"
                        not-null="false"
		/>
                
                <many-to-one
                        name="ordemCompra"
                        class="br.com.ksisolucoes.vo.entradas.estoque.OrdemCompra"
                        column="cd_ordem_compra"   
                        not-null="true"
                />
                
                 <many-to-one
                        name="fabricante"
                        class="br.com.ksisolucoes.vo.entradas.estoque.Fabricante"
                        column="cd_fabricante"   
                        not-null="false"
                />
	</class>
</hibernate-mapping>