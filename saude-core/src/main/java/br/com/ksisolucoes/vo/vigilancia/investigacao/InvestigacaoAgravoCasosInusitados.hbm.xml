<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.investigacao">

    <class name="InvestigacaoAgravoCasosInusitados" table="investigacao_agr_casos_inusitados">

        <id name="codigo"
            type="java.lang.Long"
            column="codigo" >
            <generator class="sequence">
                <param name="sequence">seq_investigacao_agr_casos_inusitados</param>
            </generator>
        </id>

        <version column="version" name="version" type="long"/>

        <property
                name="flagInformacoesComplementares"
                column="flag_informacoes_complementares"
                type="java.lang.String"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo"
                name="registroAgravo" not-null="true">
            <column name="cd_registro_agravo"/>
        </many-to-one>

        <property
                name="dataInvestigacao"
                column="dt_investigacao"
                type="java.util.Date"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"
                name="ocupacaoCbo" not-null="false">
            <column name="ocupacao_cbo"/>
        </many-to-one>



        <!-- CASO AUTOCTONE -->
        <property
                name="casoAutoctone"
                column="caso_autoctone"
                type="java.lang.Long"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Cidade"
                name="cidadeLocalInfeccao"
                not-null="false">
            <column name="cd_cidade_infeccao"/>
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Pais"
                name="paisLocalInfeccao"
                not-null="false">
            <column name="cd_pais_infeccao"/>
        </many-to-one>

        <property
                name="distritoLocalInfeccao"
                column="str_distrito_infeccao"
                type="java.lang.String"
                length="200"
        />

        <property
                name="bairroLocalInfeccao"
                column="str_bairro_infeccao"
                type="java.lang.String"
                length="200"
        />


        <!-- CONCLUSAO -->
        <property
                name="classificacaoFinal"
                column="classificacao_final"
                type="java.lang.Long"
        />

        <property
                name="criterioConfirmacaoDescarte"
                column="criterio_confirmacao_descarte"
                type="java.lang.Long"
        />

        <property
                name="doencaRelacionadaTrabalho"
                column="doenca_relacionada_trabalho"
                type="java.lang.Long"
        />

        <property
                name="evolucaoCaso"
                column="evolucao_caso"
                type="java.lang.Long"
        />

        <property
                name="dataObito"
                column="dt_obito"
                type="java.util.Date"
        />


        <!-- OBS -->
        <property
            name="observacao"
            column="observacao"
            type="java.lang.String"
            length="5000"
        />

        <!-- Encerramento -->
        <property
                name="dataEncerramento"
                column="dt_encerramento"
                type="java.util.Date"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                name="usuarioEncerramento" not-null="false">
            <column name="cd_usuario_encerramento"/>
        </many-to-one>


    </class>
</hibernate-mapping>