<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.comunicacao"  >
	<class name="Noticia" table="noticia">
		<id
			column="cd_noticia"
			name="codigo"
			type="java.lang.Long"
		>
			<generator class="assigned" />
		</id> <version column="version" name="version" type="long" />

		<property
			column="titulo_noticia"
			name="titulo"
			not-null="true"
			length="100"
			type="java.lang.String"
		 />
		
         <property
			column="ds_noticia"
			name="descricao"
			not-null="true"
			length="1024"
			type="java.lang.String"
		 />

		<many-to-one
			class="br.com.ksisolucoes.vo.controle.Usuario"
			name="usuario"
			not-null="true"
		>
			<column name="cd_usuario" />
		</many-to-one>
                
        <property
			column="dt_cadastro"
			name="dataCadastro"
			not-null="true"
			type="java.util.Date"
		 />
		 
		<property
			column="dt_fim_exibicao"
			name="dataFimExibicao"
			not-null="true"
			type="java.util.Date"
		 />

		<property
				column="corpo_noticia"
				name="corpo"
				not-null="true"
				type="java.lang.String"
		/>
	</class>
</hibernate-mapping>
