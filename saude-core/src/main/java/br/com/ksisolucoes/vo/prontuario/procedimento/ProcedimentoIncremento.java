package br.com.ksisolucoes.vo.prontuario.procedimento;

import java.io.Serializable;

import br.com.ksisolucoes.associacao.annotations.ColumnNameSIGTAP;
import br.com.ksisolucoes.associacao.annotations.IdNameSIGTAP;
import br.com.ksisolucoes.associacao.annotations.TableNameSIGTAP;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.procedimento.base.BaseProcedimentoIncremento;


@TableNameSIGTAP(value="rl_procedimento_incremento",dependencias={ProcedimentoCompetencia.class,ProcedimentoHabilitacaoCadastro.class})
public class ProcedimentoIncremento extends BaseProcedimentoIncremento implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ProcedimentoIncremento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ProcedimentoIncremento (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoIncrementoPK id) {
		super(id);
	}

	/**
	 * Constructor for required fields
	 */
	public ProcedimentoIncremento (
		br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoIncrementoPK id,
		java.lang.Double valorPercentualServicoHospitalar,
		java.lang.Double valorPercentualServicoAmbulatorial,
		java.lang.Double valorPercentualServicoProfissional) {

		super (
			id,
			valorPercentualServicoHospitalar,
			valorPercentualServicoAmbulatorial,
			valorPercentualServicoProfissional);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setId( (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoIncrementoPK)key );
    }

    public Serializable getCodigoManager() {
        return this.getId();
    }

    @IdNameSIGTAP
    @Override
    public ProcedimentoIncrementoPK getId() {
        return super.getId();
    }

    @ColumnNameSIGTAP("VL_PERCENTUAL_SA")
    @Override
    public Double getValorPercentualServicoAmbulatorial() {
        return super.getValorPercentualServicoAmbulatorial();
    }

    @ColumnNameSIGTAP("VL_PERCENTUAL_SH")
    @Override
    public Double getValorPercentualServicoHospitalar() {
        return super.getValorPercentualServicoHospitalar();
    }

    @ColumnNameSIGTAP("VL_PERCENTUAL_SP")
    @Override
    public Double getValorPercentualServicoProfissional() {
        return super.getValorPercentualServicoProfissional();
    }

    @Override
    public void setId(ProcedimentoIncrementoPK id) {
        super.setId(id);
    }

    @Override
    public void setValorPercentualServicoAmbulatorial(Double valorPercentualServicoAmbulatorial) {
        super.setValorPercentualServicoAmbulatorial(valorPercentualServicoAmbulatorial);
    }

    @Override
    public void setValorPercentualServicoHospitalar(Double valorPercentualServicoHospitalar) {
        super.setValorPercentualServicoHospitalar(valorPercentualServicoHospitalar);
    }

    @Override
    public void setValorPercentualServicoProfissional(Double valorPercentualServicoProfissional) {
        super.setValorPercentualServicoProfissional(valorPercentualServicoProfissional);
    }


}