package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseRequisicaoHIV;



public class RequisicaoHIV extends BaseRequisicaoHIV implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequisicaoHIV () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequisicaoHIV (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequisicaoHIV (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicao,
		java.lang.Long homoBissexual,
		java.lang.Long hemofilico,
		java.lang.Long acidentePerfurocortante,
		java.lang.Long udi,
		java.lang.Long heterossexual,
		java.lang.Long receptadorSangue,
		java.lang.Long gestante,
		java.lang.Long outros,
		java.lang.Long primeiraAmostra,
		java.lang.Long segundaAmostra,
		java.lang.Long confirmatorio) {

		super (
			codigo,
			exameRequisicao,
			homoBissexual,
			hemofilico,
			acidentePerfurocortante,
			udi,
			heterossexual,
			receptadorSangue,
			gestante,
			outros,
			primeiraAmostra,
			segundaAmostra,
			confirmatorio);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}