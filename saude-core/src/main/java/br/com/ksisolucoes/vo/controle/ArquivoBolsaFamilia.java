package br.com.ksisolucoes.vo.controle;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.controle.base.BaseArquivoBolsaFamilia;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;


public class ArquivoBolsaFamilia extends BaseArquivoBolsaFamilia implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ArquivoBolsaFamilia () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ArquivoBolsaFamilia (java.lang.Long codigo) {
		super(codigo);
	}

/*[CONSTRUCTOR MARKER END]*/

	public void setCodigoManager(Serializable key) {
		this.setCodigo( (java.lang.Long)key );
	}

	public Serializable getCodigoManager() {
		return this.getCodigo();
	}

	public enum Status implements IEnum {

		AGUARDANDO(0L, Bundle.getStringApplication("rotulo_aguardando")),
		PROCESSANDO(1L, Bundle.getStringApplication("rotulo_processando")),
		PROCESSADO(2L, Bundle.getStringApplication("rotulo_processado")),
		ERRO(3L, Bundle.getStringApplication("rotulo_erro")),
		CANCELADO(4L, Bundle.getStringApplication("rotulo_cancelado"));


		private Long value;
		private String descricao;

		private Status(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static ArquivoQware.Status valeuOf(Long value) {
			for (ArquivoQware.Status status : ArquivoQware.Status.values()) {
				if (status.value().equals(value)) {
					return status;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}

		@Override
		public String toString() {
			return this.descricao;
		}
	}
}