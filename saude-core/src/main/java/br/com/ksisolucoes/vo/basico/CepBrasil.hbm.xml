<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.basico"  >
    <class name="CepBrasil" table="cep_brasil">
        <id
            column="cd_cep"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 

        <version column="version" name="version" type="long" />

        <property
            column="cep"
            name="cep"
            not-null="true"
            type="java.lang.String"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.basico.Cidade"
            name="cidade"
            not-null="true"
        >
            <column name="cod_cid" />
        </many-to-one>
        
        <many-to-one
            class="br.com.ksisolucoes.vo.basico.Estado"
            name="estado"
            not-null="true"
        >
            <column name="cod_est" />
        </many-to-one>
        <property
            name="versionAll"
            column="version_all"
            type="java.lang.Long"
        />
    </class>
</hibernate-mapping>