package br.com.ksisolucoes.vo.consorcio.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the consorcio_grupo_procedimento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="consorcio_grupo_procedimento"
 */

public abstract class BaseConsorcioGrupoProcedimento extends BaseRootVO implements Serializable {

	public static String REF = "ConsorcioGrupoProcedimento";
	public static final String PROP_CONSORCIO_PROCEDIMENTO = "consorcioProcedimento";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_CONSORCIO_GRUPO = "consorcioGrupo";


	// constructors
	public BaseConsorcioGrupoProcedimento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseConsorcioGrupoProcedimento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// many to one
	private br.com.ksisolucoes.vo.consorcio.ConsorcioGrupo consorcioGrupo;
	private br.com.ksisolucoes.vo.consorcio.ConsorcioProcedimento consorcioProcedimento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_consorcio_grupo_procedimento"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_consorcio_grupo
	 */
	public br.com.ksisolucoes.vo.consorcio.ConsorcioGrupo getConsorcioGrupo () {
		return getPropertyValue(this, consorcioGrupo, PROP_CONSORCIO_GRUPO); 
	}

	/**
	 * Set the value related to the column: cd_consorcio_grupo
	 * @param consorcioGrupo the cd_consorcio_grupo value
	 */
	public void setConsorcioGrupo (br.com.ksisolucoes.vo.consorcio.ConsorcioGrupo consorcioGrupo) {
//        br.com.ksisolucoes.vo.consorcio.ConsorcioGrupo consorcioGrupoOld = this.consorcioGrupo;
		this.consorcioGrupo = consorcioGrupo;
//        this.getPropertyChangeSupport().firePropertyChange ("consorcioGrupo", consorcioGrupoOld, consorcioGrupo);
	}



	/**
	 * Return the value associated with the column: cd_consorcio_procedimento
	 */
	public br.com.ksisolucoes.vo.consorcio.ConsorcioProcedimento getConsorcioProcedimento () {
		return getPropertyValue(this, consorcioProcedimento, PROP_CONSORCIO_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_consorcio_procedimento
	 * @param consorcioProcedimento the cd_consorcio_procedimento value
	 */
	public void setConsorcioProcedimento (br.com.ksisolucoes.vo.consorcio.ConsorcioProcedimento consorcioProcedimento) {
//        br.com.ksisolucoes.vo.consorcio.ConsorcioProcedimento consorcioProcedimentoOld = this.consorcioProcedimento;
		this.consorcioProcedimento = consorcioProcedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("consorcioProcedimento", consorcioProcedimentoOld, consorcioProcedimento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.consorcio.ConsorcioGrupoProcedimento)) return false;
		else {
			br.com.ksisolucoes.vo.consorcio.ConsorcioGrupoProcedimento consorcioGrupoProcedimento = (br.com.ksisolucoes.vo.consorcio.ConsorcioGrupoProcedimento) obj;
			if (null == this.getCodigo() || null == consorcioGrupoProcedimento.getCodigo()) return false;
			else return (this.getCodigo().equals(consorcioGrupoProcedimento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}