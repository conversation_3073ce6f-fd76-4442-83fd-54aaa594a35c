package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the encaminhamento_atendimento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="encaminhamento_atendimento"
 */

public abstract class BaseEncaminhamentoAtendimento extends BaseRootVO implements Serializable {

	public static String REF = "EncaminhamentoAtendimento";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_ENCAMINHAMENTO = "encaminhamento";
	public static final String PROP_ATENDIMENTO = "atendimento";


	// constructors
	public BaseEncaminhamentoAtendimento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEncaminhamentoAtendimento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento encaminhamento;
	private br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_enc_atendimento"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_encaminhamento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento getEncaminhamento () {
		return getPropertyValue(this, encaminhamento, PROP_ENCAMINHAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_encaminhamento
	 * @param encaminhamento the cd_encaminhamento value
	 */
	public void setEncaminhamento (br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento encaminhamento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento encaminhamentoOld = this.encaminhamento;
		this.encaminhamento = encaminhamento;
//        this.getPropertyChangeSupport().firePropertyChange ("encaminhamento", encaminhamentoOld, encaminhamento);
	}



	/**
	 * Return the value associated with the column: nr_atendimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Atendimento getAtendimento () {
		return getPropertyValue(this, atendimento, PROP_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: nr_atendimento
	 * @param atendimento the nr_atendimento value
	 */
	public void setAtendimento (br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoOld = this.atendimento;
		this.atendimento = atendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimento", atendimentoOld, atendimento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.EncaminhamentoAtendimento)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.EncaminhamentoAtendimento encaminhamentoAtendimento = (br.com.ksisolucoes.vo.prontuario.basico.EncaminhamentoAtendimento) obj;
			if (null == this.getCodigo() || null == encaminhamentoAtendimento.getCodigo()) return false;
			else return (this.getCodigo().equals(encaminhamentoAtendimento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}