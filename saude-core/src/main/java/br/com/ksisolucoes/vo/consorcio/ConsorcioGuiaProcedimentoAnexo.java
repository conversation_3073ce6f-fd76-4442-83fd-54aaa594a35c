package br.com.ksisolucoes.vo.consorcio;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.consorcio.base.BaseConsorcioGuiaProcedimento;
import br.com.ksisolucoes.vo.consorcio.base.BaseConsorcioGuiaProcedimentoAnexo;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class ConsorcioGuiaProcedimentoAnexo extends BaseConsorcioGuiaProcedimentoAnexo implements CodigoManager {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public ConsorcioGuiaProcedimentoAnexo() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public ConsorcioGuiaProcedimentoAnexo(Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public ConsorcioGuiaProcedimentoAnexo(
            Long codigo,
            br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento consorcioGuiaProcedimento,
            br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo gerenciadorArquivo) {

        super(
                codigo,
                consorcioGuiaProcedimento,
                gerenciadorArquivo
        );
    }

    /**
     * Constructor for required fields
     */
    public ConsorcioGuiaProcedimentoAnexo(
            br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento consorcioGuiaProcedimento,
            br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo gerenciadorArquivo) {

        super(
                consorcioGuiaProcedimento,
                gerenciadorArquivo
        );
    }

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}