package br.com.ksisolucoes.vo.prontuario;

import java.io.Serializable;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.base.BaseAtividadeTerapeutica;



public class AtividadeTerapeutica extends BaseAtividadeTerapeutica implements CodigoManager {
	private static final long serialVersionUID = 1L;
        public static final String PROP_DIA_SEMANA_DESCRICAO = "diaSemanaDescricao";

/*[CONSTRUCTOR MARKER BEGIN]*/
	public AtividadeTerapeutica () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public AtividadeTerapeutica (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public AtividadeTerapeutica (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.FichaAcolhimento fichaAcolhimento,
		java.lang.Long diaSemana,
		java.lang.String descricao) {

		super (
			codigo,
			fichaAcolhimento,
			diaSemana,
			descricao);
	}

/*[CONSTRUCTOR MARKER END]*/
    public static enum DiaSemana implements IEnum<DiaSemana> {

        DOMINGO(0L, Bundle.getStringApplication("rotulo_domingo")),
        SEGUNDA(1L, Bundle.getStringApplication("rotulo_segunda")),
        TERCA(2L, Bundle.getStringApplication("rotulo_terca")),
        QUARTA(3L, Bundle.getStringApplication("rotulo_quarta")),
        QUINTA(4L, Bundle.getStringApplication("rotulo_quinta")),
        SEXTA(5L, Bundle.getStringApplication("rotulo_sexta")),
        SABADO(6L, Bundle.getStringApplication("rotulo_sabado"));
        private Long value;
        private String descricao;

        private DiaSemana(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static DiaSemana valeuOf(Long value) {
            for (DiaSemana diaSemana : DiaSemana.values()) {
                if (diaSemana.value().equals(value)) {
                    return diaSemana;
                }
            }
            return null;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }    
        
        
    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public String getDiaSemanaDescricao(){
        DiaSemana diaSemana = DiaSemana.valeuOf(getDiaSemana());
        if (diaSemana != null) {
            return diaSemana.descricao();
        }
        return "";
    }
}