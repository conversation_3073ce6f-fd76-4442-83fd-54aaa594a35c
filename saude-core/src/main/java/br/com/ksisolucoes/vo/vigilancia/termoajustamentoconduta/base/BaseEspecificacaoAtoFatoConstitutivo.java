package br.com.ksisolucoes.vo.vigilancia.termoajustamentoconduta.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the especificacao_ato_fato_constitutivo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="especificacao_ato_fato_constitutivo"
 */

public abstract class BaseEspecificacaoAtoFatoConstitutivo extends BaseRootVO implements Serializable {

	public static String REF = "EspecificacaoAtoFatoConstitutivo";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_LEGISLACAO = "legislacao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DESCRICAO_CONDUTA_AJUSTADA = "descricaoCondutaAjustada";
	public static final String PROP_TERMO_AJUSTAMENTO_CONDUTA = "termoAjustamentoConduta";


	// constructors
	public BaseEspecificacaoAtoFatoConstitutivo () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEspecificacaoAtoFatoConstitutivo (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseEspecificacaoAtoFatoConstitutivo (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.termoajustamentoconduta.TermoAjustamentoConduta termoAjustamentoConduta,
		java.lang.String descricaoCondutaAjustada) {

		this.setCodigo(codigo);
		this.setTermoAjustamentoConduta(termoAjustamentoConduta);
		this.setDescricaoCondutaAjustada(descricaoCondutaAjustada);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricaoCondutaAjustada;
	private java.lang.String observacao;
	private java.lang.String legislacao;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.termoajustamentoconduta.TermoAjustamentoConduta termoAjustamentoConduta;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_especificacao_ato_fato_constitutivo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: descricao_conduta_ajustada
	 */
	public java.lang.String getDescricaoCondutaAjustada () {
		return getPropertyValue(this, descricaoCondutaAjustada, PROP_DESCRICAO_CONDUTA_AJUSTADA); 
	}

	/**
	 * Set the value related to the column: descricao_conduta_ajustada
	 * @param descricaoCondutaAjustada the descricao_conduta_ajustada value
	 */
	public void setDescricaoCondutaAjustada (java.lang.String descricaoCondutaAjustada) {
//        java.lang.String descricaoCondutaAjustadaOld = this.descricaoCondutaAjustada;
		this.descricaoCondutaAjustada = descricaoCondutaAjustada;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoCondutaAjustada", descricaoCondutaAjustadaOld, descricaoCondutaAjustada);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: legislacao
	 */
	public java.lang.String getLegislacao () {
		return getPropertyValue(this, legislacao, PROP_LEGISLACAO); 
	}

	/**
	 * Set the value related to the column: legislacao
	 * @param legislacao the legislacao value
	 */
	public void setLegislacao (java.lang.String legislacao) {
//        java.lang.String legislacaoOld = this.legislacao;
		this.legislacao = legislacao;
//        this.getPropertyChangeSupport().firePropertyChange ("legislacao", legislacaoOld, legislacao);
	}



	/**
	 * Return the value associated with the column: cd_termo_ajustamento_conduta
	 */
	public br.com.ksisolucoes.vo.vigilancia.termoajustamentoconduta.TermoAjustamentoConduta getTermoAjustamentoConduta () {
		return getPropertyValue(this, termoAjustamentoConduta, PROP_TERMO_AJUSTAMENTO_CONDUTA); 
	}

	/**
	 * Set the value related to the column: cd_termo_ajustamento_conduta
	 * @param termoAjustamentoConduta the cd_termo_ajustamento_conduta value
	 */
	public void setTermoAjustamentoConduta (br.com.ksisolucoes.vo.vigilancia.termoajustamentoconduta.TermoAjustamentoConduta termoAjustamentoConduta) {
//        br.com.ksisolucoes.vo.vigilancia.termoajustamentoconduta.TermoAjustamentoConduta termoAjustamentoCondutaOld = this.termoAjustamentoConduta;
		this.termoAjustamentoConduta = termoAjustamentoConduta;
//        this.getPropertyChangeSupport().firePropertyChange ("termoAjustamentoConduta", termoAjustamentoCondutaOld, termoAjustamentoConduta);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.termoajustamentoconduta.EspecificacaoAtoFatoConstitutivo)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.termoajustamentoconduta.EspecificacaoAtoFatoConstitutivo especificacaoAtoFatoConstitutivo = (br.com.ksisolucoes.vo.vigilancia.termoajustamentoconduta.EspecificacaoAtoFatoConstitutivo) obj;
			if (null == this.getCodigo() || null == especificacaoAtoFatoConstitutivo.getCodigo()) return false;
			else return (this.getCodigo().equals(especificacaoAtoFatoConstitutivo.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}