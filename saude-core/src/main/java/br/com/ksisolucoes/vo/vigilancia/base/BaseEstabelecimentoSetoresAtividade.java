package br.com.ksisolucoes.vo.vigilancia.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.vigilancia.EstabelecimentoAtividade;

import java.io.Serializable;


/**
 * This is an object that contains data related to the estabelecimento_setores table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="estabelecimento_setores"
 */

public abstract class BaseEstabelecimentoSetoresAtividade extends BaseRootVO implements Serializable {

	public static String REF = "EstabelecimentoSetoresAtividade";
    public static final String PROP_CODIGO = "codigo";
	public static final String PROP_ESTABELECIMENTO_SETORES = "estabelecimentoSetores";
	public static final String PROP_ESTABELECIMENTO_ATIVIDADE = "estabelecimentoAtividade";


	// constructors
	public BaseEstabelecimentoSetoresAtividade() {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEstabelecimentoSetoresAtividade(Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

    protected void initialize() {
    }


    private int hashCode = Integer.MIN_VALUE;

    // primary key
    private Long codigo;

    /**
	 * Constructor for required fields
	 */
	public BaseEstabelecimentoSetoresAtividade(
            Long codigo,
            br.com.ksisolucoes.vo.vigilancia.EstabelecimentoSetores estabelecimentoSetores,
			br.com.ksisolucoes.vo.vigilancia.EstabelecimentoAtividade estabelecimentoAtividade) {

		this.setCodigo(codigo);
		this.setEstabelecimentoSetores(estabelecimentoSetores);
		this.setEstabelecimentoAtividade(estabelecimentoAtividade);
		initialize();
	}

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.EstabelecimentoSetores estabelecimentoSetores;
	private br.com.ksisolucoes.vo.vigilancia.EstabelecimentoAtividade estabelecimentoAtividade;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_estabelecimento_setores"
     */
	public Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}

	/**
	 * Return the value associated with the column: cd_estabelecimento_setores
	 */
	public br.com.ksisolucoes.vo.vigilancia.EstabelecimentoSetores getEstabelecimentoSetores () {
		return getPropertyValue(this, estabelecimentoSetores, PROP_ESTABELECIMENTO_SETORES);
	}

	/**
	 * Set the value related to the column: cd_estabelecimento_setores
	 * @param estabelecimentoSetores the cd_estabelecimento_setores value
	 */
	public void setEstabelecimentoSetores (br.com.ksisolucoes.vo.vigilancia.EstabelecimentoSetores estabelecimentoSetores) {
		this.estabelecimentoSetores = estabelecimentoSetores;
	}




	/**
	 * Return the value associated with the column: cd_atividade_estabelecimento
	 */
	public br.com.ksisolucoes.vo.vigilancia.EstabelecimentoAtividade getEstabelecimentoAtividade () {
		return getPropertyValue(this, estabelecimentoAtividade, PROP_ESTABELECIMENTO_ATIVIDADE);
	}

	/**
	 * Set the value related to the column: cd_estabelecimento_atividade
	 * @param estabelecimentoAtividade the cd_estabelecimento_atividade value
	 */
	public void setEstabelecimentoAtividade (EstabelecimentoAtividade estabelecimentoAtividade) {
		this.estabelecimentoAtividade = estabelecimentoAtividade;
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.EstabelecimentoSetoresAtividade)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.EstabelecimentoSetoresAtividade estabelecimentoSetoresAtividade = (br.com.ksisolucoes.vo.vigilancia.EstabelecimentoSetoresAtividade) obj;
			if (null == this.getCodigo() || null == estabelecimentoSetoresAtividade.getCodigo()) return false;
			else return (this.getCodigo().equals(estabelecimentoSetoresAtividade.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

}