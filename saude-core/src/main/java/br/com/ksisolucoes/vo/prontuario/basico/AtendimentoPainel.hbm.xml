<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico">
    <class name="AtendimentoPainel" table="atendimento_painel">

        <id name="codigo" column="cd_atendimento_painel" type="java.lang.Long" >
            <generator class="sequence">
                <param name="sequence">seq_atendimento</param>
            </generator>
        </id>

        <version column="version" name="version" type="long" />

        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
                     name="atendimento" not-null="true">
            <column name="nr_atendimento" />
        </many-to-one>

        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento"
                     name="tipoAtendimento" not-null="true">
            <column name="cd_tp_atendimento" />
        </many-to-one>

        <many-to-one
                name="profissional"
                class="br.com.ksisolucoes.vo.cadsus.Profissional"
        >
            <column name="cd_profissional"/>
        </many-to-one>

        <property name="dataChamada" type="timestamp">
            <column name="dt_chamada" not-null="true"/>
        </property>

    </class>
</hibernate-mapping>