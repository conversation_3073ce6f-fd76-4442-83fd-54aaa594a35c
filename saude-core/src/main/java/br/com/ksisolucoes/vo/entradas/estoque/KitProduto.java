package br.com.ksisolucoes.vo.entradas.estoque;

import java.io.Serializable;

import br.com.ksisolucoes.vo.entradas.estoque.base.BaseKitProduto;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class KitProduto extends BaseKitProduto implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public KitProduto () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public KitProduto (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public KitProduto (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.entradas.estoque.Produto produtoPrincipal,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataCadastro) {

		super (
			codigo,
			produtoPrincipal,
			usuario,
			dataCadastro);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}