package br.com.ksisolucoes.vo.vigilancia.roteiroinspecao;

import java.io.Serializable;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.base.BaseRegistroInspecaoRoteiro;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

public class RegistroInspecaoRoteiro extends BaseRegistroInspecaoRoteiro implements CodigoManager {

    private static final long serialVersionUID = 1L;

	public static enum Status implements IEnum {

		EM_INSPECAO(1L, Bundle.getStringApplication("rotulo_em_inspecao")),
		EM_REINSPECAO(2L, Bundle.getStringApplication("rotulo_em_reinspecao")),
		INSPECIONADO(3L, Bundle.getStringApplication("rotulo_inspecionado"));

		private Long value;
		private String descricao;

		private Status(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		@Override
		public Long value() {
			return this.value;
		}

		@Override
		public String descricao() {
			return this.descricao;
		}

		@Override
		public String toString() {
			return this.descricao;
		}

		public static RegistroInspecao.Status valueOf(Long status) {
			for (RegistroInspecao.Status item : RegistroInspecao.Status.values()) {
				if (item.value().equals(status)) {
					return item;
				}
			}
			return null;
		}
	}
    /*[CONSTRUCTOR MARKER BEGIN]*/
	public RegistroInspecaoRoteiro () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RegistroInspecaoRoteiro (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RegistroInspecaoRoteiro (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecao registroInspecao,
		br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento atividadeEstabelecimento,
		br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RoteiroInspecao roteiroInspecao,
		java.lang.String nomeRoteiro) {

		super (
			codigo,
			registroInspecao,
			atividadeEstabelecimento,
			roteiroInspecao,
			nomeRoteiro);
	}

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}
