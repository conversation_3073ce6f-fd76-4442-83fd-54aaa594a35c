package br.com.ksisolucoes.vo.vigilancia.investigacao;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.investigacao.base.BaseInvestigacaoAgravoFebreAmarelaDeslocamento;

import java.io.Serializable;

public class InvestigacaoAgravoFebreAmarelaDeslocamento extends BaseInvestigacaoAgravoFebreAmarelaDeslocamento implements CodigoManager {
    public InvestigacaoAgravoFebreAmarelaDeslocamento() { super(); }

    public InvestigacaoAgravoFebreAmarelaDeslocamento(Long codigo) {
        super(codigo);
    }


    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public void setCodigoManager(Serializable key) {
        this.setCodigo((Long) key);
    }

}
