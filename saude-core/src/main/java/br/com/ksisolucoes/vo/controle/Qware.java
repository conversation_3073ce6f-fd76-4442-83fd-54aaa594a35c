package br.com.ksisolucoes.vo.controle;

import java.io.Serializable;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.vo.controle.base.BaseQware;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class Qware extends BaseQware implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public Qware () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public Qware (java.lang.Long codigo) {
		super(codigo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }


	public enum ColunaArquivo implements IEnum {
		COLUNA0(0L,"CD_FAMILIAR"),
		COLUNA1(1L,"NU_NIS_INTEGRANTE"),
		COLUNA2(2L,"NM_INTEGRANTE"),
		COLUNA3(3L,"DT_ULTIMA_ALTERACAO_PESSOA"),
		COLUNA4(4L,"DT_NASCIMENTO"),
		COLUNA5(5L,"CD_SEXO"),
		COLUNA6(6L,"NM_MAE"),
		COLUNA7(7L,"CD_RACA_COR"),
		COLUNA8(8L,"NU_CPF"),
		COLUNA9(9L,"CD_INEP"),
		COLUNA10(10L,"NU_SERIE_ESCOLAR"),
		COLUNA11(11L,"IN_PA"),
		COLUNA12(12L,"CD_TIPO_INTEGRANTE");

		private Long value;
		private String descricao;

		ColunaArquivo(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}


		public static String valueOf(Long value) {
			for (ColunaArquivo coluna : ColunaArquivo.values()) {
				if (coluna.value().equals(value)) {
					return coluna.descricao;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}

		@Override
		public String toString() {
			return this.descricao;
		}
	}

}