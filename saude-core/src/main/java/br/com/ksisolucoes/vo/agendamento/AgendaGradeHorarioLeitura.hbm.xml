<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.agendamento"  >
    <class name="AgendaGradeHorarioLeitura" table="agenda_grade_horario" >
        <id
            column="cd_agenda_horario"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />    
            
        <many-to-one class="br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento"
                     name="agendaGradeAtendimento" not-null="true">
            <column name="cd_ag_gra_atendimento" />
        </many-to-one>
        
        <property 
            name="hora"
            column="hora"
            type="timestamp"
            not-null="true"
        />
        
        <property
            column="status"
            name="status"
            type="java.lang.Long"
            not-null="true"
        />

        <one-to-one 
            name="agendaGradeAtendimentoHorario" 
            class="br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario" 
            property-ref="agendaGradeHorario"
        />
                                        
    </class>
</hibernate-mapping>
