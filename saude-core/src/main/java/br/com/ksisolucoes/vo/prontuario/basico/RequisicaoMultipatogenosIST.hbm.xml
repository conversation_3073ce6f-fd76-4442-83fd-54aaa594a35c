<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="RequisicaoMultipatogenosIST" table="requisicao_multipatogenos_ist" >

        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_req_multipatogenos_ist"
        >
            <generator class="sequence">
                <param name="sequence">seq_requisicao_multipatogenos_ist</param>
            </generator>
        </id>

        <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao"
            name="exameRequisicao"
            column="cd_exame_requisicao"
            not-null="true"
        />


		<property
            name="motivoSolicitacao"
            column="motivo_solicitacao"
            type="java.lang.Long"
			not-null="false"
        />

        <property
            name="motivosOutros"
            column="ds_motivos_outros"
            type="java.lang.String"
            length="100"
            not-null="false"
        />

        <property
            column="dt_cadastro"
            name="dataCadastro"
            type="date"
            not-null="false"
        />

    </class>
</hibernate-mapping>
