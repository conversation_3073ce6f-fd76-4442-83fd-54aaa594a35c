package br.com.ksisolucoes.vo.vigilancia.investigacao.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the investigacao_agr_hepatite_viral table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agr_hepatite_viral"
 */

public abstract class BaseInvestigacaoAgravoHepatiteViral extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravoHepatiteViral";
	public static final String PROP_PACIENTE_SUBMETIDO_DROGAS_INALAVEIS_CRACK = "pacienteSubmetidoDrogasInalaveisCrack";
	public static final String PROP_BAIRRO_LOCAL_INFECCAO = "bairroLocalInfeccao";
	public static final String PROP_LOCAL_PACIENTE_BCO_SANGUE_CTA = "localPacienteBcoSangueCta";
	public static final String PROP_RESULTADO_SOROLOGIA_ANTI_HAV_IGM = "resultadoSorologiaAntiHavIgm";
	public static final String PROP_PACIENTE_SUBMETIDO_TATUAGEM_PIERCING = "pacienteSubmetidoTatuagemPiercing";
	public static final String PROP_DATA_ENCERRAMENTO = "dataEncerramento";
	public static final String PROP_SUSPEITA_HEPATITE = "suspeitaHepatite";
	public static final String PROP_CASO_AUTOCTONE = "casoAutoctone";
	public static final String PROP_LOCAL_INSTITUCIONALIZADO = "localInstitucionalizado";
	public static final String PROP_PACIENTE_SUBMETIDO_TRATAMENTO_DENTARIO = "pacienteSubmetidoTratamentoDentario";
	public static final String PROP_PACIENTE_SUBMETIDO_TRATAMENTO_CIRURGICO = "pacienteSubmetidoTratamentoCirurgico";
	public static final String PROP_CONTATO_COM_PACIENTE_PORTADOR_HBV_HBC_OCUPACIONAL = "contatoComPacientePortadorHbvHbcOcupacional";
	public static final String PROP_CLASSIFICACAO_ETIOLOGICA = "classificacaoEtiologica";
	public static final String PROP_CIDADE_LOCAL_INFECCAO = "cidadeLocalInfeccao";
	public static final String PROP_RESULTADO_SOROLOGIA_HBE_AG = "resultadoSorologiaHbeAg";
	public static final String PROP_PACIENTE_SUBMETIDO_DROGAS_INJETAVEIS = "pacienteSubmetidoDrogasInjetaveis";
	public static final String PROP_RESULTADO_SOROLOGIA_ANTI_HBE = "resultadoSorologiaAntiHbe";
	public static final String PROP_RESULTADO_SOROLOGIA_ANTI_HDV_TOT = "resultadoSorologiaAntiHdvTot";
	public static final String PROP_GENOTIPO_HCV = "genotipoHcv";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_CLASSIFICACAO_FINAL = "classificacaoFinal";
	public static final String PROP_PACIENTE_SUBMETIDO_MEDICAMENTO_INJETAVEL = "pacienteSubmetidoMedicamentoInjetavel";
	public static final String PROP_PACIENTE_SUBMETIDO_PARCEIROS_SEXUAIS = "pacienteSubmetidoParceirosSexuais";
	public static final String PROP_PACIENTE_SUBMETIDO_ACUPUNTURA = "pacienteSubmetidoAcupuntura";
	public static final String PROP_PROVAVEL_FONTE_INFECCAO = "provavelFonteInfeccao";
	public static final String PROP_RESULTADO_SOROLOGIA_HCV_RNA = "resultadoSorologiaHcvRna";
	public static final String PROP_RESULTADO_SOROLOGIA_ANTI_HBC_IGM = "resultadoSorologiaAntiHbcIgm";
	public static final String PROP_CONTATO_COM_PACIENTE_PORTADOR_HBV_HBC_SEXUAL = "contatoComPacientePortadorHbvHbcSexual";
	public static final String PROP_DATA_INVESTIGACAO = "dataInvestigacao";
	public static final String PROP_CONTATO_COM_PACIENTE_PORTADOR_HBV_HBC_DOMICILIAR = "contatoComPacientePortadorHbvHbcDomiciliar";
	public static final String PROP_PACIENTE_SUBMETIDO_OUTRAS = "pacienteSubmetidoOutras";
	public static final String PROP_DATA_ACIDENTE_TRANSFUSAO_TRANSPLANTE = "dataAcidenteTransfusaoTransplante";
	public static final String PROP_USUARIO_ENCERRAMENTO = "usuarioEncerramento";
	public static final String PROP_FLAG_INFORMACOES_COMPLEMENTARES = "flagInformacoesComplementares";
	public static final String PROP_RESULTADO_SOROLOGIA_ANTI_HBC_TOT = "resultadoSorologiaAntiHbcTot";
	public static final String PROP_RESULTADO_CTA_ANTI_HBC = "resultadoCtaAntiHbc";
	public static final String PROP_PACIENTE_SUBMETIDO_AGUA_ALIMENTO_CONTAMINADO = "pacienteSubmetidoAguaAlimentoContaminado";
	public static final String PROP_DISTRITO_LOCAL_INFECCAO = "distritoLocalInfeccao";
	public static final String PROP_FORMA_CLINICA = "formaClinica";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_RESULTADO_SOROLOGIA_ANTI_HBS = "resultadoSorologiaAntiHbs";
	public static final String PROP_AGRAVOS_ASSOCIADOS_OUTRAS_DSTS = "agravosAssociadosOutrasDsts";
	public static final String PROP_DATA_COLETA_SOROLOGIA = "dataColetaSorologia";
	public static final String PROP_TOMOU_VACINA_HEPATITE_B = "tomouVacinaHepatiteB";
	public static final String PROP_TOMOU_VACINA_HEPATITE_A = "tomouVacinaHepatiteA";
	public static final String PROP_PACIENTE_SUBMETIDO_OUTRAS_DESCRICAO = "pacienteSubmetidoOutrasDescricao";
	public static final String PROP_DATA_COLETA_AMOSTRA_CTA = "dataColetaAmostraCta";
	public static final String PROP_RESULTADO_SOROLOGIA_ANTI_HDV_IGM = "resultadoSorologiaAntiHdvIgm";
	public static final String PROP_PACIENTE_SUBMETIDO_HEMODIALISE = "pacienteSubmetidoHemodialise";
	public static final String PROP_RESULTADO_CTA_HBS_AG = "resultadoCtaHbsAg";
	public static final String PROP_PROVAVEL_FONTE_INFECCAO_OUTROS_DESC = "provavelFonteInfeccaoOutrosDesc";
	public static final String PROP_REGISTRO_AGRAVO = "registroAgravo";
	public static final String PROP_PACIENTE_SUBMETIDO_ACIDENTE_MATERIAL_BIOLOGICO = "pacienteSubmetidoAcidenteMaterialBiologico";
	public static final String PROP_RESULTADO_SOROLOGIA_HBS_AG = "resultadoSorologiaHbsAg";
	public static final String PROP_OCUPACAO_CBO = "ocupacaoCbo";
	public static final String PROP_AGRAVOS_ASSOCIADOS_HIV_AIDS = "agravosAssociadosHivAids";
	public static final String PROP_RESULTADO_SOROLOGIA_ANTI_HEV_IGM = "resultadoSorologiaAntiHevIgm";
	public static final String PROP_RESULTADO_SOROLOGIA_ANTI_HCV = "resultadoSorologiaAntiHcv";
	public static final String PROP_PACIENTE_SUBMETIDO_TRANSFUSAO_SANGUE = "pacienteSubmetidoTransfusaoSangue";
	public static final String PROP_PAIS_LOCAL_INFECCAO = "paisLocalInfeccao";
	public static final String PROP_RESULTADO_CTA_ANTI_HVC = "resultadoCtaAntiHvc";
	public static final String PROP_PACIENTE_SUBMETIDO_TRANSPLANTE = "pacienteSubmetidoTransplante";


	// constructors
	public BaseInvestigacaoAgravoHepatiteViral () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravoHepatiteViral (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseInvestigacaoAgravoHepatiteViral (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
		br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo,
		java.lang.String flagInformacoesComplementares) {

		this.setCodigo(codigo);
		this.setRegistroAgravo(registroAgravo);
		this.setOcupacaoCbo(ocupacaoCbo);
		this.setFlagInformacoesComplementares(flagInformacoesComplementares);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String flagInformacoesComplementares;
	private java.util.Date dataInvestigacao;
	private java.lang.Long casoAutoctone;
	private java.lang.String distritoLocalInfeccao;
	private java.lang.String bairroLocalInfeccao;
	private java.lang.String observacao;
	private java.util.Date dataEncerramento;
	private java.lang.Long suspeitaHepatite;
	private java.lang.Long tomouVacinaHepatiteA;
	private java.lang.Long tomouVacinaHepatiteB;
	private java.lang.Long localInstitucionalizado;
	private java.lang.Long agravosAssociadosHivAids;
	private java.lang.Long agravosAssociadosOutrasDsts;
	private java.lang.Long contatoComPacientePortadorHbvHbcSexual;
	private java.lang.Long contatoComPacientePortadorHbvHbcDomiciliar;
	private java.lang.Long contatoComPacientePortadorHbvHbcOcupacional;
	private java.lang.Long pacienteSubmetidoMedicamentoInjetavel;
	private java.lang.Long pacienteSubmetidoTatuagemPiercing;
	private java.lang.Long pacienteSubmetidoAcidenteMaterialBiologico;
	private java.lang.Long pacienteSubmetidoDrogasInalaveisCrack;
	private java.lang.Long pacienteSubmetidoAcupuntura;
	private java.lang.Long pacienteSubmetidoTransfusaoSangue;
	private java.lang.Long pacienteSubmetidoDrogasInjetaveis;
	private java.lang.Long pacienteSubmetidoTratamentoCirurgico;
	private java.lang.Long pacienteSubmetidoAguaAlimentoContaminado;
	private java.lang.Long pacienteSubmetidoTratamentoDentario;
	private java.lang.Long pacienteSubmetidoParceirosSexuais;
	private java.lang.Long pacienteSubmetidoHemodialise;
	private java.lang.Long pacienteSubmetidoTransplante;
	private java.lang.Long pacienteSubmetidoOutras;
	private java.lang.String pacienteSubmetidoOutrasDescricao;
	private java.util.Date dataAcidenteTransfusaoTransplante;
	private java.lang.Long localPacienteBcoSangueCta;
	private java.util.Date dataColetaAmostraCta;
	private java.lang.Long resultadoCtaHbsAg;
	private java.lang.Long resultadoCtaAntiHbc;
	private java.lang.Long resultadoCtaAntiHvc;
	private java.util.Date dataColetaSorologia;
	private java.lang.Long resultadoSorologiaAntiHavIgm;
	private java.lang.Long resultadoSorologiaAntiHbs;
	private java.lang.Long resultadoSorologiaAntiHdvIgm;
	private java.lang.Long resultadoSorologiaHbsAg;
	private java.lang.Long resultadoSorologiaHbeAg;
	private java.lang.Long resultadoSorologiaAntiHevIgm;
	private java.lang.Long resultadoSorologiaAntiHbcIgm;
	private java.lang.Long resultadoSorologiaAntiHbe;
	private java.lang.Long resultadoSorologiaAntiHcv;
	private java.lang.Long resultadoSorologiaAntiHbcTot;
	private java.lang.Long resultadoSorologiaAntiHdvTot;
	private java.lang.Long resultadoSorologiaHcvRna;
	private java.lang.Long genotipoHcv;
	private java.lang.Long classificacaoFinal;
	private java.lang.Long formaClinica;
	private java.lang.Long classificacaoEtiologica;
	private java.lang.Long provavelFonteInfeccao;
	private java.lang.String provavelFonteInfeccaoOutrosDesc;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo;
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo;
	private br.com.ksisolucoes.vo.basico.Cidade cidadeLocalInfeccao;
	private br.com.ksisolucoes.vo.basico.Pais paisLocalInfeccao;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_invest_agr_hepatite_viral"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: flag_informacoes_complementares
	 */
	public java.lang.String getFlagInformacoesComplementares () {
		return getPropertyValue(this, flagInformacoesComplementares, PROP_FLAG_INFORMACOES_COMPLEMENTARES); 
	}

	/**
	 * Set the value related to the column: flag_informacoes_complementares
	 * @param flagInformacoesComplementares the flag_informacoes_complementares value
	 */
	public void setFlagInformacoesComplementares (java.lang.String flagInformacoesComplementares) {
//        java.lang.String flagInformacoesComplementaresOld = this.flagInformacoesComplementares;
		this.flagInformacoesComplementares = flagInformacoesComplementares;
//        this.getPropertyChangeSupport().firePropertyChange ("flagInformacoesComplementares", flagInformacoesComplementaresOld, flagInformacoesComplementares);
	}



	/**
	 * Return the value associated with the column: dt_investigacao
	 */
	public java.util.Date getDataInvestigacao () {
		return getPropertyValue(this, dataInvestigacao, PROP_DATA_INVESTIGACAO); 
	}

	/**
	 * Set the value related to the column: dt_investigacao
	 * @param dataInvestigacao the dt_investigacao value
	 */
	public void setDataInvestigacao (java.util.Date dataInvestigacao) {
//        java.util.Date dataInvestigacaoOld = this.dataInvestigacao;
		this.dataInvestigacao = dataInvestigacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInvestigacao", dataInvestigacaoOld, dataInvestigacao);
	}



	/**
	 * Return the value associated with the column: caso_autoctone
	 */
	public java.lang.Long getCasoAutoctone () {
		return getPropertyValue(this, casoAutoctone, PROP_CASO_AUTOCTONE); 
	}

	/**
	 * Set the value related to the column: caso_autoctone
	 * @param casoAutoctone the caso_autoctone value
	 */
	public void setCasoAutoctone (java.lang.Long casoAutoctone) {
//        java.lang.Long casoAutoctoneOld = this.casoAutoctone;
		this.casoAutoctone = casoAutoctone;
//        this.getPropertyChangeSupport().firePropertyChange ("casoAutoctone", casoAutoctoneOld, casoAutoctone);
	}



	/**
	 * Return the value associated with the column: str_distrito_infeccao
	 */
	public java.lang.String getDistritoLocalInfeccao () {
		return getPropertyValue(this, distritoLocalInfeccao, PROP_DISTRITO_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: str_distrito_infeccao
	 * @param distritoLocalInfeccao the str_distrito_infeccao value
	 */
	public void setDistritoLocalInfeccao (java.lang.String distritoLocalInfeccao) {
//        java.lang.String distritoLocalInfeccaoOld = this.distritoLocalInfeccao;
		this.distritoLocalInfeccao = distritoLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("distritoLocalInfeccao", distritoLocalInfeccaoOld, distritoLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: str_bairro_infeccao
	 */
	public java.lang.String getBairroLocalInfeccao () {
		return getPropertyValue(this, bairroLocalInfeccao, PROP_BAIRRO_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: str_bairro_infeccao
	 * @param bairroLocalInfeccao the str_bairro_infeccao value
	 */
	public void setBairroLocalInfeccao (java.lang.String bairroLocalInfeccao) {
//        java.lang.String bairroLocalInfeccaoOld = this.bairroLocalInfeccao;
		this.bairroLocalInfeccao = bairroLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("bairroLocalInfeccao", bairroLocalInfeccaoOld, bairroLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: dt_encerramento
	 */
	public java.util.Date getDataEncerramento () {
		return getPropertyValue(this, dataEncerramento, PROP_DATA_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_encerramento
	 * @param dataEncerramento the dt_encerramento value
	 */
	public void setDataEncerramento (java.util.Date dataEncerramento) {
//        java.util.Date dataEncerramentoOld = this.dataEncerramento;
		this.dataEncerramento = dataEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEncerramento", dataEncerramentoOld, dataEncerramento);
	}



	/**
	 * Return the value associated with the column: suspeita_hepatite
	 */
	public java.lang.Long getSuspeitaHepatite () {
		return getPropertyValue(this, suspeitaHepatite, PROP_SUSPEITA_HEPATITE); 
	}

	/**
	 * Set the value related to the column: suspeita_hepatite
	 * @param suspeitaHepatite the suspeita_hepatite value
	 */
	public void setSuspeitaHepatite (java.lang.Long suspeitaHepatite) {
//        java.lang.Long suspeitaHepatiteOld = this.suspeitaHepatite;
		this.suspeitaHepatite = suspeitaHepatite;
//        this.getPropertyChangeSupport().firePropertyChange ("suspeitaHepatite", suspeitaHepatiteOld, suspeitaHepatite);
	}



	/**
	 * Return the value associated with the column: tomou_vacina_hepatite_a
	 */
	public java.lang.Long getTomouVacinaHepatiteA () {
		return getPropertyValue(this, tomouVacinaHepatiteA, PROP_TOMOU_VACINA_HEPATITE_A); 
	}

	/**
	 * Set the value related to the column: tomou_vacina_hepatite_a
	 * @param tomouVacinaHepatiteA the tomou_vacina_hepatite_a value
	 */
	public void setTomouVacinaHepatiteA (java.lang.Long tomouVacinaHepatiteA) {
//        java.lang.Long tomouVacinaHepatiteAOld = this.tomouVacinaHepatiteA;
		this.tomouVacinaHepatiteA = tomouVacinaHepatiteA;
//        this.getPropertyChangeSupport().firePropertyChange ("tomouVacinaHepatiteA", tomouVacinaHepatiteAOld, tomouVacinaHepatiteA);
	}



	/**
	 * Return the value associated with the column: tomou_vacina_hepatite_b
	 */
	public java.lang.Long getTomouVacinaHepatiteB () {
		return getPropertyValue(this, tomouVacinaHepatiteB, PROP_TOMOU_VACINA_HEPATITE_B); 
	}

	/**
	 * Set the value related to the column: tomou_vacina_hepatite_b
	 * @param tomouVacinaHepatiteB the tomou_vacina_hepatite_b value
	 */
	public void setTomouVacinaHepatiteB (java.lang.Long tomouVacinaHepatiteB) {
//        java.lang.Long tomouVacinaHepatiteBOld = this.tomouVacinaHepatiteB;
		this.tomouVacinaHepatiteB = tomouVacinaHepatiteB;
//        this.getPropertyChangeSupport().firePropertyChange ("tomouVacinaHepatiteB", tomouVacinaHepatiteBOld, tomouVacinaHepatiteB);
	}



	/**
	 * Return the value associated with the column: local_institucionalizado
	 */
	public java.lang.Long getLocalInstitucionalizado () {
		return getPropertyValue(this, localInstitucionalizado, PROP_LOCAL_INSTITUCIONALIZADO); 
	}

	/**
	 * Set the value related to the column: local_institucionalizado
	 * @param localInstitucionalizado the local_institucionalizado value
	 */
	public void setLocalInstitucionalizado (java.lang.Long localInstitucionalizado) {
//        java.lang.Long localInstitucionalizadoOld = this.localInstitucionalizado;
		this.localInstitucionalizado = localInstitucionalizado;
//        this.getPropertyChangeSupport().firePropertyChange ("localInstitucionalizado", localInstitucionalizadoOld, localInstitucionalizado);
	}



	/**
	 * Return the value associated with the column: agravos_assoc_hiv_aids
	 */
	public java.lang.Long getAgravosAssociadosHivAids () {
		return getPropertyValue(this, agravosAssociadosHivAids, PROP_AGRAVOS_ASSOCIADOS_HIV_AIDS); 
	}

	/**
	 * Set the value related to the column: agravos_assoc_hiv_aids
	 * @param agravosAssociadosHivAids the agravos_assoc_hiv_aids value
	 */
	public void setAgravosAssociadosHivAids (java.lang.Long agravosAssociadosHivAids) {
//        java.lang.Long agravosAssociadosHivAidsOld = this.agravosAssociadosHivAids;
		this.agravosAssociadosHivAids = agravosAssociadosHivAids;
//        this.getPropertyChangeSupport().firePropertyChange ("agravosAssociadosHivAids", agravosAssociadosHivAidsOld, agravosAssociadosHivAids);
	}



	/**
	 * Return the value associated with the column: agravos_assoc_outras_dsts
	 */
	public java.lang.Long getAgravosAssociadosOutrasDsts () {
		return getPropertyValue(this, agravosAssociadosOutrasDsts, PROP_AGRAVOS_ASSOCIADOS_OUTRAS_DSTS); 
	}

	/**
	 * Set the value related to the column: agravos_assoc_outras_dsts
	 * @param agravosAssociadosOutrasDsts the agravos_assoc_outras_dsts value
	 */
	public void setAgravosAssociadosOutrasDsts (java.lang.Long agravosAssociadosOutrasDsts) {
//        java.lang.Long agravosAssociadosOutrasDstsOld = this.agravosAssociadosOutrasDsts;
		this.agravosAssociadosOutrasDsts = agravosAssociadosOutrasDsts;
//        this.getPropertyChangeSupport().firePropertyChange ("agravosAssociadosOutrasDsts", agravosAssociadosOutrasDstsOld, agravosAssociadosOutrasDsts);
	}



	/**
	 * Return the value associated with the column: contato_pac_hbv_hbc_sexual
	 */
	public java.lang.Long getContatoComPacientePortadorHbvHbcSexual () {
		return getPropertyValue(this, contatoComPacientePortadorHbvHbcSexual, PROP_CONTATO_COM_PACIENTE_PORTADOR_HBV_HBC_SEXUAL); 
	}

	/**
	 * Set the value related to the column: contato_pac_hbv_hbc_sexual
	 * @param contatoComPacientePortadorHbvHbcSexual the contato_pac_hbv_hbc_sexual value
	 */
	public void setContatoComPacientePortadorHbvHbcSexual (java.lang.Long contatoComPacientePortadorHbvHbcSexual) {
//        java.lang.Long contatoComPacientePortadorHbvHbcSexualOld = this.contatoComPacientePortadorHbvHbcSexual;
		this.contatoComPacientePortadorHbvHbcSexual = contatoComPacientePortadorHbvHbcSexual;
//        this.getPropertyChangeSupport().firePropertyChange ("contatoComPacientePortadorHbvHbcSexual", contatoComPacientePortadorHbvHbcSexualOld, contatoComPacientePortadorHbvHbcSexual);
	}



	/**
	 * Return the value associated with the column: contato_pac_hbv_hbc_domicilar
	 */
	public java.lang.Long getContatoComPacientePortadorHbvHbcDomiciliar () {
		return getPropertyValue(this, contatoComPacientePortadorHbvHbcDomiciliar, PROP_CONTATO_COM_PACIENTE_PORTADOR_HBV_HBC_DOMICILIAR); 
	}

	/**
	 * Set the value related to the column: contato_pac_hbv_hbc_domicilar
	 * @param contatoComPacientePortadorHbvHbcDomiciliar the contato_pac_hbv_hbc_domicilar value
	 */
	public void setContatoComPacientePortadorHbvHbcDomiciliar (java.lang.Long contatoComPacientePortadorHbvHbcDomiciliar) {
//        java.lang.Long contatoComPacientePortadorHbvHbcDomiciliarOld = this.contatoComPacientePortadorHbvHbcDomiciliar;
		this.contatoComPacientePortadorHbvHbcDomiciliar = contatoComPacientePortadorHbvHbcDomiciliar;
//        this.getPropertyChangeSupport().firePropertyChange ("contatoComPacientePortadorHbvHbcDomiciliar", contatoComPacientePortadorHbvHbcDomiciliarOld, contatoComPacientePortadorHbvHbcDomiciliar);
	}



	/**
	 * Return the value associated with the column: contato_pac_hbv_hbc_ocupacional
	 */
	public java.lang.Long getContatoComPacientePortadorHbvHbcOcupacional () {
		return getPropertyValue(this, contatoComPacientePortadorHbvHbcOcupacional, PROP_CONTATO_COM_PACIENTE_PORTADOR_HBV_HBC_OCUPACIONAL); 
	}

	/**
	 * Set the value related to the column: contato_pac_hbv_hbc_ocupacional
	 * @param contatoComPacientePortadorHbvHbcOcupacional the contato_pac_hbv_hbc_ocupacional value
	 */
	public void setContatoComPacientePortadorHbvHbcOcupacional (java.lang.Long contatoComPacientePortadorHbvHbcOcupacional) {
//        java.lang.Long contatoComPacientePortadorHbvHbcOcupacionalOld = this.contatoComPacientePortadorHbvHbcOcupacional;
		this.contatoComPacientePortadorHbvHbcOcupacional = contatoComPacientePortadorHbvHbcOcupacional;
//        this.getPropertyChangeSupport().firePropertyChange ("contatoComPacientePortadorHbvHbcOcupacional", contatoComPacientePortadorHbvHbcOcupacionalOld, contatoComPacientePortadorHbvHbcOcupacional);
	}



	/**
	 * Return the value associated with the column: pac_medicamento_injetavel
	 */
	public java.lang.Long getPacienteSubmetidoMedicamentoInjetavel () {
		return getPropertyValue(this, pacienteSubmetidoMedicamentoInjetavel, PROP_PACIENTE_SUBMETIDO_MEDICAMENTO_INJETAVEL); 
	}

	/**
	 * Set the value related to the column: pac_medicamento_injetavel
	 * @param pacienteSubmetidoMedicamentoInjetavel the pac_medicamento_injetavel value
	 */
	public void setPacienteSubmetidoMedicamentoInjetavel (java.lang.Long pacienteSubmetidoMedicamentoInjetavel) {
//        java.lang.Long pacienteSubmetidoMedicamentoInjetavelOld = this.pacienteSubmetidoMedicamentoInjetavel;
		this.pacienteSubmetidoMedicamentoInjetavel = pacienteSubmetidoMedicamentoInjetavel;
//        this.getPropertyChangeSupport().firePropertyChange ("pacienteSubmetidoMedicamentoInjetavel", pacienteSubmetidoMedicamentoInjetavelOld, pacienteSubmetidoMedicamentoInjetavel);
	}



	/**
	 * Return the value associated with the column: pac_tatuagem_piercing
	 */
	public java.lang.Long getPacienteSubmetidoTatuagemPiercing () {
		return getPropertyValue(this, pacienteSubmetidoTatuagemPiercing, PROP_PACIENTE_SUBMETIDO_TATUAGEM_PIERCING); 
	}

	/**
	 * Set the value related to the column: pac_tatuagem_piercing
	 * @param pacienteSubmetidoTatuagemPiercing the pac_tatuagem_piercing value
	 */
	public void setPacienteSubmetidoTatuagemPiercing (java.lang.Long pacienteSubmetidoTatuagemPiercing) {
//        java.lang.Long pacienteSubmetidoTatuagemPiercingOld = this.pacienteSubmetidoTatuagemPiercing;
		this.pacienteSubmetidoTatuagemPiercing = pacienteSubmetidoTatuagemPiercing;
//        this.getPropertyChangeSupport().firePropertyChange ("pacienteSubmetidoTatuagemPiercing", pacienteSubmetidoTatuagemPiercingOld, pacienteSubmetidoTatuagemPiercing);
	}



	/**
	 * Return the value associated with the column: pac_acidente_material_biologico
	 */
	public java.lang.Long getPacienteSubmetidoAcidenteMaterialBiologico () {
		return getPropertyValue(this, pacienteSubmetidoAcidenteMaterialBiologico, PROP_PACIENTE_SUBMETIDO_ACIDENTE_MATERIAL_BIOLOGICO); 
	}

	/**
	 * Set the value related to the column: pac_acidente_material_biologico
	 * @param pacienteSubmetidoAcidenteMaterialBiologico the pac_acidente_material_biologico value
	 */
	public void setPacienteSubmetidoAcidenteMaterialBiologico (java.lang.Long pacienteSubmetidoAcidenteMaterialBiologico) {
//        java.lang.Long pacienteSubmetidoAcidenteMaterialBiologicoOld = this.pacienteSubmetidoAcidenteMaterialBiologico;
		this.pacienteSubmetidoAcidenteMaterialBiologico = pacienteSubmetidoAcidenteMaterialBiologico;
//        this.getPropertyChangeSupport().firePropertyChange ("pacienteSubmetidoAcidenteMaterialBiologico", pacienteSubmetidoAcidenteMaterialBiologicoOld, pacienteSubmetidoAcidenteMaterialBiologico);
	}



	/**
	 * Return the value associated with the column: pac_drogas_inalaveis_crack
	 */
	public java.lang.Long getPacienteSubmetidoDrogasInalaveisCrack () {
		return getPropertyValue(this, pacienteSubmetidoDrogasInalaveisCrack, PROP_PACIENTE_SUBMETIDO_DROGAS_INALAVEIS_CRACK); 
	}

	/**
	 * Set the value related to the column: pac_drogas_inalaveis_crack
	 * @param pacienteSubmetidoDrogasInalaveisCrack the pac_drogas_inalaveis_crack value
	 */
	public void setPacienteSubmetidoDrogasInalaveisCrack (java.lang.Long pacienteSubmetidoDrogasInalaveisCrack) {
//        java.lang.Long pacienteSubmetidoDrogasInalaveisCrackOld = this.pacienteSubmetidoDrogasInalaveisCrack;
		this.pacienteSubmetidoDrogasInalaveisCrack = pacienteSubmetidoDrogasInalaveisCrack;
//        this.getPropertyChangeSupport().firePropertyChange ("pacienteSubmetidoDrogasInalaveisCrack", pacienteSubmetidoDrogasInalaveisCrackOld, pacienteSubmetidoDrogasInalaveisCrack);
	}



	/**
	 * Return the value associated with the column: pac_acupuntura
	 */
	public java.lang.Long getPacienteSubmetidoAcupuntura () {
		return getPropertyValue(this, pacienteSubmetidoAcupuntura, PROP_PACIENTE_SUBMETIDO_ACUPUNTURA); 
	}

	/**
	 * Set the value related to the column: pac_acupuntura
	 * @param pacienteSubmetidoAcupuntura the pac_acupuntura value
	 */
	public void setPacienteSubmetidoAcupuntura (java.lang.Long pacienteSubmetidoAcupuntura) {
//        java.lang.Long pacienteSubmetidoAcupunturaOld = this.pacienteSubmetidoAcupuntura;
		this.pacienteSubmetidoAcupuntura = pacienteSubmetidoAcupuntura;
//        this.getPropertyChangeSupport().firePropertyChange ("pacienteSubmetidoAcupuntura", pacienteSubmetidoAcupunturaOld, pacienteSubmetidoAcupuntura);
	}



	/**
	 * Return the value associated with the column: pac_transfusao_sangue
	 */
	public java.lang.Long getPacienteSubmetidoTransfusaoSangue () {
		return getPropertyValue(this, pacienteSubmetidoTransfusaoSangue, PROP_PACIENTE_SUBMETIDO_TRANSFUSAO_SANGUE); 
	}

	/**
	 * Set the value related to the column: pac_transfusao_sangue
	 * @param pacienteSubmetidoTransfusaoSangue the pac_transfusao_sangue value
	 */
	public void setPacienteSubmetidoTransfusaoSangue (java.lang.Long pacienteSubmetidoTransfusaoSangue) {
//        java.lang.Long pacienteSubmetidoTransfusaoSangueOld = this.pacienteSubmetidoTransfusaoSangue;
		this.pacienteSubmetidoTransfusaoSangue = pacienteSubmetidoTransfusaoSangue;
//        this.getPropertyChangeSupport().firePropertyChange ("pacienteSubmetidoTransfusaoSangue", pacienteSubmetidoTransfusaoSangueOld, pacienteSubmetidoTransfusaoSangue);
	}



	/**
	 * Return the value associated with the column: pac_drogas_injetaveis
	 */
	public java.lang.Long getPacienteSubmetidoDrogasInjetaveis () {
		return getPropertyValue(this, pacienteSubmetidoDrogasInjetaveis, PROP_PACIENTE_SUBMETIDO_DROGAS_INJETAVEIS); 
	}

	/**
	 * Set the value related to the column: pac_drogas_injetaveis
	 * @param pacienteSubmetidoDrogasInjetaveis the pac_drogas_injetaveis value
	 */
	public void setPacienteSubmetidoDrogasInjetaveis (java.lang.Long pacienteSubmetidoDrogasInjetaveis) {
//        java.lang.Long pacienteSubmetidoDrogasInjetaveisOld = this.pacienteSubmetidoDrogasInjetaveis;
		this.pacienteSubmetidoDrogasInjetaveis = pacienteSubmetidoDrogasInjetaveis;
//        this.getPropertyChangeSupport().firePropertyChange ("pacienteSubmetidoDrogasInjetaveis", pacienteSubmetidoDrogasInjetaveisOld, pacienteSubmetidoDrogasInjetaveis);
	}



	/**
	 * Return the value associated with the column: pac_tratamento_cirurgico
	 */
	public java.lang.Long getPacienteSubmetidoTratamentoCirurgico () {
		return getPropertyValue(this, pacienteSubmetidoTratamentoCirurgico, PROP_PACIENTE_SUBMETIDO_TRATAMENTO_CIRURGICO); 
	}

	/**
	 * Set the value related to the column: pac_tratamento_cirurgico
	 * @param pacienteSubmetidoTratamentoCirurgico the pac_tratamento_cirurgico value
	 */
	public void setPacienteSubmetidoTratamentoCirurgico (java.lang.Long pacienteSubmetidoTratamentoCirurgico) {
//        java.lang.Long pacienteSubmetidoTratamentoCirurgicoOld = this.pacienteSubmetidoTratamentoCirurgico;
		this.pacienteSubmetidoTratamentoCirurgico = pacienteSubmetidoTratamentoCirurgico;
//        this.getPropertyChangeSupport().firePropertyChange ("pacienteSubmetidoTratamentoCirurgico", pacienteSubmetidoTratamentoCirurgicoOld, pacienteSubmetidoTratamentoCirurgico);
	}



	/**
	 * Return the value associated with the column: pac_agua_alimento_contaminado
	 */
	public java.lang.Long getPacienteSubmetidoAguaAlimentoContaminado () {
		return getPropertyValue(this, pacienteSubmetidoAguaAlimentoContaminado, PROP_PACIENTE_SUBMETIDO_AGUA_ALIMENTO_CONTAMINADO); 
	}

	/**
	 * Set the value related to the column: pac_agua_alimento_contaminado
	 * @param pacienteSubmetidoAguaAlimentoContaminado the pac_agua_alimento_contaminado value
	 */
	public void setPacienteSubmetidoAguaAlimentoContaminado (java.lang.Long pacienteSubmetidoAguaAlimentoContaminado) {
//        java.lang.Long pacienteSubmetidoAguaAlimentoContaminadoOld = this.pacienteSubmetidoAguaAlimentoContaminado;
		this.pacienteSubmetidoAguaAlimentoContaminado = pacienteSubmetidoAguaAlimentoContaminado;
//        this.getPropertyChangeSupport().firePropertyChange ("pacienteSubmetidoAguaAlimentoContaminado", pacienteSubmetidoAguaAlimentoContaminadoOld, pacienteSubmetidoAguaAlimentoContaminado);
	}



	/**
	 * Return the value associated with the column: pac_tratamento_dentario
	 */
	public java.lang.Long getPacienteSubmetidoTratamentoDentario () {
		return getPropertyValue(this, pacienteSubmetidoTratamentoDentario, PROP_PACIENTE_SUBMETIDO_TRATAMENTO_DENTARIO); 
	}

	/**
	 * Set the value related to the column: pac_tratamento_dentario
	 * @param pacienteSubmetidoTratamentoDentario the pac_tratamento_dentario value
	 */
	public void setPacienteSubmetidoTratamentoDentario (java.lang.Long pacienteSubmetidoTratamentoDentario) {
//        java.lang.Long pacienteSubmetidoTratamentoDentarioOld = this.pacienteSubmetidoTratamentoDentario;
		this.pacienteSubmetidoTratamentoDentario = pacienteSubmetidoTratamentoDentario;
//        this.getPropertyChangeSupport().firePropertyChange ("pacienteSubmetidoTratamentoDentario", pacienteSubmetidoTratamentoDentarioOld, pacienteSubmetidoTratamentoDentario);
	}



	/**
	 * Return the value associated with the column: pac_qtd_parceiros_sexuais
	 */
	public java.lang.Long getPacienteSubmetidoParceirosSexuais () {
		return getPropertyValue(this, pacienteSubmetidoParceirosSexuais, PROP_PACIENTE_SUBMETIDO_PARCEIROS_SEXUAIS); 
	}

	/**
	 * Set the value related to the column: pac_qtd_parceiros_sexuais
	 * @param pacienteSubmetidoParceirosSexuais the pac_qtd_parceiros_sexuais value
	 */
	public void setPacienteSubmetidoParceirosSexuais (java.lang.Long pacienteSubmetidoParceirosSexuais) {
//        java.lang.Long pacienteSubmetidoParceirosSexuaisOld = this.pacienteSubmetidoParceirosSexuais;
		this.pacienteSubmetidoParceirosSexuais = pacienteSubmetidoParceirosSexuais;
//        this.getPropertyChangeSupport().firePropertyChange ("pacienteSubmetidoParceirosSexuais", pacienteSubmetidoParceirosSexuaisOld, pacienteSubmetidoParceirosSexuais);
	}



	/**
	 * Return the value associated with the column: pac_hemodialise
	 */
	public java.lang.Long getPacienteSubmetidoHemodialise () {
		return getPropertyValue(this, pacienteSubmetidoHemodialise, PROP_PACIENTE_SUBMETIDO_HEMODIALISE); 
	}

	/**
	 * Set the value related to the column: pac_hemodialise
	 * @param pacienteSubmetidoHemodialise the pac_hemodialise value
	 */
	public void setPacienteSubmetidoHemodialise (java.lang.Long pacienteSubmetidoHemodialise) {
//        java.lang.Long pacienteSubmetidoHemodialiseOld = this.pacienteSubmetidoHemodialise;
		this.pacienteSubmetidoHemodialise = pacienteSubmetidoHemodialise;
//        this.getPropertyChangeSupport().firePropertyChange ("pacienteSubmetidoHemodialise", pacienteSubmetidoHemodialiseOld, pacienteSubmetidoHemodialise);
	}



	/**
	 * Return the value associated with the column: pac_transplante
	 */
	public java.lang.Long getPacienteSubmetidoTransplante () {
		return getPropertyValue(this, pacienteSubmetidoTransplante, PROP_PACIENTE_SUBMETIDO_TRANSPLANTE); 
	}

	/**
	 * Set the value related to the column: pac_transplante
	 * @param pacienteSubmetidoTransplante the pac_transplante value
	 */
	public void setPacienteSubmetidoTransplante (java.lang.Long pacienteSubmetidoTransplante) {
//        java.lang.Long pacienteSubmetidoTransplanteOld = this.pacienteSubmetidoTransplante;
		this.pacienteSubmetidoTransplante = pacienteSubmetidoTransplante;
//        this.getPropertyChangeSupport().firePropertyChange ("pacienteSubmetidoTransplante", pacienteSubmetidoTransplanteOld, pacienteSubmetidoTransplante);
	}



	/**
	 * Return the value associated with the column: pac_outras
	 */
	public java.lang.Long getPacienteSubmetidoOutras () {
		return getPropertyValue(this, pacienteSubmetidoOutras, PROP_PACIENTE_SUBMETIDO_OUTRAS); 
	}

	/**
	 * Set the value related to the column: pac_outras
	 * @param pacienteSubmetidoOutras the pac_outras value
	 */
	public void setPacienteSubmetidoOutras (java.lang.Long pacienteSubmetidoOutras) {
//        java.lang.Long pacienteSubmetidoOutrasOld = this.pacienteSubmetidoOutras;
		this.pacienteSubmetidoOutras = pacienteSubmetidoOutras;
//        this.getPropertyChangeSupport().firePropertyChange ("pacienteSubmetidoOutras", pacienteSubmetidoOutrasOld, pacienteSubmetidoOutras);
	}



	/**
	 * Return the value associated with the column: pac_str_outras
	 */
	public java.lang.String getPacienteSubmetidoOutrasDescricao () {
		return getPropertyValue(this, pacienteSubmetidoOutrasDescricao, PROP_PACIENTE_SUBMETIDO_OUTRAS_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: pac_str_outras
	 * @param pacienteSubmetidoOutrasDescricao the pac_str_outras value
	 */
	public void setPacienteSubmetidoOutrasDescricao (java.lang.String pacienteSubmetidoOutrasDescricao) {
//        java.lang.String pacienteSubmetidoOutrasDescricaoOld = this.pacienteSubmetidoOutrasDescricao;
		this.pacienteSubmetidoOutrasDescricao = pacienteSubmetidoOutrasDescricao;
//        this.getPropertyChangeSupport().firePropertyChange ("pacienteSubmetidoOutrasDescricao", pacienteSubmetidoOutrasDescricaoOld, pacienteSubmetidoOutrasDescricao);
	}



	/**
	 * Return the value associated with the column: dt_acidente_transfusao_transplante
	 */
	public java.util.Date getDataAcidenteTransfusaoTransplante () {
		return getPropertyValue(this, dataAcidenteTransfusaoTransplante, PROP_DATA_ACIDENTE_TRANSFUSAO_TRANSPLANTE); 
	}

	/**
	 * Set the value related to the column: dt_acidente_transfusao_transplante
	 * @param dataAcidenteTransfusaoTransplante the dt_acidente_transfusao_transplante value
	 */
	public void setDataAcidenteTransfusaoTransplante (java.util.Date dataAcidenteTransfusaoTransplante) {
//        java.util.Date dataAcidenteTransfusaoTransplanteOld = this.dataAcidenteTransfusaoTransplante;
		this.dataAcidenteTransfusaoTransplante = dataAcidenteTransfusaoTransplante;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAcidenteTransfusaoTransplante", dataAcidenteTransfusaoTransplanteOld, dataAcidenteTransfusaoTransplante);
	}



	/**
	 * Return the value associated with the column: local_paciente_banco_sangue_cta
	 */
	public java.lang.Long getLocalPacienteBcoSangueCta () {
		return getPropertyValue(this, localPacienteBcoSangueCta, PROP_LOCAL_PACIENTE_BCO_SANGUE_CTA); 
	}

	/**
	 * Set the value related to the column: local_paciente_banco_sangue_cta
	 * @param localPacienteBcoSangueCta the local_paciente_banco_sangue_cta value
	 */
	public void setLocalPacienteBcoSangueCta (java.lang.Long localPacienteBcoSangueCta) {
//        java.lang.Long localPacienteBcoSangueCtaOld = this.localPacienteBcoSangueCta;
		this.localPacienteBcoSangueCta = localPacienteBcoSangueCta;
//        this.getPropertyChangeSupport().firePropertyChange ("localPacienteBcoSangueCta", localPacienteBcoSangueCtaOld, localPacienteBcoSangueCta);
	}



	/**
	 * Return the value associated with the column: dt_coleta_amostra_cta
	 */
	public java.util.Date getDataColetaAmostraCta () {
		return getPropertyValue(this, dataColetaAmostraCta, PROP_DATA_COLETA_AMOSTRA_CTA); 
	}

	/**
	 * Set the value related to the column: dt_coleta_amostra_cta
	 * @param dataColetaAmostraCta the dt_coleta_amostra_cta value
	 */
	public void setDataColetaAmostraCta (java.util.Date dataColetaAmostraCta) {
//        java.util.Date dataColetaAmostraCtaOld = this.dataColetaAmostraCta;
		this.dataColetaAmostraCta = dataColetaAmostraCta;
//        this.getPropertyChangeSupport().firePropertyChange ("dataColetaAmostraCta", dataColetaAmostraCtaOld, dataColetaAmostraCta);
	}



	/**
	 * Return the value associated with the column: resultado_cta_hbsag
	 */
	public java.lang.Long getResultadoCtaHbsAg () {
		return getPropertyValue(this, resultadoCtaHbsAg, PROP_RESULTADO_CTA_HBS_AG); 
	}

	/**
	 * Set the value related to the column: resultado_cta_hbsag
	 * @param resultadoCtaHbsAg the resultado_cta_hbsag value
	 */
	public void setResultadoCtaHbsAg (java.lang.Long resultadoCtaHbsAg) {
//        java.lang.Long resultadoCtaHbsAgOld = this.resultadoCtaHbsAg;
		this.resultadoCtaHbsAg = resultadoCtaHbsAg;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoCtaHbsAg", resultadoCtaHbsAgOld, resultadoCtaHbsAg);
	}



	/**
	 * Return the value associated with the column: resultado_cta_anti_hbc
	 */
	public java.lang.Long getResultadoCtaAntiHbc () {
		return getPropertyValue(this, resultadoCtaAntiHbc, PROP_RESULTADO_CTA_ANTI_HBC); 
	}

	/**
	 * Set the value related to the column: resultado_cta_anti_hbc
	 * @param resultadoCtaAntiHbc the resultado_cta_anti_hbc value
	 */
	public void setResultadoCtaAntiHbc (java.lang.Long resultadoCtaAntiHbc) {
//        java.lang.Long resultadoCtaAntiHbcOld = this.resultadoCtaAntiHbc;
		this.resultadoCtaAntiHbc = resultadoCtaAntiHbc;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoCtaAntiHbc", resultadoCtaAntiHbcOld, resultadoCtaAntiHbc);
	}



	/**
	 * Return the value associated with the column: resultado_cta_anti_hvc
	 */
	public java.lang.Long getResultadoCtaAntiHvc () {
		return getPropertyValue(this, resultadoCtaAntiHvc, PROP_RESULTADO_CTA_ANTI_HVC); 
	}

	/**
	 * Set the value related to the column: resultado_cta_anti_hvc
	 * @param resultadoCtaAntiHvc the resultado_cta_anti_hvc value
	 */
	public void setResultadoCtaAntiHvc (java.lang.Long resultadoCtaAntiHvc) {
//        java.lang.Long resultadoCtaAntiHvcOld = this.resultadoCtaAntiHvc;
		this.resultadoCtaAntiHvc = resultadoCtaAntiHvc;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoCtaAntiHvc", resultadoCtaAntiHvcOld, resultadoCtaAntiHvc);
	}



	/**
	 * Return the value associated with the column: dt_coleta_sorologia
	 */
	public java.util.Date getDataColetaSorologia () {
		return getPropertyValue(this, dataColetaSorologia, PROP_DATA_COLETA_SOROLOGIA); 
	}

	/**
	 * Set the value related to the column: dt_coleta_sorologia
	 * @param dataColetaSorologia the dt_coleta_sorologia value
	 */
	public void setDataColetaSorologia (java.util.Date dataColetaSorologia) {
//        java.util.Date dataColetaSorologiaOld = this.dataColetaSorologia;
		this.dataColetaSorologia = dataColetaSorologia;
//        this.getPropertyChangeSupport().firePropertyChange ("dataColetaSorologia", dataColetaSorologiaOld, dataColetaSorologia);
	}



	/**
	 * Return the value associated with the column: resultado_sorologia_anti_hav_igm
	 */
	public java.lang.Long getResultadoSorologiaAntiHavIgm () {
		return getPropertyValue(this, resultadoSorologiaAntiHavIgm, PROP_RESULTADO_SOROLOGIA_ANTI_HAV_IGM); 
	}

	/**
	 * Set the value related to the column: resultado_sorologia_anti_hav_igm
	 * @param resultadoSorologiaAntiHavIgm the resultado_sorologia_anti_hav_igm value
	 */
	public void setResultadoSorologiaAntiHavIgm (java.lang.Long resultadoSorologiaAntiHavIgm) {
//        java.lang.Long resultadoSorologiaAntiHavIgmOld = this.resultadoSorologiaAntiHavIgm;
		this.resultadoSorologiaAntiHavIgm = resultadoSorologiaAntiHavIgm;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoSorologiaAntiHavIgm", resultadoSorologiaAntiHavIgmOld, resultadoSorologiaAntiHavIgm);
	}



	/**
	 * Return the value associated with the column: resultado_sorologia_anti_hbs
	 */
	public java.lang.Long getResultadoSorologiaAntiHbs () {
		return getPropertyValue(this, resultadoSorologiaAntiHbs, PROP_RESULTADO_SOROLOGIA_ANTI_HBS); 
	}

	/**
	 * Set the value related to the column: resultado_sorologia_anti_hbs
	 * @param resultadoSorologiaAntiHbs the resultado_sorologia_anti_hbs value
	 */
	public void setResultadoSorologiaAntiHbs (java.lang.Long resultadoSorologiaAntiHbs) {
//        java.lang.Long resultadoSorologiaAntiHbsOld = this.resultadoSorologiaAntiHbs;
		this.resultadoSorologiaAntiHbs = resultadoSorologiaAntiHbs;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoSorologiaAntiHbs", resultadoSorologiaAntiHbsOld, resultadoSorologiaAntiHbs);
	}



	/**
	 * Return the value associated with the column: resultado_sorologia_anti_hdv_igm
	 */
	public java.lang.Long getResultadoSorologiaAntiHdvIgm () {
		return getPropertyValue(this, resultadoSorologiaAntiHdvIgm, PROP_RESULTADO_SOROLOGIA_ANTI_HDV_IGM); 
	}

	/**
	 * Set the value related to the column: resultado_sorologia_anti_hdv_igm
	 * @param resultadoSorologiaAntiHdvIgm the resultado_sorologia_anti_hdv_igm value
	 */
	public void setResultadoSorologiaAntiHdvIgm (java.lang.Long resultadoSorologiaAntiHdvIgm) {
//        java.lang.Long resultadoSorologiaAntiHdvIgmOld = this.resultadoSorologiaAntiHdvIgm;
		this.resultadoSorologiaAntiHdvIgm = resultadoSorologiaAntiHdvIgm;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoSorologiaAntiHdvIgm", resultadoSorologiaAntiHdvIgmOld, resultadoSorologiaAntiHdvIgm);
	}



	/**
	 * Return the value associated with the column: resultado_sorologia_hbsag
	 */
	public java.lang.Long getResultadoSorologiaHbsAg () {
		return getPropertyValue(this, resultadoSorologiaHbsAg, PROP_RESULTADO_SOROLOGIA_HBS_AG); 
	}

	/**
	 * Set the value related to the column: resultado_sorologia_hbsag
	 * @param resultadoSorologiaHbsAg the resultado_sorologia_hbsag value
	 */
	public void setResultadoSorologiaHbsAg (java.lang.Long resultadoSorologiaHbsAg) {
//        java.lang.Long resultadoSorologiaHbsAgOld = this.resultadoSorologiaHbsAg;
		this.resultadoSorologiaHbsAg = resultadoSorologiaHbsAg;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoSorologiaHbsAg", resultadoSorologiaHbsAgOld, resultadoSorologiaHbsAg);
	}



	/**
	 * Return the value associated with the column: resultado_sorologia_hbeag
	 */
	public java.lang.Long getResultadoSorologiaHbeAg () {
		return getPropertyValue(this, resultadoSorologiaHbeAg, PROP_RESULTADO_SOROLOGIA_HBE_AG); 
	}

	/**
	 * Set the value related to the column: resultado_sorologia_hbeag
	 * @param resultadoSorologiaHbeAg the resultado_sorologia_hbeag value
	 */
	public void setResultadoSorologiaHbeAg (java.lang.Long resultadoSorologiaHbeAg) {
//        java.lang.Long resultadoSorologiaHbeAgOld = this.resultadoSorologiaHbeAg;
		this.resultadoSorologiaHbeAg = resultadoSorologiaHbeAg;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoSorologiaHbeAg", resultadoSorologiaHbeAgOld, resultadoSorologiaHbeAg);
	}



	/**
	 * Return the value associated with the column: resultado_sorologia_anti_hev_igm
	 */
	public java.lang.Long getResultadoSorologiaAntiHevIgm () {
		return getPropertyValue(this, resultadoSorologiaAntiHevIgm, PROP_RESULTADO_SOROLOGIA_ANTI_HEV_IGM); 
	}

	/**
	 * Set the value related to the column: resultado_sorologia_anti_hev_igm
	 * @param resultadoSorologiaAntiHevIgm the resultado_sorologia_anti_hev_igm value
	 */
	public void setResultadoSorologiaAntiHevIgm (java.lang.Long resultadoSorologiaAntiHevIgm) {
//        java.lang.Long resultadoSorologiaAntiHevIgmOld = this.resultadoSorologiaAntiHevIgm;
		this.resultadoSorologiaAntiHevIgm = resultadoSorologiaAntiHevIgm;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoSorologiaAntiHevIgm", resultadoSorologiaAntiHevIgmOld, resultadoSorologiaAntiHevIgm);
	}



	/**
	 * Return the value associated with the column: resultado_sorologia_anti_hbc_igm
	 */
	public java.lang.Long getResultadoSorologiaAntiHbcIgm () {
		return getPropertyValue(this, resultadoSorologiaAntiHbcIgm, PROP_RESULTADO_SOROLOGIA_ANTI_HBC_IGM); 
	}

	/**
	 * Set the value related to the column: resultado_sorologia_anti_hbc_igm
	 * @param resultadoSorologiaAntiHbcIgm the resultado_sorologia_anti_hbc_igm value
	 */
	public void setResultadoSorologiaAntiHbcIgm (java.lang.Long resultadoSorologiaAntiHbcIgm) {
//        java.lang.Long resultadoSorologiaAntiHbcIgmOld = this.resultadoSorologiaAntiHbcIgm;
		this.resultadoSorologiaAntiHbcIgm = resultadoSorologiaAntiHbcIgm;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoSorologiaAntiHbcIgm", resultadoSorologiaAntiHbcIgmOld, resultadoSorologiaAntiHbcIgm);
	}



	/**
	 * Return the value associated with the column: resultado_sorologia_anti_hbe
	 */
	public java.lang.Long getResultadoSorologiaAntiHbe () {
		return getPropertyValue(this, resultadoSorologiaAntiHbe, PROP_RESULTADO_SOROLOGIA_ANTI_HBE); 
	}

	/**
	 * Set the value related to the column: resultado_sorologia_anti_hbe
	 * @param resultadoSorologiaAntiHbe the resultado_sorologia_anti_hbe value
	 */
	public void setResultadoSorologiaAntiHbe (java.lang.Long resultadoSorologiaAntiHbe) {
//        java.lang.Long resultadoSorologiaAntiHbeOld = this.resultadoSorologiaAntiHbe;
		this.resultadoSorologiaAntiHbe = resultadoSorologiaAntiHbe;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoSorologiaAntiHbe", resultadoSorologiaAntiHbeOld, resultadoSorologiaAntiHbe);
	}



	/**
	 * Return the value associated with the column: resultado_sorologia_anti_hcv
	 */
	public java.lang.Long getResultadoSorologiaAntiHcv () {
		return getPropertyValue(this, resultadoSorologiaAntiHcv, PROP_RESULTADO_SOROLOGIA_ANTI_HCV); 
	}

	/**
	 * Set the value related to the column: resultado_sorologia_anti_hcv
	 * @param resultadoSorologiaAntiHcv the resultado_sorologia_anti_hcv value
	 */
	public void setResultadoSorologiaAntiHcv (java.lang.Long resultadoSorologiaAntiHcv) {
//        java.lang.Long resultadoSorologiaAntiHcvOld = this.resultadoSorologiaAntiHcv;
		this.resultadoSorologiaAntiHcv = resultadoSorologiaAntiHcv;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoSorologiaAntiHcv", resultadoSorologiaAntiHcvOld, resultadoSorologiaAntiHcv);
	}



	/**
	 * Return the value associated with the column: resultado_sorologia_anti_hbc_tot
	 */
	public java.lang.Long getResultadoSorologiaAntiHbcTot () {
		return getPropertyValue(this, resultadoSorologiaAntiHbcTot, PROP_RESULTADO_SOROLOGIA_ANTI_HBC_TOT); 
	}

	/**
	 * Set the value related to the column: resultado_sorologia_anti_hbc_tot
	 * @param resultadoSorologiaAntiHbcTot the resultado_sorologia_anti_hbc_tot value
	 */
	public void setResultadoSorologiaAntiHbcTot (java.lang.Long resultadoSorologiaAntiHbcTot) {
//        java.lang.Long resultadoSorologiaAntiHbcTotOld = this.resultadoSorologiaAntiHbcTot;
		this.resultadoSorologiaAntiHbcTot = resultadoSorologiaAntiHbcTot;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoSorologiaAntiHbcTot", resultadoSorologiaAntiHbcTotOld, resultadoSorologiaAntiHbcTot);
	}



	/**
	 * Return the value associated with the column: resultado_sorologia_anti_hdv_tot
	 */
	public java.lang.Long getResultadoSorologiaAntiHdvTot () {
		return getPropertyValue(this, resultadoSorologiaAntiHdvTot, PROP_RESULTADO_SOROLOGIA_ANTI_HDV_TOT); 
	}

	/**
	 * Set the value related to the column: resultado_sorologia_anti_hdv_tot
	 * @param resultadoSorologiaAntiHdvTot the resultado_sorologia_anti_hdv_tot value
	 */
	public void setResultadoSorologiaAntiHdvTot (java.lang.Long resultadoSorologiaAntiHdvTot) {
//        java.lang.Long resultadoSorologiaAntiHdvTotOld = this.resultadoSorologiaAntiHdvTot;
		this.resultadoSorologiaAntiHdvTot = resultadoSorologiaAntiHdvTot;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoSorologiaAntiHdvTot", resultadoSorologiaAntiHdvTotOld, resultadoSorologiaAntiHdvTot);
	}



	/**
	 * Return the value associated with the column: resultado_sorologia_hcv_rna
	 */
	public java.lang.Long getResultadoSorologiaHcvRna () {
		return getPropertyValue(this, resultadoSorologiaHcvRna, PROP_RESULTADO_SOROLOGIA_HCV_RNA); 
	}

	/**
	 * Set the value related to the column: resultado_sorologia_hcv_rna
	 * @param resultadoSorologiaHcvRna the resultado_sorologia_hcv_rna value
	 */
	public void setResultadoSorologiaHcvRna (java.lang.Long resultadoSorologiaHcvRna) {
//        java.lang.Long resultadoSorologiaHcvRnaOld = this.resultadoSorologiaHcvRna;
		this.resultadoSorologiaHcvRna = resultadoSorologiaHcvRna;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoSorologiaHcvRna", resultadoSorologiaHcvRnaOld, resultadoSorologiaHcvRna);
	}



	/**
	 * Return the value associated with the column: genotipo_hcv
	 */
	public java.lang.Long getGenotipoHcv () {
		return getPropertyValue(this, genotipoHcv, PROP_GENOTIPO_HCV); 
	}

	/**
	 * Set the value related to the column: genotipo_hcv
	 * @param genotipoHcv the genotipo_hcv value
	 */
	public void setGenotipoHcv (java.lang.Long genotipoHcv) {
//        java.lang.Long genotipoHcvOld = this.genotipoHcv;
		this.genotipoHcv = genotipoHcv;
//        this.getPropertyChangeSupport().firePropertyChange ("genotipoHcv", genotipoHcvOld, genotipoHcv);
	}



	/**
	 * Return the value associated with the column: classificacao_final
	 */
	public java.lang.Long getClassificacaoFinal () {
		return getPropertyValue(this, classificacaoFinal, PROP_CLASSIFICACAO_FINAL); 
	}

	/**
	 * Set the value related to the column: classificacao_final
	 * @param classificacaoFinal the classificacao_final value
	 */
	public void setClassificacaoFinal (java.lang.Long classificacaoFinal) {
//        java.lang.Long classificacaoFinalOld = this.classificacaoFinal;
		this.classificacaoFinal = classificacaoFinal;
//        this.getPropertyChangeSupport().firePropertyChange ("classificacaoFinal", classificacaoFinalOld, classificacaoFinal);
	}



	/**
	 * Return the value associated with the column: forma_clinica
	 */
	public java.lang.Long getFormaClinica () {
		return getPropertyValue(this, formaClinica, PROP_FORMA_CLINICA); 
	}

	/**
	 * Set the value related to the column: forma_clinica
	 * @param formaClinica the forma_clinica value
	 */
	public void setFormaClinica (java.lang.Long formaClinica) {
//        java.lang.Long formaClinicaOld = this.formaClinica;
		this.formaClinica = formaClinica;
//        this.getPropertyChangeSupport().firePropertyChange ("formaClinica", formaClinicaOld, formaClinica);
	}



	/**
	 * Return the value associated with the column: classificacao_etiologica
	 */
	public java.lang.Long getClassificacaoEtiologica () {
		return getPropertyValue(this, classificacaoEtiologica, PROP_CLASSIFICACAO_ETIOLOGICA); 
	}

	/**
	 * Set the value related to the column: classificacao_etiologica
	 * @param classificacaoEtiologica the classificacao_etiologica value
	 */
	public void setClassificacaoEtiologica (java.lang.Long classificacaoEtiologica) {
//        java.lang.Long classificacaoEtiologicaOld = this.classificacaoEtiologica;
		this.classificacaoEtiologica = classificacaoEtiologica;
//        this.getPropertyChangeSupport().firePropertyChange ("classificacaoEtiologica", classificacaoEtiologicaOld, classificacaoEtiologica);
	}



	/**
	 * Return the value associated with the column: provavel_fonte_infeccao
	 */
	public java.lang.Long getProvavelFonteInfeccao () {
		return getPropertyValue(this, provavelFonteInfeccao, PROP_PROVAVEL_FONTE_INFECCAO); 
	}

	/**
	 * Set the value related to the column: provavel_fonte_infeccao
	 * @param provavelFonteInfeccao the provavel_fonte_infeccao value
	 */
	public void setProvavelFonteInfeccao (java.lang.Long provavelFonteInfeccao) {
//        java.lang.Long provavelFonteInfeccaoOld = this.provavelFonteInfeccao;
		this.provavelFonteInfeccao = provavelFonteInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("provavelFonteInfeccao", provavelFonteInfeccaoOld, provavelFonteInfeccao);
	}



	/**
	 * Return the value associated with the column: provavel_fonte_infeccao_outros
	 */
	public java.lang.String getProvavelFonteInfeccaoOutrosDesc () {
		return getPropertyValue(this, provavelFonteInfeccaoOutrosDesc, PROP_PROVAVEL_FONTE_INFECCAO_OUTROS_DESC); 
	}

	/**
	 * Set the value related to the column: provavel_fonte_infeccao_outros
	 * @param provavelFonteInfeccaoOutrosDesc the provavel_fonte_infeccao_outros value
	 */
	public void setProvavelFonteInfeccaoOutrosDesc (java.lang.String provavelFonteInfeccaoOutrosDesc) {
//        java.lang.String provavelFonteInfeccaoOutrosDescOld = this.provavelFonteInfeccaoOutrosDesc;
		this.provavelFonteInfeccaoOutrosDesc = provavelFonteInfeccaoOutrosDesc;
//        this.getPropertyChangeSupport().firePropertyChange ("provavelFonteInfeccaoOutrosDesc", provavelFonteInfeccaoOutrosDescOld, provavelFonteInfeccaoOutrosDesc);
	}



	/**
	 * Return the value associated with the column: cd_registro_agravo
	 */
	public br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo getRegistroAgravo () {
		return getPropertyValue(this, registroAgravo, PROP_REGISTRO_AGRAVO); 
	}

	/**
	 * Set the value related to the column: cd_registro_agravo
	 * @param registroAgravo the cd_registro_agravo value
	 */
	public void setRegistroAgravo (br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo) {
//        br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravoOld = this.registroAgravo;
		this.registroAgravo = registroAgravo;
//        this.getPropertyChangeSupport().firePropertyChange ("registroAgravo", registroAgravoOld, registroAgravo);
	}



	/**
	 * Return the value associated with the column: ocupacao_cbo
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getOcupacaoCbo () {
		return getPropertyValue(this, ocupacaoCbo, PROP_OCUPACAO_CBO); 
	}

	/**
	 * Set the value related to the column: ocupacao_cbo
	 * @param ocupacaoCbo the ocupacao_cbo value
	 */
	public void setOcupacaoCbo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCboOld = this.ocupacaoCbo;
		this.ocupacaoCbo = ocupacaoCbo;
//        this.getPropertyChangeSupport().firePropertyChange ("ocupacaoCbo", ocupacaoCboOld, ocupacaoCbo);
	}



	/**
	 * Return the value associated with the column: cd_cidade_infeccao
	 */
	public br.com.ksisolucoes.vo.basico.Cidade getCidadeLocalInfeccao () {
		return getPropertyValue(this, cidadeLocalInfeccao, PROP_CIDADE_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: cd_cidade_infeccao
	 * @param cidadeLocalInfeccao the cd_cidade_infeccao value
	 */
	public void setCidadeLocalInfeccao (br.com.ksisolucoes.vo.basico.Cidade cidadeLocalInfeccao) {
//        br.com.ksisolucoes.vo.basico.Cidade cidadeLocalInfeccaoOld = this.cidadeLocalInfeccao;
		this.cidadeLocalInfeccao = cidadeLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("cidadeLocalInfeccao", cidadeLocalInfeccaoOld, cidadeLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: cd_pais_infeccao
	 */
	public br.com.ksisolucoes.vo.basico.Pais getPaisLocalInfeccao () {
		return getPropertyValue(this, paisLocalInfeccao, PROP_PAIS_LOCAL_INFECCAO); 
	}

	/**
	 * Set the value related to the column: cd_pais_infeccao
	 * @param paisLocalInfeccao the cd_pais_infeccao value
	 */
	public void setPaisLocalInfeccao (br.com.ksisolucoes.vo.basico.Pais paisLocalInfeccao) {
//        br.com.ksisolucoes.vo.basico.Pais paisLocalInfeccaoOld = this.paisLocalInfeccao;
		this.paisLocalInfeccao = paisLocalInfeccao;
//        this.getPropertyChangeSupport().firePropertyChange ("paisLocalInfeccao", paisLocalInfeccaoOld, paisLocalInfeccao);
	}



	/**
	 * Return the value associated with the column: cd_usuario_encerramento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioEncerramento () {
		return getPropertyValue(this, usuarioEncerramento, PROP_USUARIO_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_encerramento
	 * @param usuarioEncerramento the cd_usuario_encerramento value
	 */
	public void setUsuarioEncerramento (br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramentoOld = this.usuarioEncerramento;
		this.usuarioEncerramento = usuarioEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioEncerramento", usuarioEncerramentoOld, usuarioEncerramento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoHepatiteViral)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoHepatiteViral investigacaoAgravoHepatiteViral = (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoHepatiteViral) obj;
			if (null == this.getCodigo() || null == investigacaoAgravoHepatiteViral.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravoHepatiteViral.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}