<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD//EN" "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.integracao.branet"  >
    <class name="IntegracaoProfissionalBranet" table="integracao_prof_branet">

        <id name="codigo" type="java.lang.Long" column="cd_integracao_prof_branet">
            <generator class="sequence"><param name="sequence">seq_integracao_prof_branet</param></generator>
        </id>

        <version column="version" name="version" type="long" />

        <many-to-one class="br.com.ksisolucoes.vo.cadsus.Profissional" column="cd_profissional" name="profissional" not-null="true"/>
        <many-to-one name="usuario" column="cd_usuario" class="br.com.ksisolucoes.vo.controle.Usuario"/>

        <property column="id" name="id" not-null="false" type="java.lang.Long"/>
        <property column="id_responsavel" name="idResponsavel" not-null="false" type="java.lang.Long"/>
        <property name="json" column="json" type="java.lang.String"/>
        <property name="mensagem" column="mensagem" type="java.lang.String"/>
        <property name="data" column="data" type="timestamp" not-null="true"/>
        <property name="status" column="status" type="java.lang.String"/>
        <property name="tipoLog" column="tipo_log" type="java.lang.String"/>
        <property name="resposta" column="resposta" type="java.lang.String"/>
        <property name="dataCadastro" column="dt_cadastro" type="timestamp" not-null="true"/>

    </class>
</hibernate-mapping>
