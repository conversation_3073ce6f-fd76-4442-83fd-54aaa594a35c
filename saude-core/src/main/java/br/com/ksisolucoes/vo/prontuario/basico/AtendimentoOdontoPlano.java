package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseAtendimentoOdontoPlano;
import ch.lambdaj.Lambda;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class AtendimentoOdontoPlano extends BaseAtendimentoOdontoPlano implements CodigoManager {

    private static final long serialVersionUID = 1L;

    public static final String PROP_DESCRICAO_STATUS = "descricaoStatus";
    public static final String PROP_DESCRICAO_FACE_FORMATADO = "descricaoFaceFormatado";
    public static final String PROP_DESCRICAO_FACE = "descricaoFace";

    public enum Tipo implements IEnum {

        DENTE(0L, Bundle.getStringApplication("rotulo_dente")),
        SEXTANTE(1L, Bundle.getStringApplication("rotulo_sextante")),
        ARCADA(2L, Bundle.getStringApplication("rotulo_arcada")),
        OUTRO(3L, Bundle.getStringApplication("rotulo_outro")),
        TECIDOS_MOLES(4L, Bundle.getStringApplication("rotulo_tecidos_moles"));

        private Long value;
        private String descricao;

        private Tipo(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Tipo valueOf(Long value) {
            for (Tipo tipo : Tipo.values()) {
                if (tipo.value().equals(value)) {
                    return tipo;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }
    
    public enum TipoAplicacao {

    	COROA(1L),
        COLO(2L),
        RAIZ(3L),
    	COLO_RAIZ(4L),
    	COMPLETO(5L);

        private Long value;

        private TipoAplicacao(Long value) {
            this.value = value;
        }

        public static TipoAplicacao valueOf(Long value) {
            for (TipoAplicacao tipo : TipoAplicacao.values()) {
                if (tipo.value().equals(value)) {
                    return tipo;
                }
            }
            return null;
        }

        public Long value() {
            return value;
        }

    }

    public enum Status implements IEnum {

        PENDENTE(0L, Bundle.getStringApplication("rotulo_pendente")),
        EM_ANDAMENTO(1L, Bundle.getStringApplication("rotulo_em_andamento")),
        CONCLUIDO(2L, Bundle.getStringApplication("rotulo_concluido")),
        CANCELADO(3L, Bundle.getStringApplication("rotulo_cancelado")),
        HISTORICO(4L, Bundle.getStringApplication("rotulo_historico"));

        private Long value;
        private String descricao;

        private Status(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Status valueOf(Long value) {
            for (Status status : Status.values()) {
                if (status.value().equals(value)) {
                    return status;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }

    public enum Face implements IEnum<Face> {

        DISTAL(1L, Bundle.getStringApplication("rotulo_distal")),
        VESTIBULAR(2L, Bundle.getStringApplication("rotulo_vestibular")),
        MESIAL(3L, Bundle.getStringApplication("rotulo_mesial")),
        LINGUAL_PALATAL(4L, Bundle.getStringApplication("rotulo_lingual_palatal")),
        OCLUSAL_INCISAL(5L, Bundle.getStringApplication("rotulo_oclusal_incisal")),
        ML(6L, Bundle.getStringApplication("rotulo_m_l")),
        MO(7L, Bundle.getStringApplication("rotulo_m_o")),
        MD(8L, Bundle.getStringApplication("rotulo_m_d")),
        MV(9L, Bundle.getStringApplication("rotulo_m_v")),
        MOD(10L, Bundle.getStringApplication("rotulo_m_o_d")),
        DL(11L, Bundle.getStringApplication("rotulo_d_l")),
        DO(12L, Bundle.getStringApplication("rotulo_d_o")),
        DM(13L, Bundle.getStringApplication("rotulo_d_m")),
        DV(14L, Bundle.getStringApplication("rotulo_d_v")),
        O_L(15L, Bundle.getStringApplication("rotulo_o_l")),
        O_P(16L, Bundle.getStringApplication("rotulo_o_p")),
        CERVICAL(17L, Bundle.getStringApplication("rotulo_cervical")),
        OCLUSAL(18L, Bundle.getStringApplication("rotulo_oclusal")),
        PALATINA(19L, Bundle.getStringApplication("rotulo_palatina")),
        OV(20L, Bundle.getStringApplication("rotulo_o_v")),
        MP(21L, Bundle.getStringApplication("rotulo_mp")),
        DP(22L, Bundle.getStringApplication("rotulo_dp")),
        MI(23L, Bundle.getStringApplication("rotulo_mi")),
        DI(24L, Bundle.getStringApplication("rotulo_d_i")),
        MVD(25L, Bundle.getStringApplication("rotulo_mvd")),
        MOV(26L, Bundle.getStringApplication("rotulo_mov")),
        DOV(27L, Bundle.getStringApplication("rotulo_dov")),
        MLD(28L, Bundle.getStringApplication("rotulo_mld")),
        MPD(29L, Bundle.getStringApplication("rotulo_mpd")),
        MOL(30L, Bundle.getStringApplication("rotulo_mol")),
        MOP(31L, Bundle.getStringApplication("rotulo_mop")),
        DOL(32L, Bundle.getStringApplication("rotulo_dol")),
        DOP(33L, Bundle.getStringApplication("rotulo_dop")),
        VML(34L, Bundle.getStringApplication("rotulo_vml")),
        VMP(35L, Bundle.getStringApplication("rotulo_vmp")),
        VDL(36L, Bundle.getStringApplication("rotulo_vdl")),
        VDP(37L, Bundle.getStringApplication("rotulo_vdp")),
        VOL(38L, Bundle.getStringApplication("rotulo_vol")),
        VOP(39L, Bundle.getStringApplication("rotulo_vop")),
        MID(40L, Bundle.getStringApplication("rotulo_mid")),
        MODV(41L, Bundle.getStringApplication("rotulo_modv")),
        MODL(42L, Bundle.getStringApplication("rotulo_modl")),
        MODP(43L, Bundle.getStringApplication("rotulo_modp")),
        VMDL(44L, Bundle.getStringApplication("rotulo_vmdl")),
        VMDP(45L, Bundle.getStringApplication("rotulo_vmdp")),
        MOVL(46L, Bundle.getStringApplication("rotulo_movl")),
        MOVP(47L, Bundle.getStringApplication("rotulo_movp")),
        DOVL(48L, Bundle.getStringApplication("rotulo_dovl")),
        DOVP(49L, Bundle.getStringApplication("rotulo_dovp")),
        MODVL(50L, Bundle.getStringApplication("rotulo_modvl")),
        MODVP(51L, Bundle.getStringApplication("rotulo_modvp")),;


        private Long value;
        private String descricao;

        private Face(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Face valueOf(Long value) {
            for (Face face : Face.values()) {
                if (face.value().equals(value)) {
                    return face;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public AtendimentoOdontoPlano () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public AtendimentoOdontoPlano (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public AtendimentoOdontoPlano (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.Dente dente,
		br.com.ksisolucoes.vo.basico.TecidosMoles tecidosMoles,
		br.com.ksisolucoes.vo.prontuario.basico.SituacaoDente situacaoDente,
		br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento,
		java.lang.Long status,
		java.util.Date dataCadastro,
		java.lang.Long flagUrgente,
		java.lang.Long tipoEvolucao) {

		super (
			codigo,
			dente,
			tecidosMoles,
			situacaoDente,
			atendimento,
			status,
			dataCadastro,
			flagUrgente,
			tipoEvolucao);
	}

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getDescricaoStatus() {
        Status status = Status.valueOf(getStatus());
        if (status != null && status.descricao != null) {
            return status.descricao();
        }
        return "";
    }
    
    public String getDescricaoUrgente() {
        if (RepositoryComponentDefault.SIM_LONG.equals(getFlagUrgente())) {
            return Bundle.getStringApplication("rotulo_sim");
        }
        return Bundle.getStringApplication("rotulo_nao");
    }

    public String getDescricaoFace() {
        Face face = Face.valueOf(getFace());
        if (face != null && face.descricao() != null) {
            return face.descricao();
        } else if (getFaces() != null) {
            return getFaces();
        }
        return null;
    }

    public String getDescricaoFaceFormatado() {
        List faces = new ArrayList();
        if (getFacePalatal() != null) { faces.add("L/P"); }
        if (getFaceVestibular() != null) { faces.add("V"); }
        if (getFaceDistal() != null) { faces.add("D"); }
        if (getFaceMesial() != null) { faces.add("M"); }
        if (getFaceOclusal() != null) { faces.add("O"); }

        if (!faces.isEmpty()) {
            return Lambda.join(faces, "-");
        } else {
            Face face = Face.valueOf(getFace());
            if (face != null && face.descricao() != null) {
                return face.descricao();
            } else if (StringUtils.trimToNull(getFaces()) != null) {
                return getFaces();
            }
        }

        return null;
    }

    public String getDescricaoLocalFormatado() {
        Tipo tipo = Tipo.valueOf(getTipoEvolucao());
        if (tipo != null) {
            StringBuilder descricao = new StringBuilder();
            if (Tipo.OUTRO.equals(tipo)) {
                descricao.append(getOutro());
            } else {
                descricao.append(tipo.descricao()).append(" ");
                if (Tipo.DENTE.equals(tipo)) {
                    descricao.append(getDente().getNome());
                    String faces = getDescricaoFaceFormatado();
                    if (faces != null) {
                        descricao.append(" (").append(faces).append(")");
                    }
                } else if (Tipo.SEXTANTE.equals(tipo)) {
                    descricao.append(getDescricaoSextanteFormatado());
                } else if (Tipo.ARCADA.equals(tipo)) {
                    descricao.append(getDescricaoArcadaFormatado());
                } else if (Tipo.TECIDOS_MOLES.equals(tipo) && getTecidosMoles() != null) {
                    descricao.append(getTecidosMoles().getDescricao());
                }
            }

            return descricao.toString();
        }

        return "";
    }

    public String getDescricaoSextanteFormatado() {
        List sextante = new ArrayList();
        if (RepositoryComponentDefault.SIM_LONG.equals(getSextanteS1())) { sextante.add("S1 (18-14)"); }
        if (RepositoryComponentDefault.SIM_LONG.equals(getSextanteS2())) { sextante.add("S2 (13-23)"); }
        if (RepositoryComponentDefault.SIM_LONG.equals(getSextanteS3())) { sextante.add("S3 (24-28)"); }
        if (RepositoryComponentDefault.SIM_LONG.equals(getSextanteS4())) { sextante.add("S4 (34-38)"); }
        if (RepositoryComponentDefault.SIM_LONG.equals(getSextanteS5())) { sextante.add("S5 (33-43)"); }
        if (RepositoryComponentDefault.SIM_LONG.equals(getSextanteS6())) { sextante.add("S6 (44-48)"); }

        if (!sextante.isEmpty()) {
            return Lambda.join(sextante, ", ");
        }
        return "";
    }

    public String getDescricaoArcadaFormatado() {
        List arcada = new ArrayList();
        if (RepositoryComponentDefault.SIM_LONG.equals(getArcadaInferior())) { arcada.add("Inferior (Mandibular)"); }
        if (RepositoryComponentDefault.SIM_LONG.equals(getArcadaSuperior())) { arcada.add("Superior (Maxilar)"); }

        if (!arcada.isEmpty()) {
            return Lambda.join(arcada, ", ");
        }
        return "";
    }

    public String getDescricaoManutencao() {
        if (RepositoryComponentDefault.SIM_LONG.equals(getFlagManutencao())) {
            return Bundle.getStringApplication("rotulo_sim");
        }
        return Bundle.getStringApplication("rotulo_nao");
    }

}
