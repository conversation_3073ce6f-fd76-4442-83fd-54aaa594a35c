package br.com.ksisolucoes.vo.vigilancia.investigacao;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.base.BaseInvestigacaoAgravoRubeolaSarampo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


public class InvestigacaoAgravoRubeolaSarampo extends BaseInvestigacaoAgravoRubeolaSarampo implements CodigoManager {
    public InvestigacaoAgravoRubeolaSarampo() { super(); }

    public InvestigacaoAgravoRubeolaSarampo(Long codigo) {
        super(codigo);
    }

    public InvestigacaoAgravoRubeolaSarampo(
            Long codigo,
            RegistroAgravo registroAgravo,
            String flagInformacoesComplementares
    ) {
        super (
            codigo,
            registroAgravo,
            flagInformacoesComplementares
        );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public void setCodigoManager(Serializable key) {
        this.setCodigo((Long) key);
    }

    public static InvestigacaoAgravoRubeolaSarampo buscaPorRegistroAgravo(RegistroAgravo registroAgravo) {
        InvestigacaoAgravoRubeolaSarampo investigacao =
                LoadManager.getInstance(InvestigacaoAgravoRubeolaSarampo.class)
                        .addProperties(new HQLProperties(InvestigacaoAgravoRubeolaSarampo.class).getProperties())
                        .addProperties(new HQLProperties(
                                RegistroAgravo.class,
                                VOUtils.montarPath(InvestigacaoAgravoRubeolaSarampo.PROP_REGISTRO_AGRAVO)).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(
                                VOUtils.montarPath(InvestigacaoAgravoRubeolaSarampo.PROP_REGISTRO_AGRAVO, RegistroAgravo.PROP_CODIGO),
                                registroAgravo.getCodigo()))
                        .start().getVO();
        return investigacao;
    }

    public List<InvestigacaoAgravoRubeolaSarampoDeslocamento> getLocaisDeslocamentoByIdInvestigacaoRubeolaSarampo() {
        if (getCodigo() != null) {
            List<InvestigacaoAgravoRubeolaSarampoDeslocamento> list =
                    LoadManager.getInstance(InvestigacaoAgravoRubeolaSarampoDeslocamento.class)
                            .addProperties(new HQLProperties(InvestigacaoAgravoRubeolaSarampoDeslocamento.class).getProperties())
                            .addProperty(VOUtils.montarPath(InvestigacaoAgravoRubeolaSarampoDeslocamento.PROP_CIDADE_DESLOCAMENTO, Cidade.PROP_ESTADO, Estado.PROP_SIGLA))
                            .addProperties(new HQLProperties(
                                    InvestigacaoAgravoRubeolaSarampo.class,
                                    VOUtils.montarPath(InvestigacaoAgravoRubeolaSarampoDeslocamento.PROP_INVESTIGACAO_AGRAVO_RUBEOLA_SARAMPO)).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(
                                    VOUtils.montarPath(InvestigacaoAgravoRubeolaSarampoDeslocamento.PROP_INVESTIGACAO_AGRAVO_RUBEOLA_SARAMPO, InvestigacaoAgravoRubeolaSarampo.PROP_CODIGO),
                                    getCodigo()))
                            .start().getList();
            return list;
        }
        return new ArrayList<InvestigacaoAgravoRubeolaSarampoDeslocamento>();
    }
}
