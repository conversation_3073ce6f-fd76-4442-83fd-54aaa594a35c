<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.geral.cnes"  >
    <class name="CnesProcessoEmpresa" table="cnes_processo_empresa" >
        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_cnes_processo_empresa"
        >
            <generator class="assigned" />
        </id>

        <version column="version" name="version" type="long" />

        <many-to-one
                class="br.com.ksisolucoes.vo.geral.cnes.CnesProcesso"
                name="cnesProcesso"
                not-null="true"
        >
            <column name="cd_cnes_processo"/>
        </many-to-one>

        <property
                name="unidadeId"
                column="unidade_id"
                type="java.lang.String"
                not-null="true"
                length="50"
        />

        <property
                name="cnes"
                column="cnes"
                type="java.lang.String"
                not-null="false"
                length="7"
        />

        <property
                name="cnpjMantenedora"
                column="cnpj_mant"
                type="java.lang.String"
                not-null="false"
                length="15"
        />

        <property
                name="pessoaFisicaJuridicaIdentificador"
                column="pfpj_ind"
                type="java.lang.String"
                not-null="false"
                length="1"
        />

        <property
                name="nivelDep"
                column="nivel_dep"
                type="java.lang.String"
                not-null="false"
                length="1"
        />

        <property
                name="razaoSocial"
                column="razao_social"
                type="java.lang.String"
                not-null="true"
                length="100"
        />

        <property
                name="nomeFantasia"
                column="nome_fantasia"
                type="java.lang.String"
                not-null="true"
                length="100"
        />

        <property
                name="logradouro"
                column="logradouro"
                type="java.lang.String"
                not-null="true"
                length="100"
        />

        <property
                name="numero"
                column="numero"
                type="java.lang.String"
                not-null="false"
                length="15"
        />

        <property
                name="complemento"
                column="complemento"
                type="java.lang.String"
                not-null="false"
                length="30"
        />

        <property
                name="bairro"
                column="bairro"
                type="java.lang.String"
                not-null="true"
                length="100"
        />

        <property
                name="cep"
                column="cep"
                type="java.lang.String"
                not-null="true"
                length="15"
        />

        <property
                name="regiaoSaude"
                column="reg_saude"
                type="java.lang.String"
                not-null="false"
                length="10"
        />

        <property
                name="microRegiaoSaude"
                column="micro_reg"
                type="java.lang.String"
                not-null="false"
                length="10"
        />

        <property
                name="distritoSanitario"
                column="dist_sanit"
                type="java.lang.String"
                not-null="false"
                length="10"
        />

        <property
                name="distritoAdministrativo"
                column="dist_admin"
                type="java.lang.String"
                not-null="false"
                length="10"
        />

        <property
                name="telefone"
                column="telefone"
                type="java.lang.String"
                not-null="false"
                length="15"
        />

        <property
                name="fax"
                column="fax"
                type="java.lang.String"
                not-null="false"
                length="15"
        />

        <property
                name="email"
                column="email"
                type="java.lang.String"
                not-null="false"
                length="100"
        />

        <property
                name="cpf"
                column="cpf"
                type="java.lang.String"
                not-null="false"
                length="11"
        />

        <property
                name="cnpj"
                column="cnpj"
                type="java.lang.String"
                not-null="false"
                length="14"
        />

        <property
                name="codigoNaturezaJuridica"
                column="cod_natureza_jur"
                type="java.lang.String"
                not-null="false"
                length="10"
        />

        <property
                name="codigoAtividade"
                column="cod_ativ"
                type="java.lang.String"
                not-null="false"
                length="3"
        />

        <property
                name="codigoCliente"
                column="cod_cliente"
                type="java.lang.String"
                not-null="false"
                length="2"
        />

        <property
                name="numeroAlvara"
                column="num_alvara"
                type="java.lang.String"
                not-null="false"
                length="60"
        />

        <property
                name="dataExpedicaoAlvara"
                column="dt_expedicao_alvara"
                type="java.util.Date"
                not-null="false"
        />

        <property
                name="orgaoExpedidor"
                column="ind_orgexp"
                type="java.lang.String"
                not-null="false"
                length="2"
        />

        <property
                name="tipoUnidadeId"
                column="tp_unid_id"
                type="java.lang.String"
                not-null="false"
                length="2"
        />

        <property
                name="codigoTurnoAtendimento"
                column="cod_turnat"
                type="java.lang.String"
                not-null="false"
                length="2"
        />

        <property
                name="siglaEstado"
                column="sigestgest"
                type="java.lang.String"
                not-null="false"
                length="2"
        />

        <property
                name="codigoMunicipio"
                column="cod_mungest"
                type="java.lang.String"
                not-null="false"
                length="10"
        />

        <property
                name="statusMovimentacao"
                column="status_mov"
                type="java.lang.String"
                not-null="true"
                length="1"
        />

        <property
                name="dataAtualizacao"
                column="dt_atualizacao"
                type="java.util.Date"
                not-null="true"
        />

        <property
                name="usuarioAtualizacao"
                column="usuario_atualizacao"
                type="java.lang.String"
                not-null="true"
                length="20"
        />

    </class>
</hibernate-mapping>