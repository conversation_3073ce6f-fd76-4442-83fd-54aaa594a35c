<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
	
<hibernate-mapping package="br.com.ksisolucoes.vo.basico"  >
    <class 
        name="Doenca"
        table="doenca"
    >
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_doenca"
        >
            <generator class="assigned"/>
        </id> 
        <version column="version" name="version" type="long" />

        <property
            name="descricao"
            column="ds_doenca"
            type="java.lang.String"
            not-null="true"
            length="50"
		/>

        <many-to-one
            name="cid"
            class="br.com.ksisolucoes.vo.prontuario.basico.Cid"
            not-null="false"
        >
            <column name="cd_cid"/>
        </many-to-one>
        
        <property
            name="condicaoEsus"
            column="cd_condicao_esus"
            type="java.lang.Long"
            not-null="false"
		/>
        
        <property
            name="sigla"
            column="sigla"
            type="java.lang.String"
            length="4"
            not-null="true"
		/>
        
        <many-to-one
            name="doencaPrincipal"
            class="br.com.ksisolucoes.vo.basico.Doenca"
            not-null="false"
        >
            <column name="cd_doenca_principal"/>
        </many-to-one>

		<property
            name="padrao"
            column="padrao"
            type="java.lang.String"
            not-null="true"
		/>      
    </class>
</hibernate-mapping>