package br.com.ksisolucoes.vo.controle.web.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the programa_favorito table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="programa_favorito"
 */

public abstract class BaseProgramaFavorito extends BaseRootVO implements Serializable {

	public static String REF = "ProgramaFavorito";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_INDICE = "indice";
	public static final String PROP_PROGRAMA_WEB = "programaWeb";


	// constructors
	public BaseProgramaFavorito () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseProgramaFavorito (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseProgramaFavorito (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.controle.web.ProgramaWeb programaWeb,
		java.lang.Long indice) {

		this.setCodigo(codigo);
		this.setUsuario(usuario);
		this.setProgramaWeb(programaWeb);
		this.setIndice(indice);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long indice;

	// many to one
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.controle.web.ProgramaWeb programaWeb;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  column="cd_prg_favorito"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: indice
	 */
	public java.lang.Long getIndice () {
		return getPropertyValue(this, indice, PROP_INDICE); 
	}

	/**
	 * Set the value related to the column: indice
	 * @param indice the indice value
	 */
	public void setIndice (java.lang.Long indice) {
//        java.lang.Long indiceOld = this.indice;
		this.indice = indice;
//        this.getPropertyChangeSupport().firePropertyChange ("indice", indiceOld, indice);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_prg_web
	 */
	public br.com.ksisolucoes.vo.controle.web.ProgramaWeb getProgramaWeb () {
		return getPropertyValue(this, programaWeb, PROP_PROGRAMA_WEB); 
	}

	/**
	 * Set the value related to the column: cd_prg_web
	 * @param programaWeb the cd_prg_web value
	 */
	public void setProgramaWeb (br.com.ksisolucoes.vo.controle.web.ProgramaWeb programaWeb) {
//        br.com.ksisolucoes.vo.controle.web.ProgramaWeb programaWebOld = this.programaWeb;
		this.programaWeb = programaWeb;
//        this.getPropertyChangeSupport().firePropertyChange ("programaWeb", programaWebOld, programaWeb);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.controle.web.ProgramaFavorito)) return false;
		else {
			br.com.ksisolucoes.vo.controle.web.ProgramaFavorito programaFavorito = (br.com.ksisolucoes.vo.controle.web.ProgramaFavorito) obj;
			if (null == this.getCodigo() || null == programaFavorito.getCodigo()) return false;
			else return (this.getCodigo().equals(programaFavorito.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}