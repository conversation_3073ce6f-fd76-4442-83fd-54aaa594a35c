<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.investigacao"  >
    <class name="InvestigacaoAgravoAcidenteTrabalhoGrave" table="investigacao_agr_acidente_trabalho_grave">

        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_invest_agr_acidente_trabalho_grave">
            <generator class="assigned" />
        </id>

        <version column="version" name="version" type="long" />

        <many-to-one class="br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo"
                     name="registroAgravo" not-null="true">
            <column name="cd_registro_agravo" />
        </many-to-one>

        <property
                name="flagInformacoesComplementares"
                column="flag_informacoes_complementares"
                type="java.lang.String"
                not-null="true"
        />

        <!-- Antecedentes epidemiologicos -->
        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"
                name="ocupacao" not-null="false">
            <column name="ocupacao"/>
        </many-to-one>

        <property
                name="situacaoMercadoTrab"
                column="situacao_mercado_trab"
                type="java.lang.Long"
        />

        <property
                name="temposTrabOcup"
                column="tempos_trab_ocup"
                type="java.lang.Long"
        />

        <property
                name="temposTrabOcupUnidade"
                column="tempos_trab_ocup_unidade"
                type="java.lang.Long"
        />

        <property
                name="localAcidente"
                column="local_acidente"
                type="java.lang.Long"
        />

        <property
                name="cpfCnpj"
                column="cpf_cnpj"
                type="java.lang.String"
        />

        <property
                name="nomeEmpresaEmpregador"
                column="nome_empresa_empregador"
                type="java.lang.String"
        />

        <property
                name="cnae"
                column="cnae"
                type="java.lang.String"
        />

        <many-to-one class="br.com.ksisolucoes.vo.basico.Cidade"
                     name="cidadeEmpresa">
            <column name="cidade_empresa"/>
        </many-to-one>

        <property
                name="bairroEmpresa"
                column="bairro_empresa"
                type="java.lang.String"
        />

        <property
                name="enderecoEmpresa"
                column="endereco_empresa"
                type="java.lang.String"
        />

        <property
                name="numeroEmpresa"
                column="numero_empresa"
                type="java.lang.String"
        />

        <property
                name="pontoReferenciaEmpresa"
                column="ponto_referencia_empresa"
                type="java.lang.String"
        />

        <property
                name="telefoneEmpresa"
                column="telefone_empresa"
                type="java.lang.String"
        />

        <property
                name="empreendedorTerceirizado"
                column="empreendedor_terceirizado"
                type="java.lang.Long"
        />

        <property
                name="cnaeEmpresaPrincipal"
                column="cnae_empresa_principal"
                type="java.lang.String"
        />

        <property
                name="cnpjEmpresaPrincipal"
                column="cnpj_empresa_principal"
                type="java.lang.String"
        />

        <property
                name="nomeEmpresaPrincipal"
                column="nome_empresa_principal"
                type="java.lang.String"
        />

        <!-- Dados do acidente -->
        <property
                name="horaAcidente"
                column="hora_acidente"
                type="java.lang.String"
        />

        <property
                name="horasAposInicioJornada"
                column="hora_apos_inicio_jornada"
                type="java.lang.String"
        />

        <many-to-one class="br.com.ksisolucoes.vo.basico.Cidade"
                     name="cidadeAcidente">
            <column name="cidade_acidente"/>
        </many-to-one>

        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.Cid"
                     name="cidAcidente">
            <column name="cd_cid_acidente" />
        </many-to-one>

        <property
                name="tipoAcidente"
                column="tipo_acidente"
                type="java.lang.Long"
        />

        <property
                name="outrosAtingidos"
                column="outros_atingidos"
                type="java.lang.Long"
        />

        <property
                name="quantidadeOutrosAtingidos"
                column="quantidade_outros_atingidos"
                type="java.lang.Long"
        />

        <!-- Dados do atendimento médico -->
        <property
                name="ocorreuAtendimentoMedico"
                column="ocorreu_atendimento_medico"
                type="java.lang.Long"
        />

        <property
                name="dataAtendimentoMedico"
                column="data_atendimento_medico"
                type="timestamp"
        />

        <many-to-one class="br.com.ksisolucoes.vo.basico.Empresa"
                     name="unidadeSaudeAtendimento">
            <column name="unidade_saude_atendimento"/>
        </many-to-one>

        <property
                name="partesCorpoAtingida1"
                column="partes_corpo_atingida_1"
                type="java.lang.Long"
        />

        <property
                name="partesCorpoAtingida2"
                column="partes_corpo_atingida_2"
                type="java.lang.Long"
        />

        <property
                name="partesCorpoAtingida3"
                column="partes_corpo_atingida_3"
                type="java.lang.Long"
        />

        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.Cid"
                     name="cidAtendimentoMedico">
            <column name="cd_cid_atendimento_medico" />
        </many-to-one>

        <property
                name="regimeTratamento"
                column="regime_tratamento"
                type="java.lang.Long"
        />

        <!-- Conclusão -->
        <property
                name="evolucaoCaso"
                column="evolucao_caso"
                type="java.lang.Long"
        />

        <property
                name="dataObito"
                column="data_obito"
                type="timestamp"
        />

        <property
                name="emissaoCat"
                column="emissao_cat"
                type="java.lang.Long"
        />

        <property
                name="descSumAcid"
                column="desc_sum_acid"
                type="java.lang.String"
        />

        <property
                name="outrasInfo"
                column="outras_info"
                type="java.lang.String"
        />
    </class>
</hibernate-mapping>
