<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico">
    <class name="ConfiguracaoEstratificacao" table="configuracao_estratificacao">

        <id
            column="cd_configuracao_estratificacao"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned"/>
        </id>

        <version column="version" name="version" type="long"/>

        <property
            name="formulario"
            type="java.lang.Long"
            column="formulario"
            not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Doenca"
                name="doencaAltoRisco"
                column="cd_doenca_alto_risco"
        />
        
        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Doenca"
                name="doencaMedioRisco"
                column="cd_doenca_medio_risco"
        />
        
        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Doenca"
                name="doencaBaixoRisco"
                column="cd_doenca_baixo_risco"
        />

        <property
            name="situacao"
            column="situacao"
            type="java.lang.Long"
            not-null="true"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            name="usuario"
        >
            <column name="cd_usuario" not-null="true"/>
        </many-to-one>

        <property
            name="dataCadastro"
            type="timestamp"
            column="dt_cadastro"
            not-null="true"
        />

    </class>
</hibernate-mapping>
