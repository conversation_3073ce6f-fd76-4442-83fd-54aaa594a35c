<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.requerimentos">
    <class name="RequerimentoProjetoHidrossanitarioAnexo" table="requerimento_projeto_hidro_anexo">
        <id
                column="cd_req_proj_hidro_anexo"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="sequence">
                <param name="sequence">seq_requerimento_projeto_hidro_anexo</param>
            </generator>
        </id>
        <version column="version" name="version" type="long"/>

        <many-to-one
                class="br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo"
                column="cd_gerenciador_arquivo"
                name="gerenciadorArquivo"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitario"
                column="cd_req_projeto_hidro"
                name="requerimentoProjetoHidrossanitario"
                not-null="true"
        />

        <property
                column="situacao"
                name="situacao"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                column="descricao"
                name="descricao"
                type="java.lang.String"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario"
                name="usuario"
                not-null="true"
        />

        <property
                name="dataCadastro"
                column="dt_cadastro"
                type="timestamp"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario_alteracao"
                name="usuarioAlteracao"
                not-null="true"
        />

        <property
                name="dataAlteracao"
                column="dt_alteracao"
                type="timestamp"
                not-null="true"
        />

    </class>
</hibernate-mapping>
