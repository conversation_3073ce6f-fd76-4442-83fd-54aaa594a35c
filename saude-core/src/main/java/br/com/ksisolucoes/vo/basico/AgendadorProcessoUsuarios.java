package br.com.ksisolucoes.vo.basico;

import java.io.Serializable;

import br.com.ksisolucoes.vo.basico.base.BaseAgendadorProcessoUsuarios;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class AgendadorProcessoUsuarios extends BaseAgendadorProcessoUsuarios implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public AgendadorProcessoUsuarios () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public AgendadorProcessoUsuarios (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public AgendadorProcessoUsuarios (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.AgendadorProcesso agendadorProcesso,
		br.com.ksisolucoes.vo.controle.Usuario usuario) {

		super (
			codigo,
			agendadorProcesso,
			usuario);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}