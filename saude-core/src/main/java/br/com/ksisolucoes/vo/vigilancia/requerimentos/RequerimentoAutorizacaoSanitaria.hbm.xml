<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.requerimentos">
    <class name="RequerimentoAutorizacaoSanitaria" table="requerimento_autorizacao_sanitaria">
        <id
                column="cd_requerimento_autorizacao_sanitaria"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="assigned"/>
        </id>
        <version column="version" name="version" type="long"/>

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia"
                column="cd_req_vigilancia"
                name="requerimentoVigilancia"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.Estabelecimento"
                column="cd_estabelecimento"
                name="estabelecimento"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario"
                not-null="true"
                name="usuario"
        />

        <property
                name="dataCadastro"
                column="dt_cadastro"
                type="timestamp"
                not-null="true"
        />

        <property
                name="dataValidade"
                column="dt_validade"
                type="timestamp"
                not-null="true"
        />

        <property
                column="numero"
                name="numero"
                not-null="true"
                type="java.lang.Long"
        />

        <property
                column="ano_base"
                name="anoBase"
                type="java.lang.Long"
                not-null="false"
        />

        <property
                column="estrutura"
                name="estrutura"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                column="ds_comercializacao"
                name="descricaoComercializacao"
                type="java.lang.String"
                not-null="true"
        />

        <property
                column="ds_observacao_destaque"
                name="descricaoObservacaoDestaque"
                type="java.lang.String"
                not-null="true"
        />

        <property
                name="observacaoEstrutura"
                column="observacao_estrutura"
                type="java.lang.String"
                length="50"
        />

    </class>
</hibernate-mapping>
