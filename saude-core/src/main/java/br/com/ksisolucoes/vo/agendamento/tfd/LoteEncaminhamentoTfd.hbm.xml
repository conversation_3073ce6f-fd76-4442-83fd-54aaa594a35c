<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.agendamento.tfd"  >
    <class name="LoteEncaminhamentoTfd" table="lote_encaminhamento_tfd" >

        <id
            name="codigo"
            type="java.lang.Long"   
            column="cd_lote_encaminhamento"
        > 
            <generator class="sequence">
                <param name="sequence">seq_lote_encaminhamento_tfd</param>
            </generator>
        </id> 
        <version column="version" name="version" type="long" />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            name="usuario"
        >
            <column name="cd_usuario"/>
        </many-to-one>
         
        <many-to-one
            class="br.com.ksisolucoes.vo.basico.RegionalSaude"
            name="regionalSaude"
        >
            <column name="cd_regional"/>
        </many-to-one>
        
        <property
            name="dataEncaminhamento"
            type="timestamp"
            column="dt_encaminhamento"
        >
        </property>
        
        <property
            name="responsavel"
            type="java.lang.String"
            column="responsavel"
            length="100"
        >
        </property>
        
    </class>
</hibernate-mapping>
