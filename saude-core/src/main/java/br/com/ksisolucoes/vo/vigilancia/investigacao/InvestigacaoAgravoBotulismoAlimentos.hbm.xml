<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.investigacao">
    <class
            name="InvestigacaoAgravoBotulismoAlimentos"
            table="investigacao_agr_botulismo_alimentos"
    >

        <id name="codigo"
            type="java.lang.Long"
            column="cd_botulismo_alimentos">
            <generator class="sequence">
                <param name="sequence">seq_investigacao_agr_botulismo_alimentos</param>
            </generator>
        </id>
        <version column="version" name="version" type="long"/>

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoBotulismo"
                column="cd_investigacao_agr_botulismo"
                name="investigacaoAgravoBotulismo"
                not-null="true"
        />

        <property
                name="tipoAlimento"
                column="tipo_alimento"
                type="java.lang.String"
        />
        <property
                name="localConsumo"
                column="local_consumo"
                type="java.lang.String"
        />

    </class>
</hibernate-mapping>