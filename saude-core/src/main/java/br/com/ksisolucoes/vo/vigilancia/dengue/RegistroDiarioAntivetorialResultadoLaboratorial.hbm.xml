<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.dengue"  >
    <class name="RegistroDiarioAntivetorialResultadoLaboratorial" table="registro_diario_antivetorial_result_lab">

        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_reg_diario_vet_result_lab"
        >
            <generator class="assigned" />
        </id> 

        <version column="version" name="version" type="long" />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.dengue.RegistroDiarioAntivetorialPesquisa"
            column="cd_reg_diario_vet_peq"
            name="registroDiarioAntivetorialPesquisa"
            not-null="true"
        />

        <property
            name="aedesAegyptiA1"
            column="a1_aed_aegypti"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="albopictusA1"
            column="a1_albopictus"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="outrosA1"
            column="a1_outros"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="aedesAegyptiA2"
            column="a2_aed_aegypti"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="albopictusA2"
            column="a2_albopictus"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="outrosA2"
            column="a2_outros"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="aedesAegyptiB"
            column="b_aed_aegypti"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="albopictusB"
            column="b_albopictus"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="outrosB"
            column="b_outros"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="aedesAegyptiC"
            column="c_aed_aegypti"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="albopictusC"
            column="c_albopictus"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="outrosC"
            column="c_outros"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="aedesAegyptiD1"
            column="d1_aed_aegypti"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="albopictusD1"
            column="d1_albopictus"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="outrosD1"
            column="d1_outros"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="aedesAegyptiD2"
            column="d2_aed_aegypti"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="albopictusD2"
            column="d2_albopictus"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="outrosD2"
            column="d2_outros"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="aedesAegyptiE"
            column="e_aed_aegypti"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="albopictusE"
            column="e_albopictus"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="outrosE"
            column="e_outros"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="aedesAegyptiLarvas"
            column="larvas_aed_aegypti"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="albopictusLarvas"
            column="larvas_albopictus"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="outrosLarvas"
            column="larvas_outros"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="aedesAegyptiPupas"
            column="pupas_aed_aegypti"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="albopictusPupas"
            column="pupas_albopictus"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="outrosPupas"
            column="pupas_outros"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="aedesAegyptiExuviaPupas"
            column="exuvia_pupas_aed_aegypti"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="albopictusExuviaPupas"
            column="exuvia_pupas_albopictus"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="outrosExuviaPupas"
            column="exuvia_pupas_outros"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="aedesAegyptiAdultosPupas"
            column="adultos_pupas_aed_aegypti"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="albopictusAdultosPupas"
            column="adultos_pupas_albopictus"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="outrosAdultosPupas"
            column="adultos_pupas_outros"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            name="dataCadastro"
            column="dt_cadastro"
            type="timestamp"
            not-null="true"
        />

        <property
            name="dataUsuario"
            column="dt_usuario"
            type="timestamp"
            not-null="true"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuario"
            not-null="true"
        />
    </class>
</hibernate-mapping>
