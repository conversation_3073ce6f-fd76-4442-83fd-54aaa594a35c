package br.com.ksisolucoes.vo.enderecoestruturado;

import br.com.ksisolucoes.vo.enderecoestruturado.base.BaseEnderecoEstruturadoLogradouro;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;

import java.io.Serializable;


public class EnderecoEstruturadoLogradouro extends BaseEnderecoEstruturadoLogradouro implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EnderecoEstruturadoLogradouro () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public EnderecoEstruturadoLogradouro (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public EnderecoEstruturadoLogradouro (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuarioAlteracao,
		br.com.ksisolucoes.vo.cadsus.TipoLogradouroCadsus tipoLogradouro) {

		super (
			codigo,
			usuarioAlteracao,
			tipoLogradouro);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

	@Override
	public String getDescricaoVO() {
		return this.getDescricao();
	}

	@Override
	public String getIdentificador() {
		return this.getCodigo().toString();
	}

    @Override
    public String getDescricao() {
        return lgpdFilterText(super.getDescricao());
    }
    
}