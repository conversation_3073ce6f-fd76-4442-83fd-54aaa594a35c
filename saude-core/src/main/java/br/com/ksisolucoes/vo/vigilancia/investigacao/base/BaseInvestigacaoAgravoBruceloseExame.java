package br.com.ksisolucoes.vo.vigilancia.investigacao.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the investigacao_agr_brucelose_exame table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agr_brucelose_exame"
 */

public abstract class BaseInvestigacaoAgravoBruceloseExame extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravoBruceloseExame";
	public static final String PROP_DATA_EXAME = "dataExame";
	public static final String PROP_EXAME = "exame";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_INVESTIGACAO_AGRAVO_BRUCELOSE = "investigacaoAgravoBrucelose";
	public static final String PROP_RESULTADO_EXAME = "resultadoExame";


	// constructors
	public BaseInvestigacaoAgravoBruceloseExame () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravoBruceloseExame (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String exame;
	private java.util.Date dataExame;
	private java.lang.String resultadoExame;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoBrucelose investigacaoAgravoBrucelose;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_invest_agr_brucelose_exame"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: txt_exame
	 */
	public java.lang.String getExame () {
		return getPropertyValue(this, exame, PROP_EXAME); 
	}

	/**
	 * Set the value related to the column: txt_exame
	 * @param exame the txt_exame value
	 */
	public void setExame (java.lang.String exame) {
//        java.lang.String exameOld = this.exame;
		this.exame = exame;
//        this.getPropertyChangeSupport().firePropertyChange ("exame", exameOld, exame);
	}



	/**
	 * Return the value associated with the column: dt_exame
	 */
	public java.util.Date getDataExame () {
		return getPropertyValue(this, dataExame, PROP_DATA_EXAME); 
	}

	/**
	 * Set the value related to the column: dt_exame
	 * @param dataExame the dt_exame value
	 */
	public void setDataExame (java.util.Date dataExame) {
//        java.util.Date dataExameOld = this.dataExame;
		this.dataExame = dataExame;
//        this.getPropertyChangeSupport().firePropertyChange ("dataExame", dataExameOld, dataExame);
	}



	/**
	 * Return the value associated with the column: txt_resultado_exame
	 */
	public java.lang.String getResultadoExame () {
		return getPropertyValue(this, resultadoExame, PROP_RESULTADO_EXAME); 
	}

	/**
	 * Set the value related to the column: txt_resultado_exame
	 * @param resultadoExame the txt_resultado_exame value
	 */
	public void setResultadoExame (java.lang.String resultadoExame) {
//        java.lang.String resultadoExameOld = this.resultadoExame;
		this.resultadoExame = resultadoExame;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoExame", resultadoExameOld, resultadoExame);
	}



	/**
	 * Return the value associated with the column: cd_invest_agr_brucelose
	 */
	public br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoBrucelose getInvestigacaoAgravoBrucelose () {
		return getPropertyValue(this, investigacaoAgravoBrucelose, PROP_INVESTIGACAO_AGRAVO_BRUCELOSE); 
	}

	/**
	 * Set the value related to the column: cd_invest_agr_brucelose
	 * @param investigacaoAgravoBrucelose the cd_invest_agr_brucelose value
	 */
	public void setInvestigacaoAgravoBrucelose (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoBrucelose investigacaoAgravoBrucelose) {
//        br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoBrucelose investigacaoAgravoBruceloseOld = this.investigacaoAgravoBrucelose;
		this.investigacaoAgravoBrucelose = investigacaoAgravoBrucelose;
//        this.getPropertyChangeSupport().firePropertyChange ("investigacaoAgravoBrucelose", investigacaoAgravoBruceloseOld, investigacaoAgravoBrucelose);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoBruceloseExame)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoBruceloseExame investigacaoAgravoBruceloseExame = (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoBruceloseExame) obj;
			if (null == this.getCodigo() || null == investigacaoAgravoBruceloseExame.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravoBruceloseExame.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}