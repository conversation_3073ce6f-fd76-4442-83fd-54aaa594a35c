package br.com.ksisolucoes.vo.vigilancia.requerimentos;

import java.io.Serializable;

import br.com.ksisolucoes.vo.vigilancia.requerimentos.base.BaseRequerimentoVistoriaHidrossanitarioDeclaratorioParecerFiscal;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class RequerimentoVistoriaHidrossanitarioDeclaratorioParecerFiscal extends BaseRequerimentoVistoriaHidrossanitarioDeclaratorioParecerFiscal implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequerimentoVistoriaHidrossanitarioDeclaratorioParecerFiscal () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequerimentoVistoriaHidrossanitarioDeclaratorioParecerFiscal (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequerimentoVistoriaHidrossanitarioDeclaratorioParecerFiscal (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaHidrossanitarioDeclaratorioParecer requerimentoVistoriaHidrossanitarioDeclaratorioParecer,
		br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaFiscal requerimentoVigilanciaFiscal) {

		super (
			codigo,
			requerimentoVistoriaHidrossanitarioDeclaratorioParecer,
			requerimentoVigilanciaFiscal);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}