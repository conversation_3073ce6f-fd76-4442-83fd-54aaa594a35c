package br.com.ksisolucoes.vo.entradas.estoque;

import java.io.Serializable;

import br.com.ksisolucoes.vo.entradas.estoque.base.BaseEloProdutoBrasindice;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class EloProdutoBrasindice extends BaseEloProdutoBrasindice implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EloProdutoBrasindice () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public EloProdutoBrasindice (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public EloProdutoBrasindice (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.entradas.estoque.Produto produto,
		br.com.ksisolucoes.vo.entradas.estoque.ProdutoBrasindice produtoBrasindice) {

		super (
			codigo,
			produto,
			produtoBrasindice);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getDescricaoTipoFatorConversao() {
        ProdutoBrasindice.TipoFatorConversao tipoFatorConversao = ProdutoBrasindice.TipoFatorConversao.valueOf(getTipoFatorConversao());

        if (tipoFatorConversao != null) {
            return tipoFatorConversao.descricao();
        }

        return null;
    }

}