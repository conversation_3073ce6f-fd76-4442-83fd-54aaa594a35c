<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario"  >
    <class name="DrogaUso" table="droga_uso">
        <id
            column="cd_droga_uso"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.FichaAcolhimento"
            column="cd_ficha_acolhimento"
            name="fichaAcolhimento"
            not-null="true"
        />
                 
        <property
            column="tipo_droga"
            name="tipoDroga"
            not-null="true"
            type="java.lang.Long"
        />        
        
        <property
            column="outro_descricao"
            name="outroDescricao"
            type="java.lang.String"
        />        
        
        <property
            column="ultimo_uso"
            name="ultimoUso"
            type="java.lang.String"
        />        
        
        <property
            column="quantidade_uso"
            name="quantidadeUso"
            type="java.lang.String"
        />        
        
        <property
            column="tempo_uso"
            name="tempoUso"
            type="java.lang.String"
        />        
        
        <property
            column="overdose"
            name="overdose"
            type="java.lang.String"
        />        
        
        <property
            column="regularidade_uso"
            name="regularidadeUso"
            type="java.lang.String"
        />        
        
        <property
            column="reacao"
            name="reacao"
            type="java.lang.String"
        />        
        
    </class>
</hibernate-mapping>
