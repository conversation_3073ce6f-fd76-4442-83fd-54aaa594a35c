package br.com.ksisolucoes.vo.vigilancia.autointimacao;

import java.io.Serializable;

import br.com.ksisolucoes.vo.vigilancia.autointimacao.base.BaseAutoIntimacaoProrogacao;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class AutoIntimacaoProrogacao extends BaseAutoIntimacaoProrogacao implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public AutoIntimacaoProrogacao () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public AutoIntimacaoProrogacao (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public AutoIntimacaoProrogacao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao autoIntimacao,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataProrogada,
		java.util.Date dataUsuario) {

		super (
			codigo,
			autoIntimacao,
			usuario,
			dataProrogada,
			dataUsuario);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}