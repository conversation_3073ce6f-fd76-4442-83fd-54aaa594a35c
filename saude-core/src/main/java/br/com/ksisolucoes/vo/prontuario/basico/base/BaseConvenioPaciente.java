package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the convenio_paciente table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="convenio_paciente"
 */

public abstract class BaseConvenioPaciente extends BaseRootVO implements Serializable {

	public static String REF = "ConvenioPaciente";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_NUMERO_REGISTRO = "numeroRegistro";
	public static final String PROP_CONVENIO = "convenio";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";


	// constructors
	public BaseConvenioPaciente () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseConvenioPaciente (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseConvenioPaciente (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		br.com.ksisolucoes.vo.prontuario.basico.Convenio convenio) {

		this.setCodigo(codigo);
		this.setUsuarioCadsus(usuarioCadsus);
		this.setConvenio(convenio);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String numeroRegistro;

	// many to one
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private br.com.ksisolucoes.vo.prontuario.basico.Convenio convenio;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_convenio_paciente"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: numero_registro
	 */
	public java.lang.String getNumeroRegistro () {
		return getPropertyValue(this, numeroRegistro, PROP_NUMERO_REGISTRO); 
	}

	/**
	 * Set the value related to the column: numero_registro
	 * @param numeroRegistro the numero_registro value
	 */
	public void setNumeroRegistro (java.lang.String numeroRegistro) {
//        java.lang.String numeroRegistroOld = this.numeroRegistro;
		this.numeroRegistro = numeroRegistro;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroRegistro", numeroRegistroOld, numeroRegistro);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cd_convenio
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Convenio getConvenio () {
		return getPropertyValue(this, convenio, PROP_CONVENIO); 
	}

	/**
	 * Set the value related to the column: cd_convenio
	 * @param convenio the cd_convenio value
	 */
	public void setConvenio (br.com.ksisolucoes.vo.prontuario.basico.Convenio convenio) {
//        br.com.ksisolucoes.vo.prontuario.basico.Convenio convenioOld = this.convenio;
		this.convenio = convenio;
//        this.getPropertyChangeSupport().firePropertyChange ("convenio", convenioOld, convenio);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.ConvenioPaciente)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.ConvenioPaciente convenioPaciente = (br.com.ksisolucoes.vo.prontuario.basico.ConvenioPaciente) obj;
			if (null == this.getCodigo() || null == convenioPaciente.getCodigo()) return false;
			else return (this.getCodigo().equals(convenioPaciente.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}