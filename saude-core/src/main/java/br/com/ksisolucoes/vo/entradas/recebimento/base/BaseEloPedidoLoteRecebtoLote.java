package br.com.ksisolucoes.vo.entradas.recebimento.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the elo_pedido_lote_recebto_lote table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="elo_pedido_lote_recebto_lote"
 */

public abstract class BaseEloPedidoLoteRecebtoLote extends BaseRootVO implements Serializable {

	public static String REF = "EloPedidoLoteRecebtoLote";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_QTD_TRANSFERENCIA = "qtdTransferencia";
	public static final String PROP_PEDIDO_TRANSFERENCIA_ITEM_LOTE = "pedidoTransferenciaItemLote";
	public static final String PROP_RECEBIMENTO_GRUPO_ESTOQUE = "recebimentoGrupoEstoque";


	// constructors
	public BaseEloPedidoLoteRecebtoLote () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEloPedidoLoteRecebtoLote (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseEloPedidoLoteRecebtoLote (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferenciaItemLote pedidoTransferenciaItemLote,
		br.com.ksisolucoes.vo.entradas.recebimento.RecebimentoGrupoEstoque recebimentoGrupoEstoque) {

		this.setCodigo(codigo);
		this.setPedidoTransferenciaItemLote(pedidoTransferenciaItemLote);
		this.setRecebimentoGrupoEstoque(recebimentoGrupoEstoque);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Double qtdTransferencia;

	// many to one
	private br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferenciaItemLote pedidoTransferenciaItemLote;
	private br.com.ksisolucoes.vo.entradas.recebimento.RecebimentoGrupoEstoque recebimentoGrupoEstoque;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_elo_ped_lote_rec"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: qtd_transferencia
	 */
	public java.lang.Double getQtdTransferencia () {
		return getPropertyValue(this, qtdTransferencia, PROP_QTD_TRANSFERENCIA); 
	}

	/**
	 * Set the value related to the column: qtd_transferencia
	 * @param qtdTransferencia the qtd_transferencia value
	 */
	public void setQtdTransferencia (java.lang.Double qtdTransferencia) {
//        java.lang.Double qtdTransferenciaOld = this.qtdTransferencia;
		this.qtdTransferencia = qtdTransferencia;
//        this.getPropertyChangeSupport().firePropertyChange ("qtdTransferencia", qtdTransferenciaOld, qtdTransferencia);
	}



	/**
	 * Return the value associated with the column: cd_pedido_item_lote
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferenciaItemLote getPedidoTransferenciaItemLote () {
		return getPropertyValue(this, pedidoTransferenciaItemLote, PROP_PEDIDO_TRANSFERENCIA_ITEM_LOTE); 
	}

	/**
	 * Set the value related to the column: cd_pedido_item_lote
	 * @param pedidoTransferenciaItemLote the cd_pedido_item_lote value
	 */
	public void setPedidoTransferenciaItemLote (br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferenciaItemLote pedidoTransferenciaItemLote) {
//        br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferenciaItemLote pedidoTransferenciaItemLoteOld = this.pedidoTransferenciaItemLote;
		this.pedidoTransferenciaItemLote = pedidoTransferenciaItemLote;
//        this.getPropertyChangeSupport().firePropertyChange ("pedidoTransferenciaItemLote", pedidoTransferenciaItemLoteOld, pedidoTransferenciaItemLote);
	}



	/**
	 * Return the value associated with the column: cd_rec_gru_est
	 */
	public br.com.ksisolucoes.vo.entradas.recebimento.RecebimentoGrupoEstoque getRecebimentoGrupoEstoque () {
		return getPropertyValue(this, recebimentoGrupoEstoque, PROP_RECEBIMENTO_GRUPO_ESTOQUE); 
	}

	/**
	 * Set the value related to the column: cd_rec_gru_est
	 * @param recebimentoGrupoEstoque the cd_rec_gru_est value
	 */
	public void setRecebimentoGrupoEstoque (br.com.ksisolucoes.vo.entradas.recebimento.RecebimentoGrupoEstoque recebimentoGrupoEstoque) {
//        br.com.ksisolucoes.vo.entradas.recebimento.RecebimentoGrupoEstoque recebimentoGrupoEstoqueOld = this.recebimentoGrupoEstoque;
		this.recebimentoGrupoEstoque = recebimentoGrupoEstoque;
//        this.getPropertyChangeSupport().firePropertyChange ("recebimentoGrupoEstoque", recebimentoGrupoEstoqueOld, recebimentoGrupoEstoque);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.entradas.recebimento.EloPedidoLoteRecebtoLote)) return false;
		else {
			br.com.ksisolucoes.vo.entradas.recebimento.EloPedidoLoteRecebtoLote eloPedidoLoteRecebtoLote = (br.com.ksisolucoes.vo.entradas.recebimento.EloPedidoLoteRecebtoLote) obj;
			if (null == this.getCodigo() || null == eloPedidoLoteRecebtoLote.getCodigo()) return false;
			else return (this.getCodigo().equals(eloPedidoLoteRecebtoLote.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}