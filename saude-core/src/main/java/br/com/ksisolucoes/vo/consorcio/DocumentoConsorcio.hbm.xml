<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.consorcio">
    <class name="DocumentoConsorcio" table="documento_consorcio">
        <id
            name="codigo"
            column="cd_documento"
            type="java.lang.Long"
        >
        	<generator class="sequence">
                <param name="sequence">seq_documento_consorcio</param>
            </generator>
        </id>

        <version column="version" name="version" type="long" />

		<many-to-one
            class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"
            name="usuarioCadsus"
            column="cd_usuario_cadsus"
            not-null="true"
        />
		 
		<many-to-one  
            class="br.com.ksisolucoes.vo.consorcio.ConsorcioPrestador"
            name="consorcioPrestador"
            column="cd_prestador"
            not-null="true"
		/>
        
        <many-to-one
            class="br.com.ksisolucoes.vo.consorcio.ModeloDocumentoConsorcio"
            column="cd_modelo_documento"
            name="modeloDocumentoConsorcio"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento"
            column="cd_guia"
            name="consorcioGuiaProcedimento"
        />

        <property 
            name="descricao"
            column="descricao"
            type="java.lang.String"
            not-null="true"
        />
        
        <property
            column="dt_cadastro"
            name="dataCadastro"
            type="timestamp"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuario"
        />
        
        <property
            column="dt_cancelamento"
            name="dataCancelamento"
            type="timestamp"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario_cancelamento"
            name="usuarioCancelamento"
        />

        <property
            name="status"
            column="status"
            type="java.lang.Long"
        />

    </class>
</hibernate-mapping>
