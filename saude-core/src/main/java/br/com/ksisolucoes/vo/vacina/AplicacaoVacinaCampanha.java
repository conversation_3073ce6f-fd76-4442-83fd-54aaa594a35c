package br.com.ksisolucoes.vo.vacina;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.EnumUtil;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vacina.base.BaseAplicacaoVacinaCampanha;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Objects;


public class AplicacaoVacinaCampanha extends BaseAplicacaoVacinaCampanha implements CodigoManager {
    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public AplicacaoVacinaCampanha () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public AplicacaoVacinaCampanha (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public AplicacaoVacinaCampanha (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vacina.TipoVacina tipoVacina,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.controle.Usuario usuarioConclusao,
		br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento,
		java.lang.Long status,
		java.util.Date dataCadastro,
		java.util.Date dataConclusao,
		java.util.Date dataCancelamento) {

		super (
			codigo,
			tipoVacina,
			empresa,
			usuario,
			usuarioConclusao,
			usuarioCancelamento,
			status,
			dataCadastro,
			dataConclusao,
			dataCancelamento);
	}

    /*[CONSTRUCTOR MARKER END]*/

    public boolean isNew() {
        return Objects.isNull(getCodigo());
    }

    public enum Status implements IEnum<Status> {

        PENDENTE(1L, Bundle.getStringApplication("rotulo_pendente")),
        PROCESSADO(2L, Bundle.getStringApplication("rotulo_processado")),
        CANCELADO(3L, Bundle.getStringApplication("rotulo_cancelado")),
        ;
        private Long value;
        private String descricao;

        private Status(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        public static Status valueOf(Long value) {
            for (Status ts : Status.values()) {
                if (ts.value().equals(value)) {
                    return ts;
                }
            }
            return null;
        }
    }

    public String getDescricaoStatus() {
        return new EnumUtil().resolveDescricao(Status.values(), getStatus());
    }

    public String getDescricaoTurno() {
        return new EnumUtil().resolveDescricao(VacinaAplicacao.Turno.values(), getTurno());
    }

    public String getDescricaoDoses() {
        return new EnumUtil().resolveDescricao(VacinaCalendario.Doses.values(), getVacinaCalendario().getDose());
    }

    public String getDescricaoLocalAtendimento() {
        return new EnumUtil().resolveDescricao(VacinaAplicacao.LocalAtendimento.values(), getLocalAtendimento());
    }

    public String getHoraAplicacaoInicio() {
        if (getDataAplicacaoFim() == null) return "";
        return Data.formatarHora(getDataAplicacaoFim());
    }

    public String getHoraInicio() {
        if (getDataAplicacao() != null) {
            return new SimpleDateFormat("HH:mm", Bundle.getLocale()).format(getDataAplicacao());
        }
        return "";
    }

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}