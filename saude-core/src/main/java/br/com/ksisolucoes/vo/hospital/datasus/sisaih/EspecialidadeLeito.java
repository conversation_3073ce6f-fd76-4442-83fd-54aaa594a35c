package br.com.ksisolucoes.vo.hospital.datasus.sisaih;

import java.io.Serializable;

import br.com.ksisolucoes.vo.hospital.datasus.sisaih.base.BaseEspecialidadeLeito;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;

public class EspecialidadeLeito extends BaseEspecialidadeLeito implements CodigoManager, PesquisaObjectInterface {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public EspecialidadeLeito() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public EspecialidadeLeito(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public EspecialidadeLeito(
            java.lang.Long codigo,
            java.lang.String descricao) {

        super(
                codigo,
                descricao);
    }

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    @Override
    public String getDescricaoVO() {
        return getDescricao();
    }

    @Override
    public String getIdentificador() {
        return getCodigo().toString();
    }
}
