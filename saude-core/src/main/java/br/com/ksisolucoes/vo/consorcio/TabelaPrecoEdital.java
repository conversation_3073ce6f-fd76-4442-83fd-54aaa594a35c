package br.com.ksisolucoes.vo.consorcio;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.consorcio.base.BaseTabelaPrecoEdital;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;

import java.io.Serializable;



public class TabelaPrecoEdital extends BaseTabelaPrecoEdital implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

	public enum Situacao implements IEnum {
		ATIVO(0L, Bundle.getStringApplication("rotulo_ativo")),
		INATIVO(1L, Bundle.getStringApplication("rotulo_inativo")),
		;

		private final Long value;
		private final String descricao;

		Situacao(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static Situacao valueOf(Long value) {
			for (Situacao situacao : Situacao.values()) {
				if (situacao.value().equals(value)) {
					return situacao;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}

	}

/*[CONSTRUCTOR MARKER BEGIN]*/
	public TabelaPrecoEdital () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public TabelaPrecoEdital (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public TabelaPrecoEdital (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.controle.Usuario usuarioAlteracao,
		br.com.ksisolucoes.vo.consorcio.TipoConta tipoConta,
		java.lang.String edital,
		java.util.Date dataInicio,
		java.lang.Long situacao,
		java.util.Date dataCadastro,
		java.util.Date dataAlteracao) {

		super (
			codigo,
			usuario,
			usuarioAlteracao,
			tipoConta,
			edital,
			dataInicio,
			situacao,
			dataCadastro,
			dataAlteracao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

	@Override
	public String getDescricaoVO() {
		return getEdital();
	}

	@Override
	public String getIdentificador() {
		return Coalesce.asString((Long)getCodigoManager());
	}

	public String getDescricaoSituacao(){
		Situacao situacao = Situacao.valueOf(getSituacao());
		if (situacao != null && situacao.descricao != null) {
			return situacao.descricao();
		}
		return "";
	}
}