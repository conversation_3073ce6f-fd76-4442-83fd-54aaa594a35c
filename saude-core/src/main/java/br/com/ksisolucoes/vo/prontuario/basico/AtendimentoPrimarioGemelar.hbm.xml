<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="AtendimentoPrimarioGemelar" table="atendimento_primario_gemelar" >

        <id
                column="cd_atendimento_primario_gemelar"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="assigned" />
        </id>
        
        <version column="version" name="version" type="long" />
  
        <many-to-one
            name="atendimentoPrimario"
            class="br.com.ksisolucoes.vo.prontuario.basico.AtendimentoPrimario"
            not-null="true"
        >
        <column name="cd_atendimento_primario"/>
        </many-to-one>
  
        <property
            column="bcf"
            name="bcf"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            column="mov_fetal"
            name="movimentacaoFetal"
            type="java.lang.Long"
            not-null="false"
        />
        <property
            column="altura"
            name="altura"
            type="java.lang.Double"
            not-null="false"
        />
        <property
            column="peso"
            name="peso"
            type="java.lang.Double"
            not-null="false"
        />

        <property
            column="observacao"
            name="observacao"
            type="java.lang.String"
            length="100"
            not-null="false"
        />
    </class>
</hibernate-mapping>
