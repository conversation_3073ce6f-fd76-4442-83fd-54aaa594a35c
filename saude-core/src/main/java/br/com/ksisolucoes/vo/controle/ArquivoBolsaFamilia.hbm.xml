<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.controle">
    <class
            name="ArquivoBolsaFamilia"
            table="arquivo_bolsa_familia"
    >
        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_arquivo_bolsa_familia"
        >
            <generator class="sequence">
                <param name="sequence">seq_bolsa_familia</param>
            </generator>
        </id>
        <version column="version" name="version" type="long"/>

        <property
                column="nome_arquivo"
                name="nomeArquivo"
                not-null="false"
                type="java.lang.String"
        />
        
        
       <property
                column="ano"
                name="ano"
                not-null="false"
                type="java.lang.String"
        />
        
       <property
                column="semestre"
                name="semestre"
                not-null="false"
                type="java.lang.String"
        />
       
         
       <many-to-one
                class="Usuario"
                column="cd_usuario"
                name="usuario"
                not-null="false"
        />
       
       <property
                column="dt_envio"
                name="dataEnvio"
                not-null="false"
                type="timestamp"
        />    
        
        
        <property
                column="qtd_registros"
                name="quantidadeRegistros"
                not-null="false"
                type="java.lang.Long"
        />
        
         <property
                column="qtd_registros_atualizados"
                name="quantidadeRegistrosAtualizados"
                not-null="false"
                type="java.lang.Long"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.service.AsyncProcess"
            column="cd_process"
            name="asyncProcess"
        />

        <property
                column="situacao"
                name="situacao"
                not-null="false"
                type="java.lang.Long"
        />

        <property
                column="dt_cancelamento"
                name="dataCancelamento"
                not-null="false"
                type="date"
        />    
        
    </class>
</hibernate-mapping>