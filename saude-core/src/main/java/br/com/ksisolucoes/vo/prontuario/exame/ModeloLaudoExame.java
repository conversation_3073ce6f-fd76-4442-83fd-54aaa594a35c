package br.com.ksisolucoes.vo.prontuario.exame;

import java.io.Serializable;

import br.com.ksisolucoes.vo.prontuario.exame.base.BaseModeloLaudoExame;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.DictionaryData;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;


@DictionaryData(referenceField="referencia")
public class ModeloLaudoExame extends BaseModeloLaudoExame implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ModeloLaudoExame () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ModeloLaudoExame (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ModeloLaudoExame (
		java.lang.Long codigo,
		java.lang.String descricao) {

		super (
			codigo,
			descricao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public String getDescricaoVO() {
        return getDescricao();
    }

    public String getIdentificador() {
        return getCodigo().toString();
    }
}