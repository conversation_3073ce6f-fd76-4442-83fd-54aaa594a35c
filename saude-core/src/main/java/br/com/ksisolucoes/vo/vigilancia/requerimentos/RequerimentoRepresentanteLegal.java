package br.com.ksisolucoes.vo.vigilancia.requerimentos;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.base.BaseRequerimentoRepresentanteLegal;

import java.io.Serializable;



public class RequerimentoRepresentanteLegal extends BaseRequerimentoRepresentanteLegal implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequerimentoRepresentanteLegal () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequerimentoRepresentanteLegal (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequerimentoRepresentanteLegal (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia,
		br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimento,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.String numeroLogradouroRepresentante,
		java.util.Date dataCadastro) {

		super (
			codigo,
			requerimentoVigilancia,
			estabelecimento,
			usuario,
			numeroLogradouroRepresentante,
			dataCadastro);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}