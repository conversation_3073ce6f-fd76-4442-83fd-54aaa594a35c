<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="Receituario" table="receituario" >
         
        <id
            column="cd_receituario"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="sequence">
                <param name="sequence">seq_atendimento</param>
            </generator>
        </id>
        <version column="version" name="version" type="long" /> 
        
        <many-to-one
            class="br.com.ksisolucoes.vo.basico.Empresa"
            column="empresa"
            name="empresa"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.Profissional"
            column="cd_profissional"
            name="profissional"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.Profissional"
            column="cd_prof_aprazamento"
            name="profissionalAprazamento"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuario"
        />
        
        <property
            column="dt_cadastro"
            name="dataCadastro"
            type="timestamp"
            not-null="true"
        />
        
        <property
            column="nr_receita"
            name="numeroReceita"
            type="java.lang.Long"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.TipoReceita"
            column="cd_receita"
            name="tipoReceita"
            not-null="true"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
            name="atendimento"
        >
            <column name="nr_atendimento"/>
        </many-to-one>
        
        <property
            name="anotacao"
            column="anotacao"
            type="java.lang.String"
        />
        
        <many-to-one 
            class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"
            name="usuarioCadsus"
            column="cd_usu_cadsus"
        />
        
        <many-to-one 
            class="br.com.ksisolucoes.vo.prontuario.basico.Cid"
            name="cid"
            column="cd_cid"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.integracao.DocumentoAssinado"
                name="documentoAssinado"
                column ="cd_documento_assinado"
        />
        
        <property
            name="receitaContinua"
            column="receita_continua"
            type="java.lang.String"
            not-null="true"
            length="1"
        />
        
        <property
            name="dataUsuario"
            column="dt_usuario"
            type="java.util.Date"
            not-null="true"
        />
        
        <property
            name="situacao"
            column="situacao"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property
            name="dtBaixa"
            column="dt_baixa"
            type="java.util.Date"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario_baixa"
            name="usuarioBaixa"
        />
        
        <property 
            name="motivoBaixa"
            column="motivo_baixa"
            type="java.lang.String"
        />
        
        <property
            name="somatorioTurno"
            column="somatorio_turno"
            type="java.lang.Long"
            not-null="false"
        />

        <property
                name="flagDispensado"
                column="flag_dispensado"
                type="java.lang.Long"
                not-null="false"
        />
        
        <property
            column="dt_receituario"
            name="dataReceituario"
            type="timestamp"
            not-null="false"
        />
        
        <property
            column="impressao_prescricao"
            name="impressaoPrescricao"
            type="java.lang.Long"
            not-null="false"
        />

        <property
                column="dt_validade_receita"
                name="dataValidadeReceita"
                type="date"
                not-null="false"
        />

        <property
                column="version_prescricao"
                name="versionPrescricao"
                type="java.lang.Long"
        />

        <property
                column="codigo_barras"
                name="codigoBarras"
                type="java.lang.String"
        />

        <property
                name="token"
                column="token"
                type="java.lang.String"
        />

        <many-to-one
            name="empresaDispensacao"
            class="br.com.ksisolucoes.vo.basico.Empresa"
            column="empresa_dispensacao"
        />
    </class>
</hibernate-mapping>
