package br.com.ksisolucoes.vo.portal;

import java.io.Serializable;

import br.com.ksisolucoes.vo.portal.base.BasePortalPagina;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class PortalPagina extends BasePortalPagina implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public PortalPagina () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public PortalPagina (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public PortalPagina (
		java.lang.Long codigo,
		java.lang.String caminhoPagina) {

		super (
			codigo,
			caminhoPagina);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}