package br.com.ksisolucoes.vo.vigilancia.requerimentos.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the requerimento_vacinacao_extramuro_datas table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="requerimento_vacinacao_extramuro_datas"
 */

public abstract class BaseRequerimentoVacinacaoExtramuroDatas extends BaseRootVO implements Serializable {

	public static String REF = "RequerimentoVacinacaoExtramuroDatas";
	public static final String PROP_DATA = "data";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_HORA_INICIAL = "horaInicial";
	public static final String PROP_HORA_FINAL = "horaFinal";
	public static final String PROP_REQUERIMENTO_VACINACAO_EXTRAMURO = "requerimentoVacinacaoExtramuro";


	// constructors
	public BaseRequerimentoVacinacaoExtramuroDatas () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseRequerimentoVacinacaoExtramuroDatas (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseRequerimentoVacinacaoExtramuroDatas (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVacinacaoExtramuro requerimentoVacinacaoExtramuro,
		java.util.Date data) {

		this.setCodigo(codigo);
		this.setRequerimentoVacinacaoExtramuro(requerimentoVacinacaoExtramuro);
		this.setData(data);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date data;
	private java.util.Date horaInicial;
	private java.util.Date horaFinal;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVacinacaoExtramuro requerimentoVacinacaoExtramuro;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_requerimento_vacinacao_extramuro_datas"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: data
	 */
	public java.util.Date getData () {
		return getPropertyValue(this, data, PROP_DATA); 
	}

	/**
	 * Set the value related to the column: data
	 * @param data the data value
	 */
	public void setData (java.util.Date data) {
//        java.util.Date dataOld = this.data;
		this.data = data;
//        this.getPropertyChangeSupport().firePropertyChange ("data", dataOld, data);
	}



	/**
	 * Return the value associated with the column: hora_inicial
	 */
	public java.util.Date getHoraInicial () {
		return getPropertyValue(this, horaInicial, PROP_HORA_INICIAL); 
	}

	/**
	 * Set the value related to the column: hora_inicial
	 * @param horaInicial the hora_inicial value
	 */
	public void setHoraInicial (java.util.Date horaInicial) {
//        java.util.Date horaInicialOld = this.horaInicial;
		this.horaInicial = horaInicial;
//        this.getPropertyChangeSupport().firePropertyChange ("horaInicial", horaInicialOld, horaInicial);
	}



	/**
	 * Return the value associated with the column: hora_final
	 */
	public java.util.Date getHoraFinal () {
		return getPropertyValue(this, horaFinal, PROP_HORA_FINAL); 
	}

	/**
	 * Set the value related to the column: hora_final
	 * @param horaFinal the hora_final value
	 */
	public void setHoraFinal (java.util.Date horaFinal) {
//        java.util.Date horaFinalOld = this.horaFinal;
		this.horaFinal = horaFinal;
//        this.getPropertyChangeSupport().firePropertyChange ("horaFinal", horaFinalOld, horaFinal);
	}



	/**
	 * Return the value associated with the column: cd_requerimento_vacinacao_extramuro
	 */
	public br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVacinacaoExtramuro getRequerimentoVacinacaoExtramuro () {
		return getPropertyValue(this, requerimentoVacinacaoExtramuro, PROP_REQUERIMENTO_VACINACAO_EXTRAMURO); 
	}

	/**
	 * Set the value related to the column: cd_requerimento_vacinacao_extramuro
	 * @param requerimentoVacinacaoExtramuro the cd_requerimento_vacinacao_extramuro value
	 */
	public void setRequerimentoVacinacaoExtramuro (br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVacinacaoExtramuro requerimentoVacinacaoExtramuro) {
//        br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVacinacaoExtramuro requerimentoVacinacaoExtramuroOld = this.requerimentoVacinacaoExtramuro;
		this.requerimentoVacinacaoExtramuro = requerimentoVacinacaoExtramuro;
//        this.getPropertyChangeSupport().firePropertyChange ("requerimentoVacinacaoExtramuro", requerimentoVacinacaoExtramuroOld, requerimentoVacinacaoExtramuro);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVacinacaoExtramuroDatas)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVacinacaoExtramuroDatas requerimentoVacinacaoExtramuroDatas = (br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVacinacaoExtramuroDatas) obj;
			if (null == this.getCodigo() || null == requerimentoVacinacaoExtramuroDatas.getCodigo()) return false;
			else return (this.getCodigo().equals(requerimentoVacinacaoExtramuroDatas.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}