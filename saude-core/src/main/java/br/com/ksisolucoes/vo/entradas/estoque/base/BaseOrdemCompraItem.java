package br.com.ksisolucoes.vo.entradas.estoque.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the ordem_compra_item table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="ordem_compra_item"
 */

public abstract class BaseOrdemCompraItem extends BaseRootVO implements Serializable {

	public static String REF = "OrdemCompraItem";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_MODELO = "modelo";
	public static final String PROP_DESCRICAO_FABRICANTE = "descricaoFabricante";
	public static final String PROP_NUMERO_ITEM_PREGAO = "numeroItemPregao";
	public static final String PROP_PRECO_UNITARIO = "precoUnitario";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_ORDEM_COMPRA = "ordemCompra";
	public static final String PROP_QUANTIDADE_COMPRA_PEDIDO_TRANSFERENCIA = "quantidadeCompraPedidoTransferencia";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_QUANTIDADE_RECEBIDA = "quantidadeRecebida";
	public static final String PROP_STATUS = "status";
	public static final String PROP_DATA_USUARIO = "dataUsuario";
	public static final String PROP_PRODUTO = "produto";
	public static final String PROP_QUANTIDADE_CANCELADA = "quantidadeCancelada";
	public static final String PROP_QUANTIDADE_COMPRA = "quantidadeCompra";
	public static final String PROP_FABRICANTE = "fabricante";
	public static final String PROP_QUANTIDADE_COMPRA_ORIGINAL = "quantidadeCompraOriginal";
	public static final String PROP_QUANTIDADE_PREGAO = "quantidadePregao";


	// constructors
	public BaseOrdemCompraItem () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseOrdemCompraItem (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseOrdemCompraItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.entradas.estoque.Produto produto,
		br.com.ksisolucoes.vo.entradas.estoque.OrdemCompra ordemCompra,
		java.math.BigDecimal precoUnitario,
		java.lang.Long status,
		java.util.Date dataUsuario) {

		this.setCodigo(codigo);
		this.setProduto(produto);
		this.setOrdemCompra(ordemCompra);
		this.setPrecoUnitario(precoUnitario);
		this.setStatus(status);
		this.setDataUsuario(dataUsuario);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.math.BigDecimal quantidadeCompra;
	private java.math.BigDecimal quantidadeCompraOriginal;
	private java.math.BigDecimal quantidadeCompraPedidoTransferencia;
	private java.math.BigDecimal precoUnitario;
	private java.lang.Long status;
	private java.lang.String modelo;
	private java.math.BigDecimal quantidadeRecebida;
	private java.lang.String numeroItemPregao;
	private java.lang.String descricaoFabricante;
	private java.math.BigDecimal quantidadePregao;
	private java.util.Date dataUsuario;
	private java.util.Date dataCancelamento;
	private java.math.BigDecimal quantidadeCancelada;

	// many to one
	private br.com.ksisolucoes.vo.entradas.estoque.Produto produto;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;
	private br.com.ksisolucoes.vo.entradas.estoque.OrdemCompra ordemCompra;
	private br.com.ksisolucoes.vo.entradas.estoque.Fabricante fabricante;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_oc_item"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: qtd_compra
	 */
	public java.math.BigDecimal getQuantidadeCompra () {
		return getPropertyValue(this, quantidadeCompra, PROP_QUANTIDADE_COMPRA); 
	}

	/**
	 * Set the value related to the column: qtd_compra
	 * @param quantidadeCompra the qtd_compra value
	 */
	public void setQuantidadeCompra (java.math.BigDecimal quantidadeCompra) {
//        java.math.BigDecimal quantidadeCompraOld = this.quantidadeCompra;
		this.quantidadeCompra = quantidadeCompra;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeCompra", quantidadeCompraOld, quantidadeCompra);
	}



	/**
	 * Return the value associated with the column: qtd_compra_original
	 */
	public java.math.BigDecimal getQuantidadeCompraOriginal () {
		return getPropertyValue(this, quantidadeCompraOriginal, PROP_QUANTIDADE_COMPRA_ORIGINAL); 
	}

	/**
	 * Set the value related to the column: qtd_compra_original
	 * @param quantidadeCompraOriginal the qtd_compra_original value
	 */
	public void setQuantidadeCompraOriginal (java.math.BigDecimal quantidadeCompraOriginal) {
//        java.math.BigDecimal quantidadeCompraOriginalOld = this.quantidadeCompraOriginal;
		this.quantidadeCompraOriginal = quantidadeCompraOriginal;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeCompraOriginal", quantidadeCompraOriginalOld, quantidadeCompraOriginal);
	}



	/**
	 * Return the value associated with the column: qtd_compra_ped_transf
	 */
	public java.math.BigDecimal getQuantidadeCompraPedidoTransferencia () {
		return getPropertyValue(this, quantidadeCompraPedidoTransferencia, PROP_QUANTIDADE_COMPRA_PEDIDO_TRANSFERENCIA); 
	}

	/**
	 * Set the value related to the column: qtd_compra_ped_transf
	 * @param quantidadeCompraPedidoTransferencia the qtd_compra_ped_transf value
	 */
	public void setQuantidadeCompraPedidoTransferencia (java.math.BigDecimal quantidadeCompraPedidoTransferencia) {
//        java.math.BigDecimal quantidadeCompraPedidoTransferenciaOld = this.quantidadeCompraPedidoTransferencia;
		this.quantidadeCompraPedidoTransferencia = quantidadeCompraPedidoTransferencia;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeCompraPedidoTransferencia", quantidadeCompraPedidoTransferenciaOld, quantidadeCompraPedidoTransferencia);
	}



	/**
	 * Return the value associated with the column: preco_unitario
	 */
	public java.math.BigDecimal getPrecoUnitario () {
		return getPropertyValue(this, precoUnitario, PROP_PRECO_UNITARIO); 
	}

	/**
	 * Set the value related to the column: preco_unitario
	 * @param precoUnitario the preco_unitario value
	 */
	public void setPrecoUnitario (java.math.BigDecimal precoUnitario) {
//        java.math.BigDecimal precoUnitarioOld = this.precoUnitario;
		this.precoUnitario = precoUnitario;
//        this.getPropertyChangeSupport().firePropertyChange ("precoUnitario", precoUnitarioOld, precoUnitario);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: ds_modelo
	 */
	public java.lang.String getModelo () {
		return getPropertyValue(this, modelo, PROP_MODELO); 
	}

	/**
	 * Set the value related to the column: ds_modelo
	 * @param modelo the ds_modelo value
	 */
	public void setModelo (java.lang.String modelo) {
//        java.lang.String modeloOld = this.modelo;
		this.modelo = modelo;
//        this.getPropertyChangeSupport().firePropertyChange ("modelo", modeloOld, modelo);
	}



	/**
	 * Return the value associated with the column: qtd_recebida
	 */
	public java.math.BigDecimal getQuantidadeRecebida () {
		return getPropertyValue(this, quantidadeRecebida, PROP_QUANTIDADE_RECEBIDA); 
	}

	/**
	 * Set the value related to the column: qtd_recebida
	 * @param quantidadeRecebida the qtd_recebida value
	 */
	public void setQuantidadeRecebida (java.math.BigDecimal quantidadeRecebida) {
//        java.math.BigDecimal quantidadeRecebidaOld = this.quantidadeRecebida;
		this.quantidadeRecebida = quantidadeRecebida;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeRecebida", quantidadeRecebidaOld, quantidadeRecebida);
	}



	/**
	 * Return the value associated with the column: nr_item_pregao
	 */
	public java.lang.String getNumeroItemPregao () {
		return getPropertyValue(this, numeroItemPregao, PROP_NUMERO_ITEM_PREGAO); 
	}

	/**
	 * Set the value related to the column: nr_item_pregao
	 * @param numeroItemPregao the nr_item_pregao value
	 */
	public void setNumeroItemPregao (java.lang.String numeroItemPregao) {
//        java.lang.String numeroItemPregaoOld = this.numeroItemPregao;
		this.numeroItemPregao = numeroItemPregao;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroItemPregao", numeroItemPregaoOld, numeroItemPregao);
	}



	/**
	 * Return the value associated with the column: ds_fabricante
	 */
	public java.lang.String getDescricaoFabricante () {
		return getPropertyValue(this, descricaoFabricante, PROP_DESCRICAO_FABRICANTE); 
	}

	/**
	 * Set the value related to the column: ds_fabricante
	 * @param descricaoFabricante the ds_fabricante value
	 */
	public void setDescricaoFabricante (java.lang.String descricaoFabricante) {
//        java.lang.String descricaoFabricanteOld = this.descricaoFabricante;
		this.descricaoFabricante = descricaoFabricante;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoFabricante", descricaoFabricanteOld, descricaoFabricante);
	}



	/**
	 * Return the value associated with the column: qtd_pregao
	 */
	public java.math.BigDecimal getQuantidadePregao () {
		return getPropertyValue(this, quantidadePregao, PROP_QUANTIDADE_PREGAO); 
	}

	/**
	 * Set the value related to the column: qtd_pregao
	 * @param quantidadePregao the qtd_pregao value
	 */
	public void setQuantidadePregao (java.math.BigDecimal quantidadePregao) {
//        java.math.BigDecimal quantidadePregaoOld = this.quantidadePregao;
		this.quantidadePregao = quantidadePregao;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadePregao", quantidadePregaoOld, quantidadePregao);
	}



	/**
	 * Return the value associated with the column: dt_usuario
	 */
	public java.util.Date getDataUsuario () {
		return getPropertyValue(this, dataUsuario, PROP_DATA_USUARIO); 
	}

	/**
	 * Set the value related to the column: dt_usuario
	 * @param dataUsuario the dt_usuario value
	 */
	public void setDataUsuario (java.util.Date dataUsuario) {
//        java.util.Date dataUsuarioOld = this.dataUsuario;
		this.dataUsuario = dataUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsuario", dataUsuarioOld, dataUsuario);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: qtd_cancelada
	 */
	public java.math.BigDecimal getQuantidadeCancelada () {
		return getPropertyValue(this, quantidadeCancelada, PROP_QUANTIDADE_CANCELADA); 
	}

	/**
	 * Set the value related to the column: qtd_cancelada
	 * @param quantidadeCancelada the qtd_cancelada value
	 */
	public void setQuantidadeCancelada (java.math.BigDecimal quantidadeCancelada) {
//        java.math.BigDecimal quantidadeCanceladaOld = this.quantidadeCancelada;
		this.quantidadeCancelada = quantidadeCancelada;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeCancelada", quantidadeCanceladaOld, quantidadeCancelada);
	}



	/**
	 * Return the value associated with the column: cod_pro
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Produto getProduto () {
		return getPropertyValue(this, produto, PROP_PRODUTO); 
	}

	/**
	 * Set the value related to the column: cod_pro
	 * @param produto the cod_pro value
	 */
	public void setProduto (br.com.ksisolucoes.vo.entradas.estoque.Produto produto) {
//        br.com.ksisolucoes.vo.entradas.estoque.Produto produtoOld = this.produto;
		this.produto = produto;
//        this.getPropertyChangeSupport().firePropertyChange ("produto", produtoOld, produto);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_usu_can
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usu_can
	 * @param usuarioCancelamento the cd_usu_can value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}



	/**
	 * Return the value associated with the column: cd_ordem_compra
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.OrdemCompra getOrdemCompra () {
		return getPropertyValue(this, ordemCompra, PROP_ORDEM_COMPRA); 
	}

	/**
	 * Set the value related to the column: cd_ordem_compra
	 * @param ordemCompra the cd_ordem_compra value
	 */
	public void setOrdemCompra (br.com.ksisolucoes.vo.entradas.estoque.OrdemCompra ordemCompra) {
//        br.com.ksisolucoes.vo.entradas.estoque.OrdemCompra ordemCompraOld = this.ordemCompra;
		this.ordemCompra = ordemCompra;
//        this.getPropertyChangeSupport().firePropertyChange ("ordemCompra", ordemCompraOld, ordemCompra);
	}



	/**
	 * Return the value associated with the column: cd_fabricante
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Fabricante getFabricante () {
		return getPropertyValue(this, fabricante, PROP_FABRICANTE); 
	}

	/**
	 * Set the value related to the column: cd_fabricante
	 * @param fabricante the cd_fabricante value
	 */
	public void setFabricante (br.com.ksisolucoes.vo.entradas.estoque.Fabricante fabricante) {
//        br.com.ksisolucoes.vo.entradas.estoque.Fabricante fabricanteOld = this.fabricante;
		this.fabricante = fabricante;
//        this.getPropertyChangeSupport().firePropertyChange ("fabricante", fabricanteOld, fabricante);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.entradas.estoque.OrdemCompraItem)) return false;
		else {
			br.com.ksisolucoes.vo.entradas.estoque.OrdemCompraItem ordemCompraItem = (br.com.ksisolucoes.vo.entradas.estoque.OrdemCompraItem) obj;
			if (null == this.getCodigo() || null == ordemCompraItem.getCodigo()) return false;
			else return (this.getCodigo().equals(ordemCompraItem.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}