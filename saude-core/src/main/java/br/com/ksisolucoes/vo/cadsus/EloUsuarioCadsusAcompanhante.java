package br.com.ksisolucoes.vo.cadsus;

import java.io.Serializable;

import br.com.ksisolucoes.vo.cadsus.base.BaseEloUsuarioCadsusAcompanhante;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class EloUsuarioCadsusAcompanhante extends BaseEloUsuarioCadsusAcompanhante implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EloUsuarioCadsusAcompanhante () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public EloUsuarioCadsusAcompanhante (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public EloUsuarioCadsusAcompanhante (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsusAcompanhante usuarioCadsusAcompanhante,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {

		super (
			codigo,
			usuarioCadsusAcompanhante,
			usuarioCadsus);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}